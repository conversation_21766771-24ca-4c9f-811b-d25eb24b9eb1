package biliai

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// RecommendParams 天马推荐算法请求参数
type RecommendParams struct {
	Cmd            string // 业务请求标识，如 "maoer_home", "maoer_live"
	MID            int64  // 用户 ID
	Buvid          string // 设备号
	RequestCnt     int32  // 推荐 item 个数
	Timeout        int64  // 请求超时（单位：毫秒）
	DisplayID      int64  // 资源位的刷新次数
	FreshType      int    // 刷新方式
	Chid           string // 渠道标识
	Model          string // 手机型号
	Platform       string // 平台编号
	Version        string // 客户端版本号
	Network        string // 网络状况
	ZoneID         string // 用户所在地区 ID（暂时不需要传）
	Country        string // 用户所在国家
	Province       string // 用户所在省份
	City           string // 用户所在城市
	Sex            int    // 性别（0: Woman；1: Man）
	Persona        int    // 用户画像
	FirstLoginTime int64  // 首次登录时间戳（秒级）
	Ts             int64  // 当前时间戳（秒级）
}

// RecommendResponse 天马推荐算法响应
type RecommendResponse struct {
	Code        int            `json:"code"`
	Data        []ResponseItem `json:"data,omitempty"`
	Debug       *string        `json:"debug,omitempty"`
	SceneType   int            `json:"scene_type"`
	Sid         string         `json:"sid,omitempty"`
	UserFeature string         `json:"user_feature,omitempty"`
	Error       string         `json:"error,omitempty"`
}

// ResponseItem 天马推荐算法响应数据项
type ResponseItem struct {
	AvFeature   string  `json:"av_feature"`
	Goto        string  `json:"goto"`
	ID          int64   `json:"id"`
	RcmdReason  *string `json:"rcmd_reason,omitempty"`
	Source      string  `json:"source"`
	Tid         int     `json:"tid"`
	TrackID     string  `json:"trackid"`
	Attr        int     `json:"-"` // 资源位来源，非算法返回
	Name        string  `json:"-"` // 直播间名称，非算法返回
	OldPos      *int    `json:"-"` // 配置卡初始位置，非算法返回
	Trace       string  `json:"-"` // 埋点信息，非算法返回
	RecommendID int64   `json:"-"` // live_recommended_elements 主键，非算法返回
}

// NewResponseItem 构造函数
func NewResponseItem(gotoType string, id int64) ResponseItem {
	return ResponseItem{
		Goto: gotoType,
		ID:   id,
	}
}

var client = &http.Client{
	Timeout: 15 * time.Second,
}

func constructQueryString(params *RecommendParams) string {
	queryString := url.Values{}

	// 根据你的参数结构体添加所有需要的查询参数
	queryString.Set("cmd", params.Cmd)
	queryString.Set("mid", strconv.FormatInt(params.MID, 10))
	queryString.Set("buvid", params.Buvid)
	queryString.Set("request_cnt", strconv.FormatInt(int64(params.RequestCnt), 10))
	queryString.Set("timeout", strconv.FormatInt(params.Timeout, 10))
	queryString.Set("display_id", strconv.FormatInt(params.DisplayID, 10))
	queryString.Set("fresh_type", strconv.Itoa(params.FreshType))
	queryString.Set("chid", params.Chid)
	queryString.Set("model", params.Model)
	queryString.Set("platform", params.Platform)
	queryString.Set("version", params.Version)
	queryString.Set("network", params.Network)
	// 该参数先不传
	// queryString.Set("zone_id", params.ZoneID)
	queryString.Set("country", params.Country)
	queryString.Set("province", params.Province)
	queryString.Set("city", params.City)
	queryString.Set("sex", strconv.FormatInt(int64(params.Sex), 10))
	queryString.Set("persona", strconv.Itoa(params.Persona))
	queryString.Set("first_logintime", strconv.FormatInt(params.FirstLoginTime, 10))
	queryString.Set("ts", strconv.FormatInt(params.Ts, 10))

	return queryString.Encode()
}

// GetTianmaRecommend 获取天马推荐数据
// TODO: 后续统一使用 missevan-go 天马推荐组件
func GetTianmaRecommend(param *RecommendParams) (*RecommendResponse, error) {
	// 构建请求参数
	reqParam := constructQueryString(param)
	reqURL := fmt.Sprintf("%s%s?%s", getHost(), URLRecommand, reqParam)
	logger.Debugf("GET %s", reqURL)
	req, err := http.NewRequest(http.MethodGet, reqURL, nil)
	if err != nil {
		return nil, fmt.Errorf("天马推荐算法请求失败: %v", err)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", serviceutil.UserAgent)

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("天马推荐算法请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取天马推荐算法响应失败: %v", err)
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, body)
	if resp.StatusCode != http.StatusOK {
		truncatedBody := goutil.TruncateResponseBodyWithNotice(string(body))
		return nil, fmt.Errorf("天马推荐算法请求失败: %v, 状态码: %d, 响应: %s", err, resp.StatusCode, truncatedBody)
	}

	var response RecommendResponse
	if err = json.Unmarshal(body, &response); err != nil {
		truncatedBody := goutil.TruncateResponseBodyWithNotice(string(body))
		return nil, fmt.Errorf("解析天马推荐算法响应失败: %v, 响应: %s", err, truncatedBody)
	}

	// 1. code == 0 正常返回
	if response.Code == codeOk {
		return &response, nil
	}

	// 2. code == -3 或 code == -77 响应数据不足，创建空房间等待补位
	if response.Code == codeItemReqCntNotEnough || response.Code == codeNoResult {
		data := response.Data
		if data == nil {
			data = make([]ResponseItem, 0, param.RequestCnt)
		}
		for int32(len(data)) < param.RequestCnt {
			data = append(data, NewResponseItem(TianmaGotoTypeLive, 0))
		}
		response.Data = data
		return &response, nil
	}

	if response.Error != "" {
		logger.Error(response.Error)
		// PASS
	}

	// 3. 其他情况下直接返回空走兜底逻辑
	return nil, nil
}

// GetUserPersonaAndSex 获取用户画像和性别
func GetUserPersonaAndSex(uc mrpc.UserContext, userID int64, equipID, buvid string) (int64, tianma.Sex) {
	persona := int64(UnknownIntValue)
	sex := tianma.Sex(UnknownIntValue)
	userPersona, err := userapi.GetUserPersona(uc, userID, equipID, buvid)
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": userID, "equip_id": equipID, "buvid": buvid}).Errorf("获取用户画像出错: %v", err)
		// PASS
		return persona, sex
	}
	personaModule := userPersona.Persona & userapi.PersonaModuleMask
	if !userapi.IsValidPersona(personaModule) {
		personaModule = userapi.TypeGirl
	}
	switch userPersona.Persona {
	case userapi.SexBoy:
		sex = tianma.SexMale
	case userapi.SexGirl:
		sex = tianma.SexFemale
	default:
		sex = tianma.Sex(UnknownIntValue)
	}
	return personaModule, sex
}
