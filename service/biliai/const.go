package biliai

import (
	"os"

	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// ProdHost 天马推荐算法请求地址
	ProdHost = "http://data.bilibili.co"
	// PreHost 天马推荐算法请求地址
	PreHost = "http://pre-data.bilibili.co"
	// URLRecommand 推荐算法请求路径
	URLRecommand = "/recommand"
)

// 天马直播推荐状态码列表
const (
	codeOk                  = 0   // 正常响应
	codeItemReqCntNotEnough = -3  // 返回 item 数量不足 req_cnt
	codeNoResult            = -77 // 不返回推荐结果
)

// MaxTopRoomCount 算法最多返回的直播间数量
const MaxTopRoomCount = 10

// TianmaSceneLiveRecommends 业务请求标识
const TianmaSceneLiveRecommends = "maoer_live"

// TianmaGotoTypeLive 推荐元素类型
const TianmaGotoTypeLive = "live"

// 天马推荐场景
const (
	RecommendSceneLiveRecommendTop = "live_recommend_top" // 首页直播推荐
	RecommendSceneLiveTab          = "live_tab"           // 直播 tab 直播推荐
)

// getHost 获取天马推荐算法请求地址
func getHost() string { // UAT 和 PRE 都请求 PRE 的接口
	if os.Getenv(util.EnvDeploy) == util.DeployEnvProd {
		return ProdHost
	}
	return PreHost
}

// 整型、字符串类型的默认值
const (
	UnknownIntValue    = -1
	UnknownStringValue = ""
)

// DefaultTimeout 业务方等待 ai 结果的时间，单位 ms
const DefaultTimeout = 15000

// GetPlatform 获取平台
func GetPlatform(equipment *goutil.Equipment) string {
	switch equipment.OS {
	case goutil.Android:
		return "android"
	case goutil.IOS:
		return "ios"
	case goutil.Web:
		return "web"
	case goutil.MobileWeb:
		return "mobile_web"
	case goutil.Windows:
		return "windows"
	case goutil.HarmonyOS:
		return "harmony_os"
	default:
		return UnknownStringValue
	}
}
