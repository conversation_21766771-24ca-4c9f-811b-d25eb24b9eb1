package vod

import (
	"bytes"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"strconv"

	"github.com/google/uuid"

	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Config for netease vod service
type Config struct {
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
}

// Client for netease vod service
type Client struct {
	conf       *Config
	httpClient *http.Client
}

// ClientResponse structure for netease vod service
type ClientResponse struct {
	Code int             `json:"code"`
	Ret  json.RawMessage `json:"ret,omitempty"`
	Msg  string          `json:"msg,omitempty"`
}

// Video is the video model
type Video struct {
	VideoName       string `json:"videoName"`
	Status          int    `json:"status"`
	Description     string `json:"description"`
	CompleteTime    int64  `json:"complete_time"`
	Duration        int64  `json:"duration"`
	DurationMsec    int64  `json:"durationMsec"`
	TypeID          int    `json:"typeId"`
	TypeName        string `json:"typeName"`
	SnapshotURL     string `json:"snapshotUrl"`
	OrigURL         string `json:"origUrl"`
	DownloadOrigURL string `json:"downloadOrigUrl"`
	InitialSize     int64  `json:"initialSize"`
	CreateTime      int64  `json:"createTime"`
	UpdateTime      int64  `json:"updateTime"`
	Vid             int64  `json:"vid"`
}

// ListVideoRet is the ret of ListVideo
type ListVideoRet struct {
	CurrentPage int     `json:"currentPage"`
	PageSize    int     `json:"pageSize"`
	PageNum     int     `json:"pageNum"`
	List        []Video `json:"list"`
}

const (
	apiEndpoint = "https://vcloud.163.com"
	codeSuccess = 200
)

// NewVodClient return the netease vod client
func NewVodClient(conf *Config) *Client {
	return &Client{
		conf:       conf,
		httpClient: &http.Client{},
	}
}

func (c *Client) sign(req *http.Request) {
	nonce := uuid.New().String()
	curTime := strconv.FormatInt(goutil.TimeNow().Unix(), 10)
	h := sha1.New()
	_, _ = h.Write([]byte(c.conf.AppSecret + nonce + curTime))
	checkSum := hex.EncodeToString(h.Sum(nil))
	req.Header.Set("AppKey", c.conf.AppKey)
	req.Header.Set("Nonce", nonce)
	req.Header.Set("CurTime", curTime)
	req.Header.Set("CheckSum", checkSum)

	// extra
	req.Header.Set("Content-Type", "application/json;charset=utf-8")
}

func (c *Client) request(api string, data, v interface{}) error {
	body, err := json.Marshal(data)
	if err != nil {
		return err
	}
	uri := apiEndpoint + api
	req, err := http.NewRequest(http.MethodPost, uri, bytes.NewReader(body))
	if err != nil {
		return err
	}
	c.sign(req)

	logger.Debugf("POST %s\n%s", uri, body)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}

	// close response
	defer resp.Body.Close()

	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	logger.Debugf("HTTP %s\n%s", resp.Status, body)

	var cp ClientResponse
	err = json.Unmarshal(body, &cp)
	if err != nil {
		return err
	}

	if cp.Code != codeSuccess {
		// TODO: wrap error
		return errors.New(cp.Msg)
	}
	if v != nil {
		err = json.Unmarshal(cp.Ret, v)
		if err != nil {
			return err
		}
	}

	return nil
}

// DeleteVideo deletes single video
func (c *Client) DeleteVideo(vid int64) error {
	api := "/app/vod/video/videoDelete"
	return c.request(api, map[string]interface{}{
		"vid": vid,
	}, nil)
}

// ListVideo list video
// TODO: constants
// typeID: 直播录制: 85403，互动直播录制: 85405
// sortStr: 按视频上传时间排序，desc 表示按上传时间降序，asc 表示按上传时间升序，默认为 desc
func (c *Client) ListVideo(currentPage, pageSize, status, typeID int, sortStr string) (ListVideoRet, error) {
	var ret ListVideoRet
	api := "/app/vod/video/list"
	q := map[string]interface{}{
		"currentPage": currentPage,
		"pageSize":    pageSize,
		"status":      status,
		"type":        typeID,
	}
	if sortStr != "" {
		q["sortStr"] = sortStr
	}
	err := c.request(api, q, &ret)
	return ret, err
}
