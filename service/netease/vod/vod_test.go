package vod

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

func init() {
	logger.InitTestLog()
}

func TestVodClientDeleteVideo(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	vod := NewVodClient(&Config{
		AppKey:    "test",
		AppSecret: "testkey",
	})
	err := vod.DeleteVideo(1)
	if err != nil {
		assert.Contains(err.Error(), "点播服务未开通")
	} else {
		assert.NoError(err)
	}
}

func TestVodClientListVideo(t *testing.T) {
	assert := assert.New(t)
	assert.NotNil(assert)
	require := require.New(t)
	require.NotNil(require)

	vod := NewVodClient(&Config{
		AppKey:    "test",
		AppSecret: "testkey",
	})

	_, err := vod.ListVideo(0, 500, 0, 0, "asc")
	if err != nil {
		assert.Contains(err.Error(), "点播服务未开通")
	} else {
		assert.NoError(err)
	}
}
