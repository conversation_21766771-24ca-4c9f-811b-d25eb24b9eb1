package liveserviceredis

import (
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Expire redis set TTL
func Expire(cmd redis.Cmdable, key string, expire time.Duration) {
	err := cmd.Expire(key, expire).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// ExpireAt redis set TTL, 使用相对时间来实现 ExpireAt
func ExpireAt(cmd redis.Cmdable, key string, tm time.Time) {
	err := cmd.Expire(key, tm.Sub(goutil.TimeNow())).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
