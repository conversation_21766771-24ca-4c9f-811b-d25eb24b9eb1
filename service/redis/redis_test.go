package liveserviceredis

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestExpire(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	key := "testTTL"

	pipe := service.Redis.TxPipeline()
	err := pipe.SAdd(key, "123").Err()
	require.NoError(err)
	cmd := pipe.TTL(key)
	Expire(pipe, key, 6*time.Second)
	_, err = pipe.Exec()
	require.NoError(err)
	ttl := cmd.Val()
	assert.Equal(time.Duration(-1), ttl)

	ttl = service.Redis.TTL(key).Val()
	assert.Greater(int64(ttl), int64(0))
}

func TestExpireAt(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	key := "testTTLAt"

	pipe := service.Redis.TxPipeline()
	err := pipe.SAdd(key, "123").Err()
	require.NoError(err)
	cmd := pipe.TTL(key)
	ExpireAt(pipe, key, goutil.TimeNow().Add(6*time.Second))
	_, err = pipe.Exec()
	require.NoError(err)
	ttl := cmd.Val()
	assert.Equal(time.Duration(-1), ttl)

	ttl = service.Redis.TTL(key).Val()
	assert.Greater(int64(ttl), int64(0))
}
