package rankevent

import (
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// SyncFollowParam .
type SyncFollowParam struct {
	SyncCommonParam SyncCommonParam

	UserID       int64
	FollowUserID int64
}

// newSyncFollowParam new SyncFollowParam
func newSyncFollowParam(p *SyncCommonParam, userID, followUserID int64) *SyncFollowParam {
	return &SyncFollowParam{
		SyncCommonParam: *p,
		UserID:          userID,
		FollowUserID:    followUserID,
	}
}

// Send .
func (param *SyncFollowParam) Send(ctx mrpc.UserContext) error {
	send := param.SyncCommonParam.newRankEventParam()
	send.Follow = &userapi.RankEventFollow{
		UserID:       param.UserID,
		FollowUserID: param.FollowUserID,
	}
	return send.Send(ctx)
}
