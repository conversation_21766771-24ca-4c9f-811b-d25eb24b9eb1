package rankevent

import (
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// SyncOnlineParam .
type SyncOnlineParam struct {
	SyncCommonParam SyncCommonParam

	QuestID  string
	Duration int64 // 单位：秒
}

// newSyncOnlineParam new SyncOnlineParam
func newSyncOnlineParam(p *SyncCommonParam, questID string, duration int64) *SyncOnlineParam {
	return &SyncOnlineParam{
		SyncCommonParam: *p,
		QuestID:         questID,
		Duration:        duration,
	}
}

// Send .
func (param *SyncOnlineParam) Send(ctx mrpc.UserContext) error {
	send := param.SyncCommonParam.newRankEventParam()
	send.Online = &userapi.RankEventOnline{
		QuestID:  param.QuestID,
		Duration: param.Duration,
	}
	return send.Send(ctx)
}
