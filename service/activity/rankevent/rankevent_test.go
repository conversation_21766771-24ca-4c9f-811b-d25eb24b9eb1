package rankevent

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestNewSyncCommonParam(t *testing.T) {
	assert := assert.New(t)

	p := NewSyncCommonParam(1)
	assert.NotNil(p)
}

func TestSyncCommonParam_SetRoomInfo(t *testing.T) {
	assert := assert.New(t)

	p := NewSyncCommonParam(1)
	p.SetRoomInfo(1, 1, 1, 11)
	assert.Equal(int64(1), p.<PERSON>ID)
	assert.Equal(int64(1), p.<PERSON>ser<PERSON>)
	assert.Equal(int64(1), p.GuildID)
	assert.Equal(int64(11), p.ActivityCatalogID)
}

func TestSyncCommonParam_General(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cleanup := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeGeneral, body.EventType)
		assert.Equal(EventAddMedal, body.Event.Name)
		count++
		return "success", nil
	})
	defer cleanup()

	NewSyncCommonParam(1).SetRoomInfo(1, 1, 1, 11).
		General(EventAddMedal).Send(mrpc.NewUserContextFromEnv())
	assert.Equal(1, count)
}

func TestSyncCommonParam_Online(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cleanup := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeOnline, body.EventType)
		assert.Equal("1", body.Online.QuestID)
		count++
		return "success", nil
	})
	defer cleanup()

	err := NewSyncCommonParam(1).
		Online("1", int64(time.Hour.Seconds())).Send(mrpc.NewUserContextFromEnv())
	require.NoError(err)
	assert.Equal(1, count)
}

func TestSyncCommonParam_Follow(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var called bool
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeFollow, body.EventType)
		assert.EqualValues(12, body.Follow.UserID)
		called = true
		return "success", nil
	})
	defer cancel()

	err := NewSyncCommonParam(12).
		Follow(12, 13).Send(mrpc.NewUserContextFromEnv())
	require.NoError(err)
	assert.True(called)
}

func TestSyncCommonParam_EnterRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var called bool
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeEnterRoom, body.EventType)
		assert.Less(int64(0), body.EnterRoom.Ts)
		assert.Greater(goutil.TimeNow().UnixMicro(), body.EnterRoom.Ts)
		called = true
		return "success", nil
	})
	defer cancel()

	err := NewSyncCommonParam(1).SetRoomInfo(1, 1, 1, 11).
		EnterRoom(goutil.TimeNow()).Send(mrpc.NewUserContextFromEnv())
	require.NoError(err)
	assert.True(called)
}

func TestSyncCommonParam_BuyGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg := &livegoods.LiveGoods{ID: 100, Type: 2}
	transactionID := int64(1000)
	confirmTime := goutil.TimeNow()

	var called bool
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeBuyGoods, body.EventType)
		assert.Equal(lg.ID, body.BuyGoods.GoodsID)
		assert.Equal(lg.Type, body.BuyGoods.Type)
		assert.Equal(transactionID, body.BuyGoods.TransactionID)
		assert.Equal(confirmTime.Unix(), body.BuyGoods.ConfirmTime)
		called = true
		return "success", nil
	})
	defer cancel()

	err := NewSyncCommonParam(1).SetRoomInfo(1, 1, 1, 11).
		BuyGoods(lg, transactionID, confirmTime).Send(mrpc.NewUserContextFromEnv())
	require.NoError(err)
	assert.True(called)
}
