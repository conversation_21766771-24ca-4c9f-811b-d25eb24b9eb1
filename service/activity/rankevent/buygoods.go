package rankevent

import (
	"time"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// SyncBuyGoodsParam 购买商品事件
type SyncBuyGoodsParam struct {
	SyncCommonParam SyncCommonParam

	LiveGoods     *livegoods.LiveGoods // 购买的商品
	ConfirmTime   time.Time            // 订单确认时间
	TransactionID int64
}

func newSyncBuyGoodsParam(p *SyncCommonParam, lg *livegoods.LiveGoods, transactionID int64, confirmTime time.Time) *SyncBuyGoodsParam {
	return &SyncBuyGoodsParam{
		SyncCommonParam: *p,
		LiveGoods:       lg,
		ConfirmTime:     confirmTime,
		TransactionID:   transactionID,
	}
}

// Send a event on buy goods
func (param *SyncBuyGoodsParam) Send(ctx mrpc.UserContext) error {
	send := param.SyncCommonParam.newRankEventParam()
	send.BuyGoods = &userapi.RankEventBuyGoods{
		GoodsID:       param.LiveGoods.ID,
		Type:          param.LiveGoods.Type,
		TransactionID: param.TransactionID,
		ConfirmTime:   param.ConfirmTime.Unix(),
	}
	return send.Send(ctx)
}
