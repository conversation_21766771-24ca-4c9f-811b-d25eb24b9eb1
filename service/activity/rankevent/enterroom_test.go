package rankevent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewSyncEnterRoomParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := newSyncEnterRoomParam(&SyncCommonParam{}, goutil.TimeNow())
	require.NotNil(p)
	assert.Less(0, p.EnterTime)
	assert.Greater(goutil.TimeNow(), p.EnterTime)
}

func TestSyncEnterRoomParam_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeBuyGoods, body.EventType)
		require.NotNil(body)
		assert.Less(int64(0), body.EnterRoom.Ts)
		assert.Greater(goutil.TimeNow().UnixMicro(), body.EnterRoom.Ts)
		called = true
		return "success", nil
	})
	defer cancel()

	param := &SyncEnterRoomParam{
		SyncCommonParam: SyncCommonParam{EventType: EventTypeBuyGoods},
		EnterTime:       goutil.TimeNow(),
	}
	require.NoError(param.Send(mrpc.NewUserContextFromEnv()))
	assert.True(called)
}
