package rankevent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestNewSyncFollowParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := newSyncFollowParam(&SyncCommonParam{}, 12, 13)
	require.NotNil(p)
	assert.EqualValues(12, p.UserID)
	assert.EqualValues(13, p.FollowUserID)
}

func TestSyncFollowParam_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		require.NotNil(body)
		called = true
		return "success", nil
	})
	defer cancel()

	param := &SyncFollowParam{
		SyncCommonParam: SyncCommonParam{},
		UserID:          12,
		FollowUserID:    13,
	}
	require.NoError(param.Send(mrpc.NewUserContextFromEnv()))
	assert.True(called)
}
