package rankevent

import (
	"time"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// SyncEnterRoomParam 进入房间事件
type SyncEnterRoomParam struct {
	SyncCommonParam SyncCommonParam

	EnterTime time.Time // 进入房间时间
}

func newSyncEnterRoomParam(p *SyncCommonParam, enterTime time.Time) *SyncEnterRoomParam {
	return &SyncEnterRoomParam{
		SyncCommonParam: *p,
		EnterTime:       enterTime,
	}
}

// Send a event on enter room
func (param *SyncEnterRoomParam) Send(ctx mrpc.UserContext) error {
	send := param.SyncCommonParam.newRankEventParam()
	send.EnterRoom = &userapi.RankEventEnterRoom{
		Ts: param.EnterTime.UnixMilli(),
	}
	return send.Send(ctx)
}
