package rankevent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestNewSyncOnlineParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := newSyncOnlineParam(&SyncCommonParam{}, "1", 180)
	require.NotNil(p)
	assert.Equal("1", p.QuestID)
	assert.EqualValues(180, p.Duration)
}

func TestSyncOnlineParam_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		require.NotNil(body)
		called = true
		return "success", nil
	})
	defer cancel()

	param := &SyncOnlineParam{
		SyncCommonParam: SyncCommonParam{},
		QuestID:         primitive.NewObjectID().Hex(),
		Duration:        180,
	}
	require.NoError(param.Send(mrpc.NewUserContextFromEnv()))
	assert.True(called)
}
