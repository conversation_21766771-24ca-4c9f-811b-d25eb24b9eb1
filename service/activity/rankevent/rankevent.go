package rankevent

import (
	"time"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service/userapi"
)

// 事件类型
const (
	// EventTypeGeneral 通用事件
	EventTypeGeneral = "general"
	// EventTypeOnline 在线时长
	EventTypeOnline = "online"
	// EventTypeFollow 关注
	EventTypeFollow = "follow"
	// EventTypeEnterRoom 进入直播间
	EventTypeEnterRoom = "enter_room"
	// EventTypeBuyGoods 购买商品
	EventTypeBuyGoods = "buy_goods"
)

// SyncCommonParam 通用参数
type SyncCommonParam struct {
	RoomID            int64
	FromUserID        int64
	ToUserID          int64
	GuildID           int64
	ActivityCatalogID int64
	EventType         string
}

// NewSyncCommonParam new SyncCommonParam
func NewSyncCommonParam(fromUserID int64) *SyncCommonParam {
	return &SyncCommonParam{
		FromUserID: fromUserID,
	}
}

// SetRoomInfo set room
func (p *SyncCommonParam) SetRoomInfo(roomID, creatorID, guildID, activityCatalogID int64) *SyncCommonParam {
	p.RoomID = roomID
	p.ToUserID = creatorID
	p.GuildID = guildID
	p.ActivityCatalogID = activityCatalogID
	return p
}

// General set general event
func (p *SyncCommonParam) General(eventName string) *SyncGeneralParam {
	p.EventType = EventTypeGeneral
	return newSyncGeneralParam(p, eventName)
}

// Online set online event
func (p *SyncCommonParam) Online(questID string, duration int64) *SyncOnlineParam {
	p.EventType = EventTypeOnline
	return newSyncOnlineParam(p, questID, duration)
}

// Follow set follow event
func (p *SyncCommonParam) Follow(userID, followUserID int64) *SyncFollowParam {
	p.EventType = EventTypeFollow
	return newSyncFollowParam(p, userID, followUserID)
}

// EnterRoom set enter_room event
func (p *SyncCommonParam) EnterRoom(enterTime time.Time) *SyncEnterRoomParam {
	p.EventType = EventTypeEnterRoom
	return newSyncEnterRoomParam(p, enterTime)
}

// BuyGoods set buy_goods event
func (p *SyncCommonParam) BuyGoods(lg *livegoods.LiveGoods, transactionID int64, confirmTime time.Time) *SyncBuyGoodsParam {
	p.EventType = EventTypeBuyGoods
	return newSyncBuyGoodsParam(p, lg, transactionID, confirmTime)
}

func (p *SyncCommonParam) newRankEventParam() *userapi.RankEventParams {
	return &userapi.RankEventParams{
		UserID:            p.FromUserID,
		CreatorID:         p.ToUserID,
		RoomID:            p.RoomID,
		GuildID:           p.GuildID,
		ActivityCatalogID: p.ActivityCatalogID,
		EventType:         p.EventType,
	}
}
