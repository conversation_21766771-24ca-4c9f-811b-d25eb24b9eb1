package rankevent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewSyncBuyGoodsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg := &livegoods.LiveGoods{ID: 100, Type: 2}
	transactionID := int64(1000)
	confirmTime := goutil.TimeNow()
	p := newSyncBuyGoodsParam(&SyncCommonParam{}, lg, transactionID, confirmTime)
	require.NotNil(p)
	assert.Equal(lg.ID, p.LiveGoods.ID)
	assert.Equal(lg.Type, p.LiveGoods.Type)
	assert.Equal(transactionID, p.TransactionID)
	assert.Equal(confirmTime, p.ConfirmTime)
}

func TestSyncBuyGoodsParam_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lg := &livegoods.LiveGoods{ID: 100, Type: 2}
	transactionID := int64(1000)
	confirmTime := goutil.TimeNow()
	var called bool
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		assert.Equal(EventTypeBuyGoods, body.EventType)
		assert.Equal(lg.ID, body.BuyGoods.GoodsID)
		assert.Equal(lg.Type, body.BuyGoods.Type)
		assert.Equal(transactionID, body.BuyGoods.TransactionID)
		assert.Equal(confirmTime.Unix(), body.BuyGoods.ConfirmTime)
		called = true
		return "success", nil
	})
	defer cancel()

	param := &SyncBuyGoodsParam{
		SyncCommonParam: SyncCommonParam{EventType: EventTypeBuyGoods},
		LiveGoods:       lg,
		TransactionID:   transactionID,
		ConfirmTime:     confirmTime,
	}
	require.NoError(param.Send(mrpc.NewUserContextFromEnv()))
	assert.True(called)
}
