package rankevent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestNewSyncGeneralParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := NewSyncCommonParam(1)
	param := newSyncGeneralParam(p, "test")
	require.NotNil(param)
	require.Equal(*p, param.SyncCommonParam)
	assert.Equal("test", param.EventName)
}

func TestSyncGeneralParam_Send(t *testing.T) {
	assert := assert.New(t)

	var count int
	cleanup := mrpc.SetMock(userapi.URIRankEvent, func(input any) (any, error) {
		count++
		return "success", nil
	})
	defer cleanup()

	param := &SyncGeneralParam{
		SyncCommonParam: SyncCommonParam{},
	}
	param.Send(mrpc.NewUserContextFromEnv())
	assert.Equal(1, count)
}
