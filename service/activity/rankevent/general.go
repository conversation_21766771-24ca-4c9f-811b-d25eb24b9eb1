package rankevent

import (
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

const (
	// EventAddMedal 获得粉丝勋章
	EventAddMedal = "add_medal"
)

// SyncGeneralParam .
type SyncGeneralParam struct {
	SyncCommonParam SyncCommonParam

	EventName string
}

// newSyncGeneralParam new SyncGeneralParam
func newSyncGeneralParam(p *SyncCommonParam, eventName string) *SyncGeneralParam {
	return &SyncGeneralParam{
		SyncCommonParam: *p,
		EventName:       eventName,
	}
}

// Send .
func (param *SyncGeneralParam) Send(ctx mrpc.UserContext) {
	send := param.SyncCommonParam.newRankEventParam()
	send.Event = &userapi.RankEventGeneral{
		Name: param.EventName,
	}
	err := send.Send(ctx)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
