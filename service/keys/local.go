package keys

import (
	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// 本文件存放本地缓存的 key

// KeyRecommend0 缓存 recommend 相关数据
const KeyRecommend0 cache.KeyFormat = "recommend"

// KeyNobleRecommend1 神话推荐的缓存
// 参数是整 10 分钟
const KeyNobleRecommend1 cache.KeyFormat = "nobleRecommend%02d"

// KeyOnlineGifts0 礼物
// 已生效礼物和特殊用户礼物缓存
const KeyOnlineGifts0 cache.KeyFormat = "onlineGifts"

// KeyAllLiveCatalogs0 直播 catalogs 缓存，包含隐藏的
const KeyAllLiveCatalogs0 cache.KeyFormat = "allLiveCatalogs"

// KeyChatroomRecommend1 直播结束后的本场榜推荐
// params: 直播分区 ID, 0 代表了热度推荐
const KeyChatroomRecommend1 cache.KeyFormat = "chatroomRecommend%03d"

// KeyChatroomOpenList2 直播间开播列表热门缓存
// params: 请求的 query, 热门页干预开关 1 表示开启干预
const KeyChatroomOpenList2 cache.KeyFormat = "chatroomOpenList:v2:%s:%d"

// KeyBubbles 气泡的缓存
const KeyBubbles cache.KeyFormat = "bubbles"

// KeyEventTime1 活动的开始和结束时间缓存
// 参数是活动的 ID
const KeyEventTime1 cache.KeyFormat = "eventTime%d"

// KeyEventInfo1 活动信息缓存
// 参数是活动的 ID
const KeyEventInfo1 cache.KeyFormat = "eventInfo%d"

// KeyVoteOption0 互动礼物投票配置缓存
const KeyVoteOption0 cache.KeyFormat = "voteOption"

const (
	// KeyAllGoodsSuperFanList0 所有超粉信息（包括已下架的超粉）列表
	KeyAllGoodsSuperFanList0 cache.KeyFormat = "allGoodsSuperFanList"
	// KeyAllGoodsLuckyBoxList0 所有宝盒信息（包括已下架的幸运宝盒）列表
	KeyAllGoodsLuckyBoxList0 cache.KeyFormat = "allGoodsLuckyBoxList"
)

// KeyDramaSubCatalogIDs0 广播剧下子分区
const KeyDramaSubCatalogIDs0 cache.KeyFormat = "dramaSubCatalogIDs"

// KeyCurrentGiftWallPeriod1 礼物墙周期信息
// params 当日日期 如 20220101
const KeyCurrentGiftWallPeriod1 cache.KeyFormat = "currentGiftWallPeriod%s"

// 普通贵族和上神贵族相关 cache
const (
	KeyVipList1          cache.KeyFormat = "vipList:%d"
	KeyMapVipIDInfos1    cache.KeyFormat = "mapVipID:%d"
	KeyMapVipLevelInfos1 cache.KeyFormat = "mapVipLevel:%d"
)

// KeyActivityWidgetLuaProto2 小窗的 lua proto
// params 活动 ID, 小窗类型
const KeyActivityWidgetLuaProto2 cache.KeyFormat = "activity/widget/%d/%d"

// LocalKeyUserNoRankPointUserIDs0 不让非消费加榜单的用户 ID
const LocalKeyUserNoRankPointUserIDs0 cache.KeyFormat = "user/no_rank_point/user_ids"

// LocalKeyIMJoinInvisibleUserIDs0 拥有进场隐身角色的用户 ID
const LocalKeyIMJoinInvisibleUserIDs0 cache.KeyFormat = "im/user/join/invisible/user_ids"

// LocalKeyActivatedQuests1 活跃的任务 key
// params: 任务类型
const LocalKeyActivatedQuests1 cache.KeyFormat = "quests/activated/type/%d" // []*Quest

// LocalKeyRedpacketGrabBlockUser0 抢红包用户黑名单 key
const LocalKeyRedpacketGrabBlockUser0 cache.KeyFormat = "redpacket/grab/block/user"

// LocalKeyVipConfig0 贵族配置
const LocalKeyVipConfig0 cache.KeyFormat = "vipConfig"

// LocalKeyUsersLastRank2 主播榜的上一周期用户 ID 和排名
// params: 榜单类型; 格式化后的时间，e.g. 20060102、2006010215
// 第一个参数是榜单类型，第二个是格式化后的本周期时间
const LocalKeyUsersLastRank2 PrefixFormatter = "users/lastrank/%d/%s"

// LocalKeyLiveExtraBannersMap0 直播通栏图 key
const LocalKeyLiveExtraBannersMap0 cache.KeyFormat = "liveExtraBannersMap"

// LocalKeyActivityCronJobs0 活动的 cron jobs 缓存
const LocalKeyActivityCronJobs0 cache.KeyFormat = "activity/cron/jobs"

// LocalKeyUser7DaysCreatorRank 用户近 7 日打赏主播排名
// params: 用户 ID
const LocalKeyUser7DaysCreatorRank cache.KeyFormat = "user_7days_creator_rank/%d"

// LocalKeyPresetMessages0 预设消息列表缓存
const LocalKeyPresetMessages0 cache.KeyFormat = "preset_messages"
