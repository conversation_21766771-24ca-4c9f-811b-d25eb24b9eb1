package keys

import (
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

// KeyIMPubSub2 im 订阅，收发消息
// params: 连接总量; 当前连接编号，应该不超过连接总量
/* 例：
live-service:im:pubsub:100-000 总数为 100 的 PUB/SUB 组中，编号为 000 的 PUB/SUB
live-service:im:pubsub:100-099 总数为 100 的 PUB/SUB 组中，编号为 099 的 PUB/SUB
*/
const KeyIMPubSub2 cache.KeyFormat = "live-service:im:pubsub:%03d-%03d" // PUB/SUB

// KeyIMRoomJoined2 某段时间内加入某房间的用户
// params: 房间号; 时钟分钟/n, n 代表进场冷却时间（暂定 3 分钟）
const KeyIMRoomJoined2 cache.KeyFormat = "live-service:im:room:%d:joined:%02d" // SET

const (
	// KeyIMRoomMembers1 存放聊天室内的用户，对应 qq 群的群成员，
	// 其 score 最初存入用户加入时的时间，后续会随着房间刷新而更新
	KeyIMRoomMembers1 cache.KeyFormat = "live-service:im:room:%d:members" // ZSET
	// KeyIMRoomUsers1 存放聊天室内的注册用户
	KeyIMRoomUsers1 cache.KeyFormat = "live-service:im:room:%d:users" // HASH
	// KeyIMRoomNotify1 存放需要通知加入离开房间的用户 ID
	KeyIMRoomNotify1 cache.KeyFormat = "live-service:im:room:%d:notify" // SET

	// IMKeyRoomValuableUsers1 统计热度计算的用户
	// params: 房间号
	IMKeyRoomValuableUsers1 cache.KeyFormat = "live-service:im:room:%d:valuable:users" // SET
)

// 进入房间队列相关
const (
	// KeyIMRoomJoinQueue1 进入房间队列
	KeyIMRoomJoinQueue1 cache.KeyFormat = "live-service:im:room:%d:join_queue" // LIST
	// LockIMRoomJoinQueue1 进入房间队列锁
	LockIMRoomJoinQueue1 cache.KeyFormat = "lock:live-service:im:room:%d:join_queue"
)

// ParseIMRoomID 从形如 "live-service:im:room:%d:xxx" 的 Key 中获取房间号
func ParseIMRoomID(key string) int64 {
	key = strings.Split(key, ":")[3]
	res, _ := strconv.ParseInt(key, 10, 64)
	return res
}
