package keys

import "github.com/MiaoSiLa/missevan-go/service/cache"

const (
	// KeyActivitySuperFanCost0 用户消耗钻石数，加碎片用
	KeyActivitySuperFanCost0 cache.KeyFormat = "activity/superfan/cost" // ZSET

	// KeyRankSuperFanFlower1 心花榜
	// params: 分组
	KeyRankSuperFanFlower1 cache.KeyFormat = "rank/super_fan/flower/%d" // ZSET

	// KeySuperFanDailyFlowerProgress1 每日浇花进度
	// params: 时间 20210515
	KeySuperFanDailyFlowerProgress1 cache.KeyFormat = "super_fan/daily/progress/flower/%s" // HASH

	// KeyRankSuperFanPromotion1 心动值主榜单
	// 参数：晋级人数
	KeyRankSuperFanPromotion1 cache.KeyFormat = "rank/super_fan/promotion/%d" // ZSET

	// KeyRankSuperFanPromotionFinalRank0 心动值主榜单最后排名，20 强, 10 强
	KeyRankSuperFanPromotionFinalRank0 cache.KeyFormat = "rank/super_fan/promotion/final_rank" // HASH

	// KeyRankSuperFanRich0 超粉嘉年华神壕榜
	KeyRankSuperFanRich0 cache.KeyFormat = "rank/super_fan/rich" // ZSET

	// KeySuperFanTSOffset0 超粉的服务器时间偏移
	KeySuperFanTSOffset0 cache.KeyFormat = "super_fan/2021/ts_offset" // STRING

	// KeySuperFanPromotionTopNotify0 超粉心动榜飘屏
	KeySuperFanPromotionTopNotify0 cache.KeyFormat = "super_fan/promotion/top/notify" // STRING

	// KeySuperFanFlowerTopNotify0 超粉心花榜飘屏
	KeySuperFanFlowerTopNotify0 cache.KeyFormat = "super_fan/flower/top/notify" // STRING

	// LockSuperFanTopEffect0 超粉 Tops 特效
	LockSuperFanTopEffect0 cache.KeyFormat = "lock:super_fan/promotion/top/effect" // STRING

)
