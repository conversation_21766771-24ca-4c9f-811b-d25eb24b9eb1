package keys

import (
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/service/cache"
)

const (
	// KeyRoomsFollowed1 直播间本场关注过的用户,
	// params: 房间号
	KeyRoomsFollowed1 PrefixFormatter = "rooms/followed/%d" // SET
	// LockRoomsFollowed1 每秒发送关注消息限制锁
	// params: 房间号；时钟秒针所指数字，[0, 60)
	LockRoomsFollowed1 cache.KeyFormat = "lock:rooms/followed/%d/%02d" // KEY

	// KeyRoomsMeta1 直播间 meta 缓存
	// params: 房间号
	KeyRoomsMeta1 PrefixFormatter = "rooms/meta/%d" // HASH

	// KeyRoomsRankPointMessageLock2 直播间消息锁
	// params: user_id, date (20230509)
	KeyRoomsRankPointMessageLock2 PrefixFormatter = "rooms/rankpoint/%d/%s" // HASH

	// KeyRoomsRevenues3 直播间粉丝贡献榜
	// 用户 ID 作为成员，收益作为分数
	// params: 房间号；榜单类型；榜单类型相关的时间后缀或活动 ID，比如 /20060102，/134
	KeyRoomsRevenues3 PrefixFormatter = "rooms/revenues/%d/%d%s" // ZSET

	// KeyRoomsRoomID1 api/chatroom/{room_id} 缓存
	// params: 房间号
	KeyRoomsRoomID1 PrefixFormatter = "rooms/room_id/%d"

	// KeyRoomsOnline1 用户是否在直播间统计
	// params: 房间号
	KeyRoomsOnline1 PrefixFormatter = "rooms/online/%d" // HASH

	// KeyUsersInfo1 用户信息缓存
	KeyUsersInfo1 PrefixFormatter = "user_id/%d"

	// KeyRoomAdmin1 rooms/admin_ids/${room_id} 直播间房管用户
	KeyRoomAdmin1 PrefixFormatter = "rooms/admin_ids/%d" // SET
	// KeyRoomMute1 rooms/mute_ids/${room_id} 直播间被禁言的用户
	KeyRoomMute1 PrefixFormatter = "rooms/mute_ids/%d" // SET

	// KeyRoomsGlobalMute0 全站禁言缓存
	KeyRoomsGlobalMute0 PrefixFormatter = "rooms/mute/global" // SET

	// LockRoomQuestion2 直播间用户提问锁
	// params: 房间号，用户 ID
	LockRoomQuestion2 PrefixFormatter = "rooms/question/%d/%d/ask/lock" // LOCK

	// KeyRoomsSuppressionHotList0 直播间热度限制列表
	// member: room_id
	// score: 热度限制过期时间，秒级时间戳，0: 本次直播有效，-1: 永久有效
	KeyRoomsSuppressionHotList0 PrefixFormatter = "rooms/suppression_hot_list_v2" // ZSET
)

// users 榜单
const (
	// KeyUsersActivityApplication1 存放活动报名或具有活动资格的用户 users/activity/application/{event_id}
	// params: 活动的 event_id
	KeyUsersActivityApplication1 PrefixFormatter = "users/activity/application/%s" // SET

	// KeyUsersRank2 主播榜的用户和收益
	// 用户 ID 作为成员，收益作为分数
	// params: 榜单类型; 格式化后的时间，e.g. 20060102、2006010215
	// 第一个参数是榜单类型，第二个是格式化后的时间
	KeyUsersRank2 PrefixFormatter = "users/rank/%d/%s" // ZSET

	// KeyUsersNova2 新人榜可上榜用户
	// params: 格式化后的时间 (20060102), 槽号
	KeyUsersNova2 PrefixFormatter = "users/nova/%s/%02d" // SET

	// KeyUsersActivityDurationScale1 活动预热期间，主播活动分区直播时长所占总直播时长比例缓存 users/activity/duration/scale/{activity_key}
	// params: 活动日期, value: {creator_id}:{scale}
	KeyUsersActivityDurationScale1 PrefixFormatter = "users/activity/duration/scale/%s" // HASH
)

// 礼物连击
const (
	// KeyRoomUserGiftCombo3 用户连击礼物信息
	// params: 房间号, 用户 ID, 礼物 ID
	KeyRoomUserGiftCombo3    PrefixFormatter = "rooms/combo/%d/%d/%d" // HASH
	FieldComboLock0          cache.KeyFormat = "lock"
	FieldComboID0            cache.KeyFormat = "combo_id"
	FieldComboGiftNum0       cache.KeyFormat = "gift_num"
	FieldComboLastTimeMilli0 cache.KeyFormat = "last_time" // 毫秒级时间戳

	// KeyRoomGiftMultiCombo2 直播间一起送连击信息
	// params: 房间号, 礼物 ID
	KeyRoomGiftMultiCombo2 PrefixFormatter = "rooms/multi_combo/%d/%d" // HASH

	// KeyRoomMultiComboUsersRank1 直播间一起送连击用户连击榜单
	// params: 连击 id
	KeyRoomMultiComboUsersRank1 PrefixFormatter = "rooms/multi_combo/user/rank/%s" // ZSET
)

// guild 榜单
const (
	// KeyGuildsRank2 公会榜的主播和收益
	// 主播 ID 作为成员，收益作为分数
	KeyGuildsRank2 PrefixFormatter = "guild/rank/%d/%s" // ZSET

	// KeyGuildRevenues3 公会主播贡献榜
	// 主播 ID 作为成员，收益作为分数
	// params: 公会号；榜单类型；榜单类型相关的时间后缀或活动 ID，比如 /20060102，/134
	KeyGuildRevenues3 PrefixFormatter = "guild/revenues/%d/%d%s" // ZSET

	// KeyGuildUsersApplication1 公会赛用户分组名单
	// params: 活动 key
	KeyGuildUsersApplication1 PrefixFormatter = "guild/activity/user/application/%s" // HASH 主播 ID => 公会 ID
)

// 用户相关缓存
const (
	// KeyUsersUserID1 mongodb user 缓存
	// params: 用户 ID
	KeyUsersUserID1 PrefixFormatter = "users/user_id/%d" // STRING

	// KeyUsersTitles1 用户 titles 缓存，当 titles 有变化的时候需要进行缓存清理
	// params: 用户 ID
	KeyUsersTitles1 PrefixFormatter = "users/titles/%d" // STRING

	// KeyUsersWornAppearancesSets1 用户佩戴的外观集缓存，当 user appearance 有变化的时候需要进行缓存清理
	// params: 用户 ID
	KeyUsersWornAppearancesSets1 PrefixFormatter = "users/worn/appearances/sets/%d" // STRING

	// KeyUsersCloseSoundRecommend1 用户关闭音频播放页右上角的直播推荐
	// params: 用户 ID
	KeyUsersCloseSoundRecommend1 PrefixFormatter = "users/close/sound/recommend/%d" // STRING

	// KeyBuyFukubukuroDailyGlobalLimit2 全站每日限购
	// params: 商品 id, 当前日期 YMD
	KeyBuyFukubukuroDailyGlobalLimit2 PrefixFormatter = "buyfukubukuro/limit/%d/%s" // STRING

	// KeyBuyFukubukuroDailyUserLimit3 用户每日限购
	// params: 商品 id, user id, 当前日期 YMD
	KeyBuyFukubukuroDailyUserLimit3 PrefixFormatter = "buyfukubukuro/limit/%d/%d/%s" // STRING

	// KeyBuyFukubukuroUserLimit2 用户限购
	// params: 商品 id, user id
	KeyBuyFukubukuroUserLimit2 PrefixFormatter = "buyfukubukuro/limit/%d/%d" // STRING

	// KeyBuyFukubukuroGlobalRewardCount2 统计当日购买福袋的订单笔数
	// params: 商品 id, 当前日期 YMD
	KeyBuyFukubukuroGlobalRewardCount2 PrefixFormatter = "buyfukubukuro/reward/%d/%s" // STRING

	// LockUserBuyWishGoods1 许愿池商品购买触发锁
	// params: 用户 ID
	LockUserBuyWishGoods1 PrefixFormatter = "users/buywishgoods/%d"

	// LockRoomBuyRedPacketGoods1 礼物红包商品购买触发锁
	// params: 房间 ID
	LockRoomBuyRedPacketGoods1 PrefixFormatter = "rooms/buyredpacketgoods/%d"

	// LockUserQuestionUpdateLike2 用户提问点赞锁
	// params: 用户 ID，提问 ID
	LockUserQuestionUpdateLike2 PrefixFormatter = "users/question/update-like/%d/%s"

	// GuildUserCritialOperation1 主播、公会长、经纪人关键操作
	// params: 用户 ID
	GuildUserCritialOperation1 cache.KeyFormat = "guild:user:critial-operation:%d" // STRING，value 为用户 token 有效期 2 小时

	// KeyUserMetaMessageCount1 用于统计直播间消息数
	// params: 日期（格式为 20060102）
	// Deprecated: Use KeyUserMetaMessageCount2
	KeyUserMetaMessageCount1 PrefixFormatter = "user_meta/message_count/%s" // HASH

	// KeyUserMetaMessageCount2 用于统计用户直播间消息数
	// params: 日期（格式为 20060102），user_id
	KeyUserMetaMessageCount2 PrefixFormatter = "user_meta/message_count/%s/%d" // STRING
)

// 背包道具相关
const (
	// LockCreatorBackpackGift2 主播背包礼物锁
	// params: 主播 ID; 礼物 ID
	LockCreatorBackpackGift2 cache.KeyFormat = "lock/creator/%d/backpack/gift/%d" // STRING

	// LockUserBackpackGift2 用户背包礼物锁
	// params: 用户 ID; 礼物 ID
	LockUserBackpackGift2 cache.KeyFormat = "lock/user/%d/backpack/gift/%d" // STRING

	// KeyUsersAwardGiftDaily1 用户领取每日背包礼物 set
	// params: 每天 0 点时间戳
	// Deprecated: Use KeyUsersAwardGiftDaily2
	KeyUsersAwardGiftDaily1 PrefixFormatter = "users/award-daily-gift/%d" // SET

	// KeyUsersAwardGiftDaily2 用户领取每日背包礼物 set
	// params: 日期（20230705）；user_id
	KeyUsersAwardGiftDaily2 cache.KeyFormat = "users/award-daily-gift/%s/%d" // STRING

	// LockUseBackpackItem1 背包道具使用锁
	// params: user_id
	LockUseBackpackItem1 cache.KeyFormat = "lock:live-service:use:backpack:item:%d" // STRING
)

// 贵族相关
const (
	// KeyNobleUserVipLevel1 存放用户贵族和上神的 level
	// 参数: userID, value: {"1": 7, "2": 1}
	KeyNobleUserVipLevel1 PrefixFormatter = "user_nobles/vip/level/%d" // STRING

	// KeyNobleUserInfo1 贵族信息
	// params: 用户 ID
	// TODO: audio-chatroom 还在使用该缓存，后续考虑移除
	KeyNobleUserInfo1 PrefixFormatter = "user_nobles/info/%d" // STRING

	// KeyNobleUserVips1 普通贵族和上神信息
	// params: 用户 ID
	KeyNobleUserVips1 PrefixFormatter = "user_nobles/vips/%d" // STRING

	// KeyUsersActivityRank1 活动期间用户消费榜
	// 用户 ID 作为成员，消费作为分数
	// params: 活动日期 e.g. 20200707
	KeyUsersActivityRank1 PrefixFormatter = "activities/rank/1/%s" // ZSET

	// KeyNobleTrialExpireCronExecutionTime0 贵族体验过期定时任务执行的时间
	// 存储的是定时任务最后一次的执行时间戳，单位：秒
	KeyNobleTrialExpireCronExecutionTime0 cache.KeyFormat = "noble:trial_expire:cron:execution_time" // STRING
)

// 直播开播通知
const (
	// KeyRoomsBroadcastOpen0 直播开播全站通知 fields: FieldRewardRoom1
	KeyRoomsBroadcastOpen0 PrefixFormatter = "rooms/broadcast/open" // HASH
	// FieldRewardRoom1 直播开播通知房间信息 reward_{room_id}
	// value e.g. {"limit": 1, "title": "主播称号"}, title 为可选字段
	FieldRewardRoom1 cache.KeyFormat = "reward_%d"
	// FieldRewardUsedCounter1 直播开播通知使用次数，每次设置会重置 counter_{room_id}
	FieldRewardUsedCounter1 cache.KeyFormat = "counter_%d"
)

// 直播弹窗内容
const (
	// KeyPromptContent1 存放直播开播弹窗内容 prompt/content/{eventDate}
	// value e.g. {"title": "友情提示", "content_html": "<p>提示</p>"}
	KeyPromptContent1 cache.KeyFormat = "prompt/content/%s" // HASH
)

// 随机礼物抽奖
const (
	// LockGiftDraw3 随机礼物抽奖锁
	// params: userID; luckyGiftID; luckyGiftNum
	LockGiftDraw3 cache.KeyFormat = "lock:gift/draw/%d/%d_%d"

	// KeyGiftDrawMissCount3 随机礼物未抽中某奖池大奖次数
	// params: userID; luckyGiftID; luckyGiftNum
	KeyGiftDrawMissCount3 cache.KeyFormat = "gift/draw/miss/%d/%d_%d"

	// KeyGiftDrawStatus3 随机礼物奖池大奖抽取状态
	// params: userID; luckyGiftID; luckyGiftNum
	// value: {
	//   "miss_count": 0, // 连续未抽中大奖次数
	//   "ssr_count": 0, // 累计抽中大奖次数
	// }
	KeyGiftDrawStatus3 cache.KeyFormat = "gift/draw/status/%d/%d_%d" // HASH

	// KeyUserForbidSendLuckyGift1 禁止用户送出随机礼物
	KeyUserForbidSendLuckyGift1 cache.KeyFormat = "user:forbid:send:lucky_gift:%d" // STRING
)

const (
	// LockLuckyBoxDraw2 宝盒抽奖锁
	// params: goods_id, user_id
	LockLuckyBoxDraw2 cache.KeyFormat = "lock:lucky_box:draw:%d:%d" // STRING

	// KeyLuckyBoxDrawMissCount2 宝盒未抽中某奖池大奖次数
	// params: user_id, pool_id
	KeyLuckyBoxDrawMissCount2 cache.KeyFormat = "lucky_box/draw/miss/%d/%d" // STRING
)

// KeyBannersMetaBanner1 meta/banner 缓存
// params: 分钟/5
const KeyBannersMetaBanner1 PrefixFormatter = "banners/meta/banner/%02d"

// KeyRecommendedTopCoverGray0 首页推荐直播间封面黑白处理
const KeyRecommendedTopCoverGray0 cache.KeyFormat = "recommended/top/cover/gray"

// KeyRecommendedEvents1 推荐活动缓存
// params: 分钟/5
const KeyRecommendedEvents1 cache.KeyFormat = "recommended/events/%02d"

// KeyRecommendedPopups1 活动小窗缓存
// params: 分钟/5
const KeyRecommendedPopups1 cache.KeyFormat = "recommended/popups/%02d"

// LockGiftSetOrder1 礼物触发更新锁
// params: 礼物 ID GiftID
const LockGiftSetOrder1 cache.KeyFormat = "lock/gift/setorder/%d"

// 活动
const (
	// KeyUsersActivityIncreaseCount1 活动加成增加榜单次数记录缓存 user_id:count
	// 参数: event_id;
	KeyUsersActivityIncreaseCount1 cache.KeyFormat = "users/activity/increase/count/%d" // HASH

	// KeyUsersActivityIncreaseCount2 活动加成增加榜单次数记录缓存 user_id:count
	// 参数: event_id; 后缀
	KeyUsersActivityIncreaseCount2 cache.KeyFormat = "users/activity/increase/count/%d/%s" // HASH

	// KeyActivityWallContents1 告白墙的文案，key 是 content_id, value 是具体的文案
	// 参数 eventID
	KeyActivityWallContents1 cache.KeyFormat = "activity/wall/contents/%d" // HASH

	// KeyAdditionalScoreList1 管理员额外添加积分记录
	// params: 活动的 event_id
	KeyAdditionalScoreList1 PrefixFormatter = "admin/additional/score/%d" // SET

	// KeyGiftsActivityIncreaseCount2 活动礼物发放次数记录缓存
	// 参数: event_id; gift_id
	KeyGiftsActivityIncreaseCount2 cache.KeyFormat = "gifts/activity/increase/count/%d/%d" // SET

	// KeyGiftsActivityRewardedRoomsSet1 活动礼物发放名单
	// 参数: event_id
	KeyGiftsActivityRewardedRoomsSet1 cache.KeyFormat = "gifts/activity/rewarded/rooms/event_%d" // SET

	// KeyActivityAllRewardCount1 活动期间送礼奖励总数统计
	// 参数: event_id
	// field: value: send_gift_count: ${count}
	KeyActivityAllRewardCount1   PrefixFormatter = "activities/reward_%d" // HASH
	FieldActivityAllRewardCount0 cache.KeyFormat = "send_gift_count"

	// KeyActivityRoomRewardCount2 活动期间直播间收到的奖励总数统计
	// 参数: event_id, date (如: 20210102)
	// field: value: ${room_id}: ${count}
	KeyActivityRoomRewardCount2 PrefixFormatter = "activities/reward_%d/%s" // HASH

	// KeyActivityOngoingLiveRankEvent0 正在进行活动配置
	KeyActivityOngoingLiveRankEvent0 PrefixFormatter = "activities/ongoing_liverank_events" // STRING

	// LockActivityUserReceive2 用户领取奖励外观的锁
	// 参数: event_id, user_id
	LockActivityUserReceive2 PrefixFormatter = "lock:activities/user/receive/%d/%d" // STRING

	// KeyActivityPool1 活动奖池
	// 参数: event_id; key
	KeyActivityPool1 PrefixFormatter = "activity/pool/%s" // String

	// KeyActivityComplete1 活动已达标的参与者
	// 参数: event_id/key
	KeyActivityComplete1 PrefixFormatter = "activity/complete/%s" // SET

	// KeyActivityUserStatus2 直播活动的用户状态，如领奖状态等
	// 参数: event_id, key
	// field: value: ${user_id}: ${status}
	KeyActivityUserStatus2 PrefixFormatter = "activity/users/status/%d/%s" // HASH

	// KeyActivityUserCollect2 直播活动的用户抽奖状态
	// 参数: event_id, user_id
	// field: value: ${user_id}: ${status}
	KeyActivityUserCollect2 PrefixFormatter = "activity/users/collect/%d/%d" // SET

	// KeyActivityLuckyUser1 直播活动幸运用户
	// 参数: event_id
	KeyActivityLuckyUser1 PrefixFormatter = "activity/users/lucky/%d" // STRING

	// KeyActivityRankFreePoint1 榜单统计免费礼物积分
	// 参数: ${event_id/key}
	// field: value: ${user_id}: ${point}
	KeyActivityRankFreePoint1 PrefixFormatter = "activity/user/point/%s" // HASH

	// KeyActivityGiftWall2 活动礼物墙
	// 参数: event_id/key, user_id
	// field: value: ${gift_id}: ${num}
	KeyActivityGiftWall2 PrefixFormatter = "activity/wall/%s/%d" // HASH
)

// PK 相关
const (
	// KeyPKFighting1 PK 打榜缓存
	// 参数: room_id
	KeyPKFighting1 cache.KeyFormat = "room/pk/fighting/%d" // STRING
)

// KeyRoomsChannelSetProvider0 直播间计划平滑切换的推流提供商
const KeyRoomsChannelSetProvider0 PrefixFormatter = "rooms/channel/setprovider" // STRING

// KeyUsersBlockList0 存放主站管理后台添加的黑名单用户
// member: userID, score: 到期时间, 负数表示永封
const KeyUsersBlockList0 PrefixFormatter = "users/block_list" // ZSET

// 互动相关
const (
	// KeyRoomsInteractionVoteConfig0 存放直播间当前的互动礼物投票配置
	KeyRoomsInteractionVoteConfig0 PrefixFormatter = "rooms/interaction/vote/config" // STRING

	// KeyRoomsInteraction1 存放直播间投票中的互动信息
	// 参数: 直播间 ID, 值：{"vote": {"vote_id": "5ab9d5f1bc9b53298ce5a5a9", "gift_ids": [141, 142]}}
	KeyRoomsInteraction1 PrefixFormatter = "rooms/interaction/%d" // STRING

	// LockRoomsInteraction1 存放直播间当前的互动类型
	// 参数: 直播间 ID, 值: vote
	LockRoomsInteraction1 cache.KeyFormat = "lock:rooms/interaction/%d" // STRING
)

// KeyABTest2 下发的灰度配置，值为 [0, 100]
// params: abtest 类型，设备类型
const KeyABTest2 cache.KeyFormat = "abtest:%s/equip:%d" // string

const (
	// KeyGashaponPoolRoom1 大奖翻倍后的奖池
	// params: 房间号
	KeyGashaponPoolRoom1 cache.KeyFormat = "gashapon/pool/room/%d" // STRING
)

// KeyHotRecommendTimeOffset0 用于热门列表推荐位测试的时间偏移（String 类型）
const KeyHotRecommendTimeOffset0 cache.KeyFormat = "hot_recommend_time_offset"

// KeyGashaponPrizeList0 扭蛋商品中奖公示
// NOTICE: 没有过期时间
const KeyGashaponPrizeList0 cache.KeyFormat = "gashapon/prize/list" // LIST

// Worker 相关
const (
	KeyLockWorkerFetchPlayback0 cache.KeyFormat = "live-service:lock:worker:fetch_playback"
	KeyWorkerPlayback0          cache.KeyFormat = "live-service:worker:playback"
	KeyWorkerErrors1            cache.KeyFormat = "live-service:worker:errors:%s"        // ZSET
	KeyCronRecommendLiveIDs0    cache.KeyFormat = "live-service:cron:recommend-live-ids" // SET
	KeyCronNotifyHourRank0      cache.KeyFormat = "live-service:cron:notify-hour-rank"   // STRING
)

// 验证码相关
const (
	// KeyMobileVCode1 创建公会手机验证码
	KeyMobileVCode1 cache.KeyFormat = "mobile_%s" // HASH
	// KeyCounterVCodeIP1 发送验证码对 IP 的限制
	KeyCounterVCodeIP1 cache.KeyFormat = "validate:identifyCode:%s"
	// KeyCounterVCodeUID1 发送验证码对 UID 的限制
	KeyCounterVCodeUID1 cache.KeyFormat = "validate:confirm:%d"
)

// 神话推荐
const (
	// LockNobleRecommend1 新增神话推荐的锁，参数是推荐开始时间的时间戳
	LockNobleRecommend1 cache.KeyFormat = "lock:noble:recommend:%s"

	// LockNobleRecommendNotify0 神话推荐开始和到期通知锁
	LockNobleRecommendNotify0 cache.KeyFormat = "lock:noble:recommend:notify"
)

// 提现相关
const (
	// 银行卡一致性验证失败的用户 ID（Set 类型）
	KeyUserIDsWrongBankInfo0 cache.KeyFormat = "user_ids:wrong_bank_info"

	// 主播月最低提现金额（String 类型）
	KeyMinWithdrawValueLimit0 cache.KeyFormat = "withdrawal_price"

	// 已取消主播月提现总金额小于等于十万元限制的名单（Set 类型）
	KeyUserIDsWithoutWithdrawLevelOneLimit0 cache.KeyFormat = "disable_withdrawal_limit_list"
)

// LockMetaPullURLs 从数据库获取在播流的锁
const LockMetaPullURLs cache.KeyFormat = "lock:meta:pull:urls"

// LockGuildAgentOperate1 公会添加或删除经纪人操作锁
const LockGuildAgentOperate1 cache.KeyFormat = "lock:guild:agent:operate:%d"

// LockGuildAgentAssign1 公会分配或取消分配主播操作锁
const LockGuildAgentAssign1 cache.KeyFormat = "lock:guild:agent:assign:%d"

// LockGuildRecommendHot3 公会热门推荐位请求锁，避免并发同时插入数据
// params: 推荐位置; 分区; 推荐开始时间戳（单位：秒）
const LockGuildRecommendHot3 cache.KeyFormat = "lock:guild:recommend:hot:position:%d:name:%s:start_time:%d"

// PK 相关
const (
	// LockRoomPKEscapePunishment1 直播间多次逃跑后的 PK 冷静期，参数 room_id
	LockRoomPKEscapePunishment1 cache.KeyFormat = "lock:room/pk/escape/punishment/%d" // STRING

	// LockRoomPKInvitationRequest1 发起指定 PK 邀请的锁
	// params: room_id
	LockRoomPKInvitationRequest1 cache.KeyFormat = "lock:room/pk/invitation/request/%d" // STRING

	// LockInvitationPKOpen0 控制指定 PK 功能的开放
	LockInvitationPKOpen0 cache.KeyFormat = "lock:invitation_pk:open" // STRING

	// LockRoomPKStartMatch1 直播间开始 PK 的锁, 参数 room_id
	LockRoomPKStartMatch1 cache.KeyFormat = "lock:room:pk:start:match:%d" // STRING

	// KeyRoomPKPeakLimit1 高峰时段的 PK 次数限制
	// param: 日期 (20220722)
	KeyRoomPKPeakLimit1 cache.KeyFormat = "room:pk:peak:limit:%s" // ZSET
)

// 直播动态相关
const (
	// KeyUserLiveFeedTodayDisplayTimes2 主播动态今日在指定用户的动态中的展示次数
	// 参数: 日期（格式 20060102）和用户 ID
	// member: 主播 ID, score: 今日展示次数
	// 过期时间：36 小时
	KeyUserLiveFeedTodayDisplayTimes2 cache.KeyFormat = "user_live_feed:today_display_times:%s:%d" // ZSET

	// LockUserLiveFeed1 获取直播动态的锁，参数：用户 ID
	LockUserLiveFeed1 cache.KeyFormat = "lock:user_live_feed:%d" // STRING
)

// KeyUserConsumptionLast29Days2 用户前 29 天消费缓存，不含今日
// param: user_id, date
const KeyUserConsumptionLast29Days2 cache.KeyFormat = "live-service:user-consumption:last29days:user_id:%d:date:%d" // STRING

// KeyUserConsumptionThisMonth2 用户本月消费缓存，不含今日
// param: user_id, date
const KeyUserConsumptionThisMonth2 cache.KeyFormat = "live-service:user-consumption:this_month:user_id:%d:date:%d" // STRING

// KeyUsersPotentialHighness0 潜力上神用户名单
const KeyUsersPotentialHighness0 PrefixFormatter = "users/potential_highness" // SET

// KeyRoomsRankPointDaily2 每日用户为主播人气值累计记录
// param: user_id; date (20060102)
const KeyRoomsRankPointDaily2 PrefixFormatter = "rooms/rankpoint/%d/%s" // HASH

// KeyRoomsViewDaily2 每日访问该直播间的用户 ID
// param: room_id, date (20060102)
const KeyRoomsViewDaily2 PrefixFormatter = "rooms/view/%d/%s" // SET

// KeyUserWishNum2 用户许愿次数
// param: goods_id; 许愿周期开始时间（2006010215）
// HASH 的 field 是用户 ID, value 是许愿次数
const KeyUserWishNum2 cache.KeyFormat = "user:wish:num:goods_id:%d:%s" // HASH

// 礼物墙相关
const (
	// KeyRoomGiftWallActivatedGiftIDs2 直播间礼物墙周期内点亮礼物
	// param: room_id; _period_id
	KeyRoomGiftWallActivatedGiftIDs2 cache.KeyFormat = "room:gift_wall:activated:gift_ids:%d:%s" // SET
)

// 礼物红包相关
const (
	// KeyRoomRedPacketGiftNum1 直播间礼物红包内剩余礼物数量
	// param: _red_packet_id
	// field: gift_id, val: num
	KeyRoomRedPacketGiftNum1 cache.KeyFormat = "room:red_packet:gift:num:%s" // HASH

	// LockRedPacketUserGrab1 用户抢礼物红包锁，参数：抢红包的用户 ID
	LockRedPacketUserGrab1 cache.KeyFormat = "lock:red_packet:user:grab:%d" // STRING
)

// LockRoomUserMedalNotify3 用户直播间内勋章获取和升级消息锁
// param: room_id; user_id; medal level
const LockRoomUserMedalNotify3 cache.KeyFormat = "lock:room:user:medal_notify:%d:%d:%d" // STRING

// KeyRoomsEnableAgora0 声网白名单
const KeyRoomsEnableAgora0 PrefixFormatter = "rooms/enable_agora" // SET

// 表情相关
const (
	// LockStickerSend1 发送表情锁
	// param: user_id
	LockStickerSend1 cache.KeyFormat = "lock:sticker:send:%d" // STRING
)

// KeyRedeemUserPoint2 用户兑换商城的积分
// param: year (2023); user_id
const KeyRedeemUserPoint2 cache.KeyFormat = "shop:redeem:user_point:%d:%d" // STRING

// KeyRedeemPointBlockedGiftIDs0 送出时不加兑换积分的随机礼物列表（ID 是随机抽奖礼物的 ID 而非实际送出的礼物）
const KeyRedeemPointBlockedGiftIDs0 cache.KeyFormat = "shop:redeem:blocked:gift_ids" // SET

// ParseRewardRoomID 从形如 "reward_{roomID}" 的 Field 中获取房间号
func ParseRewardRoomID(key string) int64 {
	key = strings.SplitN(key, "_", 2)[1]
	res, _ := strconv.ParseInt(key, 10, 64)
	return res
}

// 常驻兑换商城相关
const (
	// KeyShopRedeemUserRedeemedCount2 用户对各个商品的剩余已兑换次数
	// param: user_id, goods_id
	KeyShopRedeemUserRedeemedCount2 cache.KeyFormat = "shop:redeem:user:redeemed_count:%d:%d" // STRING
)

// 直播间内消息统计
const (
	// KeyMessageUserLimit2 同一用户的消息限制：每秒 2 条
	// params: user_id, 该时刻的秒数（例：2024-01-01 19:00:02，则取 02）
	KeyMessageUserLimit2 PrefixFormatter = "users/message/%d/counter/%s" // STRING

	// KeyMessageUserRepeatLimit2 同一用户的消息重复消息限制：5 秒内不能发送相同内容
	// params: user_id, 该时刻的秒数 / 5（例：2024-01-01 19:00:30，则 30/5 取 06）
	KeyMessageUserRepeatLimit2 PrefixFormatter = "users/message/%d/%s" // SET

	// KeyMessageRoomLimit2 房间内的消息全局限制：每 500 毫秒 4 条
	// params: room_id, 该时刻的秒数 + "0" 或 "1"（例：2024-01-01 19:00:02 则取 02；若毫秒数 < 500 则为 020，反之为 021）
	KeyMessageRoomLimit2 PrefixFormatter = "rooms/message/%d/counter/%s" // STRING

	// KeyMessageMultiConnectGroupLimit2 主播连线组的互通消息全局限制：每 500 毫秒默认 10 条（阈值在 params 可配置）
	// params: group_id, 该时刻的秒数 + "0" 或 "1"（例：2024-01-01 19:00:02 则取 02；若毫秒数 < 500 则为 020，反之为 021）
	KeyMessageMultiConnectGroupLimit2 PrefixFormatter = "groups/message/%d/counter/%s" // STRING
)

const (
	// KeyUsersLevelGte85Consumption1 自然月用户等级大于 85 的用户消费
	// params: year + month (202305)
	// field: user_id, value: consumption 单位：钻石
	KeyUsersLevelGte85Consumption1 cache.KeyFormat = "users:level_gte_85:consumption:%s" // ZSET
)

// LockDanmakuSendLimit1 付费弹幕发送频率限制锁
// param: user_id
const LockDanmakuSendLimit1 cache.KeyFormat = "live-service:danmaku:send:limit:%d" // STRING

// 福袋相关
const (
	// LockLuckyBagRedeemPrizeLimit1 福袋广播剧兑换限制锁
	// params: 中奖记录 ID
	LockLuckyBagRedeemPrizeLimit1 PrefixFormatter = "lucky_bag:redeem_prize:limit:%d" // STRING

	// LockUserLuckyBagJoin1 直播间用户参与福袋锁
	// params: 用户 ID
	LockUserLuckyBagJoin1 PrefixFormatter = "lock:user:lucky_bag:join:%d" // STRING

	// KeyLuckyBagJoinNum1 直播间福袋参与人数
	// params: 福袋 ID
	KeyLuckyBagJoinNum1 PrefixFormatter = "lucky_bag:join_num:%d" // STRING
)

const (
	// LockGuildIncomeExport1 导出公会收益 CSV 锁
	// params: 用户 ID
	LockGuildIncomeExport1 cache.KeyFormat = "lock:guild:income:export:%d" // STRING
)

// KeyRoomPaidUser2 统计直播间本场消费用户
// params: room_id, open_time
const KeyRoomPaidUser2 cache.KeyFormat = "live-service:room:paid_user:%d:%d" // SET

// KeyActivityCronJobList0 活动的 cron jobs 列表
// member: job, score: timestamp, 单位：秒
const KeyActivityCronJobList0 cache.KeyFormat = "activity/cron/job/list" // ZSET

const (
	// LockLuckyGiftDrop2 随机礼物投放锁
	// params: config_id; timestamp
	LockLuckyGiftDrop2 cache.KeyFormat = "lock:lucky_gift_drop:%d:%d" // STRING

	// KeyLuckyGiftDropCount1 随机礼物投放次数
	// params: config_id
	KeyLuckyGiftDropCount1 cache.KeyFormat = "lucky_gift_drop:count:%d" // STRING
)

// 主播连线相关
const (
	// KeyMultiConnectGroupGiftScore1 主播连线礼物分数
	// params: group_id
	KeyMultiConnectGroupGiftScore1 cache.KeyFormat = "multi-connect:group:gift_score:%d" // ZSET

	// LockMultiConnectGroupCreate1 创建主播连线锁
	// params: 主麦直播间 ID
	LockMultiConnectGroupCreate1 cache.KeyFormat = "lock:multi-connect:group:create:%d" // STRING

	// LockMultiConnectGroupMemberUpdate1 主播连线成员变动
	// params: group ID
	LockMultiConnectGroupMemberUpdate1 cache.KeyFormat = "lock:multi-connect:group:member_update:%d" // STRING

	// KeyMultiConnectGroupUserRankEnable1 主播连线用户榜单是否开启
	// params: group_id
	KeyMultiConnectGroupUserRankEnable1 cache.KeyFormat = "multi-connect:group:user_rank_enable:%d" // STRING

	// LockMultiConnectGroupUserListenScore2 主播连线组用户收听积分
	// params: group ID, user ID
	LockMultiConnectGroupUserListenScore2 cache.KeyFormat = "lock:multi-connect:group:user:listen_score:%d:%d" // STRING

	// KeyMultiConnectGroupUserRank1 主播连线组用户总积分榜单（送礼积分 + 收听积分）
	// params: group ID
	KeyMultiConnectGroupUserRank1 cache.KeyFormat = "multi-connect:group:%d:user_rank" // ZSET

	// KeyMultiConnectGroupUserRankCache1 主播连线组用户榜单结果缓存
	// params: group ID
	// 存储经过在线状态、隐身、等级等过滤和排序后的最终榜单数据
	KeyMultiConnectGroupUserRankCache1 cache.KeyFormat = "multi-connect:group:%d:user_rank:cache" // STRING

	// LockMultiConnectGroupUserRankCacheUpdate1 主播连线组用户榜单结果缓存更新锁
	// params: group ID
	LockMultiConnectGroupUserRankCacheUpdate1 cache.KeyFormat = "lock:multi-connect:group:user_rank:cache_update:%d" // STRING
)

// 礼物升级相关
const (
	// LockGiftUpgrade2 礼物升级锁
	// params: user_id; base_gift_id
	LockGiftUpgrade2 cache.KeyFormat = "lock:gift:upgrade:%d:%d"
)

// tianma 推荐算法相关
const (
	// FallbackKey1 推荐算法网络异常时的兜底缓存 key
	// params: buvid
	FallbackKey1 cache.KeyFormat = "fallback:buvid:%s" // STRING

	// TestLiveTabRecommendRoomList1 直播首页算法推荐直播间测试列表（仅 UAT 测试使用）
	// params: catalog_id
	TestLiveTabRecommendRoomList1 cache.KeyFormat = "test:live_tab_recommend_room_list:%d" // STRING
)

// 粉丝团宝箱相关
const (
	// KeyFansBoxRewardsPrizeConsumedStockNum4 粉丝宝箱当天已消耗的礼物库存数量
	// param: room 直播间 ID；level 宝箱等级；type（1：猫粮；2：免费礼物；3：专属礼物 + 免费礼物；4：头像框）；date (20060102)
	KeyFansBoxRewardsPrizeConsumedStockNum4 cache.KeyFormat = "fans_box:rewards:prize:consumed_stock:room:%d:level:%d:type:%d:date:%s" // STRING

	// LockFansBoxDraw1 用户领取粉丝宝箱奖励锁，参数：领取宝箱的用户 ID
	LockFansBoxDraw1 cache.KeyFormat = "lock:fans_box:draw:user_id:%d" // STRING
)
