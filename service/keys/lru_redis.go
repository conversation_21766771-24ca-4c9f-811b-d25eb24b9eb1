package keys

import "github.com/MiaoSiLa/missevan-go/service/cache"

// KeyConnectGet1 api/v2/channel/connect/get 缓存
// params: 房间号
const KeyConnectGet1 cache.KeyFormat = "live-service:connect:get:%d"

// KeyUserFollowed1 某用户关注的所有用户
// params: 用户 ID
const KeyUserFollowed1 cache.KeyFormat = "live-service:%d:followed"

// KeyUserFollowedCreator1 某用户关注的所有主播
// params: 用户 ID
const KeyUserFollowedCreator1 cache.KeyFormat = "live-service:%d:followed_creator"

// KeyRankInvisible1 某房间的榜单隐身用户
// params: 房间号
const KeyRankInvisible1 cache.KeyFormat = "live-service:rankInvisible:%d"

// KeyChatroomVipList1 贵宾榜缓存
const KeyChatroomVipList1 cache.KeyFormat = "live-service:chatroom:vip:list:%d"

// LRULockChatroomVipList1 贵宾榜缓存更新锁，设置成功才需要更新缓存
const LRULockChatroomVipList1 cache.KeyFormat = "lock:live-service:chatroom:vip:list:%d" // STRING

// KeyChatroomRankRevenue1 直播间主播贡献榜总榜缓存
const KeyChatroomRankRevenue1 cache.KeyFormat = "live-service:chatroom:rank_revenue:%d"

// KeyMetaPullURLs0 拨测的在播流缓存
const KeyMetaPullURLs0 cache.KeyFormat = "live-service:meta:pull:urls"

// KeyEventRoomCache2 房间收益信息缓存
// 参数: event_id, room_id
const KeyEventRoomCache2 cache.KeyFormat = "live-service:event:%d:room_id:%d" // String

// KeyLiveCatalogs1 直播 catalogs 缓存
const KeyLiveCatalogs1 cache.KeyFormat = "live-service:new-catalogs:%d"

// KeyLiveMetaTabs0 meta tabs 缓存
const KeyLiveMetaTabs0 cache.KeyFormat = "live-service:meta:tabs"

// KeyParams1 直播间参数配置，参数是 mongodb params.key 的值
const KeyParams1 cache.KeyFormat = "live-service:params:%s" // string

// KeySoundCVUser1 某音频 cvuser 信息，包含所属广播剧是否屏蔽随机推荐
// param: sound_id
const KeySoundCVUser1 cache.KeyFormat = "live-service:sound:%d:cvuser:v2" // STRING

// KeyLiveGoodsList1 直播商品缓存
// param: goods_type 商品类型
const KeyLiveGoodsList1 cache.KeyFormat = "live-service:goods_type:%d:list" // STRING

// KeyGift1 缓存礼物信息
// param: gift_id
const KeyGift1 cache.KeyFormat = "live-service:gift:%d" // STRING

// KeyRoomRevenueInLast30Days2 主播前三十天收益缓存
// param: room_id, date
const KeyRoomRevenueInLast30Days2 cache.KeyFormat = "room:revenue:in_last_30days:%d:date:%d" // STRING

// KeyAllShowTags0 所有可见的标签缓存
const KeyAllShowTags0 cache.KeyFormat = "live-service:all-show-tags"

// KeyOngoingLiveShow0 正在进行的个人场
const KeyOngoingLiveShow0 cache.KeyFormat = "live-service:ongoing-live-show" // STRING

// KeyUserLiveFeed1 直播动态，参数：用户 ID，过期时间：10 分钟
const KeyUserLiveFeed1 cache.KeyFormat = "user_live_feed:%d" // STRING

// KeyLiveNotice1 生效中的公告
// param: notice type
const KeyLiveNotice1 cache.KeyFormat = "live-service:live_notice:%d" // STRING

// KeyReward1 直播奖励
// param: reward id
const KeyReward1 cache.KeyFormat = "live-service:reward:%d" // STRING

// KeyRewards1 奖励缓存
// param: reward ids
const KeyRewards1 cache.KeyFormat = "live-service:rewards:%s" // STRING

// LRUKeyRoomsVipNum1 直播间贵宾数目缓存
const LRUKeyRoomsVipNum1 cache.KeyFormat = "live-service:room:%d:vip:num" // STRING

// LRUKeyUserBlock1 缓存用户拉黑的用户 ID
// param: user_id
const LRUKeyUserBlock1 cache.KeyFormat = "live-service:user:block:%d" // SET

// KeyRoomMedalPointMulti 直播间亲密度翻倍缓存
// params: 主播 ID
const KeyRoomMedalPointMulti cache.KeyFormat = "live-service:room:medal_point_multi:%d" // STRING

// live/:roomID 缓存相关
const (
	// KeyRoomStatusCache1 live/:roomID 的缓存之一
	// params: room_id
	KeyRoomStatusCache1 cache.KeyFormat = "live/status/%d" // STRING
)

// KeyMedalRoomFollowList2 有亲密度的关注直播间缓存，有效时间：5 分钟
// params: user_id（用户 ID）, type（榜单类型 0: 所有关注主播，1: 正在开播的关注主播）
const KeyMedalRoomFollowList2 cache.KeyFormat = "live-service:medal_room:follow_list:%d:list_type:%d" // STRING

// KeyAllShowCustomTagGroups0 查询生效的个性词条列表缓存，有效时间：10 分钟
const KeyAllShowCustomTagGroups0 cache.KeyFormat = "live-service:all_show_custom_tag_groups" // STRING

// KeyAllShowCustomTagsMap0 查询生效的个性词条缓存，有效时间：10 分钟
const KeyAllShowCustomTagsMap0 cache.KeyFormat = "live-service:all_show_custom_tags_map" // STRING

// KeyHighRiskConnectStatus2 高风险连接状态缓存，有效时间：5 分钟
// params: user_id, room_id
const KeyHighRiskConnectStatus2 cache.KeyFormat = "live-service:high_risk_connect_status:%d:%d" // STRING

// LockLiveNewUserRewardRecord1 直播新用户奖励发放记录锁，有效时间：1 天
// params: user_id
const LockLiveNewUserRewardRecord1 cache.KeyFormat = "live-service:live_new_user_reward_record:%d"

// KeyUserDailyTaskIsNewStatus2 用户每日任务状态
// params: user_id, 当日起始时间 20240501
const KeyUserDailyTaskIsNewStatus2 cache.KeyFormat = "live-service:user:daily_task:is_new:status:%d:%s" // STRING

// KeyUser7DaysListenLiveTotalDuration1 用户近 7 日直播收听总时长
// params: 用户 ID
const KeyUser7DaysListenLiveTotalDuration1 cache.KeyFormat = "live-service:user:7days:listen_live:total_duration:%d" // STRING

// KeyUser7DaysRoomFreeGiftNum1 用户近 7 日直播间免费礼物数量
// params: 用户 ID
const KeyUser7DaysRoomFreeGiftNum1 cache.KeyFormat = "live-service:user:7days:room:free_gift:num:%d" // STRING

// 底部导航栏直播推荐使用
const (
	// KeyRecommendRooms2 推荐直播间
	// params: 用户 ID，生成时间
	KeyRecommendRooms2 PrefixFormatter = "recommend:rooms:%d:%d" // STRING

	// KeyUserRecommendBlockList1 用户推荐直播间黑名单
	// params: 用户 ID
	KeyUserRecommendBlockList1 PrefixFormatter = "recommend:block_list:%d" // STRING
)

// 搜索页直播推荐使用
const (
	// KeySearchRecommendRooms1 搜索页推荐直播间
	// params: 用户 ID
	KeySearchRecommendRooms1 cache.KeyFormat = "search:recommend:rooms:%d" // STRING
)

// KeyFansBoxList0 粉丝团宝箱列表缓存，有效时间：1 分钟
const KeyFansBoxList0 cache.KeyFormat = "live-service:fans_box:list" // STRING
