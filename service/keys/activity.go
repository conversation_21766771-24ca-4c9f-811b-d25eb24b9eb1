package keys

import "github.com/MiaoSiLa/missevan-go/service/cache"

const (
	// LockBoxUserReceive2 用户领取某个类型宝箱的锁
	// 参数: event_id, user_id
	LockBoxUserReceive2 cache.KeyFormat = "lock:activities/box/receive/%d/%d" // STRING
	// LockBoxUserReceiveOne2 用户领取宝箱的锁，指定时间内只能领取一个宝箱
	// 参数: event_id, user_id
	LockBoxUserReceiveOne2 cache.KeyFormat = "lock:activities/box/receive_one/%d/%d" // STRING

	// LockActivityRankComponent1 榜单组件控制锁
	// 参数: event_id
	LockActivityRankComponent1 cache.KeyFormat = "lock:activities/rank/component/%d" // STRING

	// KeyBoxPrizeAndCount4 听众宝箱的剩余奖品和数量
	// 参数: event_id, date (如: 20210102), room_id, type
	// TODO: 整合成一个 KeyFormat
	KeyBoxPrizeAndCount4 cache.KeyFormat = "activities/box/%d/%s/%d/%d" // HASH
	// KeyBoxPrizeAndCount3 宝箱的剩余奖品和数量，目前适用于 175 活动
	// 参数: event_id, room_id, type
	KeyBoxPrizeAndCount3 cache.KeyFormat = "activities/box/%d/%d/%d" // HASH

	// KeyBoxRoomUserReceiveCount4 领取某个直播间某个类型宝箱的用户 ID 和对应领取次数
	// 参数: event_id, date (如: 20210102), room_id, level
	// TODO: 整合成一个 KeyFormat
	KeyBoxRoomUserReceiveCount4 cache.KeyFormat = "activities/box/receive_count/%d/%s/%d/%d" // HASH
	// KeyBoxRoomUserReceiveCount3 领取某个直播间某个类型宝箱的用户 ID 和对应领取次数，目前适用于 175 活动
	// 参数: event_id, room_id, level
	KeyBoxRoomUserReceiveCount3 cache.KeyFormat = "activities/box/receive_count/%d/%d/%d" // HASH

	// KeyAnnualHourTop0 获得小时榜第一的主播
	// 开始小时作为成员，比如 2006010215，开始小时后一小时内获得小时榜第一的主播 ID 作为成员
	KeyAnnualHourTop0 cache.KeyFormat = "annual/hourtop" // HASH
)

// KeyActivityQuestViewRoom2 完成任务访问直播间的用户
// 参数：event_id; 日期（如 20220615）
const KeyActivityQuestViewRoom2 cache.KeyFormat = "activities/quest/view_room/%d/%s" // SET
