package storage

import (
	"bytes"
	"fmt"
	"io"
	"net/url"
	"path"
	"strings"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/image"
)

// OSS 文件目录
const (
	PathPrefixGuildImage      = "live/guild/"
	PathPrefixAvatarFrame     = "live/user/avatarframe/"
	PathPrefixBanner          = "live/banner/"
	PathPrefixSchedule        = "live/room/schedule/"
	PathPrefixBackground      = "live/room/background/"
	PathPrefixGift            = "live/gifts/"
	PathPrefixTabIcon         = "live/tags/icon/"
	PathPrefixUserLevel       = "live/userlevels/"
	PathPrefixEvent           = "live/event/"
	PathPrefixLiveIcon        = "live/room/icon/"
	PathPrefixLiveSticker     = "live/stickers/"
	PathPrefixLiveStickerIcon = "live/stickers/icons/"
	PathPrefixCreatorcard     = "live/creatorcard/"
	PathPrefixPopupImage      = "live/popup/"
	PathPrefixBubble          = "live/bubbles/"
	PathPrefixMedalFrame      = "live/medalframes/"
)

// 带协议的 OSS URL 前缀
const (
	SchemeURLPrefixLiveLog = "sound://live/log/"
)

func uploadToOSS(scheme string, fileName string, file io.Reader) (string, error) {
	schemeURL, err := service.Storage.Upload(scheme, fileName, file)
	if err != nil {
		return "", err
	}
	return schemeURL, nil
}

// CheckStorage 检查是否需要上传
func CheckStorage(sourceURL upload.SourceURL) bool {
	_, err := service.Upload.ToResource(sourceURL)
	return err == nil
}

// UploadToOSS 上传到 OSS
// SourceURL 当前缀为上传文件配置项目录时，将文件上传到 OSS 并返回协议地址
func UploadToOSS(sourceURL upload.SourceURL, pathPrefix string) (string, error) {
	res, err := service.Upload.ToResource(sourceURL)
	if err != nil {
		return "", err
	}
	file, err := res.Open()
	if err != nil {
		return "", err
	}
	defer file.Close()

	return uploadToOSS(config.DefaultCDNScheme, newFileName(sourceURL, pathPrefix), file)
}

// UploadToTarget 上传 source 文件到 ossTarget 路径
func UploadToTarget(source upload.SourceURL, ossTarget string) error {
	res, err := service.Upload.ToResource(source)
	if err != nil {
		return err
	}
	file, err := res.Open()
	if err != nil {
		return err
	}
	defer file.Close()
	u, err := url.Parse(ossTarget)
	if err != nil {
		return err
	}
	scheme := u.Scheme
	if scheme != "" {
		ossTarget = strings.TrimLeft(ossTarget, fmt.Sprintf("%s://", scheme))
	} else {
		scheme = config.DefaultCDNScheme
	}
	_, err = uploadToOSS(scheme, ossTarget, file)
	return err
}

// CovertUploadToOSS 缩放图片后上传到 oss
func CovertUploadToOSS(sourceURL upload.SourceURL, pathPrefix string, width, height int) (string, error) {
	res, err := service.Upload.ToResource(sourceURL)
	if err != nil {
		return "", err
	}
	file, err := res.Open()
	if err != nil {
		return "", err
	}
	defer file.Close()

	w := new(bytes.Buffer)
	format, err := image.FormatFromFilename(sourceURL.String())
	if err != nil {
		return "", err
	}
	_, _, err = image.Convert(file, w, width, height, format)
	if err != nil {
		return "", err
	}

	return uploadToOSS(config.DefaultCDNScheme, newFileName(sourceURL, pathPrefix), w)
}

func newFileName(sourceURL upload.SourceURL, pathPrefix string) string {
	nowTime := goutil.TimeNow()
	return path.Join(pathPrefix, nowTime.Format("200601/02/"), goutil.MD5(sourceURL.String())) +
		nowTime.Format("150405") + sourceURL.Ext()
}

// ParseSchemeURL 特化的 ParseSchemeURL
/*
1. 支持不 parse http 和 https
2. 支持路径不完整
3. 空路径不做处理
*/
func ParseSchemeURL(schemeURL string) string {
	if schemeURL == "" {
		return ""
	}
	u, err := url.Parse(schemeURL)
	if err != nil {
		logger.WithField("url", schemeURL).Error(err)
		// PASS
		return ""
	}
	if u.Scheme == "http" || u.Scheme == "https" {
		return schemeURL
	}
	if u.Scheme == "" {
		u.Scheme = config.DefaultCDNScheme
	}
	return service.Storage.Parse(u.String())
}

// ParseSchemeURLs 特化的 ParseSchemeURLs
/*
1. 支持不 parse http 和 https
2. 支持路径不完整
3. 空路径不做处理
4. 支持使用英文分号 ; 分割的多路径
*/
func ParseSchemeURLs(schemeURLs string) string {
	arr := strings.Split(schemeURLs, ";")
	for i := range arr {
		arr[i] = ParseSchemeURL(arr[i])
	}
	res := strings.Join(arr, ";")
	return res
}

// RemoveURLScheme 移除 url 的 scheme
// http://123.com -> 123.com
func RemoveURLScheme(u string) (string, error) {
	ur, err := url.Parse(u)
	if err != nil {
		return "", err
	}
	ur.Scheme = "http"
	u = ur.String()[7:]
	return u, nil
}
