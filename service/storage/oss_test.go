package storage

import (
	"net/http"
	"os"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/image"
)

func TestMain(m *testing.M) {
	config.InitTest()
	logger.InitTestLog()
	service.InitTest()

	os.Exit(m.Run())
}

func TestCheckStorage(t *testing.T) {
	assert := assert.New(t)

	url := upload.SourceURL("www.uat.missevan.com/test.jpg")
	assert.False(CheckStorage(url))

	url = upload.SourceURL(config.Conf.Service.Upload.URL + "test.jpg")
	assert.True(CheckStorage(url))
}

func TestNewFileName(t *testing.T) {
	assert := assert.New(t)

	url := upload.SourceURL("https://www.uat.missevan.com/files/2020-03-26/cf8ijigukshj1qc0ox8y3ylgwpbp3quv.png")
	assert.True(strings.HasPrefix(newFileName(url, PathPrefixAvatarFrame), PathPrefixAvatarFrame+goutil.TimeNow().Format("200601/02/")))
}

func TestUploadToOSS(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	url := upload.SourceURL("https://fm.example.com/testdata/test.jpg")
	schemeURL, err := UploadToOSS(url, PathPrefixAvatarFrame)
	require.NoError(err)
	assert.True(strings.HasPrefix(schemeURL, config.DefaultCDNScheme+"://"+PathPrefixAvatarFrame))
}

func TestUploadToTarget(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	url := upload.SourceURL("../../notexists.jpg")
	assert.Equal(upload.ErrSourceURL, UploadToTarget(url, ""), "路径不对")

	url = upload.SourceURL(config.Conf.Service.Upload.URL + "notexists.jpg")
	assert.Error(UploadToTarget(url, ""), "文件不存在")

	target := "\n\n\n"
	url = upload.SourceURL(config.Conf.Service.Upload.URL + "test.jpg")
	err := UploadToTarget(url, target)
	assert.Error(err, "target 不是 url")

	target = "oss://testdata/test.jpg"
	url = upload.SourceURL(config.Conf.Service.Upload.URL + "test.jpg")
	require.NoError(UploadToTarget(url, target))

	target = "testdata/test2.jpg"
	require.NoError(UploadToTarget(url, target))

	r, err := http.Get("http://static-test.maoercdn.com/testdata/test.jpg")
	require.NoError(err)
	assert.Equal(http.StatusOK, r.StatusCode)
	r, err = http.Get("http://static-test.maoercdn.com/testdata/test2.jpg")
	require.NoError(err)
	assert.Equal(http.StatusOK, r.StatusCode)
}

func TestCovertUploadToOSS(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	url := upload.SourceURL("test\naa")
	_, err := CovertUploadToOSS(url, PathPrefixAvatarFrame, 10, 10)
	assert.Error(err)

	url = upload.SourceURL(config.Conf.Service.Upload.URL + "/../oss.go")
	_, err = CovertUploadToOSS(url, PathPrefixAvatarFrame, 10, 10)
	assert.Error(upload.ErrSourceURL, err)

	url = upload.SourceURL(config.Conf.Service.Upload.URL + "test.jpg")
	schemeURL, err := CovertUploadToOSS(url, PathPrefixAvatarFrame, 10, 10)
	require.NoError(err)
	assert.True(strings.HasPrefix(schemeURL, config.DefaultCDNScheme+"://"+PathPrefixAvatarFrame))

	url = upload.SourceURL(config.Conf.Service.Upload.URL + "no_ext_file_jpg")
	_, err = CovertUploadToOSS(url, PathPrefixAvatarFrame, 10, 10)
	assert.Equal(image.ErrUnsupportedFormat, err)

	url = upload.SourceURL(config.Conf.Service.Upload.URL + "empty.jpg")
	_, err = CovertUploadToOSS(url, PathPrefixAvatarFrame, 10, 10)
	assert.Error(image.ErrUnsupportedFormat, err)
}

func TestParseSchemeURL(t *testing.T) {
	assert := assert.New(t)
	assert.Empty(ParseSchemeURL(""))
	assert.Empty(ParseSchemeURL("\thttps://test"))
	assert.Equal("http://test", ParseSchemeURL("http://test"))
	assert.Equal("https://test", ParseSchemeURL("https://test"))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"test1", ParseSchemeURL("//test1"))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"test2", ParseSchemeURL("test2"))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"test3", ParseSchemeURL("oss://test3"))
}

func TestParseSchemeURLs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	testURL := ";http://test;https://test;//test1;test2;oss://test3"
	res := ParseSchemeURLs(testURL)
	arr := strings.Split(res, ";")
	require.Len(arr, 6)
	expected := [6]string{
		"",
		"http://test",
		"https://test",
		config.Conf.Service.Storage["oss"].PublicURL + "test1",
		config.Conf.Service.Storage["oss"].PublicURL + "test2",
		config.Conf.Service.Storage["oss"].PublicURL + "test3",
	}
	for i := range arr {
		assert.Equal(expected[i], arr[i])
	}
}

func TestRemoveURLScheme(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := RemoveURLScheme("\n\n\n")
	assert.Error(err)
	after, err := RemoveURLScheme("oss://test.png")
	require.NoError(err)
	assert.Equal("test.png", after)
	after, err = RemoveURLScheme("//test.png")
	require.NoError(err)
	assert.Equal("test.png", after)
	after, err = RemoveURLScheme("/test.png")
	require.NoError(err)
	assert.Equal("/test.png", after)
	after, err = RemoveURLScheme("test.com/test.png")
	require.NoError(err)
	assert.Equal("test.com/test.png", after)
}
