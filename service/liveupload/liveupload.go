package liveupload

import (
	"errors"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	// ErrImageNum 检查图片数量错误
	ErrImageNum = errors.New("上传图片数量错误")
	// ErrImageType 检查上传图片类型错误
	ErrImageType = errors.New("图片格式只支持 AVIF, WebP, PNG, GIF")
	// ErrWebPImageType 检查 WebP 文件类型错误
	ErrWebPImageType = errors.New("上传两个资源文件时需包含 WebP 和 PNG 文件")
	// ErrAVIFImageType 检查 AVIF 文件类型错误
	ErrAVIFImageType = errors.New("上传三个资源文件时需包含 AVIF 和 WebP 和 PNG 文件")
	// ErrWebPRequireImage 检查 WebP 文件缺少 PNG
	ErrWebPRequireImage = errors.New("上传图片资源含有 WebP 时，必须同时上传 PNG 文件")
	// ErrAVIFRequireImage 检查 AVIF 文件缺少 WebP 或 PNG
	ErrAVIFRequireImage = errors.New("上传图片资源含有 AVIF 时，必须同时上传 WebP 和 PNG 文件")
)

// NewTargetPathWithSuffix 创建带自定义后缀的目标上传地址字符串
func NewTargetPathWithSuffix(s upload.SourceURL, prefix string, suffix string) upload.TargetPath {
	now := goutil.TimeNow()
	path := fmt.Sprintf(
		"%s%s%s_%s",
		prefix,
		now.Format("200601/02/"),
		goutil.MD5(string(s)),
		suffix,
	)
	return upload.TargetPath(path + filepath.Ext(s.String()))
}

// IsSchemeHTTP scheme 是否是 http
func IsSchemeHTTP(s string) bool {
	return s == "http" || s == "https"
}

// ImageURLFilter 用于分离并存入不同扩展名的图片地址
type ImageURLFilter struct {
	Primary   upload.SourceURL
	Secondary upload.SourceURL

	// TEMP: 后续需要将结构修改为切片，以便支持更多类型
	Tertiary upload.SourceURL
}

// NewImageURLFilter 检查并筛查上传图片的链接
// TODO: 这个函数需要对 urls 进行检查，如果不是能上传的路径，返回错误即可
func NewImageURLFilter(urls []upload.SourceURL) (*ImageURLFilter, error) {
	switch len(urls) {
	case 1:
		// 单图，PNG 或 GIF
		filter := new(ImageURLFilter)
		for _, url := range urls {
			// FIXME: 需要处理链接带 query 的情况
			switch filepath.Ext(strings.ToLower(url.String())) {
			case ".avif":
				return nil, ErrAVIFRequireImage
			case ".webp":
				return nil, ErrWebPRequireImage
			case ".png", ".gif":
				filter.Primary = url
			default:
				return nil, ErrImageType
			}
		}
		if filter.Primary == "" {
			return nil, ErrImageType
		}
		return filter, nil
	case 2:
		// 双图，上传 WebP 的情况
		filter := new(ImageURLFilter)
		for _, url := range urls {
			switch filepath.Ext(strings.ToLower(url.String())) {
			case ".avif":
				return nil, ErrAVIFRequireImage
			case ".webp":
				filter.Primary = url
			case ".png":
				filter.Secondary = url
			default:
				return nil, ErrImageType
			}
		}
		if filter.Primary == "" || filter.Secondary == "" {
			return nil, ErrWebPImageType
		}
		return filter, nil
	case 3:
		// TODO: 需支持 -web.avif 格式
		// 三图，上传 AVIF 的情况
		filter := new(ImageURLFilter)
		for _, url := range urls {
			switch filepath.Ext(strings.ToLower(url.String())) {
			case ".avif":
				filter.Primary = url
			case ".webp":
				filter.Secondary = url
			case ".png":
				filter.Tertiary = url
			default:
				return nil, ErrImageType
			}
		}
		if filter.Primary == "" || filter.Secondary == "" || filter.Tertiary == "" {
			return nil, ErrAVIFImageType
		}
		return filter, nil
	}
	return nil, ErrImageNum
}

// SecondaryTargetPath 获取次级 TargetPath，传入原先生成的 Storage TargetPath
func (filter *ImageURLFilter) SecondaryTargetPath(name upload.TargetPath) upload.TargetPath {
	return upload.TargetPath(strings.TrimSuffix(name.String(), filepath.Ext(name.String())) + filter.Secondary.Ext())
}

// TertiaryTargetPath 获取次级 TargetPath，传入原先生成的 Storage TargetPath
func (filter *ImageURLFilter) TertiaryTargetPath(name upload.TargetPath) upload.TargetPath {
	return upload.TargetPath(strings.TrimSuffix(name.String(), filepath.Ext(name.String())) + filter.Tertiary.Ext())
}
