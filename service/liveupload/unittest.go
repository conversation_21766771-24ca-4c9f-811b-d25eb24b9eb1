//go:build !release
// +build !release

package liveupload

import (
	"path/filepath"
	"runtime"
	"strings"

	"github.com/MiaoSiLa/missevan-go/service/upload"
)

// TestUploadConfig 上传测试配置
func TestUploadConfig() upload.Config {
	_, path, _, _ := runtime.Caller(0)
	path = strings.SplitN(path, "/live-service/", 2)[0]
	path = filepath.Join(path, "live-service/testdata/")
	return upload.Config{
		URL:  "https://fm.example.com/testdata/",
		Path: path,
	}
}
