package liveupload

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/upload"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestIsSchemeHTTP(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsSchemeHTTP("http"))
	assert.True(IsSchemeHTTP("https"))
	assert.False(IsSchemeHTTP("oss"))
}

func TestNewImageURLFilter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 正常存入 AVIF WebP PNG
	urls := []upload.SourceURL{"image01.avif", "image01.webp", "image01.png"}
	urlFilter, err := NewImageURLFilter(urls)
	comparedFilter := &ImageURLFilter{
		Primary:   urls[0],
		Secondary: urls[1],
		Tertiary:  urls[2],
	}
	require.NoError(err)
	assert.Equal(comparedFilter, urlFilter)

	ext := ".webp"
	targetPath := urlFilter.Primary.NewTargetPath("prefix", ext)
	assert.Equal(targetPath, urlFilter.SecondaryTargetPath(targetPath))

	ext = ".png"
	targetPath = urlFilter.Primary.NewTargetPath("prefix", ext)
	assert.Equal(targetPath, urlFilter.TertiaryTargetPath(targetPath))

	// 正常存入 PNG
	urls = []upload.SourceURL{"image01.png"}
	urlFilter, err = NewImageURLFilter(urls)
	require.NoError(err)
	assert.Equal(urls[0], urlFilter.Primary)
	assert.Empty(urlFilter.Secondary)

	// 正常存入 GIF
	urls = []upload.SourceURL{"image01.gif"}
	urlFilter, err = NewImageURLFilter(urls)
	require.NoError(err)
	assert.Equal(urls[0], urlFilter.Primary)

	// 错误数量的传入
	urls = []upload.SourceURL{}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrImageNum, err)
	assert.Nil(urlFilter)

	// 传入 WebP 时缺少 PNG
	urls = []upload.SourceURL{"image01.webp"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrWebPRequireImage, err)
	assert.Nil(urlFilter)

	// 传入了不支持的图片
	urls = []upload.SourceURL{"image01.jpeg"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrImageType, err)
	assert.Nil(urlFilter)

	// 重复上传
	urls = []upload.SourceURL{"image01.webp", "image01.webp"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrWebPImageType, err)
	assert.Nil(urlFilter)

	// 错误格式上传
	urls = []upload.SourceURL{"image01.jpeg", "image01.png"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrImageType, err)
	assert.Nil(urlFilter)

	// 测试 AVIF
	urls = []upload.SourceURL{"image01.avif", "image01.webp", "image01.png"}
	urlFilter, err = NewImageURLFilter(urls)
	require.NoError(err)
	require.NotNil(urlFilter)
	assert.EqualValues("image01.avif", urlFilter.Primary)
	assert.EqualValues("image01.webp", urlFilter.Secondary)
	assert.EqualValues("image01.png", urlFilter.Tertiary)

	urls = []upload.SourceURL{"image01.avif", "image01.webp"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrAVIFRequireImage, err)
	assert.Nil(urlFilter)

	urls = []upload.SourceURL{"image01.avif", "image01.webp", "image01.webp"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrAVIFImageType, err)
	assert.Nil(urlFilter)

	urls = []upload.SourceURL{"image01.webp", "image01.png", "image01.webp"}
	urlFilter, err = NewImageURLFilter(urls)
	require.Equal(ErrAVIFImageType, err)
	assert.Nil(urlFilter)
}
