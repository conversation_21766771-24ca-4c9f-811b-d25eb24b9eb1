package service

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInitTest(t *testing.T) {
	assert := assert.New(t)

	assert.NotPanics(func() { InitTest() })

	assert.NotNil(DB)
	assert.NotNil(LiveDB)
	assert.NotNil(NewADB)
	assert.NotNil(LogDB)

	assert.NotNil(MongoDB)

	assert.NotNil(Redis)
	assert.NotNil(IMRedis)
	assert.NotNil(LRURedis)

	assert.NotNil(DatabusPub)

	assert.NotNil(Storage)
	assert.NotNil(Vod)

	assert.NotNil(MRPC)
	assert.NotNil(PushService)
	assert.NotNil(SSO)

	assert.NotNil(NeteaseLive)
	assert.NotNil(AliyunLive)
	assert.NotNil(KsyunLive)

	assert.NotNil(Captcha)
	assert.NotNil(Upload)
}

func TestInitTestDatabusSub(t *testing.T) {
	assert := assert.New(t)

	InitTestDatabusSub()
	assert.NotNil(DatabusSub)
}
