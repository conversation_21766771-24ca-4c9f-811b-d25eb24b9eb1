package userapi

import (
	"errors"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// 黑名单相关 URI
const (
	URIAddBlocklist          = "app://live/add-blocklist" // 加入黑名单
	URIGoUserBlocklist       = "go://user/block-list"     // 获取用户拉黑的所有用户 ID
	URIGoUserBlockStatus     = "go://user/block-status"
	URIGoUserBlockStatusList = "go://user/block-status-list"
)

// BlockListType 拉黑列表类型
const (
	BlockListTypeBlockedByUser  = iota // 获取被用户拉黑的所有用户 ID
	BlockListTypeWhoBlockedUser        // 获取用户被哪些用户 ID 拉黑
)

// AddBlocklist 拉黑指定用户
// userID: 申请拉黑用户 ID
// blockUserID: 被拉黑用户 ID
func AddBlocklist(userID, blockUserID int64, clientIP string) error {
	err := service.MRPC.Call(URIAddBlocklist, clientIP, map[string]interface{}{
		"user_id":       userID,
		"block_user_id": blockUserID,
	}, nil)
	if err != nil {
		return err
	}
	return nil
}

// UserBlockList 获取用户拉黑的用户列表
func UserBlockList(uc mrpc.UserContext, userID int64, blockListType int) ([]int64, error) {
	resp := make(map[string][]int64)
	err := service.MRPC.Do(uc, URIGoUserBlocklist, map[string]any{
		"user_id": userID,
		"type":    blockListType,
	}, &resp)
	if err != nil {
		return nil, err
	}
	return resp["block_list"], nil
}

// UserBlockStatus 判断用户之间的拉黑关系
// u1BlockU2 表示 userID1 是否拉黑 userID2
// u2BlockU1 表示 userID2 是否拉黑 userID1
// TODO: 后续需要将查询结果缓存，减少 RPC 调用
func UserBlockStatus(userID1, userID2 int64) (u1BlockU2, u2BlockU1 bool, err error) {
	resp := make(map[string][]bool)
	err = service.MRPC.Call(URIGoUserBlockStatus, "", map[string][]int64{
		"user_ids": {userID1, userID2},
	}, &resp)
	if err != nil {
		return false, false, err
	}
	status, ok := resp["block_status"]
	if !ok || len(status) != 2 {
		return false, false, errors.New("rpc user/block-status response error")
	}

	return status[0], status[1], nil
}

// UserBlockStatusList 获取用户与被检查的用户集合之间的拉黑关系
func UserBlockStatusList(uc mrpc.UserContext, userID int64, checkUserIDs []int64) ([]int64, []int64, error) {
	resp := make(map[string][]int64)
	err := service.MRPC.Do(uc, URIGoUserBlockStatusList, map[string]any{
		"user_id":        userID,
		"check_user_ids": checkUserIDs,
	}, &resp)
	if err != nil {
		return nil, nil, err
	}
	return resp["block_user_list"], resp["user_block_list"], nil
}
