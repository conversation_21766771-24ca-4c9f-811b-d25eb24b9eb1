package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
)

func TestCheckTextIM(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{
			{
				Pass: false,
			},
			{
				Pass:   true,
				Labels: []string{scan.LabelEvil},
			},
		}, nil
	})
	defer cleanup()
	result, err := CheckTextIM(mrpc.UserContext{}, gaia.ParamLiveIM{})
	require.NoError(err)
	require.NotNil(result)
	assert.True(result.NotPass)
	assert.True(result.HasLabelEvil)
	assert.Len(result.Checks, 2)

	mrpc.SetMock(URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{
			{
				Pass:   true,
				Labels: []string{scan.LabelEvil},
			},
		}, nil
	})
	result, err = CheckTextIM(mrpc.UserContext{}, gaia.ParamLiveIM{})
	require.NoError(err)
	require.NotNil(result)
	assert.False(result.NotPass)
	assert.True(result.HasLabelEvil)
	assert.Len(result.Checks, 1)

	mrpc.SetMock(URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{}, nil
	})
	result, err = CheckTextIM(mrpc.UserContext{}, gaia.ParamLiveIM{})
	require.NoError(err)
	require.NotNil(result)
	assert.False(result.NotPass)
	assert.False(result.HasLabelEvil)
	assert.Empty(result.Checks)
}
