package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// URLGetUserConfig 获取用户配置信息
const URLGetUserConfig = "mrpc://missevan-main/user/get-user-config"

// 配置项状态：禁用/启用
const (
	ConfigDisable = 0 // 关闭
	ConfigEnable  = 1 // 开启
)

type GetUserConfigParam struct {
	UserID int64  `json:"user_id"` // 用户ID
	BUVID  string `json:"buvid"`   // 设备号
}

type GetUserConfigResp struct {
	Config *MUserConfig `json:"config"` // 用户配置信息
}

// MUserConfig 用户配置信息
type MUserConfig struct {
	UserID    int64     `json:"user_id"`
	Buvid     string    `json:"buvid"`
	AppConfig AppConfig `json:"app_config"`
}

// AppConfig APP 配置
type AppConfig struct {
	PersonalizedRecommend *int                     `json:"personalized_recommend,omitempty"`
	ShowSubscribeDrama    *int                     `json:"show_subscribe_drama,omitempty"`
	ShowUserCollect       *int                     `json:"show_user_collect,omitempty"`
	MessageNotification   *MessageNotificationConf `json:"message_notification,omitempty"`
}

// MessageNotificationConf 消息通知配置
type MessageNotificationConf struct {
	AtMe              *int `json:"at_me,omitempty"`
	Like              *int `json:"like,omitempty"`
	Comment           *int `json:"comment,omitempty"`
	PrivateMessage    *int `json:"private_message,omitempty"`
	Live              *int `json:"live,omitempty"`
	InterestRecommend *int `json:"interest_recommend,omitempty"`
}

// GetUserConfig 获取用户配置信息
func GetUserConfig(uc mrpc.UserContext, userID int64, buvid string) (*MUserConfig, error) {
	var resp GetUserConfigResp
	err := service.MRPC.Do(uc, URLGetUserConfig, GetUserConfigParam{UserID: userID, BUVID: buvid}, &resp)
	if err != nil {
		return nil, err
	}
	return resp.Config, nil
}
