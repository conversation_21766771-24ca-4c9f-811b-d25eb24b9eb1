package userapi

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 送礼 uri
const (
	URISendGift         = "app://live/buy-gift"
	URISendRebateGift   = "app://live/send-rebate-gift"
	URISendRebateGifts  = "app://live/send-rebate-gifts"
	URISendGashaponGift = "app://live/buy-gashapon"
	URISendLuckyGift    = "app://live/buy-lucky-gift"
	URILiveRefundGoods  = "app://live/refund-goods"
)

const (
	// URIBuyFukubukuro 购买福袋接口
	// apidoc: https://ci.maoer.co/apidoc/missevan-app/#api-live-buy_fukubukuro
	URIBuyFukubukuro = "app://live/buy-fukubukuro"
	// URIBuyWishGoods 购买许愿池商品接口
	// apidoc: https://ci.maoer.co/apidoc/missevan-app/#api-live-buy_wish_goods
	URIBuyWishGoods = "app://live/buy-wish-goods"

	// URIBuyGoods 购买商品接口
	// apidoc: https://ci.maoer.co/apidoc/missevan-app/#api-live-buy_goods
	URIBuyGoods = "app://live/buy-goods"
)

const (
	// GoodsTypeRedPacket 礼物红包
	GoodsTypeRedPacket = iota + 1
	// GoodsTypeWish 许愿池
	GoodsTypeWish
	// GoodsTypeDanmaku 付费弹幕
	GoodsTypeDanmaku
	// GoodsTypeLuckyBag 福袋
	GoodsTypeLuckyBag
	// GoodsTypeLuckyBox 宝盒
	GoodsTypeLuckyBox
)

// Gift 礼物信息
type Gift struct {
	ID      int64  `json:"id"`
	Title   string `json:"title"`
	Price   int64  `json:"price"`             // 礼物单价
	Num     int64  `json:"num"`               // 礼物数量
	Context string `json:"context,omitempty"` // 仅对白给礼物有效
}

// Goods 商品信息
type Goods struct {
	ID         int64  `json:"id"`
	Title      string `json:"title"`
	TotalPrice int    `json:"total_price"` // 商品总价
	Num        int    `json:"num"`         // 商品数量
}

type sendGashaponReqParam struct {
	FromID int64  `json:"from_id"`
	ToID   int64  `json:"to_id"`
	Noble  int    `json:"noble"` // 是否可以使用贵族钻石
	Goods  Goods  `json:"goods"` // 用户购买的盲盒，Num 传抽奖次数
	Gifts  []Gift `json:"gifts"` // 抽到的礼物

	LiveOpenLogID string `json:"live_open_log_id,omitempty"`

	UserAgent string `json:"user_agent"`
	EquipID   string `json:"equip_id"`
	BUVID     string `json:"buvid"`
}

func (param *sendGashaponReqParam) SetOpenLogID(openLogID *primitive.ObjectID) {
	if openLogID != nil && !openLogID.IsZero() {
		param.LiveOpenLogID = openLogID.Hex()
	}
}

// SendGashaponGift 送扭蛋礼物
// 返回的 tid 是用户购买记录的 tid
func SendGashaponGift(fromUserID, toUserID int64,
	goods Goods, gifts []Gift,
	isNoble bool, openLogID *primitive.ObjectID, c mrpc.UserContext) (*BalanceResp, error) {
	param := sendGashaponReqParam{
		FromID:    fromUserID,
		ToID:      toUserID,
		Noble:     util.BoolToInt(isNoble),
		Goods:     goods,
		Gifts:     gifts,
		UserAgent: c.UserAgent,
		EquipID:   c.EquipID,
		BUVID:     c.BUVID,
	}
	param.SetOpenLogID(openLogID)
	resp := new(BalanceResp)
	err := service.MRPC.Do(c, URISendGashaponGift, param, resp)
	return resp, err
}

const (
	// LuckyGiftFromDraw 抽取随机礼物
	LuckyGiftFromDraw = iota
	// LuckyGiftFromDrop 定时投放随机礼物
	LuckyGiftFromDrop
	// LuckyGiftFromGuaranteed 抽取保底随机礼物
	LuckyGiftFromGuaranteed
)

type sendLuckyGiftReqParam struct {
	FromID int64  `json:"from_id"`
	ToID   int64  `json:"to_id"`
	GiftID int64  `json:"gift_id"` // 用户送出的礼物 ID
	Price  int64  `json:"price"`   // 礼物单价
	Num    int    `json:"num"`
	Title  string `json:"title"`  // 组合后的名称，模板：${送出礼物名}（${收到礼物名}）
	Income int64  `json:"income"` // 主播收入，单位：分
	Noble  int    `json:"noble"`  // 是否可以使用贵族钻石

	LiveOpenLogID string `json:"live_open_log_id,omitempty"`

	UserAgent string `json:"user_agent"`
	EquipID   string `json:"equip_id"`
	BUVID     string `json:"buvid"`

	More *sendLuckyGiftMore `json:"more,omitempty"` // 额外信息
}

type sendLuckyGiftMore struct {
	From          int   `json:"from,omitempty"`            // 礼物来源 0: 随机抽取，1: 随机投放，2: 随机抽取命中保底
	ReceiveGiftID int64 `json:"receive_gift_id,omitempty"` // 随机礼物抽到的礼物 ID
}

func (param *sendLuckyGiftReqParam) SetOpenLogID(openLogID *primitive.ObjectID) {
	if openLogID != nil && !openLogID.IsZero() {
		param.LiveOpenLogID = openLogID.Hex()
	}
}

// LuckyGiftTitle 生成随机礼物的 title
func LuckyGiftTitle(drawSendName, drawReceiveName string) string {
	return fmt.Sprintf("%s（%s）", drawReceiveName, drawSendName)
}

// SendLuckyGift 送出随机礼物
// NOTICE: drawSendGift 是用户送出的随机礼物，drawReceiveGift 是用户抽到的奖池礼物。price (单位：钻) 表示对应礼物的单价
func SendLuckyGift(fromUserID, toUserID int64, drawSendGift *Gift, drawReceiveGift *Gift,
	isNoble bool, openLogID *primitive.ObjectID, from int, c UserContext) (*BalanceResp, error) {
	param := sendLuckyGiftReqParam{
		FromID:    fromUserID,
		ToID:      toUserID,
		GiftID:    drawSendGift.ID,
		Price:     drawSendGift.Price,    // 送出的随机礼物的单价
		Num:       int(drawSendGift.Num), // 送出的随机礼物的数量
		Title:     LuckyGiftTitle(drawSendGift.Title, drawReceiveGift.Title),
		Income:    drawReceiveGift.Price * 10, // 主播收入（单位：分），由用户抽到的礼物的单价确定
		Noble:     util.BoolToInt(isNoble),
		UserAgent: c.UserAgent,
		EquipID:   c.EquipID,
		BUVID:     c.BUVID,
		More: &sendLuckyGiftMore{
			ReceiveGiftID: drawReceiveGift.ID,
		},
	}
	// 默认抽取时不记录 from，和历史请求保持一致
	if from != LuckyGiftFromDraw {
		param.More.From = from
	}
	param.SetOpenLogID(openLogID)
	resp := new(BalanceResp)
	err := service.MRPC.Call(URISendLuckyGift, c.ClientIP, param, resp,
		c.Cookies())
	return resp, err
}

// SendRebateGift 送出白给礼物
func SendRebateGift(fromID, toID, giftID int64,
	giftNum int, openLogID *primitive.ObjectID, c UserContext) (*BalanceResp, error) {
	req := map[string]interface{}{
		"from_id":    fromID,
		"to_id":      toID,
		"gift_id":    giftID,
		"num":        giftNum,
		"user_agent": c.UserAgent,
		"equip_id":   c.EquipID,
		"buvid":      c.BUVID,
	}
	if openLogID != nil && !openLogID.IsZero() {
		req["live_open_log_id"] = openLogID.Hex()
	}
	resp := new(BalanceResp)
	err := service.MRPC.Call(URISendRebateGift, c.ClientIP, req, resp, c.Cookies())
	return resp, err
}

type buyFukubukuroParam struct {
	FromID int64  `json:"from_id"`
	GiftID int64  `json:"gift_id"`
	Price  int    `json:"price"`
	Title  string `json:"title"`
	Noble  int    `json:"noble"`

	UserAgent string `json:"user_agent"`
	EquipID   string `json:"equip_id"`
	BUVID     string `json:"buvid"`
}

// BuyFukubukuro 购买福袋
func BuyFukubukuro(fromID, giftID int64, title string, price, noble int, c UserContext) (*BalanceResp, error) {
	param := buyFukubukuroParam{
		FromID:    fromID,
		GiftID:    giftID,
		Price:     price,
		Title:     title,
		Noble:     noble,
		UserAgent: c.UserAgent,
		EquipID:   c.EquipID,
		BUVID:     c.BUVID,
	}

	resp := new(BalanceResp)
	err := service.MRPC.Call(URIBuyFukubukuro, "", param, &resp)
	return resp, err
}

type sendRebateGiftsParam struct {
	FromID int64  `json:"from_id"`
	ToID   int64  `json:"to_id"`
	Gifts  []Gift `json:"gifts"`

	LiveOpenLogID string `json:"live_open_log_id,omitempty"`

	UserAgent string `json:"user_agent"`
	EquipID   string `json:"equip_id"`
	BUVID     string `json:"buvid"`
	IP        string `json:"ip"`
}

func (param *sendRebateGiftsParam) SetOpenLogID(openLogID *primitive.ObjectID) {
	if openLogID != nil && !openLogID.IsZero() {
		param.LiveOpenLogID = openLogID.Hex()
	}
}

// SendRebateGifts 送出多组白给礼物
func SendRebateGifts(fromID, toID int64, gifts []Gift,
	openLogID *primitive.ObjectID, c UserContext) (*BalanceResp, error) {
	param := sendRebateGiftsParam{
		FromID:    fromID,
		ToID:      toID,
		Gifts:     gifts,
		UserAgent: c.UserAgent,
		EquipID:   c.EquipID,
		BUVID:     c.BUVID,
		IP:        c.ClientIP,
	}
	param.SetOpenLogID(openLogID)
	resp := new(BalanceResp)
	err := service.MRPC.Call(URISendRebateGifts, c.ClientIP, param, &resp, c.Cookies())
	return resp, err
}

type sendGiftReqParam struct {
	FromID int64 `json:"from_id"`
	ToID   int64 `json:"to_id"`
	GiftID int64 `json:"gift_id"` // 用户送出的礼物 ID
	Num    int64 `json:"num"`
	Noble  int   `json:"noble"` // 是否可以使用贵族钻石

	LiveOpenLogID string `json:"live_open_log_id,omitempty"` // 房间开播日志 id

	UserAgent string `json:"user_agent"`
	EquipID   string `json:"equip_id"`
	BUVID     string `json:"buvid"`
}

func (param *sendGiftReqParam) SetOpenLogID(openLogID *primitive.ObjectID) {
	if openLogID != nil && !openLogID.IsZero() {
		param.LiveOpenLogID = openLogID.Hex()
	}
}

// SendGift 送礼
// TODO: UserContext 改用 mrpc.UserContext
func SendGift(fromUserID, toUserID int64, gift Gift,
	isNoble bool, openLogID *primitive.ObjectID, c UserContext) (*BalanceResp, error) {
	param := sendGiftReqParam{
		FromID:    fromUserID,
		ToID:      toUserID,
		GiftID:    gift.ID,
		Num:       gift.Num,
		Noble:     goutil.BoolToInt(isNoble),
		UserAgent: c.UserAgent,
		EquipID:   c.EquipID,
		BUVID:     c.BUVID,
	}
	param.SetOpenLogID(openLogID)
	var resp BalanceResp
	err := service.MRPC.Call(URISendGift, c.ClientIP, param, &resp, c.Cookies())
	return &resp, err
}

// SendRewardGift rpc 和定时任务发送礼物奖励, 没有 user context
func SendRewardGift(fromUserID, toUserID, giftID int64, num int, openLogID *primitive.ObjectID) (*BalanceResp, error) {
	return SendGift(fromUserID, toUserID, Gift{ID: giftID, Num: int64(num)}, false, openLogID, UserContext{})
}

// BuyWishGoodsParam buy wish goods param
type BuyWishGoodsParam struct {
	FromID int64  `json:"from_id"`
	GiftID int64  `json:"gift_id"`
	Price  int    `json:"price"` // 总价
	Title  string `json:"title"`
	Num    int    `json:"num"`
	Noble  int    `json:"noble"`

	UserAgent string `json:"user_agent"`
	EquipID   string `json:"equip_id"`
	BUVID     string `json:"buvid"`
}

// BuyWishGoods 购买许愿池商品
// 方法参数中的 price 为单价
func BuyWishGoods(fromID, giftID int64, title string, price, num, noble int, c UserContext) (*BalanceResp, error) {
	param := BuyWishGoodsParam{
		FromID: fromID,
		GiftID: giftID,
		Price:  price * num, // 总价
		Title:  title,
		Num:    num,
		Noble:  noble,

		UserAgent: c.UserAgent,
		EquipID:   c.EquipID,
		BUVID:     c.BUVID,
	}

	resp := new(BalanceResp)
	err := service.MRPC.Call(URIBuyWishGoods, "", param, &resp)
	return resp, err
}

// BuyLiveGoodsParam 购买直播商品参数
type BuyLiveGoodsParam struct {
	BuyerID     int64           `json:"buyer_id"`               // 购买者 ID
	ReceiverID  int64           `json:"receiver_id"`            // 收礼者 ID
	GoodsType   int             `json:"goods_type"`             // 商品类型（1: 礼物红包; 2: 许愿池）
	Goods       []LiveGoodsElem `json:"goods"`                  // 购买的商品信息
	PackageInfo *PackageInfo    `json:"package_info,omitempty"` // 商品组合信息
	Noble       int             `json:"noble"`                  // 是否可以使用贵族钻石 (0: 否; 1: 是)
	UserAgent   string          `json:"user_agent"`             // 用户代理
	EquipID     string          `json:"equip_id"`
	BUVID       string          `json:"buvid"`
	IP          string          `json:"ip"`

	// 以下全部字段业务侧调用 BuyLiveGoods() 时不用赋值，会在 BuyLiveGoods 函数内部统一处理
	LiveOpenLogID string `json:"live_open_log_id,omitempty"` // 房间开播日志 ID
}

// LiveGoodsElem 购买的直播商品信息
type LiveGoodsElem struct {
	ID              int64  `json:"id"`                         // 商品 ID
	Title           string `json:"title"`                      // 商品标题
	Price           int    `json:"price"`                      // 商品单价 (单位: 钻)
	Num             int    `json:"num"`                        // 商品数量
	TransactionType int    `json:"transaction_type,omitempty"` // 商品类型 (1: 直播礼物; 2: 剧集)
}

// PackageInfo 商品组合信息
type PackageInfo struct {
	ID    int64  `json:"id"`    // 商品组合 ID
	Title string `json:"title"` // 商品组合标题
	Price int    `json:"price"` // 商品组合价格 (单位: 钻)
	Num   int    `json:"num"`   // 数量
}

// BuyLiveGoods 购买直播商品
func BuyLiveGoods(c mrpc.UserContext, param BuyLiveGoodsParam, openLogID *primitive.ObjectID) (*BalanceResp, error) {
	param.setOpenLogID(openLogID)
	resp := new(BalanceResp)
	err := service.MRPC.Do(c, URIBuyGoods, param, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (param *BuyLiveGoodsParam) setOpenLogID(openLogID *primitive.ObjectID) {
	if openLogID != nil && !openLogID.IsZero() {
		param.LiveOpenLogID = openLogID.Hex()
	}
}

// RefundGoodsParam 退款的直播商品信息
type RefundGoodsParam struct {
	TransactionID int64              `json:"transaction_id"`
	GoodsType     int                `json:"goods_type"`
	Goods         []*RefundGoodsElem `json:"goods"`
}

// TransactionType 交易类型
const (
	TransactionTypeGift  = iota + 1 // 直播礼物
	TransactionTypeDrama            // 剧集
)

// RefundGoodsElem 退款的直播商品信息
type RefundGoodsElem struct {
	ID              int64  `json:"id"`               // 商品 ID
	TransactionType int    `json:"transaction_type"` // 交易类型
	Title           string `json:"title"`            // 商品标题
	Price           int64  `json:"price"`            // 商品单价，单位：钻
	Num             int    `json:"num"`              // 商品数量
}

// RefundGoods 商品退款
func RefundGoods(c mrpc.UserContext, param RefundGoodsParam) (*BalanceResp, error) {
	resp := new(BalanceResp)
	err := service.MRPC.Do(c, URILiveRefundGoods, param, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
