package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUpdateUserPoint(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(9074509)
	testPoint := 10

	var expected *updateUserPointParam
	cancel := mrpc.SetMock(URIUpdateUserPoint, func(input interface{}) (interface{}, error) {
		assert.Equal(expected, input)
		return "success", nil
	})
	defer cancel()

	var c mrpc.UserContext
	expected = &updateUserPointParam{
		UserID: testUserID,
		Point:  testPoint,
		Type:   UserPointTypeLiveMedal,
		Origin: UserPointOriginWeb,
	}
	err := UpdateUserPoint(c, testUserID, testPoint, UserPointTypeLiveMedal, goutil.Web, nil)
	require.NoError(err)

	expected.Origin = UserPointOriginApp
	err = UpdateUserPoint(c, testUserID, testPoint, UserPointTypeLiveMedal, goutil.Android, nil)
	require.NoError(err)
	err = UpdateUserPoint(c, testUserID, testPoint, UserPointTypeLiveMedal, goutil.IOS, nil)
	require.NoError(err)

	expected.Origin = UserPointOriginMobileWeb
	err = UpdateUserPoint(c, testUserID, testPoint, UserPointTypeLiveMedal, goutil.MobileWeb, nil)
	require.NoError(err)
}
