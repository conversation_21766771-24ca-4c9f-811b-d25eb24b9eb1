package userapi

import (
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// urls
const (
	// APIGuildTransferRevenue .
	APIGuildTransferRevenue = "mrpc://missevan-pay/guild/transfer-revenue"
)

// GuildTransferRevenue 主播转会流程：转移指定时间内主播的流水到新的公会
func GuildTransferRevenue(c mrpc.UserContext, fromGuildID, toGuildID int64, creatorIDs []int64, startTime, endTime time.Time) error {
	var resp struct {
		Status bool `json:"status"`
	}

	err := service.MRPC.Do(c, APIGuildTransferRevenue, map[string]interface{}{
		"from_guild_id": fromGuildID,
		"creator_ids":   creatorIDs,
		"start_time":    startTime.Unix(),
		"end_time":      endTime.Unix(),
		"to_guild_id":   toGuildID,
	}, &resp)
	if err != nil {
		return err
	}

	if !resp.Status {
		logger.Warnf("转移公会主播流水无变化，公会 %d 向 公会 %d 转出以下主播：%v，时间范围 %s <= confirm_time < %s",
			fromGuildID, toGuildID, creatorIDs,
			startTime.Format(goutil.TimeFormatHMS), endTime.Format(goutil.TimeFormatHMS))
		// PASS
	}
	return nil
}
