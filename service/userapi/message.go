package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 发送消息相关 RPC 接口
var (
	// URLScanIM 检查文本
	URLScanIM = "go://scan/im"
)

// CheckTextIMResult 文本检查返回结果
type CheckTextIMResult struct {
	NotPass      bool
	HasLabelEvil bool
	Checks       []*scan.BaseCheckResult // 接口返回的检查结果
}

// CheckTextIM 检查文本
func CheckTextIM(ctx mrpc.UserContext, param gaia.ParamLiveIM) (*CheckTextIMResult, error) {
	var resp []*scan.BaseCheckResult
	err := service.MRPC.Do(ctx, URLScanIM, param, &resp)
	if err != nil {
		return nil, err
	}
	result := &CheckTextIMResult{
		Checks: resp,
	}
	for _, r := range resp {
		if !r.Pass {
			result.NotPass = true
		}
		if goutil.HasElem(r.Labels, scan.LabelEvil) {
			result.HasLabelEvil = true
		}
	}
	return result, nil
}
