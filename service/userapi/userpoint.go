package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// URI
const (
	URIUpdateUserPoint = "go://user/updatepoint"
)

// 小鱼干日志分类
const (
	UserPointTypeLiveMedal = 12
)

// 小鱼干操作来源
const (
	UserPointOriginWeb = iota
	UserPointOriginMobileWeb
	UserPointOriginApp
)

type updateUserPointParam struct {
	UserID int64       `json:"user_id"`
	Point  int         `json:"point"`
	Type   int         `json:"type"`
	Origin int         `json:"origin"`
	More   interface{} `json:"more,omitempty"`
}

// UpdateUserPoint 更新小鱼干
func UpdateUserPoint(uc mrpc.UserContext, userID int64, point, userPointType int, os goutil.Platform, more interface{}) error {
	param := &updateUserPointParam{
		UserID: userID,
		Point:  point,
		Type:   userPointType,
		More:   more,
	}
	switch os {
	case goutil.Android, goutil.IOS:
		param.Origin = UserPointOriginApp
	case goutil.MobileWeb:
		param.Origin = UserPointOriginMobileWeb
	default:
		param.Origin = UserPointOriginWeb
	}
	return service.MRPC.Do(uc, URIUpdateUserPoint, param, nil)
}
