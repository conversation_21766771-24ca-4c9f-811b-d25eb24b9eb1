package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

func TestSystemMsgBox_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var called bool
	var expectedSystemMsgs []pushservice.SystemMsg
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(body interface{}) (interface{}, error) {
		msgs, ok := body.(map[string]interface{})["systemmsgs"]
		require.True(ok)
		m, ok := msgs.([]pushservice.SystemMsg)
		require.True(ok)
		assert.Equal(expectedSystemMsgs, m)

		called = true
		return "success", nil
	})
	defer cancel()

	expectedSystemMsgs = []pushservice.SystemMsg{
		{UserID: 123, Title: "123", Content: "123"},
		{UserID: 456, Title: "456", Content: "456&gt;"},
	}
	box := NewSystemMsgBox()
	box.AddMessage(123, "123", "123")
	box.AddMessage(456, "456", "456>")
	require.NoError(box.Send())
	assert.True(called)

	expectedSystemMsgs = []pushservice.SystemMsg{
		{UserID: 123, Title: "123", Content: "123>", SendTime: 1710864000},
	}
	box = NewSystemMsgBox(1710864000)
	box.AddMessage(123, "123", "123>")
	err := box.Send(&pushservice.SystemMsgOptions{
		DisableHTMLEscape: true,
	})
	require.NoError(err)
	assert.True(called)
}
