package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

const (
	// URLMinigameExchange 活动奖品兑换
	URLMinigameExchange = "minigame://activity/exchange"
)

// SendActivityExchange .
func SendActivityExchange(ctx mrpc.UserContext, userID, eventID int64) error {
	return service.MRPC.Do(ctx, URLMinigameExchange, map[string]any{
		"user_id":  userID,
		"event_id": eventID,
	}, nil)
}
