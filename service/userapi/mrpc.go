package userapi

import (
	"context"
	"encoding/json"
	"runtime"
	"time"

	jsoniter "github.com/json-iterator/go"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/sync/errgroup"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/shorturl"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 按照实际值字母表顺序排列（大概）
const (
	URIAsk                       = "app://live/ask"
	URIConfirmAsk                = "app://live/confirm-ask"
	URICancelAsks                = "app://live/cancel-asks"
	uriAddListenHistory          = "app://live/add-listen-history"
	URICreateLivePenaltyOrder    = "app://financial/create-live-penalty-order"
	URIDramaBought               = "app://drama/drama-bought"                 // 用户是否已购买（已拥有）该剧集
	URIAddDramaTransactionRecord = "app://drama/add-drama-transaction-record" // 添加剧集券 0 元消费记录（兑换剧集）

	uriGoAddAdminlog                 = "go://util/addadminlog"
	URLGoShortURL                    = "go://util/shorturl" // 获取短链接
	URLGoDiscoverySearch             = "go://discovery/search"
	URLGOHistoryClearLivePlayHistory = "go://history/clear-live-play-history" // 清空用户的直播观看记录
	URLGOHistoryDelLivePlayHistory   = "go://history/del-live-play-history"   // 删除用户在特定直播间的观看记录
	URLGODramaCheckDramaRefined      = "go://drama/check-drama-refined"       // 判断剧集属性
	URLGoAuthRoles                   = "go://util/auth-roles"                 // 获取用户后台角色
	URLGoAuthUserIDs                 = "go://util/auth-user-ids"              // 通过角色获取用户 ID
	URLGoScanRisk                    = "go://scan/risk"                       // 风险识别检测
	URLGoIsOfficeIP                  = "go://util/is-office-ip"               // 判断是否是办公室 IP

	URIIMBroadcastUser        = "im://broadcast/user"
	URIIMBroadcast            = "im://broadcast"
	URIIMBroadcastMany        = "im://broadcast/many"
	URIIMActivityBroadcastAll = "im://activity/broadcast/all"
	URIIMNotifySet            = "im://notify/set"
	URIIMRoomList             = "im://room/list"
	URIIMRoomsList            = "im://room/batch-list"

	URILiveReward = "live://activity/reward"

	URLMainUserMarkPageViewed = "mrpc://missevan-main/user/mark-page-viewed"
	URLMainUserGetAuthInfo    = "mrpc://missevan-main/user/get-auth-info"

	URLMinigameDrawPointUpdate = "minigame://activity/drawpoint/update"
)

// 广播的优先级
const (
	BroadcastPriorityNormal    = iota // 常规消息
	BroadcastPriorityPurchased        // 消费消息
)

// UserContext 用户上下文
type UserContext struct {
	ClientIP  string
	Token     string
	UserAgent string
	EquipID   string
	BUVID     string
}

// NewUserContext new UserContext
func NewUserContext(c goutil.UserContext) UserContext {
	if c == nil { // 值为指针类型（如 *handler.Context）的空接口时判断会失效
		return UserContext{}
	}
	return UserContext{
		ClientIP:  c.ClientIP(),
		Token:     c.Token(),
		UserAgent: c.UserAgent(),
		EquipID:   c.EquipID(),
		BUVID:     c.BUVID(),
	}
}

// Cookies return cookies
func (c UserContext) Cookies() map[string]string {
	return map[string]string{
		"token":    c.Token,
		"equip_id": c.EquipID,
		"buvid":    c.BUVID,
	}
}

// BalanceResp 用户消费相关请求的响应
type BalanceResp struct {
	TransactionID    int64  `json:"transaction_id"`
	Balance          int64  `json:"balance,omitempty"`            // 消费返利礼物不会返回
	LiveNobleBalance int64  `json:"live_noble_balance,omitempty"` // 消费返利礼物不会返回
	Price            int64  `json:"price"`
	Context          string `json:"context,omitempty"`
}

// BuyBalance 用户消费钻石数据
type BuyBalance struct {
	Balance                int64 `json:"balance"`
	LiveNobleBalance       int64 `json:"live_noble_balance"`
	LiveNobleBalanceStatus int   `json:"live_noble_balance_status"`
}

// NewBuyBalance new BuyBalance
func NewBuyBalance(balanceResp *BalanceResp, isNoble bool) BuyBalance {
	return BuyBalance{
		Balance:                balanceResp.Balance,
		LiveNobleBalance:       balanceResp.LiveNobleBalance,
		LiveNobleBalanceStatus: util.BoolToInt(isNoble),
	}
}

// BuySuperFan 购买超粉
func BuySuperFan(fromID, toID, goodsID int64, goodsTotalPrice int, num int,
	creatorUsername string, renew bool, openLogID *primitive.ObjectID, c mrpc.UserContext,
) (*BalanceResp, error) {
	renewParam := 0
	if renew {
		renewParam = 1
	}
	req := map[string]interface{}{
		"from_id":    fromID,
		"to_id":      toID,
		"gift_id":    goodsID,
		"price":      goodsTotalPrice,
		"num":        num,
		"title":      creatorUsername,
		"renew":      renewParam,
		"user_agent": c.UserAgent,
		"equip_id":   c.EquipID,
		"buvid":      c.BUVID,
	}
	if openLogID != nil && !openLogID.IsZero() {
		req["live_open_log_id"] = openLogID.Hex()
	}
	resp := new(BalanceResp)
	err := service.MRPC.Do(c, "app://live/buy-super-fan", req, &resp)
	return resp, err
}

// OnlineResp 在线人数
type OnlineResp struct {
	RoomID int64 `json:"room_id"`
	Count  int64 `json:"count"`
}

// Online 在线人数
func Online(ctx mrpc.UserContext, roomID int64) (*OnlineResp, error) {
	var resp OnlineResp
	err := service.MRPC.Do(ctx, "im://online/count", map[string]any{"room_id": roomID}, &resp)
	if err != nil {
		return nil, err
	}

	return &resp, nil
}

var json2 = jsoniter.Config{
	EscapeHTML:  false,
	SortMapKeys: true,
}.Froze()

// BroadcastOption 消息配置
type BroadcastOption struct {
	Priority int
}

// Broadcast rpc
func Broadcast(roomID int64, payload interface{}, options ...*BroadcastOption) error {
	input := map[string]interface{}{
		"room_id": roomID,
		"payload": payload,
	}
	if len(options) > 0 {
		input["priority"] = options[0].Priority
	}
	value, err := json2.Marshal(input)
	if err != nil {
		return err
	}
	return service.MRPC.Call(URIIMBroadcast, "", json.RawMessage(value), nil)
}

// BroadcastUser 用户通知
func BroadcastUser(roomID, userID, payload interface{}) error {
	input := map[string]interface{}{
		"room_id": roomID,
		"user_id": userID,
		"payload": payload,
	}
	value, err := json2.Marshal(input)
	if err != nil {
		return err
	}
	return service.MRPC.Call(URIIMBroadcastUser, "", json.RawMessage(value), nil)
}

// BroadcastAll 全站通知
// roomID 为 0 则认为不需要传房间号
func BroadcastAll(payload interface{}) error {
	input := map[string]interface{}{"payload": payload}
	value, err := json2.Marshal(input)
	if err != nil {
		return err
	}
	return service.MRPC.Call("im://broadcast/all", "", json.RawMessage(value), nil)
}

// BroadcastElem 广播消息单条消息
type BroadcastElem struct {
	Type     int         `json:"type"` // 同 liveim.IMMessageType
	RoomID   int64       `json:"room_id,omitempty"`
	UserID   int64       `json:"user_id,omitempty"`
	Priority int         `json:"priority,omitempty"`
	Payload  interface{} `json:"payload"`
}

const broadcastMaxSize = 20

// BroadcastMany 广播多条消息
func BroadcastMany(messages []*BroadcastElem) error {
	length := len(messages)
	s := make([][]*BroadcastElem, 0, length/20+1)
	for i := 0; i < length; i += broadcastMaxSize {
		end := i + broadcastMaxSize
		if end > length {
			end = length
		}
		arr := messages[i:end]
		s = append(s, arr)
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	// TODO: 工具函数需要支持 errgroup
	g, _ := errgroup.WithContext(ctx)
	for i := range s {
		v := s[i]
		g.Go(func() error {
			defer func() {
				if p := recover(); p != nil {
					// From net/http/server.go
					// https://github.com/golang/go/blob/release-branch.go1.13/src/net/http/server.go#L1765
					const size = 64 << 10
					buf := make([]byte, size)
					// if the size of stack tracing message is larger than 64k, the message will be truncated.
					buf = buf[:runtime.Stack(buf, false)]
					logger.Errorf("Panic recovered, %v\n%s", p, buf)
				}
			}()
			value, err := json2.Marshal(v)
			if err != nil {
				return err
			}
			return service.MRPC.Call(URIIMBroadcastMany, "", json.RawMessage(value), nil)
		})
	}
	return g.Wait()
}

// BroadcastBox 批量广播
type BroadcastBox struct {
	List []*BroadcastElem
}

// NewBroadcastBox 初始化批量广播消息，可指定消息长度
func NewBroadcastBox(length int) *BroadcastBox {
	return &BroadcastBox{List: make([]*BroadcastElem, 0, length)}
}

// Add 添加要广播的消息
func (b *BroadcastBox) Add(elems ...*BroadcastElem) *BroadcastBox {
	b.List = append(b.List, elems...)
	return b
}

// Send 发送广播消息，发送后清空 list，与 NewBroadcastBox 的 list 不是同一个 list
func (b *BroadcastBox) Send() error {
	if len(b.List) == 0 {
		return nil
	}
	defer func() { b.List = []*BroadcastElem{} }()
	return BroadcastMany(b.List)
}

// BroadcastActivityAll 活动全站广播
func BroadcastActivityAll(payload interface{}) error {
	return service.MRPC.Call(URIIMActivityBroadcastAll, "",
		map[string]interface{}{"payload": payload}, nil)
}

// IMRoomListRequest 查询聊天室在线用户列表请求参数
type IMRoomListRequest struct {
	RoomID        int64 `json:"room_id"`
	Users         bool  `json:"users"`
	Conns         bool  `json:"conns"`
	ValuableUsers bool  `json:"valuable_users"`
}

// IMRoomListResp 查询聊天室在线用户列表响应
type IMRoomListResp struct {
	UserIDs         []int64  `json:"user_ids"`
	Conns           []string `json:"conns"`
	ValuableUserIDs []int64  `json:"valuable_user_ids"`

	Users []int64 `json:"users"` // WORKAROUND: 待删除
}

// IMRoomList 查询聊天室在线用户列表
func IMRoomList(req IMRoomListRequest, c UserContext) (*IMRoomListResp, error) {
	var resp IMRoomListResp
	err := service.MRPC.Call(URIIMRoomList, c.ClientIP, req,
		&resp, c.Cookies())
	if err != nil {
		return nil, err
	}
	// WORKAROUND: 待删除
	if len(resp.UserIDs) == 0 && len(resp.Users) != 0 {
		resp.UserIDs = resp.Users
	}
	return &resp, nil
}

// IMRoomsListRequest 批量查询多个聊天室在线用户列表请求参数
type IMRoomsListRequest struct {
	RoomIDs []int64 `json:"room_ids"`
	Users   bool    `json:"users"`
}

// IMRoomsListResp 批量查询多个聊天室在线用户列表响应
type IMRoomsListResp struct {
	Rooms map[int64]IMRoomInfo `json:"rooms"`
}

// IMRoomInfo 聊天室在线用户信息
type IMRoomInfo struct {
	UserIDs []int64 `json:"user_ids"`
}

// IMRoomsList 批量查询多个聊天室在线用户列表
func IMRoomsList(uc mrpc.UserContext, req *IMRoomsListRequest) (*IMRoomsListResp, error) {
	resp := new(IMRoomsListResp)
	err := service.MRPC.Do(uc, URIIMRoomsList, req, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// IMNotifySet im notify set rpc
func IMNotifySet(uc mrpc.UserContext, roomID int64, userIDs []int64) error {
	return service.MRPC.Do(uc, URIIMNotifySet, map[string]interface{}{
		"room_id":  roomID,
		"user_ids": userIDs,
	}, nil)
}

// Ask 提问
func Ask(fromID, toID int64, price int64, noble bool,
	openLogID *primitive.ObjectID, c mrpc.UserContext,
) (*BalanceResp, error) {
	req := map[string]interface{}{
		"from_id":    fromID,
		"to_id":      toID,
		"price":      price,
		"noble":      util.BoolToInt(noble),
		"user_agent": c.UserAgent,
		"equip_id":   c.EquipID,
		"buvid":      c.BUVID,
	}
	if openLogID != nil && !openLogID.IsZero() {
		req["live_open_log_id"] = openLogID.Hex()
	}
	resp := new(BalanceResp)
	err := service.MRPC.Do(c, URIAsk, req, resp)
	return resp, err
}

// ConfirmAsk 回答提问
func ConfirmAsk(transactionID, toID int64, sure bool, c mrpc.UserContext) (*BalanceResp, error) {
	req := map[string]interface{}{
		"transaction_id": transactionID,
		"to_id":          toID,
		"sure":           sure,
		"user_agent":     c.UserAgent,
		"equip_id":       c.EquipID,
		"buvid":          c.BUVID,
	}
	resp := new(BalanceResp)
	err := service.MRPC.Do(c, URIConfirmAsk, req, resp)
	return resp, err
}

// CancelAsksResp CancelAsks 响应
type CancelAsksResp struct {
	Transactions []CancelAsksTransaction `json:"transactions"`

	// 有返回但是没啥用
	// Balance int64 `json:"balance"` // to_id 的钻石数
	// Profit  int64 `json:"profit"`
}

// CancelAsksTransaction 取消回答响应
type CancelAsksTransaction struct {
	TransactionID int64  `json:"transaction_id"`
	Price         int64  `json:"price"`
	Error         string `json:"error"`
}

// CancelAsks 批量关闭直播间内提问
// https://apidoc.maoer.co/missevan-app/#api-live
func CancelAsks(toID int64, tIDs []int64, c UserContext) (*CancelAsksResp, error) {
	resp := new(CancelAsksResp)
	err := service.MRPC.Call(URICancelAsks, c.ClientIP,
		map[string]interface{}{
			"to_id":           toID,
			"transaction_ids": tIDs,
			"user_agent":      c.UserAgent,
			"equip_id":        c.EquipID,
			"buvid":           c.BUVID,
		},
		resp,
		c.Cookies())
	return resp, err
}

// UpdateDrawPoint 更新活动抽奖积分
func UpdateDrawPoint(eventID, userID, point int64) error {
	return service.MRPC.Call(URLMinigameDrawPointUpdate, "", map[string]int64{
		"event_id": eventID,
		"user_id":  userID,
		"point":    point,
	}, nil)
}

// SendLiveHistory 发送用户收听直播历史记录
func SendLiveHistory(userID, roomID int64, accessTime goutil.TimeUnixMilli, c UserContext) error {
	return service.MRPC.Call(uriAddListenHistory, c.ClientIP,
		map[string]interface{}{
			"user_id":     userID,
			"room_id":     roomID,
			"access_time": accessTime,
		},
		nil, nil)
}

// CreateLivePenaltyOrder 创建主播强制解约订单
func CreateLivePenaltyOrder(applicationID, price int64, clientIP string) (map[string]interface{}, error) {
	resp := make(map[string]interface{})
	err := service.MRPC.Call(URICreateLivePenaltyOrder, clientIP,
		map[string]interface{}{
			"applyment_id": applicationID,
			"price":        price,
		}, &resp)

	return resp, err
}

// DiscoverySearchParams 搜索内容的请求
type DiscoverySearchParams struct {
	SearchType       int    `json:"search_type"`
	Keyword          string `json:"keyword"`
	GuideWord        string `json:"guide_word"`
	SuggestRequestID string `json:"suggest_request_id"`
	Page             int64  `json:"page"`
	PageSize         int64  `json:"page_size"`
}

// DiscoverySearch 搜索内容
func DiscoverySearch(req *DiscoverySearchParams, clientIP string) (*search.Response, error) {
	res := new(search.Response)
	err := service.MRPC.Call(URLGoDiscoverySearch, clientIP, req, res)
	return res, err
}

// ClearLivePlayHistory 清空用户的直播间观看记录
// userID 申请删除的用户 ID
func ClearLivePlayHistory(userID int64, userCtx UserContext) error {
	return service.MRPC.Call(URLGOHistoryClearLivePlayHistory, userCtx.ClientIP, map[string]interface{}{
		"user_id": userID,
	}, nil)
}

// DelLivePlayHistory 根据直播间 ID 删除用户特定的直播间观看记录
// userID 申请删除的用户 ID
// roomIDs 指定删除记录的直播间 ID 列表
func DelLivePlayHistory(userID int64, roomIDs []int64, userCtx UserContext) error {
	err := service.MRPC.Call(URLGOHistoryDelLivePlayHistory, userCtx.ClientIP, map[string]interface{}{
		"user_id":  userID,
		"room_ids": roomIDs,
	}, nil)
	return err
}

// DramaTypeNoLiveRecommend 无推荐直播模块的剧集属性类型
const DramaTypeNoLiveRecommend = "no_live_recommend"

// CheckDetails 属性判断结果的详细信息
type CheckDetails struct {
	NoLiveRecommend bool `json:"no_live_recommend"`
}

// CheckDramaRefinedResp 单个剧集的属性判断结果
type CheckDramaRefinedResp struct {
	CheckDetails CheckDetails `json:"check_details"`
}

// CheckDramaRefined 判断剧集属性
// dramaIDs 需要进行判定的剧集 ID 列表
// dramaTypes 需要判断是否符合的属性列表
// apiDoc: https://ci.maoer.co/apidoc/missevan-go/#api-_rpc_drama-check_drama_refined
func CheckDramaRefined(dramaIDs []int64, dramaTypes []string) (map[string]CheckDramaRefinedResp, error) {
	var resp map[string]CheckDramaRefinedResp
	err := service.MRPC.Call(URLGODramaCheckDramaRefined, "",
		map[string]interface{}{
			"drama_ids": dramaIDs,
			"types":     dramaTypes,
		}, &resp)
	return resp, err
}

// ShortURL 获取短链接
func ShortURL(uc mrpc.UserContext, originURL string) (string, error) {
	var resp shorturl.AddResult
	err := service.MRPC.Do(uc, URLGoShortURL,
		map[string]interface{}{
			"origin_url": originURL,
		}, &resp)
	if err != nil {
		return "", err
	}
	return resp.ShortURL, nil
}

type authRolesResp struct {
	Roles []string `json:"roles"`
}

// UserAuthRoles 获取用户后台角色
func UserAuthRoles(uc mrpc.UserContext, userID int64) ([]string, error) {
	var resp authRolesResp
	err := service.MRPC.Do(uc, URLGoAuthRoles,
		map[string]any{
			"user_id": userID,
		}, &resp)
	if err != nil {
		return nil, err
	}
	return resp.Roles, nil
}

type authUserIDsResp struct {
	UserIDs []int64 `json:"user_ids"`
}

// AuthUserIDs 通过角色获取用户 ID
func AuthUserIDs(uc mrpc.UserContext, roles []string) ([]int64, error) {
	var resp authUserIDsResp
	err := service.MRPC.Do(uc, URLGoAuthUserIDs,
		map[string]any{
			"roles": roles,
		}, &resp)
	if err != nil {
		return nil, err
	}
	return resp.UserIDs, nil
}

// IsDramaBoughtParams 用户是否已购买（已拥有）该剧集请求参数
type IsDramaBoughtParams struct {
	DramaID int64 `json:"drama_id"` // 剧集 ID
	UserID  int64 `json:"user_id"`  // 用户 ID
}

// IsDramaBought 用户是否已购买（已拥有）该剧集
func IsDramaBought(ctx mrpc.UserContext, params IsDramaBoughtParams) (bool, error) {
	var dramaBought bool
	err := service.MRPC.Do(ctx, URIDramaBought, params, &dramaBought)
	if err != nil {
		return false, err
	}
	return dramaBought, nil
}

// 兑换场景（0: 普通兑换码兑换; 1: 福袋广播剧兑换）
const (
	RedeemSceneRedeemCode = iota
	RedeemSceneTypeLuckyBag
)

// AddDramaTransactionRecordParams 添加剧集券 0 元消费记录（兑换剧集）请求参数
type AddDramaTransactionRecordParams struct {
	UserID               int64   `json:"user_id"`                // 用户 ID（接收剧集的用户）
	OperatorID           int64   `json:"operator_id"`            // 发起兑换的用户 ID
	DramaIDs             []int64 `json:"drama_ids"`              // 剧集 IDs
	Scene                int     `json:"scene"`                  // 场景（0: 普通兑换码兑换; 1: 福袋广播剧兑换）
	ContextTransactionID int64   `json:"context_transaction_id"` // 福袋广播剧兑换时传购买福袋的交易记录 ID
	UserAgent            string  `json:"user_agent"`             // 用户代理
	EquipID              string  `json:"equip_id"`
	BUVID                string  `json:"buvid"`
	IP                   string  `json:"ip"`
}

// AddDramaTransactionRecordResp 添加剧集券 0 元消费记录（兑换剧集）响应信息
type AddDramaTransactionRecordResp struct {
	TransactionIDs []int64 `json:"transaction_ids"`
}

// AddDramaTransactionRecord 添加剧集券 0 元消费记录（兑换剧集）
func AddDramaTransactionRecord(ctx mrpc.UserContext, params AddDramaTransactionRecordParams) (*AddDramaTransactionRecordResp, error) {
	var resp AddDramaTransactionRecordResp
	err := service.MRPC.Do(ctx, URIAddDramaTransactionRecord, params, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// risk scene 风险检测场景
const (
	RiskSceneCoupon = "coupon" // 营销
)

// ScanRiskParam 风险识别检测
type ScanRiskParam struct {
	Scene  string `json:"scene"`
	UserID int64  `json:"user_id"`
	IP     string `json:"ip"`
	BUVID  string `json:"buvid,omitempty"`
}

// ScanRisk 风险识别检测
func ScanRisk(ctx mrpc.UserContext, param ScanRiskParam) (*scan.BaseCheckResult, error) {
	res := new(scan.BaseCheckResult)
	err := service.MRPC.Do(ctx, URLGoScanRisk, param, res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// IsOfficeIPParam 判断是否是办公室 IP 参数
type IsOfficeIPParam struct {
	IP string `json:"ip"`
}

// IsOfficeIPResp 判断是否是办公室 IP 响应
type IsOfficeIPResp struct {
	IsOfficeIP bool `json:"is_office_ip"`
}

// IsOfficeIP 判断是否是办公室 IP
func IsOfficeIP(ctx mrpc.UserContext, param IsOfficeIPParam) (bool, error) {
	res := new(IsOfficeIPResp)
	err := service.MRPC.Do(ctx, URLGoIsOfficeIP, param, res)
	if err != nil {
		return false, err
	}
	return res.IsOfficeIP, nil
}

// 底部导航栏直播页面
const sceneTabBarLive = "tab_bar_live" // 底部导航栏直播页面

// MarkPageViewedParam 标记用户访问页面参数
type MarkPageViewedParam struct {
	UserID int64  `json:"user_id"`
	Scene  string `json:"scene"`
}

// MarkPageViewed 标记用户访问的页面，当前仅支持底部导航栏直播页面
func MarkPageViewed(ctx mrpc.UserContext, userID int64) error {
	param := MarkPageViewedParam{
		UserID: userID,
		Scene:  sceneTabBarLive,
	}
	return service.MRPC.Do(ctx, URLMainUserMarkPageViewed, param, nil)
}

// UserAuthInfoParam 获取用户认证信息参数
type UserAuthInfoParam struct {
	UserID int64 `json:"user_id"`
}

type userAuthInfoResp struct {
	AuthInfo *UserAuthInfo `json:"auth_info"`
}

// UserAuthInfo 用户认证信息
type UserAuthInfo struct {
	Type     uint   `json:"type"`
	Title    string `json:"title"`
	Subtitle string `json:"subtitle,omitempty"`
}

// GetUserAuthInfo 获取用户认证信息
func GetUserAuthInfo(ctx mrpc.UserContext, userID int64) (*UserAuthInfo, error) {
	res := new(userAuthInfoResp)
	param := UserAuthInfoParam{
		UserID: userID,
	}
	err := service.MRPC.Do(ctx, URLMainUserGetAuthInfo, param, res)
	if err != nil {
		return nil, err
	}
	return res.AuthInfo, nil
}
