package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

// SystemMsgBox 系统消息
type SystemMsgBox struct {
	Messages []pushservice.SystemMsg

	sendTime int64 // 秒级时间戳，0 为当前时间
}

// NewSystemMsgBox init a new SystemMsgBox
func NewSystemMsgBox(sendTime ...int64) *SystemMsgBox {
	box := &SystemMsgBox{
		Messages: []pushservice.SystemMsg{},
	}
	if len(sendTime) != 0 {
		box.sendTime = sendTime[0]
	}

	return box
}

// AddMessage add a message to SystemMsgBox
func (box *SystemMsgBox) AddMessage(userID int64, title, content string) {
	box.Messages = append(box.Messages, pushservice.SystemMsg{
		UserID:   userID,
		Title:    title,
		Content:  content,
		SendTime: box.sendTime,
	})
}

// Send send system message
func (box *SystemMsgBox) Send(sysMsgOptions ...*pushservice.SystemMsgOptions) error {
	if len(box.Messages) == 0 {
		return nil
	}
	var options *pushservice.SystemMsgOptions
	if len(sysMsgOptions) != 0 {
		options = sysMsgOptions[0]
	}
	return service.PushService.SendSystemMsgWithOptions(box.Messages, options)
}
