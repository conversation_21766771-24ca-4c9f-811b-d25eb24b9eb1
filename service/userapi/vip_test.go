package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestVipTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(GetVipUserIDsParams{}, "user_ids")
	kc.Check(GetVipUserIDsResp{}, "vip_user_ids")
}

func TestIsVipUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := GetVipUserIDsParams{
		UserIDs: []int64{10},
	}
	isCalled := false
	cancel := mrpc.SetMock(URLGetVipUserIDs, func(input any) (any, error) {
		isCalled = true
		assert.Equal(param, input)
		if goutil.HasElem(param.UserIDs, int64(10)) {
			return GetVipUserIDsResp{
				VipUserIDs: []int64{10},
			}, nil
		}
		return GetVipUserIDsResp{}, nil
	})
	defer cancel()

	// 测试是用户是会员
	vipUser, err := IsVipUser(c, 10)
	require.NoError(err)
	require.True(isCalled)
	assert.True(vipUser)

	// 测试用户不是会员
	param.UserIDs = []int64{11}
	vipUser, err = IsVipUser(c, 11)
	require.NoError(err)
	require.True(isCalled)
	assert.False(vipUser)
}
