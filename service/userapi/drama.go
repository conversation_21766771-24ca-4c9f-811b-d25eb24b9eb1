package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 剧集相关接口
const (
	URLGetDramaInfo    = "mrpc://missevan-main/drama/get-drama-info"     // 获取剧集信息
	URLGetUserDramaCVs = "mrpc://missevan-main/drama/get-user-drama-cvs" // 获取用户已追或已购的剧集参演声优信息
)

// 剧集审核状态
const (
	DramaCheckedNotVerify       = iota // 未审核
	DramaCheckedPass                   // 审核通过
	DramaCheckedDiscontinued           // 下架
	DramaCheckedPolice                 // 报警（TODO: 待整合）
	DramaCheckedContractExpired        // 合约到期下架
)

// 剧集的付费类型
const (
	DramaPayTypeFree    = iota // 免费
	DramaPayTypeEpisode        // 单集付费
	DramaPayTypeDrama          // 整剧付费
)

// 剧集 refined 定义
const (
	DramaRefinedNoJapanSale = 7 // 日本地区禁售
)

// CountryCodeJP 日本地区代码
const CountryCodeJP = "JP"

// 查询声优信息业务场景
const (
	DramaCVSceneSearch        = "search"         // 搜索页业务
	DramaCVSceneLiveRecommend = "live_recommend" // 底部导航直播 tab 页推荐业务
)

// MinVipDiscountPrice 最低会员折扣价格（原价）
const MinVipDiscountPrice = 5

// DramaInfoResp 获取剧集详情结果
type DramaInfoResp struct {
	Drama *DramaInfo `json:"drama"`
}

// DramaInfo 剧集详情
type DramaInfo struct {
	ID          int64            `json:"id"`
	Name        string           `json:"name"`
	Checked     int              `json:"checked"` // 剧集过审状态 (1: 过审; 4: 合约期满下架) 接口只会返回状态为 1、4 的剧集
	IPRID       int64            `json:"ipr_id"`
	IPRName     string           `json:"ipr_name"`
	CoverURL    string           `json:"cover_url"`
	CoverColor  int64            `json:"cover_color"`
	PayType     int              `json:"pay_type"` // 付费类型 (0: 免费; 1: 单集付费; 2: 整剧付费)
	Refined     goutil.BitMask   `json:"refined"`  // refined 定义 (比特位第七位为 1 时表示日本地区禁购剧集)
	Price       int              `json:"price"`    // 剧集价格 (单位: 钻)
	CatalogID   int64            `json:"catalog_id"`
	VipDiscount *VipDiscountInfo `json:"vip_discount,omitempty"`
}

// VipDiscountInfo 会员折扣信息
type VipDiscountInfo struct {
	Rate  float32 `json:"rate"`            // 会员折扣值，e.g. 0.8
	Price int     `json:"price,omitempty"` // 整剧付费剧集的会员折扣价格，仅在整剧付费时下发。单位：钻
}

// GetDramaInfo 获取剧集详情
// 调用者应根据需求判断 Checked 和 Refined 字段
// 如果剧集不存在或者不是有效剧集，drama 字段为 nil
func GetDramaInfo(uc mrpc.UserContext, dramaID, userID int64) (*DramaInfo, error) {
	var resp DramaInfoResp
	err := service.MRPC.Do(uc, URLGetDramaInfo,
		map[string]any{
			"drama_id": dramaID,
			"user_id":  userID,
		}, &resp)
	if err != nil {
		return nil, err
	}
	return resp.Drama, nil
}

// ListDramaCVResp 用户已追或已购的剧集声优详情结果
type ListDramaCVResp struct {
	CVs []DramaCVInfo `json:"cvs"`
}

// DramaCVInfo 声优详情结果
type DramaCVInfo struct {
	ID     int64 `json:"id"`      // 声优 ID
	UserID int64 `json:"user_id"` // 声优对应的用户 ID
}

// ListUserDramaCVs 获取用户已追或已购的剧集参演声优信息
func ListUserDramaCVs(uc mrpc.UserContext, userID int64, scene string) ([]DramaCVInfo, error) {
	var resp ListDramaCVResp
	err := service.MRPC.Do(uc, URLGetUserDramaCVs,
		map[string]any{
			"user_id": userID,
			"scene":   scene,
		}, &resp)
	if err != nil {
		return nil, err
	}
	return resp.CVs, nil
}
