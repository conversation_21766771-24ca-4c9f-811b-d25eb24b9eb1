package userapi

import (
	"encoding/json"
	"errors"
	"fmt"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/search"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	mockClient *mrpc.Client
	orgClient  *mrpc.Client

	mockAction = handler.NewAction(handler.POST, nil, false)
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest()
	orgClient = service.MRPC

	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"live/send-rebate-gift": handler.NewAction(handler.POST, actionRebateGift, false),
			"live/buy-lucky-gift":   mockAction,
			"live/buy-gift":         mockAction,
			"live/buy-super-fan":    mockAction,
			"broadcast/many":        mockAction,
			"rank/revenue":          mockAction,
			"live/ask":              mockAction,
		},
	}
	addr := tutil.RunMockServer(r, 0, &h)
	appConf := config.Conf.Service.MRPC["app"]
	appConf.URL = fmt.Sprintf("http://%s/", addr)
	mockConf := mrpc.Config{"app": appConf, "im": appConf, "fm": appConf}
	mockClient = mrpc.NewRPCClient(mockConf)
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(BroadcastElem{}, "type", "room_id", "user_id", "priority", "payload")

	kc.Check(CancelAsksResp{}, "transactions")
	kc.Check(CancelAsksTransaction{}, "transaction_id", "price", "error")

	kc.Check(BalanceResp{}, "transaction_id", "balance", "live_noble_balance", "price", "context")
	kc.Check(BuyBalance{}, "balance", "live_noble_balance", "live_noble_balance_status")

	kc.Check(AddDramaTransactionRecordParams{}, "user_id", "operator_id", "drama_ids", "scene", "context_transaction_id", "user_agent", "equip_id", "buvid", "ip")
	kc.Check(AddDramaTransactionRecordResp{}, "transaction_ids")

	kc.Check(IsDramaBoughtParams{}, "drama_id", "user_id")

	kc.Check(ScanRiskParam{}, "scene", "user_id", "ip", "buvid")
}

func actionRebateGift(c *handler.Context) (handler.ActionResponse, error) {
	return &BalanceResp{
		TransactionID:    123,
		Balance:          123,
		LiveNobleBalance: 123,
		Price:            123,
	}, nil
}

func TestUserContextCookies(t *testing.T) {
	assert := assert.New(t)

	c := NewUserContext(goutil.SmartUserContext{
		UserToken:   "123",
		UserEquipID: "456",
		UserBUVID:   "789",
	})
	assert.Equal(map[string]string{
		"token":    "123",
		"buvid":    "789",
		"equip_id": "456",
	}, c.Cookies())
}

func TestNewBuyBalance(t *testing.T) {
	assert := assert.New(t)

	// 测试为贵族的情况
	balanceResp := &BalanceResp{
		Balance:          233,
		LiveNobleBalance: 234,
	}
	buyBalance := NewBuyBalance(balanceResp, true)
	assert.Equal(balanceResp.Balance, buyBalance.Balance)
	assert.Equal(balanceResp.LiveNobleBalance, buyBalance.LiveNobleBalance)
	assert.Equal(1, buyBalance.LiveNobleBalanceStatus)

	// 测试不为贵族的情况
	buyBalance = NewBuyBalance(balanceResp, false)
	assert.Equal(balanceResp.Balance, buyBalance.Balance)
	assert.Equal(balanceResp.LiveNobleBalance, buyBalance.LiveNobleBalance)
	assert.Equal(0, buyBalance.LiveNobleBalanceStatus)
}

func TestBuySuperFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.MRPC = mockClient
	defer func() { service.MRPC = orgClient }()

	mockAction.Action = func(c *handler.Context) (handler.ActionResponse, error) {
		return &BalanceResp{
			TransactionID:    432105,
			Balance:          500,
			LiveNobleBalance: 1500,
			Price:            1200,
		}, nil
	}

	oid := primitive.NewObjectID()
	resp, err := BuySuperFan(444, 666, 7, 1000, 1, "景向",
		true, &oid, mrpc.UserContext{})
	require.NoError(err)
	require.NotNil(resp)
	assert.Greater(resp.TransactionID, int64(0))
	assert.Greater(resp.Price, int64(0))
	assert.GreaterOrEqual(resp.Balance, int64(0))
	assert.GreaterOrEqual(resp.LiveNobleBalance, int64(0))
}

func TestIMRoomList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(IMRoomListRequest{}, "room_id", "users", "conns", "valuable_users")
	kc.Check(IMRoomListResp{}, "user_ids", "conns", "valuable_user_ids", "users")

	c := NewUserContext(goutil.SmartUserContext{IP: "127.0.0.1"})
	req := IMRoomListRequest{RoomID: 123}
	cancel := mrpc.SetMock(URIIMRoomList, func(interface{}) (interface{}, error) {
		var res IMRoomListResp
		if req.Users {
			res.UserIDs = []int64{123, 456}
		}
		if req.Conns {
			res.Conns = []string{"test"}
		}
		if req.ValuableUsers {
			res.ValuableUserIDs = []int64{456}
		}
		return res, nil
	})
	defer cancel()

	req.Users = true
	resp, err := IMRoomList(req, c)
	require.NoError(err)
	assert.Equal([]int64{123, 456}, resp.UserIDs)
	assert.Empty(resp.Conns)
	assert.Empty(resp.ValuableUserIDs)

	req.Conns = true
	resp, err = IMRoomList(req, c)
	require.NoError(err)
	assert.Equal([]int64{123, 456}, resp.UserIDs)
	assert.Equal([]string{"test"}, resp.Conns)
	assert.Empty(resp.ValuableUserIDs)

	req.ValuableUsers = true
	resp, err = IMRoomList(req, c)
	require.NoError(err)
	assert.Equal([]int64{123, 456}, resp.UserIDs)
	assert.Equal([]string{"test"}, resp.Conns)
	assert.Equal([]int64{456}, resp.ValuableUserIDs)

	// 兼容情况
	mrpc.SetMock(URIIMRoomList, func(interface{}) (interface{}, error) {
		return IMRoomListResp{Users: []int64{123}}, nil
	})
	req = IMRoomListRequest{RoomID: 1, Users: true}
	resp, err = IMRoomList(req, c)
	require.NoError(err)
	assert.Equal([]int64{123}, resp.UserIDs)
}

func TestIMRoomsList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	// 检查请求和响应结构体的字段
	kc.Check(IMRoomsListRequest{}, "room_ids", "users")
	kc.Check(IMRoomsListResp{}, "rooms")

	t.Run("正常获取在线用户", func(t *testing.T) {
		cleanup := mrpc.SetMock(URIIMRoomsList, func(input interface{}) (interface{}, error) {
			req, ok := input.(*IMRoomsListRequest)
			require.True(ok)
			assert.Equal([]int64{123, 456}, req.RoomIDs)
			assert.True(req.Users)

			return &IMRoomsListResp{
				Rooms: map[int64]IMRoomInfo{
					123: {UserIDs: []int64{1, 2, 3}},
					456: {UserIDs: []int64{4, 5, 6}},
				},
			}, nil
		})
		defer cleanup()

		req := &IMRoomsListRequest{
			RoomIDs: []int64{123, 456},
			Users:   true,
		}
		resp, err := IMRoomsList(mrpc.UserContext{}, req)
		require.NoError(err)
		assert.NotNil(resp)
		assert.Len(resp.Rooms, 2)
		assert.Equal(IMRoomInfo{UserIDs: []int64{1, 2, 3}}, resp.Rooms[123])
		assert.Equal(IMRoomInfo{UserIDs: []int64{4, 5, 6}}, resp.Rooms[456])
	})

	t.Run("空房间列表", func(t *testing.T) {
		cleanup := mrpc.SetMock(URIIMRoomsList, func(input interface{}) (interface{}, error) {
			return &IMRoomsListResp{
				Rooms: map[int64]IMRoomInfo{},
			}, nil
		})
		defer cleanup()

		req := &IMRoomsListRequest{
			RoomIDs: []int64{},
			Users:   true,
		}
		resp, err := IMRoomsList(mrpc.UserContext{}, req)
		require.NoError(err)
		assert.NotNil(resp)
		assert.Empty(resp.Rooms)
	})

	t.Run("不查询用户列表", func(t *testing.T) {
		cancel := mrpc.SetMock(URIIMRoomsList, func(input interface{}) (interface{}, error) {
			req, ok := input.(*IMRoomsListRequest)
			require.True(ok)
			assert.False(req.Users)

			return nil, errors.New("参数错误")
		})
		defer cancel()

		req := &IMRoomsListRequest{
			RoomIDs: []int64{123},
			Users:   false,
		}
		resp, err := IMRoomsList(mrpc.UserContext{}, req)
		require.ErrorContains(err, "参数错误")
		assert.Nil(resp)
	})
}

func TestIMNotifySet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cleanup := mrpc.SetMock(URIIMNotifySet, func(input interface{}) (output interface{}, err error) {
		count++
		return nil, nil
	})
	defer cleanup()

	require.NoError(IMNotifySet(mrpc.UserContext{}, 123, []int64{12345, 45678}))
	assert.Equal(1, count)
}

func TestAsk(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock(URIAsk, func(input interface{}) (interface{}, error) {
		return &BalanceResp{
			TransactionID: 123,
			Price:         30,
			Balance:       30,
		}, nil
	})
	defer cleanup()

	oid := primitive.NewObjectID()
	_, err := Ask(12, 10, 30, false, &oid, mrpc.UserContext{})
	require.NoError(err)
}

func TestConfirmAsk(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock(URIConfirmAsk, func(input interface{}) (interface{}, error) {
		return &BalanceResp{
			TransactionID: 123,
			Price:         30,
			Balance:       30,
		}, nil
	})
	defer cleanup()

	_, err := ConfirmAsk(12, 10, true, mrpc.UserContext{})
	require.NoError(err)
}

func TestCancelAsks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URICancelAsks, func(input interface{}) (interface{}, error) {
		v, ok := input.(map[string]interface{})
		require.True(ok)
		tids, ok := v["transaction_ids"].([]int64)
		require.True(ok)
		return &CancelAsksResp{Transactions: make([]CancelAsksTransaction, len(tids))}, nil
	})
	defer cleanup()

	r, err := CancelAsks(12, []int64{1, 2, 3}, NewUserContext(nil))
	require.NoError(err)
	assert.Len(r.Transactions, 3)
}

func TestUpdateDrawPoint(t *testing.T) {
	assert := assert.New(t)

	eventID := int64(214)
	userID := int64(12)
	point := int64(10)
	cancel := mrpc.SetMock(URLMinigameDrawPointUpdate, func(input interface{}) (interface{}, error) {
		assert.Equal(map[string]int64{
			"event_id": eventID,
			"user_id":  userID,
			"point":    point,
		}, input)
		return map[string]int64{"point": point}, nil
	})
	defer cancel()
	assert.NoError(UpdateDrawPoint(eventID, userID, point))
}

func TestBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URIIMBroadcast, func(input any) (output any, err error) {
		var body BroadcastElem
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		assert.Equal(1, body.Priority)
		return true, nil
	})
	defer cleanup()

	input := map[string]interface{}{
		"type":    "test",
		"event":   "test",
		"room_id": 123,
	}
	err := Broadcast(123, input, &BroadcastOption{Priority: BroadcastPriorityPurchased})
	require.NoError(err)
}

func TestBroadcastUser(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock(URIIMBroadcastUser, func(any) (any, error) {
		return true, nil
	})
	defer cleanup()
	const (
		roomID = 22489473
		userID = 12345678
	)
	type syncMedalMessage struct {
		Type       string `json:"type"`
		NotifyType string `json:"notify_type"`
		Event      string `json:"event"`
	}
	p := syncMedalMessage{
		Type:       "user_notify", // liveim.TypeUserNotify
		NotifyType: "medal",       // liveim.TypeMedal
		Event:      "update",      // liveim.EventUpdate
	}
	err := BroadcastUser(roomID, userID, p)
	require.NoError(err)
}

func TestBroadcastMany(t *testing.T) {
	assert := assert.New(t)

	cleanup := mrpc.SetMock(URIIMBroadcastMany, func(any) (any, error) {
		return true, nil
	})
	defer cleanup()
	msg := make([]*BroadcastElem, 21)
	for i := range msg {
		msg[i] = &BroadcastElem{Type: 1, Payload: "test"}
	}
	assert.NoError(BroadcastMany(msg))
}

func TestSendLiveHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	accessTime := goutil.NewTimeUnixMilli(goutil.TimeNow())
	cancel := mrpc.SetMock("app://live/add-listen-history", func(i interface{}) (interface{}, error) {
		param := i.(map[string]interface{})
		assert.Equal(int64(1), param["user_id"])
		assert.Equal(int64(1), param["room_id"])
		assert.Equal(accessTime, param["access_time"])
		return nil, nil
	})
	defer cancel()
	err := SendLiveHistory(1, 1, accessTime, UserContext{})
	require.NoError(err)
}

func TestCreateLivePenaltyOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	link := "https://openapi.alipay.com/gateway.do?alipay_sdk=alipay-sdk&...version=1.0"
	now := goutil.TimeNow()
	cleanup := mrpc.SetMock(URICreateLivePenaltyOrder,
		func(input interface{}) (output interface{}, err error) {
			param := input.(map[string]interface{})
			assert.Equal(int64(1000), param["applyment_id"])
			assert.Equal(int64(500000), param["price"])
			return handler.M{
				"order": map[string]interface{}{
					"price":         500000,
					"live_id":       400791,
					"guild_id":      3,
					"applyment_id":  1000,
					"status":        0,
					"type":          1,
					"create_time":   now.Unix(),
					"modified_time": now.Unix(),
					"id":            8975,
				},
				"link":  link,
				"price": "5000.00",
			}, nil
		})
	defer cleanup()

	resp, err := CreateLivePenaltyOrder(1000, 500000, UserContext{}.ClientIP)
	require.NoError(err)
	require.Equal(link, resp["link"])
	require.Equal("5000.00", resp["price"])
}

func TestDiscoverySearch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("go://discovery/search", func(i interface{}) (interface{}, error) {
		param := i.(*DiscoverySearchParams)
		assert.Equal(search.TypeLive, param.SearchType)
		assert.Equal("key", param.Keyword)
		assert.Equal("guild", param.GuideWord)
		assert.Equal(int64(1), param.Page)
		assert.Equal(int64(20), param.PageSize)
		return nil, nil
	})
	defer cancel()
	req := &DiscoverySearchParams{
		SearchType: search.TypeLive,
		Keyword:    "key",
		GuideWord:  "guild",
		Page:       1,
		PageSize:   20,
	}
	_, err := DiscoverySearch(req, "")
	require.NoError(err)
}

func TestClearLivePlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(114514)

	cleanup := mrpc.SetMock(URLGOHistoryClearLivePlayHistory, func(input interface{}) (output interface{}, err error) {
		params := input.(map[string]interface{})
		assert.EqualValues(userID, params["user_id"])
		return nil, nil
	})
	defer cleanup()

	require.NoError(ClearLivePlayHistory(userID, UserContext{ClientIP: "127.0.0.1"}))
}

func TestDelLivePlayHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(114514)
	roomIDs := []int64{1919810, 1919816, 1919819}

	cleanup := mrpc.SetMock(URLGOHistoryDelLivePlayHistory, func(input interface{}) (output interface{}, err error) {
		params := input.(map[string]interface{})
		assert.EqualValues(userID, params["user_id"])
		assert.EqualValues(roomIDs, params["room_ids"])
		return nil, nil
	})
	defer cleanup()

	require.NoError(DelLivePlayHistory(userID, roomIDs, UserContext{ClientIP: "127.0.0.1"}))
}

func TestCheckDramaRefined(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dramaIDs := []int64{1001919}
	dramaTypes := []string{"helloworld"}

	cleanup := mrpc.SetMock(URLGODramaCheckDramaRefined, func(input interface{}) (output interface{}, err error) {
		params := input.(map[string]interface{})

		assert.EqualValues(dramaIDs[0], params["drama_ids"].([]int64)[0])
		assert.EqualValues(dramaTypes[0], params["types"].([]string)[0])
		return CheckDramaRefinedResp{}, nil
	})
	defer cleanup()

	resp, err := CheckDramaRefined(dramaIDs, dramaTypes)
	require.NoError(err)
	assert.NotNil(resp)
}

func TestShortURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShortURL := "https://123.cc"
	cleanup := mrpc.SetMock(URLGoShortURL, func(input interface{}) (output interface{}, err error) {
		return handler.M{"short_url": testShortURL}, nil
	})
	defer cleanup()

	shortURL, err := ShortURL(mrpc.UserContext{}, "https://123222.wwwwww")
	require.NoError(err)
	assert.Equal(testShortURL, shortURL)
}

func TestUserAuthRoles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expectedRoles := []string{"test"}
	cancel := mrpc.SetMock(URLGoAuthRoles, func(any) (any, error) {
		return map[string]any{
			"roles": expectedRoles,
		}, nil
	})
	defer cancel()

	roles, err := UserAuthRoles(mrpc.UserContext{}, 12)
	require.NoError(err)
	assert.Equal(expectedRoles, roles)
}

func TestAuthUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expectedUserIDs := []int64{12}
	cancel := mrpc.SetMock(URLGoAuthUserIDs, func(any) (any, error) {
		return map[string]any{
			"user_ids": []int64{12},
		}, nil
	})
	defer cancel()

	userIDs, err := AuthUserIDs(mrpc.UserContext{}, []string{"test"})
	require.NoError(err)
	assert.Equal(expectedUserIDs, userIDs)
}

func TestIsDramaBought(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URIDramaBought, func(any) (any, error) {
		ok = true
		return true, nil
	})
	defer cancel()

	params := IsDramaBoughtParams{
		DramaID: 10,
		UserID:  12,
	}
	dramaBought, err := IsDramaBought(mrpc.UserContext{}, params)
	require.NoError(err)
	assert.True(dramaBought)
	assert.True(ok)
}

func TestAddDramaTransactionRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URIAddDramaTransactionRecord, func(any) (any, error) {
		ok = true
		return &AddDramaTransactionRecordResp{
			TransactionIDs: []int64{1},
		}, nil
	})
	defer cancel()

	c := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	params := AddDramaTransactionRecordParams{
		UserID:               12,
		OperatorID:           12,
		DramaIDs:             []int64{1, 2},
		Scene:                RedeemSceneTypeLuckyBag,
		ContextTransactionID: 1234567,
		UserAgent:            c.UserAgent,
		EquipID:              c.EquipID,
		BUVID:                c.BUVID,
		IP:                   c.IP,
	}
	resp, err := AddDramaTransactionRecord(mrpc.UserContext{}, params)
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues([]int64{1}, resp.TransactionIDs)
	assert.True(ok)
}

func TestScanRisk(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URLGoScanRisk, func(any) (any, error) {
		ok = true
		return &scan.CheckResult{
			Score: 10,
			Pass:  true,
		}, nil
	})
	defer cancel()

	param := ScanRiskParam{
		UserID: 1,
		IP:     "*********",
		Scene:  RiskSceneCoupon,
	}
	resp, err := ScanRisk(mrpc.UserContext{}, param)
	require.NoError(err)
	require.NotNil(resp)
	assert.True(resp.Pass)
	assert.True(ok)
}

func TestIsOfficeIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URLGoIsOfficeIP, func(any) (any, error) {
		ok = true
		return &IsOfficeIPResp{IsOfficeIP: true}, nil
	})
	defer cancel()

	param := IsOfficeIPParam{
		IP: "*********",
	}
	ok, err := IsOfficeIP(mrpc.UserContext{}, param)
	require.NoError(err)
	assert.True(ok)
}

func TestMarkPageViewed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URLMainUserMarkPageViewed, func(any) (any, error) {
		ok = true
		return "success", nil
	})
	defer cancel()

	require.NoError(MarkPageViewed(mrpc.UserContext{}, 9074509))
	assert.True(ok)
}

func TestGetUserAuthInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testResp = UserAuthInfo{
			Type:     2,
			Title:    "cc",
			Subtitle: "冠军认证",
		}
	)
	cancel := mrpc.SetMock(URLMainUserGetAuthInfo, func(any) (any, error) {
		return userAuthInfoResp{
			AuthInfo: &testResp,
		}, nil
	})
	defer cancel()

	resp, err := GetUserAuthInfo(mrpc.UserContext{}, 12)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(testResp, *resp)

	mrpc.SetMock(URLMainUserGetAuthInfo, func(any) (any, error) {
		return userAuthInfoResp{AuthInfo: nil}, nil
	})
	resp, err = GetUserAuthInfo(mrpc.UserContext{}, 12)
	require.NoError(err)
	require.Nil(resp)
}
