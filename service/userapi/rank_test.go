package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRankTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(RankListParam{}, "type", "sub_type", "bizdate")
	kc.Check(RankListResp{}, "rank_element_type", "rank_element_ids", "rank_elements", "bizdate")
}

func TestRankList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := RankListParam{
		Type:    RankTypeDramaPopularity,
		SubType: RankSubTypeWeek,
		Bizdate: goutil.TimeNow().String(),
	}
	isCalled := false
	cancel := mrpc.SetMock(URLRankList, func(input any) (any, error) {
		isCalled = true
		assert.Equal(param, input)
		return RankListResp{}, nil
	})
	defer cancel()

	resp, err := RankList(c, param)
	require.NoError(err)
	assert.NotNil(resp)
	assert.True(isCalled)
}
