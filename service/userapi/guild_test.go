package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTransferRevenue(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(APIGuildTransferRevenue,
		func(any) (any, error) {
			return handler.M{
				"status": true,
			}, nil
		})
	defer cancel()

	now := goutil.TimeNow()
	err := GuildTransferRevenue(mrpc.NewUserContextFromEnv(), 1, 3, []int64{1, 2, 3},
		goutil.BeginningOfMonth(now), goutil.EndOfMonth(now))
	assert.NoError(err)
}
