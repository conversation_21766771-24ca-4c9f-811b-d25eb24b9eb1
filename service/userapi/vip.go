package userapi

import (
	"slices"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

const (
	// URLGetVipUserIDs 获取点播会员用户 IDs
	URLGetVipUserIDs = "mrpc://missevan-main/user/get-vip-user-ids"
)

// GetVipUserIDsParams 获取点播会员用户 IDs 请求参数
type GetVipUserIDsParams struct {
	UserIDs []int64 `json:"user_ids"`
}

// GetVipUserIDsResp 获取点播会员用户 IDs 响应参数
type GetVipUserIDsResp struct {
	VipUserIDs []int64 `json:"vip_user_ids"`
}

// IsVipUser 用户是否是会员
func IsVipUser(uc mrpc.UserContext, userID int64) (bool, error) {
	var resp GetVipUserIDsResp
	err := service.MRPC.Do(uc, URLGetVipUserIDs, GetVipUserIDsParams{UserIDs: []int64{userID}}, &resp)
	if err != nil {
		return false, err
	}
	return slices.Contains(resp.VipUserIDs, userID), nil
}
