package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestUserConfigTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(GetUserConfigParam{}, "user_id", "buvid")
	kc.Check(GetUserConfigResp{}, "config")
	kc.Check(MUserConfig{}, "user_id", "buvid", "app_config")
	kc.Check(AppConfig{}, "personalized_recommend", "show_subscribe_drama", "show_user_collect", "message_notification")
	kc.Check(MessageNotificationConf{}, "at_me", "like", "comment", "private_message", "live", "interest_recommend")

	kc.CheckOmitEmpty(AppConfig{}, "personalized_recommend", "show_subscribe_drama", "show_user_collect", "message_notification")
	kc.CheckOmitEmpty(MessageNotificationConf{}, "at_me", "like", "comment", "private_message", "live", "interest_recommend")
}

func TestGetUserConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(12)
	testBuvid := "00a233d2-b7ae-11ef-951f-155b72b8d081"

	uc := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := GetUserConfigParam{
		UserID: testUserID,
		BUVID:  testBuvid,
	}
	isCalled := false
	cancel := mrpc.SetMock(URLGetUserConfig, func(input any) (any, error) {
		isCalled = true
		assert.Equal(param, input)
		return GetUserConfigResp{
			Config: &MUserConfig{
				UserID:    param.UserID,
				Buvid:     param.BUVID,
				AppConfig: AppConfig{},
			},
		}, nil
	})
	defer cancel()

	resp, err := GetUserConfig(uc, testUserID, testBuvid)
	require.NoError(err)
	assert.NotNil(resp)
	assert.Equal(testUserID, resp.UserID)
	assert.Equal(testBuvid, resp.Buvid)
	assert.True(isCalled)
}
