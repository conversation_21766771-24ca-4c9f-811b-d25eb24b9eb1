package userapi

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAddAdminLog(t *testing.T) {
	assert := assert.New(t)

	// req := httptest.NewRequest("GET", "/test/addadminlog", nil)
	// c := util.SmartUserContext{}
	c := handler.NewTestContext("GET", "/test/addadminlog", true, nil)
	logs := NewAdminLogBox(c)
	createTime := goutil.TimeNow().Unix()
	logs.AddAdminLog("test", CatalogPassRoomInfo)

	assert.Len(logs.adminLogs, 1)
	adminLog := logs.adminLogs[0]
	assert.Equal("/test/addadminlog", adminLog.URL)
	assert.Equal(CatalogPassRoomInfo, adminLog.Catalog)
	assert.Equal(int64(12), adminLog.UserID)
	assert.Equal("test", adminLog.Intro)
	assert.GreaterOrEqual(adminLog.CreateTime, createTime)
}

func TestSendAdminLog(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	c := util.SmartUserContext{Req: httptest.NewRequest("GET", "/test/addadminlog", nil)}
	logs := NewAdminLogBox(c)
	require.NoError(logs.Send())

	logs.AddAdminLog("test", CatalogPassRoomInfo)
	assert.Len(logs.adminLogs, 1)
	require.NoError(logs.Send())

	require.NoError(SendAdminLogs(c, logs.adminLogs...))
}

func TestAdminLogAddWithChannelID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := util.SmartUserContext{
		Req: httptest.NewRequest(http.MethodPost, "/123", nil),
		UID: 12,
		IP:  "127.0.0.1",
	}
	a := NewAdminLogBox(c)
	nowUnix := goutil.TimeNow().Unix()
	a.AddWithChannelID(CatalogPassRoomInfo, 123, "test")
	require.Len(a.adminLogs, 1)
	l := a.adminLogs[0]
	assert.Equal(int64(12), l.UserID)
	assert.Equal(86, l.Catalog)
	assert.Equal(int64(123), l.ChannelID)
	assert.Equal("/123", l.URL)
	assert.Equal("test", l.Intro)
	assert.Equal("127.0.0.1", l.IP)
	assert.GreaterOrEqual(l.CreateTime, nowUnix)
}
