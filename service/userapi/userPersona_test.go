package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestUserPersonaTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(GetPersonaParam{}, "equip_id", "buvid", "user_id")
	kc.Check(GetPersonaResp{}, "persona", "is_new_user", "is_new_equip", "sex")

	kc.CheckOmitEmpty(GetPersonaParam{}, "equip_id", "buvid", "user_id")
	kc.CheckOmitEmpty(GetPersonaResp{}, "is_new_user", "is_new_equip")
}

func TestGetUserPersona(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(12)
	testEquipID := "de778e88-86f2-11ef-b667-5809a8708f52"
	testBuvid := "00a233d2-b7ae-11ef-951f-155b72b8d081"

	uc := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := GetPersonaParam{
		UserID:  testUserID,
		BUVID:   testBuvid,
		EquipID: testEquipID,
	}
	isCalled := false
	cancel := mrpc.SetMock(URLGetUserPersona, func(input any) (any, error) {
		isCalled = true
		assert.Equal(param, input)
		return GetPersonaResp{}, nil
	})
	defer cancel()

	resp, err := GetUserPersona(uc, testUserID, testEquipID, testBuvid)
	require.NoError(err)
	assert.NotNil(resp)
	assert.True(isCalled)
}

func TestIsValidPersona(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsValidPersona(TypeBoy))
	assert.False(IsValidPersona(100))
}
