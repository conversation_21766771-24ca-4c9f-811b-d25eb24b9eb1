package userapi

import (
	"errors"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// 清除本地缓存 URL
const (
	URLInteractionDevFlushCache = "fm://live-interaction/dev/flush-cache"
	URLWorkerDevFlushCache      = "fm://live-service-worker/dev/flush-cache"
	URLActivityDevFlushCache    = "activity://dev/flush-cache"
)

// FlushCache 清除 live-interaction 和 live-activity 两个服务本地缓存
func FlushCache(ctx mrpc.UserContext) error {
	return errors.Join(
		service.MRPC.Do(ctx, URLInteractionDevFlushCache, nil, nil),
		service.MRPC.Do(ctx, URLActivityDevFlushCache, nil, nil),
		service.MRPC.Do(ctx, URLWorkerDevFlushCache, nil, nil),
	)
}
