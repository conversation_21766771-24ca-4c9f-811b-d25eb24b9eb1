package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// URLGetUserPersona 获取用户画像信息
const URLGetUserPersona = "mrpc://missevan-main/user/get-persona"

// 用户性别
const (
	// SexUnknown 未知性别
	SexUnknown = 0
	// SexBoy 男性
	SexBoy = 1
	// SexGirl 女性
	SexGirl = 2
)

// 用户画像
// NOTICE: 女性画像 ID 需要为奇数
const (
	TypeGeneral    int64 = 1  // 大众
	TypeBoy        int64 = 2  // 普通男
	TypeGirl       int64 = 3  // 普通女
	TypeFujoshi    int64 = 7  // 腐女
	TypeOtome      int64 = 9  // 乙女
	TypeManualGirl int64 = 11 // 人工维护女性画像
	TypeManualBoy  int64 = 12 // 人工维护男性画像
)

// PersonaModuleMask 字段 persona 第 1~8 位存模块画像，第 9~16 位存猜你喜欢音推荐策略
const PersonaModuleMask = 0x00ff

// GetPersonaParam 获取用户画像信息参数
type GetPersonaParam struct {
	EquipID string `json:"equip_id,omitempty"` // 设备号
	BUVID   string `json:"buvid,omitempty"`    // buvid
	UserID  int64  `json:"user_id,omitempty"`  // 用户 ID
}

// GetPersonaResp 获取用户画像信息响应
type GetPersonaResp struct {
	Persona    int64 `json:"persona"`                // 画像信息
	IsNewUser  *bool `json:"is_new_user,omitempty"`  // 是否为新建用户画像
	IsNewEquip *bool `json:"is_new_equip,omitempty"` // 是否为新建设备画像
	Sex        int   `json:"sex"`                    // 用户性别，0 未知，1 表示男，2 表示女
}

// GetUserPersona 获取用户画像信息
func GetUserPersona(uc mrpc.UserContext, userID int64, equipID, buvid string) (*GetPersonaResp, error) {
	var resp GetPersonaResp
	err := service.MRPC.Do(uc, URLGetUserPersona, GetPersonaParam{EquipID: equipID, BUVID: buvid, UserID: userID}, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// IsValidPersona 验证是否是有效画像
func IsValidPersona(typeValue int64) bool {
	switch typeValue {
	case TypeGeneral,
		TypeBoy,
		TypeGirl,
		TypeFujoshi,
		TypeOtome,
		TypeManualGirl,
		TypeManualBoy:
		return true
	default:
		return false
	}
}
