package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestFlushCache(t *testing.T) {
	assert := assert.New(t)

	var count int
	cleanup := mrpc.SetMock(URLInteractionDevFlushCache, func(any) (any, error) {
		count++
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(URLActivityDevFlushCache, func(any) (any, error) {
		count++
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(URLWorkerDevFlushCache, func(any) (any, error) {
		count++
		return nil, nil
	})
	defer cleanup()

	err := FlushCache(mrpc.NewUserContextFromEnv())
	assert.Nil(err)
	assert.Equal(3, count)
}
