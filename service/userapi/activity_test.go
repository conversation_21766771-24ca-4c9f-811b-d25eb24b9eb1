package userapi

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util/retry"
)

func TestRankRevenueParams_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testParams := RankRevenueParams{
		RoomID:            1,
		UserID:            2,
		CreatorID:         3,
		ActivityCatalogID: 4,
		TransactionID:     1,
	}
	count := 0
	cancel := mrpc.SetMock(URIRankRevenue, func(input any) (any, error) {
		body, ok := input.(*RankRevenueParams)
		require.True(ok)
		require.NotNil(body)
		assert.Equal(testParams.RoomID, body.RoomID)
		assert.Equal(testParams.UserID, body.UserID)
		assert.Equal(testParams.CreatorID, body.CreatorID)
		assert.Equal(testParams.ActivityCatalogID, body.ActivityCatalogID)
		count++
		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(URIInteractionRankRevenue, func(input any) (any, error) {
		body, ok := input.(*RankRevenueParams)
		require.True(ok)
		require.NotNil(body)
		assert.Equal(testParams.RoomID, body.RoomID)
		assert.Equal(testParams.UserID, body.UserID)
		assert.Equal(testParams.CreatorID, body.CreatorID)
		assert.Equal(testParams.ActivityCatalogID, body.ActivityCatalogID)
		count++
		return "success", nil
	})
	defer cancel()

	err := testParams.Send(mrpc.UserContext{})
	require.EqualError(err, "rank revenue error message: 参数错误, transaction_id: 1, error: no retry")
	assert.True(errors.Is(err, retry.ErrNoRetry))
	assert.Equal(2, count)
}

func TestActivityDatabusDelayMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testKey := "testkey"
	testMessage := "11b9d5d9bc9b53298ce5a6a6"

	cancel := mrpc.SetMock(URLDatabusDelayMessage, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*ActivityDatabusMessage)
		require.True(ok)
		require.NotNil(body)
		assert.Equal(testKey, body.Key)
		assert.Equal(testMessage, body.Message)
		return "success", nil
	})
	defer cancel()

	body := ActivityDatabusMessage{
		Key:     testKey,
		Message: testMessage,
	}
	require.NoError(ActivityDatabusDelayMessage(body))
}

func TestRankEventParams_Send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := RankEventParams{
		UserID:            9074509,
		RoomID:            112356,
		CreatorID:         9074508,
		GuildID:           11,
		ActivityCatalogID: 147,
		EventType:         RankEventTypeOnline,
		Online: &RankEventOnline{
			QuestID:  "56b9d5d9bc9b53298ce57596",
			Duration: 180,
		},
	}
	called := false
	cancel := mrpc.SetMock(URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*RankEventParams)
		require.True(ok)
		require.NotNil(body)
		assert.Equal(param, *body)
		called = true
		return "success", nil
	})
	defer cancel()
	require.NoError(param.Send(mrpc.UserContext{}))
	assert.True(called)
}
