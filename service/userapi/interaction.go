package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// interaction rpc url
const (
	URLInteractionRoomOnOpen = "fm://live-interaction/room/on-open"  // 同步直播间开播事件
	URLRoomOnClose           = "fm://live-interaction/room/on-close" // 主播关播
)

// SendRoomOnOpenEvent 发送直播间开播事件
func SendRoomOnOpenEvent(ctx mrpc.UserContext, roomID int64, openLogID string) error {
	return service.MRPC.Do(ctx, URLInteractionRoomOnOpen, map[string]any{
		"room_id":     roomID,
		"open_log_id": openLogID,
	}, nil)
}

// RoomOnCloseParam 同步 live-interaction 直播间关播事件
type RoomOnCloseParam struct {
	RoomID    int64  `json:"room_id"`
	OpenLogID string `json:"open_log_id"`
}

// RoomOnClose 同步 live-interaction 直播间关播事件
func RoomOnClose(ctx mrpc.UserContext, param RoomOnCloseParam) error {
	return service.MRPC.Do(ctx, URLRoomOnClose, param, nil)
}
