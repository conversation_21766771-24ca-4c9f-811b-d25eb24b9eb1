package userapi

import (
	"errors"
	"sync"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/retry"
)

// activity rpc url
const (
	URIRankRevenue      = "activity://rank/revenue"
	URIRankEvent        = "activity://rank/event"
	URITaskRankProgress = "activity://taskrank/progress"
	URIRunJob           = "activity://job/run"
)

// interaction rpc url
const (
	URIInteractionRankRevenue = "fm://live-interaction/rank/revenue" // 同步榜单收益
)

// URLDatabusDelayMessage 活动延时队列消息回调
const URLDatabusDelayMessage = "activity://databus/delay/message"

// goods type
const (
	SyncTypeRegisterSuperFan = "register_super_fan" // 开通超粉
	SyncTypeRenewalSuperFan  = "renewal_super_fan"  // 续费超粉
	SyncTypeQuestion         = "question"           // 提问
	SyncTypeDanmaku          = "danmaku"            // 付费弹幕
	SyncTypeReward           = "reward"             // 奖励
	SyncTypeLuckyBox         = "lucky_box"          // 宝盒
	SyncTypeLuckyGashapon    = "gashapon"           // 超能魔方
)

// 事件类型
const (
	RankEventTypeOnline = "online" // 直播间收听时长
)

// RankRevenueParams rank revenue params
type RankRevenueParams struct {
	UserID            int64  `json:"user_id"`
	CreatorID         int64  `json:"creator_id"`
	RoomID            int64  `json:"room_id"`
	TransactionID     int64  `json:"transaction_id"`
	ConfirmTime       int64  `json:"confirm_time"`          // 订单确认时间, 单位: 秒
	OpenLogID         string `json:"open_log_id,omitempty"` // 开播 ID, 关播赠送可能没有开播 ID
	CatalogID         int64  `json:"catalog_id"`
	GuildID           int64  `json:"guild_id"`
	ActivityCatalogID int64  `json:"activity_catalog_id"`

	Gift   *RankRevenueGift   `json:"gift,omitempty"`
	Goods  *RankRevenueGoods  `json:"goods,omitempty"`
	Noble  *RankRevenueNoble  `json:"noble,omitempty"`
	Reward *RankRevenueReward `json:"reward,omitempty"`
}

// RankRevenueGift rank revenue gift
type RankRevenueGift struct {
	GiftID   int64  `json:"gift_id"`
	GiftName string `json:"gift_name"`
	GiftType int    `json:"gift_type"` // 礼物类型
	GiftNum  int    `json:"gift_num"`

	// 单个礼物的价格和数量
	GiftPrice int64          `json:"gift_price"`
	GiftPoint int64          `json:"gift_point"`
	GiftAttr  goutil.BitMask `json:"gift_attr"`

	// 随机礼物专用的
	LuckyGiftID    int64  `json:"lucky_gift_id,omitempty"`
	LuckyGiftName  string `json:"lucky_gift_name,omitempty"`
	LuckyGiftPrice int64  `json:"lucky_gift_price,omitempty"`
	LuckyGiftNum   int    `json:"lucky_gift_num,omitempty"`
}

// RankRevenueGoods rank revenue goods
type RankRevenueGoods struct {
	GoodsID    int64                    `json:"goods_id,omitempty"`
	GoodsType  string                   `json:"goods_type"`
	GoodsPrice int64                    `json:"goods_price"`
	GoodsGifts []*RankRevenueGoodsGifts `json:"goods_gifts,omitempty"`
}

// RankRevenueGoodsGifts rank revenue goods gifts
type RankRevenueGoodsGifts struct {
	GiftID   int64  `json:"gift_id"`
	GiftName string `json:"gift_name"`
	GiftType int    `json:"gift_type"` // 礼物类型
	GiftNum  int    `json:"gift_num"`

	// 单个礼物的价格和数量
	GiftPrice int64          `json:"gift_price"`
	GiftPoint int64          `json:"gift_point"`
	GiftAttr  goutil.BitMask `json:"gift_attr"`
}

// RankRevenueNoble rank revenue noble
type RankRevenueNoble struct {
	IsNew              bool  `json:"is_new"`
	Type               int   `json:"type"`
	NoblePrice         int64 `json:"noble_price"`
	NobleLevel         int   `json:"noble_level"`
	PreviousNobleLevel int   `json:"previous_noble_level,omitempty"`
}

// RankRevenueReward rank revenue reward
type RankRevenueReward struct {
	EventID      int64  `json:"event_id,omitempty"`
	Key          string `json:"key,omitempty"`
	CreatorPoint int64  `json:"creator_point"`
	UserPoint    int64  `json:"user_point"`
}

// Send send rank revenue
func (p *RankRevenueParams) Send(ctx mrpc.UserContext) error {
	var (
		wg    sync.WaitGroup
		errch = make(chan error, 3)
	)

	wg.Add(1)
	goutil.Go(func() {
		defer wg.Done()

		errch <- service.MRPC.Do(ctx, URIRankRevenue, p, nil)
	})

	wg.Add(1)
	goutil.Go(func() {
		defer wg.Done()

		errch <- service.MRPC.Do(ctx, URIInteractionRankRevenue, p, nil)
	})

	wg.Add(1)
	goutil.Go(func() {
		defer wg.Done()

		errch <- retry.Do(func() error {
			return service.BiliAPI.RankRevenue(p.TransactionID, p)
		})
	})

	wg.Wait()
	close(errch)

	var err error
	for e := range errch {
		err = errors.Join(err, e)
	}
	return err
}

// ActivityDatabusMessage 活动消息队列消息
type ActivityDatabusMessage struct {
	Key     string `json:"key"`
	Message string `json:"message"`
}

// ActivityDatabusDelayMessage 活动延时队列消息回调
func ActivityDatabusDelayMessage(a ActivityDatabusMessage) error {
	return service.MRPC.Call(URLDatabusDelayMessage, "", &a, nil)
}

// RankEventParams rank event params
type RankEventParams struct {
	UserID            int64  `json:"user_id"`
	RoomID            int64  `json:"room_id,omitempty"`
	CreatorID         int64  `json:"creator_id,omitempty"`
	GuildID           int64  `json:"guild_id,omitempty"`
	ActivityCatalogID int64  `json:"activity_catalog_id,omitempty"`
	EventType         string `json:"event_type"`

	Event     *RankEventGeneral   `json:"event,omitempty"`
	Online    *RankEventOnline    `json:"online,omitempty"`
	Follow    *RankEventFollow    `json:"follow,omitempty"`
	EnterRoom *RankEventEnterRoom `json:"enter_room,omitempty"`
	BuyGoods  *RankEventBuyGoods  `json:"buy_goods,omitempty"`
}

// RankEventGeneral rank event general
type RankEventGeneral struct {
	Name string `json:"name"`
}

// RankEventOnline rank event online
type RankEventOnline struct {
	QuestID  string `json:"quest_id"`
	Duration int64  `json:"duration"` // 秒
}

// RankEventFollow rank event follow
type RankEventFollow struct {
	UserID       int64 `json:"user_id"`
	FollowUserID int64 `json:"follow_user_id"` // 被关注的用户 ID
}

// RankEventEnterRoom rank event enter_room
type RankEventEnterRoom struct {
	Ts int64 `json:"ts"` // 进房时间戳，单位毫秒
}

// RankEventBuyGoods rank event buy_goods
type RankEventBuyGoods struct {
	GoodsID       int64 `json:"goods_id"`     // 商品 ID
	Type          int   `json:"type"`         // 商品类型 2：福袋（礼包）
	ConfirmTime   int64 `json:"confirm_time"` // 订单确认时间，单位秒
	TransactionID int64 `json:"transaction_id"`
}

// Send send rank event
func (p *RankEventParams) Send(ctx mrpc.UserContext) error {
	var (
		wg    sync.WaitGroup
		errch = make(chan error, 2)
	)

	wg.Add(1)
	goutil.Go(func() {
		defer wg.Done()

		errch <- service.MRPC.Do(ctx, URIRankEvent, p, nil)
	})

	wg.Add(1)
	goutil.Go(func() {
		defer wg.Done()

		errch <- retry.Do(func() error {
			return service.BiliAPI.RankEvent(p)
		})
	})

	wg.Wait()
	close(errch)

	var err error
	for e := range errch {
		err = errors.Join(err, e)
	}
	return err
}
