package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestGetDramaDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expectedDetail := &DramaInfo{
		ID:         28,
		Name:       "椰子",
		Checked:    DramaCheckedPass,
		IPRID:      1,
		IPRName:    "天官赐福",
		CoverURL:   "http://test.com/cover",
		CoverColor: 16777215,
		PayType:    DramaPayTypeDrama,
		Refined:    0,
		Price:      200,
	}
	cancel := mrpc.SetMock(URLGetDramaInfo, func(any) (any, error) {
		return handler.M{"drama": expectedDetail}, nil
	})
	defer cancel()

	detail, err := GetDramaInfo(mrpc.UserContext{}, expectedDetail.ID, 0)
	require.NoError(err)
	require.NotNil(detail)
	assert.Equal(expectedDetail, detail)
}

func TestListUserDramaCVs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expectedDetail := []DramaCVInfo{
		{
			ID:     1,
			UserID: 233,
		},
		{
			ID:     2,
			UserID: 234,
		},
	}
	cancel := mrpc.SetMock(URLGetUserDramaCVs, func(any) (any, error) {
		return handler.M{"cvs": expectedDetail}, nil
	})
	defer cancel()

	detail, err := ListUserDramaCVs(mrpc.UserContext{}, 233, DramaCVSceneLiveRecommend)
	require.NoError(err)
	require.NotNil(detail)
	assert.Equal(expectedDetail, detail)
}
