package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 操作日志分类 ID
// https://info.missevan.com/pages/viewpage.action?pageId=4523227
const (
	CatalogPassRoomInfo   = 86 // 审核通过直播间信息
	CatalogRefuseRoomInfo = 87 // 审核拒绝直播间信息

	PassLiveMedal   = 89 // 审核通过粉丝勋章
	RefuseLiveMedal = 90 // 审核拒绝粉丝勋章

	SetLiveNotice = 96 // 设置直播开播全站通知

	AddGlobalMute    = 104 // 直播间全站禁言惩罚
	RemoveGlobalMute = 105 // 直播间全站禁言解除

	CatalogAddBannedUser = 106 // 直播间封禁用户账号
	CatalogRemoveBanUser = 107 // 直播间解封用户账号

	CatalogDamageVitality = 108 // 直播间扣除主播元气值
	CatalogReturnVitality = 126 // 返还主播元气值

	CatalogCutLive       = 109 // 直播间切断直播
	CatalogBanRoom       = 110 // 直播间封禁直播间
	CatalogUnBanRoom     = 117 // 直播间解封直播间
	CatalogUpdateBanRoom = 163 // 修改直播间封禁时长

	CatalogTopAdd    = 111 // “推荐直播”模块添加推荐
	CatalogTopEdit   = 112 // “推荐直播”模块编辑推荐
	CatalogTopRemove = 113 // “推荐直播”模块删除推荐

	CatalogRecommendedAvatarFrameAdd = 129 // 配置头像框
	CatalogRecommendedAvatarFrameDel = 130 // 删除指定头像框

	CatalogRecommendedOpenListSet = 131 // 设置直播广场推荐直播间
	CatalogRecommendedOpenListDel = 132 // 取消直播广场推荐直播间

	CatalogRecommendedLiveEventAdd = 133 // 添加直播间活动图标
	CatalogRecommendedLiveEventDel = 153 // 删除直播间活动图标

	CatalogRecommendedLiveIconAdd = 134 // 新建直播广场运营配置图标
	CatalogRecommendedLiveIconDel = 135 // 删除直播广场运营配置图标

	CatalogRecommendedLiveRecommendSet   = 136 // 设置推荐位
	CatalogRecommendedLiveRecommendClear = 137 // 清空推荐位

	CatalogSetLoveSet = 138 // 后台设置心动主播月榜

	CatalogGuildPass      = 139 // 通过公会创建
	CatalogGuildReject    = 140 // 拒绝公会创建
	CatalogGuildEditRate  = 141 // 编辑公会分成
	CatalogLiveSetCatalog = 149 // 修改直播间分区

	CatalogUploadToTarget = 154 // 上传文件到指定路径

	CatalogLiveResetInfo = 157 // 重置直播间信息（名称/封面图/背景图）

	CatalogLiveChannelProviderSet = 158 // 设置直播推流提供商

	CatalogCancelNobleRecommend = 164 // 取消神话推荐

	CatalogSetBanner = 165 // 设置直播常驻 banner

	CatalogAdditionalScore = 167 // 管理员额外添加活动分数

	CatalogLiveSetWithdrawalLimit = 168 // 管理直播间月提现额度

	CatalogSetExtraScore = 183 // 设置房间热度

	CatalogEditActivity = 185 // 管理员修改活动

	CatalogRecommendedBackgroundAdd = 191 // 设置直播间推荐背景图
	CatalogRecommendedBackgroundDel = 192 // 取消直播间推荐背景图

	CatalogGiveBackpackItem  = 193 // 发放背包物品
	CatalogClearBackpackItem = 194 // 清空背包物品

	CatalogRecommendedLivePopupAdd = 195 // 添加直播间活动小窗
	CatalogRecommendedLivePopupDel = 196 // 删除直播间活动小窗

	CatalogLivePlaybackPrioritySet = 197 // 设置直播间回放处理优先级
	CatalogPlaybackPrioritySet     = 198 // 设置指定直播回放的处理优先级

	CatalogManageGift = 199 // 管理直播间礼物

	CatalogLiveSetChannel = 200 // 设置直播间在推流

	CatalogManageTag     = 201 // 管理直播间标签
	CatalogChangeRoomTag = 204 // 直播间更新标签

	CatalogManageAppearance = 211 // 管理外观

	CatalogRecommendRoomsBlockListManage = 213 // 直播间推荐资源位黑名单管理

	CatalogDeleteGuild   = 224 // 管理员注销公会
	CatalogUpdateGuild   = 225 // 管理员更新公会
	CatalogTransferGuild = 226 // 管理员转会

	CatalogReviewGuildRecommendImage = 230 // 审核公会资源推荐位图片

	CatalogManageLiveQuestion = 232 // 管理直播间提问

	CatalogManageGuildBanner = 237 // 管理公会 banner 资源位

	CatalogUpdateSquareHotVacancy = 238 // 编辑公会直播广场热门列表推荐位的推荐次数

	CatalogReissueGift = 242 // 直播间补发礼物特效

	CatalogMangeNobleRecommendNum = 246 // 管理神话推荐

	CatalogManageSoundRecommend = 248 // 管理音频播放页直播随机推荐语

	CatalogManageExclusiveCreator = 250 // 管理三方独家主播

	CatalogManageUserLiveMedals = 257 // 管理用户直播粉丝勋章

	CatalogManageCreatorBackpack = 260 // 管理主播背包物品

	CatalogManageUserRank = 265 // 管理主播榜单相关

	CatalogManageLiveShow = 266 // 管理主播个人场相关

	CatalogManageGiftWall = 267 // 管理礼物墙相关
	CatalogManageSpeak    = 269 // 管理直播间发言权限

	CatalogManageRecommendedElements = 274 // 管理直播推荐信息

	CatalogManageRoomConnect = 287 // 管理直播间连麦

	CatalogManageShortcutGift = 297 // 管理直播间快捷送礼

	CatalogManageSticker = 298 // 管理直播间表情包

	CatalogUpdateHotSuppression = 300 // 更新直播间热度限制列表
	CatalogManageNobleHorn      = 303 // 管理贵族喇叭

	CatalogManageRoomMedal = 305 // 管理直播间勋章

	CatalogManageGuildMember = 306 // 管理公会成员
	CatalogManageLuckyBag    = 307 // 管理福袋
	CatalogManageQuests      = 309 // 进房有奖后台管理

	CatalogManageBubble            = 312 // 气泡后台管理
	CatalogManageLiveRecommend     = 314 // 直播底导搜索页配置后台管理
	CatalogManageLiveMute          = 315 // 直播禁言管理
	CatalogManageLiveLuckyGiftDrop = 319 // 随机大奖掉落配置管理
	CatalogManageDailyTask         = 320 // 直播每日任务主播白名单管理

	CatalogManageAlgorithmExposure = 322 // 固 4 推荐算法曝光干预后台管理
)

// AdminLogBox for batching add admin log
type AdminLogBox struct {
	adminLogs []adminlogger.AdminLog
	clientIP  string
	token     string
	equipID   string
	userID    int64
	url       string
}

// NewAdminLogBox initiate AdminLogBox instance
// TODO: 将入参变成 UserContext 的 interface
// DEPRECATED: 使用 goclient.NewAdminLogBox
func NewAdminLogBox(c util.UserContext) *AdminLogBox {
	return &AdminLogBox{
		adminLogs: []adminlogger.AdminLog{},
		clientIP:  c.ClientIP(),
		token:     c.Token(),
		equipID:   c.EquipID(),
		userID:    c.UserID(),
		url:       c.Request().URL.Path,
	}
}

// AddAdminLog add admin to box to add
func (a *AdminLogBox) AddAdminLog(intro string, catalog int) {
	a.AddWithChannelID(catalog, 0, intro)
}

// AddWithChannelID 添加 adminlog (带上 channel_id)
func (a *AdminLogBox) AddWithChannelID(catalog int, channelID int64, intro string) {
	a.adminLogs = append(a.adminLogs, adminlogger.AdminLog{
		UserID:     a.userID,
		Catalog:    catalog,
		ChannelID:  channelID,
		URL:        a.url,
		Intro:      intro,
		IP:         a.clientIP,
		CreateTime: goutil.TimeNow().Unix(),
	})
}

// Send 发送管理员操作日志
func (a *AdminLogBox) Send() (err error) {
	if len(a.adminLogs) > 0 {
		err = service.MRPC.Call(uriGoAddAdminlog, a.clientIP, &a.adminLogs, nil,
			map[string]string{"token": a.token, "equip_id": a.equipID})
	}
	return
}

// SendAdminLogs 发送管理员操作日志
// 以 logs 的第一条数据的 ip 作为客户端 ip
func SendAdminLogs(c util.UserContext, logs ...adminlogger.AdminLog) error {
	cookies := make(map[string]string, 2)
	if c != nil {
		cookies["token"] = c.Token()
		cookies["equip_id"] = c.EquipID()
	}
	return service.MRPC.Call(uriGoAddAdminlog, logs[0].IP, logs, nil, cookies)
}
