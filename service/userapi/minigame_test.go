package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestSendActivityExchange(t *testing.T) {
	assert := assert.New(t)

	var calledNum int
	cleanup := mrpc.SetMock(URLMinigameExchange, func(any) (any, error) {
		calledNum++
		return nil, nil
	})
	defer cleanup()

	err := SendActivityExchange(mrpc.NewUserContextFromEnv(), 1, 566)
	assert.NoError(err)
	assert.Equal(1, calledNum)
}
