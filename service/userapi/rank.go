package userapi

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

// rpc url
const (
	URLRankList = "mrpc://missevan-main/rank/list" // 获取榜单列表
)

// 榜单类型
const (
	RankTypeDramaPopularity = 2 // 人气榜
)

// 榜单子类型
const (
	RankSubTypeWeek = 2 // 周榜
)

// 榜单数据类型
const (
	RankElementTypeDrama = 2 // 剧集
)

// RankListParam 榜单列表
type RankListParam struct {
	Type    int    `json:"type"`              // 榜单类型。1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜
	SubType int    `json:"sub_type"`          // 榜单周期类型。1: 日榜；2: 周榜；3: 月榜
	Bizdate string `json:"bizdate,omitempty"` // 查询榜单的业务日期，YYYY-mm-dd 格式，如：2024-08-07
}

// RankListResp 榜单返回结果
type RankListResp struct {
	RankElementType int               `json:"rank_element_type"`       // 榜单元素类型，1：音频；2：剧集
	RankElementIDs  []int64           `json:"rank_element_ids"`        // 查询的榜单数据，按榜单里的先后顺序排序后的元素 ID（剧集 ID 或音频 ID）
	RankElements    []RankElementItem `json:"rank_elements,omitempty"` // 榜单数据详情，榜单不存在或榜单数据为空时不返回。对榜单元素类型是剧集的返回
	Bizdate         string            `json:"bizdate"`                 // 查询榜单的业务日期，YYYY-mm-dd 格式，如：2024-08-07
}

// RankElementItem 查询的榜单数据
type RankElementItem struct {
	DramaID int64 `json:"drama_id"` // 剧集 ID
	IPRID   int64 `json:"ipr_id"`   // 剧集所属的 IP ID
}

// RankList 获取榜单列表
func RankList(ctx mrpc.UserContext, param RankListParam) (*RankListResp, error) {
	resp := new(RankListResp)
	err := service.MRPC.Do(ctx, URLRankList, param, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
