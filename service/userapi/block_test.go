package userapi

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestAddBlocklist(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URIAddBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return "成功加入黑名单", nil
		})
	defer cleanup()
	err := AddBlocklist(12, 2, "127.0.0.1")
	require.NoError(err)

	mrpc.SetMock(URIAddBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return nil, errors.New("加入黑名单失败")
		})
	err = AddBlocklist(12, 2, "127.0.0.1")
	assert.EqualError(err, "加入黑名单失败")
}

func TestUserBlockList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			switch input.(map[string]any)["user_id"].(int64) {
			case 99:
				return handler.M{"block_list": []int64{1, 2, 3, 4, 5}}, nil
			case -1:
				return nil, actionerrors.ErrParams
			default:
				return handler.M{"block_list": []int64{}}, nil
			}
		})
	defer cleanup()

	userIDs, err := UserBlockList(mrpc.UserContext{}, 99, BlockListTypeBlockedByUser)
	require.NoError(err)
	require.NotNil(userIDs)
	assert.Len(userIDs, 5)

	userIDs, err = UserBlockList(mrpc.UserContext{}, 1, BlockListTypeBlockedByUser)
	require.NoError(err)
	require.NotNil(userIDs)
	assert.Len(userIDs, 0)

	_, err = UserBlockList(mrpc.UserContext{}, -1, BlockListTypeBlockedByUser)
	require.EqualError(err, "参数错误")
}

func TestUserBlockStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		param := i.(map[string][]int64)
		userIDs := param["user_ids"]
		assert.Equal([]int64{10, 100}, userIDs)
		return map[string]interface{}{"block_status": []bool{true, false}}, nil
	})
	defer cleanup()

	u1BlockU2, u2BlockU1, err := UserBlockStatus(10, 100)
	require.NoError(err)
	assert.True(u1BlockU2)
	assert.False(u2BlockU1)

	mrpc.SetMock(URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return nil, handler.ErrInvalidParam
	})
	u1BlockU2, u2BlockU1, err = UserBlockStatus(10, 10)
	assert.False(u1BlockU2)
	assert.False(u2BlockU1)
	assert.Equal(handler.ErrInvalidParam, err)
}

func TestUserBlockStatusList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(URIGoUserBlockStatusList,
		func(input interface{}) (output interface{}, err error) {
			switch input.(map[string]any)["user_id"].(int64) {
			case 99:
				return handler.M{"block_user_list": []int64{1, 2, 3}, "user_block_list": []int64{3, 4, 5}}, nil
			case -1:
				return nil, actionerrors.ErrParams
			default:
				return handler.M{"block_user_list": []int64{}, "user_block_list": []int64{}}, nil
			}
		})
	defer cleanup()

	blockUserList, userBlockList, err := UserBlockStatusList(mrpc.UserContext{}, 99, []int64{1, 2, 3, 4, 5})
	require.NoError(err)
	assert.Len(blockUserList, 3)
	assert.Len(userBlockList, 3)

	blockUserList, userBlockList, err = UserBlockStatusList(mrpc.UserContext{}, 1, []int64{1, 2, 3, 4, 5})
	require.NoError(err)
	assert.Len(blockUserList, 0)
	assert.Len(userBlockList, 0)

	_, _, err = UserBlockStatusList(mrpc.UserContext{}, -1, []int64{})
	require.EqualError(err, "参数错误")
}
