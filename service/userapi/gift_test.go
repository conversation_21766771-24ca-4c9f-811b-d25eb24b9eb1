package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGiftTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(Gift{}, "id", "title", "price", "num", "context")
	kc.Check(Goods{}, "id", "title", "total_price", "num")
	kc.Check(sendGashaponReqParam{}, "from_id", "to_id", "noble", "goods", "gifts", "noble", "live_open_log_id", "user_agent", "equip_id", "buvid")

	kc.Check(sendLuckyGiftReqParam{}, "from_id", "to_id", "gift_id", "price",
		"num", "title", "income", "noble", "live_open_log_id", "user_agent", "equip_id", "buvid", "more")

	kc.Check(buyFukubukuroParam{}, "from_id", "gift_id", "price", "title", "noble",
		"user_agent", "equip_id", "buvid")
	kc.Check(sendRebateGiftsParam{}, "from_id", "to_id", "gifts", "live_open_log_id",
		"user_agent", "equip_id", "buvid", "ip")
	kc.Check(BuyLiveGoodsParam{}, "buyer_id", "receiver_id", "goods_type", "goods", "package_info", "noble",
		"user_agent", "equip_id", "buvid", "ip", "live_open_log_id")
	kc.CheckOmitEmpty(BuyLiveGoodsParam{}, "package_info", "live_open_log_id")
	kc.Check(LiveGoodsElem{}, "id", "title", "price", "num", "transaction_type")
	kc.Check(PackageInfo{}, "id", "title", "price", "num")

	kc.Check(RefundGoodsParam{}, "transaction_id", "goods_type", "goods")
	kc.Check(RefundGoodsElem{}, "id", "transaction_type", "price", "title", "num")
}

func TestSendGashaponGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URISendGashaponGift, func(interface{}) (interface{}, error) {
		return BalanceResp{TransactionID: 1, Balance: 1}, nil
	})
	defer cancel()
	oid := primitive.NewObjectID()
	resp, err := SendGashaponGift(1, 2, Goods{}, []Gift{}, true,
		&oid, mrpc.UserContext{})
	require.NoError(err)
	assert.NotNil(resp)
}

func TestSendLuckyGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URISendLuckyGift, func(input interface{}) (interface{}, error) {
		var param sendLuckyGiftReqParam
		require.IsType(param, input)
		param = input.(sendLuckyGiftReqParam)
		assert.Equal(int64(1), param.FromID)
		assert.Equal(int64(2), param.ToID)
		assert.Equal(int64(3), param.GiftID)
		assert.Equal(int64(4), param.Price)
		assert.Equal(5, param.Num)
		assert.Equal("gift（draw）", param.Title)
		assert.Equal(int64(60), param.Income)
		assert.Equal(1, param.Noble)
		assert.Equal(1, param.More.From)
		assert.Equal(int64(7), param.More.ReceiveGiftID)
		return BalanceResp{Balance: 1}, nil
	})
	defer cancel()

	oid := primitive.NewObjectID()
	drawSendGift := &Gift{
		ID:    3,
		Title: "draw",
		Price: 4,
		Num:   5,
	}
	drawReceiveGift := &Gift{
		ID:    7,
		Title: "gift",
		Price: 6,
		Num:   1,
	}
	resp, err := SendLuckyGift(1, 2, drawSendGift, drawReceiveGift, true,
		&oid, 1, NewUserContext(nil))
	require.NoError(err)
	require.NotNil(resp)
	assert.NotZero(*resp)
}

func TestSendRebateGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	hex := "62a187de7f97c3d9d5fcefad"
	oid, err := primitive.ObjectIDFromHex(hex)
	require.NoError(err)
	cancel := mrpc.SetMock(URISendRebateGift, func(input interface{}) (interface{}, error) {
		assert.Equal(map[string]interface{}{
			"from_id":          int64(123),
			"to_id":            int64(123),
			"gift_id":          int64(123),
			"num":              123,
			"live_open_log_id": hex,
			"user_agent":       "",
			"equip_id":         "",
			"buvid":            "",
		}, input)
		return BalanceResp{
			TransactionID:    123,
			Balance:          123,
			LiveNobleBalance: 123,
			Price:            123,
		}, nil
	})
	defer cancel()

	r, err := SendRebateGift(123, 123, 123, 123, &oid, NewUserContext(nil))
	require.NoError(err)
	assert.Equal(&BalanceResp{
		TransactionID:    123,
		Balance:          123,
		LiveNobleBalance: 123,
		Price:            123,
	}, r)

	// test nil
	mrpc.SetMock(URISendRebateGift, func(input interface{}) (interface{}, error) {
		assert.Equal(map[string]interface{}{
			"from_id":    int64(123),
			"to_id":      int64(123),
			"gift_id":    int64(123),
			"num":        123,
			"user_agent": "test_ua",
			"equip_id":   "test_equip_id",
			"buvid":      "test_buvid",
		}, input)
		return BalanceResp{
			TransactionID:    123,
			Balance:          123,
			LiveNobleBalance: 123,
			Price:            123,
		}, nil
	})
	_, err = SendRebateGift(123, 123, 123, 123, nil, UserContext{
		UserAgent: "test_ua",
		EquipID:   "test_equip_id",
		BUVID:     "test_buvid",
	})
	require.NoError(err)
}

func TestBuyFukubukuro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URIBuyFukubukuro, func(input interface{}) (interface{}, error) {
		ok = true
		assert.Equal(buyFukubukuroParam{
			FromID: 12,
			GiftID: 401,
			Price:  100,
			Title:  "福袋",
			Noble:  1,
		}, input)
		return BalanceResp{}, nil
	})
	defer cancel()

	_, err := BuyFukubukuro(12, 401, "福袋", 100, 1, NewUserContext(nil))
	require.NoError(err)
	assert.True(ok)
}

func TestSendRebateGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	hex := "62a188d178a64dfb347b8254"
	oid, err := primitive.ObjectIDFromHex(hex)
	require.NoError(err)
	ok := false
	gifts := []Gift{
		{ID: 401, Title: "福袋礼物", Price: 1, Num: 3, Context: "context"},
	}
	cancel := mrpc.SetMock(URISendRebateGifts, func(input interface{}) (interface{}, error) {
		ok = true
		assert.Equal(sendRebateGiftsParam{
			FromID:        12,
			ToID:          10,
			Gifts:         gifts,
			UserAgent:     "test-ua",
			IP:            "127.0.0.1",
			LiveOpenLogID: hex,
		}, input)
		return BalanceResp{}, nil
	})
	defer cancel()

	_, err = SendRebateGifts(12, 10, gifts, &oid, UserContext{
		ClientIP:  "127.0.0.1",
		UserAgent: "test-ua",
	})
	require.NoError(err)
	assert.True(ok)
}

func TestSendGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URISendGift, func(i interface{}) (interface{}, error) {
		tutil.PrintJSON(i)
		return BalanceResp{TransactionID: 1, Balance: 1}, nil
	})
	defer cancel()

	oid := primitive.NewObjectID()
	resp, err := SendGift(1, 2, Gift{ID: 3, Num: 4}, true,
		&oid, UserContext{})
	require.NoError(err)
	assert.NotNil(resp)
}

func TestSendRewardGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &BalanceResp{
		TransactionID:    123,
		Balance:          123,
		LiveNobleBalance: 123,
		Price:            123,
	}
	cancel := mrpc.SetMock(URISendGift, func(input interface{}) (interface{}, error) {
		return resp, nil
	})
	defer cancel()

	r, err := SendRewardGift(123, 123, 123, 123, nil)
	require.NoError(err)
	assert.Equal(resp, r)
}

func TestBuyWishGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URIBuyWishGoods, func(input interface{}) (interface{}, error) {
		ok = true
		assert.Equal(BuyWishGoodsParam{
			FromID: 12,
			GiftID: 233,
			Price:  2,
			Title:  "许愿",
			Num:    2,
			Noble:  1,
		}, input)
		return BalanceResp{}, nil
	})
	defer cancel()

	_, err := BuyWishGoods(12, 233, "许愿", 1, 2, 1, NewUserContext(nil))
	require.NoError(err)
	assert.True(ok)
}

func TestBuyLiveGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	c := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := BuyLiveGoodsParam{
		BuyerID:    12,
		ReceiverID: 13,
		GoodsType:  GoodsTypeRedPacket,
		Goods: []LiveGoodsElem{
			{
				ID:    111,
				Price: 1000,
				Title: "礼物红包",
				Num:   1,
			},
		},
		Noble:     0,
		UserAgent: c.UserAgent,
		IP:        c.IP,
	}
	cancel := mrpc.SetMock(URIBuyGoods, func(input interface{}) (interface{}, error) {
		ok = true
		assert.Equal(param, input)
		return BalanceResp{}, nil
	})
	defer cancel()

	_, err := BuyLiveGoods(c, param, nil)
	require.NoError(err)
	assert.True(ok)
}

func TestBuyLiveGoodsParam_setOpenLogID(t *testing.T) {
	assert := assert.New(t)

	param := BuyLiveGoodsParam{
		BuyerID:    12,
		ReceiverID: 13,
		GoodsType:  GoodsTypeRedPacket,
		Goods: []LiveGoodsElem{
			{
				ID:    111,
				Price: 1000,
				Title: "礼物红包",
				Num:   1,
			},
		},
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
		Noble:     0,
	}

	openLogID := primitive.NewObjectID()
	param.setOpenLogID(&openLogID)
	assert.Equal(openLogID.Hex(), param.LiveOpenLogID)
}

func TestRefundGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	c := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := RefundGoodsParam{
		TransactionID: 1,
		Goods: []*RefundGoodsElem{
			{
				ID:              111,
				TransactionType: TransactionTypeDrama,
				Price:           1000,
				Title:           "福袋",
				Num:             1,
			},
		},
	}
	cancel := mrpc.SetMock(URILiveRefundGoods, func(input any) (any, error) {
		ok = true
		assert.Equal(param, input)
		return BalanceResp{}, nil
	})
	defer cancel()

	_, err := RefundGoods(c, param)
	require.NoError(err)
	assert.True(ok)
}
