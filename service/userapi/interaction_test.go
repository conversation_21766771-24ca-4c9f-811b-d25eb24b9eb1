package userapi

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestSendRoomOnOpenEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	isCalled := false
	cancel := mrpc.SetMock(URLInteractionRoomOnOpen,
		func(any) (any, error) {
			isCalled = true
			return "success", nil
		})
	defer cancel()

	require.NoError(SendRoomOnOpenEvent(mrpc.NewUserContextFromEnv(), 9074509, "6456341e86b772e2fb17d166"))
	assert.True(isCalled)
}

func TestRoomOnClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(URLRoomOnClose, func(any) (output interface{}, err error) {
		ok = true
		return "success", nil
	})
	defer cancel()

	param := RoomOnCloseParam{
		RoomID:    1,
		OpenLogID: "abc",
	}
	err := RoomOnClose(mrpc.UserContext{}, param)
	require.NoError(err)
	assert.True(ok)
}
