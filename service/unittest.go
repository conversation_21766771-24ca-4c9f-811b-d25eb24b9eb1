//go:build !release
// +build !release

package service

import (
	"log"
	"path"
	"runtime"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/missevan-go/logger"
	goservice "github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// InitTest init for test
func InitTest(new ...bool) {
	newInitTest := len(new) != 0 && new[0]
	if newInitTest {
		config.InitTest()
	}
	// 打印日志来辅助判断单元测试卡在哪一步
	tutil.Debug("start init service")
	err := Init(&config.Conf.Service)
	if err != nil {
		log.Print(err)
		// WORKAROUND: go test 第一次连 redis 偶尔会超时，重试一次
		err = Init(&config.Conf.Service)
		if err != nil {
			log.Fatal(err)
		}
	}

	// DatabusPub 改成测试的
	DatabusPub = databus.TestNew(&config.Conf.Service.Databus.Pub)
	DatabusDelayPub = databus.TestNew(&config.Conf.Service.DatabusDelay.Pub)
	DatabusLogPub = databus.TestNew(&config.Conf.Service.DatabusLog.Pub)

	if newInitTest {
		mockDB()
	}
	tutil.Debug("finish init service")
	mrpc.SetMock("go://util/addadminlog",
		func(i interface{}) (interface{}, error) {
			logger.Debugf("adminlogs: %s", tutil.SprintJSON(i))
			return "success", nil
		})
	tutil.Debug("go://util/addadminlog mocked")
}

func mockDB() {
	// db 放在每个单元测试的包目录下
	_, file, _, _ := runtime.Caller(2)
	dbPath := path.Dir(file)
	logger.Debugf("db path: %s", dbPath)

	// 获取 query 所在文件夹
	_, file, _, _ = runtime.Caller(0)
	queryPath := path.Clean(path.Dir(file) + "/../testdata")
	logger.Debugf("query path: %s", queryPath)

	DB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPath + "/test.db",
		QueryFile: queryPath + "/test.sql",
	}, DB)

	LiveDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPath + "/missevan_live.db",
		QueryFile: queryPath + "/missevan_live.sql",
	}, LiveDB)

	PayDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPath + "/missevan_pay.db",
		QueryFile: queryPath + "/missevan_pay.sql",
	}, PayDB)

	NewADB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPath + "/new_adb.db",
		QueryFile: queryPath + "/new_adb.sql",
	}, NewADB)
	LogDB = tutil.NewSqlite(&tutil.ConfigSqlite{
		DBFile:    dbPath + "/app_missevan_log.db",
		QueryFile: queryPath + "/app_missevan_log.sql",
	}, LogDB)

	goservice.DB = DB
	goservice.PayDB = PayDB
	servicedb.Driver = servicedb.DriverSqlite
}

// SetDBUseSQLite 设置 DB 为 sqlite 数据库
// Deprecated: 使用 service.InitTest(true)
func SetDBUseSQLite() {
	mockDB()
}

// InitTestDatabusSub 初始化测试 DatabusSub
func InitTestDatabusSub() {
	DatabusSub = databus.TestNew(&config.Conf.Service.Databus.Sub)
	DatabusDelaySub = databus.TestNew(&config.Conf.Service.DatabusDelay.Sub)
}
