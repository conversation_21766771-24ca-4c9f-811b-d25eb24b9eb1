package tencentcos

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/tencentyun/cos-go-sdk-v5"
)

// TODO: 迁移到 missevan-go storage

// SectionTencentCOS 腾讯云 cos
type SectionTencentCOS struct {
	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
	BucketURL       string `yaml:"bucket_url"`
}

// Client TencentCOSClient
type Client struct {
	*cos.Client
	Conf *SectionTencentCOS
}

// NewCOSClient new cos client
func NewCOSClient(conf *SectionTencentCOS) (*Client, error) {
	u, err := url.Parse(conf.BucketURL)
	if err != nil {
		return nil, err
	}
	b := &cos.BaseURL{BucketURL: u}

	client := &Client{cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  conf.AccessKeyID,
			SecretKey: conf.AccessKeySecret,
		},
	}),
		conf,
	}

	return client, nil
}

// GetPresignedURL 腾讯对象存储预签名
// url 示例 http://liverecord-1252693259.cos0.ap-shanghai.myqcloud.com/liverecord/34d993a83701925925094866421/f0.flv
func (c *Client) GetPresignedURL(url string, expire time.Duration) (res string, err error) {
	if url == "" {
		return "", fmt.Errorf("empty url")
	}
	if !strings.HasPrefix(url, c.Conf.BucketURL) {
		return url, nil
	}
	ctx := context.Background()
	presignedURL, err := c.Object.GetPresignedURL(
		ctx,
		http.MethodGet,
		strings.TrimPrefix(url, c.Conf.BucketURL), // 示例 liverecord/34d993a83701925925094866421/f0.flv
		c.Conf.AccessKeyID,
		c.Conf.AccessKeySecret,
		expire,
		nil,
	)
	if err != nil {
		return "", err
	}
	// TODO: 迁移后自己处理相关的签名逻辑
	// http://liverecord-1252693259.cos.ap-shanghai.myqcloud.com/liverecord%2F2aa38a0d8602268010726784975%2Ff0.flv?
	// q-sign-algorithm=sha1&q-ak=test&q-sign-time=1634728809%3B1634728819&q-key-time=1634728809%3B1634728819&q-header-list=host&q-url-param-list=&q-signature=82d894db941dc4c1d6f3cf80ecbbd2006107571e
	return presignedURL.String(), nil
}
