package tencentcos

import (
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(SectionTencentCOS{}, "access_key_id", "access_key_secret", "bucket_url")
}

func TestGetPresignedURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	c, err := NewCOSClient(
		&SectionTencentCOS{
			AccessKeyID:     "test",
			AccessKeySecret: "xxxx",
			BucketURL:       "http://liverecord-1252693259.cos.ap-shanghai.myqcloud.com/",
		})
	require.NoError(err)
	url := "http://liverecord-1252693259.cos.ap-shanghai.myqcloud.com/liverecord/2aa38a0d8602268010726784975/f0.flv"
	r, err := c.GetPresignedURL(url, 10*time.Second)
	require.NoError(err)
	assert.NotEqual(url, r)
	assert.True(strings.HasPrefix(r, c.Conf.BucketURL))
	assert.Contains(r, "q-signature")

	url = "http://1252693259.vod2.myqcloud.com/liverecord/2aa38a0d8602268010726784975/f0.flv"
	r, err = c.GetPresignedURL(url, 10*time.Second)
	require.NoError(err)
	assert.Equal(url, r)
}
