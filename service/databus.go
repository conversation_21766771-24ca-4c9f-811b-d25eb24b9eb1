package service

import (
	"context"
	"time"

	"github.com/MiaoSiLa/missevan-go/service/databus"
)

const defaultTimeout = 5 * time.Second

// databus
var (
	DatabusPub *databus.Databus
	DatabusSub *databus.Databus

	DatabusDelayPub *databus.Databus
	DatabusDelaySub *databus.Databus

	DatabusLogPub *databus.Databus
)

// DatabusSend 发送 databus 普通消息
func DatabusSend(key string, v interface{}, ctx ...context.Context) error {
	var c context.Context
	if len(ctx) != 0 {
		c = ctx[0]
	} else {
		var cancel func()
		c, cancel = context.WithTimeout(context.Background(), defaultTimeout)
		defer cancel()
	}
	return DatabusPub.Send(c, key, v)
}

// DatabusSendDelay 发送 databus 延时消息
func DatabusSendDelay(key string, v interface{}, deliverAt time.Time,
	ctx ...context.Context) error {
	var c context.Context
	if len(ctx) != 0 {
		c = ctx[0]
	} else {
		var cancel func()
		c, cancel = context.WithTimeout(context.Background(), defaultTimeout)
		defer cancel()
	}
	return DatabusDelayPub.SendDelay(c, key, v, deliverAt)
}

// DatabusDelayPubSend 使用 DatabusDelayPub 发送 databus 普通消息
func DatabusDelayPubSend(key string, v interface{}, ctx ...context.Context) error {
	var c context.Context
	if len(ctx) != 0 {
		c = ctx[0]
	} else {
		var cancel func()
		c, cancel = context.WithTimeout(context.Background(), defaultTimeout)
		defer cancel()
	}
	return DatabusDelayPub.Send(c, key, v)
}

// DatabusLogSend 使用 DatabusLogPub 发送 databus 普通消息
func DatabusLogSend(key string, v interface{}, ctx ...context.Context) error {
	var c context.Context
	if len(ctx) != 0 {
		c = ctx[0]
	} else {
		var cancel func()
		c, cancel = context.WithTimeout(context.Background(), defaultTimeout)
		defer cancel()
	}
	return DatabusLogPub.Send(c, key, v)
}
