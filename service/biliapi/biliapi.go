package biliapi

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	serviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// BaseResp 回调响应
type BaseResp struct {
	Code    int             `json:"code"`
	Message string          `json:"message"`
	Data    json.RawMessage `json:"data"`
}

// NewClient new client
func NewClient(conf Config) *Client {
	return &Client{
		Config: conf,
		c: http.Client{
			Timeout: 5 * time.Second,
		},
	}
}

// Client .
type Client struct {
	Config
	c http.Client
}

// Config .
type Config struct {
	URL       string `yaml:"url"`
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
	Enable    bool   `yaml:"enable"`
}

// SignParams sign params
func (conf Config) SignParams() (string, string) {
	return conf.AppKey, conf.AppSecret
}

func (c *Client) post(uri string, req, resp any) error {
	var (
		rawData []byte
		err     error
	)
	switch v := req.(type) {
	case json.Marshaler:
		rawData, err = v.MarshalJSON()
	default:
		rawData, err = json.Marshal(req)
	}
	if err != nil {
		return err
	}

	url := fmt.Sprintf("%s%s?%s", c.URL, uri, blademaster.Sign(c.Config, url.Values{}))
	logger.Debugf("POST %s", url)

	request, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(rawData))
	if err != nil {
		return fmt.Errorf("biliapi %s new request error: %w", url, err)
	}

	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("User-Agent", serviceutil.UserAgent)

	response, err := c.c.Do(request)
	if err != nil {
		return fmt.Errorf("biliapi %s do request error: %w", url, err)
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return fmt.Errorf("biliapi %s read body error: %w", url, err)
	}

	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("biliapi %s response status code: %d, body: %s",
			url, response.StatusCode, goutil.TruncateResponseBodyWithNotice(string(body)))
	}

	logger.Debugf("biliapi %s response body: %s", url, body)

	err = json.Unmarshal(body, resp)
	if err != nil {
		return fmt.Errorf("biliapi %s unmarshal error: %w, body: %s",
			url, err, goutil.TruncateResponseBodyWithNotice(string(body)))
	}

	return nil
}
