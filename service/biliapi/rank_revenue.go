package biliapi

import (
	"fmt"

	"github.com/MiaoSiLa/missevan-go/util/retry"
)

// RankRevenue 榜单收益回调
func (c *Client) RankRevenue(transactionID int64, req any) error {
	if !c.Config.Enable {
		return nil
	}

	var resp BaseResp
	err := c.post(URIBiliAPIRankRevenue, req, &resp)
	if err != nil {
		return err
	}

	// 如果 code 是 150001（数据已经存在或者数据不合法），则不重试
	if resp.Code == 150001 {
		return fmt.Errorf("rank revenue error message: %s, transaction_id: %d, error: %w", resp.Message, transactionID, retry.ErrNoRetry)
	}

	return nil
}
