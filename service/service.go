package service

import (
	"math/rand"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"
	"github.com/patrickmn/go-cache"
	"github.com/prometheus/client_golang/prometheus"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service/biliapi"
	"github.com/MiaoSiLa/live-service/service/cdn/aliyun"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/live-service/service/cdn/ksyun"
	"github.com/MiaoSiLa/live-service/service/cdn/netease"
	"github.com/MiaoSiLa/live-service/service/netease/vod"
	"github.com/MiaoSiLa/live-service/service/tencentcos"
	"github.com/MiaoSiLa/live-service/service/upos"
	"github.com/MiaoSiLa/missevan-go/logger"
	goservice "github.com/MiaoSiLa/missevan-go/service"
	"github.com/MiaoSiLa/missevan-go/service/captcha"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/upload"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// service vars
var (
	DB     *gorm.DB
	LiveDB *gorm.DB
	PayDB  *gorm.DB
	NewADB *gorm.DB
	LogDB  *gorm.DB

	MongoDB *mongodb.MongoDatabase

	Redis    *redis.Client
	IMRedis  *redis.Client
	LRURedis *redis.Client

	TencentCOS *tencentcos.Client

	// storage
	Storage *storage.Client
	Upos    *upos.Client
	Vod     *vod.Client

	// rpc client
	MRPC        *mrpc.Client
	PushService *pushservice.Client
	SSO         *sso.Client

	// cdn
	NeteaseLive *netease.Client
	AliyunLive  *aliyun.Client
	KsyunLive   *ksyun.Client
	BvcLive     *bvc.Client
	BiliLive    *bililive.Client
	BiliAPI     *biliapi.Client

	// others
	Captcha *captcha.Client
	Upload  *upload.Client
)

// 缓存
var (
	Cache5s   = cache.New(5*time.Second, 10*time.Second)
	Cache10s  = cache.New(10*time.Second, 20*time.Second)
	Cache5Min = cache.New(5*time.Minute, 10*time.Minute)
)

// PromRegistry prometheus registry
var PromRegistry = prometheus.NewRegistry()

// Init services
func Init(conf *config.SectionService) (err error) {
	DB, err = servicedb.InitDatabase(&conf.DB)
	if err != nil {
		logger.Error(err)
		return
	}
	LiveDB, err = servicedb.InitDatabase(&conf.LiveDB)
	if err != nil {
		logger.Error(err)
		return
	}
	PayDB, err = servicedb.InitDatabase(&conf.PayDB)
	if err != nil {
		logger.Error(err)
		return
	}
	NewADB, err = servicedb.InitDatabase(&conf.NewADB)
	if err != nil {
		logger.Error(err)
		return
	}
	LogDB, err = servicedb.InitDatabase(&conf.LogDB)
	if err != nil {
		logger.Error(err)
		return
	}

	MongoDB, err = mongodb.NewMongoDB(&conf.MongoDB)
	if err != nil {
		logger.Error(err)
		return
	}

	Redis, err = serviceredis.NewRedisClient(&conf.Redis)
	if err != nil {
		logger.Error(err)
		return
	}
	IMRedis, err = serviceredis.NewRedisClient(&conf.IMRedis)
	if err != nil {
		logger.Error(err)
		return
	}
	LRURedis, err = serviceredis.NewRedisClient(&conf.LRURedis)
	if err != nil {
		logger.Error(err)
		return
	}

	// 默认只初始化 DatabusPub
	// TODO: 并非所有模式都使用了 DatabusPub
	DatabusPub = databus.New(&conf.Databus.Pub)
	DatabusDelayPub = databus.New(&conf.DatabusDelay.Pub)
	DatabusLogPub = databus.New(&conf.DatabusLog.Pub)

	TencentCOS, err = tencentcos.NewCOSClient(&conf.TencentCOS)
	if err != nil {
		logger.Error(err)
		return
	}

	Storage = storage.NewClient(conf.Storage)
	Vod = vod.NewVodClient(&conf.Vod)

	MRPC, err = mrpc.NewClient(conf.MRPC)
	if err != nil {
		logger.Error(err)
		return
	}
	PushService, err = pushservice.NewPushServiceClient(&conf.PushService)
	if err != nil {
		logger.Error(err)
		return
	}
	SSO, err = sso.NewClient(conf.MRPC)
	if err != nil {
		logger.Error(err)
		return
	}

	NeteaseLive = netease.NewClient(conf.NeteaseLive)
	AliyunLive = aliyun.NewClient(conf.AliyunLive)
	KsyunLive = ksyun.NewClient(conf.KsyunLive)
	BvcLive = bvc.NewClient(conf.BvcLive)
	BiliLive = bililive.NewClient(conf.BiliLive)
	BiliAPI = biliapi.NewClient(conf.BiliAPI)

	Captcha, err = captcha.NewClient(conf.Captcha)
	if err != nil {
		logger.Error(err)
		return
	}
	Upload, err = upload.NewClient(conf.Upload, Storage)
	if err != nil {
		logger.Error(err)
		return
	}

	AfterInit()
	return nil
}

// AfterInit 初始化完成后的一些赋值操作
func AfterInit() {
	goservice.DB = DB
	goservice.PayDB = PayDB
	goservice.Redis = Redis
	goservice.LRURedis = LRURedis
	goservice.Storage = Storage
	goservice.MRPC = MRPC
	goservice.SSO = SSO
	goservice.PushService = PushService

	rand.Seed(goutil.TimeNow().UnixNano())
}
