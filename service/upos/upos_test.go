package upos

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	m.Run()
}

func TestNewClient(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := Config{}
	client := NewClient(config)
	require.NotNil(client)
	// 有默认重试次数
	assert.NotZero(client.config.Retry)

	config.Retry = 5
	client = NewClient(config)
	require.NotNil(client)
	assert.Equal(5, client.config.Retry)
}

func TestPreupload(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config := Config{
		PreuploadEndpoint: "http://uat-member.bilibili.com",

		Bucket:  "mefmboss",
		Profile: "mefm/upclone",
		Token:   "ba1bea1e70329dc72c53bc43424920f9",
	}
	client := NewClient(config)
	require.NotNil(client)

	preupload, err := client.preupload("test.mp4", "upos://mefmboss/test/test.mp4", 0)
	require.NoError(err)
	assert.Equal(1, preupload.OK)

	endpoint, err := client.startUpload(preupload)
	require.NoError(err)
	assert.NotEmpty(endpoint.UploadID)

	/*f, err := os.Open("test.mp4")
	require.NoError(err)
	defer f.Close()
	err = client.doUpload(preupload, endpoint, f)
	require.NoError(err)

	finish, err := client.finishUpload(preupload, endpoint)
	require.NoError(err)
	assert.Equal(1, finish.OK)*/
}
