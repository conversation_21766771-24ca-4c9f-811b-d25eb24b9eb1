package upos

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/MiaoSiLa/live-service/util/ratelimit"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Config for upos client
type Config struct {
	PreuploadEndpoint string `yaml:"preupload_endpoint"`

	Bucket  string `yaml:"bucket"`
	Profile string `yaml:"profile"`
	Token   string `yaml:"token"`

	Retry int `yaml:"retry"`
	// TODO: upcdn=office
}

// Client for upos
type Client struct {
	config Config

	httpClient *http.Client
	userAgent  string
	limiter    ratelimit.LimitReaderFactory
}

type preuploadResp struct {
	OK        int      `json:"ok"`
	Info      string   `json:"info,omitempty"`
	Auth      string   `json:"auth"`
	Endpoints []string `json:"endpoints"`
	// TODO: chunk_retry, chunk_retry_delay
	ChunkSize int64  `json:"chunk_size"`
	Timeout   int    `json:"timeout"`
	UposURI   string `json:"upos_uri"`

	requestSize int64
	uploadSize  int64
}

type endpointResp struct {
	OK       int    `json:"OK"`
	Bucket   string `json:"bucket"`
	Key      string `json:"key"`
	UploadID string `json:"upload_id"`

	endpoint string
	path     string

	uploadHTTPClient *http.Client
}

type finishResp struct {
	OK       int    `json:"OK"`
	Bucket   string `json:"bucket"`
	Key      string `json:"key"`
	Location string `json:"location"`
	Etag     string `json:"etag"` // 这里的 etag 是文件大小

	fileSize int64
}

type chunk struct {
	part  int
	data  []byte
	md5   string
	retry int
	err   error
}

// UploadResult upos upload result
type UploadResult struct {
	Size    int64
	UposURI string
}

// NewClient creates new upos client
func NewClient(config Config) *Client {
	if config.Retry == 0 {
		// 默认重试 2 次
		config.Retry = 2
	}
	client := &Client{
		config:     config,
		httpClient: &http.Client{Timeout: 5 * time.Second},
		userAgent:  "maoer-upos-client",
	}
	return client
}

// SetLimitReaderFactory sets limit reader factory to this client
func (c *Client) SetLimitReaderFactory(l ratelimit.LimitReaderFactory) {
	c.limiter = l
}

// Upload data to upos
// TODO: support content-type in optional params
func (c *Client) Upload(name, path string, reader io.Reader, size int64) (*UploadResult, error) {
	uposURI := "upos://" + c.config.Bucket + "/" + path
	preupload, err := c.preupload(name, uposURI, size)
	if err != nil {
		return nil, err
	}

	endpoint, err := c.startUpload(preupload)
	if err != nil {
		return nil, err
	}

	err = c.doUpload(preupload, endpoint, reader)
	if err != nil {
		return nil, err
	}

	finish, err := c.finishUpload(preupload, endpoint)
	if err != nil {
		return nil, err
	}

	return &UploadResult{
		UposURI: preupload.UposURI,
		Size:    finish.fileSize,
	}, nil
}

func (c *Client) preupload(name, uposURI string, size int64) (*preuploadResp, error) {
	params := url.Values{}
	params.Set("name", name)
	params.Set("profile", c.config.Profile)
	params.Set("r", "upos")
	if size > 0 {
		params.Set("size", strconv.FormatInt(size, 10))
	}
	params.Set("upos_uri", uposURI)

	url := c.config.PreuploadEndpoint + "/preupload?" + params.Encode()
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Cookie", c.config.Token)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var r preuploadResp

	if resp.StatusCode != http.StatusOK {
		if bytes.HasPrefix(body, []byte("Param error:")) {
			return nil, errors.New(string(body))
		}
		err = json.Unmarshal(body, &r)
		if err == nil && r.Info != "" {
			return nil, errors.New(r.Info)
		}
		if len(body) <= 50 {
			return nil, errors.New(string(body))
		}
		return nil, fmt.Errorf("http status %d", resp.StatusCode)
	}

	err = json.Unmarshal(body, &r)
	if err != nil {
		return nil, err
	}
	if r.Auth == "" {
		return nil, errors.New("no auth info")
	}
	r.requestSize = size
	return &r, nil
}

func (c *Client) tryUploadEndpoint(endpoint, path, auth string) (*endpointResp, error) {
	url := fmt.Sprintf("http:%s/%s?output=json&uploads", endpoint, path)
	req, err := http.NewRequest(http.MethodPost, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("X-Upos-Auth", auth)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status %d", resp.StatusCode)
	}

	var r endpointResp
	err = json.Unmarshal(body, &r)
	if err != nil {
		return nil, err
	}
	if r.UploadID == "" {
		return nil, errors.New("no upload id info")
	}

	r.endpoint = endpoint
	r.path = path
	return &r, nil
}

func (c *Client) tryUploadChunk(ctx context.Context, auth string, endpoint *endpointResp, chunk *chunk) {
	url := fmt.Sprintf("http:%s/%s?partNumber=%d&uploadId=%s",
		endpoint.endpoint, endpoint.path, chunk.part, endpoint.UploadID)
	var r io.Reader
	r = bytes.NewBuffer(chunk.data)
	if c.limiter != nil {
		r = c.limiter.NewReader(r)
	}
	req, err := http.NewRequest(http.MethodPut, url, r)
	if err != nil {
		chunk.err = err
		return
	}
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("X-Upos-Auth", auth)
	req = req.WithContext(ctx)
	resp, err := endpoint.uploadHTTPClient.Do(req)
	if err != nil {
		chunk.err = err
		return
	}
	defer resp.Body.Close()
	_, err = io.ReadAll(resp.Body)
	if err != nil {
		chunk.err = err
		return
	}
	if resp.StatusCode != http.StatusOK {
		chunk.err = fmt.Errorf("http status %d", resp.StatusCode)
		return
	}
	etag := resp.Header.Get("Etag")
	logExtraMsg := ""
	if etag == "" {
		logExtraMsg = " (empty)"
	} else if etag != chunk.md5 {
		logExtraMsg = " != " + etag
	}
	logger.WithField("upload_id", endpoint.UploadID).
		Debugf("upload chunk %d done, md5: %s%s", chunk.part, chunk.md5, logExtraMsg)
	if etag != "" && etag != chunk.md5 {
		chunk.err = errors.New("md5 mismatch")
		return
	}
}

func (c *Client) startUpload(preupload *preuploadResp) (*endpointResp, error) {
	parts := strings.SplitN(preupload.UposURI, "://", 2)
	if len(parts) != 2 {
		return nil, errors.New("upos uri error")
	}
	uriPath := parts[1]
	var lastErr error
	for _, endpoint := range preupload.Endpoints {
		r, err := c.tryUploadEndpoint(endpoint, uriPath, preupload.Auth)
		if err != nil {
			logger.Errorf("try upload endpoint %q error: %v", endpoint, err)
			lastErr = err
			continue
		}
		timeout := 1200
		if preupload.Timeout > 0 {
			timeout = preupload.Timeout
		}
		r.uploadHTTPClient = &http.Client{Timeout: time.Second * time.Duration(timeout)}
		return r, nil
	}
	return nil, lastErr
}

func (c *Client) finishUpload(preupload *preuploadResp, endpoint *endpointResp) (*finishResp, error) {
	url := fmt.Sprintf("http:%s/%s?output=json&uploadId=%s",
		endpoint.endpoint, endpoint.path, endpoint.UploadID)
	req, err := http.NewRequest(http.MethodPost, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("Accept", "application/json")
	req.Header.Set("X-Upos-Auth", preupload.Auth)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status %d", resp.StatusCode)
	}

	var r finishResp
	err = json.Unmarshal(body, &r)
	if err != nil {
		return nil, err
	}

	if r.Etag != "" {
		r.fileSize, _ = strconv.ParseInt(r.Etag, 10, 64)
		if r.fileSize != preupload.uploadSize {
			return nil, fmt.Errorf("size mismatch: %d != %d", r.fileSize, preupload.uploadSize)
		}
		if preupload.requestSize != 0 && r.fileSize != preupload.requestSize {
			return nil, fmt.Errorf("size mismatch: %d != %d", r.fileSize, preupload.requestSize)
		}
	}

	return &r, nil
}

func (c *Client) doUpload(preupload *preuploadResp, endpoint *endpointResp, reader io.Reader) error {
	const batchNum = 2

	var lastErr error
	ch := make(chan *chunk, batchNum)
	resCh := make(chan *chunk, batchNum)
	ctx, cancel := context.WithCancel(context.Background())

	logger.WithFields(logger.Fields{
		"upload_id": endpoint.UploadID,
		"upos_uri":  preupload.UposURI,
	}).Debug("start uploading")

	for i := 0; i < batchNum; i++ {
		util.Go(func() {
		loop:
			for chunk := range ch {
				logger.WithField("upload_id", endpoint.UploadID).
					Debugf("upload chunk %d, size: %d", chunk.part, len(chunk.data))
				if chunk.md5 == "" {
					h := md5.New()
					_, errMD5 := h.Write(chunk.data)
					if errMD5 != nil {
						chunk.err = errMD5
					} else {
						chunk.md5 = hex.EncodeToString(h.Sum(nil))
					}
				}
				for chunk.err == nil {
					c.tryUploadChunk(ctx, preupload.Auth, endpoint, chunk)
					if lastErr != nil {
						break loop
					}
					if chunk.err != nil {
						if chunk.retry == 0 {
							logger.WithField("upload_id", endpoint.UploadID).
								Warnf("upload chunk %d (retry: %d) error: %v",
									chunk.part, chunk.retry, chunk.err)
						} else {
							logger.WithField("upload_id", endpoint.UploadID).
								Errorf("upload chunk %d (retry: %d) error: %v",
									chunk.part, chunk.retry, chunk.err)
						}
						if chunk.retry < c.config.Retry {
							// retry
							chunk.err = nil
							chunk.retry++
						}
						continue
					}
					break
				}
				resCh <- chunk
			}
		})
	}

	workingNum := int64(1)
	wg := &sync.WaitGroup{}

	wg.Add(1)
	util.Go(func() {
		defer wg.Done()
		for chunk := range resCh {
			if chunk == nil {
				// errors happened in reader
				break
			}
			if lastErr != nil {
				break
			}
			if chunk.err != nil {
				lastErr = chunk.err
				break
			}
			num := atomic.AddInt64(&workingNum, -1)
			if num <= 0 {
				break
			}
		}
		for len(ch) > 0 {
			<-ch
		}
	})

	chunkIdx := 0
	for {
		if lastErr != nil {
			break
		}
		chunkIdx++
		c := chunk{
			part: chunkIdx,
			data: make([]byte, preupload.ChunkSize),
		}
		size, err := reader.Read(c.data)
		if err != nil {
			if err == io.EOF {
				break
			}
			lastErr = err
			resCh <- nil
			break
		}
		if size < int(preupload.ChunkSize) {
			c.data = c.data[:size]
		}
		preupload.uploadSize += int64(size)
		atomic.AddInt64(&workingNum, 1)
		ch <- &c
	}

	// remove the first at init
	num := atomic.AddInt64(&workingNum, -1)
	if num <= 0 {
		close(resCh)
		// else resCh leave it as-is
	}

	wg.Wait()
	cancel()
	close(ch)

	return lastErr
}
