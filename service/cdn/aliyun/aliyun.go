// Package aliyun 封装 aliyun 直播相关 api
// 文档： https://help.aliyun.com/product/29949.html?spm=a2c4g.11186623.6.540.51b114caaTlP0A
package aliyun

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const oneDay = time.Duration(24 * 60 * 60 * time.Second)

// Config for client
type Config struct {
	Host            string `yaml:"host"`
	PushHost        string `yaml:"push_host"`
	AppName         string `yaml:"app_name"`
	AuthKey         string `yaml:"auth_key"`
	AuthTimeout     int64  `yaml:"auth_timeout"`
	Endpoint        string `yaml:"endpoint"`
	AccessKeyID     string `yaml:"access_key_id"`
	AccessKeySecret string `yaml:"access_key_secret"`
}

// Client 阿里云 api client
type Client struct {
	Config
	c           http.Client
	baseParam   commonParam
	authTimeout time.Duration
}

// NewClient new client
func NewClient(c Config) *Client {
	res := &Client{
		Config:      c,
		c:           http.Client{Timeout: 5 * time.Second},
		authTimeout: time.Duration(c.AuthTimeout * int64(time.Second)),
	}
	res.baseParam = baseParam
	res.baseParam.AccessKeyID = c.AccessKeyID
	return res
}

// ChannelOpen 是否在推流中
// 返回：是否推流，查询的 request_id
// TODO: 后续考虑使用 struct 封装
func (c *Client) ChannelOpen(roomID int64) (bool, string, error) {
	p := c.baseParam.ToParams()
	p["Action"] = actionDescribeLiveStreamsFrameRateAndBitRateData
	p["DomainName"] = c.Host
	p["AppName"] = c.AppName
	p["StreamName"] = strconv.FormatInt(roomID, 10)
	c.addSignature(http.MethodGet, p)
	v := url.Values{}
	for key, value := range p {
		v.Add(key, value)
	}
	req, err := http.NewRequest(
		http.MethodGet, fmt.Sprintf("%s?%s", c.Endpoint, v.Encode()), nil)
	if err != nil {
		return false, "", err
	}
	req.Header.Add("User-Agent", util.UserAgent)
	logger.Debugf("GET %s", req.URL)
	resp, err := c.c.Do(req)
	if err != nil {
		return false, "", err
	}
	defer resp.Body.Close()
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return false, "", err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, b)
	var r struct {
		FrameRateAndBitRateInfos struct {
			FrameRateAndBitRateInfo []FrameRateAndBitRateInfo `json:"FrameRateAndBitRateInfo"`
		} `json:"FrameRateAndBitRateInfos"`
		RequestID string `json:"RequestId"`
		Code      string `json:"Code"`
		HostID    string `json:"HostId"`
		Message   string `json:"Message"`
	}
	r.FrameRateAndBitRateInfos.FrameRateAndBitRateInfo = make([]FrameRateAndBitRateInfo, 0)
	err = json.Unmarshal(b, &r)
	if err != nil {
		logger.Error(err)
		// TODO: 待优化
	}
	if r.Code != "" {
		logger.WithField("RequestId", r.RequestID).Error(r.Message)
		return false, r.RequestID, errors.New(r.Message)
	}
	if len(r.FrameRateAndBitRateInfos.FrameRateAndBitRateInfo) == 0 {
		return false, r.RequestID, nil
	}
	return false, r.RequestID, nil
}

// BuildAuthedURL 通过 roomID 获取推流和拉流的 url
func (c *Client) BuildAuthedURL(roomID int64, needPushURL bool) (flvPullURL, hlsPullURL, rtmpPullURL, pushURL string) {
	uris := [3]string{
		fmt.Sprintf("/%s/%d.flv", c.AppName, roomID),
		fmt.Sprintf("/%s/%d.m3u8", c.AppName, roomID),
		fmt.Sprintf("/%s/%d", c.AppName, roomID),
	}
	var authKeys [3]string
	rand := strings.Replace(rand(), "-", "", -1)
	now := goutil.TimeNow()
	deadline := now.Add(c.authTimeout)
	for i := 0; i < len(uris); i++ {
		authKeys[i] = c.makeURLAuth(uris[i], deadline, rand)
	}
	flvPullURL = fmt.Sprintf("http://%s%s?auth_key=%s", c.Host, uris[0], authKeys[0])
	hlsPullURL = fmt.Sprintf("http://%s%s?auth_key=%s", c.Host, uris[1], authKeys[1])
	rtmpPullURL = fmt.Sprintf("rtmp://%s%s?auth_key=%s", c.Host, uris[2], authKeys[2])

	if !needPushURL {
		return
	}
	deadline = now.Add(oneDay)
	uri := fmt.Sprintf("/%s/%d", c.AppName, roomID)
	authKey := c.makeURLAuth(uri, deadline, rand)
	pushURL = fmt.Sprintf("rtmp://%s%s?auth_key=%s", c.PushHost, uri, authKey)
	return
}
