package aliyun

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
)

func (c Config) addSignature(method string, p params) {
	keys := make([]string, 0, len(p))
	for key := range p {
		keys = append(keys, key)
	}
	sort.Strings(keys)
	signParts := make([]string, 0, len(keys))
	for i := 0; i < len(keys); i++ {
		str := percentEncode(keys[i]) + "=" + percentEncode(p[keys[i]])
		// str = percentEncode(str)
		signParts = append(signParts, str)
	}
	toSign := method + "&%2F&" + percentEncode(strings.Join(signParts, "&"))
	h := hmac.New(sha1.New, []byte(c.AccessKeySecret+"&"))
	_, _ = h.Write([]byte(toSign))
	p["Signature"] = base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func percentEncode(value string) string {
	s := url.QueryEscape(value)
	s = strings.Replace(s, "+", "%20", -1)
	s = strings.Replace(s, "*", "%2A", -1)
	s = strings.Replace(s, "%7E", "~", -1)
	return s
}

// makeURLAuth 通过给定参数获取 url auth_key
// 文档地址：https://help.aliyun.com/document_detail/85018.html?spm=5176.11202509.0.0.5a992699D0OAWf
func (c Config) makeURLAuth(uri string, deadline time.Time, rand string) string {
	timestamp := deadline.Unix()
	hashValue := fmt.Sprintf("%s-%d-%s-0-%s", uri, timestamp, rand, c.AuthKey)
	h := md5.New()
	_, _ = h.Write([]byte(hashValue))
	res := fmt.Sprintf("%d-%s-0-%s", timestamp, rand, hex.EncodeToString(h.Sum(nil)))
	return res
}

func rand() string {
	r, err := uuid.NewRandom()
	if err != nil {
		panic(err)
	}
	return r.String()
}
