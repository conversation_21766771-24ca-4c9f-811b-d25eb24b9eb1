package aliyun

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestAddSignature(t *testing.T) {
	c := testConfig
	c.AccessKeyID = "testid"
	c.AccessKeySecret = "testsecret"
	p := params{
		"Format":           "XML",
		"SignatureMethod":  "HMAC-SHA1",
		"Action":           "DescribeLiveSnapshotConfig",
		"AccessKeyId":      "testid",
		"RegionId":         "cn-shanghai",
		"ServiceCode":      "live",
		"DomainName":       "test.com",
		"AppName":          "test",
		"SignatureNonce":   "c2fe8fbb-2977-4414-8d39-348d02419c1c",
		"Version":          "2016-11-01",
		"SignatureVersion": "1.0",
		"Timestamp":        "2017-06-14T09:51:14Z",
	}
	expected := "3I5a3myPjp8FXWT4rvxX5pKb/aw="
	c.addSignature("GET", p)
	assert.Equal(t, expected, p["Signature"])
}

func TestMakeURLAuth(t *testing.T) {
	assert := assert.New(t)
	c := testConfig
	c.AuthKey = "aliyuncdnexp1234"
	deadline := time.Unix(1444435200, 0)
	assert.Equal("1444435200-0-0-80cd3862d699b7118eed99103f2a3a4f",
		c.makeURLAuth("/video/standard/1K.html", deadline, "0"))
}
