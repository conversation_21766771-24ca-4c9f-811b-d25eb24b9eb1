package aliyun

import (
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type params map[string]string // key: 参数名； value: 参数值

const (
	actionDescribeLiveStreamsFrameRateAndBitRateData = "DescribeLiveStreamsFrameRateAndBitRateData"
)

const (
	formatISO8601 = "2006-01-02T15:04:05Z"
)

var (
	baseParam = commonParam{
		Version:          "2016-11-01",
		SignatureMethod:  "HMAC-SHA1",
		SignatureVersion: "1.0",
		Format:           "json",
	}
)

// 未包含 Signature
type commonParam struct {
	Version          string
	AccessKeyID      string
	SignatureMethod  string
	SignatureVersion string
	Format           string
}

// ToParam 获取 params
// NOTICE: 使用前先补全各个参数
func (cp commonParam) ToParams() params {
	return params{
		"Version":          cp.Version,
		"AccessKeyId":      cp.AccessKey<PERSON>,
		"SignatureMethod":  cp.SignatureMethod,
		"SignatureVersion": cp.SignatureVersion,
		"Format":           cp.Format,

		"Timestamp":      goutil.TimeNow().UTC().Format(formatISO8601),
		"SignatureNonce": rand(),
	}
}
