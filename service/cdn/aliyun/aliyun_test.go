package aliyun

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var testConfig = TestConfig()

func TestMain(m *testing.M) {
	logger.InitTestLog()

	m.Run()
}

func TestTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "host", "push_host", "app_name", "auth_key", "auth_timeout", "endpoint", "access_key_id", "access_key_secret")
}

func TestChannelOpen(t *testing.T) {
	assert := assert.New(t)
	c := NewClient(testConfig)
	open, _, err := c.ChannelOpen(123)
	assert.NoError(err)
	assert.False(open)
}

func TestBuildAuthedURL(t *testing.T) {
	assert := assert.New(t)
	c := NewClient(testConfig)
	var urls [4]string
	urls[0], urls[1], urls[2], urls[3] = c.BuildAuthedURL(1, true)
	prefixes := [4]string{
		"http://s.missevan.com/live/1.flv?auth_key=",
		"http://s.missevan.com/live/1.m3u8?auth_key=",
		"rtmp://s.missevan.com/live/1?auth_key=",
		"rtmp://push.s.missevan.com/live/1?auth_key=",
	}
	for i := 0; i < len(urls); i++ {
		assert.True(strings.HasPrefix(urls[i], prefixes[i]), i)
	}
	_, _, _, u := c.BuildAuthedURL(1, false)
	assert.Empty(u)
}
