// +build !release

package bililive

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	createChannelData interface{}
	accessTokenData   interface{}
	channelInfoData   interface{}
	once              sync.Once

	testConfig Config
)

func startMockBiliLiveServer() {
	r := gin.New()
	r.POST(ActionCreateChannel, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    createChannelData,
			"message": "",
		})
	})
	r.POST(ActionGetAccessToken, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    accessTokenData,
			"message": "",
		})
	})
	r.POST(ActionCloseChannel, func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"code":    0,
			"message": "success",
		})
	})
	r.POST(ActionGetChannelInfo, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    channelInfoData,
			"message": "",
		})
	})
	r.POST(ActionKickUser, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"message": "success",
		})
	})
	r.POST(ActionSetSendPermission, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"message": "success",
		})
	})
	addr := tutil.RunMockServer(r, 0)
	testConfig = Config{
		URL:       fmt.Sprintf("http://%s/", addr),
		AppKey:    "test_key",
		AppSecret: "test_secret",
		Business:  "missevan",
	}
}

// TestConfig 测试配置
func TestConfig() Config {
	once.Do(startMockBiliLiveServer)
	return testConfig
}

// SetMockResult 设置 mock 后的结果
func SetMockResult(action string, data interface{}) {
	switch action {
	case ActionCreateChannel:
		createChannelData = data
	case ActionGetAccessToken:
		accessTokenData = data
	case ActionGetChannelInfo:
		channelInfoData = data
	}
}
