package bililive

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	goserviceutil "github.com/MiaoSiLa/missevan-go/service/util"
)

// actions
// 流媒体 bililive 接口文档: https://info.bilibili.co/pages/viewpage.action?pageId=680238132
const (
	ActionCreateChannel     = "xlive/open-interface/v2/rtcinternal/createChannel"     // 创建频道
	ActionGetAccessToken    = "xlive/open-interface/v2/rtcinternal/getJoinToken"      // 获取 AccessToken
	ActionCloseChannel      = "xlive/open-interface/v2/rtcinternal/closeChannel"      // 关闭频道
	ActionGetChannelInfo    = "xlive/open-interface/v2/rtcinternal/getChannelInfo"    // 获取频道信息
	ActionKickUser          = "xlive/open-interface/v2/rtcinternal/kickUser"          // 踢出用户
	ActionSetSendPermission = "xlive/open-interface/v2/rtcinternal/setSendPermission" // 设置用户推流权限, 可用于静音或禁视频。仅对当前会话有效
)

// 连麦业务标识
const (
	BusinessLabelConnect      = "connect"
	BusinessLabelPK           = "pk"
	BusinessLabelMultiConnect = "multi_connect" // 主播连线
)

// Config config for bililive client
type Config struct {
	URL              string `yaml:"url"`
	AppKey           string `yaml:"app_key"`
	AppSecret        string `yaml:"app_secret"`
	Business         string `yaml:"business"`
	DisableSSLVerify bool   `yaml:"disable_ssl_verify"`
}

// SignParams sign params
func (conf Config) SignParams() (string, string) {
	return conf.AppKey, conf.AppSecret
}

// Client bililive client
type Client struct {
	conf Config
	c    http.Client
}

// NewClient new Client
func NewClient(conf Config) *Client {
	client := &Client{
		conf: conf,
		c: http.Client{
			Timeout: 5 * time.Second,
		},
	}

	if strings.HasPrefix(conf.URL, "https://") {
		if conf.DisableSSLVerify {
			client.c.Transport = &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			}
		}
	}
	return client
}

// ActionURL action URL
func (c *Client) ActionURL(action string) string {
	return c.conf.URL + action
}

type biliLiveResp struct {
	Code    int64           `json:"code"`
	Data    json.RawMessage `json:"data"`
	Message string          `json:"message"`
}

func (c *Client) request(action string, body url.Values, data interface{}) error {
	signBody := blademaster.Sign(c.conf, body)
	req, err := http.NewRequest(http.MethodPost, c.ActionURL(action), strings.NewReader(signBody))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", handler.ContentTypeFormUrlencoded)
	logger.Debugf("bililive request: %s\n%s", req.URL, signBody)
	r, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer r.Body.Close()
	b, err := io.ReadAll(r.Body)
	if err != nil {
		return err
	}
	logger.Debugf("HTTP %s\n%s", r.Status, b)

	var resp biliLiveResp
	err = json.Unmarshal(b, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 0 {
		return &goserviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("bililive: (%d) - %s", resp.Code, resp.Message)}
	}
	if data == nil {
		return nil
	}
	err = json.Unmarshal(resp.Data, data)
	if err != nil {
		return &goserviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("bililive: (%d) - %v", resp.Code, err)}
	}
	return nil
}

// channel type
const (
	ChannelAll   = iota // 语音+视频
	ChannelAudio        // 语音
	ChannelVideo        // 视频
)

// CreateChannelRequestParams create channel request params
type CreateChannelRequestParams struct {
	BusinessLabel string // 业务标识: 同一个业务方下的业务类别标识
	BusinessID    string // 业务 ID, 可选
}

// Channel channel bililive
type Channel struct {
	ChannelID int64 `json:"channel_id"`
}

// CreateChannel create channel
func (c *Client) CreateChannel(params *CreateChannelRequestParams) (*Channel, error) {
	form := url.Values{}
	form.Add("business", c.conf.Business)
	form.Add("business_label", params.BusinessLabel)
	if params.BusinessID != "" {
		form.Add("business_id", params.BusinessID)
	}
	form.Add("channel_type", strconv.Itoa(ChannelAudio)) // 0: 语音+视频, 1: 语音, 2: 视频
	resp := new(Channel)
	err := c.request(ActionCreateChannel, form, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// CloseChannel close channel
func (c *Client) CloseChannel(channelID int64) error {
	form := url.Values{}
	form.Add("channel_id", strconv.FormatInt(channelID, 10))
	err := c.request(ActionCloseChannel, form, nil)
	if err != nil {
		return err
	}
	return nil
}

// AccessTokenRequestParams request params
type AccessTokenRequestParams struct {
	ChannelID   int64
	UserID      int64
	IP          string
	Platform    string
	VideoEnable bool
	AudioEnable bool
}

// AccessTokenInfo access token info
type AccessTokenInfo struct {
	AccessToken string `json:"access_token"`
}

// GetAccessToken close channel
// NOTICE: access_token 有效期为 15s, 使用后失效, 不能重复使用
func (c *Client) GetAccessToken(params *AccessTokenRequestParams) (*AccessTokenInfo, error) {
	form := url.Values{}
	form.Add("channel_id", strconv.FormatInt(params.ChannelID, 10))
	form.Add("uid", strconv.FormatInt(params.UserID, 10))
	if params.IP != "" {
		form.Add("ip", params.IP)
	}
	if params.Platform != "" {
		form.Add("platform", params.Platform)
	}
	if params.VideoEnable {
		form.Add("video_enable", "true")
	}
	if params.AudioEnable {
		form.Add("audio_enable", "true")
	}
	resp := new(AccessTokenInfo)
	err := c.request(ActionGetAccessToken, form, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ChannelInfo channel info
type ChannelInfo struct {
	ChannelID     int64   `json:"channel_id"`
	ChannelType   int64   `json:"channel_type"`
	ChannelStatus int64   `json:"channel_status"` // 频道状态: 0 正常；1 已关闭
	UserIDList    []int64 `json:"user_list"`
	CreateTime    int64   `json:"create_time"` // 频道创建时间 Unix 秒时间戳
	CloseTime     int64   `json:"close_time"`  // 频道关闭时间 Unix 秒时间戳
}

// GetChannelInfo get channel info
func (c *Client) GetChannelInfo(channelID int64) (*ChannelInfo, error) {
	form := url.Values{}
	form.Add("channel_id", strconv.FormatInt(channelID, 10))
	resp := new(ChannelInfo)
	err := c.request(ActionGetChannelInfo, form, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// KickUser kick user
func (c *Client) KickUser(channelID, userID int64) error {
	form := url.Values{}
	form.Add("channel_id", strconv.FormatInt(channelID, 10))
	form.Add("uid", strconv.FormatInt(userID, 10))
	err := c.request(ActionKickUser, form, nil)
	if err != nil {
		return err
	}
	return nil
}

// SetSendPermissionRequestParams  set permission request params
type SetSendPermissionRequestParams struct {
	ChannelID   int64
	UserID      int64
	VideoEnable bool
	AudioEnable bool
}

// SetSendPermission set send permission
func (c *Client) SetSendPermission(params *SetSendPermissionRequestParams) error {
	form := url.Values{}
	form.Add("channel_id", strconv.FormatInt(params.ChannelID, 10))
	form.Add("uid", strconv.FormatInt(params.UserID, 10))
	form.Add("video_enable", strconv.FormatBool(params.VideoEnable))
	form.Add("audio_enable", strconv.FormatBool(params.AudioEnable))
	err := c.request(ActionSetSendPermission, form, nil)
	if err != nil {
		return err
	}
	return nil
}
