package bililive

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()
	handler.SetMode(handler.TestMode)
	startMockBiliLiveServer()

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "url", "app_key", "app_secret", "business", "disable_ssl_verify")
}

func TestClient_CreateChannel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(ActionCreateChannel, Channel{ChannelID: 123445677})
	c := NewClient(TestConfig())
	res, err := c.CreateChannel(&CreateChannelRequestParams{
		BusinessLabel: "pk",
		BusinessID:    "123",
	})
	require.NoError(err)
	assert.EqualValues(123445677, res.ChannelID)
}

func TestClient_GetAccessToken(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(ActionGetAccessToken, AccessTokenInfo{AccessToken: "eeexxx"})
	c := NewClient(TestConfig())
	res, err := c.GetAccessToken(&AccessTokenRequestParams{
		ChannelID:   123,
		UserID:      10,
		VideoEnable: true,
		AudioEnable: false,
	})
	require.NoError(err)
	assert.Equal("eeexxx", res.AccessToken)
}

func TestClient_CloseChannel(t *testing.T) {
	assert := assert.New(t)

	c := NewClient(TestConfig())
	err := c.CloseChannel(1)
	assert.NoError(err)
}

func TestClient_GetChannelInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(ActionGetChannelInfo, ChannelInfo{
		ChannelID:     2,
		ChannelType:   0,
		ChannelStatus: 1,
		UserIDList:    []int64{1234},
	})
	c := NewClient(TestConfig())
	res, err := c.GetChannelInfo(1)
	require.NoError(err)
	assert.EqualValues(2, res.ChannelID)
	assert.Zero(res.ChannelType)
	assert.EqualValues(1, res.ChannelStatus)
	assert.NotEmpty(res.UserIDList)
}

func TestClient_KickUser(t *testing.T) {
	require := require.New(t)

	c := NewClient(TestConfig())
	err := c.KickUser(1, 10)
	require.NoError(err)
}

func TestClient_SetSendPermission(t *testing.T) {
	require := require.New(t)

	c := NewClient(TestConfig())
	err := c.SetSendPermission(&SetSendPermissionRequestParams{
		ChannelID: 1,
		UserID:    10,
	})
	require.NoError(err)
}
