package bvc

import (
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	logger.InitTestLog()

	handler.SetMode(handler.TestMode)
	startMockBVCServer()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Info{}, "flvPullUrl", "hlsPullUrl", "rtmpPullUrl", "pushUrl", "chainedPushUrl", "sk")

	kc = tutil.NewKeyChecker(t, tutil.YAML)
	kc.Check(Config{}, "url", "app_key", "app_secret", "app_name", "auth_secret",
		"stream_name_prefix", "stream_name_ex_prefix", "auth_timeout", "is_new_version")
}

func TestChannelOpen(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(ActionChannelPushStat, gin.H{
		"streamName": "test",
		"pushHost":   "test_host",
		"streaming":  true,
		"sk":         "c8ef2160fd498c4440f25af20e982bc4",
	})
	c := NewClient(TestConfig())
	open, sk, err := c.ChannelOpen(123, 321)
	require.NoError(err)
	assert.True(open)
	assert.Equal("c8ef2160fd498c4440f25af20e982bc4", sk)
}

func TestSpeedList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := NewClient(TestConfig())
	SetMockResult(ActionSpeedList, map[string][]string{
		"testSpeedAddrs": {"rtmp://1", "rtmp://2"},
		"ips":            {"127.0.0.1"},
	})
	res, err := c.SpeedList("127.0.0.1")
	require.NoError(err)
	require.NotNil(res)
	assert.Equal([]string{"rtmp://1", "rtmp://2"}, res.TestSpeedAddrs)
	assert.Equal([]string{"127.0.0.1"}, res.IPs)
}

func TestBuildAuthedURL(t *testing.T) {
	assert := assert.New(t)

	c := NewClient(Config{
		AuthSecret:       "1234567",
		StreamNamePrefix: "test_",
	})
	info := Info{
		FLVPullURL:  "http://testflv/",
		HLSPullURL:  "http://testhls/",
		RTMPPullURL: "rtmp://test/",
		PushURL:     "rtmp://testpush/",
		ChainedPushURL: []string{
			"rtmp://testpush1",
			"rtmp://testpush2",
		},
		SK: "111",
	}
	res := c.BuildAuthedURL(1, 2, 3, "127.0.0.1", goutil.Web, &info, true)
	assert.True(strings.HasPrefix(res.FLVPullURL, info.FLVPullURL))
	assert.True(strings.HasPrefix(res.HLSPullURL, info.HLSPullURL))
	assert.Empty(res.RTMPPullURL)
	assert.Equal(info.PushURL, res.PushURL)
	assert.Equal(info.ChainedPushURL, res.ChainedPushURL)
	assert.Contains(res.FLVPullURL, "&sk=111")
	assert.Contains(res.HLSPullURL, "&sk=111")

	res = c.BuildAuthedURL(1, 2, 3, "127.0.0.1", goutil.IOS, &info, false)
	assert.Empty(res.PushURL)
	assert.Empty(res.ChainedPushURL)
	assert.Contains(res.FLVPullURL, "&pt=ios")
	assert.Contains(res.FLVPullURL, "&sk=111")
	assert.Contains(res.HLSPullURL, "&sk=111")
}

func TestParseCDNInfo(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("missevan04", parseCDNInfo("d1-missevan04.bilivideo.com"))
	assert.Equal("missevan04", parseCDNInfo("missevan04.bilivideo.com"))
}

func TestStreamName(t *testing.T) {
	assert := assert.New(t)

	conf := Config{
		StreamNamePrefix: "test_",
	}
	assert.Equal("test_1_12345", conf.StreamName(12345, 1))
}

func TestParseStreamName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	conf := Config{
		StreamNamePrefix: "test_",
	}
	roomID, creatorID, err := conf.ParseStreamName("test_1_12345")
	require.NoError(err)
	assert.Equal(int64(1), creatorID)
	assert.Equal(int64(12345), roomID)

	_, _, err = conf.ParseStreamName("test_1")
	assert.Equal(ErrIllegalStreamName, err)

	_, _, err = conf.ParseStreamName("test_1_12345_12345")
	assert.Equal(ErrIllegalStreamName, err)
}

func TestTraceID(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(goutil.MD5("test_1_12345_0"), TraceID("test_1_12345", 0))
}

func TestBuildURLWithSign(t *testing.T) {
	assert := assert.New(t)

	// sk 不为空
	testURL := "http://d1-missevan04.bilivideo.com/live-bvc/150458/maoer_uat_3457111_172842330.flv?" +
		"cdn=missevan04&oi=2130706433&pt=web&expires=1606832464&qn=10000&len=0&trid=bd736ed25230539ca24147520872ce32&" +
		"sigparams=cdn,oi,pt,expires,qn,len,trid&sign=cea567dc01a0092c47b324fcce0cf69a&sk=sk&ptype=1"
	p := newParam("missevan04", "web", "2130706433", "bd736ed25230539ca24147520872ce32", "1606832464")
	result := p.buildURLWithSign("http", "d1-missevan04.bilivideo.com",
		"/live-bvc/150458/maoer_uat_3457111_172842330.flv", "test_auth_secret", "sk")
	assert.Equal(testURL, result)

	// sk 为空
	testURL = "http://d1-missevan04.bilivideo.com/live-bvc/150458/maoer_uat_3457111_172842330.flv?" +
		"cdn=missevan04&oi=2130706433&pt=web&expires=1606832464&qn=10000&len=0&trid=bd736ed25230539ca24147520872ce32&" +
		"sigparams=cdn,oi,pt,expires,qn,len,trid&sign=cea567dc01a0092c47b324fcce0cf69a&ptype=1"
	p = newParam("missevan04", "web", "2130706433", "bd736ed25230539ca24147520872ce32", "1606832464")
	result = p.buildURLWithSign("http", "d1-missevan04.bilivideo.com",
		"/live-bvc/150458/maoer_uat_3457111_172842330.flv", "test_auth_secret", "")
	assert.Equal(testURL, result)
}

func TestIPToIntString(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("2130706433", IPToIntString("127.0.0.1"))
	assert.Equal("42540766411282592856903984951653826664", IPToIntString("2001:db8::68"))
}

func TestUpset(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	SetMockResult(ActionUpset, Channel{
		Channel: &ChannelInfo{
			PushURL: "rtmp://live-push.bilivideo.com/live-bvc/maoer_ex_1_1?key=854c7246288611eba218f281bfa61990",
		},
	})
	c := NewClient(TestConfig())
	channel, err := c.Upset(&UpsetRequest{
		StreamName: c.StreamName(1, 1),
		Edge:       "1",
		IP:         "127.0.0.1",
	})
	require.NoError(err)
	assert.NotNil(channel)
	assert.NotEmpty(channel.Channel.PushURL)
}

func TestStreamNameEX(t *testing.T) {
	assert := assert.New(t)

	conf := Config{StreamNameEXPrefix: "maoer_ex_"}
	streamName := conf.StreamNameEX(1, 2)
	assert.Equal("maoer_ex_2_1", streamName)
}

func TestParseStreamInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	streamName, key, err := ParseStreamInfo("rtmp://live-push.bilivideo.com/live-bvc/maoer_ex_1_1?key=854c7246288611eba218f281bfa61990")
	require.NoError(err)
	assert.Equal("854c7246288611eba218f281bfa61990", key)
	assert.Equal("maoer_ex_1_1", streamName)
}
