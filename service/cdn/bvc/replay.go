package bvc

import (
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
)

// ReplayStartRequest 请求生成回放参数
type ReplayStartRequest struct {
	StreamName string // 流名称
	StartTime  int64  // 开始时间，秒级时间戳
	EndTime    int64  // 结束时间，秒级时间戳
}

// ReplayStartResp 请求生成回放返回值
type ReplayStartResp struct {
	Message string `json:"message"`
}

// ReplayStart 请求生成回放
func (c *Client) ReplayStart(req *ReplayStartRequest) (*ReplayStartResp, error) {
	form := url.Values{}
	form.Add("stream_name", req.StreamName)
	form.Add("start_time", strconv.FormatInt(req.StartTime, 10))
	form.Add("end_time", strconv.FormatInt(req.EndTime, 10))
	if c.<PERSON> {
		form.Add("new_version", "true")
	}
	body := blademaster.Sign(c.Config, form)
	request, err := http.NewRequest(http.MethodPost, c.ActionURL(ActionReplayStart), strings.NewReader(body))
	if err != nil {
		return nil, err
	}
	request.Header.Set("Content-Type", handler.ContentTypeFormUrlencoded)
	resp := new(ReplayStartResp)
	err = c.request(request, resp)
	if err != nil {
		return nil, err
	}
	return resp, err
}
