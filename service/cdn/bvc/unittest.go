//go:build !release
// +build !release

package bvc

import (
	"fmt"
	"net/http"
	"sync"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	pushStatData  interface{}
	speedListData interface{}
	upsetData     interface{}
	replayData    interface{}
	once          sync.Once

	testConfig Config
)

func startMockBVCServer() {
	r := gin.New()
	r.GET(ActionChannelPushStat, func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code":    0,
			"data":    pushStatData,
			"message": "",
		})
	})
	r.GET(ActionSpeedList, func(c *gin.Context) {
		c.J<PERSON>(http.StatusOK, gin.H{
			"code":    0,
			"data":    speedListData,
			"message": "",
		})
	})
	r.POST(ActionUpset, func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"code":    0,
			"data":    upsetData,
			"message": "",
		})
	})
	r.<PERSON>OS<PERSON>(ActionReplayStart, func(c *gin.Context) {
		c.JSO<PERSON>(http.StatusOK, gin.H{
			"code":    0,
			"data":    replayData,
			"message": "",
		})
	})
	addr := tutil.RunMockServer(r, 0)
	testConfig = Config{
		URL:                fmt.Sprintf("http://%s/", addr),
		AppKey:             "test_key",
		AppSecret:          "test_secret",
		StreamNamePrefix:   "maoer_test_",
		StreamNameEXPrefix: "maoer_ex_test_",
	}
}

// TestConfig 测试配置
func TestConfig() Config {
	once.Do(startMockBVCServer)
	return testConfig
}

// SetMockResult 设置 mock 后的结果
func SetMockResult(action string, data interface{}) {
	switch action {
	case ActionChannelPushStat:
		pushStatData = data
	case ActionSpeedList:
		speedListData = data
	case ActionUpset:
		upsetData = data
	case ActionReplayStart:
		replayData = data
	}
}
