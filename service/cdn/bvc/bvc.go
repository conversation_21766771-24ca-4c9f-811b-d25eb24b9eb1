package bvc

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/big"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/blademaster"
	goserviceutil "github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 流类型
const (
	extFLV = ".flv"
	extHLS = ".m3u8"
	// extRTMP = ""
)

// actions
// 参考推流接口文档: https://info.bilibili.co/pages/viewpage.action?pageId=156580537
const (
	ActionUpset           = "live-maoer/stream-maoer/upset"     // 设置流接口
	ActionChannelPushStat = "live-maoer/stream-maoer/push/stat" // 判断流是否正在推流
	ActionSpeedList       = "live-maoer/stream-maoer/speedList" // 获取测速用流
)

// actions
// 参考文档: https://info.bilibili.co/pages/viewpage.action?pageId=523838223
const (
	ActionReplayStart = "maoer-replay/replay/start" // 生成回放
)

// ErrIllegalStreamName 非法流名错误
var ErrIllegalStreamName = errors.New("illegal stream name")

// Config config for bvc client
type Config struct {
	URL                string `yaml:"url"`
	AppKey             string `yaml:"app_key"`
	AppSecret          string `yaml:"app_secret"`
	AppName            string `yaml:"app_name"`
	AuthSecret         string `yaml:"auth_secret"`
	StreamNamePrefix   string `yaml:"stream_name_prefix"`
	StreamNameEXPrefix string `yaml:"stream_name_ex_prefix"` // 临时推流流名前缀
	AuthTimeout        int64  `yaml:"auth_timeout"`          // 单位：秒
	IsNewVersion       bool   `yaml:"is_new_version"`        // 使用录像合成新逻辑
}

// SignParams sign params
func (conf Config) SignParams() (string, string) {
	return conf.AppKey, conf.AppSecret
}

type params [][2]string

// Info Bvc info
type Info struct {
	FLVPullURL     string   `bson:"flvPullUrl" json:"-"`
	HLSPullURL     string   `bson:"hlsPullUrl" json:"-"`
	RTMPPullURL    string   `bson:"rtmpPullUrl" json:"-"`
	PushURL        string   `bson:"pushUrl" json:"-"`
	ChainedPushURL []string `bson:"chainedPushUrl" json:"-"`
	SK             string   `bson:"sk" json:"-"`
}

// Client bvc api client
type Client struct {
	Config
	c           http.Client
	authTimeout time.Duration
}

// NewClient new Client
func NewClient(conf Config) *Client {
	c := &Client{
		Config: conf,
		c: http.Client{
			Timeout: 5 * time.Second,
		},
		authTimeout: time.Duration(conf.AuthTimeout * int64(time.Second)),
	}
	return c
}

// ActionURL action URL
func (c *Client) ActionURL(action string) string {
	return c.URL + action
}

type bvcResp struct {
	Code    int64           `json:"code"`
	Data    json.RawMessage `json:"data"`
	Message string          `json:"message"`
}

// streamStat 判断流状态接口响应体
type streamStat struct {
	StreamName string `json:"streamName"`
	PushHost   string `json:"pushHost"`
	Streaming  bool   `json:"streaming"`
	SK         string `json:"sk"` // 流回源的加密参数
}

// ChannelOpen 房间是否开启
func (c *Client) ChannelOpen(roomID int64, creatorID int64) (bool, string, error) {
	q := url.Values{}
	q.Add("streamName", c.StreamName(roomID, creatorID))
	requestURL := fmt.Sprintf("%s?%s", c.ActionURL(ActionChannelPushStat),
		blademaster.Sign(c.Config, q))
	req, err := http.NewRequest(http.MethodGet, requestURL, nil)
	if err != nil {
		return false, "", err
	}
	data := new(streamStat)
	err = c.request(req, data)
	if err != nil {
		return false, "", err
	}
	return data.Streaming, data.SK, nil
}

// SpeedListResult 测速流的列表
type SpeedListResult struct {
	TestSpeedAddrs []string `json:"testSpeedAddrs"`
	IPs            []string `json:"ips"`
}

// SpeedList 获取测速用的流
func (c *Client) SpeedList(ip string) (*SpeedListResult, error) {
	q := url.Values{}
	q.Add("ip", ip)
	requestURL := fmt.Sprintf("%s?%s", c.ActionURL(ActionSpeedList),
		blademaster.Sign(c.Config, q))
	req, err := http.NewRequest(http.MethodGet, requestURL, nil)
	if err != nil {
		return nil, err
	}
	res := new(SpeedListResult)
	err = c.request(req, res)
	if err != nil && strings.Contains(err.Error(), "不支持测速") {
		// 不支持测速不认为是错误
		err = nil
	}
	return res, err
}

func (c *Client) request(req *http.Request, data interface{}) error {
	r, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer r.Body.Close()
	b, err := io.ReadAll(r.Body)
	logger.Debugf("HTTP %s\n%s", r.Status, b)
	if err != nil {
		return err
	}
	var resp bvcResp
	err = json.Unmarshal(b, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 0 {
		return &goserviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("BVC: (%d) - %s", resp.Code, resp.Message)}
	}
	err = json.Unmarshal(resp.Data, data)
	if err != nil {
		return &goserviceutil.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("BVC: (%d) - %v", resp.Code, err)}
	}
	return nil
}

// BuildAuthedURL 生成鉴权后的流地址
func (c *Client) BuildAuthedURL(roomID, creatorID, userID int64, ip string, platform goutil.Platform, bvc *Info, needPushURL bool) *Info {
	if bvc == nil {
		return nil
	}
	streamName := c.Config.StreamName(roomID, creatorID)
	traceID := TraceID(streamName, userID)
	pt := parsePlatform(platform)
	ip = IPToIntString(ip)
	res := new(Info)
	res.FLVPullURL = c.signPlayURL(extFLV, bvc.FLVPullURL, streamName, ip, pt, traceID, bvc.SK)
	res.HLSPullURL = c.signPlayURL(extHLS, bvc.HLSPullURL, streamName, ip, pt, traceID, bvc.SK)
	// res.RTMPPullURL = c.signPlayURL(extRTMP, bvc.RTMPPullURL, streamName, ip, pt, traceID)
	if needPushURL {
		res.PushURL = bvc.PushURL
		res.ChainedPushURL = bvc.ChainedPushURL
	}
	return res
}

func (c *Client) signPlayURL(ext, uri, streamName, ip, pt, trid, sk string) string {
	if uri == "" {
		return ""
	}
	randomCode := util.RandomCode(6)
	u, err := url.Parse(uri)
	if err != nil {
		logger.Error(err)
		return ""
	}
	urlPath := fmt.Sprintf("%s%s/%s%s", u.Path, randomCode, streamName, ext)
	expire := strconv.FormatInt(goutil.TimeNow().Add(c.authTimeout).Unix(), 10)
	params := newParam(parseCDNInfo(u.Host), pt, ip, trid, expire)
	return params.buildURLWithSign(u.Scheme, u.Host, urlPath, c.AuthSecret, sk)
}

func parsePlatform(pt goutil.Platform) string {
	switch pt {
	case goutil.IOS:
		return "ios"
	case goutil.Android:
		return "android"
	default:
		return "web"
	}
}

func parseCDNInfo(host string) string {
	cdnInfo := strings.SplitN(host, ".", 2)[0]
	strs := strings.Split(cdnInfo, "-")

	return strs[len(strs)-1]
}

func newParam(cdnInfo, pt, ip, trid, expires string) params {
	return params{
		{"cdn", cdnInfo},
		{"oi", ip},
		{"pt", pt},
		{"expires", expires},
		{"qn", "10000"},
		{"len", "0"},
		{"trid", trid},
	}
}

func (p params) buildURLWithSign(scheme, host, urlPath, authSecret, sk string) (url string) {
	var query string
	signParams := make([]string, len(p))
	for i, param := range p {
		query += param[0] + "=" + param[1] + "&"
		signParams[i] = param[0]
	}
	signParamsStr := strings.Join(signParams, ",")
	query += "sigparams=" + signParamsStr
	strToSign := urlPath + authSecret + query
	url = fmt.Sprintf("%s://%s%s?%s&sign=%s",
		scheme, host, urlPath, query, goutil.MD5(strToSign))
	if sk != "" {
		url += fmt.Sprintf("&sk=%s", sk)
	}
	// if ptype&0x01 == 1 过滤直播流中的视频帧
	url += "&ptype=1"
	return
}

// StreamName 通过 roomID 和 creatorID 生成 stream 名
func (conf Config) StreamName(roomID, creatorID int64) string {
	return fmt.Sprintf("%s%d_%d", conf.StreamNamePrefix, creatorID, roomID)
}

// StreamNameEX 通过房间 ID 和连麦用户 ID 生成临时推流 stream 名
// 返回格式为: maoer_ex_{user_id}_{room_id}
func (conf Config) StreamNameEX(roomID, userID int64) string {
	return fmt.Sprintf("%s%d_%d", conf.StreamNameEXPrefix, userID, roomID)
}

// ParseStreamName 通过传入的 Stream Name 流名来转换到 RoomID 和 CreatorID
func (conf Config) ParseStreamName(streamName string) (roomID, creatorID int64, err error) {
	if !strings.Contains(streamName, conf.StreamNamePrefix) {
		return 0, 0, ErrIllegalStreamName
	}

	streamName = strings.TrimPrefix(streamName, conf.StreamNamePrefix)

	s := strings.Split(streamName, "_")
	if len(s) != 2 {
		return 0, 0, ErrIllegalStreamName
	}

	for i, value := range s {
		id, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			return 0, 0, ErrIllegalStreamName
		}

		switch i {
		case 0:
			creatorID = id
		case 1:
			roomID = id
		}
	}

	return roomID, creatorID, nil
}

// TraceID 通过 streamName 生成 trace id
func TraceID(streamName string, userID int64) string {
	return goutil.MD5(fmt.Sprintf("%s_%d", streamName, userID))
}

// IPToIntString converts ip
// support IPv4 and IPv6
func IPToIntString(ip string) string {
	ret := big.NewInt(0)
	parseIP := net.ParseIP(ip)
	if parseIP == nil {
		return ""
	}
	if strings.Contains(ip, ".") {
		// an IPv4 address can still be a 16-byte slice. so need converts the ParseIP to 4-byte.
		return ret.SetBytes(parseIP.To4()).String()
	}
	return ret.SetBytes(parseIP).String()
}

// UpsetRequest 设置流请求参数
type UpsetRequest struct {
	StreamName string `json:"streamName"`       // 验证相关参数
	Edge       string `json:"edge"`             // 是否走边缘推流, 1: 走边缘
	ResetKey   string `json:"resetKey"`         // 是否需要重置 KEY, 1: 需要
	IP         string `json:"ip"`               // 推流 IP
	BestIP     string `json:"bestIp,omitempty"` // 测试获得到的最好 IP, 默认为空, 如果测速感觉都不好可以不带
}

// ChannelInfo channel
type ChannelInfo struct {
	PushURL string `json:"pushUrl"`
}

// Channel 设置推流响应的返回
type Channel struct {
	Channel *ChannelInfo `json:"channel"`
}

// Upset 设置流
func (c *Client) Upset(request *UpsetRequest) (*Channel, error) {
	form := url.Values{}
	form.Add("edge", request.Edge)
	if request.ResetKey != "" {
		form.Add("resetKey", request.ResetKey)
	}
	form.Add("ip", request.IP)
	form.Add("streamName", request.StreamName)
	body := blademaster.Sign(c.Config, form)
	req, err := http.NewRequest(http.MethodPost, c.ActionURL(ActionUpset), strings.NewReader(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", handler.ContentTypeFormUrlencoded)
	resp := new(Channel)
	err = c.request(req, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ParseStreamInfo 解析 pushUrl 中的 streamName 和 key
func ParseStreamInfo(pushURL string) (streamName string, key string, err error) {
	pURL, err := url.Parse(pushURL)
	if err != nil {
		return "", "", err
	}
	query := pURL.Query()
	key = query.Get("key")
	urls := strings.Split(pURL.Path, "/")
	streamName = urls[len(urls)-1]
	return streamName, key, nil
}
