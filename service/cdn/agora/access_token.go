// Package agora 辅助 声网服务端接入
// 文档: https://docs.agora.io/cn
package agora

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/binary"
	"hash/crc32"
	"math/rand"
	"sort"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// consts
const (
	// versionLen = 3
	// appIDLen = 32

	version = "006"
)

// consts
const (
	KJoinChannel        = 1
	KPublishAudioStream = 2
	KPublishVideoStream = 3
	KPublishDataStream  = 4

	KPublishAudioCDN           = 5
	KPublishVideoCDN           = 6
	KRequestPublishAudioStream = 7
	KRequestPublishVideoStream = 8
	KRequestPublishDataStream  = 9
	KInvitePublishAudioStream  = 10
	KInvitePublishVideoStream  = 11
	KInvitePublishDataStream   = 12

	KAdministrateChannel = 101
)

// 用户权限等级
const (
	LevelListener     = iota // 普通听众
	LevelBroadCaster         // 连麦者
	LevelChannelOwner        // 频道创建人
)

// UserIDToAgoraID 得到声网 ID
// os 传 util.All 则返回游客 ID
// userID 传 0 则返回 0
// 不支持负数用户 ID
// TODO: 需要变成字符串版本
func UserIDToAgoraID(userID int64, os goutil.Platform) int64 {
	if userID == 0 {
		return 0
	}
	switch os {
	case goutil.Android:
		return userID*10 + 1
	case goutil.IOS:
		return userID*10 + 2
	case goutil.Web, goutil.MobileWeb:
		return userID*10 + 3
	default:
		// guest
		return userID * 10
	}
}

// AccessToken access token
type AccessToken struct {
	AppID          string
	AppCertificate string
	ChannelName    string
	UIDStr         string
	Ts             uint32
	Salt           uint32
	Message        map[uint16]uint32
	Signature      string
	CrcChannelName uint32
	CrcUID         uint32
	MsgRawContent  string
}

func random(min int, max int) int {
	rand.Seed(goutil.TimeNow().UnixNano())
	return rand.Intn(max-min) + min
}

// NewAccessToken new AccessToken
func NewAccessToken(appID, appCertificate, channelName, uidStr string) *AccessToken {
	return &AccessToken{
		AppID:          appID,
		AppCertificate: appCertificate,
		ChannelName:    channelName,
		UIDStr:         uidStr,
		Ts:             uint32(goutil.TimeNow().Unix()) + 24*3600,
		Salt:           uint32(random(1, 99999999)),
		Message:        make(map[uint16]uint32),
	}
}

// BuildPrivilege 设置 privilege 权限
func (token *AccessToken) BuildPrivilege(level int) {
	token.AddPrivilege(KJoinChannel, 0)
	if level > LevelListener {
		token.AddPrivilege(KPublishAudioStream, 0)
	}
	if level == LevelChannelOwner {
		token.AddPrivilege(KPublishAudioCDN, 0)
	}
}

// AddPrivilege add privilege
func (token *AccessToken) AddPrivilege(privilege uint16, expireTimestamp uint32) {
	token.Message[privilege] = expireTimestamp
}

// Build build
func (token *AccessToken) Build() string {
	bufM := new(bytes.Buffer)
	packUint32(bufM, token.Salt)
	packUint32(bufM, token.Ts)
	packMapUint32(bufM, token.Message)
	bytesM := bufM.Bytes()

	sigHash := hmac.New(sha256.New, []byte(token.AppCertificate))
	_, _ = sigHash.Write([]byte(token.AppID + token.ChannelName + token.UIDStr))
	_, _ = sigHash.Write(bytesM)
	bytesSig := sigHash.Sum(nil)

	crc32q := crc32.MakeTable(crc32.IEEE)
	crcChannelName := crc32.Checksum([]byte(token.ChannelName), crc32q)
	crcUID := crc32.Checksum([]byte(token.UIDStr), crc32q)

	bufContent := new(bytes.Buffer)
	packString(bufContent, string(bytesSig[:]))
	packUint32(bufContent, crcChannelName)
	packUint32(bufContent, crcUID)
	packString(bufContent, string(bytesM[:]))
	return version + token.AppID + base64.StdEncoding.EncodeToString(bufContent.Bytes())
}

func packUint16(w *bytes.Buffer, n uint16) {
	_ = binary.Write(w, binary.LittleEndian, n)
}

func packUint32(w *bytes.Buffer, n uint32) {
	_ = binary.Write(w, binary.LittleEndian, n)
}

func packString(w *bytes.Buffer, s string) {
	packUint16(w, uint16(len(s)))
	w.Write([]byte(s))
}

func packMapUint32(w *bytes.Buffer, extra map[uint16]uint32) {
	keys := []int{}
	packUint16(w, uint16(len(extra)))
	for k := range extra {
		keys = append(keys, int(k))
	}
	//should sorted keys
	sort.Ints(keys)
	for _, k := range keys {
		v := extra[uint16(k)]
		packUint16(w, uint16(k))
		packUint32(w, v)
	}
}
