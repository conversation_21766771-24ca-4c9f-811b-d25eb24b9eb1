package agora

import (
	"testing"

	"github.com/stretchr/testify/assert"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUserIDToAgoraID(t *testing.T) {
	assert := assert.New(t)
	platforms := [4]goutil.Platform{goutil.Unknown, goutil.Android, goutil.IOS, goutil.Web}
	aids := [4]int64{10, 11, 12, 13}
	for i := 0; i < len(platforms); i++ {
		assert.Equal(aids[i], UserIDToAgoraID(1, platforms[i]))
	}
}

func TestBuild(t *testing.T) {
	acctoken := NewAccessToken(
		"testid",
		"testkey",
		"testchannel",
		"12",
	)
	acctoken.Ts = 24*3600 + 1234567890
	acctoken.Salt = 123
	acctoken.AddPrivilege(KJoinChannel, 0)
	acctoken.AddPrivilege(KPublishAudioStream, 0)
	acctoken.AddPrivilege(KPublishAudioCDN, 0)
	res := acctoken.Build()
	expect := "006testidIAC9ovT6638mbKFhtl6rZ4rRuzmYwFr6PhvFm+3gywwYSepuE8zNRFNPHAB7AAAAUlSXSQMAAQAAAAAAAgAAAAAABQAAAAAA"
	assert.Equal(t, expect, res)
}

func TestBuildPrivilege(t *testing.T) {
	assert := assert.New(t)
	token := NewAccessToken(
		"testid",
		"testkey",
		"testchannel",
		"12",
	)
	token.Message = map[uint16]uint32{
		KJoinChannel:        1,
		KPublishAudioStream: 1,
		KPublishAudioCDN:    1,
	}
	token.BuildPrivilege(LevelChannelOwner)
	assert.Zero(token.Message[KJoinChannel])
	assert.Zero(token.Message[KPublishAudioStream])
	assert.Zero(token.Message[KPublishAudioCDN])
}
