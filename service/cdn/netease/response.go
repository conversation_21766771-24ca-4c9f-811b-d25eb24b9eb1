package netease

import "encoding/json"

// 	频道状态
const (
	StatusUnoccupied = iota
	StatusLiving
	StatusBanned
	StatusRecording
)

// 频道类型
const (
	TypeRtmp = iota
	TypeHls
	TypeHTTP
)

// ChannelstatsRes channelstats 返回结果
type ChannelstatsRes struct {
	NeedRecord int    `json:"needRecord"`
	UID        int64  `json:"uid"`
	Duration   int    `json:"duration"`
	Status     int    `json:"status"`
	Name       string `json:"name"`
	Filename   string `json:"filename"`
	Format     int    `json:"format"`
	Type       int    `json:"type"`
	CTime      int64  `json:"ctime"`
	CID        string `json:"cid"`
}

// ChannelInfo 房间详细信息
type ChannelInfo struct {
	CID         string `json:"cid"`
	Ctime       int64  `json:"ctime"`
	Name        string `json:"name"`
	PushURL     string `json:"pushUrl"`
	HTTPPullURL string `json:"httpPullUrl"`
	HlsPullURL  string `json:"hlsPullUrl"`
	RTMPPullURL string `json:"rtmpPullUrl"`
}

type response struct {
	Code int             `json:"code"`
	Ret  json.RawMessage `json:"ret"`
	Msg  string          `json:"msg"`
}
