package netease

import (
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/logger"
)

const testChCID = "3be89b2149654f42b97450aadf6de1f8"

var testConfig = TestConfig()

// NOTICE: 已弃用网易云推流商
func TestMain(m *testing.M) {
	logger.InitTestLog()

	// m.Run()
}

func TestAddCommonParam(t *testing.T) {
	assert := assert.New(t)
	c := Config{
		AppKey:    "123",
		AppSecret: "456",
	}
	req, _ := http.NewRequest("GET", "", nil)
	c.addCommonParam(req)
	assert.Equal("123", req.Header.Get("AppKey"))
	nonce := req.Header.Get("Nonce")
	curTime := req.Header.Get("CurTime")
	checkSum := req.Header.Get("CheckSum")
	h := sha1.New()
	_, _ = h.Write([]byte(c.AppSecret + nonce + curTime))
	assert.Equal(checkSum, hex.EncodeToString(h.Sum(nil)))
}

func TestChannelOpen(t *testing.T) {
	assert := assert.New(t)
	c := NewClient(testConfig)
	_, err := c.ChannelOpen("12")
	assert.Equal("cid error", err.Error())

	open, err := c.ChannelOpen(testChCID)
	assert.NoError(err)
	assert.False(open)
}

func TestChannelCreateDelete(t *testing.T) {
	t.Skip() // TODO: 目前因为云信未使用，并且测试失败，所以先忽略
	assert := assert.New(t)
	c := NewClient(testConfig)
	ch, err := c.CreateChannel("test create delete ch")
	require.NoError(t, err)
	assert.NoError(c.DeleteChannel(ch.CID), "测试删除 channel 失败，请手动删除, cid: %s", ch.CID)
}

func TestNewRequest(t *testing.T) {
	assert := assert.New(t)
	req, err := testConfig.newRequest(actionChannelCreate, m{"test": "123"})
	assert.NoError(err)
	assert.Equal("POST", req.Method)
	b, err := io.ReadAll(req.Body)
	assert.NoError(err)
	var param m
	assert.NoError(json.Unmarshal(b, &param))
	assert.Equal("123", param["test"].(string))
}
