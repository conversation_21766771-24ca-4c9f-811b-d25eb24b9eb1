// Package netease 封装云信直播服务端 api
// 文档： https://dev.yunxin.163.com/docs/product/直播/服务端API文档
package netease

import (
	"bytes"
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	domain = "https://vcloud.163.com/app/"
)

const (
	actionChannelCreate = "channel/create"
	actionChannelDelete = "channel/delete"
	actionChannelStats  = "channelstats"
)

type m map[string]interface{}

// Config for client
type Config struct {
	AppKey    string `yaml:"app_key"`
	AppSecret string `yaml:"app_secret"`
	AuthKey   string `yaml:"auth_key"`
}

// Client 云信 api client
type Client struct {
	Config
	c http.Client
}

// NewClient new Client
func NewClient(conf Config) *Client {
	c := &Client{
		Config: conf,
		c: http.Client{
			Timeout: 5 * time.Second,
		},
	}
	return c
}

// ChannelOpen 判断房间是否开启
func (c *Client) ChannelOpen(cid string) (bool, error) {
	if len(cid) != 32 {
		return false, errors.New("cid error")
	}
	param := m{"cid": cid}
	var res ChannelstatsRes
	err := c.post(actionChannelStats, param, &res)
	if err != nil {
		return false, err
	}
	open :=
		res.Status == StatusLiving || res.Status == StatusRecording
	return open, nil
}

// CreateChannel 创建频道
func (c *Client) CreateChannel(name string) (ChannelInfo, error) {
	var info ChannelInfo
	param := m{"name": name, "type": 0}
	err := c.post(actionChannelCreate, param, &info)
	return info, err
}

// DeleteChannel 删除频道
func (c *Client) DeleteChannel(cid string) error {
	param := m{"cid": cid}
	return c.post(actionChannelDelete, param, nil)
}

func (c *Client) post(action string, param m, ret interface{}) error {
	req, err := c.newRequest(action, param)
	if err != nil {
		return err
	}
	r, err := c.c.Do(req)
	if err != nil {
		return err
	}
	defer r.Body.Close()
	b, err := io.ReadAll(r.Body)
	logger.Debugf("HTTP %s\n%s", r.Status, b)
	if err != nil {
		return err
	}
	var resp response
	err = json.Unmarshal(b, &resp)
	if err != nil {
		return err
	}
	if resp.Code != 200 {
		return fmt.Errorf("nim error: %s, code: %d", resp.Msg, resp.Code)
	}
	if ret != nil {
		err = json.Unmarshal(resp.Ret, ret)
	}
	return err
}

func (c Config) newRequest(action string, param m) (*http.Request, error) {
	url := domain + action
	paramBytes, err := json.Marshal(param)
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(paramBytes))
	if err != nil {
		return nil, err
	}
	logger.Debugf("POST %s \n %s", req.URL, paramBytes)
	c.addCommonParam(req)
	return req, err
}

func (c Config) addCommonParam(req *http.Request) {
	nonce := strconv.FormatInt(rand.Int63(), 10)
	now := strconv.FormatInt(goutil.TimeNow().Unix(), 10)
	h := sha1.New()
	_, _ = h.Write([]byte(c.AppSecret + nonce + now))

	req.Header.Add("Content-Type", "application/json;charset=utf-8")
	req.Header.Add("User-Agent", util.UserAgent)
	req.Header.Add("CurTime", now)
	req.Header.Add("Nonce", nonce)
	req.Header.Add("AppKey", c.AppKey)
	req.Header.Add("CheckSum", hex.EncodeToString(h.Sum(nil)))
}
