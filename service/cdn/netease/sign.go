package netease

import (
	"net/url"
	"strconv"
	"time"

	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// BuildAuthedURL 生成鉴权后的 url
func (c Config) BuildAuthedURL(uri string, makeTime time.Time, isPushURL bool) string {
	timeStr := strconv.FormatInt(makeTime.Unix(), 10)
	u, err := url.Parse(uri)
	if err != nil {
		logger.Error(err)
		return uri
	}
	if isPushURL {
		strToSign := c.AuthKey + u.Path + timeStr
		q := u.Query()
		q.Set("wsTime", timeStr)
		q.Set("wsSecret", goutil.MD5(strToSign))
		u.RawQuery = q.Encode()
	} else {
		strToSign := timeStr + u.Host + u.Path + c.AuthKey
		q := u.Query()
		q.Set("playTime", timeStr)
		q.Set("playSecret", goutil.MD5(strToSign))
		u.RawQuery = q.Encode()
	}
	return u.String()
}
