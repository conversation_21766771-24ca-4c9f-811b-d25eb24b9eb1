package netease

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestBuildAuthedURL(t *testing.T) {
	assert := assert.New(t)
	expertPushURL := "rtmp://test.live.net/live/abcdefg?wsSecret=1ed9beeb63eb77d4b5d5d9ec21a1e1b8&wsTime=1234567890"
	expectPullURL := "rtmp://test.live.net/live/abcdefg?playSecret=b155322720e8f691810d50443099f66e&playTime=1234567890"
	makeTime := time.Unix(1234567890, 0)
	assert.Equal(expertPushURL, testConfig.BuildAuthedURL("rtmp://test.live.net/live/abcdefg", makeTime, true))
	assert.Equal(expectPullURL, testConfig.BuildAuthedURL("rtmp://test.live.net/live/abcdefg", makeTime, false))
}
