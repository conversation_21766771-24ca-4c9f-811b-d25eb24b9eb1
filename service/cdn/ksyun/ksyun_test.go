package ksyun

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKey(t *testing.T) {
	assert := assert.New(t)
	assert.Empty(tutil.KeyExists(tutil.YAML, Config{}, "app_key", "app_secret", "push_auth_key", "pull_auth_key", "unique_name", "app_name", "auth_timeout"))
}

func TestMakeSign(t *testing.T) {
	assert := assert.New(t)
	conf := Config{AppName: "live-fm-dev", Pull<PERSON><PERSON><PERSON><PERSON>: "pullkey", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: "pushKey", UniqueName: "acgvideo"}
	c := NewClient(conf)
	flv, hls, rtmp, push := c.BuildAuthedURL(10, 12, true)
	assert.True(strings.HasPrefix(flv, "http://ks-flv.fm.missevan.com/live-fm-dev/live_10_12.flv?audio-only=true&k="))
	assert.True(strings.HasPrefix(hls, "http://ks-hls.fm.missevan.com/live-fm-dev/live_10_12.m3u8?audio-only=true&k="))
	assert.True(strings.HasPrefix(rtmp, "rtmp://ks-rtmp.fm.missevan.com/live-fm-dev/live_10_12?audio-only=true&k="))
	assert.True(strings.HasPrefix(push, "rtmp://ks-push.fm.missevan.com/live-fm-dev/live_10_12?k="))

	_, _, _, push = c.BuildAuthedURL(10, 12, false)
	assert.Empty(push)

	// 生成测试流数据用
	// now := time.Now()
	// deadline := time.Date(2019, 9, 13, 0, 0, 0, 0, now.Location())
	// key := makeSign("test", deadline, c.PushAuthKey)
	// fmt.Printf("rtmp://ks-push.fm.missevan.com/live-fm/test?t=%d&k=%s\n\n", deadline.Unix(), key)
	// key = makeSign("test", deadline, c.PullAuthKey)
	// fmt.Printf("https://ks-flv.fm.missevan.com/live-fm/test.flv?t=%d&k=%s\n\n", deadline.Unix(), key)
	// fmt.Printf("rtmp://ks-rtmp.fm.missevan.com/live-fm/test?t=%d&k=%s\n\n", deadline.Unix(), key)
	// fmt.Printf("https://ks-hls.fm.missevan.com/live-fm/test.m3u8?t=%d&k=%s\n\n", deadline.Unix(), key)
}

func TestChannelOpen(t *testing.T) {
	logger.InitTestLog()
	conf := TestConfig()
	c := NewClient(conf)
	// 由于鉴权一定会失败，所以肯定会出错
	_, _, err := c.ChannelOpen(1, 2)
	assert.Error(t, err)
}

func TestUnmarshalBody(t *testing.T) {
	assert := assert.New(t)

	body := []byte(`{"RequestId":false,"Data":false}`)
	var v struct {
		UserID int64 `json:"user_id"`
	}
	requestID, err := unmarshalBody(body, &v)
	assert.Error(err)
	assert.Empty(requestID)

	body = []byte(`{"RequestId":"123","Data":false}`)
	requestID, err = unmarshalBody(body, &v)
	assert.Error(err)
	assert.Equal("123", requestID)

	body = []byte(`{"RequestId":"123","Data":{"RetCode":1}}`)
	_, err = unmarshalBody(body, &v)
	assert.IsType(&util.APIError{}, err)

	body = []byte(`{"RequestId":"123","Data":{"RetCode":0,"user_id":1}}`)
	_, err = unmarshalBody(body, &v)
	assert.NoError(err)
	assert.Equal(int64(1), v.UserID)
}
