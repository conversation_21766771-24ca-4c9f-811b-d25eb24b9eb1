package ksyun

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go/aws/credentials"
	v4 "github.com/aws/aws-sdk-go/aws/signer/v4"

	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// aws settings
const (
	awsRegion      = "cn-beijing-6"
	awsServiceName = "kls"
	version        = "2017-01-01"
	endpoint       = "https://kls.api.ksyun.com"
)

// domains
// TODO: 移动到 config 里面
const (
	pushDomain = "ks-push.fm.missevan.com"
	flvDomain  = "ks-flv.fm.missevan.com"
	rtmpDomain = "ks-rtmp.fm.missevan.com"
	hlsDomain  = "ks-hls.fm.missevan.com"
)

// ksyun actions
const (
	actionListRealtimePubStreamsInfo = "ListRealtimePubStreamsInfo"
)

const oneDay = time.Duration(24 * 60 * 60 * time.Second)

// Config config for ksyunclient
type Config struct {
	AppKey      string `yaml:"app_key"`
	AppSecret   string `yaml:"app_secret"`
	PushAuthKey string `yaml:"push_auth_key"`
	PullAuthKey string `yaml:"pull_auth_key"`
	UniqueName  string `yaml:"unique_name"`
	AppName     string `yaml:"app_name"`
	AuthTimeout int64  `yaml:"auth_timeout"` // 单位：秒
}

// Client 金山云 api client
type Client struct {
	Config
	c           http.Client
	creds       *credentials.Credentials
	authTimeout time.Duration
}

type commonResp struct {
	Data      json.RawMessage `json:"Data"`
	RequestID string          `json:"RequestId"`
}

type dataCommon struct {
	RetCode int    `json:"RetCode"`
	RetMsg  string `json:"RetMsg"`
}

// NewClient new Client
func NewClient(conf Config) *Client {
	c := &Client{
		Config: conf,
		c: http.Client{
			Timeout: 5 * time.Second,
		},
		authTimeout: time.Duration(conf.AuthTimeout * int64(time.Second)),
	}
	c.creds = credentials.NewStaticCredentials(conf.AppKey, conf.AppSecret, "")
	return c
}

// ChannelOpen 房间是否开启
func (c *Client) ChannelOpen(roomID int64, creatorID int64) (bool, string, error) {
	u, _ := url.Parse(endpoint)
	q := u.Query()
	q.Add("Action", actionListRealtimePubStreamsInfo)
	q.Add("Version", version)
	q.Add("UniqueName", c.UniqueName)
	q.Add("App", c.AppName)
	q.Add("Pubdomain", pushDomain)
	stream := Stream(roomID, creatorID)
	q.Add("Stream", stream)
	u.RawQuery = q.Encode()
	req, err := http.NewRequest(http.MethodGet, u.String(), nil)
	if err != nil {
		return false, "", err
	}
	var info struct {
		Count int `json:"Count"`
	}
	requestID, err := c.getResponse(req, &info)
	return info.Count == 1, requestID, err
}

// getResponse 从相应中的 Data 获取所需结果，v 同 json.Unmarshal 的 v
func (c *Client) getResponse(req *http.Request, v interface{}) (string, error) {
	req.Header.Add("User-Agent", util.UserAgent)
	req.Header.Add("Content-Type", "application/json")
	signer := v4.NewSigner(c.creds)
	now := goutil.TimeNow()
	_, err := signer.Sign(req, nil, awsServiceName, awsRegion, now)
	if err != nil {
		return "", err
	}

	logger.Debugf("%s %s", req.Method, req.URL)
	resp, err := c.c.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	b, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	logger.Debugf("HTTP %s\n%s", resp.Status, b)
	if resp.StatusCode != http.StatusOK {
		return "", &util.APIError{
			Status:  resp.StatusCode,
			Message: fmt.Sprintf("ksyun: unexpected error, http status: %d", resp.StatusCode)}
	}
	return unmarshalBody(b, v)
}

// TODO: v 改成专门的 interface{}
func unmarshalBody(body []byte, v interface{}) (string, error) {
	var r commonResp
	err := json.Unmarshal(body, &r)
	if err != nil {
		return "", err
	}
	var ret dataCommon
	err = json.Unmarshal(r.Data, &ret)
	if err != nil {
		return r.RequestID, err
	}
	if ret.RetCode != 0 {
		return r.RequestID, &util.APIError{
			Status:  http.StatusBadRequest,
			Message: fmt.Sprintf("ksyun: (%d) - %s", ret.RetCode, ret.RetMsg)}
	}
	err = json.Unmarshal(r.Data, v)
	return r.RequestID, err
}

// BuildAuthedURL 生成鉴权后的流地址
func (c *Client) BuildAuthedURL(roomID, creatorID int64, needPushURL bool) (flvPullURL, hlsPullURL, rtmpPullURL, pushURL string) {
	stream := Stream(roomID, creatorID)
	uris := [3]string{
		fmt.Sprintf("http://%s/%s/%s.flv", flvDomain, c.AppName, stream),
		fmt.Sprintf("http://%s/%s/%s.m3u8", hlsDomain, c.AppName, stream),
		fmt.Sprintf("rtmp://%s/%s/%s", rtmpDomain, c.AppName, stream),
	}
	now := goutil.TimeNow()
	deadline := now.Add(c.authTimeout)
	v := url.Values{}
	v.Add("t", strconv.FormatInt(deadline.Unix(), 10))
	v.Add("k", makeSign(stream, deadline, c.PullAuthKey))
	v.Add("audio-only", "true")
	flvPullURL = fmt.Sprintf("%s?%s", uris[0], v.Encode())
	hlsPullURL = fmt.Sprintf("%s?%s", uris[1], v.Encode())
	rtmpPullURL = fmt.Sprintf("%s?%s", uris[2], v.Encode())
	if !needPushURL {
		return
	}
	deadline = now.Add(oneDay)
	v = url.Values{}
	v.Add("t", strconv.FormatInt(deadline.Unix(), 10))
	v.Add("k", makeSign(stream, deadline, c.PushAuthKey))
	pushURL = fmt.Sprintf("rtmp://%s/%s/%s?%s", pushDomain, c.AppName, stream, v.Encode())
	return
}

// makeSign 根据 stream 和密钥生成流鉴权字段值
func makeSign(stream string, deadline time.Time, authKey string) string {
	strToSign := fmt.Sprintf("%s%s%d", authKey, stream, deadline.Unix())
	return goutil.MD5(strToSign)
}

// Stream 通过 roomID 和 creatorID 生成 stream 名
func Stream(roomID, creatorID int64) string {
	return fmt.Sprintf("live_%d_%d", roomID, creatorID)
}
