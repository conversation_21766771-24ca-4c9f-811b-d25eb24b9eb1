package util

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUnixMill(t *testing.T) {
	testTime := time.Unix(1234567890, 123456789)
	assert.Equal(t, int64(1234567890123), TimeToUnixMilli(testTime))
	testTime = time.Unix(123456789, 123000000)
	assert.Equal(t, testTime, UnixMilliToTime(123456789123))
}

func TestFloat2DP(t *testing.T) {
	assert := assert.New(t)

	a := Float2DP(0.167)
	b, _ := json.Marshal(a)
	assert.Equal(b, []byte(`0.17`))

	aa := a
	err := json.Unmarshal([]byte(`null`), &a)
	assert.NoError(err)
	assert.Equal(aa, a)

	err = json.Unmarshal([]byte(`1`), &a)
	assert.NoError(err)
	assert.EqualValues(1, a)

	err = json.Unmarshal([]byte(`"1"`), &a)
	assert.NoError(err)
	assert.EqualValues(1, a)

	assert.Error(json.Unmarshal([]byte(`"test"`), &a))
	assert.Error(json.Unmarshal([]byte(`true`), &a))
}

func TestFloat2DPString(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("233.23", Float2DP(233.2333).String())
	// 银行家舍入法举例
	// 四舍
	assert.Equal("9.82", Float2DP(9.8249).String())
	// 六入
	assert.Equal("9.83", Float2DP(9.82671).String())
	// 五后非空就进一
	assert.Equal("9.83", Float2DP(9.8251).String())
	assert.Equal("9.83", Float2DP(9.82500001).String())
	// 五后为空向前看奇偶，五前为偶应舍去
	assert.Equal("9.82", Float2DP(9.825).String())
	// 五后为空向前看奇偶，五前为奇要进一
	assert.Equal("9.84", Float2DP(9.835).String())
}

func TestFlipCoin(t *testing.T) {
	assert := assert.New(t)
	c := Coin(0.7)
	var res float64
	times := float64(100000)
	for i := float64(0); i < times; i++ {
		if c.Flip() {
			res++
		}
	}
	assert.LessOrEqual(math.Abs(times*0.7-res), times/50)

	c = Coin(0.4)
	res = 0
	for i := float64(0); i < times; i++ {
		if c.Flip() {
			res++
		}
	}
	assert.LessOrEqual(math.Abs(times*0.4-res), times/50)
}

func TestWeekday(t *testing.T) {
	assert := assert.New(t)
	weekdays := [7]time.Weekday{
		time.Sunday,
		time.Monday,
		time.Tuesday,
		time.Wednesday,
		time.Thursday,
		time.Friday,
		time.Saturday}
	testInt := [7]int{6, 0, 1, 2, 3, 4, 5}
	for i := 0; i < 7; i++ {
		assert.Equal(testInt[i], WeekdayToInt(weekdays[i]))
	}
}

func TestUniq(t *testing.T) {
	assert := assert.New(t)
	arr := []int64{1, 1}
	assert.Equal([]int64{1}, Uniq(arr))
}

func TestParseYearMonth(t *testing.T) {
	assert := assert.New(t)

	tm, month, err := ParseYearMonth("2019-12")
	assert.NoError(err)
	assert.Equal(time.Date(2019, 12, 1, 0, 0, 0, 0, time.Local), tm)
	assert.Equal(201912, month)

	_, _, err = ParseYearMonth("")
	assert.Error(err)
}

func TestYearMonthInt(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(YearMonthInt(time.Date(2006, 1, 2, 3, 4, 5, 6, time.Local)), 200601)
}

func TestBeginningOfMonth(t *testing.T) {
	assert := assert.New(t)

	t1 := time.Date(2006, 2, 31, 3, 4, 5, 6, time.Local)
	t2 := time.Date(2006, 3, 1, 0, 0, 0, 0, time.Local)
	assert.Equal(t2, BeginningOfMonth(t1))
}

func TestEndOfMonth(t *testing.T) {
	assert := assert.New(t)

	t1 := time.Date(2006, 1, 31, 3, 4, 5, 6, time.Local)
	t2 := time.Date(2006, 2, 1, 0, 0, 0, 0, time.Local)
	assert.Equal(t2, EndOfMonth(t1))
}

func TestBeginningOfWeek(t *testing.T) {
	assert := assert.New(t)

	for i := 1; i <= 7; i++ {
		t := time.Date(2018, 1, i, 15, 4, 5, 0, time.Local)
		assert.Equal("2018-01-01 00:00:00", BeginningOfWeek(t).Format(TimeFormatYMDHMS))
		t = time.Date(2021, 5, i+2, 15, 4, 5, 0, time.Local)
		assert.Equal("2021-05-03 00:00:00", BeginningOfWeek(t).Format(TimeFormatYMDHMS))
	}
}

func TestEndOfWeek(t *testing.T) {
	assert := assert.New(t)
	tm := time.Date(2021, 9, 1, 0, 0, 0, 0, time.Local)
	assert.Equal(int64(1630857600), EndOfWeek(tm).Unix())
}

func TestRandomCode(t *testing.T) {
	assert := assert.New(t)
	assert.Len(RandomCode(5), 5)
	assert.Len(RandomCode(6), 6)
}

func TestToMap(t *testing.T) {
	assert := assert.New(t)

	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 2)
	for i := 0; i < 2; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	result := ToMap(inputs, func(f *foo) int {
		return f.ID
	})
	for _, elem := range inputs {
		v, ok := result[elem.ID]
		assert.True(ok)
		assert.Equal(elem, v)
	}
}

func BenchmarkToMapWithSize(b *testing.B) {
	/*
		goos: windows
		goarch: amd64
		pkg: github.com/MiaoSiLa/live-service/util
		BenchmarkToMap-8   	   52993	     19912 ns/op	    3735 B/op	     105 allocs/op
		PASS
		ok  	github.com/MiaoSiLa/live-service/util	1.374s
		Success: Benchmarks passed.
	*/
	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 100)
	for i := 0; i < 100; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ToMapWithSize(inputs, "ID", 100)
	}
}

func BenchmarkToMapWithSizeGroundTruth(b *testing.B) {
	/*
		goos: windows
		goarch: amd64
		pkg: github.com/MiaoSiLa/live-service/util
		BenchmarkToMapGroundTruth-8   	  211090	      5008 ns/op	    2896 B/op	       3 allocs/op
		PASS
		ok  	github.com/MiaoSiLa/live-service/util	1.208s
		Success: Benchmarks passed.
	*/
	type foo struct {
		ID   int
		Name string
	}

	inputs := make([]*foo, 100)
	for i := 0; i < 100; i++ {
		inputs[i] = &foo{
			ID:   i,
			Name: "A",
		}
	}

	fn := func(list []*foo) (result map[int]*foo) {
		result = make(map[int]*foo, 100)
		for _, v := range list {
			result[v.ID] = v
		}
		return result
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		fn(inputs)
	}
}

func TestSmartUserContext(t *testing.T) {
	assert := assert.New(t)

	smartC := SmartUserContext{
		UID:         10,
		UserToken:   "testtoken",
		UserEquipID: "test-equip-id",
		IP:          "127.0.0.1",
		Req:         httptest.NewRequest("GET", "/health", nil),
		UA:          "live-service/0.0.1",
	}
	var c UserContext
	convertToUserContext := func() {
		c = interface{}(smartC).(UserContext)
	}
	assert.NotPanics(convertToUserContext)
	assert.Equal(int64(10), c.UserID())
	assert.Equal("testtoken", c.Token())
	assert.Equal("test-equip-id", c.EquipID())
	assert.Equal("127.0.0.1", c.ClientIP())
	assert.Equal("/health", c.Request().URL.Path)
	assert.Equal("live-service/0.0.1", c.UserAgent())
}

func TestCleanString(t *testing.T) {
	assert := assert.New(t)

	str := " 测😃123ABC "
	r := CleanString(str)
	assert.Equal("测123abc", r)

	str = " 测😍😃🍔⚽String❤💡🐦🐝🔥试 "
	r = CleanString(str)
	assert.Equal("测string试", r)

	str = "  😃     🍔 测⚽String❤💡🐦🐝🔥试 "
	r = CleanString(str)
	assert.Equal("测string试", r)

	str = "  测⚽String❤💡试 🐦    🐝     🔥 "
	r = CleanString(str)
	assert.Equal("测string试", r)

	str = "  测⚽String❤💡 试 🐦    🐝     🔥 "
	r = CleanString(str)
	assert.Equal("测string 试", r)
}

func TestBoolToInt(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, BoolToInt(false))
	assert.Equal(1, BoolToInt(true))
}

func TestIntToBool(t *testing.T) {
	assert := assert.New(t)

	assert.True(IntToBool(1))
	assert.True(IntToBool(-1))
	assert.True(IntToBool(-2))
	assert.False(IntToBool(0))
}

func TestDiffMonth(t *testing.T) {
	assert := assert.New(t)

	t1 := time.Date(2019, 12, 1, 6, 0, 0, 0, time.Local)
	t2 := time.Date(2020, 2, 1, 18, 0, 0, 0, time.Local)
	assert.Equal(-2, DiffMonth(t1, t2))

	t1 = time.Date(2020, 2, 29, 0, 0, 0, 0, time.Local)
	t2 = time.Date(2020, 3, 30, 0, 0, 0, 0, time.Local)
	assert.Equal(-1, DiffMonth(t1, t2))

	t1 = time.Date(2019, 5, 1, 0, 0, 0, 0, time.Local)
	t2 = time.Date(2020, 6, 1, 0, 0, 0, 0, time.Local)
	assert.Equal(-13, DiffMonth(t1, t2))
}

func TestClock(t *testing.T) {
	assert := assert.New(t)

	mc := Clock{Hour: 24}
	when := time.Date(2020, 01, 01, 12, 13, 14, 0, time.Local)
	c := MakeClock(when)
	assert.Equal(12, c.Hour)
	assert.Equal(13, c.Min)
	assert.Equal(14, c.Sec)
	assert.Equal(86400, mc.second())
	assert.True(c.Less(mc))
	assert.True(mc.Greater(c))
	assert.True(c.LessOrEqual(c))
	assert.True(mc.GreaterOrEqual(mc))

	t.Run("TestParseClock", func(t *testing.T) {
		var c Clock
		var format string
		helper := func() { c = ParseClock(format) }
		assert.NotPanics(helper)
		assert.Zero(c)
		format = "123"
		assert.PanicsWithValue("unsupported clock format: 123", helper)
		format = "hh:mm:ss"
		assert.Panics(helper)
		format = "12:mm:ss"
		assert.Panics(helper)
		format = "12:13:ss"
		assert.Panics(helper)
		format = "12:13:14"
		assert.NotPanics(helper)
		assert.Equal(Clock{12, 13, 14}, c)
	})
}

func TestSplitToInt64Array(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := SplitToInt64Array("1,2,3,4", ",")
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3, 4}, r)

	r, err = SplitToInt64Array("", ",")
	require.NoError(err)
	assert.Empty(r)

	_, err = SplitToInt64Array("test", ",")
	assert.Error(err)
}

func TestFindInt64Duplicates(t *testing.T) {
	assert := assert.New(t)

	list := []int64{1, 2, 3, 4, 5}
	repeatList := FindInt64Duplicates(list)
	assert.Len(repeatList, 0)

	list = []int64{1, 1, 2}
	repeatList = FindInt64Duplicates(list)
	assert.Equal([]int64{1}, repeatList)
}

func TestNewInt(t *testing.T) {
	assert := assert.New(t)

	a := 10
	b := NewInt(a)

	assert.Equal(a, *b)

	*b++
	assert.Equal(10, a)
}

func TestNewInt64(t *testing.T) {
	assert := assert.New(t)

	a := int64(10)
	b := NewInt64(a)

	assert.Equal(a, *b)

	*b++
	assert.Equal(int64(10), a)
}

func TestNewFloat64(t *testing.T) {
	assert := assert.New(t)

	a := 90.3
	b := NewFloat64(a)

	assert.Equal(a, *b)

	*b++
	assert.Equal(90.3, a)
}

func TestDiffInt64(t *testing.T) {
	assert := assert.New(t)
	a := []int64{0, 1, 2}
	var b []int64
	assert.ElementsMatch([]int64{0, 1, 2}, DiffInt64(a, b))
	assert.ElementsMatch([]int64{0, 1, 2}, DiffInt64(b, a))

	b = []int64{1, 2, 4, 5}
	assert.ElementsMatch([]int64{0, 4, 5}, DiffInt64(a, b))

	b = []int64{1, 2, 4, 5}
	assert.Empty(DiffInt64(b, b))
}

func TestDiffString(t *testing.T) {
	assert := assert.New(t)
	a := []string{"0", "1", "2"}
	var b []string
	assert.ElementsMatch([]string{"0", "1", "2"}, DiffString(a, b))
	assert.ElementsMatch([]string{"0", "1", "2"}, DiffString(b, a))

	b = []string{"1", "2", "4", "5"}
	assert.ElementsMatch([]string{"0", "4", "5"}, DiffString(a, b))

	b = []string{"1", "2", "4", "5"}
	assert.Empty(DiffString(b, b))
}

func TestSetDifferenceInt64(t *testing.T) {
	assert := assert.New(t)
	a := []int64{1, 2, 3}
	var b []int64
	assert.ElementsMatch(b, SetDifferenceInt64(b, a))

	b = []int64{1, 2}
	assert.ElementsMatch([]int64{3}, SetDifferenceInt64(a, b))

	b = []int64{1, 2, 4, 5}
	assert.ElementsMatch([]int64{3}, SetDifferenceInt64(a, b))

	b = []int64{1, 2, 4, 5}
	assert.Empty(SetDifferenceInt64(b, b))
}

func TestSetDifferenceString(t *testing.T) {
	assert := assert.New(t)
	a := []string{"0", "1", "2"}
	var b []string
	assert.ElementsMatch([]string{"0", "1", "2"}, SetDifferenceString(a, b))
	assert.Empty(SetDifferenceString(b, a))

	b = []string{"1", "2", "4", "5"}
	assert.ElementsMatch([]string{"0"}, SetDifferenceString(a, b))

	b = []string{"1", "2", "4", "5"}
	assert.Empty(SetDifferenceString(b, b))
}

func TestIntersectionInt64(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	a1 := []int64{1, 1}
	a2 := []int64{2, 2}
	intersection := IntersectionInt64(a1, a2)
	require.Empty(intersection)

	a1 = []int64{1, 2}
	a2 = []int64{2, 3}
	intersection = IntersectionInt64(a1, a2)
	require.NotEmpty(intersection)
	assert.Equal([]int64{2}, IntersectionInt64(a1, a2))

	a1 = []int64{1, 2}
	a2 = []int64{3, 4}
	intersection = IntersectionInt64(a1, a2)
	require.Empty(intersection)
}

func TestGroupInt64Slices(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var s1 []int64
	length := 2
	result := GroupInt64Slices(s1, length)
	require.Len(result, 1)
	assert.Empty(result[0])

	s1 = make([]int64, 0)
	result = GroupInt64Slices(s1, length)
	require.Len(result, 1)
	assert.Empty(result[0])

	s1 = []int64{1, 2, 3}
	result = GroupInt64Slices(s1, length)
	require.Len(result, 2)
	assert.Equal(result, [][]int64{{1, 2}, {3}})

	s1 = []int64{1, 2, 3, 4}
	result = GroupInt64Slices(s1, length)
	require.Len(result, 2)
	assert.Equal(result, [][]int64{{1, 2}, {3, 4}})
}

func TestIncludes(t *testing.T) {
	assert := assert.New(t)

	list := []int64{1, 2, 3, 4, 5, 6}
	assert.True(Includes(len(list), func(i int) bool {
		return list[i] == 2
	}))

	assert.False(Includes(len(list), func(i int) bool {
		return list[i] == 7
	}))

	list = []int64{}
	assert.False(Includes(len(list), func(i int) bool {
		return list[i] == 2
	}))
}

func TestFindIndex(t *testing.T) {
	assert := assert.New(t)

	list := []int64{1, 2, 3, 4, 5, 6, 1, 2}
	assert.Equal(1, FindIndex(len(list), func(i int) bool {
		return list[i] == 2
	}), "测试总是返回第一个符合的元素")

	assert.Equal(-1, FindIndex(len(list), func(i int) bool {
		return list[i] == 7
	}), "测试找不到返回 -1")

	assert.Equal(-1, FindIndex(3, func(i int) bool {
		return list[i] == 6
	}), "测试查询不会超过 length")

	list = []int64{}
	assert.Equal(-1, FindIndex(len(list), func(i int) bool {
		return list[i] == 2
	}))
}

func TestEscapeSQL(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(EscapeSQL(""))
	assert.Equal(`\r\n\\\'\"\Z `, EscapeSQL(string([]byte{'\r', '\n', '\\', '\'', '"', '\032', ' '})))

	assert.Equal(`SELECT * FROM gift WHERE name = 'gift\';show tables;--'`,
		fmt.Sprintf("SELECT * FROM gift WHERE name = '%s'", EscapeSQL("gift';show tables;--")),
	)
}

func TestIsColor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok, err := IsColor("1")
	require.Equal(ErrInvalidHexColorFormat, err)
	assert.False(ok)
	ok, err = IsColor("#qqqqqq")
	require.NoError(err)
	assert.False(ok)
	ok, err = IsColor("#fffffq")
	require.NoError(err)
	assert.False(ok)
	ok, err = IsColor("#FfF")
	require.Equal(ErrInvalidHexColorFormat, err)
	assert.False(ok)
	ok, err = IsColor("#ffFFFF")
	require.NoError(err)
	assert.False(ok)

	colorList := []string{
		"#000000",
		"#FFFFFF",
		"#ffffff",
		fmt.Sprintf("#%06X", rand.Int63n(int64(math.Pow(16, 6)))),
	}
	for _, color := range colorList {
		ok, err = IsColor(color)
		require.NoError(err)
		assert.True(ok)
	}
}

func TestNewBitMaskFromFlag(t *testing.T) {
	assert := assert.New(t)

	assert.EqualValues(0, NewBitMaskFromFlag(0))
	assert.EqualValues(4, NewBitMaskFromFlag(3))
}

func TestIsOdd(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsOdd(0))
	assert.True(IsOdd(1))
}

func TestIsValidURL(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsValidURL("", true))
	assert.False(IsValidURL("oss:12345", false))
	assert.False(IsValidURL("http://test.com", false))
	assert.False(IsValidURL("httpstest", false))
	assert.False(IsValidURL("missevan://test", false))

	assert.True(IsValidURL("https://test", false))
	assert.True(IsValidURL("missevan://test", true))
}

func TestIsValidClip(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsValidClip("_1_1_1"))
	assert.False(IsValidClip("__1_1"))
	assert.False(IsValidClip("1__1_1"))
	assert.False(IsValidClip("1_1_1_"))
	assert.False(IsValidClip("1_1__1"))
	assert.False(IsValidClip("____"))

	assert.True(IsValidClip("1_1_1_1"))
}

func TestBuildMsgID(t *testing.T) {
	assert := assert.New(t)

	msgID := BuildMsgID(1, 1, "[表情包]", "test", goutil.Web)
	assert.Len(msgID, 36)
	assert.Equal("10f2baba", msgID[len(msgID)-8:])
	assert.Equal("3", msgID[:1])

	msgID = BuildMsgID(8, 1, "[表情包]", "test", goutil.Android)
	assert.Len(msgID, 36)
	assert.Equal("e84e574e", msgID[len(msgID)-8:])
	assert.Equal("1", msgID[:1])

	msgID = BuildMsgID(20, 1, "[表情包]", "test", goutil.IOS)
	assert.Len(msgID, 36)
	assert.Equal("03ace9b3", msgID[len(msgID)-8:])
	assert.Equal("2", msgID[:1])
}

func TestIsHalfWidthChar(t *testing.T) {
	assert := assert.New(t)

	// 半角
	assert.True(IsHalfWidthChar('n'))
	assert.True(IsHalfWidthChar('1'))
	assert.True(IsHalfWidthChar(' '))
	assert.True(IsHalfWidthChar('!'))
	assert.True(IsHalfWidthChar('~'))

	// 全角
	assert.False(IsHalfWidthChar('Ｎ'))
	assert.False(IsHalfWidthChar('～'))

	// 中文
	assert.False(IsHalfWidthChar('中'))
	assert.False(IsHalfWidthChar('文'))
	assert.False(IsHalfWidthChar('字'))
}
