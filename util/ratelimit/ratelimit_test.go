package ratelimit

import (
	"bytes"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type constantLimiter struct {
	d time.Duration

	took int64
}

func (l *constantLimiter) Take(n int64) time.Duration {
	l.took += n
	return l.d * time.Duration(n)
}

func (l *constantLimiter) Return(n int64, d time.Duration) {
	l.took -= n
}

func TestLimitReaderFactory(t *testing.T) {
	require := require.New(t)

	l := &constantLimiter{}
	factory := NewLimitReaderFactory(l)
	require.NotNil(factory)

	r := factory.NewReader(&bytes.Buffer{})
	require.NotNil(r)
}

func TestReader(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &constantLimiter{d: time.Millisecond}

	data := bytes.NewBuffer(make([]byte, 200))
	r := NewReader(data, l)
	require.NotNil(r)

	buf := make([]byte, 128)
	t0 := goutil.TimeNow()
	n, err := r.Read(buf)
	require.NoError(err)
	assert.Equal(128, n)
	assert.EqualValues(128, l.took)
	d := time.Since(t0)
	assert.GreaterOrEqual(d, time.Millisecond*128)
	assert.Less(d, time.Millisecond*200)

	n, err = r.Read(buf)
	require.NoError(err)
	assert.Equal(200-128, n)
	assert.EqualValues(200, l.took)
}

func TestScaledLimiter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &constantLimiter{d: time.Millisecond}

	sl := NewScaledLimiter(l, 2)
	require.NotNil(sl)

	data := bytes.NewBuffer(make([]byte, 200))
	r := NewReader(data, sl)
	require.NotNil(r)

	buf := make([]byte, 128)
	t0 := goutil.TimeNow()
	n, err := r.Read(buf)
	require.NoError(err)
	assert.Equal(128, n)
	assert.EqualValues(64, l.took)
	d := time.Since(t0)
	assert.GreaterOrEqual(d, time.Millisecond*64)
	assert.Less(d, time.Millisecond*128)

	n, err = r.Read(buf)
	require.NoError(err)
	assert.Equal(200-128, n)
	assert.EqualValues(128, l.took)
}

func TestIOLimiter(t *testing.T) {
	require := require.New(t)

	l := &constantLimiter{d: time.Millisecond}

	iol := NewIOLimiter(l)
	require.NotNil(iol)
}
