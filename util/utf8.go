package util

import (
	"unicode/utf8"

	"github.com/rivo/uniseg"
)

// UTF8Width utf-8 字符的显示宽度
func UTF8Width(str string) int {
	var width int
	g := uniseg.NewGraphemes(str)
	for g.Next() {
		width += runeWidth(g.<PERSON><PERSON>())
	}
	return width
}

func runeWidth(runes []rune) int {
	var width int
	for i := range runes {
		width += utf8.RuneLen(runes[i])
		if width >= 2 {
			return 2
		}
	}
	return width
}

// UTF8SubStrByWidth 宽度不大于 width 的前几位子字符串
// 例子见测试
func UTF8SubStrByWidth(s string, width int) string {
	var size int
	g := uniseg.NewGraphemes(s)
	for g.Next() {
		size += runeWidth(g.Run<PERSON>())
		if size > width {
			start, _ := g.Positions()
			return s[0:start]
		}
	}
	return s
}
