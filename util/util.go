package util

import (
	"encoding/json"
	"errors"
	"fmt"
	"hash/crc32"
	"math"
	"math/rand"
	"net/http"
	"net/url"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// time second
const (
	SecondOneDay    int64 = 24 * 60 * 60 // 一天时长（单位：秒）
	SecondOneHour   int64 = 60 * 60      // 一小时时长（单位：秒）
	SecondOneMinute int64 = 60           // 一分钟时长（单位：秒）
)

// TimeFormats
const (
	TimeFormatYM                   = "2006-01"
	TimeFormatYMD                  = "2006-01-02"
	TimeFormatYMDHMS               = "2006-01-02 15:04:05"
	TimeFormatHHMM                 = "15:04"
	TimeFormatHHMMSS               = "15:04:05"
	TimeFormatYMDHHMM              = "2006-01-02 15:04"
	TimeFormatYMDHHMMSSWithNoSpace = "************05"
	TimeFormatYMWithNoSpace        = "200601"
	TimeFormatChineseYMD           = "2006 年 01 月 02 日"
	TimeFormatYMDWithNoSpace       = "20060102"
	TimeFormatYMDHHWithNoSpace     = "2006010215"
	TimeFormatYMDHHMMWithNoSpace   = "************"
	TimeFormatSS                   = "05"
	TimeFormatYMWithDot            = "2006.01"
)

// ErrInvalidMsgID 无效的消息 ID
var ErrInvalidMsgID = errors.New("invalid msg id")

// Clock 时钟
// TODO: 迁移到 missevan-go
// TODO: 支持跨天操作
type Clock struct {
	Hour int
	Min  int
	Sec  int
}

// MakeClock 构造 Clock
func MakeClock(t time.Time) Clock {
	h, m, s := t.Clock()
	return Clock{
		Hour: h,
		Min:  m,
		Sec:  s,
	}
}

// ParseClock 从格式化的时钟获取时间
// 只支持 hh:mm:ss 格式和空字符串
// TODO: 降级 panic 为 error
func ParseClock(s string) (c Clock) {
	if s == "" {
		return
	}
	pre := strings.Split(s, ":")
	if len(pre) != 3 {
		panic(fmt.Sprintf("unsupported clock format: %s", s))
	}
	var err error
	c.Hour, err = strconv.Atoi(pre[0])
	if err != nil {
		panic(err)
	}
	c.Min, err = strconv.Atoi(pre[1])
	if err != nil {
		panic(err)
	}
	c.Sec, err = strconv.Atoi(pre[2])
	if err != nil {
		panic(err)
	}
	return
}

func (c Clock) second() int {
	return c.Hour*3600 + c.Min*60 + c.Sec
}

// Less c 比 right 更早
func (c Clock) Less(right Clock) bool {
	return c.second() < right.second()
}

// Greater c 比 right 更晚
func (c Clock) Greater(right Clock) bool {
	return c.second() > right.second()
}

// LessOrEqual c 比 right 更早或相等
func (c Clock) LessOrEqual(right Clock) bool {
	return !c.Greater(right)
}

// GreaterOrEqual c 比 right 更晚或相等
func (c Clock) GreaterOrEqual(right Clock) bool {
	return !c.Less(right)
}

// TimeToUnixMilli returns t as a Unix time, the number of millseconds elapsed
// since January 1, 1970 UTC. The result does not depend on the
// location associated with t.
func TimeToUnixMilli(t time.Time) int64 {
	return t.UnixNano() / 1e6
}

// UnixMilliToTime 毫秒时间戳转换成 time.Time
func UnixMilliToTime(milli int64) time.Time {
	return time.Unix(0, milli*1e6)
}

// WeekdayToInt 将 time.Weekday 转换成星期一作为起始的 int 值
func WeekdayToInt(weekDay time.Weekday) int {
	return (int(weekDay) + 6) % 7
}

// Float2DP is a type of float with 2 decimal places when encoded to json
// 超过两位小数时使用银行家舍入法：
// 四舍六入五考虑，五后非空就进一，五后为空向前看奇偶，五前为偶应舍去，五前为奇要进一
// https://zh.wikipedia.org/wiki/%E6%95%B0%E5%80%BC%E4%BF%AE%E7%BA%A6
type Float2DP float64

// MarshalJSON implements the Marshaler interface
func (f Float2DP) MarshalJSON() ([]byte, error) {
	s := f.String()
	return []byte(s), nil
}

// String 返回格式化字符串
func (f Float2DP) String() string {
	return strconv.FormatFloat(float64(f), 'f', 2, 64)
}

// UnmarshalJSON implements the Unmarshaler interface
func (f *Float2DP) UnmarshalJSON(bytes []byte) error {
	var v interface{}
	err := json.Unmarshal(bytes, &v)
	if err != nil {
		return err
	}

	if v == nil {
		// JSON null
		return nil
	}

	switch t := v.(type) {
	case float64:
		*f = Float2DP(t)
	case string:
		n, err := strconv.ParseFloat(t, 64)
		if err != nil {
			return err
		}
		*f = Float2DP(n)
	default:
		return fmt.Errorf("json: cannot unmarshal %q into Go value of type Float2DP", bytes)
	}
	return nil
}

// Coin 用于投硬币，记录正面朝上的概率
// (-inf, 0.0] 代表一直反面
// (0.0, 1.0) 有正有反
// [1.0, +inf] 一直正面
type Coin float64

// Flip 投硬币，true 代表正面
func (c Coin) Flip() bool {
	return rand.Float64() <= float64(c)
}

// Uniq 去重
// TODO: 用接口重写以支持更多
func Uniq(arrs []int64) []int64 {
	set := make(map[int64]struct{}, len(arrs))
	for i := 0; i < len(arrs); i++ {
		set[arrs[i]] = struct{}{}
	}
	arrs = make([]int64, 0, len(set))
	for elem := range set {
		arrs = append(arrs, elem)
	}
	return arrs
}

// ParseYearMonth parses "2006-01" formated date string.
// e.g. ParseYearMonth("2006-01") returns (time.Date(2006, 1, 1, 0, 0, 0, 0, time.Local), 200601, nil).
func ParseYearMonth(month string) (time.Time, int, error) {
	tm, err := time.ParseInLocation("2006-01", month, time.Local)
	if err != nil {
		return tm, 0, err
	}
	return tm, YearMonthInt(tm), nil
}

// YearMonthInt combines t.Year() and t.Month() into a number.
// e.g. YearMonthInt(time.Date(2006, 1, 2, 3, 4, 5, 6, time.Local)) returns 200601.
func YearMonthInt(t time.Time) int {
	return t.Year()*100 + int(t.Month())
}

// BeginningOfMonth returns the beginning of the month of time t
func BeginningOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// EndOfMonth returns the end of the month of time t
func EndOfMonth(t time.Time) time.Time {
	return BeginningOfMonth(t).AddDate(0, 1, 0)
}

// BeginningOfDay 一天的开始
func BeginningOfDay(t time.Time) time.Time {
	y, m, d := t.Date()
	return time.Date(y, m, d, 0, 0, 0, 0, t.Location())
}

// BeginningOfWeek 一周的开始（周一）
func BeginningOfWeek(t time.Time) time.Time {
	weekday := WeekdayToInt(t.Weekday())
	t = BeginningOfDay(t)
	return t.Add(-24 * time.Hour * time.Duration(weekday))
}

// EndOfWeek returns the end of the week of time t
func EndOfWeek(t time.Time) time.Time {
	return BeginningOfWeek(t.AddDate(0, 0, 7))
}

// RandomCode 获取由数字组成的固定位数字符串
func RandomCode(length int) string {
	maxNum := int(math.Pow10(length)) - 1
	return fmt.Sprintf("%0*d", length, rand.Intn(maxNum))
}

// UserContext 用户上下文
// Deprecated: use missevan-go UserContext
type UserContext interface {
	Token() string
	ClientIP() string
	EquipID() string
	UserID() int64
	UserAgent() string
	Request() *http.Request
}

// SmartUserContext smart UserContext
// Deprecated: use missevan-go SmartUserContext
type SmartUserContext struct {
	UID         int64
	IP          string
	UserToken   string
	UserEquipID string
	UA          string
	Req         *http.Request
}

// UserID 用户 ID
func (c SmartUserContext) UserID() int64 {
	return c.UID
}

// ClientIP 客户端 ip
func (c SmartUserContext) ClientIP() string {
	return c.IP
}

// Token 用户 token
func (c SmartUserContext) Token() string {
	return c.UserToken
}

// EquipID 用户 equip_id
func (c SmartUserContext) EquipID() string {
	return c.UserEquipID
}

// UserAgent User-Agent
func (c SmartUserContext) UserAgent() string {
	return c.UA
}

// Request 返回 http 请求
// NOTICE: 可能是 nil
func (c SmartUserContext) Request() *http.Request {
	return c.Req
}

// ToMapWithSize transforms a slice of structs to a map based on a pivot field: []*Foo => Map<int, *Foo>
// Code from https://github.com/thoas/go-funk, with slight modification.
// n is the pre-allocated size of the map.
// NOTICE: This function should be used in the same package where the struct type of in is defined, to avoid typed coupling.
// 从 benchmark 的结果看，运行速度相比原生写法，在数量级上差不太多
func ToMapWithSize(in interface{}, pivot string, n int) interface{} {
	value := reflect.ValueOf(in)

	// input value must be a slice
	if value.Kind() != reflect.Slice {
		panic("input must be a slice")
	}

	sliceType := value.Type()

	// retrieve the struct in the slice to deduce key type
	structType := sliceType.Elem()
	if structType.Kind() == reflect.Ptr {
		structType = structType.Elem()
	}

	keyField, ok := structType.FieldByName(pivot)
	if !ok {
		return nil
	}

	// value of the map will be the input type
	collectionType := reflect.MapOf(keyField.Type, sliceType.Elem())

	// create a map from scratch
	collection := reflect.MakeMapWithSize(collectionType, n)

	for i := 0; i < value.Len(); i++ {
		element := value.Index(i)
		key := reflect.Indirect(element).FieldByName(pivot)
		collection.SetMapIndex(key, element)
	}

	return collection.Interface()
}

// ToMap is a generic function that converts a slice of structs to a map.
// K is the type of the keys in the map.
// V is the type of the values (the structs) in the map.
func ToMap[K comparable, V any](slice []V, keyFunc func(V) K) map[K]V {
	result := make(map[K]V, len(slice))
	for _, v := range slice {
		result[keyFunc(v)] = v
	}
	return result
}

// emoji
var regEmojiSpace = regexp.MustCompile(`[\x{1F600}-\x{1F6FF}|[\x{2600}-\x{26FF}]|[\x{1F300}-\x{1F5FF}]|[\x{2700}-\x{27BF}]`)

// CleanString lower case and remove emoji、space
func CleanString(str string) string {
	str = regEmojiSpace.ReplaceAllString(strings.ToLower(str), "")
	return strings.TrimSpace(str)
}

// BoolToInt bool 转 int
func BoolToInt(b bool) int {
	if b {
		return 1
	}
	return 0
}

// IntToBool int 转 bool
func IntToBool(i int) bool {
	return i != 0
}

// DiffMonth diffs the month of t1, t2.
// t1 = 2019-12-01, t2 = 2020-02-01, then DiffMonth(t1, t2) is -2.
func DiffMonth(t1 time.Time, t2 time.Time) int {
	y1, m1, _ := t1.Date()
	y2, m2, _ := t2.Date()
	return (y1*12 + int(m1)) - (y2*12 + int(m2))
}

// SplitToInt64Array split str with sep, return int64 array
func SplitToInt64Array(s, sep string) ([]int64, error) {
	if s == "" {
		return []int64{}, nil
	}

	splitList := strings.Split(s, sep)
	result := make([]int64, len(splitList))
	for i, v := range splitList {
		id, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, err
		}
		result[i] = id
	}
	return result, nil
}

// FindInt64Duplicates 检测并返回重复数字
func FindInt64Duplicates(arr []int64) []int64 {
	duplicates := make([]int64, 0, 8) //
	m := make(map[int64]int, len(arr))
	for _, v := range arr {
		m[v]++
	}
	for k, v := range m {
		if v > 1 {
			duplicates = append(duplicates, k)
		}
	}
	return duplicates
}

// NewInt creates new int pointer
func NewInt(i int) *int {
	return &i
}

// NewInt64 creates new int64 pointer
func NewInt64(i int64) *int64 {
	return &i
}

// NewFloat64 creates new float64 pointer
func NewFloat64(i float64) *float64 {
	return &i
}

// DiffInt64 returns diff elem from two slice, 传入 [1,2,3], [2,3,4] 返回 [1,4]
func DiffInt64(s1, s2 []int64) []int64 {
	checksMap := make(map[int64]int)
	for i := 0; i < len(s1); i++ {
		checksMap[s1[i]] = 1
	}
	for i := 0; i < len(s2); i++ {
		checksMap[s2[i]] |= 2
	}

	res := make([]int64, 0, len(checksMap))
	for key, status := range checksMap {
		if status != 3 {
			res = append(res, key)
		}
	}
	return res
}

// DiffString returns diff elem from two slice, 传入 ["1","2","3"], ["2","3","4"] 返回 ["1","4"]
func DiffString(s1, s2 []string) []string {
	checksMap := make(map[string]int)
	for i := 0; i < len(s1); i++ {
		checksMap[s1[i]] = 1
	}
	for i := 0; i < len(s2); i++ {
		checksMap[s2[i]] |= 2
	}

	res := make([]string, 0, len(checksMap))
	for key, status := range checksMap {
		if status != 3 {
			res = append(res, key)
		}
	}
	return res
}

// SetDifferenceInt64 returns elem in s1 but not in s2, 差集, 传入 [1,2,3], [2,3,4] 返回 [1]
func SetDifferenceInt64(s1, s2 []int64) []int64 {
	s2Map := make(map[int64]struct{}, len(s2))
	for i := 0; i < len(s2); i++ {
		s2Map[s2[i]] = struct{}{}
	}
	res := make([]int64, 0, len(s1))
	for _, v := range s1 {
		if _, ok := s2Map[v]; !ok {
			res = append(res, v)
		}
	}
	return res
}

// SetDifferenceString returns elem in s1 but not in s2, 差集, 传入 ["1","2","3"], ["2","3","4"] 返回 ["1"]
func SetDifferenceString(s1, s2 []string) []string {
	s2Map := make(map[string]struct{}, len(s2))
	for i := 0; i < len(s2); i++ {
		s2Map[s2[i]] = struct{}{}
	}
	res := make([]string, 0, len(s1))
	for _, v := range s1 {
		if _, ok := s2Map[v]; !ok {
			res = append(res, v)
		}
	}
	return res
}

// IntersectionInt64 get duplicated elements distincted from two arrays, 交集
func IntersectionInt64(a1, a2 []int64) []int64 {
	pending := make(map[int64]int)

	for i := 0; i < len(a1); i++ {
		pending[a1[i]] = 1
	}
	for i := 0; i < len(a2); i++ {
		pending[a2[i]] |= 2
	}
	intersection := make([]int64, 0, len(pending))
	for keys, status := range pending {
		if status == 3 {
			intersection = append(intersection, keys)
		}
	}

	return intersection
}

// GroupInt64Slices returns a new slice contains slices with maximum length each
func GroupInt64Slices(from []int64, each int) [][]int64 {
	result := make([][]int64, 0, len(from)/each+1)
	for n := 0; ; n += each {
		if n+each >= len(from) {
			result = append(result, from[n:])
			break
		}
		result = append(result, from[n:n+each])
	}

	return result
}

// Includes The method determines whether an array includes a certain value among its entries, returning true or false as appropriate
func Includes(length int, fn func(i int) bool) bool {
	for i := 0; i < length; i++ {
		if fn(i) {
			return true
		}
	}
	return false
}

// FindIndex The method returns the index of the first element in the array
// that satisfies the provided testing function. Otherwise,
// it returns -1, indicating that no element passed the test.
func FindIndex(length int, fn func(i int) bool) int {
	for i := 0; i < length; i++ {
		if fn(i) {
			return i
		}
	}
	return -1
}

// EscapeSQL mysql escape
// TODO: 迁移到 missevan-go
func EscapeSQL(source string) string {
	var j int
	if len(source) == 0 {
		return ""
	}
	tempStr := source[:]
	desc := make([]byte, len(tempStr)*2)
	for i := 0; i < len(tempStr); i++ {
		flag := false
		var escape byte
		switch tempStr[i] {
		case '\r':
			flag = true
			escape = 'r'
		case '\n':
			flag = true
			escape = 'n'
		case '\\':
			flag = true
			escape = '\\'
		case '\'':
			flag = true
			escape = '\''
		case '"':
			flag = true
			escape = '"'
		case '\032':
			flag = true
			escape = 'Z'
		default:
		}
		if flag {
			desc[j] = '\\'
			desc[j+1] = escape
			j = j + 2
		} else {
			desc[j] = tempStr[i]
			j = j + 1
		}
	}
	return string(desc[0:j])
}

var (
	// ErrInvalidHexColorFormat 十六进制颜色格式错误
	ErrInvalidHexColorFormat = errors.New("invalid hex color format")

	colorReg = regexp.MustCompile(`^#([A-F\d]{6}|[a-f\d]{6})$`)
)

// IsColor 是否是合法的十六进制颜色代码
// NOTICE: 不支持三字符颜色代码
func IsColor(s string) (bool, error) {
	if len(s) != 7 {
		return false, ErrInvalidHexColorFormat
	}
	if !colorReg.MatchString(s) {
		return false, nil
	}
	return true, nil
}

// NewBitMaskFromFlag new bit mask, 按照第几位来新建
func NewBitMaskFromFlag(i int) goutil.BitMask {
	var b goutil.BitMask
	if i > 0 {
		b.Set(i)
	}
	return b
}

// IsValidURL is valid url. https://xxxxx or missevan://xxxxx
func IsValidURL(paramURL string, allowMissevanScheme bool) bool {
	u, err := url.Parse(paramURL)
	if err != nil {
		return false
	}

	return u.Scheme == "https" || (allowMissevanScheme && u.Scheme == "missevan")
}

// IsValidClip is valid clip.
// clip format: top_right_bottom_left
func IsValidClip(clip string) bool {
	trbl, err := SplitToInt64Array(clip, "_")
	return err == nil && len(trbl) == 4
}

// BuildMsgID build msg_id
// {C}b4290d2-1d1b-40e4-8c80-ff93{hex(crc32)}
// {C}: 平台标识 Android 设备为 "1" ，iOS 设备为 "2"，Web 为 "3"
// {crc32}: crc32(string(user_id),string(room_id),trim(string(消息内容)),feedback_key)
func BuildMsgID(userID, roomID int64, message, feedbackKey string, os goutil.Platform) string {
	var prefix string
	switch os {
	case goutil.Android:
		prefix = "1"
	case goutil.IOS:
		prefix = "2"
	default:
		prefix = "3"
	}
	suffixCrc := crc32.ChecksumIEEE([]byte(fmt.Sprintf("%d,%d,%s,%s",
		userID, roomID, strings.TrimSpace(message), feedbackKey)))
	suffixCrcHex := fmt.Sprintf("%08x", suffixCrc)
	uid := uuid.NewString()
	return fmt.Sprintf("%s%s%s", prefix, uid[1:len(uid)-8], suffixCrcHex)
}

// IsHalfWidthChar 判断字符是否是半角字符
func IsHalfWidthChar(r rune) bool {
	return r <= 127
}
