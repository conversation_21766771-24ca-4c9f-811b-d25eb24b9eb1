package util

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsProdEnv(t *testing.T) {
	assert := assert.New(t)

	envPre := os.Getenv(EnvDeploy)
	defer func() {
		os.Setenv(EnvDeploy, envPre)
	}()
	os.Setenv(EnvDeploy, "")
	assert.False(IsProdEnv())
	os.Setenv(EnvDeploy, DeployEnvProd)
	assert.True(IsProdEnv())
	os.Setenv(EnvDeploy, DeployEnvPre)
	assert.True(IsProdEnv())
}
