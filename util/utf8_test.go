package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUTF8Width(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, UTF8Width(""))
	assert.Equal(1, UTF8Width("a"))
	assert.Equal(3, UTF8Width("aaa"))
	assert.Equal(4, UTF8Width("测试"))
	assert.Equal(3, UTF8Width("测a"))
	assert.Equal(2, UTF8Width("😅"))
	assert.Equal(10, UTF8Width("啊啊😅aa👷🏽‍♀️"))
}

func TestUTF8SubStrByWidth(t *testing.T) {
	assert := assert.New(t)

	str := "测试abc测"
	expected := []string{
		"", "", "测", "测", "测试", "测试a", "测试ab", "测试abc", "测试abc", "测试abc测",
	}
	for i := range expected {
		assert.Equal(expected[i], UTF8SubStrByWidth(str, i), i)
	}

	assert.Equal("啊啊😅aa", UTF8SubStrByWidth("啊啊😅aa👷🏽‍♀️", 9))
	assert.Equal("啊啊😅aa👷🏽‍♀️", UTF8SubStrByWidth("啊啊😅aa👷🏽‍♀️", 10))
}
