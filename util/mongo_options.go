package util

import "go.mongodb.org/mongo-driver/mongo/options"

// TODO: 迁移到 package mongodb 下面

// FindOptions 获取 find options
func FindOptions(opts []*options.FindOptions) *options.FindOptions {
	if len(opts) != 0 {
		return opts[0]
	}
	return options.Find()
}

// FindOneOptions 获取 findOneOptions
func FindOneOptions(opts []*options.FindOneOptions) *options.FindOneOptions {
	if len(opts) != 0 {
		return opts[0]
	}
	return options.FindOne()
}
