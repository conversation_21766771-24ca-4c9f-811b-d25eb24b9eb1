package params

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// RoomURL 获取直播间 url
func RoomURL(roomID int64) string {
	return fmt.Sprintf("%slive/%d", config.Conf.Params.URL.Live, roomID)
}

// PurchasedURL 获取已购页 url
func PurchasedURL() string {
	return fmt.Sprintf("%smperson/purchased", config.Conf.Params.URL.Main)
}

// UserHomepageURL 获取个人主页 url
func UserHomepageURL(userID int64) string {
	return fmt.Sprintf("%s%d", config.Conf.Params.URL.Main, userID)
}

// LuckyBagRedeemURL 获取福袋兑奖页 url
func LuckyBagRedeemURL(luckyBagID int64) string {
	return fmt.Sprintf("%suser/luckybag/redeem?lucky_bag_id=%d", config.Conf.Params.URL.Live, luckyBagID)
}

// ParseRoomIDByRoomURL 获取直播间链接中的 roomID
func ParseRoomIDByRoomURL(roomURL string) (int64, bool) {
	prefix := fmt.Sprintf("%slive/", config.Conf.Params.URL.Live)
	prefixURL, err := url.Parse(prefix)
	if err != nil {
		logger.WithField("prefix", prefix).Error(err)
		// PASS
		return 0, false
	}
	prefixURL = &url.URL{
		Host: prefixURL.Host,
		Path: prefixURL.Path,
	}

	parseBannerURL, err := url.Parse(roomURL)
	if err != nil {
		logger.WithField("room_url", roomURL).Error(err)
		// PASS
		return 0, false
	}
	parsedURL := &url.URL{
		Host: parseBannerURL.Host,
		Path: parseBannerURL.Path,
	}
	if roomID, ok := strings.CutPrefix(parsedURL.String(), prefixURL.String()); ok {
		roomID, err := strconv.ParseInt(roomID, 10, 64)
		if err != nil {
			return 0, false // 解析不到 roomID 时，按照非直播 URL 处理
		}
		return roomID, true
	}
	return 0, false
}

// BlackCardURL 黑卡活动 URL，传入 fromRoom 为 true 时需要拼接 roomID
func BlackCardURL(fromRoom bool) string {
	openURL := config.Conf.Params.BlackCard.IntroOpenURL
	if openURL == "" {
		return ""
	}
	parsedURL, err := url.Parse(openURL)
	if err != nil {
		logger.Error(err)
		// PASS
		return ""
	}
	query := parsedURL.Query()
	query.Set("navhide", "1")
	if fromRoom {
		query.Set("from_room_id", "__ROOM_ID__")
	}
	parsedURL.RawQuery = query.Encode()
	return parsedURL.String()
}
