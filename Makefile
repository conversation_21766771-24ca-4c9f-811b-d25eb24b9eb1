NAME=live-service
VERSION=1.9.7-2
TOKEN=${GITHUB_TOKEN}
REGISTRY_PREFIX=$(if $(REGISTRY),$(addsuffix /, $(REGISTRY)))
PROXY=${BUILD_HTTP_PROXY}
MGOBRANCH?=release
APP_NAME?=${NAME}

.PHONY: build publish

check:
	@echo -e 'NAME=${NAME}\nVERSION=${VERSION}'
	@echo -e 'REGISTRY_PREFIX=${REGISTRY_PREFIX}\nPROXY=${PROXY}\nMGOBRANCH=${MG<PERSON><PERSON>ANCH}'

build:
	@echo -e 'NAME=${NAME}\nVERSION=${VERSION}\nPROXY=${PROXY}\n'
	@docker build --force-rm --build-arg version=${VERSION} \
		--build-arg proxy=${PROXY} \
		--build-arg mgobranch=${<PERSON><PERSON><PERSON><PERSON><PERSON>} \
		-t ${NAME}:${VERSION} .

publish:
	docker tag ${NAME}:${VERSION} ${REGISTRY_PREFIX}${NAME}:${VERSION}
	docker push ${REGISTRY_PREFIX}${NAME}:${VERSION}

version:
	@echo ${VERSION}

rider-build:
	@env proxy=${PROXY} mgobranch=${MGOBRANCH} ./gomod.sh
	env GOOS=linux CGO_ENABLED=0 go install -v -tags release -ldflags "-X main.Version=${VERSION}"
	@printf '#!/bin/sh\n\necho "hosts: files dns" > /etc/nsswitch.conf\nexec su-exec nobody /data/app/${APP_NAME}/${NAME} "$$@"\n' > /go/bin/docker-entrypoint.sh
	chmod +x /go/bin/docker-entrypoint.sh
