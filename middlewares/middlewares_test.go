package middlewares

import (
	"errors"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestIsRole(t *testing.T) {
	assert := assert.New(t)

	var u *user.User
	var userErr error
	userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
		return u, userErr
	})

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Set("user", userFunc)
	userErr = errors.New("test error")
	h := IsRole(role.LiveAdmin)
	h(c)
	assert.Equal(500, w.Code)
	assert.JSONEq(`{"code":100010500,"info":"服务器内部错误"}`, w.Body.String())

	userErr = nil
	u = new(user.User)
	u.ID = 999
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("user", userFunc)
	h(c)
	assert.Equal(403, w.Code)
	assert.JSONEq(`{"code":200020003,"info":"用户没有权限"}`, w.Body.String())

	u.ID = 10
	w = httptest.NewRecorder()
	c, _ = gin.CreateTestContext(w)
	c.Set("user", userFunc)
	h = IsRole(role.LiveOperator, role.LiveAdmin)
	h(c)
	assert.Equal(200, w.Code)
}
