package middlewares

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

// IsRole 是否具有某角色之一的中间件
func IsRole(roleNames ...role.Role) func(c *gin.Context) {
	if len(roleNames) == 0 {
		panic("roleNames is empty")
	}
	return func(c *gin.Context) {
		ctx := handler.Context{C: c}
		u := ctx.User()
		err := ctx.GetUserError()
		if err != nil {
			logger.Error(err)
			c.AbortWithStatusJSON(http.StatusInternalServerError, handler.M{
				"code": 100010500,
				"info": "服务器内部错误"})
			return
		}

		pass := false
		if u != nil {
			pass, err = role.IsRole(u.ID, roleNames...)
			if err != nil {
				logger.Error(err)
				c.AbortWithStatusJSON(http.StatusInternalServerError, handler.M{
					"code": 100010500,
					"info": "服务器内部错误"})
				return
			}
		}

		if !pass {
			c.AbortWithStatusJSON(actionerrors.ErrNoAuthority.Status, handler.M{
				"code": actionerrors.ErrNoAuthority.Code,
				"info": actionerrors.ErrNoAuthority.Message})
			return
		}
		c.Next()
	}
}
