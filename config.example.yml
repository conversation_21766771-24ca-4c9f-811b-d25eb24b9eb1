# a config for live-service
ab:
  # activity_time_offset 活动时间偏移，一般用正值，单位：秒
  activity_time_offset: 1036800 # 12 天，五月打榜用
  # slide_score_limit 滑动切换直播间热度截取阈值（小数表示）
  slide_score_limit: 0.8
  # 取消公会发起的邀请自动续约功能的时间
  cancel_guild_auto_renewal_time: 1625068800
  # 二级分区 web 端判断
  catalogs_for_old_web: true
  # 是否开放提现入口
  open_withdraw_revenue_entrance: false
  # 是否开放开通上神 默认时间 2022-05-20 00:00:00
  open_highness: 1652976000
  # 广播是否下发 avif url
  broadcast_avif_url: false
  # 开放新合约签约/续约可选时长时间 2023-02-15 00:00:00
  open_new_contract_duration_time: 1676390400
  # 使用新的用户消息统计 redis KEY，开始时间 2023-06-20 00:00:00
  new_usermeta_msg_count_time: 1687190400
  # 拆分 KEY，新 KEY 启用时间：2023-07-01 00:00:00
  enable_new_redis_key_duration_time: 1688140800
  # 开启用户升级 155 级礼物栏刷新通知
  enable_user_155_level_gift_notify: false
  # 是否启用公会签约/续约新通知
  enable_send_new_guild_sign_system_msg: true
  # 万事屋特定年份开始使用数据库保存用户的兑换积分，默认是 2024 年
  new_live_user_redeem_point_year: 2024
  # 85 级消费奖励开启时间：2024-04-01 00:00:00
  open_level_gte85_consumption_pack_time: 1711900800
  # 付费弹幕玩法开启时间：2024-04-01 12:00:00
  open_danmaku_time: 1711944000
  # 是否展示搜索页直播模块
  enable_show_search_live: true

worker:
  worker_num: 2 # default worker number is runtime.NumCPU()
  #queue_num: 20 # default queue number is 20
  address: '' # default: '', aka. any
  port: 3014 # default: 3014

web: # use http
  address: '' # default: '', aka. any
  port: 3013 # default: 3013
  check_channel: false # 是否检查推流实时状态，default: false
  horn_open: false # 是否开启全站喇叭，临时支持配置喇叭关闭开启状态

im: # use http and websocket
  address: '' # default: '', aka. any
  port: 3015 # default: 3015
  allow_test_user: false # default: false
  rpc_address: '' # default: '', aka. any
  rpc_port: 3011 # default: 3011
  pubsub_size: 100 # default: 10
  join_sign_hmac_key: 'test_key'
  join_uuid_valid_start_time: 1713872951 # uuid 风险检测所允许的最小的时间戳
  disable_im_high_risk_check: true # 关闭 im 高风险鉴权过滤付费消息

log:
  format: "string" # string or json
  access_log: "stdout" # stdout: output to console, or define log path like "log/access_log"
  access_level: "debug"
  error_log: "stderr" # stderr: output to console, or define log path like "log/error_log"
  error_level: "error"
  agent:
    enabled: false
    dsn: 'udp://************:8911'
    app_id: live-service
    # host: '' # log host, defaults to os.Getenv("HOST") or os.Hostname()
    # instance_id: '' # log instance_id, defaults to os.Getenv("INSTANCE_ID") or os.Hostname()

http:
  mode: 'release' # default: 'release'
  rpc_key: 'testkey'
  bili_app_key:
    testkey: 'testsecret'
  disable_api_sign: false # default: false
  api_sign_key: 'testkey'
  openapi_app_key_secret:
    testkey: 'testsecret'
  session_cookie_name: "FM_SESS"
  session_prefix: 'fm:sess'
  accid_prefix: '' # default: ''
  csrf_allow_top_domains:
  - 'missevan.com'
  - 'bilibili.com'

params:
  url:
    main: 'https://www.missevan.com/'
    live: 'https://fm.missevan.com/'
    cdn: 'oss://'
    avatar_url: 'oss://avatars/'
    profile_url: 'oss://profile/'
    default_icon_url: 'oss://avatars/icon01.png'
  general:
    room_tip: '<font color="#FFC525">系统提示：</font><font color="#FFFFFF">猫耳FM直播内容及互动评论须严格遵守直播规范，严禁传播违法违规、低俗血暴、吸烟酗酒、造谣诈骗等不良有害信息。</font>'
    feedback_key: 'test_key'
  noble:
    noble_details_url: 'https://fm.example.com/noble/privilege'
    noble_guide_url: 'https://link.missevan.com/fm/noble-guide'
    custom_noble_gift_url: 'https://fm.uat.missevan.com/noble/privilege'
    buy_noble_url: 'https://fm.uat.missevan.com/noble/privilege'
    invisible_icon: 'oss://avatars/invisible.png'
    default_card_frame: 'oss://gifts/cardframes/000.png'
    horn_access_intro: '开通或续费大咖及以上贵族可获得' # 获取特权途径介绍
    recommend_top_icon: 'oss://gifts/recommend/top/007-new.png'
    recommend_highness_top_icon: 'oss://live/recommend/highness/top/001.png'
    recommend_list_icon: 'oss://gifts/recommend/list/007.png'
    recommend_highness_list_icon: 'oss://live/recommend/highness/list/001.png'
    recommend_web_frame: 'oss://live/avatarframes/noble/007-recommend.png' # Web 神话推荐头像框
    recommend_web_highness_frame: 'oss://live/avatarframes/noble/highness/001-recommend.png' # Web 上神推荐头像框
    recommend_avatar_frame: 'oss://gifts/recommendframes/007_3_177_3_21.png' # name_top_right_bottom_left.png, App 神话推荐头像框
    recommend_highness_avatar_frame: 'oss://live/recommendframes/highness/001_3_177_3_21.png' # name_top_right_bottom_left.png, App 上神推荐头像框
    recommend_notify_email: '<EMAIL>'
  recommended:
    hypnosis_disable_time_range:
    - ["08:00:00", "22:00:00"]
  security:
    sensitive_information_key: 'openssl_aes_256_cbc_testpassword'
    # 加密 IV 初始化向量值，在需要数据库进行查询的地方使用到这个固定常量
    sensitive_fixed_iv_key: 'testiv'
  interaction:
    vote_help_url: 'https://fm.example.com/vote/help'
    vote_agreement_url: 'https://fm.example.com/vote/agreement'
  medal:
    fan_rule: 'https://link.missevan.com/fm/fans-system-guide'
    super_fan_rule: 'https://link.missevan.com/fm/super-fans-guide'
    cost_point: 500
    content_html: '<p><b>温馨提示。</b></p>'
    # 是否启用粉丝团宝箱功能
    enable_fans_box: true
  guild_agreement:
    title: '猫耳FM公会主播入驻服务协议'
    content_html: '<p><b>猫耳FM公会主播入驻服务协议。</b></p>'
  live_url:
    web_room: 'https://fm.missevan.com/live/%d'
    app_room: 'missevan://live/%d'
    user_rank_rule: 'https://link.missevan.com/help/fm-ranks'
    guild_apply_contract: 'https://fm.missevan.com/user/center/guild/invite/%d' # 参数为合约 ID
    websocket:
    - 'wss://fm.example.com:3016/ws?room_id=${room_id}'
    activity_websocket:
    - 'wss://fm.example.com:3016/ws/activity?room_id=${room_id}'
  pk:
    entry_icon: 'oss://live/pk/entrance.png' # PK 入口标识
    win_effect: 'oss://live/pk/effects/win.mp4;oss://live/pk/effects/win.png'
    win_web_effect: 'oss://live/pk/effects/win-web.mp4;oss://live/pk/effects/win-web.png'
    lose_effect: 'oss://live/pk/effects/lose.mp4;oss://live/pk/effects/lose.png'
    lose_web_effect: 'oss://live/pk/effects/lose-web.mp4;oss://live/pk/effects/lose-web.png'
    draw_effect: 'oss://live/pk/effects/draw.mp4;oss://live/pk/effects/draw.png'
    draw_web_effect: 'oss://live/pk/effects/draw-web.mp4;oss://live/pk/effects/draw-web.png'
    peak_limit_start_time: '20:00:00'
    peak_limit_end_time: '22:00:00'
  gift_wall:
    guide_url: 'https://link.missevan.com/fm/gift-wall-guide'
  live_agreement:
    title: '猫耳FM主播入驻服务协议'
    content_html: '<p><b>猫耳FM主播入驻服务协议。</b></p>'
  userinfo:
    # 默认用户名片框
    default_card_frame:
      # 默认用户名片框框体
      image_url: 'oss://live/cardframes/000_690_60_216_60.png'
      # 默认名片框字体颜色
      text_color_item:
        # 默认用户昵称字体颜色
        username: "#FFFFFF"
        # 默认个性签名字体颜色
        introduction: "#FFFFFF"
        # 默认举报和管理按钮字体颜色
        report_and_manage: "#FFFFFF"
    # 默认粉丝勋章
    default_medal_frame_url: 'oss://live/medalframes/normal/level${level}_0_9_0_54${ext}'
  # 公会注销、变更、转会后邮件发送邮箱
  guild_notice_emails:
    # 以下邮箱分别是魔王，陈胖子，阿薯改改，老马，便当君，北风.，阿岳，娇娇
    guild_delete_notice_emails: "<EMAIL>,<EMAIL>"
    # 以下邮箱分别是魔王，陈胖子，阿薯改改，老马，便当君，北风.，阿岳，娇娇
    guild_update_notice_emails: "<EMAIL>,<EMAIL>"
    # 以下邮箱分别是魔王，陈胖子，阿薯改改，老马，便当君，北风.，阿岳，娇娇
    guild_transfer_notice_emails: "<EMAIL>,<EMAIL>"
  guild_rate:
    guild_rate_detail_url: 'https://fm.uat.missevan.com/user/center/applymentratedetail' # 最低分成比例变更 URL
  # 公会相关操作权限开关
  guild_operate:
    guild_permission_switch: true # 公会长、经纪人重要操作权限验证开关
    creator_permission_switch: false # 主播重要操作权限验证开关
  red_packet:
    expire_duration: '24h' # 红包过期时长
    rule: 'https://link.uat.missevan.com/fm/gift-redpacket-guide' # 红包玩法说明跳转链接
    high_risk_score: 85 # 抢红包营销风险检测时，认定为高风险用户的分数阈值，设定为 0 时不进行检测
  channel_callback: # 用于 bililive
    app_key: 'test_key'
    app_secret: 'test_secret'
  sticker:
    room_icon_frame: "oss://live/stickers/package/room.png" # 房间专属图标框
    user_icon_frame: "oss://live/stickers/package/user.png" # 用户专属图标框
  luckybag:
    # 广播剧福袋列表 url
    dramalist_url: "https://fm.uat.missevan.com/user/luckybag/dramalist"
  notification:
    set_gift_online_mention_users: # 礼物上架需要 @ 提醒的用户
    - "007811"
  black_card: # 黑卡相关配置
    intro_open_url: "https://www.missevan.com/mevent/9000" # 黑卡活动页面地址
    horn_effect_label_icon_url: "oss://live/bubbles/notify/labels/blackcard4.png" # 黑卡喇叭飘屏动效提示图标地址，目前用于直播间全站喇叭 tab 下
    horn_access_intro: '参与【星曜活动】可获得' # 获取特权途径介绍
    calculate_consumption_start_ime: 1748707200 # 计算黑卡有效消费的开始时间，单位：秒（2025-06-01 00:00:00）
    event_id_draw: 911 # 黑卡抽奖活动 ID
    prod_test_white_list_user_ids: [1, 2] # 线上测试白名单
    prod_test_end_time: 1748707200 # 线上测试结束时间，单位：秒
  live_feed: # 直播 feed 流相关配置
    position_force_insertion_switch: true # 位置强插开关
    # Tab 实验相关配置
    tab_ab_salt: 'test_salt' # 实验盐值
    tab_ab_bucket_count: 10 # 实验分桶总数
    tab_ab_bucket_ids_group_a: [2] # 实验组 A 桶 ID 列表（展示推荐 Tab）
    tab_ab_bucket_ids_group_b: [3] # 实验组 B 桶 ID 列表（热门 Tab 使用算法推荐）
    tab_ab_allow_list_user_ids_group_a: [123456, 789012] # 实验组 A 白名单用户 ID 列表
    tab_ab_allow_list_user_ids_group_b: [234567, 890123] # 实验组 B 白名单用户 ID 列表
    # 热门分区图标配置
    hot_icon: "oss://live/catalog/icon/hot.png" # 热门分区图标
    hot_dark_icon: "oss://live/catalog/icon/hot-dark.png" # 热门分区夜间模式图标
    hot_web_icon: "oss://live/catalog/icon/hot-web.png" # 热门分区 Web 图标
    # 推荐分区图标配置
    recommend_icon: "oss://live/catalog/icon/recommend.png" # 推荐分区图标
    recommend_dark_icon: "oss://live/catalog/icon/recommend-dark.png" # 推荐分区夜间模式图标
    recommend_web_icon: "oss://live/catalog/icon/recommend-web.png" # 推荐分区 Web 图标

  # 预设消息配置
  preset:
    messages:
      - "大家好！"
      - "我来了~"
      - "主播好厉害！"
      - "支持主播~"
      - "666666"
      - "主播真棒！"
      - "主播说话声音真好听"
      - "笑死我了哈哈哈"
      - "暗中观察"
      - "好可爱啊"
      - "主播好有才华"
      - "这首歌真好听"
      - "太厉害了吧"
      - "主播加油~"
      - "我是新粉丝"
      - "前方高能"
      - "这个游戏玩得太好了"
      - "弹幕走起来"
      - "打卡签到~"
      - "来了来了"

service:
  db:
    host: '************'
    port: 3306
    name: 'app_missevan'
    user: 'user'
    pass: 'password'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
  live_db:
    host: 'mysql.srv.maoer.co'
    port: 3306
    name: 'missevan_live'
    user: 'user'
    pass: 'password'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
  newadb:
    host: '************'
    port: 3306
    name: 'pay' # TODO: 这个库名之后可能会有改动，到时需要同步修改
    user: 'user'
    pass: 'password'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
  paydb:
    host: '************'
    port: 3306
    name: 'missevan_pay'
    user: 'user'
    pass: 'password'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
  log_db:
    host: 'mysql.srv.maoer.co'
    port: 3306
    name: 'app_missevan_log'
    user: 'user'
    pass: 'password'
    # max_idle_conns: 8 # default is servicedb.DefaultMaxIdleConns()
    # max_life_time: '10s' # default: '10s'
  mongodb:
    host: 'mongo.srv.maoer.co'
    port: 27017
    name: 'chatroom'
    prefix: ""
    timeout: '10s'
  redis:
    addr: '************:6379'
    # password: '' # default: ''
    db: 6 # default: 6
    # pool_size: 10 # default: serviceredis.DefaultPoolSize()
  im_redis:
    addr: '************:6379'
    # password: '' # default: ''
    db: 6 # default: 6
    # pool_size: 10 # default: serviceredis.DefaultPoolSize()
  lru_redis:
    addr: '************:6379'
    db: 6
  databus:
    pub:
      key: 'test1'
      secret: 'test1'
      group: 'LiveLog-S'
      topic: 'LiveLog-T'
      addr: 'databus.srv.maoer.co:6205'
      # pool_size: 10 # pub pool size, default: 10
    sub:
      key: 'test1'
      secret: 'test1'
      group: 'LiveLog-S'
      topic: 'LiveLog-T'
      addr: 'databus.srv.maoer.co:6205'
      # pool_size: 10 # pub pool size, default: 10
  databus_delay:
    pub:
      key: 'test1'
      secret: 'test1'
      group: 'LiveLog-S'
      topic: 'LiveLog-T'
      addr: 'databus.srv.maoer.co:6205'
      # pool_size: 10 # pub pool size, default: 10
    sub:
      key: 'test1'
      secret: 'test1'
      group: 'LiveLog-S'
      topic: 'LiveLog-T'
      addr: 'databus.srv.maoer.co:6205'
      # pool_size: 10 # pub pool size, default: 10
  databus_log:
    pub:
      key: 'test1'
      secret: 'test1'
      group: 'AppLog-S'
      topic: 'AppLog-T'
      addr: 'databus.srv.maoer.co:6205'
      # pool_size: 10 # pub pool size, default: 10
  tencent_cos: # TODO: 放到 storage 中
    access_key_id: 'test'
    access_key_secret: 'xxxx'
    bucket_url: 'http://liverecord-1252693259.cos.ap-shanghai.myqcloud.com/'
  storage:
    oss:
      type: oss
      access_key_id: accesskey2
      access_key_secret: xxxxxx
      bucket: bucket-archive
      endpoint: oss-cn-hangzhou.aliyuncs.com
      # public_url: 'http://oss.example.com/'
      public_urls:
      - url: 'http://oss.example.com/'
        weight: 9
      - url: 'http://oss.example.com/'
        weight: 1
    archive:
      type: oss
      access_key_id: accesskey
      access_key_secret: xxxxxx
      bucket: bucket-archive
      endpoint: oss-cn-hangzhou.aliyuncs.com
      public_url: 'http://archive.example.com/'
    sound:
      type: oss
      access_key_id: accesskey3
      access_key_secret: xxxxxx
      bucket: bucket-sound
      endpoint: oss-cn-hangzhou.aliyuncs.com
      public_url: 'http://sound.example.com/'
  upos:
    preupload_endpoint: 'http://uat-member.bilibili.com'
    bucket: 'mefmboss'
    profile: 'mefm/upclone'
    token: 'xxx'
    # retry: 2
  vod:
    app_key: 'test'
    app_secret: 'xxxxxx'
  mrpc:
    # mrpc url should have `/` suffix
    app:
      url: 'http://127.0.0.1:8017/rpc/'
      key: 'testkey'
    im:
      url: 'http://************:3011/rpc/'
      key: 'testkey'
    go:
      url: 'http://127.0.0.1:3032/rpc/'
      key: 'xxxxxx'
    fm:
      url: 'http://127.0.0.1:3012/rpc/'
      key: 'testkey'
    sso:
      url: 'http://127.0.0.1:3002/rpc/'
      key: 'testkey'
    drama:
      url: 'http://127.0.0.1:3002/rpc/'
      key: 'testkey'
    minigame:
      url: 'http://127.0.0.1:3035/rpc/'
      key: 'testkey'
    live:
      url: 'http://127.0.0.1:3013/rpc/'
      key: 'testkey'
    mrpc:
      url: 'http://127.0.0.1:3000/rpc/'
      key: 'testkey'
  pushservice:
    url: 'http://127.0.0.1:8090/rpc/'
    key: 'testkey'
  agora:
    app_id: 'testkey'
    app_certificate: 'testkey'
  aliyun_live:
    host: 'foo.example.com'
    push_host: 'foo.example.com'
    app_name: 'live'
    auth_key: 'testkey'
    auth_timeout: 1800 # unit: second
    endpoint: 'https://live.aliyuncs.com'
    access_key_id: 'testkey'
    access_key_secret: 'xxxxxx'
  netease_live:
    app_key: 'testkey'
    app_secret: 'app_secret'
    auth_key: 'testkey'
  ksyun_live:
    app_key: 'testkey'
    app_secret: 'app_secret'
    push_auth_key: 'pushkey'
    pull_auth_key: 'pullkey'
    unique_name: 'acgvideo'
    app_name: 'live-fm-dev'
    auth_timeout: 1800 # unit: second
  bvc_live:
    url: 'https://www.example.com/'
    app_key: 'testkey'
    app_secret: 'app_secret'
    auth_secret: 'secret'
    stream_name_prefix: 'maoer_'
    stream_name_ex_prefix: 'maoer_ex_'
    auth_timeout: 1800 # unit: second
    is_new_version: true # 使用录像合成新逻辑
  bili_live:
    url: 'https://www.example.com/'
    app_key: 'testkey'
    app_secret: 'app_secret'
    business: 'missevan'
    # disable_ssl_verify: false
  bili_api:
    url: 'https://api.bilibili.com'
    app_key: 'testkey'
    app_secret: 'app_secret'
  captcha:
    enabled: true
    access_key_id: 'xxxxx'
    access_key_secret: 'xxxxx'
    app_key: 'xxxxx'
    region_id: 'cn-hangzhou'
    endpoint: 'http://afs.aliyuncs.com'
    slide_url: 'https://www.uat.missevan.com/standalone/403/slide.html'
  upload:
    url: 'https://www.uat.missevan.com/files/'
    path: '/data/files/'
    # http: false
    # http_host_addr: '127.0.0.1:80'
