var stringWidth = require('string-width')

module.exports = {
  "settings": {
    "fences": true,
    "listItemIndent": 1,
    "paddedTable": false,
    "stringLength": stringWidth,
  },
  "plugins": [
    "pangu",
    "preset-lint-markdown-style-guide",
    ["lint-emphasis-marker", "_"],
    ["lint-list-item-indent", "space"],
    ["lint-maximum-line-length", 120],
    ["lint-no-file-name-irregular-characters", "\\.a-zA-Z0-9-_ \\u2e80-\\u2eff\\u2f00-\\u2fdf\\u3040-\\u309f\\u30a0-\\u30ff\\u3100-\\u312f\\u3200-\\u32ff\\u3400-\\u4dbf\\u4e00-\\u9fff\\uf900-\\ufaff"],
    ["lint-no-file-name-mixed-case", false],
    ["lint-no-shortcut-reference-link", false],
    ["lint-ordered-list-marker-value", "ordered"],
    ["lint-table-pipe-alignment", false],
    ["stringify", { paddedTable: false }]
  ]
}
