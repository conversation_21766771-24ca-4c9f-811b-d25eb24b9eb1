CREATE TABLE IF NOT EXISTS `mowangskuser` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`confirm` int unsigned NOT NULL DEFAULT '0' COMMENT '用户权限或身份标识'
  ,`username` varchar(20) NOT NULL
  ,`email` varchar(50) DEFAULT NULL
  ,`cip` varchar(50) NOT NULL DEFAULT ''
  ,`uip` varchar(50) NOT NULL DEFAULT ''
  ,`ctime` int unsigned NOT NULL DEFAULT '0'
  ,`utime` int unsigned NOT NULL DEFAULT '0'
  ,`quanxian` varchar(5) NOT NULL DEFAULT ''
  ,`teamid` int unsigned NOT NULL DEFAULT '1'
  ,`teamname` varchar(20) NOT NULL DEFAULT 'Drrr'
  ,`ban` tinyint unsigned NOT NULL DEFAULT '0'
  ,`ustr` int unsigned NOT NULL DEFAULT '0'
  ,`uint` int unsigned NOT NULL DEFAULT '0'
  ,`uagi` int unsigned NOT NULL DEFAULT '0'
  ,`point` int unsigned NOT NULL DEFAULT '50'
  ,`nowsound` int unsigned NOT NULL DEFAULT '0' COMMENT '记录用户当前M音时长'
  ,`iconid` int unsigned NOT NULL DEFAULT '0'
  ,`iconurl` varchar(240) NOT NULL
  ,`iconcolor` varchar(50) NOT NULL DEFAULT ''
  ,`subtitle` varchar(10) NOT NULL DEFAULT ''
  ,`boardiconid` int unsigned NOT NULL DEFAULT '0'
  ,`boardiconurl` varchar(200) NOT NULL DEFAULT ''
  ,`boardiconcolor` varchar(50) NOT NULL DEFAULT '#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1'
  ,`coverid` int DEFAULT NULL COMMENT '封面图 ID'
  ,`coverurl` varchar(60) DEFAULT NULL COMMENT '封面图'
  ,`isnewmsg` tinyint unsigned NOT NULL DEFAULT '0'
  ,`userintro` text
  ,`userintro_audio` int unsigned DEFAULT NULL
  ,`likenum` int unsigned DEFAULT '0' COMMENT '点赞数'
  ,`fansnum` int unsigned DEFAULT '0' COMMENT '粉丝数'
  ,`follownum` int unsigned DEFAULT '0' COMMENT '关注数'
  ,`soundnum` int unsigned DEFAULT '0' COMMENT '个人语音数'
  ,`albumnum` int unsigned DEFAULT '0' COMMENT '个人专辑数'
  ,`imagenum` int unsigned DEFAULT '0' COMMENT '个人图片数'
  ,`feednum` int DEFAULT '0' COMMENT 'feed 流未读信息'
  ,`soundnumchecked` int unsigned DEFAULT '0' COMMENT '审核通过的声音'
  ,`imagenumchecked` int unsigned DEFAULT '0' COMMENT '审核通过的图片'
  ,`mlevel` tinyint unsigned DEFAULT '1' COMMENT '用户当前等级'
  ,`avatar` varchar(100) DEFAULT NULL COMMENT '三次元头像'
  ,`icontype` int DEFAULT '1'
  ,`mobile` varchar(11) DEFAULT NULL
  ,`region` smallint unsigned DEFAULT NULL COMMENT '国际电话区号'
  ,`coverurl_new` varchar(60) NOT NULL DEFAULT '' COMMENT '用户上传的封面图链接'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8
;

INSERT INTO `mowangskuser`
  (`id`, `confirm`, `username`, `iconurl`, `boardiconurl`, `userintro`, `icontype`, `fansnum`, `follownum`)
VALUES
  -- default
  (12, 0, '零月', 'icon01.png', '201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png', '', 0, 0, 0)
  -- TestCheckAttention
  ,(1, 2, '323', 'icon01.png', 'icon01.png', NULL, 1, 1, 0)
  ,(2, 2, '大用户_2', 'http://static-test.missevan.com/avatars/test_icon_url.png', '201603/07/be01830e50773de808aaa232da0492d8193843.png', '虎虎虎', 1, 63, 0)
  ,(3, 2, '暗切线', 'avatars/icon01.png', '201603/28/f0dec240744b01a0a2a5cc9ff16567b5134355.png', '月声后期 | M博@暗切线', 1, 1073, 63)
  ,(4, 2, '迦夜ちゃん', '201409/28/713312c7411750896bd06f691bffd9b9173032.png', '201602/12/a0dc07785a3b213868f83041927b5978194645.png', '月声后期 | M博@暗切线', 0, 0, 105)
  ,(5, 2, '猫耳FM', 'http://static.missevan.com/avatars/icon01.png', 'icon01.png', '“猫耳FM就是M站的APP哦！”', 1, 1, 0)
  ,(6, 2, '测试用户 101', 'http://static.missevan.com/avatars/icon01.png', 'icon01.png', '“猫耳FM就是M站的APP哦！”', 1, 1, 0)
  -- TestActionGuildCreatorTerminateForcely
  ,(15, 2, '测试用户 15', 'http://static.missevan.com/avatars/icon01.png', 'icon01.png', '“猫耳FM就是M站的APP哦！”', 1, 1, 0)
  -- TestAddUserInfo
  ,(10, 2, 'bless', 'icon01.png', '201704/07/9b3529a08130e74da1dcd8b53feb50c5155007.png', '', 0, 12, 8)
  -- TestActionFollowList
  ,(17, 2, '测试用户 17', 'icon01.png', 'icon02.png', '简介', 1, 0, 1)
  -- TestRankFindAttention
  ,(45, 2, '太妃_糖', 'icon01.png', '201508/10/e6e0c66abdbb463db41d8677f9b9d98a015930.png', NULL, 0, 11, 55)
  ,(892, 2, '有直播间的用户', 'icon01.png', 'icon02.png', '简介', 1, 44, 60)
  -- mongo_user_nobles_test 下的 TestListNobleBalance
  ,(1000, 2, '平和岛野兽', 'icon01.png', '201506/15/9045cebd946a5284577f47fee8e71717141334.png', NULL, 1, 0, 10)
  -- TestActionCancelRecommend 神话推荐取消
  ,(3456864, 2, '一点半夏', 'icon01.png', '201907/09/91ea527c0c06883a0f5d9b50dd154a71153405.png', '', 1, 57, 25)
  ,(3457024, 2,  '固氮的根瘤菌固氮的根', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  -- TestActionGetUserInfo
  ,(3456835, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  -- livenoblerecommend 下的 TestFindRecommendList
  ,(248506, 2, 'Ruii', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(11223344, 2, 'testEditMedal', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(3457114, 2, '神话贵族账号', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(3457111, 2, '超粉购买人1', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(1023, 2, '测试勋章用户', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  -- TestActionRecommendScheduleApply
  ,(12345, 2, '测试首页推荐', 'icon01.png', '201506/15/9045cebd946a5284577f47fee8e71717141334.png', NULL, 1, 0, 10)
  ,(186192636, 2, '受限房间主播', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(99, 2, '测试主播转会 1', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(100, 2, '测试主播转会 2', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(101, 2, '测试外观发放 1', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '外观测试', 1, 3, 10)
  ,(102, 2, '测试外观发放 2', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '外观测试', 1, 3, 10)
  ,(103, 2, '测试外观发放 3', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '外观测试', 1, 3, 10)
  ,(104, 2, '测试外观发放 4', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '外观测试', 1, 3, 10)
  ,(105, 2, '测试主播转会 3', 'avatars/icon01.png', '201601/03/7c21932f28c2e9fcfc60c5f780617a6f181418.png', '喵喵喵', 1, 3, 10)
  ,(20210707, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(********, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(106, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(107, 0, '三方独家主播测试用户 1', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(108, 0, '三方独家主播测试用户 2', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(109, 0, '直播动态测试用户', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(110, 0, '测试礼物红包用户 1', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(111, 0, '测试礼物红包用户 2', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(400791, 0, '测试主播强制解约', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(400792, 0, '测试主播协商解约 1', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(400793, 0, '测试主播协商解约 2', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(9074509, 0, '上神测试用户', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(349524, 0, '非上神测试用户', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  -- TestActionPrizeInfo、TestNewPrizeInfoParams、TestPrizeInfoParams_getPrizeInfo
  ,(3457181, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  ,(3457182, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 10, 12)
  -- Test_buildPreviewIntro
  ,(3457183, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 78908, 12)
  -- TestGetNotice
  ,(120, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 78908, 12)
  ,(121, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 78908, 12)
  ,(122, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 78908, 12)
  ,(123, 0, 'test_user', 'icon01.png', 'icon01.png', NULL, 1, 78908, 12)
;

CREATE TABLE `authassignment` (
  `itemname` varchar(64) NOT NULL,
  `userid` varchar(64) NOT NULL,
  `bizrule` text,
  `data` text,
  PRIMARY KEY (`itemname`,`userid`)
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8
;

INSERT INTO `authassignment`
  (`itemname`, `userid`, `bizrule`, `data`)
VALUES
  ('liveadmin', '3456835', NULL, NULL)
  ,('test3', '1233', NULL, NULL)
  ,('liveoperator', '10', NULL, NULL)
;

CREATE TABLE IF NOT EXISTS `catalog`(
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级分类'
  ,`catalog_name` varchar(100) NOT NULL COMMENT '名称'
  ,`catalog_name_second` varchar(100) DEFAULT '' COMMENT '副名称'
  ,`catalog_name_alias` varchar(100) DEFAULT '' COMMENT '别名'
  ,`content` text COMMENT '详细介绍'
  ,`sort_order` int(10) COMMENT '排序'
  ,`status_is` varchar(5)  NOT NULL DEFAULT 'Y' COMMENT '状态'
)
;

INSERT INTO `catalog`
  (`id`, `parent_id`, `catalog_name`, `catalog_name_second`, `catalog_name_alias`, `content`, `sort_order`, `status_is`)
VALUES
  (5, 1, '广播剧', NULL, 'drama', NULL, 8, 'Y')
  ,(18, 5, '言情', NULL, 'yanqing_zh', NULL, 0, 'Y')
  ,(99, 0, '直播', '', 'live', '', 0, 'Y')
  ,(104, 99, '音乐', '#AAFAF9', 'live_music', '{"icon_url":"oss://live/catalogs/music.png","dark_icon_url":"oss://live/catalogs/dark/music.png"}', 0, 'Y')
  ,(105, 99, 'Pia 戏', '#FFCD72', 'live_pia', '{"icon_url":"oss://live/catalogs/pia.png","dark_icon_url":"oss://live/catalogs/dark/pia.png"}', 0, 'Y')
  ,(106, 99, '闲聊', '#CDEBF0', 'live_gossip', '{}', 0, 'N')
  ,(107, 99, '综合', '#D5FAB3', 'live_general', '{}', 0, 'N')
  ,(111, 99, '直播隐藏', '#D5FAB3', 'live_hide', '{"icon_url":"oss://light.png","dark_icon_url":"oss://dark.png","web_icon_url":"oss://web.png"}', 0, 'N')
  ,(115, 99, '放松', '#87CAFF', 'live_sleep', '{"icon_url":"oss://live/catalogs/light/sleep.png","dark_icon_url":"oss://live/catalogs/dark/sleep.png","web_icon_url":"oss://live/catalogs/web/sleep.png"}', 0, 'Y')
  ,(116, 99, '情感', '#E0AAFE', 'live_emotion', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(20, 8, '二次元音乐', '#E0AAFE', 'acg_music', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(21, 8, '三次元音乐', '#E0AAFE', 'real_music', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(38, 8, '演奏创作', '#E0AAFE', 'play_music', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(48, 8, '翻唱', '#E0AAFE', 'fcjyc', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(50, 8, 'OP/ED/OST', '#E0AAFE', 'opedost', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(51, 8, 'Vocaloid', '#E0AAFE', 'music_v', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(75, 8, '原创音乐', '#E0AAFE', 'ycyy', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  ,(76, 8, '剧情歌', '#E0AAFE', 'plot', '{"icon_url":"oss://live/catalogs/light/heart.png","dark_icon_url":"oss://live/catalogs/dark/heart.png","web_icon_url":"oss://live/catalogs/web/heart.png"}', 0, 'Y')
  -- 二级分区
  ,(118, 104, '二级音乐1', '#AAFAF9', 'live_music', '', 0, 'Y')
  ,(119, 104, '二级音乐2', '#FFCD72', 'live_pia', '', 2, 'N')
  ,(120, 105, '二级Pia 戏1', '#AAFAF9', 'live_music', '', 0, 'Y')
  ,(121, 105, '二级Pia 戏2', '#FFCD72', 'live_pia', '', 0, 'Y')
  -- 测试
  ,(122, 118, '三级音乐1', '#AAFAF9', 'live_music', '', 0, 'Y')
;

-- GRANT SELECT, INSERT, UPDATE, DELETE ON `app_missevan`.`live_recommended_elements` TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_recommended_elements` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`sort` int(10) NOT NULL COMMENT '推荐位排序/位置'
  ,`element_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '元素 ID'
  ,`element_type` tinyint(1) NOT NULL COMMENT '1 直播间推荐位，2 直播间活动，3 直播广场运营配置图标，4 头像框，5 直播广场“最热”推荐位中新增的 3 个元素'
  ,`name` varchar(50) NOT NULL DEFAULT ''
  ,`cover` varchar(255) NOT NULL DEFAULT ''
  ,`url` varchar(1000) NOT NULL DEFAULT ''
  ,`attr` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '位 1 显示关闭按钮'
  ,`start_time` int(10) unsigned DEFAULT NULL
  ,`expire_time` int(10) unsigned NOT NULL DEFAULT '0'
  ,`create_time` int(10) unsigned NOT NULL DEFAULT '0'
  ,`modified_time` int(10) unsigned NOT NULL DEFAULT '0'
  ,`extended_fields` text COMMENT '额外数据，JSON format'
) --ENGINE=InnoDB AUTO_INCREMENT=31938 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
;

INSERT INTO `live_recommended_elements`
  (sort, element_id, element_type, name, cover, url, attr, start_time, expire_time, create_time)
VALUES
  -- TestFindListElement
  (1, 99233, 4, '', '', 'http://test.png', 0, 0, 2123456789, strftime('%s','now'))
  -- TestFindRoomIcon
  ,(1, 1000, 3, '', '', 'http://show.png', 0, 0, 2123456789, strftime('%s','now'))
  ,(1, 1000, 3, '', '', 'http://hide.png', 0, 0, 2123456789, 0)
  ,(1, 1001, 3, '', '', 'http://show.png', 0, 0, 2123456789, strftime('%s','now'))
  ,(1, 1002, 3, '', '', 'http://hide.png', 0, 0, 946656000, strftime('%s','now'))
  ,(0, 1003, 3, '', '', 'http://hide.png', 0, 0, 2123456789, strftime('%s','now'))
  -- TestTable
  ,(0, 1003, 1, '', '', 'http://hide.png', 0, 0, 0, strftime('%s','now'))
  ,(0, 1003, 2, '', '', 'http://hide.png', 0, 0, 0, strftime('%s','now'))
  ,(0, 1003, 7, '', '', 'http://hide.png', 0, 0, 2123456789, strftime('%s','now'))
  ,(0, 1003, 14, '', '', 'http://hide.png', 0, 0, 2123456789, strftime('%s','now'))
  ,(0, 1003, 15, '', '', 'http://hide.png', 0, 0, 2123456789, strftime('%s','now'))
  -- TestFindOpenList, TestListSquareHotRecommended
  ,(1, 123, 5, '', '', '', 0, 0, 2123456789, strftime('%s','now'))
  ,(2, 1234, 5, '-1', '', '', 0, 0, 2123456789, strftime('%s','now'))
  ,(3, 13579, 5, '-1', '', '', 0, 0, 2123456789, strftime('%s','now'))
  ,(5, 246810, 5, '-1', '', '', 0, 0, 2123456789, strftime('%s','now'))
  ,(2, 1234, 5, '10', '', '', 0, 0, 2123456789, strftime('%s','now'))
  ,(3, 1234, 5, '-2', '', '', 0, 0, 2123456789, strftime('%s','now'))
  -- TestSchedule, TestRecordLiveScheduleRecommend
  ,(1, 22489473, 6, '', 'oss://test.png', '', 0, 0, 2123456789, strftime('%s','now'))
  -- TestCancelRecommend
  ,(1, 12345, 5, '', '', '', 0, 0, 1800, strftime('%s','now'))
  -- 测试直播粉丝勋章
  ,(1, 3456835, 8, '', '', 'oss://live/medalframes/3f12/level${level}_0_9_0_54.png', 0, 0, 2123456789, strftime('%s','now'))
  ,(1, 223355, 8, '', '', 'oss://live/medalframes/3f12/level${level}_0_9_0_54.png', 0, 0, 2123456789, strftime('%s','now'))
  ,(1, 1234567, 8, '', '', 'oss://live/medalframes/3f12/level${level}_0_9_0_54.png', 0, 0, 2123456789, strftime('%s','now'))
  -- 测试直播间背景图 TestLoadBackgroundCache, TestFindBackground
  ,(1, 223355, 10, '', '', 'http://test_background.webp;0.55', 0, 0, 2123456789, strftime('%s','now'))
  ,(1, 9074509, 17, '', '', 'http://test_background.webp;0.55', 0, 0, 2123456789, strftime('%s','now'))
  ,(1, 9074509, 17, '', '', 'http://test_background.webp;0.55', 0, 0, 1123456789, strftime('%s','now'))
;

INSERT INTO `live_recommended_elements`
  (id, sort, element_id, element_type, name, cover, url, attr, start_time, expire_time, create_time, extended_fields)
VALUES
    -- 测试直播间小窗 FindAllPopups
  (880, 1, 152, 11, '', '', 'https://static-test.missevan.com/standalone/uat/event/152/small-window/index.html?room_id=__ROOM_ID__;https://www.uat.missevan.com/mevent/1521;', 0, 0, 2123456789, strftime('%s','now'), '{}')
  ,(881, 1, 1003, 7, '', '', 'http://show.png', 0, 1630857600, 1631462400, strftime('%s','now'), '')
  ,(882, 1, 1004, 7, '', '', 'http://show.png', 0, 1630854000, 1630861200, strftime('%s','now'), '')
  ,(883, 1, 1005, 7, '', '', 'http://show.png', 0, 1630861200, 1630864800, strftime('%s','now'), '')
  ,(884, 1, 1006, 7, '', '', 'http://show.png', 0, 1631458800, 1631466000, strftime('%s','now'), '')
  ,(885, 1, 1007, 7, '', '', 'http://show.png', 0, 1630940400, 1630944000, strftime('%s','now'), '')
  ,(886, 1, 223355, 7, '', '', 'http://show.png', 0, 1626418800, 1626422400, strftime('%s','now'), '')
  -- TestFindOneAndDelete
  ,(1000, 1, 0, 2, '', 'oss://cover.png', '', 0, 0, 2123456789, strftime('%s','now'), '')
  -- TestActionEditSchedule
  ,(45311, 1, 22489473, 6, '', 'oss://avatars/icon02.png', '', 3, 1997879990, 1997889990, 0, '')
;

 CREATE TABLE IF NOT EXISTS `live_review` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`user_id` int(10) unsigned NOT NULL COMMENT '主播 ID'
  ,`room_id` int(10) unsigned NOT NULL COMMENT '房间号'
  ,`guild_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '公会 ID'
  ,`upload_time` int(10) unsigned NOT NULL COMMENT '图片上传时间'
  ,`type` int(10) NOT NULL COMMENT '图片类型, 0: 封面; 1: 背景；2: 名称'
  ,`status` int(10) NOT NULL DEFAULT '0' COMMENT '审核状态, -1: 过期; 0: 审核中; 1: 通过；2: 过期'
  ,`info` varchar(255) NOT NULL DEFAULT '' COMMENT '审核信息'
  ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
) -- ENGINE=InnoDB AUTO_INCREMENT=160276 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播间信息审核'
;

INSERT INTO `live_review`
  (user_id, room_id, guild_id, upload_time, type, status, info, create_time, modified_time)
VALUES
  -- TestFindReviewing, TestFindRoomReviewing, TestFinishReviewing
  (14, 123, 0, strftime('%s','now'), 0, 0, '{"image_url":"oss://avatars/icon01.png"}', strftime('%s','now'),strftime('%s','now'))
  ,(14, 123, 0, strftime('%s','now')-1, 1, 0, '{"image_url":"oss://avatars/icon01.png", "opacity": 2.0}', strftime('%s','now'),strftime('%s','now'))
  ,(14, 123, 0, strftime('%s','now')-2, 2, 0, '{"name":"测试直播审核"}', strftime('%s','now'),strftime('%s','now'))
;

 CREATE TABLE IF NOT EXISTS `live_noble` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
	,`vip_id` INT(11) UNSIGNED NOT NULL COMMENT 'vip ID，对应 app_missevan_sso.m_vip.id'
	,`type` tinyint(3) UNSIGNED NOT NULL COMMENT '贵族类型, 1: 普通贵族, 2: 上神贵族'
	,`level` tinyint(3) UNSIGNED NOT NULL COMMENT '贵族等级'
	,`privilege` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '特权。对应的比特位为 1 时标识拥有该权限，1：进场隐身；2：贵族气泡；3：榜单隐身；4：防禁言；5：定制礼物；6：推荐主播上热门'
	,`icon` VARCHAR(255) NOT NULL COMMENT '贵族图标'
	,`icon_mini` VARCHAR(255) NOT NULL COMMENT '贵族小图标'
	,`privilege_num` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '特权数量'
	,`effect` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '开通/续费特效，为空字符串时无该特权'
	,`effect_duration` INT(11) NOT NULL DEFAULT '0' COMMENT '开通/续费特效持续时间（毫秒）'
	,`avatar_frame` VARCHAR(255)NOT NULL COMMENT '头像框'
	,`card_frame` VARCHAR(255) NOT NULL COMMENT '名片边框'
	,`name_colors` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '贵族昵称颜色'
	,`new_card_frame` VARCHAR(255) NOT NULL COMMENT '新名片边框'
	,`exp_acceleration` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '经验加速百分比比例，0 ~ 100，为 0 时标识无该特权'
	,`medal_num` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '勋章上限，为 0 时无改特权'
	,`customer_service_type` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '客服类型，0：无客服，1：高级客服，2：VIP 客服'
	,`notify_type` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '开通/续费通知类型，0：无该特权，1：房间内，2：全站'
	,`notify_duration` INT(11) UNSIGNED NOT NULL COMMENT '购买房间内通知持续时间（毫秒）'
	,`entrance_type` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '入场特效，0：无该特权，1：带勋章，2：高级特效'
	,`horn_num` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '喇叭数量，为 0 时无该特权'
	,`create_time` INT(11) UNSIGNED NOT NULL COMMENT '创建时间'
	,`modified_time` INT(11) UNSIGNED NOT NULL COMMENT '修改时间'
) -- ENGINE = INNODB AUTO_INCREMENT = 8 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
;

INSERT INTO `live_noble`
    (id, vip_id, type, level, privilege, icon, icon_mini, privilege_num, effect, effect_duration, avatar_frame, card_frame, exp_acceleration, medal_num, customer_service_type, notify_type, notify_duration, entrance_type, horn_num, create_time, modified_time, new_card_frame)
VALUES
    (1, 1, 1, 1, 63, 'oss://gifts/noble/001.png', 'oss://gifts/noble/mini/001.png', 7, '', 0, 'oss://gifts/avatarframes/001.png', 'oss://gifts/cardframes/001.png', 0, 100, 0, 1, 5000, 0, 0, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/cardframes/001-new.png')
    ,(2, 2, 1, 2, 0, 'oss://gifts/noble/002.png', 'oss://gifts/noble/mini/002.png', 9, '', 0, 'oss://gifts/avatarframes/002.png', 'oss://gifts/cardframes/002.png', 5, 30, 0, 1, 10000, 0, 0, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/cardframes/002-new.png')
    ,(3, 3, 1, 3, 0, 'oss://gifts/noble/003.png', 'oss://gifts/noble/mini/003.png', 10, 'oss://gifts/effects/noble/003.svga', 6000, 'oss://gifts/avatarframes/003.png', 'oss://gifts/cardframes/003.png', 5, 30, 0, 1, 15000, 0, 0, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/003-new.png')
    ,(4, 4, 1, 4, 2, 'oss://gifts/noble/004.png', 'oss://gifts/noble/mini/004.png', 13, 'oss://gifts/effects/noble/004.webp;oss://gifts/effects/noble/004.svga', 8000, 'oss://gifts/avatarframes/004.png', 'oss://gifts/cardframes/004.png', 10, 30, 1, 2, 20000, 0, 1, 1576478685, 1576478685, 'oss://gifts/newcardframes/004-new.png')
    ,(5, 5, 1, 5, 3, 'oss://gifts/noble/005.png', 'oss://gifts/noble/mini/005.png', 14, 'oss://gifts/effects/noble/005.webp;oss://gifts/effects/noble/005.svga', 10000, 'oss://gifts/avatarframes/005.png', 'oss://gifts/cardframes/005.png', 15, 30, 1, 2, 25000, 0, 2, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/005-new.png')
    ,(6, 6, 1, 6, 7, 'oss://gifts/noble/006.png', 'oss://gifts/noble/mini/006.png', 15, 'oss://gifts/effects/noble/006.webp;oss://gifts/effects/noble/006.svga', 12000, 'oss://gifts/avatarframes/006.webp', 'oss://gifts/cardframes/006.png', 20, 30, 2, 2, 30000, 0, 3, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/006-new.png')
    ,(7, 7, 1, 7, 63, 'oss://gifts/noble/007.gif', 'oss://gifts/noble/mini/007.webp', 18, 'oss://gifts/effects/noble/007.webp;oss://gifts/effects/noble/007.svga', 14000, 'oss://gifts/avatarframes/007.webp', 'oss://gifts/cardframes/007.png', 25, 30, 2, 2, 60000, 0, 5, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/007-new.png')
    ,(8, 8, 2, 1, 63, 'oss://gifts/noble/007.gif', 'oss://gifts/noble/mini/007.webp', 20, 'oss://gifts/effects/noble/007.webp;oss://gifts/effects/noble/007.svga', 14000, 'oss://gifts/avatarframes/007.webp', 'oss://gifts/cardframes/007.png', 25, 30, 2, 2, 60000, 0, 5, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/007-new.png')
    ,(9, 9, 3, 1, 0, 'oss://gifts/noble/001.png', 'oss://gifts/noble/mini/001.png', 7, '', 0, 'oss://gifts/avatarframes/001.png', 'oss://gifts/cardframes/001.png', 0, 100, 0, 1, 5000, 0, 0, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/cardframes/001-new.png')
    ,(10, 10, 3, 2, 0, 'oss://gifts/noble/002.png', 'oss://gifts/noble/mini/002.png', 9, '', 0, 'oss://gifts/avatarframes/002.png', 'oss://gifts/cardframes/002.png', 5, 30, 0, 1, 10000, 0, 0,strftime('%s','now'), strftime('%s','now'), 'oss://gifts/cardframes/002-new.png')
    ,(11, 11, 3, 3, 0, 'oss://gifts/noble/003.png', 'oss://gifts/noble/mini/003.png', 10, 'oss://gifts/effects/noble/003.svga', 6000, 'oss://gifts/avatarframes/003.png', 'oss://gifts/cardframes/003.png', 5, 30, 0, 1, 15000, 0, 0, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/003-new.png')
    ,(12, 12, 3, 4, 2, 'oss://gifts/noble/004.png', 'oss://gifts/noble/mini/004.png', 13, 'oss://gifts/effects/noble/004.webp;oss://gifts/effects/noble/004.svga', 8000, 'oss://gifts/avatarframes/004.png', 'oss://gifts/cardframes/004.png', 10, 30, 1, 2, 20000, 0, 1, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/004-new.png')
    ,(13, 13, 3, 5, 3, 'oss://gifts/noble/005.png', 'oss://gifts/noble/mini/005.png', 14, 'oss://gifts/effects/noble/005.webp;oss://gifts/effects/noble/005.svga', 10000, 'oss://gifts/avatarframes/005.png', 'oss://gifts/cardframes/005.png', 15, 30, 1, 2, 25000, 0, 2, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/005-new.png')
    ,(14, 14, 3, 6, 7, 'oss://gifts/noble/006.png', 'oss://gifts/noble/mini/006.png', 15, 'oss://gifts/effects/noble/006.webp;oss://gifts/effects/noble/006.svga', 12000, 'oss://gifts/avatarframes/006.webp', 'oss://gifts/cardframes/006.png', 20, 30, 2, 2, 30000, 0, 3, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/006-new.png')
    ,(15, 15, 3, 7, 61, 'oss://gifts/noble/007.gif', 'oss://gifts/noble/mini/007.webp', 18, 'oss://gifts/effects/noble/007.webp;oss://gifts/effects/noble/007.svga', 14000, 'oss://gifts/avatarframes/007.webp', 'oss://gifts/cardframes/007.png', 25, 30, 2, 2, 60000, 0, 5, strftime('%s','now'), strftime('%s','now'), 'oss://gifts/newcardframes/007-new.png')
;

CREATE TABLE  IF NOT EXISTS `m_message_assign` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
	,`recuid` INT(10) UNSIGNED NOT NULL COMMENT '收到人M号'
	,`send_uid` INT(10) UNSIGNED NOT NULL COMMENT '发送人M号'
	,`title` VARCHAR(255) NULL COMMENT '标题'
	,`content` VARCHAR(5000) DEFAULT NULL COMMENT '正文'
	,`status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 未读; 1 已读'
	,`time` INT(10) UNSIGNED NOT NULL
) --ENGINE = INNODB AUTO_INCREMENT = 126325 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_bin;
;

CREATE TABLE IF NOT EXISTS `live` (
  `id` int unsigned NOT NULL PRIMARY KEY COMMENT '主播 ID = UserID'
  ,`room_id` int unsigned NOT NULL COMMENT '直播间房间 ID'
  ,`catalog_id` int unsigned NOT NULL DEFAULT '0'
  ,`title` varchar(30) NOT NULL DEFAULT '' COMMENT '直播间名称'
  ,`intro` varchar(160) NOT NULL DEFAULT '' COMMENT '直播间简介'
  ,`status` tinyint(1) DEFAULT '0' COMMENT '直播间状态，-1：用户注销（搜索隐藏）；0：没有开启房间；1：房间开启'
  ,`live_start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '开播时间'
  ,`contract_id` int unsigned NOT NULL DEFAULT '1' COMMENT '主播合约 ID'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL COMMENT '修改时间'
  ,`user_id` bigint NOT NULL COMMENT '主播 ID'
  ,`cover` varchar(255) NOT NULL DEFAULT '' COMMENT '直播间封面'
  ,`score` bigint NOT NULL DEFAULT 0 COMMENT '直播间热度'
  ,`username` varchar(20) NOT NULL DEFAULT ''
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
;

INSERT INTO `live`
  (`id`, `user_id`,`room_id`, `catalog_id`, `title`, `intro`, `status`, `live_start_time`, `contract_id`, `create_time`, `modified_time`, `cover`)
VALUES
  -- TestListAllAttention,
  (10, 10, 22489473, 106, 'go test 用', '', 1, 0, 1, 0, 1600743764, '')
  -- TestFindLiveByRoomID
  ,(3400560, 3400560, 61618635, 106, 'go test 用', '', 1, 0, 1, 0, 1600758027, '')
  -- TestFinishReviewing,
  ,(248507, 14, 123, 61618634, 'go test 用', '', 1, 0, 1, 0, 1617076815016, '')
  -- TestUserGuildRole
  ,(********, ********, ********, 61618634, 'go test 用', '', 1, 0, 1, 0, 1617076815016, '')
  -- TestActionPreviewGet、TestNewPreviewParam
  ,(234324, 234324, 666, 61618634, 'go test 用', '', 1, 0, 1, 0, 1617076815016, '')
  -- TestFilterLiveBanners TestFindOpenLiveByRoomIDs
  ,(3400561, 3400560, 61618636, 106, 'go test 用', '', 1, 0, 1, 0, 1600758027, '')
  -- TestFilterLiveBanners TestFindOpenLiveByRoomIDs
  ,(3400562, 3400560, 61618637, 106, 'go test 用', '', 0, 0, 1, 0, 1600758027, '')
  -- TestAllowlistImportParam_parseCSV
  ,(9074509, 9074509, 9074509, 106, 'go test 用', '', 0, 0, 1, 0, 1600758027, '')
;

CREATE TABLE IF NOT EXISTS `m_attention_user` (
  `id` int NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`user_active` int NOT NULL COMMENT '关注者'
  ,`user_passtive` int NOT NULL COMMENT '被关注者'
  ,`time` int NOT NULL COMMENT '关注时间'
  ,UNIQUE(`user_active`,`user_passtive`)
) -- ENGINE=InnoDB AUTO_INCREMENT=10030387 DEFAULT CHARSET=utf8 COMMENT='关注'
;

INSERT INTO `m_attention_user`
  (`user_active`, `user_passtive`, `time`)
VALUES
  -- TestListAllAttention,
  (12, 10, 1600681413)
  -- TestActionFollowList, TestActionLiveFeed, TestActionLiveFeedNotice
  ,(109, 12, 1600681413)
  ,(109, 13, 1600681413)
  ,(109, 14, 1600681413)
  ,(109, 15, 1600681413)
  ,(109, 17, 1600681413)
  -- TestGetNotice
  ,(110, 120, 1600681413)
  ,(110, 121, 1600681413)
  ,(110, 122, 1600681413)
  ,(110, 123, 1600681413)
;

CREATE TABLE IF NOT EXISTS `live_addendum` (
  `id` int unsigned NOT NULL PRIMARY KEY COMMENT '主播 ID = UserID'
  ,`vitality` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '元气值, 最大 12'
  ,`last_punished_time` int unsigned NOT NULL DEFAULT '0' COMMENT '上次被惩罚的时间'
  ,`is_agreed` tinyint DEFAULT '0' COMMENT '同意协议。对应的比特位为 1 同意公会主播入驻协议，2 同意主播入驻协议'
  ,`agree_guild_agreement_time` int unsigned NOT NULL DEFAULT '0' COMMENT '同意公会主播入驻协议时间'
  ,`agree_live_agreement_time` int unsigned NOT NULL DEFAULT '0' COMMENT '同意主播入驻协议时间'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int unsigned NOT NULL COMMENT '修改时间'
  ,`user_id` bigint DEFAULT NULL COMMENT '主播 ID'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8
;

CREATE UNIQUE INDEX uk_user_id ON live_addendum(user_id);

INSERT INTO `live_addendum`
-- TestPunish/testVitality
  (`id`, `user_id`, `vitality`, `last_punished_time`, `is_agreed`, `agree_guild_agreement_time`, `agree_live_agreement_time`, `create_time`, `modified_time`)
VALUES
  (10, 10, 12, 0, 0, 0, 0, 1234567890, 1600757446)
  ,(12, 12, 0, 0, 0, 0, 0, 1234567890, 1600757446)
  ,(13, 13, 6, 0, 0, 0, 0, 1234567890, 1600757446)
  ,(248506, 248506, 12, 1574415888, 1, 0, 0, 1478073645, 1579410985)
;

CREATE TABLE IF NOT EXISTS `live_addendum_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`creator_id` int unsigned NOT NULL COMMENT '主播 ID'
  ,`operator` tinyint NOT NULL COMMENT '操作 1: 加元气值, -1: 减元气值, -2: 切断直播 -3: 封禁'
  ,`vitality_change` tinyint NOT NULL COMMENT '元气值变化绝对值'
  ,`reason` varchar(100) NOT NULL COMMENT '原因'
  ,`intro` varchar(20) NOT NULL DEFAULT '' COMMENT '惩罚信息'
  ,`create_time` int unsigned NOT NULL COMMENT '创建时间'
) -- ENGINE=InnoDB AUTO_INCREMENT=30574 DEFAULT CHARSET=utf8
;

INSERT INTO `live_addendum_log`
  (`create_time`, `creator_id`, `operator`, `vitality_change`, `reason`, `intro`)
VALUES
  (1622476800, 2333666, 1, 1, '在公会 3 开播，奖励 1 元气', '')
  ,(1622563200, 2333666, 1, 1, '在公会 3 开播，奖励 1 元气', '')
  ,(1622649600, 2333666, 1, 1, '在公会 3 开播，奖励 1 元气', '')
  ,(1622736000, 2333666, -1, 1, '退会了，扣 1 元气', '')
  ,(1622822400, 2333666, 1, 1, '在公会 3 开播，奖励 1 元气', '')
  ,(1622908800, 2333666, 1, 1, '在公会 3 开播，奖励 1 元气', '')
  ,(1622995200, 2333666, 1, 1, '在公会 3 开播，奖励 1 元气', '')
;

CREATE TABLE IF NOT EXISTS `live_rank_love` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主播的用户 ID'
  ,`room_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间 ID（根据 user_id 获得）'
  ,`character` varchar(10) NOT NULL DEFAULT '' COMMENT '音属：e.g. 正太音'
  ,`point` int(11) NOT NULL DEFAULT '0' COMMENT '心动值：数字代表多少万心动值'
  ,`sound_id` bigint(20) NOT NULL DEFAULT '0' COMMENT 'M音 ID'
  ,`month` int(11) NOT NULL DEFAULT '0' COMMENT '年月：e.g. 201911'
  ,`checked` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已发布：0 未发布；1 已发布'
  ,`create_time` int(11) NOT NULL DEFAULT '0'
  ,`modified_time` int(11) NOT NULL DEFAULT '0'
) -- ENGINE=InnoDB AUTO_INCREMENT=7349 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='心动主播'
;

CREATE TABLE IF NOT EXISTS `m_sound` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`catalog_id` smallint(5) unsigned NOT NULL COMMENT '分类id'
  ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
  ,`last_update_time` int(10) unsigned NOT NULL
  ,`duration` mediumint(8) unsigned NOT NULL COMMENT '持续时间'
  ,`user_id` int(10) unsigned NOT NULL
  ,`username` varchar(20) NOT NULL
  ,`cover_image` varchar(255) NOT NULL COMMENT '封面图片'
  ,`animationid` int(10) unsigned NOT NULL
  ,`characterid` int(10) unsigned NOT NULL
  ,`seiyid` int(10) unsigned NOT NULL
  ,`soundstr` varchar(100) NOT NULL
  ,`intro` text NOT NULL COMMENT '简介'
  ,`soundurl` varchar(100) NOT NULL
  ,`soundurl_32` varchar(100) NOT NULL
  ,`soundurl_64` varchar(100) NOT NULL
  ,`soundurl_128` varchar(100) NOT NULL
  ,`downtimes` int(10) unsigned NOT NULL DEFAULT '0'
  ,`uptimes` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '被赞次数'
  ,`checked` tinyint(4) NOT NULL DEFAULT '0' COMMENT '音频状态，-3：转码失败；-2：配音未转码；-1：未转码；0：审核中；1：已审核通过；2：报警音；3：下架音'
  ,`source` tinyint(4) NOT NULL DEFAULT '0' COMMENT '来源'
  ,`download` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否允许下载'
  ,`view_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查看数'
  ,`comment_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '弹幕数'
  ,`favorite_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '收藏数'
  ,`point` int(10) NOT NULL DEFAULT '0' COMMENT '猫耳数'
  ,`push` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否推送'
  ,`refined` tinyint(3) DEFAULT '0' COMMENT '是否加精'
  ,`comments_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评论数'
  ,`sub_comments_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '子评论数'
  ,`pay_type` tinyint(4) DEFAULT '0'
  ,`type` int(11) DEFAULT '0' COMMENT '音频类型'
) -- ENGINE=InnoDB AUTO_INCREMENT=5657380 DEFAULT CHARSET=utf8
;

INSERT INTO `m_sound`
-- TestAddSoundInfo
  (`id`, `catalog_id`, `create_time`, `last_update_time`, `duration`, `user_id`, `username`, `cover_image`, `animationid`, `characterid`, `seiyid`, `soundstr`, `intro`, `soundurl`, `soundurl_32`, `soundurl_64`, `soundurl_128`, `downtimes`, `uptimes`, `checked`, `source`, `download`, `view_count`, `comment_count`, `favorite_count`, `point`, `push`, `refined`, `comments_count`, `sub_comments_count`, `pay_type`, `type`)
VALUES
  (44809, 19, 1435888828, 1565147366, 609209, 100913, '传奇火箭队', '201507/03/7ae84f02fe85afe9729cf4c7501f92ee100027.jpg', 0, 0, 0, '金牌助理之弯弯没想到 S02E00', '<p>【传奇火箭队出品】非天夜翔原著◎现代DM广播剧《金牌助理之弯弯没想到》第二季试播集（S02E00）</p><p><br /></p><p>工作组：</p><p>-传奇火箭队-</p><p>制作：临时攻【三好街民攻团】、瀛洲越客【颠覆声社】</p><p>海报：点八斤</p><p>宣传：传火营销部</p><p><br /></p><p>配音组：</p><p>报幕/旁白：雾一【绘音配音】</p><p>萧毅：<a href="http://www.missevan.com/seiy/1097">梁铎</a>【颠覆声社】</p><p>卢舟：楼生</p><p>于导演：<a href="http://www.missevan.com/seiy/1098">Mixtan</a></p><p>宁亚晴：芒果抽【颠覆声社】</p><p>小美：韶小韶【优思优铭】</p><p>杜马：燃【水岸聆音】</p><p>杜梅：KONG嘘【KA.U】</p><p>乌恒古：<a href="http://www.missevan.com/seiy/1118">柯暮卿</a>【优声由色】</p><p><br /></p><p>ED：《弯弯没想到》</p><p>原曲：《万万没想到》</p><p>填词：瀛洲越客【颠覆声社】</p><p>后期：茶雪</p><p>翻唱：<a href="http://www.missevan.com/seiy/1097">梁铎</a>【颠覆声社】ft.楼生</p><p><br /></p>', '201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', '32BIT/201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', 'MP3/201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', '128BIT/201507/03/f2ac1e2ed49df3cb4f57c38517285463100024.mp3', 54, 277, 1, 1, 0, 0, 1008, 5844, 273, 0, 0, 273, 3, 0, 0);
;

CREATE TABLE IF NOT EXISTS `guild_live_contract` (
  `id`                 int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`guild_id`          int unsigned NOT NULL COMMENT '公会 ID'
  ,`guild_owner`       int unsigned NOT NULL COMMENT '公会会长 ID'
  ,`guild_name`        varchar(50)  NOT NULL COMMENT '公会名称'
  ,`live_id`           int unsigned NOT NULL COMMENT '主播 ID'
  ,`contract_duration` int unsigned NOT NULL DEFAULT '0' COMMENT '签约时限'
  ,`contract_start`    int unsigned NOT NULL COMMENT '合约起始时间'
  ,`contract_end`      int unsigned NOT NULL COMMENT '合约结束时间'
  ,`rate`              int unsigned NOT NULL COMMENT '主播所占分成比例'
  ,`kpi`               varchar(255) NOT NULL COMMENT '指标'
  ,`type`              tinyint      NOT NULL COMMENT '签约类型（1 主播发起，2 公会发起）'
  ,`attr`              int unsigned NOT NULL DEFAULT '0' COMMENT '按位运算：第 1 位表示主播在合约期内申请过协商解约；第 2 位表示主播有未处理的降薪申请'
  ,`status`            int          NOT NULL COMMENT '合约状态，-3：已解约；-2：已失效；-1：已拒绝；0：申请/邀约中；1：合约生效中（主播申请入会通过或公会邀约通过）； 2：合约生效中（主播申请退会或公会发起解约）'
  ,`create_time`       int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time`     int unsigned NOT NULL COMMENT '修改时间'
)
;

INSERT INTO `guild_live_contract`
  (`id`, `guild_id`, `live_id`, `contract_start`, `contract_end`, `contract_duration`, `rate`, `kpi`, `status`, `type`, `guild_owner`, `guild_name`, `create_time`, `modified_time`)
VALUES
  -- TestIsInGuild, TestFindInContractingByLiveID
  (11167, 1897, 5003, 1569378503, 1999999999, 0, 0, '', 1, 1, 3013090, '测试公会（匆删）', 1569378503, 1569378503)
  -- TestRecommendScheduleApplyParamCheck
  ,(20210701, 3, 12, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会（匆删）', 1569378503, 1569378503)
  -- TestUserGuildRole
  ,(********, ********, ********, 1569378503, 1999999999, 0, 0, '', 1, 1, 3013090, '测试公会（匆删）', 1569378503, 1569378503)
  -- TestFindManagerRole
  ,(20210707, 20210707, 20210707, 1569378503, 1999999999, 0, 0, '', 1, 1, 3013090, '公会 0707', 1569378503, 1569378503)
  ,(2021070701, 20210707, 20210707, 1569378503, 1999999999, 0, 0, '', 1, 1, 3013090, '公会 0707', 1569378503, 1569378503)
  ,(23333472, 3, 5, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会 23333472（匆删）', 1569378503, 1569378503)
  ,(23333473, 3, 10, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会 23333473（匆删）', 1569378503, 1569378503)
  ,(23333474, 3, 892, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会 23333474（匆删）', 1569378503, 1569378503)
  ,(23333475, 3, 107, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会三方独家主播 1（匆删）', 1569378503, 1569378503)
  ,(23333476, 3, 108, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会三方独家主播 2（匆删）', 1569378503, 1569378503)
  -- TestApplySearch
  ,(24333476, 3, 11, 1565002308, 1999999999, 0, 0, '', 1, 1, 12, '测试公会（匆删）', 1569378503, 1569378503)
  -- TestListLiveDailyReport
  ,(25333471, 3, 2333666, 1622476800, 1622649600, 0, 0, '', 1, 1, 12, '测试公会（匆删）', 1569378503+8*3601, 1569378503+8*3601)
  ,(25333472, 3, 2333666, 1622822400, 1622995200, 0, 0, '', 1, 1, 12, '测试公会（匆删）', 1569378503+8*3601, 1569378503+8*3601)
;

CREATE TABLE `live_medal_review`
(
  `id`             bigint unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id`       bigint unsigned NOT NULL COMMENT '主播 ID'
  ,`room_id`       bigint unsigned NOT NULL COMMENT '房间 ID'
  ,`name`          varchar(10)     NOT NULL COMMENT '勋章名称'
  ,`status`        tinyint         NOT NULL DEFAULT '0' COMMENT '状态，-2：被拒绝，-1：拒绝待修改，0：审核中，1：审核通过'
  ,`create_time`   bigint unsigned NOT NULL DEFAULT '0'
  ,`modified_time` bigint unsigned NOT NULL DEFAULT '0'
) -- ENGINE=InnoDB AUTO_INCREMENT=21104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='主播粉丝勋章审核';
;

INSERT INTO `live_medal_review`
  (`id`, `user_id`, `room_id`, `name`, `status`)
VALUES
  (1034, 202001, 202001, '新年 ab', -1)
;

CREATE TABLE IF NOT EXISTS `live_noble_recommend`
(
  `id`             bigint     NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`from_user_id`  bigint     NOT NULL DEFAULT '0' COMMENT '推荐贵族用户 ID'
  ,`creator_id`    bigint     NOT NULL DEFAULT '0' COMMENT '被推荐主播 ID'
  ,`room_id`       bigint     NOT NULL DEFAULT '0' COMMENT '房间 ID'
  ,`anonymous`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否匿名推荐 默认为 0 非匿名推荐'
  ,`status`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '推荐状态：0: 正常；1: 被取消'
  ,`recommeded_schedule_id` bigint DEFAULT '0' NOT NULL COMMENT '开播列表推荐 ID'
  ,`start_time`    bigint     NOT NULL DEFAULT '0'
  ,`end_time`      bigint     NOT NULL DEFAULT '0'
  ,`create_time`   bigint     NOT NULL DEFAULT '0'
  ,`modified_time` bigint     NOT NULL DEFAULT '0'
) -- ENGINE = InnoDB AUTO_INCREMENT = 3897 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT ='贵族推荐';
;

INSERT INTO `live_noble_recommend`
  (`id`, `from_user_id`, `creator_id`, `room_id`, `anonymous`, `start_time`, `end_time`, `create_time`, `modified_time`,`recommeded_schedule_id`, `status`)
VALUES
  (1, 248506, 248506, 130709352, 0, 1602313200, 1602322200, 1580974035, 1580974035, 1, 0)
  ,(2, 12, 10, 12345, 0, 0, 1800, 1582795167, 1582795167, 2, 0)
  ,(3, 12, 12, 1234567, 0, 1602434700, 1602438000, 1582795899, 1582795899, 3, 0)
  -- TestCancelRecommendcheckUserAndRecommendID 有记录没有主播的情况
  ,(4, 12, 87498234, 1234567, 0, 1602434700, 1602438000, 1582795899, 1582795899, 5, 1)
  -- TestActionCancelRecommend 没有生效的推荐
  ,(5, 12, 10, 1234567, 0, 1999999999, 1999999999, 1582795899, 1582795899, 6, 0)
  ,(6, 248506, 248506, 130709352, 0, 0, 1999999999, 1580974035, 1580974035, 1, 1)
  ,(220, 3457024, 3456864, 61687673, 0, 1708469200, 1708469200, 1608381169, 1608381204, 656, 0)
;

-- TODO: 有些字段没有用到，但是影响了插入
CREATE TABLE IF NOT EXISTS m_event (
  id int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,title varchar(256) NOT NULL COMMENT '活动名称'
  ,mobile_cover varchar(255) DEFAULT NULL COMMENT '手机端封面'
  --,main_cover varchar(256) NOT NULL COMMENT '活动封面'
  --,share_cover varchar(255) NOT NULL DEFAULT '' COMMENT '分享封面图'
  --,intro text NOT NULL COMMENT '活动介绍'
  ,short_intro varchar(255) DEFAULT NULL COMMENT '活动短介绍'
  ,bilibili_url_pc varchar(255) DEFAULT NULL COMMENT 'B站配置的 PC url'
  ,bilibili_url_h5 varchar(255) DEFAULT NULL COMMENT 'B站配置的 H5 url'
  ,tag_id int DEFAULT NULL
  --,tag varchar(64) NOT NULL COMMENT '活动标签'
  ,type tinyint unsigned NOT NULL COMMENT '活动上传类型0是音频，1是图片'
  --,vote_time int unsigned NOT NULL COMMENT '投票开始时间'
  ,vote_start_time bigint NOT NULL DEFAULT '0' COMMENT '投票开始时间'
  --,vote_end_time int unsigned NOT NULL COMMENT '投票结束时间'
  ,draw_start_time bigint NOT NULL DEFAULT '0' COMMENT '抽奖开始时间'
  ,draw_end_time bigint NOT NULL DEFAULT '0' COMMENT '抽奖结束时间'
  ,create_time int unsigned NOT NULL COMMENT '投稿开始时间'
  ,end_time int unsigned NOT NULL COMMENT '投稿结束时间'
  ,head varchar(255) DEFAULT NULL COMMENT '音频头'
  ,tail varchar(255) DEFAULT NULL COMMENT '音频尾'
  ,extended_fields text COMMENT '额外数据，JSON format'
  ,status tinyint DEFAULT NULL COMMENT '手机端是否可见'
  ,`limit` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '活动每日投票上限'
  ,mini_cover varchar(255) DEFAULT NULL COMMENT '290图片'
  ,limit_work tinyint unsigned DEFAULT '0' COMMENT '每日可投票作品数量限制'
  ,limit_vote tinyint unsigned DEFAULT '0' COMMENT '每日每作品可投票数量限制'
  ,do_comment tinyint unsigned DEFAULT '0' COMMENT '活动是否支持评论 0：否；1：是'
  ,attr tinyint unsigned NOT NULL DEFAULT '3' COMMENT '活动属性'
  ,start_time BIGINT NOT NULL COMMENT '活动开始时间'
)
;

INSERT INTO m_event
  (id, title, type, create_time, end_time, extended_fields, status, attr, start_time)
VALUES
-- TestGetEventTime
  (110, '山海镜花首发抽奖', 3, 1586415060, 1589871060, NULL, 7, 3, 1586415060)
  ,(124,'124 活动',0,1596211200,1598889540,null,15,17,1596211200)
  ,(134,'双倍亲密度',3,1601481600,1602172800,null,31,16,1601481600)
  ,(138, '一起听', 5, 1604160000, 1606752000, '{"collect":{"type":2},"noble_extra":1,"ranks":[{"key":"k"}],"application":2,"catalog":{"type":3,"catalog_id":104}}', 31, 16, 1604160000)
  ,(139, '失眠星人 3.0', 5, 1603209600, 1606665600, '{"collect":{"gift_id":134, "max_num":2500},"application":1, "catalog":{"type":3,"catalog_id":115},"apply_start_time":1603944000, "apply_end_time":1606751940}', 15, 8, 1603209600)
  ,(140,'【直播情感区】心动管理局 —— 嘿！你的心动，被逮捕了',5,1606752000,1608048000,'{"collect":{"type":2},"apply_start_time":1606406400,"apply_end_time":1608048000}',31,24,1606752000)
  ,(142, '娱乐 Pia 戏活动 142', 5, 1607184000, 1608307200, '{"collect":{"type":2},"max_count":12, "default_page_size":12,"application":1,"apply_start_time":1607184000, "apply_end_time":1608307200,"score":0,"show_my_rank":false}', 15, 0, 1607184000)
  ,(144, '新星挑战营', 4, 1606752000, 1609344000, NULL, 8, 3, 1606752000)
  ,(145, '155 圣诞遇礼', 5, 1607339580, 1608981000, '{"collect":{"type":1,"gifts":[{"gift_id":10009,"max_num":0},{"gift_id":10010,"max_num":0},{"gift_id":10011,"max_num":0},{"gift_id":10012,"max_num":0},{"gift_id":10013,"max_num":0},{"gift_id":10014,"max_num":0},{"gift_id":10015,"max_num":0}]},"application":0,"rank_start_time":1608566400,"rank_end_time":1608912000,"score":50000}', 15, 0, 0)
  ,(157, '直播超粉嘉年华活动', 5, 1618998243, 1622303999, '{"draw_price": 10,"draw_point_start_time": 0,"draw_point_end_time": 1622303999, "box_start_time": 1619798400, "box_end_time": 1622304000}', 15, 0, 1618998243)
  ,(158, '直播超粉嘉年华活动', 5, 1618998243, 1622303999, '{"draw_price": 10,"draw_point_start_time": 0,"draw_point_end_time": 1622303999, "box_start_time": 1619798400, "box_end_time": 1622304000}', 15, 0, 1618998243)
  ,(160, '猫耳娱乐夏日 PARTY', 5, 1606752000, 1608048000, '{"apply_start_time":1606406400,"apply_end_time":1633852584}', 31, 24, 1606752000)
  -- TestWallSend
  ,(166, '166 告白大作战', 5, 1, 3376656000, '{"application":1,"noble_extra":1,"promoted_score":0,"rank_end_time":1637370520,"rank_max_count":200,"rank_start_time":1627370520,"show_my_rank":true}', 15, 0, 1618998243)
  ,(168, '168 月球探索计划', 6, 1630316400, 1632907260, '{"application":0,"collect":{"type":2},"noble_extra":1,"promoted_score":0,"rank_end_time":1630382400,"rank_max_count":200,"rank_start_time":1630296000,"show_my_rank":true,"time_offset":0,"set_up_time":1630743168}', 6, 16, 1630316400)
  ,(169, '头号公会请就位', 5, 1632452640, 1635044640, '{"application":0,"noble_extra":1,"collect":{"type":2},"ranks":[{"key":"1","promotion_num":100,"rank_end_time":1632499199,"rank_start_time":1632412800},{"key":"2","promotion_num":60,"rank_end_time":1632585599,"rank_start_time":1632499200,"last_key":"1","last_promotion_range":[0,60]},{"key":"3","promotion_num":30,"rank_end_time":1632671999,"rank_start_time":1632585600,"last_key":"2","last_promotion_range":[0,30]},{"key":"4","promotion_num":10,"rank_end_time":1632751199,"rank_start_time":1632672000,"last_key":"3","last_promotion_range":[0,10]},{"key":"5","promotion_num":8,"rank_end_time":1632752999,"rank_start_time":1632751200,"last_key":"4","last_promotion_range":[0,8]},{"key":"6","promotion_num":6,"rank_end_time":1632754799,"rank_start_time":1632753000,"last_key":"5","last_promotion_range":[0,6]},{"key":"7","promotion_num":4,"rank_end_time":1632756599,"rank_start_time":1632754800,"last_key":"6","last_promotion_range":[0,4]},{"key":"8","rank_end_time":1632758399,"rank_start_time":1632756600,"last_key":"7"}],"rank_end_time":1632765600,"rank_max_count":200,"rank_start_time":1632412800,"show_my_rank":true,"default_page_size":10}', 15, 16, 1632452640 )
  -- TestGetEventWithExtendFields
  ,(131995, '测试extendFields', 4, 1601481600, 1606751999, '{"max_count":10,"apply_start_time":1603987200, "apply_end_time":1606723199}', 8, 3, 1601481600)
  -- TestActionRankConfig
  ,(133545, '新年活动', 0, 4070880000, 4073558400, null, 15, 0, 0)
  ,(133546, '测试活动', 0, 4070880000, 4073558400, null, 15, 0, 0)
  ,(144410, '测试游戏预约活动', 4, 4070880000, 4073558400, '{"application":0,"promoted_score":0,"rank_end_time":4073558400,"rank_max_count":100,"rank_start_time":4070880000,"show_my_rank":false}', 8, 8, 4070880000)
  -- rankRevenue
  ,(999997,'多榜单积分活动',5,3376684800,3408220800,'{"application":2,"noble_extra":1,"collect":{"type":2},"ranks":[{"key":"1","promotion_num":8,"rank_end_time":3376684900,"rank_start_time":3376684800},{"key":"2","promotion_num":4,"rank_end_time":3376685000,"rank_start_time":3376684900,"last_key":"1","last_promotion_range":[0,4]},{"key":"3","promotion_num":4,"rank_end_time":3376685000,"rank_start_time":3376684900,"last_key":"1","last_promotion_range":[5,8]},{"key":"4","promotion_num":2,"rank_end_time":3376685100,"rank_start_time":3376685000,"last_key":"2","last_promotion_range":[1,2]},{"key":"5","promotion_num":2,"rank_end_time":3376685100,"rank_start_time":3376685000,"last_key":"3","last_promotion_range":[0,2]}],"rank_end_time":3408220800,"rank_start_time":3376684800}',15, 16, 3376684800)
  ,(999998, '一直开启公会活动', 5, 1, 3376656000, '{"application":0,"collect":{"type":3},"noble_extra":1,"promoted_score":0,"rank_end_time":1633622400,"rank_max_count":200,"rank_start_time":1633017600,"show_my_rank":true}', 15, 16, 1)
  ,(999999, '一直开启积分活动', 5, 1, 3376656000, '{"application":1,"noble_extra":1,"collect":{"gifts":[{"gift_id":7777,"max_num":0},{"gift_id":8888,"max_num":0},{"gift_id":9999,"max_num":0}],"type":2},"catalog":1,"rank_end_time":3376656000,"rank_start_time":1}', 15, 16, 1)
  ,(175, '直播年度盛典', 5, 1618998243, 1622303999, null, 15, 0, 1618998243)
  ,(182, '新年活动', 5, 1, 3376656000, '{"ranks":[{"collect":{"type":6},"key":"20220131","rank_start_time":1643558400,"rank_end_time":1643817600},{"collect":{"type":6},"key":"20220201","rank_start_time":1643558400,"rank_end_time":1643817600},{"collect":{"type":6},"key":"20220202","rank_start_time":1643558400,"rank_end_time":1643817600}],"collect":{"type":6},"application":0,"show_my_rank":false,"promoted_score":1500,"rank_max_count":150,"rank_start_time":1643558400,"rank_end_time":1643817600}', 15, 16, 1)
  ,(193, '花间集', 5, 1, 3376656000, '{"ranks":[{"collect":{"type":6},"key":"20220131","rank_start_time":1643558400,"rank_end_time":1643817600},{"collect":{"type":6},"key":"20220201","rank_start_time":1643558400,"rank_end_time":1643817600},{"collect":{"type":6},"key":"20220202","rank_start_time":1643558400,"rank_end_time":1643817600}],"collect":{"type":6},"application":0,"show_my_rank":false,"promoted_score":1500,"rank_max_count":150,"rank_start_time":1643558400,"rank_end_time":1643817600}', 15, 16, 1)
  ,(196, '风月宴知音', 5, 1, 3376656000, '{"application":0,"collect":{"type":2},"noble_extra":1,"promoted_score":9999999,"rank_end_time":1653840000,"rank_max_count":198,"rank_start_time":1652976000,"show_my_rank":true,"ranks":[{"collect":{"type":2},"noble_extra":1,"promotion_num":198,"promoted_score":9999999,"rank_end_time":1653231600,"widget_end_time":1653235200,"rank_max_count":200,"rank_start_time":1652976000,"key":"20to22"},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653318000,"widget_end_time":1653321600,"rank_max_count":18,"rank_start_time":1653235200,"key":"23S","last_key":"20to22","last_promotion_range":[1,18]},{"collect":{"type":2},"noble_extra":1,"promotion_num":20,"rank_end_time":1653318000,"widget_end_time":1653321600,"rank_max_count":24,"rank_start_time":1653235200,"key":"23A","last_key":"20to22","last_promotion_range":[19,42]},{"collect":{"type":2},"noble_extra":1,"promotion_num":36,"rank_end_time":1653318000,"widget_end_time":1653321600,"rank_max_count":56,"rank_start_time":1653235200,"key":"23B","last_key":"20to22","last_promotion_range":[43,98]},{"collect":{"type":2},"noble_extra":1,"promotion_num":54,"rank_end_time":1653318000,"widget_end_time":1653321600,"rank_max_count":100,"rank_start_time":1653235200,"key":"23C","last_key":"20to22","last_promotion_range":[99,198]},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653404400,"widget_end_time":1653408000,"rank_max_count":14,"rank_start_time":1653321600,"key":"24S","last_key":"24S","last_promotion_range":[0,0]},{"collect":{"type":2},"noble_extra":1,"promotion_num":20,"rank_end_time":1653404400,"widget_end_time":1653408000,"rank_max_count":24,"rank_start_time":1653321600,"key":"24A","last_key":"24A","last_promotion_range":[0,0]},{"collect":{"type":2},"noble_extra":1,"promotion_num":24,"rank_end_time":1653404400,"widget_end_time":1653408000,"rank_start_time":1653321600,"key":"24B","last_key":"23B","last_promotion_range":[1,36]},{"collect":{"type":2},"noble_extra":1,"promotion_num":30,"rank_end_time":1653404400,"widget_end_time":1653408000,"rank_start_time":1653321600,"key":"24C","last_key":"23C","last_promotion_range":[1,54]},{"collect":{"type":2},"noble_extra":1,"promotion_num":6,"rank_end_time":1653490800,"widget_end_time":1653494400,"rank_start_time":1653408000,"key":"25S","last_key":"25S","last_promotion_range":[0,0]},{"collect":{"type":2},"noble_extra":1,"promotion_num":20,"rank_end_time":1653490800,"widget_end_time":1653494400,"rank_start_time":1653408000,"key":"25A","last_key":"25A","last_promotion_range":[0,0]},{"collect":{"type":2},"noble_extra":1,"promotion_num":16,"rank_end_time":1653490800,"widget_end_time":1653494400,"rank_start_time":1653408000,"key":"25B","last_key":"24B","last_promotion_range":[1,24]},{"collect":{"type":2},"noble_extra":1,"promotion_num":12,"rank_end_time":1653490800,"widget_end_time":1653494400,"rank_start_time":1653408000,"key":"25C","last_key":"24C","last_promotion_range":[1,30]},{"collect":{"type":2},"noble_extra":1,"application":1,"promotion_num":22,"rank_end_time":1653577200,"widget_end_time":1653580800,"rank_start_time":1653494400,"key":"26","middlewares":[{"func":"challenger_buff","params":{"keys":["26ring1","26ring2","26ring3","26ring4","26ring5","26ring6"],"duration":1800000}}]},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_max_count":5,"rank_end_time":1653562800,"widget_end_time":0,"rank_start_time":1653561000,"key":"26ring1"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_max_count":5,"rank_end_time":1653564600,"widget_end_time":0,"rank_start_time":1653562800,"key":"26ring2"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653566400,"rank_max_count":5,"widget_end_time":0,"rank_start_time":1653564600,"key":"26ring3"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653568200,"rank_max_count":5,"widget_end_time":0,"rank_start_time":1653566400,"key":"26ring4"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653570000,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653568200,"key":"26ring5"},{"collect":{"type":2},"noble_extra":1,"rank_max_count":5,"application":1,"rank_end_time":1653571800,"widget_end_time":0,"rank_start_time":1653570000,"key":"26ring6"},{"collect":{"type":2},"noble_extra":1,"application":1,"promotion_num":10,"rank_end_time":1653663600,"widget_end_time":1653667200,"rank_start_time":1653580800,"key":"27","middlewares":[{"func":"challenger_buff","params":{"keys":["27ring1","27ring2","27ring3","27ring4"],"duration":1800000}}]},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653652800,"widget_end_time":0,"rank_start_time":1653651000,"rank_max_count":5,"key":"27ring1"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653654600,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653652800,"key":"27ring2"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653656400,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653654600,"key":"27ring3"},{"collect":{"type":2},"noble_extra":1,"application":1,"rank_end_time":1653658200,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653656400,"key":"27ring4"},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653750000,"widget_end_time":1653753600,"rank_start_time":1653667200,"key":"28","last_key":"27","last_promotion_range":[1,10],"middlewares":[{"func":"challenger_buff","params":{"keys":["28ring1","28ring2","28ring3","28ring4"],"duration":1800000}}]},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653739200,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653737400,"key":"28ring1","last_key":"27","last_promotion_range":[1,10]},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653741000,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653739200,"key":"28ring2","last_key":"27","last_promotion_range":[1,10]},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653742800,"widget_end_time":0,"rank_start_time":1653741000,"rank_max_count":5,"key":"28ring3","last_key":"27","last_promotion_range":[1,10]},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653744600,"widget_end_time":0,"rank_max_count":5,"rank_start_time":1653742800,"key":"28ring4","last_key":"27","last_promotion_range":[1,10]},{"collect":{"type":2},"noble_extra":1,"rank_end_time":1653753600,"widget_end_time":0,"rank_max_count":200,"rank_start_time":1652976000,"key":"creator"},{"key":"box_1","collect":{"type":2},"noble_extra":1,"rank_end_time":1653062399,"rank_start_time":1652976000,"widget_end_time":0},{"key":"box_2","collect":{"type":2},"noble_extra":1,"rank_end_time":1653148799,"rank_start_time":1653062400,"widget_end_time":0},{"key":"box_3","collect":{"type":2},"noble_extra":1,"rank_end_time":1653231599,"rank_start_time":1653148800,"widget_end_time":0}],"box_start_time":1652976000,"box_end_time":1653235199,"draw_price":1,"draw_point_start_time":1652976000,"draw_point_end_time":1653753600}', 15, 0, 1618998243)
  ,(211, '情人节活动', 5, 1, 3376656000, '{"ranks":[{"collect":{"type":6},"key":"20220131","rank_start_time":1643558400,"rank_end_time":1643817600},{"collect":{"type":6},"key":"20220201","rank_start_time":1643558400,"rank_end_time":1643817600},{"collect":{"type":6},"key":"20220202","rank_start_time":1643558400,"rank_end_time":1643817600}],"collect":{"type":6},"application":0,"show_my_rank":false,"promoted_score":1500,"rank_max_count":150,"rank_start_time":1643558400,"rank_end_time":1643817600}', 15, 16, 1)
  ,(214, '星座许愿池', 5, 1, 3376656000, '{}', 15, 16, 1660723046)
;

CREATE TABLE IF NOT EXISTS `event_vote_detail` (
	`id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY
	,`user_id` INT(10) UNSIGNED NOT NULL COMMENT '用户 ID'
	,`eid` INT(10) UNSIGNED NOT NULL COMMENT '资源 ID'
	,`event_id` INT(10) UNSIGNED NOT NULL COMMENT '活动 ID'
	,`time` INT(10) UNSIGNED NOT NULL COMMENT '投票时间'
	,`ip` VARCHAR(50) NOT NULL COMMENT '用户 IP'
	,`env` TINYINT(3) UNSIGNED NOT NULL COMMENT '1.Web 2.安卓 3.iOS 4.手机网页'
)
;

INSERT INTO event_vote_detail
    (id, user_id, eid, event_id, `time`, ip, env)
VALUES
    (428258, 473927, 0, 105, 1582624076, '127.0.0.1', 1)
    ,(223355, 223355, 0, 105, 1582624076, '127.0.0.1', 1)
    ,(223356, 223355, 0, 140, 1602259200, '127.0.0.1', 1)
;

CREATE TABLE IF NOT EXISTS `certification` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
    ,`user_id` int(10) unsigned NOT NULL COMMENT 'M号'
    ,`user_name` varchar(20) NOT NULL COMMENT '用户名'
    ,`real_name` varchar(50) NOT NULL COMMENT '真实姓名'
    ,`gender` tinyint(4) NOT NULL DEFAULT '0' COMMENT '性别（0 未知，1 为男，2 为女）'
    ,`id_type` tinyint(3) NOT NULL DEFAULT '1' COMMENT '证件类型'
    ,`id_number` varchar(50) NOT NULL COMMENT '证件号码'
    ,`id_people` varchar(255) DEFAULT NULL COMMENT '上传的手持证件照'
    ,`id_front` varchar(255) DEFAULT NULL COMMENT '上传的证件照正面'
    ,`id_back` varchar(255) DEFAULT NULL COMMENT '上传的证件照背面'
    ,`method` tinyint(3) unsigned DEFAULT '0' COMMENT '认证来源 0 未知 1 身份证 2 芝麻信用'
    ,`checked` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '审核状态 -1 已失效 0 未审核 1 过审 2 拒绝 3 失效'
    ,`create_time` int(10) unsigned NOT NULL COMMENT '申请审核时间'
    ,`update_time` int(10) unsigned NOT NULL COMMENT '更新时间 一般为审核时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

INSERT INTO `certification`
  (`id`, `user_id`, `user_name`, `real_name`, `gender`, `id_type`, `id_number`, `id_people`, `id_front`, `id_back`, `method`, `checked`, `create_time`, `update_time`)
VALUES
  -- TestActionSearchLive
  (1, 12, '文森特', '4oiC1E70nxoX3whSd4ndlQ==', 1, 11, 's+pk8cBgTqTF9rAJYfHonykoqrEiOx6jKxbkniWKrPE=', '201703/20/200f5fe25387c9de46232c1a97682d62175013.png', '201703/20/bfca075be18f0f16fdcb7f013ed6a17f175013.png', '201703/20/a48a04ec58500f8c006b7c8f6f0208b9175014.png', 0, 1, 1490003413, 1490237391)
;

CREATE TABLE IF NOT EXISTS `guild` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`name` varchar(50) NOT NULL COMMENT '公会名称'
    ,`intro` varchar(200) NOT NULL DEFAULT '' COMMENT '公会简介'
    ,`owner_name` varchar(255) NOT NULL COMMENT '法人代表姓名'
    ,`owner_id_number` varchar(255) NOT NULL COMMENT '法人代表身份证号'
    ,`owner_id_people` varchar(255) NOT NULL COMMENT '法人代表手持身份证正面照'
    ,`owner_backcover` varchar(255) NOT NULL COMMENT '法人代表身份证背面'
    ,`mobile` varchar(255) NOT NULL COMMENT '法人代表手机号'
    ,`email` varchar(255) NOT NULL COMMENT '邮箱'
    ,`qq` varchar(20) NOT NULL DEFAULT '' COMMENT 'QQ 号'
    ,`corporation_name` varchar(255) NOT NULL COMMENT '公司名称'
    ,`corporation_address` varchar(255) NOT NULL COMMENT '公司地址'
    ,`corporation_phone` varchar(255) NOT NULL COMMENT '公司电话'
    ,`business_license_number` varchar(255) NOT NULL COMMENT '营业执照号'
    ,`business_license_frontcover` varchar(255) NOT NULL COMMENT '营业执照扫描件'
    ,`tax_account` varchar(255) NOT NULL COMMENT '纳税人识别号'
    ,`bank_account` varchar(255) NOT NULL COMMENT '银行卡号'
    ,`bank_account_name` varchar(20) NOT NULL COMMENT '银行开户名'
    ,`bank` varchar(20) NOT NULL COMMENT '开户行'
    ,`bank_address` varchar(20) NOT NULL COMMENT '开户行所在地'
    ,`bank_branch` varchar(255) NOT NULL COMMENT '开户支行'
    ,`invoice_rate` tinyint(4) NOT NULL DEFAULT '-2' COMMENT '发票税率（-2 税率未知，-1 不开具发票）'
    ,`type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '公会信息类型 1: 加密'
    ,`checked` tinyint(3) NOT NULL DEFAULT '0' COMMENT '公会状态（-1 审核驳回，0 审核中，1 审核通过，2 解散）'
    ,`user_id` int(10) unsigned NOT NULL COMMENT '公会创建人用户 ID'
    ,`apply_time` int(10) unsigned NOT NULL COMMENT '申请时间'
    ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
    ,`modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
    ,`live_num` int unsigned NOT NULL DEFAULT '0' COMMENT '签约主播数'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公会表'
;

INSERT INTO `guild`
  (`id`, `name`, `intro`, `owner_name`, `owner_id_number`, `owner_id_people`, `owner_backcover`, `mobile`, `email`, `qq`, `corporation_name`, `corporation_address`, `corporation_phone`, `business_license_number`, `business_license_frontcover`, `tax_account`, `bank_account`, `bank_account_name`, `bank`, `bank_address`, `bank_branch`, `invoice_rate`, `type`, `checked`, `user_id`, `apply_time`, `create_time`, `modified_time`, `live_num`)
VALUES
  -- TestActionRecommendScheduleApplyAdd
  (3, '测试公会（匆删）', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, 12, 0, 12, **********, 2767)
  -- TestIncreaseLiveNum
  ,(7, '测试公会（匆删）', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, ********, 0, 12, **********, 4)
  ,(6, '测试公会（匆删）', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, ********, 0, 12, **********, 1)
  -- TestActionGuildCreatorTerminateForcely
  ,(66, '测试公会（匆删）', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, ********, 0, 12, **********, 10)
  -- TestUserGuildRole
  ,(********, '测试公会（匆删）', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, ********, 0, 12, **********, 0)
  -- TestFindManagerRole
  ,(20210707, '公会 0707', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, 20210707, 0, 12, **********, 0)
  -- TestSearchByGuildName
  ,(20210708, '测试搜索公会 1', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, 20210707, 0, 12, **********, 0)
  ,(20210709, '测试搜索公会 2', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, 20210707, 0, 12, **********, 0)
;

CREATE TABLE IF NOT EXISTS `guild_live_contract_applyment` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`live_id` int(10) unsigned NOT NULL COMMENT '主播 ID'
    ,`guild_id` int(10) unsigned NOT NULL COMMENT '公会 ID'
    ,`agent_id` int(10) unsigned NOT NULL COMMENT '经纪人 ID'
    ,`guild_name` varchar(50) NOT NULL DEFAULT '' COMMENT '公会名称'
    ,`contract_id` int(10) unsigned NOT NULL COMMENT '合同 ID'
    ,`contract_expire_time` int(10) unsigned NOT NULL COMMENT '合同过期时间'
    ,`type` tinyint(4) NOT NULL COMMENT '1.主播申请签约；2. 公会申请签约；3.主播申请签约；4. 主播申请续约；5.协商解约；6. 公会清退；7.强制解约'
    ,`contract_duration` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '合约时长：1. 6 个月；2. 1 年；3. 2 年'
    ,`rate` int(10) unsigned NOT NULL DEFAULT '45' COMMENT '分成比例或违约金额（分）'
    ,`expire_time` int(10) unsigned NOT NULL COMMENT '失效时间'
    ,`process_time` int(10) unsigned NOT NULL COMMENT '合约处理时间'
    ,`status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：-2 已撤回；-1 被对方拒绝；0 待处理；1 被对方同意'
    ,`initiator` tinyint(4) NOT NULL COMMENT '发起方（1 主播发起，2 公会发起）'
    ,`more` text NULL COMMENT 'json 格式，其中 attr 表示发起申请前合约表的 attr 值；rate 表示发起申请前最低分成比例值'
    ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
    ,`modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公会与主播合约申请表'
;

CREATE TABLE `live_rank_love_selection` (
  `id`             bigint     NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id`       bigint     NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`creator_id`    bigint     NOT NULL DEFAULT '0' COMMENT '主播 ID'
  ,`month`         int        NOT NULL DEFAULT '0' COMMENT '年月：e.g. 201912'
  ,`type`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '心动主播类型 0: 月度心动主播 1: 活动心动主播'
  ,`create_time`   bigint     NOT NULL DEFAULT '0'
  ,`modified_time` bigint     NOT NULL DEFAULT '0'
)
;

CREATE TABLE `event_vote` (
  `id`         int unsigned      NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`event_id`   int unsigned     NOT NULL COMMENT '活动id'
  ,`eid`        int unsigned     NOT NULL COMMENT '资源id'
  ,`vote_num`   int unsigned     NOT NULL DEFAULT '0' COMMENT '投票数'
  ,`category`   tinyint unsigned NOT NULL COMMENT '资源类型0单音1图片'
  ,`reset_vote` int              NOT NULL DEFAULT '0' COMMENT '扣除的票数'
)
;

CREATE TABLE IF NOT EXISTS `user_oa` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`oa_name` varchar(20) NOT NULL COMMENT 'OA 账号'
  ,`user_id` bigint(20) NOT NULL COMMENT '用户 ID'
  ,`status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 -1：取消绑定；1：已绑定'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OA 账号和用户 ID 关联表'
;

INSERT INTO `user_oa`
  (`id`, `oa_name`, `user_id`, `status`, `create_time`, `modified_time`, `delete_time`)
VALUES
  (1, '测试 OA 名称', 12, 1, 1624530554, 1624530554, 0);

CREATE TABLE IF NOT EXISTS `gift` (
    `id` INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY
	,`name` VARCHAR(255) DEFAULT NULL
	,`price` INT(11) DEFAULT NULL
	,`type` tinyint(4) DEFAULT '1' COMMENT '1 为直播间普通；4 为猫耳男友；7 为剧集打赏；8 为直播间白给礼物'
) --ENGINE = INNODB DEFAULT CHARSET = utf8 ROW_FORMAT = DYNAMIC COMMENT='礼物信息表'
;

INSERT INTO `gift`
  (`id`, `name`, `price`, `type`)
VALUES
  (23, 'test_gift', 144, 8)
  ,(301, 'test', 144, 8)
  ,(40023, '【半价版】牛牛', 144, 8)
  ,(40024, '【半价版】妞妞', 144, 8)
;

CREATE TABLE IF NOT EXISTS `report_live_daily_report` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  , `gmt_create` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
  , `gmt_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间'
  , `bizdate` date NOT NULL COMMENT '业务日期'
  , `creator_id` bigint(20) NOT NULL COMMENT '主播用户 ID'
  , `listener_user_num` bigint(20) NOT NULL COMMENT '收听用户数'
  , `listener_duration_total` double(10, 2) NOT NULL COMMENT '收听总时长（分钟）'
  , `listener_duration_avg` double(10, 2) NOT NULL COMMENT '人均收听时长（分钟）'
  , `listener_duration_more_than_five_minutes_num` bigint(20) NOT NULL COMMENT '超过 5 分钟收听时长用户数'
  , `listener_duration_more_than_five_minutes_ratio` double(10, 2) NOT NULL COMMENT '超过 5 分钟收听时长人数占总收听用户数比例'
  , `follow` bigint(20) NOT NULL DEFAULT 0 COMMENT '新增关注人数'
  , `unfollow` bigint(20) NOT NULL DEFAULT 0 COMMENT '取消关注人数'
  , `follower_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '每天 0 点 36 分快照的粉丝总数'
  , `live_duration` bigint NOT NULL DEFAULT '0' COMMENT '开播时长（毫秒）'
  , `daily_income` bigint NOT NULL DEFAULT '0' COMMENT '当日总收益（分）'
  , `first_live_time` bigint NOT NULL DEFAULT '0' COMMENT '首播时间（秒）'
  , `continuous_live_duration_over_2hrs` bigint NOT NULL DEFAULT '0' COMMENT '当日连续开播时长大于 2 小时的直播时长汇总（毫秒）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='主播每日报表';

CREATE UNIQUE INDEX uk_creatorid_bizdate ON report_live_daily_report (`creator_id`, `bizdate`);

INSERT INTO `report_live_daily_report`
  (`id`, `bizdate`, `creator_id`, `listener_user_num`, `listener_duration_total`, `listener_duration_avg`, `listener_duration_more_than_five_minutes_num`, `listener_duration_more_than_five_minutes_ratio`, `follow`, `unfollow`
  , `follower_count`, `live_duration`, `daily_income`, `first_live_time`, `continuous_live_duration_over_2hrs`)
VALUES
  (1, '2022-05-03', 1, 1, '1.00', '1.00', 1, '1.00', 10, 20, 15, 1000, 1000, 1651507200, 7200000)
  , (2, '2022-05-03', 12, 1, '1.00', '1.00', 1, '1.00', 15, 10, 15, 1000, 1000, 1651507200, 7200000)
  , (3, '2022-05-03', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 1000, 1000, 0, 7200000)
  , (4, '2022-06-03', 1, 1, '1.00', '1.00', 1, '1.00', 10, 20, 10, 1000, 1000, 1651507200, 7200000)
  , (5, '2021-06-01', 2333666, 1, '1.00', '1.00', 1, '1.00', 10, 20, 0, 1000, 1000, 0, 7200000)
  , (6, '2021-06-02', 2333666, 1, '1.00', '1.00', 1, '1.00', 10, 20, 10, 1000, 1000, 1622563200, 7200000)
  , (7, '2021-06-03', 2333666, 1, '1.00', '1.00', 1, '1.00', 10, 20, 20, 1000, 1000, 1622563200, 7200000)
  , (8, '2021-06-04', 2333666, 1, '1.00', '1.00', 1, '1.00', 10, 20, 30, 1000, 1000, 1622563200, 7200000)
  , (9, '2021-06-05', 2333666, 1, '1.00', '1.00', 1, '1.00', 10, 20, 40, 1000, 1000, 1622563200, 7200000)
  , (10, '2021-06-06', 2333666, 1, '1.00', '1.00', 1, '1.00', 10, 20, 50, 1000, 1000, 1622563200, 7200000)
  , (11, '2022-05-04', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (12, '2022-05-05', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (13, '2022-05-06', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (14, '2022-05-07', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (15, '2022-05-08', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (16, '2022-05-09', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (17, '2022-05-10', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (18, '2022-05-11', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (19, '2022-05-12', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (20, '2022-05-13', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (21, '2022-05-14', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (22, '2022-05-15', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (23, '2022-05-16', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 1000, 0, 7200000)
  , (24, '2022-05-17', 11, 1, '1.00', '1.00', 1, '1.00', 40, 10, 40, 7200000, 10000, 0, 7200000)
;

-- GRANT SELECT ON user_addendum TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `user_addendum` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '用户 ID'
  ,`sex` tinyint DEFAULT NULL COMMENT '性别（0 未知，1 为男，2 为女）'
  ,`birthday` date DEFAULT NULL
  ,`qq` varchar(32) DEFAULT NULL COMMENT 'QQ 昵称'
  ,`weibo` varchar(32) DEFAULT NULL COMMENT '微博昵称'
  ,`wechat` varchar(32) DEFAULT NULL COMMENT '微信昵称'
  ,`bilibili` varchar(32) DEFAULT NULL COMMENT 'Bilibili 昵称'
  ,`apple` varchar(32) DEFAULT NULL COMMENT 'Apple 昵称'
  ,`message_config` varchar(255) DEFAULT NULL COMMENT '消息设置（JSON 字符串，例: {"receive":1,"fold":0}）'
  ,`ip` varchar(50) DEFAULT NULL COMMENT '用户 IP'
  ,`ip_detail` text COMMENT '用户 IP 详情'
  ,`birthdate_mmdd` char(4) DEFAULT NULL COMMENT '用于检索的生日月日（例如 12 月 26 日生日为 1226）'
) DEFAULT CHARSET=utf8mb3 COMMENT='用户信息表';

INSERT INTO `user_addendum`
  (`id`, `birthday`, `birthdate_mmdd`)
VALUES
  (1919, '1987-01-26', '0126')
  ,(1818, '1987-08-06', '0806')
  ,(1717, NULL, '0806')
  ,(1616, '1987-01-26', '0126')
  ,(1515, '1987-01-26', '0126')
  ,(1414, '1988-01-01', '0101')
;

-- GRANT SELECT ON m_recommended_exposure_level TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `m_recommended_exposure_level` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间（单位：秒）'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间（单位：秒）'
  ,`exposure` bigint NOT NULL COMMENT '曝光量，e.g. 100,000'
  ,`level` varchar(10) NOT NULL COMMENT '曝光等级，e.g. S A B C'
  ,`status` tinyint NOT NULL COMMENT '状态，0：禁用，1：启用'
  ,`scene` tinyint NOT NULL COMMENT '场景，1：首页，2：直播页'
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='推荐位曝光等级'
;

INSERT INTO `m_recommended_exposure_level`
  (`id`, `create_time`, `modified_time`, `exposure`, `level`, `status`, `scene`)
VALUES
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100000, 'S', 1, 1)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100000, 'S', 1, 2)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 80000, 'A', 1, 1)
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 80000, 'A', 1, 2)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 60000, 'B', 1, 1)
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 60000, 'B', 1, 2)
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 40000, 'C', 1, 1)
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 40000, 'C', 1, 2)
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 40000, 'C', 0, 2)
;

-- GRANT INSERT, UPDATE, SELECT ON m_recommended_elements_daily_exposure TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `m_recommended_elements_daily_exposure` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`bizdate` date NOT NULL COMMENT '业务日期'
  ,`scene` tinyint NOT NULL COMMENT '场景，1：首页，2：直播页'
  ,`element_id` bigint NOT NULL COMMENT '干预卡 ID，sence=1 时为 m_recommended_elements id，sence=2 时为 live_recommended_elements id'
  ,`exposure_count` bigint NOT NULL COMMENT '当日实时曝光量'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐位干预卡每日（实时）曝光量'
;
