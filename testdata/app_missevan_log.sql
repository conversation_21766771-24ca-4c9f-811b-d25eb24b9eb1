-- GRANT INSERT ON live_bvclive_sdk_status TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_bvclive_sdk_status` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP'
  ,`stream_name` varchar(50) NOT NULL DEFAULT '' COMMENT '流名称'
  ,`timestamp` bigint NOT NULL DEFAULT 0 COMMENT '事件时间 ms'
  ,`sid` varchar(50) NOT NULL DEFAULT '' COMMENT 'sid'
  ,`version` varchar(50) NOT NULL DEFAULT '' COMMENT 'version'
  ,`type` int NOT NULL DEFAULT 0 COMMENT '上报指标类型 1: 直播推流指标 2: 连麦指标 3: 播放指标'
  ,`live_event` int NOT NULL DEFAULT 0 COMMENT '事件类型 1: 推流成功 2: 推流开始时失败 3: 已经在推流，过程中发生错误，推流中断'
  ,`rtc_event` int NOT NULL DEFAULT 0 COMMENT '事件类型 1: 加入连麦 2: 加入连麦成功 3: 收到首帧数据 4: 退出连麦成功 5: 更新连麦总时长'
  ,`rtc_receive_first_frame_duration` double NOT NULL DEFAULT 0 COMMENT '加入连麦至收到首桢数据的耗时，收到首帧数据时上报 ms'
  ,`rtc_join_duration` double NOT NULL DEFAULT 0 COMMENT '加入连麦至连麦成功的耗时，加入连麦成功事件上报 ms'
  ,`rtc_duration` double NOT NULL DEFAULT 0 COMMENT '本次连麦总时长，退出连麦时上报，连麦过程中也会定时上报 ms'
  ,`rtc_total_samples_duration` double NOT NULL DEFAULT 0 COMMENT 'WebRTC 接收 sample 总时长 ms'
  ,`rtc_silent_concealed_duration` double NOT NULL DEFAULT 0 COMMENT 'WebRTC 插入静音的时长 ms'
  ,`total_samples_duration` double NOT NULL DEFAULT 0 COMMENT '播放时长 使用 SDK 内部播放时统计 ms'
  ,`total_silent_samples_duration` double NOT NULL DEFAULT 0 COMMENT '播放主动插入静音的时长 使用 SDK 内部播放器统计 ms'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='bvclive sdk status 上报记录';
