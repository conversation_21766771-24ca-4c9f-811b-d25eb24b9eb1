CREATE TABLE IF NOT EXISTS `guild_agent` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL COMMENT '修改时间'
    ,`delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间'
    ,`guild_id` bigint NOT NULL COMMENT '公会 ID'
    ,`agent_id` bigint NOT NULL COMMENT '经纪人用户 ID'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
;

INSERT INTO guild_agent (id, create_time, modified_time, delete_time, guild_id, agent_id)
-- TestUserGuildRole
VALUES (23333471, 1600743764, 1600743764, 0, 23333471, 23333471)
-- TestFindManagerRole
,(20210707, 1600743764, 1600743764, 0, 20210707, 20210707)
,(23333472, 1600743764, 1600743764, 0, 3, 4)
;

CREATE TABLE IF NOT EXISTS `guild_agent_creator` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL COMMENT '修改时间'
    ,`delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间'
    ,`guild_id` bigint NOT NULL COMMENT '公会 ID'
    ,`agent_id` bigint NOT NULL COMMENT '经纪人用户 ID'
    ,`creator_id` bigint NOT NULL COMMENT '主播 ID'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
;

CREATE TABLE IF NOT EXISTS `live_tag_group` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigin NOT NULL DEFAULT 0 COMMENT '录入时间'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
    ,`name` varchar(20) NOT NULL DEFAULT '' COMMENT '标签组名称'
    ,`sort_order` int NOT NULL DEFAULT 0 COMMENT '倒序排序'
    ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '状态'
)
;

INSERT INTO live_tag_group
    (id, name, sort_order, status, create_time, modified_time)
VALUES
    (1, 'group1', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    ,(2, 'group2', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
    ,(3, 'group3', 3, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

CREATE TABLE IF NOT EXISTS `live_tag`
(
    `id`              bigint       NOT NULL PRIMARY KEY COMMENT '主键'
    ,`create_time`    bigint       NOT NULL DEFAULT '0' COMMENT '录入时间'
    ,`modified_time`  bigint       NOT NULL DEFAULT '0'
    ,`tag_name`       varchar(20)  NOT NULL COMMENT '标签名称'
    ,`tag_group_id`   bigint       NOT NULL DEFAULT 0 COMMENT '标签组 ID'
    ,`type`           tinyint      NOT NULL DEFAULT 0 COMMENT '标签类型'
    ,`icon`           varchar(100) NOT NULL COMMENT 'app 图标 url'
    ,`dark_icon`      varchar(100) NOT NULL COMMENT '夜间模式 app 图标'
    ,`web_icon`       varchar(100) NOT NULL COMMENT 'web 图标'
    ,`sort_order`     int unsigned NOT NULL DEFAULT '0' COMMENT '倒序排序'
    ,`status`         int          NOT NULL DEFAULT 0 COMMENT '状态'
)
;

INSERT INTO live_tag
    (id, tag_name, type, icon, dark_icon, web_icon, sort_order, status)
VALUES
    (1, 'test1', 0, 'oss://live/tags/icon/001.png', 'oss://live/tags/icon/001-dark.png', 'oss://live/tags/icon/001-web.png', 0, 1)
    ,(2, 'test2', 0, 'oss://live/tags/icon/002.png', 'oss://live/tags/icon/002-dark.png', 'oss://live/tags/icon/002-web.png', 1, 1)
    ,(3, 'test3', 0, 'oss://live/tags/icon/003.png', 'oss://live/tags/icon/003-dark.png', 'oss://live/tags/icon/003-web.png', 2, 0)
    ,(24, '听剧', 0, 'oss://live/tags/icon/024.png', 'oss://live/tags/icon/024-dark.png', 'oss://live/tags/icon/024-web.png', 3, 1)
;

INSERT INTO live_tag
    (id, tag_name, tag_group_id, type, icon, dark_icon, web_icon, sort_order, status)
VALUES
    (10001, 'test10001', 1, 1, '', '', '', 3, 1)
    ,(10002, 'test10002', 1, 1, '', '', '', 1, 1)
    ,(10003, 'test10003', 1, 1, '', '', '', 2, 1)
    ,(20001, 'test20001', 2, 1, '', '', '', 2, 1)
    ,(20002, 'test20002', 2, 1, '', '', '', 1, 1)
    ,(20003, 'test20003', 2, 1, '', '', '', 3, 0)
;

CREATE TABLE IF NOT EXISTS `live_tag_control_list`(
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
    ,`tag_id` bigint NOT NULL DEFAULT 0 COMMENT '标签 ID'
    ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '房间 ID'
    ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '状态，1：允许主播自行添加 tag 的白名单'
    ,KEY `idx_modifiedtime` (`modified_time`)
    ,UNIQUE KEY `uk_tagid_roomid_status` (`tag_id`, `room_id`, `status`)
)
;

INSERT INTO `live_tag_control_list`
    (`id`, `create_time`, `modified_time`, `tag_id`, `room_id`, `status`)
VALUES
    (1, 1, 1, 24, 186192636, 1) -- room_id 186192636 user_id 186192636
    ,(2, 1, 1, 24, 22489473, 1) -- room_id 22489473 user_id 10
;

CREATE TABLE IF NOT EXISTS `live_room_tag_record` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL COMMENT '修改时间'
    ,`tag_id` bigint NOT NULL DEFAULT 0 COMMENT '标签 ID'
    ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '房间 ID'
    ,`operator_id` bigint NOT NULL DEFAULT 0 COMMENT '修改直播间标签的用户 ID'
    ,`role` tinyint NOT NULL DEFAULT 0 COMMENT '角色，1：主播 2：超管'
    ,`operation` tinyint NOT NULL DEFAULT 0 COMMENT '操作，1：加入 2：移除'
    ,KEY `idx_modifiedtime` (`modified_time`)
    ,KEY `idx_tagid_roomid_createtime` (`tag_id`, `room_id`, `create_time`)
)
;

INSERT INTO live_room_tag_record
    (`id`, `create_time`, `modified_time`, `tag_id`, `room_id`, `operator_id`, `role`, `operation`)
VALUES
    (1, 1, 1, 24, 223344, 223344, 1, 1)
    ,(2, 2, 1, 24, 223344, 223344, 1, 1)
;

-- GRANT SELECT, INSERT, UPDATE ON live_txn_order TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_txn_order` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL COMMENT '修改时间'
    ,`expire_time` bigint NOT NULL DEFAULT '0' COMMENT '订单到期时间'
    ,`title` varchar(255) NOT NULL COMMENT '订单标题'
    ,`price` bigint NOT NULL COMMENT '订单价格（单位：钻石）'
    ,`status` tinyint NOT NULL COMMENT '订单状态（同 app_missevan.transaction_log.status）'
    ,`tid` bigint NOT NULL DEFAULT '0' COMMENT '对应 app_missevan.transaction_log.id'
    ,`goods_id` bigint NOT NULL COMMENT '商品 ID'
    ,`goods_type` tinyint unsigned NOT NULL COMMENT '商品类型，1：超粉'
    ,`attr` tinyint NOT NULL DEFAULT '0' COMMENT '属性'
    ,`buyer_id` bigint NOT NULL COMMENT '购买者用户 ID'
    ,`seller_id` bigint NOT NULL DEFAULT '0' COMMENT '出售者用户 ID'
    ,`more` text NOT NULL DEFAULT '' COMMENT '额外信息'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播订单表'
;

INSERT INTO `live_txn_order`
(`id`, `create_time`, `modified_time`, `expire_time`, `title`, `price`, `status`, `tid`, `goods_id`, `goods_type`,`attr`, `buyer_id`, `seller_id`, `more`)
VALUES (1, 2, 1, 1, 'bless', 100, 1, 111, 1, 1, 1, 3457111, 10, '{"bubble":{"bubble_id":1,"type":"test"}}') -- TestRoomSuperFanHistory
    ,(2, 3, 1, 1, 'bless', 100, 1, 111, 1, 1, 1, 3457111, 10, '') -- TestRoomSuperFanHistory
    ,(3, 3, 1, 1, 'bless', 100, 1, 111, 1, 1, 1, 12, 10, '') -- TestSuperFanBuyParamCheck
     -- ActionActivityFixSuperFan
    ,(4, 9, 1, 1, 'bless', 100, 1, 111, 1, 1, 1, 12, 10, '')
    ,(5, 9, 1, 1, 'bless', 100, 1, 111, 1, 1, 1, 12, 10, '')
    ,(6, 9, 1, 1, 'bless', 100, 1, 111, 1, 3, 1, 12, 10, '{"num": 1, "gifts":[{"gift_id":1,"num":3}]}')
    ,(7, 9, 1, 1, 'bless', 100, 1, 111, 1, 3, 1, 12, 10, '{"num": 1, "gifts":[{"gift_id":1,"num":3}]}')
    -- TestActionGashaponPrizeList
    ,(8, 9, 1, 1, 'bless', 100, 1, 111, 12, 3, 1, 12, 10, '{"num": 1, "gifts":[{"gift_id":10070,"num":3},{"gift_id":3,"num":4},{"gift_id":5,"num":3}]}')
    -- subTestMedalRemove
    ,(9, 1, 1, 9999999999, 'bless', 1, 1, 111, 1, 1, 1, 12, 123456, '')
    -- TestSetSuperFanOrderExpired
    ,(10, 1, 1, 9999999999, 'bless', 1, 1, 111, 1, 1, 1, 333, 333, '')
    ,(11, 1, 1, 1, 'bless', 1, 1, 111, 1, 1, 1, 333, 333, '')
    -- TestGetDataByLastID
    ,(12, 1, 1, 9999999999, 'bless', 1, 1, 1, 1, 4, 1, 333, 333, '')
    ,(13, 1, 1, 1, 'bless', 1, 1, 2, 1, 4, 2, 333, 333, '')
    ,(14, 1, 1, 9999999999, 'bless', 1, 1, 1, 1, 4, 1, 333, 333, '')
    ,(15, 1, 1, 1, 'bless', 1, 1, 2, 1, 4, 2, 333, 333, '')
    -- TestFindLastSuperFanRegister
    ,(16, 1692780601, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 2, 9074509, 9074510, '')
    ,(17, 1692780601, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 1, 9074509, 9074510, '')
    ,(18, 1692780600, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 1, 9074509, 9074510, '')
    ,(19, 1692780601, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 2, 9074509, 9074511, '')
    ,(20, 1692780601, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 1, 9074509, 9074511, '')
    ,(21, 1692780600, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 1, 9074509, 9074511, '')
    -- TestMedalList
    ,(22, 1692780601, 1692780600, 1999999998, 'super', 1, 1, 2, 1, 1, 1, 2021041417, 9074511, '')
    -- TestRoomRevenueHistory
    ,(23, 1, 1, 0, '付费弹幕', 100, 1, 2, 1, 6, 1, 2024032117, 12, '')
;

CREATE TABLE IF NOT EXISTS `live_goods` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
    ,`modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
    ,`type` int(10) NOT NULL COMMENT '类型, 1: 超粉'
    ,`num` int(10) unsigned NOT NULL COMMENT '数量（月）'
    ,`price` int(10) NOT NULL COMMENT '金额（钻石）'
    ,`title` varchar(255)  NOT NULL COMMENT '标题'
    ,`description` varchar(255)  NOT NULL COMMENT '介绍'
    ,`icon` varchar(255)  NOT NULL COMMENT '图标'
    ,`notify_duration` int(10) NOT NULL COMMENT '通知显示时长（毫秒）'
    ,`sort` int(10) NOT NULL COMMENT '排序'
    ,`more` text NULL COMMENT '额外的商品内容'
    ,`sale_start_time` bigint NOT NULL DEFAULT 0 COMMENT '售卖开始时间'
    ,`sale_end_time` bigint NOT NULL DEFAULT 0 COMMENT '售卖结束时间'
    ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '上线时间'
    ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '下线时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间商品价目表'
;

INSERT INTO `live_goods`
(`create_time`, `modified_time`, `type`, `num`, `price`, `title`, `description`, `icon`, `notify_duration`, `sort`, `more`, `sale_start_time`, `sale_end_time`, `start_time`, `end_time`)
VALUES (strftime('%s','now'), strftime('%s','now'), 1, 1, 1000, '1 个月', '折合 100 钻', 'oss://test.png', 7000, 1, '', 0, 0, 0, 0)
    ,(strftime('%s','now'), strftime('%s','now'), 1, 3, 3000, '3 个月', '折合 99 钻', 'oss://test.png', 7000, 3, '', 0, 0, 0, 0)
     -- 超粉嘉年华活动
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 99999, 'SS 级福袋', '', 'oss://test.png', 0, 1, '{"limits": [{"requirement": 1}], "gifts": [{"id": 401, "num": 1, "expire_time": 1654012799}, {"id": 402, "num": 1, "expire_time": 1654012799}, {"id": 40003, "num": 3, "expire_time": 1654012799}, {"id": 40004, "num": 2, "expire_time": 1654012799}, {"id": 40005, "num": 2, "expire_time": 1654012799}, {"id": 40006, "num": 1, "expire_time": 1654012799}, {"id": 30006, "num": 5200, "expire_time": 1622131199}, {"id": 301, "num": 5200, "duration": 10000}], "appearances":[{"id": 20004, "duration": 604800}, {"id": 10004, "duration": 604800}, {"id": 40007, "duration": 604800}]}', strftime('%s','now', '-1 minutes'), strftime('%s','now', '+1 minutes'), strftime('%s','now', '-30 minutes'), strftime('%s','now', '+30 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 5200, 'S 级福袋', '', 'oss://test.png', 0, 2, '{"limits": [{"requirement": 1}], "gifts": [{"id": 40007, "num": 1, "expire_time": 1654012799}, {"id": 40008, "num": 1, "expire_time": 1654012799}, {"id": 40009, "num": 2, "expire_time": 1654012799}, {"id": 30006, "num": 230, "expire_time": 1622131199}], "appearances":[{"id": 20004, "duration": 604800}, {"id": 40007, "duration": 604800}]}', 0, 0, strftime('%s','now', '-30 minutes'), strftime('%s','now', '-10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, 'A 级福袋', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 1, "requirement": 0, "type": 1}], "gifts": [{"id": 40010, "num": 1, "expire_time": 1654012799}, {"id": 40011, "num": 1, "expire_time": 1654012799}, {"id": 40012, "num": 2, "expire_time": 1654012799}, {"id": 40013, "num": 10, "expire_time": 1654012799}, {"id": 30006, "num": 5200, "expire_time": 1622131199}], "appearances":[{"id": 40007, "duration": 604800}]}', strftime('%s','now', '-30 minutes'), strftime('%s','now', '+1 minutes'), strftime('%s','now', '-10 minutes'), strftime('%s','now', '+30 minutes'))
    -- TestActionBuyFukubukuro
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '贵族福袋', '', 'oss://test.png', 0, 2, '{"limits": [{"requirement": 2, "noble_type": 2, "noble_level": 1}], "gifts": [{"id": 40010, "num": 1, "expire_time": 1654012799}, {"id": 40011, "num": 1, "expire_time": 1654012799}, {"id": 40012, "num": 2, "expire_time": 1654012799}, {"id": 40013, "num": 10, "expire_time": 1654012799}, {"id": 30006, "num": 5200, "expire_time": 1622131199}], "appearances":[{"id": 40007, "duration": 604800}]}', strftime('%s','now', '-30 minutes'), strftime('%s','now', '+1 minutes'), strftime('%s','now', '-10 minutes'), strftime('%s','now', '+30 minutes'))
    -- TestActionListFukubukuro
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '测试未开始售卖', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 0, "requirement": 0, "type": 1}]}', strftime('%s','now', '+1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-30 minutes'), strftime('%s','now', '+30 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '测试售卖结束', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 0, "requirement": 0, "type": 1}]}', strftime('%s','now', '-10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '-10 minutes'), strftime('%s','now', '+30 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '测试限购次数', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 1, "requirement": 0, "type": 1}]}', strftime('%s','now', '-10 minutes'), strftime('%s','now', '+24 hours', '+10 minutes'), strftime('%s','now', '-30 minutes'), strftime('%s','now', '+24 hours', '+30 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '测试购买资格', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 0, "requirement": 1, "type": 1}]}', strftime('%s','now', '-10 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-30 minutes'), strftime('%s','now', '+30 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '测试全站限购次数', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 2, "requirement": 0, "type": 2}]}', strftime('%s','now', '-10 minutes'), strftime('%s','now', '+24 hours', '+10 minutes'), strftime('%s','now', '-30 minutes'), strftime('%s','now', '+24 hours', '+30 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 2, 1, 3000, '测试商品限购次数', '', 'oss://test.png', 0, 2, '{"limits": [{"num": 2, "requirement": 0, "type": 3}]}', strftime('%s','now', '-10 minutes'), strftime('%s','now', '+24 hours', '+10 minutes'), strftime('%s','now', '-30 minutes'), strftime('%s','now', '+24 hours', '+30 minutes'))
    -- gashapon
    ,(strftime('%s','now'), strftime('%s','now'), 3, 1, 10, '单抽 10 钻', '出奇迹', 'xxx', 0, 1, '{"pool_id":3, "gashapon_name": "超能魔方"} ', strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 3, 10, 100, '10 连 100 钻', '必出精品', 'xxx', 0, 1, '{"pool_id":3, "gashapon_name": "超能魔方"} ',  strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 3, 100, 1000, '100 连 1000 钻', '必出极品', 'xxx', 0, 1, '{"pool_id":3, "gashapon_name": "超能魔方"} ',  strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    -- wish
    ,(strftime('%s','now'), strftime('%s','now'), 4, 1, 500, '秘境感应', '许愿池初级商品', 'xxx', 0, 1, '{"limits": [{"num": 30, "requirement": 0}], "gifts": [{"id": 40052, "num": 1, "duration": 21600000, "rate": 22500}], "point_rate": 977500}', strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 4, 1, 10, '梦墟感应', '许愿池高级商品', 'xxx', 0, 2, '{"limits": [{"num": 3000, "requirement": 0}], "gifts": [{"id": 40050, "num": 1, "duration": 21600000, "rate": 170}, {"id": 40051, "num": 1, "duration": 21600000, "rate": 250000}], "point_rate": 749830}', strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    -- redpacket
    ,(strftime('%s','now'), strftime('%s','now'), 5, 1, 1000, '礼物红包 1', '礼物红包备注 1', 'oss://test/icon1.png', 0, 1, '{"red_packet": {"type": 1, "skin": "oss://test/skin1.zip", "appearance_id":1000, "attr":0, "wait_durations": [0, 60000, 12000], "gifts": [{"id": 40100, "num": 10, "most_valuable": true}, {"id": 40101, "num": 15}]}}', strftime('%s', 'now', '-1 minutes'), strftime('%s', 'now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 5, 1, 1500, '礼物红包 2', '礼物红包备注 2', 'oss://test/icon2.png', 0, 2, '{"red_packet": {"type": 2, "skin": "oss://test/skin2.zip", "appearance_id":1001, "attr":0, "keywords": ["默认 1", "默认 2"], "wait_durations": [0, 60000, 12000], "gifts": [{"id": 40102, "num": 15, "most_valuable": true}, {"id": 40103, "num": 20}]}}', strftime('%s', 'now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 5, 1, 1500, '礼物红包 3', '礼物红包备注 3', 'oss://test/icon2.png', 0, 2, '{"red_packet": {"type": 2, "skin": "oss://test/skin2.zip", "appearance_id":1002, "attr":0, "wait_durations": [0, 60000, 12000], "gifts": [{"id": 40343, "num": 15, "most_valuable": true}]}}', strftime('%s', 'now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 5, 1, 1500, '礼物红包 grab', '礼物红包备注', 'oss://test/icon2.png', 0, 2, '{"red_packet": {"type": 1, "skin": "oss://test/skin2.zip", "appearance_id":1003, "attr":0, "wait_durations": [0, 60000, 12000], "gifts": [{"id": 40102, "num": 15, "most_valuable": true}, {"id": 40103, "num": 20}]}}', strftime('%s', 'now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    ,(strftime('%s','now'), strftime('%s','now'), 5, 1, 1500, '礼物红包 error more', '礼物红包备注', 'oss://test/icon2.png', 0, 2, '{}', strftime('%s', 'now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    -- danmaku
    ,(strftime('%s','now'), strftime('%s','now'), 6, 1, 100, '弹幕', '弹幕', 'oss://test/icon1.png', 0, 2, '{"danmaku": {"tip": "100 钻石/条", "effects": [{"type": 1, "icon_url": "oss://test/effect.png"}]}}', strftime('%s', 'now', '-1 minutes'), strftime('%s','now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
    -- TestSendParam_sendRedPacket
    ,(strftime('%s','now'), strftime('%s','now'), 5, 1, 0, '免费红包', '礼物红包备注 1', 'oss://test/icon1.png', 0, 1, '{"red_packet": {"type": 1, "skin": "oss://test/skin1.zip", "appearance_id":100, "attr":2, "wait_durations": [0, 60000, 12000], "gifts": [{"id": 40100, "num": 10, "most_valuable": true}, {"id": 40101, "num": 15}]}}', strftime('%s', 'now', '-1 minutes'), strftime('%s', 'now', '+10 minutes'), strftime('%s','now', '-1 minutes'), strftime('%s','now', '+10 minutes'))
;

CREATE TABLE IF NOT EXISTS `live_room_box_log` (
	`id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
	,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
	,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
	,`room_id` int NOT NULL DEFAULT '0' COMMENT '直播间 ID'
	,`box_type` tinyint NOT NULL DEFAULT '0' COMMENT '宝箱类型 1：初级，2：中级，3：高级，4：传说'
	,`unlock_time` bigint NOT NULL DEFAULT '0' COMMENT '宝箱开启时间'
	,`event_id` int NOT NULL DEFAULT '0' COMMENT '活动 ID'
) -- ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '直播间开启宝箱日志表';
;

CREATE TABLE IF NOT EXISTS `live_user_box_log` (
	`id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
	,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
	,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
	,`room_id` int NOT NULL DEFAULT '0' COMMENT '直播间 ID'
	,`user_id` int NOT NULL DEFAULT '0' COMMENT '用户 ID'
	,`box_type` tinyint NOT NULL DEFAULT '0' COMMENT '宝箱类型 1：初级，2：中级，3：高级，4：传说'
	,`receive_time` bigint NOT NULL DEFAULT '0' COMMENT '领取时间'
	,`prize_name` varchar(100) NOT NULL DEFAULT '' COMMENT '奖品名称'
	,`round_count` tinyint NOT NULL DEFAULT '0' COMMENT '当前是在该轮宝箱的第几次领取'
	,`event_id` int NOT NULL DEFAULT '0' COMMENT '活动 ID'
	,`prize_id` int NOT NULL DEFAULT '0' COMMENT '宝箱奖品的 ID'
) -- ENGINE = InnoDB CHARSET = utf8mb4 COMMENT '用户领取宝箱日志表';
;

CREATE TABLE IF NOT EXISTS `live_schedule_record`(
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint(20) NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL COMMENT '修改时间'
  ,`guild_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '公会 ID'
  ,`creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主播 ID'
  ,`room_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '直播间 ID'
  ,`recommend_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '推荐 ID'
  ,`attr` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '默认公会申请推荐，位 1: 艺人主播、位 2: 优质主播、位 3: 神话推荐'
  ,`day` bigint(20) unsigned NOT NULL COMMENT '推荐日期的 0 点时间戳'
  ,`start_time` bigint(20) NOT NULL COMMENT '开始时间'
  ,`expire_time` bigint(20) NOT NULL COMMENT '结束时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播资源位首页排期历史记录'
;

CREATE TABLE IF NOT EXISTS `live_guild_schedule_apply` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint(20)  NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint(20)  NOT NULL COMMENT '修改时间'
    ,`delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间'
    ,`operator_id` int NOT NULL DEFAULT '0' COMMENT '操作人 ID'
    ,`guild_id` int NOT NULL DEFAULT '0' COMMENT '公会 ID'
    ,`room_id` int NOT NULL DEFAULT '0' COMMENT '直播间 ID'
    ,`creator_id` int NOT NULL DEFAULT '0' COMMENT '主播 ID'
    ,`status` int(10) NOT NULL COMMENT '类型, -1: 暂停推荐; 0: 审核中; 1: 已过审; 2: 未过审'
    ,`cover` varchar(255)  NOT NULL DEFAULT ''  COMMENT '推荐封面图'
    ,`reason` varchar(255)  NOT NULL DEFAULT '' COMMENT '拒绝原因'
    ,`start_time` int(10) NOT NULL COMMENT '排期申请开始时间, 如: 0, 1800, ... , 84600'
    ,`duration` int(10) NOT NULL COMMENT '持续时间，单位秒'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='首页推荐排期资源位申请表'
;

INSERT INTO `live_guild_schedule_apply`
(`id`, `create_time`, `modified_time`, `delete_time`, `operator_id`, `guild_id`, `room_id`, `creator_id`, `status`, `cover`, `start_time`, `duration`)
VALUES
    -- TestFindApply, TestScheduleApplyEditParamCheck, TestActionRecommendScheduleApply, TestFindApplyList
    (1, strftime('%s','now'), strftime('%s','now'), 0, 1, 3, 12345, 12345, 0, 'oss://test.png', 0, 1800)
    ,(2, strftime('%s','now'), strftime('%s','now'), 1, 1, 3, 54321, 54321, 0, 'oss://test.png', 0, 1800)
    -- TestScheduleApplyResetParamReset, TestFindApplyList
    ,(3, strftime('%s','now'), strftime('%s','now'), 0, 1, 3, 1, 10, 1, 'oss://test.png', 0, 1800)
;

CREATE TABLE IF NOT EXISTS `report_live_fans_log` (
	`id` bigint AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
	`gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
	`user_id` bigint NOT NULL COMMENT '用户 ID',
	`bizdate` date NOT NULL COMMENT '日期 YYYY-mm-dd',
	`follow` bigint NOT NULL DEFAULT 0 COMMENT '新增关注人数',
	`unfollow` bigint NOT NULL DEFAULT 0 COMMENT '取消关注人数',
    UNIQUE(`user_id`, `bizdate`)
) COMMENT '主播每日发生变化的粉丝数据（通过 DMS 任务编排导入，任务名：主播后台报表数据（关注日志，断播主播））'
;

CREATE TABLE IF NOT EXISTS `report_inactive_anchor` (
	`id` bigint AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
	`gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`gmt_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
	`user_id` bigint NOT NULL DEFAULT 0 COMMENT '主播 M 号',
	`guild_id` bigint NOT NULL COMMENT '公会 ID',
	`guild_live_days` int NOT NULL DEFAULT 0 COMMENT '公会主播开播天数（只计算 guild_id 为主播当前所属公会的直播记录，按开播时间计算开播天数）',
	`last_live_date` date NOT NULL COMMENT '主播最近开播日期（包含素人主播时期，按开播时间计算最近开播日期）',
	`guild_live_income` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '公会主播的直播收益（除去贵族续费，购买福袋以外，扣除渠道费后的总流水）元',
    UNIQUE(`user_id`)
) COMMENT '断播主播日志（通过 DLA 任务生成，任务名：主播后台报表数据（关注日志，断播主播），最近 60 天内直播过且最近 10 天没有直播的主播列表，表内只存当日数据，历史数据在数仓中保存）'
;

CREATE TABLE IF NOT EXISTS `guild_recommend_blocklist` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL COMMENT '修改时间'
    ,`element_type` bigint NOT NULL COMMENT '黑名单类型, 1: 推荐位直播间黑名单（element_id 为直播间 ID）; 2: 推荐位公会黑名单（element_id 为公会 ID）;'
    ,`element_id` bigint NOT NULL DEFAULT '0' COMMENT '黑名单元素 ID'
) COMMENT '推荐资源位黑名单表'
;

CREATE TABLE IF NOT EXISTS `guild_recommend_square_hot` (
	`id` bigint(20) AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
	,`create_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建时间'
	,`modified_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新时间'
	,`position` int NOT NULL COMMENT '推荐位置'
	,`creator_id` int NOT NULL COMMENT '主播 ID'
	,`guild_id` int NOT NULL COMMENT '公会 ID'
	,`operator_id` int NOT NULL COMMENT '推荐人的用户 ID'
	,`start_time` bigint(20) NOT NULL COMMENT '推荐开始时间'
	,`end_time` bigint(20) NOT NULL COMMENT '推荐结束时间'
) COMMENT '公会直播广场热门推荐表'
;

CREATE TABLE IF NOT EXISTS `guild_recommend_vacancy` (
  `id` bigint(20) AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0'
  ,`position` int NOT NULL COMMENT '推荐位置'
  ,`guild_id` int NOT NULL COMMENT '公会 ID'
  ,`initial_vacancy` int NOT NULL COMMENT '根据流水生成的本期次数'
  ,`edited_vacancy` int NOT NULL COMMENT '运营调整后的本期次数'
  ,`vacancy` int NOT NULL COMMENT '剩余推荐次数'
  ,`start_time` bigint(20) NOT NULL COMMENT '推荐次数的生效开始时间'
  ,`end_time` bigint(20) NOT NULL COMMENT '推荐次数的生效结束时间'
) COMMENT '公会热门位推荐次数表'
;

CREATE TABLE IF NOT EXISTS `guild_edit_history` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间',
  `operator_id` bigint NOT NULL COMMENT '操作人 ID',
  `guild_id` bigint NOT NULL COMMENT '公会 ID',
  `name` varchar(255) NOT NULL COMMENT '公会名称',
  `intro` varchar(255) NOT NULL DEFAULT '' COMMENT '公会简介',
  `owner_name` varchar(255) NOT NULL COMMENT '法人代表姓名',
  `owner_id_number` varchar(255) NOT NULL COMMENT '法人代表身份证号',
  `owner_id_people` varchar(255) NOT NULL COMMENT '法人代表手持身份证正面照',
  `owner_backcover` varchar(255) NOT NULL COMMENT '法人代表身份证背面',
  `mobile` varchar(255) NOT NULL COMMENT '法人代表手机号',
  `email` varchar(255) NOT NULL COMMENT '邮箱',
  `qq` varchar(255) NOT NULL DEFAULT '' COMMENT 'QQ 号',
  `corporation_name` varchar(255) NOT NULL COMMENT '公司名称',
  `corporation_address` varchar(255) NOT NULL COMMENT '公司地址',
  `corporation_phone` varchar(255) NOT NULL COMMENT '公司电话',
  `business_license_number` varchar(255) NOT NULL COMMENT '营业执照号',
  `business_license_frontcover` varchar(255) NOT NULL COMMENT '营业执照扫描件',
  `tax_account` varchar(255) NOT NULL COMMENT '纳税人识别号',
  `bank_account` varchar(255) NOT NULL COMMENT '银行卡号',
  `bank_account_name` varchar(255) NOT NULL COMMENT '银行开户名',
  `bank` varchar(255) NOT NULL COMMENT '开户行',
  `bank_address` varchar(255) NOT NULL COMMENT '开户行所在地',
  `bank_branch` varchar(255) NOT NULL COMMENT '开户支行',
  `invoice_rate` tinyint NOT NULL DEFAULT -2 COMMENT '发票税率（-2 税率未知，-1 不开具发票）',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '公会信息类型 1: 加密',
  `checked` tinyint NOT NULL DEFAULT 0 COMMENT '公会状态（-1 审核驳回，0 审核中，1 审核通过，2 解散）',
  `user_id` bigint NOT NULL COMMENT '公会创建人用户 ID',
  `guild_create_time` bigint NOT NULL DEFAULT 0 COMMENT '公会创建时间',
  `apply_time` bigint NOT NULL COMMENT '申请时间'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT= COMMENT='公会更改历史表'
;

CREATE TABLE IF NOT EXISTS `guild_transfer_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`operator_id` bigint(20) NOT NULL COMMENT '操作人 ID'
  ,`creator_id` bigint(20) NOT NULL COMMENT '主播 ID'
  ,`from_guild_id` int unsigned NOT NULL COMMENT '转出公会 ID'
  ,`to_guild_id` int unsigned NOT NULL COMMENT '转入公会 ID'
  ,`room_id` bigint(20) NOT NULL COMMENT '房间 ID'
  ,`revenue_live_duration_type` tinyint(4) NOT NULL COMMENT '当月收益和时长转入（1 原公会，2 新公会）'
  ,`contract_type` tinyint(4) NOT NULL COMMENT '签约类型（1 续签，2 重签）'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='转会历史表'
;

CREATE TABLE `guild_recommend_banner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`room_id` bigint NOT NULL COMMENT '房间号'
  ,`guild_id` bigint NOT NULL COMMENT '公会 ID'
  ,`creator_id` bigint NOT NULL COMMENT '主播 ID'
  ,`operator_id` bigint NOT NULL COMMENT '创建者 ID'
  ,`title` varchar(50) NOT NULL COMMENT 'banner 标题'
  ,`image_url` varchar(255) NOT NULL COMMENT 'banner 图片地址'
  ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态，0：待审核，1：已过审，2：未过审'
  ,`reason` varchar(255) NOT NULL DEFAULT '' COMMENT '拒绝理由'
  ,`start_time` bigint NOT NULL COMMENT '申请时间'
  ,`end_time` bigint NOT NULL COMMENT '过期时间'
)
;

INSERT INTO `guild_recommend_banner`
  (create_time, modified_time, room_id, guild_id, creator_id, operator_id, title, image_url, status, reason, start_time, end_time)
VALUES
  (1626421360, 1626421360, 345, 111, 10101, 12, 'test1', 'oss://test.png', 0, '', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 345, 111, 10101, 12, 'test2', 'oss://test.png', 1, '', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 345, 111, 10102, 12, 'test3', 'oss://test.png', 0, '', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 18113499, 222, 12, 12, 'test4', 'oss://test.png', 0, '', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 18113499, 3, 12, 12, 'test5', 'oss://test.png', 0, '', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 223355, 222, 10103, 12, 'test6', 'oss://test.png', 0, '', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 345, 111, 10102, 12, 'test7', 'oss://test.png', 0, '', 1626428800, 1626422400)
  ,(1626421360, 1626421360, 345, 111, 10102, 12, 'test8', 'oss://test.png', 0, '', 1626528800, 1626422400)
  ,(1626421360, 1626421360, 18113499, 3, 12, 12, 'test9', 'oss://test.png', 1, '', 1626528800, 1626422400)
  ,(1626421360, 1626421360, 18113499, 23333471, 12, 12, 'test10', 'oss://test.png', 0, 'test4', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 223355, 222, 10103, 12, 'test11', 'oss://test.png', 0, 'test5', 1626418800, 1626422400)
  ,(1626421360, 1626421360, 345, 111, 10102, 12, 'test3', 'oss://test.png', 0, '', 1630857600, 1631462400)
;

CREATE TABLE `live_gashapon_buff_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`room_id` bigint NOT NULL COMMENT '房间号'
  ,`pool_id` bigint NOT NULL COMMENT '奖池 ID'
  ,`gift_id` bigint NOT NULL COMMENT '礼物 ID'
  ,`multiplier` float NOT NULL COMMENT '翻倍概率'
  ,INDEX `idx_pool_id_gift_id` (`pool_id`, `gift_id`)
)
;

CREATE TABLE IF NOT EXISTS `tripartite_exclusive_creator` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`guild_id` bigint NOT NULL DEFAULT 0 COMMENT '公会 ID'
  ,`creator_id` bigint NOT NULL DEFAULT 0 COMMENT '主播 ID'
  ,`guild_contract_id` int unsigned NOT NULL DEFAULT 0 COMMENT '主播合约 ID'
  ,`contract_end` bigint NOT NULL DEFAULT 0 COMMENT '合约结束时间'
  ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '主播状态，0：生效中；1：身份到期；2：超管移除'
) -- DEFAULT CHARACTER SET=utf8mb4 COMMENT='三方独家主播'
;

INSERT INTO `tripartite_exclusive_creator`
  (id, create_time, modified_time, guild_id, creator_id, guild_contract_id, contract_end, status)
VALUES
  (1, 1626421360, 1626421360, 3, 1, 1, 1999999999, 0)
  ,(2, 1626421361, 1626421361, 3, 2, 2, 1999999999, 0)
  ,(3, 1626421362, 1626421362, 3, 3, 3, 1626421361, 1)
  ,(4, 1626421363, 1626421363, 3, 4, 4, 1626421361, 2)
;

CREATE TABLE `archive_live_rank_month` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint(20) unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint(20) unsigned NOT NULL COMMENT '修改时间'
  ,`type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '榜单类型，1：主播榜；2：用户贡献榜'
  ,`month` varchar(10) NOT NULL DEFAULT '0' COMMENT '年月：e.g. 2022-08'
  ,`room_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '直播间 ID'
  ,`creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主播 ID'
  ,`user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`revenue` bigint(20) NOT NULL DEFAULT '0' COMMENT '主播收益'
  ,`rank` tinyint(2) NOT NULL DEFAULT '0' COMMENT '排名'
  ,INDEX `idx_month` (`month`)
)
;

CREATE TABLE `archive_live_rank_hour` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint(20) NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL COMMENT '修改时间'
  ,`type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '榜单类型，1：主播榜；2：用户贡献榜'
  ,`rank_time` varchar(20) NOT NULL DEFAULT '0' COMMENT '榜单时间：e.g. 2022-08-08 15:00:00'
  ,`room_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '直播间 ID'
  ,`creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主播 ID'
  ,`user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`score` bigint(20) NOT NULL DEFAULT '0' COMMENT 'type=1：人气值，type=2：用户贡献值'
  ,`rank` tinyint(2) NOT NULL DEFAULT '0' COMMENT '排名'
  ,INDEX `idx_ranktime` (`rank_time`)
)
;

CREATE TABLE IF NOT EXISTS `archive_live_rank_nova` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`create_time` bigint(20) NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL COMMENT '修改时间'
  ,`rank_time` varchar(20) NOT NULL DEFAULT '0' COMMENT '榜单时间：e.g. 2022-08-11'
  ,`creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '主播 ID'
  ,`room_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '直播间 ID'
  ,`score` bigint(20) NOT NULL DEFAULT '0' COMMENT '人气值'
  ,`rank` tinyint(2) NOT NULL DEFAULT '0' COMMENT '排名'
  ,INDEX `idx_ranktime` (`rank_time`)
)
;

CREATE TABLE IF NOT EXISTS `live_notice` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint(20) NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL COMMENT '修改时间'
  ,`delete_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '删除时间'
  ,`type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '公告类型，1：礼物墙公告'
  ,`content`  varchar(255) NOT NULL COMMENT '公告内容'
  ,`open_url`  varchar(255) NOT NULL COMMENT '跳转链接'
) -- ENGINE=InnoDB COMMENT='直播公告信息表'
;

INSERT INTO `live_notice`
  (`id`, `create_time`, `modified_time`, `delete_time`, `type`, `content`, `open_url`)
VALUES
  (1, 1626421360, 1626421360, 0, 1, "这是一条公告", "https://www.test.com")
;

CREATE TABLE IF NOT EXISTS `live_sticker` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`name` varchar(255) NOT NULL DEFAULT '' COMMENT '表情包名称'
  ,`intro` varchar(255) NOT NULL DEFAULT '' COMMENT '来源描述'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '表情列表显示图标（静态图）'
  ,`image` varchar(255) NOT NULL DEFAULT '' COMMENT '表情动态图资源地址'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT '表情表'
;

INSERT INTO `live_sticker`
  (`id`, `create_time`, `modified_time`, `name`, `intro`, `icon`, `image`)
VALUES
  (1, 1682265600, 1682265600, '超粉表情 1', '来源描述 1', 'oss://sticker_package/superfans/icon.png', 'oss://sticker_package/superfans/icon.webp')
  ,(2, 1682265600, 1682265600, '超粉表情 2', '来源描述 2', 'oss://sticker_package/superfans/icon.png', 'oss://sticker_package/superfans/icon.webp')
  ,(3, 1682265600, 1682265600, '专属表情 1', '来源描述 1', 'oss://sticker_package/room/icon.png', 'oss://sticker_package/superfans/icon.webp')
  ,(4, 1682265600, 1682265600, '专属表情 2', '来源描述 2', 'oss://sticker_package/room/icon.png', 'oss://sticker_package/superfans/icon.webp')
;

CREATE TABLE IF NOT EXISTS `live_sticker_package` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`type` int NOT NULL DEFAULT 0 COMMENT '表情类型，1：常规表情；2：用户专属表情；3：房间专属表情；4：超粉表情'
  ,`sort` int NOT NULL DEFAULT 0 COMMENT '表情包排序（升序）'
  ,`name` varchar(255) NOT NULL DEFAULT '' COMMENT '表情包名称'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '表情包显示图标'
  ,`expire_time` bigint NOT NULL DEFAULT 0 COMMENT '过期时间（0 表示不过期）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT '表情包表'
;

INSERT INTO `live_sticker_package`
  (`id`, `create_time`, `modified_time`, `type`, `sort`, `name`, `icon`, `expire_time`)
VALUES
  (1, 1682265600, 1682265600, 4, 1, '超粉表情包', 'oss://sticker_package/superfans/icon.png', 1686844800)
  ,(2, 1682265600, 1682265600, 3, 1, '房间 4381915 专属表情包', 'oss://sticker_package/superfans/icon.png', 0)
  ,(3, 1682265600, 1682265600, 2, 2, '用户 223344 专属表情包', 'oss://sticker_package/superfans/icon.png', 0)
;

CREATE TABLE IF NOT EXISTS `live_sticker_map` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`sort` int NOT NULL DEFAULT 0 COMMENT '表情排序（升序）'
  ,`package_id` bigint NOT NULL DEFAULT 0 COMMENT '表情包 ID'
  ,`sticker_id` bigint NOT NULL DEFAULT 0 COMMENT '表情 ID'
  ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间'
  ,`expire_time` bigint NOT NULL DEFAULT 0 COMMENT '过期时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT '表情表与表情包表关联表'
;

INSERT INTO `live_sticker_map`
  (`id`, `create_time`, `modified_time`, `sort`, `package_id`, `sticker_id`, `start_time`, `expire_time`)
VALUES
  (1, 1682265600, 1682265600, 1, 1, 1, 1682265600, 2000000000)
  ,(2, 1682265600, 1682265601, 2, 1, 2, 1682265600, 2000000000)
  ,(3, 1682265600, 1682265602, 0, 2, 3, 1682265600, 0)
  ,(4, 1682265600, 1682265603, 0, 2, 4, 1682265600, 0)
  ,(5, 1682265600, 1682265604, 0, 3, 3, 1682265600, 0)
  ,(6, 1682265600, 1682265603, 0, 2, 1, 1682265600, 0)
;

CREATE TABLE IF NOT EXISTS `live_sticker_package_owner` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`package_id` bigint NOT NULL DEFAULT 0 COMMENT '表情包 ID'
  ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '房间 ID'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
  ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间'
  ,`expire_time` bigint NOT NULL DEFAULT 0 COMMENT '过期时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT '表情包归属表'
;

INSERT INTO `live_sticker_package_owner`
  (`id`, `create_time`, `modified_time`, `package_id`, `room_id`, `user_id`, `start_time`, `expire_time`)
VALUES
  -- 超粉表情
  (1, 1682265600, 1682265600, 1, 223344, 0, 1682265600, 2000000000)
  -- 房间专属（room_id：4381915）
  ,(2, 1682265600, 1682265600, 2, 4381915, 0, 1682265600, 0)
  -- 用户专属（user_id：223344）
  ,(3, 1682265600, 1682265600, 3, 0, 223344, 1682265600, 0)
;

CREATE TABLE IF NOT EXISTS `live_redeem_goods` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`start_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '商品有效期的开始时间'
  ,`expire_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '商品有效期的结束时间'
  ,`point` int NOT NULL DEFAULT 0 COMMENT '兑换积分所需要的积分价格'
  ,`name` varchar(50) NOT NULL DEFAULT '' COMMENT '兑换商品的名称'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '商品图标'
  ,`type` tinyint NOT NULL DEFAULT 0 COMMENT '商品类型'
  ,`shop_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型，0：万事屋常驻兑换商城；1：星享馆（185 级）等级权益商城'
  ,`sort` int NOT NULL DEFAULT 1 COMMENT '商品排序（升序）'
  ,`reward_id` bigint NOT NULL DEFAULT 0 COMMENT '商品实际奖励的 ID'
  ,`more` json DEFAULT NULL COMMENT '额外的商品内容'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='常驻兑换商城商品';

CREATE TABLE IF NOT EXISTS `live_redeem_record` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '兑换用户的 ID'
  ,`goods_id` bigint NOT NULL DEFAULT 0 COMMENT '兑换商品的 ID'
  ,`name` varchar(50) NOT NULL DEFAULT '' COMMENT '兑换商品的名称'
  ,`shop_type` tinyint NOT NULL DEFAULT 0 COMMENT '商城类型，0：万事屋常驻兑换商城；1：星享馆（185 级）等级权益商城'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='常驻兑换商城的商品兑换记录';

CREATE TABLE IF NOT EXISTS `live_backpack_item` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`type` tinyint NOT NULL DEFAULT 0 COMMENT '道具类型，1: 贵族体验卡'
  ,`name` varchar(50) NOT NULL DEFAULT '' COMMENT '道具名称'
  ,`intro` varchar(255) NOT NULL DEFAULT '' COMMENT '道具描述'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '道具图标'
  ,`icon_active` varchar(255) NOT NULL DEFAULT '' COMMENT '激活图标'
  ,`intro_icon` varchar(255) NOT NULL DEFAULT '' COMMENT '道具描述图标'
  ,`label_icon` varchar(255) NOT NULL DEFAULT '' COMMENT '道具角标'
  ,`intro_open_url` varchar(255) NOT NULL DEFAULT '' COMMENT '道具跳转链接'
  ,`more` json DEFAULT NULL COMMENT '额外的内容'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='背包道具配置表';

INSERT INTO `live_backpack_item`
  (`id`, `create_time`, `modified_time`, `type`, `name`, `intro`, `icon`, `icon_active`, `intro_icon`, `label_icon`, `intro_open_url`, `more`)
VALUES
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '大咖体验卡 1 天', '贵族体验卡', 'oss://backpack_item/vip_card/icon.png', 'oss://backpack_item/vip_card/icon.webp', 'oss://backpack_item/vip_card/intro_icon.png', 'oss://backpack_item/vip_card/label_icon.png', 'https://www.test.com', '{"vip_id":12,"duration":86400,"icon":"oss://backpack_item/vip_card/icon.png"}'),
  (2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, '粉丝勋章兑换卡', '', 'oss://backpack_item/vip_card/icon.png', 'oss://backpack_item/vip_card/icon.webp', '', '', '', '{}')
;

CREATE TABLE IF NOT EXISTS `live_redpacket_grab_block_user` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间。单位：秒'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='抢红包用户黑名单表';

CREATE TABLE IF NOT EXISTS `live_user_redeem_point` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
    ,`year` int NOT NULL DEFAULT 0 COMMENT '年份'
    ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
    ,`point` bigint NOT NULL DEFAULT 0 COMMENT '用户剩余兑换积分（单位：钻石）'
    ,`all_point` bigint NOT NULL DEFAULT 0 COMMENT '用户总获得过的兑换积分（单位：钻石）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='万事屋用户的兑换积分';

CREATE UNIQUE INDEX uk_year_userid ON live_user_redeem_point(`year`,`user_id`);

CREATE TABLE IF NOT EXISTS `live_preview` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`element_type` tinyint NOT NULL DEFAULT '0' COMMENT '预告关联类型。0：无关联元素；1：剧集'
  ,`element_id` bigint NOT NULL DEFAULT '0' COMMENT '预告关联元素的 ID，无关联元素时，默认为 0'
  ,`room_id` bigint NOT NULL COMMENT '直播间 ID'
  ,`title` varchar(30) NOT NULL COMMENT '标题'
  ,`live_start_time` bigint NOT NULL DEFAULT '0' COMMENT '直播开播时间点，单位：秒'
  ,`live_schedule_time` bigint NOT NULL DEFAULT '0' COMMENT '直播预计开播时间点，一般比直播时间早，单位：秒'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '预告卡上线时间点，单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT '0' COMMENT '预告卡下线时间点，单位：秒'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播预告表';

INSERT INTO `live_preview`
  (`id`, `create_time`, `modified_time`, `element_type`, `element_id`, `room_id`, `title`, `live_start_time`, `live_schedule_time`, `start_time`, `end_time`)
VALUES
  -- TestActionPreviewGet、TestNewPreviewParam、TestFindPreview
  (1001, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 233, 666, '测试预告 1', 0, 0, 0, UNIX_TIMESTAMP() + 3600)
  ,(1002, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 233, 666, '测试预告 2', 0, 0, 0, UNIX_TIMESTAMP() + 3600)
  -- TestFindPreview
  ,(1003, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 233, 666, '测试预告 3', 0, 0, UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 7200)
;

CREATE TABLE IF NOT EXISTS `live_preview_user_reservation` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`preview_id` bigint NOT NULL COMMENT '直播预告 ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播预约用户表';

INSERT INTO `live_preview_user_reservation`
  (`id`, `create_time`, `modified_time`, `preview_id`, `user_id`)
VALUES
  -- TestActionPreviewGet、TestNewPreviewParam
  (10001, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1001, 4234234)
;

-- GRANT SELECT, INSERT, UPDATE ON live_custom TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_custom` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`delete_time` bigint NOT NULL COMMENT '删除时间'
  ,`custom_type` tinyint NOT NULL COMMENT '定制类型'
  ,`custom_id` bigint NOT NULL DEFAULT 0 COMMENT '定制物品 ID'
  ,`element_id` bigint NOT NULL DEFAULT 0 COMMENT '定制归属 ID'
  ,`element_sort` bigint NOT NULL DEFAULT 0 COMMENT '排序'
  ,`source` tinyint NOT NULL DEFAULT 0 COMMENT '来源'
  ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间'
  ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '结束时间'
  ,`batch_id` bigint NOT NULL DEFAULT 0 COMMENT '批量添加日志 ID，0 表示非批量添加'
  ,`more` json NULL COMMENT '扩展信息, json'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播间定制信息表';

-- GRANT SELECT, INSERT, UPDATE ON live_custom_batch_add_log TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_custom_batch_add_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  , `create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  , `modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
  , `delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间，单位：秒'
  , `custom_type` tinyint NOT NULL DEFAULT 0 COMMENT '定制类型'
  , `custom_id` bigint NOT NULL DEFAULT 0 COMMENT '定制物品 ID'
  , `csv_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'CSV 文件地址'
  , `effective_time` bigint NOT NULL DEFAULT 0 COMMENT '生效时间，单位：秒'
  , `expire_time` bigint NOT NULL DEFAULT 0 COMMENT '过期时间，单位：秒'
  , PRIMARY KEY (`id`)
  , KEY `idx_createtime` (`create_time`)
  , KEY `idx_deletetime` (`delete_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播间专属礼物批量分配日志表';

-- GRANT SELECT, INSERT ON live_birthday_priv_record TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_birthday_priv_record` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
  ,`celeb_time` bigint NOT NULL DEFAULT 0 COMMENT '生日特权发放的时间'
  ,KEY `idx_userid_celebtime`(`user_id`,`celeb_time`)
) DEFAULT CHARSET=utf8mb4 COMMENT='用户生日特权发放记录';

INSERT INTO `live_birthday_priv_record`
  (`create_time`, `modified_time`, `user_id`, `celeb_time`)
VALUES
  -- TestFilterRewardedUsers
  (UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1919, UNIX_TIMESTAMP('2024-04-02'))
;

-- GRANT SELECT, INSERT, UPDATE ON live_application TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_application` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`intro` varchar NOT NULL DEFAULT '' COMMENT '用途'
  ,`type` tinyint NOT NULL DEFAULT 0 COMMENT '名单类型 1: 白名单，2: 黑名单'
  ,`scope` tinyint NOT NULL DEFAULT 0 COMMENT '名单范围 1: 小窗; 2: 福袋入口; 3: 发送自定义福袋; 4: 福袋参与资格; 5: 福袋和红包中奖资格'
  ,`element_type` tinyint NOT NULL DEFAULT 0 COMMENT '成员类型 1: 用户 ID，2: 房间 ID'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播名单表';

INSERT INTO `live_application`
  (`id`, `create_time`, `modified_time`, `intro` , `type`, `scope`, `element_type`)
VALUES
  -- TestElementExistsByType, TestIsRoomLuckyBagInitiateBlocked
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试查询福袋入口黑名单', 2, 2, 2)
  -- TestIsUserLuckyBagJoinBlocked
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试参加福袋黑名单', 2, 4, 1)
  -- TestIsRoomCustomLuckyBagInitiateAllowed
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试查询实物福袋入口白名单', 1, 3, 2)
  -- TestAddUserToDrawReceivedPrizeBlockList
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '测试福袋和红包中奖资格', 2, 5, 1)
  -- 月度宝箱相关测试使用
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '月度宝箱 - 挑战榜', 1, 6, 1)
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '月度宝箱 - 公会报名', 1, 7, 1)
;

-- GRANT SELECT, INSERT ON live_application_element TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_application_element` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间'
  ,`application_id` bigint NOT NULL DEFAULT 0 COMMENT '名单 ID'
  ,`element_id` bigint NOT NULL DEFAULT 0 COMMENT '成员 ID'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播名单成员表';

INSERT INTO `live_application_element`
  (`create_time`, `modified_time`, `application_id`, `element_id`)
VALUES
  -- TestElementExistsByType, TestIsRoomLuckyBagInitiateBlocked
  (UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 123456)
  -- TestIsUserLuckyBagJoinBlocked
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 123)
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 456)
  -- TestIsRoomCustomLuckyBagInitiateAllowed
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 121423)
;

-- GRANT SELECT, INSERT ON live_new_user_reward_record TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_new_user_reward_record` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint unsigned NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
  ,`reward_id` bigint unsigned NOT NULL DEFAULT 0 COMMENT '奖励 ID'
  ,`buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid'
  ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '发放状态 0: 无资格; 1: 已发放'
  ,UNIQUE KEY `uk_userid` (`user_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播新用户奖励记录表';

-- GRANT SELECT, INSERT, UPDATE ON live_lucky_bag_initiate_record TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_lucky_bag_initiate_record`(
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '发起人用户 ID'
  ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '房间 ID'
  ,`creator_id` bigint NOT NULL DEFAULT 0 COMMENT '主播 ID'
  ,`type` tinyint NOT NULL DEFAULT 0 COMMENT '福袋类型: 1: 广播剧; 2: 实物福袋'
  ,`reward_type` tinyint NOT NULL DEFAULT 0 COMMENT '福袋奖励类型: 1: 广播剧; 2: 个人周边; 3: 剧集周边'
  ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '福袋状态: -1: 已删除; 0: 待开奖; 1: 开奖中; 2: 已结束'
  ,`prize_drama_id` bigint NOT NULL DEFAULT 0 COMMENT '奖励广播剧 ID'
  ,`prize_ipr_id` bigint NOT NULL DEFAULT 0 COMMENT '剧集 IP ID'
  ,`name` varchar(60) NOT NULL DEFAULT '' COMMENT '广播剧名称或实物奖品名称'
  ,`num` int NOT NULL DEFAULT 0 COMMENT '奖品数量'
  ,`target_type` bigint NOT NULL DEFAULT 0 COMMENT '参与对象类型: 0: 所有人; 1: 关注; 2: 粉丝勋章; 3:超粉'
  ,`message` varchar(20) NOT NULL DEFAULT '' COMMENT '口令'
  ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '抽奖开始时间'
  ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '抽奖结束时间'
  ,`scheduled_end_time` bigint NOT NULL DEFAULT 0 COMMENT '预计抽奖结束时间'
  ,`reward_time` bigint NOT NULL DEFAULT 0 COMMENT '开奖时间'
  ,`join_num` int NOT NULL DEFAULT 0 COMMENT '参与抽奖人数'
  ,`transaction_id` bigint NOT NULL DEFAULT 0 COMMENT '订单号'
  ,`more` json DEFAULT NULL COMMENT '扩展字段'
  ,INDEX `idx_roomid_status_startime` (`room_id`, `status`, `start_time`)
  ,INDEX `idx_prizeiprid_type_status` (`prize_ipr_id`, `type`, `status`)
  ,INDEX `idx_status_scheduledendtime` (`status`, `scheduled_end_time`)
  ,INDEX `idx_modifiedtime` (`modified_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播生成福袋记录';

INSERT INTO `live_lucky_bag_initiate_record`
  (`id`, `create_time`, `modified_time`, `user_id`, `room_id`, `creator_id`, `type`, `reward_type`, `status`, `prize_drama_id`, `prize_ipr_id`, `name`, `num`, `target_type`, `message`, `start_time`, `end_time`, `scheduled_end_time`, `join_num`, `transaction_id`, `more`)
VALUES
  -- TestActionPrizeInfo、TestNewPrizeInfoParams、TestPrizeInfoParams_getPrizeInfo
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 789, 3457181, 1, 1, 2, 987, 654, '广播剧 1', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 1, 987654321, '{"prize_icon": "oss://icon1.png"}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 789, 3457181, 1, 1, 2, 987, 654, '广播剧 2', 10, 0, '口令 2', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654322, '{"prize_icon": "oss://icon2.png"}')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 789, 3457181, 1, 1, 2, 987, 654, '广播剧 3', 10, 0, '口令 2', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png"}')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 789, 3457181, 1, 1, 2, 987, 654, '广播剧 1', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 1, 987654324, '{"prize_icon": "oss://icon1.png"}')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 789, 3457181, 1, 1, 2, 987, 654, '广播剧 2', 10, 0, '口令 2', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654325, '{"prize_icon": "oss://icon2.png"}')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 789, 3457181, 1, 1, 2, 987, 654, '广播剧 3', 10, 0, '口令 3', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654326, '{"prize_icon": "oss://icon3.png"}')
  -- TestActionGetDramaLuckyBags
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3456864, 100000005, 3456864, 1, 1, 0, 1234, 4567, '福袋 rpc', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654326, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457024, 100000006, 3457024, 1, 1, 0, 1234, 4567, '福袋 rpc', 20, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654326, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457114, 100000007, 3457114, 1, 1, 0, 1234, 4567, '福袋 rpc', 30, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654326, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
  ,(10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457111, 100000008, 3457111, 1, 1, 0, 1234, 4567, '福袋 rpc', 5, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654326, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
  -- TestNewJoinParams、TestJoinParam_insertRecord
  ,(11, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 18113499, 3457181, 1, 1, 0, 987, 654, '广播剧 1', 10, 0, '口令 2', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png"}')
  -- TestActionAdminRemove、TestNewAdminLuckyBagRemoveParam、TestLuckyBagRemoveParam_check、TestLuckyBagRemoveParam_delete、TestLuckyBagRemoveParam_sendSystemMsg、TestLuckyBagRemoveParam_sendRoomMsg
  ,(12, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 18113499, 3457181, 1, 1, 0, 987, 654, '删除福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png"}')
  ,(13, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 18113499, 3457181, 1, 1, 1, 987, 654, '删除福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png"}')
  ,(14, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457181, 18113499, 3457181, 1, 1, 0, 987, 654, '删除福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png"}')
  -- TestListAll、TestActionDramaList
  ,(15, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3456864, 100000005, 3456864, 1, 1, 0, 6767, 2233, '广播剧福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
  ,(16, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457024, 100000006, 3457024, 1, 1, 0, 6768, 0, '广播剧福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png"}')
  ,(17, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457114, 100000007, 3457114, 1, 1, 0, 6769, 2234, '广播剧福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
  ,(18, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3457111, 100000008, 3457111, 1, 1, 0, 6766, 2233, '广播剧福袋', 10, 0, '口令 1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 3600, 2, 987654323, '{"prize_icon": "oss://icon3.png", "prize_ipr_name": "IP 名称"}')
;

-- GRANT SELECT, INSERT ON live_lucky_bag_join_record TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_lucky_bag_join_record`(
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`lucky_bag_id` bigint NOT NULL DEFAULT 0 COMMENT '福袋 ID'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '参与用户 ID'
  ,`status` bigint NOT NULL DEFAULT 0 COMMENT '参与用户状态'
  ,`ip` VARCHAR(50) NOT NULL COMMENT '用户 IP'
  ,`source` tinyint NOT NULL DEFAULT 0 COMMENT '参与来源, 0: 来源直播间; 1: 来源广告'
  ,UNIQUE KEY `uk_luckybagid_userid` (`lucky_bag_id`, `user_id`)
  ,INDEX `idx_modifiedtime` (`modified_time`)
  ,INDEX `idx_userid_createtime` (`user_id`, `create_time`)
  ,INDEX `idx_ip_createtime` (`ip`, `create_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '用户参与福袋抽奖记录';

INSERT INTO `live_lucky_bag_join_record`
  (`id`, `create_time`, `modified_time`, `lucky_bag_id`, `user_id`, `status`, `ip`, `source`)
VALUES
  -- TestActionPrizeInfo、TestNewPrizeInfoParams、TestPrizeInfoParams_getPrizeInfo
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 3457182, 0, "", 1)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 3457182, 0, "", 1)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 3457182, 0, "", 0)
  -- TestCountOneDayJoinRecordByUser, TestCountOneDayJoinRecordByIP
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 123, 0, "127.0.0.1", 0)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 456, 0, "127.0.0.1", 0)
  -- TestActionRedeemPrize、TestNewPrizeInfoParams、TestRedeemPrizeParams_redeemPrize
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 3457182, 0, "", 0)
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 5, 3457182, 0, "", 0)
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, 3457182, 0, "", 0)
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 3457181, 0, "", 0)
  -- TestDrawByStatus、TestDraw
  ,(10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 11, 3457181, 0, "", 0)
  ,(11, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 11, 3457182, 0, "", 0)
  ,(12, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 11, 3457183, 1, "", 0)
;

-- GRANT SELECT, INSERT, UPDATE ON live_lucky_bag_user_prize TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_lucky_bag_user_prize`(
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`lucky_bag_id` bigint NOT NULL DEFAULT 0 COMMENT '福袋 ID'
  ,`reward_time` bigint NOT NULL DEFAULT 0 COMMENT '中奖时间'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '中奖用户 ID'
  ,`redeem_end_time` bigint NOT NULL DEFAULT 0 COMMENT '兑换截止时间'
  ,`redeem_time` bigint NOT NULL DEFAULT 0 COMMENT '兑换时间'
  ,`redeem_user_id` bigint NOT NULL DEFAULT 0 COMMENT '实际领奖用户 ID'
  ,`transaction_id` bigint NOT NULL DEFAULT 0 COMMENT '兑奖订单号'
  ,INDEX `idx_userid_createtime` (`user_id`, `create_time`)
  ,INDEX `idx_modifiedtime` (`modified_time`)
  ,UNIQUE KEY `uk_luckybagid_userid` (`lucky_bag_id`, `user_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '用户福袋中奖记录';

INSERT INTO `live_lucky_bag_user_prize`
  (`id`, `create_time`, `modified_time`, `lucky_bag_id`, `reward_time`, `user_id`, `redeem_end_time`, `redeem_time`, `redeem_user_id`, `transaction_id`)
VALUES
  -- TestActionPrizeInfo、TestNewPrizeInfoParams、TestPrizeInfoParams_getPrizeInfo
  -- 未兑奖
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, UNIX_TIMESTAMP(), 3457182, UNIX_TIMESTAMP() + 3600, 0, 0, 0)
  -- 已兑奖
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, UNIX_TIMESTAMP(), 3457182, UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 60, 3457182, 9876543260)
  -- 已过期
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, UNIX_TIMESTAMP(), 3457182, UNIX_TIMESTAMP() - 1, 0, 0, 0)
  -- TestActionRedeemPrize、TestNewPrizeInfoParams、TestRedeemPrizeParams_redeemPrize
  -- 未兑奖
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, UNIX_TIMESTAMP(), 3457182, UNIX_TIMESTAMP() + 3600, 0, 0, 0)
  -- 已兑奖
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 5, UNIX_TIMESTAMP(), 3457182, UNIX_TIMESTAMP() + 3600, UNIX_TIMESTAMP() + 60, 3457182, 9876543261)
  -- 已过期
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, UNIX_TIMESTAMP(), 3457182, UNIX_TIMESTAMP() - 1, 0, 0, 0)
  -- 未兑奖
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, UNIX_TIMESTAMP(), 3457181, UNIX_TIMESTAMP() + 3600, 0, 0, 0)
;

-- GRANT SELECT, INSERT, UPDATE ON live_reward_send_record TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_reward_send_record`(
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`trace_id` varchar(64) NOT NULL DEFAULT '' COMMENT '链路 ID'
  ,`uuid` varchar(64) NOT NULL DEFAULT '' COMMENT '上游发奖唯一 ID'
  ,`reward_id` bigint NOT NULL DEFAULT 0 COMMENT '奖励 ID'
  ,`reward_type` tinyint NOT NULL DEFAULT 0 COMMENT '奖励类型'
  ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '奖励状态，-1: 发放失败; 0: 待发放; 1: 成功发放'
  ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '接收奖励的用户 ID'
  ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '接收奖励的 ID'
  ,`creator_id` bigint NOT NULL DEFAULT 0 COMMENT '接收奖励的主播 ID'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '发放奖励的记录';

-- GRANT SELECT ON live_extra_banner TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_extra_banner`(
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
  ,`position` bigint NOT NULL COMMENT '直播通栏所在位置'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`room_id` bigint NOT NULL COMMENT '房间 ID'
  ,`cover` varchar(125) NOT NULL COMMENT '通栏图地址'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '直播通栏表';

-- GRANT SELECT, UPDATE ON live_user_special_red_packet TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_user_special_red_packet` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒',
  `modified_time` bigint  NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `goods_id` bigint NOT NULL COMMENT '红包所属商品 ID，live_goods 表主键',
  `gain_num` bigint NOT NULL COMMENT '初始发放的数量',
  `num` bigint NOT NULL COMMENT '当前剩余数量',
  `start_time` bigint NOT NULL DEFAULT '0' COMMENT '生效开始时间点。单位：秒',
  `end_time` bigint NOT NULL DEFAULT '0' COMMENT '失效时间点，当前时间小于 end_time 时，红包未过期，为 0 时代表永久生效。单位：秒'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户拥有的特殊红包记录';

INSERT INTO `live_user_special_red_packet`
  (`id`, `create_time`, `modified_time`, `user_id`, `goods_id`, `gain_num`, `num`, `start_time`, `end_time`)
VALUES
  -- TestFindUserValidSpecialRedPacket
  -- TestDeductNum
  -- TestSendParam_deductSpecialRedPacket
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 900, 18, 100, 1, UNIX_TIMESTAMP() - 10, UNIX_TIMESTAMP())
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 901, 19, 100, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600)
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 901, 19, 100, 1, UNIX_TIMESTAMP(), 0)
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 901, 19, 100, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 10)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 12, 23, 100, 10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600)
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 13, 23, 100, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600)
  -- TestSendParam_sendRedPacket
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 11, 23, 100, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP() + 3600)
;

-- GRANT SELECT ON live_user_7days_reward_creator_rank TO 'live_service'@'%'
CREATE TABLE `live_user_7days_reward_creator_rank` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`bizdate` date NOT NULL COMMENT '业务日期'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`room_id` bigint NOT NULL COMMENT '直播间 ID'
  ,`creator_id` bigint NOT NULL COMMENT '主播 ID'
  ,`rank` bigint NOT NULL COMMENT '打赏量排名，主播在用户近 7 日打赏的所有主播中的排名，从 1 开始，排名越靠前打赏量越高'
  ,KEY `idx_userid_bizdate` (`user_id`,`bizdate`)
  ,KEY `idx_bizdate` (`bizdate`)
  ,KEY `idx_modifiedtime` (`modified_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户近 7 日打赏主播排名表';

INSERT INTO `live_user_7days_reward_creator_rank`
  (`create_time`, `modified_time`, `bizdate`, `user_id`, `room_id`, `creator_id`, `rank`)
VALUES
  (UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '2022-01-01', 123, 18113499,12, 1)
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '2022-01-01', 123, 22334, 22334, 2)
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '2022-01-01', 123, 22335, 22335, 2)
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '2022-01-01', 123, 100000010, 9074510, 3)
  ,(UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '2022-01-02', 123, 100000010, 9074510, 3)
;

-- GRANT SELECT ON live_creator_recommend TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_creator_recommend` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间。单位：秒'
  ,`bizdate` date NOT NULL COMMENT '业务日期'
  ,`type` tinyint NOT NULL COMMENT '推荐类型（1：7 日播放量 TOP 100 剧集声优主播）'
  ,`element_type` tinyint NOT NULL COMMENT '元素类型（5：主播 ID）'
  ,`data` json NOT NULL COMMENT '主播 ID，e.g. [123456, 123457, 123458]'
  ,UNIQUE KEY `uk_bizdate_type` (`bizdate`, `type`)
  ,INDEX `idx_modifiedtime` (`modified_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播推荐表';

INSERT INTO `live_creator_recommend`
  (`id`, `create_time`, `modified_time`, `bizdate`, `type`, `element_type`, `data`)
VALUES
  -- TestFindCreatorIDs
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '2024-11-05', 1, 5, '[123456,123457]')
;

-- GRANT SELECT, INSERT, UPDATE ON live_lucky_box_record TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_lucky_box_record` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`user_id` bigint NOT NULL COMMENT '用户 ID'
    ,`room_id` bigint NOT NULL COMMENT '房间 ID'
    ,`goods_id` bigint NOT NULL COMMENT '宝盒商品 ID'
    ,`gift_id` bigint NOT NULL COMMENT '礼物 ID'
    ,`order_id` bigint NOT NULL COMMENT '订单 ID'
    ,KEY `idx_goodsid_userid_giftid` (`goods_id`, `user_id`, `gift_id`)
    ,KEY `idx_createtime` (`create_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '宝盒记录表';

-- GRANT SELECT ON live_lucky_box_task TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_lucky_box_task` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`sort` tinyint NOT NULL COMMENT '排序'
    ,`goods_id` bigint NOT NULL COMMENT '商品 ID'
    ,`name` varchar(255) NOT NULL COMMENT '任务名称'
    ,`intro` varchar(255) NOT NULL COMMENT '任务描述'
    ,`type` tinyint NOT NULL COMMENT '任务类型，1：收集全套礼物，2：收集目标礼物'
    ,`num` int NOT NULL COMMENT '收集数量'
    ,`gift_id` bigint NOT NULL COMMENT '礼物 ID'
    ,`prize_id` bigint NOT NULL COMMENT '奖品 ID'
    ,`prize_type` tinyint NOT NULL COMMENT '奖品类型，1：礼物，2：外观'
    ,`prize_name` varchar(255) NOT NULL COMMENT '奖品名称'
    ,`prize_icon` varchar(255) NOT NULL COMMENT '奖品图标'
    ,`prize_duration` bigint NOT NULL COMMENT '奖品持续时间，单位：秒'
    ,KEY `idx_goodsid` (`goods_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '宝盒任务表';

-- GRANT SELECT, INSERT ON live_lucky_box_task_record TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_lucky_box_task_record` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`lucky_box_task_id` bigint NOT NULL COMMENT '任务 ID'
    ,`user_id` bigint NOT NULL COMMENT '用户 ID'
    ,`room_id` bigint NOT NULL COMMENT '房间 ID'
    ,KEY `idx_userid_luckyboxtaskid` (`user_id`, `lucky_box_task_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '宝盒任务记录表';

-- GRANT SELECT, INSERT ON live_lucky_box_task_record_map TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_lucky_box_task_record_map` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`user_id` bigint NOT NULL COMMENT '用户 ID'
    ,`lucky_box_task_id` bigint NOT NULL COMMENT '任务 ID'
    ,`lucky_box_record_id` bigint NOT NULL COMMENT '宝盒记录 ID'
    ,KEY `idx_luckyboxtaskid_luckyboxrecordid` (`lucky_box_task_id`, `lucky_box_record_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '宝盒任务记录映射表';

-- GRANT SELECT, INSERT, UPDATE ON live_gift_rate_sync TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_gift_rate_sync` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`element_type` tinyint NOT NULL COMMENT '元素类型，1：随机礼物；2：随机礼物回报；3：宝盒'
    ,`element_id` bigint NOT NULL COMMENT '元素 ID'
    ,`status` tinyint NOT NULL COMMENT '状态, 0: 下架；1：正常'
    ,`gift_pool_id` bigint NOT NULL COMMENT '礼物池 ID'
    ,`gift_id` bigint NOT NULL COMMENT '奖池的礼物 ID'
    ,`expected_rate` decimal(8,5) NOT NULL COMMENT '期望概率'
    ,`actual_rate` decimal(8,5) NULL COMMENT '实际概率'
    ,`total_num` bigint NOT NULL DEFAULT 0 COMMENT '抽中次数'
    ,`total_user_num` bigint NOT NULL DEFAULT 0 COMMENT '抽中用户数'
    ,`more` json DEFAULT NULL COMMENT '更多信息'
    ,KEY `idx_elementtype_elementid_giftid` (`element_type`, `element_id`, `gift_id`)
    ,KEY `idx_elementtype_status` (`element_type`, `status`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '礼物概率同步表';

-- GRANT SELECT, INSERT ON live_lucky_gift_drop_config TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_lucky_gift_drop_config` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间，单位：秒'
  ,`delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间，单位：秒'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '开始时间，单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT '0' COMMENT '结束时间，单位：秒'
  ,`gift_id` bigint NOT NULL DEFAULT '0' COMMENT '随机礼物 ID'
  ,`gift_num` bigint NOT NULL DEFAULT '0' COMMENT '随机礼物档位'
  ,`prize_gift_id` bigint NOT NULL DEFAULT '0' COMMENT '投放礼物 ID'
  ,`prize_gift_total_num` bigint NOT NULL DEFAULT '0' COMMENT '投放礼物总数量'
  ,KEY `idx_giftid_giftnum` (`gift_id`, `gift_num`)
) DEFAULT CHARSET=utf8mb4 COMMENT='随机礼物投放配置表';

-- GRANT SELECT, INSERT, UPDATE ON live_lucky_gift_drop_record TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_lucky_gift_drop_record` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间，单位：秒'
  ,`config_id` bigint NOT NULL DEFAULT '0' COMMENT '配置 ID'
  ,`expected_time` bigint NOT NULL DEFAULT '0' COMMENT '期望投放时间，单位：秒'
  ,`actual_time` bigint NOT NULL DEFAULT '0' COMMENT '实际发放时间，单位：秒'
  ,`user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`room_id` bigint NOT NULL DEFAULT '0' COMMENT '房间 ID'
  ,`transaction_id` bigint NOT NULL DEFAULT '0' COMMENT '订单号'
  ,KEY `idx_configid_expectedtime` (`config_id`, `expected_time`)
) DEFAULT CHARSET=utf8mb4 COMMENT='随机礼物投放记录表';

-- GRANT SELECT, INSERT, DELETE ON live_multi_connect_blocklist TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_blocklist` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`room_id` bigint NOT NULL COMMENT '房间 ID'
    ,`block_room_id` bigint NOT NULL COMMENT '被拉黑的房间 ID'
    ,KEY `idx_roomid_blockroomid` (`room_id`, `block_room_id`)
    ,KEY `idx_blockroomid_roomid` (`block_room_id`, `room_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线黑名单表';

-- GRANT SELECT, INSERT, UPDATE ON live_multi_connect_match TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_match` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间，单位：毫秒'
    ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '结束时间，单位：毫秒'
    ,`type` tinyint NOT NULL COMMENT '匹配类型，1：申请，2：邀请'
    ,`status` tinyint NOT NULL COMMENT '状态，1：未处理，2：已同意，3：取消，4：已拒绝，5：超时'
    ,`group_id` bigint NOT NULL DEFAULT 0 COMMENT '主播连线组 ID（主播在连线中邀请时才有值）'
    ,`from_room_id` bigint NOT NULL COMMENT '发起方房间 ID'
    ,`to_room_id` bigint NOT NULL COMMENT '目标房间 ID'
    ,`bridge_room_id` bigint NOT NULL DEFAULT 0 COMMENT '中间房间 ID（仅申请存在）'
    ,KEY `idx_fromroomid_status` (`from_room_id`, `status`)
    ,KEY `idx_toroomid_status` (`to_room_id`, `status`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线匹配表';

-- GRANT SELECT, INSERT, UPDATE ON live_multi_connect_group TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_group` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间，单位：毫秒'
    ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '结束时间，单位：毫秒'
    ,`connect_id` varchar(50) NOT NULL DEFAULT '' COMMENT '连线 ID'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线组表';

-- GRANT SELECT, INSERT, UPDATE ON live_multi_connect_group_member TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_group_member` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`group_id` bigint NOT NULL COMMENT '连线组 ID'
    ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间，单位：毫秒'
    ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '结束时间，单位：毫秒'
    ,`role` tinyint NOT NULL COMMENT '角色，1：主麦，2：成员'
    ,`status` tinyint NOT NULL COMMENT '状态，1：进行中，2：退出，3：踢出，4：结束'
    ,`room_id` bigint NOT NULL COMMENT '房间 ID'
    ,UNIQUE KEY `uk_roomid_endtime` (`room_id`, `end_time`)
    ,KEY `idx_groupid_endtime_roomid` (`group_id`, `end_time`, `room_id`)
    ,KEY `idx_endtime_groupid_roomid` (`end_time`, `group_id`, `room_id`)
    ,KEY `idx_roomid_starttime` (`room_id`, `start_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线成员表';

-- GRANT SELECT, INSERT, UPDATE, DELETE ON live_multi_connect_group_mute TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_group_mute` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`group_id` bigint NOT NULL COMMENT '连线组 ID'
    ,`group_member_id` bigint NOT NULL COMMENT '主播连线成员 ID'
    ,`mute_group_member_id` bigint NOT NULL COMMENT '静音的成员 ID'
    ,UNIQUE KEY `uk_groupmemberid_mutegroupmemberid_groupid` (`group_member_id`, `mute_group_member_id`, `group_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线静音表';

-- GRANT SELECT, INSERT, DELETE ON live_multi_connect_group_mic_off TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_group_mic_off` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`group_id` bigint NOT NULL COMMENT '连线组 ID'
    ,`group_member_id` bigint NOT NULL COMMENT '主播连线成员 ID'
    ,UNIQUE KEY `uk_groupid_groupmemberid` (`group_id`, `group_member_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线闭麦表';

-- GRANT SELECT, INSERT ON live_multi_connect_record TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_multi_connect_record`(
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`group_id` bigint NOT NULL COMMENT '连线组 ID'
    ,`group_member_id` bigint NOT NULL COMMENT '连线成员 ID'
    ,`group_owner_member_id` bigint NOT NULL COMMENT '连线主麦 ID'
    ,`room_id` bigint NOT NULL COMMENT '房间 ID'
    ,`start_time` bigint NOT NULL COMMENT '连线开始时间，单位：毫秒'
    ,`end_time` bigint NOT NULL COMMENT '连线结束时间，单位：毫秒'
    ,`duration` bigint NOT NULL COMMENT '连线时长，单位：毫秒'
    ,KEY `idx_roomid_starttime` (`room_id`, `start_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '主播连线记录表';

-- GRANT SELECT, INSERT, UPDATE ON live_activity_job TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_activity_job` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间，单位：秒'
    ,`event_id` bigint NOT NULL COMMENT '事件 ID'
    ,`intro` varchar(255) NOT NULL COMMENT '描述'
    ,`run_time` bigint NOT NULL COMMENT '执行时间，单位：秒'
    ,`more` json DEFAULT NULL COMMENT '更多信息'
    ,KEY `idx_runtime` (`run_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '活动定时任务表';

-- GRANT SELECT ON live_prize TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_prize` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`type` tinyint NOT NULL DEFAULT 0 COMMENT '奖品类型，1：礼物，2：外观，3：榜单值'
    ,`element_type` tinyint NOT NULL DEFAULT 0 COMMENT '元素类型'
    ,`element_id` bigint NOT NULL DEFAULT 0 COMMENT '元素 ID'
    ,`name` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品名称'
    ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品图标'
    ,`num` bigint NOT NULL DEFAULT 0 COMMENT '奖品数量'
    ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间，单位：秒'
    ,`expire_time` bigint NOT NULL DEFAULT 0 COMMENT '过期时间，单位：秒'
    ,`duration` bigint NOT NULL DEFAULT 0 COMMENT '持续时间，单位：秒'
    ,`more` json DEFAULT NULL COMMENT '更多信息'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '奖品表';

INSERT INTO `live_prize`
    (`id`, `create_time`, `modified_time`, `type`, `element_type`, `element_id`, `name`, `icon`, `num`, `start_time`, `expire_time`, `duration`, `more`)
VALUES
    (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 1, 1011, 'appearance1', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600,  '')
    ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 1, 1011, 'appearance2', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600,  '')
    ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 1, 1011, '测试奖品', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600,  '')
    ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 1, 30001, 'gift', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 5, 3600, '')
    ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 5, 1, 30001, 'creator_gift', 'oss://icon.png', 5, UNIX_TIMESTAMP(), 0, 3600,  '')
    ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, 1, 30004, 'live_tag', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600,  '{"create_card":{"url":"oss://live/labelicon/livelist/liveshow-1.png"}}')
    ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 9, 1, 1, 'user_sticker', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600,  '')
    ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 9, 2, 1, 'room_sticker', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600,  '')
    ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 15, 1, 1, 'room_gift_custom', 'oss://icon.png', 1, UNIX_TIMESTAMP(), 0, 3600, '')
    -- TestActionFansBoxDraw
    ,(13, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 2, 334455, 'fans_box_avatar_frame', 'oss://test/fans_box.png', 1, UNIX_TIMESTAMP(), 0, 3600, '')
;

-- GRANT SELECT ON live_prize_package TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_prize_package` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`name` varchar(255) DEFAULT '' NOT NULL COMMENT '奖品包名称'
    ,KEY `idx_modifiedtime` (`modified_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='奖品包表';

-- GRANT SELECT ON live_prize_package_map TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_prize_package_map` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`prize_id` bigint NOT NULL DEFAULT 0 COMMENT '奖品 ID'
    ,`prize_package_id` bigint NOT NULL DEFAULT 0 COMMENT '奖品包 ID'
    ,KEY `idx_modifiedtime` (`modified_time`)
    ,UNIQUE KEY `uk_prizepackageid_prizeid` (`prize_package_id`, `prize_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='奖品包与奖品关联表';

-- GRANT SELECT, UPDATE, INSERT ON live_prize_log TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_prize_log` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`receive_time` bigint NOT NULL DEFAULT 0 COMMENT '领取时间，单位：秒'
    ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '状态，-1：回滚，0：领取中，1：已领取，2：领取失败'
    ,`biz` tinyint NOT NULL DEFAULT 0 COMMENT '业务类型，1：礼物升级'
    ,`biz_id` varchar(30) NOT NULL DEFAULT 0 COMMENT '业务 ID'
    ,`prize_id` bigint NOT NULL DEFAULT 0 COMMENT '奖品 ID'
    ,`prize_package_id` bigint NOT NULL DEFAULT 0 COMMENT '奖品包 ID'
    ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
    ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '房间 ID'
    ,`more` json DEFAULT NULL COMMENT '更多信息（错误信息 & 领取前信息 & 领取后信息）'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '奖品记录表';

-- GRANT SELECT ON live_gift_upgrade TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_gift_upgrade` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`type` tinyint NOT NULL DEFAULT 0 COMMENT '类型，1：初始礼物，2：终极礼物（集齐获得），3：升级礼物'
    ,`base_gift_id` bigint NOT NULL DEFAULT 0 COMMENT '基础礼物 ID'
    ,`upgrade_gift_id` bigint NOT NULL DEFAULT 0 COMMENT '升级礼物 ID'
    ,`gift_small_icon` varchar(255) NOT NULL DEFAULT '' COMMENT '升级栏图标'
    ,`weight` int NOT NULL DEFAULT 0 COMMENT '权重'
    ,`prize_id` bigint NOT NULL DEFAULT 0 COMMENT '奖品 ID'
    ,`more` json DEFAULT NULL COMMENT '更多信息'
    ,UNIQUE KEY `uk_basegiftid_upgradegiftid` (`base_gift_id`, `upgrade_gift_id`)
    ,KEY `idx_upgradegiftid_type` (`upgrade_gift_id`, `type`)
) DEFAULT CHARSET=utf8mb4 COMMENT '礼物升级表';

-- GRANT SELECT, INSERT ON live_gift_upgrade_record TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_gift_upgrade_record` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`base_gift_id` bigint NOT NULL DEFAULT 0 COMMENT '基础礼物 ID'
    ,`upgrade_gift_id` bigint NOT NULL COMMENT '升级礼物 ID'
    ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
    ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '房间 ID'
    ,`prize_log_id` bigint NOT NULL DEFAULT 0 COMMENT '奖品记录 ID'
    ,KEY `idx_userid_basegiftid_createtime` (`user_id`, `base_gift_id`, `create_time`)
) DEFAULT CHARSET=utf8mb4 COMMENT '礼物升级记录表';

-- GRANT SELECT, INSERT ON live_medal_remove_record TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_medal_remove_record` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '直播间 ID'
    ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
    ,`type` int NOT NULL DEFAULT 0 COMMENT '删除类型，1：衰减删除；2：用户删除'
    ,`point` bigint NOT NULL DEFAULT 0 COMMENT '删除前的粉丝勋章积分'
    ,KEY `idx_userid` (`user_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '粉丝勋章删除记录表';

-- GRANT SELECT, INSERT ON live_medal_point_change_log TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_medal_point_change_log` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键 ID'
    ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
    ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '更新时间，单位：秒'
    ,`medal_oid` varchar(50) NOT NULL DEFAULT '' COMMENT '粉丝勋章 ID'
    ,`room_id` bigint NOT NULL DEFAULT 0 COMMENT '直播间 ID'
    ,`creator_id` bigint NOT NULL DEFAULT 0 COMMENT '主播 ID'
    ,`user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户 ID'
    ,`change_type` int NOT NULL DEFAULT 0 COMMENT '变化类型，1：增加；2：减少'
    ,`scene` int NOT NULL DEFAULT 0 COMMENT '变化场景，1：超管后台；2：免费礼物；3：分享任务；4：在线任务；5：付费礼物；6：提问；7：超粉；8：付费弹幕；9：宝盒；10：超能魔方'
    ,`before_point` bigint NOT NULL DEFAULT 0 COMMENT '变化前的亲密度'
    ,`after_point` bigint NOT NULL DEFAULT 0 COMMENT '变化后的亲密度'
    ,`actual_change_point` bigint NOT NULL DEFAULT 0 COMMENT '实际变化的亲密度（受上限和倍率影响）'
    ,`returned_point` bigint NOT NULL DEFAULT 0 COMMENT '衰减返还的亲密度'
    ,`expected_change_point` bigint NOT NULL DEFAULT 0 COMMENT '计算倍率后的预期变化的亲密度（不受上限限制）'
    ,`point_multi` bigint NOT NULL DEFAULT 0 COMMENT '亲密度倍率'
    ,`t_level_threshold` bigint NOT NULL DEFAULT 0 COMMENT '计算倍率后的亲密度上限'
    ,`level_threshold_multi` bigint NOT NULL DEFAULT 0 COMMENT '亲密度上限倍率'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT '粉丝勋章亲密度变化明细表';

-- GRANT SELECT ON live_black_card TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_black_card` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`title` varchar(50) NOT NULL DEFAULT '' COMMENT '黑卡名称'
  ,`level` int NOT NULL DEFAULT '0' COMMENT '黑卡等级'
  ,`price` bigint NOT NULL DEFAULT '0' COMMENT '开通价格（单位：钻石）'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '黑卡等级图标'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='黑卡等级表'
;

INSERT INTO `live_black_card`
  (`id`, `create_time`, `modified_time`, `title`, `level`, `price`, `icon`)
VALUES
  -- TestActionBlackCardInfo、TestListAllBlackCard
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '星曜 V1', 1, 200000, 'oss://test1.png')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '星曜 V2', 2, 500000, 'oss://test2.png')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '星曜 V3', 3, 1000000, 'oss://test3.png')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '星曜 V4', 4, 2000000, 'oss://test4.png')
;

-- GRANT SELECT, INSERT ON live_user_black_card TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_user_black_card` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID'
  ,`black_card_id` bigint NOT NULL DEFAULT '0' COMMENT '黑卡等级 ID, 同 live_black_card.id'
  ,`start_time` bigint NOT NULL DEFAULT '0' COMMENT '开始时间（秒级时间戳）'
  ,`expire_time` bigint NOT NULL DEFAULT '0' COMMENT '过期时间（秒级时间戳）'
  ,KEY `idx_modifiedtime` (`modified_time`)
  ,KEY `idx_userid_expiretime` (`user_id`, `expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='黑卡用户开通记录表'
;

INSERT INTO `live_user_black_card`
  (`id`, `create_time`, `modified_time`, `user_id`, `black_card_id`, `start_time`, `expire_time`)
VALUES
  -- TestActionStatusGet
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 12, 3, UNIX_TIMESTAMP(), 1999999999)
;

-- GRANT SELECT ON live_black_card_gift_upgrade TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `live_black_card_gift_upgrade` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间'
  ,`level` tinyint NOT NULL COMMENT '黑卡钻石皮肤礼物对应的黑卡等级'
  ,`base_gift_id` bigint NOT NULL COMMENT '黑卡钻石皮肤基础礼物 ID'
  ,`base_gift_small_icon` varchar(255) NOT NULL DEFAULT '' COMMENT '黑卡钻石皮肤基础礼物栏图标。若字段为空，使用 MongoDB 数据库 gifts 文档的 icon 字段'
  ,`black_card_gift_id` bigint NOT NULL COMMENT '黑卡钻石皮肤礼物 ID'
  ,`black_card_gift_small_icon` varchar(255) NOT NULL DEFAULT '' COMMENT '黑卡钻石皮肤礼物栏图标。若字段为空，使用 MongoDB 数据库 gifts 文档的 icon 字段'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='黑卡钻石皮肤礼物升级表'
;

INSERT INTO `live_black_card_gift_upgrade`
  (`id`, `create_time`, `modified_time`, `level`, `base_gift_id`, `base_gift_small_icon`, `black_card_gift_id`,`black_card_gift_small_icon`)
VALUES
  -- TestFindBlackCardGiftUpgrades、TestMetaResp_buildBlackCardGifts、FindBlackCardGiftUpgrade
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 2333, 'oss://icon.png', 3444, 'oss://icon_1.png'),
  (2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 2335, 'oss://icon.png', 3555, 'oss://icon_1.png'),
  -- TestGiftSendParam_findGift
  (3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 2334, 'oss://icon.png', 3445, 'oss://icon_1.png')
;

-- GRANT SELECT ON live_preset_message TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_preset_message` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间，单位：秒'
  ,`message` varchar(100) NOT NULL COMMENT '预设消息内容'
  ,INDEX `idx_modifiedtime` (`modified_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播预设消息表';

-- 插入示例数据
INSERT INTO `live_preset_message`
  (`id`, `create_time`, `modified_time`, `message`)
VALUES
  -- TestFindAllMessages
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '大家好！')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '我来了~')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '主播好厉害！')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '支持主播~')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '666666')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '主播真棒！')
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '主播说话声音真好听')
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '笑死我了哈哈哈')
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '暗中观察')
  ,(10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '好可爱啊')
  ,(11, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '主播好有才华')
  ,(12, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '这首歌真好听')
  ,(13, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '太厉害了吧')
  ,(14, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '主播加油~')
  ,(15, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '我是新粉丝')
  ,(16, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '前方高能')
  ,(17, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '这个游戏玩得太好了')
  ,(18, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '弹幕走起来')
  ,(19, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '打卡签到~')
  ,(20, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '来了来了')
;

-- GRANT SELECT ON live_gashapon_collect_event TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_gashapon_collect_event` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间，单位：秒'
  ,`start_time` bigint NOT NULL DEFAULT 0 COMMENT '开始时间，单位：秒'
  ,`end_time` bigint NOT NULL DEFAULT 0 COMMENT '结束时间，单位：秒'
  ,`target_pool_id` bigint NOT NULL DEFAULT 0 COMMENT '目标奖池 ID'
  ,`target_gift_id` bigint NOT NULL DEFAULT 0 COMMENT '目标礼物 ID'
  ,`collect_start_time` bigint NOT NULL DEFAULT 0 COMMENT '收集开始时间，单位：秒'
  ,`collect_end_time` bigint NOT NULL DEFAULT 0 COMMENT '收集结束时间，单位：秒'
  ,`buff_start_time` bigint NOT NULL DEFAULT 0 COMMENT 'buff 开始时间，单位：秒'
  ,`buff_end_time` bigint NOT NULL DEFAULT 0 COMMENT 'buff 结束时间，单位：秒'
  ,`more` json DEFAULT NULL COMMENT '更多信息'
  ,INDEX `idx_modifiedtime` (`modified_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='直播间魔方狂欢时刻配置表';

-- GRANT INSERT, UPDATE, SELECT ON live_fans_box_task TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_fans_box_task` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间，单位：秒'
  ,`bizdate` date NOT NULL COMMENT '业务日期（格式：2025-06-16）'
  ,`room_id` bigint NOT NULL COMMENT '直播间 ID'
  ,`fans_count` bigint NOT NULL DEFAULT 0 COMMENT '当前宝箱任务的粉丝数量（当天零点前加入粉丝团的有效粉丝数量）'
  ,`level` int NOT NULL DEFAULT 0 COMMENT '宝箱等级（同 live_fans_box 表 level）'
  ,`energy` int NOT NULL DEFAULT 0 COMMENT '当日的能量值'
  ,`status` tinyint NOT NULL DEFAULT 0 COMMENT '状态；1：未完成，2：已完成'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='粉丝团宝箱任务表';

CREATE UNIQUE INDEX uk_bizdate_roomid ON live_fans_box_task(`bizdate`,`room_id`);

-- GRANT INSERT, UPDATE, SELECT ON live_fans_box_user_task TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_fans_box_user_task` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间，单位：秒'
  ,`room_id` bigint NOT NULL COMMENT '直播间 ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`fans_box_task_id` bigint NOT NULL COMMENT '粉丝团宝箱任务 ID（同 live_fans_box_task 表 id）'
  ,`user_energy` int NOT NULL COMMENT '用户当日贡献的能量值'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='粉丝团宝箱用户任务表';

CREATE UNIQUE INDEX uk_userid_fansboxtaskid ON live_fans_box_user_task(`user_id`,`fans_box_task_id`);

-- GRANT SELECT ON live_fans_box TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_fans_box` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间，单位：秒'
  ,`level` int NOT NULL DEFAULT 0 COMMENT '宝箱等级'
  ,`fans_count` bigint NOT NULL DEFAULT 0 COMMENT '达到该等级需要的粉丝数量'
  ,`name` varchar(100) NOT NULL DEFAULT '' COMMENT '宝箱名称'
  ,`icon` varchar(255) NOT NULL DEFAULT '' COMMENT '宝箱图片'
  ,`energy` int NOT NULL DEFAULT 0 COMMENT '解锁宝箱所需能量值'
  ,`more` json DEFAULT NULL COMMENT '更多信息（宝箱奖品、奖品权重、每日库存等）'
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='粉丝团宝箱表';

CREATE UNIQUE INDEX uk_level ON live_fans_box(`level`);

INSERT INTO `live_fans_box`
  (`id`, `create_time`, `modified_time`, `level`, `fans_count`, `name`, `icon`, `energy`, `more`)
VALUES
  -- TestFindTodayTask、TestActionFansBoxDraw、FindOrCreate
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 0, '粉丝团宝箱 1', 'oss://test/box1.png', 650, '{"rewards":[{"weight":500000,"daily_stock":690,"type":3,"prize_ids":[10,11]},{"weight":500000,"daily_stock":10000,"type":1,"prize_ids":[12]}]}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2, 11, '粉丝团宝箱 2', 'oss://test/box2.png', 1200, '{"rewards":[{"weight":500000,"daily_stock":100,"type":4,"prize_ids":[13]}]}')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 3, 21, '粉丝团宝箱 3', 'oss://test/box3.png', 2500, '{"rewards":[{"weight":500000,"daily_stock":690,"type":3,"prize_ids":[10,11]},{"weight":500000,"daily_stock":10000,"type":1,"prize_ids":[12]}]}')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 4, 31, '粉丝团宝箱 4', 'oss://test/box4.png', 5000, '{"rewards":[{"weight":500000,"daily_stock":690,"type":3,"prize_ids":[10,11]},{"weight":500000,"daily_stock":10000,"type":1,"prize_ids":[12]}]}')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 5, 51, '粉丝团宝箱 5', 'oss://test/box5.png', 13500, '{"rewards":[{"weight":500000,"daily_stock":690,"type":3,"prize_ids":[10,11]},{"weight":500000,"daily_stock":10000,"type":1,"prize_ids":[12]}]}')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, 101, '粉丝团宝箱 6', 'oss://test/box6.png', 45000, '{"rewards":[{"weight":500000,"daily_stock":690,"type":3,"prize_ids":[10,11]},{"weight":500000,"daily_stock":10000,"type":1,"prize_ids":[12]}]}')
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 7, 501, '粉丝团宝箱 7', 'oss://test/box7.png', 125000, '{"rewards":[{"weight":500000,"daily_stock":690,"type":3,"prize_ids":[10,11]},{"weight":500000,"daily_stock":10000,"type":1,"prize_ids":[12]}]}')
;

-- GRANT INSERT, SELECT ON live_fans_box_reward_log TO 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `live_fans_box_reward_log` (
  `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT 0 COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL DEFAULT 0 COMMENT '修改时间'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`room_id` bigint NOT NULL COMMENT '直播间 ID'
  ,`level` int NOT NULL DEFAULT 0 COMMENT '宝箱等级（同 live_fans_box 表 level）'
  ,`fans_box_task_id` bigint NOT NULL COMMENT '粉丝团宝箱任务 ID'
  ,`fans_box_user_task_id` bigint NOT NULL COMMENT '粉丝团宝箱用户任务 ID'
  ,`more` json NOT NULL COMMENT '额外信息（领取的礼物信息等）'
) DEFAULT CHARSET=utf8mb4 COMMENT='粉丝团宝箱领取日志表';

CREATE UNIQUE INDEX uk_fansboxusertaskid ON live_fans_box_reward_log(`fans_box_user_task_id`);
