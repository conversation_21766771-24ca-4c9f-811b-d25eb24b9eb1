-- GRANT SELECT, INSERT, UPDATE ON live_txn_order TO `live_service`@`%`
CREATE TABLE IF NOT EXISTS `live_txn_order` (
    `id` bigint NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`create_time` bigint NOT NULL COMMENT '创建时间'
    ,`modified_time` bigint NOT NULL COMMENT '修改时间'
    ,`expire_time` bigint NOT NULL DEFAULT '0' COMMENT '订单到期时间'
    ,`title` varchar(255) NOT NULL COMMENT '订单标题'
    ,`price` bigint NOT NULL COMMENT '订单价格（单位：钻石）'
    ,`status` tinyint NOT NULL COMMENT '订单状态（同 app_missevan.transaction_log.status）'
    ,`tid` bigint NOT NULL DEFAULT '0' COMMENT '对应 app_missevan.transaction_log.id'
    ,`goods_id` bigint NOT NULL COMMENT '商品 ID'
    ,`goods_type` tinyint unsigned NOT NULL COMMENT '商品类型，1：超粉'
    ,`attr` tinyint NOT NULL DEFAULT '0' COMMENT '属性'
    ,`buyer_id` bigint NOT NULL COMMENT '购买者用户 ID'
    ,`seller_id` bigint NOT NULL DEFAULT '0' COMMENT '出售者用户 ID'
    ,`more` text NOT NULL DEFAULT '' COMMENT '额外信息'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='直播订单表'
;

CREATE TABLE IF NOT EXISTS `guild_live_contract` (
  `id`                 int unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`guild_id`          int unsigned NOT NULL COMMENT '公会 ID'
  ,`guild_owner`       int unsigned NOT NULL COMMENT '公会会长 ID'
  ,`guild_name`        varchar(50)  NOT NULL COMMENT '公会名称'
  ,`live_id`           int unsigned NOT NULL COMMENT '主播 ID'
  ,`contract_duration` int unsigned NOT NULL DEFAULT '0' COMMENT '签约时限'
  ,`contract_start`    int unsigned NOT NULL COMMENT '合约起始时间'
  ,`contract_end`      int unsigned NOT NULL COMMENT '合约结束时间'
  ,`rate`              int unsigned NOT NULL COMMENT '主播所占分成比例'
  ,`kpi`               varchar(255) NOT NULL COMMENT '指标'
  ,`type`              tinyint      NOT NULL COMMENT '签约类型（1 主播发起，2 公会发起）'
  ,`attr`              int unsigned NOT NULL DEFAULT '0' COMMENT '按位运算：第 1 位表示主播在合约期内申请过协商解约；第 2 位表示主播有未处理的降薪申请'
  ,`status`            int          NOT NULL COMMENT '合约状态，-3：已解约；-2：已失效；-1：已拒绝；0：申请/邀约中；1：合约生效中（主播申请入会通过或公会邀约通过）； 2：合约生效中（主播申请退会或公会发起解约）'
  ,`create_time`       int unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time`     int unsigned NOT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

INSERT INTO `guild_live_contract`
  (`id`, `guild_id`, `live_id`, `contract_start`, `contract_end`, `contract_duration`, `rate`, `kpi`, `status`, `type`, `guild_owner`, `guild_name`, `create_time`, `modified_time`)
VALUES
  -- TestRecommendScheduleApplyParamCheck
  (20210701, 3, 12, 1569378503, 1999999999, 0, 0, '', 1, 1, 12, '测试公会（匆删）', 1569378503, 1569378503)
  -- TestApplySearch
  , (24333476, 3, 11, 1565002308, 1999999999, 0, 0, '', 1, 1, 12, '测试公会（匆删）', 1569378503, 1569378503)
;

CREATE TABLE IF NOT EXISTS `transaction_log` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
    ,`from_id` int(11) NOT NULL
    ,`to_id` int(11) NOT NULL
    ,`c_time` int(11) NOT NULL
    ,`gift_id` int(11) NOT NULL COMMENT '0 为知识问答 正整数为正常礼物'
    ,`title` varchar(255) DEFAULT NULL
    ,`ios_coin` int(11) DEFAULT '0'
    ,`android_coin` int(11) DEFAULT '0'
    ,`paypal_coin` int(11) DEFAULT '0'
    ,`tmallios_coin` int(11) NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收入（单元：钻）'
    ,`googlepay_coin` int(11) NOT NULL DEFAULT '0' COMMENT 'Google Pay 收入（单元：钻）'
    ,`all_coin` int(11) NOT NULL DEFAULT '0' COMMENT '钻石总和'
    ,`revenue` double NOT NULL DEFAULT '0' COMMENT '分成后收益'
    ,`income` double DEFAULT NULL
    ,`tax` double DEFAULT NULL
    ,`rate` double DEFAULT NULL
    ,`num` int(11) NOT NULL DEFAULT '1' COMMENT '直播时购买礼物数量；购买语音包时存储季度'
    ,`status` tinyint(4) DEFAULT '0' COMMENT '-3 现金退款\n-2 是取消\n-1 是未完成\n1 是成功'
    ,`type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1：直播间礼物；2：剧集单集购买；3：剧集购买；4：微信男友购买；5：全职抽卡；6：全职季包；7：剧集打赏；8: 求签；9：公会直播收益；'
    ,`suborders_num` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '购买剧集单集时存储子订单数量（本次购买的单集数）；购买语音包时存储作品 ID'
    ,`attr` int(11) NOT NULL DEFAULT '0' COMMENT 'type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物。\ntype 为 2 或 3 时，attr 为 1 表示特殊途径购买。\ntype 为 3 时，attr 为 2 表示剧集兑换。'
    ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
    ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '最后修改时间'
    ,`confirm_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '交易确认时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

INSERT INTO `transaction_log`
  (id, from_id, to_id, c_time, gift_id, title, ios_coin, android_coin, paypal_coin, tmallios_coin, googlepay_coin, all_coin, revenue, income, tax, rate, num, status, type, suborders_num, attr, create_time, modified_time, confirm_time)
VALUES
  -- TestGetTransactionsByTime; TestFixLiveGift
  (3, 12345, 10, 0, 10154, "礼物 10154", 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 2, 1, 1, 0, 0, 0, 0, 0)
  ,(4, 12345, 10, 0, 10154, "礼物 10154", 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1698249500)
  ,(5, 12345, 10, 0, 10155, "礼物 10155", 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 1698249500)
  -- TestActionLiveGetSpend; TestGetUsersLiveSpend
  ,(6, 12346, 10, 0, 10155, "", 1, 1, 1, 1, 2, 20007, 0, 0, 0, 0, 1, 1, 9, 0, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(7, 12347, 10, 0, 10155, "", 2, 3, 4, 5, 6, 20009, 0, 0, 0, 0, 1, 1, 1, 0, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(8, 12347, 1, 0, 10155, "", 2, 3, 4, 5, 6, 20009, 0, 2, 1, 0, 1, 1, 9, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1741940605)
  ,(9, 12347, 2, 0, 10155, "", 2, 3, 4, 5, 6, 20009, 0, 3, 1, 0, 1, 1, 9, 2, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1741854205)
;
