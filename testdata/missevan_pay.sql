CREATE TABLE IF NOT EXISTS `account_info` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id` int(10) unsigned NOT NULL COMMENT '用户M号'
  ,`real_name` varchar(20) NOT NULL COMMENT '真实姓名'
  ,`account` varchar(255) DEFAULT NULL COMMENT '提现账号'
  ,`mobile` varchar(255) NOT NULL COMMENT '手机号'
  ,`id_number` varchar(44) NOT NULL COMMENT '身份证号'
  ,`bank` varchar(255) NOT NULL COMMENT '开户银行'
  ,`bank_branch` varchar(255) NOT NULL COMMENT '支行信息'
  ,`bank_account` varchar(44) NOT NULL COMMENT '银行账号'
  ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int(10) unsigned DEFAULT NULL COMMENT '修改时间'
  ,`type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '0 为支付宝账户, 1 银行卡账户'
  ,`status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '记录状态'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

CREATE TABLE IF NOT EXISTS `withdrawal_record` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY
  ,`user_id` int(10) unsigned NOT NULL COMMENT '用户 M 号'
  ,`account_id` int(10) unsigned NOT NULL COMMENT '账户 ID'
  ,`profit` double unsigned NOT NULL COMMENT '兑换金额'
  ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
  ,`status` tinyint(3) NOT NULL DEFAULT 1 COMMENT '申请状态 1 为申请中， 2 为确认打款，3 为拒绝打款'
  ,`type` tinyint(3) NOT NULL DEFAULT 1 COMMENT '提现的收益类型'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

CREATE TABLE IF NOT EXISTS `balance` (
  `id` int(11) NOT NULL
  ,`ios` int(11) unsigned DEFAULT '0' COMMENT 'iOS 充值余额'
  ,`android` int(11) unsigned DEFAULT '0' COMMENT 'Android 充值余额'
  ,`paypal` int(11) unsigned DEFAULT '0' COMMENT 'PayPal 余额'
  ,`tmallios` int(11) NOT NULL DEFAULT '0' COMMENT '天猫 iOS 充值余额（单位：钻）'
  ,`googlepay` int(11) NOT NULL DEFAULT '0' COMMENT 'Google Pay 充值余额（单位：钻）'
  ,`in_ios` int(11) unsigned DEFAULT '0' COMMENT 'iOS 收益余额'
  ,`in_android` int(11) unsigned DEFAULT '0' COMMENT 'Android 收益余额'
  ,`in_paypal` int(11) unsigned DEFAULT '0' COMMENT 'PayPal 收入'
  ,`in_tmallios` int(11) NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收益余额（单位：钻）'
  ,`in_googlepay` int(11) NOT NULL DEFAULT '0' COMMENT 'Google Pay 收益余额（单位：钻）'
  ,`new_all_live_profit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人总收益（2020 年 6 月 1 日后）单位 ：分'
  ,`new_live_profit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人可提现收益（2020 年 6 月 1 日后）单位 ：分'
  ,`all_live_profit` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人总收益（2020 年 6 月 1 日前）单位 ：分'
  ,`live_profit` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人可提现收益（2020 年 6 月 1 日前）单位 ：分'
  ,`profit` double unsigned DEFAULT '0' COMMENT '总收益余额（可提现）'
  ,`drama_reward_profit` int(11) unsigned DEFAULT '0' COMMENT '剧集打赏总收益余额'
  ,`drama_buy_profit` int(11) unsigned DEFAULT '0' COMMENT '剧集购买总收益余额'
  ,`other_profit` int(11) unsigned DEFAULT '0' COMMENT '其他总收益余额'
  ,`all_drama_buy_profit` int(11) DEFAULT '0' COMMENT '剧集累计购买收益'
  ,`all_drama_reward_profit` int(11) unsigned DEFAULT '0' COMMENT '剧集累计打赏收益'
  ,`all_other_profit` int(11) unsigned DEFAULT '0' COMMENT '其他累计收益'
  ,`all_consumption` bigint(20) DEFAULT '0' COMMENT '用户总消费（单位：钻），不包含贵族钻石消费'
  ,`all_topup` bigint(20) NOT NULL DEFAULT '0' COMMENT '总充值额（单位：普通钻石）'
  ,`all_coin` int(11) NOT NULL DEFAULT '0' COMMENT '普通钻石余额（单位：钻）'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8
;

-- GRANT SELECT ON missevan_pay.transaction_log FROM 'live_service'@'%';
CREATE TABLE IF NOT EXISTS `transaction_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`from_id` int(11) NOT NULL
  ,`to_id` int(11) NOT NULL
  ,`c_time` int(11) NOT NULL
  ,`gift_id` int(11) NOT NULL COMMENT '0 为知识问答 正整数为正常礼物'
  ,`title` varchar(255) DEFAULT NULL
  ,`ios_coin` int(11) DEFAULT '0'
  ,`android_coin` int(11) DEFAULT '0'
  ,`paypal_coin` int(11) DEFAULT '0'
  ,`tmallios_coin` int(11) NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收入（单元：钻）'
  ,`googlepay_coin` int(11) NOT NULL DEFAULT '0' COMMENT 'Google Pay 收入（单元：钻）'
  ,`all_coin` int(11) NOT NULL DEFAULT '0' COMMENT '钻石总和'
  ,`revenue` double NOT NULL DEFAULT '0' COMMENT '分成后收益'
  ,`income` double DEFAULT NULL
  ,`tax` double DEFAULT NULL
  ,`rate` double DEFAULT NULL
  ,`num` int(11) NOT NULL DEFAULT '1' COMMENT '直播时购买礼物数量；购买语音包时存储季度'
  ,`status` tinyint(4) DEFAULT '0' COMMENT '-3 现金退款\n-2 是取消\n-1 是未完成\n1 是成功'
  ,`type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1：直播间礼物；2：剧集单集购买；3：剧集购买；4：微信男友购买；5：全职抽卡；6：全职季包；7：剧集打赏；8: 求签；9：公会直播收益；'
  ,`suborders_num` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '购买剧集单集时存储子订单数量（本次购买的单集数）；购买语音包时存储作品 ID'
  ,`attr` int(11) NOT NULL DEFAULT '0' COMMENT 'type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物。\ntype 为 2 或 3 时，attr 为 1 表示特殊途径购买。\ntype 为 3 时，attr 为 2 表示剧集兑换。'
  ,`create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '最后修改时间'
  ,`confirm_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '交易确认时间'
  ,INDEX `idx_toid_type_status_confirmtime` (`to_id`, `type`, `status`, `confirm_time`)
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
;

INSERT INTO `transaction_log`
  (id, from_id, to_id, c_time, gift_id, title, ios_coin, android_coin, paypal_coin, tmallios_coin, googlepay_coin, all_coin, revenue, income, tax, rate, num, status, type, suborders_num, attr, create_time, modified_time, confirm_time)
VALUES
  -- TestGetDataByLastID
  (1, 0, 0, 0, 0, "许愿池", 0, 0, 0, 0, 0, 10, 0, 0, 0, 0, 1, 1, 1, 0, 0, 0, 0, 0)
  ,(2, 0, 0, 0, 0, "许愿池", 0, 0, 0, 0, 0, 100, 0, 0, 0, 0, 10, 1, 1, 0, 0, 0, 0, 0)
;

CREATE TABLE IF NOT EXISTS `guild_live_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键'
  ,`live_id` int(10) unsigned NOT NULL COMMENT '主播 ID'
  ,`guild_id` int(10) unsigned NOT NULL COMMENT '公会 ID'
  ,`status` tinyint(4) NOT NULL COMMENT '状态（1 成功，0 创建，-1 取消，-2 错误）'
  ,`type` tinyint(4) NOT NULL COMMENT '平台（同 recharge_order.type 字段）'
  ,`price` bigint(20) NOT NULL COMMENT '金额（分）'
  ,`tid` varchar(32) NOT NULL DEFAULT '' COMMENT '平台订单号'
  ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
  ,`applyment_id` int(10) unsigned NOT NULL COMMENT '合约申请 ID'
) -- ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公会与主播订单表'
;

-- GRANT SELECT, INSERT, UPDATE ON `missevan_pay`.`guild_balance` TO 'live_service'@'%'
CREATE TABLE IF NOT EXISTS `guild_balance` (
  `id`               int unsigned    NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '主键，公会 ID'
  ,`in_ios`          int unsigned    NOT NULL DEFAULT '0' COMMENT 'iOS 收益余额'
  ,`in_android`      int unsigned    NOT NULL DEFAULT '0' COMMENT 'Android 收益余额'
  ,`in_paypal`       int unsigned    NOT NULL DEFAULT '0' COMMENT 'PayPal 收益余额'
  ,`in_googlepay`    int             NOT NULL DEFAULT '0' COMMENT 'Google Pay 收益余额（单位：钻）'
  ,`in_tmallios`     int             NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收益余额（单位：钻）'
  ,`live_profit`     double unsigned NOT NULL DEFAULT '0' COMMENT '直播总收益余额（可提现）'
  ,`all_live_profit` double unsigned NOT NULL DEFAULT '0' COMMENT '直播累计收益'
  ,`rate`            double unsigned NOT NULL DEFAULT '0.5' COMMENT '公会分成比例'
  ,`create_time`     int unsigned    NOT NULL COMMENT '创建时间'
  ,`modified_time`   int unsigned    NOT NULL COMMENT '修改时间'
)
;

INSERT INTO `guild_balance`
  (`id`, `in_ios`, `in_android`, `in_paypal`, `in_googlepay`, `in_tmallios`, `live_profit`, `all_live_profit`, `rate`, `create_time`, `modified_time`)
VALUES
  (3, 0, 0, 0, 0, 0, 0, 0, 0.6, UNIX_TIMESTAMP() - 60, UNIX_TIMESTAMP() - 60)
  ,(6, 0, 0, 0, 0, 0, 0, 0, 0.4, UNIX_TIMESTAMP() - 60, UNIX_TIMESTAMP() - 60)
  ,(7, 0, 0, 0, 0, 0, 0, 0, 0.4, UNIX_TIMESTAMP() - 60, UNIX_TIMESTAMP() - 60)
  -- TestAdminManageParam_fillGuildRate
  ,(8, 0, 0, 0, 0, 0, 0, 0, 0.6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;
