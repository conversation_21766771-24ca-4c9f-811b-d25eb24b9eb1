FROM golang:1.15-alpine3.12

ARG version
ARG proxy
ARG mgobranch

# Download packages from ali<PERSON> mirrors
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk --update add --no-cache ca-certificates tzdata git

# build bat for healthcheck
#RUN http_proxy=$proxy https_proxy=$proxy go get -d -v github.com/astaxie/bat \
#  && cd /go/src/github.com/astaxie/bat && GOOS=linux CGO_ENABLED=0 go install

COPY . /go/src/github.com/MiaoSiLa/live-service
RUN cd /go/src/github.com/MiaoSiLa/live-service \
  && chmod +x gomod.sh && ./gomod.sh \
  && GO111MODULE=on GOPROXY=https://goproxy.io,direct GOOS=linux CGO_ENABLED=0 go install -v -tags release -ldflags "-X main.Version=$version"

FROM scratch

COPY --from=0 /usr/share/zoneinfo /usr/share/zoneinfo
#COPY --from=0 /usr/share/ca-certificates /usr/share/ca-certificates
COPY --from=0 /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=0 /etc/passwd /etc/passwd
COPY --from=0 /go/bin/live-service /bin/

WORKDIR /

EXPOSE 3015
USER nobody

#HEALTHCHECK --interval=30s --timeout=2s --start-period=5s \
#  CMD ["/bin/bat", "-print=", "-pretty=false", "http://localhost:3032/"]

ENTRYPOINT ["/bin/live-service"]
