package luavm

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	luautil "github.com/MiaoSiLa/live-service/luavm/util"
)

func TestMain(m *testing.M) {
	InitTest()
	m.Run()
}

func TestPoolGetPut(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	l := Pool.Get()
	require.NotNil(l)

	err := l.DoString(`
json = require('json')
return json.decode('{"a": 1}'), json.encode({a = 1})
	`)
	require.NoError(err)
	d := luautil.GetValue(l, l.Get(-2))
	e := luautil.GetValue(l, l.Get(-1))
	assert.Equal(map[string]interface{}{"a": 1}, d)
	assert.Equal(`{"a":1}`, e)
	luautil.CleanStack(l) // stack is now clean
	Pool.Put(l)

	l2 := Pool.Get()
	require.NotNil(l2)
	assert.Equal(l, l2) // reused same object in pool
	Pool.Put(l2)
}

func TestPoolReset(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	require.NotPanics(func() {
		Pool.Reset("")
		assert.Equal("", Pool.InitScript)
	})
}

func TestPoolShutdown(t *testing.T) {
	require := require.New(t)
	require.NotPanics(func() {
		Pool.Shutdown()
	})
}
