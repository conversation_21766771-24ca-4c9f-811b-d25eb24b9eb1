package glualogger

import (
	lua "github.com/yuin/gopher-lua"

	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// TODO: logger context file set to lua file

func logDebug(l *lua.LState) int {
	v := luautil.GetArgs(l, 1)
	logger.Debug(v...)
	return 0
}

func logDebugf(l *lua.LState) int {
	format := l.CheckString(1)
	v := luautil.GetArgs(l, 2)
	logger.Debugf(format, v...)
	return 0
}

func logInfo(l *lua.LState) int {
	v := luautil.GetArgs(l, 1)
	logger.Info(v...)
	return 0
}

func logInfof(l *lua.LState) int {
	format := l.CheckString(1)
	v := luautil.GetArgs(l, 2)
	logger.Infof(format, v...)
	return 0
}

func logWarn(l *lua.LState) int {
	v := luautil.GetArgs(l, 1)
	logger.Warn(v...)
	return 0
}

func logWarnf(l *lua.LState) int {
	format := l.CheckString(1)
	v := luautil.GetArgs(l, 2)
	logger.Warnf(format, v...)
	return 0
}

func logError(l *lua.LState) int {
	v := luautil.GetArgs(l, 1)
	logger.Error(v...)
	return 0
}

func logErrorf(l *lua.LState) int {
	format := l.CheckString(1)
	v := luautil.GetArgs(l, 2)
	logger.Errorf(format, v...)
	return 0
}
