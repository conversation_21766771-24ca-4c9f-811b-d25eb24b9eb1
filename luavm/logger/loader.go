package glualogger

import (
	lua "github.com/yuin/gopher-lua"
)

var exports = map[string]lua.LGFunction{
	"debug":  logDebug,
	"debugf": logDebugf,
	"info":   logInfo,
	"infof":  logInfof,
	"warn":   logWarn,
	"warnf":  logWarnf,
	"error":  logError,
	"errorf": logErrorf,
}

// Loader for gopher-lua
func Loader(l *lua.LState) int {
	mod := l.SetFuncs(l.NewTable(), exports)
	l.Push(mod)

	l.<PERSON>Field(mod, "_DEBUG", lua.LBool(false))
	l.<PERSON>(mod, "_VERSION", lua.LString("0.0.0"))

	return 1
}
