package glualogger

import (
	"testing"

	"github.com/stretchr/testify/require"
	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()

	m.Run()
}

func TestLogger(t *testing.T) {
	require := require.New(t)

	l := lua.NewState()
	require.NotNil(l)
	defer l.Close()
	Preload(l)

	err := l.DoString(`
logger = require('logger')

logger.debug('debug')
logger.debugf('debug%s', 'f')
logger.info('info')
logger.infof('info%s %v', 'f', {a = 1})
logger.warn('warn')
logger.warnf('warn%s', 'f')
logger.error('error')
logger.errorf('error%s', 'f')
`)
	require.NoError(err)
}
