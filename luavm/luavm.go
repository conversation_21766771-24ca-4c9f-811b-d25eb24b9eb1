package luavm

import (
	"sync"
	"time"

	"github.com/tengattack/gluacrypto"
	"github.com/tengattack/gluamongo"
	gluamongomongo "github.com/tengattack/gluamongo/mongo"
	gluajson "github.com/vadv/gopher-lua-libs/json"
	gluaregexp "github.com/vadv/gopher-lua-libs/regexp"
	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/config"
	gluactxreq "github.com/MiaoSiLa/live-service/luavm/ctxreq"
	glualogger "github.com/MiaoSiLa/live-service/luavm/logger"
	gluaredis "github.com/MiaoSiLa/live-service/luavm/redis"
	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type lGlobalConfig struct {
	KeyPrefix string `lua:"key_prefix"`
}

type lStatePool struct {
	m     sync.Mutex
	saved []*lua.LState

	InitScript string
}

// Pool Global LState pool
var Pool *lStatePool

const poolSize = 100

// Init lua pool
// TODO: check max do times
// TODO: add prometheus metrics
func init() {
	Pool = &lStatePool{
		saved: make([]*lua.LState, 0, poolSize),
	}
}

func newDB(l *lua.LState) lua.LValue {
	if service.MongoDB == nil {
		return lua.LNil
	}
	ud := l.NewUserData()
	timeout := 10 * time.Second
	if service.MongoDB.Conf.Timeout > 0 {
		timeout = service.MongoDB.Conf.Timeout
	}
	ud.Value = &gluamongomongo.Database{
		Client:   &gluamongomongo.Client{Client: service.MongoDB.Client, Timeout: timeout},
		Database: service.MongoDB.Database,
	}
	l.SetMetatable(ud, l.GetTypeMetatable(gluamongomongo.DATABASE_TYPENAME))
	return ud
}

// New single lua state
func (pl *lStatePool) New() *lua.LState {
	l := lua.NewState()
	// setting the L up here.
	// load scripts, set global variables, share channels, etc...
	gluacrypto.Preload(l)
	gluamongo.Preload(l)
	glualogger.Preload(l)
	gluajson.Preload(l)
	gluaredis.Preload(l)
	gluaregexp.Preload(l)

	gluactxreq.RegisterType(l)

	var conf lGlobalConfig
	conf.KeyPrefix = config.KeyPrefix
	l.SetGlobal("config", luautil.ToLuaValue(l, conf))
	l.SetGlobal("db", newDB(l))

	if pl.InitScript != "" {
		err := l.DoString(pl.InitScript)
		if err != nil {
			logger.Errorf("lua init error: %v", err)
			// PASS
		}
	}
	return l
}

// Get lua state from pool
func (pl *lStatePool) Get() *lua.LState {
	pl.m.Lock()
	defer pl.m.Unlock()
	n := len(pl.saved)
	if n == 0 {
		return pl.New()
	}
	x := pl.saved[n-1]
	pl.saved = pl.saved[0 : n-1]
	return x
}

// Put lua state back to pool
func (pl *lStatePool) Put(l *lua.LState) {
	// clean stack
	luautil.CleanStack(l)

	pl.m.Lock()
	defer pl.m.Unlock()
	pl.saved = append(pl.saved, l)
}

// Reset reset lua vm pool
func (pl *lStatePool) Reset(initScript string) {
	pl.m.Lock()
	defer pl.m.Unlock()
	for _, l := range pl.saved {
		l.Close()
	}
	pl.InitScript = initScript
	pl.saved = make([]*lua.LState, 0, poolSize)
}

// Shutdown close all state in pool
func (pl *lStatePool) Shutdown() {
	pl.m.Lock()
	defer pl.m.Unlock()
	for _, l := range pl.saved {
		l.Close()
	}
	pl.saved = nil
}
