package gluaredis

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/config"
	glualogger "github.com/MiaoSiLa/live-service/luavm/logger"
	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()

	m.Run()
}

func TestRedis(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	l := lua.NewState()
	require.NotNil(l)
	defer l.Close()
	glualogger.Preload(l)
	Preload(l)

	err := l.DoString(`
logger = require('logger')
redis = require('redis')

local _, err = redis.call('SET', 'foo', 'bar')
if err then
  return nil, err
end
local foo, err = redis.call('GET', 'foo')
if err then
  return nil, err
end
logger.infof('foo: %s', foo)
_, err = redis.call('DEL', 'foo')
if err then
  return nil, err
end
return foo
`)
	require.NoError(err)
	res := luautil.GetValue(l, l.Get(1))
	assert.Equal(1, l.GetTop())
	assert.Equal("bar", res)
	luautil.CleanStack(l) // clean stack

	err = l.DoString(`
logger = require('logger')
redis = require('redis')

local data, err = redis.call('NONEXISTSCMD', 'foo', 'bar')
if err then
  return nil, err
end
return data
`)
	require.NoError(err)
	assert.Equal(2, l.GetTop())
	res = luautil.GetValue(l, l.Get(1))
	errStr := luautil.GetValue(l, l.Get(2))
	assert.Nil(res)
	assert.Regexp("^ERR unknown command `NONEXISTSCMD`", errStr)
	luautil.CleanStack(l) // clean stack
}
