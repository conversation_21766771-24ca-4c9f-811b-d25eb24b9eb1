package gluaredis

import (
	"github.com/go-redis/redis/v7"
	lua "github.com/yuin/gopher-lua"

	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/live-service/service"
)

func redisCall(l *lua.LState) int {
	v := luautil.GetArgs(l, 1)
	cmd := service.Redis.Do(v...)
	err := cmd.Err()
	if err == redis.Nil {
		l.Push(lua.LNil)
		return 1
	}
	if err != nil {
		l.Push(lua.LNil)
		l.Push(lua.LString(err.Error()))
		return 2
	}
	l.Push(luautil.ToLuaValue(l, cmd.Val()))
	return 1
}
