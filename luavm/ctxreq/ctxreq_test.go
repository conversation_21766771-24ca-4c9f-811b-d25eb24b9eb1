package gluactxreq

import (
	"net/http"
	"net/url"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/config"
	glualogger "github.com/MiaoSiLa/live-service/luavm/logger"
	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestCtxReq(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := lua.NewState()
	require.NotNil(l)
	defer l.Close()
	glualogger.Preload(l)
	RegisterType(l)

	c := handler.NewTestContext("POST", "/echo?foo=1", true, strings.NewReader(`bar=1`))
	c.C.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	form := url.Values{}
	form.Set("bar", "1")
	c.C.Request.Form = form

	l.SetGlobal("req", New(l, c))
	err := l.DoString(`
logger = require('logger')

return req:get_user_id(), req:get_uri_args(), req:get_post_args(),
  req:get_body_data()
`)
	require.NoError(err)
	require.Equal(4, l.GetTop())
	res := luautil.GetValue(l, l.Get(1))
	assert.Equal(12, res)
	assert.Equal(map[string]interface{}{"foo": "1"}, luautil.GetValue(l, l.Get(2)))
	assert.Equal(map[string]interface{}{"bar": "1"}, luautil.GetValue(l, l.Get(3)))
	assert.Equal("bar=1", luautil.GetValue(l, l.Get(4)))

	luautil.CleanStack(l) // clean stack
}

func TestReqGetEventMethod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := lua.NewState()
	require.NotNil(l)
	defer l.Close()
	glualogger.Preload(l)
	RegisterType(l)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	l.SetGlobal("req", New(l, c))
	err := l.DoString(`
logger = require('logger')

return req:get_event(160)
`)
	require.NoError(err)

	require.NoError(err)
	require.Equal(2, l.GetTop())
	event, err := mevent.FindSimple(160)
	require.NoError(err)
	assert.JSONEq(tutil.SprintJSON(event), tutil.SprintJSON(luautil.GetValue(l, l.Get(1))))
	assert.JSONEq(event.ExtendedFields, tutil.SprintJSON(luautil.GetValue(l, l.Get(2))))
	luautil.CleanStack(l)

	l.SetGlobal("req", New(l, c))
	err = l.DoString(`
logger = require('logger')

return req:get_event(999)
`)
	require.NoError(err)
	require.Equal(2, l.GetTop())
	assert.Equal(lua.LNil, l.Get(1))
	assert.Equal(lua.LNil, l.Get(2))
	luautil.CleanStack(l)
}
