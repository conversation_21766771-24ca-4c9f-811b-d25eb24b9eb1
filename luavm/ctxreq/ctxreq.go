package gluactxreq

import (
	"encoding/json"
	"io"
	"net/http"

	lua "github.com/yuin/gopher-lua"

	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
)

// type names
const (
	CTX_REQ_TYPENAME = "ctx{req}"
)

// CtxReq handler.Context in luavm
type CtxReq struct {
	c    *handler.Context
	body []byte
}

func (req *CtxReq) getBodyData() []byte {
	if req.c.C.Request.Method == http.MethodPost {
		if req.body == nil && req.c.C.Request.Body != nil {
			defer req.c.C.Request.Body.Close()
			body, err := io.ReadAll(req.c.C.Request.Body)
			if err != nil {
				logger.Error(err)
				// PASS
				body = make([]byte, 0)
			}
			req.body = body
		}
		return req.body
	}
	return nil
}

// New ctx req for handler.Context access in luavm
func New(l *lua.LState, c *handler.Context) lua.LValue {
	ud := l.NewUserData()
	ud.Value = &CtxReq{c: c}
	l.SetMetatable(ud, l.GetTypeMetatable(CTX_REQ_TYPENAME))
	return ud
}

func checkCtxReq(l *lua.LState) *CtxReq {
	ud := l.CheckUserData(1)
	if v, ok := ud.Value.(*CtxReq); ok {
		return v
	}
	l.ArgError(1, "ctx req expected")
	return nil
}

func reqGetUserIDMethod(l *lua.LState) int {
	req := checkCtxReq(l)

	l.Push(lua.LNumber(req.c.UserID()))
	return 1
}

func reqGetURIArgsMethod(l *lua.LState) int {
	req := checkCtxReq(l)

	query := req.c.C.Request.URL.Query()
	args := make(map[string]string, len(query))
	for k := range query {
		// TODO: multiple values
		args[k] = query.Get(k)
	}

	l.Push(luautil.ToLuaValue(l, args))
	return 1
}

func reqGetPostArgsMethod(l *lua.LState) int {
	req := checkCtxReq(l)

	form := req.c.C.Request.Form
	args := make(map[string]string, len(form))
	for k := range form {
		// TODO: multiple values
		args[k] = form.Get(k)
	}

	l.Push(luautil.ToLuaValue(l, args))
	return 1
}

func reqGetBodyDataMethod(l *lua.LState) int {
	req := checkCtxReq(l)

	body := req.getBodyData()
	if body == nil {
		l.Push(lua.LNil)
	} else {
		l.Push(lua.LString(body))
	}
	return 1
}

// TODO: 待移动到数据库包下
func reqGetEventMethod(l *lua.LState) int {
	eventID := l.ToInt64(2)
	var extendedFields map[string]interface{}
	event, err := mevent.FindSimpleWithExtendedFields(eventID, &extendedFields)
	if err != nil {
		l.Push(lua.LNil)
		l.Push(lua.LNil)
		l.Push(lua.LString(err.Error()))
		return 3
	}
	if event == nil {
		l.Push(lua.LNil)
		l.Push(lua.LNil)
		return 2
	}
	s, err := json.Marshal(event)
	if err != nil {
		l.Push(lua.LNil)
		l.Push(lua.LNil)
		l.Push(lua.LString(err.Error()))
		return 3
	}
	var m map[string]interface{}
	err = json.Unmarshal(s, &m)
	if err != nil {
		l.Push(lua.LNil)
		l.Push(lua.LNil)
		l.Push(lua.LString(err.Error()))
		return 3
	}
	l.Push(luautil.ToLuaValue(l, m))
	l.Push(luautil.ToLuaValue(l, extendedFields))
	return 2
}
