package gluactxreq

import (
	lua "github.com/yuin/gopher-lua"
)

var methods = map[string]lua.LGFunction{
	"get_user_id":   reqGetUserIDMethod,
	"get_uri_args":  reqGetURIArgsMethod,
	"get_post_args": reqGetPostArgsMethod,
	// TODO: get_headers,
	"get_body_data": reqGetBodyDataMethod,
	"get_event":     reqGetEventMethod,
}

// RegisterType for gopher-lua
func RegisterType(l *lua.LState) {
	mt := l.NewTypeMetatable(CTX_REQ_TYPENAME)
	l.<PERSON>(mt, "__index", l.SetFuncs(l.NewTable(), methods))
}
