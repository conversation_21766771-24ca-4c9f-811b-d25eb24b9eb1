// +build !release

package luavm

import (
	"github.com/MiaoSiLa/live-service/config"
)

// InitTest init for test
func InitTest() {
	config.Conf.Lua.InitScript = `
local logger = require('logger')
local crypto = require('crypto')
local json = require('json')
local redis = require('redis')
local regexp = require('regexp')
local mongo = require('mongo')

function run_action(ctx)
  logger.debug(config)
  logger.debug(ctx.params)
  logger.debug(ctx.var)
  return 'success'
end
`
}
