package live

import (
	"fmt"
	"math/rand"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TableName table name
func TableName() string {
	return "live"
}

// status 状态
const (
	StatusUserDeleted = iota - 1
	StatusClose
	StatusOpen
)

// Live 直播用户关联表
type Live struct {
	ID            int64  `gorm:"column:id;primary_key" json:"id"`
	RoomID        int64  `gorm:"column:room_id" json:"room_id"`
	CatalogID     int64  `gorm:"column:catalog_id" json:"catalog_id"`
	Title         string `gorm:"column:title" json:"title"`
	Intro         string `gorm:"column:intro" json:"intro"`
	Cover         string `gorm:"column:cover" json:"cover"`
	Status        int    `gorm:"column:status" json:"status"`
	ContractID    int64  `gorm:"column:contract_id;default:1" json:"contract_id"`
	LiveStartTime int64  `gorm:"column:live_start_time" json:"live_start_time"`
	CreateTime    int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime  int64  `gorm:"column:modified_time" json:"-"`
	UserID        int64  `gorm:"column:user_id" json:"user_id"`
	// WORKAROUND: 使用指针防止热度为 0 时 save 会失败
	Score    *int64 `gorm:"column:score;default" json:"-"`
	Username string `gorm:"column:username" json:"-"`
}

// WithFansNum 主播信息带粉丝数
type WithFansNum struct {
	UserID  int64  `gorm:"column:user_id" json:"user_id"`
	RoomID  int64  `gorm:"column:room_id" json:"room_id"`
	Title   string `gorm:"column:title" json:"title"`
	Intro   string `gorm:"column:intro" json:"intro"`
	Status  int    `gorm:"column:status" json:"status"`
	FansNum int64  `gorm:"column:fansnum" json:"fansnum"`
}

// DB the db instance of Live model
func (l Live) DB() *gorm.DB {
	return service.DB.Table(l.TableName())
}

// TableName table name
func (Live) TableName() string {
	return TableName()
}

// SetStatusAndScore set status and score
func (l *Live) SetStatusAndScore(status int, score int64) {
	if l.Status != StatusOpen && status == StatusOpen {
		l.LiveStartTime = goutil.TimeNow().Unix()
	}
	if status == StatusClose {
		// 关闭时热度设置成 0
		l.Score = goutil.NewInt64(0)
	} else {
		l.Score = &score
	}
	l.Status = status
}

// SaveParams live 表保存参数
type SaveParams struct {
	CreatorID int64
	CatalogID int64
	Title     string
	Intro     string
	Status    int
	Score     int64
	Username  string

	RoomID int64 // TODO: 待移除
}

// Save 保存
func Save(param *SaveParams) (*Live, error) {
	now := goutil.TimeNow()
	l, err := FindByCreatorID(param.CreatorID)
	if err != nil {
		return nil, err
	}
	if l != nil {
		// 正常更新
		l.CatalogID = param.CatalogID
		l.Title = param.Title
		l.Intro = param.Intro
		l.Username = param.Username
		l.SetStatusAndScore(param.Status, param.Score)
		l.ModifiedTime = now.Unix()
		if param.RoomID != 0 && param.RoomID != l.RoomID {
			logger.Errorf("直播间 ID 变动：用户（%d）房间号从 %d 变为 %d",
				l.UserID, l.RoomID, param.RoomID)
			l.RoomID = param.RoomID
		}
		err = service.DB.Save(l).Error
		return l, err
	}
	// 找不到的情况需要插入数据
	l = &Live{
		CatalogID:    param.CatalogID,
		Title:        param.Title,
		Intro:        param.Intro,
		UserID:       param.CreatorID,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		Username:     param.Username,
	}
	l.SetStatusAndScore(param.Status, param.Score)
	// 设置房间号
	if param.RoomID != 0 {
		l.ID = param.RoomID
		l.RoomID = param.RoomID
	} else {
		// 模拟 roomID 自增的情况
		// TODO: 后续直接自增处理
		var roomID int64
		err = service.DB.Select("room_id").Table(TableName()).Order("room_id DESC").Row().Scan(&roomID)
		if err != nil {
			// NOTICE: 没有数据的错误也返回
			return nil, err
		}
		l.ID = roomID + 1 + rand.Int63n(5) // 随机增加 5 以内的一个值
		l.RoomID = l.ID
	}
	la := liveaddendum.LiveAddendum{
		Vitality:     liveaddendum.MaxVitality,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	la.SetUserID(l.UserID)
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Create(l).Error
		if err != nil {
			return err
		}
		return tx.FirstOrCreate(&la).Error
	})
	if err != nil {
		return nil, err
	}
	// 新房间添加至新人榜
	err = service.Redis.SAdd(usersrank.NovaKey(now, usersrank.NovaKeySlot(l.UserID)), l.UserID).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return l, nil
}

// DeleteByCreatorID 通过主播 id 删除数据
func DeleteByCreatorID(creatorID int64) (bool, error) {
	db := service.DB.Exec("DELETE FROM "+TableName()+" WHERE user_id = ?", creatorID)
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// ListAllAttentionRoomIDs 获取用户关注的所有直播间的 ID 列表，如果 `onlyOpen` 为 `true`，只返回当前开播的直播间。
func ListAllAttentionRoomIDs(userID int64, onlyOpen bool) ([]int64, error) {
	userIDs, err := attentionuser.ListAllAttentionUserIDs(userID)
	if err != nil {
		return nil, err
	}
	if len(userIDs) == 0 {
		return []int64{}, nil
	}

	roomIDs := make([]int64, 0, len(userIDs))
	db := service.DB.Table(TableName()).Where("user_id IN (?)", userIDs)
	if onlyOpen {
		db = db.Where("status = ?", StatusOpen)
	}
	if err := db.Pluck("room_id", &roomIDs).Error; err != nil {
		return nil, err
	}

	return roomIDs, nil
}

// FindRoomID 查询主播房间号
func FindRoomID(creatorID int64) (int64, error) {
	var l Live
	err := service.DB.Select("room_id").Find(&l, "user_id = ?", creatorID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return 0, err
	}
	return l.RoomID, nil
}

// FindLives 查询主播数据
func FindLives(userIDs []int64) ([]*Live, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	userIDs = util.Uniq(userIDs)
	var lives []*Live
	err := Live{}.DB().Select("user_id, room_id").
		Find(&lives, "user_id IN (?)", userIDs).Error

	return lives, err
}

// EnsureLiveAddendum 保证 live_addendum 表存在，不存在则插入
// true 代表该主播存在，false 代表该主播不存在
func EnsureLiveAddendum(creatorID int64) (bool, error) {
	var rec struct {
		UserID   int64 `gorm:"column:user_id"`
		LaUserID int64 `gorm:"column:la_user_id"`
	}
	err := service.DB.Select("l.user_id AS user_id, la.user_id AS la_user_id").
		Table(TableName()+" AS l").
		Joins(fmt.Sprintf("LEFT JOIN %s AS la ON la.user_id = l.user_id", liveaddendum.TableName())).
		Where("l.user_id = ?", creatorID).Find(&rec).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return false, err
	}
	if rec.LaUserID == rec.UserID {
		return true, nil
	}

	la := &liveaddendum.LiveAddendum{
		Vitality: liveaddendum.MaxVitality,
	}
	la.SetUserID(creatorID)
	err = service.DB.Save(la).Error
	return err == nil, err
}

// FindLiveByRoomID 根据 roomID 查询房间信息
func FindLiveByRoomID(roomID int64) (*Live, error) {
	var live Live
	err := service.DB.Take(&live, "room_id = ?", roomID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &live, nil
}

// FindByCreatorID 通过主播 ID 查询房间信息
func FindByCreatorID(creatorID int64) (*Live, error) {
	var live Live
	err := service.DB.Take(&live, "user_id = ?", creatorID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &live, nil
}

// FindOpenLiveByRoomIDs 根据 roomIDs 查询开播中的房间信息
func FindOpenLiveByRoomIDs(roomIDs []int64) ([]*Live, error) {
	if len(roomIDs) == 0 {
		return nil, nil
	}
	var lives []*Live
	err := service.DB.Where("status = ? AND room_id IN (?)", StatusOpen, roomIDs).Find(&lives).Error
	if err != nil {
		return nil, err
	}
	return lives, nil
}
