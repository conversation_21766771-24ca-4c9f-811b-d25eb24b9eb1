package live

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("live", TableName())
	assert.Equal("live", Live{}.TableName())
}

func TestListAllAttentionRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testListenerID := int64(100)

	testOpenCreatorID := int64(10000)
	_, err := DeleteByCreatorID(testOpenCreatorID)
	require.NoError(err)
	testRoomOpen, err := Save(&SaveParams{
		CreatorID: testOpenCreatorID,
		Status:    StatusOpen,
	})
	require.NoError(err)

	testCloseCreatorID := int64(10001)
	_, err = DeleteByCreatorID(testCloseCreatorID)
	require.NoError(err)
	testRoomClose, err := Save(&SaveParams{
		CreatorID: testCloseCreatorID,
		Status:    StatusClose,
	})
	require.NoError(err)

	err = service.DB.Delete(&attentionuser.AttentionUser{}, "user_active = ?", testListenerID).Error
	require.NoError(err)
	for _, creatorID := range []int64{testOpenCreatorID, testCloseCreatorID} {
		err = service.DB.Create(&attentionuser.AttentionUser{UserActive: testListenerID, UserPasstive: creatorID}).Error
		require.NoError(err)
	}

	testCases := []struct {
		userID   int64
		onlyOpen bool
		want     []int64
	}{
		{
			userID: 0,
			want:   []int64{},
		},
		{
			userID: -1,
			want:   []int64{},
		},
		{
			userID:   testListenerID,
			onlyOpen: true,
			want:     []int64{testRoomOpen.RoomID},
		},
		{
			userID:   testListenerID,
			onlyOpen: false,
			want:     []int64{testRoomOpen.RoomID, testRoomClose.RoomID},
		},
	}

	for _, tc := range testCases {
		roomIDs, err := ListAllAttentionRoomIDs(tc.userID, tc.onlyOpen)
		require.NoError(err)
		assert.Equal(tc.want, roomIDs)

		if tc.onlyOpen {
			var count int
			err := service.DB.Table(TableName()).Where("room_id IN (?)", roomIDs).Where("status <> ?", StatusOpen).Count(&count).Error
			assert.NoError(err)
			assert.Zero(count)
		}
	}
}

func TestFindRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID, err := FindRoomID(-100)
	assert.Zero(roomID)
	assert.NoError(err)

	userID := int64(10010)
	l, err := Save(&SaveParams{
		CreatorID: userID,
		Title:     "Test FindRoomID",
		Status:    StatusClose,
	})
	require.NoError(err)

	roomID, err = FindRoomID(l.UserID)
	require.NoError(err)
	assert.Equal(l.RoomID, roomID)
}

func TestFindLives(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	err := Live{}.DB().Exec(
		"INSERT INTO live " +
			"(id, user_id, room_id, title, status, create_time, modified_time) " +
			"VALUES " +
			"(11001, 11001, 901, 'room-1', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(12001, 11002, 902, 'room-2', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(13001, 11003, 903, 'room-3', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(14001, 11004, 904, 'room-4', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(15001, 11005, 905, 'room-5', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());",
	).Error
	require.NoError(err)

	userIDs := []int64{11001, 11002, 11003, 11004, 11005, 99887766}
	defer func() {
		require.NoError(Live{}.DB().Delete(nil, "user_id IN (?)", userIDs).Error)
	}()

	lives, err := FindLives(userIDs)
	require.NoError(err)
	require.Len(lives, 5)
	for _, lv := range lives {
		assert.Greater(lv.RoomID, int64(0))
		assert.Greater(lv.UserID, int64(0))
	}
}

func TestSaveAndDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(123)
	require.NoError(service.DB.Table(liveaddendum.TableName()).
		Delete("", "user_id = ?", userID).Error)

	l, err := Save(&SaveParams{
		CreatorID: userID,
		CatalogID: 1,
		Title:     "title",
		Intro:     "intro",
		Status:    StatusOpen,
		Username:  "test",
	})
	require.NoError(err)
	assert.Equal(int64(1), l.ContractID)
	var after Live
	err = service.DB.Where("user_id = ?", l.UserID).Take(&after).Error
	require.NoError(err)

	assert.Equal(l.ID, after.ID)
	assert.Equal(l.UserID, after.UserID)
	assert.Equal(l.RoomID, after.RoomID)
	assert.NotZero(l.RoomID)
	assert.Equal(l.CatalogID, after.CatalogID)
	assert.Equal(l.Title, after.Title)
	assert.Equal(l.Intro, after.Intro)
	assert.Equal(l.Status, after.Status)
	assert.Equal(goutil.NewInt64(0), after.Score)
	assert.Equal("test", after.Username)

	assert.NotZero(l.LiveStartTime)
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
	assert.NotZero(after.LiveStartTime)
	assert.NotZero(after.CreateTime)
	assert.NotZero(after.ModifiedTime)

	ok, err := usersrank.IsNova(l.UserID)
	require.NoError(err)
	assert.True(ok)

	var count int64
	require.NoError(service.DB.Table(liveaddendum.TableName()).Where("user_id = ?", l.UserID).Count(&count).Error)
	assert.Equal(int64(1), count)

	require.NoError(service.DB.Model(&l).Update("score", 123).Error)

	l, err = Save(&SaveParams{
		CreatorID: userID,
		CatalogID: 1,
		Title:     "title",
		Intro:     "intro",
		Status:    StatusClose,
		RoomID:    321,
	})
	require.NoError(err)
	err = service.DB.Take(&after, "user_id = ?", l.UserID).Error
	require.NoError(err)

	assert.Equal(l.ID, after.ID)
	assert.Equal(l.UserID, after.UserID)
	assert.Equal(l.RoomID, after.RoomID)
	assert.Equal(l.CatalogID, after.CatalogID)
	assert.Equal(l.Title, after.Title)
	assert.Equal(l.Intro, after.Intro)
	assert.Equal(l.Status, after.Status)
	assert.Equal(goutil.NewInt64(0), after.Score)

	assert.NotZero(l.LiveStartTime)
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
	assert.NotZero(after.LiveStartTime)
	assert.NotZero(after.CreateTime)
	assert.NotZero(after.ModifiedTime)

	ok, err = DeleteByCreatorID(l.UserID)
	require.NoError(err)
	assert.True(ok)

	ok, err = DeleteByCreatorID(l.UserID)
	require.NoError(err)
	assert.False(ok)
}

func TestEnsureLiveAddendum(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var l Live
	require.NoError(service.DB.First(&l).Error)
	require.NoError(service.DB.Table(liveaddendum.TableName()).
		Delete("", "user_id = ?", l.UserID).Error)
	ok, err := EnsureLiveAddendum(l.UserID)
	require.NoError(err)
	assert.True(ok)
	var la liveaddendum.LiveAddendum
	require.NoError(service.DB.First(&la, "user_id = ?", l.UserID).Error)
	assert.Equal(liveaddendum.MaxVitality, la.Vitality)
	_, err = liveaddendum.Punish(l.UserID, liveaddendum.OperatorSub, 1, "test")
	require.NoError(err)
	ok, err = EnsureLiveAddendum(l.UserID)
	require.NoError(err)
	assert.True(ok)
	require.NoError(service.DB.First(&la, "user_id = ?", l.UserID).Error)
	assert.Equal(liveaddendum.MaxVitality-1, la.Vitality)
	ok, err = EnsureLiveAddendum(987514)
	require.NoError(err)
	assert.False(ok)
}

func TestFindLiveByRoomID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	live, err := FindLiveByRoomID(61618635)
	require.NoError(err)
	assert.NotNil(live)

	live, err = FindLiveByRoomID(616186)
	require.NoError(err)
	assert.Nil(live)
}

func TestFindOpenLiveByRoomIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	roomIDs := []int64{8635, 8636}
	lives, err := FindOpenLiveByRoomIDs(roomIDs)
	require.NoError(err)
	assert.Empty(lives)

	roomIDs = []int64{61618636, 61618637}
	lives, err = FindOpenLiveByRoomIDs(roomIDs)
	require.NoError(err)
	assert.Len(lives, 1)
}
