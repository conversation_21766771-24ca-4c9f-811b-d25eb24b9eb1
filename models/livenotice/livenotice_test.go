package livenotice

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveNotice{}, "id", "create_time", "modified_time", "delete_time", "type",
		"content", "open_url")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LiveNotice{}, "id", "create_time", "delete_time", "type",
		"content", "open_url")
}

func TestExistsType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := ExistsType(TypeGiftWall)
	require.NoError(err)
	assert.True(exists)

	exists, err = ExistsType(9999)
	require.NoError(err)
	assert.False(exists)
}

func TestAddGiftWallNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	notice := LiveNotice{
		Content: "新公告",
		OpenURL: "http://fm.test.com",
	}

	err := AddGiftWallNotice(notice.Content, notice.OpenURL)
	require.NoError(err)

	var l LiveNotice
	require.NoError(l.DB().Take(&l, "type = ? AND delete_time = 0", TypeGiftWall).Error)
	assert.Equal(notice.Content, l.Content)
	assert.Equal(notice.OpenURL, l.OpenURL)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var l LiveNotice
	require.NoError(l.DB().Take(&l, "type = ? AND delete_time = 0", TypeGiftWall).Error)

	notice, err := FindOne(l.ID, l.Type)
	require.NoError(err)
	assert.NotNil(notice)

	notice, err = FindOne(l.ID, 99999)
	require.NoError(err)
	assert.Nil(notice)
}

func TestFindAndDeleteGiftWallNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ClearCache(TypeGiftWall)
	ln, err := FindGiftWallNotice()
	require.NoError(err)
	require.NotNil(ln)

	cache, err := service.LRURedis.Get(keys.KeyLiveNotice1.Format(TypeGiftWall)).Bytes()
	require.NoError(err)
	require.NotEmpty(cache)
	var notice *LiveNotice
	err = json.Unmarshal(cache, &notice)
	require.NoError(err)

	ln, err = FindGiftWallNotice()
	require.NoError(err)
	require.NotNil(ln)
	assert.Equal(ln.ID, notice.ID)
	assert.Equal(ln.Content, notice.Content)

	err = DelGiftWallNotice(ln.ID)
	require.NoError(err)
	ClearCache(TypeGiftWall)
	ln, err = FindGiftWallNotice()
	require.NoError(err)
	assert.Nil(ln)
}
