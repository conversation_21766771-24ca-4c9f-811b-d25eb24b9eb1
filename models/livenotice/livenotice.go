package livenotice

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 公告类型
const (
	TypeGiftWall = iota + 1
)

// LiveNotice 直播公告
type LiveNotice struct {
	ID           int64 `gorm:"column:id;primary_key" json:"id"`
	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
	DeleteTime   int64 `gorm:"column:delete_time" json:"delete_time"` // 秒级时间戳

	Type    int    `gorm:"column:type" json:"type"`         // 公告类型
	Content string `gorm:"column:content" json:"content"`   // 公告内容
	OpenURL string `gorm:"column:open_url" json:"open_url"` // 公告跳转链接
}

// TableName of LiveNotice
func TableName() string {
	return "live_notice"
}

// TableName table name
func (LiveNotice) TableName() string {
	return TableName()
}

// DB the db instance of LiveNotice model
func (l LiveNotice) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeCreate gorm hook BeforeCreate
func (l *LiveNotice) BeforeCreate() error {
	l.CreateTime = goutil.TimeNow().Unix()
	l.ModifiedTime = l.CreateTime
	return nil
}

// BeforeUpdate gorm hook BeforeUpdate
func (l *LiveNotice) BeforeUpdate() error {
	l.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// ExistsType exists notice
func ExistsType(noticeType int) (bool, error) {
	db := LiveNotice{}.DB().Where("type = ? AND delete_time = 0", noticeType)
	return servicedb.Exists(db)
}

// AddGiftWallNotice 新增礼物墙公告的同时移除旧公告
func AddGiftWallNotice(content, openURL string) error {
	l := LiveNotice{
		Type:    TypeGiftWall,
		Content: content,
		OpenURL: openURL,
	}
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		// 下线当前的公告
		err := tx.Table(l.TableName()).Where("type = ? AND delete_time = 0", TypeGiftWall).
			Update("delete_time", goutil.TimeNow().Unix()).Error
		if err != nil {
			return err
		}
		// 插入新公告
		return tx.Create(&l).Error
	})
	return err
}

// FindOne find one notice by id and type
func FindOne(id int64, noticeType int) (*LiveNotice, error) {
	var notice LiveNotice
	err := notice.DB().Where("id = ? AND type = ? AND delete_time = 0", id, noticeType).
		Take(&notice).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &notice, nil
}

// FindGiftWallNotice find gift wall notice
func FindGiftWallNotice() (*LiveNotice, error) {
	key := keys.KeyLiveNotice1.Format(TypeGiftWall)
	noticeBytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	var notice *LiveNotice
	if len(noticeBytes) != 0 {
		if err = json.Unmarshal(noticeBytes, &notice); err != nil {
			return nil, err
		}
		return notice, nil
	}
	notice = new(LiveNotice)
	err = notice.DB().Where("type = ? AND delete_time = 0", TypeGiftWall).
		Take(&notice).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}
	if servicedb.IsErrNoRows(err) {
		notice = nil
	}
	noticeBytes, err = json.Marshal(notice)
	if err != nil {
		logger.Error(err)
		// PASS
		return notice, nil
	}
	err = service.LRURedis.Set(key, noticeBytes, time.Minute*10).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return notice, nil
}

// DelGiftWallNotice del gift wall notice
func DelGiftWallNotice(noticeID int64) error {
	err := LiveNotice{}.DB().
		Where("id = ? AND type = ? AND delete_time = 0", noticeID, TypeGiftWall).
		Update("delete_time", goutil.TimeNow().Unix()).Error
	return err
}

// ClearCache 移除公告缓存
func ClearCache(cacheType int) {
	key := keys.KeyLiveNotice1.Format(cacheType)
	err := service.LRURedis.Del(key).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
