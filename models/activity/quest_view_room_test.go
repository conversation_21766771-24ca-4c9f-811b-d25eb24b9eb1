package activity

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestQuestViewRoomTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(questViewRoom{}, "schedule", "time_offset")
	kc.Check(questSchedule{}, "pool_id", "room_id", "reward_type",
		"reward_id", "reward_num", "reward_duration")
}

func TestQuestViewRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g, err := gift.FindShowingGiftByGiftID(301)
	require.NoError(err)
	require.NotNil(g)

	now := goutil.TimeNow()
	quest := questViewRoom{
		Schedule: []*questSchedule{
			{
				PoolID:         int64(now.Year()*10000 + int(now.Month())*100 + now.Day()),
				RoomID:         123,
				RewardType:     rewardTypeGift,
				RewardID:       301,
				RewardNum:      1,
				RewardDuration: 10000,
			},
		},
	}

	s := &mevent.Simple{
		ID:             205,
		StartTime:      now.Unix() - 100,
		EndTime:        now.Unix() + 100,
		ExtendedFields: tutil.SprintJSON(quest),
	}
	eventKey := keys.KeyEventInfo1.Format(s.ID)
	service.Cache5Min.Set(eventKey, s, 0)

	date := strconv.FormatInt(quest.Schedule[0].PoolID, 10)
	redisKey := keys.KeyActivityQuestViewRoom2.Format(s.ID, date)
	testUserID := int64(12)
	require.NoError(service.Redis.Del(redisKey).Err())

	QuestViewRoom(s.ID, quest.Schedule[0].RoomID, testUserID)
	ok, err := service.Redis.SIsMember(redisKey, testUserID).Result()
	require.NoError(err)
	assert.True(ok)
	ttl, err := service.Redis.TTL(redisKey).Result()
	require.NoError(err)
	assert.Greater(ttl, 15*86400*time.Second)
	QuestViewRoom(s.ID, quest.Schedule[0].RoomID, testUserID)

	// 查询礼物
	ui, err := useritems.Find(bson.M{
		"user_id":     testUserID,
		"gift_id":     g.GiftID,
		"create_time": bson.M{"$gte": now.Unix()},
	}, nil)
	require.NoError(err)
	require.Equal(1, len(ui), "len(ui)")
	reward := quest.Schedule[0]
	assert.EqualValues(reward.RewardNum, ui[0].Num)
	assert.Equal(reward.RewardDuration, (ui[0].EndTime-ui[0].StartTime)*1000)
}
