package activity

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	defaultMinListCount int64 = 100
	defaultPageSize     int64 = 10
)

// EventTime 活动的时间信息
type EventTime struct {
	Start int64 `json:"start"`
	End   int64 `json:"end"`
}

// ApplicationType 限制类型
type ApplicationType int64

const (
	// ApplicationNone 无限制
	ApplicationNone ApplicationType = iota
	// ApplicationAllowList 白名单
	ApplicationAllowList
	// ApplicationBlockList 黑名单
	ApplicationBlockList
	// ApplicationGuildAllowList 公会分组白名单
	ApplicationGuildAllowList
)

// 积分类型
const (
	// NobleExtraOne 一倍积分
	NobleExtraOne int = 1
)

// 报名类型
const (
	SubscribeTypeUser    = iota // 普通用户报名
	SubscribeTypeCreator        // 主播报名
	SubscribeTypeLottery        // 抽奖报名
)

// ExtendedFields 活动配置
type ExtendedFields struct {
	// 通用配置
	RankStartTime int64    `json:"rank_start_time"`
	RankEndTime   int64    `json:"rank_end_time"`
	Ranks         []Rank   `json:"ranks"`
	Collect       *Collect `json:"collect"`

	// 活动预热开始时间
	WarmUpStartTime int64 `json:"warm_up_start_time,omitempty"`

	// REVIEW: 之后考虑 ExtendedFields 中的字段只用于校验，具体配置根据 ranks
	// 统计配置
	Application ApplicationType `json:"application"`
	// TODO: 考虑删除该字段
	CatalogID          *int64  `json:"catalog_id,omitempty"`
	ActivityCatalogIDs []int64 `json:"activity_catalog_ids,omitempty"`
	NobleExtra         *int    `json:"noble_extra,omitempty"`

	// 查询配置
	RankMaxCount    int64 `json:"rank_max_count"`
	DefaultPageSize int64 `json:"default_page_size"`
	PromotedScore   int64 `json:"promoted_score,omitempty"`
	ShowMyRank      *bool `json:"show_my_rank,omitempty"`

	TimeOffset int64 `json:"time_offset,omitempty"` // 支持配置活动时间偏移

	// 抽奖积分
	DrawPointStartTime int64 `json:"draw_point_start_time"`
	DrawPointEndTime   int64 `json:"draw_point_end_time"`

	// 预约开始结束时间
	ApplyStartTime int64 `json:"apply_start_time"`
	ApplyEndTime   int64 `json:"apply_end_time"`
	// 预约类型 1: 主播报名（需要验证主播有直播间），0: 普通用户报名
	SubscribeType int `json:"subscribe_type,omitempty"`
}

// TimeNow 当前时间（当前时间 + 时间偏移量），活动相关的时间获取需要使用该方法来获取
func (e ExtendedFields) TimeNow() time.Time {
	return timeNow(e.TimeOffset)
}

// Rank get rank by key
func (e ExtendedFields) Rank(key string) *Rank {
	for _, rank := range e.Ranks {
		if rank.Key == key {
			return &rank
		}
	}
	return nil
}

// OnGoingRanks 获取正在进行的 rank，没有则为 nil
func (e ExtendedFields) OnGoingRanks() []Rank {
	now := e.TimeNow().Unix()
	var ranks []Rank
	for i, r := range e.Ranks {
		if r.RankStartTime <= now && r.RankEndTime > now {
			ranks = append(ranks, e.Ranks[i])
		}
	}
	return ranks
}

// RanksWithOngoingWidget 获取有正在进行的小窗的 rank，没有则为 nil
func (e ExtendedFields) RanksWithOngoingWidget() []Rank {
	now := e.TimeNow().Unix()
	var ranks []Rank
	for i, r := range e.Ranks {
		et := r.RankEndTime
		if r.WidgetEndTime != nil {
			et = *r.WidgetEndTime
			// 为 0 代表没有小窗
			if et == 0 {
				continue
			}
		}

		if now >= r.RankStartTime && now < et {
			ranks = append(ranks, e.Ranks[i])
		}
	}
	return ranks
}

// GetWidgetEndTime 获取小窗结束时间
func (r Rank) GetWidgetEndTime() int64 {
	if r.WidgetEndTime == nil {
		return r.RankEndTime
	}

	return *r.WidgetEndTime
}

// RanksByKeys 通过 key 查找 rank
func (e ExtendedFields) RanksByKeys(keys ...string) []Rank {
	ranks := make([]Rank, 0, len(keys))
	for _, r := range e.Ranks {
		for _, key := range keys {
			if r.Key == key {
				ranks = append(ranks, r)
			}
		}
	}
	return ranks
}

// MinMaxParams min max params
type MinMaxParams struct {
	minScore string
	maxScore string
	limit    int64

	rankNoLimit bool
}

// MinMaxParams new minScore maxScore params
func (e ExtendedFields) MinMaxParams(min, max int64) *MinMaxParams {
	p := MinMaxParams{
		minScore: strconv.FormatInt(e.PromotedScore, 10),
		maxScore: "+inf",
		limit:    e.RankMaxCount,
	}

	if min > 0 {
		p.minScore = strconv.FormatInt(min, 10)
		// 前端传 min 时，查询出的一定都是达标的情况，会全部展示
		p.limit = 0
	}

	if max > 0 {
		p.maxScore = fmt.Sprintf("(%d", max)
	}
	return &p
}

// Limit limit
func (p MinMaxParams) Limit() int64 {
	return p.limit
}

// MinScore minScore
func (p MinMaxParams) MinScore() string {
	return p.minScore
}

// MaxScore maxScore
func (p MinMaxParams) MaxScore() string {
	return p.maxScore
}

// RankMinScore return rankMinScore
func (p MinMaxParams) RankMinScore() string {
	if p.rankNoLimit {
		return "-inf"
	}
	return p.minScore
}

// SetRankNoLimit set rankNoLimit
// rank no limit 为 true 时查询具体榜单详情时没有达标积分限制
// 用于榜单人数不足时的降级操作
func (p *MinMaxParams) SetRankNoLimit(noLimit bool) {
	p.rankNoLimit = noLimit
}

// Rank 多榜单配置
type Rank struct {
	Key          string  `json:"key"`
	PromotionNum int     `json:"promotion_num"`
	LastKey      *string `json:"last_key"`
	// LastPromotionRange 上个榜单排名限制, 0 为无限制 [start, end], 闭区间
	LastPromotionRange []int64 `json:"last_promotion_range,omitempty"`

	// 小窗结束时间，用于榜单结束但小窗仍要继续展示的情况
	// nil 时以 RankEndTime 为准，0 为不展示小窗
	WidgetEndTime *int64 `json:"widget_end_time,omitempty"`

	RankStartTime int64    `json:"rank_start_time"`
	RankEndTime   int64    `json:"rank_end_time"`
	Collect       *Collect `json:"collect,omitempty"`

	Application        ApplicationType `json:"application,omitempty"`
	NobleExtra         int             `json:"noble_extra,omitempty"`
	CatalogIDs         []int64         `json:"catalog_ids,omitempty"`          // CatalogIDs 为实时分区
	ActivityCatalogIDs []int64         `json:"activity_catalog_ids,omitempty"` // ActivityCatalogIDs 为活动开始时规划的分区

	RankMaxCount    int64 `json:"rank_max_count"`
	DefaultPageSize int64 `json:"default_page_size"`
	PromotedScore   int64 `json:"promoted_score,omitempty"`
	ShowMyRank      *bool `json:"show_my_rank,omitempty"`

	Middlewares []RankMiddlewareParam `json:"middlewares,omitempty"`
}

// RankMiddlewareParam 中间函数的参数
type RankMiddlewareParam struct {
	Func   string          `json:"func"`
	Params json.RawMessage `json:"params"`
}

// PromotedMinRank 晋级最低排名
// TODO: 目前仅支持从第一名开始
func (r Rank) PromotedMinRank() int64 {
	return int64(r.PromotionNum)
}

// CheckPromoted 查询晋级榜是否晋级
func (r Rank) CheckPromoted(key, member string) bool {
	if r.LastKey == nil || *r.LastKey == "" {
		return true
	}
	result, err := service.Redis.ZRevRank(key, member).Result()
	if err == redis.Nil {
		return false
	}
	if err != nil {
		logger.Error(err)
		return false
	}
	return r.checkPromoted(result + 1)
}

func (r Rank) checkPromoted(rank int64) bool {
	if len(r.LastPromotionRange) == 0 {
		return true
	}
	if len(r.LastPromotionRange) != 2 {
		logger.Errorf("活动 last_promotion_range 配置错误，key: %s", r.Key)
		return false
	}

	if r.LastPromotionRange[0] != 0 && rank < r.LastPromotionRange[0] {
		return false
	}
	if r.LastPromotionRange[1] != 0 && rank > r.LastPromotionRange[1] {
		return false
	}
	return true
}

// CheckCatalog check catalog
func (r Rank) CheckCatalog(activityCatalogID int64) bool {
	if len(r.ActivityCatalogIDs) == 0 {
		return true
	}

	for _, id := range r.ActivityCatalogIDs {
		if activityCatalogID == id {
			return true
		}
	}
	return false
}

// Collect 收集榜单配置
type Collect struct {
	Gifts []*CollectGift `json:"gifts,omitempty"`
	Type  int            `json:"type"` // 收集类型
}

// CollectGift 榜单礼物配置
type CollectGift struct {
	GiftID int64  `json:"gift_id"`
	MaxNum *int64 `json:"max_num"`
}

// GetEventTime 获取活动时间，首先会尝试从缓存读取
func GetEventTime(eventID int64) (startTime, endTime int64, err error) {
	key := keys.KeyEventTime1.Format(eventID)
	if v, ok := service.Cache5Min.Get(key); ok {
		timeInfo := v.(EventTime)
		return timeInfo.Start, timeInfo.End, nil
	}
	// 获取失败，从数据库获取
	start, end, err := mevent.EventTime(eventID)
	if err != nil {
		return 0, 0, err
	}
	defaultTime := int64(0)
	if start == nil {
		start = &defaultTime
	}
	if end == nil {
		end = &defaultTime
	}
	timeInfo := EventTime{
		Start: *start,
		End:   *end,
	}
	service.Cache5Min.Set(key, timeInfo, 0)
	return timeInfo.Start, timeInfo.End, nil
}

// GetEvent 获取活动信息，首先会尝试从缓存读取
func GetEvent(eventID int64) (*mevent.Simple, error) {
	key := keys.KeyEventInfo1.Format(eventID)
	if v, ok := service.Cache5Min.Get(key); ok {
		e, ok := v.(*mevent.Simple)
		if !ok || e == nil {
			return nil, nil
		}
		return e, nil
	}
	// 获取失败，从数据库获取
	e, err := mevent.FindSimple(eventID)
	if err != nil {
		return nil, err
	}
	// TODO: 返回的时候应该 copy 一份
	service.Cache5Min.Set(key, e, 0)
	return e, nil
}

// GetEventWithExtendFields 获取活动信息，会将 extended_fields 赋值给 v，首先会尝试从缓存读取
// TODO: 应该增加一个返回值返回通用的 extended_fields 配置，比如 time_offset
func GetEventWithExtendFields(eventID int64, extendedFields interface{}) (*mevent.Simple, error) {
	e, err := GetEvent(eventID)
	if err != nil {
		return nil, err
	}
	if e == nil {
		return nil, nil
	}
	if e.ExtendedFields != "" {
		err := json.Unmarshal([]byte(e.ExtendedFields), extendedFields)
		if err != nil {
			return nil, err
		}
	}
	return e, nil
}

// NewExtendedFields 初始化活动配置
func NewExtendedFields() *ExtendedFields {
	extended := new(ExtendedFields)
	extended.RankMaxCount = defaultMinListCount
	extended.DefaultPageSize = defaultPageSize
	return extended
}

// FindLiveExtendedFields find event and unmarshal extend
func FindLiveExtendedFields(eventID int64, useCache bool) (*mevent.Simple,
	*ExtendedFields, error) {
	extended := NewExtendedFields()
	var fn func(eventID int64, extendedFields interface{}) (*mevent.Simple, error)
	if useCache {
		fn = GetEventWithExtendFields
	} else {
		fn = mevent.FindSimpleWithExtendedFields
	}
	simple, err := fn(eventID, extended)
	if err != nil {
		return nil, extended, err
	}
	return simple, extended, err
}

// annualDayByQualifyKey 根据资格赛榜单的 key 获取当前是资格赛的第几天
func annualDayByQualifyKey(key string) int {
	switch key {
	case "03":
		return 1
	case "04":
		return 2
	case "05":
		return 3
	case "06":
		return 4
	}
	return 0
}

// AnnualDayOfQualify 获得 t 时刻是资格赛的第几天，如果不在资格赛期间会返回 0，用于年度直播盛典活动
func AnnualDayOfQualify(t time.Time) (int, error) {
	simple, e, err := FindLiveExtendedFields(usersrank.EventIDAnnualLive, true)
	if err != nil {
		return 0, err
	}
	if simple == nil {
		return 0, errors.New("找不到活动")
	}
	rs := e.RanksByKeys("03", "04", "05", "06")
	for _, r := range rs {
		if t.Unix() >= r.RankStartTime && time.Unix(r.RankEndTime, 0).Day() == t.Day() {
			return annualDayByQualifyKey(r.Key), nil
		}
	}
	return 0, nil
}

// CountWithMinNum 记录总数不超过默认限制数量时取记录总数, 会统计 score 值为 minNum 的情况,
// 记录总数大于默认限制数量且条件查询数量大于默认限制数量取条件查询数量, 其他均取默认限制数量
func CountWithMinNum(key string, minMaxParam *MinMaxParams) int64 {
	count, err := service.Redis.ZCount(key, minMaxParam.MinScore(), minMaxParam.MaxScore()).Result()
	if err != nil {
		logger.Error(err)
		return 0
	}
	// 如果总数超过达标人数返回达标人数，没有超过需要查询未达标的人数来补充榜单
	if count >= minMaxParam.Limit() {
		return count
	}

	var cmd *redis.IntCmd
	minMaxParam.SetRankNoLimit(true)
	if minMaxParam.maxScore == "+inf" {
		cmd = service.Redis.ZCard(key)
	} else {
		cmd = service.Redis.ZCount(key, "-inf", minMaxParam.MaxScore())
	}

	allCount, err := cmd.Result()
	if err != nil {
		logger.Error(err)
		return count
	}
	if allCount <= minMaxParam.Limit() {
		return allCount
	}
	return minMaxParam.Limit()
}

// timeNow 返回带偏移的当前时间
// offset: 时间戳偏移（单位：秒）
func timeNow(offset int64) time.Time {
	if offset == 0 {
		return goutil.TimeNow()
	}
	return goutil.TimeNow().Add(time.Duration(offset) * time.Second)
}
