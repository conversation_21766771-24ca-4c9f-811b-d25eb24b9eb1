package activity

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestKeyRank(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("test_users/rank/6/123", <PERSON><PERSON>ank(123))
	assert.Equal("test_users/rank/6/123/aaa", <PERSON><PERSON><PERSON><PERSON>(123, "aaa"))
	assert.Equal("test_users/rank/6/123/aaa/bbb", KeyRank(123, "aaa", "bbb"))
}

func TestKeyPrizePool(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("test_activity/pool/123", KeyPrizePool(123))
	assert.Equal("test_activity/pool/123/1", KeyPrizePool(123, "1"))
	assert.Equal("test_activity/pool/123/1/2", KeyPrizePool(123, "1", "2"))
}
