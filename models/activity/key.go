package activity

import (
	"strconv"
	"strings"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func appendKey(memberID int64, ks ...string) string {
	key := strconv.FormatInt(memberID, 10)
	if len(ks) > 0 && ks[0] != "" {
		key += "/" + strings.Join(ks, "/")
	}
	return key
}

// KeyRank 获取活动榜单 key
func KeyRank(eventID int64, ks ...string) string {
	return keys.KeyUsersRank2.Format(usersrank.TypeActivity, appendKey(eventID, ks...))
}

// KeyGuildRank 获取活动公会榜单 key
func KeyGuildRank(key string) string {
	return keys.KeyGuildsRank2.Format(usersrank.TypeActivity, key)
}

// KeyGuildCreatorRank 获取公会榜首席主播榜单的 key
func KeyGuildCreatorRank(guildID int64, key string) string {
	return keys.KeyGuildRevenues3.Format(guildID, usersrank.TypeActivity, "/"+key)
}

// KeyPrizePool 获取活动奖池的 key
func KeyPrizePool(eventID int64, ks ...string) string {
	return keys.KeyActivityPool1.Format(appendKey(eventID, ks...))
}

// KeyUsersApplication 获取主播榜黑白名单的 key
func KeyUsersApplication(eventID int64, ks ...string) string {
	return keys.KeyUsersActivityApplication1.Format(appendKey(eventID, ks...))
}

// KeyGuildUsersApplication 获取公会榜主播白名单的 key
func KeyGuildUsersApplication(eventID int64, ks ...string) string {
	return keys.KeyGuildUsersApplication1.Format(appendKey(eventID, ks...))
}

// KeyUserComplete 获取主播达成目标的 key
func KeyUserComplete(eventID int64, ks ...string) string {
	return keys.KeyActivityComplete1.Format(appendKey(eventID, ks...))
}

// KeyFreePoint 获得榜单统计免费礼物积分的 key
func KeyFreePoint(eventID int64, ks ...string) string {
	return keys.KeyActivityRankFreePoint1.Format(appendKey(eventID, ks...))
}

// KeyGiftWall 活动礼物墙的 key
func KeyGiftWall(userID, eventID int64, ks ...string) string {
	return keys.KeyActivityGiftWall2.Format(appendKey(eventID, ks...), userID)
}
