package activity

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/service"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
)

// Info activity list info
type Info struct {
	UserID int64                  `json:"user_id,omitempty"`
	More   map[string]interface{} `json:"more,omitempty"`

	Score int64 `json:"-"`
}

// ZADD redis zadd info
func ZADD(key string, expire time.Duration, infos ...*Info) error {
	list := make([]*redis.Z, 0, len(infos))
	for _, item := range infos {
		bs, err := json.Marshal(item)
		if err != nil {
			return err
		}
		list = append(list, &redis.Z{
			Score:  float64(item.Score),
			Member: bs,
		})
	}
	pipe := service.Redis.TxPipeline()
	pipe.ZAdd(key, list...)
	serviceredis.Expire(pipe, key, expire)
	_, err := pipe.Exec()
	return err
}

// Range redis list range
func Range(key string, start, end int64) ([]*Info, error) {
	list, err := service.Redis.ZRevRangeWithScores(key, start, end).Result()
	if err != nil {
		return nil, err
	}

	infos := make([]*Info, 0, len(list))
	for _, item := range list {
		bs, ok := item.Member.(string)
		if !ok {
			return nil, fmt.Errorf("key: %s member not string", key)
		}
		info := &Info{
			Score: int64(item.Score),
		}
		if err := json.Unmarshal([]byte(bs), info); err != nil {
			return nil, err
		}
		infos = append(infos, info)
	}
	return infos, nil
}
