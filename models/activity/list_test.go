package activity

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestInfoList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test_activity_list"
	require.NoError(service.Redis.Del(key).Err())

	list := []*Info{
		{6, map[string]interface{}{"key": "6"}, 6},
		{5, map[string]interface{}{"key": "5"}, 5},
		{4, map[string]interface{}{"key": "4"}, 4},
		{3, map[string]interface{}{"key": "3"}, 3},
		{2, map[string]interface{}{"key": "2"}, 2},
		{1, map[string]interface{}{"key": "1"}, 1},
	}
	require.NoError(ZADD(key, time.Hour, list...))

	infos, err := Range(key, 0, -1)
	require.NoError(err)
	assert.Equal(list, infos)

	infos, err = Range(key, 0, 2)
	require.NoError(err)
	assert.Equal(list[0:3], infos)

	infos, err = Range(key, 3, 5)
	require.NoError(err)
	assert.Equal(list[3:], infos)

	infos, err = Range(key, 3, -1)
	require.NoError(err)
	assert.Equal(list[3:], infos)
}
