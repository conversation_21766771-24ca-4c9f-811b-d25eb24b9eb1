package activity

import (
	"strconv"
	"time"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	rewardTypeGift = iota + 1 // 背包礼物
)

type questViewRoom struct {
	Schedule   []*questSchedule `json:"schedule"`
	TimeOffset int64            `json:"time_offset,omitempty"`
}

type questSchedule struct {
	PoolID         int64 `json:"pool_id"`         // 奖池 ID（格式化后的日期）例：20220723
	RoomID         int64 `json:"room_id"`         // 当日直播房间
	RewardType     int64 `json:"reward_type"`     // 当日直播奖励类型
	RewardID       int64 `json:"reward_id"`       // 奖励 ID
	RewardNum      int64 `json:"reward_num"`      // 奖励数量
	RewardDuration int64 `json:"reward_duration"` // 奖励持续时间，单位：毫秒
}

// QuestViewRoom 任务访问直播间
// 当日访问直播间获取奖励
func QuestViewRoom(eventID int64, roomID, userID int64) {
	var quest questViewRoom
	s, err := GetEventWithExtendFields(eventID, &quest)
	if err != nil {
		logger.Error(err)
		return
	}
	if s == nil {
		return
	}
	questNow := timeNow(quest.TimeOffset)
	if questNow.Unix() < s.StartTime || questNow.Unix() >= s.EndTime {
		return
	}
	date := questNow.Format(util.TimeFormatYMDWithNoSpace)
	tryFinishQuest := func(reward *questSchedule) {
		if strconv.FormatInt(reward.PoolID, 10) != date {
			return
		}
		if roomID != reward.RoomID {
			return
		}
		// 判断是否完成任务
		key := keys.KeyActivityQuestViewRoom2.Format(eventID, date)
		// 记录完成情况
		pipe := service.Redis.TxPipeline()
		cmd := pipe.SAdd(key, userID)
		expireDuration :=
			time.Duration(s.EndTime+15*util.SecondOneDay-questNow.Unix()) * time.Second
		pipe.Expire(key, expireDuration)
		_, err = pipe.Exec()
		if err != nil {
			logger.Error(err)
			return
		}
		if cmd.Val() == 0 {
			// 说明完成了任务
			return
		}
		switch reward.RewardType {
		case rewardTypeGift:
			// 背包礼物
			g, err := gift.FindShowingGiftByGiftID(reward.RewardID)
			if err != nil {
				logger.Error(err)
				return
			}
			if g == nil {
				logger.WithFields(logger.Fields{
					"event_id": eventID,
					"gift_id":  reward.RewardID,
				}).Error("活动任务奖励礼物不存在")
				return
			}
			if !useritems.IsBackpackGift(g) {
				logger.WithFields(logger.Fields{
					"event_id": eventID,
					"gift_id":  reward.RewardID,
				}).Error("活动任务奖励礼物不是背包礼物")
				return
			}
			// 获取奖励不需要偏移
			nowUnix := goutil.TimeNow().Unix()
			err = useritems.NewAdder(userID, 1).Append(g, reward.RewardNum, nowUnix,
				nowUnix+reward.RewardDuration/1000).Add()
			if err != nil {
				logger.Error(err)
				return
			}
		}
	}
	for _, reward := range quest.Schedule {
		tryFinishQuest(reward)
	}
}
