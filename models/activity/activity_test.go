package activity

import (
	"encoding/json"
	"reflect"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(ExtendedFields{}, "collect", "application", "apply_start_time", "apply_end_time", "rank_max_count",
		"default_page_size", "promoted_score", "show_my_rank", "catalog_id", "activity_catalog_ids", "noble_extra", "rank_start_time",
		"rank_end_time", "warm_up_start_time", "time_offset", "ranks", "draw_point_start_time", "draw_point_end_time", "subscribe_type")
}

func TestExtendedFieldsTimeNow(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)

	e := ExtendedFields{}
	// 测试 time_offset 不存在时
	assert.Equal(int64(1), e.TimeNow().Unix())

	e = ExtendedFields{
		TimeOffset: 10,
	}
	// 测试 time_offset 存在时
	assert.Equal(int64(11), e.TimeNow().Unix())

	e = ExtendedFields{
		TimeOffset: -1,
	}
	// 测试 time_offset 为负值时
	assert.Equal(int64(0), e.TimeNow().Unix())
}

func TestGetEventTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	service.Cache5Min.Flush()
	startTime, endTime, err := GetEventTime(110)
	require.NoError(err)
	assert.NotZero(startTime)
	assert.NotZero(endTime)
	now := goutil.TimeNow()
	start := util.BeginningOfDay(now).Unix()
	end := util.BeginningOfDay(now.AddDate(0, 0, 1)).Unix()
	eventTime := EventTime{
		Start: start,
		End:   end,
	}
	keyEventTime := keys.KeyEventTime1.Format(111)
	service.Cache5Min.Set(keyEventTime, eventTime, 0)
	startTime, endTime, err = GetEventTime(111)
	require.NoError(err)
	assert.Equal(start, startTime)
	assert.Equal(end, endTime)
}

func TestGetEventWithExtendFields(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试查询不存在活动时第二次查询应该正常的情况
	service.Cache5Min.Flush()
	var ext ExtendedFields
	simple, err := GetEventWithExtendFields(999, &ext)
	require.NoError(err)
	require.Nil(simple, "活动存在, 活动 ID 需更换为不存在的活动 ID")
	simple, err = GetEventWithExtendFields(999, &ext)
	require.NoError(err)
	assert.Nil(simple)

	type applyTime struct {
		ApplyStartTime int64 `json:"apply_start_time"`
		ApplyEndTime   int64 `json:"apply_end_time"`
	}
	var t1 applyTime
	e, err := GetEventWithExtendFields(131995, &t1)
	require.NoError(err)
	assert.NotNil(e)
	assert.NotEmpty(e.ExtendedFields)
	assert.NotZero(t1.ApplyStartTime)
	assert.NotZero(t1.ApplyEndTime)
	now := goutil.TimeNow()
	start := util.BeginningOfDay(now).Unix()
	end := util.BeginningOfDay(now.AddDate(0, 0, 1)).Unix()
	t1.ApplyEndTime = start
	t1.ApplyEndTime = end
	v, err := json.Marshal(t1)
	require.NoError(err)
	keyEventTime := keys.KeyEventInfo1.Format(131996)
	e.ExtendedFields = string(v)
	e.ID = 131996
	service.Cache5Min.Set(keyEventTime, e, 0)
	var t2 applyTime
	e, err = GetEventWithExtendFields(e.ID, &t2)
	require.NoError(err)
	require.NotNil(e)
	assert.Equal(string(v), e.ExtendedFields)
	assert.True(reflect.DeepEqual(t1, t2))
}

func TestNewExtendedFields(t *testing.T) {
	assert := assert.New(t)

	e := NewExtendedFields()
	assert.Equal(int64(100), e.RankMaxCount)
	assert.Equal(int64(10), e.DefaultPageSize)
}

func TestFindLiveExtendedFields(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	st := goutil.TimeNow().Unix()
	var eventID int64 = 131995
	err := service.DB.Table(mevent.TableName()).Where("id = ?",
		eventID).Updates(map[string]interface{}{"create_time": st}).Error
	require.NoError(err)
	service.Cache5Min.Flush()
	key := keys.KeyEventInfo1.Format(eventID)
	simple, _, err := FindLiveExtendedFields(eventID, true)
	require.NoError(err)
	require.NotNil(simple)
	require.Equal(st, simple.CreateTime)
	service.Cache5Min.Set(key, "", 0)
	simple, _, err = FindLiveExtendedFields(eventID, true)
	require.NoError(err)
	assert.Nil(simple)
	simple, _, err = FindLiveExtendedFields(eventID, false)
	require.NoError(err)
	require.NotNil(simple)
	assert.Equal(st, simple.CreateTime)
}

func TestExtendedFieldsOnGoingRanks(t *testing.T) {
	assert := assert.New(t)

	var e ExtendedFields
	assert.Nil(e.OnGoingRanks())

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)

	e.Ranks = []Rank{
		{Key: "1", RankStartTime: 1, RankEndTime: 2},
		{Key: "2", RankStartTime: 2, RankEndTime: 3},
		{Key: "3", RankStartTime: 1, RankEndTime: 3},
	}
	ranks := e.OnGoingRanks()
	assert.Len(ranks, 2)
	assert.False(util.Includes(len(ranks), func(i int) bool {
		return ranks[i].Key == "2"
	}))
}

func TestExtendedFieldsRanksWithOngoingWidget(t *testing.T) {
	assert := assert.New(t)

	var e ExtendedFields
	assert.Nil(e.RanksWithOngoingWidget())

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(3, 0)
	})
	defer goutil.SetTimeNow(nil)

	e.Ranks = []Rank{
		{Key: "1", RankStartTime: 1, RankEndTime: 2},
		{Key: "2", RankStartTime: 2, RankEndTime: 4, WidgetEndTime: goutil.NewInt64(0)},
		{Key: "3", RankStartTime: 1, RankEndTime: 3, WidgetEndTime: goutil.NewInt64(4)},
		{Key: "4", RankStartTime: 1, RankEndTime: 5},
	}
	ranks := e.RanksWithOngoingWidget()
	assert.Len(ranks, 2)
	assert.False(util.Includes(len(ranks), func(i int) bool {
		return ranks[i].Key == "1" || ranks[i].Key == "2"
	}))
}

func TestGetWidgetEndTime(t *testing.T) {
	assert := assert.New(t)

	r := Rank{
		RankEndTime: 100,
	}
	assert.Equal(r.RankEndTime, r.GetWidgetEndTime())

	r.WidgetEndTime = goutil.NewInt64(1)
	assert.EqualValues(*r.WidgetEndTime, r.GetWidgetEndTime())
}

func TestMinMaxParams(t *testing.T) {
	assert := assert.New(t)
	e := ExtendedFields{
		RankMaxCount:  100,
		PromotedScore: 100,
	}

	assert.Equal(&MinMaxParams{minScore: "10", maxScore: "(100"}, e.MinMaxParams(10, 100))
	assert.Equal(&MinMaxParams{minScore: "100", maxScore: "(200", limit: 100}, e.MinMaxParams(0, 200))
	assert.Equal(&MinMaxParams{minScore: "100", maxScore: "+inf", limit: 100}, e.MinMaxParams(0, 0))
}

func TestCheckPromoted(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test_key"
	require.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 100, Member: 1},
		&redis.Z{Score: 200, Member: 2},
		&redis.Z{Score: 300, Member: 3},
	).Err())
	defer service.Redis.Del(key)

	assert.True(Rank{
		LastKey:            &key,
		LastPromotionRange: []int64{0, 1},
	}.CheckPromoted(key, "3"))

	assert.False(Rank{
		LastKey:            &key,
		LastPromotionRange: []int64{0, 1},
	}.CheckPromoted(key, "2"))

	assert.True(Rank{
		LastKey:            &key,
		LastPromotionRange: []int64{1, 2},
	}.CheckPromoted(key, "2"))

	assert.True(Rank{
		LastKey:            &key,
		LastPromotionRange: []int64{2, 3},
	}.CheckPromoted(key, "2"))

	assert.True(Rank{
		LastKey:            &key,
		LastPromotionRange: []int64{2, 0},
	}.CheckPromoted(key, "2"))
}

func TestRankCheckCatalog(t *testing.T) {
	assert := assert.New(t)

	tests := []struct {
		name               string
		ActivityCatalogIDs []int64
		activityCatalogID  int64
		want               bool
	}{
		{
			name:               "没有配置",
			ActivityCatalogIDs: nil,
			activityCatalogID:  0,
			want:               true,
		},
		{
			name:               "不在对应分区",
			ActivityCatalogIDs: []int64{1, 2, 3},
			activityCatalogID:  0,
			want:               false,
		},
		{
			name:               "在对应分区",
			ActivityCatalogIDs: []int64{1, 2, 3},
			activityCatalogID:  2,
			want:               true,
		},
	}
	for _, tt := range tests {
		r := Rank{ActivityCatalogIDs: tt.ActivityCatalogIDs}
		assert.Equal(tt.want, r.CheckCatalog(tt.activityCatalogID), tt.name)
	}
}

func TestExtendedFieldsRanksByKeys(t *testing.T) {
	assert := assert.New(t)

	e := ExtendedFields{
		Ranks: []Rank{
			{Key: "1"},
			{Key: "2"},
			{Key: "3"},
			{Key: "4"},
		},
	}
	assert.Len(e.RanksByKeys("1", "2"), 2)
	assert.Len(e.RanksByKeys("1", "5"), 1)
	assert.Len(e.RanksByKeys("5"), 0)
}

func TestAnnualDayByQualifyKey(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, annualDayByQualifyKey("02"))
	assert.Equal(1, annualDayByQualifyKey("03"))
	assert.Equal(2, annualDayByQualifyKey("04"))
	assert.Equal(3, annualDayByQualifyKey("05"))
	assert.Equal(4, annualDayByQualifyKey("06"))
	assert.Equal(0, annualDayByQualifyKey("07"))
}

func TestAnnualDayOfQualify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	d, err := AnnualDayOfQualify(goutil.TimeNow())
	require.NoError(err)
	assert.Equal(0, d)

	todayBegin := goutil.BeginningOfDay(goutil.TimeNow())
	service.Cache5Min.Delete(keys.KeyEventInfo1.Format(usersrank.EventIDAnnualLive))
	e := ExtendedFields{
		Ranks: []Rank{
			{
				Key:           "03",
				RankStartTime: todayBegin.Unix(),
				RankEndTime:   todayBegin.Add(23 * time.Hour).Unix(),
			},
		},
	}
	err = service.DB.Table(mevent.TableName()).Where("id = ?", usersrank.EventIDAnnualLive).
		Updates(map[string]interface{}{"extended_fields": tutil.SprintJSON(e)}).Error
	require.NoError(err)
	d, err = AnnualDayOfQualify(goutil.TimeNow())
	require.NoError(err)
	assert.Equal(1, d)
	d, err = AnnualDayOfQualify(todayBegin.Add(23 * time.Hour).Add(2 * time.Minute))
	require.NoError(err)
	assert.Equal(1, d)
	d, err = AnnualDayOfQualify(todayBegin.Add(24 * time.Hour))
	require.NoError(err)
	assert.Equal(0, d)
	d, err = AnnualDayOfQualify(todayBegin.Add(25 * time.Hour))
	require.NoError(err)
	assert.Equal(0, d)
}

func TestCountWithMinNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testData := make([]*redis.Z, 0, 20)
	for i := 0; i < 40; i++ {
		testData = append(testData,
			&redis.Z{Score: float64(i/2 + 1), Member: i},
		)
	}
	key := KeyRank(usersrank.EventIDValentine)
	pipeline := service.Redis.Pipeline()
	pipeline.ZAdd(key, testData...)
	pipeline.Expire(key, 1*time.Minute)
	_, err := pipeline.Exec()
	require.NoError(err)

	assert.Equal(int64(40), CountWithMinNum(key, &MinMaxParams{
		minScore: "0",
		maxScore: "+inf",
		limit:    0,
	}))
	assert.Equal(int64(22), CountWithMinNum(key, &MinMaxParams{
		minScore: "10",
		maxScore: "+inf",
		limit:    0,
	}))
	assert.Equal(int64(10), CountWithMinNum(key, &MinMaxParams{
		minScore: "10",
		maxScore: "(15",
		limit:    0,
	}))
	assert.Equal(int64(20), CountWithMinNum(key, &MinMaxParams{
		minScore: "15",
		maxScore: "+inf",
		limit:    20,
	}))
	assert.Equal(int64(10), CountWithMinNum(key, &MinMaxParams{
		minScore: "20",
		maxScore: "+inf",
		limit:    10,
	}))
}
