// Package liverecord 直播间开启时间段的记录
package liverecord

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CollectionName collection name without prefix
const CollectionName = "live_records"

// 单位：毫秒
const switchGap = 10000

// LiveRecord document in collection live_records
type LiveRecord struct {
	OID    primitive.ObjectID `bson:"_id"`
	Helper `bson:",inline"`
}

// Helper for LiveRecord
// 时间单位：毫秒
type Helper struct {
	RoomOID primitive.ObjectID `bson:"_room_id"`
	RoomID  int64              `bson:"room_id"`
	GuildID int64              `bson:"guild_id,omitempty"`
	Bstatus int32              `bson:"bstatus"`

	StartTime int64 `bson:"start_time"` // 13 位时间戳
	Duration  int64 `bson:"duration"`
	EndTime   int64 `bson:"end_time"`
}

// Update 更新状态
func Update(roomObjID primitive.ObjectID, roomID, guildID int64, bstatus bool) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	lr := new(LiveRecord)
	err := collection.FindOne(ctx, bson.M{"_room_id": roomObjID},
		options.FindOne().SetSort(bson.M{"start_time": -1})).Decode(lr)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return err
	}
	notFound := mongodb.IsNoDocumentsError(err)
	// bstatus  | true | false  |
	// notFound | new  | return |
	//
	// 找到了
	// bstatus             | ture    | false  |
	// lr.bstatus(): true  | return  | close  |
	// lr.bstatus(): false | 继续判断 | return |
	//
	// bstatus && lr.bstatus():
	// lr.reopen(): true -> 重开
	// lr.reopen(): false -> 新建
	if (notFound && !bstatus) || (!notFound && lr.bstatus() == bstatus) {
		// 不需要更新
		return nil
	}
	switch {
	case !notFound && bstatus && lr.reopen():
		// 重开
		err = collection.FindOneAndUpdate(ctx, bson.M{"_id": lr.OID},
			bson.M{"$set": bson.M{
				"bstatus": 1}}).Err()
	case !notFound && !bstatus:
		// 关闭
		endTime := util.TimeToUnixMilli(goutil.TimeNow())
		err = collection.FindOneAndUpdate(ctx, bson.M{"_id": lr.OID},
			bson.M{"$set": bson.M{
				"bstatus":  0,
				"end_time": endTime,
				"duration": endTime - lr.StartTime}}).Err()
	default:
		// bstatus 肯定是 true
		// 新建
		lr.Helper = Helper{
			RoomOID:   roomObjID,
			RoomID:    roomID,
			GuildID:   guildID,
			StartTime: util.TimeToUnixMilli(goutil.TimeNow()),
			Bstatus:   1,
		}
		_, err = collection.InsertOne(ctx, &lr.Helper)
	}
	return err
}

func (lr LiveRecord) reopen() bool {
	return util.TimeToUnixMilli(goutil.TimeNow())-lr.EndTime < switchGap
}

func (lr LiveRecord) bstatus() bool {
	return lr.Bstatus != 0
}
