package liverecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testRoomID    = int64(22489473)
	testRoomIDHex = "5ab9d5d9bc9b53298ce5a5a5"
)

var (
	testRoomObjID primitive.ObjectID
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	run(m)
}

func run(m *testing.M) int {
	testRoomObjID, _ = primitive.ObjectIDFromHex(testRoomIDHex)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	_, err := collection.DeleteMany(ctx, bson.M{"_room_id": testRoomObjID})
	if err != nil {
		panic(err)
	}
	return m.Run()
}

func TestReOpenAndStatus(t *testing.T) {
	assert := assert.New(t)
	var lr LiveRecord
	assert.False(lr.reopen())
	lr.EndTime = util.TimeToUnixMilli(goutil.TimeNow()) - 1
	assert.True(lr.reopen())
	assert.False(lr.bstatus())
	lr.Bstatus = 1
	assert.True(lr.bstatus())
}

func TestUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	var lr LiveRecord
	// 不需要更新 1
	require.NoError(Update(testRoomObjID, testRoomID, 0, false))
	// 新建 1
	require.NoError(Update(testRoomObjID, testRoomID, 0, true))
	require.NoError(collection.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&lr))
	assert.True(lr.bstatus())
	// 不需要更新 2
	require.NoError(Update(testRoomObjID, testRoomID, 0, true))
	// 关闭
	require.NoError(Update(testRoomObjID, testRoomID, 0, false))
	require.NoError(collection.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&lr))
	assert.False(lr.bstatus())
	assert.Equal(lr.EndTime-lr.StartTime, lr.Duration)
	// 不需要更新 3
	require.NoError(Update(testRoomObjID, testRoomID, 0, false))
	// 重开
	require.NoError(Update(testRoomObjID, testRoomID, 0, true))
	require.NoError(collection.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&lr))
	assert.True(lr.bstatus())
	// 新建 2
	require.NoError(collection.FindOneAndUpdate(ctx,
		bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"bstatus": 0, "end_time": 42}}).Err())
	require.NoError(Update(testRoomObjID, testRoomID, 0, true))
	count, err := collection.CountDocuments(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	assert.Equal(int64(2), count)
}
