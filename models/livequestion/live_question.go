package livequestion

import (
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 点赞操作
const (
	OperationDislike = iota // 取消点赞
	OperationLike           // 点赞
)

// Collection 返回 LiveQuestion 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_questions")
}

// LiveQuestion document in collection live_questions
type LiveQuestion struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"question_id"`
	ID  string             `bson:"-" json:"_id,omitempty"` // 兼容安卓 5.5.7 iOS 4.6.8 之前的老版本，由于 ObjectID 不支持 json omitempty，使用字符串类型来支持

	Helper `bson:",inline"`

	Titles []liveuser.Title `bson:"-" json:"titles,omitempty"`
}

// Helper for LiveQuestion
// TODO: 使用 room_id 作为查询房间的索引
type Helper struct {
	RoomOID primitive.ObjectID `bson:"_room_id" json:"-"`

	RoomID           int64                `bson:"room_id" json:"room_id"`
	UserID           int64                `bson:"user_id" json:"user_id"`
	Username         string               `bson:"username" json:"username"`
	IconURL          string               `bson:"iconurl" json:"iconurl"`
	Question         string               `bson:"question" json:"question"`
	Price            int64                `bson:"price" json:"price"`
	Status           int32                `bson:"status" json:"status"`
	CreatedTime      time.Time            `bson:"created_time" json:"-"`
	CreatedTimeMsec  goutil.TimeUnixMilli `bson:"-" json:"created_time"`
	UpdatedTime      time.Time            `bson:"updated_time" json:"-"`
	UpdatedTimeMsec  goutil.TimeUnixMilli `bson:"-" json:"updated_time"`
	AnsweredTime     *time.Time           `bson:"answered_time" json:"-"`
	AnsweredTimeMsec goutil.TimeUnixMilli `bson:"-" json:"answered_time,omitempty"`
	TransactionID    int64                `bson:"transaction_id" json:"-"`
	VipType          int                  `bson:"vip_type" json:"-"` // TODO: 移除贵族等级信息
	NobleLevel       int                  `bson:"noble_level" json:"-"`
	LikedIDs         []int64              `bson:"liked_user_ids,omitempty" json:"-"` // 只有写入时需要用到
	Likes            int                  `bson:"likes,omitempty" json:"likes"`      // 只有查询时需要用到
	Liked            bool                 `bson:"liked,omitempty" json:"liked"`      // 只有查询时需要用到
}

// Find 查询提问
// NOTICE: 该函数只有老接口在使用，返回值不包含用户点赞的状态
func Find(filter interface{}) ([]*LiveQuestion, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	questions := make([]*LiveQuestion, 0)
	pipeline := bson.A{
		bson.M{"$match": filter},
		bson.M{"$sort": bson.D{bson.E{Key: "price", Value: 1}, bson.E{Key: "created_time", Value: -1}}},
		bson.M{"$project": newLiveQuestionProjection(0)},
	}

	cur, err := Collection().Aggregate(ctx, pipeline)
	if err != nil {
		return questions, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &questions)
	if err != nil {
		return questions, err
	}
	for i := range questions {
		questions[i].afterFind()
	}

	return questions, nil
}

// FindOptions find question options
type FindOptions struct {
	FindAskUserTitles bool

	UserID int64 // 该值不为 0 时，则查询用户是否点赞
}

// FindOne 查询单一提问
func FindOne(filter interface{}, opts ...*FindOptions) (*LiveQuestion, error) {
	option := new(FindOptions)
	if len(opts) > 0 {
		option = opts[0]
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pipeline := bson.A{
		bson.M{"$match": filter},
		bson.M{"$project": newLiveQuestionProjection(option.UserID)},
		bson.M{"$limit": 1},
	}
	cur, err := Collection().Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}

	defer cur.Close(ctx)
	var questions []*LiveQuestion
	err = cur.All(ctx, &questions)
	if err != nil {
		return nil, err
	}
	if len(questions) == 0 {
		return nil, nil
	}
	question := questions[0]
	if option.FindAskUserTitles {
		err = question.buildUserTitle()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	question.afterFind()

	return question, nil
}

func (lq *LiveQuestion) afterFind() {
	lq.CreatedTimeMsec = goutil.NewTimeUnixMilli(lq.CreatedTime)
	if lq.AnsweredTime != nil {
		lq.AnsweredTimeMsec = goutil.NewTimeUnixMilli(*lq.AnsweredTime)
	}
}

func (lq *LiveQuestion) buildUserTitle() error {
	u, err := liveuser.FindOneSimple(bson.M{"user_id": lq.UserID}, &liveuser.FindOptions{
		FindTitles: true,
		RoomID:     lq.RoomID,
	})
	if err != nil {
		return err
	}
	if u != nil {
		lq.Titles = u.Titles
	}
	return nil
}

func buildUsersInfo(userIDs []int64, questions []*LiveQuestion) error {
	list, err := liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": util.Uniq(userIDs)}}, &liveuser.FindOptions{
		FindTitles: true,
		RoomID:     questions[0].RoomID,
	}, options.Find().SetProjection(liveuser.ProjectionSimple))
	if err != nil {
		return err
	}

	simpleMap, err := liveuser.SimpleSliceToMap(list)
	if err != nil {
		return err
	}

	for i := 0; i < len(questions); i++ {
		if u := simpleMap[questions[i].UserID]; u != nil {
			questions[i].Username = u.Username
			questions[i].IconURL = u.IconURL
			questions[i].Titles = u.Titles
		}
	}

	return nil
}

// FindTotalValue 查询房间内问题的总价值
func FindTotalValue(roomID int64, since time.Time) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{"room_id": roomID, "status": StatusFinished, "created_time": bson.M{"$gte": since}}},
		bson.M{"$group": bson.M{"_id": nil, "total": bson.M{"$sum": "$price"}}},
	}, nil)
	if err != nil {
		return 0, err
	}

	defer cur.Close(ctx)
	var total []struct {
		Total int64 `bson:"total"`
	}
	err = cur.All(ctx, &total)
	if err != nil {
		return 0, err
	}
	if len(total) == 0 {
		return 0, nil
	}

	return total[0].Total, nil
}

func newLiveQuestionProjection(userID int64) bson.M {
	project := bson.M{
		"_id":            1,
		"_room_id":       1,
		"room_id":        1,
		"user_id":        1,
		"username":       1,
		"iconurl":        1,
		"question":       1,
		"status":         1,
		"price":          1,
		"created_time":   1,
		"updated_time":   1,
		"answered_time":  1,
		"transaction_id": 1,
		"likes": bson.M{"$cond": bson.M{
			"if":   bson.M{"$isArray": "$liked_user_ids"},
			"then": bson.M{"$size": "$liked_user_ids"},
			"else": 0,
		}},
	}
	if userID != 0 {
		project["liked"] = bson.M{"$cond": bson.M{
			"if":   bson.M{"$isArray": "$liked_user_ids"},
			"then": bson.M{"$in": bson.A{userID, "$liked_user_ids"}},
			"else": false,
		}}
	}

	return project
}

// ListLiveQuestionsByPage 按提问状态查询 room 的非关闭的 LiveQuestion
func ListLiveQuestionsByPage(userID int64, roomID int64, since time.Time, containedStatus []int32, pagination *goutil.Pagination) (questions []*LiveQuestion, err error) {
	pipeline := bson.A{
		bson.M{"$match": bson.M{
			"room_id":      roomID,
			"status":       bson.M{"$in": containedStatus},
			"created_time": bson.M{"$gte": since},
		}},
		bson.M{"$sort": bson.D{
			bson.E{Key: "answered_time", Value: -1},
		}},
		bson.M{"$project": newLiveQuestionProjection(userID)},
	}

	// 处理分页
	if pagination != nil {
		pipeline = append(pipeline, bson.M{"$skip": pagination.Offset()}, bson.M{"$limit": pagination.Limit()})
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, pipeline)
	if err != nil {
		return questions, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &questions)
	if err != nil {
		return questions, err
	}
	if len(questions) == 0 {
		questions = make([]*LiveQuestion, 0)
		return
	}

	userIDs := make([]int64, len(questions))
	for i := 0; i < len(questions); i++ {
		userIDs[i] = questions[i].UserID
		questions[i].afterFind()
	}

	err = buildUsersInfo(userIDs, questions)
	return
}

// ListLiveQuestions 查询 room 的非关闭的 LiveQuestion
// NOTICE: 该函数返回值中不包含用户点赞的状态
func ListLiveQuestions(roomID int64, since time.Time) (queue, join, finish []*LiveQuestion, err error) {
	queue, join, finish =
		make([]*LiveQuestion, 0), make([]*LiveQuestion, 0), make([]*LiveQuestion, 0)

	questions, err := Find(bson.M{
		"room_id":      roomID,
		"status":       bson.M{"$lte": StatusFinished},
		"created_time": bson.M{"$gte": since},
	})
	if err != nil {
		return
	}
	if len(questions) == 0 {
		return
	}

	userIDs := make([]int64, len(questions))
	for i, question := range questions {
		userIDs[i] = question.UserID
		// WORKAROUND: 兼容安卓 5.5.7 iOS 4.6.8 之前的老版本
		// 由于 ObjectID 不可以通过 MarshalJSON 来触发 omitempty，*ObjectID 不能正常被转换，所以使用 Hex()
		question.ID = question.OID.Hex()
		switch questions[i].Status {
		case StatusQueued:
			queue = append(queue, questions[i])
		case StatusJoined:
			join = append(join, questions[i])
		case StatusFinished:
			finish = append(finish, questions[i])
		}
	}

	err = buildUsersInfo(userIDs, questions)
	return
}

// IsUserVisible 判断用户是否可见
func (lq LiveQuestion) IsUserVisible() bool {
	return lq.Status != StatusCanceled && lq.Status != StatusHidden
}

// CancelQuestion 取消提问
func CancelQuestion(filter bson.M, setStatus int32) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res, err := Collection().UpdateMany(ctx, filter, bson.M{"$set": bson.M{
		"status":       setStatus,
		"updated_time": goutil.TimeNow(),
	}})

	return res.ModifiedCount > 0, err
}

// CancelUnhandledQuestions 关闭所有未处理的 question
func CancelUnhandledQuestions(roomID int64, creatorID int64, c goutil.UserContext) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"room_id": roomID, "status": bson.M{"$lt": StatusFinished}}
	col := Collection()
	cur, err := col.Find(ctx, filter)
	if err != nil {
		return err
	}
	var qs []LiveQuestion
	err = cur.All(ctx, &qs)
	if err != nil {
		return err
	}
	tIDs := make([]int64, 0, len(qs))
	for i := range qs {
		if qs[i].TransactionID != 0 {
			tIDs = append(tIDs, qs[i].TransactionID)
		}
	}
	finishTIDs := make([]int64, 0, len(tIDs))
	if len(tIDs) != 0 {
		resp, err := userapi.CancelAsks(creatorID, tIDs, userapi.NewUserContext(c))
		if err != nil {
			return err
		}
		for i := range resp.Transactions {
			tid := resp.Transactions[i].TransactionID
			if resp.Transactions[i].Error != "" {
				if resp.Transactions[i].Error == "该问答不存在" {
					// 可能已经更新过了，此处也将对应提问标记成取消状态
					finishTIDs = append(finishTIDs, tid)
				}
				logger.WithField("transaction_id", tid).Error(resp.Transactions[i].Error)
				// PASS
			} else {
				finishTIDs = append(finishTIDs, tid)
			}
		}
	}
	if len(finishTIDs) != 0 {
		_, err = CancelQuestion(bson.M{
			"room_id":        roomID,
			"status":         bson.M{"$lt": StatusFinished},
			"transaction_id": bson.M{"$in": finishTIDs},
		}, StatusCanceled)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// LogCancelAsks 如果 cancelAsk 返回值中 error 不为空，则记录相应的错误
func LogCancelAsks(tIDs []int64, resp *userapi.CancelAsksResp) {
	for i := range resp.Transactions {
		if resp.Transactions[i].Error != "" {
			logger.WithField("transaction_id", tIDs[i]).Error(resp.Transactions[i].Error)
		}
	}
}

// RoomRevenueByPage 某主播的回答提问的收益记录，创建时间倒序
// 参数 times: times[0] start_time，times[1] end_time，length 超过 2 会 panic
func RoomRevenueByPage(roomID, p, pageSize int64, times ...time.Time) ([]*LiveQuestion, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"room_id": roomID,
		"status":  StatusFinished,
	}

	switch len(times) {
	case 0:
		// PASS
	case 1:
		filter["created_time"] = bson.M{
			"$gte": times[0],
		}
	case 2:
		filter["created_time"] = bson.M{
			"$gte": times[0],
			"$lt":  times[1],
		}
	default:
		panic("too many time values")
	}

	col := Collection()
	count, err := col.CountDocuments(ctx, filter)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*LiveQuestion, 0), pa, nil
	}
	cursor, err := col.Find(ctx, filter,
		pa.SetFindOptions(options.Find().SetSort(bson.M{"created_time": -1})),
		options.Find().SetProjection(bson.M{"liked_user_ids": 0}),
	)
	if err != nil {
		return nil, pa, err
	}
	defer cursor.Close(ctx)
	var results []*LiveQuestion
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, pa, err
	}
	return results, pa, nil
}

// ValidLikeOperation 合法的点赞操作
func ValidLikeOperation(operation int) bool {
	return operation == OperationDislike || operation == OperationLike
}

// UpdateLike 更新点赞
func (lq *LiveQuestion) UpdateLike(userID int64, operation int) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var update bson.M
	switch operation {
	case OperationLike:
		update = bson.M{"$addToSet": bson.M{"liked_user_ids": userID}}
	case OperationDislike:
		update = bson.M{"$pull": bson.M{"liked_user_ids": userID}}
	default:
		panic("unsupported update like operation")
	}

	res, err := Collection().UpdateOne(ctx, bson.M{"_id": lq.OID}, update)
	if err != nil {
		return err
	}
	if res.ModifiedCount == 0 {
		return nil
	}
	if operation == OperationLike {
		lq.Likes++
	} else {
		lq.Likes--
	}

	// 发送广播通知
	err = userapi.Broadcast(lq.RoomID, map[string]interface{}{
		"type":    liveim.TypeQuestion,
		"event":   liveim.EventUpdate,
		"room_id": lq.RoomID,
		"question": map[string]interface{}{
			"question_id": lq.OID,
			"likes":       lq.Likes,
		},
	})
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil
}

// BatchCancelByUserID 批量取消直播间内某一用户未回答、正在回答的提问
// oids: 批量取消的提问 OID
// transactionIDs: 批量取消的提问交易 ID, 用于退款操作
func BatchCancelByUserID(roomID, userID int64) (oids []primitive.ObjectID, transactionIDs []int64, err error) {
	questions, err := Find(bson.M{
		"user_id": userID,
		"room_id": roomID,
		"status":  bson.M{"$in": bson.A{StatusQueued, StatusJoined}},
	})
	if err != nil {
		return nil, nil, err
	}
	if len(questions) <= 0 {
		return nil, nil, nil
	}
	oids = make([]primitive.ObjectID, 0, len(questions))
	transactionIDs = make([]int64, 0, len(questions))
	for _, question := range questions {
		oids = append(oids, question.OID)
		transactionIDs = append(transactionIDs, question.TransactionID)
	}
	success, err := CancelQuestion(bson.M{
		"_id":     bson.M{"$in": oids},
		"room_id": roomID,
		"status":  bson.M{"$lt": StatusCanceled},
	}, StatusCanceled)
	if err != nil {
		return nil, nil, err
	}
	if !success {
		return nil, nil, errors.New("提问取消失败")
	}
	return oids, transactionIDs, nil
}

// RoomQuestionHistory 房间回答提问的的历史记录 (startTime <= 完成时间 < endTime)
func RoomQuestionHistory(roomID int64, startTime, endTime time.Time) ([]*LiveQuestion, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"room_id":       roomID,
		"status":        StatusFinished,
		"answered_time": bson.M{"$gte": startTime, "$lt": endTime},
	}
	cursor, err := Collection().Find(ctx, filter, options.Find().SetSort(bson.M{"answered_time": 1}))
	if err != nil {
		return nil, err
	}
	var results []*LiveQuestion
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, err
	}
	return results, nil
}
