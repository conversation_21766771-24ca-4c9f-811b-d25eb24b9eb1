package livequestion

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testRoomID    = int64(22489473)
	testRoomIDHex = "5ab9d5d9bc9b53298ce5a5a5"
)

var (
	testQueue       = make([]*LiveQuestion, 2)
	testJoin        = make([]*LiveQuestion, 1)
	testFinish      = make([]*LiveQuestion, 6)
	testLikedUserID int64
	createdTime     time.Time
	since           time.Time
	testRoomObjID   primitive.ObjectID
	medal           *livemedal.Simple
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	AddTestData()
	defer ClearTestData()
	m.Run()
}

func AddTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	collection := livemedal.Collection()
	medal = &livemedal.Simple{RoomID: 11111, CreatorID: 1234, UserID: 12, Status: livemedal.StatusShow, Point: 500}
	medal.Name = "tiny medal"
	_, err := collection.InsertOne(ctx, medal)
	if err != nil {
		panic(err)
	}

	createdTime = goutil.TimeNow()
	since = createdTime.Add(-time.Minute)
	testRoomObjID, _ = primitive.ObjectIDFromHex(testRoomIDHex)
	h := Helper{
		CreatedTime: createdTime,
		RoomID:      testRoomID,
		RoomOID:     testRoomObjID,
	}
	uid := int64(-10)
	for i := 0; i < len(testQueue); i++ {
		testQueue[i] = &LiveQuestion{Helper: h}
		testQueue[i].UserID = uid - int64(i)
		testQueue[i].Status = StatusQueued
		testQueue[i].Price = 30
		testQueue[i].LikedIDs = []int64{}
		_, err := Collection().InsertOne(ctx, testQueue[i].Helper)
		if err != nil {
			panic(err)
		}
	}
	uid = -20
	for i := 0; i < len(testJoin); i++ {
		testJoin[i] = &LiveQuestion{Helper: h}
		testJoin[i].UserID = uid - int64(i)
		testJoin[i].Status = StatusJoined
		testJoin[i].Price = 30
		testJoin[i].LikedIDs = []int64{}
		_, err := Collection().InsertOne(ctx, testJoin[i].Helper)
		if err != nil {
			panic(err)
		}
		testLikedUserID = testJoin[i].UserID
	}
	uid = -30
	inserts := make([]interface{}, len(testFinish))
	for i := 0; i < len(testFinish); i++ {
		testFinish[i] = &LiveQuestion{Helper: h}
		testFinish[i].UserID = uid - int64(i)
		testFinish[i].Status = StatusFinished
		testFinish[i].Price = 30
		testFinish[i].Question = strconv.Itoa(i)
		testFinish[i].LikedIDs = []int64{}
		inserts[i] = testFinish[i]
	}

	answeredTime1 := createdTime.Add(5 * time.Minute)
	testFinish[0].AnsweredTime = &answeredTime1

	answeredTime2 := createdTime.Add(time.Minute)
	testFinish[2].AnsweredTime = &answeredTime2

	answeredTime3 := createdTime.Add(2 * time.Minute)
	testFinish[1].AnsweredTime = &answeredTime3

	answeredTime4 := createdTime.Add(4 * time.Minute)
	testFinish[3].AnsweredTime = &answeredTime4
	testFinish[5].AnsweredTime = &answeredTime4

	answeredTime5 := answeredTime4.Add(10 * time.Second)
	testFinish[4].AnsweredTime = &answeredTime5

	_, err = Collection().InsertMany(ctx, inserts)
	if err != nil {
		panic(err)
	}
}

func ClearTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, _ = Collection().DeleteMany(ctx, bson.M{
		"room_id":  testRoomID,
		"_room_id": testRoomObjID,
	})
	_, _ = livemedal.Remove(medal.UserID, medal.CreatorID, testRoomID)
}

func TestTagKey(t *testing.T) {
	l := LiveQuestion{}
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(l, "_id")
	kc.Check(l.Helper, "_room_id", "room_id", "user_id", "username",
		"iconurl", "question", "price", "status", "created_time", "updated_time", "answered_time", "transaction_id",
		"vip_type", "noble_level", "liked_user_ids", "likes", "liked")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(l, "_id", "question_id", "titles")
	kc.Check(l.Helper, "room_id", "user_id", "username",
		"iconurl", "question", "price", "status", "created_time", "updated_time", "answered_time", "likes", "liked")
}

func TestAfterFind(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	lq := &LiveQuestion{
		Helper: Helper{
			CreatedTime:  now,
			AnsweredTime: &now,
		},
	}

	lq.afterFind()
	assert.NotNil(lq.AnsweredTime)
	assert.Equal(goutil.NewTimeUnixMilli(now), lq.CreatedTimeMsec)
	assert.Equal(goutil.NewTimeUnixMilli(now), lq.AnsweredTimeMsec)

	lq.AnsweredTime = nil
	lq.AnsweredTimeMsec = 0
	lq.afterFind()
	assert.Zero(lq.AnsweredTimeMsec)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid := primitive.NewObjectIDFromTimestamp(goutil.TimeNow())
	question, err := FindOne(bson.M{"_id": oid})
	require.NoError(err)
	require.Nil(question)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	const testUserID = int64(12)
	lq := &LiveQuestion{
		OID: oid,
		Helper: Helper{
			CreatedTime: createdTime,
			RoomID:      testRoomID + 1,
			RoomOID:     testRoomObjID,
			UserID:      testUserID,
			LikedIDs:    []int64{},
		},
	}

	filter := bson.M{"_id": lq.OID}
	defer func() {
		_, err := Collection().DeleteOne(ctx, filter)
		assert.NoError(err)
	}()
	_, err = Collection().InsertOne(ctx, lq)
	require.NoError(err)

	question, err = FindOne(filter, &FindOptions{FindAskUserTitles: true, UserID: testUserID})
	require.NoError(err)
	require.NotNil(question)
	assert.Equal(lq.OID, question.OID)
	assert.NotEmpty(question.Titles)
	assert.Equal(0, question.Likes)

	err = question.UpdateLike(testUserID, OperationLike)
	require.NoError(err)

	question, err = FindOne(filter, &FindOptions{UserID: testUserID})
	require.NoError(err)
	require.NotNil(question)
	assert.Equal(lq.OID, question.OID)
	assert.Equal(1, question.Likes)
}

func TestBuildUsersInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	questions := []*LiveQuestion{
		{Helper: Helper{UserID: 12}},
	}

	err := buildUsersInfo([]int64{12}, questions)
	require.NoError(err)
	require.Len(questions, 1)
	assert.NotEmpty(questions[0].Username)
	assert.NotEmpty(questions[0].IconURL)
	assert.NotEmpty(questions[0].Titles)
}

func TestFindTotalValue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	value, err := FindTotalValue(testRoomID, createdTime)
	require.NoError(err)
	require.NotZero(value)
	assert.Equal(int64(len(testFinish)*30), value)
}

func TestListLiveQuestionsByPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ClearTestData()
	q, err := ListLiveQuestionsByPage(0, testRoomID, since, []int32{StatusFinished}, nil)
	require.NoError(err)
	require.Empty(q)

	AddTestData()
	totalQuestions := append(testQueue, testJoin...)
	q, err = ListLiveQuestionsByPage(0, testRoomID, since, []int32{StatusFinished}, nil)
	require.NoError(err)
	require.Equal(len(testFinish), len(q))
	finishedQuestions := make([]string, 0)
	for i := 0; i < len(testFinish); i++ {
		finishedQuestions = append(finishedQuestions, q[i].Question)
	}
	// 排序检查
	assert.Equal([]string{"0", "4", "3", "5", "1", "2"}, finishedQuestions)

	questions, err := Find(bson.M{"_room_id": testRoomObjID, "status": StatusJoined})
	require.NoError(err)
	for _, question := range questions {
		if question.Status == StatusJoined {
			err := question.UpdateLike(testLikedUserID, OperationLike)
			assert.Equal(1, question.Likes)
			require.NoError(err)
		}
	}

	q, err = ListLiveQuestionsByPage(testLikedUserID, testRoomID, since, []int32{StatusQueued, StatusJoined}, nil)
	require.NoError(err)
	require.Len(q, len(totalQuestions))
	for i := 0; i < len(totalQuestions); i++ {
		assert.Equal(totalQuestions[i].UserID, q[i].UserID)
		if q[i].UserID == 12 {
			assert.NotZero(len(q[i].Titles))
			assert.NotZero(q[i].CreatedTimeMsec)
		}

		// 检查点赞状态
		if q[i].Status == StatusJoined {
			assert.Equal(1, q[i].Likes)
			assert.True(q[i].Liked)
			assert.NotZero(q[i].CreatedTimeMsec)
		}
	}
}

func TestListLiveQuestions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	q, j, f, err := ListLiveQuestions(testRoomID, since)
	require.NoError(err)
	require.Equal(len(testQueue), len(q))
	require.Equal(len(testJoin), len(j))
	require.Equal(len(testFinish), len(f))
	for i := 0; i < len(testQueue); i++ {
		assert.Equal(testQueue[i].UserID, q[i].UserID)
		if q[i].UserID == 12 {
			assert.NotZero(len(q[i].Titles))
			assert.NotZero(q[i].CreatedTimeMsec)
		}
	}
	for i := 0; i < len(testJoin); i++ {
		assert.Equal(testJoin[i].UserID, j[i].UserID)
		assert.NotZero(j[i].CreatedTimeMsec)
	}
	for i := 0; i < len(testFinish); i++ {
		assert.Equal(testFinish[i].UserID, f[i].UserID)
		assert.NotZero(f[i].CreatedTimeMsec)
	}
}

func TestCancelUnhandledQuestions(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	isCalled := false
	cleanup := mrpc.SetMock(userapi.URICancelAsks, func(input interface{}) (interface{}, error) {
		isCalled = true
		return &userapi.CancelAsksResp{
			Transactions: []userapi.CancelAsksTransaction{
				{
					TransactionID: 123,
					Price:         10,
				},
				{
					TransactionID: 234,
					Error:         "该问答不存在",
				},
			},
		}, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	_, err := col.DeleteOne(ctx, bson.M{"room_id": 1234, "status": StatusQueued, "transaction_id": 123})
	require.NoError(err)
	_, err = col.DeleteOne(ctx, bson.M{"room_id": 1234, "status": StatusJoined, "transaction_id": 234})
	require.NoError(err)
	res, err := col.InsertOne(ctx, bson.M{"room_id": 1234, "status": StatusQueued, "transaction_id": 123})
	require.NoError(err)
	res2, err := col.InsertOne(ctx, bson.M{"room_id": 1234, "status": StatusJoined, "transaction_id": 234})
	require.NoError(err)
	now := goutil.TimeNow().Add(-time.Second)
	require.NoError(CancelUnhandledQuestions(1234, 12, goutil.SmartUserContext{}))
	var q LiveQuestion
	err = col.FindOne(ctx, bson.M{"_id": res.InsertedID}).Decode(&q)
	require.NoError(err)
	assert.Equal(StatusCanceled, q.Status)
	assert.True(q.UpdatedTime.After(now))
	var q2 LiveQuestion
	err = col.FindOne(ctx, bson.M{"_id": res2.InsertedID}).Decode(&q2)
	require.NoError(err)
	assert.Equal(StatusCanceled, q2.Status)
	assert.True(isCalled)
}

func TestIsUserVisible(t *testing.T) {
	assert := assert.New(t)

	var lq LiveQuestion
	lq.Status = StatusQueued
	assert.True(lq.IsUserVisible())

	lq.Status = StatusJoined
	assert.True(lq.IsUserVisible())

	lq.Status = StatusFinished
	assert.True(lq.IsUserVisible())

	lq.Status = StatusCanceled
	assert.False(lq.IsUserVisible())

	lq.Status = StatusHidden
	assert.False(lq.IsUserVisible())
}

func TestCancelQuestion(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URICancelAsks, func(input interface{}) (interface{}, error) {
		v, ok := input.(map[string]interface{})
		require.True(ok)
		tids, ok := v["transaction_ids"].([]int64)
		require.True(ok)
		return &userapi.CancelAsksResp{Transactions: make([]userapi.CancelAsksTransaction, len(tids))}, nil
	})
	defer cancel()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res, err := Collection().InsertOne(ctx, bson.M{"room_id": 1234, "status": StatusFinished, "transaction_id": 123})
	defer func() {
		_, err = Collection().DeleteOne(ctx, bson.M{"_id": res.InsertedID})
		assert.NoError(err)
	}()
	require.NoError(err)

	var q LiveQuestion
	err = Collection().FindOne(ctx, bson.M{"_id": res.InsertedID}).Decode(&q)

	success, err := CancelQuestion(bson.M{"_id": res.InsertedID}, StatusHidden)
	assert.True(success)

	err = Collection().FindOne(ctx, bson.M{"_id": res.InsertedID}).Decode(&q)
	require.NoError(err)
	assert.Equal(StatusHidden, q.Status)
}

func TestRoomRevenueByPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, pa, err := RoomRevenueByPage(testRoomID, 1, 1, goutil.TimeNow())
	require.NoError(err)
	assert.Empty(res)

	res, pa, err = RoomRevenueByPage(testRoomID, 1, 1, time.Unix(0, 0), goutil.TimeNow())
	require.NoError(err)
	assert.NotEmpty(res)

	res, pa, err = RoomRevenueByPage(testRoomID, 1, 1)
	require.NoError(err)
	assert.Len(res, 1)
	assert.EqualValues(1, pa.P)
	assert.EqualValues(1, pa.PageSize)

	res, pa, err = RoomRevenueByPage(testRoomID, 10, 100)
	require.NoError(err)
	assert.Empty(res)
	assert.EqualValues(10, pa.P)
	assert.EqualValues(100, pa.PageSize)
}

func TestUpdateLike(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	const testUserID = int64(1221)
	lq := &LiveQuestion{
		OID: primitive.NewObjectIDFromTimestamp(goutil.TimeNow()),
		Helper: Helper{
			CreatedTime: createdTime,
			RoomID:      testRoomID,
			RoomOID:     testRoomObjID,
			UserID:      testUserID,
			LikedIDs:    []int64{},
		},
	}

	filter := bson.M{"_id": lq.OID}
	defer func() {
		_, err := Collection().DeleteOne(ctx, filter)
		assert.NoError(err)
	}()
	_, err := Collection().InsertOne(ctx, lq)
	require.NoError(err)

	err = lq.UpdateLike(testUserID, OperationLike)
	require.NoError(err)
	assert.Equal(1, lq.Likes)

	question, err := FindOne(filter, &FindOptions{UserID: testUserID})
	require.NoError(err)
	require.NotNil(question)
	assert.Equal(1, question.Likes)

	err = question.UpdateLike(testUserID, OperationDislike)
	require.NoError(err)
	assert.Zero(question.Likes)

	question, err = FindOne(filter, &FindOptions{UserID: testUserID})
	require.NoError(err)
	require.NotNil(question)
	assert.Equal(0, question.Likes)

	assert.PanicsWithValue("unsupported update like operation", func() {
		assert.NoError(question.UpdateLike(testUserID, 3))
	})
}

func TestBatchCancelByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": 10101})
	require.NoError(err)
	result, err := Collection().InsertOne(ctx, &LiveQuestion{
		Helper: Helper{
			RoomID:        223344,
			UserID:        10101,
			TransactionID: 111111,
			Status:        StatusQueued,
		},
	})
	require.NoError(err)

	oids, tids, err := BatchCancelByUserID(223344, 10101)
	require.NoError(err)
	require.Len(tids, 1)
	require.Len(oids, 1)
	assert.Equal(result.InsertedID.(primitive.ObjectID), oids[0])

	oids, tids, err = BatchCancelByUserID(223344, 1)
	require.NoError(err)
	assert.Nil(tids)
	assert.Nil(oids)
}

func TestRoomQuestionHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(123)
		now        = goutil.TimeNow()
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	res, err := RoomQuestionHistory(testRoomID, now, now.Add(time.Minute))
	require.NoError(err)
	assert.Empty(res)

	_, err = Collection().InsertOne(ctx, &LiveQuestion{
		Helper: Helper{
			RoomID:        testRoomID,
			TransactionID: 111111,
			Status:        StatusFinished,
			AnsweredTime:  &now,
		},
	})
	require.NoError(err)
	res, err = RoomQuestionHistory(testRoomID, now, now.Add(time.Minute))
	require.NoError(err)
	assert.NotEmpty(res)
}
