package liveactivityjob

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTag(t *testing.T) {
	checker := tutil.NewKeyChecker(t, tutil.GORM)
	checker.Check(ActivityJob{},
		"id", "create_time", "modified_time", "delete_time",
		"event_id", "intro", "run_time", "more",
	)
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	// 测试包级别的 TableName 函数
	assert.Equal("activity_job", TableName())
	// 测试 Job 结构体的 TableName 方法
	job := &ActivityJob{}
	assert.Equal("activity_job", job.TableName())
}

func TestFindByTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理测试数据
	require.NoError(service.LiveDB.Delete(&ActivityJob{}).Error)

	// 插入测试数据
	now := goutil.TimeNow()
	pastTime := now.Add(-1 * time.Hour)
	futureTime := now.Add(1 * time.Hour)

	// 创建过去的任务
	pastJob := &ActivityJob{
		EventID:      101,
		Intro:        "过去的任务",
		RunTime:      pastTime.Unix(),
		More:         `{"test": "past"}`,
		CreateTime:   now.Add(-2 * time.Hour).Unix(),
		ModifiedTime: now.Add(-2 * time.Hour).Unix(),
	}

	// 创建将来的任务
	futureJob := &ActivityJob{
		EventID:      102,
		Intro:        "将来的任务",
		RunTime:      futureTime.Unix(),
		More:         `{"test": "future"}`,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}

	// 插入测试数据
	require.NoError(service.LiveDB.Create(pastJob).Error)
	require.NoError(service.LiveDB.Create(futureJob).Error)

	// 测试 FindbyTime 函数
	jobs, err := FindByTime(now)
	require.NoError(err)

	// 验证查询结果
	assert.Len(jobs, 1, "应该只查询到一个将来的任务")
	if len(jobs) > 0 {
		assert.Equal(int64(102), jobs[0].EventID)
		assert.Equal("将来的任务", jobs[0].Intro)
		assert.Equal(futureTime.Unix(), jobs[0].RunTime)
	}

	// 测试查询过去的时间点
	pastJobs, err := FindByTime(pastTime.Add(-1 * time.Minute))
	require.NoError(err)
	assert.Len(pastJobs, 2, "应该查询到两个任务")

	// 测试查询将来的时间点
	futureJobs, err := FindByTime(futureTime.Add(1 * time.Minute))
	require.NoError(err)
	assert.Len(futureJobs, 0, "不应该查询到任务")
}
