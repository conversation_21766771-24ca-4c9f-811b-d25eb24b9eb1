package liveactivityjob

import (
	"time"

	"github.com/MiaoSiLa/live-service/service"
)

// ActivityJob .
type ActivityJob struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	DeleteTime   int64 `gorm:"column:delete_time"`

	EventID int64  `gorm:"column:event_id"`
	Intro   string `gorm:"column:intro"`
	RunTime int64  `gorm:"column:run_time"` // 任务执行时间, 单位: 秒
	More    string `gorm:"column:more"`
}

// TableName .
func TableName() string {
	return "live_activity_job"
}

// TableName .
func (*ActivityJob) TableName() string {
	return TableName()
}

// FindByTime 根据时间查询未执行的任务
func FindByTime(now time.Time) ([]*ActivityJob, error) {
	var jobs []*ActivityJob
	err := service.LiveDB.Where("run_time >= ? AND delete_time = 0", now.Unix()).Find(&jobs).Error
	return jobs, err
}
