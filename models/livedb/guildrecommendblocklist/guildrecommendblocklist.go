package guildrecommendblocklist

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// block type
const (
	TypeElementRecommendLive  = iota + 1 // 资源位直播间黑名单（elementID 为直播间 ID）
	TypeElementRecommendGuild            // 资源位公会黑名单（elementID 为公会 ID）
)

// TableName for guild_recommend_blocklist
func TableName() string {
	return "guild_recommend_blocklist"
}

// GuildRecommendBlocklist 直播黑名单
type GuildRecommendBlocklist struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	ElementID   int64 `gorm:"column:element_id"`
	ElementType int   `gorm:"column:element_type"`
}

// TableName for current model
func (GuildRecommendBlocklist) TableName() string {
	return TableName()
}

// DB the db instance of GuildRecommendBlocklist model
func (g GuildRecommendBlocklist) DB() *gorm.DB {
	return service.LiveDB.Table(g.TableName())
}

// BeforeSave gorm hook
func (g *GuildRecommendBlocklist) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if g.DB().NewRecord(g) {
		g.CreateTime = nowTime
	}
	g.ModifiedTime = nowTime
	return
}

// Add 添加黑名单
func Add(elementType int, elementID int64) error {
	return GuildRecommendBlocklist{}.DB().Create(&GuildRecommendBlocklist{
		ElementType: elementType,
		ElementID:   elementID,
	}).Error
}

// Remove 移除黑名单
// TODO: 考虑使用软删除
func Remove(elementType int, elementID int64) error {
	return GuildRecommendBlocklist{}.DB().Delete("", "element_type = ? AND element_id = ?", elementType, elementID).Error
}

// Exists 判断封禁是否存在
func Exists(elementType int, elementID int64) (bool, error) {
	var lb GuildRecommendBlocklist
	err := GuildRecommendBlocklist{}.DB().Where("element_type = ? AND element_id = ?", elementType, elementID).
		First(&lb).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// ExistsAny 是否在资源位黑名单中，包含主播和公会黑名单
func ExistsAny(roomID, guildID int64) (bool, error) {
	if roomID == 0 && guildID == 0 {
		panic("invalid roomID and invalid guildID")
	}
	var lb GuildRecommendBlocklist
	db := GuildRecommendBlocklist{}.DB()
	if roomID != 0 {
		db = db.Where("element_type = ? AND element_id = ?", TypeElementRecommendLive, roomID)
	}
	if guildID != 0 {
		db = db.Or("element_type = ? AND element_id = ?", TypeElementRecommendGuild, guildID)
	}
	err := db.First(&lb).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
