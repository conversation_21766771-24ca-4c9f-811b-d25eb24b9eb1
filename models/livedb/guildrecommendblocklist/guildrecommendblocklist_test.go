package guildrecommendblocklist

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestBlock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(-12345)
	testGuildID := int64(-1)
	t.Run("Add", func(t *testing.T) {
		require.NoError(Add(TypeElementRecommendLive, testRoomID))
		require.NoError(Add(TypeElementRecommendGuild, testGuildID))
	})
	t.Run("Exists", func(t *testing.T) {
		exists, err := Exists(TypeElementRecommendLive, testRoomID)
		require.NoError(err)
		assert.True(exists)
		exists, err = Exists(TypeElementRecommendLive, testGuildID)
		require.NoError(err)
		assert.False(exists)
	})
	t.Run("ExistsAny", func(t *testing.T) {
		exists, err := ExistsAny(testRoomID, testGuildID)
		require.NoError(err)
		assert.True(exists)

		exists, err = ExistsAny(testRoomID, -1)
		require.NoError(err)
		assert.True(exists)

		exists, err = ExistsAny(testRoomID, 0)
		require.NoError(err)
		assert.True(exists)

		exists, err = ExistsAny(-1, 1)
		require.NoError(err)
		assert.False(exists)

		assert.PanicsWithValue("invalid roomID and invalid guildID", func() {
			_, _ = ExistsAny(0, 0)
		})
	})
	t.Run("Remove", func(t *testing.T) {
		require.NoError(Remove(TypeElementRecommendLive, testRoomID))
		require.NoError(Remove(TypeElementRecommendLive, testGuildID))
	})
}
