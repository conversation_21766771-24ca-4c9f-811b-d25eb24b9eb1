package livenewuserrewardrecord

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Status 发放状态
const (
	StatusInvalid = iota // 无资格
	StatusGranted        // 已发放
)

// LiveNewUserRewardRecord 直播新用户奖励记录
type LiveNewUserRewardRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	UserID   int64  `gorm:"column:user_id"`
	RewardID int64  `gorm:"column:reward_id"`
	BUVID    string `gorm:"column:buvid"`
	Status   int    `gorm:"column:status"`
}

// TableName table name
func (LiveNewUserRewardRecord) TableName() string {
	return "live_new_user_reward_record"
}

// BeforeSave .
func (l *LiveNewUserRewardRecord) BeforeSave() error {
	nowUnix := util.TimeNow().Unix()
	l.ModifiedTime = nowUnix
	if DB().NewRecord(l) {
		l.CreateTime = nowUnix
	}
	return nil
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// Create 创建领取记录
func (l *LiveNewUserRewardRecord) Create() error {
	return DB().Create(l).Error
}

// FindUserRewardRecord 查询用户奖励记录
func FindUserRewardRecord(userID int64) (*LiveNewUserRewardRecord, error) {
	var record LiveNewUserRewardRecord
	err := DB().Where("user_id = ?", userID).Take(&record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}

	return &record, err
}
