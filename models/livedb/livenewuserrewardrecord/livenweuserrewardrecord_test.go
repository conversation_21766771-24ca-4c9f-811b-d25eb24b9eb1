package livenewuserrewardrecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&LiveNewUserRewardRecord{}, "id", "create_time", "modified_time",
		"user_id", "reward_id", "buvid", "status")
}

func TestFindUserRewardRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(123456)
	r, err := FindUserRewardRecord(testUserID)
	require.NoError(err)
	assert.Nil(r)

	record := LiveNewUserRewardRecord{
		UserID:   testUserID,
		RewardID: 20,
		BUVID:    "XX31182368CF7A3BE1EBF6999B713F7B4F77D",
		Status:   StatusGranted,
	}
	require.NoError(record.Create())

	r, err = FindUserRewardRecord(testUserID)
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(StatusGranted, r.Status)
}
