package livetagcontrollist

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveTagControlList{}, "id", "create_time", "modified_time", "tag_id", "room_id", "status")
}

func TestIsAllowRoomAddTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	allow, err := IsAllowRoomAddTag(24, 9999)
	require.NoError(err)
	assert.True(allow)

	allow, err = IsAllowRoomAddTag(1, 1)
	require.NoError(err)
	assert.False(allow)
}

func TestControlList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, nil)
	require.NoError(err)
	assert.Equal(2, len(list))

	list, err = ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, []int64{9999, 22489473})
	require.NoError(err)
	assert.Equal(2, len(list))

	list, err = ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, []int64{1312, 4, 45, 1})
	require.NoError(err)
	assert.Empty(list)
}

func TestAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, nil)
	require.NoError(err)
	assert.Equal(2, len(list))

	err = Add(tag.TagListenDrama, StatusAllowRoomAddTag, []int64{110, 111})
	require.NoError(err)

	list, err = ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, nil)
	require.NoError(err)
	assert.Equal(4, len(list))
}

func TestRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, nil)
	require.NoError(err)
	assert.Equal(4, len(list))

	err = Remove(tag.TagListenDrama, StatusAllowRoomAddTag, []int64{list[0].RoomID, list[1].RoomID})
	require.NoError(err)

	list, err = ControlList(tag.TagListenDrama, StatusAllowRoomAddTag, nil)
	require.NoError(err)
	assert.Equal(2, len(list))
}
