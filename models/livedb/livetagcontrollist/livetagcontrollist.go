package livetagcontrollist

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// status
const (
	StatusAllowRoomAddTag = iota + 1 // 允许主播自行添加 tag 的白名单（目前只有 pia 戏使用）
)

// LiveTagControlList tag 控制列表
type LiveTagControlList struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	TagID  int64 `gorm:"column:tag_id"`
	RoomID int64 `gorm:"column:room_id"`
	Status int   `gorm:"column:status"` // 1: 允许
}

// TableName .
func (LiveTagControlList) TableName() string {
	return "live_tag_control_list"
}

// BeforeSave .
func (tcl *LiveTagControlList) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	tcl.ModifiedTime = nowUnix
	if DB().NewRecord(tcl) {
		tcl.CreateTime = nowUnix
	}
	return nil
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// IsAllowRoomAddTag 是否允许主播自行添加 tag
func IsAllowRoomAddTag(tagID, roomID int64) (bool, error) {
	return servicedb.Exists(
		DB().Model(&LiveTagControlList{}).
			Where("tag_id = ? AND room_id = ? AND status = ?", tagID, roomID, StatusAllowRoomAddTag),
	)
}

// ControlList 获取控制标签的用户列表
// NOTICE: 若 room_ids 为空，则返回标签下所有允许或拒绝的用户列表
func ControlList(tagID int64, status int, roomIDs []int64) ([]*LiveTagControlList, error) {
	var allowList []*LiveTagControlList
	db := DB().Where("tag_id = ? AND status = ?", tagID, status)
	if len(roomIDs) != 0 {
		db = db.Where("room_id IN (?)", roomIDs)
	}
	err := db.Find(&allowList).Error
	if err != nil {
		return nil, err
	}
	return allowList, nil
}

// Add 添加控制标签的用户
func Add(tagID int64, status int, roomIDs []int64) error {
	var allowList []*LiveTagControlList
	nowUnix := goutil.TimeNow().Unix()
	for _, roomID := range roomIDs {
		allowList = append(allowList, &LiveTagControlList{
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			TagID:        tagID,
			RoomID:       roomID,
			Status:       status,
		})
	}
	return servicedb.BatchInsert(DB(), new(LiveTagControlList).TableName(), allowList)
}

// Remove 移除控制标签的用户
func Remove(tagID int64, status int, roomIDs []int64) error {
	return DB().
		Where("tag_id = ? AND status = ?", tagID, status).
		Where("room_id IN (?)", roomIDs).
		Delete(LiveTagControlList{}).Error
}
