package liveluckygiftdrop

import (
	"math"
	"math/rand"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Config 随机礼物投放配置表
type Config struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 更新时间，单位：秒
	DeleteTime   int64 `gorm:"column:delete_time"`   // 删除时间，默认 0 未删除，单位：秒

	StartTime         int64 `gorm:"column:start_time"`           // 开始时间，单位：秒
	EndTime           int64 `gorm:"column:end_time"`             // 结束时间，单位：秒
	GiftID            int64 `gorm:"column:gift_id"`              // 随机礼物 ID
	GiftNum           int64 `gorm:"column:gift_num"`             // 随机礼物档位
	PrizeGiftID       int64 `gorm:"column:prize_gift_id"`        // 投放礼物 ID
	PrizeGiftTotalNum int64 `gorm:"column:prize_gift_total_num"` // 投放礼物总数量
}

// TableName .
func (Config) TableName() string {
	return "live_lucky_gift_drop_config"
}

const (
	singleGiftDropInterval = 5   // 单个奖品发放的持续时间（单位：秒）
	randomLeftOffsetRate   = 0.5 // 随机值左偏移率
	randomRightOffsetRate  = 1.5 // 随机值右偏移率
)

// FindCurrentConfig 查询生效中的配置
func FindCurrentConfig(giftID, giftNum int64) (*Config, error) {
	var config Config
	nowUnix := goutil.TimeNow().Unix()
	err := DB().Where("gift_id = ? AND gift_num = ? AND start_time <= ? AND end_time > ?",
		giftID, giftNum, nowUnix, nowUnix).
		Where("delete_time = ?", 0).
		Take(&config).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &config, nil
}

// InsertConfigs 批量插入配置
func InsertConfigs(configs []*Config) error {
	return servicedb.SplitBatchInsert(DB(), Config{}.TableName(), configs, 1000, false)
}

// FindConfigByID 根据 ID 查询指定配置
func FindConfigByID(id int64) (*Config, error) {
	var config Config
	err := DB().Table(Config{}.TableName()).Take(&config, "id = ?", id).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &config, nil
}

// FindValidConfigs 查询所有有效的配置
func FindValidConfigs() ([]*Config, error) {
	now := goutil.TimeNow()
	var configs []*Config
	err := DB().Table(Config{}.TableName()).
		Where("delete_time = 0 AND end_time > ?", now.Unix()).
		Find(&configs).Error
	return configs, err
}

// DeleteConfigs 批量删除配置
func DeleteConfigs(configIDs []int64) (bool, error) {
	now := goutil.TimeNow()
	db := DB().Table(Config{}.TableName()).
		Where("id IN (?) AND delete_time = 0", configIDs).
		Updates(map[string]any{
			"delete_time":   now.Unix(),
			"modified_time": now.Unix(),
		})
	return db.RowsAffected == int64(len(configIDs)), db.Error
}

// GiftDrop 计算奖品掉落时间线
// prevDropTime 上一个掉落期望踩中时间（单位：秒），currentTime 当前时间，remainPrizeNum 剩余奖品数
// GiftDropResult 封装奖品掉落时间线计算结果
func (config Config) GiftDrop(prevDropTime, currentTime, remainPrizeNum int64) GiftDropResult {
	result := GiftDropResult{}
	// 检查基本条件，如果不符合则直接返回
	if currentTime == 0 || currentTime >= config.EndTime || remainPrizeNum <= 0 {
		return result
	}

	// 判断 prevDropTime 是否存在
	if prevDropTime > 0 {
		result.NextDropExpectTime = prevDropTime
	}

	// 检查并处理所有过期的掉落时间
	for result.isAfterNextGiftDrop(currentTime) {
		result.NextDropExpectTime = config.nextGiftDropTime(result.NextDropExpectTime, remainPrizeNum)
		if result.NextDropExpectTime == 0 {
			return result
		}
		if result.NextDropExpectTime+singleGiftDropInterval >= currentTime {
			break
		}
		// 记录过期掉落时间
		result.ExpiredDropTimes = append(result.ExpiredDropTimes, result.NextDropExpectTime)
	}

	// 判断当前时间是否踩中掉落时间
	if !result.isHitNextGiftDrop(currentTime) {
		// 未踩中
		return result
	}
	// 记录实际踩中时间
	result.ActualTime = currentTime
	result.ExpectTime = result.NextDropExpectTime
	// 计算下一个掉落时间，从当前用户踩中的时间点开始算，保证新生成的有效掉落时间肯定大于 currentTime
	result.NextDropExpectTime = config.nextGiftDropTime(currentTime, remainPrizeNum-1)
	// 记录是否踩中的是上一个生成的掉落期望时间
	result.IsHitPrevDropTime = result.ExpectTime == prevDropTime
	return result
}

// nextGiftDropTime 返回下一个掉落时间，掉落范围为 [config.StartTime, config.EndTime)
// dropStartTime 计算掉落开始时间（首次触发时值为 0，单位：秒），remainPrizeNum 剩余奖品数量
func (config Config) nextGiftDropTime(dropStartTime, remainPrizeNum int64) int64 {
	// 判断是否首次触发
	if dropStartTime < config.StartTime {
		dropStartTime = config.StartTime
	}
	if remainPrizeNum <= 0 || dropStartTime >= config.EndTime {
		return 0
	}
	remainDuration := config.EndTime - dropStartTime - 1
	if remainDuration <= 0 {
		return 0
	}
	remainAvgDuration := float64(remainDuration) / float64(remainPrizeNum)

	// 生成随机数范围，计算公式：起始时间 + avg + random(-0.5*avg, 0.5*avg)
	minOffset := remainAvgDuration * randomLeftOffsetRate
	maxOffset := remainAvgDuration * randomRightOffsetRate

	// 异常情况处理
	// 1. maxOffset 超出配置的投放时间，maxOffset 取投放剩余时间
	// 2. maxOffset 和 minOffset 向上取整后值相同时，不具备随机性了，maxOffset 取投放剩余时间
	if maxOffset > float64(remainDuration) || math.Ceil(maxOffset)-math.Ceil(minOffset) <= 0 {
		maxOffset = float64(remainDuration)
	}

	// 异常情况处理结束后，maxOffset 和 minOffset 向上取整后值相同时，说明剩余时间已经不足以随机投放了
	if math.Ceil(maxOffset)-math.Ceil(minOffset) <= 0 {
		return 0
	}

	randomValue := minOffset + rand.Float64()*(maxOffset-minOffset)
	nextTime := dropStartTime + int64(math.Ceil(randomValue))
	if nextTime >= config.EndTime {
		return 0
	}
	return nextTime
}

// GiftDropResult 奖品掉落计算结果
type GiftDropResult struct {
	ExpiredDropTimes   []int64 // 过期的掉落时间列表（需记录落库）（单位：秒）
	ActualTime         int64   // 实际踩中掉落的时间（为 0 说明用户未踩中）（单位：秒）
	ExpectTime         int64   // ActualTime 对应的掉落时间（为 0 说明用户未踩中）（单位：秒）
	NextDropExpectTime int64   // 下一个掉落时间，为 0 则表示没有（单位：秒）
	IsHitPrevDropTime  bool    // 标记是否踩中上一个生成的掉落期望时间 prevDropTime

	PrizeGiftID int64 // 投放礼物 ID
	HitRecordID int64 // 命中掉落奖品的记录 ID
}

// IsHit 是否命中掉落礼物
func (result GiftDropResult) IsHit() bool {
	return result.ActualTime != 0
}

// isHitNextGiftDrop 是否踩中下一个掉落时间区间
// 掉落时间区间为 [nextDropExpectTime, nextDropExpectTime+singleGiftDropInterval)
func (result GiftDropResult) isHitNextGiftDrop(currentTime int64) bool {
	return result.NextDropExpectTime <= currentTime && currentTime < result.NextDropExpectTime+singleGiftDropInterval
}

// isAfterNextGiftDrop 是否在下一个掉落时间区间之后
// 掉落时间区间为 [nextDropExpectTime, nextDropExpectTime+singleGiftDropInterval)
func (result GiftDropResult) isAfterNextGiftDrop(currentTime int64) bool {
	return currentTime >= result.NextDropExpectTime+singleGiftDropInterval
}

// NewRecords 根据掉落时间线计算结果返回需要新插入的记录
func (result GiftDropResult) NewRecords(configID, currentTime int64) []Record {
	newRecords := make([]Record, 0, len(result.ExpiredDropTimes)+1)
	// 插入过期轮空的时间点
	for _, expireDropTime := range result.ExpiredDropTimes {
		newRecords = append(newRecords, Record{
			CreateTime:   currentTime,
			ModifiedTime: currentTime,
			ConfigID:     configID,
			ExpectedTime: expireDropTime,
		})
	}

	// 插入下一个投放时间点的数据
	if result.NextDropExpectTime != 0 {
		newRecords = append(newRecords, Record{
			CreateTime:   currentTime,
			ModifiedTime: currentTime,
			ConfigID:     configID,
			ExpectedTime: result.NextDropExpectTime,
		})
	}
	return newRecords
}
