package liveluckygiftdrop

import (
	"math"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTableName_Config(t *testing.T) {
	assert.Equal(t, "live_lucky_gift_drop_config", Config{}.TableName())
}

func TestTags_Config(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Config{}, "id", "create_time", "modified_time", "delete_time", "start_time", "end_time", "gift_id", "gift_num", "prize_gift_id", "prize_gift_total_num")
}

func TestFindCurrentConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(Config{}).Error
	require.NoError(err)

	now := goutil.TimeNow()
	configs := []Config{
		{StartTime: now.Unix(), EndTime: now.Add(time.Minute).Unix(), GiftID: 80001, GiftNum: 1},
		{StartTime: now.Add(-time.Minute).Unix(), EndTime: now.Add(time.Minute).Unix(), GiftID: 80002, GiftNum: 1},
		{StartTime: now.Add(-time.Hour).Unix(), EndTime: now.Add(-time.Minute).Unix(), GiftID: 80003, GiftNum: 1},
		{StartTime: now.Add(time.Minute).Unix(), EndTime: now.Add(time.Hour).Unix(), GiftID: 80004, GiftNum: 1},
	}
	err = servicedb.BatchInsert(DB(), Config{}.TableName(), configs)
	require.NoError(err)

	config, err := FindCurrentConfig(80001, 1)
	require.NoError(err)
	require.NotNil(config)
	assert.EqualValues(80001, config.GiftID)

	config, err = FindCurrentConfig(80002, 1)
	require.NoError(err)
	require.NotNil(config)
	assert.EqualValues(80002, config.GiftID)

	config, err = FindCurrentConfig(80003, 1)
	require.NoError(err)
	require.Nil(config)

	config, err = FindCurrentConfig(80004, 1)
	require.NoError(err)
	require.Nil(config)
}

func TestInsertConfigs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(Config{}).Error)

	testConf := Config{ID: 19198, GiftID: 80001}
	require.NoError(InsertConfigs([]*Config{&testConf}))

	var conf Config
	require.NoError(DB().Where("id = ?", testConf.ID).Take(&conf).Error)
	assert.Equal(testConf.GiftID, conf.GiftID)
}

func TestFindConfigByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(Config{}).Error)
	testConf := Config{ID: 19198, GiftID: 80001}
	require.NoError(DB().Create(&testConf).Error)

	conf, err := FindConfigByID(testConf.ID)
	require.NoError(err)
	assert.Equal(testConf.GiftID, conf.GiftID)
}

func TestFindValidConfigs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(Config{}).Error)

	configs, err := FindValidConfigs()
	require.NoError(err)
	assert.Empty(configs)

	now := goutil.TimeNow()
	configs = []*Config{
		{EndTime: now.Add(time.Minute).Unix(), GiftID: 80001},
		{EndTime: now.Add(-time.Minute).Unix(), GiftID: 80002},
	}
	require.NoError(servicedb.BatchInsert(DB(), Config{}.TableName(), configs))

	configs, err = FindValidConfigs()
	require.NoError(err)
	require.Len(configs, 1)
	assert.EqualValues(80001, configs[0].GiftID)
}

func TestDeleteConfigs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	configIDs := []int64{1919, 1818}
	configs := []*Config{
		{ID: configIDs[0]},
		{ID: configIDs[1]},
	}
	require.NoError(servicedb.BatchInsert(DB(), Config{}.TableName(), configs))

	ok, err := DeleteConfigs(configIDs)
	require.NoError(err)
	assert.True(ok)
	ok, err = DeleteConfigs(configIDs)
	require.NoError(err)
	assert.False(ok)
}

func TestConfig_GiftDrop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()

	t.Run("首次触发", func(t *testing.T) {
		config := Config{
			StartTime:         now.Unix(),
			EndTime:           now.Add(time.Hour).Unix(),
			PrizeGiftTotalNum: 100,
		}

		result := config.GiftDrop(0, now.Unix(), config.PrizeGiftTotalNum)

		assert.NotZero(result.NextDropExpectTime)
		assert.Empty(result.ExpiredDropTimes)
		assert.Zero(result.ActualTime)
		assert.Zero(result.ExpectTime)
		assert.False(result.IsHitPrevDropTime)
	})
	t.Run("首次触发且存在至少一个跳过点", func(t *testing.T) {
		config := Config{
			StartTime:         1740301020,
			EndTime:           1740301200,
			PrizeGiftTotalNum: 9,
		}
		result := config.GiftDrop(0, 1740301057, 9)
		require.NotEmpty(result.ExpiredDropTimes)
		assert.GreaterOrEqual(len(result.ExpiredDropTimes), 1)
		if result.ActualTime > 0 {
			assert.Greater(result.ExpectTime, int64(1740301050))
		}
	})
	t.Run("当前时间超出结束时间", func(t *testing.T) {
		config := Config{
			StartTime:         now.Unix(),
			EndTime:           now.Add(time.Hour).Unix(),
			PrizeGiftTotalNum: 100,
		}

		result := config.GiftDrop(0, now.Add(time.Hour+time.Second).Unix(), config.PrizeGiftTotalNum)

		assert.Zero(result.NextDropExpectTime)
		assert.Empty(result.ExpiredDropTimes)
		assert.Zero(result.ActualTime)
		assert.Zero(result.ExpectTime)
		assert.False(result.IsHitPrevDropTime)
	})
	t.Run("有过期时间的正常触发", func(t *testing.T) {
		config := Config{
			StartTime:         now.Add(-2 * time.Hour).Unix(),
			EndTime:           now.Add(2 * time.Hour).Unix(),
			PrizeGiftTotalNum: 100,
		}

		prevDropTime := now.Add(-time.Hour).Unix()
		currentTime := now.Unix()

		result := config.GiftDrop(prevDropTime, currentTime, config.PrizeGiftTotalNum)

		assert.NotZero(result.NextDropExpectTime)
		assert.NotEmpty(result.ExpiredDropTimes)
	})
	t.Run("存在大量过期掉落时间点", func(t *testing.T) {
		config := Config{
			StartTime:         now.Add(-2 * time.Hour).Unix(),
			EndTime:           now.Add(time.Hour * 24 * 30 * 12).Unix(),
			PrizeGiftTotalNum: 365 * 24 * 60 * 60,
		}

		prevDropTime := now.Unix()
		currentTime := now.Add(time.Hour * 24 * 30 * 11).Unix()

		result := config.GiftDrop(prevDropTime, currentTime, config.PrizeGiftTotalNum)

		assert.NotZero(result.NextDropExpectTime)
		assert.NotEmpty(result.ExpiredDropTimes)
	})
}

func TestConfig_nextGiftDropTime(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("正常情况：一小时投放一百个且每次都被用户命中", func(t *testing.T) {
		config := Config{
			StartTime: now.Unix(),
			EndTime:   now.Add(1 * time.Hour).Unix(),
		}

		var (
			dropStartTime  int64
			remainPrizeNum int64 = 100
			nextTime       int64
		)

		for {
			nextTime = config.nextGiftDropTime(dropStartTime, remainPrizeNum)
			if nextTime == 0 {
				break
			}

			if dropStartTime < config.StartTime {
				dropStartTime = config.StartTime
			}

			remainDuration := config.EndTime - dropStartTime - 1
			remainAvgDuration := float64(remainDuration) / float64(remainPrizeNum)

			assert.GreaterOrEqual(nextTime, dropStartTime+int64(math.Ceil(remainAvgDuration*randomLeftOffsetRate)))
			assert.LessOrEqual(nextTime, dropStartTime+int64(math.Ceil(remainAvgDuration*randomRightOffsetRate)))
			assert.GreaterOrEqual(nextTime, config.StartTime)
			assert.Less(nextTime, config.EndTime)
			assert.Greater(nextTime, dropStartTime)

			// 随机生成踩中的时间
			dropStartTime = nextTime + int64(rand.Intn(singleGiftDropInterval))
			remainPrizeNum--
		}
		// 奖品数量为 0
		assert.Zero(remainPrizeNum)
	})
	t.Run("异常情况：一小时投放一百个且用户未曾命中", func(t *testing.T) {
		config := Config{
			StartTime: now.Unix(),
			EndTime:   now.Add(1 * time.Hour).Unix(),
		}

		var (
			dropStartTime  int64
			remainPrizeNum int64 = 100
			nextTime       int64
		)

		for {
			nextTime = config.nextGiftDropTime(dropStartTime, remainPrizeNum)
			if nextTime == 0 {
				break
			}

			if dropStartTime < config.StartTime {
				dropStartTime = config.StartTime
			}

			remainDuration := config.EndTime - dropStartTime - 1
			remainAvgDuration := float64(remainDuration) / float64(remainPrizeNum)

			assert.GreaterOrEqual(nextTime, dropStartTime+int64(math.Ceil(remainAvgDuration*randomLeftOffsetRate)), "dropStartTime = %d", dropStartTime)
			if math.Ceil(remainAvgDuration*randomRightOffsetRate)-math.Ceil(remainAvgDuration*randomLeftOffsetRate) > 0 {
				assert.LessOrEqual(nextTime, dropStartTime+int64(math.Ceil(remainAvgDuration*randomRightOffsetRate)), "dropStartTime = %d", dropStartTime)
			}
			assert.GreaterOrEqual(nextTime, config.StartTime)
			assert.Less(nextTime, config.EndTime)

			dropStartTime = nextTime
		}
		// 抽完之后
		assert.EqualValues(100, remainPrizeNum)
		assert.Zero(nextTime)
	})
	t.Run("随机：多种投放组合且用户可能中奖", func(t *testing.T) {
		// key: 投放时间（秒）
		// value: 投放数量
		dropConfig := map[int64]int64{
			31104000: 51104000,
			2000000:  5000000,
			86400:    100,
			86401:    1000,
			8640:     1000,
			3600:     100,
			700:      100,
			650:      650,
			640:      1000,
			630:      900,
			600:      100,
			100:      100,
			50:       100,
			10:       100,
			1:        1,
		}

		for duration, prizeCount := range dropConfig {
			config := Config{
				StartTime:         now.Unix(),
				EndTime:           now.Add(time.Duration(duration) * time.Second).Unix(),
				PrizeGiftTotalNum: prizeCount,
			}

			var (
				remainPrizeNum = prizeCount
				dropStartTime  int64
				nextTime       int64
			)

			for remainPrizeNum > 0 {
				nextTime = config.nextGiftDropTime(dropStartTime, remainPrizeNum)
				// 发不完了
				if nextTime == 0 {
					break
				}

				if dropStartTime < config.StartTime {
					dropStartTime = config.StartTime
				}

				remainDuration := config.EndTime - dropStartTime - 1
				remainAvgDuration := float64(remainDuration) / float64(remainPrizeNum)
				leftBoundary := dropStartTime + int64(math.Ceil(remainAvgDuration*randomLeftOffsetRate))
				var rightBoundary int64
				if int64(math.Ceil(remainAvgDuration*randomRightOffsetRate))-int64(math.Ceil(remainAvgDuration*randomLeftOffsetRate)) > 0 {
					rightBoundary = dropStartTime + int64(math.Ceil(remainAvgDuration*randomRightOffsetRate))
				} else {
					rightBoundary = config.EndTime - 1
				}

				assert.GreaterOrEqual(nextTime, leftBoundary, "Failed at duration %d: nextTime = %d, expected >= %d", duration, nextTime, leftBoundary)
				assert.LessOrEqual(nextTime, rightBoundary, "Failed at duration %d: nextTime = %d, expected < %d", duration, nextTime, rightBoundary)
				assert.GreaterOrEqual(nextTime, config.StartTime, "Failed at duration %d: nextTime = %d, expected >= %d", duration, nextTime, config.StartTime)
				assert.Less(nextTime, config.EndTime, "Failed at duration %d: nextTime = %d, expected < %d", duration, nextTime, config.EndTime)
				assert.Greater(nextTime, dropStartTime, "Failed at duration %d: nextTime = %d, expected > %d", duration, nextTime, dropStartTime)

				// 控制用户抽中的概率
				if rand.Intn(2) == 0 {
					remainPrizeNum--
					// 用户的抽中时间随机取 [nextTime, nextTime+5) 或者 [nextTime, config.EndTime) 之间
					if nextTime+singleGiftDropInterval >= config.EndTime {
						dropStartTime = int64(rand.Intn(int(config.EndTime-nextTime))) + nextTime
					} else {
						dropStartTime = int64(rand.Intn(singleGiftDropInterval)) + nextTime
					}
				} else {
					dropStartTime = nextTime
				}
			}

			// 表示没有发完
			if nextTime == 0 {
				assert.NotZero(remainPrizeNum, "Failed: Not all prizes were distributed for duration %d", duration)
			} else {
				assert.Zero(remainPrizeNum, "Failed: Not all prizes were distributed for duration %d", duration)
				assert.Less(nextTime, config.EndTime, "Failed: Last drop time %d is not before end time %d, duration", nextTime, config.EndTime, duration)
			}
		}
	})
}

func TestGiftDropResult_IsHit(t *testing.T) {
	assert := assert.New(t)

	result := GiftDropResult{}
	assert.False(result.IsHit())

	result.ActualTime = 1
	assert.True(result.IsHit())
}

func TestGiftDropResult_isHitNextGiftDrop(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("踩中", func(t *testing.T) {
		result := GiftDropResult{
			NextDropExpectTime: now.Unix(),
		}
		isHitNextGiftDrop := result.isHitNextGiftDrop(now.Add(time.Second).Unix())
		assert.True(isHitNextGiftDrop)
	})
	t.Run("未踩中", func(t *testing.T) {
		result := GiftDropResult{
			NextDropExpectTime: now.Unix(),
		}
		isHitNextGiftDrop := result.isHitNextGiftDrop(now.Add(6 * time.Second).Unix())
		assert.False(isHitNextGiftDrop)
	})
}

func TestGiftDropResult_isAfterNextGiftDrop(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("在掉落时间之后", func(t *testing.T) {
		result := GiftDropResult{
			NextDropExpectTime: now.Unix(),
		}
		isAfterNextGiftDrop := result.isAfterNextGiftDrop(now.Add(10 * time.Second).Unix())
		assert.True(isAfterNextGiftDrop)
	})
	t.Run("未在掉落时间之后", func(t *testing.T) {
		result := GiftDropResult{
			NextDropExpectTime: now.Unix(),
		}
		isAfterNextGiftDrop := result.isAfterNextGiftDrop(now.Add(time.Second).Unix())
		assert.False(isAfterNextGiftDrop)
	})
}

func TestGiftDropResult_NewRecords(t *testing.T) {
	assert := assert.New(t)

	var configID int64
	timeNowUnix := goutil.TimeNow().Unix()
	result := GiftDropResult{}
	records := result.NewRecords(configID, timeNowUnix)
	assert.Empty(records)

	// 过期的时间点生成新记录
	result.ExpiredDropTimes = []int64{1, 2, 3}
	records = result.NewRecords(configID, timeNowUnix)
	assert.Len(records, 3)

	// 生成了新的投放点
	result.NextDropExpectTime = 4
	records = result.NewRecords(configID, timeNowUnix)
	assert.Len(records, 4)
}
