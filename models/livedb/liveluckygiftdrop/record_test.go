package liveluckygiftdrop

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTableName_Record(t *testing.T) {
	assert.Equal(t, "live_lucky_gift_drop_record", Record{}.TableName())
}

func TestTags_Record(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Record{}, "id", "create_time", "modified_time", "config_id", "expected_time", "actual_time", "user_id", "room_id")
}

func TestFindLastRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(Record{}).Error
	require.NoError(err)

	now := goutil.TimeNow()
	records := []Record{
		{ConfigID: 1, ExpectedTime: now.Add(time.Second).Unix(), ActualTime: now.Add(time.Second).Unix()},
		{ConfigID: 1, ExpectedTime: now.Add(2 * time.Second).Unix(), ActualTime: 0},
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)

	record, err := FindLastRecord(1)
	require.NoError(err)
	require.NotNil(record)
	assert.Zero(record.ActualTime)

	record, err = FindLastRecord(2)
	require.NoError(err)
	assert.Nil(record)
}

func TestUpdateTransactionID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(Record{}).Error
	require.NoError(err)

	records := []Record{
		{ID: 1, ConfigID: 1},
		{ID: 2, ConfigID: 2},
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)

	transactionID := int64(1234)
	err = UpdateTransactionID(&GiftDropResult{HitRecordID: 1}, transactionID)
	require.NoError(err)
	var afterRecords []Record
	err = DB().Order("id ASC").Find(&afterRecords).Error
	require.NoError(err)
	require.Len(afterRecords, 2)
	assert.Equal(transactionID, afterRecords[0].TransactionID)
	assert.NotZero(afterRecords[0].ModifiedTime)
	assert.Zero(afterRecords[1].TransactionID)
	assert.Zero(afterRecords[1].ModifiedTime)
}
