package liveluckygiftdrop

import (
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Record 投放记录表
type Record struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 更新时间，单位：秒

	ConfigID      int64 `gorm:"column:config_id"`     // 配置 ID
	ExpectedTime  int64 `gorm:"column:expected_time"` // 期望投放时间，单位：秒
	ActualTime    int64 `gorm:"column:actual_time"`   // 实际发放时间，单位：秒
	UserID        int64 `gorm:"column:user_id"`
	RoomID        int64 `gorm:"column:room_id"`
	TransactionID int64 `gorm:"column:transaction_id"` // 订单号
}

// TableName .
func (Record) TableName() string {
	return "live_lucky_gift_drop_record"
}

// FindLastRecord 获取最新一条记录
func FindLastRecord(configID int64) (*Record, error) {
	var record Record
	err := DB().Where("config_id = ?", configID).Order("expected_time DESC").Take(&record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// UpdateTransactionID 更新掉落结果对应的交易订单号
func UpdateTransactionID(result *GiftDropResult, transactionID int64) error {
	return DB().Model(&Record{}).Where("id = ?", result.HitRecordID).Updates(
		map[string]any{
			"transaction_id": transactionID,
			"modified_time":  goutil.TimeNow().Unix(),
		}).Error
}
