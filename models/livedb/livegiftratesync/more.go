package livegiftratesync

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/logger"
)

// More 礼物池的更多信息
type More struct {
	LuckyGift *MoreLuckyGift `json:"lucky_gift,omitempty"`
	LuckyBox  *MoreLuckyBox  `json:"lucky_box,omitempty"`
}

// MoreLuckyGift 随机礼物
type MoreLuckyGift struct {
	LuckyGiftName string `json:"lucky_gift_name"`     // 随机礼物名称
	LuckyGiftNum  int    `json:"lucky_gift_num"`      // 随机礼物档位
	GiftName      string `json:"gift_name,omitempty"` // 抽中的礼物名称
	IsSSR         bool   `json:"is_ssr,omitempty"`    // 是否是 SSR 礼物
}

// MoreLuckyBox 宝盒
type MoreLuckyBox struct {
	GoodsTitle string `json:"goods_title"`
	GiftName   string `json:"gift_name"`
	IsSSR      bool   `json:"is_ssr,omitempty"` // 是否是 SSR 礼物
}

// Marshal 序列化
func (m *More) Marshal() []byte {
	bytes, err := json.Marshal(m)
	if err != nil {
		logger.Errorf("marshal more error: %v", err)
		// PASS
	}
	return bytes
}
