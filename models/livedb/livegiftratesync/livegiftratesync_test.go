package livegiftratesync

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestGiftRateSync_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(GiftRateSync{}, "id", "create_time", "modified_time", "element_id", "element_type", "status", "gift_pool_id", "gift_id",
		"expected_rate", "actual_rate", "total_num", "total_user_num", "more")
}

func TestRate(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("0", Rate(1, 0))
	assert.Equal("0", Rate(0, 1))
	assert.Equal("1", Rate(1, 1))
	assert.Equal("0.3", Rate(3, 10))
	assert.Equal("0.33333", Rate(3, 9))
	assert.Equal("0.44444", Rate(4, 9))
	assert.Equal("0.55556", Rate(5, 9)) // 5/9 = 0.5555555555555556 测试四舍五入
}

func TestListLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(GiftRateSync{}, "element_type = ?", ElementTypeLuckyBox).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), new(GiftRateSync).TableName(), []GiftRateSync{
		{
			ElementID:   1,
			ElementType: ElementTypeLuckyBox,
			Status:      StatusShow,
		},
		{
			ElementID:   2,
			ElementType: ElementTypeLuckyBox,
			Status:      StatusShow,
		},
		{
			ElementID:   3,
			ElementType: ElementTypeLuckyBox,
			Status:      StatusHide,
		},
	})
	require.NoError(err)

	list, err := ListLuckyBox()
	require.NoError(err)
	assert.Len(list, 2)
}

func cleanupLuckyGiftRecords() error {
	return DB().Delete(GiftRateSync{}, "element_type IN (?)", []int{ElementTypeLuckyGift, ElementTypeLuckyGiftReward}).Error
}

func TestListLuckyGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := cleanupLuckyGiftRecords()
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), new(GiftRateSync).TableName(), []GiftRateSync{
		{
			ElementID:   1,
			ElementType: ElementTypeLuckyGift,
			Status:      StatusShow,
		},
		{
			ElementID:   2,
			ElementType: ElementTypeLuckyGift,
			Status:      StatusShow,
		},
		{
			ElementID:   3,
			ElementType: ElementTypeLuckyGift,
			Status:      StatusHide,
		},
		{
			ElementID:   1,
			ElementType: ElementTypeLuckyGiftReward,
			Status:      StatusShow,
		},
	})
	require.NoError(err)

	list, err := ListLuckyGift()
	require.NoError(err)
	assert.Len(list, 3)
}

func TestGiftRateSync_applyUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	sync := &GiftRateSync{
		ID:           1,
		ElementType:  ElementTypeLuckyBox,
		ExpectedRate: "0.99",
		ActualRate:   goutil.NewString("0.99"),
		More:         []byte(`{"a":1}`),
		ModifiedTime: 10,
	}
	update, ok := sync.applyUpdate(sync)
	require.False(ok)
	require.Nil(update)

	update, ok = sync.applyUpdate(&GiftRateSync{
		ElementType:  ElementTypeLuckyBox,
		ExpectedRate: "0.99001",
		ActualRate:   goutil.NewString("0.90001"),
		More:         []byte(`{"b":2}`),
		ModifiedTime: 11,
	})
	require.True(ok)
	require.NotNil(update)
	assert.Equal(sync.ID, update.id)
	require.Len(update.columns, 4)
	assert.Equal("0.99001", update.columns["expected_rate"])
	assert.Equal("0.90001", update.columns["actual_rate"])
	assert.Equal([]byte(`{"b":2}`), update.columns["more"])
	assert.EqualValues(11, update.columns["modified_time"])
}

func TestUpsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(GiftRateSync{}, "element_type = ?", ElementTypeLuckyBox).Error
	require.NoError(err)

	afterSyncs := []*GiftRateSync{
		{
			ElementID:   1,
			ElementType: ElementTypeLuckyBox,
			Status:      StatusShow,
			GiftPoolID:  1,
			GiftID:      1,
		},
		{
			ElementID:   2,
			ElementType: ElementTypeLuckyBox,
			Status:      StatusShow,
			GiftPoolID:  2,
			GiftID:      2,
		},
	}
	err = Upsert(nil, afterSyncs)
	require.NoError(err)
	list, err := ListLuckyBox()
	require.NoError(err)
	require.Len(list, 2)
	assert.Nil(list[0].ActualRate)
	assert.Nil(list[1].ActualRate)

	afterSyncs = []*GiftRateSync{
		{
			ElementType:  ElementTypeLuckyBox,
			ElementID:    1,
			Status:       StatusShow,
			GiftPoolID:   1,
			GiftID:       1,
			ExpectedRate: "0.99001",
			ActualRate:   goutil.NewString("0.99"),
		},
		{
			ElementType:  ElementTypeLuckyBox,
			ElementID:    2,
			Status:       StatusShow,
			GiftPoolID:   2,
			GiftID:       2,
			ExpectedRate: "0.02",
			ActualRate:   goutil.NewString("0.01"),
		},
		{
			ElementType:  ElementTypeLuckyBox,
			ElementID:    3,
			Status:       StatusShow,
			GiftPoolID:   1,
			GiftID:       3,
			ExpectedRate: "0.02",
			ActualRate:   goutil.NewString("0.01"),
		},
		{
			ElementType:  ElementTypeLuckyBox,
			ElementID:    3,
			Status:       StatusShow,
			GiftPoolID:   1,
			GiftID:       2,
			ExpectedRate: "0.105",
			ActualRate:   goutil.NewString("0.11"),
		},
	}
	err = Upsert(list, afterSyncs)
	require.NoError(err)
	list, err = ListLuckyBox()
	require.NoError(err)
	require.Len(list, 4)
	for i := range afterSyncs {
		assert.Equal(afterSyncs[i].ElementID, list[i].ElementID)
		assert.Equal(afterSyncs[i].ExpectedRate, list[i].ExpectedRate)
		if i == 0 {
			assert.EqualValues("0.99001", list[i].ExpectedRate)
		}
		assert.Equal(*afterSyncs[i].ActualRate, *list[i].ActualRate)
	}
}
