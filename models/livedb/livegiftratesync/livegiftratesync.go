package livegiftratesync

import (
	"encoding/json"
	"slices"
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// ElementType 元素类型
const (
	ElementTypeLuckyGift       = iota + 1 // 随机礼物
	ElementTypeLuckyGiftReward            // 随机礼物回报
	ElementTypeLuckyBox                   // 宝盒
)

// Status 状态
const (
	StatusHide = iota // 下架
	StatusShow        // 正常
)

// GiftRateSync 礼物概率同步
type GiftRateSync struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	ElementType int   `gorm:"column:element_type"` // 元素类型，1：随机礼物；2：随机礼物回报；3：宝盒
	ElementID   int64 `gorm:"column:element_id"`   // 元素 ID
	Status      int   `gorm:"column:status"`       // 状态, 0: 下架；1：正常
	GiftPoolID  int64 `gorm:"column:gift_pool_id"` // 礼物池 ID
	GiftID      int64 `gorm:"column:gift_id"`      // 奖池的礼物 ID

	ExpectedRate string  `gorm:"column:expected_rate"` // 期望概率
	ActualRate   *string `gorm:"column:actual_rate"`   // 实际概率

	TotalNum     int64 `json:"total_num"`      // 抽中次数
	TotalUserNum int64 `json:"total_user_num"` // 抽中用户数

	More []byte `gorm:"column:more"` // 更多信息

	MoreInfo *More `gorm:"-"`
}

// TableName .
func (GiftRateSync) TableName() string {
	return "live_gift_rate_sync"
}

// AfterFind .
func (sync *GiftRateSync) AfterFind() error {
	if sync.More != nil {
		return json.Unmarshal(sync.More, &sync.MoreInfo)
	}
	return nil
}

// BeforeSave .
func (sync *GiftRateSync) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	if DB().NewRecord(sync) {
		sync.CreateTime = nowUnix
	}
	sync.ModifiedTime = nowUnix
	return nil
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// Rate 计算概率，并最多保留 5 位小数
func Rate(numerator, denominator int64) string {
	var result float64
	if denominator != 0 {
		result = float64(numerator) / float64(denominator)
	}
	return strconv.FormatFloat(result, 'g', 5, 64)
}

// ListLuckyBox 列出所有宝盒记录
func ListLuckyBox() ([]*GiftRateSync, error) {
	var syncs []*GiftRateSync
	err := DB().Where("element_type = ? AND status = ?", ElementTypeLuckyBox, StatusShow).Find(&syncs).Error
	if err != nil {
		return nil, err
	}
	return syncs, nil
}

// ListLuckyGift 列出所有随机礼物记录
func ListLuckyGift() ([]*GiftRateSync, error) {
	var syncs []*GiftRateSync
	err := DB().Where("element_type IN (?) AND status = ?",
		[]int{ElementTypeLuckyGift, ElementTypeLuckyGiftReward}, StatusShow).
		Find(&syncs).Error
	if err != nil {
		return nil, err
	}
	return syncs, nil
}

type updateColumn struct {
	id      int64
	columns map[string]any
}

func (sync *GiftRateSync) applyUpdate(newSync *GiftRateSync) (*updateColumn, bool) {
	update := &updateColumn{
		id:      sync.ID,
		columns: make(map[string]any, 6),
	}
	if newSync.ExpectedRate != sync.ExpectedRate {
		update.columns["expected_rate"] = newSync.ExpectedRate
	}
	if string(newSync.More) != string(sync.More) {
		update.columns["more"] = newSync.More
	}
	if newSync.ActualRate != nil && (sync.ActualRate == nil || *newSync.ActualRate != *sync.ActualRate) {
		update.columns["actual_rate"] = *newSync.ActualRate
	}
	if newSync.TotalNum != sync.TotalNum {
		update.columns["total_num"] = newSync.TotalNum
	}
	if newSync.TotalUserNum != sync.TotalUserNum {
		update.columns["total_user_num"] = newSync.TotalUserNum
	}
	if len(update.columns) == 0 {
		return nil, false
	}
	update.columns["modified_time"] = newSync.ModifiedTime
	return update, true
}

// Upsert 更新或插入（没有删除操作）
func Upsert(beforeSyncs, afterSyncs []*GiftRateSync) error {
	if len(beforeSyncs) == 0 {
		if len(afterSyncs) == 0 {
			return nil
		}
		err := servicedb.BatchInsert(DB(), GiftRateSync{}.TableName(), afterSyncs)
		if err != nil {
			return err
		}
		return nil
	}

	var (
		inserts = make([]*GiftRateSync, 0, len(afterSyncs))
		updates = make([]*updateColumn, 0, len(afterSyncs))

		removalSyncIDs []int64
	)
	allSyncIDs := make([]int64, 0, len(beforeSyncs))
	foundSyncIDs := make([]int64, 0, len(beforeSyncs))
	for _, before := range beforeSyncs {
		allSyncIDs = append(allSyncIDs, before.ID)
	}
	for _, after := range afterSyncs {
		index := slices.IndexFunc(beforeSyncs, func(before *GiftRateSync) bool {
			if before.ElementType != after.ElementType ||
				before.ElementID != after.ElementID {
				return false
			}
			if before.ElementType == ElementTypeLuckyGift || before.ElementType == ElementTypeLuckyGiftReward {
				// 随机礼物还需判断档位相同
				if before.MoreInfo == nil || after.MoreInfo == nil ||
					before.MoreInfo.LuckyGift == nil || after.MoreInfo.LuckyGift == nil {
					return false
				}
				if before.MoreInfo.LuckyGift.LuckyGiftNum != after.MoreInfo.LuckyGift.LuckyGiftNum {
					return false
				}
			}
			return before.GiftPoolID == after.GiftPoolID && before.GiftID == after.GiftID
		})
		if index < 0 {
			inserts = append(inserts, after)
		} else {
			foundSyncIDs = append(foundSyncIDs, beforeSyncs[index].ID)
			update, ok := beforeSyncs[index].applyUpdate(after)
			if ok {
				updates = append(updates, update)
			}
		}
	}
	removalSyncIDs = sets.Diff(allSyncIDs, foundSyncIDs)
	if len(inserts) == 0 && len(updates) == 0 && len(removalSyncIDs) == 0 {
		return nil
	}
	err := servicedb.Tx(DB(), func(tx *gorm.DB) error {
		if len(inserts) > 0 {
			err := servicedb.BatchInsert(tx, GiftRateSync{}.TableName(), inserts)
			if err != nil {
				return err
			}
		}
		if len(updates) > 0 {
			for _, update := range updates {
				if update.id <= 0 {
					logger.Error("update id is zero")
					continue
				}
				err := tx.Model(&GiftRateSync{}).Where("id = ?", update.id).Updates(update.columns).Error
				if err != nil {
					return err
				}
			}
		}
		if len(removalSyncIDs) > 0 {
			err := tx.Model(&GiftRateSync{}).Where("id IN (?)", removalSyncIDs).Updates(map[string]any{
				"status":        StatusHide,
				"modified_time": goutil.TimeNow().Unix(),
			}).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
