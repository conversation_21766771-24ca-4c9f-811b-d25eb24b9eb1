package backpackitem

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&LiveBackpackItem{}, "id", "create_time", "modified_time", "type", "name",
		"icon", "icon_active", "intro", "intro_icon", "label_icon", "intro_open_url", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(&More{}, "vip_id", "duration", "allowed_nums", "icon")
}

func TestLiveBackpackItem_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	item := &LiveBackpackItem{
		Icon: "oss://icon.png",
		More: `{"vip_id": 1, "duration": 3600, "allowed_nums": [1,2,3]}`,
	}
	err := item.AfterFind()
	require.NoError(err)
	assert.Equal(storage.ParseSchemeURL("oss://icon.png"), item.IconURL)
	assert.NotNil(item.MoreInfo)
	assert.EqualValues(1, item.MoreInfo.VipID)
	assert.EqualValues(3600, item.MoreInfo.Duration)
	assert.Equal([]int{1, 2, 3}, item.MoreInfo.AllowedNums)
}

func TestLiveBackpackItem_UnmarshalMore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	item := &LiveBackpackItem{
		More: `{"vip_id": 1, "duration": 3600, "allowed_nums": [1,2,3]}`,
	}
	more, err := item.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)
	assert.EqualValues(1, more.VipID)
	assert.EqualValues(3600, more.Duration)
	assert.Equal([]int{1, 2, 3}, more.AllowedNums)
}

func TestAllItemTypes(t *testing.T) {
	assert := assert.New(t)

	assert.Equal([]int{TypeNobleTrialCard, TypeLiveMedalTrialCard}, allItemTypes())
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bpi, err := FindOne(1, TypeNobleTrialCard)
	require.NoError(err)
	assert.NotNil(bpi)

	bpi, err = FindOne(1, 3213)
	require.NoError(err)
	assert.Nil(bpi)

	bpi, err = FindOne(2321312312321, TypeNobleTrialCard)
	require.NoError(err)
	assert.Nil(bpi)
}

func TestFindOneItem(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bpi, err := FindOneItem(1)
	require.NoError(err)
	assert.NotNil(bpi)

	bpi, err = FindOneItem(1000000)
	require.NoError(err)
	assert.Nil(bpi)
}

func TestFindByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bpis, err := FindByIDs([]int64{1, 2, 3})
	require.NoError(err)
	assert.Len(bpis, 2)
}
