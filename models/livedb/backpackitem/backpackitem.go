package backpackitem

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 背包道具类型
const (
	// TypeNobleTrialCard 贵族体验卡
	TypeNobleTrialCard = iota + 1
	// TypeLiveMedalTrialCard 粉丝勋章兑换卡
	TypeLiveMedalTrialCard
)

// LiveBackpackItem 背包道具配置表
type LiveBackpackItem struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Type         int    `gorm:"column:type"`
	Name         string `gorm:"column:name"`
	Icon         string `gorm:"column:icon"`
	IconActive   string `gorm:"column:icon_active"`
	Intro        string `gorm:"column:intro"`
	IntroIcon    string `gorm:"column:intro_icon"`
	LabelIcon    string `gorm:"column:label_icon"`
	IntroOpenURL string `gorm:"column:intro_open_url"`
	More         string `gorm:"column:more"`

	IconURL       string `gorm:"-"`
	IconActiveURL string `gorm:"-"`
	IntroIconURL  string `gorm:"-"`
	LabelIconURL  string `gorm:"-"`
	MoreInfo      *More  `gorm:"-"`
}

// More 额外的内容
type More struct {
	VipID       int64  `json:"vip_id"`
	Duration    int64  `json:"duration"`               // 道具生效时长，单位：秒
	AllowedNums []int  `json:"allowed_nums,omitempty"` // 允许使用的数量
	Icon        string `json:"icon,omitempty"`         // 道具图标，目前仅用于贵族体验卡使用成功的弹窗显示

	IconURL string `json:"-"`
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// TableName 表名
func (LiveBackpackItem) TableName() string {
	return "live_backpack_item"
}

// BeforeSave gorm hook
func (bi *LiveBackpackItem) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	if DB().NewRecord(bi) {
		bi.CreateTime = nowUnix
	}
	bi.ModifiedTime = nowUnix
	return nil
}

// AfterFind gorm hook
func (bi *LiveBackpackItem) AfterFind() (err error) {
	if bi.Icon != "" {
		bi.IconURL = storage.ParseSchemeURL(bi.Icon)
	}
	if bi.IconActive != "" {
		bi.IconActiveURL = storage.ParseSchemeURL(bi.IconActive)
	}
	if bi.IntroIcon != "" {
		bi.IntroIconURL = storage.ParseSchemeURL(bi.IntroIcon)
	}
	if bi.LabelIcon != "" {
		bi.LabelIconURL = storage.ParseSchemeURL(bi.LabelIcon)
	}
	if bi.More != "" {
		bi.MoreInfo, err = bi.UnmarshalMore()
		if err != nil {
			return err
		}
		bi.MoreInfo.IconURL = storage.ParseSchemeURL(bi.MoreInfo.Icon)
	}
	return nil
}

// UnmarshalMore 解析 More 字段
func (bi *LiveBackpackItem) UnmarshalMore() (*More, error) {
	if bi.More == "" {
		return nil, nil
	}
	var more *More
	err := json.Unmarshal([]byte(bi.More), &more)
	if err != nil {
		return nil, err
	}
	return more, nil
}

// allItemTypes 所有的道具类型
func allItemTypes() []int {
	return []int{TypeNobleTrialCard, TypeLiveMedalTrialCard}
}

// FindOne 通过 ID 查找
func FindOne(id int64, itemType int) (*LiveBackpackItem, error) {
	var item LiveBackpackItem
	err := DB().Where("id = ? AND type = ?", id, itemType).Take(&item).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

// FindOneItem 通过 ID 查找支持的类型的背包道具
func FindOneItem(id int64) (*LiveBackpackItem, error) {
	var item LiveBackpackItem
	err := DB().Where("id = ? AND type IN (?)", id, allItemTypes()).Take(&item).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &item, nil
}

// FindByIDs 通过 ID 列表查找
func FindByIDs(ids []int64) ([]*LiveBackpackItem, error) {
	var items []*LiveBackpackItem
	err := DB().Where("id IN (?)", ids).Find(&items).Error
	if err != nil {
		return nil, err
	}
	return items, nil
}
