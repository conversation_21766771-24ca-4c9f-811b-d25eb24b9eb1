package liveprize

// PrizePackageMap 奖品包与奖品关联表
type PrizePackageMap struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	PrizeID        int64 `gorm:"column:prize_id"`         // 奖品 ID
	PrizePackageID int64 `gorm:"column:prize_package_id"` // 奖品包 ID
}

// TableName 返回表名
func (PrizePackageMap) TableName() string {
	return "live_prize_package_map"
}
