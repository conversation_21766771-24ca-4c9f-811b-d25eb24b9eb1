package liveprize

import (
	"encoding/json"

	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Status 奖品记录状态
const (
	StatusRollback      = iota - 1 // 回滚
	StatusReceiveing               // 领取中
	StatusReceived                 // 已领取
	StatusReceiveFailed            // 领取失败
)

// Biz 奖品记录业务类型
const (
	// BizGiftUpgrade 礼物升级
	BizGiftUpgrade = iota + 1
	// BizCreatorTask 主播任务
	BizCreatorTask
	// BizFansBoxTask 直播间粉丝团宝箱任务
	BizFansBoxTask
)

// PrizeLog 奖品记录表
type PrizeLog struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	ReceiveTime    int64  `gorm:"column:receive_time"`     // 领取时间，单位：秒
	Status         int    `gorm:"column:status"`           // 状态，-1：回滚，0：领取中，1：已领取，2：领取失败
	Biz            int    `gorm:"column:biz"`              // 业务类型，1：礼物升级
	BizID          any    `gorm:"column:biz_id"`           // 业务 ID
	PrizeID        int64  `gorm:"column:prize_id"`         // 奖品 ID
	PrizePackageID int64  `gorm:"column:prize_package_id"` // 奖品包 ID
	UserID         int64  `gorm:"column:user_id"`          // 用户 ID
	RoomID         int64  `gorm:"column:room_id"`          // 房间 ID
	More           []byte `gorm:"column:more"`             // 更多信息

	MoreInfo *PrizeLogMore `gorm:"-"`
}

// PrizeLogMore 奖品记录更多信息
type PrizeLogMore struct {
	// 错误信息
	Error string `json:"error,omitempty"`

	// TODO: 如果之前没有改类型奖品，获取永久奖品时，after 是 0，before 应该怎么存？
	Before int64 `json:"before,omitempty"` // 领取前奖品信息
	After  int64 `json:"after,omitempty"`  // 领取后奖品信息
}

// TableName .
func (PrizeLog) TableName() string {
	return "live_prize_log"
}

// BeforeSave .
func (pl *PrizeLog) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	pl.ModifiedTime = nowUnix
	if DB().NewRecord(pl) {
		pl.CreateTime = nowUnix
	}
	return nil
}

// AfterFind .
func (pl *PrizeLog) AfterFind() error {
	if len(pl.More) > 0 {
		err := json.Unmarshal(pl.More, &pl.MoreInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// Marshal 序列化
func (plm *PrizeLogMore) Marshal() []byte {
	bytes, err := json.Marshal(plm)
	if err != nil {
		logger.Errorf("marshal more error: %v", err)
		// PASS
	}
	return bytes
}

// Logging 记录奖品领取日志
func (pl *PrizeLog) Logging(f func() (err error)) (*PrizeLog, error) {
	prizeLog := &PrizeLog{
		ReceiveTime: goutil.TimeNow().Unix(),
		Status:      StatusReceiveing,
		Biz:         pl.Biz,
		BizID:       pl.BizID,
		PrizeID:     pl.PrizeID,
		UserID:      pl.UserID,
		RoomID:      pl.RoomID,
	}
	err := DB().Create(prizeLog).Error
	if err != nil {
		return prizeLog, err
	}
	defer func() {
		err := DB().Model(PrizeLog{}).Where("id = ?", prizeLog.ID).Update(map[string]any{
			"modified_time": goutil.TimeNow().Unix(),
			"status":        prizeLog.Status,
			"more":          prizeLog.MoreInfo.Marshal(),
		}).Error
		if err != nil {
			logger.WithFields(logger.Fields{
				"prize_log_id": prizeLog.ID,
			}).Errorf("update prize log failed: %v", err)
			// PASS
		}
	}()

	err = f()
	if err != nil {
		prizeLog.Status = StatusReceiveFailed
		prizeLog.MoreInfo = &PrizeLogMore{Error: err.Error()}
	} else {
		prizeLog.Status = StatusReceived
	}
	return prizeLog, err
}
