package liveprize

import (
	"encoding/json"
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Type 奖品类型
const (
	_                     = iota // 运营手动发放
	_                            // 发放礼物
	TypeUserAppearance           // 发放用户外观
	TypeCreatorAppearance        // 发放主播外观
	TypeBackpack                 // 发放背包礼物
	TypeCreatorBackpack          // 发放主播背包礼物
	TypeLiveTag                  // 发放直播间角标
	_                            // 发送飘屏
	_                            // 发放主播信息背景
	TypeSticker                  // 发送表情
	_                            // 发放用户定制礼物资格
	_                            // 贵族体验卡
	_                            // 贵族喇叭
	_                            // 自定义进场欢迎语
	_                            // 用户特殊红包
	TypeRoomGiftCustom           // 直播间定制礼物
	_                            // 以用户身份给直播间送礼
	TypeQuest                    // 进房有奖任务
)

// Prize 奖品表
type Prize struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	Type        int    `gorm:"column:type"`         // 奖品类型，1：礼物，2：外观，3：榜单值
	ElementType int    `gorm:"column:element_type"` // 元素类型
	ElementID   int64  `gorm:"column:element_id"`   // 元素 ID
	Name        string `gorm:"column:name"`         // 奖品名称
	Icon        string `gorm:"column:icon"`         // 奖品图标
	Num         int64  `gorm:"column:num"`          // 奖品数量
	StartTime   int64  `gorm:"column:start_time"`   // 开始时间，默认单位：秒
	ExpireTime  int64  `gorm:"column:expire_time"`  // 过期时间，默认单位：秒
	Duration    int64  `gorm:"column:duration"`     // 持续时间，默认单位：秒
	More        []byte `gorm:"column:more"`         // 更多信息

	MoreInfo *MoreInfo `gorm:"-"` // 更多信息
}

// MoreInfo 更多信息
type MoreInfo struct {
	CreateCard *CreateCard `json:"create_card,omitempty"` // 创建卡片
	Notify     *Notify     `json:"notify,omitempty"`      // 通知
	Quest      *Quest      `json:"quest,omitempty"`       // 任务
}

// CreateCard 直播间角标类型奖品额外参数
type CreateCard struct {
	URL string `json:"url,omitempty"` // 链接
}

// Notify 通知类型奖品额外参数
type Notify struct {
	Message string `json:"message,omitempty"` // 消息
}

// Quest 任务类型奖品额外参数
type Quest struct {
	Intro    string `json:"intro,omitempty"`    // 任务说明
	RewardID int64  `json:"reward_id"`          // 任务完成奖励 ID，只支持有持续时间的背包礼物奖励
	Duration int64  `json:"duration,omitempty"` // 直播间连续收听任务时长，单位：秒
	Daily    bool   `json:"daily,omitempty"`    // 每日任务
}

// TableName .
func (Prize) TableName() string {
	return "live_prize"
}

// DB 获取数据库连接
func DB() *gorm.DB {
	return service.LiveDB
}

// AfterFind 查询后钩子
func (p *Prize) AfterFind() error {
	if len(p.More) > 0 {
		return json.Unmarshal(p.More, &p.MoreInfo)
	}
	if p.MoreInfo != nil && p.MoreInfo.CreateCard != nil && p.MoreInfo.CreateCard.URL != "" {
		p.MoreInfo.CreateCard.URL = storage.ParseSchemeURL(p.MoreInfo.CreateCard.URL)
	}
	return nil
}

// GetTime 获取奖品合法的开始和结束时间
func (p *Prize) GetTime() (int64, int64, error) {
	if p.StartTime == 0 {
		p.StartTime = goutil.TimeNow().Unix()
	}

	// 判断发放奖励是否没有明确截止时间，通常用于指定天数的奖励
	if p.ExpireTime == 0 {
		if p.Duration == 0 {
			return 0, 0, fmt.Errorf("prize 配置错误 %d", p.ID)
		}
		p.ExpireTime = p.StartTime + p.Duration
	}
	return p.StartTime, p.ExpireTime, nil
}

// FindPrize 查找奖品
func FindPrize(id int64) (*Prize, error) {
	var prize Prize
	err := DB().Where("id = ?", id).Take(&prize).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &prize, nil
}

// FindPrizes 查找多个奖品
func FindPrizes(ids []int64) ([]*Prize, error) {
	var prizes []*Prize
	err := DB().Where("id IN (?)", ids).Find(&prizes).Error
	if err != nil {
		return nil, err
	}
	return prizes, nil
}
