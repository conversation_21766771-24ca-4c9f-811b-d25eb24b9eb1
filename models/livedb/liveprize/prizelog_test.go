package liveprize

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestPrizeLog_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(PrizeLog{}, "id", "create_time", "modified_time",
		"receive_time", "status", "biz", "biz_id", "prize_id", "prize_package_id", "user_id", "room_id", "more")
}

func TestPrizeLog_Logging(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pl := &PrizeLog{
		Biz:     6,
		BizID:   7,
		PrizeID: 8,
		UserID:  9,
		RoomID:  10,
	}
	log, err := pl.Logging(func() (err error) {
		return nil
	})
	require.NoError(err)
	require.NotNil(log)
	assert.NotZero(log.ID)
	assert.NotZero(log.ReceiveTime)
	assert.Equal(log.Status, StatusReceived)

	log, err = pl.Logging(func() (err error) {
		return errors.New("test")
	})
	assert.Equal(err, errors.New("test"))
	require.NotNil(log)
	assert.NotZero(log.ID)
	assert.NotZero(log.ReceiveTime)
	assert.Equal(log.Status, StatusReceiveFailed)
}
