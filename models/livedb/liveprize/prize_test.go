package liveprize

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestPrize_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Prize{}, "id", "create_time", "modified_time", "type", "element_type", "element_id", "name", "icon", "num", "expire_time", "duration", "start_time", "more")
}

func TestMoreInfo_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MoreInfo{}, "create_card", "notify", "quest")
}

func TestCreateCard_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CreateCard{}, "url")
}

func TestNotify_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Notify{}, "message")
}

func TestQuest_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Quest{}, "intro", "reward_id", "duration", "daily")
}

func TestPrize_GetTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	prize1 := &Prize{
		ID:         1,
		StartTime:  1600000000,
		ExpireTime: 1700000000,
		Duration:   1000,
	}
	startTime, endTime, err := prize1.GetTime()
	require.NoError(err)
	assert.Equal(int64(1600000000), startTime)
	assert.Equal(int64(1700000000), endTime)

	prize2 := &Prize{
		ID:        1,
		StartTime: 1600000000,
		Duration:  1000,
	}
	startTime, endTime, err = prize2.GetTime()
	require.NoError(err)
	assert.Equal(int64(1600000000), startTime)
	assert.Equal(int64(1600001000), endTime)

	prize3 := &Prize{
		ID:         1,
		ExpireTime: 1700000000,
		Duration:   1000,
	}
	startTime, endTime, err = prize3.GetTime()
	require.NoError(err)
	assert.Equal(now.Unix(), startTime)
	assert.Equal(int64(1700000000), endTime)
}
func TestFindPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	prize, err := FindPrize(1)
	require.NoError(err)
	require.NotNil(prize)

	prize, err = FindPrize(6)
	require.NoError(err)
	require.NotNil(prize)
	assert.Equal("oss://live/labelicon/livelist/liveshow-1.png", prize.MoreInfo.CreateCard.URL)
}

func TestFindPrizes(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	prizes, err := FindPrizes([]int64{1, 6})
	require.NoError(err)
	assert.Equal(len(prizes), 2)
	assert.Equal("oss://live/labelicon/livelist/liveshow-1.png", prizes[1].MoreInfo.CreateCard.URL)
}
