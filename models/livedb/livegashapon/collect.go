package livegashapon

import (
	"encoding/json"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// GashaponCollectEvent 魔方收集任务配置
type GashaponCollectEvent struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 单位：秒

	StartTime        int64  `gorm:"column:start_time"`         // 单位：秒
	EndTime          int64  `gorm:"column:end_time"`           // 单位：秒
	TargetPoolID     int64  `gorm:"column:target_pool_id"`     // 目标奖池 ID
	TargetGiftID     int64  `gorm:"column:target_gift_id"`     // 目标礼物 ID
	CollectStartTime int64  `gorm:"column:collect_start_time"` // 单位：秒
	CollectEndTime   int64  `gorm:"column:collect_end_time"`   // 单位：秒
	BuffStartTime    int64  `gorm:"column:buff_start_time"`    // 单位：秒
	BuffEndTime      int64  `gorm:"column:buff_end_time"`      // 单位：秒
	More             []byte `gorm:"column:more"`

	MoreInfo *MoreInfo `gorm:"-"`
}

// CollectConfig 收集配置
type CollectConfig struct {
	Level      int     `json:"level"`      // 直播间任务等级
	TargetNum  int     `json:"target_num"` // 目标数量
	Multiplier float64 `json:"multiplier"` // 倍率
}

// MoreInfo .
type MoreInfo struct {
	GlobalConfigs []*CollectConfig `json:"global_configs"` // 全站任务配置
	RoomConfigs   []*CollectConfig `json:"room_configs"`   // 直播间任务配置
}

// TableName .
func (GashaponCollectEvent) TableName() string {
	return "live_gashapon_collect_event"
}

// BeforeSave .
func (gce *GashaponCollectEvent) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	if DB().NewRecord(gce) {
		gce.CreateTime = nowUnix
	}
	gce.ModifiedTime = nowUnix
	return nil
}

// AfterFind .
func (gce *GashaponCollectEvent) AfterFind() error {
	if gce.More != nil {
		return json.Unmarshal(gce.More, &gce.MoreInfo)
	}
	return nil
}
