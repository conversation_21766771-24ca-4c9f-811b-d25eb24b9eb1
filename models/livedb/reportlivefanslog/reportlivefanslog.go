package reportlivefanslog

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
)

// TableName 主播粉丝日志表名
func TableName() string {
	return "report_live_fans_log"
}

// LiveFansLog 主播粉丝日志
type LiveFansLog struct {
	ID          int64     `gorm:"column:id;primary_key"`
	GmtCreate   time.Time `gorm:"column:gmt_create"`
	GmtModified time.Time `gorm:"column:gmt_modified"`
	UserID      int64     `gorm:"column:user_id"`
	// FIXME: BizDate 在 MySQL 是 DATE 类型，无时区，后续研究怎么调整
	BizDate  time.Time `gorm:"column:bizdate"` // DATE 类型，格式 YYYY-mm-dd
	Follow   int64     `gorm:"column:follow"`
	Unfollow int64     `gorm:"column:unfollow"`
}

// DB the db instance of LiveFansLog model
func (l LiveFansLog) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// TableName table name
func (LiveFansLog) TableName() string {
	return TableName()
}
