package databus

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	luckybagutil "github.com/MiaoSiLa/live-service/models/livedb/luckybag/util"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
)

type drawLuckyBagMessage struct {
	LuckyBagID int64 `json:"lucky_bag_id"`
}

// DelayDrawLuckyBag 福袋开奖 - 生产者
func DelayDrawLuckyBag(lb *luckybag.InitiateRecord) {
	if lb.Status != luckybag.StatusPending {
		return
	}

	var (
		key      = keys.DelayKeyDrawLuckyBag1.Format(lb.ID)
		sendTime = time.Unix(lb.ScheduledEndTime, 0)
	)
	err := service.DatabusSendDelay(key, drawLuckyBagMessage{LuckyBagID: lb.ID}, sendTime)
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Errorf("send delay msg error: %v", err)
		// PASS
	}
}

// DelayDrawLuckyBagOperator 福袋开奖 - 消费者
func DelayDrawLuckyBagOperator() func(*databus.Message) {
	return func(message *databus.Message) {
		if !keys.DelayKeyDrawLuckyBag1.MatchKey(message.Key) {
			return
		}

		var msg *drawLuckyBagMessage
		err := json.Unmarshal(message.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}
		lb, err := luckybag.FindShowingInitiateRecordByID(msg.LuckyBagID)
		if err != nil {
			logger.WithField("lucky_bag_id", msg.LuckyBagID).Error(err)
			return
		}
		if lb == nil || lb.Status != luckybag.StatusPending {
			return
		}
		_, err = luckybagutil.DrawLuckyBag(lb)
		if err != nil {
			logger.WithField("lucky_bag_id", msg.LuckyBagID).Error(err)
			return
		}
	}
}
