package databus

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestDatabusTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(drawLuckyBagMessage{}, "lucky_bag_id")
}

func TestDelayDrawLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLuckyBagID := int64(1)
	require.NoError(service.Redis.Del(keys.DelayKeyDrawLuckyBag1.Format(testLuckyBagID)).Err())
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	packet := &luckybag.InitiateRecord{
		ID:     testLuckyBagID,
		Status: luckybag.StatusPending,
	}
	DelayDrawLuckyBag(packet)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyDrawLuckyBag1.Format(testLuckyBagID), m.Key)

	var message drawLuckyBagMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(testLuckyBagID, message.LuckyBagID)
}

func TestDelayDrawLuckyBagOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := luckybag.InitiateRecord{
		UserID:        12,
		RoomID:        1,
		Type:          luckybag.TypeDrama,
		RewardType:    luckybag.RewardTypeDrama,
		Status:        0,
		Num:           1,
		TargetType:    luckybag.TargetTypeAll,
		JoinNum:       100,
		TransactionID: 1,
		More:          []byte("{}"),
	}
	require.NoError(record.Create())
	f := DelayDrawLuckyBagOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	qm := drawLuckyBagMessage{
		LuckyBagID: record.ID,
	}
	f(&databus.Message{
		Key:   keys.DelayKeyDrawLuckyBag1.Format(record.ID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
	lb, err := luckybag.FindShowingInitiateRecordByID(record.ID)
	require.NoError(err)
	require.NotNil(lb)
	assert.Equal(luckybag.StatusFinish, lb.Status)
}
