package luckybag

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Type 福袋类型
const (
	TypeDrama  = iota + 1 // 剧集福袋
	TypeEntity            // 实物福袋
)

// TargetType 福袋参与对象类型
const (
	TargetTypeAll      = iota // 所有人
	TargetTypeFollow          // 关注
	TargetTypeMedal           // 粉丝勋章
	TargetTypeSuperFan        // 超粉
)

// RewardType 福袋奖励类型
const (
	RewardTypeDrama          = iota + 1 // 剧集奖励
	RewardTypeEntityPersonal            // 个人实物奖励
	RewardTypeEntityDrama               // 剧集实物奖励
)

// RecordStatus 福袋状态
const (
	StatusDeleted = iota - 1 // 已删除
	StatusPending            // 待开奖
	StatusDrawing            // 开奖中
	StatusFinish             // 结束
)

// defaultRoomListOrderBy 福袋房间列表默认排序
/*
列表的直播间排序：
奖品数量越多，排序越靠前
抽奖倒计时越短，排序越靠前
以上都相同的情况，按福袋 ID 从小到大排序
*/
const defaultRoomListOrderBy = "num DESC, scheduled_end_time ASC, id ASC"

// InitiateRecord 福袋发起记录
type InitiateRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	DeleteTime   int64 `gorm:"column:delete_time"`

	UserID           int64  `gorm:"column:user_id"`
	RoomID           int64  `gorm:"column:room_id"`
	CreatorID        int64  `gorm:"column:creator_id"`
	Type             int    `gorm:"column:type"`
	RewardType       int    `gorm:"column:reward_type"`
	Status           int    `gorm:"column:status"`
	PrizeDramaID     int64  `gorm:"column:prize_drama_id"`
	PrizeIPRID       int64  `gorm:"column:prize_ipr_id"`
	Name             string `gorm:"column:name"`
	Num              int    `gorm:"column:num"`
	TargetType       int    `gorm:"column:target_type"`
	Message          string `gorm:"column:message"`
	StartTime        int64  `gorm:"column:start_time"`
	EndTime          int64  `gorm:"column:end_time"`
	RewardTime       int64  `gorm:"column:reward_time"`
	ScheduledEndTime int64  `gorm:"column:scheduled_end_time"`
	JoinNum          int64  `gorm:"column:join_num"` // 非实时更新
	TransactionID    int64  `gorm:"column:transaction_id"`
	More             []byte `gorm:"column:more"`

	MoreInfo *MoreInfo `gorm:"-"`
}

// MoreInfo 额外信息
type MoreInfo struct {
	PrizePrice           int64                 `json:"prize_price,omitempty"`             // 剧集单价，钻
	PrizeIcon            string                `json:"prize_icon,omitempty"`              // 奖品图标
	PrizeDramaCoverColor *int64                `json:"prize_drama_cover_color,omitempty"` // 剧集背景图主颜色，十进制表示
	StartTargetNum       *int64                `json:"start_target_num,omitempty"`        // 参与对象初始人数
	EndTargetNum         *int64                `json:"end_target_num,omitempty"`          // 参与对象结束人数
	CreatorUsername      string                `json:"creator_username,omitempty"`        // 主播昵称
	PrizeIPRName         string                `json:"prize_ipr_name,omitempty"`          // 剧集 IP 名
	PrizeVipDiscount     *PrizeVipDiscountInfo `json:"prize_vip_discount,omitempty"`      // 奖品会员折扣信息

	PrizeIconURL string `json:"-"`
}

// PrizeVipDiscountInfo 奖品会员折扣信息
type PrizeVipDiscountInfo struct {
	DiscountPrice int     `json:"discount_price"` // 剧集折扣价。单位：钻
	OriginalPrice int     `json:"original_price"` // 剧集原价。单位：钻
	Rate          float32 `json:"rate"`           // 会员折扣值，e.g. 0.8
	Num           int     `json:"num"`            // 数量
}

// TableName table name
func (InitiateRecord) TableName() string {
	return "live_lucky_bag_initiate_record"
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// BeforeCreate gorm hook BeforeCreate
func (i *InitiateRecord) BeforeCreate() error {
	i.CreateTime = goutil.TimeNow().Unix()
	i.ModifiedTime = i.CreateTime

	var err error
	i.More, err = json.Marshal(i.MoreInfo)
	if err != nil {
		return err
	}
	return nil
}

// AfterFind gorm hook
func (i *InitiateRecord) AfterFind() (err error) {
	i.MoreInfo, err = i.unmarshalMore()
	if err != nil {
		logger.WithFields(logger.Fields{
			"id": i.ID,
		}).Errorf("Error found when unmarshal more info: %v", err)
		// PASS
	}
	if i.MoreInfo != nil && i.MoreInfo.PrizeIcon != "" {
		i.MoreInfo.PrizeIconURL = storage.ParseSchemeURL(i.MoreInfo.PrizeIcon)
	}
	return
}

// Create 创建发起记录
func (i *InitiateRecord) Create() error {
	return DB().Create(i).Error
}

// CreateTx 使用事务创建福袋发起记录
func (i *InitiateRecord) CreateTx(tx *gorm.DB) error {
	if tx == nil {
		panic("tx is nil")
	}
	return tx.Create(i).Error
}

// FindInitiateRecordsByIDs 通过 ID 列表批量查找福袋发起记录
func FindInitiateRecordsByIDs(ids []int64) ([]*InitiateRecord, error) {
	var records []*InitiateRecord
	err := DB().Where("id IN (?)", ids).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// unmarshalMore 解析 More 的 JSON 结构
func (i *InitiateRecord) unmarshalMore() (*MoreInfo, error) {
	if len(i.More) == 0 {
		return nil, nil
	}

	var more MoreInfo
	err := json.Unmarshal(i.More, &more)
	if err != nil {
		return nil, err
	}
	return &more, nil
}

// FindShowingInitiateRecordByID 根据 ID 查询未删除的福袋记录
// 使用事务避免读写同步问题
func FindShowingInitiateRecordByID(id int64) (*InitiateRecord, error) {
	luckyBag := new(InitiateRecord)
	ok := false
	err := servicedb.Tx(DB(), func(tx *gorm.DB) error {
		err := tx.Where("id = ? AND status >= ?", id, StatusPending).Take(luckyBag).Error
		if err != nil {
			if servicedb.IsErrNoRows(err) {
				return nil
			}
			return err
		}
		ok = true
		return nil
	})
	if err != nil {
		return nil, err
	}
	if ok {
		return luckyBag, nil
	}
	return nil, nil
}

// CalculateIncreaseNum 计算参与对象增长数量
func CalculateIncreaseNum(more *MoreInfo) int64 {
	if more == nil {
		return 0
	}

	// 参与类型是所有人时不会存储
	if more.EndTargetNum == nil || more.StartTargetNum == nil {
		return 0
	}

	increaseNum := *more.EndTargetNum - *more.StartTargetNum
	if increaseNum < 0 {
		increaseNum = 0
	}
	return increaseNum
}

// ListFinishInitiateRecord 查询主播已结束的福袋发起记录列表
func ListFinishInitiateRecord(userID int64, pageSize int64, marker *MarkerListOption) ([]*InitiateRecord, *MarkerListOption, error) {
	var initiateRecords []*InitiateRecord
	db := DB().Where("user_id = ? AND status = ?", userID, StatusFinish)
	db = marker.ApplyTo(db, pageSize)
	err := db.Find(&initiateRecords).Error
	if err != nil {
		return nil, nil, err
	}
	var nextMarker *MarkerListOption
	if int64(len(initiateRecords)) > pageSize {
		nextCreateTime := initiateRecords[len(initiateRecords)-1].CreateTime
		initiateRecords = initiateRecords[:pageSize]
		lastInitiate := initiateRecords[len(initiateRecords)-1]
		nextMarker = &MarkerListOption{
			CreateTime: lastInitiate.CreateTime,
			ID:         lastInitiate.ID,
		}
		// 后续的数据和上一页最后一条数据相同时间的数据，需要进行 OR 操作
		if nextCreateTime == lastInitiate.CreateTime {
			nextMarker.HasSameTimeLast = true
		}
	}
	return initiateRecords, nextMarker, nil
}

// UpdateTransactionID 更新交易 ID
func UpdateTransactionID(tx *gorm.DB, luckyBagID, transactionID int64) error {
	if tx == nil {
		tx = DB()
	}

	now := goutil.TimeNow().Unix()
	return tx.Model(InitiateRecord{}).Where("id = ?", luckyBagID).Update(map[string]interface{}{
		"transaction_id": transactionID,
		"modified_time":  now,
	}).Error
}

// RemainDuration 计算剩余时间，单位：毫秒
// 剩余时间小于等于 0 时，接口应不返回福袋
func (i *InitiateRecord) RemainDuration() int64 {
	var remainDuration int64
	now := goutil.TimeNow()
	switch i.Status {
	case StatusPending:
		remainDuration = time.Unix(i.ScheduledEndTime, 0).Sub(now).Milliseconds()
	case StatusDrawing, StatusFinish:
		// 开奖后有 1 分钟公示期
		remainDuration = time.Unix(i.EndTime+goutil.SecondOneMinute, 0).Sub(now).Milliseconds()
	default:
		return 0
	}
	if remainDuration < 0 {
		return 0
	}
	return remainDuration
}

// FindLatestInitiateRecord 获取最新一条发起记录
func FindLatestInitiateRecord(roomID int64) (*InitiateRecord, error) {
	var record InitiateRecord
	err := DB().Where("room_id = ? AND status >= ?", roomID, StatusPending).
		Order("start_time DESC").Take(&record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}

// CountOtherRoomDramaRecords 查询同一 IPR 或 DramaID 下福袋数量，排除当前直播间
func CountOtherRoomDramaRecords(i *InitiateRecord) (int, error) {
	if i.Type != TypeDrama {
		return 0, errors.New("福袋类型错误")
	}
	if i.PrizeIPRID == 0 && i.PrizeDramaID == 0 {
		return 0, errors.New("IPRID 和 DramaID 不能同时为 0")
	}

	var count int
	db := DB().Model(&InitiateRecord{})
	if i.PrizeIPRID > 0 {
		db = db.Where("prize_ipr_id = ?", i.PrizeIPRID)
	} else {
		db = db.Where("prize_drama_id = ?", i.PrizeDramaID)
	}
	err := db.Where("type = ? AND status = ? AND room_id <> ?", TypeDrama, StatusPending, i.RoomID).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// InitiateDailyCount 当日某类型福袋发起次数
func InitiateDailyCount(roomID int64, luckyBagType int) (int, error) {
	beginOfToday := util.BeginningOfDay(goutil.TimeNow())
	var count int
	err := DB().Model(&InitiateRecord{}).
		Where("room_id = ? AND type = ? AND create_time >= ? AND create_time < ? AND status >= ?",
			roomID, luckyBagType, beginOfToday.Unix(), beginOfToday.AddDate(0, 0, 1).Unix(),
			StatusPending).
		Count(&count).Error
	return count, err
}

// RoomMetaMessage 福袋基本信息
type RoomMetaMessage struct {
	LuckyBagID     int64  `json:"lucky_bag_id"`
	Type           int    `json:"type"`
	Status         int    `json:"status"`
	ImageURL       string `json:"image_url"`
	NewImageURL    string `json:"new_image_url,omitempty"` // 新图标仅供 APP 新版本使用
	BigImageURL    string `json:"big_image_url"`
	PrizeIconURL   string `json:"prize_icon_url,omitempty"`
	RemainDuration int64  `json:"remain_duration"` // 单位：毫秒
	JoinStatus     *int   `json:"join_status,omitempty"`
	HasMore        bool   `json:"has_more"`
}

// NewRoomMetaMessage 构造福袋基本信息
func (i *InitiateRecord) NewRoomMetaMessage(userID int64, imageURL, newImageURL, bigImageURL string) *RoomMetaMessage {
	luckyBag := &RoomMetaMessage{
		LuckyBagID:     i.ID,
		Type:           i.Type,
		Status:         i.Status,
		ImageURL:       imageURL,
		NewImageURL:    newImageURL,
		BigImageURL:    bigImageURL,
		RemainDuration: i.RemainDuration(),
	}
	if luckyBag.RemainDuration <= 0 {
		return nil
	}

	// 主播不下发参与状态
	if i.CreatorID != userID {
		exists, err := JoinRecordExists(i.ID, userID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		luckyBag.JoinStatus = util.NewInt(util.BoolToInt(exists))
	}
	if i.Type == TypeDrama && i.PrizeIPRID > 0 {
		count, err := CountOtherRoomDramaRecords(i)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		luckyBag.HasMore = count > 0
	}
	// 只有剧集福袋下发奖品图标
	if i.MoreInfo != nil && i.Type == TypeDrama {
		luckyBag.PrizeIconURL = i.MoreInfo.PrizeIconURL
	}
	return luckyBag
}

// FindLatestInitiateRecordByRoomdID 通过 RoomID 查询最新一条福袋发起记录
func FindLatestInitiateRecordByRoomdID(luckyBagType int, roomID int64) (*InitiateRecord, error) {
	record := new(InitiateRecord)
	err := DB().Where("type = ? AND room_id = ? AND status >= ?", luckyBagType, roomID, StatusPending).
		Order("start_time DESC").Take(record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return record, nil
}

// ListPendingInitiateRecordBy 查询同一 IPR 或 DramaID 下福袋发起记录列表，包含当前直播间
func ListPendingInitiateRecordBy(i *InitiateRecord, pageSize int64, marker *MarkerListOption) ([]*InitiateRecord, *MarkerListOption, error) {
	if i.Type != TypeDrama {
		return nil, nil, errors.New("福袋类型错误")
	}

	db := DB().Where("type = ? AND status = ?", i.Type, StatusPending)
	if i.PrizeIPRID > 0 {
		db = db.Where("prize_ipr_id = ?", i.PrizeIPRID)
	} else {
		db = db.Where("prize_drama_id = ?", i.PrizeDramaID)
	}

	var initiateRecords []*InitiateRecord
	db = marker.ApplyTo(db, pageSize, defaultRoomListOrderBy)
	err := db.Find(&initiateRecords).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil, nil
		}
		return nil, nil, err
	}
	var nextMarker *MarkerListOption
	if int64(len(initiateRecords)) > pageSize {
		initiateRecords = initiateRecords[:pageSize]
		if marker == nil {
			nextMarker = &MarkerListOption{PageIndex: 1}
		} else {
			nextMarker = &MarkerListOption{PageIndex: marker.PageIndex + 1}
		}
	}
	return initiateRecords, nextMarker, nil
}

// CountPendingInitiateRecordBy 查询同一 IPR 或 DramaID 下福袋数量，包含当前直播间
func CountPendingInitiateRecordBy(i *InitiateRecord) (int64, error) {
	if i.Type != TypeDrama {
		return 0, errors.New("福袋类型错误")
	}
	if i.PrizeIPRID == 0 && i.PrizeDramaID == 0 {
		return 0, errors.New("IPRID 和 DramaID 不能同时为 0")
	}

	var count int64
	db := DB().Model(&InitiateRecord{})
	if i.PrizeIPRID > 0 {
		db = db.Where("prize_ipr_id = ?", i.PrizeIPRID)
	} else {
		db = db.Where("prize_drama_id = ?", i.PrizeDramaID)
	}
	err := db.Where("type = ? AND status = ?", i.Type, StatusPending).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// FindPendingInitiateMaps 返回 map[key][]*InitiateRecord
func FindPendingInitiateMaps(luckyBagType int, ids []int64, column string, limit int) (map[int64][]*InitiateRecord, error) {
	ids = util.Uniq(ids)
	if len(ids) == 0 {
		return nil, nil
	}

	var records []*InitiateRecord
	switch column {
	case "prize_ipr_id", "prize_drama_id":
		var queries []string
		/*
			生成的 SQL:
			SELECT * FROM (
			  SELECT * FROM live_lucky_bag_initiate_record WHERE type = 1 AND prize_ipr_id = 98 AND status = 0
			  ORDER BY num DESC, join_num ASC, scheduled_end_time ASC, id ASC LIMIT 3
			) AS t0 UNION
			SELECT * FROM (
			  SELECT * FROM live_lucky_bag_initiate_record WHERE type = 1 AND prize_ipr_id = 99 AND status = 0
			  ORDER BY num DESC, join_num ASC, scheduled_end_time ASC, id ASC LIMIT 3
			) AS t1
		*/
		for i, id := range ids {
			// 拼接每个子查询
			subQuery := fmt.Sprintf(`
SELECT * FROM (
  SELECT * FROM %s WHERE type = %d AND %s = %d AND status = %d
  ORDER BY %s LIMIT %d
) AS t%d`, InitiateRecord{}.TableName(), luckyBagType, column, id, StatusPending, defaultRoomListOrderBy, limit, i)
			queries = append(queries, subQuery)
		}
		var initiateSQL string
		if servicedb.Driver == servicedb.DriverSqlite {
			// 解决 SQLite 子查询在返回结果集时并不保证顺序
			initiateSQL = fmt.Sprintf(`
SELECT * FROM (%s) AS combined_results
ORDER BY %s`, strings.Join(queries, " UNION "), defaultRoomListOrderBy)
		} else {
			// 使用 UNION 合并子查询
			initiateSQL = strings.Join(queries, " UNION ")
		}

		err := service.LiveDB.Raw(initiateSQL).Find(&records).Error
		if err != nil {
			return nil, err
		}
		if len(records) == 0 {
			return nil, nil
		}
	default:
		panic(fmt.Sprintf("unsupported column: %s", column))
	}

	return initiateSliceToMaps(records, column), nil
}

// initiateSliceToMaps 将 InitiateRecord slice 转为 map[key][]*InitiateRecord
func initiateSliceToMaps(initiates []*InitiateRecord, column string) map[int64][]*InitiateRecord {
	if len(initiates) == 0 {
		return nil
	}
	m := make(map[int64][]*InitiateRecord)
	for _, initiate := range initiates {
		var key int64
		switch column {
		case "prize_ipr_id":
			key = initiate.PrizeIPRID
		case "prize_drama_id":
			key = initiate.PrizeDramaID
		default:
			panic(fmt.Sprintf("unsupported column: %s", column))
		}
		m[key] = append(m[key], initiate)
	}
	return m
}

// UpdateInitiateRecordDrawing 更新福袋记录状态为开奖中
func UpdateInitiateRecordDrawing(luckyBagID int64, endTargetNum *int64) (bool, error) {
	nowUnix := goutil.TimeNow().Unix()
	update := map[string]any{
		"status":        StatusDrawing,
		"end_time":      nowUnix,
		"modified_time": nowUnix,
	}
	if endTargetNum != nil {
		update["more"] = gorm.Expr("JSON_SET(more, '$.end_target_num', ?)", *endTargetNum)
	}
	db := DB().Table(InitiateRecord{}.TableName()).
		Where("id = ? AND status = ?", luckyBagID, StatusPending).Update(update)
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// UpdateInitiateRecordFinish 更新福袋记录状态为结束
func UpdateInitiateRecordFinish(tx *gorm.DB, luckyBagID int64) (bool, int64, error) {
	if tx == nil {
		tx = DB()
	}
	joinNum, err := CountUsersByLuckyBagID(tx, luckyBagID)
	if err != nil {
		logger.WithField("lucky_bag_id", luckyBagID).Error(err)
		// PASS
	}
	nowUnix := goutil.TimeNow().Unix()
	db := tx.Table(InitiateRecord{}.TableName()).
		Where("id = ? AND status = ?", luckyBagID, StatusDrawing).Update(map[string]any{
		"status":        StatusFinish,
		"reward_time":   nowUnix,
		"join_num":      joinNum,
		"modified_time": nowUnix,
	})
	if err := db.Error; err != nil {
		return false, 0, err
	}
	return db.RowsAffected > 0, joinNum, nil
}

// Refund 退款
func Refund(ctx mrpc.UserContext, record *InitiateRecord, num int) error {
	if num <= 0 || record.Type != TypeDrama {
		return nil
	}
	_, err := userapi.RefundGoods(ctx, userapi.RefundGoodsParam{
		TransactionID: record.TransactionID,
		GoodsType:     userapi.GoodsTypeLuckyBag,
		Goods: []*userapi.RefundGoodsElem{
			{
				ID:              record.PrizeDramaID,
				TransactionType: userapi.TransactionTypeDrama,
				Title:           record.Name,
				Price:           record.MoreInfo.PrizePrice,
				Num:             num,
			},
		},
	})
	if err != nil {
		return err
	}
	return nil
}

// FindTargetNum 获取目标人数
func FindTargetNum(lb *InitiateRecord) (int64, bool, error) {
	switch lb.TargetType {
	case TargetTypeFollow:
		u, err := user.FindByUserID(lb.CreatorID)
		if err != nil {
			return 0, false, err
		}
		if u == nil {
			return 0, false, nil
		}
		return u.FansNum, true, nil
	case TargetTypeMedal:
		num, err := livemedal.CountRoomMedal(lb.RoomID)
		if err != nil {
			return 0, false, err
		}
		return num, true, nil
	case TargetTypeSuperFan:
		num, err := livemedal.CountRoomSuperMedal(lb.RoomID)
		if err != nil {
			return 0, false, err
		}
		return num, true, nil
	}
	return 0, false, nil
}

// DeletePendingLuckyBag 删除待开奖的福袋
func DeletePendingLuckyBag(tx *gorm.DB, luckyBagID int64, reason string) (bool, error) {
	if tx == nil {
		tx = DB()
	}
	joinNum, err := CountUsersByLuckyBagID(tx, luckyBagID)
	if err != nil {
		logger.WithField("lucky_bag_id", luckyBagID).Error(err)
		// PASS
	}
	nowUnix := goutil.TimeNow().Unix()
	db := tx.Table(InitiateRecord{}.TableName()).
		Where("id = ? AND status = ?", luckyBagID, StatusPending).
		Updates(map[string]interface{}{
			"modified_time": nowUnix,
			"delete_time":   nowUnix,
			"status":        StatusDeleted,
			"join_num":      joinNum,
			"more":          gorm.Expr("JSON_SET(more, '$.reason', ?)", reason),
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// FindDelayPendingInitiateRecord 查询延期 delayDuration 后仍未开奖的福袋记录
func FindDelayPendingInitiateRecord(delayDuration time.Duration) ([]*InitiateRecord, error) {
	if delayDuration <= 0 {
		panic("delayDuration must be greater than 0")
	}
	var initiateRecords []*InitiateRecord
	now := goutil.TimeNow()
	err := DB().Where("status = ? AND scheduled_end_time < ?", StatusPending, now.Add(-delayDuration).Unix()).
		Find(&initiateRecords).Error
	if err != nil {
		return nil, err
	}
	return initiateRecords, nil
}

// FindPendingInitiateRecordByRoomID 根据房间 ID 获取进行中的福袋
func FindPendingInitiateRecordByRoomID(roomID int64) (*InitiateRecord, error) {
	var initiateRecord InitiateRecord
	err := DB().Where("status = ? AND room_id = ?", StatusPending, roomID).Take(&initiateRecord).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &initiateRecord, nil
}

// ListPendingInitiateRecordByRoomIDs 根据房间 ID 列表获取进行中的福袋
func ListPendingInitiateRecordByRoomIDs(roomIDs []int64) ([]InitiateRecord, error) {
	var initiateRecords []InitiateRecord
	err := DB().Select("creator_id").Where("status = ? AND room_id IN (?)", StatusPending, roomIDs).Find(&initiateRecords).Error
	if err != nil {
		return nil, err
	}
	return initiateRecords, nil
}

// FindPendingInitiateDramaRecords 获取所有进行中的剧集福袋记录
func FindPendingInitiateDramaRecords() ([]*InitiateRecord, error) {
	var records []*InitiateRecord
	err := DB().Where("type = ? AND status = ?", TypeDrama, StatusPending).
		Order(defaultRoomListOrderBy).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}
