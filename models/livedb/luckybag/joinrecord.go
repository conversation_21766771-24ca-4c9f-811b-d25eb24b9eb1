package luckybag

import (
	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Status 用户状态
const (
	StatusNormal = iota
	StatusRisk
)

// Source 用户参与来源
const (
	SourceLive = iota // 来源直播间
	SourceAD          // 来源广告
)

// JoinRecord 用户参与福袋抽奖记录
type JoinRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	LuckyBagID int64  `gorm:"column:lucky_bag_id"`
	UserID     int64  `gorm:"column:user_id"`
	Status     int    `gorm:"column:status"`
	IP         string `gorm:"column:ip"` // 用户 IP
	Source     int    `gorm:"column:source"`
}

// TableName table name
func (JoinRecord) TableName() string {
	return "live_lucky_bag_join_record"
}

// BeforeCreate hook
func (record *JoinRecord) BeforeCreate() error {
	now := goutil.TimeNow().Unix()
	record.CreateTime = now
	record.ModifiedTime = now
	return nil
}

// Create 插入创建记录
func (record *JoinRecord) Create() error {
	return DB().Create(record).Error
}

// JoinRecordExists 用户参与福袋抽奖记录是否存在
func JoinRecordExists(luckybagID, userID int64) (bool, error) {
	db := DB().Table(JoinRecord{}.TableName()).
		Where("lucky_bag_id = ? AND user_id = ?", luckybagID, userID)
	return servicedb.Exists(db)
}

// CountOneDayJoinRecordByUser 查询用户当天福袋参与记录
func CountOneDayJoinRecordByUser(userID int64) (int64, error) {
	beginTime := goutil.BeginningOfDay(goutil.TimeNow())
	var count int64
	err := DB().Table(JoinRecord{}.TableName()).
		Where("user_id = ? AND create_time >= ? AND create_time < ?", userID, beginTime.Unix(),
			beginTime.AddDate(0, 0, 1).Unix()).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// CountOneDayJoinRecordByIP 查询 ip 当天福袋参与记录
func CountOneDayJoinRecordByIP(ip string) (int64, error) {
	beginTime := goutil.BeginningOfDay(goutil.TimeNow())
	var count int64
	err := DB().Table(JoinRecord{}.TableName()).
		Where("ip = ? AND create_time >= ? AND create_time < ?", ip, beginTime.Unix(),
			beginTime.AddDate(0, 0, 1).Unix()).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// FindAllJoinUserIDs 获取福袋参与用户
func FindAllJoinUserIDs(luckyBagID int64, status int) ([]int64, error) {
	userIDs := make([]int64, 0)
	err := DB().Table(JoinRecord{}.TableName()).
		Select("user_id").
		Where("lucky_bag_id = ? AND status = ?", luckyBagID, status).
		Pluck("user_id", &userIDs).Error
	if err != nil {
		return nil, err
	}
	return userIDs, nil
}

// CountUsersByLuckyBagID 查询福袋参与人数
// 福袋结束更新状态时，需要在事务中查询总数 join_num，然后同状态一起更新到数据库中
func CountUsersByLuckyBagID(tx *gorm.DB, luckyBagID int64) (int64, error) {
	if tx == nil {
		tx = DB()
	}
	var count int64
	err := tx.Table(JoinRecord{}.TableName()).
		Where("lucky_bag_id = ?", luckyBagID).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}
