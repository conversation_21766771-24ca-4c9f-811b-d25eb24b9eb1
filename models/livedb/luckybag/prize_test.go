package luckybag

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestPrizeTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(UserPrize{}, "id", "create_time", "modified_time", "lucky_bag_id", "reward_time",
		"user_id", "redeem_end_time", "redeem_time", "redeem_user_id", "transaction_id")
	kc.Check(LuckyUser{}, "user_id", "username")
}

func TestListUserPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	// 测试中奖记录列表为空
	userPrize, nextMarker, err := ListUserPrize(userID, 5, nil)
	require.NoError(err)
	assert.Nil(nextMarker)
	assert.Empty(userPrize)

	// mock 时间，保证中奖时间
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 21, 0, 0, 0, time.Local)
	})
	defer cancel()
	// 创建用户福袋中奖记录
	/*
		id create_time
		1  1717678800
		2  1717675200
		3  1717639200
		4  1717639200
		5  1717639200
	*/
	record := UserPrize{
		LuckyBagID: 10,
		RewardTime: 1717642800,
		UserID:     userID,
	}
	require.NoError(record.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 20, 0, 0, 0, time.Local)
	})
	record2 := UserPrize{
		LuckyBagID: 11,
		RewardTime: 1717675200,
		UserID:     userID,
	}
	require.NoError(record2.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 10, 0, 0, 0, time.Local)
	})
	record3 := UserPrize{
		LuckyBagID: 12,
		RewardTime: 1717639200,
		UserID:     userID,
	}
	require.NoError(record3.Create())

	record4 := UserPrize{
		LuckyBagID: 13,
		RewardTime: 1717639200,
		UserID:     userID,
	}
	require.NoError(record4.Create())

	record5 := UserPrize{
		LuckyBagID: 14,
		RewardTime: 1717639200,
		UserID:     userID,
	}
	require.NoError(record5.Create())

	// 测试请求第一页
	userPrize, nextMarker, err = ListUserPrize(userID, 2, nil)
	require.NoError(err)
	assert.Equal(&MarkerListOption{
		CreateTime:      record2.CreateTime,
		ID:              record2.ID,
		HasSameTimeLast: false,
	}, nextMarker)
	assert.Len(userPrize, 2)
	assert.Equal(int64(10), userPrize[0].LuckyBagID)
	assert.Equal(int64(11), userPrize[1].LuckyBagID)
	assert.Equal(int64(1717642800), userPrize[0].RewardTime)
	assert.Equal(int64(1717675200), userPrize[1].RewardTime)
	assert.Equal(userID, userPrize[0].UserID)

	// 测试请求第二页
	marker := &MarkerListOption{
		CreateTime:      record2.CreateTime,
		ID:              record2.ID,
		HasSameTimeLast: false,
	}
	userPrize, nextMarker, err = ListUserPrize(userID, 2, marker)
	require.NoError(err)
	assert.Equal(&MarkerListOption{
		CreateTime:      record4.CreateTime,
		ID:              record4.ID,
		HasSameTimeLast: true,
	}, nextMarker)
	assert.Len(userPrize, 2)
	assert.Equal(int64(14), userPrize[0].LuckyBagID)
	assert.Equal(int64(13), userPrize[1].LuckyBagID)
	assert.Equal(int64(1717639200), userPrize[0].RewardTime)
	assert.Equal(int64(1717639200), userPrize[1].RewardTime)
	assert.Equal(userID, userPrize[0].UserID)

	// 测试请求第三页
	marker = &MarkerListOption{
		CreateTime:      record4.CreateTime,
		ID:              record4.ID,
		HasSameTimeLast: true,
	}
	userPrize, nextMarker, err = ListUserPrize(userID, 2, marker)
	require.NoError(err)
	assert.Nil(nextMarker)
	assert.Len(userPrize, 1)
	assert.Equal(int64(12), userPrize[0].LuckyBagID)
	assert.Equal(int64(1717639200), userPrize[0].RewardTime)
	assert.Equal(userID, userPrize[0].UserID)

	// 测试无分页
	userPrize, nextMarker, err = ListUserPrize(userID, 5, nil)
	require.NoError(err)
	assert.Nil(nextMarker)
	assert.Len(userPrize, 5)
}

func TestFindUserPrizeByLuckyBagID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户未中奖
	userPrize, err := FindUserPrizeByLuckyBagID(1, 1)
	require.NoError(err)
	assert.Nil(userPrize)

	// 测试用户已中奖
	userPrize, err = FindUserPrizeByLuckyBagID(3457182, 1)
	require.NoError(err)
	require.NotNil(userPrize)
	assert.EqualValues(1, userPrize.LuckyBagID)
	assert.EqualValues(3457182, userPrize.UserID)
}

func TestListLuckyUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs, err := ListLuckyUserIDs(20)
	require.NoError(err)
	assert.Nil(userIDs)

	record := UserPrize{
		LuckyBagID: 20,
		RewardTime: 1717642800,
		UserID:     12,
	}
	require.NoError(record.Create())

	record = UserPrize{
		LuckyBagID: 20,
		RewardTime: 1717675200,
		UserID:     10,
	}
	require.NoError(record.Create())

	userIDs, err = ListLuckyUserIDs(20)
	require.NoError(err)
	assert.Equal([]int64{10, 12}, userIDs)
	require.NoError(DB().Table(UserPrize{}.TableName()).Where("lucky_bag_id = ?", record.LuckyBagID).Delete("").Error)
}

func TestNewLuckyUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	luckyUsers, err := NewLuckyUsers([]int64{12})
	require.NoError(err)
	require.Len(luckyUsers, 1)
	assert.EqualValues(12, luckyUsers[0].UserID)
}

func TestFindUserPrizeByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试记录不存在
	userPrize, err := FindUserPrizeByID(100000)
	require.NoError(err)
	assert.Nil(userPrize)

	// 测试记录存在
	userPrize, err = FindUserPrizeByID(1)
	require.NoError(err)
	require.NotNil(userPrize)
	assert.EqualValues(1, userPrize.ID)
}
