package luckybag

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestJoinRecordTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(JoinRecord{}, "id", "create_time", "modified_time", "lucky_bag_id", "user_id", "status",
		"ip", "source")
}

func TestJoinRecordExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(JoinRecord{},
		"lucky_bag_id = ? AND user_id = ?", 10, 9074509).Error
	require.NoError(err)

	exists, err := JoinRecordExists(10, 9074509)
	require.NoError(err)
	assert.False(exists)

	record := JoinRecord{
		LuckyBagID: 10,
		UserID:     9074509,
	}
	require.NoError(record.Create())

	exists, err = JoinRecordExists(10, 9074509)
	require.NoError(err)
	assert.True(exists)
}

func TestCountOneDayJoinRecordByUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count, err := CountOneDayJoinRecordByUser(123)
	require.NoError(err)
	assert.EqualValues(1, count)

	count, err = CountOneDayJoinRecordByUser(-123)
	require.NoError(err)
	assert.Zero(count)
}

func TestCountOneDayJoinRecordByIP(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count, err := CountOneDayJoinRecordByIP("127.0.0.1")
	require.NoError(err)
	assert.GreaterOrEqual(count, int64(2))

	count, err = CountOneDayJoinRecordByIP("*********")
	require.NoError(err)
	assert.Zero(count)
}

func TestFindAllJoinUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testLuckyBagID int64 = 11

	res, err := FindAllJoinUserIDs(-999, StatusNormal)
	require.NoError(err)
	assert.Empty(res)

	res, err = FindAllJoinUserIDs(testLuckyBagID, StatusNormal)
	require.NoError(err)
	assert.NotEmpty(res)

	res, err = FindAllJoinUserIDs(testLuckyBagID, StatusRisk)
	require.NoError(err)
	assert.NotEmpty(res)
}

func TestCountUsesByLuckyBagID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	num, err := CountUsersByLuckyBagID(nil, 0)
	require.NoError(err)
	assert.Zero(num)

	num, err = CountUsersByLuckyBagID(service.LiveDB, 1)
	require.NoError(err)
	assert.NotZero(num)
}
