package luckybag

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goserviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// MarkerListOption 标识参数
type MarkerListOption struct {
	CreateTime      int64 // 创建时间的时间戳，单位：秒
	ID              int64 // 主键 ID
	HasSameTimeLast bool  // 后续的数据是否包含和最后一条数据相同时间的数据

	PageIndex int64
}

// Encode 构造 marker 字符串
func (m *MarkerListOption) Encode() string {
	if m.PageIndex > 0 {
		return fmt.Sprintf("%d", m.PageIndex)
	}
	return fmt.Sprintf("%d,%d,%d", m.CreateTime, m.ID, util.BoolToInt(m.HasSameTimeLast))
}

// ApplyTo 应用 MarkerListOption 到 db 的 Query
func (m *MarkerListOption) ApplyTo(db *gorm.DB, pageSize int64, orderBy ...string) *gorm.DB {
	if m != nil && m.CreateTime > 0 {
		if m.HasSameTimeLast {
			db = db.Where("create_time < ? OR (create_time = ? AND id < ?)", m.CreateTime, m.CreateTime, m.ID)
		} else {
			db = db.Where("create_time < ?", m.CreateTime)
		}
	} else if m != nil && m.PageIndex > 0 {
		db = db.Offset(pageSize * m.PageIndex)
	}
	orderStr := "create_time DESC, id DESC" // 默认排序
	if len(orderBy) > 0 {
		orderStr = orderBy[0]
	}
	// 查询多一条记录判断是否有更多
	return db.Order(orderStr).Limit(pageSize + 1)
}

// ParseMarker 解析 marker 参数，获取上一页最后一条记录的创建时间、主键 ID、后续的数据是否包含和最后一条数据相同时间的数据
func ParseMarker(marker string) (*MarkerListOption, error) {
	if marker == "" {
		return nil, nil
	}
	s := strings.Split(marker, ",")
	if len(s) != 3 {
		return nil, errors.New("参数格式错误")
	}
	createTime, err := strconv.ParseInt(s[0], 10, 64)
	if err != nil {
		return nil, nil
	}
	id, err := strconv.ParseInt(s[1], 10, 64)
	if err != nil {
		return nil, err
	}
	hasSameTimeLast, err := strconv.Atoi(s[2])
	if err != nil {
		return nil, err
	}
	if createTime < 0 || id < 0 || hasSameTimeLast < 0 {
		return nil, errors.New("参数值不在有效范围内")
	}
	return &MarkerListOption{
		CreateTime:      createTime,
		ID:              id,
		HasSameTimeLast: util.IntToBool(hasSameTimeLast),
	}, nil
}

// Info 福袋信息
type Info struct {
	LuckyBagID     int64       `json:"lucky_bag_id"`
	Status         int         `json:"status"`
	Type           int         `json:"type"`
	RewardType     int         `json:"reward_type"`
	PrizeName      string      `json:"prize_name"`
	PrizeNum       int         `json:"prize_num"`
	PrizePrice     int64       `json:"prize_price,omitempty"` // 单位：钻
	PrizeIconURL   string      `json:"prize_icon_url,omitempty"`
	RemainDuration int64       `json:"remain_duration"` // 单位：毫秒
	LuckyUsers     []LuckyUser `json:"lucky_users,omitempty"`
	ImageURL       string      `json:"image_url,omitempty"`
}

// FullInfo 福袋完整信息
type FullInfo struct {
	Info `json:",inline"`

	Keyword     string `json:"keyword"`
	JoinNum     int64  `json:"join_num"`
	IncreaseNum *int64 `json:"increase_num,omitempty"` // 用户侧数据不会返回
	TargetType  int    `json:"target_type"`
	JoinStatus  *int   `json:"join_status,omitempty"` // 主播侧数据不会返回
}

// NewInfo new Info
func NewInfo(r *InitiateRecord) Info {
	info := Info{
		LuckyBagID:     r.ID,
		Status:         r.Status,
		Type:           r.Type,
		RewardType:     r.RewardType,
		PrizeName:      r.Name,
		PrizeNum:       r.Num,
		RemainDuration: r.RemainDuration(),
	}
	if r.MoreInfo != nil {
		if r.Type == TypeDrama {
			// 此价格会在参与福袋的用户端展示，需要展示为原价
			if r.MoreInfo.PrizeVipDiscount != nil {
				// 有折扣信息时，根据折扣信息计算原价
				info.PrizePrice = int64(r.MoreInfo.PrizeVipDiscount.OriginalPrice / r.MoreInfo.PrizeVipDiscount.Num)
			} else {
				info.PrizePrice = r.MoreInfo.PrizePrice
			}
		}
		info.PrizeIconURL = r.MoreInfo.PrizeIconURL
	}
	return info
}

// NewFullInfo new FullInfo
func NewFullInfo(r *InitiateRecord) *FullInfo {
	joinNum := r.JoinNum
	if r.Status == StatusPending || r.Status == StatusDrawing {
		// 福袋未结束时 live_lucky_bag_initiate_record.join_num 不会被更新为实际参与人数
		var err error
		joinNum, err = loadJoinNumFromCache(r.ID)
		if err != nil {
			logger.WithField("lucky_bag_id", r.ID).Error(err)
			// PASS
		}
	}
	return &FullInfo{
		Info:       NewInfo(r),
		Keyword:    r.Message,
		TargetType: r.TargetType,
		JoinNum:    joinNum,
	}
}

// loadJoinNumFromCache 获取福袋参与人数
func loadJoinNumFromCache(luckyBagID int64) (int64, error) {
	key := keys.KeyLuckyBagJoinNum1.Format(luckyBagID)
	joinNum, err := service.Redis.Get(key).Int64()
	if err != nil {
		if goserviceredis.IsRedisNil(err) {
			return 0, nil
		}
		return 0, err
	}
	return joinNum, nil
}
