package util

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestDrawLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := luckybag.InitiateRecord{
		UserID:        12,
		RoomID:        1,
		Type:          luckybag.TypeDrama,
		RewardType:    luckybag.RewardTypeDrama,
		Status:        0,
		Num:           1,
		TargetType:    luckybag.TargetTypeAll,
		JoinNum:       100,
		TransactionID: 1,
		More:          []byte("{}"),
	}
	require.NoError(record.Create())
	joinRecords := []luckybag.JoinRecord{
		{
			UserID:     12,
			LuckyBagID: record.ID,
			Status:     luckybag.StatusRisk,
		},
		{
			UserID:     1,
			LuckyBagID: record.ID,
			Status:     luckybag.StatusNormal,
		},
	}
	require.NoError(servicedb.BatchInsert(luckybag.DB(), luckybag.JoinRecord{}.TableName(), joinRecords))

	luckyList, err := DrawLuckyBag(&record)
	require.NoError(err)
	require.Len(luckyList, 1)
	assert.Equal(joinRecords[1].UserID, luckyList[0].UserID)
	assert.Equal(luckybag.StatusFinish, record.Status)
	assert.EqualValues(2, record.JoinNum)
}

func TestLuckyUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list := []int64{3457111, 1}
	luckyList, err := luckyUsers(list)
	require.NoError(err)
	require.Len(luckyList, 2)
	assert.Equal(list[1], luckyList[0].UserID)
	assert.Equal(list[0], luckyList[1].UserID)
}

func TestSendRoomMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := &luckybag.InitiateRecord{
		ID:     1,
		UserID: 12,
		Status: luckybag.StatusFinish,
	}
	luckyList := []luckybag.LuckyUser{
		{UserID: 1, Username: "user1"},
		{UserID: 2, Username: "user2"},
	}

	isCalled := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		var body struct {
			RoomID  int64               `json:"room_id"`
			Payload luckyBagRoomMessage `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		require.NotNil(body.Payload)

		assert.Equal(liveim.TypeLuckyBag, body.Payload.Type)
		assert.Equal(liveim.EventLuckyBagFinish, body.Payload.Event)
		assert.Equal(record.ID, body.Payload.LuckyBag.LuckyBagID)
		assert.Equal(record.Status, body.Payload.LuckyBag.Status)
		assert.Equal(luckyList, body.Payload.LuckyBag.LuckyUsers)
		isCalled = true
		return "success", nil
	})
	defer cancel()

	sendRoomMsg(1, record, luckyList)
	assert.True(isCalled)
}

func TestSendLuckySystemMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserIDs := []int64{1, 2}
	record := &luckybag.InitiateRecord{
		ID:        1,
		Type:      luckybag.TypeDrama,
		Name:      "广播剧1",
		CreatorID: 1,
		RoomID:    1,
	}
	testDate := time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)

	isCalled := false
	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(input any) (output any, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.Len(systemMsgList, 2)
		assert.Equal(testUserIDs[0], systemMsgList[0].UserID)
		assert.Equal(testUserIDs[1], systemMsgList[1].UserID)
		assert.Equal("直播间喵喵福袋中奖通知", systemMsgList[0].Title)
		assert.Equal("直播间喵喵福袋中奖通知", systemMsgList[1].Title)
		content := `恭喜你在【<a href="https://fm.uat.missevan.com/live/1" target="_blank">323</a>】的直播间抽中《广播剧1》（福袋 ID：<a href="copy:1">1</a>），请在 2020-01-01 00:00:00 前完` +
			`成<a href="https://fm.uat.missevan.com/user/luckybag/redeem?lucky_bag_id=1" target="_blank">奖品兑换</a>，` +
			`超时未兑换视作放弃奖品。如有其他疑问请联系客服。`
		assert.Equal(content, systemMsgList[0].Content)
		assert.Equal(content, systemMsgList[1].Content)
		isCalled = true
		return "success", nil
	})
	defer cancel()

	sendLuckySystemMsg(testUserIDs, record, testDate)
	assert.True(isCalled)
}

func TestSendLuckyRefundMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testDate := time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	record := &luckybag.InitiateRecord{
		ID:     1,
		Type:   luckybag.TypeDrama,
		Name:   "广播剧1",
		UserID: 1,
		MoreInfo: &luckybag.MoreInfo{
			PrizePrice: 100,
		},
		CreateTime: testDate.Unix(),
	}

	isCalled := false
	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(input any) (output any, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.Len(systemMsgList, 1)
		assert.Equal(record.UserID, systemMsgList[0].UserID)
		assert.Equal("广播剧福袋退款通知", systemMsgList[0].Title)
		content := `你在 2020-01-01 00:00:00 发放的广播剧福袋（福袋 ID：<a href="copy:1">1</a>），其中 5 份《广播剧1》未被用户抽出，价值 500 钻石，已退款至你的账户中，请注意查收。`
		assert.Equal(content, systemMsgList[0].Content)
		isCalled = true
		return "success", nil
	})
	defer cancel()

	sendLuckyRefundMsg(5, record)
	assert.True(isCalled)
}

func TestDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := luckybag.InitiateRecord{
		UserID:        12,
		RoomID:        1,
		Type:          luckybag.TypeDrama,
		RewardType:    luckybag.RewardTypeDrama,
		Status:        0,
		Num:           1,
		TargetType:    luckybag.TargetTypeAll,
		TransactionID: 1,
		More:          []byte("{}"),
	}
	require.NoError(record.Create())
	testLuckyUserIDs := []int64{12, 1, 10, 3457111}
	joinRecords := []luckybag.JoinRecord{
		{
			UserID:     testLuckyUserIDs[0],
			LuckyBagID: record.ID,
			Status:     luckybag.StatusRisk,
		},
		{
			UserID:     testLuckyUserIDs[1],
			LuckyBagID: record.ID,
			Status:     luckybag.StatusNormal,
		},
		{
			UserID:     testLuckyUserIDs[2],
			LuckyBagID: record.ID,
			Status:     luckybag.StatusNormal,
		},
		{
			UserID:     testLuckyUserIDs[3],
			LuckyBagID: record.ID,
			Status:     luckybag.StatusNormal,
		},
	}
	require.NoError(servicedb.BatchInsert(luckybag.DB(), luckybag.JoinRecord{}.TableName(), joinRecords))

	isCalled := false
	cancel := mrpc.SetMock(userapi.URIIMRoomList, func(input any) (output any, err error) {
		isCalled = true
		return userapi.IMRoomListResp{UserIDs: []int64{testLuckyUserIDs[3]}}, nil
	})
	defer cancel()

	luckyUserIDs, err := draw(&record)
	require.NoError(err)
	require.True(isCalled)
	require.Len(luckyUserIDs, 1)
	assert.Equal(testLuckyUserIDs[3], luckyUserIDs[0])

	record.Num = 3
	luckyUserIDs, err = draw(&record)
	require.NoError(err)
	require.Len(luckyUserIDs, 3)
	assert.ElementsMatch(testLuckyUserIDs[1:], luckyUserIDs)

	record.Num = 50
	luckyUserIDs, err = draw(&record)
	require.NoError(err)
	require.Len(luckyUserIDs, 4)
	assert.ElementsMatch(testLuckyUserIDs, luckyUserIDs)
}

func TestRandomUserIDs(t *testing.T) {
	assert := assert.New(t)

	userIDs := []int64{1, 2, 3, 4, 5}

	assert.Len(randomUserIDs([]int64{}, 1), 0)
	assert.Len(randomUserIDs(userIDs, 0), 0)
	assert.Len(randomUserIDs(userIDs, 1), 1)
	assert.Len(randomUserIDs(userIDs, 2), 2)
	assert.Len(randomUserIDs(userIDs, 5), 5)
	assert.Len(randomUserIDs(userIDs, 6), 5)
}
