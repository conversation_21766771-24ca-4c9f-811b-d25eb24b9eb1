package util

import (
	"errors"
	"fmt"
	"html"
	"math/rand"
	"sort"
	"time"

	"github.com/jinzhu/gorm"

	confparam "github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// DrawLuckyBag 福袋开奖
func DrawLuckyBag(lb *luckybag.InitiateRecord) ([]luckybag.LuckyUser, error) {
	var targetEndNum *int64
	targetNum, needUpdateNum, err := luckybag.FindTargetNum(lb)
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Error(err)
		// PASS
	}
	if needUpdateNum {
		targetEndNum = &targetNum
	}
	// 修改福袋状态
	ok, err := luckybag.UpdateInitiateRecordDrawing(lb.ID, targetEndNum)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("乐观锁更新失败")
	}
	// 回填福袋更新的信息
	lb.Status = luckybag.StatusDrawing
	if needUpdateNum && lb.MoreInfo != nil {
		lb.MoreInfo.EndTargetNum = &targetNum
	}
	// 抽奖
	luckyUserIDs, err := draw(lb)
	if err != nil {
		return nil, err
	}
	luckyLen := len(luckyUserIDs)
	if refundNum := lb.Num - luckyLen; refundNum > 0 {
		// 退款数量 = 奖品数量 - 中奖数量
		err := luckybag.Refund(mrpc.NewUserContextFromEnv(), lb, refundNum)
		if err != nil {
			logger.WithField("lucky_bag_id", lb.ID).Error(err)
			// PASS
		}
		sendLuckyRefundMsg(refundNum, lb)
	}
	prizeList := make([]luckybag.UserPrize, 0, luckyLen)
	now := goutil.TimeNow()
	redeemEndTime := now.Add(time.Hour * 24 * 7) // 7 天领取时长
	for i := range luckyUserIDs {
		prizeList = append(prizeList, luckybag.UserPrize{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			LuckyBagID:   lb.ID,
			RewardTime:   now.Unix(),
			UserID:       luckyUserIDs[i],
		})
		if lb.Type == luckybag.TypeDrama {
			prizeList[i].RedeemEndTime = redeemEndTime.Unix()
		}
	}
	luckyUserList, err := luckyUsers(luckyUserIDs)
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Error(err)
		return nil, err
	}
	err = servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		// 修改福袋状态
		ok, joinNum, err := luckybag.UpdateInitiateRecordFinish(tx, lb.ID)
		if err != nil {
			return err
		}
		if !ok {
			return errors.New("乐观锁更新失败")
		}
		lb.JoinNum = joinNum // 回填赋值后的 join num
		if len(prizeList) > 0 {
			// 插入中奖记录
			err := servicedb.BatchInsert(tx, luckybag.UserPrize{}.TableName(), prizeList)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	// 回填福袋更新的状态
	lb.Status = luckybag.StatusFinish
	sendRoomMsg(lb.RoomID, lb, luckyUserList)
	sendLuckySystemMsg(luckyUserIDs, lb, redeemEndTime)
	return luckyUserList, nil
}

func luckyUsers(userIDs []int64) ([]luckybag.LuckyUser, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}

	luckyList, err := luckybag.NewLuckyUsers(userIDs)
	if err != nil {
		return nil, err
	}
	sort.Slice(luckyList, func(i, j int) bool {
		// 中奖名单需要按 UserID 升序排列展示
		return luckyList[i].UserID < luckyList[j].UserID
	})
	return luckyList, nil
}

type luckyBagRoomMessage struct {
	Type     string         `json:"type"`
	Event    string         `json:"event"`
	RoomID   int64          `json:"room_id"`
	LuckyBag *luckybag.Info `json:"lucky_bag"`
}

func sendRoomMsg(roomID int64, lb *luckybag.InitiateRecord, luckyList []luckybag.LuckyUser) {
	conf, err := params.FindLuckyBag()
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Error(err)
		return
	}
	msg := luckyBagRoomMessage{
		Type:   liveim.TypeLuckyBag,
		Event:  liveim.EventLuckyBagFinish,
		RoomID: roomID,
		LuckyBag: &luckybag.Info{
			LuckyBagID:     lb.ID,
			Type:           lb.Type,
			RewardType:     lb.RewardType,
			Status:         luckybag.StatusFinish,
			PrizeName:      lb.Name,
			PrizeNum:       lb.Num,
			RemainDuration: goutil.SecondOneMinute * 1000, // 开奖后，一分钟公示期
			LuckyUsers:     luckyList,
			ImageURL:       conf.ImageURL,
		},
	}
	if lb.MoreInfo != nil {
		if lb.Type == luckybag.TypeDrama {
			// 此价格会在参与福袋的用户端展示，需要展示为原价
			if lb.MoreInfo.PrizeVipDiscount != nil {
				// 有折扣信息时，根据折扣信息计算原价
				msg.LuckyBag.PrizePrice = int64(lb.MoreInfo.PrizeVipDiscount.OriginalPrice / lb.MoreInfo.PrizeVipDiscount.Num)
			} else {
				msg.LuckyBag.PrizePrice = lb.MoreInfo.PrizePrice
			}
		}
		msg.LuckyBag.PrizeIconURL = lb.MoreInfo.PrizeIconURL
	}
	err = userapi.Broadcast(roomID, msg, &userapi.BroadcastOption{Priority: userapi.BroadcastPriorityPurchased})
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Error(err)
		// PASS
	}
}

func sendLuckySystemMsg(userIDs []int64, lb *luckybag.InitiateRecord, redeemEndTime time.Time) {
	if len(userIDs) == 0 {
		return
	}
	u, err := mowangskuser.FindByUserID(lb.CreatorID)
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Error(err)
		return
	}
	if u == nil {
		logger.WithField("creator_id", lb.CreatorID).Error("user not found")
		return
	}
	redeemEndTimeStr := redeemEndTime.Format(util.TimeFormatYMDHMS)
	title := "直播间喵喵福袋中奖通知"
	var content string
	if lb.Type == luckybag.TypeDrama {
		content = fmt.Sprintf(`恭喜你在【<a href="%s" target="_blank">%s</a>】的直播间抽中《%s》（福袋 ID：<a href="copy:%d">%d</a>），`+
			`请在 %s 前完成<a href="%s" target="_blank">奖品兑换</a>，超时未兑换视作放弃奖品。如有其他疑问请联系客服。`,
			confparam.RoomURL(lb.RoomID), html.EscapeString(u.Username), html.EscapeString(lb.Name), lb.ID, lb.ID,
			redeemEndTimeStr, confparam.LuckyBagRedeemURL(lb.ID))
	} else {
		content = fmt.Sprintf(`恭喜你在【<a href="%s" target="_blank">%s</a>】的直播间抽中【%s】（福袋 ID：<a href="copy:%d">%d</a>），`+
			`请在中奖后 7 天内私信主播收奖地址信息，超时未联系视作放弃奖品。如有其他疑问请联系客服。`,
			confparam.RoomURL(lb.RoomID), html.EscapeString(u.Username), html.EscapeString(lb.Name), lb.ID, lb.ID)
	}
	systemMsgs := make([]pushservice.SystemMsg, 0, len(userIDs))
	for i := range userIDs {
		systemMsgs = append(systemMsgs, pushservice.SystemMsg{
			UserID:  userIDs[i],
			Title:   title,
			Content: content,
		})
	}
	if len(systemMsgs) == 0 {
		return
	}
	err = service.PushService.SendSystemMsgWithOptions(systemMsgs, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func sendLuckyRefundMsg(refundNum int, lb *luckybag.InitiateRecord) {
	if lb.Type != luckybag.TypeDrama || refundNum <= 0 {
		return
	}
	msg := pushservice.SystemMsg{
		UserID: lb.UserID,
		Title:  "广播剧福袋退款通知",
		Content: fmt.Sprintf(`你在 %s 发放的广播剧福袋（福袋 ID：<a href="copy:%d">%d</a>），其中 %d 份《%s》未被用户抽出，价值 %d 钻石，已退款至你的账户中，请注意查收。`,
			time.Unix(lb.CreateTime, 0).Format(util.TimeFormatYMDHMS), lb.ID, lb.ID, refundNum, html.EscapeString(lb.Name), int64(refundNum)*lb.MoreInfo.PrizePrice),
	}
	err := service.PushService.SendSystemMsgWithOptions([]pushservice.SystemMsg{msg}, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// draw 福袋抽取
func draw(lb *luckybag.InitiateRecord) ([]int64, error) {
	// 获取在直播间内的用户
	// REVIEW：大主播直播间内的用户数可能会很多
	apiResp, err := userapi.IMRoomList(userapi.IMRoomListRequest{
		RoomID: lb.RoomID,
		Users:  true,
	}, userapi.NewUserContext(nil))
	if err != nil {
		logger.WithField("lucky_bag_id", lb.ID).Error(err)
		// PASS
	}
	joinUserIDs, err := luckybag.FindAllJoinUserIDs(lb.ID, luckybag.StatusNormal)
	if err != nil {
		return nil, err
	}
	roomUserIDs := make([]int64, 0, len(joinUserIDs))
	leaveRoomUserIDs := make([]int64, 0, len(joinUserIDs))
	for i := range joinUserIDs {
		if apiResp != nil && goutil.HasElem(apiResp.UserIDs, joinUserIDs[i]) {
			roomUserIDs = append(roomUserIDs, joinUserIDs[i])
		} else {
			leaveRoomUserIDs = append(leaveRoomUserIDs, joinUserIDs[i])
		}
	}
	// 抽取的优先级：在直播间内的正常用户 > 不在直播间内的正常用户 > 灰名单用户
	result := make([]int64, 0, lb.Num)
	// 先抽取在直播间内的正常用户
	result = append(result, randomUserIDs(roomUserIDs, lb.Num)...)
	// randomUserIDs 理论上不会抽出超过指定数量的数据
	if len(result) >= lb.Num {
		return result, nil
	}
	// 再抽取不在直播间内的正常用户
	result = append(result, randomUserIDs(leaveRoomUserIDs, lb.Num-len(result))...)
	if len(result) >= lb.Num {
		return result, nil
	}
	// 最后抽取灰名单用户
	joinBlockUserIDs, err := luckybag.FindAllJoinUserIDs(lb.ID, luckybag.StatusRisk)
	if err != nil {
		return nil, err
	}
	result = append(result, randomUserIDs(joinBlockUserIDs, lb.Num-len(result))...)
	return result, nil
}

func randomUserIDs(userIDs []int64, limit int) []int64 {
	n := len(userIDs)
	if n <= limit {
		return userIDs
	}
	// partial shuffle
	// https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle
	for i := 0; i < limit; i++ {
		j := i + rand.Intn(n-i)
		userIDs[i], userIDs[j] = userIDs[j], userIDs[i]
	}
	return userIDs[:limit]
}
