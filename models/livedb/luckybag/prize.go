package luckybag

import (
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// UserPrize 用户福袋中奖记录
type UserPrize struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	LuckyBagID    int64 `gorm:"column:lucky_bag_id"`
	RewardTime    int64 `gorm:"column:reward_time"`
	UserID        int64 `gorm:"column:user_id"`
	RedeemEndTime int64 `gorm:"column:redeem_end_time"`
	RedeemTime    int64 `gorm:"column:redeem_time"`
	RedeemUserID  int64 `gorm:"column:redeem_user_id"`
	TransactionID int64 `gorm:"column:transaction_id"`
}

// TableName table name
func (UserPrize) TableName() string {
	return "live_lucky_bag_user_prize"
}

// BeforeCreate gorm hook BeforeCreate
func (u *UserPrize) BeforeCreate() error {
	u.CreateTime = goutil.TimeNow().Unix()
	u.ModifiedTime = u.CreateTime
	return nil
}

// Create 创建中奖记录
func (u *UserPrize) Create() error {
	return DB().Create(u).Error
}

// ListUserPrize 查询用户福袋中奖记录列表
func ListUserPrize(userID, pageSize int64, marker *MarkerListOption) ([]*UserPrize, *MarkerListOption, error) {
	var userPrizes []*UserPrize
	db := DB().Where("user_id = ?", userID)
	db = marker.ApplyTo(db, pageSize)
	err := db.Find(&userPrizes).Error
	if err != nil {
		return nil, nil, err
	}
	var nextMarker *MarkerListOption
	if int64(len(userPrizes)) > pageSize {
		nextCreateTime := userPrizes[len(userPrizes)-1].CreateTime
		userPrizes = userPrizes[:pageSize]
		lastPrize := userPrizes[len(userPrizes)-1]
		nextMarker = &MarkerListOption{
			CreateTime: lastPrize.CreateTime,
			ID:         lastPrize.ID,
		}
		// 后续的数据和上一页最后一条数据相同时间的数据，需要进行 OR 操作
		if nextCreateTime == lastPrize.CreateTime {
			nextMarker.HasSameTimeLast = true
		}
	}
	return userPrizes, nextMarker, nil
}

// FindUserPrizeByLuckyBagID 根据福袋 ID 查询用户中奖记录
func FindUserPrizeByLuckyBagID(userID, luckyBagID int64) (*UserPrize, error) {
	userPrize := new(UserPrize)
	err := DB().Where("lucky_bag_id = ? AND user_id = ?", luckyBagID, userID).Take(userPrize).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return userPrize, nil
}

// ListLuckyUserIDs 查询中奖用户 ID 列表（用户 ID 正序）
func ListLuckyUserIDs(luckyBagID int64) ([]int64, error) {
	var luckyUserIDs []int64
	err := DB().Table(UserPrize{}.TableName()).
		Where("lucky_bag_id = ?", luckyBagID).Order("user_id ASC").
		Pluck("user_id", &luckyUserIDs).Error
	if err != nil {
		return nil, err
	}
	return luckyUserIDs, nil
}

// LuckyUser 中奖用户信息
type LuckyUser struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
}

// NewLuckyUsers 通过中奖用户 ID 生成 LuckyUsers
func NewLuckyUsers(userIDs []int64) ([]LuckyUser, error) {
	m, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, err
	}
	luckyUsers := make([]LuckyUser, 0, len(userIDs))
	for i := range userIDs {
		u := m[userIDs[i]]
		if u == nil {
			logger.WithFields(logger.Fields{
				"user_id": userIDs[i]}).Error("开奖用户不存在")
			continue
		}
		luckyUsers = append(luckyUsers, LuckyUser{
			UserID:   u.ID,
			Username: u.Username,
		})
	}
	return luckyUsers, nil
}

// FindUserPrizeByID 根据 ID 查询用户中奖记录
func FindUserPrizeByID(prizeID int64) (*UserPrize, error) {
	userPrize := new(UserPrize)
	err := DB().Where("id = ?", prizeID).Take(userPrize).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return userPrize, nil
}
