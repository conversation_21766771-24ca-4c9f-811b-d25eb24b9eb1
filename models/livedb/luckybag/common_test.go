package luckybag

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLuckyBagTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Info{}, "lucky_bag_id", "status", "type", "reward_type", "prize_name", "prize_num", "prize_price", "prize_icon_url",
		"remain_duration", "lucky_users", "image_url")
	kc.Check(FullInfo{}, "keyword", "join_num", "increase_num", "target_type", "join_status")
}

func TestEncode(t *testing.T) {
	assert := assert.New(t)

	marker := &MarkerListOption{
		CreateTime:      1717482605,
		ID:              99,
		HasSameTimeLast: false,
	}
	markerStr := marker.Encode()
	assert.Equal("1717482605,99,0", markerStr)

	marker.HasSameTimeLast = true
	markerStr = marker.Encode()
	assert.Equal("1717482605,99,1", markerStr)
}

func TestParseMarker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := ParseMarker("1717482605,99")
	assert.EqualError(err, "参数格式错误")

	_, err = ParseMarker("-1,1717482605,99")
	assert.EqualError(err, "参数值不在有效范围内")

	marker, err := ParseMarker("1717482605,99,0")
	require.NoError(err)
	assert.Equal(&MarkerListOption{CreateTime: 1717482605, ID: 99, HasSameTimeLast: false}, marker)
}

func TestNewInfo(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time { return time.Unix(now.Unix(), 0) })
	defer cancel()

	r := &InitiateRecord{
		ID:               1,
		Status:           StatusPending,
		Type:             TypeDrama,
		RewardType:       RewardTypeDrama,
		Name:             "name",
		Num:              10,
		StartTime:        now.Unix(),
		ScheduledEndTime: now.Unix() + 10,
	}
	assert.NotPanics(func() {
		NewInfo(r)
	})

	expected := Info{
		LuckyBagID:     1,
		Status:         StatusPending,
		Type:           TypeDrama,
		RewardType:     RewardTypeDrama,
		PrizeName:      "name",
		PrizeNum:       10,
		PrizePrice:     1,
		PrizeIconURL:   "http://test.png",
		RemainDuration: 10000,
	}
	r.MoreInfo = &MoreInfo{
		PrizePrice:   1,
		PrizeIconURL: "http://test.png",
	}
	i := NewInfo(r)
	assert.Equal(expected, i)

	expected = Info{
		LuckyBagID:     1,
		Status:         StatusPending,
		Type:           TypeDrama,
		RewardType:     RewardTypeDrama,
		PrizeName:      "name",
		PrizeNum:       10,
		PrizePrice:     10,
		PrizeIconURL:   "http://test.png",
		RemainDuration: 10000,
	}
	r.MoreInfo = &MoreInfo{
		PrizePrice:   8,
		PrizeIconURL: "http://test.png",
		PrizeVipDiscount: &PrizeVipDiscountInfo{
			DiscountPrice: 80,
			OriginalPrice: 100,
			Rate:          0.8,
			Num:           10,
		},
	}
	i = NewInfo(r)
	assert.Equal(expected, i)
}

func TestNewFullInfo(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time { return time.Unix(now.Unix(), 0) })
	defer cancel()

	r := &InitiateRecord{
		ID:               1,
		Status:           StatusPending,
		Type:             TypeDrama,
		RewardType:       RewardTypeDrama,
		Name:             "name",
		Num:              10,
		StartTime:        now.Unix(),
		ScheduledEndTime: now.Unix() + 10,
		Message:          "test message",
		TargetType:       TargetTypeAll,
		MoreInfo: &MoreInfo{
			PrizePrice:   1,
			PrizeIconURL: "http://test.png",
		},
	}

	expected := &FullInfo{
		Info: Info{
			LuckyBagID:     1,
			Status:         StatusPending,
			Type:           TypeDrama,
			RewardType:     RewardTypeDrama,
			PrizeName:      "name",
			PrizeNum:       10,
			PrizePrice:     1,
			PrizeIconURL:   "http://test.png",
			RemainDuration: 10000,
		},
		Keyword:    "test message",
		TargetType: TargetTypeAll,
		JoinNum:    0,
	}

	i := NewFullInfo(r)
	assert.Equal(expected, i)
}

func TestLoadJoinNumFromCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testLuckyBagID = int64(11111)
		key            = keys.KeyLuckyBagJoinNum1.Format(testLuckyBagID)
	)

	require.NoError(service.Redis.Del(key).Err())
	joinNum, err := loadJoinNumFromCache(testLuckyBagID)
	require.NoError(err)
	assert.Zero(joinNum)

	joinNum, err = service.Redis.Incr(key).Result()
	require.NoError(err)
	assert.EqualValues(1, joinNum)

	joinNum, err = loadJoinNumFromCache(testLuckyBagID)
	require.NoError(err)
	assert.EqualValues(1, joinNum)
}
