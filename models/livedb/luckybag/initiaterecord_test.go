package luckybag

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestInitiateRecordTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	kc.Check(InitiateRecord{}, "id", "create_time", "modified_time", "delete_time",
		"user_id", "room_id", "creator_id", "type", "reward_type", "status", "prize_drama_id", "prize_ipr_id", "name", "num",
		"target_type", "message", "start_time", "end_time", "scheduled_end_time", "reward_time", "join_num", "transaction_id", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MoreInfo{}, "prize_price", "prize_icon", "prize_drama_cover_color", "start_target_num",
		"end_target_num", "creator_username", "prize_ipr_name", "prize_vip_discount")

	kc.Check(PrizeVipDiscountInfo{}, "discount_price", "original_price", "rate", "num")
}

func TestFindInitiateRecordsByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	records, err := FindInitiateRecordsByIDs([]int64{})
	require.NoError(err)
	assert.Empty(records)

	record := InitiateRecord{
		ID:         30,
		UserID:     12345,
		CreatorID:  12345,
		RoomID:     10,
		Type:       TypeEntity,
		RewardType: RewardTypeEntityDrama,
		Name:       "剧集实物福袋",
	}
	require.NoError(record.Create())

	records, err = FindInitiateRecordsByIDs([]int64{record.ID})
	require.NoError(err)
	assert.Len(records, 1)
	assert.Equal(int64(30), records[0].ID)
	assert.Equal(int64(12345), records[0].UserID)
	assert.Equal("剧集实物福袋", records[0].Name)
	require.NoError(DB().Table(InitiateRecord{}.TableName()).Where("id = ?", record.ID).Delete("").Error)
}

func TestCalculateIncreaseNum(t *testing.T) {
	assert := assert.New(t)

	more := &MoreInfo{
		StartTargetNum: util.NewInt64(10),
	}
	increaseNum := CalculateIncreaseNum(more)
	assert.Zero(increaseNum)

	more = &MoreInfo{
		StartTargetNum: util.NewInt64(10),
		EndTargetNum:   util.NewInt64(20),
	}
	increaseNum = CalculateIncreaseNum(more)
	assert.Equal(int64(10), increaseNum)

	more = &MoreInfo{
		StartTargetNum: util.NewInt64(30),
		EndTargetNum:   util.NewInt64(20),
	}
	increaseNum = CalculateIncreaseNum(more)
	assert.Equal(int64(0), increaseNum)

	more = nil
	increaseNum = CalculateIncreaseNum(more)
	assert.Equal(int64(0), increaseNum)
}

func TestListInitiate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12345)

	// 测试主播福袋发起记录列表为空
	initiates, nextMarker, err := ListFinishInitiateRecord(userID, 3, nil)
	require.NoError(err)
	assert.Nil(nextMarker)
	assert.Empty(initiates)

	more := &MoreInfo{
		StartTargetNum: util.NewInt64(10),
		EndTargetNum:   util.NewInt64(20),
	}
	moreJSON, err := json.Marshal(more)
	require.NoError(err)

	// mock 时间，保证福袋发起时间
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	// 创建福袋发起记录
	record := InitiateRecord{
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     30,
		Type:       TypeDrama,
		RewardType: RewardTypeDrama,
		Status:     StatusFinish,
		Name:       "剧集福袋",
		More:       moreJSON,
	}
	require.NoError(record.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 10, 0, 0, 0, time.Local)
	})
	record2 := InitiateRecord{
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     20,
		Type:       TypeEntity,
		RewardType: RewardTypeEntityPersonal,
		Status:     StatusFinish,
		Name:       "实物福袋",
		More:       moreJSON,
	}
	require.NoError(record2.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 11, 0, 0, 0, time.Local)
	})
	record3 := InitiateRecord{
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     10,
		Type:       TypeEntity,
		RewardType: RewardTypeEntityDrama,
		Status:     StatusFinish,
		Name:       "剧集实物福袋",
		More:       moreJSON,
	}
	require.NoError(record3.Create())

	// 测试请求第一页
	initiates, nextMarker, err = ListFinishInitiateRecord(userID, 2, nil)
	require.NoError(err)
	assert.Equal(&MarkerListOption{CreateTime: record2.CreateTime, ID: record2.ID, HasSameTimeLast: false}, nextMarker)
	assert.Len(initiates, 2)
	assert.Equal(userID, initiates[0].UserID)
	assert.Equal(userID, initiates[1].UserID)
	assert.Equal(int64(10), initiates[0].RoomID)
	assert.Equal(int64(20), initiates[1].RoomID)
	assert.Equal(RewardTypeEntityDrama, initiates[0].RewardType)
	assert.Equal(RewardTypeEntityPersonal, initiates[1].RewardType)
	assert.Equal("剧集实物福袋", initiates[0].Name)
	assert.Equal("实物福袋", initiates[1].Name)

	// 测试请求第二页
	marker := &MarkerListOption{CreateTime: record2.CreateTime, ID: record2.ID, HasSameTimeLast: false}
	initiates, nextMarker, err = ListFinishInitiateRecord(userID, 2, marker)
	require.NoError(err)
	assert.Nil(nextMarker)
	assert.Len(initiates, 1)
	assert.Equal(userID, initiates[0].UserID)
	assert.Equal(int64(30), initiates[0].RoomID)
	assert.Equal(TypeDrama, initiates[0].Type)
	assert.Equal(RewardTypeDrama, initiates[0].RewardType)
	assert.Equal("剧集福袋", initiates[0].Name)

	// 测试无分页
	initiates, nextMarker, err = ListFinishInitiateRecord(userID, 3, nil)
	require.NoError(err)
	assert.Nil(nextMarker)
	assert.Len(initiates, 3)
	assert.Equal(userID, initiates[0].UserID)
	assert.Equal(int64(10), initiates[0].RoomID)
	assert.Equal(TypeEntity, initiates[0].Type)
	assert.Equal("剧集实物福袋", initiates[0].Name)
	assert.Equal("实物福袋", initiates[1].Name)
	assert.Equal("剧集福袋", initiates[2].Name)
}

func TestInitiateRecord_BeforeCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	i := InitiateRecord{}
	require.NoError(i.BeforeCreate())
	assert.NotZero(i.CreateTime)
	assert.NotZero(i.ModifiedTime)
	assert.Equal("null", string(i.More))

	i.MoreInfo = &MoreInfo{PrizePrice: 10}
	require.NoError(i.BeforeCreate())
	assert.Equal(`{"prize_price":10}`, string(i.More))
}

func TestInitiateRecord_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	more, err := json.Marshal(MoreInfo{PrizeIcon: "oss://icon.png"})
	require.NoError(err)
	luckyBag := &InitiateRecord{
		More: more,
	}
	require.NoError(luckyBag.AfterFind())
	assert.Equal("oss://icon.png", luckyBag.MoreInfo.PrizeIcon)
	assert.Equal(storage.ParseSchemeURL("oss://icon.png"), luckyBag.MoreInfo.PrizeIconURL)
}

func TestFindShowingInitiateRecordByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试福袋不存在
	luckyBag, err := FindShowingInitiateRecordByID(10000)
	require.NoError(err)
	assert.Nil(luckyBag)

	// 测试福袋存在
	luckyBag, err = FindShowingInitiateRecordByID(1)
	require.NoError(err)
	assert.EqualValues(1, luckyBag.ID)
	require.NotEmpty(luckyBag.MoreInfo)
	assert.Equal("oss://icon1.png", luckyBag.MoreInfo.PrizeIcon)

	luckyBag.ID = 0
	luckyBag.Status = StatusDeleted
	luckyBag.DeleteTime = goutil.TimeNow().Unix()
	require.NoError(luckyBag.Create())
	luckyBag, err = FindShowingInitiateRecordByID(luckyBag.ID)
	require.NoError(err)
	assert.Nil(luckyBag)
}

func TestInitiateRecord_RemainDuration(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1718078044, 0)
	})
	defer goutil.SetTimeNow(nil)

	record := &InitiateRecord{
		Status:           StatusPending,
		ScheduledEndTime: 1718078220,
	}
	assert.EqualValues(176000, record.RemainDuration())

	record = &InitiateRecord{
		Status:  StatusDrawing,
		EndTime: 1718078220,
	}
	assert.EqualValues(236000, record.RemainDuration())

	record = &InitiateRecord{
		Status:  StatusDrawing,
		EndTime: 0,
	}
	assert.Zero(record.RemainDuration())
}

func TestFindLatestInitiateRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(InitiateRecord{},
		"room_id = ?", 9074501).Error
	require.NoError(err)

	record, err := FindLatestInitiateRecord(123456)
	require.NoError(err)
	assert.Nil(record)

	now := goutil.TimeNow().Unix()
	r := InitiateRecord{
		RoomID:    9074501,
		StartTime: now,
	}
	require.NoError(r.Create())
	record, err = FindLatestInitiateRecord(r.RoomID)
	require.NoError(err)
	assert.Equal(r.ID, record.ID)

	r = InitiateRecord{
		RoomID:    9074501,
		StartTime: now + 1000,
	}
	require.NoError(r.Create())
	record, err = FindLatestInitiateRecord(r.RoomID)
	require.NoError(err)
	assert.Equal(r.ID, record.ID)

	luckyBagID := record.ID
	r = InitiateRecord{
		RoomID:    9074501,
		StartTime: now + 2000,
		Status:    StatusDeleted,
	}
	require.NoError(r.Create())
	record, err = FindLatestInitiateRecord(r.RoomID)
	require.NoError(err)
	assert.Equal(luckyBagID, record.ID)
}

func TestCountOtherRoomDramaRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(InitiateRecord{},
		"prize_ipr_id = ? OR prize_drama_id = ?", 10000, 123).Error
	require.NoError(err)

	r := &InitiateRecord{
		RoomID: 9074501,
	}
	_, err = CountOtherRoomDramaRecords(r)
	assert.EqualError(err, "福袋类型错误")

	r.Type = TypeDrama
	_, err = CountOtherRoomDramaRecords(r)
	assert.EqualError(err, "IPRID 和 DramaID 不能同时为 0")

	r.PrizeIPRID = 10000
	count, err := CountOtherRoomDramaRecords(r)
	require.NoError(err)
	assert.Zero(count)

	nowRecord := InitiateRecord{
		RoomID:     9074502,
		PrizeIPRID: 10000,
		Type:       TypeDrama,
	}
	require.NoError(nowRecord.Create())
	count, err = CountOtherRoomDramaRecords(r)
	require.NoError(err)
	assert.Equal(1, count)

	r.RoomID = nowRecord.RoomID
	count, err = CountOtherRoomDramaRecords(r)
	require.NoError(err)
	assert.Zero(count)

	r = &InitiateRecord{
		RoomID:       9074501,
		PrizeDramaID: 123,
		Type:         TypeDrama,
	}
	count, err = CountOtherRoomDramaRecords(r)
	require.NoError(err)
	assert.Zero(count)

	nowRecord = InitiateRecord{
		RoomID:       9074502,
		PrizeDramaID: 123,
		Type:         TypeDrama,
	}
	require.NoError(nowRecord.Create())
	count, err = CountOtherRoomDramaRecords(r)
	require.NoError(err)
	assert.Equal(1, count)
}

func TestInitiateDailyCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(InitiateRecord{},
		"room_id = ?", 9074511).Error
	require.NoError(err)

	now := goutil.TimeNow()
	record := &InitiateRecord{
		RoomID:     9074511,
		Type:       TypeDrama,
		CreateTime: now.Unix(),
	}
	require.NoError(record.Create())
	// 已删除的福袋不计入发起数量
	record = &InitiateRecord{
		RoomID:     9074511,
		Type:       TypeDrama,
		Status:     StatusDeleted,
		CreateTime: now.Unix(),
	}
	require.NoError(record.Create())

	count, err := InitiateDailyCount(record.RoomID, record.Type)
	require.NoError(err)
	assert.EqualValues(1, count)
}

func TestUpdateTransactionID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(InitiateRecord{},
		"room_id = ?", 9074513).Error
	require.NoError(err)

	record := &InitiateRecord{
		RoomID: 9074513,
		Type:   TypeDrama,
		Status: StatusPending,
	}
	err = servicedb.Tx(DB(), func(tx *gorm.DB) error {
		return record.CreateTx(tx)
	})
	require.NoError(err)
	assert.Zero(record.TransactionID)

	err = UpdateTransactionID(nil, record.ID, 123456)
	require.NoError(err)

	r, err := FindShowingInitiateRecordByID(record.ID)
	require.NoError(err)
	require.NotNil(r)
	assert.EqualValues(123456, r.TransactionID)
}

func TestInitiateRecord_NewRoomMetaMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	i := InitiateRecord{
		ID:               11,
		Type:             TypeDrama,
		Status:           StatusPending,
		ScheduledEndTime: now.Add(-time.Minute).Unix(),
		MoreInfo: &MoreInfo{
			PrizeIconURL: "http://test/prize.jpg",
		},
	}
	message := i.NewRoomMetaMessage(9074509, "http://test/icon.jpg", "http://test/icon-v2.jpg", "http://test/icon-big.jpg")
	assert.Nil(message)

	i.ScheduledEndTime = now.Add(time.Minute).Unix()
	message = i.NewRoomMetaMessage(9074509, "http://test/icon.jpg", "http://test/icon-v2.jpg", "http://test/icon-big.jpg")
	require.NotNil(message)
	assert.Equal(i.MoreInfo.PrizeIconURL, message.PrizeIconURL)

	i.Type = TypeEntity
	message = i.NewRoomMetaMessage(9074509, "http://test/icon.jpg", "http://test/icon-v2.jpg", "http://test/icon-big.jpg")
	require.NotNil(message)
	assert.Empty(message.PrizeIconURL)
}

func TestFindLatestInitiateRecordByRoomdID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := InitiateRecord{
		RoomID:       10,
		Type:         TypeDrama,
		Status:       StatusFinish,
		PrizeDramaID: 1,
	}
	require.NoError(record.Create())

	initiateRecord, err := FindLatestInitiateRecordByRoomdID(TypeDrama, 10)
	require.NoError(err)
	assert.Equal(int64(10), initiateRecord.RoomID)
	assert.Equal(int64(1), initiateRecord.PrizeDramaID)

	require.NoError(DB().Table(InitiateRecord{}.TableName()).Where("id = ?", record.ID).Delete("").Error)
}

func TestListPendingInitiateRecordBy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := InitiateRecord{
		RoomID:     10,
		Type:       TypeDrama,
		Status:     StatusPending,
		PrizeIPRID: 1,
		Num:        9,
	}
	require.NoError(record.Create())

	record2 := InitiateRecord{
		RoomID:     20,
		Type:       TypeDrama,
		Status:     StatusPending,
		PrizeIPRID: 1,
		Num:        10,
	}
	require.NoError(record2.Create())

	initiateRecords, nextMarker, err := ListPendingInitiateRecordBy(&record, 2, nil)
	require.NoError(err)
	require.Nil(nextMarker)
	assert.Equal(int64(20), initiateRecords[0].RoomID)
	assert.Equal(int64(10), initiateRecords[1].RoomID)

	require.NoError(DB().Table(InitiateRecord{}.TableName()).
		Where("id IN (?)", []int64{record.ID, record2.ID}).
		Delete("").Error)
}

func TestCountPendingInitiateRecordBy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := InitiateRecord{
		RoomID:       10,
		Type:         TypeDrama,
		Status:       StatusPending,
		PrizeDramaID: 1,
	}
	require.NoError(record.Create())

	count, err := CountPendingInitiateRecordBy(&record)
	require.NoError(err)
	assert.Equal(int64(1), count)

	require.NoError(DB().Table(InitiateRecord{}.TableName()).Where("id = ?", record.ID).Delete("").Error)
}

func TestFindPendingInitiateMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	limit := 3

	initiateMaps, err := FindPendingInitiateMaps(TypeDrama, []int64{}, "prize_ipr_id", limit)
	require.NoError(err)
	require.Nil(initiateMaps)

	initiateMaps, err = FindPendingInitiateMaps(TypeDrama, []int64{99}, "prize_ipr_id", limit)
	require.NoError(err)
	require.Nil(initiateMaps)

	initiateMaps, err = FindPendingInitiateMaps(TypeDrama, []int64{4567}, "prize_ipr_id", limit)
	require.NoError(err)
	require.NotNil(initiateMaps)
	assert.Equal(3, len(initiateMaps[4567]))

	initiateMaps, err = FindPendingInitiateMaps(TypeDrama, []int64{1234}, "prize_drama_id", limit)
	require.NoError(err)
	require.NotNil(initiateMaps)
	assert.Equal(3, len(initiateMaps[1234]))
}

func TestInitiateSliceToMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := []*InitiateRecord{}
	initiateMaps := initiateSliceToMaps(record, "prize_ipr_id")
	require.Nil(initiateMaps)

	record = []*InitiateRecord{
		{
			RoomID:     100000007,
			CreatorID:  3457114,
			PrizeIPRID: 4567,
		},
		{
			RoomID:     100000006,
			CreatorID:  3457024,
			PrizeIPRID: 4567,
		},
		{
			RoomID:     100000005,
			CreatorID:  3456864,
			PrizeIPRID: 4567,
		},
	}

	assert.PanicsWithValue("unsupported column: id",
		func() { initiateSliceToMaps(record, "id") })

	initiateMaps = initiateSliceToMaps(record, "prize_ipr_id")
	require.NotNil(initiateMaps)
	assert.Equal(&InitiateRecord{
		RoomID:     100000007,
		CreatorID:  3457114,
		PrizeIPRID: 4567,
	}, initiateMaps[4567][0])
	assert.Equal(&InitiateRecord{
		RoomID:     100000006,
		CreatorID:  3457024,
		PrizeIPRID: 4567,
	}, initiateMaps[4567][1])
	assert.Equal(&InitiateRecord{
		RoomID:     100000005,
		CreatorID:  3456864,
		PrizeIPRID: 4567,
	}, initiateMaps[4567][2])
}

func TestUpdateInitiateRecordDrawing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := InitiateRecord{
		Type:     TypeDrama,
		Status:   StatusPending,
		MoreInfo: &MoreInfo{StartTargetNum: util.NewInt64(1)},
	}
	require.NoError(record.Create())
	require.NotZero(record.ID)
	ok, err := UpdateInitiateRecordDrawing(record.ID, util.NewInt64(2))
	require.NoError(err)
	require.True(ok)

	r, err := FindShowingInitiateRecordByID(record.ID)
	require.NoError(err)
	require.NotNil(r.MoreInfo)
	assert.EqualValues(1, *r.MoreInfo.StartTargetNum)
	assert.EqualValues(2, *r.MoreInfo.EndTargetNum)
	joinNum, err := loadJoinNumFromCache(r.ID)
	require.NoError(err)
	assert.Equal(joinNum, r.JoinNum)

	ok, err = UpdateInitiateRecordDrawing(-9999, util.NewInt64(1))
	require.NoError(err)
	assert.False(ok)
}

func TestUpdateInitiateRecordFinish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := InitiateRecord{
		Type:   TypeDrama,
		Status: StatusDrawing,
	}
	require.NoError(record.Create())
	require.NotZero(record.ID)
	join := &JoinRecord{
		LuckyBagID: record.ID,
		UserID:     1,
	}
	require.NoError(join.Create())

	ok, joinNum, err := UpdateInitiateRecordFinish(nil, record.ID)
	require.NoError(err)
	assert.True(ok)
	assert.EqualValues(1, joinNum)

	ok, joinNum, err = UpdateInitiateRecordFinish(nil, -9999)
	require.NoError(err)
	assert.False(ok)
	assert.Zero(joinNum)
}

func TestRefund(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok := false
	cancel := mrpc.SetMock(userapi.URILiveRefundGoods, func(input any) (any, error) {
		ok = true
		return userapi.BalanceResp{}, nil
	})
	defer cancel()

	err := Refund(mrpc.NewUserContextFromEnv(), &InitiateRecord{
		ID:            1,
		Type:          TypeDrama,
		TransactionID: 1111,
		MoreInfo:      &MoreInfo{PrizePrice: 1000},
	}, 5)
	require.NoError(err)
	assert.True(ok)
}

func TestFindTargetNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	num, needUpdate, err := FindTargetNum(&InitiateRecord{TargetType: TargetTypeAll})
	require.NoError(err)
	assert.False(needUpdate)
	assert.Zero(num)

	testUserID := int64(12)
	u, err := user.FindByUserID(testUserID)
	require.NoError(err)
	require.NotNil(u)
	num, needUpdate, err = FindTargetNum(&InitiateRecord{TargetType: TargetTypeAll, CreatorID: 12})
	require.NoError(err)
	assert.False(needUpdate)
	assert.Equal(u.FansNum, num)

	testRoomID := int64(1234)
	num, needUpdate, err = FindTargetNum(&InitiateRecord{TargetType: TargetTypeMedal, RoomID: testRoomID})
	require.NoError(err)
	assert.True(needUpdate)
	assert.NotZero(num)

	num, needUpdate, err = FindTargetNum(&InitiateRecord{TargetType: TargetTypeSuperFan, CreatorID: testRoomID})
	require.NoError(err)
	assert.True(needUpdate)
	assert.Zero(num)
}

func TestDeletePendingLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	luckyBagID := int64(14)

	ok, err := DeletePendingLuckyBag(nil, luckyBagID, "福袋口令内容含有违规内容")
	require.NoError(err)
	assert.True(ok)
	var count int
	require.NoError(DB().Model(&InitiateRecord{}).
		Where("id = ? AND status = ?", luckyBagID, StatusDeleted).Count(&count).Error)
	assert.Equal(1, count)

	ok, err = DeletePendingLuckyBag(nil, -9999, "福袋口令内容含有违规内容")
	require.NoError(err)
	assert.False(ok)
}

func TestFindDelayPendingInitiateRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(20240702)
		when       = goutil.TimeNow().AddDate(-1, 0, 0)
	)
	goutil.SetTimeNow(func() time.Time {
		return when
	})
	defer goutil.SetTimeNow(nil)
	require.NoError(DB().Delete(InitiateRecord{}, "status = ? AND scheduled_end_time < ?",
		StatusPending, when.Add(-time.Minute).Unix()).Error)

	assert.PanicsWithValue("delayDuration must be greater than 0", func() {
		_, _ = FindDelayPendingInitiateRecord(-time.Minute)
	})

	res, err := FindDelayPendingInitiateRecord(time.Minute)
	require.NoError(err)
	assert.Empty(res)

	records := []InitiateRecord{
		{
			RoomID:           testRoomID,
			StartTime:        when.Unix(),
			PrizeIPRID:       1,
			Type:             TypeDrama,
			Status:           StatusPending,
			ScheduledEndTime: when.Unix(),
		},
		{
			RoomID:           testRoomID,
			StartTime:        when.Unix(),
			PrizeIPRID:       2,
			Type:             TypeDrama,
			Status:           StatusPending,
			ScheduledEndTime: when.Add(-30 * time.Second).Unix(),
		},
		{
			RoomID:           testRoomID,
			StartTime:        when.Unix(),
			PrizeIPRID:       3,
			Type:             TypeDrama,
			Status:           StatusPending,
			ScheduledEndTime: when.Add(-time.Minute).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(DB(), InitiateRecord{}.TableName(), records))

	res, err = FindDelayPendingInitiateRecord(time.Minute)
	require.NoError(err)
	assert.Empty(res)

	res, err = FindDelayPendingInitiateRecord(30 * time.Second)
	require.NoError(err)
	assert.Len(res, 1)
}

func TestFindPendingInitiateRecordByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testRoomID = 233

	// 测试没有进行中的福袋
	require.NoError(DB().Table(InitiateRecord{}.TableName()).Delete("", "room_id = ?", testRoomID).Error)
	resp, err := FindPendingInitiateRecordByRoomID(testRoomID)
	require.NoError(err)
	assert.Nil(resp)

	// 测试获取进行中的福袋
	initiateRecord := &InitiateRecord{
		CreatorID: 1234,
		RoomID:    testRoomID,
		Status:    StatusPending,
	}
	require.NoError(DB().Create(initiateRecord).Error)
	resp, err = FindPendingInitiateRecordByRoomID(testRoomID)
	require.NoError(err)
	assert.EqualValues(1234, resp.CreatorID)
}

func TestListPendingInitiateRecordByRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testRoomID = 233

	// 测试没有进行中的福袋
	require.NoError(DB().Table(InitiateRecord{}.TableName()).Delete("", "room_id = ?", testRoomID).Error)
	roomIDs := []int64{testRoomID}
	initiateRecords, err := ListPendingInitiateRecordByRoomIDs(roomIDs)
	require.NoError(err)
	assert.Len(initiateRecords, 0)

	// 测试获取进行中的福袋
	initiateRecord := &InitiateRecord{
		CreatorID: 1234,
		RoomID:    testRoomID,
		Status:    StatusPending,
	}
	require.NoError(DB().Create(initiateRecord).Error)
	initiateRecords, err = ListPendingInitiateRecordByRoomIDs(roomIDs)
	require.NoError(err)
	assert.Len(initiateRecords, 1)
	assert.EqualValues(1234, initiateRecords[0].CreatorID)
}

func TestFindPendingInitiateDramaRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := FindPendingInitiateDramaRecords()
	require.NoError(err)
	assert.NotEmpty(res)
}
