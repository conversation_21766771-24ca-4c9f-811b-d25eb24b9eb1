package liveroomtagrecord

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// role
const (
	// RoleRoomCreator 主播
	RoleRoomCreator = iota + 1
	// RoleAdmin 超管
	RoleAdmin
)

// status
const (
	// OperationAdd 加入
	OperationAdd = iota + 1
	// OperationRemove 移除
	OperationRemove
)

// LiveRoomTagRecord 添加直播间进入标签下的记录表
type LiveRoomTagRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	TagID      int64 `gorm:"column:tag_id"`
	RoomID     int64 `gorm:"column:room_id"`
	OperatorID int64 `gorm:"column:operator_id"` // 修改直播间标签的用户 ID
	Role       int   `gorm:"column:role"`        // 操作加入的角色，1：主播 2：超管
	Operation  int   `gorm:"column:operation"`   // 操作，1：加入 2：移除
}

// TableName .
func (LiveRoomTagRecord) TableName() string {
	return "live_room_tag_record"
}

// BeforeSave gorm 钩子
func (tr *LiveRoomTagRecord) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	tr.ModifiedTime = nowUnix
	if DB().NewRecord(tr) {
		tr.CreateTime = nowUnix
	}
	return nil
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// AddRecords 直播间加入某个标签
func AddRecords(db *gorm.DB, tagID int64, roomIDs []int64, operatorID int64, role, operation int) error {
	if db == nil {
		db = DB()
	}
	nowUnix := goutil.TimeNow().Unix()
	records := make([]*LiveRoomTagRecord, 0, len(roomIDs))
	for _, id := range roomIDs {
		records = append(records, &LiveRoomTagRecord{
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			TagID:        tagID,
			RoomID:       id,
			OperatorID:   operatorID,
			Role:         role,
			Operation:    operation,
		})
	}
	return servicedb.BatchInsert(db, LiveRoomTagRecord{}.TableName(), records)
}

// FindLastRecord 获取最后一条记录
func FindLastRecord(tagID, roomID int64) (*LiveRoomTagRecord, error) {
	var record LiveRoomTagRecord
	err := DB().Where("tag_id = ? AND room_id = ?", tagID, roomID).
		Order("create_time DESC").
		Take(&record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}
