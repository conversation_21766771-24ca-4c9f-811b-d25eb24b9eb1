package liveroomtagrecord

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&LiveRoomTagRecord{}, "id", "create_time", "modified_time",
		"tag_id", "room_id", "operator_id", "role", "operation")
}

func TestAddRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testTagID  = int64(1)
		testRoomID = int64(1111111111)
	)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)

	err := AddRecords(nil, testTagID, []int64{testRoomID}, 1, RoleAdmin, OperationAdd)
	require.NoError(err)

	record, err := FindLastRecord(testTagID, testRoomID)
	require.NoError(err)
	assert.EqualValues(testRoomID, record.RoomID)
}

func TestFindLastRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testTagID  = int64(1)
		testRoomID = int64(1111111111)
	)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(2, 0)
	})
	defer goutil.SetTimeNow(nil)

	err := AddRecords(nil, testTagID, []int64{testRoomID}, 1, RoleAdmin, OperationRemove)
	require.NoError(err)

	record, err := FindLastRecord(testTagID, testRoomID)
	require.NoError(err)
	require.NotNil(record)
	assert.EqualValues(testRoomID, record.RoomID)
	assert.EqualValues(RoleAdmin, record.Role)
	assert.EqualValues(OperationRemove, record.Operation)
}
