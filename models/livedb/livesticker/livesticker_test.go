package livesticker

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&LiveSticker{}, "id", "create_time", "modified_time", "name", "intro", "icon", "image")
}

func TestFindStickersByIDs(t *testing.T) {
	require := require.New(t)

	stickers, err := FindStickersByIDs([]int64{1, 2})
	require.NoError(err)
	require.Len(stickers, 2)
}

func TestFindSticker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	sticker, err := FindSticker(1)
	require.NoError(err)
	assert.NotNil(sticker)

	sticker, err = FindSticker(1111112321123)
	require.NoError(err)
	assert.Nil(sticker)
}

func TestLiveSticker_NewStickerInfo(t *testing.T) {
	assert := assert.New(t)

	ls := &LiveSticker{
		ID: 1,
	}
	info := ls.NewStickerInfo(nil)
	assert.EqualValues(1, info.StickerID)
	assert.Zero(info.PackageID)
	assert.Empty(info.PackageName)

	ls = &LiveSticker{
		ID: 1,
	}
	info = ls.NewStickerInfo(&Package{ID: 1, Name: "test"})
	assert.EqualValues(1, info.StickerID)
	assert.EqualValues(1, info.PackageID)
	assert.Equal("test", info.PackageName)
}

func TestLiveSticker_Message(t *testing.T) {
	assert := assert.New(t)

	ls := &LiveSticker{
		Name: "test",
	}
	assert.Equal("[test]", ls.Message())
}

func TestLiveSticker_SimulateMessage(t *testing.T) {
	assert := assert.New(t)

	ls := &LiveSticker{
		ID:   1,
		Name: "test",
	}
	assert.Equal("sticker:fake-msg:1:test", ls.SimulateMessage())
}
