package livesticker

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// PackageOwner 表情包归属
type PackageOwner struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	PackageID  int64 `gorm:"column:package_id"`
	RoomID     int64 `gorm:"column:room_id"`
	UserID     int64 `gorm:"column:user_id"`
	StartTime  int64 `gorm:"column:start_time"`  // 单位：秒
	ExpireTime int64 `gorm:"column:expire_time"` // 单位：秒
}

// TableName table name
func (PackageOwner) TableName() string {
	return "live_sticker_package_owner"
}

// BeforeSave gorm 钩子
func (po *PackageOwner) BeforeSave() error {
	now := goutil.TimeNow()
	po.ModifiedTime = now.Unix()
	if DB().NewRecord(po) {
		po.CreateTime = now.Unix()
	}
	return nil
}

// AssignExclusivePackage 分配房间专属或用户专属表情包
// NOTICE: 专属表情包为用户或直播间自己独有无法共享使用，并且专属表情包没有过期时间
func AssignExclusivePackage(pkgType int, roomID, userID int64) (*Package, error) {
	pkg := &Package{
		Type: pkgType,
	}
	var err error
	pkg.Sort, pkg.Name, err = exclusivePackageSortAndName(pkgType)
	if err != nil {
		return nil, err
	}

	err = servicedb.Tx(DB(), func(tx *gorm.DB) error {
		err := tx.Create(pkg).Error
		if err != nil {
			return err
		}
		owner := &PackageOwner{
			PackageID:  pkg.ID,
			StartTime:  goutil.TimeNow().Unix(),
			ExpireTime: 0, // 用户专属和房间专属表情包永不过期
		}
		switch pkgType {
		case TypeRoom:
			owner.RoomID = roomID
		case TypeUser:
			owner.UserID = userID
		}
		err = tx.Create(owner).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return pkg, nil
}

// BatchAssignExclusivePackage 批量配置房间专属或用户专属表情包
func BatchAssignExclusivePackage(pkgType int, ownerIDs []int64) ([]*Package, error) {
	if len(ownerIDs) == 0 {
		return nil, nil
	}
	pkgSort, pkgName, err := exclusivePackageSortAndName(pkgType)
	if err != nil {
		return nil, err
	}

	// 查询当前最大的 package ID，并以此 ID 递增创建数据
	var maxID int64
	err = DB().Table(Package{}.TableName()).Select("MAX(id)").Row().Scan(&maxID)
	if err != nil {
		return nil, err
	}

	pkgs := make([]*Package, 0, len(ownerIDs))
	pkgOwners := make([]*PackageOwner, 0, len(ownerIDs))
	timeNowUnix := goutil.TimeNow().Unix()
	for idx, ownerID := range ownerIDs {
		pkg := &Package{
			ID:           maxID + int64(idx) + 1,
			CreateTime:   timeNowUnix,
			ModifiedTime: timeNowUnix,
			Type:         pkgType,
			Sort:         pkgSort,
			Name:         pkgName,
		}
		pkgs = append(pkgs, pkg)

		owner := &PackageOwner{
			CreateTime:   timeNowUnix,
			ModifiedTime: timeNowUnix,
			PackageID:    pkg.ID,
			StartTime:    timeNowUnix,
			ExpireTime:   0, // 用户专属和房间专属表情包永不过期
		}
		if pkgType == TypeUser {
			owner.UserID = ownerID
		} else {
			owner.RoomID = ownerID
		}
		pkgOwners = append(pkgOwners, owner)
	}

	// 事务中批量插入表情包并分配给直播间和用户
	err = servicedb.Tx(DB(), func(tx *gorm.DB) error {
		err := servicedb.BatchInsert(tx, Package{}.TableName(), pkgs)
		if err != nil {
			return err
		}

		err = servicedb.BatchInsert(tx, PackageOwner{}.TableName(), pkgOwners)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return pkgs, nil
}

// FindPackageOwner 表情包归属查询
func FindPackageOwner(pkgType int, roomID, userID int64, when time.Time) (*PackageOwner, error) {
	var owner PackageOwner
	db := DB().Select("owner.package_id, owner.room_id, owner.user_id, owner.start_time, owner.expire_time").
		Table(fmt.Sprintf("%s AS owner", PackageOwner{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS pkg ON pkg.id = owner.package_id", Package{}.TableName())).
		Where("pkg.type = ?", pkgType)
	db = buildTimeRangeQuery(db, "owner.start_time", "owner.expire_time", when)
	switch pkgType {
	case TypeGeneral:
		// PASS
	case TypeRoom:
		db = db.Where("owner.room_id = ?", roomID)
	case TypeUser:
		db = db.Where("owner.user_id = ?", userID)
	case TypeSuperFans:
		db = db.Where("owner.room_id = ?", roomID)
	default:
		return nil, ErrInvalidPackageType
	}
	err := db.Take(&owner).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &owner, nil
}

// FindPackageOwners 表情包归属查询
func FindPackageOwners(pkgType int, ownerIDs []int64, when time.Time) ([]*PackageOwner, error) {
	var owners []*PackageOwner
	db := DB().Select("owner.package_id, owner.room_id, owner.user_id, owner.start_time, owner.expire_time").
		Table(fmt.Sprintf("%s AS owner", PackageOwner{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS pkg ON pkg.id = owner.package_id", Package{}.TableName())).
		Where("pkg.type = ?", pkgType)
	db = buildTimeRangeQuery(db, "owner.start_time", "owner.expire_time", when)
	switch pkgType {
	case TypeGeneral:
	case TypeRoom:
		db = db.Where("owner.room_id IN (?)", ownerIDs)
	case TypeUser:
		db = db.Where("owner.user_id IN (?)", ownerIDs)
	case TypeSuperFans:
		db = db.Where("owner.room_id IN (?)", ownerIDs)
	default:
		return nil, ErrInvalidPackageType
	}
	err := db.Find(&owners).Error
	if err != nil {
		return nil, err
	}
	return owners, nil
}
