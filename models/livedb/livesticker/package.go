package livesticker

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	// ErrInvalidPackageType 无效的表情包类型
	ErrInvalidPackageType = errors.New("invalid package type")
)

// 表情包类型
const (
	TypeGeneral   = iota + 1 // 常规表情类型
	TypeUser                 // 用户专属表情类型
	TypeRoom                 // 房间专属表情类型
	TypeSuperFans            // 超粉表情
)

// 表情包默认顺序
const (
	// SortDefaultRoom 房间专属表情包默认顺序
	SortDefaultRoom = 2
	// SortDefaultUser 用户专属表情包默认顺序
	SortDefaultUser = 3
)

// 专属表情包名称
const (
	// PackageNameRoom 房间专属表情包名称
	PackageNameRoom = "直播间专属表情"
	// PackageNameUser 用户专属表情包名称
	PackageNameUser = "用户专属表情"
)

// Package 表情包
type Package struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	Type       int    `gorm:"column:type"`
	Sort       int    `gorm:"column:sort"`
	Name       string `gorm:"column:name"`
	Icon       string `gorm:"column:icon"`        // 当前仅超粉表情包有值
	ExpireTime int64  `gorm:"column:expire_time"` // 该字段仅用于分配表情包时对过期时间进行赋值，单位：秒
}

// TableName table name
func (Package) TableName() string {
	return "live_sticker_package"
}

// BeforeSave gorm 钩子
func (p *Package) BeforeSave() error {
	now := goutil.TimeNow()
	p.ModifiedTime = now.Unix()
	if DB().NewRecord(p) {
		p.CreateTime = now.Unix()
	}
	return nil
}

// AfterFind gorm 钩子
func (p *Package) AfterFind() error {
	if p.Icon != "" {
		p.Icon = storage.ParseSchemeURL(p.Icon)
	}
	return nil
}

// buildTimeRangeQuery .
func buildTimeRangeQuery(db *gorm.DB, startTimeColumn, expireTimeColumn string, when time.Time) *gorm.DB {
	sql := fmt.Sprintf("%s <= ? AND (%s > ? OR %s = 0)", startTimeColumn, expireTimeColumn, expireTimeColumn)
	return db.Where(sql, when.Unix(), when.Unix())
}

// FindPackage 查询表情包
func FindPackage(packageID int64) (*Package, error) {
	var p Package
	err := DB().Take(&p, "id = ?", packageID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &p, nil
}

// FindPackagesByIDs 根据表情包 ID 批量查找表情包
func FindPackagesByIDs(ids []int64) ([]*Package, error) {
	var packages []*Package
	err := DB().Where("id IN (?)", ids).Find(&packages).Error
	if err != nil {
		return nil, err
	}
	return packages, nil
}

// FindPackages 查找用户在当前直播间所有的有效表情包
func FindPackages(roomID, userID int64, when time.Time) ([]*Package, error) {
	var packages []*Package
	db := DB().Select("p.id, p.type, p.name, p.icon, po.room_id, po.user_id").
		Table(fmt.Sprintf("%s AS po", PackageOwner{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS p ON p.id = po.package_id", Package{}.TableName())).
		Where("(po.room_id = ? AND po.user_id = 0) OR (po.user_id = ? AND po.room_id = 0)", roomID, userID)
	err := buildTimeRangeQuery(db, "po.start_time", "po.expire_time", when).
		Order("p.sort ASC").
		Find(&packages).Error
	if err != nil {
		return nil, err
	}
	return packages, nil
}

func buildVersionItem(stickerID, modifiedTime int64) string {
	return strconv.FormatInt(stickerID, 10) + "-" + strconv.FormatInt(modifiedTime, 10)
}

// buildVersion 构建表情包版本号
// 版本号规则：md5(${sticker_id1}-${modified_time1}, ${sticker_id2}-${modified_time2}, ...)
func buildVersion(items []string) string {
	return goutil.MD5(strings.Join(items, ","))
}

// PackageVersion 获取表情包版本号
func PackageVersion(packageIDs []int64, when time.Time) (map[int64]string, error) {
	var maps []*PackageStickerMap
	db := DB().Select("sort, package_id, sticker_id, modified_time")
	err := buildTimeRangeQuery(db, "start_time", "expire_time", when).
		Where("package_id IN (?)", packageIDs).
		Order("package_id ASC, sort ASC, id ASC").
		Find(&maps).Error
	if err != nil {
		return nil, err
	}

	versions := make(map[int64]string, len(packageIDs))
	for i := 0; i < len(maps); {
		packageID := maps[i].PackageID
		versionItem := make([]string, 0)

		// NOTICE: 专属表情包 sort 都为 0，将按照 id ASC 排序
		for ; i < len(maps) && maps[i].PackageID == packageID; i++ {
			versionItem = append(versionItem, buildVersionItem(maps[i].StickerID, maps[i].ModifiedTime))
		}
		versions[packageID] = buildVersion(versionItem)
	}
	return versions, nil
}

// IsOwner 是否拥有该表情包
func (p *Package) IsOwner(roomID, userID int64, when time.Time) (bool, error) {
	db := buildTimeRangeQuery(DB(), "start_time", "expire_time", when).Where("package_id = ?", p.ID)
	switch p.Type {
	case TypeGeneral:
		// PASS
	case TypeUser:
		db = db.Where("user_id = ?", userID)
	case TypeRoom, TypeSuperFans:
		db = db.Where("room_id = ?", roomID)
	default:
		return false, ErrInvalidPackageType
	}
	var po PackageOwner
	err := db.Take(&po).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// Owners 查询该表情包的所有拥有者
func (p *Package) Owners(when time.Time) ([]*PackageOwner, error) {
	var owners []*PackageOwner
	err := buildTimeRangeQuery(DB(), "start_time", "expire_time", when).
		Where("package_id = ?", p.ID).
		Find(&owners).Error
	if err != nil {
		return nil, err
	}
	return owners, nil
}

// Stickers 查询表情包下有效的表情，查询正常返回表情列表和当前表情包版本号
func (p *Package) Stickers(when time.Time) ([]*LiveSticker, string, error) {
	db := DB().
		// NOTICE: 这里用 LiveSticker 的 ModifiedTime 接收 PackageStickerMap 的 ModifiedTime
		Select("s.id, s.name, s.intro, s.icon, s.image, m.modified_time").
		Table(fmt.Sprintf("%s AS m", PackageStickerMap{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS s ON s.id = m.sticker_id", LiveSticker{}.TableName())).
		Where("m.package_id = ?", p.ID)
	db = buildTimeRangeQuery(db, "m.start_time", "m.expire_time", when)
	switch p.Type {
	case TypeGeneral:
		db = db.Order("m.sort ASC")
	case TypeUser, TypeRoom:
		db = db.Order("m.id DESC") // 专属表情按照添加的先后顺序排序
	case TypeSuperFans:
		db = db.Order("m.sort ASC")
	default:
		return nil, "", ErrInvalidPackageType
	}
	var stickers []*LiveSticker
	err := db.Find(&stickers).Error
	if err != nil {
		return nil, "", err
	}
	versionItem := make([]string, 0, len(stickers))
	for _, sticker := range stickers {
		versionItem = append(versionItem, buildVersionItem(sticker.ID, sticker.ModifiedTime))
	}
	return stickers, buildVersion(versionItem), nil
}

// Sticker 获取表情包下的指定表情
func (p *Package) Sticker(stickerID, roomID, userID int64, when time.Time) (*LiveSticker, error) {
	db := DB().Select("s.id, s.name, s.intro, s.icon, s.image").
		Table(fmt.Sprintf("%s AS po", PackageOwner{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS m ON m.package_id = po.package_id", PackageStickerMap{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS s ON s.id = m.sticker_id", LiveSticker{}.TableName())).
		Where("m.package_id = ? AND m.sticker_id = ?", p.ID, stickerID)
	db = buildTimeRangeQuery(db, "m.start_time", "m.expire_time", when)
	switch p.Type {
	case TypeGeneral:
		// PASS
	case TypeUser:
		db = db.Where("po.user_id = ?", userID)
	case TypeRoom, TypeSuperFans:
		db = db.Where("po.room_id = ?", roomID)
	default:
		return nil, ErrInvalidPackageType
	}
	var sticker LiveSticker
	err := db.Take(&sticker).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &sticker, nil
}

// exclusivePackageSortAndName 返回专属表情包的 sort 和 name 字段的值
func exclusivePackageSortAndName(pkgType int) (int, string, error) {
	var pkgName string
	var pkgSort int
	switch pkgType {
	case TypeUser:
		pkgSort = SortDefaultUser
		pkgName = PackageNameUser
	case TypeRoom:
		pkgSort = SortDefaultRoom
		pkgName = PackageNameRoom
	default:
		return 0, "", ErrInvalidPackageType
	}
	return pkgSort, pkgName, nil
}
