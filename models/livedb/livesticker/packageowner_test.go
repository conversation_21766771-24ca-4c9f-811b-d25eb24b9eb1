package livesticker

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestOwnerTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&PackageOwner{}, "id", "create_time", "modified_time", "package_id", "room_id", "user_id", "start_time", "expire_time")
}

func TestAssignExclusivePackage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pkg, err := AssignExclusivePackage(TypeRoom, 1, 1)
	require.NoError(err)
	require.NotNil(pkg)
	assert.NotZero(pkg.ID)
	assert.NotZero(pkg.CreateTime)
	assert.Equal(SortDefaultRoom, pkg.Sort)
	assert.Equal(PackageNameRoom, pkg.Name)
	var owner PackageOwner
	err = DB().Where("package_id = ?", pkg.ID).Take(&owner).Error
	require.NoError(err)
	require.NotNil(owner)
	assert.NotZero(owner.RoomID)
	assert.Zero(owner.UserID)
	assert.NotZero(owner.CreateTime)

	pkg, err = AssignExclusivePackage(TypeUser, 1, 1)
	require.NoError(err)
	require.NotNil(pkg)
	assert.NotZero(pkg.ID)
	assert.NotZero(pkg.CreateTime)
	assert.Equal(SortDefaultUser, pkg.Sort)
	assert.Equal(PackageNameUser, pkg.Name)
	owner.ID = 0
	err = DB().Where("package_id = ?", pkg.ID).Take(&owner).Error
	require.NoError(err)
	require.NotNil(owner)
	assert.Zero(owner.RoomID)
	assert.NotZero(owner.UserID)
	assert.NotZero(owner.CreateTime)

	pkg, err = AssignExclusivePackage(TypeSuperFans, 1, 1)
	assert.Equal(ErrInvalidPackageType, err)
	assert.Nil(pkg)
}

func TestBatchAssignExclusivePackage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeUnix := int64(1999999999)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(timeUnix, 0)
	})
	defer cancel()
	require.NoError(DB().Delete(Package{}, "create_time = ?", timeUnix).Error)
	require.NoError(DB().Delete(PackageOwner{}, "create_time = ?", timeUnix).Error)

	// 为用户和直播间分别创建并分配表情包
	userIDs := []int64{101, 102, 103}
	roomIDs := []int64{201, 202, 203}
	_, err := BatchAssignExclusivePackage(TypeUser, userIDs)
	require.NoError(err)
	_, err = BatchAssignExclusivePackage(TypeRoom, roomIDs)
	require.NoError(err)

	var userPkgs []*Package
	err = DB().Where("create_time = ? AND type = ?", timeUnix, TypeUser).Find(&userPkgs).Error
	require.NoError(err)
	assert.Equal(len(userIDs), len(userPkgs))
	for _, pkg := range userPkgs {
		assert.Equal(SortDefaultUser, pkg.Sort)
		assert.Equal(PackageNameUser, pkg.Name)
	}
	var userPkgOwners []*PackageOwner
	err = DB().Where("user_id IN (?)", userIDs).Find(&userPkgOwners).Error
	require.NoError(err)
	assert.Equal(len(userIDs), len(userPkgOwners))

	var roomPkgs []*Package
	err = DB().Where("create_time = ? AND type = ?", timeUnix, TypeRoom).Find(&roomPkgs).Error
	require.NoError(err)
	assert.Equal(len(roomPkgs), len(roomPkgs))
	for _, pkg := range roomPkgs {
		assert.Equal(SortDefaultRoom, pkg.Sort)
		assert.Equal(PackageNameRoom, pkg.Name)
	}
	var roomPkgOwners []*PackageOwner
	err = DB().Where("room_id IN (?)", roomIDs).Find(&roomPkgOwners).Error
	require.NoError(err)
	assert.Equal(len(roomIDs), len(roomPkgOwners))
}

func TestFindPackageOwner(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1999999999, 0)
	})
	defer goutil.SetTimeNow(nil)

	owner, err := FindPackageOwner(TypeRoom, 4381915, 0, goutil.TimeNow())
	require.NoError(err)
	assert.NotNil(owner)

	owner, err = FindPackageOwner(TypeUser, 0, 223344, goutil.TimeNow())
	require.NoError(err)
	assert.NotNil(owner)

	owner, err = FindPackageOwner(TypeUser, 0, 111, goutil.TimeNow())
	require.NoError(err)
	assert.Nil(owner)
}

func TestFindPackageOwners(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	owners, err := FindPackageOwners(TypeRoom, []int64{4381915}, goutil.TimeNow())
	require.NoError(err)
	assert.Equal(1, len(owners))

	owners, err = FindPackageOwners(TypeUser, []int64{223344}, goutil.TimeNow())
	require.NoError(err)
	assert.Equal(1, len(owners))

	owners, err = FindPackageOwners(TypeUser, []int64{223344}, goutil.TimeNow())
	require.NoError(err)
	assert.Equal(1, len(owners))

	owners, err = FindPackageOwners(0, []int64{111}, goutil.TimeNow())
	assert.Equal(ErrInvalidPackageType, err)
	assert.Empty(owners)
}
