package livesticker

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestPackageTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&Package{}, "id", "create_time", "modified_time", "type", "sort", "name", "icon", "expire_time")
}

func TestPackageType(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, TypeGeneral)
	assert.Equal(2, TypeUser)
	assert.Equal(3, <PERSON>Room)
	assert.Equal(4, TypeSuperFans)
}

func TestBuildTimeRangeQuery(t *testing.T) {
	assert := assert.New(t)

	testNow := time.Unix(1, 0)

	assert.Equal(DB().Where("start_time <= ? AND (expire_time > ? OR expire_time = 0)", testNow.Unix(), testNow.Unix()).QueryExpr(),
		buildTimeRangeQuery(DB(), "start_time", "expire_time", testNow).QueryExpr())

	assert.Equal(DB().Where("po.start_time <= ? AND (po.expire_time > ? OR po.expire_time = 0)", testNow.Unix(), testNow.Unix()).QueryExpr(),
		buildTimeRangeQuery(DB(), "po.start_time", "po.expire_time", testNow).QueryExpr())
}

func TestFindPackage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p, err := FindPackage(1)
	require.NoError(err)
	assert.NotNil(p)

	p, err = FindPackage(111111111)
	require.NoError(err)
	assert.Nil(p)
}

func TestFindPackagesByIDs(t *testing.T) {
	require := require.New(t)

	packages, err := FindPackagesByIDs([]int64{1, 2})
	require.NoError(err)
	require.Len(packages, 2)
}

func TestFindPackages(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	var (
		testRoomID = int64(223344)
		testUserID = int64(223344)
	)

	packages, err := FindPackages(testRoomID, testUserID, goutil.TimeNow())
	require.NoError(err)
	require.Len(packages, 2)
	assert.EqualValues(1, packages[0].ID)
	assert.Equal(TypeSuperFans, packages[0].Type)
	assert.Equal("超粉表情包", packages[0].Name)
	assert.Equal("https://static-test.missevan.com/sticker_package/superfans/icon.png", packages[0].Icon)
	assert.EqualValues(3, packages[1].ID)
	assert.Equal("用户 223344 专属表情包", packages[1].Name)
}

func TestPackageVersion(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	versions, err := PackageVersion([]int64{1, 2}, goutil.TimeNow())
	require.NoError(err)
	require.NotEmpty(versions)
	assert.Equal("1311fd553dad9a1b7f2aa76614c5f0eb", versions[1]) // 超粉表情包
	assert.Equal("2f96cf94c09b7b92c1cfa6ad8d1cad7d", versions[2]) // 房间专属表情包
}

func TestPackage_IsOwner(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	p := &Package{
		ID:   1,
		Type: TypeSuperFans,
	}

	isOwner, err := p.IsOwner(223344, 223344, goutil.TimeNow())
	require.NoError(err)
	assert.True(isOwner)

	isOwner, err = p.IsOwner(1, 2, goutil.TimeNow())
	require.NoError(err)
	assert.False(isOwner)
}

func TestPackage_Owners(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	p := &Package{
		ID: 1,
	}

	owners, err := p.Owners(goutil.TimeNow())
	require.NoError(err)
	require.NotEmpty(owners)
	assert.EqualValues(223344, owners[0].RoomID)
}

func TestPackage_Stickers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	p := &Package{
		ID:   2,
		Type: TypeSuperFans,
	}
	stickers, version, err := p.Stickers(goutil.TimeNow())
	require.NoError(err)
	require.Len(stickers, 3)
	// order by sort ASC
	assert.EqualValues(3, stickers[0].ID)
	assert.EqualValues(4, stickers[1].ID)
	assert.Equal("https://static-test.missevan.com/sticker_package/room/icon.png", stickers[0].Icon)
	assert.Equal("https://static-test.missevan.com/sticker_package/superfans/icon.webp", stickers[0].Image)
	assert.Equal("2f96cf94c09b7b92c1cfa6ad8d1cad7d", version)

	p = &Package{
		ID:   2,
		Type: TypeRoom,
	}
	stickers, version, err = p.Stickers(goutil.TimeNow())
	require.NoError(err)
	require.Len(stickers, 3)
	// order by id DESC
	assert.EqualValues(1, stickers[0].ID)
	assert.EqualValues(4, stickers[1].ID)
	assert.NotEmpty(version)
}

func TestPackage_Sticker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 4, 24, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	p := Package{
		ID:   1,
		Type: TypeSuperFans,
	}
	s, err := p.Sticker(1, 223344, 0, goutil.TimeNow())
	require.NoError(err)
	require.NotNil(s)
	assert.EqualValues(1, s.ID)
}
