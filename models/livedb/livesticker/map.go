package livesticker

import (
	"time"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// PackageStickerMap 表情与表情包映射表
type PackageStickerMap struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	Sort       int   `gorm:"column:sort"` // 目前仅支持超粉表情使用
	PackageID  int64 `gorm:"column:package_id"`
	StickerID  int64 `gorm:"column:sticker_id"`
	StartTime  int64 `gorm:"column:start_time"`  // 单位：秒
	ExpireTime int64 `gorm:"column:expire_time"` // 单位：秒
}

// TableName table name
func (PackageStickerMap) TableName() string {
	return "live_sticker_map"
}

// BeforeSave gorm 钩子
func (m *PackageStickerMap) BeforeSave() error {
	now := goutil.TimeNow()
	m.ModifiedTime = now.Unix()
	if DB().NewRecord(m) {
		m.CreateTime = now.Unix()
	}
	return nil
}

// FindMap 查询表情映射
func FindMap(id int64) (*PackageStickerMap, error) {
	var m PackageStickerMap
	err := DB().Where("id = ?", id).Take(&m).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &m, nil
}

// StickerMap 表情包和表情映射关系
func (p *Package) StickerMap(stickerID int64, when time.Time) (*PackageStickerMap, error) {
	var m PackageStickerMap
	err := buildTimeRangeQuery(DB(), "start_time", "expire_time", when).
		Where("package_id = ? AND sticker_id = ?", p.ID, stickerID).
		Take(&m).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &m, nil
}
