package livesticker

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestPackageStickerMapTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&PackageStickerMap{}, "id", "create_time", "modified_time", "sort", "package_id", "sticker_id", "start_time", "expire_time")
}

func TestPackage_FindMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m, err := FindMap(1)
	require.NoError(err)
	assert.NotNil(m)

	m, err = FindMap(1111112321123)
	require.NoError(err)
	assert.Nil(m)
}

func TestPackage_StickerMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pkg := Package{ID: 1}
	m, err := pkg.StickerMap(1, goutil.TimeNow())
	require.NoError(err)
	assert.NotNil(m)

	pkg = Package{ID: 1231312123123}
	m, err = pkg.StickerMap(1, goutil.TimeNow())
	require.NoError(err)
	assert.Nil(m)
}
