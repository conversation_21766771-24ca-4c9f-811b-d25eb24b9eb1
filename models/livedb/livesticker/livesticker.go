package livesticker

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// LiveSticker 表情
type LiveSticker struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	Name  string `gorm:"column:name"`
	Intro string `gorm:"column:intro"`
	Icon  string `gorm:"column:icon"`
	Image string `gorm:"column:image"`
}

// TableName table name
func (LiveSticker) TableName() string {
	return "live_sticker"
}

// DB db
func DB() *gorm.DB {
	return service.LiveDB
}

// AfterFind gorm 钩子
func (ls *LiveSticker) AfterFind() error {
	if ls.Icon != "" {
		ls.Icon = storage.ParseSchemeURL(ls.Icon)
	}
	if ls.Image != "" {
		ls.Image = storage.ParseSchemeURL(ls.Image)
	}
	return nil
}

// FindStickersByIDs 根据表情 ID 批量查找表情
func FindStickersByIDs(ids []int64) ([]*LiveSticker, error) {
	var stickers []*LiveSticker
	err := DB().Where("id IN (?)", ids).Find(&stickers).Error
	if err != nil {
		return nil, err
	}
	return stickers, nil
}

// FindSticker 查询表情
func FindSticker(id int64) (*LiveSticker, error) {
	var sticker LiveSticker
	err := DB().Where("id = ?", id).Take(&sticker).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &sticker, nil
}

// StickerInfo 单个表情信息
type StickerInfo struct {
	PackageID   int64  `json:"package_id,omitempty"`
	PackageName string `json:"package_name,omitempty"`
	StickerID   int64  `json:"sticker_id"`
	Name        string `json:"name"`
	IconURL     string `json:"icon_url"`
	ImageURL    string `json:"image_url"`
	Intro       string `json:"intro"`
}

// NewStickerInfo 新建表情信息
// NOTICE: pkg 为 nil 时，不返回表情对应的表情包信息
func (ls *LiveSticker) NewStickerInfo(pkg *Package) *StickerInfo {
	sticker := &StickerInfo{
		StickerID: ls.ID,
		Name:      ls.Name,
		IconURL:   ls.Icon,
		ImageURL:  ls.Image,
		Intro:     ls.Intro,
	}
	if pkg != nil {
		sticker.PackageID = pkg.ID
		sticker.PackageName = pkg.Name
	}
	return sticker
}

// Message 表情消息
func (ls *LiveSticker) Message() string {
	return fmt.Sprintf("[%s]", ls.Name)
}

// SimulateMessage 模拟一个表情包的文字消息，目前仅用于校验重复发送
func (ls *LiveSticker) SimulateMessage() string {
	return fmt.Sprintf("sticker:fake-msg:%d:%s", ls.ID, ls.Name)
}
