package livecustom

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestShortcutGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID int64 = 1
		testGiftID int64 = 123
		now              = goutil.TimeNow()
	)
	require.NoError(LiveCustom{}.DB().
		Delete("", "custom_type = ? AND custom_id = ? AND element_id = ?",
			TypeShortcutGift, testRoomID, testGiftID).Error)
	err := AddShortcutGifts([]int64{testRoomID}, testGiftID, now.Unix(), now.Add(time.Minute).Unix())
	require.NoError(err)
	res, err := FindRoomShowShortcut(testRoomID)
	require.NoError(err)
	require.NotNil(res)
	sc, err := FindShortcutByIDs([]int64{res.ID})
	require.NoError(err)
	require.Len(sc, 1)

	err = RemoveShortcutGifts([]int64{res.ID})
	require.NoError(err)
	res, err = FindRoomShowShortcut(testRoomID)
	require.NoError(err)
	assert.Nil(res)
}
