package livecustom

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// custom type
const (
	TypeShortcutGift = iota + 1
	TypeRoomCustomGift
	TypeUserCustomGift
	TypeNobleHornBubble // 贵族喇叭气泡
)

// 直播间专属礼物来源（针对 TypeRoomCustomGift）
const (
	SourceDefault    = iota // 礼物墙、活动奖励直播间专属礼物
	SourceLiveShow          // 主播个人场直播间专属礼物
	SourceHornBubble        // 喇叭气泡
)

// LiveCustom of table live_custom
type LiveCustom struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	DeleteTime   int64 `gorm:"column:delete_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	CustomType  int    `gorm:"column:custom_type"`
	CustomID    int64  `gorm:"column:custom_id"`
	ElementID   int64  `gorm:"column:element_id"`
	ElementSort int64  `gorm:"column:element_sort"`
	Source      int    `gorm:"column:source"`
	StartTime   int64  `gorm:"column:start_time"`
	EndTime     int64  `gorm:"column:end_time"`
	BatchID     int64  `gorm:"column:batch_id"`
	More        string `gorm:"column:more"`
}

// TableName table name
func (LiveCustom) TableName() string {
	return "live_custom"
}

// DB the db instance of LiveCustom model
func (l LiveCustom) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeCreate automatically sets columns create_time and modified_time
func (l *LiveCustom) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically updates the column modified_time
func (l *LiveCustom) BeforeUpdate() error {
	l.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}
