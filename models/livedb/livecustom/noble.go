package livecustom

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// FindUserCustomNobleHornBubble 查询用户自定义贵族喇叭
func FindUserCustomNobleHornBubble(userID int64, when time.Time) (*LiveCustom, error) {
	var liveCustom LiveCustom
	err := service.LiveDB.
		Where("custom_type = ? AND element_id = ?", TypeNobleHornBubble, userID).
		Where("delete_time = 0 AND start_time <= ? AND end_time > ?", when.Unix(), when.Unix()).
		Order("create_time DESC").
		Take(&liveCustom).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &liveCustom, nil
}

// AddUserCustomNobleHornBubble 添加用户自定义贵族喇叭
// startTime, endTime 时间戳，单位：秒
func AddUserCustomNobleHornBubble(userID int64, hornBubble *params.HornBubble, startTime, endTime int64) error {
	hornBubbleJSON, err := json.Marshal(hornBubble)
	if err != nil {
		return err
	}
	err = service.LiveDB.Create(&LiveCustom{
		CustomType: TypeNobleHornBubble,
		ElementID:  userID,
		StartTime:  startTime,
		EndTime:    endTime,
		More:       string(hornBubbleJSON), // 所有等级（含上神）可定制的贵族气泡信息
	}).Error
	if err != nil {
		return err
	}
	return nil
}
