package livecustom

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAddUserCustomNobleHornBubbleAndFindUserCustomNobleHornBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	var (
		testUserID = int64(999)

		now   = goutil.TimeNow()
		param = &params.HornBubble{StartTime: 1, EndTime: 2}
	)

	err := service.LiveDB.Delete(&LiveCustom{}, "element_id = ? AND custom_type = ?", testUserID, TypeNobleHornBubble).Error
	require.NoError(err)

	err = AddUserCustomNobleHornBubble(testUserID, param, now.Unix(), now.AddDate(0, 1, 0).Unix())
	require.NoError(err)

	c, err := FindUserCustomNobleHornBubble(testUserID, now)
	require.NoError(err)
	require.NotNil(c)
	paramJSON, err := json.Marshal(param)
	require.NoError(err)
	assert.Equal(string(paramJSON), c.More)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 1, 2, 0, 0, 0, 0, time.Local)
	})
	err = AddUserCustomNobleHornBubble(testUserID, param, now.Unix(), now.AddDate(0, 2, 0).Unix())
	require.NoError(err)
	c, err = FindUserCustomNobleHornBubble(testUserID, now)
	require.NoError(err)
	require.NotNil(c)
	assert.EqualValues(now.AddDate(0, 2, 0).Unix(), c.EndTime)

	c, err = FindUserCustomNobleHornBubble(testUserID, now.AddDate(1, 0, 0))
	require.NoError(err)
	assert.Nil(c)
}
