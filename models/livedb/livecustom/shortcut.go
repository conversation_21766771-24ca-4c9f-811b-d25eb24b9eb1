package livecustom

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const allRoomElementID = 0

// AddShortcutGifts add shortcut gift
func AddShortcutGifts(roomIDs []int64, giftID, startTime, endTime int64) error {
	elements := make([]LiveCustom, 0, len(roomIDs))
	nowUnix := goutil.TimeNow().Unix()
	for i := range roomIDs {
		elements = append(elements, LiveCustom{
			CustomType:   TypeShortcutGift,
			CustomID:     giftID,
			ElementID:    roomIDs[i],
			StartTime:    startTime,
			EndTime:      endTime,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
		})
	}
	err := servicedb.BatchInsert(service.LiveDB, LiveCustom{}.TableName(), elements)
	if err != nil {
		return err
	}
	return nil
}

// RemoveShortcutGifts remove shortcut gift
func RemoveShortcutGifts(ids []int64) error {
	err := LiveCustom{}.DB().
		Where("id IN (?)", ids).Update("delete_time", goutil.TimeNow().Unix()).Error
	if err != nil {
		return err
	}
	return nil
}

// FindRoomShowShortcut find room show shortcut gift
func FindRoomShowShortcut(roomID int64) (*LiveCustom, error) {
	nowUnix := goutil.TimeNow().Unix()
	element := new(LiveCustom)
	err := element.DB().
		Where("custom_type = ? AND element_id IN (?)", TypeShortcutGift, []int64{allRoomElementID, roomID}).
		Where("delete_time = ?", 0).
		Where("start_time <= ? AND end_time > ?", nowUnix, nowUnix).
		Order("element_id DESC, id DESC"). // 直播间单独配置 > 全局配置
		Take(&element).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return element, nil
}

// FindShortcutByIDs find shortcut gift
func FindShortcutByIDs(ids []int64) ([]*LiveCustom, error) {
	var element []*LiveCustom
	err := LiveCustom{}.DB().
		Where("id IN (?) AND custom_type = ?", ids, TypeShortcutGift).
		Where("delete_time = ?", 0).
		Find(&element).Error
	if err != nil {
		return nil, err
	}
	return element, nil
}
