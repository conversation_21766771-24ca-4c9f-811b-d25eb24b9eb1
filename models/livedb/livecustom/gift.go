package livecustom

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// findAllCustomGiftIDs find custom gift ids
// customType: TypeUserCustomGift, elementID: userID
// customType: TypeRoomCustomGift, elementID: roomID
func findAllCustomGiftIDs(customType int, elementID int64) ([]int64, error) {
	nowUnix := goutil.TimeNow().Unix()
	var giftIDs []int64
	err := LiveCustom{}.DB().
		Where("custom_type = ? AND element_id = ? AND delete_time = ?", customType, elementID, 0).
		Where("start_time <= ? AND end_time > ?", nowUnix, nowUnix).
		Pluck("DISTINCT custom_id", &giftIDs).Error
	if err != nil {
		return nil, err
	}
	return giftIDs, nil
}

// FindAllUserCustomGiftIDs find user custom gift ids
func FindAllUserCustomGiftIDs(userID int64) ([]int64, error) {
	return findAllCustomGiftIDs(TypeUserCustomGift, userID)
}

// findCustomGift find showing custom gift
func findCustomGift(db *gorm.DB, customType int, giftID, elementID int64) (*LiveCustom, error) {
	nowUnix := goutil.TimeNow().Unix()
	var element LiveCustom
	err := db.Table(LiveCustom{}.TableName()).
		Where("custom_type = ? AND custom_id = ?", customType, giftID).
		Where("element_id = ? AND delete_time = ?", elementID, 0).
		Where("start_time <= ? AND end_time > ?", nowUnix, nowUnix).
		Take(&element).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &element, nil
}

// FindUserCustomGift find user custom gift
func FindUserCustomGift(userID, giftID int64) (*LiveCustom, error) {
	return findCustomGift(service.LiveDB, TypeUserCustomGift, giftID, userID)
}

// FindRoomCustomGift find room custom gift
func FindRoomCustomGift(roomID, giftID int64) (*LiveCustom, error) {
	return findCustomGift(service.LiveDB, TypeRoomCustomGift, giftID, roomID)
}

// isCustomGiftExists check custom gift exists
func isCustomGiftExists(customType int, giftID, elementID int64) (bool, error) {
	nowUnix := goutil.TimeNow().Unix()
	exists, err := servicedb.Exists(LiveCustom{}.DB().
		Where("custom_type = ? AND custom_id = ?", customType, giftID).
		Where("element_id = ? AND delete_time = ?", elementID, 0).
		Where("start_time <= ? AND end_time > ?", nowUnix, nowUnix))
	if err != nil {
		return false, err
	}
	return exists, nil
}

// IsUserCustomGiftExists check user custom gift exists
func IsUserCustomGiftExists(userID, giftID int64) (bool, error) {
	return isCustomGiftExists(TypeUserCustomGift, giftID, userID)
}

func addCustomGift(db *gorm.DB, customType int, elementID, giftID, startTime, endTime int64, source int) error {
	element := LiveCustom{
		CustomType: customType,
		CustomID:   giftID,
		ElementID:  elementID,
		Source:     source,
		StartTime:  startTime,
		EndTime:    endTime,
	}
	return db.Table(element.TableName()).Create(&element).Error
}

func addUserCustomGift(db *gorm.DB, userID, giftID, startTime, endTime int64, source int) error {
	return addCustomGift(db, TypeUserCustomGift, userID, giftID, startTime, endTime, source)
}

func addRoomCustomGift(db *gorm.DB, roomID, giftID int64, startTime, endTime time.Time, source int) error {
	return addCustomGift(db, TypeRoomCustomGift, roomID, giftID, startTime.Unix(), endTime.Unix(), source)
}

func removeCustomGift(db *gorm.DB, customType int, customID, elementID, giftID int64) error {
	query := db.Table(LiveCustom{}.TableName())
	if customID != 0 {
		query = query.Where("id = ?", customID)
	}
	return query.Where("custom_type = ? AND custom_id = ?", customType, giftID).
		Where("element_id = ? AND source = ?", elementID, SourceDefault).
		Where("delete_time = 0").Update("delete_time", goutil.TimeNow().Unix()).Error
}

func removeUserCustomGift(db *gorm.DB, customID, userID, giftID int64) error {
	return removeCustomGift(db, TypeUserCustomGift, customID, userID, giftID)
}

func removeRoomCustomGift(db *gorm.DB, customID, roomID, giftID int64) error {
	return removeCustomGift(db, TypeRoomCustomGift, customID, roomID, giftID)
}

// AddUserCustomGift add user custom gift
func AddUserCustomGift(userID, giftID int64, duration time.Duration, extend bool) error {
	now := goutil.TimeNow()
	return servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		liveCustom, err := findCustomGift(tx, TypeUserCustomGift, giftID, userID)
		if err != nil {
			return err
		}
		// 如果没有这个用户记录或者用户记录已经过期，则重新算过期时间，否则过期时间累加
		var expireTime int64
		if liveCustom == nil || liveCustom.ID == 0 {
			if extend {
				// 第二天 0 点加对应时长
				expireTime = goutil.BeginningOfDay(now).AddDate(0, 0, 1).Add(duration).Unix()
			} else {
				expireTime = now.Add(duration).Unix()
			}
		} else {
			// 在已有的基础上延长，对于永久的配置，是直接配置在礼物信息 gifts 里，这里不会有永久的配置
			expireTime = time.Unix(liveCustom.EndTime, 0).Add(duration).Unix()
		}
		err = addUserCustomGift(tx, userID, giftID, now.Unix(), expireTime, SourceDefault)
		if err != nil {
			return err
		}
		if liveCustom == nil || liveCustom.ID == 0 {
			return nil
		}
		// 移除旧的生效记录
		return removeUserCustomGift(tx, liveCustom.ID, userID, giftID)
	})
}

// AddRoomCustomGift add room custom gift
func AddRoomCustomGift(roomID, giftID int64, startTime, endTime time.Time, source int) error {
	return addRoomCustomGift(service.LiveDB, roomID, giftID, startTime, endTime, source)
}

// RemoveRoomCustomGift remove room custom gift
func RemoveRoomCustomGift(roomID, giftID int64) error {
	return removeRoomCustomGift(service.LiveDB, 0, roomID, giftID)
}

// RemoveLiveShowGift remove live show room custom gift
func RemoveLiveShowGift(roomID, giftID, startTime int64) error {
	err := LiveCustom{}.DB().
		Where("custom_type = ? AND custom_id = ?", TypeRoomCustomGift, giftID).
		Where("element_id = ? AND start_time = ? AND source = ?", roomID, startTime, SourceLiveShow).
		Where("delete_time = 0").
		Update("delete_time", goutil.TimeNow().Unix()).Error
	if err != nil {
		return err
	}
	return nil
}

// AssignRoomCustomGift add room custom gift and remove old custom
func AssignRoomCustomGift(customID, roomID, giftID int64, startTime, endTime time.Time, source int) error {
	return servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		err := addRoomCustomGift(tx, roomID, giftID, startTime, endTime, source)
		if err != nil {
			return err
		}
		if customID == 0 {
			return nil
		}
		// 移除旧记录
		return removeRoomCustomGift(tx, customID, roomID, giftID)
	})
}

// FindAllRoomCustomGiftIDs find room custom gift ids
func FindAllRoomCustomGiftIDs(roomID int64) ([]int64, error) {
	return findAllCustomGiftIDs(TypeRoomCustomGift, roomID)
}

// IsExistsRoomCustomGift exists room custom gift
func IsExistsRoomCustomGift(roomID, giftID int64) (bool, error) {
	return isCustomGiftExists(TypeRoomCustomGift, giftID, roomID)
}
