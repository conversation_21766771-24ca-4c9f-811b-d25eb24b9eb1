package livecustom

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	testUserID    int64 = 1
	testRoomID    int64 = 12345
	testGift111ID int64 = 111
	testGift222ID int64 = 222
)

func TestUserCustomGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?",
		TypeUserCustomGift, testUserID).Error)

	now := goutil.TimeNow()
	endTime := goutil.BeginningOfDay(now).AddDate(0, 0, 1).Add(time.Minute)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	err := AddUserCustomGift(testUserID, testGift111ID, time.Minute, true)
	require.NoError(err)
	exists, err := IsUserCustomGiftExists(testUserID, testGift111ID)
	require.NoError(err)
	assert.True(exists)
	res, err := FindUserCustomGift(testUserID, testGift111ID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(endTime.Unix(), res.EndTime)

	// 延长时间
	err = AddUserCustomGift(testUserID, testGift111ID, time.Minute, true)
	require.NoError(err)
	res, err = FindUserCustomGift(testUserID, testGift111ID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(endTime.Add(time.Minute).Unix(), res.EndTime)

	giftIDs, err := FindAllUserCustomGiftIDs(testUserID)
	require.NoError(err)
	assert.Equal([]int64{testGift111ID}, giftIDs)
}

func TestRoomCustomGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?", TypeRoomCustomGift, testRoomID).Error)
	// 添加过期的记录
	now := goutil.TimeNow()
	err := AddRoomCustomGift(testRoomID, testGift111ID, now.Add(-time.Hour), now.Add(-time.Minute), SourceDefault)
	require.NoError(err)
	exists, err := IsExistsRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	assert.False(exists)
	giftIDs, err := FindAllRoomCustomGiftIDs(testRoomID)
	require.NoError(err)
	assert.Empty(giftIDs)

	err = AddRoomCustomGift(testRoomID, testGift111ID, now, now.Add(time.Minute), SourceDefault)
	require.NoError(err)
	exists, err = IsExistsRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	assert.True(exists)
	giftIDs, err = FindAllRoomCustomGiftIDs(testRoomID)
	require.NoError(err)
	assert.Len(giftIDs, 1)

	err = AddRoomCustomGift(testRoomID, testGift222ID, now, now.Add(time.Minute), SourceDefault)
	require.NoError(err)
	exists, err = IsExistsRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	assert.True(exists)
	exists, err = IsExistsRoomCustomGift(testRoomID, testGift222ID)
	require.NoError(err)
	assert.True(exists)
	giftIDs, err = FindAllRoomCustomGiftIDs(testRoomID)
	require.NoError(err)
	assert.Len(giftIDs, 2)

	err = RemoveRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	exists, err = IsExistsRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	assert.False(exists)
}

func TestAssignRoomCustomGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?",
		TypeRoomCustomGift, testRoomID).Error)

	now := goutil.TimeNow()
	endTime := goutil.BeginningOfDay(now).AddDate(0, 0, 1).Add(time.Minute)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	err := AssignRoomCustomGift(0, testRoomID, testGift111ID, now, endTime, SourceDefault)
	require.NoError(err)
	exists, err := isCustomGiftExists(TypeRoomCustomGift, testGift111ID, testRoomID)
	require.NoError(err)
	assert.True(exists)
	res, err := FindRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(endTime.Unix(), res.EndTime)

	endTime = endTime.Add(time.Minute)
	err = AssignRoomCustomGift(res.ID, testRoomID, testGift111ID, now, endTime, SourceDefault)
	require.NoError(err)
	res, err = FindRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(endTime.Unix(), res.EndTime)

	newEndTime := endTime.Add(time.Minute)
	err = AssignRoomCustomGift(0, testRoomID, testGift111ID, now, newEndTime, SourceDefault)
	require.NoError(err)
	res, err = FindRoomCustomGift(testRoomID, testGift111ID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(endTime.Unix(), res.EndTime)
}

func TestRemoveLiveShowGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?", TypeRoomCustomGift, testRoomID).Error)
	now := goutil.TimeNow()
	endTime := now.Add(time.Minute)
	err := AddRoomCustomGift(testRoomID, testGift111ID, now, endTime, SourceDefault)
	require.NoError(err)
	err = AddRoomCustomGift(testRoomID, testGift222ID, now, endTime, SourceLiveShow)
	require.NoError(err)
	err = RemoveLiveShowGift(testRoomID, testGift222ID, now.Unix())
	require.NoError(err)
	giftIDs, err := FindAllRoomCustomGiftIDs(testRoomID)
	require.NoError(err)
	assert.Equal([]int64{testGift111ID}, giftIDs)
}
