package livecustom

import (
	"testing"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveCustom{}, "id", "create_time", "modified_time", "delete_time",
		"custom_type", "custom_id", "element_id", "element_sort", "source", "start_time", "end_time", "batch_id", "more")
}
