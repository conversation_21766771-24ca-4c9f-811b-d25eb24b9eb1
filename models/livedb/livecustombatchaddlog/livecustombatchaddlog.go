package livecustombatchaddlog

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LiveCustomBatchAddLog 直播间定制礼物批量分配日志
type LiveCustomBatchAddLog struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	DeleteTime   int64 `gorm:"column:delete_time"`

	CustomType    int    `gorm:"column:custom_type"`
	CustomID      int64  `gorm:"column:custom_id"`
	CSVURL        string `gorm:"column:csv_url"`
	EffectiveTime int64  `gorm:"column:effective_time"`
	ExpireTime    int64  `gorm:"column:expire_time"`
}

// TableName table name
func (LiveCustomBatchAddLog) TableName() string {
	return "live_custom_batch_add_log"
}

// DB the db instance of LiveCustomBatchAddLog model
func (l LiveCustomBatchAddLog) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeCreate gorm hook
func (l *LiveCustomBatchAddLog) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now
	return nil
}

// BeforeUpdate gorm hook
func (l *LiveCustomBatchAddLog) BeforeUpdate() error {
	l.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}
