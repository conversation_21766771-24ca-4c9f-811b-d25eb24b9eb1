package livemedalpointlog

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Status 状态
const (
	ChangeTypeIncrease = iota + 1 // 增加
	ChangeTypeDecrease            // 减少
)

// Scene 场景
const (
	SceneTypeAdmin      = iota + 1 // 超管后台
	SceneTypeFreeGift              // 免费礼物
	SceneTypeTaskShare             // 分享任务
	SceneTypeTaskOnline            // 在线任务
	SceneTypePayGift               // 付费礼物
	SceneTypeQuestion              // 提问
	SceneTypeSuperFan              // 超粉
	SceneTypeDanmaku               // 付费弹幕
	SceneTypeLuckyBox              // 宝盒
	SceneTypeGashapon              // 超能魔方
	SceneTypeTrialCard             // 粉丝勋章兑换卡
)

// LiveMedalPointChangeLog 粉丝勋章积分变化记录
type LiveMedalPointChangeLog struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	MedalOID            string `gorm:"column:medal_oid"` // live_medal 表的 _id
	RoomID              int64  `gorm:"column:room_id"`
	CreatorID           int64  `gorm:"column:creator_id"`
	UserID              int64  `gorm:"column:user_id"`
	ChangeType          int    `gorm:"column:change_type"`           // 变化类型, 1 增加; 2 减少
	Scene               int    `gorm:"column:scene"`                 // 变化场景
	BeforePoint         int64  `gorm:"column:before_point"`          // 变化前的总亲密度
	AfterPoint          int64  `gorm:"column:after_point"`           // 变化后的总亲密度
	ActualChangePoint   int64  `gorm:"column:actual_change_point"`   // 实际变化的亲密度，受亲密度上限、倍率、衰减返还影响
	ReturnedPoint       int64  `gorm:"column:returned_point"`        // 衰减返还的亲密度
	ExpectedChangePoint int64  `gorm:"column:expected_change_point"` // 计算倍率后的预期变化的亲密度，不受亲密度上限影响
	PointMulti          int64  `gorm:"column:point_multi"`           // 亲密度倍率
	TLevelThreshold     int64  `gorm:"column:t_level_threshold"`     // 当日亲密度上限
	LevelThresholdMulti int64  `gorm:"column:level_threshold_multi"` // 亲密度上限倍率
}

// TableName .
func (LiveMedalPointChangeLog) TableName() string {
	return "live_medal_point_change_log"
}

// DB 获取数据库连接
func DB() *gorm.DB {
	return service.LiveDB
}

// BeforeCreate .
func (l *LiveMedalPointChangeLog) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	l.CreateTime = nowUnix
	l.ModifiedTime = nowUnix
	return nil
}
