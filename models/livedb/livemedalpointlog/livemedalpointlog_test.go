package livemedalpointlog

import (
	"testing"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveMedalPointChangeLog{}, "id", "create_time", "modified_time", "medal_oid", "room_id", "creator_id", "user_id",
		"change_type", "scene", "before_point", "after_point", "actual_change_point", "returned_point", "expected_change_point",
		"point_multi", "t_level_threshold", "level_threshold_multi")
}
