package liveuser7daysrewardcreatorrank

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

// LiveUser7DaysRewardCreatorRank 用户近 7 日打赏排名
type LiveUser7DaysRewardCreatorRank struct {
	ID           int64 `gorm:"column:id;primary_key"` // ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间

	Bizdate   time.Time `gorm:"column:bizdate"`    // 业务日期
	UserID    int64     `gorm:"column:user_id"`    // 用户 ID
	RoomID    int64     `gorm:"column:room_id"`    // 房间 ID
	CreatorID int64     `gorm:"column:creator_id"` // 主播 ID
	Rank      int64     `gorm:"column:rank"`       // 打赏量排名，主播在用户近 7 日打赏的所有主播中的排名，从 1 开始，排名越靠前打赏量越高
}

// TableName 表名
func (LiveUser7DaysRewardCreatorRank) TableName() string {
	return "live_user_7days_reward_creator_rank"
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// ListUser7DaysCreatorRank 用户近 7 日打赏排名
func ListUser7DaysCreatorRank(userID int64) ([]*LiveUser7DaysRewardCreatorRank, error) {
	key := keys.LocalKeyUser7DaysCreatorRank.Format(userID)
	res, ok := service.Cache5Min.Get(key)
	if ok {
		ranks, _ := res.([]*LiveUser7DaysRewardCreatorRank)
		return ranks, nil
	}
	var ranks []*LiveUser7DaysRewardCreatorRank
	err := DB().Where("user_id = ?", userID).
		Where("bizdate = ?", DB().Select("bizdate").Model(LiveUser7DaysRewardCreatorRank{}).Order("id ASC").Limit(1).SubQuery()). // NOTICE: 同步是先写入数据再删除前一天的数据，所以这里取第一条数据（可能是前一天的数据）同批的数据，保证数据是完整的
		Find(&ranks).Error
	if err != nil {
		return nil, err
	}
	service.Cache5Min.Set(key, ranks, 0)
	return ranks, nil
}
