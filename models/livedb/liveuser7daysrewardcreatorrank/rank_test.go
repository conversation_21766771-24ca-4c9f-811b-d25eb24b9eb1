package liveuser7daysrewardcreatorrank

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&LiveUser7DaysRewardCreatorRank{}, "id", "create_time", "modified_time",
		"bizdate", "user_id", "room_id", "creator_id", "rank")
}

func TestListUser7DaysCreatorRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Delete(keys.LocalKeyUser7DaysCreatorRank.Format(123))
	service.Cache5Min.Delete(keys.LocalKeyUser7DaysCreatorRank.Format(1234))

	ids, err := ListUser7DaysCreatorRank(123)
	require.NoError(err)
	assert.Len(ids, 4)

	ids, err = ListUser7DaysCreatorRank(123)
	require.NoError(err)
	assert.Len(ids, 4)

	ids, err = ListUser7DaysCreatorRank(1234)
	require.NoError(err)
	assert.Len(ids, 0)
}
