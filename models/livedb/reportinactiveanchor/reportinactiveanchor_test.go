package reportinactiveanchor

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("report_inactive_anchor", InactiveAnchor{}.TableName())
}

func TestFindInactiveAnchorMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	require.NoError(InactiveAnchor{}.DB().FirstOrCreate(&InactiveAnchor{
		UserID:          233,
		GuildLiveDays:   1,
		GuildID:         1,
		LastLiveDate:    time.Date(2020, 04, 27, 0, 0, 0, 0, time.Local),
		GuildLiveIncome: 300,
		GmtCreate:       goutil.TimeNow(),
		GmtModified:     goutil.TimeNow(),
	}).Error)

	res, err := FindInactiveAnchorMap([]int64{233}, 1)
	require.NoError(err)
	assert.NotNil(res)
	assert.Equal(int64(1), res[233].GuildLiveDays)
	assert.Equal("2020-04-27", res[233].LastLiveDate.Format(goutil.TimeFormatYMD))
}
