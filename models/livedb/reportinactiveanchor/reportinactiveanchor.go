package reportinactiveanchor

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
)

// TableName 断播主播表名
func TableName() string {
	return "report_inactive_anchor"
}

// InactiveAnchor 断播用户
type InactiveAnchor struct {
	ID              int64         `gorm:"column:id;primary_key"`
	GmtCreate       time.Time     `gorm:"column:gmt_create"`
	GmtModified     time.Time     `gorm:"column:gmt_modified"`
	UserID          int64         `gorm:"column:user_id"`
	GuildID         int64         `gorm:"column:guild_id"`
	GuildLiveDays   int64         `gorm:"column:guild_live_days"`
	LastLiveDate    time.Time     `gorm:"column:last_live_date"`
	GuildLiveIncome util.Float2DP `gorm:"column:guild_live_income"`
}

// TableName table name
func (InactiveAnchor) TableName() string {
	return TableName()
}

// DB the db instance of InactiveAnchor model
func (i InactiveAnchor) DB() *gorm.DB {
	return service.LiveDB.Table(i.TableName())
}

// FindInactiveAnchorMap 获取断播主播，返回 map[userID]*InactiveAnchor
func FindInactiveAnchorMap(userIDs []int64, guildID int64) (map[int64]*InactiveAnchor /* map[userID]*InactiveAnchor */, error) {
	if len(userIDs) == 0 {
		return map[int64]*InactiveAnchor{}, nil
	}

	userIDs = util.Uniq(userIDs)
	var inactiveAnchorMap []*InactiveAnchor
	err := InactiveAnchor{}.DB().Select("user_id, last_live_date, guild_live_days, guild_live_income").
		Find(&inactiveAnchorMap, "user_id IN (?) AND guild_id = ?", userIDs, guildID).Error
	if err != nil {
		return nil, err
	}

	m := make(map[int64]*InactiveAnchor)
	for _, v := range inactiveAnchorMap {
		m[v.UserID] = v
	}
	return m, nil
}
