package livemulticonnect

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// 连线组限制
const (
	GroupOngoingMembersCountLimit = 9 // 连线组进行中成员数量限制
)

// member error
var (
	ErrGroupMemberModified = errors.New("连线组成员状态已变更")
)

// Group 主播连线组
type Group struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	StartTime int64  `gorm:"column:start_time"` // 单位：毫秒
	EndTime   int64  `gorm:"column:end_time"`   // 单位：毫秒
	ConnectID string `gorm:"column:connect_id"`
}

// TableName .
func (Group) TableName() string {
	return "live_multi_connect_group"
}

// BeforeCreate .
func (g *Group) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	g.CreateTime = nowUnix
	g.ModifiedTime = nowUnix
	return nil
}

// FindOngoingGroup .
func FindOngoingGroup(id int64) (*Group, error) {
	var group Group
	err := DB().Where("id = ? AND end_time = 0", id).Take(&group).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &group, nil
}

// Members 查询连线组正在连线的成员，会按照 ID 升序构建从 1 开始的麦序
func (g *Group) Members(tx *gorm.DB) ([]*GroupMember, error) {
	if tx == nil {
		tx = DB()
	}
	var members []*GroupMember
	err := tx.
		Where("group_id = ? AND end_time = 0", g.ID).
		Order("id ASC").
		Find(&members).Error
	if err != nil {
		return nil, err
	}
	for i, member := range members {
		member.Index = i + 1
	}
	return members, nil
}

type muteRoomMap struct {
	RoomID     int64 `gorm:"column:room_id"`
	MuteRoomID int64 `gorm:"column:mute_room_id"`
}

// MutedRoomIDsMap 获取当前连线各个房间的静音房间，返回房间对应的静音房间
func (g *Group) MutedRoomIDsMap() (map[int64][]int64, error) {
	var mutes []muteRoomMap
	err := DB().Select("member.room_id AS room_id, mute_member.room_id AS mute_room_id").
		Table(fmt.Sprintf("%s AS mute", GroupMute{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS member ON member.id = mute.group_member_id AND member.end_time = 0", GroupMember{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS mute_member ON mute_member.id = mute.mute_group_member_id AND mute_member.end_time = 0", GroupMember{}.TableName())).
		Where("mute.group_id = ?", g.ID).
		Find(&mutes).Error
	if err != nil {
		return nil, err
	}

	muteMap := make(map[int64][]int64, len(mutes))
	for _, mute := range mutes {
		muteMap[mute.RoomID] = append(muteMap[mute.RoomID], mute.MuteRoomID)
	}
	return muteMap, nil
}

// KeyScore 主播连线组礼物积分 key
func (g *Group) KeyScore() string {
	return keys.KeyMultiConnectGroupGiftScore1.Format(g.ID)
}

// ScoreHelper 根据传入的礼物或宝盒和数量更新主播连线组的积分
type ScoreHelper struct {
	Room   *room.Room
	Gift   *gift.Gift
	Goods  *livegoods.LiveGoods
	Num    int64
	UserID int64
}

// AddScore 更新连线组成员积分并返回通知消息数组
func (s ScoreHelper) AddScore() ([]*userapi.BroadcastElem, error) {
	// 计算送礼的积分，免费礼物只有猫粮和猫罐头计分
	var score int64
	if s.Gift != nil {
		giftScore, giftFreeScore := s.Gift.MultiConnectScores(s.Num)
		score += giftScore + giftFreeScore
	}
	if s.Goods != nil {
		score += int64(s.Goods.Price) * s.Num
	}
	if score == 0 {
		return nil, nil
	}

	groupMembers, err := FindOngoingMembersByRoomID(s.Room.RoomID)
	if err != nil {
		return nil, err
	}
	if len(groupMembers) == 0 {
		return nil, nil
	}
	roomMemberMap := util.ToMap(groupMembers, func(member *GroupMember) int64 {
		return member.RoomID
	})

	// 对所有房间下发更新积分的房间的消息
	m := roomMemberMap[s.Room.RoomID]
	afterScore, err := service.Redis.ZIncrBy(keys.KeyMultiConnectGroupGiftScore1.Format(m.GroupID), float64(score), strconv.FormatInt(s.Room.RoomID, 10)).Result()
	if err != nil {
		return nil, err
	}

	// 添加用户榜单积分
	err = AddUserRankScore(UserRankScoreSourceGift, m.GroupID, s.UserID, score)
	if err != nil {
		logger.WithFields(logger.Fields{
			"group_id": m.GroupID,
			"user_id":  s.UserID,
			"score":    score,
		}).Error(err)
		// PASS
	}

	res := make([]*userapi.BroadcastElem, 0, len(groupMembers))
	for _, member := range groupMembers {
		elem := &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: member.RoomID,
			Payload: ScoreUpdateBroadcastPayload{
				Type:   liveim.TypeMultiConnect,
				Event:  liveim.EventScoreUpdate,
				RoomID: member.RoomID,
				MultiConnect: &MultiConnectScoreInfo{
					GroupID: m.GroupID,
					Member: &MultiConnectMember{
						Index: m.Index,
						Score: goutil.NewInt64(int64(afterScore)),
						Room: &MultiConnectRoomInfo{
							RoomID:    s.Room.RoomID,
							CreatorID: s.Room.CreatorID,
						},
					},
				},
			},
		}
		res = append(res, elem)
	}
	return res, nil
}

// ClearScore 清除主播连线组礼物积分 key
func (g *Group) ClearScore() error {
	err := service.Redis.Del(g.KeyScore()).Err()
	if err != nil {
		return err
	}
	return nil
}

// FindGroupMembersScore 加载主播连线组的礼物积分，得到 room_id 和 score 的映射关系
func FindGroupMembersScore(groupID int64) (map[int64]int64, error) {
	res, err := service.Redis.ZRangeWithScores(keys.KeyMultiConnectGroupGiftScore1.Format(groupID), 0, -1).Result()
	if err != nil {
		return nil, err
	}
	scoreMap := make(map[int64]int64, len(res))
	for _, item := range res {
		roomID, err := strconv.ParseInt(item.Member.(string), 10, 64)
		if err != nil {
			logger.WithFields(logger.Fields{"group_id": groupID, "room_id": roomID}).Error("主播连线礼物积分类型异常")
			return nil, err
		}
		scoreMap[roomID] = int64(item.Score)
	}
	return scoreMap, nil
}

// CreateGroup 创建主播连线组
func CreateGroup(tx *gorm.DB, channelID int64) (*Group, error) {
	if tx == nil {
		tx = DB()
	}
	group := &Group{
		StartTime: goutil.TimeNow().UnixMilli(),
		ConnectID: strconv.FormatInt(channelID, 10),
	}
	err := tx.Create(&group).Error
	if err != nil {
		return nil, err
	}
	return group, nil
}

// InitMembers 创建主播连线组主麦和第一位成员
func (g *Group) InitMembers(tx *gorm.DB, ownerRoomID, joinRoomID int64) error {
	if tx == nil {
		tx = DB()
	}
	now := goutil.TimeNow()
	members := []GroupMember{
		{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			GroupID:      g.ID,
			StartTime:    now.UnixMilli(),
			Role:         MemberRoleOwner,
			Status:       MemberStatusOngoing,
			RoomID:       ownerRoomID, // 主麦
		},
		{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			GroupID:      g.ID,
			StartTime:    now.UnixMilli(),
			Role:         MemberRoleMember,
			Status:       MemberStatusOngoing,
			RoomID:       joinRoomID, // 加入方
		},
	}
	err := servicedb.BatchInsert(tx, GroupMember{}.TableName(), members)
	if err != nil {
		return err
	}
	return nil
}

// JoinMember 加入连线组
func (g *Group) JoinMember(tx *gorm.DB, roomID int64) (*GroupMember, error) {
	if tx == nil {
		tx = DB()
	}
	gm := &GroupMember{
		GroupID:   g.ID,
		StartTime: goutil.TimeNow().UnixMilli(),
		Role:      MemberRoleMember,
		Status:    MemberStatusOngoing,
		RoomID:    roomID,
	}
	err := tx.Create(&gm).Error
	if err != nil {
		return nil, err
	}
	return gm, nil
}

// Finish 结束连线组
func (g *Group) Finish(tx *gorm.DB) (bool, error) {
	if tx == nil {
		tx = DB()
	}
	now := goutil.TimeNow()
	db := tx.Model(Group{}).
		Where("id = ? AND end_time = 0", g.ID).
		Updates(map[string]any{
			"end_time":      now.UnixMilli(),
			"modified_time": now.Unix(),
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// OngoingMembersCount 查询连线组正在连线的成员数
func (g *Group) OngoingMembersCount(tx *gorm.DB) (int, error) {
	if tx == nil {
		tx = DB()
	}
	var count int
	err := tx.Model(GroupMember{}).
		Where("group_id = ? AND end_time = 0", g.ID).
		Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// MicOffRoomIDs 查询连线组闭麦的房间 ID
func (g *Group) MicOffRoomIDs() ([]int64, error) {
	var roomIDs []int64
	err := DB().Table(fmt.Sprintf("%s AS mic", GroupMicOff{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS member ON member.id = mic.group_member_id AND member.end_time = 0", GroupMember{}.TableName())).
		Where("mic.group_id = ?", g.ID).Pluck("member.room_id", &roomIDs).Error
	if err != nil {
		return nil, err
	}
	return roomIDs, nil
}

// LeaveMemberParam 从连线组中移除成员参数
type LeaveMemberParam struct {
	Group       *Group
	LeaveStatus int
	LeaveRoom   *room.Room

	beforeMembers     []*GroupMember
	leaveMember       *GroupMember
	withFinish        bool
	ownerMemberExited bool // 标记主麦是否退出了
}

// LeaveMember 从连线组中移除成员
func (lmp *LeaveMemberParam) LeaveMember() error {
	key := keys.LockMultiConnectGroupMemberUpdate1.Format(lmp.Group.ID)
	mutex := redismutex.New(service.Redis, key, 2*time.Second)
	if !mutex.TryLock() {
		return errors.New("当前连线组繁忙，请稍后再试")
	}
	defer mutex.Unlock()

	err := servicedb.Tx(DB(), func(tx *gorm.DB) error {
		var err error
		lmp.beforeMembers, err = lmp.Group.Members(tx)
		if err != nil {
			return err
		}
		if len(lmp.beforeMembers) < 2 {
			return ErrGroupMemberModified
		}
		lmp.leaveMember = filterTargetMemberByRoomID(lmp.beforeMembers, lmp.LeaveRoom.RoomID)
		if lmp.leaveMember == nil {
			return ErrGroupMemberModified
		}
		lmp.withFinish = len(lmp.beforeMembers) <= 2
		if lmp.withFinish {
			err = lmp.leaveWithFinish(tx)
		} else {
			err = lmp.leave(tx)
		}
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	// 清除积分不影响连线主流程，放在事务外处理
	if lmp.withFinish {
		err = lmp.Group.ClearScore()
		if err != nil {
			logger.WithField("group_id", lmp.Group.ID).Error(err)
			// PASS
		}
	} else {
		err = lmp.leaveMember.ClearScore()
		if err != nil {
			logger.WithFields(logger.Fields{"group_id": lmp.Group.ID, "member_id": lmp.leaveMember.ID}).Error(err)
			// PASS
		}
	}
	// 判断主麦是否退出
	if lmp.ownerMemberExited {
		// 主麦退出后，取消主麦发出的邀请和未处理的申请，不影响主流程，放在事务外处理
		err = CancelPendingMatch(lmp.leaveMember.RoomID)
		if err != nil {
			logger.WithField("room_id", lmp.leaveMember.RoomID).Errorf("cancel pending match error: %v", err)
			// PASS
		}
	}
	return nil
}

func (lmp *LeaveMemberParam) leave(tx *gorm.DB) error {
	if tx == nil {
		tx = DB()
	}
	switch lmp.LeaveStatus {
	case MemberStatusKickout:
		ok, err := lmp.leaveMember.Kickout(tx)
		if err != nil {
			return err
		}
		if !ok {
			return ErrGroupMemberModified
		}
	case MemberStatusQuit:
		ok, err := lmp.leaveMember.Quit(tx)
		if err != nil {
			return err
		}
		if !ok {
			return ErrGroupMemberModified
		}
		if lmp.leaveMember.ID == lmp.beforeMembers[0].ID {
			// 移交主麦
			ok, err = lmp.beforeMembers[1].UpdateOwner(tx)
			if err != nil {
				return err
			}
			if !ok {
				return ErrGroupMemberModified
			}
			// 标记主麦退出了
			lmp.ownerMemberExited = true
		}
	default:
		panic("invalid leave type")
	}

	record := lmp.leaveMember.NewRecord(lmp.beforeMembers[0].ID)
	err := tx.Create(record).Error
	if err != nil {
		return err
	}
	err = room.UnsetRoomMultiConnectStatus([]int64{lmp.leaveMember.RoomID})
	if err != nil {
		return err
	}
	return nil
}

func (lmp *LeaveMemberParam) leaveWithFinish(tx *gorm.DB) error {
	if len(lmp.beforeMembers) != 2 {
		return ErrGroupMemberModified
	}
	if tx == nil {
		tx = DB()
	}
	switch lmp.LeaveStatus {
	case MemberStatusKickout:
		// 踢出连线
		ok, err := lmp.leaveMember.Kickout(tx)
		if err != nil {
			return err
		}
		if !ok {
			return ErrGroupMemberModified
		}
	case MemberStatusQuit:
		ok, err := lmp.leaveMember.Quit(tx)
		if err != nil {
			return err
		}
		if !ok {
			return ErrGroupMemberModified
		}
	default:
		panic("invalid leave type")
	}

	var finishedMember *GroupMember
	for i := range lmp.beforeMembers {
		if lmp.beforeMembers[i].RoomID != lmp.leaveMember.RoomID {
			finishedMember = lmp.beforeMembers[i]
			break
		}
	}
	if finishedMember == nil {
		return ErrGroupMemberModified
	}
	// 结束剩余连线
	ok, err := finishedMember.Finish(tx)
	if err != nil {
		return err
	}
	if !ok {
		return ErrGroupMemberModified
	}
	// 结束连线组
	ok, err = lmp.Group.Finish(tx)
	if err != nil {
		return err
	}
	if !ok {
		return ErrGroupMemberModified
	}
	records := []*Record{
		lmp.leaveMember.NewRecord(lmp.beforeMembers[0].ID),
		finishedMember.NewRecord(lmp.beforeMembers[0].ID),
	}
	err = servicedb.BatchInsert(tx, Record{}.TableName(), records)
	if err != nil {
		return err
	}
	err = room.UnsetRoomMultiConnectStatus([]int64{lmp.leaveMember.RoomID, finishedMember.RoomID})
	if err != nil {
		return err
	}
	// 标记主麦也退出了
	lmp.ownerMemberExited = true
	return nil
}

// IsLimit 连线组进行中成员数量是否超出限制
func (g *Group) IsLimit(memberNum int) bool {
	return memberNum >= GroupOngoingMembersCountLimit
}

// Owner 获取多人连线主麦
func (g *Group) Owner() (*GroupMember, error) {
	var member GroupMember
	err := DB().Where("group_id = ? AND end_time = 0 AND role = ?", g.ID, MemberRoleOwner).Take(&member).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &member, nil
}

// AfterCloseRoom 关闭房间后的处理
func AfterCloseRoom(leaveRoom *room.Room) error {
	member, err := FindOngoingMemberByRoomID(nil, leaveRoom.RoomID)
	if err != nil {
		return err
	}
	if member == nil {
		return nil
	}
	group, err := FindOngoingGroup(member.GroupID)
	if err != nil {
		return err
	}
	if group == nil {
		logger.WithField("member_id", member.ID).Error("未找到连线组")
		// PASS
		return nil
	}

	p := LeaveMemberParam{
		Group:       group,
		LeaveStatus: MemberStatusQuit,
		LeaveRoom:   leaveRoom,
	}
	err = p.LeaveMember()
	if err != nil {
		return err
	}
	elems := p.BuildLeaveBroadcastList()
	if len(elems) > 0 {
		err = userapi.BroadcastMany(elems)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// CancelPendingMatch 当连接组结束时或者转移主麦时撤销主麦未处理的连线邀请及申请的消息
func CancelPendingMatch(roomID int64) error {
	// 查询直播间所有待处理的连线邀请和申请
	matches, err := FindPendingMatchesByRoomID(roomID)
	if err != nil {
		return err
	}
	if len(matches) == 0 {
		return nil
	}
	// 收集所有关联房间 ID（自动去重）
	roomIDs := collectRoomIDs(roomID, matches)
	// 批量查询直播间信息
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil)
	if err != nil {
		return err
	}
	// 构建直播间映射表
	roomMap := util.ToMap(rooms, func(r *room.Room) int64 {
		return r.RoomID
	})
	// 批量取消匹配
	err = BatchCancel(matches)
	if err != nil {
		return err
	}
	// 创建并发送广播消息
	return userapi.BroadcastMany(newCancelMatchBroadcastElems(matches, roomMap))
}

// collectRoomIDs 收集所有关联直播间 ID（包含主直播间和匹配涉及直播间）
func collectRoomIDs(baseRoomID int64, matches []*Match) []int64 {
	roomIDs := make([]int64, 0, len(matches)+1)
	roomIDs = append(roomIDs, baseRoomID)
	for _, m := range matches {
		switch m.Type {
		case MatchTypeApply:
			roomIDs = append(roomIDs, m.FromRoomID)
		case MatchTypeInvite:
			roomIDs = append(roomIDs, m.ToRoomID)
		}
	}
	return sets.Uniq(roomIDs)
}

// newCancelMatchBroadcastElems 创建广播消息元素
func newCancelMatchBroadcastElems(matches []*Match, roomMap map[int64]*room.Room) []*userapi.BroadcastElem {
	eventMap := map[int]string{
		MatchTypeApply:  liveim.EventApplyCancel,
		MatchTypeInvite: liveim.EventInviteCancel,
	}
	payloadElems := make([]*userapi.BroadcastElem, 0, 2*len(matches))
	for _, m := range matches {
		event, ok := eventMap[m.Type]
		if !ok {
			continue
		}
		fromRoom, fromExists := roomMap[m.FromRoomID]
		toRoom, toExists := roomMap[m.ToRoomID]
		if !fromExists || !toExists {
			continue
		}
		matchRoomMap := map[int64]*room.Room{
			m.FromRoomID: fromRoom,
			m.ToRoomID:   toRoom,
		}
		payloadElems = append(payloadElems, NewUnacceptBroadcastPayloadElems(event, m, matchRoomMap)...)
	}
	return payloadElems
}
