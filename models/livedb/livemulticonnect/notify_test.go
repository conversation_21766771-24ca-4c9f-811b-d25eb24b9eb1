package livemulticonnect

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MultiConnectInfo{}, "group_id", "status", "join_index", "duration", "quit_member",
		"mute_room_ids", "mic_off_room_ids", "members")
	kc.Check(MultiConnectMember{}, "index", "role", "score", "room")
	kc.Check(MultiConnectRoomInfo{}, "room_id", "name", "creator_id", "creator_username", "creator_iconurl")
	kc.Check(MultiConnectBroadcastPayload{}, "type", "event", "room_id", "multi_connect")
	kc.Check(ScoreUpdateBroadcastPayload{}, "type", "event", "room_id", "multi_connect")
	kc.Check(MultiConnectScoreInfo{}, "group_id", "member")
	kc.Check(RankSwitchBroadcastPayload{}, "type", "event", "room_id", "multi_connect")
}

func TestGroup_BuildOngoingMultiConnectList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testNowUnix = goutil.TimeNow().Unix()
	)
	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(4))
	require.NoError(err)
	require.Len(rooms, 4)

	group, err := CreateGroup(nil, 1)
	require.NoError(err)
	require.NotNil(group)

	res, err := group.BuildOngoingMultiConnectList()
	require.NoError(err)
	assert.Empty(res)

	members := []GroupMember{
		{GroupID: group.ID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: rooms[0].RoomID},
		{GroupID: group.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: rooms[1].RoomID},
		{GroupID: group.ID, Role: MemberRoleMember, Status: MemberStatusFinish, EndTime: testNowUnix, RoomID: rooms[1].RoomID},
		{GroupID: group.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: rooms[2].RoomID},
		{GroupID: group.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: rooms[3].RoomID},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))
	res, err = group.BuildOngoingMultiConnectList()
	require.NoError(err)
	require.Len(res, 4)
	for i := range res {
		assert.Equal(group.ID, res[i].GroupID)
		assert.Equal(MemberStatusOngoing, res[i].Status)
		require.Len(res[i].Members, 4)
		assert.Equal(rooms[i].RoomID, res[i].Members[0].Room.RoomID)
	}
}

func TestLeaveMemberParam_imEvent(t *testing.T) {
	assert := assert.New(t)

	p := LeaveMemberParam{
		LeaveStatus: MemberStatusQuit,
	}
	assert.Equal(liveim.EventQuit, p.imEvent())

	p = LeaveMemberParam{
		LeaveStatus: MemberStatusKickout,
	}
	assert.Equal(liveim.EventKickout, p.imEvent())
}

func TestLeaveMemberParam_BuildLeaveBroadcastList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(3))
	require.NoError(err)
	require.Len(rooms, 3)

	var (
		group         = &Group{ID: 1}
		beforeMembers = []*GroupMember{
			{ID: 1, GroupID: group.ID, Role: MemberRoleOwner, RoomID: rooms[0].RoomID},
			{ID: 2, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[1].RoomID},
			{ID: 3, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[2].RoomID},
		}
		leaveMember = beforeMembers[1]
	)

	p := LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusKickout,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: leaveMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   leaveMember,
	}
	elems := p.BuildLeaveBroadcastList()
	assert.Len(elems, 3)
}

func TestLeaveMemberParam_buildLeaveElems(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(3))
	require.NoError(err)
	require.Len(rooms, 3)

	var (
		group         = &Group{ID: 1}
		beforeMembers = []*GroupMember{
			{ID: 1, GroupID: group.ID, Role: MemberRoleOwner, RoomID: rooms[0].RoomID},
			{ID: 2, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[1].RoomID},
			{ID: 3, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[2].RoomID},
		}
		leaveMember = beforeMembers[1]
	)

	p := LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusQuit,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: leaveMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   leaveMember,
	}
	elems := p.buildLeaveElems()
	assert.Len(elems, 3)
}

func TestLeaveMemberParam_buildLeaveWithFinishElems(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)

	var (
		group         = &Group{ID: 1}
		beforeMembers = []*GroupMember{
			{ID: 1, GroupID: group.ID, Role: MemberRoleOwner, RoomID: rooms[0].RoomID},
			{ID: 2, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[1].RoomID},
		}
		leaveMember = beforeMembers[1]
	)

	p := LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusQuit,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: leaveMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   leaveMember,
		withFinish:    true,
	}
	elems := p.buildLeaveWithFinishElems()
	assert.Len(elems, 2)
}

func TestLeaveMemberParams_buildLeaveMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)

	var (
		group         = &Group{ID: 1}
		beforeMembers = []*GroupMember{
			{ID: 1, GroupID: group.ID, Role: MemberRoleOwner, RoomID: rooms[0].RoomID},
			{ID: 2, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[1].RoomID},
		}
		leaveMember = beforeMembers[1]
	)
	p := LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusKickout,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: leaveMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   leaveMember,
		withFinish:    true,
	}
	mc := p.buildLeaveMultiConnect()
	assert.Equal(group.ID, mc.GroupID)
	assert.Equal(NotifyGroupStatusFinish, mc.Status)
	require.NotNil(mc.QuitMember)
	assert.Equal(leaveMember.Role, mc.QuitMember.Role)
	assert.Equal(leaveMember.RoomID, mc.QuitMember.Room.RoomID)

	p = LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusQuit,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: leaveMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   leaveMember,
	}
	mc = p.buildLeaveMultiConnect()
	assert.Equal(group.ID, mc.GroupID)
	assert.Equal(NotifyGroupStatusOngoing, mc.Status)
	require.NotNil(mc.QuitMember)
}

func TestLeaveMemberParam_buildLeaveMultiConnectList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(3))
	require.NoError(err)
	require.Len(rooms, 3)

	var (
		group         = &Group{ID: 1}
		beforeMembers = []*GroupMember{
			{ID: 1, GroupID: group.ID, Role: MemberRoleOwner, RoomID: rooms[0].RoomID},
			{ID: 2, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[1].RoomID},
			{ID: 3, GroupID: group.ID, Role: MemberRoleMember, RoomID: rooms[2].RoomID},
		}
		leaveMember = beforeMembers[0]
	)
	p := LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusKickout,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: leaveMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   leaveMember,
	}
	multiList, err := p.buildLeaveMultiConnectList()
	require.NoError(err)
	require.Len(multiList, 2)
	assert.Equal(group.ID, multiList[0].GroupID)
	require.Len(multiList[0].Members, 2)
	assert.Equal(beforeMembers[1].RoomID, multiList[0].Members[0].Room.RoomID)
	assert.Equal(beforeMembers[2].RoomID, multiList[1].Members[0].Room.RoomID)
}

func TestAcceptMatchBroadcastMany(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		now = goutil.TimeNow()
	)
	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}}, opt.SetLimit(4))
	require.NoError(err)
	require.Len(rooms, 4)
	g := &Group{
		StartTime: now.UnixMilli(),
		ConnectID: "1",
	}
	require.NoError(DB().Create(&g).Error)
	members := []*GroupMember{
		{GroupID: g.ID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: rooms[0].RoomID},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: rooms[1].RoomID},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: rooms[2].RoomID},
		{GroupID: g.ID, Role: MemberRoleMember, EndTime: 1, Status: MemberStatusFinish, RoomID: rooms[3].RoomID},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	cancel := mrpc.SetMock("im://broadcast/many", func(interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()
	connectInfo, err := AcceptMatchBroadcastMany(liveim.EventApplyAccept, g, members[1].RoomID)
	require.NoError(err)
	require.NotNil(connectInfo)
	assert.Equal(g.ID, connectInfo.GroupID)
	assert.Equal(2, connectInfo.JoinIndex)
	require.Len(connectInfo.Members, 3)
	assert.Equal(members[1].RoomID, connectInfo.Members[0].Room.RoomID)
}

func TestNewRankSwitchBroadcasrPayload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGroupID := int64(1)
	members := []*GroupMember{
		{RoomID: 1},
		{RoomID: 2},
		{RoomID: 3},
	}
	elems := NewRankSwitchBroadcastPayload(liveim.EventRankOn, testGroupID, members)
	assert.Len(elems, 3)
	for i := range elems {
		assert.Equal(liveim.IMMessageTypeNormal, elems[i].Type)
		assert.Equal(members[i].RoomID, elems[i].RoomID)
		payload, ok := elems[i].Payload.(*RankSwitchBroadcastPayload)
		require.True(ok)
		assert.Equal(liveim.TypeMultiConnect, payload.Type)
		assert.Equal(liveim.EventRankOn, payload.Event)
		assert.Equal(members[i].RoomID, payload.RoomID)
		assert.Equal(testGroupID, payload.MultiConnect.GroupID)
	}
}
