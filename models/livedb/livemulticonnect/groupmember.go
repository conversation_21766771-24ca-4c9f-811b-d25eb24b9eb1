package livemulticonnect

import (
	"fmt"
	"slices"
	"sort"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MemberRole 成员角色
const (
	MemberRoleOwner  = iota + 1 // 主麦
	MemberRoleMember            // 成员
)

// MemberStatus 成员状态
const (
	MemberStatusOngoing = iota + 1 // 进行中
	MemberStatusQuit               // 退出
	MemberStatusKickout            // 踢出
	MemberStatusFinish             // 结束
)

// GroupMember 主播连线成员
type GroupMember struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	GroupID   int64 `gorm:"column:group_id"`
	StartTime int64 `gorm:"column:start_time"` // 连线开始时间，单位：毫秒
	EndTime   int64 `gorm:"column:end_time"`   // 连线结束时间，单位：毫秒
	Role      int   `gorm:"column:role"`       // 成员角色，1：主麦，2：成员
	Status    int   `gorm:"column:status"`     // 成员状态，1：进行中，2：退出，3：踢出，4：结束
	RoomID    int64 `gorm:"column:room_id"`

	Index int   `gorm:"-"` // 麦序，按照 ID 升序，从 1 开始
	Sort  int64 `gorm:"-"` // 排序字段，用于获取成员列表后的分组排序
}

// TableName .
func (GroupMember) TableName() string {
	return "live_multi_connect_group_member"
}

// BeforeCreate .
func (member *GroupMember) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	member.CreateTime = nowUnix
	member.ModifiedTime = nowUnix
	return nil
}

// MutedRoomIDs 获取当前直播间静音连线的成员
func (member *GroupMember) MutedRoomIDs() ([]int64, error) {
	var roomIDs []int64
	err := DB().Table(fmt.Sprintf("%s AS mute", GroupMute{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS member ON member.id = mute.mute_group_member_id AND member.end_time = 0", GroupMember{}.TableName())).
		Where("mute.group_id = ? AND mute.group_member_id = ?", member.GroupID, member.ID).
		Pluck("member.room_id", &roomIDs).Error
	if err != nil {
		return nil, err
	}
	return roomIDs, nil
}

// Mute 静音
func (member *GroupMember) Mute(muteMember *GroupMember) error {
	err := DB().Create(&GroupMute{
		GroupID:           member.GroupID,
		GroupMemberID:     member.ID,
		MuteGroupMemberID: muteMember.ID,
	}).Error
	if err != nil {
		return err
	}
	return nil
}

// Unmute 取消静音
func (member *GroupMember) Unmute(unmuteMember *GroupMember) error {
	err := DB().
		Where("group_id = ? AND group_member_id = ? AND mute_group_member_id = ?", member.GroupID, member.ID, unmuteMember.ID).
		Delete(&GroupMute{}).Error
	if err != nil {
		return err
	}
	return nil
}

// KeyScore 主播连线组积分 key
func (member *GroupMember) KeyScore() string {
	return keys.KeyMultiConnectGroupGiftScore1.Format(member.GroupID)
}

// ClearScore 清除主播连线组成员礼物积分
func (member *GroupMember) ClearScore() error {
	err := service.Redis.ZRem(member.KeyScore(), member.RoomID).Err()
	if err != nil {
		return err
	}
	return nil
}

func (member *GroupMember) updateMemberStatus(tx *gorm.DB, status int) (bool, error) {
	if tx == nil {
		tx = DB()
	}
	now := goutil.TimeNow()
	db := tx.Model(GroupMember{}).
		Where("id = ? AND end_time = 0", member.ID).
		Updates(map[string]any{
			"modified_time": now.Unix(),
			"end_time":      now.UnixMilli(),
			"status":        status,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	if db.RowsAffected == 0 {
		return false, nil
	}
	member.ModifiedTime = now.Unix()
	member.EndTime = now.UnixMilli()
	member.Status = status
	return true, nil
}

// Kickout 踢出连线
func (member *GroupMember) Kickout(tx *gorm.DB) (bool, error) {
	return member.updateMemberStatus(tx, MemberStatusKickout)
}

// Quit 退出连线
func (member *GroupMember) Quit(tx *gorm.DB) (bool, error) {
	return member.updateMemberStatus(tx, MemberStatusQuit)
}

// Finish 结束连线
func (member *GroupMember) Finish(tx *gorm.DB) (bool, error) {
	return member.updateMemberStatus(tx, MemberStatusFinish)
}

// UpdateOwner 更新为主麦
func (member *GroupMember) UpdateOwner(tx *gorm.DB) (bool, error) {
	if tx == nil {
		tx = DB()
	}
	now := goutil.TimeNow()
	db := tx.Model(GroupMember{}).
		Where("id = ? AND end_time = 0 AND role = ?", member.ID, MemberRoleMember).
		Updates(map[string]any{
			"modified_time": now.Unix(),
			"role":          MemberRoleOwner,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	if db.RowsAffected == 0 {
		return false, nil
	}
	member.ModifiedTime = now.Unix()
	member.Role = MemberRoleMember
	return true, nil
}

// NewRecord 连线记录
func (member *GroupMember) NewRecord(ownerMemberID int64) *Record {
	now := goutil.TimeNow()
	return &Record{
		CreateTime:         now.Unix(),
		ModifiedTime:       now.Unix(),
		GroupID:            member.GroupID,
		GroupMemberID:      member.ID,
		GroupOwnerMemberID: ownerMemberID,
		RoomID:             member.RoomID,
		StartTime:          member.StartTime,
		EndTime:            member.EndTime,
		Duration:           member.EndTime - member.StartTime,
	}
}

// MicOff 主播连线闭麦
func (member *GroupMember) MicOff() error {
	return DB().Create(&GroupMicOff{
		GroupID:       member.GroupID,
		GroupMemberID: member.ID,
	}).Error
}

// MicOn 主播连线取消闭麦
func (member *GroupMember) MicOn() error {
	return DB().Delete(&GroupMicOff{}, "group_id = ? AND group_member_id = ?", member.GroupID, member.ID).Error
}

// FindOngoingMembersByRoomID 根据 room_id 查询所在主播连线组成员
func FindOngoingMembersByRoomID(roomID int64) ([]*GroupMember, error) {
	var members []*GroupMember
	// 获取最近进行中的 group 下的所有成员
	err := DB().Table(GroupMember{}.TableName()).
		Where("group_id = ?", DB().Table(GroupMember{}.TableName()).Select("group_id").Where("room_id = ? AND end_time = 0", roomID).Order("id DESC").Limit(1).SubQuery()).
		Where("end_time = 0").Order("id ASC").
		Find(&members).Error
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}
	return members, nil
}

// FindRecentConnectedMembers 根据 roomID 查询最近连麦过的成员列表
func FindRecentConnectedMembers(roomID int64, interval time.Duration, filterConnecting bool) ([]*GroupMember, error) {
	timeNow := goutil.TimeNow()
	// 获取最近参加过的连线组中的全部成员记录
	// 为了确认同当前直播间有时间交集，不能只取其他直播间的最新一条数据
	subQuery := DB().Table(GroupMember{}.TableName()).Select("group_id").
		Where("room_id = ? AND start_time >= ?", roomID, timeNow.Add(-interval).UnixMilli()).
		SubQuery()
	var members []*GroupMember
	err := DB().Table(GroupMember{}.TableName()).
		Where("group_id IN ? AND start_time >= ?", subQuery, timeNow.Add(-interval).UnixMilli()).Order("id DESC").Find(&members).Error
	if err != nil {
		return nil, err
	}

	// groupRoomRecordsMap 表示当前房间按 group 划分的记录，主播多次进出同一个连线组会有多条记录
	groupRoomRecordsMap := make(map[int64][]*GroupMember)
	// groupRecords 表示在同一个连线组里的其他成员记录
	groupRecords := make([]*GroupMember, 0, len(members))
	// ongoingGroupID 当前房间所在的连线组 ID
	var ongoingGroupID int64
	// 第一次循环查询当前房间记录和连线组的关系，以及其他房间的记录
	for _, member := range members {
		if member.RoomID == roomID {
			groupRoomRecordsMap[member.GroupID] = append(groupRoomRecordsMap[member.GroupID], member)
			if member.EndTime == 0 {
				ongoingGroupID = member.GroupID
			}
		} else {
			groupRecords = append(groupRecords, member)
		}
	}

	visited := make(map[int64]bool)
	// ignoreRoomIDsMap 和当前房间在同一连线组的房间号
	ignoreRoomIDsMap := make(map[int64]bool)
	// connectedMembers 表示和当前房间时间有交集的成员
	connectedMembers := make([]*GroupMember, 0, len(members))
	// 第二次循环获取和当前房间有时间交集的其他成员
	for _, member := range groupRecords {
		// 表示已经处理过这个房间更新的记录或该房间正在与本房间连线
		if visited[member.RoomID] || ignoreRoomIDsMap[member.RoomID] {
			continue
		}
		// 过滤掉当前直播间正在连线的直播间
		if filterConnecting && member.GroupID == ongoingGroupID && member.EndTime == 0 {
			ignoreRoomIDsMap[member.RoomID] = true
			continue
		}
		// 获取当前房间再连线组的开始结束时间，判断与该成员是否有交集
		for _, roomMember := range groupRoomRecordsMap[member.GroupID] {
			roomStartTime, roomEndTime := roomMember.StartTime, roomMember.EndTime
			if roomEndTime == 0 {
				roomEndTime = timeNow.UnixMilli()
			}
			memberStartTime, memberEndTime := member.StartTime, member.EndTime
			if memberEndTime == 0 {
				memberEndTime = timeNow.UnixMilli()
			}
			if roomStartTime <= memberEndTime && memberStartTime <= roomEndTime {
				// sort 取房间记录的 ID 值，可以判断 connectedMembers 和本房间的连线顺序
				member.Sort = roomMember.ID
				connectedMembers = append(connectedMembers, member)
				visited[member.RoomID] = true
				break
			}
		}
	}
	sort.Slice(connectedMembers, func(i, j int) bool {
		if connectedMembers[i].Sort != connectedMembers[j].Sort {
			return connectedMembers[i].Sort > connectedMembers[j].Sort
		}
		return connectedMembers[i].ID > connectedMembers[j].ID
	})
	return connectedMembers, nil
}

// GroupMemberNum 连线组和对应成员数的结构体
type GroupMemberNum struct {
	GroupID int64 `gorm:"column:group_id"`
	Count   int   `gorm:"column:count"`
}

// FindMemberNumByRoomIDs 通过房间所在连线组的在线人数
func FindMemberNumByRoomIDs(roomIDs []int64) (map[int64]*GroupMemberNum, error) {
	ongoingMembers, err := FindOngoingMembers(roomIDs)
	if err != nil {
		return nil, err
	}

	groupIDs := util.Uniq(goutil.SliceMap(ongoingMembers, func(m *GroupMember) int64 {
		return m.GroupID
	}))

	var groupMemberNum []*GroupMemberNum
	err = DB().Table(GroupMember{}.TableName()).Select("group_id, COUNT(*) AS count").
		Where("group_id IN (?) AND end_time = 0", groupIDs).Group("group_id").Find(&groupMemberNum).Error
	if err != nil {
		return nil, err
	}

	groupMemberNumMap := util.ToMap(groupMemberNum, func(g *GroupMemberNum) int64 {
		return g.GroupID
	})

	roomMemberNumMap := make(map[int64]*GroupMemberNum, len(ongoingMembers))
	for _, m := range ongoingMembers {
		roomMemberNumMap[m.RoomID] = groupMemberNumMap[m.GroupID]
	}
	return roomMemberNumMap, nil
}

type memberSorter interface {
	getRoomID() int64
	getIndex() int
}

func sortMembers[T memberSorter](members []T, targetRoomID int64) {
	// 将当前房间排在首位，其他房间按麦序排列
	sort.Slice(members, func(i, j int) bool {
		if members[i].getRoomID() == targetRoomID {
			return true
		}
		if members[j].getRoomID() == targetRoomID {
			return false
		}
		return members[i].getIndex() < members[j].getIndex()
	})
}

// GroupMemberInfo 主播连线成员信息，包括麦序和礼物积分
type GroupMemberInfo struct {
	MemberID  int64
	GroupID   int64
	StartTime int64 // 连线开始时间，单位：毫秒
	EndTime   int64 // 连线结束时间，单位：毫秒
	Role      int   // 成员角色，1：主麦，2：成员
	RoomID    int64 // 成员房间号
	Index     int   // 成员麦序，从 1 开始按顺序递增
	Score     int64 // 成员礼物积分
}

func (gmi *GroupMemberInfo) getRoomID() int64 {
	return gmi.RoomID
}

func (gmi *GroupMemberInfo) getIndex() int {
	return gmi.Index
}

// BuildMemberInfos 获取成员信息的麦序和礼物积分，并将当前房间排首位后返回
func BuildMemberInfos(members []*GroupMember, roomID int64) ([]*GroupMemberInfo, error) {
	sort.Slice(members, func(i, j int) bool {
		return members[i].ID < members[j].ID
	})
	// 获取礼物积分
	scoreMap, err := FindGroupMembersScore(members[0].GroupID)
	if err != nil {
		return nil, err
	}

	// 更新成员麦序和礼物积分
	memberInfos := make([]*GroupMemberInfo, len(members))
	for idx, m := range members {
		memberInfos[idx] = &GroupMemberInfo{
			MemberID:  m.ID,
			GroupID:   m.GroupID,
			StartTime: m.StartTime,
			EndTime:   m.EndTime,
			Role:      m.Role,
			RoomID:    m.RoomID,
			Index:     idx + 1,
			Score:     scoreMap[m.RoomID],
		}
	}
	sortMembers(memberInfos, roomID)
	return memberInfos, nil
}

// FindOngoingMemberByRoomID 根据 room_id 查询进行中的连线信息
func FindOngoingMemberByRoomID(tx *gorm.DB, roomID int64) (*GroupMember, error) {
	if tx == nil {
		tx = DB()
	}
	var member GroupMember
	err := tx.Where("room_id = ? AND end_time = 0", roomID).Take(&member).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &member, nil
}

// FindOngoingMembers 查询直播间进行中的连麦记录
func FindOngoingMembers(roomIDs []int64) ([]*GroupMember, error) {
	var members []*GroupMember
	err := DB().Where("room_id IN (?) AND end_time = 0", roomIDs).Find(&members).Error
	if err != nil {
		return nil, err
	}
	return members, nil
}

// filterTargetMemberByRoomID 获取目标连线组成员
func filterTargetMemberByRoomID(groupMembers []*GroupMember, targetRoomID int64) *GroupMember {
	if len(groupMembers) == 0 {
		return nil
	}
	joinIndex := slices.IndexFunc(groupMembers, func(m *GroupMember) bool {
		return m.RoomID == targetRoomID
	})
	if joinIndex < 0 {
		return nil
	}
	return groupMembers[joinIndex]
}

// IsConnected 判断是否连线过
func IsConnected(fromRoomID, toRoomID int64, dayDuration time.Duration) (bool, error) {
	startTimeUnixMilli := goutil.TimeNow().Add(-dayDuration).UnixMilli()
	query := DB().Table(fmt.Sprintf("%s AS g1", GroupMember{}.TableName())).
		Joins(fmt.Sprintf("JOIN %s AS g2 ON g1.group_id = g2.group_id", GroupMember{}.TableName())).
		Where("g1.room_id = ? AND g2.room_id = ? AND g1.start_time >= ? AND g2.start_time >= ?", fromRoomID, toRoomID, startTimeUnixMilli, startTimeUnixMilli).
		Where("g1.start_time < g2.end_time OR g2.end_time = 0").
		Where("g1.end_time > g2.start_time OR g1.end_time = 0")
	exists, err := servicedb.Exists(query)
	if err != nil {
		return false, err
	}
	return exists, nil
}

// FindMembers 查询成员信息
func FindMembers(ids []int64) ([]*GroupMember, error) {
	var members []*GroupMember
	err := DB().Where("id IN (?)", ids).Find(&members).Error
	if err != nil {
		return nil, err
	}
	return members, nil
}

// FindNonFullGroupMemberRoomIDs 随机获取指定数量的未满员的主播连线的成员房间号
func FindNonFullGroupMemberRoomIDs(count int) ([]int64, error) {
	var roomIDs []int64
	// 获取未满员的组
	subQuery := DB().Table(GroupMember{}.TableName()).Select("group_id").
		Where("end_time = 0").Group("group_id").Having("COUNT(group_id) < ?", GroupOngoingMembersCountLimit).SubQuery()
	// 从未满员的组中随机获取限制数量的房间号，这里连线中的房间数据集小，直接使用 RAND() 排序
	err := DB().Table(GroupMember{}.TableName()).Where("group_id IN ? AND end_time = 0", subQuery).
		Order("RAND()").Limit(count).Pluck("room_id", &roomIDs).Error
	if err != nil {
		return nil, err
	}
	return roomIDs, nil
}
