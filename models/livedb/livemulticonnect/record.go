package livemulticonnect

import (
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Record 主播连线记录
type Record struct {
	ID           int64 `gorm:"column:id"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	GroupID            int64 `gorm:"column:group_id"`
	GroupMemberID      int64 `gorm:"column:group_member_id"`
	GroupOwnerMemberID int64 `gorm:"column:group_owner_member_id"`
	RoomID             int64 `gorm:"column:room_id"`
	StartTime          int64 `gorm:"column:start_time"` // 匹配开始时间，单位：毫秒
	EndTime            int64 `gorm:"column:end_time"`   // 匹配结束时间，单位：毫秒
	Duration           int64 `gorm:"column:duration"`   // 连线时长，单位：毫秒
}

// TableName .
func (Record) TableName() string {
	return "live_multi_connect_record"
}

// BeforeCreate .
func (r *Record) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	r.CreateTime = nowUnix
	r.ModifiedTime = nowUnix
	return nil
}

// FindMemberRecentRecords 查询主播最近的连麦记录，start_time 倒序
func FindMemberRecentRecords(roomID int64, limit int) ([]*Record, error) {
	var records []*Record
	err := DB().Where("room_id = ?", roomID).
		Order("start_time DESC").
		Limit(limit).
		Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}
