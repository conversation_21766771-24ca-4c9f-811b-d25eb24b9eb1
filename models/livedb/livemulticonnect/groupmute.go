package livemulticonnect

import (
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// GroupMute 主播连线静音
type GroupMute struct {
	ID           int64 `gorm:"column:id"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	GroupID           int64 `gorm:"column:group_id"`
	GroupMemberID     int64 `gorm:"column:group_member_id"`
	MuteGroupMemberID int64 `gorm:"column:mute_group_member_id"`
}

// TableName .
func (GroupMute) TableName() string {
	return "live_multi_connect_group_mute"
}

// BeforeCreate .
func (mute *GroupMute) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	mute.CreateTime = nowUnix
	mute.ModifiedTime = nowUnix
	return nil
}
