package livemulticonnect

import (
	"fmt"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Blocklist 主播连线黑名单
type Blocklist struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	RoomID      int64 `gorm:"column:room_id"`
	BlockRoomID int64 `gorm:"column:block_room_id"`
}

// TableName .
func (Blocklist) TableName() string {
	return "live_multi_connect_blocklist"
}

// BeforeCreate .
func (bl *Blocklist) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	bl.CreateTime = nowUnix
	bl.ModifiedTime = nowUnix
	return nil
}

// AddBlock 添加拉黑
func AddBlock(roomID, blockRoomID int64) error {
	return DB().Create(&Blocklist{
		RoomID:      roomID,
		BlockRoomID: blockRoomID,
	}).Error
}

// RemoveBlock 移除拉黑
func RemoveBlock(roomID, blockRoomID int64) error {
	return DB().Where("room_id = ? AND block_room_id = ?", roomID, blockRoomID).Delete(Blocklist{}).Error
}

// BlockedRoomIDs 获取当前直播间拉黑的房间
// 若 checkRoomIDs 为空，则返回所有拉黑的房间
// 返回值分别表示当前直播间拉黑的房间，拉黑当前直播间的房间
func BlockedRoomIDs(roomID int64, checkRoomIDs ...int64) ([]int64, []int64, error) {
	db := DB().Model(Blocklist{}).Select("room_id, block_room_id")
	if len(checkRoomIDs) == 0 {
		db = db.Where("room_id = ? OR block_room_id = ?", roomID, roomID)
	} else {
		db = db.Where("room_id = ? AND block_room_id IN (?)", roomID, checkRoomIDs).
			Or("room_id IN (?) AND block_room_id = ?", checkRoomIDs, roomID)
	}
	var blocks []*Blocklist
	err := db.Find(&blocks).Error
	if err != nil {
		return nil, nil, err
	}

	var (
		blockedRoomIDs        = make([]int64, 0, len(blocks))
		blockedCurrentRoomIDs = make([]int64, 0, len(blocks))
	)
	for _, block := range blocks {
		switch {
		case block.RoomID == roomID:
			blockedRoomIDs = append(blockedRoomIDs, block.BlockRoomID)
		case block.BlockRoomID == roomID:
			blockedCurrentRoomIDs = append(blockedCurrentRoomIDs, block.RoomID)
		}
	}
	return blockedRoomIDs, blockedCurrentRoomIDs, nil
}

// IsBlocked 是否在黑名单
// 返回值分别表示 roomID 是否拉黑 checkRoomID，checkRoomID 是否拉黑 roomID
func IsBlocked(roomID, checkRoomID int64) (bool, bool, error) {
	var block struct {
		Block1 bool `gorm:"column:block1"` // roomID 是否拉黑 checkRoomID
		Block2 bool `gorm:"column:block2"` // checkRoomID 是否拉黑 roomID
	}
	err := DB().Table(Blocklist{}.TableName()).
		Select(fmt.Sprintf("IFNULL(MAX((room_id = %d AND block_room_id = %d)), 0) AS block1, IFNULL(MAX((block_room_id = %d AND room_id = %d)), 0) AS block2", roomID, checkRoomID, roomID, checkRoomID)).
		Where("room_id = ? AND block_room_id = ?", roomID, checkRoomID).
		Or("room_id = ? AND block_room_id = ?", checkRoomID, roomID).
		Take(&block).Error
	if err != nil {
		return false, false, err
	}
	return block.Block1, block.Block2, nil
}
