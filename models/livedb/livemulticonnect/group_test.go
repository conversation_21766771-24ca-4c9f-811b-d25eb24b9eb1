package livemulticonnect

import (
	"math/rand"
	"slices"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTags_Group(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Group{}, "id", "create_time", "modified_time", "start_time", "end_time", "connect_id")
}

func TestFindOngoingGroup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(100)
	err := DB().Delete(Group{}, "id = ?", groupID).Error
	require.NoError(err)

	g, err := FindOngoingGroup(groupID)
	require.NoError(err)
	assert.Nil(g)

	g = &Group{
		ID:        groupID,
		StartTime: goutil.TimeNow().Add(-time.Hour).UnixMilli(),
		EndTime:   goutil.TimeNow().Add(time.Hour).UnixMilli(),
	}
	err = DB().Create(g).Error
	require.NoError(err)
	g, err = FindOngoingGroup(groupID)
	require.NoError(err)
	assert.Nil(g)

	err = DB().Model(Group{}).Update("end_time = 0").Error
	require.NoError(err)
	g, err = FindOngoingGroup(groupID)
	require.NoError(err)
	assert.Nil(g)

	err = DB().Model(Group{}).Updates(map[any]any{
		"start_time": goutil.TimeNow().Add(-time.Minute).Unix(),
		"end_time":   0,
	}).Error
	require.NoError(err)
	g, err = FindOngoingGroup(groupID)
	require.NoError(err)
	assert.NotNil(g)
}

func TestGroup_MutedRoomIDsMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{
		ID: 11,
	}
	err := DB().Delete(group).Error
	require.NoError(err)
	err = DB().Create(group).Error
	require.NoError(err)
	err = DB().Delete(&GroupMember{}, "group_id = ?", group.ID).Error
	require.NoError(err)
	err = DB().Delete(&GroupMute{}, "group_id = ?", group.ID).Error
	require.NoError(err)

	muteRoomIDs, err := group.MutedRoomIDsMap()
	require.NoError(err)
	assert.Empty(muteRoomIDs)

	members := []*GroupMember{
		{
			ID:      100,
			GroupID: group.ID,
			RoomID:  1000,
		},
		{
			ID:      101,
			GroupID: group.ID,
			RoomID:  1001,
		},
		{
			ID:      102,
			GroupID: group.ID,
			RoomID:  1002,
		},
	}
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)
	mutes := []*GroupMute{
		{
			GroupID:           group.ID,
			GroupMemberID:     members[0].ID,
			MuteGroupMemberID: members[1].ID,
		},
		{
			GroupID:           group.ID,
			GroupMemberID:     members[0].ID,
			MuteGroupMemberID: members[2].ID,
		},
	}
	err = servicedb.BatchInsert(DB(), GroupMute{}.TableName(), mutes)
	require.NoError(err)
	muteRoomMap, err := group.MutedRoomIDsMap()
	require.NoError(err)
	require.Len(muteRoomMap[members[0].RoomID], 2)
	assert.Equal([]int64{members[1].RoomID, members[2].RoomID}, muteRoomMap[members[0].RoomID])
}

func TestGroup_KeyScore(t *testing.T) {
	assert := assert.New(t)

	group := &Group{ID: 100}
	assert.Equal("multi-connect:group:gift_score:100", group.KeyScore())
}

func TestScoreHelper_UpdateScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(100)
	g := Group{
		ID: groupID,
	}
	members := []*GroupMember{
		{GroupID: groupID, Status: MemberStatusOngoing, RoomID: 1, Role: MemberRoleOwner},
		{GroupID: groupID, Status: MemberStatusOngoing, RoomID: 2, Role: MemberRoleMember},
		{GroupID: groupID, Status: MemberStatusOngoing, RoomID: 3, Role: MemberRoleMember},
		{GroupID: groupID, Status: MemberStatusOngoing, RoomID: 4, Role: MemberRoleMember},
		{GroupID: groupID, Status: MemberStatusOngoing, RoomID: 5, Role: MemberRoleMember},
	}
	err := DB().Delete(GroupMember{}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)
	err = g.ClearScore()
	require.NoError(err)

	r := &room.Room{
		Helper: room.Helper{RoomID: 1, CreatorID: 2},
	}
	// 有价值的礼物
	gift1 := &gift.Gift{
		Price: 100,
	}
	num := int64(2)
	elems, err := ScoreHelper{
		Room: r,
		Gift: gift1,
		Num:  num,
	}.AddScore()
	require.NoError(err)
	assert.Len(elems, 5)
	score := gift1.Price * num
	for _, elem := range elems {
		payload := elem.Payload.(ScoreUpdateBroadcastPayload)
		assert.Equal(groupID, payload.MultiConnect.GroupID)
		assert.Equal(score, *payload.MultiConnect.Member.Score)
		assert.Equal(r.RoomID, payload.MultiConnect.Member.Room.RoomID)
		assert.Equal(r.CreatorID, payload.MultiConnect.Member.Room.CreatorID)
	}

	// 设置了计分的免费礼物
	gift2 := &gift.Gift{
		Point: 10,
		Attr:  1 << (gift.AttrPointAddMultiConnect - 1),
	}
	num = 10
	score += gift2.Point * num
	elems, err = ScoreHelper{
		Room: r,
		Gift: gift2,
		Num:  num,
	}.AddScore()
	require.NoError(err)
	assert.Len(elems, 5)
	for _, elem := range elems {
		payload := elem.Payload.(ScoreUpdateBroadcastPayload)
		assert.Equal(groupID, payload.MultiConnect.GroupID)
		assert.Equal(score, *payload.MultiConnect.Member.Score)
		assert.Equal(r.RoomID, payload.MultiConnect.Member.Room.RoomID)
		assert.Equal(r.CreatorID, payload.MultiConnect.Member.Room.CreatorID)
	}

	// 没有设置 attr 的免费礼物不会增加积分
	gift3 := &gift.Gift{
		Point: 10,
	}
	elems, err = ScoreHelper{
		Room: r,
		Gift: gift3,
		Num:  num,
	}.AddScore()
	require.NoError(err)
	assert.Empty(elems)
}

func TestGroup_ClearScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{ID: 999}
	err := service.Redis.ZAdd(group.KeyScore(), &redis.Z{Score: 1, Member: 1}).Err()
	require.NoError(err)

	err = group.ClearScore()
	require.NoError(err)

	exists, err := service.Redis.Exists(group.KeyScore()).Result()
	require.NoError(err)
	assert.Zero(exists)
}

func TestFindGroupMembersScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(999)
	require.NoError(service.Redis.Del(keys.KeyMultiConnectGroupGiftScore1.Format(groupID)).Err())
	scoreMap, err := FindGroupMembersScore(groupID)
	require.NoError(err)
	require.Empty(scoreMap)

	require.NoError(service.Redis.ZAdd(keys.KeyMultiConnectGroupGiftScore1.Format(groupID),
		&redis.Z{Score: 100, Member: 100},
		&redis.Z{Score: 101, Member: 101},
		&redis.Z{Score: 102, Member: 102},
	).Err())
	scoreMap, err = FindGroupMembersScore(groupID)
	require.NoError(err)
	assert.Equal(3, len(scoreMap))
}

func TestGroup_Members(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{
		ID: 11,
	}
	err := DB().Delete(&Group{}).Error
	require.NoError(err)
	err = DB().Create(group).Error
	require.NoError(err)
	err = DB().Delete(&GroupMember{}).Error
	require.NoError(err)

	members, err := group.Members(nil)
	require.NoError(err)
	assert.Empty(members)

	members = []*GroupMember{
		{
			ID:      100,
			GroupID: group.ID,
		},
		{
			ID:      101,
			GroupID: group.ID,
		},
		{
			ID:      102,
			GroupID: group.ID,
			EndTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		},
		{
			ID:      103,
			GroupID: group.ID,
		},
	}
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)
	members, err = group.Members(nil)
	require.NoError(err)
	require.Len(members, 3)
	assert.Equal(int64(100), members[0].ID)
	assert.Equal(1, members[0].Index)
	assert.True(slices.IsSortedFunc(members, func(a, b *GroupMember) int {
		return int(a.ID - b.ID)
	}))
}

func TestCreateGroup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testChannelID int64 = 1

	group, err := CreateGroup(nil, testChannelID)
	require.NoError(err)
	require.NotNil(group)
	assert.NotZero(group.ID)
	assert.Equal(strconv.FormatInt(testChannelID, 10), group.ConnectID)
	assert.NotZero(group.StartTime)
	assert.NotZero(group.ModifiedTime)
}

func TestGroup_InitMembers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group, err := CreateGroup(nil, 1)
	require.NoError(err)
	require.NotNil(group)
	err = group.InitMembers(nil, 1, 2)
	require.NoError(err)
	members, err := group.Members(nil)
	require.NoError(err)
	require.Len(members, 2)
	assert.Equal(int64(1), members[0].RoomID)
	assert.Equal(int64(2), members[1].RoomID)
}

func TestGroup_JoinMember(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testRoomID int64 = 123

	group, err := CreateGroup(nil, 1)
	require.NoError(err)
	require.NotNil(group)

	gm, err := group.JoinMember(nil, 123)
	require.NoError(err)
	require.NotNil(gm)
	assert.Equal(testRoomID, gm.RoomID)
	assert.Equal(MemberStatusOngoing, gm.Status)
	assert.NotZero(gm.StartTime)
	assert.NotZero(gm.ID)
}

func TestGroup_Finish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{
		ID: -99999,
	}
	ok, err := group.Finish(nil)
	require.NoError(err)
	assert.False(ok)

	group, err = CreateGroup(nil, 1)
	require.NoError(err)
	require.NotNil(group)
	ok, err = group.Finish(nil)
	require.NoError(err)
	assert.True(ok)
}

func TestOngoingMembersCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g := &Group{
		StartTime: goutil.TimeNow().UnixMilli(),
		ConnectID: "11111111",
	}
	require.NoError(DB().Create(&g).Error)
	count, err := g.OngoingMembersCount(nil)
	require.NoError(err)
	assert.Zero(count)

	members := []*GroupMember{
		{GroupID: g.ID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 2},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 4},
		{GroupID: g.ID, Role: MemberRoleMember, EndTime: 123, Status: MemberStatusFinish, RoomID: 5},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))
	count, err = g.OngoingMembersCount(nil)
	require.NoError(err)
	assert.Equal(4, count)
}

func TestGroup_MicOffRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g := &Group{
		ID: 110100,
	}
	members := []*GroupMember{
		{ID: 81402921, GroupID: g.ID, RoomID: 1},
		{ID: 81402922, GroupID: g.ID, RoomID: 2},
	}
	err := DB().Delete(GroupMember{}, "group_id = ?", g.ID).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)

	roomIDs, err := g.MicOffRoomIDs()
	require.NoError(err)
	assert.Empty(roomIDs)

	mics := []*GroupMicOff{
		{GroupID: g.ID, GroupMemberID: members[0].ID},
		{GroupID: g.ID, GroupMemberID: members[1].ID},
	}
	err = DB().Delete(GroupMicOff{}, "group_id = ?", g.ID).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), GroupMicOff{}.TableName(), mics)
	require.NoError(err)
	roomIDs, err = g.MicOffRoomIDs()
	require.NoError(err)
	assert.Equal([]int64{1, 2}, roomIDs)
}

func TestLeaveMemberParam_LeaveMember(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{ConnectID: "1"}
	require.NoError(DB().Create(group).Error)
	members := []*GroupMember{
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 1, Role: MemberRoleOwner},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 2, Role: MemberRoleMember},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	p := &LeaveMemberParam{
		Group:       group,
		LeaveStatus: MemberStatusQuit,
		LeaveRoom:   &room.Room{Helper: room.Helper{RoomID: members[0].RoomID}},
	}
	require.NoError(p.LeaveMember())
	members, err := group.Members(nil)
	require.NoError(err)
	require.Empty(members)
	var records []*Record
	err = DB().Where("group_id = ?", group.ID).Find(&records).Error
	require.NoError(err)
	assert.Len(records, 2)
}

func TestLeaveMemberParam_leave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{ConnectID: "1"}
	require.NoError(DB().Create(group).Error)
	members := []*GroupMember{
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 1, Role: MemberRoleOwner},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 2, Role: MemberRoleMember},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 3, Role: MemberRoleMember},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 4, Role: MemberRoleMember},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 5, Role: MemberRoleMember},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))
	beforeMembers, err := group.Members(nil)
	require.NoError(err)
	require.Len(beforeMembers, 5)

	// 踢出用户
	kickoutMember := filterTargetMemberByRoomID(beforeMembers, members[1].RoomID)
	p := &LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusKickout,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: kickoutMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   kickoutMember,
	}
	err = p.leave(nil)
	require.NoError(err)
	assert.Equal(MemberStatusKickout, p.leaveMember.Status)
	assert.NotZero(p.leaveMember.EndTime)
	beforeMembers, err = group.Members(nil)
	require.NoError(err)
	require.Len(beforeMembers, 4)
	assert.Nil(filterTargetMemberByRoomID(beforeMembers, members[1].RoomID))

	// 主麦退出
	quitMember := filterTargetMemberByRoomID(beforeMembers, members[0].RoomID)
	p = &LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusQuit,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: quitMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   quitMember,
	}
	err = p.leave(nil)
	require.NoError(err)
	assert.Equal(MemberStatusQuit, p.leaveMember.Status)
	assert.NotZero(p.leaveMember.EndTime)
	beforeMembers, err = group.Members(nil)
	require.NoError(err)
	require.Len(beforeMembers, 3)
	assert.Equal(members[2].RoomID, beforeMembers[0].RoomID)
	assert.Equal(MemberRoleOwner, beforeMembers[0].Role)

	// 非主麦退出
	quitMember = filterTargetMemberByRoomID(beforeMembers, members[4].RoomID)
	p = &LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusQuit,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: quitMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   quitMember,
	}
	err = p.leave(nil)
	require.NoError(err)
	assert.Equal(MemberStatusQuit, p.leaveMember.Status)
	assert.NotZero(p.leaveMember.EndTime)
	beforeMembers, err = group.Members(nil)
	require.NoError(err)
	require.Len(beforeMembers, 2)
	assert.Equal(members[2].RoomID, beforeMembers[0].RoomID)
	assert.Nil(filterTargetMemberByRoomID(beforeMembers, members[4].RoomID))
}

func TestLeaveMemberParam_leaveWithFinish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group := &Group{ConnectID: "1"}
	require.NoError(DB().Create(group).Error)
	members := []*GroupMember{
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 1, Role: MemberRoleOwner},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 2, Role: MemberRoleMember},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))
	beforeMembers, err := group.Members(nil)
	require.NoError(err)
	require.Len(beforeMembers, 2)

	// 踢出用户
	kickoutMember := filterTargetMemberByRoomID(beforeMembers, members[1].RoomID)
	p := &LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusKickout,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: kickoutMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   kickoutMember,
	}
	err = p.leaveWithFinish(nil)
	require.NoError(err)
	assert.Equal(MemberStatusKickout, p.leaveMember.Status)
	assert.NotZero(p.leaveMember.EndTime)
	beforeMembers, err = group.Members(nil)
	require.NoError(err)
	require.Empty(beforeMembers)
	g, err := FindOngoingGroup(group.ID)
	require.NoError(err)
	require.Nil(g)

	// 退出
	group = &Group{ConnectID: "1"}
	require.NoError(DB().Create(group).Error)
	members = []*GroupMember{
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 1, Role: MemberRoleOwner},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: 2, Role: MemberRoleMember},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))
	beforeMembers, err = group.Members(nil)
	require.NoError(err)
	require.Len(beforeMembers, 2)
	quitMember := filterTargetMemberByRoomID(beforeMembers, members[0].RoomID)
	p = &LeaveMemberParam{
		Group:         group,
		LeaveStatus:   MemberStatusQuit,
		LeaveRoom:     &room.Room{Helper: room.Helper{RoomID: quitMember.RoomID}},
		beforeMembers: beforeMembers,
		leaveMember:   quitMember,
	}
	err = p.leaveWithFinish(nil)
	require.NoError(err)
	assert.Equal(MemberStatusQuit, p.leaveMember.Status)
	assert.NotZero(p.leaveMember.EndTime)
	beforeMembers, err = group.Members(nil)
	require.NoError(err)
	require.Empty(beforeMembers)
	g, err = FindOngoingGroup(group.ID)
	require.NoError(err)
	require.Nil(g)
}

func TestGroup_Owner(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(&GroupMember{}).Error
	require.NoError(err)

	members := []GroupMember{
		{RoomID: 9074501, GroupID: 1, EndTime: 0, Role: MemberRoleOwner},
		{RoomID: 9074502, GroupID: 1, EndTime: 0, Role: MemberRoleMember},
		{RoomID: 9074503, GroupID: 1, EndTime: 1737363542, Role: MemberRoleOwner},
		{RoomID: 9074504, GroupID: 1, EndTime: 1737363542, Role: MemberRoleMember},
	}
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)

	g := &Group{ID: 1}
	member, err := g.Owner()
	require.NoError(err)
	assert.EqualValues(9074501, member.RoomID)
}

func TestAfterCloseRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &options.FindOptions{}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$gt": 0}, "creator_id": bson.M{"$gt": 1}}, opt.SetLimit(3))
	require.NoError(err)
	require.Len(rooms, 3)
	group := &Group{ConnectID: "1"}
	require.NoError(DB().Create(group).Error)
	members := []*GroupMember{
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: rooms[0].RoomID, Role: MemberRoleOwner},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: rooms[1].RoomID, Role: MemberRoleMember},
		{GroupID: group.ID, Status: MemberStatusOngoing, RoomID: rooms[2].RoomID, Role: MemberRoleMember},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(any any) (any, error) {
		return "success", nil
	})
	defer cancel()

	err = AfterCloseRoom(rooms[0])
	require.NoError(err)
	count, err := group.OngoingMembersCount(nil)
	require.NoError(err)
	assert.Equal(2, count)
}

func TestCancelPendingMatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rooms, err := room.List(bson.M{}, options.Find().SetLimit(11))
	require.NoError(err)
	require.Len(rooms, 11)

	t.Run("不存在要撤回的匹配记录", func(t *testing.T) {
		var count int
		cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
			count++
			return "success", nil
		})
		defer cancel()
		err = CancelPendingMatch(rand.Int63())
		require.NoError(err)
		assert.Equal(0, count)
	})
	t.Run("存在两条要撤回的匹配记录", func(t *testing.T) {
		var count int
		cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
			count++
			return "success", nil
		})
		defer cancel()
		matchRecords := []Match{
			{ID: 1, FromRoomID: rooms[0].RoomID, ToRoomID: rooms[1].RoomID, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 2, FromRoomID: rooms[1].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		}
		require.NoError(DB().Delete(Match{}).Error)
		require.NoError(servicedb.BatchInsert(DB(), Match{}.TableName(), matchRecords))
		err = CancelPendingMatch(rooms[0].RoomID)
		require.NoError(err)
		// broadcast 批量发送 1 次
		assert.Equal(1, count)
	})
	t.Run("存在十一条要撤回的匹配记录", func(t *testing.T) {
		var count int
		cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
			count++
			return "success", nil
		})
		defer cancel()
		matchRecords := []Match{
			{ID: 1, FromRoomID: rooms[0].RoomID, ToRoomID: rooms[1].RoomID, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 2, FromRoomID: rooms[1].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 3, FromRoomID: rooms[2].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 4, FromRoomID: rooms[3].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 5, FromRoomID: rooms[4].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 6, FromRoomID: rooms[5].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 7, FromRoomID: rooms[6].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 8, FromRoomID: rooms[7].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 9, FromRoomID: rooms[8].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 10, FromRoomID: rooms[9].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 11, FromRoomID: rooms[10].RoomID, ToRoomID: rooms[0].RoomID, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		}
		require.NoError(DB().Delete(Match{}).Error)
		require.NoError(servicedb.BatchInsert(DB(), Match{}.TableName(), matchRecords))
		err = CancelPendingMatch(rooms[0].RoomID)
		require.NoError(err)
		// broadcast 批量发送 2 次
		assert.Equal(2, count)
	})
}

func TestCollectRoomIDs(t *testing.T) {
	assert := assert.New(t)

	t.Run("只有一个主直播间", func(t *testing.T) {
		roomIDs := collectRoomIDs(1, nil)
		assert.Len(roomIDs, 1)
	})
	t.Run("匹配记录中存在重复直播间", func(t *testing.T) {
		roomIDs := collectRoomIDs(1, []*Match{
			{ID: 1, FromRoomID: 1, ToRoomID: 2, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 2, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		})
		assert.Len(roomIDs, 2)
	})
	t.Run("匹配记录正常", func(t *testing.T) {
		roomIDs := collectRoomIDs(1, []*Match{
			{ID: 1, FromRoomID: 1, ToRoomID: 2, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 3, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 4, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		})
		assert.Len(roomIDs, 4)
	})
}

func TestNewCancelMatchBroadcastElems(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("相关直播间映射关系不存在", func(t *testing.T) {
		matches := []*Match{
			{ID: 1, FromRoomID: 1, ToRoomID: 2, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 3, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 4, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		}
		roomMap := map[int64]*room.Room{
			5: {Helper: room.Helper{RoomID: 2}},
			6: {Helper: room.Helper{RoomID: 3}},
			7: {Helper: room.Helper{RoomID: 4}},
		}
		broadcastElems := newCancelMatchBroadcastElems(matches, roomMap)
		require.NotNil(broadcastElems)
		assert.Len(broadcastElems, 0)
	})
	t.Run("相关直播间映射关系都存在", func(t *testing.T) {
		matches := []*Match{
			{ID: 1, FromRoomID: 1, ToRoomID: 2, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 3, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 1, FromRoomID: 4, ToRoomID: 1, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		}
		roomMap := map[int64]*room.Room{
			1: {Helper: room.Helper{RoomID: 1}},
			2: {Helper: room.Helper{RoomID: 2}},
			3: {Helper: room.Helper{RoomID: 3}},
			4: {Helper: room.Helper{RoomID: 4}},
		}
		broadcastElems := newCancelMatchBroadcastElems(matches, roomMap)
		require.NotNil(broadcastElems)
		assert.Len(broadcastElems, 6)
	})
}
