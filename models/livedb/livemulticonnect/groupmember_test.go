package livemulticonnect

import (
	"slices"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

func TestConsts_GroupMember(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, MemberRoleOwner)
	assert.Equal(2, MemberRoleMember)

	assert.Equal(1, MemberStatusOngoing)
	assert.Equal(2, MemberStatusQuit)
	assert.Equal(3, MemberStatusKickout)
	assert.Equal(4, MemberStatusFinish)
}

func TestTags_GroupMember(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(GroupMember{}, "id", "create_time", "modified_time", "group_id", "start_time", "end_time", "role", "status", "room_id")
}

func TestGroupMember_MutedRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	member := &GroupMember{ID: 100, GroupID: 100, RoomID: 100}
	err := DB().Delete(GroupMute{}).Error
	require.NoError(err)
	err = DB().Delete(GroupMember{}).Error
	require.NoError(err)

	roomIDs, err := member.MutedRoomIDs()
	require.NoError(err)
	assert.Empty(roomIDs)

	members := []*GroupMember{
		{
			ID:      100,
			GroupID: member.ID,
			RoomID:  100,
		},
		{
			ID:      200,
			GroupID: member.ID,
			RoomID:  200,
		},
		{
			ID:      300,
			GroupID: member.ID,
			RoomID:  300,
		},
	}
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)
	mutes := []*GroupMute{
		{
			GroupID:           member.ID,
			GroupMemberID:     100,
			MuteGroupMemberID: 200,
		},
		{
			GroupID:           member.ID,
			GroupMemberID:     100,
			MuteGroupMemberID: 300,
		},
	}
	err = servicedb.BatchInsert(DB(), GroupMute{}.TableName(), mutes)
	require.NoError(err)
	roomIDs, err = member.MutedRoomIDs()
	require.NoError(err)
	assert.Equal([]int64{200, 300}, roomIDs)
}

func TestGroupMember_Mute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	member := &GroupMember{ID: 100, GroupID: 100, RoomID: 100}
	muteMember := &GroupMember{ID: 200, GroupID: 100, RoomID: 200}
	err := DB().Delete(GroupMute{}, "group_id = ?", member.ID).Error
	require.NoError(err)

	err = member.Mute(muteMember)
	require.NoError(err)

	var mutes []*GroupMute
	err = DB().Where("group_id = ? AND group_member_id = ?", member.GroupID, member.ID).Find(&mutes).Error
	require.NoError(err)
	assert.Len(mutes, 1)
}

func TestGroupMember_Unmute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	member := &GroupMember{ID: 100, GroupID: 100, RoomID: 100}
	unmuteMember := &GroupMember{ID: 200, GroupID: 100, RoomID: 200}
	err := DB().Delete(GroupMute{}, "group_id = ?", member.ID).Error
	require.NoError(err)
	err = member.Mute(unmuteMember)
	require.NoError(err)

	err = member.Unmute(unmuteMember)
	require.NoError(err)

	var mutes []*GroupMute
	err = DB().Where("group_id = ? AND group_member_id = ?", member.GroupID, member.ID).Find(&mutes).Error
	require.NoError(err)
	assert.Empty(mutes)
}

func TestGroupMember_KeyScore(t *testing.T) {
	assert := assert.New(t)

	member := &GroupMember{ID: 100, GroupID: 100}
	assert.Equal("multi-connect:group:gift_score:100", member.KeyScore())
}

func TestGroupMember_ClearScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	member := &GroupMember{ID: 100, GroupID: 100, RoomID: 100}
	err := service.Redis.Del(member.KeyScore()).Err()
	require.NoError(err)
	err = service.Redis.ZAdd(member.KeyScore(),
		&redis.Z{Score: 100, Member: 100},
		&redis.Z{Score: 200, Member: 200},
	).Err()
	require.NoError(err)

	err = member.ClearScore()
	require.NoError(err)

	res, err := service.Redis.ZRangeWithScores(member.KeyScore(), 0, -1).Result()
	require.NoError(err)
	assert.Len(res, 1)
	assert.Equal("200", res[0].Member)
}

func TestGroupMember_MicOffAndMicOn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	member := &GroupMember{ID: 100, GroupID: 100}
	err := DB().Delete(GroupMicOff{}, "group_member_id = ?", member.ID).Error
	require.NoError(err)

	err = member.MicOff()
	require.NoError(err)
	ok, err := servicedb.Exists(DB().Model(GroupMicOff{}).Where("group_member_id = ?", member.ID))
	require.NoError(err)
	assert.True(ok)

	err = member.MicOn()
	require.NoError(err)
	ok, err = servicedb.Exists(DB().Model(GroupMicOff{}).Where("group_member_id = ?", member.ID))
	require.NoError(err)
	assert.False(ok)
}

func TestFindOngoingMembersByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(GroupMember{}).Error)
	noMembers, err := FindOngoingMembersByRoomID(123456)
	require.NoError(err)
	assert.Empty(noMembers)

	timeUnix := int64(1682265000)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(timeUnix, 0)
	})
	defer cancel()

	members := []GroupMember{
		{ID: 100, GroupID: 100, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{ID: 101, EndTime: timeUnix, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusFinish, RoomID: 2},
		{ID: 102, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
		{ID: 103, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 4},
		{ID: 104, EndTime: timeUnix, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusFinish, RoomID: 5},
		{ID: 105, GroupID: 101, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 6},
		{ID: 106, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 5},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	roomID := int64(5)
	actualMemberInfos, err := FindOngoingMembersByRoomID(roomID)
	require.NoError(err)
	// 会获取到当前连线中的 member: 100, 102, 103, 106
	assert.Len(actualMemberInfos, 4)
}

func TestFindRecentConnectedMembers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNow := time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local)
	timeNowUnixMilli := timeNow.UnixMilli()
	cancel := goutil.SetTimeNow(func() time.Time {
		return timeNow
	})
	defer cancel()

	members := []*GroupMember{
		{ID: 100, GroupID: 100, Status: MemberStatusFinish, RoomID: 1, StartTime: timeNowUnixMilli - time.Hour.Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 30).Milliseconds()},
		{ID: 101, GroupID: 100, Status: MemberStatusFinish, RoomID: 2, StartTime: timeNowUnixMilli - time.Hour.Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 30).Milliseconds()},
		{ID: 102, GroupID: 100, Status: MemberStatusFinish, RoomID: 3, StartTime: timeNowUnixMilli - time.Hour.Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 30).Milliseconds()},
		{ID: 103, GroupID: 100, Status: MemberStatusFinish, RoomID: 5, StartTime: timeNowUnixMilli - time.Hour.Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 30).Milliseconds()},
		{ID: 104, GroupID: 101, Status: MemberStatusFinish, RoomID: 1, StartTime: timeNowUnixMilli - (time.Minute * 20).Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 10).Milliseconds()},
		{ID: 105, GroupID: 101, Status: MemberStatusFinish, RoomID: 3, StartTime: timeNowUnixMilli - (time.Minute * 20).Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 10).Milliseconds()},
		{ID: 106, GroupID: 101, Status: MemberStatusFinish, RoomID: 4, StartTime: timeNowUnixMilli - (time.Minute * 20).Milliseconds(), EndTime: timeNowUnixMilli - (time.Minute * 10).Milliseconds()},
		{ID: 107, GroupID: 100, Status: MemberStatusOngoing, RoomID: 1, StartTime: timeNowUnixMilli - (time.Minute * 5).Milliseconds(), EndTime: 0},
		{ID: 108, GroupID: 100, Status: MemberStatusFinish, RoomID: 3, StartTime: timeNowUnixMilli - (time.Minute * 5).Milliseconds(), EndTime: timeNowUnixMilli},
		{ID: 109, GroupID: 100, Status: MemberStatusOngoing, RoomID: 5, StartTime: timeNowUnixMilli - (time.Minute * 5).Milliseconds(), EndTime: 0},
		{ID: 110, GroupID: 100, Status: MemberStatusOngoing, RoomID: 6, StartTime: timeNowUnixMilli - (time.Minute * 5).Milliseconds(), EndTime: 0},
	}

	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	// 当前进行中的成员被过滤
	connectedMembers, err := FindRecentConnectedMembers(1, time.Minute*6, true)
	require.NoError(err)
	assert.Len(connectedMembers, 1)

	// 不过滤当前连线进行中的成员
	connectedMembers, err = FindRecentConnectedMembers(1, time.Minute*6, false)
	require.NoError(err)
	assert.Len(connectedMembers, 3)

	connectedMembers, err = FindRecentConnectedMembers(1, time.Minute*25, true)
	require.NoError(err)
	assert.Len(connectedMembers, 2)

	// 取到相同房间的最新一条记录，并且成员更新了 Sort
	connectedMembers, err = FindRecentConnectedMembers(1, time.Hour*2, true)
	require.NoError(err)
	assert.Len(connectedMembers, 3)
	for _, member := range connectedMembers {
		// 101 和 108 表示同一连线组成员会关联不同的本房间成员表记录，并更新排序字段
		if member.ID == 108 {
			assert.Equal(int64(107), member.Sort)
		}
		if member.ID == 101 {
			assert.Equal(int64(100), member.Sort)
		}
	}
}

func TestFindMemberNumOfGroups(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	members := []*GroupMember{
		{ID: 100, GroupID: 100, RoomID: 1, Role: MemberRoleMember, EndTime: 0},
		{ID: 101, GroupID: 100, RoomID: 2, Role: MemberRoleMember, EndTime: 0},
		{ID: 102, GroupID: 100, RoomID: 3, Role: MemberRoleMember, EndTime: 0},
		{ID: 103, GroupID: 100, RoomID: 4, Role: MemberRoleMember, EndTime: 0},
		{ID: 104, GroupID: 100, RoomID: 5, Role: MemberRoleMember, EndTime: 100},
		{ID: 105, GroupID: 101, RoomID: 6, Role: MemberRoleMember, EndTime: 0},
		{ID: 106, GroupID: 101, RoomID: 7, Role: MemberRoleMember, EndTime: 0},
		{ID: 107, GroupID: 101, RoomID: 8, Role: MemberRoleMember, EndTime: 100},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	actualResult, err := FindMemberNumByRoomIDs([]int64{1, 2, 3})
	require.NoError(err)
	expectedResult := map[int64]*GroupMemberNum{
		1: {
			GroupID: 100,
			Count:   4,
		},
		2: {
			GroupID: 100,
			Count:   4,
		},
		3: {
			GroupID: 100,
			Count:   4,
		},
	}
	assert.Equal(expectedResult, actualResult)

	actualResult, err = FindMemberNumByRoomIDs([]int64{6, 10})
	require.NoError(err)
	expectedResult = map[int64]*GroupMemberNum{
		6: {
			GroupID: 101,
			Count:   2,
		},
	}
	assert.Equal(expectedResult, actualResult)

	actualResult, err = FindMemberNumByRoomIDs([]int64{10})
	require.NoError(err)
	expectedResult = map[int64]*GroupMemberNum{}
	assert.Equal(expectedResult, actualResult)
}

func TestBuildMemberInfos(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(100)
	members := []*GroupMember{
		{ID: 100, GroupID: groupID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{ID: 103, GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
		{ID: 104, GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 4},
		{ID: 105, GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 5},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	require.NoError(service.Redis.Del(keys.KeyMultiConnectGroupGiftScore1.Format(groupID)).Err())
	require.NoError(service.Redis.ZAdd(keys.KeyMultiConnectGroupGiftScore1.Format(groupID),
		&redis.Z{Score: 1, Member: 1},
		&redis.Z{Score: 3, Member: 3},
		&redis.Z{Score: 4, Member: 4},
		&redis.Z{Score: 5, Member: 5},
	).Err())

	// 获得的结果 room_id 为 5 的排首位，其余房间按麦序排列
	roomID := int64(5)
	memberInfos, err := BuildMemberInfos(members, roomID)
	require.NoError(err)
	assert.Equal(len(members), len(memberInfos))
	assert.Equal(roomID, memberInfos[0].RoomID)
	for i := 0; i < len(memberInfos); i++ {
		assert.Equal(memberInfos[i].Score, memberInfos[i].RoomID)
	}
	assert.True(slices.IsSortedFunc(memberInfos[1:], func(a, b *GroupMemberInfo) int {
		return a.Index - b.Index
	}))
}

func TestFindOngoingMemberByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(100)
	members := []*GroupMember{
		{ID: 100, GroupID: groupID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{ID: 103, GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
		{ID: 104, GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 4},
		{ID: 105, GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 5},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	m, err := FindOngoingMemberByRoomID(nil, 5)
	require.NoError(err)
	assert.NotNil(m)

	m, err = FindOngoingMemberByRoomID(nil, -123456)
	require.NoError(err)
	assert.Nil(m)
}

func TestFindOngoingMembers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(100)
	members := []*GroupMember{
		{GroupID: groupID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
		{GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 4},
		{GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 5},
		{GroupID: groupID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 5, EndTime: goutil.TimeNow().Unix()},
	}
	require.NoError(DB().Delete(GroupMember{}).Error)
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	m, err := FindOngoingMembers([]int64{1, 2, 3, 4, 5})
	require.NoError(err)
	assert.Len(m, 4)
}

func TestFilterTargetMemberByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	members := []*GroupMember{
		{RoomID: 1},
		{RoomID: 2},
	}

	member := filterTargetMemberByRoomID(members, 1)
	require.NotNil(member)
	assert.Equal(int64(1), member.RoomID)

	member = filterTargetMemberByRoomID(members, 9999999)
	assert.Nil(member)
}

func TestIsConnected(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(&GroupMember{}).Error
	require.NoError(err)

	now := goutil.TimeNow()
	members := []GroupMember{
		{GroupID: 1, RoomID: 9074501, StartTime: now.UnixMilli()},
		{GroupID: 1, RoomID: 9074502, StartTime: now.UnixMilli()},
		{GroupID: 2, RoomID: 9074503, StartTime: now.UnixMilli()},
		{GroupID: 3, RoomID: 9074502, StartTime: now.AddDate(0, 0, -8).UnixMilli()},
		{GroupID: 3, RoomID: 9074504, StartTime: now.AddDate(0, 0, -8).UnixMilli()},
		{GroupID: 4, RoomID: 9074505, StartTime: now.AddDate(0, 0, -6).UnixMilli(), EndTime: now.AddDate(0, 0, -6).UnixMilli()},
		{GroupID: 4, RoomID: 9074506, StartTime: now.AddDate(0, 0, -5).UnixMilli(), EndTime: now.AddDate(0, 0, -5).UnixMilli()},
		{GroupID: 4, RoomID: 9074507, StartTime: now.Add(-time.Hour).UnixMilli(), EndTime: now.Add(-time.Minute).UnixMilli()},
		{GroupID: 4, RoomID: 9074508, StartTime: now.Add(-time.Hour * 2).UnixMilli(), EndTime: now.Add(-time.Minute).UnixMilli()},
	}
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)

	dayDuration := time.Hour * 24 * 7
	ok, err := IsConnected(9074501, 9074502, dayDuration)
	require.NoError(err)
	assert.True(ok)

	ok, err = IsConnected(9074501, 9074503, dayDuration)
	require.NoError(err)
	assert.False(ok)

	ok, err = IsConnected(9074502, 9074503, dayDuration)
	require.NoError(err)
	assert.False(ok)

	ok, err = IsConnected(9074505, 9074506, dayDuration)
	require.NoError(err)
	assert.False(ok)

	ok, err = IsConnected(9074507, 9074508, dayDuration)
	require.NoError(err)
	assert.True(ok)
}

func TestFindMembers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		memberIDs = []int64{100, 101, 102}
	)
	err := DB().Delete(GroupMember{}, "id IN (?)", memberIDs).Error
	require.NoError(err)

	m, err := FindMembers([]int64{100, 101, 102})
	require.NoError(err)
	assert.Len(m, 0) // 未查询到数据

	members := []*GroupMember{
		{ID: 100, GroupID: 100, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{ID: 101, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 2},
		{ID: 102, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))
	m, err = FindMembers([]int64{100, 101, 102})
	require.NoError(err)
	assert.Len(m, 3) // 查询到数据
}

func TestFindNonFullGroupMemberRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(GroupMember{}).Error
	require.NoError(err)

	members := []*GroupMember{
		{ID: 100, GroupID: 100, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{ID: 101, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 2},
		{ID: 102, GroupID: 100, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	roomIDs, err := FindNonFullGroupMemberRoomIDs(0)
	require.NoError(err)
	assert.Len(roomIDs, 0)

	roomIDs, err = FindNonFullGroupMemberRoomIDs(1)
	require.NoError(err)
	assert.Len(sets.Uniq(roomIDs), 1)

	roomIDs, err = FindNonFullGroupMemberRoomIDs(2)
	require.NoError(err)
	assert.Len(sets.Uniq(roomIDs), 2)

	roomIDs, err = FindNonFullGroupMemberRoomIDs(3)
	require.NoError(err)
	assert.Len(sets.Uniq(roomIDs), 3)

	roomIDs, err = FindNonFullGroupMemberRoomIDs(4)
	require.NoError(err)
	assert.Len(sets.Uniq(roomIDs), 3)
}
