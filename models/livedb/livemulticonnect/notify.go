package livemulticonnect

import (
	"slices"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 连线状态；1: 连线中；2: 已结束
const (
	NotifyGroupStatusOngoing = iota + 1
	NotifyGroupStatusFinish
)

// 加入类型；1: 申请，2: 邀请
const (
	NotifyGroupJoinTypeApply = iota + 1
	NotifyGroupJoinTypeInvite
)

// 加入状态；0: 未发起申请或邀请，1: 处理中
const (
	NotifyGroupJoinStatusDefault = iota
	NotifyGroupJoinStatusPending
)

// MultiConnectInfo 主播连线结构
type MultiConnectInfo struct {
	GroupID       int64                 `json:"group_id"` // 连线组 ID
	Status        int                   `json:"status"`
	JoinIndex     int                   `json:"join_index,omitempty"`
	Duration      *int64                `json:"duration,omitempty"` // 本房连线总时长，单位：毫秒
	QuitMember    *MultiConnectMember   `json:"quit_member,omitempty"`
	MuteRoomIDs   []int64               `json:"mute_room_ids,omitempty"`
	MicOffRoomIDs []int64               `json:"mic_off_room_ids,omitempty"`
	Members       []*MultiConnectMember `json:"members,omitempty"`
}

// MultiConnectMember 主播连线成员
type MultiConnectMember struct {
	Index int                   `json:"index,omitempty"`
	Role  int                   `json:"role,omitempty"`
	Score *int64                `json:"score,omitempty"`
	Room  *MultiConnectRoomInfo `json:"room,omitempty"`
}

func (gcm *MultiConnectMember) getRoomID() int64 {
	return gcm.Room.RoomID
}

func (gcm *MultiConnectMember) getIndex() int {
	return gcm.Index
}

// MultiConnectRoomInfo 连麦房间信息
type MultiConnectRoomInfo struct {
	RoomID          int64  `json:"room_id,omitempty"`
	Name            string `json:"name,omitempty"`
	CreatorID       int64  `json:"creator_id,omitempty"`
	CreatorUsername string `json:"creator_username,omitempty"`
	CreatorIconURL  string `json:"creator_iconurl,omitempty"`
}

// MultiConnectBroadcastPayload 连麦信息广播
type MultiConnectBroadcastPayload struct {
	Type         string            `json:"type"`
	Event        string            `json:"event"`
	RoomID       int64             `json:"room_id"`
	MultiConnect *MultiConnectInfo `json:"multi_connect"`
}

// ScoreUpdateBroadcastPayload 更新积分信息广播
type ScoreUpdateBroadcastPayload struct {
	Type         string                 `json:"type"`
	Event        string                 `json:"event"`
	RoomID       int64                  `json:"room_id"`
	MultiConnect *MultiConnectScoreInfo `json:"multi_connect"`
}

// MultiConnectScoreInfo 更新积分主播连线信息
type MultiConnectScoreInfo struct {
	GroupID int64               `json:"group_id"`
	Member  *MultiConnectMember `json:"member"`
}

func newMultiConnectRoomInfo(room *room.Room) *MultiConnectRoomInfo {
	return &MultiConnectRoomInfo{
		RoomID:          room.RoomID,
		Name:            room.Name,
		CreatorID:       room.CreatorID,
		CreatorUsername: room.CreatorUsername,
		CreatorIconURL:  room.CreatorIconURL,
	}
}

// BuildOngoingMultiConnectList 构建进行中的主播连线信息
// 构建完整的直播间连线成员信息，供 ws 刷新连麦成员信息使用
func (g *Group) buildOngoingMultiConnectList(members []*GroupMember) ([]*MultiConnectInfo, error) {
	if len(members) == 0 {
		return nil, nil
	}
	roomIDs := make([]int64, 0, len(members))
	for i := range members {
		roomIDs = append(roomIDs, members[i].RoomID)
	}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, err
	}
	scoreMap, err := FindGroupMembersScore(g.ID)
	if err != nil {
		return nil, err
	}
	muteRoomIDsMap, err := g.MutedRoomIDsMap()
	if err != nil {
		return nil, err
	}
	micOffRoomIDs, err := g.MicOffRoomIDs()
	if err != nil {
		return nil, err
	}

	roomMap := util.ToMap(rooms, func(r *room.Room) int64 { return r.RoomID })
	memberInfos := make([]*MultiConnectMember, 0, len(members))
	for i := range members {
		if r, ok := roomMap[members[i].RoomID]; ok {
			memberInfos = append(memberInfos, &MultiConnectMember{
				Index: members[i].Index,
				Role:  members[i].Role,
				Score: goutil.NewInt64(scoreMap[members[i].RoomID]),
				Room:  newMultiConnectRoomInfo(r),
			})
		}
	}
	nowUnixMilli := goutil.TimeNow().UnixMilli()
	connectInfoList := make([]*MultiConnectInfo, 0, len(members))
	for _, member := range members {
		info := &MultiConnectInfo{
			GroupID:       g.ID,
			Status:        NotifyGroupStatusOngoing,
			Duration:      goutil.NewInt64(max(nowUnixMilli-member.StartTime, 0)),
			MuteRoomIDs:   muteRoomIDsMap[member.RoomID],
			MicOffRoomIDs: micOffRoomIDs,
		}
		copyMember := slices.Clone(memberInfos)
		// 将 member 对应房间排在首位，其他房间按麦序排列
		sortMembers(copyMember, member.RoomID)
		info.Members = copyMember
		connectInfoList = append(connectInfoList, info)
	}
	return connectInfoList, nil
}

// BuildOngoingMultiConnectList 构建进行中的主播连线信息
// 构建完整的直播间连线成员信息，供 ws 刷新连麦成员信息使用
func (g *Group) BuildOngoingMultiConnectList() ([]*MultiConnectInfo, error) {
	var members []*GroupMember
	err := servicedb.Tx(DB(), func(tx *gorm.DB) error {
		var err error
		members, err = g.Members(tx)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}
	return g.buildOngoingMultiConnectList(members)
}

func (lmp *LeaveMemberParam) imEvent() string {
	switch lmp.LeaveStatus {
	case MemberStatusQuit:
		return liveim.EventQuit
	case MemberStatusKickout:
		return liveim.EventKickout
	default:
		panic("invalid leave type")
	}
}

func (lmp *LeaveMemberParam) status() int {
	if lmp.withFinish {
		return NotifyGroupStatusFinish
	}
	return NotifyGroupStatusOngoing
}

// BuildLeaveBroadcastList 构建连线组减少成员广播消息列表
func (lmp *LeaveMemberParam) BuildLeaveBroadcastList() []*userapi.BroadcastElem {
	if lmp.withFinish {
		return lmp.buildLeaveWithFinishElems()
	}
	return lmp.buildLeaveElems()
}

func (lmp *LeaveMemberParam) buildLeaveElems() []*userapi.BroadcastElem {
	quitMultiConnect := lmp.buildLeaveMultiConnect()
	elems := make([]*userapi.BroadcastElem, 0, len(lmp.beforeMembers))
	elems = append(elems, &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: lmp.LeaveRoom.RoomID,
		Payload: MultiConnectBroadcastPayload{
			Type:         liveim.TypeMultiConnect,
			Event:        lmp.imEvent(),
			RoomID:       lmp.LeaveRoom.RoomID,
			MultiConnect: quitMultiConnect,
		},
	})

	multiConnects, err := lmp.buildLeaveMultiConnectList()
	if err != nil {
		logger.WithField("group_id", lmp.Group.ID).Error(err)
		// PASS
	}
	for i := range multiConnects {
		multiConnects[i].QuitMember = quitMultiConnect.QuitMember
		elem := &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: multiConnects[i].Members[0].Room.RoomID,
			Payload: MultiConnectBroadcastPayload{
				Type:         liveim.TypeMultiConnect,
				Event:        lmp.imEvent(),
				RoomID:       multiConnects[i].Members[0].Room.RoomID,
				MultiConnect: multiConnects[i],
			},
		}
		elems = append(elems, elem)
	}
	return elems
}

func (lmp *LeaveMemberParam) buildLeaveWithFinishElems() []*userapi.BroadcastElem {
	quitMultiConnect := lmp.buildLeaveMultiConnect()
	elems := make([]*userapi.BroadcastElem, 0, len(lmp.beforeMembers))
	for i := range lmp.beforeMembers {
		elems = append(elems, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: lmp.beforeMembers[i].RoomID,
			Payload: MultiConnectBroadcastPayload{
				Type:         liveim.TypeMultiConnect,
				Event:        lmp.imEvent(),
				RoomID:       lmp.beforeMembers[i].RoomID,
				MultiConnect: quitMultiConnect,
			},
		})
	}
	return elems
}

func (lmp *LeaveMemberParam) buildLeaveMultiConnect() *MultiConnectInfo {
	return &MultiConnectInfo{
		GroupID: lmp.Group.ID,
		Status:  lmp.status(),
		QuitMember: &MultiConnectMember{
			Index: lmp.leaveMember.Index,
			Role:  lmp.leaveMember.Role,
			Room:  newMultiConnectRoomInfo(lmp.LeaveRoom),
		},
	}
}

func (lmp *LeaveMemberParam) buildLeaveMultiConnectList() ([]*MultiConnectInfo, error) {
	if lmp.withFinish || len(lmp.beforeMembers) == 0 {
		return nil, nil
	}

	var (
		index          int
		ongoingMembers = make([]*GroupMember, 0, len(lmp.beforeMembers)-1)
	)
	for i := range lmp.beforeMembers {
		if lmp.beforeMembers[i].ID == lmp.leaveMember.ID {
			continue
		}
		index++
		lmp.beforeMembers[i].Index = index
		ongoingMembers = append(ongoingMembers, lmp.beforeMembers[i])
	}
	if len(ongoingMembers) <= 0 {
		return nil, nil
	}
	if lmp.leaveMember.Role == MemberRoleOwner {
		// 如果退出的是主麦，将第一个成员设置为房主
		ongoingMembers[0].Role = MemberRoleOwner
	}
	return lmp.Group.buildOngoingMultiConnectList(ongoingMembers)
}

// RequestBroadcastPayload 主播连线请求信息 ws 消息格式
type RequestBroadcastPayload struct {
	Type         string               `json:"type"`
	Event        string               `json:"event"`
	RoomID       int64                `json:"room_id"`
	UserID       int64                `json:"user_id"`
	MultiConnect *RequestMultiConnect `json:"multi_connect"`
}

// RequestMultiConnect 主播连线请求信息
type RequestMultiConnect struct {
	GroupID         int64             `json:"group_id,omitempty"`
	MatchID         int64             `json:"match_id"`
	RemainDuration  int64             `json:"remain_duration"` // 剩余时间，单位：毫秒
	CreateTime      int64             `json:"create_time"`     // 秒级时间戳
	RecentConnected *bool             `json:"recent_connected,omitempty"`
	MemberNum       *int              `json:"member_num,omitempty"`
	ToRoom          *MultiConnectRoom `json:"to_room,omitempty"`
	FromRoom        *MultiConnectRoom `json:"from_room,omitempty"`
}

// MultiConnectRoomStatus 房间状态
type MultiConnectRoomStatus struct {
	Open      *int `json:"open,omitempty"`
	Attention bool `json:"attention"`
}

// MultiConnectRoomStatistics 房间统计信息
type MultiConnectRoomStatistics struct {
	Score          int64 `json:"score"`
	AttentionCount int64 `json:"attention_count"`
}

// MultiConnectRoom 房间信息
type MultiConnectRoom struct {
	RoomID          int64                       `json:"room_id"`
	Name            string                      `json:"name,omitempty"`
	CreatorID       int64                       `json:"creator_id"`
	CreatorUsername string                      `json:"creator_username"`
	CreatorIconURL  *string                     `json:"creator_iconurl,omitempty"`
	Status          *MultiConnectRoomStatus     `json:"status,omitempty"`
	Statistics      *MultiConnectRoomStatistics `json:"statistics,omitempty"`
}

// ScoreClearMultiConnect 清除礼物积分主播连线相关信息
type ScoreClearMultiConnect struct {
	GroupID int64 `json:"group_id"`
}

// ScoreClearBroadcastPayload 清除礼物积分广播
type ScoreClearBroadcastPayload struct {
	Type         string                  `json:"type"`
	Event        string                  `json:"event"`
	RoomID       int64                   `json:"room_id"`
	MultiConnect *ScoreClearMultiConnect `json:"multi_connect"`
}

// NewScoreClearBroadcastPayloadElems 创建清除礼物积分广播元素
func NewScoreClearBroadcastPayloadElems(members []*GroupMember) []*userapi.BroadcastElem {
	elems := make([]*userapi.BroadcastElem, 0, len(members))
	for _, member := range members {
		elems = append(elems, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: member.RoomID,
			Payload: &ScoreClearBroadcastPayload{
				Type:   liveim.TypeMultiConnect,
				Event:  liveim.EventScoreClear,
				RoomID: member.RoomID,
				MultiConnect: &ScoreClearMultiConnect{
					GroupID: member.GroupID,
				},
			},
		})
	}
	return elems
}

// UnacceptMultiConnect 主播连线不同意
type UnacceptMultiConnect struct {
	MatchID  int64             `json:"match_id"`
	FromRoom *MultiConnectRoom `json:"from_room"`
	ToRoom   *MultiConnectRoom `json:"to_room"`
}

// UnacceptBroadcastPayload 主播连线不同意广播
type UnacceptBroadcastPayload struct {
	Type         string                `json:"type"`
	Event        string                `json:"event"`
	RoomID       int64                 `json:"room_id"`
	UserID       int64                 `json:"user_id"`
	MultiConnect *UnacceptMultiConnect `json:"multi_connect"`
}

// NewUnacceptBroadcastPayloadElems 主播连线不同意广播
func NewUnacceptBroadcastPayloadElems(event string, match *Match, roomMap map[int64]*room.Room) []*userapi.BroadcastElem {
	multiConnect := &UnacceptMultiConnect{
		MatchID: match.ID,
		FromRoom: &MultiConnectRoom{
			RoomID:          roomMap[match.FromRoomID].RoomID,
			CreatorID:       roomMap[match.FromRoomID].CreatorID,
			CreatorUsername: roomMap[match.FromRoomID].CreatorUsername,
		},
		ToRoom: &MultiConnectRoom{
			RoomID:          roomMap[match.ToRoomID].RoomID,
			CreatorID:       roomMap[match.ToRoomID].CreatorID,
			CreatorUsername: roomMap[match.ToRoomID].CreatorUsername,
		},
	}

	elems := make([]*userapi.BroadcastElem, 0, len(roomMap))
	for _, r := range roomMap {
		elems = append(elems, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: r.RoomID,
			UserID: r.CreatorID,
			Payload: &UnacceptBroadcastPayload{
				Type:         liveim.TypeMultiConnect,
				Event:        event,
				RoomID:       r.RoomID,
				UserID:       r.CreatorID,
				MultiConnect: multiConnect,
			},
		})
	}
	return elems
}

// NewUnacceptBroadcastPayload 主播连线不同意广播
func NewUnacceptBroadcastPayload(event string, match *Match, roomMap map[int64]*room.Room) *UnacceptBroadcastPayload {
	multiConnect := &UnacceptMultiConnect{
		MatchID: match.ID,
		FromRoom: &MultiConnectRoom{
			RoomID:          roomMap[match.FromRoomID].RoomID,
			CreatorID:       roomMap[match.FromRoomID].CreatorID,
			CreatorUsername: roomMap[match.FromRoomID].CreatorUsername,
		},
		ToRoom: &MultiConnectRoom{
			RoomID:          roomMap[match.ToRoomID].RoomID,
			CreatorID:       roomMap[match.ToRoomID].CreatorID,
			CreatorUsername: roomMap[match.ToRoomID].CreatorUsername,
		},
	}

	return &UnacceptBroadcastPayload{
		Type:         liveim.TypeMultiConnect,
		Event:        event,
		RoomID:       multiConnect.FromRoom.RoomID,
		UserID:       multiConnect.FromRoom.CreatorID,
		MultiConnect: multiConnect,
	}
}

// AcceptMatchBroadcastMany 接受匹配后直播间消息广播, 并返回目标直播间的 MultiConnectInfo 信息
func AcceptMatchBroadcastMany(acceptEvent string, group *Group, targetRoomID int64) (*MultiConnectInfo, error) {
	multiConnectList, err := group.BuildOngoingMultiConnectList()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(multiConnectList) == 0 {
		return nil, actionerrors.NewErrLiveForbidden("当前连线组异常")
	}
	joinIndex := slices.IndexFunc(multiConnectList, func(m *MultiConnectInfo) bool {
		return len(m.Members) > 0 && m.Members[0].Room.RoomID == targetRoomID // 当前直播间已排到 member 首位
	})
	if joinIndex < 0 {
		return nil, actionerrors.ErrCannotFindResource
	}

	broadcastElems := make([]*userapi.BroadcastElem, 0, len(multiConnectList))
	for i := range multiConnectList {
		multiConnectList[i].JoinIndex = joinIndex + 1
		elem := &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: multiConnectList[i].Members[0].Room.RoomID,
			Payload: MultiConnectBroadcastPayload{
				Type:         liveim.TypeMultiConnect,
				Event:        acceptEvent,
				RoomID:       multiConnectList[i].Members[0].Room.RoomID,
				MultiConnect: multiConnectList[i],
			},
		}
		broadcastElems = append(broadcastElems, elem)
	}
	err = userapi.BroadcastMany(broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return multiConnectList[joinIndex], nil
}

// MuteMultiConnectMember 静音/取消静音成员
type MuteMultiConnectMember struct {
	Index int               `json:"index"`
	Room  *MultiConnectRoom `json:"room"`
}

// MuteMultiConnect 静音/取消静音消息体
type MuteMultiConnect struct {
	GroupID     int64                  `json:"group_id"`
	MuteRoomIDs []int64                `json:"mute_room_ids"`
	Member      MuteMultiConnectMember `json:"member"`
}

// MuteBroadcastPayload 静音/取消静音广播
type MuteBroadcastPayload struct {
	Type         string            `json:"type"`
	Event        string            `json:"event"`
	RoomID       int64             `json:"room_id"`
	MultiConnect *MuteMultiConnect `json:"multi_connect"`
}

// MicOffMultiConnect 闭麦
type MicOffMultiConnect struct {
	GroupID       int64   `json:"group_id"`
	MicOffRoomIDs []int64 `json:"mic_off_room_ids"`
}

// MicOffBroadcastPayload 闭麦广播
type MicOffBroadcastPayload struct {
	Type         string              `json:"type"`
	Event        string              `json:"event"`
	RoomID       int64               `json:"room_id"`
	MultiConnect *MicOffMultiConnect `json:"multi_connect"`
}

// NewMicOffBroadcastPayload 创建闭麦广播元素
func NewMicOffBroadcastPayload(groupID int64, members []*GroupMember, micOffRoomIDs []int64) []*userapi.BroadcastElem {
	multiConnect := &MicOffMultiConnect{
		GroupID:       groupID,
		MicOffRoomIDs: micOffRoomIDs,
	}
	elems := make([]*userapi.BroadcastElem, 0, len(members))
	for _, member := range members {
		elems = append(elems, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: member.RoomID,
			Payload: &MicOffBroadcastPayload{
				Type:         liveim.TypeMultiConnect,
				Event:        liveim.EventMicOff,
				RoomID:       member.RoomID,
				MultiConnect: multiConnect,
			},
		})
	}
	return elems
}

// RankSwitchMultiConnect 开关在线用户榜单
type RankSwitchMultiConnect struct {
	GroupID int64 `json:"group_id"`
}

// RankSwitchBroadcastPayload 开关榜单广播
type RankSwitchBroadcastPayload struct {
	Type         string                  `json:"type"`
	Event        string                  `json:"event"`
	RoomID       int64                   `json:"room_id"`
	MultiConnect *RankSwitchMultiConnect `json:"multi_connect"`
}

// NewRankSwitchBroadcastPayload 创建开关榜单广播元素
func NewRankSwitchBroadcastPayload(event string, groupID int64, members []*GroupMember) []*userapi.BroadcastElem {
	multiConnect := &RankSwitchMultiConnect{
		GroupID: groupID,
	}
	elems := make([]*userapi.BroadcastElem, 0, len(members))
	for _, member := range members {
		elems = append(elems, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: member.RoomID,
			Payload: &RankSwitchBroadcastPayload{
				Type:         liveim.TypeMultiConnect,
				Event:        event,
				RoomID:       member.RoomID,
				MultiConnect: multiConnect,
			},
		})
	}
	return elems
}
