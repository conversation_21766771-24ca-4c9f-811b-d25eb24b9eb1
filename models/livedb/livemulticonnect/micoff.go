package livemulticonnect

import goutil "github.com/MiaoSiLa/missevan-go/util"

// GroupMicOff 主播连线闭麦表
type GroupMicOff struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	GroupID       int64 `gorm:"column:group_id"`        // 连线组 ID
	GroupMemberID int64 `gorm:"column:group_member_id"` // 主播连线成员 ID
}

// TableName .
func (GroupMicOff) TableName() string {
	return "live_multi_connect_group_mic_off"
}

// BeforeCreate .
func (micoff *GroupMicOff) BeforeCreate() (err error) {
	nowUnix := goutil.TimeNow().Unix()
	micoff.CreateTime = nowUnix
	micoff.ModifiedTime = nowUnix
	return
}
