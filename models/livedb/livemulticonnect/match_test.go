package livemulticonnect

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestConsts_Match(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, MatchTypeApply)
	assert.Equal(2, MatchTypeInvite)

	assert.Equal(1, MatchStatusPending)
	assert.Equal(2, MatchStatusAccept)
	assert.Equal(3, MatchStatusCancel)
	assert.Equal(4, MatchStatusRefuse)
	assert.Equal(5, MatchStatusTimeout)
}

func TestTags_Match(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Match{}, "id", "create_time", "modified_time", "start_time", "end_time", "type", "status", "group_id", "from_room_id", "to_room_id", "bridge_room_id")
}

func TestFindMatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		matchID = int64(100)
	)
	err := DB().Delete(Match{}, "id = ?", matchID).Error
	assert.NoError(err)

	m, err := FindMatch(matchID)
	require.NoError(err)
	assert.Nil(m)

	m = &Match{
		ID: matchID,
	}
	err = DB().Create(m).Error
	assert.NoError(err)
	m, err = FindMatch(matchID)
	require.NoError(err)
	assert.NotNil(m)
}

func TestMatch_Accept(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := &Match{
		ID:     100,
		Status: MatchStatusPending,
	}
	require.NoError(DB().Delete(Match{}, "id = ?", m.ID).Error)
	require.NoError(DB().Create(m).Error)

	ok, err := m.Accept(DB())
	require.NoError(err)
	assert.True(ok)
	match, err := FindMatch(m.ID)
	require.NoError(err)
	require.NotNil(match)
	assert.Equal(MatchStatusAccept, match.Status)

	ok, err = m.Accept(DB())
	require.NoError(err)
	assert.False(ok)
}

func TestMatch_Cancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := &Match{
		ID:     100,
		Status: MatchStatusPending,
	}
	require.NoError(DB().Delete(Match{}, "id = ?", m.ID).Error)
	require.NoError(DB().Create(m).Error)

	ok, err := m.Cancel()
	require.NoError(err)
	assert.True(ok)
	match, err := FindMatch(m.ID)
	require.NoError(err)
	require.NotNil(match)
	assert.Equal(MatchStatusCancel, match.Status)

	ok, err = m.Cancel()
	require.NoError(err)
	assert.False(ok)
}

func TestMatch_Refuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := &Match{
		ID:     100,
		Status: MatchStatusPending,
	}
	require.NoError(DB().Delete(Match{}, "id = ?", m.ID).Error)
	require.NoError(DB().Create(m).Error)

	ok, err := m.Refuse()
	require.NoError(err)
	assert.True(ok)
	match, err := FindMatch(m.ID)
	require.NoError(err)
	require.NotNil(match)
	assert.Equal(MatchStatusRefuse, match.Status)

	ok, err = m.Refuse()
	require.NoError(err)
	assert.False(ok)
}

func TestMatch_Timeout(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := &Match{
		ID:     100,
		Status: MatchStatusPending,
	}
	require.NoError(DB().Delete(Match{}, "id = ?", m.ID).Error)
	require.NoError(DB().Create(m).Error)

	ok, err := m.Timeout()
	require.NoError(err)
	assert.True(ok)
	match, err := FindMatch(m.ID)
	require.NoError(err)
	require.NotNil(match)
	assert.Equal(MatchStatusTimeout, match.Status)

	ok, err = m.Timeout()
	require.NoError(err)
	assert.False(ok)
}

func TestExistsPendingMatch(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(12132)
	)
	err := DB().Delete(Match{}, "status = ?", MatchStatusPending).Error
	require.NoError(err)

	exists, err := ExistsPendingMatch(testRoomID)
	require.NoError(err)
	assert.False(exists)

	m := &Match{
		Status:     MatchStatusPending,
		FromRoomID: testRoomID,
	}
	err = DB().Create(m).Error
	require.NoError(err)
	exists, err = ExistsPendingMatch(testRoomID)
	require.NoError(err)
	assert.True(exists)
}

func TestFindPendingMatchesByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(112233)
	require.NoError(DB().Delete(Match{}, "from_room_id = ? OR to_room_id = ?", roomID, roomID).Error)

	matches := []Match{
		{Status: MatchStatusTimeout, FromRoomID: roomID, ToRoomID: 99},
		{Status: MatchStatusPending, FromRoomID: roomID, ToRoomID: 100},
		{Status: MatchStatusPending, FromRoomID: 101, ToRoomID: roomID},
	}
	require.NoError(servicedb.BatchInsert(DB(), Match{}.TableName(), matches))

	actualMatches, err := FindPendingMatchesByRoomID(roomID)
	require.NoError(err)
	assert.Len(actualMatches, 2)
}

func TestFindMatchingRoomIDMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(112233)
	require.NoError(DB().Delete(Match{}, "from_room_id = ? OR to_room_id = ?", roomID, roomID).Error)

	matches := []Match{
		{Status: MatchStatusTimeout, FromRoomID: roomID, ToRoomID: 99},
		{Status: MatchStatusPending, Type: MatchTypeInvite, FromRoomID: roomID, ToRoomID: 100},
		{Status: MatchStatusPending, Type: MatchTypeApply, FromRoomID: roomID, ToRoomID: 101, BridgeRoomID: 102},
		{Status: MatchStatusPending, Type: MatchTypeInvite, FromRoomID: 103, ToRoomID: roomID},
	}
	err := servicedb.BatchInsert(DB(), Match{}.TableName(), matches)
	require.NoError(err)
	matchingRoomIDMap, err := FindMatchingRoomIDMap(roomID)
	require.NoError(err)
	assert.Len(matchingRoomIDMap, 3)
	assert.NotNil(matchingRoomIDMap[100])
	assert.NotNil(matchingRoomIDMap[102])
	assert.NotNil(matchingRoomIDMap[103])
}

func TestFindPendingMatches(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(&Match{}).Error
	require.NoError(err)

	err = servicedb.BatchInsert(DB(), Match{}.TableName(), []Match{
		{FromRoomID: 123, Type: MatchTypeApply, Status: MatchStatusPending},
		{FromRoomID: 456, ToRoomID: 123, Type: MatchTypeApply, Status: MatchStatusPending},
		{FromRoomID: 456, Type: MatchTypeApply, Status: MatchStatusTimeout},
	})
	require.NoError(err)

	matchList, err := FindPendingMatches([]int64{123, 456})
	require.NoError(err)
	assert.Equal(2, len(matchList))
}

func TestMatch_InitGroup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testChannelID = int64(123445677)
	)

	m := &Match{
		StartTime:  goutil.TimeNow().UnixMilli(),
		Type:       MatchTypeInvite,
		Status:     MatchStatusPending,
		FromRoomID: 123,
		ToRoomID:   234,
	}
	err := DB().Create(&m).Error
	require.NoError(err)

	bililive.SetMockResult(bililive.ActionCreateChannel, bililive.Channel{ChannelID: testChannelID})
	group, err := m.InitGroup()
	require.NoError(err)
	require.NotNil(group)
	assert.Equal(strconv.FormatInt(testChannelID, 10), group.ConnectID)
	members, err := group.Members(nil)
	require.NoError(err)
	require.Len(members, 2)
	assert.Equal(m.FromRoomID, members[0].RoomID)
	assert.Equal(m.ToRoomID, members[1].RoomID)
	match, err := FindMatch(m.ID)
	require.NoError(err)
	require.NotNil(match)
	assert.Equal(MatchStatusAccept, match.Status)
	assert.NotEmpty(match.EndTime)

	group, err = m.InitGroup()
	assert.EqualError(err, "当前匹配状态异常")
	assert.Nil(group)
}

func TestMatch_JoinGroup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		now = goutil.TimeNow()
	)

	m := &Match{
		StartTime:  now.UnixMilli(),
		Type:       MatchTypeInvite,
		Status:     MatchStatusPending,
		FromRoomID: 123,
		ToRoomID:   234,
	}
	require.NoError(DB().Create(&m).Error)
	g := &Group{
		StartTime: now.UnixMilli(),
		ConnectID: "11111111",
	}
	require.NoError(DB().Create(&g).Error)
	members := []*GroupMember{
		{GroupID: g.ID, Role: MemberRoleOwner, Status: MemberStatusOngoing, RoomID: 1},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 2},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 3},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 4},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 5},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 6},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 7},
		{GroupID: g.ID, Role: MemberRoleMember, Status: MemberStatusOngoing, RoomID: 8},
	}
	require.NoError(servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members))

	require.NoError(m.JoinGroup(g))
	members, err := g.Members(nil)
	require.NoError(err)
	require.Len(members, 9)

	m = &Match{
		StartTime:  now.UnixMilli(),
		Type:       MatchTypeInvite,
		Status:     MatchStatusPending,
		FromRoomID: 456,
		ToRoomID:   789,
	}
	require.NoError(DB().Create(&m).Error)
	err = m.JoinGroup(g)
	assert.EqualError(err, "连线人数已达上限")
}

func TestBatchCancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("不存在待更新的记录", func(t *testing.T) {
		require.NoError(DB().Delete(Match{}).Error)
		err := BatchCancel([]*Match{
			{ID: 1, FromRoomID: 100, ToRoomID: 101, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
		})
		require.NoError(err)
		var matches []*Match
		require.NoError(DB().Where("Status = ?", MatchStatusCancel).Find(&matches).Error)
		assert.Len(matches, 0)
	})
	t.Run("存在匹配记录", func(t *testing.T) {
		matchRecords := []*Match{
			{ID: 1, FromRoomID: 100, ToRoomID: 101, GroupID: 100, Type: MatchTypeInvite, Status: MatchStatusPending},
			{ID: 2, FromRoomID: 101, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 3, FromRoomID: 102, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 4, FromRoomID: 103, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 5, FromRoomID: 104, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 6, FromRoomID: 105, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 7, FromRoomID: 106, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 8, FromRoomID: 107, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 9, FromRoomID: 108, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 10, FromRoomID: 109, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
			{ID: 11, FromRoomID: 110, ToRoomID: 100, GroupID: 100, Type: MatchTypeApply, Status: MatchStatusPending},
		}
		require.NoError(DB().Delete(Match{}).Error)
		require.NoError(servicedb.BatchInsert(DB(), Match{}.TableName(), matchRecords))
		err := BatchCancel(matchRecords)
		require.NoError(err)
		var matches []*Match
		require.NoError(DB().Where("Status = ?", MatchStatusCancel).Find(&matches).Error)
		assert.Len(matches, len(matchRecords))
	})
}
