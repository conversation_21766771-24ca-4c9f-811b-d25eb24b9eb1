package livemulticonnect

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/userapi"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func TestGetUserRankGranted(t *testing.T) {
	assert := assert.New(t)

	tests := []struct {
		name     string
		mc       params.MultiConnect
		roomID   int64
		expected int
	}{
		{
			name:     "不在白名单内",
			mc:       params.MultiConnect{BetaRoomIDs: []int64{}},
			roomID:   123,
			expected: UserRankNotGranted,
		},
		{
			name:     "在白名单内",
			mc:       params.MultiConnect{BetaRoomIDs: []int64{456}},
			roomID:   456,
			expected: UserRankGranted,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(tt.expected, GetUserRankGranted(tt.mc, tt.roomID))
		})
	}
}

func TestGetUserRankEnabled(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGroupID := int64(123)
	testKey := keys.KeyMultiConnectGroupUserRankEnable1.Format(testGroupID)
	require.NoError(service.Redis.Del(testKey).Err())

	// 不存在 key 时返回 UserRankDisabled
	res := GetUserRankEnabled(testGroupID) // 确保 Redis 中没有该 key
	assert.Equal(UserRankDisabled, res)

	// 设置 key 为 0 时返回 UserRankDisabled
	require.NoError(service.Redis.Set(testKey, 0, time.Second*10).Err())
	res = GetUserRankEnabled(testGroupID)
	assert.Equal(UserRankDisabled, res)

	// 设置 key 为 1 时返回 UserRankEnabled
	require.NoError(service.Redis.Set(testKey, 1, time.Second*10).Err())
	res = GetUserRankEnabled(testGroupID)
	assert.Equal(UserRankEnabled, res)
}

func TestSwitchUserRankStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGroupID := int64(456)
	testKey := keys.KeyMultiConnectGroupUserRankEnable1.Format(testGroupID)
	require.NoError(service.Redis.Del(testKey).Err())

	// 切换为开启状态
	err := SwitchUserRankStatus(testGroupID, true)
	require.NoError(err)
	res, err := service.Redis.Get(testKey).Int()
	require.NoError(err)
	assert.Equal(1, res)
	// 切换为关闭状态
	err = SwitchUserRankStatus(testGroupID, false)
	require.NoError(err)
	res, err = service.Redis.Get(testKey).Int()
	require.True(serviceredis.IsRedisNil(err))
	assert.Zero(res)
}

func TestNewUserRankScoreCollector(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("参数非法", func(t *testing.T) {
		_, err := newUserRankScoreCollector(UserRankScoreSourceGift, 0, 1, 10)
		assert.Error(err)
		_, err = newUserRankScoreCollector(UserRankScoreSourceGift, 1, 0, 10)
		assert.Error(err)
		_, err = newUserRankScoreCollector(UserRankScoreSourceGift, 1, 1, 0)
		assert.Error(err)
	})

	t.Run("礼物积分收集器", func(t *testing.T) {
		collector, err := newUserRankScoreCollector(UserRankScoreSourceGift, 1, 2, 10)
		require.NoError(err)
		assert.NotNil(collector)
	})

	t.Run("收听积分收集器", func(t *testing.T) {
		collector, err := newUserRankScoreCollector(UserRankScoreSourceListen, 1, 2, 10)
		require.NoError(err)
		assert.NotNil(collector)
	})

	t.Run("不支持的类型", func(t *testing.T) {
		collector, err := newUserRankScoreCollector(UserRankScoreSource(100), 1, 2, 10)
		assert.NoError(err)
		assert.Nil(collector)
	})
}

func TestAddUserRankScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("不支持的类型", func(t *testing.T) {
		err := AddUserRankScore(UserRankScoreSource(100), 1, 2, 10)
		require.Error(err)
		assert.EqualError(err, "unsupported score source: 100")
	})

	t.Run("参数非法", func(t *testing.T) {
		err := AddUserRankScore(UserRankScoreSourceGift, 0, 2, 10)
		assert.Error(err)
		err = AddUserRankScore(UserRankScoreSourceGift, 1, 0, 10)
		assert.Error(err)
		err = AddUserRankScore(UserRankScoreSourceGift, 1, 2, 0)
		assert.Error(err)
	})
}

func TestUserRankGiftScoreCollector_addScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(1)
	userID := int64(2)
	score := int64(10)

	redisKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)
	err := service.Redis.Del(redisKey).Err()
	require.NoError(err)
	c := &UserRankGiftScoreCollector{groupID: groupID, userID: userID, score: score}
	err = c.addScore()
	assert.NoError(err)

	result, err := service.Redis.ZScore(redisKey, strconv.FormatInt(userID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(score, result)
}

func TestUserRankListenScoreCollector_addScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(1)
	userID := int64(2)
	score := int64(10)

	t.Run("首次加分", func(t *testing.T) {
		redisKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)
		err := service.Redis.Del(redisKey).Err()
		require.NoError(err)
		_, err = service.Redis.Del(keys.LockMultiConnectGroupUserListenScore2.Format(groupID, userID)).Result()
		require.NoError(err)

		c := &UserRankListenScoreCollector{groupID: groupID, userID: userID, score: score}
		err = c.addScore()
		assert.NoError(err)

		result, err := service.Redis.ZScore(redisKey, strconv.FormatInt(userID, 10)).Result()
		require.NoError(err)
		assert.EqualValues(score, result)
	})

	t.Run("非首次加分", func(t *testing.T) {
		redisKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)
		err := service.Redis.ZAdd(redisKey, &redis.Z{
			Score:  float64(score),
			Member: userID,
		}).Err()
		require.NoError(err)
		_, err = service.Redis.Set(keys.LockMultiConnectGroupUserListenScore2.Format(groupID, userID), score, time.Minute).Result()
		require.NoError(err)

		c := &UserRankListenScoreCollector{groupID: groupID, userID: userID, score: score}
		err = c.addScore()
		assert.NoError(err)

		result, err := service.Redis.ZScore(redisKey, strconv.FormatInt(userID, 10)).Result()
		require.NoError(err)
		assert.EqualValues(score, result)
	})
}

func TestGetUserRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(9999999201)
	userID := int64(9999999202)
	redisKey := keys.KeyMultiConnectGroupUserRankCache1.Format(groupID)

	g := &Group{
		ID: groupID,
	}

	t.Run("缓存命中直接返回", func(t *testing.T) {
		// 先写入缓存
		ranks := []*RankUserInfo{{UserID: userID, Score: 100}}
		err := storeRankInCache(groupID, ranks)
		require.NoError(err)

		got, err := GetUserRank(g)
		require.NoError(err)
		require.Len(got, 1)
		assert.Equal(userID, got[0].UserID)
	})

	t.Run("缓存未命中，生成榜单", func(t *testing.T) {
		// 清理缓存
		service.Redis.Del(redisKey)

		// 插入 group member
		roomID1 := int64(9999999203)
		roomID2 := int64(9999999204)
		members := []*GroupMember{
			{
				ID:      100,
				GroupID: groupID,
				RoomID:  roomID1,
				Status:  MemberStatusOngoing,
			},
			{
				ID:      101,
				GroupID: groupID,
				RoomID:  roomID2,
				Status:  MemberStatusOngoing,
			},
		}
		err := servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
		require.NoError(err)

		// 插入 redis zset 榜单分数
		zsetKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)
		pipeline := service.Redis.Pipeline()
		pipeline.ZAdd(zsetKey, &redis.Z{
			Score:  100,
			Member: strconv.FormatInt(userID, 10),
		})
		liveserviceredis.Expire(pipeline, zsetKey, time.Minute)
		_, err = pipeline.Exec()
		require.NoError(err)

		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		// 插入用户资料
		user := &liveuser.Simple{
			UID:          userID,
			Username:     "testuser",
			IconURL:      "icon",
			Titles:       nil,
			Contribution: 10,
		}
		filter := bson.M{"user_id": user.UID}
		update := bson.M{"$set": user}
		opts := options.Update().SetUpsert(true)
		_, err = liveuser.Collection().UpdateOne(ctx, filter, update, opts)
		require.NoError(err)

		// IMRoomsList mock
		cancel = mrpc.SetMock(userapi.URIIMRoomsList, func(input any) (output any, err error) {
			return userapi.IMRoomsListResp{
				Rooms: map[int64]userapi.IMRoomInfo{
					roomID1: {UserIDs: []int64{userID}},
				},
			}, nil
		})
		defer cancel()

		got, err := GetUserRank(g)
		require.NoError(err)
		require.Len(got, 1)
		assert.Equal(userID, got[0].UserID)
		assert.Equal("testuser", got[0].Username)
		assert.Equal(int64(100), got[0].Score)
		assert.Equal(int64(10), got[0].contribution)
	})

	t.Run("加锁失败，直接报错", func(t *testing.T) {
		// 清理缓存
		lockKey := keys.LockMultiConnectGroupUserRankCacheUpdate1.Format(groupID)
		service.Redis.Del(redisKey)
		// 模拟锁被占用
		service.Redis.Set(lockKey, "1", 10*time.Second)
		defer service.Redis.Del(lockKey)

		got, err := GetUserRank(g)
		assert.Error(err)
		assert.Nil(got)
	})
}

func TestBuildUserRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(9999999301)

	roomID1 := int64(99991314131401)
	roomID2 := int64(99991314131402)

	userID1 := int64(9999131413140101)
	userID2 := int64(9999131413140102)
	userID3 := int64(9999131413140103)
	userID4 := int64(9999131413140104)

	// 准备测试数据
	members := []*GroupMember{
		{
			ID:      100,
			GroupID: groupID,
			RoomID:  roomID1,
			Status:  MemberStatusOngoing,
		},
		{
			ID:      101,
			GroupID: groupID,
			RoomID:  roomID2,
			Status:  MemberStatusOngoing,
		},
	}
	err := service.LiveDB.Delete(GroupMember{}, "id IN (?)", []int64{members[0].ID, members[1].ID}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), GroupMember{}.TableName(), members)
	require.NoError(err)

	// 清理并插入 redis zset 榜单分数
	pipeline := service.Redis.Pipeline()
	redisKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)
	pipeline.Del(redisKey)
	pipeline.ZAdd(redisKey, &redis.Z{
		Score:  100,
		Member: strconv.FormatInt(userID1, 10),
	}, &redis.Z{
		Score:  90,
		Member: strconv.FormatInt(userID2, 10),
	}, &redis.Z{
		Score:  80,
		Member: strconv.FormatInt(userID3, 10),
	}, &redis.Z{
		Score:  80,
		Member: strconv.FormatInt(userID4, 10),
	})
	liveserviceredis.Expire(pipeline, redisKey, time.Minute)
	_, err = pipeline.Exec()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 插入用户资料
	testUser := []liveuser.Simple{
		{
			UID:          userID1,
			Username:     "testuser1",
			IconURL:      "icon1",
			Titles:       nil,
			Contribution: 10,
		},
		{
			UID:          userID2,
			Username:     "testuser2",
			IconURL:      "icon2",
			Titles:       nil,
			Contribution: 10,
		},
		{
			UID:          userID3,
			Username:     "testuser3",
			IconURL:      "icon3",
			Titles:       nil,
			Contribution: 10,
		},
		{
			UID:          userID4,
			Username:     "testuser4",
			IconURL:      "icon4",
			Titles:       nil,
			Contribution: 20,
		},
	}
	for _, user := range testUser {
		filter := bson.M{"user_id": user.UID}
		update := bson.M{"$set": user}
		opts := options.Update().SetUpsert(true)
		_, err := liveuser.Collection().UpdateOne(ctx, filter, update, opts)
		require.NoError(err)
	}

	// IMRoomsList mock
	cancelIM := mrpc.SetMock(userapi.URIIMRoomsList, func(input any) (output any, err error) {
		return userapi.IMRoomsListResp{
			Rooms: map[int64]userapi.IMRoomInfo{
				roomID1: {UserIDs: []int64{userID1, userID2, userID3}},
				roomID2: {UserIDs: []int64{userID3, userID4}},
			},
		}, nil
	})
	defer cancelIM()

	// 创建 Group 实例
	g := &Group{
		ID: groupID,
	}
	// 调用 buildUserRank
	ranks, err := buildUserRank(g)
	require.NoError(err)
	require.Len(ranks, 4)
	assert.Equal(ranks[0].UserID, userID1)
	assert.Equal(ranks[1].UserID, userID2)
	assert.Equal(ranks[2].UserID, userID4)
	assert.Equal(ranks[3].UserID, userID3)

	assert.ElementsMatch(ranks[0].RoomIDs, []int64{roomID1})
	assert.ElementsMatch(ranks[1].RoomIDs, []int64{roomID1})
	assert.ElementsMatch(ranks[2].RoomIDs, []int64{roomID2})
	assert.ElementsMatch(ranks[3].RoomIDs, []int64{roomID1, roomID2})
}

func TestGatherCandidateUserMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(9999131313101)
	userID0 := int64(888131313100)
	userID1 := int64(888131313101)
	userID2 := int64(888131313102)
	userID3 := int64(888131313103)
	userID4 := int64(888131313104)
	userID5 := int64(888131313105)
	userID6 := int64(888131313106)
	userID7 := int64(888131313107)
	userID8 := int64(888131313108)
	userID9 := int64(888131313109)

	zsetKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)

	t.Run("全部在线且可见，正常返回", func(t *testing.T) {
		pipeline := service.Redis.Pipeline()
		// 清理并插入 redis zset 榜单分数
		pipeline.Del(zsetKey)
		pipeline.ZAdd(zsetKey, &redis.Z{
			Score:  100,
			Member: strconv.FormatInt(userID0, 10),
		}, &redis.Z{
			Score:  90,
			Member: strconv.FormatInt(userID1, 10),
		})
		liveserviceredis.Expire(pipeline, zsetKey, time.Minute)
		_, err := pipeline.Exec()
		require.NoError(err)

		onlineUserRoomsMap := map[int64][]int64{
			userID0: {1},
			userID1: {1},
		}

		// 保证 usermeta 不隐身
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		_, err = usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{userID0, userID1}}})
		require.NoError(err)

		candidateMap, err := gatherCandidateUserMap(groupID, onlineUserRoomsMap, 110)
		require.NoError(err)
		require.Len(candidateMap, 2)
		assert.Equal(int64(100), candidateMap[userID0])
		assert.Equal(int64(90), candidateMap[userID1])
	})

	t.Run("部分用户不在线", func(t *testing.T) {
		pipeline := service.Redis.Pipeline()
		pipeline.Del(zsetKey)
		pipeline.ZAdd(zsetKey, &redis.Z{
			Score:  100,
			Member: strconv.FormatInt(userID0, 10),
		}, &redis.Z{
			Score:  90,
			Member: strconv.FormatInt(userID1, 10),
		})
		liveserviceredis.Expire(pipeline, zsetKey, time.Minute)
		_, err := pipeline.Exec()
		require.NoError(err)

		onlineUserRoomsMap := map[int64][]int64{
			userID0: {1},
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		_, err = usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{userID0, userID1}}})
		require.NoError(err)

		candidateMap, err := gatherCandidateUserMap(groupID, onlineUserRoomsMap, 2)
		require.NoError(err)
		require.Len(candidateMap, 1)
		assert.Equal(int64(100), candidateMap[userID0])
	})

	t.Run("部分用户隐身", func(t *testing.T) {
		pipeline := service.Redis.Pipeline()
		pipeline.Del(zsetKey)
		pipeline.ZAdd(zsetKey, &redis.Z{
			Score:  100,
			Member: strconv.FormatInt(userID0, 10),
		}, &redis.Z{
			Score:  90,
			Member: strconv.FormatInt(userID1, 10),
		})
		liveserviceredis.Expire(pipeline, zsetKey, time.Minute)
		_, err := pipeline.Exec()
		require.NoError(err)

		onlineUserRoomsMap := map[int64][]int64{
			userID0: {1},
			userID1: {1},
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		_, err = usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{userID0, userID1}}})
		require.NoError(err)
		_, err = usermeta.Collection().UpdateOne(ctx, bson.M{"user_id": userID1}, bson.M{"$set": bson.M{"user_id": userID1, "invisible": true}}, options.Update().SetUpsert(true))
		require.NoError(err)

		candidateMap, err := gatherCandidateUserMap(groupID, onlineUserRoomsMap, 2)
		require.NoError(err)
		require.Len(candidateMap, 1)
		assert.Equal(int64(100), candidateMap[userID0])
	})

	t.Run("zset 为空", func(t *testing.T) {
		service.Redis.Del(zsetKey)
		onlineUserRoomsMap := map[int64][]int64{}
		candidateMap, err := gatherCandidateUserMap(groupID, onlineUserRoomsMap, 2)
		require.NoError(err)
		assert.Len(candidateMap, 0)
	})

	t.Run("批次拉取，直到数量满足", func(t *testing.T) {
		pipeline := service.Redis.Pipeline()
		pipeline.Del(zsetKey)
		pipeline.ZAdd(zsetKey, &redis.Z{
			Score:  100,
			Member: strconv.FormatInt(userID0, 10),
		}, &redis.Z{
			Score:  80,
			Member: strconv.FormatInt(userID1, 10),
		}, &redis.Z{
			Score:  90,
			Member: strconv.FormatInt(userID2, 10),
		}, &redis.Z{
			Score:  60,
			Member: strconv.FormatInt(userID3, 10),
		}, &redis.Z{
			Score:  50,
			Member: strconv.FormatInt(userID4, 10),
		}, &redis.Z{
			Score:  70,
			Member: strconv.FormatInt(userID5, 10),
		}, &redis.Z{
			Score:  20,
			Member: strconv.FormatInt(userID6, 10),
		}, &redis.Z{
			Score:  40,
			Member: strconv.FormatInt(userID7, 10),
		}, &redis.Z{
			Score:  10,
			Member: strconv.FormatInt(userID8, 10),
		}, &redis.Z{
			Score:  30,
			Member: strconv.FormatInt(userID9, 10),
		})
		liveserviceredis.Expire(pipeline, zsetKey, time.Minute)
		_, err := pipeline.Exec()
		require.NoError(err)

		onlineUserRoomsMap := map[int64][]int64{
			userID0: {1}, // 100
			userID1: {1}, // 80
			userID2: {1}, // 90
			userID3: {1}, // 60
			userID5: {1}, // 70
			userID7: {1}, // 40
			userID9: {1}, // 30
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		_, err = usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{userID0, userID1, userID2,
			userID3, userID4, userID5, userID6, userID7, userID8, userID9}}})
		require.NoError(err)

		candidateMap, err := gatherCandidateUserMap(groupID, onlineUserRoomsMap, 3)
		require.NoError(err)
		require.Len(candidateMap, 3)
		assert.Equal(int64(100), candidateMap[userID0])
		assert.Equal(int64(90), candidateMap[userID2])
		assert.Equal(int64(80), candidateMap[userID1])

		candidateMap, err = gatherCandidateUserMap(groupID, onlineUserRoomsMap, 5)
		require.NoError(err)
		require.Len(candidateMap, 5)
		assert.Equal(int64(100), candidateMap[userID0])
		assert.Equal(int64(90), candidateMap[userID2])
		assert.Equal(int64(80), candidateMap[userID1])
		assert.Equal(int64(70), candidateMap[userID5])
		assert.Equal(int64(60), candidateMap[userID3])
	})
}

func TestGetRoomOnlineUsersMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// mock userapi.IMRoomsList
	cancel := mrpc.SetMock(userapi.URIIMRoomsList, func(input any) (output any, err error) {
		return userapi.IMRoomsListResp{
			Rooms: map[int64]userapi.IMRoomInfo{
				1: {UserIDs: []int64{1, 2}},
				2: {UserIDs: []int64{2, 3}},
				3: {UserIDs: []int64{1, 3}},
			},
		}, nil
	})
	defer cancel()

	online, err := getRoomOnlineUsersMap([]int64{1, 2, 3})
	require.NoError(err)
	assert.Len(online, 3)
	assert.ElementsMatch(online[1], []int64{1, 2})
	assert.ElementsMatch(online[2], []int64{2, 3})
	assert.ElementsMatch(online[3], []int64{1, 3})
}

func TestUserRoomsMap(t *testing.T) {
	assert := assert.New(t)

	t.Run("多房间多用户", func(t *testing.T) {
		imRooms := map[int64][]int64{
			1: {10, 20},
			2: {20, 30},
			3: {10, 30},
		}
		out := userRoomsMap(imRooms)
		assert.ElementsMatch(out[10], []int64{1, 3})
		assert.ElementsMatch(out[20], []int64{1, 2})
		assert.ElementsMatch(out[30], []int64{2, 3})
	})

	t.Run("同一用户在多个房间", func(t *testing.T) {
		imRooms := map[int64][]int64{
			1: {100},
			2: {100},
		}
		out := userRoomsMap(imRooms)
		assert.ElementsMatch(out[100], []int64{1, 2})
	})

	t.Run("空房间", func(t *testing.T) {
		imRooms := map[int64][]int64{}
		out := userRoomsMap(imRooms)
		assert.Empty(out)
	})

	t.Run("房间无用户", func(t *testing.T) {
		imRooms := map[int64][]int64{
			1: {},
			2: {},
		}
		out := userRoomsMap(imRooms)
		assert.Empty(out)
	})

	t.Run("不存在的用户", func(t *testing.T) {
		imRooms := map[int64][]int64{}
		out := userRoomsMap(imRooms)
		assert.Empty(out[200])
	})
}

func TestSelectOnlineUsers(t *testing.T) {
	assert := assert.New(t)

	onlineUserRoomsMap := map[int64][]int64{3: {1}, 5: {1}, 1: {1}, 2: {2}}
	userIDs := []int64{1, 2, 3, 4, 5}
	out := selectOnlineUsers(userIDs, onlineUserRoomsMap)
	assert.Equal([]int64{1, 2, 3, 5}, out)
}

func TestBatchGetUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID1 := int64(13131313130001)
	userID2 := int64(13131313130002)
	userID3 := int64(13131313130003)

	// 准备测试数据
	testUser := []liveuser.Simple{
		{
			UID: userID1,
		},
		{
			UID: userID2,
		},
		{
			UID: userID3,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	for _, user := range testUser {
		filter := bson.M{"user_id": user.UID}
		update := bson.M{"$set": user}
		opts := options.Update().SetUpsert(true)
		_, err := liveuser.Collection().UpdateOne(ctx, filter, update, opts)
		require.NoError(err)
	}

	out, err := batchGetUserInfo([]int64{userID1})
	require.NoError(err)
	require.Len(out, 1)
	assert.Equal(out[userID1].UserID(), userID1)

	out, err = batchGetUserInfo([]int64{userID1, userID2})
	require.NoError(err)
	require.Len(out, 2)
	assert.Equal(out[userID1].UserID(), userID1)
	assert.Equal(out[userID2].UserID(), userID2)
}

func TestGetRankFromCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// redis nil
	got, err := getRankFromCache(1313131)
	require.NoError(err)
	assert.Nil(got)

	// 正常返回
	ranks := []*RankUserInfo{{UserID: 1, Score: 100}}
	err = storeRankInCache(1313131, ranks)
	require.NoError(err)
	got, err = getRankFromCache(1313131)
	require.NoError(err)
	require.Len(got, 1)
	assert.Equal(ranks[0].UserID, got[0].UserID)
}

func TestStoreRankInCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userRank := []*RankUserInfo{{UserID: 1}}
	err := storeRankInCache(1313131, userRank)
	require.NoError(err)

	result, err := getRankFromCache(1313131)
	require.NoError(err)

	assert.Equal(len(userRank), len(result))
	assert.Equal(userRank[0].UserID, result[0].UserID)
}

func TestGetUserRankKey(t *testing.T) {
	assert := assert.New(t)
	key := getUserRankKey(111)
	assert.Equal(keys.KeyMultiConnectGroupUserRankCache1.Format(111), key)
}
