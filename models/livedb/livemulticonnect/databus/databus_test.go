package databus

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestDatabusTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(timeoutMessage{}, "match_id")
}

func TestDelayMultiConnectMatchTimeout(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	match := &livemulticonnect.Match{ID: 100}
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	DelayMultiConnectMatchTimeout(match)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyMultiConnectMatchTimeout1.Format(match.ID), m.Key)

	var message timeoutMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.Equal(match.ID, message.MatchID)
}

func TestDelayMultiConnectMatchTimeoutOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
		count++
		return "success", nil
	})
	defer cancel()

	rooms, err := room.List(bson.M{}, options.Find().SetLimit(2))
	require.NoError(err)
	require.Len(rooms, 2)

	matchRecords := []livemulticonnect.Match{
		{ID: 1, FromRoomID: rooms[0].RoomID, ToRoomID: rooms[1].RoomID, Type: livemulticonnect.MatchTypeInvite, Status: livemulticonnect.MatchStatusPending},
		{ID: 2, FromRoomID: rooms[0].RoomID, ToRoomID: rooms[1].RoomID, Type: livemulticonnect.MatchTypeApply, Status: livemulticonnect.MatchStatusPending},
	}
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.Match{}.TableName(), matchRecords))

	f := DelayMultiConnectMatchTimeoutOperator()
	assert.NotPanics(func() {
		f(&databus.Message{})
	})

	// 不存在的匹配记录
	msg := timeoutMessage{
		MatchID: int64(0),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyMultiConnectMatchTimeout1.Format(msg.MatchID),
		Value: json.RawMessage(tutil.SprintJSON(msg)),
	})
	assert.Equal(0, count)

	// 邀请记录
	msg = timeoutMessage{
		MatchID: int64(1),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyMultiConnectMatchTimeout1.Format(msg.MatchID),
		Value: json.RawMessage(tutil.SprintJSON(msg)),
	})
	assert.Equal(1, count)

	// 申请记录
	msg = timeoutMessage{
		MatchID: int64(2),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyMultiConnectMatchTimeout1.Format(msg.MatchID),
		Value: json.RawMessage(tutil.SprintJSON(msg)),
	})
	assert.Equal(2, count)
}
