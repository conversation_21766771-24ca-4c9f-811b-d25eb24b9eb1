package databus

import (
	"encoding/json"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MatchTimeoutDuration 匹配超时时间
const (
	MatchTimeoutDuration = 10 * time.Second
)

// timeoutMessage 发起主播连线邀请/申请时发送的延时消息，超时取消匹配
type timeoutMessage struct {
	MatchID int64 `json:"match_id"` // 连线匹配 ID
}

// Timeout 更新匹配状态并发送通知
func (msg *timeoutMessage) Timeout() error {
	match, err := livemulticonnect.FindMatch(msg.MatchID)
	if err != nil {
		return err
	}
	if match == nil {
		return errors.New("match not found")
	}
	if !match.IsPending() {
		return nil
	}

	var event string
	switch match.Type {
	case livemulticonnect.MatchTypeApply:
		event = liveim.EventApplyTimeout
	case livemulticonnect.MatchTypeInvite:
		event = liveim.EventInviteTimeout
	default:
		return errors.New("invalid type")
	}

	success, err := match.Timeout()
	if err != nil {
		return err
	}
	if !success {
		return nil
	}

	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": []int64{match.FromRoomID, match.ToRoomID}}}, nil)
	if err != nil {
		return err
	}
	if len(rooms) != 2 {
		return errors.New("room not found")
	}
	roomMap := util.ToMap(rooms, func(room *room.Room) int64 {
		return room.RoomID
	})

	elems := livemulticonnect.NewUnacceptBroadcastPayloadElems(event, match, roomMap)
	err = userapi.BroadcastMany(elems)
	if err != nil {
		return err
	}
	return nil
}

// DelayMultiConnectMatchTimeout 主播连线发起邀请/申请时发送延时消息，超时取消匹配
func DelayMultiConnectMatchTimeout(match *livemulticonnect.Match) {
	key := keys.DelayKeyMultiConnectMatchTimeout1.Format(match.ID)
	err := service.DatabusSendDelay(key, timeoutMessage{
		MatchID: match.ID,
	}, goutil.TimeNow().Add(MatchTimeoutDuration))
	if err != nil {
		logger.WithFields(logger.Fields{
			"match_id": match.ID,
		}).Errorf("failed to send multi_connect timeout Message: %v", err)
		// PASS
	}
}

// DelayMultiConnectMatchTimeoutOperator 主播连线邀请/申请超时-消费者
func DelayMultiConnectMatchTimeoutOperator() func(*databus.Message) {
	return func(message *databus.Message) {
		if !keys.DelayKeyMultiConnectMatchTimeout1.MatchKey(message.Key) {
			return
		}

		var msg timeoutMessage
		err := json.Unmarshal(message.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}

		err = msg.Timeout()
		if err != nil {
			logger.WithFields(logger.Fields{
				"match_id": msg.MatchID,
			}).Error(err)
			// PASS
		}
	}
}
