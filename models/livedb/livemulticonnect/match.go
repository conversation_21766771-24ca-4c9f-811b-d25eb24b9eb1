package livemulticonnect

import (
	"errors"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/keys"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MatchType 匹配类型
const (
	MatchTypeApply  = iota + 1 // 申请
	MatchTypeInvite            // 邀请
)

// MatchStatus 匹配状态
const (
	MatchStatusPending = iota + 1 // 未处理
	MatchStatusAccept             // 已同意
	MatchStatusCancel             // 取消
	MatchStatusRefuse             // 已拒绝
	MatchStatusTimeout            // 超时
)

var (
	// ErrConnectIsLimit 连线上限 error
	ErrConnectIsLimit = errors.New("连线人数已达上限")
	// ErrConnectConcurrency 连线并发 error
	ErrConnectConcurrency = errors.New("当前连线组繁忙，请稍后再试")
)

// Match 主播连线匹配表
type Match struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	StartTime    int64 `gorm:"column:start_time"` // 匹配开始时间，单位：毫秒
	EndTime      int64 `gorm:"column:end_time"`   // 匹配结束时间，单位：毫秒
	Type         int   `gorm:"column:type"`
	Status       int   `gorm:"column:status"`
	GroupID      int64 `gorm:"column:group_id"`
	FromRoomID   int64 `gorm:"column:from_room_id"`
	ToRoomID     int64 `gorm:"column:to_room_id"`
	BridgeRoomID int64 `gorm:"column:bridge_room_id"` // 发起申请时中间主播的房间号
}

// TableName .
func (Match) TableName() string {
	return "live_multi_connect_match"
}

// BeforeCreate .
func (m *Match) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	m.CreateTime = nowUnix
	m.ModifiedTime = nowUnix
	return nil
}

// FindMatch 查询匹配记录
func FindMatch(matchID int64) (*Match, error) {
	var match Match
	err := DB().Where("id = ?", matchID).Take(&match).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &match, nil
}

// IsPending 是否未处理
func (m *Match) IsPending() bool {
	return m.Status == MatchStatusPending
}

// Accept 同意匹配
func (m *Match) Accept(db *gorm.DB) (bool, error) {
	if db == nil {
		db = DB()
	}
	db = db.Model(Match{}).
		Where("id = ? AND status = ?", m.ID, MatchStatusPending). // status 作为乐观锁防止并发
		Updates(map[string]any{
			"modified_time": goutil.TimeNow().Unix(),
			"end_time":      goutil.TimeNow().UnixMilli(),
			"status":        MatchStatusAccept,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// Cancel 取消匹配
func (m *Match) Cancel() (bool, error) {
	db := DB().Model(&Match{}).
		Where("id = ? AND status = ?", m.ID, MatchStatusPending). // status 作为乐观锁防止并发
		Updates(map[string]any{
			"modified_time": goutil.TimeNow().Unix(),
			"end_time":      goutil.TimeNow().UnixMilli(),
			"status":        MatchStatusCancel,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// Refuse 拒绝匹配
func (m *Match) Refuse() (bool, error) {
	db := DB().Model(&Match{}).
		Where("id = ? AND status = ?", m.ID, MatchStatusPending). // status 作为乐观锁防止并发
		Updates(map[string]any{
			"modified_time": goutil.TimeNow().Unix(),
			"end_time":      goutil.TimeNow().UnixMilli(),
			"status":        MatchStatusRefuse,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// Timeout 超时
func (m *Match) Timeout() (bool, error) {
	db := DB().Model(&Match{}).
		Where("id = ? AND status = ?", m.ID, MatchStatusPending). // status 作为乐观锁防止并发
		Updates(map[string]any{
			"modified_time": goutil.TimeNow().Unix(),
			"end_time":      goutil.TimeNow().UnixMilli(),
			"status":        MatchStatusTimeout,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected > 0, nil
}

// ExistsPendingMatch 查询是否存在未处理的匹配
func ExistsPendingMatch(roomID int64) (bool, error) {
	return servicedb.Exists(DB().Model(Match{}).Where("status = ? AND (from_room_id = ? OR to_room_id = ?)", MatchStatusPending, roomID, roomID))
}

// FindPendingMatchesByRoomID 查找房间当前所有进行中的的匹配记录
func FindPendingMatchesByRoomID(roomID int64) ([]*Match, error) {
	var matches []*Match
	err := DB().Where("status = ? AND (from_room_id = ? OR to_room_id = ?)", MatchStatusPending, roomID, roomID).Find(&matches).Error
	if err != nil {
		return nil, err
	}
	return matches, nil
}

// FindMatchingRoomIDMap 获取房间匹配中的房间和对应匹配记录的映射
func FindMatchingRoomIDMap(roomID int64) (map[int64]*Match, error) {
	matchRecords, err := FindPendingMatchesByRoomID(roomID)
	if err != nil {
		return nil, err
	}
	matchingRoomIDMap := make(map[int64]*Match, len(matchRecords))
	for _, match := range matchRecords {
		if match.FromRoomID == roomID {
			if match.Type == MatchTypeApply {
				matchingRoomIDMap[match.BridgeRoomID] = match
			} else {
				matchingRoomIDMap[match.ToRoomID] = match
			}
		}
		if match.ToRoomID == roomID {
			matchingRoomIDMap[match.FromRoomID] = match
		}
	}
	return matchingRoomIDMap, nil
}

// FindPendingMatches 查询未处理的匹配记录
func FindPendingMatches(roomIDs []int64) ([]*Match, error) {
	var matchList []*Match
	err := DB().Where("status = ? AND (from_room_id IN (?) OR to_room_id IN (?))",
		MatchStatusPending, roomIDs, roomIDs).
		Find(&matchList).Error
	if err != nil {
		return nil, err
	}
	return matchList, nil
}

// InitGroup 初始化连线组
// 当前仅支持接受连线邀请场景
func (m *Match) InitGroup() (*Group, error) {
	channel, err := service.BiliLive.CreateChannel(&bililive.CreateChannelRequestParams{
		BusinessLabel: bililive.BusinessLabelMultiConnect,
	})
	if err != nil {
		return nil, err
	}
	if channel == nil || channel.ChannelID == 0 {
		return nil, errors.New("获取连麦频道失败")
	}
	group := new(Group)
	err = servicedb.Tx(DB(), func(tx *gorm.DB) error {
		ok, err := m.Accept(tx)
		if err != nil {
			return err
		}
		if !ok {
			return errors.New("当前匹配状态异常")
		}
		group, err = CreateGroup(tx, channel.ChannelID)
		if err != nil {
			if servicedb.IsUniqueError(err) {
				return errors.New("已在主播连线中，无法操作")
			}
			return err
		}
		err = group.InitMembers(tx, m.FromRoomID, m.ToRoomID)
		if err != nil {
			return err
		}
		err = room.SetRoomMultiConnectStatus([]int64{m.FromRoomID, m.ToRoomID}, strconv.FormatInt(channel.ChannelID, 10))
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return group, nil
}

// JoinRoomID 根据 match 类型获取加入房间 ID
func (m *Match) JoinRoomID() int64 {
	switch m.Type {
	case MatchTypeApply:
		return m.FromRoomID
	case MatchTypeInvite:
		return m.ToRoomID
	default:
		panic("未知匹配类型")
	}
}

// JoinGroup 加入连线组
func (m *Match) JoinGroup(group *Group) error {
	key := keys.LockMultiConnectGroupMemberUpdate1.Format(group.ID)
	mutex := redismutex.New(service.Redis, key, 2*time.Second)
	if !mutex.TryLock() {
		return errors.New("当前连线组繁忙，请稍后再试")
	}
	defer mutex.Unlock()

	err := servicedb.Tx(DB(), func(tx *gorm.DB) error {
		count, err := group.OngoingMembersCount(tx)
		if err != nil {
			return err
		}
		// 达到连线人数限制，不修改匹配状态，等待 databus 任务处理
		if group.IsLimit(count) {
			return ErrConnectIsLimit
		}

		ok, err := m.Accept(tx)
		if err != nil {
			return err
		}
		if !ok {
			return errors.New("当前匹配状态异常")
		}
		_, err = group.JoinMember(tx, m.JoinRoomID())
		if err != nil {
			return nil
		}
		err = room.SetRoomMultiConnectStatus([]int64{m.JoinRoomID()}, group.ConnectID)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// BatchCancel 批量取消匹配
func BatchCancel(matches []*Match) error {
	ids := goutil.SliceMap(matches, func(m *Match) int64 {
		return m.ID
	})
	db := DB().Model(&Match{}).
		Where("id IN (?) AND status = ?", ids, MatchStatusPending). // status 作为乐观锁防止并发
		Updates(map[string]any{
			"modified_time": goutil.TimeNow().Unix(),
			"end_time":      goutil.TimeNow().UnixMilli(),
			"status":        MatchStatusCancel,
		})
	if db.Error != nil {
		return db.Error
	}
	return nil
}
