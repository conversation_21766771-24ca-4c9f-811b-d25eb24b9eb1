package livemulticonnect

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTags_Blocklist(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Blocklist{}, "id", "create_time", "modified_time", "room_id", "block_room_id")
}

func TestAddBlockAndRemoveBlock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID      = int64(1)
		testBlockRoomID = int64(2)
	)
	err := DB().Delete(Blocklist{}, "room_id = ?", testRoomID).Error
	require.NoError(err)

	err = AddBlock(testRoomID, testBlockRoomID)
	require.NoError(err)
	err = AddBlock(testBlockRoomID, testRoomID)
	require.NoError(err)
	ok1, ok2, err := IsBlocked(testRoomID, testBlockRoomID)
	require.NoError(err)
	assert.True(ok1)
	assert.True(ok2)

	err = RemoveBlock(testRoomID, testBlockRoomID)
	require.NoError(err)
	ok1, ok2, err = IsBlocked(testRoomID, testBlockRoomID)
	require.NoError(err)
	assert.False(ok1)
	assert.True(ok2)

	err = RemoveBlock(testBlockRoomID, testRoomID)
	require.NoError(err)
	ok1, ok2, err = IsBlocked(testRoomID, testBlockRoomID)
	require.NoError(err)
	assert.False(ok1)
	assert.False(ok2)
}

func TestBlockedRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID      = int64(1)
		testBlockRoomID = int64(2)
	)
	err := DB().Delete(Blocklist{}, "room_id = ?", testRoomID).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), Blocklist{}.TableName(), []*Blocklist{
		{
			RoomID:      testRoomID,
			BlockRoomID: testBlockRoomID,
		},
		{
			RoomID:      testRoomID,
			BlockRoomID: 3,
		},
		{
			RoomID:      testBlockRoomID,
			BlockRoomID: testRoomID,
		},
		{
			RoomID:      313901,
			BlockRoomID: 313902,
		},
	})
	require.NoError(err)

	blockedRoomIDs, blockCurrentRoomIDs, err := BlockedRoomIDs(testRoomID)
	require.NoError(err)
	require.Len(blockedRoomIDs, 2)
	assert.Contains(blockedRoomIDs, testBlockRoomID)
	assert.Contains(blockedRoomIDs, int64(3))
	require.Len(blockCurrentRoomIDs, 1)
	assert.Contains(blockCurrentRoomIDs, testBlockRoomID)

	blockedRoomIDs, blockCurrentRoomIDs, err = BlockedRoomIDs(testRoomID, testBlockRoomID)
	require.NoError(err)
	require.Len(blockedRoomIDs, 1)
	assert.Contains(blockedRoomIDs, testBlockRoomID)
	require.Len(blockCurrentRoomIDs, 1)
	assert.Contains(blockCurrentRoomIDs, testBlockRoomID)

	blockedRoomIDs, blockCurrentRoomIDs, err = BlockedRoomIDs(testBlockRoomID)
	require.NoError(err)
	require.Len(blockedRoomIDs, 1)
	assert.Contains(blockedRoomIDs, testRoomID)
	require.Len(blockCurrentRoomIDs, 1)
	assert.Contains(blockCurrentRoomIDs, testRoomID)

	blockedRoomIDs, blockCurrentRoomIDs, err = BlockedRoomIDs(131212)
	require.NoError(err)
	assert.Empty(blockedRoomIDs)
	assert.Empty(blockCurrentRoomIDs)
}
