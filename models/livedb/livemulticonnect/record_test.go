package livemulticonnect

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTags_Record(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Record{}, "id", "create_time", "modified_time", "group_id", "group_member_id",
		"group_owner_member_id", "room_id", "start_time", "end_time", "duration")
}

func TestRecord_FindMemberRecentRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	var roomID int64 = 100

	t.Run("参数异常", func(t *testing.T) {
		records, err := FindMemberRecentRecords(0, 20)
		require.NoError(err)
		assert.Empty(records)
	})

	t.Run("成功查询但不存在记录", func(t *testing.T) {
		err := DB().Delete(GroupMember{}).Error
		require.NoError(err)
		records, err := FindMemberRecentRecords(roomID, 20)
		require.NoError(err)
		assert.Empty(records)
	})

	t.Run("成功查询到多条记录", func(t *testing.T) {
		members := make([]*Record, 0, 25)
		for i := 0; i < 25; i++ {
			members = append(members, &Record{
				CreateTime:         goutil.TimeNow().Unix() + int64(100*i),
				ModifiedTime:       goutil.TimeNow().Unix() + int64(100*i),
				GroupID:            1,
				GroupMemberID:      100,
				GroupOwnerMemberID: int64(i + 100),
				RoomID:             roomID,
				StartTime:          goutil.TimeNow().UnixMilli() + int64(100*i), // 循环中，最晚遍历的 StartTime 最大
				EndTime:            goutil.TimeNow().UnixMilli() + int64(100*i) + 10,
				Duration:           10,
			})
		}
		require.NoError(servicedb.BatchInsert(DB(), Record{}.TableName(), members))
		records, err := FindMemberRecentRecords(roomID, 20)
		require.NoError(err)
		assert.Len(records, 20)

		records, err = FindMemberRecentRecords(roomID, 30)
		require.NoError(err)
		assert.Len(records, 25)
	})
}
