package livemulticonnect

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 用户是否有开启用户榜单的权限
const (
	UserRankNotGranted = iota
	UserRankGranted
)

// 用户榜单是否开启
const (
	UserRankDisabled = iota
	UserRankEnabled
)

// 收听积分相关常量
const (
	ListenScoreThresholdDuration = 30 * time.Second // 在连线组连续收听 30s 可获得收听积分

	ListenScore int64 = 10 // 在连线组收听可获得的积分
)

// 榜单相关常量
const (
	rankTopUsersLimit  = 100 // 榜单展示的最大用户数
	rankCandidateLimit = 110 // 只取积分前 110 名进行最终排序

	keyRankExpire30Days = 30 * 24 * time.Hour // 榜单缓存有效期 30 天
)

// UserRankScoreSource 积分来源枚举
type UserRankScoreSource int

// 积分来源枚举
const (
	UserRankScoreSourceGift   UserRankScoreSource = iota // 礼物积分
	UserRankScoreSourceListen                            // 收听积分
)

// GetUserRankGranted 直播间主播是否有开启用户榜单的权限，0 表示没有，1 表示有
func GetUserRankGranted(mc params.MultiConnect, roomID int64) int {
	if mc.IsBetaRoom(roomID) {
		return UserRankGranted
	}
	return UserRankNotGranted
}

// GetUserRankEnabled 检查连线组是否开启了用户榜单功能，0 表示未开启，1 表示已开启
func GetUserRankEnabled(groupID int64) int {
	key := keys.KeyMultiConnectGroupUserRankEnable1.Format(groupID)
	val, err := service.Redis.Get(key).Int()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.WithFields(logger.Fields{"group_id": groupID, "key": key}).Error(err)
		return UserRankDisabled
	}
	if val == 0 {
		return UserRankDisabled
	}
	return UserRankEnabled
}

// SwitchUserRankStatus 开启或关闭连线组用户榜单显示，true 表示开启，false 表示关闭
func SwitchUserRankStatus(groupID int64, rankOn bool) error {
	key := keys.KeyMultiConnectGroupUserRankEnable1.Format(groupID)
	if rankOn {
		expireDuration := time.Hour * 24 * 30 // 榜单状态保持 30 天的有效期
		return service.Redis.Set(key, 1, expireDuration).Err()
	}
	return service.Redis.Del(key).Err()
}

// UserRankScoreCollector 定义增加榜单积分接口
type UserRankScoreCollector interface {
	addScore() error
}

// UserRankGiftScoreCollector 礼物积分收集器
type UserRankGiftScoreCollector struct {
	groupID int64
	userID  int64
	score   int64
}

// AddScore 增加礼物积分
func (c *UserRankGiftScoreCollector) addScore() error {
	pipe := service.Redis.TxPipeline()

	userRankKey := keys.KeyMultiConnectGroupUserRank1.Format(c.groupID)
	pipe.ZIncrBy(userRankKey, float64(c.score), strconv.FormatInt(c.userID, 10))
	liveserviceredis.Expire(pipe, userRankKey, keyRankExpire30Days)

	_, err := pipe.Exec()
	if err != nil {
		return fmt.Errorf("pipe exec failed: %v", err)
	}
	return nil
}

// UserRankListenScoreCollector 收听积分收集器
type UserRankListenScoreCollector struct {
	groupID int64
	userID  int64
	score   int64
}

// AddScore 增加收听积分
func (c *UserRankListenScoreCollector) addScore() error {
	ok, err := service.Redis.SetNX(keys.LockMultiConnectGroupUserListenScore2.Format(c.groupID, c.userID),
		c.score, keyRankExpire30Days).Result()
	if err != nil {
		return fmt.Errorf("setnx listen score failed: %v", err)
	}
	if !ok {
		return nil
	}
	pipe := service.Redis.TxPipeline()
	userRankKey := keys.KeyMultiConnectGroupUserRank1.Format(c.groupID)
	pipe.ZIncrBy(userRankKey, float64(c.score), strconv.FormatInt(c.userID, 10))
	liveserviceredis.Expire(pipe, userRankKey, keyRankExpire30Days)
	_, err = pipe.Exec()
	if err != nil {
		return fmt.Errorf("redis pipe exec failed: %v", err)
	}
	return nil
}

// newUserRankScoreCollector 工厂方法获取用户榜单积分收集器
func newUserRankScoreCollector(source UserRankScoreSource, groupID, userID, score int64) (UserRankScoreCollector, error) {
	if groupID == 0 || userID == 0 || score <= 0 {
		return nil, errors.New("invalid params")
	}
	switch source {
	case UserRankScoreSourceGift:
		return &UserRankGiftScoreCollector{
			groupID: groupID,
			userID:  userID,
			score:   score,
		}, nil
	case UserRankScoreSourceListen:
		return &UserRankListenScoreCollector{
			groupID: groupID,
			userID:  userID,
			score:   score,
		}, nil
	default:
		return nil, nil
	}
}

// AddUserRankScore 增加用户榜单积分
func AddUserRankScore(source UserRankScoreSource, groupID, userID, score int64) error {
	collector, err := newUserRankScoreCollector(source, groupID, userID, score)
	if err != nil {
		return fmt.Errorf("newUserRankScoreCollector failed: %v", err)
	}
	if collector == nil {
		return fmt.Errorf("unsupported score source: %d", source)
	}
	err = collector.addScore()
	if err != nil {
		return fmt.Errorf("add user rank score failed: %v", err)
	}
	return nil
}

// RankUserInfo 用户榜单信息
type RankUserInfo struct {
	UserID   int64            `json:"user_id"`
	Username string           `json:"username"`
	IconURL  string           `json:"iconurl"`
	Titles   []liveuser.Title `json:"titles,omitempty"`

	Score   int64   `json:"score"`    // 用户积分
	RoomIDs []int64 `json:"room_ids"` // 用户所在直播间，用户可能同时在多个直播间

	contribution int64 // 等级经验值
}

// GetUserRank 获取连线组用户榜单
func GetUserRank(group *Group) ([]*RankUserInfo, error) {
	if group == nil || group.ID == 0 {
		return nil, errors.New("invalid params")
	}
	// 首先检查缓存
	ranks, err := getRankFromCache(group.ID)
	if err != nil {
		return nil, fmt.Errorf("getRankFromCache failed: %v", err)
	}
	if ranks != nil {
		return ranks, nil
	}

	// 如果缓存不存在，则尝试获取锁，防止并发生成榜单
	lockKey := keys.LockMultiConnectGroupUserRankCacheUpdate1.Format(group.ID)
	lock := redismutex.New(
		service.Redis,
		lockKey,
		10*time.Second,
		redismutex.WithTryLockDuration(200*time.Millisecond),
	)
	if !lock.TryLock() {
		return nil, errors.New("get lock failed")
	}
	defer lock.Unlock()

	// 获取到锁后再次检查缓存
	ranks, err = getRankFromCache(group.ID)
	if err != nil {
		return nil, fmt.Errorf("lock getRankFromCache failed: %v", err)
	}
	if ranks != nil {
		return ranks, nil
	}

	// 实时生成榜单
	ranks, err = buildUserRank(group)
	if err != nil {
		return nil, fmt.Errorf("buildUserRank failed: %v", err)
	}

	// 缓存榜单数据
	err = storeRankInCache(group.ID, ranks)
	if err != nil {
		logger.WithFields(logger.Fields{
			"group_id": group.ID,
		}).Error(err)
		// PASS
	}

	return ranks, nil
}

// buildUserRank 构建连线组用户榜单
func buildUserRank(group *Group) ([]*RankUserInfo, error) {
	members, err := group.Members(nil)
	if err != nil {
		return nil, fmt.Errorf("get group members failed: %v", err)
	}
	if len(members) == 0 {
		return nil, errors.New("no ongoing members")
	}
	roomIDs := goutil.SliceMap(members, func(v *GroupMember) int64 {
		return v.RoomID
	})
	// 获取直播间在线用户
	roomOnlineUsersMap, err := getRoomOnlineUsersMap(roomIDs)
	if err != nil {
		return nil, fmt.Errorf("getRoomOnlineUsersMap failed: %v", err)
	}
	if len(roomOnlineUsersMap) == 0 {
		return make([]*RankUserInfo, 0), nil
	}

	// 生成用户和直播间的映射关系
	onlineUserRoomsMap := userRoomsMap(roomOnlineUsersMap)

	// 收集榜单候选用户
	candidateUserMap, err := gatherCandidateUserMap(group.ID, onlineUserRoomsMap, rankCandidateLimit)
	if err != nil {
		return nil, fmt.Errorf("gatherCandidateUserMap failed: %v", err)
	}
	if len(candidateUserMap) == 0 {
		return make([]*RankUserInfo, 0), nil
	}

	// 批量获取用户信息
	userIDs := make([]int64, 0, len(candidateUserMap))
	for userID := range candidateUserMap {
		userIDs = append(userIDs, userID)
	}
	userInfoMap, err := batchGetUserInfo(userIDs)
	if err != nil {
		return nil, err
	}
	if len(userInfoMap) == 0 {
		return make([]*RankUserInfo, 0), nil
	}

	// 组装并排序榜单
	ranks := make([]*RankUserInfo, 0, len(candidateUserMap))
	for uid, score := range candidateUserMap {
		userInfo, ok := userInfoMap[uid]
		if !ok {
			continue
		}
		ranks = append(ranks, &RankUserInfo{
			UserID:       uid,
			Username:     userInfo.Username,
			IconURL:      userInfo.IconURL,
			Titles:       userInfo.Titles,
			Score:        score,
			RoomIDs:      util.Uniq(onlineUserRoomsMap[uid]),
			contribution: userInfo.Contribution,
		})
	}

	// 首先按积分降序排序，如果积分相同则按贡献值降序排序，兜底使用 user_id 排序
	sort.Slice(ranks, func(i, j int) bool {
		if ranks[i].Score != ranks[j].Score {
			return ranks[i].Score > ranks[j].Score
		}
		if ranks[i].contribution != ranks[j].contribution {
			return ranks[i].contribution > ranks[j].contribution
		}
		return ranks[i].UserID < ranks[j].UserID
	})

	// 截取前 100 名用户
	if len(ranks) > rankTopUsersLimit {
		ranks = ranks[:rankTopUsersLimit]
	}

	return ranks, nil
}

// gatherCandidateUserMap 收集榜单候选用户（从 Redis 排名中筛选在线且非进场隐身用户，返回用户 ID 及其分数）
// groupID: 连线组 ID
// onlineUserRoomsMap: 在线用户 ID 到直播间的映射
// rankCandidateLimit: 候选用户数量上限，指从 Redis 排名中筛选了在线且非进场隐身的用户，还需要进行最终排序
// 返回：候选用户 ID 到分数的映射，及错误信息
func gatherCandidateUserMap(groupID int64, onlineUserRoomsMap map[int64][]int64, rankCandidateLimit int) (map[int64]int64, error) {
	redisKey := keys.KeyMultiConnectGroupUserRank1.Format(groupID)
	total, err := service.Redis.ZCard(redisKey).Result()
	if err != nil {
		return nil, fmt.Errorf("redis zcard failed: %v", err)
	}
	if total == 0 {
		return nil, nil
	}

	// 一次性取出所有 zset 数据
	res, err := service.Redis.ZRevRangeWithScores(redisKey, 0, total-1).Result()
	if err != nil {
		return nil, fmt.Errorf("redis ZRevRangeWithScores failed: %v", err)
	}

	// 构建分数映射和用户列表
	scoreMap := make(map[int64]int64, len(res))
	userIDs := make([]int64, 0, len(res))
	for _, item := range res {
		uid, err := strconv.ParseInt(item.Member.(string), 10, 64)
		if err != nil {
			logger.WithFields(logger.Fields{
				"member":   item.Member,
				"score":    item.Score,
				"group_id": groupID,
			}).Error(err)
			// PASS
			continue
		}
		scoreMap[uid] = int64(item.Score)
		userIDs = append(userIDs, uid)
	}

	// 筛选在线用户
	onlineUserIDs := selectOnlineUsers(userIDs, onlineUserRoomsMap)
	if len(onlineUserIDs) == 0 {
		return nil, nil
	}

	// 排除进场隐身用户
	// 先过滤掉因角色导致进场隐身的用户
	roleInvisibleUserIDMap := liveim.GetInvisibleRoleUserIDMap(mrpc.NewUserContextFromEnv())
	visibleAfterRole := make([]int64, 0, len(onlineUserIDs))
	for _, id := range onlineUserIDs {
		if _, isInvisible := roleInvisibleUserIDMap[id]; !isInvisible {
			visibleAfterRole = append(visibleAfterRole, id)
		}
	}

	// 再分批查库过滤启进场隐身开关的用户
	candidateMap := make(map[int64]int64, rankCandidateLimit)
	var userCount int
	batchQueryLimit := 1000 // 每批次查询的用户上限
	for i := 0; i < len(visibleAfterRole) && userCount < rankCandidateLimit; i += batchQueryLimit {
		end := min(i+batchQueryLimit, len(visibleAfterRole))
		batch := visibleAfterRole[i:end]
		visibleIDs, err := usermeta.FindInvisibleUserMap(batch)
		if err != nil {
			return nil, fmt.Errorf("FindInvisibleUserMap failed: %v", err)
		}
		for _, id := range batch {
			if _, isInvisible := visibleIDs[id]; !isInvisible {
				candidateMap[id] = scoreMap[id]
				userCount++
				if userCount >= rankCandidateLimit {
					break
				}
			}
		}
	}

	return candidateMap, nil
}

// getRoomOnlineUsersMap 根据 roomIDs 获取在线用户
func getRoomOnlineUsersMap(roomIDs []int64) (map[int64][]int64, error) {
	imRoomsList, err := userapi.IMRoomsList(mrpc.NewUserContextFromEnv(), &userapi.IMRoomsListRequest{
		RoomIDs: roomIDs,
		Users:   true,
	})
	if err != nil {
		return nil, fmt.Errorf("get im online users failed: %v", err)
	}
	roomUsersMap := make(map[int64][]int64, len(imRoomsList.Rooms))
	for roomID, roomItem := range imRoomsList.Rooms {
		roomUsersMap[roomID] = roomItem.UserIDs
	}
	return roomUsersMap, nil
}

// userRoomsMap 生成用户 ID 和直播间的映射关系
func userRoomsMap(imRooms map[int64][]int64) map[int64][]int64 {
	onlineUserRoomsMap := make(map[int64][]int64)
	for rid, users := range imRooms {
		for _, userID := range users {
			onlineUserRoomsMap[userID] = append(onlineUserRoomsMap[userID], rid)
		}
	}
	return onlineUserRoomsMap
}

// selectOnlineUsers 选择在线用户
// userIDs: 待筛选的用户 ID 列表
// onlineUserRoomsMap: 在线用户 ID 到直播间的映射
// 返回值: 保持原始顺序的在线用户 ID 列表
func selectOnlineUsers(userIDs []int64, onlineUserRoomsMap map[int64][]int64) []int64 {
	onlineUserIDs := make([]int64, 0, len(userIDs))
	for _, id := range userIDs {
		if _, ok := onlineUserRoomsMap[id]; ok {
			onlineUserIDs = append(onlineUserIDs, id)
		}
	}
	return onlineUserIDs
}

// batchGetUserInfo 批量获取用户资料
func batchGetUserInfo(userIDs []int64) (map[int64]*liveuser.Simple, error) {
	profiles, err := liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": userIDs}},
		&liveuser.FindOptions{FindTitles: true})
	if err != nil {
		return nil, fmt.Errorf("ListSimples failed: %v", err)
	}
	userProfileMap := util.ToMap(profiles, func(p *liveuser.Simple) int64 {
		return p.UserID()
	})
	return userProfileMap, nil
}

// getRankFromCache 从缓存获取榜单
func getRankFromCache(groupID int64) ([]*RankUserInfo, error) {
	rankCacheKey := getUserRankKey(groupID)
	val, err := service.Redis.Get(rankCacheKey).Bytes()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("redis get failed: %v", err)
	}
	var ranks []*RankUserInfo
	err = json.Unmarshal(val, &ranks)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}
	return ranks, nil
}

// storeRankInCache 缓存榜单数据
func storeRankInCache(groupID int64, ranks []*RankUserInfo) error {
	rankCacheKey := getUserRankKey(groupID)
	b, err := json.Marshal(ranks)
	if err != nil {
		return fmt.Errorf("marshal failed: %v", err)
	}
	err = service.Redis.Set(rankCacheKey, b, 10*time.Second).Err()
	if err != nil {
		return fmt.Errorf("redis Set failed: %v", err)
	}
	return nil
}

// getUserRankKey 获取用户榜单缓存的 Redis 键
func getUserRankKey(groupID int64) string {
	return keys.KeyMultiConnectGroupUserRankCache1.Format(groupID)
}
