package shop

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestIsBlockedGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	giftID := int64(191911)
	key := keys.KeyRedeemPointBlockedGiftIDs0.Format()
	require.NoError(service.Redis.SRem(key, giftID).Err())

	assert.False(IsBlockedGift(giftID))

	require.NoError(service.Redis.SAdd(key, giftID).Err())
	assert.True(IsBlockedGift(giftID))
}

func TestProcessUserPoints(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// mock 时间，now.Year() 取出来是 2023 年
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	config.Conf.AB["new_live_user_redeem_point_year"] = 2024

	userID := int64(1919)
	now := goutil.TimeNow()
	goods := RedeemGoods{
		Point:      1919,
		ExpireTime: now.Add(time.Minute).Unix(),
		MoreInfo:   &More{&Limit{PerUser: 1}},
	}
	key := KeyUserPoint(now.Year(), userID)
	require.NoError(service.Redis.Del(key).Err())
	countKey := KeyUserRedeemedCount(userID, goods.ID)
	require.NoError(service.Redis.Del(countKey).Err())

	_, ok, err := ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	assert.False(ok)

	require.NoError(service.Redis.Set(key, 1920000, time.Minute).Err())

	point, ok, err := ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	assert.True(ok)
	assert.EqualValues(1, point)

	points, ok, err := ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	assert.False(ok)
	assert.EqualValues(-1, points)

	require.NoError(service.Redis.Set(key, 1920000, time.Minute).Err())
	points, ok, err = ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	assert.False(ok)
	assert.EqualValues(1, points)

	// mock 时间，now.Year() 取出来是 2024 年
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)
	})
	now = goutil.TimeNow()
	goods.ExpireTime = now.Add(time.Minute).Unix()

	require.NoError(service.Redis.Del(countKey).Err())
	now = goutil.TimeNow()
	nowStamp := now.Unix()
	year := now.Year()
	// 测试用户积分不足，无法兑换商品
	var remainingPoint int64
	remainingPoint, ok, err = ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	require.False(ok)
	assert.EqualValues(-1, remainingPoint)

	// 添加测试数据
	tec := UserRedeemPoint{
		Year:         year,
		UserID:       userID,
		Point:        1919000,
		AllPoint:     1919000,
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
	}
	require.NoError(tec.DB().Save(&tec).Error)
	// 测试用户积分扣除成功
	remainingPoint, ok, err = ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	require.True(ok)
	assert.Zero(remainingPoint)

	// 验证用户积分是否扣除了
	var userPoint UserRedeemPoint
	err = UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Take(&userPoint).Error
	require.NoError(err)
	assert.Equal(year, userPoint.Year)
	assert.Equal(userID, userPoint.UserID)
	assert.Zero(userPoint.Point)
	assert.EqualValues(1919000, userPoint.AllPoint)
	require.NoError(UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Delete("").Error)

	// 测试星享馆兑换
	goods.ShopType = ShopTypeLevelPrivilege
	require.NoError(service.Redis.Del(countKey).Err())
	remainingPoint, ok, err = ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	require.True(ok)
	assert.Zero(remainingPoint)

	_, ok, err = ProcessUserPoints(userID, &goods, now)
	require.NoError(err)
	assert.False(ok)
}

func TestFindRedeemPointOld(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1919)
	key := KeyUserPoint(goutil.TimeNow().Year(), userID)
	require.NoError(service.Redis.Del(key).Err())

	now := goutil.TimeNow()
	point, err := FindRedeemPointOld(userID, now)
	require.NoError(err)
	assert.Zero(point)

	require.NoError(service.Redis.Set(key, 1919810, time.Minute).Err())

	point, err = FindRedeemPointOld(userID, now)
	require.NoError(err)
	assert.EqualValues(1919, point)
}

func TestFindRedeemPointNew(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	nowStamp := now.Unix()
	year := now.Year()
	userID := int64(1919)

	err := UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Delete("").Error
	require.NoError(err)
	// 测试没有找到用户的兑换积分记录
	point, err := FindRedeemPointNew(userID, now)
	require.NoError(err)
	assert.Zero(point)

	// 添加测试数据
	tec := UserRedeemPoint{
		Year:         year,
		UserID:       userID,
		Point:        1000,
		AllPoint:     1000,
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
	}
	require.NoError(tec.DB().Save(&tec).Error)
	// 测试找到用户的兑换积分记录
	point, err = FindRedeemPointNew(userID, now)
	require.NoError(err)
	assert.Equal(int64(1), point)
	require.NoError(tec.DB().Delete(nil, "id = ?", tec.ID).Error)
}

func TestNewUserRedeemPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1919)
	year := goutil.TimeNow().Year()
	require.NoError(UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Delete("").Error)

	// 测试第一次添加用户积分
	err := newUserRedeemPoint(userID, 30)
	require.NoError(err)
	// 验证用户积分是否插入成功
	var userPoint UserRedeemPoint
	err = UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Take(&userPoint).Error
	require.NoError(err)
	assert.Equal(year, userPoint.Year)
	assert.Equal(userID, userPoint.UserID)
	assert.EqualValues(30, userPoint.Point)
	assert.EqualValues(30, userPoint.AllPoint)

	// 测试第二次添加用户积分，触发更新操作
	err = newUserRedeemPoint(userID, 40)
	require.NoError(err)
	// 验证用户积分是否更新了
	err = UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Take(&userPoint).Error
	require.NoError(err)
	assert.Equal(year, userPoint.Year)
	assert.Equal(userID, userPoint.UserID)
	assert.EqualValues(70, userPoint.Point)
	assert.EqualValues(70, userPoint.AllPoint)
}

func TestAddUserRedeemPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1919)
	year := goutil.TimeNow().Year()
	require.NoError(UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Delete("").Error)

	// 测试第一次添加用户积分
	err := AddUserRedeemPoint(userID, 30)
	require.NoError(err)

	// 验证用户积分是否插入成功
	var userPoint UserRedeemPoint
	err = UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Take(&userPoint).Error
	require.NoError(err)
	assert.Equal(year, userPoint.Year)
	assert.Equal(userID, userPoint.UserID)
	assert.EqualValues(30, userPoint.Point)
	assert.EqualValues(30, userPoint.AllPoint)

	// 测试第二次添加用户积分，由于产生了数据，是更新用户的积分
	err = AddUserRedeemPoint(userID, 40)
	require.NoError(err)
	// 验证用户积分是否更新了
	err = UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Take(&userPoint).Error
	require.NoError(err)
	assert.Equal(year, userPoint.Year)
	assert.Equal(userID, userPoint.UserID)
	assert.EqualValues(70, userPoint.Point)
	assert.EqualValues(70, userPoint.AllPoint)
}
