package shop

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// ShopTypeRedeem 万事屋常驻兑换商城
	ShopTypeRedeem = iota
	// ShopTypeLevelPrivilege 星享馆等级权益商城
	ShopTypeLevelPrivilege
)

// RedeemGoods 积分兑换商城中的商品，每隔段时间会轮换
type RedeemGoods struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTIme int64 `gorm:"column:modified_time"`
	StartTime    int64 `gorm:"column:start_time"`
	ExpireTime   int64 `gorm:"column:expire_time"`

	Point    int64  `gorm:"column:point"`
	Name     string `gorm:"column:name"`
	Icon     string `gorm:"column:icon"`
	Type     int    `gorm:"column:type"`
	ShopType int    `gorm:"column:shop_type"`
	Sort     int    `gorm:"column:sort"`
	RewardID int64  `gorm:"column:reward_id"`
	More     string `gorm:"column:more"`

	IconURL  string `gorm:"-"`
	MoreInfo *More  `gorm:"-"`
}

// More 商品的附加内容
type More struct {
	Limit *Limit `json:"limit,omitempty"`
}

// Limit 商品的限购配置
type Limit struct {
	PerUser int `json:"per_user"`
}

// TableName for the RedeemGoods model
func (RedeemGoods) TableName() string {
	return "live_redeem_goods"
}

// DB the db instance of RedeemGoods model
func (g RedeemGoods) DB() *gorm.DB {
	return service.LiveDB.Table(g.TableName())
}

// BeforeSave gorm 钩子
func (g *RedeemGoods) BeforeSave() (err error) {
	now := goutil.TimeNow().Unix()
	if g.DB().NewRecord(g) {
		g.CreateTime = now
	}
	g.ModifiedTIme = now
	return
}

// AfterFind gorm 钩子
func (g *RedeemGoods) AfterFind() (err error) {
	if g.Icon != "" {
		g.IconURL = storage.ParseSchemeURL(g.Icon)
	}
	g.MoreInfo, err = g.UnmarshalMore()
	if err != nil {
		logger.WithFields(logger.Fields{
			"goods_id": g.ID,
		}).Errorf("Error found when unmarshal more info: %v", err)
		// PASS
	}
	return
}

// UnmarshalMore 解析 More 的 JSON 结构
func (g *RedeemGoods) UnmarshalMore() (*More, error) {
	if g.More == "" {
		return nil, nil
	}
	var more More
	err := json.Unmarshal([]byte(g.More), &more)
	if err != nil {
		return nil, err
	}
	return &more, nil
}

// FindRedeemGoods 根据 ID 查找对应的兑换商品
func FindRedeemGoods(goodsID int64) (*RedeemGoods, error) {
	var goods RedeemGoods
	err := goods.DB().Take(&goods, "id = ? AND sort > 0", goodsID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &goods, nil
}

// ListRedeemGoods 列出所有当前时间可兑换的商品
func ListRedeemGoods(timeNow time.Time, shopType int) ([]RedeemGoods, error) {
	var goodsList []RedeemGoods
	err := RedeemGoods{}.DB().
		Where("shop_type = ? AND start_time <= ? AND expire_time > ? AND sort > 0",
			shopType, timeNow.Unix(), timeNow.Unix()).
		Order("sort ASC").Find(&goodsList).Error
	if err != nil {
		return nil, err
	}
	return goodsList, nil
}
