package shop

import (
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// UserRedeemPoint 万事屋用户的兑换积分记录
type UserRedeemPoint struct {
	ID           int64 `gorm:"column:id;primary_key"`
	Year         int   `gorm:"column:year"`
	UserID       int64 `gorm:"column:user_id"`
	Point        int64 `gorm:"column:point"`
	AllPoint     int64 `gorm:"column:all_point"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
}

// TableName for the UserRedeemPoint model
func (UserRedeemPoint) TableName() string {
	return "live_user_redeem_point"
}

// DB the db instance of UserRedeemPoint model
func (g UserRedeemPoint) DB() *gorm.DB {
	return service.LiveDB.Table(g.TableName())
}

// SpentToRedeemPointRatio 用户随机礼物开销（钻）转换成积分的比例
const SpentToRedeemPointRatio = 1000

// KeyUserPoint 用户积分的 redis key
func KeyUserPoint(year int, userID int64) string {
	return keys.KeyRedeemUserPoint2.Format(year, userID)
}

// KeyUserRedeemedCount 用户对商品已兑换次数的 redis key
func KeyUserRedeemedCount(userID, goodsID int64) string {
	return keys.KeyShopRedeemUserRedeemedCount2.Format(userID, goodsID)
}

// SpentToRedeemPoint 将用户消费转换成兑换积分
func SpentToRedeemPoint(spent int64) int64 {
	return spent / SpentToRedeemPointRatio
}

// RedeemPointToSpent 将兑换积分转换成用户消费
func RedeemPointToSpent(point int64) int64 {
	return point * SpentToRedeemPointRatio
}

// newUserRedeemPoint 添加用户积分
func newUserRedeemPoint(userID, point int64) error {
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	year := now.Year()
	updateSQL := servicedb.UpdateOnDuplicateKeyExpr(fmt.Sprintf("point = point + %d, all_point = all_point + %d", point, point), "year, user_id")
	insertSQL := fmt.Sprintf(`INSERT INTO %s (year, user_id, point, all_point, create_time, modified_time)
		VALUES (%d, %d, %d, %d, %d, %d) %s`, UserRedeemPoint{}.TableName(), year, userID, point, point, nowStamp, nowStamp, updateSQL)
	err := service.LiveDB.Exec(insertSQL).Error
	if err != nil {
		return err
	}
	return nil
}

// AddUserRedeemPoint 累加用户的常驻商店兑换积分
func AddUserRedeemPoint(userID, spent int64) error {
	now := goutil.TimeNow()
	year := now.Year()
	nowStamp := now.Unix()
	var userPoint UserRedeemPoint
	err := UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).Take(&userPoint).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			// 记录不存在，添加用户积分
			return newUserRedeemPoint(userID, spent)
		}
		return err
	}
	// 记录存在，更新用户积分
	err = UserRedeemPoint{}.DB().Where("id = ?", userPoint.ID).
		Updates(map[string]interface{}{
			"point":         gorm.Expr("point + ?", spent),
			"all_point":     gorm.Expr("all_point + ?", spent),
			"modified_time": nowStamp,
		}).Error
	if err != nil {
		return err
	}
	return nil
}

func redeemPointExpiration(now time.Time) time.Duration {
	// 兑换积分固定来年 1 月 15 号过期
	return time.Date(now.Year()+1, 1, 15, 0, 0, 0, 0, time.Local).Sub(now)
}

// IsBlockedGift 查看礼物是否在获取积分的黑名单内
func IsBlockedGift(giftID int64) bool {
	// TODO: 添加缓存到 localCache
	key := keys.KeyRedeemPointBlockedGiftIDs0.Format()
	isBlocked, err := service.Redis.SIsMember(key, giftID).Result()
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return isBlocked
}

// ProcessUserPoints 处理用户的商店兑换积分和兑换次数，若此函数的剩余积分返回 -1 则代表积分不足
// NOTICE: 调用前需先判断用户积分和剩余次数是否足够
func ProcessUserPoints(userID int64, goods *RedeemGoods, now time.Time) (int64, bool, error) {
	var remainingPoint int64
	var err error
	var rollback func(pipe redis.Pipeliner) error
	if goods.ShopType == ShopTypeRedeem {
		var remaining int64
		// 常驻兑换积分需要花费用户的兑换积分
		remaining, rollback, err = spendUserRedeemPoint(userID, goods, now)
		if err != nil {
			return 0, false, err
		}
		if remaining < 0 {
			// 用户兑换积分不足
			return -1, false, nil
		}
		remainingPoint = SpentToRedeemPoint(remaining)
	}
	if goods.MoreInfo == nil || goods.MoreInfo.Limit == nil {
		if goods.ShopType == ShopTypeLevelPrivilege {
			logger.WithField("goods_id", goods.ID).Error("等级权益商城次数限制配置错误")
		}
		// 无限制商品无需记录已兑换次数
		return remainingPoint, true, nil
	}
	success, err := addUserRedeemedCount(userID, goods, now, rollback)
	return remainingPoint, success, err
}

func spendUserRedeemPoint(userID int64, goods *RedeemGoods, now time.Time) (int64, func(pipe redis.Pipeliner) error, error) {
	year := now.Year()
	cost := RedeemPointToSpent(goods.Point)
	var remaining int64
	var err error
	var rollback func(pipe redis.Pipeliner) error
	var newLiveUserRedeemPointYear int
	config.GetAB("new_live_user_redeem_point_year", &newLiveUserRedeemPointYear)
	if year >= newLiveUserRedeemPointYear {
		var userPoint int64
		err = UserRedeemPoint{}.DB().Select("point").
			Where("year = ? AND user_id = ?", year, userID).
			Row().Scan(&userPoint)
		if err != nil && !servicedb.IsErrNoRows(err) {
			return 0, nil, err
		}
		remaining = userPoint - cost
		if remaining < 0 {
			return -1, nil, nil
		}
		// 使用乐观锁（point >= 扣除的值）保证积分不足的时候不会修改数据
		db := UserRedeemPoint{}.DB().Where("year = ? AND user_id = ? AND point >= ?", year, userID, cost).
			Updates(map[string]interface{}{
				"point":         gorm.Expr("point - ?", cost),
				"modified_time": goutil.TimeNow().Unix(),
			})
		if db.Error != nil {
			return 0, nil, db.Error
		}
		// 没有修改时表示积分不足
		if db.RowsAffected == 0 {
			return -1, nil, nil
		}
		rollback = func(pipe redis.Pipeliner) error {
			// 如果出现用户兑换商品超出限制的情况（并发请求）将积分加回去
			return UserRedeemPoint{}.DB().Where("year = ? AND user_id = ?", year, userID).
				Updates(map[string]interface{}{
					"point":         gorm.Expr("point + ?", cost),
					"modified_time": goutil.TimeNow().Unix(),
				}).Error
		}
	} else {
		// TODO: 2024 年之后删除
		key := KeyUserPoint(year, userID)
		remaining, err = service.Redis.DecrBy(key, cost).Result()
		if err != nil {
			return 0, nil, err
		}
		if remaining < 0 {
			// 如果出现积分被扣成负数的情况将积分加回去
			pipe := service.Redis.TxPipeline()
			pipe.IncrBy(key, cost)
			pipe.Expire(key, redeemPointExpiration(now))
			_, err = pipe.Exec()
			if err != nil {
				return 0, nil, err
			}
			return -1, nil, nil
		}
		rollback = func(pipe redis.Pipeliner) error {
			pipe.IncrBy(key, cost)
			pipe.Expire(key, redeemPointExpiration(now))
			return nil
		}
	}
	return remaining, rollback, nil
}

func addUserRedeemedCount(userID int64, goods *RedeemGoods, now time.Time, rollback func(pipe redis.Pipeliner) error) (bool, error) {
	pipe := service.Redis.TxPipeline()
	countKey := KeyUserRedeemedCount(userID, goods.ID)
	cmd := pipe.Incr(countKey)
	ttl := time.Unix(goods.ExpireTime, 0).Add(24 * time.Hour).Sub(now)
	pipe.Expire(countKey, ttl)
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}
	count := cmd.Val()
	if int(count) > goods.MoreInfo.Limit.PerUser {
		// 如果用户超额兑换了则返还次数和积分
		pipe = service.Redis.TxPipeline()
		pipe.Decr(countKey)
		if rollback != nil {
			// TODO: 删掉旧版处理 UserRedeemPoint 的方法后可以把 rollback 的调用挪到上一层
			err = rollback(pipe)
			if err != nil {
				return false, err
			}
		}
		_, err = pipe.Exec()
		return false, err
	}
	return true, nil
}

// FindRedeemPoint 查看用户可用的常驻商店兑换积分
func FindRedeemPoint(userID int64, now time.Time) (int64, error) {
	var newLiveUserRedeemPointYear int
	config.GetAB("new_live_user_redeem_point_year", &newLiveUserRedeemPointYear)
	if now.Year() >= newLiveUserRedeemPointYear {
		return FindRedeemPointNew(userID, now)
	}
	// TODO: 2024 年之后删除
	return FindRedeemPointOld(userID, now)
}

// FindRedeemPointOld 老的查看用户可用的常驻商店兑换积分
// Deprecated: 从 2024 年开始不再使用
func FindRedeemPointOld(userID int64, now time.Time) (int64, error) {
	year := now.Year()
	key := KeyUserPoint(year, userID)
	remaining, err := service.Redis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	return SpentToRedeemPoint(remaining), nil
}

// FindRedeemPointNew 新的查看用户可用的常驻商店兑换积分
func FindRedeemPointNew(userID int64, now time.Time) (int64, error) {
	var point int64
	err := UserRedeemPoint{}.DB().Select("point").
		Where("year = ? AND user_id = ?", now.Year(), userID).Row().Scan(&point)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	return SpentToRedeemPoint(point), nil
}

// FindUserRedeemedCount 获取用户对商品的已兑换次数
func FindUserRedeemedCount(userID, goodsID int64) (int, error) {
	key := KeyUserRedeemedCount(userID, goodsID)
	redeemedCount, err := service.Redis.Get(key).Int()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	return redeemedCount, nil
}
