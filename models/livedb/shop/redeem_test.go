package shop

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RedeemGoods{}, "id", "create_time", "modified_time", "start_time", "expire_time",
		"point", "name", "icon", "type", "shop_type", "sort", "reward_id", "more")

	kc.Check(RedeemRecord{}, "id", "create_time", "modified_time", "user_id", "goods_id", "name", "shop_type")
}

func TestRedeemGoods_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goods := RedeemGoods{
		Icon: "oss://1234",
	}
	require.NoError(goods.AfterFind())
	assert.Nil(goods.MoreInfo)

	goods.More = `{"limit": {"per_user": 10}}`
	require.NoError(goods.AfterFind())
	require.NotNil(goods.MoreInfo)
	require.NotNil(goods.MoreInfo.Limit)
	assert.EqualValues(10, goods.MoreInfo.Limit.PerUser)
}

func TestRedeemGoods_UnmarshalMore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rg := RedeemGoods{
		ID:   111111,
		More: `{"limit": {"per_user": 10}}`,
	}
	require.NoError(rg.DB().Delete("", "id = ?", rg.ID).Error)
	require.NoError(rg.DB().Create(rg).Error)
	more, err := rg.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more.Limit)
	assert.EqualValues(10, more.Limit.PerUser)
}

func TestFindRedeemGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rg := RedeemGoods{
		ID:   114514,
		Sort: 1,
		Name: "goods1",
		Icon: "Hello_world",
	}
	require.NoError(rg.DB().Delete("", "id = ?", rg.ID).Error)
	require.NoError(rg.DB().Create(rg).Error)

	result, err := FindRedeemGoods(rg.ID)
	require.NoError(err)
	require.NotNil(result)
	assert.Equal(rg.Name, result.Name)
	assert.Equal("https://static-test.missevan.com/Hello_world", result.IconURL)
}

func TestListRedeemGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startTime := goutil.TimeNow().Add(-time.Minute).Unix()
	expireTime := goutil.TimeNow().Add(time.Minute).Unix()
	rg := RedeemGoods{
		ID:         114514,
		Sort:       1,
		ShopType:   ShopTypeRedeem,
		StartTime:  startTime,
		ExpireTime: expireTime,
		Icon:       "Hello_world",
	}
	require.NoError(rg.DB().Delete("", "id = ?", rg.ID).Error)
	require.NoError(rg.DB().Create(rg).Error)

	list, err := ListRedeemGoods(goutil.TimeNow(), ShopTypeRedeem)
	require.NoError(err)
	require.NotEmpty(list)

	i := goutil.FindIndex(len(list), func(i int) bool { return list[i].ID == rg.ID })
	require.NotEqualValues(-1, i)
	require.NotEmpty(list[i])
	assert.Equal("https://static-test.missevan.com/Hello_world", list[i].IconURL)
}

func TestFindUserRedeemedCount(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(1919)
	goodsID := int64(810)
	key := KeyUserRedeemedCount(userID, goodsID)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.Set(key, 1, time.Minute).Err())

	count, err := FindUserRedeemedCount(userID, goodsID)
	require.NoError(err)
	assert.Equal(1, count)
}
