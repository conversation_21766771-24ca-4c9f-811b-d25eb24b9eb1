package shop

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// RedeemRecord 用户在积分商城中兑换商品的记录
type RedeemRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTIme int64 `gorm:"column:modified_time"`

	UserID   int64  `gorm:"column:user_id"`
	GoodsID  int64  `gorm:"column:goods_id"`
	Name     string `gorm:"column:name"`
	ShopType int    `gorm:"column:shop_type"`
}

// TableName for the RedeemRecord model
func (RedeemRecord) TableName() string {
	return "live_redeem_record"
}

// DB the db instance of RedeemRecord model
func (g RedeemRecord) DB() *gorm.DB {
	return service.LiveDB.Table(g.TableName())
}

// BeforeSave gorm 钩子
func (g *RedeemRecord) BeforeSave() (err error) {
	now := goutil.TimeNow().Unix()
	if g.DB().NewRecord(g) {
		g.CreateTime = now
	}
	g.ModifiedTIme = now
	return
}

const maxRecordsCount = 50

// ListRedeemRecords 列出用户最近的兑换记录，最多 50 条
func ListRedeemRecords(userID int64, shopType int, p, pageSize int64) ([]RedeemRecord, goutil.Pagination, error) {
	db := RedeemRecord{}.DB().Where("user_id = ? AND shop_type = ?", userID, shopType)
	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	count = min(count, maxRecordsCount)

	pagination := goutil.MakePagination(count, p, pageSize)
	if !pagination.Valid() {
		return []RedeemRecord{}, pagination, nil
	}

	var recordsList []RedeemRecord
	err = pagination.ApplyTo(db).Order("create_time DESC").Find(&recordsList).Error
	if err != nil {
		return nil, pagination, err
	}

	return recordsList, pagination, nil
}
