package shop

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestListRedeemRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l1, _, err := ListRedeemRecords(111134333, ShopTypeRedeem, 1, 1)
	require.NoError(err)
	assert.Empty(l1)

	rr := RedeemRecord{
		ID:         1919,
		UserID:     810,
		CreateTime: goutil.TimeNow().Unix(),
	}
	require.NoError(rr.DB().Delete("", "id = ?", rr.ID).Error)
	require.NoError(rr.DB().Create(rr).Error)
	rr2 := RedeemRecord{
		ID:         1918,
		UserID:     810,
		ShopType:   ShopTypeLevelPrivilege,
		CreateTime: goutil.TimeNow().Unix(),
	}
	require.NoError(rr2.DB().Delete("", "id = ?", rr2.ID).Error)
	require.NoError(rr2.DB().Create(rr2).Error)

	list, pa, err := ListRedeemRecords(rr.UserID, ShopTypeRedeem, 1, 1)
	require.NoError(err)
	require.True(pa.Valid())
	assert.EqualValues(1, pa.P)
	assert.Len(list, 1)

	list, pa, err = ListRedeemRecords(rr.UserID, ShopTypeRedeem, 2, maxRecordsCount)
	require.NoError(err)
	assert.False(pa.Valid())
	assert.Empty(list)

	list, pa, err = ListRedeemRecords(rr.UserID, ShopTypeLevelPrivilege, 1, 1)
	require.NoError(err)
	require.True(pa.Valid())
	assert.EqualValues(1, pa.P)
	assert.Len(list, 1)
}
