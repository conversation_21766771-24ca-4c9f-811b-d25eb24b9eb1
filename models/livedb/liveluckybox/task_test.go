package liveluckybox

import (
	"context"
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTableName_TaskPrize(t *testing.T) {
	assert.Equal(t, "live_lucky_box_task", Task{}.TableName())
}

func TestTags_TaskPrize(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Task{}, "id", "create_time", "modified_time", "sort", "goods_id", "name", "intro", "type", "num", "gift_id",
		"prize_id", "prize_type", "prize_name", "prize_icon", "prize_duration")
}

func TestFindTasks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
	)
	err := DB().Delete(Task{}, "goods_id = ?", testGoodsID).Error
	require.NoError(err)
	tasks := make([]*Task, 0, 3)
	for i := 1; i <= cap(tasks); i++ {
		tasks = append(tasks, &Task{
			Sort:          i,
			GoodsID:       testGoodsID,
			Name:          "task",
			Intro:         "intro",
			Type:          1,
			Num:           1,
			GiftID:        int64(i),
			PrizeID:       int64(i),
			PrizeType:     1,
			PrizeDuration: 100,
		})
	}
	err = servicedb.BatchInsert(DB(), Task{}.TableName(), tasks)
	require.NoError(err)

	ts, err := FindTasks(testGoodsID)
	require.NoError(err)
	assert.Len(ts, 3)
}

func TestUserCompletedTasks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
		testPoolID  int64 = 123
		testUserID  int64 = 100001
	)
	err := servicedb.Tx(DB(), func(tx *gorm.DB) error {
		err := DB().Delete(TaskRecordMap{}, "user_id = ?", testUserID).Error
		if err != nil {
			return err
		}
		err = DB().Delete(TaskRecord{}, "user_id = ?", testUserID).Error
		if err != nil {
			return err
		}
		err = DB().Delete(Record{}, "goods_id = ? AND user_id = ?", testGoodsID, testUserID).Error
		if err != nil {
			return err
		}
		return nil
	})
	require.NoError(err)
	records := []*Record{
		{
			ID:      1000,
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  1,
		},
		{
			ID:      1001,
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  1,
		},
		{
			ID:      1002,
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  2, // SSR
		},
		{
			ID:      1003,
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  3,
		},
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)
	// 配置任务
	err = DB().Delete(Task{}, "goods_id = ?", testGoodsID).Error
	require.NoError(err)
	tasks := []*Task{
		// 收集全套
		{
			ID:      1,
			Sort:    1,
			GoodsID: testGoodsID,
			Name:    "task",
			Intro:   "intro",
			Type:    TaskTypeFullNormal,
			Num:     1,
		},
		// 开出指定礼物
		{
			ID:      2,
			Sort:    2,
			GoodsID: testGoodsID,
			Name:    "task",
			Intro:   "intro",
			Type:    TaskTypeTarget,
			Num:     1,
			GiftID:  2,
		},
		// 开出任意礼物
		{
			ID:      3,
			Sort:    3,
			GoodsID: testGoodsID,
			Name:    "task",
			Intro:   "intro",
			Type:    TaskTypeAnyNormal,
			Num:     2,
		},
	}
	err = servicedb.BatchInsert(DB(), Task{}.TableName(), tasks)
	require.NoError(err)

	// 配置奖池
	pool := &gift.PoolLuckyBox{
		Type:   gift.PoolTypeLuckyBox,
		PoolID: testPoolID,
		SSRID:  2,
		Rates: map[int64]int{
			1: 100,
			2: 100,
			3: 100,
		},
	}
	completedTasks, err := UserCompletedTasks(DB(), testGoodsID, testUserID, pool)
	require.NoError(err)
	require.Len(completedTasks, 3)
	assert.EqualValues(0, completedTasks[0].GiftID)
	assert.EqualValues(1, completedTasks[0].Num)
	assert.EqualValues(2, len(completedTasks[0].RecordIDs))
	assert.EqualValues(pool.SSRID, completedTasks[1].GiftID)
	assert.EqualValues(1, completedTasks[1].Num)
	assert.EqualValues(1, len(completedTasks[1].RecordIDs))
	assert.EqualValues(1, completedTasks[2].GiftID)
	assert.EqualValues(1, completedTasks[2].Num)
	assert.EqualValues(2, len(completedTasks[2].RecordIDs))

	maps := []*TaskRecordMap{
		{
			UserID:      testUserID,
			BoxTaskID:   tasks[1].ID,   // 指定礼物
			BoxRecordID: records[2].ID, // SSR
		},
	}
	err = servicedb.BatchInsert(DB(), TaskRecordMap{}.TableName(), maps)
	require.NoError(err)
	completedTasks, err = UserCompletedTasks(DB(), testGoodsID, testUserID, pool)
	require.NoError(err)
	require.Len(completedTasks, 2)
	// 统计的收集套数
	assert.EqualValues(0, completedTasks[0].GiftID)
	assert.EqualValues(1, completedTasks[0].Num)
	// 统计的收集任意礼物
	assert.EqualValues(1, completedTasks[1].GiftID)
	assert.EqualValues(1, completedTasks[1].Num)

	// 删除 SSR 礼物记录
	err = DB().Delete(TaskRecordMap{}, "lucky_box_task_id = ? AND lucky_box_record_id = ?", tasks[1].ID, records[2].ID).Error
	require.NoError(err)
	maps = []*TaskRecordMap{
		{
			UserID:      testUserID,
			BoxTaskID:   tasks[0].ID, // 指定全套
			BoxRecordID: records[0].ID,
		},
		{
			UserID:      testUserID,
			BoxTaskID:   tasks[0].ID, // 指定全套
			BoxRecordID: records[1].ID,
		},
		{
			UserID:      testUserID,
			BoxTaskID:   tasks[0].ID, // 指定全套
			BoxRecordID: records[3].ID,
		},
		{
			UserID:      testUserID,
			BoxTaskID:   tasks[2].ID, // 开出任意礼物
			BoxRecordID: records[1].ID,
		},
	}
	err = servicedb.BatchInsert(DB(), TaskRecordMap{}.TableName(), maps)
	require.NoError(err)
	completedTasks, err = UserCompletedTasks(DB(), testGoodsID, testUserID, pool)
	require.NoError(err)
	require.Len(completedTasks, 1)
	assert.Equal(pool.SSRID, completedTasks[0].GiftID)
}

func TestTask_SendPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nowUnix := goutil.TimeNow().Unix()
	appearances, err := appearance.Find(bson.M{
		"start_time":  bson.M{"$lte": nowUnix},
		"expire_time": bson.M{"$not": bson.M{"$lte": nowUnix}},
	}, &options.FindOptions{Limit: goutil.NewInt64(1)})
	require.NoError(err)
	require.NotEmpty(appearances)
	var (
		testUserID       int64 = 100001
		testAppearanceID       = appearances[0].ID
	)
	_, err = userappearance.Collection().DeleteMany(context.TODO(), bson.M{
		"user_id":       testUserID,
		"appearance_id": testAppearanceID,
	})
	require.NoError(err)

	task := Task{
		PrizeID:       testAppearanceID,
		PrizeType:     PrizeTypeAppearance,
		PrizeDuration: 100,
	}
	err = task.SendPrize(testUserID, 1)
	require.NoError(err)
	count, err := userappearance.Collection().CountDocuments(context.TODO(), bson.M{
		"user_id":       testUserID,
		"appearance_id": testAppearanceID,
		"status":        bson.M{"$gt": userappearance.StatusPending},
	})
	require.NoError(err)
	assert.EqualValues(1, count)

	task = Task{
		PrizeID:       testAppearanceID,
		PrizeType:     131212,
		PrizeDuration: 100,
	}
	err = task.SendPrize(testUserID, 1)
	assert.EqualError(err, "unknown prize type 131212")
}
