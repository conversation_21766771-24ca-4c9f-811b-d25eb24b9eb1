package liveluckybox

// TaskRecord 宝盒任务记录
type TaskRecord struct {
	ID           int64 `gorm:"column:id"`            // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 更新时间，单位：秒

	BoxTaskID int64 `gorm:"column:lucky_box_task_id"` // 任务 ID
	UserID    int64 `gorm:"column:user_id"`           // 用户 ID
	RoomID    int64 `gorm:"column:room_id"`           // 房间 ID
}

// TableName .
func (TaskRecord) TableName() string {
	return "live_lucky_box_task_record"
}

// UserTaskCount 用户任务完成次数
type UserTaskCount struct {
	TaskID int64 `gorm:"column:task_id"`
	Count  int   `gorm:"column:count"`
}

// CountUserFinishedTasks 查询任务完成次数
func CountUserFinishedTasks(userID int64, taskIDs []int64) ([]UserTaskCount, error) {
	var utcs []UserTaskCount
	err := DB().
		Table(TaskRecord{}.TableName()).
		Select("COUNT(*) AS count, lucky_box_task_id AS task_id").
		Where("user_id = ? AND lucky_box_task_id IN (?)", userID, taskIDs).
		Group("lucky_box_task_id").
		Find(&utcs).Error
	if err != nil {
		return nil, err
	}
	return utcs, nil
}
