package liveluckybox

import (
	"slices"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTableName_Record(t *testing.T) {
	assert.Equal(t, "live_lucky_box_record", Record{}.TableName())
}

func TestTags_Record(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Record{}, "id", "create_time", "modified_time", "user_id", "room_id", "goods_id", "gift_id", "order_id")
}

func TestFindUserRecordsByGiftIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
		testUserID  int64 = 100001
	)
	err := DB().Delete(Record{}, "goods_id = ? AND user_id = ?", testGoodsID, testUserID).Error
	require.NoError(err)
	records := make([]*Record, 0, 9)
	for i := 1; i <= 9; i++ {
		records = append(records, &Record{
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  int64(i),
		})
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)

	records, err = FindUserRecordsByGiftIDs(testGoodsID, testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Len(records, 3)
}

func TestFindUserRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
		testUserID  int64 = 100001
	)
	err := DB().Delete(Record{}, "goods_id = ? AND user_id = ?", testGoodsID, testUserID).Error
	require.NoError(err)
	records := make([]*Record, 0, 100)
	for i := 1; i <= 99; i++ {
		records = append(records, &Record{
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  int64(i),
		})
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)
	records, err = FindUserRecords(testGoodsID, testUserID)
	require.NoError(err)
	assert.Len(records, 99)

	records = make([]*Record, 0, 2)
	for i := 1; i <= 2; i++ {
		records = append(records, &Record{
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  int64(i),
		})
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)
	records, err = FindUserRecords(testGoodsID, testUserID)
	require.NoError(err)
	assert.Len(records, 100)
}

func TestFindUserValidRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
		testUserID  int64 = 100001
	)
	err := DB().Delete(Record{}, "goods_id = ? AND user_id = ?", testGoodsID, testUserID).Error
	require.NoError(err)
	err = DB().Delete(TaskRecordMap{}, "user_id = ?", testUserID).Error
	require.NoError(err)
	records := make([]*Record, 0, 9)
	for i := 1; i <= 9; i++ {
		records = append(records, &Record{
			ID:      int64(i) + 1000,
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  int64(i),
		})
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)
	maps := []TaskRecordMap{
		{
			BoxTaskID:   1,
			BoxRecordID: 1001,
		},
		{
			BoxTaskID:   2,
			BoxRecordID: 1001,
		},
		{
			BoxTaskID:   2,
			BoxRecordID: 1003,
		},
		{
			BoxTaskID:   2,
			BoxRecordID: 1004,
		},
		{
			BoxTaskID:   1,
			BoxRecordID: 1002,
		},
	}
	err = servicedb.BatchInsert(DB(), TaskRecordMap{}.TableName(), maps)
	require.NoError(err)

	rs, err := FindUserValidRecords(DB(), testGoodsID, testUserID, 1)
	require.NoError(err)
	assert.Len(rs, 7)
	exists := slices.ContainsFunc(rs, func(r *ValidRecord) bool {
		return r.RecordID == 1001 || r.RecordID == 1002
	})
	assert.False(exists)

	rs, err = FindUserValidRecords(DB(), testGoodsID, testUserID, 2)
	require.NoError(err)
	assert.Len(rs, 6)
	exists = slices.ContainsFunc(rs, func(r *ValidRecord) bool {
		return r.RecordID == 1001 || r.RecordID == 1003 || r.RecordID == 1004
	})
	assert.False(exists)

	rs, err = FindUserValidRecords(DB(), testGoodsID, testUserID, 3)
	require.NoError(err)
	assert.Len(rs, 9)
}

func TestOwnedGiftCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID int64 = 100000
		testUserID  int64 = 100001
	)
	err := DB().Delete(Record{}, "goods_id = ? AND user_id = ?", testGoodsID, testUserID).Error
	require.NoError(err)
	records := make([]*Record, 0, 100)
	for i := 1; i <= 99; i++ {
		records = append(records, &Record{
			UserID:  testUserID,
			GoodsID: testGoodsID,
			GiftID:  int64(i),
		})
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)

	pgs, err := OwnedGiftCount(testGoodsID, testUserID)
	require.NoError(err)
	assert.Len(pgs, 99)
}

func TestEachGiftNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testGoodsID100000 int64 = 100000
		testGoodsID100001 int64 = 100001
		testUserID        int64 = 100001
	)
	err := DB().Delete(Record{}, "goods_id IN (?)", testGoodsID100000).Error
	require.NoError(err)
	now := goutil.TimeNow()
	records := []*Record{
		{
			CreateTime: now.Unix(),
			UserID:     testUserID,
			GoodsID:    testGoodsID100000,
			GiftID:     1,
		},
		{
			CreateTime: now.AddDate(0, -1, 0).Unix(),
			UserID:     testUserID,
			GoodsID:    testGoodsID100000,
			GiftID:     2,
		},
		{
			CreateTime: now.Unix(),
			UserID:     testUserID,
			GoodsID:    testGoodsID100000,
			GiftID:     2,
		},
		{
			CreateTime: now.Unix(),
			UserID:     testUserID,
			GoodsID:    testGoodsID100001,
			GiftID:     2,
		},
	}
	err = servicedb.BatchInsert(DB(), Record{}.TableName(), records)
	require.NoError(err)

	goodsGiftNumMap, err := EachGiftNum([]int64{testGoodsID100000, testGoodsID100001})
	require.NoError(err)
	require.Len(goodsGiftNumMap, 2)
	require.Len(goodsGiftNumMap[testGoodsID100000], 2)
	assert.EqualValues(1, goodsGiftNumMap[testGoodsID100000][1].Num)
	assert.EqualValues(1, goodsGiftNumMap[testGoodsID100000][1].UserNum)
	assert.EqualValues(1, goodsGiftNumMap[testGoodsID100000][2].Num)
	assert.EqualValues(1, goodsGiftNumMap[testGoodsID100000][2].UserNum)
	require.Len(goodsGiftNumMap[testGoodsID100001], 1)
	assert.EqualValues(1, goodsGiftNumMap[testGoodsID100001][2].Num)
	assert.EqualValues(1, goodsGiftNumMap[testGoodsID100001][2].UserNum)
}
