package liveluckybox

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTableName_TaskRecord(t *testing.T) {
	assert.Equal(t, "live_lucky_box_task_record", TaskRecord{}.TableName())
}

func TestTags_TaskRecord(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(TaskRecord{}, "id", "create_time", "modified_time", "lucky_box_task_id", "user_id", "room_id")
}

func TestCountUserFinishedTasks(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 11111
	)
	err := DB().Delete(TaskRecord{}, "user_id = ?", testUserID).Error
	require.NoError(err)
	records := make([]*TaskRecord, 0, 3)
	for i := 1; i <= cap(records); i++ {
		records = append(records, &TaskRecord{
			BoxTaskID: int64(i),
			UserID:    testUserID,
		})
	}
	err = servicedb.BatchInsert(DB(), TaskRecord{}.TableName(), records)
	require.NoError(err)

	utcs, err := CountUserFinishedTasks(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	require.Len(utcs, 3)
	assert.EqualValues(1, utcs[0].Count)
	assert.EqualValues(1, utcs[1].Count)
	assert.EqualValues(1, utcs[2].Count)
}
