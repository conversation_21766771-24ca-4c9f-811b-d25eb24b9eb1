package liveluckybox

import (
	"fmt"

	"github.com/jinzhu/gorm"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Record 宝盒领抽奖记录
type Record struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 更新时间，单位：秒

	UserID  int64 `gorm:"column:user_id"`  // 用户 ID
	RoomID  int64 `gorm:"column:room_id"`  // 房间 ID
	GoodsID int64 `gorm:"column:goods_id"` // 宝盒商品 ID
	GiftID  int64 `gorm:"column:gift_id"`  // 礼物 ID
	OrderID int64 `gorm:"column:order_id"` // 订单 ID
}

// TableName .
func (Record) TableName() string {
	return "live_lucky_box_record"
}

// BeforeSave gorm 钩子
func (r *Record) BeforeSave() error {
	nowUnix := goutil.TimeNow().Unix()
	r.ModifiedTime = nowUnix
	if DB().NewRecord(r) {
		r.CreateTime = nowUnix
	}
	return nil
}

// FindUserRecordsByGiftIDs 根据用户 ID、商品 ID、礼物 ID 列表查找记录
func FindUserRecordsByGiftIDs(goodsID, userID int64, giftIDs []int64) ([]*Record, error) {
	var records []*Record
	err := DB().Where("goods_id = ? AND user_id = ? AND gift_id IN (?)", goodsID, userID, giftIDs).Find(&records).Error
	return records, err
}

// FindUserRecords 查询用户的宝盒记录
func FindUserRecords(goodsID, userID int64) ([]*Record, error) {
	var records []*Record
	err := DB().
		Where("goods_id = ? AND user_id = ?", goodsID, userID).
		Order("id DESC").Limit(100). // 默认返回最近 100 条记录
		Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// ValidRecord 有效记录
type ValidRecord struct {
	RecordID int64 `gorm:"column:record_id"`
	GiftID   int64 `gorm:"column:gift_id"`
	TaskID   int64 `gorm:"column:task_id"`
}

// FindUserValidRecords 查询用户未完成的宝盒记录
// TODO: 后续考虑使用 UNION ALL 一起查询
func FindUserValidRecords(db *gorm.DB, goodsID, userID, taskID int64) ([]*ValidRecord, error) {
	var records []*ValidRecord
	err := db.Table(fmt.Sprintf("%s AS r", Record{}.TableName())).
		Select(fmt.Sprintf("r.id AS record_id, r.gift_id AS gift_id, IFNULL(m.lucky_box_task_id, %d) AS task_id", taskID)).
		Joins(fmt.Sprintf("LEFT JOIN %s AS m ON r.id = m.lucky_box_record_id AND m.lucky_box_task_id = %d", TaskRecordMap{}.TableName(), taskID)).
		Where("r.goods_id = ? AND r.user_id = ? AND m.id IS NULL", goodsID, userID).
		Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// OwnedGift 拥有礼物
type OwnedGift struct {
	GiftID int64 `gorm:"column:gift_id"`
	Count  int   `gorm:"column:count"`
}

// OwnedGiftCount 查询用户拥有礼物数量
func OwnedGiftCount(goodsID, userID int64) (map[int64]OwnedGift, error) {
	var pgs []OwnedGift
	err := DB().Table(Record{}.TableName()).
		Select("gift_id, COUNT(*) AS count").
		Where("goods_id = ? AND user_id = ?", goodsID, userID).
		Group("gift_id").
		Find(&pgs).Error
	if err != nil {
		return nil, err
	}
	return goutil.ToMap(pgs, "GiftID").(map[int64]OwnedGift), nil
}

// GiftNum 礼物数量
type GiftNum struct {
	GoodsID int64 `gorm:"column:goods_id"`
	GiftID  int64 `gorm:"column:gift_id"`
	Num     int64 `gorm:"column:num"`
	UserNum int64 `gorm:"column:user_num"`
}

// EachGiftNum 每个礼物的数量
func EachGiftNum(goodsIDs []int64) (map[int64]map[int64]GiftNum, error) {
	var giftNums []GiftNum
	err := DB().Table(Record{}.TableName()).
		Select("goods_id, gift_id, COUNT(*) AS num, COUNT(DISTINCT(user_id)) AS user_num").
		Where("goods_id IN (?)", goodsIDs).
		Where("create_time >= ?", goutil.TimeNow().AddDate(0, 0, -7).Unix()).
		Group("goods_id, gift_id").
		Find(&giftNums).Error
	if err != nil {
		return nil, err
	}
	goodsGiftNumMap := make(map[int64]map[int64]GiftNum, len(giftNums)) // map[goods_id]map[gift_id]liveluckybox.GiftNum
	for _, num := range giftNums {
		if len(goodsGiftNumMap[num.GoodsID]) == 0 {
			goodsGiftNumMap[num.GoodsID] = make(map[int64]GiftNum)
		}
		goodsGiftNumMap[num.GoodsID][num.GiftID] = num
	}
	return goodsGiftNumMap, nil
}
