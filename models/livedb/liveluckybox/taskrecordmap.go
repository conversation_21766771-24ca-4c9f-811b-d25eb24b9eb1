package liveluckybox

// TaskRecordMap 宝盒任务记录映射
type TaskRecordMap struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	UserID      int64 `gorm:"column:user_id"`             // 用户 ID
	BoxTaskID   int64 `gorm:"column:lucky_box_task_id"`   // 任务 ID
	BoxRecordID int64 `gorm:"column:lucky_box_record_id"` // 宝盒记录 ID
}

// TableName .
func (TaskRecordMap) TableName() string {
	return "live_lucky_box_task_record_map"
}
