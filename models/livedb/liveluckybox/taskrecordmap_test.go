package liveluckybox

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTableName_TaskRecordMap(t *testing.T) {
	assert.Equal(t, "live_lucky_box_task_record", TaskRecord{}.TableName())
}

func TestTags_TaskRecordMap(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(TaskRecordMap{}, "id", "create_time", "modified_time", "user_id", "lucky_box_task_id", "lucky_box_record_id")
}
