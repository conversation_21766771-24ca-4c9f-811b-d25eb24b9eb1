package databus

import (
	"context"
	"encoding/json"
	"math/rand"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestDatabusTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(drawLuckyBoxMessage{}, "user_id", "room_id", "goods_id", "gift_pool")
}

func TestDrawLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	msg := drawLuckyBoxMessage{
		UserID:  rand.Int63(),
		RoomID:  rand.Int63(),
		GoodsID: rand.Int63(),
		GiftPool: &gift.PoolLuckyBox{
			PoolID: rand.Int63(),
		},
	}
	DrawLuckyBox(msg.UserID, msg.RoomID, msg.GoodsID, msg.GiftPool)

	msgs := service.DatabusPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.KeyDrawLiveLuckyBox1.Format(msg.UserID), m.Key)

	var message drawLuckyBoxMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(msg.UserID, message.UserID)
	assert.EqualValues(msg.RoomID, message.RoomID)
	assert.EqualValues(msg.GoodsID, message.GoodsID)
	assert.EqualValues(msg.GiftPool.PoolID, message.GiftPool.PoolID)
}

func TestLuckyBoxOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	f := LuckyBoxOperator()
	f(&databus.Message{})

	nowUnix := goutil.TimeNow().Unix()
	appearances, err := appearance.Find(bson.M{
		"start_time":  bson.M{"$lte": nowUnix},
		"expire_time": bson.M{"$not": bson.M{"$lte": nowUnix}},
	}, &options.FindOptions{Limit: goutil.NewInt64(1)})
	require.NoError(err)
	require.NotEmpty(appearances)
	var (
		testUserID       int64 = 181771
		testRoomID       int64 = 181772
		testGoodsID      int64 = 181773
		testPoolID       int64 = 123
		testAppearanceID       = appearances[0].ID
		giftPool               = &gift.PoolLuckyBox{
			Type:   gift.PoolTypeLuckyBox,
			PoolID: testPoolID,
			SSRID:  2,
			Rates: map[int64]int{
				1: 100,
				2: 100,
				3: 100,
			},
		}
	)

	// 测试环境初始化
	{
		// 配置抽取记录
		err := service.LiveDB.Delete(liveluckybox.TaskRecord{}, "user_id = ?", testUserID).Error
		require.NoError(err)
		records := []*liveluckybox.Record{
			{
				UserID:  testUserID,
				GoodsID: testGoodsID,
				GiftID:  1,
			},
		}
		err = servicedb.BatchInsert(service.LiveDB, liveluckybox.Record{}.TableName(), records)
		require.NoError(err)

		// 配置任务
		err = service.LiveDB.Delete(liveluckybox.TaskRecordMap{}, "user_id = ?", testUserID).Error
		require.NoError(err)
		err = service.LiveDB.Delete(liveluckybox.Task{}, "goods_id = ?", testGoodsID).Error
		require.NoError(err)
		tasks := []*liveluckybox.Task{
			{
				ID:            3,
				Sort:          3,
				GoodsID:       testGoodsID,
				Name:          "task",
				Intro:         "intro",
				Type:          liveluckybox.TaskTypeAnyNormal,
				Num:           1,
				PrizeID:       testAppearanceID,
				PrizeType:     liveluckybox.PrizeTypeAppearance,
				PrizeDuration: 100,
			},
		}
		err = servicedb.BatchInsert(service.LiveDB, liveluckybox.Task{}.TableName(), tasks)
		require.NoError(err)

		// 配置外观奖励
		_, err = userappearance.Collection().DeleteMany(context.TODO(), bson.M{
			"user_id":       testUserID,
			"appearance_id": testAppearanceID,
		})
		require.NoError(err)
	}

	// 处理消息，对测试用户开出的任意礼物进行发奖
	msg := drawLuckyBoxMessage{
		UserID:   testUserID,
		RoomID:   testRoomID,
		GoodsID:  testGoodsID,
		GiftPool: giftPool,
	}
	f(&databus.Message{
		Key:   keys.KeyDrawLiveLuckyBox1.Format(1),
		Value: json.RawMessage(tutil.SprintJSON(msg)),
	})

	// 1. record 被标记
	var records []*liveluckybox.Record
	err = service.LiveDB.Find(&records, "user_id = ?", testUserID).Error
	require.NoError(err)
	assert.Equal(1, len(records))

	// 2. taskRecords 被记录
	var taskRecords []*liveluckybox.TaskRecord
	err = service.LiveDB.Find(&taskRecords, "user_id = ?", testUserID).Error
	require.NoError(err)
	assert.Equal(1, len(taskRecords))

	// 3. 奖励被发送
	count, err := userappearance.Collection().CountDocuments(context.TODO(), bson.M{
		"user_id":       testUserID,
		"appearance_id": testAppearanceID,
	})
	require.NoError(err)
	assert.EqualValues(1, count)
}
