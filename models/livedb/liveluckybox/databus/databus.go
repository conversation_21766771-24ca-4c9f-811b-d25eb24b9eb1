package databus

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// drawLuckyBoxMessage 抽取宝盒时发送的消息
type drawLuckyBoxMessage struct {
	UserID   int64              `json:"user_id"`   // 用户 ID
	RoomID   int64              `json:"room_id"`   // 直播间 ID
	GoodsID  int64              `json:"goods_id"`  // 宝盒商品 ID
	GiftPool *gift.PoolLuckyBox `json:"gift_pool"` // 宝盒奖池
}

// DrawLuckyBox 抽盒时触发，通过 databus 异步判断任务完成情况并发放奖励
func DrawLuckyBox(userID, roomID, goodsID int64, giftPool *gift.PoolLuckyBox) {
	key := keys.KeyDrawLiveLuckyBox1.Format(userID)
	err := service.DatabusSend(key, drawLuckyBoxMessage{
		UserID:   userID,
		RoomID:   roomID,
		GoodsID:  goodsID,
		GiftPool: giftPool,
	})
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  userID,
			"goods_id": goodsID,
		}).Errorf("failed to send drawLuckyBoxMessage: %v", err)
	}
}

// LuckyBoxOperator 确认任务完成情况，并发放奖励
func LuckyBoxOperator() func(*databus.Message) {
	return func(message *databus.Message) {
		if !keys.KeyDrawLiveLuckyBox1.MatchKey(message.Key) {
			return
		}

		var msg drawLuckyBoxMessage
		err := json.Unmarshal(message.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}

		var (
			taskInfos []liveluckybox.TaskInfo
			now       = goutil.TimeNow()
		)
		err = servicedb.Tx(liveluckybox.DB(), func(tx *gorm.DB) error {
			taskInfos, err = liveluckybox.UserCompletedTasks(tx, msg.GoodsID, msg.UserID, msg.GiftPool)
			if err != nil {
				return err
			}
			if len(taskInfos) == 0 {
				return nil
			}
			taskRecords := make([]*liveluckybox.TaskRecord, 0, len(taskInfos))
			taskRecordMaps := make([]*liveluckybox.TaskRecordMap, 0, len(taskInfos))
			for _, taskInfo := range taskInfos {
				for i := 0; i < taskInfo.Num; i++ {
					taskRecords = append(taskRecords, &liveluckybox.TaskRecord{
						CreateTime:   now.Unix(),
						ModifiedTime: now.Unix(),
						BoxTaskID:    taskInfo.Task.ID,
						UserID:       msg.UserID,
						RoomID:       msg.RoomID,
					})
				}
				for _, recordID := range taskInfo.RecordIDs {
					taskRecordMaps = append(taskRecordMaps, &liveluckybox.TaskRecordMap{
						CreateTime:   now.Unix(),
						ModifiedTime: now.Unix(),
						UserID:       msg.UserID,
						BoxTaskID:    taskInfo.Task.ID,
						BoxRecordID:  recordID,
					})
				}
			}
			err = servicedb.BatchInsert(tx, liveluckybox.TaskRecord{}.TableName(), taskRecords)
			if err != nil {
				return err
			}
			err = servicedb.BatchInsert(tx, liveluckybox.TaskRecordMap{}.TableName(), taskRecordMaps)
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":  msg.UserID,
				"goods_id": msg.GoodsID,
			}).Errorf("failed to mark records for tasks: %v", err)
			return
		}

		for _, taskInfo := range taskInfos {
			err = taskInfo.Task.SendPrize(msg.UserID, taskInfo.Num)
			if err != nil {
				logger.WithFields(logger.Fields{
					"user_id":   msg.UserID,
					"goods_id":  msg.GoodsID,
					"task_id":   taskInfo.Task.ID,
					"prize_id":  taskInfo.Task.PrizeID,
					"prize_num": taskInfo.Num,
				}).Errorf("failed to send live lucky box prize: %v", err)
				return
			}
		}
	}
}
