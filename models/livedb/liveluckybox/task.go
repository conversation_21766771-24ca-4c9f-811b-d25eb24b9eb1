package liveluckybox

import (
	"fmt"
	"slices"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// TaskType 任务类型
const (
	TaskTypeFullNormal = iota + 1 // 收集全套普通礼物
	TaskTypeTarget                // 收集目标礼物
	TaskTypeAnyNormal             // 收集任意普通礼物
)

// PrizeType 奖品类型
const (
	PrizeTypeGift       = iota + 1 // 礼物
	PrizeTypeAppearance            // 外观
)

// Task 宝盒任务
type Task struct {
	ID           int64 `gorm:"column:id"`            // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 更新时间，单位：秒

	Sort          int    `gorm:"column:sort"`           // 排序
	GoodsID       int64  `gorm:"column:goods_id"`       // 商品 ID
	Name          string `gorm:"column:name"`           // 任务名称
	Intro         string `gorm:"column:intro"`          // 任务介绍
	Type          int    `gorm:"column:type"`           // 类型
	Num           int    `gorm:"column:num"`            // 收集数量
	GiftID        int64  `gorm:"column:gift_id"`        // 收集礼物 ID
	PrizeID       int64  `gorm:"column:prize_id"`       // 奖品 ID
	PrizeType     int    `gorm:"column:prize_type"`     // 奖品类型，1：礼物，2：外观
	PrizeName     string `gorm:"column:prize_name"`     // 奖品名称
	PrizeIcon     string `gorm:"column:prize_icon"`     // 奖品图标
	PrizeDuration int64  `gorm:"column:prize_duration"` // 奖品有效期，单位：秒

	PrizeIconURL string `gorm:"-"` // 奖品图标 URL
}

// TableName .
func (Task) TableName() string {
	return "live_lucky_box_task"
}

// AfterFind gorm 钩子
func (t *Task) AfterFind() error {
	if t.PrizeIcon != "" {
		t.PrizeIconURL = storage.ParseSchemeURL(t.PrizeIcon)
	}
	return nil
}

// FindTasks 查询宝箱下的所有任务
func FindTasks(goodsID int64) ([]*Task, error) {
	var tasks []*Task
	err := DB().
		Where("goods_id = ? AND sort > 0", goodsID).
		Order("sort ASC").
		Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

// TaskInfo 任务信息
type TaskInfo struct {
	Task      *Task
	GiftID    int64   // 完成任务礼物 ID
	Num       int     // 完成次数
	RecordIDs []int64 // 完成任务的记录 ID
}

// UserCompletedTasks 查询用户当前可完成的任务信息
func UserCompletedTasks(db *gorm.DB, goodsID, userID int64, pool *gift.PoolLuckyBox) ([]TaskInfo, error) {
	tasks, err := FindTasks(goodsID)
	if err != nil {
		return nil, err
	}
	if len(tasks) == 0 {
		return nil, nil
	}
	taskRecordMap := make(map[int64][]*ValidRecord, len(tasks))
	for _, task := range tasks {
		taskRecordMap[task.ID], err = FindUserValidRecords(db, goodsID, userID, task.ID)
		if err != nil {
			return nil, err
		}
	}

	completedTasks := make([]TaskInfo, 0, len(tasks))
	for _, task := range tasks {
		records, ok := taskRecordMap[task.ID]
		if !ok || len(records) == 0 {
			continue
		}
		giftRecordMap := make(map[int64][]*ValidRecord, len(records))
		for _, record := range records {
			giftRecordMap[record.GiftID] = append(giftRecordMap[record.GiftID], record)
		}
		if task.Num <= 0 {
			logger.WithFields(logger.Fields{
				"task_id": task.ID,
				"num":     task.Num,
			}).Errorf("task num is invalid")
			continue
		}
		switch task.Type {
		case TaskTypeFullNormal:
			sets := make([]int, 0, len(pool.Rates))
			for giftID := range pool.Rates {
				if pool.IsSSRID(giftID) { // SSR ID 为典藏款礼物，不统计
					continue
				}
				sets = append(sets, len(giftRecordMap[giftID])/task.Num) // 计算每种礼物完成次数
			}
			if num := slices.Min(sets); num > 0 {
				info := TaskInfo{
					Task:      task,
					Num:       num,
					RecordIDs: make([]int64, 0, len(sets)*num*task.Num),
				}
				for giftID := range pool.Rates {
					if pool.IsSSRID(giftID) { // SSR ID 为典藏款礼物，不统计
						continue
					}
					for i := 0; i < num*task.Num; i++ {
						info.RecordIDs = append(info.RecordIDs, giftRecordMap[giftID][i].RecordID)
					}
				}
				completedTasks = append(completedTasks, info)
			}
		case TaskTypeTarget:
			if num := len(giftRecordMap[task.GiftID]) / task.Num; num > 0 {
				info := TaskInfo{
					Task:      task,
					GiftID:    task.GiftID,
					Num:       num,
					RecordIDs: make([]int64, 0, num),
				}
				for i := 0; i < num; i++ {
					info.RecordIDs = append(info.RecordIDs, giftRecordMap[pool.SSRID][i].RecordID)
				}
				completedTasks = append(completedTasks, info)
			}
		case TaskTypeAnyNormal:
			// NOTICE: 该任务礼物只统计普通款礼物，不统计典藏款礼物（典藏款礼物 ID 是 draw_gift_pool 的 SSR ID）
			for giftID := range pool.Rates {
				if pool.IsSSRID(giftID) { // SSR ID 为典藏款礼物，不统计
					continue
				}
				if num := len(giftRecordMap[giftID]) / task.Num; num > 0 {
					info := TaskInfo{
						Task:      task,
						GiftID:    giftID,
						Num:       num,
						RecordIDs: make([]int64, 0, num*task.Num),
					}
					for i := 0; i < num*task.Num; i++ {
						info.RecordIDs = append(info.RecordIDs, giftRecordMap[giftID][i].RecordID)
					}
					completedTasks = append(completedTasks, info)
				}
			}
		}
	}
	return completedTasks, nil
}

// SendPrize 发送奖品
func (t Task) SendPrize(userID int64, num int) error {
	switch t.PrizeType {
	case PrizeTypeGift:
	// PASS: 暂时无该类型奖品
	case PrizeTypeAppearance:
		a, err := appearance.FindOne(t.PrizeID, appearance.TypeNoSpecified)
		if err != nil {
			return err
		}
		if a == nil {
			return fmt.Errorf("can not find appearance %d", t.PrizeID)
		}
		err = userappearance.AddAppearance(userID, t.PrizeDuration*int64(num), 0, a)
		if err != nil {
			return err
		}
	default:
		return fmt.Errorf("unknown prize type %d", t.PrizeType)
	}
	return nil
}
