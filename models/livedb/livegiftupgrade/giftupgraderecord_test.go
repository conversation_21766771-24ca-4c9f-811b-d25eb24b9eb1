package livegiftupgrade

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGiftUpgradeRecord_Tags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.GORM).
		Check(GiftUpgradeRecord{}, "id", "create_time", "modified_time", "base_gift_id", "upgrade_gift_id", "user_id", "room_id")
}

func TestIsUnlockUpgradeGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().Unix()
	userID, baseGiftID, upgradeGiftID := int64(123), int64(1), int64(2)
	err := DB().Delete(GiftUpgradeRecord{}).Error
	require.NoError(err)

	records := []GiftUpgradeRecord{
		{BaseGiftID: baseGiftID, UpgradeGiftID: upgradeGiftID, UserID: userID, CreateTime: now},
	}
	err = servicedb.BatchInsert(DB(), GiftUpgradeRecord{}.TableName(), records)
	require.NoError(err)

	unlocked, err := IsUnlockUpgradeGift(userID, baseGiftID, now, upgradeGiftID)
	require.NoError(err)
	assert.True(unlocked)

	unlocked, err = IsUnlockUpgradeGift(userID, baseGiftID, now, upgradeGiftID+1)
	require.NoError(err)
	assert.False(unlocked)
}

func TestFindGiftUpgradeRecordsByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID, baseGiftID := int64(123), int64(1)
	err := DB().Delete(GiftUpgradeRecord{}).Error
	require.NoError(err)

	res, err := FindGiftUpgradeRecordsMapByUserID(userID, baseGiftID, 0)
	require.NoError(err)
	assert.Len(res, 0)

	records := []GiftUpgradeRecord{
		{BaseGiftID: baseGiftID, UpgradeGiftID: 2, UserID: userID, CreateTime: 0},
		{BaseGiftID: baseGiftID, UpgradeGiftID: 3, UserID: userID, CreateTime: 1},
		{BaseGiftID: baseGiftID, UpgradeGiftID: 4, UserID: userID, CreateTime: 2},
		{BaseGiftID: 5, UpgradeGiftID: 6, UserID: userID},
		{BaseGiftID: 5, UpgradeGiftID: 6, UserID: int64(124)},
	}
	err = servicedb.BatchInsert(DB(), GiftUpgradeRecord{}.TableName(), records)
	require.NoError(err)

	res, err = FindGiftUpgradeRecordsMapByUserID(userID, baseGiftID, 0)
	require.NoError(err)
	assert.Len(res, 3)

	res, err = FindGiftUpgradeRecordsMapByUserID(userID, baseGiftID, 1)
	require.NoError(err)
	assert.Len(res, 2)
}

func TestFindBaseGiftRecordsMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testBaseGiftIDs = []int64{1, 2}
		testUserID      = int64(1)
		now             = goutil.TimeNow().Unix()
	)
	require.NoError(DB().Where("user_id = ? AND base_gift_id IN (?)", testUserID, testBaseGiftIDs).Delete(&GiftUpgradeRecord{}).Error)
	records := []*GiftUpgradeRecord{
		{
			BaseGiftID:    testBaseGiftIDs[0],
			UpgradeGiftID: 1,
			UserID:        testUserID,
			CreateTime:    now,
		},
		{
			BaseGiftID:    testBaseGiftIDs[0],
			UpgradeGiftID: 2,
			UserID:        testUserID,
			CreateTime:    now,
		},
		{
			BaseGiftID:    testBaseGiftIDs[0],
			UpgradeGiftID: 3,
			UserID:        testUserID,
			CreateTime:    now - 100,
		},
		{
			BaseGiftID:    testBaseGiftIDs[1],
			UpgradeGiftID: 4,
			UserID:        testUserID,
			CreateTime:    now,
		},
		{
			BaseGiftID:    testBaseGiftIDs[1],
			UpgradeGiftID: 4,
			UserID:        testUserID,
			CreateTime:    now + 10,
		},
	}
	err := servicedb.BatchInsert(DB(), GiftUpgradeRecord{}.TableName(), records)
	require.NoError(err)

	resultMap, err := FindBaseGiftRecordsMap(testUserID, []usermeta.BaseGift{
		{GiftID: testBaseGiftIDs[0], ToggleTime: now},
		{GiftID: testBaseGiftIDs[1], ToggleTime: now},
	})
	require.NoError(err)
	assert.Len(resultMap[testBaseGiftIDs[0]], 2)
	assert.Len(resultMap[testBaseGiftIDs[1]], 1)
}
