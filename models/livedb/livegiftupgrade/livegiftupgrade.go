package livegiftupgrade

import (
	"fmt"
	"sort"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
)

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// LuckyScore 幸运值信息
type LuckyScore struct {
	Current      int `json:"current"`
	Target       int `json:"target"`
	PerIncrement int `json:"per_increment"`
}

// Discount 折扣信息
type Discount struct {
	LabelIconURL string `json:"label_icon_url"`
}

// UpgradeNum 升级次数信息
type UpgradeNum struct {
	RemainUpgradeNum int `json:"remain_upgrade_num"`
	UpgradeGiftNum   int `json:"upgrade_gift_num"`
	GiftNum          int `json:"gift_num"`
}

// GiftInfo 升级礼物信息
type GiftInfo struct {
	UpgradeType   int    `json:"upgrade_type"`
	Type          int    `json:"type"`
	GiftID        int64  `json:"gift_id"`
	Name          string `json:"name"`
	IconURL       string `json:"icon_url"`
	IconActiveURL string `json:"icon_active_url,omitempty"`
	SmallIconURL  string `json:"small_icon_url,omitempty"` // 升级栏礼物小图标
	Price         int64  `json:"price"`
	Comboable     int    `json:"comboable,omitempty"`
	Lock          int    `json:"lock,omitempty"`
	Rate          string `json:"rate,omitempty"`

	weight int
}

// updateRate 返回根据权重计算出来的掉落概率的百分比字符串
func (gi *GiftInfo) updateRate(totalWeight int) {
	gi.Rate = fmt.Sprintf("%.2g%%", float64(gi.weight)/float64(totalWeight)*100)
}

// UpgradeInfo 升级信息
type UpgradeInfo struct {
	SeriesName   string      `json:"series_name,omitempty"`
	IntroOpenURL string      `json:"intro_open_url,omitempty"`
	LuckyScore   *LuckyScore `json:"lucky_score,omitempty"`
	Discount     *Discount   `json:"discount,omitempty"`
	UpgradeNum   *UpgradeNum `json:"upgrade_num,omitempty"`
	Gifts        []GiftInfo  `json:"gifts,omitempty"`
}

// BuildSeriesName 构建系列名
func BuildSeriesName(baseGiftUpgrade *GiftUpgrade) string {
	return baseGiftUpgrade.MoreInfo.SeriesName
}

// BuildDiscountIcon 构建折扣图标返回数据
func BuildDiscountIcon(upgrade *usermeta.GiftUpgrade, baseGiftUpgrade *GiftUpgrade) *Discount {
	if baseGiftUpgrade == nil || baseGiftUpgrade.MoreInfo == nil || baseGiftUpgrade.MoreInfo.LabelIcon == "" {
		return nil
	}
	if upgrade != nil && upgrade.SendCount >= baseGiftUpgrade.MoreInfo.FirstUpgradeNum {
		return nil
	}
	return &Discount{
		LabelIconURL: baseGiftUpgrade.MoreInfo.LabelIcon,
	}
}

// BuildIntro 构建升级规则信息
func BuildIntro(baseGiftUpgrade *GiftUpgrade) string {
	return baseGiftUpgrade.MoreInfo.IntroOpenURL
}

// BuildLuckyScore 构建幸运值返回数据
func BuildLuckyScore(upgrade *usermeta.GiftUpgrade, baseGiftUpgrade *GiftUpgrade) *LuckyScore {
	luckyScore := &LuckyScore{
		Target:       baseGiftUpgrade.MoreInfo.LuckyScoreTarget,
		PerIncrement: baseGiftUpgrade.MoreInfo.LuckyScorePerIncr,
	}
	if upgrade == nil {
		return luckyScore
	}
	luckyScore.Current = upgrade.LuckyScore
	return luckyScore
}

// BuildUpgradeNum 构建升级次数的返回数据
func BuildUpgradeNum(upgrade *usermeta.GiftUpgrade, baseGiftUpgrade *GiftUpgrade) *UpgradeNum {
	upgradeNum := &UpgradeNum{}
	// 升级所需的送礼次数
	upgradeNum.UpgradeGiftNum = baseGiftUpgrade.MoreInfo.FirstUpgradeNum
	if upgrade == nil {
		return upgradeNum
	}
	if upgrade.SendCount >= baseGiftUpgrade.MoreInfo.FirstUpgradeNum {
		upgradeNum.UpgradeGiftNum = baseGiftUpgrade.MoreInfo.UpgradeNum
	}
	// 剩余可升级次数
	upgradeNum.RemainUpgradeNum = upgrade.UpgradeNum
	// 本次升级已累积赠送礼物次数
	upgradeNum.GiftNum = upgrade.SendCount
	if upgrade.SendCount >= baseGiftUpgrade.MoreInfo.FirstUpgradeNum {
		// 如果送礼次数大于首次升级需要的送礼次数，减去首次送礼次数之后对每次升级需要的送礼次数取余，得到此次升级的送礼次数
		upgradeNum.GiftNum = (upgradeNum.GiftNum - baseGiftUpgrade.MoreInfo.FirstUpgradeNum) % baseGiftUpgrade.MoreInfo.UpgradeNum
	}
	return upgradeNum
}

// BuildGiftInfos 构造升级礼物信息返回数据
func BuildGiftInfos(giftUpgradeMap map[int64]*GiftUpgrade, giftUpgradeRecordMap map[int64]*GiftUpgradeRecord, giftIDMap map[int64]*gift.Gift) []GiftInfo {
	totalWeight := 0
	for _, gu := range giftUpgradeMap {
		totalWeight += gu.Weight
	}

	gfInfos := make([]GiftInfo, 0, len(giftIDMap))
	for giftID, gf := range giftIDMap {
		gfUpgrade, ok := giftUpgradeMap[giftID]
		if !ok {
			continue
		}
		gi := GiftInfo{
			UpgradeType:   gfUpgrade.Type,
			Type:          gf.Type,
			GiftID:        giftID,
			Name:          gf.Name,
			IconURL:       gf.Icon,
			IconActiveURL: gf.IconActive,
			SmallIconURL:  gfUpgrade.GiftSmallIcon,
			Price:         gf.Price,
			Comboable:     gf.Comboable,
			weight:        gfUpgrade.Weight,
		}
		if gfUpgrade.Type == GiftUpgradeTypeUpgrade {
			// 升级礼物需要格式化概率返回
			gi.updateRate(totalWeight)
		}
		if gi.UpgradeType != GiftUpgradeTypeBase && giftUpgradeRecordMap[giftID] == nil {
			gi.Lock = 1
		}
		gfInfos = append(gfInfos, gi)
	}
	sort.Slice(gfInfos, func(i, j int) bool {
		// 按类型排序，基础礼物 -> 终极礼物 -> 升级礼物
		if gfInfos[i].UpgradeType != gfInfos[j].UpgradeType {
			return gfInfos[i].UpgradeType < gfInfos[j].UpgradeType
		}
		// 升级礼物按掉率从小到大排序，掉率相同按礼物 ID 排序
		if gfInfos[i].weight != gfInfos[j].weight {
			return gfInfos[i].weight < gfInfos[j].weight
		}
		return gfInfos[i].GiftID < gfInfos[j].GiftID
	})
	return gfInfos
}

// IsAllCollected 是否收集完所有升级礼物
func IsAllCollected(giftUpgradeMap map[int64]*GiftUpgrade, giftUpgradeRecordMap map[int64]*GiftUpgradeRecord) bool {
	for giftID, gu := range giftUpgradeMap {
		if gu.Type == GiftUpgradeTypeBase {
			continue
		}
		if _, ok := giftUpgradeRecordMap[giftID]; !ok {
			return false
		}
	}
	return true
}
