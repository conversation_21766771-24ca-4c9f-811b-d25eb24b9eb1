package livegiftupgrade

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestBuildIntro(t *testing.T) {
	assert := assert.New(t)

	baseGiftUpgrade := &GiftUpgrade{MoreInfo: &MoreInfo{IntroOpenURL: "http://example.com"}}
	assert.Equal("http://example.com", BuildIntro(baseGiftUpgrade))
}

func TestBuildLuckyScore(t *testing.T) {
	assert := assert.New(t)

	upgrade := &usermeta.GiftUpgrade{
		LuckyScore: 10,
	}
	baseGiftUpgrade := &GiftUpgrade{Type: GiftUpgradeTypeBase, BaseGiftID: 1, MoreInfo: &MoreInfo{LuckyScoreTarget: 100, LuckyScorePerIncr: 10}}
	luckScore := BuildLuckyScore(upgrade, baseGiftUpgrade)
	assert.Equal(upgrade.LuckyScore, luckScore.Current)
	assert.Equal(100, luckScore.Target)
	assert.Equal(10, luckScore.PerIncrement)
}

func TestBuildUpgradeNum(t *testing.T) {
	assert := assert.New(t)

	upgrade := &usermeta.GiftUpgrade{
		SendCount:  2,
		UpgradeNum: 0,
	}
	baseGiftUpgrade := &GiftUpgrade{Type: GiftUpgradeTypeBase, BaseGiftID: 1, MoreInfo: &MoreInfo{FirstUpgradeNum: 3, UpgradeNum: 10}}

	upgradeNum := BuildUpgradeNum(nil, baseGiftUpgrade)
	assert.Equal(0, upgradeNum.GiftNum)
	assert.Equal(0, upgradeNum.RemainUpgradeNum)
	assert.Equal(3, upgradeNum.UpgradeGiftNum)

	upgradeNum = BuildUpgradeNum(upgrade, baseGiftUpgrade)
	assert.Equal(2, upgradeNum.GiftNum)
	assert.Equal(upgrade.UpgradeNum, upgradeNum.RemainUpgradeNum)
	assert.Equal(3, upgradeNum.UpgradeGiftNum)

	upgrade.SendCount = 20
	upgrade.UpgradeNum = 2
	upgradeNum = BuildUpgradeNum(upgrade, baseGiftUpgrade)
	assert.Equal(7, upgradeNum.GiftNum)
	assert.Equal(upgrade.UpgradeNum, upgradeNum.RemainUpgradeNum)
	assert.Equal(10, upgradeNum.UpgradeGiftNum)
}

func TestBuildGiftInfos(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	giftUpgradeMap := map[int64]*GiftUpgrade{
		1: {Type: GiftUpgradeTypeBase, BaseGiftID: 1},
		3: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 3, Weight: 30},
		4: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 4, Weight: 20},
		5: {Type: GiftUpgradeTypeFull, BaseGiftID: 1, UpgradeGiftID: 5},
		2: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 2, Weight: 50},
	}
	giftUpgradeRecordMap := map[int64]*GiftUpgradeRecord{
		4: {BaseGiftID: 1, UpgradeGiftID: 4},
	}
	giftIDMap := map[int64]*gift.Gift{
		1: {GiftID: 1, Name: "g1", Icon: "icon1"},
		2: {GiftID: 2, Name: "g2", Icon: "icon2"},
		3: {GiftID: 3, Name: "g3", Icon: "icon3"},
		4: {GiftID: 4, Name: "g4", Icon: "icon4"},
		5: {GiftID: 5, Name: "g5", Icon: "icon5"},
	}
	giftInfos := BuildGiftInfos(giftUpgradeMap, giftUpgradeRecordMap, giftIDMap)
	require.Len(giftInfos, 5)
	assert.Equal(GiftUpgradeTypeBase, giftInfos[0].UpgradeType)
	assert.Equal(GiftUpgradeTypeFull, giftInfos[1].UpgradeType)
	for i := 2; i < len(giftInfos)-1; i++ {
		if giftUpgradeRecordMap[giftInfos[i].GiftID] != nil {
			assert.Equal(0, giftInfos[i].Lock)
		} else {
			assert.Equal(1, giftInfos[i].Lock)
		}
		assert.LessOrEqual(giftInfos[i].weight, giftInfos[i+1].weight)
	}
}

func TestIsAllCollected(t *testing.T) {
	assert := assert.New(t)

	giftUpgradeMap := map[int64]*GiftUpgrade{
		1: {Type: GiftUpgradeTypeBase, BaseGiftID: 1},
		3: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 3, Weight: 30},
		4: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 4, Weight: 20},
		2: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 2, Weight: 50},
		5: {Type: GiftUpgradeTypeFull, BaseGiftID: 1, UpgradeGiftID: 5},
	}
	giftUpgradeRecordMap := map[int64]*GiftUpgradeRecord{
		2: {BaseGiftID: 1, UpgradeGiftID: 2},
		4: {BaseGiftID: 1, UpgradeGiftID: 4},
	}

	assert.False(IsAllCollected(giftUpgradeMap, giftUpgradeRecordMap))
	giftUpgradeRecordMap[3] = &GiftUpgradeRecord{BaseGiftID: 1, UpgradeGiftID: 3}
	giftUpgradeRecordMap[5] = &GiftUpgradeRecord{BaseGiftID: 1, UpgradeGiftID: 5}
	assert.True(IsAllCollected(giftUpgradeMap, giftUpgradeRecordMap))
}
