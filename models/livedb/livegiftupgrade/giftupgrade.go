package livegiftupgrade

import (
	"encoding/json"

	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// GiftUpgradeType 类型
const (
	GiftUpgradeTypeBase    = iota + 1 // 基础（无需解锁默认拥有）
	GiftUpgradeTypeFull               // 终极（解锁所有升级礼物后获得）
	GiftUpgradeTypeUpgrade            // 升级（满足条件后概率获得）
)

var (
	randSource = goutil.NewLockedSource(goutil.TimeNow().Unix())
)

// GiftUpgrade 礼物升级
type GiftUpgrade struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 单位：秒

	Type          int    `gorm:"column:type"`
	BaseGiftID    int64  `gorm:"column:base_gift_id"`
	UpgradeGiftID int64  `gorm:"column:upgrade_gift_id"`
	GiftSmallIcon string `gorm:"column:gift_small_icon"` // 礼物小图标
	Weight        int    `gorm:"column:weight"`
	PrizeID       int64  `gorm:"column:prize_id"`
	More          []byte `gorm:"column:more"`

	MoreInfo *MoreInfo `gorm:"-"`
}

// MoreInfo .
type MoreInfo struct {
	// 仅基础礼物配置
	IntroOpenURL      string `json:"intro_open_url"`
	SeriesName        string `json:"series_name"`          // 升级礼物的系列名
	FirstUpgradeNum   int    `json:"first_upgrade_num"`    // 首次升级所需数量
	UpgradeNum        int    `json:"upgrade_num"`          // 正常升级所需次数
	LabelIcon         string `json:"label_icon"`           // 角标，如：首次特惠
	LuckyScoreTarget  int    `json:"lucky_score_target"`   // 目标幸运值
	LuckyScorePerIncr int    `json:"lucky_score_per_incr"` // 升级获取重复礼物时增长的幸运值
}

// TableName .
func (GiftUpgrade) TableName() string {
	return "live_gift_upgrade"
}

// AfterFind .
func (g *GiftUpgrade) AfterFind() error {
	g.GiftSmallIcon = storage.ParseSchemeURL(g.GiftSmallIcon)
	if len(g.More) > 0 {
		err := json.Unmarshal(g.More, &g.MoreInfo)
		if err != nil {
			logger.WithFields(logger.Fields{"id": g.ID}).Error(err)
			return err
		}
		g.MoreInfo.LabelIcon = storage.ParseSchemeURL(g.MoreInfo.LabelIcon)
	}
	return nil
}

// FindGiftUpgradeList 通过 base_gift_id 查询对应的升级礼物信息列表
func FindGiftUpgradeList(baseGiftID int64) ([]*GiftUpgrade, error) {
	var gifts []*GiftUpgrade
	err := DB().Find(&gifts, "base_gift_id = ?", baseGiftID).Error
	if err != nil {
		return nil, err
	}
	return gifts, nil
}

// FindGiftUpgradeMap 通过 base_gift_id 查询对应 base upgrade 和 upgrade gift 的 map
func FindGiftUpgradeMap(baseGiftID int64) (*GiftUpgrade, map[int64]*GiftUpgrade, error) {
	giftUpgradeMap, err := FindUpgradeGiftsMap([]int64{baseGiftID})
	if err != nil {
		return nil, nil, err
	}
	upgradeMap, ok := giftUpgradeMap[baseGiftID]
	if !ok {
		return nil, nil, nil
	}
	return upgradeMap[baseGiftID], upgradeMap, nil
}

// FindUpgradeGiftsMap 根据基础礼物 IDs 查询所有升级礼物
// 返回 map[baseGiftID]map[giftID]*GiftUpgrade
func FindUpgradeGiftsMap(baseGiftIDs []int64) (map[int64]map[int64]*GiftUpgrade, error) {
	var giftUpgrades []*GiftUpgrade
	err := DB().Where("base_gift_id IN (?)", baseGiftIDs).Find(&giftUpgrades).Error
	if err != nil {
		return nil, err
	}

	result := make(map[int64]map[int64]*GiftUpgrade)
	for _, gu := range giftUpgrades {
		if _, ok := result[gu.BaseGiftID]; !ok {
			result[gu.BaseGiftID] = make(map[int64]*GiftUpgrade)
		}
		if gu.Type == GiftUpgradeTypeBase {
			result[gu.BaseGiftID][gu.BaseGiftID] = gu
		} else {
			result[gu.BaseGiftID][gu.UpgradeGiftID] = gu
		}
	}

	return result, nil
}

// GetUpgradeGiftID 根据幸运值判断此次升级获取的礼物
func GetUpgradeGiftID(giftUpgradeMap map[int64]*GiftUpgrade, giftUpgradeRecordMap map[int64]*GiftUpgradeRecord, luckyScore, luckScoreTarget int) (int64, error) {
	keys := make([]int64, 0, len(giftUpgradeRecordMap))
	weights := make([]int, 0, len(giftUpgradeRecordMap))

	for _, gu := range giftUpgradeMap {
		if gu.Type != GiftUpgradeTypeUpgrade {
			// 只从升级类型的礼物抽取
			continue
		}
		record := giftUpgradeRecordMap[gu.UpgradeGiftID]
		if luckyScore == luckScoreTarget && record != nil {
			// 幸运值达到目标值之后，获取未获取过的礼物
			continue
		}
		keys = append(keys, gu.UpgradeGiftID)
		weights = append(weights, gu.Weight)
	}
	d, err := goutil.NewDiscreteDistribution(weights, randSource, false)
	if err != nil {
		return 0, err
	}
	upgradeGiftID := keys[d.NextInt()]
	return upgradeGiftID, nil
}

// FindByUpgradeGiftID 根据升级礼物 ID 查询对应的升级礼物信息
func FindByUpgradeGiftID(upgradeGiftID int64) (*GiftUpgrade, error) {
	var record GiftUpgrade
	err := DB().Where("upgrade_gift_id = ? AND type IN (?)", upgradeGiftID, []int{GiftUpgradeTypeUpgrade, GiftUpgradeTypeFull}).Take(&record).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &record, nil
}
