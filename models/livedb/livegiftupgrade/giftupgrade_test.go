package livegiftupgrade

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGiftUpgrade_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(GiftUpgrade{}, "id", "create_time", "modified_time", "type", "base_gift_id", "upgrade_gift_id", "gift_small_icon", "weight", "prize_id", "more")
	kc.Check(MoreInfo{}, "intro_open_url", "series_name", "first_upgrade_num", "upgrade_num", "label_icon", "lucky_score_target", "lucky_score_per_incr")
}

func TestFindGiftUpgradeList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := DB().Delete(GiftUpgrade{}).Error
	require.NoError(err)

	baseGiftID := int64(1)
	giftList, err := FindGiftUpgradeList(baseGiftID)
	require.NoError(err)
	assert.Len(giftList, 0)

	moreInfo := MoreInfo{
		FirstUpgradeNum: 3,
		UpgradeNum:      10,
		LabelIcon:       "oss://icon.png",
	}
	moreInfoBytes, err := json.Marshal(moreInfo)
	require.NoError(err)
	guRecords := []GiftUpgrade{
		{Type: GiftUpgradeTypeBase, BaseGiftID: baseGiftID, More: moreInfoBytes},
		{Type: GiftUpgradeTypeFull, BaseGiftID: baseGiftID, UpgradeGiftID: 3},
		{Type: GiftUpgradeTypeUpgrade, BaseGiftID: baseGiftID, UpgradeGiftID: 4},
		{Type: GiftUpgradeTypeUpgrade, BaseGiftID: baseGiftID, UpgradeGiftID: 5},
		{Type: GiftUpgradeTypeUpgrade, BaseGiftID: baseGiftID, UpgradeGiftID: 6},
		{Type: GiftUpgradeTypeBase, BaseGiftID: 2},
		{Type: GiftUpgradeTypeUpgrade, BaseGiftID: 2, UpgradeGiftID: 7},
	}
	err = servicedb.BatchInsert(DB(), GiftUpgrade{}.TableName(), guRecords)
	require.NoError(err)
	giftList, err = FindGiftUpgradeList(baseGiftID)
	require.NoError(err)
	assert.Len(giftList, 5)
}

func TestGetUpgradeGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	baseGiftID := int64(1)
	giftUpgradeMap := map[int64]*GiftUpgrade{
		1: {Type: GiftUpgradeTypeBase, BaseGiftID: baseGiftID},
		2: {Type: GiftUpgradeTypeFull, BaseGiftID: baseGiftID, UpgradeGiftID: 2},
		3: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: baseGiftID, UpgradeGiftID: 3, Weight: 100},
		4: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: baseGiftID, UpgradeGiftID: 4, Weight: 0},
		5: {Type: GiftUpgradeTypeUpgrade, BaseGiftID: baseGiftID, UpgradeGiftID: 5, Weight: 0},
	}
	giftUpgradeRecordMap := map[int64]*GiftUpgradeRecord{}
	// 只对 3 设置权重，一定返回 3
	upgradeGiftID, err := GetUpgradeGiftID(giftUpgradeMap, giftUpgradeRecordMap, 0, 100)
	require.NoError(err)
	assert.Equal(int64(3), upgradeGiftID)

	// 将 3, 4 标记为已拥有，验证幸运值不同情况下的结果
	giftUpgradeMap[3].Weight = 10
	giftUpgradeMap[4].Weight = 10
	giftUpgradeMap[5].Weight = 10
	giftUpgradeRecordMap[3] = &GiftUpgradeRecord{}
	giftUpgradeRecordMap[4] = &GiftUpgradeRecord{}
	// 幸运值未满的情况下，可能返回任何礼物
	upgradeGiftID, err = GetUpgradeGiftID(giftUpgradeMap, giftUpgradeRecordMap, 50, 100)
	require.NoError(err)
	assert.Contains([]int64{3, 4, 5}, upgradeGiftID)
	// 幸运值达到目标值后，返回未拥有的礼物
	upgradeGiftID, err = GetUpgradeGiftID(giftUpgradeMap, giftUpgradeRecordMap, 100, 100)
	require.NoError(err)
	assert.Equal(int64(5), upgradeGiftID)
}

func TestFindUpgradeGiftsMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(&GiftUpgrade{}).Error)

	gifts := []*GiftUpgrade{
		{
			Type:          GiftUpgradeTypeBase,
			BaseGiftID:    1,
			UpgradeGiftID: 1,
			Weight:        0,
		},
		{
			Type:          GiftUpgradeTypeUpgrade,
			BaseGiftID:    1,
			UpgradeGiftID: 2,
			Weight:        1,
		},
		{
			Type:          GiftUpgradeTypeFull,
			BaseGiftID:    1,
			UpgradeGiftID: 3,
			Weight:        1,
		},
		{
			Type:          GiftUpgradeTypeBase,
			BaseGiftID:    2,
			UpgradeGiftID: 1,
			Weight:        1,
		},
	}
	err := servicedb.BatchInsert(DB(), GiftUpgrade{}.TableName(), gifts)
	require.NoError(err)

	resultMap, err := FindUpgradeGiftsMap([]int64{1, 2})
	require.NoError(err)
	assert.Len(resultMap, 2)
	assert.Len(resultMap[1], 3)
	assert.Len(resultMap[2], 1)
}

func TestFindByUpgradeGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(&GiftUpgrade{}).Error)

	gifts := []*GiftUpgrade{
		{Type: GiftUpgradeTypeBase, BaseGiftID: 1, UpgradeGiftID: 1},
		{Type: GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 2},
		{Type: GiftUpgradeTypeFull, BaseGiftID: 1, UpgradeGiftID: 3},
	}
	err := servicedb.BatchInsert(DB(), GiftUpgrade{}.TableName(), gifts)
	require.NoError(err)

	upgrade, err := FindByUpgradeGiftID(2)
	require.NoError(err)
	assert.Equal(GiftUpgradeTypeUpgrade, upgrade.Type)
}
