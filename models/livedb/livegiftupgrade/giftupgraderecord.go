package livegiftupgrade

import (
	"strings"

	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/util"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// GiftUpgradeRecord 礼物升级记录
type GiftUpgradeRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 单位：秒

	BaseGiftID    int64 `gorm:"column:base_gift_id"`
	UpgradeGiftID int64 `gorm:"column:upgrade_gift_id"`
	UserID        int64 `gorm:"column:user_id"`
	RoomID        int64 `gorm:"column:room_id"`
}

// TableName .
func (GiftUpgradeRecord) TableName() string {
	return "live_gift_upgrade_record"
}

// IsUnlockUpgradeGift 查询用户是否已解锁升级礼物
func IsUnlockUpgradeGift(userID, baseGiftID, toggleTime, upgradeGiftID int64) (bool, error) {
	return servicedb.Exists(
		DB().Table(GiftUpgradeRecord{}.TableName()).Where("user_id = ? AND base_gift_id = ? ", userID, baseGiftID).
			Where("upgrade_gift_id = ? AND create_time >= ?", upgradeGiftID, toggleTime),
	)
}

// FindGiftUpgradeRecordsMapByUserID 查询礼物最新一次上架后用户拥有的某个基础礼物的升级记录
func FindGiftUpgradeRecordsMapByUserID(userID, baseGiftID, toggleTime int64) (map[int64]*GiftUpgradeRecord, error) {
	var records []*GiftUpgradeRecord
	err := DB().Find(&records, "user_id = ? AND base_gift_id = ? AND create_time >= ?", userID, baseGiftID, toggleTime).Error
	if err != nil {
		return nil, err
	}
	return util.ToMap(records, func(r *GiftUpgradeRecord) int64 {
		return r.UpgradeGiftID
	}), nil
}

// FindBaseGiftRecordsMap 查询用户升级礼物
// 返回 map[baseGiftID]map[upgradeGiftID]*GiftUpgradeRecord
func FindBaseGiftRecordsMap(userID int64, baseGifts []usermeta.BaseGift) (map[int64]map[int64]*GiftUpgradeRecord, error) {
	if userID <= 0 || len(baseGifts) == 0 {
		return nil, nil
	}
	query := DB().Table(GiftUpgradeRecord{}.TableName()).
		Where("user_id = ?", userID)

	args := make([]any, 0, len(baseGifts))
	var builder strings.Builder
	for i, gift := range baseGifts {
		if i > 0 {
			builder.WriteString(" OR ")
		}
		builder.WriteString("(base_gift_id = ? AND create_time >= ?)")
		args = append(args, gift.GiftID, gift.ToggleTime)
	}
	conditions := builder.String()

	if conditions != "" {
		query = query.Where(conditions, args...)
	}
	var records []*GiftUpgradeRecord
	err := query.Find(&records).Error
	if err != nil {
		return nil, err
	}

	result := make(map[int64]map[int64]*GiftUpgradeRecord)
	for _, record := range records {
		if _, ok := result[record.BaseGiftID]; !ok {
			result[record.BaseGiftID] = make(map[int64]*GiftUpgradeRecord)
		}
		result[record.BaseGiftID][record.UpgradeGiftID] = record
	}
	return result, nil
}
