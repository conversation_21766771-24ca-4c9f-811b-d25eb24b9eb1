package roombox

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/boxprizes"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveRoomBoxLog{}, "id", "create_time", "modified_time", "room_id", "box_type", "unlock_time", "event_id")
	kc.Check(LiveUserBoxLog{}, "id", "create_time", "modified_time", "room_id", "user_id", "box_type", "receive_time", "prize_name", "round_count", "event_id", "prize_id")
}

func TestLogUnlockBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(88889)
	now := goutil.TimeNow()
	eventID := int64(usersrank.EventIDAnnualLive)
	require.NoError(LogUnlockBox(eventID, roomID, boxprizes.BoxTypeOrdinary))
	var l LiveRoomBoxLog
	require.NoError(service.LiveDB.Find(&l).Error)
	assert.Equal(boxprizes.BoxTypeOrdinary, l.BoxType)
	assert.GreaterOrEqual(l.CreateTime, now.Unix())
}
