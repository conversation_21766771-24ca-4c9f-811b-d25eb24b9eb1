package roombox

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// BoxTypeOrdinary 普通宝箱
	BoxTypeOrdinary = iota + 1
	// BoxTypeUltimate 终极宝箱
	BoxTypeUltimate
)

// LiveRoomBoxLog 直播间开启宝箱的记录
type LiveRoomBoxLog struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	RoomID     int64 `gorm:"column:room_id"`
	BoxType    int   `gorm:"column:box_type"`    // 宝箱类型
	UnlockTime int64 `gorm:"column:unlock_time"` // 宝箱开启时间
	EventID    int64 `gorm:"column:event_id"`
}

// TableName for LiveRoomBoxLog model
func (LiveRoomBoxLog) TableName() string {
	return "live_room_box_log"
}

// DB the db instance of current model
func (l *LiveRoomBoxLog) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeSave gorm hook
func (l *LiveRoomBoxLog) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if l.DB().NewRecord(l) {
		l.CreateTime = nowTime
	}
	l.ModifiedTime = nowTime
	return
}

// LogUnlockBox 记录直播间开启宝箱的日志
func LogUnlockBox(eventID, roomID int64, boxType int) error {
	log := LiveRoomBoxLog{
		EventID:    eventID,
		RoomID:     roomID,
		BoxType:    boxType,
		UnlockTime: goutil.TimeNow().Unix(),
	}
	err := log.DB().Create(&log).Error
	if err != nil {
		return err
	}
	return nil
}

const (
	// NotSuperFan 领取时不是超粉
	NotSuperFan = iota
	// SuperFan 领取时是超粉
	SuperFan
)

// LiveUserBoxLog 用户领取宝箱的记录
type LiveUserBoxLog struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	RoomID      int64  `gorm:"column:room_id"`
	UserID      int64  `gorm:"column:user_id"`
	BoxType     int    `gorm:"column:box_type"`     // 宝箱类型
	ReceiveTime int64  `gorm:"column:receive_time"` // 领取时间
	PrizeName   string `gorm:"column:prize_name"`   // 奖品名称
	RoundCount  int64  `gorm:"column:round_count"`  // 当前是在该轮宝箱的第几次领取，其中 157 活动的传奇宝箱被重复解锁后视为新的一轮
	EventID     int64  `gorm:"column:event_id"`
	PrizeID     int64  `gorm:"column:prize_id"`
}

// TableName for LiveUserBoxLog model
func (LiveUserBoxLog) TableName() string {
	return "live_user_box_log"
}

// DB the db instance of current model
func (l *LiveUserBoxLog) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeSave gorm hook
func (l *LiveUserBoxLog) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if l.DB().NewRecord(l) {
		l.CreateTime = nowTime
	}
	l.ModifiedTime = nowTime
	return
}
