package livemedalremoverecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestExistsRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	records := []RemoveRecord{
		{UserID: 1},
		{UserID: 1},
		{UserID: 2},
	}
	err := DB().Delete(RemoveRecord{}).Error
	require.NoError(err)
	err = servicedb.BatchInsert(DB(), RemoveRecord{}.TableName(), records)
	require.NoError(err)

	exists, err := ExistsRecord(1)
	require.NoError(err)
	assert.True(exists)

	exists, err = ExistsRecord(2)
	require.NoError(err)
	assert.True(exists)

	exists, err = ExistsRecord(3)
	require.NoError(err)
	assert.False(exists)
}

func TestAddRemoveRecords(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	err := DB().Delete(RemoveRecord{}).Error
	require.NoError(err)

	err = AddRemoveRecords(TypeRemoveUser, nil)
	require.NoError(err)

	err = AddRemoveRecords(TypeRemoveDecline, []*livemedal.LiveMedal{}...)
	require.NoError(err)

	err = AddRemoveRecords(TypeRemoveUser, &livemedal.LiveMedal{
		Simple: livemedal.Simple{RoomID: 1, UserID: 1},
	})
	require.NoError(err)
	var count int
	err = DB().Model(RemoveRecord{}).Count(&count).Error
	require.NoError(err)
	assert.Equal(1, count)

	err = AddRemoveRecords(TypeRemoveDecline, []*livemedal.LiveMedal{
		{Simple: livemedal.Simple{RoomID: 1, UserID: 1}},
		{Simple: livemedal.Simple{RoomID: 2, UserID: 2}},
		{Simple: livemedal.Simple{RoomID: 3, UserID: 3}},
	}...)
	require.NoError(err)
	err = DB().Model(RemoveRecord{}).Count(&count).Error
	require.NoError(err)
	assert.Equal(4, count)
}

func TestIsUserEverHadMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mongoUserID := int64(123)
	removeRecordUserID := int64(124)
	noMedalUserID := int64(125)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	lm := &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			UserID: 123,
			Status: livemedal.StatusOwned,
		},
	}
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{mongoUserID, noMedalUserID}}})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, lm)
	require.NoError(err)

	exists, err := IsUserEverHadMedal(mongoUserID)
	require.NoError(err)
	assert.True(exists)

	err = DB().Delete(RemoveRecord{}, "user_id IN (?)", []int64{removeRecordUserID, noMedalUserID}).Error
	require.NoError(err)
	err = DB().Create(&RemoveRecord{
		UserID: removeRecordUserID,
	}).Error
	require.NoError(err)

	exists, err = IsUserEverHadMedal(removeRecordUserID)
	require.NoError(err)
	assert.True(exists)

	exists, err = IsUserEverHadMedal(noMedalUserID)
	require.NoError(err)
	assert.False(exists)
}
