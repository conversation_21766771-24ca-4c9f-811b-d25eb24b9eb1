package livemedalremoverecord

import (
	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 粉丝勋章的删除类型
const (
	TypeRemoveDecline = iota + 1 // 衰减删除
	TypeRemoveUser               // 用户主动删除
)

// RemoveRecord 粉丝勋章的删除记录
type RemoveRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	RoomID int64 `gorm:"column:room_id"`
	UserID int64 `gorm:"column:user_id"`
	Type   int   `gorm:"column:type"`
	Point  int64 `gorm:"column:point"`
}

// DB 获取数据库连接
func DB() *gorm.DB {
	return service.LiveDB
}

// TableName .
func (RemoveRecord) TableName() string {
	return "live_medal_remove_record"
}

// ExistsRecord 用户是否存在被删除的粉丝勋章
func ExistsRecord(userID int64) (bool, error) {
	return servicedb.Exists(DB().Model(RemoveRecord{}).Where("user_id = ?", userID))
}

// AddRemoveRecords 添加粉丝勋章的删除记录
func AddRemoveRecords(removeType int, lms ...*livemedal.LiveMedal) error {
	if len(lms) == 0 {
		return nil
	}
	timeNowUnix := goutil.TimeNow().Unix()
	records := make([]RemoveRecord, 0, len(lms))
	for _, lm := range lms {
		if lm == nil {
			continue
		}
		records = append(records, RemoveRecord{
			CreateTime:   timeNowUnix,
			ModifiedTime: timeNowUnix,
			RoomID:       lm.RoomID,
			UserID:       lm.UserID,
			Type:         removeType,
			Point:        lm.Point,
		})
	}
	if len(records) == 0 {
		return nil
	}
	return servicedb.BatchInsert(DB(), RemoveRecord{}.TableName(), records)
}

// IsUserEverHadMedal 判断用户是否曾拥有过粉丝勋章，无法统计 live_medal_remove_record 表创建前失效或删除的记录
func IsUserEverHadMedal(userID int64) (bool, error) {
	// 查询 mongo 是否存在用户的粉丝勋章
	cnt, err := livemedal.CountMedal(bson.M{
		"user_id": userID,
		"status":  bson.M{"$gt": livemedal.StatusPending},
	})
	if err != nil {
		return false, err
	}
	if cnt > 0 {
		return true, nil
	}

	// 查询用户是否存在粉丝勋章删除记录
	exists, err := ExistsRecord(userID)
	if err != nil {
		return false, err
	}
	return exists, nil
}
