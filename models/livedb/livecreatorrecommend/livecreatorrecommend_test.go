package livecreatorrecommend

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveCreatorRecommend{}, "id", "create_time", "modified_time", "bizdate", "type", "element_type", "data")
}

func TestFindCreatorIDs(t *testing.T) {
	require := require.New(t)

	// 测试获取声优记录
	creatorIDs, err := FindCreatorIDs(TypeTopViewsDramaCv)
	require.NoError(err)
	require.NotNil(creatorIDs)
	require.Len(creatorIDs, 2)
}

func TestListRandomTopScoreDramaCVRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DB().Delete(LiveCreatorRecommend{}, "type = ? AND bizdate = ?", TypeTopViewsDramaCv, "2024-11-05").Error)
	// 测试没有房间列表
	rooms, err := ListRandomTopScoreDramaCVRooms(10, 3)
	require.NoError(err)
	require.NotNil(rooms)
	assert.Empty(rooms)

	bizdate := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	require.NoError(DB().Delete(LiveCreatorRecommend{}, "type = ? AND bizdate = ?", TypeTopViewsDramaCv, bizdate).Error)
	nowUnix := goutil.TimeNow().Unix()
	lr := LiveCreatorRecommend{
		CreateTime:   nowUnix,
		ModifiedTime: nowUnix,
		Bizdate:      bizdate,
		Type:         TypeTopViewsDramaCv,
		ElementType:  ElementTypeCreator,
		Data:         []byte("[30000,30001,30002]"),
	}
	require.NoError(DB().Table(LiveCreatorRecommend{}.TableName()).Create(&lr).Error)

	// 生成房间数据
	roomList := make([]interface{}, 3)
	roomIDs := make([]int64, 3)
	for i := 0; i < 3; i++ {
		roomID := int64(30000 + i)
		roomIDs[i] = roomID
		CreatorID := roomID
		roomList[i] = room.Room{
			Helper: room.Helper{
				RoomID:    roomID,
				CreatorID: CreatorID,
				Name:      "测试高热度声优直播间",
				NameClean: fmt.Sprintf("测试高热度声优直播间 %d", 30000+i),
				Status:    room.Status{Open: room.StatusOpenTrue, Score: float64(i)},
			},
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 删除测试数据
	_, err = room.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}})
	require.NoError(err)
	_, err = room.Collection().InsertMany(ctx, roomList)
	require.NoError(err)

	// 测试获取房间列表
	rooms, err = ListRandomTopScoreDramaCVRooms(3, 2)
	require.NoError(err)
	require.NotNil(rooms)
	require.Len(rooms, 2)
	assert.Contains([]int64{30000, 30001, 30002}, rooms[0].RoomID)
	assert.Contains([]int64{30000, 30001, 30002}, rooms[1].RoomID)
}
