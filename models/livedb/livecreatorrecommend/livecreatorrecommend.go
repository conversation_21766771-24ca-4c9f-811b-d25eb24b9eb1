package livecreatorrecommend

import (
	"encoding/json"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// TypeTopViewsDramaCv 7 日播放量 TOP 100 剧集声优主播
const TypeTopViewsDramaCv = 1

// ElementTypeCreator 主播
const ElementTypeCreator = 5

// LiveCreatorRecommend 主播推荐
type LiveCreatorRecommend struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Bizdate      string `gorm:"column:bizdate"`      // 业务日期（格式：2024-11-05）
	Type         int    `gorm:"column:type"`         // 推荐类型（1：7 日播放量 TOP 100 剧集声优主播）
	ElementType  int    `gorm:"column:element_type"` // 元素类型（5：主播 ID）
	Data         []byte `gorm:"column:data"`         // 主播 ID，e.g. [123456, 123457, 123458]

	DataInfo []int64 `gorm:"-"`
}

// TableName table name
func (LiveCreatorRecommend) TableName() string {
	return "live_creator_recommend"
}

// DB .
func DB() *gorm.DB {
	return service.LiveDB
}

// AfterFind is a GORM hook for query
func (lr *LiveCreatorRecommend) AfterFind() error {
	if len(lr.Data) > 0 {
		err := json.Unmarshal(lr.Data, &lr.DataInfo)
		if err != nil {
			return err
		}
	}
	return nil
}

// FindCreatorIDs 获取主播 IDs
func FindCreatorIDs(recommendType int) ([]int64, error) {
	var recommend LiveCreatorRecommend
	// 获取最新日期的主播推荐数据
	subQuery := DB().Select("bizdate").Model(LiveCreatorRecommend{}).Where("type = ?", recommendType).Order("id DESC").Limit(1).SubQuery()
	err := DB().Select("id, element_type, data").
		Where("type = ? AND bizdate = ?", recommendType, subQuery).
		Take(&recommend).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	if recommend.ElementType != ElementTypeCreator {
		logger.WithField("id", recommend.ID).Errorf("推荐元素类型错误: %d", recommend.ElementType)
		// PASS
		return []int64{}, nil
	}
	return recommend.DataInfo, nil
}

// ListRandomTopScoreDramaCVRooms 获取随机高热度声优房间列表
func ListRandomTopScoreDramaCVRooms(limit, size int64) ([]*room.Simple, error) {
	creatorIDs, err := FindCreatorIDs(TypeTopViewsDramaCv)
	if err != nil {
		return nil, err
	}
	if len(creatorIDs) == 0 {
		return []*room.Simple{}, nil
	}

	return room.ListRandomTopScoreRooms(bson.M{
		"creator_id":  bson.M{"$in": util.Uniq(creatorIDs)},
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}, 0, limit, size, &room.FindOptions{FindCreator: true})
}
