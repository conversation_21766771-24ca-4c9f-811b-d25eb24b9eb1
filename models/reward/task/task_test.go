package task

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewScoreTask(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(&ScoreTask{
		rewardIDs:     []int64{1},
		promotedScore: 1,
		afterScore:    2,
		addScore:      2,
	}, NewScoreTask(1, 2, 2, 1))
}

func TestScoreTaskCheckComplete(t *testing.T) {
	assert := assert.New(t)

	assert.False(
		NewScoreTask(2, 1, 2).
			CheckComplete(),
	)

	assert.True(
		NewScoreTask(2, 2, 1).
			CheckComplete(),
	)

	assert.True(
		NewScoreTask(2, 3, 1).
			CheckComplete(),
	)
}

func TestScoreTaskRewardIDs(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(NewScoreTask(1, 0, 0, 1).RewardIDs())
	assert.NotEmpty(NewScoreTask(1, 1, 1, 1).RewardIDs())
}

func TestNewNodeTask(t *testing.T) {
	assert := assert.New(t)

	task := NewNodeTask(NewScoreTask(1, 0, 0))
	assert.Equal(&NodeTask{tasks: []Tasker{NewScoreTask(1, 0, 0)}, rewardIDs: []int64{}}, task)
}

func TestNodeTaskCheckComplete(t *testing.T) {
	assert := assert.New(t)

	task := NewNodeTask(
		NewScoreTask(1, 2, 1),
		NewScoreTask(2, 2, 1),
		NewScoreTask(3, 2, 1),
	)
	assert.False(task.CheckComplete())

	task = NewNodeTask(
		NewScoreTask(1, 3, 1),
		NewScoreTask(2, 3, 1),
		NewScoreTask(3, 3, 1),
	)
	assert.True(task.CheckComplete())

	task = NewNodeTask(
		NewScoreTask(1, 4, 1),
		NewScoreTask(2, 4, 1),
		NewScoreTask(3, 4, 1),
	)
	assert.True(task.CheckComplete())
}

func TestNodeTaskCheckIsFirstComplete(t *testing.T) {
	assert := assert.New(t)

	task := NewNodeTask(
		NewScoreTask(1, 2, 1),
		NewScoreTask(2, 2, 1),
		NewScoreTask(3, 2, 1),
	)
	assert.False(task.CheckIsFirstComplete())

	task = NewNodeTask(
		NewScoreTask(1, 3, 1),
		NewScoreTask(2, 3, 1),
		NewScoreTask(3, 3, 1),
	)
	assert.True(task.CheckIsFirstComplete())

	task = NewNodeTask(
		NewScoreTask(1, 4, 1),
		NewScoreTask(2, 4, 1),
		NewScoreTask(3, 4, 1),
	)
	assert.False(task.CheckIsFirstComplete())
}

func TestNodeTaskRewardIDs(t *testing.T) {
	assert := assert.New(t)

	task := NewNodeTask(
		NewScoreTask(3, 1, 1, 1),
		NewScoreTask(2, 2, 2, 2),
		NewScoreTask(3, 4, 1, 3),
	)
	assert.Equal([]int64{2}, task.RewardIDs())

	task = NewNodeTask(
		NewScoreTask(3, 1, 1, 1),
		NewScoreTask(2, 1, 1, 2),
		NewScoreTask(3, 4, 2, 3),
	)
	assert.Equal([]int64{3}, task.RewardIDs())

	task = NewNodeTask(
		NewScoreTask(4, 1, 4, 1),
		NewScoreTask(4, 2, 4, 1),
		NewScoreTask(4, 3, 4, 1),
	)
	assert.Empty(task.RewardIDs())
}
