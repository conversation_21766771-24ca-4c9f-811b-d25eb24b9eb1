package task

// Tasker 任务
type Tasker interface {
	// CheckComplete 检查任务是否完成
	CheckComplete() bool
	// CheckIsFirstComplete 检查是否是第一次完成
	CheckIsFirstComplete() bool
	// RewardIDs 返回统计后应该发放奖励的 id
	RewardIDs() []int64
	// TODO: 之后考虑集成 SendRewards 方法
	// SendRewards()
}

// ScoreTask 达到分数发放奖励的 task
type ScoreTask struct {
	rewardIDs []int64

	promotedScore int64
	afterScore    int64
	addScore      int64
}

// NewScoreTask new score task
func NewScoreTask(score, afterScore, addScore int64, rewardIDs ...int64) *ScoreTask {
	return &ScoreTask{
		rewardIDs:     rewardIDs,
		promotedScore: score,
		afterScore:    afterScore,
		addScore:      addScore,
	}
}

// CheckComplete check complete
func (t *ScoreTask) CheckComplete() bool {
	return t.afterScore >= t.promotedScore
}

// CheckIsFirstComplete check is first complete
func (t *ScoreTask) CheckIsFirstComplete() bool {
	return t.afterScore >= t.promotedScore && t.afterScore-t.addScore < t.promotedScore
}

// RewardIDs 返回统计后应该发放奖励的 id
func (t *ScoreTask) RewardIDs() []int64 {
	if t.CheckIsFirstComplete() {
		return t.rewardIDs
	}
	return []int64{}
}

// NodeTask 晋级任务, 通常用于统计同一类型达到不同积分发放不同奖励的任务
type NodeTask struct {
	tasks     []Tasker
	rewardIDs []int64
}

// NewNodeTask new node task
// node task 需要在传入时保证顺序，一般用于同一任务的不同节点
func NewNodeTask(tasks ...Tasker) *NodeTask {
	return &NodeTask{tasks: tasks, rewardIDs: []int64{}}
}

// CheckComplete check complete
func (t *NodeTask) CheckComplete() bool {
	for _, task := range t.tasks {
		if !task.CheckComplete() {
			return false
		}
	}

	return true
}

// CheckIsFirstComplete check is first complete
// 最后一个任务第一次完成时返回 true
func (t *NodeTask) CheckIsFirstComplete() bool {
	for i, task := range t.tasks {
		if !task.CheckComplete() {
			return false
		}

		if i == len(t.tasks)-1 {
			return task.CheckIsFirstComplete()
		}
	}

	return false
}

// RewardIDs 返回统计后应该发放奖励的 id
func (t *NodeTask) RewardIDs() []int64 {
	var rewardIDs []int64
	for _, task := range t.tasks {
		rewardIDs = append(rewardIDs, task.RewardIDs()...)
	}

	if len(t.rewardIDs) != 0 {
		rewardIDs = append(rewardIDs, t.rewardIDs...)
	}

	return rewardIDs
}

// NormalTask 通常用于有不同类型任务要求的任务
type NormalTask struct {
	tasks     []Tasker
	rewardIDs []int64
}

// NewNormalTask new normal task
func NewNormalTask(tasks ...Tasker) *NormalTask {
	return &NormalTask{tasks: tasks}
}

// SetRewards sets rewardIDs
func (t *NormalTask) SetRewards(rewardIDs ...int64) *NormalTask {
	t.rewardIDs = rewardIDs
	return t
}

// CheckComplete check complete
func (t *NormalTask) CheckComplete() bool {
	for _, task := range t.tasks {
		if !task.CheckComplete() {
			return false
		}
	}

	return true
}

// CheckIsFirstComplete check is first complete
// 任务第一次都完成时返回 true
func (t *NormalTask) CheckIsFirstComplete() bool {
	var first bool
	for _, task := range t.tasks {
		if !task.CheckComplete() {
			return false
		}

		first = first || task.CheckIsFirstComplete()
	}

	return first
}

// RewardIDs 返回统计后应该发放奖励的 id
func (t *NormalTask) RewardIDs() []int64 {
	var rewardIDs []int64
	for _, task := range t.tasks {
		rewardIDs = append(rewardIDs, task.RewardIDs()...)
	}

	if t.CheckIsFirstComplete() {
		rewardIDs = append(rewardIDs, t.rewardIDs...)
	}

	return rewardIDs
}
