package reward

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	rewardStatusFailed = iota - 1
	rewardStatusPending
	rewardStatusSuccess
)

// SendRecord 奖励发放记录
type SendRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTIme int64 `gorm:"column:modified_time"`

	TraceID string `gorm:"column:trace_id"` // 链路 ID
	UUID    string `gorm:"column:uuid"`     // 上游发奖唯一 ID

	RewardID   int64 `gorm:"column:reward_id"`
	RewardType int   `gorm:"column:reward_type"`
	Status     int   `gorm:"column:status"`
	UserID     int64 `gorm:"column:user_id"`
	RoomID     int64 `gorm:"column:room_id"`
	CreatorID  int64 `gorm:"column:creator_id"`
}

// TableName for the SendRecord model
func (SendRecord) TableName() string {
	return "live_reward_send_record"
}

// DB the db instance of RedeemRecord model
func (r SendRecord) DB() *gorm.DB {
	return service.LiveDB.Table(r.TableName())
}

// BeforeSave gorm 钩子
func (r *SendRecord) BeforeSave() (err error) {
	now := goutil.TimeNow().Unix()
	if r.DB().NewRecord(r) {
		r.CreateTime = now
	}
	r.ModifiedTIme = now
	return
}
