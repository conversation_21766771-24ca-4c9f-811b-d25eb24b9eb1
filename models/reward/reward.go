package reward

import (
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/liveuserspecialredpacket"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goserviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MaoerWalletUserID 猫耳娘的零钱袋用户 ID
const MaoerWalletUserID int64 = 2939325

// reward type
const (
	TypeManual               = iota // 运营手动发放
	TypeGift                        // 发放礼物
	TypeUserAppearance              // 发放用户外观
	TypeCreatorAppearance           // 发放主播外观
	TypeBackpack                    // 发放背包礼物
	TypeCreatorBackpack             // 发放主播背包礼物
	TypeLiveTag                     // 发放直播间角标
	TypeNotify                      // 发送飘屏
	TypeCreatorCard                 // 发放主播信息背景
	TypeSticker                     // 发送表情
	TypeUserGiftCustom              // 发放用户定制礼物资格
	TypeBackPackItem                // 发放背包道具
	TypeNobleHorn                   // 贵族喇叭
	TypeCustomWelcomeMessage        // 自定义进场欢迎语
	TypeSpecialRedPacket            // 用户特殊红包
	TypeRoomGiftCustom              // 直播间定制礼物
	TypeUserSendGift                // 以用户身份给直播间送礼
	_                               // 进房有奖任务
)

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("rewards")
}

// Reward 奖励
type Reward struct {
	OID      primitive.ObjectID `bson:"_id" json:"-"`
	RewardID int64              `bson:"reward_id" json:"reward_id"`
	Type     int                `bson:"type" json:"type"`

	// 仅用于描述 reward 用途，无业务上的作用
	EventID int64  `bson:"event_id,omitempty" json:"event_id,omitempty"`
	Intro   string `bson:"intro,omitempty" json:"intro,omitempty"`

	// 外观用 ElementType 来确定类型
	// 飘屏用 ElementType 来确定飘屏类型
	// 表情包用 ElementType 来确定表情包类型
	ElementType int    `bson:"element_type,omitempty" json:"element_type,omitempty"`
	ElementID   int64  `bson:"element_id,omitempty" json:"element_id,omitempty"`
	ElementNum  int    `bson:"element_num,omitempty" json:"element_num,omitempty"`
	StartTime   int64  `bson:"start_time,omitempty" json:"start_time,omitempty"`
	EndTime     int64  `bson:"end_time,omitempty" json:"end_time,omitempty"`
	Duration    int64  `bson:"duration,omitempty" json:"duration,omitempty"` // 秒
	URL         string `bson:"url,omitempty" json:"url,omitempty"`
	Message     string `bson:"message,omitempty" json:"message,omitempty"`

	// 仅用于关联发送日志
	traceID string `bson:"-" json:"-"`
	uuid    string `bson:"-" json:"-"`
}

func (reward Reward) getTime() (int64, int64, error) {
	if reward.StartTime == 0 {
		reward.StartTime = goutil.TimeNow().Unix()
	}

	// 判断发放奖励是否没有明确截止时间，通常用于指定天数的奖励
	if reward.EndTime == 0 {
		if reward.Duration == 0 {
			return 0, 0, fmt.Errorf("reward 配置错误 %d", reward.RewardID)
		}
		reward.EndTime = reward.StartTime + reward.Duration
	}

	return reward.StartTime, reward.EndTime, nil
}

// 个人场发奖 ID
// https://info.missevan.com/pages/viewpage.action?pageId=60526263
const (
	RewardID1  = 1  // 3k 热度卡 × 1 ID：30003 有效期：30d
	RewardID2  = 2  // 6k 热度卡 × 1  ID：30001 有效期：30d
	RewardID3  = 3  // 10k 热度卡 × 1 ID：30002 有效期：30d
	RewardID4  = 4  // 头像框 ID：40109 有效期：3d
	RewardID5  = 5  // 称号 ID：50047 有效期：3d
	RewardID6  = 6  // 直播间封面角标 角标地址：live/labelicon/livelist/liveshow-1 有效期：3d
	RewardID7  = 7  // 运营手动发放
	RewardID8  = 8  // 猫耳娘送礼 ID: 40047
	RewardID9  = 9  // 10k 热度卡 × 3 ID：30002 有效期：30d
	RewardID10 = 10 // 10k 热度卡 × 6 ID：30002 有效期：30d
	RewardID11 = 11 // 称号 ID：40110 有效期：7d
	RewardID12 = 12 // 称号 ID：50048 有效期：7d
	RewardID13 = 13 // 直播间封面角标 角标地址：live/labelicon/livelist/liveshow-2 有效期：7d
	RewardID14 = 14 // 全站飘屏通知
	RewardID15 = 15 // 座驾 ID：40110 有效期：7d

	RewardID20 = 20 // 直播间挂件 ID：60004 有效期：7d
	RewardID21 = 21 // 直播间挂件 ID：60005 有效期：15d
)

// SetTrace 设置链路 ID 和上游发奖 ID
func (reward Reward) SetTrace(traceID, uuid string) Reward {
	reward.traceID = traceID
	reward.uuid = uuid
	return reward
}

// Send reward by reward type
func (reward Reward) Send(roomID, creatorID, userID int64) error {
	updateStatusCallback := reward.saveRecord(roomID, creatorID, userID)
	var err error
	switch reward.Type {
	case TypeManual:
		return nil
	case TypeGift:
		err = reward.sendGift(roomID)
	case TypeUserAppearance:
		err = reward.sendAppearance(userID)
	case TypeCreatorAppearance:
		err = reward.sendAppearance(creatorID)
	case TypeBackpack:
		err = reward.sendBackpack(userID)
	case TypeCreatorBackpack:
		err = reward.sendCreatorBackpack(creatorID)
	case TypeLiveTag:
		err = reward.sendLiveTag(roomID)
	case TypeNotify:
		err = reward.sendNotify(roomID, creatorID)
	case TypeCreatorCard:
		err = reward.sendCreateCard(roomID)
	case TypeSticker:
		err = reward.sendSticker(roomID, userID)
	case TypeUserGiftCustom:
		err = reward.sendUserGiftCustom(userID)
	case TypeBackPackItem:
		err = reward.sendBackPackItem(userID)
	case TypeNobleHorn:
		err = reward.sendNobleHorn(userID)
	case TypeCustomWelcomeMessage:
		err = reward.sendCustomWelcomeMessage(userID)
	case TypeSpecialRedPacket:
		err = reward.sendSpecialRedPacket(userID)
	case TypeRoomGiftCustom:
		err = reward.sendRoomGiftCustom(roomID)
	case TypeUserSendGift:
		err = reward.sendGiftByUser(roomID, userID)
	default:
		err = fmt.Errorf("unknown reward type %d", reward.Type)
	}
	if err != nil {
		updateStatusCallback(rewardStatusFailed)
		return err
	}
	updateStatusCallback(rewardStatusSuccess)
	return nil
}

func (reward Reward) saveRecord(roomID, creatorID, userID int64) func(int) {
	record := SendRecord{
		TraceID:    reward.traceID,
		UUID:       reward.uuid,
		RewardID:   reward.RewardID,
		RewardType: reward.Type,
		Status:     rewardStatusPending,
		UserID:     userID,
		RoomID:     roomID,
		CreatorID:  creatorID,
	}
	if err := record.DB().Save(&record).Error; err != nil {
		logger.WithField("reward_id", reward.RewardID).Errorf("奖励发放记录错误: %v", err)
		return func(status int) {
			// do nothing
		}
	}
	return func(status int) {
		err := record.DB().Where("id = ?", record.ID).Updates(
			map[string]interface{}{
				"status":        status,
				"modified_time": goutil.TimeNow().Unix(),
			}).Error
		if err != nil {
			logger.WithFields(logger.Fields{
				"record_id": record.ID,
				"reward_id": record.RewardID,
			}).Errorf("奖励记录更新错误: %v", err)
		}
	}
}

func (reward Reward) sendGift(roomID int64) error {
	giftParams := reward.buildGiftParams(roomID, MaoerWalletUserID)
	return service.MRPC.Call(userapi.URILiveReward, "", giftParams, nil)
}

func (reward Reward) sendGiftByUser(roomID, userID int64) error {
	giftParams := reward.buildGiftParams(roomID, userID)
	return service.MRPC.Call(userapi.URILiveReward, "", giftParams, nil)
}

func (reward Reward) buildGiftParams(roomID, userID int64) map[string]interface{} {
	return map[string]interface{}{
		"type":    "gift",
		"room_id": roomID,
		"user_id": userID,
		"gift_params": []map[string]int64{
			{
				"gift_id":  reward.ElementID,
				"gift_num": int64(reward.ElementNum),
			},
		},
	}
}

func (reward Reward) sendAppearance(userID int64) error {
	aItem, err := appearance.FindOne(reward.ElementID, reward.ElementType)
	if err != nil {
		return err
	}
	if aItem == nil {
		return fmt.Errorf("can not find appearance %d %d", reward.ElementID, reward.ElementType)
	}

	return userappearance.AddAppearance(userID, reward.Duration, reward.EndTime, aItem)
}

func (reward Reward) sendBackpack(userID int64) error {
	startTime, endTime, err := reward.getTime()
	if err != nil {
		return err
	}

	g, err := gift.FindShowingGiftByGiftID(reward.ElementID)
	if err != nil {
		return err
	}
	if g == nil {
		return fmt.Errorf("礼物不存在 %d", reward.ElementID)
	}
	if !useritems.IsBackpackGift(g) {
		return fmt.Errorf("礼物 %d 不是背包礼物", reward.ElementID)
	}

	return useritems.AddGiftToUsers([]int64{userID}, g, int64(reward.ElementNum), useritems.SourceNormal, startTime, endTime)
}

func (reward Reward) sendCreatorBackpack(creatorID int64) error {
	startTime, endTime, err := reward.getTime()
	if err != nil {
		return err
	}

	g, err := gift.FindShowingGiftByGiftID(reward.ElementID)
	if err != nil {
		return err
	}
	if g == nil {
		return fmt.Errorf("礼物不存在 %d", reward.ElementID)
	}
	if !creatoritems.IsBackpackGift(g) {
		return fmt.Errorf("礼物 %d 不是主播背包礼物", reward.ElementID)
	}

	return creatoritems.AddGiftToCreators([]int64{creatorID}, g, int64(reward.ElementNum), startTime, endTime)
}

func (reward Reward) sendLiveTag(roomID int64) error {
	startTime, endTime, err := reward.getTime()
	if err != nil {
		return err
	}

	return liverecommendedelements.AddLiveIcon(roomID, startTime, endTime, reward.URL)
}

func (reward Reward) sendNotify(roomID, creatorID int64) error {
	user, err := mowangskuser.FindByUserID(creatorID)
	if err != nil {
		return err
	}
	if user == nil {
		return fmt.Errorf("can not find creator %d", creatorID)
	}

	formatParams := make(map[string]string)
	formatParams["creator_username"] = html.EscapeString(user.Username)

	b, err := bubble.FindSimple(reward.ElementID)
	if err != nil {
		return err
	}
	if b != nil {
		b.AppendFormatParams(formatParams)
	}

	general := notifymessages.NewGeneral(roomID, goutil.FormatMessage(reward.Message, formatParams), b)
	if err := userapi.BroadcastMany([]*userapi.BroadcastElem{{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  roomID,
		Payload: general,
	}}); err != nil {
		return err
	}
	return nil
}

// NOTICE: 主播信息背景最大有效期至 Reward.EndTime
func (reward Reward) sendCreateCard(roomID int64) error {
	element, err := liverecommendedelements.FindOneByElementType(liverecommendedelements.ElementCreatorCard, roomID)
	if err != nil {
		return err
	}
	if element != nil {
		if element.ExpireTime < reward.EndTime {
			err = service.DB.Model(&liverecommendedelements.Model{}).
				Where("id = ?", element.ID).
				UpdateColumn("expire_time", goutil.MinInt64(element.ExpireTime+reward.Duration, reward.EndTime)).Error
		}
	} else {
		now := goutil.TimeNow()
		err = service.DB.Create(&liverecommendedelements.LiveRecommendedElements{
			Sort:        1,
			ElementID:   roomID,
			ElementType: liverecommendedelements.ElementCreatorCard,
			URL:         reward.URL,
			StartTime:   goutil.NewInt64(now.Unix()),
			ExpireTime:  goutil.MinInt64(now.Unix()+reward.Duration, reward.EndTime),
		}).Error
	}
	if err != nil {
		return err
	}
	return nil
}

// NOTICE: 表情最大有效期至 Reward.EndTime
func (reward Reward) sendSticker(roomID, userID int64) error {
	if reward.ElementType != livesticker.TypeRoom && reward.ElementType != livesticker.TypeUser {
		return errors.New("invalid sticker type")
	}

	sticker, err := livesticker.FindSticker(reward.ElementID)
	if err != nil {
		return err
	}
	if sticker == nil {
		return fmt.Errorf("can not find sticker %d", reward.ElementID)
	}
	now := goutil.TimeNow()
	owner, err := livesticker.FindPackageOwner(reward.ElementType, roomID, userID, now)
	if err != nil {
		return err
	}
	// NOTICE: 这里仅支持用户和直播专属表情。若无对应专属表情包，则会创建一个表情包并分配给用户或直播间；若存在对应表情包，则直接使用。
	var pkg *livesticker.Package
	if owner == nil {
		pkg, err = livesticker.AssignExclusivePackage(reward.ElementType, roomID, userID)
		if err != nil {
			return err
		}
	} else {
		pkg, err = livesticker.FindPackage(owner.PackageID)
		if err != nil {
			return err
		}
		if pkg == nil {
			logger.WithFields(logger.Fields{
				"reward_id":  reward.RewardID,
				"package_id": owner.PackageID,
				"user_id":    userID,
				"room_id":    roomID,
			}).Error("can not find sticker package")
			// PASS
			return nil
		}
	}
	m, err := pkg.StickerMap(sticker.ID, now)
	if err != nil {
		return err
	}
	if m != nil {
		// 若表情过期时间小于奖励最大有效时间，则给该表情续期
		// 若奖励的表情为永久，但已有的表情有过期时间，则将表情设置为永久（expire_time 为 0）
		if m.ExpireTime != 0 && (reward.EndTime == 0 || m.ExpireTime < reward.EndTime) {
			err = livesticker.DB().Model(&livesticker.PackageStickerMap{}).Where("id = ?", m.ID).Updates(
				map[string]interface{}{
					"expire_time": goutil.MinInt64(m.ExpireTime+reward.Duration, reward.EndTime),
				},
			).Error
		}
	} else {
		err = livesticker.DB().Create(&livesticker.PackageStickerMap{
			PackageID:  pkg.ID,
			StickerID:  sticker.ID,
			StartTime:  now.Unix(),
			ExpireTime: goutil.MinInt64(now.Unix()+reward.Duration, reward.EndTime),
		}).Error
	}
	if err != nil {
		return err
	}
	return nil
}

func (reward Reward) sendUserGiftCustom(userID int64) error {
	duration := reward.Duration
	if duration <= 0 {
		logger.Errorf("reward 配置错误 %d", reward.RewardID)
		return nil
	}

	now := goutil.TimeNow().Unix()
	// 若奖励的结束时间小于当前时间，则不发放
	if reward.EndTime != 0 && reward.EndTime < now {
		return nil
	}
	giftID := reward.ElementID
	custom, err := livecustom.FindUserCustomGift(userID, giftID)
	if err != nil {
		return err
	}
	var startTime int64
	if custom != nil {
		startTime = custom.EndTime
	} else {
		startTime = now
	}
	if reward.EndTime != 0 {
		remainDuration := reward.EndTime - startTime
		if remainDuration <= 0 {
			// 已经奖励到最大时间限制
			return nil
		}
		// 若奖励的结束时间小于过期时间，则以奖励的结束时间为准
		duration = goutil.MinInt64(duration, remainDuration)
	}
	return livecustom.AddUserCustomGift(userID, giftID, time.Duration(duration)*time.Second, false)
}

func (reward Reward) sendRoomGiftCustom(roomID int64) error {
	duration := reward.Duration
	if duration <= 0 {
		logger.Errorf("reward 配置错误 %d", reward.RewardID)
		return nil
	}

	nowUnix := goutil.TimeNow().Unix()
	// 若奖励的结束时间小于当前时间，则不发放
	if reward.EndTime != 0 && reward.EndTime < nowUnix {
		return nil
	}
	giftID := reward.ElementID
	custom, err := livecustom.FindRoomCustomGift(roomID, giftID)
	if err != nil {
		return err
	}
	var customID, durationStartTime int64
	if custom != nil {
		customID = custom.ID
		durationStartTime = custom.EndTime
	} else {
		durationStartTime = nowUnix
	}
	if reward.EndTime != 0 {
		remainDuration := reward.EndTime - durationStartTime
		if remainDuration <= 0 {
			// 已经奖励到最大时间限制
			return nil
		}
		// 若奖励的结束时间小于过期时间，则以奖励的结束时间为准
		duration = goutil.MinInt64(duration, remainDuration)
	}
	return livecustom.AssignRoomCustomGift(customID, roomID, giftID,
		time.Unix(nowUnix, 0),
		time.Unix(durationStartTime+duration, 0),
		livecustom.SourceDefault)
}

func (reward Reward) sendBackPackItem(userID int64) error {
	item, err := backpackitem.FindOne(reward.ElementID, reward.ElementType)
	if err != nil {
		return err
	}
	if item == nil {
		logger.WithFields(logger.Fields{
			"element_id":   reward.ElementID,
			"element_type": reward.ElementType,
		}).Errorf("can not find backpack item")
		// PASS
		return nil
	}
	startTime, endTime, err := reward.getTime()
	if err != nil {
		return err
	}

	err = useritems.AddItemToUsers([]int64{userID}, item, int64(reward.ElementNum), useritems.SourceNormal, startTime, endTime)
	if err != nil {
		return err
	}
	return nil
}

func (reward Reward) sendNobleHorn(userID int64) error {
	if reward.ElementNum <= 0 {
		logger.WithField("reward_id", reward.RewardID).Error("reward 数量配置错误")
		return nil
	}

	err := usermeta.IncrNobleHornNum(userID, int64(reward.ElementNum))
	if err != nil {
		return err
	}
	return nil
}

func (reward Reward) sendCustomWelcomeMessage(userID int64) error {
	return usermeta.AssignCustomWelcomeWithDuration(userID, reward.Duration, reward.EndTime)
}

func (reward Reward) sendSpecialRedPacket(userID int64) error {
	if reward.ElementNum <= 0 {
		logger.WithField("reward_id", reward.RewardID).Error("reward 数量配置错误")
		return nil
	}
	// 若奖励的结束时间小于当前时间，则不发放
	if reward.EndTime != 0 && reward.EndTime <= goutil.TimeNow().Unix() {
		return nil
	}
	lg, err := livegoods.FindShowingGoods(reward.ElementID, reward.ElementType)
	if err != nil {
		return err
	}
	if lg == nil {
		logger.WithFields(logger.Fields{
			"user_id":      userID,
			"element_id":   reward.ElementID,
			"element_type": reward.ElementType,
		}).Errorf("can not find red packet goods")
		// PASS
		return nil
	}
	return liveuserspecialredpacket.AddUserSpecialRedPacket(userID, reward.ElementID, int64(reward.ElementNum), reward.EndTime)
}

// BackpackItem 用户背包奖励详情
type BackpackItem struct {
	Type   int    `json:"type"`
	GiftID int64  `json:"gift_id,omitempty"`
	ItemID int64  `json:"item_id,omitempty"`
	Name   string `json:"name"`
	Price  int64  `json:"price"`
	Num    int    `json:"num"`

	IconURL string `json:"-"`
}

// FindBackpackItem 查询背包奖励
// 目前只有背包礼物和背包道具
func (reward *Reward) FindBackpackItem() (*BackpackItem, error) {
	var item BackpackItem
	switch reward.Type {
	case TypeBackpack:
		gift, err := gift.FindShowingGiftByGiftID(reward.ElementID)
		if err != nil {
			return nil, err
		}
		if gift == nil {
			return nil, nil
		}
		item = BackpackItem{
			Type:    useritems.BpItemTypeGift,
			GiftID:  gift.GiftID,
			Name:    gift.Name,
			Price:   gift.Price,
			Num:     reward.ElementNum,
			IconURL: gift.Icon,
		}
		return &item, nil
	case TypeBackPackItem:
		backPackItem, err := backpackitem.FindOneItem(reward.ElementID)
		if err != nil {
			return nil, err
		}
		if backPackItem == nil {
			return nil, nil
		}
		item = BackpackItem{
			Type:    useritems.BpItemTypeItem,
			ItemID:  backPackItem.ID,
			Name:    backPackItem.Name,
			Num:     reward.ElementNum,
			IconURL: backPackItem.IconURL,
		}
		return &item, nil
	default:
		return nil, fmt.Errorf("unsupported type %d", reward.Type)
	}
}

// NewRewardFunc new reward function
func NewRewardFunc(roomID, creatorID, userID int64) func(rewardIDs ...int64) {
	return func(rewardIDs ...int64) {
		for _, id := range rewardIDs {
			reward, err := FindRewardByRewardIDWithCache(id)
			if err != nil || reward == nil {
				logger.WithFields(logger.Fields{
					"reward_id":  id,
					"user_id":    userID,
					"room_id":    roomID,
					"creator_id": creatorID,
				}).Errorf("can not find reward %d", id)
				continue
			}

			err = reward.Send(roomID, creatorID, userID)
			if err != nil {
				logger.WithFields(logger.Fields{
					"reward_id":  id,
					"user_id":    userID,
					"room_id":    roomID,
					"creator_id": creatorID,
				}).Error(err)
			}
		}
	}
}

func findRewardByRewardID(rewardID int64) (*Reward, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var reward Reward
	err := Collection().FindOne(ctx, bson.M{"reward_id": rewardID}).Decode(&reward)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &reward, nil
}

// FindRewardByRewardIDWithCache find reward by rewardID with cache
func FindRewardByRewardIDWithCache(rewardID int64) (*Reward, error) {
	key := keys.KeyReward1.Format(rewardID)
	bytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !goserviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	} else if len(bytes) > 0 {
		var reward *Reward
		err = json.Unmarshal(bytes, &reward)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return reward, nil
		}
	}

	reward, err := findRewardByRewardID(rewardID)
	if err != nil {
		return nil, err
	}

	bytes, err = json.Marshal(reward)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, bytes, time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return reward, nil
}

// FindRewardsMapWithCache find rewards with cache
func FindRewardsMapWithCache(rewardIDs []int64) (map[int64]*Reward, error) {
	sort.Slice(rewardIDs, func(i, j int) bool { return rewardIDs[i] < rewardIDs[j] })
	rewardIDsString := goutil.JoinInt64Array(rewardIDs, ",")
	if len(rewardIDs) > 10 {
		rewardIDsString = goutil.MD5(rewardIDsString)
	}
	key := keys.KeyRewards1.Format(rewardIDsString)
	bytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !goserviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	} else if len(bytes) > 0 {
		var rewardMap map[int64]*Reward
		err = json.Unmarshal(bytes, &rewardMap)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return rewardMap, nil
		}
	}

	rewards, err := FindRewards(rewardIDs)
	if err != nil {
		return nil, err
	}
	rewardMap := util.ToMap(rewards, func(r *Reward) int64 {
		return r.RewardID
	})
	bytes, err = json.Marshal(rewardMap)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, bytes, time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return rewardMap, nil
}

// FindRewards find rewards
func FindRewards(rewardIDs []int64) ([]*Reward, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, bson.M{"reward_id": bson.M{"$in": rewardIDs}})
	if err != nil {
		return nil, err
	}
	var rewards []*Reward
	err = cur.All(ctx, &rewards)
	if err != nil {
		return nil, err
	}
	return rewards, nil
}

// IsForUser 判断该奖励是否为发放给用户的
func (reward Reward) IsForUser() bool {
	return reward.Type == TypeUserAppearance || reward.Type == TypeBackpack || reward.Type == TypeUserGiftCustom ||
		(reward.Type == TypeSticker && reward.ElementType == livesticker.TypeUser)
}
