package reward

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/liveuserspecialredpacket"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestReward_SaveRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(514)
	reward := Reward{
		RewardID: 114,
		Type:     TypeGift,
	}
	var record SendRecord
	require.NoError(record.DB().Delete("", "reward_id = ? AND user_id = ?", reward.RewardID, testUserID).Error)
	updateStatusCallback := reward.saveRecord(0, 0, testUserID)

	require.NoError(record.DB().Where("reward_id = ? AND user_id = ?", reward.RewardID, testUserID).Take(&record).Error)
	assert.Equal(reward.RewardID, record.RewardID)
	assert.Equal(reward.Type, record.RewardType)
	assert.Equal(rewardStatusPending, record.Status)
	assert.Equal(testUserID, record.UserID)

	updateStatusCallback(rewardStatusSuccess)
	require.NoError(record.DB().Where("id = ?", record.ID).Take(&record).Error)
	assert.Equal(rewardStatusSuccess, record.Status)
}

func TestRewardSendGift(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URILiveReward, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()

	reward := Reward{
		Type:       TypeGift,
		ElementID:  101,
		ElementNum: 1,
	}
	assert.NoError(reward.sendGift(12))
}

func TestReward_sendGiftByUser(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URILiveReward, func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	reward := Reward{
		Type:       TypeGift,
		ElementID:  101,
		ElementNum: 1,
	}
	assert.NoError(reward.sendGiftByUser(12, 291912818))
}

func TestRewardSendAppearance(t *testing.T) {
	assert := assert.New(t)

	reward := Reward{
		Type:        TypeCreatorAppearance,
		ElementID:   101,
		ElementType: 1,
		Duration:    7 * goutil.SecondOneDay,
	}
	assert.NoError(reward.sendAppearance(12))
}

func TestRewardSendBackpack(t *testing.T) {
	assert := assert.New(t)

	reward := Reward{
		Type:       TypeBackpack,
		ElementID:  30001,
		ElementNum: 1,
		Duration:   7 * goutil.SecondOneDay,
	}
	assert.NoError(reward.sendBackpack(12))
}

func TestRewardCreatorBackpack(t *testing.T) {
	assert := assert.New(t)

	reward := Reward{
		Type:       TypeCreatorBackpack,
		ElementID:  30001,
		ElementNum: 1,
		Duration:   7 * goutil.SecondOneDay,
	}
	assert.NoError(reward.sendCreatorBackpack(12))
}

func TestRewardSendLiveTag(t *testing.T) {
	assert := assert.New(t)

	reward := Reward{
		Type:     TypeLiveTag,
		Duration: 7 * goutil.SecondOneDay,
		URL:      "https://example",
	}
	assert.NoError(reward.sendLiveTag(12))
}

func TestRewardSendNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input interface{}) (output interface{}, err error) {
		raw, ok := input.(json.RawMessage)
		require.True(ok)
		var list []*userapi.BroadcastElem
		require.NoError(json.Unmarshal(raw, &list))
		require.Len(list, 1)
		assert.Equal(1, list[0].Type)
		assert.EqualValues(15, list[0].RoomID)
		assert.Zero(list[0].UserID)
		require.IsType(map[string]interface{}{}, list[0].Payload)
		assert.Equal(`<font color="red">零月</font>`, list[0].Payload.(map[string]interface{})["message"])
		return nil, nil
	})
	defer cancel()

	reward := Reward{
		Type:      TypeNotify,
		ElementID: 1,
		Message:   `<font color="red">${creator_username}</font>`,
	}
	assert.NoError(reward.sendNotify(15, 12))
}

func TestReward_sendCreateCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 8, 30, 23, 59, 59, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	testRoomID := int64(12)

	nowUnix := goutil.TimeNow().Unix()
	err := service.DB.Delete(liverecommendedelements.Model{},
		"element_type = ? AND element_id = ? AND start_time <= ? AND expire_time > ? AND sort > ?",
		liverecommendedelements.ElementCreatorCard, testRoomID, nowUnix, nowUnix, liverecommendedelements.SortDeleted).Error
	require.NoError(err)

	// 测试创建
	r := Reward{
		Type:     TypeCreatorCard,
		URL:      "111",
		Duration: 7 * goutil.SecondOneDay,
		EndTime:  time.Date(2023, 9, 30, 23, 59, 59, 0, time.Local).Unix(),
	}
	err = r.sendCreateCard(testRoomID)
	require.NoError(err)
	element, err := liverecommendedelements.FindOneByElementType(liverecommendedelements.ElementCreatorCard, testRoomID)
	require.NoError(err)
	require.NotNil(element)
	assert.Equal(goutil.TimeNow().Unix()+7*goutil.SecondOneDay, element.ExpireTime)

	// 测试续期
	r = Reward{
		Type:     TypeCreatorCard,
		URL:      "111",
		Duration: 3 * goutil.SecondOneDay,
		EndTime:  time.Date(2023, 9, 30, 23, 59, 59, 0, time.Local).Unix(),
	}
	err = r.sendCreateCard(testRoomID)
	require.NoError(err)
	element, err = liverecommendedelements.FindOneByElementType(liverecommendedelements.ElementCreatorCard, testRoomID)
	require.NoError(err)
	require.NotNil(element)
	assert.Equal(goutil.TimeNow().Unix()+10*goutil.SecondOneDay, element.ExpireTime)

	// 测试大于最大续期时间
	r = Reward{
		Type:     TypeCreatorCard,
		URL:      "111",
		Duration: 31 * goutil.SecondOneDay,
		EndTime:  time.Date(2023, 9, 30, 23, 59, 59, 0, time.Local).Unix(),
	}
	err = r.sendCreateCard(testRoomID)
	require.NoError(err)
	element, err = liverecommendedelements.FindOneByElementType(liverecommendedelements.ElementCreatorCard, testRoomID)
	require.NoError(err)
	require.NotNil(element)
	assert.Equal(r.EndTime, element.ExpireTime)
}

func TestNewRewardFunc(t *testing.T) {
	assert := assert.New(t)

	rewardFunc := NewRewardFunc(1, 2, 3)
	assert.NotNil(rewardFunc)
	rewardFunc(RewardID1)
}

func TestFindRewardByRewardID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	reward, err := findRewardByRewardID(RewardID1)
	require.NoError(err)
	assert.NotNil(reward)
}

func TestFindRewardByRewardIDWithCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyReward1.Format(RewardID1)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	reward, err := FindRewardByRewardIDWithCache(RewardID1)
	require.NoError(err)
	assert.NotNil(reward)

	require.NoError(service.LRURedis.Set(cacheKey, "null", time.Second).Err())
	reward, err = FindRewardByRewardIDWithCache(RewardID1)
	require.NoError(err)
	assert.Nil(reward)
}

func TestFindRewardsMapWithCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rewardIDs := []int64{-1, -2}
	rewardIDsString := goutil.JoinInt64Array(rewardIDs, ",")
	cacheKey := keys.KeyRewards1.Format(rewardIDsString)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	rewardMap, err := FindRewardsMapWithCache(rewardIDs)
	require.NoError(err)
	assert.Empty(rewardMap)

	rewardIDs = []int64{RewardID1, RewardID2}
	rewardIDsString = goutil.JoinInt64Array(rewardIDs, ",")
	cacheKey = keys.KeyRewards1.Format(rewardIDsString)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	rewardMap, err = FindRewardsMapWithCache(rewardIDs)
	require.NoError(err)
	assert.Len(rewardMap, len(rewardIDs))

	require.NoError(service.LRURedis.Set(cacheKey, "null", time.Second).Err())
	rewardMap, err = FindRewardsMapWithCache(rewardIDs)
	require.NoError(err)
	assert.Nil(rewardMap)

	rewardIDs = []int64{RewardID1, RewardID2, RewardID3, RewardID4, RewardID5, RewardID6, RewardID7, RewardID8,
		RewardID9, RewardID10, RewardID11}
	rewardMap, err = FindRewardsMapWithCache(rewardIDs)
	require.NoError(err)
	assert.Len(rewardMap, len(rewardIDs))
}

func TestFindRewards(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rewards, err := FindRewards([]int64{-1, -2})
	require.NoError(err)
	assert.Empty(rewards)

	rewards, err = FindRewards([]int64{RewardID1, RewardID2})
	require.NoError(err)
	assert.Len(rewards, 2)
}

func TestReward_IsForUser(t *testing.T) {
	assert := assert.New(t)

	r := Reward{Type: TypeUserAppearance}
	assert.True(r.IsForUser())
	r.Type = TypeGift
	assert.False(r.IsForUser())
	r.Type = TypeUserGiftCustom
	assert.True(r.IsForUser())
	r.Type = TypeSticker
	assert.False(r.IsForUser())
	r.ElementType = livesticker.TypeUser
	assert.True(r.IsForUser())
}

func TestReward_sendSticker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)

	var (
		testRoomID int64 = 101011010
		testUserID int64 = 291912818
	)

	sticker := &livesticker.LiveSticker{
		Name: "test1",
	}
	err := livesticker.DB().Create(sticker).Error
	require.NoError(err)

	// 测试用户未创建专属表情包的情况
	reward := Reward{
		ElementType: livesticker.TypeUser,
		ElementID:   sticker.ID,
		Duration:    1,
		EndTime:     2,
	}
	err = reward.sendSticker(testRoomID, testUserID)
	require.NoError(err)
	var owner livesticker.PackageOwner
	require.NoError(livesticker.DB().Where("user_id = ?", testUserID).Take(&owner).Error)
	require.NotZero(owner.PackageID)
	var pkg livesticker.Package
	require.NoError(livesticker.DB().Where("id = ?", owner.PackageID).Take(&pkg).Error)
	assert.Equal(reward.ElementType, pkg.Type)
	m, err := pkg.StickerMap(sticker.ID, goutil.TimeNow())
	require.NoError(err)
	require.NotNil(m)
	assert.EqualValues(sticker.ID, m.StickerID)
	assert.EqualValues(1, m.StartTime)
	assert.EqualValues(2, m.ExpireTime)

	// 测试更新过期时间
	reward = Reward{
		ElementType: livesticker.TypeUser,
		ElementID:   sticker.ID,
		Duration:    1,
		EndTime:     10,
	}
	err = reward.sendSticker(testRoomID, testUserID)
	require.NoError(err)
	var maps []*livesticker.PackageStickerMap
	require.NoError(livesticker.DB().Where("package_id = ?", owner.PackageID).Order("id ASC").Take(&maps).Error)
	require.NotEmpty(maps)
	require.EqualValues(sticker.ID, maps[0].StickerID)
	assert.EqualValues(1, maps[0].StartTime)
	assert.EqualValues(3, maps[0].ExpireTime)

	// 测试过期时间小于当前时间
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(5, 0)
	})
	reward = Reward{
		ElementType: livesticker.TypeUser,
		ElementID:   sticker.ID,
		Duration:    1,
		EndTime:     10,
	}
	err = reward.sendSticker(testRoomID, testUserID)
	require.NoError(err)
	m, err = pkg.StickerMap(sticker.ID, goutil.TimeNow())
	require.NoError(err)
	require.NotNil(m)
	require.EqualValues(sticker.ID, m.StickerID)
	assert.EqualValues(5, m.StartTime)
	assert.EqualValues(6, m.ExpireTime)

	// 测试房间专属
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	reward = Reward{
		ElementType: livesticker.TypeRoom,
		ElementID:   sticker.ID,
		Duration:    1,
		EndTime:     10,
	}
	err = reward.sendSticker(testRoomID, testUserID)
	require.NoError(err)
	owner = livesticker.PackageOwner{}
	require.NoError(livesticker.DB().Where("room_id = ?", testRoomID).Take(&owner).Error)
	require.NotZero(owner.PackageID)
	pkg = livesticker.Package{}
	require.NoError(livesticker.DB().Where("id = ?", owner.PackageID).Take(&pkg).Error)
	assert.Equal(reward.ElementType, pkg.Type)
	m, err = pkg.StickerMap(sticker.ID, goutil.TimeNow())
	require.NoError(err)
	require.NotNil(m)
	assert.EqualValues(sticker.ID, m.StickerID)
	assert.EqualValues(1, m.StartTime)
	assert.EqualValues(2, m.ExpireTime)

	// 测试续期大于最大有效期
	reward = Reward{
		ElementType: livesticker.TypeRoom,
		ElementID:   sticker.ID,
		Duration:    2,
		EndTime:     3,
	}
	err = reward.sendSticker(testRoomID, testUserID)
	require.NoError(err)
	owner = livesticker.PackageOwner{}
	require.NoError(livesticker.DB().Where("room_id = ?", testRoomID).Take(&owner).Error)
	require.NotZero(owner.PackageID)
	pkg = livesticker.Package{}
	require.NoError(livesticker.DB().Where("id = ?", owner.PackageID).Take(&pkg).Error)
	assert.Equal(reward.ElementType, pkg.Type)
	m, err = pkg.StickerMap(sticker.ID, goutil.TimeNow())
	require.NoError(err)
	require.NotNil(m)
	assert.EqualValues(sticker.ID, m.StickerID)
	assert.EqualValues(1, m.StartTime)
	assert.EqualValues(3, m.ExpireTime)

	// 测试不过期表情包
	reward = Reward{
		ElementType: livesticker.TypeRoom,
		ElementID:   sticker.ID,
		EndTime:     0,
	}
	err = reward.sendSticker(testRoomID, testUserID)
	require.NoError(err)
	owner = livesticker.PackageOwner{}
	require.NoError(livesticker.DB().Where("room_id = ?", testRoomID).Take(&owner).Error)
	require.NotZero(owner.PackageID)
	pkg = livesticker.Package{}
	require.NoError(livesticker.DB().Where("id = ?", owner.PackageID).Take(&pkg).Error)
	assert.Equal(reward.ElementType, pkg.Type)
	m, err = pkg.StickerMap(sticker.ID, goutil.TimeNow())
	require.NoError(err)
	require.NotNil(m)
	assert.EqualValues(sticker.ID, m.StickerID)
	assert.EqualValues(1, m.StartTime)
	assert.Zero(m.ExpireTime)
}

func TestReward_sendUserGiftCustom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1)
	reward := Reward{
		Type:      TypeUserGiftCustom,
		ElementID: 1,
		Duration:  100,
	}

	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?",
		livecustom.TypeUserCustomGift, userID).Error)

	// 没有奖励情况
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer cancel()
	err := reward.sendUserGiftCustom(userID)
	require.NoError(err)
	res, err := livecustom.FindUserCustomGift(userID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(101, res.EndTime)

	// 有奖励且没过期的情况
	err = reward.sendUserGiftCustom(userID)
	require.NoError(err)
	res, err = livecustom.FindUserCustomGift(userID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(201, res.EndTime)

	// 有奖励且过期的情况
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(300, 0)
	})
	err = reward.sendUserGiftCustom(userID)
	require.NoError(err)
	res, err = livecustom.FindUserCustomGift(userID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(400, res.EndTime)

	// 有过期时间的情况
	reward.EndTime = 450
	err = reward.sendUserGiftCustom(userID)
	require.NoError(err)
	res, err = livecustom.FindUserCustomGift(userID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(450, res.EndTime)

	// 多次发放有过期时间奖励的情况
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(440, 0)
	})
	reward.EndTime = 450
	err = reward.sendUserGiftCustom(userID)
	require.NoError(err)
	res, err = livecustom.FindUserCustomGift(userID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(450, res.EndTime)
}

func TestReward_sendRoomGiftCustom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(1)
	reward := Reward{
		Type:      TypeRoomGiftCustom,
		ElementID: 1,
		Duration:  100,
	}

	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?",
		livecustom.TypeRoomCustomGift, roomID).Error)

	// 测试没有奖励的情况
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer cancel()
	err := reward.sendRoomGiftCustom(roomID)
	require.NoError(err)
	res, err := livecustom.FindRoomCustomGift(roomID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(101, res.EndTime)

	// 测试有奖励且没过期的情况
	err = reward.sendRoomGiftCustom(roomID)
	require.NoError(err)
	res, err = livecustom.FindRoomCustomGift(roomID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(201, res.EndTime)

	// 测试有奖励且过期的情况
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(300, 0)
	})
	err = reward.sendRoomGiftCustom(roomID)
	require.NoError(err)
	res, err = livecustom.FindRoomCustomGift(roomID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(400, res.EndTime)

	// 测试有过期时间的情况
	reward.EndTime = 450
	err = reward.sendRoomGiftCustom(roomID)
	require.NoError(err)
	res, err = livecustom.FindRoomCustomGift(roomID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(450, res.EndTime)

	// 测试多次发放有过期时间奖励的情况
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(440, 0)
	})
	reward.EndTime = 450
	err = reward.sendRoomGiftCustom(roomID)
	require.NoError(err)
	res, err = livecustom.FindRoomCustomGift(roomID, reward.ElementID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(450, res.EndTime)
}

func TestReward_sendBackPackItem(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 3123123123
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	{
		r := Reward{
			ElementID:   1,
			ElementType: backpackitem.TypeNobleTrialCard,
			ElementNum:  2,
			EndTime:     2,
		}
		err = r.sendBackPackItem(testUserID)
		require.NoError(err)
		userItems, err := useritems.Find(bson.M{"user_id": testUserID, "item_id": 1}, nil)
		require.NoError(err)
		require.Len(userItems, 1)
		assert.EqualValues(2, userItems[0].EndTime)
	}
	{
		mockNow := time.Date(2022, 07, 07, 1, 0, 0, 0, time.Local)
		goutil.SetTimeNow(func() time.Time {
			return mockNow
		})
		defer goutil.SetTimeNow(nil)

		// 测试不指定 reward 的 Endtime
		r := Reward{
			ElementID:   2,
			ElementType: backpackitem.TypeLiveMedalTrialCard,
			ElementNum:  2,
			Duration:    86400,
		}
		err = r.sendBackPackItem(testUserID)
		require.NoError(err)
		userItems, err := useritems.Find(bson.M{"user_id": testUserID, "item_id": 2}, nil)
		require.NoError(err)
		require.Len(userItems, 1)
		assert.Equal(mockNow.Unix()+r.Duration, userItems[0].EndTime)
	}
}

func TestReward_sendNobleHorn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(318231292182)
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var user *liveuser.LiveUser
	err := liveuser.Collection().FindOneAndUpdate(ctx,
		bson.M{
			"user_id": testUserID,
		},
		bson.M{
			"$setOnInsert": bson.M{
				"created_time": goutil.TimeNow(),
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true),
	).Decode(&user)
	require.NoError(err)
	require.NotNil(user)
	_, err = usermeta.Collection().UpdateOne(ctx,
		bson.M{
			"user_id": testUserID,
		},
		bson.M{
			"$set": bson.M{
				"noble_horn_num": 0,
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	r := Reward{
		Type:       TypeNobleHorn,
		ElementNum: 2,
	}
	err = r.sendNobleHorn(testUserID)
	require.NoError(err)
	var meta *usermeta.UserMeta
	err = usermeta.Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
	require.NoError(err)
	require.NotNil(meta)
	assert.EqualValues(2, meta.NobleHornNum)
}

func TestReward_SendCustomWelcomeMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	testUserMeta := usermeta.UserMeta{UserID: 1919810}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": testUserMeta.UserID})
	require.NoError(err)
	_, err = usermeta.Collection().InsertOne(ctx, testUserMeta)
	require.NoError(err)
	testReward := Reward{
		Duration: time.Minute.Milliseconds() / 1000,
		EndTime:  now.Add(time.Hour).Unix(),
	}
	require.NoError(testReward.sendCustomWelcomeMessage(testUserMeta.UserID))
	var meta *usermeta.UserMeta
	err = usermeta.Collection().FindOne(ctx, bson.M{"user_id": testUserMeta.UserID}).Decode(&meta)
	require.NoError(err)
	assert.Empty(meta.CustomWelcomeMessage.Text)
	assert.Equal(now.Add(time.Minute).Unix(), meta.CustomWelcomeMessage.ExpireTime)
}

func TestReward_SendSpecialRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rp := liveuserspecialredpacket.LiveUserSpecialRedPacket{
		UserID:  1919810,
		GoodsID: 1818910,
	}
	require.NoError(liveuserspecialredpacket.LiveUserSpecialRedPacket{}.DB().Delete("", "user_id = ?", rp.UserID).Error)
	reward := Reward{
		ElementID:   999,
		ElementType: livegoods.GoodsTypeSuperFan,
	}
	require.NoError(reward.sendSpecialRedPacket(rp.UserID))
	var rp2 liveuserspecialredpacket.LiveUserSpecialRedPacket
	assert.True(servicedb.IsErrNoRows(liveuserspecialredpacket.LiveUserSpecialRedPacket{}.DB().Take(&rp2, "user_id = ?", rp.UserID).Error))
	reward.ElementNum = 1
	require.NoError(reward.sendSpecialRedPacket(rp.UserID))
	assert.True(servicedb.IsErrNoRows(liveuserspecialredpacket.LiveUserSpecialRedPacket{}.DB().Take(&rp2, "user_id = ?", rp.UserID).Error))
	reward.ElementID = 1
	require.NoError(reward.sendSpecialRedPacket(rp.UserID))
	require.NoError(liveuserspecialredpacket.LiveUserSpecialRedPacket{}.DB().Take(&rp2, "user_id = ?", rp.UserID).Error)
	assert.Equal(reward.ElementID, rp2.GoodsID)
	assert.EqualValues(reward.ElementNum, rp2.Num)
	assert.EqualValues(reward.ElementNum, rp2.GainNum)
	assert.Zero(rp2.EndTime)
}

func TestReward_FindBackpackItem(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	reward := Reward{
		Type:       TypeBackpack,
		ElementID:  40007,
		ElementNum: 10,
	}
	// 测试礼物不存在
	item, err := reward.FindBackpackItem()
	require.NoError(err)
	assert.Nil(item)

	// 测试下发的是背包礼物
	reward.ElementID = 40047
	item, err = reward.FindBackpackItem()
	require.NoError(err)
	require.NotNil(item)
	assert.Equal(reward.ElementID, item.GiftID)
	assert.Equal("幸共此时", item.Name)
	assert.Equal("https://static-test.missevan.com/live/gifts/icons/40047.png", item.IconURL)

	// 测试背包道具不存在
	reward.Type = TypeBackPackItem
	reward.ElementID = 111
	item, err = reward.FindBackpackItem()
	require.NoError(err)
	assert.Nil(item)

	// 测试下发背包道具
	reward.ElementID = 1
	item, err = reward.FindBackpackItem()
	require.NoError(err)
	require.NotNil(item)
	assert.Equal(reward.ElementID, item.ItemID)
	assert.Equal("大咖体验卡 1 天", item.Name)
	assert.Equal("https://static-test.missevan.com/backpack_item/vip_card/icon.png", item.IconURL)

	// 测试奖励类型不正确
	reward.Type = TypeCreatorBackpack
	_, err = reward.FindBackpackItem()
	require.Error(err)
}
