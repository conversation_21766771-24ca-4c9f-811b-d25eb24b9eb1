package reward

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(&SendRecord{}, "id", "create_time", "modified_time", "trace_id", "uuid",
		"reward_id", "reward_type", "status", "user_id", "room_id", "creator_id")
}

func TestSendRecord_BeforeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer cancel()

	sr := SendRecord{
		RewardID: 514,
		UserID:   1919,
	}
	require.NoError(sr.DB().Delete("", "reward_id = ?", sr.RewardID).Error)
	require.NoError(sr.DB().Save(&sr).Error)

	var record SendRecord
	require.NoError(record.DB().Where("reward_id = ?", sr.RewardID).First(&record).Error)
	assert.Equal(now.Unix(), record.CreateTime)
	assert.Equal(now.Unix(), record.ModifiedTIme)
}
