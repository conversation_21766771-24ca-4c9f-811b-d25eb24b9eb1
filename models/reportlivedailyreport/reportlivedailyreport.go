package reportlivedailyreport

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TableName 主播每日报表
func TableName() string {
	return "report_live_daily_report"
}

// ReportLiveDailyReport 主播每日报表
type ReportLiveDailyReport struct {
	ID int64 `gorm:"column:id;primary_key"` // 主键
	// FIXME: BizDate 在 MySQL 是 DATE 类型，无时区，后续研究怎么调整
	BizDate                                  time.Time     `gorm:"column:bizdate"`                                        // 业务日期 YYYY-mm-dd
	CreatorID                                int64         `gorm:"column:creator_id"`                                     // 用户 ID
	ListenerUserNum                          int64         `gorm:"column:listener_user_num"`                              // 收听用户数
	ListenerDurationTotal                    util.Float2DP `gorm:"column:listener_duration_total"`                        // 收听总时长（分钟）
	ListenerDurationAvg                      util.Float2DP `gorm:"column:listener_duration_avg"`                          // 人均收听时长（分钟）
	ListenerDurationMoreThanFiveMinutesNum   int64         `gorm:"column:listener_duration_more_than_five_minutes_num"`   // 超过 5 分钟收听时长用户数
	ListenerDurationMoreThanFiveMinutesRatio util.Float2DP `gorm:"column:listener_duration_more_than_five_minutes_ratio"` // 超过 5 分钟收听时长用户数占总收听人数的比例
	Follow                                   int64         `gorm:"column:follow"`                                         // 新增关注人数
	Unfollow                                 int64         `gorm:"column:unfollow"`                                       // 取消关注人数
	FollowerCount                            int64         `gorm:"column:follower_count"`                                 // 每天 0 点 36 分快照的粉丝总数
}

// DB the db instance of ReportLiveDailyReport model
func (l ReportLiveDailyReport) DB() *gorm.DB {
	return service.DB.Table(l.TableName())
}

// TableName table name
func (ReportLiveDailyReport) TableName() string {
	return TableName()
}

// LiveDailyReport 主播每日报表
type LiveDailyReport struct {
	BizDate                                  string
	ListenerUserNum                          int64
	ListenerDurationAvg                      util.Float2DP
	ListenerDurationMoreThanFiveMinutesNum   int64
	ListenerDurationMoreThanFiveMinutesRatio util.Float2DP
	Follow                                   int64
	Unfollow                                 int64
}

// ListLiveDailyReport 生成开始到结束时间的日期报表，传入公会 ID 时，返回筛选时间范围内主播在该公会合约（合约解约、合约失效、合约生效中等状态）时间范围内数据
func ListLiveDailyReport(creatorID, guildID int64, startDate, endDate time.Time) ([]*LiveDailyReport, error) {
	var report []*ReportLiveDailyReport

	fields := "t.bizdate, t.listener_user_num, t.listener_duration_avg" +
		", t.listener_duration_more_than_five_minutes_num" +
		", t.listener_duration_more_than_five_minutes_ratio, t.follow, t.unfollow"

	db := ReportLiveDailyReport{}.DB().
		Table(ReportLiveDailyReport{}.TableName() + " AS t")

	if guildID != 0 {
		// 如果一个用户在当天重复签约同一个公会多次，会出现 JOIN 多条数据的问题
		// 通过 DISTINCT 过滤重复数据
		fields = "DISTINCT " + fields

		// 统计数据按照天颗粒统计，bizdate 为 YYYY-mm-dd 转为时间戳为当日 0 点，筛选数据时会小于合约开始时间，
		// 导致无法显示入会当天数据（只要数据在合约（生效过）当天出现，该天数据就算公会的）
		// 跟合约开始时间判断时，将 bizdate 转为时间戳后 + 86400，解决无法显示入会当天数据问题
		// 合约开始时间 < UNIX_TIMESTAMP(数据业务时间) + 86400 AND 合约结束时间 > UNIX_TIMESTAMP(数据业务时间)
		db = db.Joins(
			"INNER JOIN "+livecontract.TableName()+" AS t1"+
				" ON t.creator_id = t1.live_id"+
				" AND t1.guild_id = ? AND t1.status IN (?)"+
				" AND t1.contract_start < UNIX_TIMESTAMP(t.bizdate) + 86400"+
				" AND t1.contract_end > UNIX_TIMESTAMP(t.bizdate)",
			guildID, []int64{livecontract.StatusUseless, livecontract.StatusFinished,
				livecontract.StatusContracting})
	}
	err := db.Select(fields).Where("t.creator_id = ? AND t.bizdate >= ? AND t.bizdate <= ?",
		creatorID, startDate.Format(goutil.TimeFormatYMD), endDate.Format(goutil.TimeFormatYMD)).
		Order("t.bizdate ASC").Find(&report).Error
	if err != nil {
		return nil, err
	}

	st := goutil.BeginningOfDay(startDate)
	et := goutil.BeginningOfDay(endDate)
	list := make([]*LiveDailyReport, 0, int64(et.Sub(st).Hours()/24)+1)
	// 生成 st 到 et 间的时间列表，采用滑动窗口算法，将匹配的日期数据写入时间列表中
	for d, i, reportLength := st, 0, len(report); goutil.TimeLte(d, et); d = d.AddDate(0, 0, 1) {
		if i < reportLength && d.Format(goutil.TimeFormatYMD) == report[i].BizDate.Format(goutil.TimeFormatYMD) {
			list = append(list, &LiveDailyReport{
				BizDate:                                  report[i].BizDate.Format(goutil.TimeFormatYMD),
				ListenerUserNum:                          report[i].ListenerUserNum,
				ListenerDurationAvg:                      report[i].ListenerDurationAvg,
				ListenerDurationMoreThanFiveMinutesNum:   report[i].ListenerDurationMoreThanFiveMinutesNum,
				ListenerDurationMoreThanFiveMinutesRatio: report[i].ListenerDurationMoreThanFiveMinutesRatio,
				Follow:                                   report[i].Follow,
				Unfollow:                                 report[i].Unfollow,
			})
			i++
			continue
		}
		list = append(list, &LiveDailyReport{BizDate: d.Format(goutil.TimeFormatYMD)})
	}
	return list, nil
}

// GuildDataReportSubQuery 获取公会主播在该公会历史数据（合约状态：合约解约、合约失效、合约生效中等状态）子查询，
// 传入 creatorID 后获取指定主播的历史数据，给列表增加 DISTINCT 过滤用户出现交叉合约，重复签约等导致的 M*N 的重复数据
// 传入 contractLimit 来控制查询历史数据时是否通过公会合约有效期来限制，若不作限制公会管理者可查询到主播不在当前公会时的数据
func GuildDataReportSubQuery(guildID, creatorID int64, startDate, endDate time.Time, contractLimit bool) (*gorm.DB, error) {
	if guildID == 0 {
		return nil, fmt.Errorf("reportlivedailyreport.GuildDataReportSubQuery: guildID is empty")
	}
	fields := "DISTINCT t1.id, t1.bizdate, t1.creator_id, t1.follow, t1.listener_duration_total, t1.listener_duration_more_than_five_minutes_num, t1.listener_user_num" +
		", t1.live_duration, t1.daily_income, t1.first_live_time"
	joinCond := fmt.Sprintf("INNER JOIN %s AS t1 ON t.live_id = t1.creator_id", TableName())
	statusCheck := fmt.Sprintf("t.status = %d", livecontract.StatusContracting)
	if contractLimit {
		// 统计数据按照天颗粒统计，bizdate 为 YYYY-mm-dd 转为时间戳为当日 0 点，筛选数据时会小于合约开始时间，
		// 导致无法显示入会当天数据（只要数据在合约（生效过）当天出现，该天数据就算公会的）
		// 跟合约开始时间判断时，将 bizdate 转为时间戳后 + 86400，解决无法显示入会当天数据问题
		// 合约开始时间 < UNIX_TIMESTAMP(数据业务时间) + 86400 AND 合约结束时间 > UNIX_TIMESTAMP(数据业务时间)
		joinCond += " AND t.contract_start < UNIX_TIMESTAMP(t1.bizdate) + 86400" +
			" AND t.contract_end > UNIX_TIMESTAMP(t1.bizdate)"
		statusCheck = fmt.Sprintf("t.status IN (%d, %d, %d)", livecontract.StatusUseless, livecontract.StatusFinished,
			livecontract.StatusContracting)
	}
	db := service.DB.Table(livecontract.TableName()+" AS t").
		Select(fields).
		Joins(joinCond).
		Where(statusCheck).
		Where("t1.bizdate >= ? AND t1.bizdate <= ?",
			startDate.Format(goutil.TimeFormatYMD), endDate.Format(goutil.TimeFormatYMD)).
		Where("t.guild_id = ?", guildID)
	if creatorID != 0 {
		db = db.Where("t.live_id = ?", creatorID)
	}
	return db, nil
}

// ListLiveDailyReportMap 生成指定用户从开始到结束时间的日期报表
func ListLiveDailyReportMap(fields string, creatorIDs []int64, startDate, endDate time.Time) (map[int64][]*ReportLiveDailyReport, error) {
	if len(creatorIDs) == 0 {
		return make(map[int64][]*ReportLiveDailyReport), nil
	}
	db := ReportLiveDailyReport{}.DB().Table(TableName()).Select(fields+", creator_id, bizdate").Where("creator_id IN (?)", creatorIDs).
		Where("bizdate >= ? AND bizdate <= ?", startDate.Format(goutil.TimeFormatYMD), endDate.Format(goutil.TimeFormatYMD)).
		Order("bizdate ASC")
	var reports []*ReportLiveDailyReport
	err := db.Find(&reports).Error
	if err != nil {
		return nil, err
	}
	if len(reports) == 0 {
		return make(map[int64][]*ReportLiveDailyReport), nil
	}
	resultMap := make(map[int64][]*ReportLiveDailyReport, len(creatorIDs))
	for _, report := range reports {
		if resultMap[report.CreatorID] == nil {
			resultMap[report.CreatorID] = make([]*ReportLiveDailyReport, 0, 2*len(reports)/len(creatorIDs)+1)
		}
		resultMap[report.CreatorID] = append(resultMap[report.CreatorID], report)
	}
	return resultMap, nil
}
