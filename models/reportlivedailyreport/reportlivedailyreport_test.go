package reportlivedailyreport

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestReportLiveDailyReportTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("report_live_daily_report", ReportLiveDailyReport{}.TableName())
}

func TestListLiveDailyReport(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startTime := time.Date(2021, 06, 01, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2021, 06, 07, 0, 0, 0, 0, time.Local)
	// 自己查看
	list, err := ListLiveDailyReport(2333666, 0, startTime, endTime)
	require.NoError(err)
	// 6 月 1 到 6 月 7 的天数
	require.Len(list, 7)
	assert.Equal(int64(20), list[0].Unfollow)
	assert.Equal(int64(20), list[1].Unfollow)
	assert.Equal(int64(20), list[2].Unfollow)
	assert.Equal(int64(20), list[3].Unfollow)
	assert.Equal(int64(20), list[4].Unfollow)
	assert.Equal(int64(20), list[5].Unfollow)
	assert.Equal(int64(0), list[6].Unfollow)

	// 公会查看
	list, err = ListLiveDailyReport(2333666, 3, startTime, endTime)
	require.NoError(err)
	// 6 月 1 到 6 月 7 的天数
	require.Len(list, 7)
	// 主播有两份合约，第一份时间：2021-06-01 至 2021-06-03
	assert.Equal(int64(20), list[0].Unfollow)
	assert.Equal(int64(20), list[1].Unfollow)
	// 不能查看不在合约范围内的数据
	assert.Equal(int64(0), list[2].Unfollow)
	assert.Equal(int64(0), list[3].Unfollow)
	// 主播有两份合约，第二份时间：2021-06-05 至 2021-06-07
	assert.Equal(int64(20), list[4].Unfollow)
	assert.Equal(int64(20), list[5].Unfollow)
	assert.Equal(int64(0), list[6].Unfollow)
}

func TestListLiveDailyReportMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startTime := time.Date(2021, 06, 01, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2021, 06, 07, 0, 0, 0, 0, time.Local)

	testCreatorID := int64(2333666)
	respMap, err := ListLiveDailyReportMap("unfollow", []int64{testCreatorID}, startTime, endTime)
	require.NoError(err)
	list := respMap[testCreatorID]
	require.NotNil(list)
	require.Len(list, 6)
	assert.Equal(int64(20), list[0].Unfollow)
	assert.Equal(int64(20), list[1].Unfollow)
	assert.Equal(int64(20), list[2].Unfollow)
	assert.Equal(int64(20), list[3].Unfollow)
	assert.Equal(int64(20), list[4].Unfollow)
	assert.Equal(int64(20), list[5].Unfollow)
}
