package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/x/bsonx"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 迁移到独立的 playback 包内
const (
	// OnePage 一页显示的数目
	OnePage = 20

	// SplitMergeDuration split merge duration
	SplitMergeDuration = 5 * time.Minute
)

// 回放状态
const (
	ArchiveUnready = iota - 2
	ArchiveFailed
	ArchiveReadyDownload
	ArchiveDownloaded
	ArchiveFinished
	ArchiveShowing
)

// PlaybackURL is the playback url model
type PlaybackURL struct {
	URL string `bson:"url"`
}

// CollectionNamePlayback collection name without prefix
const CollectionNamePlayback = "playbacks"

// Playback is the playback model
type Playback struct {
	OID      primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	RoomOID  primitive.ObjectID `bson:"_room_id" json:"-"`
	RoomID   int64              `bson:"room_id" json:"room_id"`
	UserID   int64              `bson:"user_id" json:"user_id"`
	Username string             `bson:"username,omitempty" json:"username"`
	URL      *string            `bson:"url,omitempty" json:"-"`
	URLs     []PlaybackURL      `bson:"urls,omitempty" json:"-"`
	Filename *string            `bson:"filename,omitempty" json:"-"`
	SoundID  *int64             `bson:"sound_id,omitempty" json:"sound_id"`
	Duration *float64           `bson:"duration,omitempty" json:"duration"`

	// 标识，音频 ID，录制 ID，不同渠道的录制不一样，有网易云的和阿里云的
	ChannelID   *string `bson:"channelid,omitempty" json:"-"`
	Vid         *string `bson:"vid,omitempty" json:"-"`
	Title       string  `bson:"title,omitempty" json:"title"`
	Description string  `bson:"description,omitempty" json:"description"`
	Cover       *string `bson:"cover,omitempty" json:"cover"`
	CoverURL    string  `bson:"-" json:"cover_url"`
	Size        int64   `bson:"size,omitempty" json:"-"` // bytes

	// -2 UNREADY, -1 回放处理失败
	// 0 是准备下载，1 是下载完成且保存到 oss 中，2 是在我们网站中转码和给用户生成回放完成，
	// 3 表示它会被显示到直播间首页里的直播回放里
	Archive     int       `bson:"archive" json:"-"`
	ArchiveTime time.Time `bson:"archive_time" json:"-"`
	Priority    int       `bson:"priority" json:"-"` // 回放处理优先级

	// TODO: 修改为 goutil.TimeUnixMilli
	CreatedTime int64     `bson:"created_time" json:"created_time"`
	UpdatedTime time.Time `bson:"updated_time" json:"-"`
}

// GetCover string
func (pb Playback) GetCover() string {
	if pb.Cover == nil {
		return ""
	}
	return *pb.Cover
}

// SchemeToURL string
func (pb *Playback) SchemeToURL() string {
	if pb.CoverURL != "" {
		return pb.CoverURL
	}
	if pb.Cover == nil {
		return ""
	}
	pb.CoverURL = service.Storage.Parse(config.Conf.Params.URL.CDN + *pb.Cover)
	return pb.CoverURL
}

// Update playback's value
func (pb Playback) Update(v bson.M) error {
	collection := service.MongoDB.Collection("playbacks")

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	v["updated_time"] = bsonx.Time(goutil.TimeNow())
	_, err := collection.UpdateOne(ctx, bson.M{"_id": pb.OID}, bson.M{
		"$set": v,
	})
	return err
}

// CountPage tells you how many pages of playbacks are matching the condition where the "archive" is 3
func (pb *Playback) CountPage() (int64, error) {
	collection := service.MongoDB.Collection("playbacks")

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	r, err := collection.CountDocuments(ctx, bson.M{"archive": 3})
	if err != nil {
		return 0, err
	}
	t := int64(0)
	if r%OnePage > 0 {
		t = 1
	}
	return r/OnePage + t, nil
}

// FindByPage returns the specified range of Playbacks
func (pb *Playback) FindByPage(page int64) (pbs []Playback, err error) {
	if page <= 0 {
		return []Playback{}, nil
	}

	collection := service.MongoDB.Collection("playbacks")

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	opt := options.Find().SetSort(bson.M{"created_time": -1}).
		SetSkip((page - 1) * OnePage).
		SetLimit(OnePage)

	cursor, err := collection.Find(ctx, bson.M{"archive": 3}, opt)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	err = cursor.All(ctx, &pbs)
	return pbs, err
}

var defaultPlaybackCover = "coversmini/nocover.png"

// AddPlaybackInfo processes the cover URLs of playbacks
func AddPlaybackInfo(playbacks []Playback) {
	for i := range playbacks {
		if playbacks[i].Cover == nil || *playbacks[i].Cover == "" {
			playbacks[i].Cover = &defaultPlaybackCover
		}
		playbacks[i].SchemeToURL()
	}
}

// CountRoomPlaybacksPage tells you how many pages of playbacks are matching the condition where the "archive" is 2
// and "_room_id" is the specified roomID_
func (pb *Playback) CountRoomPlaybacksPage(roomOID primitive.ObjectID, roomID int64) (int64, error) {
	collection := service.MongoDB.Collection(CollectionNamePlayback)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	r, err := collection.CountDocuments(ctx, bson.M{"_room_id": roomOID, "archive": 2})
	if err != nil {
		return 0, err
	}

	t := int64(0)
	if r%OnePage > 0 {
		t = 1
	}
	return r/OnePage + t, nil
}

// FindRoomPlaybacksByPage returns the specified range of Playbacks
func (pb *Playback) FindRoomPlaybacksByPage(roomOID primitive.ObjectID, roomID int64, page int64) (pbs []Playback,
	err error) {
	if page <= 0 {
		return []Playback{}, nil
	}

	collection := service.MongoDB.Collection(CollectionNamePlayback)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	opt := options.Find().SetSort(bson.M{"created_time": -1}).
		SetSkip((page - 1) * OnePage).
		SetLimit(OnePage)

	cursor, err := collection.Find(ctx, bson.M{"_room_id": roomOID, "archive": 2}, opt)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	err = cursor.All(ctx, &pbs)
	return pbs, err
}

// UpdatePlayback 关播后修改回放的 archive_time 和优先级信息，调用生成回放接口
func UpdatePlayback(roomID, creatorID int64, openTime, closeTime time.Time, priority int) error {
	collection := service.MongoDB.Collection(CollectionNamePlayback)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	filter := bson.M{"room_id": roomID, "archive": ArchiveUnready}
	update := bson.M{
		"$set": bson.M{
			"archive":      ArchiveReadyDownload,
			"archive_time": now.Add(SplitMergeDuration),
			"priority":     priority,
			"updated_time": now,
		},
	}
	_, err := collection.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	_, err = service.BvcLive.ReplayStart(&bvc.ReplayStartRequest{
		StreamName: service.BvcLive.StreamName(roomID, creatorID),
		StartTime:  openTime.Unix(),
		EndTime:    closeTime.Unix(),
	})
	if err != nil {
		return err
	}
	return nil
}

// SetPriority 设置未完成下载的回放处理优先级
func SetPriority(playBackOID primitive.ObjectID, priority int) (bool, error) {
	collection := service.MongoDB.Collection(CollectionNamePlayback)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	filter := bson.M{"_id": playBackOID, "archive": bson.M{"$lte": ArchiveReadyDownload}}
	update := bson.M{
		"$set": bson.M{
			"priority":     priority,
			"updated_time": now,
		},
	}
	err := collection.FindOneAndUpdate(ctx, filter, update).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
