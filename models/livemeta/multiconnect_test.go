package livemeta

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMultiConnectSettings_IsEnableInvite(t *testing.T) {
	assert := assert.New(t)

	settings := &MultiConnectSettings{
		DisableInvite: goutil.NewInt(1),
	}
	assert.False(settings.IsEnableInvite())

	settings.DisableInvite = goutil.NewInt(0)
	assert.True(settings.IsEnableInvite())
}

func TestMultiConnectSettings_IsEnableUnfollowedInvite(t *testing.T) {
	assert := assert.New(t)

	settings := &MultiConnectSettings{
		DisableInvite:           goutil.NewInt(1),
		DisableUnfollowedInvite: goutil.NewInt(1),
	}
	assert.False(settings.IsEnableUnfollowedInvite())

	settings.DisableInvite = goutil.NewInt(0)
	assert.False(settings.IsEnableUnfollowedInvite())

	settings.DisableUnfollowedInvite = goutil.NewInt(0)
	assert.True(settings.IsEnableUnfollowedInvite())
}

func TestMultiConnectSettings_IsEnableApply(t *testing.T) {
	assert := assert.New(t)

	settings := &MultiConnectSettings{
		DisableApply: goutil.NewInt(1),
	}
	assert.False(settings.IsEnableApply())

	settings.DisableApply = goutil.NewInt(0)
	assert.True(settings.IsEnableApply())
}

func TestUpdateMultiConnectSettings(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(13131313)
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	roomOID, _ := primitive.ObjectIDFromHex("62566426b990806cf29feb64")
	_, err := Collection().UpdateOne(ctx,
		bson.M{"_room_id": roomOID, "room_id": 13131313},
		bson.M{"$unset": bson.M{
			"multi_connect_settings.disable_invite":            0,
			"multi_connect_settings.disable_apply":             0,
			"multi_connect_settings.disable_unfollowed_invite": 0,
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	settings := &MultiConnectSettings{
		DisableInvite:           goutil.NewInt(1),
		DisableUnfollowedInvite: goutil.NewInt(1),
		DisableApply:            goutil.NewInt(1),
	}
	settings, err = settings.Update(testRoomID)
	require.NoError(err)
	require.NotNil(settings)
	assert.EqualValues(1, *settings.DisableInvite)
	assert.EqualValues(1, *settings.DisableApply)
	assert.EqualValues(1, *settings.DisableUnfollowedInvite)

	settings = &MultiConnectSettings{
		DisableInvite: goutil.NewInt(0),
		DisableApply:  goutil.NewInt(0),
	}
	settings, err = settings.Update(testRoomID)
	require.NoError(err)
	require.NotNil(settings)
	assert.EqualValues(0, *settings.DisableInvite)
	assert.EqualValues(0, *settings.DisableApply)
	assert.EqualValues(0, *settings.DisableUnfollowedInvite)
}

func TestFindMultiConnectSettings(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	roomOID, _ := primitive.ObjectIDFromHex("62566426b990806cf29feb64")
	_, err := Collection().UpdateOne(ctx,
		bson.M{"_room_id": roomOID, "room_id": 13131313},
		bson.M{"$set": bson.M{
			"multi_connect_settings.disable_invite":            1,
			"multi_connect_settings.disable_apply":             1,
			"multi_connect_settings.disable_unfollowed_invite": 1,
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	settings, err := FindMultiConnectSettings(13131313)
	require.NoError(err)
	require.NotNil(settings)
	assert.EqualValues(1, *settings.DisableInvite)
	assert.EqualValues(1, *settings.DisableApply)
	assert.EqualValues(1, *settings.DisableUnfollowedInvite)

	settings, err = FindMultiConnectSettings(77887788)
	require.NoError(err)
	assert.Nil(settings)
}
