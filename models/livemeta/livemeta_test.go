package livemeta

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testRoomID = int64(22489473)

var (
	testLiveMeta LiveMeta
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	run(m)
}

func run(m *testing.M) int {
	resetTestData()
	return m.Run()
}

func resetTestData() {
	orgLiveMeta, err := Find(testRoomID)
	if err != nil {
		logger.Fatal(err)
	}
	testLiveMeta = LiveMeta{
		RoomOID:      orgLiveMeta.RoomOID,
		RoomID:       testRoomID,
		Online:       1,
		Accumulation: 12,
		Ratio:        1.3,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	err = collection.FindOneAndUpdate(ctx, bson.M{"room_id": testRoomID}, bson.M{"$set": testLiveMeta}).Err()
	if err != nil {
		panic(err)
	}
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Ban{}, "type", "duration", "start_time", "expire_time")
	kc.Check(PKSettings{}, "disable_invite", "unfollowed_invite")
	kc.Check(LiveMeta{}, "_id", "_room_id", "room_id", "open", "online", "accumulation", "ratio",
		"ban", "pk_settings", "extra_score", "playback_priority",
		"speak_settings", "multi_connect_settings")
	kc.Check(Simple{}, "_id", "_room_id", "room_id", "online", "accumulation", "ratio", "ban")
	kc.Check(SpeakSettings{}, "enable", "all", "live_level", "medal")
	kc.Check(MedalInfo{}, "level", "super_fan")
	kc.Check(MultiConnectSettings{}, "disable_invite", "disable_unfollowed_invite", "disable_apply")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Ban{}, "type", "duration", "start_time", "expire_time")
	kc.Check(PKSettings{}, "disable_invite", "unfollowed_invite")
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lm, err := Find(123456)
	assert.True(err == nil && lm == nil, err)
	lm, err = Find(testRoomID)
	require.NoError(err)
	require.NotNil(lm)
	assert.Equal(lm.RoomOID, testLiveMeta.RoomOID)
}

func TestBanAndUnban(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(-13)
	meta := LiveMeta{
		RoomOID:      primitive.NewObjectID(),
		RoomID:       testRoomID,
		Online:       1,
		Accumulation: 12,
		Ratio:        1.3,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	err := collection.FindOneAndUpdate(ctx, bson.M{"room_id": testRoomID}, bson.M{"$set": meta},
		options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.True(err == nil || mongodb.IsNoDocumentsError(err))

	// 按时间封禁
	now := goutil.TimeNow()
	startTime := now.Unix()
	expireTime := now.Add(time.Minute).Unix()
	require.NoError(BanRoom(testRoomID, OneMinute, startTime, TypeBanDuration))
	b, err := FindBanned(testRoomID)
	require.NoError(err)
	require.NotNil(b)
	assert.Equal(TypeBanDuration, b.Type)
	assert.Equal(OneMinute, *b.Duration)
	assert.Equal(expireTime, *b.ExpireTime)

	// 永封
	require.NoError(BanRoom(testRoomID, 0, startTime, TypeBanForever))
	b, err = FindBanned(testRoomID)
	require.NoError(err)
	require.NotNil(b)
	assert.Equal(startTime, b.StartTime)
	assert.Equal(TypeBanForever, b.Type)
	assert.Nil(b.ExpireTime)
	assert.Nil(b.Duration)

	// 解封
	require.NoError(Unban(testRoomID))
	b, err = FindBanned(testRoomID)
	require.NoError(err)
	assert.Nil(b)
}

func TestFindBanned(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(20201010)
	now := goutil.TimeNow().Unix()
	when := now - OneMinute
	meta := LiveMeta{
		RoomOID: primitive.NewObjectID(),
		RoomID:  testRoomID,
		Ban: &Ban{
			Type:       TypeBanDuration,
			StartTime:  now,
			ExpireTime: &when,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID}, bson.M{"$set": meta},
		options.Update().SetUpsert(true))
	require.NoError(err)

	b, err := FindBanned(testRoomID)
	require.NoError(err)
	assert.Nil(b)
	lm, err := Find(testRoomID)
	require.NoError(err)
	assert.Nil(lm.Ban)
}

func TestFindSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	lms, err := FindSimple(123456)
	assert.True(err == nil && lms == nil, err)
	lms, err = FindSimple(testRoomID)
	require.NoError(err)
	require.NotNil(lms)
	assert.Equal(testLiveMeta.RoomOID, lms.RoomOID)
}

func TestFindSimples(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	simples, err := FindSimples([]int64{testRoomID, -123})
	require.NoError(err)
	require.Len(simples, 1)
	assert.Equal(testLiveMeta.RoomOID, simples[0].RoomOID)
}

func TestSimpleSliceToMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m, err := SimpleSliceToMap(FindSimples([]int64{testRoomID, -123}))
	require.NoError(err)
	assert.NotNil(m[testRoomID])

	m, err = SimpleSliceToMap([]*Simple{nil, {RoomID: 123}, {RoomID: 456}})
	require.NoError(err)
	require.Len(m, 2)
	assert.NotNil(m[123])
	assert.NotNil(m[456])

	expectErr := errors.New("test err")
	m, err = SimpleSliceToMap([]*Simple{nil, {RoomID: 123}, {RoomID: 456}},
		expectErr)
	require.Len(m, 2)
	assert.Equal(expectErr.Error(), err.Error())
}

func TestSetOpen(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	testRoomID := int64(5389336)
	_, err := col.UpdateOne(ctx, bson.M{"room_id": testRoomID}, bson.M{
		"$set": bson.M{
			"room_id":      testRoomID,
			"open":         1,
			"accumulation": 100,
			"accids":       []string{"1", "2"},
		}}, options.Update().SetUpsert(true))
	require.NoError(err)
	require.NoError(SetOpen(testRoomID, false))
	var l LiveMeta
	require.NoError(col.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&l))
	assert.Zero(l.Open)
	assert.Zero(l.Accumulation)
	require.NoError(SetOpen(testRoomID, true))
	require.NoError(col.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&l))
	assert.True(util.IntToBool(l.Open))
}

func TestSetExtraScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	testRoomID := int64(4381915)
	startTime := goutil.TimeNow().Add(2 * time.Minute)
	endTime := goutil.TimeNow().Add(4 * time.Minute)
	require.NoError(SetExtraScore([]int64{testRoomID}, 90.2, startTime, endTime))
	var l LiveMeta
	require.NoError(col.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&l))
	assert.NotEmpty(l.ExtraScore)
	assert.Equal(&ExtraScore{90.2, startTime.Unix(), endTime.Unix()}, l.ExtraScore[len(l.ExtraScore)-1])
}

func TestClearExtraScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testRoomID := int64(4381915)

	extraScore := ExtraScore{
		Score:     123,
		StartTime: goutil.TimeNow().Add(time.Hour).Unix(),
		EndTime:   goutil.TimeNow().Add(3 * time.Hour).Unix(),
	}
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{
			"extra_score": []ExtraScore{extraScore},
		}})
	require.NoError(err)
	require.NoError(ClearExtraScore([]int64{testRoomID}))
	var l LiveMeta
	require.NoError(Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&l))
	assert.Nil(l.ExtraScore)
}

func TestSetAndFindPlaybackPriority(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(4381915)
	ok, err := SetPlaybackPriority(testRoomID, 1)
	require.NoError(err)
	assert.True(ok)
	assert.Equal(1, FindPlaybackPriority(testRoomID))

	testRoomID = 99999999
	ok, err = SetPlaybackPriority(testRoomID, 1)
	require.NoError(err)
	assert.False(ok)
	assert.Zero(FindPlaybackPriority(testRoomID))
}

func TestFindPKSettings(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	roomOID, _ := primitive.ObjectIDFromHex("62566426b990806cf29feb64")
	_, err := Collection().UpdateOne(ctx,
		bson.M{"_room_id": roomOID, "room_id": 13131313},
		bson.M{"$set": bson.M{"pk_settings.unfollowed_invite": 1}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	settings, err := FindPKSettings(13131313)
	require.NoError(err)
	require.NotNil(settings)
	assert.EqualValues(0, settings.DisableInvite)
	assert.EqualValues(1, settings.UnfollowedInvite)

	settings, err = FindPKSettings(77887788)
	require.NoError(err)
	assert.Nil(settings)
}

func TestFindSpeakSettings(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	var lm LiveMeta
	require.NoError(col.FindOne(ctx, bson.M{}).Decode(&lm))

	ss := &SpeakSettings{
		Enable: true,
		All:    true,
	}
	require.NoError(SetSpeakSettings(lm.RoomID, ss))
	ss2, err := FindSpeakSettings(lm.RoomID)
	require.NoError(err)
	assert.Equal(ss, ss2)

	require.NoError(SetSpeakSettings(lm.RoomID, nil))
	ss, err = FindSpeakSettings(lm.RoomID)
	require.NoError(err)
	assert.Nil(ss)
}

func TestSpeakSettings_IsSpeakLimited(t *testing.T) {
	assert := assert.New(t)

	// 未开启
	ss := SpeakSettings{Enable: false}
	assert.False(ss.IsSpeakLimited(0, nil))

	// 全部禁言
	ss.Enable = true
	ss.All = true
	assert.True(ss.IsSpeakLimited(100, nil))

	// 只限制直播等级
	ss.All = false
	ss.LiveLevel = 10
	assert.True(ss.IsSpeakLimited(9, nil))
	assert.False(ss.IsSpeakLimited(10, nil))

	// 只限制粉丝
	ss.LiveLevel = 0
	ss.Medal = &MedalInfo{
		Level: 5,
	}
	assert.True(ss.IsSpeakLimited(0, nil))
	assert.True(ss.IsSpeakLimited(0, &MedalInfo{Level: 4}))
	assert.False(ss.IsSpeakLimited(0, &MedalInfo{Level: 5}))
	// 仅要求是超粉
	ss.Medal = &MedalInfo{Level: 0, SuperFan: true}
	assert.True(ss.IsSpeakLimited(0, nil))
	assert.True(ss.IsSpeakLimited(0, &MedalInfo{Level: 4}))
	assert.False(ss.IsSpeakLimited(0, &MedalInfo{Level: 0, SuperFan: true}))
	// 要求超粉并且等级有要求
	ss.Medal = &MedalInfo{Level: 5, SuperFan: true}
	assert.True(ss.IsSpeakLimited(0, nil))
	assert.True(ss.IsSpeakLimited(0, &MedalInfo{Level: 4, SuperFan: true}))
	assert.False(ss.IsSpeakLimited(0, &MedalInfo{Level: 5, SuperFan: true}))

	// 同时限制直播等级和粉丝
	ss.LiveLevel = 10
	ss.Medal = &MedalInfo{Level: 5}
	assert.True(ss.IsSpeakLimited(0, nil))
	assert.False(ss.IsSpeakLimited(10, nil), "满足直播等级")
	assert.False(ss.IsSpeakLimited(0, &MedalInfo{Level: 5}), "满足粉丝")
}
