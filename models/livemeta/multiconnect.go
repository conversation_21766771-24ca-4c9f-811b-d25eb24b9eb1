package livemeta

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MultiConnectSettings 主播连线相关的设置
type MultiConnectSettings struct {
	DisableInvite           *int `bson:"disable_invite,omitempty"`            // 是否允许连线邀请；0：允许；1：不允许
	DisableUnfollowedInvite *int `bson:"disable_unfollowed_invite,omitempty"` // 是否接受未关注人的连线邀请；0：接受；1：不接受
	DisableApply            *int `bson:"disable_apply,omitempty"`             // 是否允许其他主播申请加入连线；0: 允许；1：不允许
}

// IsEnableInvite 是否允许连线邀请
func (settings *MultiConnectSettings) IsEnableInvite() bool {
	return settings.DisableInvite == nil || *settings.DisableInvite == 0
}

// IsEnableUnfollowedInvite 是否接受未关注人的连线邀请
func (settings *MultiConnectSettings) IsEnableUnfollowedInvite() bool {
	return settings.IsEnableInvite() && (settings.DisableUnfollowedInvite == nil || *settings.DisableUnfollowedInvite == 0)
}

// IsEnableApply 是否允许其他主播申请加入连线
func (settings *MultiConnectSettings) IsEnableApply() bool {
	return settings.DisableApply == nil || *settings.DisableApply == 0
}

// Update 更新房间主播连线设置
func (settings *MultiConnectSettings) Update(roomID int64) (*MultiConnectSettings, error) {
	update := bson.M{}
	if settings.DisableInvite != nil {
		update["multi_connect_settings.disable_invite"] = *settings.DisableInvite
		// 关闭接受邀请, 也需要关闭接受未关注主播的邀请；打开接受邀请, 也需要打开接受未关注主播的邀请
		settings.DisableUnfollowedInvite = goutil.NewInt(*settings.DisableInvite)
	}
	if settings.DisableUnfollowedInvite != nil {
		update["multi_connect_settings.disable_unfollowed_invite"] = *settings.DisableUnfollowedInvite
	}
	if settings.DisableApply != nil {
		update["multi_connect_settings.disable_apply"] = *settings.DisableApply
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	meta := new(LiveMeta)
	err := Collection().FindOneAndUpdate(
		ctx,
		bson.M{
			"room_id": roomID,
		},
		bson.M{
			"$set": update,
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetProjection(bson.M{"multi_connect_settings": 1}),
	).Decode(&meta)
	if err != nil {
		return nil, err
	}
	return meta.MultiConnectSettings, nil
}

// FindMultiConnectSettings 获取房间主播连线设置的信息
// 如未查询到 live_meta 记录, 则 MultiConnectSettings 返回 nil
// 如该房间的 live_meta 记录不存在 multi_connect_settings 属性, 则返回默认的主播连线设置
func FindMultiConnectSettings(roomID int64) (*MultiConnectSettings, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	meta := new(LiveMeta)
	err := Collection().FindOne(ctx, bson.M{
		"room_id": roomID,
	}, options.FindOne().SetProjection(bson.M{"multi_connect_settings": 1})).Decode(meta)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	if meta.MultiConnectSettings == nil {
		meta.MultiConnectSettings = new(MultiConnectSettings)
	}
	return meta.MultiConnectSettings, nil
}
