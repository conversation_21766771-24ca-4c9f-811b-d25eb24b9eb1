package livemeta

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// time
const (
	OneDay    int64 = 24 * 60 * 60 // OneDay 一天时长（单位：秒）
	OneMinute int64 = 60           // OneMinute 一分钟时长（单位：秒）
)

// CollectionName collection name without prefix
const CollectionName = "live_meta"

var findSimpleOperators = bson.M{
	"$project": bson.M{"_id": 1, "_room_id": 1, "room_id": 1, "online": 1,
		"accumulation": 1, "ratio": 1, "ban": 1,
	},
}

// 封禁类型
const (
	TypeBanForever  = iota // 永久封禁
	TypeBanDuration        // 封禁一段时间
)

// Ban 封禁信息
type Ban struct {
	Type     int    `bson:"type" json:"type"`
	Duration *int64 `bson:"duration" json:"duration,omitempty"` // 封禁时长，单位：秒

	// 封禁开始结束时间为秒级时间戳
	StartTime  int64  `bson:"start_time" json:"start_time"`
	ExpireTime *int64 `bson:"expire_time,omitempty" json:"expire_time,omitempty"`
}

// PKSettings 存放主播 PK 相关的设置
type PKSettings struct {
	DisableInvite    int `bson:"disable_invite,omitempty" json:"disable_invite"`       // 是否接受 PK 邀请; 0：接受，1：不接受
	UnfollowedInvite int `bson:"unfollowed_invite,omitempty" json:"unfollowed_invite"` // 是否接受我未关注的主播 PK 邀请; 0：不接受；1：接受
}

// SpeakSettings 发言设置
type SpeakSettings struct {
	Enable    bool       `bson:"enable"`
	All       bool       `bson:"all"`
	LiveLevel int        `bson:"live_level"`
	Medal     *MedalInfo `bson:"medal,omitempty"`
}

// MedalInfo 粉丝勋章设置
type MedalInfo struct {
	Level    int  `bson:"level"`
	SuperFan bool `bson:"super_fan"`
}

// LiveMeta document in collection live_meta
type LiveMeta struct {
	OID primitive.ObjectID `bson:"_id,omitempty"`

	RoomOID      primitive.ObjectID `bson:"_room_id"`
	RoomID       int64              `bson:"room_id"`
	Open         int                `bson:"open"`
	Online       int64              `bson:"online"`
	Accumulation int64              `bson:"accumulation"`
	Ratio        float64            `bson:"ratio"`

	Ban              *Ban          `bson:"ban,omitempty"`
	PKSettings       *PKSettings   `bson:"pk_settings,omitempty"`
	ExtraScore       []*ExtraScore `bson:"extra_score,omitempty"`
	PlayBackPriority int           `bson:"playback_priority"` // 处理直播回放的优先级，数值越大，越优先

	// 发言设置
	SpeakSettings *SpeakSettings `bson:"speak_settings,omitempty"`

	// 主播连线设置
	MultiConnectSettings *MultiConnectSettings `bson:"multi_connect_settings,omitempty"`
}

// ExtraScore 奖励热度
type ExtraScore struct {
	Score     float64 `bson:"score"`
	StartTime int64   `bson:"start_time"`
	EndTime   int64   `bson:"end_time"`
}

// Simple live_meta 不返回 accids，返回 accids 的尺寸
type Simple struct {
	OID          primitive.ObjectID `bson:"_id"`
	RoomOID      primitive.ObjectID `bson:"_room_id"`
	RoomID       int64              `bson:"room_id"`
	Online       int64              `bson:"online"`
	Accumulation int64              `bson:"accumulation"`
	Ratio        float64            `bson:"ratio"`

	Ban *Ban `bson:"ban,omitempty"`
}

// Collection 返回 LiveMeta 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// Find 查询 roomID 所对应的 live_meta, 返回 LiveMeta
func Find(roomID int64) (*LiveMeta, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	lm := new(LiveMeta)
	err := collection.FindOne(ctx, bson.M{"room_id": roomID}).Decode(lm)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			err = nil
		}
		return nil, err
	}
	return lm, nil
}

// BanRoom 封禁
// NOTICE: duration 封禁时间单位秒
func BanRoom(roomID, duration, startTime int64, durationType int) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var ban *Ban
	switch durationType {
	case TypeBanForever:
		ban = &Ban{
			Type:      TypeBanForever,
			StartTime: startTime,
		}
	case TypeBanDuration:
		expireTime := startTime + duration
		ban = &Ban{
			StartTime:  startTime,
			Type:       durationType,
			Duration:   &duration,
			ExpireTime: &expireTime,
		}
	default:
		panic(fmt.Sprintf("unsupported type: %d", durationType))
	}
	return Collection().FindOneAndUpdate(ctx, bson.M{"room_id": roomID},
		bson.M{"$set": bson.M{"ban": ban}}).Err()
}

// Unban 解封
func Unban(roomID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	return Collection().FindOneAndUpdate(ctx, bson.M{"room_id": roomID},
		bson.M{"$unset": bson.M{"ban": ""}}).Err()
}

// FindBanned 获取房间封禁信息, 未被封禁时返回 nil
// 发现房间封禁时间到期后，会移除 ban 字段
func FindBanned(roomID int64) (*Ban, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var lm LiveMeta
	filter := bson.M{
		"room_id": roomID,
	}
	err := Collection().FindOne(ctx, filter).Decode(&lm)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			err = nil
		}
		return nil, err
	}
	// 永封和按时间封禁未过期时返回封禁信息
	if lm.Ban == nil || lm.Ban.Type == TypeBanForever || goutil.TimeNow().Unix() < *lm.Ban.ExpireTime {
		return lm.Ban, nil
	}
	// 发现已经过期则移除封禁状态
	if _, err = Collection().UpdateOne(ctx, bson.M{"room_id": roomID, "ban.expire_time": lm.Ban.ExpireTime},
		bson.M{"$unset": bson.M{"ban": ""}}); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, nil
}

// FindPKSettings 获取房间 PK 设置的信息
// 如未查询到 live_meta 记录, 则 PKSettings 返回 nil
// 如该房间的 live_meta 记录不存在 pk_settings 属性, 则返回默认的 PK 设置
func FindPKSettings(roomID int64) (*PKSettings, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	meta := new(LiveMeta)
	err := Collection().FindOne(ctx, bson.M{
		"room_id": roomID,
	}, options.FindOne().SetProjection(bson.M{"pk_settings": 1})).Decode(meta)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	if meta.PKSettings == nil {
		meta.PKSettings = new(PKSettings)
	}
	return meta.PKSettings, nil
}

// FindSimple 查询 roomID 所对应的 live_meta, 返回 Simple
func FindSimple(roomID int64) (*Simple, error) {
	return FindOneSimple(bson.M{"room_id": roomID})
}

// FindOneSimple 查找单个 simple 通用接口
func FindOneSimple(match interface{}) (*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lm := new(Simple)
	cur, err := Collection().Aggregate(ctx, [2]bson.M{
		{"$match": match},
		findSimpleOperators,
	})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	if !cur.Next(ctx) {
		return nil, nil
	}
	err = cur.Decode(lm)
	if err != nil {
		return nil, err
	}
	return lm, nil
}

// FindSimples 查询 roomIDs 所对应的 live_meta, 返回 Simple 数组
func FindSimples(roomIDs []int64) ([]*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	pipeline := []bson.M{
		{"$match": bson.M{"room_id": bson.M{"$in": roomIDs}}},
		findSimpleOperators,
	}
	cur, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var simples []*Simple
	err = cur.All(ctx, &simples)
	if err != nil {
		return nil, err
	}
	return simples, nil
}

// SimpleSliceToMap slice 转 map[roomID]*Simple，第二个参数是为了兼容 FindSimples 返回值使用的
func SimpleSliceToMap(s []*Simple, err ...error) (map[int64]*Simple /* map[roomID]*Simple */, error) {
	var returnErr error
	if len(err) != 0 {
		returnErr = err[0]
	}

	m := make(map[int64]*Simple, len(s))
	for i := 0; i < len(s); i++ {
		if lm := s[i]; lm != nil {
			m[lm.RoomID] = lm
		}
	}
	return m, returnErr
}

// SetOpen 设置开播和关播
func SetOpen(roomID int64, open bool) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	set := bson.M{
		"open":         util.BoolToInt(open),
		"accumulation": 0,
		"accids":       []string{},
	}
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": roomID, "open": util.BoolToInt(!open)},
		bson.M{"$set": set})
	return err
}

// SetExtraScore 设置直播间奖励热度
func SetExtraScore(roomIDs []int64, score float64, startTime, endTime time.Time) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	extraScore := ExtraScore{
		Score:     score,
		StartTime: startTime.Unix(),
		EndTime:   endTime.Unix(),
	}
	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$push": bson.M{
			"extra_score": extraScore,
		}})
	return err
}

// ClearExtraScore 清空直播间奖励热度
func ClearExtraScore(roomIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}}, bson.M{
		"$unset": bson.M{"extra_score": ""}})
	return err
}

// SetPlaybackPriority 设置直播间回放优先级
func SetPlaybackPriority(roomID int64, priority int) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	err := Collection().FindOneAndUpdate(ctx,
		bson.M{"room_id": roomID},
		bson.M{"$set": bson.M{
			"playback_priority": priority,
		}}).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// FindPlaybackPriority 获取直播间处理回放优先级
func FindPlaybackPriority(roomID int64) int {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var r struct {
		PlayBackPriority int `bson:"playback_priority"`
	}
	err := Collection().FindOne(ctx,
		bson.M{"room_id": roomID},
		options.FindOne().SetProjection(bson.M{"playback_priority": 1})).Decode(&r)
	if err != nil {
		logger.Info(err)
		// PASS
	}
	return r.PlayBackPriority
}

// SetSpeakSettings 设置发言设置
func SetSpeakSettings(roomID int64, ss *SpeakSettings) error {
	filter := bson.M{"room_id": roomID}
	var update bson.M
	if ss == nil {
		update = bson.M{"$unset": bson.M{"speak_settings": ""}}
	} else {
		update = bson.M{"$set": bson.M{"speak_settings": ss}}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, filter, update)
	return err
}

// FindSpeakSettings 查询发言设置
func FindSpeakSettings(roomID int64) (*SpeakSettings, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var lm LiveMeta
	err := Collection().FindOne(ctx, bson.M{"room_id": roomID},
		options.FindOne().SetProjection(bson.M{"speak_settings": 1})).Decode(&lm)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	if lm.SpeakSettings == nil {
		return nil, nil
	}
	return lm.SpeakSettings, nil
}

// IsSpeakLimited 发言受限
// 直播等级限制和粉丝等级限制只设置了一个就只判断一种
// 两个都设置的时候，二者是或的关系，只要用户符合一个发言条件，就可以在房间内发言
func (ss *SpeakSettings) IsSpeakLimited(liveLevel int, medalInfo *MedalInfo) bool {
	if !ss.Enable {
		return false
	}
	if ss.All {
		return true
	}
	/*
		-1 未被限制
		0 未设置
		1 发言受限
	*/
	var levelLimited int
	if ss.LiveLevel != 0 {
		if liveLevel < ss.LiveLevel {
			levelLimited = 1
		} else {
			levelLimited = -1
		}
	}
	var medalLimited int
	if ss.Medal != nil {
		if medalInfo == nil ||
			medalInfo.Level < ss.Medal.Level ||
			(!medalInfo.SuperFan && ss.Medal.SuperFan) {
			medalLimited = 1
		} else {
			medalLimited = -1
		}
	}
	// 0 + x = x 根据 x 状态判断
	// -1 + 1 = 0 不受限
	// NOTICE: 限制项变多后，需要 for 循环判断
	return levelLimited+medalLimited > 0
}
