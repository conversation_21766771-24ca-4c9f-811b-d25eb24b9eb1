package room

import (
	"strings"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CDN 提供方
const (
	// ChannelProviderNetease = "netease"
	ChannelProviderAliyun = "aliyun"
	ChannelProviderKsyun  = "ksyun"
	ChannelProviderBvc    = "bvc"
)

// 不下发的 pull url
const (
	URLDisableRTMP = iota + 1
	URLDisableFLV
	URLDisableHLS
)

// Channel sub struct of room
type Channel struct {
	Provider     string         `bson:"provider" json:"-"`
	SetProvider  string         `bson:"setprovider" json:"-"`
	URLDisabled  goutil.BitMask `bson:"url_disabled" json:"-"`
	DisableVideo bool           `bson:"disable_video" json:"-"`

	BVC    *bvc.Info   `bson:"bvc,omitempty" json:"-"`
	Custom *CustomInfo `bson:"custom,omitempty" json:"-"`
	// Netease *netease.Info bson:"netease,omitempty" json:"-"

	// WORKAROUND: iOS 5.4.7 版本对不下发的流字段需要字段值不存在才行
	RTMPPullURL string `bson:"-" json:"rtmp_pull_url,omitempty"`
	FLVPullURL  string `bson:"-" json:"flv_pull_url"`
	HLSPullURL  string `bson:"-" json:"hls_pull_url"`

	PushURL        string   `bson:"-" json:"push_url,omitempty"`
	ChainedPushURL []string `bson:"-" json:"chained_push_url,omitempty"`
}

// CustomInfo custom info
type CustomInfo struct {
	RTMPPullURL string `bson:"rtmp_pull_url" json:"-"`
	FLVPullURL  string `bson:"flv_pull_url" json:"-"`
	HLSPullURL  string `bson:"hls_pull_url" json:"-"`
	PushURL     string `bson:"push_url" json:"-"`
}

// SetChannelNotify channel notify
type SetChannelNotify struct {
	StatusChannel

	RoomID  int64   `json:"room_id"`
	Channel Channel `json:"channel"`
	Connect Connect `json:"connect"`
}

// BuildAuthedURL 构建鉴权后的推流拉流地址
// TODO: 后面再加参数的话用结构体封装
func (ch *Channel) BuildAuthedURL(roomID, creatorID, userID int64, ip string, equip *goutil.Equipment, needPushURL bool, noURLDisabled bool) {
	switch ch.Provider {
	/*
		// TODO: RTMPPullURL, HTTPPullURL, HLSPullURL 字段位置改变, 如: ch.RTMPPullURL 修改为 ch.Netease.RTMPPullURL
		case ChannelProviderNetease:
			t := time.Now()
			ch.RTMPPullURL = service.NeteaseLive.BuildAuthedURL(ch.RTMPPullURL, t, false)
			ch.HTTPPullURL = service.NeteaseLive.BuildAuthedURL(ch.HTTPPullURL, t, false)
			ch.HLSPullURL = service.NeteaseLive.BuildAuthedURL(ch.HLSPullURL, t, false)
			if !needPushURL {
				ch.PushURL = ""
			} else {
				ch.PushURL = service.NeteaseLive.BuildAuthedURL(ch.PushURL, t, true)
			}
	*/
	case ChannelProviderAliyun:
		ch.FLVPullURL, ch.HLSPullURL, ch.RTMPPullURL, ch.PushURL =
			service.AliyunLive.BuildAuthedURL(roomID, needPushURL)
	case ChannelProviderKsyun, "":
		ch.FLVPullURL, ch.HLSPullURL, ch.RTMPPullURL, ch.PushURL =
			service.KsyunLive.BuildAuthedURL(roomID, creatorID, needPushURL)
	case ChannelProviderBvc:
		var (
			pt       goutil.Platform
			isOldApp bool
		)
		if equip != nil {
			pt = equip.OS
			isOldApp = equip.IsOldApp(goutil.AppVersions{IOS: "4.7.9", Android: "5.6.7"})
		} else {
			// 默认使用 web platform
			pt = goutil.Web
		}
		info := service.BvcLive.BuildAuthedURL(roomID, creatorID, userID, ip, pt, ch.BVC, needPushURL)
		if info != nil {
			ch.FLVPullURL = info.FLVPullURL
			ch.HLSPullURL = info.HLSPullURL
			if !isOldApp && !ch.DisableVideo {
				// web 和 iOS >= 4.7.9, 安卓 >= 5.6.7 的版本, ch.DisableVideo 不为 true 时需要保留视频帧, 下发其中直播的 SEI 信息, 即移除 ptype 参数
				ch.FLVPullURL = removeURLPtype(ch.FLVPullURL)
				ch.HLSPullURL = removeURLPtype(ch.HLSPullURL)
			}
			ch.RTMPPullURL = info.RTMPPullURL
			ch.PushURL = info.PushURL
			ch.ChainedPushURL = info.ChainedPushURL
		}
	}
	// 获取配置的推拉流地址
	ch.buildCustomURL(needPushURL)

	if noURLDisabled {
		return
	}
	if ch.URLDisabled.IsSet(URLDisableRTMP) {
		ch.RTMPPullURL = ""
	}
	if ch.URLDisabled.IsSet(URLDisableFLV) {
		ch.FLVPullURL = ""
	}
	if ch.URLDisabled.IsSet(URLDisableHLS) {
		ch.HLSPullURL = ""
	}
}

func (ch *Channel) buildCustomURL(needPushURL bool) {
	if ch.Custom == nil {
		return
	}
	if ch.Custom.RTMPPullURL != "" {
		ch.RTMPPullURL = ch.Custom.RTMPPullURL
	}
	if ch.Custom.FLVPullURL != "" {
		ch.FLVPullURL = ch.Custom.FLVPullURL
	}
	if ch.Custom.HLSPullURL != "" {
		ch.HLSPullURL = ch.Custom.HLSPullURL
	}
	if needPushURL && ch.Custom.PushURL != "" {
		ch.PushURL = ch.Custom.PushURL
		ch.ChainedPushURL = nil
	}
}

// BuildAuthedChannelURL 构建鉴权后的推流拉流地址
func (r *Room) BuildAuthedChannelURL(userID int64, ip string, equip *goutil.Equipment, needPushURL bool, noURLDisabled bool) {
	r.Channel.BuildAuthedURL(r.RoomID, r.CreatorID, userID, ip, equip, needPushURL, noURLDisabled)
}

// removeURLPtype bvc 拉流地址移除 ptype 参数，保留视频流
func removeURLPtype(url string) string {
	return strings.ReplaceAll(url, "&ptype=1", "")
}
