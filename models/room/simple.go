package room

import (
	"sort"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 推荐来源
const (
	RecommendFromDefault      = ""
	RecommendFromVip          = "vip"
	RecommendFromRankLastHour = "rank_last_hour" // 上小时榜推荐位
	RecommendFromCustom       = "custom"         // 运营配置的
)

// 推荐标签类型
const (
	RecommendTagTypeRecommend         = iota + 1 // 推荐标签
	RecommendTagTypeLastHourRank                 // 上小时榜
	RecommendTagTypeConfig                       // 运营配置标签
	RecommendTagTypeCustomTag                    // 个性词条
	RecommendTagTypeCV                           // 声优直播标签
	RecommendTagTypeSearchUserRelated            // 搜索页用户关系标签
	RecommendTagTypeSubCatalog                   // 二级分区标签
)

// constants used for sorting
var (
	SortByOpenTime         = bson.M{"status.open_time": -1}
	SortByScore            = bson.M{"status.score": -1}
	SortByOpenScore        = bson.D{{Key: "status.open", Value: -1}, {Key: "status.score", Value: -1}, {Key: "status.open_time", Value: -1}}
	SortByOpenAccumulation = bson.D{{Key: "status.open", Value: -1}, {Key: "statistics.accumulation", Value: -1}, {Key: "status.open_time", Value: -1}}
)

// SimpleProjection should be referenced directly and is readonly. Do not assign it to a new variable.
func SimpleProjection() map[string]int {
	return mongodb.NewProjection("room_id, catalog_id, custom_tag_id, name, announcement, creator_id, " +
		"creator_username, statistics, status, cover, activity_catalog_id")
}

// SimpleRoomStatus 房间状态简要信息
type SimpleRoomStatus struct {
	Open      int   `json:"open"`                // 开播状态
	Attention *bool `json:"attention,omitempty"` // 是否关注了主播，主播连线搜索页下发
}

// Simple 房间简要信息
type Simple struct {
	RoomID          int64  `bson:"room_id" json:"room_id"`
	CatalogID       int64  `bson:"catalog_id" json:"catalog_id"`
	CustomTagID     int64  `bson:"custom_tag_id" json:"-"`
	Name            string `bson:"name" json:"name"`
	Announcement    string `bson:"announcement" json:"announcement"`
	CreatorID       int64  `bson:"creator_id" json:"creator_id"`
	CreatorUsername string `bson:"creator_username" json:"creator_username"`
	// TODO: 这里的结构体内部还不够简单，待简化
	Statistics *Statistics `bson:"statistics" json:"statistics,omitempty"`
	Status     *Status     `bson:"status" json:"status,omitempty"`

	Cover *string `bson:"cover" json:"-"`

	CoverURL            string `bson:"-" json:"cover_url,omitempty"`
	CreatorIconURL      string `bson:"-" json:"creator_iconurl,omitempty"`
	CreatorIntroduction string `bson:"-" json:"creator_introduction,omitempty"`

	// 开播列表使用
	CatalogName  string `bson:"-" json:"catalog_name,omitempty"`
	CatalogColor string `bson:"-" json:"catalog_color,omitempty"`

	// 直播推荐使用
	CustomTag *tag.CustomTag `bson:"-" json:"custom_tag,omitempty"`

	// 滑动切换直播间使用
	Background *Background `bson:"background" json:"background,omitempty"`

	// 推荐图标，因为循环引用在包外查询
	IconURL string `bson:"-" json:"icon_url,omitempty"`

	// 列表推荐相关
	From        string `bson:"-" json:"from,omitempty"`
	Index       *int   `bson:"-" json:"-"`
	RecommendID int64  `bson:"-" json:"recommend_id,omitempty"`
	// Rank        *int   `bson:"-" json:"-"`          // 榜单推荐中需要标记在榜单中的排名
	RecommendTag *RecommendTag `bson:"-" json:"recommend_tag,omitempty"`

	ActivityCatalogID int64 `bson:"activity_catalog_id" json:"activity_catalog_id"`
}

// RecommendTag 推荐标签
type RecommendTag struct {
	Type    int    `json:"type"`
	Text    string `json:"text,omitempty"`
	IconURL string `json:"icon_url,omitempty"`
}

// PreviewIntro 预览页额外信息
type PreviewIntro struct {
	IconURL string `json:"icon_url"`
	Title   string `json:"title"`
}

// SchemeToURL 数据库获取的图片自定义协议地址转成 HTTP 协议地址
func (s *Simple) SchemeToURL() {
	if s.Cover != nil {
		s.CoverURL = storage.ParseSchemeURL(*s.Cover)
	}
	if s.CoverURL == "" {
		s.CoverURL = service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
	}

	if s.Background != nil {
		s.Background.SchemeToURL()
	}
}

// Online 在线人数
func (s Simple) Online() int64 {
	if s.Statistics != nil {
		return s.Statistics.Online
	}
	return 0
}

// AfterFind 数据库获取的图片自定义协议地址转成 HTTP 协议地址, 热度取整, 未开播热度为 0
func (s *Simple) AfterFind() {
	s.SchemeToURL()
	if s.Statistics != nil {
		s.Statistics.LoadScore(s.Status)
	}
	// red_packet 存的是直播间红包数量，如存在红包该字段返回 1 即可
	if s.Status != nil && s.Status.RedPacket > 1 {
		s.Status.RedPacket = 1
	}
}

// SetOnline 设置在线人数，如果 s.Statistics 原来为 nil，则为其 new 一个新的
func (s *Simple) SetOnline(online int64) {
	if s.Statistics == nil {
		s.Statistics = new(Statistics)
	}
	s.Statistics.Online = online
}

// SetAttention 设置是否关注和粉丝数，如果 s.Statistics 原来为 nil，则为其 new 一个新的
func (s *Simple) SetAttention(fansNum int64, isFollowed bool) {
	if s.Statistics == nil {
		s.Statistics = new(Statistics)
	}
	if s.Status == nil {
		s.Status = new(Status)
	}
	s.Statistics.AttentionCount = fansNum
	s.Statistics.Attention = isFollowed
	s.Status.Attention = isFollowed
}

// FindOneSimple 只查询一个房间的基本信息
func FindOneSimple(filter interface{}, opt ...*FindOptions) (*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	var simple Simple
	mongoOpt := options.FindOne().SetProjection(SimpleProjection())
	err := collection.FindOne(ctx, filter, mongoOpt).Decode(&simple)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	simple.AfterFind()
	_ = findSimpleListHelper([]*Simple{&simple}, getOptions(opt))
	return &simple, nil
}

// FindSimpleMapByCreatorID 通过主播用户 ID 查询房间简要信息，返回对应用户 ID 的 map
// 只有第一个 opt 会被采用，opt[0] == nil 则认为是默认配置
func FindSimpleMapByCreatorID(userIDs []int64, opt ...*FindOptions) (map[int64]*Simple /* map[userID]*Simple */, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)

	filter := bson.M{"creator_id": bson.M{"$in": userIDs}}
	mongoOpt := options.Find().SetProjection(SimpleProjection())
	cur, err := collection.Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var tmpSimples []*Simple
	err = cur.All(ctx, &tmpSimples)
	if err != nil {
		return nil, err
	}
	for i := range tmpSimples {
		tmpSimples[i].AfterFind()
	}
	res := findSimpleListHelper(tmpSimples, getOptions(opt))
	return res, nil
}

// findSimpleMapHelper FindSimpleMap 辅助函数入口
func findSimpleMapHelper(simpleMap map[int64]*Simple /* map[userID]*Simple */, opt *FindOptions) {
	if opt.DisableAll {
		return
	}
	simpleMapFindNumOfPeople(simpleMap, opt.FindOnline, opt.ClientIP)
	if opt.FindCreator {
		simpleMapFindCreator(simpleMap)
	}
	if opt.FindFans {
		simpleMapFindAttention(simpleMap, opt.ListenerID)
	}
	if opt.FindCatalogInfo {
		simpleMapFindCatalog(simpleMap)
	}
	if opt.FindCustomTag {
		simpleMapFindCustomTag(simpleMap)
	}
	if opt.FindLuckyBag {
		simpleMapFindRoomLuckyBag(simpleMap)
	}
}

func simpleMapFindRoomLuckyBag(simpleMap map[int64]*Simple) {
	if len(simpleMap) == 0 {
		return
	}
	roomIDs := make([]int64, 0, len(simpleMap))
	for _, simple := range simpleMap {
		roomIDs = append(roomIDs, simple.RoomID)
	}
	initiateRecords, err := luckybag.ListPendingInitiateRecordByRoomIDs(roomIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	for _, initiateRecord := range initiateRecords {
		if simple, ok := simpleMap[initiateRecord.CreatorID]; ok {
			simple.Status.LuckyBag = LuckyBagStatusPending
		}
	}
}

// simpleMapFindNumOfPeople 查询统计人数并缩放
// NOTICE: 如果要暴露到包外的话，注意检查 nil 指针
func simpleMapFindNumOfPeople(simpleMap map[int64]*Simple, /* map[userID]*Simple */
	findOnline bool, ip string,
) {
	roomIDs := make([]int64, 0, len(simpleMap))
	for _, r := range simpleMap {
		if r.Status != nil && r.Status.Open != StatusOpenFalse &&
			r.Statistics != nil {
			roomIDs = append(roomIDs, r.RoomID)
		}
	}

	if findOnline {
		onlines, err := callOnlines(roomIDs, ip)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		// 遍历 simpleMap 保证 Statistics 不为 nil
		for _, r := range simpleMap {
			r.SetOnline(max(r.Online(), onlines[r.RoomID]))
		}
	}

	metas, err := livemeta.SimpleSliceToMap(livemeta.FindSimples(roomIDs))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 遍历 simpleMap 保证 Statistics 不为 nil
	for _, r := range simpleMap {
		if r.Statistics == nil {
			r.Statistics = new(Statistics)
		}
		r.Statistics.ScaleNumberOfPeople(metas[r.RoomID])
	}
}

// simpleMapFindCreator 查询主播信息
// NOTICE: 如果要暴露到包外的话，注意检查 nil 指针
func simpleMapFindCreator(simpleMap map[int64]*Simple /* map[userID]*Simple */) {
	defaultURL := service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
	userIDs := make([]int64, 0, len(simpleMap))
	for _, r := range simpleMap {
		userIDs = append(userIDs, r.CreatorID)
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		logger.Error(err)
		for _, r := range simpleMap {
			r.CreatorIconURL = defaultURL
		}
		return
	}
	for _, r := range simpleMap {
		if u := userMap[r.CreatorID]; u != nil {
			r.CreatorIconURL = u.IconURL
			r.CreatorIntroduction = u.UserIntro
		} else {
			r.CreatorIconURL = defaultURL
		}
	}
}

// simpleMapFindCreator 查询是否关注
// NOTICE: 如果要暴露到包外的话，注意检查 nil 指针
func simpleMapFindAttention(simpleMap map[int64]*Simple /* map[userID]*Simple */, listenerID int64) {
	checkUserIDs := make([]int64, 0, len(simpleMap))
	for _, r := range simpleMap {
		checkUserIDs = append(checkUserIDs, r.CreatorID)
	}
	attentions, err := attentionuser.CheckAttention(listenerID, checkUserIDs)
	if err != nil {
		logger.Error(err)
		return
	}
	atteMap := attentionuser.AttentionSliceToMap(attentions)
	// 遍历 simpleMap 保证 Statistics 不为 nil
	for _, r := range simpleMap {
		if atte := atteMap[r.CreatorID]; atte != nil {
			r.SetAttention(atte.FansNum, atte.Followed)
		}
	}
}

// simpleMapFindCatalog 查找分区名称，如果房间所在分区隐藏，则不返回分区信息
func simpleMapFindCatalog(simpleMap map[int64]*Simple) {
	subMap, err := catalog.AllLiveCatalogsWithSubMap(false)
	if err != nil {
		logger.Error(err)
		return
	}
	for _, simple := range simpleMap {
		simple.SetCatalogInfo(subMap[simple.CatalogID])
	}
}

func simpleMapFindCustomTag(simpleMap map[int64]*Simple) {
	tagMap, err := tag.AllShowCustomTagsMap()
	if err != nil {
		logger.Error(err)
		return
	}
	if len(tagMap) == 0 {
		return
	}
	for _, simple := range simpleMap {
		t, ok := tagMap[simple.CustomTagID]
		if !ok {
			continue
		}
		simple.CustomTag = &tag.CustomTag{
			TagID:   t.ID,
			TagName: t.TagName,
		}
	}
}

// callOnlines 查询多个房间在线人数，返回 map[roomID]在线人数
func callOnlines(roomIDs []int64, ip string) (map[int64]int64 /* map[roomID]在线人数 */, error) {
	if len(roomIDs) == 0 {
		return map[int64]int64{}, nil
	}
	// WORKAROUND: 不使用 controllers 下的 package 防止循环调用
	var onlineResp []struct {
		RoomID int64 `json:"room_id"`
		Count  int64 `json:"count"`
	}
	err := service.MRPC.Call("im://online/count", ip,
		handler.M{"room_ids": roomIDs}, &onlineResp)
	if err != nil {
		return make(map[int64]int64), err
	}
	res := make(map[int64]int64, len(onlineResp))
	for i := 0; i < len(onlineResp); i++ {
		res[onlineResp[i].RoomID] = onlineResp[i].Count
	}
	return res, nil
}

// SortSimpleByOnline 根据在线人数由高到低排序 Simple
// 支持传 map[int64]*Simple 和 slice，返回排序后的数组
// 对 map 的键无特殊要求，可以是任意的含义
func SortSimpleByOnline(m map[int64]*Simple, s []*Simple) []*Simple {
	res := make([]*Simple, 0, len(s)+len(m))
	for _, r := range m {
		if r != nil {
			res = append(res, r)
		}
	}
	for i := 0; i < len(s); i++ {
		if s[i] != nil {
			res = append(res, s[i])
		}
	}
	// 由高到低排序
	sort.SliceStable(res, func(i, j int) bool {
		return res[i].Online() > res[j].Online()
	})
	return res
}

// AggregateSimples 根据参数聚合列表
func AggregateSimples(pipeline interface{}, opt ...*FindOptions) ([]*Simple, error) {
	collection := service.MongoDB.Collection(CollectionName)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*Simple
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	for i := range res {
		res[i].AfterFind()
	}
	findSimpleListHelper(res, getOptions(opt))
	return res, nil
}

// ListSimples 根据参数查询列表
func ListSimples(filter interface{}, mongoOpt *options.FindOptions, opt ...*FindOptions) ([]*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)

	if mongoOpt == nil {
		mongoOpt = options.Find()
	}
	if mongoOpt.Projection == nil {
		// TODO: 支持 opt 中的 Projection
		mongoOpt.SetProjection(SimpleProjection())
	}

	cur, err := collection.Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*Simple
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	findSimpleListHelper(res, getOptions(opt))
	for i := 0; i < len(res); i++ {
		res[i].AfterFind()
	}
	return res, nil
}

// FindSimple 根据参数查询单个房间
func FindSimple(filter any, opt *FindOptions) (*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	mongoOpt := options.FindOne()
	if opt != nil && opt.Projection != nil {
		mongoOpt.SetProjection(opt.Projection)
	} else {
		mongoOpt.SetProjection(SimpleProjection())
	}
	var s Simple
	collection := service.MongoDB.Collection(CollectionName)
	err := collection.FindOne(ctx, filter, mongoOpt).Decode(&s)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	s.AfterFind()
	_ = findSimpleListHelper([]*Simple{&s}, getOptions([]*FindOptions{opt}))
	return &s, nil
}

func findSimpleListHelper(list []*Simple, opt *FindOptions) /* map[creator_id]*Simple */ map[int64]*Simple {
	simpleMap := goutil.ToMap(list, "CreatorID").(map[int64]*Simple)
	findSimpleMapHelper(simpleMap, opt)
	return simpleMap
}

// IsOpen 是否正在直播
func (s Simple) IsOpen() bool {
	return s.Status != nil && s.Status.Open != StatusOpenFalse
}

// SetIndex 设置推荐位
// NOTICE: 仅在必要时分配内存，不会直接引用传入的值的指针
func (s *Simple) SetIndex(index int) {
	if s.Index == nil {
		s.Index = new(int)
	}
	*s.Index = index
}

// SetCatalogInfo 设置分区信息
func (s *Simple) SetCatalogInfo(c *catalog.LiveCatalog) {
	if c == nil {
		return
	}
	s.CatalogName = c.CatalogName
	s.CatalogColor = c.Color
}

// HasNovaTag 是否满足新星直播间条件
func (s *Simple) HasNovaTag() bool {
	if s.Statistics == nil {
		return false
	}
	return hasNovaTag(s.RoomID, s.Statistics)
}
