package room

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service/cdn/agora"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestConnectTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Connect{}, "id", "provider", "push_type", "forbidden", "string_agora_uid")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Connect{}, "id", "provider", "push_type", "forbidden", "queue", "join", "finish",
		"name", "key", "agora_uid")
}

func TestBuildAgora(t *testing.T) {
	assert := assert.New(t)
	c := &Connect{ID: "1234", StringAgoraUID: 1}
	c.BuildAgora(120, agora.LevelListener)
	assert.Equal("1234", c.Name)
	assert.NotEmpty(c.Key)
	assert.Equal("120", c.AgoraUID)
}

func TestListConnects(t *testing.T) {
	assert := assert.New(t)
	c := &Connect{}
	r, err := Find(existsRoomID)
	assert.NoError(err)
	assert.NoError(c.ListConnects(r.OID, goutil.TimeNow()))
	assert.NotNil(c.Queue)
	assert.NotNil(c.Join)
	assert.NotNil(c.Finish)
}

func TestBuildBvclive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bvc.SetMockResult(bvc.ActionUpset, bvc.Channel{
		Channel: &bvc.ChannelInfo{
			PushURL: "rtmp://live-push.bilivideo.com/live-bvc/maoer_ex_223344_1?key=854c7246288611eba218f281bfa61990",
		},
	})
	connect := new(Connect)

	err := connect.BuildBvclive(1, 223344, "rtmp://txy.maoer-push.bilivideo.com/live-bvc/maoer_9075077_373206839?key=233b1e589d3411ebba1e5a01179c8800", "127.0.0.1")
	require.NoError(err)
	assert.Equal("233b1e589d3411ebba1e5a01179c8800", connect.Key)
	assert.Equal("maoer_9075077_373206839", connect.Name)

	err = connect.BuildBvclive(1, 223344, "", "127.0.0.1")
	require.NoError(err)
	assert.Equal("854c7246288611eba218f281bfa61990", connect.Key)
	assert.Equal("maoer_ex_223344_1", connect.Name)
}

func TestConnect_BuildBililive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bililive.SetMockResult(bililive.ActionGetAccessToken, bililive.AccessTokenInfo{AccessToken: "eeexxx"})
	connect := new(Connect)
	err := connect.BuildBililive(123, 235, "127.0.0.1")
	require.NoError(err)
	assert.NotEmpty(connect.Key)
}

func TestSetAndUnsetRoomMultiConnectStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := SetRoomMultiConnectStatus([]int64{testRoomID}, "1")
	require.NoError(err)
	r, err := Find(testRoomID)
	require.NoError(err)
	assert.Equal(MultiConnectStatusOngoing, r.Status.MultiConnect)
	assert.Equal("1", r.Connect.ID)
	assert.Equal(ProviderBililive, r.Connect.Provider)

	err = UnsetRoomMultiConnectStatus([]int64{testRoomID})
	require.NoError(err)
	r, err = Find(testRoomID)
	require.NoError(err)
	assert.Zero(r.Status.MultiConnect)
	assert.NotEqual("1", r.Connect.ID)
}
