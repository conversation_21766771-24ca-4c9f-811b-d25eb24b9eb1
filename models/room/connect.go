package room

import (
	"encoding/json"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/agora"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
)

// Connect sub struct of room
type Connect struct {
	ID        string `bson:"id" json:"id,omitempty"`
	Provider  string `bson:"provider" json:"provider"`
	PushType  string `bson:"push_type" json:"push_type"` // 非空则表示支持使用 bililive
	Forbidden bool   `bson:"forbidden" json:"forbidden"` // 是否禁止用户申请连麦

	StringAgoraUID int `bson:"string_agora_uid" json:"-"` // 为 0 不下发字符串声网 ID, 其他下发

	Queue    []*liveconnect.LiveConnect `bson:"-" json:"queue,omitempty"`
	Join     []*liveconnect.LiveConnect `bson:"-" json:"join,omitempty"`
	Finish   []*liveconnect.LiveConnect `bson:"-" json:"finish,omitempty"`
	Name     string                     `bson:"-" json:"name,omitempty"`
	Key      string                     `bson:"-" json:"key,omitempty"`
	AgoraUID string                     `bson:"-" json:"agora_uid,omitempty"`
}

// MarshalJSON implements json.MarshalJSON.
func (c Connect) MarshalJSON() ([]byte, error) {
	type Alias Connect
	if c.Queue == nil && c.Join == nil && c.Finish == nil {
		return json.Marshal(&struct{ *Alias }{Alias: (*Alias)(&c)})
	}
	return json.Marshal(&struct {
		*Alias
		Queue  []*liveconnect.LiveConnect `json:"queue"`
		Join   []*liveconnect.LiveConnect `json:"join"`
		Finish []*liveconnect.LiveConnect `json:"finish"`
	}{
		Alias:  (*Alias)(&c),
		Queue:  c.Queue,
		Join:   c.Join,
		Finish: c.Finish,
	})
}

// BuildAgora 构造 agora 相关的 name, key, level 是声网的等级
func (c *Connect) BuildAgora(agoraUID int64, level int) {
	var agoraUIDStr string
	if agoraUID != 0 {
		// 游客为空字符串
		agoraUIDStr = strconv.FormatInt(agoraUID, 10)
	}
	if c.StringAgoraUID != 0 {
		c.AgoraUID = agoraUIDStr
	}
	acctoken := agora.NewAccessToken(
		config.Conf.Service.Agora.AppID,
		config.Conf.Service.Agora.AppCertificate,
		c.ID,
		agoraUIDStr,
	)
	acctoken.BuildPrivilege(level)
	c.Name = c.ID
	c.Key = acctoken.Build()
}

// BuildBvclive 构造 bvclive 相关的字段
// params: 房间 ID, 用户 ID, 房主的 pushURL, ip
func (c *Connect) BuildBvclive(roomID, userID int64, pushURL, ip string) error {
	// 房主直接使用自己的 bvc 推流地址来解析 streamName 和 key
	if pushURL != "" {
		var err error
		c.Name, c.Key, err = bvc.ParseStreamInfo(pushURL)
		return err
	}

	bvcRequest := &bvc.UpsetRequest{
		StreamName: service.BvcLive.StreamNameEX(roomID, userID), // 连麦用户为临时推流 (maoer_ex_{user_id}_{room_id})
		Edge:       "1",
		IP:         ip,
	}
	channel, err := service.BvcLive.Upset(bvcRequest)
	if err != nil {
		return err
	}
	c.Name, c.Key, err = bvc.ParseStreamInfo(channel.Channel.PushURL)
	if err != nil {
		return err
	}
	return nil
}

// ListConnects 列出连麦用户队列
func (c *Connect) ListConnects(roomOID primitive.ObjectID, openTime time.Time) (err error) {
	c.Queue, c.Join, c.Finish, err = liveconnect.ListLiveConnects(roomOID, openTime)
	return err
}

// BuildBililive 构造 bililive 相关的字段
// params: 用户 ID, channel ID, ip
func (c *Connect) BuildBililive(userID, channelID int64, ip string) error {
	tokenInfo, err := service.BiliLive.GetAccessToken(&bililive.AccessTokenRequestParams{
		ChannelID:   channelID,
		UserID:      userID,
		IP:          ip,
		VideoEnable: false,
		AudioEnable: true,
	})
	if err != nil {
		return err
	}
	c.Key = tokenInfo.AccessToken
	return nil
}

// SetRoomMultiConnectStatus 设置直播间的多人连线状态
func SetRoomMultiConnectStatus(roomIDs []int64, connectID string) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$set": bson.M{
			"status.multi_connect": MultiConnectStatusOngoing,
			"connect.id":           connectID,
			"connect.provider":     ProviderBililive, // 主播连线仅支持 bililive
		}},
	)
	if err != nil {
		return err
	}
	return nil
}

// UnsetRoomMultiConnectStatus 移除直播间的多人连线状态
func UnsetRoomMultiConnectStatus(roomIDs []int64) error {
	updates := make([]mongo.WriteModel, len(roomIDs))
	for i, roomID := range roomIDs {
		m := mongo.NewUpdateOneModel()
		m.SetFilter(bson.M{"room_id": roomID})
		m.SetUpdate(bson.M{
			"$set": bson.M{ // 连线结束后，还原为新的连线 ID 和 provider，忽略声网 agora
				"connect.id":       NewConnectID(),
				"connect.provider": ProviderBvclive,
			},
			"$unset": bson.M{"status.multi_connect": 0},
		})
		updates[i] = m
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().BulkWrite(ctx, updates)
	if err != nil {
		return err
	}
	return nil
}
