package room

import (
	"github.com/MiaoSiLa/missevan-go/logger"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
)

// FilterLiveHotSuppression 过滤直播热榜限制的直播间
func FilterLiveHotSuppression(roomIDs []int64) []int64 {
	hotSuppressionRoomIDs, err := usersrank.ListHotSuppressionRoomIDs()
	if err != nil {
		logger.Errorf("FilterLiveHotSuppression ListHotSuppressionRoomIDs error: %v", err)
		// 获取热榜限制的直播间 ID 失败，直接返回传入的直播间 ID
		return roomIDs
	}

	// 将热榜限制的直播间 ID 转为 map
	hotSuppressionRoomIDsMap := make(map[int64]bool, len(hotSuppressionRoomIDs))
	for _, hotSuppressionRoomID := range hotSuppressionRoomIDs {
		hotSuppressionRoomIDsMap[hotSuppressionRoomID] = true
	}

	// 过滤热榜限制的直播间 ID
	resultRoomIDs := make([]int64, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		if !hotSuppressionRoomIDsMap[roomID] {
			resultRoomIDs = append(resultRoomIDs, roomID)
		}
	}
	return resultRoomIDs
}
