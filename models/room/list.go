package room

import (
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// List 根据参数查询多个房间
// 支持 mongo、room FindOptions
func List(filter interface{}, mongoOpt *options.FindOptions, opt ...*FindOptions) ([]*Room, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	if mongoOpt == nil {
		mongoOpt = options.Find()
	}
	cur, err := Collection().Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var rooms []*Room
	err = cur.All(ctx, &rooms)
	if err != nil {
		return nil, err
	}

	roomMap := goutil.ToMap(rooms, "CreatorID").(map[int64]*Room)
	findRoomMapHelper(roomMap, getOptions(opt))
	for _, r := range rooms {
		r.AfterFind()
	}
	return rooms, nil
}

// findRoomMapHelper 辅助函数入口
func findRoomMapHelper(roomMap map[int64]*Room /* map[userID]*Room */, opt *FindOptions) {
	if opt.DisableAll {
		return
	}
	roomMapFindNumOfPeople(roomMap, opt)
	if opt.FindFans {
		roomMapFindAttention(roomMap, opt.ListenerID)
	}
	if opt.FindCreator {
		roomMapFindCreator(roomMap)
	}
}

func roomMapFindNumOfPeople(roomMap map[int64]*Room /* map[userID]*Room */, opt *FindOptions) {
	roomIDs := make([]int64, 0, len(roomMap))
	for _, r := range roomMap {
		if r.Status.Open == StatusOpenTrue {
			roomIDs = append(roomIDs, r.RoomID)
		}
	}

	onlines := make(map[int64]int64)
	if opt.FindOnline && len(roomIDs) > 0 {
		var err error
		onlines, err = callOnlines(roomIDs, opt.ClientIP)
		if err != nil {
			logger.Error(err)
			// PASS
			onlines = make(map[int64]int64)
		}
	}

	metas, err := livemeta.SimpleSliceToMap(livemeta.FindSimples(roomIDs))
	if err != nil {
		logger.Error(err)
		// PASS
		metas = make(map[int64]*livemeta.Simple)
	}
	for _, r := range roomMap {
		switch r.Status.Open {
		case StatusOpenTrue:
			r.Statistics.Online = max(r.Statistics.Online, onlines[r.RoomID])
			r.Statistics.ScaleNumberOfPeople(metas[r.RoomID])
		case StatusOpenFalse:
			r.Statistics.Online = 0
			r.Statistics.Accumulation = 0
		}
	}
}

func roomMapFindAttention(roomMap map[int64]*Room /* map[userID]*Room */, listenerID int64) {
	checkUserIDs := make([]int64, 0, len(roomMap))
	for _, r := range roomMap {
		checkUserIDs = append(checkUserIDs, r.CreatorID)
	}
	attentions, err := attentionuser.CheckAttention(listenerID, checkUserIDs)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	atteMap := attentionuser.AttentionSliceToMap(attentions)
	for _, r := range roomMap {
		if atte := atteMap[r.CreatorID]; atte != nil {
			r.Statistics.AttentionCount = atte.FansNum
			r.Statistics.Attention = atte.Followed
			r.Status.Attention = atte.Followed
		}
	}
}

func roomMapFindCreator(roomMap map[int64]*Room /* map[userID]*Room */) {
	defaultURL := service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
	userIDs := make([]int64, 0, len(roomMap))
	for _, r := range roomMap {
		userIDs = append(userIDs, r.CreatorID)
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		logger.Error(err)
		for _, r := range roomMap {
			r.CreatorIconURL = defaultURL
		}
		return
	}
	for _, r := range roomMap {
		if u := userMap[r.CreatorID]; u != nil {
			r.CreatorIconURL = u.IconURL
		} else {
			r.CreatorIconURL = defaultURL
		}
	}
}
