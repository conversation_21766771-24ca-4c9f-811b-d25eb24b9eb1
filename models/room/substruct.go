package room

import (
	"encoding/json"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
)

// Medal 勋章
type Medal struct {
	Name      string `bson:"name" json:"name"`
	NameClean string `bson:"name_clean" json:"-"` // 纯小写冗余字段，解决勋章名称大小写敏感问题

	DefaultFrameURL string `bson:"-" json:"default_frame_url,omitempty"` // 等级为 1 的定制勋章资源
}

// Background sub struct of room
type Background struct {
	Enable          bool    `bson:"enable" json:"enable"`
	Opacity         float64 `bson:"opacity" json:"opacity"`
	Image           string  `bson:"image" json:"-"`
	ImageURL        string  `bson:"-" json:"image_url,omitempty"`
	PendantImage    string  `bson:"pendant_image" json:"-"`
	PendantImageURL string  `bson:"-" json:"pendant_image_url,omitempty"`
}

// Status sub struct of room
type Status struct {
	Open              int                 `bson:"open" json:"open"`
	OpenQuestionCount int64               `bson:"open_question_count" json:"-"`
	OpenFansCount     int64               `bson:"open_fans_count" json:"-"`  // -1 表示开播时没有正确获取成功
	OpenMedalCount    int64               `bson:"open_medal_count" json:"-"` // -1 表示开播时没有正确获取成功
	OpenRevenue       int64               `bson:"open_revenue" json:"open_revenue"`
	OpenTime          int64               `bson:"open_time" json:"open_time"` // 毫秒时间戳
	OpenLogID         *primitive.ObjectID `bson:"open_log_id,omitempty" json:"open_log_id,omitempty"`
	CloseTime         *int64              `bson:"close_time" json:"close_time"`
	Channel           StatusChannel       `bson:"channel" json:"channel"`
	Score             float64             `bson:"score" json:"-"` // 热度

	// 状态相关字段
	PK           int `bson:"pk" json:"pk,omitempty"`
	MultiConnect int `bson:"multi_connect" json:"multi_connect,omitempty"`
	RedPacket    int `bson:"red_packet" json:"red_packet,omitempty"`
	LuckyBag     int `bson:"-" json:"lucky_bag,omitempty"`

	Broadcasting bool `bson:"-" json:"broadcasting"`
	Attention    bool `bson:"-" json:"attention"`
}

// StatusChannel sub struct of Status
type StatusChannel struct {
	Type     string `bson:"type,omitempty" json:"type"`
	Event    string `bson:"event,omitempty" json:"event,omitempty"`
	Platform string `bson:"platform,omitempty" json:"platform,omitempty"`
	Time     int64  `bson:"time,omitempty" json:"time"` // 毫秒时间戳
}

// Question sub struct of room
type Question struct {
	MinPrice uint32 `bson:"min_price" json:"min_price"`
	Limit    int64  `bson:"limit" json:"limit"`
	MaxLimit int64  `bson:"-" json:"max_limit,omitempty"`

	Join []*livequestion.LiveQuestion `bson:"-" json:"join,omitempty"`
}

// MarshalJSON implements json.MarshalJSON.
func (q Question) MarshalJSON() ([]byte, error) {
	type Alias Question

	if q.Join == nil {
		// 直播间关播的情况下
		return json.Marshal(&struct{ *Alias }{Alias: (*Alias)(&q)})
	}

	return json.Marshal(&struct {
		*Alias
		Join []*livequestion.LiveQuestion `json:"join"`
	}{
		Alias: (*Alias)(&q),
		Join:  q.Join,
	})
}

// CurrentMinPrice 当前提问最低价格
func (q Question) CurrentMinPrice() uint32 {
	if q.MinPrice < livequestion.MinPrice {
		return livequestion.MinPrice
	}

	return q.MinPrice
}

// Members sub struct of room
// NOTICE: 不存入 mongodb
type Members struct {
	Admin []*livemembers.Member `bson:"-" json:"admin"`
	Mute  []*livemembers.Member `bson:"-" json:"mute"`
}

// SchemeToURL 数据库获取的图片自定义协议地址转成 HTTP 协议地址
func (bg *Background) SchemeToURL() {
	if bg.Image != "" {
		bg.ImageURL = storage.ParseSchemeURL(bg.Image)
	}
	if bg.PendantImage != "" {
		bg.PendantImageURL = storage.ParseSchemeURLs(bg.PendantImage)
	}
}

// NewMedal new medal
func NewMedal(name string) *Medal {
	return &Medal{Name: name, NameClean: util.CleanString(name)}
}
