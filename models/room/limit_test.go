package room

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestLimitTags(t *testing.T) {
	tags := []string{"type", "allowed_user_ids"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Limit{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Limit{}, tags...)
}

func TestLimit_Status(t *testing.T) {
	assert := assert.New(t)

	var l Limit
	assert.Equal(LimitStatusBlocked, l.Status(12))
	l.Type = LimitTypeNormalGift
	l.AllowedUserIDs = []int64{12}
	assert.Equal(LimitStatusNormalGiftAllowed, l.Status(12))

	l.Type = LimitTypePrivacy
	assert.Equal(LimitStatusPrivacyAllowed, l.Status(12))
	assert.Equal(LimitStatusBlocked, l.Status(13))
}
