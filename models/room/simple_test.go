package room

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
)

func TestSimpleOnline(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	s := new(Simple)
	assert.Zero(s.Online())
	s.Statistics = &Statistics{Online: 12}
	assert.Equal(int64(12), s.Online())
	s.Statistics = nil
	s.SetOnline(14)
	require.NotNil(s.Statistics)
	assert.Equal(int64(14), s.Statistics.Online)
}

func TestSetAttention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	s := new(Simple)
	s.SetAttention(14, true)
	require.NotNil(s.Statistics)
	assert.Equal(int64(14), s.Statistics.AttentionCount)
	assert.True(s.Statistics.Attention)
	assert.True(s.Status.Attention)
}

func TestFindOneSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m, err := FindOneSimple(bson.M{"creator_id": 3387502})
	require.NoError(err)
	require.NotNil(m)
	assert.Equal(int64(63888614), m.RoomID)
	assert.Equal("ssss", m.Name)
}

func TestFindSimpleMapByCreatorID(t *testing.T) {
	assert := assert.New(t)

	userIDs := []int64{10, 3387502}

	m, err := FindSimpleMapByCreatorID(userIDs, &FindOptions{})
	assert.NoError(err)
	assert.NotNil(m[userIDs[0]])
	assert.NotNil(m[userIDs[1]])
}

func TestFindSimpleListHelper(t *testing.T) {
	t.Run("统计人数", func(t *testing.T) {
		assert := assert.New(t)
		r := new(Simple)
		r.Status = &Status{Open: StatusOpenTrue}
		r.RoomID = 10
		r.Statistics = &Statistics{
			Online:       -1,
			Accumulation: -2,
		}
		opt := &FindOptions{FindOnline: true, ClientIP: "127.0.0.1"}
		findSimpleListHelper([]*Simple{r}, opt)
		assert.Equal(int64(0), r.Statistics.Online)
		assert.Equal(int64(0), r.Statistics.Accumulation)
	})
	t.Run("查询主播信息", func(t *testing.T) {
		assert := assert.New(t)
		r := new(Simple)
		r.CreatorID = 12
		opt := &FindOptions{FindCreator: true}
		findSimpleListHelper([]*Simple{r}, opt)
		assert.NotEmpty(r.CreatorIconURL)
	})
	t.Run("查询关注信息", func(t *testing.T) {
		assert := assert.New(t)
		assert.NoError(attentionuser.Follow(12, 10))
		r := new(Simple)
		r.CreatorID = 10
		opt := &FindOptions{FindFans: true, ListenerID: 12}
		findSimpleListHelper([]*Simple{r}, opt)
		assert.True(r.Statistics.Attention)
		assert.True(r.Status.Attention)
	})
	t.Run("查询分区信息", func(t *testing.T) {
		assert := assert.New(t)

		list := []*Simple{{CreatorID: 1, CatalogID: 104}, {CreatorID: 2, CatalogID: 118}}
		opt := &FindOptions{FindCatalogInfo: true}
		findSimpleListHelper(list, opt)
		for _, simple := range list {
			assert.NotEmpty(simple.CatalogName)
			assert.NotEmpty(simple.CatalogColor)
		}
		// 测试隐藏分区
		list = []*Simple{{CreatorID: 1, CatalogID: 111}, {CreatorID: 2, CatalogID: 119}}
		findSimpleListHelper(list, opt)
		for _, simple := range list {
			assert.Empty(simple.CatalogName)
			assert.Empty(simple.CatalogColor)
		}
	})
	t.Run("查询个性词条", func(t *testing.T) {
		assert := assert.New(t)

		// 测试正常返回词条
		list := []*Simple{{CreatorID: 1, CustomTagID: 10001}, {CreatorID: 2, CustomTagID: 20001}}
		opt := &FindOptions{FindCustomTag: true}
		findSimpleListHelper(list, opt)
		expected := []string{"test10001", "test20001"}
		for i, v := range list {
			assert.EqualValues(list[i].CustomTagID, v.CustomTag.TagID)
			assert.EqualValues(expected[i], v.CustomTag.TagName)
		}

		// 测试下架词条不返回
		list = []*Simple{{CreatorID: 2, CustomTagID: 20003}}
		opt = &FindOptions{FindCustomTag: true}
		findSimpleListHelper(list, opt)
		for _, v := range list {
			assert.Nil(v.CustomTag)
		}
	})
}

func TestCallOnlines(t *testing.T) {
	assert := assert.New(t)
	// 只测下 roomIDs 为空的情况，不为空的情况 TestFindSimpleMapHelper 测过了
	res, err := callOnlines([]int64{}, "")
	assert.NoError(err)
	assert.Len(res, 0)
}

func TestSortSimpleByOnline(t *testing.T) {
	assert := assert.New(t)

	m := map[int64]*Simple{
		10: {RoomID: 10, Statistics: nil},
		11: {RoomID: 11, Statistics: &Statistics{Online: 11}},
		12: {RoomID: 12, Statistics: &Statistics{Online: 12}},
	}
	s := []*Simple{
		{
			RoomID:     13,
			Statistics: &Statistics{Online: 13},
		},
	}
	res := SortSimpleByOnline(nil, nil)
	assert.Len(res, 0)
	assert.NotNil(res)

	res = SortSimpleByOnline(nil, s)
	assert.Len(res, 1)

	res = SortSimpleByOnline(m, nil)
	assert.Len(res, 3)
	assert.Equal(int64(12), res[0].Online())

	res = SortSimpleByOnline(m, s)
	assert.Len(res, 4)
	assert.Len(m, 3)
	assert.Equal(int64(13), res[0].Online())
}

func TestListSimples(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	filter := bson.M{"creator_id": 10}
	opt := &FindOptions{FindFans: true, ListenerID: 12}
	res, err := ListSimples(filter, nil, opt)
	require.NoError(err)
	require.Len(res, 1)
	assert.True(res[0].Statistics.Attention)
	assert.True(res[0].Status.Attention)
}

func TestFindSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	filter := bson.M{"creator_id": 10}
	opt := &FindOptions{FindFans: true, ListenerID: 12}
	res, err := FindSimple(filter, opt)
	require.NoError(err)
	assert.NotNil(res)
}

func TestAggregateSimples(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	pipeline := []bson.M{
		{
			"$match": bson.M{"creator_id": 10},
		}, {
			"$sample": bson.M{"size": 1},
		},
	}
	opt := &FindOptions{FindCreator: true}
	res, err := AggregateSimples(pipeline, opt)
	require.NoError(err)
	require.Len(res, 1)
	assert.NotEmpty(res[0].CreatorIconURL)
}

func TestSimpleSchemeToURL(t *testing.T) {
	assert := assert.New(t)
	s := new(Simple)
	s.SchemeToURL()
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", s.CoverURL)
	assert.Nil(s.Background)
	s.Cover = new(string)
	*s.Cover = "aaa/bb"
	s.Background = &Background{Image: "aaa/bb"}
	s.SchemeToURL()
	assert.Equal("https://static-test.missevan.com/aaa/bb", s.CoverURL)
	assert.Equal("https://static-test.missevan.com/aaa/bb", s.Background.ImageURL)
	*s.Cover = "oss://cc/dd"
	s.Background = &Background{Image: "oss://cc/dd"}
	s.SchemeToURL()
	assert.Equal("https://static-test.missevan.com/cc/dd", s.CoverURL)
	assert.Equal("https://static-test.missevan.com/cc/dd", s.Background.ImageURL)
}

func TestAfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s := new(Simple)
	s.AfterFind()

	s.Status = &Status{
		Open:  StatusOpenTrue,
		Score: 2.1,
	}
	s.Statistics = &Statistics{}
	s.AfterFind()
	assert.Equal(int64(2), s.Statistics.Score)
	s.AfterFind()
	assert.Equal(int64(2), s.Statistics.Score)

	s.Status.Open = StatusOpenFalse
	s.AfterFind()
	assert.Zero(s.Statistics.Score)

	// 重复 AfterFind
	coverURL := "oss://test"
	s.Cover = &coverURL
	s.AfterFind()
	require.NotEmpty(s.CoverURL)
	coverURL1 := s.CoverURL
	s.AfterFind()
	coverURL2 := s.CoverURL
	assert.Equal(coverURL1, coverURL2)

	s = &Simple{
		Status: &Status{RedPacket: 6},
	}
	s.AfterFind()
	assert.Equal(s.Status.RedPacket, 1)
	s = &Simple{
		Status: &Status{RedPacket: 0},
	}
	s.AfterFind()
	assert.Zero(s.Status.RedPacket)
}

func TestIsOpen(t *testing.T) {
	assert := assert.New(t)

	s := new(Simple)
	assert.False(s.IsOpen())
	s.Status = new(Status)
	assert.False(s.IsOpen())
	s.Status.Open = StatusOpenTrue
	assert.True(s.IsOpen())
}

func TestSimpleSetIndex(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	i := 1
	var s Simple
	s.SetIndex(i)
	i = 2
	require.NotNil(s.Index)
	assert.Equal(1, *s.Index) // 确保设置成功并且分配了内存，并未使用传入的内存
	s.Index = &i
	s.SetIndex(3)
	assert.Equal(3, i) // 确保只在必要的时候给指针分配内存
}

func TestSimpleMapFindCustomTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	simpleMap := map[int64]*Simple{
		1232456: {CreatorID: 1232456, CustomTagID: 10000},
		2132141: {CreatorID: 2132141, CustomTagID: 10001},
		3214324: {CreatorID: 3214324, CustomTagID: 10002},
	}
	simpleMapFindCustomTag(simpleMap)

	testSimple1 := simpleMap[1232456].CustomTag
	require.Nil(testSimple1)

	testSimple2 := simpleMap[2132141].CustomTag
	require.NotNil(testSimple2)
	assert.EqualValues(10001, testSimple2.TagID)
	assert.EqualValues("test10001", testSimple2.TagName)

	testSimple3 := simpleMap[3214324].CustomTag
	require.NotNil(testSimple3)
	assert.EqualValues(10002, testSimple3.TagID)
	assert.EqualValues("test10002", testSimple3.TagName)
}

func TestSimpleMapFindRoomLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testRoomID = 233

	// 测试获取房间的福袋状态
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Delete("", "room_id = ?", testRoomID).Error)
	simpleMap := map[int64]*Simple{
		1232456: {RoomID: testRoomID, Status: &Status{}},
		2132141: {RoomID: 123, Status: &Status{}},
	}
	initiateRecord := &luckybag.InitiateRecord{
		RoomID:    testRoomID,
		CreatorID: 1232456,
		Status:    luckybag.StatusPending,
	}
	require.NoError(luckybag.DB().Create(initiateRecord).Error)

	simpleMapFindRoomLuckyBag(simpleMap)
	assert.Equal(LuckyBagStatusPending, simpleMap[1232456].Status.LuckyBag)
	assert.Zero(simpleMap[2132141].Status.LuckyBag)
}

func TestHasNovaTag(t *testing.T) {
	tests := []struct {
		name     string
		simple   *Simple
		expected bool
	}{
		{
			name:     "StatisticsIsNil",
			simple:   &Simple{},
			expected: false,
		},
		{
			name: "RoomInExcludeList",
			simple: &Simple{
				RoomID: 10652247, // 猫耳娘的零钱袋
				Statistics: &Statistics{
					Revenue:       NovaRevenueThreshold - 1,
					TotalDuration: NovaTotalDurationThreshold - 1,
				},
			},
			expected: false,
		},
		{
			name: "AllConditionsMet",
			simple: &Simple{
				RoomID: 999999,
				Statistics: &Statistics{
					Revenue:       NovaRevenueThreshold,
					TotalDuration: NovaTotalDurationThreshold,
				},
			},
			expected: true,
		},
		{
			name: "RevenueOverThreshold",
			simple: &Simple{
				RoomID: 999999,
				Statistics: &Statistics{
					Revenue: NovaRevenueThreshold + 1,
				},
			},
			expected: false,
		},
		{
			name: "TotalDurationOverThreshold",
			simple: &Simple{
				RoomID: 999999,
				Statistics: &Statistics{
					TotalDuration: NovaTotalDurationThreshold + 1,
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.simple.HasNovaTag())
		})
	}
}
