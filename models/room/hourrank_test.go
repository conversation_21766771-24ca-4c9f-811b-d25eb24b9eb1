package room

import (
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNotifyHourRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(864000, 0)
	})
	defer goutil.SetTimeNow(nil)

	// 没有小时榜没有变动不会报错
	assert.NoError(NotifyHourRank(nil, nil))

	notifyKey := keys.KeyCronNotifyHourRank0.Format()
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow())
	baseMock := make([]*redis.Z, 11)
	for i := 0; i < len(baseMock); i++ {
		baseMock[i] = &redis.Z{
			Score:  float64(i*10) + 1,
			Member: i + 10,
		}
	}
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, baseMock...).Err())
	require.NoError(service.Redis.Del(notifyKey).Err())

	// 用户当前排名未上榜
	r := Room{}
	r.CreatorID = 10
	err := NotifyHourRank(&usersrank.RankChange{UserID: 10, PrevRank: 13, Rank: 11, Score: int64(baseMock[0].Score)}, &r)
	assert.NoError(err)
	err = service.Redis.Get(notifyKey).Err()
	assert.True(serviceredis.IsRedisNil(err))
	// 用户保持在第一名
	r.CreatorID = 20
	err = NotifyHourRank(&usersrank.RankChange{UserID: 20, PrevRank: 1, Rank: 1, Score: int64(baseMock[len(baseMock)-1].Score)}, &r)
	assert.NoError(err)
	err = service.Redis.Get(notifyKey).Err()
	assert.True(serviceredis.IsRedisNil(err))
	// 用户从榜外上榜
	err = NotifyHourRank(&usersrank.RankChange{UserID: 20, PrevRank: 12, Rank: 1, Score: int64(baseMock[len(baseMock)-1].Score)}, &r)
	assert.NoError(err)
	val, err := service.Redis.Del(notifyKey).Result()
	require.NoError(err)
	assert.Equal(int64(1), val)
	err = NotifyHourRank(&usersrank.RankChange{UserID: 20, PrevRank: 12, Rank: 2, Score: int64(baseMock[len(baseMock)-2].Score)}, &r)
	assert.NoError(err)
	err = NotifyHourRank(&usersrank.RankChange{UserID: 20, PrevRank: 14, Rank: 2, Score: int64(baseMock[len(baseMock)-2].Score)}, &r)
	assert.NoError(err)
	r.CreatorID = 11
	err = NotifyHourRank(&usersrank.RankChange{UserID: 11, PrevRank: 0, Rank: 10, Score: int64(baseMock[1].Score)}, &r)
	assert.NoError(err)
	// 用户为第十名且没有变化
	err = NotifyHourRank(&usersrank.RankChange{UserID: 11, PrevRank: 10, Rank: 10, Score: int64(baseMock[1].Score)}, &r)
	assert.NoError(err)
	val, err = service.Redis.Del(notifyKey).Result()
	require.NoError(err)
	assert.Equal(int64(1), val)
	// 榜单上只有一个用户
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, &redis.Z{
		Score:  100,
		Member: 10,
	}).Err())
	r.CreatorID = 10
	err = NotifyHourRank(&usersrank.RankChange{UserID: 10, PrevRank: 13, Rank: 1, Score: 100}, &r)
	assert.NoError(err)
	require.NoError(service.Redis.Del(key).Err())
	val, err = service.Redis.Del(notifyKey).Result()
	require.NoError(err)
	assert.Equal(int64(1), val)

	// 测试广播筛选
	popularRoomID := int64(369892)
	unpopularRoomID := int64(3192516)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := service.MongoDB.Collection(CollectionName)
	_, err = col.UpdateMany(ctx, bson.M{"room_id": bson.M{"$in": []int64{popularRoomID, unpopularRoomID}}},
		bson.M{"$set": bson.M{"status.open": StatusOpenTrue}})
	require.NoError(err)
	_, err = col.UpdateMany(ctx, bson.M{"room_id": popularRoomID},
		bson.M{"$set": bson.M{"config.popularity": 1}})
	require.NoError(err)
	popularBroadcasted := false
	unpopularBroadcasted := true
	cancel = mrpc.SetMock(userapi.URIIMBroadcastMany, func(in interface{}) (interface{}, error) {
		payloads := in.([]newRankPayload)
		for _, payload := range payloads {
			if payload.RoomID == popularRoomID {
				popularBroadcasted = true
			}
			if payload.RoomID == unpopularRoomID {
				unpopularBroadcasted = true
			}
		}
		return "success", nil
	})
	defer cancel()
	err = NotifyHourRank(&usersrank.RankChange{UserID: 10, PrevRank: 13, Rank: 11, Score: int64(baseMock[0].Score)}, &r)
	require.NoError(err)
	assert.False(popularBroadcasted)
	assert.True(unpopularBroadcasted)
}
