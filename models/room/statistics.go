package room

import (
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	muser "github.com/MiaoSiLa/missevan-go/models/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// operators
const (
	OperatorUser   = "user"
	OperatorStaff  = "staff"
	OperatorSystem = "system"
)

const oneHourMilli int64 = 3600 * 1000

// emptyMeta 空的
var emptyMeta livemeta.Simple

// Statistics sub struct of room
type Statistics struct {
	GiftCount     int64   `bson:"gift_count" json:"-"`
	QuestionCount int64   `bson:"question_count" json:"-"`
	Revenue       int64   `bson:"revenue" json:"revenue"`
	Point         float64 `bson:"point" json:"-"`
	Accumulation  int64   `bson:"accumulation" json:"accumulation"`

	TotalDuration goutil.TimeUnixMilli `bson:"total_duration" json:"-"`

	Online         int64 `bson:"-" json:"online"`
	Attention      bool  `bson:"-" json:"attention"` // Deprecated: 请使用 Status.Attention
	AttentionCount int64 `bson:"-" json:"attention_count"`
	Vip            int64 `bson:"-" json:"vip"`
	Score          int64 `bson:"-" json:"score"`
}

// ScaleNumberOfPeople 应用 livemeta 数据并缩放统计人数
// livemeta 是 nil 则只缩放统计人数
func (s *Statistics) ScaleNumberOfPeople(meta *livemeta.Simple) {
	if meta == nil {
		meta = &emptyMeta
	}
	s.Online = max(s.Online, meta.Online)
	s.Accumulation = max(s.Online, s.Accumulation, meta.Accumulation)

	// 防止友商获取实际流量
	s.Online = max(s.Online, scale(s.Online, meta.Ratio))
	s.Accumulation = max(s.Accumulation, scale(s.Accumulation, meta.Ratio))
}

// LoadScore 获取热度
func (s *Statistics) LoadScore(status *Status) {
	if status == nil {
		return
	}
	if status.Open == 1 {
		s.Score = int64(math.Round(status.Score))
		return
	}
	s.Score = 0
}

func scale(count int64, coefficient float64) int64 {
	if count <= 0 {
		return count
	}
	if coefficient <= 0.0 {
		coefficient = 1.0
	}
	scale := float64(count) * coefficient
	if scale <= 1.0 {
		return count
	}
	return int64(float64(count) * math.Log10(scale))
}

// insertStatisticsAnchor 保存一条所有统计都是 0 的 live_statistics 数据
// TODO: 迁移到单独的包下面
func (r *Room) insertStatisticsAnchor() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	insert := bson.M{
		"_room_id":             r.OID,
		"room_id":              r.RoomID,
		"catalog_id":           r.CatalogID,
		"time":                 util.TimeToUnixMilli(goutil.TimeNow()),
		"online":               0,
		"accumulation":         0,
		"vip":                  0,
		"display_accumulation": 0, // 显示的累计人数
		"display_online":       0, // 显示的在线人数
	}
	_, err := service.MongoDB.Collection("live_statistics").InsertOne(ctx, insert)
	if err != nil {
		logger.Error(err)
		return
	}
}

// CloseStatistics 关播统计
type CloseStatistics struct {
	By      string `json:"-"`
	ByID    int64  `json:"-"`
	Message string `json:"-"`

	Duration      int64  `json:"duration"`
	Revenue       *int64 `json:"revenue,omitempty"`
	Accumulation  int64  `json:"accumulation"`
	QuestionCount int64  `json:"-"`
	MessageCount  int64  `json:"message_count"`

	NewFansCount  *int64 `json:"new_fans_count,omitempty"`
	NewMedalCount *int64 `json:"new_medal_count,omitempty"`
	PaidUserCount *int64 `json:"paid_user_count,omitempty"`

	openTime    int64 // 毫秒时间戳 TODO: 使用 goutil.TimeUnixMilli
	closeTime   int64 // 毫秒时间戳 TODO: 使用 goutil.TimeUnixMilli
	creatorID   int64
	updatedTime time.Time
	room        *Room
}

// NewCloseStatistics new CloseStatistics
// 字符串均可以传空值
func NewCloseStatistics(r *Room, operator string, operatorUserID int64, message string) *CloseStatistics {
	cs := &CloseStatistics{
		By:            operator,
		ByID:          operatorUserID,
		Message:       message,
		Revenue:       new(int64),
		Accumulation:  r.Statistics.Accumulation,
		QuestionCount: r.Statistics.QuestionCount - r.Status.OpenQuestionCount,
		MessageCount:  MessageCount(r.RoomID),

		openTime:    r.Status.OpenTime,
		creatorID:   r.CreatorID,
		updatedTime: goutil.TimeNow(),
		room:        r,
	}
	if cs.By == "" {
		cs.By = OperatorUser
	}
	cs.closeTime = util.TimeToUnixMilli(cs.updatedTime)
	cs.Duration = cs.closeTime - cs.openTime
	*cs.Revenue = r.Statistics.Revenue - r.Status.OpenRevenue
	return cs
}

// BuildFull 构建完整关播统计数据（主播视角）
func (c *CloseStatistics) BuildFull() {
	if c.room.Status.OpenFansCount >= 0 {
		u, err := muser.FindByUserID(c.room.CreatorID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if u != nil {
			c.NewFansCount = util.NewInt64(max(u.FansNum-c.room.Status.OpenFansCount, 0))
		}
	}
	if c.room.Status.OpenMedalCount >= 0 {
		count, err := livemedal.CountRoomMedal(c.room.RoomID)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			c.NewMedalCount = util.NewInt64(max(count-c.room.Status.OpenMedalCount, 0))
		}
	}
	c.PaidUserCount = util.NewInt64(chatroom.CountCurrentRoomPaidUser(c.room.RoomID, c.room.Status.OpenTime))
}

// CloseStatus 关播状态数据
type CloseStatus struct {
	OpenTime  int64 `json:"open_time"`
	CloseTime int64 `json:"close_time"`
}

// CloseNotifier 关播消息通知
type CloseNotifier struct {
	Type              string          `json:"type"`
	Event             string          `json:"event"`
	RoomID            int64           `json:"room_id"`
	Message           string          `json:"message,omitempty"`
	Time              int64           `json:"time"`
	Statistics        CloseStatistics `json:"statistics"`
	Status            CloseStatus     `json:"status"`
	RecommendMaxDelay int64           `json:"recommend_max_delay"`
	By                string          `json:"by"`
	Recommend         []*Simple       `json:"recommend,omitempty"`
	User              struct {
		UserID int64 `json:"user_id"` // 总是主播 ID
	} `json:"user"`
}

type onlineCountResp struct {
	RoomID int64 `json:"room_id"`
	Count  int64 `json:"count"`
}

// NewCloseNotifier new CloseNotifier
func NewCloseNotifier(cs *CloseStatistics, recommend []*Simple, c *handler.Context) *CloseNotifier {
	closeNotifier := CloseNotifier{
		Type:       liveim.TypeRoom,
		Event:      liveim.EventClose,
		RoomID:     cs.room.RoomID,
		Message:    cs.Message,
		Time:       cs.closeTime,
		Statistics: *cs,
		Status: CloseStatus{
			OpenTime:  cs.openTime,
			CloseTime: cs.closeTime,
		},
		// 默认给 -1（默认采取保守策略，即不获取关播推荐）
		RecommendMaxDelay: -1,
		By:                cs.By,
	}
	closeNotifier.Recommend = recommend
	closeNotifier.User.UserID = cs.creatorID
	// TODO(2020-04-18): 老版本客户端和网页版目前对广播消息里不带收益值处理存在问题
	// 超管关闭直播间时，主播端收益显示异常
	// closeNotifier.Statistics.Revenue = nil

	// 大主播（推荐列表不为 nil）直接采用默认的 RecommendMaxDelay 值
	if recommend != nil {
		return &closeNotifier
	}

	var resp onlineCountResp
	// TODO: 添加 RPC 调用到 userapi 中
	err := c.MRPC("im://online/count", handler.M{"room_id": cs.room.RoomID}, &resp)
	if err != nil {
		// 如果请求发生错误，recommend_max_delay 字段默认返回 -1
		logger.WithField("room_id", cs.room.RoomID).Errorf("failed to call rpc when peforming notify close operation: %v", err)
		// PASS
	} else {
		if !cs.room.IsSpecialCreator() {
			// 根据 在线人数 除以 QPS，再乘以 1000 换算为毫秒，最终得到计算的结果进行判断
			// 如果小于 1000，则返回 1000，如果大于等于 1000 小于 10000，则返回计算后的结果
			// 超过 10000、出错时、大流量主播默认返回 -1
			delay := resp.Count * 1000 / 2000
			switch {
			case delay <= 1000:
				closeNotifier.RecommendMaxDelay = 1000
			case delay <= 10000:
				closeNotifier.RecommendMaxDelay = delay
			}
		}
	}

	return &closeNotifier
}

// Notify 广播通知
func (cn *CloseNotifier) Notify() {
	err := userapi.Broadcast(cn.RoomID, &cn)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
