package room

import (
	"encoding/json"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	existsRoomID      = int64(18113499)
	bannedRoomID      = int64(369892)
	testRoomID        = int64(22489473)
	testRoomCreatorID = int64(10)
	openingRoomID     = int64(3192516)
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	initBannedRoom()
	m.Run()
}

func initBannedRoom() {
	banRoom, err := Find(bannedRoomID, &FindOptions{DisableAll: true})
	if err != nil {
		logger.Fatal(err)
	}
	meta := livemeta.LiveMeta{
		RoomOID: banRoom.OID,
		RoomID:  bannedRoomID,
		Ban: &livemeta.Ban{
			Type:      livemeta.TypeBanForever,
			StartTime: 946656000,
		},
		Online:       1,
		Accumulation: 12,
		Ratio:        1.3,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(livemeta.CollectionName)
	err = collection.FindOneAndUpdate(ctx, bson.M{"room_id": bannedRoomID}, bson.M{"$set": meta},
		options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil && err != mongo.ErrNoDocuments {
		logger.Fatal(err)
	}
}

func TestFindRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	roomID, err := FindRoomID(-1000)
	assert.Zero(roomID)
	assert.NoError(err)
	roomID, err = FindRoomID(testRoomCreatorID)
	require.NoError(err)
	assert.Equal(testRoomID, roomID)
}

func TestFindPushingRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	rooms, err := FindPushingRoom(ChannelProviderAliyun, 2)
	require.NoError(err)
	assert.NotNil(rooms)
	rooms, err = FindPushingRoom(ChannelProviderKsyun, 0)
	require.NoError(err)
	assert.Nil(rooms)
}

func TestFindCreatorID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	creatorID, err := FindCreatorID(-1000)
	assert.Zero(creatorID)
	assert.NoError(err)
	creatorID, err = FindCreatorID(testRoomID)
	require.NoError(err)
	assert.Equal(testRoomCreatorID, creatorID)
}

func TestFind(t *testing.T) {
	assert := assert.New(t)

	r, err := Find(1234)
	assert.Nil(err)
	assert.Nil(r)
	r, err = Find(existsRoomID, &FindOptions{})
	assert.NoError(err)
	assert.Equal(r.RoomID, existsRoomID)
}

func TestFindHelper(t *testing.T) {
	t.Run("统计人数", func(t *testing.T) {
		assert := assert.New(t)

		r := new(Room)
		r.Status.Open = StatusOpenTrue
		r.RoomID = 10
		r.Statistics.Online = -1
		r.Statistics.Accumulation = -2
		opt := &FindOptions{FindOnline: true, ClientIP: "127.0.0.1"}
		findHelper(r, opt)
		assert.Equal(int64(0), r.Statistics.Online)
		assert.Equal(int64(0), r.Statistics.Accumulation)
	})
	t.Run("粉丝数", func(t *testing.T) {
		assert := assert.New(t)

		r := new(Room)
		r.CreatorID = 10
		opt := &FindOptions{FindFans: true, ListenerID: 10}
		findHelper(r, opt)
		assert.NotZero(r.Statistics.AttentionCount)
	})
	t.Run("审核信息", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		r := new(Room)
		r.CreatorID = 14
		r.RoomID = 123
		opt := &FindOptions{FindReviewing: true, ListenerID: r.CreatorID}
		findHelper(r, opt)
		require.NotNil(r.Cover)
		assert.Equal("oss://avatars/icon01.png", *r.Cover)
		require.NotNil(r.Background)
		assert.Equal("oss://avatars/icon01.png", r.Background.Image)
		assert.Equal(1.0, r.Background.Opacity)
		assert.Equal(ImageStatus(13), *r.ImageStatus)
		assert.Equal("测试直播审核", r.Name)
		assert.True(r.NameReviewing)
	})
	t.Run("主播信息", func(t *testing.T) {
		assert := assert.New(t)

		r := new(Room)
		r.CreatorID = 99999999
		opt := &FindOptions{FindCreator: true}
		findHelper(r, opt)
		defaultIconURL := service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
		assert.Equal(defaultIconURL, r.CreatorIconURL)
		r.CreatorID = 10
		findHelper(r, opt)
		assert.NotEqual(defaultIconURL, r.CreatorIconURL)
	})
}

func TestRoom_findCatalogInfo(t *testing.T) {
	assert := assert.New(t)

	r := new(Room)
	r.findCatalogInfo()
	assert.Empty(r.CatalogName)
	assert.Empty(r.CatalogColor)

	r.CatalogID = 104
	r.findCatalogInfo()
	assert.NotEmpty(r.CatalogName)
	assert.NotEmpty(r.CatalogColor)
}

func TestRoom_findCustomTag(t *testing.T) {
	assert := assert.New(t)

	r := new(Room)
	r.findCustomTag()
	assert.Nil(r.CustomTag)

	r.CustomTagID = 10001
	r.findCustomTag()
	assert.NotNil(r.CustomTag)
}

func TestRoom_SetPendant(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(Room)
	r.SetPendant(nil)
	assert.Nil(r.Background)

	ua := userappearance.UserAppearance{
		Image: "test_image",
	}
	r.SetPendant(&ua)
	require.NotNil(r.Background)
	assert.Equal("test_image", r.Background.PendantImage)
	ua.Image = "test_image_2"
	r.SetPendant(&ua)
	assert.Equal("test_image_2", r.Background.PendantImage)
	r.SetPendant(nil)
	assert.Equal("", r.Background.PendantImage)
}

func TestIsBan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(Room)
	r.RoomID = existsRoomID

	assert.False(r.IsBan())

	r, err := Find(bannedRoomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)
	assert.True(r.IsBan())
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	ok, err := Exists(existsRoomID)
	assert.NoError(err)
	assert.True(ok)
	ok, err = Exists(-10)
	assert.NoError(err)
	assert.False(ok)
}

func TestExists2(t *testing.T) {
	assert := assert.New(t)
	ok, err := Exists2(bson.M{"room_id": existsRoomID})
	assert.NoError(err)
	assert.True(ok)
	ok, err = Exists2(bson.M{"room_id": -10})
	assert.NoError(err)
	assert.False(ok)
}

func TestExistsAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := ExistsAll([]int64{existsRoomID})
	require.NoError(err)
	assert.True(exists)

	exists, err = ExistsAll([]int64{existsRoomID, 9999999})
	require.NoError(err)
	assert.False(exists)
}

func TestSchemeToURL(t *testing.T) {
	assert := assert.New(t)
	r := new(Room)
	r.SchemeToURL()
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", r.CoverURL)
	assert.Empty(r.Background)
	r.Background = new(Background)
	r.SchemeToURL()
	assert.Empty(r.Background.ImageURL)
	r.Cover = new(string)
	*r.Cover = "aaa"
	r.Background.Image = "ccc"
	r.Background.PendantImage = "pendant"
	r.SchemeToURL()
	assert.Equal("https://static-test.missevan.com/aaa", r.CoverURL)
	assert.Equal("https://static-test.missevan.com/ccc", r.Background.ImageURL)
	assert.Equal("https://static-test.missevan.com/pendant", r.Background.PendantImageURL)
}

func TestRoomAfterFind(t *testing.T) {
	assert := assert.New(t)

	r := new(Room)
	r.AfterFind()
	assert.Zero(r.Statistics.Score)

	r.Status = Status{
		Open:  StatusOpenTrue,
		Score: 2.1,
	}
	r.AfterFind()
	assert.Equal(int64(2), r.Statistics.Score)

	r.Status.Open = StatusOpenFalse
	r.AfterFind()
	assert.Zero(r.Statistics.Score)
	assert.Equal(uint32(livequestion.MinPrice), r.Question.CurrentMinPrice())
	assert.Equal(QuestionMaxLimit, r.Question.MaxLimit)
	r = &Room{
		Helper: Helper{
			RoomID: OpenListExcludeRoomIDs()[0],
			TagIDs: []int64{3},
		},
	}
	r.AfterFind()
	assert.Equal([]int64{3}, r.TagIDs)
	r = &Room{
		Helper: Helper{
			Statistics: Statistics{
				Revenue: 1,
			},
		},
	}
	r.AfterFind()
	assert.Equal([]int64{tag.TagNova}, r.TagIDs)

	r = &Room{
		Helper: Helper{Status: Status{RedPacket: 6}},
	}
	r.AfterFind()
	assert.Equal(r.Status.RedPacket, 1)

	r = &Room{
		Helper: Helper{Status: Status{RedPacket: 0}},
	}
	r.AfterFind()
	assert.Zero(r.Status.RedPacket)
}

func TestRoomKeys(t *testing.T) {
	var r Room
	var h Helper
	var s Simple

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(r, "_id")
	kc.Check(h, "room_id", "catalog_id", "custom_tag_id", "tag_ids", "name", "name_clean", "announcement",
		"type", "channel", "cover", "background", "guild_id",
		"creator_id", "creator_username", "status", "notice", "medal", "statistics",
		"created_time", "updated_time", "connect", "question",
		"set_catalog_id", "activity_catalog_id", "limit", "energy", "config")
	simpleTags := []string{
		"room_id", "catalog_id", "custom_tag_id", "name", "announcement",
		"creator_id", "creator_username", "statistics", "status", "cover",
		"activity_catalog_id",
		// SimpleProjection() 不需要以下字段
		"background",
	}
	kc.Check(s, simpleTags...)

	kc = tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(SimpleProjection(), simpleTags[:len(simpleTags)-1]...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(r, "members", "cover_url", "creator_iconurl", "image_status", "name_reviewing", "catalog_name",
		"catalog_color", "custom_tag", "preview_tag", "preview_intro")
	kc.Check(h, "room_id", "catalog_id", "custom_tag_id", "tag_ids", "name", "announcement", "type",
		"channel", "background", "creator_id", "creator_username", "status",
		"notice", "medal", "statistics", "connect", "question", "activity_catalog_id")
	kc.Check(s, "room_id", "catalog_id", "name", "announcement", "creator_id", "creator_username",
		"statistics", "status", "cover_url", "creator_iconurl", "creator_introduction", "catalog_name",
		"catalog_color", "custom_tag", "background", "icon_url", "from", "recommend_id", "recommend_tag", "activity_catalog_id")
}

func TestIsAgoraLive(t *testing.T) {
	assert := assert.New(t)
	r := new(Room)
	r.Type = TypeLive
	r.Connect.Provider = ProviderAgora
	assert.True(r.IsAgoraLive())
	r.Type = TypeConnect
	assert.False(r.IsAgoraLive())
}

func TestIsOwner(t *testing.T) {
	assert := assert.New(t)
	r := new(Room)
	r.CreatorID = 12
	u := util.SmartUserContext{UID: 12}
	assert.True(r.IsOwner(u))
	assert.False(r.IsOwner(nil))
}

func TestTrySwitchProvider(t *testing.T) {
	t.Skip()
	assert := assert.New(t)

	r := new(Room)
	r.Type = TypeLive
	r.Connect.Provider = ProviderAgora
	r.TrySwitchProvider(false)
	assert.Equal(ProviderAgora, r.Connect.Provider)
}

func TestUpdateReview(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l, err := live.Save(&live.SaveParams{
		CreatorID: 892,
		Title:     "test_review_name",
		RoomID:    bannedRoomID,
	})
	require.NoError(err)
	reviewName := fmt.Sprintf("review_name_%d", goutil.TimeNow().Hour())
	assert.NoError(livereview.Submit(l.RoomID, l.UserID, []livereview.ReviewInfo{
		{Type: livereview.TypeCover, ImageURL: "oss://test.png"},      // 封面的
		{Type: livereview.TypeBackground, ImageURL: "oss://test.png"}, // 背景的
		{Type: livereview.TypeName, Name: reviewName},                 // 名称
	}))
	defer service.DB.Table(livereview.TableName()).Delete("", "room_id = ? AND status <> ?",
		l.RoomID, livereview.StatusReviewing)
	testLiveReview := new(livereview.LiveReview)
	require.NoError(service.DB.Where("room_id = ? AND status = ? AND type = ?",
		l.RoomID, livereview.StatusReviewing, livereview.TypeCover).First(testLiveReview).Error)

	_, err = Update(l.RoomID, bson.M{"custom_tag_id": 10001})
	require.NoError(err)

	var count int
	cancel := mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		count++
		var body struct {
			Payload *updatePayload
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		payload := body.Payload
		require.NotNil(payload)
		assert.EqualValues(10001, payload.Room.CustomTagID)

		return "success", nil
	})
	defer cancel()

	// 封面
	after, err := UpdateReview(testLiveReview.UserID, testLiveReview.UploadTime, testLiveReview.Type)
	require.NoError(err)
	require.NotNil(after)
	// 检查封面是否保存
	require.NoError(service.DB.Where("user_id = ?", l.UserID).First(&l).Error)
	assert.Equal(after.ImageURL, l.Cover)
	assert.EqualValues(1, count)

	// 背景的
	testLiveReview = new(livereview.LiveReview)
	require.NoError(service.DB.Where("room_id = ? AND status = ? AND type = ?",
		l.RoomID, livereview.StatusReviewing, livereview.TypeBackground).First(testLiveReview).Error)
	after, err = UpdateReview(testLiveReview.UserID, testLiveReview.UploadTime, testLiveReview.Type)
	require.NoError(err)
	assert.NotNil(after)
	assert.EqualValues(2, count)

	// 名称
	testLiveReview = new(livereview.LiveReview)
	require.NoError(service.DB.Where("room_id = ? AND status = ? AND type = ?",
		l.RoomID, livereview.StatusReviewing, livereview.TypeName).Take(testLiveReview).Error)
	after, err = UpdateReview(testLiveReview.UserID, testLiveReview.UploadTime, testLiveReview.Type)
	require.NoError(err)
	assert.NotNil(after)
	l = new(live.Live)
	require.NoError(service.DB.Take(&l, "room_id = ?", testLiveReview.RoomID).Error)
	assert.Equal(reviewName, l.Title)
	assert.EqualValues(3, count)

	// 失败的
	after, err = UpdateReview(122, 1234567890, 0)
	assert.NoError(err)
	assert.Nil(after)
	assert.EqualValues(3, count)
}

func TestNotifyUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// tagKey
	p := updatePayload{}
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(p, "type", "event", "room_id", "room")
	kc.Check(p.Room, "room_id", "catalog_id", "custom_tag_id", "name",
		"announcement", "creator_id", "type", "notice", "background", "cover_url")
	bg := bgWithImage{}
	kc.Check(bg, "image")

	cancel := mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		var body struct {
			Payload updatePayload `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		payload := body.Payload
		require.NotNil(payload)
		require.NotNil(payload.Room)
		require.NotNil(payload.Room.Background)
		assert.Equal(int64(12), payload.Room.RoomID)
		assert.Equal(int64(12), payload.Room.CreatorID)
		return "success", nil
	})
	defer cancel()

	// NotifyUpdate
	r := new(Room)
	r.RoomID = 12
	r.Background = &Background{}
	r.CreatorID = 12
	assert.NoError(r.NotifyUpdate())
}

func TestHaveMedal(t *testing.T) {
	assert := assert.New(t)
	ok, _ := HaveMedal(testRoomID)
	assert.True(ok)
	ok, _ = HaveMedal(-100)
	assert.False(ok)
	ok, _ = HaveMedal(3192516)
	assert.False(ok)
}

func TestRoom_HaveMedal(t *testing.T) {
	assert := assert.New(t)

	room := Room{
		Helper: Helper{
			Medal: &Medal{
				Name: "测试勋章",
			},
		},
	}
	assert.True(room.HaveMedal())

	room.Medal = nil
	assert.False(room.HaveMedal())
}

func TestMedalExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	_, err := collection.UpdateOne(ctx, bson.M{"creator_id": 100},
		bson.D{bson.E{
			Key: "$set",
			Value: bson.M{
				"medal": NewMedal("测重复a"),
			},
		}})
	require.NoError(err)

	exist, err := MedalExists("测重复A", 12345)
	require.NoError(err)
	assert.True(exist)

	exist, err = MedalExists("测重复a", 12345)
	require.NoError(err)
	assert.True(exist)

	exist, err = MedalExists("测重复a", 100)
	require.NoError(err)
	assert.True(exist)

	exist, err = MedalExists("测重复A", 100)
	require.NoError(err)
	assert.False(exist)

	exist, err = MedalExists("测重复B", 100)
	require.NoError(err)
	assert.False(exist)
}

func TestOpenRoomIDs(t *testing.T) {
	assert := assert.New(t)
	res, err := OpenRoomIDs()
	// 测试房间有一个被设置成常开的
	assert.True(err == nil && len(res) != 0, err)
}

func TestAddAccumulate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"statistics.accumulation": 100}})
	require.NoError(err)
	r, err := Find(testRoomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)

	r.AddAccumulate()
	assert.EqualValues(101, r.Statistics.Accumulation)

	r, err = Find(r.RoomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	assert.EqualValues(101, r.Statistics.Accumulation)
}

func TestRoomSyncMySQL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := Find(18113499)
	require.NoError(err)
	require.NotNil(r)
	r.Status.Open = StatusOpenTrue
	require.NoError(r.SyncMySQL())
	r2, err := Find(r.RoomID)
	require.NoError(err)
	require.NotNil(r2)
	var l live.Live
	require.NoError(service.DB.Where("room_id = ?", r.RoomID).Take(&l).Error)
	assert.Equal(r.CatalogID, l.CatalogID)
	assert.Equal(r.RoomID, l.ID)
	assert.Equal(r.RoomID, l.RoomID)
	assert.Equal(r.Name, l.Title)
	assert.Equal(r.Announcement, l.Intro)
	assert.Equal(1, l.Status)
}

func TestClearRoomCache(t *testing.T) {
	assert := assert.New(t)

	roomID := int64(147)
	roomKey := keys.KeyRoomsRoomID1.Format(147)
	ClearRoomCache(roomID)
	v, err := service.Redis.Exists(roomKey).Result()
	assert.NoError(err)
	assert.Zero(v)
}

const testCloseRoom = int64(65150486)

func TestRoomClearList(t *testing.T) {
	require := require.New(t)

	r, err := Find(testCloseRoom)
	require.NoError(err)
	// 仅仅保证调用没有错误日志，详细测试已在单独的包中进行了
	r.ClearList(goutil.SmartUserContext{})
}

func TestRoomClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(liveroomtagrecord.DB().Where("room_id = ?", tag.TagListenDrama, testCloseRoom).
		Delete(&liveroomtagrecord.LiveRoomTagRecord{}).Error)

	r, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(r)
	r.Status.OpenTime = 0
	r.Status.Open = StatusOpenTrue
	oid := primitive.NewObjectID()
	r.Status.OpenLogID = &oid
	r.Statistics.Accumulation = 2
	r.Statistics.Revenue = 10 // NOTICE: 这里为 TestRoomUpdateClose 进行了一下最后值的替换
	r.TagIDs = append(r.TagIDs, tag.TagListenDrama)

	cs := NewCloseStatistics(r, "", r.CreatorID, "")
	beforeClose := util.TimeToUnixMilli(goutil.TimeNow()) - 1
	require.NoError(r.Close(cs, handler.NewTestContext("POST", "", true, nil).UserContext()))
	assert.Equal(r.RoomID, cs.room.RoomID)
	assert.Equal("user", cs.By)
	assert.Greater(cs.closeTime, beforeClose)
	assert.NotZero(cs.Duration)
	assert.Equal(r.CreatorID, cs.creatorID)
	assert.Equal(StatusOpenFalse, r.Status.Open)
	assert.Nil(r.Status.OpenLogID)
	var l live.Live
	require.NoError(service.DB.Find(&l, "user_id = ?", r.CreatorID).Error)
	assert.Equal(StatusOpenFalse, l.Status)

	record, err := liveroomtagrecord.FindLastRecord(tag.TagListenDrama, testCloseRoom)
	require.NoError(err)
	require.NotNil(record)
	assert.Equal(liveroomtagrecord.RoleRoomCreator, record.Role)
	assert.Equal(liveroomtagrecord.OperationRemove, record.Operation)

	r, err = Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(StatusOpenFalse, r.Status.Open)
	assert.Nil(r.Status.OpenLogID)
	assert.Zero(r.Status.RedPacket)
}

func TestRoomUpdateClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := Update(testCloseRoom,
		bson.M{
			"guild_id":       1,
			"catalog_id":     104,
			"set_catalog_id": 0,
		})
	require.NoError(err)
	r, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(r)
	cs := NewCloseStatistics(r, OperatorUser, 10, "")
	cs.closeTime = 1472583690147
	cs.Duration = oneHourMilli
	*cs.Revenue = 10
	cs.Accumulation = 1
	cs.updatedTime = time.Unix(1234567890, 0)
	r.updateClose(cs)
	var res struct {
		Status       Status    `bson:"status"`
		UpdatedTime  time.Time `bson:"updated_time"`
		CatalogID    int64     `bson:"catalog_id"`
		GuildID      int64     `bson:"guild_id"`
		SetCatalogID *int64    `bson:"set_catalog_id"`
	}
	assert.Zero(r.GuildID) // 查看代码是否更新 GuildID 正确
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err = Collection().FindOne(ctx, bson.M{"_id": r.OID}).Decode(&res)
	require.NoError(err)
	assert.Equal(cs.updatedTime.Unix(), res.UpdatedTime.Unix())
	assert.Equal(cs.closeTime, *res.Status.CloseTime)
	assert.Equal(int64(0), res.CatalogID)
	assert.Nil(res.SetCatalogID)
	require.NoError(err)
	assert.Zero(res.GuildID) // 查看数据库是否更新 GuildID 正确
}

func TestRoom_removeListenDramaTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(liveroomtagrecord.DB().Where("room_id = ?", tag.TagListenDrama, testCloseRoom).
		Delete(&liveroomtagrecord.LiveRoomTagRecord{}).Error)

	r := &Room{
		Helper: Helper{
			RoomID:    testCloseRoom,
			CreatorID: 13123123,
			TagIDs:    []int64{tag.TagListenDrama},
		},
	}
	err := r.removeListenDramaTag(OperatorUser, r.CreatorID)
	require.NoError(err)

	record, err := liveroomtagrecord.FindLastRecord(tag.TagListenDrama, testCloseRoom)
	require.NoError(err)
	require.NotNil(record)
	assert.Equal(liveroomtagrecord.RoleRoomCreator, record.Role)
	assert.Equal(liveroomtagrecord.OperationRemove, record.Operation)
}

func TestRoomLogClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := Update(existsRoomID, bson.M{"custom_tag_id": 10002})
	require.NoError(err)

	r, err := Find(existsRoomID)
	require.NoError(err)
	require.NotNil(r)
	now := goutil.TimeNow()
	cs := &CloseStatistics{
		openTime:      util.TimeToUnixMilli(now) - 100,
		closeTime:     util.TimeToUnixMilli(now),
		Duration:      100,
		Revenue:       new(int64),
		Accumulation:  1,
		QuestionCount: 2,
		MessageCount:  3,
		NewFansCount:  util.NewInt64(5),
		NewMedalCount: util.NewInt64(4),
		PaidUserCount: util.NewInt64(2),
	}
	*cs.Revenue = 10

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = service.MongoDB.Collection("live_statistics").InsertMany(ctx, []interface{}{bson.M{
		"_room_id":             "test_room_id",
		"room_id":              existsRoomID,
		"time":                 cs.openTime + 10,
		"online":               0,
		"accumulation":         0,
		"display_accumulation": 0,  // 显示的累计人数
		"display_online":       0,  // 显示的在线人数
		"score":                10, // 热度
	}, bson.M{
		"_room_id":             "test_room_id",
		"room_id":              existsRoomID,
		"time":                 cs.openTime + 11,
		"online":               0,
		"accumulation":         0,
		"display_accumulation": 0,  // 显示的累计人数
		"display_online":       0,  // 显示的在线人数
		"score":                20, // 热度
	}, bson.M{
		"_room_id":             "test_room_id",
		"room_id":              existsRoomID,
		"time":                 cs.openTime + 12,
		"online":               0,
		"accumulation":         0,
		"display_accumulation": 0, // 显示的累计人数
		"display_online":       0, // 显示的在线人数
	}})
	require.NoError(err)

	r.logClose(cs)
	l, err := livelog.GetRoomLogs(r.OID, now.Add(-time.Second), now.Add(time.Second))
	require.NoError(err)
	assert.GreaterOrEqual(len(l), 1)
	assert.EqualValues(cs.openTime, l[0].StartTime)
	assert.EqualValues(cs.closeTime, l[0].EndTime)
	assert.Equal(cs.Duration, l[0].Duration)
	assert.Equal(*cs.Revenue, l[0].Revenue)
	assert.Equal(cs.Accumulation, l[0].Accumulation)
	assert.Equal(cs.QuestionCount, l[0].QuestionCount)
	assert.Equal(cs.MessageCount, l[0].MessageCount)
	assert.Equal(cs.NewMedalCount, l[0].NewMedalCount)
	assert.Equal(cs.PaidUserCount, l[0].PaidUserCount)
	assert.Equal(cs.NewFansCount, l[0].NewFansCount)
	assert.EqualValues(15, l[0].AvgScore)
	assert.EqualValues(20, l[0].MaxScore)
	assert.EqualValues(10002, l[0].CustomTagID)
}

func TestClearOpenCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(Room)
	r.RoomID = testRoomID
	delKeys := []string{
		keys.KeyRoomsMeta1.Format(r.RoomID),
		keys.KeyRoomsFollowed1.Format(r.RoomID),
		roomsrank.Key(r.RoomID, roomsrank.RankTypeCurrent, goutil.TimeNow()),
		keys.KeyRoomPaidUser2.Format(r.RoomID, r.Status.OpenTime),
	}
	assert.NoError(r.ClearOpenCache())
	count, err := service.Redis.Exists(delKeys...).Result()
	require.NoError(err)
	assert.Zero(count)
}

func TestRoomReceiveGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(before)
	require.NoError(before.ReceiveGift(1, 1))
	after, err := Find(testCloseRoom)
	require.NoError(err)
	assert.Equal(int64(1), after.Statistics.GiftCount-before.Statistics.GiftCount)
	assert.Equal(int64(1), after.Statistics.Revenue-before.Statistics.Revenue)
}

func TestRoomReceiveGashaponGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(before)
	require.NoError(before.ReceiveGashaponGifts(10, 1))
	after, err := Find(testCloseRoom)
	require.NoError(err)
	assert.Equal(int64(10), after.Statistics.GiftCount-before.Statistics.GiftCount)
	assert.Equal(int64(1), after.Statistics.Revenue-before.Statistics.Revenue)
}

func TestAddSuperFanRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(before)
	require.NoError(before.AddSuperFanRevenue(1))
	after, err := Find(testCloseRoom)
	require.NoError(err)
	assert.Equal(int64(1), after.Statistics.Revenue-before.Statistics.Revenue)
}

func TestReceiveDanmaku(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(before)
	require.NoError(before.ReceiveDanmaku(1))
	after, err := Find(testCloseRoom)
	require.NoError(err)
	assert.Equal(int64(1), after.Statistics.Revenue-before.Statistics.Revenue)
}

func TestReceiveLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	before, err := Find(testCloseRoom)
	require.NoError(err)
	require.NotNil(before)
	require.NoError(before.ReceiveLuckyBox(1))
	after, err := Find(testCloseRoom)
	require.NoError(err)
	assert.Equal(int64(1), after.Statistics.Revenue-before.Statistics.Revenue)
}

func TestMessageCount(t *testing.T) {
	assert := assert.New(t)

	service.Redis.Del(keys.KeyRoomsMeta1.Format(10))
	assert.Zero(MessageCount(10))
	IncrMessageCount(10)
	assert.NotZero(MessageCount(10))
}

func TestUpdateCatalogID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(UpdateCatalogID(testRoomID, 99))

	roomID := int64(testRoomID)
	catalog := int64(106)
	err := UpdateCatalogID(roomID, catalog)
	require.NoError(err)

	res, err := Find(roomID)
	require.NoError(err)
	assert.Equal(res.CatalogID, catalog)
	// TODO: 用 mock 的数据
	liveRes, err := live.FindLiveByRoomID(roomID)
	require.NoError(err)
	require.NotNil(liveRes)
	assert.Equal(liveRes.CatalogID, catalog)
}

func TestFindAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs := bson.M{"$in": []int64{223355, 223344}}

	rooms, err := FindAll(bson.M{"creator_id": userIDs})
	require.NoError(err)
	require.NotNil(rooms)
	assert.Len(rooms, 2)

	_, err = Update(rooms[0].RoomID, bson.M{"status.open": StatusOpenFalse})
	require.NoError(err)
	_, err = Update(rooms[1].RoomID, bson.M{"status.open": StatusOpenTrue})
	require.NoError(err)
	rooms, err = FindAll(bson.M{"creator_id": userIDs, "status.open": StatusOpenTrue})
	require.NoError(err)
	require.NotNil(rooms)
	assert.Len(rooms, 1)
}

func TestUpdateOneRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{
		"creator_id": 223355,
	}
	room, err := FindOne(filter)
	require.NoError(err)
	require.NotNil(room)
	ut := goutil.TimeNow()
	updateRoom := bson.M{
		"updated_time": ut,
	}
	ok, err := UpdateOneRoom(filter, updateRoom)
	require.NoError(err)
	require.True(ok)
	room, err = FindOne(filter)
	require.NoError(err)
	require.NotNil(room)
	assert.Equal(ut.Unix(), room.UpdatedTime.Unix())

	// 测试不存在更新失败
	filter = bson.M{
		"creator_id": 22334455,
	}
	ok, err = UpdateOneRoom(filter, updateRoom)
	require.NoError(err)
	assert.False(ok)
}

func TestAppendQuestionLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{
		"creator_id": 223355,
	}

	_, err := UpdateOneRoom(filter, bson.M{"question.limit": 100})
	require.NoError(err)

	room, err := FindOne(filter)
	require.NoError(err)
	require.NotNil(room)

	ok, err := room.AppendQuestionLimit(QuestionAppendNum)
	require.NoError(err)
	assert.True(ok)

	room, err = FindOne(filter)
	require.NoError(err)
	require.NotNil(room)
	assert.Equal(100+QuestionAppendNum, room.Question.Limit)

	// 测试两次追加
	ok, err = room.AppendQuestionLimit(QuestionAppendNum)
	require.NoError(err)
	assert.True(ok)

	room, err = FindOne(filter)
	require.NoError(err)
	require.NotNil(room)
	assert.True(room.IsMaxQuestionLimit())

	// 测试 200 个提问的上限
	ok, err = room.AppendQuestionLimit(QuestionAppendNum)
	require.NoError(err)
	assert.False(ok)

	room, err = FindOne(filter)
	require.NoError(err)
	require.NotNil(room)
	assert.Equal(QuestionMaxLimit, room.Question.Limit)
}

func TestRoom_LimitStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(Room)
	r.RoomID = 186192636
	r.Status.Open = StatusOpenTrue
	r.Name = "受限开播房间"
	r.NameClean = "受限开播房间"
	r.Limit = &Limit{
		Type:           LimitTypeNormalGift,
		AllowedUserIDs: []int64{12, 123},
	}
	r.CreatedTime = goutil.TimeNow()
	r.UpdatedTime = goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	_, err := col.UpdateOne(ctx, bson.M{"room_id": r.RoomID},
		bson.M{"$set": bson.M{"limit": r.Limit}})
	require.NoError(err)
	// 测试查不出受限房间
	r, err = FindOne(bson.M{"room_id": r.RoomID},
		&FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r, "保证房间存在")
	r2, err := FindOne(bson.M{"room_id": r.RoomID, "limit": bson.M{"limit": false}},
		&FindOptions{DisableAll: true})
	require.NoError(err)
	assert.Nil(r2)

	assert.Equal(LimitStatusBlocked, r.LimitStatus(0))
	assert.Equal(LimitStatusBlocked, r.LimitStatus(1))
	assert.Equal(LimitStatusNormalGiftAllowed, r.LimitStatus(12))
	r.Limit = nil
	assert.Equal(LimitStatusNormal, r.LimitStatus(12))
	assert.Equal(LimitStatusNormal, r.LimitStatus(r.CreatorID))

	r.CreatorID = 0
	r.RoomID = 0
	assert.PanicsWithValue("room 0 creator_id is 0", func() {
		r.LimitStatus(0)
	})
}

func TestOnlyCreatorFindOnline(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := &FindOptions{
		CreatorFindOnline: true,
	}

	cancel := mrpc.SetMock("im://online/count", func(input interface{}) (output interface{}, err error) {
		output = handler.M{"room_id": 22489473, "count": 2}
		return
	})
	defer cancel()

	// 测试主播
	opt.ListenerID = 516
	room, err := Find(openingRoomID, opt)
	require.NoError(err)
	assert.Equal(int64(2), room.Statistics.Online)

	// 测试普通用户
	opt.ListenerID = 1
	opt.FindOnline = false
	room, err = Find(openingRoomID, opt)
	require.NoError(err)
	assert.Zero(room.Statistics.Online)
}

func TestRandomOpenRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.PanicsWithValue("param error", func() {
		_, _ = RandomOpenRoomID([]int64{})
	})

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var r Room
	err := Collection().FindOne(ctx, bson.M{"status.open": StatusOpenFalse}).Decode(&r)
	require.NoError(err)

	creatorIDs := []int64{r.CreatorID}
	roomID, err := RandomOpenRoomID(creatorIDs)
	require.NoError(err)
	assert.Zero(roomID)

	creatorIDs = append(creatorIDs, TestLimitedRoomCreatorID)
	roomID, err = RandomOpenRoomID(creatorIDs)
	require.NoError(err)
	assert.Equal(TestLimitedRoomID, roomID)
}

func TestUnsetRoomPKStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := Update(existsRoomID, bson.M{"status.pk": 1})
	require.NoError(err)
	assert.EqualValues(1, r.Status.PK)

	err = UnsetRoomPKStatus([]int64{existsRoomID, 1})
	require.NoError(err)
	r, err = Find(existsRoomID)
	require.NoError(err)
	assert.Zero(r.Status.PK)
}

func TestUpdateRoomRedPacketNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"status.red_packet": 5}},
	)
	require.NoError(err)

	ok, err := UpdateRoomRedPacketNum(ctx, testRoomID, -2)
	require.NoError(err)
	assert.True(ok)
	var room *Room
	err = Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&room)
	require.NoError(err)
	assert.Equal(3, room.Status.RedPacket)

	ok, err = UpdateRoomRedPacketNum(ctx, testRoomID, 2)
	require.NoError(err)
	assert.True(ok)
	err = Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&room)
	require.NoError(err)
	assert.Equal(5, room.Status.RedPacket)

	ok, err = UpdateRoomRedPacketNum(ctx, testRoomID, 0)
	require.NoError(err)
	assert.False(ok)
	err = Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&room)
	require.NoError(err)
	assert.Equal(5, room.Status.RedPacket)

	_, err = Collection().UpdateOne(ctx,
		bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"status.red_packet": -1}},
	)
	require.NoError(err)
	ok, err = UpdateRoomRedPacketNum(ctx, testRoomID, -1)
	require.NoError(err)
	assert.False(ok)
	err = Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&room)
	require.NoError(err)
	assert.Equal(-1, room.Status.RedPacket)

	ok, err = UpdateRoomRedPacketNum(ctx, testRoomID, 2)
	require.NoError(err)
	assert.True(ok)
	err = Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&room)
	require.NoError(err)
	assert.Equal(1, room.Status.RedPacket)
}

func TestNewConnectID(t *testing.T) {
	assert := assert.New(t)

	connectID := NewConnectID()
	assert.NotContains(connectID, "-")
}

func TestMatchConnectID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := [2]int64{testRoomID, openingRoomID}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := Collection().UpdateMany(ctx, bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, bson.M{
		"$set": bson.M{
			"connect.provider": ProviderAgora,
		},
	})
	require.NoError(err)
	require.EqualValues(2, res.MatchedCount)

	connectID := NewConnectID()
	err = MatchConnectID(ctx, connectID, ProviderBvclive, testRoomIDs)
	require.NoError(err)

	rooms, err := FindAll(bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, options.Find().SetProjection(bson.M{"connect": 1}))
	require.NoError(err)
	require.Len(rooms, len(testRoomIDs))
	for _, r := range rooms {
		assert.Equal(connectID, r.Connect.ID)
		assert.Equal(ProviderBvclive, r.Connect.Provider)
	}
}

func TestAfterPKConnectFinish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := []int64{223344, 18113499}
	err := service.Redis.SRem(keys.KeyRoomsEnableAgora0.Format(), 223344, 18113499).Err()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err = MatchConnectID(ctx, NewConnectID(),
		ProviderBvclive, [2]int64{testRoomIDs[0], testRoomIDs[1]})
	require.NoError(err)

	err = AfterPKConnectFinish(testRoomIDs)
	require.NoError(err)
	rooms, err := FindAll(bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, options.Find().SetProjection(bson.M{"status": 1, "connect": 1}))
	require.NoError(err)
	require.Len(rooms, 2)
	room1, room2 := rooms[0], rooms[1]
	assert.NotEmpty(room1.Connect.ID)
	assert.NotEmpty(room2.Connect.ID)
	assert.NotEqual(room1.Connect.ID, room2.Connect.ID)
	assert.Zero(room1.Status.PK)
	assert.Zero(room2.Status.PK)

	// 测试不在声网白名单中则将 provider 切换为 bvclive
	err = MatchConnectID(ctx, NewConnectID(),
		ProviderAgora, [2]int64{testRoomIDs[0], testRoomIDs[1]})
	require.NoError(err)
	err = AfterPKConnectFinish(testRoomIDs)
	require.NoError(err)
	rooms, err = FindAll(bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, options.Find().SetProjection(bson.M{"status": 1, "connect": 1}))
	require.NoError(err)
	require.Len(rooms, 2)
	assert.Equal(rooms[0].Connect.Provider, ProviderBvclive)
	assert.Equal(rooms[1].Connect.Provider, ProviderBvclive)
}

func TestRoom_IsSpecialCreator(t *testing.T) {
	assert := assert.New(t)

	r := Room{}
	assert.False(r.IsSpecialCreator())
	r.Config = &Config{Popularity: 1}
	assert.True(r.IsSpecialCreator())
}

func TestSaveChannelBVCSK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoom := &Room{Helper: Helper{RoomID: testRoomID}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := Collection().FindOneAndUpdate(ctx,
		bson.M{"room_id": testRoom.RoomID}, bson.M{"$set": bson.M{"channel.bvc.sk": "2020202"}},
		options.FindOneAndUpdate().SetReturnDocument(options.After)).Decode(testRoom)
	require.NoError(err)
	require.NotNil(testRoom.Channel)
	require.NotNil(testRoom.Channel.BVC)
	require.Equal(testRoom.Channel.BVC.SK, "2020202")

	testSK := "111"
	err = testRoom.SaveChannelBVCSK(testSK)
	require.NoError(err)
	assert.Equal(testSK, testRoom.Channel.BVC.SK)

	r, err := Find(testRoom.RoomID)
	require.NoError(err)
	require.NotNil(r)
	require.NotNil(r.Channel)
	require.NotNil(r.Channel.BVC)
	assert.Equal(testSK, r.Channel.BVC.SK)
}

func TestRoom_FilterGiftMessage(t *testing.T) {
	assert := assert.New(t)

	r := Room{}
	assert.False(r.FilterGiftMessage())

	r.Config = &Config{}
	assert.False(r.FilterGiftMessage())

	r.Config.FilterGiftMessage = true
	assert.True(r.FilterGiftMessage())
}

func TestRoom_IsOpen(t *testing.T) {
	assert := assert.New(t)

	r := new(Room)
	assert.False(r.IsOpen())

	r.Status = Status{}
	assert.False(r.IsOpen())

	r.Status.Open = StatusOpenTrue
	assert.True(r.IsOpen())
}

func TestIsDailyFirstMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(123456)
	err := service.Redis.Del(keys.KeyRoomsRankPointMessageLock2.Format(testUserID, goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))).Err()
	require.NoError(err)

	r := &Room{Helper: Helper{RoomID: 223344}}
	isOnce := r.IsDailyFirstMsg(testUserID)
	assert.True(isOnce)

	isOnce = r.IsDailyFirstMsg(testUserID)
	assert.False(isOnce)
}

func TestRoom_IsMultiConnect(t *testing.T) {
	assert := assert.New(t)

	r := Room{}
	assert.False(r.IsMultiConnect())

	r.Status.MultiConnect = MultiConnectStatusOngoing
	assert.True(r.IsMultiConnect())
}

func TestUpdateGuildID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := FindOne(bson.M{"guild_id": bson.M{"$exists": false}}, &FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(r)

	var testGuildID int64 = 123456
	require.NoError(UpdateGuildID(r.CreatorID, testGuildID))
	r, err = Find(r.RoomID)
	require.NoError(err)
	assert.Equal(testGuildID, r.GuildID)

	require.NoError(UpdateGuildID(r.CreatorID, 0))
	r, err = FindOne(bson.M{"room_id": r.RoomID, "guild_id": bson.M{"$exists": true}}, &FindOptions{DisableAll: true})
	require.NoError(err)
	require.Nil(r)
}

func TestSetGuildID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	room, err := FindOne(bson.M{"guild_id": bson.M{"$exists": false}}, &FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(room)

	// guild_id 不为 0 的情况
	testGuildID := int64(123456)
	err = SetGuildID([]int64{room.CreatorID}, testGuildID)
	require.NoError(err)
	room, err = Find(room.RoomID)
	require.NoError(err)
	assert.Equal(testGuildID, room.GuildID)

	// guild_id 为 0 的情况
	err = SetGuildID([]int64{room.CreatorID}, 0)
	require.NoError(err)
	room, err = Find(room.RoomID)
	require.NoError(err)
	assert.Zero(room.GuildID)
}

func TestCountByFilter(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorIDs := []int64{346286}
	filter := bson.M{
		"creator_id":  bson.M{"$in": creatorIDs},
		"limit":       bson.M{"$exists": false},
		"status.open": StatusOpenTrue,
	}
	count, err := CountByFilter(filter)
	require.NoError(err)
	assert.EqualValues(1, count)
}

func TestRemoveExpiredSuppressionHotTag(t *testing.T) {
	t.Run("不在名单", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		err := service.Redis.Del(key).Err()
		require.NoError(err)

		err = RemoveExpiredSuppressionHotTag(testRoomID)
		assert.NoError(err)
	})

	t.Run("永不过期", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		err := service.Redis.ZAdd(key, &redis.Z{
			Score:  -1,
			Member: testRoomID,
		}).Err()
		require.NoError(err)

		err = RemoveExpiredSuppressionHotTag(testRoomID)
		require.NoError(err)

		expireTime, err := service.Redis.ZScore(key, strconv.FormatInt(testRoomID, 10)).Result()
		require.NoError(err)
		assert.EqualValues(-1, expireTime)
	})

	t.Run("未过期", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		setTime := goutil.TimeNow().Add(time.Hour)
		key := keys.KeyRoomsSuppressionHotList0.Format()
		err := service.Redis.ZAdd(key, &redis.Z{
			Score:  float64(setTime.Unix()),
			Member: testRoomID,
		}).Err()
		require.NoError(err)

		err = RemoveExpiredSuppressionHotTag(testRoomID)
		require.NoError(err)

		expireTime, err := service.Redis.ZScore(key, strconv.FormatInt(testRoomID, 10)).Result()
		require.NoError(err)
		assert.EqualValues(setTime.Unix(), expireTime)
	})

	t.Run("本次直播有效", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		err := service.Redis.ZAdd(key, &redis.Z{
			Score:  0,
			Member: testRoomID,
		}).Err()
		require.NoError(err)

		err = RemoveExpiredSuppressionHotTag(testRoomID)
		require.NoError(err)

		err = service.Redis.ZScore(key, strconv.FormatInt(testRoomID, 10)).Err()
		assert.True(serviceredis.IsRedisNil(err))
	})

	t.Run("已过期", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		setTime := goutil.TimeNow().Add(-time.Hour)
		key := keys.KeyRoomsSuppressionHotList0.Format()
		err := service.Redis.ZAdd(key, &redis.Z{
			Score:  float64(setTime.Unix()),
			Member: testRoomID,
		}).Err()
		require.NoError(err)

		err = RemoveExpiredSuppressionHotTag(testRoomID)
		require.NoError(err)

		err = service.Redis.ZScore(key, strconv.FormatInt(testRoomID, 10)).Err()
		assert.True(serviceredis.IsRedisNil(err))
	})
}

func TestRandomOpenList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有房间信息
	rooms, err := RandomOpenList(bson.M{"creator_id": 999999999}, 6)
	require.NoError(err)
	require.NotNil(rooms)
	assert.Len(rooms, 0)

	// 测试获取房间信息
	rooms, err = RandomOpenList(bson.M{"status.open": StatusOpenTrue}, 6)
	require.NoError(err)
	assert.GreaterOrEqual(len(rooms), 1)
}

func TestListRandomTopScoreRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 生成房间数据
	roomList := make([]interface{}, 5)
	roomIDs := make([]int64, 5)
	for i := 0; i < 5; i++ {
		roomID := int64(20000 + i)
		roomIDs[i] = roomID
		roomList[i] = Room{
			Helper: Helper{
				RoomID:    roomID,
				Name:      "测试热门直播间",
				NameClean: fmt.Sprintf("测试热门直播间 %d", 20000+i),
				Status:    Status{Open: StatusOpenTrue, Score: float64(i)},
			},
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 删除测试数据
	_, err := Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, roomList)
	require.NoError(err)

	// 测试没有房间列表
	rooms, err := ListRandomTopScoreRooms(bson.M{"room_id": 99999999, "status.open": StatusOpenTrue},
		0, 50, 5, nil)
	require.NoError(err)
	assert.NotNil(rooms)
	assert.Empty(rooms)

	// 测试获取房间列表
	rooms, err = ListRandomTopScoreRooms(bson.M{"room_id": bson.M{"$in": roomIDs}, "status.open": StatusOpenTrue},
		3, 50, 50, &FindOptions{FindCreator: true,
			Projection: bson.M{
				"room_id":          1,
				"name":             1,
				"creator_id":       1,
				"creator_username": 1,
				"cover":            1,
				"background":       1,
			}})
	require.NoError(err)
	assert.Len(rooms, 2)
	assert.Contains([]int64{20000, 20001}, rooms[0].RoomID)
	assert.Contains([]int64{20000, 20001}, rooms[1].RoomID)

	// 删除测试数据
	_, err = Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}})
	require.NoError(err)
}
