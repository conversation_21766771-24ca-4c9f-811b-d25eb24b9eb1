package room

// 受限房间
const (
	LimitTypeNormalGift = iota + 1 // 礼物房 https://info.missevan.com/pages/viewpage.action?pageId=36284478
	LimitTypePrivacy               // 测试用隐私直播间 https://info.missevan.com/pages/viewpage.action?pageId=109696235
)

// 用户在房间的受限状态
const (
	LimitStatusNormal            = iota // 普通用户
	LimitStatusBlocked                  // 禁止访问
	LimitStatusNormalGiftAllowed        // 礼物房白名单用户
	LimitStatusPrivacyAllowed           // 测试直播间白名单用户
)

// Limit 房间受限信息
type Limit struct {
	Type           int     `bson:"type" json:"type"`
	AllowedUserIDs []int64 `bson:"allowed_user_ids" json:"allowed_user_ids"`
}

// Status 用户受限状态
func (l Limit) Status(userID int64) int {
	switch l.Type {
	case LimitTypeNormalGift:
		for i := range l.AllowedUserIDs {
			if l.AllowedUserIDs[i] == userID {
				return LimitStatusNormalGiftAllowed
			}
		}
	case LimitTypePrivacy:
		for i := range l.AllowedUserIDs {
			if l.AllowedUserIDs[i] == userID {
				return LimitStatusPrivacyAllowed
			}
		}
	}
	return LimitStatusBlocked
}
