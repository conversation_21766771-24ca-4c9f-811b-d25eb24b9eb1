package room

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Config rooms.config
type Config struct {
	DisableJoinQueue    bool          `bson:"disable_join_queue,omitempty" json:"disable_join_queue,omitempty"`
	DisableUserHistory  bool          `bson:"disable_user_history,omitempty" json:"disable_user_history,omitempty"`
	WebSocketURLs       []string      `bson:"websocket_urls,omitempty" json:"websocket_urls,omitempty"`
	RankSyncRevenue     bool          `bson:"rank_sync_revenue,omitempty" json:"rank_sync_revenue,omitempty"`
	FilterGiftMessage   bool          `bson:"filter_gift_message,omitempty" json:"filter_gift_message,omitempty"`
	MessageLimit        *MessageLimit `bson:"message_limit,omitempty" json:"message_limit,omitempty"`
	Popularity          int           `bson:"popularity,omitempty" json:"popularity,omitempty"`
	AllowHideGiftEffect bool          `bson:"allow_hide_gift_effect,omitempty" json:"allow_hide_gift_effect,omitempty"`
}

// MessageLimit 消息限制
type MessageLimit struct {
	Rate util.Coin `bson:"rate,omitempty" json:"rate,omitempty"` // 消息速率限制
}

// MessageLimit 消息限制
func (r *Room) MessageLimit() *MessageLimit {
	if r.Config == nil || r.Config.MessageLimit == nil {
		return nil
	}
	return r.Config.MessageLimit
}

// IsIgnore 是否忽略当前消息
func (ml *MessageLimit) IsIgnore() bool {
	return ml != nil && ml.Rate > 0 && !ml.Rate.Flip()
}

// WebSocketURLs 获取 websocket 地址
func (r *Room) WebSocketURLs() []string {
	if r.Config != nil && len(r.Config.WebSocketURLs) > 0 {
		// 优先使用数据库中配置的 websocket 地址
		return r.Config.WebSocketURLs
	}

	ws := make([]string, len(config.Conf.Params.LiveURL.WebSocket))
	for i := range config.Conf.Params.LiveURL.WebSocket {
		ws[i] = goutil.FormatMessage(config.Conf.Params.LiveURL.WebSocket[i],
			map[string]string{"room_id": strconv.FormatInt(r.RoomID, 10)})
	}
	return ws
}
