//go:build !release
// +build !release

package room

const (
	// TestLimitedRoomID 受限开播房间号
	TestLimitedRoomID int64 = 186192636

	// TestLimitedRoomCreatorID 受限房间主播 ID
	TestLimitedRoomCreatorID int64 = 186192636

	// TestExistsRoomID 存在的房间 ID
	TestExistsRoomID int64 = 100000005

	// TestNonexistentRoomID 不存在的房间 ID
	TestNonexistentRoomID int64 = 404004

	// TestClosedRoomID 关播状态的房间 ID
	TestClosedRoomID int64 = 65150486
)
