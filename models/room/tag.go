package room

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ContainsTag 是否包含指定标签
func (r *Room) ContainsTag(tagID int64) bool {
	return goutil.HasElem(r.TagIDs, tagID)
}

// AddTag 添加指定标签
func AddTag(roomIDs []int64, tagID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$addToSet": bson.M{"tag_ids": tagID}})
	if err != nil {
		return err
	}
	return nil
}

// RemoveTag 移除指定标签
func RemoveTag(roomIDs []int64, tagID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$pull": bson.M{"tag_ids": tagID}},
	)
	if err != nil {
		return err
	}
	return nil
}
