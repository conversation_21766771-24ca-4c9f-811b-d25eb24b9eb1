package room

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGashaponTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(GashaponEnergy{}, "value", "last_notify_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(energyMessage{}, "pool_id", "room_id", "value")
	kc.Check(gashaponIMMessage{}, "type", "event", "room_id", "energy", "buff",
		"default_icon_url", "open_url")
}

func TestGashaponEnergyAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.DatabusPub.ClearDebugPubMsgs()
	defer service.DatabusPub.ClearDebugPubMsgs()
	GashaponEnergyAdd(3, 1, 10)
	m := <-service.DatabusPub.DebugPubMsgs()
	require.Equal(keys.KeyGashaponBuff0.Format(), m.Key)

	var em energyMessage
	require.NoError(json.Unmarshal(m.Value, &em))
	assert.EqualValues(1, em.RoomID)
	assert.Equal(10, em.Value)
}

func TestGashaponOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var broadcastCount int

	require.NoError(service.LRURedis.Del(keys.KeyParams1.Format(params.KeyGashapon)).Err())
	gashaponCfg := params.Gashapon{
		MaxEnergy: 100,
	}
	err := service.LRURedis.Set(keys.KeyParams1.Format(params.KeyGashapon),
		tutil.SprintJSON(gashaponCfg), 10*time.Second).Err()
	require.NoError(err)
	gashaponCfg, err = params.FindGashapon()
	require.NoError(err)
	assert.NotZero(gashaponCfg.MaxEnergy)
	cancel := mrpc.SetMock("im://broadcast/many", func(i interface{}) (interface{}, error) {
		var elems []*userapi.BroadcastElem
		err := json.Unmarshal(i.(json.RawMessage), &elems)
		require.NoError(err)
		require.NotEmpty(elems)
		broadcastCount++

		energyElem := elems[0]
		assert.Equal(liveim.IMMessageTypeNormal, energyElem.Type)
		assert.NotNil(energyElem.Payload)
		payloadData, err := json.Marshal(energyElem.Payload)
		require.NoError(err)
		var payload gashaponIMMessage
		require.NoError(json.Unmarshal(payloadData, &payload))
		energy := payload.Energy
		assert.LessOrEqual(energy.Value, gashaponCfg.MaxEnergy)

		if len(elems) > 1 {
			giftBuffElem := elems[1]
			require.NotNil(giftBuffElem)
			assert.Equal(liveim.IMMessageTypeAll, giftBuffElem.Type)
			assert.NotNil(giftBuffElem.Payload)
			payloadData, err = json.Marshal(giftBuffElem.Payload)
			require.NoError(err)
			var payload notifymessages.General
			require.NoError(json.Unmarshal(payloadData, &payload))
			require.NotNil(payload)
			assert.Equal(liveim.TypeNotify, payload.IMType)
			assert.Equal(liveim.TypeMessage, payload.NotifyType)
			assert.Equal(liveim.EventNew, payload.Event)
		}
		return nil, nil
	})
	defer cancel()

	f := GashaponOperator()
	require.NotNil(f)
	assert.NotPanics(func() { f(&databus.Message{}) })

	roomID := TestLimitedRoomID
	r, err := Update(roomID, bson.M{"energy": GashaponEnergy{}})
	require.NoError(err)
	require.NotNil(r)
	em := energyMessage{
		PoolID: 3,
		RoomID: roomID,
		Value:  1,
	}

	f(&databus.Message{
		Key:   keys.KeyGashaponBuff0.Format(),
		Value: json.RawMessage(tutil.SprintJSON(em)),
	})
	assert.Equal(1, broadcastCount)
	r, err = Find(roomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	beforeEnergy := r.Energy
	assert.NotZero(beforeEnergy.LastNotifyTime)
	assert.Equal(em.Value, beforeEnergy.Value)
	// 第二次，能量值不足 buff
	f(&databus.Message{
		Key:   keys.KeyGashaponBuff0.Format(),
		Value: json.RawMessage(tutil.SprintJSON(em)),
	})
	assert.Equal(2, broadcastCount)
	r, err = Find(roomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	assert.Equal(beforeEnergy.LastNotifyTime, r.Energy.LastNotifyTime)
	assert.Equal(em.Value+beforeEnergy.Value, r.Energy.Value)
	// 第三次，正好加 buff
	beforeEnergy = r.Energy
	em.Value = 2*gashaponCfg.MaxEnergy - r.Energy.Value
	f(&databus.Message{
		Key:   keys.KeyGashaponBuff0.Format(),
		Value: json.RawMessage(tutil.SprintJSON(em)),
	})
	assert.Equal(3, broadcastCount)
	r, err = Find(roomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	assert.GreaterOrEqual(r.Energy.LastNotifyTime, beforeEnergy.LastNotifyTime)
	assert.Zero(r.Energy.Value)
	// 第四次，加 buff 并且溢出
	beforeEnergy = r.Energy
	em.Value = gashaponCfg.MaxEnergy + 2
	f(&databus.Message{
		Key:   keys.KeyGashaponBuff0.Format(),
		Value: json.RawMessage(tutil.SprintJSON(em)),
	})
	assert.Equal(4, broadcastCount)
	r, err = Find(roomID, &FindOptions{DisableAll: true})
	require.NoError(err)
	assert.GreaterOrEqual(r.Energy.LastNotifyTime, beforeEnergy.LastNotifyTime)
	assert.Equal(2, r.Energy.Value)
}
