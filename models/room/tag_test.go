package room

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
)

func TestRoom_ContainsTag(t *testing.T) {
	assert := assert.New(t)

	r := &Room{
		Helper: Helper{
			TagIDs: []int64{1},
		},
	}
	assert.True(r.ContainsTag(1))
	assert.False(r.ContainsTag(4))
}

func TestAddTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(223344)
		testTagID  = int64(10000)
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID}, bson.M{"$unset": bson.M{"tag_ids": 1}})
	require.NoError(err)

	err = AddTag([]int64{testRoomID}, testTagID)
	require.NoError(err)
	r, err := FindOne(bson.M{"room_id": testRoomID})
	require.NoError(err)
	require.NotNil(r)
	assert.Contains(r.TagIDs, testTagID)
}

func TestRemoveTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(223344)
		testTagID  = int64(10000)
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID}, bson.M{"$set": bson.M{"tag_ids": []int64{testTagID}}})
	require.NoError(err)

	err = RemoveTag([]int64{testRoomID}, testTagID)
	require.NoError(err)
	r, err := FindOne(bson.M{"room_id": testRoomID})
	require.NoError(err)
	require.NotNil(r)
	assert.NotContains(r.TagIDs, testTagID)
}
