package room

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/google/uuid"
	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/liveroomtagrecord"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/livestatistics"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CollectionName collection name without prefix
const CollectionName = "rooms"

// 直播间类型
const (
	TypeLive    = "live"    // 高清直播间
	TypeConnect = "connect" // 连麦直播间
)

// 房间开播状态
const (
	StatusOpenFalse int = iota
	StatusOpenTrue
)

// 用于 StatusChannel.Type
const (
	TypeOpen    = "open"
	TypeChannel = "channel"
)

// events
const (
	EventStart = "start"
)

// 推流提供方
const (
	ProviderAgora    = "agora"
	ProviderBvclive  = "bvclive"
	ProviderBililive = "bililive"
	ProviderLocal    = "local"
)

// 房间 PK 状态
const (
	PKStatusOngoing = iota + 1
)

// 主播连线状态
const (
	MultiConnectStatusOngoing = 1
)

// 房间福袋状态
const (
	LuckyBagStatusPending = 1 // 进行中
)

// ImageStatusOffset 图片审核状态预留位
const ImageStatusOffset = 2

// QuestionAppendNum 单次追加提问的数量
const QuestionAppendNum int64 = 50

// QuestionMaxLimit 最大上限
const QuestionMaxLimit int64 = 200

const (
	// NovaRevenueThreshold 新星主播收入限制 10w 钻石（1w RMB）
	NovaRevenueThreshold = 100000
	// NovaTotalDurationThreshold 新星主播开播时长限制 120 小时
	NovaTotalDurationThreshold = 120 * 3600 * 1000
)

// ErrRoomNameExists 直播间名称已存在
var ErrRoomNameExists = errors.New("直播间名称已存在")

// ImageStatus 记录直播间的图片是否是审核中的
// 第一位为 1 说明已上传过封面
// 第二位预留
// 第 livereview.LiveReview.Type + 2 位是 1 则说明这图片是审核中的
type ImageStatus int

// Room document in collection rooms
type Room struct {
	OID    primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Helper `bson:",inline"`

	Members        *Members     `bson:"-" json:"members,omitempty"`
	CoverURL       string       `bson:"-" json:"cover_url,omitempty"`
	ImageStatus    *ImageStatus `bson:"-" json:"image_status,omitempty"`
	CreatorIconURL string       `bson:"-" json:"creator_iconurl,omitempty"`
	NameReviewing  bool         `bson:"-" json:"name_reviewing,omitempty"`

	// 直播间预览页使用
	CatalogName  string         `bson:"-" json:"catalog_name,omitempty"`
	CatalogColor string         `bson:"-" json:"catalog_color,omitempty"`
	CustomTag    *tag.CustomTag `bson:"-" json:"custom_tag,omitempty"`
	PreviewTag   string         `bson:"-" json:"preview_tag,omitempty"`
	PreviewIntro *PreviewIntro  `bson:"-" json:"preview_intro,omitempty"`
}

// PreviewTrace 预览页埋点结构
type PreviewTrace struct {
	PreviewTags       []string `bson:"preview_tags" json:"preview_tags,omitempty"`
	PreviewIntroTitle string   `bson:"preview_intro_title" json:"preview_intro_title,omitempty"`
}

// Helper for Room
type Helper struct {
	RoomID          int64       `bson:"room_id" json:"room_id"`
	CatalogID       int64       `bson:"catalog_id" json:"catalog_id"`
	CustomTagID     int64       `bson:"custom_tag_id" json:"custom_tag_id,omitempty"`
	TagIDs          []int64     `bson:"tag_ids,omitempty" json:"tag_ids,omitempty"`
	Name            string      `bson:"name" json:"name"`
	NameClean       string      `bson:"name_clean" json:"-"`
	Announcement    string      `bson:"announcement" json:"announcement"`
	Type            string      `bson:"type" json:"type"`
	Channel         Channel     `bson:"channel" json:"channel"`
	Cover           *string     `bson:"cover" json:"-"`
	Background      *Background `bson:"background" json:"background,omitempty"`
	GuildID         int64       `bson:"guild_id" json:"-"`
	CreatorID       int64       `bson:"creator_id" json:"creator_id"`
	CreatorUsername string      `bson:"creator_username" json:"creator_username"`
	Status          Status      `bson:"status" json:"status"`
	Notice          *string     `bson:"notice" json:"notice"`
	Medal           *Medal      `bson:"medal" json:"medal,omitempty"`
	Statistics      Statistics  `bson:"statistics" json:"statistics"`
	CreatedTime     time.Time   `bson:"created_time" json:"-"`
	UpdatedTime     time.Time   `bson:"updated_time" json:"-"`
	Connect         Connect     `bson:"connect" json:"connect"`
	Question        Question    `bson:"question" json:"question"`

	SetCatalogID *int64 `bson:"set_catalog_id" json:"-"` // 用于分区迁移 关播后设置成新的分区

	ActivityCatalogID int64 `bson:"activity_catalog_id" json:"activity_catalog_id"` // 用于活动记录报名资格

	Limit *Limit `bson:"limit,omitempty" json:"-"` // 房间受限信息，比如礼物房

	Energy GashaponEnergy `bson:"energy,omitempty" json:"-"`

	Config *Config `bson:"config,omitempty" json:"-"`
}

// user interface
// TODO: 实验性质
type user interface {
	UserID() int64
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// OpenListExcludeRoomIDs 不在开播列表的账号
func OpenListExcludeRoomIDs() []int64 {
	return []int64{
		10652247, // 猫耳娘的零钱袋
		11107978, // 测试房间
		11120002, // 测试房间
	}
}

// FindRoomID 通过主播查询房间号
func FindRoomID(creatorID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	var r struct {
		RoomID int64 `bson:"room_id"`
	}
	err := collection.FindOne(ctx, bson.M{"creator_id": creatorID}, options.FindOne().SetProjection(bson.M{"room_id": 1})).Decode(&r)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return 0, err
	}
	return r.RoomID, nil
}

// FindPushingRoom 获得某个平台正在推流的房间，限制返回的房间个数最多为 maxCount
func FindPushingRoom(platform string, maxCount int64) ([]*Room, error) {
	pipeline := []bson.M{
		{
			// 用了 status.open 索引
			"$match": bson.M{
				"channel.provider":     platform,
				"status.open":          StatusOpenTrue,
				"status.channel.type":  bson.M{"$ne": TypeOpen},
				"status.channel.event": EventStart,
			},
		}, {
			"$sample": bson.M{"size": maxCount},
		},
	}
	collection := service.MongoDB.Collection(CollectionName)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*Room
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindCreatorID 通过房间号查询主播 ID
func FindCreatorID(roomID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	var r struct {
		CreatorID int64 `bson:"creator_id"`
	}
	err := collection.FindOne(ctx, bson.M{"room_id": roomID},
		options.FindOne().SetProjection(bson.M{"creator_id": 1})).Decode(&r)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return 0, err
	}
	return r.CreatorID, nil
}

// Find 寻找房间号 roomID 的 Room
// 只有第一个 opt 会被采用，opt[0] == nil 则认为是默认配置
func Find(roomID int64, opts ...*FindOptions) (*Room, error) {
	return FindOne(bson.M{"room_id": roomID}, opts...)
}

// FindOne 通用的查询房间的接口
func FindOne(filter interface{}, opts ...*FindOptions) (*Room, error) {
	opt := getOptions(opts)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	r := new(Room)
	mongoOpt := options.FindOne()
	if opt.Projection != nil {
		mongoOpt = mongoOpt.SetProjection(opt.Projection)
	}
	err := collection.FindOne(ctx, filter, mongoOpt).Decode(r)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			err = nil
		}
		return nil, err
	}
	findHelper(r, opt)
	r.AfterFind()
	return r, nil
}

// FindAll 通用的查询多个房间
func FindAll(filter interface{}, opts ...*options.FindOptions) ([]*Room, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	collection := service.MongoDB.Collection(CollectionName)
	res, err := collection.Find(ctx, filter, opts...)
	if err != nil {
		return nil, err
	}

	defer res.Close(ctx)

	var r []*Room
	err = res.All(ctx, &r)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(r); i++ {
		r[i].AfterFind()
	}
	return r, nil
}

// findHelper 辅助 Find 用
func findHelper(r *Room, opt *FindOptions) {
	if opt.DisableAll {
		return
	}
	if r.Status.Open != StatusOpenFalse {
		// 房间开启才查找统计人数
		r.findNumOfPeople((r.CreatorID == opt.ListenerID && opt.CreatorFindOnline) || opt.FindOnline, opt.ClientIP, nil)
	}
	if opt.FindFans {
		r.findAttention(opt.ListenerID)
	}
	if opt.FindReviewing && r.CreatorID == opt.ListenerID {
		r.findReviewing()
	}
	if opt.FindCreator {
		r.findCreator()
	}
	if opt.FindPendant {
		r.findPendant()
	}
	if opt.FindCatalogInfo {
		r.findCatalogInfo()
	}
	if opt.FindCustomTag {
		r.findCustomTag()
	}
}

// findNumOfPeople 查询在线人数并缩放累计人数和在线人数
// 如果 meta == nil 则在数据库中查询，否则使用传入的 meta 作为参数
func (r *Room) findNumOfPeople(findOnline bool, clientIP string, meta *livemeta.Simple) {
	if findOnline {
		// WORKAROUND: 不使用 controllers 下的 package 防止循环调用
		var onlineResp struct {
			RoomID int64 `json:"room_id"`
			Count  int64 `json:"count"`
		}
		// TODO: 移动到 userapi 下面
		err := service.MRPC.Call("im://online/count", clientIP,
			handler.M{"room_id": r.RoomID}, &onlineResp)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		r.Statistics.Online = max(r.Statistics.Online, onlineResp.Count)
	}

	// TODO: 支持没有 statistics meta 的查询
	if meta == nil {
		var err error
		meta, err = livemeta.FindSimple(r.RoomID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	r.Statistics.ScaleNumberOfPeople(meta)
}

// findAttention 查询粉丝数
func (r *Room) findAttention(listenerID int64) {
	if listenerID == r.CreatorID {
		listenerID = 0
		// 如果是房主，则将 listenerID 置 0，只查询粉丝数
	}
	res, err := attentionuser.CheckAttention(listenerID, []int64{r.CreatorID})
	if err != nil {
		logger.Error(err)
		return
	}
	if len(res) > 0 {
		r.Statistics.AttentionCount = res[0].FansNum
		r.Statistics.Attention = res[0].Followed
		r.Status.Attention = res[0].Followed
	} else {
		logger.WithField("room_id", r.RoomID).Error("the room's creator isn't found in table mowangskuser")
	}
}

// findReviewing 查找审核中的图片
func (r *Room) findReviewing() {
	reviews, err := livereview.FindRoomReviewing(r.RoomID)
	if err != nil {
		logger.Error(err)
		return
	}
	imageStatus := ImageStatus(0)
	if reviews[livereview.TypeCover] != nil {
		imageStatus |= (1 << (livereview.TypeCover + ImageStatusOffset))
		r.Cover = &reviews[livereview.TypeCover].ImageURL
	}
	if r.Cover != nil {
		imageStatus |= 1
	}
	if image := reviews[livereview.TypeBackground]; image != nil {
		imageStatus |= (1 << (livereview.TypeBackground + ImageStatusOffset))
		if r.Background == nil {
			r.Background = &Background{Enable: true}
		}
		if image.Opacity == nil {
			image.Opacity = new(float64)
			*image.Opacity = 1.0
		}
		r.Background.Opacity = *image.Opacity
		r.Background.Image = image.ImageURL
		if r.Background.Opacity < 0.0 || r.Background.Opacity > 1.0 {
			r.Background.Opacity = 1.0
		}
	}
	r.ImageStatus = &imageStatus
	if info := reviews[livereview.TypeName]; info != nil {
		r.Name = info.Name
		r.NameReviewing = true
	}
}

// findCreator 查询主播信息
func (r *Room) findCreator() {
	r.CreatorIconURL = service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
	u, err := mowangskuser.FindByUserID(r.CreatorID)
	if err != nil {
		logger.Error(err)
		return
	}
	if u == nil {
		return
	}
	r.CreatorIconURL = u.IconURL
}

// findPendant 查询直播间挂件
func (r *Room) findPendant() {
	ua, err := userappearance.FindWornAppearance(r.CreatorID, appearance.TypeBackgroundPendant)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	r.SetPendant(ua)
}

// findCatalogInfo 查询直播间分区信息
func (r *Room) findCatalogInfo() {
	subMap, err := catalog.AllLiveCatalogsWithSubMap(false)
	if err != nil {
		logger.Error(err)
		return
	}
	if ca, ok := subMap[r.CatalogID]; ok {
		r.CatalogName = ca.CatalogName
		r.CatalogColor = ca.Color
	}
}

// findCustomTag 查询直播间个性词条
func (r *Room) findCustomTag() {
	tagMap, err := tag.AllShowCustomTagsMap()
	if err != nil {
		logger.Error(err)
		return
	}
	t, ok := tagMap[r.CustomTagID]
	if ok {
		r.CustomTag = &tag.CustomTag{
			TagID:   t.ID,
			TagName: t.TagName,
		}
	}
}

// SetPendant 把找到的直播间挂件设置到房间
func (r *Room) SetPendant(ua *userappearance.UserAppearance) {
	if ua == nil {
		if r.Background == nil {
			return
		}
		r.Background.PendantImage = ""
		return
	}
	if r.Background == nil {
		r.Background = &Background{
			PendantImage: ua.Image,
			Opacity:      1.0, // 没有背景时挂件默认不透明
		}
		return
	}
	r.Background.PendantImage = ua.Image
}

// IsBan 查询封禁状态
func (r *Room) IsBan() (baned bool) {
	ban, err := livemeta.FindBanned(r.RoomID)
	if err != nil {
		logger.Error(err)
		return
	}
	return ban != nil
}

// Exists 判断房间是否存在
func Exists(roomID int64) (bool, error) {
	return Exists2(bson.M{"room_id": roomID})
}

// Exists2 根据 filter 判断房间是否存在
func Exists2(filter interface{}) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	err := collection.FindOne(ctx, filter, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// ExistsAll 根据房间 ID 列表判断房间是否都存在
func ExistsAll(roomIDs []int64) (bool, error) {
	filter := bson.M{"room_id": bson.M{"$in": roomIDs}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, err
	}
	return int64(len(roomIDs)) == count, nil
}

// IncrMessageCount 增加房间消息计数
func IncrMessageCount(roomID int64) {
	err := service.Redis.HIncrBy(
		keys.KeyRoomsMeta1.Format(roomID),
		"message_count",
		1).Err()
	if err != nil {
		logger.Error(err)
		return
	}
}

// MessageCount 房间消息计数
func MessageCount(roomID int64) int64 {
	v, err := service.Redis.HGet(
		keys.KeyRoomsMeta1.Format(roomID),
		"message_count").Result()
	if err != nil {
		if err != redis.Nil {
			logger.Error(err)
		}
		return 0
	}
	count, err := strconv.ParseInt(v, 10, 64)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return count
}

// IsPushing 房间是否推流
func (r Room) IsPushing() bool {
	c := &r.Status.Channel
	return c.Type != TypeOpen && c.Event == EventStart
}

// Update 更新直播间，返回更新后的 room
// NOTICE: 注意 updated_time 更新时机
func Update(roomID int64, v interface{}, opts ...*FindOptions) (*Room, error) {
	return UpdateRoom(bson.M{"room_id": roomID}, bson.M{"$set": v}, opts...)
}

// UpdateRoom 更新直播间，返回更新后的 room
// NOTICE: 注意 updated_time 更新时机
func UpdateRoom(filter, set interface{}, opts ...*FindOptions) (*Room, error) {
	// 默认情况下不进行任何额外查询
	opt := &FindOptions{DisableAll: true}
	if len(opts) != 0 && opts[0] != nil {
		opt = opts[0]
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	r := new(Room)
	mongoOpt := options.FindOneAndUpdate().SetReturnDocument(options.After)
	err := collection.FindOneAndUpdate(ctx, filter, set, mongoOpt).Decode(r)
	if err != nil {
		return nil, err
	}
	findHelper(r, opt)
	r.AfterFind()
	return r, nil
}

// UpdateOneRoom 更新直播间 直播间不存在不会创建
// NOTICE: 注意更新 updated_time
func UpdateOneRoom(filter, set interface{}) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	res, err := collection.UpdateOne(ctx, filter, bson.M{"$set": set})
	if err != nil {
		return false, err
	}
	return res.ModifiedCount == 1, nil
}

// SchemeToURL 数据库获取的图片自定义协议地址转成 HTTP 协议地址
// NOTICE: 如果封面没有图会设置成默认封面，背景图和首页图在为空的时候不做任何处理
func (r *Room) SchemeToURL() {
	if r.Cover != nil {
		r.CoverURL = storage.ParseSchemeURL(*r.Cover)
	}
	if r.CoverURL == "" {
		r.CoverURL = service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
	}
	if r.Background != nil {
		r.Background.SchemeToURL()
	}
}

// AfterFind 数据库获取的图片自定义协议地址转成 HTTP 协议地址, 热度取整, 未开播热度为 0，给新星直播间增加新星的 tag
func (r *Room) AfterFind() {
	r.SchemeToURL()
	r.Statistics.LoadScore(&r.Status)
	r.Question.MaxLimit = QuestionMaxLimit
	r.Question.MinPrice = r.Question.CurrentMinPrice()
	if hasNovaTag(r.RoomID, &r.Statistics) {
		// 给新星直播间增加新星的 tag
		r.TagIDs = append(r.TagIDs, tag.TagNova)
	}
	// red_packet 存的是直播间红包数量，如存在红包该字段返回 1 即可
	if r.Status.RedPacket > 1 {
		r.Status.RedPacket = 1
	}
}

// IsAgoraLive 是否是高清模式声网推流
func (h Helper) IsAgoraLive() bool {
	return h.Type == TypeLive && h.Connect.Provider == ProviderAgora
}

// IsOwner  使用 interface 判断是否是房主
// TODO: 实验性质
func (h Helper) IsOwner(u user) bool {
	return u != nil && u.UserID() == h.CreatorID
}

// TrySwitchProvider 尝试修改拉流提供商
func (h *Helper) TrySwitchProvider(isOwner bool) {
}

// UpdateReview 将审核中的信息更新到 mongodb
func UpdateReview(creatorID, uploadTime int64, reviewType int) (after *livereview.LiveReview, err error) {
	isUpdated := false
	var r *Room
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		after, err = livereview.FinishReviewing(creatorID, uploadTime, reviewType, livereview.StatusPassed, tx)
		if err != nil {
			return err
		}
		if after == nil {
			return nil
		}

		// 开始更新 mongodb
		// NOTICE: 由于在 mysql 事务中，更新 mongodb 必须放在 mysql 运行之后
		now := goutil.TimeNow()
		update := bson.M{"updated_time": now}
		// NOTICE: 直播间封面、名称需要额外同步 live 表
		db := tx.Table(live.TableName()).Where("user_id = ?", creatorID)
		switch reviewType {
		case livereview.TypeCover:
			// 同步直播间封面
			err := db.Updates(map[string]interface{}{
				"cover":         after.ImageURL,
				"modified_time": now.Unix(),
			}).Error
			if err != nil {
				return err
			}
			update["cover"], err = storage.RemoveURLScheme(after.ImageURL)
			if err != nil {
				return err
			}
		case livereview.TypeBackground:
			u, err := storage.RemoveURLScheme(after.ImageURL)
			if err != nil {
				return err
			}
			bg := Background{
				Enable: true,
				Image:  u,
			}
			if after.Opacity != nil {
				bg.Opacity = *after.Opacity
			} else {
				bg.Opacity = 1.0
			}
			update["background"] = bg
		case livereview.TypeName:
			// 判断直播间名称是否存在
			exists, err := Exists2(bson.M{"name_clean": util.CleanString(after.Name), "creator_id": bson.M{"$ne": creatorID}})
			if err != nil {
				return err
			}
			if exists {
				return ErrRoomNameExists
			}
			update["name"] = after.Name
			update["name_clean"] = util.CleanString(after.Name)
			// 同步直播间名称
			err = db.Updates(map[string]interface{}{
				"title":         after.Name,
				"modified_time": now.Unix(),
			}).Error
			if err != nil {
				return err
			}
		}

		r, err = Update(after.RoomID, update, &FindOptions{FindPendant: true})
		if err != nil {
			return err
		}
		isUpdated = true
		return nil
	})
	if err == nil && r != nil && isUpdated {
		err1 := r.NotifyUpdate()
		if err1 != nil {
			logger.Error(err1)
			// PASS
		}
	}
	return
}

// updatePayload 广播聊天室信息更新的 payload
type updatePayload struct {
	Type   string `json:"type"`
	Event  string `json:"event"`
	RoomID int64  `json:"room_id"`
	Room   struct {
		RoomID       int64        `json:"room_id"`
		CatalogID    int64        `json:"catalog_id"`
		CustomTagID  int64        `json:"custom_tag_id"`
		Name         string       `json:"name"`
		Announcement string       `json:"announcement"`
		CreatorID    int64        `json:"creator_id"`
		Type         string       `json:"type"`
		Notice       *string      `json:"notice"`
		Background   *bgWithImage `json:"background"`
		CoverURL     string       `json:"cover_url"`
	} `json:"room"`
}

type bgWithImage struct {
	Background
	Image string `json:"image"` // iOS 还在使用
}

// NotifyUpdate 广播更新房间信息成功
func (r Room) NotifyUpdate() error {
	r.SchemeToURL()
	if r.CoverURL == "" {
		r.CoverURL = service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
		user, err := mowangskuser.FindByUserID(r.CreatorID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if user != nil {
			r.CoverURL = user.IconURL
		}
	}
	p := updatePayload{Type: "room", Event: "update", RoomID: r.RoomID}
	p.Room.RoomID = r.RoomID
	p.Room.CatalogID = r.CatalogID
	p.Room.CustomTagID = r.CustomTagID
	p.Room.Name = r.Name
	p.Room.Announcement = r.Announcement
	p.Room.CreatorID = r.CreatorID
	p.Room.Type = r.Type
	p.Room.Notice = r.Notice
	if r.Background != nil {
		p.Room.Background = &bgWithImage{
			Background: *r.Background,
			Image:      r.Background.Image,
		}
		// 处理广播 Background.PendantImageURL 是否下发 avif url
		var broadcastAVIFURL bool
		config.GetAB("broadcast_avif_url", &broadcastAVIFURL)
		if !broadcastAVIFURL {
			pendantImageURLs := strings.Split(p.Room.Background.PendantImageURL, ";")
			p.Room.Background.PendantImageURL = ""
			for _, url := range pendantImageURLs {
				if !strings.HasSuffix(url, ".avif") {
					p.Room.Background.PendantImageURL = url
					break
				}
			}
		}
	}
	p.Room.CoverURL = r.CoverURL
	return userapi.Broadcast(r.RoomID, p)
}

// HaveMedal 判断房间是否有勋章
func HaveMedal(roomID int64) (bool, string) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)

	r := new(Room)
	err := collection.FindOne(ctx, bson.M{"room_id": roomID},
		options.FindOne().SetProjection(bson.M{"_id": 1, "medal": 1})).Decode(r)
	if err != nil {
		if err != mongo.ErrNoDocuments {
			logger.Error(err)
		}
		return false, ""
	}
	if r.HaveMedal() {
		return true, r.Medal.Name
	}
	return false, ""
}

// HaveMedal 判断房间是否开通了粉丝勋章
func (r Room) HaveMedal() bool {
	return r.Medal != nil
}

// MedalExists 判断勋章名称是否存在
// NOTICE: 当勋章名与主播当前生效的名称相同（大小写敏感）或与其他主播已生效的名称相同（大小写不敏感），勋章即为重名已存在
func MedalExists(name string, userID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	r := new(Room)
	err := collection.FindOne(ctx, bson.M{"medal.name_clean": util.CleanString(name)},
		options.FindOne().SetProjection(bson.M{"_id": 1, "medal": 1, "creator_id": 1})).Decode(r)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	// 判断是否与主播自己当前生效的勋章名相同（大小写敏感）
	if userID == r.CreatorID {
		return name == r.Medal.Name, nil
	}

	return true, nil
}

// FindRoomIDs 查询指定条件的房间号
func FindRoomIDs(filter bson.M) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	var rooms []struct {
		RoomID int64 `bson:"room_id"`
	}
	cur, err := collection.Find(ctx, filter,
		options.Find().SetProjection(bson.M{"room_id": 1}))
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &rooms)
	if err != nil {
		return nil, err
	}
	res := make([]int64, len(rooms))
	for i := 0; i < len(res); i++ {
		res[i] = rooms[i].RoomID
	}
	return res, nil
}

// OpenRoomIDs 开播的房间的房间号
func OpenRoomIDs() ([]int64, error) {
	return FindRoomIDs(bson.M{"status.open": StatusOpenTrue})
}

// UpdateCatalogID 修改房间的分区
func UpdateCatalogID(roomID, catalogID int64) error {
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		param := map[string]interface{}{
			"catalog_id":    catalogID,
			"modified_time": goutil.TimeNow().Unix(),
		}
		if err := tx.Table(live.TableName()).Where("room_id = ?", roomID).Updates(param).Error; err != nil {
			return err
		}
		update := bson.M{"catalog_id": catalogID, "updated_time": goutil.TimeNow()}
		if _, err := Update(roomID, update); err != nil {
			return err
		}
		return nil
	})
}

// 大流量主播名单：https://info.missevan.com/pages/viewpage.action?pageId=28281908
var specialCreatorIDs = [...]int64{
	2952796,
	189047,
	189763,
	5439631,
	4019359,
	67494,
	4156723,
	326338,
	4580231,
	11893250,
	492980,
	4197199,
	3684785,
	10001892,
}

// IsSpecialCreator 检查是否为大流量主播
func (r *Room) IsSpecialCreator() bool {
	return r != nil && r.Config != nil && r.Config.Popularity > 0
}

// AddAccumulate 更新累计人数
// NOTICE: 默认房间已开启
func (r *Room) AddAccumulate() {
	r.Statistics.Accumulation++

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	_, err := collection.UpdateOne(ctx, bson.M{"_id": r.OID},
		bson.M{"$inc": bson.M{"statistics.accumulation": 1}})
	if err != nil {
		logger.Error(err)
		return
	}
}

// SyncMySQL 同步 mysql 的 live 表数据
func (r *Room) SyncMySQL() error {
	_, err := live.Save(&live.SaveParams{
		CreatorID: r.CreatorID,
		CatalogID: r.CatalogID,
		Title:     r.Name,
		Intro:     r.Announcement,
		Status:    r.Status.Open,
		Score:     int64(r.Status.Score),
		RoomID:    r.RoomID,
		Username:  r.CreatorUsername,
	})
	return err
}

// ClearRoomCache 清除房间缓存
func ClearRoomCache(roomID int64) {
	err := service.Redis.Del(keys.KeyRoomsRoomID1.Format(roomID)).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// ClearList 清理未回答的提问和连麦列表
func (r *Room) ClearList(c goutil.UserContext) {
	err := livequestion.CancelUnhandledQuestions(r.RoomID, r.CreatorID, c)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	_, err = liveconnect.ClearQueue(r.OID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// Close 关闭房间，不会广播关闭房间消息
// NOTICE: 认为人数已经被缩放过
func (r *Room) Close(cs *CloseStatistics, ctx mrpc.UserContext) error {
	roomOnCloseParam := userapi.RoomOnCloseParam{ // 关播之前记录 OpenLogID
		RoomID:    r.RoomID,
		OpenLogID: r.Status.OpenLogID.Hex(),
	}
	err := r.updateClose(cs)
	if err != nil {
		return err
	}
	err = r.ClearOpenCache()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = livemeta.SetOpen(r.RoomID, false)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = r.removeListenDramaTag(cs.By, cs.ByID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	err = RemoveExpiredSuppressionHotTag(r.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	ClearRoomCache(r.RoomID)
	r.insertStatisticsAnchor()
	// NOTICE: 防止在查询开播时间过长房间时，会导致关播接口超时
	goutil.Go(func() {
		r.logClose(cs)
	})
	err = r.SyncMySQL()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = models.UpdatePlayback(r.RoomID, r.CreatorID, goutil.TimeUnixMilli(cs.openTime).ToTime(),
		goutil.TimeUnixMilli(cs.closeTime).ToTime(), livemeta.FindPlaybackPriority(r.RoomID))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	livelistenlogs.DelKeyRoomOnline(r.RoomID)

	if r.Status.RedPacket > 0 {
		// 查询直播间内可抢或待抢红包
		redPackets, err := redpacket.ListByRoomID(r.RoomID)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Errorf("关播后查询房间内礼物红包失败: %v", err)
			// PASS
		} else {
			for _, redPacket := range redPackets {
				// TODO: 房间内红包数量有上限，此处数量不会太多，后续可以完善为批量更新
				packet, err := redpacket.SetRedPacketExpired(redPacket.OID)
				if err != nil {
					logger.WithField("red_packet_id", redPacket.OID).Error(err)
					// PASS
					continue
				}
				if packet == nil {
					logger.WithField("red_packet_id", redPacket.OID).Warn("乐观锁更新失败")
					// PASS
					continue
				}
				// 红包中剩余礼物退还
				packet.Refund(redpacket.RefundTypeCloseLive)
			}
		}
	}
	// 关播调用 live-interaction RPC 接口同步关播事件
	goutil.Go(func() {
		err = userapi.RoomOnClose(ctx, roomOnCloseParam)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
	})
	return nil
}

// updateClose 关播操作的 mongodb 更新，必要参数从 cs 中获取并计算，同步部分字段到 r *Room
func (r *Room) updateClose(cs *CloseStatistics) error {
	set := bson.M{
		"status.open":             StatusOpenFalse,
		"status.close_time":       cs.closeTime,
		"status.score":            0,
		"status.red_packet":       0,
		"statistics.accumulation": 0,
		"notice":                  nil,
		"top":                     0,
		"updated_time":            cs.updatedTime,
	}
	inc := bson.M{
		"statistics.total_duration": cs.Duration,
	}
	unset := bson.M{
		"status.open_log_id": "",
	}

	if r.SetCatalogID != nil {
		set["catalog_id"] = *r.SetCatalogID
		unset["set_catalog_id"] = 0
	}

	guildID, err := livecontract.IsInGuild(r.CreatorID, 0)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		set["guild_id"] = guildID
		r.GuildID = guildID
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = Collection().UpdateOne(ctx, bson.M{"_id": r.OID}, bson.M{
		"$set":   set,
		"$inc":   inc,
		"$unset": unset,
	})
	if err != nil {
		return err
	}
	r.Status.CloseTime = &cs.closeTime
	r.Status.Open = StatusOpenFalse // 此字段会同步到 mysql
	r.Status.OpenLogID = nil
	r.Statistics.Accumulation = 0
	r.Notice = nil
	r.UpdatedTime = cs.updatedTime

	r.Statistics.TotalDuration += goutil.TimeUnixMilli(cs.Duration)

	cs.BuildFull()
	return nil
}

// removeListenDramaTag 移除听剧标签
func (r *Room) removeListenDramaTag(by string, operatorUserID int64) error {
	if !r.ContainsTag(tag.TagListenDrama) {
		return nil
	}

	err := RemoveTag([]int64{r.RoomID}, tag.TagListenDrama)
	if err != nil {
		return err
	}
	role := liveroomtagrecord.RoleRoomCreator
	if by != OperatorUser {
		role = liveroomtagrecord.RoleAdmin
	}
	// NOTICE: 若为系统定时任务关闭房间，role 为 RoleAdmin，operatorID 为 0
	err = liveroomtagrecord.AddRecords(nil, tag.TagListenDrama, []int64{r.RoomID},
		operatorUserID, role, liveroomtagrecord.OperationRemove)
	if err != nil {
		return err
	}
	return nil
}

// TODO: 使用 status.open_log_id
func (r *Room) logClose(cs *CloseStatistics) {
	avgScore, maxScore, err := livestatistics.FindLiveScore(r.RoomID, util.UnixMilliToTime(cs.openTime), util.UnixMilliToTime(cs.closeTime))
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id":    r.RoomID,
			"start_time": cs.openTime,
			"end_time":   cs.closeTime,
		}).Error(err)
		// PASS: 获取热度失败，不影响关播
	}

	rec := livelog.Record{
		RoomOID:       r.OID,
		RoomID:        r.RoomID,
		CreatorID:     r.CreatorID,
		CatalogID:     r.CatalogID,
		CustomTagID:   r.CustomTagID,
		GuildID:       r.GuildID,
		StartTime:     goutil.TimeUnixMilli(cs.openTime),
		EndTime:       goutil.TimeUnixMilli(cs.closeTime),
		Duration:      cs.Duration,
		Revenue:       *cs.Revenue,
		Accumulation:  cs.Accumulation,
		QuestionCount: cs.QuestionCount,
		MessageCount:  cs.MessageCount,
		NewFansCount:  cs.NewFansCount,
		NewMedalCount: cs.NewMedalCount,
		PaidUserCount: cs.PaidUserCount,
		AvgScore:      avgScore,
		MaxScore:      maxScore,
	}
	err = rec.LogClose()
	if err != nil {
		logger.Error(err)
		return
	}
}

// ClearOpenCache 清空开播统计缓存，主要是 redis 的缓存
func (r *Room) ClearOpenCache() error {
	delKeys := []string{
		keys.KeyRoomsMeta1.Format(r.RoomID),
		keys.KeyRoomsFollowed1.Format(r.RoomID),
		roomsrank.Key(r.RoomID, roomsrank.RankTypeCurrent, goutil.TimeNow()),
		keys.KeyRoomPaidUser2.Format(r.RoomID, r.Status.OpenTime),
	}
	return service.Redis.Del(delKeys...).Err()
}

// ReceiveGift 收到礼物
// giftPrice 是礼物单价
func (r *Room) ReceiveGift(giftNum int, giftPrice int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": r.RoomID}, bson.M{
		"$inc": bson.M{"statistics.gift_count": giftNum, "statistics.revenue": int64(giftNum) * giftPrice},
	})
	return err
}

// ReceiveGashaponGifts 收到扭蛋礼物
func (r *Room) ReceiveGashaponGifts(num int, totalPrice int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": r.RoomID}, bson.M{
		"$inc": bson.M{"statistics.gift_count": num, "statistics.revenue": totalPrice},
	})
	return err
}

// AddSuperFanRevenue 添加超粉收益
func (r *Room) AddSuperFanRevenue(goodsPrice int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": r.RoomID}, bson.M{
		"$inc": bson.M{"statistics.revenue": goodsPrice},
	})
	return err
}

// ReceiveQuestion 完成提问回答
func (r *Room) ReceiveQuestion(questionPrice int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": r.RoomID},
		bson.M{"$inc": bson.M{"statistics.question_count": 1, "statistics.revenue": questionPrice}})
	return err
}

// ReceiveDanmaku 付费弹幕收益统计
func (r *Room) ReceiveDanmaku(price int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": r.RoomID},
		bson.M{"$inc": bson.M{"statistics.revenue": price}})
	return err
}

// ReceiveLuckyBox 宝盒收益统计
func (r *Room) ReceiveLuckyBox(price int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": r.RoomID},
		bson.M{"$inc": bson.M{"statistics.revenue": price}})
	return err
}

// IsMaxQuestionLimit 是否超过追加上限限制
func (r *Room) IsMaxQuestionLimit() bool {
	return r.Question.Limit >= QuestionMaxLimit
}

// AppendQuestionLimit 追加提问上限
func (r *Room) AppendQuestionLimit(limit int64) (bool, error) {
	limit += r.Question.Limit
	if limit > QuestionMaxLimit {
		limit = QuestionMaxLimit
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := Collection().UpdateOne(ctx, bson.M{
		"room_id":        r.RoomID,
		"question.limit": r.Question.Limit,
	}, bson.M{
		"$set": bson.M{"question.limit": limit},
	})
	if err != nil {
		return false, err
	}

	modified := res.ModifiedCount > 0
	if modified {
		r.Question.Limit = limit
		// 清除缓存
		ClearRoomCache(r.RoomID)
		// 发送广播通知
		err = userapi.Broadcast(r.RoomID, map[string]interface{}{
			"type":     liveim.TypeQuestion,
			"event":    liveim.EventSet,
			"room_id":  r.RoomID,
			"question": r.Question,
		})
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return modified, nil
}

// LimitStatus 用户在此房间的受限状态
func (r Room) LimitStatus(userID int64) int {
	if r.CreatorID == 0 {
		panic(fmt.Sprintf("room %d creator_id is 0", r.RoomID))
	}
	if r.Limit == nil || r.CreatorID == userID {
		// 房主不受限制
		return LimitStatusNormal
	}
	return r.Limit.Status(userID)
}

// RandomOpenRoomID 随机获取开播房间的房间 ID
func RandomOpenRoomID(creatorIDs []int64) (int64, error) {
	if len(creatorIDs) == 0 {
		panic("param error")
	}
	filter := []bson.M{
		{"$match": bson.M{
			"creator_id": bson.M{
				"$in": creatorIDs,
			},
			"status.open": StatusOpenTrue,
		}},
		{"$sample": bson.M{"size": 1}},
		{"$project": mongodb.NewProjection("room_id")},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, filter)
	if err != nil {
		return 0, err
	}
	defer cur.Close(ctx)
	var simpleList []Simple
	if err = cur.All(ctx, &simpleList); err != nil {
		return 0, err
	}
	if len(simpleList) == 0 {
		return 0, nil
	}
	return simpleList[0].RoomID, nil
}

// UnsetRoomPKStatus 移除直播间 PK 状态
func UnsetRoomPKStatus(roomIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$unset": bson.M{"status.pk": 0}},
	)
	if err != nil {
		return err
	}
	return nil
}

// UpdateRoomRedPacketNum 更新直播间红包数量
// 递增操作 num 传正数，递减操作 num 传负数
func UpdateRoomRedPacketNum(ctx context.Context, roomID int64, num int) (bool, error) {
	if num == 0 {
		return false, nil
	}

	filter := bson.M{"room_id": roomID}
	if num < 0 {
		filter["status.red_packet"] = bson.M{"$gte": -num}
	}
	result, err := Collection().UpdateOne(ctx,
		filter,
		bson.M{"$inc": bson.M{"status.red_packet": num}},
	)
	if err != nil {
		return false, err
	}
	return result.ModifiedCount > 0, nil
}

// NewConnectID 生成一个新的 connectID
func NewConnectID() string {
	return strings.ReplaceAll(uuid.New().String(), "-", "")
}

// MatchConnectID 关联两个房间的 connect id
func MatchConnectID(ctx context.Context, connectID, provider string, roomIDs [2]int64) error {
	update := bson.M{
		"connect.id":       connectID, // TODO: 后续应该加一个直播 PK 专用的字段，不和用户用的连麦 ID 用一个字段
		"connect.provider": provider,
	}
	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": bson.M{"$in": roomIDs}}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

// AfterPKConnectFinish PK 连麦结束后的操作
func AfterPKConnectFinish(roomIDs []int64) error {
	if len(roomIDs) != 2 {
		return errors.New("unsupported roomIDs param")
	}

	// NOTICE: 查询声网白名单，如不在白名单内，需要将 connect.provider 修改为 bvclive
	pipe := service.Redis.Pipeline()
	roomID1Cmd := pipe.SIsMember(keys.KeyRoomsEnableAgora0.Format(), roomIDs[0])
	roomID2Cmd := pipe.SIsMember(keys.KeyRoomsEnableAgora0.Format(), roomIDs[1])
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	updates := make([]mongo.WriteModel, 2)
	// 重置 connect.id
	set1 := bson.M{
		"connect.id": NewConnectID(),
		"status.pk":  0,
	}
	if ok, err := roomID1Cmd.Result(); err == nil && !ok {
		set1["connect.provider"] = ProviderBvclive
	}
	updates[0] = mongo.NewUpdateOneModel().
		SetFilter(bson.M{"room_id": roomIDs[0]}).
		SetUpdate(bson.M{"$set": set1})
	// 重置 connect.id
	set2 := bson.M{
		"connect.id": NewConnectID(),
		"status.pk":  0,
	}
	if ok, err := roomID2Cmd.Result(); err == nil && !ok {
		set2["connect.provider"] = ProviderBvclive
	}
	updates[1] = mongo.NewUpdateOneModel().
		SetFilter(bson.M{"room_id": roomIDs[1]}).
		SetUpdate(bson.M{"$set": set2})
	_, err = service.MongoDB.Collection(CollectionName).BulkWrite(ctx, updates)
	return err
}

// SaveChannelBVCSK 保存 bvc sk
func (r *Room) SaveChannelBVCSK(sk string) error {
	// TODO: 需要等得 pre-create 重构后，如 BVC 为空则需要保存整个 bvc.info 数据
	if r.Channel.BVC == nil {
		return nil
	}
	if r.Channel.BVC.SK == sk {
		return nil
	}

	r.Channel.BVC.SK = sk
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{"room_id": r.RoomID}, bson.M{"$set": bson.M{"channel.bvc.sk": sk}})
	if err != nil {
		return err
	}
	return nil
}

// FilterGiftMessage 是否开启屏蔽部分礼物相关消息
func (r *Room) FilterGiftMessage() bool {
	return r.Config != nil && r.Config.FilterGiftMessage
}

// IsOpen 是否开播
func (r *Room) IsOpen() bool {
	return r.Status.Open != StatusOpenFalse
}

// IsDailyFirstMsg 判断该用户在该房间是否是当天的第一条消息
func (r *Room) IsDailyFirstMsg(userID int64) bool {
	var (
		now = goutil.TimeNow()

		key   = keys.KeyRoomsRankPointMessageLock2.Format(userID, now.Format(util.TimeFormatYMDWithNoSpace))
		field = fmt.Sprintf("messagelock_%d", r.RoomID)
	)

	pipe := service.Redis.TxPipeline()
	cmd := pipe.HSetNX(key, field, 1)
	liveserviceredis.ExpireAt(pipe, key, now.Add(time.Hour*36))
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return cmd.Val()
}

// IsMultiConnect 是否在主播连线中
func (r *Room) IsMultiConnect() bool {
	return r.Status.MultiConnect == MultiConnectStatusOngoing
}

// UpdateGuildID 更新直播间公会信息
func UpdateGuildID(creatorID int64, guildID int64) error {
	var update bson.M
	if guildID == 0 {
		update = bson.M{"$unset": bson.M{"guild_id": ""}}
	} else {
		update = bson.M{"$set": bson.M{"guild_id": guildID}}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	// NOTICE: 用户加入公会时，可能还未创建直播间，需要忽略更新不存在的情况
	_, err := collection.UpdateOne(ctx, bson.M{"creator_id": creatorID}, update)
	return err
}

// SetGuildID 更新指定主播的直播间公会 ID
func SetGuildID(creatorIDs []int64, guildID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateMany(ctx, bson.M{"creator_id": bson.M{"$in": creatorIDs}},
		bson.M{"$set": bson.M{"guild_id": guildID}})
	if err != nil {
		return err
	}
	return nil
}

// CountByFilter 统计直播间数量
func CountByFilter(filter interface{}) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	count, err := Collection().CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// RemoveExpiredSuppressionHotTag 移除过期的热度限制
func RemoveExpiredSuppressionHotTag(roomID int64) error {
	key := keys.KeyRoomsSuppressionHotList0.Format()
	expiredTime, err := service.Redis.ZScore(key, strconv.FormatInt(roomID, 10)).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return nil
		}
		return err
	}

	if expiredTime == -1 || int64(expiredTime) > goutil.TimeNow().Unix() {
		return nil
	}

	return service.Redis.ZRem(key, roomID).Err()
}

// RandomOpenList 随机获取指定数量开播中的房间列表
func RandomOpenList(filter bson.M, size int64) ([]*Simple, error) {
	pipeline := []bson.M{{
		"$match": filter,
	}, {
		"$sample": bson.M{"size": size},
	}, {
		"$project": SimpleProjection(),
	}}

	rooms, err := AggregateSimples(pipeline, &FindOptions{FindCreator: true})
	if err != nil {
		return nil, err
	}
	if rooms == nil {
		return make([]*Simple, 0), nil
	}
	return rooms, nil
}

// ListRandomTopScoreRooms 获取随机热门房间列表
func ListRandomTopScoreRooms(filter bson.M, skip, limit, size int64, opt *FindOptions) ([]*Simple, error) {
	pipeline := []bson.M{{
		"$match": filter,
	}, {
		"$sort": SortByScore,
	}}
	if skip > 0 {
		pipeline = append(pipeline, bson.M{"$skip": skip})
	}
	if limit > 0 {
		pipeline = append(pipeline, bson.M{"$limit": limit})
	}
	if size > 0 {
		pipeline = append(pipeline, bson.M{"$sample": bson.M{"size": size}})
	}
	var project interface{}
	if opt != nil && opt.Projection != nil {
		project = opt.Projection
	} else {
		project = SimpleProjection()
	}
	pipeline = append(pipeline, bson.M{"$project": project})

	rooms, err := AggregateSimples(pipeline, opt)
	if err != nil {
		return nil, err
	}
	if rooms == nil {
		return make([]*Simple, 0), nil
	}
	return rooms, nil
}

func hasNovaTag(roomID int64, s *Statistics) bool {
	if s == nil {
		return false
	}
	return s.Revenue <= NovaRevenueThreshold &&
		s.TotalDuration <= NovaTotalDurationThreshold &&
		!goutil.HasElem(OpenListExcludeRoomIDs(), roomID)
}
