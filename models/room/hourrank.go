package room

import (
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type newRankPayload struct {
	Type     string `json:"type"`
	Event    string `json:"event"`
	RoomID   int64  `json:"room_id"`
	RankType int    `json:"rank_type"`
	Rank     int64  `json:"rank"`
	RankUp   int64  `json:"rank_up"`
}

// NotifyHourRank 重新下发小时榜的排名，score 代表收礼之后当前用户的分数，prevRank 代表变化前的名次，currentRank 代表变化后的名次
func NotifyHourRank(rc *usersrank.RankChange, r *Room) error {
	// 小时榜没有变动不通知
	if rc == nil {
		return nil
	}

	rankLen := usersrank.RankLen(usersrank.TypeHour)
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow())
	if rc.Rank > rankLen {
		// 未上榜，通知当前用户距离上榜的分数。此时榜单一定已满
		val, err := service.Redis.ZRevRangeWithScores(key, rankLen-1, rankLen-1).Result()
		if err != nil {
			return err
		}
		if len(val) == 0 {
			return nil
		}
		if r.CreatorID != rc.UserID {
			logger.WithFields(logger.Fields{
				"room_creator_id":     r.CreatorID,
				"rank_change_user_id": rc.UserID,
			}).Error("下发小时榜排名时榜单与房间主播号不匹配")
			return nil
		}
		// 大主播不实时广播
		if r.IsSpecialCreator() {
			return nil
		}
		lastRankRevenue := int64(val[0].Score)
		payloads := []newRankPayload{{
			Type:     liveim.TypeCreator,
			Event:    liveim.EventNewRank,
			RoomID:   r.RoomID,
			RankType: usersrank.TypeHour,
			Rank:     0,
			RankUp:   max(lastRankRevenue-rc.Score+1, 1),
		}}
		broadcastHourRank(payloads)
		return nil
	}

	// 用户上榜了，此时 currentRank <= rankMaxLen
	var notifyStart int64
	if rc.Rank == 2 {
		// 需要通知第一名
		notifyStart = 1
	} else {
		notifyStart = rc.Rank
	}
	var notifyEnd int64
	var notifyOutRankRoom bool
	switch {
	case rc.PrevRank == 0 || rc.PrevRank > rankLen:
		notifyOutRankRoom = true
		// 之前未上榜
		notifyEnd = rankLen + 1
	case rc.PrevRank == rankLen:
		notifyOutRankRoom = true
		// 原本是第十名
		notifyEnd = rankLen
	default:
		notifyEnd = rc.PrevRank + 1
	}
	if notifyOutRankRoom {
		// 通知榜外用户的 key 的过期时间为 10 分钟
		_, err := service.Redis.Set(keys.KeyCronNotifyHourRank0.Format(), "1", 10*time.Minute).Result()
		if err != nil {
			logger.Errorf("redis set error: %v", err)
			// PASS
		}
	}
	val, err := service.Redis.ZRevRangeWithScores(key, max(notifyStart-2, 0), notifyEnd-1).Result()
	if err != nil {
		return err
	}
	rankStart := max(notifyStart-1, 1)
	infos := make([]*usersrank.Info, len(val))
	creatorIDs := make([]int64, len(val))
	for i := 0; i < len(val); i++ {
		creatorIDs[i], err = strconv.ParseInt(val[i].Member.(string), 10, 64)
		if err != nil {
			return err
		}
		infos[i] = &usersrank.Info{
			UserID:  creatorIDs[i],
			Rank:    rankStart + int64(i),
			Revenue: int64(val[i].Score),
		}
		if i != 0 {
			infos[i].RankUp = infos[i-1].Revenue - infos[i].Revenue + 1
		}
	}
	if len(infos) == 0 {
		return nil
	}
	if infos[0].Rank == 1 {
		// 第一名总是会被广播
		if len(infos) == 1 {
			infos[0].RankUp = infos[0].Revenue
		} else {
			infos[0].RankUp = infos[0].Revenue - infos[1].Revenue
		}
	} else {
		// infos[0] 仅用于辅助给小时榜上的第一位广播
		creatorIDs = creatorIDs[1:]
	}
	openRooms, err := List(bson.M{"status.open": StatusOpenTrue, "creator_id": bson.M{"$in": creatorIDs}},
		options.Find().SetProjection(bson.M{"room_id": 1, "creator_id": 1, "config": 1}),
		&FindOptions{DisableAll: true})
	if err != nil {
		return err
	}
	roomMaps := goutil.ToMap(openRooms, "CreatorID").(map[int64]*Room)
	payloads := make([]newRankPayload, 0, len(openRooms))
	for i := range infos {
		room := roomMaps[infos[i].UserID]
		// 大主播不实时广播
		if room == nil || room.IsSpecialCreator() {
			continue
		}
		payloads = append(payloads, newRankPayload{
			Type:     liveim.TypeCreator,
			Event:    liveim.EventNewRank,
			RoomID:   room.RoomID,
			RankType: usersrank.TypeHour,
			Rank:     infos[i].Rank,
			RankUp:   infos[i].RankUp,
		})
	}
	broadcastHourRank(payloads)
	return nil
}

func broadcastHourRank(payloads []newRankPayload) {
	if len(payloads) == 0 {
		return
	}
	msg := make([]*userapi.BroadcastElem, len(payloads))
	for i := range payloads {
		msg[i] = &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  payloads[i].RoomID,
			Payload: payloads[i],
		}
	}
	err := userapi.BroadcastMany(msg)
	if err != nil {
		logger.Errorf("小时榜更新推送失败: %v", err)
		return
	}
}
