package room

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMarshalJson(t *testing.T) {
	assert := assert.New(t)
	c := new(Connect)
	v, _ := json.Marshal(c)
	str := string(v)
	assert.NotContains(str, `"join":[]`)
	c.Queue = make([]*liveconnect.LiveConnect, 0)
	v, _ = json.Marshal(c)
	str = string(v)
	assert.Contains(str, `"queue":[]`)
	q := new(Question)
	v, _ = json.Marshal(q)
	str = string(v)
	assert.NotContains(str, `"join":[]`)
	q.Join = make([]*livequestion.LiveQuestion, 0)
	v, _ = json.<PERSON>(q)
	assert.Contains(string(v), `"join":[]`)
}

func TestCurrentMinPrice(t *testing.T) {
	assert := assert.New(t)

	q := Question{MinPrice: 0}
	assert.Equal(uint32(livequestion.MinPrice), q.CurrentMinPrice())

	q.MinPrice = 29
	assert.Equal(uint32(livequestion.MinPrice), q.CurrentMinPrice())

	q.MinPrice = 31
	assert.Equal(uint32(31), q.CurrentMinPrice())
}

func TestSubStructKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Medal{}, "name", "name_clean")
	kc.Check(Background{}, "enable", "opacity", "image", "pendant_image")
	kc.Check(Status{}, "open", "open_question_count", "open_fans_count", "open_medal_count", "open_revenue",
		"open_time", "open_log_id", "close_time", "channel", "score", "pk", "multi_connect", "red_packet")
	kc.Check(StatusChannel{}, "type", "event", "platform", "time")
	kc.Check(Question{}, "min_price", "limit")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Medal{}, "name", "default_frame_url")
	kc.Check(Background{}, "enable", "opacity", "image_url", "pendant_image_url")
	kc.Check(Status{}, "open", "open_revenue", "open_time", "open_log_id",
		"close_time", "channel", "pk", "multi_connect", "red_packet", "lucky_bag", "broadcasting", "attention")
	kc.Check(StatusChannel{}, "type", "event", "platform", "time")
	kc.Check(Question{}, "min_price", "limit", "max_limit", "join")
}

func TestNewMedal(t *testing.T) {
	assert := assert.New(t)

	m := NewMedal("ABc")
	assert.Equal("ABc", m.Name)
	assert.Equal("abc", m.NameClean)
}
