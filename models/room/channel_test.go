package room

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service/cdn/bvc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestChannelTags(t *testing.T) {
	ch := new(Channel)
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(ch, "provider", "setprovider", "url_disabled", "disable_video", "bvc", "custom")
	kc.Check(CustomInfo{}, "rtmp_pull_url", "flv_pull_url", "hls_pull_url", "push_url")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(ch, "flv_pull_url", "hls_pull_url", "rtmp_pull_url", "push_url", "chained_push_url")
	kc.CheckOmitEmpty(ch, "rtmp_pull_url", "push_url", "chained_push_url")
	kc.Check(SetChannelNotify{}, "room_id", "channel", "connect")
}

func TestChannelBuildAuthedChannelURL(t *testing.T) {
	assert := assert.New(t)
	r := new(Room)
	ip := "127.0.0.1"
	r.Channel.Provider = "aliyun"
	r.Channel.FLVPullURL = ""
	r.BuildAuthedChannelURL(0, ip, nil, false, false)
	assert.NotEmpty(r.Channel.FLVPullURL)
	r.Channel.Provider = "ksyun"
	r.BuildAuthedChannelURL(0, ip, nil, true, false)
	assert.True(strings.HasPrefix(r.Channel.PushURL, "rtmp://ks-push"), r.Channel.PushURL)
	r.Channel.URLDisabled.Set(URLDisableRTMP)
	r.Channel.URLDisabled.Set(URLDisableFLV)
	r.Channel.URLDisabled.Set(URLDisableHLS)
	r.BuildAuthedChannelURL(0, ip, nil, false, false)
	assert.Empty(r.Channel.RTMPPullURL)
	assert.Empty(r.Channel.FLVPullURL)
	assert.Empty(r.Channel.HLSPullURL)
	r.BuildAuthedChannelURL(0, ip, nil, false, true)
	assert.NotEmpty(r.Channel.RTMPPullURL)
	assert.NotEmpty(r.Channel.FLVPullURL)
	assert.NotEmpty(r.Channel.HLSPullURL)

	r.Channel.FLVPullURL = ""
	r.Channel.BVC = &bvc.Info{
		FLVPullURL:  "http://testflv/",
		HLSPullURL:  "http://testhls/",
		RTMPPullURL: "rtmp://test/",
		PushURL:     "rtmp://testpush/",
		ChainedPushURL: []string{
			"rtmp://testpush1",
			"rtmp://testpush2",
		},
	}
	r.Channel.Provider = "bvc"
	r.Channel.DisableVideo = false
	r.BuildAuthedChannelURL(0, ip, &goutil.Equipment{OS: goutil.IOS, AppVersion: "6.0.0", FromApp: true}, false, true)
	assert.NotContains(r.Channel.FLVPullURL, "&ptype=1")

	r.Channel.DisableVideo = true
	r.BuildAuthedChannelURL(0, ip, nil, false, true)
	assert.Contains(r.Channel.FLVPullURL, "&ptype=1")
}

func TestBuildCustomURL(t *testing.T) {
	assert := assert.New(t)

	// custom 是 nil
	ch := Channel{
		RTMPPullURL: "test_rtmp",
		FLVPullURL:  "test_flv",
		HLSPullURL:  "test_hls",
		PushURL:     "test_push",
	}
	ch.buildCustomURL(true)
	assert.Equal("test_rtmp", ch.RTMPPullURL)
	assert.Equal("test_flv", ch.FLVPullURL)
	assert.Equal("test_hls", ch.HLSPullURL)
	assert.Equal("test_push", ch.PushURL)

	// 没有配置推流 url
	cus := &CustomInfo{
		RTMPPullURL: "cus_rtmp",
		FLVPullURL:  "cus_flv",
		PushURL:     "",
	}
	ch.Custom = cus
	ch.buildCustomURL(true)
	assert.Equal("cus_rtmp", ch.RTMPPullURL)
	assert.Equal("cus_flv", ch.FLVPullURL)
	assert.Equal("test_hls", ch.HLSPullURL)
	assert.Equal("test_push", ch.PushURL)

	ch.Custom.PushURL = "custom_push"
	ch.ChainedPushURL = make([]string, 0)
	ch.buildCustomURL(true)
	assert.Equal("custom_push", ch.PushURL)
	assert.Nil(ch.ChainedPushURL)
}

func TestRemoveURLPtype(t *testing.T) {
	assert := assert.New(t)

	url := "test_hlv?sing=xxx"
	assert.Equal(url, removeURLPtype(url))
	assert.Equal(url, removeURLPtype(url+"&ptype=1"))
}
