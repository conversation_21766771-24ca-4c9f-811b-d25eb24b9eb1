package room

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestConfigTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Config{}, "disable_join_queue", "disable_user_history", "websocket_urls",
		"rank_sync_revenue", "filter_gift_message", "message_limit", "popularity", "allow_hide_gift_effect")
}

func TestRoom_MessageLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(223344)
	)

	r := &Room{
		Helper: Helper{
			RoomID: testRoomID,
			Config: &Config{
				MessageLimit: &MessageLimit{
					Rate: 0.1,
				},
			},
		},
	}
	ml := r.MessageLimit()
	require.NotNil(ml)
	assert.Equal(util.Coin(0.1), ml.Rate)

	r = &Room{
		Helper: Helper{
			RoomID: testRoomID,
		},
	}
	ml = r.MessageLimit()
	assert.Nil(ml)
}

func TestMessageLimit_IsIgnore(t *testing.T) {
	assert := assert.New(t)

	var ml *MessageLimit
	assert.False(ml.IsIgnore())

	ml = &MessageLimit{}
	assert.False(ml.IsIgnore())

	ml = &MessageLimit{
		Rate: 1,
	}
	assert.False(ml.IsIgnore())

	ml = &MessageLimit{
		Rate: 0.00000001,
	}
	assert.True(ml.IsIgnore())
}

func TestRoom_WebSocketURLs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(1)
	r := &Room{
		Helper: Helper{RoomID: testRoomID, CreatorID: testRoomCreatorID},
	}

	ws := r.WebSocketURLs()
	require.NotEmpty(ws)
	assert.Equal(fmt.Sprintf("wss://fm.example.com:3016/ws?room_id=%d", testRoomID), ws[0])

	r.Config = &Config{
		WebSocketURLs: []string{"wss://fm.example.com:3016/ws", "wss://fm.example.com:3016/ws?room_id=1"},
	}
	ws = r.WebSocketURLs()
	assert.Equal("wss://fm.example.com:3016/ws", ws[0])
	assert.Equal("wss://fm.example.com:3016/ws?room_id=1", ws[1])
}
