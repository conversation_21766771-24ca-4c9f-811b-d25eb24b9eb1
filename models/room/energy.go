package room

import (
	"encoding/json"
	"html"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// gashaponGiftMultiMessage 默认大奖翻倍飘屏文案
const (
	gashaponGiftMultiMessage = `<font color="${normal_color}">${creator_username}的直播间触发</font>` +
		`<font color="${highlight_color}">${gift_name} ${multiplier} 倍</font>` +
		`<font color="${normal_color}">概率，快来试试手气吧~</font>`
)

// GashaponEnergy 扭蛋能量值
type GashaponEnergy struct {
	Value          int   `bson:"value" json:"value"`
	LastNotifyTime int64 `bson:"last_notify_time" json:"-"` // 上次能量值通知的时间

	MaxValue int `bson:"-" json:"max_value,omitempty"` // 消息中不需要这个值
}

// energyMessage 能量值消息
type energyMessage struct {
	RoomID int64 `json:"room_id"`
	Value  int   `json:"value"`
	PoolID int64 `json:"pool_id"`
}

// GashaponEnergyAdd 增加扭蛋能量值
func GashaponEnergyAdd(poolID, roomID int64, value int) {
	key := keys.KeyGashaponBuff0.Format()
	err := service.DatabusSend(key, energyMessage{
		PoolID: poolID,
		RoomID: roomID,
		Value:  value})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type gashaponIMMessage struct {
	Type   string             `json:"type"`
	Event  string             `json:"event"`
	RoomID int64              `json:"room_id"`
	Energy GashaponEnergy     `json:"energy"`
	Buff   *gift.GashaponBuff `json:"buff,omitempty"`
	// WORKAROUND: iOS 4.7.0 在没有入口的情况下收到 buff 消息显示有问题，超能魔方可以抽取后移除
	DefaultIconURL string `json:"default_icon_url,omitempty"`
	OpenURL        string `json:"open_url,omitempty"`
}

// GashaponOperator 扭蛋 operator
func GashaponOperator() func(*databus.Message) {
	key := keys.KeyGashaponBuff0.Format()
	return func(m *databus.Message) {
		if !strings.HasPrefix(m.Key, key) {
			return
		}
		// TODO: databus 支持获取批量消息后需要同步更改
		var em energyMessage
		err := json.Unmarshal(m.Value, &em)
		if err != nil {
			logger.Error(err)
			return
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		col := Collection()
		var r Room
		err = col.FindOneAndUpdate(ctx, bson.M{"room_id": em.RoomID},
			bson.M{"$inc": bson.M{"energy.value": em.Value}},
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		).Decode(&r)
		if err != nil {
			if !mongodb.IsNoDocumentsError(err) {
				logger.Error(err)
			}
			return
		}
		e := r.Energy
		now := goutil.TimeNow()
		gashaponCfg, err := params.FindGashapon()
		if err != nil {
			logger.Error(err)
			return
		}
		maxValue := gashaponCfg.MaxEnergy
		if e.Value >= maxValue {
			// 能量值触发 buff 更新
			incValue := -(e.Value / maxValue * maxValue) // 已经为负数
			e.Value += incValue
			ctx, cancel := service.MongoDB.Context()
			defer cancel()
			_, err = col.UpdateOne(ctx, bson.M{"room_id": em.RoomID},
				bson.M{
					"$set": bson.M{"energy.last_notify_time": now.Unix()},
					"$inc": bson.M{"energy.value": incValue},
				})
			if err != nil {
				logger.Error(err)
				return
			}
			gb, err := gift.AddPoolGashaponBuff(em.PoolID, em.RoomID)
			defer broadcastGashapon(em.RoomID, r.CreatorUsername, e, gb) // 即使出错也广播
			if err != nil {
				logger.Error(err)
				return
			}
			return
		}
		// 查询当前存在的 buff
		buff, err := gift.FindGashaponBuff(em.RoomID)
		if err != nil {
			logger.Error(err)
			return
		}
		broadcastGashapon(em.RoomID, r.CreatorUsername, e, buff)
		// 记录广播时间
		ctx, cancel = service.MongoDB.Context()
		defer cancel()
		_, err = col.UpdateOne(ctx, bson.M{"room_id": em.RoomID},
			bson.M{"$set": bson.M{
				"energy.last_notify_time": now.Unix(),
			}})
		if err != nil {
			logger.Error(err)
			return
		}
	}
}

// broadcastGashapon 房间内大奖翻倍消息和大奖翻倍支持飘屏
func broadcastGashapon(roomID int64, creatorUserName string, energy GashaponEnergy, buff *gift.GashaponBuff) {
	msg := make([]*userapi.BroadcastElem, 0, 2)
	defer func() {
		err := userapi.BroadcastMany(msg)
		if err != nil {
			logger.Error(err)
			return
		}
	}()
	// 房间内大奖翻倍消息
	// WORKAROUND: iOS 4.7.0 在没有入口的情况下消息显示有问题，超能魔方可以抽取后移除
	conf, err := params.FindGashapon()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	roomMsg := &gashaponIMMessage{
		Type:           liveim.TypeGashapon,
		Event:          liveim.EventUpdate,
		RoomID:         roomID,
		Energy:         energy,
		Buff:           buff,
		OpenURL:        conf.OpenURL,
		DefaultIconURL: conf.IconURL,
	}
	msg = append(msg, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  roomID,
		Payload: roomMsg,
	})
	if buff == nil {
		return
	}
	// 大奖翻倍支持飘屏
	if buff.Notify {
		const defaultColor = "#FFFFFF"
		format := map[string]string{
			"creator_username": html.EscapeString(creatorUserName),
			"gift_name":        html.EscapeString(buff.Name),
			"multiplier":       buff.Multiplier,
			"highlight_color":  defaultColor,
			"normal_color":     defaultColor,
		}
		b, err := bubble.FindSimple(buff.BubbleID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if b != nil {
			if b.HighlightColor != "" {
				format["highlight_color"] = b.HighlightColor
			}
			if b.NormalColor != "" {
				format["normal_color"] = b.NormalColor
			}
		}
		// 获取飘屏文案
		message := goutil.FormatMessage(gashaponGiftMultiMessage, format)
		msg = append(msg, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeAll,
			RoomID:  roomID,
			Payload: notifymessages.NewGeneral(roomID, message, b),
		})
	}
}
