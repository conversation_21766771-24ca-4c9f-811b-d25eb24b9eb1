package room

// FindOptions 查询选项
// NOTICE：需要保证 FindOptions{} 是查询的默认配置
type FindOptions struct {
	// 查询相关操作

	// TODO: Simple 暂时未接入
	Projection interface{}

	// 后面是查询到数据后的额外选项

	// DisableAll 禁用查询的所有额外操作，比如查询在线人数之类的
	DisableAll bool

	// FindOnline 查询在线人数
	FindOnline bool
	// ClientIP 调用 rpc 需要的客户端 IP
	// 需要此参数的配置：查询在线人数
	ClientIP string

	// FindCreator 查询主播信息
	FindCreator bool

	// FindFans 查询粉丝数，收听者 ID 有的话会判断是否关注
	FindFans bool

	// FindCatalogInfo 查找分区信息
	FindCatalogInfo bool

	// FindReviewing 是否查找审核中的图片，需要收听者 ID
	// 只有收听者是房主才可能返回审核中的图片
	FindReviewing bool

	// 收听者 ID
	// 需要此参数的配置：查询粉丝数，是否查找审核中的图片, 房主查询在线人数
	ListenerID int64

	// 房主查询在线人数
	CreatorFindOnline bool

	// FindPendant 是否查询直播间挂件
	FindPendant bool

	// FindCustomTag 是否查询个性词条
	FindCustomTag bool

	// FindLuckyBag 是否查询福袋状态
	FindLuckyBag bool
}

func getOptions(opt []*FindOptions) *FindOptions {
	if len(opt) != 0 && opt[0] != nil {
		return opt[0]
	}
	return &FindOptions{}
}
