package room

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	muser "github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestStatisticsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Statistics{}, "gift_count", "question_count", "revenue", "point", "accumulation", "total_duration")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CloseNotifier{}, "type", "event", "room_id", "message", "time", "statistics", "status",
		"recommend_max_delay", "by", "recommend", "user")
	kc.CheckOmitEmpty(CloseNotifier{}, "message", "recommend")
	kc.Check(CloseNotifier{}.User, "user_id")
	kc.Check(CloseStatistics{}, "duration", "revenue", "accumulation", "message_count",
		"new_fans_count", "new_medal_count", "paid_user_count")
	kc.Check(Statistics{}, "revenue", "accumulation", "online", "attention", "attention_count", "vip", "score")
}

func TestScaleNumberOfPeople(t *testing.T) {
	assert := assert.New(t)
	defer func() {
		emptyMeta = livemeta.Simple{}
	}()
	emptyMeta.Online = 12
	emptyMeta.Accumulation = 100
	s := new(Statistics)
	s.ScaleNumberOfPeople(nil)
	assert.Equal(int64(12), s.Online)
	assert.Equal(int64(200), s.Accumulation)
}

func TestLoadScore(t *testing.T) {
	assert := assert.New(t)

	s := Statistics{}

	s.LoadScore(nil)
	assert.Zero(s.Score)

	s.LoadScore(&Status{Open: StatusOpenTrue, Score: 1.2})
	assert.Equal(int64(1), s.Score)

	s.LoadScore(&Status{Score: 1.2})
	assert.Zero(s.Score)
}

func TestScale(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(int64(-4), scale(-4, 0.0))
	assert.Equal(int64(10), scale(10, 1.0))
	assert.Equal(int64(2), scale(2, 5.0))
	assert.Greater(scale(20, 1), int64(20))
}

func TestRoomInsertStatisticsAnchor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	var r Room
	require.NoError(col.FindOne(ctx, bson.M{},
		options.FindOne().SetProjection(bson.M{"room_id": 1})).Decode(&r))

	beforeSave := util.TimeToUnixMilli(goutil.TimeNow()) - 1
	r.insertStatisticsAnchor()

	col = service.MongoDB.Collection("live_statistics")
	var rec struct {
		Time int64 `bson:"time"`
	}
	err := col.FindOne(ctx, bson.M{
		"_room_id": r.OID, "online": 0, "accumulation": 0, "vip": 0,
		"display_accumulation": 0, "display_online": 0,
	}, options.FindOne().SetSort(bson.M{"time": -1})).Decode(&rec)
	require.NoError(err)
	assert.Greater(rec.Time, beforeSave)
}

func TestNewCloseStatistics(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := FindOne(bson.M{})
	require.NoError(err)
	require.NotNil(r)
	r.Status.OpenTime = 0
	r.Status.Open = StatusOpenTrue
	r.Statistics.Accumulation = 2
	r.Statistics.Revenue = 10
	r.Statistics.QuestionCount = 10
	r.Status.OpenQuestionCount = 1

	goutil.SetTimeNow(func() time.Time { return time.Unix(10, 0) })
	defer goutil.SetTimeNow(nil)

	cs := NewCloseStatistics(r, "", 10, "test")
	assert.Equal("user", cs.By)
	assert.Equal("test", cs.Message)
	assert.NotNil(r.GuildID, "换成 sqlite 之后可能会测试失败")
	assert.Equal(int64(10000), cs.Duration)
	assert.Equal(r.Statistics.Revenue, *cs.Revenue)
	assert.Equal(r.Statistics.Accumulation, cs.Accumulation)
	assert.Equal(int64(9), cs.QuestionCount)
}

func TestNewCloseNotifier(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/close", true, handler.M{
		"room_id": 114514,
	})
	room := Room{}
	room.RoomID = 123
	cs := NewCloseStatistics(&room, "", 10, "")
	recommend := []*Simple{{RoomID: 114514}}
	notifier := NewCloseNotifier(cs, recommend, c)
	require.NotNil(notifier)
	assert.Equal(cs.room.RoomID, notifier.RoomID)
	assert.Equal(liveim.TypeRoom, notifier.Type)
	assert.Equal(liveim.EventClose, notifier.Event)
	assert.Equal(recommend, notifier.Recommend)
	assert.EqualValues(-1, notifier.RecommendMaxDelay)

	notifier = NewCloseNotifier(cs, make([]*Simple, 0), c)
	assert.NotNil(notifier.Recommend)
	assert.EqualValues(-1, notifier.RecommendMaxDelay)

	online := int64(20000)
	cancel := mrpc.SetMock("im://online/count", func(input interface{}) (output interface{}, err error) {
		return onlineCountResp{Count: online}, nil
	})
	defer cancel()
	notifier = NewCloseNotifier(cs, nil, c)
	assert.Equal(online*1000/2000, notifier.RecommendMaxDelay)

	online = 40000
	notifier = NewCloseNotifier(cs, nil, c)
	assert.Equal(int64(-1), notifier.RecommendMaxDelay)

	r := Room{
		Helper: Helper{
			RoomID:    1919,
			Config:    &Config{Popularity: 1},
			Name:      "Test_Room",
			NameClean: "test_room",
			CreatorID: 1145141919,
		},
	}
	cs.room = &r
	online = 10
	notifier = NewCloseNotifier(cs, nil, c)
	assert.EqualValues(-1, notifier.RecommendMaxDelay)
}

func TestCloseNotifier_Notify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/close", true, handler.M{
		"room_id": 114514,
	})
	room := Room{}
	room.RoomID = 123
	cs := NewCloseStatistics(&room, "", 10, "")
	*cs.Revenue = 100
	notifier := NewCloseNotifier(cs, nil, c)

	var body struct {
		RoomID  int64         `json:"room_id"`
		Payload CloseNotifier `json:"payload"`
	}
	isCalled := false
	cancel := mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		isCalled = true
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		assert.EqualValues(cs.room.RoomID, body.RoomID)
		assert.EqualValues(cs.room.RoomID, body.Payload.RoomID)
		assert.EqualValues(cs.Revenue, body.Payload.Statistics.Revenue)
		assert.Nil(body.Payload.Recommend)
		return "success", nil
	})
	defer cancel()
	notifier.Notify()
	assert.True(isCalled)

	recommend := []*Simple{{RoomID: 114514}}
	notifier.Recommend = recommend
	isCalled = false
	cancel = mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		isCalled = true
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		assert.EqualValues(recommend, body.Payload.Recommend)
		return "success", nil
	})
	defer cancel()
	notifier.Notify()
	assert.True(isCalled)
}

func TestCloseStatistics_BuildFull(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testOpenTime := goutil.TimeNow().Unix()
	chatroom.AddCurrentRoomPaidUser(1234, 9074509, testOpenTime)

	c := CloseStatistics{
		room: &Room{
			Helper: Helper{
				RoomID:    1234,
				CreatorID: 3456864,
				Status: Status{
					OpenFansCount:  1,
					OpenMedalCount: 2,
					OpenTime:       testOpenTime,
				},
			},
		},
	}
	c.BuildFull()

	u, err := muser.FindByUserID(c.room.CreatorID)
	require.NoError(err)
	assert.EqualValues(u.FansNum-1, *c.NewFansCount)
	assert.EqualValues(0, *c.NewMedalCount)
	assert.EqualValues(1, *c.PaidUserCount)
}
