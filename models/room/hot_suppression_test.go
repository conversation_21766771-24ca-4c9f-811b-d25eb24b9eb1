package room

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFilterLiveHotSuppression(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expireDay := goutil.TimeNow().AddDate(0, 0, 1)
	// 测试过滤直播热榜限制的直播间
	err := service.Redis.ZAdd(keys.KeyRoomsSuppressionHotList0.Format(), &redis.Z{
		Member: "1",
		Score:  float64(expireDay.Unix()),
	}).Err()
	require.NoError(err)

	roomIDs := []int64{1, 2, 3, 4, 5}
	expected := []int64{2, 3, 4, 5}
	actual := FilterLiveHotSuppression(roomIDs)
	assert.Equal(expected, actual)

	err = service.Redis.ZRem(keys.KeyRoomsSuppressionHotList0.Format(), "1").Err()
	assert.NoError(err)

	// 测试没有直播热榜限制的直播间
	expected = []int64{1, 2, 3, 4, 5}
	actual = FilterLiveHotSuppression(roomIDs)
	assert.Equal(expected, actual)
}
