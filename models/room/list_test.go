package room

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
)

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := []int64{186192636, 18113499, 223344}

	rooms, err := List(bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, nil, &FindOptions{
		FindOnline:  true,
		FindCreator: true,
		FindFans:    true,
	})
	require.NoError(err)
	require.Len(rooms, 3)
	for _, r := range rooms {
		assert.NotEmpty(r.CreatorIconURL)
		switch r.RoomID {
		case 223344:
			assert.Zero(r.Statistics.AttentionCount)
		case 18113499:
			assert.EqualValues(2, r.Statistics.AttentionCount)
		case 186192636:
			assert.EqualValues(3, r.Statistics.AttentionCount)
		}
	}

	// DisableAll
	rooms, err = List(bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, nil, &FindOptions{
		DisableAll: true,
	})
	require.NoError(err)
	require.Len(rooms, 3)
	for _, r := range rooms {
		assert.Empty(r.CreatorIconURL)
		assert.Zero(r.Statistics.AttentionCount)
	}

	// FindOptions nil
	rooms, err = List(bson.M{
		"room_id": bson.M{"$in": testRoomIDs},
	}, nil, nil)
	require.NoError(err)
	require.Len(rooms, 3)
	for _, r := range rooms {
		assert.Empty(r.CreatorIconURL)
		assert.Zero(r.Statistics.AttentionCount)
	}
}
