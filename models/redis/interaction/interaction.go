package interaction

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

// interaction type
const (
	TypeInteractionVote = "vote"
)

// Find 判断是否有活动在进行中，若有，返回活动类型
func Find(roomID int64) (string, error) {
	key := keys.LockRoomsInteraction1.Format(roomID)
	result, err := service.Redis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		}
		return "", err
	}
	return result, nil
}

// LockOngoing 设置互动进行中记录
func LockOngoing(roomID int64, interactionType string, duration time.Duration) (bool, error) {
	key := keys.LockRoomsInteraction1.Format(roomID)
	return service.Redis.SetNX(key, interactionType, duration).Result()
}

// Expire 设置互动进行中记录过期时间
func Expire(roomID int64, duration time.Duration) error {
	key := keys.LockRoomsInteraction1.Format(roomID)
	return service.Redis.Expire(key, duration).Err()
}

// VoteInfo 投票基本信息
type VoteInfo struct {
	VoteID  primitive.ObjectID `json:"vote_id"`
	GiftIDs []int64            `json:"gift_ids"`
}

// Info 互动基本信息
type Info struct {
	Vote *VoteInfo `json:"vote,omitempty"`
}

// SetOngoingInteractionInfo 设置进行中的投票基本信息
func SetOngoingInteractionInfo(roomID int64, info Info, duration int64) error {
	key := keys.KeyRoomsInteraction1.Format(roomID)
	b, err := json.Marshal(info)
	if err != nil {
		return err
	}

	err = service.Redis.Set(key, string(b), time.Duration(duration)*time.Millisecond).Err()
	if err != nil {
		return err
	}
	return nil
}

// DelOngoingInteractionInfo 投票结束
func DelOngoingInteractionInfo(roomID int64) error {
	return service.Redis.Del(keys.KeyRoomsInteraction1.Format(roomID)).Err()
}

// VoteGiftsOption 投票礼物配置项
type VoteGiftsOption struct {
	GiftID int64  `json:"gift_id"`
	Color  string `json:"color"`
}

// DurationSelect 投票时间配置项
type DurationSelect struct {
	Show  string `json:"show"`
	Value int64  `json:"value"`
}

// VoteDurationOption 投票时间配置项
type VoteDurationOption struct {
	Select  []*DurationSelect `json:"select"`
	Default DurationSelect    `json:"default"`
}

// VoteOption 礼物投票配置项
type VoteOption struct {
	DurationOption   VoteDurationOption `json:"duration_option"`
	GiftOptions      []*VoteGiftsOption `json:"gift_options"`
	AnnounceDuration int64              `json:"announce_duration"`
}

// GiftIDs 获取配置对应的 giftIDs
func (option *VoteOption) GiftIDs() []int64 {
	giftIDs := make([]int64, len(option.GiftOptions))
	for i := range option.GiftOptions {
		giftIDs[i] = option.GiftOptions[i].GiftID
	}
	return giftIDs
}

// Durations 获取配置允许的投票持续时间
func (option *VoteOption) Durations() []int64 {
	durations := make([]int64, len(option.DurationOption.Select))
	for i := range option.DurationOption.Select {
		durations[i] = option.DurationOption.Select[i].Value
	}
	return durations
}

// LockDuration 获取投票包含公示期的加锁时间
func (option *VoteOption) LockDuration(duration int64) time.Duration {
	return time.Duration(duration+option.AnnounceDuration) * time.Millisecond
}

// FindVoteOption 获取礼物投票配置
func FindVoteOption() (*VoteOption, error) {
	cacheKey := keys.KeyVoteOption0.Format()
	result, ok := service.Cache5Min.Get(cacheKey)
	if ok {
		if result != nil {
			res := *result.(*VoteOption) // 复制数据
			return &res, nil
		}
	}

	key := keys.KeyRoomsInteractionVoteConfig0.Format()
	v, err := service.Redis.Get(key).Result()
	if err != nil {
		return nil, fmt.Errorf("获取投票礼物配置错误: %v", err)
	}
	var option VoteOption
	err = json.Unmarshal([]byte(v), &option)
	if err != nil {
		return nil, err
	}
	data := option // 复制数据
	service.Cache5Min.Set(cacheKey, &data, 0)

	return &option, nil
}
