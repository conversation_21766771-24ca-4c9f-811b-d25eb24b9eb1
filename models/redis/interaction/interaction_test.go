package interaction

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(-99999)
	key := keys.LockRoomsInteraction1.Format(roomID)
	service.Redis.Set(key, "test", 5*time.Second)
	r, err := Find(roomID)
	require.NoError(err)
	assert.Equal("test", r)

	roomID = 99999999
	r, err = Find(roomID)
	require.NoError(err)
	assert.Empty(r)
}

func TestLockOngoing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(-11111)
	key := keys.LockRoomsInteraction1.Format(roomID)
	ok, err := LockOngoing(roomID, "test", 5*time.Second)
	require.NoError(err)
	assert.True(ok)
	r, err := service.Redis.Get(key).Result()
	require.NoError(err)
	assert.Equal("test", r)

	ok, err = LockOngoing(roomID, "test", 5*time.Second)
	require.NoError(err)
	assert.False(ok)
}

func TestExpired(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(-22222)
	key := keys.LockRoomsInteraction1.Format(roomID)
	require.NoError(Expire(roomID, 2*time.Second))

	_, err := service.Redis.Set(key, "test", time.Minute).Result()
	require.NoError(err)
	require.NoError(Expire(roomID, 2*time.Second))
	d, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.LessOrEqual(d.Milliseconds(), time.Minute.Milliseconds())
}

func TestSetOngoingInteractionAndDel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(-33333)
	param := Info{
		Vote: &VoteInfo{
			VoteID:  primitive.NewObjectIDFromTimestamp(goutil.TimeNow()),
			GiftIDs: []int64{2, 3, 4},
		}}
	require.NoError(SetOngoingInteractionInfo(roomID, param, 5000))
	key := keys.KeyRoomsInteraction1.Format(roomID)
	result, err := service.Redis.Get(key).Result()
	require.NoError(err)
	assert.NotEmpty(result)

	require.NoError(DelOngoingInteractionInfo(roomID))
	assert.Equal(redis.Nil, service.Redis.Get(key).Err())
}

func TestGiftIDs(t *testing.T) {
	assert := assert.New(t)

	v := VoteOption{
		GiftOptions: []*VoteGiftsOption{
			{GiftID: 1}, {GiftID: 2},
		},
	}
	assert.Equal([]int64{1, 2}, v.GiftIDs())
}

func TestDurations(t *testing.T) {
	assert := assert.New(t)

	v := VoteOption{
		DurationOption: VoteDurationOption{Select: []*DurationSelect{{"1", 1}, {"2", 2}}},
	}
	assert.Equal([]int64{1, 2}, v.Durations())
}

func TestLockDuration(t *testing.T) {
	assert := assert.New(t)

	v := VoteOption{
		AnnounceDuration: 30000,
	}
	assert.Equal(time.Minute, v.LockDuration(30000))
}

func TestFindVoteOption(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyRoomsInteractionVoteConfig0.Format()
	v := &VoteOption{
		DurationOption: VoteDurationOption{
			Select: []*DurationSelect{{"15s", 15000}, {"30s", 30000}, {"60s", 60000},
				{"600s", 600000}},
			Default: DurationSelect{"30s", 30000},
		},
		GiftOptions: []*VoteGiftsOption{
			{GiftID: 2, Color: "#FE929B"},
			{GiftID: 3, Color: "#B6E58B"},
			{GiftID: 4, Color: "#F5CC6E"},
			{GiftID: 5, Color: "#BEB2FF"},
		},
		AnnounceDuration: 10000,
	}
	defer service.Cache5Min.Flush()

	require.NoError(service.Redis.Del(key).Err())
	_, err := FindVoteOption()
	assert.EqualError(err, "获取投票礼物配置错误: redis: nil")

	require.NoError(service.Redis.Set(key, tutil.SprintJSON(v), 0).Err())
	fromDatabase, err := FindVoteOption()
	require.NoError(err)
	assert.NotNil(fromDatabase)
	fromCache, err := FindVoteOption()
	require.NoError(err)
	assert.NotNil(fromCache)
	assert.Equal(fromDatabase, fromCache)
	fromCache.AnnounceDuration++
	assert.NotEqual(fromDatabase, fromCache)
}
