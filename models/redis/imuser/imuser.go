package imuser

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// NewIMConnMember 生成连接在 KeyIMRoomMembers1 中的唯一 member
func NewIMConnMember(accID string, uuid string, valuableUser bool) string {
	return fmt.Sprintf("%s|%s|%d", accID, uuid, goutil.BoolToInt(valuableUser))
}

type connUser struct {
	UserID       int64
	ConnUUID     string
	ValuableUser bool
}

func parseIMConnMember(member string) *connUser {
	s := strings.SplitN(member, "|", 3)
	if len(s) != 3 {
		// 旧版本的情况
		s = strings.SplitN(member, "_", 2)
		if len(s) != 2 {
			return nil
		}
	}
	userID, _ := strconv.ParseInt(s[0], 10, 64)
	cu := &connUser{
		UserID:   userID,
		ConnUUID: s[1],
	}
	cu.ConnUUID = s[1]
	if userID > 0 && len(s) == 3 {
		cu.ValuableUser = s[2] == "1"
	}
	return cu
}

// RefreshUsers 将聊天室内的注册用户和连接同步
// NOTICE: 不同实例、不同线程同时调用可能会导致数据出错
func RefreshUsers(roomID int64, minTimeUnix int64) error {
	zKey := keys.KeyIMRoomMembers1.Format(roomID)
	hKey := keys.KeyIMRoomUsers1.Format(roomID)
	vuKey := keys.IMKeyRoomValuableUsers1.Format(roomID)

	if minTimeUnix > 0 {
		err := service.IMRedis.ZRemRangeByScore(zKey, "-inf", strconv.FormatInt(minTimeUnix, 10)).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	pipe := service.IMRedis.Pipeline()
	membersCmd := pipe.ZRange(zKey, 0, -1)
	pipe.Del(hKey)
	pipe.Del(vuKey)
	_, err := pipe.Exec()
	if err != nil {
		return err
	}
	members, _ := membersCmd.Result()
	usersCount := make(map[int64]int64, len(members))
	valuableUserIDs := make(map[int64]struct{})
	for i := 0; i < len(members); i++ {
		cu := parseIMConnMember(members[i])
		if cu != nil && cu.UserID > 0 {
			usersCount[cu.UserID] = usersCount[cu.UserID] + 1
			if cu.ValuableUser {
				valuableUserIDs[cu.UserID] = struct{}{}
			}
		}
	}
	pipe = service.IMRedis.Pipeline()
	for uid, incr := range usersCount {
		pipe.HIncrBy(hKey, strconv.FormatInt(uid, 10), incr)
	}
	for usreID := range valuableUserIDs {
		pipe.SAdd(vuKey, usreID)
	}

	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

// RoomListResult 房间列表结果
type RoomListResult struct {
	UserIDs         []int64
	Conns           []string
	ValuableUserIDs []int64
}

// ListOptions list options
type ListOptions struct {
	ListUserIDs         bool
	ListConns           bool
	ListValuableUserIDs bool
}

// List 列出聊天室内的在线用户和连接用户
// NOTICE: 传入的 opt 总应是非 nil 值
func List(roomID int64, opt *ListOptions) (*RoomListResult, error) {
	pipe := service.IMRedis.Pipeline()
	var usersCmd *redis.StringStringMapCmd
	if opt.ListUserIDs {
		usersCmd = pipe.HGetAll(keys.KeyIMRoomUsers1.Format(roomID))
	}
	var connsCmd *redis.StringSliceCmd
	if opt.ListConns {
		connsCmd = pipe.ZRange(keys.KeyIMRoomMembers1.Format(roomID), 0, -1)
	}
	var vuCmd *redis.StringSliceCmd
	if opt.ListValuableUserIDs {
		vuCmd = pipe.SMembers(keys.IMKeyRoomValuableUsers1.Format(roomID))
	}
	_, err := pipe.Exec()
	if err != nil {
		return nil, err
	}
	var res RoomListResult
	if usersCmd != nil {
		uMap := usersCmd.Val()
		res.UserIDs = make([]int64, 0, len(uMap))
		for uidStr, countStr := range uMap {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			count, _ := strconv.ParseInt(countStr, 10, 64)
			if uid != 0 && count > 0 {
				res.UserIDs = append(res.UserIDs, uid)
			}
		}
	}
	if connsCmd != nil {
		res.Conns = connsCmd.Val()
	}
	if vuCmd != nil {
		s := vuCmd.Val()
		res.ValuableUserIDs = make([]int64, 0, len(s))
		for i := range s {
			userID, _ := strconv.ParseInt(s[i], 10, 64)
			if userID > 0 {
				res.ValuableUserIDs = append(res.ValuableUserIDs, userID)
			}
		}
	}
	return &res, nil
}

// BatchList 批量列出聊天室内的在线用户
// NOTICE: 传入的 opt 总应是非 nil 值，目前只支持列出在线用户
func BatchList(roomIDs []int64, opt *ListOptions) (map[int64]*RoomListResult, error) {
	pipe := service.IMRedis.Pipeline()
	userCmdMap := make(map[int64]*redis.StringStringMapCmd, len(roomIDs))
	for _, roomID := range roomIDs {
		if opt.ListUserIDs {
			userCmdMap[roomID] = pipe.HGetAll(keys.KeyIMRoomUsers1.Format(roomID))
		}
	}
	_, err := pipe.Exec()
	if err != nil {
		return nil, err
	}

	res := make(map[int64]*RoomListResult, len(roomIDs))
	for roomID, userCmd := range userCmdMap {
		if userCmd == nil {
			continue
		}

		uMap := userCmd.Val()
		roomResult := RoomListResult{
			UserIDs: make([]int64, 0, len(uMap)),
		}
		for uidStr, countStr := range uMap {
			uid, _ := strconv.ParseInt(uidStr, 10, 64)
			count, _ := strconv.ParseInt(countStr, 10, 64)
			if uid != 0 && count > 0 {
				roomResult.UserIDs = append(roomResult.UserIDs, uid)
			}
		}
		res[roomID] = &roomResult
	}
	return res, nil
}
