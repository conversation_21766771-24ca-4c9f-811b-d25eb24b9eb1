package imuser

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestNewIMConnMember(t *testing.T) {
	assert := assert.New(t)

	m := NewIMConnMember("12", "test", true)
	assert.Equal("12|test|1", m)
}

func TestParseIMConnMember(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 不匹配的情况
	cu := parseIMConnMember("test")
	assert.Nil(cu)

	// 用户 ID 是 0
	cu = parseIMConnMember("guest|test|1")
	require.NotNil(cu)
	assert.Zero(cu.UserID)
	assert.Equal("test", cu.ConnUUID)
	assert.False(cu.ValuableUser)

	cu = parseIMConnMember("12|test|1")
	require.NotNil(cu)
	assert.EqualValues(12, cu.UserID)
	assert.Equal("test", cu.ConnUUID)
	assert.True(cu.ValuableUser)

	// 老版本的情况
	cu = parseIMConnMember("12_test")
	require.NotNil(cu)
	assert.EqualValues(12, cu.UserID)
	assert.Equal("test", cu.ConnUUID)
	assert.False(cu.ValuableUser)
}

func TestRefresh(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(99999)
	zKey := keys.KeyIMRoomMembers1.Format(roomID)
	hKey := keys.KeyIMRoomUsers1.Format(roomID)
	vuKey := keys.IMKeyRoomValuableUsers1.Format(roomID)

	score := float64(goutil.TimeNow().Unix())
	pipe := service.IMRedis.Pipeline()
	pipe.Del(zKey, hKey)
	pipe.ZAdd(zKey,
		&redis.Z{Member: "1|test1|1", Score: score},
		&redis.Z{Member: "1|test2|1", Score: score},
		&redis.Z{Member: "1|test3|1", Score: 0}, // 失效数据
		&redis.Z{Member: "2|test|0", Score: score},
		&redis.Z{Member: "guest|test|0", Score: score},
		&redis.Z{Member: "guest|test_outdated|0"},
	)
	pipe.HSet(hKey, "1", "6", "2", "8", "3", "888")
	pipe.SAdd(vuKey, "2")
	_, err := pipe.Exec()
	require.NoError(err)

	require.NoError(RefreshUsers(roomID, 1))
	res, err := service.IMRedis.HGetAll(hKey).Result()
	assert.NoError(err)
	assert.Equal("2", res["1"])
	assert.Equal("1", res["2"])
	_, ok := res["3"]
	assert.False(ok)
	err = service.IMRedis.ZScore(zKey, "1_test3").Err()
	assert.True(serviceredis.IsRedisNil(err))
	vus, err := service.IMRedis.SMembers(vuKey).Result()
	require.NoError(err)
	assert.EqualValues([]string{"1"}, vus)
}

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(99999)
	zKey := keys.KeyIMRoomMembers1.Format(roomID)
	hKey := keys.KeyIMRoomUsers1.Format(roomID)
	vuKey := keys.IMKeyRoomValuableUsers1.Format(roomID)

	conns := []*redis.Z{
		{Member: "1_test1"},
		{Member: "1_test2"},
		{Member: "2_test"},
		{Member: "guest_test"},
	}
	userIDs := []int64{1, 2, 3, 4, 5}
	pipe := service.IMRedis.Pipeline()
	pipe.Del(zKey, hKey, vuKey)
	pipe.ZAdd(zKey, conns...)
	pipe.HSet(hKey, userIDs[0], "6",
		userIDs[1], "8",
		userIDs[2], "888",
		userIDs[3], "0",
		userIDs[4], "-1")
	pipe.SAdd(vuKey, userIDs[0], userIDs[1])
	_, err := pipe.Exec()
	require.NoError(err)

	res, err := List(roomID,
		&ListOptions{ListUserIDs: true, ListConns: false, ListValuableUserIDs: true})
	require.NoError(err)
	assert.Empty(res.Conns)
	require.Len(res.UserIDs, 3)
	for i := 0; i < 3; i++ {
		assert.Contains(res.UserIDs, userIDs[i])
	}
	require.Len(res.ValuableUserIDs, 2)
	for i := 0; i < 2; i++ {
		assert.Contains(res.ValuableUserIDs, userIDs[i])
	}

	res, err = List(roomID,
		&ListOptions{ListUserIDs: false, ListConns: true, ListValuableUserIDs: false})
	require.NoError(err)
	assert.Empty(res.UserIDs)
	assert.Empty(res.ValuableUserIDs)
	assert.Len(res.Conns, len(res.Conns))
	for i := range res.Conns {
		assert.Contains(res.Conns, conns[i].Member)
	}
}

func TestBatchList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomIDs := []int64{1, 2, 3}
	pipe := service.IMRedis.TxPipeline()
	for _, testRoomID := range testRoomIDs {
		hKey := keys.KeyIMRoomUsers1.Format(testRoomID)
		pipe.Del(hKey)
		pipe.HSet(hKey,
			10, "6",
			11, "8",
			12, "888",
			13, "0",
			14, "-1")
		pipe.Expire(hKey, time.Second*10)
	}
	_, err := pipe.Exec()
	require.NoError(err)

	res, err := BatchList(testRoomIDs[:1],
		&ListOptions{ListUserIDs: true})
	require.NoError(err)
	require.Len(res, 1)
	require.NotNil(res[testRoomIDs[0]])
	assert.Len(res[testRoomIDs[0]].UserIDs, 3)

	res, err = BatchList(testRoomIDs, &ListOptions{ListUserIDs: true})
	require.NoError(err)
	require.Len(res, 3)
	for _, testRoomID := range testRoomIDs {
		require.NotNil(res[testRoomID])
		assert.Len(res[testRoomID].UserIDs, 3)
	}
}
