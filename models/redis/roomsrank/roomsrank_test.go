package roomsrank

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(tutil.KeyExists(tutil.JSON, Info{}, "revenue", "rank", "rank_invisible"))
}

func TestKey(t *testing.T) {
	assert := assert.New(t)
	when := time.Date(2019, 9, 29, 23, 59, 59, 0, time.Local)
	assert.PanicsWithValue("unsupported type: 0", func() { Key(123, 0, when) })
	assert.Equal("test_rooms/revenues/123/1", Key(123, 1, when))
	assert.Equal("test_rooms/revenues/123/2/20190923", Key(123, 2, when))
	assert.Equal("test_rooms/revenues/123/3/20190901", Key(123, 3, when))
	assert.Equal("test_rooms/revenues/123/4/2019092923", Key(123, 4, when))
}

func TestStartTime(t *testing.T) {
	assert := assert.New(t)
	when := time.Date(2019, 9, 29, 23, 59, 59, 0, time.Local)
	assert.Equal(time.Date(2019, 9, 23, 0, 0, 0, 0, time.Local), StartTime(2, when))
	assert.Equal(time.Date(2019, 9, 29, 23, 0, 0, 0, time.Local), StartTime(4, when))
	assert.PanicsWithValue("unsupported type: 0", func() { StartTime(0, when) })
}

func TestDeadline(t *testing.T) {
	assert := assert.New(t)
	// 周榜
	expected := time.Date(2019, 1, 14, 0, 0, 0, 0, time.Local)
	when := time.Date(2019, 1, 7, 0, 0, 0, 0, time.Local)
	assert.Equal(expected, Deadline(2, when))
	when = time.Date(2019, 1, 13, 23, 59, 59, 59, time.Local)
	assert.Equal(expected, Deadline(2, when))
	// 小时榜
	expected = time.Date(2020, 04, 27, 19, 0, 0, 0, time.Local)
	when = time.Date(2020, 04, 27, 18, 0, 0, 0, time.Local)
	assert.Equal(expected, Deadline(4, when))
	when = time.Date(2020, 04, 27, 18, 59, 59, 59, time.Local)
	assert.Equal(expected, Deadline(4, when))
	// panic
	assert.PanicsWithValue("unsupported type: 0", func() { Deadline(0, when) })
}

func TestTTL(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(11*24*60*time.Minute, ttl(RankTypeWeek))
	assert.Equal(45*24*time.Hour, ttl(RankTypeMonth))
	assert.Equal(90*60*time.Second, ttl(RankTypeHourly))
	assert.PanicsWithValue("unsupported type: -1", func() { ttl(-1) })
}

func TestRankLen(t *testing.T) {
	assert := assert.New(t)

	types := []int{RankTypeTotal, RankTypeCurrent, RankTypeWeek, RankTypeMonth, RankTypeHourly}
	for i := range types {
		assert.Equal(int64(50), RankLen(types[i]))
	}
	assert.Equal(int64(10), RankLen(RankTypePK))

	assert.Zero(RankLen(10))
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(1230000000, 0)
	key := Key(9999, RankTypeCurrent, when)
	assert.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 10, Member: 10},
		&redis.Z{Score: 11, Member: 11},
		&redis.Z{Score: 12, Member: 12},
		&redis.Z{Score: 13, Member: 13}).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	one := Info{ID: 19, Rank: 100}
	rank, err := Find(9999, RankTypeCurrent, when, &one)
	require.NoError(err)
	require.Len(rank, 4)
	assert.Equal([4]int64{10, 11, 12, 13},
		[4]int64{rank[3].ID, rank[2].ID, rank[1].ID, rank[0].ID})
	assert.Equal(int64(3), rank[2].Rank)
	assert.Zero(one.Rank)
	// 测试 info.assignRank 没查到的情况
	one.ID = 10
	_, err = Find(9999, RankTypeCurrent, when, &one)
	require.NoError(err)
	assert.Equal(int64(4), one.Rank)
	assert.Equal(int64(10), one.Revenue)
}

func TestAddRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(145236)
	now := goutil.TimeNow()
	keys := []string{
		Key(roomID, RankTypeHourly, now),
		Key(roomID, RankTypeCurrent, now),
		Key(roomID, RankTypeMonth, now),
	}
	require.NoError(service.Redis.Del(keys...).Err())
	require.NoError(AddRevenue(roomID, 12, 1, true))
	require.NoError(AddRevenue(roomID, 12, 1, true)) // 运行两次保证 TTL 的两种情况都能实现
	pipe := service.Redis.Pipeline()
	c1 := pipe.ZScore(keys[0], "12")
	t1 := pipe.TTL(keys[0])
	t2 := pipe.TTL(keys[1])
	c3 := pipe.ZScore(keys[2], "12")
	t3 := pipe.TTL(keys[2])
	_, err := pipe.Exec()
	require.NoError(err)
	assert.GreaterOrEqual(c1.Val(), 2.0)
	assert.GreaterOrEqual(c3.Val(), 2.0)
	assert.GreaterOrEqual(CurrentRevenue(roomID, 12), int64(2.0)) // 这里测试了 CurrentRevenue 是否正确
	assert.True(t1.Val() > 0)
	assert.True(t2.Val() < 0)
	assert.True(t3.Val() > 0)
	assert.NoError(service.Redis.Del(keys...).Err())
	assert.Zero(CurrentRevenue(roomID, 12))

	require.NoError(service.Redis.Del(keys...).Err())
	require.NoError(AddRevenue(roomID, 12, 1, false))
	require.NoError(AddRevenue(roomID, 12, 1, false)) // 运行两次保证 TTL 的两种情况都能实现
	assert.Zero(CurrentRevenue(roomID, 12))
}

func TestPKKey(t *testing.T) {
	assert := assert.New(t)

	oid := primitive.NewObjectID()
	assert.Equal("test_rooms/revenues/123/7/"+oid.Hex(), PKKey(123, oid))
}

func TestFindRankMapByRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(1230000000, 0)
	rankType := RankTypeMonth
	testRoomIDs := []int64{123, 456}
	keys := make([]string, 0, len(testRoomIDs))
	for _, roomID := range testRoomIDs {
		keys = append(keys, Key(roomID, rankType, when))
	}
	require.NoError(service.Redis.Del(keys...).Err())
	pipe := service.Redis.Pipeline()
	for _, key := range keys {
		pipe.ZAdd(key,
			&redis.Z{Score: 10, Member: 10},
			&redis.Z{Score: 11, Member: 11},
			&redis.Z{Score: 12, Member: 12},
			&redis.Z{Score: 13, Member: 13},
		)
	}
	_, err := pipe.Exec()
	require.NoError(err)

	result, err := FindRankMapByRoomIDs(when, rankType, testRoomIDs)
	require.NoError(err)
	require.NotNil(result)
	for _, roomID := range testRoomIDs {
		rank, ok := result[roomID]
		require.True(ok)
		assert.Equal([]int64{13, 12, 11},
			[]int64{rank[0].ID, rank[1].ID, rank[2].ID})
		assert.Equal([]int64{13, 12, 11},
			[]int64{rank[0].Revenue, rank[1].Revenue, rank[2].Revenue})
		assert.Equal([]int64{1, 2, 3},
			[]int64{rank[0].Rank, rank[1].Rank, rank[2].Rank})
	}
}
