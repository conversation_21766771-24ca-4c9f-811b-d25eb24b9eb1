package roomsrank

import (
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const panicRankType cache.KeyFormat = "unsupported type: %d"

// rank types
const (
	RankTypeTotal   = iota // 总榜
	RankTypeCurrent        // 本场榜
	RankTypeWeek           // 周榜
	RankTypeMonth          // 月榜（仅记录）

	RankTypeHourly   = 4 // 小时榜，每小时通知用
	RankTypeActivity = 6 // 活动榜单，第三个参数具体情况具体分析
	RankTypePK       = 7 // PK 助攻榜
)

// Key 返回格式化后的 key
// 会根据前缀不同返回不同结果
// NOTICE: 需要前缀
func Key(roomID int64, rankType int, when time.Time) string {
	switch rankType {
	case RankTypeCurrent:
		return keys.KeyRoomsRevenues3.Format(roomID, rankType, "")
	case RankTypeWeek:
		formatStr := "/20060102"
		when = when.AddDate(0, 0, -util.WeekdayToInt(when.Weekday()))
		return keys.KeyRoomsRevenues3.Format(roomID, rankType, when.Format(formatStr))
	case RankTypeMonth:
		formatStr := "/20060102"
		when = util.BeginningOfMonth(when)
		return keys.KeyRoomsRevenues3.Format(roomID, rankType, when.Format(formatStr))
	case RankTypeHourly:
		formatStr := "/2006010215"
		return keys.KeyRoomsRevenues3.Format(roomID, rankType, when.Format(formatStr))
	default:
		panic(panicRankType.Format(rankType))
	}
}

// StartTime 榜单开始时间
func StartTime(rankType int, when time.Time) (startTime time.Time) {
	y, m, d := when.Date()
	switch rankType {
	case RankTypeWeek:
		startTime = time.Date(y, m, d-util.WeekdayToInt(when.Weekday()),
			0, 0, 0, 0, when.Location())
	case RankTypeHourly:
		startTime = time.Date(y, m, d, when.Hour(), 0, 0, 0, when.Location())
	default:
		panic(panicRankType.Format(rankType))
	}
	return
}

// Deadline 对有时间限制的榜单进行截至日期的计算
func Deadline(rankType int, when time.Time) (deadline time.Time) {
	switch rankType {
	case RankTypeWeek:
		deadline = time.Date(when.Year(), when.Month(), when.Day()+7-util.WeekdayToInt(when.Weekday()),
			0, 0, 0, 0, when.Location())
	case RankTypeHourly:
		deadline = time.Date(when.Year(), when.Month(), when.Day(), when.Hour()+1, 0, 0, 0, when.Location())
	default:
		panic(panicRankType.Format(rankType))
	}
	return
}

// ttl 某类榜单的 TTL 应该设置成多少
// REVIEW: 需要和 audio-chatroom 同步
func ttl(rankType int) (ttl time.Duration) {
	switch rankType {
	case RankTypeWeek:
		ttl = 11 * 24 * time.Hour // 7 + 4 天
	case RankTypeMonth:
		ttl = 45 * 24 * time.Hour // 45 天
	case RankTypeHourly:
		ttl = 90 * time.Minute // 1.5 小时
	default:
		panic(panicRankType.Format(rankType))
	}
	return
}

// PKKey PK 助攻榜单
// TODO: 单元测试
func PKKey(roomID int64, pkOID primitive.ObjectID) string {
	return keys.KeyRoomsRevenues3.Format(roomID, RankTypePK, "/"+pkOID.Hex())
}

// RankLen 排行榜长度
func RankLen(rankType int) int64 {
	switch rankType {
	case RankTypeTotal, RankTypeCurrent, RankTypeWeek:
		return 50
	// 用户贡献榜小时榜、月榜仅记录不对外查询
	case RankTypeHourly, RankTypeMonth:
		return 50
	case RankTypePK:
		return 10
	}
	return 0
}

// Info 榜单收益信息
type Info struct {
	ID            int64 `json:"-"` // 从 redis 获取的用户 ID
	Revenue       int64 `json:"revenue"`
	Rank          int64 `json:"rank"`
	RankInvisible bool  `json:"rank_invisible"`

	*liveuser.Simple
}

// Find 查询榜单数据
// 如果 another 不为空并且其 another.ID != 0 则为其填充其他数据
func Find(roomID int64, rankType int, when time.Time, another *Info) ([]*Info, error) {
	key := Key(roomID, rankType, when)
	pipe := service.Redis.Pipeline()
	listCmd := pipe.ZRevRangeWithScores(key, 0, RankLen(rankType)-1)

	var rankCmd *redis.IntCmd
	var revenueCmd *redis.FloatCmd
	if another != nil && another.ID != 0 {
		uidStr := strconv.FormatInt(another.ID, 10)
		rankCmd = pipe.ZRevRank(key, uidStr)
		revenueCmd = pipe.ZScore(key, uidStr)
		another.RankInvisible = userstatus.IsRankInvisible(another.ID, roomID, true)
	}
	_, _ = pipe.Exec()

	res, err := makeInfoList(listCmd)
	if err != nil {
		return nil, err
	}
	// 开始处理 another
	if rankCmd != nil {
		another.assignRank(rankCmd, revenueCmd)
	}

	// 填充 Info.Simple
	set := userstatus.RankInvisibleUsers(roomID)
	ids := make([]int64, len(res), len(res)+1)
	for i := 0; i < len(res); i++ {
		ids[i] = res[i].ID
		_, res[i].RankInvisible = set[res[i].ID]
	}
	if another != nil && another.ID != 0 {
		ids = append(ids, another.ID)
		if another.Rank < int64(len(res))+1 {
			for i := range res {
				if res[i].ID == another.ID {
					res[i].RankInvisible = another.RankInvisible
					break
				}
			}
		}
	}
	proj := liveuser.ProjectionSimple
	proj.Group = 0
	users, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": ids}},
		&liveuser.FindOptions{
			FindTitles: true,
			RoomID:     roomID,
		}, options.Find().SetProjection(proj)))
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		if u := users[res[i].ID]; u != nil {
			res[i].Simple = u
		} else {
			res[i].Simple = &liveuser.Simple{UID: res[i].ID}
		}
	}
	if another != nil && another.ID != 0 {
		if u := users[another.ID]; u != nil {
			another.Simple = u
		} else {
			another.Simple = &liveuser.Simple{UID: another.ID}
		}
	}
	return res, nil
}

// makeInfoList 获取用 ID 和贡献和排行
func makeInfoList(cmd *redis.ZSliceCmd) ([]*Info, error) {
	val, err := cmd.Result()
	if err != nil {
		return nil, err
	}
	res := make([]*Info, len(val))
	for i := 0; i < len(val); i++ {
		res[i] = new(Info)
		res[i].ID, err = strconv.ParseInt(val[i].Member.(string), 10, 64)
		if err != nil {
			return nil, err
		}
		res[i].Revenue = int64(val[i].Score)
		res[i].Rank = int64(i) + 1
	}
	return res, nil
}

func (info *Info) assignRank(rankCmd *redis.IntCmd, revenueCmd *redis.FloatCmd) {
	info.Rank = 0
	info.Revenue = 0
	err := rankCmd.Err()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		return
	}
	err = revenueCmd.Err()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		return
	}
	if err != redis.Nil {
		info.Rank = rankCmd.Val() + 1
		info.Revenue = int64(revenueCmd.Val())
	}
}

// CurrentRevenue 本场榜用户的值
func CurrentRevenue(roomID, userID int64) int64 {
	key := Key(roomID, RankTypeCurrent, goutil.TimeNow())
	val, err := service.Redis.ZScore(key, strconv.FormatInt(userID, 10)).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Error(err)
		}
		return 0
	}
	return int64(val)
}

// AddRevenue 增加贡献值
func AddRevenue(roomID, userID, score int64, isOpenRoom bool) error {
	now := goutil.TimeNow()
	types := [4]int{
		RankTypeHourly,
		RankTypeWeek,
		RankTypeMonth,
		// 没有 TTL 的类型放最后，保证变量 types 和 ttls 的元素一一对应
		RankTypeCurrent,
	}
	pipe := service.Redis.Pipeline()
	userIDStr := strconv.FormatInt(userID, 10)
	type ttlPair struct {
		key string
		cmd *redis.DurationCmd
	}
	ttls := make([]ttlPair, 0, len(types)-1)
	for _, t := range types {
		if t == RankTypeCurrent && !isOpenRoom {
			continue
		}
		key := Key(roomID, t, now)
		pipe.ZIncrBy(key, float64(score), userIDStr)
		if t != RankTypeCurrent { // 本场榜没有 ttl
			ttls = append(ttls, ttlPair{key: key, cmd: pipe.TTL(key)})
		}
	}
	_, err := pipe.Exec()
	if err != nil {
		return err
	}
	// 尝试添加 TTL
	pipe = service.Redis.Pipeline()
	var doPipe int
	for i, t := range ttls {
		if t.cmd.Val() < 0 {
			pipe.Expire(t.key, ttl(types[i]))
			doPipe++
		}
	}
	if doPipe == 0 {
		return nil
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

// FindRankMapByRoomIDs 根据房间 ID 批量获取用户贡献榜
func FindRankMapByRoomIDs(when time.Time, rankType int, roomIDs []int64) (map[int64][]*Info, error) {
	limit := RankLen(rankType)
	pipe := service.Redis.Pipeline()
	cmds := make([]*redis.ZSliceCmd, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		cmds = append(cmds, pipe.ZRevRangeWithScores(Key(roomID, rankType, when), 0, limit-1))
	}
	_, err := pipe.Exec()
	if err != nil {
		return nil, err
	}
	result := make(map[int64][]*Info, len(roomIDs))
	for i, roomID := range roomIDs {
		vals := cmds[i].Val()
		infoList := make([]*Info, 0, limit)
		for j, v := range vals {
			userID, err := strconv.ParseInt(v.Member.(string), 10, 64)
			if err != nil {
				return nil, err
			}
			info := Info{
				ID:      userID,
				Revenue: int64(v.Score),
				Rank:    int64(j + 1),
			}
			infoList = append(infoList, &info)
		}
		result[roomID] = infoList
	}
	return result, nil
}
