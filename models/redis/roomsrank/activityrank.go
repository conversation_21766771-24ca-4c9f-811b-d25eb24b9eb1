package roomsrank

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/service/keys"
)

// ActivityKey 活动key
func ActivityKey(roomID int64, activityDay string) string {
	return keys.KeyRoomsRevenues3.Format(roomID, RankTypeActivity, "/"+activityDay)
}

// ActivityGuildKey 公会赛活动 key
func ActivityGuildKey(guildID int64, eventID int64) string {
	return keys.KeyGuildRevenues3.Format(guildID, RankTypeActivity, fmt.Sprintf("/%d", eventID))
}
