package chatroom

import (
	"time"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// AddCurrentRoomPaidUser 添加直播间本场消费用户
// 统计场景有：赠送有价值的礼物、开通贵族、开通 / 续费超粉、提问且被完成回答、发送付费弹幕
func AddCurrentRoomPaidUser(roomID, userID, openTime int64) {
	key := keys.KeyRoomPaidUser2.Format(roomID, openTime)
	pipe := service.Redis.TxPipeline()
	pipe.SAdd(key, userID)
	pipe.Expire(key, 15*24*time.Hour) // 15 天过期，关播时需要删除记录
	_, err := pipe.Exec()
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":   userID,
			"room_id":   roomID,
			"open_time": openTime,
		}).Error(err)
		// PASS
	}
}

// CountCurrentRoomPaidUser 获取直播间本场消费用户数量
func CountCurrentRoomPaidUser(roomID, openTime int64) int64 {
	key := keys.KeyRoomPaidUser2.Format(roomID, openTime)
	count, err := service.Redis.SCard(key).Result()
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id":   roomID,
			"open_time": openTime,
		}).Error(err)
		// PASS
	}
	return count
}
