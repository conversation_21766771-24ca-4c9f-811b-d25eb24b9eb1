package chatroom

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestCountCurrentRoomPaidUser(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testRoomID := int64(9074509)
	testOpenTime := int64(1727409158000)
	key := keys.KeyRoomPaidUser2.Format(testRoomID, testOpenTime)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	count := CountCurrentRoomPaidUser(testRoomID, testOpenTime)
	assert.Zero(count)

	AddCurrentRoomPaidUser(testRoomID, 9074501, testOpenTime)
	AddCurrentRoomPaidUser(testRoomID, 9074502, testOpenTime)

	count = CountCurrentRoomPaidUser(testRoomID, testOpenTime)
	assert.EqualValues(2, count)
}
