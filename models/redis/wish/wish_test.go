package wish

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	goodsID    int64 = 2334342
	testUserID int64 = 5456412
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestKeyWishNum(t *testing.T) {
	assert := assert.New(t)

	// 测试活动刚开始的情况
	key := KeyWishNum(goodsID, time.Date(2022, 07, 07, 0, 0, 0, 0, time.Local))
	expectedKey := fmt.Sprintf("user:wish:num:goods_id:%d:202207070000", goodsID)
	assert.Equal(expectedKey, key)

	// 测试活动周期开始 10 分钟后的情况
	key = KeyWishNum(goodsID, time.Date(2022, 07, 07, 0, 10, 0, 0, time.Local))
	assert.Equal(expectedKey, key)

	// 测试活动进行 30 分钟的情况
	key = KeyWishNum(goodsID, time.Date(2022, 07, 07, 0, 30, 0, 0, time.Local))
	expectedKey = fmt.Sprintf("user:wish:num:goods_id:%d:202207070030", goodsID)
	assert.Equal(expectedKey, key)
}

func TestUserWishNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	now := goutil.TimeNow()
	// 测试未许过愿的情况
	key := fmt.Sprintf("user:wish:num:goods_id:%d:202207070000", goodsID)
	require.NoError(service.Redis.Del(key).Err())
	num, err := UserWishNum(goodsID, testUserID, now)
	require.NoError(err)
	assert.Zero(num)

	// 测试获取到正确的值
	require.NoError(service.Redis.HSet(key, testUserID, 233).Err())
	num, err = UserWishNum(goodsID, testUserID, now)
	require.NoError(err)
	assert.Equal(int64(233), num)
}

func TestIncrUserWishNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	now := goutil.TimeNow()
	// 测试未许过愿进行添加的情况
	key := fmt.Sprintf("user:wish:num:goods_id:%d:202207070000", goodsID)
	require.NoError(service.Redis.Del(key).Err())
	num, err := IncrUserWishNum(goodsID, testUserID, 1, now)
	require.NoError(err)
	assert.Equal(int64(1), num)
	// 验证缓存值正确
	numFromCache, err := service.Redis.HGet(key, strconv.FormatInt(testUserID, 10)).Int64()
	require.NoError(err)
	assert.Equal(int64(1), numFromCache)

	// 测试许过愿继续添加的情况
	num, err = IncrUserWishNum(goodsID, testUserID, 233, now)
	require.NoError(err)
	assert.Equal(int64(234), num)
	// 验证缓存值正确
	numFromCache, err = service.Redis.HGet(key, strconv.FormatInt(testUserID, 10)).Int64()
	require.NoError(err)
	assert.Equal(int64(234), numFromCache)
}
