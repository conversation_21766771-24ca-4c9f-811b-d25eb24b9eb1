package wish

import (
	"strconv"
	"time"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// KeyWishNum 返回许愿次数 key
func KeyWishNum(goodsID int64, when time.Time) string {
	// TODO: 周期应该从参数传入
	// 获取周期开始时间（按分钟），每 15min 为一个周期
	when = when.Truncate(15 * time.Minute)
	return keys.KeyUserWishNum2.Format(goodsID, when.Format(util.TimeFormatYMDHHMMWithNoSpace))
}

// UserWishNum 返回用户当前许愿次数
func UserWishNum(goodsID, userID int64, when time.Time) (int64, error) {
	key := KeyWishNum(goodsID, when)
	num, err := service.Redis.HGet(key, strconv.FormatInt(userID, 10)).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	return num, nil
}

// IncrUserWishNum 增减用户许愿次数，该方法不对增减的次数做限制
func IncrUserWishNum(goodsID, userID int64, num int, when time.Time) (int64, error) {
	key := KeyWishNum(goodsID, when)
	pipe := service.Redis.TxPipeline()
	cmd := pipe.HIncrBy(key, strconv.FormatInt(userID, 10), int64(num))
	pipe.Expire(key, 72*time.Hour) // 设置缓存有效期为 3 天
	_, err := pipe.Exec()
	if err != nil {
		return 0, err
	}
	currentNum, err := cmd.Result()
	if err != nil {
		return 0, err
	}
	return currentNum, nil
}
