package usersrank

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActivityKey(t *testing.T) {
	assert := assert.New(t)
	prePrefix := config.KeyPrefix
	defer func() {
		config.KeyPrefix = prePrefix
	}()
	assert.Equal(fmt.Sprintf("test_users/rank/%d/%s", TypeActivity, ActivityNationalDay), ActivityKey(ActivityNationalDay))
	assert.Equal(fmt.Sprintf("test_users/rank/%d/%s", TypeActivity, ActivityChristmasDay), ActivityKey(ActivityChristmasDay))
}

func TestActivityRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	key := ActivityKey(ActivityChristmasDay)
	testData := []*redis.Z{
		{Score: 10, Member: 10},
		{Score: 11, Member: 11},
		{Score: 12, Member: 12},
	}
	assert.NoError(service.Redis.Del(key).Err())
	assert.NoError(service.Redis.ZAdd(key, testData...).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	list, pa, err := ActivityRank(ActivityChristmasDay, 1, 10, 3, nil)
	require.NoError(err)
	require.Len(list, 3)
	assert.Equal(int64(3), pa.Count)
	assert.Equal(int64(1), pa.MaxPage)
	assert.Equal(int64(12), list[0].Revenue)
	list, pa, _ = ActivityRank(ActivityChristmasDay, 1000, 10, 3, nil)
	assert.Empty(list)
	assert.Equal(int64(3), pa.Count)
	assert.Equal(int64(1), pa.MaxPage)

	// 榜单不存在
	list, pa, err = ActivityRank(ActivityChristmasDay, 1, 10, 3, &Info{UserID: 1})
	require.NoError(err)
	require.Len(list, 3)
	assert.Equal(int64(3), pa.Count)
	assert.Equal(int64(1), pa.MaxPage)

	t.Run("TestActivityCount", func(t *testing.T) {
		testData := make([]*redis.Z, 55)
		for i := 0; i < 55; i++ {
			testData[i] = &redis.Z{Score: float64(i), Member: i}
		}
		assert.NoError(service.Redis.ZAdd(key, testData...).Err())
		assert.Equal(int64(50), ActivityCount(ActivityChristmasDay, 50))
	})
}

func TestActivityInfoKey(t *testing.T) {
	assert := assert.New(t)

	info := ActivityInfoMap[Activity20200518]
	assert.Equal("test_users/rank/6/20200518_1", info.Key(1))
	info.Partition = false
	assert.Equal("test_users/rank/6/20200518", info.Key(1))
}

func TestNewActivityDay(t *testing.T) {
	assert := assert.New(t)

	base := time.Date(2020, 05, 18, 12, 0, 0, 0, time.Local)
	now := goutil.TimeNow()
	config.Conf.AB["activity_time_offset"] = int(base.Unix() - now.Unix())
	activityDay, info := NewActivityDay(now)
	assert.Equal("20200518", activityDay)
	assert.NotNil(info)
	base = time.Date(2020, 05, 17, 12, 0, 0, 0, time.Local)
	config.Conf.AB["activity_time_offset"] = int(base.Unix() - now.Unix())
	activityDay, info = NewActivityDay(now)
	assert.Equal("", activityDay)
	assert.Nil(info)
}
