package usersrank

import (
	"slices"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const panicRankType cache.KeyFormat = "unsupported type: %d"

// KeyUsersNovaSlotNum redis key users/nova/${20060102}/${slot} 的槽数量
const KeyUsersNovaSlotNum = 10

// RankTypes
const (
	TypeDay = iota + 1
	TypeWeek
	TypeMonth
	TypeHour
	TypeGashaponWeek // 盲盒主播周榜
	_
	TypeNova      // 新人榜
	TypeLastMonth // 上期月榜
	typeLimit

	TypeHourHomepage = 101 // 首页排行榜的直播小时榜
	TypeHourLiveTab  = 102 // 首页底部导航栏直播推荐 tab 小时榜
)

// RankLen 排行榜长度
func RankLen(rankType int) int64 {
	switch rankType {
	case TypeHour:
		return 10
	case TypeHourHomepage, TypeHourLiveTab:
		return 20
	default:
		return 50
	}
}

// Info 榜单收益信息
type Info struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
	Revenue  int64  `json:"revenue"`
	Rank     int64  `json:"rank"`

	RankUp      int64   `json:"rank_up"`                // RankUp = 前一名的收益 - 自己的收益 + 1
	RankChanges **int64 `json:"rank_changes,omitempty"` // 主播排名变化 = 上一场的名次 - 本场名次，null 表示新上榜，除了需要显示排名变化的地方返回该字段，其余地方不返回该字段
	IsNova      bool    `json:"is_nova,omitempty"`      // 是否是新人（只有满足新人榜上榜条件或未创建直播间的情况才有值），仅新人榜使用该值

	RoomID        int64             `json:"-"` // 直播间 ID
	RoomUsersRank []*roomsrank.Info `json:"-"` // 助力榜榜单
}

// RankChange 分数增加后的排名变化
type RankChange struct {
	UserID   int64 `json:"user_id"`
	PrevRank int64 `json:"prev_rank"`
	Rank     int64 `json:"rank"`
	Score    int64 `json:"score"`
}

// Key 返回格式化后的 key
// 会根据前缀不同返回不同结果
// NOTICE: 需要前缀
func Key(rankType int, when time.Time) string {
	formatStr := util.TimeFormatYMDWithNoSpace
	switch rankType {
	case TypeWeek, TypeGashaponWeek:
		when = when.AddDate(0, 0, -util.WeekdayToInt(when.Weekday()))
	case TypeMonth:
		when = util.BeginningOfMonth(when)
	case TypeHour:
		formatStr = util.TimeFormatYMDHHWithNoSpace
	case TypeHourHomepage, TypeHourLiveTab:
		// 由于首页排行榜的直播小时榜和首页底部导航栏直播推荐 tab 小时榜数量与小时榜（TypeHour）数量不一致，所以需要单独区分 type
		rankType = TypeHour
		formatStr = util.TimeFormatYMDHHWithNoSpace
	case TypeDay, TypeNova:
		// 使用默认值
	default:
		panic(panicRankType.Format(rankType))
	}
	return keys.KeyUsersRank2.Format(rankType, when.Format(formatStr))
}

// LastRankKey 返回上一场榜单 key
func LastRankKey(rankType int, when time.Time) string {
	switch rankType {
	case TypeWeek, TypeGashaponWeek:
		when = when.AddDate(0, 0, -7)
	case TypeMonth:
		when = util.BeginningOfMonth(when).AddDate(0, -1, 0)
	case TypeHour, TypeHourHomepage, TypeHourLiveTab:
		when = when.Add(-time.Hour)
	case TypeDay, TypeNova:
		when = when.AddDate(0, 0, -1)
	default:
		panic(panicRankType.Format(rankType))
	}

	return Key(rankType, when)
}

// lastRankCacheKey 返回上一场榜单缓存 key
// 时间为本场时间，数据为上一场数据
func lastRankCacheKey(rankType int, when time.Time) string {
	formatStr := util.TimeFormatYMDWithNoSpace
	switch rankType {
	case TypeWeek, TypeGashaponWeek:
		when = when.AddDate(0, 0, -util.WeekdayToInt(when.Weekday()))
	case TypeMonth:
		when = util.BeginningOfMonth(when)
	case TypeHour:
		formatStr = util.TimeFormatYMDHHWithNoSpace
	case TypeHourHomepage, TypeHourLiveTab:
		rankType = TypeHour
		formatStr = util.TimeFormatYMDHHWithNoSpace
	case TypeDay, TypeNova:
		// 使用默认值
	default:
		panic(panicRankType.Format(rankType))
	}

	return keys.LocalKeyUsersLastRank2.Format(rankType, when.Format(formatStr))
}

// Deadline 返回 when 时刻某主播榜的截止日期
func Deadline(rankType int, when time.Time) time.Time {
	switch rankType {
	case TypeWeek, TypeGashaponWeek:
		when = time.Date(when.Year(), when.Month(), when.Day()+7-util.WeekdayToInt(when.Weekday()),
			0, 0, 0, 0, when.Location())
	case TypeMonth:
		when = time.Date(when.Year(), when.Month()+1, 1, 0, 0, 0, 0, when.Location())
	case TypeHour:
		when = time.Date(when.Year(), when.Month(), when.Day(), when.Hour()+1, 0, 0, 0, when.Location())
	case TypeDay, TypeNova:
		when = time.Date(when.Year(), when.Month(), when.Day()+1, 0, 0, 0, 0, when.Location())
	default:
		panic(panicRankType.Format(rankType))
	}
	return when
}

// ttl 某类榜单的 TTL 应该设置成多少
func ttl(rankType int) (ttl time.Duration) {
	// REVIEW: 需要和 audio-chatroom 保持
	now := goutil.TimeNow()
	switch rankType {
	case TypeDay, TypeNova:
		ttl = 3 * 24 * time.Hour
	case TypeWeek, TypeGashaponWeek:
		expireTime := goutil.BeginningOfWeek(now).AddDate(0, 0, 16)
		ttl = expireTime.Sub(now)
	case TypeMonth:
		expireTime := time.Date(now.Year(), now.Month()+2, 10, 0, 0, 0, 0, now.Location())
		ttl = expireTime.Sub(now)
	case TypeHour:
		ttl = 3 * time.Hour
	default:
		panic(panicRankType.Format(rankType))
	}
	return
}

// Top3 返回日榜、周榜、月榜、小时榜、新人榜的 top3
func Top3(when time.Time) (map[int][]*Info /* map[rankType][]*Info */, error) {
	rankTypes := [...]int{TypeDay, TypeWeek, TypeMonth, TypeHour, TypeNova}
	// NOTICE: 使用变量无法定义数组
	var keys [len(rankTypes)]string
	for i := range rankTypes {
		keys[i] = Key(rankTypes[i], when)
	}
	pipe := service.Redis.Pipeline()
	var cmds [len(rankTypes)]*redis.ZSliceCmd
	for i := range keys {
		cmds[i] = pipe.ZRevRangeWithScores(keys[i], 0, 2)
	}
	_, err := pipe.Exec()
	if err != nil {
		return nil, err
	}
	var tmpTop3 [len(rankTypes)][]*Info
	userIDs := make([]int64, 0, 3*len(rankTypes))
	for i := range tmpTop3 {
		val, _ := cmds[i].Result()
		tmpTop3[i] = make([]*Info, len(val))
		for j := range val {
			tmpTop3[i][j] = new(Info)
			tmpTop3[i][j].UserID, err = strconv.ParseInt(val[j].Member.(string), 10, 64)
			if err != nil {
				return nil, err
			}
			tmpTop3[i][j].Revenue = int64(val[j].Score)
			tmpTop3[i][j].Rank = int64(j) + 1
			userIDs = append(userIDs, tmpTop3[i][j].UserID)
		}
	}
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, err
	}
	res := make(map[int][]*Info, len(tmpTop3))
	for i := range tmpTop3 {
		for j := range tmpTop3[i] {
			u := users[tmpTop3[i][j].UserID]
			if u == nil {
				continue
			}
			tmpTop3[i][j].IconURL = u.IconURL
			tmpTop3[i][j].Username = u.Username
		}
		res[rankTypes[i]] = tmpTop3[i]
	}
	return res, nil
}

// ListLastHourTop3CreatorIDs 返回上一小时的 top3 主播 ID 列表
func ListLastHourTop3CreatorIDs() ([]int64, error) {
	key := LastRankKey(TypeHour, goutil.TimeNow())

	ranks, err := service.Redis.ZRevRange(key, 0, 2).Result()
	if err != nil {
		return nil, err
	}
	creatorIDs := make([]int64, len(ranks))
	for i, v := range ranks {
		creatorIDs[i], err = strconv.ParseInt(v, 10, 64)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return creatorIDs, nil
}

// FindOptions 查询榜单数据时的选项
type FindOptions struct {
	DisableFindRankChange bool  // 禁用查询排名变化
	RankCount             int64 // 查询榜单数量，0 为使用默认数量
}

// Find 查询榜单数据
// 如果 another 不为空并且其 another.UserID != 0 则为其更新 Rank, Revenue
func Find(rankType int, when time.Time, another *Info, opts ...FindOptions) ([]*Info, error) {
	var opt FindOptions
	if len(opts) > 0 {
		opt = opts[0]
	}

	key := Key(rankType, when)
	pipe := service.Redis.Pipeline()

	var count int64
	if opt.RankCount > 0 {
		count = opt.RankCount
	} else {
		count = RankLen(rankType)
	}
	listCmd := pipe.ZRevRangeWithScores(key, 0, count-1)

	var rankCmd *redis.IntCmd
	var revenueCmd *redis.FloatCmd
	if another != nil && another.UserID != 0 {
		uidStr := strconv.FormatInt(another.UserID, 10)
		rankCmd = pipe.ZRevRank(key, uidStr)
		revenueCmd = pipe.ZScore(key, uidStr)
	}
	// 在 another.assignRank(rankCmd, revenueCmd) 内部处理了错误
	_, _ = pipe.Exec()

	var (
		lastRankMap map[int64]int64
		err         error
	)
	if !opt.DisableFindRankChange {
		lastRankMap, err = findLastRankMap(rankType, when)
		if err != nil {
			return nil, err
		}
	}

	res, err := makeInfoList(listCmd, lastRankMap, 0)
	if err != nil {
		return nil, err
	}

	// 开始处理 another
	if rankCmd != nil {
		another.assignRank(rankCmd, revenueCmd)
	}

	return res, nil
}

// FindLastRank 获取上一场榜单主播 ID、收益和排名
func FindLastRank(rankType int, when time.Time) ([]Info, error) {
	cacheKey := lastRankCacheKey(rankType, when)
	res, ok := service.Cache5Min.Get(cacheKey)
	if ok {
		if lastRankInfos, ok := res.([]Info); ok {
			return lastRankInfos, nil
		}
	}

	lastRankKey := LastRankKey(rankType, when)
	vals, err := service.Redis.ZRevRangeWithScores(lastRankKey, 0, RankLen(rankType)-1).Result()
	if err != nil {
		return nil, err
	}
	results := make([]Info, 0, len(vals))
	for i, v := range vals {
		userID, err := strconv.ParseInt(v.Member.(string), 10, 64)
		if err != nil {
			return nil, err
		}
		info := Info{
			UserID:  userID,
			Revenue: int64(v.Score),
			Rank:    int64(i + 1),
		}
		results = append(results, info)
	}
	service.Cache5Min.SetDefault(cacheKey, results)
	return results, nil
}

func findLastRankMap(rankType int, when time.Time) (map[int64]int64, error) {
	lastRankInfos, err := FindLastRank(rankType, when)
	if err != nil {
		return nil, err
	}

	lastRankMap := make(map[int64]int64, len(lastRankInfos))
	for _, info := range lastRankInfos {
		lastRankMap[info.UserID] = info.Rank
	}
	return lastRankMap, nil
}

// FindRankInfoWithRankNum 获取指定数量的榜单数据
func FindRankInfoWithRankNum(rankType int, when time.Time, rankNum int64) ([]*Info, error) {
	key := Key(rankType, when)
	stop := rankNum
	if rankNum > RankLen(rankType) {
		stop = RankLen(rankType)
	}
	listCmd := service.Redis.ZRevRangeWithScores(key, 0, stop-1)
	res, err := makeInfoList(listCmd, nil, 0)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindOne 查询某个用户的排行
// NOTICE: 只返回了 UserID, Rank, Revenue 三个值
func FindOne(rankType int, when time.Time, userID int64) (*Info, error) {
	key := Key(rankType, when)
	pipe := service.Redis.Pipeline()
	uidStr := strconv.FormatInt(userID, 10)
	rankCmd := pipe.ZRevRank(key, uidStr)
	revenueCmd := pipe.ZScore(key, uidStr)
	_, err := pipe.Exec()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}

	res := &Info{UserID: userID}
	if serviceredis.IsRedisNil(err) {
		return res, nil
	}
	res.Rank = rankCmd.Val() + 1
	res.Revenue = int64(revenueCmd.Val())
	return res, nil
}

// FindHourRank 查询小时榜数据，返回本小时前十和上小时前三
// 如果 another 不为空并且其 another.UserID !=0 则为其更新 Rank, Revenue
func FindHourRank(when time.Time, another *Info) (lastTop3, nowTop10 []*Info, err error) {
	nowKey := Key(TypeHour, when)
	lastKey := LastRankKey(TypeHour, when)
	pipe := service.Redis.Pipeline()

	top3Cmd := pipe.ZRevRangeWithScores(lastKey, 0, 2)
	// 固定返回前十名数据，这里不用 RankLen() 是因为主播贡献榜小时榜的接口是单独的
	top10Cmd := pipe.ZRevRangeWithScores(nowKey, 0, 9)
	var rankCmd *redis.IntCmd
	var revenueCmd *redis.FloatCmd
	if another != nil && another.UserID != 0 {
		uidStr := strconv.FormatInt(another.UserID, 10)
		rankCmd = pipe.ZRevRank(nowKey, uidStr)
		revenueCmd = pipe.ZScore(nowKey, uidStr)
	}
	_, _ = pipe.Exec()

	lastTop3, err = makeInfoList(top3Cmd, nil, 0)
	if err != nil {
		return
	}

	lastRankMap, err := findLastRankMap(TypeHour, when)
	if err != nil {
		return
	}
	nowTop10, err = makeInfoList(top10Cmd, lastRankMap, 0)
	if err != nil {
		return
	}
	if rankCmd != nil {
		another.assignRank(rankCmd, revenueCmd)
	}
	return
}

func makeInfoList(rank *redis.ZSliceCmd, lastRankMap map[int64]int64, offset int64) ([]*Info, error) {
	val, err := rank.Result()
	if err != nil {
		return nil, err
	}
	uids := make([]int64, len(val))
	res := make([]*Info, len(val))
	for i := 0; i < len(val); i++ {
		res[i] = new(Info)
		res[i].UserID, err = strconv.ParseInt(val[i].Member.(string), 10, 64)
		if err != nil {
			return nil, err
		}
		res[i].Revenue = int64(val[i].Score)
		res[i].Rank = int64(i) + 1 + offset
		if lastRankMap != nil {
			lastRank, ok := lastRankMap[res[i].UserID]
			if ok {
				// 上一场第 2 名，本场第 1 名，则 2-1=1，表示排名上升 1 位
				// 上一场第 1 名，本场第 2 名，则 1-2=-1，表示排名下降 1 位
				// 上一场第 1 名，本场第 1 名，则 1-1=0，表示排名不变
				rankChanges := util.NewInt64(lastRank - res[i].Rank)
				res[i].RankChanges = &rankChanges
			} else {
				// 新上榜返回 null
				res[i].RankChanges = new(*int64)
			}
		}
		uids = append(uids, res[i].UserID)
		if i >= 1 {
			res[i].RankUp = res[i-1].Revenue - res[i].Revenue + 1
		}
	}
	// 配置第一名的 rank_up, 显示第一名和第二名的差值
	if len(res) == 1 {
		// 没有第二名，视第二名分数为 0
		res[0].RankUp = res[0].Revenue
	} else if len(res) >= 2 {
		res[0].RankUp = res[0].Revenue - res[1].Revenue
	}
	users, err := mowangskuser.FindSimpleMap(uids)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		u := users[res[i].UserID]
		if u == nil {
			continue
		}
		res[i].IconURL = u.IconURL
		res[i].Username = u.Username
	}
	return res, nil
}

func (info *Info) assignRank(rankCmd *redis.IntCmd, revenueCmd *redis.FloatCmd) {
	info.Rank = 0
	info.Revenue = 0
	err := rankCmd.Err()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		return
	}
	err = revenueCmd.Err()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		return
	}
	if !serviceredis.IsRedisNil(err) {
		info.Rank = rankCmd.Val() + 1
		info.Revenue = int64(revenueCmd.Val())
	}
}

// addRevenueType 需要的增加贡献分的榜单类型
func addRevenueType(userID, roomID int64) []int {
	types := []int{
		TypeHour,
		TypeDay,
		TypeWeek,
		TypeMonth,
	}
	exists, err := IsNova(userID)
	if err != nil {
		logger.WithField("user_id", userID).Error(err)
		// PASS
		return types
	}
	if exists {
		types = append(types, TypeNova)
	}

	// 热度限制的直播间不加所有常驻榜单
	exists, err = IsSuppressionHotRoom(roomID)
	if err != nil {
		logger.WithField("room_id", roomID).Error(err)
		// PASS
		return types
	}
	if exists {
		// 去掉所有常驻榜单
		types = make([]int, 0)
	}

	return types
}

// AddRevenue 增加收益值，返回小时榜的排名变化
func AddRevenue(userID, roomID, score int64) (*RankChange, error) {
	now := goutil.TimeNow()
	types := addRevenueType(userID, roomID)
	if len(types) == 0 {
		return nil, nil
	}
	keys := make([]string, len(types))
	for i := range types {
		keys[i] = Key(types[i], now)
	}

	pipe := service.Redis.TxPipeline()
	userIDStr := strconv.FormatInt(userID, 10)
	ttls := make([]*redis.DurationCmd, len(types))
	var rankChange *RankChange
	hasHourRank := slices.Contains(types, TypeHour)
	// 小时榜需要额外处理小时榜变化
	if hasHourRank {
		hourKey := Key(TypeHour, now)
		preRankCmd := pipe.ZRevRank(hourKey, userIDStr)
		for i := range types {
			pipe.ZIncrBy(keys[i], float64(score), userIDStr)
			ttls[i] = pipe.TTL(keys[i])
		}

		curRankCmd := pipe.ZRevRank(hourKey, userIDStr)
		curScoreCmd := pipe.ZScore(hourKey, userIDStr)
		_, err := pipe.Exec()
		if err != nil && !serviceredis.IsRedisNil(err) {
			return nil, err
		}

		rankChange = &RankChange{
			UserID: userID,
			Rank:   curRankCmd.Val() + 1,
			Score:  int64(curScoreCmd.Val()),
		}
		// 获取原来榜上排名，当原来不在榜上时排名为 0
		if !serviceredis.IsRedisNil(preRankCmd.Err()) {
			rankChange.PrevRank = preRankCmd.Val() + 1
		}
	} else {
		for i := range types {
			pipe.ZIncrBy(keys[i], float64(score), userIDStr)
			ttls[i] = pipe.TTL(keys[i])
		}
		_, err := pipe.Exec()
		if err != nil && !serviceredis.IsRedisNil(err) {
			return nil, err
		}
	}

	// 尝试添加 TTL
	pipe = service.Redis.Pipeline()
	var doPipe int
	for i := range ttls {
		if ttls[i].Val() < 0 {
			pipe.Expire(keys[i], ttl(types[i]))
			doPipe++
		}
	}
	if doPipe > 0 {
		_, err := pipe.Exec()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return rankChange, nil
}

// HourRank 用户小时榜
func HourRank(userID int64) (rank, rankUp int64, err error) {
	pipe := service.Redis.TxPipeline()
	key := Key(TypeHour, goutil.TimeNow())
	userIDStr := strconv.FormatInt(userID, 10)
	scoreCmd := pipe.ZScore(key, userIDStr)
	rankCmd := pipe.ZRevRank(key, userIDStr)
	_, err = pipe.Exec()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return
	}
	score := scoreCmd.Val()
	rank = rankCmd.Val()
	if score != 0 {
		// 这时候说明这人在榜上
		rank++
	}
	nextRank := RankLen(TypeHour)
	if rank != 0 {
		if rank > nextRank {
			rank = 0
		} else if rank == 1 {
			nextRank = 2
		} else {
			nextRank = rank - 1
		}
	}
	nextRank-- // 转成 redis 的 rank
	res, err := service.Redis.ZRevRangeWithScores(key, nextRank, nextRank).Result()
	if err != nil {
		return
	}
	if len(res) == 0 {
		if rank == 1 {
			// 榜单只有一人的情况，返回总分
			return 1, int64(score), nil
		}
		// 为空说明榜单不满并且此人没分数
		return 0, 1, nil
	}

	if rank == 1 {
		// 第一名的情况
		// 显示和第二名的差值
		rankUp = int64(score - res[0].Score)
	} else {
		// 不是第一名的情况
		// 排名上升的分值 = 前一名分值 - 当前分值 + 1
		rankUp = int64(res[0].Score - score + 1)
	}
	return rank, rankUp, nil
}

// AddGashaponWeek 增加盲盒主播榜
func AddGashaponWeek(roomID, userID int64, num int) error {
	exists, err := IsSuppressionHotRoom(roomID)
	if err != nil {
		return err
	}
	if exists {
		// 热度黑名单限制，不加主播榜
		return nil
	}
	now := goutil.TimeNow()
	key := Key(TypeGashaponWeek, now)
	pipe := service.Redis.TxPipeline()
	pipe.ZIncrBy(key, float64(num), strconv.FormatInt(userID, 10))
	pipe.Expire(key, ttl(TypeGashaponWeek))
	_, err = pipe.Exec()
	return err
}

// NovaKeySlot 通过 user_id 获取对应槽号
func NovaKeySlot(userID int64) int {
	// NOTICE: 如 user_id 为负数, 则返回槽号 0
	if userID < 0 {
		return 0
	}
	return int(userID % KeyUsersNovaSlotNum)
}

// NovaKey 封装获取 KEY
func NovaKey(now time.Time, slot int) string {
	return keys.KeyUsersNova2.Format(now.Format(util.TimeFormatYMDWithNoSpace), slot)
}

// IsNova 是否满足新人榜上榜条件
func IsNova(userID int64) (bool, error) {
	key := NovaKey(goutil.TimeNow(), NovaKeySlot(userID))
	exists, err := service.Redis.SIsMember(key, userID).Result()
	if err != nil {
		return false, err
	}
	return exists, nil
}

// IsSuppressionHotRoom 是否是热度限制直播间
// 关播的时候会清理过期的热度限制
func IsSuppressionHotRoom(roomID int64) (bool, error) {
	key := keys.KeyRoomsSuppressionHotList0.Format()
	expireTime, err := service.Redis.ZScore(key, strconv.FormatInt(roomID, 10)).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return false, nil
		}
		return false, err
	}

	if expireTime <= 0 || int64(expireTime) > goutil.TimeNow().Unix() {
		return true, nil
	}

	return false, nil
}

// ListHotSuppressionRoomIDs 获取热度限制直播间
// TODO: 直播间打压的逻辑都内聚到一个模块中
func ListHotSuppressionRoomIDs() ([]int64, error) {
	key := keys.KeyRoomsSuppressionHotList0.Format()
	now := goutil.TimeNow().Unix()
	pipe := service.Redis.TxPipeline()
	// score: 热度限制过期时间，0: 本次直播有效，-1: 永久有效。所以需要查询负数
	leftCmd := pipe.ZRangeByScore(key, &redis.ZRangeBy{Min: "-inf", Max: "0"})
	rightCmd := pipe.ZRangeByScore(key, &redis.ZRangeBy{Min: strconv.FormatInt(now, 10), Max: "+inf"})
	_, err := pipe.Exec()
	if err != nil {
		return nil, err
	}
	left := leftCmd.Val()
	right := rightCmd.Val()
	roomIDStrList := append(left, right...)
	roomIDs := make([]int64, 0, len(roomIDStrList))
	for _, v := range roomIDStrList {
		roomID, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			logger.Error(err)
			continue
		}
		roomIDs = append(roomIDs, roomID)
	}
	return roomIDs, nil
}

// AddNovaRevenue 添加新人榜分数
func AddNovaRevenue(userID int64, score int64) error {
	key := Key(TypeNova, goutil.TimeNow())
	pipe := service.Redis.TxPipeline()
	pipe.ZIncrBy(key, float64(score), strconv.FormatInt(userID, 10))
	ttlCmd := pipe.TTL(key)
	if _, err := pipe.Exec(); err != nil {
		return err
	}
	// 尝试添加 TTL
	if ttlCmd.Val() < 0 {
		err := service.Redis.Expire(key, ttl(TypeNova)).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// FindRank 获取榜单主播 ID、收益和排名
func FindRank(when time.Time, rankType int) ([]Info, error) {
	key := Key(rankType, when)
	limit := RankLen(rankType)
	vals, err := service.Redis.ZRevRangeWithScores(key, 0, limit-1).Result()
	if err != nil {
		return nil, err
	}
	results := make([]Info, 0, limit)
	for i, v := range vals {
		userID, err := strconv.ParseInt(v.Member.(string), 10, 64)
		if err != nil {
			return nil, err
		}
		info := Info{
			UserID:  userID,
			Revenue: int64(v.Score),
			Rank:    int64(i + 1),
		}
		results = append(results, info)
	}
	return results, nil
}
