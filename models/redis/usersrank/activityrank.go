package usersrank

import (
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 特殊的 RankTypes
const (
	TypeActivity = 6 // 活动类型
)

// 活动日期
const (
	ActivityChristmasDay         = "20191224" // 圣诞节活动日期
	ActivityNewYearsDay          = "20200101" // 元旦活动日期
	ActivityLunarNewYearDay      = "20200124" // 元旦活动日期
	ActivityHypnosisDay          = "20200301" // 失眠星人一期收集活动
	ActivityMayCollection        = "20200518" // 五月收集猫粮打榜活动
	ActivityHypnosisDay2         = "20200703" // 失眠星人二期活动日期
	ActivityJulyRebate           = "20200707" // 七月用户消费返利活动
	ActivityAugSummerHypnosisDay = "20200801" // 8 月暑期夏夜伴你入眠活动
	ActivityQixiFestivalDay      = "20200822" // 直播七夕活动
	ActivityNationalDay          = "20201001" // 国庆直播粉丝亲密度双倍活动
	ActivityHypnosisDay3         = "20201101" // 失眠星人三期活动日期
)

// 活动 ID
const (
	EventIDChristmasCollection        = 95  // 圣诞收集雪花活动: https://www.missevan.com/mevent/95
	EventIDJulyRoomCollectionActivity = 117 // 7 月收集心动主播语音活动: https://www.missevan.com/mevent/117
	EventIDHypnosisActivity           = 119 // 失眠星人二期活动: https://www.missevan.com/mevent/119
	EventIDAugSummerHypnosis          = 124 // 8 月暑期夏夜伴你入眠活动: https://www.missevan.com/mevent/124
	EventIDQixiFestival               = 128 // 七夕鹊桥活动：https://www.missevan.com/mevent/128
	EventIDNationalDay                = 134 // 国庆直播粉丝亲密度双倍活动：https://www.missevan.com/mevent/134
	EventIDListenTogether             = 138 // 【一起听】音乐征集: https://www.missevan.com/mevent/138
	EventIDHypnosisActivity3          = 139 // 失眠星人三期活动: https://www.missevan.com/mevent/139
	EventIDHeartAdministration        = 140 // 直播收集活动：心动管理局: https://www.missevan.com/mevent/140
	EventIDNovaChallenge              = 144 // 新星挑战营: https://www.missevan.com/mevent/144
	EventIDNewYear2021                = 151 // 新岁迎佳音: https://www.missevan.com/mevent/151
	EventIDMarchStarSign              = 152 // 星河献礼, 三月随机礼物星座活动: https://www.missevan.com/mevent/152
	EventIDSuperFanParty              = 157 // 超粉嘉年华, 五月活动: https://www.missevan.com/mevent/157
	EventIDSongOfLight                = 163 // 光夜之歌，七月活动: https://www.missevan.com/mevent/163
	EventIDSongOfNight                = 164 // 光夜之歌，七月活动: https://www.missevan.com/mevent/163
	EventIDShowLoveAction             = 166 // 告白大作战，七夕活动: https://www.missevan.com/mevent/166
	EventIDOccupyMoon                 = 168 // 月球占领计划，中秋活动: https://www.missevan.com/mevent/168
	EventIDUserRank                   = 169 // 头号公会请就位 主播赛，国庆活动: https://www.missevan.com/mevent/169
	EventIDGuildRank                  = 170 // 头号公会请就位 公会赛，国庆活动: https://www.missevan.com/mevent/169
	EventIDDoubleEleventh             = 172 // 礼物狂欢节，双十一活动: https://info.missevan.com/pages/viewpage.action?pageId=50726383
	EventIDChristmas                  = 174 // 欧气比拼！祈愿好运~，圣诞活动: https://info.missevan.com/pages/viewpage.action?pageId=50732964
	EventIDAnnualLive                 = 175 // 2021 猫耳FM直播年度盛典: https://info.missevan.com/pages/viewpage.action?pageId=50728820
	EventIDNewYear2022                = 182 // 新春节节高: https://info.missevan.com/pages/viewpage.action?pageId=53183181
	EventIDValentine                  = 184 // 玫瑰之夜: https://info.missevan.com/pages/viewpage.action?pageId=55312429
	EventIDPK                         = 191 // 春日集结: https://info.missevan.com/pages/viewpage.action?pageId=60525242
	EventIDCollectFlowers             = 193 // 花间集: https://info.missevan.com/pages/viewpage.action?pageId=64823362
	EventID196                        = 196 // 风月宴知音: https://info.missevan.com/pages/viewpage.action?pageId=64823777
	EventIDQiXi2022                   = 211 // 直播七夕活动: https://info.missevan.com/pages/viewpage.action?pageId=80380007
	EventIDWishes                     = 214 // 星座许愿池活动: https://info.missevan.com/pages/viewpage.action?pageId=82576205
	Event215                          = 215 // 时空变奏曲: https://info.missevan.com/pages/viewpage.action?pageId=84706959
	Event257                          = 257 // 春节活动: https://info.missevan.com/pages/viewpage.action?pageId=90773996
)

// activity type
const (
	// activityNationalDay  = "national_day"
	// activityChristmasDay = "christmas"
	// activityNewYearsDay  = "new_years_day"
	// activityLunarNewYear = "lunar_new_year"

	Activity20200518 = "20200518"
	Activity20200519 = "20200519"
	Activity20200520 = "20200520"
	Activity20200521 = "20200521"

	Activity20200522 = "20200522"
	Activity20200523 = "20200523"
	Activity20200524 = "20200524"
)

// ActivityInfo activity info
type ActivityInfo struct {
	ActivityDay string
	Previous    string
	Partition   bool
	PaCount     int64
	RankLimit   int64
}

// ActivityInfoMap 赛程
var ActivityInfoMap = map[string]ActivityInfo{
	Activity20200518: {Activity20200518, "", true, 70, 50},
	Activity20200519: {Activity20200519, Activity20200518, true, 50, 30},
	Activity20200520: {Activity20200520, Activity20200519, true, 30, 10},
	Activity20200521: {Activity20200521, Activity20200520, true, 10, 3},

	Activity20200522: {Activity20200522, Activity20200521, false, 12, 8},
	Activity20200523: {Activity20200523, Activity20200522, false, 8, 5},
	Activity20200524: {Activity20200524, Activity20200523, false, 5, 3},
}

// ActivityRank 活动主播榜排行
func ActivityRank(activityDay string, p, pageSize, count int64, selfInfo *Info) ([]*Info, goutil.Pagination, error) {
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*Info, 0), pa, nil
	}

	key := ActivityKey(activityDay)
	ranksCmd := service.Redis.ZRevRangeWithScores(key, pa.Offset(), pa.Offset()+pageSize-1)
	res, err := makeInfoList(ranksCmd, nil, pa.Offset())
	if err != nil {
		return nil, pa, err
	}

	if selfInfo != nil && selfInfo.UserID != 0 {
		pipe := service.Redis.Pipeline()
		var rankCmd *redis.IntCmd
		var revenueCmd *redis.FloatCmd
		uidStr := strconv.FormatInt(selfInfo.UserID, 10)
		rankCmd = pipe.ZRevRank(key, uidStr)
		revenueCmd = pipe.ZScore(key, uidStr)
		_, err := pipe.Exec()
		if err != nil {
			if serviceredis.IsRedisNil(err) {
				return res, pa, nil
			}
			return nil, pa, err
		}
		if rankCmd != nil {
			selfInfo.assignRank(rankCmd, revenueCmd)
		}
	}

	return res, pa, nil
}

// ActivityRevenueInfo 活动收益信息
func ActivityRevenueInfo(activityDay string, creatorID int64) int64 {
	key := ActivityKey(activityDay)
	val, err := service.Redis.ZScore(key, strconv.FormatInt(creatorID, 10)).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Error(err)
			// PASS
		}
		return 0
	}
	return int64(val)
}

// ActivityCount 活动榜单记录总数
func ActivityCount(activityDay string, paCount int64) int64 {
	count, err := service.Redis.ZCard(ActivityKey(activityDay)).Result()
	if err != nil {
		logger.Error(err)
		return 0
	}
	if count > paCount {
		count = paCount
	}
	return count
}

// ActivityKey 获取活动榜单 key
func ActivityKey(activityDay string) string {
	return keys.KeyUsersRank2.Format(TypeActivity, activityDay)
}

// ActivityGuildKey 获取活动公会榜单 key
func ActivityGuildKey(activityDay string) string {
	return keys.KeyGuildsRank2.Format(TypeActivity, activityDay)
}

// ActivityEventID 通过 eventID 获取活动榜单 key
func ActivityEventID(activityDay int64) string {
	return keys.KeyUsersRank2.Format(TypeActivity, strconv.FormatInt(activityDay, 10))
}

// ActivityGuildEventID 通过 eventID 获取活动公会榜单 key
func ActivityGuildEventID(activityDay int64) string {
	return keys.KeyGuildsRank2.Format(TypeActivity, strconv.FormatInt(activityDay, 10))
}

// NewActivityDay new NewActivityDay
func NewActivityDay(when time.Time) (string, *ActivityInfo) {
	var offset int64
	config.GetAB("activity_time_offset", &offset)
	when = when.Add(time.Duration(offset) * time.Second)
	activityDay := when.Format("20060102")
	v, ok := ActivityInfoMap[activityDay]
	if !ok {
		return "", nil
	}
	return activityDay, &v
}

// Key 榜单 key
func (ai ActivityInfo) Key(catalogID int64) string {
	if ai.Partition {
		return ActivityKey(fmt.Sprintf("%s_%d", ai.ActivityDay, catalogID))
	}
	return ActivityKey(ai.ActivityDay)
}
