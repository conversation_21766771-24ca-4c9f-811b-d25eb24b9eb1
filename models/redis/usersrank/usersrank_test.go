package usersrank

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	logger.InitTestLog()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(tutil.KeyExists(tutil.JSON, Info{}, "user_id", "username",
		"iconurl", "revenue", "rank", "rank_changes", "rank_up", "is_nova"))
}

func TestRankLen(t *testing.T) {
	assert.Equal(t, int64(10), RankLen(TypeHour))
	assert.Equal(t, int64(20), RankLen(TypeHourHomepage))
	assert.Equal(t, int64(50), RankLen(TypeDay))
}

func TestKey(t *testing.T) {
	assert := assert.New(t)
	now := goutil.TimeNow()
	// 今日榜
	when := time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal("test_users/rank/1/20190101", Key(TypeDay, when))
	when = time.Date(2019, 1, 1, 23, 59, 59, 0, now.Location())
	assert.Equal("test_users/rank/1/20190101", Key(TypeDay, when))
	// 周榜
	when = time.Date(2019, 1, 7, 0, 0, 0, 0, now.Location())
	assert.Equal("test_users/rank/2/20190107", Key(TypeWeek, when))
	when = time.Date(2019, 1, 13, 23, 59, 59, 0, now.Location())
	assert.Equal("test_users/rank/2/20190107", Key(TypeWeek, when))
	// 月榜
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal("test_users/rank/3/20190101", Key(TypeMonth, when))
	when = time.Date(2019, 1, 31, 23, 59, 59, 0, now.Location())
	assert.Equal("test_users/rank/3/20190101", Key(TypeMonth, when))
	// 小时榜
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal("test_users/rank/4/2019010100", Key(TypeHour, when))
	when = time.Date(2019, 1, 1, 0, 59, 59, 0, now.Location())
	assert.Equal("test_users/rank/4/2019010100", Key(TypeHour, when))
	// 首页排行榜的直播小时榜
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal("test_users/rank/4/2019010100", Key(TypeHourHomepage, when))
	when = time.Date(2019, 1, 1, 0, 59, 59, 0, now.Location())
	assert.Equal("test_users/rank/4/2019010100", Key(TypeHourHomepage, when))
	// 新人榜
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal("test_users/rank/7/20190101", Key(TypeNova, when))
	when = time.Date(2019, 1, 1, 23, 59, 59, 0, now.Location())
	assert.Equal("test_users/rank/7/20190101", Key(TypeNova, when))
	// panic
	assert.PanicsWithValue("unsupported type: -1", func() { Key(-1, when) })
}

func TestLastRankKey(t *testing.T) {
	assert := assert.New(t)

	// 周榜
	when := time.Date(2024, 3, 4, 15, 36, 0, 0, time.Local)
	assert.Equal("test_users/rank/2/20240226", LastRankKey(TypeWeek, when))
	when = time.Date(2024, 3, 10, 15, 36, 0, 0, time.Local)
	assert.Equal("test_users/rank/2/20240226", LastRankKey(TypeWeek, when))

	// 盲盒主播周榜
	when = time.Date(2024, 3, 4, 15, 36, 0, 0, time.Local)
	assert.Equal("test_users/rank/5/20240226", LastRankKey(TypeGashaponWeek, when))
	when = time.Date(2024, 3, 10, 15, 36, 0, 0, time.Local)
	assert.Equal("test_users/rank/5/20240226", LastRankKey(TypeGashaponWeek, when))

	// 月榜
	when = time.Date(2024, 3, 1, 15, 36, 0, 0, time.Local)
	assert.Equal("test_users/rank/3/20240201", LastRankKey(TypeMonth, when))
	when = time.Date(2024, 3, 31, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/3/20240201", LastRankKey(TypeMonth, when))

	// 小时榜
	when = time.Date(2024, 3, 1, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/4/2024022923", LastRankKey(TypeHour, when))
	when = time.Date(2024, 3, 1, 1, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/4/2024030100", LastRankKey(TypeHour, when))

	// 首页排行榜的直播小时榜
	when = time.Date(2024, 3, 1, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/4/2024022923", LastRankKey(TypeHourHomepage, when))
	when = time.Date(2024, 3, 1, 1, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/4/2024030100", LastRankKey(TypeHourHomepage, when))

	// 今日榜
	when = time.Date(2024, 3, 1, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/1/20240229", LastRankKey(TypeDay, when))
	when = time.Date(2024, 3, 31, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/1/20240330", LastRankKey(TypeDay, when))

	// 新人榜
	when = time.Date(2024, 3, 1, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/7/20240229", LastRankKey(TypeNova, when))
	when = time.Date(2024, 3, 31, 0, 0, 0, 0, time.Local)
	assert.Equal("test_users/rank/7/20240330", LastRankKey(TypeNova, when))
}

func TestDeadline(t *testing.T) {
	assert := assert.New(t)
	now := goutil.TimeNow()
	// 今日榜
	expected := time.Date(2019, 1, 2, 0, 0, 0, 0, now.Location())
	when := time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal(expected, Deadline(TypeDay, when))
	when = time.Date(2019, 1, 1, 23, 59, 59, 0, now.Location())
	assert.Equal(expected, Deadline(TypeDay, when))
	// 周榜
	expected = time.Date(2019, 1, 14, 0, 0, 0, 0, now.Location())
	when = time.Date(2019, 1, 7, 0, 0, 0, 0, now.Location())
	assert.Equal(expected, Deadline(TypeWeek, when))
	when = time.Date(2019, 1, 13, 23, 59, 59, 0, now.Location())
	assert.Equal(expected, Deadline(TypeWeek, when))
	// 月榜
	expected = time.Date(2019, 2, 1, 0, 0, 0, 0, now.Location())
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal(expected, Deadline(TypeMonth, when))
	when = time.Date(2019, 1, 31, 23, 59, 59, 0, now.Location())
	assert.Equal(expected, Deadline(TypeMonth, when))
	// 小时榜
	expected = time.Date(2019, 1, 1, 1, 0, 0, 0, now.Location())
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal(expected, Deadline(TypeHour, when))
	when = time.Date(2019, 1, 1, 0, 59, 59, 0, now.Location())
	assert.Equal(expected, Deadline(TypeHour, when))
	// 新人榜
	expected = time.Date(2019, 1, 2, 0, 0, 0, 0, now.Location())
	when = time.Date(2019, 1, 1, 0, 0, 0, 0, now.Location())
	assert.Equal(expected, Deadline(TypeNova, when))
	when = time.Date(2019, 1, 1, 23, 59, 59, 0, now.Location())
	assert.Equal(expected, Deadline(TypeNova, when))
	// panic
	assert.PanicsWithValue("unsupported type: -2", func() { Deadline(-2, when) })
}

func TestTTL(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 12, 31, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	now := goutil.TimeNow()
	expireTime := time.Date(2024, 1, 10, 0, 0, 0, 0, now.Location())
	assert.Equal(expireTime.Sub(now), ttl(TypeWeek))

	expireTime = time.Date(2024, 2, 10, 0, 0, 0, 0, now.Location())
	assert.Equal(expireTime.Sub(now), ttl(TypeMonth))

	assert.Equal(3*24*60*time.Minute, ttl(TypeDay))
	assert.Equal(3*60*time.Minute, ttl(TypeHour))
	assert.Equal(3*24*60*time.Minute, ttl(TypeNova))
	assert.PanicsWithValue("unsupported type: -3", func() { ttl(-3) })
}

func TestTop3(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(1000000000, 0)
	testData := []*redis.Z{
		{Score: 10, Member: 10},
		{Score: 11, Member: 11},
		{Score: 12, Member: 12},
	}
	for i := TypeDay; i < typeLimit; i++ {
		// 跳过非常规榜单
		if i == 6 || i == TypeLastMonth { // 活动榜单的 type 6 和上期月榜的 type 8
			continue
		}
		key := Key(i, when)
		assert.NoError(service.Redis.ZAdd(key, testData...).Err())
		assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())
	}

	ranks, err := Top3(when)
	require.NoError(err)
	require.Len(ranks, 5)
	assert.NotEmpty(ranks[TypeDay])
	assert.NotEmpty(ranks[TypeWeek])
	assert.NotEmpty(ranks[TypeMonth])
	assert.NotEmpty(ranks[TypeHour])
	assert.NotEmpty(ranks[TypeNova])
	require.Len(ranks[TypeDay], 3)
	assert.Equal([3]int64{10, 11, 12},
		[3]int64{ranks[TypeDay][2].UserID, ranks[TypeDay][1].UserID, ranks[TypeDay][0].UserID})
	assert.Equal([3]int64{1, 2, 3},
		[3]int64{ranks[TypeDay][0].Rank, ranks[TypeDay][1].Rank, ranks[TypeDay][2].Rank})
}

func TestListLastHourTop3CreatorIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testLastRankKey := LastRankKey(TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(testLastRankKey).Err())

	lastRankMembers := []*redis.Z{
		{Score: float64(4), Member: 9074501},
		{Score: float64(3), Member: "9074502+"},
		{Score: float64(2), Member: 9074503},
	}
	require.NoError(service.Redis.ZAdd(testLastRankKey, lastRankMembers...).Err())

	lastHourTop3CreatorIDs, err := ListLastHourTop3CreatorIDs()
	require.NoError(err)
	require.Equal(3, len(lastHourTop3CreatorIDs))
	assert.EqualValues(9074501, lastHourTop3CreatorIDs[0])
	assert.Zero(lastHourTop3CreatorIDs[1])
	assert.EqualValues(9074503, lastHourTop3CreatorIDs[2])
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(1230000000, 0)
	key := Key(TypeDay, when)
	assert.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 10, Member: 10},
		&redis.Z{Score: 11, Member: 11},
		&redis.Z{Score: 12, Member: 12},
		&redis.Z{Score: 13, Member: 13}).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	lastRankMap := map[int64]int64{
		13: 2,
		12: 1,
		11: 3,
	}
	cacheKey := lastRankCacheKey(TypeDay, when)
	service.Cache5Min.SetDefault(cacheKey, lastRankMap)

	one := Info{UserID: 19, Rank: 100}
	rank, err := Find(TypeDay, when, &one)
	require.NoError(err)
	require.Len(rank, 4)
	assert.Equal([4]int64{10, 11, 12, 13},
		[4]int64{rank[3].UserID, rank[2].UserID, rank[1].UserID, rank[0].UserID})
	assert.Equal(int64(2), rank[2].RankUp)
	assert.Equal(int64(3), rank[2].Rank)
	assert.EqualValues(1, **rank[0].RankChanges)
	assert.EqualValues(-1, **rank[1].RankChanges)
	assert.Zero(**rank[2].RankChanges)
	assert.Nil(*rank[3].RankChanges)
	assert.Zero(one.Rank)

	// 测试不查询 rank change
	one = Info{UserID: 19, Rank: 100}
	rank, err = Find(TypeDay, when, &one, FindOptions{DisableFindRankChange: true})
	require.NoError(err)
	require.Len(rank, 4)
	assert.Equal([4]int64{10, 11, 12, 13},
		[4]int64{rank[3].UserID, rank[2].UserID, rank[1].UserID, rank[0].UserID})
	assert.Equal(int64(2), rank[2].RankUp)
	assert.Equal(int64(3), rank[2].Rank)
	assert.Nil(rank[0].RankChanges)
	assert.Nil(rank[1].RankChanges)
	assert.Nil(rank[2].RankChanges)
	assert.Nil(rank[3].RankChanges)
	assert.Zero(one.Rank)

	// 测试指定 rank count
	one = Info{UserID: 19, Rank: 100}
	rank, err = Find(TypeDay, when, &one, FindOptions{RankCount: 2})
	require.NoError(err)
	require.Len(rank, 2)
	assert.Equal([2]int64{12, 13}, [2]int64{rank[1].UserID, rank[0].UserID})

	// 测试 info.assignRank 没查到的情况
	one.UserID = 10
	_, err = Find(TypeDay, when, &one)
	require.NoError(err)
	assert.Equal(int64(4), one.Rank)
	assert.Equal(int64(10), one.Revenue)
}

func TestFindLastRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2024, 3, 13, 0, 0, 0, 0, time.Local)
	cacheKey := lastRankCacheKey(TypeHour, when)
	service.Cache5Min.Delete(cacheKey)

	testLastRankKet := LastRankKey(TypeHour, when)
	require.NoError(service.Redis.Del(testLastRankKet).Err())

	lastRankMembers := []*redis.Z{
		{Score: float64(4), Member: 9074501},
		{Score: float64(3), Member: 9074502},
		{Score: float64(2), Member: 9074503},
	}
	require.NoError(service.Redis.ZAdd(testLastRankKet, lastRankMembers...).Err())

	// 测试缓存没有数据
	lastRanks, err := FindLastRank(TypeHour, when)
	require.NoError(err)
	require.Equal(3, len(lastRanks))
	assert.EqualValues(9074501, lastRanks[0].UserID)
	assert.EqualValues(9074502, lastRanks[1].UserID)
	assert.EqualValues(9074503, lastRanks[2].UserID)

	res, ok := service.Cache5Min.Get(cacheKey)
	assert.True(ok)
	assert.Equal(lastRanks, res.([]Info))

	lastRanks, err = FindLastRank(TypeHour, when)
	require.NoError(err)
	require.Equal(3, len(lastRanks))
}

func TestFindLastRankMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2024, 3, 13, 0, 0, 0, 0, time.Local)
	cacheKey := lastRankCacheKey(TypeHour, when)
	service.Cache5Min.Delete(cacheKey)

	testLastRankKet := LastRankKey(TypeHour, when)
	require.NoError(service.Redis.Del(testLastRankKet).Err())

	lastRankMembers := []*redis.Z{
		{Score: float64(4), Member: 9074501},
		{Score: float64(3), Member: 9074502},
		{Score: float64(2), Member: 9074503},
	}
	require.NoError(service.Redis.ZAdd(testLastRankKet, lastRankMembers...).Err())

	lastRankMap, err := findLastRankMap(TypeHour, when)
	require.NoError(err)
	require.Equal(3, len(lastRankMap))
	expectedUserID := []int64{9074501, 9074502, 9074503}
	for i, v := range expectedUserID {
		rank, ok := lastRankMap[v]
		assert.True(ok)
		assert.EqualValues(i+1, rank)
	}
}

func TestFindRankInfoWithRankNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	key := Key(TypeDay, now)
	_, err := service.Redis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		pipeliner.Del(key)
		pipeliner.ZAdd(key, &redis.Z{Score: 10, Member: 10}, &redis.Z{Score: 11, Member: 11},
			&redis.Z{Score: 12, Member: 12}, &redis.Z{Score: 13, Member: 13})
		pipeliner.Expire(key, 5*time.Minute)
		return nil
	})
	require.NoError(err)

	// 测试获取榜单数据
	info, err := FindRankInfoWithRankNum(TypeDay, now, 4)
	require.NoError(err)
	require.Len(info, 4)
	assert.EqualValues(13, info[0].UserID)
	assert.EqualValues(1, info[0].Rank)
	assert.EqualValues(10, info[3].UserID)
	assert.EqualValues(4, info[3].Rank)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(1234000000, 0)
	key := Key(TypeDay, when)
	assert.NoError(service.Redis.ZAdd(key, &redis.Z{Score: 12, Member: 12}).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	res, err := FindOne(TypeDay, when, 999)
	assert.NoError(err)
	require.NotNil(res)
	assert.Zero(res.Revenue)

	res, err = FindOne(TypeDay, when, 12)
	require.NoError(err)
	assert.Equal(int64(12), res.Revenue)
	assert.Equal(int64(1), res.Rank)
}

func TestFindHourRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(123000000, 0)
	key := Key(TypeHour, when)
	assert.NoError(service.Redis.ZAdd(key,
		&redis.Z{Score: 10, Member: 10},
		&redis.Z{Score: 11, Member: 11},
		&redis.Z{Score: 12, Member: 12},
		&redis.Z{Score: 13, Member: 13}).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	lastRankMap := map[int64]int64{
		13: 2,
		12: 1,
		11: 3,
	}
	cacheKey := lastRankCacheKey(TypeHour, when)
	service.Cache5Min.SetDefault(cacheKey, lastRankMap)

	one := Info{UserID: 19, Rank: 100}
	top3, top10, err := FindHourRank(when, &one)
	require.NoError(err)
	require.Len(top10, 4)
	assert.Empty(top3)
	assert.Equal([4]int64{10, 11, 12, 13},
		[4]int64{top10[3].UserID, top10[2].UserID, top10[1].UserID, top10[0].UserID})
	assert.Equal(int64(1), top10[0].RankUp)
	assert.Equal(int64(2), top10[2].RankUp)
	assert.Equal(int64(3), top10[2].Rank)
	assert.EqualValues(1, **top10[0].RankChanges)
	assert.EqualValues(-1, **top10[1].RankChanges)
	assert.Zero(**top10[2].RankChanges)
	assert.Nil(*top10[3].RankChanges)
	assert.Zero(one.Rank)
}

func TestMakeInfoList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2024, 3, 5, 0, 0, 0, 0, time.Local)
	testRankKet := Key(TypeHour, when)

	require.NoError(service.Redis.Del(testRankKet).Err())

	rankMembers := []*redis.Z{
		{Score: float64(5), Member: 9074501},
		{Score: float64(4), Member: 9074502},
		{Score: float64(3), Member: 9074503},
		{Score: float64(2), Member: 9074504},
	}
	require.NoError(service.Redis.ZAdd(testRankKet, rankMembers...).Err())

	lastRankMap := map[int64]int64{
		9074501: 1,
		9074502: 3,
		9074503: 2,
	}
	rankCmd := service.Redis.ZRevRangeWithScores(testRankKet, 0, RankLen(TypeHour)-1)
	info, err := makeInfoList(rankCmd, lastRankMap, 0)
	require.NoError(err)
	require.NotNil(info)
	assert.EqualValues(0, **info[0].RankChanges)
	assert.EqualValues(1, **info[1].RankChanges)
	assert.EqualValues(-1, **info[2].RankChanges)
	assert.Nil(*info[3].RankChanges)
}

func Test_addRevenueType(t *testing.T) {
	t.Run("普通用户", func(t *testing.T) {
		assert := assert.New(t)

		types := addRevenueType(1, 1)
		assert.Equal([]int{TypeHour, TypeDay, TypeWeek, TypeMonth}, types)
	})

	t.Run("新星主播", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := NovaKey(goutil.TimeNow(), NovaKeySlot(2))
		err := service.Redis.SAdd(key, 2).Err()
		require.NoError(err)
		defer service.Redis.Del(key)

		types := addRevenueType(2, 1)
		assert.Equal([]int{TypeHour, TypeDay, TypeWeek, TypeMonth, TypeNova}, types)
	})

	t.Run("热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		err := service.Redis.ZAdd(key, &redis.Z{
			Score:  -1,
			Member: 3,
		}).Err()
		require.NoError(err)
		defer service.Redis.Del(key)

		types := addRevenueType(3, 3)
		assert.Equal([]int{}, types)
	})
}

func TestAddRevenue(t *testing.T) {
	testUserID := int64(10)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	t.Run("普通用户", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		now := goutil.TimeNow()
		keys := []string{
			Key(TypeHour, now),
			Key(TypeDay, now),
			Key(TypeWeek, now),
			Key(TypeMonth, now),
		}
		require.NoError(service.Redis.Del(keys...).Err())

		rankChange, err := AddRevenue(testUserID, 1, 1)
		require.NoError(err)
		assert.Equal(&RankChange{UserID: testUserID, PrevRank: 0, Rank: 1, Score: 1}, rankChange)

		rankChange, err = AddRevenue(testUserID, 1, 1)
		require.NoError(err)
		assert.Equal(&RankChange{UserID: testUserID, PrevRank: 1, Rank: 1, Score: 2}, rankChange)

		pipe := service.Redis.TxPipeline()
		c1 := pipe.ZScore(keys[0], "10")
		c2 := pipe.ZScore(keys[1], "10")
		c3 := pipe.ZScore(keys[2], "10")
		c4 := pipe.ZScore(keys[3], "10")
		t1 := pipe.TTL(keys[0])
		t2 := pipe.TTL(keys[1])
		t3 := pipe.TTL(keys[2])
		t4 := pipe.TTL(keys[3])
		_, err = pipe.Exec()
		require.NoError(err)
		assert.GreaterOrEqual(c1.Val(), 2.0)
		assert.GreaterOrEqual(c2.Val(), 2.0)
		assert.GreaterOrEqual(c3.Val(), 2.0)
		assert.GreaterOrEqual(c4.Val(), 2.0)
		assert.True(t1.Val() > 0)
		assert.True(t2.Val() > 0)
		assert.True(t3.Val() > 0)
		assert.True(t4.Val() > 0)
	})

	t.Run("新星主播", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := NovaKey(goutil.TimeNow(), NovaKeySlot(testUserID))
		require.NoError(service.Redis.SAdd(key, testUserID).Err())
		defer service.Redis.Del(key)

		now := goutil.TimeNow()
		keys := []string{
			Key(TypeHour, now),
			Key(TypeDay, now),
			Key(TypeWeek, now),
			Key(TypeMonth, now),
			Key(TypeNova, now),
		}
		require.NoError(service.Redis.Del(keys...).Err())

		rankChange, err := AddRevenue(testUserID, 1, 1)
		require.NoError(err)
		assert.Equal(&RankChange{UserID: testUserID, PrevRank: 0, Rank: 1, Score: 1}, rankChange)

		rankChange, err = AddRevenue(testUserID, 1, 1)
		require.NoError(err)
		assert.Equal(&RankChange{UserID: testUserID, PrevRank: 1, Rank: 1, Score: 2}, rankChange)

		pipe := service.Redis.TxPipeline()
		c1 := pipe.ZScore(keys[0], "10")
		c2 := pipe.ZScore(keys[1], "10")
		c3 := pipe.ZScore(keys[2], "10")
		c4 := pipe.ZScore(keys[3], "10")
		c5 := pipe.ZScore(keys[4], "10")
		t1 := pipe.TTL(keys[0])
		t2 := pipe.TTL(keys[1])
		t3 := pipe.TTL(keys[2])
		t4 := pipe.TTL(keys[3])
		t5 := pipe.TTL(keys[4])
		_, err = pipe.Exec()
		require.NoError(err)
		assert.GreaterOrEqual(c1.Val(), 2.0)
		assert.GreaterOrEqual(c2.Val(), 2.0)
		assert.GreaterOrEqual(c3.Val(), 2.0)
		assert.GreaterOrEqual(c4.Val(), 2.0)
		assert.GreaterOrEqual(c5.Val(), 2.0)
		assert.True(t1.Val() > 0)
		assert.True(t2.Val() > 0)
		assert.True(t3.Val() > 0)
		assert.True(t4.Val() > 0)
		assert.True(t5.Val() > 0)
	})

	t.Run("热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		require.NoError(service.Redis.ZAdd(key, &redis.Z{
			Score:  -1,
			Member: 1,
		}).Err())
		defer service.Redis.Del(key)

		now := goutil.TimeNow()
		keys := []string{
			Key(TypeHour, now),
			Key(TypeDay, now),
			Key(TypeWeek, now),
			Key(TypeMonth, now),
		}
		require.NoError(service.Redis.Del(keys...).Err())

		rankChange, err := AddRevenue(testUserID, 1, 1)
		require.NoError(err)
		assert.Nil(rankChange)

		rankChange, err = AddRevenue(testUserID, 1, 1)
		require.NoError(err)
		assert.Nil(rankChange)

		pipe := service.Redis.TxPipeline()
		c1 := pipe.ZScore(keys[0], "10")
		c2 := pipe.ZScore(keys[1], "10")
		c3 := pipe.ZScore(keys[2], "10")
		c4 := pipe.ZScore(keys[3], "10")
		t1 := pipe.TTL(keys[0])
		t2 := pipe.TTL(keys[1])
		t3 := pipe.TTL(keys[2])
		t4 := pipe.TTL(keys[3])
		_, _ = pipe.Exec()
		assert.EqualValues(c1.Val(), 0)
		assert.GreaterOrEqual(c2.Val(), 2.0)
		assert.GreaterOrEqual(c3.Val(), 2.0)
		assert.GreaterOrEqual(c4.Val(), 2.0)
		assert.True(t1.Val() <= 0)
		assert.True(t2.Val() > 0)
		assert.True(t3.Val() > 0)
		assert.True(t4.Val() > 0)
	})
}

func TestHourRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	})
	defer goutil.SetTimeNow(nil)

	// 榜单不满的情况
	key := Key(TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())
	rank, rankup, err := HourRank(123)
	require.NoError(err)
	assert.Zero(rank)
	assert.Equal(int64(1), rankup)

	// 额外查询下第二名没有的情况
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Member: 1, Score: float64(1)}).Err())
	rank, rankup, err = HourRank(1)
	require.NoError(err)
	assert.Equal(int64(1), rank)
	assert.Equal(int64(1), rankup)
	z := make([]*redis.Z, 19)
	for i := 0; i < len(z); i++ {
		userID := i + 2
		z[i] = &redis.Z{Member: userID, Score: float64(userID)}
	}
	require.NoError(service.Redis.ZAdd(key, z...).Err())
	// 在榜外，有分数
	rank, rankup, err = HourRank(2)
	require.NoError(err)
	assert.Zero(rank)
	assert.Equal(int64(10), rankup)
	// 在榜外，没分数
	rank, rankup, err = HourRank(21)
	require.NoError(err)
	assert.Zero(rank)
	assert.Equal(int64(12), rankup)
	// 榜内，不是第一名
	for i := 11; i < 20; i++ {
		rank, rankup, err = HourRank(int64(i))
		require.NoError(err)
		assert.Equal(int64(21-i), rank)
		assert.Equal(int64(2), rankup)
	}
	// 第一名
	rank, rankup, err = HourRank(20)
	require.NoError(err)
	assert.Equal(int64(1), rank)
	assert.Equal(int64(1), rankup)
}

func TestAddGashaponWeek(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer goutil.SetTimeNow(nil)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 9, 13, 0, 0, 0, 0, time.Local)
	})
	key := Key(TypeGashaponWeek, goutil.TimeNow())
	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: 1, Member: 12}).Err())

	require.NoError(AddGashaponWeek(1, 12, 10))
	pipe := service.Redis.Pipeline()
	valCmd := pipe.ZScore(key, strconv.Itoa(12))
	ttlCmd := pipe.TTL(key)
	_, err := pipe.Exec()
	require.NoError(err)
	assert.EqualValues(10+1, valCmd.Val())
	assert.Greater(ttlCmd.Val(), time.Duration(0))
}

func TestNovaKeySlot(t *testing.T) {
	assert := assert.New(t)

	slot := NovaKeySlot(10)
	assert.EqualValues(0, slot)

	slot = NovaKeySlot(19)
	assert.EqualValues(9, slot)

	slot = NovaKeySlot(-11)
	assert.EqualValues(0, slot)
}

func TestNovaKey(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	key := NovaKey(goutil.TimeNow(), 1)
	assert.Equal(key, "test_users/nova/20000101/01")
}

func TestIsNova(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1000)
	key := NovaKey(goutil.TimeNow(), 0)

	require.NoError(service.Redis.SAdd(key, testUserID).Err())
	exists, err := IsNova(testUserID)
	require.NoError(err)
	assert.True(exists)

	require.NoError(service.Redis.SRem(key, testUserID).Err())
	exists, err = IsNova(testUserID)
	require.NoError(err)
	assert.False(exists)
}

func TestIsSuppressionHotRoom(t *testing.T) {
	t.Run("非热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		require.NoError(service.Redis.Del(key).Err())

		exists, err := IsSuppressionHotRoom(1)
		require.NoError(err)
		assert.False(exists)
	})

	t.Run("永不过期热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		require.NoError(service.Redis.ZAdd(key, &redis.Z{
			Score:  -1,
			Member: 1,
		}).Err())
		defer service.Redis.Del(key)

		exists, err := IsSuppressionHotRoom(1)
		require.NoError(err)
		assert.True(exists)
	})

	t.Run("本场结束过期热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		require.NoError(service.Redis.ZAdd(key, &redis.Z{
			Score:  0,
			Member: 1,
		}).Err())
		defer service.Redis.Del(key)

		exists, err := IsSuppressionHotRoom(1)
		require.NoError(err)
		assert.True(exists)
	})

	t.Run("未过期热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		require.NoError(service.Redis.ZAdd(key, &redis.Z{
			Score:  float64(goutil.TimeNow().Add(time.Hour).Unix()),
			Member: 1,
		}).Err())
		defer service.Redis.Del(key)

		exists, err := IsSuppressionHotRoom(1)
		require.NoError(err)
		assert.True(exists)
	})

	t.Run("过期热度限制直播间", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		key := keys.KeyRoomsSuppressionHotList0.Format()
		require.NoError(service.Redis.ZAdd(key, &redis.Z{
			Score:  float64(goutil.TimeNow().Add(-time.Hour).Unix()),
			Member: 1,
		}).Err())
		defer service.Redis.Del(key)

		exists, err := IsSuppressionHotRoom(1)
		require.NoError(err)
		assert.False(exists)
	})
}

func TestListHotSuppressionRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyRoomsSuppressionHotList0.Format()
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	// 测试返回空数组
	data, err := ListHotSuppressionRoomIDs()
	require.NoError(err)
	assert.Empty(data)

	// 测试成功返回
	now := goutil.TimeNow().Unix()
	members := []*redis.Z{
		{Member: 1, Score: float64(-1)},
		{Member: 2, Score: float64(0)},
		{Member: 3, Score: float64(now)},
		{Member: 4, Score: float64(now - 3)},
		{Member: 5, Score: float64(now + 3)},
	}
	err = service.Redis.ZAdd(key, members...).Err()
	require.NoError(err)
	data, err = ListHotSuppressionRoomIDs()
	require.NoError(err)
	require.NotEmpty(data)
	assert.EqualValues([]int64{1, 2, 3, 5}, data)
}

func TestAddNovaRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1000)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := Key(TypeNova, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())

	err := AddNovaRevenue(testUserID, 1)
	require.NoError(err)
	err = AddNovaRevenue(testUserID, 1) // 运行两次保证 TTL 的两种情况都能实现
	require.NoError(err)

	pipe := service.Redis.Pipeline()
	score := pipe.ZScore(key, strconv.FormatInt(testUserID, 10))
	ttl := pipe.TTL(key)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.EqualValues(score.Val(), 2)
	assert.Greater(ttl.Val(), time.Duration(0))
}

func TestFindRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Unix(1659353217, 0)
	testData := []*redis.Z{
		{Score: 10, Member: 1},
		{Score: 11, Member: 2},
		{Score: 12, Member: 3},
		{Score: 13, Member: 4},
	}
	key := Key(TypeMonth, when)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, testData...).Err())
	results, err := FindRank(when, TypeMonth)
	require.NoError(err)
	require.Len(results, 4)
	assert.Equal([3]int64{4, 3, 2},
		[3]int64{results[0].UserID, results[1].UserID, results[2].UserID})
	assert.Equal([3]int64{13, 12, 11},
		[3]int64{results[0].Revenue, results[1].Revenue, results[2].Revenue})
}
