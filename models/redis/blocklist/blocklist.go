package blocklist

import (
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// StatusBlockUserForever 用户永久加入黑名单
const StatusBlockUserForever = -1

// Exists 判断用户是否在主站管理后台的黑名单中
// NOTICE: 该函数不是判断用户与用户之间的拉黑关系
func Exists(userID int64) (bool, error) {
	key := keys.KeyUsersBlockList0.Format()
	userStr := strconv.FormatInt(userID, 10)
	value, err := service.Redis.ZScore(key, userStr).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		}
		return false, err
	}
	return value <= StatusBlockUserForever || int64(value) > goutil.TimeNow().Unix(), nil
}

// KeyUserBlock 返回用户黑名单缓存 key（缓存用户所有拉黑的用户 ID）
func KeyUserBlock(userID int64) string {
	return keys.LRUKeyUserBlock1.Format(userID)
}

// 缓存用户黑名单，黑名单缓存有效期 30 分钟
func load(userID int64) ([]int64, error) {
	userIDs, err := userapi.UserBlockList(mrpc.NewUserContextFromEnv(), userID, userapi.BlockListTypeBlockedByUser)
	if err != nil {
		return nil, err
	}
	// NOTICE: 防止缓存穿透，如果用户没有拉黑任何用户，则缓存一个 0
	if len(userIDs) <= 0 {
		userIDs = append(userIDs, 0)
	}
	vals := make([]interface{}, 0, len(userIDs))
	for _, id := range userIDs {
		vals = append(vals, id)
	}
	_, err = service.LRURedis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		key := KeyUserBlock(userID)
		pipeliner.SAdd(key, vals...)
		pipeliner.Expire(key, time.Minute) // 有效期 1 分钟
		return nil
	})
	if err != nil {
		return nil, err
	}
	return userIDs, nil
}

// Remove 将 block_user_id 从 user_id 的黑名单缓存中移除
func Remove(userID, blockUserID int64) error {
	key := KeyUserBlock(userID)
	err := service.LRURedis.SRem(key, blockUserID).Err()
	if err != nil {
		return err
	}
	return nil
}

// Clear 清除用户黑名单缓存
func Clear(userID int64) error {
	key := KeyUserBlock(userID)
	err := service.LRURedis.Del(key).Err()
	if err != nil {
		return err
	}
	return err
}

// IsBlocked user_id 是否拉黑 block_user_id
func IsBlocked(userID, blockUserID int64) (bool, error) {
	key := KeyUserBlock(userID)
	pipe := service.LRURedis.Pipeline()
	existsCmd := pipe.Exists(key)
	blockedCmd := pipe.SIsMember(key, blockUserID)
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}

	// 缓存若不存在，重新加载黑名单数据
	exists, _ := existsCmd.Result()
	if !goutil.IntToBool(int(exists)) {
		blockedUserIDs, err := load(userID)
		if err != nil {
			return false, err
		}
		return goutil.HasElem(blockedUserIDs, blockUserID), nil
	}

	return blockedCmd.Val(), nil
}
