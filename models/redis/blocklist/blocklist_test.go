package blocklist

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var testForeverBlockUserID int64 = 2020101402

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestBlockListExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	testData := []*redis.Z{
		{Score: float64(now.Add(-time.Minute).Unix()), Member: 2020101401},
		{Score: float64(now.Add(time.Minute).Unix()), Member: 2020101402},
		{Score: -1, Member: testForeverBlockUserID},
	}
	key := keys.KeyUsersBlockList0.Format()
	require.NoError(service.Redis.ZAdd(key, testData...).Err())

	exists, err := Exists(2020101401)
	require.NoError(err)
	assert.False(exists)

	exists, err = Exists(2020101402)
	require.NoError(err)
	assert.True(exists)

	exists, err = Exists(testForeverBlockUserID)
	require.NoError(err)
	assert.True(exists)

	exists, err = Exists(99999999)
	require.NoError(err)
	assert.False(exists)
}

func TestLoadAndClear(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testBlockUSerID := int64(99)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			switch {
			case input.(map[string]any)["user_id"].(int64) <= 0:
				return nil, actionerrors.ErrParams
			case input.(map[string]any)["user_id"].(int64) == testBlockUSerID:
				return handler.M{"block_list": []int64{1, 2, 3, 4, 5}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(Clear(testBlockUSerID))

	blockedUserIDs, err := load(0)
	require.EqualError(err, "参数错误")
	require.Empty(blockedUserIDs)

	blockedUserIDs, err = load(testBlockUSerID)
	require.NoError(err)
	require.Equal([]int64{1, 2, 3, 4, 5}, blockedUserIDs)
	key := KeyUserBlock(testBlockUSerID)
	userIDs, err := service.LRURedis.SMembers(key).Result()
	require.NoError(err)
	assert.Len(userIDs, 5)

	err = Clear(testBlockUSerID)
	require.NoError(err)
	userIDs, err = service.LRURedis.SMembers(key).Result()
	require.NoError(err)
	assert.Len(userIDs, 0)
}

func TestRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_list": []int64{1, 2, 3, 4, 5}}, nil
		})
	defer cleanup()
	testBlockUserID := int64(99)
	require.NoError(Clear(testBlockUserID))

	require.NoError(service.LRURedis.SAdd(KeyUserBlock(99), 1000).Err())
	block, err := IsBlocked(testBlockUserID, 1000)
	require.NoError(err)
	assert.True(block)

	require.NoError(Remove(testBlockUserID, 1000))
	block, err = IsBlocked(testBlockUserID, 1000)
	require.NoError(err)
	assert.False(block)
}

func TestIsBlocked(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testBlockUserID := int64(99)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testBlockUserID {
				return handler.M{"block_list": []int64{1, 2, 3, 4, 5}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(Clear(testBlockUserID))

	block, err := IsBlocked(testBlockUserID, 1)
	require.NoError(err)
	assert.True(block)

	block, err = IsBlocked(testBlockUserID, 1111)
	require.NoError(err)
	assert.False(block)

	block, err = IsBlocked(testBlockUserID, 1)
	require.NoError(err)
	assert.True(block)

	require.NoError(Clear(1000))
	block, err = IsBlocked(1000, 1111)
	require.NoError(err)
	assert.False(block)
}
