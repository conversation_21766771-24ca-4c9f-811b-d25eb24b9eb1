package rankpoint

import (
	"time"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 需求文档：https://info.missevan.com/pages/viewpage.action?pageId=80380736

// PointShareDaily 分享加的积分
const PointShareDaily int64 = 5

// FieldShareLock1 房间分享锁
// 参数：room_id
const FieldShareLock1 cache.KeyFormat = "sharelock_%d"

// KeyRoomsRankPointDaily 用户为主播增加人气的详情 key
func KeyRoomsRankPointDaily(userID int64) string {
	now := goutil.TimeNow()
	return keys.KeyRoomsRankPointDaily2.Format(userID, now.Format(util.TimeFormatYMDWithNoSpace))
}

// KeyRoomsViewDaily 每日访问该直播间的用户 ID key
func KeyRoomsViewDaily(roomID int64) string {
	now := goutil.TimeNow()
	return keys.KeyRoomsViewDaily2.Format(roomID, now.Format(util.TimeFormatYMDWithNoSpace))
}

// AddUserShareRoomDaily 增加用户分享直播间次数
func AddUserShareRoomDaily(userID int64, roomID int64) (bool, error) {
	if !allowAddRankPoint(userID) {
		return false, nil
	}
	key := KeyRoomsRankPointDaily(userID)
	field := FieldShareLock1.Format(roomID)
	pipe := service.Redis.TxPipeline()
	cmd := pipe.HSetNX(key, field, 1)
	pipe.Expire(key, 36*time.Hour)
	_, err := pipe.Exec()
	if err != nil {
		return false, err
	}
	return cmd.Result()
}

func allowAddRankPoint(userID int64) bool {
	return !mowangskuser.IsSpecialRolesUser([]role.Role{
		role.LiveAdmin,
		role.LiveJudgement,
		role.LiveOperator,
		role.LiveFinance,
	}, keys.LocalKeyUserNoRankPointUserIDs0.Format(), userID)
}

// AddViewCountDaily 增加房间访问次数
func AddViewCountDaily(userID, roomID int64) (before, after int64, err error) {
	key := KeyRoomsViewDaily(roomID)
	pipe := service.Redis.TxPipeline()
	cmdAdd := pipe.SAdd(key, userID)
	cmdCount := pipe.SCard(key)
	pipe.Expire(key, 36*time.Hour)
	_, err = pipe.Exec()
	if err != nil {
		return 0, 0, err
	}
	after = cmdCount.Val()
	before = after - cmdAdd.Val()
	return before, after, nil
}
