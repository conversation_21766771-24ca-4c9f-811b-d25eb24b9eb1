package rankpoint

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestKeyRoomsRankPointDaily(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := KeyRoomsRankPointDaily(12)
	assert.Equal("test_rooms/rankpoint/12/20220707", key)
}

func TestKeyRoomsViewDaily(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := KeyRoomsViewDaily(123)
	assert.Equal("test_rooms/view/123/20220707", key)
}

func TestAddUserShareRoomDaily(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	key := KeyRoomsRankPointDaily(12)
	require.NoError(service.Redis.HDel(key, FieldShareLock1.Format(1)).Err())

	// 不让加分享的用户
	ok, err := AddUserShareRoomDaily(10, 1)
	require.NoError(err)
	assert.False(ok)

	ok, err = AddUserShareRoomDaily(12, 1)
	require.NoError(err)
	assert.True(ok)

	ok, err = AddUserShareRoomDaily(12, 1)
	require.NoError(err)
	assert.False(ok)
}

func TestAddViewCountDaily(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := KeyRoomsViewDaily(123)
	require.NoError(service.Redis.SRem(key, 12).Err())
	before, after, err := AddViewCountDaily(12, 123)
	require.NoError(err)
	assert.Equal(before+1, after)
	before, after, err = AddViewCountDaily(12, 123)
	require.NoError(err)
	assert.Equal(before, after)
}

func TestAllowAddRankPoint(t *testing.T) {
	assert := assert.New(t)

	assert.False(allowAddRankPoint(10))
	assert.False(allowAddRankPoint(10))
	assert.True(allowAddRankPoint(123))
}
