package messagelimit

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoom = &room.Room{
			Helper: room.Helper{
				RoomID:    11111111,
				CreatorID: 1000000,
			},
		}
		testUser = &liveuser.Simple{
			UID: 999999,
		}
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	now := goutil.TimeNow()
	err := service.Redis.Del(
		KeyRoomMessageLimit(testRoom.RoomID, now),
		KeyUserMessageLimit(testUser.UserID(), now),
		KeyUserMessageRepeatLimit(testUser.UserID(), now),
	).Err()
	require.NoError(err)

	status := Status(testRoom, testUser, "11111111")
	assert.Equal(models.MessageStatusNormal, status)

	status = Status(testRoom, testUser, "11111111")
	assert.Equal(models.MessageStatusLimited, status)
}

func TestKeyUserMessageLimit(t *testing.T) {
	assert := assert.New(t)

	var (
		testUserID = int64(100000)
	)

	key := KeyUserMessageLimit(testUserID, time.Date(2020, 1, 1, 0, 0, 10, 0, time.Local))
	assert.Equal("test_users/message/100000/counter/10", key)
}

func TestIncrUserMessageLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(100000)
		now        = goutil.TimeNow()
	)
	require.NoError(service.Redis.Del(KeyUserMessageLimit(testUserID, now)).Err())

	pipe := service.Redis.Pipeline()
	f := incrUserMessageLimit(pipe, testUserID, now)
	_, err := pipe.Exec()
	require.NoError(err)
	assert.False(f())

	f = incrUserMessageLimit(pipe, testUserID, now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.False(f())

	f = incrUserMessageLimit(pipe, testUserID, now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.True(f())

	f = incrUserMessageLimit(pipe, testUserID, now.Add(time.Second))
	_, err = pipe.Exec()
	require.NoError(err)
	assert.False(f())
}

func TestKeyUserMessageRepeatLimit(t *testing.T) {
	assert := assert.New(t)

	var (
		testUserID = int64(100000)
	)

	key := KeyUserMessageRepeatLimit(testUserID, time.Date(2020, 1, 1, 0, 0, 10, 0, time.Local))
	assert.Equal("test_users/message/100000/02", key)

	key = KeyUserMessageRepeatLimit(testUserID, time.Date(2020, 1, 1, 0, 0, 01, 0, time.Local))
	assert.Equal("test_users/message/100000/00", key)

	key = KeyUserMessageRepeatLimit(testUserID, time.Date(2020, 1, 1, 0, 0, 51, 0, time.Local))
	assert.Equal("test_users/message/100000/10", key)
}

func TestAddUserMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(100000)
		now        = goutil.TimeNow()
	)
	require.NoError(service.Redis.Del(KeyUserMessageRepeatLimit(testUserID, now)).Err())

	pipe := service.Redis.Pipeline()
	f := addUserMessage(pipe, testUserID, "测试重复消息", now)
	_, err := pipe.Exec()
	require.NoError(err)
	assert.False(f())

	f = addUserMessage(pipe, testUserID, "测试重复消息", now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.True(f())

	f = addUserMessage(pipe, testUserID, "测试重复消息，不重复了", now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.False(f())
}

func TestKeyRoomMessageLimit(t *testing.T) {
	assert := assert.New(t)

	var (
		testRoomID = int64(100000)
	)

	key := KeyRoomMessageLimit(testRoomID, time.Date(2020, 1, 1, 0, 0, 22, 100, time.Local))
	assert.Equal("test_rooms/message/100000/counter/220", key)

	key = KeyRoomMessageLimit(testRoomID, time.Date(2020, 1, 1, 0, 0, 22, 999000000, time.Local))
	assert.Equal("test_rooms/message/100000/counter/221", key)
}

func TestIncrRoomMessageLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(100000)
		now        = goutil.TimeNow()
	)
	require.NoError(service.Redis.Del(KeyRoomMessageLimit(testRoomID, now)).Err())

	pipe := service.Redis.Pipeline()
	f := incrRoomMessageLimit(pipe, testRoomID, now)
	_, err := pipe.Exec()
	require.NoError(err)
	assert.False(f())

	f = incrRoomMessageLimit(pipe, testRoomID, now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.False(f())

	err = service.Redis.IncrBy(KeyRoomMessageLimit(testRoomID, now), 2).Err() // 当前时间房间消息数已达 4 条
	require.NoError(err)
	f = incrRoomMessageLimit(pipe, testRoomID, now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.True(f())

	now = now.Add(time.Second)
	require.NoError(service.Redis.Del(KeyRoomMessageLimit(testRoomID, now)).Err())
	f = incrRoomMessageLimit(pipe, testRoomID, now)
	_, err = pipe.Exec()
	require.NoError(err)
	assert.False(f())
}

func TestKeyMultiConnectGroupMessageLimit(t *testing.T) {
	assert := assert.New(t)

	testGroupID := int64(123)
	key := KeyMultiConnectGroupMessageLimit(testGroupID, time.Date(2020, 1, 1, 0, 0, 22, 100, time.Local))
	assert.Equal("test_groups/message/123/counter/220", key)

	key = KeyMultiConnectGroupMessageLimit(testGroupID, time.Date(2020, 1, 1, 0, 0, 22, 999000000, time.Local))
	assert.Equal("test_groups/message/123/counter/221", key)
}

func TestIncrGroupMessageLimit(t *testing.T) {
	require := require.New(t)

	testGroupID := int64(123)
	when := time.Date(2025, 6, 6, 0, 0, 22, 100, time.Local)
	require.NoError(service.Redis.Del(KeyMultiConnectGroupMessageLimit(testGroupID, when)).Err())

	limited, err := IncrGroupMessageLimit(testGroupID, when, 1)
	require.NoError(err)
	require.False(limited)

	limited, err = IncrGroupMessageLimit(testGroupID, when, 1)
	require.NoError(err)
	require.True(limited)
}

func TestGetSecondAndMillisecond(t *testing.T) {
	assert := assert.New(t)

	when := time.Date(2020, 1, 1, 0, 0, 22, 100, time.Local)
	assert.Equal("220", getSecondAndMillisecond(when))

	when = time.Date(2020, 1, 1, 0, 0, 22, 999000000, time.Local)
	assert.Equal("221", getSecondAndMillisecond(when))
}
