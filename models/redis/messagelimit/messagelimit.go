package messagelimit

import (
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Status 消息限制状态
func Status(room *room.Room, user *liveuser.Simple, msg string) int {
	isRoomAdmin, err := livemembers.IsRoomAdmin(room.OID, user.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if room.IsOwner(user) || isRoomAdmin || user.IsRole(liveuser.RoleStaff) {
		// 主播或者管理员不受限制
		return models.MessageStatusNormal
	}
	if room.MessageLimit().IsIgnore() {
		return models.MessageStatusIgnore
	}

	now := goutil.TimeNow()
	pipe := service.Redis.TxPipeline()
	isUserMsgLimit := incrUserMessageLimit(pipe, user.UserID(), now)
	isUserMsgRepeat := addUserMessage(pipe, user.UserID(), msg, now)
	isRoomMsgLimit := incrRoomMessageLimit(pipe, room.RoomID, now)
	_, err = pipe.Exec()
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id": room.RoomID,
			"user_id": user.UserID(),
			"msg":     msg,
		}).Error(err)
		// PASS
		return models.MessageStatusNormal
	}
	if isUserMsgLimit() || isUserMsgRepeat() || isRoomMsgLimit() {
		return models.MessageStatusLimited
	}
	return models.MessageStatusNormal
}

// KeyUserMessageLimit 用户消息限制
func KeyUserMessageLimit(userID int64, when time.Time) string {
	return keys.KeyMessageUserLimit2.Format(userID, when.Format(util.TimeFormatSS))
}

func incrUserMessageLimit(pipeline redis.Pipeliner, userID int64, when time.Time) func() bool {
	key := KeyUserMessageLimit(userID, when)
	cmd := pipeline.Incr(key)
	pipeline.Expire(key, 5*time.Second)
	return func() bool {
		// 同一用户的发言限制：每秒 2 条
		return cmd.Val() > 2
	}
}

// KeyUserMessageRepeatLimit 用户消息重复限制
func KeyUserMessageRepeatLimit(userID int64, when time.Time) string {
	second := fmt.Sprintf("%02d", when.Second()/5)
	return keys.KeyMessageUserRepeatLimit2.Format(userID, second)
}

func addUserMessage(pipeline redis.Pipeliner, userID int64, msg string, when time.Time) func() bool {
	key := KeyUserMessageRepeatLimit(userID, when)
	cmd := pipeline.SAdd(key, goutil.MD5(msg))
	pipeline.Expire(key, 10*time.Second)
	return func() bool {
		// 同一用户的消息重复限制：5 秒内不能发送相同内容
		return cmd.Val() == 0
	}
}

// KeyRoomMessageLimit 房间消息限制
func KeyRoomMessageLimit(roomID int64, when time.Time) string {
	secondAndMillisecond := getSecondAndMillisecond(when)
	return keys.KeyMessageRoomLimit2.Format(roomID, secondAndMillisecond)
}

func incrRoomMessageLimit(pipeline redis.Pipeliner, roomID int64, when time.Time) func() bool {
	key := KeyRoomMessageLimit(roomID, when)
	cmd := pipeline.Incr(key)
	pipeline.Expire(key, 5*time.Second)
	return func() bool {
		// 单房间内的全局限制：每 0.5 秒 4 条
		return cmd.Val() > 4
	}
}

// KeyMultiConnectGroupMessageLimit 主播连线组消息限制
func KeyMultiConnectGroupMessageLimit(groupID int64, when time.Time) string {
	secondAndMillisecond := getSecondAndMillisecond(when)
	return keys.KeyMessageMultiConnectGroupLimit2.Format(groupID, secondAndMillisecond)
}

// IncrGroupMessageLimit 增加互通消息计数，返回是否达到限制值
func IncrGroupMessageLimit(groupID int64, when time.Time, limit int) (bool, error) {
	pipe := service.Redis.TxPipeline()
	key := KeyRoomMessageLimit(groupID, when)
	cmd := pipe.Incr(key)
	pipe.Expire(key, 5*time.Second)
	_, err := pipe.Exec()
	if err != nil {
		logger.WithFields(logger.Fields{"group_id": groupID}).Error(err)
		// PASS
	}
	return cmd.Val() > int64(limit), nil
}

// getSecondAndMillisecond 获取秒并根据毫秒数取 0 或 1，用于填充消息限制的 key
func getSecondAndMillisecond(when time.Time) string {
	second := when.Format(util.TimeFormatSS)
	millisecond := int64(when.Nanosecond()) / time.Millisecond.Nanoseconds() // 取当前毫秒数
	// 当前毫秒数小于 500 补 0，否则补 1
	if millisecond < 500 {
		return second + "0"
	}
	return second + "1"
}
