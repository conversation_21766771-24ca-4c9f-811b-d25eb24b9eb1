package liverevenues

import (
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const rankCacheSize = 20

// CollectionName collection name
const CollectionName = "live_revenues"

// LiveRevenue 直播间用户贡献的收益
type LiveRevenue struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`

	RoomOID         primitive.ObjectID `bson:"_room_id" json:"-"`
	RoomID          int64              `bson:"room_id" json:"room_id"`
	UserID          int64              `bson:"user_id" json:"user_id"`
	GiftRevenue     int64              `bson:"gift_revenue" json:"gift_revenue"`
	QuestionRevenue int64              `bson:"question_revenue" json:"question_revenue"`
	NobleRevenue    int64              `bson:"noble_revenue" json:"noble_revenue"`
	SuperFanRevenue int64              `bson:"super_fan_revenue" json:"super_fan_revenue"`
	DanmakuRevenue  int64              `bson:"danmaku_revenue" json:"danmaku_revenue"`
	LuckyBoxRevenue int64              `bson:"lucky_box_revenue" json:"lucky_box_revenue"`
	Revenue         int64              `bson:"revenue" json:"revenue"`
	Point           int64              `bson:"point" json:"point"`
	CreatedTime     time.Time          `bson:"created_time" json:"-"`
	UpdatedTime     time.Time          `bson:"updated_time" json:"-"`
}

// RankRevenue 用户榜单收益
type RankRevenue struct {
	Rank    int64 `bson:"-" json:"rank"`
	Revenue int64 `bson:"revenue" json:"revenue"`
	ID      int64 `bson:"user_id" json:"-"` // 防止和 Simple.UserID() 冲突

	*liveuser.Simple `bson:"-"`
	RankInvisible    bool `bson:"-" json:"rank_invisible"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// Count 返回 count
func Count(roomID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	count, err := collection.CountDocuments(ctx, bson.M{"room_id": roomID})
	if err != nil {
		return 0, err
	}
	return count, nil
}

// FindRankByPage 查询榜单数据
func FindRankByPage(roomID int64, pa goutil.Pagination) ([]*RankRevenue, error) {
	res := findRankWithCache(roomID, pa)
	if res != nil {
		return res, nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	opt := pa.SetFindOptions(nil).SetSort(bson.M{"revenue": -1}).
		SetProjection(bson.M{"revenue": 1, "user_id": 1})
	cur, err := collection.Find(ctx, bson.M{"room_id": roomID}, opt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return make([]*RankRevenue, 0), nil
	}
	err = assignRankRevenue(roomID, res, pa.Offset()+1)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindRankByUser 查询某用户贡献的榜单收益
func FindRankByUser(roomID, userID int64) (*RankRevenue, error) {
	// 查询用户信息
	proj := liveuser.ProjectionSimple
	proj.Group = 0
	u, err := liveuser.FindOneSimple(bson.M{"user_id": userID}, &liveuser.FindOptions{FindTitles: true, RoomID: roomID},
		options.FindOne().SetProjection(proj))
	if err != nil {
		return nil, err
	}
	if u == nil {
		u = &liveuser.Simple{UID: userID}
	}

	res := new(RankRevenue)
	defer func() { res.Simple = u }()
	res.RankInvisible = userstatus.IsRankInvisible(userID, roomID, true)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	filter := bson.M{"room_id": roomID, "user_id": userID}
	err = collection.FindOne(ctx, filter,
		options.FindOne().SetProjection(bson.M{"revenue": 1})).Decode(res)
	if err != nil {
		if err != mongo.ErrNoDocuments {
			return nil, err
		}
		return res, nil
	}
	res.ID = userID
	// TODO: 在贡献相同时，需要按照 mongodb 默认排序增加名次
	count, err := collection.CountDocuments(ctx, bson.M{"room_id": roomID, "revenue": bson.M{"$gt": res.Revenue}})
	if err != nil {
		return nil, err
	}
	res.Rank = count + 1

	return res, nil
}

func findRankWithCache(roomID int64, pa goutil.Pagination) []*RankRevenue {
	// trimOthers 从缓存的数组中根据 pa 取出需要的数据
	trimOthers := func(res []*RankRevenue, pa goutil.Pagination) []*RankRevenue {
		if pa.Offset() > int64(len(res)) {
			return []*RankRevenue{}
		}
		end := len(res)
		if pa.Offset()+pa.PageSize < int64(end) {
			end = int(pa.Offset() + pa.PageSize)
		}
		return res[pa.Offset():end]
	}
	if pa.Offset()+pa.PageSize > rankCacheSize {
		// 此时查询的数据已超出缓存的数据
		return nil
	}
	key := keys.KeyChatroomRankRevenue1.Format(roomID)
	val, err := service.LRURedis.Get(key).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		// PASS
	}
	if val != "" {
		var res []*RankRevenue
		err = json.Unmarshal([]byte(val), &res)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return trimOthers(res, pa)
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	opt := options.Find().SetLimit(rankCacheSize).SetSort(bson.M{"revenue": -1}).
		SetProjection(bson.M{"revenue": 1, "user_id": 1})
	cur, err := collection.Find(ctx, bson.M{"room_id": roomID}, opt)
	if err != nil {
		logger.Error(err)
		return nil
	}
	defer cur.Close(ctx)
	res := []*RankRevenue{}
	err = cur.All(ctx, &res)
	if err != nil {
		logger.Error(err)
		return nil
	}
	err = assignRankRevenue(roomID, res, 1)
	if err != nil {
		logger.Error(err)
		return nil
	}
	b, err := json.Marshal(res)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, string(b), time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return trimOthers(res, pa)
}

func assignRankRevenue(roomID int64, res []*RankRevenue, rankStart int64) error {
	userIDs := make([]int64, len(res))
	for i := 0; i < len(res); i++ {
		res[i].Rank = rankStart + int64(i)
		userIDs[i] = res[i].ID
	}

	// 查询用户信息
	proj := liveuser.ProjectionSimple
	proj.Group = 0
	users, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(
		bson.M{"user_id": bson.M{"$in": userIDs}},
		&liveuser.FindOptions{
			FindTitles: true,
			RoomID:     roomID,
		}, options.Find().SetProjection(proj)))
	if err != nil {
		return err
	}
	for i := 0; i < len(res); i++ {
		if u := users[res[i].ID]; u != nil {
			res[i].Simple = u
		} else {
			res[i].Simple = &liveuser.Simple{UID: res[i].ID}
		}
		res[i].RankInvisible = userstatus.IsRankInvisible(res[i].ID, roomID, false)
	}
	return nil
}

// AddGiftRevenue 添加礼物收益
func AddGiftRevenue(userID int64, roomOID primitive.ObjectID, roomID int64,
	revenue int64, point ...int64) error {
	var addPoint int64
	if len(point) != 0 {
		addPoint = point[0]
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	col := Collection()
	inc := bson.M{"gift_revenue": revenue, "revenue": revenue}
	if addPoint != 0 {
		inc["point"] = addPoint
	}
	res, err := col.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": userID},
		bson.M{
			"$inc": inc,
			"$set": bson.M{"updated_time": now},
		})
	if err != nil {
		return err
	}
	if res.ModifiedCount != 0 {
		return nil
	}
	// 插入新的
	lr := LiveRevenue{
		RoomOID:     roomOID,
		RoomID:      roomID,
		UserID:      userID,
		GiftRevenue: revenue,
		Revenue:     revenue,
		Point:       addPoint,
		CreatedTime: now,
		UpdatedTime: now,
	}
	_, err = col.InsertOne(ctx, lr)
	return err
}

// AddPoint 添加免费榜单积分
func AddPoint(userID int64, roomOID primitive.ObjectID, roomID, point int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	col := Collection()
	res, err := col.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": userID},
		bson.M{
			"$inc": bson.M{"point": point},
			"$set": bson.M{"updated_time": now},
		})
	if err != nil {
		return err
	}
	if res.ModifiedCount != 0 {
		return nil
	}
	// 插入新的
	lr := LiveRevenue{
		RoomOID:     roomOID,
		RoomID:      roomID,
		UserID:      userID,
		Point:       point,
		CreatedTime: now,
		UpdatedTime: now,
	}
	_, err = col.InsertOne(ctx, lr)
	return err
}

// AddSuperRevenue 添加超粉收益
func AddSuperRevenue(userID int64, roomOID primitive.ObjectID, roomID int64,
	revenue int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	col := Collection()
	res, err := col.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": userID},
		bson.M{
			"$inc": bson.M{"super_fan_revenue": revenue, "revenue": revenue},
			"$set": bson.M{"updated_time": now},
		})
	if err != nil {
		return err
	}
	if res.ModifiedCount != 0 {
		return nil
	}
	// 插入新的
	lr := LiveRevenue{
		RoomOID:         roomOID,
		RoomID:          roomID,
		UserID:          userID,
		SuperFanRevenue: revenue,
		Revenue:         revenue,
		CreatedTime:     now,
		UpdatedTime:     now,
	}
	_, err = col.InsertOne(ctx, lr)
	return err
}

// AddQuestionRevenue 添加提问收益
func AddQuestionRevenue(userID int64, roomOID primitive.ObjectID, roomID int64,
	revenue int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	col := Collection()
	res, err := col.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": userID},
		bson.M{
			"$inc": bson.M{"question_revenue": revenue, "revenue": revenue},
			"$set": bson.M{"updated_time": now},
		})
	if err != nil {
		return err
	}
	if res.ModifiedCount != 0 {
		return nil
	}
	// 插入新的
	lr := LiveRevenue{
		RoomOID:         roomOID,
		RoomID:          roomID,
		UserID:          userID,
		QuestionRevenue: revenue,
		Revenue:         revenue,
		CreatedTime:     now,
		UpdatedTime:     now,
	}
	_, err = col.InsertOne(ctx, lr)
	return err
}

// AddDanmakuRevenue 添加付费弹幕收益
func AddDanmakuRevenue(userID int64, roomOID primitive.ObjectID, roomID int64, revenue int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	res, err := Collection().UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": userID},
		bson.M{
			"$inc": bson.M{"danmaku_revenue": revenue, "revenue": revenue},
			"$set": bson.M{"updated_time": now},
		})
	if err != nil {
		return err
	}
	if res.ModifiedCount != 0 {
		return nil
	}
	// 插入新的
	lr := LiveRevenue{
		RoomOID:        roomOID,
		RoomID:         roomID,
		UserID:         userID,
		DanmakuRevenue: revenue,
		Revenue:        revenue,
		CreatedTime:    now,
		UpdatedTime:    now,
	}
	_, err = Collection().InsertOne(ctx, lr)
	return err
}

// AddLuckyBoxRevenue 添加宝盒收益
func AddLuckyBoxRevenue(userID int64, roomOID primitive.ObjectID, roomID int64, revenue int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	res, err := Collection().UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": userID},
		bson.M{
			"$inc": bson.M{"lucky_box_revenue": revenue, "revenue": revenue},
			"$set": bson.M{"updated_time": now},
		})
	if err != nil {
		return err
	}
	if res.ModifiedCount != 0 {
		return nil
	}
	// 插入新的
	lr := LiveRevenue{
		RoomOID:         roomOID,
		RoomID:          roomID,
		UserID:          userID,
		LuckyBoxRevenue: revenue,
		Revenue:         revenue,
		CreatedTime:     now,
		UpdatedTime:     now,
	}
	_, err = Collection().InsertOne(ctx, lr)
	return err
}
