package liverevenues

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	testRoomID  = int64(180683)
	testRoomHex = "5d39a98b2555501487516424"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	run(m)
}

func run(m *testing.M) int {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	opt := options.Update().SetUpsert(true)
	roomOID, _ := primitive.ObjectIDFromHex(testRoomHex)
	rev := map[string]interface{}{
		"room_id":  testRoomID,
		"_room_id": roomOID,
		"user_id":  10,
		"revenue":  10,
	}
	_, err := collection.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": 10},
		bson.M{"$set": rev}, opt)
	if err != nil {
		panic(err)
	}

	rev["user_id"] = 11
	rev["revenue"] = 11
	_, err = collection.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": 11},
		bson.M{"$set": rev}, opt)
	if err != nil {
		panic(err)
	}

	rev["user_id"] = 12
	rev["revenue"] = 12
	_, err = collection.UpdateOne(ctx, bson.M{"_room_id": roomOID, "user_id": 12},
		bson.M{"$set": rev}, opt)
	if err != nil {
		panic(err)
	}
	return m.Run()
}

func TestCount(t *testing.T) {
	assert := assert.New(t)
	count, err := Count(testRoomID)
	assert.NoError(err)
	assert.Equal(int64(3), count)
}

func TestRankByPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	key := keys.KeyChatroomRankRevenue1.Format(testRoomID)
	err := service.LRURedis.Del(key).Err()
	require.True(err == nil || err == redis.Nil)
	pa := goutil.MakePagination(3, 1, 2)
	rrs, err := FindRankByPage(testRoomID, pa)
	require.NoError(err)
	require.Len(rrs, 2)
	assert.Equal(int64(12), rrs[0].ID)
	assert.Equal(int64(1), rrs[0].Rank)
	assert.Equal(int64(11), rrs[1].ID)
	assert.Equal(int64(2), rrs[1].Rank)
	rrs, err = FindRankByPage(testRoomID, pa)
	require.NoError(err)
	require.Len(rrs, 2)
	assert.Zero(rrs[0].ID)
	assert.Equal(int64(1), rrs[0].Rank)
	assert.Zero(rrs[1].ID)
	assert.Equal(int64(2), rrs[1].Rank)
	// 测试空文档
	rrs, err = FindRankByPage(-1, pa)
	assert.NoError(err)
	assert.NotNil(rrs)
	assert.Empty(rrs)
}

func TestFindRankByUser(t *testing.T) {
	assert := assert.New(t)
	rr, err := FindRankByUser(testRoomID, -1)
	assert.NoError(err)
	if assert.NotNil(rr) {
		assert.Zero(rr.Revenue)
	}
	rr, err = FindRankByUser(testRoomID, 10)
	assert.NoError(err)
	require.NotNil(t, rr)
	assert.Equal(int64(3), rr.Rank)
	assert.Equal(int64(10), rr.Revenue)
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)

	lr := LiveRevenue{}
	rr := &RankRevenue{}
	kc.Check(lr, "_id", "_room_id", "room_id",
		"user_id", "gift_revenue", "question_revenue", "noble_revenue", "super_fan_revenue", "danmaku_revenue", "lucky_box_revenue", "revenue", "point",
		"created_time", "updated_time")
	kc.Check(rr, "revenue", "user_id")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(rr, "rank", "revenue", "rank_invisible")
	kc.Check(lr, "room_id", "user_id",
		"gift_revenue", "question_revenue", "noble_revenue", "super_fan_revenue", "danmaku_revenue", "lucky_box_revenue", "revenue", "point")
}

func TestAddGiftRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2020, 07, 02, 0, 0, 0, 0, time.Local)
	roomOID := primitive.NewObjectIDFromTimestamp(when)
	roomID := int64(20200702)
	userID := int64(1234)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	filter := bson.M{"_room_id": roomOID, "room_id": roomID, "user_id": userID}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	require.NoError(AddGiftRevenue(userID, roomOID, roomID, 10))
	var lr LiveRevenue
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.GiftRevenue, lr.Revenue)
	assert.Equal(int64(10), lr.Revenue)
	require.NoError(AddGiftRevenue(userID, roomOID, roomID, 5))

	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.GiftRevenue, lr.Revenue)
	assert.Equal(int64(15), lr.Revenue)

	require.NoError(AddGiftRevenue(userID, roomOID, roomID, 5, 1))
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.GiftRevenue, lr.Revenue)
	assert.EqualValues(20, lr.Revenue)
	assert.EqualValues(1, lr.Point)
}

func TestAddPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2021, 07, 01, 0, 0, 0, 0, time.Local)
	roomOID := primitive.NewObjectIDFromTimestamp(when)
	roomID := int64(20200702)
	userID := int64(1234)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	filter := bson.M{"_room_id": roomOID, "room_id": roomID, "user_id": userID}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	require.NoError(AddPoint(userID, roomOID, roomID, 10))
	var lr LiveRevenue
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.EqualValues(10, lr.Point)
	require.NoError(AddPoint(userID, roomOID, roomID, 5))
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.EqualValues(15, lr.Point)
}

func TestAddQuestionRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2022, 01, 01, 0, 0, 0, 0, time.Local)
	roomOID := primitive.NewObjectIDFromTimestamp(when)
	roomID := int64(20220101)
	userID := int64(12345)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	filter := bson.M{"_room_id": roomOID, "room_id": roomID, "user_id": userID}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	err = AddQuestionRevenue(userID, roomOID, roomID, 10)
	require.NoError(err)
	var lr LiveRevenue
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.QuestionRevenue, lr.Revenue)
	assert.Equal(int64(10), lr.Revenue)
	err = AddQuestionRevenue(userID, roomOID, roomID, 5)
	require.NoError(err)

	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.QuestionRevenue, lr.Revenue)
	assert.Equal(int64(15), lr.Revenue)
}

func TestAddDanmakuRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2022, 01, 01, 0, 0, 0, 0, time.Local)
	roomOID := primitive.NewObjectIDFromTimestamp(when)
	roomID := int64(20220101)
	userID := int64(12345)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	filter := bson.M{"_room_id": roomOID, "room_id": roomID, "user_id": userID}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	err = AddDanmakuRevenue(userID, roomOID, roomID, 10)
	require.NoError(err)
	var lr LiveRevenue
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.DanmakuRevenue, lr.Revenue)
	assert.Equal(int64(10), lr.Revenue)
	err = AddDanmakuRevenue(userID, roomOID, roomID, 5)
	require.NoError(err)

	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.DanmakuRevenue, lr.Revenue)
	assert.Equal(int64(15), lr.Revenue)
}

func TestAddLuckyBoxRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2022, 01, 01, 0, 0, 0, 0, time.Local)
	roomOID := primitive.NewObjectIDFromTimestamp(when)
	roomID := int64(20220101)
	userID := int64(12345)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	filter := bson.M{"_room_id": roomOID, "room_id": roomID, "user_id": userID}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	err = AddLuckyBoxRevenue(userID, roomOID, roomID, 10)
	require.NoError(err)
	var lr LiveRevenue
	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.LuckyBoxRevenue, lr.Revenue)
	assert.Equal(int64(10), lr.Revenue)
	err = AddLuckyBoxRevenue(userID, roomOID, roomID, 5)
	require.NoError(err)

	require.NoError(col.FindOne(ctx, filter).Decode(&lr))
	assert.Equal(lr.LuckyBoxRevenue, lr.Revenue)
	assert.Equal(int64(15), lr.Revenue)
}
