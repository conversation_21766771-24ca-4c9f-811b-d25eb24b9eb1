package gift

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestCSVGiftTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	tags := []string{
		"gift_id", "type", "exclusive", "attr", "name", "name_clean",
		"price", "point",
		"order", "added_time",
		"icon", "icon_active",
		"label_icon",
		"intro", "intro_icon", "intro_open_url",
		"effect", "web_effect", "notify_duration",
		"comboable", "combo_effect", "web_combo_effect",
		"user_id", "room_id",
		"noble_level", "medal_level", "user_level",
		"notify_message", "notify_bubble_id",
		"allowed_nums",
		"lucky_effect", "web_lucky_effect",
		"base_gift_id", "vip_type",
		"toggle_time",
	}
	kc.Check(CSVGift{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(CSVGift{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.TagParser{TagName: "csv"})
	kc.Check(CSVGift{}, "礼物 ID", "类型", "专属类型", "属性", "名称", "特殊名称",
		"价格（钻）", "亲密度",
		"排序", "上线时间",
		"图标",
		"角标",
		"简介", "简介图标", "简介跳转地址",
		"特效", "通知时长（毫秒）",
		"是否可连击", "连击特效",
		"用户 ID", "房间 ID",
		"贵族等级限制", "勋章等级限制", "用户等级限制",
		"飘屏消息内容", "飘屏幕气泡 ID",
		"赠送数量限制", "开箱动画",
		"模版礼物 ID",
		"贵族类别",
		"上下架时间",
	)
}

func TestBuildMongoGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := CSVGift{
		GiftID:           1,
		AddedTimeParam:   "2021-07-01 10:00:00",
		IconParam:        1,
		LabelIconParam:   "shen.webp",
		EffectParam:      3,
		ComboEffectParam: 4,
		AllowedNumsParam: "10/100/-1",
		LuckyEffectParam: 5,
		VipType:          vip.TypeLiveHighness,
	}
	require.NoError(p.BuildMongoGifts())
	assert.Equal("oss://live/gifts/icons/1.png", p.Icon)
	assert.Equal("oss://live/gifts/icons/active/1.webp", p.IconActive)
	assert.Equal("oss://live/gifts/labels/shen.webp", p.LabelIcon)
	assert.Equal("oss://live/gifts/effects/v2/3.mp4;oss://live/gifts/effects/v2/3.png", p.Effect)
	assert.Equal("oss://live/gifts/effects/v2/3-web.mp4;"+
		"oss://live/gifts/effects/v2/3-web.webm;oss://live/gifts/effects/v2/3-web.png", p.WebEffect)
	assert.Equal("oss://live/gifts/comboeffects/4.svga", p.ComboEffect)
	assert.Equal("oss://live/gifts/comboeffects/4-web.svga", p.WebComboEffect)
	assert.Equal([]int64{10, 100, -1}, p.AllowedNums)
	assert.Equal("oss://live/gifts/luckyeffects/v2/5.mp4;oss://live/gifts/luckyeffects/v2/5.png", p.LuckyEffect)
	assert.Equal("oss://live/gifts/luckyeffects/v2/5-web.mp4;"+
		"oss://live/gifts/luckyeffects/v2/5-web.webm;oss://live/gifts/luckyeffects/v2/5-web.png", p.WebLuckyEffect)
	assert.Equal(vip.TypeLiveHighness, p.VipType)

	p = CSVGift{
		GiftID:         1,
		AddedTimeParam: "2021-07-01 10:00:00",
		IconParam:      1,
		Type:           TypeDrawReceive,
	}
	require.NoError(p.BuildMongoGifts())
	assert.Equal("oss://live/gifts/icons/1.png", p.Icon)
	assert.Empty(p.IconActive)
}

func TestCSVGift_TrimSpaces(t *testing.T) {
	assert := assert.New(t)

	gift := CSVGift{
		Name:          "  hello",
		NameClean:     "world    ",
		Intro:         " test 1   ",
		NotifyMessage: "    test  2 ",
	}
	gift.TrimSpaces()
	assert.Equal("hello", gift.Name)
	assert.Equal("world", gift.NameClean)
	assert.Equal("test 1", gift.Intro)
	assert.Equal("test  2", gift.NotifyMessage)
}
