package gift

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/vip"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGiftOwnedByVIP(t *testing.T) {
	assert := assert.New(t)

	g := Gift{Type: TypeNormal}
	assert.True(g.OwnedByVIP(nil))

	g.Type = TypeNoble
	g.NobleLevel = 1
	assert.False(g.OwnedByVIP(nil))

	g.VipType = vip.TypeLiveHighness
	g.NobleLevel = 1
	assert.False(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveNoble, Level: 1}))
	assert.False(g.OwnedByVIP(&vip.UserVip{Type: -1, Level: 1}), "未知贵族")
	futureUnix := goutil.TimeNow().Unix() + 10
	assert.True(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveHighness, Level: 1, ExpireTime: futureUnix}))

	g.VipType = vip.TypeLiveNoble
	g.NobleLevel = 5
	assert.False(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveNoble, Level: 1}))
	assert.True(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveNoble, Level: 5, ExpireTime: futureUnix}))
	assert.True(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveHighness, Level: 1, ExpireTime: futureUnix}))

	g.VipType = vip.TypeLiveTrialNoble
	g.NobleLevel = 5
	assert.False(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveNoble, Level: 1}))
	assert.True(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveNoble, Level: 5, ExpireTime: futureUnix}))
	assert.True(g.OwnedByVIP(&vip.UserVip{Type: vip.TypeLiveHighness, Level: 1, ExpireTime: futureUnix}))
}

func TestGiftOwnedByFans(t *testing.T) {
	assert := assert.New(t)

	g := Gift{Type: TypeNormal}
	assert.True(g.OwnedByFans(nil))

	g.Type = TypeMedal
	assert.False(g.OwnedByFans(nil))

	medal := new(livemedal.LiveMedal)
	medal.Level = 9
	g.Type = TypeMedal
	g.MedalLevel = medal.Level + 1
	assert.False(g.OwnedByFans(medal))

	g.MedalLevel = medal.Level
	assert.True(g.OwnedByFans(medal))

	g.Type = TypeSuperFan
	g.MedalLevel = 0
	assert.False(g.OwnedByFans(medal))

	medal.SuperFan = &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Unix() + 100}
	assert.True(g.OwnedByFans(medal))
}

func TestGift_OwnedByUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(123)
	g := Gift{Type: TypeNormal}
	assert.True(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeNormal, UserID: 1}
	assert.False(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeNormal, UserID: userID}
	assert.True(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeCustom, UserID: 1}
	assert.False(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeCustom, UserID: userID}
	assert.True(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeCustom, UserLevel: 10}
	assert.False(g.OwnedByUser(userID, 9))
	assert.True(g.OwnedByUser(userID, 10))

	g = Gift{Type: TypeCustom, UserID: 0, GiftID: 2022}
	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND custom_id = ?",
		livecustom.TypeUserCustomGift, g.GiftID).Error)
	assert.False(g.OwnedByUser(userID, 0))
	require.NoError(livecustom.AddUserCustomGift(userID, g.GiftID, time.Minute, false))
	assert.True(g.OwnedByUser(userID, 0))

	// Tests for gift with GiftExclusiveUser
	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveUser, UserID: userID}
	assert.True(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveUser, UserID: 1}
	assert.False(g.OwnedByUser(userID, 0))

	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveUser, UserLevel: 10}
	assert.False(g.OwnedByUser(userID, 9))
	assert.True(g.OwnedByUser(userID, 10))

	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveUser, UserID: 0, GiftID: 2022}
	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND custom_id = ?",
		livecustom.TypeUserCustomGift, g.GiftID).Error)
	assert.False(g.OwnedByUser(userID, 0))
	require.NoError(livecustom.AddUserCustomGift(userID, g.GiftID, time.Minute, false))
	assert.True(g.OwnedByUser(userID, 0))
}

func TestGift_OwnedByRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(123)
	g := Gift{Type: TypeNormal}
	assert.True(g.OwnedByRoom(roomID))

	g = Gift{Type: TypeDrawSend}
	assert.True(g.OwnedByRoom(roomID))
	g.RoomID = 321
	assert.False(g.OwnedByRoom(roomID))

	g.Type = TypeRoomCustom
	g.RoomID = 1
	assert.False(g.OwnedByRoom(roomID))
	g.RoomID = roomID
	assert.True(g.OwnedByRoom(roomID))

	g.RoomID = 0
	g.GiftID = 777
	err := livecustom.RemoveRoomCustomGift(roomID, g.GiftID)
	require.NoError(err)
	assert.False(g.OwnedByRoom(roomID))

	now := goutil.TimeNow()
	err = livecustom.AddRoomCustomGift(roomID, g.GiftID, now, now.Add(time.Minute), livecustom.SourceDefault)
	require.NoError(err)
	assert.True(g.OwnedByRoom(roomID))

	g.Type = TypeDrawSend
	g.Attr = 1 << (AttrDisableDrawRoomCustomAfterSSR - 1)
	assert.True(g.OwnedByRoom(roomID))

	g.RoomID = 1
	assert.False(g.OwnedByRoom(roomID))

	// Tests for gift with GiftExclusiveRoom
	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveRoom, RoomID: roomID}
	assert.True(g.OwnedByRoom(roomID))

	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveRoom, RoomID: 1}
	assert.False(g.OwnedByRoom(roomID))

	g = Gift{Type: TypeDrawSend, Exclusive: GiftExclusiveRoom, RoomID: 0, GiftID: 2022}
	err = livecustom.RemoveRoomCustomGift(roomID, g.GiftID)
	require.NoError(err)
	assert.False(g.OwnedByRoom(roomID))
	err = livecustom.AddRoomCustomGift(roomID, g.GiftID, now, now.Add(time.Minute), livecustom.SourceDefault)
	require.NoError(err)
	assert.True(g.OwnedByRoom(roomID))
}

func TestGift_IsExclusiveTabGift(t *testing.T) {
	assert := assert.New(t)

	testData := []struct {
		gift    Gift
		wantRes bool
	}{
		{Gift{Type: TypeNormal, Exclusive: GiftExclusiveUser}, true},
		{Gift{Type: TypeNormal, Exclusive: GiftExclusiveRoom}, true},
		{Gift{Type: TypeNormal}, false},
		{Gift{Type: TypeCustom}, true},
		{Gift{Type: TypeCustom, NobleLevel: 1}, false},
		{Gift{Type: TypeRebate}, false},
		{Gift{Type: TypeRoomCustom}, true},
		{Gift{Type: TypeDrawSend}, false},
		{Gift{Type: TypeDrawSend, Attr: 1 << (AttrDisableDrawRoomCustomAfterSSR - 1)}, true},
		{Gift{Type: TypeDrawSend, UserLevel: 1}, true},
		{Gift{Type: TypeDrawSend, UserID: 1}, true},
		{Gift{Type: TypeDrawReceive}, false},
	}

	for i := range testData {
		assert.Equal(testData[i].wantRes, testData[i].gift.IsExclusiveTabGift(), "testData %d", i)
	}
}

func TestGift_OwnedByMedal(t *testing.T) {
	assert := assert.New(t)

	g := Gift{
		Type: TypeDrawSend,
		Attr: 1 << (AttrDrawSendSuperFan - 1),
	}
	assert.False(g.OwnedByMedal(nil))

	medal := new(livemedal.LiveMedal)
	assert.False(g.OwnedByMedal(medal))

	medal.SuperFan = &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Unix() + 100}
	assert.True(g.OwnedByMedal(medal))
}
