package gift

import (
	"math/rand"
	"slices"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestPoolGift_PoolGiftType(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(TypeDrawReceive, PoolGift{Type: PoolTypeGift}.PoolGiftType())
	assert.Equal(TypeRebate, PoolGift{Type: PoolTypeRebateGift}.PoolGiftType())
	assert.Panics(func() { PoolGift{Type: 100}.PoolGiftType() })
}

func TestPoolGiftValid(t *testing.T) {
	assert := assert.New(t)

	drID := int64(90001)
	pool := PoolGift{Rates: make(map[int64]int), SSRID: 0}
	valid := func() bool {
		ok, _ := pool.Valid()
		return ok
	}
	assert.False(valid(), "未配置掉落")
	pool.Rates[drID] = 0
	assert.False(valid(), "礼物配置错误")
	pool.SSRID = 1
	assert.False(valid(), "保底次数错误")
	pool.MissLimit = 2
	assert.False(valid(), "保底礼物配置错误")
	pool.SSRID = drID
	pool.Rates[1] = 1
	assert.False(valid(), "礼物配置错误")
	pool.Rates[1] = 0
	pool.Rates[drID] = -1
	assert.False(valid(), "掉率配置错误")
	pool.Rates[drID] = 1
	assert.True(valid())

	pool.SSRID = 0
	pool.MissLimit = 0
	pool.Rates = map[int64]int{30006: 100}
	assert.False(valid(), "礼物配置错误")
	pool.Type = PoolTypeRebateGift
	assert.True(valid())

	pool.Type = PoolTypeGift
	pool.PRDConfig = &PRDConfig{
		InitialC:   0,
		IncrementC: 0,
	}
	assert.False(valid(), "未配置保底礼物")
	pool.SSRID = drID
	pool.Rates = map[int64]int{drID: 100}
	assert.False(valid(), "初始概率配置错误")
	pool.PRDConfig.InitialC = 0.1
	assert.False(valid(), "初始概率配置错误")
	pool.PRDConfig.IncrementC = 0.1
	assert.True(valid())
}

func TestPoolGift_Draw(t *testing.T) {
	seed := int64(1)

	t.Run("TestRandomValueFromSeed", func(t *testing.T) {
		testSource := rand.NewSource(seed)
		testRand := rand.New(testSource)
		assert.Equal(t, 81, testRand.Intn(100))
		assert.Equal(t, 87, testRand.Intn(100))
		assert.Equal(t, 47, testRand.Intn(100))
	})

	t.Run("TestTrueRandomnessDraw", func(t *testing.T) {
		oldSource := source
		defer func() {
			source = oldSource
		}()
		source = goutil.NewLockedSource(seed)

		drawUserID := int64(123)
		pool := PoolGift{
			Rates: map[int64]int{90001: 85, 90002: 15},
		}
		require.NoError(t, service.Redis.Del(pool.KeyDrawStatus(drawUserID)).Err())
		require.NoError(t, service.Redis.Del(keys.KeyGiftDrawMissCount3.Format(drawUserID, pool.GiftID, pool.GiftNum)).Err())

		// Got random value from seed: 81
		result, err := pool.Draw(drawUserID)
		require.NoError(t, err)
		assert.Equal(t, int64(90001), result.GiftID)
		assert.False(t, result.Guaranteed)

		// Got random value from seed: 87
		result, err = pool.Draw(drawUserID)
		require.NoError(t, err)
		assert.Equal(t, int64(90002), result.GiftID)
		assert.False(t, result.Guaranteed)

		// Got random value from seed: 47
		result, err = pool.Draw(drawUserID)
		require.NoError(t, err)
		assert.Equal(t, int64(90001), result.GiftID)
		assert.False(t, result.Guaranteed)
	})

	t.Run("TestPseudoRandomnessDraw", func(t *testing.T) {
		oldSource := source
		defer func() {
			source = oldSource
		}()
		source = goutil.NewLockedSource(seed)

		drawUserID := int64(123)
		pool := PoolGift{
			Rates: map[int64]int{90001: 50, 90002: 40, 90003: 10},
			SSRID: 90003,
			PRDConfig: &PRDConfig{
				InitialC:   0.01474584478107488,
				IncrementC: 0.01474584478107488,
			},
		}
		require.NoError(t, service.Redis.Del(pool.KeyDrawStatus(drawUserID)).Err())
		require.NoError(t, service.Redis.Del(keys.KeyGiftDrawMissCount3.Format(drawUserID, pool.GiftID, pool.GiftNum)).Err())

		drawResults := make([]int64, 0, 20)
		for i := 0; i < 20; i++ {
			result, err := pool.Draw(drawUserID)
			require.NoError(t, err)
			assert.False(t, result.Guaranteed)
			drawResults = append(drawResults, result.GiftID)
		}
		assert.Equal(
			t,
			[]int64{
				90002, 90001, 90002, 90002, 90002, 90002, 90001, 90001, 90002, 90002,
				90001, 90001, 90001, 90001, 90002, 90002, 90003, 90001, 90002, 90002,
			},
			drawResults,
		)
		drawStatus, err := pool.getDrawStatus(drawUserID)
		require.NoError(t, err)
		assert.Equal(t, int64(3), drawStatus.missCount)
		assert.Equal(t, int64(1), drawStatus.ssrCount)
	})

	t.Run("TestWarmupSSR", func(t *testing.T) {
		oldSource := source
		defer func() {
			source = oldSource
		}()
		source = goutil.NewLockedSource(seed)

		drawUserID := int64(123)
		pool := PoolGift{
			Rates: map[int64]int{90001: 50, 90002: 40, 90003: 10},
			SSRID: 90003,
			PRDConfig: &PRDConfig{
				InitialC:       0.01474584478107488,
				IncrementC:     0.01474584478107488,
				WarmupSSRCount: 1,
			},
		}
		require.NoError(t, service.Redis.Del(pool.KeyDrawStatus(drawUserID)).Err())
		require.NoError(t, service.Redis.Del(keys.KeyGiftDrawMissCount3.Format(drawUserID, pool.GiftID, pool.GiftNum)).Err())

		drawResults := make([]int64, 0, 40)
		for i := 0; i < 40; i++ {
			result, err := pool.Draw(drawUserID)
			require.NoError(t, err)
			assert.False(t, result.Guaranteed)
			drawResults = append(drawResults, result.GiftID)
		}
		assert.Equal(
			t,
			[]int64{
				90002, 90002, 90001, 90002, 90002, 90001, 90001, 90001, 90002, 90001,
				90003, 90001, 90002, 90002, 90001, 90002, 90002, 90001, 90001, 90002,
				90001, 90003, 90001, 90001, 90002, 90001, 90001, 90002, 90001, 90002,
				90001, 90002, 90002, 90001, 90001, 90001, 90002, 90002, 90003, 90002,
			},
			drawResults,
		)
		drawStatus, err := pool.getDrawStatus(drawUserID)
		require.NoError(t, err)
		assert.Equal(t, int64(1), drawStatus.missCount)
		assert.Equal(t, int64(3), drawStatus.ssrCount)
	})

	t.Run("TestNoConsecutiveGuaranteed", func(t *testing.T) {
		drawUserID := int64(123)
		pool := PoolGift{
			Rates:     map[int64]int{90001: 100, 90002: 0},
			SSRID:     90002,
			MissLimit: 10,
		}
		require.NoError(t, service.Redis.Del(pool.KeyDrawStatus(drawUserID)).Err())
		require.NoError(t, service.Redis.Del(keys.KeyGiftDrawMissCount3.Format(drawUserID, pool.GiftID, pool.GiftNum)).Err())

		giftIDResults := make([]int64, 0, 20)
		isGuaranteedResults := make([]bool, 0, 20)
		for i := 0; i < 20; i++ {
			result, err := pool.Draw(drawUserID)
			require.NoError(t, err)
			giftIDResults = append(giftIDResults, result.GiftID)
			isGuaranteedResults = append(isGuaranteedResults, result.Guaranteed)
		}
		assert.Equal(
			t,
			[]int64{
				90001, 90001, 90001, 90001, 90001, 90001, 90001, 90001, 90001, 90002,
				90001, 90001, 90001, 90001, 90001, 90001, 90001, 90001, 90001, 90002,
			},
			giftIDResults,
		)
		assert.Equal(
			t,
			[]bool{
				false, false, false, false, false, false, false, false, false, true,
				false, false, false, false, false, false, false, false, false, true,
			},
			isGuaranteedResults,
		)
	})
}

func TestPoolGift_IsSSR(t *testing.T) {
	assert := assert.New(t)

	assert.False(PoolGift{SSRID: 0}.IsSSR(5))
	assert.False(PoolGift{SSRID: 0}.IsSSR(0))

	assert.True(PoolGift{SSRID: 5}.IsSSR(5))
	assert.False(PoolGift{SSRID: 5}.IsSSR(1))
}

func TestPoolGift_GuessSSRID(t *testing.T) {
	assert := assert.New(t)

	pool := PoolGift{
		Rates: map[int64]int{
			90001: 50,
			90002: 100,
			90003: 10,
		},
	}
	assert.EqualValues(90003, pool.GuessSSRID())
	// 测试有多个最小概率的情况
	pool.Rates[90001] = 10
	assert.EqualValues(0, pool.GuessSSRID())
}

func TestPoolGift_isGuaranteed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(123)
	pool := PoolGift{
		GiftID:    123,
		GiftNum:   1,
		SSRID:     123,
		MissLimit: 1,
		Rates: map[int64]int{
			123:  5,
			1234: 10,
		},
	}
	key := pool.KeyDrawStatus(userID)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.Del(keys.KeyGiftDrawMissCount3.Format(userID, pool.GiftID, pool.GiftNum)).Err())

	require.NoError(pool.addDrawResult(userID, &PoolGiftDrawResult{GiftID: 456}))
	require.NoError(pool.addDrawResult(userID, &PoolGiftDrawResult{GiftID: 789}))
	assert.True(pool.isGuaranteed(userID, 2))
	ttl, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.Greater(int64(ttl), 29*24*int64(time.Hour))
	require.NoError(pool.addDrawResult(userID, &PoolGiftDrawResult{GiftID: pool.SSRID}))
	pool.MissLimit = 3
	assert.False(pool.isGuaranteed(userID, 0))
	pool.SSRID = 0
	require.NoError(pool.addDrawResult(userID, &PoolGiftDrawResult{GiftID: 789}))
}

func TestPoolGift_addDrawResult(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	drawUserID := int64(90000)
	pool := PoolGift{
		Rates: map[int64]int{
			90001: 50,
			90002: 50,
			90003: 0,
		},
	}
	drawCounts := make(map[int64]int, 2)
	for i := 0; i < 100; i++ {
		result, err := pool.Draw(drawUserID)
		require.NoError(err)
		drawCounts[result.GiftID]++
	}
	tutil.PrintJSON(drawCounts)
	require.NoError(service.Redis.Del(pool.KeyDrawStatus(drawUserID)).Err())
	require.NoError(service.Redis.Del(keys.KeyGiftDrawMissCount3.Format(drawUserID, pool.GiftID, pool.GiftNum)).Err())
	pool.SSRID = 90003
	pool.MissLimit = 3
	drawCounts = make(map[int64]int, 2)
	for i := 0; i < 300; i++ {
		result, err := pool.Draw(drawUserID)
		require.NoError(err)
		if i%3 == 2 {
			assert.Equal(pool.SSRID, result.GiftID)
			assert.True(result.Guaranteed)
		} else {
			drawCounts[result.GiftID]++
		}
	}
	tutil.PrintJSON(drawCounts)
}

func TestFindPoolGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dp, err := FindPoolGift(123, 100)
	require.NoError(err)
	assert.Nil(dp)

	dp = &PoolGift{
		PoolID:    1,
		GiftID:    80001,
		GiftNum:   1,
		Rates:     map[int64]int{90001: 85, 90002: 10, 90003: 5},
		SSRID:     90003,
		MissLimit: 100,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = CollectionDrawPool().UpdateOne(ctx,
		bson.M{"gift_id": dp.GiftID, "gift_num": dp.GiftNum}, bson.M{"$set": dp},
		options.Update().SetUpsert(true))
	require.NoError(err)
	dp, err = FindPoolGift(dp.GiftID, dp.GiftNum)
	require.NoError(err)
	require.NotNil(dp)
	assert.True(dp.Valid())
}

func TestListPoolGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	giftIDs := []int64{80001}
	pools, err := ListPoolGift(giftIDs)
	require.NoError(err)
	foundIdx := slices.IndexFunc(pools, func(pool *PoolGift) bool {
		return pool.GiftNum == 1
	})
	require.GreaterOrEqual(foundIdx, 0)
	assert.EqualValues(1, pools[foundIdx].PoolID)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = CollectionDrawPool().DeleteMany(ctx, bson.M{"gift_id": 0})
	require.NoError(err)

	giftIDs = []int64{0}
	pools, err = ListPoolGift(giftIDs)
	require.NoError(err)
	assert.Len(pools, 0)
}

func TestDrawRate(t *testing.T) {
	// 这里模拟的是单个用户连续抽的情况
	pool := PoolGift{
		Rates: map[int64]int{
			90006: 96500,
			90007: 3500,
		},
		SSRID:     90007,
		MissLimit: 70,
	}
	drawRes := make(map[int64]int, 2)
	logger.Debug("start")
	drawCount := int64(1000000)
	for i := int64(0); i < drawCount; i++ {
		drawID := pool.testDraw()
		drawRes[drawID]++
	}
	for giftID, count := range drawRes {
		tutil.Debugf("gift %d: %f", giftID, float64(count)/float64(drawCount))
	}
}

var missCount int64

func (pg PoolGift) testDraw() (giftID int64) {
	defer func() {
		if giftID == pg.SSRID {
			missCount = 0
			return
		}
		missCount++
	}()
	if pg.MissLimit < missCount+1 {
		giftID = pg.SSRID
		return
	}
	giftIDs := make([]int64, 0, len(pg.Rates))
	weights := make([]int, 0, len(pg.Rates))
	for giftID, w := range pg.Rates {
		giftIDs = append(giftIDs, giftID)
		weights = append(weights, w)
	}
	seed, err := goutil.NewDiscreteDistribution(weights, source, false)
	if err != nil {
		logger.WithFields(logger.Fields{
			"gift_id":  pg.GiftID,
			"gift_num": pg.GiftNum,
		}).Error(err)
		return
	}
	giftID = giftIDs[seed.NextInt()]
	return
}

func TestPoolGift_IsLockRequired(t *testing.T) {
	assert := assert.New(t)

	// 测试普通礼物池不需要加锁
	pool := PoolGift{
		Rates: map[int64]int{
			90001: 50,
			90002: 50,
		},
	}
	assert.False(pool.IsLockRequired())

	// 测试有保底机制的礼物池需要加锁
	pool.MissLimit = 10
	assert.True(pool.IsLockRequired())

	// 测试有 PRD 配置的礼物池需要加锁
	pool = PoolGift{
		Rates: map[int64]int{
			90001: 50,
			90002: 50,
		},
		PRDConfig: &PRDConfig{
			InitialC:   0.01,
			IncrementC: 0.01,
		},
	}
	assert.True(pool.IsLockRequired())

	// 测试同时有保底和 PRD 的礼物池需要加锁
	pool.MissLimit = 10
	assert.True(pool.IsLockRequired())
}

func TestPoolGift_RollbackDrawResult(t *testing.T) {
	tests := []struct {
		name      string
		pg        PoolGift
		userID    int64
		result    *PoolGiftDrawResult
		wantSSR   int64
		wantMiss  int64
		shouldErr bool
	}{
		{
			name: "回滚 SSR 抽奖结果",
			pg: PoolGift{
				SSRID:     100,
				MissLimit: 10,
			},
			userID: 123,
			result: &PoolGiftDrawResult{
				GiftID: 100,
				IsSSR:  true,
			},
			wantSSR:  -1, // SSR 计数应该减 1
			wantMiss: 0,  // miss 计数保持不变
		},
		{
			name: "回滚非 SSR 抽奖结果",
			pg: PoolGift{
				SSRID:     100,
				MissLimit: 10,
			},
			userID: 123,
			result: &PoolGiftDrawResult{
				GiftID: 200,
				IsSSR:  false,
			},
			wantSSR:  0,  // SSR 计数保持不变
			wantMiss: -1, // miss 计数应该减 1
		},
		{
			name: "没有配置 SSR 时直接返回",
			pg: PoolGift{
				SSRID:     0, // 没有配置 SSR
				MissLimit: 0,
			},
			userID: 123,
			result: &PoolGiftDrawResult{
				GiftID: 200,
				IsSSR:  false,
			},
			wantSSR:  0,
			wantMiss: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			key := tt.pg.KeyDrawStatus(tt.userID)
			err := service.Redis.HMSet(key, map[string]interface{}{
				fieldSSRCount:  0,
				fieldMissCount: 0,
			}).Err()
			require.NoError(t, err)

			// 执行回滚
			err = tt.pg.RollbackDrawResult(tt.userID, tt.result)
			if tt.shouldErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// 验证结果
			ssrCount, err := service.Redis.HGet(key, fieldSSRCount).Int64()
			require.NoError(t, err)
			missCount, err := service.Redis.HGet(key, fieldMissCount).Int64()
			require.NoError(t, err)

			assert.Equal(t, tt.wantSSR, ssrCount)
			assert.Equal(t, tt.wantMiss, missCount)
		})
	}
}
