package gift

// EffectOptions 特效选项
type EffectOptions struct {
	Words   *EffectOptionWords   `json:"words,omitempty"`
	Avatars *EffectOptionAvatars `json:"avatars,omitempty"`
}

// EffectOptionWords 特效赠言配置
type EffectOptionWords struct {
	Text     string `json:"text"`
	ImageURL string `json:"image_url"`
}

// EffectOptionAvatars 特效头像配置
type EffectOptionAvatars struct {
	CreatorIconURL string `json:"creator_iconurl"`
	UserIconURL    string `json:"user_iconurl"`
}

// SetEffectWords 设置特效赠言
func (ng *NotifyGift) SetEffectWords(w *EffectOptionWords) {
	if ng.EffectOptions == nil {
		ng.EffectOptions = new(EffectOptions)
	}
	ng.EffectOptions.Words = w
}

// SetEffectCreatorIconURL 设置特效主播头像
func (ng *NotifyGift) SetEffectCreatorIconURL(creatorIconURL string) {
	if ng.EffectOptions == nil {
		ng.EffectOptions = new(EffectOptions)
	}
	if ng.EffectOptions.Avatars == nil {
		ng.EffectOptions.Avatars = new(EffectOptionAvatars)
	}
	ng.EffectOptions.Avatars.CreatorIconURL = creatorIconURL
}

// SetEffectUserIconURL 设置特效用户头像
func (ng *NotifyGift) SetEffectUserIconURL(userIconURL string) {
	if ng.EffectOptions == nil {
		ng.EffectOptions = new(EffectOptions)
	}
	if ng.EffectOptions.Avatars == nil {
		ng.EffectOptions.Avatars = new(EffectOptionAvatars)
	}
	ng.EffectOptions.Avatars.UserIconURL = userIconURL
}
