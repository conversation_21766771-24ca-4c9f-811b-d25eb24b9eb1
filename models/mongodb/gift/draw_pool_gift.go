package gift

import (
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// PoolGift 礼物奖池
type PoolGift struct {
	OID    primitive.ObjectID `bson:"_id,omitempty"`
	PoolID int64              `bson:"pool_id"`
	Type   int                `bson:"type"` // 总是 0

	GiftID  int64 `bson:"gift_id"`
	GiftNum int   `bson:"gift_num"`

	// SSRID 大奖 ID
	SSRID     int64         `bson:"ssr_id"`
	MissLimit int64         `bson:"miss_limit"`
	Rates     map[int64]int `bson:"rates"`      // map[giftID]掉率
	PRDConfig *PRDConfig    `bson:"prd_config"` // PRD 随机分布算法配置，为空则使用普通随机算法
}

// PRDConfig PRD 随机分布算法配置
type PRDConfig struct {
	// 初始概率，范围在 (0.0, 1.0) 之间的浮点数
	InitialC float64 `bson:"initial_c"`

	// 每次未抽中后递增的概率，范围在 (0.0, 1.0) 之间的浮点数
	IncrementC float64 `bson:"increment_c"`

	// 从第几次抽中 SSR 之后启用 PRD 算法
	// 如果设置为 0，则从第一次开始就使用 PRD 算法
	WarmupSSRCount int64 `bson:"warmup_ssr_count"`
}

type drawStatus struct {
	missCount int64 // 连续未抽中大奖的次数
	ssrCount  int64 // 累计抽中大奖的次数
}

func newDrawStatusFromCache(cache map[string]string) (*drawStatus, error) {
	if cache == nil {
		return &drawStatus{}, nil
	}
	missCount := int64(0)
	if v, ok := cache[fieldMissCount]; ok {
		var err error
		missCount, err = strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, err
		}
	}
	ssrCount := int64(0)
	if v, ok := cache[fieldSSRCount]; ok {
		var err error
		ssrCount, err = strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, err
		}
	}
	return &drawStatus{missCount: missCount, ssrCount: ssrCount}, nil
}

// PoolGiftType 奖池类型
func (pg PoolGift) PoolGiftType() int {
	switch pg.Type {
	case PoolTypeGift:
		return TypeDrawReceive
	case PoolTypeRebateGift:
		return TypeRebate
	default:
		panic(fmt.Sprintf("invalid pool type: %d, pool id: %d", pg.Type, pg.PoolID))
	}
}

// Valid 掉率是否合理
// 1. 礼物是否都存在
// 2. 掉率是否正确
// 3. PRD 随机抽奖配置是否正确
// 4. 在掉率有效的情况会返回奖池查询的到的礼物
func (pg PoolGift) Valid() (bool, map[int64]Gift) {
	f := logger.Fields{"gift_id": pg.GiftID, "gift_num": pg.GiftNum}
	if pg.MissLimit != 0 {
		if pg.MissLimit < 0 || pg.SSRID == 0 {
			logger.WithFields(f).Error("保底次数错误")
			return false, nil
		}
	}
	if len(pg.Rates) == 0 {
		logger.WithFields(f).Error("未配置掉落")
		return false, nil
	}
	if pg.PRDConfig != nil {
		if pg.SSRID == 0 {
			logger.WithFields(f).Error("PRD 随机抽奖需要配置保底礼物")
			return false, nil
		}
		if pg.PRDConfig.InitialC <= 0 || pg.PRDConfig.InitialC >= 1 {
			logger.WithFields(f).Error("PRD 随机抽奖初始概率配置错误")
			return false, nil
		}
		if pg.PRDConfig.IncrementC <= 0 || pg.PRDConfig.IncrementC >= 1 {
			logger.WithFields(f).Error("PRD 随机抽奖递增概率配置错误")
			return false, nil
		}
	}
	gs, err := FindAllShowingGifts()
	if err != nil {
		logger.Error(err)
		return false, nil
	}

	poolGiftType := pg.PoolGiftType()
	giftMap := goutil.ToMap(gs, "GiftID").(map[int64]Gift)
	if pg.SSRID != 0 {
		g, ok := giftMap[pg.SSRID]
		if !ok || g.Type != poolGiftType {
			logger.WithFields(f).Error("保底礼物配置错误")
			return false, nil
		}
	}
	found := 0
	for giftID, weight := range pg.Rates {
		if weight < 0 {
			logger.WithFields(f).Error("掉率配置错误")
			return false, nil
		}
		if weight == 0 {
			continue
		}
		found++
		g, ok := giftMap[giftID]
		if !ok || g.Type != poolGiftType {
			logger.WithFields(f).Error("礼物配置错误")
			return false, nil
		}
	}
	return found > 0, giftMap
}

// IsLockRequired 抽奖是否需要加锁
// 存在保底机制或者 PRD 随机抽奖时，会根据是否抽中大奖来更新 miss 次数，因此需要加锁来保证 miss 次数的正确性
func (pg *PoolGift) IsLockRequired() bool {
	return pg.MissLimit > 0 || pg.PRDConfig != nil
}

// PoolGiftDrawResult 抽奖结果
type PoolGiftDrawResult struct {
	GiftID     int64
	IsSSR      bool
	Guaranteed bool
}

// Draw 抽奖
// 保底机制：如果同一个用户前 n 次没抽中大奖，则第 n 次将抽中大奖
func (pg PoolGift) Draw(userID int64) (*PoolGiftDrawResult, error) {
	result, err := pg.doDraw(userID)
	if err != nil {
		return nil, err
	}
	err = pg.addDrawResult(userID, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (pg PoolGift) doDraw(userID int64) (*PoolGiftDrawResult, error) {
	if pg.SSRID == 0 {
		giftID, err := randomKeyFromRates(pg.Rates)
		if err != nil {
			return nil, err
		}
		return &PoolGiftDrawResult{GiftID: giftID, IsSSR: false, Guaranteed: false}, nil
	}
	drawStatus, err := pg.getDrawStatus(userID)
	if err != nil {
		return nil, err
	}
	// 优先查看当前是否达到保底条件，如果是保底，则返回大奖礼物
	if pg.isGuaranteed(userID, drawStatus.missCount) {
		return &PoolGiftDrawResult{GiftID: pg.SSRID, IsSSR: true, Guaranteed: true}, nil
	}
	if pg.PRDConfig != nil {
		giftID, err := pg.drawByPRD(userID, drawStatus)
		if err != nil {
			return nil, err
		}
		return &PoolGiftDrawResult{GiftID: giftID, IsSSR: pg.IsSSR(giftID), Guaranteed: false}, nil
	}
	giftID, err := randomKeyFromRates(pg.Rates)
	if err != nil {
		return nil, err
	}
	return &PoolGiftDrawResult{GiftID: giftID, IsSSR: pg.IsSSR(giftID), Guaranteed: false}, nil
}

func (pg PoolGift) drawByPRD(userID int64, drawStatus *drawStatus) (int64, error) {
	// 在配置的次数之前使用普通随机算法
	if pg.PRDConfig.WarmupSSRCount > drawStatus.ssrCount {
		return randomKeyFromRates(pg.Rates)
	}

	// 使用 PRD 随机分布算法判断是否抽中大奖
	// 当前概率 = 初始概率 + (连续未中次数 * 递增概率)
	ssrRate := pg.PRDConfig.InitialC + float64(drawStatus.missCount)*pg.PRDConfig.IncrementC
	if ssrRate >= 1 || ssrRate > rand.New(source).Float64() {
		return pg.SSRID, nil
	}
	// 如果未抽中大奖，则在非大奖组内按概率随机抽取一个
	rates := make(map[int64]int, len(pg.Rates)-1)
	for k, v := range pg.Rates {
		if k != pg.SSRID {
			rates[k] = v
		}
	}
	return randomKeyFromRates(rates)
}

func randomKeyFromRates[T comparable](rates map[T]int) (T, error) {
	keys := make([]T, 0, len(rates))
	weights := make([]int, 0, len(rates))
	for k, w := range rates {
		keys = append(keys, k)
		weights = append(weights, w)
	}
	distribution, err := goutil.NewDiscreteDistribution(weights, source, false)
	if err != nil {
		var zero T
		return zero, err
	}
	return keys[distribution.NextInt()], nil
}

// IsSSR 是否是大奖
func (pg PoolGift) IsSSR(giftID int64) bool {
	if pg.SSRID == 0 {
		return false
	}

	return pg.SSRID == giftID
}

// GuessSSRID 根据概率猜测大奖的礼物 ID
func (pg PoolGift) GuessSSRID() int64 {
	var ssrID, minWeightCount int64
	minWeight := -1
	for giftID, weight := range pg.Rates {
		if minWeight == -1 || weight < minWeight {
			minWeightCount = 1
			ssrID = giftID
			minWeight = weight
		} else if weight == minWeight {
			minWeightCount++
		}
	}
	if minWeightCount > 1 {
		// 如果同时存在多个 minWeight 相同则视为没有大奖
		return 0
	}
	return ssrID
}

// isGuaranteed 是否达到保底条件
func (pg PoolGift) isGuaranteed(userID int64, missCount int64) bool {
	if pg.SSRID == 0 || pg.MissLimit == 0 {
		return false
	}

	return pg.MissLimit <= missCount+1
}

// shouldTrackDrawResult 判断是否需要记录抽奖结果
func (pg PoolGift) shouldTrackDrawResult() bool {
	if pg.SSRID == 0 {
		return false
	}
	// PRD 随机抽奖也需要记录 miss 次数，用于累加大奖概率
	if pg.MissLimit == 0 && pg.PRDConfig == nil {
		return false
	}
	return true
}

// addDrawResult 更新抽奖结果
func (pg PoolGift) addDrawResult(userID int64, result *PoolGiftDrawResult) error {
	if !pg.shouldTrackDrawResult() {
		return nil
	}

	pipe := service.Redis.TxPipeline()
	key := pg.KeyDrawStatus(userID)
	if result.IsSSR {
		// 抽中大奖时，重置 miss 计数并增加 SSR 计数
		pipe.HSet(key, fieldMissCount, 0)
		pipe.HIncrBy(key, fieldSSRCount, 1)
	} else {
		// 未抽中大奖时，增加 miss 计数
		pipe.HIncrBy(key, fieldMissCount, 1)
	}
	pipe.Expire(key, 30*24*time.Hour)
	_, err := pipe.Exec()
	if err != nil {
		if result.Guaranteed {
			// 保底抽奖，更新抽奖状态失败返回错误避免连续触发保底
			return err
		}
		// 主动抽中大奖，更新抽奖状态失败则记录错误日志，不影响抽奖结果
		// 抽中小奖时只会递增 miss_count，更新抽奖状态失败也无明显影响
		logger.WithFields(logger.Fields{
			"user_id":  userID,
			"gift_id":  pg.GiftID,
			"gift_num": pg.GiftNum,
			"ssr_id":   pg.SSRID,
		}).Errorf("更新抽奖状态失败: %v", err)
		// PASS
	}
	return nil
}

// RollbackDrawResult 回滚抽奖结果
func (pg PoolGift) RollbackDrawResult(userID int64, drawResult *PoolGiftDrawResult) error {
	if !pg.shouldTrackDrawResult() {
		return nil
	}

	pipe := service.Redis.TxPipeline()
	key := pg.KeyDrawStatus(userID)
	if drawResult.IsSSR {
		// 回滚抽中大奖的结果：减少 SSR 计数
		// 由于抽到大奖后会重置 miss 计数，原始的 miss 计数已经丢失，暂不考虑回滚
		pipe.HIncrBy(key, fieldSSRCount, -1)
	} else {
		// 回滚未中大奖的结果：减少 miss 计数
		pipe.HIncrBy(key, fieldMissCount, -1)
	}
	pipe.Expire(key, 30*24*time.Hour)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

const (
	fieldSSRCount  = "ssr_count"
	fieldMissCount = "miss_count"
)

// KeyDrawStatus 大奖抽取状态缓存 key
func (pg PoolGift) KeyDrawStatus(userID int64) string {
	return keys.KeyGiftDrawStatus3.Format(userID, pg.GiftID, pg.GiftNum)
}

// legacyCacheDeadline 旧缓存兼容截止时间
const legacyCacheDeadline = 1743436800 // 2025-04-01 00:00:00+08:00

func (pg PoolGift) getDrawStatus(userID int64) (*drawStatus, error) {
	drawStatusKey := pg.KeyDrawStatus(userID)
	drawStatusCache, err := service.Redis.HGetAll(drawStatusKey).Result()
	if err != nil {
		return nil, err
	}
	if len(drawStatusCache) == 0 {
		if goutil.TimeNow().Unix() < legacyCacheDeadline {
			// TEMP: 兼容期先尝试从新的缓存中获取，如果新缓存不存在再从旧的缓存中获取。兼容期结束后，直接从新的缓存中获取。
			missCountKey := keys.KeyGiftDrawMissCount3.Format(userID, pg.GiftID, pg.GiftNum)
			missCount, err := service.Redis.Get(missCountKey).Int64()
			if err != nil && !serviceredis.IsRedisNil(err) {
				return nil, err
			}
			// 将旧缓存中的数据迁移到新的 HASH 缓存中
			pipe := service.Redis.TxPipeline()
			pipe.HSet(drawStatusKey, fieldMissCount, missCount)
			pipe.Expire(drawStatusKey, 30*24*time.Hour)
			if _, err := pipe.Exec(); err != nil {
				return nil, err
			}
			return &drawStatus{missCount: missCount}, nil
		}
		return &drawStatus{}, nil
	}

	drawStatus, err := newDrawStatusFromCache(drawStatusCache)
	if err != nil {
		return nil, err
	}
	return drawStatus, nil
}

// KeyLockGiftDraw 随机礼物抽奖锁
func (pg PoolGift) KeyLockGiftDraw(userID int64) string {
	return keys.LockGiftDraw3.Format(userID, pg.GiftID, pg.GiftNum)
}

// FindPoolGift find PoolGift
func FindPoolGift(giftID int64, giftNum int) (*PoolGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var res PoolGift
	err := CollectionDrawPool().
		FindOne(ctx, bson.M{
			"type":     PoolTypeGift,
			"gift_id":  giftID,
			"gift_num": giftNum}).Decode(&res)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return &res, nil
}

// ListPoolGift 查询礼物奖池
func ListPoolGift(giftIDs []int64) ([]*PoolGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var pools []*PoolGift
	cur, err := CollectionDrawPool().Find(ctx, bson.M{
		"type":    PoolTypeGift,
		"gift_id": bson.M{"$in": giftIDs},
	})
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &pools)
	if err != nil {
		return nil, err
	}
	return pools, nil
}
