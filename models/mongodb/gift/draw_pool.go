package gift

import (
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"math"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 原来的随机礼物都需要加上 type 字段
// REVIEW: 前缀从 Pool -> DrawPool?
const (
	// PoolTypeGift 奖池类型为随机礼物
	PoolTypeGift = iota
	// PoolTypeGashapon 奖池类型为盲盒奖池
	PoolTypeGashapon
	// PoolTypeRebateGift 奖池类型为白给礼物奖池
	PoolTypeRebateGift
	// PoolTypeLuckyBox 奖池类型为宝盒
	PoolTypeLuckyBox
)

// BuffDuration buff 持续时间
const BuffDuration = 180 * time.Second

var (
	source = goutil.NewLockedSource(goutil.TimeNow().Unix())
)

// BuffRate 大奖翻倍里的奖品、对应的翻倍概率、翻倍次数、是否要飘屏和飘屏样式、命中 buff 后各礼物权重
type BuffRate struct {
	GiftID     int64   `bson:"gift_id" json:"gift_id,omitempty"`
	Rate       int     `bson:"rate" json:"rate,omitempty"` // 各 buff 权重
	Multiplier float64 `bson:"multiplier" json:"multiplier,omitempty"`

	Notify   bool  `bson:"notify" json:"-"`
	BubbleID int64 `bson:"bubble_id" json:"-"`

	GiftRates map[int64]int `bson:"gift_rates,omitempty" json:"gift_rates"` // 命中 buff 后各礼物权重
}

// PoolGashapon 盲盒相关奖池
type PoolGashapon struct {
	OID    primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	PoolID int64              `bson:"pool_id" json:"pool_id"`
	Type   int                `bson:"type,omitempty" json:"-"` // 总是为 1

	Rates                      map[int64]int `bson:"rates,omitempty" json:"rates"`                           // 基础奖池的奖品概率：map[giftID]概率
	RankGiftIDs                []int64       `bson:"rank_gift_ids,omitempty" json:"rank_gift_ids,omitempty"` // 用户榜展示的礼物
	GrandGiftID                int64         `bson:"grand_gift_id" json:"grand_gift_id"`
	GrandNotifyBubbleID        int64         `bson:"grand_notify_bubble_id" json:"grand_notify_bubble_id"`
	GrandNotifyMessage         string        `bson:"grand_notify_message" json:"grand_notify_message"`                     // 抽中奖品 != 翻倍奖品时的文案
	GrandNotifyMessageWithBuff string        `bson:"grand_notify_message_with_buff" json:"grand_notify_message_with_buff"` // 抽中奖品 == 翻倍奖品时的文案

	BuffRates  []BuffRate `bson:"buff_rates,omitempty" json:"-"`  // 大奖翻倍的奖池
	BuffMoment string     `bson:"buff_moment" json:"buff_moment"` // 大奖翻倍玩法名称

	CurrentBuff *BuffRate `bson:"-" json:"current_buff"`
}

// GashaponBuff 盲盒奖池 buff
type GashaponBuff struct {
	// 礼物字段
	GiftID        int64  `json:"gift_id,omitempty"`
	Name          string `json:"name,omitempty"`            // 礼物名称
	IconURL       string `json:"icon_url"`                  // buff 图标，暂时同礼物图标
	IconActiveURL string `json:"icon_active_url,omitempty"` // buff 动态图标，暂时同礼物动态图标

	RemainDuration int64  `json:"remain_duration,omitempty"`
	IsNew          bool   `json:"is_new,omitempty"`
	BuffDuration   int64  `json:"buff_duration,omitempty"`
	Multiplier     string `json:"multiplier,omitempty"`
	Moment         string `json:"moment,omitempty"` // 玩法名称，未下发时，默认超能时刻

	PoolID   int64 `json:"-"` // 奖池 ID
	Notify   bool  `json:"-"`
	BubbleID int64 `json:"-"`
}

// DrawResult 抽奖结果
type DrawResult struct {
	GiftID int64 // REVIEW: 应该返回礼物？
	Gift   *Gift // 抽奖的时候未赋值，赋值待外部实现赋值
	Num    int
}

// CollectionDrawPool 奖池 collection
func CollectionDrawPool() *mongo.Collection {
	return service.MongoDB.Collection("gift_draw_pool")
}

// FindGashaponBuff 根据直播间 ID 获取该直播间对应的 buff
func FindGashaponBuff(roomID int64) (*GashaponBuff, error) {
	key := keys.KeyGashaponPoolRoom1.Format(roomID)
	pipe := service.Redis.TxPipeline()
	getCmd := pipe.Get(key)
	ttlCmd := pipe.TTL(key)
	_, err := pipe.Exec()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			return nil, err
		}
		return nil, nil
	}

	bytes, err := getCmd.Bytes()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			return nil, err
		}
		return nil, nil
	}
	if len(bytes) == 0 {
		return nil, nil
	}

	var pool PoolGashapon
	err = json.Unmarshal(bytes, &pool)
	if err != nil {
		return nil, err
	}
	if pool.CurrentBuff == nil {
		return nil, nil
	}
	g, err := FindShowingGiftByGiftID(pool.CurrentBuff.GiftID)
	if err != nil {
		return nil, err
	}
	if g == nil {
		return nil, nil
	}
	return &GashaponBuff{
		GiftID:         g.GiftID,
		Name:           g.Name,
		IconURL:        g.Icon,
		IconActiveURL:  g.IconActive,
		RemainDuration: ttlCmd.Val().Milliseconds(),
		BuffDuration:   BuffDuration.Milliseconds(),
		Multiplier:     pool.CurrentBuff.FormattedMultiplier(),
		PoolID:         pool.PoolID,
	}, nil
}

// FindPool find pool
func FindPool(poolID int64, poolType int) (*PoolGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var res PoolGift
	err := CollectionDrawPool().
		FindOne(ctx, bson.M{
			"pool_id": poolID,
			"type":    poolType,
		}).Decode(&res)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return &res, nil
}

// GashaponDrawParam 盲盒抽奖参数
type GashaponDrawParam struct {
	DrawCount int
	RoomID    int64

	GuaranteedPool map[int64]int // 连击必中的奖池
}

// FindPoolGashapon find PoolGashapon
func FindPoolGashapon(poolID, roomID int64) (*PoolGashapon, error) {
	pg, err := findPoolGashaponInBuff(poolID, roomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if pg != nil {
		return pg, nil
	}
	return FindPoolGashaponInMongo(poolID)
}

func findPoolGashaponInBuff(poolID, roomID int64) (*PoolGashapon, error) {
	poolKey := keys.KeyGashaponPoolRoom1.Format(roomID)
	v, err := service.Redis.Get(poolKey).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	if len(v) == 0 {
		return nil, nil
	}
	var pg PoolGashapon
	err = json.Unmarshal(v, &pg)
	if err != nil {
		return nil, err
	}
	if pg.PoolID != poolID {
		// buff 后的奖池可能是前一批盲盒的奖池
		return nil, nil
	}
	return &pg, nil
}

// FindPoolGashaponInMongo 查询奖池
func FindPoolGashaponInMongo(poolID int64) (*PoolGashapon, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pg := new(PoolGashapon)
	err := CollectionDrawPool().FindOne(ctx,
		bson.M{"pool_id": poolID, "type": PoolTypeGashapon}).Decode(pg)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return pg, nil
}

// Draw 抽奖，抽 DrawCount 次, GuaranteedPool 不为 nil 的话会触发连抽必中
// 盲盒礼物文档：https://info.missevan.com/pages/viewpage.action?pageId=36284108
// 返回抽奖结果、抽中的保底礼物 ID 和 error
func (pg PoolGashapon) Draw(param *GashaponDrawParam) ([]DrawResult, int64, error) {
	if param.DrawCount <= 0 {
		return nil, 0, errors.New("invalid draw count")
	}
	if param.RoomID <= 0 {
		return nil, 0, errors.New("invalid room_id")
	}
	giftNumMap := make(map[int64]int)
	baseDistribution, baseGiftIDs, err := PoolToDistribution(pg.Rates)
	if err != nil {
		logger.Error(err)
		return nil, 0, err
	}
	var guaranteedGiftID int64 // 保底抽中的礼物 ID
	for i := 0; i < param.DrawCount; i++ {
		if param.GuaranteedPool != nil &&
			i == param.DrawCount-1 {
			var found bool
			// 最后一次抽奖时检查是否满足了保底，即是否抽中了必中奖池中的奖品
			for giftID := range param.GuaranteedPool {
				if giftNumMap[giftID] > 0 {
					found = true
					break
				}
			}
			if !found {
				// 连抽必中
				guaranteedGiftID, err = param.EnsureDraw()
				if err != nil {
					return nil, 0, err
				}
				giftNumMap[guaranteedGiftID]++
				break
			}
		}
		giftID := baseGiftIDs[baseDistribution.NextInt()]
		giftNumMap[giftID]++
	}
	drawRes := make([]DrawResult, 0, len(giftNumMap))
	for giftID, num := range giftNumMap {
		drawRes = append(drawRes, DrawResult{
			GiftID: giftID,
			Num:    num,
		})
	}
	// 按照抽中的礼物数量从少到多，依次返回礼物 ID 和对应的数量
	// 数量相同按照礼物 ID 倒序排（一般礼物 ID 大的更贵）
	// TODO: 后续改成这里查询礼物，按照价格排序
	sort.Slice(drawRes, func(i, j int) bool {
		if drawRes[i].Num != drawRes[j].Num {
			return drawRes[i].Num < drawRes[j].Num
		}
		return drawRes[i].GiftID > drawRes[j].GiftID
	})
	return drawRes, guaranteedGiftID, nil
}

// EnsureDraw 连击必中获取盲盒
func (param *GashaponDrawParam) EnsureDraw() (int64, error) {
	// 连抽必中
	comboDistribution, comboGiftIDs, err := PoolToDistribution(param.GuaranteedPool)
	if err != nil {
		logger.Error(err)
		return 0, err
	}
	giftID := comboGiftIDs[comboDistribution.NextInt()]
	return giftID, nil
}

// AddPoolGashaponBuff 增加 buff
func AddPoolGashaponBuff(poolID, roomID int64) (*GashaponBuff, error) {
	pg, err := FindPoolGashaponInMongo(poolID)
	if err != nil {
		return nil, err
	}
	if pg == nil {
		logger.WithFields(logger.Fields{
			"pool_id": poolID,
		}).Error("盲盒奖池不存在")
		return nil, nil
	}
	if len(pg.BuffRates) == 0 {
		return nil, nil
	}
	weights := make([]int, len(pg.BuffRates))
	for i := range pg.BuffRates {
		weights[i] = pg.BuffRates[i].Rate
	}
	d, err := goutil.NewDiscreteDistribution(weights, source, false)
	if err != nil {
		return nil, err
	}
	br := pg.BuffRates[d.NextInt()]
	if br.Multiplier <= 0 {
		logger.WithField("pool_id", pg.PoolID).Error("奖池 buff 配置异常")
		return nil, nil
	}
	pg.CurrentBuff = &br
	g, err := FindShowingGiftByGiftID(br.GiftID)
	if err != nil {
		return nil, err
	}
	if g == nil {
		logger.WithFields(logger.Fields{
			"pool_id": poolID,
			"gift_id": br.GiftID,
		}).Error("大奖翻倍礼物缺失")
		return nil, nil
	}
	if pg.Rates == nil {
		logger.WithFields(logger.Fields{
			"pool_id": poolID,
		}).Error("buff 奖池数据异常")
		return nil, nil
	}
	// 根据 buff 倍数，获取 pg.Rates 中各礼物的权重
	pg.Rates = buildBuffRates(pg.CurrentBuff, pg.Rates)
	if len(pg.Rates) == 0 {
		logger.WithFields(logger.Fields{
			"pool_id":    poolID,
			"gift_id":    br.GiftID,
			"multiplier": br.Multiplier,
		}).Error("buff 奖池数据翻倍异常")
		return nil, nil
	}
	j, err := json.Marshal(pg)
	if err != nil {
		return nil, err
	}
	err = service.Redis.Set(keys.KeyGashaponPoolRoom1.Format(roomID), j, BuffDuration).Err()
	if err != nil {
		return nil, err
	}
	if err = newLiveGashaponBuffLog(roomID, pg).save(); err != nil {
		logger.Error(err)
		// PASS
	}
	gb := GashaponBuff{
		GiftID:         g.GiftID,
		Name:           g.Name,
		IconURL:        g.Icon,
		RemainDuration: BuffDuration.Milliseconds(),
		IsNew:          true,
		BuffDuration:   BuffDuration.Milliseconds(),
		Multiplier:     br.FormattedMultiplier(),
		Moment:         pg.BuffMoment,
		Notify:         br.Notify,
		BubbleID:       br.BubbleID,
	}
	return &gb, nil
}

// buildBuffRates 构建 buff 奖池的礼物权重
func buildBuffRates(buff *BuffRate, giftRates map[int64]int) map[int64]int {
	if buff == nil {
		return giftRates
	}
	if len(buff.GiftRates) > 0 {
		// 有配置礼物权重，则取配置权重
		return buff.GiftRates
	}
	return calculateBuffRates(giftRates, buff.GiftID, buff.Multiplier)
}

// calculateBuffRates 根据 buff 倍数，按比例重新计算 rates 中各礼物的权重
func calculateBuffRates(rateMap map[int64]int, buffGiftID int64, multiplier float64) map[int64]int {
	if len(rateMap) == 1 {
		return rateMap
	}
	var (
		scale = 10 // 权重扩大 10 倍计算，防止原有权重为 1 被计算为 0 的情况
	)
	// 计算原始总权重
	totalWeight := 0
	for _, weight := range rateMap {
		totalWeight += weight * scale
	}
	beforeWeight := rateMap[buffGiftID] * scale // 原始 buff 礼物权重
	afterWeight := int(float64(beforeWeight) * multiplier)
	if afterWeight > totalWeight {
		return nil
	}
	// 计算缩放因子，缩放因子 = (原始总权重 - 翻倍后 buff 礼物权重) / (原始总权重 - 原始 buff 礼物权重)
	scaleFactor := float64(totalWeight-afterWeight) / float64(totalWeight-beforeWeight)
	newRateMap := make(map[int64]int)
	for giftID, weight := range rateMap {
		if giftID == buffGiftID {
			newRateMap[giftID] = afterWeight
			continue
		}
		newRateMap[giftID] = int(math.Floor(float64(weight) * float64(scale) * scaleFactor))
	}

	return newRateMap
}

// PoolToDistribution 根据奖池获得随机分布
func PoolToDistribution(p map[int64]int) (goutil.Distribution, []int64, error) {
	keys := make([]int64, 0, len(p))
	weights := make([]int, 0, len(p))
	for k, w := range p {
		keys = append(keys, k)
		weights = append(weights, w)
	}
	d, err := goutil.NewDiscreteDistribution(weights, source, false)
	if err != nil {
		logger.Error(err)
		return nil, nil, err
	}
	return d, keys, nil
}

// FormattedMultiplier 获取格式化后的倍率
func (br BuffRate) FormattedMultiplier() string {
	return fmt.Sprintf("%.1f", br.Multiplier)
}

const (
	defaultGrandNotifyMessage = `<font color="${highlight_color}">${username}</font>` +
		`<font color="${normal_color}"> 在${gachapon_name}内${draw_count}获得 </font>` +
		`<font color="${highlight_color}">${gift_name}</font>` +
		`<font color="${normal_color}">，手气爆棚~</font>`
	defaultGrandNotifyMessageWithBuff = `<font color="${normal_color}">恭喜 </font>` +
		`<font color="${highlight_color}">${username}</font>` +
		`<font color="${normal_color}"> 在 </font>` +
		`<font color="${highlight_color}">${creator_username}</font>` +
		`<font color="${normal_color}"> 的直播间通过超能时刻 ${multiplier} 倍概率获得了 </font>` +
		`<font color="${highlight_color}">${gift_name}</font>`
)

// NotifyPayload 飘屏
func (pg PoolGashapon) NotifyPayload(drawCount int, drs []DrawResult,
	u *liveuser.Simple, roomID int64, creatorUsername, gachaponName string) *NotifyPayload {
	var dr *DrawResult
	for i := range drs {
		if drs[i].GiftID == pg.GrandGiftID {
			dr = &drs[i]
		}
	}
	if dr == nil {
		// 未抽中对应礼物
		return nil
	}

	format := map[string]string{
		"username":         html.EscapeString(u.Username),
		"creator_username": html.EscapeString(creatorUsername),
		"gift_name":        html.EscapeString(dr.Gift.Name),
		"gachapon_name":    html.EscapeString(gachaponName),
		"draw_count":       DrawCountStr(drawCount),
		"highlight_color":  colorWhite,
		"normal_color":     colorWhite,
	}
	b, err := bubble.FindSimple(pg.GrandNotifyBubbleID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if b != nil {
		b.AppendFormatParams(format)
	}
	msg := defaultGrandNotifyMessage
	if pg.GrandNotifyMessage != "" {
		msg = pg.GrandNotifyMessage
	}
	if pg.CurrentBuff != nil && pg.CurrentBuff.GiftID == dr.GiftID {
		// 当前奖品翻倍中
		format["multiplier"] = pg.CurrentBuff.FormattedMultiplier()
		if pg.GrandNotifyMessageWithBuff != "" {
			msg = pg.GrandNotifyMessageWithBuff
		} else {
			msg = defaultGrandNotifyMessageWithBuff
		}
	}
	msg = goutil.FormatMessage(msg, format)

	return &NotifyPayload{
		Type:         liveim.TypeNotify,
		NotifyType:   liveim.TypeGift,
		Event:        liveim.EventSend,
		RoomID:       roomID,
		User:         u,
		Gift:         NewNotifyGift(dr.Gift, dr.Num),
		Message:      msg,
		NotifyBubble: b,
	}
}

// DrawCountStr 抽奖次数文案
func DrawCountStr(count int) string {
	if count == 1 {
		return "单抽"
	}
	return fmt.Sprintf(" %d 连", count)
}
