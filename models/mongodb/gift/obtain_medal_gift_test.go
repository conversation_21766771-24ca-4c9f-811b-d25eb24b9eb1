package gift

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindObtainMedalGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	creatorID := int64(20230217)
	lm := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			CreatorID: creatorID,
			UserID:    creatorID,
			Status:    livemedal.StatusOwned,
		},
	}
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": creatorID})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, lm)
	require.NoError(err)

	key := keys.KeyRoomMedalPointMulti.Format(creatorID)
	require.NoError(service.DB.Table(liverecommendedelements.TableName()).Delete("", "element_type = ?",
		liverecommendedelements.ElementMultiMedalPoint).Error)
	require.NoError(service.LRURedis.Del(key).Err())

	g := FindObtainMedalGift(creatorID, creatorID, &goutil.Equipment{})
	require.NotNil(g)
	assert.Equal(livemedal.ObtainMedalGiftID, g.GiftID)

	now := goutil.TimeNow()
	err = liverecommendedelements.BatchAddRoomMedalPointMulti([]int64{creatorID}, now, now.Add(5*time.Second), liverecommendedelements.PointMulti{
		PointMultiAdd: 1,
	})
	require.NoError(err)
	require.NoError(service.LRURedis.Del(key).Err())
	g = FindObtainMedalGift(creatorID, creatorID, &goutil.Equipment{})
	require.NotNil(g)
	assert.Equal(livemedal.ObtainMedalHalfPriceGiftID, g.GiftID)

	// 没有勋章的用户能获取 1 折粉丝勋章的礼物
	userID := int64(20250311)
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	require.NoError(err)
	g = FindObtainMedalGift(creatorID, userID, &goutil.Equipment{})
	require.NotNil(g)
	assert.Equal(livemedal.ObtainMedalDiscountGiftID, g.GiftID)
}
