package gift

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

// liveGashaponBuffLog 超能魔方 buff 记录
type liveGashaponBuffLog struct {
	ID           int64   `gorm:"column:id;primary_key"`
	CreateTime   int64   `gorm:"column:create_time"`
	ModifiedTime int64   `gorm:"column:modified_time"`
	RoomID       int64   `gorm:"column:room_id"`
	PoolID       int64   `gorm:"column:pool_id"`
	GiftID       int64   `gorm:"column:gift_id"`
	Multiplier   float64 `gorm:"column:multiplier"`
}

// TableName table name
func (liveGashaponBuffLog) TableName() string {
	return "live_gashapon_buff_log"
}

// BeforeCreate hook
func (l *liveGashaponBuffLog) BeforeCreate() error {
	now := util.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now
	return nil
}

// BeforeUpdate hook
func (l liveGashaponBuffLog) BeforeUpdate(scope *gorm.Scope) error {
	now := util.TimeNow().Unix()
	return scope.SetColumn("modified_time", now)
}

// newLiveGashaponBuffLog new liveGashaponBuffLog
// poolInBuff 中 CurrentBuff 需要不为 nil
func newLiveGashaponBuffLog(roomID int64, poolInBuff *PoolGashapon) *liveGashaponBuffLog {
	return &liveGashaponBuffLog{
		RoomID:     roomID,
		PoolID:     poolInBuff.PoolID,
		GiftID:     poolInBuff.CurrentBuff.GiftID,
		Multiplier: poolInBuff.CurrentBuff.Multiplier,
	}
}

// save 保存超能魔方 buff 记录
func (l *liveGashaponBuffLog) save() error {
	return service.LiveDB.Save(&l).Error
}
