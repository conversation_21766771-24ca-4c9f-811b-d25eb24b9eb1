package gift

import (
	"fmt"
	"strings"
	"time"

	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CSVGift csv gift
type CSVGift struct {
	GiftID    int64  `bson:"gift_id" json:"gift_id" csv:"礼物 ID"`
	Type      int    `bson:"type" json:"type" csv:"类型"`
	Exclusive int    `bson:"exclusive,omitempty" json:"exclusive,omitempty" csv:"专属类型,omitempty"`
	Attr      *int   `bson:"attr,omitempty" json:"attr,omitempty" csv:"属性,omitempty"`
	Name      string `bson:"name" json:"name" csv:"名称"`
	NameClean string `bson:"name_clean" json:"name_clean" csv:"特殊名称"`

	Price int64 `bson:"price" json:"price" csv:"价格（钻）"`
	Point int64 `bson:"point,omitempty" json:"point,omitempty" csv:"亲密度"`

	Order int `bson:"order" json:"order" csv:"排序"`

	AddedTimeParam string    `bson:"-" json:"added_time" csv:"上线时间"`
	AddedTime      time.Time `bson:"added_time" json:"-" csv:"-"`

	IconParam uint64 `bson:"-" json:"-" csv:"图标"` // 神话礼物和白给礼物可能会复用
	Icon      string `bson:"icon" json:"icon" csv:"-"`

	IconActive string `bson:"icon_active,omitempty" json:"icon_active,omitempty" csv:"-"`

	LabelIconParam string `bson:"-" json:"-" csv:"角标"` // 支持自定义后缀
	LabelIcon      string `bson:"label_icon,omitempty" json:"label_icon,omitempty" csv:"-"`

	Intro          string `bson:"intro" json:"intro,omitempty" csv:"简介"`
	IntroIconParam string `bson:"-" json:"-" csv:"简介图标"` // 支持自定义后缀
	IntroIcon      string `bson:"intro_icon,omitempty" json:"intro_icon,omitempty" csv:"-"`
	IntroOpenURL   string `bson:"intro_open_url,omitempty" json:"intro_open_url,omitempty" csv:"简介跳转地址"`

	EffectParam    uint64 `bson:"-" json:"-" csv:"特效"` // 白给礼物可能会复用
	Effect         string `bson:"effect,omitempty" json:"effect,omitempty"  csv:"-"`
	WebEffect      string `bson:"web_effect,omitempty" json:"web_effect,omitempty" csv:"-"`
	NotifyDuration int64  `bson:"notify_duration,omitempty" json:"notify_duration,omitempty" csv:"通知时长（毫秒）"`

	Comboable        int    `bson:"comboable" json:"comboable" csv:"是否可连击"`
	ComboEffectParam uint64 `bson:"-" json:"-" csv:"连击特效"` // 白给礼物可能会复用
	ComboEffect      string `bson:"combo_effect,omitempty" json:"combo_effect,omitempty" csv:"-"`
	WebComboEffect   string `bson:"web_combo_effect,omitempty" json:"web_combo_effect,omitempty" csv:"-"`

	UserID int64 `bson:"user_id,omitempty" json:"user_id,omitempty" csv:"用户 ID"`
	RoomID int64 `bson:"room_id,omitempty" json:"room_id,omitempty" csv:"房间 ID"`

	NobleLevel int `bson:"noble_level,omitempty" json:"noble_level,omitempty" csv:"贵族等级限制"`
	MedalLevel int `bson:"medal_level,omitempty" json:"medal_level,omitempty" csv:"勋章等级限制"`
	UserLevel  int `bson:"user_level,omitempty" json:"user_level,omitempty" csv:"用户等级限制"`

	NotifyMessage  string `bson:"notify_message,omitempty" json:"notify_message,omitempty" csv:"飘屏消息内容"`
	NotifyBubbleID uint64 `bson:"notify_bubble_id,omitempty" json:"notify_bubble_id,omitempty" csv:"飘屏幕气泡 ID"`

	AllowedNumsParam string  `bson:"-" json:"-" csv:"赠送数量限制"`
	AllowedNums      []int64 `bson:"allowed_nums,omitempty" json:"allowed_nums,omitempty" csv:"-"`

	LuckyEffectParam uint64 `bson:"-" json:"-" csv:"开箱动画"`
	LuckyEffect      string `bson:"lucky_effect,omitempty" json:"lucky_effect,omitempty" csv:"-"`
	WebLuckyEffect   string `bson:"web_lucky_effect,omitempty" json:"web_lucky_effect,omitempty" csv:"-"`

	BaseGiftID int64 `bson:"base_gift_id,omitempty" json:"base_gift_id,omitempty" csv:"模版礼物 ID"`
	VipType    int   `bson:"vip_type,omitempty" json:"vip_type,omitempty" csv:"贵族类别"`

	ToggleTime int64 `bson:"toggle_time,omitempty" json:"toggle_time,omitempty" csv:"上下架时间"`
}

// BuildMongoGifts 构建完整的礼物信息
func (g *CSVGift) BuildMongoGifts() (err error) {
	g.NameClean = util.CleanString(g.NameClean)
	g.AddedTime, err = time.ParseInLocation(util.TimeFormatYMDHMS, g.AddedTimeParam, time.Local)
	if err != nil {
		return err
	}
	g.Icon = fmt.Sprintf("oss://live/gifts/icons/%d.png", g.IconParam)
	// 随机礼物抽出的奖品无动态图标
	if g.Type != TypeDrawReceive {
		g.IconActive = fmt.Sprintf("oss://live/gifts/icons/active/%d.webp", g.IconParam)
	}
	if g.LabelIconParam != "" {
		if strings.Contains(g.LabelIconParam, ".") {
			// 自定义后缀
			g.LabelIcon = fmt.Sprintf("oss://live/gifts/labels/%s", g.LabelIconParam)
		} else {
			g.LabelIcon = fmt.Sprintf("oss://live/gifts/labels/%s.png", g.LabelIconParam)
		}
	}
	if g.IntroIconParam != "" {
		if strings.Contains(g.IntroIconParam, ".") {
			// 自定义后缀
			g.IntroIcon = fmt.Sprintf("oss://live/gifts/events/%s", g.IntroIconParam)
		} else {
			g.IntroIcon = fmt.Sprintf("oss://live/gifts/events/%s.png", g.IntroIconParam)
		}
	}
	if g.EffectParam != 0 {
		g.Effect = fmt.Sprintf("oss://live/gifts/effects/v2/%d.mp4;oss://live/gifts/effects/v2/%d.png",
			g.EffectParam, g.EffectParam)
		g.WebEffect = fmt.Sprintf("oss://live/gifts/effects/v2/%d-web.mp4;oss://live/gifts/effects/v2/%d-web.webm;oss://live/gifts/effects/v2/%d-web.png",
			g.EffectParam, g.EffectParam, g.EffectParam)
	}
	if g.ComboEffectParam != 0 {
		g.ComboEffect = fmt.Sprintf("oss://live/gifts/comboeffects/%d.svga", g.ComboEffectParam)
		g.WebComboEffect = fmt.Sprintf("oss://live/gifts/comboeffects/%d-web.svga", g.ComboEffectParam)
	}
	if g.AllowedNumsParam != "" {
		allowedNums, err := goutil.SplitToInt64Array(g.AllowedNumsParam, "/")
		if err != nil {
			return err
		}
		g.AllowedNums = allowedNums
	}
	if g.LuckyEffectParam != 0 {
		g.LuckyEffect = fmt.Sprintf("oss://live/gifts/luckyeffects/v2/%d.mp4;oss://live/gifts/luckyeffects/v2/%d.png",
			g.LuckyEffectParam, g.LuckyEffectParam)
		g.WebLuckyEffect = fmt.Sprintf("oss://live/gifts/luckyeffects/v2/%d-web.mp4;oss://live/gifts/luckyeffects/v2/%d-web.webm;oss://live/gifts/luckyeffects/v2/%d-web.png",
			g.LuckyEffectParam, g.LuckyEffectParam, g.LuckyEffectParam)
	}
	return
}

// TrimSpaces 剔除礼物所有字符串参数的前后空格
func (g *CSVGift) TrimSpaces() {
	g.Name = strings.TrimSpace(g.Name)
	g.NameClean = strings.TrimSpace(g.NameClean)

	g.AddedTimeParam = strings.TrimSpace(g.AddedTimeParam)

	g.LabelIconParam = strings.TrimSpace(g.LabelIconParam)

	g.Intro = strings.TrimSpace(g.Intro)
	g.IntroIconParam = strings.TrimSpace(g.IntroIconParam)
	g.IntroOpenURL = strings.TrimSpace(g.IntroOpenURL)

	g.NotifyMessage = strings.TrimSpace(g.NotifyMessage)

	g.AllowedNumsParam = strings.TrimSpace(g.AllowedNumsParam)
}
