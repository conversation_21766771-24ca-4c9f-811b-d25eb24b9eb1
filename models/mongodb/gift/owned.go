package gift

import (
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// OwnedByVIP 被 vip 拥有
// NOTICE: 需要先于是否是定制礼物的判断
func (g *Gift) OwnedByVIP(uv *vip.UserVip) bool {
	if (g.Type != TypeNoble && g.Type != TypeCustom) ||
		g.NobleLevel <= 0 {
		// 不是贵族礼物或者定制礼物
		// 贵族等级无
		return true
	}
	if uv == nil || !uv.IsActive() {
		return false
	}
	switch uv.Type {
	case vip.TypeLiveHighness:
		// 上神总是返回 true
		return true
	case vip.TypeLiveNoble, vip.TypeLiveTrialNoble:
		// 礼物的 vip_type 不填也认为是普通贵族等级
		return (g.VipType == 0 || g.VipType == vip.TypeLiveNoble || g.VipType == vip.TypeLiveTrialNoble) && g.NobleLevel <= uv.Level
	}
	return false
}

// OwnedByFans 被粉丝（包括超粉）拥有
// medal 为当前用户送礼房间的勋章
func (g *Gift) OwnedByFans(medal *livemedal.LiveMedal) bool {
	if g.Type != TypeMedal && g.Type != TypeSuperFan {
		return true
	}
	if medal == nil {
		return false
	}
	switch g.Type {
	case TypeMedal:
		return g.MedalLevel <= medal.Level
	case TypeSuperFan:
		return livemedal.IsSuperFanActive(medal.SuperFan)
	}
	return true
}

// OwnedByUser 用户是否拥有
// 仅用于单个礼物的权限场景，不要在循环里调用
func (g *Gift) OwnedByUser(userID int64, userLevel int) bool {
	if g.Exclusive == GiftExclusiveUser {
		return g.ownedCustomByUser(userID, userLevel)
	}
	switch g.Type {
	case TypeNormal:
		if g.UserID != 0 {
			return g.UserID == userID
		}
		return true
	case TypeDrawSend:
		if g.UserID != 0 {
			return g.UserID == userID
		}
		if g.UserLevel != 0 {
			// 用户等级限定礼物
			return userLevel >= g.UserLevel
		}
		return true
	case TypeCustom:
		return g.ownedCustomByUser(userID, userLevel)
	default:
		return true
	}
}

// ownedCustomByUser 用户是否拥有定制礼物
// 仅用于单个礼物的权限场景，不要在循环里调用
func (g *Gift) ownedCustomByUser(userID int64, userLevel int) bool {
	if g.UserID != 0 {
		return g.UserID == userID
	}
	if g.UserLevel != 0 {
		// 用户等级限定礼物
		return userLevel >= g.UserLevel
	}
	owned, err := livecustom.IsUserCustomGiftExists(userID, g.GiftID)
	if err != nil {
		logger.WithField("gift_id", g.GiftID).Error(err)
		// PASS
	}
	return owned
}

// OwnedByRoom 房间是否拥有
func (g *Gift) OwnedByRoom(roomID int64) bool {
	if g.Exclusive == GiftExclusiveRoom {
		return g.ownedCustomByRoom(roomID)
	}
	switch g.Type {
	case TypeRoomCustom:
		return g.ownedCustomByRoom(roomID)
	case TypeDrawSend:
		if g.IsDisableDrawRoomCustomAfterSSR() {
			return g.ownedCustomByRoom(roomID)
		}
	}
	return g.RoomID == 0 || g.RoomID == roomID
}

// ownedCustomByRoom 房间是否拥有直播间定制礼物
// 仅用于单个礼物的权限场景，不要在循环里调用
func (g *Gift) ownedCustomByRoom(roomID int64) bool {
	if g.RoomID != 0 {
		return g.RoomID == roomID
	}
	exists, err := livecustom.IsExistsRoomCustomGift(roomID, g.GiftID)
	if err != nil {
		logger.WithField("gift_id", g.GiftID).Error(err)
		// PASS
		return false
	}
	return exists
}

// IsExclusiveTabGift 判断礼物是否为属于专属栏礼物
// NOTICE: 该方法仅判断礼物是否属于【专属】tab 栏。若判断房间或用户是否拥有礼物展示的赠送资格，需要使用 OwnedByRoom 或 OwnedByUser 方法
func (g *Gift) IsExclusiveTabGift() bool {
	// 多个直播间共同拥有的礼物需要根据 Exclusive 字段判断
	switch g.Exclusive {
	case GiftExclusiveUser, GiftExclusiveRoom:
		return true
	}

	switch g.Type {
	case TypeRoomCustom:
		return true
	case TypeCustom:
		// 非贵族定制礼物属于专属礼物
		return g.NobleLevel == 0
	case TypeDrawSend:
		return g.RoomID != 0 || g.UserID != 0 || g.UserLevel > 0 || g.IsDisableDrawRoomCustomAfterSSR()
	}

	return false
}

// OwnedByMedal 是否拥有超粉随机礼物
// NOTICE: 目前仅支持随机礼物
func (g *Gift) OwnedByMedal(medal *livemedal.LiveMedal) bool {
	if medal == nil {
		return false
	}
	switch g.Type {
	case TypeDrawSend:
		return g.IsDrawSendSuperFan() && livemedal.IsSuperFanActive(medal.SuperFan)
	default:
		return false
	}
}
