package gift

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestNotifyTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(EffectOptions{}, "words", "avatars")
	kc.Check(EffectOptionWords{}, "text", "image_url")
	kc.Check(EffectOptionAvatars{}, "creator_iconurl", "user_iconurl")
}

func TestNotifyGiftSetEffectWords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ng := NotifyGift{}
	ng.SetEffectWords(&EffectOptionWords{Text: "123", ImageURL: "456"})
	require.NotNil(ng.EffectOptions)
	assert.Equal(&EffectOptionWords{Text: "123", ImageURL: "456"},
		ng.EffectOptions.Words)

	ng.SetEffectWords(&EffectOptionWords{Text: "456", ImageURL: "123"})
	assert.Equal(&EffectOptionWords{Text: "456", ImageURL: "123"},
		ng.EffectOptions.Words)
}

func TestNotifyGiftSetEffectCreatorIconURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ng := NotifyGift{}
	ng.SetEffectCreatorIconURL("123")
	require.NotNil(ng.EffectOptions)
	require.NotNil(ng.EffectOptions.Avatars)
	assert.Equal("123", ng.EffectOptions.Avatars.CreatorIconURL)

	ng.SetEffectCreatorIconURL("456")
	assert.Equal("456", ng.EffectOptions.Avatars.CreatorIconURL)
}

func TestNotifyGiftSetEffectUserIconURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ng := NotifyGift{}
	ng.SetEffectUserIconURL("123")
	require.NotNil(ng.EffectOptions)
	require.NotNil(ng.EffectOptions.Avatars)
	assert.Equal("123", ng.EffectOptions.Avatars.UserIconURL)

	ng.SetEffectUserIconURL("456")
	assert.Equal("456", ng.EffectOptions.Avatars.UserIconURL)
}
