package gift

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestGiftTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Gift{}, "_id",
		"type", "exclusive",
		"gift_id", "name", "name_clean",
		"icon", "icon_active",
		"setorder",
		"intro", "intro_icon", "intro_open_url",
		"effect", "web_effect", "effect_duration", "notify_duration",
		"comboable", "combo_effect", "web_combo_effect", "combos",
		"label_icon",
		"price", "point", "medal_point_buff",
		"base_gift_id",
		"attr",
		"order", "added_time",
		"toggle_time",
		"user_id",
		"room_id",
		"vip_type", "noble_level",
		"medal_level",
		"user_level",
		"notify_message", "notify_bubble_id",
		"allowed_nums",
		"lucky_effect", "web_lucky_effect",
	)
	kc.Check(SetOrder{}, "effective_time", "order")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Gift{}, "type",
		"gift_id", "name",
		"intro", "intro_icon_url", "intro_open_url",
		"icon_url", "icon_active_url",
		"setorder",
		"effect_url", "web_effect_url", "effect_duration", "notify_duration",
		"comboable", "combo_effect_url", "web_combo_effect_url",
		"label_icon_url",
		"price", "point", "medal_point_buff",
		"order", "toggle_time",
		"vip_type", "noble_level",
		"medal_level",
		"user_level",
		"allowed_nums",
		"is_lucky",
		"is_super_fan",
		"diy_entry",
	)
	kc.Check(SetOrder{}, "effective_time", "order")
	kc.Check(DiyEntry{}, "image_url")

	kc.Check(NotifyGift{}, "gift_id", "name", "icon_url", "price", "num",
		"effect_url", "new_effect_url", "web_effect_url", "effect_duration", "effect_options",
		"notify_duration")

	kc.Check(NotifyPayload{}, "type", "notify_type", "event",
		"room_id", "user", "bubble", "gift", "notify_bubble", "message")

	kc.Check(ShortcutGift{}, "gift_id", "name", "icon_url", "price", "comboable")
}

func TestGift_ComboLevel(t *testing.T) {
	assert := assert.New(t)

	g := Gift{
		Price: 3,
		Combos: []Combo{
			{
				TargetPrice:    10,
				ComboEffect:    "http://test1.png",
				WebComboEffect: "http://testweb1.png",
			},
			{
				TargetPrice:    100,
				ComboEffect:    "http://test2.png",
				WebComboEffect: "http://testweb2.png",
			},
			{
				TargetPrice:    1000,
				RepeatAddPrice: 800,
				ComboEffect:    "http://test3.png",
				WebComboEffect: "http://testweb3.png",
			},
		},
	}

	res := g.ComboLevel(1)
	assert.Equal(ComboLevel{
		TargetPrice:    10,
		TargetNum:      4,
		AchievedPrice:  0,
		AchievedNum:    0,
		ComboEffect:    g.Combos[0].ComboEffect,
		WebComboEffect: g.Combos[0].WebComboEffect,
	}, *res)

	res = g.ComboLevel(10)
	assert.Equal(ComboLevel{
		TargetPrice:    100,
		TargetNum:      34,
		AchievedPrice:  10,
		AchievedNum:    4,
		ComboEffect:    g.Combos[0].ComboEffect,
		WebComboEffect: g.Combos[0].WebComboEffect,
	}, *res)

	res = g.ComboLevel(15)
	assert.Equal(ComboLevel{
		TargetPrice:    100,
		TargetNum:      34,
		AchievedPrice:  10,
		AchievedNum:    4,
		ComboEffect:    g.Combos[0].ComboEffect,
		WebComboEffect: g.Combos[0].WebComboEffect,
	}, *res)

	res = g.ComboLevel(100)
	assert.Equal(ComboLevel{
		TargetPrice:    1000,
		TargetNum:      334,
		AchievedPrice:  100,
		AchievedNum:    34,
		ComboEffect:    g.Combos[1].ComboEffect,
		WebComboEffect: g.Combos[1].WebComboEffect,
	}, *res)

	res = g.ComboLevel(800)
	assert.Equal(ComboLevel{
		TargetPrice:    1000,
		TargetNum:      334,
		AchievedPrice:  100,
		AchievedNum:    34,
		ComboEffect:    g.Combos[1].ComboEffect,
		WebComboEffect: g.Combos[1].WebComboEffect,
	}, *res)

	res = g.ComboLevel(1000)
	assert.Equal(ComboLevel{
		TargetPrice:              1800,
		TargetNum:                600,
		AchievedPrice:            1000,
		AchievedNum:              334,
		ComboEffect:              g.Combos[2].ComboEffect,
		WebComboEffect:           g.Combos[2].WebComboEffect,
		IsReachMaxFixedThreshold: true,
	}, *res)

	res = g.ComboLevel(1800)
	assert.Equal(ComboLevel{
		TargetPrice:              2600,
		TargetNum:                867,
		AchievedPrice:            1800,
		AchievedNum:              600,
		ComboEffect:              g.Combos[2].ComboEffect,
		WebComboEffect:           g.Combos[2].WebComboEffect,
		IsReachMaxFixedThreshold: true,
	}, *res)

	res = g.ComboLevel(2700)
	assert.Equal(ComboLevel{
		TargetPrice:              3400,
		TargetNum:                1134,
		AchievedPrice:            2600,
		AchievedNum:              867,
		ComboEffect:              g.Combos[2].ComboEffect,
		WebComboEffect:           g.Combos[2].WebComboEffect,
		IsReachMaxFixedThreshold: true,
	}, *res)
}

func TestFindShowGiftByGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gift1 := Gift{
		GiftID: 8,
		Name:   "鲸鱼",
		Icon:   "https://static-test.missevan.com/gifts/icons/008.png",
	}
	gift, err := FindShowingGiftByGiftID(8)
	require.NoError(err)
	require.NotNil(gift)
	assert.Equal(gift1.GiftID, gift.GiftID)
	assert.Equal(gift1.Name, gift.Name)
	assert.Equal(gift1.Icon, gift.Icon)

	gift, err = FindShowingGiftByGiftID(-1)
	assert.NoError(err)
	assert.Nil(gift)
}

func TestGiftConstructIconURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gift := Gift{
		Icon:      "icon.png",
		Effect:    "effect.svga",
		WebEffect: "effect.webm",
		LabelIcon: "label.png",
		Combos: []Combo{
			{
				TargetPrice: 1,
				ComboEffect: "effect1.webm",
			},
			{
				TargetPrice: 3,
				ComboEffect: "effect3.webm",
			},
			{
				TargetPrice: 2,
				ComboEffect: "effect2.webm",
			},
		},
	}
	expected := gift
	require.NotPanics(func() { gift.ConstructIconURL() })
	expected.Icon = "https://static-test.missevan.com/icon.png"
	expected.Effect = "https://static-test.missevan.com/effect.svga"
	expected.WebEffect = "https://static-test.missevan.com/effect.webm"
	expected.LabelIcon = "https://static-test.missevan.com/label.png"
	assert.Equal(expected, gift)
	assert.NotPanics(func() { gift.ConstructIconURL() })
	assert.Equal(expected, gift)
	assert.Len(gift.Combos, 3)
	assert.Equal(gift.Combos[0].ComboEffect, "https://static-test.missevan.com/effect1.webm")
	assert.Equal(gift.Combos[2].ComboEffect, "https://static-test.missevan.com/effect3.webm")
}

func TestGift_MedalPoint(t *testing.T) {
	assert := assert.New(t)

	g := Gift{
		Price: 1,
		Point: 1,
	}
	assert.Equal(g.Price+g.Point, g.MedalPoint(1))
	g.Attr.Set(AttrDisableMedalPoint)
	assert.Equal(g.Price, g.MedalPoint(1))
	g.MedalPointBuff = 5.5
	assert.EqualValues(5, g.MedalPoint(1))
	g.MedalPointBuff = 5.5
	assert.EqualValues(11, g.MedalPoint(2))
	g.Type = TypeFree
	assert.Equal(g.Price, g.MedalPoint(1))
	g.Type = TypeDrawSend
	assert.Equal(g.Price, g.MedalPoint(1))
}

func TestFindGiftMapByGiftIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5s.Flush()
	gifts, err := FindAllShowingGifts()
	require.NoError(err)
	giftIDs := make([]int64, len(gifts))
	for i := range gifts {
		giftIDs[i] = gifts[i].GiftID
	}
	giftIDs[len(giftIDs)-1] = 987654
	m, err := FindGiftMapByGiftIDs(giftIDs)
	require.NoError(err)
	for i := 0; i < len(giftIDs)-1; i++ {
		_, ok := m[giftIDs[i]]
		assert.True(ok)
	}
	_, ok := m[giftIDs[len(giftIDs)-1]]
	assert.False(ok)
}

func TestFindShowingMultiComboGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyOnlineGifts0.Format()
	gifts := []Gift{
		{GiftID: 1, Comboable: ComboableTypeSingle},
		{GiftID: 2, Comboable: ComboableTypeMulti},
		{GiftID: 3, Comboable: ComboableTypeMulti},
	}
	service.Cache5s.Set(key, gifts, 0)
	defer func() {
		service.Cache5s.Flush()
	}()
	gifts, err := FindShowingMultiComboGifts()
	require.NoError(err)
	assert.Equal(2, len(gifts))
}

func TestFindDiyEntry(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gifts := []Gift{
		{GiftID: 998},
		{GiftID: 999},
	}
	testEntry := "oss://testentry.png"
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := diygifts.Collection().DeleteOne(ctx, bson.M{"gift_id": gifts[0].GiftID})
	require.NoError(err)
	err = diygifts.Collection().FindOneAndUpdate(ctx,
		bson.M{"gift_id": gifts[1].GiftID},
		bson.M{"$set": bson.M{"entry_image": testEntry, "status": 1}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	).Err()
	require.NoError(err)
	findDiyEntry(gifts)
	assert.Nil(gifts[0].DiyEntry)
	require.NotNil(gifts[1].DiyEntry)
	assert.Equal(storage.ParseSchemeURL(testEntry), gifts[1].DiyEntry.ImageURL)
}

func TestCopyGiftSlice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	org := []Gift{{
		Name: "test",
	}}
	src := copyGiftSlice(org)
	require.Equal(len(org), len(src))
	org[0].Name = "123"
	assert.Equal("test", src[0].Name)
}

func TestToTypeMap(t *testing.T) {
	assert := assert.New(t)
	gifts := []Gift{
		{Type: 1},
		{Type: 2},
		{Type: 3},
		{Type: 4},
		{Type: 5},
		{Type: 6, NobleLevel: 4},
		{Type: 6, NobleLevel: 1},
	}
	m := toTypeMap(gifts)
	for i := 1; i < 6; i++ {
		assert.Len(m[i], 1)
	}
	assert.Len(m[6], 2)
}

func TestNotifyPayloadSetGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyBubbles.Format()
	service.Cache10s.Set(key, []bubble.Bubble{
		{BubbleID: 100},
		{BubbleID: 1},
	}, 0)
	nb := NotifyBuilder{
		Gift: &Gift{},
	}
	nb.findBubble()
	require.NotNil(nb.notifyBubble)
	assert.EqualValues(1, nb.notifyBubble.BubbleID)

	nb.Gift.NotifyBubbleID = 100
	nb.findBubble()
	require.NotNil(nb.notifyBubble)
	assert.EqualValues(100, nb.notifyBubble.BubbleID)
}

func TestNotifyBuilderFormatMessage(t *testing.T) {
	assert := assert.New(t)

	np := NotifyBuilder{
		RoomID:          123,
		CreatorUsername: "><",
		User:            &liveuser.Simple{Username: "<>"},
		Gift:            &Gift{Name: ">>"},
		GiftNum:         100,
		LuckyGift:       &Gift{Name: "<<"},
		LuckyGiftNum:    9,
	}

	assert.Equal(`<font color="#FFFFFF"><b>&lt;&gt;</b></font> `+
		`<font color="#FFFFFF">给</font> `+
		`<font color="#FFFFFF"><b>&gt;&lt;</b></font> `+
		`<font color="#FFFFFF">送出</font> `+
		`<font color="#FFFFFF"><b>100 个&gt;&gt;</b></font>`+
		`<font color="#FFFFFF">，快来围观吧~</font>`,
		np.formatMessage())

	np = NotifyBuilder{
		RoomID:                 123,
		CreatorUsername:        "主播",
		User:                   &liveuser.Simple{Username: "<>"},
		Gift:                   &Gift{Name: "一起送", Comboable: ComboableTypeMulti},
		GiftNum:                100,
		MultiComboTop1Username: "top1",
	}

	assert.Equal(`<font color="#FFFFFF">大家在</font> `+
		`<font color="#FFFFFF"><b>主播</b></font> `+
		`<font color="#FFFFFF">的直播间送出</font> `+
		`<font color="#FFFFFF"><b>一起送 × 100</b></font>`+
		`<font color="#FFFFFF">，</font>`+
		`<font color="#FFFFFF"><b>top1</b></font> `+
		`<font color="#FFFFFF">成为最强助攻王！心意满分！快来围观~</font>`,
		np.formatMessage())
}

func TestNotifyBuilderBuild(t *testing.T) {
	assert := assert.New(t)

	nb := &NotifyBuilder{
		RoomID:          123,
		CreatorUsername: "><",
		User:            &liveuser.Simple{Username: "<>"},
		Gift:            &Gift{Name: ">>"},
		GiftNum:         100,
		LuckyGift:       &Gift{Name: "<<"},
		LuckyGiftNum:    9,
	}

	np := nb.Build()
	assert.Equal(liveim.TypeNotify, np.Type)
	assert.Equal(liveim.TypeGift, np.NotifyType)
	assert.Equal(liveim.EventSend, np.Event)
	assert.EqualValues(123, np.RoomID)
	assert.NotNil(np.User)
	assert.NotNil(np.Gift)
	assert.NotEmpty(np.Message)

	nb.NotifyGift = &NotifyGift{Num: 9}
	np = nb.Build()
	assert.Equal(np.Gift, nb.NotifyGift)
}

func TestNewNotifyGift(t *testing.T) {
	assert := assert.New(t)
	g := &Gift{
		Effect: "1;2;3;4",
	}
	r := NewNotifyGift(g, 10)
	assert.Equal(g.Effect, r.EffectURL)
	assert.Equal(10, r.Num)
}

func TestNotifyMessageTemplate(t *testing.T) {
	assert := assert.New(t)

	var g Gift
	assert.Equal(DefaultComboNotifyMessage, g.NotifyMessageTemplate())
	g.Comboable = ComboableTypeMulti
	assert.Equal(DefaultRoomMultiComboNotifyMessage, g.NotifyMessageTemplate())
	g.Type = TypeDrawReceive
	assert.Empty(g.NotifyMessageTemplate())
	g.Type = TypeNormal
	g.NotifyMessage = "123"
	assert.Equal(g.NotifyMessage, g.NotifyMessageTemplate())
}

func TestNewNotifyGiftLucky(t *testing.T) {
	assert := assert.New(t)
	gs := &Gift{
		Effect: "123",
	}
	gr := &Gift{
		LuckyEffect: "456",
	}
	ng := NewNotifyGiftLucky(gs, gr, 12)
	assert.Equal(gr.LuckyEffect, ng.EffectURL)
}

func TestGroupGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testPrizeUserGiftID int64 = 204
		testPrizeRoomGiftID int64 = 703
		testUserID          int64 = 123
		testRoomID          int64 = 789
	)
	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND custom_id = ?",
		livecustom.TypeUserCustomGift, testPrizeUserGiftID).Error)
	require.NoError(livecustom.RemoveRoomCustomGift(testRoomID, testPrizeRoomGiftID))

	var findGiftIDs = func(gs []Gift) []int64 {
		ids := make([]int64, 0, len(gs))
		for _, g := range gs {
			ids = append(ids, g.GiftID)
		}
		return ids
	}

	key := keys.KeyOnlineGifts0.Format()
	gifts := []Gift{
		{Type: TypeNormal, GiftID: 1},
		{Type: TypeNormal, GiftID: 10001, UserID: testUserID},
		{Type: TypeNormal, GiftID: 10002, RoomID: testRoomID},
		{Type: TypeCustom, UserID: testUserID, GiftID: 201},
		{Type: TypeCustom, UserID: testUserID, NobleLevel: 7, GiftID: 202},
		{Type: TypeCustom, UserID: 456, GiftID: 203},
		{Type: TypeCustom, UserID: 0, GiftID: testPrizeUserGiftID},
		{Type: TypeCustom, UserID: 0, GiftID: 205},
		{Type: TypeFree, GiftID: 301},
		{Type: TypeRebate, GiftID: 401},
		{Type: TypeMedal, GiftID: 501},
		{Type: TypeNoble, NobleLevel: 1, GiftID: 601},
		{Type: TypeRoomCustom, RoomID: 456, GiftID: 701},
		{Type: TypeRoomCustom, RoomID: testRoomID, GiftID: 702},
		{Type: TypeRoomCustom, RoomID: 0, GiftID: testPrizeRoomGiftID},
		{Type: TypeRoomCustom, RoomID: 0, GiftID: 704},
		{Type: TypeDrawSend, GiftID: 80001},
		{Type: TypeDrawSend, Attr: 1 << (AttrDisableDrawRoomCustomAfterSSR - 1), RoomID: testRoomID, GiftID: 80002},
		{Type: TypeDrawReceive, GiftID: 90001},
		{Type: TypeSuperFan, GiftID: 100001},
		{Type: TypeDrawSend, Attr: 1 << (AttrDrawSendSuperFan - 1), GiftID: 10101010101},
		{Type: TypeDrawSend, GiftID: 80003, Exclusive: GiftExclusiveRoom},
		{Type: TypeMedal, GiftID: 502},
		{Type: TypeSuperFan, GiftID: 100002},
		{Type: TypeNoble, NobleLevel: 1, GiftID: 602},
		{Type: TypeMedal, GiftID: 503, UserID: testUserID},
		{Type: TypeSuperFan, GiftID: 100003, UserID: testUserID},
		{Type: TypeNoble, NobleLevel: 1, GiftID: 603, UserID: testUserID},
	}
	service.Cache5s.Set(key, gifts, 0)
	defer service.Cache5s.Flush()

	shows, extra := GroupGifts(gifts, GroupGiftsOptions{
		User:   &GroupGiftsOptionsUser{}, // 未登录用户
		RoomID: nil,
	})
	require.Len(shows, 4)

	// 检查所有展示的礼物是否正确设置了标记
	for _, tab := range shows {
		for _, g := range tab {
			// 检查是否正确设置了 IsLucky
			if g.Type == TypeDrawSend {
				assert.True(g.IsLucky)
			} else {
				assert.False(g.IsLucky)
			}

			// 检查是否正确设置了 IsSuperFan
			if g.Type == TypeSuperFan || g.Attr.IsSet(AttrDrawSendSuperFan) {
				assert.True(g.IsSuperFan)
			} else {
				assert.False(g.IsSuperFan)
			}
		}
	}

	// 普通礼物
	require.NotEmpty(shows[0])
	assert.ElementsMatch(findGiftIDs(shows[0]), []int64{1, 10002, 80001})
	// 贵族礼物
	require.NotEmpty(shows[1])
	assert.ElementsMatch(findGiftIDs(shows[1]), []int64{601, 602})
	// 专属礼物
	require.NotEmpty(shows[2])
	assert.ElementsMatch(findGiftIDs(shows[2]), []int64{701, 702, 703, 704, 80002, 80003})
	// 粉丝礼物
	require.NotEmpty(shows[3])
	require.ElementsMatch(findGiftIDs(shows[3]), []int64{501, 502, 100001, 100002, 10101010101})
	assert.Equal(TypeMedal, shows[3][0].Type)
	assert.Equal(TypeMedal, shows[3][1].Type)
	assert.Equal(TypeSuperFan, shows[3][2].Type)
	assert.Equal(TypeSuperFan, shows[3][3].Type)
	// 不显示但需要返回的礼物
	assert.ElementsMatch(findGiftIDs(extra), []int64{201, 202, 203, 204, 205, 301, 401})

	// 测试配置了用户 ID 的礼物
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			User: &GroupGiftsOptionsUser{
				UserID: testUserID,
			},
			RoomID: nil,
		})
	require.Len(shows, 4)
	// 普通礼物
	assert.ElementsMatch(findGiftIDs(shows[0]), []int64{1, 10001, 10002, 80001})
	// 贵族礼物
	assert.ElementsMatch(findGiftIDs(shows[1]), []int64{202, 601, 602, 603})
	// 专属礼物
	assert.ElementsMatch(findGiftIDs(shows[2]), []int64{201, 701, 702, 703, 704, 80002, 80003})
	// 粉丝礼物
	assert.ElementsMatch(findGiftIDs(shows[3]), []int64{501, 502, 503, 100001, 100002, 100003, 10101010101})

	// 测试发放用户礼物过期
	require.NoError(livecustom.AddUserCustomGift(testUserID, testPrizeUserGiftID, -time.Hour, false))
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			User: &GroupGiftsOptionsUser{
				UserID: testUserID,
			},
			RoomID: &testRoomID,
		})
	require.Len(shows, 4)
	// 专属礼物
	require.NotEmpty(shows[2])
	assert.ElementsMatch(findGiftIDs(shows[2]), []int64{80002, 201, 702})

	// 测试有发放用户礼物
	require.NoError(livecustom.AddUserCustomGift(testUserID, testPrizeUserGiftID, time.Hour, false))
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			User: &GroupGiftsOptionsUser{
				UserID: testUserID,
			},
			RoomID: &testRoomID,
		})
	require.Len(shows, 4)
	// 专属礼物
	require.NotEmpty(shows[2])
	assert.ElementsMatch(findGiftIDs(shows[2]), []int64{80002, 201, 204, 702})

	// 测试直播间有直播间定制礼物
	now := goutil.TimeNow()
	require.NoError(livecustom.AddRoomCustomGift(testRoomID, testPrizeRoomGiftID, now, now.Add(time.Hour), livecustom.SourceDefault))
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			User: &GroupGiftsOptionsUser{
				UserID: testUserID,
			},
			RoomID: &testRoomID,
		})
	require.Len(shows, 4)
	// 专属礼物
	assert.ElementsMatch(findGiftIDs(shows[2]), []int64{80002, 201, 204, 702, 703})

	// 测试直播间有失去抽奖资格的礼物
	require.NoError(livecustom.RemoveRoomCustomGift(testRoomID, 80002))
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			User: &GroupGiftsOptionsUser{
				UserID: testUserID,
			},
			RoomID: &testRoomID,
		})
	require.Len(shows, 4)
	// 专属礼物
	assert.ElementsMatch(findGiftIDs(shows[2]), []int64{80002, 201, 204, 702, 703})

	// 测试直播间无直播间定制礼物
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			User: &GroupGiftsOptionsUser{
				UserID: 6789,
			},
			RoomID: util.NewInt64(6789),
		})
	require.Len(shows, 4)
	// 普通礼物
	assert.ElementsMatch(findGiftIDs(shows[0]), []int64{1, 80001})
	// 定制礼物
	assert.Empty(shows[2])

	// 测试要隐藏的随机礼物
	shows, _ = GroupGifts(gifts,
		GroupGiftsOptions{
			RoomID:         util.NewInt64(6789),
			HideLuckyGifts: true,
		})
	require.Len(shows, 4)
	// 普通礼物
	assert.ElementsMatch(findGiftIDs(shows[0]), []int64{1, 10001})
}

func TestGift_checkOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time { return now })
	defer cancel()
	giftID := int64(999)
	lockKey := keys.LockGiftSetOrder1.Format(giftID)
	order := int(giftID)
	effectiveTime := now.Add(time.Second * -10)
	g := &Gift{
		GiftID:    giftID,
		Name:      "测试定时上下架更新 toggle_time",
		NameClean: "测试定时上下架更新 toggle_time",
		Order:     OrderHide,
		SetOrder: &SetOrder{
			EffectiveTime: effectiveTime,
			Order:         order,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := Collection().FindOneAndUpdate(ctx, bson.M{"gift_id": giftID},
		bson.M{"$set": g}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(err)

	t.Run("礼物定时上架", func(t *testing.T) {
		require.NoError(service.Redis.Del(lockKey).Err())
		g.checkOrder()
		time.Sleep(time.Millisecond * 200) // 等待异步更新完成
		g, err = FindByGiftID(giftID)
		require.NoError(err)
		require.NotNil(g)
		assert.Nil(g.SetOrder)                           // 确认礼物的 SetOrder 被更新
		assert.Equal(order, g.Order)                     // 更新了礼物的 Order
		assert.Equal(effectiveTime.Unix(), g.ToggleTime) // 上架更新礼物的 toggle_time
	})

	t.Run("礼物上架状态定时切换排序", func(t *testing.T) {
		// 更新礼物的 SetOrder 值
		newEffectiveTime := now.Add(time.Second * -5)
		newOrder := 1
		g.SetOrder = &SetOrder{
			EffectiveTime: newEffectiveTime,
			Order:         newOrder,
		}
		require.NoError(service.Redis.Del(lockKey).Err())
		g.checkOrder()
		time.Sleep(time.Millisecond * 200) // 等待异步更新完成
		g, err = FindByGiftID(giftID)
		require.NoError(err)
		require.NotNil(g)
		assert.Nil(g.SetOrder)
		assert.Equal(newOrder, g.Order)
		assert.Equal(effectiveTime.Unix(), g.ToggleTime) // 没有发生上下架切换，toggle_time 没有变化
	})

	t.Run("礼物定时下架", func(t *testing.T) {
		// 更新礼物的 SetOrder 值
		newEffectiveTime := now.Add(time.Second * -1)
		newOrder := OrderHide
		g.SetOrder = &SetOrder{
			EffectiveTime: newEffectiveTime,
			Order:         newOrder,
		}
		require.NoError(service.Redis.Del(lockKey).Err())
		g.checkOrder()
		time.Sleep(time.Millisecond * 200) // 等待异步更新完成
		g, err = FindByGiftID(giftID)
		require.NoError(err)
		require.NotNil(g)
		assert.Nil(g.SetOrder)
		assert.Equal(newOrder, g.Order)
		assert.Equal(newEffectiveTime.Unix(), g.ToggleTime) // 下架更新礼物的 toggle_time
	})
}

func TestGiftAttr(t *testing.T) {
	assert := assert.New(t)

	g := Gift{Attr: 0}
	assert.False(g.AllowPointAddActivity())
	assert.True(g.AllowAddMedalPoint())
	assert.True(g.AllowAddScore())
	assert.False(g.AlwaysNotify())
	assert.False(g.AllowPointAddPK())
	assert.False(g.AllowPointAddRank())
	assert.False(g.AllowPointAddMultiConnect())
	assert.False(g.IsDisableDrawRoomCustomAfterSSR())
	assert.False(g.IsHotCard())

	for i := 1; i < AttrCount; i++ {
		g.Attr.Set(i)
	}
	assert.True(g.AllowPointAddActivity())
	assert.False(g.AllowAddMedalPoint())
	assert.False(g.AllowAddScore())
	assert.True(g.AlwaysNotify())
	assert.True(g.AllowPointAddPK())
	assert.True(g.AllowPointAddMultiConnect())
	assert.True(g.AllowPointAddRank())
	assert.True(g.IsDisableDrawRoomCustomAfterSSR())
	assert.False(g.IsHotCard())

	g.Type = TypeFree
	assert.True(g.IsHotCard())
}

func TestComboFlags(t *testing.T) {
	assert := assert.New(t)

	g := Gift{Price: 1, ComboEffect: "test"}

	flags := g.ComboFlags(ComboEffectMinPrice - 1)
	assert.False(flags.IsSet(ComboFlagEffect), "价格不足")
	flags = g.ComboFlags(ComboEffectMinPrice)
	assert.True(flags.IsSet(ComboFlagEffect), "正常情况")
	g.Effect = "test"
	flags = g.ComboFlags(ComboEffectMinPrice)
	assert.False(flags.IsSet(ComboFlagEffect), "有正常特效")
	g.Effect = ""
	g.ComboEffect = ""
	flags = g.ComboFlags(ComboEffectMinPrice)
	assert.False(flags.IsSet(ComboFlagEffect), "没有有连击特效")

	flags = g.ComboFlags(ComboNotifyMinPrice - 1)
	assert.False(flags.IsSet(ComboFlagNotify), "价格不足")
	flags = g.ComboFlags(ComboNotifyMinPrice)
	assert.True(flags.IsSet(ComboFlagNotify), "正常情况")
}

func TestPKScores(t *testing.T) {
	assert := assert.New(t)

	gifts := []Gift{
		{Point: 1},
		{Price: 1},
		{Point: 1, Attr: 1 << (AttrPointAddPK - 1)},
		{Price: 1, Point: 1, Attr: 1 << (AttrPointAddPK - 1)},
	}
	expected := [][2]int64{{0, 0}, {3, 0}, {0, 3}, {3, 3}}
	for i := range gifts {
		score, freeScore := gifts[i].PKScores(3)
		assert.Equal(expected[i][0], score, i)
		assert.Equal(expected[i][1], freeScore, i)
	}
}

func TestAbleToSend(t *testing.T) {
	assert := assert.New(t)

	cases := map[int]bool{
		TypeHide:        false,
		TypeNormal:      true,
		TypeCustom:      true,
		TypeFree:        false,
		TypeRebate:      false,
		TypeMedal:       true,
		TypeNoble:       true,
		TypeRoomCustom:  true,
		TypeDrawSend:    false,
		TypeDrawReceive: false,
		TypeSuperFan:    true,
		TypeBlackCard:   true,
	}
	for giftType, ableToSend := range cases {
		assert.Equal(ableToSend, AbleToSend(giftType), giftType)
	}
}

func TestFindByGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g, err := FindByGiftID(8)
	require.NoError(err)
	assert.NotNil(g)

	g, err = FindByGiftID(99899)
	require.NoError(err)
	assert.Nil(g)
}

func TestComboRemainDuration(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(5*time.Second, ComboRemainDuration(100))
	assert.Equal(15*time.Second, ComboRemainDuration(1000))
	assert.Equal(15*time.Second, ComboRemainDuration(1001))
}

func TestGift_IsDrawSendSuperFan(t *testing.T) {
	assert := assert.New(t)

	g := &Gift{}
	assert.False(g.IsDrawSendSuperFan())

	g.Attr = 1 << (AttrDrawSendSuperFan - 1)
	assert.True(g.IsDrawSendSuperFan())
}

func TestGift_MultiConnectScores(t *testing.T) {
	assert := assert.New(t)

	testCases := []struct {
		name              string
		gift              Gift
		num               int64
		expectedScore     int64
		expectedFreeScore int64
	}{
		{
			name: "有价值礼物",
			gift: Gift{
				Price: 10,
			},
			num:           2,
			expectedScore: 20,
		},
		{
			name: "免费礼物未设置 attr",
			gift: Gift{
				Point: 10,
			},
			num:               2,
			expectedScore:     0,
			expectedFreeScore: 0,
		},
		{
			name: "免费礼物设置 attr",
			gift: Gift{
				Point: 10,
				Attr:  1 << (AttrPointAddMultiConnect - 1),
			},
			num:               2,
			expectedScore:     0,
			expectedFreeScore: 20,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actualScore, actualFreeScore := tc.gift.MultiConnectScores(tc.num)
			assert.Equal(tc.expectedScore, actualScore)
			assert.Equal(tc.expectedFreeScore, actualFreeScore)
		})
	}
}

func TestGift_IsBlackCardBaseGift(t *testing.T) {
	assert := assert.New(t)

	var g Gift
	g.Attr.Set(AttrDisableMedalPoint)
	assert.False(g.IsBlackCardBaseGift())

	g.Attr.Set(AttrBlackCardBase)
	assert.True(g.IsBlackCardBaseGift())
}
