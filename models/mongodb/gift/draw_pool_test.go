package gift

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

/*
func initDrawGifts() {
	dsg := []Gift{
		{GiftID: 80001, Name: "draw send 1", NameClean: "ds1", Type: TypeDrawSend, AddedTime: time.Unix(0, 0), AllowedNums: []int{1, 10, 100}},
		{GiftID: 90001, Name: "draw receive 1", NameClean: "dr1", Type: TypeDrawReceive, AddedTime: time.Unix(0, 0)},
		{GiftID: 90002, Name: "draw receive 2", NameClean: "dr2", Type: TypeDrawReceive, AddedTime: time.Unix(0, 0)},
		{GiftID: 90003, Name: "draw receive 3", NameClean: "dr3", Type: TypeDrawReceive, AddedTime: time.Unix(0, 0)},
	}
	col := Collection()
	for i := range dsg {
		_, err := col.UpdateOne(context.Background(), bson.M{"gift_id": dsg[i].GiftID},
			bson.M{"$set": dsg[i]}, options.Update().SetUpsert(true))
		if err != nil {
			logger.Error(err)
		}
	}
}
func TestInit(t *testing.T) {
	initDrawGifts()
}
*/

func TestPoolTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(PoolGift{}, "_id", "pool_id", "type",
		"gift_id", "gift_num", "ssr_id", "miss_limit", "rates", "prd_config")
	kc.Check(PoolGashapon{}, "_id", "pool_id", "type",
		"rates", "rank_gift_ids", "grand_gift_id", "grand_notify_bubble_id",
		"grand_notify_message", "grand_notify_message_with_buff",
		"buff_rates", "buff_moment")
	kc.Check(BuffRate{}, "gift_id", "rate", "multiplier", "notify", "bubble_id", "gift_rates")

	kc.Check(PoolLuckyBox{}, "_id", "pool_id", "type", "ssr_id", "miss_limit", "miss_limit_end_time", "miss_limit_start_time", "rates")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(PoolGashapon{}, "pool_id", "rates", "rank_gift_ids", "grand_gift_id",
		"grand_notify_bubble_id", "grand_notify_message", "grand_notify_message_with_buff",
		"buff_moment", "current_buff")
}

func TestFindPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dp, err := FindPool(123, PoolTypeGift)
	require.NoError(err)
	assert.Nil(dp)

	dp = &PoolGift{
		PoolID:    1,
		GiftID:    80001,
		GiftNum:   1,
		Rates:     map[int64]int{90001: 85, 90002: 10, 90003: 5},
		SSRID:     90003,
		MissLimit: 100,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err = CollectionDrawPool().UpdateOne(ctx,
		bson.M{"gift_id": dp.GiftID, "gift_num": dp.GiftNum}, bson.M{"$set": dp},
		options.Update().SetUpsert(true))
	require.NoError(err)

	dp, err = FindPoolGift(dp.GiftID, dp.GiftNum)
	require.NoError(err)
	require.NotNil(dp)
	assert.True(dp.Valid())
}

func TestFindPoolGashapon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试 findPoolGashaponInBuff
	poolID := int64(1)
	roomID := int64(123)
	poolKey := keys.KeyGashaponPoolRoom1.Format(roomID)
	require.NoError(service.Redis.Del(poolKey).Err())
	pg, err := findPoolGashaponInBuff(poolID, roomID)
	require.NoError(err)
	assert.Nil(pg, "没有 buff")
	require.NoError(service.Redis.Set(poolKey, "test", time.Minute).Err())
	_, err = findPoolGashaponInBuff(poolID+1, roomID)
	assert.Error(err, "buff 缓存数据错误")
	pg = &PoolGashapon{
		PoolID: poolID,
	}
	require.NoError(service.Redis.Set(poolKey, tutil.SprintJSON(pg), time.Minute).Err())
	pg, err = findPoolGashaponInBuff(poolID+1, roomID)
	require.NoError(err)
	assert.Nil(pg, "上一个礼物的 buff")
	pg, err = FindPoolGashapon(poolID, roomID)
	require.NoError(err)
	assert.Equal(&PoolGashapon{
		PoolID: poolID,
	}, pg)

	// 测试 FindPoolGashapon
	pg, err = FindPoolGashapon(123, roomID)
	require.NoError(err)
	assert.Nil(pg, "没有奖池")
	require.NoError(service.Redis.Set(poolKey, "A", time.Minute).Err())
	pg, err = FindPoolGashapon(1, roomID)
	require.NoError(err)
	assert.Nil(pg, "奖池类型错误")

	pg = &PoolGashapon{
		PoolID: 3,
		Type:   PoolTypeGashapon,
		Rates: map[int64]int{
			80001: 10,
			80002: 10,
		},
		BuffRates: []BuffRate{
			{GiftID: 80001, Rate: 10, Multiplier: 2.0},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = CollectionDrawPool().UpdateOne(ctx,
		bson.M{"pool_id": pg.PoolID}, bson.M{"$set": pg},
		options.Update().SetUpsert(true))
	require.NoError(err)
	pg, err = FindPoolGashapon(pg.PoolID, 123)
	require.NoError(err)
	require.NotNil(pg)
	assert.Nil(pg.CurrentBuff)
}

func TestPoolGashaponDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	comboPrizeID1 := int64(5)
	comboPrizeID2 := int64(6)
	pg := PoolGashapon{
		Rates: map[int64]int{
			1:             10,
			2:             11,
			3:             35,
			4:             80,
			comboPrizeID1: 1,
			comboPrizeID2: 2,
		},
	}
	_, _, err := pg.Draw(&GashaponDrawParam{
		DrawCount: 0,
		RoomID:    1,
	})
	require.Equal(errors.New("invalid draw count"), err)
	_, _, err = pg.Draw(&GashaponDrawParam{
		DrawCount: 1,
		RoomID:    0,
	})
	require.Equal(errors.New("invalid room_id"), err)

	pg.Type = PoolTypeGashapon
	param := &GashaponDrawParam{
		DrawCount: 10,
		RoomID:    1,
		GuaranteedPool: map[int64]int{
			comboPrizeID1: 2,
			comboPrizeID2: 2,
		},
	}
	loopTimes := 10
	draw := func() {
		for i := 0; i < loopTimes; i++ {
			r, guaranteedGiftID, err := pg.Draw(param)
			require.NoError(err)
			var count int64
			foundcombo := false
			for i, v := range r {
				if i > 0 {
					assert.GreaterOrEqual(r[i].Num, r[i-1].Num)
				}
				if v.GiftID == comboPrizeID1 || v.GiftID == comboPrizeID2 {
					foundcombo = true
				}
				count += int64(v.Num)
			}
			assert.Equal(param.DrawCount, int(count))
			assert.True(foundcombo)
			if guaranteedGiftID != 0 {
				giftIDs := make([]int64, 0, len(param.GuaranteedPool))
				for giftID := range param.GuaranteedPool {
					giftIDs = append(giftIDs, giftID)
				}
				assert.Contains(giftIDs, guaranteedGiftID)
			}
		}
	}
	draw()

	pg = PoolGashapon{
		Type: PoolTypeGashapon,
		Rates: map[int64]int{
			comboPrizeID1: 1,
		},
	}
	param = &GashaponDrawParam{
		DrawCount: 10,
		RoomID:    123,
		GuaranteedPool: map[int64]int{
			comboPrizeID1: 2,
		},
	}
	draw()
}

func TestEnsureDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	giftIDs := []int64{1, 2, 3}
	c := &GashaponDrawParam{
		GuaranteedPool: map[int64]int{
			giftIDs[0]: 10,
			giftIDs[1]: 11,
			giftIDs[2]: 35,
		},
	}
	for i := 0; i < 10; i++ {
		giftID, err := c.EnsureDraw()
		require.NoError(err)
		assert.Contains(giftIDs, giftID)
	}
}

func TestAddPoolGashaponBuff(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gb, err := AddPoolGashaponBuff(998, 789)
	require.NoError(err)
	assert.Nil(gb)

	// 没配置 buff_rates
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := CollectionDrawPool()
	wrongBuffPool := PoolGashapon{
		PoolID:    -1,
		Type:      PoolTypeGashapon,
		Rates:     map[int64]int{},
		BuffRates: make([]BuffRate, 0),
	}
	_, err = col.UpdateOne(ctx, bson.M{"pool_id": wrongBuffPool.PoolID},
		bson.M{"$set": wrongBuffPool},
		options.Update().SetUpsert(true))
	require.NoError(err)
	gb, err = AddPoolGashaponBuff(wrongBuffPool.PoolID, 789)
	require.NoError(err)
	assert.Nil(gb)
	// 倍率错误
	wrongBuffPool.BuffRates = []BuffRate{{GiftID: 90001, Multiplier: -1, Rate: 10}}
	_, err = col.UpdateOne(ctx, bson.M{"pool_id": wrongBuffPool.PoolID},
		bson.M{"$set": wrongBuffPool},
		options.Update())
	require.NoError(err)
	gb, err = AddPoolGashaponBuff(wrongBuffPool.PoolID, 789)
	require.NoError(err)
	assert.Nil(gb)
	// 礼物不存在
	wrongBuffPool.BuffRates = []BuffRate{{GiftID: -1, Multiplier: 2, Rate: 10}}
	_, err = col.UpdateOne(ctx, bson.M{"pool_id": wrongBuffPool.PoolID},
		bson.M{"$set": wrongBuffPool},
		options.Update())
	require.NoError(err)
	gb, err = AddPoolGashaponBuff(wrongBuffPool.PoolID, 789)
	require.NoError(err)
	assert.Nil(gb)

	// 正常情况
	poolID := int64(3)
	var pg PoolGashapon
	require.NoError(col.FindOne(ctx, bson.M{"pool_id": poolID}).Decode(&pg))
	roomID := int64(123)
	gb, err = AddPoolGashaponBuff(poolID, roomID)
	require.NoError(err)
	require.NotNil(gb)
	require.NotZero(gb.GiftID)
	assert.Equal(BuffDuration.Milliseconds(), gb.RemainDuration)
	var br *BuffRate
	for i := range pg.BuffRates {
		if gb.GiftID == pg.BuffRates[i].GiftID {
			br = &pg.BuffRates[i]
			assert.Equal(br.FormattedMultiplier(), gb.Multiplier)
			break
		}
	}
	require.NotNil(br)
	pgInBuff, err := findPoolGashaponInBuff(poolID, roomID)
	require.NoError(err)
	require.NotNil(pgInBuff)
	assert.Equal(pg.Rates[br.GiftID]*int(br.Multiplier)*10, pgInBuff.Rates[br.GiftID])
}

func TestBuildBuffRates(t *testing.T) {
	assert := assert.New(t)

	rates := map[int64]int{
		1: 10,
		2: 15,
		3: 35,
		4: 39,
		5: 1,
	}
	br := &BuffRate{
		GiftID:     1,
		Multiplier: 2.0,
	}
	afterRates := buildBuffRates(br, rates)
	assert.Equal(200, afterRates[1]) // 翻倍
	assert.Equal(8, afterRates[5])

	br.GiftRates = map[int64]int{1: 1, 2: 1, 3: 1, 4: 1, 5: 1}
	afterRates = buildBuffRates(br, rates)
	assert.Equal(1, afterRates[1])
	assert.Equal(1, afterRates[5])
}

func TestCalculateBuffRates(t *testing.T) {
	assert := assert.New(t)

	beforeRates := map[int64]int{
		1: 10, // %10
		2: 15, // %15
		3: 35, // %35
		4: 39, // %39
		5: 1,  // %1
	}

	// 测试将 ID=1 的礼物概率翻倍（2.0 倍）后的概率分布
	afterRates := calculateBuffRates(beforeRates, 1, 2.0)
	assert.Equal(200, afterRates[1]) // 20%
	assert.Equal(133, afterRates[2]) // 13.3%
	assert.Equal(311, afterRates[3]) // 31.1%
	assert.Equal(346, afterRates[4]) // 34.6%
	assert.Equal(8, afterRates[5])   // 0.8%

	// 测试将 ID=3 的礼物概率翻倍（5.0 倍）后的概率分布
	afterRates = calculateBuffRates(beforeRates, 3, 5.0)
	assert.Nil(afterRates)

	// 测试将 ID=4 的礼物概率翻倍（1.5 倍）后的概率分布
	afterRates = calculateBuffRates(beforeRates, 4, 1.5)
	assert.Equal(68, afterRates[1])  // 6.8%
	assert.Equal(102, afterRates[2]) // 10.2%
	assert.Equal(238, afterRates[3]) // 23.8%
	assert.Equal(585, afterRates[4]) // 58.5%
	assert.Equal(6, afterRates[5])   // 0.6%

	// 测试将 ID=5 的礼物概率翻倍（5.0 倍）后的概率分布
	afterRates = calculateBuffRates(beforeRates, 5, 5.0)
	assert.Equal(95, afterRates[1])  // 9.5%
	assert.Equal(143, afterRates[2]) // 14.3%
	assert.Equal(335, afterRates[3]) // 33.5%
	assert.Equal(374, afterRates[4]) // 37.4%
	assert.Equal(50, afterRates[5])  // 5.0%
}

func TestPoolToDistribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids := []int64{1, 2, 3}
	p := map[int64]int{
		ids[0]: 10,
		ids[1]: 11,
		ids[2]: 35,
	}
	d, r, err := PoolToDistribution(p)
	require.NoError(err)
	assert.NotNil(d)
	assert.Len(r, 3)
	assert.Contains(r, ids[0])
	assert.Contains(r, ids[1])
	assert.Contains(r, ids[2])

	p = map[int64]int{1: -1}
	_, _, err = PoolToDistribution(p)
	assert.Error(err)
}

func TestFindGashaponBuff(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b, err := json.Marshal(PoolGashapon{
		CurrentBuff: &BuffRate{
			GiftID:     301,
			Multiplier: 1.2,
		},
	})
	require.NoError(err)
	roomID := int64(10240)
	key := keys.KeyGashaponPoolRoom1.Format(roomID)
	err = service.Redis.Set(key, b, BuffDuration).Err()
	require.NoError(err)
	defer service.Redis.Del(key)

	g, err := FindGashaponBuff(-1)
	require.NoError(err)
	assert.Nil(g)
	g, err = FindGashaponBuff(int64(roomID))
	require.NoError(err)
	assert.Equal(&GashaponBuff{
		GiftID:         301,
		Name:           "猫粮",
		IconURL:        "https://static-test.missevan.com/gifts/icons/007.png",
		RemainDuration: 180000,
		BuffDuration:   180000,
		Multiplier:     "1.2",
	}, g)
}

func TestBuffRateFormatedMultiplier(t *testing.T) {
	assert := assert.New(t)

	br := BuffRate{Multiplier: 2}
	assert.Equal("2.0", br.FormattedMultiplier())
	br.Multiplier = 2.19
	assert.Equal("2.2", br.FormattedMultiplier())
}

func TestPoolGashaponNotifyPayload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pg := PoolGashapon{
		GrandGiftID:         1,
		GrandNotifyBubbleID: 3,
	}
	key := keys.KeyBubbles.Format()
	service.Cache10s.Set(key, []bubble.Bubble{
		{BubbleID: pg.GrandNotifyBubbleID,
			NormalColor:    "#111111",
			HighlightColor: "#222222",
		},
	}, 0)
	u := &liveuser.Simple{
		UID:      12,
		Username: "<test>",
	}
	notify := pg.NotifyPayload(2, []DrawResult{{GiftID: 2, Num: 2}}, u, 123, "><", "")
	assert.Nil(notify)

	drs := []DrawResult{
		{GiftID: 2, Num: 99},
		{GiftID: 1, Num: 1, Gift: &Gift{Name: "gift"}},
	}
	notify = pg.NotifyPayload(100, drs, u, 123, "><", "")
	require.NotNil(notify)
	logger.Debug(notify.Message) // TODO: 文案还可能改动，待超能魔方上线之后优化
	pg.CurrentBuff = &BuffRate{GiftID: 1, Multiplier: 100}
	notify = pg.NotifyPayload(100, drs, u, 123, "><", "")
	require.NotNil(notify)
	logger.Debug(notify.Message)
}

func TestDrawCountStr(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("单抽", DrawCountStr(1))
	assert.Equal(" 10 连", DrawCountStr(10))
	assert.Equal(" 100 连", DrawCountStr(100))
	assert.Equal(" 99 连", DrawCountStr(99))
}
