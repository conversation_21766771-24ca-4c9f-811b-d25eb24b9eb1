package gift

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// PoolLuckyBox 宝盒奖池
type PoolLuckyBox struct {
	OID                primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	PoolID             int64              `bson:"pool_id" json:"pool_id"`
	Type               int                `bson:"type" json:"type"`     // 总是 PoolTypeLuckyBox(3)
	SSRID              int64              `bson:"ssr_id" json:"ssr_id"` // 大奖 ID
	MissLimit          int64              `bson:"miss_limit" json:"miss_limit"`
	MissLimitStartTime int64              `bson:"miss_limit_start_time" json:"miss_limit_start_time"`
	MissLimitEndTime   int64              `bson:"miss_limit_end_time" json:"miss_limit_end_time"`
	Rates              map[int64]int      `bson:"rates" json:"rates"` // map[giftID]掉率
}

// FindPoolLuckyBox find PoolLuckyBox
func FindPoolLuckyBox(poolID int64) (*PoolLuckyBox, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var res PoolLuckyBox
	err := CollectionDrawPool().
		FindOne(ctx, bson.M{
			"pool_id": poolID,
			"type":    PoolTypeLuckyBox,
		}).Decode(&res)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return &res, nil
}

// ListPoolLuckyBox 查询宝盒奖池
func ListPoolLuckyBox(poolIDs []int64) ([]*PoolLuckyBox, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var pools []*PoolLuckyBox
	cur, err := CollectionDrawPool().Find(ctx, bson.M{
		"pool_id": bson.M{"$in": poolIDs},
		"type":    PoolTypeLuckyBox,
	})
	if err != nil {
		return nil, err
	}
	err = cur.All(ctx, &pools)
	if err != nil {
		return nil, err
	}
	return pools, nil
}

// Draw 抽奖
func (p *PoolLuckyBox) Draw(userID int64, drawNum int) ([]int64, error) {
	giftIDs := make([]int64, 0, len(p.Rates))
	weights := make([]int, 0, len(p.Rates))
	for giftID, w := range p.Rates {
		giftIDs = append(giftIDs, giftID)
		weights = append(weights, w)
	}
	d, err := goutil.NewDiscreteDistribution(weights, source, false)
	if err != nil {
		return nil, err
	}
	drawedGiftIDs := make([]int64, 0, drawNum)
	for i := 0; i < drawNum; i++ {
		drawedGiftIDs = append(drawedGiftIDs, giftIDs[d.NextInt()])
	}
	return p.guaranteed(userID, drawedGiftIDs)
}

func (p *PoolLuckyBox) isMissLimit() bool {
	if p.MissLimit <= 0 {
		return false
	}
	nowUnix := goutil.TimeNow().Unix()
	return p.MissLimitStartTime <= nowUnix && (p.MissLimitEndTime == 0 || nowUnix < p.MissLimitEndTime)
}

// guaranteed 保底机制
// NOTICE: 不是原子性操作，可能会有并发问题，需要外部保证并发安全
func (p *PoolLuckyBox) guaranteed(userID int64, giftIDs []int64) ([]int64, error) {
	if !p.isMissLimit() {
		return giftIDs, nil
	}

	key := keys.KeyLuckyBoxDrawMissCount2.Format(userID, p.PoolID)
	curMissDrawNum, err := service.Redis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.WithFields(logger.Fields{
			"user_id": userID,
			"pool_id": p.PoolID,
		}).Error(err)
		return giftIDs, nil
	}

	// 计算保底次数
	for i := 0; i < len(giftIDs); i++ {
		curMissDrawNum++
		if p.IsSSRID(giftIDs[i]) { // 抽中了大奖，保底次数清零
			curMissDrawNum = 0
		} else {
			if curMissDrawNum >= p.MissLimit { // 未抽中大奖且到达保底次数，触发保底将当前礼物替换为大奖，保底次数清零
				giftIDs[i] = p.SSRID
				curMissDrawNum = 0
			}
		}
	}

	err = service.Redis.Set(key, curMissDrawNum, 30*24*time.Hour).Err()
	if err != nil {
		return nil, err
	}
	return giftIDs, nil
}

// IsSSRID 是否是大奖
func (p *PoolLuckyBox) IsSSRID(giftID int64) bool {
	return p.SSRID == giftID
}
