package gift

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindPoolLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testPoolID int64 = 11111
		testGiftID int64 = 22222
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := CollectionDrawPool().DeleteMany(ctx, bson.M{"type": PoolTypeLuckyBox, "goods_id": testPoolID})
	require.NoError(err)
	_, err = CollectionDrawPool().InsertOne(ctx, PoolLuckyBox{
		Type:   PoolTypeLuckyBox,
		PoolID: testPoolID,
		SSRID:  testGiftID,
	})
	require.NoError(err)

	pool, err := FindPoolLuckyBox(testPoolID)
	require.NoError(err)
	require.NotNil(pool)
	assert.Equal(testPoolID, pool.PoolID)
	assert.Equal(testGiftID, pool.SSRID)
}

func TestListPoolLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := CollectionDrawPool().DeleteMany(ctx, bson.M{"pool_id": bson.M{"$in": bson.A{100011, 200011, 300011, 400011, 500011}}})
	require.NoError(err)
	pools := bson.A{
		PoolLuckyBox{PoolID: 100011, Type: PoolTypeLuckyBox},
		PoolLuckyBox{PoolID: 200011, Type: PoolTypeLuckyBox},
		PoolLuckyBox{PoolID: 300011, Type: PoolTypeLuckyBox},
		PoolLuckyBox{PoolID: 400011, Type: PoolTypeLuckyBox},
		PoolLuckyBox{PoolID: 500011, Type: PoolTypeLuckyBox},
	}
	_, err = CollectionDrawPool().InsertMany(ctx, pools)
	require.NoError(err)

	ps, err := ListPoolLuckyBox([]int64{100011, 200011, 300011, 400011, 500011})
	require.NoError(err)
	assert.Len(ps, 5)
}

func TestPoolLuckyBox_Draw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 123
		testPoolID int64 = 11111
		testGiftID int64 = 22222
	)
	key := keys.KeyLuckyBoxDrawMissCount2.Format(testUserID, testPoolID)
	require.NoError(service.Redis.Del(key).Err())

	pool := PoolLuckyBox{
		Type:               PoolTypeLuckyBox,
		PoolID:             testPoolID,
		SSRID:              testGiftID,
		MissLimit:          2,
		MissLimitStartTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		MissLimitEndTime:   goutil.TimeNow().Add(time.Hour).Unix(),
		Rates:              map[int64]int{1: 100},
	}
	giftIDs, err := pool.Draw(testUserID, 2)
	require.NoError(err)
	require.Len(giftIDs, 2)
	assert.Contains(giftIDs, pool.SSRID)
}

func TestPoolLuckyBox_IsSSRID(t *testing.T) {
	assert := assert.New(t)

	pool := PoolLuckyBox{
		PoolID: 1,
		SSRID:  2,
	}
	assert.True(pool.IsSSRID(2))
	assert.False(pool.IsSSRID(1))
}

func TestPoolLuckyBox_isMissLimit(t *testing.T) {
	assert := assert.New(t)

	pool := PoolLuckyBox{
		MissLimit: 0,
	}
	assert.False(pool.isMissLimit())

	pool = PoolLuckyBox{
		MissLimit:          1,
		MissLimitStartTime: goutil.TimeNow().Add(time.Hour).Unix(),
	}
	assert.False(pool.isMissLimit())

	pool = PoolLuckyBox{
		MissLimit:          1,
		MissLimitStartTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		MissLimitEndTime:   goutil.TimeNow().Add(-time.Minute).Unix(),
	}
	assert.False(pool.isMissLimit())

	pool = PoolLuckyBox{
		MissLimit:          1,
		MissLimitStartTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		MissLimitEndTime:   goutil.TimeNow().Add(time.Minute).Unix(),
	}
	assert.True(pool.isMissLimit())
}

func TestPoolLuckyBox_guaranteed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 123
		testPoolID int64 = 11111
	)

	pool := PoolLuckyBox{
		PoolID:    testPoolID,
		SSRID:     22222,
		MissLimit: 0,
	}
	giftIDs, err := pool.guaranteed(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, giftIDs)

	pool = PoolLuckyBox{
		PoolID:             testPoolID,
		SSRID:              22222,
		MissLimit:          4,
		MissLimitStartTime: goutil.TimeNow().Add(time.Hour).Unix(),
	}
	giftIDs, err = pool.guaranteed(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, giftIDs)

	pool = PoolLuckyBox{
		PoolID:             testPoolID,
		SSRID:              22222,
		MissLimit:          4,
		MissLimitStartTime: goutil.TimeNow().Add(-2 * time.Hour).Unix(),
		MissLimitEndTime:   goutil.TimeNow().Add(-time.Hour).Unix(),
	}
	giftIDs, err = pool.guaranteed(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, giftIDs)

	key := keys.KeyLuckyBoxDrawMissCount2.Format(testUserID, testPoolID)
	require.NoError(service.Redis.Del(key).Err())
	pool = PoolLuckyBox{
		PoolID:             testPoolID,
		SSRID:              22222,
		MissLimit:          4,
		MissLimitStartTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		MissLimitEndTime:   goutil.TimeNow().Add(time.Hour).Unix(),
	}
	giftIDs, err = pool.guaranteed(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, giftIDs)
	drawNum, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(3, drawNum)

	// 触发保底
	giftIDs, err = pool.guaranteed(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Len(giftIDs, 3)
	assert.Contains(giftIDs, pool.SSRID)
	drawNum, err = service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(2, drawNum) // 上次保底抽奖次数为 3 次，这次的第一抽触发了保底，所以保底抽奖次数为 2 次

	giftIDs, err = pool.guaranteed(testUserID, []int64{1, 2, pool.SSRID})
	require.NoError(err)
	assert.Len(giftIDs, 3)
	assert.Contains(giftIDs, pool.SSRID)
	drawNum, err = service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.Zero(drawNum) // 由于最后一抽是大奖，所以保底抽奖次数为 0 次

	giftIDs, err = pool.guaranteed(testUserID, []int64{1, pool.SSRID, 3})
	require.NoError(err)
	assert.Len(giftIDs, 3)
	assert.Contains(giftIDs, pool.SSRID)
	drawNum, err = service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(1, drawNum) // 由于中间一抽是大奖，所以保底抽奖次数为 1 次

	giftIDs, err = pool.guaranteed(testUserID, []int64{1, 2, 3})
	require.NoError(err)
	assert.Len(giftIDs, 3)
	assert.Contains(giftIDs, pool.SSRID)
	drawNum, err = service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.Zero(drawNum) // 由于最后一抽触发保底，所以保底抽奖次数为 0 次
}
