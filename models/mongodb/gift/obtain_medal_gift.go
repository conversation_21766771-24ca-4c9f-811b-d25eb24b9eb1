package gift

import (
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalremoverecord"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ObtainMedalGift 一键获取粉丝勋章礼物
type ObtainMedalGift struct {
	GiftID   int64     `json:"gift_id"`
	Name     string    `json:"name"`
	Price    int64     `json:"price"` // 钻石
	IconURL  string    `json:"icon_url"`
	Discount *discount `json:"discount,omitempty"`
}

type discount struct {
	Tip string `json:"tip,omitempty"`
}

// FindObtainMedalGift 查询一键获取粉丝勋章礼物
func FindObtainMedalGift(creatorID, userID int64, equip *goutil.Equipment) *ObtainMedalGift {
	giftID := livemedal.ObtainMedalGiftID

	var discountTip string
	if userID > 0 {
		// 只有登录用户才判断是否有资格购买 1 折粉丝勋章
		medalCfg, err := params.FindMedal()
		if err != nil {
			logger.Error(err)
			return nil
		}
		if medalCfg.IsFirstMedalDiscountEnable(equip) {
			exists, err := livemedalremoverecord.IsUserEverHadMedal(userID)
			if err != nil {
				logger.Error(err)
				return nil
			}
			if !exists {
				giftID = livemedal.ObtainMedalDiscountGiftID
				discountTip = medalCfg.FirstDiscount.GiftTip
			}
		}
	}

	// 1 折粉丝勋章礼物优先于半价粉丝勋章礼物展示，当不是 1 折粉丝勋章礼物时判断是否返回半价粉丝勋章礼物
	if giftID != livemedal.ObtainMedalDiscountGiftID {
		pointMulti, _ := livemedal.FindMedalPointMultiple(creatorID, false)
		if pointMulti > 1 {
			// NOTICE: 目前仅支持双倍亲密度直播间
			giftID = livemedal.ObtainMedalHalfPriceGiftID
		}
	}

	g, err := FindByGiftID(giftID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if g == nil {
		logger.WithFields(logger.Fields{"creator_id": creatorID, "gift_id": giftID}).Error("一键获取粉丝勋章礼物配置错误")
		return nil
	}
	mg := &ObtainMedalGift{
		GiftID:  giftID,
		Name:    g.Name,
		Price:   g.Price,
		IconURL: g.Icon,
	}
	if mg.GiftID == livemedal.ObtainMedalDiscountGiftID && discountTip != "" {
		mg.Discount = &discount{
			Tip: discountTip,
		}
	}
	return mg
}
