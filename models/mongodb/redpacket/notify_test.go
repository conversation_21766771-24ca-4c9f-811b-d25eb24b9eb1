package redpacket

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/liveim"
)

func TestNewEmptyNotify(t *testing.T) {
	assert := assert.New(t)

	n := NewEmptyNotify(primitive.NewObjectID(), 1)
	assert.Equal(liveim.TypeRedPacket, n.Type)
	assert.Equal(liveim.EventRedPacketEmpty, n.Event)
}

func TestNewExpireNotify(t *testing.T) {
	assert := assert.New(t)

	n := NewExpireNotify(primitive.NewObjectID(), 1)
	assert.Equal(liveim.TypeRedPacket, n.Type)
	assert.Equal(liveim.EventRedPacketExpire, n.Event)
}
