package redpacket

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LiveRedPacketLog 用户获取红包记录
type LiveRedPacketLog struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`   // 创建时间，单位：秒
	ModifiedTime int64              `bson:"modified_time"` // 更新时间，单位：秒

	RedPacketOID primitive.ObjectID `bson:"_red_packet_id"` // 红包 live_red_packet 表 _id
	UserID       int64              `bson:"user_id"`        // 用户 ID
	GiftID       int64              `bson:"gift_id"`        // 礼物 ID
	Luckiest     bool               `bson:"luckiest"`       // 最佳手气
	IP           string             `bson:"ip"`             // 用户 IP
}

// CreateLog 创建抢红包记录
func CreateLog(ctx context.Context, redPacketOID primitive.ObjectID, userID, giftID int64, isMostValuableGift bool, ip string) (*LiveRedPacketLog, error) {
	luckiest := false
	if giftID >= 0 && isMostValuableGift {
		// 若为最有价值礼物，需要判断是否为第一次抢到到该礼物，若是则为最佳手气
		luckiestLog, err := FindLuckiestLog(ctx, redPacketOID)
		if err != nil {
			return nil, err
		}
		luckiest = luckiestLog == nil
	}
	now := goutil.TimeNow()
	log := &LiveRedPacketLog{
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		RedPacketOID: redPacketOID,
		UserID:       userID,
		GiftID:       giftID,
		Luckiest:     luckiest,
		IP:           ip,
	}
	result, err := LiveRedPacketLogCollection().InsertOne(ctx, log)
	if err != nil {
		if mongodb.IsDuplicateKeyError(err) {
			// 唯一索引重复时，说明用户已抢过红包，此时不再创建，视作抢红包失败，不报错
			return nil, nil
		}
		return nil, err
	}
	log.OID = result.InsertedID.(primitive.ObjectID)
	return log, nil
}

// FindLuckiestLog 查询最佳手气红包记录
func FindLuckiestLog(ctx context.Context, redPacketID primitive.ObjectID) (*LiveRedPacketLog, error) {
	var log *LiveRedPacketLog
	err := LiveRedPacketLogCollection().
		FindOne(ctx, bson.M{"_red_packet_id": redPacketID, "luckiest": true}).Decode(&log)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return nil, err
	}
	return log, nil
}

// LiveRedPacketLogCollection collection
func LiveRedPacketLogCollection() *mongo.Collection {
	return service.MongoDB.Collection("live_red_packet_log")
}

// ListNotLuckiestRedPacketLog 查询非最佳手气用户获取红包记录
func ListNotLuckiestRedPacketLog(redPacketOID primitive.ObjectID, page, pageSize int64) ([]*LiveRedPacketLog, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"_red_packet_id": redPacketOID, "gift_id": bson.M{"$gt": 0}, "luckiest": false}
	count, err := LiveRedPacketLogCollection().CountDocuments(ctx, filter)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, page, pageSize)
	if !pa.Valid() {
		return make([]*LiveRedPacketLog, 0), pa, nil
	}
	mongoOpt := pa.SetFindOptions(nil)
	mongoOpt = mongoOpt.SetSort(bson.M{"create_time": -1})
	cur, err := LiveRedPacketLogCollection().Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	logs := make([]*LiveRedPacketLog, 0, pa.Limit())
	err = cur.All(ctx, &logs)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	return logs, pa, nil
}

// FindUserGrabbedRedPacketOIDs 根据红包 IDs 查询用户抢过的红包
func FindUserGrabbedRedPacketOIDs(userID int64, redPacketOIDs []primitive.ObjectID) ([]primitive.ObjectID, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"user_id":        userID,
		"_red_packet_id": bson.M{"$in": redPacketOIDs},
	}
	cur, err := LiveRedPacketLogCollection().Find(ctx, filter, options.Find())
	if err != nil {
		return nil, err
	}
	redPacketLogList := make([]*LiveRedPacketLog, 0, len(redPacketOIDs))
	err = cur.All(ctx, &redPacketLogList)
	if err != nil {
		return nil, err
	}

	grabbedRedPacketOIDs := make([]primitive.ObjectID, 0, len(redPacketLogList))
	for _, r := range redPacketLogList {
		grabbedRedPacketOIDs = append(grabbedRedPacketOIDs, r.RedPacketOID)
	}
	return grabbedRedPacketOIDs, nil
}

// FindUserLog 查询用户抢某红包的记录
func FindUserLog(redPacketOID primitive.ObjectID, userID int64) (*LiveRedPacketLog, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var log LiveRedPacketLog
	err := LiveRedPacketLogCollection().FindOne(ctx, bson.M{"_red_packet_id": redPacketOID, "user_id": userID}).Decode(&log)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &log, nil
}

// CountOneDayRedPacketLog 查询当天抢到红包记录数
// userID 用户 ID（可选）
// ip 用户 IP（可选）
// userID 和 ip 参数均不为空时触发 panic
func CountOneDayRedPacketLog(userID int64, ip string) (int64, error) {
	if userID > 0 && ip != "" {
		panic("参数错误")
	}
	if userID <= 0 && ip == "" {
		return 0, nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"gift_id":     bson.M{"$gt": 0},
		"create_time": bson.M{"$gte": goutil.BeginningOfDay(goutil.TimeNow()).Unix()},
	}
	if userID > 0 {
		filter["user_id"] = userID
	}
	if ip != "" {
		filter["ip"] = ip
	}

	return LiveRedPacketLogCollection().CountDocuments(ctx, filter)
}
