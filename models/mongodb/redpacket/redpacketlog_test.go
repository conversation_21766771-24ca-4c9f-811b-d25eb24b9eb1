package redpacket

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLiveRedPacketLogTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LiveRedPacketLog{}, "_id", "create_time", "modified_time",
		"_red_packet_id", "user_id", "gift_id", "luckiest", "ip")
}

func TestCreateLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testCreateLogUserID := int64(3423455)
	_, err := LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"user_id": testCreateLogUserID})
	require.NoError(err)
	testRedPacketOID := primitive.NewObjectID()
	testCreateLogGiftID := int64(324234)
	log, err := CreateLog(ctx, testRedPacketOID, testCreateLogUserID, testCreateLogGiftID, true, testIP)
	require.NoError(err)
	require.NotNil(log)
	assert.Equal(log.RedPacketOID, testRedPacketOID)
	assert.True(log.Luckiest)
	// 验证数据被创建
	err = LiveRedPacketLogCollection().FindOne(ctx, bson.M{
		"_red_packet_id": testRedPacketOID,
		"user_id":        testCreateLogUserID,
		"gift_id":        testCreateLogGiftID,
		"ip":             "127.0.0.1",
	}).Err()
	require.NoError(err)

	// 测试重复创建的情况
	log, err = CreateLog(ctx, testRedPacketOID, testCreateLogUserID, testCreateLogGiftID, false, testIP)
	require.NoError(err)
	assert.Nil(log)
	// 验证数据未被重复创建
	count, err := LiveRedPacketLogCollection().CountDocuments(ctx, bson.M{
		"_red_packet_id": testRedPacketOID,
		"user_id":        testCreateLogUserID,
	})
	require.NoError(err)
	assert.Equal(int64(1), count)

	// 验证已有最佳手气时插入数据
	testCreateLogUserID2 := int64(23434234)
	log, err = CreateLog(ctx, testRedPacketOID, testCreateLogUserID2, testCreateLogGiftID, true, testIP)
	require.NoError(err)
	require.NotNil(log)
	assert.Equal(log.RedPacketOID, testRedPacketOID)
	assert.False(log.Luckiest)
}

func TestListNotLuckiestRedPacketLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 构建测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := LiveRedPacketLogCollection().DeleteMany(ctx, bson.M{"gift_id": testGiftID})
	require.NoError(err)

	testRedPacketOID := primitive.NewObjectID()
	logs := make([]interface{}, 0, 6)
	nowUnix := goutil.TimeNow().Unix()
	for i := 0; i < 6; i++ {
		w := &LiveRedPacketLog{
			RedPacketOID: testRedPacketOID,
			UserID:       testUserID + int64(i),
			GiftID:       testGiftID,
			CreateTime:   nowUnix + int64(i),
			ModifiedTime: nowUnix + int64(i),
			Luckiest:     false,
		}
		logs = append(logs, w)
	}
	_, err = LiveRedPacketLogCollection().InsertMany(ctx, logs)
	require.NoError(err)

	res, _, err := ListNotLuckiestRedPacketLog(testRedPacketOID, 1, 5)
	require.NoError(err)
	assert.Len(res, 5)
	assert.Equal(logs[5].(*LiveRedPacketLog).CreateTime, res[0].CreateTime)
}

func TestFindUserGrabbedRedPacketOIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRedPacketOID1 := primitive.NewObjectID()
	testRedPacketOID2 := primitive.NewObjectID()
	testRedPacketOID3 := primitive.NewObjectID()

	nowUnix := goutil.TimeNow().Unix()
	logs := []interface{}{
		&LiveRedPacketLog{
			RedPacketOID: testRedPacketOID1,
			UserID:       12,
			GiftID:       1,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
		},
		&LiveRedPacketLog{
			RedPacketOID: testRedPacketOID2,
			UserID:       12,
			GiftID:       2,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
		},
		&LiveRedPacketLog{
			RedPacketOID: testRedPacketOID3,
			UserID:       13,
			GiftID:       2,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
		},
	}
	redPacketIDs := []primitive.ObjectID{testRedPacketOID1, testRedPacketOID2, testRedPacketOID3}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除测试数据
	_, err := LiveRedPacketLogCollection().DeleteMany(ctx,
		bson.M{
			"user_id": bson.M{"$in": []int64{12, 13}},
			"gift_id": bson.M{"$in": []int64{1, 2}},
		},
	)
	require.NoError(err)
	// 生成测试数据
	_, err = LiveRedPacketLogCollection().InsertMany(ctx, logs)
	require.NoError(err)

	grabbedRedPacketOIDs, err := FindUserGrabbedRedPacketOIDs(12, redPacketIDs)
	require.NoError(err)
	require.Len(grabbedRedPacketOIDs, 2)
	assert.Equal(grabbedRedPacketOIDs[0], testRedPacketOID1)
	assert.Equal(grabbedRedPacketOIDs[1], testRedPacketOID2)
}

func TestFindLuckiestLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试最佳手气记录不存在
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := FindLuckiestLog(ctx, primitive.NewObjectID())
	require.NoError(err)
	assert.Nil(res)

	// 构建测试数据
	_, err = LiveRedPacketLogCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	nowUnix := goutil.TimeNow().Unix()
	log := &LiveRedPacketLog{
		OID:          primitive.NewObjectID(),
		RedPacketOID: primitive.NewObjectID(),
		UserID:       testUserID,
		GiftID:       testGiftID,
		CreateTime:   nowUnix,
		ModifiedTime: nowUnix,
		Luckiest:     true,
	}
	_, err = LiveRedPacketLogCollection().InsertOne(ctx, log)
	require.NoError(err)
	res, err = FindLuckiestLog(ctx, log.RedPacketOID)
	require.NoError(err)
	assert.Equal(log, res)
}

func TestFindUserLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := LiveRedPacketLogCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	// 测试无数据的情况
	redPacketOID := primitive.NewObjectID()
	log, err := FindUserLog(redPacketOID, testUserID)
	require.NoError(err)
	assert.Nil(log)

	// 测试有数据的情况
	log = &LiveRedPacketLog{
		OID:          primitive.NewObjectID(),
		RedPacketOID: redPacketOID,
		UserID:       testUserID,
		GiftID:       testGiftID,
	}
	_, err = LiveRedPacketLogCollection().InsertOne(ctx, log)
	require.NoError(err)
	log, err = FindUserLog(redPacketOID, testUserID)
	require.NoError(err)
	require.NotNil(log)
	assert.Equal(testGiftID, log.GiftID)
}

func TestCountOneDayRedPacketLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := LiveRedPacketLogCollection().DeleteMany(ctx,
		bson.M{"$or": bson.A{bson.M{"user_id": testUserID}, bson.M{"ip": testIP}}})
	require.NoError(err)

	logs := make([]interface{}, 0, 5)
	for i := 0; i < 5; i++ {
		log := &LiveRedPacketLog{
			OID:          primitive.NewObjectID(),
			RedPacketOID: primitive.NewObjectID(),
			CreateTime:   goutil.TimeNow().Unix(),
			UserID:       testUserID,
			GiftID:       testGiftID,
			IP:           testIP,
		}
		if i == 0 {
			log.GiftID = 0
		}
		if i == 1 {
			log.CreateTime = goutil.TimeNow().AddDate(0, 0, -1).Unix()
		}
		if i == 2 {
			log.IP = "*************"
		}
		logs = append(logs, log)
	}
	_, err = LiveRedPacketLogCollection().InsertMany(ctx, logs)
	require.NoError(err)

	// 测试 userID 和 ip 均不为空
	assert.PanicsWithValue("参数错误", func() {
		_, _ = CountOneDayRedPacketLog(testUserID, testIP)
	})

	// 测试参数均为空
	count, err := CountOneDayRedPacketLog(0, "")
	require.NoError(err)
	assert.EqualValues(0, count)

	// 测试查询用户一天内抢到的红包数
	count, err = CountOneDayRedPacketLog(testUserID, "")
	require.NoError(err)
	assert.EqualValues(3, count)

	// 测试查询 IP 一天内抢到的红包数
	count, err = CountOneDayRedPacketLog(0, testIP)
	require.NoError(err)
	assert.EqualValues(2, count)
}
