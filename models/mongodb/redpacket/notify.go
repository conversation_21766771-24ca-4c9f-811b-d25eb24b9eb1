package redpacket

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/liveim"
)

// LiveRedPacketNotifyPayload 直播间消息礼物红包信息载体
type LiveRedPacketNotifyPayload struct {
	RedPacketID string `json:"red_packet_id"`
}

// EmptyOrExpireNotify 红包抢完或红包过期直播间消息
type EmptyOrExpireNotify struct {
	Type      string                      `json:"type"`
	Event     string                      `json:"event"`
	RoomID    int64                       `json:"room_id"`
	RedPacket *LiveRedPacketNotifyPayload `json:"red_packet"`
}

// NewEmptyNotify 创建一个新的红包抢完通知结构体
func NewEmptyNotify(oid primitive.ObjectID, roomID int64) *EmptyOrExpireNotify {
	return &EmptyOrExpireNotify{
		Type:   liveim.TypeRedPacket,
		Event:  liveim.EventRedPacketEmpty,
		RoomID: roomID,
		RedPacket: &LiveRedPacketNotifyPayload{
			RedPacketID: oid.Hex(),
		},
	}
}

// NewExpireNotify 创建一个新的红包过期通知结构体
func NewExpireNotify(oid primitive.ObjectID, roomID int64) *EmptyOrExpireNotify {
	return &EmptyOrExpireNotify{
		Type:   liveim.TypeRedPacket,
		Event:  liveim.EventRedPacketExpire,
		RoomID: roomID,
		RedPacket: &LiveRedPacketNotifyPayload{
			RedPacketID: oid.Hex(),
		},
	}
}
