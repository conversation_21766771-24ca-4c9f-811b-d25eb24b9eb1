package databus

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
)

type redPacketSetGrabbingMessage struct {
	RedPacketID string `json:"red_packet_id"`
}

// DelayRedPacketSetGrabbing 待抢红包转化为可抢红包 - 生产者
func DelayRedPacketSetGrabbing(packet *redpacket.LiveRedPacket) {
	if packet.Status != redpacket.StatusWaiting {
		return
	}

	var (
		key      = keys.DelayKeyRedPacketSetGrabbing1.Format(packet.OID.Hex())
		sendTime = time.Unix(packet.StartGrabTime, 0)
	)
	err := service.DatabusSendDelay(key, redPacketSetGrabbingMessage{RedPacketID: packet.OID.Hex()}, sendTime)
	if err != nil {
		logger.WithField("red_packet_id", packet.OID).Errorf("send delay msg error: %v", err)
		// PASS
	}
}

// DelayRedPacketSetGrabbingOperator 待抢红包转化为可抢红包 - 消费者
func DelayRedPacketSetGrabbingOperator() func(*databus.Message) {
	return func(message *databus.Message) {
		if !keys.DelayKeyRedPacketSetGrabbing1.MatchKey(message.Key) {
			return
		}

		var msg *redPacketSetGrabbingMessage
		err := json.Unmarshal(message.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}
		oid, err := primitive.ObjectIDFromHex(msg.RedPacketID)
		if err != nil {
			logger.WithField("red_packet_id", msg.RedPacketID).Error(err)
			return
		}

		// NOTICE: 此处只更新数据库的状态，不需要发消息通知等行为
		ok, err := redpacket.SetRedPacketGrabbing(oid)
		if err != nil {
			logger.WithField("red_packet_id", msg.RedPacketID).Error(err)
			return
		}
		if !ok {
			logger.WithField("red_packet_id", msg.RedPacketID).Warn("乐观锁更新失败")
			// PASS
		}
	}
}

// redPacketExpiredMessage 红包过期消息
type redPacketExpiredMessage struct {
	RedPacketID string `json:"red_packet_id"`

	oid primitive.ObjectID
}

// DelayRedPacketExpired 红包过期 - 生产者
func DelayRedPacketExpired(packet *redpacket.LiveRedPacket) {
	expireDuration, err := time.ParseDuration(config.Conf.Params.RedPacket.ExpireDuration)
	if err != nil {
		logger.WithField("red_packet_id", packet.OID.Hex()).Error(err)
		return
	}

	var (
		key      = keys.DelayKeyRedPacketExpired1.Format(packet.OID.Hex())
		sendTime = time.Unix(packet.CreateTime, 0).Add(expireDuration)
	)
	err = service.DatabusSendDelay(key, redPacketExpiredMessage{RedPacketID: packet.OID.Hex()}, sendTime)
	if err != nil {
		logger.WithField("red_packet_id", packet.OID.Hex()).Errorf("send delay msg error: %v", err)
		// PASS
	}
}

// DelayRedPacketExpiredOperator 红包过期 - 消费者
func DelayRedPacketExpiredOperator() func(*databus.Message) {
	return func(message *databus.Message) {
		if !keys.DelayKeyRedPacketExpired1.MatchKey(message.Key) {
			return
		}

		var msg *redPacketExpiredMessage
		err := json.Unmarshal(message.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}
		msg.oid, err = primitive.ObjectIDFromHex(msg.RedPacketID)
		if err != nil {
			logger.WithField("red_packet_id", msg.RedPacketID).Error(err)
			return
		}
		packet, err := redpacket.FindWaitAndGrabRedPacket(msg.oid)
		if err != nil {
			logger.WithField("red_packet_id", msg.RedPacketID).Error(err)
			return
		}
		if packet == nil {
			return
		}
		// 修改红包状态
		packet, err = redpacket.SetRedPacketExpired(msg.oid)
		if err != nil {
			logger.WithField("red_packet_id", msg.RedPacketID).Error(err)
			return
		}
		if packet == nil {
			logger.WithField("red_packet_id", msg.RedPacketID).Warn("乐观锁更新失败")
			return
		}
		if packet.RemainGiftNum <= 0 {
			return
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		// 直播间内红包数量 -1
		ok, err := room.UpdateRoomRedPacketNum(ctx, packet.RoomID, -1)
		if err != nil {
			logger.WithFields(logger.Fields{"red_packet_id": msg.RedPacketID, "room_id": packet.RoomID}).Error(err)
			// PASS
		}
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": msg.RedPacketID, "room_id": packet.RoomID}).Error("直播间内红包数量更新失败")
			// PASS
		}
		// 退还红包中的礼物
		packet.Refund(redpacket.RefundTypeExpire)

		// 直播间消息
		err = userapi.Broadcast(packet.RoomID, redpacket.NewExpireNotify(packet.OID, packet.RoomID),
			&userapi.BroadcastOption{Priority: userapi.BroadcastPriorityPurchased})
		if err != nil {
			logger.WithFields(logger.Fields{"red_packet_id": msg.RedPacketID, "room_id": packet.RoomID}).Error(err)
			// PASS
		}
	}
}
