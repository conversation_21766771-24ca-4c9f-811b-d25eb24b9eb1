package databus

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mongodb/redpacket"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testRoomID = 234234
	testUserID = 21312
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestDelayRedPacketSetGrabbing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	redPacketOID := primitive.NewObjectID()
	require.NoError(service.Redis.Del(keys.DelayKeyRedPacketSetGrabbing1.Format(redPacketOID.Hex())).Err())
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	packet := &redpacket.LiveRedPacket{
		OID:          redPacketOID,
		WaitDuration: 1,
		Status:       redpacket.StatusWaiting,
	}
	DelayRedPacketSetGrabbing(packet)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyRedPacketSetGrabbing1.Format(redPacketOID.Hex()), m.Key)

	var message redPacketSetGrabbingMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(redPacketOID.Hex(), message.RedPacketID)
}

func TestDelayRedPacketSetGrabbingOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := redpacket.LiveRedPacketCollection().InsertOne(ctx, redpacket.LiveRedPacket{
		RoomID: testRoomID,
		UserID: testUserID,
		Status: redpacket.StatusWaiting,
	})
	require.NoError(err)

	f := DelayRedPacketSetGrabbingOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	redPacketID := res.InsertedID.(primitive.ObjectID)
	qm := redPacketSetGrabbingMessage{
		RedPacketID: redPacketID.Hex(),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyRedPacketSetGrabbing1.Format(redPacketID.Hex()),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
	packet, err := redpacket.FindRedPacketByOID(redPacketID, testRoomID)
	require.NoError(err)
	require.NotNil(packet)
	assert.Equal(redpacket.StatusGrabbing, packet.Status)
}

func TestDelayRedPacketExpired(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	redPacketOID := primitive.NewObjectID()
	require.NoError(service.Redis.Del(keys.DelayKeyRedPacketExpired1.Format(redPacketOID.Hex())).Err())
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	packet := &redpacket.LiveRedPacket{
		OID: redPacketOID,
	}
	DelayRedPacketExpired(packet)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyRedPacketExpired1.Format(redPacketOID.Hex()), m.Key)

	var message redPacketExpiredMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(redPacketOID.Hex(), message.RedPacketID)
}

func TestDelayRedPacketExpiredOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := redpacket.LiveRedPacketCollection().InsertOne(ctx, redpacket.LiveRedPacket{
		RoomID: testRoomID,
		UserID: testUserID,
		Status: redpacket.StatusGrabbing,
	})
	require.NoError(err)

	f := DelayRedPacketExpiredOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	redPacketID := res.InsertedID.(primitive.ObjectID)
	qm := redPacketExpiredMessage{
		RedPacketID: redPacketID.Hex(),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyRedPacketExpired1.Format(redPacketID.Hex()),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
	packet, err := redpacket.FindRedPacketByOID(redPacketID, testRoomID)
	require.NoError(err)
	require.NotNil(packet)
	assert.Equal(redpacket.StatusExpired, packet.Status)
}
