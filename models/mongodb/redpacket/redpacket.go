package redpacket

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// StatusRefund 退款红包
	StatusRefund = -3
	// StatusWaiting 待抢红包
	StatusWaiting = 0
	// StatusGrabbing 可抢红包
	StatusGrabbing = 1
	// StatusEmpty 失效红包（红包被抢完）
	StatusEmpty = 2
	// StatusExpired 过期红包（红包未抢完，系统设置过期）
	StatusExpired = 3
)

// MaxCountGrabbingAndWaiting 房间内可抢和待抢红包的最大数量
const MaxCountGrabbingAndWaiting = 50

// 口令红包的口令类型
const (
	// KeywordTypeDefault 默认口令
	KeywordTypeDefault = iota + 1
	// KeywordTypeCustom 自定义口令
	KeywordTypeCustom
)

// MinPriceBroadcast 需要全站飘屏的红包最小售价（单位：钻石）
const MinPriceBroadcast = 21000

const (
	// RefundTypeExpire 红包过期退还（红包中的礼物）
	RefundTypeExpire = iota + 1
	// RefundTypeCloseLive 直播间关播退还（红包中的礼物）
	RefundTypeCloseLive
)

// LiveRedPacket 用户发送红包记录
type LiveRedPacket struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`   // 创建时间，单位：秒
	ModifiedTime int64              `bson:"modified_time"` // 更新时间，单位：秒

	GoodsID            int64       `bson:"goods_id"`              // 礼物红包商品 ID，live_goods 的 id
	UserID             int64       `bson:"user_id"`               // 发送红包的用户 ID
	RoomID             int64       `bson:"room_id"`               // 发送红包的直播间 ID
	CreatorID          int64       `bson:"creator_id"`            // 主播 ID
	AppearanceID       int64       `bson:"appearance_id"`         // 红包皮肤 appearance_id
	WaitDuration       int64       `bson:"wait_duration"`         // 开抢等待时间，单位：毫秒
	StartGrabTime      int64       `bson:"start_grab_time"`       // 开抢时间，单位：秒
	ExpireTime         int64       `bson:"expire_time"`           // 过期时间，单位：秒
	RemainGiftNum      int64       `bson:"remain_gift_num"`       // 剩余礼物数量
	Status             int         `bson:"status"`                // 红包状态，0：待抢红包，1：可抢红包，2：失效红包，3：过期红包
	Info               *PacketInfo `bson:"info"`                  // 红包信息
	TransactionID      int64       `bson:"transaction_id"`        // 订单号
	Context            string      `bson:"context"`               // 购买订单上下文，抢红包后存入背包礼物的 context
	SpecialRedPacketID int64       `bson:"special_red_packet_id"` // 若为特殊红包时，特殊红包 ID
}

// PacketInfo 红包信息
type PacketInfo struct {
	KeywordType int    `bson:"keyword_type"` // 口令红包的口令类型（1：默认口令；2：自定义口令）
	Keyword     string `bson:"keyword"`      // 口令内容
}

// LiveRedPacketCollection collection
func LiveRedPacketCollection() *mongo.Collection {
	return service.MongoDB.Collection("live_red_packet")
}

// AfterGrabUpdate 在抢红包后更新红包信息，剩余红包礼物数量 -1，在礼物数量为 0 时更新红包状态
// 若更新失败，第一个参数返回 false
func (rp *LiveRedPacket) AfterGrabUpdate(ctx context.Context) (bool, error) {
	now := goutil.TimeNow()
	updates := bson.M{
		"modified_time": now.Unix(),
	}
	if rp.RemainGiftNum == 1 {
		// 若更新前仅剩最后一个礼物，则需要将红包设为失效（红包被抢完状态）
		updates["status"] = StatusEmpty
		updates["expire_time"] = now.Unix()
	}
	err := LiveRedPacketCollection().FindOneAndUpdate(ctx, bson.M{
		"_id":             rp.OID,
		"status":          StatusGrabbing,
		"remain_gift_num": bson.M{"$gt": 0},
	}, bson.M{
		"$set": updates,
		"$inc": bson.M{"remain_gift_num": -1},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After)).Decode(rp)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	if rp.RemainGiftNum == 0 && rp.Status == StatusGrabbing {
		// 并发抢最后一个礼物时可能导致状态值未更新成功，需要重新更新
		_, err := LiveRedPacketCollection().UpdateOne(ctx, bson.M{
			"_id": rp.OID,
		}, bson.M{
			"$set": bson.M{
				"status":      StatusEmpty,
				"expire_time": now.Unix(),
			},
		})
		if err != nil {
			return false, err
		}
		rp.Status = StatusEmpty
		rp.ExpireTime = now.Unix()
	}
	return true, nil
}

// FindRedPacketByOID 查询用户发送红包信息
func FindRedPacketByOID(redPacketOID primitive.ObjectID, roomID int64) (*LiveRedPacket, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var redPacket *LiveRedPacket
	err := LiveRedPacketCollection().FindOne(ctx, bson.M{"_id": redPacketOID, "room_id": roomID}).Decode(&redPacket)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return redPacket, nil
}

// FindWaitAndGrabRedPacket 查询待抢或可抢状态红包
func FindWaitAndGrabRedPacket(redPacketOID primitive.ObjectID) (*LiveRedPacket, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var redPacket *LiveRedPacket
	err := LiveRedPacketCollection().FindOne(ctx, bson.M{
		"_id":    redPacketOID,
		"status": bson.M{"$in": []int{StatusWaiting, StatusGrabbing}},
	}).Decode(&redPacket)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return redPacket, nil
}

// SetRedPacketGrabbing 将红包设置为可抢红包
func SetRedPacketGrabbing(oid primitive.ObjectID) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res, err := LiveRedPacketCollection().UpdateOne(ctx,
		bson.M{
			"_id":    oid,
			"status": StatusWaiting,
		},
		bson.M{
			"$set": bson.M{
				"status": StatusGrabbing,
			},
		},
	)
	if err != nil {
		return false, err
	}
	return res.ModifiedCount > 0, nil
}

// SetRedPacketExpired 将红包置为过期
func SetRedPacketExpired(_id primitive.ObjectID) (*LiveRedPacket, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var (
		packet  *LiveRedPacket
		nowUnix = goutil.TimeNow().Unix()
	)
	err := LiveRedPacketCollection().FindOneAndUpdate(ctx,
		bson.M{
			"_id":    _id,
			"status": bson.M{"$in": bson.A{StatusWaiting, StatusGrabbing}},
		},
		bson.M{
			"$set": bson.M{
				"status":        StatusExpired,
				"expire_time":   nowUnix,
				"modified_time": nowUnix,
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&packet)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return packet, nil
}

// FindGiftNumFromCache 从缓存中查询礼物红包中剩余的礼物数量
func (rp *LiveRedPacket) FindGiftNumFromCache() (map[int64]int64, error) {
	key := keys.KeyRoomRedPacketGiftNum1.Format(rp.OID.Hex())
	giftNumMap, err := service.Redis.HGetAll(key).Result() // 获取数量为个位数
	if err != nil {
		return nil, err
	}

	giftNums := make(map[int64]int64, len(giftNumMap))
	for k, v := range giftNumMap {
		giftID, err := strconv.ParseInt(k, 0, 64)
		if err != nil {
			continue
		}
		num, err := strconv.ParseInt(v, 0, 64)
		if err != nil {
			continue
		}
		if num < 0 {
			// 并发抢时，可能在获取时拿到负数，此时需要赋值为 0
			num = 0
		}
		giftNums[giftID] = num
	}
	return giftNums, nil
}

// DecGiftNumForCache 扣除缓存中剩余红包礼物数量，若更新失败，第一个参数返回 false
func (rp *LiveRedPacket) DecGiftNumForCache(giftID int64) (bool, error) {
	key := keys.KeyRoomRedPacketGiftNum1.Format(rp.OID.Hex())
	field := strconv.FormatInt(giftID, 10)
	// 每次礼物数量 -1
	currentNum, err := service.Redis.HIncrBy(key, field, -1).Result()
	if err != nil {
		return false, err
	}
	if currentNum < 0 {
		// 若更新后数量小于 0，说明出现了并发抢最后一个礼物的情况，此时回退此次更新，视作更新失败
		_, err := service.Redis.HIncrBy(key, field, 1).Result()
		return false, err
	}
	return true, nil
}

// ListByRoomID 通过 room_id 查询用户发送的可抢和待抢红包列表
func ListByRoomID(roomID int64) ([]*LiveRedPacket, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"room_id": roomID,
		"status":  bson.M{"$in": []int64{StatusWaiting, StatusGrabbing}},
	}
	// 按开抢时间正序排序，若开抢时间一致则按红包状态倒序排序（可抢红包在待抢红包之前）
	sort := bson.D{{Key: "start_grab_time", Value: 1}, {Key: "status", Value: -1}}
	opt := options.Find().SetSort(sort).SetLimit(MaxCountGrabbingAndWaiting)
	cur, err := LiveRedPacketCollection().Find(ctx, filter, opt)
	if err != nil {
		return nil, err
	}

	redPacketList := make([]*LiveRedPacket, 0, MaxCountGrabbingAndWaiting)
	err = cur.All(ctx, &redPacketList)
	if err != nil {
		return nil, err
	}
	return redPacketList, nil
}

// IsExpired checks whether the red packet is expired
func (rp *LiveRedPacket) IsExpired() bool {
	return (rp.Status == StatusEmpty || rp.Status == StatusExpired) && rp.ExpireTime > 0
}

// IsValid 红包是否有效，只可以查询红包过期后 7 天内抢到红包的记录
func (rp *LiveRedPacket) IsValid() bool {
	return !rp.IsExpired() || goutil.TimeNow().AddDate(0, 0, -7).Unix() < rp.ExpireTime
}

// CountGrabbingAndWaiting 房间内可抢和待抢红包的数量
func CountGrabbingAndWaiting(roomID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"room_id": roomID,
		"status":  bson.M{"$in": []int64{StatusWaiting, StatusGrabbing}},
	}
	count, err := LiveRedPacketCollection().CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// Refund 退还礼物红包中的剩余礼物
func (rp *LiveRedPacket) Refund(refundType int) {
	if rp.RemainGiftNum <= 0 {
		return
	}
	creator, err := mowangskuser.FindByUserID(rp.CreatorID)
	redPacketID := rp.OID.Hex()
	if err != nil {
		logger.WithField("red_packet_id", redPacketID).Error(err)
		return
	}
	if creator == nil {
		logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "user_id": rp.CreatorID}).Errorf("user not found")
		return
	}

	// 查找礼物配置
	goods, err := livegoods.Find(rp.GoodsID, livegoods.GoodsTypeRedPacket)
	if err != nil {
		logger.WithField("red_packet_id", redPacketID).Error(err)
		return
	}
	if goods == nil {
		logger.WithField("red_packet_id", redPacketID).Error("未查询到礼物红包配置")
		return
	}
	more, err := goods.UnmarshalMore()
	if err != nil {
		logger.WithField("red_packet_id", redPacketID).Error(err)
		return
	}
	if !more.IsValidRedPacket() {
		logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "goods_id": goods.ID}).Error("礼物红包配置记录为空")
		return
	}
	giftIDs := make([]int64, 0, len(more.RedPacket.Gifts))
	for _, gift := range more.RedPacket.Gifts {
		giftIDs = append(giftIDs, gift.ID)
	}
	giftMap, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		logger.WithField("red_packet_id", redPacketID).Error(err)
		return
	}
	// 统计剩余礼物数量
	giftNumMap, err := rp.FindGiftNumFromCache()
	if err != nil {
		logger.WithField("red_packet_id", redPacketID).Error(err)
		return
	}
	if giftNumMap == nil {
		logger.WithField("red_packet_id", redPacketID).Error("剩余礼物数量统计异常")
		return
	}
	var (
		now          = goutil.TimeNow()
		startTime    = now.Unix()
		endTime      int64
		sysMsgInfos  = make([]string, 0, len(giftNumMap)) // ["幸运签×2", "情缘花灯×1"]
		sysMsgFormat string
	)
	switch refundType {
	case RefundTypeExpire:
		endTime = now.Add(24 * time.Hour).Unix()
		sysMsgFormat = "您 %s 在%s直播间内发出的礼物红包，已超过红包可存续最大时间，已停止领取。剩余礼物%s 已返还至您的礼物背包中，请在 24 小时内使用。"
	case RefundTypeCloseLive:
		endTime = now.Add(24*time.Hour + 5*time.Minute).Unix()
		sysMsgFormat = "您 %s 在%s直播间内发出的礼物红包，由于主播下播，已停止领取。剩余礼物%s 已返还至您的礼物背包中，请在 24 小时内使用。"
	default:
		panic(fmt.Errorf("不支持的礼物红包退回类型：%d", refundType))
	}

	for _, id := range giftIDs {
		g, ok := giftMap[id]
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "gift_id": id}).Error("礼物不存在")
			continue
		}
		giftNum, ok := giftNumMap[id]
		if !ok {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "gift_id": id}).Error("礼物不存在")
			continue
		}
		if giftNum <= 0 {
			if giftNum < 0 {
				logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "gift_id": id, "num": giftNum}).Error("礼物剩余数量异常")
			}
			continue
		}

		// 系统消息
		sysMsgInfos = append(sysMsgInfos, fmt.Sprintf("%s×%d", g.Name, giftNum))

		// 退回礼物
		if rp.UserID == rp.CreatorID {
			// 返回给主播背包
			err = creatoritems.NewTransactionAdder(rp.CreatorID, rp.TransactionID, rp.Context, int(giftNum)).
				Append(g, giftNum, startTime, endTime).
				Add()
		} else {
			// 返回给用户背包
			err = useritems.NewTransactionAdder(rp.UserID, rp.TransactionID, rp.Context, int(giftNum)).
				Append(g, giftNum, startTime, endTime).
				Add()
		}
		if err != nil {
			logger.WithFields(logger.Fields{"red_packet_id": redPacketID, "gift_id": g.GiftID, "num": giftNum}).
				Errorf("礼物红包中礼物退回失败 error: %v", err)
		}
	}

	// 发送系统消息
	sysMsgs := []pushservice.SystemMsg{
		{
			UserID: rp.UserID,
			Title:  "礼物红包剩余礼物已退回",
			Content: fmt.Sprintf(sysMsgFormat,
				time.Unix(rp.CreateTime, 0).Format(goutil.TimeFormatHMS),
				creator.Username,
				strings.Join(sysMsgInfos, "、")),
		},
	}
	err = service.PushService.SendSystemMsgWithOptions(sysMsgs, nil)
	if err != nil {
		logger.WithField("red_packet_id", redPacketID).Error(err)
		// PASS
	}
}
