package redpacket

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testUserID    = int64(12)
	testRoomID    = int64(123)
	testGiftID    = int64(23332)
	testCreatorID = int64(456623)
	testIP        = "127.0.0.1"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(-3, StatusRefund)
	assert.Equal(0, StatusWaiting)
	assert.Equal(1, StatusGrabbing)
	assert.Equal(2, StatusEmpty)
	assert.Equal(3, StatusExpired)
}

func TestLiveRedPacketTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LiveRedPacket{}, "_id", "create_time", "modified_time", "goods_id", "user_id", "room_id",
		"creator_id", "appearance_id", "wait_duration", "start_grab_time", "expire_time", "remain_gift_num",
		"status", "info", "transaction_id", "context", "special_red_packet_id")

	kc.Check(PacketInfo{}, "keyword_type", "keyword")
}

func mockOneLiveRedPacket(t *testing.T) *LiveRedPacket {
	require := require.New(t)

	packet := &LiveRedPacket{
		OID:       primitive.NewObjectID(),
		GoodsID:   55,
		Status:    StatusWaiting,
		UserID:    testUserID,
		RoomID:    testRoomID,
		CreatorID: testCreatorID,
		Info: &PacketInfo{
			KeywordType: 1,
			Keyword:     "test",
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := LiveRedPacketCollection().DeleteMany(ctx, bson.M{"user_id": packet.UserID})
	require.NoError(err)
	_, err = LiveRedPacketCollection().InsertOne(ctx, packet)
	require.NoError(err)
	return packet
}

func TestLiveRedPacket_AfterGrabUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试礼物剩余数量充足的情况
	nowUnix := goutil.TimeNow().Unix()
	testOID := primitive.NewObjectID()
	redPacket := &LiveRedPacket{
		OID:           testOID,
		GoodsID:       55,
		UserID:        testUserID,
		RoomID:        testRoomID,
		CreatorID:     testCreatorID,
		Status:        StatusGrabbing,
		RemainGiftNum: 2,
		CreateTime:    nowUnix,
		ModifiedTime:  nowUnix,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := LiveRedPacketCollection().DeleteOne(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = LiveRedPacketCollection().InsertOne(ctx, redPacket)
	require.NoError(err)
	ok, err := redPacket.AfterGrabUpdate(ctx)
	require.NoError(err)
	assert.True(ok)
	// 验证数据
	require.NoError(err)
	assert.Equal(StatusGrabbing, redPacket.Status)
	require.Equal(int64(1), redPacket.RemainGiftNum)

	// 测试礼物剩余数量为 1 的情况
	ok, err = redPacket.AfterGrabUpdate(ctx)
	require.NoError(err)
	assert.True(ok)
	require.NoError(err)
	assert.Equal(StatusEmpty, redPacket.Status)
	require.Equal(int64(0), redPacket.RemainGiftNum)

	// 测试礼物剩余数量为 0 的情况
	ok, err = redPacket.AfterGrabUpdate(ctx)
	require.NoError(err)
	assert.False(ok)
	require.NoError(err)
	assert.Equal(StatusEmpty, redPacket.Status)
	assert.Equal(int64(0), redPacket.RemainGiftNum)
}

func TestFindRedPacketByOID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	packet := mockOneLiveRedPacket(t)

	newPacket, err := FindRedPacketByOID(packet.OID, packet.RoomID)
	require.NoError(err)
	assert.Equal(packet, newPacket)

	newPacket, err = FindRedPacketByOID(primitive.NewObjectID(), packet.RoomID)
	require.NoError(err)
	assert.Nil(newPacket)
}

func TestFindWaitAndGrabRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	packet := mockOneLiveRedPacket(t)

	newPacket, err := FindWaitAndGrabRedPacket(packet.OID)
	require.NoError(err)
	assert.Equal(packet, newPacket)

	newPacket, err = FindWaitAndGrabRedPacket(primitive.NewObjectID())
	require.NoError(err)
	assert.Nil(newPacket)
}

func TestSetRedPacketGrabbing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	packet := mockOneLiveRedPacket(t)

	ok, err := SetRedPacketGrabbing(packet.OID)
	require.NoError(err)
	assert.True(ok)

	ok, err = SetRedPacketGrabbing(packet.OID)
	require.NoError(err)
	assert.False(ok)
}

func TestSetRedPacketExpired(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := LiveRedPacketCollection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	assert.NoError(err)
	res, err := LiveRedPacketCollection().InsertOne(ctx, &LiveRedPacket{
		UserID: testUserID,
		RoomID: testRoomID,
	})
	require.NoError(err)

	packet, err := SetRedPacketExpired(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.Equal(StatusExpired, packet.Status)
}

func TestLiveRedPacket_FindGiftNumFromCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		rp  = &LiveRedPacket{OID: primitive.NewObjectID()}
		key = keys.KeyRoomRedPacketGiftNum1.Format(rp.OID.Hex())
	)
	defer func() {
		_ = service.Redis.Del(key)
	}()
	_, err := service.Redis.Pipelined(func(pipeliner redis.Pipeliner) error {
		pipeliner.HSet(key, "1", 2)
		pipeliner.HSet(key, "3", 4)
		pipeliner.HSet(key, "4", -1) // 并发抢可能为负数
		return nil
	})
	require.NoError(err)

	giftNums, err := rp.FindGiftNumFromCache()
	require.NoError(err)
	require.Len(giftNums, 3)
	assert.EqualValues(2, giftNums[1])
	assert.EqualValues(4, giftNums[3])
	assert.EqualValues(0, giftNums[4]) // 负数的情况返回 0
}

func TestLiveRedPacket_DecGiftNumForCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		rp  = &LiveRedPacket{OID: primitive.NewObjectID()}
		key = keys.KeyRoomRedPacketGiftNum1.Format(rp.OID.Hex())
	)
	defer func() {
		_ = service.Redis.Del(key)
	}()

	// 测试正常扣除礼物数的情况
	require.NoError(service.Redis.HSet(key, "2333", 1).Err())
	ok, err := rp.DecGiftNumForCache(2333)
	require.NoError(err)
	assert.True(ok)
	num, err := service.Redis.HGet(key, "2333").Result()
	require.NoError(err)
	assert.Equal("0", num)

	// 测试礼物数已为 0 时进行扣除
	ok, err = rp.DecGiftNumForCache(2333)
	require.NoError(err)
	assert.False(ok)
	num, err = service.Redis.HGet(key, "2333").Result()
	require.NoError(err)
	assert.Equal("0", num)
}

func TestListByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	nowUnix := goutil.TimeNow().Unix()
	redPackets := []interface{}{
		&LiveRedPacket{
			OID:           primitive.NewObjectID(),
			GoodsID:       55,
			UserID:        110,
			RoomID:        testRoomID,
			CreatorID:     12,
			Status:        StatusWaiting,
			WaitDuration:  60000,
			StartGrabTime: nowUnix + 60,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
		&LiveRedPacket{
			OID:           primitive.NewObjectID(),
			GoodsID:       56,
			UserID:        111,
			RoomID:        testRoomID,
			CreatorID:     12,
			Status:        StatusGrabbing,
			StartGrabTime: nowUnix,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
		&LiveRedPacket{
			OID:           primitive.NewObjectID(),
			GoodsID:       57,
			UserID:        111,
			RoomID:        testRoomID,
			CreatorID:     12,
			Status:        StatusExpired,
			WaitDuration:  120000,
			StartGrabTime: nowUnix + 120,
			CreateTime:    nowUnix,
			ModifiedTime:  nowUnix,
		},
	}
	_, err := LiveRedPacketCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = LiveRedPacketCollection().InsertMany(ctx, redPackets)
	require.NoError(err)

	list, err := ListByRoomID(testRoomID)
	require.NoError(err)
	require.Len(list, 2)
	assert.Equal(list[0], redPackets[1])
	assert.Equal(list[1], redPackets[0])
}

func TestLiveRedPacket_IsExpired(t *testing.T) {
	assert := assert.New(t)

	redPacket := &LiveRedPacket{
		Status: StatusGrabbing,
	}

	// 测试红包未过期
	assert.False(redPacket.IsExpired())

	// 测试红包已过期
	redPacket.Status = StatusExpired
	redPacket.ExpireTime = goutil.TimeNow().Unix()
	assert.True(redPacket.IsExpired())
}

func TestLiveRedPacket_IsValid(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	// 测试红包过期
	redPacket := &LiveRedPacket{
		Status:     StatusExpired,
		ExpireTime: now.Unix(),
	}
	assert.True(redPacket.IsValid())

	// 测试红包过期后 7 天
	goutil.SetTimeNow(func() time.Time {
		return now.AddDate(0, 0, 7)
	})
	defer goutil.SetTimeNow(nil)
	assert.False(redPacket.IsValid())
}

func TestLiveRedPacket_Refund(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGiftID := int64(40343)
	testUserID := int64(234234)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	nowUnix := goutil.TimeNow().Unix()
	redPacket := LiveRedPacket{
		OID:           primitive.NewObjectID(),
		GoodsID:       19,
		UserID:        testUserID,
		RoomID:        testRoomID,
		CreatorID:     12,
		Status:        StatusGrabbing,
		StartGrabTime: nowUnix,
		RemainGiftNum: 0,
	}
	// 测试无剩余礼物退还的情况
	redPacket.Refund(RefundTypeExpire)
	// 验证无礼物退还
	var ui *useritems.UserItem
	err = useritems.Collection().FindOne(ctx, bson.M{"user_id": testUserID, "gift_id": testGiftID}).Decode(&ui)
	require.True(mongodb.IsNoDocumentsError(err))

	// 测试有剩余礼物需要退还的情况
	redPacket.RemainGiftNum = 1
	// 创建礼物数据
	g := gift.Gift{
		GiftID: testGiftID,
		Name:   "测试礼物",
		Type:   gift.TypeRebate,
	}
	_, err = gift.Collection().DeleteOne(ctx, bson.M{"gift_id": testGiftID})
	require.NoError(err)
	_, err = gift.Collection().InsertOne(ctx, g)
	require.NoError(err)
	key := keys.KeyRoomRedPacketGiftNum1.Format(redPacket.OID.Hex())
	field := strconv.FormatInt(testGiftID, 10)
	// 设置缓存中剩余礼物数量
	_, err = service.Redis.HSet(key, field, 1).Result()
	require.NoError(err)
	redPacket.Refund(RefundTypeExpire)
	// 验证礼物被退还
	err = useritems.Collection().FindOne(ctx, bson.M{"user_id": testUserID, "gift_id": testGiftID}).Decode(&ui)
	require.NoError(err)
	assert.Equal(int64(1), ui.Num)
}
