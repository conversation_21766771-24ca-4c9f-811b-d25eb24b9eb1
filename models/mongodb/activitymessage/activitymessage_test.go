package activitymessage

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	logger.InitTestLog()
	service.InitTest()
	service.SetDBUseSQLite()
	addTestData()
	m.Run()
}

var testMsg ActivityMessage

func addTestData() {
	t := time.Unix(1234567890, 0)
	testMsg = ActivityMessage{
		EventID:      10,
		FromUserID:   12,
		ToUserID:     13,
		RoomID:       123,
		Content:      "test",
		CreateTime:   t,
		ModifiedTime: goutil.TimeNow(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"create_time": t},
		bson.M{"$set": testMsg}, options.Update().SetUpsert(true))
	if err != nil {
		logger.Fatal(err)
	}
}

func TestTagKey(t *testing.T) {
	msg := ActivityMessage{}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(msg, "_id", "event_id", "type", "from_user_id", "to_user_id", "room_id",
		"content", "create_time", "modified_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(msg, "type", "room_id", "content", "create_time", "from_user", "to_user")
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)
	col := Collection()
	assert.Equal("test_activity_messages", col.Name())
}

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctime := time.Unix(1234567890, 0)
	filter := bson.M{"create_time": ctime, "event_id": 10}
	msgs, err := List(filter, nil, true)
	require.NoError(err)
	require.Len(msgs, 1)
	assert.Equal(testMsg.EventID, msgs[0].EventID)
	assert.NotNil(msgs[0].FromUser)
	assert.NotNil(msgs[0].ToUser)
}

func TestListByPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctime := time.Unix(1234567890, 0)
	filter := bson.M{"create_time": ctime, "event_id": 10}
	res, pa, err := ListByPage(filter, nil, 1, 1)
	require.NoError(err)
	assert.Equal(int64(1), pa.Count)
	assert.Len(res, 1)
	res, _, err = ListByPage(filter, nil, 2, 1)
	require.NoError(err)
	assert.Empty(res)
}

func TestAssignJSON(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	msgs := []*ActivityMessage{
		{FromUserID: 12, ToUserID: 13, CreateTime: time.Unix(123, 0)},
		{FromUserID: 14, ToUserID: 15},
		{FromUserID: 16, ToUserID: 17},
	}
	require.NoError(AssignJSON(msgs))
	for i := 0; i < 2; i++ {
		assert.NotNil(msgs[i].FromUser, i)
		assert.NotNil(msgs[i].ToUser, i)
	}
	assert.Nil(msgs[2].FromUser)
	assert.Nil(msgs[2].ToUser)
	assert.Equal(msgs[0].CreateTimeJSON, goutil.TimeUnixMilli(123000))
}
