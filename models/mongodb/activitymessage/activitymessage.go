package activitymessage

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 告白类型
const (
	TypeNotifyShowLove = iota + 1 // 附带直播间飘屏的告白
	TypeNormalShowLove            // 普通告白

	TypeLimit
)

// ActivityMessage activity message
type ActivityMessage struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	EventID      int64              `bson:"event_id" json:"-"`
	Type         int                `bson:"type" json:"type"`
	FromUserID   int64              `bson:"from_user_id" json:"-"`
	ToUserID     int64              `bson:"to_user_id" json:"-"`
	RoomID       int64              `bson:"room_id" json:"room_id"`
	Content      string             `bson:"content" json:"content"`
	CreateTime   time.Time          `bson:"create_time" json:"-"`
	ModifiedTime time.Time          `bson:"modified_time" json:"-"`

	FromUser       *mowangskuser.Simple `bson:"-" json:"from_user"`
	ToUser         *mowangskuser.Simple `bson:"-" json:"to_user"`
	CreateTimeJSON goutil.TimeUnixMilli `bson:"-" json:"create_time"` // json 中使用的 create_time
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("activity_messages")
}

// List list ActivityMessage
func List(filter interface{}, opt *options.FindOptions, assignJSON bool) ([]*ActivityMessage, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	cur, err := col.Find(ctx, filter, opt)
	if err != nil {
		return nil, err
	}
	var res []*ActivityMessage
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	if assignJSON {
		err = AssignJSON(res)
		if err != nil {
			return res, err
		}
	}
	return res, nil
}

// ListByPage list ActivityMessage by page
func ListByPage(filter interface{}, opt *options.FindOptions, p, pageSize int64) ([]*ActivityMessage, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	count, err := col.CountDocuments(ctx, filter)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []*ActivityMessage{}, pa, nil
	}
	opt = pa.SetFindOptions(opt)
	res, err := List(filter, opt, true)
	return res, pa, err
}

// AssignJSON 补上用户信息，创建时间
func AssignJSON(msgs []*ActivityMessage) error {
	userIDs := make([]int64, len(msgs)*2)
	for i := range msgs {
		userIDs[2*i] = msgs[i].FromUserID
		userIDs[2*i+1] = msgs[i].ToUserID
	}
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return err
	}
	for i := range msgs {
		if u := users[msgs[i].FromUserID]; u != nil {
			msgs[i].FromUser = u
		}
		if u := users[msgs[i].ToUserID]; u != nil {
			msgs[i].ToUser = u
		}
		msgs[i].CreateTimeJSON = goutil.NewTimeUnixMilli(msgs[i].CreateTime)
	}
	return nil
}
