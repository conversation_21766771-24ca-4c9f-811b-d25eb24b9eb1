package livegashaponuserrank

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// SortScoreDesc sort by score desc
var SortScoreDesc = struct {
	Score        int `bson:"score"`
	ModifiedTime int `bson:"modified_time"`
}{-1, 1}

// Rank user rank
type Rank struct {
	OID          primitive.ObjectID `bson:"_id"`
	Key          string             `bson:"key,omitempty"` // 每周一的日期 2006-01-02
	PoolID       int64              `bson:"pool_id"`
	UserID       int64              `bson:"user_id"`
	Score        int64              `bson:"score"`
	Achievement  map[string]int     `bson:"achievement,omitempty"`
	CreateTime   int64              `bson:"create_time,omitempty"`
	ModifiedTime int64              `bson:"modified_time,omitempty"` // 秒级时间戳
}

// Key returns user rank key by time
func Key(now time.Time) string {
	return util.BeginningOfWeek(now).Format(util.TimeFormatYMD)
}

// GiftKey 添加 gift_ 前缀
func GiftKey(giftID int64) string {
	return fmt.Sprintf("gift_%d", giftID)
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_gashapon_user_rank")
}

// Save 保存用户榜记录
func (r *Rank) Save(giftNumMap map[int64]int) error {
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	inc := bson.M{
		"score": r.Score,
	}
	for key, n := range giftNumMap {
		inc["achievement."+GiftKey(key)] = n
	}
	update := bson.M{
		"$set":         bson.M{"modified_time": now.Unix()},
		"$setOnInsert": bson.M{"create_time": now.Unix()},
		"$inc":         inc,
	}

	_, err := Collection().UpdateOne(ctx,
		bson.M{
			"key":     Key(now),
			"pool_id": r.PoolID,
			"user_id": r.UserID,
		},
		update,
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

// RankList find user rank
func RankList(filter bson.M, findOptions ...*options.FindOptions) ([]*Rank, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, filter, findOptions...)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var ranks []*Rank
	err = cur.All(ctx, &ranks)
	return ranks, err
}

// FindRank find user rank
func FindRank(filter bson.M, opts ...*options.FindOneOptions) (*Rank, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var rank Rank
	err := Collection().FindOne(ctx, filter, opts...).Decode(&rank)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &rank, err
}
