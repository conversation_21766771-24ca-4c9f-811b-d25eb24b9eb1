package livegashaponuserrank

import (
	"fmt"
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestRankSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gm := map[int64]int{1: 1, 2: 2, 3: 3}
	rank := Rank{
		PoolID:      -1,
		UserID:      12,
		Score:       123,
		Achievement: map[string]int{"gift_1": 1, "gift_2": 2, "gift_3": 3},
	}

	defer func() {
		ctx, cancel := service.MongoDB.Context()
		_, _ = Collection().DeleteMany(ctx, bson.M{"pool_id": rank.PoolID})
		cancel()
	}()

	err := rank.Save(gm)
	require.NoError(err)

	r, err := FindRank(bson.M{
		"pool_id": rank.PoolID,
		"user_id": rank.UserID,
	})
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(r.Score, rank.Score)
	assert.Equal(r.Achievement, rank.Achievement)

	err = rank.Save(gm)
	require.NoError(err)

	r, err = FindRank(bson.M{
		"pool_id": rank.PoolID,
		"user_id": rank.UserID,
	})
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(r.Score, rank.Score*2)
	for key := range gm {
		assert.EqualValues(key*2, r.Achievement[fmt.Sprintf("gift_%d", key)])
	}
}

func TestRankList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "2006-01-02"
	list, err := RankList(bson.M{"key": key}, options.Find().SetSort(SortScoreDesc))
	require.NoError(err)
	require.NotEmpty(list)
	assert.True(sort.SliceIsSorted(list, func(i, j int) bool {
		return list[i].Score >= list[j].Score
	}))
}
