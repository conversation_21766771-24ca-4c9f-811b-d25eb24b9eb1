package userappearance

import (
	"context"
	"encoding/json"
	"errors"
	"html"
	"slices"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// status 物品状态
const (
	StatusPending = iota // 暂无
	StatusOwned          // 已持有
	StatusWorn           // 正在使用
	StatusExpired        // 已过期（该状态不会存到数据库中）
)

// own status 物品历史状态，由于 新的 和 已持有 两个状态互斥，故而新建一种新的枚举值
const (
	OwnStatusNew  = iota // 新的
	OwnStatusUsed        // 使用过
)

const unlimitedTime = -1 // 不指定时长，如果模版为有限时间则取最大时间，如果模版为无限时间则为永久

// 外观最大的佩戴数量
const (
	MaxWearingBadgesNum         = 2 // 最大佩戴称号数量
	MaxWearingIdentityBadgesNum = 3 // 最大佩戴身份铭牌数量
)

// UserAppearance mongodb 查找的外观的结构
// 这里的 JSON tag 仅用于 Redis 缓存
type UserAppearance struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`

	AppearanceID int64  `bson:"appearance_id" json:"appearance_id"`
	Name         string `bson:"name" json:"name"`
	Type         int    `bson:"type" json:"type"`
	Status       int    `bson:"status,omitempty" json:"status"` // 发放外观时，如果用户已经持有过外观并且没有过期，则不应该更新 Status（使用 omitempty）
	OwnStatus    int    `bson:"own_status" json:"own_status"`
	UserID       int64  `bson:"user_id" json:"user_id"`
	From         int    `bson:"from" json:"from"`
	Intro        string `bson:"intro" json:"intro"`
	Icon         string `bson:"icon" json:"icon"`

	Effect         string `bson:"effect" json:"effect,omitempty"`
	WebEffect      string `bson:"web_effect" json:"web_effect,omitempty"`
	EffectDuration int64  `bson:"effect_duration" json:"effect_duration,omitempty"`

	// 头像框/名片框/气泡框/称号/直播间挂件和进场通知都需要 Image 字段
	Image string `bson:"image,omitempty" json:"image,omitempty"`
	// 新版名片框需要 ImageNew 字段
	// WORKAROUND: 兼容 iOS >= 4.7.4, 安卓 >= 5.6.2
	ImageNew string `bson:"image_new,omitempty" json:"image_new,omitempty"`
	// 仅气泡框和送礼通知需要 Frame 和 TextColor 字段
	Frame     string `bson:"frame,omitempty" json:"frame,omitempty"`
	TextColor string `bson:"text_color,omitempty" json:"text_color,omitempty"`
	// 目前仅名片框和送礼通知需要 TextColorItem 字段
	TextColorItem *appearance.TextColorItem `bson:"text_color_item,omitempty" json:"text_color_item,omitempty"`
	// 仅座驾需要 MessageBar 字段
	MessageBar *appearance.MessageBar `bson:"message_bar,omitempty" json:"message_bar,omitempty"`
	// 仅称号需要 Position 字段，用于定位称号的顺序和位置
	Position int `bson:"position,omitempty" json:"position,omitempty"`
	// 仅进场通知需要 WelcomeMessage 和 EntryStyle 字段
	WelcomeMessage *appearance.WelcomeMessage `bson:"welcome_message,omitempty" json:"welcome_message,omitempty"`
	EntryStyle     int                        `bson:"entry_style,omitempty" json:"entry_style,omitempty"`
	// 目前仅红包需要 resource 字段，用于存红包封面皮肤资源地址
	Resource string `bson:"resource,omitempty" json:"resource,omitempty"`

	StartTime    int64  `bson:"start_time" json:"start_time"`
	ExpireTime   *int64 `bson:"expire_time" json:"expire_time"`
	ModifiedTime int64  `bson:"modified_time" json:"modified_time"`
}

// Vehicle 座驾
type Vehicle struct {
	UserID int64 `json:"-"`

	VehicleID   int64  `json:"-"`
	VehicleName string `json:"-"`

	IconURL        string `json:"icon_url"`
	Effect         string `json:"effect_url"`
	WebEffect      string `json:"web_effect_url"`
	EffectDuration int64  `json:"effect_duration,omitempty"`

	// Message 和 Bubble 用于兼容老版本客户端，新版本客户端（iOS 4.6.4，安卓 5.5.3）使用 MessageBar
	Message string         `json:"message,omitempty"`
	Bubble  *bubble.Simple `json:"bubble,omitempty"`

	MessageBar *appearance.MessageBar `json:"message_bar"`
}

// EntryBubble 进场通知
type EntryBubble struct {
	ImageURL       string                     `json:"image_url,omitempty"`
	EntryStyle     int                        `json:"entry_style,omitempty"`
	WelcomeMessage *appearance.WelcomeMessage `json:"welcome_message,omitempty"`
}

// GiftNotification 礼物通知
type GiftNotification struct {
	UsernameColor string `json:"username_color,omitempty"`
	TextColor     string `json:"text_color,omitempty"`
	FrameURL      string `json:"frame_url,omitempty"`
}

// Collection 返回 User Appearances 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("user_appearances")
}

// ListAppearanceByUserID finds the available appearances by user_id and type
func ListAppearanceByUserID(userID int64, appearanceType int) ([]UserAppearance, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"user_id":    userID,
		"status":     bson.M{"$gt": StatusPending},
		"start_time": bson.M{"$lte": goutil.TimeNow().Unix()},
		"type":       appearanceType,
	}

	sort := bson.D{bson.E{Key: "start_time", Value: -1}}
	opts := options.Find().SetSort(sort)
	appearances := make([]UserAppearance, 0)
	cur, err := Collection().Find(ctx, filter, opts)
	if err != nil {
		return appearances, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &appearances)
	if err != nil {
		return nil, err
	}

	return appearances, nil
}

// NewUserAppearance convert appearance to userappearance without start time and expire time
func NewUserAppearance(userID int64, a *appearance.Appearance) *UserAppearance {
	ua := &UserAppearance{
		AppearanceID:   a.ID,
		UserID:         userID,
		Name:           a.Name,
		Type:           a.Type,
		From:           a.From,
		Intro:          a.Intro,
		Icon:           a.Icon,
		Effect:         a.Effect,
		WebEffect:      a.WebEffect,
		EffectDuration: a.EffectDuration,
		Image:          a.Image,
		ImageNew:       a.ImageNew,
		Frame:          a.Frame,
		TextColor:      a.TextColor,
		TextColorItem:  a.TextColorItem,
		WelcomeMessage: a.WelcomeMessage,
		EntryStyle:     a.EntryStyle,
		Resource:       a.Resource,
	}

	if a.MessageBar != nil {
		ua.MessageBar = &appearance.MessageBar{
			Message: a.MessageBar.Message,
			Image:   a.MessageBar.Image,
		}
	}

	return ua
}

// NewEntryBubble 从用户外观生成新的进场通知外观
func NewEntryBubble(ua *UserAppearance) *EntryBubble {
	entry := EntryBubble{
		ImageURL:       storage.ParseSchemeURL(ua.Image),
		WelcomeMessage: ua.WelcomeMessage,
		EntryStyle:     ua.EntryStyle,
	}
	entry.setDefaultWelcomeMessage()
	return &entry
}

// 检查欢迎信息，如果有空的部分则补上默认值
func (e *EntryBubble) setDefaultWelcomeMessage() {
	if e.WelcomeMessage == nil {
		e.WelcomeMessage = &appearance.WelcomeMessage{}
	}
	if e.WelcomeMessage.Text == "" {
		e.WelcomeMessage.Text = "欢迎进入本直播间"
	}
	if e.WelcomeMessage.Colors == "" {
		e.WelcomeMessage.Colors = "#FFFFFF"
	}
}

// NewGiftNotification 从用户外观生成新的礼物通知外观
func NewGiftNotification(ua *UserAppearance) *GiftNotification {
	return &GiftNotification{
		UsernameColor: ua.TextColorItem.Username,
		TextColor:     ua.TextColor,
		FrameURL:      storage.ParseSchemeURL(ua.Frame),
	}
}

// ParseSchemeURL parse url
func (ua *UserAppearance) ParseSchemeURL() {
	ua.Icon = storage.ParseSchemeURL(ua.Icon)
	ua.Effect = storage.ParseSchemeURL(ua.Effect)
	ua.WebEffect = storage.ParseSchemeURL(ua.WebEffect)
	ua.Image = storage.ParseSchemeURL(ua.Image)
	ua.ImageNew = storage.ParseSchemeURL(ua.ImageNew)
	ua.Frame = storage.ParseSchemeURL(ua.Frame)

	if ua.MessageBar != nil {
		ua.MessageBar.Image = storage.ParseSchemeURL(ua.MessageBar.Image)
	}
}

// IsActive 此外观是否生效中
func (ua *UserAppearance) IsActive(when time.Time) bool {
	whenUnix := when.Unix()
	if whenUnix < ua.StartTime {
		return false
	}
	if ua.ExpireTime != nil && whenUnix >= *ua.ExpireTime {
		return false
	}
	return true
}

// SetStatus set appearance status
func (ua *UserAppearance) SetStatus(status int, expireTime, startTime int64) {
	ua.Status = status
	ua.OwnStatus = OwnStatusUsed
	ua.SetTime(startTime, expireTime)
}

// SetTime 设置时间
func (ua *UserAppearance) SetTime(startTime, expireTime int64) {
	now := goutil.TimeNow().Unix()
	if startTime != 0 {
		ua.StartTime = startTime
	} else {
		ua.StartTime = now
	}

	ua.ExpireTime = &expireTime
	ua.ModifiedTime = now
}

// ClearCache clear user appearance cache
func ClearCache(userIDs ...int64) {
	if len(userIDs) == 0 {
		return
	}
	delKeys := make([]string, 0, len(userIDs)*2)
	for _, userID := range userIDs {
		delKeys = append(delKeys,
			keys.KeyUsersTitles1.Format(userID),
			keys.KeyUsersWornAppearancesSets1.Format(userID),
		)
	}
	err := service.Redis.Del(delKeys...).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// SortAppearances 用于排序外观物品
// 排序的详细情况请参考 https://info.missevan.com/pages/viewpage.action?pageId=28281330
// 排序规则:
// 1. 未过期的外观中状态为佩戴中的的放最前面;
// 2. 未过期外观越早过期越排在前边;
// 3. 已过期外观越晚过期越排在前边;
// 4. 过期时间相同的越新获得的排在越前面;
// 5. 使用中的称号按照称号的位置（Position）排序，位置值越大，越排在后面;
func SortAppearances(appearances []UserAppearance) {
	now := goutil.TimeNow().Unix()
	sort.SliceStable(appearances, func(i, j int) bool {
		expireNil1 := appearances[i].ExpireTime == nil
		expired1 := !expireNil1 && *appearances[i].ExpireTime <= now
		expireNil2 := appearances[j].ExpireTime == nil
		expired2 := !expireNil2 && *appearances[j].ExpireTime <= now

		// 其中一个过期，另一个不过期的情况
		if expired1 != expired2 {
			return !expired1
		}
		// 均过期的情况
		if expired1 {
			if *appearances[i].ExpireTime == *appearances[j].ExpireTime {
				return appearances[i].StartTime > appearances[j].StartTime
			}
			return *appearances[i].ExpireTime > *appearances[j].ExpireTime
		}
		if appearances[i].Status != appearances[j].Status {
			// 如果有佩戴的项目，优先置前
			if appearances[i].Status == StatusWorn {
				return true
			} else if appearances[j].Status == StatusWorn {
				return false
			}
		} else if appearances[i].Status == StatusWorn {
			// 当前只有称号有同时佩戴多个的情况，此处需要对多个佩戴中状态的外观根据 Position 排序
			return appearances[i].Position < appearances[j].Position
		}
		// 判断其中一个永久，另一个不是永久的情况
		if expireNil1 != expireNil2 {
			return !expireNil1
		}
		// 判断过期时间是否相等
		if expireNil1 || (*appearances[i].ExpireTime == *appearances[j].ExpireTime) {
			return appearances[i].StartTime > appearances[j].StartTime
		}

		return *appearances[i].ExpireTime <= *appearances[j].ExpireTime
	})
}

// FindValidAppearance finds a specific valid appearance based on appearance_id, user_id, and type
func FindValidAppearance(appearanceID, userID int64, appearanceType int) (*UserAppearance, error) {
	now := goutil.TimeNow()
	filter := appearance.SetValidTimeFilter(bson.M{
		"appearance_id": appearanceID,
		"type":          appearanceType,
		"user_id":       userID,
		"status":        bson.M{"$gt": StatusPending},
	}, now.Unix())
	item, err := FindOne(filter, nil)
	if err != nil {
		return nil, err
	}

	return item, nil
}

// FindWornAppearance finds the worn appearance of user by user_id
func FindWornAppearance(userID int64, appearanceType int) (*UserAppearance, error) {
	filter := appearance.SetValidTimeFilter(bson.M{
		"type":    appearanceType,
		"user_id": userID,
		"status":  StatusWorn,
	}, goutil.TimeNow().Unix())
	appearanceItem, err := FindOne(filter, nil)
	if err != nil {
		return nil, err
	}
	return appearanceItem, nil
}

// findUserWornAppearancesSets 从 Redis 缓存中查询用户佩戴的外观集
func findUserWornAppearancesSets(userID int64) (map[int][]*UserAppearance, error) {
	mAppearance := make(map[int][]*UserAppearance)
	if userID <= 0 {
		return mAppearance, nil
	}

	key := keys.KeyUsersWornAppearancesSets1.Format(userID)
	r, err := service.Redis.Get(key).Result() // TODO: 后续考虑移到 LRURedis
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}

	now := goutil.TimeNow()
	if r != "" {
		err = json.Unmarshal([]byte(r), &mAppearance)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		for uaType, uas := range mAppearance {
			filteredUas := make([]*UserAppearance, 0, len(uas))
			for _, ua := range uas {
				// 过滤过期的外观
				if ua.IsActive(now) {
					filteredUas = append(filteredUas, ua)
				}
			}
			mAppearance[uaType] = filteredUas
		}
		return mAppearance, nil
	}

	appearances, err := Find(appearance.SetValidTimeFilter(bson.M{
		"user_id": userID,
		"status":  StatusWorn,
	}, now.Unix()), nil)
	if err != nil {
		return nil, err
	}
	for i := range appearances {
		mAppearance[appearances[i].Type] = append(mAppearance[appearances[i].Type], appearances[i])
	}

	// 对称号进行排序
	sortBadges(mAppearance[appearance.TypeBadge])
	b, err := json.Marshal(mAppearance)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	err = service.Redis.Set(key, b, 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return mAppearance, err
}

// findUsersWornAppearancesSetsMap 从 Redis 缓存中查询用户佩戴的外观集
// return map[userID]map[appearanceType][]appearance
func findUsersWornAppearancesSetsMap(userIDs []int64) (map[int64]map[int][]*UserAppearance, error) {
	mAppearanceMap := make(map[int64]map[int][]*UserAppearance, len(userIDs))
	if len(userIDs) <= 0 {
		return mAppearanceMap, nil
	}

	for _, userID := range userIDs {
		// 用户默认外观数据为空 map
		mAppearanceMap[userID] = make(map[int][]*UserAppearance)
	}

	keyList := make([]string, len(userIDs))
	for i, userID := range userIDs {
		keyList[i] = keys.KeyUsersWornAppearancesSets1.Format(userID)
	}
	result, err := service.Redis.MGet(keyList...).Result()
	if err != nil {
		logger.Error(err)
		return mAppearanceMap, nil
	}

	now := goutil.TimeNow()
	noCacheUserIDs := make([]int64, 0, len(userIDs))
	for i, r := range result {
		if r == nil {
			noCacheUserIDs = append(noCacheUserIDs, userIDs[i])
			continue
		}
		mAppearance := make(map[int][]*UserAppearance)
		mAppearanceString := r.(string)
		if mAppearanceString == "" {
			mAppearanceMap[userIDs[i]] = mAppearance
			continue
		}
		err = json.Unmarshal([]byte(mAppearanceString), &mAppearance)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		for uaType, uas := range mAppearance {
			filteredUas := make([]*UserAppearance, 0, len(uas))
			for _, ua := range uas {
				// 过滤过期的外观
				if ua.IsActive(now) {
					filteredUas = append(filteredUas, ua)
				}
			}
			mAppearance[uaType] = filteredUas
		}
		mAppearanceMap[userIDs[i]] = mAppearance
	}

	if len(noCacheUserIDs) == 0 {
		return mAppearanceMap, nil
	}

	appearances, err := Find(appearance.SetValidTimeFilter(bson.M{
		"user_id": bson.M{"$in": noCacheUserIDs},
		"status":  StatusWorn,
	}, now.Unix()), nil)
	if err != nil {
		return nil, err
	}
	newCacheMap := make(map[int64]map[int][]*UserAppearance, len(noCacheUserIDs))
	for i := range appearances {
		if newCacheMap[appearances[i].UserID] == nil {
			newCacheMap[appearances[i].UserID] = make(map[int][]*UserAppearance)
		}
		newCacheMap[appearances[i].UserID][appearances[i].Type] = append(newCacheMap[appearances[i].UserID][appearances[i].Type], appearances[i])
	}

	newCaches := make(map[string]string, len(noCacheUserIDs))
	for _, userID := range noCacheUserIDs {
		redisKey := keys.KeyUsersWornAppearancesSets1.Format(userID)
		mAppearance, ok := newCacheMap[userID]
		if !ok {
			// 查找不到用户外观时，缓存中存 `{}`
			mAppearance = make(map[int][]*UserAppearance)
			newCacheMap[userID] = mAppearance
		}
		// 对称号进行排序
		sortBadges(mAppearance[appearance.TypeBadge])
		b, err := json.Marshal(mAppearance)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		newCaches[redisKey] = string(b)
	}
	// 新增缓存
	pipe := service.Redis.Pipeline()
	for key, value := range newCaches {
		pipe.Set(key, value, 5*time.Minute)
	}
	_, err = pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	for userID, appearanceMap := range newCacheMap {
		mAppearanceMap[userID] = appearanceMap
	}

	return mAppearanceMap, err
}

// Find finds appearances
func Find(filter interface{}, findOpt *options.FindOptions) ([]*UserAppearance, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col, err := Collection().Find(ctx, filter, findOpt)
	if err != nil {
		return nil, err
	}
	defer col.Close(ctx)
	var res []*UserAppearance
	err = col.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindOne finds appearances
func FindOne(filter interface{}, findOpt *options.FindOneOptions) (*UserAppearance, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var res UserAppearance
	var err error
	if findOpt != nil {
		err = Collection().FindOne(ctx, filter, findOpt).Decode(&res)
	} else {
		err = Collection().FindOne(ctx, filter).Decode(&res)
	}
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &res, nil
}

// FindVehicle finds vehicle, type is restricted with TypeVehicle
func FindVehicle(subFilter interface{}) ([]*Vehicle, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"$and": bson.A{
		bson.M{"type": appearance.TypeVehicle},
		subFilter,
	}}
	col, err := Collection().Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer col.Close(ctx)
	var appearanceItems []*UserAppearance
	err = col.All(ctx, &appearanceItems)
	if err != nil {
		return nil, err
	}
	res := make([]*Vehicle, 0)
	for i := range appearanceItems {
		item := NewVehicle(appearanceItems[i])
		res = append(res, item)
	}
	return res, nil
}

// FindWornVehicle finds a worn vehicle
func FindWornVehicle(userID int64) (*Vehicle, error) {
	appearanceItem, err := FindWornAppearance(userID, appearance.TypeVehicle)
	if err != nil || appearanceItem == nil {
		return nil, err
	}
	vehicle := NewVehicle(appearanceItem)
	return vehicle, nil
}

// FindEntryBubbles finds the entry bubbles for the given users
func FindEntryBubbles(userIDs []int64) []*UserAppearance {
	nowUnix := goutil.TimeNow().Unix()
	entryBubbles, err := Find(appearance.SetValidTimeFilter(bson.M{
		"user_id": bson.M{"$in": userIDs},
		"status":  StatusWorn,
		"type":    appearance.TypeEntryBubble,
	}, nowUnix), nil)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	return entryBubbles
}

// UserHasNewAppearance whether the user has new appearance(s) (noble appearance excluded)
func UserHasNewAppearance(userID int64) (bool, error) {
	filter := appearance.SetValidTimeFilter(
		bson.M{
			"user_id":    userID,
			"own_status": OwnStatusNew,
			"from":       bson.M{"$nin": appearance.VipFromList()},
		}, goutil.TimeNow().Unix())
	uaItem, err := FindOne(filter, nil)
	return uaItem != nil, err
}

// NewVehicle converts an appearance to new vehicle
func NewVehicle(a *UserAppearance) *Vehicle {
	if a.Type != appearance.TypeVehicle {
		panic("appearance is not vehicle")
	}

	v := new(Vehicle)

	v.UserID = a.UserID
	v.VehicleID = a.AppearanceID
	v.VehicleName = a.Name

	v.IconURL = storage.ParseSchemeURLs(a.Icon)
	v.Effect = storage.ParseSchemeURLs(a.Effect)
	v.WebEffect = storage.ParseSchemeURLs(a.WebEffect)
	v.EffectDuration = a.EffectDuration

	v.MessageBar = new(appearance.MessageBar)
	v.MessageBar.Message = a.MessageBar.Message
	v.MessageBar.ImageURL = storage.ParseSchemeURL(a.MessageBar.Image)

	v.Message = a.MessageBar.Message
	v.Bubble = new(bubble.Simple)
	v.Bubble.Type = bubble.TypeStrVehicle
	v.Bubble.ImageURL = v.MessageBar.ImageURL
	return v
}

// NewMessageBubble new message bubble
func NewMessageBubble(ua *UserAppearance) *bubble.Simple {
	if ua.Type != appearance.TypeMessageBubble {
		panic("appearance is not message bubble")
	}
	simple := &bubble.Simple{
		Type:      bubble.TypeStrMessage,
		ImageURL:  storage.ParseSchemeURL(ua.Image),
		TextColor: ua.TextColor,
	}
	if ua.Frame != "" {
		simple.FrameURL = storage.ParseSchemeURL(ua.Frame)
	}
	return simple
}

const (
	vehicleUsernameMaxWidth = 12 // 座驾支持完整显示的用户名宽度
	usernameTrimWidth       = 10 // 座驾不支持完整显示时截取的宽度
)

// FormatMessage 格式化气泡消息
func (v *Vehicle) FormatMessage(username string) {
	u := util.UTF8SubStrByWidth(username, vehicleUsernameMaxWidth)
	if u != username {
		// 用户名太长
		u = util.UTF8SubStrByWidth(username, usernameTrimWidth) + "..."
	}

	v.MessageBar.Message = goutil.FormatMessage(v.MessageBar.Message, map[string]string{
		"username":        html.EscapeString(u),
		"vehicle_name":    html.EscapeString(v.VehicleName),
		"normal_color":    html.EscapeString(v.MessageBar.NormalColor),
		"highlight_color": html.EscapeString(v.MessageBar.HighlightColor),
	})
	v.Message = v.MessageBar.Message
	// 不向用户返回颜色信息
	v.MessageBar.NormalColor = ""
	v.MessageBar.HighlightColor = ""
}

// getAppearanceFromPriority returns the priority level of an appearance source
// Higher value means higher priority
func getAppearanceFromPriority(from int) int {
	switch from {
	case appearance.FromHighness:
		return 3
	case appearance.FromBlackCard:
		return 2
	case appearance.FromNoble:
		return 1
	default:
		return 0
	}
}

// ShouldAutoWear determines if an appearance should be automatically worn based on priority
// using a pre-fetched map of worn appearances
// Returns true if it should be worn, false otherwise
func ShouldAutoWear(wornAppearance *UserAppearance, newAppearance *UserAppearance) bool {
	if wornAppearance == nil {
		return true
	}

	currentPriority := getAppearanceFromPriority(wornAppearance.From)
	newPriority := getAppearanceFromPriority(newAppearance.From)

	return newPriority >= currentPriority
}

// 基于持续时间创建/更新用户外观的 mongodb 事务项
// 具体规则参见：https://info.missevan.com/pages/viewpage.action?pageId=36282181
// wear 参数可控制该外观的佩戴状态，但不会自动取消同类型其他外观的佩戴状态，需外部控制同类型外观的佩戴状态
func updateUserAppearanceWithDuration(userID, duration, expireTime int64, oldUa *UserAppearance, aItem *appearance.Appearance, wear bool) mongo.WriteModel {
	if expireTime == 0 && duration == 0 {
		panic("expire_time and duration shouldn't be 0 at the same time")
	}
	// 初始化
	now := goutil.TimeNow()
	pendingExpireTime := int64(0)
	if expireTime == unlimitedTime || duration == unlimitedTime {
		pendingExpireTime = unlimitedTime
	} else if expireTime != 0 {
		pendingExpireTime = expireTime
	} else if duration != 0 {
		pendingExpireTime = now.Unix() + duration
	}

	newUa := NewUserAppearance(userID, aItem)
	newUa.UserID = userID
	newUa.ModifiedTime = now.Unix()
	if wear {
		// 设置自动佩戴
		newUa.Status = StatusWorn
	} else {
		newUa.Status = StatusOwned
	}

	// NOTICE: 此处开区间直接赋值，在列表接口中展示时会减 1 秒
	newExpireTime := func(expTime int64) *int64 {
		// 如果下发的是无限时间的外观模版，并且持续时间为 -1
		if aItem.ExpireTime == nil && expTime == unlimitedTime {
			return nil
		}
		// 如果叠加时间超出限制的，以及发放时长为 -1 的（取模版中的最大值/无限）
		if aItem.ExpireTime != nil && (expTime >= *aItem.ExpireTime || expTime == unlimitedTime) {
			return aItem.ExpireTime
		}

		return &expTime
	}

	if oldUa == nil {
		newUa.StartTime = now.Unix()
		newUa.OwnStatus = OwnStatusNew
		// 现有逻辑下的身份铭牌是自动佩戴，用户在外观中心看不到，所以每次下发新的身份铭牌都把状态置为已使用
		if newUa.Type == appearance.TypeIdentityBadge {
			newUa.OwnStatus = OwnStatusUsed
		}
		newUa.ExpireTime = newExpireTime(pendingExpireTime)
		return mongo.NewInsertOneModel().SetDocument(newUa)
	}

	updateDocument := mongo.NewUpdateOneModel().SetFilter(bson.M{"_id": oldUa.OID})
	newUa.StartTime = oldUa.StartTime
	newUa.OwnStatus = oldUa.OwnStatus
	// 对于已经持有永久外观的，duration > 0 不做处理，expire_time > 0 则会直接设定为 expire_time
	if oldUa.ExpireTime == nil {
		if expireTime > 0 {
			newUa.ExpireTime = &expireTime
			return updateDocument.SetUpdate(bson.M{"$set": newUa})
		}

		return nil
	}
	if *oldUa.ExpireTime < now.Unix() {
		// 如果已经持有外观但是持有的外观已经过期
		newUa.StartTime = now.Unix()
		newUa.OwnStatus = OwnStatusNew
		newUa.ExpireTime = newExpireTime(pendingExpireTime)
		return updateDocument.SetUpdate(bson.M{"$set": newUa})
	}

	if !wear {
		// 不更新外观佩戴状态
		newUa.Status = 0
	}

	// 已经持有外观，包括但不限于叠加时间超出限制/过期时间超过上限的
	if duration > 0 {
		pendingExpireTime = *oldUa.ExpireTime + duration
	}

	newUa.ExpireTime = newExpireTime(pendingExpireTime)
	return updateDocument.SetUpdate(bson.M{"$set": newUa})
}

// AddAppearance adds an appearance to user
// NOTICE: 不支持贵族外观
func AddAppearance(userID, duration, expireTime int64, aItem *appearance.Appearance) error {
	// 暂时不需支持自动佩戴功能
	return AddAppearances(userID, duration, expireTime, []*appearance.Appearance{aItem}, false)
}

// AddAppearances adds appearances to user
// NOTICE: 不支持贵族外观
// 使用 wear 启用自动佩戴的话，为用户重复发放延长/刷新持续时间时也会自动佩戴
func AddAppearances(userID, duration, expireTime int64, aItems []*appearance.Appearance, wear bool) error {
	if len(aItems) == 0 {
		return nil
	}

	updates := make([]mongo.WriteModel, 0, len(aItems)+1)
	if wear {
		models := newTakeOffAppearancesUpdates(userID, aItems)
		if len(models) > 0 {
			updates = append(updates, models...)
		}
	}

	appearanceIDs := make([]int64, 0, len(aItems))
	for _, aItem := range aItems {
		appearanceIDs = append(appearanceIDs, aItem.ID)
	}
	filter := bson.M{
		"user_id":       userID,
		"appearance_id": bson.M{"$in": appearanceIDs},
	}
	userAppearances, err := Find(filter, nil)
	if err != nil {
		return err
	}
	uaMap := goutil.ToMap(userAppearances, "AppearanceID").(map[int64]*UserAppearance)

	for _, aItem := range aItems {
		oldUa := uaMap[aItem.ID]
		update := updateUserAppearanceWithDuration(userID, duration, expireTime, oldUa, aItem, wear)
		if update != nil {
			updates = append(updates, update)
		}
	}

	if len(updates) > 0 {
		// 保证同一用户的外观操作会同时成功或失败
		err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
			_, err = Collection().BulkWrite(ctx, updates, options.BulkWrite().SetOrdered(true))
			return err
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// BatchAddAppearance adds an appearance to multiple users
// 使用 wear 启用自动佩戴的话，为用户重复发放延长/刷新持续时间时也会自动佩戴
func BatchAddAppearance(userIDs []int64, duration, expireTime int64, aItem *appearance.Appearance, wear bool) error {
	return BatchAddAppearances(userIDs, duration, expireTime, []*appearance.Appearance{aItem}, wear)
}

// BatchAddAppearances adds appearances to multiple users
// 使用 wear 启用自动佩戴的话，为用户重复发放延长/刷新持续时间时也会自动佩戴
func BatchAddAppearances(userIDs []int64, duration, expireTime int64, aItems []*appearance.Appearance, wear bool) error {
	if len(userIDs) == 0 || len(aItems) == 0 {
		return nil
	}
	duplicates := util.FindInt64Duplicates(userIDs)
	if len(duplicates) > 0 {
		return errors.New("duplicates found in userIDs, abort")
	}

	updates := make([]mongo.WriteModel, 0, len(userIDs)*len(aItems)+1)
	if wear {
		// 佩戴需将同类型的已佩戴物品先卸下
		update := newBatchTakeOffAppearancesUpdate(userIDs, aItems)
		if update != nil {
			updates = append(updates, update)
		}
	}

	// 查找旧用户外观
	appearanceIDs := make([]int64, 0, len(aItems))
	for _, aItem := range aItems {
		appearanceIDs = append(appearanceIDs, aItem.ID)
	}
	filter := bson.M{
		"user_id":       bson.M{"$in": userIDs},
		"appearance_id": bson.M{"$in": appearanceIDs},
	}
	userAppearances, err := Find(filter, nil)
	if err != nil {
		return err
	}

	m := make(map[int64]map[int64]*UserAppearance)
	for _, ua := range userAppearances {
		mUserAppearance := m[ua.AppearanceID]
		if mUserAppearance == nil {
			mUserAppearance = make(map[int64]*UserAppearance)
			m[ua.AppearanceID] = mUserAppearance
		}
		mUserAppearance[ua.UserID] = ua
	}

	// 添加、更新外观
	for _, aItem := range aItems {
		mUserAppearance := m[aItem.ID]
		for i := range userIDs {
			var oldUa *UserAppearance
			if mUserAppearance != nil {
				oldUa = mUserAppearance[userIDs[i]]
			}
			update := updateUserAppearanceWithDuration(userIDs[i], duration, expireTime, oldUa, aItem, wear)
			if update != nil {
				updates = append(updates, update)
			}
		}
	}

	if len(updates) > 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		// REVIEW: 可能出现同一用户部分外观发送成功而部分失败的情况
		_, err = Collection().BulkWrite(ctx, updates, options.BulkWrite().SetOrdered(true))
		if err != nil {
			return err
		}
	}
	return nil
}

// NewTakeOffAppearancesUpdateByType 卸下用户佩戴中的特定类型外观
func NewTakeOffAppearancesUpdateByType(userID int64, aType int) mongo.WriteModel {
	return newTakeOffUpdate(bson.M{
		"user_id": userID,
		"type":    aType,
		"status":  StatusWorn,
	})
}

func newTakeOffUpdate(filter bson.M) mongo.WriteModel {
	return mongo.NewUpdateManyModel().
		SetFilter(filter).
		SetUpdate(bson.M{
			"$set": bson.M{
				"status":        StatusOwned,
				"modified_time": goutil.TimeNow().Unix(),
			},
			"$unset": bson.M{
				// 去除外观的显示位置参数，防止再次佩戴时残留上次的位置参数
				"position": 0,
			},
		})
}

// NewTakeOffAppearancesUpdate 卸下用户佩戴中的同类型外观
func NewTakeOffAppearancesUpdate(userID int64, aItems []*appearance.Appearance) mongo.WriteModel {
	return newBatchTakeOffAppearancesUpdate([]int64{userID}, aItems)
}

// newTakeOffAppearancesUpdates 按照不同规则卸下佩戴中的同类型外观
func newTakeOffAppearancesUpdates(userID int64, aItems []*appearance.Appearance) []mongo.WriteModel {
	updates := make([]mongo.WriteModel, 0, len(aItems))
	// 根据不同的取下规则对外观进行分组
	// limitItems 取下规则：每种类型保留最多一个，如果有超出的，则留下最晚佩戴的
	limitItems := make([]*appearance.Appearance, 0, len(aItems))
	// sameTypeItems 取下规则：卸下所有用户佩戴中的同类型外观
	sameTypeItems := make([]*appearance.Appearance, 0, len(aItems))
	for _, item := range aItems {
		switch item.Type {
		case appearance.TypeIdentityBadge:
			limitItems = append(limitItems, item)
		default:
			sameTypeItems = append(sameTypeItems, item)
		}
	}

	// 处理 limitItems
	if len(limitItems) > 0 {
		models := takeOffRetailOne(userID, limitItems)
		if len(models) > 0 {
			updates = append(updates, models...)
		}
	}
	// 处理 sameTypeItems
	if len(sameTypeItems) > 0 {
		update := NewTakeOffAppearancesUpdate(userID, sameTypeItems)
		if update != nil {
			updates = append(updates, update)
		}
	}

	return updates
}

// takeOffRetailOne 将同类型的外观类型只保留最早佩戴的一个，其余的都卸下
// NOTICE: 目前仅用于身份铭牌
func takeOffRetailOne(userID int64, items []*appearance.Appearance) []mongo.WriteModel {
	updates := make([]mongo.WriteModel, 0, len(items))
	types := goutil.SliceMap(items, func(item *appearance.Appearance) int {
		return item.Type
	})
	types = sets.Uniq(types)
	uaSets, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return nil
	}
	// 对每个种类进行处理
	for _, t := range types {
		switch t {
		case appearance.TypeIdentityBadge:
			// 如果用户已经佩戴了超过限制数量的身份铭牌，需要将最早佩戴的卸下
			if len(uaSets[t]) >= MaxWearingIdentityBadgesNum {
				userAppearances := uaSets[t]
				// 按照佩戴时间升序排序
				sort.Slice(userAppearances, func(i, j int) bool {
					return userAppearances[i].StartTime < userAppearances[j].StartTime
				})
				// 卸下最早佩戴的铭牌，留下 userAppearances 中除最后一个元素外的所有元素
				updates = append(updates, newBatchTakeOffAppearancesUpdateByID(userID, userAppearances[:len(userAppearances)-1]))
			}
		default:
			// PASS
		}
	}
	return updates
}

func newBatchTakeOffAppearancesUpdate(userIDs []int64, aItems []*appearance.Appearance) mongo.WriteModel {
	if len(aItems) == 0 || len(userIDs) == 0 {
		return nil
	}

	types := make([]int, 0, len(aItems))
	for _, aItem := range aItems {
		types = append(types, aItem.Type)
	}
	filter := bson.M{
		"user_id": bson.M{
			"$in": userIDs,
		},
		"status": StatusWorn,
		"type": bson.M{
			"$in": sets.Uniq(types),
		},
	}
	return newTakeOffUpdate(filter)
}

// FindCardFrame 获取用户名片框
func FindCardFrame(userID int64) (*UserAppearance, error) {
	uaSets, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return nil, err
	}
	uas := uaSets[appearance.TypeCardFrame]
	if len(uas) == 0 {
		return nil, nil
	}
	// 默认取第一位
	cardFrame := uas[0]
	cardFrame.ImageNew = storage.ParseSchemeURL(cardFrame.ImageNew)
	if cardFrame.TextColorItem == nil {
		cardFrame.TextColorItem = new(appearance.TextColorItem)
	}
	if cardFrame.TextColorItem.Username == "" {
		cardFrame.TextColorItem.Username = config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Username
	}
	if cardFrame.TextColorItem.Introduction == "" {
		cardFrame.TextColorItem.Introduction = config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Introduction
	}
	if cardFrame.TextColorItem.ReportAndManage == "" {
		cardFrame.TextColorItem.ReportAndManage = config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.ReportAndManage
	}
	return cardFrame, err
}

// FindMessageBubble 查询用户佩戴中的气泡框
func FindMessageBubble(userID int64) (*bubble.Simple, error) {
	uaSets, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return nil, err
	}
	if uas, ok := uaSets[appearance.TypeMessageBubble]; ok && len(uas) > 0 {
		// 默认取第一位
		return NewMessageBubble(uas[0]), nil
	}
	return nil, nil
}

// FindBadges 查询用户佩戴中的称号
// NOTICE: 此处不过滤数量，会返回查找到的佩戴中的所有称号
func FindBadges(userID int64) ([]*UserAppearance, error) {
	uaSets, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return nil, err
	}

	return uaSets[appearance.TypeBadge], nil
}

// FindWornIdentityBadges 查询用户佩戴中的身份铭牌
func FindWornIdentityBadges(userID int64) ([]*UserAppearance, error) {
	uaSets, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return nil, err
	}
	return uaSets[appearance.TypeIdentityBadge], nil
}

// newBatchTakeOffAppearancesUpdateByID 按照 ID 卸下用户佩戴中的外观
func newBatchTakeOffAppearancesUpdateByID(userID int64, appearances []*UserAppearance) mongo.WriteModel {
	ids := goutil.SliceMap(appearances, func(appearance *UserAppearance) int64 {
		return appearance.AppearanceID
	})
	return newTakeOffUpdate(bson.M{
		"user_id":       userID,
		"appearance_id": bson.M{"$in": ids},
	})
}

func sortBadges(badges []*UserAppearance) {
	sort.Slice(badges, func(i, j int) bool {
		return badges[i].Position < badges[j].Position
	})
}

// AssignBadgesPosition 分配称号的位置
func AssignBadgesPosition(badges []*UserAppearance) {
	for i := range badges {
		badges[i].Position = i
	}
}

// FindUserAppearances 查询用户佩戴中的称号、头像框、座驾、进场气泡
func FindUserAppearances(userID int64) (map[int][]*UserAppearance, error) {
	userAppearances := make(map[int][]*UserAppearance)
	uaSets, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return nil, err
	}
	if uas := uaSets[appearance.TypeAvatarFrame]; len(uas) > 0 {
		// 默认取第一位
		userAppearances[appearance.TypeAvatarFrame] = []*UserAppearance{uas[0]}
	}
	if uas := uaSets[appearance.TypeBadge]; len(uas) > 0 {
		if len(uas) > MaxWearingBadgesNum {
			uas = uas[:MaxWearingBadgesNum]
		}
		for _, v := range uas {
			v.Image = storage.ParseSchemeURL(v.Image)
		}

		userAppearances[appearance.TypeBadge] = uas
	}
	if uas := uaSets[appearance.TypeVehicle]; len(uas) > 0 {
		// 默认取第一位
		userAppearances[appearance.TypeVehicle] = []*UserAppearance{uas[0]}
	}
	if uas := uaSets[appearance.TypeEntryBubble]; len(uas) > 0 {
		// 默认取第一位
		userAppearances[appearance.TypeEntryBubble] = []*UserAppearance{uas[0]}
	}
	if uas := uaSets[appearance.TypeIdentityBadge]; len(uas) > 0 {
		if len(uas) > MaxWearingIdentityBadgesNum {
			uas = uas[:MaxWearingIdentityBadgesNum]
		}
		userAppearances[appearance.TypeIdentityBadge] = uas
	}

	return userAppearances, nil
}

// FindUsersAppearancesMap 查询用户佩戴中的称号、头像框、座驾、进场气泡
// return map[userID]map[appearanceType][]appearance
func FindUsersAppearancesMap(userIDs []int64) (map[int64]map[int][]*UserAppearance, error) {
	// 去重
	userIDs = util.Uniq(userIDs)
	uaSetsMap, err := findUsersWornAppearancesSetsMap(userIDs)
	if err != nil {
		return nil, err
	}

	userAppearancesMap := make(map[int64]map[int][]*UserAppearance, len(uaSetsMap))
	for userID, uaSets := range uaSetsMap {
		if userAppearancesMap[userID] == nil {
			userAppearancesMap[userID] = make(map[int][]*UserAppearance)
		}
		if uas := uaSets[appearance.TypeAvatarFrame]; len(uas) > 0 {
			// 默认取第一位
			userAppearancesMap[userID][appearance.TypeAvatarFrame] = []*UserAppearance{uas[0]}
		}
		if uas := uaSets[appearance.TypeBadge]; len(uas) > 0 {
			if len(uas) > MaxWearingBadgesNum {
				// 如果查询到的已佩戴称号数量超出上限，则自动裁切到上限
				uas = uas[:MaxWearingBadgesNum]
			}
			for _, v := range uas {
				v.Image = storage.ParseSchemeURL(v.Image)
			}
			userAppearancesMap[userID][appearance.TypeBadge] = uas
		}
		if uas := uaSets[appearance.TypeVehicle]; len(uas) > 0 {
			// 默认取第一位
			userAppearancesMap[userID][appearance.TypeVehicle] = []*UserAppearance{uas[0]}
		}
		if uas := uaSets[appearance.TypeEntryBubble]; len(uas) > 0 {
			// 默认取第一位
			userAppearancesMap[userID][appearance.TypeEntryBubble] = []*UserAppearance{uas[0]}
		}
		if uas := uaSets[appearance.TypeIdentityBadge]; len(uas) > 0 {
			if len(uas) > MaxWearingIdentityBadgesNum {
				uas = uas[:MaxWearingIdentityBadgesNum]
			}
			userAppearancesMap[userID][appearance.TypeIdentityBadge] = uas
		}
	}

	return userAppearancesMap, nil
}

// AddNobleAppearances 添加贵族外观
// expireTime 过期时间，单位：秒
func AddNobleAppearances(userID, expireTime int64, level int, isRegistration bool) error {
	nowUnix := goutil.TimeNow().Unix()
	if !isRegistration {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		res, err := Collection().UpdateMany(ctx,
			bson.M{
				"user_id": userID,
				"from":    appearance.FromNoble,
			},
			bson.M{
				"$set": bson.M{
					"expire_time":   expireTime,
					"modified_time": nowUnix,
				},
			},
		)
		if err != nil {
			return err
		}
		// 如果没有更新到数据，说明可能已修改过
		if res.ModifiedCount == 0 {
			return nil
		}
		ClearCache(userID)
		return nil
	}

	appearances, err := appearance.AllNobleAppearances(level)
	if err != nil {
		return err
	}
	if len(appearances) == 0 {
		return nil
	}

	// 批量获取用户当前佩戴的外观
	wornAppearancesByType, err := FindUserAppearances(userID)
	if err != nil {
		return err
	}

	updates := make([]mongo.WriteModel, 0, 2*len(appearances)+1)
	// NOTICE: 删除所有贵族外观，这里会删除所有普通贵族的外观，普通贵族外观会在后续逻辑中重新添加
	updates = append(updates, mongo.NewDeleteManyModel().SetFilter(bson.M{
		"user_id": userID,
		"from":    appearance.FromNoble,
	}))
	for _, a := range appearances {
		newAppearance := NewUserAppearance(userID, a)
		newAppearance.SetStatus(StatusWorn, expireTime, 0)
		var wornAppearance *UserAppearance
		if wornAppearances, exists := wornAppearancesByType[a.Type]; exists && len(wornAppearances) > 0 {
			wornAppearance = wornAppearances[0]
		}
		if wornAppearance != nil && !ShouldAutoWear(wornAppearance, newAppearance) {
			continue
		}
		updates = append(
			updates,
			// 设定当前类型对应的其他佩戴中的外观为持有状态
			mongo.NewUpdateManyModel().SetFilter(bson.M{
				"user_id": userID,
				"type":    a.Type,
				"status":  StatusWorn,
			}).SetUpdate(bson.M{
				"$set": bson.M{
					"status":        StatusOwned,
					"modified_time": nowUnix,
				},
			}),
			// 佩戴新的贵族外观
			mongo.NewInsertOneModel().SetDocument(newAppearance),
		)
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = Collection().BulkWrite(ctx, updates)
	if err != nil {
		return err
	}
	ClearCache(userID)
	return nil
}

// AddHighnessAppearances 添加上神贵族外观
func AddHighnessAppearances(userID, expireTime int64, isRegistration bool) error {
	now := goutil.TimeNow().Unix()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	if !isRegistration {
		res, err := Collection().UpdateMany(ctx,
			bson.M{
				"user_id": userID,
				"from":    appearance.FromHighness,
			},
			bson.M{
				"$set": bson.M{
					"expire_time":   expireTime,
					"modified_time": now,
				},
			})
		if err != nil || res.ModifiedCount == 0 {
			return err
		}
		ClearCache(userID)
		return nil
	}

	highnessAppearances, err := appearance.AllHighnessAppearances()
	if err != nil {
		return err
	}
	if len(highnessAppearances) == 0 {
		return nil
	}
	updates := make([]mongo.WriteModel, 0, 2*len(highnessAppearances)+1)
	// 清空并新建
	updates = append(updates, mongo.NewDeleteManyModel().SetFilter(bson.M{
		"user_id": userID,
		"from": bson.M{
			"$in": appearance.VipFromList(),
		},
	}))
	// 卸下用户当前佩戴的进场通知（无论来源），避免与上神默认进场通知冲突
	updates = append(updates, mongo.NewUpdateManyModel().SetFilter(bson.M{
		"user_id": userID,
		"type":    appearance.TypeEntryBubble,
		"status":  StatusWorn,
	}).SetUpdate(bson.M{
		"$set": bson.M{
			"status":        StatusOwned,
			"modified_time": now,
		},
	}))
	for i := range highnessAppearances {
		// 上神拥有最高优先级，总是佩戴（不需要检查）
		// 设定当前类型对应的其他佩戴中的外观为持有状态
		updates = append(updates, mongo.NewUpdateManyModel().SetFilter(bson.M{
			"user_id": userID,
			"type":    highnessAppearances[i].Type,
			"status":  StatusWorn,
		}).SetUpdate(bson.M{
			"$set": bson.M{
				"status":        StatusOwned,
				"modified_time": now,
			},
		}))
		item := NewUserAppearance(userID, highnessAppearances[i])
		item.SetStatus(StatusWorn, expireTime, 0)
		// 插入新的上神外观
		updates = append(updates, mongo.NewInsertOneModel().SetDocument(item))
	}

	_, err = Collection().BulkWrite(ctx, updates)
	if err != nil {
		return err
	}

	ClearCache(userID)
	return nil
}

// ClearUserVipAppearance 清空用户贵族外观
// NOTICE: 没有清空上神背包礼物
func ClearUserVipAppearance(userIDs []int64, vipTypes []int) {
	appearanceFromList := make([]int, 0, len(vipTypes))
	for _, vipType := range vipTypes {
		switch vipType {
		case vip.TypeLiveNoble:
			appearanceFromList = append(appearanceFromList, appearance.FromNoble)
		case vip.TypeLiveHighness:
			appearanceFromList = append(appearanceFromList, appearance.FromHighness)
		default:
			panic("直播贵族类型错误")
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{
		"user_id": bson.M{"$in": userIDs},
		"from":    bson.M{"$in": appearanceFromList},
	})
	if err != nil {
		logger.Errorf("移除贵族外观失败：%v，涉及用户 ID：%s", err, goutil.JoinInt64Array(userIDs, ","))
		// PASS
	}
	ClearCache(userIDs...)
}

// WearUserMaxNobleAppearances 佩戴当前用户最高等级的贵族外观
// NOTICE: 支持上神贵族、普通贵族、体验贵族
// TODO: 用户当前佩戴非贵族外观时，不佩戴当前最高等级的贵族外观
func WearUserMaxNobleAppearances(userIDs []int64) error {
	uvs, err := vip.LiveUserLevelByUserIDs(userIDs)
	if err != nil {
		return err
	}
	// 将用户按照贵族类型和贵族等级分组，首先为上神贵族、普通贵族、体验贵族分组，其次按照贵族等级统计用户贵族信息
	uvMap := make(map[int]map[int][]*vip.UserVip, 3) // map[vip_type]map[vip_level][]*vip.UserVip
	for _, uv := range uvs {
		_, ok := uvMap[uv.Type]
		if !ok {
			uvMap[uv.Type] = make(map[int][]*vip.UserVip, 8)
		}
		_, ok = uvMap[uv.Type][uv.Level]
		if !ok {
			uvMap[uv.Type][uv.Level] = make([]*vip.UserVip, 0)
		}
		uvMap[uv.Type][uv.Level] = append(uvMap[uv.Type][uv.Level], uv)
	}

	nowUnix := goutil.TimeNow().Unix()
	updates := make([]mongo.WriteModel, 0, 2*len(uvs)+1)
	// 清空并新建
	updates = append(updates, mongo.NewDeleteManyModel().SetFilter(bson.M{
		"user_id": bson.M{"$in": userIDs},
		"from": bson.M{
			"$in": appearance.VipFromList(),
		},
	}))
	// 按用户贵族等级分组顺序依次为其佩戴指定的外观
	for nobleType, nobleTypeMap := range uvMap {
		for nobleTypeLevel, nobleTypeLevelUserVips := range nobleTypeMap {
			var appearanceTypes []int
			if nobleType == vip.TypeLiveHighness {
				appearanceTypes = appearance.HighnessAppearanceTypes()
			} else {
				appearanceTypes = appearance.NobleAppearanceTypes(nobleTypeLevel)
			}
			// TODO: 挪到循环外一次性全部查询
			// 按照贵族类型和贵族等级以及贵族等级对应的外观类型获取对应的外观 ID
			appearanceIDs := make([]int64, 0, len(appearanceTypes))
			for _, t := range appearanceTypes {
				appearanceIDs = append(appearanceIDs, appearance.VipAppearanceID(nobleType, nobleTypeLevel, t))
			}
			// 查询当前贵族等级的所有外观
			appearances, err := appearance.Find(bson.M{"id": bson.M{"$in": appearanceIDs}}, nil)
			if err != nil {
				errUserIDs := make([]int64, 0, len(nobleTypeLevelUserVips))
				for _, uv := range nobleTypeLevelUserVips {
					errUserIDs = append(errUserIDs, uv.UserID)
				}
				logger.WithFields(logger.Fields{
					"appearance_ids": appearanceIDs,
					"user_ids":       errUserIDs,
				}).Error(err)
				// PASS
				continue
			}
			for _, appearance := range appearances {
				for _, uv := range nobleTypeLevelUserVips {
					// 设定当前类型对应的其他佩戴中的外观为持有状态
					updates = append(updates, mongo.NewUpdateManyModel().SetFilter(bson.M{
						"user_id": uv.UserID,
						"type":    appearance.Type,
						"status":  StatusWorn,
					}).SetUpdate(bson.M{
						"$set": bson.M{
							"status":        StatusOwned,
							"modified_time": nowUnix,
						},
					}))
					// 插入新的外观
					item := NewUserAppearance(uv.UserID, appearance)
					item.SetStatus(StatusWorn, uv.ExpireTime, 0)
					updates = append(updates, mongo.NewInsertOneModel().SetDocument(item))
				}
			}
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = Collection().BulkWrite(ctx, updates)
	if err != nil {
		return err
	}
	ClearCache(userIDs...)
	return nil
}

// AddNewBlackCardAppearances 添加黑卡外观
func AddNewBlackCardAppearances(userID int64, level int, expireTime int64) error {
	appearances, err := appearance.AllBlackCardAppearances(level)
	if err != nil {
		return err
	}

	// 批量获取用户当前佩戴的外观
	wornAppearancesByType, err := findUserWornAppearancesSets(userID)
	if err != nil {
		return err
	}

	// 判断用户是否为上神，若是则黑卡进场通知不自动佩戴
	isHighnessUser := false
	if uvMap, err := vip.UserVipInfos(userID, false, nil); err == nil {
		if uv := uvMap[vip.TypeLiveHighness]; uv != nil && uv.IsActive() {
			isHighnessUser = true
		}
	}

	now := goutil.TimeNow().Unix()
	updates := make([]mongo.WriteModel, 0, 2*len(appearances)+1)
	updates = append(updates, mongo.NewDeleteManyModel().SetFilter(bson.M{
		"user_id": userID,
		"from":    appearance.FromBlackCard,
	}))
	for _, a := range appearances {
		newAppearance := NewUserAppearance(userID, a)
		newAppearance.SetStatus(StatusWorn, expireTime, 0)
		var wornAppearance *UserAppearance
		if wornAppearances, exists := wornAppearancesByType[a.Type]; exists && len(wornAppearances) > 0 {
			wornAppearance = wornAppearances[0]
		}

		// 如果用户是上神且当前未佩戴任何进场通知，则不自动佩戴黑卡进场通知，保证客户端展示上神默认进场通知
		if a.Type == appearance.TypeEntryBubble && isHighnessUser {
			currentEntryBubbles := wornAppearancesByType[appearance.TypeEntryBubble]
			if len(currentEntryBubbles) == 0 {
				na := NewUserAppearance(userID, a)
				na.SetStatus(StatusOwned, expireTime, 0)
				updates = append(updates, mongo.NewInsertOneModel().SetDocument(na))
				continue
			}
		}

		// 如果是身份铭牌类型，检查数量限制并卸下超出限制的旧铭牌
		if a.Type == appearance.TypeIdentityBadge {
			// 现有逻辑下的身份铭牌是自动佩戴，用户在外观中心看不到，所以每次下发新的身份铭牌都把状态置为已使用
			newAppearance.OwnStatus = OwnStatusUsed
			// 获取用户当前佩戴的身份铭牌
			currentIdentityBadges := wornAppearancesByType[appearance.TypeIdentityBadge]
			nonBlackCardBadges := make([]*UserAppearance, 0, len(currentIdentityBadges))
			for _, badge := range currentIdentityBadges {
				if badge.From != appearance.FromBlackCard {
					nonBlackCardBadges = append(nonBlackCardBadges, badge)
				}
			}

			// 如果当前佩戴的身份铭牌数量已经达到或超过限制，需要先卸下最早佩戴的
			if len(nonBlackCardBadges)+1 >= MaxWearingIdentityBadgesNum {
				// 按照佩戴时间升序排序
				sort.Slice(nonBlackCardBadges, func(i, j int) bool {
					return nonBlackCardBadges[i].StartTime < nonBlackCardBadges[j].StartTime
				})
				// 计算需要卸下的数量，为新铭牌腾出空间
				needToRemoveCount := len(nonBlackCardBadges) - MaxWearingIdentityBadgesNum + 1
				if needToRemoveCount > 0 {
					toRemoveBadges := nonBlackCardBadges[:needToRemoveCount]
					updates = append(updates, newBatchTakeOffAppearancesUpdateByID(userID, toRemoveBadges))
				}
			}

			// 添加新的身份铭牌
			updates = append(updates, mongo.NewInsertOneModel().SetDocument(newAppearance))
			continue
		}

		if wornAppearance != nil && !ShouldAutoWear(wornAppearance, newAppearance) {
			continue
		}
		updates = append(
			updates,
			// 设定当前类型对应的其它外观为持有状态
			mongo.NewUpdateManyModel().
				SetFilter(bson.M{
					"user_id": userID,
					"type":    a.Type,
					"status":  StatusWorn,
				}).
				SetUpdate(bson.M{
					"$set": bson.M{
						"status":        StatusOwned,
						"modified_time": now,
					},
				}),
			// 佩戴新的黑卡外观
			mongo.NewInsertOneModel().SetDocument(newAppearance),
		)
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err = Collection().BulkWrite(ctx, updates)
	if err != nil {
		return err
	}

	ClearCache(userID)
	return nil
}

// RenewBlackCardAppearances 续期黑卡外观
func RenewBlackCardAppearances(userID int64, expireTime int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateMany(
		ctx,
		bson.M{
			"user_id": userID,
			"from":    appearance.FromBlackCard,
		},
		bson.M{
			"$set": bson.M{
				"expire_time":   expireTime,
				"modified_time": goutil.TimeNow().Unix(),
			},
		},
	)
	if err != nil {
		return err
	}

	ClearCache(userID)
	return nil
}

// UserBlackCardInfo 用户黑卡信息，用于清除黑卡外观，避免循环引用
type UserBlackCardInfo struct {
	UserID     int64
	Level      int
	ExpireTime int64
	StartTime  int64
}

// ClearBlackCardAppearances 清除用户的黑卡外观
func ClearBlackCardAppearances(userBlackCardMap map[int64]*UserBlackCardInfo) error {
	if len(userBlackCardMap) == 0 {
		return nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	userIDs := make([]int64, 0, len(userBlackCardMap))
	for userID := range userBlackCardMap {
		userIDs = append(userIDs, userID)
	}

	// 预先查询所有用户当前佩戴的外观，检查是否有佩戴黑卡外观
	wornAppearancesMap, err := findUsersWornAppearancesSetsMap(userIDs)
	if err != nil {
		return err
	}

	// 记录每个用户佩戴的黑卡外观类型
	usersWornBlackCardTypes := make(map[int64][]int)
	for userID, wornAppearances := range wornAppearancesMap {
		wornBlackCardTypes := make([]int, 0)
		for appearanceType, appearanceList := range wornAppearances {
			for _, ua := range appearanceList {
				if ua.From == appearance.FromBlackCard {
					wornBlackCardTypes = append(wornBlackCardTypes, appearanceType)
					break
				}
			}
		}
		if len(wornBlackCardTypes) > 0 {
			usersWornBlackCardTypes[userID] = wornBlackCardTypes
		}
	}

	operations := make([]mongo.WriteModel, 0)

	// 先删除所有用户的黑卡外观
	operations = append(operations, mongo.NewDeleteManyModel().SetFilter(bson.M{
		"user_id": bson.M{"$in": userIDs},
		"from":    appearance.FromBlackCard,
	}))

	// 根据新等级重新添加黑卡外观（仅对之前佩戴了黑卡外观的用户）
	// 第一步：收集所有需要的外观 ID
	needAppearanceIDs := make([]int64, 0)
	userAppearanceMapping := make(map[int64][]int64) // userID -> []appearanceID

	for userID, blackCardInfo := range userBlackCardMap {
		if blackCardInfo == nil || blackCardInfo.Level <= 0 {
			continue
		}

		// 获取用户之前佩戴的黑卡外观类型
		wornBlackCardTypes, hasWornBlackCard := usersWornBlackCardTypes[userID]
		if !hasWornBlackCard {
			continue
		}

		// 获取新等级对应的外观类型列表
		availableTypes := appearance.BlackCardAppearanceTypes(blackCardInfo.Level)
		userNeedAppearanceIDs := make([]int64, 0)

		for _, availableType := range availableTypes {
			// 检查这个外观类型是否是用户之前佩戴的
			if slices.Contains(wornBlackCardTypes, availableType) {
				appearanceID := appearance.BlackCardAppearanceID(blackCardInfo.Level, availableType)
				needAppearanceIDs = append(needAppearanceIDs, appearanceID)
				userNeedAppearanceIDs = append(userNeedAppearanceIDs, appearanceID)
			}
		}

		if len(userNeedAppearanceIDs) > 0 {
			userAppearanceMapping[userID] = userNeedAppearanceIDs
		}
	}

	// 第二步：批量查询外观模板
	var appearanceMap map[int64]*appearance.Appearance
	if len(needAppearanceIDs) > 0 {
		var err error
		appearanceMap, err = appearance.FindMapByIDs(needAppearanceIDs)
		if err != nil {
			return err
		}
	}

	// 第三步：创建用户外观记录
	for userID, appearanceIDs := range userAppearanceMapping {
		blackCardInfo := userBlackCardMap[userID]
		if blackCardInfo == nil {
			continue
		}

		for _, appearanceID := range appearanceIDs {
			if a, exists := appearanceMap[appearanceID]; exists {
				newAppearance := NewUserAppearance(userID, a)
				newAppearance.SetStatus(StatusWorn, blackCardInfo.ExpireTime, blackCardInfo.StartTime)
				operations = append(operations, mongo.NewInsertOneModel().SetDocument(newAppearance))
			}
		}
	}

	if len(operations) > 0 {
		_, err := Collection().BulkWrite(ctx, operations)
		if err != nil {
			return err
		}
	}

	// 清理缓存
	ClearCache(userIDs...)
	return nil
}
