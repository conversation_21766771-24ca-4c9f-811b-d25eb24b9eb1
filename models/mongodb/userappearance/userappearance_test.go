package userappearance

import (
	"context"
	"fmt"
	"math/rand"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(UserAppearance{}, "_id", "appearance_id", "name", "type", "status", "own_status", "user_id", "from", "intro",
		"icon", "effect", "web_effect", "effect_duration",
		"image", "image_new", "frame", "text_color", "text_color_item", "message_bar", "position", "welcome_message", "entry_style", "resource",
		"start_time", "expire_time", "modified_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(UserAppearance{}, "appearance_id", "name", "type", "status", "own_status", "user_id", "from", "intro",
		"icon", "effect", "web_effect", "effect_duration",
		"image", "image_new", "frame", "text_color", "text_color_item", "message_bar", "position", "welcome_message", "entry_style", "resource",
		"start_time", "expire_time", "modified_time")
	kc.Check(Vehicle{}, "icon_url", "effect_url", "web_effect_url", "effect_duration",
		"message", "bubble",
		"message_bar",
	)
	kc.Check(EntryBubble{}, "image_url", "entry_style", "welcome_message")
}

func TestListAppearanceByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	expTime := now.Add(time.Minute).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询座驾",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a2 := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询头像框",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a3 := &UserAppearance{
		Type:         appearance.TypeGiftNotification,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询送礼通知",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	col := Collection()
	_, err := col.UpdateOne(ctx,
		bson.M{"user_id": a.UserID, "appearance_id": a.AppearanceID},
		bson.M{"$set": a}, options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = col.UpdateOne(ctx,
		bson.M{"user_id": a2.UserID, "appearance_id": a2.AppearanceID},
		bson.M{"$set": a2}, options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = col.UpdateOne(ctx,
		bson.M{"user_id": a3.UserID, "appearance_id": a3.AppearanceID},
		bson.M{"$set": a3}, options.Update().SetUpsert(true))
	require.NoError(err)

	// 模拟查询
	filter := bson.M{
		"user_id":    a.UserID,
		"status":     bson.M{"$gt": StatusPending},
		"start_time": bson.M{"$lte": now.Unix()},
		"type":       appearance.TypeVehicle,
	}
	cur, err := Collection().Find(ctx, filter, options.Find())
	require.NoError(err)
	defer cur.Close(ctx)
	var findUserAppearances []UserAppearance
	err = cur.All(ctx, &findUserAppearances)
	require.NoError(err)

	appearances, err := ListAppearanceByUserID(a.UserID, appearance.TypeVehicle)
	require.NoError(err)
	SortAppearances(appearances)

	// 对比长度
	assert.Len(findUserAppearances, len(appearances))
	appearanceIDs := make([]int64, len(appearances))
	for i := range appearances {
		item := appearances[i]
		appearanceIDs[i] = item.AppearanceID
	}
	findUserAppearanceIDs := make([]int64, len(findUserAppearances))
	for i := range findUserAppearances {
		findUserAppearanceIDs[i] = findUserAppearances[i].AppearanceID
	}
	// 对比元素是否一致
	assert.ElementsMatch(appearanceIDs, findUserAppearanceIDs)

	// 类型筛选
	filter["type"] = appearance.TypeAvatarFrame
	cur, err = Collection().Find(ctx, filter, options.Find())
	require.NoError(err)
	defer cur.Close(ctx)
	err = cur.All(ctx, &findUserAppearances)
	require.NoError(err)

	appearances, err = ListAppearanceByUserID(a.UserID, appearance.TypeAvatarFrame)
	require.NoError(err)
	SortAppearances(appearances)

	// 对比长度
	assert.Len(findUserAppearances, len(appearances))
	appearanceIDs = make([]int64, len(appearances))
	for i := range appearances {
		appearanceIDs[i] = appearances[i].AppearanceID
	}
	findUserAppearanceIDs = make([]int64, len(findUserAppearances))
	for i := range findUserAppearances {
		findUserAppearanceIDs[i] = findUserAppearances[i].AppearanceID
	}
	// 对比元素是否一致
	assert.ElementsMatch(appearanceIDs, findUserAppearanceIDs)

	// 类型筛选
	filter["type"] = appearance.TypeGiftNotification
	cur, err = Collection().Find(ctx, filter, options.Find())
	require.NoError(err)
	defer cur.Close(ctx)
	err = cur.All(ctx, &findUserAppearances)
	require.NoError(err)

	appearances, err = ListAppearanceByUserID(a.UserID, appearance.TypeGiftNotification)
	require.NoError(err)
	SortAppearances(appearances)

	// 对比长度
	assert.Len(findUserAppearances, len(appearances))
	appearanceIDs = make([]int64, len(appearances))
	for i := range appearances {
		appearanceIDs[i] = appearances[i].AppearanceID
	}
	findUserAppearanceIDs = make([]int64, len(findUserAppearances))
	for i := range findUserAppearances {
		findUserAppearanceIDs[i] = findUserAppearances[i].AppearanceID
	}
	// 对比元素是否一致
	assert.ElementsMatch(appearanceIDs, findUserAppearanceIDs)
}

func TestNewUserAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	aItem := &appearance.Appearance{
		ID:             1,
		Name:           "测试转换",
		Type:           appearance.TypeCardFrame,
		From:           appearance.FromCustom,
		Icon:           "oss://testdata/test.webp",
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 1000,
		Image:          "oss://testdata/test.webp",
		Frame:          "oss://testdata/test.webp",
		TextColor:      "#FFFFFF",
		MessageBar: &appearance.MessageBar{
			Image: "oss://testdata/test.webp",
		},
		WelcomeMessage: &appearance.WelcomeMessage{
			Text: "欢迎 2022 神壕冠军进入直播间",
		},
		Resource: "oss://test/skin.zip",
	}

	testUserID := int64(12)
	ua := NewUserAppearance(testUserID, aItem)
	require.NotNil(ua)
	assert.NotZero(ua.AppearanceID)
	assert.Equal(testUserID, ua.UserID)
	assert.NotEmpty(ua.Name)
	assert.NotEmpty(ua.Type)
	assert.Equal(appearance.FromCustom, ua.From)
	assert.NotEmpty(ua.Icon)
	assert.NotZero(ua.Effect)
	assert.NotZero(ua.WebEffect)
	assert.NotZero(ua.EffectDuration)
	assert.NotEmpty(ua.Image)
	assert.NotEmpty(ua.Frame)
	assert.Equal(aItem.TextColor, ua.TextColor)
	assert.NotNil(ua.MessageBar)
	assert.NotNil(ua.WelcomeMessage)
	assert.NotEmpty(ua.Resource)
}

func TestNewEntryBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ua := UserAppearance{
		AppearanceID: 10010,
		Name:         "test_name",
		Image:        "oss://test_image.png",
		WelcomeMessage: &appearance.WelcomeMessage{
			Text:   "欢迎 XXX 用户",
			Colors: "#FFFFFF",
		},
	}
	eb := NewEntryBubble(&ua)
	require.NotNil(eb)
	assert.Equal("https://static-test.missevan.com/test_image.png", eb.ImageURL)
}

func TestEntryBubble_setDefaultWelcomeMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	e := EntryBubble{}
	e.setDefaultWelcomeMessage()
	require.NotNil(e.WelcomeMessage)
	assert.Equal("欢迎进入本直播间", e.WelcomeMessage.Text)
	assert.Equal("#FFFFFF", e.WelcomeMessage.Colors)

	e.WelcomeMessage = &appearance.WelcomeMessage{
		Text:   "",
		Colors: "",
	}
	e.setDefaultWelcomeMessage()
	assert.Equal("欢迎进入本直播间", e.WelcomeMessage.Text)
	assert.Equal("#FFFFFF", e.WelcomeMessage.Colors)
}

func TestSetAppearance(t *testing.T) {
	assert := assert.New(t)

	a := &UserAppearance{
		Type:         appearance.TypeVehicle,
		From:         appearance.FromCustom,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试自定义外观初始化",
	}
	now := goutil.TimeNow()
	a.SetStatus(StatusWorn, now.Add(time.Minute).Unix(), 0)
	assert.Equal(appearance.FromCustom, a.From)
	assert.Equal(StatusWorn, a.Status)
	assert.Equal(OwnStatusUsed, a.OwnStatus)
	assert.Equal(now.Add(time.Minute).Unix(), *a.ExpireTime)
	assert.GreaterOrEqual(a.StartTime, now.Unix())

	a = &UserAppearance{
		Type:         appearance.TypeVehicle,
		From:         appearance.FromNoble,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试贵族外观初始化",
	}
	a.SetStatus(StatusWorn, now.Add(time.Minute).Unix(), now.Unix())
	assert.Equal(appearance.FromNoble, a.From)
	assert.Equal(StatusWorn, a.Status)
	assert.Equal(OwnStatusUsed, a.OwnStatus)
	assert.Equal(now.Add(time.Minute).Unix(), *a.ExpireTime)
	assert.Equal(a.StartTime, now.Unix())
}

func TestSortAppearances(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	validExpTime := now.Add(2 * time.Minute).Unix()
	validExpTime2 := now.Add(4 * time.Minute).Unix()
	expTime := now.Add(2 * -time.Minute).Unix()
	expTime2 := now.Add(4 * -time.Minute).Unix()
	appearances := []UserAppearance{
		// 永久 + 佩戴
		{
			AppearanceID: 1,
			StartTime:    now.Add(-time.Minute).Unix(),
			ExpireTime:   nil,
			Status:       StatusWorn,
		},
		// 有效 × 2，开始时间与下面的一个外观不同
		{
			AppearanceID: 2,
			StartTime:    now.Add(1 * -time.Minute).Unix(),
			ExpireTime:   &validExpTime,
			Status:       StatusOwned,
		},
		// 有效 × 2，过期时间不同
		{
			AppearanceID: 3,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   &validExpTime,
			Status:       StatusOwned,
		},
		{
			AppearanceID: 4,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   &validExpTime2,
			Status:       StatusOwned,
		},
		// 永久 × 2，开始时间不同
		{
			AppearanceID: 5,
			StartTime:    now.Add(-time.Minute).Unix(),
			ExpireTime:   nil,
			Status:       StatusOwned,
		},
		{
			AppearanceID: 6,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   nil,
			Status:       StatusOwned,
		},
		// 过期，过期时间与 id 8 相同
		{
			AppearanceID: 7,
			StartTime:    now.Add(1 * -time.Minute).Unix(),
			ExpireTime:   &expTime,
			Status:       StatusOwned,
		},
		// 过期 × 1
		{
			AppearanceID: 8,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   &expTime,
			Status:       StatusOwned,
		},
		// 过期 × 1，佩戴中
		{
			AppearanceID: 9,
			StartTime:    now.Add(3 * -time.Minute).Unix(),
			ExpireTime:   &expTime,
			Status:       StatusWorn,
		},
		// 过期 × 1，与上两个过期时间不同
		{
			AppearanceID: 10,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   &expTime2,
			Status:       StatusOwned,
		},
		// 过期 × 1，与上个开始时间不同
		{
			AppearanceID: 11,
			StartTime:    now.Add(3 * -time.Minute).Unix(),
			ExpireTime:   &expTime2,
			Status:       StatusOwned,
		},
	}

	times := 0
	for times < 5 {
		times++
		t.Run(fmt.Sprintf("SortShuffledAppearance%d", times), func(t *testing.T) {
			rand.Shuffle(len(appearances), func(i, j int) {
				appearances[i], appearances[j] = appearances[j], appearances[i]
			})

			SortAppearances(appearances)
			ids := make([]int64, len(appearances))
			for i := range appearances {
				ids[i] = appearances[i].AppearanceID
			}
			ok := assert.Equal("1,2,3,4,5,6,7,8,9,10,11", goutil.JoinInt64Array(ids, ","))
			if !ok {
				t.Fail()
			}
		})
	}

	// 称号排序
	appearances = []UserAppearance{
		// 永久 × 2，佩戴顺序不同的称号
		{
			AppearanceID: 1,
			Type:         appearance.TypeBadge,
			StartTime:    now.Add(10 * -time.Second).Unix(),
			ExpireTime:   nil,
			Status:       StatusWorn,
			Position:     0,
		},
		{
			AppearanceID: 2,
			Type:         appearance.TypeBadge,
			StartTime:    now.Add(100 * -time.Second).Unix(),
			ExpireTime:   nil,
			Status:       StatusWorn,
			Position:     1,
		},
		// 有效 × 2，过期时间不同的称号
		{
			AppearanceID: 3,
			Type:         appearance.TypeBadge,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   &validExpTime,
			Status:       StatusOwned,
		},
		{
			AppearanceID: 4,
			Type:         appearance.TypeBadge,
			StartTime:    now.Add(2 * -time.Minute).Unix(),
			ExpireTime:   &validExpTime2,
			Status:       StatusOwned,
		},
		// 永久 × 1，不佩戴的正常外观
		{
			AppearanceID: 5,
			Type:         appearance.TypeBadge,
			StartTime:    now.Add(50 * -time.Second).Unix(),
			ExpireTime:   nil,
			Status:       StatusOwned,
			Position:     1,
		},
	}

	times = 0
	for times < 5 {
		times++
		t.Run(fmt.Sprintf("SortShuffledAppearanceWithPosition%d", times), func(t *testing.T) {
			rand.Shuffle(len(appearances), func(i, j int) {
				appearances[i], appearances[j] = appearances[j], appearances[i]
			})

			SortAppearances(appearances)
			ids := make([]int64, len(appearances))
			for i := range appearances {
				ids[i] = appearances[i].AppearanceID
			}
			ok := assert.Equal("1,2,3,4,5", goutil.JoinInt64Array(ids, ","))
			if !ok {
				t.Fail()
			}
		})
	}
}

func TestFindValidAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	expTime := now.Add(4 * time.Minute).Unix()
	a := &UserAppearance{ // 2 分钟后生效，不应该被查询到
		Type:         appearance.TypeVehicle,
		Status:       StatusOwned,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询座驾",
		StartTime:    now.Add(2 * time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a2 := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusWorn,
		UserID:       1,
		AppearanceID: 2,
		Name:         "测试查询头像框",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   nil,
	}
	col := Collection()
	_, err := col.UpdateOne(ctx,
		bson.M{"user_id": a.UserID, "appearance_id": a.AppearanceID},
		bson.M{"$set": a}, options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = col.UpdateOne(ctx,
		bson.M{"user_id": a2.UserID, "appearance_id": a2.AppearanceID},
		bson.M{"$set": a2}, options.Update().SetUpsert(true))
	require.NoError(err)

	item, err := FindValidAppearance(a.AppearanceID, a.UserID, appearance.TypeVehicle)
	require.NoError(err)
	require.Nil(item)

	item, err = FindValidAppearance(a2.AppearanceID, a.UserID, appearance.TypeAvatarFrame)
	require.NoError(err)
	require.NotNil(item)
	assert.Equal(a2.AppearanceID, item.AppearanceID)
	assert.Equal(a2.Status, item.Status)
}

func TestFindWornAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	expTime := now.Add(time.Minute).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       StatusWorn,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询已佩戴座驾",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	aItem, err := FindWornAppearance(a.UserID, appearance.TypeVehicle)
	require.NoError(err)
	require.Nil(aItem)

	defer func() {
		_, err := Collection().DeleteOne(ctx, bson.M{"appearance_id": a.AppearanceID})
		assert.NoError(err)
	}()
	_, err = Collection().InsertOne(ctx, a)
	require.NoError(err)

	aItem, err = FindWornAppearance(a.UserID, appearance.TypeVehicle)
	require.NoError(err)
	vehicle := NewVehicle(aItem)
	require.NotNil(vehicle)

	assert.Equal(a.AppearanceID, vehicle.VehicleID)
	assert.Equal(a.Name, vehicle.VehicleName)
	assert.NotNil(vehicle.MessageBar)
	assert.NotNil(vehicle.Bubble)
}

func TestFindUserWornAppearancesSets(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(240 * time.Hour).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       2001,
		AppearanceID: 2007,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test.png",
	}
	a2 := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       2001,
		AppearanceID: 2008,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test2.png",
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, []interface{}{a, a2})
	require.NoError(err)
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"appearance_id": bson.M{"$in": bson.A{
				a.AppearanceID,
				a2.AppearanceID,
			}}})
		assert.NoError(err)
	}()

	ClearCache(2001)
	uaSets, err := findUserWornAppearancesSets(2001)
	require.NoError(err)
	badges := uaSets[appearance.TypeBadge]
	require.NotEmpty(badges)
	require.Len(badges, 2)
	assert.Equal(a.AppearanceID, badges[0].AppearanceID)
	assert.Equal(a2.AppearanceID, badges[1].AppearanceID)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test.png",
		storage.ParseSchemeURL(badges[0].Image))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test2.png",
		storage.ParseSchemeURL(badges[1].Image))

	uaSets, err = findUserWornAppearancesSets(2001)
	require.NoError(err)
	badges = uaSets[appearance.TypeBadge]
	require.NotEmpty(badges)
	require.Len(badges, 2)
	assert.Equal(a.AppearanceID, badges[0].AppearanceID)
	assert.Equal(a2.AppearanceID, badges[1].AppearanceID)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test.png",
		storage.ParseSchemeURL(badges[0].Image))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test2.png",
		storage.ParseSchemeURL(badges[1].Image))

	uaSets, err = findUserWornAppearancesSets(2999)
	require.NoError(err)
	assert.Empty(uaSets)
	uaSets, err = findUserWornAppearancesSets(0)
	require.NoError(err)
	assert.Empty(uaSets)
}

func TestFindUsersWornAppearancesSetsMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(time.Hour).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       20221228,
		AppearanceID: 202212281,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test.png",
	}
	a2 := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       20221228,
		AppearanceID: 202212282,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test2.png",
	}
	a3 := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       202212283,
		AppearanceID: 202212284,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test2.png",
	}
	a4 := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       202212285,
		AppearanceID: 202212286,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test2.png",
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, []interface{}{a, a2, a3, a4})
	require.NoError(err)
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"appearance_id": bson.M{"$in": bson.A{
				a.AppearanceID,
				a2.AppearanceID,
				a3.AppearanceID,
				a4.AppearanceID,
			}}})
		assert.NoError(err)
	}()
	ClearCache(a.UserID, a3.UserID, a4.UserID, -99999)
	uaSetsMap, err := findUsersWornAppearancesSetsMap([]int64{a.UserID, a3.UserID, -99999})
	require.NoError(err)
	require.Len(uaSetsMap, 3)
	a1Sets, ok := uaSetsMap[a.UserID]
	require.True(ok)
	badges := a1Sets[appearance.TypeBadge]
	require.NotEmpty(badges)
	require.Len(badges, 2)
	assert.Equal(a.AppearanceID, badges[0].AppearanceID)
	assert.Equal(a2.AppearanceID, badges[1].AppearanceID)

	a3Sets, ok := uaSetsMap[a3.UserID]
	require.True(ok)
	badges = a3Sets[appearance.TypeBadge]
	require.NotEmpty(badges)
	require.Len(badges, 1)
	assert.Equal(a3.AppearanceID, badges[0].AppearanceID)

	otherSets, ok := uaSetsMap[-99999]
	require.True(ok)
	require.Empty(otherSets)

	// 从缓存中读取 a a3 -99999, a4 没有缓存
	uaSetsMap, err = findUsersWornAppearancesSetsMap([]int64{a.UserID, a3.UserID, -99999, a4.UserID})
	require.NoError(err)
	require.Len(uaSetsMap, 4)
	a1Sets, ok = uaSetsMap[a.UserID]
	require.True(ok)
	assert.Len(a1Sets[appearance.TypeBadge], 2)

	a3Sets, ok = uaSetsMap[a3.UserID]
	require.True(ok)
	assert.Len(a3Sets[appearance.TypeBadge], 1)

	otherSets, ok = uaSetsMap[-99999]
	require.True(ok)
	assert.Empty(otherSets[appearance.TypeBadge])

	a4Sets, ok := uaSetsMap[a4.UserID]
	require.True(ok)
	assert.Len(a4Sets[appearance.TypeBadge], 1)
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(time.Minute).Unix()
	userID := int64(123456)
	a := &UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       StatusWorn,
		UserID:       userID,
		AppearanceID: 1,
		Name:         "测试查询座驾方法",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	a2 := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusWorn,
		UserID:       userID,
		AppearanceID: 1,
		Name:         "测试查询头像框方法",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"appearance_id": a.AppearanceID})
		assert.NoError(err)
	}()
	_, err := Collection().InsertMany(ctx, []interface{}{a, a2})
	require.NoError(err)

	res, err := Find(bson.M{
		"type":    bson.M{"$in": bson.A{appearance.TypeVehicle, appearance.TypeAvatarFrame}},
		"user_id": userID,
	}, nil)
	require.NoError(err)
	assert.Len(res, 2)
	for i := range res {
		assert.NotNil(res[i].MessageBar)
	}
}

func TestFindVehicle(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(time.Minute).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       StatusWorn,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询座驾方法",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	// 不应该查到头像框
	a2 := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusWorn,
		UserID:       1,
		AppearanceID: 1,
		Name:         "测试查询头像框方法",
		Image:        "oss://testdata/test.webp",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"appearance_id": a.AppearanceID})
		assert.NoError(err)
	}()
	_, err := Collection().InsertMany(ctx, []interface{}{a, a2})
	require.NoError(err)

	res, err := FindVehicle(bson.M{"appearance_id": a.AppearanceID})
	require.NoError(err)
	assert.Len(res, 1)
	assert.NotNil(res[0].MessageBar)
	assert.NotNil(res[0].Bubble)
}

func TestFindWornVehicle(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(2 * time.Minute).Unix()
	a := &UserAppearance{
		Status:       StatusWorn,
		UserID:       10,
		AppearanceID: 2,
		Name:         "测试查询佩戴中的座驾",
		Type:         appearance.TypeVehicle,
		MessageBar: &appearance.MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: &expTime,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"appearance_id": a.AppearanceID})
		assert.NoError(err)
	}()
	_, err := Collection().InsertOne(ctx, a)
	require.NoError(err)

	v, err := FindWornVehicle(a.UserID)
	require.NoError(err)
	require.NotNil(v)
	assert.Equal(a.AppearanceID, v.VehicleID)
	assert.Equal(a.UserID, v.UserID)

	v, err = FindWornVehicle(-999)
	require.NoError(err)
	assert.Nil(v)
}

func TestFindEntryBubbles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uid := int64(111202)
	aid := int64(11110101)
	ua := UserAppearance{
		AppearanceID: aid,
		UserID:       uid,
		Status:       StatusWorn,
		Type:         appearance.TypeEntryBubble,
		Image:        "test_image",
		WelcomeMessage: &appearance.WelcomeMessage{
			Text: "",
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"appearance_id": aid})
		assert.NoError(err)
	}()
	_, err := Collection().InsertOne(ctx, ua)
	require.NoError(err)

	uas := FindEntryBubbles([]int64{uid})
	require.NotZero(len(uas))
	res := uas[0]
	require.Equal(ua.AppearanceID, res.AppearanceID)
}

func TestNewVehicle(t *testing.T) {
	assert := assert.New(t)

	aItem := &UserAppearance{
		AppearanceID:   1,
		Name:           "测试转换",
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 1000,
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
	}

	aItem.Type = appearance.TypeAvatarFrame
	assert.PanicsWithValue("appearance is not vehicle",
		func() {
			NewVehicle(aItem)
		})

	aItem.Type = appearance.TypeVehicle
	v := NewVehicle(aItem)

	assert.NotNil(v)
	assert.NotZero(v.VehicleID)
	assert.NotEmpty(v.VehicleName)
	assert.NotEmpty(v.Effect)
	assert.NotEmpty(v.WebEffect)
	assert.NotZero(v.EffectDuration)
	assert.NotNil(v.MessageBar)
	assert.NotEmpty(v.Message)
	assert.NotNil(v.Bubble)
	assert.Equal(bubble.TypeStrVehicle, v.Bubble.Type)
}

func TestVehicleFuncs(t *testing.T) {
	assert := assert.New(t)

	testEffect := "oss://test.mp4;oss://test.webp"
	message := "${username}骑着${vehicle_name}"
	a := UserAppearance{
		Name:      "白龙马",
		Effect:    testEffect,
		WebEffect: testEffect,
		Type:      appearance.TypeVehicle,
		MessageBar: &appearance.MessageBar{
			Message: message,
			Image:   "oss://testdata/test.webp",
		},
	}
	v := NewVehicle(&a)

	testEffect = storage.ParseSchemeURLs(testEffect)
	assert.Equal(testEffect, v.Effect)
	assert.Equal(testEffect, v.WebEffect)

	// 刚好的情况
	v.FormatMessage("<唐三藏>1234")
	assert.Equal("&lt;唐三藏&gt;1234骑着白龙马", v.MessageBar.Message)
	assert.Equal("&lt;唐三藏&gt;1234骑着白龙马", v.Message)
	v.MessageBar.Message = message
	v.FormatMessage("<唐三藏>一二")
	assert.Equal("&lt;唐三藏&gt;一二骑着白龙马", v.MessageBar.Message)
	assert.Equal("&lt;唐三藏&gt;一二骑着白龙马", v.Message)

	// 截取的情况
	v.MessageBar.Message = message
	v.FormatMessage("<唐三藏>12345")
	assert.Equal("&lt;唐三藏&gt;12...骑着白龙马", v.MessageBar.Message)
	assert.Equal("&lt;唐三藏&gt;12...骑着白龙马", v.Message)
	v.MessageBar.Message = message
	v.FormatMessage("<唐三藏>一二三")
	assert.Equal("&lt;唐三藏&gt;一...骑着白龙马", v.MessageBar.Message)
	assert.Equal("&lt;唐三藏&gt;一...骑着白龙马", v.Message)

	colorMessage := "${normal_color} " + message + " ${highlight_color}"
	v.MessageBar.Message = colorMessage
	v.MessageBar.NormalColor = "#FFFFFF"
	v.MessageBar.HighlightColor = "#000000"
	v.FormatMessage("<唐三藏>")
	assert.Equal("#FFFFFF &lt;唐三藏&gt;骑着白龙马 #000000", v.MessageBar.Message)
	assert.Equal("#FFFFFF &lt;唐三藏&gt;骑着白龙马 #000000", v.Message)
	assert.Empty(v.MessageBar.NormalColor)
	assert.Empty(v.MessageBar.HighlightColor)
}

func TestUserHasNewAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(13)
	hasNew, err := UserHasNewAppearance(testUserID)
	require.NoError(err)
	assert.False(hasNew)

	now := goutil.TimeNow()
	expTime := now.Add(-time.Minute).Unix()
	// 不应该查出过期且全新的外观
	expiredUA := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusNew,
		UserID:       testUserID,
		AppearanceID: 2003,
		Name:         "测试查询用户持有的过期外观",
		Image:        "oss://testdata/test.webp",
		StartTime:    now.Add(2 * -time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	// 不应该查出有效的非全新外观
	validUA := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       testUserID,
		AppearanceID: 2004,
		Name:         "测试查询用户持有的使用过外观",
		Image:        "oss://testdata/test.webp",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   nil,
	}
	// 不应该查出贵族外观
	nobleUA := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		UserID:       testUserID,
		AppearanceID: 2005,
		Name:         "测试查询用户持有的贵族外观",
		Image:        "oss://testdata/test.webp",
		From:         appearance.FromNoble,
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   nil,
	}
	// 应该有这个外观
	newUA := &UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusNew,
		UserID:       testUserID,
		AppearanceID: 2006,
		Name:         "测试查询用户持有的新外观",
		Image:        "oss://testdata/test.webp",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   nil,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
		assert.NoError(err)
	}()
	_, err = Collection().InsertMany(ctx, []interface{}{expiredUA, validUA, nobleUA})
	require.NoError(err)

	hasNew, err = UserHasNewAppearance(testUserID)
	require.NoError(err)
	assert.False(hasNew)

	_, err = Collection().InsertOne(ctx, newUA)
	require.NoError(err)

	hasNew, err = UserHasNewAppearance(testUserID)
	require.NoError(err)
	assert.True(hasNew)
}

func TestNewMessageBubble(t *testing.T) {
	assert := assert.New(t)

	ua := &UserAppearance{}
	assert.PanicsWithValue("appearance is not message bubble", func() {
		NewMessageBubble(ua)
	})
	ua.Type = appearance.TypeMessageBubble
	ua.TextColor = "#123456"
	ua.Frame = "oss://frame.png"
	ua.Image = "oss://image.png"
	b := NewMessageBubble(ua)
	assert.Equal(&bubble.Simple{
		Type:      "message",
		ImageURL:  "https://static-test.missevan.com/image.png",
		FrameURL:  "https://static-test.missevan.com/frame.png",
		TextColor: "#123456",
	}, b)
}

func TestUpdateUserAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.PanicsWithValue("expire_time and duration shouldn't be 0 at the same time", func() {
		updateUserAppearanceWithDuration(0, 0, 0, nil, nil, false)
	})

	userID := int64(1)
	duration := int64(7 * 24 * 60 * 60)
	appearanceID := int64(1000)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id": appearanceID,
		},
		bson.M{
			"$set": bson.M{
				"type": appearance.TypeCardFrame,
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	a, err := appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试用户未持有外观
	// 有限时间模版下发
	update := updateUserAppearanceWithDuration(userID, duration, 0, nil, a, false)
	require.NotNil(update)
	now := goutil.TimeNow()
	var document *UserAppearance
	var insertModel *mongo.InsertOneModel
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	expectedTime := now.Unix() + duration
	assert.LessOrEqual(expectedTime, *document.ExpireTime)

	// 测试用户已持有外观
	// 有限时间模版下发，重复获得，时间叠加
	oldUa := &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   document.ExpireTime,
	}
	update = updateUserAppearanceWithDuration(userID, duration, 0, oldUa, a, false)
	require.NotNil(update)
	var updateModel *mongo.UpdateOneModel
	var updateDocument *UserAppearance
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	expectedTime2 := expectedTime + duration
	assert.LessOrEqual(expectedTime2, *updateDocument.ExpireTime)

	// 测试用户已持有外观
	// 有限时间模版下发，时长已经达到最大限制的
	oldUa.ExpireTime = updateDocument.ExpireTime
	update = updateUserAppearanceWithDuration(userID, duration+duration, 0, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*a.ExpireTime, *updateDocument.ExpireTime)

	// 查询时间上限较短的外观
	appearanceID = int64(1001)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试用户未持有外观
	// 有限时间外观模版下发，持续时间为 -1（取最大值）
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, nil, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.Equal(*a.ExpireTime, *document.ExpireTime)

	// 测试用户已持有外观
	// 有限时间外观模版下发，持续时间为 -1（取最大值）
	validTime := now.Add(time.Minute).Unix()
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &validTime,
	}
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*a.ExpireTime, *updateDocument.ExpireTime)

	// 测试用户未持有外观
	// 时长已经达到最大限制的
	update = updateUserAppearanceWithDuration(userID, duration, 0, nil, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.LessOrEqual(*a.ExpireTime, *document.ExpireTime)

	// 测试用户持有过期外观
	// 有限时间外观模版下发有限时长
	expTime := now.Add(-time.Minute).Unix()
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	duration = int64(4 * 60 * 60)
	update = updateUserAppearanceWithDuration(userID, duration, 0, oldUa, a, true)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(StatusWorn, updateDocument.Status)
	assert.Equal(OwnStatusNew, updateDocument.OwnStatus)
	assert.LessOrEqual(now.Unix()+duration, *updateDocument.ExpireTime)

	// 测试用户持有过期外观
	// 有限时间外观模版下发，叠加时长已经达到最大限制
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	update = updateUserAppearanceWithDuration(userID, duration*10, 0, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.LessOrEqual(*a.ExpireTime, *updateDocument.ExpireTime)

	// 测试用户持有过期外观
	// 有限时间外观模版下发，持续时间为 -1（取最大值）
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*a.ExpireTime, *updateDocument.ExpireTime)

	// 查询无时间限制的外观模版
	appearanceID = int64(1002)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试用户未持有外观
	// 无限时间外观模版下发，下发有限时长
	update = updateUserAppearanceWithDuration(userID, duration, 0, nil, a, true)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.Equal(StatusWorn, document.Status)
	assert.Equal(OwnStatusNew, document.OwnStatus)
	assert.LessOrEqual(now.Unix()+duration, *document.ExpireTime)

	// 测试用户未持有外观
	// 无限时间外观模版下发，持续时间为 -1（永久）
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, nil, a, false)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.Nil(document.ExpireTime)

	// 测试用户已持有外观
	// 无限时间外观模版下发，持续时间为 -1（永久）
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &validTime,
	}
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Nil(updateDocument.ExpireTime)

	// 测试用户已持有外观
	// 无限时间外观模版下发有限时长
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &validTime,
	}
	update = updateUserAppearanceWithDuration(userID, duration, 0, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(validTime+duration, *updateDocument.ExpireTime)

	// 测试用户持有过期外观
	// 无限时间外观模版下发有限时长
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	update = updateUserAppearanceWithDuration(userID, duration, 0, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.LessOrEqual(now.Unix()+duration, *updateDocument.ExpireTime)

	// 测试用户持有过期外观
	// 无限时间外观模版下发，持续时间为 -1（永久）
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Nil(updateDocument.ExpireTime)

	// 测试用户已持有永久外观
	// 均不作处理
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   nil,
	}
	update = updateUserAppearanceWithDuration(userID, duration, 0, oldUa, a, false)
	assert.Nil(update)
	// 测试用户已持有该永久外观，持续时间为 -1（不作处理）
	update = updateUserAppearanceWithDuration(userID, unlimitedTime, 0, oldUa, a, false)
	assert.Nil(update)
}

func TestUpdateUserAppearanceWithExpireTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.PanicsWithValue("expire_time and duration shouldn't be 0 at the same time", func() {
		updateUserAppearanceWithDuration(0, 0, 0, nil, nil, false)
	})

	userID := int64(1)
	duration := int64(7 * 24 * 60 * 60)
	appearanceID := int64(1000)
	a, err := appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 处理函数中，只处理「-1」和「模版并非无限时长，并且 -1，或是达到上限」的情况

	// 没有过期，直接设定为 expire time
	// 过期，直接设定为 expire time
	// 达到 expire time 上限，直接设定为 上限
	// 对于永久外观而言，expire time > 0 直接设定为上限
	// 对于永久外观而言，expire time == -1 设定为永久

	now := goutil.TimeNow()
	expireTime := now.Unix() + duration
	duration = int64(7 * 24 * 60 * 60)
	appearanceID = int64(1000)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试用户未持有外观
	// 有限时间模版下发，过期时间为 7 天后
	update := updateUserAppearanceWithDuration(userID, 0, expireTime, nil, a, false)
	require.NotNil(update)
	now = goutil.TimeNow()
	var document *UserAppearance
	var insertModel *mongo.InsertOneModel
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.LessOrEqual(expireTime, *document.ExpireTime)

	// 测试用户已持有外观
	// 有限时间模版下发，重复获得，不作处理，过期时间为 7 天后
	oldUa := &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   document.ExpireTime,
	}
	expireTime = now.Unix() + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	var updateModel *mongo.UpdateOneModel
	var updateDocument *UserAppearance
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.LessOrEqual(expireTime, *updateDocument.ExpireTime)

	// 测试用户已持有外观
	// 有限时间模版下发，过期时间已经达到上限的
	oldUa.ExpireTime = updateDocument.ExpireTime
	expireTime = *a.ExpireTime + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*a.ExpireTime, *updateDocument.ExpireTime)

	// // 查询时间上限较短的外观
	appearanceID = int64(1001)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试用户未持有外观
	// 有限时间外观模版下发，过期时间为 -1（取最大值）
	update = updateUserAppearanceWithDuration(userID, 0, unlimitedTime, nil, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.Equal(*document.ExpireTime, *a.ExpireTime)

	// 测试用户已持有外观
	// 有限时间外观模版下发，过期时间为 -1（取最大值）
	validTime := now.Add(time.Minute).Unix()
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &validTime,
	}
	update = updateUserAppearanceWithDuration(userID, 0, unlimitedTime, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*a.ExpireTime, *updateDocument.ExpireTime)

	// 测试用户未持有外观
	// 过期时间已经达到上限的
	expireTime = now.Unix() + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, nil, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.LessOrEqual(*a.ExpireTime, *document.ExpireTime)

	// 测试用户持有过期外观
	// 有限时间外观模版下发过期时间，过期时间为 4 小时后
	itemExpireTime := now.Add(-time.Minute).Unix()
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &itemExpireTime,
	}
	duration = int64(4 * 60 * 60)
	expireTime = now.Unix() + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.LessOrEqual(now.Unix()+duration, *updateDocument.ExpireTime)

	// 测试用户持有过期外观
	// 有限时间外观模版下发，过期时间已经达到上限
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &itemExpireTime,
	}
	expireTime = now.Unix() + duration*10
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.LessOrEqual(*a.ExpireTime, *updateDocument.ExpireTime)

	// 测试用户持有过期外观
	// 有限时间外观模版下发，过期时间为 -1（取最大值）
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &itemExpireTime,
	}
	update = updateUserAppearanceWithDuration(userID, 0, unlimitedTime, oldUa, a, false)
	require.NotNil(update)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*a.ExpireTime, *updateDocument.ExpireTime)

	// 查询无时间限制的外观模版
	appearanceID = int64(1002)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试用户未持有外观
	// 无限时间外观模版下发，下发有限时长
	expireTime = now.Unix() + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, nil, a, false)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.LessOrEqual(expireTime, *document.ExpireTime)

	// 测试用户未持有外观
	// 无限时间外观模版下发，持续时间为 -1（永久）
	update = updateUserAppearanceWithDuration(userID, 0, -1, nil, a, false)
	require.NotPanics(func() {
		insertModel = update.(*mongo.InsertOneModel)
		document = insertModel.Document.(*UserAppearance)
	})
	require.NotNil(document)
	assert.Equal(appearanceID, document.AppearanceID)
	assert.Nil(document.ExpireTime)

	// 测试用户已持有外观
	// 无限时间外观模版下发，过期时间为 -1（永久）
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &validTime,
	}
	update = updateUserAppearanceWithDuration(userID, 0, -1, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Nil(updateDocument.ExpireTime)

	// 测试用户已持有外观
	// 无限时间外观模版下发指定时间
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &validTime,
	}
	expireTime = now.Unix() + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*updateDocument.ExpireTime, expireTime)

	// 测试用户持有过期外观
	// 无限时间外观模版下发指定过期时间
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &itemExpireTime,
	}
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*updateDocument.ExpireTime, expireTime)

	// 测试用户持有过期外观
	// 无限时间外观模版下发，过期时间为 -1（永久）
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   &itemExpireTime,
	}
	update = updateUserAppearanceWithDuration(userID, 0, -1, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Nil(updateDocument.ExpireTime)

	// 测试用户已持有永久外观，有过期时间则处理
	oldUa = &UserAppearance{
		AppearanceID: a.ID,
		UserID:       userID,
		Status:       StatusOwned,
		OwnStatus:    OwnStatusUsed,
		StartTime:    now.Unix(),
		ExpireTime:   nil,
	}
	expireTime = now.Unix() + duration
	update = updateUserAppearanceWithDuration(userID, 0, expireTime, oldUa, a, false)
	require.NotPanics(func() {
		updateModel = update.(*mongo.UpdateOneModel)
		updateDocument = updateModel.Update.(bson.M)["$set"].(*UserAppearance)
	})
	require.NotNil(updateDocument)
	assert.Equal(appearanceID, updateDocument.AppearanceID)
	assert.Equal(*updateDocument.ExpireTime, expireTime)

	// 测试用户已持有该永久外观，过期时间为 -1（不作处理）
	update = updateUserAppearanceWithDuration(userID, 0, -1, oldUa, a, false)
	assert.Nil(update)
}

func TestAddAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	userID := int64(1)
	duration := int64(7 * 24 * 60 * 60)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
		assert.NoError(err)
	}()

	// 查询常规外观
	appearanceID := int64(1000)
	a, err := appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)

	// 测试常规外观发放
	err = AddAppearance(userID, duration, 0, a)
	require.NoError(err)
	aItem, err := FindValidAppearance(appearanceID, userID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(aItem.AppearanceID, appearanceID)
	expectedTime := now.Unix() + duration
	assert.LessOrEqual(expectedTime, *aItem.ExpireTime)

	// 变更为佩戴状态
	_, err = Collection().UpdateOne(ctx, bson.M{"user_id": userID, "appearance_id": a.ID, "type": a.Type},
		bson.M{"$set": bson.M{"status": StatusWorn}})
	require.NoError(err)

	// 测试重复下发的情况，佩戴状态不应该更新
	err = AddAppearance(userID, duration, 0, a)
	require.NoError(err)
	aItem, err = FindValidAppearance(appearanceID, userID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(aItem.AppearanceID, appearanceID)
	assert.LessOrEqual(expectedTime+duration, *aItem.ExpireTime)
	assert.Equal(StatusWorn, aItem.Status)

	// 查询永久外观
	appearanceID = int64(1002)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试永久外观发放
	err = AddAppearance(userID, -1, 0, a)
	require.NoError(err)
	aItem, err = FindValidAppearance(appearanceID, userID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(aItem.AppearanceID, appearanceID)
	assert.Nil(aItem.ExpireTime)
}

func TestAddAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	userID := int64(1)
	duration := int64(7 * 24 * 60 * 60)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
		assert.NoError(err)
	}()

	// 查询常规外观
	appearanceID := int64(1000)
	a, err := appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)

	// 测试常规外观发放
	appearances := []*appearance.Appearance{a}
	err = AddAppearances(userID, duration, 0, appearances, false)
	require.NoError(err)
	aItem, err := FindValidAppearance(appearanceID, userID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(aItem.AppearanceID, appearanceID)
	expectedTime := now.Unix() + duration
	assert.LessOrEqual(expectedTime, *aItem.ExpireTime)

	// 变更为佩戴状态
	_, err = Collection().UpdateOne(ctx, bson.M{"user_id": userID, "appearance_id": a.ID, "type": a.Type},
		bson.M{"$set": bson.M{"status": StatusWorn}})
	require.NoError(err)

	// 测试重复下发的情况，佩戴状态不应该更新
	err = AddAppearances(userID, duration, 0, appearances, false)
	require.NoError(err)
	aItem, err = FindValidAppearance(appearanceID, userID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(aItem.AppearanceID, appearanceID)
	assert.LessOrEqual(expectedTime+duration, *aItem.ExpireTime)
	assert.Equal(StatusWorn, aItem.Status)

	// 查询永久外观
	appearanceID = int64(1002)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	// 测试永久外观发放
	appearances = []*appearance.Appearance{a}
	err = AddAppearances(userID, -1, 0, appearances, false)
	require.NoError(err)
	aItem, err = FindValidAppearance(appearanceID, userID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(aItem.AppearanceID, appearanceID)
	assert.Nil(aItem.ExpireTime)
}

func TestAddAppearances_IdentityBadge(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	startTime := now.Add(-time.Minute).Unix()
	duration := int64(7 * 24 * 60 * 60)
	expTime := now.Add(time.Duration(duration) * time.Second).Unix()

	identityBadge1 := &appearance.Appearance{
		ID:             999,
		Type:           appearance.TypeIdentityBadge,
		StartTime:      startTime,
		ExpireTime:     &expTime,
		EffectDuration: duration,
	}

	identityBadge2 := &appearance.Appearance{
		ID:             1000,
		Type:           appearance.TypeIdentityBadge,
		StartTime:      startTime,
		ExpireTime:     &expTime,
		EffectDuration: duration,
	}

	identityBadge3 := &appearance.Appearance{
		ID:             1001,
		Type:           appearance.TypeIdentityBadge,
		StartTime:      startTime,
		ExpireTime:     &expTime,
		EffectDuration: duration,
	}

	_, err := appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{identityBadge1.ID, identityBadge2.ID, identityBadge3.ID}}})
	require.NoError(err)
	_, err = appearance.Collection().InsertMany(ctx, []interface{}{identityBadge1, identityBadge2, identityBadge3})
	require.NoError(err)
	t.Run("新用户佩戴身份铭牌", func(t *testing.T) {
		userID := int64(10001)
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
		require.NoError(err)

		err = AddAppearances(userID, duration, expTime, []*appearance.Appearance{identityBadge1}, true)
		require.NoError(err)

		ClearCache(userID)
		identityBadges, err := FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 1)
	})
	t.Run("替换最早佩戴的铭牌", func(t *testing.T) {
		userID := int64(10001)
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
		require.NoError(err)
		ClearCache(userID)
		identityBadges, err := FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 0)

		err = AddAppearances(userID, duration, expTime, []*appearance.Appearance{identityBadge1, identityBadge2}, true)
		require.NoError(err)
		ClearCache(userID)
		identityBadges, err = FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 2)

		err = AddAppearances(userID, duration, expTime, []*appearance.Appearance{identityBadge3}, true)
		require.NoError(err)

		ClearCache(userID)
		identityBadges, err = FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 2)
		// 早穿上的 999 铭牌被替换
		assert.Equal(identityBadge2.ID, identityBadges[0].AppearanceID)
	})
}

func TestBatchAddAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs := []int64{2, 3, 4}
	duration := int64(7 * 24 * 60 * 60)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": userIDs}})
		assert.NoError(err)
	}()

	err := BatchAddAppearance([]int64{12, 12}, duration, 0, nil, false)
	assert.EqualError(err, "duplicates found in userIDs, abort")

	// 查询常规外观
	appearanceID := int64(1000)
	a, err := appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	err = BatchAddAppearance(userIDs, duration, 0, a, true)
	require.NoError(err)

	now := goutil.TimeNow()
	filter := appearance.SetValidTimeFilter(bson.M{
		"appearance_id": appearanceID,
		"type":          appearance.TypeCardFrame,
		"user_id":       bson.M{"$in": userIDs},
		"status":        bson.M{"$gt": StatusPending},
	}, now.Unix())
	uaItems, err := Find(filter, nil)
	require.NoError(err)
	require.NotEmpty(uaItems)
	mUserAppearance := goutil.ToMap(uaItems, "UserID").(map[int64]*UserAppearance)
	expectedTime := now.Unix() + duration
	for i := range userIDs {
		uaItem, ok := mUserAppearance[userIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.Equal(StatusWorn, uaItem.Status)
		assert.Equal(OwnStatusNew, uaItem.OwnStatus)
		assert.LessOrEqual(expectedTime, *uaItem.ExpireTime)
	}

	// 查询永久外观
	appearanceID2 := int64(1002)
	a, err = appearance.FindOne(appearanceID2, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	err = BatchAddAppearance(userIDs, -1, 0, a, true)
	require.NoError(err)

	// 确认旧外观已卸下
	uaItems, err = Find(filter, nil)
	require.NoError(err)
	require.NotEmpty(uaItems)
	mUserAppearance = goutil.ToMap(uaItems, "UserID").(map[int64]*UserAppearance)
	for i := range userIDs {
		uaItem, ok := mUserAppearance[userIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.Equal(StatusOwned, uaItem.Status)
	}

	filter["appearance_id"] = appearanceID2
	uaItems, err = Find(filter, nil)
	require.NoError(err)
	require.NotEmpty(uaItems)
	mUserAppearance = goutil.ToMap(uaItems, "UserID").(map[int64]*UserAppearance)
	for i := range userIDs {
		uaItem, ok := mUserAppearance[userIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.Equal(StatusWorn, uaItem.Status)
		assert.Equal(OwnStatusNew, uaItem.OwnStatus)
		assert.Nil(uaItem.ExpireTime)
	}
}

func TestBatchAddAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs := []int64{2, 3, 4}
	duration := int64(7 * 24 * 60 * 60)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": userIDs}})
		assert.NoError(err)
	}()

	appearanceID := int64(1000)
	a, err := appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	appearances := []*appearance.Appearance{a}
	err = BatchAddAppearances([]int64{12, 12}, duration, 0, appearances, false)
	assert.EqualError(err, "duplicates found in userIDs, abort")

	// 查询常规外观
	err = BatchAddAppearances(userIDs, duration, 0, appearances, false)
	require.NoError(err)

	now := goutil.TimeNow()
	filter := appearance.SetValidTimeFilter(bson.M{
		"appearance_id": appearanceID,
		"type":          appearance.TypeCardFrame,
		"user_id":       bson.M{"$in": userIDs},
		"status":        bson.M{"$gt": StatusPending},
	}, now.Unix())
	uaItems, err := Find(filter, nil)
	require.NoError(err)
	require.NotEmpty(uaItems)
	mUserAppearance := goutil.ToMap(uaItems, "UserID").(map[int64]*UserAppearance)
	expectedTime := now.Unix() + duration
	for i := range userIDs {
		uaItem, ok := mUserAppearance[userIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.Equal(StatusOwned, uaItem.Status)
		assert.Equal(OwnStatusNew, uaItem.OwnStatus)
		assert.LessOrEqual(expectedTime, *uaItem.ExpireTime)
	}

	// 查询永久外观
	appearanceID = int64(1002)
	a, err = appearance.FindOne(appearanceID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(a)

	appearances = []*appearance.Appearance{a}
	err = BatchAddAppearances(userIDs, -1, 0, appearances, false)
	require.NoError(err)

	filter["appearance_id"] = appearanceID
	uaItems, err = Find(filter, nil)
	require.NoError(err)
	require.NotEmpty(uaItems)
	mUserAppearance = goutil.ToMap(uaItems, "UserID").(map[int64]*UserAppearance)
	for i := range userIDs {
		uaItem, ok := mUserAppearance[userIDs[i]]
		require.True(ok)
		require.NotNil(uaItem)
		assert.Equal(StatusOwned, uaItem.Status)
		assert.Equal(OwnStatusNew, uaItem.OwnStatus)
		assert.Nil(uaItem.ExpireTime)
	}
}

func TestFindCardFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(240 * time.Hour).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeCardFrame,
		Status:       StatusWorn,
		UserID:       12,
		AppearanceID: 12,
		Name:         "测试查询名片框",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		ImageNew:     "oss://testdata/testCardFrame-new.webp",
		TextColorItem: &appearance.TextColorItem{
			Username: "#FFFFF0",
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteOne(ctx, bson.M{"appearance_id": a.AppearanceID})
	require.NoError(err)
	_, err = Collection().InsertOne(ctx, a)
	require.NoError(err)

	cardFrame, err := FindCardFrame(0)
	require.NoError(err)
	assert.Nil(cardFrame)

	cardFrame, err = FindCardFrame(120)
	require.NoError(err)
	assert.Nil(cardFrame)

	ClearCache(12)
	cardFrame, err = FindCardFrame(12)
	require.NoError(err)
	require.NotNil(cardFrame)
	assert.Equal(storage.ParseSchemeURL(a.ImageNew), cardFrame.ImageNew)
	require.NotNil(cardFrame.TextColorItem)
	assert.Equal("#FFFFF0", cardFrame.TextColorItem.Username)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Introduction, cardFrame.TextColorItem.Introduction)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.ReportAndManage, cardFrame.TextColorItem.ReportAndManage)
}

func TestFindMessageBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	userID := int64(2001)
	expTime := now.Add(240 * time.Hour).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeMessageBubble,
		Status:       StatusWorn,
		UserID:       userID,
		AppearanceID: 2001,
		Name:         "测试查询气泡框",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://show.png",
		Frame:        "oss://show.png",
		TextColor:    "#FFFFFF",
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteOne(ctx, bson.M{"appearance_id": a.AppearanceID})
	require.NoError(err)
	_, err = Collection().InsertOne(ctx, a)
	require.NoError(err)

	// 测试普通气泡框
	key := keys.KeyUsersWornAppearancesSets1.Format(userID)
	require.NoError(service.Redis.Del(key).Err())
	b, err := FindMessageBubble(userID)
	require.NoError(err)
	require.NotNil(b)

	assert.Equal(&bubble.Simple{
		Type:      bubble.TypeStrMessage,
		ImageURL:  storage.ParseSchemeURL(a.Image),
		FrameURL:  storage.ParseSchemeURL(a.Frame),
		TextColor: a.TextColor,
	}, b)

	// 测试佩戴中的贵族气泡框
	level := 7
	ua := UserAppearance{
		AppearanceID: appearance.VipAppearanceID(vip.TypeLiveNoble, level, appearance.TypeMessageBubble),
		Type:         appearance.TypeMessageBubble,
		From:         appearance.FromNoble,
		Status:       StatusWorn,
		Image:        "image.png",
		Frame:        "frame.png",
		TextColor:    "#888888",
	}
	require.NoError(service.Redis.Set(key,
		tutil.SprintJSON(map[int][]UserAppearance{
			appearance.TypeMessageBubble: {ua}}), time.Minute).Err())

	b, err = FindMessageBubble(userID)
	require.NoError(err)
	require.NotNil(b)
	assert.Equal(&bubble.Simple{
		Type:      bubble.TypeStrMessage,
		ImageURL:  "https://static-test.missevan.com/image.png",
		TextColor: "#888888",
		FrameURL:  "https://static-test.missevan.com/frame.png",
	}, b)
	// assert.Equal(&bubble.Simple{Type: bubble.TypeStrNoble, NobleLevel: level}, b)
}

func TestFindBadges(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(240 * time.Hour).Unix()
	a := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       2001,
		AppearanceID: 2007,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test.png",
	}
	a2 := &UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       StatusWorn,
		UserID:       2001,
		AppearanceID: 2008,
		Name:         "测试查询称号",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
		Image:        "oss://testdata/test2.png",
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, []interface{}{a, a2})
	require.NoError(err)
	defer func() {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"appearance_id": bson.M{"$in": bson.A{a.AppearanceID, a2.AppearanceID}}})
		assert.NoError(err)
	}()

	ClearCache(2001)
	badges, err := FindBadges(2001)
	require.NoError(err)
	require.NotEmpty(badges)
	assert.Equal(a.AppearanceID, badges[0].AppearanceID)
	assert.Equal(a2.AppearanceID, badges[1].AppearanceID)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test.png",
		storage.ParseSchemeURL(badges[0].Image))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test2.png",
		storage.ParseSchemeURL(badges[1].Image))

	badges, err = FindBadges(2001)
	require.NoError(err)
	require.NotEmpty(badges)
	assert.Equal(a.AppearanceID, badges[0].AppearanceID)
	assert.Equal(a2.AppearanceID, badges[1].AppearanceID)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test.png",
		storage.ParseSchemeURL(badges[0].Image))
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"testdata/test2.png",
		storage.ParseSchemeURL(badges[1].Image))

	badges, err = FindBadges(2999)
	require.NoError(err)
	require.Empty(badges)
	badges, err = FindBadges(1999)
	require.NoError(err)
	require.Empty(badges)
	badges, err = FindBadges(0)
	require.NoError(err)
	require.Empty(badges)
}

func TestSortBadges(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var mUas map[int][]*UserAppearance
	require.NotPanics(func() {
		sortBadges(mUas[appearance.TypeBadge])
	})

	mUas = make(map[int][]*UserAppearance)
	require.NotPanics(func() {
		sortBadges(mUas[appearance.TypeBadge])
	})

	mUas = map[int][]*UserAppearance{
		appearance.TypeBadge: {
			{
				AppearanceID: 1,
				Position:     1,
			},
			{
				AppearanceID: 2,
				Position:     2,
			},
		},
	}
	sortBadges(mUas[appearance.TypeBadge])
	assert.Equal(mUas[appearance.TypeBadge][0].AppearanceID, mUas[appearance.TypeBadge][0].AppearanceID)
	assert.Equal(mUas[appearance.TypeBadge][1].AppearanceID, mUas[appearance.TypeBadge][1].AppearanceID)

	mUas = map[int][]*UserAppearance{
		appearance.TypeBadge: {
			{
				AppearanceID: 2,
				Position:     2,
			},
			{
				AppearanceID: 2,
				Position:     1,
			},
		},
	}
	sortBadges(mUas[appearance.TypeBadge])
	assert.Equal(mUas[appearance.TypeBadge][0].AppearanceID, mUas[appearance.TypeBadge][1].AppearanceID)
	assert.Equal(mUas[appearance.TypeBadge][1].AppearanceID, mUas[appearance.TypeBadge][0].AppearanceID)
}

func TestFindUserAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ClearCache(2010)
	mUserAppearance, err := FindUserAppearances(2010)
	require.NoError(err)
	require.Equal(2, len(mUserAppearance))
	avatars, ok := mUserAppearance[appearance.TypeAvatarFrame]
	assert.True(ok)
	require.NotEmpty(avatars)
	require.Len(avatars, 1)

	badges, ok := mUserAppearance[appearance.TypeBadge]
	assert.True(ok)
	require.NotEmpty(badges)
	require.Len(badges, 1)
}

func TestFindUsersAppearancesMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ClearCache(2010)
	mUserAppearances, err := FindUsersAppearancesMap([]int64{2010, -12345})
	require.NoError(err)
	require.Len(mUserAppearances, 2)
	mUserAppearance, ok := mUserAppearances[2010]
	require.True(ok)
	avatars, ok := mUserAppearance[appearance.TypeAvatarFrame]
	assert.True(ok)
	require.NotEmpty(avatars)
	require.Len(avatars, 1)

	badges, ok := mUserAppearance[appearance.TypeBadge]
	assert.True(ok)
	require.NotEmpty(badges)
	require.Len(badges, 1)

	mUserAppearance, ok = mUserAppearances[-12345]
	require.True(ok)
	badges, ok = mUserAppearance[appearance.TypeBadge]
	assert.False(ok)
	require.Empty(badges)
}

func TestAddNobleAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = 423412342342
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	assert.NoError(err)

	findUserAppearances := func(userID int64, status int) ([]UserAppearance, error) {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		opts := options.Find().
			SetSort(bson.M{"appearance_id": 1})
		cur, err := Collection().Find(ctx, bson.M{
			"user_id": userID,
			"from":    appearance.FromNoble,
			"status":  status,
			"type": bson.M{
				"$in": []int{appearance.TypeAvatarFrame, appearance.TypeCardFrame, appearance.TypeMessageBubble},
			},
		}, opts)
		if err != nil {
			return nil, err
		}
		var userAppearances []UserAppearance
		err = cur.All(ctx, &userAppearances)
		if err != nil {
			return nil, err
		}
		return userAppearances, nil
	}

	expireTime := goutil.TimeNow().AddDate(1, 0, 0).Unix()
	err = AddNobleAppearances(testUserID, expireTime, 2, true)
	require.NoError(err)
	// 查询当前佩戴的体验卡外观资源
	userAppearances, err := findUserAppearances(testUserID, StatusWorn)
	require.NoError(err)
	assert.Equal(2, len(userAppearances))
	for _, ua := range userAppearances {
		require.NotNil(ua.ExpireTime)
		assert.Equal(expireTime, *ua.ExpireTime)
	}

	expireTime = goutil.TimeNow().AddDate(2, 0, 0).Unix()
	err = AddNobleAppearances(testUserID, expireTime, 1, false)
	require.NoError(err)
	userAppearances, err = findUserAppearances(testUserID, StatusWorn)
	require.NoError(err)
	assert.Equal(2, len(userAppearances))
	for _, ua := range userAppearances {
		require.NotNil(ua.ExpireTime)
		assert.Equal(expireTime, *ua.ExpireTime)
	}

	expireTime = goutil.TimeNow().AddDate(3, 0, 0).Unix()
	err = AddNobleAppearances(testUserID, expireTime, 4, true)
	require.NoError(err)
	userAppearances, err = findUserAppearances(testUserID, StatusWorn)
	require.NoError(err)
	assert.Equal(3, len(userAppearances))
	for _, ua := range userAppearances {
		require.NotNil(ua.ExpireTime)
		assert.Equal(expireTime, *ua.ExpireTime)
	}
}

func TestAddHighnessAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": 907450912})
	assert.NoError(err)

	// 测试开通
	testTime := goutil.TimeNow().Add(time.Minute).Unix()
	err = AddHighnessAppearances(907450912, testTime, true)
	require.NoError(err)

	appearanceTypes := []int{
		appearance.TypeVehicle, appearance.TypeAvatarFrame, appearance.TypeCardFrame, appearance.TypeMessageBubble,
	}
	opts := options.Find().
		SetSort(bson.M{"appearance_id": 1})
	cur, err := Collection().Find(ctx, bson.M{
		"user_id": 907450912,
		"from":    appearance.FromHighness,
		"type": bson.M{
			"$in": appearanceTypes,
		},
	}, opts)
	require.NoError(err)
	defer cur.Close(ctx)
	var aItem []UserAppearance
	err = cur.All(ctx, &aItem)
	require.NoError(err)
	require.Len(aItem, 4)
	checkAppearanceIDs := []int64{91, 92, 93, 94}
	for i := range aItem {
		assert.Equal(checkAppearanceIDs[i], aItem[i].AppearanceID)
		assert.Equal(testTime, *aItem[i].ExpireTime)
		assert.Equal(StatusWorn, aItem[i].Status)
	}

	// 测试续订
	testTime = goutil.TimeNow().Add(time.Minute).Unix()
	err = AddHighnessAppearances(907450912, testTime, false)
	require.NoError(err)
	cur, err = Collection().Find(ctx, bson.M{
		"user_id": 907450912,
		"from":    appearance.FromHighness,
		"appearance_id": bson.M{
			"$in": appearanceTypes,
		},
	}, opts)
	require.NoError(err)
	aItem = []UserAppearance{}
	err = cur.All(ctx, &aItem)
	require.NoError(err)
	for i := range aItem {
		assert.Equal(checkAppearanceIDs[i], aItem[i].AppearanceID)
		assert.Equal(testTime, *aItem[i].ExpireTime)
	}

	// 测试清空并重建
	testTime = goutil.TimeNow().Add(time.Minute).Unix()
	err = AddHighnessAppearances(907450912, testTime, true)
	require.NoError(err)
	cur, err = Collection().Find(ctx, bson.M{
		"user_id": 907450912,
		"from":    appearance.FromHighness,
		"appearance_id": bson.M{
			"$in": appearanceTypes,
		},
	}, opts)
	require.NoError(err)
	aItem = []UserAppearance{}
	err = cur.All(ctx, &aItem)
	require.NoError(err)
	for i := range aItem {
		assert.Equal(checkAppearanceIDs[i], aItem[i].AppearanceID)
		assert.Equal(testTime, *aItem[i].ExpireTime)
	}
}

func TestClearUserVipAppearance(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserIDs := []int64{1000010, 1000011}
	testVipTypes := []int{appearance.FromNoble, appearance.FromHighness}

	ua1000010 := &UserAppearance{
		UserID: testUserIDs[0],
		From:   testVipTypes[0],
	}
	ua1000011 := &UserAppearance{
		UserID: testUserIDs[1],
		From:   testVipTypes[1],
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, []interface{}{ua1000010, ua1000011})
	require.NoError(err)

	ClearUserVipAppearance(testUserIDs, []int{vip.TypeLiveNoble, vip.TypeLiveHighness})

	filter := bson.M{
		"user_id": bson.M{"$in": testUserIDs},
		"from":    bson.M{"$in": testVipTypes},
	}
	count, err := Collection().CountDocuments(ctx, filter)
	require.NoError(err)
	assert.Zero(count)
}

func TestWearUserMaxNobleAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID1 int64 = 312312321321321
		testUserID2 int64 = 234234234123123
		testUserID3 int64 = 142343423524312
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": bson.A{testUserID1, testUserID2, testUserID3}}})
	assert.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{
		UserAppearance{
			UserID:       testUserID1,
			Type:         appearance.TypeAvatarFrame,
			Status:       StatusWorn,
			From:         appearance.FromNoble,
			AppearanceID: appearance.VipAppearanceID(1, 1, appearance.TypeAvatarFrame),
		},
		UserAppearance{
			UserID:       testUserID2,
			Type:         appearance.TypeCardFrame,
			Status:       StatusWorn,
			From:         appearance.FromNoble,
			AppearanceID: appearance.VipAppearanceID(1, 2, appearance.TypeCardFrame),
		},
	})
	require.NoError(err)

	uvs := []*vip.UserVip{
		{
			UserID: testUserID1,
			Type:   vip.TypeLiveNoble,
			Level:  3,
		},
		{
			UserID: testUserID2,
			Type:   vip.TypeLiveTrialNoble,
			Level:  4,
		},
		{
			UserID: testUserID3,
			Type:   vip.TypeLiveHighness,
			Level:  1,
		},
	}
	cancel = mrpc.SetMock(vip.URLLiveUserLevel, func(any) (any, error) {
		return map[string]interface{}{"data": uvs}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(vip.URLVipList, func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	err = WearUserMaxNobleAppearances([]int64{testUserID1, testUserID2, testUserID3})
	require.NoError(err)

	for _, uv := range uvs {
		uas, err := Find(bson.M{
			"user_id": uv.UserID,
			"from":    bson.M{"$in": bson.A{appearance.FromNoble, appearance.FromHighness}},
			"status":  StatusWorn,
		}, nil)
		require.NoError(err)
		if uv.Type == vip.TypeLiveHighness {
			require.Equal(len(appearance.HighnessAppearanceTypes()), len(uas))
		} else {
			require.Equal(len(appearance.NobleAppearanceTypes(uv.Level)), len(uas))
		}
		for _, ua := range uas {
			appearanceID := appearance.VipAppearanceID(uv.Type, uv.Level, ua.Type)
			assert.Equal(appearanceID, ua.AppearanceID)
		}
	}
}

func TestFindWornIdentityBadges(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	startTime := now.Add(-time.Minute).Unix()
	duration := int64(7 * 24 * 60 * 60)
	expTime := now.Add(time.Duration(duration) * time.Second).Unix()
	userID := int64(10001)

	identityBadge := &UserAppearance{
		AppearanceID:   999,
		UserID:         userID,
		Type:           appearance.TypeIdentityBadge,
		StartTime:      startTime,
		ExpireTime:     &expTime,
		EffectDuration: duration,
		Status:         StatusWorn,
	}

	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	require.NoError(err)

	ClearCache(userID)
	identityBadges, err := FindWornIdentityBadges(userID)
	require.NoError(err)
	assert.Equal(0, len(identityBadges))

	_, err = Collection().InsertOne(ctx, identityBadge)
	require.NoError(err)
	ClearCache(userID)
	identityBadges, err = FindWornIdentityBadges(userID)
	require.NoError(err)
	assert.Equal(1, len(identityBadges))
}

func TestNewBatchTakeOffAppearancesUpdateByID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	startTime := now.Add(-time.Minute).Unix()
	duration := int64(7 * 24 * 60 * 60)
	expTime := now.Add(time.Duration(duration) * time.Second).Unix()
	userID := int64(10001)

	identityBadge := &UserAppearance{
		AppearanceID:   999,
		UserID:         userID,
		Type:           appearance.TypeIdentityBadge,
		StartTime:      startTime,
		ExpireTime:     &expTime,
		EffectDuration: duration,
		Status:         StatusWorn,
	}

	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	require.NoError(err)
	ClearCache(userID)
	identityBadges, err := FindWornIdentityBadges(userID)
	require.NoError(err)
	assert.Equal(0, len(identityBadges))

	_, err = Collection().InsertOne(ctx, identityBadge)
	require.NoError(err)
	ClearCache(userID)
	identityBadges, err = FindWornIdentityBadges(userID)
	require.NoError(err)
	assert.Equal(1, len(identityBadges))

	updates := make([]mongo.WriteModel, 0, 2)
	update := newBatchTakeOffAppearancesUpdateByID(userID, []*UserAppearance{identityBadge})
	if update != nil {
		updates = append(updates, update)
	}
	require.NoError(err)
	if len(updates) > 0 {
		// 保证同一用户的外观操作会同时成功或失败
		err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
			_, err = Collection().BulkWrite(ctx, updates, options.BulkWrite().SetOrdered(true))
			return err
		})
		require.NoError(err)
	}

	ClearCache(userID)
	identityBadges, err = FindWornIdentityBadges(userID)
	require.NoError(err)
	assert.Equal(0, len(identityBadges))
}

func TestNewTakeOffAppearancesUpdates(t *testing.T) {
	t.Run("身份铭牌", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		now := goutil.TimeNow()
		startTime := now.Add(-time.Minute).Unix()
		duration := int64(7 * 24 * 60 * 60)
		expTime := now.Add(time.Duration(duration) * time.Second).Unix()

		identityBadge1 := &appearance.Appearance{
			ID:             999,
			Type:           appearance.TypeIdentityBadge,
			StartTime:      startTime,
			ExpireTime:     &expTime,
			EffectDuration: duration,
		}

		identityBadge2 := &appearance.Appearance{
			ID:             1000,
			Type:           appearance.TypeIdentityBadge,
			StartTime:      startTime,
			ExpireTime:     &expTime,
			EffectDuration: duration,
		}

		userID := int64(10001)
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
		require.NoError(err)
		ClearCache(userID)
		identityBadges, err := FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 0)

		err = AddAppearances(userID, duration, expTime, []*appearance.Appearance{identityBadge1, identityBadge2}, true)
		require.NoError(err)
		ClearCache(userID)
		identityBadges, err = FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 2)

		updates := newTakeOffAppearancesUpdates(userID, []*appearance.Appearance{identityBadge1, identityBadge2})
		require.NoError(err)
		require.NotEmpty(updates)
		_, err = Collection().BulkWrite(ctx, updates)
		require.NoError(err)

		ClearCache(userID)
		identityBadges, err = FindWornIdentityBadges(userID)
		require.NoError(err)
		assert.Len(identityBadges, 1)
		// 早穿上的 999 铭牌被卸下
		assert.Equal(identityBadge2.ID, identityBadges[0].AppearanceID)
	})
	t.Run("普通类型全部卸下", func(t *testing.T) {
		require := require.New(t)
		assert := assert.New(t)

		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		now := goutil.TimeNow()
		startTime := now.Add(-time.Minute).Unix()
		duration := int64(7 * 24 * 60 * 60)
		expTime := now.Add(time.Duration(duration) * time.Second).Unix()
		userID := int64(10002)
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
		require.NoError(err)
		ClearCache(userID)

		// 添加两个头像框外观
		avatarFrame1 := &appearance.Appearance{
			ID:         1001,
			Type:       appearance.TypeAvatarFrame,
			StartTime:  startTime,
			ExpireTime: &expTime,
		}
		avatarFrame2 := &appearance.Appearance{
			ID:         1002,
			Type:       appearance.TypeAvatarFrame,
			StartTime:  startTime,
			ExpireTime: &expTime,
		}

		// 添加并佩戴两个同类型外观
		require.NoError(AddAppearances(userID, startTime, expTime, []*appearance.Appearance{avatarFrame1, avatarFrame2}, true))

		// 验证添加结果
		filter := bson.M{
			"user_id":       userID,
			"type":          appearance.TypeAvatarFrame,
			"status":        StatusWorn,
			"appearance_id": bson.M{"$in": []int64{1001, 1002}},
		}
		count, err := Collection().CountDocuments(ctx, filter)
		require.NoError(err)
		assert.Equal(int64(2), count)

		updates := newTakeOffAppearancesUpdates(userID, []*appearance.Appearance{avatarFrame1, avatarFrame2})
		require.NotEmpty(updates)
		_, err = Collection().BulkWrite(ctx, updates)
		require.NoError(err)

		// 两个头像框都被卸下
		count, err = Collection().CountDocuments(ctx, filter)
		require.NoError(err)
		assert.Zero(count)
	})
}

func TestAddNewBlackCardAppearances(t *testing.T) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testUserID := int64(999999)
	testLevel := 2
	testExpireTime := goutil.TimeNow().Add(30 * 24 * time.Hour).Unix()

	// 准备测试用的黑卡外观数据
	testAppearanceIDs := appearance.AllBlackCardAppearanceIDs(testLevel)
	testAppearanceTypes := appearance.BlackCardAppearanceTypes(testLevel)
	testAppearances := make([]interface{}, 0, len(testAppearanceIDs))
	for i, id := range testAppearanceIDs {
		testAppearances = append(testAppearances, &appearance.Appearance{
			ID:           id,
			Name:         fmt.Sprintf("测试黑卡外观 %d", id),
			Type:         testAppearanceTypes[i], // 使用正确的外观类型
			From:         appearance.FromBlackCard,
			Icon:         fmt.Sprintf("test_icon_%d.png", i),
			Intro:        fmt.Sprintf("测试黑卡外观介绍 %d", i),
			StartTime:    goutil.TimeNow().Unix(),
			ExpireTime:   nil,
			ModifiedTime: goutil.TimeNow().Unix(),
		})
	}

	// 清理并插入测试数据
	_, err := appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": testAppearanceIDs}})
	require.NoError(t, err)
	defer appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": testAppearanceIDs}})

	_, err = appearance.Collection().InsertMany(ctx, testAppearances)
	require.NoError(t, err)

	// 清理用户外观数据
	_, err = Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(t, err)
	defer Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})

	// 测试成功添加黑卡外观的基本功能
	t.Run("SuccessAdd", func(t *testing.T) {
		err := AddNewBlackCardAppearances(testUserID, testLevel, testExpireTime)
		require.NoError(t, err)

		// 验证外观是否正确添加
		filter := bson.M{
			"user_id": testUserID,
			"from":    appearance.FromBlackCard,
		}
		userAppearances, err := Find(filter, nil)
		require.NoError(t, err)
		assert.Len(t, userAppearances, len(testAppearanceIDs))

		// 验证所有外观都是佩戴状态
		for _, ua := range userAppearances {
			assert.Equal(t, StatusWorn, ua.Status)
			assert.Equal(t, testUserID, ua.UserID)
			assert.Equal(t, appearance.FromBlackCard, ua.From)
			assert.Equal(t, testExpireTime, *ua.ExpireTime)
			assert.Contains(t, testAppearanceIDs, ua.AppearanceID)
		}
	})

	// 测试替换现有黑卡外观，验证旧的黑卡外观被删除，新的被添加
	t.Run("ReplaceExisting", func(t *testing.T) {
		// 先添加一些其他等级的黑卡外观
		otherLevel := 1
		otherAppearanceIDs := appearance.AllBlackCardAppearanceIDs(otherLevel)
		otherAppearanceTypes := appearance.BlackCardAppearanceTypes(otherLevel)
		otherTestAppearances := make([]interface{}, 0, len(otherAppearanceIDs))
		for i, id := range otherAppearanceIDs {
			otherTestAppearances = append(otherTestAppearances, &appearance.Appearance{
				ID:           id,
				Name:         fmt.Sprintf("测试其他黑卡外观 %d", id),
				Type:         otherAppearanceTypes[i], // 使用正确的外观类型
				From:         appearance.FromBlackCard,
				Icon:         fmt.Sprintf("test_other_icon_%d.png", i),
				Intro:        fmt.Sprintf("测试其他黑卡外观介绍 %d", i),
				StartTime:    goutil.TimeNow().Unix(),
				ExpireTime:   nil,
				ModifiedTime: goutil.TimeNow().Unix(),
			})
		}

		// 插入其他等级的外观数据
		_, err := appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": otherAppearanceIDs}})
		require.NoError(t, err)
		defer appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": otherAppearanceIDs}})

		_, err = appearance.Collection().InsertMany(ctx, otherTestAppearances)
		require.NoError(t, err)

		// 执行添加新黑卡外观
		err = AddNewBlackCardAppearances(testUserID, testLevel, testExpireTime)
		require.NoError(t, err)

		// 验证旧的黑卡外观被删除
		oldFilter := bson.M{
			"user_id":       testUserID,
			"appearance_id": bson.M{"$in": otherAppearanceIDs},
		}
		oldAppearances, err := Find(oldFilter, nil)
		require.NoError(t, err)
		assert.Len(t, oldAppearances, 0)

		// 验证新的黑卡外观存在
		newFilter := bson.M{
			"user_id":       testUserID,
			"appearance_id": bson.M{"$in": testAppearanceIDs},
		}
		newAppearances, err := Find(newFilter, nil)
		require.NoError(t, err)
		assert.Len(t, newAppearances, len(testAppearanceIDs))
	})
}

func TestRenewBlackCardAppearances(t *testing.T) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testUserID := int64(999999)
	testLevel := 2
	originalExpireTime := goutil.TimeNow().Add(7 * 24 * time.Hour).Unix()
	newExpireTime := goutil.TimeNow().Add(30 * 24 * time.Hour).Unix()

	// 准备测试用的黑卡外观数据
	testAppearanceIDs := appearance.AllBlackCardAppearanceIDs(testLevel)
	testAppearanceTypes := appearance.BlackCardAppearanceTypes(testLevel)
	testAppearances := make([]interface{}, 0, len(testAppearanceIDs))
	for i, id := range testAppearanceIDs {
		testAppearances = append(testAppearances, &appearance.Appearance{
			ID:           id,
			Name:         fmt.Sprintf("测试黑卡外观 %d", id),
			Type:         testAppearanceTypes[i],
			From:         appearance.FromBlackCard,
			Icon:         fmt.Sprintf("test_icon_%d.png", i),
			Intro:        fmt.Sprintf("测试黑卡外观介绍 %d", i),
			StartTime:    goutil.TimeNow().Unix(),
			ExpireTime:   nil,
			ModifiedTime: goutil.TimeNow().Unix(),
		})
	}

	// 清理并插入测试外观数据
	_, err := appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": testAppearanceIDs}})
	require.NoError(t, err)
	defer appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": testAppearanceIDs}})

	_, err = appearance.Collection().InsertMany(ctx, testAppearances)
	require.NoError(t, err)

	// 清理用户外观数据
	_, err = Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(t, err)
	defer Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})

	// 测试成功续期黑卡外观
	t.Run("SuccessRenew", func(t *testing.T) {
		// 先添加黑卡外观
		err := AddNewBlackCardAppearances(testUserID, testLevel, originalExpireTime)
		require.NoError(t, err)

		// 验证外观添加成功
		filter := bson.M{
			"user_id": testUserID,
			"from":    appearance.FromBlackCard,
		}
		userAppearances, err := Find(filter, nil)
		require.NoError(t, err)
		require.Len(t, userAppearances, len(testAppearanceIDs))

		// 验证初始过期时间
		for _, ua := range userAppearances {
			assert.Equal(t, originalExpireTime, *ua.ExpireTime)
		}

		// 执行续期操作
		err = RenewBlackCardAppearances(testUserID, newExpireTime)
		require.NoError(t, err)

		// 验证续期后的过期时间
		userAppearances, err = Find(filter, nil)
		require.NoError(t, err)
		require.Len(t, userAppearances, len(testAppearanceIDs))

		for _, ua := range userAppearances {
			assert.Equal(t, newExpireTime, *ua.ExpireTime)
			assert.Equal(t, StatusWorn, ua.Status)
			assert.Equal(t, appearance.FromBlackCard, ua.From)
			assert.Contains(t, testAppearanceIDs, ua.AppearanceID)
		}
	})

	// 测试续期不存在的黑卡外观（用户没有任何黑卡外观）
	t.Run("RenewNonExistent", func(t *testing.T) {
		nonExistentUserID := int64(888888)

		// 清理该用户的所有外观数据
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": nonExistentUserID})
		require.NoError(t, err)
		defer Collection().DeleteMany(ctx, bson.M{"user_id": nonExistentUserID})

		// 尝试续期不存在的黑卡外观，应该不会报错
		err = RenewBlackCardAppearances(nonExistentUserID, newExpireTime)
		require.NoError(t, err)

		// 验证没有外观被添加
		filter := bson.M{
			"user_id": nonExistentUserID,
			"from":    appearance.FromBlackCard,
		}
		userAppearances, err := Find(filter, nil)
		require.NoError(t, err)
		assert.Len(t, userAppearances, 0)
	})

	// 测试仅续期黑卡外观，不影响用户持有的其他类型外观
	t.Run("RenewOnlyBlackCard", func(t *testing.T) {
		mixedUserID := int64(777777)

		// 清理该用户的所有外观数据
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": mixedUserID})
		require.NoError(t, err)
		defer Collection().DeleteMany(ctx, bson.M{"user_id": mixedUserID})

		// 添加黑卡外观
		err = AddNewBlackCardAppearances(mixedUserID, testLevel, originalExpireTime)
		require.NoError(t, err)

		// 手动添加一个非黑卡外观
		otherExpireTime := goutil.TimeNow().Add(10 * 24 * time.Hour).Unix()
		otherAppearance := &UserAppearance{
			UserID:       mixedUserID,
			AppearanceID: 99999,
			Name:         "测试其他外观",
			Type:         appearance.TypeBadge,
			Status:       StatusWorn,
			From:         appearance.FromCustom,
			StartTime:    goutil.TimeNow().Unix(),
			ExpireTime:   &otherExpireTime,
			ModifiedTime: goutil.TimeNow().Unix(),
		}
		_, err = Collection().InsertOne(ctx, otherAppearance)
		require.NoError(t, err)

		// 执行续期操作
		err = RenewBlackCardAppearances(mixedUserID, newExpireTime)
		require.NoError(t, err)

		// 验证黑卡外观被续期
		blackCardFilter := bson.M{
			"user_id": mixedUserID,
			"from":    appearance.FromBlackCard,
		}
		blackCardAppearances, err := Find(blackCardFilter, nil)
		require.NoError(t, err)
		for _, ua := range blackCardAppearances {
			assert.Equal(t, newExpireTime, *ua.ExpireTime)
		}

		// 验证其他外观没有被影响
		otherFilter := bson.M{
			"user_id":       mixedUserID,
			"appearance_id": 99999,
		}
		otherAppearances, err := Find(otherFilter, nil)
		require.NoError(t, err)
		require.Len(t, otherAppearances, 1)
		assert.Equal(t, otherExpireTime, *otherAppearances[0].ExpireTime)
	})

	// 测试续期操作后是否正确清理了缓存
	t.Run("VerifyCacheClear", func(t *testing.T) {
		cacheUserID := int64(666666)

		// 清理该用户的所有外观数据
		_, err := Collection().DeleteMany(ctx, bson.M{"user_id": cacheUserID})
		require.NoError(t, err)
		defer Collection().DeleteMany(ctx, bson.M{"user_id": cacheUserID})

		// 添加黑卡外观
		err = AddNewBlackCardAppearances(cacheUserID, testLevel, originalExpireTime)
		require.NoError(t, err)

		// 模拟缓存存在的情况
		// 这里虽然无法直接验证缓存清理，但可以确保函数调用了 ClearCache
		err = RenewBlackCardAppearances(cacheUserID, newExpireTime)
		require.NoError(t, err)

		// 验证续期成功，间接验证缓存清理正常
		filter := bson.M{
			"user_id": cacheUserID,
			"from":    appearance.FromBlackCard,
		}
		userAppearances, err := Find(filter, nil)
		require.NoError(t, err)
		for _, ua := range userAppearances {
			assert.Equal(t, newExpireTime, *ua.ExpireTime)
		}
	})
}

func TestShouldAutoWear(t *testing.T) {
	t.Run("ShouldWearWhenNoCurrentAppearance", func(t *testing.T) {
		testNewAppearance := &UserAppearance{
			From: appearance.FromDefault,
		}

		result := ShouldAutoWear(nil, testNewAppearance)

		assert.True(t, result)
	})

	t.Run("ShouldWearWhenNewAppearanceHasHigherPriority", func(t *testing.T) {
		testCurrentAppearance := &UserAppearance{
			From: appearance.FromNoble, // 优先级 1
		}
		testNewAppearance := &UserAppearance{
			From: appearance.FromBlackCard, // 优先级 2
		}

		result := ShouldAutoWear(testCurrentAppearance, testNewAppearance)

		assert.True(t, result)
	})

	t.Run("ShouldWearWhenNewAppearanceHasEqualPriority", func(t *testing.T) {
		testCurrentAppearance := &UserAppearance{
			From: appearance.FromBlackCard, // 优先级 2
		}
		testNewAppearance := &UserAppearance{
			From: appearance.FromBlackCard, // 优先级 2
		}

		result := ShouldAutoWear(testCurrentAppearance, testNewAppearance)

		assert.True(t, result)
	})

	t.Run("ShouldNotWearWhenNewAppearanceHasLowerPriority", func(t *testing.T) {
		testCurrentAppearance := &UserAppearance{
			From: appearance.FromHighness, // 优先级 3
		}
		testNewAppearance := &UserAppearance{
			From: appearance.FromBlackCard, // 优先级 2
		}

		result := ShouldAutoWear(testCurrentAppearance, testNewAppearance)

		assert.False(t, result)
	})

	t.Run("ShouldTestAllPriorityLevels", func(t *testing.T) {
		testCases := []struct {
			name               string
			currentFrom        int
			newFrom            int
			expectedShouldWear bool
		}{
			{"Default to Noble", appearance.FromDefault, appearance.FromNoble, true},
			{"Default to BlackCard", appearance.FromDefault, appearance.FromBlackCard, true},
			{"Default to Highness", appearance.FromDefault, appearance.FromHighness, true},
			{"Noble to BlackCard", appearance.FromNoble, appearance.FromBlackCard, true},
			{"Noble to Highness", appearance.FromNoble, appearance.FromHighness, true},
			{"BlackCard to Highness", appearance.FromBlackCard, appearance.FromHighness, true},
			{"Noble to Default", appearance.FromNoble, appearance.FromDefault, false},
			{"BlackCard to Default", appearance.FromBlackCard, appearance.FromDefault, false},
			{"BlackCard to Noble", appearance.FromBlackCard, appearance.FromNoble, false},
			{"Highness to Default", appearance.FromHighness, appearance.FromDefault, false},
			{"Highness to Noble", appearance.FromHighness, appearance.FromNoble, false},
			{"Highness to BlackCard", appearance.FromHighness, appearance.FromBlackCard, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				testCurrentAppearance := &UserAppearance{
					From: tc.currentFrom,
				}
				testNewAppearance := &UserAppearance{
					From: tc.newFrom,
				}

				result := ShouldAutoWear(testCurrentAppearance, testNewAppearance)

				assert.Equal(t, tc.expectedShouldWear, result)
			})
		}
	})
}
