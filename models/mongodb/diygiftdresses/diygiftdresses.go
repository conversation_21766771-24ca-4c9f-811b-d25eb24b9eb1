package diygiftdresses

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
)

// DiyGiftDress 装扮配置
type DiyGiftDress struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`
	GiftID       int64              `bson:"gift_id"`
	Type         int                `bson:"type"`
	Name         string             `bson:"name"`
	Icon         string             `bson:"icon"`
	Image        string             `bson:"image"`
	Price        int64              `bson:"price"`
	Order        int                `bson:"order"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("diy_gift_dresses")
}

// GetDiyGiftsDressCountMap 定制礼物配置项选项的数量
// 格式：map[giftID]map[dressType]count
func GetDiyGiftsDressCountMap(giftIDs []int64) (map[int64]map[int]int, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{
		bson.M{
			"$match": bson.M{
				"gift_id": bson.M{"$in": giftIDs},
				"order":   bson.M{"$gt": 0},
			},
		},
		bson.M{
			"$group": bson.M{
				"_id": bson.M{
					"gift_id": "$gift_id",
					"type":    "$type",
				},
				"count": bson.M{"$sum": 1},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var diyGiftsDressCountList []struct {
		Group DiyGiftDress `bson:"_id"`
		Count int          `bson:"count"`
	}
	if err = cur.All(ctx, &diyGiftsDressCountList); err != nil {
		return nil, err
	}
	resMap := make(map[int64]map[int]int, len(diyGiftsDressCountList))
	for _, diyGiftsEffect := range diyGiftsDressCountList {
		giftID := diyGiftsEffect.Group.GiftID
		dressType := diyGiftsEffect.Group.Type
		v := resMap[giftID]
		if v == nil {
			resMap[giftID] = map[int]int{dressType: diyGiftsEffect.Count}
		} else {
			v[dressType] = diyGiftsEffect.Count
		}
	}
	return resMap, nil
}

// FindDiyGiftDressMapByType 通过定制礼物装扮类型查询定制礼物
func FindDiyGiftDressMapByType(giftID int64, types []int) (map[int][]*DiyGiftDress, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, bson.M{
		"gift_id": giftID,
		"type":    bson.M{"$in": types},
		"order":   bson.M{"$gt": 0},
	}, options.Find().SetSort(bson.D{{Key: "type", Value: 1}, {Key: "order", Value: 1}}))
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var dresses []*DiyGiftDress
	err = cur.All(ctx, &dresses)
	if err != nil {
		return nil, err
	}
	res := make(map[int][]*DiyGiftDress, len(types))
	if len(dresses) == 0 {
		return res, nil
	}
	for s, e := 0, 0; ; e++ {
		if e == len(dresses) {
			res[dresses[s].Type] = dresses[s:e]
			break
		}
		// dresses 已按照 type 和 order 排序，所以可以按照 type 区间处理
		if dresses[s].Type != dresses[e].Type {
			res[dresses[s].Type] = dresses[s:e]
			s = e
		}
	}
	return res, nil
}
