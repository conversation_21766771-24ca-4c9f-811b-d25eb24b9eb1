package diygiftdresses

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestGetDiyGiftsDressCountMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	dressMap, err := GetDiyGiftsDressCountMap([]int64{2})
	require.NoError(err)
	assert.NotEmpty(dressMap)
	dressTypeMap, ok := dressMap[2]
	require.True(ok)
	count, ok := dressTypeMap[1]
	require.True(ok)
	assert.EqualValues(2, count)
	count, ok = dressTypeMap[2]
	require.True(ok)
	assert.EqualValues(1, count)
}

func TestFindDiyGiftDressMapByType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	col := service.MongoDB.Collection("diy_gift_dresses")
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testGiftID := int64(456)

	_, err := col.DeleteMany(ctx, bson.M{"gift_id": testGiftID})
	require.NoError(err)

	res, err := FindDiyGiftDressMapByType(testGiftID, []int{1, 2})
	require.NoError(err)
	require.NotNil(res)
	assert.Empty(res)

	testData := []interface{}{
		DiyGiftDress{
			GiftID: 456,
			Type:   1,
			Name:   "1_1",
			Order:  1,
		},
		DiyGiftDress{
			GiftID: 456,
			Type:   1,
			Name:   "1_2",
			Order:  2,
		},
		DiyGiftDress{
			GiftID: 456,
			Type:   2,
			Name:   "2_1",
			Order:  1,
		},
	}
	_, err = col.InsertMany(ctx, testData)
	require.NoError(err)

	res, err = FindDiyGiftDressMapByType(testGiftID, []int{1, 2, 3})
	require.NoError(err)
	require.NotNil(res)
	dresses := res[1]
	require.Len(dresses, 2)
	assert.Equal(1, dresses[0].Order)
	assert.Equal(2, dresses[1].Order)
	assert.Len(res[2], 1)
	assert.Empty(res[3])
}
