package liveshows

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 个人场挑战类型
const (
	ShowTypeStar = iota + 1 // 繁星挑战
	ShowTypeMoon            // 朗月挑战
)

// 个人场审核状态
const (
	ShowStatusCheckPending  = iota + 1 // 待审核
	ShowStatusCheckApproved            // 审核通过
	ShowStatusCheckRejected            // 审核拒绝
	ShowStatusCheckCanceled            // 取消
)

// 个人场小窗类型
const (
	ShowWidgetTypeStar = iota + 1 // 繁星挑战小窗
	ShowWidgetTypeMoon            // 朗月挑战小窗
)

// CollectGiftID 个人场收集礼物 ID
const CollectGiftID = 70009

// LiveShow struct
type LiveShow struct {
	// 应该向前端返回 _id, 以便于更新状态
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"live_show_id,omitempty"`
	CreateTime   int64              `bson:"create_time,omitempty" json:"-"`
	ModifiedTime int64              `bson:"modified_time,omitempty" json:"-"`

	CreatorID  int64 `bson:"creator_id" json:"creator_id,omitempty"`
	ShowType   int   `bson:"show_type" json:"show_type,omitempty"` // 个人场类型
	StartTime  int64 `bson:"start_time" json:"start_time,omitempty"`
	EndTime    int64 `bson:"end_time" json:"end_time,omitempty"`
	WidgetType int   `bson:"widget_type" json:"widget_type,omitempty"` // 小窗类型
	Status     int   `bson:"status" json:"status,omitempty"`           // 审核状态

	Point   int64          `bson:"point" json:"point,omitempty"`
	Collect map[string]int `bson:"collect,omitempty" json:"collect,omitempty"`
}

// NewLiveShow new live show
func NewLiveShow(creatorID int64, showType int, startTime, endTime int64) *LiveShow {
	return &LiveShow{
		CreatorID:  creatorID,
		ShowType:   showType,
		WidgetType: showType, // 小窗类型目前与个人场类型相同
		StartTime:  startTime,
		EndTime:    endTime,
		Status:     ShowStatusCheckPending,
	}
}

// Create new live show
func (show *LiveShow) Create(ctx context.Context) error {
	now := goutil.TimeNow().Unix()
	show.CreateTime = now
	show.ModifiedTime = now

	res, err := Collection().InsertOne(ctx, show)
	if err != nil {
		return err
	}

	show.OID = res.InsertedID.(primitive.ObjectID)
	return nil
}

// UpdateStatus 更新审核状态
func (show *LiveShow) UpdateStatus(ctx context.Context, status int) (bool, error) {
	var pass bool
	switch status {
	case ShowStatusCheckApproved:
		pass = show.Status == ShowStatusCheckPending
	case ShowStatusCheckRejected:
		pass = show.Status == ShowStatusCheckPending
	case ShowStatusCheckCanceled:
		pass =
			show.Status == ShowStatusCheckPending || show.Status == ShowStatusCheckApproved
	}
	if !pass {
		return false, nil
	}

	res, err := Collection().UpdateOne(ctx,
		bson.M{
			"_id":    show.OID,
			"status": show.Status,
		},
		bson.M{"$set": bson.M{
			"status":        status,
			"modified_time": goutil.TimeNow().Unix(),
		}},
	)
	if err != nil {
		return false, err
	}

	return res.ModifiedCount == 1, nil
}

// Collection mongo collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_shows")
}

// LiveShowName 个人场名称
func LiveShowName(liveShowType int) string {
	switch liveShowType {
	case ShowTypeStar:
		return "繁星挑战"
	case ShowTypeMoon:
		return "朗月挑战"
	default:
		panic(fmt.Sprintf("个人场类型错误 %d", liveShowType))
	}
}

// FindOne find one live show
func FindOne(m bson.M) (*LiveShow, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var show LiveShow
	err := Collection().FindOne(ctx, m).Decode(&show)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &show, nil
}

// FindAllGoingShowByTime 获取这个时间段内生效中的个人场
func FindAllGoingShowByTime(startTime, endTime time.Time) ([]LiveShow, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, bson.M{
		"status":     ShowStatusCheckApproved,
		"start_time": bson.M{"$lt": endTime.Unix()},
		"end_time":   bson.M{"$gt": startTime.Unix()},
	})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var shows []LiveShow
	err = cur.All(ctx, &shows)
	if err != nil {
		return nil, err
	}

	return shows, nil
}

// MapCreatorIDOngoingShows find ongoing shows by cache
func MapCreatorIDOngoingShows() (map[int64]LiveShow, error) {
	key := keys.KeyOngoingLiveShow0.Format()
	res, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}

	if len(res) != 0 {
		var shows map[int64]LiveShow
		err = json.Unmarshal(res, &shows)
		if err == nil {
			return shows, nil
		}
		logger.Error(err)
		// PASS
	}

	now := goutil.TimeNow()
	// 个人场开始前 30m, 结束后 10m 后仍需返回数据，额外预留 10m 数据
	shows, err := FindAllGoingShowByTime(now.Add(-20*time.Minute), now.Add(40*time.Minute))
	if err != nil {
		return nil, err
	}

	mshows := goutil.ToMap(shows, "CreatorID").(map[int64]LiveShow)
	bytes, err := json.Marshal(mshows)
	if err != nil {
		logger.Error(err)
		return mshows, nil
	}

	err = service.LRURedis.Set(key, bytes, 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return mshows, nil
}
