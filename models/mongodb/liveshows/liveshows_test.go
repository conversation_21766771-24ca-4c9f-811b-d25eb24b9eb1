package liveshows

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestNewLiveShow(t *testing.T) {
	assert := assert.New(t)

	show := NewLiveShow(1, ShowTypeMoon, 1, 2)
	assert.Equal(&LiveShow{
		CreatorID:  1,
		ShowType:   ShowTypeMoon,
		StartTime:  1,
		EndTime:    2,
		WidgetType: ShowWidgetTypeMoon,
		Status:     ShowStatusCheckPending,
	}, show)
}

func TestLiveShowCreateAndUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{
		"creator_id": 1,
	})
	require.NoError(err)

	show := NewLiveShow(1, ShowTypeMoon, 1, 2)
	err = show.Create(ctx)
	require.NoError(err)
	assert.NotEmpty(show.OID)

	ok, err := show.UpdateStatus(ctx, ShowStatusCheckApproved)
	require.NoError(err)
	require.True(ok)

	show, err = FindOne(bson.M{"_id": show.OID})
	require.NoError(err)
	require.NotNil(show)
	assert.Equal(ShowStatusCheckApproved, show.Status)
}

func TestFindAllGoingShowByTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{
		"end_time": bson.M{"$lte": 10},
	})
	require.NoError(err)

	_, err = Collection().InsertMany(ctx, []interface{}{
		LiveShow{Status: ShowStatusCheckApproved, CreatorID: 2, StartTime: 1, EndTime: 2},
		LiveShow{Status: ShowStatusCheckApproved, CreatorID: 2, StartTime: 2, EndTime: 3},
		LiveShow{Status: ShowStatusCheckApproved, CreatorID: 2, StartTime: 2, EndTime: 3},
		LiveShow{Status: ShowStatusCheckApproved, CreatorID: 2, StartTime: 3, EndTime: 4},
		LiveShow{Status: ShowStatusCheckApproved, CreatorID: 2, StartTime: 4, EndTime: 5},
		LiveShow{Status: ShowStatusCheckApproved, CreatorID: 2, StartTime: 4, EndTime: 5},
	})
	require.NoError(err)

	shows, err := FindAllGoingShowByTime(time.Unix(2, 0), time.Unix(4, 0))
	require.NoError(err)
	assert.Equal(3, len(shows))

	shows, err = FindAllGoingShowByTime(time.Unix(1, 0), time.Unix(4, 0))
	require.NoError(err)
	assert.Equal(4, len(shows))

	shows, err = FindAllGoingShowByTime(time.Unix(2, 0), time.Unix(6, 0))
	require.NoError(err)
	assert.Equal(5, len(shows))
}

func TestMapCreatorIDOngoingShows(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	key := keys.KeyOngoingLiveShow0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	_, err := Collection().DeleteMany(ctx, bson.M{
		"end_time": bson.M{"$lte": 3603},
	})
	require.NoError(err)

	show := NewLiveShow(3, ShowTypeMoon, 2, 3)
	err = show.Create(ctx)
	require.NoError(err)
	ok, err := show.UpdateStatus(ctx, ShowStatusCheckApproved)
	require.NoError(err)
	require.True(ok)
	show.Status = ShowStatusCheckApproved

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(2, 0)
	})
	defer goutil.SetTimeNow(nil)

	// db
	shows, err := MapCreatorIDOngoingShows()
	require.NoError(err)
	require.Len(shows, 1)
	assert.Equal(*show, shows[3])

	// cache
	shows, err = MapCreatorIDOngoingShows()
	require.NoError(err)
	require.Len(shows, 1)
	show.CreateTime = 0
	show.ModifiedTime = 0
	assert.Equal(*show, shows[3])
}
