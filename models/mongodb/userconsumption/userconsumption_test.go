package userconsumption

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestAddUserConsumption(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	date := goutil.TimeNow()
	dateUnix := goutil.BeginningOfDay(date).Unix()
	key := keys.KeyUserConsumptionLast29Days2.Format(testUserID, dateUnix)
	require.NoError(service.Redis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	res, err := AddUserConsumption(testUserID, 123, date)
	require.NoError(err)
	assert.EqualValues(123, res)

	res, err = AddUserConsumption(testUserID, 123, date)
	require.NoError(err)
	assert.EqualValues(246, res)

	// 测试没有缓存是否报错
	yesterday := date.AddDate(0, 0, -1)
	res, err = AddUserConsumption(testUserID, 123, yesterday)
	require.NoError(err)
	assert.EqualValues(123, res)

	// 测试有缓存是否被修改时间
	require.NoError(service.Redis.Set(key, 123, 30*time.Hour).Err())
	res, err = AddUserConsumption(testUserID, 123, yesterday)
	require.NoError(err)
	assert.EqualValues(246, res)
	ttl, err := service.Redis.TTL(key).Result()
	require.NoError(err)
	assert.GreaterOrEqual(time.Minute, ttl)
}

func TestFindUserConsumption(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	now := goutil.TimeNow()
	consumption, err := findUserConsumption(testUserID, now.AddDate(0, 0, -50), now)
	require.NoError(err)
	assert.GreaterOrEqual(consumption, int64(0))

	consumption, err = findUserConsumption(testUserID, now, now.AddDate(1, 0, 0))
	require.NoError(err)
	assert.Zero(consumption)
}

func TestFindUserConsumptionInLast29Days(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	date := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyUserConsumptionLast29Days2.Format(testUserID, date.Unix())
	require.NoError(service.Redis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	now := goutil.BeginningOfDay(goutil.TimeNow())
	_, err = Collection().InsertMany(ctx, []interface{}{
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.AddDate(0, 0, -3).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.AddDate(0, 0, -15).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.AddDate(0, 0, -29).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.AddDate(0, 0, -30).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.AddDate(0, 0, -35).Unix()},
	})
	require.NoError(err)
	res, err := FindUserConsumptionInLast29Days(testUserID, date)
	require.NoError(err)
	assert.EqualValues(369, res)

	consumptionSum, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.Equal(res, consumptionSum)
	// 测试从缓存获取数据
	res2, err := FindUserConsumptionInLast29Days(testUserID, date)
	require.NoError(err)
	assert.EqualValues(res, res2)
}

func TestFindUserConsumptionInThisMonth(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(goutil.TimeNow())
	testTimeUnix := nowStampFirstDayOfThisMonth.AddDate(0, 0, 16).Unix()
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTimeUnix, 0)
	})
	defer goutil.SetTimeNow(nil)

	testUserID := int64(3457181)
	date := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyUserConsumptionThisMonth2.Format(testUserID, date.Unix())
	require.NoError(service.Redis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{
		UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, 3).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, 15).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, 16).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, -1).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, -2).Unix()},
	})
	require.NoError(err)
	res, err := FindUserConsumptionInThisMonth(testUserID, date)
	require.NoError(err)
	assert.EqualValues(369, res)

	consumptionSum, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.Equal(res, consumptionSum)

	// 测试从缓存获取数据
	res2, err := FindUserConsumptionInThisMonth(testUserID, date)
	require.NoError(err)
	assert.EqualValues(res, res2)
}

func TestFindPotentialHighnessProgress(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	now := goutil.BeginningOfDay(goutil.TimeNow())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	_, err = Collection().InsertMany(ctx, []interface{}{
		UserConsumption{UserID: testUserID, Consumption: 123, Date: now.Unix()},
	})
	require.NoError(err)

	// 测试没有过期时间记录的情况
	progress, err := FindPotentialHighnessExpireConsumption(testUserID)
	require.NoError(err)
	assert.EqualValues(123, progress.Consumption)
	assert.Zero(progress.Expire)

	firstDate := now.AddDate(0, 0, -29).Unix()
	_, err = Collection().InsertOne(ctx,
		UserConsumption{UserID: testUserID, Consumption: 123, Date: firstDate},
	)
	require.NoError(err)

	// 测试有过期时间记录的情况
	progress, err = FindPotentialHighnessExpireConsumption(testUserID)
	require.NoError(err)
	assert.EqualValues(123, progress.Consumption)
	assert.EqualValues(123, progress.Expire)
}

func TestTimeRange(t *testing.T) {
	assert := assert.New(t)

	assert.Panicsf(func() {
		_, _ = timeRange(goutil.TimeNow(), -3)
	}, "daysAgo error")

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 06, 16, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	startDate, endDate := timeRange(goutil.TimeNow(), 3)
	assert.EqualValues(1655049600, startDate.Unix())
	assert.EqualValues(1655308800, endDate.Unix())
}

func TestFindUserBlackCardSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	testUserID2 := int64(9074508)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{testUserID, testUserID2}}})
	require.NoError(err)

	// 测试用户没有黑卡消费
	res, err := FindUserBlackCardSpend(testUserID)
	require.NoError(err)
	assert.Zero(res)

	// 测试用户有黑卡消费
	timeNow := goutil.TimeNow()
	_, err = Collection().InsertMany(ctx, []interface{}{
		UserConsumption{UserID: testUserID, Consumption: 123, Date: timeNow.Add(-2 * time.Second).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: timeNow.Add(-4 * time.Second).Unix()},
		UserConsumption{UserID: testUserID2, Consumption: 123, Date: timeNow.Add(-4 * time.Second).Unix()},
		UserConsumption{UserID: testUserID, Consumption: 123, Date: timeNow.Add(-6 * time.Second).Unix()},
	})
	require.NoError(err)
	res, err = FindUserBlackCardSpend(testUserID)
	require.NoError(err)
	assert.EqualValues(369, res)
}
