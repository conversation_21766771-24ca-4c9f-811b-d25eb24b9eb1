package userconsumption

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// UserConsumption 用户直播消费
type UserConsumption struct {
	UserID      int64 `bson:"user_id"`
	Date        int64 `bson:"date"` // 消费日期时间戳（秒）
	Consumption int64 `bson:"consumption"`
}

// Collection user_consumption
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("user_consumption")
}

// AddUserConsumption 更新用户当天消费记录
func AddUserConsumption(userID, consumption int64, userConsumptionTime time.Time) (int64, error) {
	todayUnix := goutil.BeginningOfDay(goutil.TimeNow()).Unix()
	dateUnix := goutil.BeginningOfDay(userConsumptionTime).Unix()
	if dateUnix < todayUnix {
		// 增加的消费记录为昨日记录，当日生成的前 29 天消费缓存和当月消费缓存时长改为 1 分钟
		userConsumptionLast29DaysKey := keys.KeyUserConsumptionLast29Days2.Format(userID, todayUnix)
		userConsumptionThisMonthKey := keys.KeyUserConsumptionThisMonth2.Format(userID, todayUnix)
		pipe := service.Redis.TxPipeline()
		pipe.Expire(userConsumptionLast29DaysKey, time.Minute)
		pipe.Expire(userConsumptionThisMonthKey, time.Minute)
		_, err := pipe.Exec()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	after := UserConsumption{}
	err := Collection().FindOneAndUpdate(ctx,
		bson.M{
			"user_id": userID,
			"date":    dateUnix,
		},
		bson.M{
			"$setOnInsert": bson.M{
				"user_id": userID,
				"date":    dateUnix,
			},
			"$inc": bson.M{"consumption": consumption},
		},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	).Decode(&after)
	if err != nil {
		return 0, err
	}
	return after.Consumption, nil
}

// findUserConsumption find user consumption
func findUserConsumption(userID int64, startDate, endDate time.Time) (int64, error) {
	match := bson.M{
		"$match": bson.M{
			"user_id": userID,
			"date":    bson.M{"$gte": startDate.Unix(), "$lt": endDate.Unix()}},
	}
	group := bson.M{
		"$group": bson.M{
			"_id":             nil,
			"consumption_sum": bson.M{"$sum": "$consumption"},
		}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{match, group})
	if err != nil {
		return 0, err
	}
	defer cur.Close(ctx)
	var resArray []struct {
		ConsumptionSum int64 `bson:"consumption_sum"`
	}
	err = cur.All(ctx, &resArray)
	if err != nil {
		return 0, err
	}
	if len(resArray) == 0 {
		return 0, nil
	}
	return resArray[0].ConsumptionSum, nil
}

// FindUserConsumptionInLast29Days 获取用户前 29 天内的消费总额（有缓存），不包含 userConsumptionTime 日消费
func FindUserConsumptionInLast29Days(userID int64, userConsumptionTime time.Time) (int64, error) {
	startDate, endDate := timeRange(userConsumptionTime, 29)
	dateUnix := endDate.Unix()
	key := keys.KeyUserConsumptionLast29Days2.Format(userID, dateUnix)
	consumptionSum, err := service.Redis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	if err == nil {
		return consumptionSum, nil
	}
	consumptionSum, err = findUserConsumption(userID, startDate, endDate)
	if err != nil {
		return 0, err
	}
	// 缓存时间比一天稍长一点
	if err = service.Redis.Set(key, consumptionSum, 30*time.Hour).Err(); err != nil {
		logger.Error(err)
		// PASS
	}
	return consumptionSum, nil
}

// FindUserConsumptionInThisMonth 获取用户本月内的消费总额（有缓存），不包含 userConsumptionTime 日消费
func FindUserConsumptionInThisMonth(userID int64, userConsumptionTime time.Time) (int64, error) {
	// 计算黑卡有效消费的开始时间
	calculateConsumptionStartTime := config.Conf.Params.BlackCard.CalculateConsumptionStartTime
	if userConsumptionTime.Unix() < calculateConsumptionStartTime {
		return 0, nil
	}
	startDate := goutil.BeginningOfMonth(userConsumptionTime)
	endDate := goutil.BeginningOfDay(userConsumptionTime)
	startTime := startDate.Unix()
	if startTime < calculateConsumptionStartTime {
		// 从计算黑卡有效消费的开始时间开始计算有黑卡消费
		startDate = time.Unix(calculateConsumptionStartTime, 0)
	}
	key := keys.KeyUserConsumptionThisMonth2.Format(userID, endDate.Unix())
	totalConsumption, err := service.Redis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	if err == nil {
		return totalConsumption, nil
	}
	totalConsumption, err = findUserConsumption(userID, startDate, endDate)
	if err != nil {
		return 0, err
	}
	// 缓存时间比一天稍长一点
	if err = service.Redis.Set(key, totalConsumption, 30*time.Hour).Err(); err != nil {
		logger.Error(err)
		// PASS
	}
	return totalConsumption, nil
}

// FindUserConsumptionInLast40Days 获取用户 40 天内的消费记录（前 39 天消费 + 当天消费）
func FindUserConsumptionInLast40Days(userID int64) (int64, error) {
	now := goutil.TimeNow()
	startDate, _ := timeRange(now, 39)
	return findUserConsumption(userID, startDate, now)
}

// PotentialHighnessConsumption 潜力上神消费
type PotentialHighnessConsumption struct {
	StartDate   int64
	Expire      int64 // 今晚过期钻石数（第一天的消费）
	Consumption int64
}

// FindPotentialHighnessExpireConsumption 查询潜力上神当日过期和当日消费
func FindPotentialHighnessExpireConsumption(userID int64) (*PotentialHighnessConsumption, error) {
	startDate, endDate := timeRange(goutil.TimeNow(), 29)
	startDateUnix := startDate.Unix()
	endDateUnix := endDate.Unix()
	filter := bson.M{
		"user_id": userID,
		"date": bson.M{
			// 即将过期的数据仅为 29 天前的当日消费
			"$in": bson.A{startDateUnix, endDateUnix},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Find(ctx, filter, options.Find())
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var userConsumptionList []UserConsumption
	err = cur.All(ctx, &userConsumptionList)
	if err != nil {
		return nil, err
	}

	progress := &PotentialHighnessConsumption{
		StartDate: startDateUnix,
	}
	for _, consumption := range userConsumptionList {
		if consumption.Date == startDateUnix {
			progress.Expire = consumption.Consumption
		} else {
			progress.Consumption = consumption.Consumption
		}
	}
	return progress, nil
}

// 根据传入天数获取对应的范围的起始截止日期 0 点的时间
func timeRange(endTime time.Time, daysAgo int) (time.Time, time.Time) {
	if daysAgo < 0 {
		panic("daysAgo error")
	}
	endDate := goutil.BeginningOfDay(endTime)
	startDate := endDate.AddDate(0, 0, -daysAgo)
	return startDate, endDate
}

// FindUserBlackCardSpend 查询用户当月黑卡消费
func FindUserBlackCardSpend(userID int64) (int64, error) {
	now := goutil.TimeNow()
	nowTime := now.Unix()
	// 计算黑卡有效消费的开始时间
	calculateConsumptionStartTime := config.Conf.Params.BlackCard.CalculateConsumptionStartTime
	if nowTime < calculateConsumptionStartTime {
		return 0, nil
	}
	startTime := goutil.BeginningOfMonth(now).Unix()
	if startTime < calculateConsumptionStartTime {
		startTime = calculateConsumptionStartTime
	}
	match := bson.M{
		"$match": bson.M{
			"user_id": userID,
			"date":    bson.M{"$gte": startTime, "$lt": nowTime},
		},
	}
	group := bson.M{
		"$group": bson.M{
			"_id":             nil,
			"consumption_sum": bson.M{"$sum": "$consumption"},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{match, group})
	if err != nil {
		return 0, err
	}
	defer cur.Close(ctx)
	var resArray []struct {
		ConsumptionSum int64 `bson:"consumption_sum"`
	}
	err = cur.All(ctx, &resArray)
	if err != nil {
		return 0, err
	}
	if len(resArray) == 0 {
		return 0, nil
	}
	return resArray[0].ConsumptionSum, nil
}
