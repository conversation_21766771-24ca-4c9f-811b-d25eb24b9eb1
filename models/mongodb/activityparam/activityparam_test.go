package activityparam

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestOngoingJobsParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(3, 0)
	})
	defer cancel()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{
		"event_id": bson.M{"$in": []int64{110, 124, 134, 138, 160}},
	})
	require.NoError(err)

	_, err = Collection().InsertMany(ctx, []any{
		&ActivityParam{
			EventID: 110,
			Jobs: []Job{
				{
					RunTime: 1,
				},
			},
		},
		&ActivityParam{
			EventID: 124,
			Jobs: []Job{
				{
					RunTime: 2,
				},
			},
		},
		&ActivityParam{
			EventID: 134,
			Jobs: []Job{
				{
					RunTime: 3,
				},
			},
		},
		&ActivityParam{
			EventID: 138,
			Jobs: []Job{
				{
					RunTime: 4,
				},
			},
		},
	})
	require.NoError(err)

	now := goutil.TimeNow()
	params, err := OngoingJobsParams(now)
	require.NoError(err)
	require.Len(params, 2)
	assert.EqualValues(134, params[0].EventID)
	assert.EqualValues(138, params[1].EventID)
}
