package activityparam

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
)

// ActivityParam activity_param
type ActivityParam struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"_id"`
	CreateTime   int64              `bson:"create_time" json:"create_time"`
	ModifiedTime int64              `bson:"modified_time" json:"modified_time"`

	EventID int64 `bson:"event_id" json:"event_id"`

	Jobs []Job `bson:"jobs,omitempty" json:"jobs,omitempty"`
}

// Job job
type Job struct {
	URL         string `bson:"url" json:"url"`
	Param       string `bson:"param" json:"param"`       // 数据库里保存的需要是 json 格式的字符串数据
	RunTime     int64  `bson:"run_time" json:"run_time"` // 任务执行时间, 单位秒
	Description string `bson:"description" json:"description"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("activity_params")
}

// OngoingJobsParams 获取还有任务没有结束的活动配置
func OngoingJobsParams(now time.Time) ([]*ActivityParam, error) {
	filter := bson.M{
		"jobs.run_time": bson.M{"$gte": now.Unix()},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var params []*ActivityParam
	cur, err := Collection().Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	defer cur.Close(ctx)
	err = cur.All(ctx, &params)
	if err != nil {
		return nil, err
	}

	return params, nil
}
