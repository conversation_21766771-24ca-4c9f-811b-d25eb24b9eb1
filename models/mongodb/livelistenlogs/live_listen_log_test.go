package livelistenlogs

import (
	"context"
	"sort"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LiveListenLog{}, "_id", "user_id", "accid", "room_id", "catalog_id", "open_log_id",
		"start_time", "last_time", "duration", "ips", "equip_ids", "equips",
		"start_medal_point", "last_medal_point", "create_time", "modified_time")
	kc.Check(Equip{}, "equip_id", "buvid", "version", "os", "ip")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(OnlineTime{}, "start_time", "current_time", "leave_time")
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)
	col := Collection()
	assert.Equal("test_live_listen_logs", col.Name())
}

func TestListUser7DaysListenLiveTotalDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	testUserID := int64(52947378)
	require.NoError(service.LRURedis.Del(keys.KeyUser7DaysListenLiveTotalDuration1.Format(testUserID)).Err())
	_, err := Collection().DeleteMany(context.Background(), bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = Collection().InsertMany(context.Background(), []any{
		LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    10,
			Duration:  7 * time.Hour.Milliseconds(),
		},
		LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    10,
			Duration:  7 * time.Hour.Milliseconds(),
		},
		LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    11,
			Duration:  7 * time.Hour.Milliseconds(),
		},
		LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -11)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    12,
			Duration:  4 * time.Minute.Milliseconds(),
		},
	})
	require.NoError(err)

	results, err := ListUser7DaysListenLiveTotalDuration(testUserID)
	require.NoError(err)
	require.Len(results, 2)
	sort.Slice(results, func(i, j int) bool { return results[i].RoomID > results[j].RoomID })
	assert.EqualValues(11, results[0].RoomID)
	assert.EqualValues(7*time.Hour.Milliseconds(), results[0].TotalDuration)
	assert.EqualValues(10, results[1].RoomID)
	assert.EqualValues(14*time.Hour.Milliseconds(), results[1].TotalDuration)

	results, err = ListUser7DaysListenLiveTotalDuration(testUserID)
	require.NoError(err)
	require.Len(results, 2)
	sort.Slice(results, func(i, j int) bool { return results[i].RoomID > results[j].RoomID })
	assert.EqualValues(11, results[0].RoomID)
	assert.EqualValues(7*time.Hour.Milliseconds(), results[0].TotalDuration)
	assert.EqualValues(10, results[1].RoomID)
	assert.EqualValues(14*time.Hour.Milliseconds(), results[1].TotalDuration)
}

func TestIsUserAppListenLogDurationReached(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(907450)
	testUserID1 := int64(9074507)
	testUserID2 := int64(9074508)
	testUserID3 := int64(9074509)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{
		"$in": []int64{testUserID, testUserID1, testUserID2, testUserID3},
	}})
	require.NoError(err)

	reached, err := IsUserAppListenLogDurationReached(testUserID, liveListenLogMinDurationPreRecord, nil)
	require.NoError(err)
	assert.Zero(reached)

	testEquipID := "de778e88-86f2-11ef-b667-5809a8708f52"
	testBUVID := "00a233d2-b7ae-11ef-951f-155b72b8d081"
	_, err = Collection().InsertMany(ctx, []any{
		&LiveListenLog{
			UserID:   testUserID,
			Duration: 3000,
			Equips: []*Equip{
				{EquipID: &testEquipID},
			},
		},
		&LiveListenLog{
			UserID:   testUserID1,
			Duration: NewUserRewardLimitDuration - 1,
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:   testUserID2,
			Duration: NewUserRewardLimitDuration,
			Equips: []*Equip{
				{EquipID: &testEquipID},
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:   testUserID3,
			Duration: NewUserRewardLimitDuration + 1,
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
	})
	require.NoError(err)

	// 测试没有 APP 收听记录
	reached, err = IsUserAppListenLogDurationReached(testUserID, NewUserRewardLimitDuration, nil)
	require.NoError(err)
	assert.False(reached)

	// 测试 APP 收听时长小于限制时长
	reached, err = IsUserAppListenLogDurationReached(testUserID1, NewUserRewardLimitDuration, nil)
	require.NoError(err)
	assert.False(reached)

	// 测试 APP 收听时长等于限制时长
	reached, err = IsUserAppListenLogDurationReached(testUserID2, NewUserRewardLimitDuration, nil)
	require.NoError(err)
	assert.True(reached)

	// 测试 APP 收听时长大于限制时长
	reached, err = IsUserAppListenLogDurationReached(testUserID3, NewUserRewardLimitDuration, nil)
	require.NoError(err)
	assert.True(reached)

	today := goutil.BeginningOfDay(goutil.TimeNow())
	_, err = Collection().InsertMany(ctx, []any{
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  NewUserRewardLimitDuration - 1,
			StartTime: goutil.TimeUnixMilli(today.Add(-time.Hour).UnixMilli()),
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  NewUserRewardLimitDuration,
			StartTime: goutil.TimeUnixMilli(today.Add(time.Hour).UnixMilli()),
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:    testUserID1,
			Duration:  NewUserRewardLimitDuration,
			StartTime: goutil.TimeUnixMilli(today.Add(-time.Hour).UnixMilli()),
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:    testUserID1,
			Duration:  NewUserRewardLimitDuration,
			StartTime: goutil.TimeUnixMilli(today.Add(time.Hour).UnixMilli()),
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
	})
	require.NoError(err)

	// 测试用户在当日 0 点前在 App 收听符合新客要求
	reached, err = IsUserAppListenLogDurationReached(testUserID, NewUserRewardLimitDuration, &today)
	require.NoError(err)
	assert.False(reached)

	// 测试用户在当日 0 点前在 App 收听不符合新客要求
	reached, err = IsUserAppListenLogDurationReached(testUserID1, NewUserRewardLimitDuration, &today)
	require.NoError(err)
	assert.True(reached)
}

func TestUserTodayAppListenDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	duration, err := UserTodayAppListenDuration(testUserID)
	require.NoError(err)
	assert.Nil(duration)

	testEquipID := "de778e88-86f2-11ef-b667-5809a8708f52"
	testBUVID := "00a233d2-b7ae-11ef-951f-155b72b8d081"
	today := goutil.BeginningOfDay(goutil.TimeNow())
	_, err = Collection().InsertMany(ctx, []any{
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  3000,
			StartTime: goutil.TimeUnixMilli(today.Add(-time.Hour).UnixMilli()),
			Equips: []*Equip{
				{EquipID: &testEquipID},
			},
		},
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  1000,
			StartTime: goutil.TimeUnixMilli(today.UnixMilli()),
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  2000,
			StartTime: goutil.TimeUnixMilli(today.Add(time.Hour).UnixMilli()),
			Equips: []*Equip{
				{EquipID: &testEquipID},
				{BUVID: &testBUVID},
			},
		},
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  2000,
			StartTime: goutil.TimeUnixMilli(today.Add(time.Hour).UnixMilli()),
		},
		&LiveListenLog{
			UserID:    testUserID,
			Duration:  2000,
			StartTime: goutil.TimeUnixMilli(today.AddDate(0, 0, 1).UnixMilli()),
			Equips: []*Equip{
				{BUVID: &testBUVID},
			},
		},
	})
	require.NoError(err)

	duration, err = UserTodayAppListenDuration(testUserID)
	require.NoError(err)
	require.NotNil(duration)
	assert.EqualValues(3000, *duration)
}

func TestKeyRoomOnline(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("test_rooms/online/123", KeyRoomOnline(123))
}

func TestDelKeyRoomOnline(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(147852)
	key := KeyRoomOnline(roomID)
	require.NoError(service.Redis.HSet(key, "guest-test", tutil.SprintJSON(OnlineTime{})).Err())
	DelKeyRoomOnline(roomID)
	val, err := service.Redis.HLen(key).Result()
	require.NoError(err)
	assert.Zero(val)
}

func TestIsNewUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testNewUserID = int64(999999)
		now           = goutil.TimeNow()
	)

	beginTime := goutil.BeginningOfDay(now)
	key := keys.KeyUserDailyTaskIsNewStatus2.Format(testNewUserID, beginTime.Format(goutil.TimeFormatYMDWithNoSpace))
	require.NoError(service.LRURedis.Del(key).Err())

	isNew, err := IsNewUser(testNewUserID)
	require.NoError(err)
	assert.True(isNew)

	require.NoError(service.LRURedis.Set(key, 0, time.Minute).Err())
	isNew, err = IsNewUser(testNewUserID)
	require.NoError(err)
	assert.False(isNew)
}
