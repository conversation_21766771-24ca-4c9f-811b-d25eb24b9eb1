package livelistenlogs

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// NewUserRewardLimitDuration 新用户奖励限制时长 单位：毫秒
	NewUserRewardLimitDuration = 5 * 60 * 1000

	liveListenLogMinDurationPreRecord = 1000 // 单条直播收听记录最短时间，单位：毫秒
)

// LiveListenLog 收听日志
type LiveListenLog struct {
	OID primitive.ObjectID `bson:"_id,omitempty"`

	UserID          int64                `bson:"user_id"`
	AccID           string               `bson:"accid"`
	RoomID          int64                `bson:"room_id"`
	CatalogID       int64                `bson:"catalog_id"`
	OpenLogID       *primitive.ObjectID  `bson:"open_log_id,omitempty"`
	StartTime       goutil.TimeUnixMilli `bson:"start_time"`        // 毫秒时间戳
	StartMedalPoint int64                `bson:"start_medal_point"` // 勋章亲密度记录
	LastTime        goutil.TimeUnixMilli `bson:"last_time"`         // 毫秒时间戳
	LastMedalPoint  int64                `bson:"last_medal_point"`
	Duration        int64                `bson:"duration"` // TODO: 因为不是时间戳，所以没用 UnixMilli

	// omitempty 防止 $addToSet 失败
	IPs      []string `bson:"ips,omitempty"`       // ip 一般都会传，特殊情况比如测试可能不传
	EquipIDs []string `bson:"equip_ids,omitempty"` // web 没有 equip_id

	Equips []*Equip `bson:"equips,omitempty"`

	CreateTime   time.Time `bson:"create_time"`
	ModifiedTime time.Time `bson:"modified_time"`
}

// OnlineTime redis 用
type OnlineTime struct {
	StartTime   goutil.TimeUnixMilli `json:"start_time"`
	CurrentTime goutil.TimeUnixMilli `json:"current_time"`
	LeaveTime   goutil.TimeUnixMilli `json:"leave_time"`
}

// Equip 设备信息
type Equip struct {
	EquipID *string `bson:"equip_id"` // web 为 nil
	BUVID   *string `bson:"buvid"`    // 没有则为 nil
	Version *string `bson:"version"`  // 客户端版本，web 为 nil
	IP      string  `bson:"ip"`

	OS goutil.Platform `bson:"os"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_listen_logs")
}

// ListenTotalDuration 收听时长
type ListenTotalDuration struct {
	RoomID        int64 `bson:"_id" json:"room_id"`
	TotalDuration int64 `bson:"total_duration" json:"total_duration"` // 毫秒时间戳
}

// ListUser7DaysListenLiveTotalDuration 用户 7 天内各个直播间收听总时长
func ListUser7DaysListenLiveTotalDuration(userID int64) ([]*ListenTotalDuration, error) {
	key := keys.KeyUser7DaysListenLiveTotalDuration1.Format(userID)
	res, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	if err == nil {
		var results []*ListenTotalDuration
		err = json.Unmarshal(res, &results)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return results, nil
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	today := goutil.BeginningOfDay(goutil.TimeNow())
	cur, err := Collection().Aggregate(ctx,
		bson.A{
			bson.M{
				"$match": bson.M{
					"user_id": userID,
					"start_time": bson.M{
						"$gte": today.AddDate(0, 0, -7).UnixMilli(),
						"$lt":  today.UnixMilli(),
					},
				},
			},
			bson.M{
				"$group": bson.M{
					"_id":            "$room_id",
					"total_duration": bson.M{"$sum": "$duration"},
				},
			},
			bson.M{
				"$match": bson.M{
					"total_duration": bson.M{"$gte": (5 * time.Minute).Milliseconds()}, // 大于等于 5 分钟
				},
			},
		},
	)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var results []*ListenTotalDuration
	err = cur.All(ctx, &results)
	if err != nil {
		return nil, err
	}
	res, err = json.Marshal(results)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, res, 30*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return results, nil
}

// IsUserAppListenLogDurationReached 用户在 APP 收听总时长是否达到限制
func IsUserAppListenLogDurationReached(userID, limitDuration int64, beforeDate *time.Time) (bool, error) {
	filter := bson.M{
		"user_id": userID,
		"equips": bson.M{
			"$elemMatch": bson.M{
				"buvid": bson.M{"$ne": nil},
			},
		},
	}
	if beforeDate != nil {
		filter["start_time"] = bson.M{
			"$lt": beforeDate.UnixMilli(),
		}
	}
	match := bson.M{"$match": filter}
	sort := bson.M{
		"$sort": bson.M{
			"start_time": -1,
		},
	}
	limit := bson.M{
		// 按照每条收听记录最短 1 秒，(新客限制在客户端收听限制时长/最短收听记录时长)+1 计算查询条数
		"$limit": (NewUserRewardLimitDuration / liveListenLogMinDurationPreRecord) + 1,
	}
	group := bson.M{
		"$group": bson.M{
			"_id": nil,
			"total_duration": bson.M{
				"$sum": bson.M{
					"$max": bson.A{"$duration", liveListenLogMinDurationPreRecord},
				},
			},
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{match, sort, limit, group})
	if err != nil {
		return false, err
	}
	defer cur.Close(ctx)
	var resArray []struct {
		TotalDuration int64 `bson:"total_duration"` // 单位：毫秒
	}
	err = cur.All(ctx, &resArray)
	if err != nil {
		return false, err
	}
	if len(resArray) == 0 {
		return false, nil
	}

	return resArray[0].TotalDuration >= limitDuration, nil
}

// UserTodayAppListenDuration 用户今日在 App 收听时长
func UserTodayAppListenDuration(userID int64) (*int64, error) {
	today := goutil.BeginningOfDay(goutil.TimeNow())
	match := bson.M{
		"$match": bson.M{
			"user_id": userID,
			"start_time": bson.M{
				"$gte": today.UnixMilli(),
				"$lt":  today.AddDate(0, 0, 1).UnixMilli(),
			},
			"equips": bson.M{
				"$elemMatch": bson.M{
					"buvid": bson.M{"$ne": nil},
				},
			},
		},
	}
	group := bson.M{
		"$group": bson.M{
			"_id": nil,
			"total_duration": bson.M{
				"$sum": "$duration",
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{match, group})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	// 用户今日第一次进直播间没有上报心跳，时长为 0
	var resArray []struct {
		TotalDuration int64 `bson:"total_duration"` // 单位：毫秒
	}
	err = cur.All(ctx, &resArray)
	if err != nil {
		return nil, err
	}
	if len(resArray) == 0 {
		return nil, nil
	}
	return &resArray[0].TotalDuration, nil
}

// KeyRoomOnline redis online key
func KeyRoomOnline(roomID int64) string {
	return keys.KeyRoomsOnline1.Format(roomID)
}

// DelKeyRoomOnline 删除 roomOnline 缓存
// NOTICE: 此情况不会对缓存中存入的用户在 mongodb 中缓存的收听日志、勋章任务、等级任务等有影响
func DelKeyRoomOnline(roomID int64) {
	key := KeyRoomOnline(roomID)
	// TODO: 记录用户收听日志 live_listen_log 目前会有 5 分钟左右的误差
	err := service.Redis.Del(key).Err()
	if err != nil {
		logger.Error(err)
		return
	}
}

// IsNewUser 判断是否是新用户
// TODO: 新用户的计算规则替换成新口径
func IsNewUser(userID int64) (bool, error) {
	beginTime := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyUserDailyTaskIsNewStatus2.Format(userID, beginTime.Format(util.TimeFormatYMDWithNoSpace))
	isNew, err := service.LRURedis.Get(key).Int()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return false, err
	}
	if err == nil {
		return util.IntToBool(isNew), nil
	}

	reached, err := IsUserAppListenLogDurationReached(userID, NewUserRewardLimitDuration, &beginTime)
	if err != nil {
		return false, err
	}
	err = service.LRURedis.Set(key, util.BoolToInt(!reached), 24*time.Hour).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return !reached, nil
}
