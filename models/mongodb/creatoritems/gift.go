package creatoritems

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	sqlgift "github.com/MiaoSiLa/live-service/models/mysql/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MaoerWalletUserID 猫耳娘的零钱袋用户 ID
const MaoerWalletUserID int64 = 2939325

// Adder 礼物 adder
// NOTICE: 逻辑同 useritems.Adder
type Adder struct {
	userID int64
	now    time.Time

	// 礼物红包使用
	transactionID int64
	context       string

	Adds []CreatorItem
}

// NewAdder new adder
func NewAdder(userID int64, giftCap int) *Adder {
	return &Adder{
		userID: userID,
		now:    goutil.TimeNow(),
		Adds:   make([]CreatorItem, 0, giftCap),
	}
}

// NewTransactionAdder new transaction Adder
func NewTransactionAdder(userID int64, transactionID int64, context string, giftCap int) *Adder {
	return &Adder{
		userID:        userID,
		transactionID: transactionID,
		context:       context,
		now:           goutil.TimeNow(),
		Adds:          make([]CreatorItem, 0, giftCap),
	}
}

// Append append 待添加的背包礼物
func (adder *Adder) Append(g *gift.Gift, num int64, startTime, endTime int64) *Adder {
	adder.Adds = append(adder.Adds, CreatorItem{
		CreateTime:   adder.now.Unix(),
		ModifiedTime: adder.now.Unix(),
		Type:         TypeFromGiftType(g.Type),
		CreatorID:    adder.userID,
		StartTime:    startTime,
		EndTime:      endTime,

		GiftID:  g.GiftID,
		Num:     num,
		GainNum: num,

		TransactionID: adder.transactionID,
		Context:       adder.context,
	})
	return adder
}

// Add 将背包礼物下发给主播
func (adder *Adder) Add() error {
	if len(adder.Adds) == 0 {
		return nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	adds := make([]interface{}, len(adder.Adds))
	for i := range adder.Adds {
		adds[i] = adder.Adds[i]
	}
	_, err := Collection().InsertMany(ctx, adds)
	if err != nil {
		return err
	}
	adder.Adds = make([]CreatorItem, 0)
	return nil
}

// AddGiftToCreators 为多个主播添加礼物，可用于给单个主播发礼物
func AddGiftToCreators(creatorIDs []int64, g *gift.Gift, num int64, startTime, endTime int64) error {
	inserts := make([]interface{}, len(creatorIDs))
	now := goutil.TimeNow()
	for i := range creatorIDs {
		inserts[i] = &CreatorItem{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			Type:         TypeFromGiftType(g.Type),
			CreatorID:    creatorIDs[i],
			StartTime:    startTime,
			EndTime:      endTime,

			GiftID:  g.GiftID,
			Num:     num,
			GainNum: num,
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, inserts)
	return err
}

// CountGiftNum 计算某礼物数量
func CountGiftNum(creatorID, giftID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	type m bson.M
	cur, err := Collection().Aggregate(ctx, []m{
		{
			"$match": setValidTimeFilter(bson.M{
				"type":       m{"$in": []int{TypeFreeGift, TypeRebateGift}},
				"creator_id": creatorID,
				"gift_id":    giftID,
				"num":        m{"$gt": 0},
			}, now.Unix()),
		},
		{
			"$group": m{"_id": 1, "count": m{"$sum": "$num"}},
		},
	})
	if err != nil {
		return 0, nil
	}
	defer cur.Close(ctx)
	var count []*struct {
		Count int64 `bson:"count"`
	}
	err = cur.All(ctx, &count)
	if err != nil || len(count) == 0 {
		return 0, err
	}

	return count[0].Count, nil
}

// UnsetGift 清空礼物
func UnsetGift(creatorID, giftID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	type m bson.M
	_, err := Collection().UpdateMany(ctx, setValidTimeFilter(bson.M{
		"type":       m{"$in": []int{TypeFreeGift, TypeRebateGift}},
		"creator_id": creatorID,
		"gift_id":    giftID,
		"num":        m{"$ne": 0}}, now.Unix()),
		m{"$set": m{"num": 0, "modified_time": now.Unix()}})
	return err
}

// GiftSender 背包礼物送礼器
// TODO: 考虑与用户背包逻辑整合到一起
type GiftSender struct {
	RoomID     int64
	CreatorID  int64
	SendUserID int64
	Gift       *gift.Gift
	Num        int64
	OpenLogID  *primitive.ObjectID
	C          goutil.UserContext
}

var errGiftNum = errors.New("gift num is not enough")

// Send 送礼
// 返回是否送出，剩余礼物数量，订单号，error
func (s GiftSender) Send() (bool, int64, []int64, error) {
	if s.Num <= 0 {
		panic("num must greater than 0")
	}
	now := goutil.TimeNow().Unix()
	creatorItems, err := Find(setValidTimeFilter(bson.M{
		"creator_id": s.CreatorID,
		"gift_id":    s.Gift.GiftID,
		"num":        bson.M{"$gt": 0},
	}, now), options.Find().SetSort(bson.M{"end_time": 1}))
	if err != nil {
		return false, 0, nil, err
	}
	var countBefore int64
	for i := range creatorItems {
		countBefore += creatorItems[i].Num
	}
	if countBefore < s.Num {
		return false, countBefore, nil, nil
	}

	unlock := lockSendCreatorGift(s.CreatorID, s.Gift.GiftID)
	if unlock == nil {
		return false, countBefore, nil, nil
	}
	defer unlock()

	updates := make([]mongo.WriteModel, 0, len(creatorItems))
	gc := make([]userapi.Gift, 0, len(creatorItems))
	rebateGiftNum := int64(0)
	num := s.Num // 待送出的礼物数量
	var g *sqlgift.Gift
	for i := 0; i < len(creatorItems) && num > 0; i++ {
		// numInc 减少的数量
		// NOTICE: numInc 是负数
		numInc := -creatorItems[i].Num
		if num < creatorItems[i].Num {
			numInc = -num
		}
		num += numInc // 减少待送出的礼物数量
		updateOne := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": creatorItems[i].OID, "num": bson.M{"$gte": -numInc}}).
			SetUpdate(bson.M{
				"$inc": bson.M{"num": numInc},
				"$set": bson.M{"modified_time": now},
			})
		updates = append(updates, updateOne)
		switch creatorItems[i].Type {
		case TypeRebateGift:
			if g == nil {
				g, err = sqlgift.FindGift(s.Gift.GiftID)
				if err != nil {
					return false, 0, nil, err
				}
				if g == nil {
					logger.WithFields(logger.Fields{
						"gift_id": s.Gift.GiftID,
					}).Error("user item gift not found")
					return false, 0, nil, nil
				}
			}
			if creatorItems[i].TransactionID == 0 {
				rebateGiftNum -= numInc // numInc 是负数
			} else {
				gc = append(gc, userapi.Gift{
					ID:      s.Gift.GiftID,
					Title:   g.Name,
					Price:   s.Gift.Price,
					Num:     -numInc, // numInc 是负数
					Context: creatorItems[i].Context,
				})
			}
		}
	}

	transactionIDs := make([]int64, 0, 2)
	err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		res, sessErr := Collection().BulkWrite(ctx, updates)
		if sessErr != nil {
			return sessErr
		}
		if res.ModifiedCount != int64(len(updates)) {
			return errGiftNum
		}
		// TODO: 将订单传给 missevan-app
		if rebateGiftNum != 0 {
			rebateResp, sessErr := userapi.SendRebateGift(s.SendUserID, s.CreatorID,
				s.Gift.GiftID, int(rebateGiftNum), s.OpenLogID, userapi.NewUserContext(s.C))
			if sessErr != nil {
				return sessErr
			}
			transactionIDs = append(transactionIDs, rebateResp.TransactionID)
		}

		if len(gc) != 0 {
			rpcResp, sessErr := userapi.SendRebateGifts(s.SendUserID, s.CreatorID,
				gc, s.OpenLogID, userapi.NewUserContext(s.C))
			if sessErr != nil {
				return sessErr
			}
			transactionIDs = append(transactionIDs, rpcResp.TransactionID)
		}
		return nil
	})
	if err != nil {
		if err == errGiftNum {
			return false, countBefore, nil, nil
		}
		return false, countBefore, nil, err
	}
	return true, countBefore - s.Num, transactionIDs, nil
}

// lockSendCreatorGift 返回 unlock 函数，返回 nil 说明设置共享锁超时
func lockSendCreatorGift(creatorID, giftID int64) (unlock func()) {
	key := keys.LockCreatorBackpackGift2.Format(creatorID, giftID)
	times := 10 // 请求次数
	for i := 0; i < 10; i++ {
		ok, err := service.Redis.SetNX(key, "1", 10*time.Second).Result()
		if err != nil {
			logger.Error(err)
			return nil
		}
		if ok {
			return func() {
				err := service.Redis.Del(key).Err()
				if err != nil {
					logger.Error(err)
				}
			}
		}
		if i != times-1 {
			// 最后一次不需要等待
			<-time.After(100 * time.Millisecond) // 100 毫秒后再次尝试
		}
	}
	return nil
}
