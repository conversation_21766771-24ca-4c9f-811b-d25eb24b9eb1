package creatoritems

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewTransactionAdder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 初始化
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1234567890, 0)
	})
	defer goutil.SetTimeNow(nil)
	adder := NewTransactionAdder(135, 123, "test", 2)
	require.Equal(&Adder{
		userID:        135,
		transactionID: 123,
		context:       "test",
		now:           goutil.TimeNow(),
		Adds:          []CreatorItem{},
	}, adder)

	// 增加待添加礼物
	g := &gift.Gift{GiftID: 456, Type: gift.TypeRebate}
	adder = adder.Append(g, 1, 123, 999)
	require.Len(adder.Adds, 1)
	assert.Equal(TypeRebateGift, adder.Adds[0].Type)

	// 添加礼物到数据库
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	_, err := col.DeleteMany(ctx, bson.M{"creator_id": adder.userID, "gift_id": g.GiftID})
	require.NoError(err)
	require.NoError(adder.Add())
	assert.Empty(adder.Adds)
	require.NoError(adder.Add(), "测试空数据情况")
	assert.NoError(col.FindOne(ctx, bson.M{"creator_id": adder.userID, "gift_id": g.GiftID}).Err())
}

func TestAddGiftToCreators(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{"creator_id": 12, "gift_id": 30001})
	require.NoError(err)

	creators := []int64{12}
	g, err := gift.FindShowingGiftByGiftID(30001)
	require.NoError(err)
	now := goutil.TimeNow().Unix()
	err = AddGiftToCreators(creators, g, 1, now, now+10)
	require.NoError(err)

	list, err := FindBackpackItems(12, nil)
	require.NoError(err)
	require.GreaterOrEqual(len(list), 1)
	assert.True(goutil.Includes(len(list), func(i int) bool {
		return list[i].GiftID == 30001 && list[i].TimeLeft <= 10
	}))
}

func TestCountGiftNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorID := int64(12)
	giftID := int64(30001)

	// 与 TestAddGiftToCreators 公用数据
	num, err := CountGiftNum(creatorID, giftID)
	require.NoError(err)
	assert.Equal(int64(1), num)

	num, err = CountGiftNum(creatorID, giftID+1)
	require.NoError(err)
	assert.Zero(num)
}

func TestUnsetGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorID := int64(12)
	giftID := int64(-23)

	require.NoError(UnsetGift(creatorID, giftID))

	num, err := CountGiftNum(creatorID, giftID)
	require.NoError(err)
	assert.Zero(num)
}
