package creatoritems

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBackpackItemTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(BackpackItem{}, "type",
		"gift_id", "comboable", "price", "num", "allowed_nums",
		"name", "icon_url", "icon_active_url", "intro", "intro_icon_url", "intro_open_url", "label_icon_url", "time_left")
}

func initBackpackGift() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	g := gift.Gift{
		GiftID:    301,
		Name:      "猫粮",
		NameClean: "猫粮",
		Icon:      "gifts/icons/007.png",
		Type:      gift.TypeFree,
		Point:     1,
		Order:     301,
		AddedTime: time.Unix(0, 0),
	}
	err := gift.Collection().FindOneAndUpdate(ctx, bson.M{"gift_id": 301},
		bson.M{"$set": &g}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil {
		logger.Fatal(err)
	}

	g = gift.Gift{
		GiftID:    302,
		Name:      "夏有凉风",
		NameClean: "夏有凉风",
		Icon:      "gifts/icons/302.png",
		LabelIcon: "oss://live/gifts/label/icons/302.png",
		Type:      gift.TypeFree,
		Point:     1,
		Order:     302,
		AddedTime: time.Unix(0, 0),
	}
	err = gift.Collection().FindOneAndUpdate(ctx, bson.M{"gift_id": 302},
		bson.M{"$set": &g}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil {
		logger.Error(err)
	}
}

func TestFindBackpackItems(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	initBackpackGift()
	g, err := gift.FindShowingGiftByGiftID(302)
	require.NoError(err)
	now := goutil.TimeNow()
	require.NoError(AddGiftToCreators([]int64{9998}, g, 10, now.Unix(), now.Unix()+1000))

	findItem := func(bp []BackpackItem) *BackpackItem {
		for i := range bp {
			if bp[i].GiftID == 302 {
				return &bp[i]
			}
		}
		return nil
	}

	e := &goutil.Equipment{
		FromApp: false,
	}
	bp, err := FindBackpackItems(9998, e)
	require.NoError(err)
	item := findItem(bp)
	require.NotNil(item)
	assert.GreaterOrEqual(item.Num, int64(10))
	assert.NotContains(item.IconURL, "web")
	assert.NotEmpty(item.LabelIcon)

	e.FromApp = true
	bp, err = FindBackpackItems(9998, e)
	require.NoError(err)
	item = findItem(bp)
	require.NotNil(item)
	assert.NotContains(item.IconURL, "web")
	assert.NotEmpty(item.LabelIcon)
}
