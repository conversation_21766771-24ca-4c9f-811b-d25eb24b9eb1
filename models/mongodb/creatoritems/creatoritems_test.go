package creatoritems

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)

	kc.Check(CreatorItem{}, "_id", "create_time", "modified_time",
		"type", "creator_id", "start_time", "end_time",
		"gift_id", "num", "gain_num", "transaction_id", "context")
}

func TestIsBackpackGift(t *testing.T) {
	assert := assert.New(t)

	g := &gift.Gift{Type: gift.TypeNormal}
	assert.False(IsBackpackGift(g))
	g.Type = gift.TypeFree
	assert.True(IsBackpackGift(g))
	g.Type = gift.TypeRebate
	assert.True(IsBackpackGift(g))
}

func TestTypeFromGiftType(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(TypeFreeGift, TypeFromGiftType(gift.TypeFree))
	assert.Equal(TypeRebateGift, TypeFromGiftType(gift.TypeRebate))

	assert.PanicsWithValue("不支持的礼物类型: -1", func() {
		TypeFromGiftType(-1)
	})
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userItems, err := Find(bson.M{}, nil)
	require.NoError(err)
	require.NotEmpty(userItems)

	li2, err := Find(bson.M{"_id": userItems[0].OID}, options.Find().SetProjection(bson.M{"type": 1}))
	require.NoError(err)
	require.Len(li2, 1)
	assert.Equal(userItems[0].OID, li2[0].OID)
	assert.Equal(userItems[0].Type, li2[0].Type)
	assert.Zero(li2[0].GiftID)
}

func TestSetValidTimeFilter(t *testing.T) {
	assert := assert.New(t)

	f1 := bson.M{}
	nowUnix := int64(123)
	f2 := setValidTimeFilter(f1, nowUnix)
	assert.Equal(f1, f2)
	assert.Equal(bson.M{"$lte": nowUnix}, f1["start_time"])
	assert.Equal(bson.M{"$gt": nowUnix}, f1["end_time"])
}
