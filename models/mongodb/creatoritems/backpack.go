package creatoritems

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 背包中的物品类型
const (
	BPItemTypeGift = iota + 1 // 背包物品礼物
)

// BackpackItem 主播在背包中看到的物品
type BackpackItem struct {
	Type int `json:"type"`

	GiftID      int64 `json:"gift_id"`
	Comboable   int   `json:"comboable,omitempty"`
	Price       int64 `json:"price"`
	Num         int64 `json:"num"`
	AllowedNums []int `json:"allowed_nums,omitempty"`

	Name          string `json:"name"`
	IconURL       string `json:"icon_url"`
	IconActiveURL string `json:"icon_active_url,omitempty"`
	Intro         string `json:"intro"`
	IntroIconURL  string `json:"intro_icon_url,omitempty"`
	IntroOpenURL  string `json:"intro_open_url,omitempty"`
	LabelIcon     string `json:"label_icon_url,omitempty"`
	TimeLeft      int64  `json:"time_left"` // 剩余时间，秒
}

// FindBackpackItems 查找用户背包物品
func FindBackpackItems(creatorID int64, e *goutil.Equipment) ([]BackpackItem, error) {
	now := goutil.TimeNow().Unix()
	creatorItems, err := Find(setValidTimeFilter(bson.M{
		"creator_id": creatorID,
		"num":        bson.M{"$gt": 0},
	}, now), options.Find().SetSort(bson.D{
		// 按照过期时间排序，再按照礼物 ID 排序
		{Key: "end_time", Value: 1},
		{Key: "gift_id", Value: 1},
	}))
	if err != nil {
		return nil, err
	}
	// 目前认为礼物不会超过 20 个
	giftIDs := make([]int64, 0, 20)
	itemGifts := make(map[int64]*CreatorItem, 20)
	for i := range creatorItems {
		switch creatorItems[i].Type {
		case TypeFreeGift, TypeRebateGift:
			// 数量聚合在一起，过期时间返回最早的过期时间
			g := itemGifts[creatorItems[i].GiftID]
			if g == nil {
				giftIDs = append(giftIDs, creatorItems[i].GiftID)
				itemGifts[creatorItems[i].GiftID] = creatorItems[i]
				continue
			}
			g.Num += creatorItems[i].Num
			if g.EndTime > creatorItems[i].EndTime {
				g.EndTime = creatorItems[i].EndTime
			}
		}
	}
	res := make([]BackpackItem, 0, len(giftIDs))

	gifts, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return res, err
	}
	for i := range giftIDs {
		g := gifts[giftIDs[i]]
		if g == nil {
			continue
		}
		ig := itemGifts[giftIDs[i]] // ig 是 nil 说明代码逻辑有问题
		item := BackpackItem{
			Type: BPItemTypeGift,

			GiftID:      g.GiftID,
			Comboable:   g.Comboable,
			Price:       g.Price,
			Num:         ig.Num,
			AllowedNums: g.AllowedNums,

			Name:         g.Name,
			Intro:        g.Intro,
			IntroIconURL: g.IntroIcon,
			IntroOpenURL: g.IntroOpenURL,
			LabelIcon:    g.LabelIcon,
			TimeLeft:     ig.EndTime - now,

			IconURL:       g.Icon,
			IconActiveURL: g.IconActive,
		}
		res = append(res, item)
	}
	return res, nil
}
