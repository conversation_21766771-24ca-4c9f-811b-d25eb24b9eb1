package creatoritems

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
)

// 物品类型
const (
	TypeFreeGift   = iota + 3 // 免费礼物，值同礼物类型
	TypeRebateGift            // 白给礼物，值同礼物类型
)

// CreatorItem 背包物品
type CreatorItem struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	Type      int   `bson:"type"`
	CreatorID int64 `bson:"creator_id"`
	StartTime int64 `bson:"start_time"`
	EndTime   int64 `bson:"end_time"`

	GiftID  int64 `bson:"gift_id"`
	Num     int64 `bson:"num"`
	GainNum int64 `bson:"gain_num"`

	TransactionID int64  `bson:"transaction_id,omitempty"`
	Context       string `bson:"context,omitempty"`
}

// IsBackpackGift 该礼物是否能下发到背包
func IsBackpackGift(g *gift.Gift) bool {
	return g.Type == gift.TypeFree || g.Type == gift.TypeRebate
}

// TypeFromGiftType 礼物类型转物品类型
func TypeFromGiftType(giftType int) int {
	switch giftType {
	case gift.TypeFree:
		return TypeFreeGift
	case gift.TypeRebate:
		return TypeRebateGift
	default:
		panic(fmt.Sprintf("不支持的礼物类型: %d", giftType))
	}
}

// Collection returns collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("creator_items")
}

// Find 列出物品
func Find(filter interface{}, opt *options.FindOptions) ([]*CreatorItem, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, filter, opt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var items []*CreatorItem
	err = cur.All(ctx, &items)
	if err != nil {
		return nil, err
	}
	return items, nil
}

// setValidTimeFilter 为 mongodb 的 filter 设置有效时间的判断
func setValidTimeFilter(filter bson.M, nowUnix int64) bson.M {
	filter["start_time"] = bson.M{"$lte": nowUnix}
	filter["end_time"] = bson.M{"$gt": nowUnix}
	return filter
}
