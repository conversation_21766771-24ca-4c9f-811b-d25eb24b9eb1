package livevote

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/redis/interaction"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// VoteContent 互动投票选项详情
type VoteContent struct {
	GiftID      int64  `bson:"gift_id" json:"gift_id"`
	Color       string `bson:"color" json:"color"`
	IconURL     string `bson:"icon_url" json:"icon_url"`
	WebIconURL  string `bson:"web_icon_url" json:"web_icon_url"` // TODO: 待前端取消使用后，移除该字段
	Description string `bson:"description" json:"description"`
	Num         int64  `bson:"num" json:"num"`
}

// CollectionName collection name
const CollectionName = "live_votes"

// Vote document in collection live_votes
type Vote struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"vote_id"`

	RoomID    int64          `bson:"room_id" json:"room_id"`
	CreatorID int64          `bson:"creator_id" json:"-"`
	Duration  int64          `bson:"duration" json:"duration"`
	Title     string         `bson:"title" json:"title"`
	Content   []*VoteContent `bson:"content" json:"content"`
	EndTime   time.Time      `bson:"end_time" json:"-"`

	CreateTime   time.Time `bson:"create_time" json:"-"`
	ModifiedTime time.Time `bson:"modified_time" json:"-"`

	RemainDuration   int64 `bson:"-" json:"remain_duration"`
	AnnounceDuration int64 `bson:"-" json:"announce_duration"`
}

// Collection mongo collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// GiftIDs 获取对应投票的 giftIDs
func (v *Vote) GiftIDs() []int64 {
	giftIDs := make([]int64, len(v.Content))
	for i := range v.Content {
		giftIDs[i] = v.Content[i].GiftID
	}
	return giftIDs
}

// AddVote 创建投票
func AddVote(r *room.Room, duration int64, title string, contents []string, voteOption *interaction.VoteOption) (*Vote, error) {
	if len(contents) < 2 {
		panic("invalid contents length")
	}
	giftIDs := voteOption.GiftIDs()
	gifts, err := gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, err
	}
	if len(gifts) == 0 || len(gifts) != len(giftIDs) {
		panic("vote gift not all exists")
	}

	voteContents := make([]*VoteContent, len(contents))
	for i := range contents {
		voteContents[i] = &VoteContent{
			GiftID:      voteOption.GiftOptions[i].GiftID,
			Color:       voteOption.GiftOptions[i].Color,
			IconURL:     gifts[voteOption.GiftOptions[i].GiftID].Icon,
			WebIconURL:  gifts[voteOption.GiftOptions[i].GiftID].Icon,
			Description: contents[i],
		}
	}
	now := goutil.TimeNow()
	vote := Vote{
		RoomID:       r.RoomID,
		CreatorID:    r.CreatorID,
		Duration:     duration,
		Title:        title,
		Content:      voteContents,
		EndTime:      now.Add(time.Duration(duration) * time.Millisecond),
		CreateTime:   now,
		ModifiedTime: now,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	result, err := Collection().InsertOne(ctx, vote)
	if err != nil {
		return nil, err
	}
	vote.OID = result.InsertedID.(primitive.ObjectID)
	vote.CalculateDuration(voteOption)
	return &vote, nil
}

// CloseVote 提前关闭投票
func CloseVote(creatorID int64) (*Vote, error) {
	voteOption, err := interaction.FindVoteOption()
	if err != nil {
		return nil, err
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	var vote Vote
	filter := bson.M{"creator_id": creatorID, "end_time": bson.M{"$gt": now}}
	update := bson.M{"end_time": now, "modified_time": now}
	err = Collection().FindOneAndUpdate(ctx, filter, bson.M{"$set": update},
		options.FindOneAndUpdate().SetReturnDocument(options.After)).Decode(&vote)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	vote.CalculateDuration(voteOption)
	return &vote, nil
}

// FindOngoingVote 获取直播间内未结束的投票信息，包含投票中和投票结束公示期
func FindOngoingVote(roomID int64) (*Vote, error) {
	voteOption, err := interaction.FindVoteOption()
	if err != nil {
		return nil, err
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	announceEndTime := goutil.TimeNow().Add(-time.Duration(voteOption.AnnounceDuration) * time.Millisecond)
	var vote Vote
	err = Collection().FindOne(ctx, bson.M{"room_id": roomID, "end_time": bson.M{"$gt": announceEndTime}},
		&options.FindOneOptions{Sort: bson.M{"create_time": -1}}).Decode(&vote)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	vote.CalculateDuration(voteOption)
	return &vote, nil
}

// CalculateDuration 计算投票剩余时间
func (v *Vote) CalculateDuration(option *interaction.VoteOption) {
	now := goutil.TimeNow()
	if v.EndTime.After(now) {
		v.RemainDuration = v.EndTime.Sub(now).Milliseconds()
		v.AnnounceDuration = option.AnnounceDuration
		return
	}

	announceEndTime := v.EndTime.Add(time.Duration(option.AnnounceDuration) * time.Millisecond)
	if announceEndTime.After(now) {
		v.AnnounceDuration = option.AnnounceDuration
	}
}

// VotePayload document in collection votes
type VotePayload struct {
	VoteID           primitive.ObjectID `json:"vote_id"`
	Title            string             `json:"title"`
	Duration         int64              `json:"duration"`
	RemainDuration   int64              `json:"remain_duration,omitempty"`
	AnnounceDuration int64              `json:"announce_duration"`
	Content          []*VoteContent     `json:"content"`
}

// NotifyPayload 投票房间内消息
type NotifyPayload struct {
	Type    string       `json:"type"`
	Event   string       `json:"event"`
	RoomID  int64        `json:"room_id"`
	Message string       `json:"message,omitempty"`
	Vote    *VotePayload `json:"vote"`
}

// SendBroadcast 发送投票相关房间内消息
func (v *Vote) SendBroadcast(event, message string) {
	payload := &NotifyPayload{
		Type:    liveim.TypeVote,
		Event:   event,
		RoomID:  v.RoomID,
		Message: message,
		Vote: &VotePayload{
			VoteID:           v.OID,
			Title:            v.Title,
			Duration:         v.Duration,
			RemainDuration:   v.RemainDuration,
			AnnounceDuration: v.AnnounceDuration,
			Content:          v.Content,
		},
	}
	if err := userapi.Broadcast(v.RoomID, payload); err != nil {
		logger.Error(err)
		// PASS
	}
}
