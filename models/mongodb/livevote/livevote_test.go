package livevote

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/redis/interaction"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Vote{}, "_id", "room_id", "creator_id", "duration", "title", "content", "end_time",
		"create_time", "modified_time")
	kc.Check(VoteContent{}, "gift_id", "color", "icon_url", "web_icon_url", "description", "num")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Vote{}, "vote_id", "room_id", "duration", "announce_duration", "title", "content",
		"remain_duration")
	kc.Check(VoteContent{}, "gift_id", "color", "icon_url", "web_icon_url", "description", "num")

	kc.Check(NotifyPayload{}, "type", "event", "room_id", "message", "vote")
	kc.Check(VotePayload{}, "vote_id", "title", "duration", "remain_duration", "announce_duration", "content")
}

const announceUnixMilli = int64(10000)

var option = &interaction.VoteOption{
	GiftOptions: []*interaction.VoteGiftsOption{
		{GiftID: 2, Color: "#FE929B"},
		{GiftID: 3, Color: "#B6E58B"},
		{GiftID: 4, Color: "#F5CC6E"},
		{GiftID: 5, Color: "#BEB2FF"},
	},
	AnnounceDuration: 10000,
}

func TestGiftIDs(t *testing.T) {
	assert := assert.New(t)

	v := Vote{
		Content: []*VoteContent{
			{GiftID: 1}, {GiftID: 2},
		},
	}
	assert.Equal([]int64{1, 2}, v.GiftIDs())
}

func TestVote(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := &room.Room{
		Helper: room.Helper{
			RoomID:    22489473,
			CreatorID: 10,
		},
	}
	title := "test"
	contents := []string{"生", "三分熟", "五分熟", "七分熟"}
	duration := int64(10000)

	defer func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, _ = Collection().DeleteMany(ctx, bson.M{"room_id": r.RoomID})
	}()

	vote, err := AddVote(r, duration, title, contents, option)
	require.NoError(err)
	require.NotNil(vote)
	assert.Equal(duration, vote.Duration)
	assert.Equal(r.RoomID, vote.RoomID)
	assert.Equal(r.CreatorID, vote.CreatorID)
	assert.Equal(title, vote.Title)
	for i := range vote.Content {
		assert.Equal(contents[i], vote.Content[i].Description)
		assert.NotEmpty(vote.Content[i].WebIconURL)
		assert.NotEmpty(vote.Content[i].IconURL)
		assert.Zero(vote.Content[i].Num)
	}

	vote, err = FindOngoingVote(r.RoomID)
	require.NoError(err)
	require.NotNil(vote)
	assert.Equal(announceUnixMilli, vote.AnnounceDuration)
	assert.GreaterOrEqual(duration*1000, vote.RemainDuration)

	vote, err = CloseVote(r.CreatorID)
	require.NoError(err)
	require.NotNil(vote)
	assert.Zero(vote.RemainDuration)
	assert.Equal(announceUnixMilli, vote.AnnounceDuration)

	vote, err = FindOngoingVote(r.RoomID)
	require.NoError(err)
	require.NotNil(vote)
	assert.Zero(vote.RemainDuration)
	assert.Equal(announceUnixMilli, vote.AnnounceDuration)
}

func TestCalculateDuration(t *testing.T) {
	assert := assert.New(t)

	vote := Vote{
		Content: []*VoteContent{{GiftID: 2}, {GiftID: 3}},
		EndTime: goutil.TimeNow().Add(time.Minute),
	}
	vote.CalculateDuration(option)
	assert.LessOrEqual(int64(vote.RemainDuration), goutil.Milliseconds(time.Minute))
	assert.Equal(announceUnixMilli, vote.AnnounceDuration)

	vote = Vote{
		Content:          []*VoteContent{{GiftID: 1}, {GiftID: 2}, {GiftID: 3}},
		AnnounceDuration: 10000,
		EndTime:          goutil.TimeNow().Add(-5 * time.Second),
	}
	vote.CalculateDuration(option)
	assert.Zero(vote.RemainDuration)
	assert.Equal(announceUnixMilli, vote.AnnounceDuration)
}
