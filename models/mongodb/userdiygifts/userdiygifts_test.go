package userdiygifts

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)

	kc.Check(UserDiyGift{}, "_id", "create_time", "modified_time",
		"user_id", "room_id", "_diy_id", "gift_id", "icon_config", "words", "dress")
	kc.Check(Dress{}, "type", "_dress_id")
}

func TestFindUserLastDiyGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ud := UserDiyGift{
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
		UserID:       159,
		DiyOID:       primitive.NewObjectID(),
		GiftID:       753,
	}
	ud2 := ud
	ud2.CreateTime -= 60

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	_, err := col.DeleteMany(ctx, bson.M{"user_id": ud.UserID, "gift_id": ud.GiftID})
	require.NoError(err)
	last, err := FindUserLastDiyGift(ud.UserID, ud.GiftID)
	require.NoError(err)
	assert.Nil(last)

	res, err := col.InsertMany(ctx, []interface{}{ud, ud2})
	require.NoError(err)
	ud.OID = res.InsertedIDs[0].(primitive.ObjectID)

	last, err = FindUserLastDiyGift(ud.UserID, ud.GiftID)
	require.NoError(err)
	require.NotNil(last)
	assert.Equal(ud.OID, last.OID)
}

func TestDressIDMap(t *testing.T) {
	assert := assert.New(t)

	ud := UserDiyGift{
		Dress: []Dress{
			{Type: 1, DressOID: primitive.NewObjectID()},
			{Type: 2, DressOID: primitive.NewObjectID()},
		},
	}
	d := ud.Dress
	assert.NotEqual(d[0].DressOID, d[1].DressOID)
	dressMap := ud.DressIDMap()
	assert.Equal(map[int]primitive.ObjectID{
		d[0].Type: d[0].DressOID,
		d[1].Type: d[1].DressOID,
	}, dressMap)
}

func TestUserDiyGiftInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	ud := UserDiyGift{
		UserID: 12,
		RoomID: 34,
		GiftID: 56,
	}
	_, err := col.DeleteMany(ctx, bson.M{"user_id": ud.UserID, "room_id": ud.RoomID})
	require.NoError(err)

	require.NoError(ud.Insert())
	assert.False(ud.OID.IsZero())
	assert.NotZero(ud.CreateTime)
	assert.NotZero(ud.ModifiedTime)

	var after UserDiyGift
	require.NoError(col.FindOne(ctx, bson.M{"_id": ud.OID}).Decode(&after))
	assert.Equal(ud.UserID, after.UserID)
}
