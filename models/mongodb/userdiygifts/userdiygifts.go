package userdiygifts

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 头像配置
const (
	IconConfigCreator = iota + 1
	IconConfigUser
)

// UserDiyGift 用户定制礼物信息
type UserDiyGift struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	UserID     int64              `bson:"user_id"`
	RoomID     int64              `bson:"room_id"`
	DiyOID     primitive.ObjectID `bson:"_diy_id"`
	GiftID     int64              `bson:"gift_id"`
	IconConfig goutil.BitMask     `bson:"icon_config"`
	Words      string             `bson:"words"`
	Dress      []Dress            `bson:"dress"`
}

// Dress 装扮
type Dress struct {
	Type     int                `bson:"type"`
	DressOID primitive.ObjectID `bson:"_dress_id"`
}

// Collection user_diy_gifts
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("user_diy_gifts")
}

// FindUserLastDiyGift 查询用户最近一次的 diy 记录
func FindUserLastDiyGift(userID int64, giftID int64) (*UserDiyGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var ud UserDiyGift
	err := Collection().FindOne(ctx, bson.M{"user_id": userID, "gift_id": giftID},
		options.FindOne().SetSort(bson.M{"create_time": -1})).Decode(&ud)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return &ud, nil
}

// DressIDMap 返回 map[dress.type]dress._dress_id
func (ud UserDiyGift) DressIDMap() map[int]primitive.ObjectID {
	res := make(map[int]primitive.ObjectID, len(ud.Dress))
	for _, d := range ud.Dress {
		res[d.Type] = d.DressOID
	}
	return res
}

// Insert 插入新的用户定制信息
func (ud *UserDiyGift) Insert() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	ud.CreateTime = now.Unix()
	ud.ModifiedTime = now.Unix()
	res, err := Collection().InsertOne(ctx, ud)
	if err != nil {
		return err
	}
	ud.OID = res.InsertedID.(primitive.ObjectID)
	return nil
}
