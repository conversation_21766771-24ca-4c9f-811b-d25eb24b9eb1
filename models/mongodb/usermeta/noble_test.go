package usermeta

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
)

func TestIncrNobleHornNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1424124124312414)
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{
			"user_id": testUserID,
		},
		bson.M{
			"$set": bson.M{
				"noble_horn_num": 0,
			},
		},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	err = IncrNobleHornNum(testUserID, 1)
	require.NoError(err)

	var meta *UserMeta
	err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
	require.NoError(err)
	assert.EqualValues(1, meta.NobleHornNum)
}

func TestFindInvisibleUserMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 构造测试数据
	userID1 := int64(1314900001)
	userID2 := int64(1314900002)
	userID3 := int64(1314900003)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": []int64{userID1, userID2, userID3}}})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{
		bson.M{"user_id": userID1, "invisible": true},
		bson.M{"user_id": userID2, "invisible": false},
		bson.M{"user_id": userID3, "invisible": true},
	})
	require.NoError(err)

	// 查询
	invisibleSet, err := FindInvisibleUserMap([]int64{userID1, userID2, userID3})
	assert.NoError(err)
	assert.Contains(invisibleSet, userID1)
	assert.NotContains(invisibleSet, userID2)
	assert.Contains(invisibleSet, userID3)

	// 查询空
	invisibleSet, err = FindInvisibleUserMap([]int64{})
	assert.NoError(err)
	assert.Len(invisibleSet, 0)
}
