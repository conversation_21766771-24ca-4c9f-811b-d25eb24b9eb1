package usermeta

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGiftUpgrade_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(GiftUpgrade{}, "gift_id", "toggle_time", "lucky_score", "send_count", "upgrade_num")
}

func TestFindGiftUpgrade(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		userID     = int64(123)
		giftID     = int64(456)
		toggleTime = int64(123456789)
	)
	userMeta := UserMeta{
		UserID: userID,
	}
	err := Collection().FindOneAndReplace(context.Background(), bson.M{"user_id": userID}, userMeta).Err()
	require.NoError(err)

	upgrade, err := FindGiftUpgrade(userID, giftID, toggleTime)
	require.NoError(err)
	assert.Nil(upgrade)

	_, err = Collection().UpdateOne(context.Background(), bson.M{"user_id": userID},
		bson.M{"$push": bson.M{"gift_upgrades": GiftUpgrade{
			GiftID:     giftID,
			ToggleTime: toggleTime,
			UpgradeNum: 1,
			SendCount:  1,
		}}})
	require.NoError(err)

	upgrade, err = FindGiftUpgrade(userID, giftID, toggleTime)
	require.NoError(err)
	assert.NotNil(upgrade)
	assert.Equal(1, upgrade.UpgradeNum)
	assert.Equal(1, upgrade.SendCount)

	upgrade, err = FindGiftUpgrade(userID, giftID, -1)
	require.NoError(err)
	assert.Nil(upgrade)
}

func TestUpsertGiftUpgradeParam_calcIncrUpgradeNum(t *testing.T) {
	assert := assert.New(t)

	up := UpsertGiftUpgradeParam{
		FirstUpgradeThreshold: 3,
		UpgradeThreshold:      10,
	}

	type testCase struct {
		before int
		after  int
		want   int
	}

	testCases := []testCase{
		{before: 0, after: 1, want: 0},
		{before: 0, after: 3, want: 1},
		{before: 0, after: 24, want: 3},

		{before: 1, after: 15, want: 2},
		{before: 3, after: 12, want: 0},
		{before: 4, after: 15, want: 1},
		{before: 15, after: 25, want: 1},
	}
	for _, tc := range testCases {
		up.AddSendCount = tc.after - tc.before
		assert.Equal(tc.want, up.calcIncrUpgradeNum(tc.after))
	}
}

func TestUpsertGiftUpgradeParam_Upsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		userID     = int64(123)
		giftID     = int64(456)
		toggleTime = int64(123456789)
	)
	userMeta := UserMeta{
		UserID: userID,
	}
	err := Collection().FindOneAndReplace(context.Background(), bson.M{"user_id": userID}, userMeta).Err()
	require.NoError(err)

	p := UpsertGiftUpgradeParam{
		UserID:                userID,
		GiftID:                giftID,
		ToggleTime:            toggleTime,
		AddSendCount:          1,
		FirstUpgradeThreshold: 3,
		UpgradeThreshold:      10,
	}
	upgrade, err := p.Upsert()
	require.NoError(err)
	assert.NotNil(upgrade)
	assert.Zero(upgrade.UpgradeNum)
	assert.Equal(1, upgrade.SendCount)

	p.AddSendCount = 2
	upgrade, err = p.Upsert()
	require.NoError(err)
	assert.Equal(1, upgrade.UpgradeNum)
	assert.Equal(3, upgrade.SendCount)

	p.AddSendCount = 3
	upgrade, err = p.Upsert()
	require.NoError(err)
	assert.Equal(1, upgrade.UpgradeNum)
	assert.Equal(6, upgrade.SendCount)

	p.AddSendCount = 11
	upgrade, err = p.Upsert()
	require.NoError(err)
	assert.Equal(2, upgrade.UpgradeNum)
	assert.Equal(17, upgrade.SendCount)
}

func TestFindGiftUpgradeMaps(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		userID     = int64(123)
		giftID     = int64(456)
		toggleTime = int64(123456789)
	)
	userMeta := UserMeta{
		UserID: userID,
	}
	err := Collection().FindOneAndReplace(context.Background(), bson.M{"user_id": userID}, userMeta).Err()
	require.NoError(err)

	upgrade, err := FindGiftUpgradeMaps(userID, []BaseGift{
		{GiftID: giftID, ToggleTime: toggleTime},
	})
	require.NoError(err)
	assert.Len(upgrade, 0)

	_, err = Collection().UpdateOne(context.Background(), bson.M{"user_id": userID},
		bson.M{"$push": bson.M{"gift_upgrades": GiftUpgrade{
			GiftID:     giftID,
			ToggleTime: toggleTime,
			UpgradeNum: 1,
			SendCount:  1,
		}}})
	require.NoError(err)
	upgrade, err = FindGiftUpgradeMaps(userID, []BaseGift{
		{GiftID: giftID, ToggleTime: toggleTime},
	})
	require.NoError(err)
	assert.Len(upgrade, 1)
}

func TestUpdateAfterGiftUpgrade(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var (
		userID     = int64(123)
		giftID     = int64(456)
		toggleTime = int64(123456789)
	)

	userMeta := UserMeta{
		UserID: userID,
		GiftUpgrades: []*GiftUpgrade{
			{
				GiftID:     giftID,
				ToggleTime: 100,
				UpgradeNum: 3,
				LuckyScore: 0,
			},
			{
				GiftID:     giftID,
				ToggleTime: 200,
				UpgradeNum: 0,
				LuckyScore: 0,
			},
			{
				GiftID:     giftID,
				ToggleTime: toggleTime,
				UpgradeNum: 3,
				LuckyScore: 30,
			},
		},
	}
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	require.NoError(err)

	// 确认找不到记录时也会返回 error
	upgrade, err := UpdateAfterGiftUpgrade(userID, giftID, toggleTime, 20)
	assert.True(mongodb.IsNoDocumentsError(err))
	assert.Nil(upgrade)

	// 插入新的记录
	_, err = Collection().InsertOne(ctx, userMeta)
	require.NoError(err)
	// 次数不符合要求
	upgrade, err = UpdateAfterGiftUpgrade(userID, giftID, 200, 20)
	assert.True(mongodb.IsNoDocumentsError(err))
	assert.Nil(upgrade)

	// 更新升级次数和幸运值
	upgrade, err = UpdateAfterGiftUpgrade(userID, giftID, toggleTime, 20)
	require.NoError(err)
	assert.NotNil(upgrade)
	assert.Equal(2, upgrade.UpgradeNum)
	assert.Equal(20, upgrade.LuckyScore)

	// 查询 usermeta 里的另一条记录没有改变
	otherGU, err := FindGiftUpgrade(userID, giftID, 100)
	require.NoError(err)
	assert.NotNil(otherGU)
	assert.Equal(3, otherGU.UpgradeNum)
	assert.Equal(0, otherGU.LuckyScore)
}
