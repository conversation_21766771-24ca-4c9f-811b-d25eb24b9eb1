package usermeta

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 上神续费阈值
// 使用 vip 包下常量会有包循环引用的问题，在此处定义需要跟 vip 包常量数值保持一直
const (
	highnessRenewalThreshold int64 = 5000000 // 续费上神所需累计消费总钻石数
)

// HighnessSpend 用户续费上神消费的钻石数
func HighnessSpend(userID int64) int64 {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var rec UserMeta
	err := Collection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(bson.M{
			"highness_spend": 1,
		})).Decode(&rec)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		logger.WithFields(logger.Fields{
			"user_id": userID,
		}).Error(err)
		return 0
	}
	if rec.HighnessSpend <= 0 {
		return 0
	}
	return rec.HighnessSpend % highnessRenewalThreshold
}

// ResetHighnessSpend 重置用户上神消费进度为 spend
func ResetHighnessSpend(userID, spend int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{"user_id": userID},
		bson.M{
			"$set": bson.M{
				"highness_spend":      spend,
				"highness_reset_time": goutil.TimeNow().Unix(), // WORKAROUND: 临时记录上神开通时间
			},
		},
	)
	return err
}

// AddHighnessSpend 增加上神消费进度
// 调用前需要保证调用场景正确
func AddHighnessSpend(userID int64, spend int64) (before int64, after int64, err error) {
	if userID <= 0 || spend <= 0 {
		panic("param error")
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	projection := bson.M{
		"highness_spend": 1,
	}

	var userMeta UserMeta
	err = Collection().FindOneAndUpdate(ctx,
		bson.M{"user_id": userID},
		bson.M{"$inc": bson.M{"highness_spend": spend}},
		options.FindOneAndUpdate().SetProjection(projection).SetReturnDocument(options.After)).
		Decode(&userMeta)
	if err != nil {
		return
	}
	before = userMeta.HighnessSpend - spend
	after = userMeta.HighnessSpend
	return
}
