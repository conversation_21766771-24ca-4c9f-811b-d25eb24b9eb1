package usermeta

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(&CustomWelcomeMessage{}, "text", "expire_time")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(&CustomWelcomeMessage{}, "text", "expire_time")
}

func TestFindUserValidCustomWelcomeMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(191819)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	msg, err := FindUserValidCustomWelcomeMessage(testUserID)
	require.NoError(err)
	assert.Nil(msg)
	_, err = Collection().InsertOne(ctx, bson.M{
		"user_id":                testUserID,
		"custom_welcome_message": &CustomWelcomeMessage{},
	})
	require.NoError(err)
	msg, err = FindUserValidCustomWelcomeMessage(testUserID)
	require.NoError(err)
	assert.Nil(msg)
	_, err = Collection().UpdateOne(ctx, bson.M{"user_id": testUserID}, bson.M{"$set": bson.M{
		"custom_welcome_message": &CustomWelcomeMessage{
			Text:       "test",
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		},
	}})
	require.NoError(err)
	msg, err = FindUserValidCustomWelcomeMessage(testUserID)
	require.NoError(err)
	require.NotNil(msg)
}

func TestFindUserValidCustomWelcomeMessageMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(191819)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	msg, err := FindUserValidCustomWelcomeMessageMap([]int64{testUserID})
	require.NoError(err)
	assert.Nil(msg[testUserID])
	_, err = Collection().InsertOne(ctx, bson.M{
		"user_id":                testUserID,
		"custom_welcome_message": &CustomWelcomeMessage{},
	})
	require.NoError(err)
	msg, err = FindUserValidCustomWelcomeMessageMap([]int64{testUserID})
	require.NoError(err)
	assert.Nil(msg[testUserID])
	_, err = Collection().UpdateOne(ctx, bson.M{"user_id": testUserID}, bson.M{"$set": bson.M{
		"custom_welcome_message": &CustomWelcomeMessage{
			Text:       "test",
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		},
	}})
	require.NoError(err)
	msg, err = FindUserValidCustomWelcomeMessageMap([]int64{testUserID})
	require.NoError(err)
	require.NotNil(msg[testUserID])
	assert.Equal("test", msg[testUserID].Text)
}

func TestUpdateWelcomeMessageText(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(191819)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	msg, err := UpdateWelcomeMessageText(testUserID, "")
	require.NoError(err)
	assert.Nil(msg)
	_, err = Collection().InsertOne(ctx, bson.M{
		"user_id": testUserID,
		"custom_welcome_message": &CustomWelcomeMessage{
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		},
	})
	require.NoError(err)
	msg, err = UpdateWelcomeMessageText(testUserID, "测试")
	require.NoError(err)
	assert.Equal("测试", msg.Text)
}

func TestAssignCustomWelcomeWithDuration(t *testing.T) {
	testUserID := int64(1919810)
	now := goutil.TimeNow()

	// 清理测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	}()

	// 测试只传入 duration 参数的情况
	t.Run("DurationOnly", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户但不设置欢迎语
		_, err = Collection().InsertOne(ctx, UserMeta{UserID: testUserID})
		require.NoError(err)

		duration := int64(3600) // 1小时
		err = AssignCustomWelcomeWithDuration(testUserID, duration, 0)
		require.NoError(err)

		// 验证结果
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Empty(meta.CustomWelcomeMessage.Text) // 新发放的欢迎语文本应为空
		assert.Equal(now.Unix()+duration, meta.CustomWelcomeMessage.ExpireTime)
	})

	// 测试只传入 endTime 参数的情况
	t.Run("EndTimeOnly", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户但不设置欢迎语
		_, err = Collection().InsertOne(ctx, UserMeta{UserID: testUserID})
		require.NoError(err)

		endTime := now.Add(2 * time.Hour).Unix()
		err = AssignCustomWelcomeWithDuration(testUserID, 0, endTime)
		require.NoError(err)

		// 验证结果
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Empty(meta.CustomWelcomeMessage.Text) // 新发放的欢迎语文本应为空
		assert.Equal(endTime, meta.CustomWelcomeMessage.ExpireTime)
	})

	// 测试 duration 和 endTime 都传入，且 duration 计算出的过期时间更小的情况
	t.Run("DurationSmallerThanEndTime", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户但不设置欢迎语
		_, err = Collection().InsertOne(ctx, UserMeta{UserID: testUserID})
		require.NoError(err)

		duration := int64(1800) // 30分钟
		endTime := now.Add(2 * time.Hour).Unix()
		err = AssignCustomWelcomeWithDuration(testUserID, duration, endTime)
		require.NoError(err)

		// 验证结果：应该使用 duration
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Empty(meta.CustomWelcomeMessage.Text)
		assert.Equal(now.Unix()+duration, meta.CustomWelcomeMessage.ExpireTime)
	})

	// 测试 duration 和 endTime 都传入，且 endTime 更小的情况
	t.Run("EndTimeSmallerThanDuration", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户但不设置欢迎语
		_, err = Collection().InsertOne(ctx, UserMeta{UserID: testUserID})
		require.NoError(err)

		duration := int64(7200)                     // 2小时
		endTime := now.Add(30 * time.Minute).Unix() // 30分钟后
		err = AssignCustomWelcomeWithDuration(testUserID, duration, endTime)
		require.NoError(err)

		// 验证结果：应该使用 endTime
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Empty(meta.CustomWelcomeMessage.Text)
		assert.Equal(endTime, meta.CustomWelcomeMessage.ExpireTime)
	})

	// 测试用户已有未过期欢迎语，延长使用时间的情况
	t.Run("ExtendValidWelcome", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户并设置未过期的欢迎语
		existingExpireTime := now.Add(time.Hour).Unix()
		existingWelcomeMsg := &CustomWelcomeMessage{
			Text:       "现有的欢迎语",
			ExpireTime: existingExpireTime,
		}
		_, err = Collection().InsertOne(ctx, UserMeta{
			UserID:               testUserID,
			CustomWelcomeMessage: existingWelcomeMsg,
		})
		require.NoError(err)

		duration := int64(1800) // 30分钟
		err = AssignCustomWelcomeWithDuration(testUserID, duration, 0)
		require.NoError(err)

		// 验证结果：文本保持不变，过期时间延长
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Equal("现有的欢迎语", meta.CustomWelcomeMessage.Text)                          // 文本应保持不变
		assert.Equal(existingExpireTime+duration, meta.CustomWelcomeMessage.ExpireTime) // 在原有基础上延长
	})

	// 测试用户已有过期欢迎语，重新发放的情况
	t.Run("ReassignExpiredWelcome", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户并设置已过期的欢迎语
		expiredTime := now.Add(-time.Hour).Unix() // 1小时前过期
		expiredWelcomeMsg := &CustomWelcomeMessage{
			Text:       "已过期的欢迎语",
			ExpireTime: expiredTime,
		}
		_, err = Collection().InsertOne(ctx, UserMeta{
			UserID:               testUserID,
			CustomWelcomeMessage: expiredWelcomeMsg,
		})
		require.NoError(err)

		duration := int64(3600) // 1小时
		err = AssignCustomWelcomeWithDuration(testUserID, duration, 0)
		require.NoError(err)

		// 验证结果：文本被重置，过期时间重新计算
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Empty(meta.CustomWelcomeMessage.Text)                            // 文本应被重置
		assert.Equal(now.Unix()+duration, meta.CustomWelcomeMessage.ExpireTime) // 基于当前时间计算
	})

	// 测试 duration 为 0 且 endTime 早于当前时间的边界情况
	t.Run("EndTimeInPast", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		// 清理数据
		_, err := Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
		require.NoError(err)

		// 创建用户但不设置欢迎语
		_, err = Collection().InsertOne(ctx, UserMeta{UserID: testUserID})
		require.NoError(err)

		pastEndTime := now.Add(-time.Hour).Unix() // 1小时前
		err = AssignCustomWelcomeWithDuration(testUserID, 0, pastEndTime)
		require.NoError(err)

		// 验证结果：应该使用当前时间
		var meta UserMeta
		err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&meta)
		require.NoError(err)
		require.NotNil(meta.CustomWelcomeMessage)
		assert.Empty(meta.CustomWelcomeMessage.Text)
		assert.Equal(now.Unix(), meta.CustomWelcomeMessage.ExpireTime) // 使用当前时间
	})
}
