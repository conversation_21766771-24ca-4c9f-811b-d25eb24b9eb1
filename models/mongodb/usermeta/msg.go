package usermeta

import (
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// DailyMsgCount 用户每日发言数，达到该值增加贡献值
	DailyMsgCount = 3
)

// IncrMessageCount 增加用户当日发言数
func IncrMessageCount(userID int64) int64 {
	var (
		today = goutil.BeginningOfDay(goutil.TimeNow())

		key         string
		msgCountCmd *redis.IntCmd
	)

	pipe := service.Redis.TxPipeline()
	// WORKAROUND: 拆分旧的消息数量统计的 KEY，于 2023-06-20 00:00:00 生效
	var startTime int64
	config.GetAB("new_usermeta_msg_count_time", &startTime)
	if startTime <= today.Unix() {
		key = keys.KeyUserMetaMessageCount2.Format(today.Format(util.TimeFormatYMDWithNoSpace), userID)
		msgCountCmd = pipe.Incr(key)
	} else {
		key = keys.KeyUserMetaMessageCount1.Format(today.Format(util.TimeFormatYMDWithNoSpace))
		msgCountCmd = pipe.HIncrBy(key, strconv.FormatInt(userID, 10), 1)
	}
	liveserviceredis.ExpireAt(pipe, key, today.AddDate(0, 0, 1).Add(time.Minute)) // 第二天 00:01 过期
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return msgCountCmd.Val()
}

// CountUserTodayMessage 获取用户当天的消息数量
func CountUserTodayMessage(userID int64) (int64, error) {
	today := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyUserMetaMessageCount2.Format(today.Format(util.TimeFormatYMDWithNoSpace), userID)
	count, err := service.Redis.Get(key).Int64()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return 0, nil
		}
		return 0, err
	}
	return count, nil
}
