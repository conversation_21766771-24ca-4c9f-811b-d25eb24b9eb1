package usermeta

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CustomWelcomeMessage 用户的自定义进场欢迎语
type CustomWelcomeMessage struct {
	Text       string `bson:"text" json:"text"`
	ExpireTime int64  `bson:"expire_time" json:"expire_time"` // 单位：秒
}

// FindUserValidCustomWelcomeMessage 获取用户有效的自定义进场欢迎语
func FindUserValidCustomWelcomeMessage(userID int64) (*CustomWelcomeMessage, error) {
	welcomeMap, err := FindUserValidCustomWelcomeMessageMap([]int64{userID})
	if err != nil {
		return nil, err
	}
	return welcomeMap[userID], nil
}

// FindUserValidCustomWelcomeMessageMap 批量获取用户有效的自定义进场欢迎语
func FindUserValidCustomWelcomeMessageMap(userIDs []int64) (map[int64]*CustomWelcomeMessage, error) {
	welcomeMsgMap := make(map[int64]*CustomWelcomeMessage, len(userIDs))
	welcomeMap, err := findCustomWelcomeMessageMap(userIDs)
	if err != nil {
		return nil, err
	}
	now := goutil.TimeNow()
	for _, uID := range userIDs {
		// 用户没有自定义进场通知或者资格已过期不显示相关内容
		if welcomeMap[uID] != nil && now.Unix() >= welcomeMap[uID].ExpireTime {
			continue
		}
		welcomeMsgMap[uID] = welcomeMap[uID]
	}
	return welcomeMsgMap, nil
}

func findCustomWelcomeMessageMap(userIDs []int64) (map[int64]*CustomWelcomeMessage, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Find(ctx, bson.M{"user_id": bson.M{
		"$in": userIDs,
	}}, options.Find().SetProjection(bson.M{
		"user_id":                1,
		"custom_welcome_message": 1,
	}))
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*UserMeta
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	welcomeMsgMap := make(map[int64]*CustomWelcomeMessage, len(userIDs))
	for _, r := range res {
		welcomeMsgMap[r.UserID] = r.CustomWelcomeMessage
	}
	return welcomeMsgMap, nil
}

// UpdateWelcomeMessageText 用户自定义编辑进场欢迎语
func UpdateWelcomeMessageText(userID int64, text string) (*CustomWelcomeMessage, error) {
	// 自定义资格检测
	welcomeMessage, err := FindUserValidCustomWelcomeMessage(userID)
	if err != nil {
		return nil, err
	}
	if welcomeMessage == nil {
		return nil, nil
	}
	// 自定义欢迎语
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = Collection().UpdateOne(ctx, bson.M{
		"user_id": userID,
	}, bson.M{"$set": bson.M{
		"custom_welcome_message.text": text,
	}})
	if err != nil {
		return nil, err
	}
	welcomeMessage.Text = text
	return welcomeMessage, nil
}

// AssignCustomWelcomeWithDuration 根据持续时间发放用户的自定义欢迎语
// duration 和 endTime 单位为秒
func AssignCustomWelcomeWithDuration(userID, duration, endTime int64) error {
	welcomeMessageMap, err := findCustomWelcomeMessageMap([]int64{userID})
	if err != nil {
		return err
	}
	var (
		nowUnix = goutil.TimeNow().Unix()
		update  = make(bson.M, 2)
	)
	if welcomeMessageMap[userID] != nil && welcomeMessageMap[userID].ExpireTime > nowUnix {
		// 用户拥有未过期的自定义欢迎语
		nowUnix = welcomeMessageMap[userID].ExpireTime
	} else {
		// 新发放的欢迎语需要重置其内容
		update["$unset"] = bson.M{"custom_welcome_message.text": ""}
	}

	var expireTime int64
	if duration == 0 {
		// 当 duration=0 时，过期时间直接使用 endTime
		expireTime = max(nowUnix, endTime)
	} else if endTime == 0 {
		// 当 endTime=0 时，过期时间直接使用 duration
		expireTime = nowUnix + duration
	} else {
		// 当 duration 和 endTime 都有效时，限制过期时间不超过 endTime
		expireTime = min(nowUnix+duration, endTime)
	}

	update["$set"] = bson.M{"custom_welcome_message.expire_time": expireTime}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = Collection().UpdateOne(ctx, bson.M{
		"user_id": userID,
	}, update)
	return err
}
