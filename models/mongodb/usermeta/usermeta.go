package usermeta

import (
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
)

// UserMeta user_meta 表结构体
// TODO: 待补充
type UserMeta struct {
	UserID               int64                 `bson:"user_id"`
	HighnessSpend        int64                 `bson:"highness_spend"`
	NobleHornNum         int64                 `bson:"noble_horn_num"`
	CustomWelcomeMessage *CustomWelcomeMessage `bson:"custom_welcome_message,omitempty"`
	GiftUpgrades         []*GiftUpgrade        `bson:"gift_upgrades,omitempty"`
}

// Collection user_meta
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("user_meta")
}
