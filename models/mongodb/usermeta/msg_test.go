package usermeta

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestIncrMessageCount(t *testing.T) {
	require := require.New(t)

	when := time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return when
	})
	defer goutil.SetTimeNow(nil)

	testUserID := int64(223344)

	// 使用旧的消息数量统计 KEY
	config.Conf.AB["new_usermeta_msg_count_time"] = when.Unix() + 1
	defer delete(config.Conf.AB, "new_usermeta_msg_count_time")
	key := keys.KeyUserMetaMessageCount1.Format(
		goutil.BeginningOfDay(goutil.TimeNow()).Format(util.TimeFormatYMDWithNoSpace),
	)
	require.NoError(service.Redis.Del(key).Err())
	IncrMessageCount(testUserID)
	count, err := service.Redis.HGet(key, strconv.FormatInt(testUserID, 10)).Result()
	require.NoError(err)
	require.Equal("1", count)

	// 使用新的消息数量统计 KEY
	config.Conf.AB["new_usermeta_msg_count_time"] = when.Unix()
	key = keys.KeyUserMetaMessageCount2.Format(
		goutil.BeginningOfDay(goutil.TimeNow()).Format(util.TimeFormatYMDWithNoSpace),
		testUserID,
	)
	require.NoError(service.Redis.Del(key).Err())
	IncrMessageCount(testUserID)
	count, err = service.Redis.Get(key).Result()
	require.NoError(err)
	require.Equal("1", count)
}

func TestCountUserTodayMessage(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	when := time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local)
	cancel := goutil.SetTimeNow(func() time.Time { return when })
	defer cancel()

	testUserID := int64(223344)
	key := keys.KeyUserMetaMessageCount2.Format(
		goutil.BeginningOfDay(goutil.TimeNow()).Format(util.TimeFormatYMDWithNoSpace),
		testUserID,
	)
	// 测试 KEY 存在的情况
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.Set(key, 5, 24*time.Hour).Err())
	count, err := CountUserTodayMessage(testUserID)
	require.NoError(err)
	assert.EqualValues(5, count)

	// 测试 KEY 不存在的情况
	require.NoError(service.Redis.Del(key).Err())
	count, err = CountUserTodayMessage(testUserID)
	require.NoError(err)
	assert.Zero(count)
}
