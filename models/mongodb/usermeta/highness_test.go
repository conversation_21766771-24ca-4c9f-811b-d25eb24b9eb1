package usermeta

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestHighnessSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": 9074509}
	_, err := Collection().DeleteOne(ctx, filter)
	require.NoError(err)

	assert.Zero(HighnessSpend(9074509))

	_, err = Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"highness_spend": 1000,
			},
		}, options.Update().SetUpsert(true))
	require.NoError(err)
	assert.EqualValues(1000, HighnessSpend(9074509))
}

func TestResetHighnessSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateOne(ctx,
		bson.M{"user_id": 9074509},
		bson.M{"$set": bson.M{"highness_spend": 1000}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	assert.EqualValues(1000, HighnessSpend(9074509))
	require.NoError(ResetHighnessSpend(9074509, 200))
	assert.EqualValues(200, HighnessSpend(9074509))
}

func TestAddHighnessSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": 9074509}
	_, err := Collection().DeleteOne(ctx, filter)
	require.NoError(err)

	assert.Panics(func() {
		_, _, _ = AddHighnessSpend(0, 1)
	}, "param error")

	assert.Panics(func() {
		_, _, _ = AddHighnessSpend(9074509, 0)
	}, "param error")

	_, err = Collection().UpdateOne(ctx,
		bson.M{
			"user_id": 9074509,
		},
		bson.M{
			"$set": bson.M{
				"highness_spend": 1000,
			},
		}, options.Update().SetUpsert(true))
	require.NoError(err)
	beforeSpend, afterSpend, err := AddHighnessSpend(9074509, 100)
	require.NoError(err)
	assert.EqualValues(1000, beforeSpend)
	assert.EqualValues(1100, afterSpend)
}
