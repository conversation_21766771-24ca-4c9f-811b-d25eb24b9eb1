package usermeta

import (
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
)

// GiftUpgrade 礼物升级
type GiftUpgrade struct {
	GiftID     int64 `bson:"gift_id"`     // 礼物ID
	ToggleTime int64 `bson:"toggle_time"` // 礼物上架时间
	LuckyScore int   `bson:"lucky_score"` // 幸运值
	SendCount  int   `bson:"send_count"`  // 送礼次数
	UpgradeNum int   `bson:"upgrade_num"` // 可升级次数
}

func findGiftUpgrade(userMeta *UserMeta, giftID int64, toggleTime int64) *GiftUpgrade {
	if userMeta == nil {
		return nil
	}
	for _, upgrade := range userMeta.GiftUpgrades {
		if upgrade.GiftID == giftID && upgrade.ToggleTime == toggleTime {
			return upgrade
		}
	}
	return nil
}

// FindGiftUpgrade 查找礼物升级记录
func FindGiftUpgrade(userID int64, giftID int64, toggleTime int64) (*GiftUpgrade, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var userMeta UserMeta
	err := Collection().FindOne(ctx, bson.M{
		"user_id": userID,
	}).Decode(&userMeta)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return findGiftUpgrade(&userMeta, giftID, toggleTime), nil
}

// BaseGift 基础礼物信息
type BaseGift struct {
	GiftID     int64
	ToggleTime int64
}

// FindGiftUpgradeMaps 查找礼物升级记录
func FindGiftUpgradeMaps(userID int64, params []BaseGift) (map[int64]*GiftUpgrade, error) {
	if userID <= 0 || len(params) == 0 {
		return nil, nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var userMeta UserMeta
	err := Collection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(bson.M{
			"user_id":       1,
			"gift_upgrades": 1,
		})).Decode(&userMeta)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	if len(userMeta.GiftUpgrades) == 0 {
		return nil, nil
	}

	result := make(map[int64]*GiftUpgrade, len(userMeta.GiftUpgrades))
	for _, upgrade := range userMeta.GiftUpgrades {
		for _, param := range params {
			if upgrade.GiftID == param.GiftID && upgrade.ToggleTime == param.ToggleTime {
				result[param.GiftID] = upgrade
			}
		}
	}

	return result, nil
}

// UpsertGiftUpgradeParam 礼物升级参数
type UpsertGiftUpgradeParam struct {
	UserID                int64
	GiftID                int64
	ToggleTime            int64
	AddSendCount          int
	FirstUpgradeThreshold int // 首次升级所需次数
	UpgradeThreshold      int // 升级需要次数
}

func (up *UpsertGiftUpgradeParam) calcIncrUpgradeNum(afterSendCount int) int {
	// 计算升级次数的函数
	calcUpgradeCount := func(sendCount int) int {
		if sendCount < up.FirstUpgradeThreshold {
			return 0
		}

		upgradeCount := 1
		remainCount := sendCount - up.FirstUpgradeThreshold
		if up.UpgradeThreshold > 0 && remainCount >= up.UpgradeThreshold {
			upgradeCount += remainCount / up.UpgradeThreshold
		}
		return upgradeCount
	}

	// 计算增加前后的升级次数差值
	upgradeNum := calcUpgradeCount(afterSendCount) - calcUpgradeCount(afterSendCount-up.AddSendCount)
	return max(upgradeNum, 0)
}

// Upsert 更新或插入礼物升级记录
// Notice: 忽略 user_meta 记录不存在的情况
func (up *UpsertGiftUpgradeParam) Upsert() (*GiftUpgrade, error) {
	// 加锁，防止重复插入
	lock := redismutex.New(
		service.Redis,
		keys.LockGiftUpgrade2.Format(up.UserID, up.GiftID),
		2*time.Second,
	)
	if !lock.TryLock() {
		return nil, errors.New("操作过于频繁，请稍后再试")
	}
	defer lock.Unlock()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"user_id": up.UserID,
		"gift_upgrades": bson.M{
			"$elemMatch": bson.M{
				"gift_id":     up.GiftID,
				"toggle_time": up.ToggleTime,
			},
		},
	}
	inc := bson.M{
		"gift_upgrades.$.send_count": up.AddSendCount,
	}
	projection := bson.M{
		"user_id":       1,
		"gift_upgrades": 1,
	}
	var userMeta UserMeta
	err := Collection().FindOneAndUpdate(
		ctx,
		filter,
		bson.M{"$inc": inc},
		options.FindOneAndUpdate().SetProjection(projection).SetReturnDocument(options.After)).Decode(&userMeta)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return nil, err
	}

	if mongodb.IsNoDocumentsError(err) {
		// 如果没有找到匹配的记录，插入新的 upgrade
		err = Collection().FindOneAndUpdate(ctx, bson.M{
			"user_id": up.UserID,
			"$nor": []bson.M{{
				"gift_upgrades": bson.M{
					"$elemMatch": bson.M{
						"gift_id":     up.GiftID,
						"toggle_time": up.ToggleTime,
					}}}},
		}, bson.M{
			"$push": bson.M{
				"gift_upgrades": &GiftUpgrade{
					GiftID:     up.GiftID,
					ToggleTime: up.ToggleTime,
					SendCount:  up.AddSendCount,
					UpgradeNum: 0,
				},
			},
		},
			options.FindOneAndUpdate().SetProjection(projection).SetReturnDocument(options.After)).Decode(&userMeta)
		if err != nil {
			if mongodb.IsNoDocumentsError(err) {
				logger.WithFields(logger.Fields{
					"user_id":     up.UserID,
					"gift_id":     up.GiftID,
					"toggle_time": up.ToggleTime,
					"send_count":  up.AddSendCount,
				}).Error("insert new upgrade failed")
				return nil, nil
			}
			return nil, err
		}
	}

	upgrade := findGiftUpgrade(&userMeta, up.GiftID, up.ToggleTime)
	if upgrade == nil {
		return nil, errors.New("upgrade not found")
	}
	incrUpgradeNum := up.calcIncrUpgradeNum(upgrade.SendCount)
	if incrUpgradeNum <= 0 {
		return upgrade, nil
	}
	// update upgrade_num
	var result UserMeta
	err = Collection().FindOneAndUpdate(
		ctx,
		filter,
		bson.M{"$inc": bson.M{"gift_upgrades.$.upgrade_num": incrUpgradeNum}},
		options.FindOneAndUpdate().SetProjection(projection).SetReturnDocument(options.After)).Decode(&result)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return nil, err
	}

	return findGiftUpgrade(&result, up.GiftID, up.ToggleTime), nil
}

// UpdateAfterGiftUpgrade 升级后更新幸运值，并将可升级次数减 1
func UpdateAfterGiftUpgrade(userID int64, giftID int64, toggleTime int64, luckyScore int) (*GiftUpgrade, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	projection := bson.M{
		"user_id":       1,
		"gift_upgrades": 1,
	}
	var userMeta UserMeta
	err := Collection().FindOneAndUpdate(
		ctx,
		bson.M{
			"user_id": userID,
			"gift_upgrades": bson.M{
				"$elemMatch": bson.M{
					"gift_id":     giftID,
					"toggle_time": toggleTime,
					"upgrade_num": bson.M{"$gte": 1},
				},
			},
		},
		bson.M{
			"$set": bson.M{
				"gift_upgrades.$.lucky_score": luckyScore,
			},
			"$inc": bson.M{
				"gift_upgrades.$.upgrade_num": -1,
			},
		},
		options.FindOneAndUpdate().SetProjection(projection).SetReturnDocument(options.After)).Decode(&userMeta)
	if err != nil {
		// NoDocumentsError 也需要被返回
		return nil, err
	}
	return findGiftUpgrade(&userMeta, giftID, toggleTime), nil
}
