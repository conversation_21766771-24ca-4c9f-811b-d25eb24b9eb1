package usermeta

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
)

// IncrNobleHornNum 增加用户贵族喇叭数量
func IncrNobleHornNum(userID, num int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateOne(ctx,
		bson.M{
			"user_id": userID,
		},
		bson.M{
			"$inc": bson.M{"noble_horn_num": num},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

// FindInvisibleUserMap 批量查询用户是否开启了进场隐身
func FindInvisibleUserMap(userIDs []int64) (map[int64]struct{}, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var records []struct {
		UserID int64 `bson:"user_id"`
	}
	cur, err := Collection().Find(ctx, bson.M{
		"user_id":   bson.M{"$in": userIDs},
		"invisible": true,
	}, options.Find().SetProjection(bson.M{"user_id": 1}))
	if err != nil {
		return nil, fmt.Errorf("find invisible users: %v", err)
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &records)
	if err != nil {
		return nil, fmt.Errorf("decode invisible users: %v", err)
	}
	invisibleSet := make(map[int64]struct{}, len(records))
	for i := 0; i < len(records); i++ {
		invisibleSet[records[i].UserID] = struct{}{}
	}
	return invisibleSet, nil
}
