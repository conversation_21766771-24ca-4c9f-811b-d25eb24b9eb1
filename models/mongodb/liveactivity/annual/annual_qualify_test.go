package annual

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/boxprizes"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)

	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestAchievementKey(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("quest_superfan_1", achievementKey(QuestTypeSuperFan, 1))
	assert.Equal("quest_gift_4", achievementKey(QuestTypeGift, 4))
	assert.Equal("quest_hourtop_4", achievementKey(QuestTypeHourTop, 4))
	assert.Equal("quest_hourtop_1", achievementKey(QuestTypeHourTop, 1))
}

func TestScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	q := QualityQuestRecord{}
	assert.Zero(q.score())
	q = QualityQuestRecord{
		CreatorID: 102345,
		Achievement: map[string]int64{
			"quest_superfan_1": 5,
			"quest_superfan_2": 5,
			"quest_superfan_3": 10,
			"quest_superfan_4": 24,
			"quest_noble_1":    1,
			"quest_noble_2":    2,
			"quest_noble_3":    4,
			"quest_noble_4":    17,
			"quest_gift_1":     200,
			"quest_gift_2":     250,
			"quest_gift_3":     600,
			"quest_gift_4":     720,
			"quest_hourtop_1":  1,
			"quest_hourtop_2":  2,
			"quest_hourtop_3":  8,
			"quest_hourtop_4":  3,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": q.CreatorID})
	require.NoError(err)
	assert.Equal(int64(68), q.score())
}

func TestUpdateQuestKey(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(10244)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteOne(ctx, bson.M{"room_id": roomID})
	require.NoError(err)

	q := &questMessage{
		Type:         QuestTypeSuperFan,
		RoomID:       10244,
		dayOfQualify: 1,
		CreatorID:    14,
	}
	before, after, err := q.updateQuestKey()
	require.NoError(err)
	a := after.Achievement
	b := before.Achievement
	require.Len(a, 1)
	assert.EqualValues(1, a["quest_superfan_1"])
	assert.EqualValues(0, b["quest_superfan_1"])
	q = &questMessage{
		Type:         QuestTypeNoble,
		RoomID:       10244,
		dayOfQualify: 2,
		CreatorID:    14,
	}
	_, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	require.Len(a, 2)
	assert.EqualValues(1, a["quest_noble_2"])
	q = &questMessage{
		Type:         QuestTypeNoble,
		RoomID:       10244,
		dayOfQualify: 2,
		CreatorID:    14,
	}
	_, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	require.Len(a, 2)
	assert.EqualValues(2, a["quest_noble_2"])
	q = &questMessage{
		Type:         QuestTypeGift,
		RoomID:       10244,
		GiftNum:      200,
		dayOfQualify: 2,
		CreatorID:    14,
	}
	before, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	b = before.Achievement
	require.Len(a, 3)
	assert.EqualValues(200, a["quest_gift_2"])
	assert.EqualValues(0, b["quest_gift_2"])
	assert.EqualValues(2, b["quest_noble_2"])
	q = &questMessage{
		Type:         QuestTypeHourTop,
		RoomID:       10244,
		dayOfQualify: 2,
		CreatorID:    14,
	}
	_, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	require.Len(a, 4)
	assert.EqualValues(1, a["quest_hourtop_2"])
	q = &questMessage{
		Type:         QuestTypeHourTop,
		RoomID:       10244,
		dayOfQualify: 2,
		CreatorID:    14,
	}
	before, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	require.Len(a, 4)
	assert.EqualValues(2, a["quest_hourtop_2"])
	assert.NotEqual(before.Achievement, after.Achievement)
}

func TestUpdateScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	id1, _ := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a1")
	id2, _ := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a2")
	id3, _ := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a3")
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{id1, id2, id3}}})
	require.NoError(err)

	q2 := QualityQuestRecord{
		OID:         id2,
		EventID:     usersrank.EventIDAnnualLive,
		CreatedTime: goutil.TimeNow(),
	}
	q3 := QualityQuestRecord{
		OID:         id3,
		EventID:     usersrank.EventIDAnnualLive,
		Score:       10,
		CreatedTime: goutil.TimeNow(),
	}
	_, err = liveactivity.Collection().InsertMany(ctx, []interface{}{q2, q3})
	require.NoError(err)

	require.NoError(updateScore(id1, 1), "找不到记录的情况")
	require.NoError(updateScore(id2, 1), "新增 score 字段")
	require.NoError(updateScore(id3, 11), "更新 score 字段")

	match := bson.M{"_id": bson.M{"$in": []primitive.ObjectID{id1, id2, id3}}}
	cur, err := liveactivity.Collection().Find(ctx, match, options.Find().SetProjection(bson.M{"score": 1}))
	require.NoError(err)
	defer cur.Close(ctx)
	var r []QualityQuestRecord
	require.NoError(cur.All(ctx, &r))
	require.Len(r, 2)
	assert.Equal(id2, r[0].OID)
	assert.Equal(int64(1), r[0].Score)
	assert.Equal(id3, r[1].OID)
	assert.Equal(int64(11), r[1].Score)
}

func TestKeyDailyLimit(t *testing.T) {
	assert := assert.New(t)

	allTypes := []string{QuestTypeSuperFan, QuestTypeNoble, QuestTypeGift, QuestTypeHourTop, ""}
	assert.Equal(&QuestConfig{Require: 2, DailyLimit: 5, Score: 1}, keyDailyLimit(allTypes[0]))
	assert.Equal(&QuestConfig{Require: 1, DailyLimit: 5, Score: 1}, keyDailyLimit(allTypes[1]))
	assert.Equal(&QuestConfig{Require: 100, DailyLimit: 5, Score: 1}, keyDailyLimit(allTypes[2]))
	assert.Equal(&QuestConfig{Require: 1, DailyLimit: -1, Score: 2}, keyDailyLimit(allTypes[3]))
	assert.Nil(keyDailyLimit(allTypes[4]))
}

func TestQuestScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	q := QualityQuestRecord{
		EventID:   usersrank.EventIDAnnualLive,
		RoomID:    102443,
		CreatorID: 23,
		Achievement: map[string]int64{
			"quest_superfan_1": 1,
			"quest_superfan_2": 5,
			"quest_superfan_3": 10,
			"quest_superfan_4": 14,
			"quest_noble_1":    5,
			"quest_noble_2":    5,
			"quest_noble_3":    6,
			"quest_noble_4":    6,
			"quest_gift_1":     100,
			"quest_gift_2":     150,
			"quest_gift_3":     500,
			"quest_gift_4":     600,
			"quest_hourtop_1":  8,
			"quest_hourtop_2":  8,
			"quest_hourtop_3":  8,
			"quest_hourtop_4":  8,
			"quest_hourtop_5":  8,
			"invalid":          100,
		},
		CreatedTime: goutil.TimeNow(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": q.CreatorID})
	require.NoError(err)
	_, err = liveactivity.Collection().InsertOne(ctx, q)
	require.NoError(err)
	score, err := QuestScore(q.CreatorID)
	require.NoError(err)
	assert.Equal(int64(108), score)
	q = QualityQuestRecord{
		EventID:   usersrank.EventIDAnnualLive,
		RoomID:    102444,
		CreatorID: 33,
		Achievement: map[string]int64{
			"quest_superfan_1": 1,
			"quest_noble_1":    5,
			"quest_gift_1":     100,
			"quest_hourtop_1":  8,
		},
		CreatedTime: goutil.TimeNow(),
	}
	_, err = liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": q.CreatorID})
	require.NoError(err)
	_, err = liveactivity.Collection().InsertOne(ctx, q)
	require.NoError(err)
	score, err = QuestScore(q.CreatorID)
	require.NoError(err)
	assert.Equal(int64(22), score)
	q = QualityQuestRecord{
		EventID:     usersrank.EventIDAnnualLive,
		RoomID:      102445,
		CreatorID:   33,
		CreatedTime: goutil.TimeNow(),
	}
	_, err = liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": q.CreatorID})
	require.NoError(err)
	_, err = liveactivity.Collection().InsertOne(ctx, q)
	require.NoError(err)
	score, err = QuestScore(q.CreatorID)
	require.NoError(err)
	assert.Equal(int64(0), score)
	// 没有记录的情况
	_, err = liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": q.CreatorID})
	require.NoError(err)
	score, err = QuestScore(q.CreatorID)
	require.NoError(err)
	assert.Equal(int64(0), score)
}

func TestAward(t *testing.T) {
	creatorID := int64(10246)
	q := &questMessage{
		Type:      QuestTypeHourTop,
		CreatorID: creatorID,
	}
	q.award(10, 9)
	q.award(8, 9)
	q.award(9, 11)
}

func TestUnlockBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(102489)
	boxType := boxprizes.BoxTypeOrdinary
	eventID := int64(usersrank.EventIDAnnualLive)
	q := &questMessage{
		RoomID: roomID,
	}
	service.Redis.Del(keys.KeyBoxPrizeAndCount3.Format(eventID, roomID, boxType))
	q.unlockBox(boxType)
	val, err := service.Redis.HGetAll(keys.KeyBoxPrizeAndCount3.Format(eventID, roomID, boxType)).Result()
	require.NoError(err)
	pc, err := boxprizes.PrizeConfig(usersrank.EventIDAnnualLive, boxType)
	require.NoError(err)
	result := make(map[string]string)
	for k, v := range pc {
		result[strconv.FormatInt(k, 10)] = strconv.FormatInt(v.PrizeCount, 10)
	}
	assert.Equal(result, val)
	require.NoError(service.Redis.HSet(keys.KeyBoxRoomUserReceiveCount3.Format(eventID, roomID, boxType), "12", 1).Err(), "用户领取了普通宝箱的奖品")
	q.unlockBox(boxType)
	count, err := service.Redis.Exists(keys.KeyBoxRoomUserReceiveCount3.Format(eventID, roomID, boxType)).Result()
	require.NoError(err)
	assert.Zero(count, "重复开启普通宝箱删除所有用户的普通宝箱抽奖次数")
	val, err = service.Redis.HGetAll(keys.KeyBoxPrizeAndCount3.Format(eventID, roomID, boxType)).Result()
	require.NoError(err)
	for k, v := range pc {
		result[strconv.FormatInt(k, 10)] = strconv.FormatInt(v.PrizeCount*2, 10)
	}
	assert.Equal(result, val, "重复开启宝箱奖品累加")
}

func TestSendQuestMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.Redis.Del(keys.KeyAnnualHourTop0.Format()).Err())
	service.DatabusPub.ClearDebugPubMsgs()
	defer service.DatabusPub.ClearDebugPubMsgs()
	SendQuestMessage(3, 10, "test", 1)
	SendQuestMessage(4, 5, QuestTypeHourTop, 0)
	SendQuestMessage(4, 6, QuestTypeHourTop, 0)

	msgs := service.DatabusPub.DebugPubMsgs()
	require.Len(msgs, 2)
	m := <-msgs
	require.Equal(keys.KeyAnnualQualifyQuest1.Format(10), m.Key)

	var qm questMessage
	require.NoError(json.Unmarshal(m.Value, &qm))
	assert.EqualValues(3, qm.RoomID)
	assert.EqualValues(10, qm.CreatorID)

	m = <-msgs
	qm = questMessage{}
	require.NoError(json.Unmarshal(m.Value, &qm))
	assert.EqualValues(4, qm.RoomID)
	assert.EqualValues(5, qm.CreatorID)
}

func TestQualifyOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(102448)
	creatorID := int64(102448)
	f := QualifyOperator()
	todayBegin := util.BeginningOfDay(goutil.TimeNow())
	assert.NotPanics(func() { f(&databus.Message{}) })
	qm := questMessage{
		Type:       QuestTypeGift,
		CreatorID:  creatorID,
		RoomID:     roomID,
		GiftNum:    100,
		CreateTime: todayBegin.Unix(),
	}
	f(&databus.Message{
		Key:   keys.KeyAnnualQualifyQuest1.Format(creatorID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})

	service.Cache5Min.Delete(keys.KeyEventInfo1.Format(usersrank.EventIDAnnualLive))
	e := activity.ExtendedFields{
		Ranks: []activity.Rank{
			{
				Key:           "03",
				RankStartTime: todayBegin.Unix(),
				RankEndTime:   todayBegin.Add(23 * time.Hour).Unix(),
			},
		},
	}
	v, _ := json.Marshal(e)
	err := service.DB.Table(mevent.TableName()).Where("id = ?", usersrank.EventIDAnnualLive).
		Updates(map[string]interface{}{"extended_fields": string(v)}).Error
	require.NoError(err)
	qm.CreateTime = goutil.TimeNow().Unix()
	f(&databus.Message{
		Key:   keys.KeyAnnualQualifyQuest1.Format(creatorID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
}

func TestTodayFinishedQuestDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	todayBegin := util.BeginningOfDay(goutil.TimeNow())
	service.Cache5Min.Delete(keys.KeyEventInfo1.Format(usersrank.EventIDAnnualLive))
	e := activity.ExtendedFields{
		Ranks: []activity.Rank{
			{
				Key:           "03",
				RankStartTime: todayBegin.Add(-24 * time.Hour).Unix(),
				RankEndTime:   todayBegin.Add(23 * time.Hour).Unix(),
			},
			{
				Key:           "04",
				RankStartTime: todayBegin.Add(24 * time.Hour).Unix(),
				RankEndTime:   todayBegin.Add(47 * time.Hour).Unix(),
			},
			{
				Key:           "06",
				RankStartTime: todayBegin.Add(48 * time.Hour).Unix(),
				RankEndTime:   todayBegin.Add(71 * time.Hour).Unix(),
			},
		},
	}
	v, _ := json.Marshal(e)
	err := service.DB.Table(mevent.TableName()).Where("id = ?", usersrank.EventIDAnnualLive).
		Updates(map[string]interface{}{"extended_fields": string(v)}).Error
	require.NoError(err)

	randCreatorID := goutil.TimeNow().Unix()
	d, n, err := TodayFinishedQuestDetail(randCreatorID)
	require.NoError(err)
	require.Zero(n)
	assert.ElementsMatch([]QuestConfig{{
		Require: 2, DailyLimit: 5, Score: 1, QuestType: "superfan"}, {Require: 1, DailyLimit: 5, Score: 1, QuestType: "noble"},
		{Require: 100, DailyLimit: 5, Score: 1, QuestType: "gift"}, {Require: 1, DailyLimit: -1, Score: 2, QuestType: "hourtop"}}, d)

	id := int64(102467)
	q1 := QualityQuestRecord{
		EventID:   usersrank.EventIDAnnualLive,
		RoomID:    id,
		CreatorID: id,
		Achievement: map[string]int64{
			"quest_superfan_2": 3,
			"quest_superfan_4": 11,
			"quest_noble_1":    5,
			"quest_noble_2":    5,
			"quest_noble_4":    6,
			"quest_gift_1":     100,
			"quest_gift_4":     600,
			"quest_hourtop_1":  18,
			"quest_hourtop_2":  18,
			"quest_hourtop_4":  28,
			"quest_hourtop_5":  58,
		},
		CreatedTime: goutil.TimeNow(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": id})
	require.NoError(err)
	_, err = liveactivity.Collection().InsertOne(ctx, q1)
	require.NoError(err)
	d, n, err = TodayFinishedQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		2, 5, 1, "superfan", 0}, {1, 5, 1, "noble", 5},
		{100, 5, 1, "gift", 100}, {1, -1, 2, "hourtop", 18}}, d)
	require.Equal(int64(64), n)
	goutil.SetTimeNow(func() time.Time {
		return todayBegin.Add(30 * time.Hour)
	})
	defer goutil.SetTimeNow(nil)
	d, n, err = TodayFinishedQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		2, 5, 1, "superfan", 3}, {1, 5, 1, "noble", 5},
		{100, 5, 1, "gift", 0}, {1, -1, 2, "hourtop", 18}}, d)
	require.Equal(int64(64), n)
	goutil.SetTimeNow(func() time.Time {
		return todayBegin.Add(50 * time.Hour)
	})
	d, n, err = TodayFinishedQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		2, 5, 1, "superfan", 10}, {1, 5, 1, "noble", 5},
		{100, 5, 1, "gift", 500}, {1, -1, 2, "hourtop", 28}}, d)
	require.Equal(int64(64), n)
	goutil.SetTimeNow(func() time.Time {
		return todayBegin.Add(100 * time.Hour)
	})
	d, n, err = TodayFinishedQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		2, 5, 1, "superfan", 0}, {1, 5, 1, "noble", 0},
		{100, 5, 1, "gift", 0}, {1, -1, 2, "hourtop", 0}}, d)
	require.Equal(int64(64), n)
}

func TestUpScoreCreatorIDsStr(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	id1 := int64(102489)
	id2 := int64(102490)
	id3 := int64(102491)
	id4 := int64(102492)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": []int64{id1, id2, id3, id4}}})
	require.NoError(err)
	q1 := QualityQuestRecord{
		EventID:     usersrank.EventIDAnnualLive,
		RoomID:      id1,
		CreatorID:   id1,
		Score:       157,
		CreatedTime: goutil.TimeNow(),
	}
	q2 := QualityQuestRecord{
		EventID:     usersrank.EventIDAnnualLive,
		RoomID:      id2,
		CreatorID:   id2,
		Score:       401,
		CreatedTime: goutil.TimeNow(),
	}
	q3 := QualityQuestRecord{
		EventID:     usersrank.EventIDAnnualLive,
		RoomID:      id3,
		CreatorID:   id3,
		Score:       30,
		CreatedTime: goutil.TimeNow(),
	}
	q4 := QualityQuestRecord{
		EventID:     usersrank.EventIDAnnualLive,
		RoomID:      id4,
		CreatorID:   id4,
		CreatedTime: goutil.TimeNow(),
	}
	_, err = liveactivity.Collection().InsertMany(ctx, []interface{}{q1, q2, q3, q4})
	require.NoError(err)

	v, err := UpScoreCreatorIDsStr(30)
	require.NoError(err)
	assert.Contains(v, strconv.FormatInt(id1, 10))
	assert.Contains(v, strconv.FormatInt(id2, 10))
	assert.Contains(v, strconv.FormatInt(id3, 10))
	assert.NotContains(v, strconv.FormatInt(id4, 10))

	v, err = UpScoreCreatorIDsStr(150)
	require.NoError(err)
	assert.Contains(v, strconv.FormatInt(id1, 10))
	assert.Contains(v, strconv.FormatInt(id2, 10))
	assert.NotContains(v, strconv.FormatInt(id3, 10))
	assert.NotContains(v, strconv.FormatInt(id4, 10))
	v, err = UpScoreCreatorIDsStr(400)
	require.NoError(err)
	assert.NotContains(v, strconv.FormatInt(id1, 10))
	assert.Contains(v, strconv.FormatInt(id2, 10))
	v, err = UpScoreCreatorIDsStr(401)
	require.NoError(err)
	assert.Contains(v, strconv.FormatInt(id2, 10))
	v, err = UpScoreCreatorIDsStr(1000)
	require.NoError(err)
	assert.NotContains(v, strconv.FormatInt(id1, 10))
	assert.NotContains(v, strconv.FormatInt(id2, 10))
}
