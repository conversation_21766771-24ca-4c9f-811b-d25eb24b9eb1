package annual

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/livedb/roombox"
	"github.com/MiaoSiLa/live-service/models/mongodb/boxprizes"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 移除超粉嘉年华相关代码

const (
	// QuestTypeSuperFan 新增超粉
	QuestTypeSuperFan = "superfan"
	// QuestTypeNoble 增加新秀及以上的贵族
	QuestTypeNoble = "noble"
	// QuestTypeGift 收到指定礼物
	QuestTypeGift = "gift"
	// QuestTypeHourTop 获得小时榜第一
	QuestTypeHourTop = "hourtop"
)

const (
	// QualifyDays 资格赛的天数
	QualifyDays = 4
)

const (
	// scoreAddPointMulti1 亲密度加 1 倍需要的神明之钥数量
	scoreAddPointMulti1 = 20
	// scoreAddPointMulti2 亲密度加 2 倍需要的神明之钥数量
	scoreAddPointMulti2 = 40
)

const (
	// rankKeyQualifyingDay4 资格赛最后一天榜单的 key
	rankKeyQualifyingDay4 = "06"
)

// QualityQuestRecord 数据库中存储的资格赛任务的数据
type QualityQuestRecord struct {
	OID         primitive.ObjectID `bson:"_id,omitempty"`
	EventID     int64              `bson:"event_id"`
	RoomID      int64              `bson:"room_id"`
	CreatorID   int64              `bson:"creator_id"`
	Achievement map[string]int64   `bson:"achievement"`
	Score       int64              `bson:"score"` // 神明之钥数量
	CreatedTime time.Time          `bson:"created_time,omitempty"`
	UpdatedTime time.Time          `bson:"updated_time,omitempty"`
}

// QuestConfig 任务的配置详情
type QuestConfig struct {
	Require    int64 // 完成一次任务的要求，比如增加 2 个超级粉丝视为完成一次任务，则此值为 2
	DailyLimit int64 // 任务单日完成次数限制，为 -1 表示无单日次数限制
	Score      int64 // 完成一次任务任务可获得的神明之钥数量
	// 下面两个字段用于 TodayFinishedQuestDetail
	QuestType string // 任务类型
	Num       int64  // 对应任务完成详情，如超粉新增人数、礼物收集个数等
}

// questMessage 完成的任务详情
type questMessage struct {
	RoomID     int64  `json:"room_id"`
	CreatorID  int64  `json:"creator_id"`
	Type       string `json:"type"`
	GiftNum    int64  `json:"gift_num"`
	CreateTime int64  `json:"create_time"`

	dayOfQualify int
}

// SendQuestMessage 发送 message 消息
func SendQuestMessage(roomID int64, creatorID int64, questType string, giftNum int64) {
	_, e, err := activity.FindLiveExtendedFields(usersrank.EventIDAnnualLive, true)
	if err != nil {
		logger.Error(err)
		return
	}
	now := e.TimeNow()
	m := &questMessage{
		RoomID:     roomID,
		Type:       questType,
		CreatorID:  creatorID,
		GiftNum:    giftNum,
		CreateTime: now.Unix(),
	}
	if questType == QuestTypeHourTop {
		// 最后一个上小时榜第一任务完成时间不在资格赛时间内，将此任务的 CreateTime 调用时间向前偏移 1 小时
		m.CreateTime -= 3600
		hour := now.Add(-time.Hour).Format("2006010215")
		ok, err := service.Redis.HSet(keys.KeyAnnualHourTop0.Format(), hour, creatorID).Result()
		if err != nil {
			logger.WithFields(logger.Fields{"hour": hour, "creator_id": creatorID}).Error(err)
			return
		}
		if ok == 0 {
			// 说明此小时的任务已经被完成了，不必再发送消息
			return
		}
	}
	key := keys.KeyAnnualQualifyQuest1.Format(creatorID)
	err = service.DatabusSend(key, m)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func achievementKey(questType string, day int) string {
	return fmt.Sprintf("quest_%s_%d", questType, day)
}

func (q QualityQuestRecord) score() int64 {
	achievement := q.Achievement
	var score int64
	allTypes := []string{QuestTypeSuperFan, QuestTypeNoble, QuestTypeGift, QuestTypeHourTop}
	for _, questType := range allTypes {
		config := keyDailyLimit(questType)
		if config == nil {
			panic("数据库任务类型错误")
		}
		for i := 1; i <= QualifyDays; i++ {
			// i 表示资格赛的第几天
			value := achievement[achievementKey(questType, i)]
			value = value / config.Require
			if config.DailyLimit > 0 && value > config.DailyLimit {
				value = config.DailyLimit
			}
			score += value * config.Score
		}
	}
	return score
}

// updateQuestKey 更新任务对应 key 的值
func (q *questMessage) updateQuestKey() (before, after QualityQuestRecord, err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	keyToUpdate := achievementKey(q.Type, q.dayOfQualify)
	key := fmt.Sprintf("achievement.%s", keyToUpdate)
	updates := bson.M{
		"$set": bson.M{
			// REVIEW: updated_time 应该不需要时间偏移
			"updated_time": goutil.TimeNow(),
		},
		"$setOnInsert": bson.M{
			// REVIEW: created_time 应该不需要时间偏移
			"created_time": goutil.TimeNow(),
		},
	}
	switch q.Type {
	case QuestTypeSuperFan, QuestTypeNoble, QuestTypeHourTop:
		// TODO: 针对 QuestTypeHourTop 的情况，需要维护一个 redis set，记录哪些小时任务的消息被处理过，被处理过则直接 return
		updates["$inc"] = bson.M{key: 1}
	case QuestTypeGift:
		updates["$inc"] = bson.M{key: q.GiftNum}
	default:
		panic("任务类型错误")
	}
	filter := bson.M{"room_id": q.RoomID, "creator_id": q.CreatorID, "event_id": usersrank.EventIDAnnualLive}
	err = liveactivity.Collection().FindOneAndUpdate(ctx, filter, updates,
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&after)
	before.Achievement = make(map[string]int64, len(after.Achievement))
	for k, v := range after.Achievement {
		if k == keyToUpdate {
			if q.Type == QuestTypeGift {
				v -= q.GiftNum
			} else {
				v--
			}
		}
		before.Achievement[k] = v
	}
	return
}

// updateScore 更新数据库神明之钥数量
func updateScore(id primitive.ObjectID, score int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	updates := bson.M{
		"$set": bson.M{
			// REVIEW: updated_time 应该不需要时间偏移
			"updated_time": goutil.TimeNow(),
			"score":        score,
		},
	}
	_, err := liveactivity.Collection().UpdateOne(ctx, bson.M{"_id": id}, updates)
	return err
}

func keyDailyLimit(questType string) *QuestConfig {
	switch questType {
	case QuestTypeSuperFan:
		return &QuestConfig{Require: 2, DailyLimit: 5, Score: 1}
	case QuestTypeNoble:
		return &QuestConfig{Require: 1, DailyLimit: 5, Score: 1}
	case QuestTypeGift:
		return &QuestConfig{Require: 100, DailyLimit: 5, Score: 1}
	case QuestTypeHourTop:
		return &QuestConfig{Require: 1, DailyLimit: -1, Score: 2}
	}
	return nil
}

// QuestScore 获得神明之钥数量
func QuestScore(creatorID int64) (int64, error) {
	var q QualityQuestRecord
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"creator_id": creatorID, "event_id": usersrank.EventIDAnnualLive}
	err := liveactivity.Collection().FindOne(ctx, filter, options.FindOne().SetProjection(bson.M{"achievement": 1})).Decode(&q)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return 0, nil
		}
		return 0, err
	}
	return q.score(), nil
}

// award 根据神明之钥数量判断是否需要发放奖励，需要的话发放奖励
func (q *questMessage) award(beforeScore, currentScore int64) {
	if currentScore <= beforeScore {
		return
	}
	if beforeScore/10 != currentScore/10 {
		// 主播每获得 10 个神明之钥，发放神明祝信（普通宝箱）
		q.unlockBox(roombox.BoxTypeOrdinary)
	}
	if q.Type == QuestTypeHourTop {
		// 每完成 1 次终极任务（即获得小时榜第一），发放神明馈礼（终极宝箱）
		q.unlockBox(roombox.BoxTypeUltimate)
	}
	q.addPointMulti(beforeScore, currentScore)
}

func (q *questMessage) unlockBox(boxType int) {
	eventID := int64(usersrank.EventIDAnnualLive)
	prizes, err := boxprizes.PrizeConfig(eventID, boxType)
	if err != nil {
		logger.WithFields(logger.Fields{
			"event_id": usersrank.EventIDAnnualLive,
			"box_type": boxType,
		}).Errorf("find box prize config error: %v", err)
		return
	}
	if len(prizes) == 0 {
		logger.WithFields(logger.Fields{
			"event_id": usersrank.EventIDAnnualLive,
			"box_type": boxType,
		}).Errorf("no box prize config")
		return
	}
	pipe := service.Redis.TxPipeline()
	key := keys.KeyBoxPrizeAndCount3.Format(eventID, q.RoomID, boxType)
	for _, v := range prizes {
		pipe.HIncrBy(key, strconv.FormatInt(v.PrizeID, 10), v.PrizeCount)
	}
	// 删除直播间用户领取次数缓存
	pipe.Del(keys.KeyBoxRoomUserReceiveCount3.Format(eventID, q.RoomID, boxType))
	_, err = pipe.Exec()
	if err != nil {
		logger.WithFields(logger.Fields{
			"event_id": usersrank.EventIDAnnualLive,
			"room_id":  q.RoomID,
			"box_type": boxType,
		}).Errorf("unlock box error: %v", err)
		return
	}
	err = roombox.LogUnlockBox(eventID, q.RoomID, boxType)
	if err != nil {
		logger.WithFields(logger.Fields{
			"event_id": usersrank.EventIDAnnualLive,
			"room_id":  q.RoomID,
			"box_type": boxType,
		}).Errorf("create room box log record error: %v", err)
		return
	}
}

func (q *questMessage) addPointMulti(beforeScore, currentScore int64) {
	// TODO: 删除老代码
}

// QualifyOperator 年度直播盛典资格赛任务 operator
func QualifyOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		// prefix 为 keys.KeyAnnualQualifyQuest1 的前缀
		prefix := "annual:qualify_quest:"
		if !strings.HasPrefix(m.Key, prefix) {
			return
		}
		var qm questMessage
		err := json.Unmarshal(m.Value, &qm)
		if err != nil {
			logger.Error(err)
			// PASS
			return
		}

		f := logger.Fields{
			"event_id":    usersrank.EventIDAnnualLive,
			"creator_id":  qm.CreatorID,
			"create_time": qm.CreateTime,
			"quest_type":  qm.Type,
		}

		qm.dayOfQualify, err = activity.AnnualDayOfQualify(time.Unix(qm.CreateTime, 0))
		if err != nil {
			logger.WithFields(f).Errorf("get day of qualify error: %v", err)
			return
		}
		if qm.dayOfQualify == 0 || (qm.dayOfQualify == 4 && time.Unix(qm.CreateTime, 0).Hour() == 23) {
			// 不在资格赛任务期间，第四天 23 点之后不能再完成任务
			return
		}

		before, after, err := qm.updateQuestKey()
		if err != nil {
			logger.WithFields(f).Error(err)
			return
		}
		beforeScore := before.score()
		afterScore := after.score()
		if beforeScore != afterScore {
			if err := updateScore(after.OID, afterScore); err != nil {
				logger.WithFields(f).Error(err)
				// PASS
			}
		}
		qm.award(beforeScore, afterScore)
	}
}

// TodayFinishedQuestDetail 获取直播间某日资格赛任务完成情况和资格赛期间一共完成了多少次终极任务，用于资格赛半窗
// 返回的数组 QuestConfig 依次是本日新增超粉、新增贵族、收取的礼物、获得小时榜第一的情况，根据 QuestType 判断任务类型
func TodayFinishedQuestDetail(creatorID int64) ([]QuestConfig, int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	simple, e, err := activity.FindLiveExtendedFields(usersrank.EventIDAnnualLive, true)
	if err != nil {
		return nil, 0, err
	}
	if simple == nil {
		return nil, 0, errors.New("找不到活动")
	}
	day, err := activity.AnnualDayOfQualify(e.TimeNow())
	if err != nil {
		return nil, 0, err
	}

	typeList := []string{QuestTypeSuperFan, QuestTypeNoble, QuestTypeGift, QuestTypeHourTop}
	configList := make([]QuestConfig, 0, len(typeList))
	proj := bson.M{}
	for _, t := range typeList {
		if day != 0 {
			// 在资格赛最后一天的 23:00 及之后，半窗不显示活动完成详情，day 为 0，不返回任务完成详情
			key := fmt.Sprintf("achievement.%s", achievementKey(t, day))
			proj[key] = 1
		}
		config := keyDailyLimit(t)
		if config == nil {
			panic("任务类型错误")
		}
		configList = append(configList, QuestConfig{
			Require:    config.Require,
			DailyLimit: config.DailyLimit,
			Score:      config.Score,
			QuestType:  t,
		})
	}
	// 查询资格赛期间完成多少次终极任务
	hourTopKeys := make([]string, 0, 4)
	for i := 1; i <= 4; i++ {
		key := achievementKey(QuestTypeHourTop, i)
		hourTopKeys = append(hourTopKeys, key)
		proj[fmt.Sprintf("achievement.%s", key)] = 1
	}

	col := liveactivity.Collection()
	match := bson.M{"creator_id": creatorID, "event_id": usersrank.EventIDAnnualLive}
	var r QualityQuestRecord
	err = col.FindOne(ctx, match, options.FindOne().SetProjection(proj)).Decode(&r)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return configList, 0, nil
		}
		return nil, 0, err
	}
	hourTopNum := int64(0)
	for _, key := range hourTopKeys {
		num := r.Achievement[key]
		hourTopNum += num
	}
	if day == 0 {
		return configList, hourTopNum, nil
	}

	for i, v := range configList {
		num := r.Achievement[achievementKey(v.QuestType, day)]
		c := configList[i]
		maxNumOneDay := c.Require * c.DailyLimit
		if maxNumOneDay > 0 && num > maxNumOneDay {
			// 计算上限
			num = maxNumOneDay
		}
		configList[i].Num = num
	}
	return configList, hourTopNum, nil
}

// UpScoreCreatorIDs 获得所有神明之钥数量总数大于等于 score 的主播 ID 列表
func UpScoreCreatorIDs(score int) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	match := bson.M{"event_id": usersrank.EventIDAnnualLive, "score": bson.M{"$gte": score}}
	cur, err := liveactivity.Collection().Find(ctx, match, options.Find().SetProjection(bson.M{"creator_id": 1}))
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var upCreatorIDs []struct {
		CreatorID int64 `bson:"creator_id"`
	}
	err = cur.All(ctx, &upCreatorIDs)
	if err != nil {
		return nil, err
	}
	ids := make([]int64, 0, len(upCreatorIDs))
	for _, item := range upCreatorIDs {
		ids = append(ids, item.CreatorID)
	}
	return ids, nil
}

// UpScoreCreatorIDsStr 获得所有神明之钥数量总数大于等于 score 的主播 ID 列表
func UpScoreCreatorIDsStr(score int) ([]string, error) {
	ids, err := UpScoreCreatorIDs(score)
	if err != nil {
		return nil, err
	}
	userIDs := make([]string, len(ids))
	for k, v := range ids {
		userIDs[k] = strconv.FormatInt(v, 10)
	}
	return userIDs, nil
}
