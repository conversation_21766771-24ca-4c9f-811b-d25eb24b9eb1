package liveactivity

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var collectionActivityRoomID = int64(4381915)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)

	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestKeys(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(tutil.KeyExists(tutil.BSON, LiveActivities{}, "creator_id", "room_id", "achievement", "event_id",
		"score", "created_time", "updated_time"))
	assert.Empty(tutil.KeyExists(tutil.JSON, LiveActivities{}, "achievement", "score", "room", "rank", "is_mine"))

	assert.Empty(tutil.KeyExists(tutil.BSON, Achievement{}, "gift_124", "gift_117", "gift_134"))
	assert.Empty(tutil.KeyExists(tutil.JSON, Achievement{}, "gift_124", "gift_117", "gift_134"))
}

func TestListLiveActivities(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	filter := bson.M{"activity_date": usersrank.ActivityMayCollection, "room_id": collectionActivityRoomID}
	res, err := ListLiveActivities(filter, nil)
	require.NoError(err)
	for _, lc := range res {
		assert.Equal(collectionActivityRoomID, lc.RoomID)
		assert.Equal(collectionActivityRoomID, lc.Room.RoomID)
	}
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("test_live_activities", Collection().Name())
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	f := bson.M{"activity_date": usersrank.ActivityQixiFestivalDay, "room_id": collectionActivityRoomID}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, f)
	require.NoError(err)
	res, err := FindOne(f)
	require.NoError(err)
	assert.Nil(res)

	_, err = Collection().InsertOne(ctx, f)
	require.NoError(err)
	res, err = FindOne(f)
	require.NoError(err)
	assert.Equal(collectionActivityRoomID, res.RoomID)
}

func TestFindUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"event_id": usersrank.EventIDHeartAdministration}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	la := LiveActivities{
		CreatorID: 11,
		EventID:   usersrank.EventIDHeartAdministration,
		Score:     2500,
	}
	_, err = Collection().InsertOne(ctx, la)
	require.NoError(err)
	ids, err := FindUserIDs(filter)
	require.NoError(err)
	assert.NotNil(ids)

	filter = bson.M{"score": 25000, "event_id": usersrank.EventIDHeartAdministration}
	ids, err = FindUserIDs(filter)
	require.NoError(err)
	assert.Nil(ids)
}
