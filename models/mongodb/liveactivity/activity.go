package liveactivity

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// CollectionName collection name without prefix
const CollectionName = "live_activities"

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

var activityProjection = bson.M{
	"room_id":     1,
	"creator_id":  1,
	"achievement": 1,
	"score":       1,
}

// LiveActivities mongodb 查找的单条 activity 信息的结构
type LiveActivities struct {
	CreatorID   int64            `bson:"creator_id" json:"-"`
	RoomID      int64            `bson:"room_id" json:"-"`
	Achievement map[string]int64 `bson:"achievement" json:"achievement"`
	EventID     int64            `bson:"event_id" json:"-"`
	Score       int64            `bson:"score" json:"score"`
	CreatedTime time.Time        `bson:"created_time" json:"-"`
	UpdatedTime time.Time        `bson:"updated_time" json:"-"`

	Room   *room.Simple `bson:"-" json:"room"`
	Rank   int64        `bson:"-" json:"rank"`
	IsMine bool         `bson:"-" json:"is_mine,omitempty"`
}

// Achievement 收集进度
// TODO: 待删除
type Achievement struct {
	Gift124 int64 `bson:"gift_124" json:"gift_124"`
	Gift117 int64 `bson:"gift_117" json:"gift_117"`
	Gift134 int64 `bson:"gift_134" json:"gift_134"`
}

// Record 数据库中存储的数据
type Record struct {
	OID         primitive.ObjectID `bson:"_id,omitempty"`
	EventID     int64              `bson:"event_id" json:"-"`
	RoomOID     primitive.ObjectID `bson:"_room_id"`
	RoomID      int64              `bson:"room_id"`
	CreatorID   int64              `bson:"creator_id"`
	Score       int64              `bson:"score"`
	Achievement Achievement        `bson:"achievement"`
	CreatedTime time.Time          `bson:"created_time,omitempty"`
	UpdatedTime time.Time          `bson:"updated_time,omitempty"`
}

// ListLiveActivities 根据参数查询列表
func ListLiveActivities(filter interface{}, mongoOpt *options.FindOptions) ([]*LiveActivities, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)

	if mongoOpt == nil {
		mongoOpt = options.Find()
	}
	if mongoOpt.Projection == nil {
		mongoOpt.SetProjection(activityProjection)
	}

	cur, err := collection.Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*LiveActivities
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	userIDs := make([]int64, 0, len(res))
	for _, r := range res {
		userIDs = append(userIDs, r.CreatorID)
	}
	opt := &room.FindOptions{FindCreator: true}
	roomsMap, err := room.FindSimpleMapByCreatorID(userIDs, opt)
	if err != nil {
		return nil, err
	}
	for _, r := range res {
		r.Room = roomsMap[r.CreatorID]
	}
	return res, nil
}

// FindOne 根据参数查询活动榜单数据榜单
func FindOne(filter interface{}) (*LiveActivities, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res := new(LiveActivities)
	err := Collection().FindOne(ctx, filter).Decode(res)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}

	opt := &room.FindOptions{FindCreator: true}
	res.Room, err = room.FindOneSimple(bson.M{"creator_id": res.CreatorID}, opt)
	if err != nil {
		logger.Error(err)
		return res, nil
	}
	return res, nil
}

// FindUserIDs 查找符合条件的用户 ID
func FindUserIDs(filter interface{}) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var res []*LiveActivities
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	var userIDs []int64
	for _, r := range res {
		userIDs = append(userIDs, r.CreatorID)
	}
	return userIDs, nil
}
