package box

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/boxprizes"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)

	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(QuestRecord{}, "_id", "event_id", "room_id", "creator_id",
		"achievement", "score", "created_time", "updated_time")
}

func TestAchievementKey(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("quest_superfan_1", achievementKey(QuestTypeSuperFan, 1))
	assert.Equal("quest_gift_1", achievementKey(QuestTypeGift, 1))
	assert.Equal("quest_noble_2", achievementKey(QuestTypeNoble, 2))
}

func TestScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	q := QuestRecord{}
	assert.Zero(q.score(1))
	q = QuestRecord{
		CreatorID: 20220507,
		Achievement: map[string]int64{
			"quest_superfan_1": 5,
			"quest_superfan_2": 5,
			"quest_superfan_3": 10,
			"quest_noble_1":    1,
			"quest_noble_2":    2,
			"quest_noble_3":    10,
			"quest_gift_1":     5000,
			"quest_gift_2":     12500,
			"quest_gift_3":     36000,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": q.CreatorID})
	require.NoError(err)
	assert.Equal(int64(7), q.score(1))
	assert.Equal(int64(11), q.score(2))
	assert.Equal(int64(21), q.score(3))
}

func TestUpdateQuestKey(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(10244)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveactivity.Collection().DeleteOne(ctx, bson.M{"room_id": roomID})
	require.NoError(err)

	q := &questMessage{
		Type:      QuestTypeSuperFan,
		RoomID:    10244,
		dayOfBox:  1,
		CreatorID: 14,
	}
	before, after, err := q.updateQuestKey()
	require.NoError(err)
	a := after.Achievement
	b := before.Achievement
	require.Len(a, 1)
	assert.EqualValues(1, a["quest_superfan_1"])
	assert.EqualValues(0, b["quest_superfan_1"])
	q = &questMessage{
		Type:      QuestTypeNoble,
		RoomID:    10244,
		dayOfBox:  2,
		CreatorID: 14,
	}
	_, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	require.Len(a, 2)
	assert.EqualValues(1, a["quest_noble_2"])
	q = &questMessage{
		Type:      QuestTypeNoble,
		RoomID:    10244,
		dayOfBox:  2,
		CreatorID: 14,
	}
	_, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	require.Len(a, 2)
	assert.EqualValues(2, a["quest_noble_2"])
	q = &questMessage{
		Type:      QuestTypeGift,
		RoomID:    10244,
		Revenue:   200,
		dayOfBox:  2,
		CreatorID: 14,
	}
	before, after, err = q.updateQuestKey()
	require.NoError(err)
	a = after.Achievement
	b = before.Achievement
	require.Len(a, 3)
	assert.EqualValues(200, a["quest_gift_2"])
	assert.EqualValues(0, b["quest_gift_2"])
	assert.EqualValues(2, b["quest_noble_2"])
}

func TestKeyDailyLimit(t *testing.T) {
	assert := assert.New(t)

	allTypes := []string{QuestTypeSuperFan, QuestTypeNoble, QuestTypeGift}
	assert.Equal(&QuestConfig{Require: 1, DailyLimit: 5, Score: 1}, keyDailyLimit(allTypes[0]))
	assert.Equal(&QuestConfig{Require: 1, DailyLimit: 5, Score: 2}, keyDailyLimit(allTypes[1]))
	assert.Equal(&QuestConfig{Require: 6000, DailyLimit: -1, Score: 1}, keyDailyLimit(allTypes[2]))
}

func TestAward(t *testing.T) {
	require := require.New(t)

	creatorID := int64(10246)
	q := &questMessage{
		Type:      QuestTypeGift,
		CreatorID: creatorID,
	}
	require.NotPanics(func() { q.award(1, 4) })
	require.NotPanics(func() { q.award(4, 5) })
	require.NotPanics(func() { q.award(4, 35) })
}

func TestUnlockUserBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(102489)
	boxType := boxprizes.BoxLevelS
	eventID := int64(usersrank.EventID196)

	now := goutil.TimeNow()
	day := now.Format(util.TimeFormatYMDWithNoSpace)
	keyBoxPrize := keys.KeyBoxPrizeAndCount4.Format(eventID, day, roomID, boxType)
	keyBoxRoomUserReceive := keys.KeyBoxRoomUserReceiveCount4.Format(eventID, day, roomID, boxType)
	require.NoError(service.Redis.Del(keyBoxPrize).Err())
	unlockUserBox(roomID, now.Unix(), boxType)
	val, err := service.Redis.HGetAll(keyBoxPrize).Result()
	require.NoError(err)
	pc, err := boxprizes.PrizeConfig(usersrank.EventID196, boxType)
	require.NoError(err)
	result := make(map[string]string)
	for k, v := range pc {
		result[strconv.FormatInt(k, 10)] = strconv.FormatInt(v.PrizeCount, 10)
	}
	assert.Equal(result, val)
	require.NoError(service.Redis.HSet(keyBoxRoomUserReceive, "12", 1).Err(), "用户领取了普通宝箱的奖品")
	unlockUserBox(roomID, now.Unix(), boxType)
	count, err := service.Redis.Exists(keyBoxRoomUserReceive).Result()
	require.NoError(err)
	assert.Zero(count, "重复开启 S 级宝箱删除所有用户的普通宝箱抽奖次数")
	val, err = service.Redis.HGetAll(keyBoxPrize).Result()
	require.NoError(err)
	for k, v := range pc {
		result[strconv.FormatInt(k, 10)] = strconv.FormatInt(v.PrizeCount*2, 10)
	}
	assert.Equal(result, val, "重复开启宝箱奖品累加")
}

func TestSendQuestMessage(t *testing.T) {
	t.Skip()
	assert := assert.New(t)
	require := require.New(t)

	service.DatabusPub.ClearDebugPubMsgs()
	defer service.DatabusPub.ClearDebugPubMsgs()
	SendQuestMessage(3, 10, "test", 1)
	SendQuestMessage(4, 5, QuestTypeGift, 6000)

	msgs := service.DatabusPub.DebugPubMsgs()
	require.Len(msgs, 2)
	m := <-msgs
	require.Equal(keys.KeyActivityEvent196Quest1.Format(10), m.Key)

	var qm questMessage
	require.NoError(json.Unmarshal(m.Value, &qm))
	assert.EqualValues(3, qm.RoomID)
	assert.EqualValues(10, qm.CreatorID)

	m = <-msgs
	qm = questMessage{}
	require.NoError(json.Unmarshal(m.Value, &qm))
	assert.EqualValues(4, qm.RoomID)
	assert.EqualValues(5, qm.CreatorID)
}

func TestActivityEvent196Operator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(102448)
	creatorID := int64(102448)
	f := ActivityEvent196Operator()
	todayBegin := util.BeginningOfDay(goutil.TimeNow())
	assert.NotPanics(func() { f(&databus.Message{}) })
	qm := questMessage{
		Type:       QuestTypeGift,
		CreatorID:  creatorID,
		RoomID:     roomID,
		Revenue:    100,
		CreateTime: todayBegin.Unix(),
	}
	f(&databus.Message{
		Key:   keys.KeyActivityEvent196Quest1.Format(creatorID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})

	service.Cache5Min.Delete(keys.KeyEventInfo1.Format(usersrank.EventID196))
	e := activity.ExtendedFields{
		Ranks: []activity.Rank{
			{
				Key:           "box_3",
				RankStartTime: todayBegin.Unix(),
				RankEndTime:   todayBegin.Add(23 * time.Hour).Unix(),
			},
		},
	}
	v, _ := json.Marshal(e)
	err := service.DB.Table(mevent.TableName()).Where("id = ?", usersrank.EventID196).
		Updates(map[string]interface{}{"extended_fields": string(v)}).Error
	require.NoError(err)
	qm.CreateTime = goutil.TimeNow().Unix()
	f(&databus.Message{
		Key:   keys.KeyActivityEvent196Quest1.Format(creatorID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
}

func TestTodayQuestDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	todayBegin := util.BeginningOfDay(goutil.TimeNow())
	service.Cache5Min.Delete(keys.KeyEventInfo1.Format(usersrank.EventID196))
	e := activity.ExtendedFields{
		Ranks: []activity.Rank{
			{
				Key:           "box_1",
				RankStartTime: todayBegin.Add(-24 * time.Hour).Unix(),
				RankEndTime:   todayBegin.Add(23 * time.Hour).Unix(),
			},
			{
				Key:           "box_2",
				RankStartTime: todayBegin.Add(24 * time.Hour).Unix(),
				RankEndTime:   todayBegin.Add(47 * time.Hour).Unix(),
			},
			{
				Key:           "box_3",
				RankStartTime: todayBegin.Add(48 * time.Hour).Unix(),
				RankEndTime:   todayBegin.Add(71 * time.Hour).Unix(),
			},
		},
	}
	v, _ := json.Marshal(e)
	err := service.DB.Table(mevent.TableName()).Where("id = ?", usersrank.EventID196).
		Updates(map[string]interface{}{"extended_fields": string(v)}).Error
	require.NoError(err)

	randCreatorID := goutil.TimeNow().Unix()
	d, info, err := TodayQuestDetail(randCreatorID)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		Require: 1, DailyLimit: 5, Score: 1, QuestType: "superfan"}, {Require: 1, DailyLimit: 5, Score: 2, QuestType: "noble"},
		{Require: 6000, DailyLimit: -1, Score: 1, QuestType: "gift"}}, d)
	assert.Zero(info.Score)
	assert.Equal(boxprizes.BoxLevelC, info.QuestType)
	assert.Equal(int64(5), info.RankUp)

	id := int64(102467)
	q1 := QuestRecord{
		EventID:   usersrank.EventID196,
		RoomID:    id,
		CreatorID: id,
		Achievement: map[string]int64{
			"quest_superfan_2": 3,
			"quest_superfan_3": 11,
			"quest_noble_1":    5,
			"quest_noble_2":    5,
			"quest_noble_3":    6,
			"quest_gift_1":     6001,
			"quest_gift_3":     126000,
		},
		CreatedTime: goutil.TimeNow(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = liveactivity.Collection().DeleteOne(ctx, bson.M{"creator_id": id})
	require.NoError(err)
	_, err = liveactivity.Collection().InsertOne(ctx, q1)
	require.NoError(err)
	// 第一天
	d, info, err = TodayQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		1, 5, 1, "superfan", 0, 0}, {1, 5, 2, "noble", 5, 0},
		{6000, -1, 1, "gift", 1, 1}}, d)
	assert.Equal(int64(11), info.Score)
	assert.Equal(boxprizes.BoxLevelA, info.QuestType)
	assert.Equal(int64(9), info.RankUp)
	assert.Zero(info.LevelSBoxOpenTimes)
	goutil.SetTimeNow(func() time.Time {
		return todayBegin.Add(30 * time.Hour)
	})
	defer goutil.SetTimeNow(nil)
	// 第二天
	d, info, err = TodayQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		1, 5, 1, "superfan", 3, 0}, {1, 5, 2, "noble", 5, 0},
		{6000, -1, 1, "gift", 0, 0}}, d)
	assert.Equal(int64(13), info.Score)
	assert.Equal(boxprizes.BoxLevelA, info.QuestType)
	assert.Equal(int64(7), info.RankUp)
	assert.Zero(info.LevelSBoxOpenTimes)
	goutil.SetTimeNow(func() time.Time {
		return todayBegin.Add(50 * time.Hour)
	})
	// 第三天
	d, info, err = TodayQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		1, 5, 1, "superfan", 5, 0}, {1, 5, 2, "noble", 5, 0},
		{6000, -1, 1, "gift", 21, 0}}, d)
	assert.Equal(int64(36), info.Score)
	assert.Equal(boxprizes.BoxLevelS, info.QuestType)
	assert.Equal(int64(14), info.RankUp)
	assert.Equal(int64(1), info.LevelSBoxOpenTimes)
	goutil.SetTimeNow(func() time.Time {
		return todayBegin.Add(100 * time.Hour)
	})
	// 活动结束
	d, info, err = TodayQuestDetail(id)
	require.NoError(err)
	assert.ElementsMatch([]QuestConfig{{
		1, 5, 1, "superfan", 0, 0}, {1, 5, 2, "noble", 0, 0},
		{6000, -1, 1, "gift", 0, 0}}, d)
	require.Zero(info.Score)
}

func TestNewBoxQuestInfo(t *testing.T) {
	assert := assert.New(t)

	tests := []struct {
		name       string
		score      int64
		wantType   int
		wantRankUp int64
	}{
		{
			name:       "未能解锁 C 级宝箱",
			score:      1,
			wantType:   boxprizes.BoxLevelC,
			wantRankUp: 4,
		},
		{
			name:       "未能解锁 S 级宝箱",
			score:      32,
			wantType:   boxprizes.BoxLevelS,
			wantRankUp: 3,
		},
		{
			name:       "刚好解锁 C 宝箱",
			score:      5,
			wantType:   boxprizes.BoxLevelB,
			wantRankUp: 5,
		},
		{
			name:       "刚好解锁 B 宝箱",
			score:      10,
			wantType:   boxprizes.BoxLevelA,
			wantRankUp: 10,
		},
		{
			name:       "刚好解锁 S 宝箱",
			score:      35,
			wantType:   boxprizes.BoxLevelS,
			wantRankUp: 15,
		},
		{
			name:       "多次解锁 S 宝箱",
			score:      56,
			wantType:   boxprizes.BoxLevelS,
			wantRankUp: 9,
		},
	}
	for _, tt := range tests {
		assert.Equal(tt.wantType, newBoxQuestInfo(tt.score).QuestType, tt.name)
		assert.Equal(tt.wantRankUp, newBoxQuestInfo(tt.score).RankUp, tt.name)
	}
}
