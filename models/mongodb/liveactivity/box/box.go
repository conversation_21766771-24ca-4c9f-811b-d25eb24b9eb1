package box

import (
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/livedb/roombox"
	"github.com/MiaoSiLa/live-service/models/mongodb/boxprizes"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// QuestTypeSuperFan 新增超粉
	QuestTypeSuperFan = "superfan"
	// QuestTypeNoble 增加新秀及以上的贵族
	QuestTypeNoble = "noble"
	// QuestTypeGift 收到指定礼物
	QuestTypeGift = "gift"
)

// QuestRecord 数据库中存储的宝箱任务的数据
type QuestRecord struct {
	OID         primitive.ObjectID `bson:"_id,omitempty"`
	EventID     int64              `bson:"event_id"`
	RoomID      int64              `bson:"room_id"`
	CreatorID   int64              `bson:"creator_id"`
	Achievement map[string]int64   `bson:"achievement"`
	Score       int64              `bson:"score"` // 灵力值
	CreatedTime time.Time          `bson:"created_time,omitempty"`
	UpdatedTime time.Time          `bson:"updated_time,omitempty"`
}

// QuestConfig 任务的配置详情
type QuestConfig struct {
	Require    int64 // 完成一次任务的要求，比如增加 2 个超级粉丝视为完成一次任务，则此值为 2
	DailyLimit int64 // 任务单日完成次数限制，为 -1 表示无单日次数限制
	Score      int64 // 完成一次任务任务可获得的灵力值

	QuestType string // 任务类型
	Num       int64  // 对应任务完成次数
	Progress  int64  // 当前收集进度
}

// questMessage 完成的任务详情
type questMessage struct {
	RoomID     int64  `json:"room_id"`
	CreatorID  int64  `json:"creator_id"`
	Type       string `json:"type"`
	Revenue    int64  `json:"revenue"`
	CreateTime int64  `json:"create_time"`

	dayOfBox int
}

// SendQuestMessage 发送 message 消息-宝箱积分生产者
func SendQuestMessage(roomID int64, creatorID int64, questType string, revenue int64) {
	/*
		_, e, err := activity.FindLiveExtendedFields(usersrank.EventID196, true)
		if err != nil {
			logger.Error(err)
			return
		}
		m := &questMessage{
			RoomID:     roomID,
			Type:       questType,
			CreatorID:  creatorID,
			Revenue:    revenue,
			CreateTime: e.TimeNow().Unix(),
		}
		key := keys.KeyActivityEvent196Quest1.Format(creatorID)
		err = service.DatabusSend(key, m)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	*/
}

func achievementKey(questType string, day int) string {
	return fmt.Sprintf("quest_%s_%d", questType, day)
}

func (q QuestRecord) score(boxDay int) int64 {
	achievement := q.Achievement
	var score int64
	allTypes := []string{QuestTypeSuperFan, QuestTypeNoble, QuestTypeGift}
	for _, questType := range allTypes {
		config := keyDailyLimit(questType)
		if config == nil {
			panic("数据库任务类型错误")
		}
		value := achievement[achievementKey(questType, boxDay)]
		value = value / config.Require
		if config.DailyLimit > 0 && value > config.DailyLimit {
			value = config.DailyLimit
		}
		score += value * config.Score
	}
	return score
}

// updateQuestKey 更新任务对应 key 的值
func (q *questMessage) updateQuestKey() (before, after QuestRecord, err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	keyToUpdate := achievementKey(q.Type, q.dayOfBox)
	key := fmt.Sprintf("achievement.%s", keyToUpdate)
	updates := bson.M{
		"$set": bson.M{
			"updated_time": goutil.TimeNow(),
		},
		"$setOnInsert": bson.M{
			"created_time": goutil.TimeNow(),
		},
	}
	switch q.Type {
	case QuestTypeSuperFan, QuestTypeNoble:
		updates["$inc"] = bson.M{key: 1}
	case QuestTypeGift:
		updates["$inc"] = bson.M{key: q.Revenue}
	default:
		panic("任务类型错误")
	}
	filter := bson.M{"room_id": q.RoomID, "creator_id": q.CreatorID, "event_id": usersrank.EventID196}
	err = liveactivity.Collection().FindOneAndUpdate(ctx, filter, updates,
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&after)
	before.Achievement = make(map[string]int64, len(after.Achievement))
	for k, v := range after.Achievement {
		if k == keyToUpdate {
			if q.Type == QuestTypeGift {
				v -= q.Revenue
			} else {
				v--
			}
		}
		before.Achievement[k] = v
	}
	return
}

func keyDailyLimit(questType string) *QuestConfig {
	switch questType {
	case QuestTypeSuperFan:
		return &QuestConfig{Require: 1, DailyLimit: 5, Score: 1}
	case QuestTypeNoble:
		return &QuestConfig{Require: 1, DailyLimit: 5, Score: 2}
	case QuestTypeGift:
		return &QuestConfig{Require: 6000, DailyLimit: -1, Score: 1}
	}
	return nil
}

// award 根据灵力值判断是否需要发放奖励，需要的话发放奖励
func (q *questMessage) award(beforeScore, currentScore int64) {
	if currentScore <= beforeScore {
		return
	}
	boxes := boxesNeedAdd(beforeScore, currentScore)
	if len(boxes) == 0 {
		return
	}
	// 闯关成功开启宝箱
	goUnlockboxes(q.RoomID, q.CreateTime, boxes)
	// 给主播发热度卡
	creatorReward(q.CreatorID, boxes)
	// 全站飘屏
	sendBoxLevelSBroadcast(q.RoomID, q.CreatorID, boxes)
}

func goUnlockboxes(roomID, nowUnix int64, boxes []int) {
	goutil.Go(func() {
		for i := range boxes {
			unlockUserBox(roomID, nowUnix, boxes[i])
		}
	})
}

func unlockUserBox(roomID, nowUnix int64, boxType int) {
	eventID := int64(usersrank.EventID196)
	prizes, err := boxprizes.PrizeConfig(eventID, boxType)
	if err != nil {
		logger.WithFields(logger.Fields{
			"event_id": eventID,
			"box_type": boxType,
		}).Errorf("find box prize config error: %v", err)
		return
	}
	if len(prizes) == 0 {
		logger.WithFields(logger.Fields{
			"event_id": eventID,
			"box_type": boxType,
		}).Errorf("no box prize config")
		return
	}
	day := time.Unix(nowUnix, 0).Format(util.TimeFormatYMDWithNoSpace)
	pipe := service.Redis.TxPipeline()
	key := keys.KeyBoxPrizeAndCount4.Format(eventID, day, roomID, boxType)
	for _, v := range prizes {
		pipe.HIncrBy(key, strconv.FormatInt(v.PrizeID, 10), v.PrizeCount)
	}
	// 删除直播间用户领取次数缓存
	pipe.Del(keys.KeyBoxRoomUserReceiveCount4.Format(eventID, day, roomID, boxType))
	_, err = pipe.Exec()
	if err != nil {
		logger.WithFields(logger.Fields{
			"event_id": eventID,
			"room_id":  roomID,
			"box_type": boxType,
		}).Errorf("unlock box error: %v", err)
		return
	}
	err = roombox.LogUnlockBox(eventID, roomID, boxType)
	if err != nil {
		logger.WithFields(logger.Fields{
			"event_id": eventID,
			"room_id":  roomID,
			"box_type": boxType,
		}).Errorf("create room box log record error: %v", err)
		return
	}
}

func creatorReward(creatorID int64, boxes []int) {
	var (
		gift30003Num int64 // 3k 热度卡
		gift30001Num int64 // 6k 热度卡
	)

	for _, boxType := range boxes {
		switch boxType {
		case boxprizes.BoxLevelC:
			gift30003Num++
		case boxprizes.BoxLevelB:
			gift30001Num++
		case boxprizes.BoxLevelA:
			gift30001Num += 2
		case boxprizes.BoxLevelS:
			gift30001Num += 3
		}
	}

	if gift30003Num > 0 {
		err := sendCreatorGift(creatorID, 30003, gift30003Num)
		if err != nil {
			logger.WithFields(logger.Fields{
				"creator_id": creatorID,
				"gift_id":    30003,
				"gift_num":   gift30003Num}).Error(err)
		}
	}
	if gift30001Num > 0 {
		err := sendCreatorGift(creatorID, 30001, gift30001Num)
		if err != nil {
			logger.WithFields(logger.Fields{
				"creator_id": creatorID,
				"gift_id":    30001,
				"gift_num":   gift30001Num}).Error(err)
		}
	}
}

func sendCreatorGift(creatorID, giftID, giftNum int64) error {
	startTime := goutil.TimeNow().Unix()
	endTime := int64(1653753600) // 2022-05-29 00:00:00
	g, err := gift.FindShowingGiftByGiftID(giftID)
	if err != nil {
		return err
	}
	if g == nil {
		return fmt.Errorf("gift %d not found", giftID)
	}
	err = creatoritems.AddGiftToCreators([]int64{creatorID}, g, giftNum, startTime, endTime)
	if err != nil {
		return err
	}
	return nil
}

func sendBoxLevelSBroadcast(roomID, creatorID int64, boxes []int) {
	boxType := boxes[len(boxes)-1]
	if boxType != boxprizes.BoxLevelS {
		return
	}
	message := `<font color="${highlight_color}">${creator_username}</font>` +
		`<font color="${normal_color}"> 通关了瑞兽试炼~ </font>` +
		`<font color="${highlight_color}">龙腾宝箱</font>` +
		`<font color="${normal_color}">将立即在直播间开启！</font>`
	formatMap := map[string]string{
		"normal_color":    "#FFFFFF",
		"highlight_color": "#FFFFFF",
	}
	u, err := liveuser.Find(creatorID)
	if err != nil {
		logger.Error(err)
		return
	}
	if u == nil {
		logger.Errorf("user %d not found", creatorID)
		return
	}
	formatMap["creator_username"] = html.EscapeString(u.Username)

	b, err := bubble.FindSimple(19601)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if b != nil {
		b.AppendFormatParams(formatMap)
	}
	notify := notifymessages.NewGeneral(roomID, goutil.FormatMessage(message, formatMap), b)
	err = userapi.BroadcastAll(notify)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type powerStage struct {
	BoxType int
	Limit   int64
	Accum   int64
}

var powerStages = [4]powerStage{
	{boxprizes.BoxLevelC, 5, 5},
	{boxprizes.BoxLevelB, 5, 10},
	{boxprizes.BoxLevelA, 10, 20},
	{boxprizes.BoxLevelS, 15, 35},
}

func boxesNeedAdd(before, after int64) []int {
	res := make([]int, 0, 2)
	preLimit := [4]int64{
		powerStages[0].Accum,
		powerStages[1].Accum,
		powerStages[2].Accum,
		powerStages[3].Accum,
	}
	boxes := [4]int{
		boxprizes.BoxLevelC,
		boxprizes.BoxLevelB,
		boxprizes.BoxLevelA,
		boxprizes.BoxLevelS,
	}
	for i := range preLimit {
		if after < preLimit[i] {
			return res
		}
		if before < preLimit[i] {
			// 需要开启 i + 1 等级的宝箱
			before = preLimit[i]
			res = append(res, boxes[i])
		}
	}
	// 计算重复开启第四个宝箱的情况
	count := (after-powerStages[3].Accum)/powerStages[3].Limit - (before-powerStages[3].Accum)/powerStages[3].Limit
	for i := int64(0); i < count; i++ {
		res = append(res, boxprizes.BoxLevelS)
	}
	return res
}

func event196DayByKey(key string) int {
	switch key {
	case "box_1":
		return 1
	case "box_2":
		return 2
	case "box_3":
		return 3
	}
	return 0
}

// Event196BoxDay 获得 t 时刻是宝箱活动的第几天
func Event196BoxDay(t time.Time) (int, error) {
	simple, e, err := activity.FindLiveExtendedFields(usersrank.EventID196, true)
	if err != nil {
		return 0, err
	}
	if simple == nil {
		return 0, errors.New("找不到活动")
	}
	rs := e.RanksByKeys("box_1", "box_2", "box_3")
	for _, r := range rs {
		if t.Unix() >= r.RankStartTime && time.Unix(r.RankEndTime, 0).Day() == t.Day() {
			return event196DayByKey(r.Key), nil
		}
	}
	return 0, nil
}

// ActivityEvent196Operator 196 活动宝箱任务 operator
func ActivityEvent196Operator() func(*databus.Message) {
	return func(m *databus.Message) {
		prefix := "activity:event196_quest:"
		if !strings.HasPrefix(m.Key, prefix) {
			return
		}
		var qm questMessage
		err := json.Unmarshal(m.Value, &qm)
		if err != nil {
			logger.Error(err)
			// PASS
			return
		}

		f := logger.Fields{
			"event_id":    usersrank.EventID196,
			"creator_id":  qm.CreatorID,
			"create_time": qm.CreateTime,
			"quest_type":  qm.Type,
		}

		qm.dayOfBox, err = Event196BoxDay(time.Unix(qm.CreateTime, 0))
		if err != nil {
			logger.WithFields(f).Errorf("get day error: %v", err)
			return
		}
		if qm.dayOfBox == 0 || (qm.dayOfBox == 3 && time.Unix(qm.CreateTime, 0).Hour() == 23) {
			// 不在宝箱任务开启期间，第三天 23 点之后不能再完成任务
			return
		}

		before, after, err := qm.updateQuestKey()
		if err != nil {
			logger.WithFields(f).Error(err)
			return
		}
		beforeScore := before.score(qm.dayOfBox)
		afterScore := after.score(qm.dayOfBox)
		qm.award(beforeScore, afterScore)
	}
}

// QuestInfo 当日宝箱解锁信息
type QuestInfo struct {
	Score     int64 // 当前灵力值
	RankUp    int64 // 解锁下一级所需要的灵力值
	QuestType int   // 待解锁的宝箱等级

	LevelSBoxOpenTimes int64 // s 级宝箱解锁次数
}

// TodayQuestDetail 获取直播间某日资格赛任务完成情况和当日灵力值详情，用于半窗
// 返回的数组 QuestConfig 依次是本日新增超粉、新增贵族、收取的礼物的情况，根据 QuestType 判断任务类型
func TodayQuestDetail(creatorID int64) ([]QuestConfig, QuestInfo, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	simple, e, err := activity.FindLiveExtendedFields(usersrank.EventID196, true)
	if err != nil {
		return nil, QuestInfo{}, err
	}
	if simple == nil {
		return nil, QuestInfo{}, errors.New("找不到活动")
	}
	day, err := Event196BoxDay(e.TimeNow())
	if err != nil {
		return nil, QuestInfo{}, err
	}

	typeList := []string{QuestTypeSuperFan, QuestTypeNoble, QuestTypeGift}
	configList := make([]QuestConfig, 0, len(typeList))
	proj := bson.M{}
	for _, t := range typeList {
		if day != 0 {
			// 在宝箱的最后一天的 23:00 及之后，半窗不显示活动完成详情，day 为 0，不返回任务完成详情
			key := fmt.Sprintf("achievement.%s", achievementKey(t, day))
			proj[key] = 1
		}
		config := keyDailyLimit(t)
		if config == nil {
			panic("任务类型错误")
		}
		configList = append(configList, QuestConfig{
			Require:    config.Require,
			DailyLimit: config.DailyLimit,
			Score:      config.Score,
			QuestType:  t,
		})
	}

	col := liveactivity.Collection()
	match := bson.M{"creator_id": creatorID, "event_id": usersrank.EventID196}
	var r QuestRecord
	err = col.FindOne(ctx, match, options.FindOne().SetProjection(proj)).Decode(&r)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return configList, QuestInfo{
				RankUp:    powerStages[0].Accum,
				QuestType: boxprizes.BoxLevelC,
			}, nil
		}
		return nil, QuestInfo{}, err
	}

	if day == 0 {
		return configList, QuestInfo{}, nil
	}
	var score int64
	for i, v := range configList {
		value := r.Achievement[achievementKey(v.QuestType, day)]
		config := configList[i]
		num := value / config.Require
		if config.DailyLimit > 0 && value > config.DailyLimit {
			num = config.DailyLimit
		}
		configList[i].Num = num
		configList[i].Progress = value % config.Require
		score += num * config.Score
	}
	return configList, newBoxQuestInfo(score), nil
}

func newBoxQuestInfo(score int64) (info QuestInfo) {
	info.Score = score
	for i := range powerStages {
		if score < powerStages[i].Accum {
			// 未到下一关卡
			info.QuestType = powerStages[i].BoxType
			info.RankUp = powerStages[i].Accum - score
			return
		} else if score == powerStages[i].Accum {
			// 刚好解锁关卡
			if i < 3 {
				info.QuestType = powerStages[i+1].BoxType
				info.RankUp = powerStages[i+1].Limit
			} else {
				info.QuestType = powerStages[i].BoxType
				info.RankUp = powerStages[i].Limit
				info.LevelSBoxOpenTimes = 1
			}
			return
		}
	}
	// 重复解锁第四关卡的情况
	info.QuestType = powerStages[3].BoxType
	info.RankUp = powerStages[3].Limit - ((score - powerStages[3].Accum) % powerStages[3].Limit)
	info.LevelSBoxOpenTimes = (score-powerStages[3].Accum)/powerStages[3].Limit + 1
	return
}
