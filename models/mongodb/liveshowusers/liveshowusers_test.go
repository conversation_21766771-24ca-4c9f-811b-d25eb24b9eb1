package liveshowusers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestFindTopFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("62fb6f90e342003eb1f53a76")
	require.NoError(err)

	user, err := FindTopFan(oid)
	require.NoError(err)
	require.NotNil(user)
	assert.EqualValues(11, user.UserID)
	assert.EqualValues(300, user.Point)
}
