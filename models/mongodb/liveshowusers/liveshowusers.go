package liveshowusers

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// LiveShowUser struct
type LiveShowUser struct {
	OID          primitive.ObjectID `bson:"_id" json:"-"`
	CreateTime   int64              `bson:"create_time" json:"-"`
	ModifiedTime int64              `bson:"modified_time" json:"-"`

	LiveShowOID primitive.ObjectID `bson:"live_show_id" json:"-"`
	UserID      int64              `bson:"user_id" json:"user_id,omitempty"`
	Point       int64              `bson:"point" json:"point,omitempty"`
}

// Collection mongo collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_show_users")
}

// FindTopFan find top fan by live show id
func FindTopFan(oid primitive.ObjectID) (*LiveShowUser, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var user LiveShowUser
	err := Collection().FindOne(ctx,
		bson.M{"live_show_id": oid},
		options.FindOne().SetSort(
			bson.D{
				{Key: "point", Value: -1},
				{Key: "modified_time", Value: 1}, // 分数相同取最先达到该分数的用户
			},
		),
	).Decode(&user)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &user, nil
}
