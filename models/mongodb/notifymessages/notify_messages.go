package notifymessages

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 特效展示层
const (
	EffectLayerGift    = "gift"
	EffectLayerSpecial = "special"
)

// 特效播放方式
const (
	EffectShowRoomNormal = 0 // room_id 对应的房间跳转和在房间内都播放特效
	EffectShowRoomJump   = 1 // 仅在跳转到 room_id 房间时播放，在房间内没跳转不播放（兼容老版本 effect 特效消息）
)

// CollectionName collection name
const CollectionName = "notify_messages"

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// NotifyMessage notify_message 接口
type NotifyMessage interface {
	ID() primitive.ObjectID
	SetID(primitive.ObjectID)
	Time() time.Time
	SetTime(time.Time)
}

// Insert 插入一条新的数据
// NOTICE: v 是出参时才能把 InsertedID 传出
func Insert(v NotifyMessage) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	if v.Time().IsZero() {
		v.SetTime(goutil.TimeNow())
	}
	res, err := Collection().InsertOne(ctx, v)
	if err != nil {
		return err
	}
	id, ok := res.InsertedID.(primitive.ObjectID)
	if !ok {
		return fmt.Errorf("_id not found: %v", res.InsertedID)
	}
	v.SetID(id)
	return nil
}

// Basic 每条插入的数据应该必须都有的字段
type Basic struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`

	Type        int       `bson:"type" json:"-"` // mongodb 索引
	IMType      string    `bson:"-" json:"type"` // 总是 notify
	NotifyType  string    `bson:"notify_type" json:"notify_type"`
	Event       string    `bson:"event" json:"event"`
	NotifyQueue int       `bson:"notify_queue,omitempty" json:"notify_queue,omitempty"`
	IP          string    `bson:"ip,omitempty" json:"-"`
	CreateTime  time.Time `bson:"create_time" json:"-"`
}

// ID 返回 _id
func (b *Basic) ID() primitive.ObjectID {
	return b.OID
}

// SetID 设置 _id
func (b *Basic) SetID(id primitive.ObjectID) {
	b.OID = id
}

// Time 返回 create_time
func (b *Basic) Time() time.Time {
	return b.CreateTime
}

// SetTime 设置 create_time
func (b *Basic) SetTime(t time.Time) {
	b.CreateTime = t
}

// Horn 全站喇叭消息
type Horn struct {
	Basic `bson:",inline"`

	UserID  int64  `bson:"user_id" json:"-"`       // 发起人
	RoomID  int64  `bson:"room_id" json:"room_id"` // 发起人所在房间
	Message string `bson:"message" json:"message"`

	Bubble       *bubble.Simple `bson:"-" json:"bubble,omitempty"` // 4.5.3 5.4.3 弃用此字段
	NotifyBubble *bubble.Simple `bson:"-" json:"notify_bubble,omitempty"`
}

// General 通用的消息
// notify_type: message
// event: new
type General struct {
	Basic `bson:",inline"`

	RoomID       int64          `bson:"room_id" json:"room_id,omitempty"`
	Message      string         `bson:"message" json:"message"`
	NotifyBubble *bubble.Simple `bson:"notify_bubble,omitempty" json:"notify_bubble,omitempty"`
	Effect       *liveim.Effect `bson:"effect,omitempty" json:"effect,omitempty"`
	EffectShow   int            `bson:"effect_show,omitempty" json:"effect_show,omitempty"`
	EffectLayer  string         `bson:"effect_layer,omitempty" json:"effect_layer,omitempty"`
}

// NewHorn new Horn
func NewHorn(userID, roomID int64, message string, notifyBubble *bubble.Simple, ip string) *Horn {
	return &Horn{
		Basic: Basic{
			Type:       TypeHorn,
			IMType:     liveim.TypeNotify,
			NotifyType: liveim.TypeMessage,
			Event:      liveim.EventHorn,
			IP:         ip,
		},
		UserID:       userID,
		RoomID:       roomID,
		Message:      message,
		Bubble:       notifyBubble,
		NotifyBubble: notifyBubble,
	}
}

// NewGiftNotify gift notify
func NewGiftNotify(userID, roomID int64, message string, notifyBubble *bubble.Simple) *Horn {
	return &Horn{
		Basic: Basic{
			Type:       TypeGift,
			IMType:     liveim.TypeNotify,
			NotifyType: liveim.TypeGift,
			Event:      liveim.EventSend,
		},
		UserID:       userID,
		RoomID:       roomID,
		Message:      message,
		NotifyBubble: notifyBubble,
	}
}

// NewGeneral new General
func NewGeneral(roomID int64, message string, notifyBubble *bubble.Simple) *General {
	return &General{
		Basic: Basic{
			Type:       TypeGeneral,
			IMType:     liveim.TypeNotify,
			NotifyType: liveim.TypeMessage,
			Event:      liveim.EventNew,
		},
		RoomID:       roomID,
		Message:      message,
		NotifyBubble: notifyBubble,
	}
}

// NewPriorityNotify new priority notify
func NewPriorityNotify(roomID int64, message string, notifyBubble *bubble.Simple) *General {
	return &General{
		Basic: Basic{
			Type:        TypeGeneral,
			IMType:      liveim.TypeNotify,
			NotifyType:  liveim.TypeMessage,
			Event:       liveim.EventNew,
			NotifyQueue: liveim.NotifyQueuePriority,
		},
		RoomID:       roomID,
		Message:      message,
		NotifyBubble: notifyBubble,
	}
}

// Danmaku 弹幕消息
type Danmaku struct {
	Basic `bson:",inline"`

	UserID        int64          `bson:"user_id" json:"user_id"`
	RoomID        int64          `bson:"room_id" json:"room_id"`
	Message       string         `bson:"message" json:"message"`
	DanmakuBubble *bubble.Simple `bson:"danmaku_bubble,omitempty" json:"danmaku_bubble,omitempty"`
}

// NewDanmaku new danmaku
func NewDanmaku(userID, roomID int64, message string, b *bubble.Simple, ip string) *Danmaku {
	return &Danmaku{
		Basic: Basic{
			Type:   TypeDanmaku,
			IMType: liveim.TypeMessage,
			Event:  liveim.EventDanmaku,
			IP:     ip,
		},
		UserID:        userID,
		RoomID:        roomID,
		Message:       message,
		DanmakuBubble: b,
	}
}
