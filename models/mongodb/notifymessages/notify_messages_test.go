package notifymessages

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func clearOutdated() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res, err := Collection().DeleteMany(ctx, bson.M{"create_time": bson.M{"$lt": goutil.TimeNow().AddDate(0, -1, 0)}})
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Debugf("deleted count: %d", res.DeletedCount)
}

func TestMain(m *testing.M) {
	config.InitTest()
	logger.InitTestLog()
	service.InitTest()
	service.SetDBUseSQLite()

	clearOutdated()

	m.Run()
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)

	col := Collection()
	assert.Equal("test_notify_messages", col.Name())
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Basic{}, "_id", "type", "notify_type", "event", "notify_queue", "ip", "create_time")
	kc.Check(Horn{}, "user_id", "room_id", "message")
	kc.Check(General{}, "room_id", "message", "notify_bubble",
		"effect", "effect_show", "effect_layer")
	kc.Check(Danmaku{}, "user_id", "room_id", "message", "danmaku_bubble")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Basic{}, "type", "notify_type", "event", "notify_queue")
	kc.Check(Horn{}, "room_id", "message", "bubble", "notify_bubble")
	kc.Check(General{}, "room_id", "message", "notify_bubble",
		"effect", "effect_show", "effect_layer")
	kc.Check(Danmaku{}, "user_id", "room_id", "message", "danmaku_bubble")
}

func TestBasicNotifyMessageInterface(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := &Basic{
		CreateTime: time.Unix(100, 0),
	}
	var err error
	hex := "5e71d9f61a19541d6ead2b5b"
	b.OID, err = primitive.ObjectIDFromHex(hex)
	require.NoError(err)

	v := NotifyMessage(b)
	assert.Equal(hex, v.ID().Hex())
	v.SetID(primitive.NilObjectID)
	assert.Equal(primitive.NilObjectID, b.OID)
	assert.Equal(int64(100), v.Time().Unix())
	v.SetTime(time.Unix(200, 0))
	assert.Equal(int64(200), b.CreateTime.Unix())
}

func TestInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	h := NewHorn(12, 123, "TestInsert", nil, "127.0.0.1")
	require.NoError(Insert(h))
	require.NotEqual(primitive.NilObjectID, h.ID())
	assert.False(h.CreateTime.IsZero())

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	h2 := new(Horn)
	require.NoError(Collection().FindOne(ctx, bson.M{"_id": h.ID()}).Decode(&h2))
	assert.Equal(h.Time().Unix(), h2.Time().Unix())
	h.SetTime(h2.Time())
	h.IMType = ""
	assert.Equal(h, h2)
}

func TestNewHorn(t *testing.T) {
	assert := assert.New(t)

	b := &bubble.Simple{Type: "custom", BubbleID: 1}
	h := NewHorn(123, 456, "test", b, "127.0.0.1")
	assert.Equal(primitive.NilObjectID, h.OID)
	assert.Equal(1, h.Type)
	assert.Equal("notify", h.IMType)
	assert.Equal("message", h.NotifyType)
	assert.Equal("horn", h.Event)
	assert.Equal(int64(123), h.UserID)
	assert.Equal(int64(456), h.RoomID)
	assert.Equal("test", h.Message)
	assert.Equal(b, h.Bubble)
	assert.Equal(b, h.NotifyBubble)
	assert.Equal("127.0.0.1", h.IP)
}

func TestNewGeneral(t *testing.T) {
	assert := assert.New(t)

	b := &bubble.Simple{Type: "custom", BubbleID: 1}
	g := NewGeneral(123, "test", b)
	assert.Equal(primitive.NilObjectID, g.OID)
	assert.Equal(3, g.Type)
	assert.Equal("notify", g.IMType)
	assert.Equal("message", g.NotifyType)
	assert.Equal("new", g.Event)
	assert.Equal(int64(123), g.RoomID)
	assert.Equal("test", g.Message)
	assert.Equal(b, g.NotifyBubble)
}

func TestNewPriorityNotify(t *testing.T) {
	assert := assert.New(t)

	g := NewPriorityNotify(123, "test", nil)
	assert.Equal(primitive.NilObjectID, g.OID)
	assert.Equal(3, g.Type)
	assert.Equal("notify", g.IMType)
	assert.Equal("message", g.NotifyType)
	assert.Equal("new", g.Event)
	assert.Equal(1, g.NotifyQueue)
	assert.Equal(int64(123), g.RoomID)
	assert.Equal("test", g.Message)
}

func TestNewDanmaku(t *testing.T) {
	assert := assert.New(t)

	d := NewDanmaku(123, 123, "<font>&nbsp; message &nbsp;</font>", nil, "127.0.0.1")
	assert.Equal(primitive.NilObjectID, d.OID)
	assert.Equal(TypeDanmaku, d.Type)
	assert.Equal(liveim.EventDanmaku, d.Event)
	assert.EqualValues(123, d.RoomID)
	assert.Equal("<font>&nbsp; message &nbsp;</font>", d.Message)
	assert.Equal("127.0.0.1", d.IP)
}
