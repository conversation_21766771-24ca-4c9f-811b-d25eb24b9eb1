package livemedal

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CollectionName collection name
const CollectionName = "live_medals"

// status
const (
	StatusPending = iota
	StatusOwned
	StatusShow
)

// status of share
const (
	ShareStatusPending = iota
	ShareStatusShared
)

// MinContribution 获取勋章的最少贡献
const MinContribution = 60

// 增加亲密度类型
const (
	TypeGiftAddMedalPoint = iota + 1
	TypeQuestionAddMedalPoint
	TypeSuperFanMedalPoint  // 开通续费超粉
	TypeDanmakuMedalPoint   // 付费弹幕
	TypeLuckyBoxMedalPoint  // 宝盒
	TypeTrialCardMedalPoint // 粉丝勋章兑换卡
)

const (
	frameExtPNG  = ".png"
	frameExtWebP = ".webp"
)

// 触发获取勋章的最后操作
const (
	FromNormalConsume  = iota // 通过普通送礼、提问、超能魔方等方式获取勋章
	FromOneClickObtain        // 通过一键送礼获取勋章
	FromAdmin                 // 通过后台补发亲密度获取勋章
	FromBuySuperFan           // 通过直接购买超粉获取勋章
)

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// LiveMedal 直播粉丝勋章
// TODO: 取消 LiveMedal 与 Simple 和 Mini 之间继承关系，合成一个更完整的结构体
type LiveMedal struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	RoomOID      primitive.ObjectID `bson:"_room_id" json:"-"`
	TPoint       int64              `bson:"t_point" json:"-"`
	TUpdatedTime time.Time          `bson:"t_updated_time" json:"-"`
	CreatedTime  time.Time          `bson:"created_time" json:"-"`
	UpdatedTime  time.Time          `bson:"updated_time" json:"-"`

	// TFreePoint 当日获取的免费亲密度
	TFreePoint       *int64     `bson:"t_free_point" json:"-"`
	TFreeUpdatedTime *time.Time `bson:"t_free_updated_time" json:"-"`

	TReducePoint *int64 `bson:"t_reduce_point" json:"-"` // 当日亲密度衰减值，值为负数，如：-25

	Simple `bson:",inline"`

	TodayThreshold int64 `bson:"-" json:"today_threshold"`
	TodayPoint     int64 `bson:"-" json:"today_point"`
}

// CalcData 根据数据库数据计算其他数据
func (lm *LiveMedal) CalcData() {
	if lm.Status == StatusPending {
		return
	}
	lm.calculateTodayThreshold() // TODO: 通过参数控制是否查询
}

func (lm *LiveMedal) calculateTodayThreshold() {
	now := goutil.TimeNow()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	calcPoint := lm.TPoint
	lastTPointUpdateTime := lm.TUpdatedTime
	if lm.TFreeUpdatedTime != nil && lm.TFreeUpdatedTime.After(lastTPointUpdateTime) {
		lastTPointUpdateTime = *lm.TFreeUpdatedTime
	}
	if lastTPointUpdateTime.Before(todayStart) {
		// 今日未获取亲密度
		calcPoint = lm.Point
		lm.TodayPoint = 0
	} else {
		lm.TodayPoint = max(lm.Point-lm.TPoint, 0) // 用户总是不送礼靠免费亲密度升级可能出现负数的情况
	}
	// calcPoint 为负数时，ParseLevel(calcPoint) 为 0 会导致程序崩溃
	if calcPoint < 0 {
		logger.Warnf("calcPoint of user %d is less than 0", lm.UserID)
		calcPoint = 0
	}
	_, thresholdMulti := FindMedalPointMultiple(lm.CreatorID, IsSuperFanActive(lm.SuperFan))
	lm.TodayThreshold = levelThreshold()[ParseLevel(calcPoint)-1] * thresholdMulti
}

// HasActiveSuperFan 判断用户当前是否有开通任意房间的超粉
func HasActiveSuperFan(userID int64) bool {
	lm, err := FindOne(bson.M{
		"user_id":               userID,
		"super_fan.expire_time": bson.M{"$gte": goutil.TimeNow().Unix()}}, nil)
	if err != nil {
		logger.Error(err)
		return false
	}
	if lm == nil {
		return false
	}
	return true
}

// IsSuperFanActive 判断用户超粉是否在有效期
func IsSuperFanActive(sf *SuperFan) bool {
	return sf != nil && goutil.TimeNow().Unix() < sf.ExpireTime
}

// Simple 直播粉丝勋章简单结果
type Simple struct {
	RoomID     int64 `bson:"room_id" json:"room_id"`
	CreatorID  int64 `bson:"creator_id" json:"creator_id"`
	UserID     int64 `bson:"user_id" json:"user_id"` // 勋章拥有者
	Point      int64 `bson:"point" json:"point"`
	Status     int   `bson:"status" json:"status"`
	From       int   `bson:"from" json:"-"`
	LiveStatus *int  `bson:"-" json:"live_status,omitempty"`

	LevelUpPoint int64 `bson:"-" json:"level_up_point"`

	Mini `bson:",inline"`

	// 主播信息
	CreatorUsername string `bson:"-" json:"creator_username,omitempty"`
	CreatorIconURL  string `bson:"-" json:"creator_iconurl,omitempty"`

	// 勋章拥有者信息
	Username string `bson:"-" json:"username,omitempty"`
	IconURL  string `bson:"-" json:"iconurl,omitempty"`

	RankInvisible bool `bson:"-" json:"rank_invisible"`
}

// Mini 显示勋章牌子所需字段
type Mini struct {
	Name string `bson:"name" json:"name"`

	SuperFan *SuperFan `bson:"super_fan,omitempty" json:"super_fan,omitempty"`

	Level     int    `bson:"-" json:"level"`
	FrameURL  string `bson:"-" json:"frame_url,omitempty"`
	NameColor string `bson:"-" json:"name_color,omitempty"`
}

// SuperFan 超粉相关信息
type SuperFan struct {
	ExpireTime int64 `bson:"expire_time" json:"expire_time"` // 过期时间，秒级时间戳

	RegisterTime int64 `bson:"-" json:"register_time,omitempty"` // 开通时间，秒级时间戳
	Days         int64 `bson:"-" json:"days,omitempty"`          // 开通天数
}

// FindCustomMedalFrame 获取定制粉丝勋章
func FindCustomMedalFrame(roomID int64, level int) (string, error) {
	medalFrame, err := liverecommendedelements.FindMedalFrame(roomID)
	if err != nil {
		return "", err
	}
	if medalFrame == "" {
		return "", nil
	}
	// WORKAROUND: ParseSchemeURL 会对占位符做 URLEncode 处理，所以替换完后在做完整地址转换
	// 定制粉丝勋章扩展名不使用占位符替换
	formatMap := map[string]string{
		"level": fmt.Sprintf("%02d", level),
	}
	frameURL := goutil.FormatMessage(medalFrame, formatMap)
	return storage.ParseSchemeURL(frameURL), nil
}

// FindMedalFrame 获取直播间定制和高等级粉丝勋章，定制优先级 > 高等级优先级
func (s *Simple) FindMedalFrame() {
	medalFrame, err := FindCustomMedalFrame(s.RoomID, s.Level)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 如果没有定制粉丝勋章返回默认粉丝勋章
	if medalFrame != "" {
		s.FrameURL = medalFrame
		return
	}

	// 20 级以下不下发默认勋章
	if s.Level <= 20 {
		return
	}
	// 默认粉丝勋章 40 级（现在最高 40 级）默认下发 WebP，其余等级只有 PNG
	formatMap := map[string]string{
		"level": fmt.Sprintf("%02d", s.Level),
	}
	if s.Level >= 40 {
		formatMap["ext"] = frameExtWebP
	} else {
		formatMap["ext"] = frameExtPNG
	}
	defaultFrameURL := config.Conf.Params.UserInfo.DefaultMedalFrameURL
	formatFrameURL := goutil.FormatMessage(defaultFrameURL, formatMap)
	s.FrameURL = storage.ParseSchemeURL(formatFrameURL)
}

// BuildSuperFan 填充超粉信息
func (s *Simple) BuildSuperFan(registerTime int64) {
	s.SuperFan.RegisterTime = registerTime
	registerDay := goutil.BeginningOfDay(time.Unix(s.SuperFan.RegisterTime, 0))
	today := goutil.BeginningOfDay(goutil.TimeNow())
	// 陪伴天数 = 当前日期 - 开通日期 + 1
	s.SuperFan.Days = int64(today.Sub(registerDay).Hours()/24) + 1
	if s.SuperFan.Days < 0 {
		s.SuperFan.Days = 0
	}
}

// From 勋章来源于
type From struct {
	Name            string `json:"name"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	CreatorIconURL  string `json:"creator_iconurl"`
	RoomID          int64  `json:"room_id"`
}

// AfterFind 计算用户等级
// NOTICE: 没对 s.Status 进行判断
func (s *Simple) AfterFind() {
	levelStart := levelStart()
	s.Level = ParseLevel(s.Point)
	if s.Level < len(levelStart) {
		s.LevelUpPoint = levelStart[s.Level]
	} else {
		s.LevelUpPoint = levelStart[len(levelStart)-1]
	}
	if !IsSuperFanActive(s.SuperFan) {
		s.SuperFan = nil
	}
}

// ParseLevel parse live medal point level
func ParseLevel(point int64) int {
	levelStart := levelStart()
	i := 0
	for i < len(levelStart) && point >= levelStart[i] {
		i++
	}
	if i <= 0 {
		// WORKAROUND: 如果 point 传负数, i = 0
		i = 1
	}
	return i
}

// GetDailyReduction 获取衰减亲密度
// TODO: 等级可以通过 point 计算出
func GetDailyReduction(point int64, level int) int {
	dailyReduction := PointDailyReduction()
	if len(dailyReduction) < level {
		return dailyReduction[len(dailyReduction)-1].reductionPoint(point)
	}
	if level > 0 {
		return dailyReduction[level-1].reductionPoint(point)
	}
	return 0
}

// List 查询用户的勋章及附带信息
// NOTICE: 获取亲密度数据 status 需要大于 StatusPending
func List(filter interface{}, mongoOpt *options.FindOptions, opts ...*FindOptions) ([]*LiveMedal, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()

	cur, err := collection.Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*LiveMedal
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	listHelper(res, getOptions(opts))
	return res, nil
}

// ListUserAll 获取用户所有已点亮的勋章及附带信息
func ListUserAll(userID int64, opts ...*FindOptions) ([]*LiveMedal, error) {
	return List(bson.M{
		"user_id": userID,
		"status":  bson.M{"$gt": StatusPending},
	}, nil, opts...)
}

// listHelper 辅助 FindList 用
func listHelper(liveMedals []*LiveMedal, opt *FindOptions) {
	if opt.DisableAll {
		for i := 0; i < len(liveMedals); i++ {
			liveMedals[i].AfterFind()
		}
		return
	}

	simples := make([]*Simple, 0, len(liveMedals))
	for i := range liveMedals {
		simples = append(simples, &liveMedals[i].Simple)
	}
	listSimpleHelper(simples)
	for i := 0; i < len(liveMedals); i++ {
		liveMedals[i].CalcData()
	}
	if opt.OnlyMedal {
		return
	}

	listFindUserInfo(liveMedals, opt.FindCreator, opt.FindUser)
}

// 获取用户信息
func listFindUserInfo(liveMedals []*LiveMedal, findCreator, findUser bool) {
	if !findCreator && !findUser {
		return
	}
	creatorIDs, userIDs := make([]int64, 0, len(liveMedals)), make([]int64, 0, len(liveMedals))
	for i := 0; i < len(liveMedals); i++ {
		if findCreator {
			creatorIDs = append(creatorIDs, liveMedals[i].CreatorID)
		}
		if findUser {
			userIDs = append(userIDs, liveMedals[i].UserID)
		}
	}
	users, err := mowangskuser.FindSimpleMap(append(userIDs, creatorIDs...))
	if err != nil {
		logger.Error(err)
		return
	}
	for i := 0; i < len(liveMedals); i++ {
		if findCreator {
			if u := users[liveMedals[i].CreatorID]; u != nil {
				liveMedals[i].CreatorUsername = u.Username
				liveMedals[i].CreatorIconURL = u.IconURL
			}
		}
		if findUser {
			if u := users[liveMedals[i].UserID]; u != nil {
				liveMedals[i].Username = u.Username
				liveMedals[i].IconURL = u.IconURL
			}
		}
	}
}

// Wear 戴上勋章
func Wear(userID, creatorID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()

	err := collection.FindOne(ctx, bson.M{"user_id": userID, "creator_id": creatorID, "status": StatusOwned}).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}

	now := goutil.TimeNow()
	_, err = collection.BulkWrite(ctx, []mongo.WriteModel{
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{"user_id": userID, "creator_id": creatorID, "status": StatusOwned}).
			SetUpdate(bson.M{"$set": bson.M{"status": StatusShow, "updated_time": now}}),
		mongo.NewUpdateManyModel().
			SetFilter(bson.M{"user_id": userID, "creator_id": bson.M{"$ne": creatorID}, "status": StatusShow}).
			SetUpdate(bson.M{"$set": bson.M{"status": StatusOwned, "updated_time": now}}),
	})
	if err != nil {
		return false, err
	}
	DelUsersTitlesCache(userID)
	return true, nil
}

// TakeOff 脱下勋章
func TakeOff(userID, creatorID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()

	err := collection.FindOneAndUpdate(ctx,
		bson.M{"user_id": userID, "creator_id": creatorID, "status": StatusShow},
		bson.M{"$set": bson.M{"status": StatusOwned, "updated_time": goutil.TimeNow()}},
	).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	DelUsersTitlesCache(userID)
	return true, nil
}

// Remove 移除勋章
func Remove(userID, creatorID, roomID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()

	var lm LiveMedal
	err := collection.FindOneAndDelete(ctx,
		bson.M{
			"user_id":    userID,
			"creator_id": creatorID,
			"status":     bson.M{"$gt": StatusPending},
		}).Decode(&lm)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	SendLiveMedalChange(userID, roomID, ChangeTypeRemove, ChangeReasonLoss, ChangeSourceRemove)
	if lm.Status == StatusShow {
		DelUsersTitlesCache(userID)
	}
	return true, nil
}

// FindSimples 通用的查询接口
// NOTICE: 不查询是否榜单隐身
func FindSimples(filter interface{}, opts *options.FindOptions, findUser, findCreator bool) ([]*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()

	cur, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*Simple
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	listSimpleHelper(res) // TODO: 使用 option 控制
	userIDs, creatorIDs := make([]int64, len(res)), make([]int64, len(res))
	for i := 0; i < len(res); i++ {
		if findCreator {
			creatorIDs[i] = res[i].CreatorID
		}
		if findUser {
			userIDs[i] = res[i].UserID
		}
	}
	if !findUser && !findCreator {
		return res, nil
	}
	users, err := mowangskuser.FindSimpleMap(append(userIDs, creatorIDs...))
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		if findCreator {
			if u := users[res[i].CreatorID]; u != nil {
				res[i].CreatorUsername = u.Username
				res[i].CreatorIconURL = u.IconURL
			}
		}
		if findUser {
			if u := users[res[i].UserID]; u != nil {
				res[i].Username = u.Username
				res[i].IconURL = u.IconURL
			}
		}
	}
	return res, nil
}

// SimpleHelper 获取勋章等级，构建定制勋章框
func (s *Simple) BuildFullInfo() {
	s.AfterFind()
	s.FindMedalFrame()
}

func listSimpleHelper(simples []*Simple) {
	for i := range simples {
		simples[i].BuildFullInfo() // FIXME: 批量获取定制勋章框
	}
}

// FindOne 查询某个勋章
// NOTICE: 不查询是否榜单隐身
func FindOne(filter interface{}, mongoOpts *options.FindOneOptions, opts ...FindOptions) (*LiveMedal, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()
	if mongoOpts == nil {
		mongoOpts = options.FindOne()
	}
	res := new(LiveMedal)
	err := collection.FindOne(ctx, filter, mongoOpts).Decode(res)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	defer func() {
		res.BuildFullInfo()
		res.CalcData()
	}()
	if len(opts) != 0 && opts[0].OnlyMedal {
		return res, nil
	}
	uids := []int64{res.UserID, res.CreatorID}
	users, err := mowangskuser.FindSimpleMap(uids)
	if err != nil {
		return nil, err
	}
	if u := users[res.CreatorID]; u != nil {
		res.CreatorUsername = u.Username
		res.CreatorIconURL = u.IconURL
	}
	if u := users[res.UserID]; u != nil {
		res.Username = u.Username
		res.IconURL = u.IconURL
	}
	return res, nil
}

// FindOwnedMedal 查询用户拥有的指定房间的勋章
func FindOwnedMedal(userID, roomID int64, opts ...FindOptions) (*LiveMedal, error) {
	return FindOne(bson.M{
		"user_id": userID,
		"room_id": roomID,
		"status":  bson.M{"$gt": StatusPending},
	}, nil, opts...)
}

// HasUserOwnedMedal 用户是否拥有指定房间的勋章
func HasUserOwnedMedal(userID, roomID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var medal LiveMedal
	err := Collection().FindOne(ctx, bson.M{
		"room_id": roomID,
		"user_id": userID,
		"status":  bson.M{"$gt": StatusPending},
	}).Decode(&medal)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

// CountMedal 根据条件查询用户勋章数量
func CountMedal(filter interface{}) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	return Collection().CountDocuments(ctx, filter)
}

// CountRoomMedal 直播间勋章数量
func CountRoomMedal(roomID int64) (int64, error) {
	return CountMedal(bson.M{
		"room_id": roomID,
		"status":  bson.M{"$gt": StatusPending},
	})
}

// CountNormalMedal 用户普通粉丝勋章数量（不包含超粉勋章）
func CountNormalMedal(userID int64) (int64, error) {
	return CountMedal(bson.M{
		"user_id":               userID,
		"status":                bson.M{"$gt": StatusPending},
		"super_fan.expire_time": bson.M{"$not": bson.M{"$gt": goutil.TimeNow().Unix()}}, // 超粉字段不存在或已过期
	})
}

// CountSuperMedal 用户生效中的超粉勋章数量
func CountSuperMedal(userID int64) (int64, error) {
	return CountMedal(bson.M{
		"user_id":               userID,
		"status":                bson.M{"$gt": StatusPending},
		"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
	})
}

// CountRoomSuperMedal 直播间生效中的超粉勋章数量
func CountRoomSuperMedal(roomID int64) (int64, error) {
	return CountMedal(bson.M{
		"room_id":               roomID,
		"status":                bson.M{"$gt": StatusPending},
		"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
	})
}

// MedalCount 用户勋章数量
type MedalCount struct {
	FansNum      int64 `bson:"fans_num"`
	SuperFansNum int64 `bson:"super_fans_num"`
}

// FindRoomMedalFansAndSuperFansCount 查询直播间勋章粉丝、超粉数量
func FindRoomMedalFansAndSuperFansCount(roomID int64) (*MedalCount, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	type a bson.A
	type m bson.M
	cur, err := Collection().Aggregate(ctx, a{
		m{"$match": m{"room_id": roomID, "status": m{"$gt": StatusPending}}},
		m{"$group": m{
			"_id":      nil,
			"fans_num": m{"$sum": 1},
			"super_fans_num": m{"$sum": m{
				"$cond": m{
					"if":   m{"$gt": a{"$super_fan.expire_time", goutil.TimeNow().Unix()}},
					"then": 1,
					"else": 0,
				},
			}},
		}},
	})
	if err != nil {
		return nil, err
	}

	var res []MedalCount
	defer cur.Close(ctx)
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	if len(res) == 0 {
		return &MedalCount{}, nil
	}

	return &res[0], nil
}

// UpdateName 更新勋章
func UpdateName(roomID int64, name string) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"room_id": roomID}
	update := bson.M{"name": name, "updated_time": goutil.TimeNow()}
	_, err := Collection().UpdateMany(ctx, filter, bson.M{"$set": update})
	return err
}

// FindShowingMiniMap 查询用户佩戴中的勋章
func FindShowingMiniMap(userIDs []int64) (map[int64]*Mini /* map[userID]*Mini */, error) {
	opts := options.Find().SetProjection(bson.M{"room_id": 1, "user_id": 1, "name": 1, "point": 1, "super_fan": 1})
	// 去重
	userIDs = util.Uniq(userIDs)
	filter := bson.M{"user_id": bson.M{"$in": userIDs}, "status": StatusShow}
	simples, err := FindSimples(filter, opts, false, false)
	if err != nil {
		return nil, err
	}
	listSimpleHelper(simples)
	res := make(map[int64]*Mini, len(simples))
	for i := 0; i < len(simples); i++ {
		res[simples[i].UserID] = &simples[i].Mini
	}
	return res, nil
}

// FindShowingMini 查询某用户佩戴中的勋章 mini 版
func FindShowingMini(userID int64) (*Mini, error) {
	opts := options.FindOne().SetProjection(bson.M{"room_id": 1, "user_id": 1, "name": 1, "point": 1, "super_fan": 1})
	filter := bson.M{"user_id": userID, "status": StatusShow}
	lm, err := FindOne(filter, opts)
	if lm == nil {
		return nil, err
	}
	lm.BuildFullInfo()
	return &lm.Mini, nil
}

// MedalUpdatedInfo 勋章亲密度更新信息
type MedalUpdatedInfo struct {
	Before *LiveMedal
	After  *LiveMedal
}

// UpdateUserShareAndCheck 更新用户的分享状态和时间，并判断是否是今天的第一次分享
func UpdateUserShareAndCheck(roomOID primitive.ObjectID, roomID int64, userID int64) (bool, error) {
	liveUsersCol := service.MongoDB.Collection("live_users")
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lu, err := FindLiveUserShare(userID, roomOID)
	now := goutil.TimeNow()
	bod := util.BeginningOfDay(now)
	set := bson.M{}
	if lu == nil {
		if err != nil {
			return false, err
		}
		_, err = liveUsersCol.InsertOne(ctx, bson.M{
			"_room_id":       roomOID,
			"room_id":        roomID,
			"user_id":        userID,
			"t_status_share": ShareStatusShared,
			"t_time_share":   now,
		})
		if err != nil {
			return false, err
		}
		return true, nil
	}
	if lu.StatusShare != ShareStatusPending && lu.TimeShare.After(bod) {
		// 不为本日首次更新则直接返回
		return false, nil
	}
	// 为本日首次更新
	set["t_status_share"] = ShareStatusShared
	set["t_time_share"] = now
	_, err = liveUsersCol.UpdateOne(ctx, bson.M{"_id": lu.OID}, bson.M{"$set": set})
	if err != nil {
		return false, err
	}
	return true, nil
}

type syncMedalMessage struct {
	Type       string `json:"type"`
	NotifyType string `json:"notify_type"`
	Event      string `json:"event"`
	RoomID     int64  `json:"room_id"`
	UserID     int64  `json:"user_id"`
	Medal      medal  `json:"medal"`
}

type medal struct {
	Name     string    `json:"name"`
	Level    int       `json:"level"`
	FrameURL string    `json:"frame_url,omitempty"`
	SuperFan *SuperFan `json:"super_fan,omitempty"`
}

// syncMedal 同步勋章等级
func SyncMedal(fromRoomID, roomID, userID int64, level int, medalName string) error {
	DelUsersTitlesCache(userID)
	m := Simple{
		RoomID: roomID,
		Mini: Mini{
			Level: level,
		}}
	m.FindMedalFrame()
	p := syncMedalMessage{
		Type:       liveim.TypeUserNotify,
		NotifyType: liveim.TypeMedal,
		Event:      liveim.EventUpdate,
		RoomID:     roomID,
		UserID:     userID,
		Medal: medal{
			Name:     medalName,
			Level:    level,
			FrameURL: m.FrameURL,
		},
	}
	// 跨直播间送礼导致的勋章等级改变，通知发送到送出礼物所在的直播间
	if fromRoomID != 0 {
		return userapi.BroadcastUser(fromRoomID, userID, p)
	}
	return userapi.BroadcastUser(roomID, userID, p)
}

// activityPointMulti 获取直播间亲密度倍数信息
// 直播间单独配置优先级 > 所有直播间配置优先级
func activityPointMulti(creatorID int64) *liverecommendedelements.PointMulti {
	pointMulti, err := liverecommendedelements.FindCurrentRoomMedalPointMulti(creatorID)
	if err != nil {
		logger.WithField("creator_id", creatorID).Error(err)
		return nil
	}
	return pointMulti
}

// FindMedalPointMultiple 获取勋章每日经验上限倍数和增加的经验倍数
func FindMedalPointMultiple(creatorID int64, isSuperFan bool) (pointMulti, thresholdMulti int64) {
	pointMulti, thresholdMulti = 1, 1
	now := goutil.TimeNow()
	if isSuperFan {
		// 超粉亲密度和上限翻倍
		pointMulti++
		thresholdMulti++
		if now.Weekday() == time.Saturday {
			// 超粉在周六亲密度上限为 3 倍
			thresholdMulti++
		}
	}
	currentMulti := activityPointMulti(creatorID)
	if currentMulti == nil {
		return
	}
	pointMulti += currentMulti.PointMultiAdd
	thresholdMulti += currentMulti.ThresholdMultiAdd
	return
}

// LiveUserShare 用户分享消息
type LiveUserShare struct {
	OID         primitive.ObjectID `bson:"_id"`
	RoomOID     primitive.ObjectID `bson:"_room_id"`
	RoomID      int64              `bson:"room_id"`
	UserID      int64              `bson:"user_id"`
	StatusShare int                `bson:"t_status_share"`
	TimeShare   time.Time          `bson:"t_time_share"`
}

// FindLiveUserShare 获取 live_user 的分享信息
func FindLiveUserShare(userID int64, roomOID primitive.ObjectID) (*LiveUserShare, error) {
	liveUsersCol := service.MongoDB.Collection("live_users")
	filter := bson.M{"user_id": userID, "_room_id": roomOID}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var lu LiveUserShare
	err := liveUsersCol.FindOne(ctx, filter, options.FindOne().SetProjection(
		bson.M{"_room_id": 1, "room_id": 1, "user_id": 1, "t_status_share": 1, "t_time_share": 1})).Decode(&lu)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}

	return &lu, nil
}

// DelUsersTitlesCache 删除用户 titles 缓存
func DelUsersTitlesCache(userIDs ...int64) {
	if len(userIDs) == 0 {
		panic("userIDs is empty")
	}
	cacheKeys := make([]string, len(userIDs))
	for i := range userIDs {
		cacheKeys[i] = keys.KeyUsersTitles1.Format(userIDs[i])
	}
	err := service.Redis.Del(cacheKeys...).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// IsRoomSuperFan 查询用户是否为直播间超粉
func IsRoomSuperFan(roomID, userID int64) (bool, error) {
	filter := bson.M{"room_id": roomID, "user_id": userID, "super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()}}
	lm, err := FindOne(filter, nil)
	if lm == nil {
		return false, err
	}
	return true, nil
}

// FindRoomSuperFanMap 批量查询用户直播间超粉身份
func FindRoomSuperFanMap(roomID int64, userIDs []int64) (map[int64]struct{} /* map[userID]struct{} */, error) {
	userIDs = util.Uniq(userIDs)
	filter := bson.M{"room_id": roomID, "user_id": bson.M{"$in": userIDs}, "super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()}}
	simples, err := FindSimples(filter, nil, false, false)
	if err != nil {
		return nil, err
	}
	res := make(map[int64]struct{}, len(simples))
	for i := 0; i < len(simples); i++ {
		res[simples[i].UserID] = struct{}{}
	}
	return res, nil
}

// SetSuperFanExpired 设置超粉过期
func SetSuperFanExpired(roomID int64, userIDs []int64) error {
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateMany(ctx,
		bson.M{"room_id": roomID, "user_id": bson.M{"$in": userIDs}},
		bson.M{"$set": bson.M{"super_fan.expire_time": now.Unix(), "updated_time": now}})
	return err
}

// FindUserMedalsLevelGte 获取用户指定等级的勋章信息
func FindUserMedalsLevelGte(userID int64, level int) ([]*Simple, error) {
	if level > UserMedalLevelLimit() {
		level = UserMedalLevelLimit()
	}
	filter := bson.M{
		"user_id": userID,
		"status":  bson.M{"$gt": StatusPending},
		"point":   bson.M{"$gte": levelStart()[level-1]},
	}
	opt := options.Find().SetSort(bson.M{"point": -1})
	return FindSimples(filter, opt, false, false)
}

// HasUserOwnedMedalByCreatorID 用户是否拥有某个主播的勋章
func HasUserOwnedMedalByCreatorID(userID, creatorID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var medal LiveMedal
	err := Collection().FindOne(ctx, bson.M{
		"user_id":    userID,
		"creator_id": creatorID,
		"status":     bson.M{"$gt": StatusPending},
	}).Decode(&medal)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}
