package livemedal

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNormalPrivilege(t *testing.T) {
	assert := assert.New(t)

	assert.NotEmpty(NormalPrivilege())
}

func TestSuperPrivilege(t *testing.T) {
	assert := assert.New(t)

	privilege := SuperPrivilege(false)
	assert.NotEmpty(privilege)

	privilege2 := SuperPrivilege(true)
	assert.NotEmpty(privilege2)
	assert.NotEqual(privilege, privilege2)
}
