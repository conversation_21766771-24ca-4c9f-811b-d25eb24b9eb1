package livemedal

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	s, lm := Simple{}, LiveMedal{}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(s, "room_id", "creator_id", "user_id", "point", "status", "from")
	kc.Check(s.Mini, "name", "super_fan")
	kc.Check(lm, "_id", "_room_id", "t_point", "t_updated_time", "created_time",
		"updated_time", "t_free_updated_time", "t_free_point", "t_reduce_point")
	kc.Check(SuperFan{}, "expire_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(s, "room_id", "creator_id", "user_id", "status", "live_status", "point",
		"level_up_point", "creator_username", "creator_iconurl", "username",
		"iconurl", "rank_invisible")
	kc.Check(s.Mini, "name", "super_fan", "level", "frame_url", "name_color")
	kc.Check(lm, "today_threshold", "today_point")
	kc.Check(From{}, "name", "creator_id", "creator_username", "creator_iconurl", "room_id")
	kc.Check(SuperFan{}, "expire_time", "register_time", "days")
	kc.Check(MedalTask{}, "task_type", "name", "description", "status")
}

func TestFindCustomMedalFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	frameURL, err := FindCustomMedalFrame(-999, 1)
	require.NoError(err)
	assert.Empty(frameURL)

	service.Cache5Min.Flush()
	frameURL, err = FindCustomMedalFrame(223355, 1)
	require.NoError(err)
	assert.NotEmpty(frameURL)
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png", frameURL)
}

func TestAfterFind(t *testing.T) {
	assert := assert.New(t)

	s := Simple{Point: 200}
	s.SuperFan = &SuperFan{
		ExpireTime: 1,
	}
	s.AfterFind()
	assert.Equal("2300", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Nil(s.SuperFan)

	s.Point = 500
	s.AfterFind()
	assert.Equal("4700", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Empty(s.FrameURL)
}

func TestListSimpleHelper(t *testing.T) {
	assert := assert.New(t)

	s := &Simple{Point: 600000, Status: StatusShow}
	listSimpleHelper([]*Simple{s})
	assert.Equal("20780000", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Empty(s.FrameURL)

	s = &Simple{Point: 1480000, Status: StatusShow}
	listSimpleHelper([]*Simple{s})
	assert.Equal("241780000", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Equal("https://static-test.missevan.com/live/medalframes/normal/level24_0_9_0_54.png", s.FrameURL)

	s = &Simple{Point: 60000000, Status: StatusShow}
	listSimpleHelper([]*Simple{s})
	assert.Equal("4015000000", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Equal("https://static-test.missevan.com/live/medalframes/normal/level40_0_9_0_54.webp", s.FrameURL)

	s = &Simple{RoomID: 223355, Point: 60000000, Status: StatusShow}
	listSimpleHelper([]*Simple{s})
	assert.Equal("4015000000", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level40_0_9_0_54.png", s.FrameURL)

	s = &Simple{Point: 600000, Status: StatusShow}
	listSimpleHelper([]*Simple{s})
	assert.Equal("20780000", fmt.Sprintf("%d%d", s.Level, s.LevelUpPoint))
	assert.Empty(s.NameColor)
	assert.Empty(s.FrameURL)
}

func TestWearTakeOffRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()
	now := goutil.TimeNow().Unix()
	s := Simple{RoomID: now, CreatorID: now, UserID: 12, Status: StatusOwned, Point: 500}
	s.Name = "tiny medal"
	_, err := collection.InsertOne(ctx, s)
	require.NoError(err)
	defer func() {
		_, _ = collection.DeleteMany(ctx, bson.M{"name": "tiny medal"})
	}()

	res, err := Wear(12, now)
	require.NoError(err)
	assert.True(res)
	t.Run("TestFindShowingTinyMedals", subTestFindShowingMiniMap)
	t.Run("TestFindShowingMini", subTestFindShowingMini)
	res, err = TakeOff(12, now)
	require.NoError(err)
	assert.True(res)

	_, err = Wear(12, now)
	require.NoError(err)
	key := keys.KeyUsersTitles1.Format(12)
	require.NoError(service.Redis.Set(key, "[]", time.Minute).Err())
	res, err = Remove(12, now, now)
	require.NoError(err)
	assert.True(res)
	val, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	assert.Zero(val)
	res, err = Remove(123, now, now)
	require.NoError(err)
	assert.False(res)

	// test no document
	ok, err := Wear(783749872389, now)
	require.NoError(err)
	require.False(ok)
}

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()
	s := &Simple{RoomID: 22489473, CreatorID: 10, UserID: 12, Status: StatusOwned, Point: 200}
	err := collection.FindOneAndUpdate(ctx, bson.M{"creator_id": 10, "user_id": 12},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.False(err != nil && err != mongo.ErrNoDocuments, err)

	list, err := List(bson.M{"user_id": 12}, options.Find().SetSort(bson.M{"point": -1}), &FindOptions{FindCreator: true, FindUser: true})
	require.NoError(err)
	assert.NotEmpty(list)
	assert.GreaterOrEqual(len(list), 2)
	assert.GreaterOrEqual(list[0].Point, list[1].Point)
	for _, lm := range list {
		if lm.CreatorID == 10 {
			assert.NotEmpty(lm.CreatorUsername)
			assert.NotEmpty(lm.Username)
		}
	}
}

func TestListUserAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(99999)
	testMedals := []Simple{
		{RoomID: 1, CreatorID: 11, UserID: testUserID, Status: StatusOwned},
		{RoomID: 2, CreatorID: 12, UserID: testUserID, Status: StatusOwned},
		{RoomID: 3, CreatorID: 13, UserID: testUserID, Status: StatusPending},
	}

	// 插入测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	medals := make([]interface{}, 0, len(testMedals))
	for _, m := range testMedals {
		medals = append(medals, m)
	}
	res, err := Collection().InsertMany(ctx, medals)
	require.NoError(err)
	require.Equal(len(testMedals), len(res.InsertedIDs))
	defer func() {
		_, _ = Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	}()

	userAllMedals, err := ListUserAll(testUserID)
	require.NoError(err)
	assert.Equal(len(testMedals)-1, len(userAllMedals))
}

func TestListHelper(t *testing.T) {
	assert := assert.New(t)

	medals := []*LiveMedal{{Simple: Simple{UserID: 10, CreatorID: 12}}}
	listHelper(medals, &FindOptions{OnlyMedal: true})
	assert.Empty(medals[0].CreatorUsername)
	assert.Empty(medals[0].Username)

	medals = []*LiveMedal{{Simple: Simple{UserID: 10, CreatorID: 12}}}
	listHelper(medals, &FindOptions{FindUser: true})
	assert.Empty(medals[0].CreatorUsername)
	assert.NotEmpty(medals[0].Username)

	medals = []*LiveMedal{{Simple: Simple{UserID: 10, CreatorID: 12}}}
	listHelper(medals, &FindOptions{FindCreator: true})
	assert.NotEmpty(medals[0].CreatorUsername)
	assert.Empty(medals[0].Username)

	medals = []*LiveMedal{{Simple: Simple{UserID: 10, CreatorID: 12}}}
	listHelper(medals, &FindOptions{FindCreator: true, FindUser: true})
	assert.NotEmpty(medals[0].CreatorUsername)
	assert.NotEmpty(medals[0].Username)
}

func TestFindSimples(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	res, err := FindSimples(bson.M{"user_id": 12, "creator_id": 10}, nil, true, true)
	require.NoError(err)
	require.NotEmpty(res, 1)
	assert.NotEmpty(res[0].IconURL)
	assert.NotEmpty(res[0].CreatorIconURL)
	res, err = FindSimples(bson.M{"user_id": 12, "creator_id": 10}, nil, false, false)
	require.NoError(err)
	assert.NotZero(res[0].Level)
}

func TestLiveMedalCalcData(t *testing.T) {
	assert := assert.New(t)
	var lm LiveMedal
	now := goutil.TimeNow()
	lm.Point = 200
	lm.TUpdatedTime = now
	lm.TPoint = 100
	lm.CalcData()
	assert.Zero(lm.TodayPoint)
	lm.Status = StatusShow
	lm.CalcData()
	assert.Equal(int64(100), lm.TodayPoint)
	// assert.Equal(int64(500), lm.TodayThreshold)
	lm.TUpdatedTime = time.Unix(0, 0)
	lm.TFreeUpdatedTime = new(time.Time)
	*lm.TFreeUpdatedTime = now.Add(time.Minute)
	lm.CalcData()
	assert.Equal(int64(100), lm.TodayPoint)
	lm.TUpdatedTime = time.Unix(0, 0)
	lm.TFreeUpdatedTime = &lm.TUpdatedTime
	lm.CalcData()
	assert.Zero(lm.TodayPoint)
	if 1578326400 <= now.Unix() && now.Unix() < 1579708800 {
		assert.Equal(int64(1000), lm.TodayThreshold) // 双倍亲密度上限
	} else {
		assert.Equal(int64(500), lm.TodayThreshold) // 正常亲密度上限
	}
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	res, err := FindOne(bson.M{"user_id": 12, "creator_id": 10}, nil)
	require.NoError(err)
	require.NotNil(res)
	assert.NotEmpty(res.IconURL)
	assert.NotEmpty(res.CreatorIconURL)
	// assert.NotZero(res.TodayThreshold)
	res, err = FindOne(bson.M{"user_id": 12, "creator_id": 10}, nil, FindOptions{OnlyMedal: true})
	require.NoError(err)
	require.NotNil(res)
	assert.Empty(res.IconURL)
	assert.Empty(res.CreatorIconURL)
}

func TestHasUserOwnedMedal(t *testing.T) {
	require := require.New(t)

	res, err := FindOne(bson.M{"user_id": 12, "creator_id": 10}, nil, FindOptions{OnlyMedal: true})
	require.NoError(err)
	require.NotNil(res)

	hasOwnedMedal, err := HasUserOwnedMedal(12, res.RoomID)
	require.NoError(err)
	require.True(hasOwnedMedal)

	hasOwnedMedal, err = HasUserOwnedMedal(9999, -9999)
	require.NoError(err)
	require.False(hasOwnedMedal)
}

func TestCountRoomMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(1234)
	)

	count, err := CountRoomMedal(-12334)
	require.NoError(err)
	assert.Zero(count)

	count, err = CountRoomMedal(testRoomID)
	require.NoError(err)
	assert.NotZero(count)
}

func TestCountNormalMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count, err := CountNormalMedal(-12334)
	require.NoError(err)
	assert.Zero(count)
	count, err = CountNormalMedal(12)
	require.NoError(err)
	assert.NotZero(count)
}

func TestCountSuperMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(-20210414)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lm := LiveMedal{
		Simple: Simple{
			RoomID:    1234,
			CreatorID: 10,
			UserID:    testUserID,
			Point:     1,
			Status:    StatusOwned,
			Mini: Mini{
				Name:     "Test",
				SuperFan: &SuperFan{ExpireTime: goutil.TimeNow().Add(10 * time.Minute).Unix()},
			},
		},
	}
	_, err := Collection().UpdateOne(ctx, bson.M{"user_id": testUserID}, bson.M{"$set": lm},
		options.Update().SetUpsert(true))
	require.NoError(err)
	count, err := CountSuperMedal(testUserID)
	require.NoError(err)
	assert.Equal(int64(1), count)
	assert.Zero(CountSuperMedal(-9999999))
}

func TestFindRoomMedalFansAndSuperFansCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(10341)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"room_id": 347142109, "status": bson.M{"$gt": StatusPending}}
	_, err := Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	lm := LiveMedal{
		Simple: Simple{
			RoomID:    347142109,
			CreatorID: 9075623,
			UserID:    testUserID,
			Point:     1,
			Status:    StatusOwned,
			Mini: Mini{
				Name: "Ayaka 勋章测试",
			},
		},
	}
	lm2 := LiveMedal{
		Simple: Simple{
			RoomID:    347142109,
			CreatorID: 9075623,
			UserID:    testUserID + 1,
			Point:     1,
			Status:    StatusOwned,
			Mini: Mini{
				Name:     "Ayaka 勋章测试",
				SuperFan: &SuperFan{ExpireTime: goutil.TimeNow().Add(10 * time.Minute).Unix()},
			},
		},
	}
	lm3 := LiveMedal{
		Simple: Simple{
			RoomID:    347142110,
			CreatorID: 9075624,
			UserID:    testUserID + 2,
			Point:     1,
			Status:    StatusOwned,
			Mini: Mini{
				Name: "Ayaka 勋章测试",
			},
		},
	}
	_, err = Collection().UpdateOne(ctx,
		bson.M{"user_id": testUserID, "room_id": lm.RoomID},
		bson.M{"$set": lm},
		options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = Collection().UpdateOne(ctx,
		bson.M{"user_id": testUserID + 1, "room_id": lm.RoomID},
		bson.M{"$set": lm2},
		options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = Collection().UpdateOne(ctx,
		bson.M{"user_id": testUserID + 2, "room_id": lm3.RoomID},
		bson.M{"$set": lm3},
		options.Update().SetUpsert(true))
	require.NoError(err)

	result, err := FindRoomMedalFansAndSuperFansCount(lm.RoomID)
	require.NoError(err)
	assert.Equal(int64(2), result.FansNum)
	assert.Equal(int64(1), result.SuperFansNum)

	result, err = FindRoomMedalFansAndSuperFansCount(lm3.RoomID)
	require.NoError(err)
	assert.Equal(int64(1), result.FansNum)
	assert.Zero(result.SuperFansNum)

	result, err = FindRoomMedalFansAndSuperFansCount(-999999)
	require.NoError(err)
	require.NotNil(result)
	assert.Zero(result.FansNum)
	assert.Zero(result.SuperFansNum)
}

func TestUpdateName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	roomID := int64(1234567)
	userID := int64(12)

	// 手动插入测试数据
	lm := LiveMedal{
		Simple: Simple{
			RoomID:    roomID,
			CreatorID: 3456837,
			UserID:    userID,
			Point:     100,
			Status:    StatusOwned,
			Mini: Mini{
				Name: "test",
			},
		},
	}
	_, err := Collection().UpdateOne(ctx, bson.M{"room_id": roomID, "user_id": userID},
		bson.M{"$set": lm}, options.Update().SetUpsert(true))
	require.NoError(err)

	require.NoError(UpdateName(roomID, "test update"))
	foundLm, err := FindOne(bson.M{"room_id": roomID, "user_id": userID}, nil)
	require.NoError(err)
	require.NotNil(foundLm)
	assert.Equal("test update", foundLm.Name)
	assert.NoError(UpdateName(roomID, "test"))
}

func TestLiveMedalCalculateTodayThreshold(t *testing.T) {
	assert := assert.New(t)
	now := goutil.TimeNow()
	lm := LiveMedal{
		TPoint:       1000,
		TUpdatedTime: now.Add(-24 * time.Hour),
	}
	lm.Point = 1500
	lm.AfterFind()
	assert.Equal(7, lm.Level)
	// 昨天加过亲密度，今天还没加
	lm.calculateTodayThreshold()
	assert.Equal(int64(1000), lm.TodayThreshold)
	// 今天加过亲密度，刚升级上来
	lm.TUpdatedTime = now
	lm.calculateTodayThreshold()
	assert.Equal(int64(500), lm.TodayThreshold)
	// 如果有了粉丝勋章后但是用户之后没有持续增加亲密度，亲密度会衰减
	// 但是亲密度一般不会衰减为负数，这里测试负数是为了防止手动修改数据库值导致亲密度为负数时程序奔溃
	lm.Point = -1
	lm.TUpdatedTime = now.Add(-24 * time.Hour)
	lm.TodayThreshold = 0
	lm.AfterFind()
	lm.calculateTodayThreshold()
}

func TestIsSuperFanActive(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsSuperFanActive(nil))
	assert.False(IsSuperFanActive(&SuperFan{}))
	assert.True(IsSuperFanActive(&SuperFan{
		ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
	}))
}

// TestWearTakeOffRemove 中调用
func subTestFindShowingMiniMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID = int64(3456835)
	var testRoomID = int64(1234567)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lm := LiveMedal{
		Simple: Simple{
			RoomID: testRoomID,
			UserID: testUserID,
			Point:  1,
			Status: StatusShow,
			Mini: Mini{
				Name: "test",
			},
		},
	}
	_, err := Collection().UpdateOne(ctx, bson.M{"user_id": testUserID, "room_id": testRoomID}, bson.M{"$set": lm},
		options.Update().SetUpsert(true))
	require.NoError(err)
	res, err := FindShowingMiniMap([]int64{12, 100, testUserID})
	require.NoError(err)
	require.Len(res, 2)
	assert.Equal(Mini{Level: 4, Name: "tiny medal"}, *res[12])
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png", res[testUserID].FrameURL)
}

// TestWearTakeOffRemove 中调用
func subTestFindShowingMini(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()

	res, err := FindShowingMini(12)
	require.NoError(err)
	assert.NotNil(res)
	assert.Equal(Mini{Level: 4, Name: "tiny medal"}, *res)

	res, err = FindShowingMini(3456835)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal("https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png", res.FrameURL)
}

func TestSyncMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res := make([]syncMedalMessage, 0, 3)
	broadCastMap := make(map[int64]syncMedalMessage)
	cancel := mrpc.SetMock("im://broadcast/user", func(input interface{}) (output interface{}, err error) {
		var body struct {
			RoomID  int64            `json:"room_id,omitempty"`
			UserID  int64            `json:"user_id,omitempty"`
			Payload syncMedalMessage `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		res = append(res, body.Payload)
		broadCastMap[body.RoomID] = body.Payload
		return "success", nil
	})
	defer cancel()
	const (
		fromRoomID = 123
		roomID     = 22489473
		userID     = 12345678
		level      = 5
		maxLevel   = 40
		name       = "小恶魔"
	)
	err := SyncMedal(0, roomID, userID, level, name)
	require.NoError(err)
	require.Len(res, 1)
	assert.Empty(res[0].Medal.FrameURL)

	err = SyncMedal(0, roomID, userID, maxLevel, name)
	require.NoError(err)
	require.Len(res, 2)
	assert.NotEmpty(res[1].Medal.FrameURL)

	err = SyncMedal(fromRoomID, roomID, userID, maxLevel, name)
	require.NoError(err)
	require.Len(res, 3)
	require.NotEmpty(broadCastMap[fromRoomID])
	assert.Equal(roomID, int(broadCastMap[fromRoomID].RoomID))
}

func TestActivityPointMulti(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testCreatorID := int64(102456)
	key := keys.KeyRoomMedalPointMulti.Format(testCreatorID)
	require.NoError(service.LRURedis.Del(key).Err())

	p := activityPointMulti(testCreatorID)
	assert.Nil(p)

	multi := liverecommendedelements.PointMulti{
		PointMultiAdd:     3,
		ThresholdMultiAdd: 4,
	}
	now := goutil.TimeNow()
	require.NoError(liverecommendedelements.BatchAddRoomMedalPointMulti([]int64{0}, now, now.Add(5*time.Second), multi))
	require.NoError(service.LRURedis.Del(key).Err())
	p = activityPointMulti(testCreatorID)
	require.NotNil(p)
	assert.EqualValues(3, p.PointMultiAdd)
	assert.EqualValues(4, p.ThresholdMultiAdd)

	multi = liverecommendedelements.PointMulti{
		PointMultiAdd:     1,
		ThresholdMultiAdd: 2,
	}
	require.NoError(liverecommendedelements.BatchAddRoomMedalPointMulti([]int64{testCreatorID}, now, now.Add(5*time.Second), multi))
	require.NoError(service.LRURedis.Del(key).Err())
	p = activityPointMulti(testCreatorID)
	require.NotNil(p)
	assert.EqualValues(1, p.PointMultiAdd)
	assert.EqualValues(2, p.ThresholdMultiAdd)
}

func TestFindMedalPointMultiple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64
	goutil.SetTimeNow(func() time.Time {
		// 周四
		return time.Date(2021, 4, 8, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := keys.KeyRoomMedalPointMulti.Format(testUserID)
	require.NoError(service.LRURedis.Del(key).Err())
	pointMulti, thresholdMulti := FindMedalPointMultiple(testUserID, false)
	assert.Equal(int64(1), pointMulti)
	assert.Equal(int64(1), thresholdMulti)

	pointMulti, thresholdMulti = FindMedalPointMultiple(testUserID, true)
	assert.Equal(int64(2), pointMulti)
	assert.Equal(int64(2), thresholdMulti)

	goutil.SetTimeNow(func() time.Time {
		// 周六
		return time.Date(2021, 4, 10, 0, 0, 0, 0, time.Local)
	})
	require.NoError(service.LRURedis.Del(key).Err())
	pointMulti, thresholdMulti = FindMedalPointMultiple(testUserID, true)
	assert.Equal(int64(2), pointMulti)
	assert.Equal(int64(3), thresholdMulti)

	require.NoError(service.DB.Table(liverecommendedelements.TableName()).
		Delete("", "element_type = ?", liverecommendedelements.ElementMultiMedalPoint).Error)
	require.NoError(service.LRURedis.Del(key).Err())
	pointMulti, thresholdMulti = FindMedalPointMultiple(testUserID, false)
	assert.Equal(int64(1), pointMulti)
	assert.Equal(int64(1), thresholdMulti)

	multi := liverecommendedelements.PointMulti{PointMultiAdd: 1, ThresholdMultiAdd: 2}
	now := goutil.TimeNow()
	require.NoError(liverecommendedelements.BatchAddRoomMedalPointMulti([]int64{testUserID}, now, now.Add(5*time.Second), multi))
	require.NoError(service.LRURedis.Del(key).Err())
	pointMulti, thresholdMulti = FindMedalPointMultiple(testUserID, false)
	assert.Equal(int64(2), pointMulti)
	assert.Equal(int64(3), thresholdMulti)
}

func TestUpdateUserShareAndCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	roomID := int64(22489473)
	var r struct {
		ID primitive.ObjectID `bson:"_id"`
	} // WORKAROUND: 防止循环引用
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := service.MongoDB.Collection("rooms").
		FindOne(ctx, bson.M{"room_id": roomID}).Decode(&r)
	require.NoError(err)
	roomOID := r.ID
	userID := int64(12)
	now := goutil.TimeNow()
	// 无法在 live_user 中找到数据的时候
	liveUsersCol := service.MongoDB.Collection("live_users")
	userIDNotExist := int64(22334433)
	_, err = liveUsersCol.DeleteOne(ctx, bson.M{"user_id": userIDNotExist, "room_id": roomID})
	require.NoError(err)
	newShare, err := UpdateUserShareAndCheck(roomOID, roomID, userIDNotExist)
	require.True(newShare)
	require.NoError(err)
	// 今日首次分享
	_, err = liveUsersCol.UpdateOne(ctx, bson.M{"user_id": userID, "room_id": roomID}, bson.M{"$set": bson.M{
		"t_status_share": ShareStatusPending, "t_time_share": time.Unix(0, 0)}})
	require.NoError(err)
	newShare, err = UpdateUserShareAndCheck(roomOID, roomID, userID)
	require.NoError(err)
	require.True(newShare)
	l2, err := FindLiveUserShare(userID, roomOID)
	require.NoError(err)
	assert.Equal(ShareStatusShared, l2.StatusShare)
	assert.True(l2.TimeShare.YearDay() == now.YearDay())
	// 今日第二次分享
	newShare, err = UpdateUserShareAndCheck(roomOID, roomID, userID)
	require.NoError(err)
	require.False(newShare)
	l3, err := FindLiveUserShare(userID, roomOID)
	require.NoError(err)
	assert.Equal(l2.TimeShare, l3.TimeShare)
}

func TestDelUsersTitlesCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs := []int64{111, 222}
	cacheKey := []string{
		keys.KeyUsersTitles1.Format(userIDs[0]),
		keys.KeyUsersTitles1.Format(userIDs[1]),
	}

	pipe := service.Redis.Pipeline()
	pipe.Set(cacheKey[0], "[]", time.Minute)
	pipe.Set(cacheKey[1], "[]", time.Minute)
	_, err := pipe.Exec()
	require.NoError(err)

	assert.PanicsWithValue("userIDs is empty", func() { DelUsersTitlesCache() })

	DelUsersTitlesCache(userIDs...)
	val, err := service.Redis.Exists(cacheKey...).Result()
	require.NoError(err)
	assert.Zero(val)
}

func TestParseLevel(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, ParseLevel(-1))
	assert.Equal(1, ParseLevel(0))
	assert.Equal(1, ParseLevel(1))
	assert.Equal(1, ParseLevel(5))
	assert.Equal(21, ParseLevel(800000))
	assert.Equal(40, ParseLevel(16000000))
}

func TestGetArrayByConfig(t *testing.T) {
	assert := assert.New(t)

	levelStartAfter := []int64{0, 200, 300, 500, 700, 1000, 1500, 1600, 1800, 2000,
		5500, 10000, 15000, 25000, 35000, 45000, 60000, 120000, 300000, 600000,
		780000, 980000, 1220000, 1480000, 1780000, 2130000, 2530000, 2980000, 3480000, 4050000,
		4700000, 5400000, 6150000, 7000000, 7900000, 9000000, 10200000, 11500000, 13000000, 15000000}
	levelThresholdAfter := []int64{
		500, 500, 500, 500, 500, 500, 1000, 1000, 1000, 1000,
		1500, 1500, 1500, 1500, 1500, 2000, 2000, 5000, 8000, 8000,
		10000, 12000, 14000, 16000, 18000, 20000, 22000, 24000, 26000, 28000,
		30000, 32000, 34000, 36000, 38000, 40000, 42000, 44000, 46000, 50000,
	}
	catFoodCountAfter := []int64{
		10, 10, 10, 10, 10, 20, 20, 20, 20, 20,
		40, 40, 40, 40, 40, 50, 50, 50, 50, 60,
		100, 100, 100, 100, 100, 150, 150, 150, 150, 150,
		200, 200, 200, 200, 200, 250, 250, 250, 250, 300,
	}
	pointReduceBefore := [20]float64{25, 25, 25, 25, 25, 25, 50, 50, 50, 50,
		75, 75, 75, 75, 75, 100, 100, 125, 240, 300,
	}
	dailyReductionBefore := make([]DailyReduction, 20)
	for i, v := range pointReduceBefore {
		dailyReductionBefore[i] = DailyReduction{v, PointReduce}
	}
	dailyReductionAfter := make([]DailyReduction, 0, 20)
	for _, v := range pointReduceBefore {
		dailyReductionAfter = append(dailyReductionAfter, DailyReduction{v * 2, PointReduce})
	}
	for i := 0; i < 20; i++ {
		dailyReductionAfter = append(dailyReductionAfter, DailyReduction{0.025, PointProportionReduce})
	}
	// 没有配置，认为超粉已上线
	config.Conf.AB = make(map[string]interface{})
	assert.Equal(int64(30), UserMaxMedalCount())
	assert.Equal(levelStartAfter, levelStart())
	assert.Equal(levelThresholdAfter, levelThreshold())
	assert.Equal(catFoodCountAfter, catFoodCount())
	assert.Equal(dailyReductionAfter, PointDailyReduction())
	assert.Equal(int64(30), UserMaxMedalCount())
	assert.Equal(levelStartAfter, levelStart())
	assert.Equal(levelThresholdAfter, levelThreshold())
	assert.Equal(catFoodCountAfter, catFoodCount())
	assert.Equal(dailyReductionAfter, PointDailyReduction())
}

func TestCatFoodAddCount(t *testing.T) {
	assert := assert.New(t)

	expected := [9]int64{10, 20, 40, 50, 100, 150, 200, 250, 300}
	levelRange := [9][2]int{{0, 5}, {6, 10}, {11, 15}, {16, 19}, {21, 25}, {26, 30}, {31, 35}, {36, 39}, {40, 41}}
	for i := range expected {
		assert.Equal(expected[i], CatFoodAddCount(levelRange[i][0]), i)
		assert.Equal(expected[i], CatFoodAddCount(levelRange[i][1]), i)
	}
	assert.Equal(int64(60), CatFoodAddCount(20))
}

func TestGetDailyReduction(t *testing.T) {
	assert := assert.New(t)

	level := 0
	assert.Equal(0, GetDailyReduction(10, level))
	level = 41
	assert.Equal(500000, GetDailyReduction(20000000, level))
	level = 40
	assert.Equal(350000, GetDailyReduction(14000000, level))
}

func TestReductionPoint(t *testing.T) {
	assert := assert.New(t)

	r := DailyReduction{
		Value: 50,
		Type:  PointReduce,
	}
	assert.Equal(50, r.reductionPoint(100))
	r = DailyReduction{
		Value: 0.0025,
		Type:  PointProportionReduce,
	}
	assert.Equal(0, r.reductionPoint(100))
	assert.Equal(2, r.reductionPoint(1000))
	r.Type = 3
	assert.Equal(0, r.reductionPoint(1000))
}

func TestFindSuperFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(202104260935)
	testRoomID := int64(202104260940)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"user_id": testUserID, "room_id": testRoomID}
	lm := LiveMedal{
		Simple: Simple{
			RoomID:    testRoomID,
			CreatorID: testRoomID,
			UserID:    testUserID,
			Point:     1,
			Status:    StatusOwned,
			Mini: Mini{
				Name:     "Test",
				SuperFan: &SuperFan{ExpireTime: goutil.TimeNow().Add(time.Minute).Unix()},
			},
		},
	}
	_, err := Collection().UpdateOne(ctx, filter, bson.M{"$set": lm},
		options.Update().SetUpsert(true))
	require.NoError(err)

	t.Run("IsRoomSuperFan", func(t *testing.T) {
		ok, err := IsRoomSuperFan(testRoomID, testUserID)
		require.NoError(err)
		assert.True(ok)

		ok, err = IsRoomSuperFan(-99999, testUserID)
		require.NoError(err)
		assert.False(ok)
	})

	t.Run("FindRoomSuperFanMap", func(t *testing.T) {
		noMedalUserID := int64(9999999)
		m, err := FindRoomSuperFanMap(testRoomID, []int64{testUserID, noMedalUserID})
		require.NoError(err)
		_, ok := m[testUserID]
		assert.True(ok)

		_, ok = m[noMedalUserID]
		assert.False(ok)
	})
}

func TestSetSuperFanExpired(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(9074509)
	testUserIDs := []int64{12123, 471150}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx,
		bson.M{"room_id": testRoomID, "user_id": bson.M{"$in": testUserIDs}})
	require.NoError(err)

	s := Simple{RoomID: testRoomID, UserID: testUserIDs[0]}
	s.SuperFan = &SuperFan{
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	}

	s2 := new(Simple)
	*s2 = s
	s2.UserID = testUserIDs[1]
	_, err = Collection().InsertMany(ctx, []interface{}{s, s2})
	require.NoError(err)

	err = SetSuperFanExpired(testRoomID, testUserIDs)
	require.NoError(err)
	assert.False(IsRoomSuperFan(testRoomID, testUserIDs[0]))
	assert.False(IsRoomSuperFan(testRoomID, testUserIDs[1]))
}

func TestFindUserMedalsLevelGte(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(12)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testRoomID := int64(22489473)
	s := &Simple{RoomID: testRoomID, CreatorID: 10, UserID: userID, Point: 700, Status: StatusOwned}
	err := Collection().FindOneAndUpdate(ctx, bson.M{"creator_id": 10, "user_id": userID},
		bson.M{"$set": s}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(err)

	// 测试获取等级 ≥ 40 的粉丝勋章信息
	medals, err := FindUserMedalsLevelGte(userID, 40)
	require.NoError(err)
	require.NotEmpty(medals)
	for _, m := range medals {
		assert.True(m.Level >= 40)
	}

	// 测试获取等级 ≥ 5 的粉丝勋章信息
	medals, err = FindUserMedalsLevelGte(userID, 5)
	require.NoError(err)
	require.NotEmpty(medals)
	for _, m := range medals {
		assert.True(m.Level >= 5)
	}
}

func TestSimple_BuildSuperFan(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	s := Simple{
		Mini: Mini{
			SuperFan: &SuperFan{
				ExpireTime: now.AddDate(0, 0, 5).Unix(),
			},
		},
	}
	// 测试超粉未过期，返回天数和开通时间
	testRegisterTime := now.AddDate(0, 0, -3).Unix()
	s.BuildSuperFan(testRegisterTime)
	require.NotNil(s.SuperFan)
	assert.EqualValues(4, s.SuperFan.Days)
	assert.Equal(testRegisterTime, s.SuperFan.RegisterTime)
}

func TestHasUserOwnedMedalByCreatorID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID := int64(9074509)
	testCreatorID := int64(9074511)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": testUserID, "creator_id": testCreatorID}
	_, err := Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	_, err = Collection().InsertOne(ctx, LiveMedal{
		Simple: Simple{
			CreatorID: testCreatorID,
			UserID:    testUserID,
			Status:    StatusOwned,
		},
	})
	require.NoError(err)

	exists, err := HasUserOwnedMedalByCreatorID(testUserID, testCreatorID)
	require.NoError(err)
	assert.True(exists)

	exists, err = HasUserOwnedMedalByCreatorID(testUserID, 9)
	require.NoError(err)
	assert.False(exists)
}
