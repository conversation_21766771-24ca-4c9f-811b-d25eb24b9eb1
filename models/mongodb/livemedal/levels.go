package livemedal

// 文档：https://info.missevan.com/pages/viewpage.action?pageId=28282115

// RevenueThreshold 主播收到的钻石超过该值，才可以申请勋章
const RevenueThreshold = 30000

// superFanUsernameColor 超粉昵称颜色
const superFanUsernameColor = "#FF8686"

// UserMaxMedalCount 获得普通用户勋章数限制，因为超粉活动开始前后勋章数不一样，所以需要判断当前时间
func UserMaxMedalCount() int64 {
	// userMaxMedalCount 用户勋章数限制
	const userMaxMedalCount = 30
	// 默认返回超粉上线后的数量
	return userMaxMedalCount
}

func levelStart() []int64 {
	// levelStart 各等级最低亲密度（包含），每行 10 个
	levelStart := []int64{0, 200, 300, 500, 700, 1000, 1500, 1600, 1800, 2000,
		5500, 10000, 15000, 25000, 35000, 45000, 60000, 120000, 300000, 600000,
		780000, 980000, 1220000, 1480000, 1780000, 2130000, 2530000, 2980000, 3480000, 4050000,
		4700000, 5400000, 6150000, 7000000, 7900000, 9000000, 10200000, 11500000, 13000000, 15000000}
	return levelStart
}

func levelThreshold() []int64 {
	// levelThreshold 各个等级每日上限，每行 10 个
	levelThreshold := []int64{
		500, 500, 500, 500, 500, 500, 1000, 1000, 1000, 1000,
		1500, 1500, 1500, 1500, 1500, 2000, 2000, 5000, 8000, 8000,
		10000, 12000, 14000, 16000, 18000, 20000, 22000, 24000, 26000, 28000,
		30000, 32000, 34000, 36000, 38000, 40000, 42000, 44000, 46000, 50000,
	}
	return levelThreshold
}

// LevelThreshold fix data
func LevelThreshold() []int64 {
	return levelThreshold()
}

// catFoodCount 获得每日猫粮数量
func catFoodCount() []int64 {
	// catFoodCount 每个等级每日获取猫粮数量，每行 10 个
	catFoodCount := []int64{10, 10, 10, 10, 10, 20, 20, 20, 20, 20,
		40, 40, 40, 40, 40, 50, 50, 50, 50, 60, // 目前只有 20 级这一个等级的每日猫粮数量为 60
		100, 100, 100, 100, 100, 150, 150, 150, 150, 150,
		200, 200, 200, 200, 200, 250, 250, 250, 250, 300,
	}
	return catFoodCount
}

// CatFoodAddCount 获取猫粮数目
func CatFoodAddCount(level int) int64 {
	catFoodCount := catFoodCount()
	if level > len(catFoodCount)-1 {
		return catFoodCount[len(catFoodCount)-1]
	}
	if level < 1 {
		return catFoodCount[0]
	}
	return catFoodCount[level-1]
}

// UserMedalLevelLimit 用户勋章等级限制，因为超粉活动开始前后勋章等级上限不一样，所以需要判断当前时间
func UserMedalLevelLimit() int {
	const userMaxMedalLevel = 40
	return userMaxMedalLevel
}

const (
	// PointReduce 亲密度直接衰减某个值
	PointReduce = iota
	// PointProportionReduce 亲密度按某个比例衰减
	PointProportionReduce
)

// DailyReduction 每日衰减得类型和该类型对应的值
type DailyReduction struct {
	Value float64
	Type  int
}

// PointDailyReduction 获得亲密度的每日衰减配置
func PointDailyReduction() []DailyReduction {
	// 1-20 级亲密度衰减的值
	pointReduce := [20]float64{50, 50, 50, 50, 50, 50, 100, 100, 100, 100,
		150, 150, 150, 150, 150, 200, 200, 250, 480, 600,
	}
	dailyReduction := make([]DailyReduction, 0, 40)
	for _, v := range pointReduce {
		dailyReduction = append(dailyReduction, DailyReduction{v, PointReduce})
	}
	// 21-40 级亲密度衰减的比例
	for i := 0; i < 20; i++ {
		dailyReduction = append(dailyReduction, DailyReduction{0.025, PointProportionReduce})
	}
	return dailyReduction
}

func (r DailyReduction) reductionPoint(point int64) int {
	if r.Type == PointReduce {
		return int(r.Value)
	}
	if r.Type == PointProportionReduce {
		// 小数时向下取整
		return int(float64(point) * r.Value)
	}
	return 0
}

// SuperFanUsernameColor 超粉彩色昵称颜色
func SuperFanUsernameColor() string {
	return superFanUsernameColor
}
