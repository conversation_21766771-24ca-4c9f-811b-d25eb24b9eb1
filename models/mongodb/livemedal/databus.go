package livemedal

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 粉丝牌变动类型
const (
	ChangeTypeNew    = iota + 1 // 新增粉丝牌
	ChangeTypeRemove            // 流失粉丝牌
)

// 粉丝牌变动原因
const (
	ChangeReasonLoss         = iota // 衰减流失
	ChangeReasonOneClick            // 一键赠送礼物
	ChangeReasonAccumulation        // 累计消费
	ChangeReasonAdmin               // 后台操作
	ChangeReasonBuySuperFan         // 购买超粉
)

// 粉丝牌变动的由来
const (
	ChangeSourceDecline     = iota // 常规衰减
	ChangeSourceRemove             // 用户删除勋章
	ChangeSourceOneClick           // 一键送礼
	ChangeSourceGift               // 赠送礼物
	ChangeSourceGashapon           // 扭蛋
	ChangeSourceQuestion           // 回答问题
	ChangeSourceAdmin              // 后台补发亲密度
	ChangeSourceBuySuperFan        // 购买超粉
	ChangeSourceBuyDanmaku         // 购买付费弹幕
	ChangeSourceBuyLuckyBox        // 购买宝盒
	ChangeSourceTrialCard          // 粉丝勋章兑换卡
)

type liveMedalChangeMessage struct {
	UserID       int64 `json:"user_id"`
	RoomID       int64 `json:"room_id"`
	ChangeType   int   `json:"change_type"`
	ChangeReason int   `json:"change_reason"`
	ChangeSource int   `json:"change_source"`
	CreateTime   int64 `json:"create_time"`
}

// SendLiveMedalChange 发送用户直播粉丝牌的变化
func SendLiveMedalChange(userID, roomID int64, changeType, changeReason, source int) {
	message := liveMedalChangeMessage{
		UserID:       userID,
		RoomID:       roomID,
		ChangeType:   changeType,
		ChangeReason: changeReason,
		ChangeSource: source,
		CreateTime:   util.TimeNow().Unix(),
	}
	key := keys.KeyLiveMedalChange1.Format(userID)
	if err := service.DatabusSend(key, message); err != nil {
		logger.WithFields(logger.Fields{
			"user_id": userID,
			"room_id": roomID,
		}).Errorf("databus send error: %v", err)
		return
	}
}

// MedalChangeReason 获取粉丝牌变动的原因
func MedalChangeReason(source int) int {
	switch source {
	case ChangeSourceDecline, ChangeSourceRemove:
		return ChangeReasonLoss
	case ChangeSourceOneClick:
		return ChangeReasonOneClick
	case ChangeSourceAdmin:
		return ChangeReasonAdmin
	case ChangeSourceBuySuperFan:
		return ChangeReasonBuySuperFan
	default:
		return ChangeReasonAccumulation
	}
}
