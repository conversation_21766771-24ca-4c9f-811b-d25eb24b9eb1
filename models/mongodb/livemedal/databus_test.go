package livemedal

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestSendLiveMedalChange(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(114514)
	roomID := int64(1919)
	key := keys.KeyLiveMedalChange1.Format(userID)
	require.NoError(service.Redis.Del(key).Err())
	service.DatabusPub.ClearDebugPubMsgs()
	defer service.DatabusPub.ClearDebugPubMsgs()
	SendLiveMedalChange(userID, roomID, ChangeTypeRemove, ChangeReasonLoss, ChangeSourceDecline)

	msgs := service.DatabusPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(key, m.Key)

	var message liveMedalChangeMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.Equal(userID, message.UserID)
	assert.Equal(roomID, message.RoomID)
	assert.Equal(ChangeTypeRemove, message.ChangeType)
	assert.Equal(ChangeReasonLoss, message.ChangeReason)
	assert.Equal(ChangeSourceDecline, message.ChangeSource)
}

func TestMedalChangeReason(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(ChangeReasonAccumulation, MedalChangeReason(ChangeSourceGift))
	assert.Equal(ChangeReasonOneClick, MedalChangeReason(ChangeSourceOneClick))
	assert.Equal(ChangeReasonAdmin, MedalChangeReason(ChangeSourceAdmin))
	assert.Equal(ChangeReasonBuySuperFan, MedalChangeReason(ChangeSourceBuySuperFan))
	assert.Equal(ChangeReasonLoss, MedalChangeReason(ChangeSourceRemove))
}
