package livemedal

import (
	"sort"

	"github.com/MiaoSiLa/live-service/service/storage"
)

// FanPrivilege 粉丝和超粉特权
type FanPrivilege struct {
	IconURL     string `json:"iconurl"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Sort        int    `json:"-"`
}

// 粉丝勋章特权
var normalPrivilege = []*FanPrivilege{
	{
		IconURL:     "oss://live/superfans/privilege/medal.png",
		Name:        "粉丝勋章",
		Description: "粉丝专属勋章",
	},
	{
		IconURL:     "oss://live/superfans/privilege/catfood.png",
		Name:        "每日猫粮",
		Description: "每日可得猫粮",
	},
	{
		IconURL:     "oss://live/superfans/privilege/gift.png",
		Name:        "粉丝礼物",
		Description: "解锁粉丝等级礼物",
	},
}

// 超粉特权，默认顺序对应普通粉丝特权，sort 表示重要性排序，越小在越前面
var superPrivilege = []*FanPrivilege{
	{
		IconURL:     "oss://live/superfans/privilege/sf-medal.png",
		Name:        "超级粉丝勋章",
		Description: "粉丝勋章增加尊贵标识",
		Sort:        1,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-catcan.png",
		Name:        "猫罐头",
		Description: "每日可得猫粮，更有额外猫罐头",
		Sort:        6,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-gift.png",
		Name:        "超粉礼物",
		Description: "超粉专属礼物",
		Sort:        5,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-pinkname.png",
		Name:        "粉名展示",
		Description: "开通超粉的房间内显示粉色昵称",
		Sort:        2,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-pointdouble.png",
		Name:        "亲密度双倍",
		Description: "给主播打赏可得双倍亲密度",
		Sort:        3,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-pointlimit.png",
		Name:        "亲密度多倍上限",
		Description: "周六 3 倍，平时 2 倍上限",
		Sort:        4,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-extravolumes.png",
		Name:        "额外勋章容量",
		Description: "超级粉丝勋章不占用粉丝勋章数量上限",
		Sort:        7,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-extendtime.png",
		Name:        "延长猫粮有效期",
		Description: "猫粮有效期延长至当周周日 24 点",
		Sort:        8,
	},
	{
		IconURL:     "oss://live/superfans/privilege/sf-protect.png",
		Name:        "粉丝身份保护",
		Description: "超粉身份期间，不会失去粉丝勋章",
		Sort:        9,
	},
}

// NormalPrivilege 粉丝特权
func NormalPrivilege() []*FanPrivilege {
	dst := make([]*FanPrivilege, len(normalPrivilege))
	copy(dst, normalPrivilege)

	for i := range dst {
		dst[i].IconURL = storage.ParseSchemeURL(dst[i].IconURL)
	}
	return dst
}

// SuperPrivilege 按照优先级排序后的超粉特权
func SuperPrivilege(sorted bool) []*FanPrivilege {
	dst := make([]*FanPrivilege, len(superPrivilege))
	copy(dst, superPrivilege)
	for i := range dst {
		dst[i].IconURL = storage.ParseSchemeURL(dst[i].IconURL)
	}
	if !sorted {
		return dst
	}
	sort.Slice(dst, func(i, j int) bool {
		return dst[i].Sort < dst[j].Sort
	})
	return dst
}
