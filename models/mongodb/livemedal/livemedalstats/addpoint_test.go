package livemedalstats

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAddFreePointParam_AddFreePoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22489473)
	roomIDHex := "5ab9d5d9bc9b53298ce5a5a5"
	roomOID, _ := primitive.ObjectIDFromHex(roomIDHex)
	userID := int64(12345678)
	creatorID := int64(10)
	now := goutil.TimeNow()
	// t_free_updated_time normal
	insert := bson.M{
		"room_id":             roomID,
		"_room_id":            roomOID,
		"creator_id":          creatorID,
		"name":                "小恶魔",
		"user_id":             userID,
		"point":               990,
		"t_point":             500,
		"status":              1,
		"t_free_point":        nil,
		"t_reduce_point":      -25,
		"t_free_updated_time": now.Add(-24 * time.Hour),
		"t_updated_time":      now.Add(-48 * time.Hour),
		"super_fan": bson.M{
			"expire_time": goutil.TimeNow().Add(-time.Minute).Unix(),
		},
	}
	filter := bson.M{"room_id": roomID, "user_id": userID}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemedal.Collection()
	_, err := col.UpdateOne(ctx, filter, bson.M{"$set": insert}, options.Update().SetUpsert(true))
	require.NoError(err)

	addParam := &AddFreePointParam{
		RoomID:    roomID,
		CreatorID: creatorID,
		UserID:    userID,
		PointAdd:  10,
		Scene:     livemedalpointlog.SceneTypeFreeGift,
	}
	medalUpdatedInfo, err := addParam.AddFreePoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(1025), medalUpdatedInfo.After.Point)
	assert.Equal(int64(1015), medalUpdatedInfo.After.TPoint) // 增加了 t_reduce_point: -25, 所以 990 - (-25) = 1015
	assert.Equal("小恶魔", medalUpdatedInfo.After.Name)
	assert.Equal(int64(10), *medalUpdatedInfo.After.TFreePoint)
	assert.True(*medalUpdatedInfo.After.TFreeUpdatedTime == medalUpdatedInfo.After.TUpdatedTime && medalUpdatedInfo.After.TUpdatedTime.After(now))
	// t_free_updated_time nil
	testUserID := int64(87654321)
	insert["t_free_updated_time"] = nil
	insert["user_id"] = testUserID
	filter = bson.M{"room_id": roomID, "user_id": testUserID}
	_, err = col.UpdateOne(ctx, filter, bson.M{"$set": insert}, options.Update().SetUpsert(true))
	require.NoError(err)

	addParam = &AddFreePointParam{
		RoomID:    roomID,
		CreatorID: creatorID,
		UserID:    testUserID,
		PointAdd:  10,
		Scene:     livemedalpointlog.SceneTypeFreeGift,
	}
	medalUpdatedInfo, err = addParam.AddFreePoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	assert.Equal(int64(1025), medalUpdatedInfo.After.Point)
	assert.Equal(int64(1015), medalUpdatedInfo.After.TPoint)
	assert.Equal("小恶魔", medalUpdatedInfo.After.Name)
	assert.Equal(int64(10), *medalUpdatedInfo.After.TFreePoint)
	assert.True(*medalUpdatedInfo.After.TFreeUpdatedTime == medalUpdatedInfo.After.TUpdatedTime && medalUpdatedInfo.After.TUpdatedTime.After(now))

	// 测试超粉送礼亲密度双倍
	_, err = col.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"super_fan": bson.M{
			"expire_time": goutil.TimeNow().Add(time.Minute).Unix(),
		},
	}}, options.Update().SetUpsert(true))
	require.NoError(err)
	param := &AddFreePointParam{
		RoomID:    roomID,
		CreatorID: creatorID,
		UserID:    testUserID,
		PointAdd:  10,
		Scene:     livemedalpointlog.SceneTypeFreeGift,
	}
	medalUpdatedInfo, err = param.AddFreePoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	assert.EqualValues(1025, medalUpdatedInfo.Before.Point)
	assert.EqualValues(1045, medalUpdatedInfo.After.Point)

	// 测试不是送免费礼物场景，亲密度不双倍
	addParam = &AddFreePointParam{
		RoomID:    roomID,
		CreatorID: creatorID,
		UserID:    testUserID,
		PointAdd:  10,
		Scene:     livemedalpointlog.SceneTypeTaskOnline,
	}
	medalUpdatedInfo, err = addParam.AddFreePoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	assert.EqualValues(1045, medalUpdatedInfo.Before.Point)
	assert.EqualValues(1055, medalUpdatedInfo.After.Point)
}

func TestAddFreePointParam_savePointChangeLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &AddFreePointParam{
		RoomID:                1,
		CreatorID:             2,
		UserID:                3,
		PointAdd:              10,
		actualAddPoint:        10,
		pointMulti:            1,
		thresholdMulti:        1,
		actualTLevelThreshold: 500,
	}
	require.NoError(livemedalpointlog.DB().Delete(livemedalpointlog.LiveMedalPointChangeLog{}, "room_id = ?", param.RoomID).Error)
	param.savePointChangeLog(&livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Point: 100,
		},
	}, nil)

	var logs livemedalpointlog.LiveMedalPointChangeLog
	require.NoError(livemedalpointlog.DB().First(&logs, "room_id = ?", param.RoomID).Error)
	assert.EqualValues(param.UserID, logs.UserID)
	assert.EqualValues(param.actualTLevelThreshold, logs.TLevelThreshold)
	assert.EqualValues(100, logs.AfterPoint)
	assert.EqualValues(100, logs.BeforePoint)
}

func TestOwnedAndLimitCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(20210506)
	testRoomID := int64(20210413)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": testUserID, "room_id": testRoomID}
	_, err := livemedal.Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	param := AddPointParam{
		UserID: testUserID,
	}
	require.NoError(param.ownedAndLimitCount())
	assert.Zero(param.ownedNormalMedalCount)
	assert.Zero(param.ownedAllMedalCount)
	assert.Equal(int64(30), param.maxMedalNum)

	param.UV = &vip.UserVip{
		UserID:     testUserID,
		Level:      2,
		ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		Info: &vip.Info{
			Level:    2,
			MedalNum: 40,
		},
	}
	require.NoError(param.ownedAndLimitCount())
	assert.Zero(param.ownedNormalMedalCount)
	assert.Equal(int64(40), param.maxMedalNum)

	_, err = livemedal.Collection().InsertOne(ctx, livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID:    testRoomID,
			CreatorID: testRoomID,
			UserID:    testUserID,
			Point:     60,
			Status:    livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Name:     "Test",
				SuperFan: &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(-time.Minute).Unix()},
			},
		},
	})
	require.NoError(err)
	param = AddPointParam{
		UserID: testUserID,
	}
	require.NoError(param.ownedAndLimitCount())
	assert.NotZero(param.ownedNormalMedalCount)
	assert.NotZero(param.ownedAllMedalCount)

	_, err = livemedal.Collection().UpdateOne(ctx, filter, bson.M{"$set": bson.M{"super_fan.expire_time": goutil.TimeNow().Add(time.Minute).Unix()}})
	require.NoError(err)
	greaterParam := AddPointParam{
		UserID: testUserID,
	}
	require.NoError(greaterParam.ownedAndLimitCount())
	assert.Greater(param.ownedNormalMedalCount, greaterParam.ownedNormalMedalCount)
	assert.Equal(param.ownedAllMedalCount, greaterParam.ownedAllMedalCount)
}

func TestAddPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22489473)
	userID := int64(3457111)
	roomIDHex := "5ab9d5d9bc9b53298ce5a5a5"
	roomOID, _ := primitive.ObjectIDFromHex(roomIDHex)
	param := AddPointParam{
		RoomOID:   roomOID,
		RoomID:    roomID,
		CreatorID: 10,
		MedalName: "测试",
		UserID:    userID,
		Type:      livemedal.TypeGiftAddMedalPoint,
		PointAdd:  10,
	}
	require.NoError(service.LRURedis.Set(keys.KeyRoomMedalPointMulti.Format(userID), "[]", 5*time.Second).Err())

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": userID}
	_, err := livemedal.Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	// 新房间增加 10 亲密度
	medalUpdatedInfo, err := param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(livemedal.StatusPending, medalUpdatedInfo.After.Status)
	assert.Equal(param.PointAdd, medalUpdatedInfo.After.Point)

	// 房间增加 10 亲密度，不够开通勋章
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(livemedal.StatusPending, medalUpdatedInfo.After.Status)
	assert.Equal(2*param.PointAdd, medalUpdatedInfo.After.Point)

	// 房间增加 40 亲密度，开通第一个勋章
	param.PointAdd = 40
	param.From = livemedal.FromOneClickObtain
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(livemedal.StatusShow, medalUpdatedInfo.After.Status)
	assert.Equal(int64(60), medalUpdatedInfo.After.Point)
	assert.Zero(medalUpdatedInfo.After.TPoint)
	assert.Equal(1, medalUpdatedInfo.After.From)

	// 房间增加 40 亲密度, 当日有 25 衰减
	require.NoError(livemedal.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": userID, "room_id": roomID},
		bson.M{"$set": bson.M{"t_reduce_point": -25}}).Err())
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(livemedal.StatusShow, medalUpdatedInfo.After.Status)
	assert.Equal(int64(125), medalUpdatedInfo.After.Point)
	assert.Equal(int64(25), medalUpdatedInfo.After.TPoint)

	// 周四超粉亲密度翻倍，上限翻倍
	testDate := time.Date(2021, 4, 8, 0, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return testDate
	})
	defer goutil.SetTimeNow(nil)
	require.NoError(livemedal.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": userID, "room_id": roomID},
		bson.M{"$set": bson.M{"super_fan": livemedal.SuperFan{ExpireTime: testDate.Add(time.Hour).Unix()}}}).Err())
	param.PointAdd = 250
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(625), medalUpdatedInfo.After.Point)
	// 超粉亲密度翻倍达到限额, 上限为 500 * 2
	param.PointAdd = 500
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(1025), medalUpdatedInfo.After.Point)

	goutil.SetTimeNow(func() time.Time {
		return testDate.Add(2 * 24 * time.Hour)
	})
	// 周六超粉过期，达到上限
	param.PointAdd = 600
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(1525), medalUpdatedInfo.After.Point)
	// 周六超粉生效，亲密度双倍，上限三倍
	require.NoError(livemedal.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": userID, "room_id": roomID},
		bson.M{"$set": bson.M{"super_fan": livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()}}}).Err())
	param.PointAdd = 600
	medalUpdatedInfo, err = param.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(2525), medalUpdatedInfo.After.Point)
}

func TestFinalMedalCheckLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livemedal.Collection()
	now := goutil.TimeNow().Unix()
	s := livemedal.Simple{RoomID: now, CreatorID: now, UserID: now, Status: livemedal.StatusPending, Point: 50}
	s.Name = "testFinalMedalCheckLimit"
	_, err := collection.InsertOne(ctx, s)
	require.NoError(err)
	defer func() {
		_, _ = collection.DeleteMany(ctx, bson.M{"user_id": now, "status": livemedal.StatusPending})
	}()

	param := AddPointParam{
		UserID:                now,
		ownedNormalMedalCount: 1,
		maxMedalNum:           2,
	}
	require.NoError(param.finalMedalCheckLimit(50))
	r, err := collection.CountDocuments(ctx, s)
	require.NoError(err)
	assert.Equal(int64(1), r)

	require.NoError(param.finalMedalCheckLimit(60))
	r, err = collection.CountDocuments(ctx, s)
	require.NoError(err)
	assert.Zero(r)
}

func TestAddOnlineTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22489473)
	roomIDHex := "5ab9d5d9bc9b53298ce5a5a5"
	roomOID, _ := primitive.ObjectIDFromHex(roomIDHex)
	userID := int64(123456)
	creatorID := int64(10)
	now := goutil.TimeNow()
	insert := bson.M{
		"room_id":             roomID,
		"_room_id":            roomOID,
		"creator_id":          creatorID,
		"name":                "小恶魔",
		"user_id":             userID,
		"point":               1000,
		"status":              1,
		"t_free_point":        nil,
		"t_free_updated_time": now.Add(-24 * time.Hour),
		"t_updated_time":      now.Add(-24 * time.Hour),
	}
	filter := bson.M{"room_id": roomID, "user_id": userID}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemedal.Collection()
	_, err := col.UpdateOne(ctx, filter, bson.M{"$set": insert}, options.Update().SetUpsert(true))
	require.NoError(err)
	luCol := service.MongoDB.Collection("live_users")
	_, err = luCol.DeleteMany(ctx, filter)
	require.NoError(err)
	// 第一次加
	todayBegin := goutil.BeginningOfDay(goutil.TimeNow())
	p := AddOnlineTimeParam{
		UserID:       userID,
		RoomID:       roomID,
		RoomOID:      roomOID,
		CreatorID:    creatorID,
		AccessTime:   todayBegin.Add(6 * time.Minute),
		LastCallTime: todayBegin.Add(-30 * time.Minute),
	}
	medalUpdatedInfo, err := p.AddOnlineTime()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	var lu struct {
		AcqOnline      int64 `bson:"acq_online"`   // 单位：毫秒
		TodayAcqOnline int64 `bson:"t_acq_online"` // 单位：毫秒
	}
	require.NoError(luCol.FindOne(ctx, filter).Decode(&lu))
	assert.Equal(int64(360000), lu.TodayAcqOnline)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(1005), medalUpdatedInfo.After.Point)
	assert.Equal(int64(5), *medalUpdatedInfo.After.TFreePoint)
	// 第二次加, 观看半小时
	_, err = col.UpdateOne(ctx, filter, bson.M{"$set": bson.M{"t_free_point": nil}})
	require.NoError(err)
	p.AccessTime = todayBegin.Add(30 * time.Minute)
	p.LastCallTime = todayBegin.Add(6 * time.Minute)
	medalUpdatedInfo, err = p.AddOnlineTime()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NoError(luCol.FindOne(ctx, filter).Decode(&lu))
	assert.Equal(int64(1800000), lu.TodayAcqOnline)
	require.NotNil(medalUpdatedInfo.After)
	assert.Equal(int64(1020), medalUpdatedInfo.After.Point)
	assert.Equal(int64(15), *medalUpdatedInfo.After.TFreePoint) // 第二次加之前清空了结果，所以不累计第一次加的结果
	// 直接观看半小时，但是有上限不能加满
	_, err = col.UpdateOne(ctx, filter, bson.M{
		"$set": bson.M{"point": 1483, "t_free_point": 0}})
	require.NoError(err)
	_, err = luCol.DeleteMany(ctx, filter)
	require.NoError(err)
	p.AccessTime = todayBegin.Add(30 * time.Minute)
	p.LastCallTime = todayBegin
	medalUpdatedInfo, err = p.AddOnlineTime()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	require.NoError(luCol.FindOne(ctx, filter).Decode(&lu))
	assert.Equal(int64(1800000), lu.TodayAcqOnline)
	assert.Equal(int64(1500), medalUpdatedInfo.After.Point)
	assert.Equal(int64(17), *medalUpdatedInfo.After.TFreePoint)
	// 上限限制了不让加
	_, err = luCol.DeleteMany(ctx, filter)
	require.NoError(err)
	p.AccessTime = todayBegin.Add(30 * time.Minute)
	p.LastCallTime = todayBegin
	medalUpdatedInfo, err = p.AddOnlineTime()
	require.Nil(medalUpdatedInfo)
	require.NoError(err)
	var lm livemedal.LiveMedal
	require.NoError(col.FindOne(ctx, filter).Decode(&lm))
	assert.Equal(int64(1500), lm.Point)
	assert.Equal(int64(17), *lm.TFreePoint)
	// 没触发开关，不加
	p.AccessTime = todayBegin.Add(30 * time.Minute)
	p.LastCallTime = p.AccessTime.Add(-1 * time.Millisecond)
	medalUpdatedInfo, err = p.AddOnlineTime()
	require.NoError(err)
	require.Nil(medalUpdatedInfo)
	require.NoError(luCol.FindOne(ctx, filter).Decode(&lu))
	assert.Equal(int64(1800001), lu.AcqOnline)
	assert.Zero(lu.TodayAcqOnline)
	require.NoError(col.FindOne(ctx, filter).Decode(&lm))
	assert.Equal(int64(1500), lm.Point)
	assert.Equal(int64(17), *lm.TFreePoint)
	// 没有勋章，不加亲密度，但是加时间
	_, err = col.UpdateOne(ctx, filter, bson.M{
		"$set": bson.M{"status": livemedal.StatusPending, "t_point": lm.Point, "t_free_point": 0}})
	require.NoError(err)
	_, err = luCol.DeleteMany(ctx, filter)
	require.NoError(err)
	p.AccessTime = todayBegin.Add(time.Hour)
	p.LastCallTime = todayBegin
	medalUpdatedInfo, err = p.AddOnlineTime()
	require.NoError(err)
	require.Nil(medalUpdatedInfo)
	require.NoError(luCol.FindOne(ctx, filter).Decode(&lu))
	assert.Equal(int64(3600000), lu.AcqOnline)
	assert.Zero(lu.TodayAcqOnline)
	require.NoError(col.FindOne(ctx, filter).Decode(&lm))
	assert.Equal(int64(1500), lm.Point)
	assert.Zero(*lm.TFreePoint)
}
