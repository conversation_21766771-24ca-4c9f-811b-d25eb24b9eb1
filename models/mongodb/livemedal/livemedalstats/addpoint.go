package livemedalstats

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 时间常量定义
const (
	ms360000  = 360000  // 6 分钟
	ms1800000 = 1800000 // 30 分钟
)

// AddFreePointParam 添加免费亲密度参数
type AddFreePointParam struct {
	RoomID    int64
	CreatorID int64
	UserID    int64
	PointAdd  int64
	Scene     int

	actualAddPoint        int64
	returnedPoint         int64
	pointMulti            int64
	thresholdMulti        int64
	actualTLevelThreshold int64
}

// IsSceneFreeGift 是否是免费礼物场景
func (param *AddFreePointParam) IsSceneFreeGift() bool {
	return param.Scene == livemedalpointlog.SceneTypeFreeGift
}

// AddFreePoint 添加免费亲密度
// 没获得勋章不加亲密度
func (param *AddFreePointParam) AddFreePoint() (*livemedal.MedalUpdatedInfo, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemedal.Collection()
	filter := bson.M{
		"room_id":    param.RoomID,
		"user_id":    param.UserID,
		"status":     bson.M{"$ne": livemedal.StatusPending},
		"creator_id": bson.M{"$ne": param.UserID}, // 房主不给自己加免费经验值
	}
	opts := options.FindOne().SetProjection(
		bson.M{"_id": 1, "point": 1, "status": 1, "t_point": 1, "t_free_point": 1, "t_updated_time": 1, "t_free_updated_time": 1, "t_reduce_point": 1, "name": 1, "super_fan": 1})
	var beforeMedal livemedal.LiveMedal
	err := col.FindOne(ctx, filter, opts).Decode(&beforeMedal)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	now := goutil.TimeNow()
	bod := util.BeginningOfDay(now)
	var todayFirstSign bool
	if beforeMedal.TUpdatedTime.Before(bod) &&
		(beforeMedal.TFreeUpdatedTime == nil || beforeMedal.TFreeUpdatedTime.Before(bod)) {
		// 今日新增亲密度时，使用 TPoint 来记录首次更新前的亲密度
		beforeMedal.TPoint = beforeMedal.Point
		todayFirstSign = true
	}
	level := livemedal.ParseLevel(beforeMedal.TPoint)
	param.actualTLevelThreshold = livemedal.LevelThreshold()[level-1]
	param.pointMulti, param.thresholdMulti = livemedal.FindMedalPointMultiple(param.CreatorID, livemedal.IsSuperFanActive(beforeMedal.SuperFan))
	if param.IsSceneFreeGift() {
		// 免费礼物需要计算亲密度倍率
		param.PointAdd *= param.pointMulti
	}
	param.actualTLevelThreshold *= param.thresholdMulti
	rest := param.actualTLevelThreshold - (beforeMedal.Point - beforeMedal.TPoint)
	if rest <= 0 {
		// 没有余量用于增加亲密度
		param.savePointChangeLog(&beforeMedal, nil)
		return nil, nil
	}
	set := bson.M{
		"updated_time":        now,
		"t_free_updated_time": now,
	}
	param.actualAddPoint = min(rest, param.PointAdd)
	inc := bson.M{"point": param.actualAddPoint}
	if todayFirstSign {
		// 今日新增亲密度时，需要更新 TPoint 的值
		set["t_point"] = beforeMedal.TPoint
		set["t_free_point"] = param.actualAddPoint
	} else {
		if beforeMedal.TFreePoint != nil {
			inc["t_free_point"] = param.actualAddPoint
		} else {
			set["t_free_point"] = param.actualAddPoint
		}
	}
	update := bson.M{
		"$set": set,
		"$inc": inc,
	}
	// 赠送免费礼物可防止亲密值衰减
	if param.IsSceneFreeGift() {
		// 防止亲密值衰减
		set["t_updated_time"] = now
		// 如果今日有衰减需要加回来
		if beforeMedal.TReducePoint != nil {
			param.returnedPoint = -*beforeMedal.TReducePoint // 衰减返还的亲密度
			param.actualAddPoint += param.returnedPoint
			inc["point"] = param.actualAddPoint
			set["t_point"] = beforeMedal.TPoint - *beforeMedal.TReducePoint
			update["$unset"] = bson.M{"t_reduce_point": ""}
		}
	}
	afterUpdateMedal := new(livemedal.LiveMedal)
	err = col.FindOneAndUpdate(ctx, filter, update,
		options.FindOneAndUpdate().SetReturnDocument(options.After)).Decode(afterUpdateMedal)
	if err != nil {
		return nil, err
	}
	afterUpdateMedal.BuildFullInfo()
	// 如果等级改变向 rpc 发送消息
	var nLevel = livemedal.ParseLevel(beforeMedal.Point + param.actualAddPoint)
	if level != nLevel {
		if err = livemedal.SyncMedal(0, param.RoomID, param.UserID, nLevel, beforeMedal.Name); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	param.savePointChangeLog(&beforeMedal, afterUpdateMedal)
	return &livemedal.MedalUpdatedInfo{
		Before: &beforeMedal, // FIXME: Before 的数据应该用 After 的减去加上的数据算出来
		After:  afterUpdateMedal,
	}, nil
}

func (param *AddFreePointParam) savePointChangeLog(beforeMedal, afterMedal *livemedal.LiveMedal) {
	if beforeMedal == nil && afterMedal == nil {
		return
	}
	record := livemedalpointlog.LiveMedalPointChangeLog{
		MedalOID:            beforeMedal.OID.Hex(),
		RoomID:              param.RoomID,
		UserID:              param.UserID,
		CreatorID:           param.CreatorID,
		ChangeType:          livemedalpointlog.ChangeTypeIncrease,
		Scene:               param.Scene,
		ActualChangePoint:   param.actualAddPoint,
		ReturnedPoint:       param.returnedPoint,
		ExpectedChangePoint: param.PointAdd,
		PointMulti:          param.pointMulti,
		TLevelThreshold:     param.actualTLevelThreshold,
		LevelThresholdMulti: param.thresholdMulti,
	}
	if afterMedal != nil {
		record.BeforePoint = afterMedal.Point - param.actualAddPoint
		record.AfterPoint = afterMedal.Point
	} else {
		// 如果 afterMedal 为 nil，则说明是达到亲密度上限
		record.BeforePoint = beforeMedal.Point
		record.AfterPoint = record.BeforePoint
	}
	err := livemedalpointlog.DB().Create(&record).Error
	if err != nil {
		logger.WithFields(
			logger.Fields{
				"oid":                   beforeMedal.OID.Hex(),
				"user_id":               param.UserID,
				"change_type":           record.ChangeType,
				"actual_change_point":   record.ActualChangePoint,
				"expected_change_point": record.ExpectedChangePoint,
			},
		).Error(err)
	}
}

// AddPointParam 添加亲密度参数
type AddPointParam struct {
	RoomOID    primitive.ObjectID
	RoomID     int64
	CreatorID  int64
	MedalName  string
	From       int
	FromRoomID int64 // 用户送出礼物时所在的直播间，跨直播间送礼时不为 0
	IsRoomOpen bool

	UserID int64
	UV     *vip.UserVip

	Type       int
	Source     int // TODO: 合并到 scene 和 Source
	PointAdd   int64
	ExpireTime int64 // 超粉到期时间

	Scene int // 亲密度变化场景

	ownedNormalMedalCount int64
	ownedAllMedalCount    int64
	maxMedalNum           int64

	actualAddPoint        int64
	returnedPoint         int64
	pointMulti            int64
	thresholdMulti        int64
	actualTLevelThreshold int64
}

// AddPoint 添加亲密度和勋章
func (p *AddPointParam) AddPoint() (*livemedal.MedalUpdatedInfo, error) {
	if p.CreatorID == p.UserID {
		return nil, nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	record := new(livemedal.LiveMedal)
	filter := bson.M{"user_id": p.UserID, "room_id": p.RoomID}
	opts := options.FindOne().SetProjection(bson.M{"_id": 1, "point": 1, "t_point": 1, "t_free_point": 1, "t_updated_time": 1,
		"t_free_updated_time": 1, "t_reduce_point": 1, "status": 1, "name": 1, "super_fan": 1})
	err := livemedal.Collection().FindOne(ctx, filter, opts).Decode(record)
	if err != nil {
		if !mongodb.IsNoDocumentsError(err) {
			return nil, err
		}
		record = nil
	}
	// 开通续费超粉亲密度和上限按开通后计算
	isSuperFan := p.Type == livemedal.TypeSuperFanMedalPoint || (record != nil && livemedal.IsSuperFanActive(record.SuperFan))
	// 获取超粉和活动的亲密度倍数
	p.pointMulti, p.thresholdMulti = livemedal.FindMedalPointMultiple(p.CreatorID, isSuperFan)
	medalUpdatedInfo := &livemedal.MedalUpdatedInfo{
		Before: record, // FIXME: Before 的数据应该用 After 的减去加上的数据算出来
	}
	defer func() {
		if medalUpdatedInfo != nil && medalUpdatedInfo.After != nil {
			medalUpdatedInfo.After.BuildFullInfo()
		}
	}()
	if record == nil {
		// 用户未拥有当前房间勋章
		medalUpdatedInfo.After, err = p.addLiveMedal()
		return medalUpdatedInfo, err
	}
	medalUpdatedInfo.After, err = p.updateLiveMedalPoint(record)
	return medalUpdatedInfo, err
}

// 获取已有勋章数量和限制（不包含超粉勋章）
// TODO: 优化，例如使用一个新的 status，避免每次送礼都要 count
func (p *AddPointParam) ownedAndLimitCount() (err error) {
	p.ownedNormalMedalCount, err = livemedal.CountNormalMedal(p.UserID)
	if err != nil {
		return
	}
	p.ownedAllMedalCount, err = livemedal.CountMedal(bson.M{"user_id": p.UserID, "status": bson.M{"$gt": livemedal.StatusPending}})
	if err != nil {
		return
	}
	p.maxMedalNum = vip.MaxMedalLimitNum(p.UV)
	return
}

// 新增粉丝勋章
func (p *AddPointParam) addLiveMedal() (*livemedal.LiveMedal, error) {
	if err := p.ownedAndLimitCount(); err != nil {
		return nil, err
	}
	// 超粉不受粉丝勋章总数限制
	if p.Type != livemedal.TypeSuperFanMedalPoint && p.ownedNormalMedalCount >= p.maxMedalNum {
		return nil, nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	threshold := livemedal.LevelThreshold()[0]

	p.actualTLevelThreshold = threshold * p.thresholdMulti
	p.PointAdd *= p.pointMulti
	p.actualAddPoint = min(p.actualTLevelThreshold, p.PointAdd)
	lm := bson.M{
		// 下文的 filter 包括了 user_id 和 room_id
		"_room_id":       p.RoomOID,
		"t_point":        0,
		"t_updated_time": now,
		"created_time":   now,
		"updated_time":   now,
		"creator_id":     p.CreatorID,
		"status":         livemedal.StatusPending,
		"name":           p.MedalName,
	}
	isNew := p.actualAddPoint >= livemedal.MinContribution
	if isNew {
		lm["status"] = livemedal.StatusOwned
		lm["from"] = p.From
		if p.ownedAllMedalCount <= 0 {
			lm["status"] = livemedal.StatusShow
		}
	}
	if p.Type == livemedal.TypeSuperFanMedalPoint {
		lm["super_fan.expire_time"] = p.ExpireTime
	}
	updates := bson.M{
		"$set": lm,
		"$inc": bson.M{
			"point": p.actualAddPoint,
		},
	}
	filter := bson.M{"user_id": p.UserID, "room_id": p.RoomID}
	var medalAfterUpdate livemedal.LiveMedal
	err := livemedal.Collection().FindOneAndUpdate(ctx, filter, updates,
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).
		Decode(&medalAfterUpdate)
	if err != nil {
		return nil, err
	}
	p.savePointChangeLog(nil, &medalAfterUpdate)
	isNew = isNew && medalAfterUpdate.Point-p.actualAddPoint < livemedal.MinContribution
	if medalAfterUpdate.Point >= livemedal.MinContribution && medalAfterUpdate.Status == livemedal.StatusPending {
		// 用户送礼较快时，可能两次更新亲密度操作前计算出来的 status 都为 livemedal.StatusPending，
		// 更新完毕后的亲密度可能大于等于 livemedal.MinContribution 且 status 仍为 livemedal.StatusPending，此时需要更新 status 为正确的状态
		status := livemedal.StatusOwned
		if p.ownedAllMedalCount <= 0 {
			status = livemedal.StatusShow
		}
		medalAfterUpdate.Status = status
		lmBefore := new(livemedal.LiveMedal)
		err := livemedal.Collection().FindOneAndUpdate(ctx, filter, bson.M{"$set": bson.M{"status": status, "from": p.From}},
			options.FindOneAndUpdate().SetReturnDocument(options.Before)).Decode(lmBefore)
		if err != nil {
			return nil, err
		}
		isNew = lmBefore.Status == livemedal.StatusPending
	}
	if isNew {
		livemedal.SendLiveMedalChange(p.UserID, p.RoomID, livemedal.ChangeTypeNew, livemedal.MedalChangeReason(p.Source), p.Source)
		p.contributeEnergy()
	}
	if err := p.finalMedalCheckLimit(medalAfterUpdate.Point); err != nil {
		logger.Error(err)
		// PASS
	}

	if medalAfterUpdate.Status > livemedal.StatusPending {
		// 拥有勋章才同步
		if err := livemedal.SyncMedal(p.FromRoomID, p.RoomID, p.UserID, livemedal.ParseLevel(medalAfterUpdate.Point), p.MedalName); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	if medalAfterUpdate.Status == livemedal.StatusShow {
		livemedal.DelUsersTitlesCache(p.UserID)
	}
	return &medalAfterUpdate, nil
}

// 更新已有粉丝勋章亲密度
func (p *AddPointParam) updateLiveMedalPoint(record *livemedal.LiveMedal) (*livemedal.LiveMedal, error) {
	err := p.ownedAndLimitCount()
	if err != nil {
		return nil, err
	}
	now := goutil.TimeNow()
	bod := util.BeginningOfDay(now)
	var todayFirstSign bool
	if record.TUpdatedTime.Before(bod) &&
		(record.TFreeUpdatedTime == nil || record.TFreeUpdatedTime.Before(bod)) {
		// 今日首次新增亲密度时，使用 TPoint 来记更新前的亲密度
		record.TPoint = record.Point
		todayFirstSign = true
	}
	level := livemedal.ParseLevel(record.TPoint)
	threshold := livemedal.LevelThreshold()[level-1]
	// 活动亲密度双倍
	p.actualTLevelThreshold = threshold * p.thresholdMulti
	p.PointAdd *= p.pointMulti
	rest := p.actualTLevelThreshold - (record.Point - record.TPoint)
	if rest <= 0 && p.Type != livemedal.TypeSuperFanMedalPoint {
		// 没有余量用于增加亲密度且不是购买超粉
		p.savePointChangeLog(record, nil)
		return nil, nil
	}
	var isNew bool
	set := bson.M{
		"updated_time": now,
	}
	update := bson.M{"$set": set}
	if rest > 0 {
		set["t_updated_time"] = now
		p.actualAddPoint = min(rest, p.PointAdd)
		if todayFirstSign {
			// 今日首次新增亲密度时，需要更新 TPoint 的值
			set["t_point"] = record.TPoint
		}

		// TODO: 调用这个函数的应该都是付费礼物都需要防止亲密值衰减，可以考虑去除 type 判断
		if goutil.HasElem([]int{livemedal.TypeGiftAddMedalPoint, livemedal.TypeQuestionAddMedalPoint, livemedal.TypeSuperFanMedalPoint, livemedal.TypeDanmakuMedalPoint, livemedal.TypeLuckyBoxMedalPoint}, p.Type) &&
			record.TReducePoint != nil {
			// 付费亲密度，如果今日有衰减需要加回来
			set["t_point"] = record.TPoint - *record.TReducePoint
			p.actualAddPoint -= *record.TReducePoint
			p.returnedPoint = -*record.TReducePoint
			update["$unset"] = bson.M{"t_reduce_point": ""}
		}
		update["$inc"] = bson.M{"point": p.actualAddPoint}
		// 忽略超粉太便宜的情况
		if record.Status == livemedal.StatusPending && (record.Point+p.actualAddPoint) >= livemedal.MinContribution {
			isNew = true
			set["status"] = livemedal.StatusOwned
			set["from"] = p.From
			if p.ownedAllMedalCount <= 0 {
				set["status"] = livemedal.StatusShow
			}
		}
	}
	if p.Type == livemedal.TypeSuperFanMedalPoint {
		update["$max"] = bson.M{"super_fan.expire_time": p.ExpireTime}
	}
	lm := new(livemedal.LiveMedal)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	if err = livemedal.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": p.UserID, "room_id": p.RoomID}, update,
		options.FindOneAndUpdate().SetReturnDocument(options.After)).Decode(lm); err != nil {
		return nil, err
	}
	p.savePointChangeLog(record, lm)
	isNew = isNew && lm.Point-p.actualAddPoint < livemedal.MinContribution
	// 送礼太快可能导致参数 record 的 point 的值较老，不是最新值，导致 status 设置错误，需要使用更新完后的 lm 根据最新 point 值再次更新 status 状态
	if rest > 0 && lm.Status == livemedal.StatusPending && lm.Point >= livemedal.MinContribution {
		lm.Status = livemedal.StatusOwned
		if p.ownedAllMedalCount <= 0 {
			lm.Status = livemedal.StatusShow
		}
		lmBefore := new(livemedal.LiveMedal)
		err := livemedal.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": p.UserID, "room_id": p.RoomID},
			bson.M{"$set": bson.M{"status": lm.Status, "from": p.From}},
			options.FindOneAndUpdate().SetReturnDocument(options.Before)).Decode(lmBefore)
		if err != nil {
			return nil, err
		}
		isNew = lmBefore.Status == livemedal.StatusPending
	}
	if isNew {
		// 新获得勋章发送 databus 消息
		livemedal.SendLiveMedalChange(p.UserID, p.RoomID, livemedal.ChangeTypeNew, livemedal.MedalChangeReason(p.Source), p.Source)
		p.contributeEnergy()
	}
	if record.Status == livemedal.StatusPending {
		// 勋章的状态由 pending 变成非 pending 之后会在勋章数量满了的情况下移除掉其余还未拥有的勋章
		if err = p.finalMedalCheckLimit(lm.Point); err != nil {
			logger.Error(err)
			// PASS
		}
	}

	if rest <= 0 {
		// 不增加亲密度时，不需要下面同步勋章等级
		return lm, nil
	}

	var nLevel = livemedal.ParseLevel(lm.Point)
	// NOTICE: 用户 0 < point && point < 60 未获得勋章时, 勋章等级也为 1, 这种新获取勋章等级未变动时也需要发送等级变更消息
	if isNew || level != nLevel {
		if err = livemedal.SyncMedal(p.FromRoomID, p.RoomID, p.UserID, nLevel, lm.Name); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return lm, nil
}

func (p *AddPointParam) finalMedalCheckLimit(finalPoint int64) error {
	if finalPoint >= livemedal.MinContribution && p.ownedNormalMedalCount+1 >= p.maxMedalNum {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		// 勋章数量满了的话，只对拥有的勋章的经验进行累加，这里移除掉其余还未拥有的勋章
		if _, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": p.UserID, "status": livemedal.StatusPending}); err != nil {
			return err
		}
	}
	return nil
}

func (p *AddPointParam) savePointChangeLog(beforeMedal, afterMedal *livemedal.LiveMedal) {
	if beforeMedal == nil && afterMedal == nil {
		return
	}
	record := livemedalpointlog.LiveMedalPointChangeLog{
		RoomID:              p.RoomID,
		UserID:              p.UserID,
		CreatorID:           p.CreatorID,
		ChangeType:          livemedalpointlog.ChangeTypeIncrease,
		Scene:               p.Scene,
		ActualChangePoint:   p.actualAddPoint,
		ReturnedPoint:       p.returnedPoint,
		ExpectedChangePoint: p.PointAdd,
		PointMulti:          p.pointMulti,
		TLevelThreshold:     p.actualTLevelThreshold,
		LevelThresholdMulti: p.thresholdMulti,
	}
	if afterMedal != nil {
		record.MedalOID = afterMedal.OID.Hex()
		record.BeforePoint = afterMedal.Point - p.actualAddPoint
		record.AfterPoint = afterMedal.Point
	} else {
		// 如果 afterMedal 为 nil，则说明是达到亲密度上限
		record.MedalOID = beforeMedal.OID.Hex()
		record.BeforePoint = beforeMedal.Point
		record.AfterPoint = record.BeforePoint
	}
	err := livemedalpointlog.DB().Create(&record).Error
	if err != nil {
		logger.WithFields(
			logger.Fields{
				"oid":                   record.MedalOID,
				"user_id":               p.UserID,
				"change_type":           record.ChangeType,
				"actual_change_point":   record.ActualChangePoint,
				"expected_change_point": record.ExpectedChangePoint,
			},
		).Error(err)
	}
}

func (p *AddPointParam) contributeEnergy() {
	if !p.IsRoomOpen {
		return
	}
	// TODO: Re-enable after fixing import cycle
	// err := energycontributor.Contribute(p.RoomID, p.UserID, energyPerMedal)
	// if err != nil {
	// 	logger.Error(err)
	// }
}

// AddOnlineTimeParam 观看时长增加亲密度参数
type AddOnlineTimeParam struct {
	UserID       int64
	RoomID       int64
	RoomOID      primitive.ObjectID
	CreatorID    int64
	AccessTime   time.Time
	LastCallTime time.Time
}

// AddOnlineTime 增加在线时间记录
func (param *AddOnlineTimeParam) AddOnlineTime() (*livemedal.MedalUpdatedInfo, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	collection := service.MongoDB.Collection("live_users")
	var lu struct {
		RoomOID         primitive.ObjectID `bson:"_room_id"`
		RoomID          int64              `bson:"room_id"`
		TodayTimeOnline time.Time          `bson:"t_time_online"`
		TodayAcqOnline  int64              `bson:"t_acq_online"` // 单位：毫秒
		AcqOnline       int64              `bson:"acq_online"`   // 单位：毫秒
	}
	filter := bson.M{"room_id": param.RoomID, "user_id": param.UserID}
	err := collection.FindOne(ctx, filter, options.FindOne().SetProjection(
		bson.M{"t_time_online": 1, "t_acq_online": 1, "acq_online": 1})).Decode(&lu)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return nil, err
	}
	durationMs := param.AccessTime.Sub(param.LastCallTime).Milliseconds()
	lu.AcqOnline += durationMs // 计算总观看时长
	lu.RoomOID = param.RoomOID
	lu.RoomID = param.RoomID
	owned, err := livemedal.HasUserOwnedMedal(param.UserID, param.RoomID)
	if err != nil {
		return nil, err
	}
	if !owned {
		// 用户没有当前直播间勋章时只计算总观看时长 AcqOnline, 不修改 TodayAcqOnline 及 TodayTimeOnline
		_, err = collection.UpdateOne(ctx, filter, bson.M{"$set": lu},
			options.Update().SetUpsert(true))
		return nil, err
	}
	// 统计当日观看时间，计算勋章亲密度观看时长任务
	todayBegin := goutil.BeginningOfDay(param.AccessTime)
	todayDurationMs := durationMs
	if param.LastCallTime.Before(todayBegin) {
		// 处理跨天的情况，仅统计当日时长，不考虑跨天的累计
		todayDurationMs = param.AccessTime.Sub(todayBegin).Milliseconds()
	}
	var acqs [2]int64
	if lu.TodayTimeOnline.Before(todayBegin) {
		lu.TodayAcqOnline = todayDurationMs
	} else {
		acqs[0] = lu.TodayAcqOnline
		lu.TodayAcqOnline += todayDurationMs
	}
	acqs[1] = lu.TodayAcqOnline
	lu.TodayTimeOnline = param.AccessTime
	_, err = collection.UpdateOne(ctx, filter, bson.M{"$set": lu},
		options.Update().SetUpsert(true))
	if err != nil {
		return nil, err
	}
	// 计算勋章是否需要增加经验值
	var zone [2]int64
	for i := 0; i < 2; i++ {
		switch {
		case acqs[i] < ms360000:
			zone[i] = 0
		case acqs[i] < ms1800000:
			zone[i] = 5
		default:
			zone[i] = 20
		}
	}
	if zone[0] == zone[1] {
		// 没触发开关
		return nil, nil
	}
	addPointparam := &AddFreePointParam{
		RoomID:    param.RoomID,
		CreatorID: param.CreatorID,
		UserID:    param.UserID,
		PointAdd:  zone[1] - zone[0],
		Scene:     livemedalpointlog.SceneTypeTaskOnline,
	}
	return addPointparam.AddFreePoint()
}
