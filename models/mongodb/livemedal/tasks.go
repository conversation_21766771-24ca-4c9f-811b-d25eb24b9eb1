package livemedal

// 一键获取粉丝勋章礼物
// 粉丝体系优化: https://info.missevan.com/pages/viewpage.action?pageId=84719169
const (
	ObtainMedalGiftID          int64 = 10154
	ObtainMedalHalfPriceGiftID int64 = 10155
	ObtainMedalDiscountGiftID  int64 = 10335 // 1 折购买粉丝勋章礼物
)

// 任务类型
const (
	TaskTypeSendCatFood      = iota + 1 // 赠送猫粮
	TaskTypeShareRoom                   // 分享直播间
	TaskTypeOnline6Minute               // 收听直播 6 分钟
	TaskTypeOnline30Minute              // 收听直播 30 分钟
	TaskTypeSendGift                    // 赠送礼物
	TaskTypeRegisterSuperFan            // 开通超粉
)

// MedalTask 亲密度任务
type MedalTask struct {
	TaskType    int    `json:"task_type"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      int    `json:"status"`
}

// MedalTasks 粉丝亲密度任务
func MedalTasks() []*MedalTask {
	return []*MedalTask{
		{
			TaskType:    TaskTypeSendCatFood,
			Name:        "赠送猫粮",
			Description: "+1 亲密度 / 1 猫粮，超粉双倍",
		},
		{
			TaskType:    TaskTypeShareRoom,
			Name:        "分享开播直播间",
			Description: "+10 亲密度",
		},
		{
			TaskType:    TaskTypeOnline6Minute,
			Name:        "收听 6 分钟直播",
			Description: "+5 亲密度",
		},
		{
			TaskType:    TaskTypeOnline30Minute,
			Name:        "收听 30 分钟直播",
			Description: "+15 亲密度",
		},
		{
			TaskType:    TaskTypeSendGift,
			Name:        "赠送礼物",
			Description: "+1 亲密度 / 1 钻，超粉双倍",
		},
		{
			TaskType:    TaskTypeRegisterSuperFan,
			Name:        "开通超粉",
			Description: "开通超粉，双倍亲密度助力升级",
		},
	}
}
