package boxprizes

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestPrizeConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := PrizeConfig(175, BoxTypeOrdinary)
	require.NoError(err)
	require.Len(r, 3)
	r, err = PrizeConfig(175, BoxTypeUltimate)
	require.NoError(err)
	assert.Len(r, 6)
	r, err = PrizeConfig(1, BoxTypeUltimate)
	require.NoError(err)
	assert.Len(r, 0)
}
