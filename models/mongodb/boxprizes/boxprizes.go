package boxprizes

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// BoxTypeOrdinary 普通宝箱
	BoxTypeOrdinary = iota + 1
	// BoxTypeUltimate 终极宝箱
	BoxTypeUltimate
)

// 宝箱等级
const (
	BoxLevelC = iota + 1
	BoxLevelB
	BoxLevelA
	BoxLevelS
)

// 宝箱奖品类型
const (
	PrizeTypeAvatarFrame   = iota + 1 // 奖品类型为头像框
	PrizeTypeMessageBubble            // 奖品类型为气泡框
	PrizeTypeBackpackGift             // 奖品类型为背包礼物
	PrizeTypeGiftCustom               // 奖品类型为礼物赠送资格
	PrizeTypeBadge                    // 奖品类型为称号
	PrizeTypeVehicle                  // 奖品类型为座驾
)

// BoxPrize 宝箱奖品
type BoxPrize struct {
	OID        primitive.ObjectID `bson:"_id,omitempty"`
	PrizeID    int64              `bson:"prize_id"`    // 宝箱奖品 ID
	EventID    int64              `bson:"event_id"`    // 活动 ID
	BoxType    int                `bson:"box_type"`    // 宝箱类型，比如初级宝箱、终极宝箱
	PrizeCount int64              `bson:"prize_count"` // 宝箱奖品发放的数量

	PrizeItemName     string `bson:"prize_item_name"`               // 宝箱奖品名称
	PrizeItemID       int64  `bson:"prize_item_id"`                 // 宝箱奖品的 ID，比如礼物 ID、外观 ID
	PrizeItemType     int    `bson:"prize_item_type"`               // 宝箱奖品的类型，比如头像框、背包礼物
	PrizeItemDuration int64  `bson:"prize_item_duration,omitempty"` // 宝箱奖品的有效期，比如外观有效期、礼物赠送资格有效期，背包礼物类型的奖品不需要设置该字段
	PrizeItemEndTime  int64  `bson:"prize_item_end_time,omitempty"` // 宝箱奖品的有效期的结束时间，只有背包礼物类型的奖品需要设置该字段
	PrizeItemNum      int64  `bson:"prize_item_num"`                // 宝箱奖品的数量
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("box_prizes")
}

// PrizeConfig 根据活动 ID 和宝箱类型获得对应的宝箱奖品，返回宝箱奖品 ID 到宝箱奖品的对应关系
func PrizeConfig(eventID int64, boxType int) (map[int64]*BoxPrize, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	cur, err := col.Find(ctx, bson.M{"event_id": eventID, "box_type": boxType})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*BoxPrize
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	resMap := goutil.ToMap(res, "PrizeID").(map[int64]*BoxPrize)
	return resMap, nil
}
