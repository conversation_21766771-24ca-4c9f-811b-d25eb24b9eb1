package usercommon

import (
	"fmt"
	"sort"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service/storage"
)

// LevelStart 等级经验对照
var LevelStart = []int64{
	0,
	100,
	500,
	1000,
	2000,
	3000,
	4500,
	6500,
	8500,
	11000,
	15000,
	20000,
	25000,
	30000,
	35000,
	40000,
	50000,
	60000,
	70000,
	85000,
	100000,
	120000,
	140000,
	160000,
	180000,
	200000,
	230000,
	270000,
	310000,
	350000,
	400000,
	450000,
	550000,
	650000,
	750000,
	850000,
	950000,
	1050000,
	1200000,
	1400000,
	1600000,
	1800000,
	2000000,
	2200000,
	2500000,
	3000000,
	3500000,
	4200000,
	5000000,
	5800000,
	6600000,
	7400000,
	8200000,
	9000000,
	9800000,
	10600000,
	11500000,
	12500000,
	13500000,
	14500000,
	16000000,
	17500000,
	19000000,
	20500000,
	22000000,
	23500000,
	25000000,
	27000000,
	29000000,
	31000000,
	33000000,
	35500000,
	38000000,
	40500000,
	43000000,
	45500000,
	48000000,
	51000000,
	54000000,
	57000000,
	60000000,
	63500000,
	67000000,
	70500000,
	74000000,
	78000000,
	82000000,
	86000000,
	90000000,
	94000000,
	98000000,
	102000000,
	107000000,
	112000000,
	117000000,
	122000000,
	127000000,
	132000000,
	137000000,
	143000000,
	149000000,
	155000000,
	162000000,
	169000000,
	176000000,
	183000000,
	190000000,
	197000000,
	205000000,
	213000000,
	221000000,
	229000000,
	237000000,
	245000000,
	254000000,
	263000000,
	272000000,
	281000000,
	290000000,
	300000000,
	310000000,
	320000000,
	330000000,
	340000000,
	350000000,
	360000000,
	375000000,
	390000000,
	405000000,
	420000000,
	435000000,
	450000000,
	470000000,
	490000000,
	510000000,
	530000000,
	550000000,
	580000000,
	610000000,
	640000000,
	670000000,
	700000000,
	735000000,
	770000000,
	805000000,
	840000000,
	880000000,
	920000000,
	960000000,
	1000000000,
	1060000000,
	1120000000,
	1180000000,
	1240000000,
	1300000000,
	1360000000,
	1420000000,
	1480000000,
	1540000000,
	1600000000,
	1660000000,
	1720000000,
	1780000000,
	1840000000,
	1900000000,
	1960000000,
	2020000000,
	2080000000,
	2140000000,
	2200000000,
	2260000000,
	2320000000,
	2380000000,
	2440000000,
	2500000000,
	2560000000,
	2620000000,
	2680000000,
	2740000000,
	2800000000,
	2860000000,
	2920000000,
	2980000000,
	3040000000,
	3100000000,
	3160000000,
	3220000000,
	3280000000,
	3340000000,
	3400000000,
	3460000000,
	3520000000,
	3580000000,
	3640000000,
	3700000000,
	3760000000,
	3820000000,
	3880000000,
	3940000000,
	4000000000,
	4060000000,
	4120000000,
	4180000000,
	4240000000,
	4300000000,
	4360000000,
	4420000000,
	4480000000,
	4540000000,
	4600000000,
	4660000000,
	4720000000,
	4780000000,
	4840000000,
	4900000000,
	4960000000,
	5020000000,
	5080000000,
	5140000000,
	5200000000,
	5260000000,
	5320000000,
	5380000000,
	5440000000,
	5500000000,
	5560000000,
	5620000000,
	5680000000,
	5740000000,
	5800000000,
	5860000000,
	5920000000,
	5980000000,
	6040000000,
	6100000000,
	6160000000,
	6220000000,
	6280000000,
	6340000000,
	6400000000,
	6460000000,
	6520000000,
	6580000000,
	6640000000,
	6700000000,
	6760000000,
	6820000000,
	6880000000,
	6940000000,
	7000000000,
}

// Level 获取等级
func Level(contribution int64) int {
	if contribution <= 0 {
		contribution = 1
	}
	return sort.Search(len(LevelStart), func(i int) bool {
		return LevelStart[i] > contribution
	})
}

// LevelIconURL 等级 icon，120 级以下（不含 120 级）用客户端本地图片
func LevelIconURL(level int) string {
	if level < 120 {
		return ""
	}
	return storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/level%d.webp", config.DefaultCDNScheme, level))
}

// IsMaxLevel 判断是否满级
func IsMaxLevel(level int) bool {
	return level >= len(LevelStart)
}

// GetLevelStartContribution 返回当前等级起始贡献值
func GetLevelStartContribution(level int) int64 {
	if level == 0 {
		return 0
	}
	return LevelStart[level-1]
}
