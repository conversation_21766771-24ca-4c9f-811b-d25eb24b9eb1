package usercommon

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLevel(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, Level(-1))
	assert.Equal(1, Level(0))
	assert.Equal(20, Level(LevelStart[19]))
	assert.Equal(50, Level(LevelStart[49]))
	assert.Equal(150, Level(LevelStart[149]))
	assert.Equal(200, Level(LevelStart[199]))
	assert.Equal(201, Level(LevelStart[200]))
	assert.Equal(250, Level(LevelStart[249]))
	assert.Equal(250, Level(LevelStart[249]*2))
}
