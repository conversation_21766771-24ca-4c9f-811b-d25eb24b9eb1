package useritems

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	mgift "github.com/MiaoSiLa/live-service/models/mysql/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Adder 礼物 adder
type Adder struct {
	userID int64
	now    time.Time

	// 福袋礼物、礼物红包使用
	transactionID int64
	context       string

	Adds []UserItem
}

// NewAdder new adder
func NewAdder(userID int64, giftCap int) *Adder {
	return &Adder{
		userID: userID,
		now:    goutil.TimeNow(),
		Adds:   make([]UserItem, 0, giftCap),
	}
}

// NewTransactionAdder new transaction Adder
func NewTransactionAdder(userID int64, transactionID int64, context string, giftCap int) *Adder {
	return &Adder{
		userID:        userID,
		transactionID: transactionID,
		context:       context,
		now:           goutil.TimeNow(),
		Adds:          make([]UserItem, 0, giftCap),
	}
}

// Append append 待添加的背包礼物
func (adder *Adder) Append(g *gift.Gift, num int64, startTime, endTime int64) *Adder {
	adder.Adds = append(adder.Adds, UserItem{
		CreateTime:   adder.now.Unix(),
		ModifiedTime: adder.now.Unix(),
		Type:         TypeFromGiftType(g.Type),
		UserID:       adder.userID,
		StartTime:    startTime,
		EndTime:      endTime,

		GiftID:  g.GiftID,
		Num:     num,
		GainNum: num,

		TransactionID: adder.transactionID,
		Context:       adder.context,
	})
	return adder
}

// Add 将背包礼物下发给用户
func (adder *Adder) Add() error {
	if len(adder.Adds) == 0 {
		return nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	adds := make([]interface{}, len(adder.Adds))
	for i := range adder.Adds {
		adds[i] = adder.Adds[i]
	}
	_, err := Collection().InsertMany(ctx, adds)
	if err != nil {
		return err
	}
	adder.Adds = make([]UserItem, 0)
	return nil
}

// AddGiftToUsers 为多个用户添加礼物，可用于给单个用户发礼物
func AddGiftToUsers(userIDs []int64, g *gift.Gift, num int64, source int, startTime, endTime int64) error {
	inserts := make([]interface{}, len(userIDs))
	now := goutil.TimeNow()
	for i := range userIDs {
		inserts[i] = &UserItem{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			Type:         TypeFromGiftType(g.Type),
			Source:       source,
			UserID:       userIDs[i],
			StartTime:    startTime,
			EndTime:      endTime,

			GiftID:  g.GiftID,
			Num:     num,
			GainNum: num,
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, inserts)
	return err
}

// AddGiftToUserAndUpdateEndTime 为用户添加礼物 并且更新同一个礼物的结束日期
func AddGiftToUserAndUpdateEndTime(userID int64, g *gift.Gift, num int64, startTime, endTime int64) error {
	now := goutil.TimeNow().Unix()
	updates := []mongo.WriteModel{
		mongo.NewUpdateManyModel().SetFilter(
			setValidTimeFilter(bson.M{
				"user_id": userID,
				"gift_id": g.GiftID,
				"num":     bson.M{"$gt": 0},
			}, now)).
			SetUpdate(bson.M{
				"$set": bson.M{"end_time": endTime, "modified_time": now},
			}),
		mongo.NewInsertOneModel().SetDocument(&UserItem{
			CreateTime:   now,
			ModifiedTime: now,
			Type:         TypeFromGiftType(g.Type),
			UserID:       userID,
			StartTime:    startTime,
			EndTime:      endTime,

			GiftID:  g.GiftID,
			Num:     num,
			GainNum: num,
		}),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().BulkWrite(ctx, updates, options.BulkWrite().SetOrdered(true))
	if err != nil {
		return err
	}
	return nil
}

var errGiftNum = errors.New("gift num is not enough")

// GiftSender 背包礼物送礼器
type GiftSender struct {
	UserID    int64
	RoomID    int64
	CreatorID int64
	Gift      *gift.Gift
	Num       int64
	OpenLogID *primitive.ObjectID
	C         goutil.UserContext
}

// Send 送礼
// 返回是否送出，剩余礼物数量，订单号，error
func (s GiftSender) Send() (bool, int64, []int64, error) {
	if s.Num <= 0 {
		panic("num must greater than 0")
	}
	now := goutil.TimeNow().Unix()
	userItems, err := Find(setValidTimeFilter(bson.M{
		"user_id": s.UserID,
		"gift_id": s.Gift.GiftID,
		"num":     bson.M{"$gt": 0},
	}, now), options.Find().SetSort(bson.M{"end_time": 1}))
	if err != nil {
		return false, 0, nil, err
	}
	var countBefore int64
	for i := range userItems {
		countBefore += userItems[i].Num
	}
	if countBefore < s.Num {
		return false, countBefore, nil, nil
	}

	unlock := lockSendGift(s.UserID, s.Gift.GiftID)
	if unlock == nil {
		return false, countBefore, nil, nil
	}
	defer unlock()

	updates := make([]mongo.WriteModel, 0, len(userItems))
	gc := make([]userapi.Gift, 0, len(userItems))
	rebateGiftNum := int64(0)
	num := s.Num // 待送出的礼物数量
	var g *mgift.Gift
	for i := 0; i < len(userItems) && num > 0; i++ {
		// numInc 减少的数量
		// NOTICE: numInc 是负数
		numInc := -userItems[i].Num
		if num < userItems[i].Num {
			numInc = -num
		}
		num += numInc // 减少待送出的礼物数量
		updateOne := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": userItems[i].OID, "num": bson.M{"$gte": -numInc}}).
			SetUpdate(bson.M{
				"$inc": bson.M{"num": numInc},
				"$set": bson.M{"modified_time": now},
			})
		updates = append(updates, updateOne)
		switch userItems[i].Type {
		case TypeRebateGift:
			if g == nil {
				g, err = mgift.FindGift(s.Gift.GiftID)
				if err != nil {
					return false, 0, nil, err
				}
				if g == nil {
					logger.WithFields(logger.Fields{
						"gift_id": s.Gift.GiftID,
					}).Error("user item gift not found")
					return false, 0, nil, nil
				}
			}
			if userItems[i].TransactionID == 0 {
				rebateGiftNum -= numInc // numInc 是负数
			} else {
				gc = append(gc, userapi.Gift{
					ID:      s.Gift.GiftID,
					Title:   g.Name,
					Price:   s.Gift.Price,
					Num:     -numInc, // numInc 是负数
					Context: userItems[i].Context,
				})
			}
		}
	}

	transactionIDs := make([]int64, 0, 2)
	err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		res, sessErr := Collection().BulkWrite(ctx, updates)
		if sessErr != nil {
			return sessErr
		}
		if res.ModifiedCount != int64(len(updates)) {
			return errGiftNum
		}
		// TODO: 将订单传给 missevan-app
		if rebateGiftNum != 0 {
			rebateResp, sessErr := userapi.SendRebateGift(s.UserID, s.CreatorID,
				s.Gift.GiftID, int(rebateGiftNum), s.OpenLogID, userapi.NewUserContext(s.C))
			if sessErr != nil {
				return sessErr
			}
			transactionIDs = append(transactionIDs, rebateResp.TransactionID)
		}

		if len(gc) != 0 {
			rpcResp, sessErr := userapi.SendRebateGifts(s.UserID, s.CreatorID,
				gc, s.OpenLogID, userapi.NewUserContext(s.C))
			if sessErr != nil {
				return sessErr
			}
			transactionIDs = append(transactionIDs, rpcResp.TransactionID)
		}
		return nil
	})
	if err != nil {
		if err == errGiftNum {
			return false, countBefore, nil, nil
		}
		logger.Error(err) // 刚使用 mongodb 的事务，重点专注下
		return false, countBefore, nil, err
	}
	return true, countBefore - s.Num, transactionIDs, nil
}

// lockSendGift 返回 unlock 函数，返回 nil 说明设置共享锁超时
func lockSendGift(userID, giftID int64) (unlock func()) {
	key := keys.LockUserBackpackGift2.Format(userID, giftID)
	times := 10 // 请求次数
	for i := 0; i < 10; i++ {
		ok, err := service.Redis.SetNX(key, "1", 10*time.Second).Result()
		if err != nil {
			logger.Error(err)
			return nil
		}
		if ok {
			return func() {
				err := service.Redis.Del(key).Err()
				if err != nil {
					logger.Error(err)
				}
			}
		}
		if i != times-1 {
			// 最后一次不需要等待
			<-time.After(100 * time.Millisecond) // 100 毫秒后再次尝试
		}
	}
	return nil
}

// CountGiftNum 计算某礼物数量
func CountGiftNum(userID, giftID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	type m bson.M
	cur, err := Collection().Aggregate(ctx, []m{
		{
			"$match": setValidTimeFilter(bson.M{
				"type":    m{"$in": []int{TypeFreeGift, TypeRebateGift}},
				"user_id": userID,
				"gift_id": giftID,
				"num":     m{"$gt": 0},
			}, now.Unix()),
		},
		{
			"$group": m{"_id": 1, "count": m{"$sum": "$num"}},
		},
	})
	if err != nil {
		return 0, nil
	}
	defer cur.Close(ctx)
	var count struct {
		Count int64 `bson:"count"`
	}
	if cur.Next(ctx) {
		err = cur.Decode(&count)
		if err != nil {
			return 0, err
		}
	}
	return count.Count, nil
}

// UnsetGift 清空礼物
func UnsetGift(userID, giftID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	type m bson.M
	_, err := Collection().UpdateMany(ctx, setValidTimeFilter(bson.M{
		"type":    m{"$in": []int{TypeFreeGift, TypeRebateGift}},
		"user_id": userID,
		"gift_id": giftID,
		"num":     m{"$ne": 0}}, now.Unix()),
		m{"$set": m{"num": 0, "modified_time": now.Unix()}})
	return err
}

// AddCatFoods 添加猫粮和猫罐头
func AddCatFoods(userID int64) (bool, error) {
	var (
		now = goutil.TimeNow()
		st  = util.BeginningOfDay(now)
		et  = st.Unix() + util.SecondOneDay

		newKeyTime int64  // 使用新 redis 猫粮是否发放的 KEY 的时间
		key        string // 猫粮是否发放的 KEY
	)
	config.GetAB("enable_new_redis_key_duration_time", &newKeyTime)
	if newKeyTime <= now.Unix() {
		// use new key
		key = keys.KeyUsersAwardGiftDaily2.Format(st.Format(util.TimeFormatYMDWithNoSpace), userID)
		ok, err := service.Redis.Get(key).Int()
		if err != nil && !serviceredis.IsRedisNil(err) {
			return false, err
		}
		if goutil.IntToBool(ok) {
			// 今天猫粮已发放
			return false, nil
		}
	} else {
		// use old key
		key = keys.KeyUsersAwardGiftDaily1.Format(st.Unix())
		ok, err := service.Redis.SIsMember(key, userID).Result()
		if err != nil {
			return false, err
		}
		if ok {
			// 今天猫粮已发放
			return false, nil
		}
	}

	// 先查询用户勋章，若该用户没有勋章，则不添加猫粮
	lm, err := livemedal.FindOne(bson.M{
		"user_id": userID,
		"status":  bson.M{"$gt": livemedal.StatusPending}},
		options.FindOne().SetSort(bson.M{"point": -1}))
	if err != nil {
		return false, err
	}
	if lm == nil {
		return false, nil
	}

	gMap, err := gift.FindGiftMapByGiftIDs([]int64{GiftIDCatFood, GiftIDCatCanFood})
	if err != nil {
		return false, err
	}
	if len(gMap) != 2 {
		// 礼物不存在
		return false, fmt.Errorf("gift %d or %d not exists", GiftIDCatFood, GiftIDCatCanFood)
	}

	// 发放猫罐头数量：开通的超粉数量 * 10
	superMedalCount, err := livemedal.CountSuperMedal(userID)
	if err != nil {
		return false, err
	}

	if newKeyTime <= now.Unix() {
		// use new key
		ok, err := service.Redis.SetNX(key, 1, 36*time.Hour).Result()
		if err != nil {
			return false, err
		}
		if !ok {
			// 说明添加失败，已经领取了猫粮
			return false, nil
		}
	} else {
		// use old key
		pipe := service.Redis.TxPipeline()
		cmd := pipe.SAdd(key, userID)
		pipe.Expire(key, 24*time.Hour)
		_, err = pipe.Exec()
		if err != nil {
			return false, err
		}
		if cmd.Val() == 0 {
			// 说明添加失败，已经领取了猫粮
			return false, nil
		}
	}

	adder := NewAdder(userID, 2)

	// 超粉用户获得的 `猫粮` 和 `猫罐头` 的有效期都延长至当周周日的 23:59:59
	if superMedalCount > 0 {
		et = util.BeginningOfWeek(now.Add(7 * 24 * time.Hour)).Unix()
		adder.Append(gMap[GiftIDCatCanFood], 10*superMedalCount, now.Unix(), et)
	}

	count := livemedal.CatFoodAddCount(lm.Level)
	adder.Append(gMap[GiftIDCatFood], count, now.Unix(), et)
	err = adder.Add()
	if err != nil {
		// 失败的时候释放锁
		err1 := service.Redis.SRem(key, userID).Err()
		if err1 != nil {
			logger.Error(err1)
			// PASS
		}
		return false, err
	}
	return true, nil
}

// AddCatCanFood 添加猫罐头
func AddCatCanFood(userID int64, count, st, et int64) error {
	gift, err := gift.FindShowingGiftByGiftID(GiftIDCatCanFood)
	if err != nil {
		return err
	}
	if gift == nil {
		return fmt.Errorf("gift %d not exists", GiftIDCatCanFood)
	}
	err = AddGiftToUsers([]int64{userID}, gift, count, SourceNormal, st, et)
	return err
}
