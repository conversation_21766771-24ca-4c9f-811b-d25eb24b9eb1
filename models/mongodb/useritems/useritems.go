package useritems

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 物品类型
const (
	TypeFreeGift   = iota + 3 // 免费礼物，值同礼物类型
	TypeRebateGift            // 白给礼物，根据获取方式可能有订单号，值同礼物类型

	TypeItem = 101 // 道具
)

// 物品来源
const (
	SourceNormal = iota // 正常业务逻辑和活动发放
	SourceAdmin         // 后台发放
)

// UserItem 背包物品
type UserItem struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	Type      int   `bson:"type"`
	Source    int   `bson:"source"`
	UserID    int64 `bson:"user_id"`
	StartTime int64 `bson:"start_time"`
	EndTime   int64 `bson:"end_time"`

	ItemID  int64 `bson:"item_id,omitempty"` // 道具 ID
	GiftID  int64 `bson:"gift_id,omitempty"`
	Num     int64 `bson:"num"`
	GainNum int64 `bson:"gain_num"`

	TransactionID int64  `bson:"transaction_id,omitempty"`
	Context       string `bson:"context,omitempty"`
}

// IsBackpackGift 该礼物是否能下发到背包
func IsBackpackGift(g *gift.Gift) bool {
	return g.Type == gift.TypeFree || g.Type == gift.TypeRebate
}

// TypeFromGiftType 礼物类型转物品类型
func TypeFromGiftType(giftType int) int {
	switch giftType {
	case gift.TypeFree:
		return TypeFreeGift
	case gift.TypeRebate:
		return TypeRebateGift
	default:
		panic(fmt.Sprintf("不支持的礼物类型: %d", giftType))
	}
}

// Collection returns collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("user_items")
}

// Find 列出物品
func Find(filter interface{}, opt *options.FindOptions) ([]*UserItem, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx, filter, opt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var items []*UserItem
	err = cur.All(ctx, &items)
	if err != nil {
		return nil, err
	}
	return items, nil
}

// setValidTimeFilter 为 mongodb 的 filter 设置有效时间的判断
func setValidTimeFilter(filter bson.M, nowUnix int64) bson.M {
	filter["start_time"] = bson.M{"$lte": nowUnix}
	filter["end_time"] = bson.M{"$gt": nowUnix}
	return filter
}

// SetUserGiftEndTime 设置延长用户背包有效期内的礼物的有效时间
func SetUserGiftEndTime(userID, giftID int64, endTime time.Time) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	nowUnix := goutil.TimeNow().Unix()
	_, err := Collection().UpdateMany(ctx,
		bson.M{
			"user_id":    userID,
			"gift_id":    giftID,
			"num":        bson.M{"$gt": 0},
			"start_time": bson.M{"$lte": nowUnix}, // 根据 start_time 和 end_time 判断，仅修改在有效期内且原结束时间 < 待更新时间的礼物
			"end_time":   bson.M{"$lt": endTime.Unix(), "$gte": nowUnix},
		},
		bson.M{"$set": bson.M{
			"end_time":      endTime.Unix(),
			"modified_time": nowUnix,
		}})
	return err
}

// ListExpireBackpackUserIDs 查询过期背包礼物要过期的用户
func ListExpireBackpackUserIDs(when time.Time, excludeGiftIDs []int64) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"type":       bson.M{"$in": []int{TypeFreeGift, TypeRebateGift}},
		"num":        bson.M{"$gt": 0},
		"start_time": bson.M{"$lt": when.Add(-10 * time.Minute).Unix()}, // 过滤执行周期 10min 前获取的 24h 过期礼物
		"end_time": bson.M{
			"$gte": when.Add(24 * time.Hour).Add(-10 * time.Minute).Unix(), // 执行周期 10 分钟
			"$lt":  when.Add(24 * time.Hour).Unix(),
		}}
	if len(excludeGiftIDs) > 0 {
		filter["gift_id"] = bson.M{"$nin": excludeGiftIDs}
	}
	cur, err := Collection().Aggregate(ctx, []bson.M{
		{"$match": filter},
		{"$group": bson.M{
			"_id": "$user_id",
		}},
	})
	if err != nil {
		return nil, err
	}
	var res []struct {
		UserID int64 `bson:"_id"`
	}
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	userIDs := make([]int64, 0, len(res))
	for i := range res {
		userIDs = append(userIDs, res[i].UserID)
	}
	return userIDs, nil
}
