package useritems

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBackpackItemTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(BackpackItem{}, "type", "item_id", "item_type", "gift_id", "comboable", "price", "num", "allowed_nums",
		"name", "icon_url", "icon_active_url", "intro", "intro_icon_url", "intro_open_url", "label_icon_url", "time_left",
		"trial_noble")
}

func initBackpackGift() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	g := gift.Gift{
		GiftID:    301,
		Name:      "猫粮",
		NameClean: "猫粮",
		Icon:      "gifts/icons/007.png",
		Type:      gift.TypeFree,
		Point:     1,
		Order:     301,
		AddedTime: time.Unix(0, 0),
	}
	err := gift.Collection().FindOneAndUpdate(ctx, bson.M{"gift_id": 301},
		bson.M{"$set": &g}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil {
		logger.Error(err)
	}

	g = gift.Gift{
		GiftID:    302,
		Name:      "夏有凉风",
		NameClean: "夏有凉风",
		Icon:      "gifts/icons/302.png",
		LabelIcon: "oss://live/gifts/label/icons/302.png",
		Type:      gift.TypeFree,
		Point:     1,
		Order:     302,
		AddedTime: time.Unix(0, 0),
	}
	err = gift.Collection().FindOneAndUpdate(ctx, bson.M{"gift_id": 302},
		bson.M{"$set": &g}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	if err != nil {
		logger.Error(err)
	}
}

func TestFindBackpackItems(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)

	var (
		now = goutil.TimeNow()

		testGiftID          = int64(302)
		testItemID          = int64(1)
		testItemLiveMedalID = int64(2)
		testUserID          = int64(9998)
	)
	initBackpackGift()

	service.Cache5Min.Set(keys.KeyVipList1.Format(vip.TypeLiveTrialNoble), []vip.Info{
		{
			VipID: 9,
			Level: 1,
		},
	}, time.Hour)
	g, err := gift.FindShowingGiftByGiftID(testGiftID)
	require.NoError(err)
	require.NotNil(g)
	require.NoError(AddGiftToUsers([]int64{testUserID}, g, 10, SourceNormal, now.Unix(), now.Unix()+1000))

	for _, itemID := range []int64{testItemID, testItemLiveMedalID} {
		bpItem, err := backpackitem.FindOneItem(itemID)
		require.NoError(err)
		require.NotNil(bpItem)
		require.NoError(AddItemToUsers([]int64{testUserID}, bpItem, 10, SourceNormal, now.Unix(), now.Unix()+1000))
	}

	findGift := func(bp []BackpackItem) *BackpackItem {
		for i := range bp {
			if bp[i].GiftID == testGiftID {
				return &bp[i]
			}
		}
		return nil
	}
	findItem := func(bp []BackpackItem, itemID int64) *BackpackItem {
		for i := range bp {
			if bp[i].ItemID == itemID {
				return &bp[i]
			}
		}
		return nil
	}
	{
		e := &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.Android,
			AppVersion: "6.0.7",
		}
		bp, err := FindBackpackItems(e, testUserID)
		require.NoError(err)

		gift := findGift(bp)
		require.NotNil(gift)
		require.EqualValues(testGiftID, gift.GiftID)
		assert.GreaterOrEqual(gift.Num, int64(10))
		assert.NotContains(gift.IconURL, "web")
		assert.NotEmpty(gift.LabelIcon)

		item := findItem(bp, testItemID)
		require.NotNil(item)
		require.EqualValues(testItemID, item.ItemID)
		assert.GreaterOrEqual(item.Num, int64(10))
		assert.NotContains(item.IconURL, "web")
		assert.NotEmpty(item.LabelIcon)

		// 客户端版本 < 6.3.8 不展示新增的道具类型
		itemLiveMedal := findItem(bp, testItemLiveMedalID)
		assert.Nil(itemLiveMedal)
	}

	{
		// 测试非 App 请求
		e := &goutil.Equipment{
			FromApp: false,
			OS:      goutil.Web,
		}
		bp, err := FindBackpackItems(e, testUserID)
		require.NoError(err)

		gift := findGift(bp)
		require.NotNil(gift)
		require.EqualValues(testGiftID, gift.GiftID)
		assert.GreaterOrEqual(gift.Num, int64(10))
		assert.NotContains(gift.IconURL, "web")
		assert.NotEmpty(gift.LabelIcon)

		item := findItem(bp, testItemID)
		require.NotNil(item)
		require.EqualValues(testItemID, item.ItemID)
		assert.GreaterOrEqual(item.Num, int64(10))
		assert.NotContains(item.IconURL, "web")
		assert.NotEmpty(item.LabelIcon)

		itemLiveMedal := findItem(bp, testItemLiveMedalID)
		require.NotNil(itemLiveMedal)
		assert.EqualValues(testItemLiveMedalID, itemLiveMedal.ItemID)
	}

	{
		e := &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.Android,
			AppVersion: "6.0.6",
		}
		bp, err := FindBackpackItems(e, testUserID)
		require.NoError(err)
		gift := findGift(bp)
		require.NotNil(gift)
		require.EqualValues(testGiftID, gift.GiftID)
		assert.GreaterOrEqual(gift.Num, int64(10))
		assert.NotContains(gift.IconURL, "web")
		assert.NotEmpty(gift.LabelIcon)
		item := findItem(bp, testItemID)
		require.Nil(item)
	}

	{
		e := &goutil.Equipment{
			FromApp:    true,
			OS:         goutil.Android,
			AppVersion: "6.3.8",
		}
		bp, err := FindBackpackItems(e, testUserID)
		require.NoError(err)
		item := findItem(bp, testItemLiveMedalID)
		require.NotNil(item)
		assert.EqualValues(testItemLiveMedalID, item.ItemID)
	}
}

func TestFindGiftInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gm, err := findGiftInfo([]int64{1, 2, 3})
	require.NoError(err)
	assert.Equal(3, len(gm))

	gm, err = findGiftInfo([]int64{})
	require.NoError(err)
	assert.Empty(gm)
}

func TestFindItemInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Set(keys.KeyVipList1.Format(vip.TypeLiveTrialNoble), []vip.Info{{VipID: 12, Title: "aaa"}}, 0)
	defer service.Cache5Min.Flush()

	itemMap, err := findItemInfo([]int64{1})
	require.NoError(err)
	assert.Equal(1, len(itemMap))
	require.NotNil(itemMap[1])
	require.NotNil(itemMap[1].trialNoble)
	assert.Equal("aaa", itemMap[1].trialNoble.Title)

	itemMap, err = findItemInfo([]int64{})
	require.NoError(err)
	assert.Empty(itemMap)
}
