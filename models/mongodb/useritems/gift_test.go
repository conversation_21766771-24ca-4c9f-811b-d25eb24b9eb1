package useritems

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func clearCatFoods(t *testing.T, userID int64) {
	require := require.New(t)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	bod := util.BeginningOfDay(goutil.TimeNow())

	_, err := Collection().DeleteMany(ctx, bson.M{
		"user_id":    userID,
		"gift_id":    bson.M{"$in": []int64{GiftIDCatFood, GiftIDCatCanFood}},
		"start_time": bson.M{"$gte": bod.Unix()},
	})
	require.NoError(err)
}

func TestNewTransactionAdder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 初始化
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1234567890, 0)
	})
	defer goutil.SetTimeNow(nil)
	adder := NewTransactionAdder(135, 123, "test", 2)
	require.Equal(&Adder{
		userID:        135,
		transactionID: 123,
		context:       "test",
		now:           goutil.TimeNow(),
		Adds:          []UserItem{},
	}, adder)

	// 增加待添加礼物
	g := &gift.Gift{GiftID: 456, Type: gift.TypeRebate}
	adder = adder.Append(g, 1, 123, 999)
	require.Len(adder.Adds, 1)
	assert.Equal(TypeRebateGift, adder.Adds[0].Type)

	// 添加礼物到数据库
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	_, err := col.DeleteMany(ctx, bson.M{"user_id": adder.userID, "gift_id": g.GiftID})
	require.NoError(err)
	require.NoError(adder.Add())
	assert.Empty(adder.Adds)
	require.NoError(adder.Add(), "测试空数据情况")
	assert.NoError(col.FindOne(ctx, bson.M{"user_id": adder.userID, "gift_id": g.GiftID}).Err())
}

func TestGiftSenderSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(147852369)
	giftID := int64(23)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	tid := int64(1234)
	now := goutil.TimeNow().Unix()
	inserts := []interface{}{
		UserItem{
			Type:      TypeRebateGift,
			UserID:    userID,
			GiftID:    giftID,
			Num:       10,
			StartTime: now - 86400,
			EndTime:   now + 86400*2,
		},
		UserItem{
			Type:          TypeRebateGift,
			UserID:        userID,
			GiftID:        giftID,
			Num:           10,
			StartTime:     now - 86400,
			EndTime:       now + 86400,
			TransactionID: tid,
			Context:       "{}",
		},
	}
	_, err := col.DeleteMany(ctx, bson.M{"user_id": userID, "gift_id": giftID})
	require.NoError(err)
	res, err := col.InsertMany(ctx, inserts)
	require.NoError(err)

	// num 负数
	sender := GiftSender{
		UserID:    userID,
		RoomID:    123,
		CreatorID: 456,
		Gift:      &gift.Gift{GiftID: giftID},
		Num:       -100,
	}
	sender.C = goutil.SmartUserContext{
		Req: httptest.NewRequest(http.MethodPost, "/live/send-rebate-gift", nil),
	}
	assert.PanicsWithValue("num must greater than 0", func() {
		_, _, _, _ = sender.Send()
	})

	// 数量太多
	sender.Num = 99999
	ok, count, _, err := sender.Send()
	require.NoError(err)
	assert.False(ok)
	assert.Equal(int64(20), count)

	// 正常送礼
	cancel = mrpc.SetMock(userapi.URISendRebateGift, func(interface{}) (interface{}, error) {
		tid++
		return userapi.BalanceResp{
			TransactionID: tid,
		}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(userapi.URISendRebateGifts, func(interface{}) (interface{}, error) {
		tid++
		return userapi.BalanceResp{
			TransactionID: tid,
		}, nil
	})
	defer cancel()
	sender.Num = 15
	ok, count, tids, err := sender.Send()
	require.NoError(err)
	require.True(ok)
	assert.Equal(int64(5), count)
	assert.Equal([]int64{1235, 1236}, tids)
	// 查看送礼是否正常
	var li []UserItem
	cur, err := col.Find(ctx, bson.M{"_id": bson.M{"$in": res.InsertedIDs}})
	require.NoError(err)
	defer cur.Close(ctx)
	require.NoError(cur.All(ctx, &li))
	assert.Equal(int64(5), li[0].Num)
	assert.Zero(li[1].Num)
}

func TestCountGiftNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 和 TestGiftSenderSend 共用测试数据
	userID := int64(147852369)
	giftID := int64(23)

	num, err := CountGiftNum(userID, giftID)
	require.NoError(err)
	assert.Equal(int64(5), num)

	num, err = CountGiftNum(userID, giftID+1)
	require.NoError(err)
	assert.Zero(num)
}

func TestUnsetGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(147852369)
	giftID := int64(-23)

	require.NoError(UnsetGift(userID, giftID))

	num, err := CountGiftNum(userID, giftID)
	require.NoError(err)
	assert.Zero(num)
}

func TestAddCatFoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2023, 7, 2, 0, 0, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return when
	})
	defer goutil.SetTimeNow(nil)

	var (
		userID = int64(12)
		bod    = util.BeginningOfDay(goutil.TimeNow())
	)

	// mock userSuperMedal
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{
		"user_id": userID,
	})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, bson.M{
		"user_id": userID,
		"status":  livemedal.StatusOwned,
		"super_fan": bson.M{
			"expire_time": goutil.TimeNow().Add(24 * time.Hour).Unix(),
		},
	})
	require.NoError(err)

	testAddCatFoods := func() {
		// 领取猫粮
		ok, err := AddCatFoods(userID)
		require.NoError(err)
		require.True(ok)

		ok, err = AddCatFoods(userID)
		require.NoError(err)
		require.False(ok)
		userItems, err := Find(bson.M{
			"user_id":    userID,
			"gift_id":    bson.M{"$in": []int64{GiftIDCatFood, GiftIDCatCanFood}},
			"start_time": bson.M{"$gte": bod.Unix()},
		}, nil)
		require.NoError(err)
		assert.Len(userItems, 2)
		m, ok := goutil.ToMap(userItems, "GiftID").(map[int64]*UserItem)
		require.True(ok)
		et := time.Unix(goutil.TimeNow().Unix()+7*util.SecondOneDay, 0)
		et = util.BeginningOfWeek(et)
		assert.Equal(et.Unix(), m[GiftIDCatFood].EndTime)
		assert.Equal(et.Unix(), m[GiftIDCatCanFood].EndTime)
	}

	// 测试新 key
	config.Conf.AB["enable_new_redis_key_duration_time"] = when.Add(-time.Hour).Unix()
	defer delete(config.Conf.AB, "enable_new_redis_key_duration_time")
	clearCatFoods(t, userID)
	key := keys.KeyUsersAwardGiftDaily2.Format(bod.Format(util.TimeFormatYMDWithNoSpace), userID)
	require.NoError(service.Redis.Del(key).Err())
	testAddCatFoods()

	// 测试旧 key
	config.Conf.AB["enable_new_redis_key_duration_time"] = when.Add(time.Hour).Unix()
	clearCatFoods(t, userID)
	key = keys.KeyUsersAwardGiftDaily1.Format(bod.Unix())
	require.NoError(service.Redis.Del(key).Err())
	testAddCatFoods()
}

func TestAddCatCanFood(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var userID int64 = 12
	clearCatFoods(t, userID)
	now := goutil.TimeNow()
	bod := util.BeginningOfDay(now)
	et := util.BeginningOfWeek(now.Add(7 * 24 * time.Hour)).Unix()
	err := AddCatCanFood(userID, 10, now.Unix(), et)
	require.NoError(err)
	userItems, err := Find(bson.M{
		"user_id":    userID,
		"gift_id":    GiftIDCatCanFood,
		"start_time": bson.M{"$gte": bod.Unix()},
	}, nil)
	require.NoError(err)
	require.Len(userItems, 1)
	assert.Equal(int64(10), userItems[0].Num)
}

func TestAddGiftToUserAndUpdateEndTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	testGiftID := int64(30014)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "gift_id": testGiftID})
	require.NoError(err)

	now := goutil.TimeNow()
	insert := make([]interface{}, 3)
	insert[0] = UserItem{
		UserID:  testUserID,
		GiftID:  testGiftID,
		Num:     1,
		EndTime: now.Add(time.Minute).Unix(),
	}
	insert[1] = UserItem{
		UserID:  testUserID,
		GiftID:  testGiftID,
		Num:     0,
		EndTime: now.Add(time.Minute).Unix(),
	}
	insert[2] = UserItem{
		UserID:  testUserID,
		GiftID:  testGiftID,
		Num:     1,
		EndTime: now.Add(-time.Minute).Unix(),
	}
	_, err = Collection().InsertMany(ctx, insert)
	require.NoError(err)

	g := &gift.Gift{
		GiftID: testGiftID,
		Type:   gift.TypeFree,
	}
	endTime := now.Add(2 * time.Minute).Unix()
	require.NoError(AddGiftToUserAndUpdateEndTime(testUserID, g, 1, endTime, endTime))

	userItems, err := Find(bson.M{
		"user_id":  testUserID,
		"gift_id":  testGiftID,
		"end_time": bson.M{"$gte": endTime},
	}, nil)
	require.NoError(err)
	require.Len(userItems, 2)
	for _, item := range userItems {
		assert.Equal(endTime, item.EndTime)
	}
}
