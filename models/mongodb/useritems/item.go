package useritems

import (
	"context"
	"errors"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// AddItemToUsers 为多个用户添加道具卡，可用于给单个用户发道具卡
// startTime 和 endTime 为道具卡的有效期，单位：秒
func AddItemToUsers(userIDs []int64, item *backpackitem.LiveBackpackItem, num int64, source int, startTime, endTime int64) error {
	inserts := make([]interface{}, len(userIDs))
	nowUnix := goutil.TimeNow().Unix()
	for i := range userIDs {
		inserts[i] = &UserItem{
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			Type:         TypeItem,
			Source:       source,
			UserID:       userIDs[i],
			StartTime:    startTime,
			EndTime:      endTime,
			ItemID:       item.ID,
			Num:          num,
			GainNum:      num,
		}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, inserts)
	return err
}

// CountItemNum 查找用户背包中的道具数量
func CountItemNum(userID, itemID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	type m bson.M
	cur, err := Collection().Aggregate(ctx, []m{
		{
			"$match": setValidTimeFilter(bson.M{
				"type":    TypeItem,
				"user_id": userID,
				"item_id": itemID,
				"num":     m{"$gt": 0},
			}, goutil.TimeNow().Unix()),
		},
		{
			"$group": m{"_id": 1, "count": m{"$sum": "$num"}},
		},
	})
	if err != nil {
		return 0, err
	}
	var count []struct {
		Count int64 `bson:"count"`
	}
	err = cur.All(ctx, &count)
	if err != nil {
		return 0, err
	}
	if len(count) == 0 {
		return 0, nil
	}
	return count[0].Count, nil
}

// ItemUseTrigger 使用道具回调函数
type ItemUseTrigger interface {
	// Execute 扣减道具事务内执行的业务自定义的回调方法，如果该方法返回 error 会回滚道具扣减操作
	Execute() error
}

// ItemUseLiveMedalTrigger 使用粉丝勋章兑换卡回调
type ItemUseLiveMedalTrigger struct {
	RoomOID    primitive.ObjectID
	RoomID     int64
	CreatorID  int64
	MedalName  string
	UserID     int64
	ItemID     int64
	Num        int64
	UV         *vip.UserVip
	IsRoomOpen bool

	MedalUpdatedInfo *livemedal.MedalUpdatedInfo
}

// Execute 发放粉丝牌
func (iu *ItemUseLiveMedalTrigger) Execute() (err error) {
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    iu.RoomOID,
		RoomID:     iu.RoomID,
		CreatorID:  iu.CreatorID,
		UserID:     iu.UserID,
		UV:         iu.UV,
		MedalName:  iu.MedalName,
		Type:       livemedal.TypeTrialCardMedalPoint,
		Source:     livemedal.ChangeSourceTrialCard,
		PointAdd:   livemedal.MinContribution,
		Scene:      livemedalpointlog.SceneTypeTrialCard,
		IsRoomOpen: iu.IsRoomOpen,
	}
	iu.MedalUpdatedInfo, err = medalParam.AddPoint()
	return
}

// ItemUseTrailNobleTrigger 使用贵族体验卡回调
type ItemUseTrailNobleTrigger struct {
	UserContext   mrpc.UserContext
	UserID        int64
	CreatorID     int64
	ItemID        int64
	Duration      int64
	Num           int64
	LiveOpenLogID *primitive.ObjectID
	NewTrialNoble *vip.Info // 当前需要新开通的体验贵族信息

	BuyNewTrialNoble *vip.UserVip // 购买成功后发放的贵族信息
}

// Execute 发放贵族体验卡
func (iu *ItemUseTrailNobleTrigger) Execute() (err error) {
	var openLogID string
	if iu.LiveOpenLogID != nil && !iu.LiveOpenLogID.IsZero() {
		openLogID = iu.LiveOpenLogID.Hex()
	}
	iu.BuyNewTrialNoble, err = vip.BuyLiveTrialNoble(iu.UserContext, &vip.BuyTrialNobleRequestParam{
		VipID:         iu.NewTrialNoble.VipID,
		UserID:        iu.UserID,
		CreatorID:     iu.CreatorID,
		Num:           iu.Num,
		Duration:      iu.Duration,
		LiveOpenLogID: openLogID,
		IP:            iu.UserContext.IP,
		UserAgent:     iu.UserContext.UserAgent,
		EquipID:       iu.UserContext.EquipID,
		BUVID:         iu.UserContext.BUVID,
	})
	return
}

// Use 使用道具
func Use(userID, itemID, useNum int64, cb ItemUseTrigger) (ok bool, remain int64, err error) {
	now := goutil.TimeNow().Unix()
	userItems, err := Find(setValidTimeFilter(bson.M{
		"user_id": userID,
		"item_id": itemID,
		"num":     bson.M{"$gt": 0},
	}, now), options.Find().SetSort(bson.M{"end_time": 1}))
	if err != nil {
		return
	}
	var allCount int64
	for i := range userItems {
		allCount += userItems[i].Num
	}
	if allCount < useNum {
		return
	}

	updates := make([]mongo.WriteModel, 0, len(userItems))
	num := useNum // 待使用的道具数量
	for i := 0; i < len(userItems) && num > 0; i++ {
		// numInc 减少的数量
		// NOTICE: numInc 是负数
		numInc := -userItems[i].Num
		if num < userItems[i].Num {
			numInc = -num
		}
		num += numInc // 减少待送出的礼物数量
		updateOne := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": userItems[i].OID, "num": bson.M{"$gte": -numInc}}).
			SetUpdate(bson.M{
				"$inc": bson.M{"num": numInc},
				"$set": bson.M{"modified_time": now},
			})
		updates = append(updates, updateOne)
	}

	err = mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		res, err := Collection().BulkWrite(ctx, updates)
		if err != nil {
			return err
		}
		if res.ModifiedCount != int64(len(updates)) {
			return errors.New("item num is not enough")
		}

		if err := cb.Execute(); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return false, allCount, err
	}
	return true, allCount - useNum, nil
}

// UnsetItem 清空道具
func UnsetItem(userID, itemID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	nowUnix := goutil.TimeNow().Unix()
	_, err := Collection().UpdateMany(ctx, setValidTimeFilter(bson.M{
		"type":    TypeItem,
		"user_id": userID,
		"item_id": itemID,
		"num":     bson.M{"$ne": 0}}, nowUnix),
		bson.M{"$set": bson.M{"num": 0, "modified_time": nowUnix}})
	if err != nil {
		return err
	}
	return nil
}
