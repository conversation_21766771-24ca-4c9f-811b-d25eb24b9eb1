package useritems

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/vip"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 背包中的物品类型
const (
	BpItemTypeGift = iota + 1 // 背包物品礼物
	BpItemTypeItem            // 背包物品道具
)

// 注册的礼物 ID
const (
	GiftIDCatFood    = int64(301)
	GiftIDCatCanFood = int64(30005) // 猫罐头

	GiftID30001 = int64(30001) // 免费礼物 6k 热度卡
	GiftID30002 = int64(30002) // 免费礼物 10k 热度卡
	GiftID30003 = int64(30003) // 免费礼物 3k 热度卡
	GiftID30006 = int64(30006) // 免费礼物 守护之力
	GiftID30007 = int64(30007) // 免费礼物 1k 热度卡
	GiftID30008 = int64(30008) // 免费礼物 30k 热度卡
	GiftID40001 = int64(40001) // 白给礼物幻雪冰城
	GiftID40002 = int64(40002) // 白给礼物招财喵
)

// BackpackItem 直播用户在背包中看到的物品
// REVIEW: 貌似现在没有主播能看见的物品？
type BackpackItem struct {
	Type int `json:"type"`

	ItemID   int64 `json:"item_id,omitempty"`
	ItemType int   `json:"item_type,omitempty"`

	GiftID    int64 `json:"gift_id,omitempty"`
	Comboable int   `json:"comboable,omitempty"`
	Price     int64 `json:"price"`

	Num         int64 `json:"num"`
	AllowedNums []int `json:"allowed_nums,omitempty"`

	Name          string `json:"name"`
	IconURL       string `json:"icon_url"`
	IconActiveURL string `json:"icon_active_url,omitempty"`
	Intro         string `json:"intro"`
	IntroIconURL  string `json:"intro_icon_url,omitempty"`
	IntroOpenURL  string `json:"intro_open_url,omitempty"`
	LabelIcon     string `json:"label_icon_url,omitempty"`
	TimeLeft      int64  `json:"time_left"` // 剩余时间，单位：秒

	TrialNoble *TrialNoble `json:"trial_noble,omitempty"` // 仅当道具为贵族体验卡时有值
}

// TrialNoble 贵族体验卡贵族信息
type TrialNoble struct {
	Level   int    `json:"level"`
	Title   string `json:"title"`
	IconURL string `json:"icon_url"`
}

// backpackItemInfo 背包中的道具（包含体验卡信息）
type backpackItemInfo struct {
	*backpackitem.LiveBackpackItem
	trialNoble *TrialNoble
}

// FindBackpackItems 查找用户背包物品
func FindBackpackItems(e *goutil.Equipment, userID int64) ([]BackpackItem, error) {
	now := goutil.TimeNow().Unix()
	userItems, err := Find(setValidTimeFilter(bson.M{
		"user_id": userID,
		"num":     bson.M{"$gt": 0},
	}, now), options.Find().SetSort(bson.D{
		// 按照过期时间排序，再按照礼物 ID 排序
		{Key: "end_time", Value: 1},
		{Key: "gift_id", Value: 1},
		{Key: "item_id", Value: 1},
	}))
	if err != nil {
		return nil, err
	}
	// WORKAROUND: 安卓 < 6.0.7 和 iOS < 6.0.7 版本和 Web 不返回背包道具
	isOldApp := e.IsOldApp(goutil.AppVersions{
		IOS:     "6.0.7",
		Android: "6.0.7",
	})

	// WORKAROUND: 安卓 < 6.3.8 和 iOS < 6.3.8 版本只能展示贵族体验卡道具
	isOnlyShowNoble := e.IsOldApp(goutil.AppVersions{
		IOS:     "6.3.8",
		Android: "6.3.8",
	})

	// 目前认为礼物不会超过 20 个
	var (
		giftIDs   = make([]int64, 0, 20)
		itemIDs   = make([]int64, 0, 20)
		itemGifts = make(map[int64]*UserItem, 20)
		itemItems = make(map[int64]*UserItem, 20)
	)
	for i := range userItems {
		switch userItems[i].Type {
		case TypeFreeGift, TypeRebateGift:
			// 数量聚合在一起，过期时间返回最早的过期时间
			g := itemGifts[userItems[i].GiftID]
			if g == nil {
				giftIDs = append(giftIDs, userItems[i].GiftID)
				itemGifts[userItems[i].GiftID] = userItems[i]
				continue
			}
			g.Num += userItems[i].Num
			if g.EndTime > userItems[i].EndTime {
				g.EndTime = userItems[i].EndTime
			}
		case TypeItem:
			if isOldApp {
				continue
			}
			// 数量聚合在一起，过期时间返回最早的过期时间，同上方背包礼物
			item := itemItems[userItems[i].ItemID]
			if item == nil {
				itemIDs = append(itemIDs, userItems[i].ItemID)
				itemItems[userItems[i].ItemID] = userItems[i]
				continue
			}
			item.Num += userItems[i].Num
			item.EndTime = goutil.MinInt64(item.EndTime, userItems[i].EndTime)
		}
	}

	// 查询礼物信息
	giftMap, err := findGiftInfo(giftIDs)
	if err != nil {
		return nil, err
	}
	// 查询道具信息
	itemMap, err := findItemInfo(itemIDs)
	if err != nil {
		return nil, err
	}
	addedGiftMap := make(map[int64]struct{}, len(giftIDs))
	addedItemMap := make(map[int64]struct{}, len(itemIDs))
	res := make([]BackpackItem, 0, len(giftIDs)+len(itemIDs))
	for _, userItem := range userItems {
		switch userItem.Type {
		case TypeFreeGift, TypeRebateGift:
			if _, ok := addedGiftMap[userItem.GiftID]; ok {
				continue
			}
			addedGiftMap[userItem.GiftID] = struct{}{}
			g := giftMap[userItem.GiftID]
			if g == nil {
				continue
			}
			ig := itemGifts[userItem.GiftID] // ig 是 nil 说明代码逻辑有问题
			item := BackpackItem{
				Type: BpItemTypeGift,

				GiftID:    g.GiftID,
				Comboable: g.Comboable,
				Price:     g.Price,

				Num:         ig.Num,
				AllowedNums: g.AllowedNums,

				Name:         g.Name,
				Intro:        g.Intro,
				IntroIconURL: g.IntroIcon,
				IntroOpenURL: g.IntroOpenURL,
				LabelIcon:    g.LabelIcon,
				TimeLeft:     ig.EndTime - now,

				IconURL:       g.Icon,
				IconActiveURL: g.IconActive,
			}
			res = append(res, item)
		case TypeItem:
			if isOldApp {
				continue
			}
			if _, ok := addedItemMap[userItem.ItemID]; ok {
				continue
			}
			addedItemMap[userItem.ItemID] = struct{}{}
			item := itemMap[userItem.ItemID]
			if item == nil {
				continue
			}
			if item.Type != backpackitem.TypeNobleTrialCard && isOnlyShowNoble {
				continue
			}
			ii := itemItems[userItem.ItemID] // ii 是 nil 说明代码逻辑有问题
			backpackItem := BackpackItem{
				Type:          BpItemTypeItem,
				ItemID:        ii.ItemID,
				ItemType:      item.Type,
				Num:           ii.Num,
				AllowedNums:   item.MoreInfo.AllowedNums,
				Name:          item.Name,
				Intro:         item.Intro,
				IntroIconURL:  item.IntroIconURL,
				IntroOpenURL:  item.IntroOpenURL,
				LabelIcon:     item.LabelIconURL,
				TimeLeft:      ii.EndTime - now,
				IconURL:       item.IconURL,
				IconActiveURL: item.IconActiveURL,
			}
			switch backpackItem.ItemType {
			case backpackitem.TypeNobleTrialCard:
				backpackItem.TrialNoble = item.trialNoble
			}
			res = append(res, backpackItem)
		}
	}

	return res, nil
}

func findGiftInfo(giftIDs []int64) (map[int64]*gift.Gift, error) {
	if len(giftIDs) == 0 {
		return nil, nil
	}

	return gift.FindGiftMapByGiftIDs(giftIDs)
}

func findItemInfo(itemIDs []int64) (map[int64]*backpackItemInfo, error) {
	if len(itemIDs) == 0 {
		return nil, nil
	}

	items, err := backpackitem.FindByIDs(itemIDs)
	if err != nil {
		return nil, err
	}
	vips, err := vip.ListByVipType(vip.TypeLiveTrialNoble)
	if err != nil {
		return nil, err
	}
	vipMap := goutil.ToMap(vips, "VipID").(map[int64]*vip.Info)
	backpackItems := make([]*backpackItemInfo, 0, len(items))
	for _, item := range items {
		bi := &backpackItemInfo{
			LiveBackpackItem: item,
		}
		switch item.Type {
		case backpackitem.TypeNobleTrialCard:
			bi.trialNoble = &TrialNoble{
				IconURL: item.MoreInfo.IconURL,
			}
			if vipInfo, ok := vipMap[item.MoreInfo.VipID]; ok {
				bi.trialNoble.Title = vipInfo.Title
				bi.trialNoble.Level = vipInfo.Level
			}
		}
		backpackItems = append(backpackItems, bi)
	}
	return goutil.ToMap(backpackItems, "ID").(map[int64]*backpackItemInfo), nil
}
