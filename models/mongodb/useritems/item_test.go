package useritems

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAddItemToUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(123)
		testItemID = int64(1)
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID}, nil)
	require.NoError(err)

	err = AddItemToUsers([]int64{testUserID}, &backpackitem.LiveBackpackItem{ID: testItemID}, 1111, SourceNormal, 0, 0)
	require.NoError(err)
	var ui UserItem
	err = Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&ui)
	require.NoError(err)
	assert.EqualValues(testItemID, ui.ItemID)
	assert.EqualValues(1111, ui.Num)
}

func TestCountItemNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1321123)
		testItemID = int64(1)
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 8, 31, 11, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertOne(ctx, UserItem{
		Type:      TypeItem,
		UserID:    testUserID,
		ItemID:    testItemID,
		Num:       1,
		StartTime: goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		EndTime:   goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	})
	require.NoError(err)

	count, err := CountItemNum(testUserID, testItemID)
	require.NoError(err)
	assert.NotZero(count)

	res, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	assert.NotZero(res.DeletedCount)
	count, err = CountItemNum(testUserID, testItemID)
	require.NoError(err)
	assert.Zero(count)
}

func TestUseTrailNobleItem(t *testing.T) {
	// 测试使用贵族体验卡道具
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1321123)
		testItemID = int64(1)
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 8, 31, 11, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	cleanup := mrpc.SetMock(vip.URLBuyLiveTrialNoble, func(any) (any, error) {
		return handler.M{
			"vip_info": &vip.Info{VipID: 12, Level: 4},
		}, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = Collection().InsertOne(ctx, UserItem{
		Type:      TypeItem,
		UserID:    testUserID,
		ItemID:    testItemID,
		Num:       3,
		StartTime: goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		EndTime:   goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	})
	require.NoError(err)

	useNum := int64(2)
	cb := &ItemUseTrailNobleTrigger{
		UserContext:   mrpc.NewUserContextFromEnv(),
		UserID:        testUserID,
		ItemID:        testItemID,
		Duration:      int64(24 * time.Hour.Seconds()),
		Num:           useNum,
		NewTrialNoble: &vip.Info{VipID: 12, Level: 4},
	}
	ok, remain, err := Use(testUserID, testItemID, useNum, cb)
	require.NoError(err)
	require.True(ok)
	assert.EqualValues(1, remain)
	require.NotNil(cb.BuyNewTrialNoble)
	// 检查发放的贵族体验卡和用户实际得到的贵族体验卡
	assert.Equal(cb.NewTrialNoble.VipID, cb.BuyNewTrialNoble.VipID)
	assert.Equal(cb.NewTrialNoble.Level, cb.BuyNewTrialNoble.Level)
}

func TestUnsetItem(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1321123)
		testItemID = int64(1)
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	err = AddItemToUsers([]int64{testUserID}, &backpackitem.LiveBackpackItem{ID: testItemID}, 1111, SourceNormal, 0, 0)
	require.NoError(err)

	err = UnsetItem(testUserID, testItemID)
	require.NoError(err)

	count, err := CountItemNum(testUserID, testItemID)
	require.NoError(err)
	assert.Zero(count)
}

func TestUseTrailNobleItemTrgger(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 准备测试数据
	iu := &ItemUseTrailNobleTrigger{
		UserContext: mrpc.UserContext{
			IP:        "127.0.0.1",
			UserAgent: "test-agent",
			EquipID:   "equip123",
			BUVID:     "buvid123",
		},
		UserID:        1002,
		CreatorID:     2002,
		ItemID:        3002,
		Duration:      7200,
		Num:           2,
		LiveOpenLogID: nil,
		NewTrialNoble: &vip.Info{
			VipID: 10,
		},
	}

	var (
		callRPC      bool
		callRPCParam *vip.BuyTrialNobleRequestParam
	)
	cancel := mrpc.SetMock(vip.URLBuyLiveTrialNoble, func(input any) (any, error) {
		callRPC = true
		p, ok := input.(*vip.BuyTrialNobleRequestParam)
		require.True(ok)

		callRPCParam = p
		return handler.M{
			"vip_info": iu.NewTrialNoble,
		}, nil
	})
	defer cancel()
	err := iu.Execute()
	require.NoError(err)
	require.True(callRPC)
	assert.Equal(iu.NewTrialNoble.VipID, callRPCParam.VipID)
	assert.Equal(iu.UserID, callRPCParam.UserID)

	// rpc 接口返回值更新了 iu.BuyNewTrialNoble
	assert.Equal(iu.BuyNewTrialNoble.VipID, iu.NewTrialNoble.VipID)
}

func TestUseLiveMedalItemTrigger(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 准备测试数据
	iu := &ItemUseLiveMedalTrigger{
		RoomID:    1,
		CreatorID: 2,
		MedalName: "测试",
		UserID:    11,
		ItemID:    2,
		Num:       1,
	}

	err := iu.Execute()
	require.NoError(err)
	assert.NotNil(iu.MedalUpdatedInfo)
}
