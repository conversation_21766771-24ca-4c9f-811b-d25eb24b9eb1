package useritems

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestItemTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)

	kc.Check(UserItem{}, "_id", "create_time", "modified_time",
		"type", "source", "user_id", "start_time", "end_time",
		"item_id", "gift_id", "num", "gain_num", "transaction_id", "context")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(3, TypeFreeGift)
	assert.Equal(4, TypeRebateGift)
	assert.Equal(101, TypeItem)
}

func TestIsBackpackGift(t *testing.T) {
	assert := assert.New(t)

	g := &gift.Gift{Type: gift.TypeNormal}
	assert.False(IsBackpackGift(g))
	g.Type = gift.TypeFree
	assert.True(IsBackpackGift(g))
	g.Type = gift.TypeRebate
	assert.True(IsBackpackGift(g))
}

func TestTypeFromGiftType(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(TypeFreeGift, TypeFromGiftType(gift.TypeFree))
	assert.Equal(TypeRebateGift, TypeFromGiftType(gift.TypeRebate))

	assert.PanicsWithValue("不支持的礼物类型: -1", func() {
		TypeFromGiftType(-1)
	})
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userItems, err := Find(bson.M{}, nil)
	require.NoError(err)
	require.NotEmpty(userItems)

	li2, err := Find(bson.M{"_id": userItems[0].OID}, options.Find().SetProjection(bson.M{"type": 1}))
	require.NoError(err)
	require.Len(li2, 1)
	assert.Equal(userItems[0].OID, li2[0].OID)
	assert.Equal(userItems[0].Type, li2[0].Type)
	assert.Zero(li2[0].GiftID)
}

func TestSetValidTimeFilter(t *testing.T) {
	assert := assert.New(t)

	f1 := bson.M{}
	nowUnix := int64(123)
	f2 := setValidTimeFilter(f1, nowUnix)
	assert.Equal(f1, f2)
	assert.Equal(bson.M{"$lte": nowUnix}, f1["start_time"])
	assert.Equal(bson.M{"$gt": nowUnix}, f1["end_time"])
}

func TestSetUserGiftEndTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(123)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": testUserID, "gift_id": GiftIDCatFood}
	_, err := Collection().DeleteMany(ctx, filter, nil)
	require.NoError(err)

	now := goutil.TimeNow()
	nowUnix := now.Unix()
	err = AddGiftToUsers([]int64{testUserID}, &gift.Gift{
		GiftID: GiftIDCatFood,
		Type:   TypeFreeGift,
	}, 10, SourceNormal, nowUnix, now.Add(2*time.Second).Unix())
	require.NoError(err)

	et := now.Add(10 * time.Second)
	err = SetUserGiftEndTime(testUserID, GiftIDCatFood, et)
	require.NoError(err)
	res, err := Find(filter, nil)
	require.NoError(err)
	require.Len(res, 1)
	assert.Equal(et.Unix(), res[0].EndTime)
}

func TestListExpireBackpackUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testTime := time.Date(2020, 02, 01, 0, 0, 0, 0, time.Local)
	cancel := goutil.SetTimeNow(func() time.Time {
		return testTime
	})
	defer cancel()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{
		"type": bson.M{"$in": []int{TypeFreeGift, TypeRebateGift}},
		"end_time": bson.M{
			"$gte": testTime.Add(24 * time.Hour).Add(-10 * time.Minute).Unix(),
			"$lt":  testTime.Add(24 * time.Hour).Unix(),
		},
	}, nil)
	require.NoError(err)

	userIDs, err := ListExpireBackpackUserIDs(testTime, nil)
	require.NoError(err)
	assert.Empty(userIDs)

	err = AddGiftToUsers([]int64{1, 2, 3}, &gift.Gift{
		Type:   TypeFreeGift,
		GiftID: GiftIDCatFood,
	}, 1, SourceNormal, testTime.Add(-1*time.Second).Unix(), testTime.Add(24*time.Hour).Add(-10*time.Minute).Unix())
	require.NoError(err)
	userIDs, err = ListExpireBackpackUserIDs(testTime, nil)
	require.NoError(err)
	assert.Empty(userIDs)

	err = AddGiftToUsers([]int64{1, 2, 3}, &gift.Gift{
		Type:   TypeFreeGift,
		GiftID: GiftIDCatFood,
	}, 1, SourceNormal, testTime.Add(-11*time.Minute).Unix(), testTime.Add(24*time.Hour).Add(-10*time.Minute).Unix())
	require.NoError(err)
	userIDs, err = ListExpireBackpackUserIDs(testTime, nil)
	require.NoError(err)
	assert.Len(userIDs, 3)

	err = AddGiftToUsers([]int64{1, 2, 3}, &gift.Gift{
		Type:   TypeFreeGift,
		GiftID: GiftIDCatFood,
	}, 1, SourceNormal, testTime.Add(-11*time.Minute).Unix(), testTime.Add(24*time.Hour).Add(-10*time.Minute).Unix())
	require.NoError(err)
	userIDs, err = ListExpireBackpackUserIDs(testTime, []int64{GiftIDCatFood})
	require.NoError(err)
	assert.Empty(userIDs)
}
