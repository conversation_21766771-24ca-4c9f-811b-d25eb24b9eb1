package quests

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// UserQuest 用户完成任务详情
type UserQuest struct {
	OID primitive.ObjectID `bson:"_id,omitempty"`

	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`
	QuestOID     primitive.ObjectID `bson:"_quest_id"`

	UserID int64 `bson:"user_id"`
}

// CollectionUserQuests collection user_quests
func CollectionUserQuests() *mongo.Collection {
	return service.MongoDB.Collection("user_quests")
}

// FinishRoomQuestParam 用户完成房间任务参数
type FinishRoomQuestParam struct {
	UserID    int64
	StartTime time.Time
	CallTime  time.Time
	LastTime  time.Time

	// TODO: 后续改为使用 Room 中的字段
	RoomID    int64
	CreatorID int64

	// 目前只有全局直播间连续收听时长任务使用
	Room *room.Room
}

// ViewRoom 完成任务访问直播间
func (param *FinishRoomQuestParam) ViewRoom() {
	qs, err := FindCurrentQuests(QuestTypeRoomView)
	if err != nil {
		logger.Error(err)
		return
	}
	for i := range qs {
		if param.RoomID == qs[i].RoomID {
			qs[i].finishRoom(param)
		}
	}
}

// ListenRoomDuration 完成直播间连续收听时长任务（离开再进要重新统计）
func (param *FinishRoomQuestParam) ListenRoomDuration() {
	quests, err := FindCurrentQuests(QuestTypeRoomListenDuration)
	if err != nil {
		logger.Error(err)
		return
	}

	for _, quest := range quests {
		if param.isListenRoomDurationReached(quest) {
			quest.finishRoom(param)
		}
	}
}

// isListenRoomDurationReached 直播间连续收听时长是否满足（离开再进要重新统计）
func (param *FinishRoomQuestParam) isListenRoomDurationReached(quest *Quest) bool {
	// 没有配置房间 ID 为全局任务
	if quest.RoomID != 0 && param.RoomID != quest.RoomID {
		return false
	}

	duration := param.CallTime.Unix() - goutil.MaxInt64(quest.StartTime, param.StartTime.Unix())
	return duration >= quest.Duration
}
