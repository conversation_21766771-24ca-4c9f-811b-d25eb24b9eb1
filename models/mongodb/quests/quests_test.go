package quests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestQuestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)

	kc.Check(Quest{}, "_id", "create_time", "modified_time", "type", "start_time",
		"end_time", "intro", "reward_id", "event_id", "duration", "daily", "repeat_count", "room_id", "rank", "user_rank_reward_id_map")
}

func TestFindQuests(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 查询所有任务
	qs, err := FindQuests(bson.M{})
	require.NoError(err)
	assert.Greater(len(qs), 0)
}

func TestFindCurrentQuests(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 使用不存在的 type 查询
	testType := 9
	qs, err := FindCurrentQuests(testType)
	require.NoError(err)
	assert.NotNil(qs)
	assert.Empty(qs)

	now := goutil.TimeNow()
	key := keys.LocalKeyActivatedQuests1.Format(9)
	service.Cache10s.SetDefault(key, []*Quest{
		{
			StartTime: now.Unix(),
			EndTime:   now.Unix() + 10,
		},
		{
			StartTime: now.Unix() + 10,
			EndTime:   now.Unix() + 20,
		},
	})
	qs, err = FindCurrentQuests(testType)
	require.NoError(err)
	assert.Equal(1, len(qs), "len(q)")

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := CollectionQuests()

	testType = 8
	q := Quest{
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		Type:         testType,
		StartTime:    now.Unix(),
		EndTime:      now.Unix() + 10,
		Intro:        "TestFindCurrentQuests",
		RewardID:     16,
		RoomID:       12346,
	}
	_, err = col.UpdateOne(ctx, bson.M{"type": testType}, bson.M{"$set": q},
		options.Update().SetUpsert(true))
	require.NoError(err)
	qs, err = FindCurrentQuests(testType)
	require.NoError(err)
	assert.NotEmpty(qs)
}

func TestFindHourQuests(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := time.Unix(1705656051, 0)
	quest, err := FindHourQuests(now)
	require.NoError(err)
	assert.Equal(3, len(quest))
	for _, q := range quest {
		assert.Equal(now.Unix(), q.StartTime)
		assert.Equal(QuestTypeHourTop, q.Type)
	}
}

func TestQuest_InTimeRange(t *testing.T) {
	assert := assert.New(t)

	q := Quest{
		StartTime: 1,
		EndTime:   3,
	}
	expated := []bool{false, true, true, false}
	for i := int64(0); i < 4; i++ {
		now := time.Unix(i, 0)
		assert.Equal(expated[i], q.InTimeRange(now))
	}
}

func TestQuest_Duplicate(t *testing.T) {
	assert := assert.New(t)

	q1 := Quest{Type: 1}
	q2 := q1.duplicate()
	assert.Equal(&Quest{Type: 1}, q2)
	q2.Type = 2
	assert.Equal(1, q1.Type)
}

func TestFilterCurrentQuest(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	qs := []*Quest{
		{
			StartTime: now.Unix(),
			EndTime:   now.Unix() + 10,
		},
		{
			StartTime: now.Unix() + 10,
			EndTime:   now.Unix() + 20,
		},
	}
	assert.Equal([]*Quest{
		{
			StartTime: now.Unix(),
			EndTime:   now.Unix() + 10,
		},
	}, filterCurrentQuest(qs, now))
}

func TestQuest_finishRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)

	reward, err := reward.FindRewardByRewardIDWithCache(reward.RewardID1)
	require.NoError(err)
	require.NotNil(reward)

	q := Quest{
		OID:      oid,
		RoomID:   123,
		RewardID: reward.RewardID,
		reward:   reward,
	}
	userIDs := []int64{12, 9074508}
	testCreatorID := int64(12)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := CollectionUserQuests()
	filter := bson.M{
		"_quest_id": q.OID,
		"user_id":   bson.M{"$in": userIDs},
	}
	_, err = col.DeleteMany(ctx, filter)
	require.NoError(err)

	param := &FinishRoomQuestParam{
		UserID:    userIDs[0],
		CreatorID: testCreatorID,
	}
	q.finishRoom(param) // 第一次完成
	count, err := col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(1, count)
	q.finishRoom(param) // 第二次不完成
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(1, count)

	// 测试每日刷新
	q.Daily = true
	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now.AddDate(0, 0, 1)
	})
	defer goutil.SetTimeNow(nil)
	q.finishRoom(param) // 第一次完成
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(2, count)

	q.finishRoom(param) // 第二次不完成
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(2, count)

	called := false
	cancel1 := mrpc.SetMock(userapi.URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		require.NotNil(body)
		called = true
		return "success", nil
	})
	defer cancel1()

	q.RoomID = 0
	q.Type = QuestTypeRoomListenDuration
	q.Daily = false
	q.Duration = 10
	room := &room.Room{
		Helper: room.Helper{
			RoomID:            123556,
			CreatorID:         9074509,
			GuildID:           123,
			ActivityCatalogID: 12,
		},
	}
	param = &FinishRoomQuestParam{
		UserID:    userIDs[1],
		CreatorID: testCreatorID,
		Room:      room,
		CallTime:  now,
		StartTime: now.Add(-10 * time.Second),
		LastTime:  now,
	}
	q.finishRoom(param) // 第一次完成
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(3, count)
	assert.True(called)

	called = false
	param.CallTime = param.CallTime.Add(1 * time.Second)
	q.finishRoom(param) // 第二次不完成
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(3, count)
	assert.False(called)

	var calledNum int
	cancel = mrpc.SetMock(userapi.URLMinigameExchange, func(input interface{}) (output interface{}, err error) {
		calledNum++
		return "success", nil
	})
	defer cancel()
	_, err = col.DeleteMany(ctx, filter)
	require.NoError(err)
	q.EventID = 566
	q.RepeatCount = 2
	param.CallTime = now.Add(20 * time.Second) // 测试一次完成多次任务
	q.finishRoom(param)
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(2, count)
	assert.Equal(2, calledNum)

	// 测试可重复完成任务
	_, err = col.DeleteMany(ctx, filter)
	require.NoError(err)
	param.CallTime = now.Add(10 * time.Second)
	q.finishRoom(param)
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(1, count)
	assert.Equal(3, calledNum)

	q.finishRoom(param)
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(2, count)
	assert.Equal(4, calledNum)

	// 测试任务已全部完成
	param.CallTime = param.CallTime.Add(10 * time.Second)
	q.finishRoom(param)
	count, err = col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(2, count)
	assert.Equal(4, calledNum)
}

func TestQuest_FinishHourTop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := time.Unix(1705656051, 0)
	qs, err := FindHourQuests(now)
	require.NoError(err)
	require.EqualValues(3, len(qs))

	q := qs[0]
	_, err = CollectionUserQuests().DeleteMany(ctx, bson.M{
		"_quest_id": q.OID,
	})
	require.NoError(err)

	q.FinishHourTop(1, 1, nil)
	count, err := CollectionUserQuests().CountDocuments(ctx, bson.M{
		"_quest_id": q.OID,
	})
	require.NoError(err)
	assert.EqualValues(1, count)

	q.FinishHourTop(1, 0, nil)
	count, err = CollectionUserQuests().CountDocuments(ctx, bson.M{
		"_quest_id": q.OID,
	})
	require.NoError(err)
	assert.EqualValues(2, count)

	q.FinishHourTop(1, 0, []*roomsrank.Info{{ID: 1, Rank: 1}})
	count, err = CollectionUserQuests().CountDocuments(ctx, bson.M{
		"_quest_id": q.OID,
	})
	require.NoError(err)
	assert.EqualValues(3, count)

	q.userRankRewardMap = map[int]*reward.Reward{1: q.reward, 2: q.reward}
	q.FinishHourTop(1, 0, []*roomsrank.Info{{ID: 1, Rank: 1}})
	count, err = CollectionUserQuests().CountDocuments(ctx, bson.M{
		"_quest_id": q.OID,
	})
	require.NoError(err)
	assert.EqualValues(5, count)
}

func TestQuest_sendRankEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		require.NotNil(body)
		called = true
		return "success", nil
	})
	defer cancel()

	room := &room.Room{
		Helper: room.Helper{
			RoomID:            123556,
			CreatorID:         9074509,
			GuildID:           123,
			ActivityCatalogID: 12,
		},
	}
	q := Quest{
		OID:      primitive.NewObjectID(),
		Duration: 180,
	}
	require.NoError(q.sendRankEvent(9074510, room))
	assert.True(called)
}

func TestInsertQuests(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomIDs := []int64{9074510, 9074509}
	col := CollectionQuests()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"room_id": bson.M{"$in": roomIDs},
	}
	_, err := col.DeleteMany(ctx, filter)
	require.NoError(err)

	quests := make([]any, 0, 2)
	for _, v := range roomIDs {
		quests = append(quests, Quest{
			RoomID: v,
		})
	}
	require.NoError(InsertQuests(quests))

	count, err := col.CountDocuments(ctx, filter)
	require.NoError(err)
	assert.EqualValues(len(roomIDs), count)
}

func TestFindQuestByOID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := CollectionQuests()

	// 查找不存在的任务
	_, err = col.DeleteOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)
	quest, err := FindQuestByOID(oid)
	require.NoError(err)
	assert.Nil(quest)

	// 查找存在的任务
	_, err = col.InsertOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)

	quest, err = FindQuestByOID(oid)
	require.NoError(err)
	assert.Equal(oid.Hex(), quest.OID.Hex())
}

func TestDelQuestByOID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := CollectionQuests()

	// 删除不存在的任务
	_, err = col.DeleteOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)
	ok, err := DelQuestByOID(oid)
	assert.NoError(err)
	assert.False(ok)

	// 删除存在的任务
	var q Quest
	_, err = col.InsertOne(ctx, bson.M{
		"_id": oid,
	})
	require.NoError(err)
	ok, err = DelQuestByOID(oid)
	require.NoError(err)
	assert.True(ok)
	err = col.FindOne(ctx, bson.M{
		"_id": oid,
	}).Decode(&q)
	assert.True(mongodb.IsNoDocumentsError(err))
}

func TestQuest_IsFinished(t *testing.T) {
	assert := assert.New(t)

	q := Quest{
		StartTime: 1,
		EndTime:   3,
	}
	expated := []bool{false, false, false, true, true}
	for i := int64(0); i <= 4; i++ {
		cancel := goutil.SetTimeNow(func() time.Time {
			return time.Unix(i, 0)
		})
		assert.Equal(expated[i], q.IsFinished())
		cancel()
	}
}
