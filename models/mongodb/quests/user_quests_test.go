package quests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUserQuestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)

	kc.Check(UserQuest{}, "_id", "create_time", "modified_time", "_quest_id", "user_id")
}

func TestFinishRoomQuestParam_ViewRoom(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	key := keys.LocalKeyActivatedQuests1.Format(QuestTypeRoomView)
	service.Cache10s.SetDefault(key, []*Quest{
		{
			StartTime: now.Unix(),
			EndTime:   now.Unix() + 10,
		},
	})
	f := FinishRoomQuestParam{
		RoomID:    123,
		CreatorID: 456,
		UserID:    789,
	}
	assert.NotPanics(func() { f.ViewRoom() })
}

func TestFinishRoomQuestParam_ListenRoomDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URILiveReward, func(input interface{}) (output interface{}, err error) {
		called = true
		return handler.M{"success": true}, nil
	})
	defer cancel()

	key := keys.LocalKeyActivatedQuests1.Format(QuestTypeRoomListenDuration)
	service.Cache10s.Delete(key)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	qCol := CollectionQuests()
	now := goutil.TimeNow()
	testOID, _ := primitive.ObjectIDFromHex("6613b4bc29f21e142e80a55c")
	testQuest := &Quest{
		OID:       testOID,
		Type:      QuestTypeRoomListenDuration,
		RoomID:    18113499,
		RewardID:  8,
		Duration:  10,
		StartTime: now.Unix(),
		EndTime:   now.Add(time.Minute).Unix(),
	}
	err := qCol.FindOneAndUpdate(ctx, bson.M{"_id": testOID},
		bson.M{"$set": testQuest},
		options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(err)

	uqCol := CollectionUserQuests()
	_, err = uqCol.DeleteMany(ctx, bson.M{"_quest_id": testOID,
		"user_id": bson.M{"$in": []int64{9074508, 9074509}}})
	require.NoError(err)

	// 测试收听时长达标发放奖励
	f := FinishRoomQuestParam{
		RoomID:    testQuest.RoomID,
		CreatorID: 9074508,
		UserID:    9074509,
		StartTime: now.Add(-time.Minute),
		CallTime:  now.Add(10 * time.Second),
	}
	f.ListenRoomDuration()
	err = uqCol.FindOne(ctx, bson.M{
		"_quest_id": testOID,
		"user_id":   f.UserID,
	}).Err()
	require.NoError(err)
	assert.True(called)

	// 测试收听时长不符合不放奖励
	called = false
	f.UserID = 9074510
	f.CallTime = now.Add(9 * time.Second)
	f.ListenRoomDuration()
	err = uqCol.FindOne(ctx, bson.M{
		"_quest_id": testOID,
		"user_id":   f.UserID,
	}).Err()
	assert.True(mongodb.IsNoDocumentsError(err))
	assert.False(called)

	called1 := false
	cancel1 := mrpc.SetMock(userapi.URIRankEvent, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.RankEventParams)
		require.True(ok)
		require.NotNil(body)
		called1 = true
		return "success", nil
	})
	defer cancel1()

	_, err = qCol.UpdateOne(ctx, bson.M{"_id": testOID},
		bson.M{"$set": bson.M{"reward_id": 0, "room_id": 0}})
	require.NoError(err)
	service.Cache10s.Delete(key)

	// 测试不限制房间达标发放奖励
	f.CreatorID = 9074509
	f.UserID = 9074508
	f.CallTime = now.Add(10 * time.Minute)
	f.Room = &room.Room{
		Helper: room.Helper{
			RoomID:            123556,
			CreatorID:         9074509,
			GuildID:           123,
			ActivityCatalogID: 12,
		},
	}
	f.ListenRoomDuration()
	err = uqCol.FindOne(ctx, bson.M{
		"_quest_id": testOID,
		"user_id":   f.UserID,
	}).Err()
	assert.False(mongodb.IsNoDocumentsError(err))
	assert.True(called1)
}

func TestFinishRoomQuestParam_isListenRoomDurationReached(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	quest := Quest{
		RoomID:    123456,
		StartTime: now.Unix(),
		Duration:  5,
	}
	param := FinishRoomQuestParam{
		RoomID:    123,
		StartTime: now.Add(time.Second),
		CallTime:  now.Add(time.Second),
	}
	// 测试房间号不是任务房间号
	assert.False(param.isListenRoomDurationReached(&quest))

	// 测试收听开始时间在任务开始之后，收听时长不满足
	param.RoomID = 123456
	assert.False(param.isListenRoomDurationReached(&quest))

	// 测试收听开始时间在任务开始之前，收听时长不满足
	param.StartTime = now.Add(-time.Second)
	param.CallTime = now.Add(time.Second)
	assert.False(param.isListenRoomDurationReached(&quest))

	// 测试收听开始时间在任务开始之前，收听时长满足
	param.CallTime = now.Add(6 * time.Second)
	assert.True(param.isListenRoomDurationReached(&quest))

	// 测试收听开始时间在任务开始之后，收听时长满足
	param.StartTime = now.Add(time.Second)
	assert.True(param.isListenRoomDurationReached(&quest))

	// 测试没有限制房间，收听时长满足
	quest.RoomID = 0
	param.RoomID = 12123
	assert.True(param.isListenRoomDurationReached(&quest))
}
