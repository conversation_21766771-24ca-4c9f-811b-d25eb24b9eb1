package quests

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/activity/rankevent"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// 任务类型
const (
	QuestTypeRoomView           = iota + 1 // 访问直播间
	QuestTypeHourTop                       // 获得小时榜 TopN
	QuestTypeRoomListenDuration            // 直播间连续收听时长（离开再进要重新统计）
)

// Quest 任务
type Quest struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	Type        int    `bson:"type"`                   // 任务类型
	StartTime   int64  `bson:"start_time"`             // 任务开始时间，单位：秒
	EndTime     int64  `bson:"end_time"`               // 任务结束时间，单位：秒
	Intro       string `bson:"intro,omitempty"`        // 任务说明
	RewardID    int64  `bson:"reward_id"`              // 奖励 ID
	EventID     int64  `bson:"event_id,omitempty"`     // 活动 ID
	Duration    int64  `bson:"duration,omitempty"`     // 直播间连续收听任务时长，单位：秒
	Daily       bool   `bson:"daily,omitempty"`        // 每日任务
	RepeatCount int64  `bson:"repeat_count,omitempty"` // 可重复次数（若是每日任务则表示每日可完成次数，否则表示总共可完成次数）

	// 访问直播间任务相关
	RoomID int64 `bson:"room_id,omitempty"`

	// 小时榜排名
	Rank int `bson:"rank,omitempty"`

	// 小时榜用户助力榜奖励
	UserRankRewardIDMap map[int]int64 `bson:"user_rank_reward_id_map,omitempty"`

	reward            *reward.Reward
	userRankRewardMap map[int]*reward.Reward
}

// CollectionQuests collection quests
func CollectionQuests() *mongo.Collection {
	return service.MongoDB.Collection("quests")
}

// InTimeRange 在时间范围内
func (q Quest) InTimeRange(now time.Time) bool {
	nowUnix := now.Unix()
	return q.StartTime <= nowUnix && nowUnix < q.EndTime
}

func (q Quest) duplicate() *Quest {
	q2 := q
	return &q2
}

func (q Quest) finishRoom(param *FinishRoomQuestParam) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	uqCol := CollectionUserQuests()
	filter := bson.M{
		"_quest_id": q.OID,
		"user_id":   param.UserID,
	}
	// 是否每日刷新
	if q.Daily {
		filter["create_time"] = bson.M{
			"$gte": util.BeginningOfDay(now).Unix(),
			"$lt":  util.BeginningOfDay(now).Add(24 * time.Hour).Unix(),
		}
	}
	count, err := uqCol.CountDocuments(ctx, filter)
	if err != nil {
		logger.WithFields(logger.Fields{
			"quest_id": q.OID.Hex(),
			"user_id":  param.UserID,
		}).Error(err)
		return
	}
	// NOTICE: RepeatCount 为 0 时表示只可完成 1 次，大于 0 时表示可重复完成的次数
	if (q.RepeatCount == 0 && count >= 1) || (q.RepeatCount > 0 && count >= q.RepeatCount) {
		// 当前任务已全部完成
		return
	}

	// 当前完成次数
	curFinishedCount := int64(1)
	if q.Type == QuestTypeRoomListenDuration {
		if q.RepeatCount > 0 {
			// 任务可能会完成多次（任务 6min 触发一次，如单次任务连续观看时长是 3min，此时一次会完成 2 次任务）
			startTimeUnix := max(param.StartTime.Unix(), q.StartTime)
			curFinishedCount = (param.CallTime.Unix()-startTimeUnix)/q.Duration - max((param.LastTime.Unix()-startTimeUnix)/q.Duration, 0)
			if curFinishedCount <= 0 {
				// 未满足任务条件
				return
			}
			curFinishedCount = min(curFinishedCount, q.RepeatCount-count)
		}

		if q.RoomID == 0 {
			for i := int64(0); i < curFinishedCount; i++ {
				// 用于完成直播间连续收听时长全局任务后调用活动事件
				if q.EventID != 0 {
					err = userapi.SendActivityExchange(mrpc.NewUserContextFromEnv(), param.UserID, q.EventID)
				} else {
					err = q.sendRankEvent(param.UserID, param.Room)
				}
				if err != nil {
					logger.WithFields(logger.Fields{
						"quest_id": q.OID.Hex(),
						"user_id":  param.UserID,
					}).Error(err)
					// 如果调用失败了可以重复调用
					return
				}
			}
		}
	}

	uqs := make([]any, 0, curFinishedCount)
	for i := int64(0); i < curFinishedCount; i++ {
		uqs = append(uqs, UserQuest{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			QuestOID:     q.OID,
			UserID:       param.UserID,
		})
	}
	_, err = uqCol.InsertMany(ctx, uqs)
	if err != nil {
		logger.WithFields(logger.Fields{
			"quest_id": q.OID.Hex(),
			"user_id":  param.UserID,
		}).Error(err)
		return
	}
	if q.reward != nil {
		for i := int64(0); i < curFinishedCount; i++ {
			err = q.reward.Send(q.RoomID, param.CreatorID, param.UserID)
			if err != nil {
				logger.WithFields(logger.Fields{
					"quest_id": q.OID.Hex(),
					"user_id":  param.UserID,
				}).Error(err)
				return
			}
		}
	}
}

// FinishHourTop 完成小时榜任务
func (q Quest) FinishHourTop(roomID, creatorID int64, roomUsersRank []*roomsrank.Info) {
	if roomID == 0 {
		// 未找到直播间
		return
	}
	// 完成小时榜任务
	q.finishAndSendReward(roomID, []int64{creatorID}, []*reward.Reward{q.reward})
	if len(roomUsersRank) == 0 {
		// 没有助力榜
		return
	}
	// 批量完成助力榜任务
	roomUsersRankMap := util.ToMap(roomUsersRank, func(r *roomsrank.Info) int64 {
		return r.Rank
	})
	userIDs := make([]int64, 0, len(q.userRankRewardMap))
	rewards := make([]*reward.Reward, 0, len(q.userRankRewardMap))
	for rank, r := range q.userRankRewardMap {
		roomUserRank, ok := roomUsersRankMap[int64(rank)]
		if !ok {
			continue
		}
		userIDs = append(userIDs, roomUserRank.ID)
		rewards = append(rewards, r)
	}
	if len(userIDs) == 0 {
		// 没有助力榜任务
		return
	}
	q.finishAndSendReward(roomID, userIDs, rewards)
}

func (q Quest) finishAndSendReward(roomID int64, userIDs []int64, rewards []*reward.Reward) {
	now := goutil.TimeNow()
	inserts := make([]any, 0, len(userIDs))
	for _, userID := range userIDs {
		inserts = append(inserts, UserQuest{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			QuestOID:     q.OID,
			UserID:       userID,
		})
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	uqCol := CollectionUserQuests()
	_, err := uqCol.InsertMany(ctx, inserts)
	if err != nil {
		logger.WithFields(logger.Fields{
			"quest_id": q.OID.Hex(),
			"user_ids": userIDs,
		}).Error(err)
		return
	}
	for i, userID := range userIDs {
		err = rewards[i].Send(roomID, userID, userID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"quest_id": q.OID.Hex(),
				"user_id":  userID,
			}).Error(err)
			// PASS
		}
	}
}

func (q Quest) sendRankEvent(userID int64, _ *room.Room) error {
	return rankevent.NewSyncCommonParam(userID).
		// 全局任务暂时不需要传当前完成任务的房间信息，后续有需要再开
		// SetRoomInfo(room.RoomID, room.CreatorID, room.GuildID, room.ActivityCatalogID).
		Online(q.OID.Hex(), q.Duration).Send(mrpc.NewUserContextFromEnv())
}

// IsFinished 判断是否已结束
func (q Quest) IsFinished() bool {
	nowUnix := goutil.TimeNow().Unix()
	return q.EndTime <= nowUnix
}

// FindQuests 查询任务
func FindQuests(filter bson.M, options ...*options.FindOptions) ([]*Quest, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := CollectionQuests().Find(ctx, filter, options...)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var qs []*Quest
	err = cur.All(ctx, &qs)
	if err != nil {
		return nil, err
	}

	rewardIDs := make([]int64, 0, len(qs))
	for _, q := range qs {
		if q.RewardID == 0 {
			continue
		}
		rewardIDs = append(rewardIDs, q.RewardID)
		for _, rewardID := range q.UserRankRewardIDMap {
			rewardIDs = append(rewardIDs, rewardID)
		}
	}

	rewardIDs = sets.Uniq(rewardIDs)
	rewardMap := make(map[int64]*reward.Reward, len(rewardIDs))
	for _, r := range rewardIDs {
		rewardMap[r], err = reward.FindRewardByRewardIDWithCache(r)
		if err != nil {
			return nil, err
		}
	}

	res := make([]*Quest, 0, len(qs))
	for _, q := range qs {
		// 直播间连续收听时长任务没有房间 ID 为全局任务，只调用活动事件没有奖励
		if q.Type == QuestTypeRoomListenDuration && q.RoomID == 0 {
			res = append(res, q)
			continue
		}

		r := rewardMap[q.RewardID]
		if r == nil {
			logger.WithFields(logger.Fields{
				"quest_id":  q.OID.Hex(),
				"reward_id": q.RewardID,
			}).Error("奖励不存在")
			continue
		}
		q.reward = r
		q.userRankRewardMap = make(map[int]*reward.Reward, len(q.UserRankRewardIDMap))
		for rank, rewardID := range q.UserRankRewardIDMap {
			r := rewardMap[rewardID]
			if r == nil {
				logger.WithFields(logger.Fields{
					"quest_id":  q.OID.Hex(),
					"reward_id": rewardID,
				}).Error("奖励不存在")
				continue
			}
			q.userRankRewardMap[rank] = r
		}
		res = append(res, q)
	}
	return res, nil
}

// FindCurrentQuests 查询当前生效中的任务
func FindCurrentQuests(questType int) ([]*Quest, error) {
	key := keys.LocalKeyActivatedQuests1.Format(questType)
	v, ok := service.Cache10s.Get(key)
	if ok {
		q := v.([]*Quest)
		return filterCurrentQuest(q, goutil.TimeNow()), nil
	}

	now := goutil.TimeNow()
	res, err := FindQuests(bson.M{
		"type":       questType,
		"start_time": bson.M{"$lte": now.Add(20 * time.Second).Unix()}, // 20s 后开始的任务也一并查询出来
		"end_time":   bson.M{"$gt": now.Unix()},
	})
	if err != nil {
		return nil, err
	}

	service.Cache10s.SetDefault(key, res)
	return filterCurrentQuest(res, now), nil
}

// FindHourQuests 查询小时榜任务
func FindHourQuests(now time.Time) ([]*Quest, error) {
	return FindQuests(bson.M{
		"type":       QuestTypeHourTop,
		"start_time": bson.M{"$lte": now.Unix()},
		"end_time":   bson.M{"$gt": now.Unix()},
	})
}

func filterCurrentQuest(quests []*Quest, now time.Time) []*Quest {
	res := make([]*Quest, 0, len(quests))
	for _, q := range quests {
		if q.InTimeRange(now) {
			res = append(res, q.duplicate())
		}
	}
	return res
}

// InsertQuests 插入任务
func InsertQuests(quests []any) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := CollectionQuests().InsertMany(ctx, quests)
	return err
}

// FindQuestByOID 根据 oid 查询配置
func FindQuestByOID(oid primitive.ObjectID) (*Quest, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var quest Quest
	err := CollectionQuests().FindOne(ctx, bson.M{
		"_id": oid,
	}).Decode(&quest)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &quest, nil
}

// DelQuestByOID 删除配置
func DelQuestByOID(oid primitive.ObjectID) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	result, err := CollectionQuests().DeleteOne(ctx, bson.M{
		"_id": oid,
	})
	if err != nil {
		return false, err
	}
	return result.DeletedCount > 0, nil
}
