package livestatistics

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
)

// LiveStatistics of live_statistics
type LiveStatistics struct {
	OID                 primitive.ObjectID `bson:"_id,omitempty"`
	RoomOID             primitive.ObjectID `bson:"_room_id"`
	RoomID              int64              `bson:"room_id"`
	CatalogID           int64              `bson:"catalog_id"`
	CustomTagID         int64              `bson:"custom_tag_id"`
	Time                int64              `bson:"time"` // 单位：毫秒
	Online              int64              `bson:"online"`
	Accumulation        int64              `bson:"accumulation"`
	Vip                 int64              `bson:"vip"`
	BaseScore           int64              `bson:"base_score"`
	DisplayOnline       int64              `bson:"display_online"`
	DisplayAccumulation int64              `bson:"display_accumulation"`
}

// OnlineStatistics online statistics
type OnlineStatistics struct {
	RoomID        int64 `bson:"room_id"`
	Time          int64 `bson:"time"` // 单位：毫秒
	Online        int64 `bson:"online"`
	DisplayOnline int64 `bson:"display_online"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_statistics")
}
