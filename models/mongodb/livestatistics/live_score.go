package livestatistics

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
)

// FindLiveScore 获取直播热度
func FindLiveScore(roomID int64, startTime, endTime time.Time) (int64, int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	type m bson.M
	pipeline := []m{{
		"$match": m{
			"room_id": roomID,
			"time":    m{"$gt": util.TimeToUnixMilli(startTime), "$lt": util.TimeToUnixMilli(endTime)},
		},
	}, {
		"$group": m{
			"_id":       nil,
			"avg_score": m{"$avg": "$score"},
			"max_score": m{"$max": "$score"},
		},
	}}
	cursor, err := Collection().Aggregate(ctx, pipeline)
	if err != nil {
		return 0, 0, err
	}
	defer cursor.Close(ctx)
	type LiveMedal struct {
		AvgScore float64 `bson:"avg_score"`
		MaxScore int64   `bson:"max_score"`
	}
	var score []*LiveMedal
	if err = cursor.All(ctx, &score); err != nil {
		return 0, 0, err
	}
	if len(score) == 0 {
		return 0, 0, nil
	}
	return int64(score[0].AvgScore), score[0].MaxScore, nil
}
