package livestatistics

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestFindLiveScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	sTime := util.UnixMilliToTime(1621405301000)
	eTime := util.UnixMilliToTime(1621405303000)
	roomID := int64(233333)

	_, err := service.MongoDB.Collection("live_statistics").DeleteMany(ctx, bson.M{
		"room_id": roomID,
		"time":    bson.M{"$gte": util.TimeToUnixMilli(sTime), "$lte": util.TimeToUnixMilli(eTime)},
	})
	require.NoError(err)

	_, err = service.MongoDB.Collection("live_statistics").InsertMany(ctx, []interface{}{
		bson.M{
			// 开始节点，不参与最高热度，平均热度计算
			"room_id":              roomID,
			"time":                 util.TimeToUnixMilli(sTime),
			"online":               0,
			"accumulation":         0,
			"display_accumulation": 0, // 显示的累计人数
			"display_online":       0, // 显示的在线人数
			"score":                0, // 显示的热度
		}, bson.M{
			"room_id":              roomID,
			"time":                 util.TimeToUnixMilli(sTime) + 1,
			"online":               0,
			"accumulation":         0,
			"display_accumulation": 0,  // 显示的累计人数
			"display_online":       0,  // 显示的在线人数
			"score":                10, // 热度
		}, bson.M{
			"room_id":              roomID,
			"time":                 util.TimeToUnixMilli(eTime) - 1,
			"online":               0,
			"accumulation":         0,
			"display_accumulation": 0,  // 显示的累计人数
			"display_online":       0,  // 显示的在线人数
			"score":                20, // 热度
		}, bson.M{
			// 结束节点，不参与最高热度，平均热度计算
			"room_id":              roomID,
			"time":                 util.TimeToUnixMilli(eTime),
			"online":               0,
			"accumulation":         0,
			"display_accumulation": 0, // 显示的累计人数
			"display_online":       0, // 显示的在线人数
			"score":                0, // 显示的热度
		}})
	require.NoError(err)

	avgScore, maxScore, err := FindLiveScore(roomID, sTime, eTime)
	require.NoError(err)
	assert.Equal(int64(15), avgScore)
	assert.Equal(int64(20), maxScore)
}
