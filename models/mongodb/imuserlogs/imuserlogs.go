package imuserlogs

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Log 用户连接使用信息
type Log struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	UserID         int64  `bson:"user_id"`
	GuestID        string `bson:"guest_id,omitempty"`
	ConnectionUUID string `bson:"connection_uuid"` // 服务端生成的连接标识
	JoinUUID       string `bson:"join_uuid"`       // 用户加入房间时的传参
	Status         int    `bson:"status"`          // 1: 高风险状态
	RoomID         int64  `bson:"room_id"`
	IP             string `bson:"ip"`
	UserAgent      string `bson:"user_agent"`
	EquipID        string `bson:"equip_id"`
	BUVID          string `bson:"buvid"`
	RenewTime      int64  `bson:"renew_time"`
}

// IM User Log Status
const (
	StatusNotConnected = iota - 1 // 未连接，只用于缓存，不会存入 MongoDB
	StatusNormal                  // 正常状态
	StatusDisableAllMsg
	StatusDisablePurchasedMsg
)

// Collection mongo collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("im_user_logs")
}

// Create new live user operate record
func (l *Log) Create() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now

	res, err := Collection().InsertOne(ctx, l)
	if err != nil {
		return err
	}

	l.OID = res.InsertedID.(primitive.ObjectID)
	return nil
}

// UpdateOne update one
func UpdateOne(filter, update interface{}) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateOne(ctx, filter,
		bson.M{"$set": update},
	)
	return err
}

const highRiskConnectCountThreshold = 50

// IsHighRisk 是否为高风险连接
// 同一个 IP 地址五分钟内的活跃连接数超过 50，为高风险
// 同一个 joinUUID 五分钟内的活跃连接数超过 50，也为高风险
// TODO: 拆分成两个判断函数
func IsHighRisk(ip, joinUUID string) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	timeFilter := bson.M{"$gte": goutil.TimeNow().Add(-5 * time.Minute).Unix()}
	if ip != "" {
		count, err := Collection().CountDocuments(ctx, bson.M{
			"ip":         ip,
			"renew_time": timeFilter,
		})
		if err != nil {
			return false, err
		}
		if count >= highRiskConnectCountThreshold {
			// 判断是否为办公室 IP
			isOfficeIP, err := userapi.IsOfficeIP(mrpc.NewUserContextFromEnv(), userapi.IsOfficeIPParam{IP: ip})
			if err != nil {
				logger.WithField("ip", ip).Error(err)
				// 发生错误时，维持 IP 连接数量高风险判定
				return true, nil
			}
			if isOfficeIP {
				// 对办公室 IP 连接次数不做限制，不认定高风险
				return false, nil
			}
			return true, nil
		}
	}

	if joinUUID != "" {
		count, err := Collection().CountDocuments(ctx, bson.M{
			"join_uuid":  joinUUID,
			"renew_time": timeFilter,
		})
		if err != nil {
			return false, err
		}
		if count >= highRiskConnectCountThreshold {
			return true, nil
		}
	}
	return false, nil
}

// KeyHighRiskConnectStatus 高风险连接状态缓存的 key
func KeyHighRiskConnectStatus(userID, roomID int64) string {
	return keys.KeyHighRiskConnectStatus2.Format(userID, roomID)
}

// IsNoRiskConnect 判断用户是否连接连接上直播间并且不是高风险
func IsNoRiskConnect(userID, roomID int64) (bool, error) {
	val, err := service.LRURedis.Get(KeyHighRiskConnectStatus(userID, roomID)).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}
	if err == nil {
		// Return from cache
		return val == StatusNormal, nil
	}

	log, err := findLastConnectLog(userID, roomID)
	if err != nil {
		return false, err
	}

	status := StatusNotConnected
	if log != nil {
		status = log.Status
	}

	err = service.LRURedis.Set(KeyHighRiskConnectStatus(userID, roomID), status, 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return status == StatusNormal, nil
}

const inactivityTimeLimit = 7 * time.Minute

func findLastConnectLog(userID, roomID int64) (*Log, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 查询用户在直播间内最近的一次 WebSocket 连接记录是否在 inactivityTimeLimit 分钟内，若超过或者没有连接记录，则视为不在直播间内。
	filter := bson.M{
		"user_id":    userID,
		"room_id":    roomID,
		"renew_time": bson.M{"$gte": goutil.TimeNow().Add(-inactivityTimeLimit).Unix()},
	}
	var log Log
	err := Collection().FindOne(ctx, filter, options.FindOne().SetSort(bson.M{"renew_time": -1})).Decode(&log)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &log, nil
}
