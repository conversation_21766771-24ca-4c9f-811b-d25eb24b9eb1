package imuserlogs

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(-1, StatusNotConnected)
	assert.Equal(0, StatusNormal)
	assert.Equal(1, StatusDisableAllMsg)
	assert.Equal(2, StatusDisablePurchasedMsg)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Log{}, "_id", "create_time", "modified_time",
		"user_id", "guest_id", "connection_uuid", "join_uuid", "status", "room_id",
		"ip", "user_agent", "equip_id", "buvid", "renew_time")
}

func TestLog_CreateAndUpdateOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := Log{
		UserID: 10,
		RoomID: 10,
	}
	err := l.Create()
	require.NoError(err)
	assert.False(l.OID.IsZero())
	assert.NotEmpty(l.CreateTime)
	assert.NotEmpty(l.ModifiedTime)

	err = UpdateOne(bson.M{"_id": l.OID}, bson.M{"user_id": 11})
	require.NoError(err)
}

func TestIsHighRisk(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testIP := "*********"
	testJoinUUID := "3b8e5a08-055a-4485-b464-cc7528941bb7"
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx,
		bson.M{
			"$or": bson.A{
				bson.M{"ip": testIP},
				bson.M{"join_uuid": testJoinUUID},
			}})
	require.NoError(err)

	assert.False(IsHighRisk(testIP, testJoinUUID))

	cancelFunc := mrpc.SetMock(userapi.URLGoIsOfficeIP, func(any) (any, error) {
		return &userapi.IsOfficeIPResp{IsOfficeIP: false}, nil
	})
	defer cancelFunc()
	now := goutil.TimeNow()
	inserts := make([]interface{}, 0, highRiskConnectCountThreshold)
	for i := 0; i < highRiskConnectCountThreshold; i++ {
		inserts = append(inserts, &Log{
			IP:        testIP,
			JoinUUID:  testJoinUUID,
			RenewTime: now.Unix(),
		})
	}
	_, err = Collection().InsertMany(ctx, inserts)
	require.NoError(err)
	assert.True(IsHighRisk(testIP, "test"))
	assert.True(IsHighRisk("test", testJoinUUID))
	assert.True(IsHighRisk(testIP, testJoinUUID))
	assert.False(IsHighRisk("test", "test"))

	cancelFunc2 := mrpc.SetMock(userapi.URLGoIsOfficeIP, func(any) (any, error) {
		return &userapi.IsOfficeIPResp{IsOfficeIP: true}, nil
	})
	defer cancelFunc2()
	assert.False(IsHighRisk(testIP, ""))
}

func TestKeyHighRiskConnectStatus(t *testing.T) {
	assert := assert.New(t)

	userID := int64(123)
	roomID := int64(456)
	expectedKey := keys.KeyHighRiskConnectStatus2.Format(userID, roomID)
	actualKey := KeyHighRiskConnectStatus(userID, roomID)
	assert.Equal(expectedKey, actualKey)
}

func TestIsNoRiskConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(123)
	testRoomID := int64(456)

	testCases := []struct {
		status   int
		expected bool
	}{
		{
			status:   StatusNormal,
			expected: true,
		},
		{
			status:   StatusNotConnected,
			expected: false,
		},
		{
			status:   StatusDisableAllMsg,
			expected: false,
		},
	}

	for _, tc := range testCases {
		err := service.LRURedis.Set(KeyHighRiskConnectStatus(testUserID, testRoomID), tc.status, 5*time.Minute).Err()
		require.NoError(err)
		isConnected, err := IsNoRiskConnect(testUserID, testRoomID)
		require.NoError(err)
		assert.Equal(tc.expected, isConnected)
	}

	err := service.LRURedis.Del(KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow().Unix()
	inserted, err := Collection().InsertOne(ctx, Log{
		CreateTime:   now,
		ModifiedTime: now,
		UserID:       testUserID,
		Status:       0,
		RoomID:       testRoomID,
		RenewTime:    now,
	})
	require.NoError(err)
	defer func() {
		_, _ = Collection().DeleteOne(ctx, bson.M{"_id": inserted.InsertedID})
	}()

	isConnected, err := IsNoRiskConnect(testUserID, testRoomID)
	require.NoError(err)
	assert.True(isConnected)

	err = UpdateOne(bson.M{"_id": inserted.InsertedID}, bson.M{"status": StatusDisableAllMsg})
	require.NoError(err)
	isConnected, err = IsNoRiskConnect(testUserID, testRoomID)
	require.NoError(err)
	assert.True(isConnected)

	err = service.LRURedis.Del(KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
	require.NoError(err)

	isConnected, err = IsNoRiskConnect(testUserID, testRoomID)
	require.NoError(err)
	assert.False(isConnected)

	err = service.LRURedis.Del(KeyHighRiskConnectStatus(testUserID, testRoomID)).Err()
	require.NoError(err)
}
