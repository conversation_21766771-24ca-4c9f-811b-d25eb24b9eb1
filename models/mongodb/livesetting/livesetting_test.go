package livesetting

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testRoomID = int64(223344)
	testKey1   = "key1"
	testKey2   = "key2"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Setting{}, "_id", "create_time", "modified_time", "room_id", "key", "value")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Setting{}, "key", "value")
}

func mock(t *testing.T) {
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, bson.A{
		Setting{
			RoomID: testRoomID,
			Key:    testKey1,
			Value:  `{"name":"哇哈哈"}`,
		},
		Setting{
			RoomID: testRoomID,
			Key:    testKey2,
			Value:  `{"title":"哈啥哈"}`,
		},
	})
	require.NoError(err)
}

func TestFindByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mock(t)

	s, err := FindByRoomID(testRoomID, testKey1)
	require.NoError(err)
	assert.NotNil(s)
}

func TestUpdateOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mock(t)

	err := UpdateOne(testRoomID, testKey1, `{"name":"missevan"}`)
	require.NoError(err)
	s, err := FindByRoomID(testRoomID, testKey1)
	require.NoError(err)
	require.NotNil(s)
	assert.Equal(`{"name":"missevan"}`, s.Value)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	err = UpdateOne(testRoomID, testKey1, `{"name":"missevan go"}`)
	require.NoError(err)
	s, err = FindByRoomID(testRoomID, testKey1)
	require.NoError(err)
	require.NotNil(s)
	assert.Equal(`{"name":"missevan go"}`, s.Value)
}

func TestIsValid(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsValid("1"))
	assert.True(IsValid("live_assistant_settings"))
}
