package livesetting

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MaxChatroomSettingsLen 直播间设置 JSON 字符串最大长度
const MaxChatroomSettingsLen = 1024

// Setting 直播间配置信息
type Setting struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	CreateTime   int64              `bson:"create_time" json:"-"`
	ModifiedTime int64              `bson:"modified_time" json:"-"`

	RoomID int64  `bson:"room_id" json:"-"`
	Key    string `bson:"key" json:"key"`
	Value  string `bson:"value" json:"value"`
}

// Collection 返回 live_settings 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_settings")
}

// FindByRoomID 获取直播间指定配置信息
func FindByRoomID(roomID int64, key string) (*Setting, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	s := new(Setting)
	err := Collection().FindOne(ctx, bson.M{
		"room_id": roomID,
		"key":     key,
	}).Decode(s)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return s, nil
}

// UpdateOne 更新直播间指定的配置信息
func UpdateOne(roomID int64, key, value string) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	nowUnix := goutil.TimeNow().Unix()
	_, err := Collection().UpdateOne(ctx,
		bson.M{
			"room_id": roomID,
			"key":     key,
		},
		bson.M{
			"$set": bson.M{
				"modified_time": nowUnix,
				"value":         value,
			},
			"$setOnInsert": bson.M{
				"create_time": nowUnix,
			},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

// KeyAllowList 允许设置的白名单
var KeyAllowList = []string{"live_assistant_settings"}

// IsValid 当前是否是被允许的配置
func IsValid(key string) bool {
	return goutil.HasElem(
		KeyAllowList, // 配置白名单
		key,
	)
}
