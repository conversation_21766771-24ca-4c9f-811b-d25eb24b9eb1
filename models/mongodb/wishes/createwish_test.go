package wishes

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCreateWish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	liveGoods := &livegoods.LiveGoods{
		ID:    45646,
		Price: 1,
	}
	orderContext := `{"transaction_id":23333,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}`
	wish, err := CreateWish(liveGoods, 233, 1111, 2, goutil.TimeNow(), orderContext)
	require.NoError(err)
	require.NotNil(wish)
	assert.Equal(int64(233), wish.UserID)
	assert.Equal(liveGoods.ID, wish.GoodsID)
	assert.Equal(int64(liveGoods.Price), wish.GoodsPrice)
	assert.Equal(2, wish.Num)
	assert.Equal(int64(1111), wish.TransactionID)
	assert.Equal(orderContext, wish.Context)
	assert.Equal(StatusWaiting, wish.Status)
	assert.Nil(wish.Rewards)
}
