package wishes

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/missevan-go/util"
)

func TestListWishesByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 构建测试数据
	_, err := Collection().DeleteMany(context.Background(), bson.M{"user_id": 12})
	require.NoError(err)
	ws := make([]interface{}, 0, 20)
	now := util.TimeNow()
	beginningOfMonth := util.BeginningOfMonth(now)
	for i := 1; i < 20; i++ {
		unixNow := now.Unix()
		if i == 1 {
			// 插入一条上月的许愿记录，该条记录不会被查出
			unixNow = beginningOfMonth.Add(-time.Second).Unix()
		}
		w := Wish{
			CreateTime: unixNow,
			UserID:     12,
			GoodsID:    15,
			GoodsPrice: 10,
		}
		ws = append(ws, w)
	}
	w := Wish{
		CreateTime: now.Add(time.Second).Unix(),
		UserID:     12,
		GoodsID:    14,
		GoodsPrice: 300,
	}
	d1 := Reward{
		GiftID: 3001,
		Type:   RewardTypeRedeemPoint,
		Point:  100,
	}
	d2 := Reward{
		GiftID: 3000,
		Type:   RewardTypeBackpackGift,
		Num:    10,
	}
	w.Rewards = append(w.Rewards, d1, d2)
	ws = append(ws, w)
	_, err = Collection().InsertMany(context.Background(), ws)
	require.NoError(err)

	// 测试获取许愿记录
	res, pa, err := ListWishesByUserID(12, now, 1, 20)
	require.NoError(err)

	// 插入 20 条许愿记录，其中一条为上月记录，此处只能查出 19 条
	require.Len(res, 19)
	assert.Len(res[0].Rewards, 2)
	assert.Nil(res[1].Rewards)

	assert.Equal(int64(20), pa.PageSize)
	assert.Equal(int64(1), pa.P)
}
