package wishes

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
)

// CreateWish 创建许愿记录
func CreateWish(liveGoods *livegoods.LiveGoods, userID, transactionID int64, num int, when time.Time, context string) (*Wish, error) {
	wish := &Wish{
		CreateTime:    when.Unix(),
		ModifiedTime:  when.Unix(),
		UserID:        userID,
		GoodsID:       liveGoods.GoodsID(),
		GoodsPrice:    int64(liveGoods.Price), // 存入单价
		Num:           num,
		TransactionID: transactionID,
		Context:       context,
		Status:        StatusWaiting,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	result, err := Collection().InsertOne(ctx, wish)
	if err != nil {
		return nil, err
	}
	wish.OID = result.InsertedID.(primitive.ObjectID)
	return wish, nil
}
