package wishes

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ListWishesByUserID 获取许愿记录
func ListWishesByUserID(userID int64, now time.Time, page, pageSize int64) ([]*Wish, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 只查询活动当月的许愿记录
	beginningOfMonth := goutil.BeginningOfMonth(now).Unix()
	filter := bson.M{"user_id": userID, "create_time": bson.M{"$gte": beginningOfMonth}}
	count, err := Collection().CountDocuments(ctx, filter)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	wishes := make([]*Wish, 0)
	pa := goutil.MakePagination(count, page, pageSize)
	if !pa.Valid() {
		return wishes, pa, nil
	}
	mongoOpt := pa.SetFindOptions(nil)
	mongoOpt = mongoOpt.SetSort(bson.M{"create_time": -1})
	cur, err := Collection().Find(ctx, filter, mongoOpt)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &wishes)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	return wishes, pa, nil
}
