package wishes

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Wish{}, "_id", "create_time",
		"modified_time", "user_id", "goods_id", "goods_price", "num",
		"transaction_id", "context", "status", "rewards")
	kc.Check(Reward{}, "type", "point", "gift_id", "num")
}

func TestListStatusWaiting(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := time.Date(2022, 8, 22, 0, 0, 0, 0, time.Local)
	nowStamp := now.Unix()
	wish1 := Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp + 10*goutil.SecondOneMinute,
		ModifiedTime: nowStamp + 10*goutil.SecondOneMinute,
		UserID:       10,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          4,
		Status:       StatusWaiting,
	}
	wish2 := Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp + 2*goutil.SecondOneHour - 1,
		ModifiedTime: nowStamp + 2*goutil.SecondOneHour - 1,
		UserID:       11,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          2,
		Status:       StatusWaiting,
	}
	wish3 := Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp + 2*goutil.SecondOneHour,
		ModifiedTime: nowStamp + 2*goutil.SecondOneHour,
		UserID:       12,
		GoodsID:      101,
		GoodsPrice:   100,
		Num:          3,
		Status:       StatusWaiting,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除数据
	_, err := Collection().DeleteMany(ctx, bson.M{"status": StatusWaiting, "create_time": bson.M{"$gte": nowStamp, "$lte": nowStamp + 2*goutil.SecondOneHour}})
	assert.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{wish1, wish2, wish3})
	require.NoError(err)

	listWish, err := ListStatusWaiting(nowStamp, nowStamp+2*goutil.SecondOneHour)
	require.NoError(err)
	assert.Len(listWish, 2)
}

func TestBatchUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	nowStamp := now.Unix()
	wish1 := Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp - 1,
		ModifiedTime: nowStamp - 1,
		UserID:       10,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          4,
		Status:       StatusWaiting,
	}
	wish2 := Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		UserID:       11,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          2,
		Status:       StatusWaiting,
	}
	wish3 := Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		UserID:       12,
		GoodsID:      101,
		GoodsPrice:   100,
		Num:          3,
		Status:       StatusWaiting,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除数据
	_, err := Collection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{wish1.OID, wish2.OID, wish3.OID}}})
	assert.NoError(err)

	_, err = Collection().InsertMany(ctx, []interface{}{wish1, wish2, wish3})
	require.NoError(err)

	rewards1 := []Reward{
		{Type: RewardTypeBackpackGift, GiftID: 100, Num: 1},
		{Type: RewardTypeRedeemPoint, Point: 100},
	}
	rewards2 := []Reward{
		{Type: RewardTypeBackpackGift, GiftID: 101, Num: 1},
		{Type: RewardTypeRedeemPoint, Point: 500},
	}
	rewards3 := []Reward{
		{Type: RewardTypeBackpackGift, GiftID: 100, Num: 2},
		{Type: RewardTypeRedeemPoint, Point: 900},
	}

	// 测试更新许愿奖励信息
	updates := []mongo.WriteModel{
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": wish1.OID, "status": StatusWaiting}).
			SetUpdate(bson.M{"$set": bson.M{
				"status":        StatusFinished,
				"rewards":       rewards1,
				"modified_time": nowStamp,
			}}),
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": wish2.OID, "status": StatusWaiting}).
			SetUpdate(bson.M{"$set": bson.M{
				"status":        StatusFinished,
				"rewards":       rewards2,
				"modified_time": nowStamp,
			}}),
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": wish3.OID, "status": StatusWaiting}).
			SetUpdate(bson.M{"$set": bson.M{
				"status":        StatusFinished,
				"rewards":       rewards3,
				"modified_time": nowStamp,
			}}),
	}
	err = BatchUpdate(updates)
	require.NoError(err)

	cur, err := Collection().Find(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{wish1.OID, wish2.OID, wish3.OID}}})
	require.NoError(err)

	wishList := make([]*Wish, 0)
	err = cur.All(ctx, &wishList)
	require.NoError(err)

	wishMap := goutil.ToMap(wishList, "OID").(map[primitive.ObjectID]*Wish)
	require.NotNil(StatusFinished, wishMap[wish1.OID])
	assert.Equal(rewards1, wishMap[wish1.OID].Rewards)
	assert.Equal(nowStamp, wishMap[wish1.OID].ModifiedTime)

	require.NotNil(StatusFinished, wishMap[wish2.OID])
	assert.Equal(rewards2, wishMap[wish2.OID].Rewards)
	assert.Equal(nowStamp, wishMap[wish2.OID].ModifiedTime)

	require.NotNil(StatusFinished, wishMap[wish3.OID])
	assert.Equal(rewards3, wishMap[wish3.OID].Rewards)
	assert.Equal(nowStamp, wishMap[wish3.OID].ModifiedTime)
}
