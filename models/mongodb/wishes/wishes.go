package wishes

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
)

// 开奖状态
const (
	StatusWaiting  = iota // 待开奖
	StatusFinished        // 已开奖
)

// 奖励类型
const (
	RewardTypeBackpackGift = iota + 1 // 背包礼物
	RewardTypeRedeemPoint             // 活动兑换积分
)

// Wish 许愿记录
type Wish struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	UserID        int64  `bson:"user_id"`
	GoodsID       int64  `bson:"goods_id"`
	GoodsPrice    int64  `bson:"goods_price"` // 商品单价
	Num           int    `bson:"num"`
	TransactionID int64  `bson:"transaction_id"` // 订单号
	Context       string `bson:"context"`        // 购买订单上下文，开奖后存入背包礼物的 context

	Status  int      `bson:"status"`
	Rewards []Reward `bson:"rewards,omitempty"`
}

// Reward 奖励
type Reward struct {
	Type   int   `bson:"type"`
	Point  int64 `bson:"point,omitempty"`
	GiftID int64 `bson:"gift_id,omitempty"`
	Num    int   `bson:"num,omitempty"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("wishes")
}

// ListStatusWaiting 查询某时间段内的未开奖的许愿记录（按许愿时间正序排序）
func ListStatusWaiting(startTime, endTime int64) ([]*Wish, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"status":      StatusWaiting,
		"create_time": bson.M{"$gte": startTime, "$lt": endTime},
	}
	opt := options.Find().SetSort(bson.M{"create_time": 1})
	cur, err := Collection().Find(ctx, filter, opt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var wishes []*Wish
	err = cur.All(ctx, &wishes)

	return wishes, err
}

// BatchUpdate 更新许愿奖励信息（每 500 条更新一次）
func BatchUpdate(updates []mongo.WriteModel) error {
	writeFunc := func(writes []mongo.WriteModel) error {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		_, err := Collection().BulkWrite(ctx, writes)
		if err != nil {
			return err
		}
		return nil
	}

	batchSize := 500
	updatesLen := len(updates)
	for offset := 0; offset < updatesLen; offset += batchSize {
		var batchUpdate []mongo.WriteModel
		leftBorder, rightBorder := offset, offset+batchSize
		if rightBorder >= updatesLen {
			batchUpdate = updates[leftBorder:]
		} else {
			batchUpdate = updates[leftBorder:rightBorder]
		}

		err := writeFunc(batchUpdate)
		if err != nil {
			return err
		}
	}

	return nil
}
