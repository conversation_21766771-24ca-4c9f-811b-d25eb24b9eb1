package guest

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
)

// CollectionName collection name
const CollectionName = "guests"

// Guest 游客信息
type Guest struct {
	OID         primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	UserID      string             `bson:"user_id" json:"user_id"`
	Username    string             `bson:"username" json:"username"`
	AccID       string             `bson:"accid" json:"accid"`
	Token       string             `bson:"token" json:"token"`
	CreatedTime time.Time          `bson:"created_time" json:"-"`
	UpdatedTime time.Time          `bson:"updated_time" json:"-"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}
