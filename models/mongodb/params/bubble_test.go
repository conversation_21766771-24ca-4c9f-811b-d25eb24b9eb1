package params

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBubbleTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Bubble{}, "_id", "key", "horns", "custom_horn_bubble")
	kc.Check(HornBubble{}, "usage", "start_time", "end_time", "horn_msg_template", "noble_bubbles", "black_card_bubbles")
	kc.Check(NobleBubble{}, "type", "level", "bubble_id", "icon")

	kc = tutil.<PERSON><PERSON>ey<PERSON><PERSON><PERSON>(t, tutil.JSON)
	kc.Check(Bubble{}, "key", "horns", "custom_horn_bubble")
	kc.Check(HornBubble{}, "start_time", "end_time", "horn_msg_template", "noble_bubbles", "black_card_bubbles")
	kc.Check(NobleBubble{}, "type", "level", "bubble_id", "icon")
}

func TestDefaultBubble(t *testing.T) {
	assert := assert.New(t)

	bubble := DefaultBubble()
	assert.Equal(KeyBubble, bubble.Key)
}

func TestFindBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()

	key := keys.KeyParams1.Format(KeyBubble)
	err := service.LRURedis.Del(key).Err()
	require.NoError(err)
	bubble, err := FindBubble()
	assert.NoError(err)
	assert.Equal(KeyBubble, bubble.Key)
	hb := bubble.FindHornBubble(now)
	require.NotNil(hb)
	assert.EqualValues(84, hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 4))
	assert.EqualValues(90, hb.UserVipNotifyBubbleID(vip.TypeLiveHighness, 1))

	defaultBubble := DefaultBubble()
	defaultBubble.Horns[0].NobleBubbles[0].BubbleID = 888888
	defaultBubbleJSON, err := json.Marshal(defaultBubble)
	require.NoError(err)
	err = service.LRURedis.Set(key, defaultBubbleJSON, time.Minute).Err()
	require.NoError(err)
	bubble, err = FindBubble()
	assert.NoError(err)
	hb = bubble.FindHornBubble(now)
	require.NotNil(hb)
	assert.Equal(KeyBubble, bubble.Key)
	assert.EqualValues(888888, hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 4))
}

func TestBubble_UserVipNotifyBubbleID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bubble := DefaultBubble()
	hb := bubble.FindHornBubble(goutil.TimeNow())
	require.NotNil(hb)
	// 普通贵族
	assert.EqualValues(84, hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 4))
	assert.EqualValues(85, hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 5))
	assert.EqualValues(86, hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 6))
	assert.EqualValues(87, hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 7))
	// 上神
	assert.EqualValues(90, hb.UserVipNotifyBubbleID(vip.TypeLiveHighness, 1))
	// 体验贵族
	assert.EqualValues(84, hb.UserVipNotifyBubbleID(vip.TypeLiveTrialNoble, 4))
	assert.EqualValues(85, hb.UserVipNotifyBubbleID(vip.TypeLiveTrialNoble, 5))
	assert.EqualValues(86, hb.UserVipNotifyBubbleID(vip.TypeLiveTrialNoble, 6))
	assert.EqualValues(87, hb.UserVipNotifyBubbleID(vip.TypeLiveTrialNoble, 7))
	assert.Zero(hb.UserVipNotifyBubbleID(vip.TypeLiveNoble, 983981231293812))
	assert.Zero(hb.UserVipNotifyBubbleID(132321312, 7))
}

func TestHornBubble_IsActive(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	hb := HornBubble{
		StartTime: now.AddDate(0, 0, -1).Unix(),
		EndTime:   now.AddDate(0, 0, 1).Unix(),
	}
	assert.True(hb.IsActive(now))

	hb = HornBubble{
		StartTime: now.AddDate(0, 0, 1).Unix(),
		EndTime:   now.AddDate(0, 0, 2).Unix(),
	}
	assert.False(hb.IsActive(now))

	hb = HornBubble{
		StartTime: now.AddDate(0, 0, -1).Unix(),
		EndTime:   now.Unix(),
	}
	assert.False(hb.IsActive(now))
}
