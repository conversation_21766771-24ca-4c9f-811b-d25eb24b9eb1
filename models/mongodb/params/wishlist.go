package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// WishListConfig 心愿单配置
type WishListConfig struct {
	OID      primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key      string             `bson:"key,omitempty" json:"key,omitempty"`
	OpenTime int64              `bson:"open_time,omitempty" json:"open_time,omitempty"` // 开启时间，0 为不限制（秒级时间戳）
	Name     string             `bson:"name,omitempty" json:"name,omitempty"`           // 心愿单名称
}

// 默认心愿单配置
func defaultWishListConfig() *WishListConfig {
	return &WishListConfig{
		Key:  KeyWishList,
		Name: "心愿单",
	}
}

// FindWishList 查找心愿单配置
func FindWishList() (*WishListConfig, error) {
	config := defaultWishListConfig()
	err := findParams(KeyWishList, &config)
	if err != nil {
		return nil, err
	}
	return config, err
}

// IsOpen 是否开启
func (c *WishListConfig) IsOpen() bool {
	now := goutil.TimeNow().Unix()
	return c.OpenTime <= now
}
