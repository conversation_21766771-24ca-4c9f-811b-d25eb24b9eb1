package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestFindParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "test"
	cacheKey := keys.KeyParams1.Format(key)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	v := make(map[string]bool)
	require.NoError(findParams("test", &v))
	assert.Empty(v)

	ok, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.EqualValues(1, ok)

	require.NoError(service.LRURedis.Set(cacheKey, `{"test":true}`, time.Second).Err())
	require.NoError(findParams("test", &v))
	assert.Equal(map[string]bool{"test": true}, v)
}

func TestClearCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeySoundRecommend)
	_, err := service.LRURedis.Del(cacheKey).Result()
	require.NoError(err)

	assert.PanicsWithValue("empty param keys", func() {
		ClearCache()
	})
	require.NoError(service.Redis.Set(cacheKey, "TestClearKeySoundRecommendCache", time.Minute).Err())
	ClearCache(KeySoundRecommend)
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.Zero(exists)
}
