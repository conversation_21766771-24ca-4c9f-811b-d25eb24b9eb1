package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Gift 礼物相关配置
type Gift struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	ClosedRoomAllowDrawGift bool `bson:"closed_room_allow_draw_gift" json:"closed_room_allow_draw_gift"`
}

// DefaultGift 默认配置
func DefaultGift() Gift {
	return Gift{
		Key:                     KeyGift,
		ClosedRoomAllowDrawGift: false,
	}
}

// FindGift 查询配置礼物配置
func FindGift() (Gift, error) {
	gift := DefaultGift()
	err := findParams(KeyGift, &gift)
	return gift, err
}
