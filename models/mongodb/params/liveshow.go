package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service/storage"
)

// LiveShow 个人场配置
type LiveShow struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key,omitempty" json:"key,omitempty"`

	Cover        string `bson:"cover,omitempty" json:"cover,omitempty"`
	MiniURL      string `bson:"mini_url,omitempty" json:"mini_url,omitempty"`
	FoldImage    string `bson:"fold_image,omitempty" json:"fold_image,omitempty"`
	FullURL      string `bson:"full_url,omitempty" json:"full_url,omitempty"`
	FoldImageURL string `bson:"-" json:"-"`
}

// AfterLoad after load
func (show *LiveShow) AfterLoad() {
	show.FoldImageURL = storage.ParseSchemeURL(show.FoldImage)
}

// DefaultLiveShow 默认配置
func DefaultLiveShow() LiveShow {
	return LiveShow{
		Key:       KeyLiveShow,
		Cover:     "oss://live/liveshow/widget/cover.png",
		MiniURL:   config.Conf.Params.URL.Main + "blackboard/activity-liveshow.html?room_id=__ROOM_ID__",
		FoldImage: "oss://live/liveshow/widget/foldimage.png",
		FullURL:   config.Conf.Params.URL.Main + "standalone/event/live-show/index.html?room_id=__ROOM_ID__",
	}
}

// FindLiveShow 查询个人场配置
func FindLiveShow() (LiveShow, error) {
	sr := DefaultLiveShow()
	err := findParams(sr.Key, &sr)
	sr.AfterLoad()
	return sr, err
}
