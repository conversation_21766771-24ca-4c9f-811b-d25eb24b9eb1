package params

import (
	"encoding/json"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// 参数类型
const (
	KeyGlobal         = "global"
	KeySoundRecommend = "sound_recommend"
	KeyGashapon       = "gashapon"
	KeyRank           = "rank"
	KeyRedeemShop     = "redeem_shop"
	KeyPrivilegeShop  = "privilege_shop"
	KeyLiveShow       = "live_show"
	KeyRedPacket      = "red_packet"
	KeyGift           = "gift"
	KeySticker        = "sticker"
	KeyPia            = "pia"
	KeyMultiCombo     = "multi_combo"
	KeyBubble         = "bubble"
	KeyReward         = "reward"
	KeyLuckyBag       = "lucky_bag"
	KeyWishList       = "wish_list"
	KeyLuckyBox       = "lucky_box"
	KeyTestConfig     = "test_config"
	KeyPK             = "pk"
	KeyMultiConnect   = "multi_connect"
	KeyLiveTask       = "live_task"
	KeyMedal          = "medal"
)

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("params")
}

func findParams(key string, v interface{}) error {
	cacheKey := keys.KeyParams1.Format(key)
	cache, err := service.LRURedis.Get(cacheKey).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}
	if len(cache) != 0 {
		err = json.Unmarshal(cache, v)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return nil
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err = Collection().FindOne(ctx, bson.M{"key": key}).Decode(v)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		return err
	}

	b, err := json.Marshal(v)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(cacheKey, string(b), 10*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// ClearCache 清空配置相关缓存
func ClearCache(paramKeys ...string) {
	if len(paramKeys) == 0 {
		panic("empty param keys")
	}
	cacheKeys := make([]string, 0, len(paramKeys))
	for paramKey := range paramKeys {
		cacheKey := keys.KeyParams1.Format(paramKey)
		cacheKeys = append(cacheKeys, cacheKey)
	}
	_, err := service.LRURedis.Del(cacheKeys...).Result()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
