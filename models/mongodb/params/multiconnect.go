package params

import (
	"slices"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
)

// MultiConnect 主播连线相关配置
type MultiConnect struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	StartTime int64  `bson:"start_time" json:"start_time"` // 主播连线开始时间，秒级时间戳
	EntryIcon string `bson:"entry_icon" json:"entry_icon"` // 入口图标

	BetaStartTime int64   `bson:"beta_start_time,omitempty" json:"beta_start_time,omitempty"` // 主播连线新功能生效时间，秒级时间戳
	BetaEndTime   int64   `bson:"beta_end_time,omitempty" json:"beta_end_time,omitempty"`     // 主播连线新功能过期时间，秒级时间戳
	BetaRoomIDs   []int64 `bson:"beta_room_ids,omitempty" json:"beta_room_ids,omitempty"`     // 主播连线新功能白名单
}

// AfterLoad after load
func (mc *MultiConnect) AfterLoad() {
	mc.EntryIcon = storage.ParseSchemeURL(mc.EntryIcon)
}

// DefaultMultiConnect 默认的主播连线配置
func DefaultMultiConnect() MultiConnect {
	return MultiConnect{
		Key:       KeyMultiConnect,
		StartTime: 0,
		EntryIcon: "",
	}
}

// FindMultiConnect 查找主播连线配置
func FindMultiConnect() (MultiConnect, error) {
	mc := DefaultMultiConnect()
	err := findParams(KeyMultiConnect, &mc)
	mc.AfterLoad()
	return mc, err
}

// IsFullOpen 检查是否全面开放
func (mc *MultiConnect) IsFullOpen(when time.Time) bool {
	return mc.StartTime != 0 && mc.StartTime <= when.Unix()
}

// isInBetaTimeRange 检查是否在新功能内测时间内，如果未配置新功能内测时间，则返回 true
func (mc *MultiConnect) isInBetaTimeRange(when time.Time) bool {
	return mc.BetaStartTime <= when.Unix() && (mc.BetaEndTime >= when.Unix() || mc.BetaEndTime == 0)
}

// IsBetaRoom 检查是否属于新功能白名单用户
func (mc *MultiConnect) IsBetaRoom(roomID int64) bool {
	return slices.Contains(mc.BetaRoomIDs, roomID)
}

// IsOpen 检查是否开放，目前主播连线全量开放
func (mc *MultiConnect) IsOpen(when time.Time, roomID int64) bool {
	return mc.IsFullOpen(when)
}
