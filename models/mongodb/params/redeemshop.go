package params

import (
	"net/url"
	"strconv"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// RedeemShop 兑换商城相关配置
type RedeemShop struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Name     string `bson:"name" json:"name"`
	Icon     string `bson:"icon" json:"icon"`
	WebIcon  string `bson:"web_icon" json:"web_icon"`
	ShopURL  string `bson:"shop_url" json:"shop_url"`
	ShowTime int64  `bson:"show_time" json:"show_time"` // 单位：秒

	IconURL    string `bson:"-" json:"-"`
	WebIconURL string `bson:"-" json:"-"`
}

func (s *RedeemShop) afterLoad() {
	if s.Icon != "" {
		s.IconURL = storage.ParseSchemeURL(s.Icon)
	}
	if s.WebIcon != "" {
		s.WebIconURL = storage.ParseSchemeURL(s.WebIcon)
	}
}

// DefaultRedeemShop 默认的常驻兑换商城配置
func DefaultRedeemShop() RedeemShop {
	return RedeemShop{
		Key:     KeyRedeemShop,
		Name:    "万事屋",
		Icon:    "oss://live/shop/icons/entrance/redeem-shop.png",
		WebIcon: "oss://live/shop/icons/entrance/redeem-shop-web.png",
	}
}

// FindRedeemShop 查找常驻兑换商城配置
func FindRedeemShop() (RedeemShop, error) {
	s := DefaultRedeemShop()
	err := findParams(KeyRedeemShop, &s)
	s.afterLoad()
	return s, err
}

// DefaultPrivilegeShop 默认的等级权益商城配置
func DefaultPrivilegeShop() RedeemShop {
	return RedeemShop{
		Key:     KeyPrivilegeShop,
		Name:    "星享馆",
		Icon:    "oss://live/shop/icons/entrance/privilege-shop.png",
		WebIcon: "oss://live/shop/icons/entrance/privilege-shop-web.png",
	}
}

// FindPrivilegeShop 查找等级权益商城配置
func FindPrivilegeShop() (RedeemShop, error) {
	s := DefaultPrivilegeShop()
	err := findParams(KeyPrivilegeShop, &s)
	s.afterLoad()
	return s, err
}

// IsShow 判断是否显示常驻商城的入口
func (s RedeemShop) IsShow() bool {
	return s.Name != "" && s.ShopURL != "" && goutil.TimeNow().Unix() >= s.ShowTime
}

// GetShopURL 在 ShopURL 上补充 from_room_id
func (s RedeemShop) GetShopURL(roomID int64) (string, error) {
	shopURL, err := url.Parse(s.ShopURL)
	if err != nil {
		return "", err
	}
	q := shopURL.Query()
	q.Add("from_room_id", strconv.FormatInt(roomID, 10))
	shopURL.RawQuery = q.Encode()
	return shopURL.String(), nil
}
