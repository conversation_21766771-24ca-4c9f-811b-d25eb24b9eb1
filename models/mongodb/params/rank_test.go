package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestRankTags(t *testing.T) {
	keys := []string{
		"_id", "key",
		"hour_top3_frames", "hour_top1_notify_message", "hour_top1_bubble_id",
		"last_hour_recommend", "rank_event", "month_reward_url",
	}
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Rank{}, keys...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Rank{}, keys[1:]...)
}

func TestDefaultRank(t *testing.T) {
	assert := assert.New(t)

	r := DefaultRank()
	assert.Equal(KeyRank, r.Key)
	assert.Equal([3]string{
		"oss://live/hourrank/001-top1.png",
		"oss://live/hourrank/001-top2.png",
		"oss://live/hourrank/001-top3.png",
	}, r.HourTop3Frames)
	assert.Equal(`<font color="${normal_color}">恭喜主播 </font><font color="${highlight_color}"><b>${creator_username}</b></font><font color="${normal_color}"> 获得 </font><font color="${highlight_color}">${hour_start}-${hour_end}</font><font color="${normal_color}"> 小时榜第一名！快来围观吧~</font>`,
		r.HourTop1Message)
	assert.EqualValues(bubble.BubbleIDHourTop1, r.HourTop1BubbleID)
	assert.EqualValues([3]string{
		"oss://live/labelicon/livelist/lasthour01-1.png",
		"oss://live/labelicon/livelist/lasthour02-1.png",
		"oss://live/labelicon/livelist/lasthour03-1.png",
	}, r.LastHourRecommend.IconGroups[0].Icons)
	assert.EqualValues(r.MonthRewardURL, "https://www.missevan.com/mtopic/399")
}

func TestFindRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := FindRank()
	require.NoError(err)
	assert.Equal(KeyRank, r.Key)

	r.HourTop3Frames = [3]string{"1"}
	r.RankEvent = nil
	cacheKey := keys.KeyParams1.Format(r.Key)
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(r), 10*time.Second).Err()
	require.NoError(err)

	r, err = FindRank()
	require.NoError(err)
	assert.Equal([3]string{"1"}, r.HourTop3Frames)
	assert.Nil(r.RankEvent)

	r.RankEvent = &RankEvent{
		Icon:    "oss://test-icon.jpg",
		OpenURL: "http://open",
	}
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(r), 10*time.Second).Err()
	require.NoError(err)

	r, err = FindRank()
	require.NoError(err)
	require.NotNil(r.RankEvent)
	assert.Equal("https://static-test.missevan.com/test-icon.jpg", r.RankEvent.IconURL)
	assert.Equal(r.RankEvent.OpenURL, r.RankEvent.OpenURL)
}

func TestRankHourTop3FrameURL(t *testing.T) {
	assert := assert.New(t)

	r := DefaultRank()
	expected := [3]string{
		storage.ParseSchemeURL(r.HourTop3Frames[0]),
		storage.ParseSchemeURL(r.HourTop3Frames[1]),
		storage.ParseSchemeURL(r.HourTop3Frames[2]),
	}
	for i := 1; i <= 3; i++ {
		assert.Equal(expected[i-1], r.HourTop3FrameURL(i))
	}
	assert.Panics(func() { r.HourTop3FrameURL(0) })
	assert.Panics(func() { r.HourTop3FrameURL(4) })
}

func TestRank_LastHourRecommendIcons(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := util.TimeNow()
	r, err := FindRank()
	require.NoError(err)
	assert.Equal([3]string{
		"oss://live/labelicon/livelist/lasthour01-1.png",
		"oss://live/labelicon/livelist/lasthour02-1.png",
		"oss://live/labelicon/livelist/lasthour03-1.png",
	}, r.LastHourRecommendIcons(now))

	r.LastHourRecommend =
		LastHourRecommend{IconGroups: []LastHourRecommendIconGroup{
			defaultLastHourRecommendIconGroup(),
			{
				Icons:     [3]string{"1", "2", "3"},
				StartTime: now.Add(time.Hour).Unix(),
				EndTime:   now.Add(3 * time.Hour).Unix(),
			},
		}}

	assert.Equal(defaultLastHourRecommendIconGroup().Icons, r.LastHourRecommendIcons(now))
	assert.Equal([3]string{"1", "2", "3"}, r.LastHourRecommendIcons(now.Add(2*time.Hour)))
}

func TestRankHourTop1NotifyInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bs, err := bubble.AllBubbles()
	require.NoError(err)
	require.NotEmpty(bs)

	r := DefaultRank()
	r.HourTop1BubbleID = bs[0].BubbleID

	msg, b := r.HourTop1NotifyInfo("<>", "<00:00>", "<01:00>")
	assert.Equal(`<font color="#FFFFFF">恭喜主播 </font><font color="#FFFFFF"><b>&lt;&gt;</b></font><font color="#FFFFFF"> 获得 </font><font color="#FFFFFF">&lt;00:00&gt;-&lt;01:00&gt;</font><font color="#FFFFFF"> 小时榜第一名！快来围观吧~</font>`, msg)
	assert.NotNil(b)
}
