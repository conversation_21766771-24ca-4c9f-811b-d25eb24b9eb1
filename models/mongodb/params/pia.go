package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Pia 戏相关配置
type Pia struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Name     string `bson:"name" json:"name"`
	ShowTime int64  `bson:"show_time" json:"show_time"` // 单位：秒
}

// DefaultPia 默认的 Pia 配置
func DefaultPia() Pia {
	return Pia{
		Key:      KeyPia,
		Name:     "pia 戏",
		ShowTime: 0, // TODO: 待定
	}
}

// FindPia 查找 pia 戏配置
func FindPia() (Pia, error) {
	p := DefaultPia()
	err := findParams(KeyPia, &p)
	return p, err
}

// IsShow 判断是否显示 pia 戏的入口
func (p Pia) IsShow() bool {
	return p.Name != "" && p.ShowTime != 0 && goutil.TimeNow().Unix() >= p.ShowTime
}
