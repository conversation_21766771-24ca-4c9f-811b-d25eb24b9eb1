package params

import (
	"html"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Rank 榜单相关配置
type Rank struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	HourTop3Frames [3]string `bson:"hour_top3_frames,omitempty" json:"hour_top3_frames,omitempty"`

	HourTop1Message  string `bson:"hour_top1_notify_message,omitempty" json:"hour_top1_notify_message,omitempty"`
	HourTop1BubbleID int64  `bson:"hour_top1_bubble_id,omitempty" json:"hour_top1_bubble_id,omitempty"`

	LastHourRecommend LastHourRecommend `bson:"last_hour_recommend,omitempty" json:"last_hour_recommend,omitempty"` // 开播列表上小时榜推荐图标
	RankEvent         *RankEvent        `bson:"rank_event,omitempty" json:"rank_event,omitempty"`                   // 排行榜顶部 tab 栏最右侧的活动配置
	MonthRewardURL    string            `bson:"month_reward_url,omitempty" json:"month_reward_url,omitempty"`       // 排行榜月榜奖励地址
}

// LastHourRecommend 上小时榜推荐图标配置
type LastHourRecommend struct {
	IconGroups []LastHourRecommendIconGroup `bson:"icon_groups" json:"icon_groups"`
}

// LastHourRecommendIconGroup 上小时榜推荐图标分组配置
type LastHourRecommendIconGroup struct {
	Icons     [3]string `bson:"icons" json:"icons"`           // 开播列表上小时榜推荐图标
	StartTime int64     `bson:"start_time" json:"start_time"` // 开始时间, 秒级时间戳
	EndTime   int64     `bson:"end_time" json:"end_time"`     // 结束时间, 秒级时间戳
}

// RankEvent 排行榜顶部 tab 栏最右侧的活动配置
type RankEvent struct {
	Icon    string `bson:"icon" json:"icon"`
	OpenURL string `bson:"open_url" json:"open_url"`

	IconURL string `bson:"-" json:"-"`
}

func (r *RankEvent) afterLoad() {
	if r.Icon != "" {
		r.IconURL = storage.ParseSchemeURL(r.Icon)
	}
}

func defaultLastHourRecommendIconGroup() LastHourRecommendIconGroup {
	return LastHourRecommendIconGroup{
		Icons: [3]string{
			"oss://live/labelicon/livelist/lasthour01-1.png",
			"oss://live/labelicon/livelist/lasthour02-1.png",
			"oss://live/labelicon/livelist/lasthour03-1.png",
		},
	}
}

// DefaultRank 默认榜单相关配置
func DefaultRank() Rank {
	return Rank{
		Key: KeyRank,
		HourTop3Frames: [3]string{
			"oss://live/hourrank/001-top1.png",
			"oss://live/hourrank/001-top2.png",
			"oss://live/hourrank/001-top3.png",
		},
		HourTop1Message:  `<font color="${normal_color}">恭喜主播 </font><font color="${highlight_color}"><b>${creator_username}</b></font><font color="${normal_color}"> 获得 </font><font color="${highlight_color}">${hour_start}-${hour_end}</font><font color="${normal_color}"> 小时榜第一名！快来围观吧~</font>`,
		HourTop1BubbleID: bubble.BubbleIDHourTop1,
		LastHourRecommend: LastHourRecommend{
			IconGroups: []LastHourRecommendIconGroup{
				defaultLastHourRecommendIconGroup(),
			},
		},
		MonthRewardURL: config.Conf.Params.URL.Main + "mtopic/399",
	}
}

// FindRank 查询榜单相关配置
func FindRank() (Rank, error) {
	r := DefaultRank()
	err := findParams(KeyRank, &r)
	if r.RankEvent != nil {
		// TODO: 确认统一成内部处理还是外部使用时处理
		r.RankEvent.afterLoad()
	}
	return r, err
}

// HourTop3FrameURL 榜单前三背景图
func (param Rank) HourTop3FrameURL(rank int) string {
	return storage.ParseSchemeURL(param.HourTop3Frames[rank-1])
}

// LastHourRecommendIcons 上小时榜推荐图标
func (param Rank) LastHourRecommendIcons(when time.Time) [3]string {
	groups := param.LastHourRecommend
	for i := len(groups.IconGroups) - 1; i >= 0; i-- {
		r := groups.IconGroups[i]
		if r.StartTime <= when.Unix() && (r.EndTime == 0 || when.Unix() < r.EndTime) {
			return r.Icons
		}
	}

	return defaultLastHourRecommendIconGroup().Icons
}

// HourTop1NotifyInfo 小时榜第一广播消息文案 + 气泡
func (param Rank) HourTop1NotifyInfo(creatorUsername,
	hourStart, hourEnd string) (string, *bubble.Simple) {
	formatParam := map[string]string{
		"creator_username": html.EscapeString(creatorUsername),
		"hour_start":       html.EscapeString(hourStart),
		"hour_end":         html.EscapeString(hourEnd),
		"normal_color":     "#FFFFFF",
		"highlight_color":  "#FFFFFF",
	}
	var b *bubble.Simple
	if param.HourTop1BubbleID != 0 {
		var err error
		b, err = bubble.FindSimple(param.HourTop1BubbleID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if b != nil {
			b.AppendFormatParams(formatParam)
		}
	}
	return goutil.FormatMessage(param.HourTop1Message, formatParam), b
}
