package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLuckyBagTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LuckyBagConfig{}, "_id", "key", "open_time", "name", "image", "new_image", "big_image", "lucky_bag_drama", "lucky_bag_entity", "high_risk_score")
	kc.Check(LuckyBag{}, "name", "max_prize_num", "keyword", "initiate_limit_daily",
		"targets", "rewards", "countdowns")
	kc.Check(LuckyBagTarget{}, "type", "name")
	kc.Check(LuckyBagReward{}, "type", "name", "intro", "prize_icon")
	kc.Check(LuckyBagCountdown{}, "min_price", "duration")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LuckyBagConfig{}, "key", "open_time", "name", "image", "new_image", "big_image", "lucky_bag_drama", "lucky_bag_entity", "high_risk_score")
	kc.Check(LuckyBag{}, "name", "max_prize_num", "keyword", "initiate_limit_daily",
		"targets", "rewards", "countdowns")
	kc.Check(LuckyBagTarget{}, "type", "name")
	kc.Check(LuckyBagReward{}, "type", "name", "intro", "prize_icon")
	kc.Check(LuckyBagCountdown{}, "min_price", "duration")
}

func TestLuckyBagConfig_afterLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config := LuckyBagConfig{
		Image:          "oss://luckybag/image.png",
		BigImage:       "oss://luckybag/big_image.png",
		LuckyBagEntity: defaultLuckyBagEntityConfig(),
	}
	require.NotPanics(func() {
		config.afterLoad()
	})
	assert.Equal("https://static-test.missevan.com/luckybag/image.png", config.ImageURL)
	assert.Equal("https://static-test.missevan.com/luckybag/big_image.png", config.BigImageURL)
	assert.Equal("https://static-test.missevan.com/live/luckybag/prize-personal.png", config.LuckyBagEntity.Rewards[0].PrizeIconURL)
	assert.Equal("https://static-test.missevan.com/live/luckybag/prize-drama.png", config.LuckyBagEntity.Rewards[1].PrizeIconURL)
}

func TestLuckyBagConfig_IsOpen(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	config := LuckyBagConfig{
		OpenTime: now.Add(time.Minute).Unix(),
	}
	assert.False(config.IsOpen())

	config.OpenTime = now.Unix()
	assert.True(config.IsOpen())
}

func TestFindLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyLuckyBag)
	err := service.LRURedis.Del(cacheKey).Err()
	require.NoError(err)

	// 测试从数据库获取
	luckyBags, err := FindLuckyBag()
	require.NoError(err)
	require.NotNil(luckyBags.LuckyBagDrama)
	assert.Equal("剧集福袋", luckyBags.LuckyBagDrama.Name)
	assert.Equal(2, len(luckyBags.LuckyBagDrama.Targets))
	assert.Equal(1, len(luckyBags.LuckyBagDrama.Rewards))
	assert.Equal(4, len(luckyBags.LuckyBagDrama.Countdowns))
	assert.Equal("实物福袋", luckyBags.LuckyBagEntity.Name)
	assert.Equal(4, len(luckyBags.LuckyBagEntity.Targets))
	assert.Equal(2, len(luckyBags.LuckyBagEntity.Rewards))
	assert.Equal(4, len(luckyBags.LuckyBagEntity.Countdowns))

	// 测试从缓存获取
	luckyBags, err = FindLuckyBag()
	require.NoError(err)
	require.NotNil(luckyBags.LuckyBagDrama)
	require.NotNil(luckyBags.LuckyBagEntity)
}

func TestLuckyBag_IsRewardTypeValid(t *testing.T) {
	assert := assert.New(t)

	l := defaultLuckyBagDramaConfig()
	assert.True(l.IsRewardTypeValid(luckybag.RewardTypeDrama))
	assert.False(l.IsRewardTypeValid(luckybag.RewardTypeEntityDrama))
}

func TestLuckyBag_IsTargetTypeValid(t *testing.T) {
	assert := assert.New(t)

	l := defaultLuckyBagDramaConfig()
	assert.True(l.IsTargetTypeValid(luckybag.TargetTypeAll))
	assert.False(l.IsTargetTypeValid(luckybag.TargetTypeMedal))
}

func TestLuckyBag_IsCountdownValid(t *testing.T) {
	assert := assert.New(t)

	l := defaultLuckyBagDramaConfig()
	assert.False(l.IsCountdownValid(600, 10))
	assert.True(l.IsCountdownValid(180000, 10))
	assert.False(l.IsCountdownValid(600000, 10))
	assert.True(l.IsCountdownValid(600000, 200))

	l = defaultLuckyBagEntityConfig()
	assert.False(l.IsCountdownValid(600, 0))
	assert.True(l.IsCountdownValid(600000, 0))
}

func TestLuckyBag_PrizeIconURL(t *testing.T) {
	assert := assert.New(t)

	l := LuckyBag{
		Rewards: []LuckyBagReward{
			{
				Type:         luckybag.RewardTypeEntityPersonal,
				PrizeIcon:    "oss://live/luckybag/prize-personal.png",
				PrizeIconURL: "https://static-test.missevan.com/live/luckybag/prize-personal.png",
			},
		},
	}
	prizeIcon, prizeIconURL := l.PrizeIconURL(luckybag.RewardTypeEntityPersonal)
	assert.Equal("oss://live/luckybag/prize-personal.png", prizeIcon)
	assert.Equal("https://static-test.missevan.com/live/luckybag/prize-personal.png", prizeIconURL)

	prizeIcon, prizeIconURL = l.PrizeIconURL(luckybag.RewardTypeEntityDrama)
	assert.Empty(prizeIcon)
	assert.Empty(prizeIconURL)
}
