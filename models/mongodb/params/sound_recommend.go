package params

import (
	"math/rand"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// 音频播放页推荐直播间策略
const (
	SRStrategyNone = iota // 没有推荐
	SRStrategyUP          // UP 主推荐
	SRStrategyCV          // CV 推荐
	SRStrategyLive        // 随机推荐
)

// SoundRecommend 音频播放页直播推荐配置
type SoundRecommend struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Live *SoundRecommendLive `bson:"live,omitempty" json:"live,omitempty"`
}

// DramaAllowList 推荐白名单
type DramaAllowList struct {
	DramaID       int64   `bson:"drama_id" json:"drama_id"`
	RoomAllowList []int64 `bson:"room_allow_list" json:"room_allow_list"`
	StartTime     int64   `bson:"start_time" json:"start_time"` // 单位：秒
	EndTime       int64   `bson:"end_time" json:"end_time"`     // 单位：秒，0 表示永久有效
}

// SoundRecommendLive 音频播放页随机推荐配置
type SoundRecommendLive struct {
	// 推荐白名单
	DramaAllowList []DramaAllowList `bson:"drama_allow_list,omitempty" json:"drama_allow_list,omitempty"`
	// 随机推荐分区黑名单
	BlocklistLiveCatalog []int64 `bson:"blocklist_live_catalog,omitempty" json:"blocklist_live_catalog,omitempty"`
	// 随机推荐直播间黑名单
	BlocklistRoom []int64 `bson:"blocklist_room,omitempty" json:"blocklist_room,omitempty"`
	// 随机推荐音频黑名单
	BlocklistSound []int64 `bson:"blocklist_sound,omitempty" json:"blocklist_sound,omitempty"`
	// 随机推荐音频分区黑名单
	BlocklistSoundCatalog []int64 `bson:"blocklist_sound_catalog,omitempty" json:"blocklist_sound_catalog,omitempty"`

	ScorePercent int `bson:"score_percent" json:"score_percent"` // 百分比 0 - 100

	MessagePool []string `bson:"message_pool,omitempty" json:"message_pool"`

	OffDuration int64 `bson:"off_duration" json:"off_duration"` // 单位：毫秒
}

// FindDramaAllowList 返回符合条件的白名单，白名单为直播间 ID
func (s *SoundRecommendLive) FindDramaAllowList(dramaID int64) []int64 {
	if dramaID == 0 {
		return nil
	}

	now := util.TimeNow().Unix()
	for _, d := range s.DramaAllowList {
		if d.DramaID != dramaID {
			continue
		}

		if d.StartTime <= now && (d.EndTime == 0 || now < d.EndTime) {
			return d.RoomAllowList
		}
	}
	return nil
}

// DefaultSoundRecommend 默认配置
func DefaultSoundRecommend() SoundRecommend {
	return SoundRecommend{
		Key: KeySoundRecommend,
		Live: &SoundRecommendLive{
			ScorePercent: 20,
			MessagePool: []string{
				"耳机党福利",
				"我想对你说",
				"声控党福利",
				"用声音治愈你",
				"你的虚拟恋人",
				"小可爱请查收",
				"千万不要点开！",
				"今日份声控福利",
				"让我陪在你身边",
				"戴好耳机偷偷听",
				"今日份声音恋人",
			},
			OffDuration: 3 * 24 * 3600 * 1000, // 3 天
		},
	}
}

// FindSoundRecommend 查询配置音频推荐配置
func FindSoundRecommend() (SoundRecommend, error) {
	sr := DefaultSoundRecommend()
	err := findParams(KeySoundRecommend, &sr)
	return sr, err
}

// RandomLiveMessage 随机一个直播推荐文案
func (sr SoundRecommend) RandomLiveMessage() string {
	if len(sr.Live.MessagePool) == 0 {
		return ""
	}
	idx := rand.Intn(len(sr.Live.MessagePool))
	return sr.Live.MessagePool[idx]
}

// DeleteSoundMessage 删除推荐语
func DeleteSoundMessage(messages []string) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	update := bson.M{"$pull": bson.M{"live.message_pool": bson.M{"$in": messages}}}
	_, err := Collection().UpdateOne(ctx, bson.M{"key": KeySoundRecommend}, update)
	if err != nil {
		return err
	}
	clearKeySoundRecommendCache()
	return nil
}

// AddSoundMessage 新增推荐语
func AddSoundMessage(messages []string) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	update := bson.M{"$push": bson.M{"live.message_pool": bson.M{"$each": messages}}}
	_, err := Collection().UpdateOne(ctx, bson.M{"key": KeySoundRecommend}, update)
	if err != nil {
		return err
	}
	clearKeySoundRecommendCache()
	return nil
}

func clearKeySoundRecommendCache() {
	cacheKey := keys.KeyParams1.Format(KeySoundRecommend)
	_, err := service.LRURedis.Del(cacheKey).Result()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
