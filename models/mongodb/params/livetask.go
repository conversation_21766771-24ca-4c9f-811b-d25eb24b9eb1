package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
)

// LiveTask 直播任务
type LiveTask struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Icon                string              `bson:"icon" json:"icon"`               // 直播间内直播任务图标
	IconActive          string              `bson:"icon_active" json:"icon_active"` // 直播间内直播任务图标
	GiftID              int64               `bson:"gift_id" json:"gift_id"`         // 赠送礼物 ID
	DailyListenDuration DailyListenDuration `bson:"daily_listen_duration" json:"daily_listen_duration"`

	IconURL       string `bson:"-" json:"-"`
	IconActiveURL string `bson:"-" json:"-"`
}

// DailyListenDuration 每日收听时长
type DailyListenDuration struct {
	NewUserFinishDuration int64 `bson:"new_user_finish_duration" json:"new_user_finish_duration"` // 新用户完成时间，单位：毫秒
	OldUserFinishDuration int64 `bson:"old_user_finish_duration" json:"old_user_finish_duration"` // 老用户完成时间，单位：毫秒
}

// DefaultLiveTask 默认配置
func DefaultLiveTask() LiveTask {
	return LiveTask{
		Key:        KeyLiveTask,
		Icon:       "oss://live/task/entrance.png",
		IconActive: "oss:///live/task/entrance.webp",
		GiftID:     10244,
		DailyListenDuration: DailyListenDuration{
			NewUserFinishDuration: 180000,
			OldUserFinishDuration: 300000,
		},
	}
}

func (t *LiveTask) afterLoad() {
	t.IconURL = storage.ParseSchemeURL(t.Icon)
	t.IconActiveURL = storage.ParseSchemeURL(t.IconActive)
}

// FindLiveTask 查询直播任务配置
func FindLiveTask() (*LiveTask, error) {
	task := DefaultLiveTask()
	err := findParams(KeyLiveTask, &task)
	if err != nil {
		return nil, err
	}
	task.afterLoad()
	return &task, nil
}
