package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MultiCombo 相关配置
type MultiCombo struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	ShowShortcutsStartTime int64 `bson:"show_shortcuts_start_time" json:"show_shortcuts_start_time"` // 秒级时间戳
	ShowShortcutsEndTime   int64 `bson:"show_shortcuts_end_time" json:"show_shortcuts_end_time"`     // 秒级时间戳

	DisableSendRoomIDs []int64 `bson:"disable_send_room_ids" json:"disable_send_room_ids"`
}

// DefaultMultiCombo 默认的 room combo 配置
func DefaultMultiCombo() MultiCombo {
	return MultiCombo{
		Key:                KeyMultiCombo,
		DisableSendRoomIDs: []int64{},
	}
}

// FindMultiCombo 查找 room combo 配置
func FindMultiCombo() (MultiCombo, error) {
	c := DefaultMultiCombo()
	err := findParams(KeyMultiCombo, &c)
	return c, err
}

// EnableShowShortcuts 是否开放直播间快捷一起送按钮
func (m MultiCombo) EnableShowShortcuts() bool {
	nowUnix := goutil.TimeNow().Unix()
	return m.ShowShortcutsStartTime <= nowUnix && nowUnix < m.ShowShortcutsEndTime
}

// EnableSend 直播间是否有一起送礼物资格
func (m MultiCombo) EnableSend(roomID int64) bool {
	return !goutil.HasElem(m.DisableSendRoomIDs, roomID)
}
