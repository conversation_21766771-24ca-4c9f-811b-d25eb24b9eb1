package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestStickerTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Sticker{}, "_id", "key", "superfans")
	kc.Check(SuperFansStickerPackage{}, "package_id", "unlock_start_time", "unlock_end_time", "unlock_trigger_num")
}

func TestFindSticker(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeySticker)
	require.NoError(service.LRURedis.Del(cacheKey).Err())
	sticker, err := FindSticker()
	require.NoError(err)
	assert.Equal(KeySticker, sticker.Key)

	sticker.SuperFans.UnlockTriggerNum = 100
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(sticker), 10*time.Second).Err()
	require.NoError(err)
	sticker, err = FindSticker()
	require.NoError(err)
	assert.EqualValues(100, sticker.SuperFans.UnlockTriggerNum)
}

func TestSticker_IsValidSuperFansUnlockTime(t *testing.T) {
	assert := assert.New(t)

	param := Sticker{
		Key: KeySticker,
		SuperFans: SuperFansStickerPackage{
			UnlockStartTime: 1,
			UnlockEndTime:   3,
		},
	}

	isValid := param.IsValidSuperFansUnlockTime(time.Unix(2, 0))
	assert.True(isValid)

	isValid = param.IsValidSuperFansUnlockTime(time.Unix(3, 0))
	assert.False(isValid)

	isValid = param.IsValidSuperFansUnlockTime(time.Unix(4, 0))
	assert.False(isValid)
}
