package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// RedPacket 礼物红包相关配置
type RedPacket struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Name           string  `bson:"name" json:"name"`                         // 礼物红包名称
	Price          int64   `bson:"price,omitempty" json:"price,omitempty"`   // 礼物价格
	PriceType      int     `bson:"price_type" json:"price_type"`             // 价格类型，0:固定单价 1:最低价格
	Icon           string  `bson:"icon" json:"icon"`                         // 礼物红包图
	Image          string  `bson:"image" json:"image"`                       // APP 直播间左上角玩法栏红包小图标
	BigIcon        string  `bson:"big_icon" json:"big_icon"`                 // 礼物红包大图
	DisableRoomIDs []int64 `bson:"disable_room_ids" json:"disable_room_ids"` // 禁止发红包直播间列表

	// 用于在 Android < 6.2.5 和 iOS < 6.2.5 的版本直接下发到 meta 接口
	IconActive   string `bson:"icon_active,omitempty" json:"icon_active,omitempty"`       // 礼物红包动态图
	CornerIcon   string `bson:"corner_icon,omitempty" json:"corner_icon,omitempty"`       // 礼物红包角标图
	Intro        string `bson:"intro,omitempty" json:"intro,omitempty"`                   // 礼物红包简介
	IntroIcon    string `bson:"intro_icon,omitempty" json:"intro_icon,omitempty"`         // 礼物红包简介图标
	IntroOpenURL string `bson:"intro_open_url,omitempty" json:"intro_open_url,omitempty"` // 礼物红包跳转链接
	Position     int    `bson:"position,omitempty" json:"position,omitempty"`             // 礼物红包显示位置

	WebOldVersion bool `bson:"web_old_version" json:"web_old_version"` // TODO: 是否兼容 web 老版本，后续 web 支持之后可以移除

	IconURL       string `bson:"-" json:"-"`
	ImageURL      string `bson:"-" json:"-"`
	BigIconURL    string `bson:"-" json:"-"`
	IconActiveURL string `bson:"-" json:"-"`
	CornerIconURL string `bson:"-" json:"-"`
	IntroIconURL  string `bson:"-" json:"-"`
}

// AfterLoad after load
func (rp *RedPacket) AfterLoad() {
	rp.IconURL = storage.ParseSchemeURL(rp.Icon)
	rp.ImageURL = storage.ParseSchemeURL(rp.Image)
	rp.BigIconURL = storage.ParseSchemeURL(rp.BigIcon)
	rp.IconActiveURL = storage.ParseSchemeURL(rp.IconActive)
	rp.CornerIconURL = storage.ParseSchemeURL(rp.CornerIcon)
	rp.IntroIconURL = storage.ParseSchemeURL(rp.IntroIcon)
}

// DefaultRedPacket 礼物红包默认配置
func DefaultRedPacket() RedPacket {
	return RedPacket{
		Key: KeyRedPacket,
	}
}

// FindRedPacket 查询礼物红包配置
func FindRedPacket() (RedPacket, error) {
	rp := DefaultRedPacket()
	err := findParams(rp.Key, &rp)
	rp.AfterLoad()
	return rp, err
}

// IsRoomDisabled 某直播间是否禁止发红包
func (rp *RedPacket) IsRoomDisabled(roomID int64) bool {
	return goutil.HasElem(rp.DisableRoomIDs, roomID)
}
