package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 扭蛋显示位置 bitmask
const (
	StatusGashaponShowInGifts       = iota + 1 // 显示在礼物栏
	StatusGashaponShowInInteractive            // 显示在玩法栏
)

// Gashapon 扭蛋配置
// json tag 用于缓存
type Gashapon struct {
	OID                  primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key                  string             `bson:"key,omitempty" json:"key,omitempty"`
	Name                 string             `bson:"name,omitempty" json:"name,omitempty"`
	Price                int64              `bson:"price" json:"price"`                                                       // 礼物价格
	Image                string             `bson:"image" json:"image"`                                                       // APP 直播间左上角玩法栏宝盒小图标
	InteractionLabelIcon string             `bson:"interaction_label_icon,omitempty" json:"interaction_label_icon,omitempty"` // 玩法栏角标图
	Icon                 string             `bson:"icon,omitempty" json:"icon,omitempty"`
	IconActive           string             `bson:"icon_active,omitempty" json:"icon_active,omitempty"`
	LabelIcon            string             `bson:"label_icon,omitempty" json:"label_icon,omitempty"`
	Status               goutil.BitMask     `bson:"status,omitempty" json:"status,omitempty"`
	OpenURL              string             `bson:"open_url,omitempty" json:"open_url,omitempty"`
	Intro                string             `bson:"intro" json:"intro"`
	IntroIcon            string             `bson:"intro_icon" json:"intro_icon"`
	IntroOpenURL         string             `bson:"intro_open_url" json:"intro_open_url"`
	Position             int                `bson:"position" json:"position"`
	MaxEnergy            int                `bson:"max_energy" json:"max_energy"` // 最大能量值
	Rule                 string             `bson:"rule" json:"rule"`             // 规则链接

	RewardLiveIcons     []string `bson:"reward_live_icons,omitempty" json:"reward_live_icons,omitempty"`         // 周榜奖励角标
	RewardBubbleID      int64    `bson:"reward_bubble_id,omitempty" json:"reward_bubble_id,omitempty"`           // 周榜飘屏气泡
	RewardNotifyMessage string   `bson:"reward_notify_message,omitempty" json:"reward_notify_message,omitempty"` // 周榜飘屏消息

	WebOldVersion         bool `bson:"web_old_version" json:"web_old_version"`                   // TODO: 是否兼容 web 老版本，后续 web 支持之后可以移除
	EnableNewMsgBroadcast bool `bson:"enable_new_msg_broadcast" json:"enable_new_msg_broadcast"` // 是否开启新消息广播

	ImageURL                string   `bson:"-" json:"-"`
	InteractionLabelIconURL string   `bson:"-" json:"-"`
	IconURL                 string   `bson:"-" json:"-"`
	IconActiveURL           string   `bson:"-" json:"-"`
	LabelIconURL            string   `bson:"-" json:"-"`
	IntroIconURL            string   `bson:"-" json:"-"`
	RewardLiveIconURLs      []string `bson:"-" json:"-"`
}

// AfterLoad after load
func (g *Gashapon) AfterLoad() {
	g.ImageURL = storage.ParseSchemeURL(g.Image)
	g.InteractionLabelIconURL = storage.ParseSchemeURL(g.InteractionLabelIcon)
	g.IconURL = storage.ParseSchemeURL(g.Icon)
	g.IconActiveURL = storage.ParseSchemeURL(g.IconActive)
	g.LabelIconURL = storage.ParseSchemeURL(g.LabelIcon)
	g.IntroIconURL = storage.ParseSchemeURL(g.IntroIcon)
	for i := range g.RewardLiveIcons {
		g.RewardLiveIconURLs = append(g.RewardLiveIconURLs, storage.ParseSchemeURL(g.RewardLiveIcons[i]))
	}
}

// DefaultGashapon 默认配置
func DefaultGashapon() Gashapon {
	return Gashapon{
		Key: KeyGashapon,
	}
}

// FindGashapon 查询超能魔方配置
func FindGashapon() (Gashapon, error) {
	sr := DefaultGashapon()
	err := findParams(sr.Key, &sr)
	sr.AfterLoad()
	return sr, err
}

// IsShowInGifts 是否显示在礼物栏
func (g *Gashapon) IsShowInGifts() bool {
	return g.Status.IsSet(StatusGashaponShowInGifts)
}

// IsShowInInteractive 是否显示在玩法栏
func (g *Gashapon) IsShowInInteractive() bool {
	return g.Status.IsSet(StatusGashaponShowInInteractive)
}
