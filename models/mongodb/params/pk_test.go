package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestPKTags(t *testing.T) {
	tags := []string{"_id", "key", "no_limit_room_ids"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(PK{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(PK{}, tags[1:]...)
}

func TestFindPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyPK)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	pk, err := FindPK()
	require.NoError(err)
	assert.Equal(KeyPK, pk.Key)
}

func TestIsNoLimitPKRoomID(t *testing.T) {
	assert := assert.New(t)

	pk := PK{Key: KeyPK, NoLimitPKRoomIDs: []int64{1, 2, 3}}
	assert.True(pk.IsNoLimitPKRoomID(1))
	assert.False(pk.IsNoLimitPKRoomID(4))
}
