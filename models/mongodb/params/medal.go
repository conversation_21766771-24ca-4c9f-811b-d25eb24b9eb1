package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Medal 粉丝勋章相关配置
type Medal struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	FirstDiscount MedalFirstDiscount `bson:"first_discount" json:"first_discount"`
}

// MedalFirstDiscount 购买首个粉丝勋章折扣信息
type MedalFirstDiscount struct {
	StartTime int64  `bson:"start_time" json:"start_time"`
	StatusTip string `bson:"status_tip" json:"status_tip"` // 关注/粉团按钮标签文案
	GiftTip   string `bson:"gift_tip" json:"gift_tip"`     // 粉丝勋章页礼物折扣文案
}

// DefaultMedal 默认的 Medal 配置
func DefaultMedal() Medal {
	return Medal{
		Key: KeyMedal,
	}
}

// FindMedal 查找 Medal 配置
func FindMedal() (Medal, error) {
	medal := DefaultMedal()
	err := findParams(KeyMedal, &medal)
	return medal, err
}

// IsFirstMedalDiscountEnable 是否有首次获取粉丝牌折扣
func (m *Medal) IsFirstMedalDiscountEnable(equip *goutil.Equipment) bool {
	// WORKAROUND: 安卓 >= 6.3.2, iOS >= 6.3.2, 才下发粉丝牌折扣信息
	if equip.IsOldApp(goutil.AppVersions{
		Android: "6.3.2",
		IOS:     "6.3.2",
	}) {
		return false
	}
	timeNowUnix := goutil.TimeNow().Unix()
	if m.FirstDiscount.StartTime == 0 || timeNowUnix < m.FirstDiscount.StartTime {
		return false
	}
	return true
}
