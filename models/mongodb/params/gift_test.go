package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGiftTags(t *testing.T) {
	tags := []string{"_id", "key", "closed_room_allow_draw_gift"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Gift{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Gift{}, tags[1:]...)
}

func TestDefaultGift(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(Gift{
		Key:                     KeyGift,
		ClosedRoomAllowDrawGift: false,
	}, DefaultGift())
}

func TestFindGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyGift)
	require.NoError(service.LRURedis.Set(cacheKey,
		`{"key":"gift","closed_room_allow_draw_gift":false}`, time.Minute).Err())
	gift, err := FindGift()
	require.NoError(err)
	assert.False(gift.ClosedRoomAllowDrawGift)
	ClearCache(KeyGift)
}
