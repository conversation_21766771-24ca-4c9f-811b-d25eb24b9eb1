package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/service/storage"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LuckyBagConfig 福袋配置
type LuckyBagConfig struct {
	OID            primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key            string             `bson:"key,omitempty" json:"key,omitempty"`
	OpenTime       int64              `bson:"open_time,omitempty" json:"open_time,omitempty"`               // 开启时间，0 为不限制（秒级时间戳）
	Name           string             `bson:"name,omitempty" json:"name,omitempty"`                         // 福袋名称
	Image          string             `bson:"image,omitempty" json:"image,omitempty"`                       // 福袋图标
	NewImage       string             `bson:"new_image,omitempty" json:"new_image,omitempty"`               // 新福袋图标
	BigImage       string             `bson:"big_image,omitempty" json:"big_image,omitempty"`               // 福袋大图
	LuckyBagDrama  *LuckyBag          `bson:"lucky_bag_drama,omitempty" json:"lucky_bag_drama,omitempty"`   // 剧集福袋
	LuckyBagEntity *LuckyBag          `bson:"lucky_bag_entity,omitempty" json:"lucky_bag_entity,omitempty"` // 实物福袋
	HighRiskScore  float64            `bson:"high_risk_score,omitempty" json:"high_risk_score,omitempty"`   // 高风险分数阈值

	ImageURL    string `bson:"-" json:"-"`
	NewImageURL string `bson:"-" json:"-"`
	BigImageURL string `bson:"-" json:"-"`
}

// LuckyBag 福袋
type LuckyBag struct {
	Name               string              `bson:"name" json:"name"`                                                     // 福袋类型名称
	MaxPrizeNum        int                 `bson:"max_prize_num" json:"max_prize_num"`                                   // 最大奖品数量
	Keyword            string              `bson:"keyword" json:"keyword"`                                               // 默认参与口令
	InitiateLimitDaily int                 `bson:"initiate_limit_daily,omitempty" json:"initiate_limit_daily,omitempty"` // 当日可发福袋最大数量，0 的时候无限制
	Targets            []LuckyBagTarget    `bson:"targets" json:"targets"`
	Rewards            []LuckyBagReward    `bson:"rewards" json:"rewards"`
	Countdowns         []LuckyBagCountdown `bson:"countdowns" json:"countdowns"`
}

// LuckyBagTarget 参与对象类型
type LuckyBagTarget struct {
	Type int    `bson:"type" json:"type"`
	Name string `bson:"name" json:"name"`
}

// LuckyBagReward 福袋奖励类型
type LuckyBagReward struct {
	Type      int    `bson:"type" json:"type"`
	Name      string `bson:"name" json:"name"`
	Intro     string `bson:"intro,omitempty" json:"intro,omitempty"`
	PrizeIcon string `bson:"prize_icon,omitempty" json:"prize_icon,omitempty"` // 奖品图标

	PrizeIconURL string `bson:"-" json:"-"`
}

// LuckyBagCountdown 福袋倒计时
type LuckyBagCountdown struct {
	MinPrice int64 `bson:"min_price" json:"min_price"` // 最小金额（钻）
	Duration int64 `bson:"duration" json:"duration"`   // 毫秒
}

func defaultLuckyBagDramaConfig() *LuckyBag {
	return &LuckyBag{
		Name:               "剧集福袋",
		MaxPrizeNum:        50,
		Keyword:            "点点关注抽福袋", // TODO: 口令待确认
		InitiateLimitDaily: 10,
		Targets: []LuckyBagTarget{
			{
				Type: luckybag.TargetTypeAll,
				Name: "所有人",
			},
			{
				Type: luckybag.TargetTypeFollow,
				Name: "关注",
			},
		},
		Rewards: []LuckyBagReward{
			{
				Type: luckybag.RewardTypeDrama,
				Name: "剧集奖励",
			},
		},
		Countdowns: []LuckyBagCountdown{
			{
				MinPrice: 0,      // 最小金额（钻）
				Duration: 180000, // 毫秒
			},
			{
				MinPrice: 0,      // 最小金额（钻）
				Duration: 300000, // 毫秒
			},
			{
				MinPrice: 200,    // 最小金额（钻）
				Duration: 600000, // 毫秒
			},
			{
				MinPrice: 1000,   // 最小金额（钻）
				Duration: 900000, // 毫秒
			},
		},
	}
}

func defaultLuckyBagEntityConfig() *LuckyBag {
	return &LuckyBag{
		Name:        "实物福袋",
		MaxPrizeNum: 50,
		Keyword:     "点点关注抽福袋", // TODO: 口令待确认
		Targets: []LuckyBagTarget{
			{
				Type: luckybag.TargetTypeAll,
				Name: "所有人",
			},
			{
				Type: luckybag.TargetTypeFollow,
				Name: "关注",
			},
			{
				Type: luckybag.TargetTypeMedal,
				Name: "粉丝勋章",
			},
			{
				Type: luckybag.TargetTypeSuperFan,
				Name: "超级粉丝",
			},
		},
		Rewards: []LuckyBagReward{
			{
				Type:      luckybag.RewardTypeEntityPersonal,
				Name:      "个人周边",
				Intro:     "对奖励内容进行备注说明，如“签名照”",
				PrizeIcon: "oss://live/luckybag/prize-personal.png",
			},
			{
				Type:      luckybag.RewardTypeEntityDrama,
				Name:      "剧集周边",
				Intro:     "对奖励内容进行备注说明，如“签名照”",
				PrizeIcon: "oss://live/luckybag/prize-drama.png",
			},
		},
		Countdowns: []LuckyBagCountdown{
			{
				MinPrice: 0,      // 最小金额（钻）
				Duration: 180000, // 毫秒
			},
			{
				MinPrice: 0,      // 最小金额（钻）
				Duration: 300000, // 毫秒
			},
			{
				MinPrice: 0,      // 最小金额（钻）
				Duration: 600000, // 毫秒
			},
			{
				MinPrice: 0,      // 最小金额（钻）
				Duration: 900000, // 毫秒
			},
		},
	}
}

// 默认福袋配置
func defaultLuckyBagConfig() *LuckyBagConfig {
	return &LuckyBagConfig{
		Key:            KeyLuckyBag,
		Name:           "喵喵福袋",
		Image:          "oss://live/luckybag/icon-small.png",
		NewImage:       "oss://live/luckybag/icon-v2.png",
		BigImage:       "oss://live/luckybag/icon-big.png",
		LuckyBagDrama:  defaultLuckyBagDramaConfig(),
		LuckyBagEntity: defaultLuckyBagEntityConfig(),
	}
}

func (l *LuckyBagConfig) afterLoad() {
	l.ImageURL = storage.ParseSchemeURL(l.Image)
	l.NewImageURL = storage.ParseSchemeURL(l.NewImage)
	l.BigImageURL = storage.ParseSchemeURL(l.BigImage)
	if l.LuckyBagEntity != nil {
		for i, v := range l.LuckyBagEntity.Rewards {
			l.LuckyBagEntity.Rewards[i].PrizeIconURL = storage.ParseSchemeURL(v.PrizeIcon)
		}
	}
}

// IsOpen 是否开启
func (l *LuckyBagConfig) IsOpen() bool {
	now := goutil.TimeNow().Unix()
	return l.OpenTime <= now
}

// FindLuckyBag 查找福袋配置
func FindLuckyBag() (*LuckyBagConfig, error) {
	config := defaultLuckyBagConfig()
	err := findParams(KeyLuckyBag, &config)
	if err != nil {
		return nil, err
	}

	config.afterLoad()
	return config, err
}

// IsRewardTypeValid 奖励类型是否有效
func (l *LuckyBag) IsRewardTypeValid(rewardType int) bool {
	for _, v := range l.Rewards {
		if v.Type == rewardType {
			return true
		}
	}
	return false
}

// IsTargetTypeValid 参与目标类型是否有效
func (l *LuckyBag) IsTargetTypeValid(targetType int) bool {
	for _, v := range l.Targets {
		if v.Type == targetType {
			return true
		}
	}
	return false
}

// IsCountdownValid 倒计时是否有效
func (l *LuckyBag) IsCountdownValid(countdown, price int64) bool {
	for _, v := range l.Countdowns {
		if v.Duration == countdown && v.MinPrice <= price {
			return true
		}
	}
	return false
}

// PrizeIconURL 奖品图标
// 剧集福袋奖品图标为剧集封面
func (l *LuckyBag) PrizeIconURL(rewardType int) (string /* oss 地址 */, string /* http 地址 */) {
	for _, v := range l.Rewards {
		if v.Type == rewardType {
			return v.PrizeIcon, v.PrizeIconURL
		}
	}
	return "", ""
}
