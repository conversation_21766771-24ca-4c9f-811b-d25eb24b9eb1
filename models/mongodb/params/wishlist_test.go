package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestWishListConfigTags(t *testing.T) {
	keys := []string{"_id", "key", "open_time", "name"}
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(WishListConfig{}, keys...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(WishListConfig{}, keys[1:]...)
}

func TestFindWishList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyParams1.Format(KeyWishList)
	require.NoError(service.LRURedis.Del(key).Err())
	wlc, err := FindWishList()
	require.NoError(err)
	assert.Equal(KeyWishList, wlc.Key)
	wlc.Name = "心愿单"

	wlc.OpenTime = util.TimeNow().Unix()
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(wlc), 10*time.Second).Err())
	wlc2, err := FindWishList()
	require.NoError(err)
	assert.Equal(wlc.Name, wlc2.Name)
	assert.Equal(wlc.OpenTime, wlc2.OpenTime)
}

func TestWishlistConfig_IsOpen(t *testing.T) {
	assert := assert.New(t)

	c := WishListConfig{
		OpenTime: util.TimeNow().Unix(),
	}
	assert.True(c.IsOpen())
	c.OpenTime = util.TimeNow().Add(time.Second).Unix()
	assert.False(c.IsOpen())
}
