package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMultiConnectTags(t *testing.T) {
	tags := []string{"_id", "key", "start_time", "entry_icon",
		"beta_start_time", "beta_end_time", "beta_room_ids"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(MultiConnect{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MultiConnect{}, tags[1:]...)
}

func TestFindMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyMultiConnect)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	pk, err := FindPK()
	require.NoError(err)
	assert.Equal(KeyPK, pk.Key)
}

func TestMultiConnect_IsFullOpen(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("未配置", func(t *testing.T) {
		var mc MultiConnect
		isFullOpen := mc.IsFullOpen(now)
		assert.False(isFullOpen)
	})
	t.Run("全面开放", func(t *testing.T) {
		mc := MultiConnect{
			StartTime: now.Add(-time.Hour).Unix(),
		}
		isFullOpen := mc.IsFullOpen(now)
		assert.True(isFullOpen)
	})
}

func TestMultiConnect_isInBetaTimeRange(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("未配置白名单生效时间", func(t *testing.T) {
		var mc MultiConnect
		isInBetaTimeRange := mc.isInBetaTimeRange(now)
		assert.True(isInBetaTimeRange)
	})
	t.Run("在白名单生效时间内", func(t *testing.T) {
		mc := MultiConnect{
			BetaStartTime: now.Add(-time.Hour).Unix(),
			BetaEndTime:   now.Add(time.Hour).Unix(),
		}
		isInBetaTimeRange := mc.isInBetaTimeRange(now)
		assert.True(isInBetaTimeRange)
	})
	t.Run("在白名单生效时间前", func(t *testing.T) {
		mc := MultiConnect{
			BetaStartTime: now.Add(time.Hour).Unix(),
			BetaEndTime:   now.Add(time.Hour * 2).Unix(),
		}
		isInBetaTimeRange := mc.isInBetaTimeRange(now)
		assert.False(isInBetaTimeRange)
	})
	t.Run("在白名单生效时间后", func(t *testing.T) {
		mc := MultiConnect{
			BetaStartTime: now.Add(-time.Hour * 2).Unix(),
			BetaEndTime:   now.Add(-time.Hour).Unix(),
		}
		isInBetaTimeRange := mc.isInBetaTimeRange(now)
		assert.False(isInBetaTimeRange)
	})
}

func TestMultiConnect_isBetaRoom(t *testing.T) {
	assert := assert.New(t)

	t.Run("未配置白名单", func(t *testing.T) {
		var mc MultiConnect
		isBetaRoom := mc.IsBetaRoom(1)
		assert.False(isBetaRoom)
	})
	t.Run("属于新功能白名单直播间", func(t *testing.T) {
		mc := MultiConnect{
			BetaRoomIDs: []int64{1, 2, 3},
		}
		isBetaRoom := mc.IsBetaRoom(1)
		assert.True(isBetaRoom)
	})
}

func TestMultiConnect_IsOpen(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("未配置", func(t *testing.T) {
		var mc MultiConnect
		isOpen := mc.IsOpen(now, 1)
		assert.False(isOpen)
	})
	t.Run("全面开放", func(t *testing.T) {
		mc := MultiConnect{
			BetaRoomIDs: []int64{1, 2, 3},
			StartTime:   now.Add(-time.Hour).Unix(),
		}
		isOpen := mc.IsOpen(now, 4)
		assert.True(isOpen)
	})
}
