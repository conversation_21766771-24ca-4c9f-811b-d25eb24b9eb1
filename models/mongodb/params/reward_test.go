package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestFindReward(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyReward)
	err := service.LRURedis.Del(cacheKey).Err()
	require.NoError(err)

	r, err := FindReward()
	require.NoError(err)
	assert.EqualValues(20, r.LiveNewUserRewardID)

	// 测试缓存获取
	r, err = FindReward()
	require.NoError(err)
	assert.EqualValues(20, r.LiveNewUserRewardID)
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.NotZero(exists)
}
