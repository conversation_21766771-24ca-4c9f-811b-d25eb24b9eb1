package params

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Global 全局通用配置
type Global struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Maintain *GlobalMaintain `bson:"maintain,omitempty" json:"maintain,omitempty"`
}

// GlobalMaintain .
type GlobalMaintain struct {
	StartTime int64 `bson:"start_time" json:"start_time"` // 开始时间
	EndTime   int64 `bson:"end_time" json:"end_time"`     // 结束时间
}

// DefaultGlobal 默认配置
func DefaultGlobal() Global {
	return Global{
		Key: KeyGlobal,
		Maintain: &GlobalMaintain{
			StartTime: time.Date(2025, 6, 3, 0, 0, 0, 0, time.Local).Unix(),
			EndTime:   time.Date(2025, 6, 6, 0, 0, 0, 0, time.Local).Unix(),
		},
	}
}

// FindGlobal 查询 Global 配置
func FindGlobal() (Global, error) {
	global := DefaultGlobal()
	err := findParams(KeyGlobal, &global)
	return global, err
}

// IsUnderMaintenance 检查是否在维护时间内
func (g Global) IsUnderMaintenance() bool {
	if g.Maintain == nil {
		return false
	}
	now := goutil.TimeNow().Unix()
	return now >= g.Maintain.StartTime && now < g.Maintain.EndTime
}

// MaintainMsg 返回维护消息
func (gm GlobalMaintain) MaintainMsg() string {
	return fmt.Sprintf("功能维护中，预计 %s 恢复使用", time.Unix(gm.EndTime, 0).Format("01-02 15:04"))
}
