package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
)

func TestDefaultTestConfig(t *testing.T) {
	assert.Equal(t, TestConfig{Key: KeyTestConfig}, DefaultTestConfig())
}

func TestFindTestConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	err := Collection().FindOneAndUpdate(ctx, bson.M{"key": KeyTestConfig}, bson.M{
		"$set": bson.M{
			"room_ids": []int64{1},
			"user_ids": []int64{1},
		},
	}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(err)

	testConfig, err := FindTestConfig()
	require.NoError(err)
	assert.Equal([]int64{1}, testConfig.RoomIDs)
	assert.Equal([]int64{1}, testConfig.UserIDs)
}

func TestIsTestUser(t *testing.T) {
	config := TestConfig{
		UserIDs: []int64{1},
	}
	assert.True(t, config.IsTestUser(1))
	assert.False(t, config.IsTestUser(2))
}

func TestIsTestRoom(t *testing.T) {
	config := TestConfig{
		RoomIDs: []int64{1},
	}
	assert.True(t, config.IsTestRoom(1))
	assert.False(t, config.IsTestRoom(2))
}
