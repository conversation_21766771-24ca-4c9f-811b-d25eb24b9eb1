package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestRedPacketTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(RedPacket{}, "_id", "key", "name", "price", "price_type", "icon", "image", "big_icon", "icon_active", "corner_icon", "intro", "intro_icon", "intro_open_url", "position", "disable_room_ids", "web_old_version")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RedPacket{}, "key", "name", "price", "price_type", "icon", "image", "big_icon", "icon_active", "corner_icon", "intro", "intro_icon", "intro_open_url", "position", "disable_room_ids", "web_old_version")
}

func TestFindRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyRedPacket)
	require.NoError(service.LRURedis.Del(cacheKey).Err())
	packet, err := FindRedPacket()
	require.NoError(err)
	assert.Equal(KeyRedPacket, packet.Key)

	packet.Intro = "这是礼物红包"
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(packet), 10*time.Second).Err()
	require.NoError(err)
	packet, err = FindRedPacket()
	require.NoError(err)
	assert.Equal("这是礼物红包", packet.Intro)
}

func TestRedPacket_IsRoomDisabled(t *testing.T) {
	assert := assert.New(t)

	rp := RedPacket{DisableRoomIDs: []int64{1, 2}}
	assert.True(rp.IsRoomDisabled(1))
	assert.False(rp.IsRoomDisabled(22))

	rp = RedPacket{DisableRoomIDs: nil}
	assert.False(rp.IsRoomDisabled(1))
}
