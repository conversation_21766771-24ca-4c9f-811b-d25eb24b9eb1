package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestLuckyBoxTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LuckyBox{}, "_id", "key", "open_time", "status", "name", "price", "price_type", "icon", "image", "interaction_label_icon", "icon_active",
		"label_icon", "intro", "intro_icon", "intro_open_url", "position", "allow_room_ids")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LuckyBox{}, "key", "open_time", "status", "name", "price", "price_type", "icon", "image", "interaction_label_icon", "icon_active", "label_icon", "intro",
		"intro_icon", "intro_open_url", "position", "allow_room_ids")
}

func TestFindLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyLuckyBox)
	require.NoError(service.LRURedis.Del(cacheKey).Err())
	box, err := FindLuckyBox()
	require.NoError(err)
	assert.Equal(KeyLuckyBox, box.Key)

	box.Intro = "这是礼物宝盒"
	require.NoError(service.LRURedis.Set(cacheKey, tutil.SprintJSON(box), 10*time.Second).Err())
	box, err = FindLuckyBox()
	require.NoError(err)
	assert.Equal("这是礼物宝盒", box.Intro)
}

func TestLuckyBox_IsOpen(t *testing.T) {
	assert := assert.New(t)

	c := LuckyBox{
		OpenTime: util.TimeNow().Unix(),
	}
	assert.True(c.IsOpen())
	c.OpenTime = util.TimeNow().Add(time.Second).Unix()
	assert.False(c.IsOpen())
}

func TestLuckyBox_IsAllowRoom(t *testing.T) {
	assert := assert.New(t)

	testRoomID := int64(1003)
	c := LuckyBox{}
	assert.False(c.IsAllowRoom(testRoomID))

	c.AllowRoomIDs = []int64{}
	assert.False(c.IsAllowRoom(testRoomID))

	c.AllowRoomIDs = []int64{1001, 1002}
	assert.False(c.IsAllowRoom(testRoomID))

	c.AllowRoomIDs = []int64{1001, 1002, 1003, 1004}
	assert.True(c.IsAllowRoom(testRoomID))
}
