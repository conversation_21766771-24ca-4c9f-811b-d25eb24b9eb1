package params

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SuperFansStickerPackage 超粉表情包相关配置
type SuperFansStickerPackage struct {
	PackageID        int64 `bson:"package_id" json:"package_id"`                 // 表情包 ID
	UnlockStartTime  int64 `bson:"unlock_start_time" json:"unlock_start_time"`   // 解锁开始时间，单位：秒
	UnlockEndTime    int64 `bson:"unlock_end_time" json:"unlock_end_time"`       // 解锁结束时间，单位：秒
	UnlockTriggerNum int64 `bson:"unlock_trigger_num" json:"unlock_trigger_num"` // 触发解锁数量
}

// Sticker 表情相关配置
type Sticker struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	SuperFans SuperFansStickerPackage `bson:"superfans" json:"superfans"` // 超粉表情包
}

// DefaultSticker 表情默认配置
func DefaultSticker() Sticker {
	return Sticker{
		Key: KeySticker,
		SuperFans: SuperFansStickerPackage{
			PackageID:        1,
			UnlockStartTime:  time.Date(2023, 5, 19, 20, 0, 0, 0, time.Local).Unix(),
			UnlockEndTime:    time.Date(2023, 5, 29, 23, 0, 0, 0, time.Local).Unix(),
			UnlockTriggerNum: 30,
		},
	}
}

// FindSticker 查询表情配置
func FindSticker() (Sticker, error) {
	s := DefaultSticker()
	err := findParams(s.Key, &s)
	return s, err
}

// IsValidSuperFansUnlockTime 是否在超粉表情包解锁时间内
func (s *Sticker) IsValidSuperFansUnlockTime(when time.Time) bool {
	return s.SuperFans.UnlockStartTime <= when.Unix() && s.SuperFans.UnlockEndTime > when.Unix()
}
