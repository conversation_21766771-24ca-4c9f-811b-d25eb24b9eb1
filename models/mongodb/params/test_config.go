package params

import (
	"slices"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TestConfig 测试相关配置
type TestConfig struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	UserIDs []int64 `bson:"user_ids" json:"user_ids"`
	RoomIDs []int64 `bson:"room_ids" json:"room_ids"`
}

// DefaultTestConfig 默认的测试配置
func DefaultTestConfig() TestConfig {
	return TestConfig{
		Key: KeyTestConfig,
	}
}

// FindTestConfig 查找测试配置
func FindTestConfig() (TestConfig, error) {
	p := DefaultTestConfig()
	err := findParams(KeyTestConfig, &p)
	return p, err
}

// IsTestUser 是否是测试用户
func (t TestConfig) IsTestUser(userID int64) bool {
	return slices.Contains(t.UserIDs, userID)
}

// IsTestRoom 是否是测试房间
func (t TestConfig) IsTestRoom(roomID int64) bool {
	return slices.Contains(t.RoomIDs, roomID)
}
