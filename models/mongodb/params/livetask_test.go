package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestFindLiveTask(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyLiveTask)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	expected := DefaultLiveTask()
	expected.IconURL = "https://static-test.missevan.com/live/task/entrance.png"
	expected.IconActiveURL = "https://static-test.missevan.com//live/task/entrance.webp"

	task, err := FindLiveTask()
	require.NoError(err)
	assert.Equal(expected, *task)
}
