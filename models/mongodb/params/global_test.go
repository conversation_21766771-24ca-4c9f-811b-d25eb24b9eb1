package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGlobal_Tags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Global{}, "_id", "key", "maintain")
	kc.Check(GlobalMaintain{}, "start_time", "end_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Global{}, "key", "maintain")
	kc.Check(GlobalMaintain{}, "start_time", "end_time")
}

func TestDefaultGlobal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	global := DefaultGlobal()
	assert.Equal("global", global.Key)
	require.NotNil(global.Maintain)
	assert.NotZero(global.Maintain.StartTime)
	assert.NotZero(global.Maintain.EndTime)
}

func TestFindGlobal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	global, err := FindGlobal()
	assert.NoError(err)
	assert.Equal("global", global.Key)
	require.NotNil(global.Maintain)
	assert.NotZero(global.Maintain.StartTime)
	assert.NotZero(global.Maintain.EndTime)
}

func TestGlobal_IsUnderMaintenance(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	horn, err := FindGlobal()
	assert.NoError(err)

	horn.Maintain.StartTime = 0
	horn.Maintain.EndTime = now.Add(3 * time.Hour).Unix()
	assert.True(horn.IsUnderMaintenance())

	horn.Maintain.StartTime = now.Add(3 * time.Hour).Unix()
	horn.Maintain.EndTime = now.Add(6 * time.Hour).Unix()
	assert.False(horn.IsUnderMaintenance())

	horn.Maintain.StartTime = now.Add(-3 * time.Hour).Unix()
	horn.Maintain.EndTime = now.Add(-6 * time.Hour).Unix()
	assert.False(horn.IsUnderMaintenance())
}

func TestGlobalMaintain_MaintainMsg(t *testing.T) {
	assert := assert.New(t)

	gm := GlobalMaintain{
		EndTime: time.Date(2025, 6, 6, 0, 0, 0, 0, time.Local).Unix(),
	}
	assert.Equal("功能维护中，预计 06-06 00:00 恢复使用", gm.MaintainMsg())
}
