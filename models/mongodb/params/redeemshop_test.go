package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRedeemShopTags(t *testing.T) {
	keys := []string{"_id", "key", "name", "icon", "web_icon", "shop_url", "show_time"}
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(RedeemShop{}, keys...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RedeemShop{}, keys[1:]...)
}

func TestRedeemShop_AfterLoad(t *testing.T) {
	assert := assert.New(t)

	rs := RedeemShop{Icon: "11111", WebIcon: "22222"}
	rs.afterLoad()
	assert.Equal("https://static-test.missevan.com/11111", rs.IconURL)
	assert.Equal("https://static-test.missevan.com/22222", rs.WebIconURL)
}

func TestFindRedeemShop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rs, err := FindRedeemShop()
	require.NoError(err)
	assert.Equal(KeyRedeemShop, rs.Key)

	rs.Name = "万事屋"
	rs.Icon = "oss://shop/yorozuya.png"
	rs.ShopURL = "https://www.missevan.com/some_url_to_shop"
	key := keys.KeyParams1.Format(rs.Key)
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(rs), 10*time.Second).Err())

	rs2, err := FindRedeemShop()
	require.NoError(err)
	assert.Equal(rs.Name, rs2.Name)
	assert.Equal(rs.Icon, rs2.Icon)
	assert.Equal(rs.ShopURL, rs2.ShopURL)
	require.NoError(service.LRURedis.Del(key).Err())
}

func TestFindPrivilegeShop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rs, err := FindPrivilegeShop()
	require.NoError(err)
	assert.Equal(KeyPrivilegeShop, rs.Key)

	rs.Name = "星享馆"
	rs.Icon = "oss://shop/stellaris.png"
	rs.ShopURL = "https://www.missevan.com/some_url_to_shop"

	key := keys.KeyParams1.Format(rs.Key)
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(rs), 10*time.Second).Err())

	rs2, err := FindPrivilegeShop()
	require.NoError(err)
	assert.Equal(rs.Name, rs2.Name)
	assert.Equal(rs.Icon, rs2.Icon)
	assert.Equal(rs.ShopURL, rs2.ShopURL)
	require.NoError(service.LRURedis.Del(key).Err())
}

func TestRedeemShop_IsShow(t *testing.T) {
	assert := assert.New(t)

	s := RedeemShop{ShopURL: "testURL"}
	assert.False(s.IsShow())

	s = RedeemShop{Name: "testName"}
	assert.False(s.IsShow())

	s = RedeemShop{Name: "testName", ShopURL: "testURL", ShowTime: goutil.TimeNow().Add(time.Minute).Unix()}
	assert.False(s.IsShow())

	s = RedeemShop{Name: "testName", ShopURL: "testURL"}
	assert.True(s.IsShow())
}
