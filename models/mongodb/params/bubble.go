package params

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Bubble 飘屏相关配置
type Bubble struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	Horns            []*HornBubble `bson:"horns" json:"horns"`
	CustomHornBubble *HornBubble   `bson:"custom_horn_bubble" json:"custom_horn_bubble"`
}

// HornBubble 喇叭飘屏相关配置
type HornBubble struct {
	Usage            string            `bson:"usage,omitempty" json:"-"`                     // 只用于显示当前配置用途
	StartTime        int64             `bson:"start_time" json:"start_time"`                 // 开始时间（单位：秒），若为 0 表示永久有效
	EndTime          int64             `bson:"end_time" json:"end_time"`                     // 结束时间（单位：秒），若为 0 表示永久有效
	HornMsgTemplate  string            `bson:"horn_msg_template" json:"horn_msg_template"`   // 喇叭消息模板
	NobleBubbles     []NobleBubble     `bson:"noble_bubbles" json:"noble_bubbles"`           // 贵族气泡（包含贵族和上神）
	BlackCardBubbles []BlackCardBubble `bson:"black_card_bubbles" json:"black_card_bubbles"` // 黑卡气泡
}

// BlackCardBubble 黑卡气泡相关配置
type BlackCardBubble struct {
	Level    int    `bson:"level" json:"level"`
	BubbleID int64  `bson:"bubble_id" json:"bubble_id"`
	Icon     string `bson:"icon" json:"icon"`
}

// NobleBubble 贵族气泡相关配置
type NobleBubble struct {
	Type     int    `bson:"type" json:"type"`
	Level    int    `bson:"level" json:"level"`
	BubbleID int64  `bson:"bubble_id" json:"bubble_id"`
	Icon     string `bson:"icon" json:"icon"`
}

func defaultHornBubble() *HornBubble {
	return &HornBubble{
		Usage:           "默认喇叭贵族飘屏配置",
		StartTime:       0, // 永久生效
		EndTime:         0, // 永久生效
		HornMsgTemplate: `<font color="${highlight_color}">${username}</font><font color="${normal_color}">：${message}（</font><font color="${highlight_color}">${creator_username}</font><font color="${normal_color}">的直播间）</font>`,
		NobleBubbles: []NobleBubble{
			{
				Type:     vip.TypeLiveNoble,
				Level:    4,
				BubbleID: 84,
				Icon:     "oss://live/bubbles/notify/icons/84.png",
			},
			{
				Type:     vip.TypeLiveNoble,
				Level:    5,
				BubbleID: 85,
				Icon:     "oss://live/bubbles/notify/icons/85.png",
			},
			{
				Type:     vip.TypeLiveNoble,
				Level:    6,
				BubbleID: 86,
				Icon:     "oss://live/bubbles/notify/icons/86.png",
			},
			{
				Type:     vip.TypeLiveNoble,
				Level:    7,
				BubbleID: 87,
				Icon:     "oss://live/bubbles/notify/icons/87.png",
			},
			{
				Type:     vip.TypeLiveHighness,
				Level:    1,
				BubbleID: 90,
				Icon:     "oss://live/bubbles/notify/icons/90.png",
			},
		},
		BlackCardBubbles: []BlackCardBubble{
			{
				Level:    liveuserblackcard.MinBlackCardLevelHasHorn,
				BubbleID: 103,
				Icon:     "oss://live/bubbles/notify/icons/blackcard3.png",
			},
			{
				Level:    liveuserblackcard.BlackCardLevelHasLiveHorn,
				BubbleID: 104,
				Icon:     "oss://live/bubbles/notify/icons/blackcard4.png",
			},
		},
	}
}

// DefaultBubble 默认配置
func DefaultBubble() Bubble {
	return Bubble{
		Key: KeyBubble,
		Horns: []*HornBubble{
			defaultHornBubble(),
		},
	}
}

// FindBubble 查询配置飘屏配置
func FindBubble() (Bubble, error) {
	bubble := DefaultBubble()
	err := findParams(KeyBubble, &bubble)
	return bubble, err
}

// FindNobleBubble 查询用户对应贵族气泡
func FindNobleBubble(uv *vip.UserVip) (*NobleBubble, error) {
	bp, err := FindBubble()
	if err != nil {
		return nil, err
	}
	hornBubble := bp.FindHornBubble(goutil.TimeNow())
	level := 0
	nobleType := 0
	if uv == nil || ((uv.Type == vip.TypeLiveNoble || uv.Type == vip.TypeLiveTrialNoble) && uv.Level < vip.NobleLevel4) {
		// 当前等级无气泡，展示最低拥有全站喇叭的等级（大咖）气泡 ID
		level = vip.NobleLevel4
		// 体验贵族普通贵族气泡 ID 一致，所以这里传入相同 type
		nobleType = vip.TypeLiveNoble
	} else {
		level = uv.Level
		nobleType = uv.Type
	}
	bubbleInfo := hornBubble.UserVipNotifyBubble(nobleType, level)
	return bubbleInfo, nil
}

// FindBlackCardBubble 查询对应黑卡会员气泡 ID
func FindBlackCardBubble(userBlackCard *liveuserblackcard.UserBlackCardInfo) (*BlackCardBubble, error) {
	bp, err := FindBubble()
	if err != nil {
		return nil, err
	}
	hornBubble := bp.FindHornBubble(goutil.TimeNow())
	level := 0
	if userBlackCard == nil || userBlackCard.Level < liveuserblackcard.MinBlackCardLevelHasHorn {
		// 当前等级无气泡，展示最低拥有全站喇叭的等级（3 级）气泡 ID
		level = liveuserblackcard.MinBlackCardLevelHasHorn
	} else {
		level = userBlackCard.Level
	}
	bubbleInfo := hornBubble.BlackCardNotifyBubble(level)
	return bubbleInfo, nil
}

// FindHornBubble 查询喇叭飘屏配置
func (b *Bubble) FindHornBubble(when time.Time) *HornBubble {
	// 最后一位默认是最新添加的，故从末尾开始循环
	for i := len(b.Horns) - 1; i >= 0; i-- {
		horn := b.Horns[i]
		if horn == nil {
			logger.Error("params bubble 配置异常，存在 nil 的 horn 配置")
			// PASS
			continue
		}
		if horn.IsActive(when) {
			return horn
		}
	}
	return defaultHornBubble()
}

// UserVipNotifyBubbleID 喇叭贵族气泡 ID
func (hb *HornBubble) UserVipNotifyBubbleID(nobleType, level int) int64 {
	// 体验贵族普通贵族气泡 ID 一致
	if nobleType == vip.TypeLiveTrialNoble {
		nobleType = vip.TypeLiveNoble
	}
	for _, bubble := range hb.NobleBubbles {
		if bubble.Type == nobleType && bubble.Level == level {
			return bubble.BubbleID
		}
	}
	return 0
}

// UserVipNotifyBubble 获取喇叭贵族气泡
func (hb *HornBubble) UserVipNotifyBubble(nobleType, level int) *NobleBubble {
	// 体验贵族普通贵族气泡 ID 一致
	if nobleType == vip.TypeLiveTrialNoble {
		nobleType = vip.TypeLiveNoble
	}
	for _, bubble := range hb.NobleBubbles {
		if bubble.Type == nobleType && bubble.Level == level {
			return &bubble
		}
	}
	return nil
}

// BlackCardNotifyBubble 获取喇叭黑卡气泡
func (hb *HornBubble) BlackCardNotifyBubble(level int) *BlackCardBubble {
	for _, bubble := range hb.BlackCardBubbles {
		if bubble.Level == level {
			return &bubble
		}
	}
	return nil
}

// IsActive 是否生效
func (hb *HornBubble) IsActive(when time.Time) bool {
	return hb != nil && hb.StartTime <= when.Unix() && (hb.EndTime == 0 || hb.EndTime > when.Unix())
}
