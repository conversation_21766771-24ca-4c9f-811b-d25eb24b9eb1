package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindPia(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyPia)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	pia, err := FindPia()
	require.NoError(err)
	assert.Equal("pia 戏", pia.Name)
	assert.Equal(KeyPia, pia.Key)
}

func TestPia_IsShow(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(2, 0)
	})
	defer goutil.SetTimeNow(nil)

	pia := Pia{
		Name:     "pia 戏",
		ShowTime: 1,
	}
	assert.True(pia.IsShow())

	pia = Pia{
		Name:     "pia 戏",
		ShowTime: 3,
	}
	assert.False(pia.IsShow())

	pia = Pia{
		ShowTime: 1,
	}
	assert.False(pia.IsShow())
}
