package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestGashaponTags(t *testing.T) {
	srTag := []string{"_id", "key", "name", "price", "image", "interaction_label_icon",
		"icon", "icon_active", "label_icon", "status", "open_url", "intro", "intro_icon", "intro_open_url",
		"position", "max_energy", "rule", "reward_live_icons", "reward_bubble_id", "reward_notify_message", "web_old_version", "enable_new_msg_broadcast"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Gashapon{}, srTag...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Gashapon{}, srTag[1:]...)
}

func TestDefaultGashapon(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(Gashapon{
		Key:  KeyGashapon,
		Icon: "",
	}, DefaultGashapon())
}

func TestFindGashapon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gashapon, err := FindGashapon()
	require.NoError(err)
	assert.Equal(Gashapon{
		Key:  KeyGashapon,
		Icon: "",
	}, gashapon)
}
