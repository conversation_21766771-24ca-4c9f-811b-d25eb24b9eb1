package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestSoundRecommendTags(t *testing.T) {
	srBsonTag := []string{"_id", "key", "live"}
	srlBsonTag := []string{"drama_allow_list", "blocklist_live_catalog",
		"blocklist_room", "blocklist_sound", "blocklist_sound_catalog", "score_percent",
		"message_pool", "off_duration"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(SoundRecommend{}, srBsonTag...)
	kc.Check(SoundRecommendLive{}, srlBsonTag...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(SoundRecommend{}, srBsonTag[1:]...)
	kc.Check(SoundRecommendLive{}, srlBsonTag...)
}

func TestFindSoundRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeySoundRecommend)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	sr := DefaultSoundRecommend()
	_, err := col.UpdateOne(ctx, bson.M{"key": sr.Key},
		bson.M{"$set": sr}, options.Update().SetUpsert(true))
	require.NoError(err)

	// 从数据库读取了被修改的数据
	sr0 := DefaultSoundRecommend()
	sr1, err := FindSoundRecommend()
	require.NoError(err)
	assert.NotEqual(sr0, sr1)
	assert.Equal(sr.Live.ScorePercent, sr1.Live.ScorePercent)

	// 从缓存读取到了空数据，返回了默认配置
	require.NoError(service.LRURedis.Set(cacheKey, `{}`, time.Second).Err())
	sr2, err := FindSoundRecommend()
	require.NoError(err)
	assert.Equal(sr0, sr2)
}

func TestSoundRecommend_RandomLiveMessage(t *testing.T) {
	assert := assert.New(t)

	sr := DefaultSoundRecommend()

	assert.NotPanics(func() {
		length := 11
		s := make(map[string]struct{}, length)
		for i := 0; i < length*10; i++ {
			msg := sr.RandomLiveMessage()
			s[msg] = struct{}{}
		}
		_, ok := s["今日份声音恋人"]
		assert.True(ok)
	})

	sr.Live.MessagePool = []string{}
	assert.Equal("", sr.RandomLiveMessage())
}

func TestDeleteSoundMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeySoundRecommend)
	require.NoError(service.LRURedis.Set(cacheKey, "TestDeleteSoundMessage", time.Minute).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	messagesToAppend := []string{"c1", "c2", "c3"}
	update := bson.M{"$push": bson.M{"live.message_pool": bson.M{"$each": messagesToAppend}}}
	_, err := Collection().UpdateOne(ctx, bson.M{"key": KeySoundRecommend}, update)
	require.NoError(err)

	messages := []string{"c1", "c2", "c4"}
	require.NoError(DeleteSoundMessage(messages))
	var r SoundRecommend
	require.NoError(Collection().FindOne(ctx, bson.M{"key": KeySoundRecommend}).Decode(&r))
	assert.NotContains(r.Live.MessagePool, "c1")
	assert.NotContains(r.Live.MessagePool, "c2")
	assert.Contains(r.Live.MessagePool, "c3")
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.Zero(exists)
}

func TestAddSoundMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeySoundRecommend)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	messages := []string{"e1", "e2", "e3"}
	update := bson.M{"$pull": bson.M{"live.message_pool": bson.M{"$in": messages}}}
	_, err := Collection().UpdateOne(ctx, bson.M{"key": KeySoundRecommend}, update)
	require.NoError(err)

	require.NoError(AddSoundMessage(messages))
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.Zero(exists)
	var r SoundRecommend
	require.NoError(Collection().FindOne(ctx, bson.M{"key": KeySoundRecommend}).Decode(&r))
	assert.Contains(r.Live.MessagePool, messages[0])
	assert.Contains(r.Live.MessagePool, messages[1])
	assert.Contains(r.Live.MessagePool, messages[2])

	require.NoError(AddSoundMessage([]string{"e3", "e4"}))
	require.NoError(Collection().FindOne(ctx, bson.M{"key": KeySoundRecommend}).Decode(&r))
	require.NoError(err)
	assert.Contains(r.Live.MessagePool, "e3")
	assert.Contains(r.Live.MessagePool, "e4")
}

func TestClearKeySoundRecommendCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeySoundRecommend)
	_, err := service.LRURedis.Del(cacheKey).Result()
	require.NoError(err)
	clearKeySoundRecommendCache()

	service.Redis.Set(cacheKey, "TestClearKeySoundRecommendCache", time.Minute)
	clearKeySoundRecommendCache()
	exists, err := service.LRURedis.Exists(cacheKey).Result()
	require.NoError(err)
	assert.Zero(exists)
}

func TestSoundRecommendLive_FindDramaAllowList(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	s := SoundRecommendLive{
		DramaAllowList: []DramaAllowList{
			{
				DramaID:       1,
				RoomAllowList: []int64{1, 2, 3},
				StartTime:     now.Add(-time.Hour).Unix(),
				EndTime:       now.Add(time.Hour).Unix(),
			},
		},
	}
	// 测试 DramaID 为 0
	assert.Nil(s.FindDramaAllowList(0))
	// 测试 DramaID 没有推荐池
	assert.Nil(s.FindDramaAllowList(2))
	// 测试有有效推荐池
	assert.Equal(3, len(s.FindDramaAllowList(1)))
	// 测试没有有效推荐池
	s.DramaAllowList[0].StartTime = now.Add(time.Hour).Unix()
	assert.Nil(s.FindDramaAllowList(1))
	// 测试推荐池已结束
	s.DramaAllowList[0].StartTime = now.Add(-time.Hour).Unix()
	s.DramaAllowList[0].EndTime = now.Add(-time.Hour).Unix()
	assert.Nil(s.FindDramaAllowList(1))
	// 测试推荐池结束时间为 0 (永久生效)
	s.DramaAllowList[0].EndTime = 0
	assert.Equal(3, len(s.FindDramaAllowList(1)))
}
