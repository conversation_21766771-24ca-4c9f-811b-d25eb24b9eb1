package params

import (
	"slices"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service/storage"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 宝盒显示位置 bitmask
const (
	StatusLuckyBoxShowInGifts       = iota + 1 // 显示在礼物栏
	StatusLuckyBoxShowInInteractive            // 显示在玩法栏
)

// LuckyBox 礼物宝盒相关配置
type LuckyBox struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	OpenTime             int64          `bson:"open_time,omitempty" json:"open_time,omitempty"`                           // 开启时间，0 为不限制（秒级时间戳）
	Status               goutil.BitMask `bson:"status,omitempty" json:"status,omitempty"`                                 // 礼物宝盒状态
	Name                 string         `bson:"name" json:"name"`                                                         // 礼物宝盒名称
	Price                int64          `bson:"price" json:"price"`                                                       // 礼物价格
	PriceType            int            `bson:"price_type" json:"price_type"`                                             // 价格类型，0: 固定单价 1: 最低价格
	Icon                 string         `bson:"icon" json:"icon"`                                                         // 礼物宝盒图
	Image                string         `bson:"image" json:"image"`                                                       // APP 直播间左上角玩法栏宝盒小图标
	InteractionLabelIcon string         `bson:"interaction_label_icon,omitempty" json:"interaction_label_icon,omitempty"` // 玩法栏角标图
	IconActive           string         `bson:"icon_active" json:"icon_active"`                                           // 礼物宝盒动态图
	LabelIcon            string         `bson:"label_icon" json:"label_icon"`                                             // 礼物宝盒角标图
	Intro                string         `bson:"intro" json:"intro"`                                                       // 礼物宝盒简介
	IntroIcon            string         `bson:"intro_icon" json:"intro_icon"`                                             // 礼物宝盒简介图标
	IntroOpenURL         string         `bson:"intro_open_url" json:"intro_open_url"`                                     // 礼物宝盒跳转链接
	Position             int            `bson:"position" json:"position"`                                                 // 礼物宝盒显示位置
	AllowRoomIDs         []int64        `bson:"allow_room_ids,omitempty" json:"allow_room_ids,omitempty"`                 // 不受开始时间限制的房间白名单

	IconURL                 string `bson:"-" json:"-"`
	ImageURL                string `bson:"-" json:"-"`
	InteractionLabelIconURL string `bson:"-" json:"-"`
	IconActiveURL           string `bson:"-" json:"-"`
	LabelIconURL            string `bson:"-" json:"-"`
	IntroIconURL            string `bson:"-" json:"-"`
}

// IsOpen 是否开启
func (lb *LuckyBox) IsOpen() bool {
	now := goutil.TimeNow().Unix()
	return lb.OpenTime <= now
}

func (lb *LuckyBox) afterLoad() {
	lb.IconURL = storage.ParseSchemeURL(lb.Icon)
	lb.ImageURL = storage.ParseSchemeURL(lb.Image)
	lb.InteractionLabelIconURL = storage.ParseSchemeURL(lb.InteractionLabelIcon)
	lb.IconActiveURL = storage.ParseSchemeURL(lb.IconActive)
	lb.LabelIconURL = storage.ParseSchemeURL(lb.LabelIcon)
	lb.IntroIconURL = storage.ParseSchemeURL(lb.IntroIcon)
}

// DefaultLuckyBox 礼物宝盒默认配置
func DefaultLuckyBox() LuckyBox {
	return LuckyBox{
		Key: KeyLuckyBox,
	}
}

// FindLuckyBox 查询礼物宝盒配置
func FindLuckyBox() (*LuckyBox, error) {
	box := DefaultLuckyBox()
	err := findParams(box.Key, &box)
	if err != nil {
		return &box, err
	}
	box.afterLoad()
	return &box, err
}

// IsAllowRoom 是否为开放宝盒的测试房间
func (lb *LuckyBox) IsAllowRoom(roomID int64) bool {
	return slices.Contains(lb.AllowRoomIDs, roomID)
}

// IsShowInGifts 是否显示在礼物栏
func (lb *LuckyBox) IsShowInGifts() bool {
	return lb.Status.IsSet(StatusLuckyBoxShowInGifts)
}

// IsShowInInteractive 是否显示在玩法栏
func (lb *LuckyBox) IsShowInInteractive() bool {
	return lb.Status.IsSet(StatusLuckyBoxShowInInteractive)
}
