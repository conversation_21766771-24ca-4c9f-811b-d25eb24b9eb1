package params

import (
	"slices"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// PK - PK 相关配置
type PK struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	NoLimitPKRoomIDs []int64 `bson:"no_limit_room_ids" json:"no_limit_room_ids"` // 热门时段不受次数限制的房间号
}

// DefaultPK - 默认的 PK 配置
func DefaultPK() PK {
	return PK{
		Key:              KeyPK,
		NoLimitPKRoomIDs: []int64{},
	}
}

// FindPK - 查找 PK 配置
func FindPK() (PK, error) {
	pk := DefaultPK()
	err := findParams(KeyPK, &pk)
	return pk, err
}

// IsNoLimitPKRoomID - 判断 roomID 在不受限制的白名单中
func (pk *PK) IsNoLimitPKRoomID(roomID int64) bool {
	return slices.Contains(pk.NoLimitPKRoomIDs, roomID)
}
