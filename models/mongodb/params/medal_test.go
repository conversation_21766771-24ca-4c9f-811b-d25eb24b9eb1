package params

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMedalTags(t *testing.T) {
	tags := []string{"_id", "key", "first_discount"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Medal{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Medal{}, tags[1:]...)
}

func TestFindMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyMedal)
	require.NoError(service.LRURedis.Del(cacheKey).Err())

	medal, err := FindMedal()
	require.NoError(err)
	assert.Equal(KeyMedal, medal.Key)
}

func TestMedal_IsFirstMedalDiscountEnable(t *testing.T) {
	assert := assert.New(t)

	timeNow := goutil.TimeNow().Unix()
	medal := Medal{Key: KeyMedal}
	assert.False(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{}))
	medal.FirstDiscount.StartTime = timeNow - 10
	assert.True(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{}))
	medal.FirstDiscount.StartTime = timeNow + 10
	assert.False(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{}))

	medal = Medal{
		Key: KeyMedal,
		FirstDiscount: MedalFirstDiscount{
			StartTime: timeNow,
		},
	}
	assert.True(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{FromApp: false}))
	assert.False(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{FromApp: true, OS: goutil.Android, AppVersion: "6.3.1"}))
	assert.False(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{FromApp: true, OS: goutil.IOS, AppVersion: "6.3.1"}))
	assert.True(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{FromApp: true, OS: goutil.Android, AppVersion: "6.3.2"}))
	assert.True(medal.IsFirstMedalDiscountEnable(&goutil.Equipment{FromApp: true, OS: goutil.IOS, AppVersion: "6.3.2"}))
}
