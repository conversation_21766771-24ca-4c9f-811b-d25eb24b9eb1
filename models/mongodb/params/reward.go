package params

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Reward 奖励相关配置
type Reward struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Key string             `bson:"key" json:"key"`

	LiveNewUserRewardID int64 `bson:"live_new_user_reward_id" json:"live_new_user_reward_id"` // 直播新用户奖励 ID
}

// FindReward 查询奖励相关配置
func FindReward() (Reward, error) {
	r := Reward{}
	err := findParams(KeyReward, &r)
	return r, err
}
