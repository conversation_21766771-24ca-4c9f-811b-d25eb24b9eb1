package params

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMultiComboTags(t *testing.T) {
	tags := []string{"_id", "key", "show_shortcuts_start_time", "show_shortcuts_end_time", "disable_send_room_ids"}

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(MultiCombo{}, tags...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MultiCombo{}, tags[1:]...)
}

func TestFindMultiCombo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cacheKey := keys.KeyParams1.Format(KeyMultiCombo)
	require.NoError(service.LRURedis.Del(cacheKey).Err())
	params, err := FindMultiCombo()
	require.NoError(err)
	assert.Equal(KeyMultiCombo, params.Key)

	params.ShowShortcutsStartTime = 1
	params.ShowShortcutsEndTime = 2
	params.DisableSendRoomIDs = []int64{1, 2}
	err = service.LRURedis.Set(cacheKey, tutil.SprintJSON(params), 5*time.Second).Err()
	require.NoError(err)
	params, err = FindMultiCombo()
	require.NoError(err)
	assert.EqualValues(1, params.ShowShortcutsStartTime)
	assert.EqualValues(2, params.ShowShortcutsEndTime)
	assert.Equal([]int64{1, 2}, params.DisableSendRoomIDs)
}

func TestMultiCombo_EnableShowShortcuts(t *testing.T) {
	assert := assert.New(t)

	params := MultiCombo{
		ShowShortcutsStartTime: 1,
		ShowShortcutsEndTime:   2,
	}
	assert.False(params.EnableShowShortcuts())

	now := goutil.TimeNow()
	params.ShowShortcutsStartTime = now.Add(-5 * time.Second).Unix()
	params.ShowShortcutsEndTime = now.Add(5 * time.Second).Unix()
	assert.True(params.EnableShowShortcuts())
}

func TestMultiCombo_EnableSend(t *testing.T) {
	assert := assert.New(t)

	params := MultiCombo{
		DisableSendRoomIDs: []int64{1, 2},
	}
	assert.False(params.EnableSend(1))
	assert.False(params.EnableSend(2))
	assert.True(params.EnableSend(3))
}
