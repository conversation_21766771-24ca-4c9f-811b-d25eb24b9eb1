package luckyuser

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LuckyUser lucky user
type LuckyUser struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	CreateTime   int64              `bson:"create_time" json:"-"`
	ModifiedTime int64              `bson:"modified_time" json:"-"`

	UserID    int64 `bson:"user_id" json:"user_id"`
	StartTime int64 `bson:"start_time" json:"start_time"`
	EndTime   int64 `bson:"end_time" json:"end_time"`
	Score     int64 `bson:"score" json:"score"`
}

// Collection mongo collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("lucky_users")
}

// LuckyEditor lucky editor
type LuckyEditor struct {
	OldUserID     int64
	NewUserID     int64
	LuckyScore    int64
	NewLuckyScore int64
	Now           time.Time
}

// NewLuckyEditor new lucky editor
func NewLuckyEditor(now time.Time) *LuckyEditor {
	return &LuckyEditor{Now: now}
}

// SetOldUser set old lucky user
func (lucky *LuckyEditor) SetOldUser(oldUserID, luckyScore int64) {
	lucky.OldUserID = oldUserID
	lucky.LuckyScore = luckyScore
}

// SetNewUser set new lucky user
func (lucky *LuckyEditor) SetNewUser(userID, newLuckyScore int64) {
	lucky.NewUserID = userID
	lucky.NewLuckyScore = newLuckyScore
}

// Update add new lucky user and update old lucky user's end time
func (lucky *LuckyEditor) Update() error {
	if lucky.OldUserID == 0 && lucky.NewUserID == 0 {
		panic("old user 和 new user 不能都为 0")
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	models := make([]mongo.WriteModel, 0, 2)
	if lucky.OldUserID != 0 {
		set := bson.M{
			"modified_time": lucky.Now.Unix(),
		}
		// 有新福星产生，更新 end_time
		if lucky.NewUserID != 0 {
			set["end_time"] = lucky.Now.Unix()
		}

		update := bson.M{
			"$set": set,
			"$inc": bson.M{"score": lucky.LuckyScore},
		}

		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{
			"user_id":  lucky.OldUserID,
			"end_time": 0,
		}).SetUpdate(update))
	}

	// 有新福星产生插入新福星数据
	if lucky.NewUserID != 0 {
		models = append(models, mongo.NewInsertOneModel().SetDocument(LuckyUser{
			UserID:       lucky.NewUserID,
			StartTime:    lucky.Now.Unix(),
			Score:        lucky.NewLuckyScore,
			CreateTime:   lucky.Now.Unix(),
			ModifiedTime: lucky.Now.Unix(),
		}))
	}

	_, err := Collection().BulkWrite(ctx, models, options.BulkWrite().SetOrdered(true))
	if err != nil {
		return err
	}

	return nil
}

// FindLuckyUser find lucky user
func FindLuckyUser(filter bson.M) (*LuckyUser, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var lucky LuckyUser
	err := Collection().FindOne(ctx, filter).Decode(&lucky)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &lucky, nil
}

// FindUserLuckyHistoryCount 查询福星次数
func FindUserLuckyHistoryCount(userID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	count, err := Collection().CountDocuments(ctx, bson.M{"user_id": userID})
	if err != nil {
		return 0, err
	}

	return count, nil
}

// FindUserLuckyHistory 查询福星记录
func FindUserLuckyHistory(userID int64, pa goutil.Pagination) ([]LuckyUser, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx,
		bson.M{
			"user_id": userID,
		},
		pa.SetFindOptions(options.Find().SetSort(bson.M{"start_time": -1})),
	)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	var history []LuckyUser
	err = cur.All(ctx, &history)
	if err != nil {
		return nil, err
	}

	return history, nil
}
