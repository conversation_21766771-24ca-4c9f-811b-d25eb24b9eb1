package luckyuser

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestLuckyEditor(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	now := goutil.TimeNow()
	editor := NewLuckyEditor(now)
	editor.SetNewUser(1, 100)
	err = editor.Update()
	require.NoError(err)

	oldLucky, err := FindLuckyUser(bson.M{})
	require.NoError(err)
	assert.EqualValues(1, oldLucky.UserID)
	assert.EqualValues(now.Unix(), oldLucky.StartTime)
	assert.EqualValues(0, oldLucky.EndTime)
	assert.EqualValues(100, oldLucky.Score)

	editor = NewLuckyEditor(now.Add(time.Second))
	editor.SetOldUser(1, 100)
	editor.SetNewUser(2, 100)
	err = editor.Update()
	require.NoError(err)

	res, err := Collection().Find(ctx, bson.M{})
	require.NoError(err)

	var list []LuckyUser
	require.NoError(res.All(ctx, &list))
	require.Len(list, 2)

	oldLucky.EndTime = now.Add(time.Second).Unix()
	assert.EqualValues(oldLucky.UserID, list[0].UserID)
	assert.EqualValues(oldLucky.StartTime, list[0].StartTime)
	assert.EqualValues(now.Add(time.Second).Unix(), list[0].EndTime)
	assert.EqualValues(200, list[0].Score)

	assert.EqualValues(2, list[1].UserID)
	assert.EqualValues(now.Add(time.Second).Unix(), list[1].StartTime)
	assert.EqualValues(0, list[1].EndTime)
	assert.EqualValues(100, list[1].Score)
}

func TestFindUserLuckyHistoryCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// TODO: 后续只删除指定用户
	_, err := Collection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	now := goutil.TimeNow()
	editor := NewLuckyEditor(now)
	editor.SetNewUser(1, 100)
	require.NoError(editor.Update())

	editor = NewLuckyEditor(now.Add(time.Second))
	editor.SetOldUser(1, 10)
	editor.SetNewUser(2, 100)
	require.NoError(editor.Update())

	count, err := FindUserLuckyHistoryCount(1)
	require.NoError(err)
	assert.EqualValues(1, count)
}

func TestFindUserLuckyHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	now := goutil.TimeNow()
	editor := NewLuckyEditor(now)
	editor.SetNewUser(1, 100)
	require.NoError(editor.Update())

	editor = NewLuckyEditor(now.Add(time.Second))
	editor.SetOldUser(1, 10)
	editor.SetNewUser(1, 100)
	require.NoError(editor.Update())

	list, err := FindUserLuckyHistory(1, goutil.MakePagination(20, 1, 20))
	require.NoError(err)
	assert.Len(list, 2)
}
