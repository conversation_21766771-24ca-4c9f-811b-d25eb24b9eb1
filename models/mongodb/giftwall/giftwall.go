package giftwall

import (
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Gift 礼物墙礼物关系表
type Gift struct {
	OID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	CreateTime   int64              `bson:"create_time" json:"-"`
	ModifiedTime int64              `bson:"modified_time" json:"-"`

	ShowGiftID       int64   `bson:"show_gift_id" json:"-"`       // 上墙礼物 ID
	TargetGiftID     int64   `bson:"target_gift_id" json:"-"`     // 定位礼物 ID
	EffectiveGiftIDs []int64 `bson:"effective_gift_ids" json:"-"` // 有效礼物 ID
}

// GiftCollection 返回 Gift 的 Collection
func GiftCollection() *mongo.Collection {
	return service.MongoDB.Collection("gift_wall_gifts")
}

// ListGifts 查询全部数据
func ListGifts(p, pageSize int64) ([]*Gift, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	count, err := GiftCollection().CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []*Gift{}, pa, nil
	}
	cur, err := GiftCollection().Find(ctx, bson.M{},
		pa.SetFindOptions(nil).SetSort(bson.M{"show_gift_id": 1})) // 按照上墙礼物 ID 从小到大排列
	if err != nil {
		return nil, pa, err
	}
	gifts := make([]*Gift, 0, pageSize)
	err = cur.All(ctx, &gifts)
	if err != nil {
		return nil, pa, err
	}
	return gifts, pa, nil
}

// AddGiftWallGift 新增上墙礼物
func AddGiftWallGift(showGiftID, targetGiftID int64, effectiveGiftIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	_, err := GiftCollection().InsertOne(ctx, &Gift{
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
		ShowGiftID:       showGiftID,
		TargetGiftID:     targetGiftID,
		EffectiveGiftIDs: effectiveGiftIDs,
	})
	if err != nil {
		return err
	}
	return nil
}

// FindOneByShowGiftID 根据上墙礼物 ID 查询礼物墙关联关系
func FindOneByShowGiftID(showGiftID int64) (*Gift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var g *Gift
	err := GiftCollection().FindOne(ctx, bson.M{
		"show_gift_id": showGiftID,
	}).Decode(&g)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return g, nil
}

// DelGiftByShowGiftID 删除上墙礼物 ID 下的礼物墙记录
func DelGiftByShowGiftID(showGiftID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := GiftCollection().DeleteOne(ctx, bson.M{
		"show_gift_id": showGiftID,
	})
	if err != nil {
		return err
	}
	return nil
}

// UpdateOneByShowGiftID 根据上墙礼物 ID 更新定位礼物 ID 和有效礼物 ID
func UpdateOneByShowGiftID(showGiftID, targetGiftID int64, effectiveGiftIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := GiftCollection().UpdateOne(ctx,
		bson.M{"show_gift_id": showGiftID},
		bson.M{"$set": bson.M{
			"modified_time":      goutil.TimeNow().Unix(),
			"target_gift_id":     targetGiftID,
			"effective_gift_ids": effectiveGiftIDs,
		}},
	)
	if err != nil {
		return err
	}
	return nil
}

// ExistEffectiveGiftIDs 校验有效礼物是否被其他上墙礼物绑定
func ExistEffectiveGiftIDs(effectiveGiftIDs []int64, opt ...*Options) (bool, []int64, error) {
	filter := bson.M{"effective_gift_ids": bson.M{"$elemMatch": bson.M{"$in": effectiveGiftIDs}}}
	if option := getOptions(opt); !option.IgnoreOID.IsZero() {
		filter["_id"] = bson.M{"$ne": option.IgnoreOID}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := GiftCollection().Find(ctx, filter)
	if err != nil {
		return false, nil, err
	}
	var gs []*Gift
	err = cur.All(ctx, &gs)
	if err != nil {
		return false, nil, err
	}
	ids := make([]int64, 0, len(gs))
	for _, g := range gs {
		ids = append(ids, g.ShowGiftID)
	}
	return len(ids) != 0, ids, nil
}

// FindGiftsByShowGiftIDs 根据上墙礼物查找礼物墙礼物关系
func FindGiftsByShowGiftIDs(showGiftIDs []int64) ([]*Gift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	showGiftIDs = util.Uniq(showGiftIDs)
	cur, err := GiftCollection().Find(ctx, bson.M{"show_gift_id": bson.M{"$in": showGiftIDs}})
	if err != nil {
		return nil, err
	}
	var gifts []*Gift
	err = cur.All(ctx, &gifts)
	if err != nil {
		return nil, err
	}
	return gifts, nil
}

// ListItem 礼物墙列表
type ListItem struct {
	ShowGiftID           string `json:"show_gift_id"`
	ShowGiftName         string `json:"show_gift_name"`
	ShowGiftIconURL      string `json:"show_gift_icon_url"`
	TargetGiftID         string `json:"target_gift_id"`
	TargetGiftName       string `json:"target_gift_name"`
	TargetGiftIconURL    string `json:"target_gift_icon_url"`
	EffectiveGiftID      string `json:"effective_gift_id"`
	EffectiveGiftName    string `json:"effective_gift_name"`
	EffectiveGiftIconURL string `json:"effective_gift_icon_url"`
}

// NewListItem new gifiwall list item
func NewListItem(giftwall *Gift, giftMap map[int64]*gift.Gift) *ListItem {
	item := new(ListItem)
	if show, ok := giftMap[giftwall.ShowGiftID]; ok {
		item.ShowGiftID = strconv.FormatInt(show.GiftID, 10)
		item.ShowGiftName = show.Name
		item.ShowGiftIconURL = show.Icon
	}
	if target, ok := giftMap[giftwall.TargetGiftID]; ok {
		item.TargetGiftID = strconv.FormatInt(target.GiftID, 10)
		item.TargetGiftName = target.Name
		item.TargetGiftIconURL = target.Icon
	}
	var iconURL string
	ids := make([]string, 0, len(giftwall.EffectiveGiftIDs))
	names := make([]string, 0, len(giftwall.EffectiveGiftIDs))
	for i, id := range giftwall.EffectiveGiftIDs {
		if effective, ok := giftMap[id]; ok {
			// NOTICE: 只取 effective_gift 下第一个礼物的图标地址
			if i == 0 {
				iconURL = effective.Icon
			}
			ids = append(ids, strconv.FormatInt(effective.GiftID, 10))
			names = append(names, effective.Name)
		}
	}
	if len(ids) != 0 {
		item.EffectiveGiftID = strings.Join(ids, ",")
		item.EffectiveGiftName = strings.Join(names, ",")
		item.EffectiveGiftIconURL = iconURL
	}
	return item
}
