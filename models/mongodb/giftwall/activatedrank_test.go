package giftwall

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRankTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(ActivatedRank{}, "_id", "create_time", "modified_time",
		"user_id", "room_id", "_period_id", "show_gift_id", "activated_num")
}

func TestAddUserRankAndFindUserRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pOID := primitive.NewObjectID()
	defer func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := RankCollection().DeleteMany(ctx, bson.M{"_period_id": pOID})
		assert.NoError(err)
	}()
	require.NoError(AddUserRank(pOID, 1, 1, 1, 1))
	rank, err := FindUserRank(pOID, 1, 1, 1)
	require.NoError(err)
	require.NotNil(rank)

	require.NoError(AddUserRank(pOID, 1, 1, 1, 1))
	rank2, err := FindUserRank(pOID, 1, 1, 1)
	require.NoError(err)
	require.NotNil(rank2)
	assert.Greater(rank2.ActivatedNum, rank.ActivatedNum)
}

func TestFindTop5(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pOID := primitive.NewObjectID()
	defer func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := RankCollection().DeleteMany(ctx, bson.M{"_period_id": pOID})
		assert.NoError(err)
	}()
	require.NoError(AddUserRank(pOID, 1, 1, 1, 1))

	res, err := FindTop5(pOID, 1, 1)
	require.NoError(err)
	assert.NotEmpty(res)

	res, err = FindTop5(pOID, -9999, 1)
	require.NoError(err)
	assert.Empty(res)
}

func createTestGiftWallRankData(periodOID primitive.ObjectID, roomID int64) (func(int64), error) {
	ranks := []interface{}{
		ActivatedRank{
			ModifiedTime: goutil.TimeNow().Unix(),
			PeriodOID:    periodOID,
			RoomID:       roomID,
			UserID:       1,
			ShowGiftID:   1,
			ActivatedNum: 1,
		},
		ActivatedRank{
			ModifiedTime: goutil.TimeNow().Unix() - 1,
			PeriodOID:    periodOID,
			RoomID:       roomID,
			UserID:       2,
			ShowGiftID:   1,
			ActivatedNum: 1,
		},
		ActivatedRank{
			ModifiedTime: goutil.TimeNow().Unix(),
			PeriodOID:    periodOID,
			RoomID:       roomID,
			UserID:       3,
			ShowGiftID:   2,
			ActivatedNum: 3,
		},
		ActivatedRank{
			ModifiedTime: goutil.TimeNow().Unix(),
			PeriodOID:    periodOID,
			RoomID:       roomID,
			UserID:       1,
			ShowGiftID:   2,
			ActivatedNum: 1,
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := RankCollection().InsertMany(ctx, ranks)
	if err != nil {
		return nil, err
	}
	return cleanupTestGiftWallRankData, nil
}

func cleanupTestGiftWallRankData(roomID int64) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := RankCollection().DeleteMany(ctx, bson.M{"room_id": roomID})
	if err != nil {
		logger.Error(err)
	}
}

func TestFindUserGiftsRankTop1(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		now    = goutil.TimeNow()
		pOID   = primitive.NewObjectIDFromTimestamp(now)
		roomID = now.Unix()
	)
	resMap, err := FindUserGiftsRankTop1(pOID, roomID, []int64{1, 2})
	require.NoError(err)
	assert.Zero(len(resMap))

	cleanup, err := createTestGiftWallRankData(pOID, roomID)
	require.NoError(err)
	defer cleanup(roomID)
	resMap, err = FindUserGiftsRankTop1(pOID, roomID, []int64{1, 2})
	require.NoError(err)
	assert.EqualValues(2, resMap[1].UserID)
	assert.EqualValues(3, resMap[2].UserID)
}
