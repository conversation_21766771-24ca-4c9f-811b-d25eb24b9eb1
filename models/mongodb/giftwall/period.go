package giftwall

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// PeriodDuration 周期持续时间
	PeriodDuration = 14 * 24 * time.Hour

	// PeriodMaxSpanNum 最大创建的未来周期数量
	PeriodMaxSpanNum = 5
	// PeriodMaxSpanDuration 最多创建未来 5 个周期（跨度最大为 10 周）
	PeriodMaxSpanDuration = PeriodMaxSpanNum * PeriodDuration
)

const (
	// RewardTypeCustomGift 直播间专属礼物奖励
	RewardTypeCustomGift = 1
	// RewardTypeAppearance 直播间外观奖励
	RewardTypeAppearance = 2
)

// RewardInfo 礼物墙奖励
type RewardInfo struct {
	Threshold int64 `bson:"threshold" json:"threshold"`   // 点亮目标
	Type      int   `bson:"type" json:"type"`             // 点亮目标奖励类型
	ElementID int64 `bson:"element_id" json:"element_id"` // 点亮目标满足奖励 ID（直播间专属礼物 ID、外观 ID）
}

// Period 礼物墙周期
type Period struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	StartTime   int64         `bson:"start_time"`
	EndTime     int64         `bson:"end_time"`
	ShowGiftIDs []int64       `bson:"show_gift_ids"` // 上墙礼物 ID
	Rewards     []*RewardInfo `bson:"rewards"`
}

// PeriodCollection 返回 Period 的 Collection
func PeriodCollection() *mongo.Collection {
	return service.MongoDB.Collection("gift_wall_periods")
}

// TotalNum 需要点亮的总数量
func (p *Period) TotalNum() int64 {
	return int64(len(p.ShowGiftIDs))
}

// FindByPeriodOID 查找礼物墙周期信息
func FindByPeriodOID(oid primitive.ObjectID) (*Period, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var p *Period
	err := PeriodCollection().FindOne(ctx, bson.M{"_id": oid}).Decode(&p)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return p, nil
}

// findCurrentPeriod 根据时间获取对应周期信息
func findCurrentPeriod(when time.Time) (*Period, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"start_time": bson.M{"$lte": when.Unix()},
		"end_time":   bson.M{"$gt": when.Unix()},
	}
	var period Period
	err := PeriodCollection().FindOne(ctx, filter).Decode(&period)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &period, nil
}

func currentPeriodKey(when time.Time) string {
	return keys.KeyCurrentGiftWallPeriod1.Format(when.Format(util.TimeFormatYMDWithNoSpace))
}

// clearCurrentPeriodCache 清理当前周期缓存
// TODO: 缓存使用 redis 替换
func clearCurrentPeriodCache(when time.Time) {
	cacheKey := currentPeriodKey(when)
	service.Cache5Min.Delete(cacheKey)
}

// CurrentPeriodInfo current period info
func CurrentPeriodInfo(when time.Time) (*Period, error) {
	cacheKey := currentPeriodKey(when)
	p, ok := service.Cache5Min.Get(cacheKey)
	if ok {
		if p == nil {
			return nil, nil
		}
		res := *p.(*Period) // 复制数据
		return &res, nil
	}
	res, err := findCurrentPeriod(when)
	if err != nil {
		return nil, err
	}
	if res == nil {
		service.Cache5Min.Set(cacheKey, nil, 0)
		return nil, nil
	}
	data := *res // 复制数据
	service.Cache5Min.Set(cacheKey, &data, 0)
	return res, nil
}

// FindPeriods 周期开始和结束时间查询周期
func FindPeriods(startTime, endTime time.Time) ([]*Period, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := PeriodCollection().Find(ctx, bson.M{
		"start_time": bson.M{"$lt": endTime.Unix()},
		"end_time":   bson.M{"$gt": startTime.Unix()},
	})
	if err != nil {
		return nil, err
	}
	var periods []*Period
	err = cur.All(ctx, &periods)
	if err != nil {
		return nil, err
	}
	return periods, nil
}

// CreatePeriod create period
func CreatePeriod(startTime, endTime time.Time, showGiftIDs []int64, rewards []*RewardInfo) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().InsertOne(ctx, &Period{
		CreateTime:   goutil.TimeNow().Unix(),
		ModifiedTime: goutil.TimeNow().Unix(),
		StartTime:    startTime.Unix(),
		EndTime:      endTime.Unix(),
		ShowGiftIDs:  showGiftIDs,
		Rewards:      rewards,
	})
	if err != nil {
		return err
	}
	return nil
}

// DelPeriod 删除未开始的周期
func DelPeriod(oid primitive.ObjectID) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	nowUnix := goutil.TimeNow().Unix()
	res, err := PeriodCollection().DeleteOne(ctx, bson.M{
		"_id":        oid,
		"start_time": bson.M{"$gt": nowUnix},
	})
	if err != nil {
		return false, err
	}
	return res.DeletedCount > 0, nil
}

// FindUnfinishedPeriodByShowGiftID 查询绑定上墙礼物 ID 未结束的周期
func FindUnfinishedPeriodByShowGiftID(showGiftID int64) (*Period, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var p *Period
	err := PeriodCollection().FindOne(ctx,
		bson.M{
			"show_gift_ids": bson.M{"$in": bson.A{showGiftID}},
			"end_time":      bson.M{"$gt": goutil.TimeNow().Unix()},
		},
	).Decode(&p)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return p, nil
}

// UpdatePeriod 更新礼物墙周期
func UpdatePeriod(oid primitive.ObjectID, giftIDs []int64, rewards []*RewardInfo) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()

	updateFields := bson.M{
		"modified_time": now.Unix(),
	}

	if len(giftIDs) > 0 {
		updateFields["show_gift_ids"] = giftIDs
	}

	if len(rewards) > 0 {
		updateFields["rewards"] = rewards
	}

	_, err := PeriodCollection().UpdateOne(ctx,
		bson.M{"_id": oid},
		bson.M{"$set": updateFields},
	)
	if err != nil {
		return err
	}
	// TODO: 如果更新的是当前周期，需要删除当前周期缓存
	return nil
}
