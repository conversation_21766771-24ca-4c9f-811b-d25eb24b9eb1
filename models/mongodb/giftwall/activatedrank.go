package giftwall

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActivatedRank 用户礼物墙对应礼物点亮榜单
type ActivatedRank struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	UserID       int64              `bson:"user_id"`
	RoomID       int64              `bson:"room_id"`
	PeriodOID    primitive.ObjectID `bson:"_period_id"`
	ShowGiftID   int64              `bson:"show_gift_id"`  // 上墙礼物 ID
	ActivatedNum int64              `bson:"activated_num"` // 对应上墙礼物的激活数量
}

// RankCollection 返回 ActivatedInfo 的 Collection
func RankCollection() *mongo.Collection {
	return service.MongoDB.Collection("gift_wall_activated_ranks")
}

// AddUserRank add activated rank
func AddUserRank(periodOID primitive.ObjectID, roomID, userID, showGiftID, addNum int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	nowUnix := goutil.TimeNow().Unix()
	_, err := RankCollection().UpdateOne(ctx,
		bson.M{
			"user_id":      userID,
			"room_id":      roomID,
			"_period_id":   periodOID,
			"show_gift_id": showGiftID,
		},
		bson.M{
			"$inc": bson.M{
				"activated_num": addNum,
			},
			"$set": bson.M{
				"modified_time": nowUnix,
			},
			"$setOnInsert": bson.M{
				"create_time": nowUnix,
			},
		}, options.Update().SetUpsert(true))
	return err
}

// FindTop5 find top5 users
func FindTop5(periodOID primitive.ObjectID, roomID, showGiftID int64) ([]*ActivatedRank, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"room_id":      roomID,
		"_period_id":   periodOID,
		"show_gift_id": showGiftID,
	}
	cur, err := RankCollection().Find(ctx, filter,
		options.Find().SetSort(
			bson.D{{Key: "activated_num", Value: -1}, {Key: "modified_time", Value: 1}}).SetLimit(5),
	)
	if err != nil {
		return nil, err
	}

	res := make([]*ActivatedRank, 0, 5)
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindUserRank find user rank
func FindUserRank(periodOID primitive.ObjectID, roomID, userID, showGiftID int64) (*ActivatedRank, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	ar := new(ActivatedRank)
	err := RankCollection().FindOne(ctx, bson.M{
		"user_id":      userID,
		"room_id":      roomID,
		"_period_id":   periodOID,
		"show_gift_id": showGiftID,
	}).Decode(ar)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return ar, nil
}

// UserGiftRankTop1 user gift activated rank top1
type UserGiftRankTop1 struct {
	ShowGiftID int64 `bson:"_id"`
	UserID     int64 `bson:"user_id"`
}

// FindUserGiftsRankTop1 find user gifts activated rank top1
// return map[show_gift_id]UserGiftRankTop1
func FindUserGiftsRankTop1(periodOID primitive.ObjectID, roomID int64, showGiftIDs []int64) (map[int64]*UserGiftRankTop1, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cursor, err := RankCollection().Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{
			"_period_id":   periodOID,
			"room_id":      roomID,
			"show_gift_id": bson.M{"$in": showGiftIDs}},
		},
		bson.M{"$sort": bson.D{bson.E{Key: "activated_num", Value: -1}, bson.E{Key: "modified_time", Value: 1}}},
		bson.M{"$group": bson.M{
			"_id":     "$show_gift_id",
			"user_id": bson.M{"$first": "$user_id"},
		}},
	})
	if err != nil {
		return nil, err
	}
	var results []*UserGiftRankTop1
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, err
	}
	res := goutil.ToMap(results, "ShowGiftID").(map[int64]*UserGiftRankTop1)
	return res, nil
}
