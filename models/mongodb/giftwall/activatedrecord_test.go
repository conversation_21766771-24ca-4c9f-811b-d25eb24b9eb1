package giftwall

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRecordTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(ActivatedRecord{}, "_id", "create_time", "modified_time",
		"room_id", "creator_id", "_period_id", "show_gift_id", "activated_num", "revenue")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(updateMessage{}, "type", "event", "room_id", "time", "gift_wall", "creator")
	kc.Check(ActivatedDetail{}, "activated_num", "total_num")
}

func TestAddRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	periodOID := primitive.NewObjectID()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	defer func() {
		_, err := RecordCollection().DeleteMany(ctx, bson.M{"_period_id": periodOID})
		assert.NoError(err)
	}()
	err := AddRecord(periodOID, 1, 1, 1, 1, 1)
	require.NoError(err)

	err = AddRecord(periodOID, 1, 1, 1, 2, 1)
	require.NoError(err)

	var ar ActivatedRecord
	require.NoError(RecordCollection().FindOne(ctx, bson.M{"_period_id": periodOID}).Decode(&ar))
	assert.Equal(int64(3), ar.ActivatedNum)
	assert.Equal(int64(2), ar.Revenue)
}

func TestActiveGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup, err := createTestGiftsData()
	require.NoError(err)
	defer cleanup()
	cleanupPeriod, err := createTestPeriodData(goutil.TimeNow())
	require.NoError(err)
	defer cleanupPeriod()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = RecordCollection().DeleteMany(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}})
	require.NoError(err)
	r, err := room.FindOne(bson.M{"status.open": 1})
	require.NoError(err)
	require.NotNil(r)

	r.CreatorID = 1
	message, err := ActiveGift(r, 1, 1, 100, 1)
	require.NoError(err)
	assert.NotNil(message)

	var ar ActivatedRecord
	require.NoError(RecordCollection().FindOne(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}}).Decode(&ar))
	assert.Equal(int64(1), ar.ActivatedNum)
	assert.Equal(int64(100), ar.Revenue)
	assert.Equal(testShowGiftIDs[1], ar.ShowGiftID)
	assert.Equal(r.RoomID, ar.RoomID)

	message, err = ActiveGift(r, 1, 1, 100, 5)
	require.NoError(err)
	assert.Nil(message)

	var newRecord ActivatedRecord
	require.NoError(RecordCollection().FindOne(ctx, bson.M{"show_gift_id": bson.M{"$in": testShowGiftIDs}}).Decode(&newRecord))
	assert.Equal(int64(6), newRecord.ActivatedNum)
	assert.Equal(int64(200), newRecord.Revenue)
}

func TestActivatedCountByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(223344)
	testUserID := int64(100)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID, "creator_id": testUserID})
	require.NoError(err)
	periodOID := primitive.NewObjectID()
	_, err = RecordCollection().InsertOne(ctx, ActivatedRecord{
		PeriodOID: periodOID,
		RoomID:    testRoomID,
	})
	require.NoError(err)

	count, err := ActivatedCountByRoomID(periodOID, testRoomID)
	require.NoError(err)
	assert.EqualValues(count, 1)
}

func TestFindActivatedDetail(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftIDs := []int64{1000, 2000}
	testRoomID := int64(223344)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()}, "end_time": bson.M{"$gt": now.Unix()}})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
	})
	require.NoError(err)
	_, err = RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = RecordCollection().InsertOne(ctx, ActivatedRecord{
		PeriodOID: res.InsertedID.(primitive.ObjectID),
		RoomID:    testRoomID,
	})
	require.NoError(err)

	ad, err := FindActivatedDetail(testRoomID)
	require.NoError(err)
	require.NotNil(ad)
	assert.EqualValues(len(testShowGiftIDs), ad.TotalNum)
	assert.EqualValues(1, ad.ActivatedNum)
}

func TestFindOneRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := FindOneRecord(bson.M{"room_id": -1})
	require.NoError(err)
	assert.Nil(res)

	res, err = FindOneRecord(bson.M{})
	require.NoError(err)
	assert.NotNil(res)
}

func TestListRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := ListRecord(bson.M{"room_id": -999})
	require.NoError(err)
	assert.Empty(list)

	list, err = ListRecord(bson.M{})
	require.NoError(err)
	assert.NotEmpty(list)
}
