package giftwall

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// PromotionRevenueThreshold 收益展示变色阈值
const PromotionRevenueThreshold = 50000 // 单位: 钻石

// ActivatedRecord 直播间礼物墙点亮记录
type ActivatedRecord struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	RoomID       int64              `bson:"room_id"`
	CreatorID    int64              `bson:"creator_id"`
	PeriodOID    primitive.ObjectID `bson:"_period_id"`
	ShowGiftID   int64              `bson:"show_gift_id"`  // 上墙礼物 ID
	ActivatedNum int64              `bson:"activated_num"` // 对应上墙礼物的激活数量
	Revenue      int64              `bson:"revenue"`
}

// RecordCollection 返回 ActivatedRecord 的 Collection
func RecordCollection() *mongo.Collection {
	return service.MongoDB.Collection("gift_wall_activated_records")
}

// AddRecord add activated record
func AddRecord(periodOID primitive.ObjectID, roomID, creatorID, showGiftID, addNum, addRevenue int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	nowUnix := goutil.TimeNow().Unix()
	incData := bson.M{
		"activated_num": addNum,
	}
	if addRevenue > 0 {
		// 免费礼物不计算价值
		incData["revenue"] = addRevenue
	}
	_, err := RecordCollection().UpdateOne(ctx,
		bson.M{
			"room_id":      roomID,
			"_period_id":   periodOID,
			"show_gift_id": showGiftID,
		},
		bson.M{
			"$inc": incData,
			"$set": bson.M{
				"modified_time": nowUnix,
			},
			"$setOnInsert": bson.M{
				"creator_id":  creatorID,
				"create_time": nowUnix,
			},
		}, options.Update().SetUpsert(true))
	return err
}

// ActiveGift 点亮礼物
func ActiveGift(r *room.Room, fromUserID, receiveGiftID, receiveRevenue int64, receiveNum int) (*userapi.BroadcastElem, error) {
	now := goutil.TimeNow()
	// 获取周期信息
	p, err := CurrentPeriodInfo(now)
	if err != nil {
		return nil, err
	}
	if p == nil {
		// 周期不存在
		return nil, nil
	}
	// 判断礼物信息
	gs, err := FindGiftsByShowGiftIDs(p.ShowGiftIDs)
	if err != nil {
		return nil, err
	}
	var showGiftID int64
	for i := range gs {
		if goutil.HasElem(gs[i].EffectiveGiftIDs, receiveGiftID) {
			showGiftID = gs[i].ShowGiftID
			break
		}
	}
	if showGiftID == 0 {
		// 非有效礼物
		return nil, nil
	}
	// 添加点亮记录
	err = AddRecord(p.OID, r.RoomID, r.CreatorID, showGiftID, int64(receiveNum), receiveRevenue)
	if err != nil {
		return nil, err
	}
	// 添加用户点亮榜单
	err = AddUserRank(p.OID, r.RoomID, fromUserID, showGiftID, int64(receiveNum))
	if err != nil {
		return nil, err
	}
	activatedGiftKey := keys.KeyRoomGiftWallActivatedGiftIDs2.Format(r.RoomID, p.OID.Hex())
	pipe := service.Redis.TxPipeline()
	addCmd := pipe.SAdd(activatedGiftKey, showGiftID)
	countCmd := pipe.SCard(activatedGiftKey)
	activatedGiftKeyTTLCmd := pipe.TTL(activatedGiftKey)
	_, err = pipe.Exec()
	if err != nil {
		return nil, err
	}
	res := addCmd.Val()
	if res == 0 {
		// 礼物非首次点亮，不再发奖励和发消息
		return nil, nil
	}
	if ttl := activatedGiftKeyTTLCmd.Val(); ttl == -1 {
		// 7 days extend
		serviceredis.ExpireAt(service.Redis, activatedGiftKey, time.Unix(p.EndTime, 0).Add(7*24*time.Hour))
	}

	// 点亮总数量变化消息
	count := countCmd.Val()
	message := newUpdateNotifyMessage(r, count, p.TotalNum())
	// 发放礼物墙奖励
	for i := range p.Rewards {
		if count == p.Rewards[i].Threshold {
			switch p.Rewards[i].Type {
			case RewardTypeCustomGift:
				err = livecustom.AddRoomCustomGift(r.RoomID, p.Rewards[i].ElementID, now, time.Unix(p.EndTime, 0), livecustom.SourceDefault)
			case RewardTypeAppearance:
				aItem, err2 := appearance.FindOne(p.Rewards[i].ElementID, appearance.TypeNoSpecified)
				if err2 != nil {
					return message, err2
				}
				if aItem == nil {
					return message, fmt.Errorf("外观 (ID: %d) 奖励不存在", p.Rewards[i].ElementID)
				}
				err = userappearance.AddAppearance(r.CreatorID, 0, p.EndTime, aItem)
			}
			if err != nil {
				return message, err // 发奖失败，不影响直播间消息
			}
			break
		}
	}
	return message, nil
}

// updateMessage gift wall activated message
type updateMessage struct {
	Type     string           `json:"type"`
	Event    string           `json:"event"`
	RoomID   int64            `json:"room_id"`
	Time     int64            `json:"time"` // 秒级时间戳
	GiftWall *ActivatedDetail `json:"gift_wall"`
	Creator  *liveuser.Simple `json:"creator"`
}

// ActivatedDetail activated detail
type ActivatedDetail struct {
	ActivatedNum int64 `json:"activated_num"`
	TotalNum     int64 `json:"total_num"`
}

func newUpdateNotifyMessage(r *room.Room, activatedCount, totalNum int64) *userapi.BroadcastElem {
	u, err := mowangskuser.FindByUserID(r.CreatorID)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if u == nil {
		return nil
	}
	return &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: r.RoomID,
		Payload: updateMessage{
			Type:   liveim.TypeGiftWall,
			Event:  liveim.EventGiftWallUpdate,
			RoomID: r.RoomID,
			Time:   goutil.TimeNow().Unix(),
			GiftWall: &ActivatedDetail{
				ActivatedNum: activatedCount,
				TotalNum:     totalNum,
			},
			Creator: &liveuser.Simple{
				UID:      r.CreatorID,
				Username: r.CreatorUsername,
				IconURL:  u.IconURL,
			},
		},
	}
}

// ActivatedCountByRoomID 查询直播间上墙礼物的点亮次数
func ActivatedCountByRoomID(periodOID primitive.ObjectID, roomID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	count, err := RecordCollection().CountDocuments(ctx, bson.M{
		"_period_id": periodOID,
		"room_id":    roomID,
	})
	if err != nil {
		return 0, err
	}
	return count, nil
}

// FindActivatedDetail find giftwall activated detail
func FindActivatedDetail(roomID int64) (*ActivatedDetail, error) {
	period, err := CurrentPeriodInfo(goutil.TimeNow())
	if err != nil {
		return nil, err
	}
	if period == nil {
		return nil, nil
	}
	count, err := ActivatedCountByRoomID(period.OID, roomID)
	if err != nil {
		return nil, err
	}
	return &ActivatedDetail{ActivatedNum: count, TotalNum: period.TotalNum()}, nil
}

// FindOneRecord 获取点亮记录
func FindOneRecord(filter bson.M) (*ActivatedRecord, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	ar := new(ActivatedRecord)
	err := RecordCollection().FindOne(ctx, filter).Decode(ar)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return ar, nil
}

// ListRecord 获取点亮记录
func ListRecord(filter bson.M, findOptions ...*options.FindOptions) ([]*ActivatedRecord, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := RecordCollection().Find(ctx, filter, findOptions...)
	if err != nil {
		return nil, err
	}
	var res []*ActivatedRecord
	err = cur.All(ctx, &res)
	return res, err
}
