package giftwall

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindByPeriodOID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	testShowGiftIDs := []int64{1000}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx,
		bson.M{
			"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()},
			"end_time":   bson.M{"$gt": now.Unix()},
		})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, &Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
	})
	require.NoError(err)

	gifts, err := FindByPeriodOID(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.Equal(gifts.ShowGiftIDs, testShowGiftIDs)
}

func createTestPeriodData(when time.Time) (func(), error) {
	p := &Period{
		CreateTime:   when.Unix(),
		ModifiedTime: when.Unix(),
		StartTime:    when.Add(-time.Minute).Unix(),
		EndTime:      when.Add(time.Minute).Unix(),
		ShowGiftIDs:  testShowGiftIDs,
		Rewards: []*RewardInfo{
			{Threshold: 1, Type: RewardTypeCustomGift, ElementID: 1},
			{Threshold: 2, Type: RewardTypeAppearance, ElementID: 1000},
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().InsertOne(ctx, p)
	if err != nil {
		return nil, err
	}

	return cleanTestPeriodData, nil
}

func cleanTestPeriodData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{"show_gift_ids": testShowGiftIDs})
	if err != nil {
		logger.Error(err)
	}
}

func TestPeriod_TotalNum(t *testing.T) {
	assert := assert.New(t)

	p := Period{
		ShowGiftIDs: []int64{1, 2, 3},
	}
	assert.Equal(int64(3), p.TotalNum())
}

func TestFindCurrentPeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	p, err := findCurrentPeriod(now)
	require.NoError(err)
	assert.Nil(p)

	cleanup, err := createTestPeriodData(now)
	require.NoError(err)
	defer cleanup()
	p, err = findCurrentPeriod(now)
	require.NoError(err)
	assert.NotNil(p)
}

func TestCurrentPeriodInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	service.Cache5Min.Flush()
	p, err := CurrentPeriodInfo(now)
	require.NoError(err)
	assert.Nil(p)

	cleanup, err := createTestPeriodData(now)
	require.NoError(err)
	defer cleanup()
	service.Cache5Min.Flush()
	p, err = CurrentPeriodInfo(now)
	require.NoError(err)
	assert.NotNil(p)

	cacheP, err := CurrentPeriodInfo(now)
	require.NoError(err)
	assert.NotNil(cacheP)
	assert.Equal(p, cacheP)
}

func TestDelPeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime := now.Unix()
	endTime := now.Add(PeriodDuration).Unix()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := PeriodCollection().InsertOne(ctx, bson.M{
		"start_time": startTime,
		"end_time":   endTime,
	})
	require.NoError(err)
	ok, err := DelPeriod(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.False(ok)

	_, err = PeriodCollection().UpdateOne(ctx,
		bson.M{"_id": res.InsertedID},
		bson.M{"$set": bson.M{"start_time": startTime + 1}},
	)
	require.NoError(err)
	ok, err = DelPeriod(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	assert.True(ok)
}

func TestFindPeriods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	startTime, endTime := now, now.Add(PeriodDuration)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{
		"start_time": bson.M{"$lt": endTime.Unix()},
		"end_time":   bson.M{"$gt": startTime.Unix()},
	})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, bson.M{
		"start_time": startTime.Unix(),
		"end_time":   endTime.Unix(),
	})
	require.NoError(err)

	ps, err := FindPeriods(startTime, endTime)
	require.NoError(err)
	require.NotEmpty(ps)
	assert.Equal(res.InsertedID, ps[0].OID)

	ps, err = FindPeriods(now.Add(-time.Hour), now)
	require.NoError(err)
	require.Empty(ps)
}

func TestCreatePeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	err = CreatePeriod(now, now.Add(PeriodDuration), []int64{1}, []*RewardInfo{{Threshold: 1, Type: RewardTypeAppearance, ElementID: 2}})
	require.NoError(err)

	var p *Period
	err = PeriodCollection().FindOne(ctx, bson.M{"show_gift_ids": bson.M{"$in": bson.A{1}}}).Decode(&p)
	require.NoError(err)
	require.NotNil(p)
	assert.Equal(now.Unix(), p.StartTime)
	require.NotEmpty(p.Rewards)
	assert.Equal(RewardInfo{Threshold: 1, Type: RewardTypeAppearance, ElementID: 2}, *p.Rewards[0])
}

func TestFindUnfinishedPeriodByShowGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 3, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx, bson.M{
		"show_gift_ids": []int64{1010101},
	})
	require.NoError(err)
	now := goutil.TimeNow()
	_, err = PeriodCollection().InsertMany(ctx, bson.A{
		bson.M{"show_gift_ids": []int64{1010101}, "end_time": now.Unix()},
		bson.M{"show_gift_ids": []int64{1010101}, "end_time": now.Add(time.Second).Unix()},
	})
	require.NoError(err)

	p, err := FindUnfinishedPeriodByShowGiftID(1010101)
	require.NoError(err)
	require.NotNil(p)
	assert.Greater(p.EndTime, now.Unix())

	p, err = FindUnfinishedPeriodByShowGiftID(909090)
	require.NoError(err)
	assert.Nil(p)
}

func TestUpdatePeriod(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 0, 0, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	testShowGiftIDs := []int64{1000}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PeriodCollection().DeleteMany(ctx,
		bson.M{
			"start_time": bson.M{"$lt": now.Add(1 * time.Hour).Unix()},
			"end_time":   bson.M{"$gt": now.Unix()},
		})
	require.NoError(err)
	res, err := PeriodCollection().InsertOne(ctx, &Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
		Rewards: []*RewardInfo{
			{Threshold: 1, Type: RewardTypeCustomGift, ElementID: 1},
			{Threshold: 2, Type: RewardTypeAppearance, ElementID: 2},
		},
	})
	require.NoError(err)

	objectOID := res.InsertedID.(primitive.ObjectID)
	// 测试只更新上墙礼物
	err = UpdatePeriod(objectOID, []int64{2000, 3000}, []*RewardInfo{})
	require.NoError(err)
	// 断言更新了上墙礼物，没有更新奖励设置
	p, err := FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{2000, 3000}, p.ShowGiftIDs)
	assert.Equal([]*RewardInfo{
		{Threshold: 1, Type: RewardTypeCustomGift, ElementID: 1},
		{Threshold: 2, Type: RewardTypeAppearance, ElementID: 2},
	}, p.Rewards)

	// 测试只更新奖励设置
	err = UpdatePeriod(objectOID, []int64{}, []*RewardInfo{
		{Threshold: 3, Type: RewardTypeCustomGift, ElementID: 10},
		{Threshold: 4, Type: RewardTypeAppearance, ElementID: 20},
	})
	require.NoError(err)
	// 断言更新了奖励设置，没有更新上墙礼物
	p, err = FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{2000, 3000}, p.ShowGiftIDs)
	assert.Equal([]*RewardInfo{
		{Threshold: 3, Type: RewardTypeCustomGift, ElementID: 10},
		{Threshold: 4, Type: RewardTypeAppearance, ElementID: 20},
	}, p.Rewards)

	// 测试更新上墙礼物、奖励设置
	err = UpdatePeriod(objectOID, []int64{4000, 5000}, []*RewardInfo{
		{Threshold: 5, Type: RewardTypeCustomGift, ElementID: 100},
		{Threshold: 6, Type: RewardTypeAppearance, ElementID: 200},
	})
	require.NoError(err)
	// 断言更新了上墙礼物、奖励设置
	p, err = FindByPeriodOID(objectOID)
	require.NoError(err)
	assert.Equal([]int64{4000, 5000}, p.ShowGiftIDs)
	assert.Equal([]*RewardInfo{
		{Threshold: 5, Type: RewardTypeCustomGift, ElementID: 100},
		{Threshold: 6, Type: RewardTypeAppearance, ElementID: 200},
	}, p.Rewards)
}
