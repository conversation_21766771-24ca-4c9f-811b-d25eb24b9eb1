package giftwall

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Gift{}, "_id", "create_time", "modified_time",
		"show_gift_id", "target_gift_id", "effective_gift_ids")
	kc.Check(Period{}, "_id", "create_time", "modified_time",
		"start_time", "end_time", "show_gift_ids", "rewards")
}

var testShowGiftIDs = []int64{1000, 2000, 10095}

func createTestGiftsData() (func(), error) {
	wallGifts := make([]interface{}, 0, len(testShowGiftIDs))
	for i, id := range testShowGiftIDs {
		wallGifts = append(wallGifts, &Gift{
			CreateTime:       goutil.TimeNow().Unix(),
			ModifiedTime:     goutil.TimeNow().Unix(),
			ShowGiftID:       id,
			TargetGiftID:     id,
			EffectiveGiftIDs: []int64{int64(i)},
		})
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().InsertMany(ctx, wallGifts)
	if err != nil {
		return nil, err
	}

	return cleanTestGiftsData, nil
}

func cleanTestGiftsData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().DeleteMany(ctx, bson.M{"show_gift_id": bson.M{"$in": []int64{1000, 2000, 10095}}})
	if err != nil {
		logger.Error(err)
	}
}

func TestListGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup, err := createTestGiftsData()
	require.NoError(err)
	defer cleanup()

	gifts, pa, err := ListGifts(1, 20)
	require.NoError(err)
	assert.EqualValues(1, pa.P)
	assert.EqualValues(20, pa.PageSize)
	assert.LessOrEqual(len(gifts), 20)
}

func TestAddGiftWallGift(t *testing.T) {
	assert := assert.New(t)

	err := AddGiftWallGift(1000, 2000, []int64{1000, 2000, 10095})
	assert.NoError(err)
}

func TestFindOneByShowGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)

	g, err := FindOneByShowGiftID(testShowGiftID)
	require.NoError(err)
	assert.NotNil(g)
}

func TestDelGiftByShowGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftID := int64(1000)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": testShowGiftID,
	})
	require.NoError(err)
	_, err = GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       testShowGiftID,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)

	err = DelGiftByShowGiftID(testShowGiftID)
	require.NoError(err)

	g, err := FindOneByShowGiftID(testShowGiftID)
	require.NoError(err)
	assert.Nil(g)
}

func TestUpdateOneByShowGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	_, err = GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       1000,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)

	err = UpdateOneByShowGiftID(1000, 2000, []int64{1000, 3000, 10095})
	assert.NoError(err)

	g := new(Gift)
	err = GiftCollection().FindOne(ctx, bson.M{"show_gift_id": 1000}).Decode(g)
	require.NoError(err)
	require.NotNil(g)
	assert.EqualValues(3000, g.EffectiveGiftIDs[1])
}

func TestExistEffectiveGiftIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().DeleteMany(ctx, bson.M{
		"show_gift_id": bson.M{"$in": bson.A{1000, 2000, 10095}},
	})
	require.NoError(err)
	res, err := GiftCollection().InsertOne(ctx, bson.M{
		"show_gift_id":       1000,
		"target_gift_id":     2000,
		"effective_gift_ids": bson.A{1000, 2000, 10095},
	})
	require.NoError(err)

	// 忽略指定记录
	exists, ids, err := ExistEffectiveGiftIDs([]int64{1000, 2000, 10095}, &Options{IgnoreOID: res.InsertedID.(primitive.ObjectID)})
	require.NoError(err)
	assert.False(exists)
	assert.Empty(ids)

	// 不忽略指定记录
	exists, ids, err = ExistEffectiveGiftIDs([]int64{1000, 2000, 10095})
	require.NoError(err)
	assert.True(exists)
	assert.NotEmpty(ids)
}

func TestFindGiftsByShowGiftIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGiftIDs := []int64{1000, 2000, 10095}
	wallGifts := make([]interface{}, 0, len(testGiftIDs))
	for _, id := range testGiftIDs {
		wallGifts = append(wallGifts, &Gift{
			CreateTime:       goutil.TimeNow().Unix(),
			ModifiedTime:     goutil.TimeNow().Unix(),
			ShowGiftID:       id,
			TargetGiftID:     id,
			EffectiveGiftIDs: testGiftIDs,
		})
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := GiftCollection().InsertMany(ctx, wallGifts)
	require.NoError(err)

	gifts, err := FindGiftsByShowGiftIDs(testGiftIDs)
	require.NoError(err)
	assert.GreaterOrEqual(len(gifts), 3)
	assert.LessOrEqual(len(gifts), 20)
}

func TestNewListItem(t *testing.T) {
	assert := assert.New(t)

	giftwall := &Gift{
		ShowGiftID:       1,
		TargetGiftID:     2,
		EffectiveGiftIDs: []int64{3, 1, 2},
	}
	giftMap := map[int64]*gift.Gift{
		1: {GiftID: 1, Name: "1", Icon: "icon1"},
		2: {GiftID: 2, Name: "2", Icon: "icon2"},
		3: {GiftID: 3, Name: "3", Icon: "icon3"},
	}
	item := NewListItem(giftwall, giftMap)
	assert.NotNil(item)
	assert.Equal("1", item.ShowGiftID)
	assert.Equal("2", item.TargetGiftID)
	assert.Equal("3,1,2", item.EffectiveGiftID)
	assert.Equal("icon3", item.EffectiveGiftIconURL)
}
