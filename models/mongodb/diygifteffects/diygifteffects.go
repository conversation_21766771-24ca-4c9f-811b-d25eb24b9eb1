package diygifteffects

import (
	"fmt"
	"sort"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// Status 特效状态
const (
	StatusPending = iota
	StatusValid
	StatusExpire
)

// DiyGiftEffect 定制礼物特效
type DiyGiftEffect struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`
	GiftID       int64              `bson:"gift_id"`
	UniqueID     string             `bson:"unique_id"`
	UniqueName   string             `bson:"unique_name"`
	Effect       string             `bson:"effect"`
	OldEffect    string             `bson:"old_effect"` // WORKAROUND: 兼容旧版本客户端（安卓 < 5.6.8 iOS < 5.8.0）的特效
	WebEffect    string             `bson:"web_effect"`
	Status       int                `bson:"status"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("diy_gift_effects")
}

// Dress 生成特效的装扮
type Dress struct {
	Type    int    `form:"type" json:"type"`
	DressID string `form:"dress_id" json:"dress_id"`
}

// UniqueID 生成 unique_id
// 格式：${d[0].type}:${d[0].dress_id}|${d[1].type}:${d[1].dress_id}|...
func UniqueID(d []Dress) string {
	sort.Slice(d, func(i, j int) bool {
		return d[i].Type < d[j].Type
	})
	pairs := make([]string, len(d))
	for i := range d {
		pairs[i] = fmt.Sprintf("%d:%s", d[i].Type, d[i].DressID)
	}
	return strings.Join(pairs, "|")
}

// FindValidEffect 查询生效中的礼物特效
func FindValidEffect(giftID int64, uniqueID string) (*DiyGiftEffect, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var e DiyGiftEffect
	err := Collection().FindOne(ctx,
		bson.M{"gift_id": giftID, "unique_id": uniqueID, "status": StatusValid}).Decode(&e)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return &e, nil
}

// GetDiyGiftsEffectCountMap 定制礼物特效数量
func GetDiyGiftsEffectCountMap(giftIDs []int64) (map[int64]int, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{
		bson.M{
			"$match": bson.M{
				"gift_id": bson.M{"$in": giftIDs},
				"status":  StatusValid,
			},
		},
		bson.M{
			"$group": bson.M{
				"_id":   "$gift_id",
				"count": bson.M{"$sum": 1},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var diyGiftsEffectCountList []struct {
		GiftID int64 `bson:"_id"`
		Count  int   `bson:"count"`
	}
	if err = cur.All(ctx, &diyGiftsEffectCountList); err != nil {
		return nil, err
	}
	resMap := make(map[int64]int, len(diyGiftsEffectCountList))
	for _, diyGiftsEffect := range diyGiftsEffectCountList {
		resMap[diyGiftsEffect.GiftID] = diyGiftsEffect.Count
	}
	return resMap, nil
}
