package diygifteffects

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(DiyGiftEffect{}, "_id", "create_time", "modified_time",
		"gift_id", "unique_id", "unique_name",
		"effect", "old_effect", "web_effect",
		"status")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Dress{}, "type", "dress_id")
}

func TestUniqueID(t *testing.T) {
	assert := assert.New(t)

	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(Dress{}, "type", "dress_id")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Dress{}, "type", "dress_id")

	d := []Dress{
		{Type: 2, DressID: "456"},
		{Type: 1, DressID: "123"},
	}
	uniqueID := UniqueID(d)
	assert.Equal("1:123|2:456", uniqueID)
}

func TestFindValidEffect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	e := DiyGiftEffect{
		GiftID:    1234,
		UniqueID:  "test",
		Effect:    "123.mp4",
		WebEffect: "456.mp4",
		Status:    StatusValid,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	_, err := col.UpdateOne(ctx,
		bson.M{"gift_id": e.GiftID, "unique_id": e.UniqueID},
		bson.M{"$set": e},
		options.Update().SetUpsert(true))
	require.NoError(err)

	after, err := FindValidEffect(e.GiftID, e.UniqueID)
	require.NoError(err)
	require.NotNil(after)
	assert.Equal(e.Effect, after.Effect)
	assert.Equal(e.WebEffect, after.WebEffect)

	_, err = col.UpdateOne(ctx,
		bson.M{"gift_id": e.GiftID, "unique_id": e.UniqueID},
		bson.M{"$set": bson.M{"status": StatusExpire}})
	require.NoError(err)
	after, err = FindValidEffect(e.GiftID, e.UniqueID)
	require.NoError(err)
	require.Nil(after)
}

func TestGetDiyGiftsEffectCountMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	effectCountMap, err := GetDiyGiftsEffectCountMap([]int64{2, 3, 4})
	require.NoError(err)
	effectCount, ok := effectCountMap[2]
	require.True(ok)
	assert.EqualValues(2, effectCount)
	effectCount, ok = effectCountMap[3]
	require.True(ok)
	assert.EqualValues(1, effectCount)
	_, ok = effectCountMap[4]
	require.False(ok)
}
