package liveuser

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const (
	medalNotifyLevelThreshold = 9
)

// MedalNotify medal notify
type MedalNotify struct {
	Type   string         `json:"type"`
	Event  string         `json:"event"`
	RoomID int64          `json:"room_id"`
	User   *Simple        `json:"user,omitempty"`
	Room   *medalRoomInfo `json:"room,omitempty"`
	Medal  *medalInfo     `json:"medal"`
	Bubble *bubble.Simple `json:"bubble,omitempty"`
}

type medalRoomInfo struct {
	RoomID          int64  `json:"room_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
}

type medalInfo struct {
	Name     string              `json:"name"`
	Level    int                 `json:"level"`
	FrameURL string              `json:"frame_url,omitempty"`
	SuperFan *livemedal.SuperFan `json:"super_fan,omitempty"`
}

func isGetNewMedal(medalUpdatedInfo *livemedal.MedalUpdatedInfo) bool {
	return (medalUpdatedInfo.Before == nil || medalUpdatedInfo.Before.Status == livemedal.StatusPending) &&
		medalUpdatedInfo.After.Status != livemedal.ShareStatusPending
}

// 勋章升级 >= 9 级时，需要发送勋章升级消息
func isNotifiableLevelUp(medalUpdatedInfo *livemedal.MedalUpdatedInfo) bool {
	afterLevel := livemedal.ParseLevel(medalUpdatedInfo.After.Point)
	return medalUpdatedInfo.Before != nil && medalUpdatedInfo.Before.Status != livemedal.StatusPending &&
		medalUpdatedInfo.After.Status != livemedal.StatusPending &&
		afterLevel > livemedal.ParseLevel(medalUpdatedInfo.Before.Point) &&
		afterLevel >= medalNotifyLevelThreshold
}

// MedalNotifyParam medal notify param
type MedalNotifyParam struct {
	MedalUpdatedInfo *livemedal.MedalUpdatedInfo
	User             *Simple         // 需要是有对应房间 titles 的用户信息
	BubblePtr        **bubble.Simple // *BubblePtr 值为 nil 时，不再根据 userID 查询外观
	CreatorUsername  string

	UserID int64 // 当参数中 User 为空时，根据 userID 和 roomID 查询相应信息
	RoomID int64
}

func (m *MedalNotifyParam) notifyUser() (*Simple, error) {
	if m.User != nil {
		return m.User, nil
	}
	u, err := FindOneSimple(bson.M{"user_id": m.UserID},
		&FindOptions{FindTitles: true, RoomID: m.RoomID})
	if err != nil {
		return nil, err
	}
	m.User = u
	return u, err
}

func (m *MedalNotifyParam) notifyBubble() *bubble.Simple {
	if m.BubblePtr != nil {
		return *m.BubblePtr
	}
	b, err := userappearance.FindMessageBubble(m.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	m.BubblePtr = &b
	return b
}

// NewUserMedalNotify get new medal notify
// NOTICE: 调用此方法需要保证 MedalUpdatedInfo.After 中包含定制粉丝勋章框和勋章等级完整的勋章信息
func (m *MedalNotifyParam) NewUserMedalNotify() *userapi.BroadcastElem {
	if m == nil || m.MedalUpdatedInfo == nil || m.MedalUpdatedInfo.After == nil {
		return nil
	}
	u, err := m.notifyUser()
	if err != nil {
		logger.Error(err)
		return nil
	}
	var payload *MedalNotify
	if isGetNewMedal(m.MedalUpdatedInfo) {
		// 新获得勋章
		payload = &MedalNotify{
			Type:   liveim.TypeMember,
			Event:  liveim.EventMedalGetNew,
			RoomID: m.MedalUpdatedInfo.After.RoomID,
			User:   u,
			Room: &medalRoomInfo{
				RoomID:          m.MedalUpdatedInfo.After.RoomID,
				CreatorID:       m.MedalUpdatedInfo.After.CreatorID,
				CreatorUsername: m.CreatorUsername,
			},
			Medal: &medalInfo{
				Name:     m.MedalUpdatedInfo.After.Name,
				Level:    m.MedalUpdatedInfo.After.Level,
				FrameURL: m.MedalUpdatedInfo.After.FrameURL,
				SuperFan: m.MedalUpdatedInfo.After.SuperFan,
			},
			Bubble: m.notifyBubble(),
		}
	} else if isNotifiableLevelUp(m.MedalUpdatedInfo) {
		// 升级消息
		payload = &MedalNotify{
			Type:   liveim.TypeMember,
			Event:  liveim.EventMedalLevelUp,
			RoomID: m.MedalUpdatedInfo.After.RoomID,
			User:   u,
			Medal: &medalInfo{
				Name:     m.MedalUpdatedInfo.After.Name,
				Level:    m.MedalUpdatedInfo.After.Level,
				FrameURL: m.MedalUpdatedInfo.After.FrameURL,
				SuperFan: m.MedalUpdatedInfo.After.SuperFan,
			},
			Bubble: m.notifyBubble(),
		}
	}
	if payload != nil {
		// 对用户在直播间内勋章升级加 5 秒的锁，防止发送重复的升级消息
		lock := keys.LockRoomUserMedalNotify3.Format(m.MedalUpdatedInfo.After.RoomID,
			m.MedalUpdatedInfo.After.UserID, m.MedalUpdatedInfo.After.Level)
		ok, err := service.Redis.SetNX(lock, "1", 5*time.Second).Result()
		if err != nil {
			logger.Error(err)
			return nil
		}
		if !ok {
			return nil
		}
		return &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  m.MedalUpdatedInfo.After.RoomID,
			Payload: payload,
		}
	}
	return nil
}
