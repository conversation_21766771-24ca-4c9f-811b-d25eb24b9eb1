package liveuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
)

func TestFindOneLiveUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lu, err := FindOneLiveUser(bson.M{"user_id": bson.M{"$gt": 0}})
	require.NoError(err)
	assert.NotNil(lu)

	lu, err = FindOneLiveUser(bson.M{"user_id": -11111})
	require.NoError(err)
	assert.Nil(lu)
}
