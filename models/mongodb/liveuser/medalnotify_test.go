package liveuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(MedalNotify{}, "type", "event", "room_id", "user", "room", "medal", "bubble")
	kc.Check(medalRoomInfo{}, "room_id", "creator_id", "creator_username")
	kc.Check(medalInfo{}, "name", "level", "frame_url", "super_fan")
}

var (
	testMedalPending = &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Point:  10,
			Status: livemedal.StatusPending,
		},
	}

	testMedalLevel8 = &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Point:  1799,
			Status: livemedal.StatusShow,
			Mini: livemedal.Mini{
				Level: 8,
			},
		},
	}

	testMedalLevel9 = &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Point:  1800,
			Status: livemedal.StatusShow,
			Mini: livemedal.Mini{
				Level: 9,
			},
		},
	}
)

func TestIsGetNewMedal(t *testing.T) {
	assert := assert.New(t)

	assert.False(isGetNewMedal(&livemedal.MedalUpdatedInfo{
		Before: testMedalPending, After: testMedalPending,
	}))
	assert.False(isGetNewMedal(&livemedal.MedalUpdatedInfo{
		Before: testMedalLevel8, After: testMedalLevel9,
	}))
	assert.True(isGetNewMedal(&livemedal.MedalUpdatedInfo{
		Before: testMedalPending, After: testMedalLevel8,
	}))
}

func TestIsNotifiableLevelUp(t *testing.T) {
	assert := assert.New(t)

	assert.False(isNotifiableLevelUp(&livemedal.MedalUpdatedInfo{
		Before: testMedalPending, After: testMedalPending,
	}))
	assert.False(isNotifiableLevelUp(&livemedal.MedalUpdatedInfo{
		Before: testMedalPending, After: testMedalLevel8,
	}))
	assert.False(isNotifiableLevelUp(&livemedal.MedalUpdatedInfo{
		Before: testMedalPending, After: testMedalLevel9,
	}))
	assert.False(isNotifiableLevelUp(&livemedal.MedalUpdatedInfo{
		Before: testMedalLevel8, After: testMedalLevel8,
	}))
	assert.True(isNotifiableLevelUp(&livemedal.MedalUpdatedInfo{
		Before: testMedalLevel8, After: testMedalLevel9,
	}))
}

func TestNotifyBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := &bubble.Simple{
		BubbleID: 1234,
	}
	p := MedalNotifyParam{
		BubblePtr: &b,
	}
	b = p.notifyBubble()
	require.NotNil(b)
	assert.EqualValues(1234, b.BubbleID)

	p = MedalNotifyParam{
		BubblePtr: nil,
	}
	assert.Nil(p.notifyBubble())
}

func TestNewUserMedalNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := MedalNotifyParam{
		User:            &Simple{},
		BubblePtr:       nil,
		CreatorUsername: "test",
	}

	assert.Nil(p.NewUserMedalNotify())

	p.MedalUpdatedInfo = &livemedal.MedalUpdatedInfo{
		Before: nil,
		After:  testMedalPending,
	}
	assert.Nil(p.NewUserMedalNotify())

	p.MedalUpdatedInfo = &livemedal.MedalUpdatedInfo{
		Before: nil,
		After:  testMedalLevel8,
	}
	notify := p.NewUserMedalNotify()
	require.NotNil(notify)
	assert.Equal(liveim.EventMedalGetNew, notify.Payload.(*MedalNotify).Event)

	p.MedalUpdatedInfo = &livemedal.MedalUpdatedInfo{
		Before: testMedalLevel8,
		After:  testMedalLevel8,
	}
	notify = p.NewUserMedalNotify()
	assert.Nil(notify)

	p.MedalUpdatedInfo = &livemedal.MedalUpdatedInfo{
		Before: testMedalLevel8,
		After:  testMedalLevel9,
	}
	notify = p.NewUserMedalNotify()
	require.NotNil(notify)
	assert.Equal(liveim.EventMedalLevelUp, notify.Payload.(*MedalNotify).Event)

	// 测试锁生效
	notify = p.NewUserMedalNotify()
	assert.Nil(notify)
}
