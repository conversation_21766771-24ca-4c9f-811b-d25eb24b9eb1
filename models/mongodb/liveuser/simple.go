package liveuser

import (
	"sort"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// enum Title.Type
// 勋章顺序：超管 > 主播/房管 > 贵族 > 粉丝勋章 > 直播等级
const (
	TitleTypeStaff         = "staff"
	TitleTypeNoble         = "noble"
	TitleTypeHighness      = "highness"
	TitleTypeMedal         = "medal"
	TitleTypeLevel         = "level"
	TitleTypeAvatarFrame   = "avatar_frame"
	TitleTypeUsername      = "username"
	TitleTypeBadge         = "badge"
	TitleTypeIdentityBadge = "identity_badge"
)

var (
	// ProjectionSimple 查找 Simple 用
	ProjectionSimple = struct {
		UserID       int `bson:"user_id,omitempty"`
		Username     int `bson:"username,omitempty"`
		NameColor    int `bson:"name_color,omitempty"`
		IconURL      int `bson:"iconurl,omitempty"`
		Group        int `bson:"group,omitempty"`
		Contribution int `bson:"contribution,omitempty"`
	}{1, 1, 1, 1, 1, 1}

	// ProjectionTitles 只查找用户 titles 用
	ProjectionTitles = struct {
		UserID       int `bson:"user_id,omitempty"` // 查询勋章需要用户 ID
		NameColor    int `bson:"name_color,omitempty"`
		Group        int `bson:"group,omitempty"`
		Contribution int `bson:"contribution,omitempty"`
	}{1, 1, 1, 1}
)

// Title 用户 title
type Title struct {
	Type      string              `json:"type"`
	Name      string              `json:"name,omitempty"`
	SuperFan  *livemedal.SuperFan `json:"super_fan,omitempty"`
	Level     int                 `json:"level,omitempty"`
	Color     string              `json:"color,omitempty"` // TODO: 使用 colors 代替，后面移除该字段
	Colors    string              `json:"colors,omitempty"`
	IconURL   string              `json:"icon_url,omitempty"`
	FrameURL  string              `json:"frame_url,omitempty"`
	NameColor string              `json:"name_color,omitempty"` // 粉丝勋章的名称颜色
	// AppearanceID 目前只有称号会使用该字段
	AppearanceID int64 `json:"appearance_id,omitempty"`
}

// Simple 包含了简单的用户信息，主要用于聊天室消息
// NOTICE: liveuser 下面的查找 Simple 的函数应该保证返回的结果可以直接用于聊天室消息
type Simple struct {
	UID       int64  `bson:"user_id" json:"user_id"`
	Username  string `bson:"username" json:"username"`
	NameColor string `bson:"name_color" json:"-"`
	IconURL   string `bson:"iconurl" json:"iconurl"`
	// TODO: 此处可以考虑改成指针 + omitempty
	Contribution int64 `bson:"contribution" json:"-"`

	// 勋章顺序：超管 > 主播/房管 > 贵族 > 粉丝勋章 > 直播等级
	Group       *string         `bson:"group,omitempty" json:"-"`
	VipInfo     *vip.UserInfo   `bson:"-" json:"-"`
	Medal       *livemedal.Mini `bson:"-" json:"-"` // 用户当前佩戴勋章
	IsSuperFan  bool            `bson:"-" json:"-"` // 用户是否对应直播间内的超粉
	AvatarFrame string          `bson:"-" json:"-"`

	Titles []Title `bson:"-" json:"titles,omitempty"`

	// Web 神话/上神推荐头像框
	RecommendFrameURL string `bson:"-" json:"recommend_frame_url,omitempty"`
	// App 神话/上神推荐头像框
	RecommendAvatarFrameURL string `bson:"-" json:"recommend_avatar_frame_url,omitempty"`

	// Vehicle 进场座驾
	Vehicle *userappearance.Vehicle `bson:"-" json:"vehicle,omitempty"`

	// CustomWelcomeMessage 自定义进场欢迎语（临时兼容用）
	CustomWelcomeMessage *usermeta.CustomWelcomeMessage `bson:"-" json:"custom_welcome_message,omitempty"`
	// EntryBubble 进场通知外观
	EntryBubble *userappearance.EntryBubble `bson:"-" json:"entry_bubble,omitempty"`

	UserVipMap        map[int]*vip.UserVip                     `bson:"-" json:"-"`
	UserAppearanceMap map[int][]*userappearance.UserAppearance `bson:"-" json:"-"` // map[外观类型][]外观
}

// UserInfo 用户基本信息
type UserInfo struct {
	Simple `bson:",inline" json:",inline"`

	Noble        *vip.UserNoble `bson:"-"  json:"noble,omitempty"`
	TrialNoble   *vip.UserNoble `bson:"-"  json:"trial_noble,omitempty"`
	Highness     *vip.UserNoble `bson:"-"  json:"highness,omitempty"`
	Confirm      uint           `bson:"confirm" json:"confirm"`
	Introduction string         `bson:"introduction" json:"introduction"`
}

// UserID 接口
func (u Simple) UserID() int64 {
	return u.UID
}

// IsRole 用户是否是某角色
func (u *Simple) IsRole(role string) (res bool) {
	if u.Group == nil {
		return false
	}
	switch role {
	case RoleStaff:
		res = *u.Group == RoleStaff || *u.Group == RoleAdmin
	case RoleAdmin:
		res = *u.Group == RoleAdmin
	}
	return
}

// MakeTitles 构建 Titles 用于聊天消息
// 勋章顺序：超管 > 主播/房管 > 贵族 > 粉丝勋章 > 直播等级
// 其他 titles 暂无顺序
func (u *Simple) MakeTitles() {
	u.Titles = make([]Title, 0, 6)
	if u.IsRole(RoleStaff) {
		u.Titles = append(u.Titles, StaffTitle())
	}
	if u.VipInfo != nil {
		// 体验贵族不存在或过期时，才显示普通贵族的信息
		if trialNoble := u.VipInfo.LiveTrialNoble; trialNoble != nil {
			u.Titles = append(u.Titles, NobleTitle(trialNoble))
		} else {
			if noble := u.VipInfo.LiveNoble; noble != nil {
				u.Titles = append(u.Titles, NobleTitle(noble))
			}
		}
		if highness := u.VipInfo.LiveHighness; highness != nil {
			u.Titles = append(u.Titles, HighnessTitle(highness))
		}
	}
	if u.Medal != nil {
		u.Titles = append(u.Titles, MedalTitle(u.Medal))
	}
	u.Titles = append(u.Titles, LevelTitle(u.Contribution))
	u.buildNameColor()
	u.findUserAppearances()
}

// 昵称颜色优先级: 活动定制 > 上神 > 超粉
func (u *Simple) buildNameColor() {
	if u.NameColor != "" {
		u.Titles = append(u.Titles, UsernameTitle(u.NameColor))
	} else if u.VipInfo != nil && u.VipInfo.LiveHighness != nil {
		title := UsernameTitle(u.VipInfo.LiveHighness.NameColors)
		if u.IsSuperFan {
			// WORKAROUND: 兼容旧版本上神用户且是超粉时的颜色展示
			title.Color = livemedal.SuperFanUsernameColor()
		}
		u.Titles = append(u.Titles, title)
	} else if u.IsSuperFan {
		u.Titles = append(u.Titles, UsernameTitle(livemedal.SuperFanUsernameColor()))
	}
}

// Level 用户等级
func (u *Simple) Level() int {
	for i := range u.Titles {
		if u.Titles[i].Type == TitleTypeLevel {
			return u.Titles[i].Level
		}
	}
	return 0
}

// StaffTitle 超管的 title
func StaffTitle() Title {
	return Title{Type: TitleTypeStaff, Name: "超管", Color: "#F45B41"}
}

// NobleTitle 普通贵族的 title
func NobleTitle(info *vip.Info) Title {
	return Title{Type: TitleTypeNoble, Name: info.Title, Level: info.Level}
}

// HighnessTitle 上神的 title
func HighnessTitle(info *vip.Info) Title {
	return Title{Type: TitleTypeHighness, Name: info.Title, Level: info.Level}
}

// MedalTitle 粉丝勋章的 title
func MedalTitle(m *livemedal.Mini) Title {
	return Title{
		Type:      TitleTypeMedal,
		Name:      m.Name,
		SuperFan:  m.SuperFan,
		Level:     m.Level,
		FrameURL:  m.FrameURL,
		NameColor: m.NameColor,
	}
}

// LevelTitle 等级的 title
func LevelTitle(contribution int64) (t Title) {
	t.Type = TitleTypeLevel
	t.Level = usercommon.Level(contribution)
	t.IconURL = usercommon.LevelIconURL(t.Level)
	return
}

// UsernameTitle 用户名颜色的 title
// TODO: 后续版本昵称颜色不返回 color 字段
func UsernameTitle(colors string) (t Title) {
	color := colors
	if strings.Contains(colors, ";") {
		// WORKAROUND: 兼容旧版本，iOS < 4.7.5, 安卓 < 5.6.3 color 下发单色
		color = strings.Split(colors, ";")[0]
	}
	return Title{Type: TitleTypeUsername, Color: color, Colors: colors}
}

// appearanceBadges 外观中心的称号 badges
func appearanceBadges(userAppearances []*userappearance.UserAppearance) []Title {
	titles := make([]Title, 0, len(userAppearances))
	for _, appearance := range userAppearances {
		titles = append(titles, Title{
			Type:         TitleTypeBadge,
			IconURL:      storage.ParseSchemeURL(appearance.Image),
			AppearanceID: appearance.AppearanceID,
		})
	}
	return titles
}

// appearanceIdentityBadges 外观中心的身份铭牌 identityBadges
func appearanceIdentityBadges(userAppearances []*userappearance.UserAppearance) []Title {
	titles := make([]Title, 0, len(userAppearances))
	for _, appearance := range userAppearances {
		titles = append(titles, Title{
			Type:         TitleTypeIdentityBadge,
			IconURL:      storage.ParseSchemeURL(appearance.Image),
			AppearanceID: appearance.AppearanceID,
		})
	}
	return titles
}

func (u *Simple) findUserAppearances() {
	if u.UserAppearanceMap == nil {
		var err error
		u.UserAppearanceMap, err = userappearance.FindUserAppearances(u.UserID())
		if err != nil {
			logger.WithField("user_id", u.UserID()).Errorf("failed to find user titles from user_appearances: %v", err)
			return
		}
	}
	if len(u.UserAppearanceMap) > 0 {
		userAppearances := u.UserAppearanceMap
		if avatarFrames := userAppearances[appearance.TypeAvatarFrame]; len(avatarFrames) > 0 {
			u.Titles = append(u.Titles, Title{Type: TitleTypeAvatarFrame, IconURL: storage.ParseSchemeURL(avatarFrames[0].Image)})
		}
		if badges := userAppearances[appearance.TypeBadge]; len(badges) > 0 {
			u.Titles = append(u.Titles, appearanceBadges(badges)...)
		}
		if identityBadges := userAppearances[appearance.TypeIdentityBadge]; len(identityBadges) > 0 {
			sort.Slice(identityBadges, func(i, j int) bool {
				// 优先展示黑卡身份铭牌
				if identityBadges[i].From == appearance.FromBlackCard {
					return true
				}
				if identityBadges[j].From == appearance.FromBlackCard {
					return false
				}
				return identityBadges[i].StartTime < identityBadges[j].StartTime
			})
			u.Titles = append(u.Titles, appearanceIdentityBadges(identityBadges)...)
		}
	}
}

// FindOptions find title options
type FindOptions struct {
	FindTitles bool
	FindVips   bool // 查询贵族信息，失败则会返回错误
	RoomID     int64

	UvMap map[int64]*vip.UserInfo
}

// FindOneSimple find one simple
func FindOneSimple(filter interface{}, opts *FindOptions, mongoOpt ...*options.FindOneOptions) (*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	opt := util.FindOneOptions(mongoOpt)
	if opt.Projection == nil {
		opt.SetProjection(ProjectionSimple)
	}

	res := new(Simple)
	err := Collection().FindOne(ctx, filter, opt).Decode(res)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	if opts == nil {
		return res, nil
	}
	if opts.FindTitles || opts.FindVips {
		err = res.findVips()
		if err != nil {
			if opts.FindVips {
				return nil, err
			}
			logger.Error(err)
			// PASS
		}
	}
	if opts.FindTitles {
		res.buildMedal(opts.RoomID)
		res.MakeTitles()
	}
	return res, nil
}

func (u *Simple) findVips() error {
	var err error
	u.UserVipMap, err = vip.UserVipInfos(u.UserID(), false, nil)
	if err != nil {
		return err
	}
	u.VipInfo = vip.NewUserVipInfo(u.UserVipMap)
	return nil
}

// ListSimples 查询 simples
func ListSimples(filter interface{}, opts *FindOptions, mongoOpt ...*options.FindOptions) ([]*Simple, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	opt := util.FindOptions(mongoOpt)
	if opt.Projection == nil {
		opt.SetProjection(ProjectionSimple)
	}

	cur, err := Collection().Find(ctx, filter, opt)
	if err != nil {
		return nil, err
	}
	var res []*Simple
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}

	if opts == nil || !opts.FindTitles {
		return res, nil
	}
	// 开始查询 titles
	userIDs := make([]int64, len(res))
	for i := 0; i < len(userIDs); i++ {
		userIDs[i] = res[i].UID
	}
	// 查找贵族
	if opts.UvMap == nil {
		opts.UvMap, err = vip.MapUsersInfo(userIDs, nil, nil)
		if err != nil {
			opts.UvMap = make(map[int64]*vip.UserInfo)
			logger.Error(err)
			// PASS
		}
	}
	// 查找 medals
	medals, err := livemedal.FindShowingMiniMap(userIDs)
	if err != nil {
		medals = make(map[int64]*livemedal.Mini)
		logger.Error(err)
		// PASS
	}
	// 查找 super medals
	var superMedals map[int64]struct{}
	if opts.RoomID > 0 {
		superMedals, err = livemedal.FindRoomSuperFanMap(opts.RoomID, userIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	// 查找外观
	usersAppearancesMap, err := userappearance.FindUsersAppearancesMap(userIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	for i := 0; i < len(userIDs); i++ {
		res[i].VipInfo = opts.UvMap[userIDs[i]]
		res[i].Medal = medals[userIDs[i]]
		if superMedals != nil {
			_, res[i].IsSuperFan = superMedals[userIDs[i]]
		}
		res[i].UserAppearanceMap = usersAppearancesMap[userIDs[i]]
		res[i].MakeTitles()
	}

	return res, nil
}

// UserTitlesMap 通过 userIDs 查询 titles
func UserTitlesMap(userIDs []int64, roomID int64, mNoble map[int64]*vip.UserInfo) (map[int64][]Title /* map[userID][]Title*/, error) {
	l, err := ListSimples(bson.M{"user_id": bson.M{"$in": util.Uniq(userIDs)}},
		&FindOptions{
			FindTitles: true,
			RoomID:     roomID,
			UvMap:      mNoble,
		}, options.Find().SetProjection(ProjectionTitles))
	if err != nil {
		return nil, err
	}
	res := make(map[int64][]Title, len(l))
	for i := 0; i < len(l); i++ {
		res[l[i].UID] = l[i].Titles
	}
	return res, nil
}

// SimpleSliceToMap []*Simple -> map[userID]*Simple
// NOTICE: 入参保证不会出现两个同样用户 ID 的
// TODO: 后续用 util 的 slice 转 map 函数代替
func SimpleSliceToMap(l []*Simple, err ...error) (map[int64]*Simple /* map[userID]*Simple */, error) {
	var returnErr error
	if len(err) != 0 {
		returnErr = err[0]
	}

	m := make(map[int64]*Simple, len(l))
	for i := 0; i < len(l); i++ {
		if u := l[i]; u != nil {
			m[u.UID] = u
		}
	}
	return m, returnErr
}

// FindUserInfo 获取用户基本信息
// roomID 参数不为 0 时，会查询用户对该直播间的特殊 title
func FindUserInfo(userID int64, roomID int64) (*UserInfo, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	u := new(UserInfo)
	err := collection.FindOne(ctx, bson.M{"user_id": userID}).Decode(u)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			err = nil
		}
		return nil, err
	}
	err = u.findVips()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	u.buildMedal(roomID)
	u.MakeTitles()

	return u, nil
}

// 查询用户当前佩戴勋章和指定直播间超粉状态（用于超粉彩色昵称）
func (u *Simple) buildMedal(roomID int64) {
	filter := bson.M{"user_id": u.UserID(), "status": livemedal.StatusShow}
	if roomID > 0 {
		filter = bson.M{
			"user_id": u.UserID(),
			"$or": []bson.M{
				{"status": livemedal.StatusShow},
				{"room_id": roomID, "super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()}},
			},
		}
	}
	lms, err := livemedal.List(filter, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	for i := range lms {
		if lms[i].Status == livemedal.StatusShow {
			u.Medal = &lms[i].Mini
		}
		if lms[i].RoomID == roomID && livemedal.IsSuperFanActive(lms[i].SuperFan) {
			u.IsSuperFan = true
		}
	}
}

// ListOptions 列表查询 option
type ListOptions struct {
	Filter interface{}

	MongoOpt *options.FindOptions

	FindTitles bool
	RoomID     int64

	UserVipMap map[int64]*vip.UserInfo

	FindEntryAppearances bool // FindVehicle and FindEntryBubble

	usersAppearancesMap map[int64]map[int][]*userappearance.UserAppearance
}

// ListSimpleByOpts 通过 ListOptions 查询 simple
func ListSimpleByOpts(opts *ListOptions) ([]*Simple, error) {
	if opts.Filter == nil {
		panic("Filter is nil.")
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Find(ctx, opts.Filter, opts.MongoOpt)
	if err != nil {
		return nil, err
	}
	res := []*Simple{}
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}

	userIDs := make([]int64, len(res))
	for i := 0; i < len(userIDs); i++ {
		userIDs[i] = res[i].UID
	}
	if opts.FindTitles || opts.FindEntryAppearances {
		// 查找外观
		opts.usersAppearancesMap, err = userappearance.FindUsersAppearancesMap(userIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	// 查询 titles
	if opts.FindTitles {
		// 查找贵族
		if opts.UserVipMap == nil {
			opts.UserVipMap, err = vip.MapUsersInfo(userIDs, nil, nil)
			if err != nil {
				opts.UserVipMap = make(map[int64]*vip.UserInfo)
				logger.Error(err)
				// PASS
			}
		}
		// 查找 medals
		medals, err := livemedal.FindShowingMiniMap(userIDs)
		if err != nil {
			medals = make(map[int64]*livemedal.Mini)
			logger.Error(err)
			// PASS
		}
		// 查找 super medals
		var superMedals map[int64]struct{}
		if opts.RoomID > 0 {
			superMedals, err = livemedal.FindRoomSuperFanMap(opts.RoomID, userIDs)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}

		for i := 0; i < len(userIDs); i++ {
			res[i].VipInfo = opts.UserVipMap[userIDs[i]]
			res[i].Medal = medals[userIDs[i]]
			if superMedals != nil {
				_, res[i].IsSuperFan = superMedals[userIDs[i]]
			}
			res[i].UserAppearanceMap = opts.usersAppearancesMap[userIDs[i]]
			res[i].MakeTitles()
		}
	}

	if opts.FindEntryAppearances {
		assignEntryAppearances(res, opts.usersAppearancesMap)
	}

	return res, nil
}

// AssignEntryAppearances 为用户添加使用的座驾和进场气泡信息
func AssignEntryAppearances(s *Simple) {
	userAppearancesMap, err := userappearance.FindUserAppearances(s.UserID())
	if err != nil {
		logger.Error(err)
		return
	}
	assignEntryAppearances([]*Simple{s}, map[int64]map[int][]*userappearance.UserAppearance{
		s.UserID(): userAppearancesMap})
}

// AssignCustomWelcomeMessage 为用户添加生效中的自定义进场欢迎语
func AssignCustomWelcomeMessage(s *Simple) {
	userID := s.UserID()
	msg, err := usermeta.FindUserValidCustomWelcomeMessage(userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if msg == nil || msg.Text == "" {
		// 没有生效中的自定义欢迎语
		return
	}
	if s.EntryBubble != nil && s.EntryBubble.WelcomeMessage != nil {
		// 有进场通知且自定义欢迎语不为空时替换
		s.EntryBubble.WelcomeMessage.Text = msg.Text
	} else {
		// 如果没有进场通知，当前就只给 CustomWelcomeMessage 赋值（临时兼容用）
		s.CustomWelcomeMessage = msg
	}
}

func assignEntryAppearances(simples []*Simple, usersAppearancesMap map[int64]map[int][]*userappearance.UserAppearance) {
	if len(simples) == 0 || len(usersAppearancesMap) == 0 {
		return
	}
	userIDs := make([]int64, len(simples))
	for i := range simples {
		userIDs[i] = simples[i].UserID()
	}

	for _, s := range simples {
		v := usersAppearancesMap[s.UserID()]
		if uas := v[appearance.TypeVehicle]; len(uas) > 0 {
			s.Vehicle = userappearance.NewVehicle(uas[0])
			s.Vehicle.FormatMessage(s.Username)
		}
		if uas := v[appearance.TypeEntryBubble]; len(uas) > 0 {
			s.EntryBubble = userappearance.NewEntryBubble(uas[0])
		}
	}
}
