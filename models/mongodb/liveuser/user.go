package liveuser

import (
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// role
const (
	RoleStaff = "staff"
	RoleAdmin = "admin"
)

// IUser interface
type IUser interface {
	UserID() int64
}

// CollectionName collection name without prefix
const CollectionName = "users"

// User document in collection users
type User struct {
	OID primitive.ObjectID `bson:"_id,omitempty" json:"-"`

	UID          int64  `bson:"user_id" json:"user_id"`
	AccID        string `bson:"accid" json:"accid"`
	Username     string `bson:"username" json:"username"`
	NameColor    string `bson:"name_color" json:"-"`
	Token        string `bson:"token" json:"-"`
	Avatar       string `bson:"avatar" json:"-"`
	IconURL      string `bson:"iconurl" json:"iconurl"`
	Confirm      int32  `bson:"confirm" json:"confirm"`
	Introduction string `bson:"introduction" json:"introduction"`
	Contribution int64  `bson:"contribution" json:"-"`

	CreatedTime time.Time `bson:"created_time" json:"-"`
	UpdatedTime time.Time `bson:"updated_time" json:"-"`
	Point       float64   `bson:"point" json:"-"`
	Group       *string   `bson:"group" json:"-"`

	Online bool `bson:"-" json:"online"`

	VipInfo *vip.UserInfo   `bson:"-" json:"-"`
	Medal   *livemedal.Mini `bson:"-" json:"-"`
	Titles  []Title         `bson:"-" json:"titles"`
}

// UserID IUserID interface
func (u User) UserID() int64 {
	return u.UID
}

// Collection mongo collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// Find 查询用户
func Find(userID int64) (*User, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	u := new(User)
	err := collection.FindOne(ctx, bson.M{"user_id": userID}).Decode(u)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			err = nil
		}
		return nil, err
	}
	return u, nil
}

// Exists 判断用户是否存在
func Exists(userID int64) (bool, error) {
	opt := options.FindOne().SetReturnKey(true)
	s, err := FindOneSimple(bson.M{"user_id": userID}, nil, opt)
	return s != nil, err
}

// IsStaff 是否超管
func IsStaff(u IUser) (bool, *string, error) {
	if u.UserID() == 0 {
		return false, nil, nil
	}
	opt := options.FindOne().SetProjection(map[string]int{"group": 1})
	s, err := FindOneSimple(bson.M{"user_id": u.UserID()}, nil, opt)
	if err != nil || s == nil {
		return false, nil, err
	}
	return s.IsRole(RoleStaff), s.Group, nil
}

// IsProfitAdmin 是否超管（查看收益）
func IsProfitAdmin(u IUser) (bool, error) {
	_, group, err := IsStaff(u)
	if err != nil {
		return false, nil
	}
	if group != nil && *group == RoleAdmin {
		return true, nil
	}
	return false, nil
}

// MakeTitles 生成 Titles
// TODO: 和 Simple 那边的共用代码
func (u *User) MakeTitles(opts *FindOptions) {
	uvs, err := vip.UserVipInfos(u.UserID(), false, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	} else if uvs != nil {
		u.VipInfo = vip.NewUserVipInfo(uvs)
	}
	lm, err := livemedal.FindOne(bson.M{"user_id": u.UserID(), "status": livemedal.StatusShow}, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	s := &Simple{
		UID:          u.UserID(),
		NameColor:    u.NameColor,
		Contribution: u.Contribution,
		Group:        u.Group,
		VipInfo:      u.VipInfo,
	}
	if lm != nil {
		s.Medal = &lm.Mini
	}
	if opts != nil && opts.RoomID != 0 {
		if lm != nil && lm.RoomID == opts.RoomID {
			// 佩戴中的的勋章为待查询直播间勋章时，可直接读取超粉状态
			s.IsSuperFan = livemedal.IsSuperFanActive(lm.SuperFan)
		} else {
			// 查询获取用户直播间超粉身份
			if s.IsSuperFan, err = livemedal.IsRoomSuperFan(opts.RoomID, u.UserID()); err != nil {
				logger.Error(err)
				// PASS
			}
		}
	}
	s.MakeTitles()
	u.Titles = s.Titles
}

// Update 更新用户信息，找不到用户返回 false
func Update(userID int64, update bson.M) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	update["updated_time"] = goutil.TimeNow()
	err := collection.FindOneAndUpdate(ctx, bson.M{"user_id": userID}, bson.M{"$set": update},
		options.FindOneAndUpdate()).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return false, err
	}
	return true, nil
}

func deleteUserInfoCache(userIDs ...int64) {
	keyList := make([]string, len(userIDs))
	for i := 0; i < len(userIDs); i++ {
		keyList[i] = keys.KeyUsersInfo1.Format(userIDs[i])
	}
	if err := service.Redis.Del(keyList...).Err(); err != nil {
		logger.Errorf("redis del error: %v", err)
		// PASS
	}
}

// AddConsumptionByLevelGte85InCurrentMonth 统计当月用户等级大于等于 85 的消费金额
func AddConsumptionByLevelGte85InCurrentMonth(when time.Time, userID, spend int64) (int64, error) {
	pipeliner := service.Redis.TxPipeline()
	key := keys.KeyUsersLevelGte85Consumption1.Format(when.Format(util.TimeFormatYMWithNoSpace))
	cmd := pipeliner.ZIncrBy(key, float64(spend), strconv.FormatInt(userID, 10))
	pipeliner.Expire(key, 45*24*time.Hour) // 每个自然月统计一次，这里多设置 15 天的过期时间方便后续查看数据
	_, err := pipeliner.Exec()
	if err != nil {
		return 0, err
	}
	return int64(cmd.Val()), nil
}

// ConsumptionByLevelGte85InCurrentMonth 获取当月用户等级大于等于 85 的消费金额
func ConsumptionByLevelGte85InCurrentMonth(when time.Time, userID int64) (int64, error) {
	key := keys.KeyUsersLevelGte85Consumption1.Format(when.Format(util.TimeFormatYMWithNoSpace))
	spend, err := service.Redis.ZScore(key, strconv.FormatInt(userID, 10)).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	return int64(spend), nil
}
