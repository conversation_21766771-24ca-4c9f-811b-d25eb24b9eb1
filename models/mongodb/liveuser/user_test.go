package liveuser

import (
	"encoding/json"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const ExistsUserID = int64(3387502)
const ExistsUsername = "夜柃"

func TestMain(m *testing.M) {
	handler.SetMode(handler.TestMode)
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
	clearTestData()
}

func clearTestData() {
	service.DB.Table(messageassign.TableName()).Delete("",
		"title = ?", "直播用户等级升级！新特权解锁！")
}

func TestKeys(t *testing.T) {
	var (
		user   User
		simple Simple
		title  Title
	)

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.CheckOmitEmpty(ProjectionSimple, "user_id",
		"username", "name_color", "iconurl", "group", "contribution")
	kc.CheckOmitEmpty(ProjectionTitles, "user_id", "name_color", "group", "contribution")
	kc.Check(user, "_id", "user_id", "accid", "username", "name_color", "token",
		"avatar", "iconurl", "confirm", "introduction", "contribution", "created_time",
		"updated_time", "point", "group")
	kc.Check(simple, "user_id", "username", "name_color", "iconurl", "contribution", "group")
	kc.Check(LiveUser{}, "_id", "user_id", "_room_id", "room_id", "t_time_online", "t_acq_online", "acq_online",
		"t_status_share", "t_time_share")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(user, "online", "titles", "user_id", "accid", "username",
		"iconurl", "confirm", "introduction")
	kc.Check(simple, "user_id", "username", "iconurl", "titles",
		"recommend_frame_url", "recommend_avatar_frame_url", "vehicle", "custom_welcome_message", "entry_bubble")
	kc.Check(title, "type", "name", "super_fan", "level", "color", "colors", "icon_url",
		"frame_url", "name_color", "appearance_id")
}

func TestIuser(t *testing.T) {
	assert := assert.New(t)
	u := IUser(&User{UID: 12})
	assert.Equal(int64(12), u.UserID())
	u = IUser(User{UID: 12})
	assert.Equal(int64(12), u.UserID())
	u = IUser(&Simple{UID: 12})
	assert.Equal(int64(12), u.UserID())
	u = IUser(Simple{UID: 12})
	assert.Equal(int64(12), u.UserID())
	u = IUser(handler.CreateTestContext(true))
	assert.Equal(int64(12), u.UserID())
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	u, err := Find(1234)
	assert.Nil(err)
	assert.Nil(u)
	u, err = Find(ExistsUserID)
	assert.NoError(err)
	require.NotNil(t, u)
	assert.Equal(ExistsUsername, u.Username)
}

func TestExists(t *testing.T) {
	assert := assert.New(t)

	b, err := Exists(ExistsUserID)
	assert.NoError(err)
	assert.True(b)
}

func TestIsStaffAndProfitAdmin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.CreateTestContext(true)
	access, group, err := IsStaff(c)
	assert.NoError(err)
	assert.True(access)
	require.NotNil(group)
	assert.True(*group == "staff" || *group == "admin", *group)
	access, err = IsProfitAdmin(c)
	assert.NoError(err)
	assert.True(access)

	c.User().ID = 789
	access, _, err = IsStaff(c)
	require.NoError(err)
	assert.False(access)
	access, _, _ = IsStaff(handler.CreateTestContext(false))
	assert.False(access)
}

func TestUserMakeTitles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u := &User{
		UID:          3456835,
		Contribution: 1000,
	}
	m := map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			UserID:     u.UID,
			VipID:      1,
			Level:      1,
			ExpireTime: goutil.TimeNow().Add(2 * time.Hour).Unix(),
			Info:       &vip.Info{},
		},
	}
	mJSON, err := json.Marshal(m)
	require.NoError(err)
	key := keys.KeyNobleUserVips1.Format(u.UID)
	service.Redis.Set(key, mJSON, 2*time.Second)

	u.MakeTitles(nil)
	titleTypes := make([]string, len(u.Titles))
	for i := 0; i < len(u.Titles); i++ {
		titleTypes[i] = u.Titles[i].Type
	}
	assert.Contains(titleTypes, "noble")
}

func TestUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	intro := strconv.FormatInt(now.Unix(), 10)
	update := bson.M{"introduction": intro}

	ok, err := Update(ExistsUserID, update)
	require.NoError(err)
	assert.True(ok)
	r, err := Find(ExistsUserID)
	require.NoError(err)
	assert.Equal(intro, r.Introduction)
	assert.WithinDuration(now, r.UpdatedTime, 5*time.Second)

	ok, err = Update(1234567890, update)
	require.NoError(err)
	assert.False(ok)
}

func TestDeleteUserInfoCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs := []int64{123456, 12345}
	pipe := service.Redis.Pipeline()
	for _, id := range userIDs {
		key := keys.KeyUsersInfo1.Format(id)
		pipe.Set(key, "111", time.Minute)
	}
	_, err := pipe.Exec()
	require.NoError(err)

	deleteUserInfoCache([]int64{123456, 12345}...)
	assert.Zero(service.Redis.Exists([]string{keys.KeyUsersInfo1.Format(userIDs[0]), keys.KeyUsersInfo1.Format(userIDs[1])}...).Val())
}

func TestAddConsumptionByLevelGte85InCurrentMonth(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	var (
		testUserID = int64(123456)
		now        = goutil.TimeNow()
	)

	err := service.Redis.Del(
		keys.KeyUsersLevelGte85Consumption1.Format(now.Format("200601")),
	).Err()
	require.NoError(err)

	// 为当月添加
	c, err := AddConsumptionByLevelGte85InCurrentMonth(now, testUserID, 100)
	require.NoError(err)
	assert.EqualValues(100, c)

	c, err = AddConsumptionByLevelGte85InCurrentMonth(now, testUserID, 100)
	require.NoError(err)
	assert.EqualValues(200, c)
}

func TestConsumptionByLevelGte85InCurrentMonth(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	var (
		testUserID = int64(123456)
		now        = goutil.TimeNow()
	)

	err := service.Redis.Del(
		keys.KeyUsersLevelGte85Consumption1.Format(now.Format("200601")),
	).Err()
	require.NoError(err)

	// 为当月添加
	s, err := AddConsumptionByLevelGte85InCurrentMonth(now, testUserID, 100)
	require.NoError(err)
	spend, err := ConsumptionByLevelGte85InCurrentMonth(now, testUserID)
	require.NoError(err)
	assert.Equal(s, spend)
}
