package liveuser

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSimpleMakeTitles(t *testing.T) {
	assert := assert.New(t)

	u := &Simple{
		UID:       987654321,
		NameColor: "1234",
		Group:     new(string),
		VipInfo: &vip.UserInfo{
			LiveNoble:    &vip.Info{Type: vip.TypeLiveNoble, Level: 3, Title: "ssr"},
			LiveHighness: &vip.Info{Type: vip.TypeLiveHighness, Level: 1, Title: "god"},
		},
		Medal:        &livemedal.Mini{Name: "medal", Level: 100},
		Contribution: 0,
	}
	*u.Group = "admin"
	u.MakeTitles()
	assert.Equal([]Title{
		StaffTitle(),
		{Type: "noble", Name: "ssr", Level: 3},
		{Type: "highness", Name: "god", Level: 1},
		{Type: "medal", Name: "medal", Level: 100},
		{Type: "level", Level: 1},
		{Type: "username", Color: "1234", Colors: "1234"},
	}, u.Titles)

	// 测试有体验贵族的情况
	u = &Simple{
		UID:       987654321,
		NameColor: "1234",
		Group:     new(string),
		VipInfo: &vip.UserInfo{
			LiveNoble:      &vip.Info{Type: vip.TypeLiveNoble, Level: 3, Title: "ssr"},
			LiveHighness:   &vip.Info{Type: vip.TypeLiveHighness, Level: 1, Title: "god"},
			LiveTrialNoble: &vip.Info{Type: vip.TypeLiveTrialNoble, Level: 5, Title: "dad"},
		},
		Medal:        &livemedal.Mini{Name: "medal", Level: 100},
		Contribution: 0,
	}
	*u.Group = "admin"
	u.MakeTitles()
	assert.Equal([]Title{
		StaffTitle(),
		{Type: "noble", Name: "dad", Level: 5},
		{Type: "highness", Name: "god", Level: 1},
		{Type: "medal", Name: "medal", Level: 100},
		{Type: "level", Level: 1},
		{Type: "username", Color: "1234", Colors: "1234"},
	}, u.Titles)
}

func TestSimpleIsRole(t *testing.T) {
	assert := assert.New(t)

	u := Simple{UID: 12}
	assert.False(u.IsRole(RoleAdmin))
	u.Group = new(string)
	*u.Group = "admin"
	assert.True(u.IsRole(RoleStaff))
	assert.True(u.IsRole(RoleAdmin))
	*u.Group = "staff"
	assert.True(u.IsRole(RoleStaff))
	assert.False(u.IsRole(RoleAdmin))
	assert.False(u.IsRole("123"))
}

func TestMakeLevelTitle(t *testing.T) {
	assert := assert.New(t)

	expected := func(level int) Title {
		return Title{
			Level:   level,
			Type:    TitleTypeLevel,
			IconURL: usercommon.LevelIconURL(level),
		}
	}

	assert.Equal(expected(1), LevelTitle(-10))
	assert.Equal(expected(20), LevelTitle(usercommon.LevelStart[19]))
	assert.Equal(expected(30), LevelTitle(usercommon.LevelStart[29]))
	assert.Equal(expected(166), LevelTitle(usercommon.LevelStart[149]*2))
}

func TestUsernameTitle(t *testing.T) {
	assert := assert.New(t)

	colors := "#FFFFFF"
	assert.Equal(Title{Type: TitleTypeUsername, Color: colors, Colors: colors}, UsernameTitle(colors))

	colors = "#FFFFFF;#000000"
	assert.Equal(Title{Type: TitleTypeUsername, Color: "#FFFFFF", Colors: colors}, UsernameTitle(colors))
}

func TestFindSimple(t *testing.T) {
	_, err := livemedal.Wear(12, 3456837)
	require.NoError(t, err)

	t.Run("TestFindOneSimple", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		testUserID := int64(12)
		key := keys.KeyNobleUserVips1.Format(testUserID)
		defer func() {
			assert.NoError(service.Redis.Del(key).Err())
		}()
		require.NoError(service.Redis.Set(key, `{"1":{"user_id":12,"type":1,"level":1}}`, 30*time.Second).Err())
		filter := bson.M{"user_id": 12}
		s, err := FindOneSimple(filter, &FindOptions{FindTitles: true})
		require.NoError(err)
		require.NotNil(s)
		assert.Equal(int64(12), s.UserID())
		assert.NotNil(s.Medal)

		s, err = FindOneSimple(filter, &FindOptions{FindVips: true})
		require.NoError(err)
		require.NotNil(s)
		assert.EqualValues(12, s.UserID())
		assert.NotEmpty(s.UserVipMap)

		opt := options.FindOne().SetProjection(ProjectionTitles)
		s, err = FindOneSimple(filter, nil, opt)
		require.NoError(err)
		require.NotNil(s)
		assert.NotZero(s.UserID())
		assert.Emptyf(s.Username+s.IconURL, "username: %s, iconurl: %s", s.Username, s.IconURL)
		assert.Nil(s.Medal)

		filter["user_id"] = "123"
		s, err = FindOneSimple(filter, nil)
		require.NoError(err)
		assert.Nil(s)
	})
	t.Run("TestListSimples", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		filter := bson.M{"user_id": bson.M{"$in": []int64{12, 3456835}}}
		mNoble := map[int64]*vip.UserInfo{
			12: {LiveNoble: &vip.Info{Type: vip.TypeLiveNoble, Level: 1}},
		}
		l, err := ListSimples(filter, &FindOptions{UvMap: mNoble}, options.Find().SetSort(bson.M{"user_id": 1}))
		require.NoError(err)
		require.Len(l, 2)
		assert.Equal(int64(12), l[0].UserID())
		assert.Nil(l[0].Medal)
		assert.Nil(l[0].VipInfo) // findTitles 为 false 导致了 mNoble 未使用
		assert.Nil(l[1].VipInfo)
		assert.Len(append(l[0].Titles, l[1].Titles...), 0)

		opt := options.Find().SetProjection(ProjectionTitles)
		l, err = ListSimples(filter, &FindOptions{FindTitles: true, UvMap: mNoble}, opt)
		require.NoError(err)
		require.Len(l, 2)
		assert.NotZero(l[0].UserID())
		assert.NotZero(l[0].Medal)
		assert.Emptyf(l[0].Username+l[0].IconURL, "username: %s, iconurl: %s", l[0].Username, l[0].IconURL)
		assert.NotNil(l[0].Medal)
		assert.NotNil(l[0].VipInfo)
		assert.NotNil(l[0].Titles)
		assert.Equal("medal", l[0].Titles[2].Type)
		assert.Equal(fmt.Sprintf("https://static-test.missevan.com/live/medalframes/3f12/level%02d_0_9_0_54.png", l[0].Titles[2].Level), l[0].Titles[2].FrameURL)
		assert.Nil(l[1].VipInfo)

		noble1 := tutil.SprintJSON(make(map[int]int))
		noble2 := tutil.SprintJSON(map[int]int{1: 1})
		key1 := keys.KeyNobleUserVipLevel1.Format(12)
		key2 := keys.KeyNobleUserVipLevel1.Format(3456835)
		pipe := service.Redis.Pipeline()
		pipe.Set(key1, noble1, 10*time.Second)
		pipe.Set(key2, noble2, 10*time.Second)
		_, err = pipe.Exec()
		require.NoError(err)
		l, err = ListSimples(filter, &FindOptions{FindTitles: true}, opt) // 测试不传 mNoble
		require.NoError(err)
		require.Len(l, 2)
		assert.NotZero(l[0].UserID())
		assert.NotZero(l[0].Medal)
		assert.Emptyf(l[0].Username+l[0].IconURL, "username: %s, iconurl: %s", l[0].Username, l[0].IconURL)
		assert.NotNil(l[0].Medal)
		assert.Nil(l[0].VipInfo)
		assert.NotNil(l[1].VipInfo)
	})
}

func TestSimpleFindVips(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	keyUser1 := keys.KeyNobleUserVips1.Format(3456835)
	keyUser2 := keys.KeyNobleUserVips1.Format(9876543)
	pipe := service.Redis.Pipeline()
	pipe.Set(keyUser1, `{"1":{"user_id":3456835,"type":1,"level":1,"expire_time":99999999999}}`, 30*time.Second)
	pipe.Set(keyUser2, `null`, 30*time.Second)
	_, err := pipe.Exec()
	require.NoError(err)
	defer func() {
		assert.NoError(service.Redis.Del(keyUser1, keyUser2).Err())
	}()

	u := Simple{UID: 3456835}
	err = u.findVips()
	require.NoError(err)
	assert.NotNil(u.VipInfo)

	u.UID = 9876543
	err = u.findVips()
	require.NoError(err)
	require.NotNil(u.VipInfo)
	assert.Nil(u.VipInfo.LiveHighness)
	assert.Nil(u.VipInfo.LiveNoble)
}

func TestUserTitlesMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mapTitles, err := UserTitlesMap([]int64{10, 12}, 0, nil)
	require.NoError(err)
	assert.NotEmpty(mapTitles[10])
	assert.NotEmpty(mapTitles[12])
}

func TestSimpleSliceToMap(t *testing.T) {
	assert := assert.New(t)

	testErr := errors.New("test error")
	l := []*Simple{nil, {UID: 10}}
	m, err := SimpleSliceToMap(l, testErr)
	assert.Equal(testErr, err)
	assert.Len(m, 1)
	assert.NotNil(m[10])
}

func TestFindAppearanceTitles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	u := new(Simple)
	u.UID = 2010
	userappearance.ClearCache(u.UserID())
	mUserTitles, err := userappearance.FindUserAppearances(u.UserID())
	require.NoError(err)
	require.NotNil(mUserTitles)
	require.Equal(2, len(mUserTitles))

	u.findUserAppearances()
	require.NotEmpty(u.Titles)
	require.Len(u.Titles, len(mUserTitles))

	// 头像框
	assert.Equal(TitleTypeAvatarFrame, u.Titles[0].Type)
	avatars, ok := mUserTitles[appearance.TypeAvatarFrame]
	require.True(ok)
	require.NotEmpty(avatars)
	assert.Equal(storage.ParseSchemeURL(avatars[0].Image), u.Titles[0].IconURL)

	// 称号
	assert.NotZero(u.Titles[1].AppearanceID)
	titles, ok := mUserTitles[appearance.TypeBadge]
	require.True(ok)
	require.NotEmpty(titles)
	assert.Equal(titles[0].AppearanceID, u.Titles[1].AppearanceID)
	assert.Equal(TitleTypeBadge, u.Titles[1].Type)
	assert.Equal(storage.ParseSchemeURL(titles[0].Image), u.Titles[1].IconURL)

	uAppearanceMap := make(map[int][]*userappearance.UserAppearance)
	uAppearanceMap[appearance.TypeAvatarFrame] = []*userappearance.UserAppearance{
		&userappearance.UserAppearance{
			AppearanceID: 1,
		},
	}
	u = new(Simple)
	u.UID = 2010
	userappearance.ClearCache(u.UserID())
	u.UserAppearanceMap = uAppearanceMap
	u.findUserAppearances()
	require.NotEmpty(u.Titles)
	require.Len(u.Titles, 1)
	assert.Equal(TitleTypeAvatarFrame, u.Titles[0].Type)

	u = new(Simple)
	u.UID = 2001
	userappearance.ClearCache(u.UserID())
	u.findUserAppearances()
	require.Empty(u.Titles)
}

func TestListSimplesByOpts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": 1233} // 此用户有进场气泡

	opt := &ListOptions{
		FindTitles:           true,
		FindEntryAppearances: true,
	}
	assert.PanicsWithValue("Filter is nil.", func() {
		_, _ = ListSimpleByOpts(opt)
	})

	opt.Filter = filter
	res, err := ListSimpleByOpts(opt)
	require.NoError(err)
	require.Len(res, 1)
	assert.NotNil(res[0].EntryBubble)
	assert.NotEmpty(res[0].Titles)
}

func TestAssignEntryAppearances(t *testing.T) {
	assert := assert.New(t)

	testUser := &Simple{
		UID: 1233, // 此用户有进场气泡
	}
	AssignEntryAppearances(testUser)
	assert.NotNil(testUser.EntryBubble)

	testUser = &Simple{
		UID: 1233,
	}
	testUser2 := &Simple{
		UID: -1233,
	}
	testUaMap := make(map[int64]map[int][]*userappearance.UserAppearance, 2)
	testUaMap[testUser2.UID] = map[int][]*userappearance.UserAppearance{
		appearance.TypeVehicle: []*userappearance.UserAppearance{
			&userappearance.UserAppearance{Type: appearance.TypeVehicle,
				MessageBar: &appearance.MessageBar{}}},
		appearance.TypeEntryBubble: []*userappearance.UserAppearance{
			&userappearance.UserAppearance{Type: appearance.TypeEntryBubble}},
	}

	assignEntryAppearances([]*Simple{testUser, testUser2}, testUaMap)
	assert.Nil(testUser.Vehicle)
	assert.Nil(testUser.EntryBubble)
	assert.NotNil(testUser2.Vehicle)
	assert.NotNil(testUser2.EntryBubble)
}

func TestAssignCustomWelcomeMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s := Simple{
		UID: 1919810,
		EntryBubble: &userappearance.EntryBubble{
			WelcomeMessage: &appearance.WelcomeMessage{},
		},
	}
	welcome := usermeta.CustomWelcomeMessage{
		Text: "test",
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": s.UserID()})
	require.NoError(err)
	_, err = usermeta.Collection().InsertOne(ctx, bson.M{
		"user_id":                s.UserID(),
		"custom_welcome_message": &welcome,
	})
	require.NoError(err)
	AssignCustomWelcomeMessage(&s)
	welcome.ExpireTime = goutil.TimeNow().Add(time.Minute).Unix()
	_, err = usermeta.Collection().UpdateOne(ctx, bson.M{"user_id": s.UserID()}, bson.M{"$set": bson.M{
		"custom_welcome_message": &welcome,
	}})
	require.NoError(err)
	AssignCustomWelcomeMessage(&s)
	assert.Equal(welcome.Text, s.EntryBubble.WelcomeMessage.Text)
	s.EntryBubble = nil
	AssignCustomWelcomeMessage(&s)
	require.NotNil(s.CustomWelcomeMessage)
	assert.Equal(welcome.Text, s.CustomWelcomeMessage.Text)
}
