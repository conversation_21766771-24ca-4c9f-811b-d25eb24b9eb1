package liveuser

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// LiveUser live_users 表结构体
type LiveUser struct {
	OID     primitive.ObjectID `bson:"_id,omitempty"`
	UserID  int64              `bson:"user_id"`
	RoomOID primitive.ObjectID `bson:"_room_id"`
	RoomID  int64              `bson:"room_id"`

	TodayTimeOnline time.Time `bson:"t_time_online"`
	TodayAcqOnline  int64     `bson:"t_acq_online"` // 单位：毫秒
	AcqOnline       int64     `bson:"acq_online"`   // 观看总时长，单位：毫秒

	StatusShare int       `bson:"t_status_share"`
	TimeShare   time.Time `bson:"t_time_share"`
}

// LiveUsersCollection live_users
func LiveUsersCollection() *mongo.Collection {
	return service.MongoDB.Collection("live_users")
}

// FindOneLiveUser find one live user status
func FindOneLiveUser(filter interface{}) (*LiveUser, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var lu LiveUser
	err := LiveUsersCollection().FindOne(ctx, filter).Decode(&lu)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &lu, nil
}
