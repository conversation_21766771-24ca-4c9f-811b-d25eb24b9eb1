package appearance

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Appearance{}, "_id", "id", "name", "type", "from", "intro",
		"icon", "effect", "web_effect", "effect_duration",
		"image", "image_new", "frame", "text_color", "text_color_item", "message_bar", "welcome_message", "entry_style", "resource",
		"start_time", "expire_time", "modified_time")
	kc.Check(MessageBar{}, "message", "normal_color", "highlight_color", "image")
	kc.Check(WelcomeMessage{}, "text", "colors", "shine")
	kc.Check(TextColorItem{}, "username", "introduction", "report_and_manage")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MessageBar{}, "message", "image", "normal_color", "highlight_color", "image_url")
	kc.Check(WelcomeMessage{}, "text", "colors", "shine")
	kc.Check(TextColorItem{}, "username", "introduction", "report_and_manage")
}

func TestVipAppearanceID(t *testing.T) {
	assert := assert.New(t)

	id := VipAppearanceID(typeLiveNoble, 1, TypeAvatarFrame)
	assert.Equal(int64(TypeAvatarFrame*10+1), id)

	id = VipAppearanceID(typeLiveHighness, 1, TypeAvatarFrame)
	assert.Equal(int64(92), id)
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(3 * 7 * 24 * time.Hour).Unix()

	a := &Appearance{
		ID:         1005,
		Name:       "测试查询外观模版",
		Type:       TypeMessageBubble,
		Image:      "oss://testdata/test.webp",
		TextColor:  "#FFFFFF",
		StartTime:  now.Unix(),
		ExpireTime: &expTime,
	}
	expTime2 := now.Add(24 * time.Hour).Unix()
	a2 := &Appearance{
		ID:         1006,
		Name:       "测试查询外观模版 2",
		Type:       TypeMessageBubble,
		Image:      "oss://testdata/test.webp",
		TextColor:  "#FFFFFF",
		StartTime:  now.Unix(),
		ExpireTime: &expTime2,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{a.ID, a2.ID}}})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{a, a2})
	require.NoError(err)

	appearances, err := Find(bson.M{
		"id":   bson.M{"$in": bson.A{a.ID, a2.ID}},
		"type": TypeMessageBubble,
	}, nil)
	require.NoError(err)
	require.Len(appearances, 2)
	for i := range appearances {
		assert.Equal(TypeMessageBubble, appearances[i].Type)
	}
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(3 * 7 * 24 * time.Hour).Unix()
	a := &Appearance{
		ID:             1000,
		Name:           "测试查询外观模版",
		Type:           TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: &expTime,
	}
	expTime2 := now.Add(24 * time.Hour).Unix()
	a2 := &Appearance{
		ID:             1001,
		Name:           "测试查询外观模版 2",
		Type:           TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: &expTime2,
	}
	a3 := &Appearance{
		ID:             1002,
		Name:           "测试查询外观模版 3",
		Type:           TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: nil,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{a.ID, a2.ID, a3.ID}}})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{a, a2, a3})
	require.NoError(err)

	aItem, err := FindOne(a.ID, a.Type)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(a.ID, aItem.ID)
	assert.Equal(a.Type, aItem.Type)
	assert.Equal(a.StartTime, aItem.StartTime)
	aItem, err = FindOne(a2.ID, a2.Type)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(a2.ID, aItem.ID)
	aItem, err = FindOne(a3.ID, a3.Type)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(a3.ID, aItem.ID)
	assert.Nil(aItem.ExpireTime)
}

func TestFindMapByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	expTime := now.Add(3 * 7 * 24 * time.Hour).Unix()
	a := &Appearance{
		ID:             1000,
		Name:           "测试查询外观模版",
		Type:           TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: &expTime,
	}
	expTime2 := now.Add(24 * time.Hour).Unix()
	a2 := &Appearance{
		ID:             1001,
		Name:           "测试查询外观模版 2",
		Type:           TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: &expTime2,
	}
	a3 := &Appearance{
		ID:             1002,
		Name:           "测试查询外观模版 3",
		Type:           TypeCardFrame,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		MessageBar: &MessageBar{
			Image: "oss://testdata/test.webp",
		},
		StartTime:  now.Unix(),
		ExpireTime: nil,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{a.ID, a2.ID, a3.ID}}})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{a, a2, a3})
	require.NoError(err)

	aItem, err := FindMapByIDs([]int64{a.ID, a2.ID, a3.ID})
	require.NoError(err)
	require.NotNil(aItem[a.ID])
	require.NotNil(aItem[a2.ID])
	require.NotNil(aItem[a3.ID])
	assert.Equal(a.ID, aItem[a.ID].ID)
	assert.Equal("测试查询外观模版", aItem[a.ID].Name)
	assert.Equal(a2.ID, aItem[a2.ID].ID)
	assert.Equal("测试查询外观模版 2", aItem[a2.ID].Name)
	assert.Equal(a3.ID, aItem[a3.ID].ID)
	assert.Equal("测试查询外观模版 3", aItem[a3.ID].Name)
}

func TestFindVipAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	a := &Appearance{
		ID:         TypeCardFrame*10 + 1,
		Name:       "测试查询贵族名片框",
		Type:       TypeCardFrame,
		From:       FromNoble,
		Image:      "oss://gifts/cardframes/001-new.png",
		StartTime:  now.Unix(),
		ExpireTime: nil,
	}
	a2 := &Appearance{
		ID:         TypeCardFrame*10 + 3,
		Name:       "测试查询贵族名片框 3 级",
		Type:       TypeCardFrame,
		From:       FromNoble,
		Image:      "oss://gifts/cardframes/003-new.png",
		StartTime:  now.Unix(),
		ExpireTime: nil,
	}
	a3 := &Appearance{
		ID:         TypeAvatarFrame*10 + 3,
		Name:       "测试查询贵族头像框 3 级",
		Type:       TypeAvatarFrame,
		From:       FromNoble,
		Image:      "oss://testdata/test.png",
		StartTime:  now.Unix(),
		ExpireTime: nil,
	}
	a4 := &Appearance{
		ID:         TypeMessageBubble*10 + 1,
		Name:       "测试查询贵族气泡框 1 级",
		Type:       TypeMessageBubble,
		From:       FromNoble,
		Image:      "oss://testdata/test.png",
		Frame:      "oss://testdata/test.png",
		TextColor:  "#FFFFFF",
		StartTime:  now.Unix(),
		ExpireTime: nil,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{a.ID, a2.ID, a3.ID, a4.ID}}})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{a, a2, a3, a4})
	require.NoError(err)

	level := 1
	aItem, err := FindVipAppearance(typeLiveNoble, level, TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(TypeCardFrame, aItem.Type)
	assert.Equal(int64(TypeCardFrame*10+level), aItem.ID)

	aItem, err = FindVipAppearance(typeLiveNoble, level, TypeMessageBubble)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(TypeMessageBubble, aItem.Type)
	assert.Equal(int64(TypeMessageBubble*10+level), aItem.ID)
	assert.Equal(a4.TextColor, aItem.TextColor)

	level = 3
	aItem, err = FindVipAppearance(typeLiveNoble, level, TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(TypeCardFrame, aItem.Type)
	assert.Equal(int64(TypeCardFrame*10+level), aItem.ID)

	aItem, err = FindVipAppearance(typeLiveNoble, -1, TypeCardFrame)
	require.NoError(err)
	require.Nil(aItem)

	aItem, err = FindVipAppearance(typeLiveNoble, level, TypeAvatarFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(TypeAvatarFrame, aItem.Type)
	assert.Equal(int64(TypeAvatarFrame*10+level), aItem.ID)
}

func TestIsVip(t *testing.T) {
	assert := assert.New(t)

	assert.True(IsVip(FromNoble))
	assert.True(IsVip(FromHighness))

	assert.False(IsVip(FromDefault))
	assert.False(IsVip(FromEvent))
	assert.False(IsVip(FromCustom))
}

func TestLiveHighnessAppearanceIDByType(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int64(91), LiveHighnessAppearanceIDByType(TypeVehicle))
	assert.Equal(int64(92), LiveHighnessAppearanceIDByType(TypeAvatarFrame))
	assert.Equal(int64(93), LiveHighnessAppearanceIDByType(TypeCardFrame))
	assert.Equal(int64(94), LiveHighnessAppearanceIDByType(TypeMessageBubble))

	assert.Zero(LiveHighnessAppearanceIDByType(TypeLimit))
}

func TestVipFromList(t *testing.T) {
	assert := assert.New(t)

	arr := VipFromList()
	assert.Len(arr, 2)
	assert.Equal(FromNoble, arr[0])
	assert.Equal(FromHighness, arr[1])
}

func TestHighnessAppearanceTypes(t *testing.T) {
	assert := assert.New(t)

	arr := HighnessAppearanceTypes()
	assert.Equal(4, len(arr))
	assert.Contains(arr, TypeVehicle)
	assert.Contains(arr, TypeAvatarFrame)
	assert.Contains(arr, TypeCardFrame)
	assert.Contains(arr, TypeMessageBubble)
}

func TestNobleAppearanceTypes(t *testing.T) {
	assert := assert.New(t)

	arr := NobleAppearanceTypes(1)
	assert.Equal(2, len(arr))
	assert.Contains(arr, TypeAvatarFrame)
	assert.Contains(arr, TypeCardFrame)

	arr = NobleAppearanceTypes(4)
	assert.Equal(3, len(arr))
	assert.Contains(arr, TypeAvatarFrame)
	assert.Contains(arr, TypeCardFrame)
	assert.Contains(arr, TypeMessageBubble)
}

func TestAllHighnessAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	appearances, err := AllHighnessAppearances()
	require.NoError(err)
	assert.Len(appearances, 4)
}

func TestAllNobleAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	appearances, err := AllNobleAppearances(4)
	require.NoError(err)
	assert.Equal(3, len(appearances))

	appearances, err = AllNobleAppearances(1)
	require.NoError(err)
	assert.Equal(2, len(appearances))
}

func TestNewSyncUpdate(t *testing.T) {
	assert := assert.New(t)

	vehicle := &Appearance{
		Name:           "测试同步座驾",
		Type:           1,
		From:           1,
		Intro:          "测试同步",
		Icon:           "oss://live/vehicles/icons/test.png",
		EffectDuration: 4000,
		Effect:         "oss://live/vehicles/effects/test.mp4;oss://live/vehicles/effects/test.png",
		WebEffect:      "oss://live/vehicles/effects/test-web.mp4;oss://live/vehicles/effects/test-web.webm;oss://live/vehicles/effects/test-web.png",
		MessageBar: &MessageBar{
			Message: "<font color=\"#FFF297\">${username}</font><font color=\"#C4E8FF\">乘坐</font><font color=\"#FFF297\">${vehicle_name}</font><font color=\"#C4E8FF\">来了</font>",
			Image:   "oss://live/vehicles/messagebars/test_0_260_0_260.png",
		},
	}

	updateM := vehicle.NewSyncUpdate()
	assert.Equal(vehicle.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(vehicle.Icon, updateM["$set"].(bson.M)["icon"])
	assert.NotEmpty(updateM["$set"].(bson.M)["effect"])
	assert.Equal(vehicle.Effect, updateM["$set"].(bson.M)["effect"])
	assert.NotEmpty(updateM["$set"].(bson.M)["web_effect"])
	assert.Equal(vehicle.WebEffect, updateM["$set"].(bson.M)["web_effect"])
	assert.NotZero(updateM["$set"].(bson.M)["effect_duration"])
	assert.Equal(vehicle.EffectDuration, updateM["$set"].(bson.M)["effect_duration"])

	// 测试气泡框可留空的字段
	vehicle.EffectDuration = 0
	updateM = vehicle.NewSyncUpdate()
	assert.Nil(updateM["$set"].(bson.M)["effect_duration"])
	assert.Equal("", updateM["$unset"].(bson.M)["effect_duration"])

	avatarFrame := &Appearance{
		Name:  "测试同步头像框",
		Type:  2,
		From:  1,
		Intro: "测试同步",
		Icon:  "oss://live/avatarframes/icons/test.png",
		Image: "oss://live/avatarframes/images/test.png",
	}

	updateM = avatarFrame.NewSyncUpdate()
	assert.Equal(avatarFrame.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(avatarFrame.Icon, updateM["$set"].(bson.M)["icon"])
	assert.NotEmpty(updateM["$set"].(bson.M)["image"])
	assert.Equal(avatarFrame.Image, updateM["$set"].(bson.M)["image"])

	cardFrame := &Appearance{
		Name:     "测试同步名片框",
		Type:     3,
		From:     1,
		Intro:    "测试同步",
		Icon:     "oss://live/cardframes/icons/test.png",
		Image:    "oss://live/cardframes/images/test.png",
		ImageNew: "oss://live/cardframes/images/test_new.png",
		TextColorItem: &TextColorItem{
			Username:        "#FFFFFF",
			Introduction:    "#FFFFFF",
			ReportAndManage: "#FFFFFF",
		},
	}

	updateM = cardFrame.NewSyncUpdate()
	assert.Equal(cardFrame.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(cardFrame.Icon, updateM["$set"].(bson.M)["icon"])
	assert.NotEmpty(updateM["$set"].(bson.M)["image"])
	assert.Equal(cardFrame.Image, updateM["$set"].(bson.M)["image"])
	assert.NotEmpty(updateM["$set"].(bson.M)["image_new"])
	assert.Equal(cardFrame.ImageNew, updateM["$set"].(bson.M)["image_new"])
	assert.Equal(cardFrame.TextColorItem, updateM["$set"].(bson.M)["text_color_item"])

	messageBubble := &Appearance{
		Name:      "测试同步消息气泡",
		Type:      4,
		From:      1,
		Intro:     "测试同步",
		Icon:      "oss://live/messagebubbles/icons/test.png",
		Image:     "oss://live/messagebubbles/images/test_36_36_36_36.png",
		Frame:     "oss://live/messagebubbles/frames/test_corner0.png",
		TextColor: "#FFFFFF",
	}

	updateM = messageBubble.NewSyncUpdate()
	assert.Equal(messageBubble.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(messageBubble.Icon, updateM["$set"].(bson.M)["icon"])
	assert.NotEmpty(updateM["$set"].(bson.M)["image"])
	assert.Equal(messageBubble.Image, updateM["$set"].(bson.M)["image"])
	assert.NotEmpty(updateM["$set"].(bson.M)["frame"])
	assert.Equal(messageBubble.Frame, updateM["$set"].(bson.M)["frame"])
	assert.NotEmpty(updateM["$set"].(bson.M)["text_color"])
	assert.Equal(messageBubble.TextColor, updateM["$set"].(bson.M)["text_color"])

	// 测试气泡框可留空的字段
	messageBubble.Frame = ""
	messageBubble.TextColor = ""
	updateM = messageBubble.NewSyncUpdate()
	assert.Nil(updateM["$set"].(bson.M)["frame"])
	assert.Equal("", messageBubble.Frame, updateM["$unset"].(bson.M)["frame"])
	assert.Nil(updateM["$set"].(bson.M)["text_color"])
	assert.Equal("", messageBubble.TextColor, updateM["$unset"].(bson.M)["text_color"])

	badge := &Appearance{
		Name:  "测试同步称号",
		Type:  5,
		From:  1,
		Intro: "测试同步",
		Icon:  "oss://live/badges/icons/test.png",
		Image: "oss://live/badges/images/test.png",
	}

	updateM = badge.NewSyncUpdate()
	assert.Equal(badge.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(badge.Icon, updateM["$set"].(bson.M)["icon"])
	assert.NotEmpty(updateM["$set"].(bson.M)["image"])
	assert.Equal(badge.Image, updateM["$set"].(bson.M)["image"])

	entryBubble := &Appearance{
		Name:  "进场通知",
		Type:  TypeEntryBubble,
		From:  1,
		Intro: "进场通知测试同步",
		Icon:  "oss://live/bubbles/icons/test.png",
		Image: "oss://live/bubbles/entries/test.png",
		WelcomeMessage: &WelcomeMessage{
			Text:   "欢迎光临本直播间",
			Colors: "#FECE5B;#FE982A",
			Shine:  true,
		},
	}
	updateM = entryBubble.NewSyncUpdate()
	assert.Equal(entryBubble.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(entryBubble.Icon, updateM["$set"].(bson.M)["icon"])
	assert.Equal(entryBubble.Image, updateM["$set"].(bson.M)["image"])
	assert.Equal(entryBubble.WelcomeMessage, updateM["$set"].(bson.M)["welcome_message"])

	redPacket := &Appearance{
		Name:     "礼物红包",
		Type:     8,
		From:     1,
		Intro:    "礼物红包测试同步",
		Icon:     "oss://live/badges/icons/test.png",
		Resource: "oss://test/skin.zip",
	}
	updateM = redPacket.NewSyncUpdate()
	assert.Equal(redPacket.Name, updateM["$set"].(bson.M)["name"])
	assert.Equal(redPacket.Icon, updateM["$set"].(bson.M)["icon"])
	assert.Equal(redPacket.Resource, updateM["$set"].(bson.M)["resource"])
}

func TestUserLevelGte45RewardAppearanceIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids := UserLevelGte45RewardAppearanceIDs()
	require.Len(ids, 2)
	assert.Equal(ids[0], AppearanceIDBirthdayAvatarFrameForLv45)
	assert.Equal(ids[1], AppearanceIDBirthdayBadgeForLv45)
}

func TestGetBirthdayAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx,
		bson.M{
			"id":   AppearanceIDBirthdayAvatarFrameForLv45,
			"type": TypeAvatarFrame,
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "oss://live/avatarframes/40261.png",
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	_, err = Collection().UpdateOne(ctx,
		bson.M{
			"id":   AppearanceIDBirthdayBadgeForLv45,
			"type": TypeBadge,
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "oss://live/badges/50227.png",
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	appearances, err := GetBirthdayAppearances()
	require.NoError(err)
	assert.Len(appearances, 2)
}

func TestUserLevelGte85RewardAppearanceIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids := UserLevelGte85RewardAppearanceIDs()
	require.Len(ids, 2)
	assert.Equal(ids[0], AppearanceID40250AvatarFrameForLv85)
	assert.Equal(ids[1], AppearanceID10113MessageBubbleForLv85)
}

func TestUserLevelGte160RewardAppearanceIDs(t *testing.T) {
	assert := assert.New(t)

	ids := UserLevelGte160RewardAppearanceIDs()
	assert.Equal([]int64{40262, 10118, 30033, 20107}, ids)
}

func TestTypeName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("座驾", TypeName(TypeVehicle))
	assert.Equal("", TypeName(10))
}

func TestGetIdentityBadgesByLevel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	t.Run("无映射等级的身份铭牌", func(t *testing.T) {
		appearances, err := GetIdentityBadgesByLevel([]int{200})
		assert.Equal(fmt.Errorf("[200] 级身份铭牌外观 ID 为空"), err)
		assert.Empty(appearances)
	})
	t.Run("获取 180 级身份铭牌", func(t *testing.T) {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"id": bson.M{
				"$in": []int64{AppearanceIDIdentityBadgeForLv180},
			},
		})
		require.NoError(err)
		appearance := &Appearance{
			ID: AppearanceIDIdentityBadgeForLv180,
		}
		_, err = Collection().InsertOne(ctx, appearance)
		require.NoError(err)
		appearances, err := GetIdentityBadgesByLevel([]int{180})
		require.NoError(err)
		require.Len(appearances, 1)
		assert.EqualValues(AppearanceIDIdentityBadgeForLv180, appearances[0].ID)
	})
	t.Run("获取 190 级身份铭牌", func(t *testing.T) {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"id": bson.M{
				"$in": []int64{AppearanceIDIdentityBadgeForLv190},
			},
		})
		require.NoError(err)
		appearance := &Appearance{
			ID: AppearanceIDIdentityBadgeForLv190,
		}
		_, err = Collection().InsertOne(ctx, appearance)
		require.NoError(err)
		appearances, err := GetIdentityBadgesByLevel([]int{190})
		require.NoError(err)
		require.Len(appearances, 1)
		assert.EqualValues(AppearanceIDIdentityBadgeForLv190, appearances[0].ID)
	})
	t.Run("获取 180 190 级身份铭牌", func(t *testing.T) {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"id": bson.M{
				"$in": []int64{AppearanceIDIdentityBadgeForLv180, AppearanceIDIdentityBadgeForLv190},
			},
		})
		require.NoError(err)
		_, err = Collection().InsertMany(ctx, []any{
			&Appearance{
				ID: AppearanceIDIdentityBadgeForLv180,
			},
			&Appearance{
				ID: AppearanceIDIdentityBadgeForLv190,
			},
		})
		require.NoError(err)
		appearances, err := GetIdentityBadgesByLevel([]int{180, 190})
		require.NoError(err)
		require.Len(appearances, 2)
		assert.EqualValues(AppearanceIDIdentityBadgeForLv180, appearances[0].ID)
		assert.EqualValues(AppearanceIDIdentityBadgeForLv190, appearances[1].ID)
	})
	t.Run("获取 180 200 级身份铭牌", func(t *testing.T) {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"id": bson.M{
				"$in": []int64{AppearanceIDIdentityBadgeForLv180},
			},
		})
		require.NoError(err)
		_, err = Collection().InsertMany(ctx, []any{
			&Appearance{
				ID: AppearanceIDIdentityBadgeForLv180,
			},
		})
		require.NoError(err)
		appearances, err := GetIdentityBadgesByLevel([]int{180, 200})
		require.NoError(err)
		require.Len(appearances, 1)
		assert.EqualValues(AppearanceIDIdentityBadgeForLv180, appearances[0].ID)
	})
	t.Run("身份铭牌外观缺失", func(t *testing.T) {
		_, err := Collection().DeleteMany(ctx, bson.M{
			"id": bson.M{
				"$in": []int64{AppearanceIDIdentityBadgeForLv180, AppearanceIDIdentityBadgeForLv190},
			},
		})
		require.NoError(err)
		appearances, err := GetIdentityBadgesByLevel([]int{180})
		assert.Equal(fmt.Errorf("[180] 级身份铭牌外观 [%d] 缺失", AppearanceIDIdentityBadgeForLv180), err)
		assert.Empty(appearances)
	})
}

func TestBlackCardAppearanceID(t *testing.T) {
	assert := assert.New(t)

	// 测试黑卡 1 级外观 ID 计算
	id := BlackCardAppearanceID(1, TypeIdentityBadge)
	assert.Equal(int64(109), id) // 100 + (1-1)*20 + 9

	// 测试黑卡 2 级外观 ID 计算
	id = BlackCardAppearanceID(2, TypeGiftNotification)
	assert.Equal(int64(130), id) // 100 + (2-1)*20 + 10

	// 测试黑卡 3 级外观 ID 计算
	id = BlackCardAppearanceID(3, TypeMessageBubble)
	assert.Equal(int64(144), id) // 100 + (3-1)*20 + 4

	// 测试黑卡 4 级外观 ID 计算
	id = BlackCardAppearanceID(4, TypeVehicle)
	assert.Equal(int64(161), id) // 100 + (4-1)*20 + 1
}

func TestBlackCardAppearanceTypes(t *testing.T) {
	assert := assert.New(t)

	t.Run("黑卡 1 级外观类型", func(t *testing.T) {
		types := BlackCardAppearanceTypes(1)
		expected := []int{TypeIdentityBadge, TypeEntryBubble}
		assert.Equal(expected, types)
	})

	t.Run("黑卡 2 级外观类型", func(t *testing.T) {
		types := BlackCardAppearanceTypes(2)
		expected := []int{TypeIdentityBadge, TypeEntryBubble, TypeGiftNotification}
		assert.Equal(expected, types)
	})

	t.Run("黑卡 3 级外观类型", func(t *testing.T) {
		types := BlackCardAppearanceTypes(3)
		expected := []int{TypeIdentityBadge, TypeEntryBubble, TypeGiftNotification, TypeMessageBubble}
		assert.Equal(expected, types)
	})

	t.Run("黑卡 4 级外观类型", func(t *testing.T) {
		types := BlackCardAppearanceTypes(4)
		expected := []int{TypeIdentityBadge, TypeEntryBubble, TypeGiftNotification, TypeMessageBubble, TypeVehicle}
		assert.Equal(expected, types)
	})
}

// TestAllBlackCardAppearanceIDs 测试获取所有黑卡外观 ID
func TestAllBlackCardAppearanceIDs(t *testing.T) {
	assert := assert.New(t)

	t.Run("黑卡 1 级外观 ID", func(t *testing.T) {
		ids := AllBlackCardAppearanceIDs(1)
		expected := []int64{109, 107} // TypeIdentityBadge, TypeEntryBubble
		assert.Equal(expected, ids)
	})

	t.Run("黑卡 2 级外观 ID", func(t *testing.T) {
		ids := AllBlackCardAppearanceIDs(2)
		expected := []int64{129, 127, 130} // TypeIdentityBadge, TypeEntryBubble, TypeGiftNotification
		assert.Equal(expected, ids)
	})

	t.Run("黑卡 3 级外观 ID", func(t *testing.T) {
		ids := AllBlackCardAppearanceIDs(3)
		expected := []int64{149, 147, 150, 144} // TypeIdentityBadge, TypeEntryBubble, TypeGiftNotification, TypeMessageBubble
		assert.Equal(expected, ids)
	})

	t.Run("黑卡 4 级外观 ID", func(t *testing.T) {
		ids := AllBlackCardAppearanceIDs(4)
		expected := []int64{169, 167, 170, 164, 161} // TypeIdentityBadge, TypeEntryBubble, TypeGiftNotification, TypeMessageBubble, TypeVehicle
		assert.Equal(expected, ids)
	})
}

// TestAllBlackCardAppearances 测试获取黑卡外观
func TestAllBlackCardAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testLevel := 2
	testAppearanceIDs := AllBlackCardAppearanceIDs(testLevel)

	// 准备测试数据
	testAppearances := make([]interface{}, 0, len(testAppearanceIDs))
	for _, id := range testAppearanceIDs {
		testAppearances = append(testAppearances, &Appearance{
			ID:         id,
			Name:       fmt.Sprintf("测试黑卡外观 %d", id),
			Type:       int(id-100) % 20, // 根据 ID 逆向计算外观类型
			From:       FromBlackCard,
			StartTime:  goutil.TimeNow().Unix(),
			ExpireTime: nil,
		})
	}

	// 清理旧数据并插入测试数据
	_, err := Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": testAppearanceIDs}})
	require.NoError(err)
	defer Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": testAppearanceIDs}})

	_, err = Collection().InsertMany(ctx, testAppearances)
	require.NoError(err)

	// 测试获取黑卡外观
	appearances, err := AllBlackCardAppearances(testLevel)
	require.NoError(err)
	assert.Len(appearances, len(testAppearanceIDs))

	// 验证返回的外观都是黑卡外观
	for _, appearance := range appearances {
		assert.Equal(FromBlackCard, appearance.From)
		assert.Contains(testAppearanceIDs, appearance.ID)
	}
}
