package appearance

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcard"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// AppearanceID 外观 ID 常量
const (
	AppearanceIDReserved             int64 = 100 // 预留的前 100 个 ID
	AppearanceIDLiveHighnessReserved int64 = 90  // 上神外观起始值

	AppearanceIDBirthdayAvatarFrameForLv45 int64 = 40261 // 每日一次，用户等级 >= 45 && 当天过生日，赠送的头像框（每年仅能领取一次）
	AppearanceIDBirthdayBadgeForLv45       int64 = 50227 // 每日一次，用户等级 >= 45 && 当天过生日，赠送的称号（每年仅能领取一次）

	AppearanceID40250AvatarFrameForLv85   int64 = 40250 // 每个自然月，用户等级 >= 85 && 直播消费满 1000 元人民币，赠送的头像框
	AppearanceID10113MessageBubbleForLv85 int64 = 10113 // 每个自然月，用户等级 >= 85 && 直播消费满 1000 元人民币，赠送的气泡框

	AppearanceID40262AvatarFrameForLv160   int64 = 40262 // 用户等级 >= 160，赠送的永久头像框
	AppearanceID10118MessageBubbleForLv160 int64 = 10118 // 用户等级 >= 160，赠送的永久气泡框
	AppearanceID30033CardFrameForLv160     int64 = 30033 // 用户等级 >= 160，赠送的永久名片框
	AppearanceID20107VehicleForLv160       int64 = 20107 // 用户等级 >= 160，赠送的永久座驾

	AppearanceIDIdentityBadgeForLv180 int64 = 90001 // 180 级身份铭牌

	AppearanceIDIdentityBadgeForLv190 int64 = 90002 // 190 级身份铭牌
)

// type 外观中心的物品类型，TypeLimit 为最后一种枚举值
const (
	TypeVehicle           = iota + 1 // 座驾
	TypeAvatarFrame                  // 头像框
	TypeCardFrame                    // 名片框
	TypeMessageBubble                // 气泡框
	TypeBadge                        // 称号
	TypeBackgroundPendant            // 直播间挂件
	TypeEntryBubble                  // 进场通知（气泡）
	TypeRedPacket                    // 红包封面
	TypeIdentityBadge                // 身份铭牌
	TypeGiftNotification             // 送礼通知
	TypeLimit                        // 最大值，新的枚举值需要添加到 TypeLimit 之前

	TypeNoSpecified = -1 // 用于查询非特定的 type
)

// from 来源类型
const (
	FromDefault   = iota // 默认
	FromEvent            // 活动
	FromCustom           // 专属定制
	FromNoble            // 贵族外观（普通贵族和体验贵族）
	FromHighness         // 上神外观
	FromBlackCard        // 黑卡外观
)

// 会员类型
// WORKAROUND: 防止包循环引用，常量跟 vip 包下保持一致
const (
	typeLiveNoble      = iota + 1 // 普通贵族
	typeLiveHighness              // 上神
	typeLiveTrialNoble            // 体验贵族

	highnessLevel = 1
)

// 进场通知样式
const (
	EntryStyleDefault     = 0 // 默认
	EntryStyleLeftToRight = 1 // 从左往右展开
)

// Appearance mongodb 查找的外观的结构
type Appearance struct {
	OID primitive.ObjectID `bson:"_id,omitempty"`

	ID    int64  `bson:"id"`
	Name  string `bson:"name"`
	Type  int    `bson:"type"`
	From  int    `bson:"from"`
	Intro string `bson:"intro"`
	Icon  string `bson:"icon"`

	Effect         string `bson:"effect"`
	WebEffect      string `bson:"web_effect"`
	EffectDuration int64  `bson:"effect_duration"`

	// 头像框/名片框/气泡框/称号/直播间挂件和进场通知都需要 Image 字段
	Image string `bson:"image,omitempty"`
	// 新版名片框需要 ImageNew 字段
	// WORKAROUND: 兼容 iOS >= 4.7.4, 安卓 >= 5.6.2
	ImageNew string `bson:"image_new,omitempty"`
	// 仅气泡框需要 Frame 和 TextColor 字段
	Frame     string `bson:"frame,omitempty"`
	TextColor string `bson:"text_color,omitempty"`
	// 目前仅名片框需要 TextColorItem 字段
	TextColorItem *TextColorItem `bson:"text_color_item,omitempty"`
	// 仅座驾需要 MessageBar 字段
	MessageBar *MessageBar `bson:"message_bar,omitempty"`
	// 仅进场通知需要 WelcomeMessage 和 EntryStyle 字段
	WelcomeMessage *WelcomeMessage `bson:"welcome_message,omitempty"`
	EntryStyle     int             `bson:"entry_style,omitempty"`
	// 目前仅红包需要 resource 字段，用于存红包封面皮肤资源地址
	Resource string `bson:"resource,omitempty"`

	StartTime    int64  `bson:"start_time"`
	ExpireTime   *int64 `bson:"expire_time"`
	ModifiedTime int64  `bson:"modified_time"`
}

// MessageBar 消息栏
// TODO: 需要统一所有 MessageBar 的结构和相应的业务逻辑
type MessageBar struct {
	Message string `bson:"message" json:"message"`
	// 文字背景图片，有缓存，需要添加 json tag
	Image string `bson:"image" json:"image,omitempty"`
	// 文字颜色 有缓存，需要添加 json tag
	NormalColor string `bson:"normal_color" json:"normal_color,omitempty"`
	// 高亮文字颜色 有缓存，需要添加 json tag
	HighlightColor string `bson:"highlight_color" json:"highlight_color,omitempty"`

	ImageURL string `bson:"-" json:"image_url"`
}

// WelcomeMessage 进场通知的可定制欢迎文案
type WelcomeMessage struct {
	Text string `bson:"text" json:"text"`
	// Colors 文案的渐变色，格式: "#000000;#FFFFFF"
	Colors string `bson:"colors,omitempty" json:"colors,omitempty"`
	// Shine 控制是否有闪光
	Shine bool `bson:"shine,omitempty" json:"shine,omitempty"`
}

// TextColorItem 名片框文本颜色配置
type TextColorItem struct {
	// 用户昵称字体颜色，格式: "#FFFFFF"
	Username string `bson:"username,omitempty" json:"username,omitempty"`
	// 个性签名字体颜色，格式: "#FFFFFF"
	Introduction string `bson:"introduction,omitempty" json:"introduction,omitempty"`
	// 举报和管理按钮字体颜色，格式: "#FFFFFF"
	ReportAndManage string `bson:"report_and_manage,omitempty" json:"report_and_manage,omitempty"`
}

// Collection 返回 Appearances 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("appearances")
}

// VipAppearanceID returns a new noble appearance id based on vip type, vip level and appearance type
func VipAppearanceID(vipType, level, appearanceType int) int64 {
	switch vipType {
	case typeLiveNoble, typeLiveTrialNoble:
		return int64(appearanceType*10 + level)
	case typeLiveHighness:
		return LiveHighnessAppearanceIDByType(appearanceType)
	default:
		return 0
	}
}

// BlackCardAppearanceID returns a black card appearance ID based on black card level and appearance type
// Formula: 100 + (blackcard_level-1)*20 + appearanceType
// Each black card level reserves 20 IDs
func BlackCardAppearanceID(blackCardLevel, appearanceType int) int64 {
	return 100 + int64((blackCardLevel-1)*20+appearanceType)
}

// Find finds appearances
func Find(filter interface{}, findOpt *options.FindOptions) ([]*Appearance, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col, err := Collection().Find(ctx, filter, findOpt)
	if err != nil {
		return nil, err
	}

	defer col.Close(ctx)
	res := make([]*Appearance, 0)
	err = col.All(ctx, &res)
	if err != nil {
		return nil, err
	}

	return res, nil
}

// FindOne finds a appearance based on appearance id and appearance type
func FindOne(id int64, appearanceType int) (*Appearance, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := SetValidTimeFilter(bson.M{
		"id": id,
	}, goutil.TimeNow().Unix())
	if appearanceType != TypeNoSpecified {
		filter["type"] = appearanceType
	}
	var item Appearance
	err := Collection().FindOne(ctx, filter).Decode(&item)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &item, nil
}

// FindMapByIDs 通过外观 ID 查询外观
func FindMapByIDs(ids []int64) (map[int64]*Appearance, error) {
	appearances, err := Find(bson.M{
		"id": bson.M{"$in": util.Uniq(ids)},
	}, nil)
	if err != nil {
		return nil, err
	}
	return goutil.ToMap(appearances, "ID").(map[int64]*Appearance), nil
}

// FindVipAppearance finds a vip appearance
func FindVipAppearance(vipType, level, appearanceType int) (*Appearance, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	filter := SetValidTimeFilter(bson.M{
		"id":   VipAppearanceID(vipType, level, appearanceType),
		"type": appearanceType,
		"from": bson.M{
			"$in": VipFromList(),
		},
	}, now.Unix())
	var item Appearance
	err := Collection().FindOne(ctx, filter).Decode(&item)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return &item, nil
}

// SetValidTimeFilter 为 mongodb 的 filter 设置有效时间的判断
// appearance 和 user_appearance 的有效时间判断是一致的
func SetValidTimeFilter(filter bson.M, nowUnix int64) bson.M {
	filter["start_time"] = bson.M{"$lte": nowUnix}
	filter["expire_time"] = bson.M{"$not": bson.M{"$lte": nowUnix}}
	return filter
}

// IsVip 是否是贵族外观
func IsVip(from int) bool {
	return from == FromNoble || from == FromHighness
}

// IsVipOrBlackCard 是否是贵族外观或黑卡外观
func IsVipOrBlackCard(from int) bool {
	return IsVip(from) || from == FromBlackCard
}

// LiveHighnessAppearanceIDByType 根据外观类型返回上神外观 ID
func LiveHighnessAppearanceIDByType(appearanceType int) int64 {
	if appearanceType >= TypeLimit {
		return 0
	}
	return AppearanceIDLiveHighnessReserved + int64(appearanceType)
}

// VipFromList 来源贵族外观
func VipFromList() []int {
	return []int{FromNoble, FromHighness}
}

// HighnessAppearanceTypes 上神外观类型
func HighnessAppearanceTypes() []int {
	return []int{
		TypeVehicle, TypeAvatarFrame, TypeCardFrame, TypeMessageBubble,
	}
}

// NobleAppearanceTypes 贵族外观类型
func NobleAppearanceTypes(level int) []int {
	types := []int{
		TypeAvatarFrame, TypeCardFrame,
	}
	if level >= 4 /* vip.NobleLevel4 */ { // 贵族等级大于等于 4 级（大咖）才有气泡框
		types = append(types, TypeMessageBubble)
	}
	return types
}

// BlackCardAppearanceTypes 获取指定等级黑卡可用的外观类型列表
func BlackCardAppearanceTypes(level int) []int {
	types := []int{
		TypeIdentityBadge, // 身份铭牌，所有等级都有
		TypeEntryBubble,   // 进场通知，所有等级都有
	}

	// Lv2 及以上等级拥有送礼通知
	if level >= liveblackcard.BlackCardLevel2 {
		types = append(types, TypeGiftNotification)
	}

	// Lv3 及以上等级拥有气泡框
	if level >= liveblackcard.BlackCardLevel3 {
		types = append(types, TypeMessageBubble)
	}

	// Lv4 及以上等级拥有专属座驾
	if level >= liveblackcard.BlackCardLevel4 {
		types = append(types, TypeVehicle)
	}

	return types
}

func allAppearances(appearanceIDs []int64) ([]*Appearance, error) {
	filter := bson.M{
		"id": bson.M{"$in": appearanceIDs},
	}
	appearances, err := Find(filter, nil)
	if err != nil {
		return nil, err
	}
	return appearances, nil
}

// AllHighnessAppearances 获取上神外观
func AllHighnessAppearances() ([]*Appearance, error) {
	appearanceTypes := HighnessAppearanceTypes()
	appearanceIDs := goutil.SliceMap(appearanceTypes, func(appearanceType int) int64 {
		return VipAppearanceID(typeLiveHighness, highnessLevel, appearanceType)
	})
	return allAppearances(appearanceIDs)
}

// AllNobleAppearances 获取贵族外观
func AllNobleAppearances(level int) ([]*Appearance, error) {
	appearanceTypes := NobleAppearanceTypes(level)
	appearanceIDs := goutil.SliceMap(appearanceTypes, func(appearanceType int) int64 {
		return VipAppearanceID(typeLiveNoble, level, appearanceType)
	})
	return allAppearances(appearanceIDs)
}

// AllBlackCardAppearances 获取黑卡外观
func AllBlackCardAppearances(level int) ([]*Appearance, error) {
	return allAppearances(AllBlackCardAppearanceIDs(level))
}

// AllBlackCardAppearanceIDs 获取指定等级黑卡的所有外观ID
func AllBlackCardAppearanceIDs(level int) []int64 {
	appearanceTypes := BlackCardAppearanceTypes(level)
	appearanceIDs := goutil.SliceMap(appearanceTypes, func(appearanceType int) int64 {
		return BlackCardAppearanceID(level, appearanceType)
	})
	return appearanceIDs
}

// NewSyncUpdate 新建 MongoDB 更新的 bson.M
func (a *Appearance) NewSyncUpdate() bson.M {
	var (
		unsetM = bson.M{}
		setM   = bson.M{
			"name":  a.Name,
			"from":  a.From,
			"type":  a.Type,
			"icon":  a.Icon,
			"intro": a.Intro,
		}
	)
	switch a.Type {
	case TypeVehicle:
		setM["effect"] = a.Effect
		setM["web_effect"] = a.WebEffect
		setM["message_bar"] = a.MessageBar
		if a.EffectDuration != 0 {
			setM["effect_duration"] = a.EffectDuration
		} else {
			unsetM["effect_duration"] = ""
		}
	case TypeAvatarFrame:
		setM["image"] = a.Image
	case TypeCardFrame:
		setM["image"] = a.Image
		setM["image_new"] = a.ImageNew
		setM["text_color_item"] = a.TextColorItem
	case TypeMessageBubble:
		setM["image"] = a.Image
		if a.Frame != "" {
			setM["frame"] = a.Frame
		} else {
			unsetM["frame"] = ""
		}
		if a.TextColor != "" {
			setM["text_color"] = a.TextColor
		} else {
			unsetM["text_color"] = ""
		}
	case TypeBadge, TypeBackgroundPendant:
		setM["image"] = a.Image
	case TypeEntryBubble:
		setM["image"] = a.Image
		setM["welcome_message"] = a.WelcomeMessage
	case TypeRedPacket:
		setM["resource"] = a.Resource
	}

	if len(unsetM) > 0 {
		return bson.M{"$set": setM, "$unset": unsetM}
	}

	return bson.M{"$set": setM}
}

// UserLevelGte45RewardAppearanceIDs 用户等级大于等于 45 可获取的生日特权奖励外观 ID
func UserLevelGte45RewardAppearanceIDs() []int64 {
	return []int64{AppearanceIDBirthdayAvatarFrameForLv45, AppearanceIDBirthdayBadgeForLv45}
}

// GetBirthdayAppearances 获取生日特权外观
func GetBirthdayAppearances() ([]*Appearance, error) {
	appearanceIDs := UserLevelGte45RewardAppearanceIDs()
	appearances, err := Find(bson.M{
		"id":   bson.M{"$in": appearanceIDs},
		"type": bson.M{"$in": []int{TypeBadge, TypeAvatarFrame}},
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("生日特权外观错误：%v", err)
	}
	if len(appearances) != len(appearanceIDs) {
		return nil, fmt.Errorf("生日特权外观 %v 缺失", appearanceIDs)
	}
	return appearances, nil
}

// UserLevelGte85RewardAppearanceIDs 用户等级大于等于 85 完成消费任务奖励的外观 ID
func UserLevelGte85RewardAppearanceIDs() []int64 {
	return []int64{AppearanceID40250AvatarFrameForLv85, AppearanceID10113MessageBubbleForLv85}
}

// UserLevelGte160RewardAppearanceIDs 用户等级 160 等级外观套装外观 ID
func UserLevelGte160RewardAppearanceIDs() []int64 {
	return []int64{
		AppearanceID40262AvatarFrameForLv160,
		AppearanceID10118MessageBubbleForLv160,
		AppearanceID30033CardFrameForLv160,
		AppearanceID20107VehicleForLv160,
	}
}

// GetGte160RewardAppearances 获取 160 级特权外观
func GetGte160RewardAppearances() ([]*Appearance, error) {
	appearanceIDs := UserLevelGte160RewardAppearanceIDs()
	appearances, err := Find(bson.M{
		"id": bson.M{"$in": appearanceIDs},
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("160 级特权外观错误：%v", err)
	}
	if len(appearances) != len(appearanceIDs) {
		return nil, fmt.Errorf("160 级特权外观 %v 缺失", appearanceIDs)
	}
	return appearances, nil
}

// userLevelIdentityBadgeMap 等级和身份铭牌映射关系，200 级及之后每升 10 级的铭牌由运营手动发放
var userLevelIdentityBadgeMap = map[int][]int64{
	180: {AppearanceIDIdentityBadgeForLv180},
	190: {AppearanceIDIdentityBadgeForLv190},
}

// GetIdentityBadgesByLevel 获取身份铭牌特权外观
func GetIdentityBadgesByLevel(levels []int) ([]*Appearance, error) {
	appearanceIDs := make([]int64, 0, len(levels))
	for _, level := range levels {
		appearanceIDs = append(appearanceIDs, userLevelIdentityBadgeMap[level]...)
	}
	if len(appearanceIDs) == 0 {
		return nil, fmt.Errorf("%v 级身份铭牌外观 ID 为空", levels)
	}
	appearances, err := Find(bson.M{
		"id": bson.M{"$in": appearanceIDs},
	}, nil)
	if err != nil {
		return nil, fmt.Errorf("获取 %v 级身份铭牌外观错误：%v", levels, err)
	}
	if len(appearances) != len(appearanceIDs) {
		return nil, fmt.Errorf("%v 级身份铭牌外观 %v 缺失", levels, appearanceIDs)
	}
	return appearances, nil
}

// TypeName 通过外观类型获取外观类型名称
func TypeName(Type int) string {
	switch Type {
	case TypeVehicle:
		return "座驾"
	case TypeAvatarFrame:
		return "头像框"
	case TypeCardFrame:
		return "名片框"
	case TypeMessageBubble:
		return "气泡框"
	case TypeBadge:
		return "称号"
	case TypeBackgroundPendant:
		return "直播间挂件"
	case TypeEntryBubble:
		return "进场通知"
	case TypeRedPacket:
		return "红包封面"
	default:
		return ""
	}
}
