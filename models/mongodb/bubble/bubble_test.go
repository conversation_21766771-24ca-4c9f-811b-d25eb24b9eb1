package bubble

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Bubble{}, "_id", "bubble_id", "type", "image", "float", "shine",
		"normal_color", "highlight_color", "create_time", "modified_time",
		"usage", "compat", "danmaku", "effect_image_url")
	kc.Check(Simple{}, "type", "noble_level", "bubble_id",
		"image_url", "text_color", "frame_url", "float", "shine",
		"normal_color", "highlight_color", "compat", "effect_image_url")
	kc.Check(Danmaku{}, "icon_url", "category", "user_id", "user_level",
		"noble_type", "noble_level")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Bubble{}, "bubble_id", "image_url", "float", "shine", "effect_image_url")
	kc.Check(Simple{}, "type", "noble_level", "bubble_id", "image_url",
		"text_color", "frame_url", "float", "shine", "compat_type", "effect_image_url")
}

func TestAllBubbles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := Bubble{BubbleID: 100, Image: "oss://test.png"}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)
	bubbles, err := AllBubbles()
	require.NoError(err)
	assert.NotNil(bubbles)
	assert.GreaterOrEqual(len(bubbles), 1)
	b.Image = storage.ParseSchemeURL(b.Image)
	assert.True(func() bool {
		for i := range bubbles {
			if bubbles[i].Image == b.Image {
				return true
			}
		}
		return false
	}(), "url 链接未处理")

	t.Run("FindOne", func(t *testing.T) {
		bubble, err := FindOne(100)
		require.NoError(err)
		assert.Equal(int64(100), bubble.BubbleID)

		bubble, err = FindOne(9999999999)
		require.NoError(err)
		assert.Nil(bubble)
	})
	key := keys.KeyBubbles.Format()
	v, ok := service.Cache10s.Get(key)
	require.True(ok)
	cachedBubbles := v.([]Bubble)
	assert.Equal(bubbles, cachedBubbles)
	cachedBubbles[0].BubbleID++
	bubbles, err = AllBubbles()
	require.NoError(err)
	assert.NotEqual(cachedBubbles, b)
}

func TestNewSimple(t *testing.T) {
	assert := assert.New(t)

	b := Bubble{
		BubbleID: 128,
		Image:    "http://example.test.png",
		Float:    1,
		Shine:    1,
	}
	assert.Equal(&Simple{
		Type:     TypeStrCustom,
		BubbleID: 128,
		ImageURL: "http://example.test.png",
		Float:    1,
		Shine:    1,
	}, b.NewSimple())
}

func TestFindSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	all, err := AllBubbles()
	require.NoError(err)
	require.NotEmpty(all)

	b, err := FindSimple(all[0].BubbleID)
	require.NoError(err)
	assert.Equal(TypeStrCustom, b.Type)
	assert.Equal(all[0].BubbleID, b.BubbleID)
	assert.Equal(all[0].Image, b.ImageURL)
	assert.Zero(b.NobleLevel)

	b, err = FindSimple(123456789)
	require.NoError(err)
	assert.Nil(b)
}

func TestTypeStr(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("custom", TypeStr(0))
	assert.Equal("custom", TypeStr(1))
	assert.Equal("vehicle", TypeStr(2))
}

func TestSimpleAppendFormatParams(t *testing.T) {
	assert := assert.New(t)

	b := Simple{}
	f := make(map[string]string, 2)
	b.AppendFormatParams(f)
	assert.Empty(f)
	b.HighlightColor = "123"
	b.NormalColor = "456"
	b.AppendFormatParams(f)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(f, "highlight_color", "normal_color")
}
