package bubble

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
)

// 消息中的 type
const (
	TypeStrNoble   = "noble"
	TypeStrCustom  = "custom" // 目前用于飘屏气泡
	TypeStrVehicle = "vehicle"
	TypeStrMessage = "message"
)

// 气泡悬停效果
const (
	FloatDisable = iota // 默认，不悬停
	FloatEnable         // 悬停
)

// 数据库中的 type
const (
	TypeNotify  = iota + 1 // 飘屏气泡，坐标控制拉伸区域
	TypeVehicle            // 进场座驾气泡，坐标控制文字显示区域
	TypeDanmaku            // 付费弹幕气泡，坐标控制拉伸区域
)

// special bubble
const (
	BubbleIDDefault  = 1  // 消息中无气泡字段时的缺省气泡 ID
	BubbleIDHourTop1 = 12 // 广播上小时榜第一的通知气泡 ID
)

// 付费弹幕气泡类型
const (
	DanmakuCategoryNormal    = iota + 1 // 普通
	DanmakuCategoryRoomAdmin            // 房管专属
	DanmakuCategoryPersonal             // 个人专属
	DanmakuCategoryNoble                // 贵族专属
	DanmakuCategoryUserLevel            // 等级专属
	DanmakuCategorySuperFan             // 超粉专属
)

// Bubble 气泡
type Bubble struct {
	OID      primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	BubbleID int64              `bson:"bubble_id" json:"bubble_id"`
	Type     int                `bson:"type" json:"-"`
	Image    string             `bson:"image" json:"image_url"`
	// 悬停效果
	Float int `bson:"float,omitempty" json:"float,omitempty"`
	// 控制闪光
	Shine int `bson:"shine,omitempty" json:"shine,omitempty"`
	// 特效图片，在需要展示特效的气泡中配置
	EffectImage string `bson:"effect_image,omitempty" json:"effect_image_url,omitempty"`
	// 文本颜色
	NormalColor    string `bson:"normal_color" json:"-"`
	HighlightColor string `bson:"highlight_color" json:"-"`

	CreateTime   time.Time `bson:"create_time" json:"-"`
	ModifiedTime time.Time `bson:"modified_time" json:"-"`

	// usage 字段仅用于表示气泡的用途，不会被读取使用
	Usage string `bson:"usage,omitempty" json:"-"`

	// 神话上神兼容字段
	// WORKAROUND: 兼容 iOS < 6.0.9 和 Android < 6.0.9 控制是否下发原有的神话上神飘屏样式
	Compat int `bson:"compat,omitempty" json:"-"`

	// 付费弹幕结构
	Danmaku *Danmaku `bson:"danmaku,omitempty" json:"-"`
}

// Danmaku 付费弹幕结构
type Danmaku struct {
	IconURL    string `bson:"icon_url" json:"-"`
	Category   int    `bson:"category,omitempty" json:"-"`
	UserID     int64  `bson:"user_id,omitempty" json:"-"`
	UserLevel  int    `bson:"user_level,omitempty" json:"-"`
	NobleType  int    `bson:"noble_type,omitempty" json:"-"` // 气泡贵族类型：普通贵族（包含体验贵族），上神
	NobleLevel int    `bson:"noble_level,omitempty" json:"-"`
}

// Simple 气泡简化版，常用于广播消息
type Simple struct {
	Type       string `bson:"type" json:"type"`
	NobleLevel int    `bson:"noble_level,omitempty" json:"noble_level,omitempty"`
	BubbleID   int64  `bson:"bubble_id,omitempty" json:"bubble_id,omitempty"`
	ImageURL   string `bson:"image_url,omitempty" json:"image_url,omitempty"`

	TextColor string `bson:"text_color,omitempty" json:"text_color,omitempty"`
	FrameURL  string `bson:"frame_url,omitempty" json:"frame_url,omitempty"`

	// 悬停效果
	Float int `bson:"float,omitempty" json:"float,omitempty"`
	// 控制闪光
	Shine int `bson:"shine,omitempty" json:"shine,omitempty"`
	// 特效图片，在需要展示特效的气泡中配置
	EffectImageURL string `bson:"effect_image_url,omitempty" json:"effect_image_url,omitempty"`
	// 文本颜色
	NormalColor    string `bson:"normal_color" json:"-"`
	HighlightColor string `bson:"highlight_color" json:"-"`

	// 神话上神兼容字段
	// WORKAROUND: 兼容 iOS < 6.0.9 和 Android < 6.0.9 控制是否下发原有的神话上神飘屏样式
	Compat int `bson:"compat,omitempty" json:"-"`

	// 上神兼容字段，只会有字符串值 highness
	// WORKAROUND: 兼容 iOS < 6.0.9 和 Android < 6.0.9 上神飘屏不支持闪光判断
	CompatType string `bson:"-" json:"compat_type,omitempty"`
}

// NewSimple new simple
func (b *Bubble) NewSimple() *Simple {
	return &Simple{
		Type:           TypeStr(b.Type),
		BubbleID:       b.BubbleID,
		ImageURL:       b.Image,
		Float:          b.Float,
		Shine:          b.Shine,
		NormalColor:    b.NormalColor,
		HighlightColor: b.HighlightColor,
		Compat:         b.Compat,
		EffectImageURL: b.EffectImage,
	}
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("bubbles")
}

// AllBubbles 查询所有 bubbles
func AllBubbles() ([]Bubble, error) {
	key := keys.KeyBubbles.Format()
	v, ok := service.Cache10s.Get(key)
	if ok {
		v := v.([]Bubble)
		res := make([]Bubble, len(v))
		copy(res, v)
		return res, nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}
	res := make([]Bubble, 0)
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	for i := range res {
		res[i].Image = storage.ParseSchemeURL(res[i].Image)
		res[i].EffectImage = storage.ParseSchemeURL(res[i].EffectImage)
		if res[i].Danmaku != nil {
			res[i].Danmaku.IconURL = storage.ParseSchemeURL(res[i].Danmaku.IconURL)
		}
	}
	cached := make([]Bubble, len(res))
	copy(cached, res)
	service.Cache10s.Set(key, cached, 0)
	return res, nil
}

// FindOne  find one bubble from cache
func FindOne(bubbleID int64) (*Bubble, error) {
	l, err := AllBubbles()
	if err != nil {
		return nil, err
	}
	for _, value := range l {
		if value.BubbleID == bubbleID {
			return &value, nil
		}
	}
	return nil, nil
}

// FindSimple 查询下发的气泡（通知中的）
func FindSimple(bubbleID int64) (*Simple, error) {
	b, err := AllBubbles()
	if err != nil {
		return nil, err
	}
	for i := range b {
		if b[i].BubbleID == bubbleID {
			return b[i].NewSimple(), nil
		}
	}
	return nil, nil
}

// TypeStr 广播消息中的气泡类型
func TypeStr(bubbleType int) string {
	switch bubbleType {
	case TypeNotify:
		return TypeStrCustom
	case TypeVehicle:
		return TypeStrVehicle
	default:
		return TypeStrCustom
	}
}

// AppendFormatParams 将文字颜色参数加入 format
func (b Simple) AppendFormatParams(format map[string]string) {
	if b.NormalColor != "" {
		format["normal_color"] = b.NormalColor
	}
	if b.HighlightColor != "" {
		format["highlight_color"] = b.HighlightColor
	}
}
