package viewlog

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(ViewLog{}, "_id", "created_time")
	kc.Check(Helper{}, "_room_id", "room_id", "room_name",
		"room_creator_id", "room_cover", "user_id", "ip", "updated_time")
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, pa, err := Find(-20, 1, 10)
	require.NoError(err)
	assert.Len(res, 1)
	assert.Equal(int64(10), pa.PageSize)
	res, _, err = Find(-20, 100, 20)
	assert.True(err == nil && len(res) == 0, err)
}

func TestDeleteByRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 插入数据
	userID := int64(114514)
	testIP := "127.0.0.1"
	OID1, err := primitive.ObjectIDFromHex("63886201968c1c17734091c1")
	require.NoError(err)
	r1 := room.Room{
		OID: OID1,
		Helper: room.Helper{
			RoomID: 1919810,
		},
	}
	require.NoError(Update(userID, &r1, testIP, util.TimeNow()))
	OID2, err := primitive.ObjectIDFromHex("63886201968c1c17734091c2")
	require.NoError(err)
	r2 := room.Room{
		OID: OID2,
		Helper: room.Helper{
			RoomID: 1919816,
		},
	}
	require.NoError(Update(userID, &r2, testIP, util.TimeNow()))

	err = DeleteByRoomIDs(userID, []int64{1919810})
	require.NoError(err)
	// 此数据已删除
	_, err = findOne(bson.M{"user_id": userID, "room_id": 1919810})
	assert.True(mongodb.IsNoDocumentsError(err))
	// 此数据未删除
	_, err = findOne(bson.M{"user_id": userID, "room_id": 1919816})
	assert.NoError(err)
}

func TestDeleteAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 插入数据
	userID := int64(1145145)
	testIP := "127.0.0.1"
	OID1, err := primitive.ObjectIDFromHex("63886201968c1c17734091c1")
	require.NoError(err)
	r1 := room.Room{
		OID: OID1,
		Helper: room.Helper{
			RoomID: 1919810,
		},
	}
	require.NoError(Update(userID, &r1, testIP, util.TimeNow()))
	OID2, err := primitive.ObjectIDFromHex("63886201968c1c17734091c2")
	require.NoError(err)
	r2 := room.Room{
		OID: OID2,
		Helper: room.Helper{
			RoomID: 1919816,
		},
	}
	require.NoError(Update(userID, &r2, testIP, util.TimeNow()))

	err = DeleteAll(userID)
	require.NoError(err)
	// 数据已全部删除
	_, err = findOne(bson.M{"user_id": userID})
	assert.True(mongodb.IsNoDocumentsError(err))
}

func TestUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(deleteOne(bson.M{"user_id": -10}))
	r := new(room.Room)
	idHex := "5992718c7b065178ec35f6a6"
	r.OID, _ = primitive.ObjectIDFromHex(idHex)
	r.Name = "测试更新访问日志"
	r.CreatorID = 516
	r.RoomID = 3192516
	ip := "127.0.0.1"
	when := time.Date(2019, 12, 1, 0, 0, 0, 0, time.Local)
	add1s := when.Add(time.Second)
	require.NoError(Update(-10, r, ip, when))
	require.NoError(Update(-10, r, ip, when.Add(time.Second)))
	vl, err := findOne(bson.M{"user_id": -10})
	require.NoError(err)
	assert.Equal(r.RoomID, vl.RoomID)
	assert.Equal(r.CreatorID, vl.RoomCreatorID)
	assert.Equal(ip, vl.IP)
	assert.Equal(when.Unix(), vl.CreatedTime.Unix())
	assert.Equal(add1s.Unix(), vl.UpdatedTime.Unix())
}

func deleteOne(filter interface{}) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteOne(ctx, filter)
	return err
}

func findOne(filter interface{}) (*ViewLog, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	vl := new(ViewLog)
	err := Collection().FindOne(ctx, filter).Decode(vl)
	return vl, err
}
