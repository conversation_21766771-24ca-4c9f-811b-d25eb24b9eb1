package viewlog

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CollectionName collection name
const CollectionName = "view_logs"

// ViewLog view_logs 单条数据
type ViewLog struct {
	OID         primitive.ObjectID `bson:"_id"`
	Helper      `bson:",inline"`
	CreatedTime time.Time `bson:"created_time"`
}

// Helper for ViewLog
type Helper struct {
	RoomOID       primitive.ObjectID `bson:"_room_id"`
	RoomID        int64              `bson:"room_id"`
	RoomName      string             `bson:"room_name"`
	RoomCreatorID int64              `bson:"room_creator_id"`
	RoomCover     *string            `bson:"room_cover"`

	UserID int64  `bson:"user_id"`
	IP     string `bson:"ip"`

	UpdatedTime time.Time `bson:"updated_time"`
}

// Collection mongodb collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// Find 用户访问过的房间，时间倒序
func Find(userID int64, p, pageSize int64) (res []*ViewLog, pa goutil.Pagination, err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := Collection()

	filter := bson.M{"user_id": userID}
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return
	}
	pa = goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return
	}
	opt := pa.SetFindOptions(nil).SetSort(bson.M{"updated_time": -1})
	cur, err := collection.Find(ctx, filter, opt)
	if err != nil {
		return
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &res)
	return
}

// DeleteByRoomIDs 根据 roomIDs 来删除用户的访问记录
func DeleteByRoomIDs(userID int64, roomIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID, "room_id": bson.M{"$in": roomIDs}})
	return err
}

// DeleteAll 删除用户的所有访问记录
func DeleteAll(userID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	return err
}

// Update 更新用户访问房间的时间
func Update(userID int64, r *room.Room, ip string, when time.Time) error {
	if userID == r.CreatorID {
		// 主播访问自己房间不记录访问日志
		return nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().UpdateOne(ctx, bson.M{"_room_id": r.OID, "user_id": userID}, bson.M{
		"$set": Helper{
			RoomOID:       r.OID,
			RoomID:        r.RoomID,
			RoomName:      r.Name,
			RoomCover:     r.Cover,
			RoomCreatorID: r.CreatorID,
			UserID:        userID,
			IP:            ip,
			UpdatedTime:   when,
		},
		"$setOnInsert": bson.M{
			"created_time": when,
		}}, options.Update().SetUpsert(true))
	return err
}
