package livepk

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var testMatchingRoomIDs = []int64{114693474, 3192516}

func TestPickPKPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, false}}, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{
		"status": PKPoolStatusWaiting,
	})
	require.NoError(err)
	var p pkMatchingParam
	result, err := p.pickPKPool(&Pool{}, true)
	require.NoError(err)
	assert.False(result, "匹配池中无数据")

	require.NoError(createTestPoolData(PKTypeRandom, true, roomID30, roomID31))
	poolID30, err := FindWaitingPKByRoomID(roomID30)
	require.NoError(err)

	result, err = p.pickPKPool(poolID30, true)
	require.NoError(err)
	assert.True(result, "混合匹配池可以匹配到相同等级直播间")

	result, err = p.pickPKPool(poolID30, false)
	require.NoError(err)
	assert.True(result, "同级匹配池可以匹配到相同等级直播间")

	_, err = PoolCollection().UpdateOne(ctx,
		bson.M{"room_id": roomID31, "status": PKPoolStatusWaiting},
		bson.M{
			"$set": bson.M{
				"level": 999,
			},
		})
	require.NoError(err)
	poolID30, err = FindWaitingPKByRoomID(roomID30)
	require.NoError(err)
	require.NotNil(poolID30)

	result, err = p.pickPKPool(poolID30, true)
	require.NoError(err)
	assert.False(result, "混合匹配池不能匹配到创建 30s 内不同等级的匹配")

	result, err = p.pickPKPool(poolID30, false)
	require.NoError(err)
	assert.False(result, "同级匹配池不能匹配到不同等级的匹配")

	res, err := PoolCollection().UpdateOne(ctx,
		bson.M{"room_id": roomID31, "status": PKPoolStatusWaiting},
		bson.M{
			"$set": bson.M{
				"create_time": goutil.TimeNow().Add(-31 * time.Second).Unix(),
			},
		})
	require.NoError(err)
	require.NotEmpty(res.ModifiedCount)
	result, err = p.pickPKPool(poolID30, true)
	require.NoError(err)
	assert.True(result, "混合匹配池可以匹配到创建 30s 后不同等级的匹配")

	p.pools = p.pools[:0]
	mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{true, false}}, nil
	})
	require.NoError(createTestPoolData(PKTypeRandom, true, roomID30, roomID31))
	result, err = p.pickPKPool(poolID30, false)
	require.NoError(err)
	assert.False(result, "不匹配到有拉黑关系的主播")
}

func TestPKMatchingParam_blockExists(t *testing.T) {
	assert := assert.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, false}}, nil
	})
	defer cleanup()
	param := new(pkMatchingParam)
	exists := param.blockExists(1, 2)
	assert.False(exists)

	mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{true, false}}, nil
	})
	exists = param.blockExists(1, 2)
	assert.True(exists)

	mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, true}}, nil
	})
	exists = param.blockExists(1, 2)
	assert.True(exists)
}

func TestPickRandomLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{"status": PKPoolStatusWaiting})
	require.NoError(err)
	require.NoError(createTestPoolData(PKTypeRandom, true, testMatchingRoomIDs...))
	param := new(pkMatchingParam)
	m, err := param.pickRandomLive()
	require.NoError(err)
	assert.True(m)
	assert.Len(param.pools, 2)

	require.NoError(clearTestPoolData(testMatchingRoomIDs[0]))
	defer func() {
		assert.NoError(clearTestPoolData(testMatchingRoomIDs[1]))
	}()
	m, err = param.pickRandomLive()
	assert.NoError(err)
	assert.False(m)
}

func TestCheckRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testOpenRoomID  int64 = 114693474 // 开播直播间
		testCloseRoomID int64 = 18113499  // 关播的直播间
	)
	pool1 := &Pool{
		RoomID: testOpenRoomID,
		Status: PKPoolStatusWaiting,
	}
	pool2 := &Pool{
		RoomID: testCloseRoomID,
		Status: PKPoolStatusWaiting,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := PoolCollection().InsertMany(ctx, []interface{}{pool1, pool2}, nil)
	require.NoError(err)
	defer func() {
		delRes, err := PoolCollection().DeleteMany(ctx, bson.M{
			"_id": bson.M{"$in": res.InsertedIDs},
		})
		assert.NoError(err)
		assert.EqualValues(2, delRes.DeletedCount)
	}()

	// 匹配到的直播间中有已关播的直播间
	param := &pkMatchingParam{
		pools: []*Pool{pool1, pool2}, // 其中 pool2 为已关播直播间
	}
	ok, err := param.checkRoom()
	require.NoError(err)
	assert.False(ok)
	cur, err := PoolCollection().Find(ctx, bson.M{
		"_id": bson.M{"$in": res.InsertedIDs},
	})
	require.NoError(err)
	defer cur.Close(ctx)
	pools := make([]Pool, 0, 2)
	require.NoError(cur.All(ctx, &pools))
	for _, p := range pools {
		if p.RoomID == testCloseRoomID {
			assert.Equal(PKPoolStatusFail, p.Status)
		} else {
			assert.Equal(PKPoolStatusWaiting, p.Status)
		}
	}

	// 直播间全部开播
	pool2.RoomID = testMatchingRoomIDs[1] // 将 pool2 room_id 修改为开播房间号
	_, err = PoolCollection().UpdateOne(ctx, bson.M{
		"room_id": testCloseRoomID,
	}, bson.M{"$set": pool2}, options.Update().SetUpsert(true))
	require.NoError(err)
	ok, err = param.checkRoom()
	require.NoError(err)
	assert.True(ok)
}
