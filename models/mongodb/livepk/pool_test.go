package livepk

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	roomID30 int64 = 30
	roomID31 int64 = 31
)

func TestPoolTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Pool{}, "_id", "type", "level", "room_id", "creator_id", "to_room_id", "to_creator_id", "duration",
		"status", "start_time", "create_time", "modified_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Pool{}, "pk_pool_id", "type", "room_id", "creator_id", "to_room_id", "to_creator_id", "duration",
		"status")
}

func TestRoomLevel(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, RoomLevel(0))
	assert.Equal(0, RoomLevel(50000))
	assert.Equal(1, RoomLevel(50001))
}

func TestInsertRandomPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pool, err := InsertRandomPool(1, 1, 1)
	require.NoError(err)
	require.NotNil(pool)
	assert.NotEmpty(pool.OID.Hex())
	assert.Equal(PKPoolStatusWaiting, pool.Status)
	assert.Equal(1, pool.Level)
}

func createTestPoolData(poolType int, sameLevel bool, roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	inserts := make([]interface{}, 0, len(roomIDs))
	for i, roomID := range roomIDs {
		p := Pool{
			Type:         poolType,
			RoomID:       roomID,
			CreatorID:    10,
			Status:       PKPoolStatusWaiting,
			StartTime:    goutil.NewTimeUnixMilli(now),
			CreateTime:   now.Add(-5 * time.Second).Unix(),
			ModifiedTime: now.Unix(),
		}
		if !sameLevel {
			p.Level = i
		}
		inserts = append(inserts, p)
	}
	_, err := PoolCollection().InsertMany(ctx, inserts)
	if err != nil {
		return err
	}
	return nil
}

func clearTestPoolData(roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{
		"room_id": bson.M{"$in": roomIDs},
	})
	if err != nil {
		return err
	}
	return nil
}

func TestSetPoolRoomStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// init data
	require.NoError(clearTestPoolData(roomID30, roomID31))
	require.NoError(createTestPoolData(PKTypeRandom, true, roomID30, roomID31))

	pool, err := FindWaitingPKByRoomID(roomID30)
	assert.NoError(err)
	assert.Equal(PKPoolStatusWaiting, pool.Status)
	pool, err = FindWaitingPKByRoomID(roomID31)
	assert.NoError(err)
	assert.Equal(PKPoolStatusWaiting, pool.Status)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err = SetPoolRoomStatus(ctx, PKPoolStatusSuccess, [2]int64{roomID30, roomID31})
	assert.NoError(err)

	pool, err = FindWaitingPKByRoomID(roomID30)
	assert.NoError(err)
	assert.Nil(pool)
	pool, err = FindWaitingPKByRoomID(roomID31)
	assert.NoError(err)
	assert.Nil(pool)

	_, err = AddInvitationToPool(roomID30, 10, roomID31, 10, time.Second.Milliseconds())
	require.NoError(err)
	pool, err = FindWaitingPKByRoomID(roomID31)
	require.NoError(err)
	require.NotNil(pool)
	assert.Equal(PKPoolStatusWaiting, pool.Status)
	assert.Equal(PKTypeInvitation, pool.Type)
	assert.EqualValues(roomID31, pool.ToRoomID)
}

func TestFindRandomPKWaitingLives(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// init data
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{"status": PKPoolStatusWaiting})
	require.NoError(err)
	require.NoError(createTestPoolData(PKTypeRandom, true, roomID30, roomID31))
	pools, err := FindRandomPKWaitingLives()
	require.NoError(err)
	assert.GreaterOrEqual(len(pools), 2)

	require.NoError(clearTestPoolData(roomID30))
	defer func() {
		assert.NoError(clearTestPoolData(roomID31))
	}()
	pools, err = FindRandomPKWaitingLives()
	require.NoError(err)
	assert.GreaterOrEqual(len(pools), 1)
}

func TestSampleOneWaitingPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// init data
	require.NoError(clearTestPoolData(roomID30))
	filter := bson.M{"room_id": bson.M{"$in": []int64{roomID30, -11}}}
	pool, err := SampleOneWaitingPool(filter)
	require.NoError(err)
	assert.Nil(pool)

	require.NoError(createTestPoolData(PKTypeRandom, true, roomID30))
	pool, err = SampleOneWaitingPool(filter)
	require.NoError(err)
	assert.NotNil(pool)
}

func TestFindPKPoolByOIDAndSetWaitPoolStatusByOID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testRoomID := int64(2022021104)
	var pk LivePK
	err := PoolCollection().FindOneAndUpdate(ctx, bson.M{"room_id": testRoomID},
		bson.M{"$set": Pool{
			RoomID: testRoomID,
			Status: PKPoolStatusWaiting,
		}}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&pk)
	require.NoError(err)

	result, err := FindPKPoolByOID(pk.OID)
	require.NoError(err)
	require.NotNil(result)

	ok, err := SetWaitPoolStatusByOID(pk.OID, PKPoolStatusCancel)
	require.NoError(err)
	assert.True(ok)

	result, err = FindPKPoolByOID(pk.OID)
	require.NoError(err)
	require.NotNil(result)
	assert.Equal(PKPoolStatusCancel, result.Status)

	ok, err = SetWaitPoolStatusByOID(pk.OID, PKPoolStatusFail)
	require.NoError(err)
	assert.False(ok)
}

func TestWaitingRemainDuration(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	pool := &Pool{StartTime: goutil.NewTimeUnixMilli(now)}

	// 指定 PK
	pool.Type = PKTypeInvitation
	now = now.Add(time.Second)
	remainDuration := pool.WaitingRemainDuration()
	assert.EqualValues(goutil.Milliseconds(PKInvitationDuration-time.Second), remainDuration)
	now = now.Add(PKInvitationDuration)
	remainDuration = pool.WaitingRemainDuration()
	assert.Zero(remainDuration)

	// 随机 PK
	pool.Type = PKTypeRandom
	remainDuration = pool.WaitingRemainDuration()
	assert.EqualValues(goutil.Milliseconds(PKMatchDuration-PKInvitationDuration-time.Second), remainDuration)
	now = now.Add(PKMatchDuration)
	remainDuration = pool.WaitingRemainDuration()
	assert.Zero(remainDuration)
}

func TestFindPool(t *testing.T) {
	require := require.New(t)

	pool, err := InsertRandomPool(1, 1, 0)
	require.NoError(err)
	require.NotNil(pool)
	pool, err = FindPool(bson.M{"type": PKTypeRandom})
	require.NoError(err)
	require.NotNil(pool)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = PoolCollection().DeleteMany(ctx, bson.M{"type": PKTypeRandom})
	require.NoError(err)
	pool, err = FindPool(bson.M{"type": PKTypeRandom})
	require.NoError(err)
	require.Nil(pool)
}

func TestCancelPKInvitation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{"room_id": roomID18113499})
	require.NoError(err)
	res, err := PoolCollection().InsertOne(ctx, &Pool{
		Type:   PKTypeInvitation,
		RoomID: roomID18113499,
		Status: PKPoolStatusWaiting,
	})
	require.NoError(err)

	pool, err := CancelPKInvitation(roomID18113499)
	require.NoError(err)
	require.NotNil(pool)
	assert.Equal(res.InsertedID.(primitive.ObjectID), pool.OID)

	pool, err = CancelPKInvitation(11111)
	require.NoError(err)
	assert.Nil(pool)
}

func TestNewPKInvitationFailedPayload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pool := Pool{RoomID: 10, ToRoomID: 11}

	payload := pool.NewPKInvitationFailedPayload(liveim.EventPKInviteRefuse)
	require.NotNil(payload)
	assert.Equal(pool.RoomID, payload.RoomID)

	payload = pool.NewPKInvitationFailedPayload(liveim.EventPKInviteCancel)
	require.NotNil(payload)
	assert.Equal(pool.ToRoomID, payload.RoomID)

	assert.PanicsWithValue("广播消息类型错误", func() {
		pool.NewPKInvitationFailedPayload("error im event")
	})
}
