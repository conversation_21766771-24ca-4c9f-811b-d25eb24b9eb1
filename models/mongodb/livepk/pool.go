package livepk

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 匹配池匹配状态
const (
	PKPoolStatusFail    = iota - 1 // 匹配失败
	PKPoolStatusWaiting            // 匹配等待
	PKPoolStatusCancel             // 匹配取消
	PKPoolStatusSuccess            // 匹配成功
	PKPoolStatusRefuse             // 匹配拒绝
)

const (
	// PKMatchDuration 匹配等待时长
	PKMatchDuration = time.Minute
	// PKInvitationDuration 指定 PK 邀请等待时长
	PKInvitationDuration = 10 * time.Second
)

// OptionalFightingDurations 指定 PK 打榜可选时长, 单位分钟
var OptionalFightingDurations = []int64{5, 10, 20, 30}

const roomLevelRevenueThreshold = 50000 // 主播等级划分消费阈值，单位钻

// Pool PK 匹配池
type Pool struct {
	OID         primitive.ObjectID   `bson:"_id,omitempty" json:"pk_pool_id"`                        // ID
	Type        int                  `bson:"type" json:"type"`                                       // PK 类型
	Level       int                  `bson:"level" json:"-"`                                         // 直播间划分的等级
	RoomID      int64                `bson:"room_id" json:"room_id"`                                 // 房间 ID
	CreatorID   int64                `bson:"creator_id" json:"creator_id"`                           // 房主 ID
	ToRoomID    int64                `bson:"to_room_id,omitempty" json:"to_room_id,omitempty"`       // 指定 PK 被邀请方房间 ID
	ToCreatorID int64                `bson:"to_creator_id,omitempty" json:"to_creator_id,omitempty"` // 指定 PK 被邀请方房主 ID
	Duration    int64                `bson:"duration,omitempty" json:"duration,omitempty"`           // 指定 PK 时长, 单位毫秒
	Status      int                  `bson:"status" json:"status"`                                   // 当前匹配状态
	StartTime   goutil.TimeUnixMilli `bson:"start_time" json:"-"`                                    // 匹配开始时间

	CreateTime   int64 `bson:"create_time" json:"-"`
	ModifiedTime int64 `bson:"modified_time" json:"-"`
}

// PoolCollection mongo collection
func PoolCollection() *mongo.Collection {
	return service.MongoDB.Collection("live_pk_pool")
}

// RoomLevel room pk level
func RoomLevel(revenue int64) int {
	if revenue > roomLevelRevenueThreshold {
		return 1
	}
	return 0
}

// InsertRandomPool insert random pool
func InsertRandomPool(roomID, creatorID int64, level int) (*Pool, error) {
	now := goutil.TimeNow()
	pool := &Pool{
		Type:         PKTypeRandom,
		Level:        level,
		RoomID:       roomID,
		CreatorID:    creatorID,
		Status:       PKPoolStatusWaiting,
		StartTime:    goutil.NewTimeUnixMilli(now),
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	insertRes, err := PoolCollection().InsertOne(ctx, pool)
	if err != nil {
		return nil, err
	}
	var ok bool
	pool.OID, ok = insertRes.InsertedID.(primitive.ObjectID)
	if !ok {
		return nil, errors.New("PK 匹配 ID 解析错误")
	}
	return pool, nil
}

// AddInvitationToPool 发起指定 PK 匹配
func AddInvitationToPool(roomID, creatorID, toRoomID, toCreatorID, duration int64) (*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := goutil.TimeNow()
	pool := &Pool{
		Type:         PKTypeInvitation,
		RoomID:       roomID,
		CreatorID:    creatorID,
		Status:       PKPoolStatusWaiting,
		ToRoomID:     toRoomID,
		ToCreatorID:  toCreatorID,
		Duration:     duration,
		StartTime:    goutil.NewTimeUnixMilli(now),
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	res, err := PoolCollection().InsertOne(ctx, pool)
	if err != nil {
		return nil, err
	}
	pool.OID = res.InsertedID.(primitive.ObjectID)
	return pool, nil
}

// FindWaitingPKByRoomID 根据房间号, 查询当前正在等待 PK 的直播间
func FindWaitingPKByRoomID(roomID int64) (*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pool := new(Pool)
	err := PoolCollection().FindOne(ctx,
		bson.M{
			"$or": []bson.M{
				{"room_id": roomID},
				{"to_room_id": roomID},
			},
			"status": PKPoolStatusWaiting,
		},
	).Decode(pool)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return pool, nil
}

// FindRandomPKWaitingLives 查询正在等待随机 PK 匹配的直播间
func FindRandomPKWaitingLives() ([]*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cursor, err := PoolCollection().Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{
			"type":        bson.M{"$ne": PKTypeInvitation}, // TODO: 兼容没有 type 的 PK 数据，后面按 PKTypeRandom 类型查询
			"status":      PKPoolStatusWaiting,
			"create_time": bson.M{"$lt": goutil.TimeNow().Add(-2 * time.Second).Unix()}, // 确保主播刚提交的匹配，不会被立即匹配到
		}},
		bson.M{"$sample": bson.M{"size": 2}},
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	pools := make([]*Pool, 0, 2)
	if err := cursor.All(ctx, &pools); err != nil {
		return nil, err
	}
	return pools, nil
}

// SampleOneWaitingPool 随机获取一个等待 PK 的直播间
func SampleOneWaitingPool(filter interface{}) (*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cursor, err := PoolCollection().Aggregate(ctx, bson.A{
		bson.M{"$match": filter},
		bson.M{"$sample": bson.M{"size": 1}},
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var pools []*Pool
	if err := cursor.All(ctx, &pools); err != nil {
		return nil, err
	}
	if len(pools) == 0 {
		return nil, nil
	}
	return pools[0], nil
}

// SetPoolRoomStatus 修改 PK 状态
func SetPoolRoomStatus(ctx context.Context, status int, roomIDs [2]int64) error {
	_, err := PoolCollection().UpdateMany(ctx,
		bson.M{
			"room_id": bson.M{"$in": roomIDs},
			"status":  PKPoolStatusWaiting,
		},
		bson.M{
			"$set": bson.M{
				"status":        status,
				"modified_time": goutil.TimeNow().Unix(),
			},
		},
	)
	if err != nil {
		return err
	}
	return nil
}

// FindPKPoolByOID 根据 OID, 查询 PK 记录
func FindPKPoolByOID(OID primitive.ObjectID) (*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pool := new(Pool)
	err := PoolCollection().FindOne(ctx,
		bson.M{
			"_id": OID,
		},
	).Decode(pool)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return pool, nil
}

// SetWaitPoolStatusByOID 根据 OID 修改匹配中的 PK 匹配状态
func SetWaitPoolStatusByOID(matchOID primitive.ObjectID, updateStatus int) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	result, err := PoolCollection().UpdateOne(ctx,
		bson.M{
			"_id":    matchOID,
			"status": PKPoolStatusWaiting,
		},
		bson.M{
			"$set": bson.M{
				"status":        updateStatus,
				"modified_time": goutil.TimeNow().Unix(),
			},
		},
	)
	if err != nil {
		return false, err
	}
	return result.ModifiedCount == 1, nil
}

// WaitingRemainDuration 指定 PK 邀请等待期倒计时
func (p *Pool) WaitingRemainDuration() int64 {
	var remainDuration int64
	switch p.Type {
	case PKTypeRandom:
		remainDuration = goutil.Milliseconds(PKMatchDuration) -
			int64(goutil.NewTimeUnixMilli(goutil.TimeNow())-p.StartTime)
	case PKTypeInvitation:
		remainDuration = goutil.Milliseconds(PKInvitationDuration) -
			int64(goutil.NewTimeUnixMilli(goutil.TimeNow())-p.StartTime)
	}
	if remainDuration < 0 {
		remainDuration = 0
	}
	return remainDuration
}

// FindPool 查询匹配记录
func FindPool(filter interface{}) (*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pool := new(Pool)
	err := PoolCollection().FindOne(ctx, filter).Decode(pool)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return pool, nil
}

// CancelPKInvitation 取消指定 PK 邀请
func CancelPKInvitation(roomID int64) (*Pool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pool := new(Pool)
	err := PoolCollection().FindOneAndUpdate(ctx,
		bson.M{
			"type":    PKTypeInvitation,
			"room_id": roomID,
			"status":  PKPoolStatusWaiting,
		},
		bson.M{
			"$set": bson.M{
				"status":        PKPoolStatusCancel,
				"modified_time": goutil.TimeNow().Unix(),
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(pool)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return pool, nil
}

// PKInvitationPKInfo 指定 PK 邀请匹配相关信息
type PKInvitationPKInfo struct {
	MatchID    string `json:"match_id"`
	FromRoomID int64  `json:"from_room_id"`
	ToRoomID   int64  `json:"to_room_id"`
	CreateTime int64  `json:"create_time"`
}

// PKInvitationFailedPayload 指定 PK 邀请失败广播消息载体
type PKInvitationFailedPayload struct {
	Type   string              `json:"type"`
	Event  string              `json:"event"`
	RoomID int64               `json:"room_id"`
	PK     *PKInvitationPKInfo `json:"pk"`
}

// NewPKInvitationFailedPayload 创建匹配失败广播消息内容
func (p *Pool) NewPKInvitationFailedPayload(event string) *PKInvitationFailedPayload {
	var roomID int64
	switch event {
	case liveim.EventPKInviteRefuse:
		roomID = p.RoomID
	case liveim.EventPKInviteCancel:
		roomID = p.ToRoomID
	default:
		panic("广播消息类型错误")
	}
	payload := &PKInvitationFailedPayload{
		Type:   liveim.TypePK,
		Event:  event,
		RoomID: roomID,
		PK: &PKInvitationPKInfo{
			MatchID:    p.OID.Hex(),
			FromRoomID: p.RoomID,
			ToRoomID:   p.ToRoomID,
			CreateTime: p.CreateTime,
		},
	}
	return payload
}
