package livepk

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type pkMatchDelayMessage struct {
	MatchID string `json:"match_id"`
	Count   int    `json:"count"` // 匹配的次数, 从 0 开始

	poolOID primitive.ObjectID
}

// DelayPKMatchingStart 匹配开始消息-生产者
func DelayPKMatchingStart(pool *Pool) {
	key := keys.DelayKeyPKMatchingStart1.Format(pool.RoomID)
	if err := service.DatabusDelayPubSend(key, pkMatchDelayMessage{MatchID: pool.OID.Hex(), Count: 0}); err != nil {
		logger.Error(err)
		// PASS
	}
}

// PKMatchingStartOperator 开始匹配-消费者
func PKMatchingStartOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !strings.HasPrefix(m.Key, "pk/matching/start/") {
			return
		}
		var v pkMatchDelayMessage
		err := json.Unmarshal(m.Value, &v)
		if err != nil {
			logger.Error(err)
			return
		}
		v.poolOID, err = primitive.ObjectIDFromHex(v.MatchID)
		if err != nil {
			logger.WithField("pool_id", v.MatchID).Error(err)
			return
		}
		pool, err := FindPKPoolByOID(v.poolOID)
		if err != nil {
			logger.WithField("pool_id", v.MatchID).Error(err)
			return
		}
		// 可能存在数据库同步延迟的情况，导致这里查询不到，本次异常退出后不会影响后续匹配
		if pool == nil {
			logger.Errorf("pk pool %s not found", v.MatchID)
			return
		}
		if pool.Status != PKPoolStatusWaiting {
			return
		}
		var delayAgainDuration = 5
		if v.Count*delayAgainDuration <= 30 { // 30 秒内只匹配同等级直播间
			matchSuccess, err := pool.MatchPKPool(false)
			if err != nil {
				logger.WithField("room_id", pool.RoomID).Error(err)
			}
			if matchSuccess {
				return
			}
		} else if v.Count*delayAgainDuration <= 60 { // 30 - 60 秒内混合匹配池中匹配
			matchSuccess, err := pool.MatchPKPool(true)
			if err != nil {
				logger.WithField("room_id", pool.RoomID).Error(err)
			}
			if matchSuccess {
				return
			}
		} else { // 超时
			return
		}
		// 未匹配到，重新进入匹配队列
		key := keys.DelayKeyPKMatchingStart1.Format(pool.RoomID)
		if err := service.DatabusSendDelay(key, pkMatchDelayMessage{MatchID: pool.OID.Hex(), Count: v.Count + 1},
			goutil.TimeNow().Add(time.Duration(delayAgainDuration)*time.Second)); err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

// DelayPKMatchTimeout 匹配超时延时消息-生产者
func DelayPKMatchTimeout(pool *Pool) {
	key := keys.DelayKeyPKMatchingTimeout1.Format(pool.RoomID)

	sendTime := time.Unix(pool.CreateTime, 0)
	switch pool.Type {
	case PKTypeRandom:
		sendTime = sendTime.Add(PKMatchDuration)
	case PKTypeInvitation:
		// WORKAROUND: 比客户端实际显示多一秒的用户操作时间，防止在最后一秒同意或拒绝邀请时报错
		sendTime = sendTime.Add(PKInvitationDuration).Add(time.Second)
	default:
		panic(fmt.Errorf("unsupported pk type: %d", pool.Type))
	}
	if err := service.DatabusSendDelay(key, pkMatchDelayMessage{MatchID: pool.OID.Hex()}, sendTime); err != nil {
		logger.WithField("pool_id", pool.OID).Error(err)
		// PASS
	}
}

type pkMatchTimeoutMsg struct {
	Type   string       `json:"type"`
	Event  string       `json:"event"`
	RoomID int64        `json:"room_id"`
	PK     *pkMatchInfo `json:"pk,omitempty"`
}

type pkMatchInfo struct {
	MatchID     string `json:"match_id"`
	MatchStatus int    `json:"match_status"`
	FromRoomID  int64  `json:"from_room_id,omitempty"`
	ToRoomID    int64  `json:"to_room_id,omitempty"`
	CreateTime  int64  `json:"create_time"`
}

// DelayPKMatchingTimeoutOperator 匹配超时-消费者
func DelayPKMatchingTimeoutOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !strings.HasPrefix(m.Key, "pk/matching/timeout/") {
			return
		}
		var v pkMatchDelayMessage
		err := json.Unmarshal(m.Value, &v)
		if err != nil {
			logger.Error(err)
			return
		}
		v.poolOID, err = primitive.ObjectIDFromHex(v.MatchID)
		if err != nil {
			logger.WithField("pool_id", v.MatchID).Error(err)
			return
		}
		pool, err := FindPKPoolByOID(v.poolOID)
		if err != nil {
			logger.WithField("pool_id", v.MatchID).Error(err)
			return
		}
		if pool == nil {
			logger.Errorf("pk pool %s not found", v.MatchID)
			return
		}
		if pool.Status != PKPoolStatusWaiting {
			return
		}
		ok, err := SetWaitPoolStatusByOID(pool.OID, PKPoolStatusFail)
		if err != nil {
			logger.WithField("pool_id", v.MatchID).Error(err)
			return
		}
		if !ok {
			// 乐观锁更新失败，说明 pk 匹配状态已被改变，提前结束执行
			logger.Warnf("waiting pool %s not found", v.MatchID)
			return
		}
		switch pool.Type {
		case PKTypeRandom:
			pool.sendRandomPKTimeout()
		case PKTypeInvitation:
			pool.sendInvitePKTimeout()
		}
	}
}

func (p *Pool) sendRandomPKTimeout() *pkMatchTimeoutMsg {
	err := room.UnsetRoomPKStatus([]int64{p.RoomID})
	if err != nil {
		logger.WithField("pool_id", p.OID).Error(err)
		// PASS
	}
	notify := pkMatchTimeoutMsg{
		Type:   liveim.TypePK,
		Event:  liveim.EventPKMatchFail,
		RoomID: p.RoomID,
		PK: &pkMatchInfo{
			MatchID:     p.OID.Hex(),
			MatchStatus: PKPoolStatusFail,
			CreateTime:  p.CreateTime,
		},
	}
	if err := userapi.Broadcast(p.RoomID, notify); err != nil {
		logger.WithField("pool_id", p.OID).Error(err)
		// PASS
	}
	return &notify
}

func (p *Pool) sendInvitePKTimeout() []*userapi.BroadcastElem {
	elems := make([]*userapi.BroadcastElem, 2)
	// 发起方
	pk := &pkMatchInfo{
		MatchID:     p.OID.Hex(),
		MatchStatus: PKPoolStatusFail,
		FromRoomID:  p.RoomID,
		ToRoomID:    p.ToRoomID,
		CreateTime:  p.CreateTime,
	}

	elems[0] = &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: p.RoomID,
		UserID: p.CreatorID,
		Payload: pkMatchTimeoutMsg{
			Type:   liveim.TypePK,
			Event:  liveim.EventPKInviteTimeout,
			RoomID: p.RoomID,
			PK:     pk,
		},
	}
	// 被邀请方
	elems[1] = &userapi.BroadcastElem{
		Type:   liveim.IMMessageTypeNormal,
		RoomID: p.ToRoomID,
		UserID: p.ToCreatorID,
		Payload: pkMatchTimeoutMsg{
			Type:   liveim.TypePK,
			Event:  liveim.EventPKInviteTimeout,
			RoomID: p.ToRoomID,
			PK:     pk,
		},
	}
	if err := userapi.BroadcastMany(elems); err != nil {
		logger.WithField("pool_id", p.OID).Error(err)
		// PASS
	}
	return elems
}

type finishPKMessage struct {
	PKID string `json:"pk_id"`

	pkOID primitive.ObjectID
}

// DelayPKFightingFinish PK 正常结束-生产者
func DelayPKFightingFinish(pk *LivePK) {
	key := keys.DelayKeyPKFightingFinish1.Format(pk.OID.Hex())
	sendTime := pk.StartTime.ToTime().Add(time.Duration(pk.FightingDuration) * time.Millisecond)
	err := service.DatabusSendDelay(key, finishPKMessage{
		PKID: pk.OID.Hex(),
	}, sendTime)
	if err != nil {
		logger.WithField("pk_id", pk.OID).Error(err)
		// PASS
	}
}

// DelayPKFightingFinishOperator PK 打榜结束-消费者
func DelayPKFightingFinishOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !strings.HasPrefix(m.Key, "pk/fighting/finish/") {
			return
		}

		var v finishPKMessage
		err := json.Unmarshal(m.Value, &v)
		if err != nil {
			logger.Error(err)
			return
		}
		v.pkOID, err = primitive.ObjectIDFromHex(v.PKID)
		if err != nil {
			logger.WithField("pk_id", v.PKID).Error(err)
			return
		}
		pk, err := FindPKRecordByOIDAndStatus(v.pkOID, PKRecordStatusFighting)
		if err != nil {
			logger.WithField("pk_id", v.PKID).Error(err)
			return
		}
		if pk == nil {
			return
		}
		pk.WinnerRoomID = pk.winnerRoomIDByScore()
		ok, err := FightingToPunishment(v.pkOID, pk.WinnerRoomID)
		if err != nil {
			logger.WithField("pk_id", v.PKID).Errorf("finish pk error: %v", err)
			return
		}
		if !ok {
			logger.Warnf("fighting pk %s not found", pk.OID)
			return
		}
		// pk 加活动榜单
		goutil.Go(func() {
			if ActivityFunc != nil {
				ActivityFunc(*pk)
			}
		})
		pk.BroadcastPKFightingFinish()
	}
}

// PKFinishNotify pk finish notify
type PKFinishNotify struct {
	Type   string  `json:"type"`
	Event  string  `json:"event"`
	RoomID int64   `json:"room_id"`
	PK     *LivePK `json:"pk"`
}

// BroadcastPKFightingFinish pk finish broadcast
func (lp *LivePK) BroadcastPKFightingFinish() {
	msg := make([]*userapi.BroadcastElem, 2)
	if err := lp.FindTopFans(3); err != nil {
		logger.WithField("pk_id", lp.OID).Error(err)
		// PASS
	}
	if err := lp.FindRooms(); err != nil {
		logger.WithField("pk_id", lp.OID).Error(err)
		// PASS
	}
	for i := range lp.Fighters {
		msg[i] = &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: lp.Fighters[i].RoomID,
			Payload: &PKFinishNotify{
				Type:   liveim.TypePK,
				Event:  liveim.EventPKFinish,
				RoomID: lp.Fighters[i].RoomID,
				PK:     lp.buildRoomFinishPK(lp.Fighters[i].RoomID),
			},
		}
	}
	err := userapi.BroadcastMany(msg)
	if err != nil {
		logger.WithField("pk_id", lp.OID).Error(err)
		return
	}
}

// DelayPKPunishFinish 惩罚期正常结束-生产者
func DelayPKPunishFinish(pk *LivePK) {
	key := keys.DelayKeyPKPunishFinish1.Format(pk.OID.Hex())
	timeAt := pk.StartTime.ToTime().Add(time.Duration(pk.FightingDuration) * time.Millisecond).Add(PKPunishmentDuration)
	err := service.DatabusSendDelay(key, finishPKMessage{
		PKID: pk.OID.Hex(),
	}, timeAt)
	if err != nil {
		logger.WithField("pk_id", pk.OID).Error(err)
		// PASS
	}
}

// DelayPKPunishFinishOperator PK 惩罚期正常结束-消费者
func DelayPKPunishFinishOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !strings.HasPrefix(m.Key, "pk/punish/finish/") {
			return
		}
		var v finishPKMessage
		err := json.Unmarshal(m.Value, &v)
		if err != nil {
			logger.Error(err)
			return
		}
		v.pkOID, err = primitive.ObjectIDFromHex(v.PKID)
		if err != nil {
			logger.Error(err)
			return
		}
		pk, err := FindPKRecordByOIDAndStatus(v.pkOID, PKRecordStatusPunishment)
		if err != nil {
			logger.WithField("pk_id", v.PKID).Error(err)
			return
		}
		if pk == nil {
			return
		}

		ok, err := PunishmentToConnect(v.pkOID)
		if err != nil {
			logger.WithField("pk_id", v.PKID).Errorf("finish pk punish error: %v", err)
			return
		}
		if !ok {
			logger.Warnf("punish pk %s not found", pk.OID)
			return
		}
		err = room.UnsetRoomPKStatus(pk.PKRoomIDs())
		if err != nil {
			logger.WithField("pk_id", v.PKID).Error(err)
			// PASS
		}
		pk.BroadcastPKPunishFinish()
	}
}

// BroadcastPKPunishFinish pk finish broadcast
func (lp *LivePK) BroadcastPKPunishFinish() {
	if err := lp.FindRooms(); err != nil {
		logger.WithField("pk_id", lp.OID).Error(err)
		// PASS
	}
	msg := make([]*userapi.BroadcastElem, 2)
	for i := range lp.Fighters {
		msg[i] = &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: lp.Fighters[i].RoomID,
			Payload: &PKFinishNotify{
				Type:   liveim.TypePK,
				Event:  liveim.EventPKPunishFinish,
				RoomID: lp.Fighters[i].RoomID,
				PK:     lp.buildRoomPunishPKFinish(lp.Fighters[i].RoomID),
			},
		}
	}
	err := userapi.BroadcastMany(msg)
	if err != nil {
		logger.WithField("pk_id", lp.OID).Error(err)
		return
	}
}
