package livepk

import (
	"context"
	"errors"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActivityFunc 添加活动榜单函数
// TODO: 活动榜单迁移完成后使用 RPC
var ActivityFunc func(pk LivePK)

// PK 类型
const (
	PKTypeRandom     = iota // 随机 PK
	PKTypeInvitation        // 指定 PK
	PKTypeRank              // PK 排位赛
)

// PK 状态
const (
	PKRecordStatusMatching   = iota // PK 匹配中（注意: 该状态不存储在数据库中, 只用作返回当前的 PK 状态）
	PKRecordStatusFighting          // PK 进行中
	PKRecordStatusPunishment        // PK 惩罚期进行中
	PKRecordStatusConnect           // PK 普通连麦期
	PKRecordStatusFinished          // PK 结束
)

// PK 结果
const (
	PKResultLose = iota // PK 失败
	PKResultWin         // PK 胜利
	PKResultDraw        // PK 平局
)

// PK 时长
const (
	RandomPKFightingDuration = 5 * time.Minute // 随机 PK 默认时长
	PKPunishmentDuration     = 1 * time.Minute // PK 惩罚默认时长
)

// 逃跑惩罚
const (
	pkRunawayLimit              = 3              // 逃跑次数限制
	PKRunawayPunishmentDuration = 12 * time.Hour // 逃跑次数过多惩罚时间限制
)

// 高峰时段 PK 次数限制
const pkPeakLimit = 2

// Fighter 直播间两个主播 PK 详情
type Fighter struct {
	RoomID    int64 `bson:"room_id" json:"room_id"`       // 房间 ID
	CreatorID int64 `bson:"creator_id" json:"creator_id"` // 房主 ID
	GuildID   int64 `bson:"guild_id,omitempty" json:"-"`  // 主播公会 ID
	Score     int64 `bson:"score" json:"score"`           // PK 总积分
	FreeScore int64 `bson:"free_score" json:"-"`          // PK 免费积分（不暴露给用户）
	Mute      int   `bson:"mute" json:"-"`                // 是否静音对手

	Name            string            `bson:"-" json:"name"`               // 房间名称
	CreatorUsername string            `bson:"-" json:"creator_username"`   // 房主昵称
	CreatorIconURL  string            `bson:"-" json:"creator_iconurl"`    // 房主头像
	TopFans         []*roomsrank.Info `bson:"-" json:"top_fans,omitempty"` // PK 榜单收益信息

	Provider string `bson:"-" json:"-"` // 连麦服务提供商
	PushType string `bson:"-" json:"-"`
}

// LivePK PK 记录
type LivePK struct {
	OID              primitive.ObjectID   `bson:"_id,omitempty" json:"pk_id"`             // ID
	Type             int                  `bson:"type" json:"type"`                       // PK 类型
	Fighters         [2]*Fighter          `bson:"fighters" json:"fighters"`               // PK 主播详情
	FightingDuration int64                `bson:"fighting_duration" json:"-"`             // PK 打榜时长，单位毫秒
	Status           int                  `bson:"status" json:"status"`                   // PK 状态
	StartTime        goutil.TimeUnixMilli `bson:"start_time" json:"start_time,omitempty"` // PK 开始时间
	PunishStartTime  goutil.TimeUnixMilli `bson:"punish_start_time" json:"-"`             // PK 惩罚开始时间, PK 打榜结束时间
	ConnectStartTime goutil.TimeUnixMilli `bson:"connect_start_time" json:"-"`            // PK 普通连麦开始时间, PK 惩罚结束时间
	EndTime          goutil.TimeUnixMilli `bson:"end_time" json:"-"`                      // PK 结束时间
	WinnerRoomID     int64                `bson:"winner_room_id,omitempty" json:"-"`      // PK 胜利的直播间，平局值为 0
	RunawayRoomID    int64                `bson:"runaway_room_id,omitempty" json:"-"`     // PK 逃跑的直播间

	ConnectID       string `bson:"connect_id,omitempty" json:"-"`
	ConnectProvider string `bson:"connect_provider,omitempty" json:"-"`

	CreateTime   int64 `bson:"create_time" json:"-"`
	ModifiedTime int64 `bson:"modified_time" json:"-"`

	Result         *int  `bson:"-" json:"result,omitempty"`          // PK 胜负结果
	Mute           int   `bson:"-" json:"mute"`                      // 根据房间返回是否静音对手
	RemainDuration int64 `bson:"-" json:"remain_duration,omitempty"` // 距离 PK 结束或惩罚结束倒计时, 单位毫秒
	Duration       int64 `bson:"-" json:"duration,omitempty"`        // PK 匹配期、进行期、惩罚期总时长, 单位毫秒

	EffectURL    string `bson:"-" json:"effect_url,omitempty"`
	WebEffectURL string `bson:"-" json:"web_effect_url,omitempty"`

	CloseStatus *int `bson:"-" json:"close_status,omitempty"`
}

// PKCollection mongo collection
func PKCollection() *mongo.Collection {
	return service.MongoDB.Collection("live_pk_record")
}

// FindAndSetRoom Fighter 查找对应房间信息
func (f *Fighter) FindAndSetRoom() (*room.Room, error) {
	r, err := room.Find(f.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, err
	}
	if r == nil {
		return nil, errors.New("无法找到该聊天室")
	}
	f.Name = r.Name
	f.CreatorUsername = r.CreatorUsername
	f.CreatorIconURL = r.CreatorIconURL
	return r, nil
}

// ReorderFightersByRoomID 将指定房间的 Fighter 调整到 Fighters 首位
// Fighters 默认首位为当前直播间
func (lp *LivePK) ReorderFightersByRoomID(roomID int64) {
	lp.Fighters = lp.reorderFighters(roomID)
}

// winnerRoomIDByScore 根据 PK 战绩获取主播间 ID, 平局返回 0, 未考虑逃跑的情况
func (lp LivePK) winnerRoomIDByScore() int64 {
	if lp.Fighters[0].Score > lp.Fighters[1].Score {
		return lp.Fighters[0].RoomID
	} else if lp.Fighters[0].Score < lp.Fighters[1].Score {
		return lp.Fighters[1].RoomID
	}
	return 0
}

// PKRoomIDs 获取 PK 的直播间
func (lp LivePK) PKRoomIDs() []int64 {
	return []int64{lp.Fighters[0].RoomID, lp.Fighters[1].RoomID}
}

// Winner get pk winner
func (lp LivePK) Winner() *Fighter {
	if lp.WinnerRoomID == 0 {
		return nil
	}

	if lp.WinnerRoomID == lp.Fighters[0].RoomID {
		return lp.Fighters[0]
	}
	return lp.Fighters[1]
}

func (lp LivePK) buildRoomFinishPK(roomID int64) *LivePK {
	pk := &LivePK{
		OID:            lp.OID,
		Fighters:       lp.reorderFighters(roomID),
		Status:         PKRecordStatusPunishment,
		WinnerRoomID:   lp.WinnerRoomID,
		StartTime:      lp.StartTime,
		RemainDuration: PKPunishmentDuration.Milliseconds(),
		Duration:       PKPunishmentDuration.Milliseconds(),
		CloseStatus:    util.NewInt(PKRecordStatusFighting),
	}
	pk.buildMute()
	pk.buildRoomResult()
	pk.buildEffectByResult()
	return pk
}

func (lp LivePK) reorderFighters(roomID int64) [2]*Fighter {
	if lp.Fighters[0].RoomID == roomID {
		return lp.Fighters
	}
	return [2]*Fighter{lp.Fighters[1], lp.Fighters[0]}
}

// NOTICE: 需已保证当前直播间已排序到 fighters 首位
func (lp *LivePK) buildMute() {
	lp.Mute = lp.Fighters[0].Mute
}

// NOTICE: 需已保证当前直播间已排序到 fighters 首位
func (lp *LivePK) buildRoomResult() {
	if lp.WinnerRoomID == 0 {
		lp.Result = util.NewInt(PKResultDraw)
		return
	}

	if lp.Fighters[0].RoomID == lp.WinnerRoomID {
		lp.Result = util.NewInt(PKResultWin)
		return
	}
	lp.Result = util.NewInt(PKResultLose)
}

func (lp *LivePK) buildEffectByResult() {
	if lp.Result == nil {
		return
	}
	switch *lp.Result {
	case PKResultLose:
		lp.EffectURL = storage.ParseSchemeURLs(config.Conf.Params.PK.LoseEffect)
		lp.WebEffectURL = storage.ParseSchemeURLs(config.Conf.Params.PK.LoseWebEffect)
	case PKResultWin:
		lp.EffectURL = storage.ParseSchemeURLs(config.Conf.Params.PK.WinEffect)
		lp.WebEffectURL = storage.ParseSchemeURLs(config.Conf.Params.PK.WinWebEffect)
	case PKResultDraw:
		lp.EffectURL = storage.ParseSchemeURLs(config.Conf.Params.PK.DrawEffect)
		lp.WebEffectURL = storage.ParseSchemeURLs(config.Conf.Params.PK.DrawWebEffect)
	}
}

func (lp LivePK) buildRoomPunishPKFinish(roomID int64) *LivePK {
	pk := &LivePK{
		OID:         lp.OID,
		Fighters:    lp.reorderFighters(roomID),
		Status:      PKRecordStatusConnect,
		StartTime:   lp.StartTime,
		CloseStatus: util.NewInt(PKRecordStatusPunishment),
	}
	pk.buildMute()
	return pk
}

// FindTopFans 查询前三粉丝信息
// numFans top 粉丝数量
func (lp *LivePK) FindTopFans(numFans int64) error {
	cmds := make([]*redis.ZSliceCmd, 0, len(lp.Fighters))
	pipe := service.Redis.Pipeline()
	for _, fighter := range lp.Fighters {
		topFansKey := roomsrank.PKKey(fighter.RoomID, lp.OID)
		cmds = append(cmds, pipe.ZRevRangeWithScores(topFansKey, 0, numFans-1))
	}
	if _, err := pipe.Exec(); err != nil {
		return err
	}

	userIDs := make([]int64, 0, numFans*2)
	for i, cmd := range cmds {
		val := cmd.Val()
		for j := 0; j < len(val); j++ {
			userID, err := strconv.ParseInt(val[j].Member.(string), 10, 64)
			if err != nil {
				return err
			}
			userIDs = append(userIDs, userID)
			lp.Fighters[i].TopFans = append(lp.Fighters[i].TopFans, &roomsrank.Info{
				Rank:    int64(j),
				Revenue: int64(val[j].Score),
				Simple: &liveuser.Simple{
					UID: userID,
				},
			})
		}
	}
	if len(userIDs) == 0 {
		return nil
	}
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return err
	}
	for _, fighter := range lp.Fighters {
		for _, fans := range fighter.TopFans {
			user, ok := users[fans.UserID()]
			if !ok {
				continue
			}
			fans.Username = user.Username
			fans.IconURL = user.IconURL
		}
	}
	return nil
}

func (lp *LivePK) findFighterByRoomID(roomID int64) *Fighter {
	for _, f := range lp.Fighters {
		if f.RoomID == roomID {
			return f
		}
	}
	return nil
}

// FindRooms Fighters 查询房间和房主信息
func (lp *LivePK) FindRooms() error {
	rooms, err := room.List(bson.M{
		"room_id": bson.M{
			"$in": []int64{lp.Fighters[0].RoomID, lp.Fighters[1].RoomID},
		},
	}, nil, &room.FindOptions{FindCreator: true})
	if err != nil {
		return err
	}
	if len(rooms) != 2 {
		return errors.New("无法找到该聊天室")
	}
	for _, r := range rooms {
		f := lp.findFighterByRoomID(r.RoomID)
		f.GuildID = r.GuildID
		f.Name = r.Name
		f.CreatorUsername = r.CreatorUsername
		f.CreatorIconURL = r.CreatorIconURL
		f.Provider = r.Connect.Provider
		f.PushType = r.Connect.PushType
	}
	return nil
}

// MatchProvider 返回 PK 匹配的连麦服务提供商
// 优先级: agora 白名单 > bililive > bvclive
// 注: agora 白名单是在开播时 pre-create 中设置生效，同时在 PK 结束后会根据是否在声网白名单中恢复成声网或是 bvclive
// 双方 push type 均不为空时，返回 bililive;
// 其他情况默认返回 bvclive
func (lp *LivePK) MatchProvider() (connectID, provider string, err error) {
	// 优先使用 agora 白名单
	if lp.Fighters[0].Provider == room.ProviderAgora || lp.Fighters[1].Provider == room.ProviderAgora {
		return room.NewConnectID(), room.ProviderAgora, nil
	}

	if lp.Fighters[0].PushType != "" && lp.Fighters[1].PushType != "" {
		// PK 双方为支持 bililive 的 PC 或客户端时, 临时使用 bililive, 结束 PK 后还原为旧的 provider
		res, err := service.BiliLive.CreateChannel(&bililive.CreateChannelRequestParams{
			BusinessLabel: bililive.BusinessLabelPK,
		})
		if err != nil {
			return "", "", err
		}
		return strconv.FormatInt(res.ChannelID, 10), room.ProviderBililive, nil
	}
	return room.NewConnectID(), room.ProviderBvclive, nil
}

// CreatePKRecord 创建随机 PK
func CreatePKRecord(ctx context.Context, connectID, provider string, fighters [2]*Fighter) (*LivePK, error) {
	now := goutil.TimeNow()
	pk := &LivePK{
		Type:             PKTypeRandom,
		Fighters:         fighters,
		FightingDuration: RandomPKFightingDuration.Milliseconds(),
		Status:           PKRecordStatusFighting,
		StartTime:        goutil.NewTimeUnixMilli(now),
		PunishStartTime:  goutil.NewTimeUnixMilli(now.Add(RandomPKFightingDuration)),
		ConnectID:        connectID,
		ConnectProvider:  provider,
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
	}
	result, err := PKCollection().InsertOne(ctx, pk)
	if err != nil {
		return nil, err
	}
	pk.OID = result.InsertedID.(primitive.ObjectID)
	pk.BuildDuration()
	return pk, nil
}

// CreatePKInvitationRecord 创建指定 PK
// duration: 指定 PK 持续时长
func CreatePKInvitationRecord(ctx context.Context, duration time.Duration, connectID, provider string, fighters [2]*Fighter) (*LivePK, error) {
	now := goutil.TimeNow()
	pk := &LivePK{
		Type:             PKTypeInvitation,
		Fighters:         fighters,
		FightingDuration: duration.Milliseconds(),
		Status:           PKRecordStatusFighting,
		StartTime:        goutil.NewTimeUnixMilli(now),
		PunishStartTime:  goutil.NewTimeUnixMilli(now.Add(duration)),
		ConnectID:        connectID,
		ConnectProvider:  provider,
		CreateTime:       now.Unix(),
		ModifiedTime:     now.Unix(),
	}
	result, err := PKCollection().InsertOne(ctx, pk)
	if err != nil {
		return nil, err
	}
	pk.OID = result.InsertedID.(primitive.ObjectID)
	pk.BuildDuration()
	return pk, nil
}

// FindFightingPKRecordByRoomID 查询当前房间 PK 详情
func FindFightingPKRecordByRoomID(roomID int64) (*LivePK, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pk := new(LivePK)
	err := PKCollection().FindOne(ctx,
		bson.M{
			"fighters.room_id": roomID,
			"status":           PKRecordStatusFighting,
		},
	).Decode(&pk)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return pk, nil
}

// FindCurrentPKRecord 查询状态为进行期、惩罚期、普通连麦期的 PK
func FindCurrentPKRecord(roomID int64) (*LivePK, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pk := new(LivePK)
	err := PKCollection().FindOne(ctx,
		bson.M{
			"fighters.room_id": roomID,
			"status": bson.M{"$in": []int{
				PKRecordStatusFighting, PKRecordStatusPunishment, PKRecordStatusConnect},
			},
		},
	).Decode(&pk)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	pk.ReorderFightersByRoomID(roomID)
	pk.Mute = pk.Fighters[0].Mute
	pk.BuildDuration()
	if pk.Status != PKRecordStatusFighting {
		pk.buildRoomResult()
	}
	return pk, nil
}

// UpdateOngoingScore 更新直播间礼物数量
// PK 刷礼物，递增该直播间 PK 总分数
// score: 礼物总价格
// freeScore: 免费礼物总积分
func UpdateOngoingScore(ctx context.Context, roomID int64, score, freeScore int64) (*LivePK, error) {
	inc := bson.M{"fighters.$[item].score": score + freeScore}
	if freeScore != 0 {
		inc["fighters.$[item].free_score"] = freeScore
	}
	var lp LivePK
	err := PKCollection().FindOneAndUpdate(ctx,
		bson.M{
			"fighters.room_id": roomID,
			"status":           PKRecordStatusFighting,
		},
		bson.M{"$inc": inc},
		options.FindOneAndUpdate().SetArrayFilters(
			options.ArrayFilters{
				Filters: []interface{}{bson.M{"item.room_id": roomID}},
			},
		).SetReturnDocument(options.After),
	).Decode(&lp)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &lp, nil
}

// AddPKScore 增加 pk 值
func AddPKScore(r *room.Room, userID, score, freeScore int64) ([]*userapi.BroadcastElem, error) {
	if score == 0 && freeScore == 0 {
		return nil, nil
	}
	key := keys.KeyPKFighting1.Format(r.RoomID)
	err := service.Redis.Get(key).Err()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			err = nil
		}
		return nil, err
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lp, err := UpdateOngoingScore(ctx, r.RoomID, score, freeScore)
	if err != nil {
		return nil, err
	}
	if lp == nil {
		// 此房间没有在进行的 pk
		return nil, nil
	}
	addKey := roomsrank.PKKey(r.RoomID, lp.OID)
	pipe := service.Redis.TxPipeline()
	cmds := [2]*redis.ZSliceCmd{
		pipe.ZRevRangeWithScores(addKey, 0, 2),
	}
	pipe.ZIncrBy(addKey, float64(score+freeScore), strconv.FormatInt(userID, 10))
	cmds[1] = pipe.ZRevRangeWithScores(addKey, 0, 2)
	liveserviceredis.Expire(pipe, addKey, 3*24*time.Hour) // 每次都设置过期时间，pk 打榜（打榜期最长 30 分钟）结束后，最长保留 3 天
	_, err = pipe.Exec()
	if err != nil {
		return nil, err
	}
	beforeTopFans := cmds[0].Val()
	afterTopFans := cmds[1].Val()
	changed := len(beforeTopFans) != len(afterTopFans)
	if !changed {
		for i := range afterTopFans {
			if afterTopFans[i].Member != beforeTopFans[i].Member {
				changed = true
				break
			}
		}
	}

	userIDs := make([]int64, 0, 5) // 两个主播加受影响的主播的 top3
	for _, f := range lp.Fighters {
		if f.RoomID != r.RoomID {
			anotherRoom, err := room.FindOneSimple(bson.M{"room_id": f.RoomID},
				&room.FindOptions{DisableAll: true})
			if err != nil {
				return nil, err
			}
			if anotherRoom != nil {
				// anotherRoom 理论上不会是 nil
				f.Name = anotherRoom.Name
				userIDs = append(userIDs, anotherRoom.CreatorID)
			}
			continue
		}
		// 本房间的操作
		f.Name = r.Name
		userIDs = append(userIDs, f.CreatorID)
		if !changed {
			continue
		}
		f.TopFans = make([]*roomsrank.Info, 0, len(afterTopFans))
		for i := range afterTopFans {
			userID, err := strconv.ParseInt(afterTopFans[i].Member.(string), 10, 64)
			if err != nil {
				return nil, err
			}
			userIDs = append(userIDs, userID)
			info := roomsrank.Info{
				ID:      userID,
				Revenue: int64(afterTopFans[i].Score),
				Rank:    int64(i + 1),
			}
			f.TopFans = append(f.TopFans, &info)
		}
	}
	// 广播消息中的用户不需要 titles
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, err
	}
	for _, f := range lp.Fighters {
		u := userMap[f.CreatorID]
		if u != nil {
			f.CreatorIconURL = u.IconURL
			f.CreatorUsername = u.Username
		}
		if f.RoomID == r.RoomID {
			for i := range f.TopFans {
				f.TopFans[i].Simple = &liveuser.Simple{
					UID: f.TopFans[i].ID,
				}
				u = userMap[f.TopFans[i].ID]
				if u != nil {
					f.TopFans[i].Username = u.Username
					f.TopFans[i].IconURL = u.IconURL
				}
			}
		}
	}
	lp.BuildDuration()
	lp2 := CopyLivePK(lp, true)
	lp.Mute = lp.Fighters[0].Mute
	lp2.BuildDuration()
	lp2.Mute = lp2.Fighters[0].Mute
	res := []*userapi.BroadcastElem{
		{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  lp.Fighters[0].RoomID,
			Payload: pkNotify{Type: liveim.TypePK, Event: liveim.EventPKUpdate, PK: lp},
		},
		{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  lp2.Fighters[0].RoomID,
			Payload: pkNotify{Type: liveim.TypePK, Event: liveim.EventPKUpdate, PK: lp2},
		},
	}
	return res, nil
}

type pkNotify struct {
	Type  string  `json:"type"`
	Event string  `json:"event"`
	PK    *LivePK `json:"pk"`
}

// BuildDuration 计算时长
func (lp *LivePK) BuildDuration() {
	now := goutil.NewTimeUnixMilli(goutil.TimeNow())
	switch lp.Status {
	case PKRecordStatusFighting:
		lp.Duration = int64(lp.PunishStartTime - lp.StartTime)
		lp.RemainDuration = int64(lp.PunishStartTime - now)
	case PKRecordStatusPunishment:
		lp.Duration = int64(lp.ConnectStartTime - lp.PunishStartTime)
		lp.RemainDuration = int64(lp.ConnectStartTime - now)
	case PKRecordStatusConnect:
		lp.Duration = int64(now - lp.ConnectStartTime)
	}
	if lp.RemainDuration < 0 {
		lp.RemainDuration = 0
	}
}

// CopyLivePK 包含所有数据库数据的新的 LivePK
func CopyLivePK(lp *LivePK, swapFighters bool) *LivePK {
	res := &LivePK{
		OID:              lp.OID,
		Type:             lp.Type,
		Status:           lp.Status,
		StartTime:        lp.StartTime,
		PunishStartTime:  lp.PunishStartTime,
		ConnectStartTime: lp.ConnectStartTime,
		EndTime:          lp.EndTime,
		WinnerRoomID:     lp.WinnerRoomID,
		CreateTime:       lp.CreateTime,
		ModifiedTime:     lp.ModifiedTime,
	}
	if swapFighters {
		res.Fighters = [2]*Fighter{lp.Fighters[1], lp.Fighters[0]}
	} else {
		res.Fighters = [2]*Fighter{lp.Fighters[0], lp.Fighters[1]}
	}
	return res
}

// FindUnfinishedPKRecord 查找未结束的 PK
func FindUnfinishedPKRecord(roomID int64) (*LivePK, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pk := new(LivePK)
	err := PKCollection().FindOne(ctx,
		bson.M{
			"fighters.room_id": roomID,
			"status":           bson.M{"$in": []int{PKRecordStatusFighting, PKRecordStatusPunishment, PKRecordStatusConnect}},
		},
	).Decode(&pk)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return pk, nil
}

// SetFighterMute 静音对手，返回更新后的 LivePK 记录
// mute: true 静音对方; false 取消静音对方
func SetFighterMute(pkID primitive.ObjectID, roomID int64, mute bool) (*LivePK, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pk := new(LivePK)
	err := PKCollection().FindOneAndUpdate(ctx,
		bson.M{
			"_id": pkID,
		},
		bson.M{
			"$set": bson.M{"fighters.$[item].mute": goutil.BoolToInt(mute)},
		},
		options.FindOneAndUpdate().SetArrayFilters(
			options.ArrayFilters{
				Filters: []interface{}{
					bson.M{"item.room_id": roomID},
				},
			},
		).SetReturnDocument(options.After),
	).Decode(&pk)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return pk, nil
}

// FindOne 查询 PK
func FindOne(filter interface{}) (*LivePK, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pk := new(LivePK)
	err := PKCollection().FindOne(ctx,
		filter,
	).Decode(&pk)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}

	return pk, nil
}

// FindPKRecordByOIDAndStatus 根据 pkOID 和状态查询 PK
func FindPKRecordByOIDAndStatus(pkOID primitive.ObjectID, status int) (*LivePK, error) {
	return FindOne(bson.M{
		"_id":    pkOID,
		"status": status,
	})
}

// FightingToPunishment 根据 pkOID 修改 PK 进入惩罚期的状态
func FightingToPunishment(pkOID primitive.ObjectID, winnerRoomID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	res, err := PKCollection().UpdateOne(ctx,
		bson.M{
			"_id":    pkOID,
			"status": PKRecordStatusFighting,
		},
		bson.M{
			"$set": bson.M{
				"status":             PKRecordStatusPunishment,
				"winner_room_id":     winnerRoomID,
				"connect_start_time": goutil.NewTimeUnixMilli(now.Add(PKPunishmentDuration)),
				"modified_time":      now.Unix(),
			},
		},
	)
	if err != nil {
		return false, err
	}
	return res.ModifiedCount > 0, nil
}

// PunishmentToConnect 根据 pkOID 修改 PK 为普通连麦期状态
func PunishmentToConnect(pkOID primitive.ObjectID) (bool, error) {
	now := goutil.TimeNow()
	return UpdateOne(
		bson.M{
			"_id":    pkOID,
			"status": PKRecordStatusPunishment,
		},
		bson.M{
			"$set": bson.M{
				"status":        PKRecordStatusConnect,
				"modified_time": now.Unix(),
			},
		},
	)
}

// UpdateOne update one pk record
func UpdateOne(filter, set interface{}) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res, err := PKCollection().UpdateOne(ctx, filter, set)
	if err != nil {
		return false, nil
	}
	return res.ModifiedCount > 0, err
}

// CheckRunawayPunishment 检查直播间逃跑次数
func CheckRunawayPunishment(roomID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	count, err := PKCollection().CountDocuments(ctx, bson.M{
		"type":            PKTypeRandom,
		"runaway_room_id": roomID,
		"create_time":     bson.M{"$gte": now.Add(-2 * time.Hour).Unix()},
	})

	if err != nil {
		return false, err
	}
	return count < pkRunawayLimit, nil
}

// ClosePK close one pk record
func (lp LivePK) ClosePK() (bool, error) {
	now := goutil.TimeNow()
	update := bson.M{
		"status":        PKRecordStatusFinished,
		"end_time":      goutil.NewTimeUnixMilli(now),
		"modified_time": now.Unix(),
	}
	switch lp.Status {
	case PKRecordStatusFighting:
		update["runaway_room_id"] = lp.Fighters[0].RoomID
		update["winner_room_id"] = lp.Fighters[1].RoomID
	case PKRecordStatusPunishment:
		// PASS
	case PKRecordStatusConnect:
		// PASS
	default:
		return false, nil
	}

	if lp.Status != PKRecordStatusFighting {
		return UpdateOne(bson.M{"_id": lp.OID, "status": lp.Status}, bson.M{"$set": update})
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var newPK LivePK
	err := PKCollection().
		FindOneAndUpdate(
			ctx,
			bson.M{"_id": lp.OID, "status": lp.Status},
			bson.M{"$set": update},
			options.FindOneAndUpdate().SetReturnDocument(options.After),
		).
		Decode(&newPK)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}

	goutil.Go(func() {
		if ActivityFunc != nil {
			ActivityFunc(newPK)
		}
	})

	return true, nil
}

// AfterCloseRoom 关闭直播间后，需要执行的 PK 相关的操作
func AfterCloseRoom(roomID int64) error {
	pool, err := FindWaitingPKByRoomID(roomID)
	if err != nil {
		return err
	}
	if pool != nil {
		switch pool.Type {
		case PKTypeRandom:
			ok, err := SetWaitPoolStatusByOID(pool.OID, PKPoolStatusFail)
			if err != nil {
				return err
			}
			if !ok {
				logger.Warnf("pool %s status update failed", pool.OID)
				return nil
			}
		case PKTypeInvitation:
			var (
				status          int
				broadcastEvent  string
				broadcastUserID int64
			)
			switch roomID {
			case pool.RoomID:
				status = PKPoolStatusCancel
				broadcastEvent = liveim.EventPKInviteCancel
				broadcastUserID = pool.ToCreatorID
			case pool.ToRoomID:
				status = PKPoolStatusRefuse
				broadcastEvent = liveim.EventPKInviteRefuse
				broadcastUserID = pool.CreatorID
			default:
				logger.Errorf("room %d close room, pool %s room_id or to_room_id error", roomID, pool.OID)
				return nil
			}
			ok, err := SetWaitPoolStatusByOID(pool.OID, status)
			if err != nil {
				return err
			}
			if !ok {
				logger.Warnf("pool %s status update failed", pool.OID)
				return nil
			}
			payload := pool.NewPKInvitationFailedPayload(broadcastEvent)
			err = userapi.BroadcastUser(payload.RoomID, broadcastUserID, payload)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		default:
			logger.Errorf("pool %s type error", pool.OID)
			return nil
		}
		err = room.UnsetRoomPKStatus([]int64{roomID})
		if err != nil {
			return err
		}
		return nil
	}
	pk, err := FindUnfinishedPKRecord(roomID)
	if err != nil {
		return err
	}
	if pk == nil {
		return nil
	}
	pk.ReorderFightersByRoomID(roomID)
	ok, err := pk.ClosePK()
	if err != nil {
		return err
	}
	if !ok {
		return nil
	}
	err = room.AfterPKConnectFinish(pk.PKRoomIDs())
	if err != nil {
		return err
	}
	// 仅随机 PK 处理冷静期
	if pk.Type == PKTypeRandom {
		pass, err := CheckRunawayPunishment(roomID)
		if err != nil {
			return err
		}
		if !pass {
			err = service.Redis.Set(keys.LockRoomPKEscapePunishment1.Format(roomID), 1, PKRunawayPunishmentDuration).Err()
			if err != nil {
				logger.WithField("pk_id", pk.OID).Error(err)
				// PASS
			}
		}
	}
	elems := pk.ClosePKNotifies(pk.Status)
	if len(elems) == 0 {
		return nil
	}
	err = userapi.BroadcastMany(elems)
	if err != nil {
		logger.WithField("pk_id", pk.OID).Error(err)
		// PASS
	}
	return nil
}

// isWithinPKPeakLimitTime 是否在 PK 高峰限制时段内
func isWithinPKPeakLimitTime() bool {
	now := goutil.TimeNow()
	nowClock := util.Clock{Hour: now.Hour(), Min: now.Minute(), Sec: now.Second()}
	startClock := util.ParseClock(config.Conf.Params.PK.PeakLimitStartTime)
	endClock := util.ParseClock(config.Conf.Params.PK.PeakLimitEndTime)
	return nowClock.GreaterOrEqual(startClock) && nowClock.LessOrEqual(endClock)
}

// ExceedPKPeakLimit 直播间是否超过了高峰时段 PK 次数限制
// isNoLimitInvitation 表示是否由白名单内直播间发起邀请，随机 PK 传入 false
func ExceedPKPeakLimit(roomID int64, isNoLimitInvitation bool) (bool, error) {
	if ok := isWithinPKPeakLimitTime(); !ok {
		return false, nil
	}

	// 由白名单直播间发起的邀请，不校验 PK 次数
	if isNoLimitInvitation {
		return false, nil
	}

	key := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	count, err := service.Redis.ZScore(key, strconv.FormatInt(roomID, 10)).Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return false, nil
		}
		return false, err
	}
	return count >= pkPeakLimit, nil
}

// IncrPKPeakCount 增加高峰时段连通 PK 次数
func IncrPKPeakCount(pipe redis.Pipeliner, pk *LivePK, fromRoomID *int64) {
	if ok := isWithinPKPeakLimitTime(); !ok {
		return
	}

	pkConfig, err := params.FindPK()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 白名单内直播间发起邀请时，双方不增加 PK 计数
	if pk.Type == PKTypeInvitation && fromRoomID != nil && pkConfig.IsNoLimitPKRoomID(*fromRoomID) {
		return
	}

	now := goutil.TimeNow()
	key := keys.KeyRoomPKPeakLimit1.Format(now.Format(util.TimeFormatYMDWithNoSpace))
	for _, f := range pk.Fighters {
		// 白名单内直播间作为被邀请方或随机匹配时，只有白名单直播间不增加 PK 计数
		if pkConfig.IsNoLimitPKRoomID(f.RoomID) {
			continue
		}
		pipe.ZIncrBy(key, 1, strconv.FormatInt(f.RoomID, 10))
	}
	liveserviceredis.ExpireAt(pipe, key, now.Add(24*time.Hour))
}

// OngoingPKRoomIDs 从房间号集合中获取正在 PK 流程中的房间号
func OngoingPKRoomIDs(roomIDs []int64) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	roomIDMap := make(map[int64]struct{}, len(roomIDs))
	for _, roomID := range roomIDs {
		roomIDMap[roomID] = struct{}{}
	}

	// 匹配中的房间号
	poolCur, err := PoolCollection().Find(ctx,
		bson.M{
			"$or":    bson.A{bson.M{"room_id": bson.M{"$in": roomIDs}}, bson.M{"to_room_id": bson.M{"$in": roomIDs}}},
			"status": PKPoolStatusWaiting,
		},
		options.Find().SetProjection(bson.M{"room_id": 1, "to_room_id": 1}),
	)
	if err != nil {
		return nil, err
	}
	defer poolCur.Close(ctx)
	var pools []*Pool
	err = poolCur.All(ctx, &pools)
	if err != nil {
		return nil, err
	}
	waitingRoomIDs := make([]int64, 0, len(pools))
	for _, pool := range pools {
		if _, ok := roomIDMap[pool.RoomID]; ok {
			waitingRoomIDs = append(waitingRoomIDs, pool.RoomID)
		}
		if _, ok := roomIDMap[pool.ToRoomID]; ok {
			waitingRoomIDs = append(waitingRoomIDs, pool.ToRoomID)
		}
	}

	// PK 中的房间号
	pkCur, err := PKCollection().Find(ctx,
		bson.M{
			"fighters.room_id": bson.M{"$in": roomIDs},
			"status":           bson.M{"$in": []int{PKRecordStatusFighting, PKRecordStatusPunishment, PKRecordStatusConnect}},
		},
		options.Find().SetProjection(bson.M{"fighters": 1}),
	)
	if err != nil {
		return nil, err
	}
	defer pkCur.Close(ctx)
	var pk []*LivePK
	err = pkCur.All(ctx, &pk)
	if err != nil {
		return nil, err
	}

	pkRoomIDs := make([]int64, 0, len(pools))
	for _, p := range pk {
		for _, f := range p.Fighters {
			if _, ok := roomIDMap[f.RoomID]; ok {
				pkRoomIDs = append(pkRoomIDs, f.RoomID)
			}
		}
	}
	return append(waitingRoomIDs, pkRoomIDs...), nil
}
