package livepk

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
)

// ClosePKMessage close pk message
type ClosePKMessage struct {
	Type   string         `json:"type"`
	Event  string         `json:"event"`
	RoomID int64          `json:"room_id"`
	PK     *closePKNotify `json:"pk"`
}

const (
	statusForcedCloseFalse = iota // 结束 PK 的发起方
	statusForcedCloseTrue
)

type closePKNotify struct {
	PKID         primitive.ObjectID `json:"pk_id"`
	CloseStatus  int                `json:"close_status"`
	ForcedClose  int                `json:"forced_close"`
	Result       *int               `json:"result,omitempty"`
	EffectURL    string             `json:"effect_url,omitempty"`
	WebEffectURL string             `json:"web_effect_url,omitempty"`
}

func (message *ClosePKMessage) newBroadcastElem() *userapi.BroadcastElem {
	return &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  message.RoomID,
		Payload: message,
	}
}

// CloseOngoingPKNotifies 在进行期退出 PK 时直播间的消息
// REVIEW: 和 closeAfterOngoingPKNotifies 方法合并
// NOTICE: Fighters 需保证主动发起方在第一位
func (lp LivePK) closeOngoingPKNotifies() []*userapi.BroadcastElem {
	elems := make([]*userapi.BroadcastElem, 2)
	// 主动退出方直播间
	message := &ClosePKMessage{
		Type:   liveim.TypePK,
		Event:  liveim.EventClose,
		RoomID: lp.Fighters[0].RoomID,
		PK: &closePKNotify{
			PKID:         lp.OID,
			CloseStatus:  PKRecordStatusFighting,
			ForcedClose:  statusForcedCloseFalse,
			Result:       util.NewInt(PKResultLose),
			EffectURL:    storage.ParseSchemeURLs(config.Conf.Params.PK.LoseEffect),
			WebEffectURL: storage.ParseSchemeURLs(config.Conf.Params.PK.LoseWebEffect),
		},
	}
	elems[0] = message.newBroadcastElem()
	// 对手直播间
	message = &ClosePKMessage{
		Type:   liveim.TypePK,
		Event:  liveim.EventClose,
		RoomID: lp.Fighters[1].RoomID,
		PK: &closePKNotify{
			PKID:         lp.OID,
			CloseStatus:  PKRecordStatusFighting,
			ForcedClose:  statusForcedCloseTrue,
			Result:       util.NewInt(PKResultWin),
			EffectURL:    storage.ParseSchemeURLs(config.Conf.Params.PK.WinEffect),
			WebEffectURL: storage.ParseSchemeURLs(config.Conf.Params.PK.WinWebEffect),
		},
	}
	elems[1] = message.newBroadcastElem()
	return elems
}

// CloseAfterOngoingPKNotifies 在惩罚期/普通连麦期退出 PK 时直播间的消息
// NOTICE: livepk.Fighters 需保证主动发起方在第一位
func (lp LivePK) closeAfterOngoingPKNotifies(closeStatus int) []*userapi.BroadcastElem {
	elems := make([]*userapi.BroadcastElem, 2)
	// 主动退出方直播间
	message := &ClosePKMessage{
		Type:   liveim.TypePK,
		Event:  liveim.EventClose,
		RoomID: lp.Fighters[0].RoomID,
		PK: &closePKNotify{
			PKID:        lp.OID,
			CloseStatus: closeStatus,
			ForcedClose: statusForcedCloseFalse,
		},
	}
	elems[0] = message.newBroadcastElem()
	// 对手直播间
	message = &ClosePKMessage{
		Type:   liveim.TypePK,
		Event:  liveim.EventClose,
		RoomID: lp.Fighters[1].RoomID,
		PK: &closePKNotify{
			PKID:        lp.OID,
			CloseStatus: closeStatus,
			ForcedClose: statusForcedCloseTrue,
		},
	}
	elems[1] = message.newBroadcastElem()
	return elems
}

// ClosePKNotifies 构造关闭 PK 的消息
func (lp *LivePK) ClosePKNotifies(closeStatus int) []*userapi.BroadcastElem {
	switch closeStatus {
	case PKRecordStatusFighting:
		return lp.closeOngoingPKNotifies()
	case PKRecordStatusPunishment, PKRecordStatusConnect:
		return lp.closeAfterOngoingPKNotifies(closeStatus)
	}
	return nil
}
