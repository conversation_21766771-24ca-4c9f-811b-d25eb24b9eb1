package livepk

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestDatabusTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(pkMatchInfo{}, "match_id", "match_status", "from_room_id",
		"to_room_id", "create_time")
}

func TestDelayPKMatchingStart(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	poolOID := primitive.NewObjectIDFromTimestamp(goutil.TimeNow())
	testRoomID := int64(1)
	pool := &Pool{
		OID:    poolOID,
		RoomID: testRoomID,
	}
	DelayPKMatchingStart(pool)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyPKMatchingStart1.Format(testRoomID), m.Key)

	var message pkMatchDelayMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(poolOID.Hex(), message.MatchID)
}

func TestPKMatchingStartOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{
		"status": PKPoolStatusWaiting,
	})
	require.NoError(err)
	_, err = PKCollection().DeleteMany(ctx,
		bson.M{"fighters.room_id": bson.M{"$in": testMatchingRoomIDs}},
	)
	require.NoError(err)
	qm := pkMatchDelayMessage{
		MatchID: primitive.NewObjectIDFromTimestamp(goutil.TimeNow()).Hex(),
	}
	assert.NotPanics(func() {
		PKMatchingStartOperator()(&databus.Message{
			Key:   keys.DelayKeyPKMatchingStart1.Format(111),
			Value: json.RawMessage(tutil.SprintJSON(qm)),
		})
	})

	require.NoError(createTestPoolData(PKTypeRandom, true, testMatchingRoomIDs...))
	p, err := FindWaitingPKByRoomID(testMatchingRoomIDs[0])
	require.NoError(err)
	require.NotNil(p)
	qm = pkMatchDelayMessage{
		MatchID: p.OID.Hex(),
		Count:   6,
	}
	assert.NotPanics(func() {
		PKMatchingStartOperator()(&databus.Message{
			Key:   keys.DelayKeyPKMatchingStart1.Format(111),
			Value: json.RawMessage(tutil.SprintJSON(qm)),
		})
	})
	pk, err := FindCurrentPKRecord(testMatchingRoomIDs[0])
	require.NoError(err)
	assert.NotNil(pk)
}

func TestDelayPKMatchTimeout(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	poolOID := primitive.NewObjectIDFromTimestamp(goutil.TimeNow())
	testRoomID := int64(1)
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	pool := &Pool{
		OID:    poolOID,
		Type:   PKTypeRandom,
		RoomID: testRoomID,
	}
	DelayPKMatchTimeout(pool)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyPKMatchingTimeout1.Format(testRoomID), m.Key)

	var message pkMatchDelayMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(poolOID.Hex(), message.MatchID)
}

func TestDelayPKMatchingTimeoutOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(2022021101)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var p Pool
	err := PoolCollection().FindOneAndUpdate(ctx, bson.M{"room_id": roomID},
		bson.M{"$set": Pool{
			RoomID: roomID,
			Type:   PKTypeRandom,
			Status: PKPoolStatusWaiting,
		}}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&p)
	require.NoError(err)
	f := DelayPKMatchingTimeoutOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	qm := pkMatchDelayMessage{
		MatchID: p.OID.Hex(),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyPKMatchingTimeout1.Format(roomID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
	pool, err := FindPKPoolByOID(p.OID)
	require.NoError(err)
	require.NotNil(pool)
	assert.Equal(PKPoolStatusFail, pool.Status)
}

func TestSendRandomPKTimeout(t *testing.T) {
	assert := assert.New(t)

	p := Pool{
		Type:   PKTypeRandom,
		RoomID: 1,
	}
	res := p.sendRandomPKTimeout()
	assert.Equal(liveim.EventPKMatchFail, res.Event)
	assert.Equal(p.RoomID, res.RoomID)
}

func TestSendInvitePKTimeout(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := Pool{
		Type:        PKTypeInvitation,
		RoomID:      1,
		CreatorID:   1,
		ToRoomID:    2,
		ToCreatorID: 2,
	}
	res := p.sendInvitePKTimeout()
	require.Len(res, 2)
	assert.Equal(p.RoomID, res[0].RoomID)
	assert.Equal(p.CreatorID, res[0].UserID)
	assert.Equal(p.ToRoomID, res[1].RoomID)
	assert.Equal(p.ToCreatorID, res[1].UserID)
}

func TestDelayPKFightingFinish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pkOID := primitive.NewObjectIDFromTimestamp(goutil.TimeNow())
	require.NoError(service.Redis.Del(keys.DelayKeyPKFightingFinish1.Format(pkOID.Hex())).Err())
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	pool := &LivePK{
		OID: pkOID,
	}
	DelayPKFightingFinish(pool)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyPKFightingFinish1.Format(pkOID.Hex()), m.Key)

	var message finishPKMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(pkOID.Hex(), message.PKID)
}

func TestDelayPKFightingFinishOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(22489473)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var pk LivePK
	err := PKCollection().FindOneAndUpdate(ctx, bson.M{"fighters.room_id": roomID},
		bson.M{"$set": LivePK{
			OID:    primitive.ObjectID{},
			Status: PKRecordStatusFighting,
			Fighters: [2]*Fighter{
				{RoomID: roomID},
				{RoomID: 18113499},
			}}}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&pk)
	require.NoError(err)
	f := DelayPKFightingFinishOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	qm := finishPKMessage{
		PKID: pk.OID.Hex(),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyPKFightingFinish1.Format(roomID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
	pool, err := FindPKRecordByOIDAndStatus(pk.OID, PKRecordStatusPunishment)
	require.NoError(err)
	assert.NotNil(pool)
}

func TestDelayPKPunishFinish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pkOID := primitive.NewObjectIDFromTimestamp(goutil.TimeNow())
	require.NoError(service.Redis.Del(keys.DelayKeyPKPunishFinish1.Format(pkOID.Hex())).Err())
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()
	pool := &LivePK{
		OID: pkOID,
	}
	DelayPKPunishFinish(pool)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyPKPunishFinish1.Format(pkOID.Hex()), m.Key)

	var message finishPKMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(pkOID.Hex(), message.PKID)
}

func TestDelayPKPunishFinishOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(2022021103)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var pk LivePK
	err := PKCollection().FindOneAndUpdate(ctx, bson.M{"fighters.room_id": roomID},
		bson.M{"$set": LivePK{
			OID:    primitive.ObjectID{},
			Status: PKRecordStatusPunishment,
			Fighters: [2]*Fighter{
				{RoomID: roomID},
				{RoomID: 1},
			}}}, options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&pk)
	require.NoError(err)
	f := DelayPKPunishFinishOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	qm := finishPKMessage{
		PKID: pk.OID.Hex(),
	}
	f(&databus.Message{
		Key:   keys.DelayKeyPKPunishFinish1.Format(roomID),
		Value: json.RawMessage(tutil.SprintJSON(qm)),
	})
	pool, err := FindPKRecordByOIDAndStatus(pk.OID, PKRecordStatusConnect)
	require.NoError(err)
	assert.NotNil(pool)
}
