package livepk

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/bililive"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	roomIDInsert100 int64 = 100
	roomIDInsert200 int64 = 200
	roomID18113499  int64 = 18113499
	roomID223344    int64 = 223344
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LivePK{}, "_id", "type", "fighters", "fighting_duration", "status", "start_time", "punish_start_time",
		"connect_start_time", "end_time", "winner_room_id", "runaway_room_id", "connect_id", "connect_provider", "create_time", "modified_time")
	kc.Check(Fighter{}, "room_id", "creator_id", "guild_id", "score", "free_score", "mute")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LivePK{}, "pk_id", "type", "fighters", "status", "start_time", "result", "mute",
		"remain_duration", "duration", "effect_url", "web_effect_url", "close_status")
	kc.Check(Fighter{}, "room_id", "creator_id", "score", "name", "creator_username",
		"creator_iconurl", "top_fans")
}

func createTestPKData(roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	for _, roomID := range roomIDs {
		if _, err := CreatePKRecord(ctx, "", "", [2]*Fighter{
			{
				RoomID:    10101,
				CreatorID: 2,
			},
			{
				RoomID:    roomID,
				CreatorID: 1,
			},
		}); err != nil {
			return err
		}
	}
	return nil
}

func clearTestPKData(roomIDs ...int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PKCollection().DeleteMany(ctx, bson.M{
		"fighters.room_id": bson.M{"$in": roomIDs},
	})
	if err != nil {
		return err
	}
	return nil
}

func TestWinnerRoomIDByScore(t *testing.T) {
	assert := assert.New(t)

	pk := LivePK{
		Fighters: [2]*Fighter{{}, {}},
	}
	assert.Zero(pk.winnerRoomIDByScore())

	pk = LivePK{
		Fighters: [2]*Fighter{
			{
				RoomID: 1,
				Score:  1,
			},
			{
				RoomID: 2,
				Score:  2,
			},
		},
	}
	assert.EqualValues(2, pk.winnerRoomIDByScore())

	pk = LivePK{
		Fighters: [2]*Fighter{
			{
				RoomID: 2,
				Score:  2,
			},
			{
				RoomID: 1,
				Score:  1,
			},
		},
	}
	assert.EqualValues(2, pk.winnerRoomIDByScore())
}

func TestPKRoomIDs(t *testing.T) {
	assert := assert.New(t)

	pk := LivePK{
		Fighters: [2]*Fighter{
			{
				RoomID: 2,
			},
			{
				RoomID: 1,
			},
		},
	}
	assert.Equal([]int64{2, 1}, pk.PKRoomIDs())
}

func TestLivePKWinner(t *testing.T) {
	assert := assert.New(t)

	pk := LivePK{
		WinnerRoomID: 1,
		Fighters: [2]*Fighter{
			{
				RoomID: 2,
			},
			{
				RoomID: 1,
			},
		},
	}

	assert.Equal(&Fighter{RoomID: 1}, pk.Winner())

	pk.WinnerRoomID = 0
	assert.Nil(pk.Winner())
}

func TestReorderFighters(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pk := LivePK{
		Fighters: [2]*Fighter{
			{
				RoomID: 2,
			},
			{
				RoomID: 1,
			},
		},
	}

	fighters := pk.reorderFighters(1)
	require.Len(fighters, 2)
	assert.EqualValues(1, fighters[0].RoomID)
	assert.EqualValues(2, fighters[1].RoomID)

	fighters = pk.reorderFighters(2)
	require.Len(fighters, 2)
	assert.EqualValues(2, fighters[0].RoomID)
	assert.EqualValues(1, fighters[1].RoomID)
}

func TestBuildRoomResult(t *testing.T) {
	assert := assert.New(t)

	pk := LivePK{
		WinnerRoomID: 1,
		Fighters: [2]*Fighter{
			{
				RoomID: 2,
			},
			{
				RoomID: 1,
			},
		},
	}
	pk.buildRoomResult()
	assert.Equal(PKResultLose, *pk.Result)

	pk = LivePK{
		WinnerRoomID: 1,
		Fighters: [2]*Fighter{
			{
				RoomID: 1,
			},
			{
				RoomID: 2,
			},
		},
	}
	pk.buildRoomResult()
	assert.Equal(PKResultWin, *pk.Result)

	pk = LivePK{
		WinnerRoomID: 0,
		Fighters: [2]*Fighter{
			{
				RoomID: 1,
			},
			{
				RoomID: 2,
			},
		},
	}
	pk.buildRoomResult()
	assert.Equal(PKResultDraw, *pk.Result)
}

func findItem(fighters [2]*Fighter, roomID int64) *Fighter {
	for _, d := range fighters {
		if d.RoomID == roomID {
			return d
		}
	}
	return nil
}

func TestFindFightingPKRecordByRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(11)
	// init data
	require.NoError(createTestPKData(roomID))
	defer func() {
		assert.NoError(clearTestPKData(roomID))
	}()

	// before update room
	pk, err := FindFightingPKRecordByRoomID(roomID)
	require.NoError(err)
	beforeUpdateRoom := pk.findFighterByRoomID(roomID)

	// update
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lp, err := UpdateOngoingScore(ctx, roomID, 100, 10)
	require.NoError(err)
	require.NotNil(lp)

	// after update room
	pk, err = FindFightingPKRecordByRoomID(roomID)
	require.NoError(err)
	afterUpdateRoom := pk.findFighterByRoomID(roomID)
	assert.Equal(afterUpdateRoom.Score, beforeUpdateRoom.Score+110)
	assert.Equal(afterUpdateRoom.FreeScore, beforeUpdateRoom.FreeScore+10)
	assert.Equal(afterUpdateRoom, findItem(lp.Fighters, roomID))

	pk, err = FindFightingPKRecordByRoomID(11111)
	require.NoError(err)
	assert.Nil(pk)

	require.NoError(clearTestPKData(roomID))
	lp, err = UpdateOngoingScore(ctx, roomID, 100, 10)
	require.NoError(err)
	assert.Nil(lp)
}

func TestCreatePKRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	fighters := [2]*Fighter{
		{
			RoomID:    roomIDInsert100,
			CreatorID: 1,
			Score:     0,
		},
		{
			RoomID:    roomIDInsert200,
			CreatorID: 2,
			Score:     0,
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pk, err := CreatePKRecord(ctx, "", "", fighters)
	require.NoError(err)
	assert.NotNil(pk)
	assert.NotEmpty(pk.OID)
	assert.NotZero(pk.Duration, pk.RemainDuration)
}

func TestCreatePKInvitationRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pk, err := CreatePKInvitationRecord(ctx, time.Second, "", "", [2]*Fighter{
		{RoomID: 1, CreatorID: 2},
		{RoomID: 2, CreatorID: 1},
	})
	require.NoError(err)
	assert.NotNil(pk)
}

func TestFindTopFans(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// pk 助攻榜单 redis key
	// test_rooms/revenues/1000/7/62021590f0af33b2fcd342f4
	// test_rooms/revenues/1001/7/62021590f0af33b2fcd342f4
	oid, err := primitive.ObjectIDFromHex("62021590f0af33b2fcd342f4")
	require.NoError(err)
	pkKey1000 := roomsrank.PKKey(1000, oid)
	pkKey1001 := roomsrank.PKKey(1001, oid)
	// init test data
	pipe := service.Redis.Pipeline()
	pipe.Del(pkKey1000, pkKey1001)
	pipe.ZAdd(
		pkKey1000,
		&redis.Z{Score: 111111, Member: 1234},
		&redis.Z{Score: 111, Member: 3457114})
	pipe.ZAdd(
		pkKey1001,
		&redis.Z{Score: 9999, Member: 10000})
	_, err = pipe.Exec()
	require.NoError(err)

	pk := &LivePK{
		OID: oid,
		Fighters: [2]*Fighter{
			{
				RoomID: 1000,
			},
			{
				RoomID: 1001,
			},
		},
	}
	err = pk.FindTopFans(3)
	require.NoError(err)
	assert.Len(pk.Fighters[0].TopFans, 2)
	assert.Equal(int64(111111), pk.Fighters[0].TopFans[0].Revenue)
	assert.Equal(int64(1234), pk.Fighters[0].TopFans[0].UID)
	assert.Equal(int64(9999), pk.Fighters[1].TopFans[0].Revenue)
}

func TestAddPKScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(room.Room)
	// 不加积分的情况
	res, err := AddPKScore(r, 12, 0, 0)
	require.NoError(err)
	assert.Nil(res)
	// pk 缓存不存在的情况
	key := keys.KeyPKFighting1.Format(r.RoomID)
	require.NoError(service.Redis.Del(key).Err())
	res, err = AddPKScore(r, 12, 100, 0)
	require.NoError(err)
	assert.Nil(res)
	// pk 不存在的情况
	require.NoError(service.Redis.Set(key, "test", 10*time.Second).Err())
	res, err = AddPKScore(r, 12, 100, 0)
	require.NoError(err)
	assert.Nil(res)

	lp, err := FindFightingPKRecordByRoomID(roomIDInsert100)
	require.NoError(err)
	require.NotNil(lp)
	key = keys.KeyPKFighting1.Format(roomIDInsert100)
	require.NoError(service.Redis.Set(key, lp.OID.Hex(), 10*time.Second).Err())
	// 榜单长度不同的助攻榜
	topFanKey := roomsrank.PKKey(roomIDInsert100, lp.OID)
	require.NoError(service.Redis.Del(topFanKey).Err())
	r.RoomID = roomIDInsert100
	r.Name = "test"
	res, err = AddPKScore(r, 12, 0, 100)
	require.NoError(err)
	require.Len(res, 2)
	var pns [2]pkNotify
	require.IsType(pns[0], res[0].Payload)
	pns = [2]pkNotify{
		res[0].Payload.(pkNotify),
		res[1].Payload.(pkNotify),
	}
	if res[0].RoomID != roomIDInsert100 {
		pns[0], pns[1] = pns[1], pns[0]
	}
	assert.Equal(lp.Fighters[0].Score+100, pns[0].PK.Fighters[0].Score)
	assert.NotEmpty(pns[0].PK.Fighters[0].TopFans)
	assert.Equal(pns[0].PK.Fighters[0], pns[1].PK.Fighters[1])

	// 榜单长度相同的助攻榜
	require.NoError(service.Redis.ZAdd(topFanKey,
		&redis.Z{Member: 13, Score: 101},
		&redis.Z{Member: 14, Score: 102},
		&redis.Z{Member: 15, Score: 103},
	).Err())
	res, err = AddPKScore(r, 12, 100, 0)
	require.NoError(err)
	require.Len(res, 2)
	pns = [2]pkNotify{
		res[0].Payload.(pkNotify),
		res[1].Payload.(pkNotify),
	}
	if res[0].RoomID != roomIDInsert100 {
		pns[0], pns[1] = pns[1], pns[0]
	}
	assert.Equal(lp.Fighters[0].Score+200, pns[0].PK.Fighters[0].Score)
	assert.NotEmpty(pns[0].PK.Fighters[0].TopFans)
}

func TestFindRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	pk := LivePK{
		Fighters: [2]*Fighter{
			{
				RoomID:    roomID18113499,
				CreatorID: 1,
				Score:     0,
			},
			{
				RoomID:    roomID223344,
				CreatorID: 2,
				Score:     0,
			},
		},
	}
	require.NoError(pk.FindRooms())
	assert.NotEmpty(pk.Fighters[0].Name)
	assert.NotEmpty(pk.Fighters[0].CreatorUsername)
	assert.NotEmpty(pk.Fighters[0].CreatorIconURL)
	assert.NotEmpty(pk.Fighters[1].CreatorIconURL)

	r, err := room.Find(pk.Fighters[0].RoomID)
	require.NoError(err)
	assert.Equal(r.Connect.Provider, pk.Fighters[0].Provider)
	assert.Equal(r.GuildID, pk.Fighters[0].GuildID)
	r, err = room.Find(pk.Fighters[1].RoomID)
	require.NoError(err)
	assert.Equal(r.Connect.Provider, pk.Fighters[1].Provider)
	assert.Equal(r.GuildID, pk.Fighters[1].GuildID)

	pk.Fighters[0].RoomID = 23123
	err = pk.FindRooms()
	assert.EqualError(err, "无法找到该聊天室")
}

func TestBuildDuration(t *testing.T) {
	assert := assert.New(t)

	var now time.Time
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	lp := LivePK{
		Status:           PKRecordStatusFighting,
		StartTime:        1000,
		PunishStartTime:  3000,
		ConnectStartTime: 4000,
	}
	now = time.Unix(2, 0)
	lp.BuildDuration()
	assert.EqualValues(2000, lp.Duration)
	assert.EqualValues(1000, lp.RemainDuration)

	now = time.Unix(5, 0)
	lp.Status = PKRecordStatusPunishment
	lp.BuildDuration()
	assert.EqualValues(1000, lp.Duration)
	assert.Zero(lp.RemainDuration)

	now = time.Unix(6, 0)
	lp.Status = PKRecordStatusConnect
	lp.BuildDuration()
	assert.EqualValues(2000, lp.Duration)
	assert.Zero(lp.RemainDuration)
}

func TestFindAndSetRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	fighter := Fighter{
		RoomID: roomID18113499,
	}
	room1, err := fighter.FindAndSetRoom()
	require.NoError(err)
	assert.NotNil(room1)
	assert.Equal(room1.Name, fighter.Name)
}

func TestFindCurrentPKRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(clearTestPKData(roomID18113499))
	require.NoError(createTestPKData(roomID18113499))

	pk, err := FindCurrentPKRecord(roomID18113499)
	require.NoError(err)
	assert.NotNil(pk)
	assert.Equal(roomID18113499, pk.Fighters[0].RoomID)
	assert.Equal(pk.Fighters[0].Mute, pk.Mute)
	assert.NotEmpty(pk.Duration, pk.RemainDuration)
	assert.Nil(pk.Result)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = PKCollection().UpdateOne(ctx,
		bson.M{"fighters.room_id": roomID18113499},
		bson.M{"$set": bson.M{"status": PKRecordStatusPunishment}})
	require.NoError(err)
	pk, err = FindCurrentPKRecord(roomID18113499)
	require.NoError(err)
	assert.NotNil(pk)
	assert.Equal(roomID18113499, pk.Fighters[0].RoomID)
	assert.Equal(pk.Fighters[0].Mute, pk.Mute)
	assert.NotEmpty(pk.Duration, pk.RemainDuration)
	assert.NotNil(pk.Result)
}

func TestReorderFightersByRoomID(t *testing.T) {
	assert := assert.New(t)

	lpk := new(LivePK)
	lpk.Fighters = [2]*Fighter{
		{RoomID: 10},
		{RoomID: 19},
	}
	lpk.ReorderFightersByRoomID(19)
	assert.EqualValues(19, lpk.Fighters[0].RoomID)

	lpk.Fighters = [2]*Fighter{
		{RoomID: 10},
		{RoomID: 19},
	}
	lpk.ReorderFightersByRoomID(10)
	assert.EqualValues(10, lpk.Fighters[0].RoomID)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var roomID int64 = 1
	// init data
	require.NoError(clearTestPKData(roomID))
	require.NoError(createTestPKData(roomID))

	pk, err := FindOne(bson.M{"fighters.room_id": roomID})
	require.NoError(err)
	assert.NotNil(pk)

	pk, err = FindOne(bson.M{"fighters.room_id": -999999999})
	require.NoError(err)
	assert.Nil(pk)
}

func TestFinishFightingPKAndPunishPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var roomID int64 = 1
	// init data
	require.NoError(createTestPKData(roomID))
	defer func() {
		assert.NoError(clearTestPKData(roomID))
	}()

	pk, err := FindFightingPKRecordByRoomID(roomID)
	require.NoError(err)
	require.NotNil(pk)
	ok, err := FightingToPunishment(pk.OID, 1)
	require.NoError(err)
	assert.True(ok)

	ok, err = PunishmentToConnect(pk.OID)
	require.NoError(err)
	assert.True(ok)
}

func TestSetFighterMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := PKCollection().InsertOne(ctx, LivePK{
		Fighters: [2]*Fighter{
			{
				RoomID:    roomIDInsert100,
				CreatorID: 2,
				Mute:      0,
			},
			{
				RoomID:    roomIDInsert200,
				CreatorID: 1,
				Mute:      0,
			},
		},
	})
	require.NoError(err)
	pkID := res.InsertedID.(primitive.ObjectID)
	defer func() {
		_, err := PKCollection().DeleteOne(ctx, bson.M{"_id": pkID})
		assert.NoError(err)
	}()

	pk, err := SetFighterMute(pkID, roomIDInsert100, true)
	require.NoError(err)
	for _, f := range pk.Fighters {
		if f.RoomID == roomIDInsert100 {
			assert.Equal(1, f.Mute)
		} else {
			assert.Equal(0, f.Mute)
		}
	}

	pk, err = SetFighterMute(pkID, roomIDInsert100, false)
	require.NoError(err)
	for _, f := range pk.Fighters {
		assert.Equal(0, f.Mute)
	}

	pk, err = SetFighterMute(primitive.NewObjectID(), roomIDInsert100, true)
	require.NoError(err)
	assert.Nil(pk)
}

func TestMatchProvider(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 返回 bvclive
	pk := &LivePK{
		Fighters: [2]*Fighter{
			{
				Provider: room.ProviderBvclive,
				RoomID:   1,
			},
			{
				Provider: room.ProviderBvclive,
				RoomID:   2,
			},
		},
	}
	key := keys.KeyRoomsEnableAgora0.Format()
	require.NoError(service.Redis.Del(key).Err())
	// 返回 bvc
	connectID, provider, err := pk.MatchProvider()
	require.NoError(err)
	assert.Equal(room.ProviderBvclive, provider)
	assert.NotEmpty(connectID)

	// 返回 bililive
	pk.Fighters[0].PushType = "pc"
	pk.Fighters[1].PushType = "ios"
	bililive.SetMockResult(bililive.ActionCreateChannel, bililive.Channel{ChannelID: 123456})
	connectID, provider, err = pk.MatchProvider()
	require.NoError(err)
	assert.Equal(room.ProviderBililive, provider)
	assert.EqualValues("123456", connectID)

	// 返回 agora
	pk.Fighters[0].Provider = room.ProviderAgora
	connectID, provider, err = pk.MatchProvider()
	require.NoError(err)
	assert.Equal(room.ProviderAgora, provider)
	assert.NotEmpty(connectID)
}

func TestCheckRunawayPunishment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PKCollection().DeleteMany(ctx, bson.M{
		"runaway_room_id": roomID18113499,
	})
	require.NoError(err)

	runaway, err := CheckRunawayPunishment(roomID18113499)
	require.NoError(err)
	assert.True(runaway)

	// 指定 PK 不计入逃跑
	pks := make([]interface{}, 0, pkRunawayLimit)
	for i := 0; i < pkRunawayLimit; i++ {
		pks = append(pks, &LivePK{
			Type:          PKTypeInvitation,
			Status:        PKRecordStatusFinished,
			RunawayRoomID: roomID18113499,
			CreateTime:    goutil.TimeNow().Unix(),
		})
	}
	res, err := PKCollection().InsertMany(ctx, pks)
	require.NoError(err)
	require.Len(res.InsertedIDs, pkRunawayLimit)
	runaway, err = CheckRunawayPunishment(roomID18113499)
	require.NoError(err)
	assert.True(runaway)

	// 随机 PK 计入逃跑
	pks = make([]interface{}, 0, pkRunawayLimit)
	for i := 0; i < pkRunawayLimit; i++ {
		pks = append(pks, &LivePK{
			Type:          PKTypeRandom,
			Status:        PKRecordStatusFinished,
			RunawayRoomID: roomID18113499,
			CreateTime:    goutil.TimeNow().Unix(),
		})
	}
	res, err = PKCollection().InsertMany(ctx, pks)
	require.NoError(err)
	require.Len(res.InsertedIDs, pkRunawayLimit)
	runaway, err = CheckRunawayPunishment(roomID18113499)
	require.NoError(err)
	assert.False(runaway)
}

func TestClosePK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pk, err := CreatePKRecord(ctx, "", "", [2]*Fighter{
		{
			RoomID:    1,
			CreatorID: 2,
			Mute:      0,
		},
		{
			RoomID:    2,
			CreatorID: 1,
			Mute:      0,
		},
	})
	require.NoError(err)
	require.NotNil(pk)

	// 进行期结束 PK，若本房逃跑，则算对方胜利
	closepk, err := pk.ClosePK()
	require.NoError(err)
	require.True(closepk)
	err = PKCollection().FindOne(ctx, bson.M{"_id": pk.OID}).Decode(pk)
	require.NoError(err)
	assert.Equal(pk.Fighters[0].RoomID, pk.RunawayRoomID)
	assert.Equal(pk.Fighters[1].RoomID, pk.WinnerRoomID)
	assert.Equal(PKRecordStatusFinished, pk.Status)

	// 惩罚期结束 PK，若 PK 胜利方为对方直播间，本房此时结束 PK 不算逃跑
	res := PKCollection().FindOneAndUpdate(ctx, bson.M{"_id": pk.OID}, bson.M{
		"$set": bson.M{
			"status":         PKRecordStatusPunishment,
			"winner_room_id": pk.Fighters[1].RoomID, // 设置 PK 胜利方为对方直播间
		},
		"$unset": bson.M{
			"runaway_room_id": 0,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	require.NoError(res.Err())
	pk = new(LivePK)
	require.NoError(res.Decode(pk))
	closepk, err = pk.ClosePK()
	require.NoError(err)
	require.True(closepk)
	err = PKCollection().FindOne(ctx, bson.M{"_id": pk.OID}).Decode(pk)
	require.NoError(err)
	assert.Zero(pk.RunawayRoomID)
	assert.Equal(pk.Fighters[1].RoomID, pk.WinnerRoomID)
	assert.Equal(PKRecordStatusFinished, pk.Status)

	res = PKCollection().FindOneAndUpdate(ctx, bson.M{"_id": pk.OID}, bson.M{
		"$set": bson.M{
			"status":         PKRecordStatusPunishment,
			"winner_room_id": pk.Fighters[1].RoomID, // 设置 PK 胜利方为对方直播间
		},
		"$unset": bson.M{
			"runaway_room_id": 0,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	require.NoError(res.Err())
	pk = new(LivePK)
	require.NoError(res.Decode(pk))
	closepk, err = pk.ClosePK()
	require.NoError(err)
	require.True(closepk)
	err = PKCollection().FindOne(ctx, bson.M{"_id": pk.OID}).Decode(pk)
	require.NoError(err)
	assert.Zero(pk.RunawayRoomID)
	assert.Equal(pk.Fighters[1].RoomID, pk.WinnerRoomID)
	assert.Equal(PKRecordStatusFinished, pk.Status)

	// 连麦期结束 PK，无影响
	res = PKCollection().FindOneAndUpdate(ctx, bson.M{"_id": pk.OID}, bson.M{
		"$set": bson.M{
			"status": PKRecordStatusConnect,
		},
		"$unset": bson.M{
			"winner_room_id":  0,
			"runaway_room_id": 0,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	require.NoError(res.Err())
	pk = new(LivePK)
	require.NoError(res.Decode(pk))
	closepk, err = pk.ClosePK()
	require.NoError(err)
	require.True(closepk)
	err = PKCollection().FindOne(ctx, bson.M{"_id": pk.OID}).Decode(pk)
	require.NoError(err)
	assert.Empty(pk.WinnerRoomID)
	assert.Empty(pk.RunawayRoomID)
	assert.Equal(PKRecordStatusFinished, pk.Status)

	// 其他状态
	pk = &LivePK{Status: PKPoolStatusWaiting}
	closepk, err = pk.ClosePK()
	require.NoError(err)
	assert.False(closepk)
}

func TestAfterCloseRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := PoolCollection().DeleteMany(ctx, bson.M{"room_id": roomID18113499})
	require.NoError(err)
	_, err = InsertRandomPool(roomID18113499, 1, 0)
	require.NoError(err)
	r, err := room.Update(roomID18113499, bson.M{"status.pk": 1})
	require.NoError(err)
	require.NotNil(r)
	require.Equal(1, r.Status.PK)

	// 随机 PK 等待匹配中
	err = AfterCloseRoom(roomID18113499)
	require.NoError(err)
	err = PoolCollection().FindOne(ctx, bson.M{
		"room_id": roomID18113499,
		"status":  PKPoolStatusFail,
	}).Err()
	require.NoError(err)
	r, err = room.FindOne(bson.M{"room_id": roomID18113499}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	assert.Zero(r.Status.PK)

	// 指定 PK 等待中
	pool, err := AddInvitationToPool(roomID18113499, 10, 223344, 10, time.Second.Milliseconds())
	require.NoError(err)
	require.NotNil(pool)
	err = AfterCloseRoom(roomID18113499)
	require.NoError(err)
	p := new(Pool)
	require.NoError(PoolCollection().FindOne(ctx, bson.M{"_id": pool.OID}).Decode(p))
	assert.Equal(PKTypeInvitation, p.Type)
	assert.Equal(PKPoolStatusCancel, p.Status)

	// PK 进行中
	r, err = room.Update(roomID18113499, bson.M{"status.pk": 1})
	require.NoError(err)
	require.NotNil(r)
	require.Equal(1, r.Status.PK)
	punishmentKey := keys.LockRoomPKEscapePunishment1.Format(roomID18113499)
	require.NoError(service.Redis.Del(punishmentKey).Err())
	_, err = PKCollection().DeleteMany(ctx, bson.M{"runaway_room_id": roomID18113499})
	require.NoError(err)
	pks := make([]interface{}, 0, pkRunawayLimit)
	for i := 0; i < pkRunawayLimit; i++ {
		pks = append(pks, &LivePK{
			Status:        PKRecordStatusFinished,
			RunawayRoomID: roomID18113499,
			CreateTime:    goutil.TimeNow().Unix(),
		})
	}
	_, err = PKCollection().InsertMany(ctx, pks)
	require.NoError(err)
	_, err = PKCollection().DeleteMany(ctx, bson.M{"status": bson.M{
		"$in": []int{PKRecordStatusFighting, PKRecordStatusPunishment, PKRecordStatusConnect}},
	})
	require.NoError(err)
	pk, err := CreatePKRecord(ctx, "", "", [2]*Fighter{
		{
			RoomID:    roomID18113499,
			CreatorID: 2,
			Mute:      0,
		},
		{
			RoomID:    2,
			CreatorID: 1,
			Mute:      0,
		},
	})
	require.NoError(err)
	require.NotNil(pk)

	err = AfterCloseRoom(roomID18113499)
	require.NoError(err)
	err = PKCollection().FindOne(ctx, bson.M{"_id": pk.OID}).Decode(pk)
	require.NoError(err)
	assert.Equal(pk.Fighters[0].RoomID, pk.RunawayRoomID)
	assert.Equal(pk.Fighters[1].RoomID, pk.WinnerRoomID)
	assert.Equal(PKRecordStatusFinished, pk.Status)
	oldConnectID := r.Connect.ID
	r, err = room.FindOne(bson.M{"room_id": roomID18113499}, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	assert.NotEqual(oldConnectID, r.Connect.ID)
	assert.Zero(r.Status.PK)
	val, err := service.Redis.Get(punishmentKey).Result()
	require.NoError(err)
	assert.Equal("1", val)
}

func TestIsWithinPKPeakLimitTime(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	ok := isWithinPKPeakLimitTime()
	assert.False(ok)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 0, 0, 0, time.Local)
	})
	ok = isWithinPKPeakLimitTime()
	assert.True(ok)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 22, 1, 0, 0, time.Local)
	})
	ok = isWithinPKPeakLimitTime()
	assert.False(ok)
}

func TestExceedPKPeakLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.Redis.Del(key).Err())

	exceed, err := ExceedPKPeakLimit(1, false)
	require.NoError(err)
	assert.False(exceed)

	err = service.Redis.ZIncrBy(key, 2, "1").Err()
	require.NoError(err)
	exceed, err = ExceedPKPeakLimit(1, false)
	require.NoError(err)
	assert.True(exceed)

	exceed, err = ExceedPKPeakLimit(1, true)
	require.NoError(err)
	assert.False(exceed)
}

func TestIncrPKPeakCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.Redis.Del(key).Err())

	pipe := service.Redis.Pipeline()
	IncrPKPeakCount(pipe, &LivePK{Fighters: [2]*Fighter{{RoomID: 1}, {RoomID: 2}}}, nil)
	_, err := pipe.Exec()
	require.NoError(err)

	pipe = service.Redis.Pipeline()
	room1Cmd := pipe.ZScore(key, "1")
	room2Cmd := pipe.ZScore(key, "2")
	_, err = pipe.Exec()
	require.NoError(err)
	room1Count, _ := room1Cmd.Result()
	room2Count, _ := room2Cmd.Result()
	assert.EqualValues(1, room1Count)
	assert.EqualValues(1, room2Count)
}

// TestNoLimitPKRoom - 测试白名单中的直播间不受 PK 次数限制
func TestNoLimitPKRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 0, 0, 0, time.Local)
	})
	defer cancel()
	key := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.LRURedis.Del(keys.KeyParams1.Format(params.KeyPK)).Err())

	// 验证白名单房间不受热门时段次数限制
	roomID1, roomID2, noLimitRoomID := int64(1), int64(2), int64(3)
	noLimitRoomIDs := []int64{noLimitRoomID}
	// 写入配置
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := params.Collection().UpdateOne(ctx,
		bson.M{"key": params.KeyPK},
		bson.M{
			"$set": bson.M{
				"no_limit_room_ids": noLimitRoomIDs,
			}},
		options.Update().SetUpsert(true))
	require.NoError(err)

	// 验证白名单中的直播间不增加 PK 次数记录
	pipe := service.Redis.Pipeline()
	// 白名单直播间作为邀请方时，不增加双方 PK 次数
	IncrPKPeakCount(pipe, &LivePK{Type: PKTypeInvitation, Fighters: [2]*Fighter{{RoomID: roomID2}, {RoomID: noLimitRoomID}}}, &noLimitRoomID)
	// 白名单直播间作为被邀请方时，不增加白名单直播间 PK 次数
	IncrPKPeakCount(pipe, &LivePK{Type: PKTypeInvitation, Fighters: [2]*Fighter{{RoomID: roomID2}, {RoomID: noLimitRoomID}}}, &roomID2)
	// 随机匹配时，不增加白名单直播间 PK 次数
	IncrPKPeakCount(pipe, &LivePK{Type: PKTypeRandom, Fighters: [2]*Fighter{{RoomID: roomID1}, {RoomID: noLimitRoomID}}}, nil)
	_, err = pipe.Exec()
	require.NoError(err)

	pipe = service.Redis.Pipeline()

	room1Cmd := pipe.ZScore(key, strconv.FormatInt(roomID1, 10))
	room2Cmd := pipe.ZScore(key, strconv.FormatInt(roomID2, 10))
	noLimitRoomCmd := pipe.ZScore(key, strconv.FormatInt(noLimitRoomID, 10))
	_, _ = pipe.Exec()

	count, err := room1Cmd.Result()
	require.NoError(err)
	assert.EqualValues(1, count)
	count, err = room2Cmd.Result()
	require.NoError(err)
	assert.EqualValues(1, count)
	count, err = noLimitRoomCmd.Result()
	assert.True(serviceredis.IsRedisNil(err))
	assert.Zero(count)
}

func TestFilterPKRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := PoolCollection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": []int64{50000, 51000, 60000}}})
	require.NoError(err)
	_, err = PoolCollection().InsertMany(ctx, []interface{}{
		Pool{
			OID:      primitive.NewObjectID(),
			RoomID:   50000,
			ToRoomID: 50001,
			Status:   PKPoolStatusWaiting,
		},
		Pool{
			OID:      primitive.NewObjectID(),
			RoomID:   51000,
			ToRoomID: 51001,
			Status:   PKPoolStatusWaiting,
		},
	})
	require.NoError(err)

	_, err = PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": 60000})
	require.NoError(err)
	_, err = PKCollection().InsertMany(ctx, []interface{}{
		LivePK{
			OID:    primitive.NewObjectID(),
			Status: PKRecordStatusConnect,
			Fighters: [2]*Fighter{
				{
					RoomID: 60000,
				},
				{
					RoomID: 60001,
				},
			},
		},
	})
	require.NoError(err)

	filterRoomIDs, err := OngoingPKRoomIDs([]int64{50000, 50001, 51000, 51001, 60000, 99999})
	require.NoError(err)
	assert.Equal([]int64{50000, 50001, 51000, 51001, 60000}, filterRoomIDs)
}
