package livepk

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// matchMsgPayload 匹配成功发送消息体
type matchMsgPayload struct {
	Type   string  `json:"type"`
	Event  string  `json:"event"`
	RoomID int64   `json:"room_id"`
	LivePK *LivePK `json:"pk"`
}

type pkMatchingParam struct {
	pools []*Pool // 匹配 PK 的两个直播间
	pk    *LivePK
}

// MatchPKPool match pk pool
func (p *Pool) MatchPKPool(mixMatch bool) (bool, error) {
	param := new(pkMatchingParam)
	match, err := param.pickPKPool(p, mixMatch)
	if err != nil {
		return false, err
	}
	if !match {
		return false, nil
	}
	ok, err := param.checkRoom()
	if err != nil {
		return false, err
	}
	if !ok {
		return false, nil
	}
	err = param.createPK()
	if err != nil {
		return false, err
	}
	DelayPKFightingFinish(param.pk)
	DelayPKPunishFinish(param.pk)
	param.broadcast()
	return true, nil
}

func (param *pkMatchingParam) pickPKPool(p *Pool, mixMatch bool) (bool, error) {
	filter := bson.M{
		"type":    PKTypeRandom,
		"room_id": bson.M{"$ne": p.RoomID},
		"status":  PKPoolStatusWaiting,
	}
	if !mixMatch {
		// 只匹配相同等级的直播间, 相同等级池中匹配
		filter["level"] = p.Level
	} else {
		// 混合等级匹配池，只能匹配到:
		// 30s 中仍未匹配到对手的其他等级的直播间；
		// 相同等级直播间；
		filter["$or"] = bson.A{
			bson.M{"level": p.Level},
			bson.M{"create_time": bson.M{"$lt": goutil.TimeNow().Add(-30 * time.Second).Unix()}},
		}
	}
	var pp, tempPool *Pool // pick pool, temp pool
	// 如匹配双方存在拉黑关系，需重试匹配一次
	for i := 0; i < 2; i++ {
		pickPool, err := SampleOneWaitingPool(filter)
		if err != nil {
			return false, err
		}
		if pickPool == nil {
			return false, nil
		}
		// 判断 pick pool 是否和上一次一样，如果一样直接退出重试
		if tempPool != nil && tempPool.OID == pickPool.OID {
			break
		}
		tempPool = pickPool
		// 不匹配有拉黑关系的主播
		exists := param.blockExists(p.CreatorID, pickPool.CreatorID)
		if !exists {
			pp = pickPool
			break
		}
	}
	if pp == nil {
		return false, nil
	}
	param.pools = []*Pool{p, pp}
	return true, nil
}

// blockExists 双方是否有拉黑关系, 只要有一方拉黑另一方就返回 true
func (param *pkMatchingParam) blockExists(fromUserID, toUserID int64) bool {
	// 不匹配到有拉黑关系的主播
	fromBlockTo, toBlockFrom, err := userapi.UserBlockStatus(
		fromUserID, toUserID)
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	return fromBlockTo || toBlockFrom
}

// pickRandomLive 随机匹配两个 PK 对象
func (param *pkMatchingParam) pickRandomLive() (bool, error) {
	var err error
	param.pools, err = FindRandomPKWaitingLives()
	if err != nil {
		return false, err
	}
	return len(param.pools) >= 2, nil
}

// NOTICE: 如一方直播间已关播，则将关播的直播间的状态修改为匹配失败，并且退出本次匹配
func (param *pkMatchingParam) checkRoom() (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	closeRoomIDs, err := room.FindRoomIDs(bson.M{
		"room_id":     bson.M{"$in": []int64{param.pools[0].RoomID, param.pools[1].RoomID}},
		"status.open": room.StatusOpenFalse,
	})
	if err != nil {
		return false, err
	}
	if len(closeRoomIDs) != 0 {
		_, err = PoolCollection().UpdateMany(ctx,
			bson.M{
				"room_id": bson.M{"$in": closeRoomIDs},
				"status":  PKPoolStatusWaiting,
			},
			bson.M{"$set": bson.M{
				"status":        PKPoolStatusFail,
				"modified_time": goutil.TimeNow().Unix(),
			}},
		)
		return false, err
	}
	return true, nil
}

// createPK 直播间随机匹配 PK
func (param *pkMatchingParam) createPK() error {
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		var roomIDs [2]int64
		param.pk = new(LivePK)
		for i, pool := range param.pools {
			param.pk.Fighters[i] = &Fighter{
				RoomID:    pool.RoomID,
				CreatorID: pool.CreatorID,
			}
			roomIDs[i] = pool.RoomID
		}
		err := param.pk.FindRooms()
		if err != nil {
			return err
		}
		connectID, provider, err := param.pk.MatchProvider()
		if err != nil {
			return err
		}
		param.pk, err = CreatePKRecord(ctx, connectID, provider, param.pk.Fighters)
		if err != nil {
			return err
		}

		err = room.MatchConnectID(ctx, connectID, provider, roomIDs)
		if err != nil {
			return err
		}
		// 更新 PKPool 状态为匹配成功
		err = SetPoolRoomStatus(ctx, PKPoolStatusSuccess, roomIDs)
		if err != nil {
			return err
		}
		// PK 打榜计时
		pipe := service.Redis.TxPipeline()
		for _, f := range param.pk.Fighters {
			key := keys.KeyPKFighting1.Format(f.RoomID)
			pipe.Set(key, param.pk.OID.Hex(), RandomPKFightingDuration)
		}
		// 递增双方 PK 高峰时段连通次数
		IncrPKPeakCount(pipe, param.pk, nil)
		if _, err = pipe.Exec(); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (param *pkMatchingParam) broadcast() {
	messages := make([]*userapi.BroadcastElem, 0, len(param.pk.Fighters))
	for i, f := range param.pk.Fighters {
		var pk *LivePK
		if i == 0 {
			pk = param.pk
		} else {
			pk = CopyLivePK(param.pk, true)
			pk.BuildDuration()
		}

		payload := &matchMsgPayload{
			Type:   liveim.TypePK,
			Event:  liveim.EventPKMatchSuccess,
			RoomID: f.RoomID,
			LivePK: pk,
		}
		messages = append(messages, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  payload.RoomID,
			Payload: payload,
		})
	}
	if err := userapi.BroadcastMany(messages); err != nil {
		logger.Error(err)
		// PASS
	}
}
