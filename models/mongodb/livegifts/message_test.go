package livegifts

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMessageTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(RoomMessage{}, "type", "event", "room_id", "user", "room", "bubble",
		"time", "message_prefix", "gift", "lucky", "gift_notification", "combo", "multi_combo", "current_revenue", "open_url")
	kc.Check(Combo{}, "id", "effect_url", "web_effect_url", "num",
		"total_num", "achieved_num", "target_num", "remain_time")
}

func TestCombo_BuildEffect(t *testing.T) {
	assert := assert.New(t)

	type data struct {
		num             int64
		previousGiftNum int64

		wantNotify       bool
		wantEffectURL    string
		wantWebEffectURL string
		wantAchievedNum  int64
		wantTargetNum    int64
	}

	t.Run("单人连击礼物", func(t *testing.T) {
		g := &gift.Gift{
			GiftID:         1,
			ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
			WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
			Price:          66,
			Comboable:      gift.ComboableTypeSingle,
		}
		testData := []data{
			{
				num:              9, // 594 钻
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
			},
			{
				num:              16, // 1056 钻
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    g.ComboEffect,
				wantWebEffectURL: g.WebComboEffect,
			},
			{
				num:              20,
				previousGiftNum:  16,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
			},
			{
				num:              319, // 21054 钻
				previousGiftNum:  0,
				wantNotify:       true,
				wantEffectURL:    g.ComboEffect,
				wantWebEffectURL: g.WebComboEffect,
			},
			{
				num:              320,
				previousGiftNum:  319,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
			},
		}
		for i := range testData {
			c := Combo{
				Num:      int(testData[i].num),
				TotalNum: int(testData[i].num),
			}
			c.BuildEffect(g, testData[i].previousGiftNum)
			msg := fmt.Sprintf("num: %d, previousGiftNum: %d", testData[i].num, testData[i].previousGiftNum)
			assert.Equal(testData[i].wantNotify, c.Notify, msg)
			assert.Equal(testData[i].wantEffectURL, c.EffectURL, msg)
			assert.Equal(testData[i].wantWebEffectURL, c.WebEffectURL, msg)
		}
	})

	t.Run("配置档位特效的单人连击礼物", func(t *testing.T) {
		g := &gift.Gift{
			GiftID:         1,
			ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
			WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
			Price:          100,
			Comboable:      gift.ComboableTypeSingle,
			Combos: []gift.Combo{
				{
					TargetPrice:    1000,
					ComboEffect:    "https://static-test.maoercdn.com/combo/1.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/1-web.png",
				},
				{
					TargetPrice:    3000,
					RepeatAddPrice: 3000,
					ComboEffect:    "https://static-test.maoercdn.com/combo/2.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/2-web.png",
				},
			},
		}
		testData := []data{
			{
				num:              9,
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
			},
			{
				num:              11,
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    g.Combos[0].ComboEffect,
				wantWebEffectURL: g.Combos[0].WebComboEffect,
			},
			{
				num:              200,
				previousGiftNum:  180,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
			},
			{
				num:              210,
				previousGiftNum:  200,
				wantNotify:       true,
				wantEffectURL:    g.Combos[1].ComboEffect,
				wantWebEffectURL: g.Combos[1].WebComboEffect,
			},
			{
				num:              240, // 21054 钻
				previousGiftNum:  210,
				wantNotify:       false,
				wantEffectURL:    g.Combos[1].ComboEffect,
				wantWebEffectURL: g.Combos[1].WebComboEffect,
			},
		}
		for i := range testData {
			c := Combo{
				Num:      int(testData[i].num),
				TotalNum: int(testData[i].num),
			}
			c.BuildEffect(g, testData[i].previousGiftNum)
			msg := fmt.Sprintf("num: %d, previousGiftNum: %d", testData[i].num, testData[i].previousGiftNum)
			assert.Equal(testData[i].wantNotify, c.Notify, msg)
			assert.Equal(testData[i].wantEffectURL, c.EffectURL, msg)
			assert.Equal(testData[i].wantWebEffectURL, c.WebEffectURL, msg)
		}
	})

	t.Run("一起送连击礼物", func(t *testing.T) {
		// 一起送连击礼物
		g := &gift.Gift{
			GiftID:         1,
			ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
			WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
			Price:          66,
			Comboable:      gift.ComboableTypeMulti,
			Combos: []gift.Combo{
				{
					TargetPrice:    660, // 数量 10
					ComboEffect:    "https://static-test.maoercdn.com/combo/1.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/1-web.png",
				},
				{
					TargetPrice:    1320, // 数量 20
					ComboEffect:    "https://static-test.maoercdn.com/combo/1.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/1-web.png",
				},
				{
					TargetPrice:    3300, // 数量 50
					ComboEffect:    "https://static-test.maoercdn.com/combo/2.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/2-web.png",
				},
				{
					TargetPrice:    6600, // 数量 100
					ComboEffect:    "https://static-test.maoercdn.com/combo/2.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/2-web.png",
				},
				{
					TargetPrice:    9900, // 数量 150
					ComboEffect:    "https://static-test.maoercdn.com/combo/2.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/2-web.png",
				},
				{
					TargetPrice:    21000, // 数量 319
					RepeatAddPrice: 16800, // 数量 573, 828, 1082, 1337 ....
					ComboEffect:    "https://static-test.maoercdn.com/combo/3.png",
					WebComboEffect: "https://static-test.maoercdn.com/combo/3-web.png",
				},
			},
		}
		testData := []data{
			{
				num:              9,
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
				wantAchievedNum:  0,
				wantTargetNum:    10,
			},
			{
				num:              15,
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    g.Combos[0].ComboEffect,
				wantWebEffectURL: g.Combos[0].WebComboEffect,
				wantAchievedNum:  10,
				wantTargetNum:    20,
			},
			{
				num:              15,
				previousGiftNum:  10,
				wantNotify:       false,
				wantEffectURL:    "",
				wantWebEffectURL: "",
				wantAchievedNum:  10,
				wantTargetNum:    20,
			},
			{
				num:              52,
				previousGiftNum:  0,
				wantNotify:       false,
				wantEffectURL:    g.Combos[3].ComboEffect,
				wantWebEffectURL: g.Combos[3].WebComboEffect,
				wantAchievedNum:  50,
				wantTargetNum:    100,
			},
			{
				num:              400,
				previousGiftNum:  0,
				wantNotify:       true,
				wantEffectURL:    g.Combos[5].ComboEffect,
				wantWebEffectURL: g.Combos[5].WebComboEffect,
				wantAchievedNum:  319,
				wantTargetNum:    573,
			},
			{
				num:              319,
				previousGiftNum:  318,
				wantNotify:       true,
				wantEffectURL:    g.Combos[5].ComboEffect,
				wantWebEffectURL: g.Combos[5].WebComboEffect,
				wantAchievedNum:  319,
				wantTargetNum:    573,
			},
			{
				num:              573,
				previousGiftNum:  0,
				wantNotify:       true,
				wantEffectURL:    g.Combos[5].ComboEffect,
				wantWebEffectURL: g.Combos[5].WebComboEffect,
				wantAchievedNum:  573,
				wantTargetNum:    828,
			},
			{
				num:              1100,
				previousGiftNum:  800,
				wantNotify:       true,
				wantEffectURL:    g.Combos[5].ComboEffect,
				wantWebEffectURL: g.Combos[5].WebComboEffect,
				wantAchievedNum:  1082,
				wantTargetNum:    1337,
			},
		}
		for i := range testData {
			c := Combo{
				Num:      int(testData[i].num),
				TotalNum: int(testData[i].num),
			}
			c.BuildEffect(g, testData[i].previousGiftNum)
			msg := fmt.Sprintf("num: %d, previousGiftNum: %d", testData[i].num, testData[i].previousGiftNum)
			assert.Equal(testData[i].wantNotify, c.Notify, msg)
			assert.Equal(testData[i].wantEffectURL, c.EffectURL, msg)
			assert.Equal(testData[i].wantWebEffectURL, c.WebEffectURL, msg)
			assert.Equal(testData[i].wantAchievedNum, *c.AchievedNum, msg)
			assert.Equal(testData[i].wantTargetNum, c.TargetNum, msg)
		}
	})
}
