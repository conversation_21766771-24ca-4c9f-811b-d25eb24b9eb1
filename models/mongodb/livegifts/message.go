package livegifts

import (
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type roomCreator struct {
	RoomID          int64  `json:"room_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
	CreatorIconURL  string `json:"creator_iconurl"`
}

// RoomMessage 房间内消息
type RoomMessage struct {
	Type             string                           `json:"type"`
	Event            string                           `json:"event"`
	RoomID           int64                            `json:"room_id"`
	User             *liveuser.Simple                 `json:"user"`
	RoomCreator      *roomCreator                     `json:"room,omitempty"`
	Bubble           *bubble.Simple                   `json:"bubble,omitempty"`
	Time             goutil.TimeUnixMilli             `json:"time"`
	MessagePrefix    string                           `json:"message_prefix,omitempty"`
	Gift             *gift.NotifyGift                 `json:"gift"`
	Lucky            *gift.NotifyGift                 `json:"lucky,omitempty"`
	GiftNotification *userappearance.GiftNotification `json:"gift_notification,omitempty"`
	Combo            *Combo                           `json:"combo,omitempty"`
	MultiCombo       *Combo                           `json:"multi_combo,omitempty"`
	CurrentRevenue   *int64                           `json:"current_revenue,omitempty"`

	// TEMP: 临时兼容超能魔方系统消息使用字段，需要移除老消息时一并移除
	OpenURL string `json:"open_url,omitempty"`
}

// Combo 连击
type Combo struct {
	ID           string `json:"id"`
	EffectURL    string `json:"effect_url,omitempty"`
	WebEffectURL string `json:"web_effect_url,omitempty"`
	Num          int    `json:"num"`                    // 当前送礼用户的累计赠送数量
	TotalNum     int    `json:"total_num,omitempty"`    // 一起送的所有用户送礼总数
	AchievedNum  *int64 `json:"achieved_num,omitempty"` // 一起送的档位达标个数
	TargetNum    int64  `json:"target_num,omitempty"`   // 一起送的档位目标个数
	RemainTime   int64  `json:"remain_time"`            // 单位毫秒

	Top1UserID int64 `json:"-"` // 一起送助攻王用户 ID

	Notify bool `json:"-"` // 是否全站飘屏
}

// BuildEffect 构建连击特效和是否飘屏的判断
func (c *Combo) BuildEffect(g *gift.Gift, previousGiftNum int64) {
	previousPrice := previousGiftNum * g.Price
	var totalPrice int64
	if c.TotalNum > 0 {
		totalPrice = int64(c.TotalNum) * g.Price // 一起送连击需要使用 c.TotalNum
	} else {
		totalPrice = int64(c.Num) * g.Price // 单人连击无 c.TotalNum
	}

	if g.Comboable == gift.ComboableTypeMulti { // 一起送连击礼物
		comboLevel := g.ComboLevel(totalPrice)
		c.AchievedNum = util.NewInt64(comboLevel.AchievedNum)
		c.TargetNum = comboLevel.TargetNum
		if previousPrice < comboLevel.AchievedPrice {
			c.EffectURL = comboLevel.ComboEffect
			c.WebEffectURL = comboLevel.WebComboEffect
			if comboLevel.IsReachMaxFixedThreshold {
				// 达到最高档位后，特效和飘屏阈值固定，一起触发
				c.Notify = true
			}
		}
		return
	}

	if totalPrice/gift.ComboNotifyMinPrice > previousPrice/gift.ComboNotifyMinPrice {
		// 金额每达到 ComboNotifyMinPrice，触发飘屏
		c.Notify = true
	}
	if len(g.Combos) > 0 { // 配置档位的单人连击礼物
		comboLevel := g.ComboLevel(totalPrice)
		if previousPrice < comboLevel.AchievedPrice {
			c.EffectURL = comboLevel.ComboEffect
			c.WebEffectURL = comboLevel.WebComboEffect
		}
		return
	}

	if g.ComboEffect != "" &&
		((previousPrice < gift.ComboEffectMinPrice && totalPrice >= gift.ComboEffectMinPrice) ||
			(totalPrice/3000 > previousPrice/3000)) {
		// 金额达到 1000、3000、6000、9000 ...n*3000 钻，触发连击特效
		c.EffectURL = g.ComboEffect
		c.WebEffectURL = g.WebComboEffect
	}
}

// ComboNotifyPayload 赠送礼物飘屏信息
type ComboNotifyPayload struct {
	*gift.NotifyPayload `json:",inline"`
	Combo               *Combo `json:"combo,omitempty"` // TODO: 解决循环引用问题
	MultiCombo          *Combo `json:"multi_combo,omitempty"`
}
