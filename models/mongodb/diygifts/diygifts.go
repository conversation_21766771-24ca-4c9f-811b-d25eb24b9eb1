package diygifts

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Status 定制礼物状态
const (
	StatusOff = iota
	StatusOn
)

// Background 背景图
type Background struct {
	Image  string `bson:"image"`
	Width  int    `bson:"width"`
	Height int    `bson:"height"`
}

// Avatars 头像配置
type Avatars struct {
	CreatorPreviewPosition []int `bson:"creator_preview_position"`
	UserPreviewPosition    []int `bson:"user_preview_position"`
}

// Words 赠言配置
type Words struct {
	Image           string `bson:"image"`
	PreviewPosition []int  `bson:"preview_position"` // 手机端预览坐标 [x,y,w,h], 左上角起点的坐标以及显示区域的长宽
	TextColor       string `bson:"text_color"`
}

// DressType 装扮配置
type DressType struct {
	Name string `bson:"name"`
	Type int    `bson:"type"`
}

// DiyGift 定制礼物
type DiyGift struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`
	GiftID       int64              `bson:"gift_id"`
	EntryImage   string             `bson:"entry_image"`
	Background   Background         `bson:"background"`
	Avatars      *Avatars           `bson:"avatars,omitempty"`
	Words        *Words             `bson:"words,omitempty"`
	DressTypes   []DressType        `bson:"dress_types"`
	Status       int                `bson:"status"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("diy_gifts")
}

// ListDiyGiftsByPage 获取 diy 礼物列表
func ListDiyGiftsByPage(p, pageSize int64) ([]DiyGift, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	filter := bson.M{}
	count, err := col.CountDocuments(ctx, filter)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []DiyGift{}, pa, nil
	}
	opt := pa.SetFindOptions(nil)
	cur, err := col.Find(ctx, filter, opt)
	if err != nil {
		return nil, pa, err
	}
	defer cur.Close(ctx)
	var res []DiyGift
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, pa, err
	}
	return res, pa, nil
}
