package diygifts

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// FindDiyEntry 查询 DIY 礼物入口
func FindDiyEntry(giftIDs []int64) map[int64]string {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Find(ctx, bson.M{
		"gift_id": bson.M{"$in": giftIDs},
		"status":  StatusOn,
	}, options.Find().SetProjection(mongodb.NewProjection("gift_id, entry_image")))
	if err != nil {
		logger.Error(err)
		return map[int64]string{}
	}
	defer cur.Close(ctx)
	var diyGifts []DiyGift
	err = cur.All(ctx, &diyGifts)
	if err != nil {
		logger.Error(err)
		return map[int64]string{}
	}
	res := make(map[int64]string, len(diyGifts))
	for i := range diyGifts {
		res[diyGifts[i].GiftID] = storage.ParseSchemeURL(diyGifts[i].EntryImage)
	}
	return res
}
