package diygifts

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// FindDiyGiftByGiftID 通过礼物 ID 查询定制礼物
func FindDiyGiftByGiftID(giftID int64) (*DiyGift, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var dg DiyGift
	err := Collection().FindOne(ctx, bson.M{"gift_id": giftID, "status": StatusOn}).Decode(&dg)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	return &dg, nil
}
