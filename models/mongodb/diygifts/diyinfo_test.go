package diygifts

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

func TestFindDiyGiftByGiftID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	diyGift, err := FindDiyGiftByGiftID(-1)
	assert.NoError(err)
	assert.Nil(diyGift)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	err = col.FindOne(ctx,
		bson.M{"status": StatusOn}).Decode(&diyGift)
	if !mongodb.IsNoDocumentsError(err) {
		require.NoError(err)
	} else {
		diyGift = &DiyGift{
			GiftID: 2247,
			Status: StatusOn,
		}
		_, err = col.InsertOne(ctx, diyGift)
		require.NoError(err)
	}
	require.NotNil(diyGift)
	diyGift, err = FindDiyGiftByGiftID(diyGift.GiftID)
	require.NoError(err)
	assert.NotNil(diyGift)
}
