package diygifts

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestListDiyGiftsByPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	diyGifts, pa, err := ListDiyGiftsByPage(1, 20)
	require.NoError(err)
	assert.EqualValues(1, pa.MaxPage)
	require.NotEmpty(diyGifts)
	assert.Equal([]int{120, 180, 1005, 102}, diyGifts[0].Words.PreviewPosition)
	assert.EqualValues("oss://testentry.png", diyGifts[0].EntryImage)
	assert.NotEmpty(diyGifts[0].Background)
	assert.Len(diyGifts[0].<PERSON><PERSON><PERSON><PERSON><PERSON>, 2)
}
