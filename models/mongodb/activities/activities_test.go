package activities

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestCollection(t *testing.T) {
	assert := assert.New(t)
	col := Collection()
	assert.Equal("test_activities", col.Name())
}

func TestSetValidTimeFilter(t *testing.T) {
	assert := assert.New(t)

	f1 := bson.M{}
	nowUnix := int64(123)
	f2 := SetValidTimeFilter(f1, nowUnix)
	assert.Equal(f1, f2)
	assert.Equal(bson.M{"$lte": nowUnix}, f1["start_time"])
	assert.Equal(bson.M{"$not": bson.M{"$lte": nowUnix}}, f1["end_time"])
}

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().Unix()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{})
	require.NoError(err)

	acts := []Activity{
		{CreateTime: now, ModifiedTime: now, Type: TypeFaaSLua, Order: 1, Content: "print('1')", StartTime: now - 10, EndTime: util.NewInt64(now + 30)},
		{CreateTime: now, ModifiedTime: now, Type: TypeFaaSLua, Order: 2, Content: "print('2')", StartTime: now - 10, EndTime: util.NewInt64(now + 30)},
		{CreateTime: now, ModifiedTime: now, Type: TypeFaaSLua, Order: 3, Content: "print('3')", StartTime: now + 30, EndTime: util.NewInt64(now + 30)},
	}
	inserts := make([]interface{}, 0, len(acts))
	for _, a := range acts {
		inserts = append(inserts, a)
	}
	_, err = Collection().InsertMany(ctx, inserts)
	require.NoError(err)

	acts, err = List(TypeFaaSLua)
	require.NoError(err)
	assert.Len(acts, 2)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := Collection().InsertOne(ctx, Activity{
		Type:    TypeFaaSLua,
		Content: `aaa`,
	})
	require.NoError(err)
	r, err := FindOne(bson.M{"type": TypeFaaSLua})
	require.NoError(err)
	assert.NotNil(r)
}
