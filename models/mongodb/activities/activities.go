package activities

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// activity types
const (
	TypeFaaSLua = iota + 1
	TypeFaaSEventWidget
	TypeFaaSLiveShowWidget
)

// Activity activities 存储结构
type Activity struct {
	OID          primitive.ObjectID `bson:"_id,omitempty"`
	CreateTime   int64              `bson:"create_time"`
	ModifiedTime int64              `bson:"modified_time"`

	Type    int    `bson:"type"`
	Order   int    `bson:"order"`
	EventID int64  `bson:"event_id"`
	Key     string `bson:"key,omitempty"`
	Content string `bson:"content"`

	StartTime int64  `bson:"start_time"`
	EndTime   *int64 `bson:"end_time"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("activities")
}

// SetValidTimeFilter 为 mongodb 的 filter 设置有效时间的判断
func SetValidTimeFilter(filter bson.M, nowUnix int64) bson.M {
	filter["start_time"] = bson.M{"$lte": nowUnix}
	filter["end_time"] = bson.M{"$not": bson.M{"$lte": nowUnix}}
	return filter
}

// List activities by type
func List(t int) ([]Activity, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow().Unix()
	opts := &options.FindOptions{Sort: bson.M{"order": 1}}
	cur, err := Collection().Find(ctx, SetValidTimeFilter(bson.M{
		"type":  t,
		"order": bson.M{"$gt": 0},
	}, now), opts)
	if err != nil {
		return nil, err
	}

	var activities []Activity
	err = cur.All(ctx, &activities)
	return activities, err
}

// FindOne find one by filter
func FindOne(filter bson.M, opts ...*options.FindOneOptions) (*Activity, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var activity Activity
	err := Collection().FindOne(ctx, filter, opts...).Decode(&activity)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &activity, err
}
