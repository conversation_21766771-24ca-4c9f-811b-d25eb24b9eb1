package livebvclivesdkstatus

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestBatchInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(BatchInsert(nil))

	now := util.TimeNow()
	records := []LiveBvcLiveSdkStatus{
		{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			StreamName:   "test_batch_insert_1",
		},
		{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			StreamName:   "test_batch_insert_2",
		},
	}

	require.NoError(BatchInsert(records))

	streamNames := make([]string, len(records))
	for i := range records {
		streamNames[i] = records[i].StreamName
	}
	var records2 []LiveBvcLiveSdkStatus
	err := service.LogDB.Where("stream_name IN (?)", streamNames).Find(&records2).Error
	require.NoError(err)
	assert.Equal(len(records), len(records2))
}
