package livebvclivesdkstatus

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// LiveBvcLiveSdkStatus bvclive sdk status 上报记录
type LiveBvcLiveSdkStatus struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	IP           string `gorm:"column:ip"`

	StreamName string `gorm:"column:stream_name"`
	// Timestamp 事件时间 ms
	Timestamp int64  `gorm:"column:timestamp"`
	Sid       string `gorm:"column:sid"`
	Version   string `gorm:"column:version"`
	// Type 上报指标类型 1: 直播推流指标 2: 连麦指标 3: 播放指标
	Type int `gorm:"column:type"`
	// LiveEvent 事件类型 1: 推流成功 2: 推流开始时失败 3: 已经在推流，过程中发生错误，推流中断
	LiveEvent uint64 `gorm:"column:live_event"`

	// RtcEvent 事件类型 1: 加入连麦 2: 加入连麦成功 3: 收到首帧数据 4: 退出连麦成功 5: 更新连麦总时长
	RtcEvent uint64 `gorm:"column:rtc_event"`
	// RtcReceiveFirstFrameDuration 加入连麦至收到首桢数据的耗时，收到首帧数据时上报 ms
	RtcReceiveFirstFrameDuration float64 `gorm:"column:rtc_receive_first_frame_duration"`
	// RtcJoinDuration 加入连麦至连麦成功的耗时，加入连麦成功事件上报 ms
	RtcJoinDuration float64 `gorm:"column:rtc_join_duration"`
	// RtcDuration 本次连麦总时长，退出连麦时上报，连麦过程中也会定时上报 ms
	RtcDuration float64 `gorm:"column:rtc_duration"`

	// RtcTotalSamplesDuration WebRTC 接收 sample 总时长 ms
	RtcTotalSamplesDuration float64 `gorm:"column:rtc_total_samples_duration"`
	// RtcSilentConcealedDuration WebRTC 插入静音的时长 ms
	RtcSilentConcealedDuration float64 `gorm:"column:rtc_silent_concealed_duration"`
	// TotalSamplesDuration 播放时长 使用 SDK 内部播放时统计 ms
	TotalSamplesDuration float64 `gorm:"column:total_samples_duration"`
	// TotalSilentSamplesDuration 播放主动插入静音的时长 使用 SDK 内部播放器统计 ms
	TotalSilentSamplesDuration float64 `gorm:"column:total_silent_samples_duration"`
}

// TableName table name
func (LiveBvcLiveSdkStatus) TableName() string {
	return TableName()
}

// TableName table name
func TableName() string {
	return "live_bvclive_sdk_status"
}

// BatchInsert 批量插入记录
func BatchInsert(records []LiveBvcLiveSdkStatus) error {
	if len(records) == 0 {
		return nil
	}
	return servicedb.SplitBatchInsert(service.LogDB, TableName(), records, 1000, true)
}
