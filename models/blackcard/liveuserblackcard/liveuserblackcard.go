package liveuserblackcard

import (
	"fmt"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcard"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// MinBlackCardLevelHasCustomWelcome 拥有自定义欢迎语的最低黑卡等级限制
const MinBlackCardLevelHasCustomWelcome = 2

// MinBlackCardLevelHasHorn 拥有全站喇叭的最低黑卡等级限制
const MinBlackCardLevelHasHorn = liveblackcard.BlackCardLevel3

// BlackCardLevelHasLiveHorn 拥有动效的全站喇叭黑卡等级
const BlackCardLevelHasLiveHorn = liveblackcard.BlackCardLevel4

// LiveUserBlackCard 黑卡等级表
type LiveUserBlackCard struct {
	ID           int64 `gorm:"column:id"`            // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`   // 创建时间（秒级时间戳）
	ModifiedTime int64 `gorm:"column:modified_time"` // 修改时间（秒级时间戳）
	UserID       int64 `gorm:"column:user_id"`       // 用户 ID
	BlackCardID  int64 `gorm:"column:black_card_id"` // 黑卡等级 ID, 同 live_black_card.id
	StartTime    int64 `gorm:"column:start_time"`    // 开始时间（秒级时间戳）
	ExpireTime   int64 `gorm:"column:expire_time"`   // 过期时间（秒级时间戳）
}

// DB the db instance of LiveUserBlackCard model
func (lubc LiveUserBlackCard) DB() *gorm.DB {
	return service.LiveDB.Table(TableName())
}

// TableName for LiveUserBlackCard
func TableName() string {
	return "live_user_black_card"
}

// TableName for current model
func (LiveUserBlackCard) TableName() string {
	return TableName()
}

// BeforeSave gorm 钩子
func (lubc *LiveUserBlackCard) BeforeSave() error {
	nowUnix := util.TimeNow().Unix()
	lubc.ModifiedTime = nowUnix
	if lubc.DB().NewRecord(lubc) {
		lubc.CreateTime = nowUnix
	}
	return nil
}

// BeforeCreate .
func (lubc *LiveUserBlackCard) BeforeCreate() error {
	nowUnix := util.TimeNow().Unix()
	lubc.CreateTime = nowUnix
	lubc.ModifiedTime = nowUnix
	return nil
}

// UserBlackCardInfo 用户黑卡信息
type UserBlackCardInfo struct {
	UserID     int64  `gorm:"column:user_id"`
	Level      int    `gorm:"column:level"`       // 黑卡等级
	Title      string `gorm:"column:title"`       // 黑卡名称
	Price      int64  `gorm:"column:price"`       // 开通价格（单位：钻石）
	StartTime  int64  `gorm:"column:start_time"`  // 开始时间（秒级时间戳）
	ExpireTime int64  `gorm:"column:expire_time"` // 过期时间（秒级时间戳）
}

// FindUserActiveBlackCard 查询用户生效中的黑卡信息
func FindUserActiveBlackCard(userID int64) (*UserBlackCardInfo, error) {
	var info UserBlackCardInfo
	unixTimeNow := util.TimeNow().Unix()
	err := LiveUserBlackCard{}.DB().Select("t1.user_id, t1.expire_time, t2.level, t2.title, t2.price").
		Table(TableName()+" AS t1").
		Joins(fmt.Sprintf("LEFT JOIN %s AS t2 ON t1.black_card_id = t2.id", liveblackcard.TableName())).
		Where("t1.user_id = ? AND t1.start_time <= ? AND t1.expire_time > ?", userID, unixTimeNow, unixTimeNow).
		Order("t2.level DESC, t1.expire_time DESC").Take(&info).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &info, nil
}

// FindUserBlackCardRecentTwoMonths 查询用户最近两个月的开通记录
func FindUserBlackCardRecentTwoMonths(userID int64) ([]*UserBlackCardInfo, error) {
	var userBlackCards []*UserBlackCardInfo
	now := util.TimeNow()
	startTime := util.BeginningOfMonth(now).AddDate(0, -1, 0).Unix()
	nowStamp := now.Unix()
	err := LiveUserBlackCard{}.DB().Select("t1.user_id, t1.start_time, t1.expire_time, t2.level, t2.title, t2.price").
		Table(TableName()+" AS t1").
		Joins(fmt.Sprintf("LEFT JOIN %s AS t2 ON t1.black_card_id = t2.id", liveblackcard.TableName())).
		Where("t1.user_id = ? AND t1.start_time >= ? AND t1.expire_time > ?", userID, startTime, nowStamp).
		Order("t2.level DESC").
		Find(&userBlackCards).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return userBlackCards, nil
}

// FindUserIDsByExpireTime 查询指定时间过期的用户 IDs
func FindUserIDsByExpireTime(expireTime int64) ([]int64, error) {
	var userIDs []int64
	err := LiveUserBlackCard{}.DB().Where("expire_time = ?", expireTime).Pluck("user_id", &userIDs).Error
	if err != nil {
		return nil, err
	}
	return userIDs, nil
}

// FindUserBlackCardByUserIDs 查询指定用户过期时间大于指定时间的开通记录
func FindUserBlackCardByUserIDs(userIDs []int64, timeStamp int64) ([]*UserBlackCardInfo, error) {
	var userBlackCards []*UserBlackCardInfo
	err := LiveUserBlackCard{}.DB().Select("t1.user_id, t1.start_time, t1.expire_time, t2.level, t2.title, t2.price").
		Table(TableName()+" AS t1").
		Joins(fmt.Sprintf("LEFT JOIN %s AS t2 ON t1.black_card_id = t2.id", liveblackcard.TableName())).
		Where("t1.user_id IN (?) AND t1.expire_time > ?", userIDs, timeStamp).
		Find(&userBlackCards).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return userBlackCards, nil
}

// AddBlackCardPrivileges 黑卡特权发放入口函数
func AddBlackCardPrivileges(userID int64, oldLevel int, newLevel int, newExpireTime int64, isRenewal bool) {
	addBlackCardAppearances(userID, newLevel, newExpireTime, isRenewal)
	addBlackCardRewards(userID, oldLevel, newLevel, newExpireTime)
}

func addBlackCardAppearances(userID int64, newLevel int, newExpireTime int64, isRenewal bool) {
	logField := logger.Fields{
		"user_id":     userID,
		"level":       newLevel,
		"expire_time": newExpireTime,
		"is_renewal":  isRenewal,
	}
	if isRenewal {
		if err := userappearance.RenewBlackCardAppearances(userID, newExpireTime); err != nil {
			logger.WithFields(logField).Errorf("renew black card appearances failed: %v", err)
			// PASS
		}
	} else {
		if err := userappearance.AddNewBlackCardAppearances(userID, newLevel, newExpireTime); err != nil {
			logger.WithFields(logField).Errorf("add new black card appearances failed: %v", err)
			// PASS
		}
	}
}

func addBlackCardRewards(userID int64, oldLevel int, newLevel int, newExpireTime int64) {
	logFields := logger.Fields{
		"user_id":   userID,
		"old_level": oldLevel,
		"new_level": newLevel,
	}

	// 发放自定义进场欢迎语权益
	if err := addBlackCardCustomWelcome(userID, newLevel, newExpireTime); err != nil {
		logger.WithFields(logFields).Errorf("add black card custom welcome failed: %v", err)
		// PASS
	}
	// 发放黑卡全站喇叭奖励
	if err := addBlackCardHorns(userID, oldLevel, newLevel); err != nil {
		logger.WithFields(logFields).Errorf("add black card horns failed: %v", err)
		// PASS
	}
	// 发放黑卡专属表情
	if err := addBlackCardStickers(userID, newLevel, newExpireTime); err != nil {
		logger.WithFields(logFields).Errorf("add black card stickers failed: %v", err)
		// PASS
	}
}

func addBlackCardCustomWelcome(userID int64, newLevel int, newExpireTime int64) error {
	if !haveBlackCardPrivilege(newLevel, PrivilegeCustomWelcome) {
		return nil
	}
	return usermeta.AssignCustomWelcomeWithDuration(userID, 0, newExpireTime)
}

// level -> hornNum
var hornNumMap = map[int]int{
	liveblackcard.BlackCardLevel3: 5,
	liveblackcard.BlackCardLevel4: 5,
}

func addBlackCardHorns(userID int64, oldLevel int, newLevel int) error {
	// 计算累加的喇叭数量：从 oldLevel+1 到 newLevel 的所有等级喇叭数量之和
	totalHorns := 0
	for level := oldLevel + 1; level <= newLevel; level++ {
		if hornNum, exists := hornNumMap[level]; exists {
			totalHorns += hornNum
		}
	}

	if totalHorns == 0 {
		return nil
	}

	// 调用增加黑卡喇叭数量的函数
	_, _, err := userstatus.IncreaseHornNum(userID, userstatus.HornTypeBlackCard, totalHorns)
	return err
}

// 黑卡等级对应的表情ID
var blackCardStickerIDs = map[int][]int64{
	liveblackcard.BlackCardLevel1: {209, 210},
	liveblackcard.BlackCardLevel2: {209, 210},
	liveblackcard.BlackCardLevel3: {209, 210, 211},
	liveblackcard.BlackCardLevel4: {209, 210, 211, 212},
}

// addBlackCardStickers 发放黑卡专属表情
func addBlackCardStickers(userID int64, level int, expireTime int64) error {
	stickerIDs, ok := blackCardStickerIDs[level]
	if !ok || len(stickerIDs) == 0 {
		return fmt.Errorf("can not find sticker ids for level %d", level)
	}

	timeNow := util.TimeNow()
	// 查找用户的专属表情包
	owner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, userID, timeNow)
	if err != nil {
		return err
	}

	// 若用户没有专属表情包，则创建一个
	var pkg *livesticker.Package
	if owner == nil {
		pkg, err = livesticker.AssignExclusivePackage(livesticker.TypeUser, 0, userID)
		if err != nil {
			return err
		}
	} else {
		pkg, err = livesticker.FindPackage(owner.PackageID)
		if err != nil {
			return err
		}
		if pkg == nil {
			return fmt.Errorf("can not find sticker package for user %d", userID)
		}
	}

	// 批量查询表情是否存在
	stickers, err := livesticker.FindStickersByIDs(stickerIDs)
	if err != nil {
		return err
	}
	if len(stickers) != len(stickerIDs) {
		logger.Errorf("some stickers not found, expected %d but got %d", len(stickerIDs), len(stickers))
		// PASS
	}
	if len(stickers) == 0 {
		return nil
	}
	stickerMap := util.ToMap(stickers, "ID").(map[int64]*livesticker.LiveSticker)

	// 批量查询表情包中是否已有这些表情
	var existMaps []*livesticker.PackageStickerMap
	err = livesticker.DB().Where("package_id = ? AND sticker_id IN (?)", pkg.ID, stickerIDs).Find(&existMaps).Error
	if err != nil {
		return err
	}
	existMapsByID := make(map[int64]*livesticker.PackageStickerMap)
	for _, m := range existMaps {
		existMapsByID[m.StickerID] = m
	}

	// 收集需要更新和需要新增的记录
	var toUpdateExpire []*livesticker.PackageStickerMap
	var toInsertSticker []*livesticker.PackageStickerMap
	nowUnix := timeNow.Unix()

	for _, stickerID := range stickerIDs {
		// 跳过不存在的表情
		if _, exists := stickerMap[stickerID]; !exists {
			continue
		}

		if m, exists := existMapsByID[stickerID]; exists {
			// 如果表情已存在且过期时间晚于新的过期时间，则跳过
			if m.ExpireTime >= expireTime && m.ExpireTime != 0 {
				continue
			}
			// 收集需要更新的记录
			toUpdateExpire = append(toUpdateExpire, m)
		} else {
			// 收集需要插入的记录
			toInsertSticker = append(toInsertSticker, &livesticker.PackageStickerMap{
				PackageID:    pkg.ID,
				StickerID:    stickerID,
				StartTime:    nowUnix,
				ExpireTime:   expireTime,
				CreateTime:   nowUnix,
				ModifiedTime: nowUnix,
			})
		}
	}

	toUpdateExpireIDs := util.SliceMap(toUpdateExpire, func(m *livesticker.PackageStickerMap) int64 {
		return m.ID
	})
	if len(toUpdateExpireIDs) > 0 {
		err = livesticker.DB().Model(&livesticker.PackageStickerMap{}).
			Where("id IN (?)", toUpdateExpireIDs).
			Updates(map[string]interface{}{
				"expire_time":   expireTime,
				"modified_time": nowUnix,
			}).Error
		if err != nil {
			logger.Errorf("batch update sticker maps failed: %v", err)
			// PASS
		}
	}
	if len(toInsertSticker) > 0 {
		err = servicedb.BatchInsert(livesticker.DB(), livesticker.PackageStickerMap{}.TableName(), toInsertSticker)
		if err != nil {
			logger.Errorf("batch insert sticker maps failed: %v", err)
			// PASS
		}
	}

	return nil
}

// ClearBlackCardPrivileges 清理黑卡权益，如果用户没有黑卡则清空，如果用户黑卡等级降级则清理高等级权益
// userBlackCardMap: 用户当前黑卡等级信息，若用户没有黑卡，则对应的 value 为 nil
func ClearBlackCardPrivileges(userBlackCardMap map[int64]*UserBlackCardInfo) {
	if len(userBlackCardMap) == 0 {
		return
	}

	clearBlackCardAppearances(userBlackCardMap)
	clearBlackCardRewards(userBlackCardMap)
}

func clearBlackCardAppearances(userBlackCardMap map[int64]*UserBlackCardInfo) {
	userBlackCardInfos := make(map[int64]*userappearance.UserBlackCardInfo, len(userBlackCardMap))
	for userID, info := range userBlackCardMap {
		if info == nil {
			userBlackCardInfos[userID] = nil
		} else {
			userBlackCardInfos[userID] = &userappearance.UserBlackCardInfo{
				UserID:     info.UserID,
				Level:      info.Level,
				ExpireTime: info.ExpireTime,
				StartTime:  info.StartTime,
			}
		}
	}
	err := userappearance.ClearBlackCardAppearances(userBlackCardInfos)
	if err != nil {
		logger.Errorf("清理用户黑卡外观失败：%v，涉及用户：%+v", err, userBlackCardInfos)
		// PASS
	}
}

func clearBlackCardRewards(userBlackCardMap map[int64]*UserBlackCardInfo) {
	clearCustomWelcomeUserIDs := make([]int64, 0, len(userBlackCardMap))
	clearBlackCardHornUserIDs := make([]int64, 0, len(userBlackCardMap))
	clearBlackCardStickerUserIDs := make([]int64, 0, len(userBlackCardMap))

	for userID, info := range userBlackCardMap {
		// 使用权限检查函数判断是否需要清理
		if !HaveBlackCardPrivilege(info, PrivilegeCustomWelcome) {
			clearCustomWelcomeUserIDs = append(clearCustomWelcomeUserIDs, userID)
		}

		if !HaveBlackCardPrivilege(info, PrivilegeBlackCardHorn) {
			clearBlackCardHornUserIDs = append(clearBlackCardHornUserIDs, userID)
		}

		if !HaveBlackCardPrivilege(info, PrivilegeBlackCardSticker) {
			clearBlackCardStickerUserIDs = append(clearBlackCardStickerUserIDs, userID)
		}
	}

	// 批量清理自定义欢迎语
	if len(clearCustomWelcomeUserIDs) > 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := usermeta.Collection().UpdateMany(ctx, bson.M{
			"user_id": bson.M{"$in": clearCustomWelcomeUserIDs},
		}, bson.M{
			"$unset": bson.M{
				"custom_welcome_message": 0,
			},
		})
		if err != nil {
			logger.Errorf("清理用户自定义欢迎语失败：%v，涉及用户：%s", err, util.JoinInt64Array(clearCustomWelcomeUserIDs, ","))
			// PASS
		}
	}

	// 批量清理黑卡喇叭
	if len(clearBlackCardHornUserIDs) > 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := usermeta.Collection().UpdateMany(ctx, bson.M{
			"user_id": bson.M{"$in": clearBlackCardHornUserIDs},
		}, bson.M{
			"$unset": bson.M{
				"black_card_horn_num": 0,
			},
		})
		if err != nil {
			logger.Errorf("清理用户黑卡喇叭状态失败：%v，涉及用户：%s", err, util.JoinInt64Array(clearBlackCardHornUserIDs, ","))
			// PASS
		}
	}

	// 批量清理黑卡专属表情
	if len(clearBlackCardStickerUserIDs) > 0 {
		allBlackCardStickerIDs := getAllBlackCardStickerIDs()
		if len(allBlackCardStickerIDs) == 0 {
			return
		}

		timeNow := util.TimeNow()
		// 查询用户的专属表情包
		for _, userID := range clearBlackCardStickerUserIDs {
			// 查找用户的专属表情包
			owner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, userID, timeNow)
			if err != nil {
				logger.Errorf("查询用户专属表情包失败：%v，用户ID：%d", err, userID)
				continue
			}
			if owner == nil {
				// 用户没有专属表情包，不需要清理
				continue
			}

			// 清理用户的黑卡专属表情
			err = livesticker.DB().Delete(&livesticker.PackageStickerMap{}, "package_id = ? AND sticker_id IN (?)", owner.PackageID, allBlackCardStickerIDs).Error
			if err != nil {
				logger.Errorf("清理用户黑卡专属表情失败：%v，用户ID：%d", err, userID)
				// PASS
			}
		}

	}
}

// getAllBlackCardStickerIDs 获取所有黑卡专属表情ID
func getAllBlackCardStickerIDs() []int64 {
	var stickerIDs []int64
	for _, ids := range blackCardStickerIDs {
		stickerIDs = append(stickerIDs, ids...)
	}
	// 去重
	uniqueIDs := make(map[int64]struct{})
	for _, id := range stickerIDs {
		uniqueIDs[id] = struct{}{}
	}

	result := make([]int64, 0, len(uniqueIDs))
	for id := range uniqueIDs {
		result = append(result, id)
	}
	return result
}

// 黑卡权限常量定义
const (
	PrivilegeCustomWelcome    = iota + 1 // 自定义欢迎语
	PrivilegeBlackCardHorn               // 黑卡喇叭
	PrivilegeBlackCardSticker            // 黑卡专属表情
)

// HaveBlackCardPrivilege 检查黑卡是否拥有特定权限
func HaveBlackCardPrivilege(info *UserBlackCardInfo, privilegeType int) bool {
	if info == nil {
		return false
	}
	return haveBlackCardPrivilege(info.Level, privilegeType)
}

func haveBlackCardPrivilege(level int, privilegeType int) bool {
	switch privilegeType {
	case PrivilegeCustomWelcome:
		// 2 级及以上黑卡有自定义欢迎语权限
		return level >= MinBlackCardLevelHasCustomWelcome
	case PrivilegeBlackCardHorn:
		// 3 级及以上黑卡有喇叭权限
		return level >= MinBlackCardLevelHasHorn
	case PrivilegeBlackCardSticker:
		// 所有黑卡等级都有专属表情权限
		return true
	// 可以添加更多权限的判断逻辑
	default:
		return false
	}
}
