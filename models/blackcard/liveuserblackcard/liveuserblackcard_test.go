package liveuserblackcard

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"go.mongodb.org/mongo-driver/bson"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveUserBlackCard{}, "id", "create_time", "modified_time", "user_id", "black_card_id", "start_time", "expire_time")
	kc.Check(UserBlackCardInfo{}, "user_id", "level", "title", "price", "start_time", "expire_time")
}

func TestLiveUserBlackCard_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_user_black_card", LiveUserBlackCard{}.TableName())
}

func TestLiveUserBlackCard_BeforeSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	cancel := goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer cancel()

	lbc := LiveUserBlackCard{UserID: 12345}
	require.NoError(lbc.DB().Delete("", "user_id = ?", lbc.UserID).Error)
	require.NoError(lbc.DB().Save(&lbc).Error)

	var record LiveUserBlackCard
	require.NoError(record.DB().Where("user_id = ?", lbc.UserID).First(&record).Error)
	assert.Equal(now.Unix(), record.CreateTime)
	assert.Equal(now.Unix(), record.ModifiedTime)
}

func TestFindUserActiveBlackCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户没有黑卡信息
	testUserID := int64(2334)
	require.NoError(LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testUserID).Error)
	res, err := FindUserActiveBlackCard(testUserID)
	require.NoError(err)
	assert.Nil(res)

	// 测试用户有黑卡信息
	timeNow := goutil.TimeNow()
	infos := []LiveUserBlackCard{
		{
			UserID:      testUserID,
			BlackCardID: 2,
			StartTime:   timeNow.Unix(),
			ExpireTime:  timeNow.Add(time.Minute).Unix(),
		},
		{
			UserID:      testUserID,
			BlackCardID: 3,
			StartTime:   timeNow.Unix(),
			ExpireTime:  timeNow.Add(time.Minute).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))
	res, err = FindUserActiveBlackCard(testUserID)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(testUserID, res.UserID)
	assert.Equal(3, res.Level)
	assert.Equal("星曜 V3", res.Title)
	assert.EqualValues(1000000, res.Price)
}

func TestFindUserBlackCardRecentTwoMonths(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户没有黑卡信息
	testUserID := int64(2334)
	require.NoError(LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testUserID).Error)
	res, err := FindUserBlackCardRecentTwoMonths(testUserID)
	require.NoError(err)
	require.Empty(res)

	// 测试用户有黑卡信息
	timeNow := goutil.TimeNow()
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(timeNow)
	infos := []LiveUserBlackCard{
		{
			UserID:      testUserID,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
		{
			UserID:      testUserID,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -2, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
		{
			UserID:      testUserID,
			BlackCardID: 3,
			StartTime:   timeNow.Unix() - 1,
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))
	res, err = FindUserBlackCardRecentTwoMonths(testUserID)
	require.NoError(err)
	require.NotNil(res)
	require.Len(res, 2)
	assert.Equal(testUserID, res[0].UserID)
	assert.Equal(3, res[0].Level)
	assert.Equal("星曜 V3", res[0].Title)
	assert.EqualValues(1000000, res[0].Price)
	assert.EqualValues(infos[2].StartTime, res[0].StartTime)
	assert.EqualValues(infos[2].ExpireTime, res[0].ExpireTime)

	assert.Equal(testUserID, res[1].UserID)
	assert.Equal(2, res[1].Level)
	assert.Equal("星曜 V2", res[1].Title)
	assert.EqualValues(500000, res[1].Price)
	assert.EqualValues(infos[0].StartTime, res[1].StartTime)
	assert.EqualValues(infos[0].ExpireTime, res[1].ExpireTime)
}

func TestFindUserIDsByExpireTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNow := goutil.TimeNow()
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(timeNow)
	testUserID1 := int64(1111)
	testUserID2 := int64(2222)
	require.NoError(LiveUserBlackCard{}.DB().Delete("", "user_id IN (?)", []int64{testUserID1, testUserID2}).Error)

	// 测试用户没有黑卡信息
	res, err := FindUserIDsByExpireTime(nowStampFirstDayOfThisMonth.Unix())
	require.NoError(err)
	require.Empty(res)

	// 测试用户有黑卡信息
	infos := []LiveUserBlackCard{
		{
			UserID:      testUserID1,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		{
			UserID:      testUserID2,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		{
			UserID:      testUserID2,
			BlackCardID: 3,
			StartTime:   timeNow.Unix() - 1,
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))
	res, err = FindUserIDsByExpireTime(nowStampFirstDayOfThisMonth.Unix())
	require.NoError(err)
	require.NotEmpty(res)
	require.Len(res, 2)
	assert.Equal(testUserID1, res[0])
	assert.Equal(testUserID2, res[1])
}

func TestFindUserBlackCardByUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	timeNow := goutil.TimeNow()
	testUserID1 := int64(1111)
	testUserID2 := int64(2222)
	require.NoError(LiveUserBlackCard{}.DB().Delete("", "user_id IN (?)", []int64{testUserID1, testUserID2}).Error)

	// 测试用户没有黑卡信息
	res, err := FindUserBlackCardByUserIDs([]int64{testUserID1, testUserID2}, timeNow.Unix())
	require.NoError(err)
	require.Empty(res)

	// 测试用户有黑卡信息
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(timeNow)
	infos := []LiveUserBlackCard{
		{
			UserID:      testUserID1,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		{
			UserID:      testUserID2,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		{
			UserID:      testUserID2,
			BlackCardID: 3,
			StartTime:   timeNow.Unix() - 1,
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))
	res, err = FindUserBlackCardByUserIDs([]int64{testUserID1, testUserID2}, nowStampFirstDayOfThisMonth.Unix())
	require.NoError(err)
	require.NotEmpty(res)
	require.Len(res, 1)
	assert.Equal(testUserID2, res[0].UserID)
}

func TestHaveBlackCardPrivilege(t *testing.T) {
	t.Run("用户黑卡信息为空", func(t *testing.T) {
		// 当 info 为 nil 时，应该返回 false
		assert.False(t, HaveBlackCardPrivilege(nil, PrivilegeCustomWelcome))
		assert.False(t, HaveBlackCardPrivilege(nil, PrivilegeBlackCardHorn))
		assert.False(t, HaveBlackCardPrivilege(nil, PrivilegeBlackCardSticker))
	})

	t.Run("自定义欢迎语权限", func(t *testing.T) {
		// 测试低于 MinBlackCardLevelHasCustomWelcome (2) 的等级，应该没有自定义欢迎语权限
		for level := range MinBlackCardLevelHasCustomWelcome {
			info := &UserBlackCardInfo{
				UserID: 123,
				Level:  level,
				Title:  "Test",
			}
			assert.False(t, HaveBlackCardPrivilege(info, PrivilegeCustomWelcome))
		}

		// 测试等于和高于 MinBlackCardLevelHasCustomWelcome (2) 的等级，应该有自定义欢迎语权限
		testCases := []int{MinBlackCardLevelHasCustomWelcome, MinBlackCardLevelHasCustomWelcome + 1, MinBlackCardLevelHasCustomWelcome + 2}
		for _, level := range testCases {
			info := &UserBlackCardInfo{
				UserID: 123,
				Level:  level,
				Title:  "Test",
			}
			assert.True(t, HaveBlackCardPrivilege(info, PrivilegeCustomWelcome))
		}
	})

	t.Run("黑卡喇叭权限", func(t *testing.T) {
		// 测试低于 MinBlackCardLevelHasHorn (3) 的等级，应该没有喇叭权限
		for level := 1; level < MinBlackCardLevelHasHorn; level++ {
			info := &UserBlackCardInfo{
				UserID: 123,
				Level:  level,
				Title:  "Test",
			}
			assert.False(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardHorn))
		}

		// 测试等于和高于 MinBlackCardLevelHasHorn (3) 的等级，应该有喇叭权限
		testCases := []int{MinBlackCardLevelHasHorn, MinBlackCardLevelHasHorn + 1, MinBlackCardLevelHasHorn + 2}
		for _, level := range testCases {
			info := &UserBlackCardInfo{
				UserID: 123,
				Level:  level,
				Title:  "Test",
			}
			assert.True(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardHorn))
		}
	})

	t.Run("黑卡专属表情权限", func(t *testing.T) {
		// 所有黑卡等级都有专属表情权限
		testCases := []int{1, 2, 3, 4, 5}
		for _, level := range testCases {
			info := &UserBlackCardInfo{
				UserID: 123,
				Level:  level,
				Title:  "Test",
			}
			assert.True(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardSticker))
		}
	})

	t.Run("未定义的权限类型", func(t *testing.T) {
		info := &UserBlackCardInfo{
			UserID: 123,
			Level:  5,
			Title:  "Test",
		}

		// 测试未定义的权限类型，应该返回 false
		undefinedPrivileges := []int{0, -1, 999, 100}
		for _, privilege := range undefinedPrivileges {
			assert.False(t, HaveBlackCardPrivilege(info, privilege))
		}
	})

	t.Run("边界值测试", func(t *testing.T) {
		// 测试黑卡等级为 0 的情况
		info := &UserBlackCardInfo{
			UserID: 123,
			Level:  0,
			Title:  "Test",
		}
		assert.False(t, HaveBlackCardPrivilege(info, PrivilegeCustomWelcome))
		assert.False(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardHorn))
		assert.True(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardSticker))

		// 测试负数等级的情况
		info.Level = -1
		assert.False(t, HaveBlackCardPrivilege(info, PrivilegeCustomWelcome))
		assert.False(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardHorn))
		assert.True(t, HaveBlackCardPrivilege(info, PrivilegeBlackCardSticker))
	})
}

func TestAddBlackCardHorns(t *testing.T) {
	testUserID := int64(888888)

	t.Run("AddHornsFromLevel0To1", func(t *testing.T) {
		// 测试从 0 级升级到 1 级，不应该有喇叭奖励（1 级和 2 级没有喇叭）
		oldLevel := 0
		newLevel := 1

		err := addBlackCardHorns(testUserID, oldLevel, newLevel)
		require.NoError(t, err)
	})

	t.Run("AddHornsFromLevel0To3", func(t *testing.T) {
		// 测试从 0 级升级到 3 级，应该有 5 个喇叭奖励
		oldLevel := 0
		newLevel := 3

		err := addBlackCardHorns(testUserID, oldLevel, newLevel)
		require.NoError(t, err)
	})

	t.Run("AddHornsFromLevel1To4", func(t *testing.T) {
		// 测试从 1 级升级到 4 级，应该有 10 个喇叭奖励（3 级 5 个 + 4 级 5 个）
		oldLevel := 1
		newLevel := 4

		err := addBlackCardHorns(testUserID, oldLevel, newLevel)
		require.NoError(t, err)
	})

	t.Run("AddHornsFromLevel3To3", func(t *testing.T) {
		// 测试相同等级，不应该有喇叭奖励
		oldLevel := 3
		newLevel := 3

		err := addBlackCardHorns(testUserID, oldLevel, newLevel)
		require.NoError(t, err)
	})

	t.Run("AddHornsLevelDowngrade", func(t *testing.T) {
		// 测试等级降级，不应该有喇叭奖励
		oldLevel := 4
		newLevel := 2

		err := addBlackCardHorns(testUserID, oldLevel, newLevel)
		require.NoError(t, err)
	})
}

func TestAddBlackCardCustomWelcome(t *testing.T) {
	testUserID := int64(777777)
	testExpireTime := goutil.TimeNow().Add(30 * 24 * time.Hour).Unix()

	t.Run("AddCustomWelcomeSuccess", func(t *testing.T) {
		// 测试成功添加自定义欢迎语（使用 2 级黑卡，满足最低级别要求）
		err := addBlackCardCustomWelcome(testUserID, 2, testExpireTime)
		// 由于这个函数调用的是外部模块，我们主要验证不会 panic 或返回明显错误
		// 实际的业务逻辑验证应该在对应的模块中进行
		require.NoError(t, err)
	})

	t.Run("AddCustomWelcomeWithZeroUserID", func(t *testing.T) {
		// 测试用户 ID 为 0 的情况（使用 2 级黑卡）
		err := addBlackCardCustomWelcome(0, 2, testExpireTime)
		require.NoError(t, err)
	})

	t.Run("AddCustomWelcomeWithZeroExpireTime", func(t *testing.T) {
		// 测试过期时间为 0 的情况（使用 2 级黑卡）
		err := addBlackCardCustomWelcome(testUserID, 2, 0)
		require.NoError(t, err)
	})

	t.Run("AddCustomWelcomeWithLowLevel", func(t *testing.T) {
		// 测试低于最低权限级别的黑卡（1 级），应该不执行任何操作
		err := addBlackCardCustomWelcome(testUserID, 1, testExpireTime)
		require.NoError(t, err)
	})

	t.Run("AddCustomWelcomeWithHighLevel", func(t *testing.T) {
		// 测试高级别黑卡（4 级），应该正常执行
		err := addBlackCardCustomWelcome(testUserID, 4, testExpireTime)
		require.NoError(t, err)
	})
}

func TestAddBlackCardStickers(t *testing.T) {
	// 创建测试表情数据
	testStickers := []*livesticker.LiveSticker{
		{ID: 209, Name: "test_sticker_209", Icon: "test_icon_209", Image: "test_image_209"},
		{ID: 210, Name: "test_sticker_210", Icon: "test_icon_210", Image: "test_image_210"},
		{ID: 211, Name: "test_sticker_211", Icon: "test_icon_211", Image: "test_image_211"},
		{ID: 212, Name: "test_sticker_212", Icon: "test_icon_212", Image: "test_image_212"},
	}
	require.NoError(t, servicedb.BatchInsert(service.LiveDB, (&livesticker.LiveSticker{}).TableName(), testStickers))
	defer func() {
		// 清理测试表情数据
		livesticker.DB().Delete(&livesticker.LiveSticker{}, "id IN (?)", []int64{209, 210, 211, 212})
	}()

	testUserID := int64(666666)
	testExpireTime := goutil.TimeNow().Add(30 * 24 * time.Hour).Unix()

	t.Run("AddStickersLevel1", func(t *testing.T) {
		// 测试为 1 级黑卡添加表情
		level := 1
		err := addBlackCardStickers(testUserID, level, testExpireTime)
		require.NoError(t, err)
	})

	t.Run("AddStickersLevel4", func(t *testing.T) {
		// 测试为 4 级黑卡添加表情并验证发放成功
		testUserID := int64(777777)
		level := 4
		err := addBlackCardStickers(testUserID, level, testExpireTime)
		require.NoError(t, err)

		// 验证表情发放成功
		owner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, testUserID, goutil.TimeNow())
		require.NoError(t, err)
		require.NotNil(t, owner)

		// 验证 4 级黑卡应该有 4 个表情 (209, 210, 211, 212)
		var stickerMaps []*livesticker.PackageStickerMap
		err = livesticker.DB().Where("package_id = ? AND sticker_id IN (?)", owner.PackageID, []int64{209, 210, 211, 212}).Find(&stickerMaps).Error
		require.NoError(t, err)
		require.Len(t, stickerMaps, 4)
	})

	t.Run("AddStickersInvalidLevel", func(t *testing.T) {
		// 测试无效等级
		level := 0
		err := addBlackCardStickers(testUserID, level, testExpireTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "can not find sticker ids for level")
	})

	t.Run("AddStickersHighLevel", func(t *testing.T) {
		// 测试超出定义范围的等级
		level := 10
		err := addBlackCardStickers(testUserID, level, testExpireTime)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "can not find sticker ids for level")
	})

	t.Run("UpdateExistingStickers", func(t *testing.T) {
		// 测试更新已有表情的过期时间场景 (len(toUpdateExpire) > 0)
		testUserID := int64(888888)
		level := 3 // 使用 3 级黑卡，包含表情 209, 210, 211

		// 首次调用，创建用户专属表情包并添加表情
		initialExpireTime := goutil.TimeNow().Add(30 * 24 * time.Hour).Unix()
		err := addBlackCardStickers(testUserID, level, initialExpireTime)
		require.NoError(t, err)

		// 再次调用，测试更新过期时间的逻辑 (触发 len(toUpdateExpire) > 0 分支)
		newExpireTime := goutil.TimeNow().Add(60 * 24 * time.Hour).Unix()
		err = addBlackCardStickers(testUserID, level, newExpireTime)
		require.NoError(t, err)

		// 验证过期时间已更新 - 查找任意一个表情映射验证即可
		owner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, testUserID, goutil.TimeNow())
		require.NoError(t, err)
		require.NotNil(t, owner)
		stickerMap := livesticker.PackageStickerMap{}
		err = livesticker.DB().Where("package_id = ? AND sticker_id = ?", owner.PackageID, 209).First(&stickerMap).Error
		require.NoError(t, err)
		assert.Equal(t, newExpireTime, stickerMap.ExpireTime)
	})
}

func TestClearBlackCardPrivileges(t *testing.T) {
	testUserID1 := int64(999001)
	testUserID2 := int64(999002)
	testUserID3 := int64(999003)
	timeNow := goutil.TimeNow()
	futureTime := timeNow.Add(30 * 24 * time.Hour).Unix()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 创建测试外观模板
	testAppearances := []*appearance.Appearance{
		{
			ID:    appearance.BlackCardAppearanceID(1, appearance.TypeIdentityBadge),
			Name:  "黑卡 V1 身份铭牌",
			Type:  appearance.TypeIdentityBadge,
			From:  appearance.FromBlackCard,
			Intro: "测试用黑卡身份铭牌",
			Icon:  "test_icon_v1.png",
			Image: "test_image_v1.png",
		},
		{
			ID:    appearance.BlackCardAppearanceID(2, appearance.TypeIdentityBadge),
			Name:  "黑卡 V2 身份铭牌",
			Type:  appearance.TypeIdentityBadge,
			From:  appearance.FromBlackCard,
			Intro: "测试用黑卡身份铭牌",
			Icon:  "test_icon_v2.png",
			Image: "test_image_v2.png",
		},
		{
			ID:    appearance.BlackCardAppearanceID(3, appearance.TypeMessageBubble),
			Name:  "黑卡 V3 气泡框",
			Type:  appearance.TypeMessageBubble,
			From:  appearance.FromBlackCard,
			Intro: "测试用黑卡气泡框",
			Icon:  "test_icon_v3.png",
			Image: "test_image_v3.png",
		},
	}
	_, err := appearance.Collection().InsertMany(ctx, goutil.SliceMap(testAppearances, func(app *appearance.Appearance) interface{} {
		return app
	}))
	require.NoError(t, err)

	// 清理测试外观模板
	defer appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": goutil.SliceMap(testAppearances, func(app *appearance.Appearance) interface{} {
		return app.ID
	})}})

	// 创建测试用户外观
	testUserAppearances := []*userappearance.UserAppearance{
		{
			AppearanceID: appearance.BlackCardAppearanceID(1, appearance.TypeIdentityBadge),
			UserID:       testUserID1,
			Name:         "黑卡 V1 身份铭牌",
			Type:         appearance.TypeIdentityBadge,
			Status:       userappearance.StatusWorn,
			OwnStatus:    userappearance.OwnStatusUsed,
			From:         appearance.FromBlackCard,
			Image:        "test_image_v1.png",
			StartTime:    timeNow.Unix(),
			ExpireTime:   &futureTime,
			ModifiedTime: timeNow.Unix(),
		},
		{
			AppearanceID: appearance.BlackCardAppearanceID(2, appearance.TypeIdentityBadge),
			UserID:       testUserID2,
			Name:         "黑卡 V2 身份铭牌",
			Type:         appearance.TypeIdentityBadge,
			Status:       userappearance.StatusWorn,
			OwnStatus:    userappearance.OwnStatusUsed,
			From:         appearance.FromBlackCard,
			Image:        "test_image_v2.png",
			StartTime:    timeNow.Unix(),
			ExpireTime:   &futureTime,
			ModifiedTime: timeNow.Unix(),
		},
		{
			AppearanceID: appearance.BlackCardAppearanceID(1, appearance.TypeIdentityBadge),
			UserID:       testUserID3,
			Name:         "黑卡 V1 身份铭牌",
			Type:         appearance.TypeIdentityBadge,
			Status:       userappearance.StatusWorn,
			OwnStatus:    userappearance.OwnStatusUsed,
			From:         appearance.FromBlackCard,
			Image:        "test_image_v1.png",
			StartTime:    timeNow.Unix(),
			ExpireTime:   &futureTime,
			ModifiedTime: timeNow.Unix(),
		},
		{
			AppearanceID: appearance.BlackCardAppearanceID(3, appearance.TypeMessageBubble),
			UserID:       testUserID3,
			Name:         "黑卡 V3 气泡框",
			Type:         appearance.TypeMessageBubble,
			Status:       userappearance.StatusWorn,
			OwnStatus:    userappearance.OwnStatusUsed,
			From:         appearance.FromBlackCard,
			Image:        "test_image_v3.png",
			StartTime:    timeNow.Unix(),
			ExpireTime:   &futureTime,
			ModifiedTime: timeNow.Unix(),
		},
	}
	_, err = userappearance.Collection().InsertMany(ctx, goutil.SliceMap(testUserAppearances, func(ua *userappearance.UserAppearance) interface{} {
		return ua
	}))
	require.NoError(t, err)

	// 清理测试用户外观
	defer func() {
		userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": goutil.SliceMap(testUserAppearances, func(ua *userappearance.UserAppearance) interface{} {
			return ua.UserID
		})}})
	}()

	// 创建测试黑卡表情
	testStickers := []*livesticker.LiveSticker{
		{ID: 209, Name: "test_sticker_209", Icon: "test_icon_209", Image: "test_image_209"},
		{ID: 210, Name: "test_sticker_210", Icon: "test_icon_210", Image: "test_image_210"},
		{ID: 211, Name: "test_sticker_211", Icon: "test_icon_211", Image: "test_image_211"},
		{ID: 212, Name: "test_sticker_212", Icon: "test_icon_212", Image: "test_image_212"},
	}
	require.NoError(t, servicedb.BatchInsert(service.LiveDB, (&livesticker.LiveSticker{}).TableName(), testStickers))

	// 清理测试黑卡表情
	defer livesticker.DB().Delete(&livesticker.LiveSticker{}, "id IN (?)", goutil.SliceMap(testStickers, func(s *livesticker.LiveSticker) int64 {
		return s.ID
	}))

	// 为测试用户创建专属表情包并添加表情
	stickerIDMap := map[int64][]int64{
		testUserID1: {209, 210},      // V1 用户有表情 209, 210
		testUserID2: {209, 210},      // V2 用户有表情 209, 210
		testUserID3: {209, 210, 211}, // V3 用户有表情 209, 210, 211
	}

	for userID, stickerIDs := range stickerIDMap {
		pkg, err := livesticker.AssignExclusivePackage(livesticker.TypeUser, 0, userID)
		require.NoError(t, err)

		// 为每个用户添加对应等级的表情
		for _, stickerID := range stickerIDs {
			stickerMap := &livesticker.PackageStickerMap{
				PackageID:    pkg.ID,
				StickerID:    stickerID,
				StartTime:    timeNow.Unix(),
				ExpireTime:   futureTime,
				CreateTime:   timeNow.Unix(),
				ModifiedTime: timeNow.Unix(),
			}
			err = livesticker.DB().Create(stickerMap).Error
			require.NoError(t, err)
		}

		defer func(packageID int64) {
			livesticker.DB().Delete(&livesticker.PackageStickerMap{}, "package_id = ?", packageID)
			livesticker.DB().Delete(&livesticker.PackageOwner{}, "package_id = ?", packageID)
			livesticker.DB().Delete(&livesticker.Package{}, "id = ?", packageID)
		}(pkg.ID)
	}

	// 创建测试用户状态
	testUserMetas := []*usermeta.UserMeta{
		{
			UserID:               testUserID1,
			NobleHornNum:         0,   // V1 用户没有喇叭
			CustomWelcomeMessage: nil, // V1 用户没有自定义欢迎语权限
		},
		{
			UserID:       testUserID2,
			NobleHornNum: 0, // V2 用户没有喇叭
			CustomWelcomeMessage: &usermeta.CustomWelcomeMessage{
				Text:       "V2 用户欢迎语",
				ExpireTime: futureTime,
			},
		},
		{
			UserID:       testUserID3,
			NobleHornNum: 5, // V3 用户有喇叭
			CustomWelcomeMessage: &usermeta.CustomWelcomeMessage{
				Text:       "V3 用户欢迎语",
				ExpireTime: futureTime,
			},
		},
	}
	for _, meta := range testUserMetas {
		_, err := usermeta.Collection().InsertOne(ctx, meta)
		require.NoError(t, err)
	}

	// 清理测试用户状态
	defer func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		for _, meta := range testUserMetas {
			usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": meta.UserID})
		}
		// 清理 user_meta 表中的黑卡喇叭数据
		userstatus.UserMetaCollection().UpdateMany(ctx, bson.M{
			"user_id": bson.M{"$in": []int64{testUserID1, testUserID2, testUserID3}},
		}, bson.M{
			"$unset": bson.M{
				"black_card_horn_num": 0,
			},
		})
	}()

	t.Run("黑卡过期应该删除所有黑卡外观和权益", func(t *testing.T) {
		// 模拟用户 1 黑卡完全过期（无有效黑卡）
		userBlackCardMap := map[int64]*UserBlackCardInfo{
			testUserID1: nil, // 没有有效黑卡
		}

		ClearBlackCardPrivileges(userBlackCardMap)

		// 验证黑卡外观被删除
		count, err := userappearance.Collection().CountDocuments(ctx, bson.M{
			"user_id": testUserID1,
			"from":    appearance.FromBlackCard,
		})
		require.NoError(t, err)
		assert.Equal(t, int64(0), count)

		// 验证自定义欢迎语被清理
		var meta usermeta.UserMeta
		err = usermeta.Collection().FindOne(ctx, bson.M{"user_id": testUserID1}).Decode(&meta)
		if err == nil {
			assert.Nil(t, meta.CustomWelcomeMessage)
		}

		// 验证黑卡喇叭被清理
		var userStatus userstatus.GeneralStatus
		err = userstatus.UserMetaCollection().FindOne(ctx, bson.M{"user_id": testUserID1}).Decode(&userStatus)
		if err == nil {
			assert.Equal(t, int64(0), userStatus.BlackCardHornNum)
		}

		// 验证黑卡表情被清理
		owner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, testUserID1, timeNow)
		if err == nil && owner != nil {
			var stickerMaps []*livesticker.PackageStickerMap
			err = livesticker.DB().Where("package_id = ? AND sticker_id IN (?)", owner.PackageID, []int64{209, 210, 211, 212}).Find(&stickerMaps).Error
			require.NoError(t, err)
			assert.Len(t, stickerMaps, 0) // 应该没有黑卡表情
		}
	})

	t.Run("黑卡降级应该替换成低级别黑卡外观和权益", func(t *testing.T) {
		// 模拟用户 3 从 V3 降级到 V1
		userBlackCardMap := map[int64]*UserBlackCardInfo{
			testUserID3: {
				UserID:     testUserID3,
				Level:      1, // 降级到 V1
				StartTime:  timeNow.Unix(),
				ExpireTime: futureTime,
			},
		}

		ClearBlackCardPrivileges(userBlackCardMap)

		// 验证高级别黑卡外观被删除，低级别外观被保留
		identityBadgeCount, err := userappearance.Collection().CountDocuments(ctx, bson.M{
			"user_id":       testUserID3,
			"appearance_id": appearance.BlackCardAppearanceID(1, appearance.TypeIdentityBadge),
			"from":          appearance.FromBlackCard,
		})
		require.NoError(t, err)
		assert.Greater(t, identityBadgeCount, int64(0)) // V1 身份铭牌应该存在

		bubbleCount, err := userappearance.Collection().CountDocuments(ctx, bson.M{
			"user_id":       testUserID3,
			"appearance_id": appearance.BlackCardAppearanceID(3, appearance.TypeMessageBubble),
			"from":          appearance.FromBlackCard,
		})
		require.NoError(t, err)
		assert.Equal(t, int64(0), bubbleCount) // V3 气泡框应该被删除

		// 验证自定义欢迎语被清理（V1 没有权限）
		var meta usermeta.UserMeta
		err = usermeta.Collection().FindOne(ctx, bson.M{"user_id": testUserID3}).Decode(&meta)
		if err == nil {
			assert.Nil(t, meta.CustomWelcomeMessage)
		}

		// 验证黑卡喇叭被清理（V1 没有权限）
		var userStatus userstatus.GeneralStatus
		err = userstatus.UserMetaCollection().FindOne(ctx, bson.M{"user_id": testUserID3}).Decode(&userStatus)
		if err == nil {
			assert.Equal(t, int64(0), userStatus.BlackCardHornNum)
		}
	})
}
