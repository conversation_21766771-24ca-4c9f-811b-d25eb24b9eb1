package liveblackcard

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
)

// BlackCardLevel constants for each black card level
const (
	BlackCardLevel1 = iota + 1
	BlackCardLevel2
	BlackCardLevel3
	BlackCardLevel4
)

// LevelNum 黑卡等级数量
const LevelNum = 4

// MaxLevel 最高黑卡等级
const MaxLevel = BlackCardLevel4

// LiveBlackCard 黑卡等级表
type LiveBlackCard struct {
	ID           int64  `gorm:"column:id"`            // 主键 ID
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间（秒级时间戳）
	ModifiedTime int64  `gorm:"column:modified_time"` // 修改时间（秒级时间戳）
	Title        string `gorm:"column:title"`         // 黑卡名称
	Level        int    `gorm:"column:level"`         // 黑卡等级
	Price        int64  `gorm:"column:price"`         // 开通价格（单位：钻石）
	Icon         string `gorm:"column:icon"`          // 黑卡等级图标

	IconURL string `gorm:"-"`
}

// DB the db instance of LiveBlackCard model
func (lbc LiveBlackCard) DB() *gorm.DB {
	return service.LiveDB.Table(TableName())
}

// TableName for LiveBlackCard
func TableName() string {
	return "live_black_card"
}

// TableName for current model
func (LiveBlackCard) TableName() string {
	return TableName()
}

// AfterFind gorm 钩子
func (lbc *LiveBlackCard) AfterFind() error {
	lbc.IconURL = storage.ParseSchemeURL(lbc.Icon)
	return nil
}

// ListAllBlackCard  获取全部黑卡，按照黑卡等级升序排列
func ListAllBlackCard() ([]LiveBlackCard, error) {
	var cards []LiveBlackCard
	err := LiveBlackCard{}.DB().Where("level > 0").Order("level ASC").Find(&cards).Error
	if err != nil {
		return nil, err
	}
	return cards, nil
}
