package liveblackcard

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTagKeys(t *testing.T) {
	lbc := LiveBlackCard{}
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(lbc, "id", "create_time", "modified_time", "title", "level", "price", "icon")
}

func TestLiveBlackCard_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_black_card", LiveBlackCard{}.TableName())
}

func TestListAllBlackCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := ListAllBlackCard()
	require.NoError(err)
	require.Len(res, 4)
	assert.Equal(1, res[0].Level)
	assert.Equal("星曜 V1", res[0].Title)
	assert.EqualValues(200000, res[0].Price)
	assert.Equal("oss://test1.png", res[0].Icon)
	assert.Equal("https://static-test.missevan.com/test1.png", res[0].IconURL)
	assert.Equal(2, res[1].Level)
	assert.Equal("星曜 V2", res[1].Title)
	assert.EqualValues(500000, res[1].Price)
	assert.Equal("oss://test2.png", res[1].Icon)
	assert.Equal("https://static-test.missevan.com/test2.png", res[1].IconURL)
	assert.Equal(3, res[2].Level)
	assert.Equal("星曜 V3", res[2].Title)
	assert.EqualValues(1000000, res[2].Price)
	assert.Equal("oss://test3.png", res[2].Icon)
	assert.Equal("https://static-test.missevan.com/test3.png", res[2].IconURL)
	assert.Equal(4, res[3].Level)
	assert.Equal("星曜 V4", res[3].Title)
	assert.EqualValues(2000000, res[3].Price)
	assert.Equal("oss://test4.png", res[3].Icon)
	assert.Equal("https://static-test.missevan.com/test4.png", res[3].IconURL)
}
