package liveblackcardgiftupgrade

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// BlackCardGiftUpgrade 黑卡钻石皮肤礼物升级表
type BlackCardGiftUpgrade struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`   // 单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"` // 单位：秒

	Level                  int    `gorm:"column:level"`                      // 黑卡钻石皮肤礼物对应的黑卡等级
	BaseGiftID             int64  `gorm:"column:base_gift_id"`               // 黑卡钻石皮肤基础礼物 ID
	BaseGiftSmallIcon      string `gorm:"column:base_gift_small_icon"`       // 黑卡钻石皮肤基础礼物栏图标。若字段为空，使用 MongoDB 数据库 gifts 文档的 icon 字段
	BlackCardGiftID        int64  `gorm:"column:black_card_gift_id"`         // 黑卡钻石皮肤礼物 ID
	BlackCardGiftSmallIcon string `gorm:"column:black_card_gift_small_icon"` // 黑卡钻石皮肤礼物栏图标。若字段为空，使用 MongoDB 数据库 gifts 文档的 icon 字段

	BaseGiftSmallIconURL      string `gorm:"-"`
	BlackCardGiftSmallIconURL string `gorm:"-"`
}

// GiftInfo 黑卡钻石皮肤礼物信息
type GiftInfo struct {
	GiftID        int64  `json:"gift_id"`
	Type          int    `json:"type"`
	Name          string `json:"name"`
	IconURL       string `json:"icon_url"`
	SmallIconURL  string `json:"small_icon_url"` // 礼物小图标
	Price         int64  `json:"price"`          // 黑卡开通价格，单位：钻石
	Comboable     int    `json:"comboable,omitempty"`
	IconActiveURL string `json:"icon_active_url,omitempty"` // 礼物被选中时显示的动画
}

// BlackCardInfo 黑卡信息
type BlackCardInfo struct {
	Level    int       `json:"level"`
	BaseGift *GiftInfo `json:"base_gift,omitempty"`
	Gift     *GiftInfo `json:"gift,omitempty"`
}

// TableName .
func (BlackCardGiftUpgrade) TableName() string {
	return "live_black_card_gift_upgrade"
}

// DB .
func (b BlackCardGiftUpgrade) DB() *gorm.DB {
	return service.LiveDB.Table(b.TableName())
}

// AfterFind gorm 钩子
func (b *BlackCardGiftUpgrade) AfterFind() error {
	b.BaseGiftSmallIconURL = storage.ParseSchemeURL(b.BaseGiftSmallIcon)
	b.BlackCardGiftSmallIconURL = storage.ParseSchemeURL(b.BlackCardGiftSmallIcon)
	return nil
}

// ListBlackCardGiftUpgrades 根据基础礼物 IDs 查询所有黑卡礼物信息
func ListBlackCardGiftUpgrades(baseGiftIDs []int64) ([]BlackCardGiftUpgrade, error) {
	var blackCardGiftUpgrades []BlackCardGiftUpgrade
	err := BlackCardGiftUpgrade{}.DB().Where("base_gift_id IN (?)", baseGiftIDs).Find(&blackCardGiftUpgrades).Error
	if err != nil {
		return nil, err
	}

	return blackCardGiftUpgrades, nil
}

// NewGiftInfo 构建黑卡礼物
func NewGiftInfo(gift gift.Gift, smallIconURL string) *GiftInfo {
	if smallIconURL == "" {
		smallIconURL = gift.Icon
	}
	return &GiftInfo{
		GiftID:        gift.GiftID,
		Type:          gift.Type,
		Name:          gift.Name,
		IconURL:       gift.Icon,
		SmallIconURL:  smallIconURL,
		Price:         gift.Price,
		Comboable:     gift.Comboable,
		IconActiveURL: gift.IconActive,
	}
}

// FindBlackCardGiftUpgrade 根据黑卡钻石礼物 ID 查询黑卡礼物升级信息
func FindBlackCardGiftUpgrade(blackCardGiftID int64) (*BlackCardGiftUpgrade, error) {
	var blackCardGiftUpgrade BlackCardGiftUpgrade
	err := blackCardGiftUpgrade.DB().Where("black_card_gift_id = ?", blackCardGiftID).
		Find(&blackCardGiftUpgrade).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &blackCardGiftUpgrade, nil
}
