package liveblackcardgiftupgrade

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(BlackCardGiftUpgrade{}, "id", "create_time", "modified_time", "level", "base_gift_id",
		"base_gift_small_icon", "black_card_gift_id", "black_card_gift_small_icon")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(GiftInfo{}, "gift_id", "type", "name", "icon_url", "small_icon_url", "price", "comboable", "icon_active_url")
	kc.Check(BlackCardInfo{}, "level", "base_gift", "gift")
	kc.CheckOmitEmpty(GiftInfo{}, "comboable", "icon_active_url")
	kc.CheckOmitEmpty(BlackCardInfo{}, "base_gift", "gift")
}

func TestBlackCardGiftUpgrade_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b := BlackCardGiftUpgrade{
		BaseGiftSmallIcon:      "oss://icon.png",
		BlackCardGiftSmallIcon: "oss://icon_1.png",
	}
	require.NoError(b.AfterFind())
	assert.Equal("https://static-test.missevan.com/icon.png", b.BaseGiftSmallIconURL)
	assert.Equal("https://static-test.missevan.com/icon_1.png", b.BlackCardGiftSmallIconURL)
}

func TestListBlackCardGiftUpgrades(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无黑卡信息
	res, err := ListBlackCardGiftUpgrades([]int64{111112})
	require.NoError(err)
	require.NotNil(res)
	assert.Len(res, 0)

	// 测试获取黑卡信息
	res, err = ListBlackCardGiftUpgrades([]int64{2333})
	require.NoError(err)
	require.NotNil(res)
	require.Len(res, 1)
	assert.Equal(1, res[0].Level)
	assert.EqualValues(2333, res[0].BaseGiftID)
	assert.Equal("oss://icon.png", res[0].BaseGiftSmallIcon)
	assert.EqualValues(3444, res[0].BlackCardGiftID)
	assert.EqualValues("oss://icon_1.png", res[0].BlackCardGiftSmallIcon)
}

func TestFindBlackCardGiftUpgrade(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试无黑卡信息
	res, err := FindBlackCardGiftUpgrade(111112)
	require.NoError(err)
	require.Nil(res)

	// 测试有黑卡信息
	res, err = FindBlackCardGiftUpgrade(3444)
	require.NoError(err)
	require.NotNil(res)
	assert.Equal(1, res.Level)
	assert.EqualValues(2333, res.BaseGiftID)
	assert.Equal("oss://icon.png", res.BaseGiftSmallIcon)
	assert.EqualValues(3444, res.BlackCardGiftID)
	assert.Equal("oss://icon_1.png", res.BlackCardGiftSmallIcon)
	assert.Equal("https://static-test.missevan.com/icon.png", res.BaseGiftSmallIconURL)
	assert.Equal("https://static-test.missevan.com/icon_1.png", res.BlackCardGiftSmallIconURL)
}
