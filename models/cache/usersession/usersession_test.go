package usersession

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKeys(t *testing.T) {
	assert := assert.New(t)

	kp := tutil.JSON
	assert.Empty(tutil.KeyExists(kp, UserSession{}, "guest", "user"))
	assert.Empty(tutil.KeyExists(kp, User{}, "id", "token", "expire_at", "username",
		"mobile", "region", "email", "iconurl", "confirm"))
}

func TestSessionFromCookie(t *testing.T) {
	assert := assert.New(t)

	r, _ := http.NewRequest("GET", "", nil)
	s, err := SessionFromCookie(r)
	assert.NoError(err)
	assert.Nil(s)

	r.AddCookie(&http.Cookie{Name: config.Conf.HTTP.SessionCookieName})
	s, err = SessionFromCookie(r)
	assert.NoError(err)
	assert.Nil(s)

	r, _ = http.NewRequest("GET", "", nil)
	r.AddCookie(&http.Cookie{Name: "TEST_SESS", Value: "test"})
	_, err = SessionFromCookie(r)
	assert.NoError(err)
}

func TestSessionFromCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s, err := SessionFromCache("test")
	assert.NoError(err)
	assert.Nil(s)

	key := config.Conf.HTTP.SessionPrefix + "test"
	require.NoError(service.LRURedis.Set(key, "{}", time.Minute).Err())
	s, err = SessionFromCache("test")
	assert.NoError(err)
	assert.Nil(s)

	require.NoError(service.LRURedis.Set(key, `{"user":{"id":12}}`, time.Minute).Err())
	s, err = SessionFromCache("test")
	require.NoError(err)
	require.True(s != nil && s.User != nil)
	assert.Equal(int64(12), s.User.ID)
}
