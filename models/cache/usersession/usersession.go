package usersession

import (
	"encoding/json"
	"net/http"

	"github.com/go-redis/redis/v7"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/guest"
	"github.com/MiaoSiLa/live-service/service"
)

// UserSession 直播间用户 session
type UserSession struct {
	Guest *guest.Guest `json:"guest,omitempty"`
	User  *User        `json:"user,omitempty"`
}

// User 注册用户
type User struct {
	ID       int64  `json:"id"`
	Token    string `json:"token"`
	ExpireAt int64  `json:"expire_at"` // 时间戳，单位：秒
	Username string `json:"username"`
	Mobile   string `json:"mobile,omitempty"`
	Region   int    `json:"region,omitempty"`
	Email    string `json:"email,omitempty"`
	IconURL  string `json:"iconurl,omitempty"`
	Confirm  int    `json:"confirm"`
}

// SessionFromCookie 从 cookie 中获取 session key, 进一步从缓存中获取 UserSession
func SessionFromCookie(r *http.Request) (*UserSession, error) {
	cookie, err := r.Cookie(config.Conf.HTTP.SessionCookieName)
	if err != nil || !(cookie != nil && cookie.Value != "") {
		// err 只可能是 http.ErrNoCookie
		return nil, nil
	}
	return SessionFromCache(cookie.Value)
}

// SessionFromCache 从 cache 获取 UserSession
func SessionFromCache(key string) (*UserSession, error) {
	key = config.Conf.HTTP.SessionPrefix + key

	val, err := service.LRURedis.Get(key).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}
	if val == "" {
		return nil, nil
	}
	s := new(UserSession)
	err = json.Unmarshal([]byte(val), s)
	if err != nil ||
		(s.User == nil && s.Guest == nil) {
		return nil, err
	}
	return s, nil
}
