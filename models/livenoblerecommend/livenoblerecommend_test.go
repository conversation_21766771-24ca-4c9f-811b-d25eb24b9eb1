package livenoblerecommend

import (
	"fmt"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest()
	service.SetDBUseSQLite()

	now := goutil.TimeNow().Unix()
	testNr.StartTime = StartTime(now) // 保证 testNr 是当前的神话推荐
	testNr.EndTime = EndTime(now + 7200)
	err := service.DB.Model(testNr).Updates(&testNr).Error
	if err != nil {
		logger.Error(err)
	}
	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(NobleRecommend{}, "id", "from_user_id", "creator_id", "room_id", "anonymous",
		"status", "recommeded_schedule_id", "start_time", "end_time", "create_time", "modified_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(NobleRecommend{}, "from_user_id", "creator_id", "room_id", "anonymous",
		"status", "start_time", "end_time", "create_time", "is_highness")
}

// 这条数据应该作为常开的选择
var testNr = NobleRecommend{
	ID:         1,
	FromUserID: 248506,
	CreatorID:  248506,
	RoomID:     130709352,
}

func TestFindRecommendList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := FindOptions{
		FromUserID:      testNr.FromUserID,
		CreatorUsername: "r",
		ID:              testNr.ID,
		RoomID:          testNr.RoomID,
		Sort:            CreateTimeAsc,
		FromUsername:    "r",
		P:               1,
		PageSize:        10,
		Status:          1}
	resAll, pa, err := FindRecommendList(opt)
	require.NoError(err)
	assert.GreaterOrEqual(int64(1), pa.Count)
	require.GreaterOrEqual(len(resAll), 1)
	assert.Equal(testNr.FromUserID, resAll[0].FromUserID)
	assert.Equal(testNr.RoomID, resAll[0].RoomID)
	assert.Equal("Ruii", resAll[0].FromUsername)
	assert.Equal("Ruii", resAll[0].CreatorUsername)
	opt.Status = 0
	resPending, _, err := FindRecommendList(opt)
	require.NoError(err)
	assert.NotEmpty(resPending)
	opt.Status = -1
	resExpired, _, err := FindRecommendList(opt)
	require.NoError(err)
	assert.Equal(len(resAll), len(resPending)+len(resExpired))
}

func TestAddOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var records []NobleRecommend
	require.NoError(addOrder(service.DB, false, CreateTimeDesc).Find(&records).Error)
	require.GreaterOrEqual(len(records), 2)
	assert.Greater(records[0].CreateTime, records[1].CreateTime)

	db := service.DB.Select("r.user_id").Table(TableName() + " AS r").Where("id = 1").Joins("LEFT JOIN user AS u ON u.id = r.user_id")
	db = addOrder(db, true, "empty")
	assert.Equal(gorm.Expr("SELECT r.user_id FROM live_noble_recommend AS r "+
		"LEFT JOIN user AS u ON u.id = r.user_id WHERE (id = 1) ORDER BY r.start_time DESC"),
		db.QueryExpr())
}

func TestCurrentRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := 248506
	uvKey := keys.KeyNobleUserVips1.Format(testUserID)
	require.NoError(service.Redis.Set(uvKey, `{"2":{"type":2,"level":1,"user_id":248506,"expire_time":9999999999}}`,
		30*time.Second).Err())
	fromDatabase, err := CurrentRecommend()
	require.NoError(err)
	assert.NotNil(fromDatabase)
	assert.True(fromDatabase.IsHighness)
	fromCache, err := CurrentRecommend()
	require.NoError(err)
	assert.NotNil(fromCache)
	assert.Equal(fromDatabase, fromCache)
	fromCache.ID++
	assert.NotEqual(fromDatabase, fromCache)

	now := goutil.TimeNow()
	key := keys.KeyNobleRecommend1.Format(now.Minute() / 10 * 10)
	service.Cache5Min.Set(key, nil, 0)
	fromCache, err = CurrentRecommend()
	require.NoError(err)
	assert.Nil(fromCache)

	// 测试没有推荐的情况
	defer goutil.SetTimeNow(nil)
	goutil.SetTimeNow(func() time.Time { return time.Unix(147852, 0) })
	service.Cache5Min.Flush()
	fromDatabase, err = CurrentRecommend()
	require.NoError(err)
	assert.Nil(fromDatabase)
	fromCache, err = CurrentRecommend()
	require.NoError(err)
	assert.Nil(fromCache)
}

func TestFindByFromUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	r, _, err := FindByFromUser(testNr.FromUserID, 1, 20)
	require.NoError(err)
	require.NotEmpty(r)
	assert.NotEmpty(r[0].CreatorIconURL)
	r, _, err = FindByFromUser(testNr.FromUserID, 10, 20)
	require.NoError(err)
	assert.Empty(r)
}

func TestKeyCurrentRecommend(t *testing.T) {
	require := assert.New(t)
	when := time.Unix(180, 0) // 初始 3 分钟
	for i := 0; i < 5; i++ {
		require.Equal(fmt.Sprintf("nobleRecommend%d0", i), KeyCurrentRecommend(when))
		when = when.Add(5 * time.Minute)
		require.Equal(fmt.Sprintf("nobleRecommend%d0", i), KeyCurrentRecommend(when))
		when = when.Add(5 * time.Minute)
	}
}

func TestFindWithNameUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := FindWithNameUserInfo(2)
	require.NoError(err)
	assert.Equal(int64(2), res.ID)
	assert.Equal(int64(12), res.FromUserID)
	assert.Equal(int64(10), res.CreatorID)
	assert.Equal(int64(12345), res.RoomID)
	assert.Equal(0, res.Anonymous)
	assert.Equal(int64(2), res.ID)
	assert.Equal(StatusNormal, res.Status)
	assert.Equal("零月", res.FromUsername)
	assert.Equal("bless", res.CreatorUsername)

	// 测试数据不存在情况
	res, err = FindWithNameUserInfo(222)
	require.NoError(err)
	assert.Nil(res)
}

func TestFindRecommender(t *testing.T) {
	assert := assert.New(t)

	// 不存在的用户匿名推荐
	n := NobleRecommend{FromUserID: -9999, Anonymous: 1}
	r := n.FindRecommender()
	assert.Equal("神秘人", r.Username)
	assert.NotEmpty(r.RecommendFrameURL)
	assert.NotEmpty(r.RecommendAvatarFrameURL)
	// 不存在的用户推荐
	n = NobleRecommend{FromUserID: -9999, Anonymous: 0}
	r = n.FindRecommender()
	assert.Equal("神秘人", r.Username)
	// 正常用户匿名推荐
	n = NobleRecommend{FromUserID: 10, Anonymous: 1}
	r = n.FindRecommender()
	assert.Equal("神秘人", r.Username)
	// 正常用户非匿名推荐
	n = NobleRecommend{FromUserID: 3456835, Anonymous: 0}
	r = n.FindRecommender()
	assert.Equal("我的力量无人能及", r.Username)
	assert.NotEmpty(r.RecommendFrameURL)
	assert.NotEmpty(r.RecommendAvatarFrameURL)
}

func TestIsFreeTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok, err := IsFreeTime(RecommendDuration * 10)
	require.NoError(err)
	assert.True(ok)
	ok, err = IsFreeTime(testNr.StartTime, service.DB)
	require.NoError(err)
	assert.False(ok)
}

func TestStartTimeEndTime(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int64(0), StartTime(100))
	assert.Equal(int64(0), StartTime(0))
	assert.Equal(int64(1800), EndTime(0))
	assert.Equal(int64(1800), EndTime(100))
}

func TestRecommendTime(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("1970-01-01 08:00 至 08:30", RecommendTime(0, RecommendDuration))
	assert.Equal("1970-01-01 23:30 至 1970-01-02 00:00", RecommendTime(55800, 55800+RecommendDuration))
}

func TestFindByRecommendTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := FindByRecommendTime(testNr.StartTime, testNr.StartTime+1)
	require.NoError(err)
	assert.NotEmpty(res)
	res, err = FindByRecommendTime(testNr.StartTime, testNr.StartTime)
	require.NoError(err)
	assert.Empty(res)
	res, err = FindByRecommendTime(testNr.EndTime-1, testNr.EndTime)
	require.NoError(err)
	assert.NotEmpty(res)
	res, err = FindByRecommendTime(testNr.EndTime, testNr.EndTime)
	require.NoError(err)
	assert.Empty(res)
	assert.NotNil(res)
	st := testNr.EndTime/2 + testNr.StartTime/2
	et := st
	res, err = FindByRecommendTime(st, et)
	require.NoError(err)
	assert.NotEmpty(res)
	st = testNr.StartTime - 1
	et = testNr.EndTime + 1
	res, err = FindByRecommendTime(st, et)
	require.NoError(err)
	assert.NotEmpty(res)
}
