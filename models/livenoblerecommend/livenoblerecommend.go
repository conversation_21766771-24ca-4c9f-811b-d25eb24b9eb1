package livenoblerecommend

import (
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// RecommendDuration 每次推荐的时长
const RecommendDuration = 1800

// FindOptions Find 选项
type FindOptions struct {
	ID              int64  `form:"id"`
	Sort            string `form:"-"`
	RoomID          int64  `form:"room_id"`
	FromUsername    string `form:"from_username"`
	CreatorUsername string `form:"creator_username"`
	Status          int    `form:"status"`

	P          int64 `form:"-"`
	PageSize   int64 `form:"-"`
	FromUserID int64 `form:"-"`
}

// FindOptions Status emum
const (
	OptStatusExpired = iota - 1
	OptStatusPending
	OptStatusAll
)

// Cancel status values
const (
	StatusNormal = iota
	StatusCancel
)

// sort
const (
	StartTimeAsc   = "start_time ASC"
	StartTimeDesc  = "start_time DESC"
	CreateTimeAsc  = "create_time ASC"
	CreateTimeDesc = "create_time DESC"
)

var (
	validSorts = [4]string{StartTimeAsc, StartTimeDesc, CreateTimeAsc, CreateTimeDesc}
)

// NobleRecommend of table live_noble_recommend
type NobleRecommend struct {
	ID                   int64 `gorm:"column:id" json:"-"`
	FromUserID           int64 `gorm:"column:from_user_id" json:"from_user_id"` // 推荐人 ID
	CreatorID            int64 `gorm:"column:creator_id" json:"creator_id"`     // 推荐的主播 ID
	RoomID               int64 `gorm:"column:room_id" json:"room_id"`           // 被推荐的房间 ID
	Anonymous            int   `gorm:"column:anonymous" json:"anonymous"`       // 匿名推荐
	Status               int   `gorm:"column:status" json:"status"`             // 是否取消 0：未取消 1：取消
	RecommededScheduleID int64 `gorm:"column:recommeded_schedule_id" json:"-"`  // 开播推荐 ID
	StartTime            int64 `gorm:"column:start_time" json:"start_time"`
	EndTime              int64 `gorm:"column:end_time" json:"end_time"`

	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`

	IsHighness bool `gorm:"-" json:"is_highness"`
}

const tableName = "live_noble_recommend"

// TableName of NobleRecommend
func TableName() string {
	return tableName
}

// TableName table name
func (NobleRecommend) TableName() string {
	return TableName()
}

// BeforeCreate automatically sets columns create_time and modified_time
func (n *NobleRecommend) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	n.CreateTime = now
	n.ModifiedTime = now

	return nil
}

// WithUserInfo LiveReview with user info
type WithUserInfo struct {
	NobleRecommend
	FromUsername    string `gorm:"column:from_username" json:"from_username"`
	CreatorUsername string `gorm:"column:creator_username" json:"creator_username"`
	CreatorIconURL  string `gorm:"-" json:"creator_iconurl,omitempty"` // 用户头像无法直接从数据库获取
	RecommendTime   string `gorm:"-" json:"recommend_time"`
}

// FindRecommendList 查找贵族推荐列表
// 默认创建时间倒序排序
// NOTICE: 未查询主播头像
func FindRecommendList(opt FindOptions) ([]*WithUserInfo, goutil.Pagination, error) {
	db := service.DB.Table(TableName() + " AS r").
		Select("uf.username as from_username, uc.username as creator_username, r.*").
		Joins(fmt.Sprintf("LEFT JOIN %s AS uf ON uf.id = r.from_user_id", mowangskuser.TableName())).
		Joins(fmt.Sprintf("LEFT JOIN %s AS uc ON uc.id = r.creator_id", mowangskuser.TableName()))
	db = addOrder(db, true, opt.Sort)
	if opt.ID != 0 {
		db = db.Where("r.id = ?", opt.ID)
	}
	if opt.RoomID != 0 {
		db = db.Where("r.room_id = ?", opt.RoomID)
	}

	if opt.FromUsername != "" {
		db = db.Where("uf.username LIKE ?", servicedb.ToLikeStr(opt.FromUsername))
	}
	if opt.CreatorUsername != "" {
		db = db.Where("uc.username LIKE ?", servicedb.ToLikeStr(opt.CreatorUsername))
	}
	if opt.FromUserID != 0 {
		db = db.Where("r.from_user_id = ?", opt.FromUserID)
	}
	now := goutil.TimeNow()
	switch opt.Status {
	case OptStatusExpired:
		db = db.Where("end_time <= ?", now.Unix())
	case OptStatusPending:
		db = db.Where("end_time > ?", now.Unix())
	case OptStatusAll:
	default:
		panic(fmt.Sprintf("unknown status filter: %d", opt.Status))
	}

	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, opt.P, opt.PageSize)
	if !pa.Valid() {
		return []*WithUserInfo{}, pa, nil
	}
	var res []*WithUserInfo
	db = pa.ApplyTo(db).Find(&res)
	for i := range res {
		res[i].RecommendTime = RecommendTime(res[i].StartTime, res[i].EndTime)
	}
	return res, pa, db.Error
}

// WithNameUserInfo 带用户信息的推荐
type WithNameUserInfo struct {
	ID                   int64  `gorm:"column:id"`
	FromUserID           int64  `gorm:"column:from_user_id"`           // 推荐人 ID
	CreatorID            int64  `gorm:"column:creator_id"`             // 推荐的主播 ID
	RoomID               int64  `gorm:"column:room_id"`                // 被推荐的房间 ID
	Anonymous            int    `gorm:"column:anonymous"`              // 匿名推荐
	Status               int    `gorm:"column:status"`                 // 是否取消 0：未取消 1：取消
	RecommededScheduleID int64  `gorm:"column:recommeded_schedule_id"` // 开播推荐 ID
	StartTime            int64  `gorm:"column:start_time"`
	EndTime              int64  `gorm:"column:end_time"`
	FromUsername         string `gorm:"column:from_username"`
	CreatorUsername      string `gorm:"column:creator_username"`

	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
}

// FindWithNameUserInfo 通过推荐 ID 返回带用户信息的推荐
func FindWithNameUserInfo(recommendID int64) (*WithNameUserInfo, error) {
	var res WithNameUserInfo
	db := service.DB.Table(TableName()).
		Select(fmt.Sprintf("uf.username as from_username, uc.username as creator_username, %s.*", TableName())).
		Joins(fmt.Sprintf("LEFT JOIN %s AS uf ON uf.id = %s.from_user_id",
			mowangskuser.TableName(), TableName())).
		Joins(fmt.Sprintf("LEFT JOIN %s AS uc ON uc.id = %s.creator_id",
			mowangskuser.TableName(), TableName()))
	db = db.Where(TableName()+".id = ?", recommendID)
	if err := db.First(&res).Error; err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &res, nil
}

// TODO: 添加默认排序参数并提取到工具包中
func addOrder(db *gorm.DB, joined bool, sorts ...string) *gorm.DB {
	valids := make([]string, 0, len(sorts))
	prefix := ""
	if joined {
		prefix = "r."
	}
	for i := 0; i < len(sorts); i++ {
		for j := 0; j < len(validSorts); j++ {
			if sorts[i] == validSorts[j] {
				valids = append(valids, prefix+sorts[i])
				break
			}
		}
	}
	if len(valids) == 0 {
		valids = []string{prefix + StartTimeDesc}
	}
	for i := 0; i < len(valids); i++ {
		db = db.Order(valids[i])
	}
	return db
}

// KeyCurrentRecommend 获取当前推荐房间缓存 key
func KeyCurrentRecommend(now time.Time) string {
	return keys.KeyNobleRecommend1.Format(now.Minute() / 10 * 10)
}

// CurrentRecommend 当前生效的神话推荐，已判断元气值
// 其他包测试假数据往本地缓存中设置修改
func CurrentRecommend() (*NobleRecommend, error) {
	now := goutil.TimeNow()
	key := KeyCurrentRecommend(now)
	cache := service.Cache5Min
	v, ok := cache.Get(key)
	if ok {
		if v != nil {
			res := *v.(*NobleRecommend) // 复制数据
			return &res, nil
		}
		return nil, nil
	}
	nowUnix := now.Unix()
	var res NobleRecommend
	err := service.DB.Where("status = ? AND start_time <= ? AND end_time > ?", StatusNormal, nowUnix, nowUnix).First(&res).Error
	if err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			return nil, err
		}
		cache.Set(key, nil, 0)
		return nil, nil
	}
	vitality, err := liveaddendum.Vitality(res.CreatorID)
	if err != nil {
		return nil, err
	}
	if vitality == nil || *vitality <= 6 {
		// 当前推荐主播元气值不足 6，虽然有推荐但是不成功显示
		cache.Set(key, nil, 0)
		return nil, nil
	}
	// 查询推荐用户的贵族状态
	uv, err := vip.UserActivatedVip(res.FromUserID, false, nil)
	if err != nil {
		return nil, nil
	}
	res.IsHighness = uv != nil && uv.Type == vip.TypeLiveHighness

	store := res // 复制数据
	cache.Set(key, &store, 0)
	return &res, nil
}

// FindByFromUser 通过推荐人查询，创建时间倒序，已查询主播头像
func FindByFromUser(fromUserID int64, p, pageSize int64) ([]*WithUserInfo, goutil.Pagination, error) {
	opt := FindOptions{
		FromUserID: fromUserID,
		Sort:       CreateTimeDesc,
		P:          p,
		PageSize:   pageSize,
		Status:     OptStatusAll,
	}
	res, pa, err := FindRecommendList(opt)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	if len(res) == 0 {
		return res, pa, nil
	}
	userIDs := make([]int64, len(res))
	for i := range res {
		userIDs[i] = res[i].CreatorID
	}
	userIDs = util.Uniq(userIDs)
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		logger.Error(err)
		// 找不到头像也返回
		return res, pa, nil
	}
	for i := range res {
		if u := users[res[i].CreatorID]; u != nil {
			res[i].CreatorIconURL = u.IconURL
		}
	}
	return res, pa, nil
}

// FindRecommender 获取 recommender
func (n *NobleRecommend) FindRecommender() (recommender *liveuser.Simple) {
	var err error
	defer func() {
		if recommender == nil {
			recommender = &liveuser.Simple{
				IconURL:  service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon),
				Username: "神秘人",
			}
		}
		if n.IsHighness {
			// 上神用户使用专有推荐头像框
			recommender.RecommendFrameURL = service.Storage.Parse(config.Conf.Params.NobleParams.RecommendWebHighnessFrame)
			recommender.RecommendAvatarFrameURL = service.Storage.Parse(config.Conf.Params.NobleParams.RecommendHighnessAvatarFrame)
		} else {
			recommender.RecommendFrameURL = service.Storage.Parse(config.Conf.Params.NobleParams.RecommendWebFrame)
			recommender.RecommendAvatarFrameURL = service.Storage.Parse(config.Conf.Params.NobleParams.RecommendAvatarFrame)
		}
	}()

	if n.Anonymous == 0 {
		recommender, err = liveuser.FindOneSimple(bson.M{"user_id": n.FromUserID}, nil,
			options.FindOne().SetProjection(bson.M{"user_id": 1, "username": 1, "iconurl": 1}))
		if err != nil {
			// 出错了降级成神秘人推荐
			logger.Error(err)
			// PASS
		}
	}
	return
}

// IsFreeTime 是否是空闲可预约的时间
func IsFreeTime(startTime int64, tx ...*gorm.DB) (bool, error) {
	var db *gorm.DB
	if len(tx) > 0 {
		db = tx[0]
	} else {
		db = service.DB
	}
	endTime := startTime + RecommendDuration
	var nr NobleRecommend
	err := db.Take(&nr, "start_time < ? AND end_time > ? AND status = ?", endTime, startTime, StatusNormal).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return true, nil
		}
		return false, err
	}
	return false, nil
}

// StartTime 根据传入时间计算传入时间所在推荐时段的结束时间
func StartTime(when int64) int64 {
	return (when / RecommendDuration) * RecommendDuration
}

// EndTime 根据传入时间计算传入时间所在推荐时段的结束时间
func EndTime(when int64) int64 {
	return (when/RecommendDuration + 1) * RecommendDuration
}

// RecommendTime 推荐时间文案
func RecommendTime(startTime, endTime int64) string {
	st := time.Unix(startTime, 0)
	et := time.Unix(endTime, 0)
	dhm := "2006-01-02 15:04"
	stStr := st.Format(dhm)
	etStr := et.Format(dhm)
	etStr = strings.TrimPrefix(etStr, stStr[:11]) // 去掉 etStr 年月日相同的部分
	return fmt.Sprintf("%s 至 %s", stStr, etStr)
}

// FindByRecommendTime 查找推荐时间中的推荐
func FindByRecommendTime(startTime, endTime int64) ([]*NobleRecommend, error) {
	var nr []*NobleRecommend
	err := service.DB.Find(&nr, "status = ? AND start_time < ? AND end_time > ?", StatusNormal, endTime, startTime).Error
	return nr, err
}
