package widget

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestWidgetParamWaitTime(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1647359980, 0)
	})
	defer goutil.SetTimeNow(nil)

	refreshDuration := NewWidget(goutil.TimeNow().Unix()).waitTime(1647360000)
	assert.LessOrEqual(RefreshDuration, refreshDuration)
}

func TestBuildNotStartWidget(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1632412800, 0)
	})
	defer goutil.SetTimeNow(nil)

	w := NewWidget(goutil.TimeNow().Unix())
	w.BuildNotStartWidget(1632412800 + 10)
	assert.Equal(StatusNotStart, w.Status)
	assert.EqualValues(1632412800, w.TS)
	assert.LessOrEqual(int64(10000), w.RefreshDuration)
}

func TestBuildWarmUpWidget(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1632412800, 0)
	})
	defer goutil.SetTimeNow(nil)

	w := NewWidget(goutil.TimeNow().Unix())
	w.BuildWarmUpWidget(1632412800 + 10)
	assert.Equal(StatusWarmUp, w.Status)
	assert.EqualValues(1632412800, w.TS)
	assert.LessOrEqual(int64(10000), w.RefreshDuration)
}

func TestBuildOnGoingWidget(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1632412800, 0)
	})
	defer goutil.SetTimeNow(nil)

	w := NewWidget(goutil.TimeNow().Unix())
	w.BuildOnGoingWidget(16324128000)
	assert.Equal(&Widget{
		Status:          StatusOnGoing,
		TS:              goutil.TimeNow().Unix(),
		RefreshDuration: RefreshDuration,
	}, w)
}

func TestBuildCompletedWidget(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	w := NewWidget(now)
	w.BuildCompletedWidget(now)
	assert.Equal(StatusCompleted, w.Status)
	assert.Equal(now, w.TS)
}

func TestBuildEndingWidget(t *testing.T) {
	assert := assert.New(t)

	w := NewWidget(goutil.TimeNow().Unix())
	w.BuildEndingWidget()
	assert.Equal(&Widget{
		Status: StatusEnding,
		TS:     goutil.TimeNow().Unix(),
	}, w)
}

func TestBuildNotQualifiedWidget(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1632412800, 0)
	})
	defer goutil.SetTimeNow(nil)

	w := NewWidget(goutil.TimeNow().Unix())
	w.BuildNotQualifiedWidget(1632412800 + 10)
	assert.Equal(StatusNotQualified, w.Status)
	assert.EqualValues(1632412800, w.TS)
	assert.LessOrEqual(int64(10000), w.RefreshDuration)
}
