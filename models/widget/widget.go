package widget

import "math/rand"

// RefreshDuration 小窗刷新时间 毫秒
const RefreshDuration int64 = 2e4

// 小窗状态
const (
	StatusNotStart     = iota // 未开始
	StatusOnGoing             // 进行中
	StatusEnding              // 活动结束
	StatusCompleted           // 活动已达成目标，如奖池瓜分完毕、奖励领取完毕
	StatusNotQualified        // 无资格
	StatusWarmUp              // 预热
)

// Widget 小窗
type Widget struct {
	Status          int   `json:"status"`
	TS              int64 `json:"ts"`
	RefreshDuration int64 `json:"refresh_duration,omitempty"`
}

// NewWidget 创建小窗
func NewWidget(now int64) *Widget {
	return &Widget{TS: now}
}

func (param *Widget) build(status int, refreshDuration int64) {
	param.Status = status
	param.RefreshDuration = refreshDuration
}

func (param *Widget) waitTime(refreshTime int64) (refreshDuration int64) {
	// 错峰请求，随机 0 ~ 5s，要在榜单结束后刷新
	refreshDuration = (refreshTime-param.TS)*1000 + rand.Int63n(5e3)
	return
}

// BuildNotStartWidget 未开始小窗
func (param *Widget) BuildNotStartWidget(rankStartTime int64) {
	ref := param.waitTime(rankStartTime)
	param.build(StatusNotStart, ref)
}

// BuildWarmUpWidget 预热小窗
func (param *Widget) BuildWarmUpWidget(rankStartTime int64) {
	ref := param.waitTime(rankStartTime)
	if (rankStartTime-param.TS)*1000 > RefreshDuration {
		ref = RefreshDuration
	}
	param.build(StatusWarmUp, ref)
}

// BuildOnGoingWidget 进行中小窗
func (param *Widget) BuildOnGoingWidget(rankEndTime int64) {
	ref := param.waitTime(rankEndTime)
	// 榜单结束时间剩余超过 20s 刷新时间就下发 20s，要是没超过，就下发剩余时间加 5s 随机数
	// 防止切换赛程时，有的在用户最后几秒刷新，就会导致榜单结束十多秒后才会切换赛程
	if (rankEndTime-param.TS)*1000 > RefreshDuration {
		ref = RefreshDuration
	}
	param.build(StatusOnGoing, ref)
}

// BuildCompletedWidget 达成目标小窗
func (param *Widget) BuildCompletedWidget(rankEndTime int64) {
	ref := param.waitTime(rankEndTime)
	param.build(StatusCompleted, ref)
}

// BuildEndingWidget 活动结束小窗
func (param *Widget) BuildEndingWidget() {
	param.build(StatusEnding, 0)
}

// BuildNotQualifiedWidget 无资格小窗
func (param *Widget) BuildNotQualifiedWidget(rankEndTime int64) {
	ref := param.waitTime(rankEndTime)
	param.build(StatusNotQualified, ref)
}
