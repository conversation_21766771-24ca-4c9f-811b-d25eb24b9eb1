package models

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMessageTagKey(t *testing.T) {
	var m Message

	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(m, "_id", "_room_id",
		"room_id", "msg_id", "status", "user_id", "message", "bubble", "sticker", "danmaku", "ip", "create_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(m, "room_id", "msg_id", "status",
		"user_id", "message", "bubble", "create_time")
}

func TestMessageMarshalJSON(t *testing.T) {
	require := require.New(t)
	var m Message
	m.CreateTime = time.Unix(1e9, 0)
	val, err := json.Marshal(m)
	require.NoErrorf(err, "Marshal failed: %v", err)

	var createTime struct {
		CreateTime int64 `json:"create_time"`
	}
	err = json.Unmarshal(val, &createTime)
	require.NoErrorf(err, "Unmarshal failed: %v", err)
	require.Equal(int64(1e12), createTime.CreateTime, "Restore failed")
}

func TestMessageInsert(t *testing.T) {
	require := require.New(t)
	m := Message{
		RoomID: 123456,
		MsgID:  "testtest",
	}
	beforeInsert := goutil.TimeNow().Add(-time.Second)
	_, err := m.Insert()
	require.NoError(err)
	require.NotEqual(primitive.NilObjectID, m.OID)
	require.True(m.CreateTime.After(beforeInsert))

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := service.MongoDB.Collection("messages")
	require.NoError(col.FindOneAndDelete(ctx,
		bson.M{"_id": m.OID, "room_id": m.RoomID, "msg_id": m.MsgID}).Err())
}
