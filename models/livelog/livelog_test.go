package livelog

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(Record{}, "_id", "_room_id", "room_id", "creator_id", "catalog_id", "custom_tag_id",
		"guild_id", "start_time", "end_time", "duration", "revenue", "accumulation",
		"question_count", "message_count", "new_fans_count", "new_medal_count", "paid_user_count", "max_score", "avg_score")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Record{}, "_id", "_room_id", "room_id", "creator_id", "catalog_id",
		"guild_id", "start_time", "end_time", "duration", "revenue", "accumulation",
		"question_count", "message_count", "catalog_name", "max_score", "avg_score")
}

func TestGetLiveLog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	data, fromTime, toTime, _, guildID, err := getTestData(t)
	require.NoError(err)

	roomID := data[0].RoomOID
	// 测试获取直播间所有直播记录
	results, err := GetRoomLogs(roomID, fromTime, toTime)
	require.NoError(err)
	assert.Equal(data[2], results[0])
	assert.Len(results, 3)

	// 测试获取直播间最新直播记录
	results, err = GetRoomLogsByPage(roomID, fromTime, toTime, 0, goutil.MakePagination(1, 1, 1))
	require.NoError(err)
	assert.Equal(data[2], results[0])

	// 测试获取直播间指定公会直播记录
	results, err = GetRoomLogsByPage(roomID, fromTime, toTime, guildID, goutil.MakePagination(3, 1, 3))
	require.NoError(err)
	assert.Len(results, 2)
	assert.Equal(data[1], results[1])
	assert.Equal(data[2], results[0])

	// 测试获取直播间所有直播记录条数
	pageCount, err := CountRoomLogs(roomID, fromTime, toTime, 0)
	require.NoError(err)
	assert.EqualValues(3, pageCount)
}

func TestRoomTotalRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	data, fromTime, toTime, _, _, err := getTestData(t)
	require.NoError(err)
	require.NotEmpty(data)
	roomID := data[0].RoomOID

	count, err := RoomTotalRevenue(roomID, fromTime, toTime)
	require.NoError(err)
	assert.Equal(int64(18), count)
}

func TestCountDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := Collection().FindOneAndReplace(ctx, bson.M{"room_id": 223355, "catalog_id": 116},
		bson.M{
			"room_id":    223355,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2020, 11, 6, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2020, 11, 9, 0, 0, 0, 0, time.Local)),
			"duration":   108000000,
			"creator_id": 223355,
			"catalog_id": 116,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)
	st := goutil.NewTimeUnixMilli(time.Date(2020, 11, 6, 0, 0, 0, 0, time.Local))
	et := goutil.NewTimeUnixMilli(time.Date(2020, 11, 10, 0, 0, 0, 0, time.Local))

	scale, err := CountDuration(223355, 116, st, et)
	require.NoError(err)
	assert.NotNil(scale)
}

func TestLiveTotalDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := Collection().FindOneAndReplace(ctx, bson.M{"room_id": 2233556, "guild_id": 2333, "duration": 108000000},
		bson.M{
			"room_id":    2233556,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2012, 11, 6, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2012, 11, 9, 0, 0, 0, 0, time.Local)),
			"duration":   108000000,
			"creator_id": 2233556,
			"guild_id":   2333,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	err = Collection().FindOneAndReplace(ctx, bson.M{"room_id": 2233556, "guild_id": 23, "duration": 108000001},
		bson.M{
			"room_id":    2233556,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2012, 11, 6, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2012, 11, 9, 0, 0, 0, 0, time.Local)),
			"duration":   108000001,
			"creator_id": 2233556,
			"guild_id":   23,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	st := time.Date(2012, 11, 6, 0, 0, 0, 0, time.Local)
	et := time.Date(2012, 11, 10, 0, 0, 0, 0, time.Local)

	// guildID != 0
	duration, err := LiveTotalDuration(2233556, 2333, st, et)
	require.NoError(err)
	assert.Equal(int64(108000000), duration)

	// guildID = 0
	duration, err = LiveTotalDuration(2233556, 0, st, et)
	require.NoError(err)
	assert.Equal(int64(216000001), duration)
}

func TestLogClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := Record{
		RoomOID:       primitive.NilObjectID,
		RoomID:        123,
		CreatorID:     123,
		CatalogID:     123,
		GuildID:       123,
		StartTime:     1230,
		EndTime:       1234,
		Duration:      4,
		Revenue:       1,
		Accumulation:  2,
		QuestionCount: 3,
		MessageCount:  4,
		AvgScore:      5,
		MaxScore:      6,
	}

	// 有开播日志的情况
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	res, err := col.InsertOne(ctx, bson.M{
		"_room_id":   l.OID,
		"room_id":    l.RoomID,
		"start_time": l.StartTime,
		"end_time":   0,
	})
	require.NoError(err)
	require.NoError(l.LogClose())
	var after Record
	require.NoError(col.FindOne(ctx,
		bson.M{"_id": res.InsertedID}).Decode(&after))
	assert.Equal(after, l)

	// 没有开播日志的情况
	_, err = col.DeleteMany(ctx, bson.M{"room_id": l.RoomID})
	l.OID = primitive.NilObjectID
	require.NoError(err)
	require.NoError(l.LogClose())
	assert.NotEqual(primitive.NilObjectID, l.OID)
}
