package livelog

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Record of live_logs
type Record struct {
	OID           primitive.ObjectID   `bson:"_id,omitempty" json:"_id"`
	RoomOID       primitive.ObjectID   `bson:"_room_id" json:"_room_id"`
	RoomID        int64                `bson:"room_id" json:"room_id"`
	CreatorID     int64                `bson:"creator_id" json:"creator_id"`
	CatalogID     int64                `bson:"catalog_id" json:"catalog_id"`
	CustomTagID   int64                `bson:"custom_tag_id" json:"-"`
	GuildID       int64                `bson:"guild_id,omitempty" json:"guild_id"`
	StartTime     goutil.TimeUnixMilli `bson:"start_time" json:"start_time"`
	EndTime       goutil.TimeUnixMilli `bson:"end_time" json:"end_time"` // end_time 为 0 的数据是开播中的数据
	Duration      int64                `bson:"duration" json:"duration"`
	Revenue       int64                `bson:"revenue" json:"revenue"`
	Accumulation  int64                `bson:"accumulation" json:"accumulation"`
	QuestionCount int64                `bson:"question_count" json:"question_count"`
	MessageCount  int64                `bson:"message_count" json:"message_count"`
	NewFansCount  *int64               `bson:"new_fans_count,omitempty" json:"-"`
	NewMedalCount *int64               `bson:"new_medal_count,omitempty" json:"-"`
	PaidUserCount *int64               `bson:"paid_user_count,omitempty" json:"-"`
	AvgScore      int64                `bson:"avg_score" json:"avg_score"`
	MaxScore      int64                `bson:"max_score" json:"max_score"`

	CatalogName string `bson:"-" json:"catalog_name,omitempty"`
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection("live_logs")
}

// GetRoomLogs 获取房间直播日志
func GetRoomLogs(roomOID primitive.ObjectID, fromTime, toTime time.Time) ([]Record, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cursor, err := collection.Find(ctx, bson.M{
		"_room_id": roomOID,
		"start_time": bson.M{
			"$gte": util.TimeToUnixMilli(fromTime),
			"$lt":  util.TimeToUnixMilli(toTime),
		},
		"end_time": bson.M{"$gt": 0},
	}, options.Find().SetSort(bson.M{"start_time": -1}))
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []Record
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, err
	}
	return results, nil
}

// RoomTotalRevenue 获取直播房间 [fromTime, toTime) 时间区间段的总收益
func RoomTotalRevenue(roomOID primitive.ObjectID, fromTime, toTime time.Time) (int64, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cursor, err := collection.Aggregate(ctx, bson.A{
		bson.M{
			"$match": bson.M{
				"start_time": bson.M{
					"$gte": util.TimeToUnixMilli(fromTime),
					"$lt":  util.TimeToUnixMilli(toTime),
				},
				"_room_id": roomOID,
				"end_time": bson.M{"$gt": 0},
			},
		},
		bson.M{
			"$group": bson.M{
				"_id": nil,
				"total_revenue": bson.M{
					"$sum": "$revenue",
				},
			},
		},
	}, nil)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var res []struct {
		TotalRevenue int64 `bson:"total_revenue"`
	}
	err = cursor.All(ctx, &res)
	if err != nil {
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}

	return res[0].TotalRevenue, nil
}

// GetRoomLogsByPage 获取房间直播日志（分页）
func GetRoomLogsByPage(roomOID primitive.ObjectID, fromTime, toTime time.Time, guildID int64, pa goutil.Pagination) ([]Record, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	match := bson.M{
		"_room_id": roomOID,
		"start_time": bson.M{
			"$gte": util.TimeToUnixMilli(fromTime),
			"$lt":  util.TimeToUnixMilli(toTime),
		},
		"end_time": bson.M{"$gt": 0},
	}
	if guildID != 0 {
		match["guild_id"] = guildID
	}
	cursor, err := collection.Find(ctx, match, pa.SetFindOptions(options.Find().SetSort(bson.M{"start_time": -1})))
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var results []Record
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, err
	}
	return results, nil
}

// CountRoomLogs 返回房间直播日志的数量
func CountRoomLogs(roomOID primitive.ObjectID, fromTime, toTime time.Time, guildID int64) (int64, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	match := bson.M{
		"_room_id": roomOID,
		"start_time": bson.M{
			"$gte": util.TimeToUnixMilli(fromTime),
			"$lt":  util.TimeToUnixMilli(toTime),
		},
		"end_time": bson.M{"$gt": 0},
	}
	if guildID != 0 {
		match["guild_id"] = guildID
	}
	counts, err := collection.CountDocuments(ctx, match)
	return counts, err
}

// LiveDuration 直播时长
type LiveDuration struct {
	CreatorID int64   `bson:"creator_id"`
	Duration  float64 `bson:"duration"`
	Total     float64 `bson:"total"`
}

// CountDuration 根据房间 ID 和分区 ID 查询直播总时长和在某个分区直播时长
func CountDuration(roomID, catalogID int64, st, et goutil.TimeUnixMilli) (*LiveDuration, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	type m bson.M
	type a bson.A
	cur, err := Collection().Aggregate(ctx, []m{
		{"$match": m{"room_id": roomID, "start_time": m{"$gte": st}, "end_time": m{"$lt": et, "$gt": 0}}},
		{"$group": m{
			"_id":   "$creator_id",
			"total": m{"$sum": "$duration"},
			// TODO: 如果 catalogID 是 0，不查 duration
			"duration": m{"$sum": m{"$cond": a{m{"$eq": a{"$catalog_id", catalogID}}, "$duration", 0}}},
		}},
		{"$project": m{"creator_id": "$_id", "duration": "$duration", "total": "$total"}},
	})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)

	if !cur.Next(ctx) {
		return nil, nil
	}

	scale := new(LiveDuration)
	err = cur.Decode(&scale)
	if err != nil {
		return nil, err
	}
	return scale, nil
}

// LiveTotalDuration 根据 creatorID、guildID，获取在范围时间内开播的总直播时长（毫秒）
// guildID <> 0 获取主播在 guildID 公会的总直播时长
// guildID = 0 获取主播总直播时长
func LiveTotalDuration(creatorID, guildID int64, st, et time.Time) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	type m bson.M

	match := bson.M{
		"creator_id": creatorID,
		"start_time": bson.M{
			"$gte": util.TimeToUnixMilli(st),
			"$lt":  util.TimeToUnixMilli(et),
		},
		"end_time": bson.M{"$gt": 0},
	}
	if guildID != 0 {
		match["guild_id"] = guildID
	}

	cur, err := Collection().Aggregate(ctx, []m{
		{"$match": match},
		{
			"$group": m{
				"_id":      "$creator_id",
				"duration": m{"$sum": "$duration"},
			},
		},
		{"$project": m{"duration": "$duration"}},
	})
	if err != nil {
		return 0, err
	}
	defer cur.Close(ctx)

	var res []struct {
		Duration int64 `bson:"duration"`
	}
	err = cur.All(ctx, &res)
	if err != nil {
		return 0, err
	}
	if len(res) == 0 {
		return 0, nil
	}
	return res[0].Duration, nil
}

// LogClose 记录关播状态
func (l *Record) LogClose() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()

	type m bson.M
	err := col.FindOneAndUpdate(ctx, m{
		"room_id":    l.RoomID,
		"start_time": l.StartTime,
		"end_time":   0,
	}, m{
		"$set": l,
	}, options.FindOneAndUpdate().
		SetUpsert(true).
		SetReturnDocument(options.After)).Decode(l)
	return err
}
