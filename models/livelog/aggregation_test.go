package livelog

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func getTestData(t *testing.T) (data []Record, fromTime, toTime time.Time, guildID1, guildID2 int64, err error) {
	require := require.New(t)

	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	fromTime = util.UnixMilliToTime(1522138345941)
	toTime = util.UnixMilliToTime(1522138345947)
	guildID1 = 3
	guildID2 = 2233

	oID1, err := primitive.ObjectIDFromHex("60f687062b247117f37a0c54")
	require.NoError(err)
	oID2, err := primitive.ObjectIDFromHex("60f687532b2471ec377a0c56")
	require.NoError(err)
	oID3, err := primitive.ObjectIDFromHex("60f6876a2b247149d67a0c57")
	require.NoError(err)
	roomOID, err := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	require.NoError(err)

	data = []Record{
		{
			OID:          oID1,
			RoomOID:      roomOID,
			RoomID:       22489473,
			CreatorID:    12,
			GuildID:      guildID1,
			StartTime:    1522138345941,
			EndTime:      1522138345942,
			Duration:     7200000,
			Revenue:      6,
			Accumulation: 7,
		},
		{
			OID:          oID2,
			RoomOID:      roomOID,
			RoomID:       22489473,
			CreatorID:    12,
			GuildID:      guildID2,
			StartTime:    1522138345943,
			EndTime:      1522138345944,
			Duration:     1,
			Revenue:      6,
			Accumulation: 7,
		},
		{
			OID:          oID3,
			RoomOID:      roomOID,
			RoomID:       22489473,
			CreatorID:    12,
			GuildID:      guildID2,
			StartTime:    1522138345945,
			EndTime:      1522138345946,
			Duration:     1,
			Revenue:      6,
			Accumulation: 7,
		},
	}
	for _, v := range data {
		_, err = collection.ReplaceOne(ctx, bson.M{"_id": v.OID}, v,
			options.Replace().SetUpsert(true))
		if err != nil {
			break
		}
	}
	return
}

func TestAggregation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	data, fromTime, toTime, guildID1, guildID2, err := getTestData(t)
	require.NoError(err)

	result, err := GetUserLiveTotalDuration(guildID1, []int64{12}, fromTime, toTime)
	assert.NoError(err)
	assert.NotEmpty(result)
	for k, v := range result {
		assert.NotZero(k)
		assert.NotZero(v.TotalDuration)
		assert.NotZero(v.Days)
	}

	result2, err := GetUserLiveTimes(guildID1, fromTime, toTime)
	assert.NoError(err)
	assert.NotEmpty(result2)
	for k, v := range result2 {
		assert.NotZero(k)
		assert.NotZero(v)
	}

	openLiveNum, err := GetOpenLiveNum(guildID2, fromTime, toTime)
	assert.NoError(err)
	assert.NotEmpty(result2)
	assert.EqualValues(openLiveNum, 1)

	result3, result4, err := GetDailyCreatorNumAndDuration(guildID1, fromTime, toTime)
	assert.NoError(err)
	assert.NotEmpty(result3)
	assert.NotEmpty(result4)
	for k, v := range result3 {
		assert.NotZero(k)
		assert.NotZero(v)
	}
	for k, v := range result4 {
		assert.NotZero(k)
		assert.NotZero(v)
	}

	result5, err := GetGuildTotalDuration([]int64{guildID1}, fromTime, toTime)
	assert.NoError(err)
	assert.NotEmpty(result5)
	for k, v := range result5 {
		assert.NotZero(k)
		assert.NotZero(v)
	}

	// 测试获取直播间所有直播记录
	result6, err := GetRoomLogsAggs(data[0].RoomOID, fromTime, toTime, 0)
	assert.NoError(err)
	assert.NotEmpty(result6)
	assert.NotZero(result6.TotalDuration)
	assert.NotZero(result6.Days)
	assert.NotZero(result6.TotalRevenue)
	assert.NotZero(result6.TotalAccumulation)
	assert.NotZero(result6.DailyAVGAccumulation)
	assert.Equal(int64(7200002), result6.TotalDuration)
	assert.Equal(1, result6.Days)

	// 测试获取直播间指定公会直播记录
	result7, err := GetRoomLogsAggs(data[0].RoomOID, fromTime, toTime, guildID2)
	assert.NoError(err)
	assert.NotEmpty(result7)
	assert.Equal(int64(2), result7.TotalDuration)
	assert.Equal(0, result7.Days)
}

// 测试公会 ID 和主播 ID
const (
	testGuildID int64 = 3
	testLiveID  int64 = 400789
)

func TestGetUserLiveTotalDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	firstDayOfMonth := goutil.BeginningOfMonth(now).AddDate(0, -5, 1)
	lastDayOfMonth := firstDayOfMonth.AddDate(0, 0, 1)
	userMonthDurationMap, err := GetUserMonthLiveDuration(testGuildID, testLiveID, firstDayOfMonth,
		now.AddDate(0, 0, 2))
	assert.NoError(err)
	assert.Equal(0, len(userMonthDurationMap))
	context, cancel := service.MongoDB.Context()
	defer cancel()
	rec := []interface{}{
		// 前第 6 个月
		Record{
			GuildID:   testGuildID,
			RoomID:    100,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDayOfMonth),
			EndTime:   goutil.NewTimeUnixMilli(lastDayOfMonth),
			Duration:  10000},
		// 前第 4 个月（两条）
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDayOfMonth.AddDate(0, 2, 0)),
			EndTime:   goutil.NewTimeUnixMilli(firstDayOfMonth.AddDate(0, 2, 1)),
			Duration:  100000},
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDayOfMonth.AddDate(0, 2, 2)),
			EndTime:   goutil.NewTimeUnixMilli(firstDayOfMonth.AddDate(0, 2, 3)),
			Duration:  9999},
		// 前第 3 个月
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDayOfMonth.AddDate(0, 3, 0)),
			EndTime:   goutil.NewTimeUnixMilli(firstDayOfMonth.AddDate(0, 3, 1)),
			Duration:  100000},
		// 本月
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(now),
			EndTime:   goutil.NewTimeUnixMilli(now.AddDate(0, 0, 1)),
			Duration:  100000},
	}
	_, err = Collection().InsertMany(context, rec)
	require.NoError(err)

	defer func() {
		_, err := Collection().DeleteMany(context, bson.M{"creator_id": testLiveID})
		require.NoError(err)
	}()

	userMonthDurationMap, err = GetUserMonthLiveDuration(testGuildID, testLiveID, firstDayOfMonth,
		now.AddDate(0, 0, 2))
	assert.NoError(err)
	assert.Equal(4, len(userMonthDurationMap))
	yearMonth1 := firstDayOfMonth.Format(util.TimeFormatYMWithNoSpace)
	assert.Equal(int64(10000), userMonthDurationMap[yearMonth1].TotalDuration)
	yearMonth2 := firstDayOfMonth.AddDate(0, 2, 0).Format(util.TimeFormatYMWithNoSpace)
	assert.Equal(int64(109999), userMonthDurationMap[yearMonth2].TotalDuration)
	yearMonth3 := firstDayOfMonth.AddDate(0, 3, 0).Format(util.TimeFormatYMWithNoSpace)
	assert.Equal(int64(100000), userMonthDurationMap[yearMonth3].TotalDuration)
	yearMonth4 := now.Format(util.TimeFormatYMWithNoSpace)
	assert.Equal(int64(100000), userMonthDurationMap[yearMonth4].TotalDuration)
}

func TestGetUserDayLiveDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	firstDay := now.AddDate(0, 0, -6)
	lastDay := firstDay.AddDate(0, 0, 1)
	userDayDurationMap, err := GetUserDayLiveDuration(testLiveID, testGuildID, now,
		now)
	require.NoError(err)
	assert.Equal(0, len(userDayDurationMap))
	context, cancel := service.MongoDB.Context()
	defer cancel()
	rec := []interface{}{
		// 前第 6 个天
		Record{
			GuildID:   testGuildID,
			RoomID:    100,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDay),
			EndTime:   goutil.NewTimeUnixMilli(lastDay),
			Duration:  10000},
		// 前第 5 个天（两条）
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDay.AddDate(0, 0, 1)),
			EndTime:   goutil.NewTimeUnixMilli(lastDay.AddDate(0, 0, 1)),
			Duration:  100000},
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDay.AddDate(0, 0, 1)),
			EndTime:   goutil.NewTimeUnixMilli(lastDay.AddDate(0, 0, 1)),
			Duration:  9999},
		// 前第 2 个天
		Record{
			GuildID:   testGuildID,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(firstDay.AddDate(0, 0, 3)),
			EndTime:   goutil.NewTimeUnixMilli(lastDay.AddDate(0, 0, 3)),
			Duration:  100000},
		// 昨日
		Record{
			GuildID:   233,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(now),
			EndTime:   goutil.NewTimeUnixMilli(now.AddDate(0, 0, 1)),
			Duration:  100000},
		// 昨日
		Record{
			GuildID:   233,
			RoomID:    101,
			CreatorID: testLiveID,
			CatalogID: 115,
			StartTime: goutil.NewTimeUnixMilli(now) + 233,
			EndTime:   goutil.NewTimeUnixMilli(now.AddDate(0, 0, 1)),
			Duration:  100000},
	}
	_, err = Collection().InsertMany(context, rec)
	require.NoError(err)

	defer func() {
		_, err := Collection().DeleteMany(context, bson.M{"creator_id": testLiveID})
		require.NoError(err)
	}()

	// GuildID != 0
	userDayDurationMap, err = GetUserDayLiveDuration(testLiveID, testGuildID, firstDay,
		now.AddDate(0, 0, 2))
	require.NoError(err)
	require.Equal(3, len(userDayDurationMap))
	yearMonthDay1 := firstDay.Format(util.TimeFormatYMD)
	assert.Equal(int64(10000), userDayDurationMap[yearMonthDay1].TotalDuration)
	yearMonthDay2 := firstDay.AddDate(0, 0, 1).Format(util.TimeFormatYMD)
	assert.Equal(int64(109999), userDayDurationMap[yearMonthDay2].TotalDuration)
	yearMonthDay3 := firstDay.AddDate(0, 0, 3).Format(util.TimeFormatYMD)
	assert.Equal(int64(100000), userDayDurationMap[yearMonthDay3].TotalDuration)

	// GuildID = 0
	userDayDurationMap, err = GetUserDayLiveDuration(testLiveID, 0, firstDay,
		now.AddDate(0, 0, 2))
	require.NoError(err)
	require.Equal(4, len(userDayDurationMap))
	yearMonthDay1 = firstDay.Format(util.TimeFormatYMD)
	assert.Equal(int64(10000), userDayDurationMap[yearMonthDay1].TotalDuration)
	yearMonthDay2 = firstDay.AddDate(0, 0, 1).Format(util.TimeFormatYMD)
	assert.Equal(int64(109999), userDayDurationMap[yearMonthDay2].TotalDuration)
	yearMonthDay3 = firstDay.AddDate(0, 0, 3).Format(util.TimeFormatYMD)
	assert.Equal(int64(100000), userDayDurationMap[yearMonthDay3].TotalDuration)
	yearMonthDay4 := now.Format(util.TimeFormatYMD)
	assert.Equal(int64(200000), userDayDurationMap[yearMonthDay4].TotalDuration)
}

func TestFindTotalRevenueInLast30Days(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(20220419)
	dateUnix := goutil.BeginningOfDay(goutil.TimeNow()).Unix()
	key := keys.KeyRoomRevenueInLast30Days2.Format(testRoomID, dateUnix)
	require.NoError(service.LRURedis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	now := goutil.BeginningOfDay(goutil.TimeNow())
	_, err = Collection().InsertMany(ctx, []interface{}{
		Record{RoomID: testRoomID, Revenue: 123, StartTime: goutil.NewTimeUnixMilli(now), EndTime: 1},
		Record{RoomID: testRoomID, Revenue: 123, StartTime: goutil.NewTimeUnixMilli(now.AddDate(0, 0, -3)), EndTime: 1},
		Record{RoomID: testRoomID, Revenue: 123, StartTime: goutil.NewTimeUnixMilli(now.AddDate(0, 0, -15)), EndTime: 1},
		Record{RoomID: testRoomID, Revenue: 123, StartTime: goutil.NewTimeUnixMilli(now.AddDate(0, 0, -30)), EndTime: 1},
		Record{RoomID: testRoomID, Revenue: 123, StartTime: goutil.NewTimeUnixMilli(now.AddDate(0, 0, -35)), EndTime: 1},
	})
	require.NoError(err)
	res, err := FindTotalRevenueInLast30Days(testRoomID)
	require.NoError(err)
	assert.EqualValues(369, res)

	consumptionSum, err := service.LRURedis.Get(key).Int64()
	require.NoError(err)
	assert.Equal(res, consumptionSum)
	// 测试从缓存获取数据
	res2, err := FindTotalRevenueInLast30Days(testRoomID)
	require.NoError(err)
	assert.EqualValues(res, res2)
}

func TestGetUserGuildTerminationMetrics(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testCreatorID := int64(1145141919)
	testGuildID1 := int64(18189914)
	testGuildID2 := int64(18189915)
	_, err := Collection().DeleteMany(ctx, bson.M{"creator_id": testCreatorID})
	require.NoError(err)

	now := goutil.TimeNow()
	recordTime := now.AddDate(0, 0, -2)
	documents := []interface{}{&Record{
		CreatorID: testCreatorID,
		GuildID:   testGuildID1,
		Revenue:   1,
		StartTime: goutil.TimeUnixMilli(recordTime.UnixMilli()),
		EndTime:   goutil.TimeUnixMilli(recordTime.UnixMilli()),
		Duration:  hourMilli,
	}, &Record{
		CreatorID: testCreatorID,
		GuildID:   testGuildID1,
		Revenue:   1,
		StartTime: goutil.TimeUnixMilli(recordTime.UnixMilli()),
		EndTime:   goutil.TimeUnixMilli(recordTime.UnixMilli()),
		Duration:  hourMilli,
	}, &Record{
		CreatorID: testCreatorID,
		GuildID:   testGuildID2,
		Revenue:   2,
		StartTime: goutil.TimeUnixMilli(recordTime.UnixMilli()),
		EndTime:   goutil.TimeUnixMilli(recordTime.UnixMilli()),
		Duration:  hourMilli,
	}}
	recordTime = recordTime.AddDate(0, 0, 1)
	documents = append(documents, &Record{
		CreatorID: testCreatorID,
		GuildID:   testGuildID1,
		Revenue:   1,
		StartTime: goutil.TimeUnixMilli(recordTime.UnixMilli()),
		EndTime:   goutil.TimeUnixMilli(recordTime.UnixMilli()),
		Duration:  2 * hourMilli,
	})
	_, err = Collection().InsertMany(ctx, documents)
	require.NoError(err)

	startTime := now.AddDate(0, 0, -3)
	metricsMap, err := GetUserGuildTerminationMetrics([]int64{testCreatorID}, startTime, now)
	require.NoError(err)
	require.Len(metricsMap, 1)
	userMetrics, ok := metricsMap[testCreatorID]
	require.True(ok)
	require.NotNil(userMetrics)
	metrics, ok := userMetrics[testGuildID1]
	require.True(ok)
	require.NotNil(metrics)
	assert.EqualValues(3, metrics.Count)
	assert.EqualValues(2, metrics.Days)
	assert.EqualValues(3, metrics.Revenue)
	metrics, ok = userMetrics[testGuildID2]
	require.True(ok)
	require.NotNil(metrics)
	assert.EqualValues(1, metrics.Count)
	assert.EqualValues(0, metrics.Days)
	assert.EqualValues(2, metrics.Revenue)
}
