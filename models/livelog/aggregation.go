package livelog

import (
	"math"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const hourMilli = int64(3600000) // 1 小时的毫秒数

// UserLiveTotalDuration struct
type UserLiveTotalDuration struct {
	ID            int64 `bson:"_id"`
	TotalDuration int64 `bson:"total_duration"`
	Days          int   `bson:"days"`
}

// GetUserLiveTotalDuration 统计 guildID 公会中主播的直播时长，毫秒，返回 map[主播 ID]{直播时长，有效天}
func GetUserLiveTotalDuration(guildID int64, userIDs []int64, fromTime, toTime time.Time) (map[int64]*UserLiveTotalDuration, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	if len(userIDs) <= 0 {
		return nil, nil
	}

	type m bson.M
	var results []UserLiveTotalDuration
	pipeline := []m{{
		"$match": m{
			"guild_id":   guildID,
			"creator_id": m{"$in": userIDs},
			"start_time": m{"$gte": util.TimeToUnixMilli(fromTime), "$lt": util.TimeToUnixMilli(toTime)},
			"end_time":   m{"$gt": 0},
		},
	}, {
		"$group": m{
			"_id": m{
				"local_days": m{
					"$trunc": m{
						// 按东八区的日期进行分组
						"$divide": bson.A{m{"$add": bson.A{"$start_time", 8 * hourMilli}}, 24 * hourMilli},
					}},
				"creator_id": "$creator_id",
			},
			"sum_duration": m{"$sum": "$duration"},
		},
	}, {
		"$group": m{
			"_id":            "$_id.creator_id",
			"total_duration": m{"$sum": "$sum_duration"},
			"days":           m{"$sum": m{"$cond": bson.A{m{"$gte": bson.A{"$sum_duration", 2 * hourMilli}}, 1, 0}}},
		},
	},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, err
	}

	mapResult := make(map[int64]*UserLiveTotalDuration, len(results))
	for i, v := range results {
		mapResult[v.ID] = &results[i]
	}
	return mapResult, nil
}

// UserLiveMonthDuration struct
type UserLiveMonthDuration struct {
	YearMonth     string `bson:"year_month"`
	TotalDuration int64  `bson:"total_duration"`
}

// GetUserMonthLiveDuration 统计 guildID 公会中某个主播的月直播时长（毫秒），返回 map[年月]{月直播时长}
func GetUserMonthLiveDuration(guildID int64, userID int64, fromTime, toTime time.Time) (map[string]*UserLiveMonthDuration, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	type m bson.M
	pipeline := []m{
		{
			"$match": m{
				"guild_id":   guildID,
				"creator_id": userID,
				"start_time": m{"$gte": util.TimeToUnixMilli(fromTime), "$lt": util.TimeToUnixMilli(toTime)},
				"end_time":   m{"$gt": 0},
			},
		},
		{
			"$group": m{
				"_id": m{
					"year_month": m{
						"$dateToString": m{
							// 按东八区的日期
							"format": "%Y%m", "date": m{"$toDate": m{"$add": bson.A{"$start_time", 8 * hourMilli}}}},
					},
				},
				"total_duration": m{"$sum": "$duration"},
			},
		},
		{
			"$project": m{
				"year_month":     "$_id.year_month",
				"total_duration": "$total_duration",
			},
		},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	var results []UserLiveMonthDuration
	err = cursor.All(ctx, &results)
	if err != nil {
		return nil, err
	}

	mapResult := make(map[string]*UserLiveMonthDuration, len(results))
	for i, v := range results {
		mapResult[v.YearMonth] = &results[i]
	}
	return mapResult, nil
}

// UserLiveDayDuration struct
type UserLiveDayDuration struct {
	BizDate       string `bson:"_id"`
	TotalDuration int64  `bson:"total_duration"`
}

// GetUserDayLiveDuration 根据 creatorID、guildID，返回主播的范围时间每天直播时长（毫秒）
//  guildID <> 0 时返回主播在该公会的天直播时长
//  guildID = 0 时返回主播的天直播时长
//  return map["年-月-日"]: {
//  	"biz_date": "年-月-日",
//  	"total_duration": 日直播时长
//  }
func GetUserDayLiveDuration(creatorID, guildID int64, fromTime, toTime time.Time) (map[string]*UserLiveDayDuration, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	type m bson.M
	match := m{
		"creator_id": creatorID,
		"start_time": m{"$gte": util.TimeToUnixMilli(fromTime), "$lt": util.TimeToUnixMilli(toTime)},
		"end_time":   m{"$gt": 0},
	}
	if guildID != 0 {
		match["guild_id"] = guildID
	}
	pipeline := []m{
		{
			"$match": match,
		},
		{
			"$group": m{
				"_id": m{
					"$dateToString": m{
						// 按东八区的日期
						"format": "%Y-%m-%d", "date": m{"$toDate": m{"$add": bson.A{"$start_time", 8 * hourMilli}}},
					},
				},
				"total_duration": m{"$sum": "$duration"},
			},
		},
	}

	cur, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var results []UserLiveDayDuration
	err = cur.All(ctx, &results)
	if err != nil {
		return nil, err
	}
	mapResult := make(map[string]*UserLiveDayDuration, len(results))
	for i, v := range results {
		mapResult[v.BizDate] = &results[i]
	}
	return mapResult, nil
}

// GetDailyCreatorNumAndDuration 统计发起直播的主播人数，按天
func GetDailyCreatorNumAndDuration(guildID int64, fromTime, toTime time.Time) (dailyCreatorNum map[string]int64,
	dailyDuration map[string]int64, err error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pipeline := []bson.M{{
		"$match": bson.M{
			"guild_id": guildID,
			"start_time": bson.M{
				"$gte": util.TimeToUnixMilli(fromTime),
				"$lt":  util.TimeToUnixMilli(toTime),
			},
			"end_time": bson.M{"$gt": 0},
		},
	}, {
		"$group": bson.M{
			"_id": bson.M{
				"local_days": bson.M{
					"$trunc": bson.M{
						"$divide": bson.A{
							// 按东八区的日期进行分组
							bson.M{"$add": bson.A{"$start_time", 8 * hourMilli}},
							24 * hourMilli,
						},
					},
				},
				"creator_id": "$creator_id",
			},
			"count":    bson.M{"$sum": 1},
			"duration": bson.M{"$sum": "$duration"},
		},
	}, {
		"$group": bson.M{
			"_id":            "$_id.local_days",
			"sum":            bson.M{"$sum": "$count"}, // 按次数
			"count":          bson.M{"$sum": 1},        // 按人数
			"total_duration": bson.M{"$sum": "$duration"},
		},
	},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, nil, err
	}
	defer cursor.Close(ctx)

	var result []struct {
		ID            int64 `bson:"_id"`
		Count         int64 `bson:"count"`
		Sum           int64 `bson:"sum"`
		TotalDuration int64 `bson:"total_duration"`
	}
	err = cursor.All(ctx, &result)
	if err != nil {
		return nil, nil, err
	}

	dailyCreatorNum = make(map[string]int64, len(result))
	dailyDuration = make(map[string]int64, len(result))
	for _, v := range result {
		// time.Unix 把 unix timestamp 转成 local time
		// 所以先将 v.ID_ 转成 unix timestamp
		t := time.Unix((v.ID*24-8)*3600, 0).Format("2006-01-02")
		dailyCreatorNum[t] = v.Count
		dailyDuration[t] = v.TotalDuration
	}

	return dailyCreatorNum, dailyDuration, nil
}

// GetOpenLiveNum 指定时间内公会开播主播数，不统计开播中的主播数量
// 公会数据报表需求统计开播主播数时，不记录未关播的主播：https://info.missevan.com/pages/viewpage.action?pageId=67240851
func GetOpenLiveNum(guildID int64, fromTime, toTime time.Time) (int64, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pipeline := []bson.M{
		{
			"$match": bson.M{
				"guild_id": guildID,
				"start_time": bson.M{
					"$gte": util.TimeToUnixMilli(fromTime),
					"$lt":  util.TimeToUnixMilli(toTime),
				},
				"end_time": bson.M{"$gt": 0},
			},
		},
		{
			"$group": bson.M{
				"_id": "$creator_id",
			},
		},
		{
			"$group": bson.M{
				"_id":   nil,
				"count": bson.M{"$sum": 1},
			},
		},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(ctx)

	var result []struct {
		Count int64 `bson:"count"`
	}
	err = cursor.All(ctx, &result)
	if err != nil {
		return 0, err
	}
	if len(result) == 0 {
		return 0, nil
	}

	return result[0].Count, nil
}

// GetUserLiveTimes 指定时间内公会主播的开播次数，返回 map[主播 ID]次数
func GetUserLiveTimes(guildID int64, fromTime, toTime time.Time) (map[int64]int64 /* map[主播 ID]次数 */, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var result []struct {
		ID    int64 `bson:"_id"`
		Count int64 `bson:"count"`
	}
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"guild_id": guildID,
				"start_time": bson.M{
					"$gte": util.TimeToUnixMilli(fromTime),
					"$lt":  util.TimeToUnixMilli(toTime),
				},
				"end_time": bson.M{"$gt": 0},
			},
		},
		{
			"$group": bson.M{
				"_id":   "$creator_id",
				"count": bson.M{"$sum": 1},
			},
		},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	err = cursor.All(ctx, &result)
	if err != nil {
		return nil, err
	}

	mapResult := make(map[int64]int64, len(result))
	for _, v := range result {
		mapResult[v.ID] = v.Count
	}
	return mapResult, nil
}

// GetGuildTotalDuration 获取开播时间在指定范围内的，以下公会的直播时长，毫秒，返回 map[公会 ID]时长
func GetGuildTotalDuration(guildIDs []int64, fromTime, toTime time.Time) (map[int64]int64 /* map[公会 ID]时长 */, error) {
	if len(guildIDs) == 0 {
		return nil, nil
	}
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var result []struct {
		ID            int64 `bson:"_id"`
		TotalDuration int64 `bson:"total_duration"`
	}
	pipeline := []bson.M{{
		"$match": bson.M{
			"guild_id": bson.M{"$in": guildIDs},
			"start_time": bson.M{
				"$gte": util.TimeToUnixMilli(fromTime),
				"$lt":  util.TimeToUnixMilli(toTime),
			},
			"end_time": bson.M{"$gt": 0},
		},
	}, {
		"$group": bson.M{
			"_id":            "$guild_id",
			"total_duration": bson.M{"$sum": "$duration"},
		},
	},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	err = cursor.All(ctx, &result)
	if err != nil {
		return nil, err
	}

	mapResult := make(map[int64]int64, len(result))
	for _, v := range result {
		mapResult[v.ID] = v.TotalDuration
	}
	return mapResult, nil
}

// UserLiveAggs 主播房间统计信息
type UserLiveAggs struct {
	TotalDuration        int64         `bson:"total_duration" json:"total_duration"`
	TotalRevenue         int64         `bson:"total_revenue" json:"total_revenue"`
	TotalAccumulation    int64         `bson:"total_accumulation" json:"total_accumulation"`
	Days                 int           `bson:"days" json:"days"` // 有效天
	DailyAVGAccumulation util.Float2DP `bson:"-" json:"daily_avg_accumulation"`
}

// GetRoomLogsAggs 获取房间的统计信息，返回 {直播时长（毫秒），有效天}
func GetRoomLogsAggs(roomOID primitive.ObjectID, fromTime, toTime time.Time, guildID int64) (UserLiveAggs, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	match := bson.M{
		"_room_id": roomOID,
		"start_time": bson.M{
			"$gte": util.TimeToUnixMilli(fromTime),
			"$lt":  util.TimeToUnixMilli(toTime),
		},
		"end_time": bson.M{"$gt": 0},
	}
	if guildID != 0 {
		match["guild_id"] = guildID
	}
	pipeline := []bson.M{{
		"$match": match,
	}, {
		"$group": bson.M{
			"_id": bson.M{
				"local_days": bson.M{
					"$trunc": bson.M{
						"$divide": bson.A{
							// 按东八区的日期进行分组
							bson.M{"$add": bson.A{"$start_time", 8 * hourMilli}},
							24 * hourMilli,
						},
					},
				},
			},
			"sum_duration":     bson.M{"$sum": "$duration"},
			"sum_revenue":      bson.M{"$sum": "$revenue"},
			"sum_accumulation": bson.M{"$sum": "$accumulation"},
		},
	}, {
		"$group": bson.M{
			"_id":                nil,
			"total_duration":     bson.M{"$sum": "$sum_duration"},
			"total_revenue":      bson.M{"$sum": "$sum_revenue"},
			"total_accumulation": bson.M{"$sum": "$sum_accumulation"},
			"days": bson.M{
				"$sum": bson.M{
					"$cond": bson.A{bson.M{"$gte": bson.A{"$sum_duration", 2 * hourMilli}}, 1, 0},
				},
			},
		},
	},
	}
	var result UserLiveAggs
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return result, err
	}
	defer cursor.Close(ctx)
	if cursor.Next(ctx) {
		err = cursor.Decode(&result)
		if err != nil {
			return result, err
		}
	}

	days := math.Ceil(float64(toTime.Unix()-fromTime.Unix()) / 24 * time.Hour.Seconds())
	result.DailyAVGAccumulation = util.Float2DP(float64(result.TotalAccumulation) / days)
	return result, nil
}

// FindTotalRevenueInLast30Days 获取主播前三十天的收益
func FindTotalRevenueInLast30Days(roomID int64) (int64, error) {
	date := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyRoomRevenueInLast30Days2.Format(roomID, date.Unix())
	revenue, err := service.LRURedis.Get(key).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return 0, err
	}
	if err == nil {
		return revenue, nil
	}
	startDate := date.AddDate(0, 0, -30)
	match := bson.M{
		"$match": bson.M{
			"room_id": roomID,
			"start_time": bson.M{
				"$gte": util.TimeToUnixMilli(startDate),
				"$lt":  util.TimeToUnixMilli(date),
			},
			"end_time": bson.M{"$gt": 0},
		},
	}
	group := bson.M{
		"$group": bson.M{
			"_id":           nil,
			"total_revenue": bson.M{"$sum": "$revenue"},
		}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := Collection().Aggregate(ctx, bson.A{match, group})
	if err != nil {
		return 0, err
	}
	defer cur.Close(ctx)
	var resArray []struct {
		TotalRevenue int64 `bson:"total_revenue"`
	}
	err = cur.All(ctx, &resArray)
	if err != nil {
		return 0, err
	}
	if len(resArray) != 0 {
		revenue = resArray[0].TotalRevenue
	}
	// 缓存时间比一天稍长一点
	if err = service.LRURedis.Set(key, revenue, 30*time.Hour).Err(); err != nil {
		logger.Error(err)
		// PASS
	}
	return revenue, nil
}

// UserGuildTerminationMetrics 清退公会主播使用的统计数据
type UserGuildTerminationMetrics struct {
	Count   int64 // 开播次数（单位：次）
	Days    int64 // 直播有效天数（单位：天）
	Revenue int64 // 直播流水，保留退款额（单位：钻）
}

// GetUserGuildTerminationMetrics 获取清退公会主播用的统计数据
// 返回值采用 map[主播 ID][公会 ID]统计数据 的结构
func GetUserGuildTerminationMetrics(liveIDs []int64, metricStartTime time.Time, metricEndTime time.Time) (map[int64]map[int64]*UserGuildTerminationMetrics, error) {
	collection := Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	type index struct {
		CreatorID int64 `bson:"creator_id"` // 作为分组依据的主播 ID
		GuildID   int64 `bson:"guild_id"`   // 作为分组依据的公会 ID
	}
	var result []struct {
		ID      index `bson:"_id"`     // 分组依据 ID
		Count   int64 `bson:"count"`   // 开播次数（单位：次）
		Days    int64 `bson:"days"`    // 直播有效天数（单位：天）
		Revenue int64 `bson:"revenue"` // 直播流水，保留退款额（单位：钻）
	}
	type m bson.M
	pipeline := []m{
		{
			"$match": m{
				"creator_id": m{"$in": liveIDs},
				"start_time": m{"$lt": util.TimeToUnixMilli(metricEndTime)},   // 包含在 metricEndTime 前尚未结束的
				"end_time":   m{"$gt": util.TimeToUnixMilli(metricStartTime)}, // 包含在 metricStartTime 后才结束的
			},
		},
		{
			"$group": m{
				"_id": m{
					"local_days": m{
						"$trunc": m{
							// 按东八区的日期进行分组
							"$divide": bson.A{m{"$add": bson.A{"$start_time", 8 * hourMilli}}, 24 * hourMilli},
						}},
					"creator_id": "$creator_id",
					"guild_id":   "$guild_id",
				},
				"count":        m{"$sum": 1},
				"revenue":      m{"$sum": "$revenue"},
				"sum_duration": m{"$sum": "$duration"},
			},
		}, {
			"$group": m{
				"_id": m{
					"creator_id": "$_id.creator_id",
					"guild_id":   "$_id.guild_id",
				},
				"days":    m{"$sum": m{"$cond": bson.A{m{"$gte": bson.A{"$sum_duration", 2 * hourMilli}}, 1, 0}}},
				"count":   m{"$sum": "$count"},
				"revenue": m{"$sum": "$revenue"},
			},
		},
	}
	cursor, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)
	err = cursor.All(ctx, &result)
	if err != nil {
		return nil, err
	}

	mapResult := make(map[int64]map[int64]*UserGuildTerminationMetrics, len(liveIDs))
	for _, v := range result {
		if _, ok := mapResult[v.ID.CreatorID]; !ok {
			creatorMap := make(map[int64]*UserGuildTerminationMetrics, 1)
			mapResult[v.ID.CreatorID] = creatorMap
		}
		d := UserGuildTerminationMetrics{
			Count:   v.Count,
			Days:    v.Days,
			Revenue: v.Revenue,
		}
		mapResult[v.ID.CreatorID][v.ID.GuildID] = &d
	}
	return mapResult, nil
}
