package liveconnect

import (
	"reflect"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CollectionName collection name without prefix
const CollectionName = "live_connects"

// Stauts
const (
	StatusQueued int32 = iota
	StatusJoined
	StatusFinished
	StatusCanceled
	StatusRejected
)

// LiveConnect document in collection live_connects
type LiveConnect struct {
	OID    primitive.ObjectID `bson:"_id" json:"-"`
	Helper `bson:",inline"`

	Online *bool            `bson:"-" json:"online,omitempty"`
	Titles []liveuser.Title `bson:"-" json:"titles,omitempty"`
}

// Helper for LiveConnect
type Helper struct {
	RoomOID      primitive.ObjectID `bson:"_room_id" json:"-"`
	RoomID       int64              `bson:"room_id" json:"room_id"`
	UserID       int64              `bson:"user_id" json:"user_id"`
	Username     string             `bson:"username" json:"username"` // TODO: 从 users 获取最新用户信息
	IconURL      string             `bson:"iconurl" json:"iconurl"`   // TODO: 从 users 获取最新用户信息
	Introduction string             `bson:"introduction" json:"introduction"`
	Status       int32              `bson:"status" json:"status"`
	PushType     string             `bson:"push_type" json:"-"`

	CreatedTime   int64  `bson:"created_time" json:"created_time"` // 13 位时间戳
	UpdatedTime   int64  `bson:"updated_time" json:"-"`
	ConnectedTime *int64 `bson:"connected_time" json:"connected_time"`
}

// user interface
type user interface {
	UserID() int64
}

// Collection collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// ListLiveConnects 查询 room 的 LiveConnect
func ListLiveConnects(room primitive.ObjectID, since time.Time) (queue, join, finish []*LiveConnect, err error) {
	queue, join, finish =
		make([]*LiveConnect, 0), make([]*LiveConnect, 0), make([]*LiveConnect, 0)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	var cur *mongo.Cursor
	cur, err = collection.Find(ctx, bson.M{
		"_room_id":     room,
		"status":       bson.M{"$lte": StatusFinished},
		"created_time": bson.M{"$gte": util.TimeToUnixMilli(since)},
	})
	if err != nil {
		return
	}
	defer cur.Close(ctx)
	connects := make([]*LiveConnect, 0)
	err = cur.All(ctx, &connects)
	if err != nil {
		return
	}

	if len(connects) == 0 {
		return
	}
	userIDs := make([]int64, len(connects))
	for i := 0; i < len(connects); i++ {
		userIDs[i] = connects[i].UserID
		switch connects[i].Status {
		case StatusQueued:
			queue = append(queue, connects[i])
		case StatusJoined:
			join = append(join, connects[i])
		case StatusFinished:
			finish = append(finish, connects[i])
		}
	}
	if titlesMap, err2 := liveuser.UserTitlesMap(userIDs, connects[0].RoomID, nil); err2 != nil {
		logger.Error(err2)
		// PASS
	} else {
		for i := 0; i < len(connects); i++ {
			if t := titlesMap[connects[i].UserID]; len(t) != 0 {
				connects[i].Titles = t
			}
		}
	}
	return
}

// IsUserInQueue 判断此用户是否在队列中
func IsUserInQueue(userID int64, list []*LiveConnect) bool {
	for i := 0; i < len(list); i++ {
		if list[i].UserID == userID {
			return true
		}
	}
	return false
}

// IsUserInQueue2 使用 interface 版本的判断游客是否在队列中
// TODO: 实验性质
func IsUserInQueue2(u user, list []*LiveConnect) bool {
	v := reflect.ValueOf(u)
	return u != nil && !(v.Kind() == reflect.Ptr && v.IsNil()) &&
		IsUserInQueue(u.UserID(), list)
}

// ClearQueue 清理未处理或处理中的连麦列表
func ClearQueue(room primitive.ObjectID) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	now := util.TimeToUnixMilli(goutil.TimeNow())
	res, err := Collection().UpdateMany(ctx, bson.M{
		"_room_id":     room,
		"status":       bson.M{"$in": []int32{StatusQueued, StatusJoined}},
		"created_time": bson.M{"$lt": now},
	}, bson.M{"$set": bson.M{"status": StatusCanceled, "updated_time": now}})
	if err != nil {
		return 0, err
	}
	return res.ModifiedCount, nil
}

// FindOne 查询一个连麦记录
func FindOne(filter interface{}) (*LiveConnect, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var connect LiveConnect
	err := Collection().FindOne(ctx, filter).Decode(&connect)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &connect, nil
}

// IsRoomConnecting 检查直播间主播连麦状态
// NOTICE: 该方法忽略了直播间的开播状态，需要调用前保证直播间在开播中
func IsRoomConnecting(roomID int64) (connecting bool, err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	err = Collection().FindOne(ctx,
		bson.M{
			"room_id": roomID,
			"status":  StatusJoined,
		},
		options.FindOne().SetProjection(bson.M{"_id": 1}),
	).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// ConnectClose 停止连麦
func ConnectClose(roomID, userID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := util.TimeToUnixMilli(goutil.TimeNow())
	result, err := Collection().UpdateMany(ctx, bson.M{
		"room_id":      roomID,
		"user_id":      userID,
		"status":       bson.M{"$in": []int32{StatusQueued, StatusJoined}},
		"created_time": bson.M{"$lt": now},
	}, bson.M{"$set": bson.M{"status": StatusCanceled, "updated_time": now}})
	if err != nil {
		return 0, err
	}
	return result.ModifiedCount, nil
}

// UpdateOne 更新连麦记录
func UpdateOne(filter, update interface{}) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	res, err := Collection().UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return false, err
	}
	return res.ModifiedCount > 0, nil
}

// ConnectingRoomIDs 从房间号集合中获取正在进行听众连麦的房间号
func ConnectingRoomIDs(roomIDs []int64) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cur, err := Collection().Find(ctx,
		bson.M{
			"room_id": bson.M{"$in": roomIDs},
			"status":  StatusJoined,
		},
		options.Find().SetProjection(bson.M{"room_id": 1}),
	)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var connects []*LiveConnect
	err = cur.All(ctx, &connects)
	if err != nil {
		return nil, err
	}

	connectingRoomIDs := goutil.SliceMap(connects, func(c *LiveConnect) int64 {
		return c.RoomID
	})
	return connectingRoomIDs, nil
}
