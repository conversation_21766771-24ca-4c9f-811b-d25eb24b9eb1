package liveconnect

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testRoomID    = int64(22489473)
	testRoomIDHex = "5ab9d5d9bc9b53298ce5a5a5"
)

var (
	testQueue     = make([]*LiveConnect, 2)
	testJoin      = make([]*LiveConnect, 1)
	testFinish    = make([]*LiveConnect, 1)
	since         time.Time
	createdTime   int64
	testRoomObjID primitive.ObjectID
	medal         *livemedal.Simple
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	AddTestData()
	defer ClearTestData()
	m.Run()
}

func AddTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	collection := livemedal.Collection()
	medal = &livemedal.Simple{RoomID: 11111, CreatorID: 1234, UserID: 12, Status: livemedal.StatusShow, Point: 500}
	medal.Name = "tiny medal"
	_, err := collection.InsertOne(ctx, medal)
	if err != nil {
		panic(err)
	}

	collection = service.MongoDB.Collection(CollectionName)
	now := goutil.TimeNow()
	createdTime = util.TimeToUnixMilli(now)
	since = now.Add(-time.Minute)
	testRoomObjID, _ = primitive.ObjectIDFromHex(testRoomIDHex)
	h := Helper{
		CreatedTime: createdTime,
		RoomID:      testRoomID,
		RoomOID:     testRoomObjID,
	}
	uid := int64(-10)
	for i := 0; i < len(testQueue); i++ {
		testQueue[i] = &LiveConnect{Helper: h}
		testQueue[i].UserID = uid - int64(i)
		testQueue[i].Status = StatusQueued
		_, err := collection.InsertOne(ctx, testQueue[i].Helper)
		if err != nil {
			panic(err)
		}
	}
	uid = -20
	for i := 0; i < len(testJoin); i++ {
		testJoin[i] = &LiveConnect{Helper: h}
		testJoin[i].UserID = uid - int64(i)
		testJoin[i].Status = StatusJoined
		_, err := collection.InsertOne(ctx, testJoin[i].Helper)
		if err != nil {
			panic(err)
		}
	}
	uid = -30
	for i := 0; i < len(testFinish); i++ {
		testFinish[i] = &LiveConnect{Helper: h}
		testFinish[i].UserID = uid - int64(i)
		testFinish[i].Status = StatusFinished
		_, err := collection.InsertOne(ctx, testFinish[i].Helper)
		if err != nil {
			panic(err)
		}
	}
}

func ClearTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := Collection()
	_, _ = col.DeleteMany(ctx, bson.M{
		"room_id":      testRoomID,
		"_room_id":     testRoomObjID,
		"created_time": createdTime,
	})
	// 删除今天之前的数据
	_, _ = col.DeleteMany(ctx, bson.M{
		"created_time": bson.M{"$lt": util.TimeToUnixMilli(util.
			BeginningOfDay(goutil.TimeNow()))}})
	_, _ = livemedal.Remove(medal.UserID, medal.CreatorID, testRoomID)
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(LiveConnect{}, "_id")
	kc.Check(Helper{}, "_room_id", "room_id", "user_id", "username",
		"iconurl", "introduction", "status", "push_type", "created_time", "updated_time", "connected_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LiveConnect{}, "online", "titles")
	kc.Check(Helper{}, "room_id", "user_id", "username",
		"iconurl", "introduction", "status", "created_time", "connected_time")
}

func TestListLiveConnects(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	q, j, f, err := ListLiveConnects(testRoomObjID, since)
	require.NoError(err)
	require.Equal(len(testQueue), len(q))
	require.Equal(len(testJoin), len(j))
	require.Equal(len(testFinish), len(f))
	for i := 0; i < len(testQueue); i++ {
		assert.True(func() bool {
			for j := 0; i < len(q); j++ {
				if testQueue[i].UserID == q[j].UserID {
					return true
				}
			}
			return false
		}())
		if q[i].UserID == 12 {
			assert.Len(q[i].Titles, 1)
		}
	}
	for i := 0; i < len(testJoin); i++ {
		assert.Equal(testJoin[i].UserID, j[i].UserID)
	}
	for i := 0; i < len(testFinish); i++ {
		assert.Equal(testFinish[i].UserID, f[i].UserID)
	}
}

func TestIsUserInQueue(t *testing.T) {
	assert := assert.New(t)
	l := []*LiveConnect{{}, {}, {}}
	userID := int64(12)
	assert.False(IsUserInQueue(userID, l))
	l[1].UserID = userID
	assert.True(IsUserInQueue(userID, l))

	u := util.SmartUserContext{UID: 12}
	assert.True(IsUserInQueue2(u, l))
	assert.True(IsUserInQueue2(&u, l))
	assert.False(IsUserInQueue2(util.SmartUserContext{UID: 11}, l))
	assert.False(IsUserInQueue2((*util.SmartUserContext)(nil), l))
	assert.False(IsUserInQueue2(nil, l))
}

func TestClearQueue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	const testHex = "5e6f308e6d4cf2146447f651"
	roomOID, _ := primitive.ObjectIDFromHex(testHex)

	now := goutil.TimeNow()
	createTime := util.TimeToUnixMilli(now) - 100000
	col := Collection()
	_, err := col.InsertMany(ctx, []interface{}{
		Helper{
			RoomOID:     roomOID,
			RoomID:      123456,
			Status:      StatusQueued,
			CreatedTime: createTime,
		},
		Helper{
			RoomOID:     roomOID,
			RoomID:      123456,
			Status:      StatusJoined,
			CreatedTime: createTime,
		},
	})
	require.NoError(err)
	count, err := ClearQueue(roomOID)
	require.NoError(err)
	assert.Equal(int64(2), count)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lc, err := FindOne(bson.M{})
	require.NoError(err)
	assert.NotNil(lc)

	lc, err = FindOne(bson.M{"room_id": -111123456})
	require.NoError(err)
	assert.Nil(lc)
}

func TestIsRoomConnecting(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	connecting, err := IsRoomConnecting(testJoin[0].RoomID)
	require.NoError(err)
	assert.True(connecting)

	connecting, err = IsRoomConnecting(-99999)
	require.NoError(err)
	assert.False(connecting)
}

func TestConnectClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now.Add(1)
	})

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertOne(ctx, &LiveConnect{
		Helper: Helper{
			RoomID:      111,
			UserID:      111,
			Status:      StatusJoined,
			CreatedTime: now.Unix(),
		},
	})
	require.NoError(err)

	count, err := ConnectClose(111, 111)
	require.NoError(err)
	assert.NotZero(count)
}

func TestUpdateOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lc, err := FindOne(bson.M{})
	require.NoError(err)
	require.NotNil(lc)

	now := goutil.TimeNow().UnixMilli()
	ok, err := UpdateOne(bson.M{"_id": lc.OID}, bson.M{"updated_time": now})
	require.NoError(err)
	require.True(ok)

	lc, err = FindOne(bson.M{"_id": lc.OID})
	require.NoError(err)
	assert.Equal(now, lc.UpdatedTime)
}

func TestConnectingRoomIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().DeleteMany(ctx, bson.M{
		"room_id": bson.M{"$in": []int64{50000, 50001, 50002, 50003}},
	})
	require.NoError(err)
	_, err = Collection().InsertMany(ctx, []interface{}{
		Helper{RoomID: 50000, Status: StatusJoined},
		Helper{RoomID: 50001, Status: StatusJoined},
		Helper{RoomID: 50002, Status: StatusJoined},
		Helper{RoomID: 50003, Status: StatusFinished},
	})
	require.NoError(err)

	filteredRoomIDs, err := ConnectingRoomIDs([]int64{50000, 50001, 50002, 50003, 50004})
	require.NoError(err)
	assert.Equal([]int64{50000, 50001, 50002}, filteredRoomIDs)

	filteredRoomIDs, err = ConnectingRoomIDs([]int64{50004})
	require.NoError(err)
	assert.Equal([]int64{}, filteredRoomIDs)
}
