// Package liveim 直播间 websocket 聊天室即时消息相关
package liveim

import (
	"bytes"
	"encoding/json"
	"errors"
	"math/rand"
	"sync"
	"time"

	"github.com/andybalholm/brotli"
	"github.com/go-redis/redis/v7"
	jsoniter "github.com/json-iterator/go"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// im 消息类型
const (
	IMMessageTypeNormal = iota // 一般消息
	IMMessageTypeAll           // 全局广播

	IMMessageTypeActivity    // 活动一般消息
	IMMessageTypeActivityAll // 活动房间全局消息
)

var invisibleRoles = []role.Role{role.LiveAdmin, role.LiveJudgement, role.LiveOperator, role.AuditLive}

const dataMaxLength = 0x01 << (3 * 8)

var (
	// ErrDataTooLong 数据超长
	ErrDataTooLong = errors.New("data too long")

	// ErrEncoderNotExists 无法找到 encoder
	ErrEncoderNotExists = errors.New("encoder not exists")
)

// data type
const (
	DataTypeIMMessage byte = 0x00
	DataTypePayload   byte = 0x01
)

var json2 = jsoniter.Config{
	EscapeHTML:  false,
	SortMapKeys: true,
}.Froze()

// LiveJudgmentUserIDSet 获取 livejudgment 的用户 ID 的集合
// NOTICE: 对于进场通知，需要将隐身人员当成匿名用户进入。避免主播根据人数变化察觉超管进入
// Deprecated: 使用的地方待替换
func LiveJudgmentUserIDSet() map[int64]struct{} {
	return mowangskuser.SpecialRolesUserIDs(invisibleRoles, keys.LocalKeyIMJoinInvisibleUserIDs0.Format())
}

// HasInvisibleRoles 用户是否拥有进场隐身角色
// TODO: 移动到其他包
func HasInvisibleRoles(uc mrpc.UserContext, userID int64) bool {
	key := keys.LocalKeyIMJoinInvisibleUserIDs0.Format()
	v, ok := service.Cache5Min.Get(key)
	if ok {
		set := v.(map[int64]struct{})
		_, ok = set[userID]
		return ok
	}
	roles := make([]string, len(invisibleRoles))
	for i := range invisibleRoles {
		roles[i] = string(invisibleRoles[i])
	}
	userIDs, err := userapi.AuthUserIDs(uc, roles)
	if err != nil {
		logger.Error(err)
		// PASS
		return false
	}
	set := make(map[int64]struct{}, len(userIDs))
	for i := range userIDs {
		set[userIDs[i]] = struct{}{}
	}
	service.Cache5Min.Set(key, set, 0)
	_, ok = set[userID]
	return ok
}

// GetInvisibleRoleUserIDMap 获取拥有进场隐身角色的用户 ID 集合
func GetInvisibleRoleUserIDMap(uc mrpc.UserContext) map[int64]struct{} {
	key := keys.LocalKeyIMJoinInvisibleUserIDs0.Format()
	v, ok := service.Cache5Min.Get(key)
	if ok {
		return v.(map[int64]struct{})
	}
	roles := make([]string, len(invisibleRoles))
	for i := range invisibleRoles {
		roles[i] = string(invisibleRoles[i])
	}
	userIDs, err := userapi.AuthUserIDs(uc, roles)
	if err != nil {
		logger.Error(err)
		// PASS
		return map[int64]struct{}{}
	}
	set := make(map[int64]struct{}, len(userIDs))
	for i := range userIDs {
		set[userIDs[i]] = struct{}{}
	}
	service.Cache5Min.Set(key, set, 0)
	return set
}

// 消息的优先级类型
const (
	PriorityNormal = iota
	PriorityPurchased
)

// IMMessage im 的消息
type IMMessage struct {
	Type     int             `json:"type"`
	RoomID   int64           `json:"room_id,omitempty"`
	UserID   int64           `json:"user_id,omitempty"`
	Priority int             `json:"priority,omitempty"`
	Payload  json.RawMessage `json:"payload"`

	CompressedPayload []byte `json:"-"` // 会根据格式拼接到 json 数据后
}

// SetPayload 设置从请求来的 payload
func (im *IMMessage) SetPayload(payload interface{}) error {
	var rawData []byte
	switch v := payload.(type) {
	case json.RawMessage:
		rawData = v
	default:
		var err error
		rawData, err = json2.Marshal(payload)
		if err != nil {
			return err
		}
	}
	im.Payload = rawData
	compressedPayload, err := BrotliEncode(rawData)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	im.CompressedPayload = compressedPayload
	return nil
}

// Encode message encode
/*
第一个字节：固定 0x00
第二到第四个字节：小端字节序的未压缩的 IMMessage json 格式长度
第五个字节到长度所指范围：IMMessage json data
剩下数据: IMMessage.CompressedPayload
*/
// TODO: 后续将使用 pb 进行 encode/decode
func (im IMMessage) Encode() ([]byte, error) {
	m, err := json2.Marshal(im)
	if err != nil {
		return nil, err
	}
	// 兼容旧版本数据，默认使用不压缩的格式
	var decode bool
	config.GetAB("im_message_decode", &decode)
	if !decode || im.CompressedPayload == nil {
		return m, nil
	}
	length := len(m)
	if length > dataMaxLength {
		// 超长则不进行压缩
		return m, nil
	}
	out := make([]byte, 4+length+len(im.CompressedPayload))
	out[0] = DataTypeIMMessage
	out[1] = byte(length)
	out[2] = byte(length >> 8)
	out[3] = byte(length >> 16) // 2 * 8
	copy(out[4:4+length], m)
	copy(out[4+length:], im.CompressedPayload)
	return out, nil
}

// Effect 特效
type Effect struct {
	EffectURL    string `json:"effect_url"`
	WebEffectURL string `json:"web_effect_url"`
}

// RoomEffect 直播间特效广播
type RoomEffect struct {
	Type   string `json:"type"`
	Event  string `json:"event"`
	RoomID int64  `json:"room_id"`
	Effect Effect `json:"effect"`
}

// NewRoomEffect new RoomEffect
func NewRoomEffect(roomID int64, e Effect) *RoomEffect {
	return &RoomEffect{
		Type:   TypeEffect,
		Event:  EventShow,
		RoomID: roomID,
		Effect: e,
	}
}

// KeyIMPubSubRandomly 获取一个随机的 key
func KeyIMPubSubRandomly() string {
	index := rand.Intn(config.Conf.IM.PubsubSize)
	return keys.KeyIMPubSub2.Format(config.Conf.IM.PubsubSize, index)
}

// KeyIMPubSubByIndex 通过 index 生成 key
func KeyIMPubSubByIndex(index int) string {
	return keys.KeyIMPubSub2.Format(config.Conf.IM.PubsubSize, index)
}

// Publish 广播消息
func Publish(m *IMMessage) error {
	val, err := m.Encode()
	if err != nil {
		return err
	}
	err = service.IMRedis.Publish(KeyIMPubSubRandomly(), val).Err()
	return err
}

const joinedCycle = 3 // 加入房间的周期，3 分钟

// KeyRoomJoined 已经进场的用户的 set（3 分钟的周期内）
func KeyRoomJoined(roomID int64) string {
	return keys.KeyIMRoomJoined2.Format(roomID, goutil.TimeNow().Minute()/joinedCycle)
}

// FilterJoined 过滤已经入场用户，返回未入场用户
func FilterJoined(roomID int64, userIDs []int64) []int64 {
	key := KeyRoomJoined(roomID)

	pipe := service.IMRedis.Pipeline()
	members := make([]interface{}, len(userIDs))
	boolCmds := make([]*redis.BoolCmd, len(members))
	for i := range members {
		members[i] = userIDs[i]
		boolCmds[i] = pipe.SIsMember(key, members[i])
	}
	pipe.SAdd(key, members...)
	// ttl 2 倍周期的时间
	// NOTICE: ttl + 周期（目前是 3 个周期）不能超过一小时，否则会出现 key 无法清空的情况
	pipe.Expire(key, joinedCycle*2*time.Minute)
	_, err := pipe.Exec()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	res := make([]int64, 0, len(userIDs))
	for i := range boolCmds {
		if !boolCmds[i].Val() {
			res = append(res, userIDs[i])
		}
	}
	return res
}

// BrotliEncode brotli 编码
/* 数据格式
第一个字节：固定 0x01
第二到第四个字节：小端字节序的未压缩的原始消息长度
后续：被压缩后的数据
*/
func BrotliEncode(payload []byte) ([]byte, error) {
	const maxLen = 0x01 << (3 * 8)
	length := len(payload)
	if length > maxLen {
		return nil, ErrDataTooLong
	}
	v := brotliEncoders.Get()
	if v == nil {
		return nil, ErrEncoderNotExists
	}
	defer brotliEncoders.Put(v)
	be := v.(*brotliEncoder)
	compressed, err := be.Encode(payload)
	if err != nil {
		return nil, err
	}
	out := make([]byte, 4+len(compressed))
	out[0] = DataTypePayload
	out[1] = byte(length)
	out[2] = byte(length >> 8)
	out[3] = byte(length >> 16) // 2 * 8
	copy(out[4:], compressed)
	return out, nil
}

type brotliEncoder struct {
	writer *brotli.Writer
}

var brotliEncoders = sync.Pool{
	New: func() interface{} {
		e := &brotliEncoder{
			writer: brotli.NewWriterOptions(nil, brotli.WriterOptions{
				Quality: brotli.DefaultCompression,
			}),
		}
		return e
	},
}

// Encode encode data
func (be *brotliEncoder) Encode(p []byte) ([]byte, error) {
	b := bytes.NewBuffer(make([]byte, 0, 1024))
	be.writer.Reset(b)
	defer be.writer.Reset(nil)
	_, err := be.writer.Write(p)
	if err != nil {
		return nil, err
	}
	err = be.writer.Close()
	if err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

// NotifyRoomGiftUpdate 通知直播间更新礼物
func NotifyRoomGiftUpdate(roomID int64) error {
	return userapi.Broadcast(roomID, map[string]interface{}{
		"type":    TypeRoom,
		"event":   EventGiftUpdate,
		"room_id": roomID,
	})
}
