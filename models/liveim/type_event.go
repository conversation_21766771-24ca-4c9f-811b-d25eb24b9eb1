package liveim

/*
每个 const () 内定义对应 type 下所用 event 和其他常量，
注释掉的 event 是定义有重名的，但是是对应 type 可以组合的
文档：https://github.com/MiaoSiLa/audio-chatroom/blob/master/doc/websocket.md
*/

// type event room
const (
	TypeRoom = "room"

	EventJoin       = "join"
	EventUpdate     = "update"
	EventStatistics = "statistics"
	EventClose      = "close"
	EventOpen       = "open"
	EventRecommend  = "recommend"
	EventGiftUpdate = "gift_update"
)

// type event activity
const (
	TypeActivity = "activity"

	EventConnectActivity = "connect"
)

// type event message
const (
	TypeMessage = "message"

	EventNew     = "new"
	EventHorn    = "horn"
	EventSticker = "sticker"
	EventDanmaku = "danmaku"
)

// type event gift
const (
	TypeGift = "gift"

	EventSend      = "send"
	EventCrossSend = "cross_send"
)

// type event question
const (
	TypeQuestion = "question"

	EventSet    = "set"
	EventAsk    = "ask"
	EventAnswer = "answer"

	AnswerTypeJoin   = "join"
	AnswerTypeFinish = "finish"
	AnswerTypeCancel = "cancel"
)

// type event creator
const (
	TypeCreator = "creator"

	EventNewRank      = "new_rank"
	EventLastHourRank = "last_hour_rank"
)

// type event member
const (
	TypeMember = "member"

	// EventJoin = "join"
	EventLeave     = "leave"
	EventJoinQueue = "join_queue"

	EventAddAdmin    = "add_admin"
	EventRemoveAdmin = "remove_admin"
	EventAddMute     = "add_mute"
	EventRemoveMute  = "remove_mute"

	EventFollowed = "followed"

	EventMedalGetNew  = "medal_get_new"
	EventMedalLevelUp = "medal_level_up"
)

// type event channel
const (
	TypeChannel = "channel"

	EventStart = "start"
	EventStop  = "stop"
)

// type event connect
const (
	TypeConnect = "connect"

	EventRequest = "request"
	EventCancel  = "cancel"
	EventConfirm = "confirm"
	EventForbid  = "forbid"
	// EventStop = "stop"
	EventClear = "clear"
)

// type event noble
const (
	TypeNoble = "noble"

	// EventJoin = "join"
	EventRegistration = "registration"
	EventRenewal      = "renewal"
)

// type event notify
const (
	TypeNotify     = "notify"
	TypeUserNotify = "user_notify"

	// notify_type 和 event 同其他的 type event
)

// type event user
const (
	TypeUser = "user"

	EventLevelUp   = "level_up"
	EventBlockUser = "block_user"
)

// type event medal
const (
	TypeMedal = "medal"
)

// type event level
const (
	TypeLevel = "level"
)

// type event interaction
const (
	TypeVote = "vote"

	EventVoteStart  = "start"
	EventVoteUpdate = "update"
	EventVoteClose  = "close"
)

// type event effect
const (
	TypeEffect = "effect"

	EventShow = "show"
)

// type event super_fan
const (
	TypeSuperFan = "super_fan"

	EventSuperFanRegistration = "registration"
	EventSuperFanRenewal      = "renewal"
)

// type event gashapon
const (
	TypeGashapon = "gashapon"

	EventGashaponGacha = "gacha"
)

// type event pk
const (
	TypePK = "pk"

	EventPKMatchStart   = "match_start"
	EventPKMatchStop    = "match_stop"
	EventPKMatchSuccess = "match_success"
	EventPKMatchFail    = "match_fail"

	EventPKInviteTimeout = "invite_timeout"

	EventPKClose        = "close"
	EventPKFinish       = "finish"
	EventPKPunishFinish = "punish_finish"

	EventPKUnmute       = "unmute"
	EventPKForcedUnmute = "forced_unmute"
	EventPKMute         = "mute"
	EventPKForcedMute   = "forced_mute"
	EventPKUpdate       = "update"

	EventPKInviteRequest = "invite_request"
	EventPKInviteRefuse  = "invite_refuse"
	EventPKInviteCancel  = "invite_cancel"
)

// type gift wall
const (
	TypeGiftWall = "gift_wall"

	EventGiftWallUpdate = "update"
)

// type red packet
const (
	TypeRedPacket = "red_packet"

	EventRedPacketEmpty  = "empty"
	EventRedPacketExpire = "expire"
	EventRedPacketGrab   = "grab"
	EventRedPacketSend   = "send"
)

// 飘屏优先级
const (
	NotifyQueueNormal = iota
	NotifyQueuePriority
)

// type pia 戏
const (
	TypePia = "pia"

	EventPiaStart = "start"
	EventPiaStop  = "stop"
)

// type 福袋
const (
	TypeLuckyBag = "lucky_bag"

	EventLuckyBagNew    = "new"
	EventLuckyBagFinish = "finish"
	EventLuckyBagRemove = "remove"
)

// type 宝盒
const (
	TypeLuckyBox = "lucky_box"

	EventLuckyBoxDraw = "draw"
)

// type 主播连线
const (
	TypeMultiConnect = "multi_connect"

	EventMute          = "mute"
	EventForcedMute    = "forced_mute"
	EventUnmute        = "unmute"
	EventForcedUnmute  = "forced_unmute"
	EventMicOff        = "mic_off"
	EventScoreUpdate   = "score_update"
	EventScoreClear    = "score_clear"
	EventApplyRequest  = "apply_request"
	EventApplyAccept   = "apply_accept"
	EventApplyCancel   = "apply_cancel"
	EventApplyRefuse   = "apply_refuse"
	EventApplyTimeout  = "apply_timeout"
	EventInviteRequest = "invite_request"
	EventInviteAccept  = "invite_accept"
	EventInviteCancel  = "invite_cancel"
	EventInviteRefuse  = "invite_refuse"
	EventInviteTimeout = "invite_timeout"
	EventKickout       = "kickout"
	EventQuit          = "quit"
	EventRankOn        = "rank_on"
	EventRankOff       = "rank_off"
	EventCrossMsgOn    = "cross_msg_on"
	EventCrossMsgOff   = "cross_msg_off"
)

// type 黑卡
const (
	TypeBlackCard = "black_card"

	EventBlackCardUpgrade = "upgrade"
)

// type 粉丝团宝箱
const (
	TypeFansBox = "fans_box"

	EventFansBoxTaskNew    = "task_new"
	EventFansBoxTaskUpdate = "task_update"
)
