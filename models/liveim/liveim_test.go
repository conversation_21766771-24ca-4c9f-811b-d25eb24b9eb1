package liveim

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/andybalholm/brotli"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/models/role"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestIMMessageSetPayload(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var payload interface{}
	payload = json.RawMessage(`[{"test":"hello world"},{"test":"hello world"},{"test":"hello world"}]`)
	var im IMMessage
	require.NoError(im.SetPayload(payload))
	assert.Equal(payload.(json.RawMessage), im.Payload)
	assert.NotNil(im.CompressedPayload)
	assert.NotEqual(payload, im.CompressedPayload)

	payload = map[string]string{"test": "hellow world"}
	im = IMMessage{}
	require.NoError(im.SetPayload(payload))
	assert.JSONEq(tutil.SprintJSON(payload), string(im.Payload))
	assert.NotNil(im.CompressedPayload)

	payload = map[string]string{"test": "<font color=\"#FFD5A8\">小蜜蜂の</font>"}
	im = IMMessage{}
	require.NoError(im.SetPayload(payload))
	assert.Equal(`{"test":"<font color=\"#FFD5A8\">小蜜蜂の</font>"}`, string(im.Payload))
	assert.NotNil(im.CompressedPayload)
}

func TestIMMessageEncode(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	payload := json.RawMessage([]byte(`{"test":"<hello> world"}`))
	var im IMMessage
	require.NoError(im.SetPayload(payload))
	jsonData, err := json2.Marshal(im)
	require.NoError(err)
	config.Conf.AB["im_message_decode"] = true
	data, err := im.Encode()
	require.NoError(err)
	jsonLength := len(jsonData)
	require.Equal(4+jsonLength+len(im.CompressedPayload), len(data))
	assert.Equal([]byte{
		DataTypeIMMessage,
		byte(jsonLength),
		byte(jsonLength >> 8),
		byte(jsonLength >> 16),
	}, data[0:4])
	assert.Equal(jsonData, data[4:4+jsonLength])
	assert.Equal(im.CompressedPayload, data[4+jsonLength:])

	config.Conf.AB["im_message_decode"] = false
	data, err = im.Encode()
	require.NoError(err)
	assert.Equal(jsonData, data)
}

func TestRoomEffect(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	e := NewRoomEffect(123, Effect{EffectURL: "123"})
	assert.Equal("effect", e.Type)
	assert.Equal("show", e.Event)
	assert.Equal(int64(123), e.RoomID)
	assert.Equal("123", e.Effect.EffectURL)
	kc.Check(e, "type", "event", "room_id", "effect")
	kc.Check(e.Effect, "effect_url", "web_effect_url")
}

func TestLivejudgmentsUserIDSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roleJudgement := role.AuthAssignment{ItemName: string(role.LiveJudgement), UserID: "10"}
	require.NoError(service.DB.Table(roleJudgement.TableName()).
		FirstOrCreate(&roleJudgement, "itemname = ? AND userid = ?",
			roleJudgement.ItemName, roleJudgement.UserID).Error)

	roleAdmin := role.AuthAssignment{ItemName: string(role.LiveAdmin), UserID: "1000"}
	require.NoError(service.DB.Table(roleJudgement.TableName()).
		FirstOrCreate(&roleAdmin, "itemname =? AND userid = ?", roleAdmin.ItemName, roleAdmin.UserID).Error)
	defer func() {
		assert.NoError(service.DB.Delete(&roleAdmin, "itemname = ? AND userid = ?",
			roleAdmin.ItemName, roleAdmin.UserID).Error)
	}()
	service.Cache5Min.Flush()
	userIDSet := LiveJudgmentUserIDSet()
	_, ok := userIDSet[10]
	assert.True(ok)
	_, ok = userIDSet[1000]
	assert.True(ok)
	userIDSet[12] = struct{}{}
	service.Cache5Min.Set(keys.LocalKeyIMJoinInvisibleUserIDs0.Format(), userIDSet, 0)
	userIDSet = LiveJudgmentUserIDSet()
	_, ok = userIDSet[12]
	assert.True(ok)
}

func TestHasInvisibleRoles(t *testing.T) {
	assert := assert.New(t)

	userID := int64(10)
	cancel := mrpc.SetMock(userapi.URLGoAuthUserIDs, func(any) (any, error) {
		return map[string]any{
			"user_ids": []int64{userID},
		}, nil
	})
	defer cancel()

	service.Cache5Min.Flush()

	assert.True(HasInvisibleRoles(mrpc.UserContext{}, userID))

	key := keys.LocalKeyIMJoinInvisibleUserIDs0.Format()
	service.Cache5Min.Set(key, map[int64]struct{}{}, 0)
	assert.False(HasInvisibleRoles(mrpc.UserContext{}, userID))
}

func TestGetInvisibleRoleUserIDMap(t *testing.T) {
	assert := assert.New(t)

	userID := int64(12345)
	service.Cache5Min.Delete(keys.LocalKeyIMJoinInvisibleUserIDs0.Format())

	// mock userapi.AuthUserIDs 返回 userID
	cancel := mrpc.SetMock(userapi.URLGoAuthUserIDs, func(any) (any, error) {
		return map[string]any{
			"user_ids": []int64{userID},
		}, nil
	})
	defer cancel()

	// 缓存未命中，走接口
	set := GetInvisibleRoleUserIDMap(mrpc.UserContext{})
	_, ok := set[userID]
	assert.True(ok)

	// 缓存命中
	set2 := GetInvisibleRoleUserIDMap(mrpc.UserContext{})
	assert.Equal(set, set2)

	// 接口异常，返回空 map
	service.Cache5Min.Flush()
	cancel2 := mrpc.SetMock(userapi.URLGoAuthUserIDs, func(any) (any, error) {
		return nil, fmt.Errorf("mock error")
	})
	defer cancel2()
	set3 := GetInvisibleRoleUserIDMap(mrpc.UserContext{})
	assert.Empty(set3)
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(IMMessage{}, "type", "room_id", "user_id", "priority", "payload")
}

func TestIndexImPubSub(t *testing.T) {
	assert := assert.New(t)

	for i := 0; i < config.Conf.IM.PubsubSize; i++ {
		assert.Equal(fmt.Sprintf("live-service:im:pubsub:001-00%d", i), KeyIMPubSubByIndex(i))
	}
}

func TestPublish(t *testing.T) {
	assert := assert.New(t)

	assert.NoError(Publish(&IMMessage{Type: IMMessageTypeNormal, RoomID: 1, Payload: json.RawMessage(`{"type":"normal"}`)}))
	assert.NoError(Publish(&IMMessage{Type: IMMessageTypeAll, Payload: json.RawMessage(`{"type":"all"}`)}))
}

func TestJoinedCycle(t *testing.T) {
	assert := assert.New(t)

	assert.Less(joinedCycle*3, 60)
}

func TestKeyRoomJoined(t *testing.T) {
	assert := assert.New(t)

	now := time.Unix(60, 0)
	goutil.SetTimeNow(func() time.Time { return now })
	defer goutil.SetTimeNow(nil)

	assert.Equal("live-service:im:room:1:joined:00", KeyRoomJoined(1))
	now = time.Unix(240, 0)
	assert.Equal("live-service:im:room:1:joined:01", KeyRoomJoined(1))
}

func TestFilterJoined(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := time.Unix(1800, 0)
	goutil.SetTimeNow(func() time.Time { return now })
	defer goutil.SetTimeNow(nil)

	key := KeyRoomJoined(123)
	require.NoError(service.IMRedis.Del(key).Err())
	// NOTICE: 实际中 0 也会被加入
	assert.Equal([]int64{0, 1}, FilterJoined(123, []int64{0, 1}))
	assert.Empty(FilterJoined(123, []int64{0, 1}))
	ttl, err := service.IMRedis.TTL(key).Result()
	require.NoError(err)
	assert.LessOrEqual(float64(2*joinedCycle*60-1), ttl.Seconds())
	assert.Less(ttl.Minutes()+joinedCycle, float64(60))
}

func TestBrotliEncode(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	raw := []byte("Hello world Hello world")
	res, err := BrotliEncode(raw)
	require.NoError(err)
	tutil.PrintJSON(res)
	require.Greater(len(res), 4)
	assert.EqualValues(0x01, res[0])

	length := binary.LittleEndian.Uint32([]byte{
		res[1],
		res[2],
		res[3],
		0,
	})
	assert.Len(raw, int(length))
}

func TestBrotliEncoder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	payloads := [2][]byte{
		[]byte("hello world"),
		[]byte("test test test"),
	}
	var expected [2][]byte
	for i := range payloads {
		buff := bytes.NewBuffer([]byte{})
		w := brotli.NewWriterOptions(buff, brotli.WriterOptions{
			Quality: brotli.DefaultCompression,
		})
		_, err := w.Write(payloads[i])
		require.NoError(err, i)
		require.NoError(w.Close(), i) // 使用 close 和使用 flush 不一样
		expected[i] = buff.Bytes()
	}
	v := brotliEncoders.Get()
	require.NotNil(v)
	be := v.(*brotliEncoder)
	var actualArr [2][]byte
	for i := range payloads {
		var err error
		actual, err := be.Encode(payloads[i])
		require.NoError(err, i)
		assert.Equal(expected[i], actual, i)
		actualArr[i] = actual
		// https://www.multiutil.com/brotli-to-text-decompress/
		tutil.PrintJSON(actual)
	}
}

func TestNotifyRoomGiftUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var p json.RawMessage
	cancel := mrpc.SetMock("im://broadcast", func(param interface{}) (interface{}, error) {
		p = param.(json.RawMessage)
		return nil, nil
	})
	defer cancel()

	err := NotifyRoomGiftUpdate(1)
	require.NoError(err)
	assert.JSONEq(tutil.SprintJSON(map[string]interface{}{
		"room_id": int64(1),
		"payload": map[string]interface{}{
			"type":    TypeRoom,
			"event":   EventGiftUpdate,
			"room_id": int64(1),
		},
	}), string(p))
}
