package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
)

// BannerCollection collection banners
func BannerCollection() *mongo.Collection {
	return service.MongoDB.Collection("banners")
}

// Banner struct
type Banner struct {
	OID           primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	URL           string             `bson:"url" json:"url"`
	AppURL        string             `bson:"app_url" json:"app_url"`
	ImageURL      string             `bson:"image_url" json:"image_url"`
	SmallImageURL string             `bson:"small_image_url,omitempty" json:"small_image_url,omitempty"`
	Title         string             `bson:"title" json:"title"`
	Order         *int               `bson:"order" json:"order,omitempty"`
	CreateTime    time.Time          `bson:"create_time" json:"-"`
	RoomID        int64              `bson:"-" json:"room_id,omitempty"`
}

// FindAllBanners returns all banners
func FindAllBanners() (banners []*Banner, err error) {
	banners = make([]*Banner, 0)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	findOpts := options.Find().SetSort(bson.M{"order": 1})
	cursor, err := BannerCollection().Find(ctx, bson.M{}, findOpts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &banners)
	return banners, err
}

// ClearAllBanners clear all banners
func ClearAllBanners() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := BannerCollection().DeleteMany(ctx, bson.M{})
	if err != nil {
		return err
	}
	return nil
}

// AddBanners add all banners
func AddBanners(banners []Banner) error {
	bannerInterface := make([]interface{}, len(banners))
	for index, value := range banners {
		bannerInterface[index] = value
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := BannerCollection().InsertMany(ctx, bannerInterface)
	if err != nil {
		return err
	}
	return nil
}
