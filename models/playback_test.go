package models

import (
	"testing"
	"time"

	lorem "github.com/drhodes/golorem"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var testPubURL = "https://static-test.missevan.com/"

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	collection := service.MongoDB.Collection(CollectionNamePlayback)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	pb := Playback{}
	err := collection.FindOne(ctx, bson.M{}).Decode(&pb)
	require.NoError(err)
	assert.NotEmpty(pb.OID.Hex())

	title := lorem.Sentence(2, 3)
	err = pb.Update(bson.M{"title": title})
	require.NoError(err)

	err = collection.FindOne(ctx, bson.M{"_id": pb.OID}).Decode(&pb)
	require.NoError(err)
	// TODO: test updated_time
	assert.Equal(title, pb.Title)
}

func TestPlaybackCounts(t *testing.T) {
	assert := assert.New(t)

	pb := &Playback{}
	n, err := pb.CountPage()
	assert.NoError(err)
	assert.True(n >= 0)
}

func TestGetPlaybackByPage(t *testing.T) {
	assert := assert.New(t)

	pb := &Playback{}
	_, err := pb.FindByPage(1)
	assert.NoError(err)
}

func TestAddPlaybackInfo(t *testing.T) {
	assert := assert.New(t)

	pb := []Playback{{}}
	AddPlaybackInfo(pb)
	assert.Equal(defaultPlaybackCover, pb[0].GetCover())
	assert.Equal(testPubURL+defaultPlaybackCover, pb[0].SchemeToURL())
}

func TestUpdatePlayback(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	collection := service.MongoDB.Collection(CollectionNamePlayback)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 02, 01, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	testRoomID := int64(20201202)
	_, err := collection.UpdateOne(ctx, bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"room_id": testRoomID, "archive_time": now, "archive": ArchiveUnready}}, options.Update().SetUpsert(true))
	require.NoError(err)

	require.NoError(UpdatePlayback(testRoomID, testRoomID, now, now, 0))
	var p Playback
	require.NoError(collection.FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&p))
	assert.Equal(ArchiveReadyDownload, p.Archive)
	assert.Equal(p.ArchiveTime.Unix(), now.Add(SplitMergeDuration).Unix())
}

func TestSetPriority(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok, err := SetPriority(primitive.NewObjectIDFromTimestamp(goutil.TimeNow()), 1)
	require.NoError(err)
	assert.False(ok)

	collection := service.MongoDB.Collection(CollectionNamePlayback)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testRoomID := 2021021801
	var res Playback
	err = collection.FindOneAndUpdate(ctx,
		bson.M{"room_id": testRoomID},
		bson.M{"$set": bson.M{"room_id": testRoomID, "archive": ArchiveReadyDownload}},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).Decode(&res)
	require.NoError(err)
	ok, err = SetPriority(res.OID, 1)
	require.NoError(err)
	assert.True(ok)
}
