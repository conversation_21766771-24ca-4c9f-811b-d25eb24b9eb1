package livemedalreview

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var m = LiveMedalReview{ID: 1034, UserID: 202001, RoomID: 202001, Name: "新年 ab", Status: StatusRefused}

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := FindOne(9090909090)
	require.NoError(err)
	assert.Nil(r)

	r, err = FindOne(m.ID)
	require.NoError(err)
	assert.Equal(m.UserID, r.UserID)
}

func TestFindLastMedalReview(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := FindLastMedalReview(m.UserID)
	require.NoError(err)
	assert.Equal(r.ID, m.ID)
	assert.Equal(r.Status, StatusRefused)

	r, err = FindLastMedalReview(9090909090)
	require.NoError(err)
	assert.Nil(r)
}

func TestFindMedalReview(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := FindOptions{
		Sort:   CreateTimeDesc,
		UserID: 202001,
	}

	r, _, err := FindMedalReview(1, 50, opt)
	require.NoError(err)
	assert.Equal(m.UserID, r[0].UserID)
}

func TestFinishReviewing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m := LiveMedalReview{UserID: 202020, RoomID: 202020, Name: "通过 a", Status: StatusReviewing}
	require.NoError(service.DB.Where("user_id = ? AND status = ?",
		m.UserID, m.Status).FirstOrCreate(&m).Error)

	after, err := FinishReviewing(m.ID, StatusPassed, nil)
	require.NoError(err)
	assert.Equal(after.Status, StatusPassed)

	m.Status = StatusReviewing
	require.NoError(service.DB.Save(&m).Error)
	after, err = FinishReviewing(m.ID, StatusRefused, nil)
	require.NoError(err)
	assert.Equal(after.Status, StatusRefused)

	after, err = FinishReviewing(1234567890, StatusRefused, nil)
	require.NoError(err)
	assert.Nil(after)
}

func TestUpdateMedal(t *testing.T) {

	assert := assert.New(t)
	require := require.New(t)
	// 成功
	m := LiveMedalReview{UserID: 892, RoomID: 369892, Name: "测试 A"}
	require.NoError(service.DB.Where("user_id = ? AND status = ?",
		m.UserID, StatusReviewing).FirstOrCreate(&m).Error)

	after, err := UpdateMedal(m.ID)
	assert.NoError(err)
	assert.NotNil(after)
	// 失败的
	_, err = UpdateMedal(999999999999)
	require.NoError(err)
}

func TestInEditTimeLimit(t *testing.T) {
	assert := assert.New(t)
	now := goutil.TimeNow()
	nowDate := now.AddDate(0, 0, -31)
	ok := InEditTimeLimit(nowDate.Unix())
	assert.False(ok)

	nowDate = now.AddDate(0, 0, +1)
	ok = InEditTimeLimit(nowDate.Unix())
	assert.True(ok)
}
