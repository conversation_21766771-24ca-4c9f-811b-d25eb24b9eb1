package livemedalreview

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TableName table name
func TableName() string {
	return "live_medal_review"
}

// sort
const (
	CreateTimeAsc    = "create_time ASC"
	CreateTimeDesc   = "create_time DESC"
	ModifiedTimeDesc = "modified_time DESC"
)

// medal status
const (
	StatusRefused   = iota - 1 // 审核被拒绝
	StatusReviewing            // 审核中
	StatusPassed               // 审核通过
)

// LimitDaysOfEditMedal 修改勋章时间间隔限制（天）
const LimitDaysOfEditMedal = 30

// FindOptions Find 选项
type FindOptions struct {
	Sort      string `form:"sort"`
	Username  string `form:"username"`
	MedalName string `form:"medal_name"`
	UserID    int64  `form:"user_id"`
	Type      int    `form:"type"`
}

const (
	typeReviewing = iota + 1
	typeRejected
	typePass
)

// LiveMedalReview 主播粉丝勋章审核表
type LiveMedalReview struct {
	ID int64 `gorm:"column:id;primary_key" json:"id"`

	UserID int64  `gorm:"column:user_id" json:"user_id"`
	RoomID int64  `gorm:"column:room_id" json:"room_id"`
	Name   string `gorm:"column:name" json:"name"`
	Status int    `gorm:"column:status" json:"status"`

	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
}

// TableName table name
func (LiveMedalReview) TableName() string {
	return TableName()
}

// BeforeCreate gorm hook BeforeCreate
func (lr *LiveMedalReview) BeforeCreate() error {
	lr.CreateTime = goutil.TimeNow().Unix()
	lr.ModifiedTime = lr.CreateTime
	return nil
}

// BeforeUpdate gorm hook BeforeUpdate
func (lr *LiveMedalReview) BeforeUpdate() error {
	lr.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// WithUserInfo LiveMedalReview with user info
type WithUserInfo struct {
	LiveMedalReview
	Username string `gorm:"column:username" json:"username"`
}

// FindOne 通过 ID 查找
func FindOne(id int64) (*LiveMedalReview, error) {
	var l LiveMedalReview
	err := service.DB.Where("id = ?", id).First(&l).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
	}
	return &l, err
}

// FindLastMedalReview 查找最新的勋章申请记录
func FindLastMedalReview(userID int64) (*LiveMedalReview, error) {
	lm := new(LiveMedalReview)
	err := service.DB.Where("user_id = ?", userID).Order("modified_time DESC").First(&lm).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
	}
	return lm, err
}

// FindMedalReview 查找勋章审核记录
// 默认创建时间倒序排序
func FindMedalReview(p, pageSize int64, opt FindOptions) ([]*WithUserInfo, goutil.Pagination, error) {
	db := service.DB.Table(TableName() + " AS r").
		Joins(fmt.Sprintf("LEFT JOIN %s AS u ON u.id = r.user_id", mowangskuser.TableName())).Select("u.username, r.*")
	switch opt.Type {
	case typeReviewing:
		db = db.Where("r.status = ?", StatusReviewing)
	case typeRejected:
		db = db.Where("r.status < ?", StatusReviewing)
	case typePass:
		db = db.Where("r.status = ?", StatusPassed)
	}
	db = db.Order(opt.Sort)
	if opt.UserID != 0 {
		db = db.Where("r.user_id = ?", opt.UserID)
	}
	if opt.MedalName != "" {
		db = db.Where("r.name LIKE ?", servicedb.ToLikeStr(opt.MedalName))
	}
	if opt.Username != "" {
		db = db.Where("u.username LIKE ?", servicedb.ToLikeStr(opt.Username))
	}
	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []*WithUserInfo{}, pa, nil
	}
	var res []*WithUserInfo
	db = pa.ApplyTo(db).Find(&res)
	return res, pa, db.Error
}

// UpdateMedal 将审核通过的的勋章更新到 DB
func UpdateMedal(medalID int64) (after *LiveMedalReview, err error) {
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		after, err = FinishReviewing(medalID, StatusPassed, tx)
		if err != nil {
			return err
		}
		if after == nil {
			return nil
		}
		// 开始更新 mongodb
		// NOTICE: 由于在 mysql 事务中，更新 mongodb 必须放在 mysql 运行之后
		// TODO: 放到 mongodb 事务中处理
		// example: https://github.com/mongodb/mongo-go-driver/blob/master/examples/documentation_examples/examples.go
		update := bson.M{"updated_time": goutil.TimeNow(), "medal": room.NewMedal(after.Name)}
		_, err = room.Update(after.RoomID, update)
		if err != nil {
			return err
		}
		err = livemedal.UpdateName(after.RoomID, after.Name)
		if err != nil {
			return err
		}
		return nil
	})
	return
}

// FinishReviewing 尝试将主播审核中的粉丝勋章通过或拒绝
func FinishReviewing(medalID int64, passOrRefuse int, tx *gorm.DB) (after *LiveMedalReview, err error) {
	if passOrRefuse != StatusPassed && passOrRefuse != StatusRefused {
		panic(fmt.Sprintf("wrong input status: %d", passOrRefuse))
	}
	doTx := func(tx *gorm.DB) error {
		after = new(LiveMedalReview)
		err := tx.First(after, "id = ? AND status = ?", medalID, StatusReviewing).Error
		if err != nil {
			if gorm.IsRecordNotFoundError(err) {
				err = nil
			}
			after = nil
			return err
		}

		// NOTICE: 暂不支持通过 BeforeUpdate 更新 ModifiedTime
		after.Status = passOrRefuse
		after.ModifiedTime = goutil.TimeNow().Unix()
		return tx.Model(after).Updates(after).Error
	}
	if tx == nil {
		err = servicedb.Tx(service.DB, doTx)
	} else {
		err = doTx(tx)
	}
	return
}

// InEditTimeLimit 是否在改名限制时间段内
func InEditTimeLimit(date int64) bool {
	y, m, d := time.Unix(date, 0).Date()
	LimitEnd := time.Date(y, m, d+LimitDaysOfEditMedal, 0, 0, 0, 0, time.Local)
	return goutil.TimeNow().Before(LimitEnd)
}
