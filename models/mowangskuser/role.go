package mowangskuser

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/role"
)

// SpecialRolesUserIDs 特殊用户集合
// TODO: 迁移到 missevan-go
func SpecialRolesUserIDs(roles []role.Role, cacheKey string) map[int64]struct{} {
	copySet := func(s map[int64]struct{}) map[int64]struct{} {
		res := make(map[int64]struct{}, len(s))
		for userID := range s {
			res[userID] = struct{}{}
		}
		return res
	}
	if cacheKey != "" {
		v, ok := service.Cache5Min.Get(cacheKey)
		if ok {
			set := v.(map[int64]struct{})
			return copySet(set)
		}
	}
	var rec []role.AuthAssignment
	err := service.DB.Select("userid").Where("itemname IN (?)", roles).Find(&rec).Error
	if err != nil {
		logger.Error(err)
		return map[int64]struct{}{}
	}
	set := make(map[int64]struct{}, len(rec))
	for i := range rec {
		userID, err := strconv.ParseInt(rec[i].UserID, 10, 64)
		if err != nil {
			logger.WithField("userid", rec[i].UserID).Error(err)
			continue
		}
		set[userID] = struct{}{}
	}
	if cacheKey != "" {
		service.Cache5Min.SetDefault(cacheKey, copySet(set))
	}
	return set
}

// IsSpecialRolesUser 是否是特殊角色用户
func IsSpecialRolesUser(roles []role.Role, cacheKey string, userID int64) bool {
	set := SpecialRolesUserIDs(roles, cacheKey)
	_, ok := set[userID]
	return ok
}
