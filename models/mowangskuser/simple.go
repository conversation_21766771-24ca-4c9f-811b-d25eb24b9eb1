package mowangskuser

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "mowangskuser"

// TableName table name
func TableName() string {
	return tableName
}

// consts
const (
	UserIconTypeCartoon = 0 // 二次元头像
	UserIconTypeNormal  = 1 // 普通头像
	UserIconTypeSpecial = 2 // 特殊头像
)

// confirm
const (
	ConfirmCertificated = 2 // 通过实名认证：开直播
)

// Simple 简单用户信息结构
type Simple struct {
	ID           int64  `gorm:"column:id" json:"user_id"`
	Confirm      uint   `gorm:"column:confirm" json:"-"`
	Username     string `gorm:"column:username" json:"username"`
	UserIntro    string `gorm:"column:userintro" json:"-"`
	IconType     int64  `gorm:"column:icontype" json:"-"`
	IconURL      string `gorm:"column:iconurl" json:"iconurl"`
	Avatar       string `gorm:"column:avatar" json:"-"`
	BoardIconURL string `gorm:"column:boardiconurl" json:"-"`
}

// DB the db instance of Simple model
func (Simple) DB() *gorm.DB {
	return service.DB.Table(TableName())
}

// TableName table name
func (Simple) TableName() string {
	return TableName()
}

// ExistsAll 根据 userIDs 判断用户是否全部存在
func ExistsAll(userIDs []int64) (bool, error) {
	var count int
	err := service.DB.Table(tableName).Where("id IN (?)", userIDs).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return len(userIDs) == count, err
}

// FindByUserID 通过 UserID 查找单个简单用户信息
func FindByUserID(userID int64) (*Simple, error) {
	u := new(Simple)
	err := service.DB.Select("id, confirm, username, userintro, icontype, iconurl, avatar, boardiconurl").
		Find(u, "id = ?", userID).Error
	if servicedb.IsErrNoRows(err) {
		return nil, nil
	}
	if err = u.processUserIconURL(); err != nil {
		return nil, err
	}
	return u, err
}

// FindSimpleList 通过 UserIDs 查找简单用户信息
func FindSimpleList(userIDs []int64) ([]*Simple, error) {
	if len(userIDs) == 0 {
		return nil, nil
	}
	users := make([]*Simple, 0, len(userIDs))
	err := service.DB.Select("id, username, userintro, icontype, iconurl, avatar, boardiconurl").
		Find(&users, "id IN (?)", userIDs).Error
	if err != nil {
		return nil, err
	}
	if err = BuildUserIconURL(users); err != nil {
		return nil, err
	}
	return users, nil
}

// FindSimpleMap 查询用户简单用户信息，返回 map[userID]*Simple
func FindSimpleMap(userIDs []int64) (map[int64]*Simple /* map[userID]*Simple */, error) {
	userIDs = util.Uniq(userIDs)
	tmp, err := FindSimpleList(userIDs)
	if err != nil {
		return nil, err
	}
	return SimpleSliceToMap(tmp), nil
}

// SimpleSliceToMap Simple slice 转 map[userID]*Simple
func SimpleSliceToMap(s []*Simple) map[int64]*Simple /* map[userID]*Simple */ {
	m := make(map[int64]*Simple, len(s))
	for i := 0; i < len(s); i++ {
		if s[i] != nil {
			m[s[i].ID] = s[i]
		}
	}
	return m
}

// processUserIconURL processes iconurl of user
// NOTICE: 不要多次调用
func (u *Simple) processUserIconURL() error {
	s := &user.Simple{
		IconType:     u.IconType,
		IconURL:      u.IconURL,
		Avatar:       u.Avatar,
		BoardIconURL: u.BoardIconURL,
	}
	err := s.AfterFind() // 由 missevan-go 中的 AfterFind 处理用户头像 IconURL
	if err != nil {
		return err
	}
	u.IconURL = s.BoardIconURL2
	return nil
}

// BuildUserIconURL 处理用户头像
func BuildUserIconURL(users []*Simple) error {
	for i := 0; i < len(users); i++ {
		if err := users[i].processUserIconURL(); err != nil {
			return err
		}
	}
	return nil
}

// SearchUserByUsername 通过用户名查找用户（在指定的 ID 范围内查询）
func SearchUserByUsername(username string, userIDsRange []int64) (userIDs []int64, err error) {
	if username == "" || len(userIDsRange) == 0 {
		return
	}
	err = Simple{}.DB().
		Select("id").
		Where("username LIKE ?", servicedb.ToLikeStr(username)).
		Where("id IN (?)", userIDsRange).
		Pluck("id", &userIDs).Error

	return
}

// SearchUserIDs 在 allUserIDs 中查找指定 userID 或者用户名模糊匹配 username 的所有用户 ID
func SearchUserIDs(userID int64, username string, allUserIDs []int64) ([]int64, error) {
	if len(allUserIDs) == 0 {
		return []int64{}, nil
	}
	if userID != 0 {
		if !goutil.HasElem(allUserIDs, userID) {
			return []int64{}, nil
		}
		allUserIDs = []int64{userID}
	}
	if username != "" {
		return SearchUserByUsername(username, allUserIDs)
	}
	return allUserIDs, nil
}
