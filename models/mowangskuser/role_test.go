package mowangskuser

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/models/role"
)

func TestSpecialRolesUserIDs(t *testing.T) {
	assert := assert.New(t)

	roles := []role.Role{role.LiveAdmin}
	set1 := SpecialRolesUserIDs(roles, "")
	assert.NotEmpty(set1)
	set2 := SpecialRolesUserIDs(roles, "123")
	assert.Equal(set1, set2)
	set3 := SpecialRolesUserIDs(roles, "123")
	assert.Equal(set1, set3)
}

func TestIsSpecialRolesUser(t *testing.T) {
	assert := assert.New(t)

	roles := []role.Role{role.LiveAdmin}
	set := SpecialRolesUserIDs(roles, "")
	for userID := range set {
		assert.True(IsSpecialRolesUser(roles, "", userID))
	}
}
