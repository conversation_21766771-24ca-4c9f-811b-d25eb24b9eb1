package mowangskuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/config/params"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("mowangskuser", TableName())
	assert.Equal("mowangskuser", Simple{}.TableName())
}

func TestExistsAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := ExistsAll([]int64{12})
	require.NoError(err)
	assert.True(exists)

	exists, err = ExistsAll([]int64{12, 9999999})
	require.NoError(err)
	assert.False(exists)
}

func TestFindByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	user, err := FindByUserID(12)
	require.NoError(err)
	assert.NotEmpty(user)
	assert.Equal("零月", user.Username)

	// not exists
	user, err = FindByUserID(-1)
	require.NoError(err)
	assert.Nil(user)
}

func TestFindSimpleList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	users, err := FindSimpleList([]int64{1, 12})
	require.NoError(err)
	assert.Len(users, 2)
}

func TestFindSimpleMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	users, err := FindSimpleMap([]int64{1, 12})
	require.NoError(err)
	assert.Len(users, 2)
	assert.NotNil(users[1])
	assert.NotNil(users[12])
}

func TestSimpleSliceToMap(t *testing.T) {
	assert := assert.New(t)
	s := []*Simple{nil, {ID: 1}, {ID: 23}}
	m := SimpleSliceToMap(s)
	assert.Len(m, 2)
	assert.Equal(s[1], m[1])
	assert.Equal(s[2], m[23])
}

func TestSimpleProcessUserIconURL(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s := &Simple{
		IconType: UserIconTypeCartoon,
	}
	require.NoError(s.processUserIconURL())
	url := storage.ParseSchemeURL(config.Conf.Params.URL.ProfileURL + "icon01.png")
	assert.Equal(url, s.IconURL)

	s = &Simple{
		IconType: UserIconTypeNormal,
	}
	require.NoError(s.processUserIconURL())
	url = storage.ParseSchemeURL(params.URL.DefaultIconURL)
	assert.Equal(url, s.IconURL)

	s = &Simple{
		IconType: UserIconTypeSpecial,
		IconURL:  "http://test/icon01.png",
	}
	require.NoError(s.processUserIconURL())
	assert.Equal("http://test/icon01.png", s.IconURL)
}

func TestSearchUserByUsername(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userIDs, err := SearchUserByUsername("", []int64{})
	require.NoError(err)
	assert.Empty(userIDs)

	userIDs, err = SearchUserByUsername("月", []int64{})
	require.NoError(err)
	assert.Empty(userIDs)

	userIDs, err = SearchUserByUsername("月", []int64{1, 12})
	require.NoError(err)
	assert.NotEmpty(userIDs)
	assert.Equal(userIDs, []int64{12})
}

func TestSearchUserIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	foundUserIDs, err := SearchUserIDs(11, "", nil)
	require.NoError(err)
	assert.Len(foundUserIDs, 0)
	foundUserIDs, err = SearchUserIDs(11, "", []int64{1, 2, 3})
	require.NoError(err)
	assert.Len(foundUserIDs, 0)
	foundUserIDs, err = SearchUserIDs(1, "", []int64{1, 2, 3})
	require.NoError(err)
	assert.Equal([]int64{1}, foundUserIDs)
	foundUserIDs, err = SearchUserIDs(0, "零月", []int64{1, 2, 3, 12, 13, 14, 15})
	require.NoError(err)
	assert.Equal([]int64{12, 13, 14, 15}, foundUserIDs)
	foundUserIDs, err = SearchUserIDs(12, "零月", []int64{1, 2, 3, 12, 13, 14, 15})
	require.NoError(err)
	assert.Equal([]int64{12}, foundUserIDs)
	foundUserIDs, err = SearchUserIDs(0, "", []int64{1, 2, 3})
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, foundUserIDs)
}
