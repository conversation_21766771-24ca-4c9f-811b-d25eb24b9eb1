package userstatus

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAddUserSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	require.NoError(GeneralSetOne(bson.M{"user_id": userID},
		bson.M{"t_time_purchase": time.Unix(0, 0)}))
	gsBefore, _, err := UserGeneral(userID, nil)
	require.NoError(err)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time { return now })
	defer goutil.SetTimeNow(nil)
	require.NoError(AddUserSpend(userID, 10))
	gsAfter, _, err := UserGeneral(userID, nil)
	require.NoError(err)
	assert.EqualValues(10, gsAfter.TotalSpend-gsBefore.TotalSpend)
}

func TestTodayFirstSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	first, err := todayFirstSpend(-1)
	require.NoError(err)
	assert.True(first, "不存在的用户")

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time { return time.Unix(0, 0) })
	defer goutil.SetTimeNow(nil)
	first, err = todayFirstSpend(12)
	require.NoError(err)
	assert.False(first)

	goutil.SetTimeNow(func() time.Time {
		return now.Add(24 * time.Hour)
	})
	first, err = todayFirstSpend(12)
	require.NoError(err)
	assert.True(first)
}
