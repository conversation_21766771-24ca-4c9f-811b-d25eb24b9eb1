package userstatus

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// AddUserSpend 增加用户消费
// TODO: 支持用户消费增加经验值
func AddUserSpend(userID, spend int64) error {
	update := bson.M{"$inc": bson.M{"total_spend": spend}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userMetaCollection().UpdateOne(ctx, bson.M{"user_id": userID},
		update, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	return nil
}

// todayFirstSpend 是否是该用户今日第一次消费
func todayFirstSpend(userID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var up userPurchase
	err := userMetaCollection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(bson.M{"t_time_purchase": 1})).Decode(&up)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return true, nil
		}
		return false, err
	}
	bod := util.BeginningOfDay(goutil.TimeNow())
	return up.TimePurchase.Before(bod), nil
}
