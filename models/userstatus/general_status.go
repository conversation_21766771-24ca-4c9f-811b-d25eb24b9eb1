package userstatus

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 封禁类型
const (
	TypeBanForever  = iota // 永久封禁
	TypeBanDuration        // 封禁一段时间
)

// 全站喇叭类型
const (
	HornTypeNoble     = iota + 1 // 贵族喇叭
	HornTypeBlackCard            // 黑卡喇叭
)

// Ban 封禁信息
type Ban struct {
	Type int `bson:"type" json:"type"`
	// 封禁开始结束时间为秒级时间戳
	StartTime  int64  `bson:"start_time" json:"start_time"`
	ExpireTime *int64 `bson:"expire_time,omitempty" json:"expire_time,omitempty"`
}

// GeneralStatus 通用状态
type GeneralStatus struct {
	UserID           int64 `bson:"user_id" json:"-"`
	Invisible        *bool `bson:"invisible" json:"invisible,omitempty"` // 没有权限为 nil
	NobleHornNum     int64 `bson:"noble_horn_num" json:"-"`
	BlackCardHornNum int64 `bson:"black_card_horn_num" json:"-"`

	HornNum int64 `bson:"-" json:"horn_num"`

	RecommendNum *int `bson:"recommend_num" json:"recommend_num,omitempty"` // 没有权限为 nil

	Ban *Ban `bson:"ban,omitempty" json:"-"`

	TotalSpend int64 `bson:"total_spend" json:"-"`

	BackpackNotifiedTime int64 `bson:"backpack_notified_time,omitempty" json:"-"` // 背包礼物过期通知时间，秒级时间戳
}

func gsProjection() map[string]int {
	return mongodb.NewProjection(
		"user_id, invisible, noble_horn_num, black_card_horn_num, recommend_num, ban, total_spend")
}

func (status *GeneralStatus) checkHornNum() {
	status.NobleHornNum = max(0, status.NobleHornNum)
	status.BlackCardHornNum = max(0, status.BlackCardHornNum)
	// NOTICE: 如果有其他的全站喇叭，记得每个都要检查
	status.HornNum = status.NobleHornNum + status.BlackCardHornNum
}

// UserMetaCollection collection user_meta
// 通用设置用到的 collection
func UserMetaCollection() *mongo.Collection {
	return service.MongoDB.Collection("user_meta")
}

// UserGeneral 该用户的通用的状态变量
// NOTICE: 未查询背包
func UserGeneral(userID int64, c goutil.UserContext) (GeneralStatus, *vip.UserVip, error) {
	uv, err := vip.UserActivatedVip(userID, false, c)
	if err != nil {
		return GeneralStatus{}, uv, err
	}
	g, err := findGeneralSettings(userID, uv)
	if err != nil {
		return g, uv, err
	}

	return g, uv, nil
}

// HornNum 某用户的喇叭数目
func HornNum(userID int64) (nobleHornNum, blackCardHornNum int64) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var rec GeneralStatus
	err := UserMetaCollection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(bson.M{"noble_horn_num": 1, "black_card_horn_num": 1})).Decode(&rec)
	if err != nil && err != mongo.ErrNoDocuments {
		logger.Error(err)
		return 0, 0
	}
	rec.checkHornNum()
	return rec.NobleHornNum, rec.BlackCardHornNum
}

func findGeneralSettings(userID int64, uv *vip.UserVip) (res GeneralStatus, err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err = UserMetaCollection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(gsProjection())).
		Decode(&res)
	if mongodb.IsNoDocumentsError(err) {
		err = nil
	}
	if !vip.HavePrivilege(uv, vip.PrivilegeInvisible) {
		res.Invisible = nil
	} else if res.Invisible == nil {
		res.Invisible = new(bool)
	}
	if !vip.HavePrivilege(uv, vip.PrivilegeRecommend) {
		res.RecommendNum = nil
	} else if res.RecommendNum == nil {
		res.RecommendNum = new(int)
	}

	res.HornNum = res.NobleHornNum + res.BlackCardHornNum
	return
}

// GeneralSetOne updateOne 的封装，查询不到会插入
// NOTICE: 注意保证 set 包含插入的必要字段
// TODO: user_id 应该放到 $setOnInsert 中
func GeneralSetOne(filter, set interface{}) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := UserMetaCollection().UpdateOne(ctx, filter,
		update{Set: set}, options.Update().SetUpsert(true))
	return err
}

// SetInvisible 设置隐身
func SetInvisible(userID int64, invisible bool) error {
	filter := bson.M{"user_id": userID}
	set := bson.M{"user_id": userID, "invisible": invisible}
	return GeneralSetOne(filter, set)
}

// Invisible 获取隐身，没有根据贵族判断状态
func Invisible(userID int64) bool {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var res GeneralStatus
	err := UserMetaCollection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(bson.M{
			"invisible": 1,
		})).Decode(&res)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		logger.Error(err)
		return false
	}
	if res.Invisible != nil {
		return *res.Invisible
	}
	return false
}

type update struct {
	Set interface{} `bson:"$set,omitempty"`
	Inc interface{} `bson:"$inc,omitempty"`
}

// IncreaseHornRecommendNum 发放喇叭和神话推荐
func IncreaseHornRecommendNum(userID int64, hornNum, recommendNum uint) error {
	if userID <= 0 || (hornNum == 0 && recommendNum == 0) {
		panic("param error")
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := UserMetaCollection().UpdateOne(ctx, bson.M{"user_id": userID},
		update{
			Inc: bson.M{
				"noble_horn_num": hornNum,
				"recommend_num":  recommendNum,
			},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

// IncreaseHornNum 喇叭数量增加，返回操作之后的喇叭信息（不论是否成功）
func IncreaseHornNum(userID int64, hornType int, num int) (bool, GeneralStatus, error) {
	if num == 0 {
		return false, GeneralStatus{}, nil
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := UserMetaCollection()
	proj := bson.M{"noble_horn_num": 1, "black_card_horn_num": 1}
	isBlackCardType := hornType == HornTypeBlackCard
	var before GeneralStatus
	err := col.FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(proj)).Decode(&before)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return false, before, err
	}
	filter := bson.M{"user_id": userID}
	if num < 0 {
		// 如果已有的喇叭数量小于减少的数量，则返回错误
		beforeNum := before.NobleHornNum
		if isBlackCardType {
			beforeNum = before.BlackCardHornNum
		}
		if beforeNum+int64(num) < 0 {
			before.checkHornNum()
			return false, before, nil
		}
		// 保证减少后的喇叭数量大于等于 0
		if isBlackCardType {
			filter["black_card_horn_num"] = bson.M{"$gte": -num}
		} else {
			filter["noble_horn_num"] = bson.M{"$gte": -num}
		}
	}
	var inc bson.M
	if isBlackCardType {
		inc = bson.M{"black_card_horn_num": int64(num)}
	} else {
		inc = bson.M{"noble_horn_num": int64(num)}
	}
	var after GeneralStatus
	err = col.FindOneAndUpdate(ctx, filter, update{Inc: inc},
		options.FindOneAndUpdate().SetProjection(proj).SetReturnDocument(options.After)).
		Decode(&after)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		before.checkHornNum()
		return false, before, err
	}
	after.checkHornNum()
	return true, after, nil
}

// DecreaseHornNum 喇叭数量减少，返回操作之后的喇叭信息（不论是否成功）
func DecreaseHornNum(userID int64, hornType int, num uint) (bool, GeneralStatus, error) {
	if num == 0 {
		return false, GeneralStatus{}, nil
	}

	return IncreaseHornNum(userID, hornType, -int(num))
}

// DecreaseRecommendNum 神话推荐数量减少，返回操作之后的剩余推荐数目（不论是否成功）
func DecreaseRecommendNum(userID int64, num int) (bool, int, error) {
	if num <= 0 {
		panic("num must greater than 0")
	}
	return IncreaseRecommendNum(userID, -num)
}

// IncreaseRecommendNum 神话推荐数量增加，返回操作之后的剩余推荐数目（不论是否成功）
func IncreaseRecommendNum(userID int64, num int) (bool, int, error) {
	if num == 0 {
		panic("num cannot be 0")
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := UserMetaCollection()
	filter := bson.M{"user_id": userID}
	proj := bson.M{"recommend_num": 1}
	var before GeneralStatus
	err := col.FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(proj)).Decode(&before)
	if err != nil {
		// NOTICE: 添加推荐次数可能会失败
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return false, 0, err
	}
	// 没有推荐字段的也可以加次数
	if before.RecommendNum == nil {
		before.RecommendNum = new(int)
	}
	// 判断推荐次数减少情况
	if num < 0 {
		if *before.RecommendNum+num < 0 {
			return false, *before.RecommendNum, nil
		}
		filter["recommend_num"] = bson.M{"$gte": -num}
	}
	inc := bson.M{"recommend_num": num}
	var after GeneralStatus
	err = col.FindOneAndUpdate(ctx, filter, update{Inc: inc},
		options.FindOneAndUpdate().SetProjection(proj).SetReturnDocument(options.After)).
		Decode(&after)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return false, *before.RecommendNum, err
	}
	return true, *after.RecommendNum, nil
}

// BanUser 封禁用户，用户在 user_meta 不存在时，插入新数据
func BanUser(userID, startTime, duration int64, durationType int) error {
	var ban *Ban
	switch durationType {
	case TypeBanForever:
		ban = &Ban{
			Type:      TypeBanForever,
			StartTime: startTime,
		}
	case TypeBanDuration:
		expireTime := startTime + duration
		ban = &Ban{
			StartTime:  startTime,
			Type:       TypeBanDuration,
			ExpireTime: &expireTime,
		}
	default:
		panic(fmt.Sprintf("unsupported type: %d", durationType))
	}
	return GeneralSetOne(bson.M{"user_id": userID}, bson.M{"user_id": userID, "ban": ban})
}

// Unban 解封
func Unban(userID int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	return UserMetaCollection().FindOneAndUpdate(ctx, bson.M{"user_id": userID},
		bson.M{"$unset": bson.M{"ban": ""}}).Err()
}

// FindBanned 获取用户封禁信息, 未被封禁时返回 nil
// cleanup 定时任务发现用户封禁时间到期后会移除 ban 字段
func FindBanned(userID int64) (*Ban, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var gs GeneralStatus
	filter := bson.M{
		"user_id": userID,
	}
	err := UserMetaCollection().FindOne(ctx, filter).Decode(&gs)
	if err != nil {
		if mongo.ErrNoDocuments == err {
			err = nil
		}
		return nil, err
	}
	// 永封和按时间封禁未过期时返回封禁信息
	if gs.Ban == nil || gs.Ban.Type == TypeBanForever || goutil.TimeNow().Unix() < *gs.Ban.ExpireTime {
		return gs.Ban, nil
	}
	// 发现已经过期则移除封禁状态
	if _, err = UserMetaCollection().UpdateOne(ctx, bson.M{"user_id": userID, "ban.expire_time": gs.Ban.ExpireTime},
		bson.M{"$unset": bson.M{"ban": ""}}); err != nil {
		logger.Error(err)
		// PASS
	}
	return nil, nil
}

// IsBanned 判断用户是否被封禁
func IsBanned(userID int64) (bool, error) {
	ban, err := FindBanned(userID)
	if err != nil {
		return false, err
	}
	return ban != nil, nil
}

// FindNotBackPackNotifiedUserIDs 筛除今日背包礼物过期已通知过的用户，返回今日未通知过的用户
func FindNotBackPackNotifiedUserIDs(userIDs []int64, when time.Time) ([]int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := UserMetaCollection().Find(ctx,
		bson.M{
			"user_id":                bson.M{"$in": userIDs},
			"backpack_notified_time": bson.M{"$not": bson.M{"$gte": goutil.BeginningOfDay(when).Unix()}},
		},
	)
	if err != nil {
		return nil, err
	}
	var res []GeneralStatus
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	notifyUserIDs := make([]int64, 0, len(res))
	for _, gs := range res {
		notifyUserIDs = append(notifyUserIDs, gs.UserID)
	}
	return notifyUserIDs, nil
}

// SaveUsersBackPackNotifiedTime 记录用户背包礼物过期通知时间
func SaveUsersBackPackNotifiedTime(userIDs []int64, when time.Time) error {
	updates := make([]mongo.WriteModel, 0, len(userIDs))
	for i := range userIDs {
		updateOne := mongo.NewUpdateOneModel().
			SetFilter(bson.M{"user_id": userIDs[i]}).
			SetUpdate(bson.M{
				"$set": bson.M{"backpack_notified_time": when.Unix()},
			}).SetUpsert(true)
		updates = append(updates, updateOne)
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := UserMetaCollection().BulkWrite(ctx, updates)
	return err
}
