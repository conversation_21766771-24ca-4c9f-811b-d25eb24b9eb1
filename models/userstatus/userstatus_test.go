package userstatus

import (
	"testing"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	testUA     string
	testRoomID = int64(22489473)
	testRoom   struct {
		ID        primitive.ObjectID `bson:"_id"`
		RoomID    int64              `bson:"room_id"`
		CreatorID int64              `bson:"creator_id"`
	} // WORKAROUND: 防止循环引用
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	testUA = tutil.PkgPath()

	findTestRoom()
	m.Run()
}

func findTestRoom() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	err := service.MongoDB.Collection("rooms").
		FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&testRoom)
	if err != nil {
		logger.Fatal(err)
	}
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	gsTags := []string{"user_id", "invisible", "noble_horn_num",
		"recommend_num", "ban", "total_spend", "backpack_notified_time"}
	kc.Check(GeneralStatus{}, gsTags...)
	kc.Check(ActivityStatus{}, "event_166")

	kc = tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(gsProjection(), "user_id", "invisible", "noble_horn_num",
		"recommend_num", "ban", "total_spend")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(GeneralStatus{}, "invisible", "horn_num", "recommend_num")
}
