package userstatus

import (
	"math"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testBanForeverUserID = 20201104

func TestGeneralStatusCheckHornNum(t *testing.T) {
	assert := assert.New(t)
	s := GeneralStatus{
		NobleHornNum:     -10,
		BlackCardHornNum: -10,
		HornNum:          10,
	}
	s.checkHornNum()
	assert.Zero(s.<PERSON>orn<PERSON>um + s.BlackCardHornNum + s.<PERSON>)
}

func TestUserGeneralStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	key := keys.KeyNobleUserVips1.Format(userID)
	err := service.Redis.Set(key, "null", 30*time.Second).Err()
	require.NoError(err)
	c := goutil.SmartUserContext{UID: userID}
	settings, _, err := UserGeneral(userID, c)
	require.NoError(err)
	assert.Equal(userID, settings.UserID)

	// 查找不存在的
	var vipInfo vip.Info
	vipInfo.Privilege.Set(vip.PrivilegeInvisible)
	vipInfo.Privilege.Set(vip.PrivilegeBubble)
	vipInfo.Privilege.Set(vip.PrivilegeRecommend)
	uv := &vip.UserVip{ExpireTime: goutil.TimeNow().Unix() + 1000, Info: &vipInfo}
	settings, err = findGeneralSettings(-100, uv)
	require.NoError(err)
	assert.Equal(GeneralStatus{Invisible: new(bool), RecommendNum: new(int)}, settings)
	// 正常查询，有贵族特权
	userID = int64(-12)
	require.NoError(GeneralSetOne(bson.M{"user_id": userID}, bson.M{
		"user_id": userID, "invisible": true, "noble_horn_num": 100, "recommend_num": 4,
	}))
	settings, err = findGeneralSettings(userID, uv)
	require.NoError(err)
	expected := GeneralStatus{
		UserID:       userID,
		Invisible:    new(bool),
		NobleHornNum: 100,
		HornNum:      100,
		RecommendNum: new(int),
	}
	*expected.RecommendNum = 4
	*expected.Invisible = true
	assert.Equal(expected, settings)
	// 正常查询，没有贵族特权
	vipInfo.Privilege = 0
	settings, err = findGeneralSettings(userID, uv)
	require.NoError(err)
	expected.Invisible = nil
	expected.RecommendNum = nil
	assert.Equal(expected, settings)
}

func TestSetInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := UserMetaCollection()
	testUserID := int64(1912242013)
	filter := bson.M{"user_id": testUserID}
	_, err := collection.DeleteMany(ctx, filter)
	require.NoError(err)
	assert.NoError(SetInvisible(testUserID, true))
	var settings GeneralStatus
	require.NoError(UserMetaCollection().FindOne(ctx, filter).Decode(&settings))
	assert.True(settings.Invisible != nil && *settings.Invisible, settings.Invisible)
}

func TestInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := UserMetaCollection()
	testUserID := int64(1912242020)
	filter := bson.M{"user_id": testUserID}
	_, err := collection.UpdateOne(ctx, filter, bson.M{"$unset": bson.M{"invisible": 1}})
	require.NoError(err)

	assert.False(Invisible(testUserID))
	require.NoError(SetInvisible(testUserID, true))
	assert.True(Invisible(testUserID))
	require.NoError(SetInvisible(testUserID, false))
	assert.False(Invisible(testUserID))
}

func TestHornNum(t *testing.T) {
	assert := assert.New(t)

	assert.NotZero(HornNum(-12))
}

func TestIncreaseHornRecommendNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": bson.M{
		"$in": []int64{
			3456839,
			3456840,
		},
	}}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := UserMetaCollection().DeleteMany(ctx, filter)
	require.NoError(err)

	// 测试 panic
	assert.PanicsWithValue("param error",
		func() {
			_ = IncreaseHornRecommendNum(9074509, 0, 0)
		},
	)
	assert.PanicsWithValue("param error",
		func() {
			_ = IncreaseHornRecommendNum(0, 1, 122)
		},
	)

	// 测试没有用户数据
	err = IncreaseHornRecommendNum(3456839, 1, 1)
	require.NoError(err)
	uv := &vip.UserVip{
		ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		Info: &vip.Info{
			Privilege: math.MaxInt16,
		},
	}
	generalStatus, err := findGeneralSettings(3456839, uv)
	require.NoError(err)
	assert.Equal(int64(1), generalStatus.NobleHornNum)
	require.NotNil(generalStatus.RecommendNum)
	assert.Equal(1, *generalStatus.RecommendNum)

	// 测试没有推荐字段的也可以加次数
	err = GeneralSetOne(
		bson.M{"user_id": 3456840},
		bson.M{"noble_horn_num": 5},
	)
	require.NoError(err)
	err = IncreaseHornRecommendNum(3456840, 1, 2)
	require.NoError(err)
	generalStatus, err = findGeneralSettings(3456840, uv)
	require.NoError(err)
	assert.Equal(int64(6), generalStatus.NobleHornNum)
	require.NotNil(generalStatus.RecommendNum)
	assert.Equal(2, *generalStatus.RecommendNum)

	// 测试只加神话推荐次数
	err = IncreaseHornRecommendNum(3456840, 0, 2)
	require.NoError(err)
	generalStatus, err = findGeneralSettings(3456840, uv)
	require.NoError(err)
	assert.Equal(int64(6), generalStatus.NobleHornNum)
	require.NotNil(generalStatus.RecommendNum)
	assert.Equal(4, *generalStatus.RecommendNum)
}

func TestIncreaseHornNum(t *testing.T) {
	const testUserID = 123456

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 清理测试数据
	_, err := UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(t, err)
	defer UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})

	// 插入测试数据
	_, err = UserMetaCollection().InsertOne(ctx, bson.M{"user_id": testUserID, "noble_horn_num": 0, "black_card_horn_num": 0})
	require.NoError(t, err)

	t.Run("增加贵族喇叭数量", func(t *testing.T) {
		ok, numInfo, err := IncreaseHornNum(testUserID, HornTypeNoble, 5)
		require.NoError(t, err)
		assert.True(t, ok)
		assert.Equal(t, int64(5), numInfo.NobleHornNum)
		assert.Equal(t, int64(0), numInfo.BlackCardHornNum)
		assert.Equal(t, int64(5), numInfo.HornNum)
	})

	t.Run("增加黑卡喇叭数量", func(t *testing.T) {
		ok, numInfo, err := IncreaseHornNum(testUserID, HornTypeBlackCard, 3)
		require.NoError(t, err)
		assert.True(t, ok)
		assert.Equal(t, int64(5), numInfo.NobleHornNum)
		assert.Equal(t, int64(3), numInfo.BlackCardHornNum)
		assert.Equal(t, int64(8), numInfo.HornNum)
	})

	t.Run("减少贵族喇叭数量", func(t *testing.T) {
		ok, numInfo, err := IncreaseHornNum(testUserID, HornTypeNoble, -2)
		require.NoError(t, err)
		assert.True(t, ok)
		assert.Equal(t, int64(3), numInfo.NobleHornNum)
		assert.Equal(t, int64(3), numInfo.BlackCardHornNum)
		assert.Equal(t, int64(6), numInfo.HornNum)
	})

	t.Run("减少喇叭数量超过现有数量", func(t *testing.T) {
		ok, numInfo, err := IncreaseHornNum(testUserID, HornTypeNoble, -10)
		require.NoError(t, err)
		assert.False(t, ok)
		assert.Equal(t, int64(3), numInfo.NobleHornNum)
		assert.Equal(t, int64(3), numInfo.BlackCardHornNum)
		assert.Equal(t, int64(6), numInfo.HornNum)
	})

	t.Run("增加喇叭数量为 0", func(t *testing.T) {
		ok, numInfo, err := IncreaseHornNum(testUserID, HornTypeNoble, 0)
		require.NoError(t, err)
		assert.False(t, ok)
		assert.Zero(t, numInfo.UserID)
		assert.Zero(t, numInfo.NobleHornNum)
		assert.Zero(t, numInfo.BlackCardHornNum)
		assert.Zero(t, numInfo.HornNum)
	})

	t.Run("不存在的用户", func(t *testing.T) {
		ok, numInfo, err := IncreaseHornNum(99999999, HornTypeNoble, 1)
		require.NoError(t, err)
		assert.False(t, ok)
		assert.Zero(t, numInfo.UserID)
		assert.Zero(t, numInfo.NobleHornNum)
		assert.Zero(t, numInfo.BlackCardHornNum)
		assert.Zero(t, numInfo.HornNum)
	})
}

func TestDecreaseHornNum(t *testing.T) {
	const testUserID = 654321

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 清理测试数据
	_, err := UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(t, err)
	defer UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})

	// 插入测试数据
	_, err = UserMetaCollection().InsertOne(ctx, bson.M{"user_id": testUserID, "noble_horn_num": 10, "black_card_horn_num": 5})
	require.NoError(t, err)

	t.Run("正常减少贵族喇叭", func(t *testing.T) {
		ok, numInfo, err := DecreaseHornNum(testUserID, HornTypeNoble, 1)
		require.NoError(t, err)
		assert.True(t, ok)
		assert.Equal(t, int64(9), numInfo.NobleHornNum)
		assert.Equal(t, int64(5), numInfo.BlackCardHornNum)
		assert.Equal(t, int64(14), numInfo.HornNum)
	})

	t.Run("正常减少黑卡喇叭", func(t *testing.T) {
		ok, numInfo, err := DecreaseHornNum(testUserID, HornTypeBlackCard, 2)
		require.NoError(t, err)
		assert.True(t, ok)
		assert.Equal(t, int64(9), numInfo.NobleHornNum)
		assert.Equal(t, int64(3), numInfo.BlackCardHornNum)
		assert.Equal(t, int64(12), numInfo.HornNum)
	})

	t.Run("减少喇叭数量为 0", func(t *testing.T) {
		ok, numInfo, err := DecreaseHornNum(testUserID, HornTypeNoble, 0)
		require.NoError(t, err)
		assert.False(t, ok)
		assert.Zero(t, numInfo.UserID)
		assert.Zero(t, numInfo.NobleHornNum)
		assert.Zero(t, numInfo.BlackCardHornNum)
		assert.Zero(t, numInfo.HornNum)
	})
}

func TestDecreaseRecommendNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := GeneralSetOne(bson.M{"user_id": 12}, bson.M{"recommend_num": 1000})
	require.NoError(err)

	// 测试 panic
	assert.PanicsWithValue("num must greater than 0",
		func() {
			_, _, _ = DecreaseRecommendNum(12, 0)
		})

	// 正常情况
	ok, v, err := DecreaseRecommendNum(12, 1)
	require.NoError(err)
	assert.True(ok)
	assert.Equal(999, v)
	// 测试不能为负数的情况
	ok, v, err = DecreaseRecommendNum(12, 10001)
	require.NoError(err)
	assert.False(ok)
	assert.Equal(999, v)
}

func TestIncreaseRecommendNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := GeneralSetOne(bson.M{"user_id": 12}, bson.M{"recommend_num": 1000})
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// recommend_num 不存在的情况
	testNilUserID := int64(1234567132)
	_, err = UserMetaCollection().UpdateOne(ctx,
		bson.M{"user_id": testNilUserID}, bson.M{"$unset": bson.
			M{"recommend_num": 0}, "$set": bson.M{"user_id": testNilUserID}},
		options.Update().SetUpsert(true))
	require.NoError(err)

	// 测试 panic
	assert.PanicsWithValue("num cannot be 0",
		func() {
			_, _, _ = IncreaseRecommendNum(12, 0)
		})

	// 正常情况
	ok, v, err := IncreaseRecommendNum(12, 1)
	require.NoError(err)
	assert.True(ok)
	assert.Equal(1001, v)
	ok, v, err = IncreaseRecommendNum(12, -1)
	require.NoError(err)
	assert.True(ok)
	assert.Equal(1000, v)
	// 测试不能为负数的情况
	ok, v, err = IncreaseRecommendNum(12, -10001)
	require.NoError(err)
	assert.False(ok)
	assert.Equal(1000, v)
	// 测试 recommend_num 不存在的情况
	ok, v, err = IncreaseRecommendNum(testNilUserID, 1)
	require.NoError(err)
	assert.True(ok)
	assert.Equal(1, v)
	ok, v, err = IncreaseRecommendNum(3456835, -1)
	require.NoError(err)
	assert.False(ok)
	assert.Zero(v)
}

func TestBanAndUnban(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(-13)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	// 按时间封禁
	now := goutil.TimeNow()
	startTime := now.Unix()
	require.NoError(BanUser(testUserID, startTime, util.SecondOneMinute, TypeBanDuration))
	b, err := FindBanned(testUserID)
	require.NoError(err)
	require.NotNil(b)
	assert.Equal(TypeBanDuration, b.Type)
	assert.Equal(now.Add(time.Minute).Unix(), *b.ExpireTime)

	// 永封
	require.NoError(BanUser(testUserID, startTime, 0, TypeBanForever))
	b, err = FindBanned(testUserID)
	require.NoError(err)
	require.NotNil(b)
	assert.Equal(startTime, b.StartTime)
	assert.Equal(TypeBanForever, b.Type)
	assert.Nil(b.ExpireTime)

	// 解封
	require.NoError(Unban(testUserID))
	b, err = FindBanned(testUserID)
	require.NoError(err)
	assert.Nil(b)
}

func TestFindBanned(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(GeneralSetOne(bson.M{"user_id": testBanForeverUserID},
		bson.M{"ban": Ban{
			Type:      TypeBanForever,
			StartTime: goutil.TimeNow().Unix()}},
	))

	b, err := FindBanned(testBanForeverUserID)
	require.NoError(err)
	require.NotNil(b)
	assert.Equal(TypeBanForever, b.Type)
}

func TestIsBanned(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	banned, err := IsBanned(testBanForeverUserID)
	require.NoError(err)
	assert.True(banned)
	banned, err = IsBanned(-12)
	require.NoError(err)
	assert.False(banned)
}

func TestFindNotBackPackNotifiedUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(-123456)
		testTime   = goutil.TimeNow()
	)

	require.NoError(SaveUsersBackPackNotifiedTime([]int64{testUserID}, testTime.Add(-24*time.Hour)))
	userIDs, err := FindNotBackPackNotifiedUserIDs([]int64{testUserID}, testTime)
	require.NoError(err)
	require.NotEmpty(userIDs)
	assert.Equal(testUserID, userIDs[0])

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var gs GeneralStatus
	err = UserMetaCollection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&gs)
	require.NoError(err)
	userIDs, err = FindNotBackPackNotifiedUserIDs([]int64{testUserID}, testTime.Add(-24*time.Hour))
	require.NoError(err)
	require.Empty(userIDs)
}

func TestSaveUsersBackPackNotifiedTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(-123456)
		testTime   = goutil.TimeNow()
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	require.NoError(SaveUsersBackPackNotifiedTime([]int64{testUserID}, testTime))

	var gs GeneralStatus
	err = UserMetaCollection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&gs)
	require.NoError(err)
	assert.Equal(testTime.Unix(), gs.BackpackNotifiedTime)

	testTime = testTime.Add(time.Hour)
	require.NoError(SaveUsersBackPackNotifiedTime([]int64{testUserID}, testTime))
	err = UserMetaCollection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&gs)
	require.NoError(err)
	assert.Equal(testTime.Unix(), gs.BackpackNotifiedTime)
}
