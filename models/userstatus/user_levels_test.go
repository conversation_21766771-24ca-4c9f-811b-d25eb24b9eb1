package userstatus

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAddOnlineTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := NewAddContributionParams(12, 100, "aaa", FromNormal, nil)
	s1, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := userMetaCollection()
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": bson.M{
		"t_time_online": time.Unix(0, 0), "t_time_live": time.Unix(0, 0)}})
	require.NoError(err)

	require.NoError(param.AddOnlineTime(100))
	s2, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Zero(s2.Contribution - s1.Contribution)
	require.NoError(param.AddOnlineTime(100 + questOnline))
	s2, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(200), s2.Contribution-s1.Contribution)
}

func TestAddPurchaseContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := NewAddContributionParams(12, 100, "aaa", FromNormal, nil)
	s1, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := userMetaCollection()
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": bson.M{
		"t_time_purchase": time.Unix(0, 0)}})
	require.NoError(err)
	require.NoError(param.AddPurchaseContribution(10))
	s2, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(510), s2.Contribution-s1.Contribution)
	require.NoError(param.AddPurchaseContribution(10))
	s2, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(520), s2.Contribution-s1.Contribution)
}

func TestAddContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := NewAddContributionParams(12, 100, "aaa", FromNormal, nil)
	s1, err := liveuser.FindOneSimple(bson.M{"user_id": 12}, nil)
	require.NoError(err)
	require.NoError(param.AddContribution(100))
	s2, err := liveuser.FindOneSimple(bson.M{"user_id": 12}, nil)
	require.NoError(err)
	assert.Equal(int64(100), s2.Contribution-s1.Contribution)

	// 给一个不存在的用户加经验值
	param = NewAddContributionParams(-123456, 100, "aaa", FromNormal, nil)
	assert.NoError(param.AddContribution(100))
	s1, err = liveuser.FindOneSimple(bson.M{"user_id": -123456}, nil)
	assert.NoError(err)
	assert.Nil(s1)
}

func TestSyncContribution(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return true, nil
	})
	defer cancel()

	err := syncContribution(100, &liveuser.Simple{UID: 12}, nil)
	assert.NoError(err)

	err = syncContribution(100, &liveuser.Simple{UID: 12}, &vip.UserVip{
		Info: &vip.Info{ExpAcceleration: 5},
	})
	assert.NoError(err)
}

func TestNewUserLevelInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 无加速
	userID := int64(12)
	con := int64(100)
	expectedLevel := 2
	startCon := usercommon.LevelStart[expectedLevel-1]
	upCon := usercommon.LevelStart[expectedLevel]
	userLevelInfo, err := NewUserLevelInfo(&liveuser.Simple{UID: userID, Contribution: con}, nil)
	require.NoError(err)
	require.NotNil(userLevelInfo)
	assert.EqualValues(con-startCon, userLevelInfo.Exp)
	require.EqualValues(expectedLevel, userLevelInfo.Level)
	assert.EqualValues(upCon-startCon, userLevelInfo.LevelUpExp)
	assert.EqualValues((upCon-con)/10, userLevelInfo.LevelUpCoin)
	assert.Empty(userLevelInfo.LevelIconURL)
	assert.Nil(userLevelInfo.LevelSpeedUp)

	// 贵族加速
	con = int64(44800)
	expectedLevel = 16
	startCon = usercommon.LevelStart[expectedLevel-1]
	upCon = usercommon.LevelStart[expectedLevel]
	userLevelInfo, err = NewUserLevelInfo(&liveuser.Simple{UID: userID, Contribution: con}, &vip.UserVip{
		ExpireTime: goutil.TimeNow().Unix() + 1000,
		Info: &vip.Info{
			ExpAcceleration: 5,
		}})
	require.NoError(err)
	require.NotNil(userLevelInfo)
	assert.EqualValues(con-startCon, userLevelInfo.Exp)
	require.EqualValues(expectedLevel, userLevelInfo.Level)
	assert.EqualValues(upCon-startCon, userLevelInfo.LevelUpExp)
	assert.EqualValues(496, userLevelInfo.LevelUpCoin)
	assert.Empty(userLevelInfo.LevelIconURL)
	require.NotNil(userLevelInfo.LevelSpeedUp)
	assert.EqualValues(1.05, userLevelInfo.LevelSpeedUp.Factor)

	// 满级
	expectedLevel = len(usercommon.LevelStart)
	con = usercommon.LevelStart[expectedLevel-1] + 100
	startCon = usercommon.LevelStart[expectedLevel-1]
	userLevelInfo, err = NewUserLevelInfo(&liveuser.Simple{UID: userID, Contribution: con}, &vip.UserVip{
		ExpireTime: goutil.TimeNow().Unix() + 1000,
		Info: &vip.Info{
			ExpAcceleration: 25,
		}})
	require.NoError(err)
	require.NotNil(userLevelInfo)
	assert.EqualValues(con-startCon, userLevelInfo.Exp)
	require.EqualValues(expectedLevel, userLevelInfo.Level)
	assert.Zero(userLevelInfo.LevelUpExp)
	assert.Zero(userLevelInfo.LevelUpCoin)
	assert.NotEmpty(userLevelInfo.LevelIconURL)
}

func TestUserLevel_CalcCoin(t *testing.T) {
	assert := assert.New(t)

	u := &UserLevelInfo{
		Exp:        44800,
		LevelUpExp: 50000,
		LevelSpeedUp: &speedUp{
			PrivilegeTitle: "新秀",
			Factor:         1.05,
		},
	}
	u.calcCoin()
	assert.EqualValues(496, u.LevelUpCoin)

	u.Exp = 99
	u.LevelUpExp = 100
	u.LevelSpeedUp = nil
	u.calcCoin()
	assert.EqualValues(1, u.LevelUpCoin)
}

func TestAfterUserLevelUp(t *testing.T) {
	assert := assert.New(t)

	assert.NotPanics(func() {
		ap := AfterUserLevelUpParam{
			User:            &liveuser.Simple{},
			RoomID:          1,
			CreatorUsername: "test_name",
			BeforeLevel:     5,
			AfterLevel:      10,
		}
		ap.AfterUserLevelUp()
	})

	assert.NotPanics(func() {
		ap := AfterUserLevelUpParam{
			User:            &liveuser.Simple{},
			RoomID:          1,
			CreatorUsername: "test_name",
			BeforeLevel:     5,
			AfterLevel:      200,
		}
		ap.AfterUserLevelUp()
	})
}

func TestLevel45BirthdayPrivilege(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 1, 26, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := appearance.Collection().UpdateMany(ctx,
		bson.M{
			"id": bson.M{"$in": appearance.UserLevelGte45RewardAppearanceIDs()},
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "test_icon",
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	testIDs := []int64{1919, 1515, 1818}
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testIDs}})
	require.NoError(err)

	// 不是生日
	sent, err := level45BirthdayPrivilege(testIDs[2], 44, 45)
	require.NoError(err)
	assert.False(sent)
	now := goutil.TimeNow()
	assert.False(livebirthdayprivrecord.IsRewarded(testIDs[2], now.Year()))

	// 今年已经领过
	require.True(livebirthdayprivrecord.IsRewarded(testIDs[0], now.Year()))
	sent, err = level45BirthdayPrivilege(testIDs[0], 44, 45)
	require.NoError(err)
	assert.False(sent)
	var ua userappearance.UserAppearance
	require.Error(userappearance.Collection().FindOne(ctx, bson.M{"user_id": testIDs[0]}).Decode(&ua))

	err = livebirthdayprivrecord.LiveBirthdayPrivRecord{}.DB().Delete("", "user_id = ?", testIDs[1]).Error
	require.NoError(err)
	require.False(livebirthdayprivrecord.IsRewarded(testIDs[1], now.Year()))

	// 未达到等级
	sent, err = level45BirthdayPrivilege(testIDs[1], 43, 44)
	require.NoError(err)
	assert.False(sent)
	assert.False(livebirthdayprivrecord.IsRewarded(testIDs[1], now.Year()))

	// 成功发放
	sent, err = level45BirthdayPrivilege(testIDs[1], 44, 45)
	require.NoError(err)
	assert.True(sent)
	assert.True(livebirthdayprivrecord.IsRewarded(testIDs[1], now.Year()))
	require.NoError(userappearance.Collection().FindOne(ctx, bson.M{"user_id": testIDs[1]}).Decode(&ua))
	require.NotNil(ua)
	assert.Equal(now.Day()+1, time.Unix(*ua.ExpireTime, 0).Day())
}

func TestLevel160Privilege(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := appearance.Collection().UpdateMany(ctx,
		bson.M{
			"id": bson.M{"$in": appearance.UserLevelGte160RewardAppearanceIDs()},
		},
		bson.M{"$set": bson.M{
			"name": "160 级特权外观",
			"icon": "test_icon",
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	testID := int64(1919)
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testID})
	require.NoError(err)

	// 未达到等级
	sent, err := level160Privilege(testID, 158, 159)
	require.NoError(err)
	assert.False(sent)

	// 成功发放
	sent, err = level160Privilege(testID, 159, 160)
	require.NoError(err)
	assert.True(sent)

	ua, err := userappearance.FindOne(bson.M{"user_id": testID}, nil)
	require.NoError(err)
	assert.NotNil(ua)
}

func TestFindBubbleIDByLevel(t *testing.T) {
	assert := assert.New(t)

	f := func(before, after int, value *notifyStyle) {
		for i := before; i <= after; i++ {
			value.level = i
			assert.Equal(value, findLevelBubbleConfig(i))
		}
	}

	f(55, 69, &notifyStyle{
		messageFile:        "oss://live/userlevels/messagebars/55_0_122_0_122.png",
		textColor:          "#E7C665",
		highlightTextColor: "#FFF5CD",
	})
	f(120, 124, &notifyStyle{
		levelBubbleID:      bubbleIDLevel120,
		messageFile:        "oss://live/userlevels/messagebars/120_0_122_0_122.png",
		textColor:          "#D1C3FF",
		highlightTextColor: "#FEFFC3",
	})
	f(125, 129, &notifyStyle{
		levelBubbleID:      bubbleIDLevel125,
		messageFile:        "oss://live/userlevels/messagebars/125_0_122_0_122.png",
		textColor:          "#CEB1FF",
		highlightTextColor: "#B6DEFF",
	})
	f(130, 134, &notifyStyle{
		levelBubbleID:      bubbleIDLevel130,
		messageFile:        "oss://live/userlevels/messagebars/130_0_122_0_122.png",
		textColor:          "#F08EFF",
		highlightTextColor: "#FFC197",
	})
	f(135, 139, &notifyStyle{
		levelBubbleID:      bubbleIDLevel135,
		messageFile:        "oss://live/userlevels/messagebars/135_0_122_0_122.png",
		textColor:          "#FF88E4",
		highlightTextColor: "#FFD5B9",
	})
	f(140, 144, &notifyStyle{
		levelBubbleID:      bubbleIDLevel140,
		messageFile:        "oss://live/userlevels/messagebars/140_0_122_0_122.png",
		textColor:          "#FF8FA4",
		highlightTextColor: "#FBFFC9",
	})
	f(145, 149, &notifyStyle{
		levelBubbleID:      bubbleIDLevel145,
		messageFile:        "oss://live/userlevels/messagebars/145_0_122_0_122.png",
		textColor:          "#FF9E48",
		highlightTextColor: "#FFCD48",
	})
	f(150, 199, &notifyStyle{
		levelBubbleID:      bubbleIDLevel150,
		messageFile:        "oss://live/userlevels/messagebars/150_0_122_0_122.png",
		textColor:          "#FFE072",
		highlightTextColor: "#FF8F52",
	})
	f(201, 209, &notifyStyle{
		levelBubbleID:      bubbleIDLevel201,
		messageFile:        "oss://live/userlevels/messagebars/201_0_122_0_122.png",
		textColor:          "#FFF0CD",
		highlightTextColor: "#FFF0CD",
	})
	f(210, 210, &notifyStyle{
		levelBubbleID:      bubbleIDLevelGte200Interval10,
		messageFile:        "oss://live/userlevels/messagebars/new200_0_122_0_122.png",
		textColor:          "#AA8534",
		highlightTextColor: "#834A0C",
	})
}

func TestLevelUpSystemNotify(t *testing.T) {
	assert := assert.New(t)

	equal := func(res *pushservice.SystemMsg, title, content string) {
		assert.Equal(&pushservice.SystemMsg{
			UserID:  1234,
			Title:   title,
			Content: content,
		}, res)
	}

	basicTitle := "直播用户等级升级！新特权解锁！"
	equal(levelUpSystemNotify(1234, 0, 45), basicTitle, `尊贵的用户您好！恭喜您的直播等级已达到 45 级，即刻起为您解锁该等级权益：您可在生日当天获得小寿星专属头像框与称号~ 生日信息可通过前往“我的” - “设置” - “个人资料” - “出生年月”处设置和修改。（每年您仅可解锁该外观权益 1 次哦，请谨慎修改生日信息！如有更多问题可联系客服咨询）<a href="https://fm.uat.missevan.com/user/level" target="_blank">了解更多等级特权请点击此处~</a>`)
	equal(levelUpSystemNotify(1234, 0, 70), basicTitle, "恭喜您直播用户等级升级到 70 级，再接再厉哦 (•̀ᴗ•́)و ̑̑ \n您的新特权高级客服将在 QQ 平台为您服务，以下是您的专属高级客服 QQ：<a href=\"copy:800181712\">800181712</a>，请您添加喔~\n添加时请备注您的 M 号，么么哒 (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 90), basicTitle, "恭喜您直播用户等级升级到 90 级，再接再厉哦 (•̀ᴗ•́)و ̑̑ \n您的新特权 VIP 客服将在 QQ 平台为您服务，以下是您的专属 VIP 客服 QQ：<a href=\"copy:3008181774\">3008181774</a>，请您添加喔~\n添加时请备注您的 M 号，么么哒 (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 120), basicTitle, "当当当当~ 恭喜您直播用户等级升级到 120 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权个人定制进场特效正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href=\"copy:3008181774\">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 140), basicTitle, "当当当当~ 恭喜您直播用户等级升级到 140 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权个人定制气泡框正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href=\"copy:3008181774\">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 150), basicTitle, "当当当当~ 恭喜您直播用户等级升级到 150 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权荣耀特权礼包（包含定制动态头像框、气泡框、个人名片框及定制动效礼物）正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href=\"copy:3008181774\">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 155), basicTitle, "当当当当~ 恭喜您直播用户等级升级到 155 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权专属礼物赠送资格已成功解锁（礼物包内含礼物每个季度更新一次），您可以在专属礼物栏内进行赠送哦~ (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 160), basicTitle, "尊贵的用户您好！恭喜您的直播等级已达到 160 级，您已获得该等级专属系列外观使用权益，可前往“我的” - “直播中心” - “外观中心”处佩戴等级专属外观。如更有更多问题可联系客服咨询~ <a href=\"https://fm.uat.missevan.com/user/level\" target=\"_blank\">了解更多等级特权请点击此处~</a>")
	equal(levelUpSystemNotify(1234, 0, 165), basicTitle, "当当当当~ 恭喜您直播用户等级升级到 165 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权定制静态表情包正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href=\"copy:3008181774\">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 170), basicTitle, "当当当当~ 恭喜您直播用户等级升级到 170 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权定制静态表情包正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href=\"copy:3008181774\">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)")
	equal(levelUpSystemNotify(1234, 0, 185), basicTitle, `尊贵的用户您好！恭喜您的直播等级已达到 185 级，即刻起为您解锁星享馆使用权益。星享馆是仅为直播等级 ≥ 185 级的用户提供的尊享权益礼包，每两个月更新一次。您可以升级至 6.1.3 版本，前往任意直播间内点击右下角“···”按钮，或前往 PC 端直播间点击右侧“玩法”区域处打开星享馆。如有更多问题可联系客服咨询~ <a href="https://fm.uat.missevan.com/user/level" target="_blank">了解更多等级特权请点击此处~</a>`)
	// 不加的几次
	assert.Nil(levelUpSystemNotify(1234, 0, 44))
	assert.Nil(levelUpSystemNotify(1234, 45, 68))
	assert.Nil(levelUpSystemNotify(1234, 70, 88))
	assert.Nil(levelUpSystemNotify(1234, 90, 118))
	assert.Nil(levelUpSystemNotify(1234, 120, 139))
	assert.Nil(levelUpSystemNotify(1234, 140, 149))
	assert.Nil(levelUpSystemNotify(1234, 150, 154))
	assert.Nil(levelUpSystemNotify(1234, 155, 159))
	assert.Nil(levelUpSystemNotify(1234, 160, 164))
	assert.Nil(levelUpSystemNotify(1234, 165, 169))
	assert.Nil(levelUpSystemNotify(1234, 170, 184))
}

func TestLevelUpNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 不飘屏情况
	user := liveuser.Simple{UID: 3456835, Username: "姓名"}
	param := NewAddContributionParams(3456835, 22489473, "测试用", FromNormal, nil)
	f := func(elem *userapi.BroadcastElem) string {
		v, ok := elem.Payload.(*notifymessages.General)
		require.True(ok)
		return v.Message
	}

	ap := AfterUserLevelUpParam{
		User:            &user,
		RoomID:          param.roomID,
		CreatorUsername: param.creatorUsername,
		BeforeLevel:     0,
		AfterLevel:      2,
	}
	assert.Nil(ap.levelUpNotify())

	// 榜单隐身
	require.NoError(EnableRankInvisible(param.userID, param.roomID, primitive.NilObjectID))
	ap = AfterUserLevelUpParam{
		User:        &user,
		BeforeLevel: 130,
		AfterLevel:  131,
	}
	notifies := ap.levelUpNotify()
	require.Equal(1, len(notifies))
	assert.Equal("<font color=\"#F08EFF\">恭喜</font> <font color=\"#FFC197\">姓名</font> <font color=\"#F08EFF\">"+
		"升级到</font> <font color=\"#FFC197\">131</font> <font color=\"#F08EFF\">级~</font>", f(notifies[0]))

	// 榜单不隐身
	require.NoError(DisableRankInvisible(param.userID, []int64{param.roomID}))
	ap = AfterUserLevelUpParam{
		User:            &user,
		RoomID:          param.roomID,
		CreatorUsername: param.creatorUsername,
		BeforeLevel:     130,
		AfterLevel:      131,
	}
	notifies = ap.levelUpNotify()
	require.Equal(1, len(notifies))
	assert.Equal("<font color=\"#F08EFF\">恭喜</font> <font color=\"#FFC197\">姓名</font> <font color=\"#F08EFF\">"+
		"在</font> <font color=\"#FFC197\">测试用</font> <font color=\"#F08EFF\">直播间内升级到</font> <font color=\"#FFC197\">"+
		"131</font> <font color=\"#F08EFF\">级，快来围观吧~</font>", f(notifies[0]))

	// 用户隐身
	require.NoError(SetInvisible(param.userID, true))
	ap = AfterUserLevelUpParam{
		User:        &user,
		BeforeLevel: 130,
		AfterLevel:  132,
	}
	notifies = ap.levelUpNotify()
	require.Equal(1, len(notifies))
	assert.Equal("<font color=\"#F08EFF\">恭喜</font> <font color=\"#FFC197\">姓名</font> <font color=\"#F08EFF\">"+
		"升级到</font> <font color=\"#FFC197\">132</font> <font color=\"#F08EFF\">级~</font>", f(notifies[0]))

	// 用户不隐身
	require.NoError(SetInvisible(param.userID, false))
	ap = AfterUserLevelUpParam{
		User:            &user,
		RoomID:          param.roomID,
		CreatorUsername: param.creatorUsername,
		BeforeLevel:     130,
		AfterLevel:      132,
	}
	notifies = ap.levelUpNotify()
	require.Equal(1, len(notifies))
	assert.Equal("<font color=\"#F08EFF\">恭喜</font> <font color=\"#FFC197\">姓名</font> <font color=\"#F08EFF\">"+
		"在</font> <font color=\"#FFC197\">测试用</font> <font color=\"#F08EFF\">直播间内升级到</font> <font color=\"#FFC197\">"+
		"132</font> <font color=\"#F08EFF\">级，快来围观吧~</font>", f(notifies[0]))

	ap = AfterUserLevelUpParam{
		User:            &user,
		RoomID:          param.roomID,
		CreatorUsername: param.creatorUsername,
		BeforeLevel:     150,
		AfterLevel:      155,
	}
	notifies = ap.levelUpNotify()
	require.Equal(1, len(notifies))

	config.Conf.AB["enable_user_155_level_gift_notify"] = true
	defer delete(config.Conf.AB, "enable_user_155_level_gift_notify")
	ap = AfterUserLevelUpParam{
		User:            &user,
		RoomID:          param.roomID,
		CreatorUsername: param.creatorUsername,
		BeforeLevel:     150,
		AfterLevel:      155,
	}
	notifies = ap.levelUpNotify()
	require.Equal(2, len(notifies))

	ap = AfterUserLevelUpParam{
		User:            &user,
		RoomID:          param.roomID,
		CreatorUsername: param.creatorUsername,
		BeforeLevel:     155,
		AfterLevel:      156,
	}
	notifies = ap.levelUpNotify()
	require.Equal(1, len(notifies))
}

func TestNewLevelUpEffectMessage(t *testing.T) {
	assert := assert.New(t)

	b := &bubble.Simple{Type: "custom", BubbleID: 1}
	l := LevelUpEffect{
		Level:          120,
		IconURL:        "url1",
		EffectURL:      "url2",
		EffectDuration: 3000,
	}
	mb := MessageBar{
		ImageURL: "aaa",
		Message:  "bbb",
	}
	u := &liveuser.UserInfo{
		Simple: liveuser.Simple{},
	}
	g := newLevelUpEffectMessage(123, l, u, b, mb)
	assert.Equal("level_up", g.Event)
	assert.Equal(int64(123), g.RoomID)
	assert.Equal(b, g.Bubble)
}

func TestLevelUpEffect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := AfterUserLevelUpParam{
		User:   &liveuser.Simple{UID: 12},
		RoomID: 100,
	}

	var count int
	cancel := mrpc.SetMock("im://broadcast/many", func(input any) (any, error) {
		count++
		return nil, nil
	})
	defer cancel()
	require.NotPanics(func() {
		p.BeforeLevel = 200
		p.AfterLevel = 201
		p.levelUpEffect()
	})
	<-time.After(1100 * time.Millisecond)
	assert.Equal(1, count)
}

func TestQuestJoinRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := NewAddContributionParams(12, 100, "aaa", FromNormal, nil)
	s1, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := userMetaCollection()
	// 普通情况
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": bson.M{
		"t_status_view": 0, "t_time_view": time.Unix(0, 0)}})
	require.NoError(err)
	require.NoError(param.QuestJoinRoom()) // 经验加了100
	s2, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(100), s2.Contribution-s1.Contribution)
	s1 = s2
	// 不连续的情况
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": bson.M{
		"t_status_view": 6}, "$unset": bson.M{"t_time_view": ""}})
	require.NoError(err)
	require.NoError(param.QuestJoinRoom())
	s2, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(100), s2.Contribution-s1.Contribution)
	s1 = s2
	// 连续并且 7 天的情况
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": bson.M{
		"t_status_view": 6, "t_time_view": goutil.TimeNow().AddDate(0, 0, -1)}})
	require.NoError(err)
	require.NoError(param.QuestJoinRoom())
	s2, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(700), s2.Contribution-s1.Contribution)
	s1 = s2
	// 重复调用
	require.NoError(param.QuestJoinRoom())
	s2, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(s1.Contribution, s2.Contribution)
}

func TestAddWithFirstTimeBonus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	param := NewAddContributionParams(12, 100, "aaa", FromNormal, nil)
	s1, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := userMetaCollection()
	// 今日首次分享
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": bson.M{
		"t_status_share": ShareStatusPending, "t_time_share": time.Unix(0, 0)}})
	require.NoError(err)
	require.NoError(param.AddWithFirstTimeBonus(400))
	s2, err := liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(400), s2.Contribution-s1.Contribution)
	us1, _ := findUserShareInfo(param.userID)
	require.NotNil(us1)
	assert.Equal(ShareStatusShared, us1.TStatusShare)
	assert.Equal(now.Unix(), us1.TTimeShare.Unix())
	s1 = s2
	// 今日第二次分享
	require.NoError(param.AddWithFirstTimeBonus(400))
	s2, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	require.NoError(err)
	assert.Equal(int64(0), s2.Contribution-s1.Contribution)
	us2, _ := findUserShareInfo(param.userID)
	require.NotNil(us2)
	assert.Equal(us1.TTimeShare, us2.TTimeShare)
}

func TestAfterUserLevelUpParam_levelUpEffectNotifies(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mockMessageBubble := &bubble.Simple{}
	mockUserInfo := &liveuser.UserInfo{}

	t.Run("无效升级", func(t *testing.T) {
		ap := &AfterUserLevelUpParam{BeforeLevel: 100, AfterLevel: 99, RoomID: 123}
		notifies := ap.levelUpEffectNotifies(mockMessageBubble, mockUserInfo)
		require.Len(notifies, 0)
	})
	t.Run("单级升级 - 普通特效", func(t *testing.T) {
		ap := &AfterUserLevelUpParam{BeforeLevel: 99, AfterLevel: 100, RoomID: 123}
		notifies := ap.levelUpEffectNotifies(mockMessageBubble, mockUserInfo)
		require.Len(notifies, 1)
		assert.Equal(liveim.IMMessageTypeNormal, notifies[0].Type, "通知类型应为普通消息")
		assert.False(notifies[0].Payload.(*levelUpEffectMessage).DisableEffect, "普通消息不禁用特效")
		assert.NotNil(notifies[0].Payload.(*levelUpEffectMessage).Bubble, "普通消息不禁用直播间消息")
	})
	t.Run("单级升级 - 增强特效", func(t *testing.T) {
		ap := &AfterUserLevelUpParam{BeforeLevel: 199, AfterLevel: 200, RoomID: 123}
		notifies := ap.levelUpEffectNotifies(mockMessageBubble, mockUserInfo)
		require.Len(notifies, 2)
		assert.Equal(liveim.IMMessageTypeAll, notifies[0].Type, "第一个通知应为全站消息")
		assert.Equal(liveim.IMMessageTypeNormal, notifies[1].Type, "第二个通知应为普通消息")
		assert.True(notifies[1].Payload.(*levelUpEffectMessage).DisableEffect, "普通消息应禁用特效")
		assert.NotNil(notifies[1].Payload.(*levelUpEffectMessage).Bubble, "普通消息不禁用直播间消息")
	})
	t.Run("跨级升级 - 存在增强特效等级 - 特效等级是最高等级", func(t *testing.T) {
		ap := &AfterUserLevelUpParam{BeforeLevel: 190, AfterLevel: 200, RoomID: 123}
		notifies := ap.levelUpEffectNotifies(mockMessageBubble, mockUserInfo)
		require.Len(notifies, 2, "应返回 2 个通知")
		assert.Equal(liveim.IMMessageTypeAll, notifies[0].Type, "第一个通知应为全站消息")
		assert.Equal(liveim.IMMessageTypeNormal, notifies[1].Type, "第二个通知应为普通消息")
		assert.True(notifies[1].Payload.(*levelUpEffectMessage).DisableEffect, "普通消息应禁用特效")
		assert.NotNil(notifies[1].Payload.(*levelUpEffectMessage).Bubble, "普通消息不禁用直播间消息")
	})
	t.Run("跨级升级 - 存在增强特效等级 - 特效等级不是最高等级", func(t *testing.T) {
		ap := &AfterUserLevelUpParam{BeforeLevel: 190, AfterLevel: 205, RoomID: 123}
		notifies := ap.levelUpEffectNotifies(mockMessageBubble, mockUserInfo)
		require.Len(notifies, 2, "应返回 2 个通知")
		assert.Equal(liveim.IMMessageTypeAll, notifies[0].Type, "第一个通知应为全站消息")
		assert.Equal(liveim.IMMessageTypeNormal, notifies[1].Type, "第二个通知应为普通消息（最终等级）")
		assert.False(notifies[1].Payload.(*levelUpEffectMessage).DisableEffect, "第二个普通消息不禁用特效")
		assert.NotNil(notifies[1].Payload.(*levelUpEffectMessage).Bubble, "第二个普通消息不禁用直播间消息")
	})
	t.Run("跨级升级 - 不存在增强特效等级", func(t *testing.T) {
		ap := &AfterUserLevelUpParam{BeforeLevel: 190, AfterLevel: 195, RoomID: 123}
		notifies := ap.levelUpEffectNotifies(mockMessageBubble, mockUserInfo)
		require.Len(notifies, 1, "应返回 1 个通知")
		assert.Equal(liveim.IMMessageTypeNormal, notifies[0].Type, "通知类型应为普通消息")
		assert.False(notifies[0].Payload.(*levelUpEffectMessage).DisableEffect)
	})
}

func TestBuildBroadcastElem(t *testing.T) {
	assert := assert.New(t)

	t.Run("不带优先级", func(t *testing.T) {
		payload := &levelUpEffectMessage{}
		elem := buildBroadcastElem(liveim.IMMessageTypeNormal, 123, payload)
		assert.Equal(liveim.IMMessageTypeNormal, elem.Type)
		assert.Equal(int64(123), elem.RoomID)
		assert.Equal(payload, elem.Payload)
	})
	t.Run("带优先级", func(t *testing.T) {
		payload := &levelUpEffectMessage{}
		elem := buildBroadcastElem(liveim.IMMessageTypeAll, 123, payload)
		assert.Equal(liveim.IMMessageTypeAll, elem.Type)
		assert.Equal(int64(123), elem.RoomID)
		assert.Equal(payload, elem.Payload)
	})
}

func TestNewLevelUpEffect(t *testing.T) {
	assert := assert.New(t)

	mockBubble := &bubble.Simple{}
	mockUserInfo := &liveuser.UserInfo{}
	mockBubbleCfg := &notifyStyle{}

	msg := newLevelUpEffect(123, 100, mockBubble, mockUserInfo, mockBubbleCfg)
	assert.Equal(liveim.TypeUser, msg.Type)
	assert.Equal(liveim.EventLevelUp, msg.Event)
	assert.Equal(int64(123), msg.RoomID)
	assert.Equal(100, msg.Level.Level)
	assert.Equal(mockBubble, msg.Bubble)
	assert.Equal(mockUserInfo, msg.User)
	assert.False(msg.DisableEffect)
}

func TestLevelUpEffectMessage_disableEffect(t *testing.T) {
	assert := assert.New(t)

	msg := &levelUpEffectMessage{}
	disabledMsg := msg.disableEffect()
	assert.True(disabledMsg.DisableEffect)
	assert.Equal(msg, disabledMsg)
}

func TestNewLevelUpEnhancedEffect(t *testing.T) {
	assert := assert.New(t)

	mockUserInfo := &liveuser.UserInfo{Simple: liveuser.Simple{
		Username: "testuser",
	}}
	mockBubbleCfg := &notifyStyle{}

	msg := newLevelUpEnhancedEffect(123, 200, mockUserInfo, mockBubbleCfg)
	assert.Equal(liveim.TypeNotify, msg.Type)
	assert.Equal(liveim.TypeUser, msg.NotifyType)
	assert.Equal(liveim.EventLevelUp, msg.Event)
	assert.Equal(int64(123), msg.RoomID)
	assert.NotNil(msg.Level)
	assert.Equal(200, msg.Level.Level)
	assert.Equal(mockUserInfo, msg.User)
	assert.Contains(msg.MessageBar.Message, "testuser")
}

func TestCurEnhancedEffectMaxLevel(t *testing.T) {
	assert := assert.New(t)

	t.Run("before >= after", func(t *testing.T) {
		level := curEnhancedEffectMaxLevel(200, 190)
		assert.Zero(level)
	})
	t.Run("低于阈值", func(t *testing.T) {
		level := curEnhancedEffectMaxLevel(190, 195)
		assert.Zero(level)
	})
	t.Run("存在增强特效等级", func(t *testing.T) {
		level := curEnhancedEffectMaxLevel(190, 205)
		assert.Equal(200, level)
	})
	t.Run("范围内无增强特效等级", func(t *testing.T) {
		level := curEnhancedEffectMaxLevel(201, 209)
		assert.Zero(level)
	})
}

func TestLevelMilestones(t *testing.T) {
	assert := assert.New(t)

	t.Run("before >= after", func(t *testing.T) {
		levels := levelMilestones(200, 190, []int{160, 180, 190})
		assert.Empty(levels)
	})
	t.Run("低于所有特定等级", func(t *testing.T) {
		levels := levelMilestones(150, 155, []int{160, 180, 190})
		assert.Empty(levels)
	})
	t.Run("穿过单一特定等级", func(t *testing.T) {
		levels := levelMilestones(170, 180, []int{160, 180, 190})
		assert.Equal([]int{180}, levels)
	})
	t.Run("穿过多个特定等级", func(t *testing.T) {
		levels := levelMilestones(150, 200, []int{160, 180, 190})
		assert.Equal([]int{160, 180, 190}, levels)
	})
	t.Run("范围内无特定等级", func(t *testing.T) {
		levels := levelMilestones(201, 209, []int{160, 180, 190})
		assert.Empty(levels)
	})
	t.Run("空或 nil 的 importantLevels", func(t *testing.T) {
		// 测试空切片
		levels1 := levelMilestones(150, 200, []int{})
		assert.Empty(levels1)
		// 测试 nil
		levels2 := levelMilestones(150, 200, nil)
		assert.Empty(levels2)
	})
}
