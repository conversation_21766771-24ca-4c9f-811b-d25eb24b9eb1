package userstatus

import (
	"fmt"
	"math"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 相关文档: https://info.missevan.com/pages/viewpage.action?pageId=28280990 直播用户等级体系

const (
	systemNotify45  = 45
	systemNotify70  = 70
	systemNotify90  = 90
	systemNotify120 = 120
	systemNotify140 = 140
	systemNotify150 = 150
	systemNotify155 = 155
	systemNotify160 = 160
	systemNotify165 = 165
	systemNotify170 = 170
	systemNotify180 = 180
	systemNotify185 = 185
	systemNotify190 = 190
)

const (
	privilegeExclusiveGiftUserLevel = 155 // 可以拥有专属礼物包的用户等级
)

const (
	questOnline = 6 * 60 * 1000 // 观看直播 6 分钟任务时长
)

const (
	contributionQuestDailySpend = 500 // 经验值奖励：每日首次在直播间消费
)

// status of share
const (
	ShareStatusPending = iota
	ShareStatusShared
)

// MaoerWalletUserID 猫耳娘的零钱袋用户 ID
const MaoerWalletUserID int64 = 2939325

// AddContributionParams From 增加经验来源
const (
	FromNormal   = iota // 默认来源
	FromGiftSend        // 来自直播间送礼
)

const (
	enhancedEffectUserLevelStart    = 200  // 增强特效的起始等级
	enhancedEffectUserLevelInterval = 10   // 增强特效的等级间隔
	defaultEffectDuration           = 3000 // 默认特效时长（毫秒）
)

// 升级权益发放动作
type levelUpPrivilegeAction func(userID int64, levelFrom int, levelTo int) (bool, error)

// 升级权益发放动作列表，使用 slice 保证顺序执行
var levelUpPrivilegeActionList = []struct {
	name   string
	action levelUpPrivilegeAction
}{
	{"45 级生日权益", level45BirthdayPrivilege},
	{"160 级升级权益", level160Privilege},
	{"180 190 级升级权益", level180Or190Privilege},
}

func userMetaCollection() *mongo.Collection {
	return service.MongoDB.Collection("user_meta")
}

type userOnline struct {
	UserID int64 `bson:"user_id,omitempty"` // 使用 omitempty 防止覆盖保存的时候忘记添加 user_id 导致用户数据缺失

	AcqOnline  int64     `bson:"t_acq_online"`
	TimeOnline time.Time `bson:"t_time_online"`
}

// AddOnlineTime 增加在线时间
func (param *AddContributionParams) AddOnlineTime(durationMili int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	umCol := userMetaCollection()

	filter := bson.M{"user_id": param.userID}
	var uo userOnline
	err := umCol.FindOne(ctx, filter, options.FindOne().SetProjection(
		bson.M{
			"t_acq_online":  1,
			"t_time_online": 1})).Decode(&uo)
	if err != nil && err != mongo.ErrNoDocuments {
		return err
	}
	beforeTime := uo.TimeOnline
	now := goutil.TimeNow()
	bod := util.BeginningOfDay(now)
	var beforeAcq int64
	if beforeTime.Before(bod) {
		beforeAcq = 0
	} else {
		beforeAcq = uo.AcqOnline
	}
	afterAcq := beforeAcq + durationMili
	if beforeAcq < questOnline && questOnline <= afterAcq {
		// 完成任务“连续观看一个直播 6 分钟”
		err := param.AddContribution(200)
		if err != nil {
			return err
		}
	}
	uo.UserID = param.userID
	uo.AcqOnline = afterAcq
	uo.TimeOnline = now
	_, err = umCol.UpdateOne(ctx, filter, bson.M{"$set": uo}, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	return nil
}

type userPurchase struct {
	StatusPurchase int       `bson:"t_status_purchase,omitempty"`
	TimePurchase   time.Time `bson:"t_time_purchase"`
}

// AddPurchaseContribution 增加花费钻石的经验值，不包括贵族开通续费
// NOTICE: 一般来说，1 钻石 = 10 经验值
func (param *AddContributionParams) AddPurchaseContribution(add int64) error {
	// 任务每日首次在直播间消费
	first, err := todayFirstSpend(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if first {
		add += contributionQuestDailySpend
		up := userPurchase{
			TimePurchase:   goutil.TimeNow(),
			StatusPurchase: 1,
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err = userMetaCollection().UpdateOne(ctx, bson.M{"user_id": param.userID},
			bson.M{"$set": up}, options.Update().SetUpsert(true))
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	return param.AddContribution(add)
}

// AddContributionParams 增加经验参数
type AddContributionParams struct {
	userID          int64
	roomID          int64 // 实际消费直播间
	creatorUsername string
	isFirstJoin     bool
	from            int // 0: 无来源 1: 直播间送礼
	userVip         *vip.UserVip

	fromRoomID          int64 // 跨直播间消费时的来源直播间
	fromCreatorUsername string
}

// NewAddContributionParams AddContributionParams 的构造函数
func NewAddContributionParams(userID int64, roomID int64, creatorUsername string, from int, userVip *vip.UserVip) *AddContributionParams {
	return &AddContributionParams{userID: userID, roomID: roomID, creatorUsername: creatorUsername, from: from, userVip: userVip}
}

// SetFromRoom set fromRoom info
func (param *AddContributionParams) SetFromRoom(fromRoomID int64, fromCreatorUsername string) {
	param.fromRoomID = fromRoomID
	param.fromCreatorUsername = fromCreatorUsername
}

// NotifyRoomID 通知的直播间 ID
func (param *AddContributionParams) NotifyRoomID() int64 {
	if param.fromRoomID != 0 {
		return param.fromRoomID
	}
	return param.roomID
}

// NotifyCreatorUsername 通知的主播用户名
func (param *AddContributionParams) NotifyCreatorUsername() string {
	if param.fromRoomID != 0 { // 根据直播间 ID 来源判断
		return param.fromCreatorUsername
	}
	return param.creatorUsername
}

// AddContribution 增加经验值
// 只给存在的用户加经验值
// 只有用户直播间送礼才发送用户等级经验变更 WS 消息
func (param *AddContributionParams) AddContribution(add int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	update := bson.M{
		"$set": bson.M{"updated_time": goutil.TimeNow()},
		"$inc": bson.M{"contribution": add},
	}
	var s liveuser.Simple
	err := liveuser.Collection().FindOneAndUpdate(ctx, bson.M{"user_id": param.userID}, update,
		options.FindOneAndUpdate().SetProjection(bson.M{"user_id": 1, "username": 1, "contribution": 1}).
			SetReturnDocument(options.Before)).Decode(&s)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil
		}
		return err
	}
	beforeLevel := usercommon.Level(s.Contribution)
	afterLevel := usercommon.Level(s.Contribution + add)
	if beforeLevel < afterLevel {
		ap := AfterUserLevelUpParam{
			User:            &s,
			RoomID:          param.NotifyRoomID(),
			CreatorUsername: param.NotifyCreatorUsername(),
			BeforeLevel:     beforeLevel,
			AfterLevel:      afterLevel,
			IsFirstJoin:     param.isFirstJoin,
		}
		ap.AfterUserLevelUp()
	}
	s.Contribution += add
	if param.from == FromGiftSend { // 当前只支持赠送礼物时同步用户等级经验
		return syncContribution(param.NotifyRoomID(), &s, param.userVip)
	}
	return nil
}

type syncLevelMessage struct {
	Type       string         `json:"type"`
	NotifyType string         `json:"notify_type"`
	Event      string         `json:"event"`
	RoomID     int64          `json:"room_id"`
	UserID     int64          `json:"user_id"`
	UserLevel  *UserLevelInfo `json:"user_level"`
}

// UserLevelInfo 用户等级进度条信息
type UserLevelInfo struct {
	Exp          int64    `json:"exp"`                      // 当前经验值 - 本级初始经验值
	LevelUpExp   int64    `json:"level_up_exp"`             // 下级初始经验值 - 本级初始经验值，用户满级时为 0
	LevelUpCoin  int64    `json:"level_up_coin"`            // 钻石差值，用户满级时为 0
	Level        int      `json:"level"`                    // 用户等级
	LevelSpeedUp *speedUp `json:"level_speed_up,omitempty"` // 无加速特权或用户满级时不返回
	LevelIconURL string   `json:"level_icon_url"`           // 等级勋章，下发则展示，不下发则自己生成
}

type speedUp struct {
	PrivilegeTitle string  `json:"privilege_title"` // 贵族名称
	Factor         float64 `json:"factor"`          // 消费加速倍数
}

// syncContribution 同步经验值
// userVip 需要是用户有效的最高等级贵族信息
func syncContribution(roomID int64, s *liveuser.Simple, userVip *vip.UserVip) error {
	if roomID == 0 {
		return nil
	}
	userLevel, err := NewUserLevelInfo(s, userVip)
	if err != nil {
		return err
	}
	payload := &syncLevelMessage{
		Type:       liveim.TypeUserNotify,
		NotifyType: liveim.TypeLevel,
		Event:      liveim.EventUpdate,
		RoomID:     roomID,
		UserID:     s.UserID(),
		UserLevel:  userLevel,
	}
	return userapi.BroadcastUser(roomID, s.UserID(), payload)
}

// NewUserLevelInfo 生成用户等级数据
func NewUserLevelInfo(user *liveuser.Simple, userVip *vip.UserVip) (*UserLevelInfo, error) {
	level := usercommon.Level(user.Contribution)
	startCon := usercommon.GetLevelStartContribution(level)
	p := &UserLevelInfo{
		Exp:          user.Contribution - startCon,
		Level:        level,
		LevelIconURL: usercommon.LevelIconURL(level),
	}
	isMaxLevel := usercommon.IsMaxLevel(level)
	if isMaxLevel {
		// 满级时 LevelUpExp 为 0
		p.LevelUpExp = 0
	} else {
		nextCon := usercommon.GetLevelStartContribution(level + 1)
		p.LevelUpExp = nextCon - startCon
		p.buildLevelSpeedUp(userVip)
		p.calcCoin()
	}
	return p, nil
}

func (u *UserLevelInfo) buildLevelSpeedUp(userVip *vip.UserVip) {
	if userVip == nil || !userVip.IsActive() || userVip.Info == nil {
		return
	}
	if userVip.Info.ExpAcceleration == 0 {
		return
	}
	u.LevelSpeedUp = &speedUp{
		Factor:         float64(userVip.Info.ExpAcceleration)/100 + 1,
		PrivilegeTitle: userVip.Title,
	}
}

func (u *UserLevelInfo) calcCoin() {
	f := float64(1)
	if u.LevelSpeedUp != nil {
		f = u.LevelSpeedUp.Factor
	}
	// 1 钻石 = 10 经验
	// LevelUpCoin = (下一级初始经验值 - 当前经验值) / [10*(1+ExpAcceleration%)]
	u.LevelUpCoin = int64(math.Ceil(float64((u.LevelUpExp - u.Exp)) / float64(f*10)))
}

// AfterUserLevelUpParam 用户升级参数
type AfterUserLevelUpParam struct {
	User            *liveuser.Simple
	RoomID          int64
	CreatorUsername string
	BeforeLevel     int
	AfterLevel      int
	IsFirstJoin     bool
}

// AfterUserLevelUp 用户升级后的操作
func (ap *AfterUserLevelUpParam) AfterUserLevelUp() {
	levelUpSystemNotify(ap.User.UserID(), ap.BeforeLevel, ap.AfterLevel)
	levelUpPrivilege(ap.User.UserID(), ap.BeforeLevel, ap.AfterLevel)
	if ap.User.UserID() == MaoerWalletUserID {
		// 猫耳娘的零钱袋不发送升级特效和飘屏
		return
	}
	ap.levelUpNotify()
	ap.levelUpEffect()
}

func levelUpPrivilege(userID int64, levelFrom int, levelTo int) {
	var privilegeSent bool
	for _, item := range levelUpPrivilegeActionList {
		actionResult, err := item.action(userID, levelFrom, levelTo)
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":     userID,
				"action_name": item.name,
			}).Error(err)
			// PASS
		}
		privilegeSent = actionResult || privilegeSent
	}
	if privilegeSent {
		userappearance.ClearCache(userID)
	}
}

func level45BirthdayPrivilege(userID int64, levelFrom int, levelTo int) (bool, error) {
	// 不是突破 45 级的的情况不需要发放生日特权
	if levelFrom >= systemNotify45 || systemNotify45 > levelTo {
		return false, nil
	}
	// 获取生日特权外观
	appearances, err := appearance.GetBirthdayAppearances()
	if err != nil {
		return false, err
	}
	// 确认用户是否今天生日
	now := goutil.TimeNow()
	isBirthday, err := user.IsUserBirthday(userID, now, user.LeapYearLookForward)
	if err != nil {
		return false, err
	}
	if !isBirthday {
		return false, nil
	}
	// 确认用户今年是否已经发放过
	isRewarded, err := livebirthdayprivrecord.IsRewarded(userID, now.Year())
	if err != nil {
		return false, err
	}
	if isRewarded {
		return false, nil
	}
	err = livebirthdayprivrecord.RecordUserBirthday(userID, goutil.TimeNow())
	if err != nil {
		return false, fmt.Errorf("生日特权记录错误：%v", err)
	}
	expireTime := util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1).Unix()
	err = userappearance.AddAppearances(userID, 0, expireTime, appearances, true)
	if err != nil {
		return false, fmt.Errorf("生日特权外观发放错误：%v", err)
	}
	return true, nil
}

func level160Privilege(userID int64, levelFrom int, levelTo int) (bool, error) {
	// 不是突破 160 级的的情况不需要发放特权
	if levelFrom >= systemNotify160 || systemNotify160 > levelTo {
		return false, nil
	}
	// 获取等级特权外观
	appearances, err := appearance.GetGte160RewardAppearances()
	if err != nil {
		return false, err
	}
	// TODO: -1 为永久有效，需改为使用 userappearance 中对应的常量
	err = userappearance.AddAppearances(userID, 0, -1, appearances, false)
	if err != nil {
		return false, fmt.Errorf("160 级特权外观发放错误：%v", err)
	}
	return true, nil
}

// level180Or190Privilege 180 或 190 等级权益
func level180Or190Privilege(userID int64, levelFrom int, levelTo int) (bool, error) {
	// 判断升级是否经过了 180 190 级
	privilegeLevels := levelMilestones(levelFrom, levelTo, []int{systemNotify180, systemNotify190})
	if len(privilegeLevels) == 0 {
		return false, nil
	}
	// 获取对应等级的身份铭牌外观
	appearances, err := appearance.GetIdentityBadgesByLevel(privilegeLevels)
	if err != nil {
		return false, err
	}
	err = userappearance.AddAppearances(userID, 0, -1, appearances, true)
	if err != nil {
		return false, fmt.Errorf("%d 级特权身份铭牌外观发放错误：%v", levelTo, err)
	}
	return true, nil
}

// levelUpSystemNotify 触发等级上升的通知
func levelUpSystemNotify(userID int64, beforeLevel, afterLevel int) *pushservice.SystemMsg {
	var content string
	title := "直播用户等级升级！新特权解锁！"
	// 多次跳级只通知最新的一条
	switch {
	case beforeLevel < systemNotify185 && systemNotify185 <= afterLevel:
		content = `尊贵的用户您好！恭喜您的直播等级已达到 185 级，即刻起为您解锁星享馆使用权益。` +
			`星享馆是仅为直播等级 ≥ 185 级的用户提供的尊享权益礼包，每两个月更新一次。` +
			`您可以升级至 6.1.3 版本，前往任意直播间内点击右下角“···”按钮，或前往 PC 端直播间点击右侧“玩法”区域处打开星享馆。如有更多问题可联系客服咨询~ ` +
			`<a href="` + config.Conf.Params.URL.Live + `user/level" target="_blank">了解更多等级特权请点击此处~</a>`
	case beforeLevel < systemNotify170 && systemNotify170 <= afterLevel:
		content = `当当当当~ 恭喜您直播用户等级升级到 170 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权定制静态表情包正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href="copy:3008181774">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)`
	case beforeLevel < systemNotify165 && systemNotify165 <= afterLevel:
		content = `当当当当~ 恭喜您直播用户等级升级到 165 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权定制静态表情包正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href="copy:3008181774">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)`
	case beforeLevel < systemNotify160 && systemNotify160 <= afterLevel:
		content = `尊贵的用户您好！恭喜您的直播等级已达到 160 级，您已获得该等级专属系列外观使用权益，` +
			`可前往“我的” - “直播中心” - “外观中心”处佩戴等级专属外观。如更有更多问题可联系客服咨询~ ` +
			`<a href="` + config.Conf.Params.URL.Live + `user/level" target="_blank">了解更多等级特权请点击此处~</a>`
	case beforeLevel < systemNotify155 && systemNotify155 <= afterLevel:
		content = `当当当当~ 恭喜您直播用户等级升级到 155 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权专属礼物赠送资格已成功解锁（礼物包内含礼物每个季度更新一次），您可以在专属礼物栏内进行赠送哦~ (,,• ₃ •,,)`
	case beforeLevel < systemNotify150 && systemNotify150 <= afterLevel:
		content = `当当当当~ 恭喜您直播用户等级升级到 150 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权荣耀特权礼包（包含定制动态头像框、气泡框、个人名片框及定制动效礼物）正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href="copy:3008181774">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)`
	case beforeLevel < systemNotify140 && systemNotify140 <= afterLevel:
		content = `当当当当~ 恭喜您直播用户等级升级到 140 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权个人定制气泡框正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href="copy:3008181774">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)`
	case beforeLevel < systemNotify120 && systemNotify120 <= afterLevel:
		content = `当当当当~ 恭喜您直播用户等级升级到 120 级，好厉害哦 (☆ﾟ∀ﾟ) 您的新特权个人定制进场特效正在快马加鞭向您奔来，请您联系专属 VIP 客服 QQ：<a href="copy:3008181774">3008181774</a> 咨询定制事宜哦！~ (,,• ₃ •,,)`
	case beforeLevel < systemNotify90 && systemNotify90 <= afterLevel:
		content = `恭喜您直播用户等级升级到 90 级，再接再厉哦 (•̀ᴗ•́)و ̑̑ ` + "\n" +
			`您的新特权 VIP 客服将在 QQ 平台为您服务，以下是您的专属 VIP 客服 QQ：<a href="copy:3008181774">3008181774</a>，请您添加喔~` + "\n" +
			`添加时请备注您的 M 号，么么哒 (,,• ₃ •,,)`
	case beforeLevel < systemNotify70 && systemNotify70 <= afterLevel:
		content = `恭喜您直播用户等级升级到 70 级，再接再厉哦 (•̀ᴗ•́)و ̑̑ ` + "\n" +
			`您的新特权高级客服将在 QQ 平台为您服务，以下是您的专属高级客服 QQ：<a href="copy:800181712">800181712</a>，请您添加喔~` + "\n" +
			`添加时请备注您的 M 号，么么哒 (,,• ₃ •,,)`
	case beforeLevel < systemNotify45 && systemNotify45 <= afterLevel:
		content = `尊贵的用户您好！恭喜您的直播等级已达到 45 级，即刻起为您解锁该等级权益：` +
			`您可在生日当天获得小寿星专属头像框与称号~ ` +
			`生日信息可通过前往“我的” - “设置” - “个人资料” - “出生年月”处设置和修改。` +
			`（每年您仅可解锁该外观权益 1 次哦，请谨慎修改生日信息！如有更多问题可联系客服咨询）` +
			`<a href="` + config.Conf.Params.URL.Live + `user/level" target="_blank">了解更多等级特权请点击此处~</a>`
	default:
		return nil
	}
	msg := pushservice.SystemMsg{
		UserID:  userID,
		Title:   title,
		Content: content,
	}
	err := service.PushService.SendSystemMsg([]pushservice.SystemMsg{msg})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return &msg
}

const (
	inRoomNotify    = `<font color="${textColor}">恭喜</font> <font color="${highlightTextColor}">${username}</font> <font color="${textColor}">在</font> <font color="${highlightTextColor}">${creator_username}</font> <font color="${textColor}">直播间内升级到</font> <font color="${highlightTextColor}">${level}</font> <font color="${textColor}">级，快来围观吧~</font>`
	notInRoomNotify = `<font color="${textColor}">恭喜</font> <font color="${highlightTextColor}">${username}</font> <font color="${textColor}">升级到</font> <font color="${highlightTextColor}">${level}</font> <font color="${textColor}">级~</font>`
)

const (
	bubbleIDLevel120 int64 = 12000 + iota
	bubbleIDLevel125
	bubbleIDLevel130
	bubbleIDLevel135
	bubbleIDLevel140
	bubbleIDLevel145
	bubbleIDLevel150
	bubbleIDLevel201
)

const (
	bubbleIDLevelGte200Interval10 = 12012 // 200 级及之后每升 10 级新飘屏配置
)

// notifyStyle 升级相关样式
type notifyStyle struct {
	level int
	// 飘屏 bubble ID
	levelBubbleID int64
	// 等级特效背景图
	messageFile string
	// 文字颜色
	textColor string
	// 高亮文字颜色
	highlightTextColor string
}

func (style *notifyStyle) buildParam() map[string]string {
	return map[string]string{
		"level":              strconv.Itoa(style.level),
		"textColor":          style.textColor,
		"highlightTextColor": style.highlightTextColor,
	}
}

func findLevelBubbleConfig(afterLevel int) (style *notifyStyle) {
	defer func() {
		if style != nil {
			style.level = afterLevel
		}
	}()
	switch {
	case afterLevel >= 200 && afterLevel%10 == 0:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevelGte200Interval10,
			messageFile:        "oss://live/userlevels/messagebars/new200_0_122_0_122.png",
			textColor:          "#AA8534", // 仅特效使用
			highlightTextColor: "#834A0C", // 仅特效使用
		}
	case afterLevel >= 201:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel201,
			messageFile:        "oss://live/userlevels/messagebars/201_0_122_0_122.png",
			textColor:          "#FFF0CD", // 仅特效使用
			highlightTextColor: "#FFF0CD", // 仅特效使用
		}
	case afterLevel >= 150:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel150,
			messageFile:        "oss://live/userlevels/messagebars/150_0_122_0_122.png",
			textColor:          "#FFE072",
			highlightTextColor: "#FF8F52",
		}
	case afterLevel >= 145:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel145,
			messageFile:        "oss://live/userlevels/messagebars/145_0_122_0_122.png",
			textColor:          "#FF9E48",
			highlightTextColor: "#FFCD48",
		}
	case afterLevel >= 140:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel140,
			messageFile:        "oss://live/userlevels/messagebars/140_0_122_0_122.png",
			textColor:          "#FF8FA4",
			highlightTextColor: "#FBFFC9",
		}
	case afterLevel >= 135:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel135,
			messageFile:        "oss://live/userlevels/messagebars/135_0_122_0_122.png",
			textColor:          "#FF88E4",
			highlightTextColor: "#FFD5B9",
		}
	case afterLevel >= 130:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel130,
			messageFile:        "oss://live/userlevels/messagebars/130_0_122_0_122.png",
			textColor:          "#F08EFF",
			highlightTextColor: "#FFC197",
		}
	case afterLevel >= 125:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel125,
			messageFile:        "oss://live/userlevels/messagebars/125_0_122_0_122.png",
			textColor:          "#CEB1FF",
			highlightTextColor: "#B6DEFF",
		}
	case afterLevel >= 120:
		return &notifyStyle{
			levelBubbleID:      bubbleIDLevel120,
			messageFile:        "oss://live/userlevels/messagebars/120_0_122_0_122.png",
			textColor:          "#D1C3FF",
			highlightTextColor: "#FEFFC3",
		}
	case afterLevel >= 100:
		return &notifyStyle{
			messageFile:        "oss://live/userlevels/messagebars/100_0_122_0_122.png",
			textColor:          "#BB9DFF",
			highlightTextColor: "#EAA3FF",
		}
	case afterLevel >= 70:
		return &notifyStyle{
			messageFile:        "oss://live/userlevels/messagebars/70_0_122_0_122.png",
			textColor:          "#FFA1A1",
			highlightTextColor: "#FFD9D9",
		}
	case afterLevel >= 55:
		return &notifyStyle{
			messageFile:        "oss://live/userlevels/messagebars/55_0_122_0_122.png",
			textColor:          "#E7C665",
			highlightTextColor: "#FFF5CD",
		}
	}
	return nil
}

// levelUpNotify 升级飘屏
func (ap *AfterUserLevelUpParam) levelUpNotify() []*userapi.BroadcastElem {
	bubbleCfg := findLevelBubbleConfig(ap.AfterLevel)
	if bubbleCfg == nil || bubbleCfg.levelBubbleID == 0 {
		// bubbleCfg == nil 说明用户当前等级较低，不需要执行下面其余的逻辑
		return nil
	}

	paramMap := bubbleCfg.buildParam()
	paramMap["username"] = ap.User.Username

	// 在房间内升级时，判断用户是否隐身
	if ap.RoomID != 0 && ap.CreatorUsername != "" {
		paramMap["creator_username"] = ap.CreatorUsername
		if v, err := vip.UserActivatedVip(ap.User.UserID(), true, nil); err == nil {
			userInvisible := vip.HavePrivilege(v, vip.PrivilegeInvisible) && Invisible(ap.User.UserID())
			rankInvisible := vip.HavePrivilege(v, vip.PrivilegeRankInvisible) && IsRankInvisible(ap.User.UserID(), ap.RoomID, true)
			if userInvisible || rankInvisible {
				delete(paramMap, "creator_username")
			}
		}
	}

	b, err := bubble.FindSimple(bubbleCfg.levelBubbleID)
	if err != nil {
		logger.Error(err)
		// PASS
	} else if b == nil {
		logger.Errorf("cannot find bubble %d", bubbleCfg.levelBubbleID)
		// PASS
	}
	if ap.AfterLevel >= 200 && b != nil { // 用户直播等级大于等于 200，飘屏字体颜色从 Bubble 获取
		paramMap["textColor"] = b.NormalColor
		paramMap["highlightTextColor"] = b.HighlightColor
	}
	notifies := make([]*userapi.BroadcastElem, 0, 2)
	// 根据是否在房间内和用户是否隐身来选择要发送的飘屏消息
	if _, ok := paramMap["creator_username"]; ok {
		notifies = append(notifies, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeAll,
			Payload: notifymessages.NewGeneral(ap.RoomID, goutil.FormatMessage(inRoomNotify, paramMap), b),
		})
	} else {
		notifies = append(notifies, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeAll,
			Payload: notifymessages.NewGeneral(0, goutil.FormatMessage(notInRoomNotify, paramMap), b),
		})
	}

	var enableNotify bool
	config.GetAB("enable_user_155_level_gift_notify", &enableNotify)
	// 更新 155 级用户礼物包
	if enableNotify && ap.RoomID != 0 &&
		ap.BeforeLevel < privilegeExclusiveGiftUserLevel && ap.AfterLevel >= privilegeExclusiveGiftUserLevel {
		notifies = append(notifies, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: ap.RoomID,
			UserID: ap.User.UserID(),
			Payload: map[string]interface{}{
				"type":    liveim.TypeRoom,
				"event":   liveim.EventGiftUpdate,
				"room_id": ap.RoomID,
			},
		})
	}

	goutil.Go(func() {
		// 当用户因为首次进入直播间升级时，websocket 还没有建立起来，需延迟 1s 下发
		if ap.IsFirstJoin {
			<-time.After(time.Second)
		}
		err = userapi.BroadcastMany(notifies)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	})
	return notifies
}

// LevelUpEffect 升级特效
type LevelUpEffect struct {
	Level          int    `json:"level,omitempty"`
	IconURL        string `json:"icon_url,omitempty"`
	EffectURL      string `json:"effect_url,omitempty"`
	WebEffectURL   string `json:"web_effect_url,omitempty"`
	EffectDuration int64  `json:"effect_duration,omitempty"`  // 特效时长，毫秒级
	EffectImageURL string `json:"effect_image_url,omitempty"` // app 合成增强特效的图片地址（目前就是头像地址）
}

// MessageBar 特效提示
type MessageBar struct {
	ImageURL string `json:"image_url"`
	Message  string `json:"message"`
}

// levelUpEffectMessage 升级特效
// type: user
// event: level_up
type levelUpEffectMessage struct {
	Type          string             `json:"type"`
	NotifyType    string             `json:"notify_type,omitempty"`
	Event         string             `json:"event"`
	RoomID        int64              `json:"room_id"`
	MessageBar    MessageBar         `json:"message_bar"`
	Level         LevelUpEffect      `json:"level"`
	User          *liveuser.UserInfo `json:"user"`
	Bubble        *bubble.Simple     `json:"bubble,omitempty"`
	DisableEffect bool               `json:"disable_effect"` // 是否禁用特效，应用场景：发全站特效时，直播间特效需要禁用
}

// newLevelUpEffectMessage new levelUpEffectMessage
func newLevelUpEffectMessage(roomID int64, level LevelUpEffect, user *liveuser.UserInfo, notifyBubble *bubble.Simple, messageBar MessageBar) *levelUpEffectMessage {
	return &levelUpEffectMessage{
		Type:       liveim.TypeUser,
		Event:      liveim.EventLevelUp,
		User:       user,
		RoomID:     roomID,
		MessageBar: messageBar,
		Bubble:     notifyBubble,
		Level:      level,
	}
}

// effectMessage 升级特效消息格式
const (
	effectMessage        = `<font color="${textColor}">恭喜您升级到</font> <font color="${highlightTextColor}">${level}</font> <font color="${textColor}">级</font>`
	enhanceEffectMessage = `<font color="${textColor}">恭喜 [ ${username} ] </font> <font color="${highlightTextColor}">升级到 Lv.${level} ！</font>`
)

const (
	usernameMaxWidth  = 12 // 飘屏信息条支持完整显示的用户名宽度
	usernameTrimWidth = 10 // 飘屏信息条不支持完整显示时截取的宽度
)

// levelUpEffect 升级特效
// WORKAROUND: 延迟 1s 发送，兼容 iOS 4.6.3 版本客户端升级特效会被打断
func (ap *AfterUserLevelUpParam) levelUpEffect() []*userapi.BroadcastElem {
	if ap.RoomID == 0 {
		return nil
	}

	messageBubble, err := userappearance.FindMessageBubble(ap.User.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}

	userInfo, err := liveuser.FindUserInfo(ap.User.UserID(), ap.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if userInfo == nil {
		return nil
	}

	notifies := ap.levelUpEffectNotifies(messageBubble, userInfo)
	if len(notifies) == 0 {
		return nil
	}

	goutil.Go(func() {
		// 当用户因为首次进入直播间升级时，websocket 还没有建立起来，需延迟 1s 下发
		<-time.After(time.Second)
		for i, notify := range notifies {
			err := userapi.BroadcastMany([]*userapi.BroadcastElem{notify})
			if err != nil {
				logger.Error(err)
				// PASS
			}
			if i != len(notifies)-1 {
				<-time.After(100 * time.Millisecond) // 延迟 100 毫秒发送，保证优先级高的全站通知先发送
			}
		}
	})
	return notifies
}

// levelUpEffectNotifies 生成升级特效广播通知，返回结果中按优先级正序排列，保证发送时优先级最高的先发送：增强特效优先级最高在 slice 最前面
func (ap *AfterUserLevelUpParam) levelUpEffectNotifies(messageBubble *bubble.Simple, userInfo *liveuser.UserInfo) []*userapi.BroadcastElem {
	if ap.BeforeLevel <= 0 || ap.AfterLevel <= ap.BeforeLevel {
		return nil
	}
	// 在 (BeforeLevel, AfterLevel] 区间内查找需要发增强特效的最大用户等级
	enhancedEffectUserLevel := curEnhancedEffectMaxLevel(ap.BeforeLevel, ap.AfterLevel)
	// 判断需要发送最终等级的普通特效消息：升级区间内不存在发增强特效的等级或 AfterLevel 不是要发增强特效的等级
	needFinalNormalEffect := enhancedEffectUserLevel == 0 || enhancedEffectUserLevel != ap.AfterLevel

	notifies := make([]*userapi.BroadcastElem, 0, 2)
	// 需要下发增强特效
	if enhancedEffectUserLevel > 0 {
		bubbleCfg := findLevelBubbleConfig(enhancedEffectUserLevel)
		if bubbleCfg == nil {
			return nil
		}
		enhancedEffect := newLevelUpEnhancedEffect(ap.RoomID, enhancedEffectUserLevel, userInfo, bubbleCfg)
		notifies = append(notifies, buildBroadcastElem(liveim.IMMessageTypeAll, ap.RoomID, enhancedEffect))
		// 判断最后是否会下发普通特效，不下发则添加普通特效兼容老版本
		if !needFinalNormalEffect {
			effect := newLevelUpEffect(ap.RoomID, enhancedEffectUserLevel, messageBubble, userInfo, bubbleCfg).disableEffect()
			notifies = append(notifies, buildBroadcastElem(liveim.IMMessageTypeNormal, ap.RoomID, effect))
		}
	}
	// 需要下发普通特效
	if needFinalNormalEffect {
		bubbleCfg := findLevelBubbleConfig(ap.AfterLevel)
		if bubbleCfg == nil {
			return nil
		}
		effect := newLevelUpEffect(ap.RoomID, ap.AfterLevel, messageBubble, userInfo, bubbleCfg)
		notifies = append(notifies,
			buildBroadcastElem(liveim.IMMessageTypeNormal, ap.RoomID, effect),
		)
	}
	return notifies
}

// buildBroadcastElem 构建广播数据
func buildBroadcastElem(msgType int, roomID int64, payload *levelUpEffectMessage) *userapi.BroadcastElem {
	return &userapi.BroadcastElem{
		Type:    msgType,
		RoomID:  roomID,
		Payload: payload,
	}
}

// newLevelUpEffect 构建升级特效，发给直播间用户
func newLevelUpEffect(roomID int64, level int, bubble *bubble.Simple,
	userInfo *liveuser.UserInfo, bubbleCfg *notifyStyle) *levelUpEffectMessage {
	return &levelUpEffectMessage{
		Type:   liveim.TypeUser,
		Event:  liveim.EventLevelUp,
		User:   userInfo,
		RoomID: roomID,
		MessageBar: MessageBar{
			ImageURL: storage.ParseSchemeURL(bubbleCfg.messageFile),
			Message:  goutil.FormatMessage(effectMessage, bubbleCfg.buildParam()),
		},
		Bubble: bubble,
		Level: LevelUpEffect{
			Level:          level,
			EffectURL:      storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/%d.mp4", config.DefaultCDNScheme, level)),
			WebEffectURL:   storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/%d-web.mp4", config.DefaultCDNScheme, level)),
			EffectDuration: defaultEffectDuration,
			IconURL:        usercommon.LevelIconURL(level),
		},
	}
}

// disableEffect 禁用特效
func (m *levelUpEffectMessage) disableEffect() *levelUpEffectMessage {
	m.DisableEffect = true
	return m
}

// newLevelUpEnhancedEffect 构建升级增强特效，发给全站用户
func newLevelUpEnhancedEffect(roomID int64, level int, userInfo *liveuser.UserInfo, bubbleCfg *notifyStyle) *levelUpEffectMessage {
	buildParam := bubbleCfg.buildParam()

	username := util.UTF8SubStrByWidth(userInfo.Username, usernameMaxWidth)
	if username != userInfo.Username {
		username = util.UTF8SubStrByWidth(username, usernameTrimWidth) + "..."
	}

	buildParam["username"] = username
	buildParam["level"] = strconv.Itoa(level)

	effectURL := storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/new%d.mp4", config.DefaultCDNScheme, level)) +
		";" + storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/new%d.png", config.DefaultCDNScheme, level))

	webEffectURL := storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/new%d-web.mp4", config.DefaultCDNScheme, level)) +
		";" + storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/new%d-web.webm", config.DefaultCDNScheme, level)) +
		";" + storage.ParseSchemeURL(fmt.Sprintf("%s://live/userlevels/effects/v2/new%d-web.png", config.DefaultCDNScheme, level))

	return &levelUpEffectMessage{
		Type:       liveim.TypeNotify,
		NotifyType: liveim.TypeUser,
		Event:      liveim.EventLevelUp,
		User:       userInfo,
		RoomID:     roomID,
		MessageBar: MessageBar{
			ImageURL: storage.ParseSchemeURL(bubbleCfg.messageFile),
			Message:  goutil.FormatMessage(enhanceEffectMessage, buildParam),
		},
		Level: LevelUpEffect{
			Level:          level,
			EffectURL:      effectURL,
			EffectImageURL: userInfo.IconURL, // 头像地址
			WebEffectURL:   webEffectURL,
			EffectDuration: defaultEffectDuration,
		},
	}
}

// curEnhancedEffectMaxLevel 查找 (before, after] 区间内要发增强特效的最高等级，不存在返回 0
func curEnhancedEffectMaxLevel(before, after int) int {
	if after < enhancedEffectUserLevelStart || before >= after {
		return 0
	}
	maxEnhanced := after - ((after - enhancedEffectUserLevelStart) % enhancedEffectUserLevelInterval)
	if maxEnhanced > before {
		return maxEnhanced
	}
	return 0
}

// levelMilestones 查询用户升级过程中经过的特定等级，即从 (before, after] 区间内找特定等级 milestoneLevels
func levelMilestones(before, after int, milestoneLevels []int) []int {
	levels := make([]int, 0, len(milestoneLevels))
	for _, level := range milestoneLevels {
		if before < level && after >= level {
			levels = append(levels, level)
		}
	}
	return levels
}

type userView struct {
	UserID int64 `bson:"user_id,omitempty"` // 使用 omitempty 防止覆盖保存的时候忘记添加 user_id 导致用户数据缺失

	StatusView int       `bson:"t_status_view"`
	TimeView   time.Time `bson:"t_time_view"`
}

// QuestJoinRoom 尝试完成加入房间任务
func (param *AddContributionParams) QuestJoinRoom() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	umCol := userMetaCollection()

	filter := bson.M{"user_id": param.userID}
	var uv userView
	err := umCol.FindOne(ctx, filter, options.FindOne().SetProjection(
		bson.M{
			"t_status_view": 1,
			"t_time_view":   1})).Decode(&uv)
	if err != nil && err != mongo.ErrNoDocuments {
		return err
	}
	now := goutil.TimeNow()
	bod := util.BeginningOfDay(now)
	boy := util.BeginningOfDay(now.AddDate(0, 0, -1))
	if uv.TimeView.After(bod) {
		// 不是今日首次进入直播间
		return nil
	}
	uv.UserID = param.userID
	if uv.TimeView.Before(boy) {
		uv.StatusView = 1
	} else {
		uv.StatusView++ // 存入数据的时候暂时不需要使用 $inc 增加天数，避免并发操作多加一天
	}
	uv.TimeView = now
	expPlus := int64(100) // 每日访问直播间
	if uv.StatusView >= 7 {
		expPlus += 600 // 连续 7 日访问直播间
	}
	param.isFirstJoin = true
	err = param.AddContribution(expPlus)
	if err != nil {
		return err
	}
	_, err = umCol.UpdateOne(ctx, filter, bson.M{"$set": uv}, options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	return nil
}

type userShare struct {
	UserID       int64     `bson:"user_id,omitempty"` // 使用 omitempty 防止覆盖保存的时候忘记添加 user_id 导致用户数据缺失
	TStatusShare int       `bson:"t_status_share"`
	TTimeShare   time.Time `bson:"t_time_share"`
}

// findUserShareInfo 获取用户元信息的分享信息
func findUserShareInfo(userID int64) (*userShare, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	umCol := userMetaCollection()
	filter := bson.M{"user_id": userID}
	var us userShare
	err := umCol.FindOne(ctx, filter, options.FindOne().SetProjection(
		bson.M{
			"t_status_share": 1,
			"t_time_share":   1})).Decode(&us)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}

	return &us, nil
}

// AddWithFirstTimeBonus 首次分享奖励
func (param *AddContributionParams) AddWithFirstTimeBonus(add int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	umCol := userMetaCollection()
	firstTime := false
	now := goutil.TimeNow()
	us, err := findUserShareInfo(param.userID)
	if us == nil {
		if err != nil {
			return err
		}
		// 未在用户元信息中查询到分享信息的情况下
		us = &userShare{
			UserID:       param.userID,
			TStatusShare: ShareStatusShared,
			TTimeShare:   now,
		}
		_, err := umCol.InsertOne(ctx, us)
		if err != nil {
			return err
		}
		firstTime = true
	}
	if err == nil {
		// 在用户元信息查询到分享信息的情况下
		bod := util.BeginningOfDay(now)
		if us.TStatusShare == ShareStatusPending || us.TTimeShare.Before(bod) {
			firstTime = true
			us.TStatusShare = ShareStatusShared
			us.TTimeShare = now
			_, err = umCol.UpdateOne(ctx, bson.M{"user_id": param.userID}, bson.M{"$set": us})
			if err != nil {
				return err
			}
		}
	}
	if firstTime {
		err = param.AddContribution(add)
		if err != nil {
			return err
		}
	}
	return nil
}
