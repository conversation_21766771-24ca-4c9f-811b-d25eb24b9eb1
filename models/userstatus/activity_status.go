package userstatus

import (
	"context"
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// 告白类型
const (
	TypeNotifyShowLove = iota + 1 // 附带直播间飘屏的告白
	TypeNormalShowLove            // 普通告白

	TypeLimit
)

const showLoveNoLimit = -1

// ActivityStatus 活动点数
type ActivityStatus struct {
	Event166 Event166Status `bson:"event_166"`
}

// Event166Status 166 活动告白剩余次数
type Event166Status struct {
	SweetMachineNum  int64 `bson:"sweet_machine_num"`  // 甜蜜留声机
	LoveBillboardNum int64 `bson:"love_billboard_num"` // 爱的公告牌
}

// EventKey event key
func EventKey(eventID int) string {
	return fmt.Sprintf("event_%d", eventID)
}

// UserEventData 获取用户活动信息
func UserEventData(userID int64, key string, out interface{}) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := UserMetaCollection().FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(mongodb.NewProjection(key))).Decode(out)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
	}
	return err
}

// DecreaseEvent166ShowLoveNum 消耗 166 告白次数
// TODO: 写成反射读取 tag 的形式，传入相关 key 和上面的 activityStatus 结构
func DecreaseEvent166ShowLoveNum(ctx context.Context, userID int64, showLoveType int) (bool, Event166Status, error) {
	col := UserMetaCollection()
	filter := bson.M{"user_id": userID}
	proj := bson.M{"event_166": 1}
	var before ActivityStatus
	err := col.FindOne(ctx, filter,
		options.FindOne().SetProjection(proj)).Decode(&before)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return false, Event166Status{}, err
	}

	var inc bson.M
	switch showLoveType {
	case TypeNotifyShowLove:
		if before.Event166.LoveBillboardNum <= 0 {
			return false, before.Event166, nil
		}
		inc = bson.M{"event_166.love_billboard_num": -1}
		filter["event_166.love_billboard_num"] = before.Event166.LoveBillboardNum
	case TypeNormalShowLove:
		if before.Event166.SweetMachineNum <= 0 {
			// 无次数返回 false, 若为无限次 -1 则不用扣除次数，返回 true
			return before.Event166.SweetMachineNum == showLoveNoLimit, before.Event166, nil
		}
		inc = bson.M{"event_166.sweet_machine_num": -1}
		filter["event_166.sweet_machine_num"] = before.Event166.SweetMachineNum
	default:
		panic(fmt.Sprintf("unsupported show love type :%d", showLoveType))
	}

	var after ActivityStatus
	err = col.FindOneAndUpdate(ctx, filter, update{Inc: inc},
		options.FindOneAndUpdate().SetProjection(proj).SetReturnDocument(options.After)).
		Decode(&after)
	if err != nil {
		return false, Event166Status{}, err
	}
	return true, after.Event166, nil
}
