package userstatus

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/service"
)

func TestUserEventData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(3456835)
	require.NoError(GeneralSetOne(bson.M{"user_id": userID},
		bson.M{"user_id": userID, "event_166": Event166Status{SweetMachineNum: 1, LoveBillboardNum: 2}}))

	var event166Status ActivityStatus
	require.NoError(UserEventData(userID, EventKey(166), &event166Status))
	assert.EqualValues(1, event166Status.Event166.SweetMachineNum)
	assert.EqualValues(2, event166Status.Event166.LoveBillboardNum)

	eventStatus := new(ActivityStatus)
	require.NoError(UserEventData(-12345, "test_key", eventStatus))
	assert.Zero(eventStatus.Event166.SweetMachineNum)
	assert.Zero(eventStatus.Event166.LoveBillboardNum)
}

func TestDecreaseEvent166ShowLoveNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(3456835)
	require.NoError(GeneralSetOne(bson.M{"user_id": 3456835},
		bson.M{"user_id": userID, "event_166": Event166Status{SweetMachineNum: 1, LoveBillboardNum: 1}}))
	// 测试找不到
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	ok, event166, err := DecreaseEvent166ShowLoveNum(ctx, userID+1, TypeNormalShowLove)
	require.NoError(err)
	assert.False(ok)
	assert.Zero(event166.LoveBillboardNum)
	assert.Zero(event166.SweetMachineNum)
	// 普通告白，正确减少
	ok, event166, err = DecreaseEvent166ShowLoveNum(ctx, userID, TypeNormalShowLove)
	require.NoError(err)
	assert.True(ok)
	assert.Zero(event166.SweetMachineNum)
	assert.EqualValues(1, event166.LoveBillboardNum)
	// 普通告白，点数不够
	ok, event166, err = DecreaseEvent166ShowLoveNum(ctx, userID, TypeNormalShowLove)
	require.NoError(err)
	assert.False(ok)
	assert.Zero(event166.SweetMachineNum)
	assert.EqualValues(1, event166.LoveBillboardNum)
	// 飘屏告白，正确减少
	ok, event166, err = DecreaseEvent166ShowLoveNum(ctx, userID, TypeNotifyShowLove)
	require.NoError(err)
	assert.True(ok)
	assert.Zero(event166.LoveBillboardNum)
	// 飘屏告白，点数不够
	ok, event166, err = DecreaseEvent166ShowLoveNum(ctx, userID, TypeNormalShowLove)
	require.NoError(err)
	assert.False(ok)
	assert.Zero(event166.LoveBillboardNum)
	// 普通告白，无限告白
	require.NoError(GeneralSetOne(bson.M{"user_id": 3456835},
		bson.M{"user_id": userID, "event_166": Event166Status{SweetMachineNum: -1}}))
	ok, event166, err = DecreaseEvent166ShowLoveNum(ctx, userID, TypeNormalShowLove)
	require.NoError(err)
	assert.True(ok)
	assert.Zero(event166.LoveBillboardNum)
	assert.EqualValues(-1, event166.SweetMachineNum)
}
