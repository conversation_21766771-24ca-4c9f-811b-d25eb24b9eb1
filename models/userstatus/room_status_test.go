package userstatus

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/service"
)

/*
TestEnableRankInvisible 有下列测试的初始化
TestListRankInvisibleRoomID
TestDisableRankInvisible
*/

func TestEnableRankInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := collectionLiveUsers()
	filter := bson.M{"user_id": 1234, "room_id": testRoomID}
	_, err := col.DeleteMany(ctx, filter)

	require.NoError(EnableRankInvisible(1234, testRoom.RoomID, testRoom.ID))

	require.NoError(err)
	filter["rank_invisible"] = true
	assert.NoError(col.FindOne(ctx, filter).Err())
}

func TestListRankInvisibleRoomID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, pa, err := ListRankInvisibleRoomID(1234, 1, 20)
	require.NoError(err)
	assert.Equal(int64(len(res)), pa.Count)
	assert.Equal([]int64{testRoomID}, res)

	res, pa, err = ListRankInvisibleRoomID(1234, 2, 20)
	require.NoError(err)
	assert.Equal(int64(1), pa.Count)
	assert.Equal([]int64{}, res)
}

func TestIsRankInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	assert.False(IsRankInvisible(1234, 12345, true))
	key := KeyRankInvisible(12345)
	err := service.LRURedis.Get(key).Err()
	assert.Equal(redis.Nil, err)
	require.NoError(service.LRURedis.Set(key, "[1234]", 5*time.Second).Err())
	assert.True(IsRankInvisible(1234, 12345, false))
	// TODO: 查询结果打印出来和数据库一致，但是目前没有详细测试
}

func TestRankInvisibleUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s1 := RankInvisibleUsers(123456)
	key := KeyRankInvisible(123456)
	userIDs, err := loadRankInvisibleFormCache(key)
	require.NoError(err)
	require.NotNil(userIDs)
	assert.Equal(len(s1), len(userIDs))
	s2 := map[int64]struct{}{-123: {}}
	require.NoError(service.LRURedis.Set(key, "[-123]", 5*time.Second).Err())
	s3 := RankInvisibleUsers(123456)
	assert.Equal(s3, s2)
}

func TestDisableRankInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(DisableRankInvisible(1234, []int64{testRoomID, 123456}))

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := collectionLiveUsers()
	filter := bson.M{"user_id": 1234, "room_id": 123456}
	assert.Equal(mongo.ErrNoDocuments, col.FindOne(ctx, filter).Err())

	var res struct {
		RankInvisible bool `bson:"rank_invisible"`
	}
	filter["room_id"] = testRoomID
	require.NoError(col.FindOne(ctx, filter).Decode(&res))
	assert.False(res.RankInvisible)
}
