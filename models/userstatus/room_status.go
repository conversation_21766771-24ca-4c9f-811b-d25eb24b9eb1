package userstatus

import (
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func collectionLiveUsers() *mongo.Collection {
	return service.MongoDB.Collection("live_users")
}

// ListRankInvisibleRoomID 列出排行榜隐身的用户
func ListRankInvisibleRoomID(userID, p, pageSize int64) ([]int64, goutil.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := collectionLiveUsers()
	filter := bson.M{"user_id": userID, "rank_invisible": true}
	count, err := col.CountDocuments(ctx, filter)
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []int64{}, pa, nil
	}
	var tmp []struct {
		RoomID int64 `bson:"room_id"`
	}
	cur, err := col.Find(ctx, filter,
		pa.SetFindOptions(nil).SetProjection(bson.M{"room_id": 1}))
	if err != nil {
		return nil, pa, err
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &tmp)
	if err != nil {
		return nil, pa, err
	}
	res := make([]int64, len(tmp))
	for i := range tmp {
		res[i] = tmp[i].RoomID
	}
	return res, pa, nil
}

// DisableRankInvisible 取消榜单隐身
func DisableRankInvisible(userID int64, roomIDs []int64) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := collectionLiveUsers()
	filter := bson.M{"user_id": userID, "room_id": bson.M{"$in": roomIDs}}
	set := bson.M{"rank_invisible": false}
	_, err := col.UpdateMany(ctx, filter, bson.M{"$set": set}, options.Update())
	if err != nil {
		return err
	}
	for i := range roomIDs {
		err = clearRankInvisibleCache(KeyRankInvisible(roomIDs[i]))
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// EnableRankInvisible 开启榜单隐身
func EnableRankInvisible(userID int64, roomID int64, roomOID primitive.ObjectID) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := collectionLiveUsers()
	filter := bson.M{"user_id": userID, "room_id": roomID}
	set := bson.M{
		"_room_id":       roomOID,
		"room_id":        roomID,
		"user_id":        userID,
		"rank_invisible": true,
	}
	_, err := col.UpdateOne(ctx, filter, bson.M{"$set": set},
		options.Update().SetUpsert(true))
	if err != nil {
		return err
	}
	err = clearRankInvisibleCache(KeyRankInvisible(roomID))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

// IsRankInvisible 是否榜单隐身
// current: 是否实时，对于用户自己的隐身状态（比如真爱榜、粉丝榜），应该查询实时状态
func IsRankInvisible(userID int64, roomID int64, current bool) bool {
	if !current {
		v := RankInvisibleUsers(roomID)
		_, ok := v[userID]
		return ok
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := collectionLiveUsers()
	filter := bson.M{"user_id": userID, "room_id": roomID, "rank_invisible": true}
	err := col.FindOne(ctx, filter, options.FindOne().SetReturnKey(true)).Err()
	if err != nil {
		if err != mongo.ErrNoDocuments {
			logger.Error(err)
			// PASS
		}
		return false
	}
	return true
}

// KeyRankInvisible 榜单隐身超时缓存 key
func KeyRankInvisible(roomID int64) string {
	return keys.KeyRankInvisible1.Format(roomID)
}

// RankInvisibleUsers 榜单隐身的用户
func RankInvisibleUsers(roomID int64) map[int64]struct{} {
	key := KeyRankInvisible(roomID)
	userIDs, err := loadRankInvisibleFormCache(key)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if userIDs != nil {
		res := make(map[int64]struct{}, len(userIDs))
		for i := range userIDs {
			res[userIDs[i]] = struct{}{}
		}
		return res
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := collectionLiveUsers()
	filter := bson.M{"room_id": roomID, "rank_invisible": true}
	var rec []struct {
		UserID int64 `bson:"user_id"`
	}
	cur, err := col.Find(ctx, filter, options.Find().SetProjection(bson.M{"user_id": 1}))
	if err != nil {
		logger.Error(err)
		return map[int64]struct{}{}
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &rec)
	if err != nil {
		logger.Error(err)
		return map[int64]struct{}{}
	}
	userIDs = make([]int64, len(rec))
	for i := range userIDs {
		userIDs[i] = rec[i].UserID
	}
	uvMap, err := vip.MapUsersInfo(userIDs, nil, nil)
	if err != nil {
		logger.Error(err)
		return map[int64]struct{}{}
	}
	res := make(map[int64]struct{}, len(uvMap))
	userIDs = make([]int64, 0, len(uvMap))
	for userID, v := range uvMap {
		if vip.UserHavePrivilege(v, vip.PrivilegeRankInvisible) {
			res[userID] = struct{}{}
			userIDs = append(userIDs, userID)
		}
	}
	err = saveRankInvisibleCache(key, userIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return res
}

func loadRankInvisibleFormCache(key string) ([]int64, error) {
	v, err := service.LRURedis.Get(key).Result()
	if err != nil {
		if err == redis.Nil {
			err = nil
		}
		return nil, err
	}
	var userIDs []int64
	err = json.Unmarshal([]byte(v), &userIDs)
	if err != nil {
		return nil, err
	}
	return userIDs, nil
}

func saveRankInvisibleCache(key string, userIDs []int64) error {
	v, err := json.Marshal(userIDs)
	if err != nil {
		return err
	}
	err = service.LRURedis.Set(key, string(v), 10*time.Minute).Err()
	return err
}

func clearRankInvisibleCache(key string) error {
	err := service.LRURedis.Del(key).Err()
	if err != nil && err != redis.Nil {
		return err
	}
	return nil
}
