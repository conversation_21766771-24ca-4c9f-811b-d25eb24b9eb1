package category

import (
	"testing"
	"time"

	cache "github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestFindMany(t *testing.T) {
	assert := assert.New(t)
	limits := [4]int{4, 4, 0, 8}
	lens := [4]int{4, 4, 6, 6}
	res := [4]([]*Category){}
	var err error
	for i := 0; i < len(limits); i++ {
		res[i], err = FindMany(limits[i])
		assert.NoError(err)
		require.Equal(t, lens[i], len(res[i]))
	}
	assert.Equal("电台", res[3][0].Name)
	assert.Equal("音乐", res[3][1].Name)
	assert.Equal("催眠", res[3][2].Name)
	assert.Equal("广播剧", res[3][3].Name)
	assert.Equal("听书", res[3][4].Name)
	assert.Equal("其他", res[3][5].Name)
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	all, err := FindMany(0)
	require.NoError(t, err)
	holder = cache.New(5*time.Minute, 10*time.Minute)
	ids := [2]primitive.ObjectID{all[0].OID, all[5].OID}
	res := [2]*Category{all[0], all[5]}
	r, err := Find(primitive.NewObjectIDFromTimestamp(goutil.TimeNow()))
	assert.Nil(r)
	assert.NoError(err)
	for i := 0; i < len(ids); i++ {
		r, err = Find(ids[i])
		assert.NoError(err)
		assert.Equal(*res[i], *r)
	}
}

func TestCopy(t *testing.T) {
	assert := assert.New(t)
	old := &Category{Sort: 123}
	new := copy(old)
	assert.Equal(*old, *new)
	new.Sort = 456
	assert.NotEqual(old.Sort, new.Sort)
}

func TestCopySlice(t *testing.T) {
	assert := assert.New(t)
	old := []*Category{{Sort: 123}, {Sort: 456}}
	new := copySlice(old)
	require.Equal(t, len(old), len(new))
	for i := 0; i < len(old); i++ {
		assert.Equal(*old[i], *new[i])
		new[i].Sort = new[i].Sort + 1
		assert.NotEqual(old[i].Sort, new[i].Sort)
	}
}
