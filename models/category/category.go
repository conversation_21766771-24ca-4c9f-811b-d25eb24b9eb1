// Package category 项目中无用
package category

import (
	"time"

	cache "github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

// CollectionName collection name without prefix
const CollectionName = "categories"

var (
	holder     = cache.New(5*time.Minute, 10*time.Minute)
	findAllOpt = options.Find().SetSort(bson.M{"sort": -1})
)

// Category document in collection categories
type Category struct {
	OID       primitive.ObjectID `bson:"_id" json:"_id"`
	Sort      int32              `bson:"sort" json:"sort"`
	ShortName string             `bson:"short_name" json:"short_name"`
	// TODO: 改成 *string 还是继续使用 []string
	SuggestTags []string  `bson:"suggest_tags" json:"suggest_tags"`
	Name        string    `bson:"name" json:"name"`
	NameClean   string    `bson:"name_clean" json:"-"`
	Desciption  string    `bson:"desciption" json:"desciption"`
	CreatedTime time.Time `bson:"created_time" json:"-"`
	UpdatedTime time.Time `bson:"updated_time" json:"-"`
}

// FindMany 返回根据 Category.Sort 递减排序的 category 数组
// limit <= 0 则返回全部
func FindMany(limit int) ([]*Category, error) {
	v, ok := holder.Get("all")
	if ok {
		res := v.([]*Category)
		if limit > 0 && limit <= len(res) {
			res = res[0:limit]
		}
		return copySlice(res), nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	cur, err := collection.Find(ctx, bson.M{}, findAllOpt)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	res := make([]*Category, 0, 6)
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	// 将查询到的结果存入缓存
	holder.Set("all", res, 0)
	if limit > 0 && limit <= len(res) {
		res = res[0:limit]
	}
	return copySlice(res), nil
}

// Find find cagegory
func Find(id primitive.ObjectID) (*Category, error) {
	v, ok := holder.Get(id.Hex())
	if ok {
		return copy(v.(*Category)), nil
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	c := new(Category)
	err := collection.FindOne(ctx, bson.M{"_id": id}).Decode(c)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			err = nil
		}
		return nil, err
	}
	holder.Set(id.Hex(), c, 0)
	return copy(c), err
}

func copy(old *Category) *Category {
	if old == nil {
		return nil
	}
	new := new(Category)
	*new = *old
	return new
}

func copySlice(old []*Category) []*Category {
	new := make([]*Category, len(old))
	for i := 0; i < len(old); i++ {
		new[i] = copy(old[i])
	}
	return new
}
