package models

import (
	"encoding/json"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// message type
const (
	MessageTypeNormal = iota
	MessageTypeNotify
)

// 消息状态
const (
	MessageStatusIgnore  = iota - 2 // 忽略（不存储到 mongodb 中）
	MessageStatusFaked              // 假发送
	MessageStatusNormal             // 正常
	MessageStatusLimited            // 限流
)

// Sticker 表情
type Sticker struct {
	PackageID int64 `bson:"package_id" json:"-"` // 表情包 ID
	StickerID int64 `bson:"sticker_id" json:"-"` // 表情 ID
}

// Danmaku 付费弹幕
type Danmaku struct {
	OrderID int64          `bson:"order_id" json:"-"`
	Bubble  *bubble.Simple `bson:"bubble" json:"-"`
}

// Message 实现了 mongodb 查找的单条 messages 信息的结构
type Message struct {
	OID        primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	RoomOID    primitive.ObjectID `bson:"_room_id" json:"-"`
	RoomID     int64              `bson:"room_id" json:"room_id"`
	MsgID      string             `bson:"msg_id" json:"msg_id"`
	Status     int                `bson:"status" json:"status"`
	UserID     int64              `bson:"user_id" json:"user_id"`
	Message    string             `bson:"message" json:"message"`
	Bubble     *bubble.Simple     `bson:"bubble" json:"bubble,omitempty"`
	Sticker    *Sticker           `bson:"sticker,omitempty" json:"-"`
	Danmaku    *Danmaku           `bson:"danmaku,omitempty" json:"-"`
	IP         string             `bson:"ip" json:"-"`
	CreateTime time.Time          `bson:"create_time" json:"create_time"`
}

// MarshalJSON implements json.MarshalJSON.
func (m Message) MarshalJSON() ([]byte, error) {
	type Alias Message
	return json.Marshal(&struct {
		*Alias
		Stamp int64 `json:"create_time"`
	}{
		Alias: (*Alias)(&m),
		Stamp: util.TimeToUnixMilli(m.CreateTime),
	})
}

// MessageCollection 返回 messages 集合
func MessageCollection() *mongo.Collection {
	return service.MongoDB.Collection("messages")
}

// Insert 将 m 插入 mongodb，插入成功会更新 ID_
func (m *Message) Insert() (*mongo.InsertOneResult, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	if m.CreateTime.IsZero() {
		m.CreateTime = goutil.TimeNow()
	}
	res, err := MessageCollection().InsertOne(ctx, m)
	if err != nil {
		return nil, err
	}
	objectID, ok := res.InsertedID.(primitive.ObjectID)
	if !ok {
		return nil, errors.New("cannot find _id")
	}
	m.OID = objectID
	return res, nil
}
