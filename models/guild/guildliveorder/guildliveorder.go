package guildliveorder

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
)

/*
CREATE TABLE `guild_live_order` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `live_id` int(10) unsigned NOT NULL COMMENT '主播 ID',
  `guild_id` int(10) unsigned NOT NULL COMMENT '公会 ID',
  `status` tinyint(4) NOT NULL COMMENT '状态（1 成功，0 创建，-1 取消，-2 错误）',
  `type` tinyint(4) NOT NULL COMMENT '平台（同 recharge_order.type 字段）',
  `price` bigint NOT NULL COMMENT '金额（单位：分）',
  `tid` varchar(32) NOT NULL COMMENT '平台订单号',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int(10) unsigned NOT NULL COMMENT '修改时间',
  `applyment_id` int(10) unsigned NOT NULL COMMENT '合约申请 ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公会与主播订单表';
*/

const tableName = "guild_live_order"

// DB the db instance of GuildLiveOrder model
func (GuildLiveOrder) DB() *gorm.DB {
	return service.PayDB.Table(GuildLiveOrder{}.TableName())
}

// TableName table name for GuildLiveOrder model
func TableName() string {
	return tableName
}

// TableName table name for GuildLiveOrder model
func (GuildLiveOrder) TableName() string {
	return TableName()
}

// GuildLiveOrder 公会与主播订单表
type GuildLiveOrder struct {
	ID int64 `gorm:"column:id;primary_key" json:"id"`

	LiveID      int64 `gorm:"column:live_id" json:"live_id"`
	GuildID     int64 `gorm:"column:guild_id" json:"guild_id"`
	ApplymentID int64 `gorm:"column:applyment_id" json:"applyment_id"`

	Type   int    `gorm:"column:type" json:"type"`
	Status int64  `gorm:"column:status" json:"status"`
	Price  int64  `gorm:"column:price" json:"price"`
	TID    string `gorm:"column:tid" json:"tid"`

	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
}
