package contractapplyment

import (
	"encoding/json"
	"fmt"
	"math"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

/*
CREATE TABLE `guild_live_contract_applyment` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `live_id` int(10) unsigned NOT NULL COMMENT '主播 ID',
  `guild_id` int(10) unsigned NOT NULL COMMENT '公会 ID',
  `guild_name` varchar(50) NOT NULL DEFAULT '' COMMENT '公会名称',
  `contract_id` int(10) unsigned NOT NULL COMMENT '合同 ID',
  `contract_expire_time` int(10) unsigned NOT NULL COMMENT '合同过期时间',
  `type` tinyint(4) NOT NULL COMMENT '1.主播申请签约； 2. 公会申请签约；
									  3.主播申请续约； 4. 公会申请续约；
                                	  5.协商解约 6. 公会清退
                             		  7.强制解约',
  `contract_duration` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '合约时长，1. 6 个月；2. 1 年；3. 2 年',
  `rate` int(10) unsigned NOT NULL DEFAULT '45' COMMENT '分成比例或违约金额（分）',
  `expire_time` int(10) unsigned NOT NULL COMMENT '失效或生效时间。
    签约：当前时间 + 7 * 24 小时
    续约时间：合同到期时间
    协商解约时间： 当前时间 + 7 * 24 小时
    公会清退：当前时间 + 48 小时
    强制解约：统一写当前时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态
    -3 已失效（不含时间超时失效），
    -2 已撤回，
    -1 被对方拒绝，
    0 待处理，
    1 被对方同意',
  `initiator` tinyint(4) NOT NULL COMMENT '发起方（1 主播发起，2 公会发起）',
  `agent_id` bigint NOT NULL DEFAULT 0 COMMENT '经纪人 ID',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int(10) unsigned NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `IDX_GUILD_ID` (`guild_id`) USING BTREE,
  KEY `IDX_LIVE_ID` (`live_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公公会与主播合约申请表';
*/

const tableName = "guild_live_contract_applyment"

// DB the db instance of ContractApplyment model
func (ca ContractApplyment) DB() *gorm.DB {
	return service.DB.Table(TableName())
}

// TableName table name
func TableName() string {
	return tableName
}

// TableName table name for ContractApplyment model
func (ContractApplyment) TableName() string {
	return TableName()
}

// ContractApplyment 公公会与主播合约申请表
type ContractApplyment struct {
	ID int64 `gorm:"column:id;primary_key" json:"id"`

	LiveID    int64  `gorm:"column:live_id" json:"live_id"`
	GuildID   int64  `gorm:"column:guild_id" json:"guild_id"`
	GuildName string `gorm:"column:guild_name" json:"guild_name"`
	AgentID   int64  `gorm:"column:agent_id" json:"agent_id"`

	ContractID         int64 `gorm:"column:contract_id" json:"-"`
	ContractDuration   int64 `gorm:"column:contract_duration" json:"-"`
	ContractExpireTime int64 `gorm:"column:contract_expire_time" json:"contract_expire_time"`

	Type        int64 `gorm:"column:type" json:"type"`
	Status      int64 `gorm:"column:status" json:"status"`
	ExpireTime  int64 `gorm:"column:expire_time" json:"expire_time"`
	ProcessTime int64 `gorm:"column:process_time" json:"process_time"` // 合约处理时间
	Initiator   int   `gorm:"column:initiator" json:"initiator"`
	Rate        int   `gorm:"column:rate" json:"rate"`

	More string `gorm:"column:more" json:"-"`

	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
}

// More 合约申请的附加内容
type More struct {
	Attr *goutil.BitMask `json:"attr,omitempty"` // 主播发起协商解约申请，attr 表示发起申请前合约表的 attr 值
	Rate *int            `json:"rate,omitempty"` // 公会修改主播最低分成比例，rate 表示发起申请前合约表的 rate 值
}

const (
	// AgreeEditRate 主播同意修改最低分成比例
	AgreeEditRate = iota
	// DisagreeEditRate 主播拒绝修改最低分成比例
	DisagreeEditRate
)

// BeforeCreate automatically set field modified_time
func (ca *ContractApplyment) BeforeCreate(scope *gorm.Scope) (err error) {
	nowStamp := goutil.TimeNow().Unix()
	err = scope.SetColumn("create_time", nowStamp)
	if nil == err {
		err = scope.SetColumn("modified_time", nowStamp)
	}
	return err
}

// BeforeUpdate automatically update field modified_time
func (ca *ContractApplyment) BeforeUpdate(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", goutil.TimeNow().Unix())
}

// IsTimeToRevoke checks whether current time can revoke applyment
func (ca *ContractApplyment) IsTimeToRevoke() bool {
	return IsTimeToRevoke(ca.Type, ca.CreateTime, ca.ExpireTime)
}

// IsTimeToRevoke checks whether current time can revoke applyment
func IsTimeToRevoke(applymentType int64, applyAt int64, expireTime int64) (isRevokable bool) {
	nowStamp := goutil.TimeNow().Unix()
	if nowStamp >= expireTime {
		return false
	}

	switch applymentType {
	case TypeLiveSign:
		isRevokable = nowStamp-applyAt >= RevokeStartTimeLiveSign
	case TypeLiveRenew:
		isRevokable = nowStamp-applyAt >= RevokeStartTimeLiveRenew
	case TypeLiveTerminate:
		isRevokable = nowStamp-applyAt < RevokeStartTimeLiveTerminate
	case TypeLiveTerminateForcely:
		// PASS
	case TypeGuildSign:
		isRevokable = true
	case TypeGuildRenew:
		isRevokable = true
	case TypeGuildExpel:
		isRevokable = true
	case TypePlatformExpel:
		isRevokable = false
	}

	return
}

// IsPending 申请是否是待处理的
func (ca ContractApplyment) IsPending() bool {
	return ca.Status == StatusPending && ca.ExpireTime > goutil.TimeNow().Unix()
}

// IsExpired checks whether the applyment is expired
func (ca *ContractApplyment) IsExpired() bool {
	return ca.Status == StatusPending && goutil.TimeNow().Unix() >= ca.ExpireTime
}

// IsNewContractDuration 当前时间是否开放使用新的签约/续约时长
func IsNewContractDuration() bool {
	var openNewContractDurationTime int64
	config.GetAB("open_new_contract_duration_time", &openNewContractDurationTime)
	return goutil.TimeNow().Unix() >= openNewContractDurationTime
}

// IsLegalContractDuration check whether contract duration is legal
func IsLegalContractDuration(duration int64) bool {
	if IsNewContractDuration() {
		switch duration {
		case ContractDurationThreeYear, ContractDurationFiveYear:
			return true
		}
		return false
	}
	switch duration {
	case ContractDurationOneYear, ContractDurationTwoYear:
		return true
	}
	return false
}

// GetContractEnd get expired end time
func GetContractEnd(start time.Time, duration int64) (end time.Time) {
	y, m, d := start.Date()
	switch duration {
	case ContractDurationSixMonths:
		end = time.Date(y, m+6, d, 0, 0, 0, 0, start.Location())
	case ContractDurationOneYear:
		end = time.Date(y+1, m, d, 0, 0, 0, 0, start.Location())
	case ContractDurationTwoYear:
		end = time.Date(y+2, m, d, 0, 0, 0, 0, start.Location())
	case ContractDurationThreeYear:
		end = time.Date(y+3, m, d, 0, 0, 0, 0, start.Location())
	case ContractDurationFiveYear:
		end = time.Date(y+5, m, d, 0, 0, 0, 0, start.Location())
	default:
		panic(fmt.Sprintf("Invalid contract duration param: %d", duration))
	}
	return
}

// GetLeftTimeString get formated left time
func GetLeftTimeString(leftDuration time.Duration) (leftTime string) {
	if leftHours := leftDuration.Hours(); leftHours > 0 {
		if leftHours > 24 {
			leftTime = fmt.Sprintf("%d 天", int(leftHours)/24)
		} else if leftHours >= 1 {
			leftTime = fmt.Sprintf("%.0f 小时", math.Floor(leftHours))
		} else {
			leftMinutes := leftDuration.Minutes()
			if leftMinutes >= 1 {
				leftTime = fmt.Sprintf("%.0f 分钟", math.Floor(leftMinutes))
			} else {
				leftTime = fmt.Sprintf("%.0f 秒", math.Floor(leftDuration.Seconds()))
			}
		}
	}
	return
}

// FindByID 通过 ContractApplyment.ID 获取申请
func FindByID(id int64) (*ContractApplyment, error) {
	ca := new(ContractApplyment)
	err := service.DB.Find(ca, "id = ?", id).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return ca, nil
}

// ExpireTime 计算截止日期
func ExpireTime(when time.Time, caType int64) int64 {
	y, m, d := when.Date()
	switch caType {
	case TypeLiveSign, TypeGuildSign:
		return when.AddDate(0, 0, ExpireDaysSignApplyment).Unix()
	case TypeLiveTerminate:
		return when.AddDate(0, 0, ExpireDaysTerminateApplyment).Unix()
	case TypeGuildExpel:
		return time.Date(y, m, d+ExpireDaysExpelApplyment, 0, 0, 0, 0, when.Location()).Unix()
	case TypeRateDown:
		return time.Date(y, m, d+ExpireDaysRateDown, 0, 0, 0, 0, when.Location()).Unix()
	default:
		panic(fmt.Errorf("unsupported caType: %d", caType))
	}
}

// FormatContractDuration 获取签约时长文字描述
func FormatContractDuration(ca *ContractApplyment) string {
	durationStr, ok := DurationLabelMap[ca.ContractDuration]
	if ok {
		return durationStr
	}
	logger.WithField("id", ca.ID).Warnf("contrate_duration unsupported: %d", ca.ContractDuration)
	// 对于未设置签约时长的情况（早期签约，现在都已解约），返回“-”
	return "-"
}

// UnmarshalMore 解析 More 的 JSON 结构
func (ca ContractApplyment) UnmarshalMore() (*More, error) {
	if ca.More == "" {
		return nil, nil
	}
	var more More
	err := json.Unmarshal([]byte(ca.More), &more)
	if err != nil {
		return nil, err
	}
	return &more, nil
}

// ApplicationEditRateInvalid 公会申请降薪失效
func ApplicationEditRateInvalid(guildID, creatorID int64, tx *gorm.DB) error {
	now := goutil.TimeNow()
	return tx.Table(tableName).Where("guild_id = ? AND live_id = ? AND type = ? AND status = ?",
		guildID, creatorID, TypeRateDown, StatusPending).Updates(map[string]interface{}{
		"status":        StatusInvalid,
		"process_time":  now.Unix(),
		"modified_time": now.Unix(),
	}).Error
}
