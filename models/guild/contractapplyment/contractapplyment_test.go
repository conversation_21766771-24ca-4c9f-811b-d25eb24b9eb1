package contractapplyment

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("guild_live_contract_applyment", TableName())
	assert.Equal("guild_live_contract_applyment", ContractApplyment{}.TableName())
}

func TestContractApplymentTagKeys(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.GORM).
		Check(ContractApplyment{}, "id", "live_id", "guild_id", "guild_name", "agent_id",
			"contract_id", "contract_duration", "contract_expire_time",
			"type", "status", "expire_time", "process_time", "initiator", "initiator", "rate", "more",
			"create_time", "modified_time")

	tutil.NewKeyChecker(t, tutil.JSON).
		Check(ContractApplyment{}, "id", "live_id", "guild_id", "guild_name", "agent_id",
			"contract_expire_time", "type", "status", "expire_time", "process_time", "initiator", "initiator", "rate",
			"create_time")
}

func TestIsTimeToRevoke(t *testing.T) {
	assert := assert.New(t)
	tm := goutil.TimeNow()

	ca := new(ContractApplyment)
	// 已过期失效
	ca.ExpireTime = tm.AddDate(0, 0, -5).Unix()
	subTestIsTimeToRevoke(assert, ca, TypeLiveSign, tm.Unix(), false)

	// 未过期
	ca.ExpireTime = tm.AddDate(0, 0, 7).Unix()

	// 主播签约申请
	subTestIsTimeToRevoke(assert, ca, TypeLiveSign, tm.Unix(), false)
	subTestIsTimeToRevoke(assert, ca, TypeLiveSign, tm.AddDate(0, 0, -2).Unix(), true)

	// 主播续约申请
	subTestIsTimeToRevoke(assert, ca, TypeLiveRenew, tm.Unix(), false)
	subTestIsTimeToRevoke(assert, ca, TypeLiveRenew, tm.AddDate(0, 0, -2).Unix(), true)

	// 主播协商解约申请
	subTestIsTimeToRevoke(assert, ca, TypeLiveTerminate, tm.AddDate(0, 0, -5).Unix(), false)
	subTestIsTimeToRevoke(assert, ca, TypeLiveTerminate, tm.AddDate(0, 0, -2).Unix(), true)

	// 主播强制解约
	subTestIsTimeToRevoke(assert, ca, TypeLiveTerminateForcely, tm.Unix(), false)

	// 公会邀请签约
	subTestIsTimeToRevoke(assert, ca, TypeGuildSign, tm.Unix(), true)
	// 公会邀请续约
	subTestIsTimeToRevoke(assert, ca, TypeGuildRenew, tm.Unix(), true)
	// 公会清退
	subTestIsTimeToRevoke(assert, ca, TypeGuildExpel, tm.Unix(), true)
	// 平台清退
	subTestIsTimeToRevoke(assert, ca, TypePlatformExpel, tm.Unix(), false)
}

func subTestIsTimeToRevoke(assert *assert.Assertions, applyment *ContractApplyment, applymentType int64, applyAt int64, expected bool) {
	applyment.Type = applymentType
	applyment.CreateTime = applyAt
	isRevokable := applyment.IsTimeToRevoke()

	assert.Equal(expected, isRevokable)
}

func TestGetContractEnd(t *testing.T) {
	assert := assert.New(t)

	timeString := "2020-01-03 13:24:41"
	loc, _ := time.LoadLocation("Local")
	tm, err := time.ParseInLocation("2006-01-02 15:04:05", timeString, loc)
	assert.NoError(err)

	contractEnd := GetContractEnd(tm, ContractDurationSixMonths)
	contractEndString := contractEnd.Format("2006-01-02 15:04:05")
	assert.Equal("2020-07-03 00:00:00", contractEndString)

	contractEnd = GetContractEnd(tm, ContractDurationOneYear)
	contractEndString = contractEnd.Format("2006-01-02 15:04:05")
	assert.Equal("2021-01-03 00:00:00", contractEndString)

	contractEnd = GetContractEnd(tm, ContractDurationTwoYear)
	contractEndString = contractEnd.Format("2006-01-02 15:04:05")
	assert.Equal("2022-01-03 00:00:00", contractEndString)

	contractEnd = GetContractEnd(tm, ContractDurationThreeYear)
	contractEndString = contractEnd.Format("2006-01-02 15:04:05")
	assert.Equal("2023-01-03 00:00:00", contractEndString)

	contractEnd = GetContractEnd(tm, ContractDurationFiveYear)
	contractEndString = contractEnd.Format("2006-01-02 15:04:05")
	assert.Equal("2025-01-03 00:00:00", contractEndString)
}

func TestIsNewContractDuration(t *testing.T) {
	assert := assert.New(t)

	nowTimestamp := goutil.TimeNow().Unix()
	config.Conf.AB["open_new_contract_duration_time"] = nowTimestamp + 1
	defer delete(config.Conf.AB, "open_new_contract_duration_time")
	assert.False(IsNewContractDuration())

	config.Conf.AB["open_new_contract_duration_time"] = nowTimestamp
	assert.True(IsNewContractDuration())
}

func TestIsLegalContractDuration(t *testing.T) {
	assert := assert.New(t)

	nowTimestamp := goutil.TimeNow().Unix()
	config.Conf.AB["open_new_contract_duration_time"] = nowTimestamp + 1
	defer delete(config.Conf.AB, "open_new_contract_duration_time")
	assert.False(IsLegalContractDuration(ContractDurationSixMonths))
	assert.True(IsLegalContractDuration(ContractDurationOneYear))
	assert.True(IsLegalContractDuration(ContractDurationTwoYear))
	assert.False(IsLegalContractDuration(99999))

	config.Conf.AB["open_new_contract_duration_time"] = nowTimestamp
	assert.False(IsLegalContractDuration(ContractDurationOneYear))
	assert.False(IsLegalContractDuration(ContractDurationTwoYear))
	assert.True(IsLegalContractDuration(ContractDurationThreeYear))
	assert.True(IsLegalContractDuration(ContractDurationFiveYear))
}

func TestIsExpired(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	applyment := new(ContractApplyment)
	applyment.Status = StatusInvalid
	applyment.ExpireTime = now.AddDate(0, 0, -3).Unix()
	assert.False(applyment.IsExpired())

	applyment.Status = StatusPending
	assert.True(applyment.IsExpired())

	applyment.ExpireTime = now.AddDate(0, 0, 3).Unix()
	assert.False(applyment.IsExpired())
}

func TestFormatContractDuration(t *testing.T) {
	assert := assert.New(t)
	ca := &ContractApplyment{ContractDuration: 0}
	assert.Equal("-", FormatContractDuration(ca))
	ca.ContractDuration = 1
	assert.Equal("6 个月", FormatContractDuration(ca))
	ca.ContractDuration = 2
	assert.Equal("12 个月", FormatContractDuration(ca))
	ca.ContractDuration = 3
	assert.Equal("24 个月", FormatContractDuration(ca))
	ca.ContractDuration = 4
	assert.Equal("3 年", FormatContractDuration(ca))
	ca.ContractDuration = 5
	assert.Equal("5 年", FormatContractDuration(ca))
}

func TestApplicationEditRateInvalid(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	application := ContractApplyment{
		LiveID:  2333,
		GuildID: 32222,
		Type:    TypeRateDown,
		Status:  StatusPending,
	}
	db := service.DB.Table(tableName)
	require.NoError(db.Create(&application).Error)
	defer func() {
		require.NoError(db.Delete("", application).Error)
	}()
	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	require.NoError(ApplicationEditRateInvalid(application.GuildID, application.LiveID, db))

	newApplication := ContractApplyment{}
	require.NoError(db.Where("id = ?", application.ID).First(&newApplication).Error)
	assert.Equal(StatusInvalid, newApplication.Status)
	assert.Equal(now.Unix(), newApplication.ProcessTime)
}
