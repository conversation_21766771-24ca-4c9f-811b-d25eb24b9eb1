package contractapplyment

// 合约申请有效期（天）
const (
	ExpireDaysSignApplyment      = 7 // 签约申请有效期（天）
	ExpireDaysTerminateApplyment = 5 // 协商解约有效期（天）
	ExpireDaysRateDown           = 7 // 降薪申请有效期（天）
)

// RenewDaysBeforeContractEnd 开始续约申请需距合约结束前的时长（天）
const RenewDaysBeforeContractEnd = 15

// ExpireDaysExpelApplyment 公会清退有效期（天）
const ExpireDaysExpelApplyment = 3

// 可撤回的时间起点（秒）
const (
	RevokeStartTimeLiveSign      = 24 * 3600 // 撤回主播签约申请，24 小时后
	RevokeStartTimeLiveRenew     = 24 * 3600 // 撤回主播续约申请，24 小时后
	RevokeStartTimeLiveTerminate = 72 * 3600 // 撤回主播协商解约申请，3 个自然日内
)

// 默认的结算方式及分成比例
const (
	SettlementDefault = "对公结算"
	RateTipDefault    = "45%"
)

// 合约申请发起方
const (
	InitiatorLive     = iota + 1 // 主播
	InitiatorGuild               // 公会
	InitiatorPlatform            // 平台
)

// 合约申请类型
const (
	TypeLiveSign             int64 = iota + 1 // 主播申请签约
	TypeGuildSign                             // 公会申请签约
	TypeLiveRenew                             // 主播申请续约
	TypeGuildRenew                            // 公会申请续约
	TypeLiveTerminate                         // 主播协商解约
	TypeGuildExpel                            // 公会清退主播
	TypeLiveTerminateForcely                  // 主播强制解约
	TypeRateUp                                // 公会申请对主播升薪
	TypeRateDown                              // 公会申请对主播降薪
	TypePlatformExpel                         // 平台清退主播
)

// 合约时长
const (
	ContractDurationSixMonths int64 = iota + 1 // Deprecated: 六个月，该值对于新签订的合约或申请已不再使用
	ContractDurationOneYear                    // Deprecated: 十二个月，该值对于新签订的合约或申请已不再使用
	ContractDurationTwoYear                    // Deprecated: 二十四个月，该值对于新签订的合约或申请已不再使用
	ContractDurationThreeYear                  // 3 年
	ContractDurationFiveYear                   // 5 年
)

// 合约申请状态
const (
	StatusOutdated                int64 = iota - 4 // 超时未处理被系统拒绝
	StatusInvalid                                  // 已失效
	StatusRevoked                                  // 已撤回
	StatusDeclined                                 // 被对方拒绝
	StatusPending                                  // 待处理
	StatusAgreed                                   // 被对方同意
	StatusTerminateForcelySuccess                  // 强制解约时已生效状态, 只在主播后台用于计算值
)

var (
	// DurationLabelMap 合约时长标识
	DurationLabelMap = map[int64]string{
		ContractDurationSixMonths: "6 个月",
		ContractDurationOneYear:   "12 个月",
		ContractDurationTwoYear:   "24 个月",
		ContractDurationThreeYear: "3 年",
		ContractDurationFiveYear:  "5 年",
	}
)
