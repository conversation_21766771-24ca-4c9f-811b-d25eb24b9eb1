package livecontract

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "guild_live_contract"

// DB the db instance of GuildAgentCreator model
func (lc LiveContract) DB() *gorm.DB {
	return service.DB.Table(lc.TableName())
}

// TableName table name
func TableName() string {
	return tableName
}

// TableName table name
func (LiveContract) TableName() string {
	return tableName
}

// ADB the analytic db instance of LiveContract model
func ADB(tableAlias ...string) *gorm.DB {
	tableName := TableName()
	if len(tableAlias) > 0 {
		tableName = tableName + " AS " + tableAlias[0]
	}
	if servicedb.Driver == servicedb.DriverSqlite {
		// 单元测试的 sqlite 不用指定库名
		return service.NewADB.Table(tableName)
	}
	if goutil.IsProdEnv() {
		// 线上环境使用新 ADB 的 live.guild_live_contract 表
		tableName = "live." + tableName
	} else {
		// UAT 环境使用 app_missevan.guild_live_contract 表
		tableName = config.Conf.Service.DB.Name + "." + tableName
	}

	return service.NewADB.Table(tableName)
}

// LiveContract 公会与主播关系表
type LiveContract struct {
	ID               int64          `gorm:"column:id;primary_key" json:"id"`
	GuildID          int64          `gorm:"column:guild_id" json:"guild_id"`
	GuildOwner       int64          `gorm:"column:guild_owner" json:"guild_owner"`
	GuildName        string         `gorm:"column:guild_name" json:"guild_name"`
	LiveID           int64          `gorm:"column:live_id" json:"live_id"` // 主播 ID
	ContractDuration int64          `gorm:"column:contract_duration" json:"contract_duration"`
	ContractStart    int64          `gorm:"column:contract_start" json:"contract_start"`
	ContractEnd      int64          `gorm:"column:contract_end" json:"contract_end"`
	Rate             int            `gorm:"column:rate" json:"rate"`
	KPI              string         `gorm:"column:kpi" json:"kpi"`
	Type             int64          `gorm:"column:type" json:"type"`
	Attr             goutil.BitMask `gorm:"column:attr" json:"attr"`
	Status           int64          `gorm:"column:status" json:"status"`
	CreateTime       int64          `gorm:"column:create_time" json:"create_time"` // 需要作为响应返回
	ModifiedTime     int64          `gorm:"column:modified_time" json:"-"`
}

// ReturnData for contract pagination data
type ReturnData struct {
	Data       []guild.Response  `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
}

// BeforeCreate automatically set field modified_time
func (l *LiveContract) BeforeCreate(scope *gorm.Scope) (err error) {
	err = scope.SetColumn("create_time", goutil.TimeNow().Unix())
	if nil == err {
		err = scope.SetColumn("modified_time", goutil.TimeNow().Unix())
	}
	return err
}

// BeforeUpdate automatically update field modified_time
func (l *LiveContract) BeforeUpdate(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", goutil.TimeNow().Unix())
}

// GetByPK 根据 ID 获取合约
func GetByPK(ID int64) (*LiveContract, error) {
	l := new(LiveContract)
	err := service.DB.First(l, ID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, err
	}
	return l, nil
}

// LiveCount 获取该主播某状态下的合约数量
func LiveCount(liveID, status int64) (int, error) {
	var count int
	err := service.DB.Table(TableName()).
		Where("live_id = ? and status = ?", liveID, status).
		Count(&count).Error
	return count, err
}

// HaveRight 查看用户是否有权限操作该合约
// TODO: 未考虑申请解约的情况
func (l *LiveContract) HaveRight(status, fromWho, userID int64) *handler.ActionError {
	now := goutil.TimeNow().Unix()
	if now >= l.ContractEnd {
		return actionerrors.ErrContractExpired
	}
	if l.Status != status {
		return actionerrors.ErrContractStatus
	}
	// REVIEW: 这个的判断是否多余？
	if l.Type != fromWho {
		return actionerrors.ErrNoAuthority
	}
	if l.Type == FromLiver && l.GuildOwner != userID {
		return actionerrors.ErrNoAuthority
	}
	if l.Type == FromGuild && l.LiveID != userID {
		return actionerrors.ErrNoAuthority
	}
	return nil
}

// UpdateOtherContractsFinished 使某主播其它所有合约状态失效
func UpdateOtherContractsFinished(liveID, ID int64) {
	table := service.DB.Table(TableName()).
		Where("live_id = ? AND status = ?", liveID, StatusUntreated)
	if ID != 0 {
		table = table.Not("id = ?", ID)
	}
	table.Update("status", StatusFinished)
}

// UpdateContract 修改合约状态
func (l *LiveContract) UpdateContract(status int64) bool {
	var updateAttrs = map[string]interface{}{
		"status": status,
	}
	// 合约变为生效
	if StatusUntreated == l.Status && status == StatusContracting {
		// 使其它未处理的合约失效
		UpdateOtherContractsFinished(l.LiveID, l.ID)
		// 合约由未处理变为生效时，记录签约时间及更新公会旗下人数
		updateAttrs["contract_start"] = goutil.TimeNow().Unix()
		if err := guild.IncreaseLiveNum(l.GuildID, 1); err != nil {
			logger.Error(err)
			// PASS
		}
	} else if (StatusContracting == l.Status || StatusTerminating == l.Status) && (StatusFinished == status || StatusUseless == status) {
		// 合约由生效状态变为失效状态，记录合约终止时间及公会旗下人数
		updateAttrs["contract_end"] = goutil.TimeNow().Unix()
		if err := guild.IncreaseLiveNum(l.GuildID, -1); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err := service.DB.Model(l).Updates(updateAttrs).Error
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// InviteOrJoinRecords provide the history of guild's invitation request or live anchor's join request
func (l LiveContract) InviteOrJoinRecords(whoLaunch int, liveID int64, page int64, pageSize int64, keyword string, searchType int, status int) (ReturnData, error) {
	var returnData ReturnData
	var records []guild.Response
	var totalCount int64

	db := service.DB.Table(guild.TableName()+" AS g").
		Select("g.id, g.name, g.intro, g.user_id, t.contract_start, t.contract_end, t.status, t.create_time").
		Where("g.checked = ? AND t.live_id = ?", guild.CheckedPass, liveID).
		Where("t.type = ?", whoLaunch).
		Joins("LEFT JOIN " + l.TableName() + " AS t ON t.guild_id = g.id")

	// status 默认取 10（不在 status 取值范围内，代表获取除已解约外的所有的状态）
	// 否则根据 status 值获取对应的状态记录
	if 10 > status {
		db = db.Where("t.status = ?", status)
	} else {
		// 不显示已解约的合约
		db = db.Not("t.status = ?", StatusUseless)
	}

	if keyword != "" {
		if searchType == 0 {
			// 按会长 ID 过滤
			ownerID, err := strconv.ParseInt(keyword, 10, 64)
			if nil != err {
				return returnData, actionerrors.ErrParams
			}
			db = db.Where("g.user_id = ?", ownerID)
		} else {
			// 按公会名称过滤
			db = db.Where("name LIKE ?", servicedb.ToLikeStr(keyword))
		}
	}

	db.Count(&totalCount)
	err := db.Order("t.id DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).Scan(&records).Error

	returnData.Data = records
	returnData.Pagination = goutil.MakePagination(totalCount, page, pageSize)

	return returnData, err
}

// CopeWithTerminatingContract for live anchor coping with guild's contract termination
func (l LiveContract) CopeWithTerminatingContract(guildID int64, userID int64, finalStatus int64) (bool, error) {
	c := service.DB.Model(&LiveContract{}).
		Where("live_id = ? AND guild_id = ? AND status = ? AND type = ?", userID, guildID, StatusTerminating, FromGuild).
		Update("status", finalStatus).RowsAffected
	if c == 0 {
		return false, actionerrors.ErrGuildNotTerminate
	}

	return true, nil
}

// IsInGuild 判断主播是否在公会中，guildID 如果不为 0，则判断其是否是该公会的成员
// 返回所在公会 ID
func IsInGuild(liveID int64, guildID int64) (int64, error) {
	lc, err := FindInContractingByLiveID(liveID, guildID, "guild_id")
	if err != nil || lc == nil {
		return 0, err
	}
	return lc.GuildID, nil
}

// GetGuildID 获取主播所属公会 ID
func GetGuildID(liveID int64) (guildID int64, err error) {
	return IsInGuild(liveID, 0)
}

// FindInContractingByLiveID 通过主播 ID 查找该主播签约中的合同
// 如果 guildID 不为 0，则查找属于主播和该公会之前签约中的合同
// selects 控制查找的字段，默认全部查找
func FindInContractingByLiveID(liveID, guildID int64, selects ...string) (*LiveContract, error) {
	if liveID <= 0 || guildID < 0 {
		return nil, nil
	}
	db := service.DB.Where("live_id = ? AND status = ? AND contract_end > ?",
		liveID, StatusContracting, goutil.TimeNow().Unix())
	if guildID > 0 {
		db = db.Where("guild_id = ?", guildID)
	}
	if selectStr := strings.Join(selects, ","); selectStr != "" {
		db = db.Select(selectStr)
	}
	lc := new(LiveContract)
	err := db.First(lc).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return lc, nil
}

// IsInProtecting 主播是否在保护期内
func IsInProtecting(contractStart int64) bool {
	y, m, d := time.Unix(contractStart, 0).Date()
	protectEnd := time.Date(y, m, d+ProtectedDaysOfFirstSign, 0, 0, 0, 0, time.Local)
	return goutil.TimeNow().Before(protectEnd)
}

// IsInProtectingGuildExpel 主播是否在公会清退保护期内
func IsInProtectingGuildExpel(contractStart int64) bool {
	y, m, d := time.Unix(contractStart, 0).Date()
	protectEnd := time.Date(y, m, d+ProtectedDaysOfGuildExpel, 0, 0, 0, 0, time.Local)
	return goutil.TimeNow().Before(protectEnd)
}

// FindGuildRecordLiveIDs 获取公会合约内、已解除和已过期的主播 ID 列表
func FindGuildRecordLiveIDs(guildID int64) ([]int64, error) {
	var liveIDs []int64
	err := service.DB.Model(&LiveContract{}).Where("guild_id = ? AND status IN (?)", guildID, []int64{
		StatusUseless, StatusFinished, StatusContracting,
	}).Pluck("DISTINCT live_id", &liveIDs).Error
	if err != nil {
		return nil, err
	}
	return liveIDs, nil
}

// AgreeExpelGuildLive 通过清退申请
func AgreeExpelGuildLive(guildID, liveID int64) error {
	nowStamp := goutil.TimeNow().Unix()
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 清退申请生效
		db := tx.Table(contractapplyment.TableName()).
			Where("guild_id = ? AND live_id = ? AND status = ? AND type = ? AND expire_time > ?",
				guildID, liveID, contractapplyment.StatusPending, contractapplyment.TypeGuildExpel, nowStamp).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusAgreed,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return servicedb.ErrNoRowsAffected
		}

		// 合约解约
		db = tx.Table(TableName()).
			Where("guild_id = ? AND live_id = ? AND status = ?", guildID, liveID, StatusContracting).
			Updates(map[string]interface{}{
				"status":        StatusUseless,
				"contract_end":  nowStamp,
				"modified_time": nowStamp,
			})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return servicedb.ErrNoRowsAffected
		}
		// TODO: 分成比例修改上线后需要失效未处理的降薪申请
		// 未处理的续约、协商解约申请失效
		err := tx.Table(contractapplyment.TableName()).
			Where("live_id = ? AND guild_id = ? AND status = ?", liveID, guildID, contractapplyment.StatusPending).
			Where("type IN (?)", []int64{contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew, contractapplyment.TypeLiveTerminate}).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusInvalid,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return err
		}

		// 更新公会主播数冗余字段
		err = guild.IncreaseLiveNum(guildID, -1, tx)
		if err != nil {
			return err
		}

		// 去除主播的公会经纪人身份和公会经纪人与主播关系
		return servicedb.Tx(service.LiveDB, func(tx2 *gorm.DB) error {
			// 如果主播是经纪人，需要去除主播的经纪人身份
			err = tx2.Table(guildagent.GuildAgent{}.TableName()).
				Where("guild_id = ? AND agent_id = ? AND delete_time = 0", guildID, liveID).
				Updates(map[string]interface{}{
					"delete_time":   nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}

			// 去除公会经纪人与主播关系
			err = tx2.Table(guildagent.AgentCreator{}.TableName()).
				Where("guild_id = ? AND (agent_id = ? OR creator_id = ?) AND delete_time = 0",
					guildID, liveID, liveID).
				Updates(map[string]interface{}{
					"delete_time":   nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return err
			}
			return nil
		})
	})
	if err != nil {
		return err
	}
	return nil
}

// UnsetAttrBitMaskExpr 将 attr 第 i 位设为 0 的原生 SQL 表达式
func UnsetAttrBitMaskExpr(i int) interface{} {
	return gorm.Expr("attr &~ ?", 1<<(i-1))
}

// SetAttrBitMaskExpr 将 attr 第 i 位设为 1 的原生 SQL 表达式
func SetAttrBitMaskExpr(i int) interface{} {
	return gorm.Expr("attr | ?", 1<<(i-1))
}

// NewGuildRateApplication 新建公会修改主播最低分成比例记录
func (lc *LiveContract) NewGuildRateApplication(editRate int) (*contractapplyment.ContractApplyment, error) {
	encodeRate, err := json.Marshal(&contractapplyment.More{
		Rate: &lc.Rate,
	})
	if err != nil {
		return nil, err
	}
	now := goutil.TimeNow()
	newApplication := &contractapplyment.ContractApplyment{
		LiveID:             lc.LiveID,
		GuildID:            lc.GuildID,
		GuildName:          lc.GuildName,
		ContractID:         lc.ID,
		ContractDuration:   lc.ContractDuration,
		ContractExpireTime: lc.ContractEnd,
		ExpireTime:         contractapplyment.ExpireTime(now, contractapplyment.TypeRateDown),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               editRate,
		More:               string(encodeRate),
		CreateTime:         now.Unix(),
		ModifiedTime:       now.Unix(),
	}
	if newApplication.Rate > lc.Rate {
		newApplication.Type = contractapplyment.TypeRateUp
		// 分成比例调高直接同意，过期时间设置为申请创建时间
		newApplication.ExpireTime = newApplication.CreateTime
		newApplication.Status = contractapplyment.StatusAgreed
	} else {
		newApplication.Type = contractapplyment.TypeRateDown
		newApplication.Status = contractapplyment.StatusPending
	}
	return newApplication, nil
}

// SaveGuildRate 保存降薪记录
func SaveGuildRate(application *contractapplyment.ContractApplyment) error {
	updateContract := map[string]interface{}{
		"modified_time": application.ModifiedTime,
	}
	switch application.Type {
	case contractapplyment.TypeRateDown:
		updateContract["attr"] = SetAttrBitMaskExpr(AttrBitMaskLiveGuildRate)
	case contractapplyment.TypeRateUp:
		updateContract["attr"] = UnsetAttrBitMaskExpr(AttrBitMaskLiveGuildRate)
		updateContract["rate"] = application.Rate
	default:
		panic(fmt.Errorf("不支持的调薪类型：%d", application.Type))
	}
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 更新公会与主播合约表
		dbRes := tx.Table(tableName).
			Where("id = ?", application.ContractID).Updates(updateContract)
		if dbRes.Error != nil {
			return dbRes.Error
		}
		if dbRes.RowsAffected == 0 {
			return servicedb.ErrNoRowsAffected
		}

		// 添加申请记录
		if err := tx.Create(&application).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// UpdateGuildRate 更新最低分成比例
func (lc *LiveContract) UpdateGuildRate(operate int, application *contractapplyment.ContractApplyment) error {
	now := goutil.TimeNow()
	updateContract := map[string]interface{}{
		"modified_time": now.Unix(),
		"attr":          UnsetAttrBitMaskExpr(AttrBitMaskLiveGuildRate),
	}
	updateApp := map[string]interface{}{
		"process_time":  now.Unix(),
		"modified_time": now.Unix(),
	}
	switch operate {
	case contractapplyment.DisagreeEditRate:
		updateApp["status"] = contractapplyment.StatusDeclined
	case contractapplyment.AgreeEditRate:
		updateApp["status"] = contractapplyment.StatusAgreed
		updateContract["rate"] = application.Rate
	default:
		panic(fmt.Errorf("不支持的操作类型：%d", operate))
	}
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		dbRes := tx.Table(application.TableName()).
			Where("id = ? AND status = ?", application.ID, contractapplyment.StatusPending).
			Updates(updateApp)
		if dbRes.Error != nil {
			return dbRes.Error
		}
		if dbRes.RowsAffected == 0 {
			return servicedb.ErrNoRowsAffected
		}

		return tx.Table(tableName).Where("id = ?", lc.ID).Updates(updateContract).Error
	})
	return err
}

// FindUsersGuildContractingContracts 批量获取主播生效中的合约列表
func FindUsersGuildContractingContracts(guildID int64, liveIDs []int64) ([]*LiveContract, error) {
	var contracts []*LiveContract
	err := service.DB.Where("guild_id = ? AND live_id IN (?)", guildID, liveIDs).
		Where("status = ?", StatusContracting).
		Find(&contracts).Error
	return contracts, err
}
