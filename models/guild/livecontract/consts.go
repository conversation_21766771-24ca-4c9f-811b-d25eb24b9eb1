package livecontract

// status
const (
	StatusUseless     int64 = iota - 3 // 合约解约（主播退会申请/公会发起解约被通过）
	StatusFinished                     // 合约失效（合约到期/主播加入其中一个公会后，未决的其它合约失效）
	StatusRefused                      // 合约被拒绝（主播申请入会/公会邀约）
	StatusUntreated                    // 合约未决（主播申请中/公会邀约中）
	StatusContracting                  // 合约生效中（主播申请退会/公会发起解约过程中及被拒绝后，主播申请入会通过/公会邀约通过）
	StatusTerminating                  // 合约生效中（主播申请退会、公会发起解约）【已弃用】
)

// type 字段用
const (
	FromLiver = iota + 1
	FromGuild
)

// attr 字段用，按位运算，表示第几位
const (
	AttrBitMaskLiveTerminated = iota + 1 // 第 1 位表示主播在合约期内申请过协商解约
	AttrBitMaskLiveGuildRate             // 第 2 位表示主播有未处理的降薪申请
)

// ProtectedDaysOfFirstSign 签约的保护期时长（天）
const ProtectedDaysOfFirstSign = 180

// ProtectedDaysOfGuildExpel 公会清退主播的保护期时长（天）
const ProtectedDaysOfGuildExpel = 30

// StatusValid 判断 status 是否是有效的
func StatusValid(status int64) bool {
	return StatusUseless <= status && status <= StatusTerminating
}

// TypeValid 判断 type 是否是有效的
func TypeValid(from int64) bool {
	return FromLiver <= from && from <= FromGuild
}
