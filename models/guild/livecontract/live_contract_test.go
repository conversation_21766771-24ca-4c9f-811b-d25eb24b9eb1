package livecontract

import (
	"math"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 用于 service 单例的初始化
func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("guild_live_contract", TableName())
	assert.Equal("guild_live_contract", LiveContract{}.TableName())
}

func TestGetByPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := LiveContract{
		ID:        172,
		GuildID:   3,
		GuildName: "测试 liveContract 使用数据库的 pk 来寻找合同的功能",
	}
	err := service.DB.Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)

	time.Sleep(time.Millisecond * 200)

	contract, _ := GetByPK(0)
	assert.Nil(contract)
	contract, _ = GetByPK(172)
	assert.NotNil(contract)
}

func TestHaveRight(t *testing.T) {
	l := new(LiveContract)
	l.ContractEnd = math.MaxInt64
	l.Status = StatusFinished
	l.Type = FromGuild
	l.LiveID = 123
	assert.Nil(t, l.HaveRight(l.Status, l.Type, l.LiveID))
}

func TestUpdateContract(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := LiveContract{
		ID:        188,
		GuildID:   3,
		GuildName: "测试 liveContract 更新合同的功能",
	}
	err := service.DB.Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)

	testID := int64(188)
	require.NoError(service.DB.Table(TableName()).Where("id = ?", testID).
		UpdateColumn("status", StatusUntreated).Error)
	time.Sleep(200 * time.Millisecond)
	contract, err := GetByPK(testID)
	require.NoError(err)
	require.NotNil(contract)
	assert.True(contract.UpdateContract(StatusUntreated))
	updatedC, err := GetByPK(testID)
	require.NoError(err)
	require.NotNil(updatedC)
	assert.Equal(StatusUntreated, updatedC.Status)
}

func TestIsInGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	guildID, err := IsInGuild(987654, 0)
	assert.NoError(err)
	assert.Zero(guildID)
	lc := new(LiveContract)
	require.NoError(service.DB.Find(lc, "status = ? AND contract_end > ? AND guild_id != 0", StatusContracting, goutil.TimeNow().Unix()).Error)
	guildID, err = IsInGuild(lc.LiveID, 0)
	require.NoError(err)
	assert.Equal(lc.GuildID, guildID)
	guildID, err = IsInGuild(lc.LiveID, lc.GuildID+1)
	require.NoError(err)
	assert.Zero(guildID)
	guildID, err = IsInGuild(lc.LiveID, lc.GuildID)
	require.NoError(err)
	assert.Equal(lc.GuildID, guildID)
}

func TestFindInContractingByLiveID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lc := new(LiveContract)
	require.NoError(service.DB.First(lc,
		"status = ? AND contract_end > ? AND guild_id <> 0 AND live_id <> 0",
		StatusContracting, goutil.TimeNow().Unix()).Error)

	lc2, err := FindInContractingByLiveID(lc.LiveID, 0)
	require.NoError(err)
	require.NotNil(lc2)
	assert.NotZero(lc2.ID)
	assert.True(lc2.CreateTime == lc.CreateTime || lc2.CreateTime != 0)
	assert.True(lc2.ModifiedTime == lc.CreateTime || lc2.ModifiedTime != 0) // 这三个变量有可能因为其他包的测试而发生变化
	lc2.ID, lc2.CreateTime, lc2.ModifiedTime = lc.ID, lc.CreateTime, lc.ModifiedTime
	assert.Equal(*lc, *lc2)
	lc2, err = FindInContractingByLiveID(lc.LiveID, 0, "live_id", "guild_id,contract_start")
	require.NoError(err)
	assert.Zero(lc2.ID)
	assert.Equal([]int64{lc2.LiveID, lc2.GuildID, lc2.ContractStart},
		[]int64{lc.LiveID, lc.GuildID, lc.ContractStart})
	lc2, err = FindInContractingByLiveID(lc.LiveID, lc.GuildID)
	require.NoError(err)
	assert.NotNil(lc2)
	lc2, err = FindInContractingByLiveID(lc.LiveID, lc.GuildID+1)
	require.NoError(err)
	assert.Nil(lc2)
	lc2, err = FindInContractingByLiveID(lc.LiveID, -1)
	require.NoError(err)
	assert.Nil(lc2)
}

func TestIsInProtecting(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	ok := IsInProtecting(now.Unix())
	assert.True(ok)

	nowDate := now.AddDate(0, 0, -ProtectedDaysOfFirstSign)
	ok = IsInProtecting(nowDate.Unix())
	assert.False(ok)
}

func TestIsInProtectingGuildExpel(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	ok := IsInProtectingGuildExpel(now.Unix())
	assert.True(ok)

	nowDate := now.AddDate(0, 0, -ProtectedDaysOfFirstSign)
	ok = IsInProtectingGuildExpel(nowDate.Unix())
	assert.False(ok)
}

func TestGetGuildID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildID, err := GetGuildID(77889900)
	assert.NoError(err)
	assert.Equal(int64(0), guildID)

	c := LiveContract{
		ID:          188,
		GuildID:     222333,
		LiveID:      6677,
		Status:      StatusContracting,
		ContractEnd: goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:   "测试 GetGuildID 方法",
	}
	err = service.DB.Assign(c).FirstOrCreate(&c).Error
	require.NoError(err)
	guildID, err = GetGuildID(c.LiveID)
	assert.NoError(err)
	assert.Equal(c.GuildID, guildID)
}

func TestFindGuildRecordLiveIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	liveIDs, err := FindGuildRecordLiveIDs(23333471)
	require.NoError(err)
	assert.Len(liveIDs, 1)
}

const (
	testGuildID int64 = 3
	testLiveID  int64 = 12
)

func TestAgreeExpelGuildLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()

	// 创建公会合约
	la := LiveContract{
		GuildID:     testGuildID,
		LiveID:      testLiveID,
		ContractEnd: 1999999999,
		Status:      StatusContracting,
		Type:        FromGuild,
		GuildOwner:  100008,
		GuildName:   "测试公会清退主播",
		Rate:        guildrate.RatePercent45,
	}
	require.NoError(la.DB().Create(&la).Error)
	defer la.DB().Delete("", "id = ?", la.ID)

	// 创建未处理的公会清退主播申请
	applyment1 := contractapplyment.ContractApplyment{
		LiveID:             la.LiveID,
		GuildID:            la.GuildID,
		GuildName:          la.GuildName,
		ContractID:         la.ID,
		ContractDuration:   la.ContractDuration,
		ContractExpireTime: la.ContractEnd,
		Type:               contractapplyment.TypeGuildExpel,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, 3).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               la.Rate,
	}
	require.NoError(applyment1.DB().Create(&applyment1).Error)
	defer applyment1.DB().Delete("", "id = ?", applyment1.ID)

	// 创建未处理的续约申请
	applyment2 := contractapplyment.ContractApplyment{
		LiveID:             la.LiveID,
		GuildID:            la.GuildID,
		GuildName:          la.GuildName,
		ContractID:         la.ID,
		ContractDuration:   la.ContractDuration,
		ContractExpireTime: la.ContractEnd,
		Type:               contractapplyment.TypeLiveRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, contractapplyment.ExpireDaysExpelApplyment).Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               la.Rate,
	}
	require.NoError(applyment2.DB().Create(&applyment2).Error)
	defer applyment2.DB().Delete("", "id = ?", applyment2.ID)

	// 创建未处理的协商解约申请
	applyment3 := contractapplyment.ContractApplyment{
		LiveID:             la.LiveID,
		GuildID:            la.GuildID,
		GuildName:          la.GuildName,
		ContractID:         la.ID,
		ContractDuration:   la.ContractDuration,
		ContractExpireTime: la.ContractEnd,
		Type:               contractapplyment.TypeLiveTerminate,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, contractapplyment.ExpireDaysTerminateApplyment).Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               la.Rate,
	}
	require.NoError(applyment3.DB().Create(&applyment3).Error)
	defer applyment3.DB().Delete("", "id = ?", applyment3.ID)

	// 创建公会经纪人
	ga := guildagent.GuildAgent{
		GuildID: testGuildID,
		AgentID: testLiveID,
	}
	require.NoError(ga.DB().Create(&ga).Error)
	defer ga.DB().Delete("", "id = ?", ga.ID)

	// 创建公会经纪人与主播关系
	gac := guildagent.AgentCreator{
		GuildID:   testGuildID,
		AgentID:   testLiveID,
		CreatorID: 444,
	}
	require.NoError(gac.DB().Create(&gac).Error)
	defer gac.DB().Delete("", "id = ?", gac.ID)

	// 测试清退主播
	assert.NoError(AgreeExpelGuildLive(testGuildID, testLiveID))

	// 验证公会合约是否已解约
	laAfter := new(LiveContract)
	require.NoError(laAfter.DB().Where("id = ?", la.ID).First(&laAfter).Error)
	assert.Equal(StatusUseless, laAfter.Status)

	// 验证清退申请是否生效
	applyment1After := new(contractapplyment.ContractApplyment)
	require.NoError(applyment1After.DB().Where("id = ?", applyment1.ID).First(&applyment1After).Error)
	assert.Equal(contractapplyment.StatusAgreed, applyment1After.Status)

	// 验证未处理的续约申请是否失效
	applyment2After := new(contractapplyment.ContractApplyment)
	require.NoError(applyment2After.DB().Where("id = ?", applyment2.ID).First(&applyment2After).Error)
	assert.Equal(contractapplyment.StatusInvalid, applyment2After.Status)

	// 验证未处理的协商解约申请
	applyment3After := new(contractapplyment.ContractApplyment)
	require.NoError(applyment3After.DB().Where("id = ?", applyment3.ID).First(&applyment3After).Error)
	assert.Equal(contractapplyment.StatusInvalid, applyment3After.Status)

	// 验证主播的经纪人身份是否删除
	gaAfter := new(guildagent.GuildAgent)
	err := gaAfter.DB().Where("id = ?", ga.ID).First(&gaAfter).Error
	require.NoError(err)
	assert.Greater(gaAfter.DeleteTime, int64(0))

	// 验证公会经纪人与主播关系是否删除
	gacAfter := new(guildagent.AgentCreator)
	require.NoError(gacAfter.DB().Where("id = ?", gac.ID).First(&gacAfter).Error)
	assert.Greater(gacAfter.DeleteTime, int64(0))
}

func TestNewGuildRateApplication(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ca := LiveContract{
		ID:      101,
		GuildID: 3222,
		LiveID:  2333,
		Rate:    45,
	}
	newApplication, err := ca.NewGuildRateApplication(42)
	require.NoError(err)
	assert.Equal(ca.ID, newApplication.ContractID)

	newApplication, err = ca.NewGuildRateApplication(46)
	require.NoError(err)
	assert.Equal(ca.ID, newApplication.ContractID)
}

func TestSaveGuildRate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ca := LiveContract{
		GuildID: 3222,
		LiveID:  2333,
		Rate:    45,
	}
	require.NoError(service.DB.Table(tableName).Create(&ca).Error)
	defer func() {
		require.NoError(service.DB.Table(tableName).Delete("", "id = ?", ca.ID).Error)
	}()

	// 降薪
	application := contractapplyment.ContractApplyment{
		LiveID:     2333,
		GuildID:    3222,
		ContractID: ca.ID,
		Rate:       43,
		Type:       contractapplyment.TypeRateDown,
	}
	require.NoError(SaveGuildRate(&application))

	// 检查数据
	newApplication := contractapplyment.ContractApplyment{}
	require.NoError(newApplication.DB().First(&newApplication, "id = ?", application.ID).Error)
	assert.Equal(application.Rate, newApplication.Rate)
	assert.Equal(application.Type, newApplication.Type)
	attr := new(goutil.BitMask)
	require.NoError(service.DB.Table(tableName).Select("attr").Where("id = ?", ca.ID).Row().Scan(attr))
	assert.True(attr.IsSet(AttrBitMaskLiveGuildRate))

	// 涨薪
	application.ID = 0
	application.Rate = 46
	application.Type = contractapplyment.TypeRateUp
	require.NoError(SaveGuildRate(&application))

	// 检查数据
	newApplication = contractapplyment.ContractApplyment{}
	require.NoError(newApplication.DB().First(&newApplication, "id = ?", application.ID).Error)
	assert.Equal(application.Rate, newApplication.Rate)
	assert.Equal(application.Type, newApplication.Type)
	attr = new(goutil.BitMask)
	require.NoError(service.DB.Table(tableName).Select("attr").Where("id = ?", ca.ID).Row().Scan(attr))
	assert.False(attr.IsSet(AttrBitMaskLiveGuildRate))
}

func TestUpdateGuildRate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 创建主播与公会合约
	now := goutil.TimeNow()
	contract := LiveContract{
		LiveID:       1234,
		Status:       StatusContracting,
		GuildID:      3,
		Rate:         45,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	contract.Attr.Set(AttrBitMaskLiveGuildRate)
	require.NoError(service.DB.Table(contract.TableName()).Create(&contract).Error)
	defer func() {
		service.DB.Table(contract.TableName()).Delete("", "id = ?", contract.ID)
	}()

	application := contractapplyment.ContractApplyment{
		GuildID:      3,
		LiveID:       1234,
		Status:       contractapplyment.StatusPending,
		Rate:         40,
		Type:         contractapplyment.TypeRateDown,
		Initiator:    contractapplyment.InitiatorGuild,
		ExpireTime:   contractapplyment.ExpireTime(goutil.TimeNow(), contractapplyment.TypeRateDown),
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
	}
	require.NoError(service.DB.Table(application.TableName()).Create(&application).Error)
	defer func() {
		service.DB.Table(application.TableName()).Delete("", "id = ?", application.ID)
	}()

	// 拒绝调整
	require.NoError(contract.UpdateGuildRate(contractapplyment.DisagreeEditRate, &application))
	// 查看数据库数据
	newContract := new(LiveContract)
	newApplication := new(contractapplyment.ContractApplyment)
	service.DB.Table(newApplication.TableName()).Where("guild_id = ? AND live_id = ?", application.GuildID, application.LiveID).First(newApplication)
	assert.Equal(contractapplyment.StatusDeclined, newApplication.Status)
	require.NoError(service.DB.Table(newContract.TableName()).First(newContract, "id = ?", contract.ID).Error)
	assert.Equal(goutil.BitMask(0), newContract.Attr)

	// 同意调整
	require.NoError(service.DB.Table(application.TableName()).Where("id = ?", application.ID).
		Update("status", contractapplyment.StatusPending).Error)
	require.NoError(contract.UpdateGuildRate(contractapplyment.AgreeEditRate, &application))
	// 查看数据库数据是否正常
	newApplication = new(contractapplyment.ContractApplyment)
	service.DB.Table(newApplication.TableName()).Where("guild_id = ? AND live_id = ?", application.GuildID, application.LiveID).First(newApplication)
	assert.Equal(contractapplyment.StatusAgreed, newApplication.Status)
	newContract = new(LiveContract)
	require.NoError(service.DB.Table(newContract.TableName()).First(newContract, "id = ?", contract.ID).Error)
	assert.Equal(40, newContract.Rate)
}

func TestADB(t *testing.T) {
	assert := assert.New(t)

	// 测试 sqlite 环境
	assert.Equal(service.NewADB.Table(tableName), ADB())

	// 测试生产环境
	servicedb.Driver = servicedb.DriverMysql
	os.Setenv(util.EnvDeploy, util.DeployEnvProd)
	defer func() {
		servicedb.Driver = servicedb.DriverSqlite
		os.Setenv(util.EnvDeploy, "")
	}()
	assert.Equal(service.NewADB.Table("live."+tableName+" AS lc"), ADB("lc"))

	// 测试 UAT 环境
	os.Setenv(util.EnvDeploy, "")
	assert.Equal(service.NewADB.Table("app_missevan."+tableName+" AS lc"), ADB("lc"))
}

func TestFindUsersGuildContractingContracts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ca := LiveContract{
		GuildID: 20250301,
		LiveID:  1,
		Status:  1,
	}
	require.NoError(service.DB.Table(tableName).Create(&ca).Error)
	defer func() {
		require.NoError(service.DB.Table(tableName).Delete("", "id = ?", ca.ID).Error)
	}()

	contracts, err := FindUsersGuildContractingContracts(ca.GuildID, []int64{1})
	require.NoError(err)
	assert.Len(contracts, 1)

	contracts, err = FindUsersGuildContractingContracts(-999999, []int64{1234, 5678})
	require.NoError(err)
	assert.Empty(contracts)
}
