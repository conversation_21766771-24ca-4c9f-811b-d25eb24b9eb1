package livecontract

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestStatusValid(t *testing.T) {
	assert := assert.New(t)
	assert.True(StatusValid(StatusUseless))
	assert.Fals<PERSON>(StatusValid(-4))
	assert.False(StatusValid(999))
}

func TestTypeValid(t *testing.T) {
	assert := assert.New(t)
	assert.True(TypeValid(FromLiver))
	assert.False(TypeValid(-1))
	assert.False(TypeValid(999))
}
