package guildbalance

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	_, err := Find(3)
	assert.NoError(err)

	b, err := Find(-1)
	assert.Nil(b)
	assert.NoError(err)
}

func TestFindMap(t *testing.T) {
	assert := assert.New(t)
	arr := []int64{3, -1}
	res, err := FindMap(arr)

	assert.NoError(err)
	assert.NotNil(res[3])
	assert.Nil(res[-1])
}

func TestUpdateRate(t *testing.T) {
	assert := assert.New(t)
	oldB, _ := Find(3)
	require.NotNil(t, oldB)
	assert.NoError(UpdateRate(3, 0.6))
	time.Sleep(1 * time.Second)
	b, _ := Find(3)
	require.NotNil(t, b)
	assert.NotEqual(oldB.ModifiedTime, b.ModifiedTime)
	assert.NoError(UpdateRate(0, 0.3))
	assert.Error(UpdateRate(3, 10))
	newB, _ := Find(3)
	assert.Equal(newB.ModifiedTime, b.ModifiedTime)
}
