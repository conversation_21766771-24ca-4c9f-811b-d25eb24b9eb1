package guildbalance

import (
	"errors"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "guild_balance"

// TableName table name
func TableName() string {
	return tableName
}

// GuildBalance 公会收益表
type GuildBalance struct {
	// 公会 ID
	ID int64 `gorm:"column:id;primary_key" json:"id"`
	// GuildName     string  `gorm:"column:guild_name" json:"guild_name"`
	InIos         int64   `gorm:"column:in_ios" json:"in_ios"`
	InAndroid     int64   `gorm:"column:in_android" json:"in_android"`
	InPaypal      int64   `gorm:"column:in_paypal" json:"in_paypal"`
	LiveProfit    float64 `gorm:"column:live_profit" json:"live_profit"`
	AllLiveProfit float64 `gorm:"column:all_live_profit" json:"all_live_profit"`
	Rate          float64 `gorm:"column:rate" sql:"DEFAULT:0.5" json:"rate"`
	CreateTime    int64   `gorm:"column:create_time" json:"-"`
	ModifiedTime  int64   `gorm:"column:modified_time" json:"-"`
}

// DB the db instance of GuildBalance model
func (gb GuildBalance) DB() *gorm.DB {
	return service.PayDB.Table(gb.TableName())
}

// TableName table name
func (GuildBalance) TableName() string {
	return tableName
}

// Find 通过公会 ID 查询收益表
func Find(guildID int64) (*GuildBalance, error) {
	balance := new(GuildBalance)
	err := GuildBalance{}.DB().Find(balance, "id = ?", guildID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return balance, nil
}

// FindMap 查询 ids 所属收益表，返回 map[id]*GuildBalance
func FindMap(ids []int64) (map[int64]*GuildBalance /* map[id]*GuildBalance */, error) {
	temp := make([]*GuildBalance, 0)
	err := GuildBalance{}.DB().Find(&temp, "id IN (?)", ids).Error
	if err != nil {
		return nil, err
	}
	res := make(map[int64]*GuildBalance)
	for i := 0; i < len(temp); i++ {
		res[temp[i].ID] = temp[i]
	}
	return res, nil
}

// UpdateRate 更新 ID 所属公会分成
// NOTICE：如果收益表不存在，不会返回 error也不会新插入一张表
func UpdateRate(id int64, rate float64) error {
	if rate < 0 || rate > 1.0 {
		return errors.New("rate should be in [0, 1.0]")
	}
	if id <= 0 {
		return nil
	}
	balance := &GuildBalance{ID: id}
	err := GuildBalance{}.DB().Model(balance).Update("rate", rate).Error
	return err
}

// BeforeCreate automatically set field modified_time
func (balance *GuildBalance) BeforeCreate(scope *gorm.Scope) (err error) {
	err = scope.SetColumn("create_time", goutil.TimeNow().Unix())
	if nil == err {
		err = scope.SetColumn("modified_time", goutil.TimeNow().Unix())
	}
	return err
}

// BeforeUpdate automatically update field modified_time
func (balance *GuildBalance) BeforeUpdate(scope *gorm.Scope) (err error) {
	err = scope.SetColumn("modified_time", goutil.TimeNow().Unix())
	return err
}
