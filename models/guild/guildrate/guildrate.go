package guildrate

import "fmt"

const (
	// RatePercent45 平台默认最低分成比例 45%
	RatePercent45 = 45
	// RatePercent50 平台规定最低分成比例 50%
	RatePercent50 = 50
	// RatePercent55 平台规定最低分成比例 55%
	RatePercent55 = 55
	// RatePercent60 平台规定最低分成比例 60%
	RatePercent60 = 60
)

// ContractRates 主播与公会最低分成比例列表
var ContractRates = []int{RatePercent45, RatePercent50, RatePercent55, RatePercent60}

// IsLegalContractRate 检查是否符合平台最低分成比例
func IsLegalContractRate(rate int) bool {
	for _, contractRate := range ContractRates {
		if contractRate == rate {
			return true
		}
	}
	return false
}

// ApplymentRate 防止传入的 rate 为空
func ApplymentRate(r int) (rate int) {
	rate = r
	if rate == 0 {
		rate = RatePercent45
	}
	return
}

// FormatRateToString 格式化最低分成比例
func FormatRateToString(rate int) string {
	return fmt.Sprintf("%d%%", rate)
}
