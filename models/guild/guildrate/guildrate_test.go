package guildrate

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsLegalContractRate(t *testing.T) {
	assert := assert.New(t)

	// 测试合法最低分成比例
	assert.Equal(true, IsLegalContractRate(RatePercent45))

	// 测试不合法最低分成比例
	assert.Equal(false, IsLegalContractRate(46))
}

func TestApplymentRate(t *testing.T) {
	assert := assert.New(t)

	// 测试 rate 为空
	rate := ApplymentRate(0)
	assert.Equal(RatePercent45, rate)

	// 测试 rate 不为空
	rate = ApplymentRate(RatePercent60)
	assert.Equal(RatePercent60, rate)
}
