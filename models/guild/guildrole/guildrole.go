package guildrole

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 公会中的角色
const (
	RoleGuildLiveCreator = iota + 1
	RoleGuildOwner
	RoleGuildAgent
)

// GuildRole 公会角色，按位运算：第 1 位公会主播，第 2 位公会会长，第 3 位公会经纪人
type GuildRole struct {
	goutil.BitMask
}

// IsGuildManager 是否为公会管理
func (r GuildRole) IsGuildManager() bool {
	return r.IsGuildOwner() || r.IsGuildAgent()
}

// IsGuildOwner 是否为公会会长
func (r GuildRole) IsGuildOwner() bool {
	return r.IsSet(RoleGuildOwner)
}

// IsGuildAgent 是否为公会经纪人
func (r GuildRole) IsGuildAgent() bool {
	return r.IsSet(RoleGuildAgent)
}

// IsGuildLiveCreator 是否为主播
func (r GuildRole) IsGuildLiveCreator() bool {
	return r.IsSet(RoleGuildLiveCreator)
}

// IsGuildOwner 判断用户是否是通过申请的公会会长，是会长返回公会 ID 和公会名称
func IsGuildOwner(userID int64) (*guild.IDName, error) {
	g := new(guild.IDName)

	err := guild.Guild{}.DB().Select("id, name").
		Where("user_id = ? AND checked = ?", userID, guild.CheckedPass).First(g).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return g, nil
}

// FindAssociatedContractWithAgent 访问用户是否为被查看用户的会长或经纪人，并返回合约详情
func FindAssociatedContractWithAgent(creatorID, userID int64) (role GuildRole, g *livecontract.LiveContract, err error) {
	// 判断访问用户是否为会长
	g, err = livecontract.FindInContractingByLiveID(creatorID, 0)
	if err != nil {
		return
	}
	if g == nil {
		return
	}
	if g.GuildOwner == userID {
		role.Set(RoleGuildOwner)
	}

	// 判断访问用户是否属于对应经纪人
	isAssigned, err := guildagent.IsAssigned(creatorID, userID)
	if err != nil {
		return
	}
	if isAssigned {
		role.Set(RoleGuildAgent)
	}
	return
}

// UserGuildRole 获取用户在公会的角色
func UserGuildRole(userID int64) (role GuildRole, guildInfo *guild.IDName, err error) {
	if userID == 0 {
		return
	}
	var liveGuildID int64
	guildInfo, err = IsGuildOwner(userID)
	if err != nil {
		return
	}
	if guildInfo != nil {
		role.Set(RoleGuildOwner)
	}

	agentGuildID, err := guildagent.GuildID(userID)
	if err != nil {
		return
	}
	if agentGuildID > 0 {
		role.Set(RoleGuildAgent)
		if guildInfo == nil {
			guildInfo, err = guild.SimpleInfo(agentGuildID)
			if err != nil {
				return
			}
		}
	}

	liveGuildID, err = livecontract.GetGuildID(userID)
	if err != nil {
		return
	}
	if liveGuildID > 0 {
		role.Set(RoleGuildLiveCreator)
	}
	return
}

// GuildManagerRole 公会管理员（会长、经纪人）信息
type GuildManagerRole struct {
	Role       GuildRole
	GuildInfo  *guild.IDName
	CreatorIDs []int64
}

// Options 获取用户公会角色信息选项
type Options struct {
	GuildOwnerWithContracted bool // 为 true 时，并且同时是公会会长才能查询合约内、已解除和已过期的主播 ID, 为 false 时只查询生效中的
}

// FindManagerRole 获取用户在公会中的管理（会长、经纪人）角色信息、公会信息、旗下主播，不在公会中担任管理角色时，返回 nil
func FindManagerRole(userID int64, findCreatorIDs bool, option ...Options) (*GuildManagerRole, error) {
	role, guildInfo, err := UserGuildRole(userID)
	if err != nil {
		return nil, err
	}
	if guildInfo == nil || !role.IsGuildManager() {
		return nil, nil
	}
	res := &GuildManagerRole{
		Role:      role,
		GuildInfo: guildInfo,
	}
	if !findCreatorIDs {
		return res, nil
	}

	if err = res.FindContractCreatorIDs(userID, option...); err != nil {
		return nil, err
	}
	return res, nil
}

// FindContractCreatorIDs 获取公会管理员旗下主播
func (gmr *GuildManagerRole) FindContractCreatorIDs(userID int64, option ...Options) error {
	var creatorIDs []int64
	if gmr.Role.IsGuildOwner() {
		if len(option) != 0 && option[0].GuildOwnerWithContracted {
			var err error
			// 获取公会会长旗下合约内、已解除和已过期的主播信息
			if gmr.CreatorIDs, err = livecontract.FindGuildRecordLiveIDs(gmr.GuildInfo.ID); err != nil {
				return err
			}
			return nil
		}
		// 获取公会会长返回旗下所有主播信息
		err := livecontract.LiveContract{}.DB().
			Where("guild_id = ?", gmr.GuildInfo.ID).
			Where("status = ? AND contract_end > ?", livecontract.StatusContracting, goutil.TimeNow().Unix()).
			Pluck("live_id", &creatorIDs).Error
		if err != nil {
			return err
		}
	} else if gmr.Role.IsGuildAgent() {
		// 查询经纪人旗下所有主播信息
		var liveIDs []int64
		err := guildagent.AgentCreator{}.DB().
			Where("agent_id = ? AND delete_time = 0", userID).
			Pluck("creator_id", &liveIDs).Error
		if err != nil {
			return err
		}
		err = livecontract.LiveContract{}.DB().
			Where("guild_id = ?", gmr.GuildInfo.ID).
			Where("status = ? AND contract_end > ?", livecontract.StatusContracting, goutil.TimeNow().Unix()).
			Where("live_id IN (?)", liveIDs).
			Pluck("live_id", &creatorIDs).Error
		if err != nil {
			return err
		}
	}
	gmr.CreatorIDs = creatorIDs
	return nil
}

// ExistsContractCreator 判断公会管理员旗下是否有主播
func (gmr GuildManagerRole) ExistsContractCreator() bool {
	return len(gmr.CreatorIDs) > 0
}
