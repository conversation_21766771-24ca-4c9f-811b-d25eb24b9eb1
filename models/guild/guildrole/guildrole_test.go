package guildrole

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestGuildRole(t *testing.T) {
	assert := assert.New(t)

	var role GuildRole
	assert.Equal(0, int(role.BitMask))

	role.Set(RoleGuildLiveCreator)
	assert.Equal(1<<(uint(RoleGuildLiveCreator)-1), int(role.BitMask))
	assert.True(role.IsGuildLiveCreator())
	assert.False(role.IsGuildAgent())
	assert.False(role.IsGuildOwner())

	role.Set(RoleGuildOwner)
	assert.True(role.IsGuildOwner())
	assert.True(role.IsGuildManager())
	assert.Equal(1<<(uint(RoleGuildLiveCreator)-1)|1<<(uint(RoleGuildOwner)-1), int(role.BitMask))

	assert.False(role.IsGuildAgent())
	role.Set(RoleGuildAgent)
	assert.True(role.IsGuildAgent())
}

func TestFindAssociatedContractWithAgent(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	isOwnerOrAgent, g, err := FindAssociatedContractWithAgent(1, 20)
	assert.False(isOwnerOrAgent.IsGuildManager())
	assert.Nil(g)
	assert.Nil(err)

	// 测试公会会长访问
	testContractStart := int64(23333333)
	require.NoError(livecontract.LiveContract{}.DB().Create(livecontract.LiveContract{
		ID:            222333,
		GuildID:       222333,
		LiveID:        1,
		GuildOwner:    20,
		Status:        livecontract.StatusContracting,
		ContractStart: testContractStart,
		ContractEnd:   goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:     "测试 GetGuildID 方法",
	}).Error)
	defer livecontract.LiveContract{}.DB().Delete("", "id = ?", 222333)

	isOwnerOrAgent, g, err = FindAssociatedContractWithAgent(1, 20)
	require.NoError(err)
	assert.True(isOwnerOrAgent.IsGuildManager())
	assert.Equal(testContractStart, g.ContractStart)

	// 将合约会长转给其他人
	err = livecontract.LiveContract{}.DB().Where("id = ?", 222333).UpdateColumn(livecontract.LiveContract{GuildOwner: 233}).Error
	require.NoError(err)

	// 测试经纪人访问
	require.NoError(guildagent.AgentCreator{}.DB().Create(guildagent.AgentCreator{
		ID:        6768978,
		GuildID:   100,
		AgentID:   20,
		CreatorID: 1,
	}).Error)
	defer guildagent.AgentCreator{}.DB().Delete("", "id = ?", 6768978)

	isOwnerOrAgent, g, err = FindAssociatedContractWithAgent(1, 20)
	require.NoError(err)
	assert.True(isOwnerOrAgent.IsGuildManager())
	assert.Equal(testContractStart, g.ContractStart)
}

func TestIsGuildOwner(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试不存在的公会
	g, err := IsGuildOwner(23333)
	assert.Nil(err)
	assert.Nil(g)

	g, err = IsGuildOwner(12)
	require.NoError(err)
	assert.Equal(int64(3), g.ID)
	assert.Equal("测试公会（匆删）", g.Name)
}

func TestUserGuildRole(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	r, g, err := UserGuildRole(23333471)
	require.Nil(err)
	assert.True(r.IsGuildOwner())
	assert.True(r.IsGuildAgent())
	assert.True(r.IsGuildLiveCreator())
	assert.True(r.IsGuildManager())
	assert.Equal("测试公会（匆删）", g.Name)

	// 删除经纪人身份
	guildagent.GuildAgent{}.DB().Delete("", "id = ?", 23333471)
	// 删除主播身份
	live.Live{}.DB().Delete("", "id = ?", 23333471)
	livecontract.LiveContract{}.DB().Delete("", "id = ?", 23333471)
	// 删除会长身份
	guild.Guild{}.DB().Delete("", "id = ?", 23333471)

	r, g, err = UserGuildRole(23333471)
	require.Nil(err)
	assert.False(r.IsGuildOwner())
	assert.False(r.IsGuildAgent())
	assert.False(r.IsGuildLiveCreator())
	assert.False(r.IsGuildManager())
	assert.Nil(g)
}

func TestFindManagerRole(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testExistsID := int64(20210707)
	r, err := FindManagerRole(testExistsID, true)
	require.Nil(err)
	assert.True(r.Role.IsGuildOwner())
	assert.True(r.Role.IsGuildAgent())
	assert.True(r.Role.IsGuildManager())
	assert.Equal("公会 0707", r.GuildInfo.Name)
	assert.True(r.ExistsContractCreator())
	assert.Len(r.CreatorIDs, 2)

	r, err = FindManagerRole(testExistsID, false)
	require.Nil(err)
	assert.False(r.ExistsContractCreator())
	// 删除会长身份
	require.NoError(guild.Guild{}.DB().Where("id = ?", testExistsID).UpdateColumn("user_id", 2021070701).Error)
	r, err = FindManagerRole(testExistsID, true)
	require.Nil(err)
	assert.False(r.Role.IsGuildOwner())
	assert.True(r.Role.IsGuildAgent())
	assert.Empty(r.CreatorIDs)

	// 删除经纪人身份
	require.NoError(guildagent.GuildAgent{}.DB().Delete("", "id = ?", testExistsID).Error)
	r, err = FindManagerRole(testExistsID, true)
	require.Nil(err)
	assert.Nil(r)

	option := Options{
		GuildOwnerWithContracted: true,
	}
	r, err = FindManagerRole(2021070701, true, option)
	require.Nil(err)
	assert.Len(r.CreatorIDs, 1)
}
