package guildbanner

import (
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 公会申请 banner 审核状态
const (
	StatusPending = iota
	StatusPass
	StatusReject
)

// BannerMaxCount 同一时间段 banner 最大数量
const BannerMaxCount = int64(5)

// StatusFindAll 查询所有的状态
const StatusFindAll = -1

// GuildRecommendBanner 公会 banner
type GuildRecommendBanner struct {
	ID           int64  `gorm:"column:id;primary_key" json:"id"`
	CreateTime   int64  `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64  `gorm:"column:modified_time" json:"-"`
	RoomID       int64  `gorm:"column:room_id" json:"room_id"`
	GuildID      int64  `gorm:"column:guild_id" json:"guild_id"`
	CreatorID    int64  `gorm:"column:creator_id" json:"creator_id"`
	OperatorID   int64  `gorm:"column:operator_id" json:"-"`
	Title        string `gorm:"column:title" json:"title"`
	ImageURL     string `gorm:"column:image_url" json:"image_url"`
	Status       int    `gorm:"column:status" json:"status"`
	Reason       string `gorm:"column:reason" json:"reason,omitempty"`
	StartTime    int64  `gorm:"column:start_time" json:"start_time"`
	EndTime      int64  `gorm:"column:end_time" json:"end_time"`

	GuildName       string `gorm:"-" json:"guild_name"`
	CreatorUsername string `gorm:"-" json:"creator_username"`
}

// TableName table name
func (GuildRecommendBanner) TableName() string {
	return "guild_recommend_banner"
}

// BeforeUpdate hook
func (g GuildRecommendBanner) BeforeUpdate(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", goutil.TimeNow().Unix())
}

// BeforeCreate hook
func (g *GuildRecommendBanner) BeforeCreate() error {
	now := goutil.TimeNow().Unix()
	g.CreateTime = now
	g.ModifiedTime = now
	return nil
}

// AfterFind gorm 钩子
func (g *GuildRecommendBanner) AfterFind() error {
	g.ImageURL = storage.ParseSchemeURL(g.ImageURL)
	return nil
}

// Create 创建 banner 申请
func (g GuildRecommendBanner) Create(tx *gorm.DB) error {
	if tx == nil {
		tx = service.LiveDB
	}
	return tx.Create(&g).Error
}

// FindBannerByIDs 根据 bannerID 批量获取 banner
func FindBannerByIDs(bannerIDs []int64) ([]*GuildRecommendBanner, error) {
	var banners []*GuildRecommendBanner
	err := service.LiveDB.Find(&banners, "id IN (?)", bannerIDs).Error
	if err != nil {
		return nil, err
	}
	if len(banners) != 0 {
		err = addExtraInfoLiveBanner(banners)
		if err != nil {
			return nil, err
		}
	}
	return banners, nil
}

// UpdateStatus 批量更新 banner 审核状态
func UpdateStatus(bannerIDs []int64, status int, reason string) (bool, error) {
	update := map[string]interface{}{
		"status": status,
	}
	if reason != "" {
		update["reason"] = reason
	}
	db := service.LiveDB.Model(&GuildRecommendBanner{}).Where("id IN (?) AND status = ?", bannerIDs, StatusPending).Updates(update)
	return db.RowsAffected > 0, db.Error
}

// PeriodPendingCount 获取按照时间分组的待审核 banner 申请数量
func PeriodPendingCount(tx *gorm.DB) (map[int64]GuildRecommendCount, error) {
	if tx == nil {
		tx = service.LiveDB
	}
	guildRecommendCount := make([]GuildRecommendCount, 0)
	err := tx.Table(GuildRecommendBanner{}.TableName()).Select("COUNT(*) AS count, start_time").
		Group("start_time").Find(&guildRecommendCount, "status = ?", StatusPending).Error
	if err != nil {
		return nil, err
	}
	guildRecommendCountMap := goutil.ToMap(guildRecommendCount, "StartTime").(map[int64]GuildRecommendCount)
	return guildRecommendCountMap, nil
}

// FindBannerPendingList 超管获取公会待审核 banner 申请列表
func FindBannerPendingList(creatorID, guildID, p, pageSize int64) ([]*GuildRecommendBanner, *goutil.Pagination, error) {
	db := service.LiveDB.Table(GuildRecommendBanner{}.TableName()).Where("status = ?", StatusPending)
	if creatorID != 0 {
		db = db.Where("creator_id = ? ", creatorID)
	}
	if guildID != 0 {
		db = db.Where("guild_id = ?", guildID)
	}
	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, nil, err
	}
	guildBannerList := make([]*GuildRecommendBanner, 0)
	pagination := goutil.MakePagination(count, p, pageSize)
	if !pagination.Valid() {
		return guildBannerList, &pagination, nil
	}
	err = pagination.ApplyTo(db).Order("create_time DESC").Find(&guildBannerList).Error
	if err != nil {
		return nil, nil, err
	}
	if len(guildBannerList) != 0 {
		err = addExtraInfoLiveBanner(guildBannerList)
		if err != nil {
			return nil, nil, err
		}
	}
	return guildBannerList, &pagination, nil
}

// addExtraInfoLiveBanner 添加 banner 信息
func addExtraInfoLiveBanner(list []*GuildRecommendBanner) error {
	roomIDs := make([]int64, 0, len(list))
	guildIds := make([]int64, 0, len(list))
	for _, v := range list {
		roomIDs = append(roomIDs, v.RoomID)
		guildIds = append(guildIds, v.GuildID)
	}
	roomSimple, err := room.ListSimples(
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(
			bson.M{
				"room_id":          1,
				"creator_username": 1,
			},
		),
	)
	if err != nil {
		return err
	}
	roomSimpleMap := goutil.ToMap(roomSimple, "RoomID").(map[int64]*room.Simple)
	guildNameMap, err := guild.FindSimpleMap(guildIds)
	if err != nil {
		return err
	}
	for i, v := range list {
		simple, ok := roomSimpleMap[v.RoomID]
		if ok {
			list[i].CreatorUsername = simple.CreatorUsername
		}
		g, ok := guildNameMap[v.GuildID]
		if ok {
			list[i].GuildName = g.Name
		}
	}
	return nil
}

// GuildRecommendCount 公会申请 Banner 次数
type GuildRecommendCount struct {
	GuildRecommendBanner

	Count int64 `gorm:"column:count" json:"count"`
}

// FindGuildRecommendCount 获取公会当月申请次数
func FindGuildRecommendCount(guildIDs []int64, beginTime, endTime time.Time, p, pageSize int64) ([]GuildRecommendCount, *goutil.Pagination, error) {
	db := service.LiveDB.Table(GuildRecommendBanner{}.TableName())
	if len(guildIDs) != 0 {
		db = db.Where("guild_id IN (?)", guildIDs)
	}
	db = db.Where("create_time >= ? AND create_time < ? AND status = ?",
		beginTime.Unix(), endTime.Unix(), StatusPass).Group("guild_id")
	var count int64
	// SELECT count(*) FROM ( SELECT count(*) as name  ... GROUP BY guild_id ) AS count_table
	err := db.Count(&count).Error
	if err != nil {
		return nil, nil, err
	}
	db = db.Select("COUNT(*) AS count, guild_id")
	guildRecommendCount := make([]GuildRecommendCount, 0)
	pagination := goutil.MakePagination(count, p, pageSize)
	if !pagination.Valid() {
		return guildRecommendCount, &pagination, nil
	}
	err = pagination.ApplyTo(db).Order("guild_id ASC").Scan(&guildRecommendCount).Error
	if err != nil {
		return nil, nil, err
	}
	if len(guildRecommendCount) != 0 {
		arr := make([]*GuildRecommendBanner, len(guildRecommendCount))
		for i := range arr {
			arr[i] = &guildRecommendCount[i].GuildRecommendBanner
		}
		err = addExtraInfoLiveBanner(arr)
		if err != nil {
			return nil, nil, err
		}
	}
	return guildRecommendCount, &pagination, nil
}

// FindApplyList 获取公会 banner 申请列表
func FindApplyList(guildID int64, creatorIDs []int64, status int, p, pageSize int64) (list []*GuildRecommendBanner, pa goutil.Pagination, err error) {
	list = make([]*GuildRecommendBanner, 0)
	if len(creatorIDs) == 0 {
		pa = goutil.MakePagination(0, p, pageSize)
		return
	}
	db := service.LiveDB.Table(GuildRecommendBanner{}.TableName()).
		Where("guild_id = ? AND creator_id IN (?)", guildID, creatorIDs)
	if status != StatusFindAll {
		db = db.Where("status = ?", status)
	}
	var count int64
	err = db.Count(&count).Error
	if err != nil {
		return
	}
	pa = goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return
	}
	err = pa.ApplyTo(db).Order("create_time DESC").Find(&list).Error
	if len(list) != 0 {
		err = addExtraInfoLiveBanner(list)
		if err != nil {
			return nil, pa, err
		}
	}
	return
}

// CheckCreatorCreateTime 检查主播是否在时间范围内有申请记录
// 公会 Banner 申请的是下周时间，限制的是当周申请次数，所以以创建时间为准
func CheckCreatorCreateTime(creatorID int64, startTime, endTime time.Time) (bool, error) {
	banner := GuildRecommendBanner{}
	err := service.LiveDB.Model(GuildRecommendBanner{}).
		First(&banner, "creator_id = ? AND create_time >= ? AND create_time < ? AND status <> ?",
			creatorID, startTime.Unix(), endTime.Unix(), StatusReject).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}
