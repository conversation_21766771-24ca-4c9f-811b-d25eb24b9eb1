package guildbanner

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestFindBannerByIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	banners, err := FindBannerByIDs([]int64{1, 2, 3})
	require.NoError(err)
	assert.Len(banners, 3)
}

func TestUpdateStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok, err := UpdateStatus([]int64{4}, StatusPass, "")
	require.NoError(err)
	assert.True(ok)

	ok, err = UpdateStatus([]int64{5, 6}, StatusReject, "test")
	require.NoError(err)
	assert.True(ok)

	var g GuildRecommendBanner
	require.NoError(service.LiveDB.First(&g, "id = ?", 5).Error)
	assert.Equal(StatusReject, g.Status)
	assert.Equal("test", g.Reason)
}

func TestPeriodPendingCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	countMap, err := PeriodPendingCount(nil)
	require.NoError(err)
	assert.Len(countMap, 4)
}

func TestFindBannerList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	banners, pagination, err := FindBannerPendingList(0, 111, 1, 10)
	require.NoError(err)
	assert.Equal(int64(5), pagination.Count)
	assert.Len(banners, 5)

	banners, pagination, err = FindBannerPendingList(10101, 111, 1, 10)
	require.NoError(err)
	assert.Equal(int64(1), pagination.Count)
	assert.Len(banners, 1)
}

func TestFindGuildRecommendCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 7, 13, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	monthBegin := goutil.BeginningOfMonth(now)
	monthEnd := goutil.EndOfMonth(now)
	guildCount, pa, err := FindGuildRecommendCount([]int64{}, monthBegin, monthEnd, 1, 20)
	require.NoError(err)
	assert.Equal(int64(3), pa.Count)
	assert.Len(guildCount, 3)

	guildCount, pa, err = FindGuildRecommendCount([]int64{111, 222, 333}, monthBegin, monthEnd, 1, 20)
	require.NoError(err)
	assert.Equal(int64(2), pa.Count)
	assert.Len(guildCount, 2)
}

func TestFindApplyList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	list, pagination, err := FindApplyList(111, []int64{10101, 10102}, 0, 1, 20)
	require.NoError(err)
	assert.Len(list, 5)
	assert.Equal(int64(5), pagination.Count)
	assert.Equal(int64(1), pagination.MaxPage)
	assert.Equal(int64(1), pagination.P)
	assert.Equal(int64(20), pagination.PageSize)

	list, pagination, err = FindApplyList(111, []int64{10101, 10102}, -1, 1, 20)
	require.NoError(err)
	assert.Len(list, 6)
	assert.Equal(int64(6), pagination.Count)
	assert.Equal(int64(1), pagination.MaxPage)
	assert.Equal(int64(1), pagination.P)

	list, pagination, err = FindApplyList(111, []int64{10102}, 0, 1, 20)
	require.NoError(err)
	assert.Len(list, 4)
	assert.Equal(int64(4), pagination.Count)
	assert.Equal(int64(1), pagination.MaxPage)
	assert.Equal(int64(1), pagination.P)
}

func TestCheckCreatorCreateTime(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	now := goutil.TimeNow()
	banner := GuildRecommendBanner{
		CreatorID:  11223344,
		CreateTime: now.Unix(),
	}
	require.NoError(banner.Create(nil))

	beginning := util.BeginningOfWeek(now)
	ending := util.BeginningOfWeek(now.AddDate(0, 0, 7))
	exists, err := CheckCreatorCreateTime(banner.CreatorID, beginning, ending)
	require.NoError(err)
	assert.True(exists)
}
