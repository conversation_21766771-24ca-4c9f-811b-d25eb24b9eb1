package guild

import (
	"strings"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("guild", TableName())
	assert.Equal("guild", Guild{}.TableName())
}

func TestSchemeToURL(t *testing.T) {
	assert := assert.New(t)
	g := new(Guild)
	g.OwnerBackcover = "oss://test.png"
	g.OwnerIDPeople = "oss://test.png"
	g.BusinessLicenseFrontcover = "oss://test.png"
	g.schemeToURL()
	assert.True(strings.HasPrefix(g.OwnerBackcoverURL, "https://static-test.missevan.com/"))
	assert.True(strings.HasPrefix(g.OwnerIDPeopleURL, "https://static-test.missevan.com/"))
	assert.True(strings.HasPrefix(g.BusinessLicenseFrontcoverURL, "https://static-test.missevan.com/"))
	assert.True(strings.HasSuffix(g.OwnerBackcoverURL, "test.png"))
	assert.True(strings.HasSuffix(g.OwnerIDPeopleURL, "test.png"))
	assert.True(strings.HasSuffix(g.BusinessLicenseFrontcoverURL, "test.png"))
}

func TestFormat(t *testing.T) {
	assert := assert.New(t)

	testOSSURL := "oss://test.png"
	bankAccount := "0000000000000000000"

	newGuild := func(check int) *Guild {
		g := new(Guild)
		g.Checked = check
		g.OwnerBackcover = testOSSURL
		g.OwnerIDPeople = testOSSURL
		g.BusinessLicenseFrontcover = testOSSURL
		g.BankAccount = bankAccount
		return g
	}
	type testGuild struct {
		guild               *Guild
		isLiveFinanceRole   bool
		isGuildAccountAdmin bool
		targetURL           string
		targetBankAccount   string
	}
	testGuilds := []testGuild{}

	// 判空
	testGuilds = append(testGuilds, testGuild{
		guild:               &Guild{Checked: CheckedPass},
		isLiveFinanceRole:   false,
		isGuildAccountAdmin: false,
		targetBankAccount:   "",
	})
	// 不脱敏
	testGuilds = append(testGuilds, testGuild{
		guild:               newGuild(CheckedReject),
		isLiveFinanceRole:   false,
		isGuildAccountAdmin: false,
		targetURL:           "https://static-test.missevan.com/test.png",
		targetBankAccount:   bankAccount,
	})
	testGuilds = append(testGuilds, testGuild{
		guild:               newGuild(CheckedChecking),
		isLiveFinanceRole:   false,
		isGuildAccountAdmin: false,
		targetURL:           "https://static-test.missevan.com/test.png",
		targetBankAccount:   bankAccount,
	})
	testGuilds = append(testGuilds, testGuild{
		guild:               newGuild(CheckedPass),
		isLiveFinanceRole:   true,
		isGuildAccountAdmin: true,
		targetURL:           "https://static-test.missevan.com/test.png",
		targetBankAccount:   bankAccount,
	})
	// 脱敏
	testGuilds = append(testGuilds, testGuild{
		guild:               newGuild(CheckedPass),
		isLiveFinanceRole:   false,
		isGuildAccountAdmin: false,
		targetURL:           "*",
		targetBankAccount:   "00***************00",
	})
	testGuilds = append(testGuilds, testGuild{
		guild:               newGuild(CheckedPass),
		isLiveFinanceRole:   true,
		isGuildAccountAdmin: false,
		targetURL:           "*",
		targetBankAccount:   bankAccount,
	})

	for _, guild := range testGuilds {
		guild.guild.Format(guild.isLiveFinanceRole, guild.isGuildAccountAdmin)
		assert.Equal(guild.targetURL, guild.guild.OwnerBackcoverURL)
		assert.Equal(guild.targetURL, guild.guild.OwnerIDPeopleURL)
		assert.Equal(guild.targetURL, guild.guild.BusinessLicenseFrontcoverURL)
		assert.Equal(guild.targetBankAccount, guild.guild.BankAccount)
	}
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g, err := Find(3)
	require.NoError(err)
	require.NotNil(g)
	assert.Equal("测试公会（匆删）", g.Name)
	empty, err := Find(0)
	require.NoError(err)
	assert.Nil(empty)

	// 测试 checked = 1
	g, err = Find(3, CheckedPass)
	require.NoError(err)
	require.NotNil(g)
	assert.Equal("测试公会（匆删）", g.Name)

	// 测试 checked = 0 和 checked = -1
	g, err = Find(3, CheckedChecking, CheckedReject)
	require.NoError(err)
	assert.Nil(g)
}

func TestSwitchChecked(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	g, err := Find(3)
	require.NoError(err)
	defer func() {
		service.DB.Model(g).Update("checked", CheckedPass)
	}()
	require.NoError(service.DB.Model(g).Update("checked", CheckedChecking).Error)
	checkeds := []int{CheckedDissolved, CheckedReject, CheckedChecking, CheckedPass, CheckedDissolved}
	testRes := []bool{false, true, true, true, true}
	for i := 0; i < len(checkeds); i++ {
		ok, ownerID, err := SwitchChecked(service.DB, g.ID, checkeds[i])
		assert.NoError(err)
		assert.Equal(testRes[i], ok, i)
		assert.Equal(g.UserID, ownerID)
	}
	_, _, err = SwitchChecked(nil, 0, CheckedChecking)
	assert.True(gorm.IsRecordNotFoundError(err))
}

func TestEncryptAndDecrypt(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	g := Guild{
		Name:          "sssss",
		Intro:         "测试公会",
		OwnerName:     "钢铁侠",
		OwnerIDNumber: "123456789012345678",
		Mobile:        "***********",
		Bank:          "测试银行",
		CreateTime:    **********,
		ModifiedTime:  0,
	}
	// 加密
	g.Encrypt()
	assert.Equal(TypeEncrypted, g.Type)
	assert.Equal("4v7DAn/ke4W3CnJoCdr/Vg==", g.OwnerName)
	assert.Equal("FI4pA4hmBqq5ilfqC/tT7mfsJRQSu2eSmiMdsOpoXN4=", g.OwnerIDNumber)
	assert.Empty(g.Email)
	assert.Equal("N8JYFUBz1jHA/dMy5bxtvg==", g.Mobile)

	// 解密
	require.NoError(g.Decrypt())
	assert.Equal(TypeUnencrypted, g.Type)
	assert.Equal("钢铁侠", g.OwnerName)
	assert.Equal("123456789012345678", g.OwnerIDNumber)
	assert.Empty(g.Email)
	assert.Equal("***********", g.Mobile)
}

func TestFindSimpleMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guilds, err := FindSimpleMap([]int64{3, ********})
	require.NoError(err)
	assert.Len(guilds, 2)
}

func TestFindIDsByGuildNameLike(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildIDs, err := FindIDsByGuildNameLike("测")
	require.NoError(err)
	assert.GreaterOrEqual(len(guildIDs), 2)
}

func TestSimpleInfoByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试公会存在
	g, err := SimpleInfoByUserID(12)
	require.NoError(err)
	require.NotNil(g)
	assert.Equal("测试公会（匆删）", g.Name)

	// 测试公会不存在
	empty, err := SimpleInfoByUserID(0)
	require.NoError(err)
	assert.Nil(empty)
}

var checkCodeUserID int64 = 2333333

func TestSetOperatePermission(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.GuildUserCritialOperation1.Format(checkCodeUserID)
	require.NoError(service.Redis.Del(key).Err())

	err := SetOperatePermission(checkCodeUserID, "testxixihaha")
	require.NoError(err)

	v, err := service.Redis.Get(key).Result()
	require.NoError(err)
	assert.Equal(goutil.MD5("testxixihaha"), v)
}

func TestCheckOperatePermission(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.GuildUserCritialOperation1.Format(checkCodeUserID)
	require.NoError(service.Redis.Set(key, goutil.MD5("芜湖起飞"), time.Minute).Err())
	defer func() {
		require.NoError(service.Redis.Del(key).Err())
	}()

	pass, err := CheckOperatePermission(checkCodeUserID, "芜湖")
	require.NoError(err)
	assert.False(pass)

	pass, err = CheckOperatePermission(checkCodeUserID, "芜湖起飞")
	require.NoError(err)
	assert.True(pass)
}

func TestSearchByGuildName(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	gs, err := SearchByGuildName("测试搜索公会")
	require.NoError(err)
	assert.Len(gs, 2)
	assert.Equal(gs[0].ID, int64(20210708))
	assert.Equal(gs[1].ID, int64(20210709))
}

func TestIncreaseLiveNum(t *testing.T) {
	assert := assert.New(t)

	g := new(Guild)
	guildID := int64(7)
	assert.NoError(service.DB.First(g, guildID).Error)
	num := g.LiveNum
	defer func() {
		service.DB.Table(TableName()).Where("id = ?", guildID).Update("live_num", num)
	}()

	plus := int64(7)
	err := IncreaseLiveNum(g.ID, plus)
	assert.NoError(err)
	assert.NoError(service.DB.First(g, guildID).Error)
	assert.Equal(num+plus, g.LiveNum)

	minus := -g.LiveNum + 1
	err = IncreaseLiveNum(g.ID, minus)
	assert.NoError(err)
	assert.NoError(service.DB.First(g, guildID).Error)
	assert.Equal(int64(1), g.LiveNum)

	minus = -g.LiveNum - 1
	err = IncreaseLiveNum(g.ID, minus)
	assert.NoError(err)
	assert.NoError(service.DB.First(g, guildID).Error)
	assert.Equal(int64(0), g.LiveNum)
}
