package guild

import (
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// IUser 用户信息接口
type IUser interface {
	UserID() int64
}

const tableName = "guild"

// 是否加密类型
const (
	TypeUnencrypted = iota
	TypeEncrypted
)

// 发票税率
const (
	InvoiceRateUnknown = iota - 2
	InvoiceRateNoIssue
)

// 是否可开发票
const (
	InvoiceRateUnknownStr = "是" // 可开发票，但税率未知
	InvoiceRateNoIssueStr = "否" // 不可开发票
)

// TableName table name
func TableName() string {
	return tableName
}

type Guild struct {
	ID    int64  `gorm:"column:id;primary_key" json:"id"`
	Name  string `gorm:"column:name" json:"name"`
	Intro string `gorm:"column:intro" json:"intro"`

	OwnerName         string `gorm:"column:owner_name" json:"owner_name" encrypt:"create_time"`
	OwnerIDNumber     string `gorm:"column:owner_id_number" json:"owner_id_number" encrypt:"create_time"`
	OwnerIDPeople     string `gorm:"column:owner_id_people" json:"-"`
	OwnerIDPeopleURL  string `gorm:"-" json:"owner_id_people_url"`
	OwnerBackcover    string `gorm:"column:owner_backcover" json:"-"`
	OwnerBackcoverURL string `gorm:"-" json:"owner_backcover_url"`

	Mobile string `gorm:"column:mobile" json:"mobile" encrypt:"create_time"`
	Email  string `gorm:"column:email" json:"email" encrypt:"create_time"`
	QQ     string `gorm:"column:qq" json:"qq"` // TODO: QQ 之后需要调整为加密

	CorporationName    string `gorm:"column:corporation_name" json:"corporation_name" encrypt:"create_time"`
	CorporationAddress string `gorm:"column:corporation_address" json:"corporation_address" encrypt:"create_time"`
	CorporationPhone   string `gorm:"column:corporation_phone" json:"corporation_phone" encrypt:"create_time"`

	BusinessLicenseNumber        string `gorm:"column:business_license_number" json:"business_license_number" encrypt:"create_time"`
	BusinessLicenseFrontcover    string `gorm:"column:business_license_frontcover" json:"-"`
	BusinessLicenseFrontcoverURL string `gorm:"-" json:"business_license_frontcover_url"`
	TaxAccount                   string `gorm:"column:tax_account" json:"tax_account" encrypt:"create_time"`

	BankAccount     string `gorm:"column:bank_account" json:"bank_account" encrypt:"create_time"`
	BankAccountName string `gorm:"column:bank_account_name" json:"bank_account_name"`
	Bank            string `gorm:"column:bank" json:"bank"`
	BankAddress     string `gorm:"column:bank_address" json:"bank_address"`
	BankBranch      string `gorm:"column:bank_branch" json:"bank_branch"`
	InvoiceRate     int    `gorm:"column:invoice_rate;default:-2" json:"invoice_rate"` // 默认值 -2（开具发票且税率未知）

	Type         int   `gorm:"column:type" json:"-"`
	Checked      int   `gorm:"column:checked" json:"checked"`
	UserID       int64 `gorm:"column:user_id" json:"user_id"`
	ApplyTime    int64 `gorm:"column:apply_time" json:"apply_time"`
	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
	LiveNum      int64 `gorm:"live_num" json:"-"` // 公会签约主播数量
}

// Response for api response structure
type Response struct {
	ID     int64  `json:"id"`
	Name   string `json:"name"`
	Intro  string `json:"intro"`
	UserID int64  `json:"user_id"`

	Status        int   `json:"status"`
	ContractStart int64 `json:"contract_start,omitempty"`
	ContractEnd   int64 `json:"contract_end,omitempty"`
	CreateTime    int64 `json:"create_time,omitempty"`
}

// DB the db instance of Guild model
func (Guild) DB() *gorm.DB {
	return service.DB.Table(TableName())
}

// TableName table name
func (Guild) TableName() string {
	return tableName
}

// Encrypt 加密
func (g *Guild) Encrypt() {
	if g.Type != TypeUnencrypted {
		return
	}
	encrypt(g)
	g.Type = TypeEncrypted
}

// Decrypt 解密
func (g *Guild) Decrypt() error {
	if g.Type != TypeEncrypted {
		return nil
	}
	if err := decrypt(g); err != nil {
		return err
	}
	g.Type = TypeUnencrypted
	return nil
}

// TODO: 后面可以考虑迁移到 missevan-go
func encrypt(g interface{}) {
	data := reflect.ValueOf(g).Elem()

	// REVIEW: 加密使用的 iv 从 encrypt tag 里获取
	createTime := data.FieldByName("CreateTime").Int()
	createTimeStr := strconv.FormatInt(createTime, 10)
	for i := 0; i < data.NumField(); i++ {
		tag := data.Type().Field(i).Tag.Get("encrypt")
		value := data.Field(i)
		if value.String() == "" {
			continue
		}

		switch tag {
		case "create_time":
			value.SetString(goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey,
				createTimeStr, value.String()))
		case "fixed":
			value.SetString(goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey,
				config.Conf.Params.Security.SensitiveFixedIVKey, value.String()))
		}
	}
}

// TODO: 后面可以考虑迁移到 missevan-go
func decrypt(g interface{}) error {
	data := reflect.ValueOf(g).Elem()

	createTime := data.FieldByName("CreateTime").Int()
	createTimeStr := strconv.FormatInt(createTime, 10)
	for i := 0; i < data.NumField(); i++ {
		tag := data.Type().Field(i).Tag.Get("encrypt")
		value := data.Field(i)
		if value.String() == "" {
			continue
		}

		switch tag {
		case "create_time":
			origin, err := goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey,
				createTimeStr, value.String())
			if err != nil {
				return err
			}
			value.SetString(origin)
		case "fixed":
			origin, err := goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey,
				config.Conf.Params.Security.SensitiveFixedIVKey, value.String())
			if err != nil {
				return err
			}
			value.SetString(origin)
		}
	}
	return nil
}

// Format 如果公会处于审核通过/解散状态，就调用打码函数
// isLiveFinanceRole【直播财务】银行账户不脱敏
// isGuildAccountAdmin【公会账户管理员】可以查看公会脱敏前的完整信息
func (g *Guild) Format(isLiveFinanceRole, isGuildAccountAdmin bool) {
	g.schemeToURL()
	if isGuildAccountAdmin {
		return
	}
	if g.Checked >= CheckedPass {
		g.OwnerIDNumber = goutil.MosaicString(g.OwnerIDNumber, goutil.MosaicIDNumber)
		if g.OwnerIDPeopleURL != "" {
			g.OwnerIDPeopleURL = "*"
		}
		if g.OwnerBackcoverURL != "" {
			g.OwnerBackcoverURL = "*"
		}
		g.Mobile = goutil.MosaicString(g.Mobile, goutil.MosaicPhoneNumber)
		g.Email = goutil.MosaicString(g.Email, goutil.MosaicEmail)
		g.CorporationPhone = goutil.MosaicString(g.CorporationPhone, goutil.MosaicPhoneNumber)
		if g.BusinessLicenseFrontcoverURL != "" {
			g.BusinessLicenseFrontcoverURL = "*"
		}
		if !isLiveFinanceRole {
			g.BankAccount = goutil.MosaicString(g.BankAccount, goutil.MosaicBankCardNumber)
		}
	}
}

// schemeToURL 数据库获取的图片自定义协议地址转成 HTTP 协议地址
func (g *Guild) schemeToURL() {
	if g.OwnerBackcover != "" {
		g.OwnerBackcoverURL = service.Storage.Parse(g.OwnerBackcover)
	}
	if g.OwnerIDPeople != "" {
		g.OwnerIDPeopleURL = service.Storage.Parse(g.OwnerIDPeople)
	}
	if g.BusinessLicenseFrontcover != "" {
		g.BusinessLicenseFrontcoverURL = service.Storage.Parse(g.BusinessLicenseFrontcover)
	}
}

// BeforeSave gorm hook
func (g *Guild) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if service.DB.NewRecord(g) {
		g.CreateTime = nowTime
	}
	g.ModifiedTime = nowTime
	g.Encrypt()
	return
}

// BeforeUpdate automatically update field modified_time
func (g *Guild) BeforeUpdate(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", goutil.TimeNow().Unix())
}

// Find 根据 ID 查询公会
func Find(id int64, checked ...int) (*Guild, error) {
	g := new(Guild)
	db := service.DB.Where("id = ?", id)
	// TODO: 后续调整为默认查询审核通过的公会
	if len(checked) != 0 {
		db = db.Where("checked IN (?)", checked)
	}
	err := db.Find(g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	err = g.Decrypt()
	if err != nil {
		return nil, err
	}
	return g, nil
}

// SwitchChecked 将 ID 所属公会的状态改成 changeCheckedTo
// NOTICE: 如果公会不存在，gorm.IsRecordNotFoundError(err) 为 true
func SwitchChecked(tx *gorm.DB, guildID int64, changeCheckedTo int) (success bool, ownerID int64, err error) {
	g := new(Guild)
	err = service.DB.Select("id, checked, user_id").First(g, guildID).Error
	if err != nil {
		return
	}
	ownerID = g.UserID
	// 目前状态和想修改的状态一致
	if g.Checked == changeCheckedTo {
		success = true
		return
	}
	// 判断是否可以从原状态修改成现状态
	switch changeCheckedTo {
	case CheckedReject:
		success = g.Checked == CheckedChecking
	case CheckedChecking:
		success = g.Checked == CheckedReject
	case CheckedPass:
		success = g.Checked == CheckedChecking
	case CheckedDissolved:
		success = g.Checked == CheckedPass
	default:
		panic(fmt.Sprintf("unsupported status: %d", changeCheckedTo))
	}
	if !success {
		return
	}
	db := tx
	if db == nil {
		db = service.DB
	}
	err = db.Model(g).Where("id = ? AND checked = ?", g.ID, g.Checked).Update("checked", changeCheckedTo).Error
	if err != nil {
		success = false
	}
	return
}

// IDName 公会 ID 和公会名称
type IDName struct {
	ID   int64  `gorm:"column:id;primary_key" json:"id"`
	Name string `gorm:"column:name" json:"name"`

	// 公会长的用户 ID
	UserID int64 `gorm:"column:user_id" json:"user_id"`
}

// SimpleInfo 获取公会简要信息（公会 ID 及名称）
func SimpleInfo(guildID int64) (*IDName, error) {
	g := new(IDName)
	err := service.DB.Table(TableName()).Select("id, name").
		Where("id = ? AND checked = ?", guildID, CheckedPass).First(g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}

	return g, nil
}

// SimpleInfoByUserID 根据会长 ID 获取公会简要信息（公会 ID 及名称）
func SimpleInfoByUserID(userID int64) (*IDName, error) {
	g := new(IDName)
	err := service.DB.Table(TableName()).Select("id, name").
		Where("user_id = ? AND checked = ?", userID, CheckedPass).First(g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}

	return g, nil
}

// ExistsByName 查询公会名称是否存在
func ExistsByName(name string) (bool, error) {
	g := new(Guild)
	err := service.DB.Select("id").First(g, "name = ? AND checked IN (?)",
		name, []int{CheckedPass, CheckedChecking}).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// InvoiceRateStr 获取发票税率
func InvoiceRateStr(invoiceRate int) string {
	switch invoiceRate {
	case InvoiceRateUnknown:
		return InvoiceRateUnknownStr
	case InvoiceRateNoIssue:
		return InvoiceRateNoIssueStr
	default:
		panic(fmt.Sprintf("未知类型: %d", invoiceRate))
	}
}

// FindSimpleMap 获取公会简要信息（公会 ID 及名称）
func FindSimpleMap(guildIDs []int64) (map[int64]IDName, error) {
	var g []IDName
	err := service.DB.Table(TableName()).Select("id, name, user_id").
		Where("id IN (?) AND checked = ?", guildIDs, CheckedPass).Find(&g).Error
	if err != nil {
		return nil, err
	}
	guildNamesMap := goutil.ToMap(g, "ID").(map[int64]IDName)
	return guildNamesMap, nil
}

// FindIDsByGuildNameLike 根据公会名称模糊查询
func FindIDsByGuildNameLike(guildName string) (ids []int64, err error) {
	if guildName == "" {
		return
	}
	err = service.DB.Table(TableName()).
		Select("id").
		Where("name LIKE ? AND checked = ?", servicedb.ToLikeStr(guildName), CheckedPass).
		Pluck("id", &ids).Error
	return
}

// SetOperatePermission 主播、公会长、经纪人重要操作添加临时（2h）操作权限，当以下缓存可用时，此次登录拥有权限
func SetOperatePermission(userID int64, token string) error {
	key := keys.GuildUserCritialOperation1.Format(userID)
	return service.Redis.Set(key, goutil.MD5(token), 2*time.Hour).Err()
}

// CheckOperatePermission 检查用户操作（签约、续约、解约）权限
func CheckOperatePermission(userID int64, token string) (bool, error) {
	key := keys.GuildUserCritialOperation1.Format(userID)
	dbToken, err := service.Redis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return false, err
	}
	return dbToken == goutil.MD5(token), nil
}

// SearchByGuildName 通过公会名称查找公会
func SearchByGuildName(guildName string) ([]*Guild, error) {
	var gs []*Guild
	err := Guild{}.DB().Select("id, name").
		Where("name LIKE ?", servicedb.ToLikeStr(guildName)).Find(&gs).Error
	if err != nil {
		return nil, err
	}

	return gs, nil
}

// IncreaseLiveNum 修改公会的主播数
func IncreaseLiveNum(guildID, num int64, tx ...*gorm.DB) error {
	if num == 0 {
		return nil
	}

	var db *gorm.DB
	if len(tx) != 0 && tx[0] != nil {
		db = tx[0]
	} else {
		db = Guild{}.DB()
	}

	nowStamp := goutil.TimeNow().Unix()
	db = db.Table(TableName()).Where("id = ?", guildID)
	if num > 0 {
		db = db.Update(map[string]interface{}{
			"live_num":      gorm.Expr("live_num + ?", num),
			"modified_time": nowStamp,
		})
	} else {
		db = db.Update(map[string]interface{}{
			"live_num":      gorm.Expr("GREATEST(live_num, ?) - ?", -num, -num),
			"modified_time": nowStamp,
		})
	}

	if err := db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		logger.WithField("guild_id", guildID).Error("公会主播数更新失败")
		// PASS
	}

	return nil
}
