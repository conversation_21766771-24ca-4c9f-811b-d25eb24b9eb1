package recommend

import (
	"database/sql"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// GuildRecommendVacancy 公会热门位推荐次数表
type GuildRecommendVacancy struct {
	ID             int64 `gorm:"column:id;primary_key"`
	CreateTime     int64 `gorm:"column:create_time"`
	ModifiedTime   int64 `gorm:"column:modified_time"`
	Position       int   `gorm:"column:position"`
	GuildID        int64 `gorm:"column:guild_id"`
	InitialVacancy int64 `gorm:"column:initial_vacancy"` // 根据流水生成的本期次数
	EditedVacancy  int64 `gorm:"column:edited_vacancy"`  // 运营调整后的本期次数
	Vacancy        int64 `gorm:"column:vacancy"`         // 剩余推荐次数
	StartTime      int64 `gorm:"column:start_time"`
	EndTime        int64 `gorm:"column:end_time"`
}

// DB the db instance of GuildAgentCreator model
func (g GuildRecommendVacancy) DB() *gorm.DB {
	return service.LiveDB.Table(g.TableName())
}

// TableName of Model
func (GuildRecommendVacancy) TableName() string {
	return "guild_recommend_vacancy"
}

// BeforeCreate automatically set field modified_time
func (g *GuildRecommendVacancy) BeforeCreate(scope *gorm.Scope) (err error) {
	now := goutil.TimeNow().Unix()
	err = scope.SetColumn("create_time", now)
	if err == nil {
		err = scope.SetColumn("modified_time", now)
	}
	return err
}

// BeforeUpdate automatically update field modified_time
func (g *GuildRecommendVacancy) BeforeUpdate(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", goutil.TimeNow().Unix())
}

// VacancyLeft 获得公会当期剩余推荐次数
func VacancyLeft(db *gorm.DB, guildID int64, startTime time.Time, position int) (int64, error) {
	var vacancy int64
	err := db.Model(&GuildRecommendVacancy{}).Select("vacancy").
		Where("guild_id = ? AND position = ? AND start_time <= ? AND end_time > ?",
			guildID, position, startTime.Unix(), startTime.Unix()).Row().Scan(&vacancy)
	if err != nil && err != sql.ErrNoRows {
		return 0, err
	}
	return vacancy, nil
}

// TimesUsed 获得公会当期的已用推荐次数
func TimesUsed(db *gorm.DB, guildIDs []int64, startTime time.Time, position int) (map[int64]int64, error) {
	var vacancys []GuildRecommendVacancy
	vacancyDB := db.Model(&GuildRecommendVacancy{}).
		Where("guild_id IN (?) AND position = ? AND start_time <= ? AND end_time > ?",
			guildIDs, position, startTime.Unix(), startTime.Unix())
	err := vacancyDB.Select("guild_id, edited_vacancy, vacancy").Scan(&vacancys).Error
	if err != nil {
		return nil, err
	}
	r := make(map[int64]int64, len(vacancys))
	for _, v := range vacancys {
		if v.EditedVacancy < v.Vacancy {
			logger.Errorf("公会 %d 的热门位推荐次数有误", v.GuildID)
			// PASS
			r[v.GuildID] = 0
			continue
		}
		r[v.GuildID] = v.EditedVacancy - v.Vacancy
	}

	return r, nil
}

// UpdateByGuildAndPosition 根据公会 ID 和位置更新
func UpdateByGuildAndPosition(db *gorm.DB, guildID int64, startTime time.Time, position int, updates map[string]interface{}) error {
	// TODO: 考虑使用 modified_time 做乐观锁
	db = db.Model(&GuildRecommendVacancy{}).
		Where("guild_id = ? AND position = ? AND start_time <= ? AND end_time > ?",
			guildID, position, startTime.Unix(), startTime.Unix()).
		Update(updates)
	if err := db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return servicedb.ErrNoRowsAffected
	}
	return nil
}

// DecreaseVacancy 扣除公会推荐次数，扣除一次
func DecreaseVacancy(db *gorm.DB, guildID int64, startTime time.Time, position int) error {
	db = db.Model(&GuildRecommendVacancy{}).
		Where("guild_id = ? AND position = ? AND start_time <= ? AND end_time > ?",
			guildID, position, startTime.Unix(), startTime.Unix()).
		UpdateColumns(map[string]interface{}{
			"vacancy": servicedb.SubSatExpr("vacancy", 1),
		})
	if err := db.Error; err != nil {
		return err
	}
	if db.RowsAffected == 0 {
		return servicedb.ErrNoRowsAffected
	}
	return nil
}
