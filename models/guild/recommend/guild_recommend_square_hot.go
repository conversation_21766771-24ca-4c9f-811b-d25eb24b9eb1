package recommend

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
)

// GuildRecommendSquareHot 公会直播广场热门推荐表
type GuildRecommendSquareHot struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`
	Position     int   `gorm:"column:position"`
	CreatorID    int64 `gorm:"column:creator_id"`
	GuildID      int64 `gorm:"column:guild_id"`
	OperatorID   int64 `gorm:"column:operator_id"`
	StartTime    int64 `gorm:"column:start_time"`
	EndTime      int64 `gorm:"column:end_time"`
}

// TableName of Model
func (GuildRecommendSquareHot) TableName() string {
	return "guild_recommend_square_hot"
}

// DB the db instance of GuildRecommendSquareHot model
func (g GuildRecommendSquareHot) DB() *gorm.DB {
	return service.LiveDB.Table(g.TableName())
}
