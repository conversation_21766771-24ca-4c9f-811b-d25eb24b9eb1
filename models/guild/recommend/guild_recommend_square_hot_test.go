package recommend

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// 用于 service 单例的初始化
func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestGuildRecommendSquareHotTagKey(t *testing.T) {
	var g GuildRecommendSquareHot

	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(g, "id", "create_time",
		"modified_time", "position", "creator_id", "guild_id", "operator_id", "start_time", "end_time")
}

func TestGuildRecommendSquareHotTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("guild_recommend_square_hot", GuildRecommendSquareHot{}.TableName())
}
