package recommend

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestDBTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("guild_recommend_vacancy", GuildRecommendVacancy{}.TableName())
}

func TestVacancyLeft(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildID := int64(10238)
	vacancy, err := VacancyLeft(service.LiveDB, guildID, time.Unix(9, 0), 8)
	require.NoError(err)
	assert.Zero(vacancy)
	r := GuildRecommendVacancy{
		Position:  8,
		GuildID:   guildID,
		Vacancy:   12,
		StartTime: 19,
		EndTime:   20,
	}
	require.NoError(service.LiveDB.Create(&r).Error)
	vacancy, err = VacancyLeft(service.LiveDB, guildID, time.Unix(19, 0), 8)
	require.NoError(err)
	assert.Equal(int64(12), vacancy)
}

func TestTimesUsed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildID1 := int64(10249)
	guildID2 := int64(10250)
	vacancys, err := TimesUsed(service.LiveDB, []int64{guildID1}, time.Unix(9, 0), 8)
	require.NoError(err)
	assert.Equal(int64(0), vacancys[guildID1])
	r1 := GuildRecommendVacancy{
		Position:      8,
		GuildID:       guildID1,
		EditedVacancy: 12,
		Vacancy:       1,
		StartTime:     29,
		EndTime:       30,
	}
	require.NoError(service.LiveDB.Create(&r1).Error)
	r2 := GuildRecommendVacancy{
		Position:      8,
		GuildID:       guildID2,
		EditedVacancy: 12,
		Vacancy:       13,
		StartTime:     29,
		EndTime:       30,
	}
	require.NoError(service.LiveDB.Create(&r2).Error)
	vacancys, err = TimesUsed(service.LiveDB, []int64{guildID1, guildID2}, time.Unix(29, 0), 8)
	require.NoError(err)
	require.Len(vacancys, 2)
	assert.Equal(int64(11), vacancys[guildID1])
	assert.Equal(int64(0), vacancys[guildID2])
}

func TestUpdateByGuildAndPosition(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildID := int64(10239)
	err := UpdateByGuildAndPosition(service.LiveDB, guildID, time.Unix(9, 0), 8, map[string]interface{}{"edited_vacancy": 2})
	require.Equal(servicedb.ErrNoRowsAffected, err)
	r := GuildRecommendVacancy{
		Position:      8,
		GuildID:       guildID,
		EditedVacancy: 12,
		StartTime:     19,
		EndTime:       20,
	}
	require.NoError(service.LiveDB.Create(&r).Error)
	err = UpdateByGuildAndPosition(service.LiveDB, guildID, time.Unix(19, 0), 8, map[string]interface{}{"edited_vacancy": 8})
	require.NoError(err)
	var vacancy int64
	require.NoError(service.LiveDB.Table(GuildRecommendVacancy{}.TableName()).Select("edited_vacancy").Where("id = ?", r.ID).Row().Scan(&vacancy))
	assert.Equal(int64(8), vacancy)
}

func TestDecreaseVacancy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	guildID := int64(10240)
	err := DecreaseVacancy(service.LiveDB, guildID, time.Unix(9, 0), 8)
	require.Equal(servicedb.ErrNoRowsAffected, err)
	r := GuildRecommendVacancy{
		Position:       8,
		GuildID:        guildID,
		InitialVacancy: 1,
		EditedVacancy:  1,
		Vacancy:        1,
		StartTime:      19,
		EndTime:        20,
	}
	require.NoError(service.LiveDB.Create(&r).Error)
	err = DecreaseVacancy(service.LiveDB, guildID, time.Unix(19, 0), 8)
	require.NoError(err)
	var vacancy int64
	require.NoError(service.LiveDB.Table(GuildRecommendVacancy{}.TableName()).Select("vacancy").Where("id = ?", r.ID).Row().Scan(&vacancy))
	assert.Equal(int64(0), vacancy)
	err = DecreaseVacancy(service.LiveDB, guildID, time.Unix(19, 0), 8)
	require.NoError(err)
	require.NoError(service.LiveDB.Table(GuildRecommendVacancy{}.TableName()).Select("vacancy").Where("id = ?", r.ID).Row().Scan(&vacancy))
	assert.Equal(int64(0), vacancy)
}
