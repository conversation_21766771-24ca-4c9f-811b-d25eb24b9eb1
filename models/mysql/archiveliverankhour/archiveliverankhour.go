package archiveliverankhour

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// Type 榜单类型
const (
	TypeCreatorsRank  = iota + 1 // 主播榜
	TypeRoomUsersRank            // 用户贡献榜
)

// ArchiveLiveRankHour 直播主播小时榜归档
type ArchiveLiveRankHour struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Type         int    `gorm:"column:type"`
	RankTime     string `gorm:"column:rank_time"`
	RoomID       int64  `gorm:"column:room_id"`
	CreatorID    int64  `gorm:"column:creator_id"`
	UserID       int64  `gorm:"column:user_id"`
	Score        int64  `gorm:"column:score"`
	Rank         int    `gorm:"column:rank"`
}

// TableName of model "archive_live_rank_hour"
func (ArchiveLiveRankHour) TableName() string {
	return "archive_live_rank_hour"
}

// BatchInsert 批量插入小时榜记录
func BatchInsert(records []ArchiveLiveRankHour) error {
	return servicedb.BatchInsert(service.LiveDB, ArchiveLiveRankHour{}.TableName(), records)
}
