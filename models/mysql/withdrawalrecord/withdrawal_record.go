package withdrawalrecord

import (
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TableName table name
func TableName() string {
	return "withdrawal_record"
}

// 提现的收益类型
const (
	// 旧直播收益提现
	TypeWithDrawLive = 2
	// 新直播收益提现
	TypeWithDrawLiveNew = 5
	// 公会主播收益提现
	TypeWithDrawGuildCreator = 6
)

// 提现状态
const (
	StatusCreate  int = iota + 1 // 申请中
	StatusConfirm                // 确认打款
	StatusInvalid                // 拒绝打款
)

// WithdrawalRecord model for table app_missevan.withdrawal_record
type WithdrawalRecord struct {
	ID         int64   `gorm:"column:id;primary_key" json:"id"`
	UserID     int64   `gorm:"column:user_id" json:"user_id"`
	AccountID  int64   `gorm:"column:account_id" json:"account_id"`
	Profit     float64 `gorm:"column:profit" json:"profit"`
	Status     int     `gorm:"column:status" json:"status"`
	Type       int     `gorm:"column:type" json:"type"`
	CreateTime int64   `gorm:"column:create_time" json:"-"`
}

// DB the db instance of WithdrawalRecord model
func (WithdrawalRecord) DB() *gorm.DB {
	return service.PayDB.Table(WithdrawalRecord{}.TableName())
}

// TableName for current model
func (WithdrawalRecord) TableName() string {
	return TableName()
}

// WithdrawRecordData 用户提现记录
type WithdrawRecordData struct {
	ID                int64   `gorm:"column:id;primary_key" json:"id"`
	Account           string  `gorm:"column:account" json:"account"`
	Mobile            string  `gorm:"column:mobile" json:"mobile"`
	RealName          string  `gorm:"column:real_name" json:"real_name"`
	IDNumber          string  `gorm:"column:id_number" json:"id_number"`
	Bank              string  `gorm:"column:bank" json:"bank"`
	BankAccount       string  `gorm:"column:bank_account" json:"bank_account"`
	Profit            float64 `gorm:"column:profit" json:"profit"`
	Status            int     `gorm:"column:status" json:"status"`
	AccountType       int     `gorm:"column:type" json:"-"`
	AccountCreateTime int64   `gorm:"column:account_create_time" json:"-"`
	CreateTime        int64   `gorm:"column:create_time" json:"create_time"`
	AccountID         int64   `gorm:"column:account_id" json:"-"`
}

// GetWithdrawRecord 获取用户提现记录，根据 ID 降序排序
func GetWithdrawRecord(userID int64, recordType int, p, pageSize int64) (res []*WithdrawRecordData, pa goutil.Pagination, err error) {
	db := WithdrawalRecord{}.DB().Select("id, profit, create_time, status, account_id").
		Where("user_id = ? AND type = ?", userID, recordType)

	var totalCount int64
	err = db.Count(&totalCount).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}

	pa = goutil.MakePagination(totalCount, p, pageSize)
	if !pa.Valid() {
		return []*WithdrawRecordData{}, pa, nil
	}
	db = pa.ApplyTo(db)
	err = db.Order("id DESC").Scan(&res).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	if len(res) > 0 {
		accountIDs := make([]int64, len(res))
		for i := range res {
			accountIDs[i] = res[i].AccountID
		}
		accountIDs = util.Uniq(accountIDs)
		var accInfos []accountinfo.AccountInfo
		err = accountinfo.AccountInfo{}.DB().
			Select("id, account, mobile, real_name, id_number, bank, bank_account, type, create_time").
			Where("id IN (?)", accountIDs).Scan(&accInfos).Error
		if err != nil {
			return nil, goutil.Pagination{}, err
		}

		// 敏感数据脱敏处理
		for i := range res {
			// TODO: 调整为直接调用 AccountInfo 里的方法进行 Decrypt
			for j := range accInfos {
				if res[i].AccountID == accInfos[j].ID {
					res[i].Account = accInfos[j].Account
					res[i].Mobile = accInfos[j].Mobile
					res[i].RealName = accInfos[j].RealName
					res[i].IDNumber = accInfos[j].IDNumber
					res[i].Bank = accInfos[j].Bank
					res[i].BankAccount = accInfos[j].BankAccount
					res[i].AccountType = accInfos[j].Type
					res[i].AccountCreateTime = accInfos[j].CreateTime
					break
				}
			}

			err = res[i].MosaicSensitiveData()
			if err != nil {
				return nil, goutil.Pagination{}, err
			}
		}
	}
	return res, pa, nil
}

// MosaicSensitiveData 敏感数据脱敏处理
func (res *WithdrawRecordData) MosaicSensitiveData() error {
	// 真实姓名脱敏处理
	res.RealName = goutil.MosaicString(res.RealName, goutil.MosaicRealName)
	iv := strconv.FormatInt(res.AccountCreateTime, 10)
	if res.AccountType == accountinfo.TypeBank {
		// 银行卡账号解密处理
		originBankAccount, err := goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, res.BankAccount)
		if err != nil {
			return err
		}
		// 银行卡账号脱敏处理
		mosaicBankAccount := goutil.MosaicString(originBankAccount, goutil.MosaicBankCardNumber)
		res.BankAccount = mosaicBankAccount
	} else if res.AccountType == accountinfo.TypeAlipay {
		// 支付宝账号解密处理
		originAccount, err := goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, res.Account)
		if err != nil {
			return err
		}
		var mosaicAccount string
		// 支付宝账号脱敏处理
		if goutil.IsEmail(originAccount) {
			mosaicAccount = goutil.MosaicString(originAccount, goutil.MosaicEmail)
		} else {
			mosaicAccount = goutil.MosaicString(originAccount, goutil.MosaicPhoneNumber)
		}
		res.Account = mosaicAccount
	}
	// 手机号解密处理
	mobile, err := goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, res.Mobile)
	if err != nil {
		return err
	}
	// 手机号脱敏处理
	res.Mobile = goutil.MosaicString(mobile, goutil.MosaicPhoneNumber)
	// 身份证号解密处理
	originIDNumber, err := goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, res.IDNumber)
	if err != nil {
		return err
	}
	// 身份证号脱敏处理
	mosaicIDNumber := goutil.MosaicString(originIDNumber, goutil.MosaicIDNumber)
	res.IDNumber = mosaicIDNumber
	return nil
}
