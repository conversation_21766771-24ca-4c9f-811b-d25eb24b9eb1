package withdrawalrecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	m.Run()
}

func TestMosaicSensitiveData(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	resp := WithdrawRecordData{
		ID:                1,
		Account:           "",
		Mobile:            goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, "**********", "**********"),
		RealName:          "test_real_name",
		IDNumber:          goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, "**********", "123456789012345678"),
		Bank:              "test_bank",
		BankAccount:       goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, "**********", "6228480402564890018"),
		Profit:            2000,
		Status:            1,
		AccountType:       accountinfo.TypeBank,
		AccountCreateTime: **********,
		CreateTime:        **********,
	}
	err := resp.MosaicSensitiveData()
	require.NoError(err)
	assert.Equal(goutil.MosaicString("123456789012345678", goutil.MosaicIDNumber), resp.IDNumber)
	assert.Equal(goutil.MosaicString("6228480402564890018", goutil.MosaicBankCardNumber), resp.BankAccount)
	assert.Equal(goutil.MosaicString("**********", goutil.MosaicPhoneNumber), resp.Mobile)
}
