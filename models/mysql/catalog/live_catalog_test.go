package catalog

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestLiveCatalogTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveCatalog{}, "id", "sort_order", "parent_id", "catalog_name", "catalog_name_second", "content")
	kc.Check(icons{}, "icon_url", "dark_icon_url", "web_icon_url")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LiveCatalog{}, "catalog_id", "catalog_name", "color", "sub_catalogs",
		"icon_url", "dark_icon_url", "web_icon_url")
	kc.Check(icons{}, "icon_url", "dark_icon_url", "web_icon_url")
}

func TestLiveCatalogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	catalogs1, err := LiveCatalogs(false)
	require.NoError(err)
	assert.Len(catalogs1, 4)
	// 从缓存读取
	str, err := json.Marshal(catalogs1[:1])
	require.NoError(err)
	key := "live-service:new-catalogs:0"
	err = service.LRURedis.Set(key, str, 10*time.Minute).Err()
	require.NoError(err)
	defer service.LRURedis.Del(key)
	catalogs2, err := LiveCatalogs(false)
	require.NoError(err)
	assert.Len(catalogs2, 1)
	assert.Equal(catalogs1[0].ID, catalogs2[0].ID)
	assert.Equal(catalogs1[0].CatalogName, catalogs2[0].CatalogName)

	catalogs1, err = LiveCatalogs(true)
	require.NoError(err)
	assert.Greater(len(catalogs1), 4)
	m, err := AllLiveCatalogsMap()
	require.NoError(err)
	for i := range catalogs1 {
		lc, ok := m[catalogs1[i].ID]
		assert.True(ok)
		catalogs1[i].CatalogName = "test"
		assert.NotEqual(lc.CatalogName, "test")
	}
	// 从缓存读取
	key = "live-service:new-catalogs:1"
	str, err = json.Marshal(catalogs1[:1])
	require.NoError(err)
	err = service.LRURedis.Set(key, str, 10*time.Minute).Err()
	require.NoError(err)
	defer service.LRURedis.Del(key)
	catalogs2, err = LiveCatalogs(true)
	require.NoError(err)
	assert.Len(catalogs2, 1)
	assert.Equal(catalogs1[0].ID, catalogs2[0].ID)
	assert.Equal(catalogs1[0].CatalogName, catalogs2[0].CatalogName)
}

func TestLiveSubCatalogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	subCatalogs, err := LiveSubCatalogs(false)
	require.NoError(err)
	assert.Len(subCatalogs, 3)
}

func TestAllLiveCatalogsWithSubMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	catalogs, err := AllLiveCatalogsWithSubMap(true)
	require.NoError(err)

	assert.NotNil(catalogs[104])
	assert.NotNil(catalogs[118])
	assert.NotNil(catalogs[119])

	catalogs, err = AllLiveCatalogsWithSubMap(false)
	require.NoError(err)
	assert.Nil(catalogs[119])
}

func TestSubCatalogIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids, err := SubCatalogIDs(104, true)
	require.NoError(err)
	assert.Len(ids, 2)

	ids, err = SubCatalogIDs(104, false)
	require.NoError(err)
	assert.Len(ids, 1)

	// 隐藏一级分区
	ids, err = SubCatalogIDs(111, false)
	require.NoError(err)
	assert.Nil(ids)

	// 不存的一级分区
	ids, err = SubCatalogIDs(99999, false)
	require.NoError(err)
	assert.Nil(ids)
}

func TestFindCatalog(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	catalogs, err := FindCatalog(false)
	require.NoError(err)
	require.Len(catalogs, 4)
	require.Len(catalogs[0].SubCatalogs, 1)
	subCatalog := catalogs[0].SubCatalogs[0]
	assert.Nil(subCatalog.IconURL)
	assert.Len(catalogs[1].SubCatalogs, 2)
	// 没有三级分区
	assert.Nil(catalogs[0].SubCatalogs[0].SubCatalogs)
}

func TestCatalogExists(t *testing.T) {
	assert := assert.New(t)
	ok := LiveCatalogExists(104)
	assert.True(ok)
	ok = LiveCatalogExists(98)
	assert.False(ok)
}

func TestDelLiveCatalogCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyLiveCatalogs1.Format(0)
	_, err := service.LRURedis.Set(key, "[]", 10*time.Second).Result()
	require.NoError(err)

	DelLiveCatalogCache(false)
	_, err = service.LRURedis.Get(key).Result()
	assert.Equal(redis.Nil, err)
}
