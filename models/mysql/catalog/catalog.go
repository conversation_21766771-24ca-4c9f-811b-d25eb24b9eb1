package catalog

import (
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
)

const tableName = "catalog"

// 分区类别
const (
	CatalogIDDrama      = 5 // 广播剧分区
	CatalogIDMusicRadio = 8 // 音乐分区
)

// TableName table name
func TableName() string {
	return tableName
}

// FindSubCatalogIDs 根据 ParentID 获取对应的子 ID
// TODO: 应该完整的构建分类树并生成缓存
func FindSubCatalogIDs(parentID int64) ([]int64, error) {
	var ids []int64
	err := service.DB.Table(tableName).Where("parent_id = ? AND status_is = ?", parentID, yes).
		Pluck("id", &ids).Error
	if err != nil {
		return nil, err
	}
	return ids, nil
}

// DramaSubCatalogIDs 广播剧子分区 ID
func DramaSubCatalogIDs() []int64 {
	key := keys.KeyDramaSubCatalogIDs0.Format()
	v, ok := service.Cache5Min.Get(key)
	if ok {
		cache := v.([]int64)
		ret := make([]int64, len(cache))
		copy(ret, cache)
		return ret
	}
	ret, err := FindSubCatalogIDs(CatalogIDDrama)
	if err != nil {
		logger.Error(err)
		return []int64{}
	}
	cache := make([]int64, len(ret))
	copy(cache, ret)
	service.Cache5Min.SetDefault(key, cache)
	return ret
}
