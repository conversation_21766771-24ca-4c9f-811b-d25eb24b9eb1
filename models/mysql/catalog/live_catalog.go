package catalog

import (
	"encoding/json"
	"sort"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Alias
const (
	AliasLive = "live"
)

const (
	yes = "Y"
	no  = "N" //nolint:deadcode,varcheck
)

// 直播分区
const (
	CatalogIDMusic        = 104 // 音乐分区
	CatalogIDPia          = 105 // 配音分区
	CatalogIDLiveHypnosis = 115 // 放松分区
	CatalogIDEmotion      = 116 // 情感分区
	CatalogIDAncient      = 122 // 古风分区
)

// LiveCatalog 直播间用的特殊的 catalog，将 catalog_name_second 作为直播间 catalog 的颜色使用
type LiveCatalog struct {
	ID          int64  `gorm:"column:id;primary_key" json:"catalog_id"`
	ParentID    int64  `gorm:"column:parent_id" json:"-"`
	SortOrder   int    `gorm:"column:sort_order" json:"-"`
	CatalogName string `gorm:"column:catalog_name" json:"catalog_name"`
	// 数据库字段 catalog_name_second 作为直播间 catalog 的颜色来使用
	Color string `gorm:"column:catalog_name_second" json:"color"`
	// 数据库字段 content 作为直播间 catalog 的图标来使用
	Content  string `gorm:"column:content" json:"-"`
	StatusIs string `gorm:"status_is" json:"-"`

	// 子分区
	SubCatalogs []*LiveCatalog `gorm:"foreignkey:parent_id;association_foreignkey:id" json:"sub_catalogs,omitempty"`

	// 子分区没有下列 icon
	IconURL     *string `gorm:"-" json:"icon_url,omitempty"`
	DarkIconURL *string `gorm:"-" json:"dark_icon_url,omitempty"`
	WebIconURL  *string `gorm:"-" json:"web_icon_url,omitempty"`
}

// icons 图标
type icons struct {
	IconURL     string `json:"icon_url"`
	DarkIconURL string `json:"dark_icon_url"`
	WebIconURL  string `json:"web_icon_url"`
}

// TableName table name
func (LiveCatalog) TableName() string {
	return tableName
}

// AfterFind Parse URL
func (catalog *LiveCatalog) AfterFind() error {
	if catalog.Content == "" {
		// 子分区不配置 icon
		return nil
	}
	var i icons
	err := json.Unmarshal([]byte(catalog.Content), &i)
	if err != nil {
		logger.WithField("catalog_id", catalog.ID).Error(err)
		return nil
	}

	catalog.IconURL = goutil.NewString(storage.ParseSchemeURL(i.IconURL))
	catalog.DarkIconURL = goutil.NewString(storage.ParseSchemeURL(i.DarkIconURL))
	catalog.WebIconURL = goutil.NewString(storage.ParseSchemeURL(i.WebIconURL))
	return nil
}

// LiveCatalogs 查询直播所有分类，根据 sort_order 倒序排序
func LiveCatalogs(all bool) ([]*LiveCatalog, error) {
	key := keys.KeyLiveCatalogs1.Format(util.BoolToInt(all))
	v, err := service.LRURedis.Get(key).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
		// PASS
	} else if v != "" {
		var list []*LiveCatalog
		if err = json.Unmarshal([]byte(v), &list); err == nil {
			return list, nil
		}
		logger.Error(err)
		// PASS
	}
	list, err := FindCatalog(all)
	if err != nil {
		return nil, err
	}

	b, err := json.Marshal(list)
	if err != nil {
		logger.Error(err)
		return list, nil
	}

	err = service.LRURedis.Set(key, b, 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return list, nil
}

// LiveSubCatalogs 查询所有二级分区
func LiveSubCatalogs(all bool) ([]*LiveCatalog, error) {
	// TODO: 增加缓存，json 标签中不存在 parent_id，直接加 json 标签会在 meta/data 接口返回这个字段
	list := make([]*LiveCatalog, 0, 16)
	catalogs, err := FindCatalog(all)
	if err != nil {
		return nil, err
	}
	for _, catalog := range catalogs {
		list = append(list, catalog.SubCatalogs...)
	}
	sort.Slice(list, func(i, j int) bool {
		return list[i].SortOrder > list[j].SortOrder
	})
	return list, nil
}

// AllLiveCatalogsMap 全部的 catalog 转 map
// map 是 map[catalogID]*LiveCatalog
func AllLiveCatalogsMap() (map[int64]*LiveCatalog, error) {
	l, err := LiveCatalogs(true)
	if err != nil {
		return nil, err
	}
	m := make(map[int64]*LiveCatalog, len(l))
	for i := range l {
		m[l[i].ID] = l[i]
	}
	return m, nil
}

// AllLiveCatalogsWithSubMap 全部的 catalog 包括二级分区 转 map
// map 是 map[catalogID]*LiveCatalog
func AllLiveCatalogsWithSubMap(all bool) (map[int64]*LiveCatalog, error) {
	l, err := LiveCatalogs(all)
	if err != nil {
		return nil, err
	}
	m := make(map[int64]*LiveCatalog, len(l))
	for _, item := range l {
		m[item.ID] = item
		for _, sub := range item.SubCatalogs {
			sub.ParentID = item.ID
			m[sub.ID] = sub
		}
	}
	return m, nil
}

// SubCatalogIDs 获取一个未隐藏一级分区下的所有未隐藏二级分区
func SubCatalogIDs(catalogID int64, withParent bool) ([]int64, error) {
	l, err := LiveCatalogs(false)
	if err != nil {
		return nil, err
	}
	var catalog *LiveCatalog
	for _, c := range l {
		if c.ID == catalogID {
			catalog = c
			break
		}
	}
	if catalog == nil {
		return nil, nil
	}
	length := len(catalog.SubCatalogs)
	if withParent {
		length++
	}
	if length == 0 {
		return nil, nil
	}

	catalogIDs := make([]int64, 0, length)
	for _, subCatalog := range catalog.SubCatalogs {
		catalogIDs = append(catalogIDs, subCatalog.ID)
	}
	if withParent {
		catalogIDs = append(catalogIDs, catalog.ID)
	}
	return catalogIDs, nil
}

// FindCatalog 查询直播所有分类，根据 sort_order 倒序排序，不使用缓存
func FindCatalog(all bool) ([]*LiveCatalog, error) {
	subQuery := service.DB.Select("id").
		Table(TableName()).Where("catalog_name_alias = ?", AliasLive).Limit(1).SubQuery()
	db := service.DB.Select("id, sort_order, catalog_name, catalog_name_second, content").
		Where("parent_id = ?", subQuery)
	if !all {
		db = db.Where("status_is = ?", yes)
	}
	var res []*LiveCatalog
	err := db.Order("sort_order DESC").Preload("SubCatalogs", func(db *gorm.DB) *gorm.DB {
		if !all {
			db = db.Where("status_is = ?", yes)
		}
		return db.Order("sort_order DESC")
	}).Find(&res).Error
	return res, err
}

// LiveCatalogExists 判断分区 ID 是否存在
func LiveCatalogExists(catalogID int64) bool {
	m, err := AllLiveCatalogsWithSubMap(true)
	if err != nil {
		logger.Error(err)
		return false
	}
	return m[catalogID] != nil
}

// DelLiveCatalogCache 删除分区缓存
func DelLiveCatalogCache(all bool) {
	err := service.LRURedis.Del(keys.KeyLiveCatalogs1.Format(util.BoolToInt(all))).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
