package catalog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestFindSubCatalogIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids, err := FindSubCatalogIDs(-1)
	require.NoError(err)
	assert.Len(ids, 0)
	ids, err = FindSubCatalogIDs(CatalogIDMusicRadio)
	require.NoError(err)
	assert.Len(ids, 8)
}

func TestDramaSubCatalogIDs(t *testing.T) {
	assert := assert.New(t)

	ids := DramaSubCatalogIDs()
	assert.Equal([]int64{18}, ids)

	service.Cache5Min.SetDefault(keys.KeyDramaSubCatalogIDs0.Format(), []int64{123})
	defer service.Cache5Min.Flush()
	ids = DramaSubCatalogIDs()
	assert.Equal([]int64{123}, ids)
}
