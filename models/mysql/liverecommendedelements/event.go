package liverecommendedelements

import (
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// EventExtendedFields 活动配置
type EventExtendedFields struct {
	CatalogIDs []int64 `json:"catalog_ids,omitempty"`
	TagIDs     []int64 `json:"tag_ids,omitempty"`
	RoomIDs    []int64 `json:"room_ids,omitempty"`
	OldCover   string  `json:"old_cover,omitempty"` // 存储老活动图标
}

// IsAllRoomShow 是否在所有直播间显示活动
func (e *EventExtendedFields) IsAllRoomShow() bool {
	return len(e.CatalogIDs) == 0 && len(e.TagIDs) == 0 && len(e.RoomIDs) == 0
}

// Event 直播间活动
type Event struct {
	Attribute

	ShowClose bool `gorm:"-" json:"show_close"`

	Sort           int    `gorm:"column:sort" json:"-"`
	ID             int64  `gorm:"column:id" json:"-"`
	ExpireTime     int64  `gorm:"column:expire_time" json:"expire_time,omitempty"`
	ExtendedFields string `gorm:"column:extended_fields" json:"extended_fields,omitempty"`
}

func (event *Event) beforeSave() {
	if event.ShowClose {
		event.Attribute.Attr.Set(AttrBitShowClose)
	} else {
		event.Attribute.Attr.Unset(AttrBitShowClose)
	}
	event.Cover, _ = service.Storage.Format(event.Cover)
}

// Save 保存直播间活动 sort should be greater than 0
func (event *Event) Save(db *gorm.DB) error {
	event.beforeSave()
	m := Model{
		ElementType:    ElementEvent,
		ID:             event.ID,
		Sort:           event.Sort,
		Attribute:      event.Attribute,
		ExpireTime:     event.ExpireTime,
		ExtendedFields: event.ExtendedFields,
	}
	return db.Save(&m).Error
}

func (event *Event) afterFind() {
	if event.Attribute.Attr.IsSet(AttrBitShowClose) {
		event.ShowClose = true
	}
	event.Cover = storage.ParseSchemeURL(event.Cover)
}

// FindAllEvents 获取当前设置的且生效的直播间活动
func FindAllEvents() (list []Event, err error) {
	list = allEventFromCache()
	if list != nil {
		// 说明查到了缓存
		return list, nil
	}
	now := goutil.TimeNow().Unix()
	db := TableEvent(service.DB).Select("id, cover, url, attr, extended_fields").
		Where("sort > ?", SortDeleted).
		Where("expire_time > ?", now).
		Where("start_time <= ?", now).
		Order("sort")
	err = db.Scan(&list).Error
	if err != nil {
		return nil, err
	}
	if list == nil {
		list = []Event{}
	}
	for i := range list {
		list[i].afterFind()
	}

	saveEventToCache(list)
	return list, nil
}

func allEventFromCache() (list []Event) {
	key := keys.KeyRecommendedEvents1.Format(goutil.TimeNow().Minute() / 5)
	v, err := service.Redis.Get(key).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Error(err)
		}
		return
	}
	err = json.Unmarshal([]byte(v), &list)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return list
}

func saveEventToCache(list []Event) {
	key := keys.KeyRecommendedEvents1.Format(goutil.TimeNow().Minute() / 5)
	b, err := json.Marshal(list)
	if err != nil {
		logger.Error(err)
		return
	}
	err = service.Redis.Set(key, b, 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		return
	}
}

// ClearEventCache 清除直播活动查询缓存
func ClearEventCache() {
	now := goutil.TimeNow()
	key := keys.KeyRecommendedEvents1.Format(now.Minute() / 5)
	err := service.Redis.Del(key).Err()
	if err != nil {
		logger.Error(err)
	}
}
