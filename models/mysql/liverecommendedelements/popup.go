package liverecommendedelements

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Popup 直播间活动小窗
type Popup struct {
	Attribute

	ElementID      int64  `gorm:"column:element_id" json:"element_id,omitempty"` // 活动 ID
	Sort           int    `gorm:"column:sort" json:"sort"`                       // 值为 0 表示逻辑删除
	ExpireTime     int64  `gorm:"column:expire_time" json:"expire_time,omitempty"`
	ExtendedFields string `gorm:"column:extended_fields" json:"extended_fields,omitempty"`
}

// 分区名单，标签名单，房间 ID 在黑白名单下都取并集
const (
	// ApplicationAllowList 白名单
	ApplicationAllowList = iota
	// ApplicationBlockList 黑名单
	ApplicationBlockList
)

// PopupShowConfig 直播间活动小窗显示的配置
type PopupShowConfig struct {
	AllRoomShow     bool `json:"all_room_show"`
	ApplicationType int  `json:"application_type,omitempty"` // 0: 白名单，1: 黑名单

	CatalogIDs []int64 `json:"catalog_ids,omitempty"`
	TagIDs     []int64 `json:"tag_ids,omitempty"`
	RoomIDs    []int64 `json:"room_ids,omitempty"`

	// 如果字段不为 0，优先使用这个字段判断 RoomIDs 是否属于黑白名单
	RoomApplicationID int64 `json:"room_application_id,omitempty"`

	LiveShowID string `json:"live_show_id,omitempty"`
}

// PopupRespItem 直播间活动小窗返回项
type PopupRespItem struct {
	MiniURL      string `json:"mini_url"`
	ImageURL     string `json:"image_url"`
	FoldImageURL string `json:"fold_image_url"`
	FullURL      string `json:"full_url,omitempty"`
	OpenURL      string `json:"open_url,omitempty"`
	ShowClose    bool   `json:"show_close"` // TODO: 兼容客户端旧版本, 版本号待定, 目前都返回 true

	StartTime int64 `json:"-"`
	EndTime   int64 `json:"-"`
}

func (popup *Popup) beforeSave() {
	popup.Cover, _ = service.Storage.Format(popup.Cover)
}

// Save 保存直播间活动小窗 sort should be greater than 0
func (popup *Popup) Save(db *gorm.DB) (int64, error) {
	popup.beforeSave()
	m := Model{
		Sort:           popup.Sort,
		ElementID:      popup.ElementID,
		ElementType:    ElementPopup,
		Attribute:      popup.Attribute,
		ExpireTime:     popup.ExpireTime,
		ExtendedFields: popup.ExtendedFields,
	}
	err := db.Save(&m).Error
	return m.ID, err
}

func (popup *Popup) afterFind() {
	popup.Cover = storage.ParseSchemeURL(popup.Cover)
}

// PopupURL 拼接直播间小窗存入数据时使用的 URL 字符串，包括传入类型，miniURL，以及活动使用的 eventURL
func (p *PopupRespItem) PopupURL() string {
	return fmt.Sprintf("%s;%s;%s;%s", p.MiniURL, p.FoldImageURL, p.OpenURL, p.FullURL)
}

// ParsePopupURL 解析直播间小窗的 URL 字符串，包括 miniURL，foldImageURL 和活动 openURL 以及 fullURL
func (p *PopupRespItem) ParsePopupURL(url string) error {
	urls := strings.Split(url, ";")
	// 当 urls 的长度为 3 就默认没有 FoldImageURL
	// WORKAROUND: 老数据下线后就可以不用再兼容了
	if len(urls) == 3 {
		p.MiniURL = urls[0]
		p.OpenURL = urls[1]
		p.FullURL = urls[2]
		return nil
	}
	if len(urls) == 4 {
		p.MiniURL = urls[0]
		p.FoldImageURL = urls[1]
		p.OpenURL = urls[2]
		p.FullURL = urls[3]
		return nil
	}
	return errors.New("urls no match")
}

// NewPopupItem new
func NewPopupItem(p Popup) (*PopupRespItem, error) {
	popupItem := &PopupRespItem{
		ImageURL:  p.Cover,
		ShowClose: true,
	}
	err := popupItem.ParsePopupURL(p.URL)
	if err != nil {
		return nil, err
	}
	return popupItem, nil
}

// FindAllPopups 获取当前生效的直播间活动小窗
func FindAllPopups() (list []Popup, err error) {
	list = allPopupsFromCache()
	if list != nil {
		// 说明查到了缓存
		return list, nil
	}
	now := goutil.TimeNow().Unix()
	db := TablePopup(service.DB).Select("element_id, cover, url, attr, extended_fields").
		Where("sort > ?", SortDeleted).
		Where("expire_time > ?", now).
		Where("start_time <= ?", now).
		Order("sort ASC")
	err = db.Scan(&list).Error
	if err != nil {
		return nil, err
	}
	if list == nil {
		list = []Popup{}
	}
	for i := range list {
		list[i].afterFind()
	}

	savePopupToCache(list)
	return list, nil
}

func allPopupsFromCache() (list []Popup) {
	key := keys.KeyRecommendedPopups1.Format(goutil.TimeNow().Minute() / 5)
	// TODO: 缓存应该使用 LRURedis，而不是 Redis
	v, err := service.Redis.Get(key).Result()
	if err != nil {
		if err != redis.Nil {
			logger.Error(err)
		}
		return
	}
	err = json.Unmarshal([]byte(v), &list)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return list
}

func savePopupToCache(list []Popup) {
	key := keys.KeyRecommendedPopups1.Format(goutil.TimeNow().Minute() / 5)
	b, err := json.Marshal(list)
	if err != nil {
		logger.Error(err)
		return
	}
	// TODO: 缓存应该使用 LRURedis，而不是 Redis
	err = service.Redis.Set(key, b, 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		return
	}
}

// ClearPopupCache 清除直播活动小窗查询缓存
func ClearPopupCache() {
	now := goutil.TimeNow()
	key := keys.KeyRecommendedPopups1.Format(now.Minute() / 5)
	err := service.Redis.Del(key).Err()
	if err != nil {
		logger.Error(err)
	}
}
