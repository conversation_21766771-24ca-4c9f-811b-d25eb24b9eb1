package liverecommendedelements

import (
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// FindLiveIcons 返回未过期的记录
func FindLiveIcons() (list []Model, err error) {
	err = TableLiveIcon(service.DB).Where("expire_time > ?", goutil.TimeNow().Unix()).Find(&list).Error
	return list, err
}

// DeleteLiveIcon 删除单条记录
func DeleteLiveIcon(id int64) (bool, error) {
	db := TableLiveIcon(service.DB).Where("id = ?", id).Limit(1).Delete(Model{})
	return db.RowsAffected > 0, db.Error
}
