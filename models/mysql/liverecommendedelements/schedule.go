package liverecommendedelements

import (
	"time"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MaxTopRoomCount 推荐直播最大数目
const MaxTopRoomCount = 10

// ListSchedule 返回 tm 当前所在的排期
func ListSchedule(tm time.Time) ([]Model, error) {
	var list []Model
	err := TableSchedule(service.DB).Where("sort > ?", SortDeleted).
		Where("start_time <= ?", tm.Unix()).
		Where("expire_time > ?", tm.Unix()).
		Order("sort").Limit(MaxTopRoomCount).Find(&list).Error
	if err != nil {
		return list, err
	}

	for i := range list {
		list[i].ParseSchemeURL()
	}
	return list, nil
}

// ListScheduleDuration 返回指定时间段内的排期
func ListScheduleDuration(begin, end time.Time) ([]Model, error) {
	var list []Model
	err := TableSchedule(service.DB).Where("sort > ?", SortDeleted).Where("expire_time > ? AND start_time < ?", begin.Unix(), end.Unix()).
		Order("start_time, sort").Find(&list).Error
	if err != nil {
		return list, err
	}

	for i := range list {
		list[i].ParseSchemeURL()
	}
	return list, nil
}

// AttrToTag attr to tag
func AttrToTag(b goutil.BitMask) int {
	switch {
	case b.IsSet(AttrTagNoble):
		return AttrTagNoble
	case b.IsSet(AttrTagQualityAnchor):
		return AttrTagQualityAnchor
	case b.IsSet(AttrTagArtist):
		return AttrTagArtist
	case b.IsSet(AttrTagOfficialAnchor):
		return AttrTagOfficialAnchor
	}
	return int(b)
}
