package liverecommendedelements

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// RoomIcon 房间图标推荐
type RoomIcon struct {
	RoomID  int64  `gorm:"column:element_id" json:"room_id"`
	IconURL string `gorm:"column:url" json:"icon_url"`
}

// FindRoomIcon 查询这些房间的推荐图标
func FindRoomIcon(roomID []int64) ([]*RoomIcon, error) {
	var record []*RoomIcon
	now := goutil.TimeNow().Unix()
	err := tableElement(ElementLiveIcon, nil).Select("element_id, url").
		Where("element_id IN (?)", roomID).
		Where("start_time <= ? AND expire_time > ? AND sort > ?", now, now, SortDeleted).
		Order("element_id ASC").Order("id DESC").Find(&record).Error
	if err != nil {
		return nil, err
	}
	res := make([]*RoomIcon, 0, len(record))
	for i, j := 0, -1; i < len(record); i++ {
		if j == -1 ||
			record[i].RoomID != res[j].RoomID {
			res = append(res, record[i])
			j++
		}
	}
	return res, nil
}

// FindOpenList 查找开播列表推荐房间房间号
// sort 是榜单位置，element_id 是房间号
func FindOpenList(openListType int, catalogID int64, filterHot4AndHot6 bool) ([]Model, error) {
	now := goutil.TimeNow().Unix()
	db := tableElement(ElementSquare, nil).Select("element_id, sort").
		Where("start_time <= ? AND expire_time > ? AND sort > ?",
			now, now, SortDeleted)
	switch openListType {
	case SquareTypeCatalog:
		db = db.Where("name = ?", strconv.FormatInt(catalogID, 10))
	case SquareTypeHot: // WORKAROUND: 0.9.3 就可以删除此兼容
		db = db.Where("name = ? OR name = ''", strconv.Itoa(SquareTypeHot))
		if filterHot4AndHot6 {
			// 排除热 4 和热 6 数据
			db = db.Where("sort NOT IN (?)", []int{4, 6})
		}
	default: //
		db = db.Where("name = ?", strconv.Itoa(openListType))
	}
	var rec []Model
	err := db.Order("sort ASC").Find(&rec).Error
	if err != nil {
		return nil, err
	}
	return rec, nil
}

// FindActiveHot6 查询当前生效的热 6 推荐直播间
func FindActiveHot6() (*Model, error) {
	var hot6 Model
	now := goutil.TimeNow().Unix()
	err := tableElement(ElementSquare, nil).
		Where("start_time <= ? AND expire_time > ? AND name = ? AND sort = 6", now, now, strconv.Itoa(SquareTypeHot)).
		First(&hot6).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}
	return &hot6, nil
}

// RecommendCache map[element_id]map[element_type]*Model
type RecommendCache map[int64]map[int]*Model

// loadRecommendCache load cache
// NOTICE: 目前仅支持直播间定制粉丝勋章、直播间推荐背景、主播信息背景卡片查询
func loadRecommendCache(elementID int64, elementType int) (*Model, error) {
	key := keys.KeyRecommend0.Format()
	// TODO: 验证缓存中的 recommend 的是否在有效期内
	v, ok := service.Cache5Min.Get(key)
	if ok {
		res, ok := v.(RecommendCache)
		if !ok {
			return nil, nil
		}
		roomCache, ok := res[elementID]
		if !ok {
			return nil, nil
		}
		cache, ok := roomCache[elementType]
		if !ok {
			return nil, nil
		}
		return cache, nil
	}

	var (
		now    = goutil.TimeNow().Unix()
		models []*Model
	)
	err := service.DB.Select("url, element_id, element_type").
		Where("element_type IN (?)", []int{ElementMedalFrame, ElementBackground, ElementCreatorCard}).
		Where("start_time <= ? AND expire_time > ? AND sort > ?", now, now, SortDeleted).
		Order("create_time ASC").Find(&models).Error
	if err != nil {
		return nil, err
	}

	res := make(RecommendCache)
	for _, m := range models {
		if _, ok := res[m.ElementID]; !ok {
			res[m.ElementID] = make(map[int]*Model)
		}
		res[m.ElementID][m.ElementType] = m
	}
	service.Cache5Min.Set(key, res, 0)

	roomCache, ok := res[elementID]
	if !ok {
		return nil, nil
	}
	cache, ok := roomCache[elementType]
	if !ok {
		return nil, nil
	}
	return cache, nil
}

// FindMedalFrame 直播间定制粉丝勋章
func FindMedalFrame(roomID int64) (string, error) {
	frame, err := loadRecommendCache(roomID, ElementMedalFrame)
	if err != nil {
		return "", err
	}
	if frame == nil {
		return "", nil
	}
	return frame.URL, nil
}

// BackgroundInfo background info
type BackgroundInfo struct {
	ImageURL string  `json:"image_url"`
	Opacity  float64 `json:"opacity"`
}

// BuildBackgroundURL 生成推荐背景图地址加透明度的 URL
func BuildBackgroundURL(url string, opacity float64) string {
	return fmt.Sprintf("%s;%.2f", url, opacity)
}

// ParseBackgroundURL 解析推荐背景图，返回背景图地址和透明度
func ParseBackgroundURL(recommendURL string) (url string, opacity float64) {
	if recommendURL == "" {
		return
	}
	list := strings.SplitN(recommendURL, ";", 2)
	if len(list) != 2 {
		logger.Errorf("unsupported recommend background url: %s", recommendURL)
		return
	}
	opacity, _ = strconv.ParseFloat(list[1], 64)
	if opacity == 0 {
		opacity = 1.0
	}
	return list[0], opacity
}

// FindBackground 获取直播间配置背景图和透明度
func FindBackground(roomID int64) (*BackgroundInfo, error) {
	background, err := loadRecommendCache(roomID, ElementBackground)
	if err != nil {
		return nil, err
	}
	if background == nil {
		return nil, nil
	}
	// URL 格式为：背景图地址;透明度
	url, opacity := ParseBackgroundURL(background.URL)
	if url == "" {
		return nil, nil
	}
	return &BackgroundInfo{
		ImageURL: storage.ParseSchemeURL(url),
		Opacity:  opacity,
	}, nil
}

// CreatorCardInfo creator card info
type CreatorCardInfo struct {
	FrameURL string `json:"frame_url"`
}

// FindCreatorCard 查询直播间主播信息背景
func FindCreatorCard(roomID int64) (*CreatorCardInfo, error) {
	card, err := loadRecommendCache(roomID, ElementCreatorCard)
	if err != nil {
		return nil, err
	}
	if card == nil {
		return nil, nil
	}
	return &CreatorCardInfo{
		FrameURL: storage.ParseSchemeURL(card.URL),
	}, nil
}
