package liverecommendedelements

import (
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTable(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	check := func(elementType int, f func(*gorm.DB) *gorm.DB) {
		var res Model
		require.NoError(f(service.DB).First(&res).Error, elementType)
		assert.Equal(elementType, res.ElementType)
	}
	check(1, TableRoom)
	check(2, TableEvent)
	check(3, TableLiveIcon)
	check(5, TableSquare)
	check(6, TableSchedule)
	check(7, TableLiveBanners)
	check(11, TablePopup)
	check(14, TableLiveTabRecommend)
	check(15, TableSearchRecommend)
}

func TestFindListElement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, pa, err := FindListElement(ElementAvatarFrame, "", 1, 20)
	require.NoError(err)
	assert.GreaterOrEqual(pa.Count, int64(1))
	assert.GreaterOrEqual(len(list), 1)
}

func TestParseSchemeURL(t *testing.T) {
	assert := assert.New(t)

	httpTest := "http://www.missevan.com/test.png"
	httpsTest := "https://www.missevan.com/test.png"
	param := Model{
		Attribute: Attribute{
			URL:   "",
			Cover: "",
		},
	}
	param.ParseSchemeURL()
	assert.Equal(param.URL, param.URL)
	assert.Equal(param.Cover, param.Cover)

	param.URL = httpTest
	param.Cover = httpsTest
	param.ParseSchemeURL()
	assert.Equal(httpTest, param.URL)
	assert.Equal(httpsTest, param.Cover)

	param.URL = "oss://url.png"
	param.Cover = "oss://cover.png"
	param.ParseSchemeURL()
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"url.png", param.URL)
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"cover.png", param.Cover)
}

func TestFindOneAndDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	element, err := FindOneAndDelete(1000, 2)
	require.NoError(err)
	require.NotNil(element)
	assert.Equal(SortDeleted, element.Sort)

	element, err = FindOneAndDelete(1000, 2)
	require.NoError(err)
	assert.Nil(element)
}

func TestFindSquareElement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	position := 3524
	name := "分区 0 的指定时间段的直播广场推荐位"
	startTime := now
	expireTime := now.Add(time.Hour)
	st := startTime.Unix()
	baseRecord1 := Model{
		ElementType: ElementSquare,
		Sort:        position,
		ElementID:   1,
		Attribute: Attribute{
			Name:      name,
			StartTime: &st,
		},
		ExpireTime: expireTime.Unix(),
	}
	require.NoError(service.DB.Where("sort = ?", position).Delete(Model{}).Error)
	records := make([]Model, 6)
	// records[0] 的开始时间与 startTime 相同，结束时间与 expireTime 相同
	for i := range records {
		records[i] = baseRecord1
	}
	// 覆盖了开始时间 startTime
	st = startTime.Add(-time.Second).Unix()
	records[1].Attribute.StartTime = &st
	records[1].ExpireTime = startTime.Add(time.Minute).Unix()
	// 覆盖了结束时间 expireTime
	st = expireTime.Add(-time.Second).Unix()
	records[2].Attribute.StartTime = &st
	records[2].ExpireTime = expireTime.Add(time.Minute).Unix()
	// 覆盖了开始时间 startTime 和结束时间 expireTime
	st = startTime.Add(-time.Second).Unix()
	records[3].Attribute.StartTime = &st
	records[3].ExpireTime = expireTime.Add(time.Minute).Unix()
	// records[4] 的结束时间和开始时间 startTime 相同
	st = startTime.Add(-time.Minute).Unix()
	records[4].Attribute.StartTime = &st
	records[4].ExpireTime = startTime.Unix()
	// 覆盖了开始时间 startTime 和结束时间 expireTime，但是属于不同分区
	records[5] = records[3]
	records[5].Name = "分区 1 的指定时间段的直播广场推荐位"
	for i := range records {
		require.NoError(service.DB.Create(&records[i]).Error)
	}
	_, err := FindSquareElement(0, name, startTime.Unix(), expireTime.Unix())
	assert.EqualError(err, "invalid position")
	r, err := FindSquareElement(position, name, startTime.Unix(), expireTime.Unix())
	require.NoError(err)
	require.Len(r, 4)
	assert.Equal(records[0].ID, r[0].ID)
	assert.Equal(records[1].ID, r[1].ID)
	assert.Equal(records[2].ID, r[2].ID)
	assert.Equal(records[3].ID, r[3].ID)
	require.NoError(service.DB.Where("sort = ?", position).Delete(Model{}).Error)
}

func TestBannerPeriodCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count, err := BannerPeriodCount(2123356789, 2123360389)
	require.NoError(err)
	assert.Equal(int64(1), count)

	count, err = BannerPeriodCount(2223356789, 2223360389)
	require.NoError(err)
	assert.Zero(count)
}

func TestPeriodBanner(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bannerList, err := PeriodBanner(time.Unix(2123356789, 0), time.Unix(2123360389, 0))
	require.NoError(err)
	assert.Len(bannerList, 1)
}

func TestLiveBannerExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	elements := []LiveRecommendedElements{
		{
			ElementID:  1003,
			StartTime:  goutil.NewInt64(2123356789),
			ExpireTime: 2123360389,
		},
	}
	ok, err := LiveBannerExists(elements)
	require.NoError(err)
	assert.True(ok)

	elements[0].ElementID = 1005
	ok, err = LiveBannerExists(elements)
	require.NoError(err)
	assert.False(ok)
}

func TestAddLiveIcon(t *testing.T) {
	require := require.New(t)

	starTime := goutil.TimeNow().Unix()
	err := AddLiveIcon(12344321, starTime, starTime+goutil.SecondOneMinute, "oss://test.png")
	require.NoError(err)
}

func TestFindOneByElementType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	element, err := FindOneByElementType(ElementAvatarFrame, 99233)
	require.NoError(err)
	assert.NotNil(element)

	element, err = FindOneByElementType(ElementAvatarFrame, 99233111111111)
	require.NoError(err)
	assert.Nil(element)
}
