package liverecommendedelements

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestListTabRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有推荐的直播间
	require.NoError(service.DB.Table(TableName()).
		Delete("", "element_type = ?", ElementLiveTabRecommend).Error)
	res, err := ListLiveTabRecommend()
	require.NoError(err)
	require.Len(res, 0)

	// 测试有推荐的直播间
	now := goutil.TimeNow()
	recommend := &LiveRecommendedElements{
		ElementID:   4381915,
		ElementType: ElementLiveTabRecommend,
		StartTime:   goutil.NewInt64(now.Unix()),
		ExpireTime:  now.Add(time.Hour).Unix(),
		Sort:        1,
	}
	require.NoError(service.DB.Create(recommend).Error)
	res, err = ListLiveTabRecommend()
	require.NoError(err)
	require.Len(res, 1)
	assert.EqualValues(4381915, res[0].ElementID)
	assert.EqualValues(recommend.Sort, res[0].Sort)
}

func TestListSearchRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有推荐的直播间
	require.NoError(service.DB.Table(TableName()).
		Delete("", "element_type = ?", ElementSearchRecommend).Error)
	res, err := ListSearchRecommend()
	require.NoError(err)
	require.Len(res, 0)

	// 测试有推荐的直播间
	now := goutil.TimeNow()
	recommend := &LiveRecommendedElements{
		ElementID:   4381915,
		ElementType: ElementSearchRecommend,
		StartTime:   goutil.NewInt64(now.Unix()),
		ExpireTime:  now.Add(time.Hour).Unix(),
		Sort:        1,
	}
	require.NoError(service.DB.Create(recommend).Error)
	res, err = ListSearchRecommend()
	require.NoError(err)
	require.Len(res, 1)
	assert.EqualValues(4381915, res[0].ElementID)
	assert.EqualValues(recommend.Sort, res[0].Sort)
}

func TestListRecommendTag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有标签
	require.NoError(service.DB.Table(TableName()).
		Delete("", "element_type = ?", ElementRecommendTag).Error)
	res, err := ListRecommendTag([]int64{4381915})
	require.NoError(err)
	require.Len(res, 0)

	// 测试获取直播间标签
	now := goutil.TimeNow()
	tag := &LiveRecommendedElements{
		ElementID:   4381915,
		ElementType: ElementRecommendTag,
		StartTime:   goutil.NewInt64(now.Unix()),
		ExpireTime:  now.Add(time.Hour).Unix(),
		Name:        "测试标签",
	}
	require.NoError(service.DB.Create(tag).Error)
	res, err = ListRecommendTag([]int64{4381915})
	require.NoError(err)
	require.Len(res, 1)
	assert.EqualValues(4381915, res[0].ElementID)
	assert.Equal("测试标签", res[0].Name)
}

func TestListDailyTaskAllowList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testTime := int64(2123356789)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTime, 0)
	})
	defer cancel()
	testAllowList := Model{
		Sort:        1,
		ElementID:   2233,
		ElementType: ElementDailyTaskAllowList,
		Attribute: Attribute{
			StartTime: &testTime,
		},
		ExpireTime: testTime + 10,
	}
	require.NoError(service.DB.Delete(&Model{}, "element_type = ?", ElementDailyTaskAllowList).Error)
	require.NoError(service.DB.Create(&testAllowList).Error)
	defer cancel()
	creatorIDs, err := ListDailyTaskAllowList()
	require.NoError(err)
	assert.Len(creatorIDs, 1)
	assert.EqualValues(testAllowList.ElementID, creatorIDs[0])
}
