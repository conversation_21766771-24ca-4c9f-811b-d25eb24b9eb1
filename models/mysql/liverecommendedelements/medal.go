package liverecommendedelements

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const allCreatorElementID = 0

// PointMulti 亲密度倍数信息
type PointMulti struct {
	PointMultiAdd     int64 `json:"point_multi_add,omitempty"`     // 亲密度加的倍数
	ThresholdMultiAdd int64 `json:"threshold_multi_add,omitempty"` // 亲密度上限加的倍数
}

// BatchAddRoomMedalPointMulti batch add room medal point multi
func BatchAddRoomMedalPointMulti(creatorIDs []int64, startTime, endTime time.Time, multi PointMulti) error {
	v, err := json.Marshal(multi)
	if err != nil {
		return err
	}
	elements := make([]*LiveRecommendedElements, 0, len(creatorIDs))
	now := goutil.TimeNow()
	for _, creatorID := range creatorIDs {
		elements = append(elements, &LiveRecommendedElements{
			Sort:           1,
			ElementID:      creatorID,
			ElementType:    ElementMultiMedalPoint,
			StartTime:      goutil.NewInt64(startTime.Unix()),
			ExpireTime:     endTime.Unix(),
			ExtendedFields: string(v),
			CreateTime:     now.Unix(),
			ModifiedTime:   now.Unix(),
		})
	}
	err = servicedb.BatchInsert(service.DB, TableName(), elements)
	if err != nil {
		return err
	}

	var keysToDelete []string
	for _, creatorID := range creatorIDs {
		if creatorID > 0 {
			keysToDelete = append(keysToDelete, keys.KeyRoomMedalPointMulti.Format(creatorID))
		}
	}
	if len(keysToDelete) > 0 {
		if err := service.LRURedis.Del(keysToDelete...).Err(); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

const roomMedalPointMultiCacheDuration = 5 * time.Minute

type medalPointMultiCache struct {
	CreatorID  int64 `json:"creator_id"`
	StartTime  int64 `json:"start_time"`
	ExpireTime int64 `json:"expire_time"`

	PointMultiAdd     int64 `json:"point_multi_add"`     // 亲密度加的倍数
	ThresholdMultiAdd int64 `json:"threshold_multi_add"` // 亲密度上限加的倍数
}

func loadRoomMedalMultiCache(creatorID int64) ([]*medalPointMultiCache, error) {
	key := keys.KeyRoomMedalPointMulti.Format(creatorID)
	cache, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	var res []*medalPointMultiCache
	if cache != "" {
		err = json.Unmarshal([]byte(cache), &res)
		if err != nil {
			return nil, err
		}
		return res, nil
	}

	var elements []LiveRecommendedElements
	now := goutil.TimeNow()
	err = tableElement(ElementMultiMedalPoint, service.DB).
		Where("element_id IN (?)", []int64{creatorID, allCreatorElementID}).
		Where("sort > ?", SortDeleted).
		Where("start_time <= ? ", now.Add(roomMedalPointMultiCacheDuration).Unix()). // 缓存时间是 5 分钟，所以需要把即将开始的也查出来
		Where("expire_time > ?", now.Unix()).
		Order("element_id DESC, start_time ASC"). // 直播间单独配置优先级 > 全局配置优先级
		Find(&elements).Error
	if err != nil {
		return nil, err
	}
	pointMultiList := make([]*medalPointMultiCache, 0, len(elements))
	for i := range elements {
		element := elements[i]
		if element.ExtendedFields == "" || element.StartTime == nil {
			continue
		}
		var pointMulti medalPointMultiCache
		err = json.Unmarshal([]byte(element.ExtendedFields), &pointMulti)
		if err != nil {
			return nil, err
		}
		pointMulti.CreatorID = element.ElementID
		pointMulti.StartTime = *element.StartTime
		pointMulti.ExpireTime = element.ExpireTime
		pointMultiList = append(pointMultiList, &pointMulti)
	}
	value, err := json.Marshal(pointMultiList)
	if err != nil {
		return nil, err
	}
	err = service.LRURedis.Set(key, string(value), roomMedalPointMultiCacheDuration).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return pointMultiList, nil
}

// FindCurrentRoomMedalPointMulti find current room medal point multi
func FindCurrentRoomMedalPointMulti(creatorID int64) (*PointMulti, error) {
	multiList, err := loadRoomMedalMultiCache(creatorID)
	if err != nil {
		return nil, err
	}
	nowUnix := goutil.TimeNow().Unix()
	for i := range multiList {
		multi := multiList[i]
		if multiList[i].StartTime <= nowUnix && multiList[i].ExpireTime > nowUnix {
			// 缓存中已按照优先级排序，所以直接取第一个生效的即可
			return &PointMulti{
				PointMultiAdd:     multi.PointMultiAdd,
				ThresholdMultiAdd: multi.ThresholdMultiAdd,
			}, nil
		}
	}
	return nil, nil
}
