package liverecommendedelements

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSchedule(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	endTime := time.Unix(9999999999, 0)

	list, err := ListSchedule(goutil.TimeNow())
	require.NoError(err)
	assert.NotEmpty(list)

	list, err = ListSchedule(endTime)
	require.NoError(err)
	assert.Empty(list)

	list, err = ListScheduleDuration(time.Unix(0, 0), endTime)
	require.NoError(err)
	assert.NotEmpty(list)
}

func TestAttrToTag(t *testing.T) {
	assert := assert.New(t)

	var a goutil.BitMask
	assert.Equal(AttrTagNormal, AttrToTag(a))
	a.Set(AttrTagArtist)
	assert.Equal(AttrTagArtist, AttrToTag(a))
	a.Set(AttrTagQualityAnchor)
	assert.Equal(AttrTagQualityAnchor, AttrToTag(a))
	a.Set(AttrTagNoble)
	assert.Equal(AttrTagNoble, AttrToTag(a))
	var b goutil.BitMask
	b.Set(AttrTagOfficialAnchor)
	assert.Equal(AttrTagOfficialAnchor, AttrToTag(b))
}
