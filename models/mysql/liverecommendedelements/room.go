// Package liverecommendedelements 直播推荐相关
package liverecommendedelements

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/service"
)

// Room 直播间
type Room struct {
	Position int   `json:"position"`
	RoomID   int64 `json:"room_id"`

	Name            string         `json:"name"`      // 直播标题
	CoverURL        string         `json:"cover_url"` // 直播封面图
	CreatorID       int64          `json:"creator_id"`
	CreatorUsername string         `json:"creator_username"` // 主播昵称
	CreatorIconURL  string         `json:"creator_iconurl"`  // 主播头像
	CatalogID       int64          `json:"catalog_id"`
	CatalogName     string         `json:"catalog_name,omitempty"`
	CatalogColor    string         `json:"catalog_color,omitempty"`
	CustomTag       *tag.CustomTag `json:"custom_tag,omitempty"`
	Status          Status         `json:"status"`
}

// Status 直播间状态
type Status struct {
	Open int `json:"open"` // 是否正在直播：0 否，1 是
}

// EnsureCoverURL 如果直播封面图为空，则用默认猫猫图代替
func (r *Room) EnsureCoverURL() {
	if r.CoverURL == "" {
		r.CoverURL = service.Storage.Parse(config.Conf.Params.URL.AvatarURL)
	}
}

// Set 由 Model 赋值
func (r *Room) Set(m Model) {
	r.RoomID = m.ElementID
	r.Position = m.Sort
}

// SaveRoom 保存直播间推荐位
func SaveRoom(db *gorm.DB, position int, roomID int64) error {
	m := Model{
		ElementType: ElementRoom,
		Sort:        position,
		ElementID:   roomID,
	}

	err := TableRoom(db).Where("sort = ?", position).Assign(m).FirstOrCreate(&m).Error
	return err
}

// ClearRoom 清除直播间推荐位
func ClearRoom(position int) error {
	return TableRoom(service.DB).Where("sort = ?", position).Update("element_id", 0).Error
}

// FindRoomByPosition 获得某个推荐位的直播间推荐位列表
func FindRoomByPosition(position int) (list []Event, err error) {
	err = TableRoom(service.DB).Where("sort = ?", position).Find(&list).Error
	return
}
