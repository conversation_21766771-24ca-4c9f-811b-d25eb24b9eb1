package liverecommendedelements

import (
	"errors"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// constants
const (
	ElementRoom               = iota + 1 // 首页 "正在直播" 模块推荐位
	ElementEvent                         // App 直播间活动模块
	ElementLiveIcon                      // 直播广场运营配置图标
	ElementAvatarFrame                   // 头像框
	ElementSquare                        // 直播广场房间列表推荐，name 转成的数字代表分区，最热不配置 name
	ElementSchedule                      // "直播推荐" 排期表
	ElementLiveBanner                    // Banner 排期表
	ElementMedalFrame                    // 直播间定制粉丝勋章
	ElementUserCardFrame                 // 用户定制名片框
	ElementBackground                    // 直播间背景图, url 字段存储格式为: 背景图地址;透明度
	ElementPopup                         // 直播间活动小窗
	ElementCreatorCard                   // 主播信息背景（https://info.missevan.com/pages/viewpage.action?pageId=90784172）
	ElementMultiMedalPoint               // 勋章亲密度翻倍，TODO: 迁移到 live_custom
	ElementLiveTabRecommend              // 直播 tab 页推荐
	ElementSearchRecommend               // 搜索页推荐
	ElementRecommendTag                  // 推荐直播间标签
	ElementDailyTaskAllowList            // 日常任务进入直播间白名单
	ElementAlgorithmExposure             // 直播列表推荐算法曝光干预卡，运营配置直播间特定时段的曝光量
)

// constants
const (
	AttrBitShowClose = iota + 1 // 小窗是否可以关闭
)

// schedule tag value 按照第几位实现的
const (
	AttrTagNormal = iota
	AttrTagArtist
	AttrTagQualityAnchor
	AttrTagNoble
	AttrTagOfficialAnchor
	AttrTagLimit
)

// element delete status
const (
	SortDeleted = iota
)

// TableName table name
func TableName() string {
	return "live_recommended_elements"
}

// Attribute of Model
type Attribute struct {
	Name  string `gorm:"column:name" json:"name,omitempty"`
	Cover string `gorm:"column:cover" json:"cover"`
	URL   string `gorm:"column:url" json:"url"`
	// type 2 活动标签是否显示关闭按钮 位 1 显示关闭按钮
	// type 6 超管直播推荐标签，按位实现，位 1: 显示艺人、位 2: 显示优质经验主播、位 3: 显示神话推荐、位 4: 显示官签主播
	Attr goutil.BitMask `gorm:"column:attr" json:"-"`

	StartTime *int64 `gorm:"column:start_time" json:"start_time"`
}

// Model of table live_recommended_elements
type Model struct {
	ID          int64 `gorm:"column:id;primary_key" json:"-"`
	Sort        int   `gorm:"column:sort" json:"sort"` // 值为 0 表示逻辑删除
	ElementID   int64 `gorm:"column:element_id" json:"element_id"`
	ElementType int   `gorm:"column:element_type" json:"element_type"`

	Attribute

	ExpireTime     int64  `gorm:"column:expire_time" json:"-"`
	CreateTime     int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime   int64  `gorm:"column:modified_time" json:"-"`
	ExtendedFields string `gorm:"column:extended_fields" json:"-"`
}

// AlgorithmExposureExtendedFields 直播列表推荐算法曝光干预卡更多字段
type AlgorithmExposureExtendedFields struct {
	ExposureLevelID int64 `json:"exposure_level_id,omitempty"` // 曝光等级 ID
}

// SquareExtendedFields 直播广场房间列表推荐扩展字段
type SquareExtendedFields struct {
	From int `json:"from"` // 推荐来源
}

// 直播广场房间列表推荐来源
const (
	SquareFromOperator = iota // 运营推荐
	SquareFromGuild           // 公会推荐
)

// TableName of Model
func (Model) TableName() string {
	return TableName()
}

// LiveRecommendedElements of table live_recommended_elements
type LiveRecommendedElements struct {
	ID          int64  `gorm:"column:id;primary_key" json:"-"`
	Sort        int    `gorm:"column:sort" json:"sort"` // 值为 0 表示逻辑删除
	ElementID   int64  `gorm:"column:element_id" json:"element_id"`
	ElementType int    `gorm:"column:element_type" json:"element_type"`
	Name        string `gorm:"column:name" json:"name,omitempty"`
	Cover       string `gorm:"column:cover" json:"cover"`
	URL         string `gorm:"column:url" json:"url"`
	// type 2 活动标签是否显示关闭按钮 位 1 显示关闭按钮
	// type 6 超管直播推荐标签，按位实现，位 1: 显示艺人、位 2: 显示优质经验主播、位 3: 显示神话推荐
	Attr      goutil.BitMask `gorm:"column:attr" json:"-"`
	StartTime *int64         `gorm:"column:start_time" json:"start_time"`

	ExpireTime     int64  `gorm:"column:expire_time" json:"-"`
	CreateTime     int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime   int64  `gorm:"column:modified_time" json:"-"`
	ExtendedFields string `gorm:"column:extended_fields" json:"-"`
}

// TableName of LiveRecommendedElements
func (LiveRecommendedElements) TableName() string {
	return TableName()
}

// BeforeCreate automatically sets columns create_time and modified_time
func (m *Model) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically updates the column modified_time
func (m *Model) BeforeUpdate(scope *gorm.Scope) (err error) {
	// NOTICE: 只修改 m 的字段值，对 gorm.Update(s) 无效，所以需要 SetColumn
	m.ModifiedTime = goutil.TimeNow().Unix()
	return scope.SetColumn("modified_time", m.ModifiedTime)
}

// ParseSchemeURL 解析 Model URL and Cover 协议地址
func (m *Model) ParseSchemeURL() {
	m.URL = storage.ParseSchemeURL(m.URL)
	m.Cover = storage.ParseSchemeURL(m.Cover)
}

// BeforeCreate automatically sets columns create_time and modified_time
func (l *LiveRecommendedElements) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically updates the column modified_time
func (l *LiveRecommendedElements) BeforeUpdate(scope *gorm.Scope) (err error) {
	// NOTICE: 只修改 l 的字段值，对 gorm.Update(s) 无效，所以需要 SetColumn
	modifiedTime := goutil.TimeNow().Unix()
	return scope.SetColumn("modified_time", modifiedTime)
}

// TableRoom 首页 "正在直播" 模块推荐位
func TableRoom(db *gorm.DB) *gorm.DB {
	return tableElement(ElementRoom, db)
}

// TableEvent App 直播间活动模块
func TableEvent(db *gorm.DB) *gorm.DB {
	return tableElement(ElementEvent, db)
}

// TableLiveIcon 直播广场运营配置图标
func TableLiveIcon(db *gorm.DB) *gorm.DB {
	return tableElement(ElementLiveIcon, db)
}

// TableSquare 直播广场“最热”推荐位中新增的 3 个元素
func TableSquare(db *gorm.DB) *gorm.DB {
	return tableElement(ElementSquare, db)
}

// TableSchedule "直播推荐" 排期表
func TableSchedule(db *gorm.DB) *gorm.DB {
	return tableElement(ElementSchedule, db)
}

// TableLiveBanners Banner 排期表
func TableLiveBanners(db *gorm.DB) *gorm.DB {
	return tableElement(ElementLiveBanner, db)
}

// TablePopup 直播间活动小窗
func TablePopup(db *gorm.DB) *gorm.DB {
	return tableElement(ElementPopup, db)
}

// TableLiveTabRecommend 直播 tab 页推荐列表
func TableLiveTabRecommend(db *gorm.DB) *gorm.DB {
	return tableElement(ElementLiveTabRecommend, db)
}

// TableSearchRecommend 搜索页推荐列表
func TableSearchRecommend(db *gorm.DB) *gorm.DB {
	return tableElement(ElementSearchRecommend, db)
}

// TableRecommendTag 推荐直播间标签
func TableRecommendTag(db *gorm.DB) *gorm.DB {
	return tableElement(ElementRecommendTag, db)
}

// TableDailyTaskAllowList 每日任务直播间白名单
func TableDailyTaskAllowList(db *gorm.DB) *gorm.DB {
	return tableElement(ElementDailyTaskAllowList, db)
}

// TableAlgorithmExposure 热门列表推荐算法曝光干预卡
func TableAlgorithmExposure(db *gorm.DB) *gorm.DB {
	return tableElement(ElementAlgorithmExposure, db)
}

func tableElement(elementType int, db *gorm.DB) *gorm.DB {
	if db == nil {
		db = service.DB
	}
	return db.Table(TableName()).Where("element_type = ?", elementType)
}

// FindListElement 返回未过期的记录
func FindListElement(elementType int, sort string, p, pageSize int64) ([]*Model, goutil.Pagination, error) {
	db := tableElement(elementType, service.DB).Where("sort > ?", SortDeleted).
		Where("expire_time > ?", goutil.TimeNow().Unix())
	if sort != "" {
		db = db.Order(sort)
	}

	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}

	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*Model, 0), pa, nil
	}

	var res []*Model
	err = pa.ApplyTo(db).Find(&res).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	for i := 0; i < len(res); i++ {
		res[i].ParseSchemeURL()
	}
	return res, pa, nil
}

// FindElement 通过 ID 查找单个直播推荐元素
func FindElement(id int64, elementType int) (*Model, error) {
	record := new(Model)
	err := tableElement(elementType, service.DB).First(record, "id = ? AND sort > ?", id, SortDeleted).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return record, nil
}

// FindOneAndDelete 逻辑删除单条记录
func FindOneAndDelete(id int64, elementType int) (*Model, error) {
	element, err := FindElement(id, elementType)
	if err != nil {
		return nil, err
	}
	if element == nil {
		return nil, nil
	}

	now := goutil.TimeNow().Unix()
	err = tableElement(elementType, service.DB).Where("id = ?", id).Updates(
		map[string]interface{}{
			"modified_time": now,
			"sort":          SortDeleted,
		}).Error
	if err != nil {
		return nil, err
	}
	element.Sort = SortDeleted
	element.ModifiedTime = now
	return element, err
}

// FindSquareElement 查找指定分区中占用了指定时间段的直播广场指定位置的所有记录
func FindSquareElement(position int, name string, startTime, expireTime int64) ([]*Model, error) {
	if position == 0 {
		return nil, errors.New("invalid position")
	}
	db := tableElement(ElementSquare, service.DB).Where("sort = ? AND name = ?", position, name).
		Where("start_time < ? AND expire_time > ?", expireTime, startTime)
	var r []*Model
	err := db.Scan(&r).Error
	if err != nil {
		return nil, err
	}
	return r, nil
}

// FindAlgorithmExposureElement 查找指定分区中占用了指定时间段的直播广场指定位置的所有记录
func FindAlgorithmExposureElement(name string, roomID, startTime, expireTime int64) ([]*Model, error) {
	db := tableElement(ElementAlgorithmExposure, service.DB).
		Where("sort = 4 AND name = ? AND element_id = ?", name, roomID).
		Where("start_time < ? AND expire_time > ?", expireTime, startTime)
	var r []*Model
	err := db.Scan(&r).Error
	if err != nil {
		return nil, err
	}
	return r, nil
}

// BannerPeriodCount 某时段 banner 数量
func BannerPeriodCount(startTime, expireTime int64) (int64, error) {
	var count int64
	// NOTICE: 因为 banner 是硬删除，所以不需要判断 sort
	err := TableLiveBanners(service.DB).Where("start_time < ? AND expire_time > ?", expireTime, startTime).
		Count(&count).Error
	return count, err
}

// PeriodBanner 某时段 banner 数据
// TODO: banner 后续改为软删除
func PeriodBanner(startTime, expireTime time.Time) ([]*Model, error) {
	var modelList []*Model
	err := TableLiveBanners(service.DB).Where("start_time < ? AND expire_time > ?", expireTime.Unix(), startTime.Unix()).
		Find(&modelList).Error
	if err != nil {
		return nil, err
	}
	return modelList, nil
}

// LiveBannerExists 直播 banner 主播在对应时段是否被推荐
func LiveBannerExists(elements []LiveRecommendedElements) (bool, error) {
	if len(elements) == 0 {
		return false, nil
	}
	var model Model
	db := service.DB
	for _, element := range elements {
		db = db.Or("element_type = ? AND element_id = ? AND start_time < ? AND expire_time > ?",
			ElementLiveBanner, element.ElementID, element.ExpireTime, element.StartTime)
	}
	err := db.First(&model).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// AddLiveIcon 添加直播角标
func AddLiveIcon(roomID, startTime, expireTime int64, url string) error {
	return service.DB.Create(&LiveRecommendedElements{
		Sort:        1,
		ElementID:   roomID,
		ElementType: ElementLiveIcon,
		URL:         storage.ParseSchemeURL(url),
		StartTime:   &startTime,
		ExpireTime:  expireTime,
	}).Error
}

// FindOneByElementType 查询推荐元素
// NOTICE: 仅用于实时查询推荐元素用来更新或创建，不同于 loadRecommendCache 获取本地缓存的非实时数据会错误更新或创建
func FindOneByElementType(elementType int, elementID int64) (*Model, error) {
	now := goutil.TimeNow().Unix()
	var model Model
	err := service.DB.Where("element_type = ? AND element_id = ?", elementType, elementID).
		Where("start_time <= ? AND expire_time > ? AND sort > ?", now, now, SortDeleted).
		Take(&model).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &model, nil
}
