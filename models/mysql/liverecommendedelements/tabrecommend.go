package liverecommendedelements

import (
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ListLiveTabRecommend 获取直播 tab 页推荐直播间位置干预数据
func ListLiveTabRecommend() ([]Model, error) {
	var recommends []Model
	nowUnix := goutil.TimeNow().Unix()
	// sort 为位置，element_id 为直播间 ID
	err := TableLiveTabRecommend(service.DB).Select("sort, element_id").
		Where("start_time <= ? AND expire_time > ? AND sort > ?", nowUnix, nowUnix, SortDeleted).
		Find(&recommends).Error
	if err != nil {
		return nil, err
	}
	return recommends, nil
}

// ListSearchRecommend 获取搜索页推荐直播间位置干预数据
func ListSearchRecommend() ([]Model, error) {
	var recommends []Model
	nowUnix := goutil.TimeNow().Unix()
	// sort 为位置，element_id 为直播间 ID，name 为标签文案
	err := TableSearchRecommend(service.DB).Select("sort, element_id, name").
		Where("start_time <= ? AND expire_time > ? AND sort > ?", nowUnix, nowUnix, SortDeleted).
		Find(&recommends).Error
	if err != nil {
		return nil, err
	}
	return recommends, nil
}

// ListRecommendTag 获取推荐直播间标签列表
func ListRecommendTag(roomIDs []int64) ([]Model, error) {
	var recommendTags []Model
	nowUnix := goutil.TimeNow().Unix()
	// element_id 为直播间 ID，name 为标签文案
	err := TableRecommendTag(service.DB).Select("element_id, name").
		Where("start_time <= ? AND expire_time > ? AND element_id IN (?)", nowUnix, nowUnix, roomIDs).
		Find(&recommendTags).Error
	if err != nil {
		return nil, err
	}
	return recommendTags, nil
}

// ListDailyTaskAllowList 获取白名单中的主播 IDs
func ListDailyTaskAllowList() ([]int64, error) {
	now := goutil.TimeNow().Unix()
	var creatorIDs []int64
	err := TableDailyTaskAllowList(service.DB).
		Where("start_time <= ? AND expire_time > ? AND sort > ?", now, now, SortDeleted).
		Pluck("element_id", &creatorIDs).Error
	if err != nil {
		return nil, err
	}
	return creatorIDs, nil
}
