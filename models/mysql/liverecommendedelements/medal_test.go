package liverecommendedelements

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBatchAddAndFindCurrentRoomMedalPointMulti(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testCreatorID = int64(12345)
	require.NoError(service.DB.Table(TableName()).Delete("", "element_type = ?", ElementMultiMedalPoint).Error)
	key := keys.KeyRoomMedalPointMulti.Format(testCreatorID)
	require.NoError(service.LRURedis.Del(key).Err())

	multi, err := FindCurrentRoomMedalPointMulti(testCreatorID)
	require.NoError(err)
	require.Nil(multi)

	now := goutil.TimeNow()
	err = BatchAddRoomMedalPointMulti([]int64{0}, now, now.Add(5*time.Second), PointMulti{
		PointMultiAdd:     1,
		ThresholdMultiAdd: 2,
	})
	require.NoError(err)
	// 全局亲密度翻倍
	require.NoError(service.LRURedis.Del(key).Err())
	multi, err = FindCurrentRoomMedalPointMulti(testCreatorID)
	require.NoError(err)
	require.NotNil(multi)
	assert.EqualValues(1, multi.PointMultiAdd)
	assert.EqualValues(2, multi.ThresholdMultiAdd)

	err = BatchAddRoomMedalPointMulti([]int64{testCreatorID}, now, now.Add(5*time.Second), PointMulti{
		PointMultiAdd:     3,
		ThresholdMultiAdd: 4,
	})
	require.NoError(err)
	// 直播间单独配置
	multi, err = FindCurrentRoomMedalPointMulti(testCreatorID)
	require.NoError(err)
	require.NotNil(multi)
	assert.EqualValues(3, multi.PointMultiAdd)
	assert.EqualValues(4, multi.ThresholdMultiAdd)
	// 从缓存中读取
	multi, err = FindCurrentRoomMedalPointMulti(testCreatorID)
	require.NoError(err)
	require.NotNil(multi)
	assert.EqualValues(3, multi.PointMultiAdd)
	assert.EqualValues(4, multi.ThresholdMultiAdd)
}

func TestLoadRoomMedalMultiCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.DB.Table(TableName()).Delete("", "element_type = ?", ElementMultiMedalPoint).Error)
	var testCreatorID = int64(12345)
	insertData := []*LiveRecommendedElements{
		{
			Sort:           1,
			ElementID:      0,
			ElementType:    ElementMultiMedalPoint,
			StartTime:      goutil.NewInt64(1),
			ExpireTime:     10,
			ExtendedFields: "{}",
		},
		{
			Sort:           1,
			ElementID:      testCreatorID,
			ElementType:    ElementMultiMedalPoint,
			StartTime:      goutil.NewInt64(10),
			ExpireTime:     20,
			ExtendedFields: "{}",
		},
		{
			Sort:           1,
			ElementID:      testCreatorID,
			ElementType:    ElementMultiMedalPoint,
			StartTime:      goutil.NewInt64(302),
			ExpireTime:     500,
			ExtendedFields: "{}",
		},
	}
	err := servicedb.BatchInsert(service.DB, TableName(), insertData)
	require.NoError(err)
	key := keys.KeyRoomMedalPointMulti.Format(testCreatorID)
	require.NoError(service.LRURedis.Del(key).Err())
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(1, 0)
	})
	defer goutil.SetTimeNow(nil)
	multi, err := loadRoomMedalMultiCache(testCreatorID)
	require.NoError(err)
	assert.Len(multi, 2)
	count, err := service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.NotEmpty(count)

	multi, err = loadRoomMedalMultiCache(testCreatorID)
	require.NoError(err)
	assert.Len(multi, 2)

	goutil.SetTimeNow(nil)
	require.NoError(service.LRURedis.Del(key).Err())
	multi, err = loadRoomMedalMultiCache(testCreatorID)
	require.NoError(err)
	assert.Empty(multi)
}
