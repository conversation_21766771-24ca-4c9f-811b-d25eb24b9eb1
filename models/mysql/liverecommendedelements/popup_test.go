package liverecommendedelements

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewPopupItem(t *testing.T) {
	assert := assert.New(t)

	p := Popup{
		Attribute: Attribute{
			URL:   "http://example.com/miniurl;http://example.com/foldimageurl;;http://example.com/fullurl",
			Cover: "https://static-test.missevan.com/cover.png",
		},
	}
	popupItem, err := NewPopupItem(p)
	assert.NoError(err)
	assert.Equal("http://example.com/miniurl", popupItem.MiniURL)
	assert.Equal("http://example.com/foldimageurl", popupItem.FoldImageURL)
	assert.Equal("", popupItem.OpenURL)
	assert.Equal("http://example.com/fullurl", popupItem.FullURL)
	assert.Equal("https://static-test.missevan.com/cover.png", popupItem.ImageURL)

	popupItem, err = NewPopupItem(p)
	assert.NoError(err)
	assert.NotNil(popupItem)

	// error url
	p.Attribute.URL = "http://example.com/miniurl;;;;http://example.com/fullurl"
	popupItem, err = NewPopupItem(p)
	assert.Error(err, errors.New("urls no match"))
	assert.Nil(popupItem)
}

func TestPopupSave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().Unix()
	popup := Popup{
		ElementID: 152,
		Attribute: Attribute{
			Name:      "test popup save",
			Cover:     "https://static-test.missevan.com/cover.png",
			StartTime: &now,
		},
	}

	_, err := popup.Save(service.DB)
	require.NoError(err)
	db := service.DB.Table(TableName()).Delete("", "element_id = ? AND start_time = ?",
		popup.ElementID, popup.StartTime)
	require.NoError(db.Error)
	assert.NotZero(db.RowsAffected)
}

func TestPopupURL(t *testing.T) {
	assert := assert.New(t)

	popupOpenURLEmpty := &PopupRespItem{
		MiniURL:      "http://example.com/miniurl",
		FoldImageURL: "http://example.com/foldimageurl",
		OpenURL:      "",
		FullURL:      "http://example.com/fullurl",
	}
	url := popupOpenURLEmpty.PopupURL()
	assert.Equal("http://example.com/miniurl;http://example.com/foldimageurl;;http://example.com/fullurl", url)

	popupFullURLEmpty := &PopupRespItem{
		MiniURL:      "http://example.com/miniurl",
		FoldImageURL: "http://example.com/foldimageurl",
		OpenURL:      "http://example.com/openurl",
		FullURL:      "",
	}
	url = popupFullURLEmpty.PopupURL()
	assert.Equal("http://example.com/miniurl;http://example.com/foldimageurl;http://example.com/openurl;", url)
}

func TestParsePopupURL(t *testing.T) {
	assert := assert.New(t)

	// len 4
	openEventURL := "http://example.com/miniurl;http://example.com/foldimageurl;http://example.com/openurl;"
	fullEventURL := "http://example.com/miniurl;http://example.com/foldimageurl;;http://example.com/fullurl"

	popItemLen4 := &PopupRespItem{}
	err := popItemLen4.ParsePopupURL(openEventURL)
	assert.NoError(err)
	assert.Equal("http://example.com/miniurl", popItemLen4.MiniURL)
	assert.Equal("http://example.com/foldimageurl", popItemLen4.FoldImageURL)
	assert.Equal("http://example.com/openurl", popItemLen4.OpenURL)
	assert.Equal("", popItemLen4.FullURL)

	popItemLen4 = &PopupRespItem{}
	err = popItemLen4.ParsePopupURL(fullEventURL)
	assert.NoError(err)
	assert.Equal("http://example.com/miniurl", popItemLen4.MiniURL)
	assert.Equal("http://example.com/foldimageurl", popItemLen4.FoldImageURL)
	assert.Equal("", popItemLen4.OpenURL)
	assert.Equal("http://example.com/fullurl", popItemLen4.FullURL)

	// len 3
	noFoldOpenEventURL := "http://example.com/miniurl;http://example.com/openurl;"
	noFoldFullEventURL := "http://example.com/miniurl;;http://example.com/fullurl"

	popItemLen3 := &PopupRespItem{}
	err = popItemLen3.ParsePopupURL(noFoldOpenEventURL)
	assert.NoError(err)
	assert.Equal("http://example.com/miniurl", popItemLen3.MiniURL)
	assert.Equal("http://example.com/openurl", popItemLen3.OpenURL)
	assert.Equal("", popItemLen3.FullURL)

	popItemLen3 = &PopupRespItem{}
	err = popItemLen3.ParsePopupURL(noFoldFullEventURL)
	assert.NoError(err)
	assert.Equal("http://example.com/miniurl", popItemLen3.MiniURL)
	assert.Equal("", popItemLen3.OpenURL)
	assert.Equal("http://example.com/fullurl", popItemLen3.FullURL)

	// 错误的 URL
	popItemErr := &PopupRespItem{}
	err = popItemErr.ParsePopupURL("http://example.com/miniurl;;;;http://example.com/fullurl")
	assert.Error(err, "urls no match")
}

func TestPopupCache(t *testing.T) {
	assert := assert.New(t)

	pre := make([]Popup, 3)
	pre[0].ElementID = 1
	savePopupToCache(pre)
	l := allPopupsFromCache()
	assert.Equal(pre, l)
	assert.Equal(int64(1), l[0].ElementID)
	ClearPopupCache()
	l = allPopupsFromCache()
	assert.Nil(l)
}

func TestFindAllPopups(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ClearPopupCache()
	l1, err := FindAllPopups()
	require.NoError(err)
	assert.NotEmpty(l1)
	assert.NotZero(l1[0].ElementID)
	assert.NotEmpty(l1[0].ExtendedFields)
	assert.NotEmpty(allPopupsFromCache())
	l2, err := FindAllPopups()
	require.NoError(err)
	assert.NotEmpty(l2)
	assert.NotZero(l2[0].ElementID)
	assert.JSONEq(tutil.SprintJSON(l1), tutil.SprintJSON(l2))
}
