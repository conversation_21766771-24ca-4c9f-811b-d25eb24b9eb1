package liverecommendedelements

import (
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 开播列表推荐
const (
	SquareTypeCatalog = -iota
	SquareTypeHot
	SquareTypeNova
	SquareTypeLimit
)

// ListSquareHotRecommended 分区置顶列表，默认开始时间倒叙排序
func ListSquareHotRecommended(p, pageSize int64) ([]*Model, goutil.Pagination, error) {
	db := tableElement(ElementSquare, service.DB).Where("expire_time > ?", goutil.TimeNow().Unix()).
		Order("start_time DESC")

	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}

	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*Model, 0), pa, nil
	}

	var res []*Model
	err = pa.ApplyTo(db).Find(&res).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	return res, pa, nil
}
