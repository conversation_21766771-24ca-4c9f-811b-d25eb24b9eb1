package liverecommendedelements

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestFindRoomIcon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	url := "http://show.png"
	r, err := FindRoomIcon([]int64{1000, 1001, 1002, 1003, 0})
	require.NoError(err)
	assert.Equal([]*RoomIcon{{1000, url}, {1001, url}}, r)
}

func TestFindOpenList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 热门
	res, err := FindOpenList(SquareTypeHot, 0, false)
	require.NoError(err)
	assert.Contains(res, Model{ElementID: 123, Sort: 1})
	assert.Contains(res, Model{ElementID: 1234, Sort: 2})

	// 分区
	res, err = FindOpenList(0, 10, false)
	require.NoError(err)
	assert.Contains(res, Model{ElementID: 1234, Sort: 2})

	// 新星
	res, err = FindOpenList(SquareTypeNova, 0, false)
	require.NoError(err)
	assert.Contains(res, Model{ElementID: 1234, Sort: 3})
}

func TestFindMedalFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()

	url, err := FindMedalFrame(223355)
	require.NoError(err)
	assert.NotEmpty(url)
	assert.Equal("oss://live/medalframes/3f12/level${level}_0_9_0_54.png", url)

	url, err = FindMedalFrame(120)
	require.NoError(err)
	assert.Empty(url)
}

func TestBuildBackgroundURL(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("http://test.png;0.50", BuildBackgroundURL("http://test.png", 0.5))
}

func TestParseBackgroundURL(t *testing.T) {
	assert := assert.New(t)

	url, opacity := ParseBackgroundURL("http://test.png;0.5")
	assert.Equal("http://test.png", url)
	assert.Equal(0.5, opacity)
}

func TestLoadRecommendCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()

	cache, err := loadRecommendCache(223355, ElementBackground)
	require.NoError(err)
	assert.NotNil(cache)
	assert.Equal("http://test_background.webp;0.55", cache.URL)

	cache, err = loadRecommendCache(99999, ElementBackground)
	require.NoError(err)
	assert.Nil(cache)
}

func TestFindBackground(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	service.Cache5Min.Flush()

	bg, err := FindBackground(223355)
	require.NoError(err)
	require.NotNil(bg)
	assert.Equal("http://test_background.webp", bg.ImageURL)
	assert.Equal(0.55, bg.Opacity)

	bg, err = FindBackground(120)
	require.NoError(err)
	assert.Nil(bg)
}

func TestFindCreatorCardByRoomID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var (
		testRoomID int64 = 11111
		now              = goutil.TimeNow()
	)

	err := service.DB.Delete(Model{}, "element_id = ?", testRoomID).Error
	require.NoError(err)
	eles := []*LiveRecommendedElements{
		{
			Sort:        1,
			URL:         "oss://test1",
			ElementID:   testRoomID,
			ElementType: ElementCreatorCard,
			StartTime:   goutil.NewInt64(now.Unix()),
			ExpireTime:  now.Unix() + 1,
		},
		{
			Sort:        1,
			URL:         "oss://test2",
			ElementID:   testRoomID,
			ElementType: ElementCreatorCard,
			StartTime:   goutil.NewInt64(now.Unix()),
			ExpireTime:  now.Unix() + 2,
		},
		{
			Sort:        0,
			URL:         "oss://test3",
			ElementID:   testRoomID,
			ElementType: ElementCreatorCard,
			StartTime:   goutil.NewInt64(now.Unix()),
			ExpireTime:  now.Unix() + 2,
		},
	}
	err = servicedb.BatchInsert(service.DB, TableName(), eles)
	require.NoError(err)
	service.Cache5Min.Flush()

	card, err := FindCreatorCard(testRoomID)
	require.NoError(err)
	require.NotNil(card)
	assert.Equal(storage.ParseSchemeURL("oss://test2"), card.FrameURL)
}
