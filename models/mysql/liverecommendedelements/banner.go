package liverecommendedelements

import (
	"time"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ListLiveBanners 返回指定时间段内的排期
func ListLiveBanners(begin, end time.Time) ([]Model, error) {
	var list []Model
	err := TableLiveBanners(service.DB).Where("expire_time > ? AND start_time < ?", begin.Unix(), end.Unix()).
		Order("start_time, sort").Find(&list).Error
	return list, err
}

// ListLiveBannersAt 返回 tm 当前所在的排期
func ListLiveBannersAt(tm time.Time) ([]Model, error) {
	var list []Model
	err := TableLiveBanners(service.DB).
		Where("start_time <= ?", tm.Unix()).
		Where("expire_time > ?", tm.Unix()).
		Order("sort").Find(&list).Error
	return list, err
}

// DeleteLiveBanner 删除 ID 对应的直播 banner 记录
func DeleteLiveBanner(id int64) (bool, error) {
	db := TableLiveBanners(service.DB).Where("id = ?", id).
		Where("expire_time > ?", goutil.TimeNow().Unix()).Delete("")
	if err := db.Error; err != nil {
		return false, err
	}
	if db.RowsAffected == 0 {
		return false, nil
	}
	return true, nil
}
