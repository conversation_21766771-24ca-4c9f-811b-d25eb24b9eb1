package liverecommendedelements

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestEventSave(t *testing.T) {
	require := require.New(t)

	now := goutil.TimeNow().Unix()
	event := Event{
		Attribute: Attribute{
			Name:      "test event save",
			Cover:     "https://static-test.missevan.com/cover.png",
			StartTime: &now,
		},
	}

	require.NoError(event.Save(service.DB))
}

func TestEventCache(t *testing.T) {
	assert := assert.New(t)

	pre := make([]Event, 3)
	saveEventToCache(pre)
	l := allEventFromCache()
	assert.Equal(pre, l)
	ClearEventCache()
	l = allEventFromCache()
	assert.Nil(l)
}

func TestFindAllEvents(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ClearEventCache()
	l1, err := FindAllEvents()
	require.NoError(err)
	assert.NotNil(l1)
	tutil.PrintJSON(l1)
	assert.NotNil(allEventFromCache())
	l2, err := FindAllEvents()
	require.NoError(err)
	assert.NotNil(l2)
	assert.JSONEq(tutil.SprintJSON(l1), tutil.SprintJSON(l2))
}

func TestEventExtendedFields(t *testing.T) {
	assert := assert.New(t)

	e := EventExtendedFields{
		CatalogIDs: []int64{},
		TagIDs:     []int64{},
		RoomIDs:    []int64{},
		OldCover:   "test",
	}
	assert.True(e.IsAllRoomShow())

	e.RoomIDs = []int64{2233}
	assert.False(e.IsAllRoomShow())
}
