package liverecommendedelements

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestEnsureCoverURL(t *testing.T) {
	assert := assert.New(t)
	r := new(Room)
	r.EnsureCoverURL()
	assert.NotEmpty(r.CoverURL)
}

func TestRoomTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Room{}, "position", "room_id", "name", "cover_url", "creator_id",
		"creator_username", "creator_iconurl", "catalog_id", "catalog_name", "catalog_color",
		"custom_tag", "status")
}
