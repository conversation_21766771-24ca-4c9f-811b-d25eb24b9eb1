package liverecommendedelements

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSquareType(t *testing.T) {
	assert := assert.New(t)
	assert.Equal(-1, SquareTypeHot)
	assert.Equal(-2, <PERSON>Type<PERSON>ova)
}

func TestListSquareHotRecommended(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, pa, err := ListSquareHotRecommended(1, 20)
	require.NoError(err)
	assert.NotEmpty(list)
	assert.GreaterOrEqual(pa.Count, int64(len(list)))
}
