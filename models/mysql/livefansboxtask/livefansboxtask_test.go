package livefansboxtask

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/liveim"
	mongodbgift "github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxusertask"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveFansBoxTask{}, "id", "create_time", "modified_time", "bizdate", "room_id", "fans_count",
		"level", "energy", "status")
	kc.Check(TaskInfo{}, "id", "room_id", "level", "status", "current_energy", "target_energy", "more")
}

func TestLiveFansBoxTask_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_fans_box_task", tableName)
}

func TestLiveFansBoxTask_BeforeCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &LiveFansBoxTask{}
	require.NoError(l.BeforeCreate())
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
}

func TestLiveFansBoxTask_BeforeUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &LiveFansBoxTask{}
	require.NoError(l.BeforeUpdate())
	assert.NotZero(l.ModifiedTime)
}

func TestGetHasStockRewards(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock data
	roomID := int64(75456425)
	level := livefansbox.Level2
	rewards := []livefansbox.RewardInfo{
		{Type: livefansbox.RewardTypeFreeGift, DailyStock: 100, PrizeIDs: []int64{6543001, 6543002}},
		{Type: livefansbox.RewardTypeAvatarFrame, DailyStock: 100, PrizeIDs: []int64{6543003}},
	}
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key1 := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, livefansbox.RewardTypeFreeGift,
		todayFormat)
	key2 := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, livefansbox.RewardTypeAvatarFrame,
		todayFormat)
	require.NoError(service.Redis.Del(key1, key2).Err())
	require.NoError(service.Redis.IncrBy(key2, 100).Err())

	// 测试未配置奖励信息时
	task := TaskInfo{
		RoomID: roomID,
		Level:  level,
	}
	stockRewards, err := task.GetHasStockRewards()
	require.NoError(err)
	assert.Empty(stockRewards)

	// 测试配置了奖励信息时
	task.MoreInfo.Rewards = rewards
	stockRewards, err = task.GetHasStockRewards()
	require.NoError(err)
	require.Len(stockRewards, 1)
	assert.Equal(livefansbox.RewardTypeFreeGift, stockRewards[0].Type)
}

func TestFindTodayTask(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试查找不存在的任务
	roomID := int64(312547425)
	require.NoError(LiveFansBoxTask{}.DB().Delete("", "room_id = ?", roomID).Error)
	taskInfo, err := FindTodayTask(roomID)
	require.NoError(err)
	assert.Nil(taskInfo)

	// 测试查找今日任务
	// Mock data
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	task := &LiveFansBoxTask{
		Level:     livefansbox.Level2,
		Bizdate:   todayFormat,
		RoomID:    roomID,
		FansCount: 50,
		Energy:    2333,
		Status:    StatusFinished,
	}
	require.NoError(LiveFansBoxTask{}.DB().Create(task).Error)
	taskInfo, err = FindTodayTask(roomID)
	require.NoError(err)
	assert.NotNil(taskInfo)
	assert.Equal(roomID, taskInfo.RoomID)
	assert.Equal(livefansbox.Level2, taskInfo.Level)
	assert.Equal(StatusFinished, taskInfo.Status)
	assert.Equal(2333, taskInfo.CurrentEnergy) // 验证当前能量值已正确查询
	assert.NotZero(taskInfo.TargetEnergy)      // 验证目标能量值已正确查询
	assert.Len(taskInfo.MoreInfo.Rewards, 1)
}

func TestFindOrCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试创建粉丝团宝箱任务
	roomID := int64(312547425)
	require.NoError(LiveFansBoxTask{}.DB().Delete("", "room_id = ?", roomID).Error)
	now := goutil.TimeNow()
	taskInfo, err := FindOrCreate(nil, roomID, now)
	require.NoError(err)
	assert.NotNil(taskInfo)
	assert.Equal(roomID, taskInfo.RoomID)
	assert.EqualValues(0, taskInfo.FansCount)
	assert.Equal(livefansbox.Level1, taskInfo.Level)
	assert.Equal(0, taskInfo.Energy)
	assert.Equal(StatusUnfinished, taskInfo.Status)

	// 测试获取粉丝团宝箱任务
	require.NoError(taskInfo.DB().Where("id = ?", taskInfo.ID).Update("energy", 2333).Error)
	taskInfo1, err := FindOrCreate(nil, roomID, now)
	require.NoError(err)
	require.NotNil(taskInfo1)
	assert.Equal(taskInfo.ID, taskInfo1.ID)
	assert.Equal(2333, taskInfo1.Energy)
}

func TestTaskInfo_updateTaskEnergy(t *testing.T) {
	now := goutil.TimeNow()
	today := now.Format(goutil.TimeFormatYMD)
	testRoomID := int64(87654321)

	// 清理测试数据
	cleanTestTask := func() {
		LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
	}
	cleanTestTask()
	defer cleanTestTask()

	// 创建测试数据
	testTask := &LiveFansBoxTask{
		Bizdate:   today,
		RoomID:    testRoomID,
		FansCount: 50,
		Level:     livefansbox.Level2,
		Energy:    0,
		Status:    StatusUnfinished,
	}
	require.NoError(t, LiveFansBoxTask{}.DB().Create(testTask).Error)

	// 获取宝箱配置
	boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
	require.NoError(t, err)
	boxConfig, exists := boxConfigMap[livefansbox.Level2]
	require.True(t, exists)
	require.NotNil(t, boxConfig)

	taskInfo := &TaskInfo{
		ID:           testTask.ID,
		RoomID:       testRoomID,
		Level:        livefansbox.Level2,
		Status:       StatusUnfinished,
		TargetEnergy: boxConfig.Energy,
	}

	t.Run("NormalEnergyUpdate", func(t *testing.T) {
		energyAdd := 100
		ok, err := taskInfo.updateTaskEnergy(nil, energyAdd)
		require.NoError(t, err)
		assert.True(t, ok)

		// 验证能量值更新
		var updatedTask LiveFansBoxTask
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(&updatedTask).Error
		require.NoError(t, err)
		assert.Equal(t, energyAdd, updatedTask.Energy)
		assert.Equal(t, StatusUnfinished, updatedTask.Status)
	})

	t.Run("EnergyReachesTargetAndCompleteTask", func(t *testing.T) {
		energyAdd := taskInfo.TargetEnergy - 100 + 50 // 超过目标能量
		ok, err := taskInfo.updateTaskEnergy(nil, energyAdd)
		require.NoError(t, err)
		assert.True(t, ok)

		// 验证任务完成
		var updatedTask LiveFansBoxTask
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(&updatedTask).Error
		require.NoError(t, err)
		assert.Equal(t, taskInfo.TargetEnergy, updatedTask.Energy) // 能量值不应超过目标值
		assert.Equal(t, StatusFinished, updatedTask.Status)        // 任务应该已完成
	})

	t.Run("CompletedTaskNoLongerUpdates", func(t *testing.T) {
		// 任务已完成，尝试继续更新
		ok, err := taskInfo.updateTaskEnergy(nil, 100)
		require.NoError(t, err)
		assert.False(t, ok) // 应该返回 false，表示没有更新
	})

	t.Run("NonExistentTaskID", func(t *testing.T) {
		fakeTaskInfo := &TaskInfo{
			ID:           999999999,
			TargetEnergy: 1000,
		}
		ok, err := fakeTaskInfo.updateTaskEnergy(nil, 100)
		require.NoError(t, err)
		assert.False(t, ok) // 应该返回 false，表示没有找到记录
	})
}

func TestTaskInfo_ContributeEnergy(t *testing.T) {
	now := goutil.TimeNow()
	today := now.Format(goutil.TimeFormatYMD)

	t.Run("CompletedTaskNoLongerUpdates", func(t *testing.T) {
		// 测试已完成任务不再更新的情况
		taskInfo := &TaskInfo{
			ID:           123,
			RoomID:       456,
			Level:        livefansbox.Level2,
			Status:       StatusFinished, // 已完成状态
			TargetEnergy: 1000,
		}

		// 已完成的任务调用 ContributeEnergy 应该直接返回 false, false, nil，不进行任何操作
		updated, refreshed, err := taskInfo.ContributeEnergy(456, 789, 100)
		require.NoError(t, err)
		assert.False(t, updated)
		assert.False(t, refreshed)
	})

	t.Run("NormalEnergyContribution", func(t *testing.T) {
		testRoomID := int64(98764321)
		testUserID := int64(12345678)

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
			livefansboxusertask.LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID)
		}
		cleanTestData()
		defer cleanTestData()

		// 创建测试任务
		testTask := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    0,
			Status:    StatusUnfinished,
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(testTask).Error)

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		taskInfo := &TaskInfo{
			ID:           testTask.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: boxConfig.Energy,
		}

		energyAdd := 100
		updated, refreshed, err := taskInfo.ContributeEnergy(testRoomID, testUserID, energyAdd)
		require.NoError(t, err)
		assert.True(t, updated)
		assert.True(t, refreshed)

		// 验证任务能量值更新
		var updatedTask LiveFansBoxTask
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(&updatedTask).Error
		require.NoError(t, err)
		assert.Equal(t, energyAdd, updatedTask.Energy)
		assert.Equal(t, StatusUnfinished, updatedTask.Status)

		// 验证用户能量贡献记录
		userTask, err := livefansboxusertask.FindUserTask(testTask.ID, testUserID)
		require.NoError(t, err)
		require.NotNil(t, userTask)
		assert.Equal(t, testRoomID, userTask.RoomID)
		assert.Equal(t, testUserID, userTask.UserID)
		assert.Equal(t, testTask.ID, userTask.FansBoxTaskID)
		assert.Equal(t, energyAdd, userTask.UserEnergy)
	})

	t.Run("EnergyAccumulationButTaskNotCompleted", func(t *testing.T) {
		testRoomID := int64(98764322)
		testUserID := int64(12345679)

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
			livefansboxusertask.LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID)
		}
		cleanTestData()
		defer cleanTestData()

		// 创建测试任务
		testTask := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    100, // 初始能量为 100
			Status:    StatusUnfinished,
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(testTask).Error)

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		taskInfo := &TaskInfo{
			ID:           testTask.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: boxConfig.Energy,
		}

		energyAdd := 200
		updated, refreshed, err := taskInfo.ContributeEnergy(testRoomID, testUserID, energyAdd)
		require.NoError(t, err)
		assert.True(t, updated)
		assert.True(t, refreshed)

		// 验证任务能量值累加
		var updatedTask LiveFansBoxTask
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(&updatedTask).Error
		require.NoError(t, err)
		assert.Equal(t, 300, updatedTask.Energy)              // 100 + 200 = 300
		assert.Equal(t, StatusUnfinished, updatedTask.Status) // 仍然未完成

		// 验证用户能量贡献记录
		userTask, err := livefansboxusertask.FindUserTask(testTask.ID, testUserID)
		require.NoError(t, err)
		require.NotNil(t, userTask)
		assert.Equal(t, energyAdd, userTask.UserEnergy) // 用户贡献了 200 能量
	})

	t.Run("EnergyAccumulationAndCompleteTask", func(t *testing.T) {
		testRoomID := int64(98764323)
		testUserID := int64(12345680)

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
			livefansboxusertask.LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID)
		}
		cleanTestData()
		defer cleanTestData()

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		// 创建测试任务，初始能量接近目标值
		initialEnergy := boxConfig.Energy - 50
		testTask := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    initialEnergy,
			Status:    StatusUnfinished,
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(testTask).Error)

		taskInfo := &TaskInfo{
			ID:           testTask.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: boxConfig.Energy,
		}

		// 贡献足够完成任务的能量
		energyAdd := 100 // 超过所需的 50
		updated, refreshed, err := taskInfo.ContributeEnergy(testRoomID, testUserID, energyAdd)
		require.NoError(t, err)
		assert.True(t, updated)
		assert.True(t, refreshed)

		// 验证任务完成
		var updatedTask LiveFansBoxTask
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(&updatedTask).Error
		require.NoError(t, err)
		assert.Equal(t, taskInfo.TargetEnergy, updatedTask.Energy) // 能量值不应超过目标值
		assert.Equal(t, StatusFinished, updatedTask.Status)        // 任务应该已完成

		// 验证用户能量贡献记录
		userTask, err := livefansboxusertask.FindUserTask(testTask.ID, testUserID)
		require.NoError(t, err)
		require.NotNil(t, userTask)
		assert.Equal(t, energyAdd, userTask.UserEnergy) // 用户能量记录应该是实际贡献的值
	})

	t.Run("NoUpdateAfterTaskCompletion", func(t *testing.T) {
		testRoomID := int64(98764324)
		testUserID := int64(12345681)

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
			livefansboxusertask.LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID)
		}
		cleanTestData()
		defer cleanTestData()

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		// 创建已完成的测试任务
		testTask := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    boxConfig.Energy, // 已达到目标能量
			Status:    StatusFinished,   // 已完成
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(testTask).Error)

		taskInfo := &TaskInfo{
			ID:           testTask.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusFinished, // 已完成状态
			TargetEnergy: boxConfig.Energy,
		}

		// 获取任务完成前的状态
		beforeTask := &LiveFansBoxTask{}
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(beforeTask).Error
		require.NoError(t, err)

		// 尝试继续贡献能量
		updated, refreshed, err := taskInfo.ContributeEnergy(testRoomID, testUserID, 100)
		require.NoError(t, err)
		assert.False(t, updated)
		assert.False(t, refreshed)

		// 验证任务状态没有变化
		afterTask := &LiveFansBoxTask{}
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(afterTask).Error
		require.NoError(t, err)
		assert.Equal(t, beforeTask.Energy, afterTask.Energy)
		assert.Equal(t, beforeTask.Status, afterTask.Status)
	})

	t.Run("ZeroEnergyContribution", func(t *testing.T) {
		testRoomID := int64(98764325)
		testUserID := int64(12345682)

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
		}
		cleanTestData()
		defer cleanTestData()

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		task := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    100,
			Status:    StatusUnfinished,
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(task).Error)

		taskInfo := &TaskInfo{
			ID:           task.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: boxConfig.Energy,
		}

		beforeTask := &LiveFansBoxTask{}
		err = LiveFansBoxTask{}.DB().Where("id = ?", task.ID).Take(beforeTask).Error
		require.NoError(t, err)

		// 贡献 0 能量
		updated, refreshed, err := taskInfo.ContributeEnergy(testRoomID, testUserID, 0)
		require.NoError(t, err)
		assert.True(t, updated)
		assert.True(t, refreshed)

		// 验证任务状态没有变化
		afterTask := &LiveFansBoxTask{}
		err = LiveFansBoxTask{}.DB().Where("id = ?", task.ID).Take(afterTask).Error
		require.NoError(t, err)
		assert.Equal(t, beforeTask.Energy, afterTask.Energy)
		assert.Equal(t, beforeTask.Status, afterTask.Status)
	})

	t.Run("NegativeEnergyContribution", func(t *testing.T) {
		testRoomID := int64(98764326)
		testUserID := int64(12345683)

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
		}
		cleanTestData()
		defer cleanTestData()

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		task := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    100,
			Status:    StatusUnfinished,
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(task).Error)

		taskInfo := &TaskInfo{
			ID:           task.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: boxConfig.Energy,
		}

		beforeTask := &LiveFansBoxTask{}
		err = LiveFansBoxTask{}.DB().Where("id = ?", task.ID).Take(beforeTask).Error
		require.NoError(t, err)

		// 贡献负能量
		updated, refreshed, err := taskInfo.ContributeEnergy(testRoomID, testUserID, -50)
		require.NoError(t, err)
		assert.True(t, updated)
		assert.True(t, refreshed)

		// 验证任务状态处理负能量的情况
		afterTask := &LiveFansBoxTask{}
		err = LiveFansBoxTask{}.DB().Where("id = ?", task.ID).Take(afterTask).Error
		require.NoError(t, err)
		// 能量值应该不变或减少，但不应该为负数
		assert.GreaterOrEqual(t, afterTask.Energy, 0)
	})
}

func TestTaskInfo_NewTaskUpdateMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(123456)
	taskID := int64(789)
	targetEnergy := 10000
	currentEnergy := 1400
	status := StatusUnfinished

	taskInfo := &TaskInfo{
		ID:            taskID,
		RoomID:        roomID,
		Level:         livefansbox.Level2,
		Status:        status,
		CurrentEnergy: currentEnergy,
		TargetEnergy:  targetEnergy,
	}

	message := taskInfo.NewTaskUpdateMessage()

	assert.Equal(liveim.TypeFansBox, message.Type)
	assert.Equal(liveim.EventFansBoxTaskUpdate, message.Event)
	assert.Equal(roomID, message.RoomID)
	require.NotNil(message.FansBox)
	require.NotNil(message.FansBox.BoxTask)
	assert.Equal(taskID, message.FansBox.BoxTask.ID)
	assert.Equal(targetEnergy, message.FansBox.BoxTask.TargetEnergy)
	assert.Equal(currentEnergy, message.FansBox.BoxTask.CurrentEnergy)
	assert.Equal(status, message.FansBox.BoxTask.Status)
}

func TestIsEligibleGiftType(t *testing.T) {
	tests := []struct {
		gift     *mongodbgift.Gift
		expected bool
	}{
		{
			nil,
			false,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeNormal},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeNoble},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeUpgrade},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeBlackCard},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeCustom},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeRoomCustom},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeDrawSend},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeMedal},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeSuperFan},
			true,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeFree},
			false,
		},
		{
			&mongodbgift.Gift{Type: mongodbgift.TypeRebate},
			false,
		},
		{
			&mongodbgift.Gift{Type: -1},
			false,
		},
	}

	for i, tt := range tests {
		t.Run(fmt.Sprintf("TestCase%d", i), func(t *testing.T) {
			result := isEligibleGiftType(tt.gift)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTaskInfo_ContributeFromGift(t *testing.T) {
	now := goutil.TimeNow()
	today := now.Format(goutil.TimeFormatYMD)

	t.Run("DisabledFansBox", func(t *testing.T) {
		// 保存原始配置值
		originalConfig := config.Conf.Params.MedalParams.EnableFansBox
		defer func() {
			// 还原配置值
			config.Conf.Params.MedalParams.EnableFansBox = originalConfig
		}()

		// 禁用粉丝宝箱功能
		config.Conf.Params.MedalParams.EnableFansBox = false

		taskInfo := &TaskInfo{
			ID:           123,
			RoomID:       456,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: 1000,
		}

		param := GiftContribution{
			RoomID:  456,
			UserID:  789,
			Gift:    &mongodbgift.Gift{Type: mongodbgift.TypeNormal, Price: 100},
			GiftNum: 1,
		}

		// 当功能被禁用时，应该直接返回 false, false, nil
		updated, refreshed, err := taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "粉丝宝箱功能被禁用时，不应该贡献能量")
		assert.False(t, refreshed)
	})

	t.Run("IneligibleGiftType", func(t *testing.T) {
		taskInfo := &TaskInfo{
			ID:           123,
			RoomID:       456,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: 1000,
		}

		// 测试免费礼物（不符合要求的礼物类型）
		param := GiftContribution{
			RoomID:  456,
			UserID:  789,
			Gift:    &mongodbgift.Gift{Type: mongodbgift.TypeFree, Price: 0},
			GiftNum: 1,
		}

		updated, refreshed, err := taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "免费礼物不应该贡献能量")
		assert.False(t, refreshed)

		// 测试白给礼物（不符合要求的礼物类型）
		param.Gift.Type = mongodbgift.TypeRebate
		updated, refreshed, err = taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "白给礼物不应该贡献能量")
		assert.False(t, refreshed)
	})

	t.Run("UserNotFansMember", func(t *testing.T) {
		testRoomID := int64(98765432)
		testUserID := int64(87654321)

		taskInfo := &TaskInfo{
			ID:           123,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: 1000,
		}

		param := GiftContribution{
			RoomID:  testRoomID,
			UserID:  testUserID,
			Gift:    &mongodbgift.Gift{Type: mongodbgift.TypeNormal, Price: 100},
			GiftNum: 1,
		}

		updated, refreshed, err := taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "非粉丝团成员不应该能贡献能量")
		assert.False(t, refreshed)
	})

	t.Run("ZeroOrNegativePrice", func(t *testing.T) {
		taskInfo := &TaskInfo{
			ID:           123,
			RoomID:       456,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: 1000,
		}

		// 测试 0 价格礼物
		param := GiftContribution{
			RoomID:  456,
			UserID:  789,
			Gift:    &mongodbgift.Gift{Type: mongodbgift.TypeNormal, Price: 0},
			GiftNum: 1,
		}

		updated, refreshed, err := taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "0 价格礼物不应该贡献能量")
		assert.False(t, refreshed)

		// 测试负价格礼物
		param.Gift.Price = -100
		updated, refreshed, err = taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "负价格礼物不应该贡献能量")
		assert.False(t, refreshed)

		// 测试 0 数量礼物
		param.Gift.Price = 100
		param.GiftNum = 0
		updated, refreshed, err = taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "0 数量礼物不应该贡献能量")
		assert.False(t, refreshed)
	})

	t.Run("ValidGiftContribution", func(t *testing.T) {
		testRoomID := int64(98765433)
		testUserID := int64(87654322)
		testCreatorID := int64(12345678)

		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		// 清理测试数据
		cleanTestData := func() {
			LiveFansBoxTask{}.DB().Delete("", "room_id = ?", testRoomID)
			livefansboxusertask.LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID)
			service.MongoDB.Collection("live_medals").DeleteMany(ctx, bson.M{
				"room_id": testRoomID,
				"user_id": testUserID,
			})
		}
		cleanTestData()
		defer cleanTestData()

		// 创建测试任务
		testTask := &LiveFansBoxTask{
			Bizdate:   today,
			RoomID:    testRoomID,
			FansCount: 50,
			Level:     livefansbox.Level2,
			Energy:    0,
			Status:    StatusUnfinished,
		}
		require.NoError(t, LiveFansBoxTask{}.DB().Create(testTask).Error)

		// 创建测试勋章
		testMedal := livemedal.Simple{
			RoomID:    testRoomID,
			UserID:    testUserID,
			CreatorID: testCreatorID,
			Status:    livemedal.StatusOwned,
		}
		_, err := service.MongoDB.Collection("live_medals").InsertOne(ctx, testMedal)
		require.NoError(t, err)

		// 获取宝箱配置
		boxConfigMap, err := livefansbox.FindLevelFansBoxMap()
		require.NoError(t, err)
		boxConfig, exists := boxConfigMap[livefansbox.Level2]
		require.True(t, exists)
		require.NotNil(t, boxConfig)

		taskInfo := &TaskInfo{
			ID:           testTask.ID,
			RoomID:       testRoomID,
			Level:        livefansbox.Level2,
			Status:       StatusUnfinished,
			TargetEnergy: boxConfig.Energy,
		}

		// 测试有效的礼物贡献
		giftPrice := int64(50)
		giftNum := 2
		expectedEnergy := giftPrice * int64(giftNum) // 50 * 2 = 100

		param := GiftContribution{
			RoomID:  testRoomID,
			UserID:  testUserID,
			Gift:    &mongodbgift.Gift{Type: mongodbgift.TypeNormal, Price: giftPrice},
			GiftNum: giftNum,
		}

		updated, refreshed, err := taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.True(t, updated)
		assert.True(t, refreshed)

		// 验证任务能量值更新
		var updatedTask LiveFansBoxTask
		err = LiveFansBoxTask{}.DB().Where("id = ?", testTask.ID).Take(&updatedTask).Error
		require.NoError(t, err)
		assert.Equal(t, int(expectedEnergy), updatedTask.Energy)

		// 验证用户能量贡献记录
		userTask, err := livefansboxusertask.FindUserTask(testTask.ID, testUserID)
		require.NoError(t, err)
		if userTask != nil {
			assert.Equal(t, testRoomID, userTask.RoomID)
			assert.Equal(t, testUserID, userTask.UserID)
			assert.Equal(t, testTask.ID, userTask.FansBoxTaskID)
			assert.Equal(t, int(expectedEnergy), userTask.UserEnergy)
		}
	})

	t.Run("CompletedTask", func(t *testing.T) {
		// 测试已完成的任务不应该再接受能量贡献
		taskInfo := &TaskInfo{
			ID:           123,
			RoomID:       456,
			Level:        livefansbox.Level2,
			Status:       StatusFinished, // 已完成状态
			TargetEnergy: 1000,
		}

		param := GiftContribution{
			RoomID:  456,
			UserID:  789,
			Gift:    &mongodbgift.Gift{Type: mongodbgift.TypeNormal, Price: 100},
			GiftNum: 1,
		}

		updated, refreshed, err := taskInfo.ContributeFromGift(param)
		require.NoError(t, err)
		assert.False(t, updated, "已完成的任务不应该再接受能量贡献")
		assert.False(t, refreshed)
	})
}
