package certification

import (
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Certification 实名认证表
type Certification struct {
	ID         int64   `gorm:"column:id;primary_key" json:"id"`
	UserID     int64   `gorm:"column:user_id" json:"user_id"`
	UserName   string  `gorm:"column:user_name" json:"user_name"`
	RealName   string  `gorm:"column:real_name" json:"real_name"`
	Gender     uint8   `gorm:"column:gender" json:"gender"`
	IDType     uint8   `gorm:"column:id_type" json:"id_type"`
	IDNumber   string  `gorm:"column:id_number" json:"id_number"`
	IDPeople   string  `gorm:"column:id_people" json:"id_people"`
	IDFront    string  `gorm:"column:id_front" json:"id_front"`
	IDBack     string  `gorm:"column:id_back" json:"id_back"`
	Method     uint8   `gorm:"column:method" json:"method"`
	Checked    Checked `gorm:"column:checked" json:"checked"`
	CreateTime int64   `gorm:"column:create_time" json:"create_time"`
	UpdateTime int64   `gorm:"column:update_time" json:"-"`
}

// Checked 审核状态
type Checked = int8

// Method 认证来源
type Method = uint8

// IDType 证件类型
type IDType = uint8

// Gender 性别
type Gender = uint8

// 审核状态
const (
	CheckedArchived Checked = iota - 2 // 被归档（被注销的用户的实名认证信息）
	CheckedInvalid                     // 已失效
	CheckedChecking                    // 审核中
	CheckedPassed                      // 已过审
	CheckRejected                      // 被拒绝
)

// 认证来源
const (
	MethodUnknown Method = iota // 未知
	MethodIDCard                // 身份证
	MethodZhima                 // 芝麻信用
)

// IDTypeEncrypt 加密常量
const IDTypeEncrypt = 10

// 证件类型
const (
	IDTypeIDCard                   IDType = iota + 1 // 身份证
	IDTypeTravelPermitHK                             // 港澳居民来往内地通行证
	IDTypeTravelPermitTW                             // 台湾居民来往大陆通行证
	IDTypePassportCN                                 // 护照（中国签发）
	IDTypePassportForeign                            // 护照（境外签发）
	IDTypeResidencePermitForeigner                   // 外国人永久居留证
)

// 性别
const (
	GenderUnknown Gender = iota // 未知
	GenderMale                  // 男
	GenderFemale                // 女
)

const tableName = "certification"

// TableName table name of Certification model
func TableName() string {
	return tableName
}

// TableName table name of Certification model
func (cr *Certification) TableName() string {
	return tableName
}

// MosaicRealName get mosaic real name
func (cr *Certification) MosaicRealName() string {
	realName, err := cr.DecryptedRealName()
	if err != nil {
		logger.Error(err)
		return ""
	}

	return goutil.MosaicString(realName, goutil.MosaicRealName)
}

// MosaicIDNumber get mosaic id number
func (cr *Certification) MosaicIDNumber() string {
	idNumber, err := cr.DecryptedIDNumber()
	if err != nil {
		logger.Error(err)
		return ""
	}

	return goutil.MosaicString(idNumber, goutil.MosaicIDNumber)
}

// DecryptedRealName get decrypted real name
func (cr *Certification) DecryptedRealName() (realName string, err error) {
	realName = cr.RealName
	if cr.IDType > IDTypeEncrypt {
		iv := strconv.FormatInt(cr.CreateTime, 10)
		realName, err = goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, cr.RealName)
		if err != nil {
			return
		}
	}

	return
}

// DecryptedIDNumber get decrypted id number
func (cr *Certification) DecryptedIDNumber() (idNumber string, err error) {
	idNumber = cr.IDNumber
	if cr.IDType > IDTypeEncrypt {
		iv := config.Conf.Params.Security.SensitiveFixedIVKey
		idNumber, err = goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, cr.IDNumber)
		if err != nil {
			return
		}
	}

	return
}

// Info get user certification info
func Info(creatorID int64) (cert *Certification, err error) {
	cert = new(Certification)
	err = service.DB.Table(TableName()).
		Select("id, real_name, id_number, id_type, create_time").
		First(&cert, "user_id = ? AND checked = ?", creatorID, CheckedPassed).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, err
	}

	return
}
