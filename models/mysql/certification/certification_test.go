package certification

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestMosaicIDNumber(t *testing.T) {
	require := require.New(t)

	cert := new(Certification)
	idNumber := "123456789012345678"

	iv := config.Conf.Params.Security.SensitiveFixedIVKey
	cert.IDType = IDTypeEncrypt + IDTypeIDCard
	cert.IDNumber = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, idNumber)

	idNumberMosaic := cert.MosaicIDNumber()
	require.Equal(goutil.MosaicString(idNumber, goutil.MosaicIDNumber), idNumberMosaic)
}

func TestMosaicRealName(t *testing.T) {
	require := require.New(t)

	cert := new(Certification)
	timeStamp := goutil.TimeNow().Unix()
	realName := "realname12345"

	cert.IDType = IDTypeEncrypt + IDTypeIDCard
	iv := strconv.FormatInt(timeStamp, 10)
	cert.CreateTime = timeStamp
	cert.RealName = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, realName)

	require.Equal(goutil.MosaicString(realName, goutil.MosaicRealName), cert.MosaicRealName())
}

func TestDecryptedRealName(t *testing.T) {
	require := require.New(t)

	cert := new(Certification)
	timeStamp := goutil.TimeNow().Unix()
	realName := "real_name"

	iv := strconv.FormatInt(timeStamp, 10)
	cert.IDType = IDTypeEncrypt + IDTypeIDCard
	cert.CreateTime = timeStamp
	cert.RealName = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, realName)

	realNameDecrypted, err := cert.DecryptedRealName()
	require.NoError(err)
	require.Equal(realName, realNameDecrypted)
}

func TestDecryptedIDNumber(t *testing.T) {
	require := require.New(t)

	cert := new(Certification)
	idNumber := "123456789012345678"

	cert.IDType = IDTypeEncrypt + IDTypeIDCard
	iv := config.Conf.Params.Security.SensitiveFixedIVKey
	cert.IDNumber = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, idNumber)

	idNumberDecrypted, err := cert.DecryptedIDNumber()
	require.NoError(err)
	require.Equal(idNumber, idNumberDecrypted)
}

func TestInfo(t *testing.T) {
	require := require.New(t)

	cert, err := Info(99999999999)
	require.NoError(err)
	require.Nil(cert)

	creatorID := int64(33333)
	defer require.NoError(service.DB.Table(TableName()).Delete("", "user_id = ?", creatorID).Error)
	require.NoError(service.DB.Create(&Certification{
		UserID:     creatorID,
		UserName:   "user_name",
		RealName:   "real_name",
		Gender:     GenderMale,
		IDType:     IDTypeIDCard,
		IDNumber:   "afKls4D3",
		IDPeople:   "oss://nocover.png",
		IDFront:    "oss://nocover.png",
		IDBack:     "oss://nocover.png",
		Method:     MethodZhima,
		Checked:    CheckedPassed,
		CreateTime: goutil.TimeNow().Unix(),
		UpdateTime: goutil.TimeNow().Unix(),
	}).Error)

	cert, err = Info(creatorID)
	require.NoError(err)
	require.NotNil(cert)
	require.Greater(cert.ID, int64(0))
}
