package guildtransferhistory

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 签约类型
const (
	TypeContractOriginal = iota + 1 // 续签
	TypeContractAgain               // 重签
)

// 当月流水和时长所属类型
const (
	TypeRevenueOriginal = iota + 1 // 原公会
	TypeRevenueNew                 // 新公会
)

const tableName = "guild_transfer_history"

// TableName table name
func TableName() string {
	return tableName
}

// GuildTransferHistory 转会历史表
type GuildTransferHistory struct {
	ID                      int64 `gorm:"column:id;primary_key" json:"id"`
	CreateTime              int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime            int64 `gorm:"column:modified_time" json:"-"`
	OperatorID              int64 `gorm:"column:operator_id" json:"operator_id"`
	CreatorID               int64 `gorm:"column:creator_id" json:"creator_id"`
	FromGuildID             int64 `gorm:"column:from_guild_id" json:"from_guild_id"`
	ToGuildID               int64 `gorm:"column:to_guild_id" json:"to_guild_id"`
	RoomID                  int64 `gorm:"column:room_id" json:"room_id"`
	RevenueLiveDurationType int   `gorm:"column:revenue_live_duration_type" json:"revenue_live_duration_type"`
	ContractType            int   `gorm:"column:contract_type" json:"contract_type"`
}

// DB the db instance of GuildTransferHistory model
func (gth GuildTransferHistory) DB() *gorm.DB {
	return service.LiveDB.Table(gth.TableName())
}

// TableName provides table name
func (GuildTransferHistory) TableName() string {
	return tableName
}

// BeforeSave gorm hook
func (gth *GuildTransferHistory) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if gth.DB().NewRecord(gth) {
		gth.CreateTime = nowTime
	}
	gth.ModifiedTime = nowTime
	return
}

// SwitchRevenueLiveDurationType 获取当月流水和时长转入类型
func SwitchRevenueLiveDurationType(revenueLiveDurationType int) (info string) {
	switch revenueLiveDurationType {
	case TypeRevenueOriginal:
		return "原公会"
	case TypeRevenueNew:
		return "新公会"
	default:
		panic(fmt.Sprintf("未知类型: %d", revenueLiveDurationType))
	}
}

// SwitchContractType 获取签约类型
func SwitchContractType(contractType int) (info string) {
	switch contractType {
	case TypeContractOriginal:
		return "续签"
	case TypeContractAgain:
		return "重签"
	default:
		panic(fmt.Sprintf("未知类型: %d", contractType))
	}
}
