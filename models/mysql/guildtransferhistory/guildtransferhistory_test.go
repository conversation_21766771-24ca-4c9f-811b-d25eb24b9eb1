package guildtransferhistory

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestGuildTransferHistoryTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.GORM).Check(GuildTransferHistory{},
		"id", "create_time", "modified_time", "operator_id", "creator_id", "from_guild_id",
		"to_guild_id", "room_id", "revenue_live_duration_type", "contract_type")

	tutil.NewKeyChecker(t, tutil.JSON).Check(GuildTransferHistory{},
		"id", "create_time", "operator_id", "creator_id", "from_guild_id",
		"to_guild_id", "room_id", "revenue_live_duration_type", "contract_type")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("guild_transfer_history", TableName())
	assert.Equal("guild_transfer_history", GuildTransferHistory{}.TableName())
}

func TestSwitchRevenueLiveDurationType(t *testing.T) {
	assert := assert.New(t)

	s := SwitchRevenueLiveDurationType(TypeRevenueOriginal)
	assert.Equal("原公会", s)

	s = SwitchRevenueLiveDurationType(TypeRevenueNew)
	assert.Equal("新公会", s)

	assert.PanicsWithValue("未知类型: 3", func() { SwitchRevenueLiveDurationType(3) })
}

func TestSwitchContractType(t *testing.T) {
	assert := assert.New(t)

	s := SwitchContractType(TypeContractOriginal)
	assert.Equal("续签", s)

	s = SwitchContractType(TypeContractAgain)
	assert.Equal("重签", s)

	assert.PanicsWithValue("未知类型: 3", func() { SwitchContractType(3) })
}
