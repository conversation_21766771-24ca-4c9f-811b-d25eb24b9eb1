package tag

import (
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mysql/livetaggroup"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// tag 启用状态
const (
	StatusHide = iota
	StatusShow
)

// type 标签类型
const (
	TypeLiveTag   = iota // 直播标签
	TypeCustomTag        // 个性词条
)

const (
	// TagNova 新星
	TagNova int64 = 1
	// TagListenDrama 听剧
	TagListenDrama int64 = 24
)

// Tag 标签 与分区类似 主播可以有多个标签
type Tag struct {
	ID           int64  `gorm:"column:id;primary_key" json:"tag_id"`
	CreateTime   int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime int64  `gorm:"column:modified_time" json:"-"`
	TagName      string `gorm:"column:tag_name" json:"tag_name"`
	TagGroupID   int64  `gorm:"column:tag_group_id" json:"tag_group_id,omitempty"`
	Type         int    `gorm:"column:type" json:"type"`
	Status       int    `gorm:"column:status" json:"-"`
	SortOrder    int    `gorm:"column:sort_order" json:"-"`
	IconURL      string `gorm:"column:icon" json:"icon_url"`
	DarkIconURL  string `gorm:"column:dark_icon" json:"dark_icon_url"`
	WebIconURL   string `gorm:"column:web_icon" json:"web_icon_url"`
}

// TableName table name
func (Tag) TableName() string {
	return TableName()
}

// BeforeCreate 添加创建时间
func (tag *Tag) BeforeCreate(scope *gorm.Scope) error {
	return scope.SetColumn("create_time", goutil.TimeNow().Unix())
}

// BeforeSave 添加更新时间
func (tag *Tag) BeforeSave(scope *gorm.Scope) error {
	return scope.SetColumn("modified_time", goutil.TimeNow().Unix())
}

// AfterFind parse url
func (tag *Tag) AfterFind(tx *gorm.DB) (err error) {
	tag.IconURL = storage.ParseSchemeURL(tag.IconURL)
	tag.DarkIconURL = storage.ParseSchemeURL(tag.DarkIconURL)
	tag.WebIconURL = storage.ParseSchemeURL(tag.WebIconURL)
	return
}

// TableName table name
func TableName() string {
	return "live_tag"
}

// FindTags 按照类别查询 tag，根据 sort_order 倒序排序，不使用缓存
func FindTags(all bool, tagType int) ([]*Tag, error) {
	db := service.LiveDB.Where("type = ?", tagType)
	if !all {
		db = db.Where("status = ?", StatusShow)
	}
	var res []*Tag
	err := db.Order("sort_order DESC").Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindTagByID 通过 ID 查找标签
func FindTagByID(tagID int64, tagType int) (*Tag, error) {
	var tag Tag
	err := service.LiveDB.Where("id = ? AND type = ?", tagID, tagType).First(&tag).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &tag, nil
}

// ListByID 通过 ID 列表查找标签
func ListByID(tagIDs []int64, tagType int) ([]*Tag, error) {
	var tags []*Tag
	err := service.LiveDB.Where("id IN (?) AND type = ?", tagIDs, tagType).Find(&tags).Error
	if err != nil {
		return nil, err
	}
	return tags, nil
}

// FindNextTagID 根据分类和组 ID 查询最大 ID
func FindNextTagID(groupID int64, tagType int) (int64, error) {
	var maxID int64
	err := service.LiveDB.Table(TableName()).Select("COALESCE(MAX(id), 0)").
		Where("tag_group_id = ? AND type = ?", groupID, tagType).Row().Scan(&maxID)
	if err != nil {
		return 0, err
	}
	return maxID + 1, nil
}

// ShowLiveTags 所有可见的直播标签
type ShowLiveTags struct {
	TagInfo map[int64]*Tag `json:"tag_info"`
}

// AllShowLiveTags 获取所有可见的直播标签，不包含个性词条
func AllShowLiveTags() (*ShowLiveTags, error) {
	// 使用缓存，查询数据库可见的标签
	key := keys.KeyAllShowTags0.Format()
	v, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}
	if v != "" {
		var showTags ShowLiveTags
		if err = json.Unmarshal([]byte(v), &showTags); err == nil {
			return &showTags, nil
		}
		logger.Error(err)
		// PASS
	}
	db := service.LiveDB.Where("type = ? AND status = ?", TypeLiveTag, StatusShow)
	var res []*Tag
	err = db.Find(&res).Error
	if err != nil {
		return nil, err
	}
	showTagIDs := make(map[int64]*Tag, len(res))
	for _, tag := range res {
		showTagIDs[tag.ID] = tag
	}
	showTags := &ShowLiveTags{showTagIDs}

	b, err := json.Marshal(showTags)
	if err != nil {
		logger.Error(err)
		return showTags, nil
	}
	err = service.LRURedis.Set(key, b, 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return showTags, nil
}

// IncludeTagIDs tagIDs 是否包含在配置里
func (s *ShowLiveTags) IncludeTagIDs(configTagIDs []int64, tagIDs []int64) bool {
	if s.TagInfo == nil {
		// configTagIDs 是从配置中读出来的 tag_ids，因为在写配置时已经判断了 tag 是否存在，所以一般 configTagIDs 都是已经存在的 tag
		// TagInfo 为 nil 时一般用于降级的场景
		return len(util.IntersectionInt64(configTagIDs, tagIDs)) > 0
	}
	showTagIDs := make([]int64, 0, len(configTagIDs))
	for _, tagID := range configTagIDs {
		if _, ok := s.TagInfo[tagID]; ok {
			showTagIDs = append(showTagIDs, tagID)
		}
	}
	intersection := util.IntersectionInt64(showTagIDs, tagIDs)
	return len(intersection) > 0
}

// ClearAllShowTagsCache clear cache of all show tags
func ClearAllShowTagsCache() {
	key := keys.KeyAllShowTags0.Format()
	err := service.LRURedis.Del(key).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// ListAllShowCustomTagGroups 查询生效的个性词条，按照标签组返回
func ListAllShowCustomTagGroups() (map[int64][]*Tag, error) {
	var customTag []*Tag
	err := service.LiveDB.Where("type = ? AND status = ?", TypeCustomTag, StatusShow).
		Order("sort_order DESC").Find(&customTag).Error
	if err != nil {
		return nil, err
	}

	groupMap := make(map[int64][]*Tag)
	for _, v := range customTag {
		groupMap[v.TagGroupID] = append(groupMap[v.TagGroupID], v)
	}

	return groupMap, nil
}

// AllShowCustomTagsMap 获取所有可见的个性词条，map[ID]*Tag
func AllShowCustomTagsMap() (map[int64]*Tag, error) {
	key := keys.KeyAllShowCustomTagsMap0.Format()
	v, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}

	if len(v) != 0 {
		var tagMap map[int64]*Tag
		if err = json.Unmarshal(v, &tagMap); err == nil {
			return tagMap, nil
		}
		logger.Error(err)
		// PASS
	}

	var customTag []*Tag
	err = service.LiveDB.Where("type = ? AND status = ?", StatusShow, TypeCustomTag).
		Find(&customTag).Error
	if err != nil {
		return nil, err
	}

	tagMap := goutil.ToMap(customTag, "ID").(map[int64]*Tag)

	b, err := json.Marshal(tagMap)
	if err != nil {
		logger.Error(err)
		// PASS
		return tagMap, nil
	}

	err = service.LRURedis.Set(key, b, 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return tagMap, nil
}

// CustomTagGroups 个性词条组
type CustomTagGroups struct {
	TagGroupID   int64        `json:"tag_group_id"`
	TagGroupName string       `json:"tag_group_name"`
	Tags         []*CustomTag `json:"tags"`
}

// CustomTag 个性词条
type CustomTag struct {
	TagID   int64  `json:"tag_id"`
	TagName string `json:"tag_name"`
}

// ListAllCustomTagGroups 获取个性词条列表
func ListAllCustomTagGroups() ([]*CustomTagGroups, error) {
	key := keys.KeyAllShowCustomTagGroups0.Format()
	v, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}

	if len(v) != 0 {
		var groups []*CustomTagGroups
		if err = json.Unmarshal(v, &groups); err == nil {
			return groups, nil
		}
		logger.Error(err)
		// PASS
	}

	allTagGroups, err := livetaggroup.FindAllLiveTagGroups()
	if err != nil {
		return nil, err
	}

	customTagMap, err := ListAllShowCustomTagGroups()
	if err != nil {
		return nil, err
	}

	groups := make([]*CustomTagGroups, 0, len(allTagGroups))
	for i := range allTagGroups {
		group := allTagGroups[i]
		customTagList, ok := customTagMap[group.ID]
		if !ok {
			// 如果词条为空，不返回组信息
			continue
		}

		tags := make([]*CustomTag, 0, len(customTagList))
		for _, customTag := range customTagList {
			tags = append(tags, &CustomTag{
				TagID:   customTag.ID,
				TagName: customTag.TagName,
			})
		}

		groups = append(groups, &CustomTagGroups{
			TagGroupID:   group.ID,
			TagGroupName: group.Name,
			Tags:         tags,
		})
	}

	b, err := json.Marshal(groups)
	if err != nil {
		logger.Error(err)
		// PASS
		return groups, nil
	}
	err = service.LRURedis.Set(key, b, 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return groups, nil
}
