package tag

import "github.com/MiaoSiLa/live-service/models/liveim"

// PiaPayload pia broadcast payload
type PiaPayload struct {
	Type   string `json:"type"`
	Event  string `json:"event"`
	RoomID int64  `json:"room_id"`
	By     string `json:"by,omitempty"`
}

// NewPiaPayload pia broadcast payload
func NewPiaPayload(isStart bool, roomID int64, isAdmin bool) *PiaPayload {
	payload := &PiaPayload{
		Type:   liveim.TypePia,
		RoomID: roomID,
	}
	if isStart {
		payload.Event = liveim.EventPiaStart
	} else {
		payload.Event = liveim.EventPiaStop
	}
	if isAdmin {
		payload.By = "admin"
	}
	return payload
}
