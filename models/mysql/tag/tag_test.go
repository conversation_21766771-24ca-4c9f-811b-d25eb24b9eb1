package tag

import (
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	os.Exit(m.Run())
}

func TestFindTags(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tags, err := FindTags(true, TypeLiveTag)
	require.NoError(err)
	require.Len(tags, 4)
	assert.Equal("听剧", tags[0].TagName)
	assert.Equal("test3", tags[1].TagName)
	assert.Equal("test2", tags[2].TagName)
	assert.Equal("test1", tags[3].TagName)

	tags, err = FindTags(false, TypeLiveTag)
	require.NoError(err)
	require.Len(tags, 3)
	assert.Equal("听剧", tags[0].TagName)
	assert.Equal("test2", tags[1].TagName)
	assert.Equal("test1", tags[2].TagName)
	assert.Equal("https://static-test.missevan.com/live/tags/icon/002.png", tags[1].IconURL)
	assert.Equal("https://static-test.missevan.com/live/tags/icon/002-dark.png", tags[1].DarkIconURL)
	assert.Equal("https://static-test.missevan.com/live/tags/icon/002-web.png", tags[1].WebIconURL)

	tags, err = FindTags(false, TypeCustomTag)
	require.NoError(err)
	require.NotEmpty(tags)
	assert.Equal("test10001", tags[0].TagName)
}

func TestFindTagByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试 ID 不存在
	tag, err := FindTagByID(999, TypeLiveTag)
	require.NoError(err)
	assert.Nil(tag)

	// 正常情况
	tag, err = FindTagByID(3, TypeLiveTag)
	require.NoError(err)
	require.NotNil(tag)
	assert.Equal(int64(3), tag.ID)
	assert.Equal("test3", tag.TagName)
}

func TestListByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tags, err := ListByID([]int64{999}, TypeLiveTag)
	require.NoError(err)
	assert.Len(tags, 0)
	tags, err = ListByID([]int64{999, 3, 4}, TypeLiveTag)
	require.NoError(err)
	require.Len(tags, 1)
	assert.Equal(int64(3), tags[0].ID)
	tags, err = ListByID([]int64{1, 2, 3}, TypeLiveTag)
	require.NoError(err)
	require.Len(tags, 3)
	// 查询出来的顺序是按照 ID 升序排列的
	assert.Equal(int64(1), tags[0].ID)
	assert.Equal(int64(2), tags[1].ID)
	assert.Equal(int64(3), tags[2].ID)
}

func TestFindMaxIDByType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	maxID, err := FindNextTagID(0, TypeLiveTag)
	require.NoError(err)
	assert.EqualValues(25, maxID)

	maxID, err = FindNextTagID(0, 3)
	require.NoError(err)
	assert.EqualValues(1, maxID)
}

func TestAllShowTags(t *testing.T) {
	require := require.New(t)

	ClearAllShowTagsCache()

	showTags, err := AllShowLiveTags()
	require.NoError(err)

	showTagsFromCache, err := AllShowLiveTags()
	require.NoError(err)
	require.Equal(len(showTags.TagInfo), len(showTagsFromCache.TagInfo))
	for k := range showTags.TagInfo {
		require.NotNil(showTagsFromCache.TagInfo[k])
	}
}

func TestIncludeTagIDs(t *testing.T) {
	assert := assert.New(t)

	s := &ShowLiveTags{}
	assert.True(s.IncludeTagIDs([]int64{1, 2, 3}, []int64{1}))
	assert.False(s.IncludeTagIDs([]int64{1, 2, 3}, []int64{4}))

	s = &ShowLiveTags{
		TagInfo: map[int64]*Tag{
			1: {},
			2: {},
			3: {},
		},
	}
	assert.False(s.IncludeTagIDs([]int64{4}, []int64{5}))
	assert.False(s.IncludeTagIDs([]int64{3}, []int64{2}))
	assert.True(s.IncludeTagIDs([]int64{2, 3}, []int64{2}))
}

func TestClearAllShowTagsCache(t *testing.T) {
	require := require.New(t)

	showTags := &ShowLiveTags{TagInfo: map[int64]*Tag{1: {ID: 1}}}
	b, err := json.Marshal(showTags)
	require.NoError(err)
	key := keys.KeyAllShowTags0.Format()
	require.NoError(service.LRURedis.Set(key, b, 10*time.Minute).Err())
	ClearAllShowTagsCache()
	_, err = service.LRURedis.Get(key).Result()
	require.True(serviceredis.IsRedisNil(err))
}

func TestListAllShowCustomTagGroups(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	groupMap, err := ListAllShowCustomTagGroups()
	require.NoError(err)
	require.NotEmpty(groupMap)

	group1, ok := groupMap[1]
	require.True(ok)
	require.NotEmpty(group1)
	assert.EqualValues(10001, group1[0].ID)
	assert.EqualValues(10003, group1[1].ID)
	assert.EqualValues(10002, group1[2].ID)

	group2, ok := groupMap[2]
	require.True(ok)
	require.NotEmpty(group1)
	assert.EqualValues(20001, group2[0].ID)
	assert.EqualValues(20002, group2[1].ID)
}

func TestAllShowCustomTagsMap(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key := keys.KeyAllShowCustomTagsMap0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试不从缓存获取
	tagMap, err := AllShowCustomTagsMap()
	require.NoError(err)
	assert.Equal(5, len(tagMap))

	// 测试缓存
	g, err := service.LRURedis.Get(key).Bytes()
	require.NoError(err)
	require.NotEmpty(g)
	err = json.Unmarshal(g, &tagMap)
	require.NoError(err)
	require.Equal(5, len(tagMap))

	// 测试从缓存获取
	tagMap, err = AllShowCustomTagsMap()
	require.NoError(err)
	assert.Equal(5, len(tagMap))
}

func TestListAllCustomTagGroups(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyAllShowCustomTagGroups0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试没有缓存查询
	groups, err := ListAllCustomTagGroups()
	require.NoError(err)
	require.Equal(2, len(groups))
	group := groups[0]
	assert.EqualValues(2, group.TagGroupID)
	tags := group.Tags
	require.Equal(2, len(tags))
	assert.EqualValues(20001, tags[0].TagID)
	assert.EqualValues(20002, tags[1].TagID)

	group = groups[1]
	assert.EqualValues(1, group.TagGroupID)
	tags = group.Tags
	require.Equal(3, len(tags))
	assert.EqualValues(10001, tags[0].TagID)
	assert.EqualValues(10003, tags[1].TagID)

	// 确认缓存是否创建
	g, err := service.LRURedis.Get(key).Bytes()
	require.NoError(err)
	require.NotEmpty(g)
	require.Equal(2, len(groups))

	// 测试缓存查询
	groups2, err := ListAllCustomTagGroups()
	require.NoError(err)
	require.Equal(2, len(groups2))
	assert.Equal(groups, groups2)
}
