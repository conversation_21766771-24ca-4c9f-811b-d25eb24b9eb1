package tag

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/liveim"
)

func TestNewPiaPayload(t *testing.T) {
	assert := assert.New(t)

	payload := NewPiaPayload(true, 1, true)
	assert.Equal(liveim.TypePia, payload.Type)
	assert.Equal(liveim.EventPiaStart, payload.Event)
	assert.Equal("admin", payload.By)
	assert.Equal(int64(1), payload.RoomID)

	payload = NewPiaPayload(false, 1, false)
	assert.Equal(liveim.TypePia, payload.Type)
	assert.Equal(liveim.EventPiaStop, payload.Event)
	assert.Empty(payload.By)
	assert.Equal(int64(1), payload.RoomID)
}
