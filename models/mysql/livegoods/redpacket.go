package livegoods

import (
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 礼物红包类型
const (
	// RedPacketTypeNormal 普通红包
	RedPacketTypeNormal = iota + 1
	// RedPacketTypeKeyword 口令红包
	RedPacketTypeKeyword
)

// MaxCountRedPacketGiftItem 单个礼物红包的最大礼物种类数量
const MaxCountRedPacketGiftItem = 5

// attr 字段用，按位运算，表示第几位
const (
	// AttrBitMaskOnlyCreator 仅限主播使用
	AttrBitMaskOnlyCreator = iota + 1 // 比特位第一位为 1 表示仅限主播使用
	AttrBitMaskSpecialFree            // 比特位第二位为 1 表示特殊红包 - 免费
)

// RedPacket 礼物红包相关信息
type RedPacket struct {
	Type int `json:"type"` // 红包类型。1：普通红包；2：口令红包
	// TODO: 红包二期上线后删除该字段
	Skin          string          `json:"skin"`                      // 红包皮肤压缩包资源地址
	AppearanceID  int64           `json:"appearance_id"`             // 红包皮肤外观 ID
	Attr          goutil.BitMask  `json:"attr,omitempty"`            // 红包属性。比特位第一位为 1 表示仅限主播使用
	Keywords      []string        `json:"keywords,omitempty"`        // 红包口令配置（非口令红包不需要该字段）。e.g. ["默认 1","默认 2"]
	WaitDurations []int64         `json:"wait_durations"`            // 可选等待时间。元素单位：毫秒，为 0 时表示立即开抢
	RoomAllowList []int64         `json:"room_allow_list,omitempty"` // 直播间白名单，若未配置则所有直播间可用
	UserAllowList []int64         `json:"user_allow_list,omitempty"` // 用户白名单，若配置了用户 ID 则仅白名单内用户可用
	CornerIcon    string          `json:"corner_icon,omitempty"`     // 礼物红包角标
	Gifts         []RedPacketGift `json:"gifts"`                     // 礼物红包包含的礼物

	GiftTotalNum   int64 `json:"-"` // 礼物总数
	GiftTotalPrice int64 `json:"-"` // 礼物总价
}

// RedPacketGift 礼物红包礼物
type RedPacketGift struct {
	ID           int64 `json:"id"`                      // 礼物 ID
	Num          int64 `json:"num"`                     // 数量
	MostValuable bool  `json:"most_valuable,omitempty"` // 是否是最有价值礼物
}

// IsMostValuableGift 判断是否为最有价值礼物（可能有多个最有价值礼物）
func (m *RedPacket) IsMostValuableGift(giftID int64) bool {
	for _, gift := range m.Gifts {
		if gift.MostValuable && gift.ID == giftID {
			return true
		}
	}
	return false
}

// CornerIconURL red packet icon
func (m RedPacket) CornerIconURL() string {
	return storage.ParseSchemeURL(m.CornerIcon)
}

// IsValidRedPacket 判断礼物红包配置是否正确
func (more *More) IsValidRedPacket() bool {
	return more != nil && more.RedPacket != nil && len(more.RedPacket.Gifts) != 0
}

// CanUse 判断礼物红包是否可用
func (m *RedPacket) CanUse(userID, roomID, creatorID int64, userOwnedThisSpecialRedPacket bool) bool {
	// 判断礼物红包是否仅限主播使用
	if m.Attr.IsSet(AttrBitMaskOnlyCreator) && userID != creatorID {
		return false
	}
	// 判断直播间是否在礼物红包白名单中
	if m.RoomAllowList != nil && !goutil.HasElem(m.RoomAllowList, roomID) {
		return false
	}
	// 判断用户是否在礼物红包白名单中
	if m.UserAllowList != nil && !goutil.HasElem(m.UserAllowList, userID) {
		return false
	}
	// 判断是否为用户拥有的特殊红包
	if m.IsSpecialRedPacket() && !userOwnedThisSpecialRedPacket {
		return false
	}
	return true
}

// IsSpecialRedPacket 是否特殊红包（目前仅红包商品会被设置为免费）
func (m *RedPacket) IsSpecialRedPacket() bool {
	// 目前特殊红包仅包含免费红包
	return m.Attr.IsSet(AttrBitMaskSpecialFree)
}

// ListValidRedPacketGoods 获得红包商品
func ListValidRedPacketGoods() ([]*GoodsWithMore, error) {
	goods, err := ListLiveGoods(GoodsTypeRedPacket)
	if err != nil {
		return nil, err
	}
	goodsLen := len(goods)
	if goodsLen == 0 {
		return []*GoodsWithMore{}, nil
	}
	redPacketGoods := make([]*GoodsWithMore, 0, len(goods))
	for _, gd := range goods {
		// 检查售卖时间
		if !(gd.IsValidSaleTime() && gd.IsValidShowTime()) {
			continue
		}
		// 检查配置是否异常，异常配置的情况记录错误并跳过
		more, err := gd.UnmarshalMore()
		if err != nil {
			logger.WithField("goods_id", gd.ID).Errorf("礼物红包商品 more 信息解析错误：%v", err)
			continue
		}
		if !more.IsValidRedPacket() {
			logger.WithField("goods_id", gd.ID).Error("礼物红包配置错误")
			continue
		}
		if gd.IsFree() && !more.RedPacket.IsSpecialRedPacket() {
			logger.WithField("goods_id", gd.ID).Error("礼物红包价格配置错误")
			continue
		}
		redPacketGoods = append(redPacketGoods, &GoodsWithMore{
			gd,
			*more,
		})
	}
	return redPacketGoods, nil
}
