package livegoods

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTagKeys(t *testing.T) {
	lg := LiveGoods{}
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(lg, "id", "create_time", "modified_time", "type", "num", "price", "title",
		"description", "icon", "notify_duration", "sort", "more",
		"sale_start_time", "sale_end_time", "start_time", "end_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(lg, "id", "type", "num", "price", "title", "description", "icon", "more",
		"sale_start_time", "sale_end_time", "start_time", "end_time")

	kc.Check(More{}, "limits", "gifts", "appearances", "reward_ids", "order_rewards_info", "pool_id", "energy_type",
		"guaranteed_pool", "gashapon_name", "point_rate", "event_id", "red_packet", "danmaku", "super_fan", "lucky_box")
	kc.Check(Limit{}, "num", "requirement", "type", "noble_type", "noble_level")
	kc.Check(GiftItem{}, "id", "num", "expire_time", "duration", "rate", "lucky")
	kc.Check(AppearanceItem{}, "id", "duration")
	kc.Check(SuperFan{}, "limit_type", "allow_room_ids")
}

func TestListShowingSuperFanAndAllSuperFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := ListShowingSuperFan()
	require.NoError(err)
	assert.NotEmpty(res)

	res2, err := ListShowingSuperFan()
	require.NoError(err)
	assert.ElementsMatch(res, res2)

	allRes, err := AllSuperFan()
	require.NoError(err)
	assert.NotEmpty(res)
	assert.GreaterOrEqual(len(allRes), len(res))
}

func TestAllLuckyBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Create(&LiveGoods{Type: GoodsTypeLuckyBox}).Error
	require.NoError(err)
	service.Cache5Min.Delete(keys.KeyAllGoodsLuckyBoxList0.Format())

	allRes, err := AllLuckyBox()
	require.NoError(err)
	assert.NotEmpty(allRes)

	// test local cache
	allRes, err = AllLuckyBox()
	require.NoError(err)
	assert.NotEmpty(allRes)
}

func TestFindShowingGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := FindShowingGoods(1, GoodsTypeSuperFan)
	require.NoError(err)
	assert.NotNil(res)

	res, err = FindShowingGoods(9999, GoodsTypeSuperFan)
	require.NoError(err)
	assert.Nil(res)
}

func TestLiveListGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyLiveGoodsList1.Format(GoodsTypeGashapon)
	require.NoError(service.LRURedis.Del(key).Err())

	res, err := ListLiveGoods(GoodsTypeGashapon)
	require.NoError(err)
	assert.NotEmpty(res)
	assert.Equal(gashaponOrderTitle(res[0].Num, "超能魔方"), res[0].OrderTitle())

	res2, err := ListLiveGoods(GoodsTypeGashapon)
	require.NoError(err)
	r, err := json.Marshal(res)
	require.NoError(err)
	r2, err := json.Marshal(res2)
	require.NoError(err)
	assert.Equal(r, r2)
}

func TestLiveGoodsGoodsAttrStr(t *testing.T) {
	assert := assert.New(t)

	check := func(goodsType int, attr int, expected string) {
		lg := LiveGoods{Type: goodsType, Attr: attr}
		assert.Equal(expected, lg.GoodsAttrStr())
	}

	check(GoodsTypeSuperFan, AttrSuperFanRegister, "开通")
	check(GoodsTypeSuperFan, AttrSuperFanRenew, "续费")
	check(GoodsTypeFukubukuro, 0, "福袋")
	check(GoodsTypeGashapon, 0, "抽奖")
	check(-1, 0, "")
}

func TestMoreHasNumLimit(t *testing.T) {
	assert := assert.New(t)

	more := new(More)
	hasLimit := more.HasNumLimit()
	assert.False(hasLimit)

	more.Limits = []Limit{{Num: 1, Type: LimitNumDailyUser}}
	hasLimit = more.HasNumLimit()
	assert.True(hasLimit)
}

func TestLiveGoods_IsFree(t *testing.T) {
	assert := assert.New(t)

	liveGoods := LiveGoods{
		Price: 1,
	}
	assert.False(liveGoods.IsFree())

	liveGoods.Price = 0
	assert.True(liveGoods.IsFree())
}

func TestLiveGoods_GashaponEnergyValue(t *testing.T) {
	assert := assert.New(t)

	lg := LiveGoods{
		Num:   10,
		Price: 580,
		More:  `{"energy_type": 1}`,
	}
	assert.Equal(10, lg.GashaponEnergyValue())

	lg.More = `{"energy_type": 2}`
	assert.Equal(58, lg.GashaponEnergyValue())
}

func TestMoreRequirement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	more := new(More)
	requirement := more.Requirement()
	assert.Nil(requirement)

	more.Limits = []Limit{{Requirement: RequireSuperFan}}
	limit := more.Requirement()
	require.NotNil(limit)
	assert.Equal(more.Limits[0].Requirement, limit.Requirement)

	more.Limits = []Limit{
		{Requirement: RequireSuperFan},
		{Requirement: 2},
	}
	limit = more.Requirement()
	require.NotNil(limit)
	assert.Equal(more.Limits[0].Requirement, limit.Requirement)
}

func TestFindShowingFukubukuro(t *testing.T) {
	require := require.New(t)

	now := goutil.TimeNow()
	err := service.LiveDB.Table(TableName()).
		Where("title = ?", "SS 级福袋").
		Update(map[string]interface{}{
			"start_time": now.Add(2 * -time.Minute).Unix(),
			"end_time":   now.Add(-time.Minute).Unix(),
		}).Error
	require.NoError(err)

	lgs, err := FindAllShowingFukubukuro()
	require.NoError(err)
	require.NotEmpty(lgs)

	for _, lg := range lgs {
		lg.IsValidShowTime()
	}
}

func TestCalculateLimitState(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var more More
	more.Limits = []Limit{
		{Num: 1, Type: LimitNumDailyUser},
	}
	mOrderCount := map[int]int64{
		LimitNumDailyUser: 1,
	}
	mLimitState, limitType := more.CalculateLimitState(mOrderCount)
	require.NotZero(limitType)
	require.NotEmpty(mLimitState)
	require.NotNil(mLimitState[limitType])
	assert.Equal(mOrderCount[limitType], mLimitState[limitType].OrderCount)
	assert.Equal(more.Limits[0].Num, mLimitState[limitType].LimitNum)
	assert.Zero(mLimitState[limitType].RemainingNum)

	more.Limits = []Limit{
		{Num: 4, Type: LimitNumDailyUser},
		{Num: 10, Type: LimitNumDailyGlobal},
	}
	mOrderCount = map[int]int64{
		LimitNumDailyUser:   1, // 最终剩余数量为 3
		LimitNumDailyGlobal: 9, // 最终剩余数量为 1
	}
	mLimitState, limitType = more.CalculateLimitState(mOrderCount)
	require.NotZero(limitType)
	require.NotEmpty(mLimitState)
	require.NotNil(mLimitState[limitType])
	assert.Equal(mOrderCount[limitType], mLimitState[limitType].OrderCount)
	assert.Equal(more.Limits[1].Num, mLimitState[limitType].LimitNum)                                      // 全站限制
	assert.Equal(more.Limits[1].Num-mOrderCount[more.Limits[1].Type], mLimitState[limitType].RemainingNum) // 遵循最小值

	more.Limits = []Limit{
		{Num: 10, Type: LimitNumDailyGlobal},
	}
	mOrderCount = make(map[int]int64)
	mLimitState, limitType = more.CalculateLimitState(mOrderCount)
	require.NotZero(limitType)
	require.NotEmpty(mLimitState)
	require.NotNil(mLimitState[limitType])
	assert.EqualValues(10, mLimitState[limitType].RemainingNum)
}

func TestCheckSaleStatus(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	lg := &LiveGoods{
		ID:   1,
		Type: GoodsTypeSuperFan,
	}
	assert.PanicsWithValue(fmt.Sprintf("livegoods id %d is not fukubukuro", lg.ID), func() {
		_, _ = lg.CheckSaleStatus(0, 0, 0)
	})

	// 售卖未开始
	lg = &LiveGoods{
		ID:            2,
		Type:          GoodsTypeFukubukuro,
		SaleStartTime: now.Add(time.Minute).Unix(),
		SaleEndTime:   now.Add(2 * time.Minute).Unix(),
		StartTime:     now.Add(-time.Minute).Unix(),
		EndTime:       now.Add(time.Minute).Unix(),
		More:          `{}`,
	}
	testUserID := int64(12)
	status, err := lg.CheckSaleStatus(testUserID, 0, 0)
	require.NoError(err)
	assert.Equal(SaleNotStarted, status)

	status, err = lg.CheckSaleStatus(0, 0, 0)
	require.NoError(err)
	assert.Equal(SaleNotStarted, status)

	// 正在售卖中
	lg.SaleStartTime = now.Add(-time.Minute).Unix()
	lg.SaleEndTime = now.Add(time.Minute).Unix()
	status, err = lg.CheckSaleStatus(testUserID, 0, 0)
	require.NoError(err)
	assert.Equal(SaleOngoing, status)

	// 售卖已结束
	lg.SaleStartTime = now.Add(2 * -time.Minute).Unix()
	lg.SaleEndTime = now.Add(-time.Minute).Unix()
	status, err = lg.CheckSaleStatus(testUserID, 0, 0)
	require.NoError(err)
	assert.Equal(SaleEnded, status)

	status, err = lg.CheckSaleStatus(0, 0, 0)
	require.NoError(err)
	assert.Equal(SaleEnded, status)

	// 用户未登录，userID 为 0
	lg.SaleStartTime = now.Add(-time.Minute).Unix()
	lg.SaleEndTime = now.Add(time.Minute).Unix()
	status, err = lg.CheckSaleStatus(0, 1, 0)
	require.NoError(err)
	assert.Equal(SaleOngoing, status)

	// 限购次数达到上限
	lg.More = `{"limits": [{"num": 1, "type": 1}]}`
	status, err = lg.CheckSaleStatus(testUserID, 1, 1)
	require.NoError(err)
	assert.Equal(SalePurchaseLimitReached, status)

	// 未达到上限时
	status, err = lg.CheckSaleStatus(testUserID, 0, 1)
	require.NoError(err)
	assert.Equal(SaleOngoing, status)

	// 没有超粉购买资格
	lg.More = `{"limits": [{"requirement": 1}]}`
	status, err = lg.CheckSaleStatus(-1, 0, 0)
	require.NoError(err)
	assert.Equal(SalePurchaseRequirementNotMet, status)

	// 有购买资格
	lg.More = `{"limits": [{"requirement": 1}]}`
	status, err = lg.CheckSaleStatus(testUserID, 0, 0)
	require.NoError(err)
	assert.Equal(SaleOngoing, status)

	// 没有贵族购买资格
	lg.More = `{"limits": [{"requirement": 2, "noble_type": 1, "noble_level": 7}]}`
	status, err = lg.CheckSaleStatus(1, 0, 0)
	require.NoError(err)
	assert.Equal(SalePurchaseRequirementNotMet, status)

	// 限制了购买资格和限购次数
	// 只要资格不足，无视剩余购买次数
	lg.More = `{"limits": [{"requirement": 1, "num": 1, "type": 1}]}`
	status, err = lg.CheckSaleStatus(-1, 0, 0)
	require.NoError(err)
	assert.Equal(SalePurchaseRequirementNotMet, status)

	// 满足资格，但已经达到限购上限
	status, err = lg.CheckSaleStatus(testUserID, 1, 1)
	require.NoError(err)
	assert.Equal(SalePurchaseLimitReached, status)

	// 完全满足
	status, err = lg.CheckSaleStatus(testUserID, 0, 1)
	require.NoError(err)
	assert.Equal(SaleOngoing, status)
}

func TestIsValidSaleTime(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	lg := &LiveGoods{
		ID:            2,
		Type:          GoodsTypeFukubukuro,
		SaleStartTime: now - 5,
		SaleEndTime:   now + 5,
		StartTime:     now - 5,
		EndTime:       now + 5,
		More:          `{}`,
	}
	assert.True(lg.IsValidSaleTime())

	lg.SaleStartTime = now + 5
	assert.False(lg.IsValidSaleTime())

	lg.SaleStartTime = now - 5
	lg.SaleEndTime = now - 5
	assert.False(lg.IsValidSaleTime())
}

func TestIsValidShowTime(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	lg := &LiveGoods{
		ID:            2,
		Type:          GoodsTypeFukubukuro,
		SaleStartTime: now - 5,
		SaleEndTime:   now + 5,
		StartTime:     now - 5,
		EndTime:       now + 5,
		More:          `{}`,
	}
	assert.True(lg.IsValidSaleTime())

	lg.StartTime = now + 5
	assert.False(lg.IsValidShowTime())

	lg.StartTime = now - 5
	lg.EndTime = now - 5
	assert.False(lg.IsValidShowTime())
}

func TestGashaponOrderTitle(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("超能魔方单抽", gashaponOrderTitle(1, "超能魔方"))
	assert.Equal("超能魔方 10 连", gashaponOrderTitle(10, "超能魔方"))
	assert.Equal("超能魔方 100 连", gashaponOrderTitle(100, "超能魔方"))
}

func TestGoodsTotalPrice(t *testing.T) {
	assert := assert.New(t)

	lg := LiveGoods{
		Type:  GoodsTypeFukubukuro,
		Price: 1,
	}
	assert.Equal(1, lg.GoodsTotalPrice())
}

func TestListByIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试没有数据时
	goodsIDs := []int64{1000, 1001}
	list, err := ListByIDs(goodsIDs, GoodsTypeRedPacket)
	require.NoError(err)
	assert.Empty(list)

	// 测试有数据时
	goodsIDs = []int64{17, 18}
	list, err = ListByIDs(goodsIDs, GoodsTypeRedPacket)
	require.NoError(err)
	assert.Len(list, 2)
}

func TestFindShowingDanmaku(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	g, err := FindShowingDanmaku()
	require.NoError(err)
	assert.NotNil(g)
}

func TestLuckyBox_SkinURL(t *testing.T) {
	assert := assert.New(t)

	lb := LuckyBox{
		Skin: "oss://test.png",
	}
	res := lb.SkinURL()
	assert.NotEmpty(res)
}
