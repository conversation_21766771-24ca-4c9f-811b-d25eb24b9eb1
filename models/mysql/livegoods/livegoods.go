package livegoods

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Goods 商品
type Goods interface {
	GoodsID() int64
	OrderTitle() string
	GoodsSellerID() int64

	GoodsType() int
	GoodsAttr() int

	GoodsNum() int
	GoodsTotalPrice() int
}

// 购买资格对象
const (
	RequireSuperFan = iota + 1 // 超粉才能购买
	RequireNoble               // 贵族才能购买
)

// 限购次数类型
const (
	LimitNumDailyUser   = iota + 1 // 用户每日限购
	LimitNumDailyGlobal            // 全站每日限购
	LimitTypeUser                  // 用户限购
)

// 礼包奖励订单笔数类型
const (
	OrderCountUser   = iota + 1 // 用户订单笔数
	OrderCountGlobal            // 全站每日订单笔数
)

// 商品类型
const (
	GoodsTypeSuperFan   = iota + 1 // 超粉
	GoodsTypeFukubukuro            // 福袋
	GoodsTypeGashapon              // 超能魔方
	GoodsTypeWish                  // 星座许愿
	GoodsTypeRedPacket             // 礼物红包
	GoodsTypeDanmaku               // 付费弹幕
	GoodsTypeLuckyBox              // 宝盒
)

// 商品属性
const (
	AttrSuperFanRegister = iota + 1 // 超粉开通
	AttrSuperFanRenew               // 超粉续费
)

// 商品售卖状态
const (
	SaleNotStarted                = iota // 未开始售卖
	SaleOngoing                          // 正常售卖中
	SaleEnded                            // 售卖已结束
	SalePurchaseLimitReached             // 购买次数达到上限
	SalePurchaseRequirementNotMet        // 没有购买资格
)

// LiveGoods model for table missevan_live.live_goods
type LiveGoods struct {
	ID           int64 `gorm:"column:id;primary_key" json:"id"`
	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`

	Type           int    `gorm:"column:type" json:"type"`
	Num            int    `gorm:"column:num" json:"num"`
	Price          int    `gorm:"column:price" json:"price"`
	Title          string `gorm:"column:title" json:"title"`
	Description    string `gorm:"column:description" json:"description,omitempty"`
	Icon           string `gorm:"column:icon" json:"icon,omitempty"`
	NotifyDuration int64  `gorm:"column:notify_duration" json:"-"` // 毫秒
	Sort           int64  `gorm:"column:sort" json:"-"`
	More           string `gorm:"column:more" json:"more,omitempty"` // 缓存需要 json tag

	// 售卖开始时间与结束时间
	SaleStartTime int64 `gorm:"column:sale_start_time" json:"sale_start_time"`
	SaleEndTime   int64 `gorm:"column:sale_end_time" json:"sale_end_time"`
	// 展示时间与过期时间
	StartTime int64 `gorm:"column:start_time" json:"start_time"`
	EndTime   int64 `gorm:"column:end_time" json:"end_time"`

	SellerID      int64  `gorm:"-" json:"-"` // TODO: 该字段目前是手动赋值的，后续需要修改
	Attr          int    `gorm:"-" json:"-"`
	TxnOrderTitle string `gorm:"-" json:"-"`
}

// GoodsWithMore current model with more
type GoodsWithMore struct {
	LiveGoods
	MoreInfo More
}

// Limit 限购配置
type Limit struct {
	Num         int64 `json:"num"`                   // 限购次数
	Type        int   `json:"type,omitempty"`        // 限购次数类型，区分全站数量限购、用户数量限购
	Requirement int   `json:"requirement"`           // 购买资格对象，比如 1 超粉，2 贵族
	NobleType   int   `json:"noble_type,omitempty"`  // 购买用户的贵族类型限制
	NobleLevel  int   `json:"noble_level,omitempty"` // 购买用户的贵族等级限制
}

// GiftItem 商品附加的礼物
type GiftItem struct {
	ID         int64 `json:"id"`
	Num        int64 `json:"num"`
	ExpireTime int64 `json:"expire_time,omitempty"` // 过期时间，和持续时间二选一
	Duration   int64 `json:"duration,omitempty"`    // 持续时间（单位：毫秒），和过期时间二选一
	Rate       int   `json:"rate"`                  // 中奖概率
	Lucky      int   `json:"lucky,omitempty"`       // 幸运等级
}

// AppearanceItem 商品附加的外观
type AppearanceItem struct {
	ID       int64 `json:"id"`
	Duration int64 `json:"duration"`
}

// EnergyType 能量值类型
const (
	EnergyTypeBuyNum = iota + 1 // 按照购买数量添加能量值
	EnergyTypePrice             // 按照花费实际金额 (元)
)

// More 商品的附加内容
type More struct {
	Limits           []Limit           `json:"limits,omitempty"`
	Gifts            []GiftItem        `json:"gifts,omitempty"`
	Appearances      []AppearanceItem  `json:"appearances,omitempty"`
	RewardIDs        []int64           `json:"reward_ids,omitempty"`
	OrderRewardsInfo *OrderRewardsInfo `json:"order_rewards_info,omitempty"` // 福袋（礼包）奖励订单信息

	PoolID         int64         `json:"pool_id,omitempty"`
	EnergyType     int           `json:"energy_type,omitempty"`     // 超能魔方能量值类型
	GuaranteedPool map[int64]int `json:"guaranteed_pool,omitempty"` // 超能魔方保底奖池
	GashaponName   string        `json:"gashapon_name,omitempty"`   // 超能魔方玩法名称

	// 许愿池相关信息
	// Limits[0].Num 为每个周期最大抽奖次数
	// Gifts[0] 为大奖抽中的奖品，Gifts[1] 为小奖抽中的奖品
	// PointRate 和 Gifts[].Rate 一起计算抽奖结果
	PointRate int   `json:"point_rate,omitempty"` // 单次抽中积分的概率（概率大）
	EventID   int64 `json:"event_id,omitempty"`   // 对应的活动 ID

	RedPacket *RedPacket `json:"red_packet,omitempty"` // 礼物红包配置信息

	Danmaku *Danmaku `json:"danmaku,omitempty"` // 付费弹幕配置信息

	SuperFan *SuperFan `json:"super_fan,omitempty"` // 超粉配置信息

	LuckyBox *LuckyBox `json:"lucky_box,omitempty"` // 宝盒配置信息
}

// OrderRewardsInfo 礼包订单奖励配置
type OrderRewardsInfo struct {
	OrderRewards []OrderRewardItem `json:"order_rewards"`
	Type         int               `json:"type"` // 订单笔数类型
}

// OrderRewardItem 订单笔数与奖励 ID
// 订单笔数在 OrderCounts 中时可获得 RewardIDs 中全部奖励
type OrderRewardItem struct {
	OrderCounts []int64 `json:"order_counts,omitempty"`
	RewardIDs   []int64 `json:"reward_ids"`
}

// Danmaku 付费弹幕配置
type Danmaku struct {
	Tip     string           `json:"tip"`
	Effects []*DanmakuEffect `json:"effects,omitempty"`
}

// DanmakuEffect 付费弹幕特效
type DanmakuEffect struct {
	Type    int    `json:"type"`
	IconURL string `json:"icon_url"`
}

// 超粉限制类型
const (
	SuperFanLimitTypeNot     = iota
	SuperFanLimitTypeFirst   // 全站范围首次开通超粉
	SuperFanLimitTypeOpen    // 直播间开通
	SuperFanLimitTypeRenewal // 直播间续费
	SuperFanLimitTypeBuy     // 直播间开通和续费
)

// SuperFan 超粉配置信息
type SuperFan struct {
	LimitType    int     `json:"limit_type"`
	AllowRoomIDs []int64 `json:"allow_room_ids"`
}

// LuckyBox 宝盒配置信息
type LuckyBox struct {
	GiftPoolID     int64  `json:"gift_pool_id"`     // 礼物池 ID
	Skin           string `json:"skin"`             // 皮肤资源压缩包地址
	DrawNumOptions []int  `json:"draw_num_options"` // 抽盒次数选项
	Num            int    `json:"num"`              // 宝盒数量
	ResultMsg      string `json:"result_msg"`       // 抽盒结果提示
}

// TableName for live_goods
func TableName() string {
	return "live_goods"
}

// TableName for current model
func (LiveGoods) TableName() string {
	return TableName()
}

// DB the db instance of this model
func (LiveGoods) DB() *gorm.DB {
	return service.LiveDB.Table(TableName())
}

// GoodsID goods id
func (lg LiveGoods) GoodsID() int64 {
	return lg.ID
}

// GoodsSellerID goods seller_id
func (lg LiveGoods) GoodsSellerID() int64 {
	return lg.SellerID
}

// GoodsType goods type
func (lg LiveGoods) GoodsType() int {
	return lg.Type
}

// GoodsNum goods num
func (lg LiveGoods) GoodsNum() int {
	return lg.Num
}

// GoodsTotalPrice goods total price
func (lg LiveGoods) GoodsTotalPrice() int {
	return lg.Price
}

// GoodsIconURL goods icon
func (lg LiveGoods) GoodsIconURL() string {
	return storage.ParseSchemeURL(lg.Icon)
}

// GoodsNotifyDuration goods notify duration
func (lg LiveGoods) GoodsNotifyDuration() int64 {
	return lg.NotifyDuration
}

// OrderTitle order title
func (lg LiveGoods) OrderTitle() string {
	switch lg.Type {
	case GoodsTypeGashapon:
		// 理论上正常配置不会返回空
		moreInfo, err := lg.UnmarshalMore()
		if err != nil {
			logger.Error(err)
			return ""
		}
		if moreInfo == nil {
			return ""
		}
		return gashaponOrderTitle(lg.Num, moreInfo.GashaponName)
	}
	return lg.TxnOrderTitle
}

// GoodsAttr goods attr
func (lg LiveGoods) GoodsAttr() int {
	return lg.Attr
}

// GoodsAttrStr goods attr
func (lg LiveGoods) GoodsAttrStr() string {
	switch lg.GoodsType() {
	case GoodsTypeSuperFan:
		switch lg.Attr {
		case AttrSuperFanRegister:
			return "开通"
		case AttrSuperFanRenew:
			return "续费"
		}
	case GoodsTypeFukubukuro:
		return "福袋"
	case GoodsTypeGashapon:
		return "抽奖"
	}
	return ""
}

// SkinURL 获取宝盒封面图链接
func (lb LuckyBox) SkinURL() string {
	return storage.ParseSchemeURL(lb.Skin)
}

// HasNumLimit 是否有限制购买次数
func (more More) HasNumLimit() bool {
	if len(more.Limits) == 0 {
		return false
	}

	var numSet bool
	for _, v := range more.Limits {
		if v.Num > 0 {
			numSet = true
			break
		}
	}

	return numSet
}

// HasOrderRewards 是否有订单笔数奖励
func (more More) HasOrderRewards() bool {
	return more.OrderRewardsInfo != nil && len(more.OrderRewardsInfo.OrderRewards) != 0
}

// IsFree 是否免费（目前仅红包商品会被设置为免费）
func (lg LiveGoods) IsFree() bool {
	return lg.Price == 0
}

// GashaponEnergyValue 魔盒能量值
func (lg LiveGoods) GashaponEnergyValue() int {
	more, err := lg.UnmarshalMore()
	if err != nil {
		return 0
	}
	if more == nil {
		return 0
	}
	switch more.EnergyType {
	case EnergyTypeBuyNum:
		return lg.Num
	case EnergyTypePrice:
		// 钻石需要转换为元，忽略小数问题
		return lg.Price / 10
	default:
		panic(fmt.Sprintf("unknown energy type: %d", more.EnergyType))
	}
}

// LimitState 限购状态
type LimitState struct {
	OrderCount   int64
	LimitNum     int64
	RemainingNum int64
}

// CalculateLimitState 计算限购状态
func (more More) CalculateLimitState(mOrderCount map[int]int64) (map[int]*LimitState, int) {
	foundRemaining := int64(-1)
	foundLimitType := int(0)
	mLimitState := make(map[int]*LimitState, len(more.Limits))
	for _, v := range more.Limits {
		if v.Requirement > 0 && v.Num <= 0 {
			// 非购买数量限制
			continue
		}
		limitType := v.Type
		orderCount := mOrderCount[limitType]
		remaining := v.Num - orderCount
		if remaining <= 0 {
			remaining = 0
		}

		mLimitState[limitType] = &LimitState{OrderCount: orderCount, LimitNum: v.Num, RemainingNum: remaining}
		if foundRemaining == -1 {
			foundRemaining = remaining
			foundLimitType = limitType
		}
		if remaining < foundRemaining {
			foundRemaining = remaining
			foundLimitType = limitType
		}
	}

	return mLimitState, foundLimitType
}

// Requirement 获取限购的对象
// NOTICE: 目前仅支持配置超粉/贵族一种用户身份限制
func (more More) Requirement() *Limit {
	for i := range more.Limits {
		// 使用 Requirement 字段区分商品售卖数量限制和用户身份限制
		if more.Limits[i].Requirement > 0 {
			return &more.Limits[i]
		}
	}

	return nil
}

// ListShowingSuperFan 获取售卖中的超粉信息列表
func ListShowingSuperFan() ([]*LiveGoods, error) {
	nowUnix := goutil.TimeNow().Unix()
	var showingList []*LiveGoods
	err := service.LiveDB.Where("type = ? AND sort > 0", GoodsTypeSuperFan).
		Where("start_time <= ?", nowUnix).
		Where("end_time = 0 OR end_time > ?", nowUnix).
		Order("sort ASC").
		Find(&showingList).Error
	if err != nil {
		return nil, err
	}
	return showingList, nil
}

// AllSuperFan 获取所有超粉信息（包括已下架）
// 有本地 5 分钟缓存
func AllSuperFan() ([]LiveGoods, error) {
	key := keys.KeyAllGoodsSuperFanList0.Format()
	cache := service.Cache5Min

	v, ok := cache.Get(key)
	if ok {
		return copyGoodsSlice(v.([]LiveGoods)), nil
	}
	var res []LiveGoods
	err := service.LiveDB.Where("type = ?", GoodsTypeSuperFan).Find(&res).Error
	if err != nil {
		return nil, err
	}
	cache.Set(key, res, 0)
	return copyGoodsSlice(res), nil
}

// AllLuckyBox 获取所有宝盒信息（包括已下架）
// 有本地 5 分钟缓存
func AllLuckyBox() ([]LiveGoods, error) {
	key := keys.KeyAllGoodsLuckyBoxList0.Format()
	cache := service.Cache5Min

	v, ok := cache.Get(key)
	if ok {
		return copyGoodsSlice(v.([]LiveGoods)), nil
	}
	var res []LiveGoods
	err := service.LiveDB.Where("type = ?", GoodsTypeLuckyBox).Find(&res).Error
	if err != nil {
		return nil, err
	}
	cache.Set(key, res, 0)
	return copyGoodsSlice(res), nil
}

// ListLiveGoods 获取直播商品
// 有 10 分钟缓存
func ListLiveGoods(goodsType int) ([]LiveGoods, error) {
	key := keys.KeyLiveGoodsList1.Format(goodsType)
	v, err := service.LRURedis.Get(key).Result()
	var res []LiveGoods
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	} else if v != "" {
		if err = json.Unmarshal([]byte(v), &res); err == nil {
			return res, nil
		}
		logger.Error(err)
		// PASS
	}
	err = service.LiveDB.Where("sort > 0 AND type = ?", goodsType).
		Order("sort ASC").Find(&res).Error
	if err != nil {
		return nil, err
	}
	str, err := json.Marshal(&res)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = service.LRURedis.Set(key, string(str), 10*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return res, nil
}

func copyGoodsSlice(org []LiveGoods) []LiveGoods {
	dst := make([]LiveGoods, len(org))
	copy(dst, org)
	return dst
}

// FindShowingGoods 查询上架中的商品
func FindShowingGoods(id int64, goodsType int) (*LiveGoods, error) {
	var g LiveGoods
	err := service.LiveDB.Where("id = ? AND type = ? AND sort > 0", id, goodsType).Take(&g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &g, nil
}

// Find 查询商品
func Find(id int64, goodsType int) (*LiveGoods, error) {
	var g LiveGoods
	err := service.LiveDB.Where("id = ? AND type = ?", id, goodsType).First(&g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &g, nil
}

// FindByIDs 查找指定 ID 所有商品
func FindByIDs(ids []int64) ([]*LiveGoods, error) {
	var gs []*LiveGoods
	err := service.LiveDB.Where("id IN (?)", ids).Find(&gs).Error
	if err != nil {
		return nil, err
	}
	return gs, nil
}

// ListByIDs 根据商品 ID 数组查询商品
func ListByIDs(goodsIDs []int64, goodsType int) ([]LiveGoods, error) {
	var res []LiveGoods
	err := service.LiveDB.
		Where("id IN (?) AND type = ?", goodsIDs, goodsType).
		Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

// FindAllShowingFukubukuro 查询正在展示的福袋
func FindAllShowingFukubukuro() ([]*LiveGoods, error) {
	now := goutil.TimeNow().Unix()
	g := make([]*LiveGoods, 0)
	err := service.LiveDB.Where("type = ? AND start_time <= ? AND end_time > ?",
		GoodsTypeFukubukuro, now, now).Find(&g).Error
	if err != nil {
		return nil, err
	}
	return g, nil
}

// CheckSaleStatus 检查福袋的售卖状态
// NOTICE: 由于 livetxnorder 已经引入 livegoods，无法在该函数内查询历史订单数量
func (lg *LiveGoods) CheckSaleStatus(userID, historyOrderNum int64, limitType int) (int, error) {
	if lg.Type != GoodsTypeFukubukuro {
		panic(fmt.Sprintf("livegoods id %d is not fukubukuro", lg.ID))
	}

	now := goutil.TimeNow()
	// 未开始售卖
	if lg.SaleStartTime >= now.Unix() {
		return SaleNotStarted, nil
	}
	// 已结束售卖
	if lg.SaleEndTime < now.Unix() {
		return SaleEnded, nil
	}
	// 如果 userID 是 0（即未登录），并且此时可以购买，直接返回正在售卖
	if userID == 0 {
		return SaleOngoing, nil
	}

	more, err := lg.UnmarshalMore()
	if err != nil {
		return 0, err
	}
	// 用户身份购买资格判断
	requirementLimit := more.Requirement()
	if requirementLimit != nil {
		switch requirementLimit.Requirement {
		case RequireSuperFan:
			if !livemedal.HasActiveSuperFan(userID) {
				return SalePurchaseRequirementNotMet, nil
			}
		case RequireNoble:
			uv, err := vip.UserActivatedVip(userID, false, nil)
			if err != nil {
				return 0, err
			}
			if uv == nil {
				return SalePurchaseRequirementNotMet, nil
			}
			if !vip.IsVipGte(uv, requirementLimit.NobleType, requirementLimit.NobleLevel) {
				return SalePurchaseRequirementNotMet, nil
			}
		}
	}

	// 达到限购次数上限
	if more.HasNumLimit() {
		for _, v := range more.Limits {
			if v.Type == limitType {
				if (v.Num - historyOrderNum) <= 0 {
					return SalePurchaseLimitReached, nil
				}
				break
			}
		}
	}

	return SaleOngoing, nil
}

// UnmarshalMore 解析 More 的 JSON 结构
func (lg LiveGoods) UnmarshalMore() (*More, error) {
	if lg.More == "" {
		return nil, nil
	}
	var more More
	err := json.Unmarshal([]byte(lg.More), &more)
	if err != nil {
		return nil, err
	}
	return &more, nil
}

// IsValidSaleTime 是否是有效的售卖时间
func (lg LiveGoods) IsValidSaleTime() bool {
	now := goutil.TimeNow().Unix()
	return lg.SaleStartTime <= now && lg.SaleEndTime > now
}

// IsValidShowTime 是否是有效的展示时间
func (lg LiveGoods) IsValidShowTime() bool {
	now := goutil.TimeNow().Unix()
	return lg.StartTime <= now && (lg.EndTime == 0 || lg.EndTime > now)
}

func gashaponOrderTitle(num int, name string) string {
	if num == 1 {
		return fmt.Sprintf("%s单抽", name)
	}
	return fmt.Sprintf("%s %d 连", name, num)
}

// FindShowingDanmaku 查询当前可以购买的付费弹幕
func FindShowingDanmaku() (*LiveGoods, error) {
	now := goutil.TimeNow().Unix()
	var g LiveGoods
	err := service.LiveDB.
		Where("type = ? AND sort > 0", GoodsTypeDanmaku).
		Where("start_time <= ?", now).
		Where("end_time = 0 OR end_time > ?", now).
		Order("start_time DESC"). // 优先展示最新的付费弹幕
		Take(&g).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &g, nil
}
