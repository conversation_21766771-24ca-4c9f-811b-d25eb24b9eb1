package livegoods

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRedPacketTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(RedPacket{}, "type", "skin", "appearance_id", "attr", "keywords", "wait_durations", "room_allow_list", "user_allow_list", "corner_icon", "gifts")
	kc.CheckOmitEmpty(RedPacket{}, "attr", "keywords", "room_allow_list", "user_allow_list", "corner_icon")
	kc.Check(RedPacketGift{}, "id", "num", "most_valuable")
	kc.CheckOmitEmpty(RedPacketGift{}, "most_valuable")
}

func TestRedPacket_IsMostValuableGift(t *testing.T) {
	assert := assert.New(t)

	rp := RedPacket{
		Gifts: []RedPacketGift{
			{
				ID:           1,
				Num:          1,
				MostValuable: false,
			},
			{
				ID:           2,
				Num:          1,
				MostValuable: true,
			},
		},
	}
	assert.False(rp.IsMostValuableGift(1))
	assert.True(rp.IsMostValuableGift(2))
	assert.False(rp.IsMostValuableGift(3))
}

func TestMore_IsValidRedPacket(t *testing.T) {
	assert := assert.New(t)

	more := &More{}
	// 测试 more.RedPacket 为 nil
	assert.False(more.IsValidRedPacket())

	more.RedPacket = &RedPacket{
		Gifts: []RedPacketGift{
			{
				ID:           1,
				Num:          1,
				MostValuable: false,
			},
			{
				ID:           2,
				Num:          1,
				MostValuable: true,
			},
		},
	}

	// 测试 more.RedPacket.Gifts 不是 nil
	assert.True(more.IsValidRedPacket())
}

func TestRedPacket_CanUse(t *testing.T) {
	assert := assert.New(t)

	// 测试礼物红包仅限主播使用
	rp := &RedPacket{Attr: AttrBitMaskOnlyCreator}
	assert.False(rp.CanUse(12, 3, 13, false))
	assert.True(rp.CanUse(12, 3, 12, false))

	// 测试房间不在白名单内
	rp.RoomAllowList = []int64{1, 2}
	assert.False(rp.CanUse(12, 3, 12, false))

	// 测试房间在白名单内
	assert.True(rp.CanUse(12, 1, 12, false))

	// 测试用户不在白名单内
	rp.UserAllowList = []int64{12, 13}
	assert.False(rp.CanUse(11, 1, 11, false))

	// 测试用户在白名单内
	assert.True(rp.CanUse(12, 1, 12, false))

	// 测试不为用户的特殊红包
	rp.UserAllowList = nil
	rp.Attr.Set(AttrBitMaskSpecialFree)
	assert.False(rp.CanUse(12, 1, 12, false))

	// 测试为用户的特殊红包
	assert.True(rp.CanUse(12, 1, 12, true))
}

func TestListValidRedPacketGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(LiveGoods{}.DB().Delete("", "type = ?", GoodsTypeRedPacket).Error)
	key := keys.KeyLiveGoodsList1.Format(GoodsTypeRedPacket)
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试不在售卖时间内的情况
	testGoodsID := int64(456465)
	now := goutil.TimeNow().Unix()
	more := `{"red_packet":{"type":1,"skin":"oss://test/test.zip","appearance_id":80001,"attr":0,"wait_durations":[0,45000]]}}`
	require.NoError(createRedPacketGoods(testGoodsID, 0, now, 1, more))
	goods, err := ListValidRedPacketGoods()
	require.NoError(err)
	assert.Empty(goods)

	// 测试红包配置错误的情况
	endTime := now + 60
	more = "error more"
	require.NoError(createRedPacketGoods(testGoodsID, 0, endTime, 1, more))
	require.NoError(service.LRURedis.Del(key).Err())
	goods, err = ListValidRedPacketGoods()
	require.NoError(err)
	assert.Empty(goods)

	// 测试红包未配置礼物的情况
	more = `{"red_packet":{"type":1,"skin":"oss://test/test.zip","appearance_id":80001,"attr":0,"wait_durations":[0,45000]]}}`
	require.NoError(createRedPacketGoods(testGoodsID, 0, endTime, 1, more))
	require.NoError(service.LRURedis.Del(key).Err())
	goods, err = ListValidRedPacketGoods()
	require.NoError(err)
	assert.Empty(goods)

	// 测试付费红包可用的情况
	more = `{"red_packet":{"type":1,"skin":"oss://test/test.zip","appearance_id":80001,"attr":0,"wait_durations":[0,45000],"gifts":[{"id":40089,"num":17}]}}`
	require.NoError(createRedPacketGoods(testGoodsID, 0, endTime, 1, more))
	require.NoError(service.LRURedis.Del(key).Err())
	goods, err = ListValidRedPacketGoods()
	require.NoError(err)
	require.Len(goods, 1)
	assert.Equal(testGoodsID, goods[0].ID)

	// 测试价格配置错误的情况
	require.NoError(createRedPacketGoods(testGoodsID, 0, endTime, 0, more))
	require.NoError(service.LRURedis.Del(key).Err())
	goods, err = ListValidRedPacketGoods()
	require.NoError(err)
	assert.Empty(goods)

	// 测试免费红包可用的情况
	more = `{"red_packet":{"type":1,"skin":"oss://test/test.zip","appearance_id":80001,"attr":2,"wait_durations":[0,45000],"gifts":[{"id":40089,"num":17}]}}`
	require.NoError(createRedPacketGoods(testGoodsID, 0, endTime, 0, more))
	require.NoError(service.LRURedis.Del(key).Err())
	goods, err = ListValidRedPacketGoods()
	require.NotEmpty(goods)
	require.NoError(err)
	require.Len(goods, 1)
	assert.Equal(testGoodsID, goods[0].ID)
}

func createRedPacketGoods(goodsID, startTime, endTime int64, price int, more string) error {
	err := LiveGoods{}.DB().Delete("", "id = ?", goodsID).Error
	if err != nil {
		return err
	}
	gd := &LiveGoods{
		ID:            goodsID,
		Type:          GoodsTypeRedPacket,
		Price:         price,
		Sort:          1,
		SaleStartTime: startTime,
		SaleEndTime:   endTime,
	}
	gd.More = more
	return gd.DB().Create(gd).Error
}
