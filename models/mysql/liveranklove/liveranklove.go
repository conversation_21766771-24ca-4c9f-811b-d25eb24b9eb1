package liveranklove

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/msound"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Model of "live_rank_love"
type Model struct {
	ID int `gorm:"column:id" json:"-"`

	UserID    int64  `gorm:"column:user_id" json:"user_id"`
	Name      string `gorm:"-" json:"name"`
	IconURL   string `gorm:"-" json:"icon_url"`
	RoomID    int64  `gorm:"column:room_id" json:"room_id"`
	Character string `gorm:"column:character" json:"character"`
	Point     int    `gorm:"column:point" json:"point"`
	SoundID   int64  `gorm:"column:sound_id" json:"sound_id"`
	SoundURL  string `gorm:"-" json:"soundurl"`
	Duration  int64  `gorm:"-" json:"duration"`

	Month   int `gorm:"column:month" json:"-"`
	Checked int `gorm:"column:checked" json:"checked"`

	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`

	Status struct {
		Open int `json:"open"`
	} `json:"status"`
}

// TableName of model "live_rank_love"
func (Model) TableName() string {
	return tableName
}

const tableName = "live_rank_love"

// BeforeCreate automatically sets columns create_time and modified_time
func (m *Model) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically updates the column modified_time
func (m *Model) BeforeUpdate() error {
	m.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// GetLatest 返回当前月之前的最新发布的心动主播榜
func GetLatest() ([]Model, error) {
	m := make([]Model, 0)

	month := util.YearMonthInt(goutil.TimeNow())
	// TODO: ORDER BY month DESC LIMIT 1
	subQuery := service.DB.Table(tableName).Select("MAX(month)").Where("checked = 1 AND month < ?", month).SubQuery()
	err := service.DB.Where("month = ?", subQuery).Where("checked = 1").Order("point DESC").Find(&m).Error
	if err != nil {
		return m, err
	}
	err = AddUserInfo(m)
	if err != nil {
		return nil, err
	}
	err = AddSoundInfo(m)
	if err != nil {
		return nil, err
	}
	return m, nil
}

// Publish 发布 month 年月的数据
func Publish(month int) (bool, error) {
	db := service.DB.Table(tableName).Where("month = ?", month).Where("checked = 0").Update("checked", 1)
	if err := db.Error; err != nil {
		return false, err
	}
	if db.RowsAffected == 0 {
		return false, nil
	}
	return true, nil
}

// Replace 替换某月的心动主播配置
func Replace(tx *gorm.DB, month int, list []Model) error {
	err := tx.Exec("DELETE FROM "+tableName+" WHERE month = ?", month).Error
	if err != nil {
		return err
	}

	for i := range list {
		list[i].Month = month
		err := tx.Create(&list[i]).Error
		if err != nil {
			return err
		}
	}
	return nil
}

// Get 获取指定月份的数据
func Get(month int) ([]Model, error) {
	m := []Model{}

	err := service.DB.Where("month = ?", month).Order("point DESC").Find(&m).Error
	if err != nil {
		return m, err
	}
	err = AddUserInfo(m)
	if err != nil {
		return nil, err
	}
	err = AddSoundInfo(m)
	if err != nil {
		return nil, err
	}
	return m, nil
}

// AddUserInfo 为 list 添加用户头像，昵称信息
func AddUserInfo(list []Model) error {
	if len(list) == 0 {
		return nil
	}
	userIDs := make([]int64, len(list))
	for i, m := range list {
		userIDs[i] = m.UserID
	}

	users, err := mowangskuser.FindSimpleList(userIDs)
	if err != nil {
		return err
	}

	for i, m := range list {
		for _, u := range users {
			if m.UserID == u.ID {
				list[i].Name = u.Username
				list[i].IconURL = u.IconURL
				break
			}
		}
	}
	return nil
}

// AddSoundInfo 为 list 添加音频播放地址
func AddSoundInfo(list []Model) error {
	if len(list) == 0 {
		return nil
	}
	soundIDs := make([]int64, len(list))
	for i, m := range list {
		soundIDs[i] = m.SoundID
	}

	sounds, err := msound.FindSimpleList(soundIDs)
	if err != nil {
		return err
	}

	for i, m := range list {
		for _, s := range sounds {
			if m.SoundID == s.ID {
				list[i].SoundURL = s.SoundURL
				list[i].Duration = s.Duration
				break
			}
		}
	}
	return nil
}
