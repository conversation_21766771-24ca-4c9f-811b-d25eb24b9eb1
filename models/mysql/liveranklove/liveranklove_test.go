package liveranklove

import (
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testSoundID = int64(44809)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestModel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	month, _ := strconv.Atoi(time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, time.Local).Format("200601"))

	err := Replace(service.DB, month, []Model{{
		UserID:  10,
		SoundID: testSoundID,
		Point:   2,
	}, {
		UserID:  10,
		SoundID: testSoundID,
		Point:   3,
	}})
	require.NoError(err)

	testHelper := func(list []Model) {
		require.Len(list, 2)
		assert.Greater(list[0].Point, list[1].Point)
		assert.Equal(list[0].Month, month)
		assert.Equal(list[1].Month, month)
		assert.Equal(1, strings.Count(list[0].IconURL, "http"))
		assert.Equal(1, strings.Count(list[0].SoundURL, "http"))
		assert.Equal(1, strings.Count(list[1].IconURL, "http"))
		assert.Equal(1, strings.Count(list[1].SoundURL, "http"))
	}

	time.Sleep(time.Millisecond * 800)

	list, err := GetLatest()
	require.NoError(err)
	if len(list) > 0 {
		assert.Greater(month, list[0].Month)
	}

	list, err = Get(month)
	require.NoError(err)
	testHelper(list)

	ok, err := Publish(month)
	require.NoError(err)
	assert.True(ok)

	list, err = GetLatest()
	require.NoError(err)
	testHelper(list)

	list2, err := Get(month)
	require.NoError(err)
	assert.ElementsMatch(list, list2)

	ok, err = Publish(month)
	require.NoError(err)
	assert.False(ok)
}

func TestAddUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list := []Model{
		{
			UserID: 10,
		},
	}
	err := AddUserInfo(list)
	require.NoError(err)
	assert.Equal(1, strings.Count(list[0].IconURL, "http"))
}

func TestAddSoundInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list := []Model{
		{SoundID: testSoundID},
	}
	err := AddSoundInfo(list)
	require.NoError(err)
	assert.Equal(1, strings.Count(list[0].SoundURL, "http"))
	assert.NotZero(list[0].Duration)
}
