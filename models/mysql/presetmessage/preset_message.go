package presetmessage

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

// PresetMessage 预设消息结构，存储预先定义的常用聊天消息
type PresetMessage struct {
	ID           int64  `gorm:"column:id;primary_key"`
	Message      string `gorm:"column:message"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
}

// TableName 返回表名
func (PresetMessage) TableName() string {
	return "live_preset_message"
}

// DB the db instance of current model
func (p *PresetMessage) DB() *gorm.DB {
	return service.LiveDB.Table(p.TableName())
}

// FindAllMessages 获取所有预设消息
func FindAllMessages() ([]string, error) {
	key := keys.LocalKeyPresetMessages0.Format()
	// 从缓存中获取
	if cachedData, found := service.Cache5Min.Get(key); found {
		if messages, ok := cachedData.([]string); ok {
			return messages, nil
		}
	}
	var messages []string
	presetMessage := PresetMessage{}
	if err := presetMessage.DB().Pluck("message", &messages).Error; err != nil {
		return nil, err
	}
	// 更新缓存
	service.Cache5Min.SetDefault(key, messages)
	if len(messages) == 0 {
		return nil, nil
	}
	return messages, nil
}
