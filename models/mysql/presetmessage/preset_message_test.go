package presetmessage

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestFindAllMessages(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试查询数据
	messages, err := FindAllMessages()
	require.NoError(err)
	require.Len(messages, 20)
	assert.Equal("大家好！", messages[0])

	// 测试从缓存中获取数据
	cachedMessages, err := FindAllMessages()
	require.NoError(err)
	require.NotEmpty(cachedMessages)

	// 验证两次查询结果一致
	assert.Equal(messages, cachedMessages)
	assert.Equal(len(messages), len(cachedMessages))
	assert.Equal(messages[0], cachedMessages[0])
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	msg := PresetMessage{}
	assert.Equal("live_preset_message", msg.TableName())
}
