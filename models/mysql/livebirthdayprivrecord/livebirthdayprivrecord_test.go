package livebirthdayprivrecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveBirthdayPrivRecord{}, "id", "create_time", "modified_time", "user_id", "celeb_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_birthday_priv_record", TableName())
	assert.Equal("live_birthday_priv_record", LiveBirthdayPrivRecord{}.TableName())
}

func TestIsRewarded(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(114514)
	now := goutil.TimeNow()
	err := LiveBirthdayPrivRecord{}.DB().Delete("", "user_id = ?", userID).Error
	require.NoError(err)

	year := now.Year()
	exists, err := IsRewarded(userID, year)
	require.NoError(err)
	assert.False(exists)

	require.NoError(RecordUserBirthday(userID, now))
	exists, err = IsRewarded(userID, year)
	require.NoError(err)
	assert.True(exists)
}

func TestFilterRewardedUsers(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userIDs := []int64{114, 514, 1919, 810}
	year := 2024

	res, err := FilterRewardedUserIDs(userIDs, year)
	require.NoError(err)
	assert.Len(res, 1)
	assert.EqualValues(1919, res[0])
}

func TestRecordUserBirthday(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userID := int64(1818)
	var pr LiveBirthdayPrivRecord
	err := pr.DB().Delete("", "user_id = ?", userID).Error
	require.NoError(err)

	now := goutil.TimeNow()
	require.NoError(RecordUserBirthday(userID, now))
	require.NoError(pr.DB().Take(&pr, "user_id = ?", userID).Error)
	assert.Equal(now.Unix(), pr.CelebTime)
}

func TestBatchRecordUserBirthday(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	userIDs := []int64{1919, 1818, 1717}
	var pr LiveBirthdayPrivRecord
	err := pr.DB().Delete("", "user_id IN (?)", userIDs).Error
	require.NoError(err)

	now := goutil.TimeNow()
	require.NoError(BatchRecordUserBirthday(userIDs, now))
	prs := make([]*LiveBirthdayPrivRecord, 0, len(userIDs))
	require.NoError(pr.DB().Find(&prs, "user_id IN (?)", userIDs).Error)
	require.Len(prs, 3)
	assert.Equal(prs[0].CelebTime, now.Unix())
	assert.Equal(prs[1].CelebTime, now.Unix())
	assert.Equal(prs[2].CelebTime, now.Unix())
}
