package livebirthdayprivrecord

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TableName table name
func TableName() string {
	return "live_birthday_priv_record"
}

// LiveBirthdayPrivRecord 用户生日特权发放记录表
type LiveBirthdayPrivRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	UserID    int64 `gorm:"column:user_id"`
	CelebTime int64 `gorm:"column:celeb_time"`
}

// TableName table name
func (LiveBirthdayPrivRecord) TableName() string {
	return TableName()
}

// DB the db instance of LiveBirthdayPrivRecord model
func (pr LiveBirthdayPrivRecord) DB() *gorm.DB {
	return service.LiveDB.Table(TableName())
}

// BeforeSave gorm hook
func (pr *LiveBirthdayPrivRecord) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if pr.DB().NewRecord(pr) {
		pr.CreateTime = nowTime
	}
	pr.ModifiedTime = nowTime
	return
}

// IsRewarded 确认一名用户今年是否已经发过特权
func IsRewarded(userID int64, year int) (bool, error) {
	yearBeginning := time.Date(year, 1, 1, 0, 0, 0, 0, time.Local).Unix()
	yearEnding := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.Local).Unix()
	db := LiveBirthdayPrivRecord{}.DB().
		Where("user_id = ? AND celeb_time >= ? AND celeb_time < ?", userID, yearBeginning, yearEnding)
	return servicedb.Exists(db)
}

// FilterRewardedUserIDs 从 userIDs 中筛选出所有在 year 当年内发放过生日特权的用户
func FilterRewardedUserIDs(userIDs []int64, year int) ([]int64, error) {
	var rewardedUserIDs []int64
	yearBeginning := time.Date(year, 1, 1, 0, 0, 0, 0, time.Local).Unix()
	yearEnding := time.Date(year+1, 1, 1, 0, 0, 0, 0, time.Local).Unix()
	err := LiveBirthdayPrivRecord{}.DB().
		Where("user_id IN (?) AND celeb_time >= ? AND celeb_time < ?",
			userIDs, yearBeginning, yearEnding).Pluck("user_id", &rewardedUserIDs).Error
	if err != nil {
		return nil, err
	}
	return rewardedUserIDs, nil
}

// RecordUserBirthday 记录用户生日特权发放
func RecordUserBirthday(userID int64, celebTime time.Time) error {
	pr := LiveBirthdayPrivRecord{
		UserID:    userID,
		CelebTime: celebTime.Unix(),
	}
	return pr.DB().Create(&pr).Error
}

// BatchRecordUserBirthday 批量记录用户生日特权发放
func BatchRecordUserBirthday(userIDs []int64, celebTime time.Time) error {
	records := make([]LiveBirthdayPrivRecord, 0, len(userIDs))
	nowUnix := goutil.TimeNow().Unix()
	for _, uID := range userIDs {
		record := LiveBirthdayPrivRecord{
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,

			UserID:    uID,
			CelebTime: celebTime.Unix(),
		}
		records = append(records, record)
	}
	return servicedb.BatchInsert(LiveBirthdayPrivRecord{}.DB(), TableName(), records)
}
