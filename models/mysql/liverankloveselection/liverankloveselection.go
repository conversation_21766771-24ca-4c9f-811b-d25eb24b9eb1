package liverankloveselection

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Model of rank_love_selection
type Model struct {
	ID int `gorm:"column:id" json:"-"`

	UserID    int64 `gorm:"column:user_id" json:"user_id"`
	CreatorID int64 `gorm:"column:creator_id" json:"creator_id"`
	Month     int   `gorm:"column:month" json:"month"`

	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
}

// TableName of Model
func (Model) TableName() string {
	return tableName
}

const tableName = "live_rank_love_selection"

// BeforeCreate automatically sets columns create_time and modified_time
func (m *Model) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	m.CreateTime = now
	m.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically updates the column modified_time
func (m *Model) BeforeUpdate() error {
	m.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// GetMonthRankLove 返回用户某月的心动主播
func GetMonthRankLove(userID int64, month int) (*Model, error) {
	var selection Model
	err := service.DB.Table(tableName).Where("user_id = ? AND month = ?", userID, month).Take(&selection).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil, nil
		}
		return nil, err
	}
	return &selection, nil
}

// SetMonthRankLove 设置用户某月的心动主播
func SetMonthRankLove(userID, creatorID int64, month int) error {
	m := Model{
		UserID:    userID,
		CreatorID: creatorID,
		Month:     month,
	}
	// WORKAROUND: 以 Assign(s).FirstOrCreate(&t) 为例，如果 t.BeforeUpdate 只是修改 t 的字段值，则它很可能无效，
	// 原因跟 "gorm:update_attrs" 有关。所以在这里手动更新修改时间
	// https://github.com/jinzhu/gorm/blob/v1.9.12/callback_update.go#L62
	m.ModifiedTime = goutil.TimeNow().Unix()
	return service.DB.Where("user_id = ? AND month = ?", userID, month).
		Assign(m).FirstOrCreate(&m).Error
}
