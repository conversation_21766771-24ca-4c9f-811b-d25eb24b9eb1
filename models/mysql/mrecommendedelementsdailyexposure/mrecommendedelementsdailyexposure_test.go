package mrecommendedelementsdailyexposure

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, SceneHomePage)
	assert.Equal(2, SceneLivePage)
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Model{}, "id", "create_time", "modified_time", "bizdate", "scene", "element_id", "exposure_count")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_recommended_elements_daily_exposure", Model{}.TableName())
}

func TestUpdateOrCreateDailyExposure(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	// 删除测试数据
	require.NoError(Model{}.DB().Delete("", "element_id IN (?) AND scene = ?", []int64{11, 12, 13, 14}, SceneLivePage).Error)

	// 测试没有今日曝光数据时
	UpdateOrCreateDailyExposure([]int64{11, 12, 13}, SceneLivePage)

	// 验证数据插入成功
	var models []Model
	require.NoError(Model{}.DB().Where("element_id IN (?) AND scene = ?", []int64{11, 12, 13}, SceneLivePage).Find(&models).Error)
	require.Len(models, 3)
	assert.EqualValues(11, models[0].ElementID)
	assert.EqualValues(1, models[0].ExposureCount)
	assert.EqualValues(12, models[1].ElementID)
	assert.EqualValues(1, models[1].ExposureCount)
	assert.EqualValues(13, models[2].ElementID)
	assert.EqualValues(1, models[2].ExposureCount)

	// 测试有今日曝光数据时
	UpdateOrCreateDailyExposure([]int64{11, 12, 13}, SceneLivePage)
	var updatedModels []Model
	require.NoError(Model{}.DB().Where("element_id IN (?) AND scene = ?", []int64{11, 12, 13}, SceneLivePage).Find(&updatedModels).Error)
	require.Len(updatedModels, 3)
	assert.EqualValues(11, updatedModels[0].ElementID)
	assert.EqualValues(2, updatedModels[0].ExposureCount)
	assert.EqualValues(12, updatedModels[1].ElementID)
	assert.EqualValues(2, updatedModels[1].ExposureCount)
	assert.EqualValues(13, updatedModels[2].ElementID)
	assert.EqualValues(2, updatedModels[2].ExposureCount)

	// 测试有的存在有的不存在时
	UpdateOrCreateDailyExposure([]int64{11, 12, 14}, SceneLivePage)

	var updatedModels1 []Model
	require.NoError(Model{}.DB().Where("element_id IN (?) AND scene = ?", []int64{11, 12, 14}, SceneLivePage).Find(&updatedModels1).Error)
	require.Len(updatedModels1, 3)
	assert.EqualValues(11, updatedModels[0].ElementID)
	assert.EqualValues(3, updatedModels1[0].ExposureCount)
	assert.EqualValues(12, updatedModels1[1].ElementID)
	assert.EqualValues(3, updatedModels1[1].ExposureCount)
	assert.EqualValues(14, updatedModels1[2].ElementID)
	assert.EqualValues(1, updatedModels1[2].ExposureCount)
}
