package guildscheduleapply

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 公会推荐限制
const (
	GuildApplyCountLimit = 5000
)

// recommend status
const (
	StatusPaused    = iota - 1 // 暂停推荐
	StatusReviewing            // 待审核
	StatusPassed
	StatusRefused
)

// TableName table name
func TableName() string {
	return "live_guild_schedule_apply"
}

// LiveGuildScheduleApply 首页推荐排期资源位申请表
type LiveGuildScheduleApply struct {
	ID           int64 `gorm:"column:id;primary_key" json:"apply_id"`
	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
	DeleteTime   int64 `gorm:"column:delete_time" json:"-"`

	OperatorID int64  `gorm:"column:operator_id" json:"-"`
	GuildID    int64  `gorm:"column:guild_id" json:"guild_id"`
	RoomID     int64  `gorm:"column:room_id" json:"room_id"`
	CreatorID  int64  `gorm:"column:creator_id" json:"creator_id"`
	Status     int    `gorm:"column:status" json:"status"`
	Cover      string `gorm:"column:cover" json:"-"`
	Reason     string `gorm:"column:reason" json:"reason,omitempty"`
	StartTime  int    `gorm:"column:start_time" json:"start_time"` // 首页推荐期申请开始时间, 0, 1800, ..., 84600
	Duration   int    `gorm:"column:duration" json:"-"`            // 持续时间，单位秒

	EndTime         int    `gorm:"-" json:"end_time"` // 首页推荐结束时间
	CreatorUsername string `gorm:"-" json:"creator_username,omitempty"`
	CoverURL        string `gorm:"-" json:"cover_url,omitempty"`
	CoverReviewing  bool   `gorm:"-" json:"cover_reviewing,omitempty"`
}

// TableName for LiveGuildScheduleApply model
func (LiveGuildScheduleApply) TableName() string {
	return TableName()
}

// DB the db instance of current model
func (l LiveGuildScheduleApply) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeSave gorm hook
func (l *LiveGuildScheduleApply) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if l.DB().NewRecord(l) {
		l.CreateTime = nowTime
	}
	l.ModifiedTime = nowTime
	return
}

// AfterFind gorm hook AfterFind
func (l *LiveGuildScheduleApply) AfterFind() (err error) {
	// NOTICE: 没处理跨天的情况
	l.EndTime = l.StartTime + l.Duration
	l.CoverURL = storage.ParseSchemeURL(l.Cover)
	return err
}

// Save 保存推荐申请
func (l *LiveGuildScheduleApply) Save(reviewCover string) error {
	err := servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		err := tx.Save(&l).Error
		if err != nil {
			return err
		}
		if reviewCover == "" {
			// 不需要处理背景图
			return nil
		}
		l.CoverReviewing = true
		// TODO: 合并事务数据库
		lr := &livereview.LiveReview{
			UserID:     l.CreatorID,
			RoomID:     l.RoomID,
			UploadTime: goutil.TimeNow().Unix(),
			Type:       livereview.TypeScheduleRecommend,
			Status:     livereview.StatusReviewing,
			GuildID:    l.GuildID,
			ReviewInfo: livereview.ReviewInfo{
				ImageURL: reviewCover,
			},
		}
		return lr.Add()
	})
	if err != nil {
		return err
	}
	return nil
}

// PauseApply 暂停推荐
func PauseApply(applyID int64) (bool, error) {
	db := LiveGuildScheduleApply{}.DB().Where("id = ? AND status = ? AND delete_time = 0", applyID, StatusPassed).
		Updates(map[string]interface{}{
			"status":        StatusPaused,
			"modified_time": goutil.TimeNow().Unix(),
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected != 0, nil
}

// RestartApply 重新开始推荐
func RestartApply(applyID int64) (bool, error) {
	apply, err := FindApplyByID(applyID)
	if err != nil {
		return false, err
	}
	if apply == nil {
		return false, nil
	}
	db := LiveGuildScheduleApply{}.DB().Where("id = ? AND status = ? AND delete_time = 0", applyID, StatusPaused).
		Updates(map[string]interface{}{
			"status":        StatusPassed,
			"modified_time": goutil.TimeNow().Unix(),
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected != 0, nil
}

// FindApplyByID 通过申请 ID 查找推荐
func FindApplyByID(applyID int64) (*LiveGuildScheduleApply, error) {
	record := new(LiveGuildScheduleApply)
	err := LiveGuildScheduleApply{}.DB().First(record, "id = ? AND delete_time = 0", applyID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return record, nil
}

// FindApply 通过主播 ID 和公会 ID 查找推荐
func FindApply(creatorID int64, guildID int64) (*LiveGuildScheduleApply, error) {
	record := new(LiveGuildScheduleApply)
	err := LiveGuildScheduleApply{}.DB().First(record, "creator_id = ? AND guild_id = ? AND delete_time = 0",
		creatorID, guildID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return record, nil
}

// ApplyCount 公会推荐申请总量
// 排除未过审、暂停的推荐申请
func ApplyCount(guildID int64) (int64, error) {
	var count int64
	err := LiveGuildScheduleApply{}.DB().Where("guild_id = ? AND delete_time = 0", guildID).
		Not("status", []int{StatusPaused, StatusRefused}).Count(&count).Error
	if err != nil {
		return count, err
	}
	return count, nil
}

// apply search type
const (
	SearchTypeAll = iota
	SearchTypePending
	SearchTypePassed
	SearchTypeRefused
	SearchTypePaused
)

// SearchParam search param
type SearchParam struct {
	CreatorIDs      []int64
	CreatorUsername string
	Type            int
}

// FindApplyList 根据主播获取申请列表
func FindApplyList(p, pageSize int64, searchParam SearchParam) ([]*LiveGuildScheduleApply, goutil.Pagination, error) {
	var count int64
	var err error
	db := LiveGuildScheduleApply{}.DB().Where("delete_time = 0")
	creatorIDs := searchParam.CreatorIDs
	if searchParam.CreatorUsername != "" {
		creatorIDs, err = mowangskuser.SearchUserByUsername(searchParam.CreatorUsername, searchParam.CreatorIDs)
		if err != nil {
			return nil, goutil.Pagination{}, err
		}
		if len(creatorIDs) == 0 {
			return make([]*LiveGuildScheduleApply, 0), goutil.Pagination{}, nil
		}
	}
	db = db.Where("creator_id IN (?)", creatorIDs)
	switch searchParam.Type {
	case SearchTypePending:
		db = db.Where("status = ?", StatusReviewing)
	case SearchTypePassed:
		db = db.Where("status = ?", StatusPassed)
	case SearchTypeRefused:
		db = db.Where("status = ?", StatusRefused)
	case SearchTypePaused:
		db = db.Where("status = ?", StatusPaused)
	}
	db = db.Order("id DESC")
	err = db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*LiveGuildScheduleApply, 0), pa, nil
	}

	var res []*LiveGuildScheduleApply
	err = pa.ApplyTo(db).Find(&res).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	return res, pa, err
}

// AfterQuitGuild 主播退出公会后清理相关数据
// NOTICE: creatorIDs 为 nil 时，清空公会旗下所有主播的推荐数据；creatorIDs 不为 nil 时，清空 creatorIDs 中主播的推荐数据
func AfterQuitGuild(guildID int64, creatorIDs []int64) error {
	if creatorIDs != nil && len(creatorIDs) == 0 {
		return nil
	}

	now := goutil.TimeNow().Unix()
	db := LiveGuildScheduleApply{}.DB().Where("guild_id = ? AND delete_time = 0", guildID)
	if len(creatorIDs) > 0 {
		db = db.Where("creator_id IN (?)", creatorIDs)
	}
	err := db.Updates(map[string]interface{}{
		"delete_time":   now,
		"modified_time": now,
	}).Error
	if err != nil {
		return err
	}

	db = service.DB.Table(livereview.TableName()).Where("guild_id = ? AND status = ? AND type = ?",
		guildID, livereview.StatusReviewing, livereview.TypeScheduleRecommend)
	if len(creatorIDs) > 0 {
		db = db.Where("user_id IN (?)", creatorIDs)
	}
	err = db.Updates(map[string]interface{}{
		"status":        livereview.StatusOutdated,
		"modified_time": now,
	}).Error
	if err != nil {
		return err
	}
	return nil
}
