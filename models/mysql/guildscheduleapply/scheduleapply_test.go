package guildscheduleapply

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	sf := LiveGuildScheduleApply{}
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(sf, "id", "create_time", "modified_time", "delete_time", "operator_id", "guild_id", "room_id", "creator_id", "status", "cover",
		"reason", "start_time", "duration")

	kc = tutil.New<PERSON>eyChecker(t, tutil.JSON)
	kc.Check(sf, "apply_id", "create_time", "guild_id", "room_id", "creator_id",
		"status", "reason", "start_time", "end_time", "creator_username", "cover_url", "cover_reviewing")
}

var (
	testCreatorID       int64 = 12345
	testDeleteCreatorID int64 = 54321
	testGuildID         int64 = 3
	testExistApplyID    int64 = 1
)

func TestLiveGuildScheduleApplySave(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cover := "oss://test_edit.png"
	apply := &LiveGuildScheduleApply{
		GuildID:   2021070118,
		RoomID:    1,
		CreatorID: 1,
		Cover:     "oss://test/png",
		StartTime: 0,
		Duration:  1800,
	}
	require.NoError(apply.Save(cover))
	assert.NotZero(apply.ID)
	assert.NotZero(apply.CreateTime)
	assert.NotZero(apply.ModifiedTime)
	assert.True(apply.CoverReviewing)

	count, err := ApplyCount(apply.GuildID)
	require.NoError(err)
	assert.EqualValues(1, count)
	lr, err := livereview.FindGuildRoomReviewing(apply.GuildID, apply.RoomID, livereview.TypeScheduleRecommend)
	require.NoError(err)
	require.NotNil(lr)
	assert.Equal(cover, lr.ImageURL)
}

func TestApplyPauseAndRestart(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testPassApplyID := int64(3)
	ok, err := PauseApply(testPassApplyID)
	require.NoError(err)
	assert.True(ok)

	ok, err = RestartApply(testPassApplyID)
	require.NoError(err)
	assert.True(ok)
}

func TestFindApplyByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	apply, err := FindApplyByID(testExistApplyID)
	require.NoError(err)
	assert.NotNil(apply)
	assert.NotZero(apply.EndTime)

	apply, err = FindApplyByID(-99999)
	require.NoError(err)
	assert.Nil(apply)
}

func TestFindApply(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	apply, err := FindApply(testCreatorID, testGuildID)
	require.NoError(err)
	assert.NotNil(apply)

	apply, err = FindApply(testCreatorID, 999)
	require.NoError(err)
	assert.Nil(apply)

	apply, err = FindApply(testDeleteCreatorID, testGuildID)
	require.NoError(err)
	assert.Nil(apply)
}

func TestApplyCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count, err := ApplyCount(testGuildID)
	require.NoError(err)
	assert.GreaterOrEqual(count, int64(1))

	count, err = ApplyCount(99999)
	require.NoError(err)
	assert.Zero(count)
}

func TestFindApplyList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testCreatorIDs := []int64{12345, 10}
	res, pa, err := FindApplyList(1, 20, SearchParam{CreatorIDs: testCreatorIDs})
	require.NoError(err)
	assert.NotNil(pa)
	assert.NotEmpty(res)

	res, pa, err = FindApplyList(1, 20, SearchParam{CreatorIDs: testCreatorIDs[:1]})
	require.NoError(err)
	assert.NotNil(pa)
	assert.Len(res, 1)

	res, pa, err = FindApplyList(1, 20, SearchParam{CreatorIDs: testCreatorIDs[:1], Type: SearchTypeRefused})
	require.NoError(err)
	assert.NotNil(pa)
	assert.Empty(res)

	res, pa, err = FindApplyList(1, 20, SearchParam{CreatorIDs: testCreatorIDs[:1], CreatorUsername: "111111111"})
	require.NoError(err)
	assert.NotNil(pa)
	assert.Empty(res)

	res, pa, err = FindApplyList(1, 20, SearchParam{CreatorIDs: testCreatorIDs[:1], Type: SearchTypePending, CreatorUsername: "b"})
	require.NoError(err)
	assert.NotNil(pa)
	assert.Empty(res)
}

func TestAfterQuitGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	apply := &LiveGuildScheduleApply{
		GuildID:   20210709,
		RoomID:    1,
		CreatorID: 1,
		Cover:     "oss://test/png",
		StartTime: 0,
		Duration:  1800,
	}
	require.NoError(apply.Save(apply.Cover))

	err := AfterQuitGuild(apply.GuildID, []int64{apply.CreatorID})
	require.NoError(err)
	record, err := FindApply(apply.CreatorID, apply.GuildID)
	require.NoError(err)
	assert.Nil(record)
	lr, err := livereview.FindGuildRoomReviewing(apply.GuildID, apply.RoomID, livereview.TypeScheduleRecommend)
	require.NoError(err)
	assert.Nil(lr)

	apply = &LiveGuildScheduleApply{
		GuildID:   20211123,
		RoomID:    1,
		CreatorID: 2,
		Cover:     "oss://test/png",
		StartTime: 0,
		Duration:  1800,
	}
	require.NoError(apply.Save(apply.Cover))

	err = AfterQuitGuild(apply.GuildID, []int64{})
	require.NoError(err)
	record, err = FindApply(apply.CreatorID, apply.GuildID)
	require.NoError(err)
	assert.NotNil(record)

	err = AfterQuitGuild(apply.GuildID, nil)
	require.NoError(err)
	record, err = FindApply(apply.CreatorID, apply.GuildID)
	require.NoError(err)
	assert.Nil(record)
	lr, err = livereview.FindGuildRoomReviewing(apply.GuildID, apply.RoomID, livereview.TypeScheduleRecommend)
	require.NoError(err)
	assert.Nil(lr)
}
