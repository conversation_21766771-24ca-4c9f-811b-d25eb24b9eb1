package gift

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// gift type
const (
	TypeLiveCommon = 1
	TypeLiveRebate = 8
)

// TableName table name
func TableName() string {
	return "gift"
}

// Gift 礼物表
type Gift struct {
	ID    int64  `gorm:"column:id;primary_key" json:"id"`
	Name  string `gorm:"column:name" json:"name"`
	Price int64  `gorm:"column:price" json:"price"`
	Type  int    `gorm:"column:type" json:"type"`
}

// TableName table name
func (Gift) TableName() string {
	return TableName()
}

// FindGift 获取礼物
func FindGift(giftID int64) (*Gift, error) {
	key := keys.KeyGift1.Format(giftID)
	giftBytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	var gift *Gift
	if len(giftBytes) != 0 {
		if err = json.Unmarshal(giftBytes, &gift); err != nil {
			return nil, err
		}
		return gift, nil
	}
	gift = new(Gift)
	err = service.DB.First(gift, "id = ?", giftID).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}
	if !servicedb.IsErrNoRows(err) {
		giftBytes, err = json.Marshal(gift)
		if err != nil {
			logger.Error(err)
			// PASS
			return gift, nil
		}
	} else {
		giftBytes = []byte("null")
	}
	// 防止查不到数据击穿到数据库中，创建一个空缓存
	err = service.LRURedis.Set(key, giftBytes, time.Minute*10).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return gift, nil
}
