package gift

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Gift{}, "id", "name", "price", "type")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Gift{}, "id", "name", "price", "type")
}

func TestFindGift(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key := keys.KeyGift1.Format(40023)
	require.NoError(service.LRURedis.Del(key).Err())

	gift, err := FindGift(40023)
	require.NoError(err)
	require.NotNil(gift)
	assert.Equal("【半价版】牛牛", gift.Name)

	giftBytes, err := service.LRURedis.Get(key).Bytes()
	require.NoError(err)
	gift = new(Gift)
	err = json.Unmarshal(giftBytes, &gift)
	require.NoError(err)
	assert.Equal("【半价版】牛牛", gift.Name)

	key = keys.KeyGift1.Format(1234)
	require.NoError(service.LRURedis.Del(key).Err())
	gift, err = FindGift(1234)
	require.NoError(err)
	require.NotNil(gift)
	giftStr, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	assert.Equal("null", giftStr)
	gift, err = FindGift(1234)
	require.NoError(err)
	require.Nil(gift)
}
