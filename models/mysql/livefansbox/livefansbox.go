package livefansbox

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "live_fans_box"

// MaxLevel 粉丝团宝箱最高等级
const MaxLevel = 7

// Level constants for each fans box level
const (
	Level1 = iota + 1
	Level2
	Level3
	Level4
	Level5
	Level6
	Level7
)

// 奖励类型
const (
	RewardTypeCatFood              = iota + 1 // 猫粮
	RewardTypeFreeGift                        // 免费礼物
	RewardTypeExclusiveAndFreeGift            // 专属礼物 + 免费礼物
	RewardTypeAvatarFrame                     // 头像框
)

// RewardTipMap 不同奖励的提示路径文案
var RewardTipMap = map[int]string{
	RewardTypeCatFood:              "查看路径：礼物背包",
	RewardTypeFreeGift:             "查看路径：礼物背包",
	RewardTypeExclusiveAndFreeGift: "查看路径：礼物栏专属和背包",
	RewardTypeAvatarFrame:          "查看路径：外观中心",
}

// LiveFansBox 粉丝团宝箱表
type LiveFansBox struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	Level     int    `gorm:"column:level"`      // 宝箱等级
	FansCount int64  `gorm:"column:fans_count"` // 达到该等级需要的粉丝数量
	Name      string `gorm:"column:name"`       // 宝箱名称
	Icon      string `gorm:"column:icon"`       // 宝箱图片
	Energy    int    `gorm:"column:energy"`     // 解锁宝箱所需能量值
	More      []byte `gorm:"column:more"`       // 更多信息（宝箱奖品、奖品权重、每日库存等）

	MoreInfo More   `gorm:"-"`
	IconURL  string `gorm:"-"`
}

// More 更多信息
type More struct {
	Rewards []RewardInfo `json:"rewards"`
}

// RewardInfo 宝箱奖品、奖品权重、每日库存等
type RewardInfo struct {
	Type       int     `json:"type"`        // 奖励类型。1：猫粮；2：免费礼物；3：专属礼物 + 免费礼物；4：头像框
	Weight     int64   `json:"weight"`      // 奖品权重（e.g. 掉率为 50% 时，权重存储为 500000）
	DailyStock int64   `json:"daily_stock"` // 每日库存
	PrizeIDs   []int64 `json:"prize_ids"`   // 直播奖品（live_prize）表 ID
}

// DB the db instance of current model
func (l LiveFansBox) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// TableName for current model
func (l LiveFansBox) TableName() string {
	return tableName
}

// AfterFind gorm 钩子
func (l *LiveFansBox) AfterFind() error {
	if len(l.More) > 0 {
		err := json.Unmarshal(l.More, &l.MoreInfo)
		if err != nil {
			return err
		}
	}
	l.IconURL = storage.ParseSchemeURL(l.Icon)

	return nil
}

// TODO: 待产品确认礼物 ID 之后，此处硬编码写死
const guaranteedPrizeID = 100000

// GetGuaranteedReward 获取保底的奖励
func GetGuaranteedReward() *RewardInfo {
	return &RewardInfo{
		Type:     RewardTypeFreeGift,
		PrizeIDs: []int64{guaranteedPrizeID},
	}
}

// GetConsumedStock 获取粉丝团宝箱奖励的每日消耗库存数量
func GetConsumedStock(roomID int64, level int, rewards []RewardInfo) ([]int64, error) {
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	consumedStockKeys := make([]string, len(rewards))
	for i, reward := range rewards {
		consumedStockKeys[i] = keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, reward.Type, todayFormat)
	}
	rewardConsumedStocks := make([]int64, 0, len(rewards))
	consumedStocks, err := service.Redis.MGet(consumedStockKeys...).Result()
	if err != nil {
		return rewardConsumedStocks, err
	}
	for i, val := range consumedStocks {
		if val == nil {
			val = "0"
		}
		consumedStock, err := strconv.ParseInt(val.(string), 10, 64)
		if err != nil {
			logger.WithFields(logger.Fields{
				"level": level,
				"index": i,
			}).Error("直播间粉丝团宝箱已消耗库存缓存值数据错误")
		}
		rewardConsumedStocks = append(rewardConsumedStocks, consumedStock)
	}
	return rewardConsumedStocks, nil
}

// IncrConsumedStock 增加或减少宝箱奖励库存的消耗数
func IncrConsumedStock(roomID int64, level, rewardType int, num int64) (int64, error) {
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, rewardType, todayFormat)
	pipe := service.Redis.TxPipeline()
	cmd := pipe.IncrBy(key, num)
	pipe.Expire(key, 72*time.Hour) // 设置缓存有效期为 3 天
	_, err := pipe.Exec()
	if err != nil {
		return 0, err
	}
	currentNum, err := cmd.Result()
	if err != nil {
		return 0, err
	}
	return currentNum, nil
}

// EnableFansBox 是否启用粉丝宝箱功能
func EnableFansBox() bool {
	return config.Conf.Params.MedalParams.EnableFansBox
}

// ListFansBox 粉丝团宝箱列表，有 1 分钟缓存
func ListFansBox() ([]*LiveFansBox, error) {
	key := keys.KeyFansBoxList0.Format()
	fansBoxBytes, err := service.LRURedis.Get(key).Bytes()
	var list []*LiveFansBox
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	} else if len(fansBoxBytes) != 0 {
		err = json.Unmarshal(fansBoxBytes, &list)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return list, nil
		}
	}
	err = LiveFansBox{}.DB().Order("level ASC").Find(&list).Error
	if err != nil {
		return nil, err
	}
	cacheByte, err := json.Marshal(&list)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = service.LRURedis.Set(key, string(cacheByte), time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return list, nil
}

// FindLevelFansBoxMap 粉丝团宝箱等级 Map
func FindLevelFansBoxMap() (map[int]*LiveFansBox, error) {
	fansBoxList, err := ListFansBox()
	if err != nil {
		return nil, err
	}
	return goutil.ToMap(fansBoxList, "Level").(map[int]*LiveFansBox), nil
}
