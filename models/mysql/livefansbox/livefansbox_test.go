package livefansbox

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(7, MaxLevel)
	assert.Equal(1, Level1)
	assert.Equal(2, Level2)
	assert.Equal(3, Level3)
	assert.Equal(4, Level4)
	assert.Equal(5, Level5)
	assert.Equal(6, Level6)
	assert.Equal(7, Level7)

	assert.Equal(1, RewardTypeCatFood)
	assert.Equal(2, RewardTypeFreeGift)
	assert.Equal(3, RewardTypeExclusiveAndFreeGift)
	assert.Equal(4, RewardTypeAvatarFrame)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveFansBox{}, "id", "create_time", "modified_time", "level", "fans_count", "name", "icon", "energy", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(More{}, "rewards")
	kc.Check(RewardInfo{}, "type", "weight", "daily_stock", "prize_ids")
}

func TestLiveFansBox_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_fans_box", LiveFansBox{}.TableName())
}

func TestLiveFansBox_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := LiveFansBox{
		Icon: "oss://icon.png",
		More: []byte("{\"rewards\":[{\"weight\":500000,\"daily_stock\":690,\"prize_ids\":[1,2],\"type\":3}]}"),
	}
	require.NoError(l.AfterFind())
	require.Len(l.MoreInfo.Rewards, 1)
	assert.EqualValues(3, l.MoreInfo.Rewards[0].Type)
	assert.EqualValues(690, l.MoreInfo.Rewards[0].DailyStock)
	assert.EqualValues(500000, l.MoreInfo.Rewards[0].Weight)
	assert.Equal([]int64{1, 2}, l.MoreInfo.Rewards[0].PrizeIDs)
	assert.Equal("https://static-test.missevan.com/icon.png", l.IconURL)
}

func TestGetGuaranteedReward(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	reward := GetGuaranteedReward()
	require.NotNil(reward)
	assert.Equal(RewardTypeFreeGift, reward.Type)
	assert.Equal([]int64{guaranteedPrizeID}, reward.PrizeIDs)
}

func TestGetConsumedStock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock data
	roomID := int64(6456425)
	level := Level1
	rewards := []RewardInfo{
		{Type: RewardTypeFreeGift, DailyStock: 100, PrizeIDs: []int64{4543001, 4543002}},
		{Type: RewardTypeAvatarFrame, DailyStock: 100, PrizeIDs: []int64{4543003}},
	}
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key1 := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, RewardTypeFreeGift, todayFormat)
	key2 := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, RewardTypeAvatarFrame, todayFormat)
	require.NoError(service.Redis.Del(key1, key2).Err())
	require.NoError(service.Redis.IncrBy(key2, 6).Err())

	// 测试获取库存消耗数据
	consumedStocks, err := GetConsumedStock(roomID, level, rewards)
	require.NoError(err)
	require.Len(consumedStocks, 2)
	assert.EqualValues(0, consumedStocks[0])
	assert.EqualValues(6, consumedStocks[1])
}

func TestIncrConsumedStock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock data
	roomID := int64(6456425)
	level := Level1
	rewardType := RewardTypeFreeGift
	num := int64(1)
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(roomID, level, rewardType, todayFormat)
	require.NoError(service.Redis.Del(key).Err())

	// 测试无缓存时新增库存消耗数据
	newConsumedStock, err := IncrConsumedStock(roomID, level, rewardType, num)
	require.NoError(err)
	assert.EqualValues(num, newConsumedStock)
	// 验证数据
	val, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(num, val)

	// 测试有缓存时新增库存消耗数据
	newConsumedStock, err = IncrConsumedStock(roomID, level, rewardType, num)
	require.NoError(err)
	assert.EqualValues(num*2, newConsumedStock)
	// 验证数据
	val, err = service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(num*2, val)
}

func TestEnableFansBox(t *testing.T) {
	assert := assert.New(t)

	before := config.Conf.Params.MedalParams.EnableFansBox
	config.Conf.Params.MedalParams.EnableFansBox = true
	assert.True(EnableFansBox())

	config.Conf.Params.MedalParams.EnableFansBox = false
	assert.False(EnableFansBox())
	config.Conf.Params.MedalParams.EnableFansBox = before
}

func TestListFansBox(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyFansBoxList0.Format()
	require.NoError(service.LRURedis.Del(key).Err())

	// 测试没有缓存的情况
	fansBoxList, err := ListFansBox()
	require.NoError(err)
	require.Len(fansBoxList, 7)

	// 验证缓存是否生成
	fansBoxBytes, err := service.LRURedis.Get(key).Bytes()
	require.NoError(err)
	require.NotEmpty(fansBoxBytes)
	var res []*LiveFansBox
	err = json.Unmarshal(fansBoxBytes, &res)
	require.NoError(err)
	require.Len(res, 7)
	assert.Equal(fansBoxList, res)

	// 测试有缓存的情况
	fansBoxList1, err := ListFansBox()
	require.NoError(err)
	require.Len(fansBoxList1, 7)
	assert.Equal(res, fansBoxList1)
}

func TestFindLevelFansBoxMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	fansBoxMap, err := FindLevelFansBoxMap()
	require.NoError(err)
	require.Len(fansBoxMap, 7)
	assert.NotEmpty(fansBoxMap[1])
	assert.NotEmpty(fansBoxMap[2])
	assert.NotEmpty(fansBoxMap[3])
	assert.NotEmpty(fansBoxMap[4])
	assert.NotEmpty(fansBoxMap[5])
	assert.NotEmpty(fansBoxMap[6])
	assert.NotEmpty(fansBoxMap[7])
}
