package archiveliveranknova

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestBatchInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Where("rank_time = ?", "2022-08-11").Delete(&ArchiveLiveRankNova{}).Error
	require.NoError(err)

	records := []ArchiveLiveRankNova{
		{
			RankTime:  "2022-08-11",
			RoomID:    123,
			CreatorID: 9074508,
			Score:     10,
			Rank:      1,
		},
		{
			RankTime:  "2022-08-11",
			RoomID:    121,
			CreatorID: 9074509,
			Score:     9,
			Rank:      2,
		},
	}
	err = BatchInsert(records)
	require.NoError(err)

	var count int
	err = service.LiveDB.Table(ArchiveLiveRankNova{}.TableName()).Where("rank_time = ?", "2022-08-11").Count(&count).Error
	require.NoError(err)
	assert.Equal(2, count)
}
