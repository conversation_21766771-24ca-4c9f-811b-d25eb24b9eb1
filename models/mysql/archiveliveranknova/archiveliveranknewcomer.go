package archiveliveranknova

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// ArchiveLiveRankNova 直播新人榜榜归档
type ArchiveLiveRankNova struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	RankTime     string `gorm:"column:rank_time"`
	RoomID       int64  `gorm:"column:room_id"`
	CreatorID    int64  `gorm:"column:creator_id"`
	Score        int64  `gorm:"column:score"`
	Rank         int    `gorm:"column:rank"`
}

// TableName of model "archive_live_rank_nova"
func (ArchiveLiveRankNova) TableName() string {
	return "archive_live_rank_nova"
}

// BatchInsert 批量插入新人榜记录
func BatchInsert(records []ArchiveLiveRankNova) error {
	return servicedb.BatchInsert(service.LiveDB, ArchiveLiveRankNova{}.TableName(), records)
}
