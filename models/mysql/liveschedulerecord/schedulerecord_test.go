package liveschedulerecord

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(ScheduleRecord{}, "id", "create_time", "modified_time", "guild_id", "creator_id", "room_id",
		"recommend_id", "attr", "day", "start_time", "expire_time")
}

func TestRecordScheduleByDay(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testDay := int64(12345678)
	err := RecordScheduleByDay(testDay, []*ScheduleRecord{
		{
			CreatorID:   1,
			RoomID:      1,
			RecommendID: 1,
			Day:         testDay,
		},
		{
			CreatorID:   2,
			RoomID:      2,
			RecommendID: 2,
			Day:         testDay,
		},
	})
	require.NoError(err)
	var result []ScheduleRecord
	require.NoError(ScheduleRecord{}.DB().Where("", "day = ?", testDay).Find(&result).Error)
	assert.Len(result, 2)

	err = RecordScheduleByDay(testDay, []*ScheduleRecord{})
	require.NoError(err)
	require.NoError(ScheduleRecord{}.DB().Where("", "day = ?", testDay).Find(&result).Error)
	assert.Empty(result)
}
