package liveschedulerecord

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TableName table name
func TableName() string {
	return "live_schedule_record"
}

// ScheduleRecord of table live_schedule_record
type ScheduleRecord struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	GuildID     int64 `gorm:"column:guild_id"`
	CreatorID   int64 `gorm:"column:creator_id"`
	RoomID      int64 `gorm:"column:room_id"`
	RecommendID int64 `gorm:"column:recommend_id"`
	// 直播推荐标签，按位实现，默认公会申请推荐，位 1: 艺人主播、位 2: 优质主播、位 3: 神话推荐
	Attr       goutil.BitMask `gorm:"column:attr"`
	Day        int64          `gorm:"column:day"` // 推荐日期的 0 点时间戳, 如: 1623859200
	StartTime  int64          `gorm:"column:start_time"`
	ExpireTime int64          `gorm:"column:expire_time"`
}

// TableName table name
func (ScheduleRecord) TableName() string {
	return TableName()
}

// DB the db instance of current model
func (s ScheduleRecord) DB() *gorm.DB {
	return service.LiveDB.Table(s.TableName())
}

// RecordScheduleByDay 按日期记录首页排期推荐
func RecordScheduleByDay(day int64, data []*ScheduleRecord) error {
	return servicedb.Tx(service.LiveDB, func(tx *gorm.DB) error {
		err := tx.Table(TableName()).Delete("", "day = ?", day).Error
		if err != nil {
			return err
		}
		if len(data) > 0 {
			err = servicedb.BatchInsert(tx, TableName(), data)
			if err != nil {
				return err
			}
		}
		return nil
	})
}
