package livetxnorder

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// SuperFanTxnOrder 超粉开通/续费订单
// NOTICE: 联表查询结果，并不是表 live_txn_order 所存数据
type SuperFanTxnOrder struct {
	ID         int64 `gorm:"column:id"`
	CreateTime int64 `gorm:"column:create_time"`

	ExpireTime int64  `gorm:"column:expire_time"`
	Title      string `gorm:"column:title"` // 主播用户名
	Price      int    `gorm:"column:price"`
	Status     int    `gorm:"column:status"`

	TID     int64 `gorm:"column:tid"`
	GoodsID int64 `gorm:"column:goods_id"`
	Attr    int   `gorm:"column:attr"`

	BuyerID  int64 `gorm:"column:buyer_id"`
	SellerID int64 `gorm:"column:seller_id"`

	// 表 live_goods 字段
	Num int `gorm:"-"`
}

// GoodsTitle 商品简介
func (sto SuperFanTxnOrder) GoodsTitle() string {
	if sto.Attr == livegoods.AttrSuperFanRegister {
		return fmt.Sprintf("开通直播超粉--%d 个月", sto.Num)
	}
	return fmt.Sprintf("续费直播超粉--%d 个月", sto.Num)
}

// RoomSuperFanByPage 某主播的房间内开通/续费超粉的记录，创建时间倒序
// 参数 times: times[0] start_time，times[1] end_time，length 超过 2 会 panic
func RoomSuperFanByPage(creatorID int64, p, pageSize int64, times ...time.Time) ([]*SuperFanTxnOrder, util.Pagination, error) {
	var count int64
	db := LiveTxnOrder{}.DB().Where("goods_type = ? AND status = ? AND seller_id = ?",
		livegoods.GoodsTypeSuperFan, StatusSuccess, creatorID)

	switch len(times) {
	case 0:
		// PASS
	case 1:
		db = db.Where("create_time >= ?", times[0].Unix())
	case 2:
		db = db.Where("create_time >= ? AND create_time < ?", times[0].Unix(), times[1].Unix())
	default:
		panic("too many time values")
	}

	err := db.Count(&count).Error
	if err != nil {
		return nil, util.Pagination{}, err
	}
	pa := util.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []*SuperFanTxnOrder{}, pa, nil
	}

	goods, err := livegoods.AllSuperFan()
	if err != nil {
		return nil, pa, err
	}
	goodsMap := util.ToMap(goods, "ID").(map[int64]livegoods.LiveGoods)

	res := make([]*SuperFanTxnOrder, 0)
	err = pa.ApplyTo(db).Order("create_time DESC").Find(&res).Error
	if err != nil {
		return nil, pa, err
	}
	for i := range res {
		res[i].Num = goodsMap[res[i].GoodsID].Num
	}
	return res, pa, err
}

// IsFirstBuySuperFan 是否是第一次购买超粉
func IsFirstBuySuperFan(userID int64) (bool, error) {
	var order LiveTxnOrder
	err := order.DB().
		Select("id").
		Where("goods_type = ? AND buyer_id = ? AND status = ?",
			livegoods.GoodsTypeSuperFan,
			userID,
			StatusSuccess,
		).Take(&order).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return true, nil
		}
		return false, err
	}

	return false, nil
}

// IsPurchasedSuperFanInRoom 是否在某主播直播间购买过指定超粉
func IsPurchasedSuperFanInRoom(creatorID, userID, goodsID int64) (bool, error) {
	exists, err := servicedb.Exists(LiveTxnOrder{}.DB().
		Where("goods_type = ? AND goods_id = ?", livegoods.GoodsTypeSuperFan, goodsID).
		Where("seller_id = ? AND buyer_id = ? AND status = ?", creatorID, userID, StatusSuccess))
	if err != nil {
		return false, err
	}
	return exists, nil
}
