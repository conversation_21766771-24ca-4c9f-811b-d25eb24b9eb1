package livetxnorder

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 订单状态
// 目前同 transaction_log
const (
	StatusIllegal       = iota - 5 // 非法交易（代充）
	StatusRefundDiamond            // 退钻
	StatusRefund                   // 退款（人民币）
	StatusCancel                   // 取消（直播问答取消）
	StatusUndone                   // 未完成（直播问答提问中）
	StatusCreate                   // 创建，值为 0
	StatusSuccess                  // 交易成功，值为 1
)

// 开播状态
// 同 models/mongodb/livegifts/live_gifts.go
const (
	OpenStatusNoRoom = iota - 1 // 不在直播间
	OpenStatusClosed            // 直播间关播
	OpenStatusOpen              // 直播间开播
)

// TableName live_txn_order
func TableName() string {
	return "live_txn_order"
}

// ADBTableName adb 下的表名
func ADBTableName() string {
	if util.IsProdEnv() {
		// 线上环境 adb 同步表在 live 库下
		return "live." + TableName()
	}
	return TableName()
}

// ADB adb 的表
func ADB() *gorm.DB {
	db := service.NewADB
	if !util.IsProdEnv() {
		// 非线上环境没有 adb, 这里使用 missevan_live 代替
		db = service.LiveDB
	}

	return db.Table(ADBTableName())
}

// LiveTxnOrder model for table missevan_live.live_txn_order
type LiveTxnOrder struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	ExpireTime int64  `gorm:"column:expire_time"` // 超粉过期时间
	Title      string `gorm:"column:title"`
	Price      int    `gorm:"column:price"`
	Status     int    `gorm:"column:status"`

	TID       int64 `gorm:"column:tid"`
	GoodsID   int64 `gorm:"column:goods_id"`
	GoodsType int   `gorm:"column:goods_type"`
	Attr      int   `gorm:"column:attr"`

	BuyerID  int64     `gorm:"column:buyer_id" json:"-"` // REVIEW: json tag 好像不需要？
	SellerID int64     `gorm:"column:seller_id" json:"-"`
	More     *MoreInfo `gorm:"column:more" json:"-"`
}

// MoreInfo 订单额外信息
type MoreInfo struct {
	// TODO: 改成必传项
	OpenStatus *int `json:"open_status,omitempty"`
	// 用户购买时使用的气泡
	Bubble *bubble.Simple `json:"bubble,omitempty"`

	// Num 购买数量，星座许愿中，num 是用户购买数量，其他情况（如果记录）是 live_goods 中的 num
	Num   int     `json:"num,omitempty"`
	Gifts []*Gift `json:"gifts,omitempty"`
	// GuaranteedGiftID 触发保底时抽出的礼物 ID
	GuaranteedGiftID int64 `json:"guaranteed_gift_id,omitempty"`

	DanmakuMessage string `json:"danmaku_message,omitempty"`
}

// Value 实现方法
func (m *MoreInfo) Value() (driver.Value, error) {
	if m == nil {
		return "", nil
	}
	return json.Marshal(m)
}

// Scan 实现方法
func (m *MoreInfo) Scan(input interface{}) error {
	switch p := input.(type) {
	case string:
		// 兼容 sqlite 中已提前存入的数据
		if p == "" {
			return nil
		}
		return json.Unmarshal([]byte(p), m)
	case []uint8:
		if len(p) == 0 {
			return nil
		}
		return json.Unmarshal(input.([]byte), m)
	default:
		return nil
	}
}

// DB the db instance of current model
func (lto LiveTxnOrder) DB() *gorm.DB {
	return service.LiveDB.Table(lto.TableName())
}

// TableName for current model
func (LiveTxnOrder) TableName() string {
	return TableName()
}

// BeforeSave gorm hook
func (lto *LiveTxnOrder) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if lto.DB().NewRecord(lto) && lto.CreateTime == 0 {
		lto.CreateTime = nowTime
	}
	lto.ModifiedTime = nowTime
	return
}

// NewOrder new LiveTxnOrder
// NOTICE: 如果 more 中记录的 num 不是 live_goods 中的 num 时，需要手动计算 live_txn_order 的 price
func NewOrder(goods livegoods.Goods, buyerID, orderExpireTime int64, more *MoreInfo) *LiveTxnOrder {
	return &LiveTxnOrder{
		BuyerID:    buyerID,
		SellerID:   goods.GoodsSellerID(),
		GoodsID:    goods.GoodsID(),
		GoodsType:  goods.GoodsType(),
		Attr:       goods.GoodsAttr(),
		Price:      goods.GoodsTotalPrice(),
		Title:      goods.OrderTitle(),
		Status:     StatusCreate,
		ExpireTime: orderExpireTime,
		More:       more,
	}
}

// NewOrderWithNum new LiveTxnOrder
func NewOrderWithNum(goods livegoods.Goods, num int, buyerID, orderExpireTime int64, more *MoreInfo) *LiveTxnOrder {
	return &LiveTxnOrder{
		BuyerID:    buyerID,
		SellerID:   goods.GoodsSellerID(),
		GoodsID:    goods.GoodsID(),
		GoodsType:  goods.GoodsType(),
		Attr:       goods.GoodsAttr(),
		Price:      goods.GoodsTotalPrice() * num,
		Title:      goods.OrderTitle(),
		Status:     StatusCreate,
		ExpireTime: orderExpireTime,
		More:       more,
	}
}

// NewOrderByTransactionLog 通过 transaction_log 生成订单
// transaction_log 中使用 tid, from_id, to_id, status
func NewOrderByTransactionLog(g livegoods.Goods,
	t *transactionlog.TransactionLog, more *MoreInfo) *LiveTxnOrder {
	return &LiveTxnOrder{
		BuyerID:   t.FromID,
		SellerID:  t.ToID,
		TID:       t.ID,
		GoodsID:   g.GoodsID(),
		GoodsType: g.GoodsType(),
		Attr:      g.GoodsAttr(),
		Price:     g.GoodsTotalPrice(),
		Title:     g.OrderTitle(),
		Status:    t.Status, // NOTICE: 目前 transaction_log 和 live_txn_order 的该字段枚举值一致
		More:      more,
	}
}

// SetSuperFanOrderExpired update super fan order expired
func SetSuperFanOrderExpired(userIDs []int64, creatorID int64) error {
	now := goutil.TimeNow().Unix()
	err := LiveTxnOrder{}.DB().
		Where("buyer_id IN (?) AND seller_id = ? AND goods_type = ?", userIDs, creatorID, livegoods.GoodsTypeSuperFan).
		Where("status = ? AND expire_time > ?", StatusSuccess, now).
		Update(map[string]interface{}{
			"expire_time":   now,
			"modified_time": now,
		}).Error

	return err
}

// RoomSuperFanHistory 房间内开通/续费超粉的历史消息记录 (startTime <= 订单时间 < endTime)
func RoomSuperFanHistory(creatorID int64, startTime, endTime time.Time, limit int64) ([]*LiveTxnOrder, error) {
	resp := make([]*LiveTxnOrder, 0)
	db := LiveTxnOrder{}.DB().Where("goods_type = ? AND status = ?", livegoods.GoodsTypeSuperFan, StatusSuccess).
		Where("seller_id = ?", creatorID).Order("create_time ASC")
	db = setTimeFilter(db, startTime, endTime)
	if limit > 0 {
		db.Limit(limit)
	}
	err := db.Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RoomRevenueHistory 房间内开通/续费超粉、发送付费弹幕的记录 (startTime <= 订单时间 < endTime)
// NOTICE: 当前仅包含超粉、付费弹幕、宝盒
func RoomRevenueHistory(creatorID int64, startTime, endTime time.Time) ([]*LiveTxnOrder, error) {
	var resp []*LiveTxnOrder
	db := LiveTxnOrder{}.DB().Where("goods_type IN (?) AND status = ?",
		[]int{livegoods.GoodsTypeSuperFan, livegoods.GoodsTypeDanmaku, livegoods.GoodsTypeLuckyBox}, StatusSuccess).
		Where("seller_id = ?", creatorID).Order("create_time ASC")
	db = setTimeFilter(db, startTime, endTime)
	err := db.Find(&resp).Error
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func setTimeFilter(db *gorm.DB, startTime, endTime time.Time) *gorm.DB {
	return db.
		Where("create_time >= ? AND create_time < ?", startTime.Unix(), endTime.Unix())
}

// CountHistoryOrderByGoodsIDWithUserID 查询特定类型的指定商品历史购买记录
// TODO: 包内没有单元测试
func CountHistoryOrderByGoodsIDWithUserID(buyerID, goodsID int64, startTime, endTime time.Time, goodsType int) (int64, error) {
	var resp int64
	db := LiveTxnOrder{}.DB().
		Where("buyer_id = ? AND status = ?", buyerID, StatusSuccess).
		Where("goods_type = ? AND goods_id = ?", goodsType, goodsID)
	err := setTimeFilter(db, startTime, endTime).Count(&resp).Error
	if err != nil {
		return 0, err
	}
	return resp, nil
}

// CountHistoryOrderByGoodsID 查询特定类型的指定商品历史购买记录
// TODO: 包内没有单元测试
func CountHistoryOrderByGoodsID(goodsID int64, startTime, endTime time.Time, goodsType int) (int64, error) {
	var resp int64
	db := LiveTxnOrder{}.DB().
		Where("status = ?", StatusSuccess).
		Where("goods_type = ? AND goods_id = ?", goodsType, goodsID)
	err := setTimeFilter(db, startTime, endTime).Count(&resp).Error
	if err != nil {
		return 0, err
	}
	return resp, nil
}

// BuyerHistoryListByGoodsType 获取购买人购买某类商品的记录
func BuyerHistoryListByGoodsType(buyerID int64, goodsType, p, pageSize int64) ([]*LiveTxnOrder, goutil.Pagination, error) {
	db := LiveTxnOrder{}.DB().
		Where("buyer_id = ? AND status = ?", buyerID, StatusSuccess).
		Where("goods_type = ?", goodsType)
	if goodsType == livegoods.GoodsTypeGashapon {
		// WORKAROUND: 魔盒需要过滤掉重启前的订单记录
		db = db.Where("create_time > ?", time.Date(2025, 1, 1, 0, 0, 0, 0, time.Local).Unix())
	}
	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}

	resp := make([]*LiveTxnOrder, 0)
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return resp, pa, nil
	}

	err = pa.ApplyTo(db.Order("id DESC")).Find(&resp).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	return resp, pa, err
}

// FindLatestSuperFanRegisterOrder 查询最新的超粉开通订单
func FindLatestSuperFanRegisterOrder(buyerID, sellerID int64) (*LiveTxnOrder, error) {
	var order LiveTxnOrder
	err := LiveTxnOrder{}.DB().
		Where("buyer_id = ? AND seller_id = ? AND status = ?", buyerID, sellerID, StatusSuccess).
		Where("goods_type = ? AND attr = ?", livegoods.GoodsTypeSuperFan, livegoods.AttrSuperFanRegister).
		Order("create_time DESC").Take(&order).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &order, nil
}

// FindLatestSuperFanRegisterMap 根据购买者 ID 查询生效的超粉购买记录
func FindLatestSuperFanRegisterMap(buyerID int64, sellerIDs []int64) (map[int64]LiveTxnOrder, error) {
	var orders []LiveTxnOrder
	err := LiveTxnOrder{}.DB().
		Select("seller_id, MAX(create_time) AS create_time").
		Where("buyer_id = ? AND seller_id IN (?) AND status = ?", buyerID, sellerIDs, StatusSuccess).
		Where("goods_type = ? AND attr = ?", livegoods.GoodsTypeSuperFan, livegoods.AttrSuperFanRegister).
		Group("seller_id").Find(&orders).Error
	if err != nil {
		return nil, err
	}

	orderMap := goutil.ToMap(orders, "SellerID").(map[int64]LiveTxnOrder)
	return orderMap, nil
}
