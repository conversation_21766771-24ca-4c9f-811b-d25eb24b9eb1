package livetxnorder

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSuperFanTxnOrderTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(SuperFanTxnOrder{}, "id", "create_time", "expire_time",
		"title", "price", "status", "tid", "goods_id", "attr", "buyer_id", "seller_id")
}

func TestSuperFanTxnOrderGoodsTitle(t *testing.T) {
	assert := assert.New(t)

	sto := SuperFanTxnOrder{
		Num:  9,
		Attr: livegoods.AttrSuperFanRegister,
	}
	assert.Equal("开通直播超粉--9 个月", sto.GoodsTitle())
	sto.Num = 52
	sto.Attr = livegoods.AttrSuperFanRenew
	assert.Equal("续费直播超粉--52 个月", sto.GoodsTitle())
}

func TestRoomSuperFanByPage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, pa, err := RoomSuperFanByPage(10, 100, 100)
	require.NoError(err)
	assert.Empty(res)
	assert.False(pa.Valid())

	res, pa, err = RoomSuperFanByPage(10, 1, 20, goutil.TimeNow())
	require.NoError(err)
	assert.Empty(res)

	res, pa, err = RoomSuperFanByPage(10, 1, 20, time.Unix(0, 0), goutil.TimeNow())
	require.NoError(err)
	assert.NotEmpty(res)

	res, pa, err = RoomSuperFanByPage(10, 1, 20)
	require.NoError(err)
	require.NotEmpty(res)
	assert.NotZero(res[0].Num)
	assert.True(pa.Valid())
}

func TestIsFirstBuySuperFan(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testBuyerID := int64(999999)

	err := LiveTxnOrder{}.DB().Delete("", "buyer_id = ?", testBuyerID).Error
	require.NoError(err)
	isFirst, err := IsFirstBuySuperFan(testBuyerID)
	require.NoError(err)
	assert.True(isFirst)

	err = LiveTxnOrder{}.DB().Create(&LiveTxnOrder{
		Status:    StatusSuccess,
		GoodsType: livegoods.GoodsTypeSuperFan,
		BuyerID:   testBuyerID,
	}).Error
	require.NoError(err)
	isFirst, err = IsFirstBuySuperFan(testBuyerID)
	require.NoError(err)
	assert.False(isFirst)
}

func TestIsPurchasedSuperFanInRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testCreatorID int64 = 111
		testBuyerID   int64 = 222
		testGoodsID   int64 = 333
	)

	err := LiveTxnOrder{}.DB().Delete("", "buyer_id = ? AND goods_id = ?", testBuyerID, testGoodsID).Error
	require.NoError(err)
	isPurchased, err := IsPurchasedSuperFanInRoom(testCreatorID, testBuyerID, testGoodsID)
	require.NoError(err)
	assert.False(isPurchased)

	err = LiveTxnOrder{}.DB().Create(&LiveTxnOrder{
		Status:    StatusSuccess,
		GoodsType: livegoods.GoodsTypeSuperFan,
		SellerID:  testCreatorID,
		BuyerID:   testBuyerID,
		GoodsID:   testGoodsID,
	}).Error
	require.NoError(err)
	isPurchased, err = IsPurchasedSuperFanInRoom(testCreatorID, testBuyerID, testGoodsID)
	require.NoError(err)
	assert.True(isPurchased)

	isPurchased, err = IsPurchasedSuperFanInRoom(-123, testBuyerID, testGoodsID)
	require.NoError(err)
	assert.False(isPurchased)
}
