package livetxnorder

import (
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestLiveTxnOrderTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveTxnOrder{}, "id", "create_time", "modified_time", "expire_time",
		"title", "price", "status", "tid", "goods_id", "goods_type", "attr",
		"buyer_id", "seller_id", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MoreInfo{}, "open_status", "bubble", "num", "gifts", "guaranteed_gift_id", "danmaku_message")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("live_txn_order", LiveTxnOrder{}.TableName())
}

func TestADBTableName(t *testing.T) {
	assert := assert.New(t)
	os.Setenv(util.EnvDeploy, "")
	assert.Equal(TableName(), ADBTableName())
	os.Setenv(util.EnvDeploy, util.DeployEnvProd)
	assert.Equal("live."+TableName(), ADBTableName())
}

func TestADB(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int64
	os.Setenv(util.EnvDeploy, "")
	require.NoError(ADB().Count(&count).Error)
	assert.NotZero(count)
}

type testGoods struct{}

func (tg testGoods) GoodsID() int64 {
	return 999
}

func (tg testGoods) OrderTitle() string {
	return "测试商品"
}

func (tg testGoods) GoodsSellerID() int64 {
	return 888
}

func (tg testGoods) GoodsType() int {
	return 777
}

func (tg testGoods) GoodsAttr() int {
	return 666
}

func (tg testGoods) GoodsNum() int {
	return 444
}

func (tg testGoods) GoodsTotalPrice() int {
	return 333
}

func TestNewOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nowStamp := goutil.TimeNow().Unix()
	var goods testGoods
	order := NewOrder(goods, 1000, nowStamp, nil)
	require.NotNil(order)
	assert.Equal(int64(1000), order.BuyerID)
	assert.Equal(goods.GoodsSellerID(), order.SellerID)
	assert.Equal(goods.GoodsID(), order.GoodsID)
	assert.Equal(goods.GoodsType(), order.GoodsType)
	assert.Equal(goods.GoodsAttr(), order.Attr)
	assert.Equal(goods.GoodsTotalPrice(), order.Price)
	assert.Equal(goods.OrderTitle(), order.Title)
	assert.Equal(StatusCreate, order.Status)
	assert.Equal(nowStamp, order.ExpireTime)
}

func TestNewOrderWithNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	nowStamp := goutil.TimeNow().Unix()
	var goods testGoods
	order := NewOrderWithNum(goods, 2, 1000, nowStamp, nil)
	require.NotNil(order)
	assert.Equal(int64(1000), order.BuyerID)
	assert.Equal(goods.GoodsSellerID(), order.SellerID)
	assert.Equal(goods.GoodsID(), order.GoodsID)
	assert.Equal(goods.GoodsType(), order.GoodsType)
	assert.Equal(goods.GoodsAttr(), order.Attr)
	assert.Equal(goods.GoodsTotalPrice()*2, order.Price)
	assert.Equal(goods.OrderTitle(), order.Title)
	assert.Equal(StatusCreate, order.Status)
	assert.Equal(nowStamp, order.ExpireTime)
}

func TestNewOrderByTransactionLog(t *testing.T) {
	assert := assert.New(t)

	var g testGoods
	tl := &transactionlog.TransactionLog{
		ID:     12347,
		FromID: 123,
		ToID:   456,
		Status: transactionlog.StatusSuccess,
	}
	more := &MoreInfo{
		Gifts:            []*Gift{{GiftID: 1, Num: 2}, {GiftID: 2, Num: 3}},
		GuaranteedGiftID: 2,
	}
	o := NewOrderByTransactionLog(g, tl, more)
	assert.Equal(tl.FromID, o.BuyerID)
	assert.Equal(tl.ToID, o.SellerID)
	assert.Equal(tl.ID, o.TID)
	assert.Equal(g.GoodsID(), o.GoodsID)
	assert.Equal(g.GoodsType(), o.GoodsType)
	assert.Equal(g.GoodsAttr(), o.Attr)
	assert.Equal(g.GoodsTotalPrice(), o.Price)
	assert.Equal(g.OrderTitle(), o.Title)
	assert.Equal(more, o.More)
}

func TestSetSuperFanOrderExpired(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(333)
	err := SetSuperFanOrderExpired([]int64{testUserID}, testUserID)
	require.NoError(err)
	var superFanOrder LiveTxnOrder
	err = superFanOrder.DB().Select("id, expire_time").
		Where("buyer_id = ? AND seller_id = ?", testUserID, testUserID).
		Where("goods_type = ?", livegoods.GoodsTypeSuperFan).
		Order("id DESC").Take(&superFanOrder).Error
	require.NoError(err)
	assert.LessOrEqual(superFanOrder.ExpireTime, goutil.TimeNow().Unix())

	var superFanOrder2 LiveTxnOrder
	err = superFanOrder.DB().Select("id, expire_time").
		Where("id = ?", 11).Take(&superFanOrder2).Error
	require.NoError(err)
	assert.EqualValues(1, superFanOrder2.ExpireTime)
}

func TestRoomSuperFanHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testCreatorID := int64(10)
	res, err := RoomSuperFanHistory(testCreatorID, time.Unix(0, 0), time.Unix(2, 0), 0)
	require.NoError(err)
	assert.Empty(res)

	res, err = RoomSuperFanHistory(testCreatorID, time.Unix(0, 0), time.Unix(4, 0), 0)
	require.NoError(err)
	require.Len(res, 3)
	require.NotEmpty(res[0].More)
	require.NotNil(res[0].More.Bubble)
	assert.Equal("test", res[0].More.Bubble.Type)
	require.NotNil(res[1].More)
	require.Nil(res[1].More.Bubble)

	res, err = RoomSuperFanHistory(99999, time.Unix(0, 0), time.Unix(4, 0), 0)
	require.NoError(err)
	assert.Empty(res)
}

func TestBuyerHistoryListByGoodsType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 12
	orders, _, err := BuyerHistoryListByGoodsType(testUserID, livegoods.GoodsTypeSuperFan, 1, 20)
	require.NoError(err)
	assert.NotEmpty(orders)

	orders, _, err = BuyerHistoryListByGoodsType(testUserID, livegoods.GoodsTypeSuperFan, 2, 20)
	require.NoError(err)
	assert.Empty(orders)

	require.NoError(LiveTxnOrder{}.DB().
		Where("goods_type = ? AND buyer_id = ?", livegoods.GoodsTypeGashapon, testUserID).
		Delete(LiveTxnOrder{}).Error)

	orders, _, err = BuyerHistoryListByGoodsType(testUserID, livegoods.GoodsTypeGashapon, 1, 20)
	require.NoError(err)
	assert.Empty(orders)

	order := &LiveTxnOrder{
		BuyerID:   testUserID,
		GoodsID:   1,
		GoodsType: livegoods.GoodsTypeGashapon,
		Price:     100,
		Title:     "测试魔方",
		Status:    StatusSuccess,
		More:      nil,
	}
	err = LiveTxnOrder{}.DB().Create(&order).Error
	require.NoError(err)
	orders, _, err = BuyerHistoryListByGoodsType(testUserID, livegoods.GoodsTypeGashapon, 1, 20)
	require.NoError(err)
	assert.NotEmpty(orders)
}

func TestRoomRevenueHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := RoomRevenueHistory(-1, time.Unix(0, 0), time.Unix(2, 0))
	require.NoError(err)
	assert.Empty(res)

	res, err = RoomRevenueHistory(12, time.Unix(0, 0), time.Unix(2, 0))
	require.NoError(err)
	assert.NotEmpty(res)
}

func TestCountHistoryOrderByGoodsIDWithUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testUserID = 1300
	const testGoodsID = 4
	order := &LiveTxnOrder{
		BuyerID:   testUserID,
		GoodsID:   testGoodsID,
		GoodsType: livegoods.GoodsTypeFukubukuro,
		Price:     100,
		Title:     "测试商品",
		Status:    StatusSuccess,
		More:      nil,
	}
	err := LiveTxnOrder{}.DB().Create(&order).Error
	require.NoError(err)
	defer func() {
		assert.NoError(LiveTxnOrder{}.DB().
			Where("goods_id = ? AND buyer_id = ?", testGoodsID, testUserID).
			Delete(LiveTxnOrder{}).Error)
	}()

	now := goutil.TimeNow()
	beginOfToday := util.BeginningOfDay(now)
	nextDayFromToday := beginOfToday.AddDate(0, 0, 1)

	count, err := CountHistoryOrderByGoodsIDWithUserID(
		testUserID, testGoodsID, beginOfToday, nextDayFromToday, livegoods.GoodsTypeFukubukuro)
	require.NoError(err)
	assert.Equal(int64(1), count)
}

func TestCountHistoryOrderByGoodsID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testUserID = 1301
	const testGoodsID = 3
	order := &LiveTxnOrder{
		BuyerID:   testUserID,
		GoodsID:   testGoodsID,
		GoodsType: livegoods.GoodsTypeFukubukuro,
		Price:     100,
		Title:     "测试商品",
		Status:    StatusSuccess,
		More:      nil,
	}
	err := LiveTxnOrder{}.DB().Create(&order).Error
	require.NoError(err)
	defer func() {
		assert.NoError(LiveTxnOrder{}.DB().
			Where("goods_id = ? AND buyer_id = ?", testGoodsID, testUserID).
			Delete(LiveTxnOrder{}).Error)
	}()

	now := goutil.TimeNow()
	beginOfToday := util.BeginningOfDay(now)
	nextDayFromToday := beginOfToday.AddDate(0, 0, 1)

	count, err := CountHistoryOrderByGoodsID(
		testGoodsID, beginOfToday, nextDayFromToday, livegoods.GoodsTypeFukubukuro)
	require.NoError(err)
	assert.Equal(int64(1), count)
}

func TestFindLatestSuperFanRegisterOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	order, err := FindLatestSuperFanRegisterOrder(9074509, 9074510)
	require.NoError(err)
	require.NotNil(order)
	assert.EqualValues(17, order.ID)
}

func TestFindLatestSuperFanRegisterMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	orderMap, err := FindLatestSuperFanRegisterMap(9074509, []int64{9074510, 9074511})
	require.NoError(err)
	assert.Equal(2, len(orderMap))
}
