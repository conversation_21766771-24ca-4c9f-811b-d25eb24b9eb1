package guildedithistory

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "guild_edit_history"

// TableName table name
func TableName() string {
	return tableName
}

// GuildEditHistory 公会更改历史表
type GuildEditHistory struct {
	ID                        int64  `gorm:"column:id;primary_key"`
	CreateTime                int64  `gorm:"column:create_time"`
	ModifiedTime              int64  `gorm:"column:modified_time"`
	GuildID                   int64  `gorm:"column:guild_id"`
	OperatorID                int64  `gorm:"column:operator_id"`
	Name                      string `gorm:"column:name"`
	Intro                     string `gorm:"column:intro"`
	OwnerName                 string `gorm:"column:owner_name" encrypt:"guild_create_time"`
	OwnerIDNumber             string `gorm:"column:owner_id_number" encrypt:"guild_create_time"`
	OwnerIDPeople             string `gorm:"column:owner_id_people"`
	OwnerBackcover            string `gorm:"column:owner_backcover"`
	Mobile                    string `gorm:"column:mobile" encrypt:"guild_create_time"`
	Email                     string `gorm:"column:email" encrypt:"guild_create_time"`
	QQ                        string `gorm:"column:qq"` // TODO: QQ 之后需要调整为加密
	CorporationName           string `gorm:"column:corporation_name" encrypt:"guild_create_time"`
	CorporationAddress        string `gorm:"column:corporation_address" encrypt:"guild_create_time"`
	CorporationPhone          string `gorm:"column:corporation_phone" encrypt:"guild_create_time"`
	BusinessLicenseNumber     string `gorm:"column:business_license_number" encrypt:"guild_create_time"`
	BusinessLicenseFrontcover string `gorm:"column:business_license_frontcover"`
	TaxAccount                string `gorm:"column:tax_account" encrypt:"guild_create_time"`
	BankAccount               string `gorm:"column:bank_account" encrypt:"guild_create_time"`
	BankAccountName           string `gorm:"column:bank_account_name"`
	Bank                      string `gorm:"column:bank"`
	BankAddress               string `gorm:"column:bank_address"`
	BankBranch                string `gorm:"column:bank_branch"`
	InvoiceRate               int    `gorm:"column:invoice_rate;default:-2"`
	Type                      int    `gorm:"column:type"`
	Checked                   int    `gorm:"column:checked"`
	UserID                    int64  `gorm:"column:user_id"`
	ApplyTime                 int64  `gorm:"column:apply_time"`
	GuildCreateTime           int64  `gorm:"column:guild_create_time"`
}

// NewGuildEditHistory new GuildEditHistory g 是加密后的公会数据
func NewGuildEditHistory(g guild.Guild, uid int64) *GuildEditHistory {
	nowStamp := goutil.TimeNow().Unix()
	return &GuildEditHistory{
		GuildID:                   g.ID,
		OperatorID:                uid,
		Name:                      g.Name,
		Intro:                     g.Intro,
		OwnerName:                 g.OwnerName,
		OwnerIDNumber:             g.OwnerIDNumber,
		OwnerIDPeople:             g.OwnerIDPeople,
		OwnerBackcover:            g.OwnerBackcover,
		Mobile:                    g.Mobile,
		Email:                     g.Email,
		QQ:                        g.QQ,
		CorporationName:           g.CorporationName,
		CorporationAddress:        g.CorporationAddress,
		CorporationPhone:          g.CorporationPhone,
		BusinessLicenseNumber:     g.BusinessLicenseNumber,
		BusinessLicenseFrontcover: g.BusinessLicenseFrontcover,
		TaxAccount:                g.TaxAccount,
		BankAccount:               g.BankAccount,
		BankAccountName:           g.BankAccountName,
		Bank:                      g.Bank,
		BankAddress:               g.BankAddress,
		BankBranch:                g.BankBranch,
		InvoiceRate:               g.InvoiceRate,
		Type:                      g.Type,
		Checked:                   g.Checked,
		UserID:                    g.UserID,
		ApplyTime:                 g.ApplyTime,
		GuildCreateTime:           g.CreateTime,
		CreateTime:                nowStamp,
		ModifiedTime:              nowStamp,
	}
}

// DB the db instance of GuildEditHistory model
func (geh GuildEditHistory) DB() *gorm.DB {
	return service.LiveDB.Table(geh.TableName())
}

// TableName provides table name
func (GuildEditHistory) TableName() string {
	return tableName
}

// BeforeSave gorm hook
func (geh *GuildEditHistory) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if geh.DB().NewRecord(geh) {
		geh.CreateTime = nowTime
	}
	geh.ModifiedTime = nowTime
	return
}
