package guildedithistory

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestGuildEditHistoryTags(t *testing.T) {
	tutil.NewKeyChecker(t, tutil.GORM).Check(GuildEditHistory{},
		"id", "guild_id", "operator_id", "name", "intro", "mobile", "email", "qq", "owner_name", "owner_id_number", "owner_backcover",
		"corporation_name", "corporation_address", "corporation_phone", "business_license_number", "tax_account", "business_license_frontcover",
		"bank_account", "bank_account_name", "bank", "bank_address", "bank_branch", "invoice_rate", "guild_create_time", "apply_time",
		"create_time", "modified_time", "checked", "user_id", "type", "owner_id_people")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("guild_edit_history", TableName())
	assert.Equal("guild_edit_history", GuildEditHistory{}.TableName())
}

func TestNewGuildEditHistory(t *testing.T) {
	assert := assert.New(t)
	nowStamp := goutil.TimeNow().Unix()
	g := guild.Guild{
		ID:            6,
		Name:          "测试转会 1",
		Intro:         "测试公会 1",
		OwnerName:     "钢铁侠",
		OwnerIDNumber: "123456789012345678",
		Mobile:        "***********",
		Bank:          "测试银行",
		CreateTime:    nowStamp - 1,
		ModifiedTime:  nowStamp - 1,
		Type:          guild.TypeUnencrypted,
		Checked:       guild.CheckedPass,
		UserID:        111,
	}
	uid := int64(11)
	geh := NewGuildEditHistory(g, uid)
	assert.Equal(g.ID, geh.GuildID)
	assert.Equal(g.CreateTime, geh.GuildCreateTime)
	assert.Equal(nowStamp, geh.CreateTime)
	assert.Equal(nowStamp, geh.ModifiedTime)
	assert.Equal(g.Name, geh.Name)
	assert.Equal(g.Intro, geh.Intro)
	assert.Equal(g.OwnerName, geh.OwnerName)
	assert.Equal(g.OwnerIDNumber, geh.OwnerIDNumber)
	assert.Equal(g.Mobile, geh.Mobile)
	assert.Equal(g.Bank, geh.Bank)
	assert.Equal(g.UserID, geh.UserID)
	assert.Equal(g.Type, geh.Type)
	assert.Equal(g.Checked, geh.Checked)
	assert.Equal(uid, geh.OperatorID)
}
