package msound

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const testSoundID = int64(44809)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestSimpleTag(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)

	keys := strings.Split(simpleSelect, ", ")
	kc.Check(Simple{}, keys...)
}

func TestFindSimpleList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	list, err := FindSimpleList(nil)
	require.NoError(err)
	assert.Nil(list)

	list, err = FindSimpleList([]int64{testSoundID})
	require.NoError(err)
	require.Len(list, 1)
	assert.Equal(1, strings.Count(list[0].SoundURL, "http"))
	assert.NotZero(list[0].Duration)
}

func TestBuildSoundURL(t *testing.T) {
	assert := assert.New(t)

	// 测试非协议地址的获取音频完整地址
	sound := Simple{
		SoundURL: "MP3/201202/05/test.mp3",
	}
	sound.buildSoundURL()
	assert.Equal(config.Conf.Service.Storage["oss"].PublicURL+"MP3/201202/05/test.mp3", sound.SoundURL)

	// 测试协议地址获取完整音频地址
	sound.SoundURL = "sound://aod/201202/05/test.m4a"
	sound.buildSoundURL()
	assert.Equal(config.Conf.Service.Storage["sound"].PublicURL+"aod/201202/05/test.m4a", sound.SoundURL)
}

func TestIsNoLiveRecommend(t *testing.T) {
	assert := assert.New(t)

	s := Simple{Refined: 128}
	assert.True(s.IsNoLiveRecommend())
	s.Refined = 1
	assert.False(s.IsNoLiveRecommend())
}

func TestFindSimple(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	s, err := FindSimple(testSoundID)
	require.NoError(err)
	require.NotNil(s)
	s, err = FindSimple(123456)
	require.NoError(err)
	assert.Nil(s)
}
