package msound

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
)

// Source Type
const (
	StatusOriginal = 1 // 原创
)

// checked Type
// -3：转码失败；-2：配音未转码；-1：未转码；0：审核中；1：已审核通过；2：报警音；3：下架音
const (
	CheckedApproved = 1 // 已审核通过
)

// 音频属性
const (
	RefinedNoLiveRecommend = 1 << 7 // 播放页屏蔽全部直播推荐
)

// 音频类型
const (
	TypeLive = 3 // 直播回放
)

const simpleSelect = "id, catalog_id, duration, user_id, soundurl_64, checked, refined, type"

// Simple structure for m_sound
type Simple struct {
	ID        int64  `gorm:"column:id"`
	CatalogID int64  `gorm:"column:catalog_id"`
	Duration  int64  `gorm:"column:duration"`
	UserID    int64  `gorm:"column:user_id"`
	SoundURL  string `gorm:"column:soundurl_64"`
	Checked   int    `gorm:"column:checked"`
	Refined   int    `gorm:"column:refined"`
	Type      int    `gorm:"column:type"`
}

// TableName table name
func (Simple) TableName() string {
	return "m_sound"
}

// TODO: 改成 AfterFind 钩子
func (s *Simple) buildSoundURL() {
	s.SoundURL = storage.ParseSchemeURL(s.SoundURL)
}

// IsNoLiveRecommend 是否屏蔽播放页全部直播推荐
func (s *Simple) IsNoLiveRecommend() bool {
	return (s.Refined & RefinedNoLiveRecommend) != 0
}

// FindSimpleList 通过 soundIDs 查找简单音频信息
func FindSimpleList(soundIDs []int64) (sounds []*Simple, err error) {
	if len(soundIDs) == 0 {
		return nil, nil
	}
	err = service.DB.Select(simpleSelect).
		Where("id IN (?)", soundIDs).Find(&sounds).Error
	if err != nil {
		return nil, err
	}
	buildSoundURL(sounds)
	return sounds, nil
}

func buildSoundURL(sounds []*Simple) {
	for i := range sounds {
		sounds[i].buildSoundURL()
	}
}

// FindSimple 单个音频简单信息
func FindSimple(soundID int64) (*Simple, error) {
	var s Simple
	err := service.DB.Select(simpleSelect).
		First(&s, "id = ?", soundID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	s.buildSoundURL()
	return &s, nil
}
