package recommendedexposurelevel

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// constants for status
const (
	StatusDisabled = iota // 禁用
	StatusEnabled         // 启用
)

// constants for scene
const (
	SceneHome = iota + 1 // 首页
	SceneLive            // 直播页
)

// TableName table name
func TableName() string {
	return "m_recommended_exposure_level"
}

// Model of table m_recommended_exposure_level
type Model struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Exposure     int64  `gorm:"column:exposure"`
	Level        string `gorm:"column:level"`
	Status       int    `gorm:"column:status"`
	Scene        int    `gorm:"column:scene"`
}

// TableName of Model
func (Model) TableName() string {
	return TableName()
}

// FindEnabledByID finds the enabled level record by id
// Returns the record if found and enabled, nil otherwise
// Returns error if database operation fails
func FindEnabledByID(id int64, scene int) (*Model, error) {
	var model Model
	err := service.DB.Where("id = ? AND scene = ? AND status = ?", id, scene, StatusEnabled).
		First(&model).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}

	return &model, nil
}
