package recommendedexposurelevel

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	// 测试 TableName 函数
	assert.Equal("m_recommended_exposure_level", TableName())

	// 测试 Model 的 TableName 方法
	model := Model{}
	assert.Equal("m_recommended_exposure_level", model.TableName())
}

func TestFindEnabledByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试查找启用的记录
	model, err := FindEnabledByID(2, SceneLive)
	require.NoError(err)
	require.NotNil(model)
	assert.EqualValues(2, model.ID)
	assert.EqualValues(StatusEnabled, model.Status)
	assert.Equal("S", model.Level)

	// 测试查找禁用的记录
	model, err = FindEnabledByID(9, SceneLive)
	require.NoError(err)
	assert.Nil(model, "禁用的记录应该返回 nil")

	// 测试查找不存在的记录
	model, err = FindEnabledByID(10000, SceneHome)
	require.NoError(err)
	assert.Nil(model, "不存在的记录应该返回 nil")
}
