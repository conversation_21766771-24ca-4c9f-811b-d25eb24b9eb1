package liveuserspecialredpacket

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LiveUserSpecialRedPacket 用户特殊红包
type LiveUserSpecialRedPacket struct {
	ID           int64 `gorm:"column:id;primary_key"`
	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	UserID    int64 `gorm:"column:user_id"`    // 用户 ID
	GoodsID   int64 `gorm:"column:goods_id"`   // 红包所属商品 ID，live_goods 表主键
	GainNum   int64 `gorm:"column:gain_num"`   // 初始发放的数量
	Num       int64 `gorm:"column:num"`        // 当前剩余数量
	StartTime int64 `gorm:"column:start_time"` // 生效开始时间点。单位：秒
	EndTime   int64 `gorm:"column:end_time"`   // 失效时间点，当前时间小于 end_time 时，红包未过期，为 0 时代表永久生效。单位：秒
}

// TableName for current model
func TableName() string {
	return "live_user_special_red_packet"
}

// TableName for LiveUserSpecialRedPacket
func (LiveUserSpecialRedPacket) TableName() string {
	return TableName()
}

// BeforeCreate hook
func (lr *LiveUserSpecialRedPacket) BeforeCreate() error {
	nowUnix := goutil.TimeNow().Unix()
	lr.CreateTime = nowUnix
	lr.ModifiedTime = nowUnix
	return nil
}

// BeforeUpdate hook
func (lr *LiveUserSpecialRedPacket) BeforeUpdate() error {
	lr.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// DB the db instance of this model
func (lr LiveUserSpecialRedPacket) DB() *gorm.DB {
	return service.LiveDB.Table(TableName())
}

// TimeLeft 返回当前红包剩余的生效时长，单位：秒
func (lr LiveUserSpecialRedPacket) TimeLeft() int64 {
	return max(lr.EndTime-goutil.TimeNow().Unix(), 0)
}

// AddUserSpecialRedPacket 为用户添加特殊红包
func AddUserSpecialRedPacket(userID, goodsID, num, endTime int64) error {
	now := goutil.TimeNow()
	userRedPacket := LiveUserSpecialRedPacket{
		UserID:    userID,
		GoodsID:   goodsID,
		GainNum:   num,
		Num:       num,
		StartTime: now.Unix(),
		EndTime:   endTime,
	}
	return userRedPacket.DB().Create(&userRedPacket).Error
}

// ListUserValidRedPacket 查询用户生效的（可发送）红包
func ListUserValidRedPacket(userID int64, goodsIDs []int64) ([]*LiveUserSpecialRedPacket, error) {
	now := goutil.TimeNow().Unix()
	var redPackets []*LiveUserSpecialRedPacket
	err := LiveUserSpecialRedPacket{}.DB().
		Where("user_id = ? AND goods_id IN (?) AND num > 0 AND start_time <= ? AND (? < end_time OR end_time = 0)",
			userID, goodsIDs, now, now).
		Order("end_time ASC").
		Find(&redPackets).Error
	if err != nil {
		return nil, err
	}
	return redPackets, nil
}

// FindUserValidSpecialRedPacket 查询用户指定生效中的（可发送）红包记录
func FindUserValidSpecialRedPacket(userID int64, goodsID int64) (*LiveUserSpecialRedPacket, error) {
	now := goutil.TimeNow().Unix()
	var lr LiveUserSpecialRedPacket

	sql := fmt.Sprintf(`
		SELECT * FROM (
			SELECT *
			FROM %s
			WHERE user_id = ? AND goods_id = ? AND num > 0 AND start_time <= ? AND ? < end_time
			ORDER BY end_time LIMIT 1
		) t1
		UNION
		SELECT * FROM (
			SELECT *
			FROM %s
			WHERE user_id = ? AND goods_id = ? AND num > 0 AND start_time <= ? AND end_time = 0
			LIMIT 1
		) t2
		ORDER BY end_time DESC
	`, TableName(), TableName())

	err := LiveUserSpecialRedPacket{}.DB().
		Raw(sql, userID, goodsID, now, now, userID, goodsID, now).
		Take(&lr).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &lr, nil
}

// DeductNum 扣减红包，返回是否扣减成功
func DeductNum(id int64) (bool, error) {
	nowUnix := goutil.TimeNow().Unix()
	db := service.LiveDB.Model(&LiveUserSpecialRedPacket{}).
		Where("id = ? AND num > 0 AND start_time <= ? AND (? < end_time OR end_time = 0)", id, nowUnix, nowUnix).
		Update("num", gorm.Expr("num - 1"))
	if db.Error != nil {
		return false, db.Error
	}
	return db.RowsAffected > 0, nil
}
