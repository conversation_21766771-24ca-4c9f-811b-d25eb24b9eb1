package liveuserspecialredpacket

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestTagKeys(t *testing.T) {
	lr := LiveUserSpecialRedPacket{}
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(lr, "id", "create_time", "modified_time", "user_id", "goods_id", "gain_num", "num",
		"start_time", "end_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_user_special_red_packet", TableName())
}

func TestLiveUserSpecialRedPacket_TimeLeft(t *testing.T) {
	assert := assert.New(t)

	// 测试有剩余生效时间时
	lr := LiveUserSpecialRedPacket{
		EndTime: goutil.TimeNow().Add(time.Minute).Unix(),
	}
	assert.EqualValues(60, lr.TimeLeft())

	// 测试无剩余生效时间时
	lr.EndTime = goutil.TimeNow().Add(-time.Minute).Unix()
	assert.Zero(lr.TimeLeft())
}

func TestAddUserSpecialRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rp := LiveUserSpecialRedPacket{
		UserID:  1919810,
		GoodsID: 1818910,
	}
	require.NoError(LiveUserSpecialRedPacket{}.DB().Delete("", "user_id = ?", rp.UserID).Error)
	require.NoError(AddUserSpecialRedPacket(rp.UserID, rp.GoodsID, 0, goutil.TimeNow().Unix()))
	var rp2 LiveUserSpecialRedPacket
	require.NoError(LiveUserSpecialRedPacket{}.DB().Take(&rp2, "user_id = ?", rp.UserID).Error)
	require.NotNil(rp2)
	assert.Equal(rp.GoodsID, rp2.GoodsID)
}

func TestListUserValidRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户无特殊红包的情况
	testUserID := int64(2334564)
	testGoodsID := int64(45646564)
	require.NoError(LiveUserSpecialRedPacket{}.DB().Delete("", "user_id = ?", testUserID).Error)
	redPackets, err := ListUserValidRedPacket(testUserID, []int64{testGoodsID})
	require.NoError(err)
	assert.Empty(redPackets)

	// 测试用户有特殊红包的情况
	now := goutil.TimeNow().Unix()
	lr := &LiveUserSpecialRedPacket{
		UserID:    testUserID,
		GoodsID:   testGoodsID,
		GainNum:   3,
		Num:       1,
		StartTime: now - 60,
		EndTime:   now + 60,
	}
	require.NoError(lr.DB().Create(lr).Error)
	redPackets, err = ListUserValidRedPacket(testUserID, []int64{testGoodsID})
	require.NoError(err)
	require.Len(redPackets, 1)
	assert.Equal(lr.ID, redPackets[0].ID)

	// 测试用户有特殊红包，但是次数已用尽的情况
	err = lr.DB().Where("user_id = ?", testUserID).Update(map[string]interface{}{
		"num": 0,
	}).Error
	require.NoError(err)
	redPackets, err = ListUserValidRedPacket(testUserID, []int64{testGoodsID})
	require.NoError(err)
	assert.Empty(redPackets)

	// 测试用户红包不在有效期的情况
	err = lr.DB().Where("user_id = ?", testUserID).Update(map[string]interface{}{
		"num":      1,
		"end_time": now,
	}).Error
	require.NoError(err)
	redPackets, err = ListUserValidRedPacket(testUserID, []int64{testGoodsID})
	require.NoError(err)
	assert.Empty(redPackets)

	// 测试用户红包永久生效的情况
	err = lr.DB().Where("user_id = ?", testUserID).Update(map[string]interface{}{
		"num":      1,
		"end_time": 0,
	}).Error
	require.NoError(err)
	redPackets, err = ListUserValidRedPacket(testUserID, []int64{testGoodsID})
	require.NoError(err)
	require.Len(redPackets, 1)
	assert.Equal(lr.ID, redPackets[0].ID)
}

func TestFindUserValidSpecialRedPacket(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试当前红包不可用
	testUserID, testGoodsID := int64(900), int64(18)
	rRedPacket, err := FindUserValidSpecialRedPacket(testUserID, testGoodsID)
	require.NoError(err)
	assert.Nil(rRedPacket)

	// 测试当前红包可用
	testUserID, testGoodsID = int64(901), int64(19)
	rRedPacket, err = FindUserValidSpecialRedPacket(testUserID, testGoodsID)
	require.NoError(err)
	// 找到过期时间最近的一条记录
	assert.EqualValues(4, rRedPacket.ID)
}

func TestDeductNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试当前红包可用
	testID := int64(4)
	result, err := DeductNum(testID)
	require.NoError(err)
	assert.True(result)
	var num int64
	err = LiveUserSpecialRedPacket{}.DB().Select("num").
		Where("id = ?", testID).
		Row().Scan(&num)
	require.NoError(err)
	assert.Zero(num)

	// 测试当前红包不可用
	result, err = DeductNum(testID)
	require.NoError(err)
	assert.False(result)
	err = LiveUserSpecialRedPacket{}.DB().Select("num").
		Where("id = ?", testID).
		Row().Scan(&num)
	require.NoError(err)
	assert.Zero(num)
}
