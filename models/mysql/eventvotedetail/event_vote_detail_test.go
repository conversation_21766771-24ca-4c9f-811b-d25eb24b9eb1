package eventvotedetail

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestGetVoteTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	time, err := FindVoteTime(105, 473927)
	require.NoError(err)
	assert.Equal(int64(1582624076), time)
}
