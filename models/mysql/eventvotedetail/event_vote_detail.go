package eventvotedetail

import (
	"database/sql"

	"github.com/MiaoSiLa/live-service/service"
)

const tableName = "event_vote_detail"

// EventVoteDetail 记录用户报名投票时间
type EventVoteDetail struct {
	ID      int64  `gorm:"column:id;primary_key"` // 主键 ID
	UserID  int64  `gorm:"column:user_id"`        // 用户 ID
	EID     int64  `gorm:"column:eid"`            // 资源 ID
	EventID int64  `gorm:"column:event_id"`       // 活动 ID
	Time    int64  `gorm:"column:time"`           // 投票时间
	IP      string `gorm:"column:ip"`             // 用户 IP
	ENV     int8   `gorm:"column:env"`            // 1.Web 2.安卓 3.iOS 4.手机网页
}

// TableName table name
func (e EventVoteDetail) TableName() string {
	return tableName
}

// FindVoteTime 获取报名投票时间
func FindVoteTime(eventID, userID int64) (int64, error) {
	var time int64
	err := service.DB.Table(tableName).Select("time").Where("user_id = ? AND event_id = ?", userID, eventID).Row().Scan(&time)
	if err != nil && err != sql.ErrNoRows {
		return 0, err
	}
	return time, nil
}
