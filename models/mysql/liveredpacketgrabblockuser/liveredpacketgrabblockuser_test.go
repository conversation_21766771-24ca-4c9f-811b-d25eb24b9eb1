package liveredpacketgrabblockuser

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const testUserID = 233

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(RedpacketGrabBlockUser{}, "id", "create_time", "modified_time", "user_id")
}

func TestRedpacketGrabBlockUser_TableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("live_redpacket_grab_block_user", tableName)
	assert.Equal("live_redpacket_grab_block_user", RedpacketGrabBlockUser{}.TableName())
}

func TestGetAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试缓存中没数据，从数据库中获取
	key := keys.LocalKeyRedpacketGrabBlockUser0.Format()
	service.Cache5Min.Delete(key)
	r := &RedpacketGrabBlockUser{
		UserID: testUserID,
	}
	require.NoError(r.DB().Delete("", "user_id = ?", testUserID).Error)
	require.NoError(r.DB().Create(r).Error)
	res, err := getAll()
	require.NoError(err)
	assert.Len(res, 1)
	_, ok := res[testUserID]
	assert.True(ok)

	// 测试缓存中有数据
	require.NoError(r.DB().Delete("", "user_id = ?", testUserID).Error)
	res, err = getAll()
	require.NoError(err)
	assert.Len(res, 1)
	_, ok = res[testUserID]
	assert.True(ok)
}

func TestIsBlocked(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户不在抢红包黑名单中
	key := keys.LocalKeyRedpacketGrabBlockUser0.Format()
	service.Cache5Min.Delete(key)
	r := &RedpacketGrabBlockUser{
		UserID: testUserID,
	}
	require.NoError(r.DB().Delete("", "user_id = ?", testUserID).Error)

	isBlocked, err := IsBlocked(testUserID)
	require.NoError(err)
	assert.False(isBlocked)

	// 测试用户在抢红包黑名单中
	require.NoError(r.DB().Create(r).Error)
	service.Cache5Min.Delete(key)
	isBlocked, err = IsBlocked(testUserID)
	require.NoError(err)
	assert.True(isBlocked)
}
