package liveredpacketgrabblockuser

import (
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
)

// TODO: 迁移使用 live_application 表, 与福袋共用 ScopeUserDrawReceivedPrize

const tableName = "live_redpacket_grab_block_user"

// RedpacketGrabBlockUser 抢红包用户黑名单
type RedpacketGrabBlockUser struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键 ID
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间。单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 修改时间。单位：秒
	UserID       int64 `gorm:"column:user_id"`        // 用户 ID
}

// TableName of model "live_redpacket_grab_block_user"
func (RedpacketGrabBlockUser) TableName() string {
	return tableName
}

// DB the db instance of RedpacketGrabBlockUser model
func (r RedpacketGrabBlockUser) DB() *gorm.DB {
	return service.LiveDB.Table(r.TableName())
}

// getAll 返回黑名单中所有用户
func getAll() (map[int64]struct{}, error) {
	key := keys.LocalKeyRedpacketGrabBlockUser0.Format()
	cached, ok := service.Cache5Min.Get(key)
	if ok {
		return cached.(map[int64]struct{}), nil
	}
	var blockUserIDs []int64
	err := RedpacketGrabBlockUser{}.DB().Pluck("user_id", &blockUserIDs).Error
	if err != nil {
		return nil, err
	}
	blockUserIDMap := make(map[int64]struct{}, len(blockUserIDs))
	for _, userID := range blockUserIDs {
		blockUserIDMap[userID] = struct{}{}
	}
	var d time.Duration
	if len(blockUserIDMap) == 0 {
		// 若数据库中没有数据缓存空数据一分钟
		d = time.Minute
	}
	service.Cache5Min.Set(key, blockUserIDMap, d)
	return blockUserIDMap, nil
}

// IsBlocked 用户是否在抢红包黑名单中
func IsBlocked(userID int64) (bool, error) {
	blockUserIDMap, err := getAll()
	if err != nil {
		return false, err
	}
	_, isBlocked := blockUserIDMap[userID]
	return isBlocked, nil
}
