package accountinfo

import (
	"strconv"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 账户类型
const (
	TypeAlipay = 0 // 支付宝账户
	TypeBank   = 1 // 银行账户
)

// StatusConfirmed 信息状态：已确认
const StatusConfirmed = 1

// AccountInfo model for table app_missevan.account_info
type AccountInfo struct {
	ID     int64 `gorm:"column:id;primary_key" json:"id"`
	UserID int64 `gorm:"column:user_id" json:"user_id"`

	RealName string `gorm:"column:real_name" json:"real_name"`
	Account  string `gorm:"column:account" json:"account"`
	Mobile   string `gorm:"column:mobile" json:"mobile"`
	IDNumber string `gorm:"column:id_number" json:"id_number"`

	Bank        string `gorm:"column:bank" json:"bank"`
	BankBranch  string `gorm:"column:bank_branch" json:"bank_branch"`
	BankAccount string `gorm:"column:bank_account" json:"bank_account"`
	Status      int    `gorm:"column:status" json:"status"`
	Type        int    `gorm:"column:type" json:"type"`

	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
}

// DB the db instance of AccountInfo model
func (AccountInfo) DB() *gorm.DB {
	return service.PayDB.Table(AccountInfo{}.TableName())
}

// TableName for current model
func (AccountInfo) TableName() string {
	return "account_info"
}

// DecryptedBankAccount get decrypted bank account
func (ai *AccountInfo) DecryptedBankAccount() (bankAcc string, err error) {
	iv := strconv.FormatInt(ai.CreateTime, 10)
	bankAcc, err = goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, ai.BankAccount)
	if err != nil {
		return
	}
	return
}

// DecryptedIDNumber get decrypted id number
func (ai *AccountInfo) DecryptedIDNumber() (idNumber string, err error) {
	iv := strconv.FormatInt(ai.CreateTime, 10)
	idNumber, err = goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, ai.IDNumber)
	if err != nil {
		return
	}
	return
}

// DecryptedMobile get decrypted mobile
func (ai *AccountInfo) DecryptedMobile() (mobile string, err error) {
	iv := strconv.FormatInt(ai.CreateTime, 10)
	mobile, err = goutil.Decrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, ai.Mobile)
	if err != nil {
		return
	}
	return
}

// MosaicRealName get mosaic real name
func (ai *AccountInfo) MosaicRealName() string {
	return goutil.MosaicString(ai.RealName, goutil.MosaicRealName)
}

// MosaicMobile get mosaic mobile
func (ai *AccountInfo) MosaicMobile() string {
	mobile, err := ai.DecryptedMobile()
	if err != nil {
		logger.Error(err)
		return ""
	}

	return goutil.MosaicString(mobile, goutil.MosaicPhoneNumber)
}

// MosaicBankAccount get mosaic bank account
func (ai *AccountInfo) MosaicBankAccount() string {
	bankAcc, err := ai.DecryptedBankAccount()
	if err != nil {
		logger.Error(err)
		return ""
	}

	return goutil.MosaicString(bankAcc, goutil.MosaicBankCardNumber)
}

// MosaicIDNumber get mosaic id number
func (ai *AccountInfo) MosaicIDNumber() string {
	idNumber, err := ai.DecryptedIDNumber()
	if err != nil {
		logger.Error(err)
		return ""
	}

	return goutil.MosaicString(idNumber, goutil.MosaicIDNumber)
}
