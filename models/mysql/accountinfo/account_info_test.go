package accountinfo

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestDecryptedBankAccount(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	timeStamp := goutil.TimeNow().Unix()
	bankAccount := "**********"

	iv := strconv.FormatInt(timeStamp, 10)
	acc.CreateTime = timeStamp
	acc.BankAccount = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, bankAccount)

	bankAccountDecrypted, err := acc.DecryptedBankAccount()
	require.NoError(err)
	require.Equal(bankAccount, bankAccountDecrypted)
}

func TestDecryptedIDNumber(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	timeStamp := goutil.TimeNow().Unix()
	idNumber := "123456789012345678"

	iv := strconv.FormatInt(timeStamp, 10)
	acc.CreateTime = timeStamp
	acc.IDNumber = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, idNumber)

	idNumberDecrypted, err := acc.DecryptedIDNumber()
	require.NoError(err)
	require.Equal(idNumber, idNumberDecrypted)
}

func TestDecryptedMobile(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	mobile := "***********"

	timeStamp := goutil.TimeNow().Unix()
	iv := strconv.FormatInt(timeStamp, 10)
	acc.CreateTime = timeStamp
	acc.Mobile = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, mobile)

	mobileDecrypted, err := acc.DecryptedMobile()
	require.NoError(err)
	require.Equal(mobile, mobileDecrypted)
}

func TestMosaicBankAccount(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	timeStamp := goutil.TimeNow().Unix()
	bankAccount := "**********"

	iv := strconv.FormatInt(timeStamp, 10)
	acc.CreateTime = timeStamp
	acc.BankAccount = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, bankAccount)

	bankAccountMosaic := acc.MosaicBankAccount()
	require.Equal(goutil.MosaicString(bankAccount, goutil.MosaicBankCardNumber), bankAccountMosaic)
}

func TestMosaicIDNumber(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	timeStamp := goutil.TimeNow().Unix()
	idNumber := "123456789012345678"

	iv := strconv.FormatInt(timeStamp, 10)
	acc.CreateTime = timeStamp
	acc.IDNumber = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, idNumber)

	idNumberMosaic := acc.MosaicIDNumber()
	require.Equal(goutil.MosaicString(idNumber, goutil.MosaicIDNumber), idNumberMosaic)
}

func TestMosaicRealName(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	acc.RealName = "realname12345"
	require.Equal(goutil.MosaicString(acc.RealName, goutil.MosaicRealName), acc.MosaicRealName())
}

func TestMosaicMobile(t *testing.T) {
	require := require.New(t)

	acc := new(AccountInfo)
	mobile := "***********"

	timeStamp := goutil.TimeNow().Unix()
	iv := strconv.FormatInt(timeStamp, 10)
	acc.CreateTime = timeStamp
	acc.Mobile = goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, mobile)

	require.Equal(goutil.MosaicString(mobile, goutil.MosaicPhoneNumber), acc.MosaicMobile())
}
