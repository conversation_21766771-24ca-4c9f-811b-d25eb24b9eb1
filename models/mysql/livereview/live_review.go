package livereview

import (
	"encoding/json"
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// image type
const (
	TypeCover = iota
	TypeBackground
	TypeName
	TypeScheduleRecommend
	typeCount
)

var typeStr = [4]string{
	TypeCover:             "直播封面",
	TypeBackground:        "直播背景图",
	TypeName:              "直播间名称",
	TypeScheduleRecommend: "首页推荐位推荐图",
}

// TypeStr 图片类型到文案的映射
func TypeStr(imageType int) string {
	return typeStr[imageType]
}

// image status
const (
	StatusOutdated = iota - 1
	StatusReviewing
	StatusPassed
	StatusRefused
)

// sort
const (
	RoomIDAsc      = "room_id ASC"
	RoomIDDesc     = "room_id DESC"
	UserIDAsc      = "user_id ASC"
	UserIDDesc     = "user_id DESC"
	UploadTimeAsc  = "upload_time ASC"
	UploadTimeDesc = "upload_time DESC"
)

var (
	validTypes = [3]int{TypeCover, TypeBackground, TypeName}
	validSorts = [6]string{RoomIDAsc, RoomIDDesc, UserIDAsc, UserIDDesc, UploadTimeAsc, UploadTimeDesc}
)

// JOIN 缩写成 r
// live_review AS r
const tableName = "live_review"

// TableName table name
func TableName() string {
	return tableName
}

// FindOptions Find 选项
type FindOptions struct {
	Sort     string
	RoomID   int64
	UserID   int64
	Username string
}

// Image 审核图片的格式
type Image struct {
	Opacity *float64 `gorm:"-" json:"opacity,omitempty"`
	URL     string   `gorm:"-" json:"image_url"`
}

// ReviewInfo 审核信息
type ReviewInfo struct {
	Opacity  *float64 `gorm:"-" json:"opacity,omitempty"`
	ImageURL string   `gorm:"-" json:"image_url,omitempty"`

	Name string `gorm:"-" json:"name,omitempty"`

	Type int `gorm:"-" json:"-"` // 目前仅用于批量提交审核
}

// LiveReview 图片审核表
type LiveReview struct {
	ID int64 `gorm:"column:id;primary_key" json:"-"`

	UserID  int64 `gorm:"column:user_id" json:"user_id"`
	RoomID  int64 `gorm:"column:room_id" json:"room_id"`
	GuildID int64 `gorm:"column:guild_id" json:"-"`

	UploadTime int64 `gorm:"column:upload_time" json:"upload_time"`
	Type       int   `gorm:"column:type" json:"type"`
	Status     int   `gorm:"column:status" json:"status"`

	Info string `gorm:"column:info" json:"-"`

	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`

	ReviewInfo
}

// IsErrorType 是否是错误的 type
func IsErrorType(reviewType int) error {
	if TypeValid(reviewType) {
		return nil
	}
	return fmt.Errorf("unsupported type: %d", reviewType)
}

// BeforeSave gorm hook BeforeSave
// NOTICE: 使用 LiveReview 而不是 *LiveReview 是不会调用这些钩子函数的
func (lr *LiveReview) BeforeSave() (err error) {
	if err = IsErrorType(lr.Type); err != nil {
		return
	}
	lr.Info, err = reviewInfoToString(lr.ReviewInfo)
	return
}

// BeforeCreate gorm hook BeforeCreate
func (lr *LiveReview) BeforeCreate() error {
	lr.CreateTime = goutil.TimeNow().Unix()
	lr.ModifiedTime = lr.CreateTime
	return nil
}

// BeforeUpdate gorm hook BeforeUpdate
func (lr *LiveReview) BeforeUpdate() error {
	lr.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// AfterFind gorm hook AfterFind
func (lr *LiveReview) AfterFind() (err error) {
	if !TypeValid(lr.Type) {
		logger.Warnf("unsupported type: %d", lr.Type)
	}
	if lr.Info != "" {
		err = json.Unmarshal([]byte(lr.Info), &lr.ReviewInfo)
	}
	return err
}

// Add add new live review
func (lr *LiveReview) Add() error {
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Table(TableName()).Where("user_id = ? AND guild_id = ? AND status = ? AND type = ?",
			lr.UserID, lr.GuildID, StatusReviewing, lr.Type).Updates(
			map[string]interface{}{
				"modified_time": goutil.TimeNow().Unix(),
				"status":        StatusOutdated,
			}).Error
		if err != nil {
			return err
		}
		return tx.Create(&lr).Error
	})
	return err
}

// LogKeywords 管理员日志关键词
func (lr LiveReview) LogKeywords() (keywordStatus, keywordType, keywordInfo string) {
	switch lr.Status {
	case StatusPassed:
		keywordStatus = "通过"
	case StatusRefused:
		keywordStatus = "拒绝"
	}
	keywordType = TypeStr(lr.Type)
	if lr.Type == TypeName {
		keywordInfo = lr.Name
		return
	}
	keywordInfo = storage.ParseSchemeURL(lr.ImageURL)
	return
}

func reviewInfoToString(v ReviewInfo) (string, error) {
	b, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

// WithUserInfo LiveReview with user info
type WithUserInfo struct {
	LiveReview
	Username string `gorm:"column:username" json:"username"`
}

// TableName table name
func (LiveReview) TableName() string {
	return TableName()
}

// TypeValid imageType 是否有效
func TypeValid(imageType int) bool {
	return TypeCover <= imageType && imageType < typeCount
}

// FindReviewing 查找审核中的结果
// 默认创建时间倒序排序
func FindReviewing(imageType int, p, pageSize int64, opt FindOptions) ([]*WithUserInfo, goutil.Pagination, error) {
	db := service.DB.Table(TableName()+" AS r").
		Joins(fmt.Sprintf("LEFT JOIN %s AS u ON u.id = r.user_id", mowangskuser.TableName())).Select("u.username, r.*").
		Where("r.type = ? AND r.status = ?", imageType, StatusReviewing)
	db = addOrder(db, true, opt.Sort)
	if opt.UserID != 0 {
		db = db.Where("r.user_id = ?", opt.UserID)
	}
	if opt.RoomID != 0 {
		db = db.Where("r.room_id = ?", opt.RoomID)
	}
	if opt.Username != "" {
		db = db.Where("u.username LIKE ?", servicedb.ToLikeStr(opt.Username))
	}
	var count int64
	err := db.Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return []*WithUserInfo{}, pa, nil
	}
	var res []*WithUserInfo
	db = pa.ApplyTo(db).Find(&res)
	for i := 0; i < len(res); i++ {
		err = res[i].AfterFind() // 防止内嵌结构导致不调用 hooks
		if err != nil {
			return nil, pa, err
		}
		res[i].SchemeToURL()
	}
	return res, pa, db.Error
}

// FindRoomReviewing 查找直播间审核中的信息
// NOTICE: 未将 scheme url 转换成 http url
func FindRoomReviewing(roomID int64) (images [3]*ReviewInfo /* type 作为下标 */, err error) {
	var reviewing []LiveReview
	err = service.DB.Select("type, info").Order("upload_time DESC").
		Find(&reviewing, "type IN (?) AND status = ? AND room_id = ?",
			validTypes[:], StatusReviewing, roomID).Error
	if err != nil {
		return
	}
	if len(reviewing) > typeCount {
		logger.WithField("room_id", roomID).Error("too many reviewing records")
	}

	for i, count := 0, 0; i < len(reviewing) && count < len(validTypes); i++ {
		if images[reviewing[i].Type] == nil {
			images[reviewing[i].Type] = &reviewing[i].ReviewInfo
			count++
		}
	}
	return
}

func addOrder(db *gorm.DB, joined bool, sorts ...string) *gorm.DB {
	valids := make([]string, 0, len(sorts))
	prefix := ""
	if joined {
		prefix = "r."
	}
	for i := 0; i < len(sorts); i++ {
		for j := 0; j < len(validSorts); j++ {
			if sorts[i] == validSorts[j] {
				valids = append(valids, prefix+sorts[i])
				break
			}
		}
	}
	if len(valids) == 0 {
		valids = []string{prefix + UploadTimeDesc}
	}
	for i := 0; i < len(valids); i++ {
		db = db.Order(valids[i])
	}
	return db
}

// SchemeToURL scheme to http url
func (ri *ReviewInfo) SchemeToURL() {
	ri.ImageURL = storage.ParseSchemeURL(ri.ImageURL)
}

// URLToScheme http url to scheme
func (ri *ReviewInfo) URLToScheme() bool {
	imageURL, ok := service.Storage.Format(ri.ImageURL)
	if ok {
		ri.ImageURL = imageURL
	}
	return ok
}

type submitRecord struct {
	UserID int64 `gorm:"column:user_id" json:"user_id"`
	RoomID int64 `gorm:"column:room_id" json:"room_id"`

	Type   int `gorm:"column:type" json:"type"`
	Status int `gorm:"column:status" json:"status"`

	Info string `gorm:"column:info" json:"-"`

	UploadTime   int64 `gorm:"column:upload_time" json:"upload_time"`
	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
}

// Submit 提交审核
func Submit(roomID, userID int64, infos []ReviewInfo) error {
	now := goutil.TimeNow()
	types := make([]int, len(infos))
	inserts := make([]submitRecord, 0, len(infos))
	for i := range infos {
		err := IsErrorType(infos[i].Type)
		if err != nil {
			return err
		}
		types[i] = infos[i].Type
		s := submitRecord{
			RoomID:       roomID,
			UserID:       userID,
			Type:         infos[i].Type,
			UploadTime:   now.Unix(),
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
		}
		s.Info, err = reviewInfoToString(infos[i])
		if err != nil {
			return err
		}
		inserts = append(inserts, s)
	}
	return servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Table(TableName()).Where("user_id = ? AND status = ? AND type IN (?)",
			userID, StatusReviewing, types).Updates(
			map[string]interface{}{
				"modified_time": now.Unix(),
				"status":        StatusOutdated,
			}).Error
		if err != nil {
			return err
		}
		return helper.BatchInsert(tx, TableName(), inserts)
	})
}

// FinishReviewing 尝试将某用户审核中的信息通过或拒绝
// after == nil 说明没有找到可以更改的待审核信息
func FinishReviewing(userID, uploadTime int64, reviewType int, passOrRefuse int, tx *gorm.DB) (after *LiveReview, err error) {
	if passOrRefuse != StatusPassed && passOrRefuse != StatusRefused {
		logger.Errorf("wrong input status: %d", passOrRefuse)
		return
	}
	doTx := func(tx *gorm.DB) error {
		after = new(LiveReview)
		innerErr := addOrder(tx, false, UploadTimeDesc).
			First(after, "type = ? AND status = ? AND user_id = ? AND upload_time = ?",
				reviewType, StatusReviewing, userID, uploadTime).Error
		if innerErr != nil {
			if gorm.IsRecordNotFoundError(innerErr) {
				innerErr = nil
			}
			after = nil
			return innerErr
		}
		after.Status = passOrRefuse
		afterDB := tx.Model(after).Where("status = ?", StatusReviewing).Updates(after)
		if innerErr = afterDB.Error; innerErr != nil {
			return innerErr
		}
		if afterDB.RowsAffected == 0 {
			after = nil
			return nil
		}
		return nil
	}

	if tx == nil {
		err = servicedb.Tx(service.DB, doTx)
	} else {
		err = doTx(tx)
	}
	return
}

// FindGuildRoomReviewing 查找公会直播间审核中的信息
func FindGuildRoomReviewing(guildID, roomID int64, reviewType int) (*LiveReview, error) {
	var reviewing LiveReview
	err := service.DB.Where("type = ? AND status = ? AND guild_id = ? AND room_id = ?",
		reviewType, StatusReviewing, guildID, roomID).First(&reviewing).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return &reviewing, nil
}

// ListGuildRoomReviewing 批量查找公会直播间审核中的信息
func ListGuildRoomReviewing(guildID int64, roomIDs []int64, reviewType int) ([]*LiveReview, error) {
	var reviews []*LiveReview
	err := service.DB.Where("type = ? AND status = ? AND guild_id = ? AND room_id IN (?)",
		reviewType, StatusReviewing, guildID, roomIDs).Find(&reviews).Error
	if err != nil {
		return nil, err
	}
	return reviews, nil
}
