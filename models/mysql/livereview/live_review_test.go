package livereview

import (
	"testing"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTypeStr(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("直播封面", TypeStr(TypeCover))
	assert.Equal("直播背景图", TypeStr(TypeBackground))
	assert.Equal("直播间名称", TypeStr(TypeName))
}

func TestTableName(t *testing.T) {
	assert.Equal(t, "live_review", TableName())
	assert.Equal(t, "live_review", LiveReview{}.TableName())
}

func TestTypeValid(t *testing.T) {
	assert := assert.New(t)
	for i := 0; i < len(validTypes); i++ {
		assert.True(TypeValid(i))
	}
	assert.False(TypeValid(-1))
}

func TestIsErrorType(t *testing.T) {
	assert := assert.New(t)
	for i := 0; i < len(validTypes); i++ {
		assert.Nil(IsErrorType(i))
	}
	assert.EqualError(IsErrorType(-1), "unsupported type: -1")
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveReview{}, "id", "user_id", "room_id", "guild_id", "upload_time", "type", "status", "info", "create_time", "modified_time")
	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(LiveReview{}, "user_id", "room_id", "status", "type", "upload_time")
	kc.Check(Image{}, "opacity", "image_url")
}

func TestLiveReviewAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lr := &LiveReview{
		UserID:     1,
		RoomID:     2,
		GuildID:    2021070118,
		UploadTime: 4,
		Type:       TypeScheduleRecommend,
		Status:     StatusReviewing,
	}
	require.NoError(lr.Add())
	assert.NotEmpty(lr.ID)
	assert.NotEmpty(lr.CreateTime)
}

func TestFindReviewing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	opt := FindOptions{UserID: 14, RoomID: 123, Username: "零月"}
	res, pa, err := FindReviewing(TypeCover, 1, 10, opt)
	require.NoError(err)
	assert.Equal(int64(1), pa.Count)
	require.Len(res, 1)
	assert.Equal(int64(14), res[0].UserID)
	assert.Equal(int64(123), res[0].RoomID)
	assert.Equal("零月2", res[0].Username)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", res[0].ImageURL)
	_, _, err = FindReviewing(TypeCover, 0, 0, opt)
	assert.NoError(err)
}

func TestFindRoomReviewing(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	reviews, err := FindRoomReviewing(123)
	require.NoError(err)
	r := reviews[TypeCover]
	assert.Equal("oss://avatars/icon01.png", r.ImageURL)
	r = reviews[TypeBackground]
	require.NotNil(r)
	assert.Equal("oss://avatars/icon01.png", r.ImageURL)
	r = reviews[TypeName]
	assert.Equal("测试直播审核", r.Name)
}

func TestAddOrder(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var res []LiveReview
	require.NoError(addOrder(service.DB, false, UploadTimeDesc).Find(&res).Error)
	require.GreaterOrEqual(len(res), 2)
	assert.Greater(res[0].UploadTime, res[1].UploadTime)

	db := service.DB.Select("r.user_id").Table(TableName() + " AS r").Where("id = 1").Joins("LEFT JOIN user AS u ON u.id = r.user_id")
	db = addOrder(db, true, "empty")
	assert.Equal(gorm.Expr("SELECT r.user_id FROM live_review AS r "+
		"LEFT JOIN user AS u ON u.id = r.user_id WHERE (id = 1) ORDER BY r.upload_time DESC"),
		db.QueryExpr())
}

func TestWithUserInfoSchemeToURL(t *testing.T) {
	assert := assert.New(t)
	r := &WithUserInfo{}
	r.ImageURL = "oss://test.png"
	r.SchemeToURL()
	assert.Equal("https://static-test.missevan.com/test.png", r.ImageURL)
	r.URLToScheme()
	assert.Equal("oss://test.png", r.ImageURL)
	r.ImageURL = ""
	assert.False(r.URLToScheme())
	assert.Equal("", r.ImageURL)
}

func TestSubmit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	require.NoError(Submit(1234, 999,
		[]ReviewInfo{
			{Type: TypeCover, ImageURL: "https://static-test.missevan.com/test.png"},
			{Type: TypeBackground, ImageURL: "https://static-test.missevan.com/test.png"},
		}))
	var l []LiveReview
	require.NoError(service.DB.Where("room_id = ? AND status = ? AND user_id = ?",
		1234, StatusReviewing, 999).Find(&l).Error)
	require.Len(l, 2)
	assert.GreaterOrEqual(l[0].UploadTime, now.Unix())
	assert.GreaterOrEqual(l[1].UploadTime, now.Unix())
	assert.EqualError(Submit(1234, 999,
		[]ReviewInfo{
			{Type: -1, ImageURL: "https://static-test.missevan.com/test.png"},
		}), "unsupported type: -1")
}

func TestFinishReviewing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var l LiveReview
	require.NoError(service.DB.Where("user_id = ?", 14).First(&l).Error)
	// 确保是审核封面
	require.Equal(TypeCover, l.Type)
	after, err := FinishReviewing(l.UserID, l.UploadTime, l.Type, StatusRefused, nil)
	require.NoError(err)
	require.NotNil(after)
	assert.Equal(StatusRefused, after.Status)
	assert.NotZero(after.ID)

	// status 不正确
	after, err = FinishReviewing(123, 12340, 0, -2, nil)
	assert.NoError(err)
	assert.Nil(after)
	// 没找到
	after, err = FinishReviewing(123, 12340, 0, StatusPassed, service.DB)
	assert.NoError(err)
	assert.Nil(after)
}

func TestLiveReviewHooks(t *testing.T) {
	assert := assert.New(t)

	l := LiveReview{}
	t.Run("BeforeSave", func(t *testing.T) {
		l.Type = -1
		assert.Error(l.BeforeSave())
		l.Type = TypeName
		l.Name = "test"
		assert.NoError(l.BeforeSave())
		assert.NotEmpty(l.Info)
	})
	t.Run("BeforeCreate", func(t *testing.T) {
		assert.NoError(l.BeforeCreate())
		assert.NotZero(l.CreateTime)
		assert.Equal(l.CreateTime, l.ModifiedTime)
	})
	t.Run("BeforeUpdate", func(t *testing.T) {
		l.ModifiedTime = 0
		assert.NoError(l.BeforeUpdate())
		assert.NotZero(l.ModifiedTime)
	})
	t.Run("AfterFind", func(t *testing.T) {
		l.Type = -1
		assert.NoError(l.AfterFind())
	})
}

func TestLogKeywords(t *testing.T) {
	assert := assert.New(t)

	l := LiveReview{
		Type: TypeCover,
		ReviewInfo: ReviewInfo{
			ImageURL: "oss://test.png",
		},
	}
	ks, kt, ki := l.LogKeywords()
	assert.Empty(ks)
	assert.Equal("直播封面", kt)
	assert.Equal("https://static-test.missevan.com/test.png", ki)

	l.Status = StatusPassed
	l.Type = TypeBackground
	l.ImageURL = "https://test.png"
	ks, kt, ki = l.LogKeywords()
	assert.Equal("通过", ks)
	assert.Equal("直播背景图", kt)
	assert.Equal("https://test.png", ki)

	l.Status = StatusRefused
	l.Type = TypeName
	l.Name = "test name"
	ks, kt, ki = l.LogKeywords()
	assert.Equal("拒绝", ks)
	assert.Equal("直播间名称", kt)
	assert.Equal("test name", ki)
}

func TestFindGuildRoomReviewing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGuildID := int64(99999)
	lr := LiveReview{
		RoomID:  1,
		GuildID: testGuildID,
		Type:    TypeScheduleRecommend,
		Status:  StatusReviewing,
	}
	require.NoError(service.DB.Save(&lr).Error)
	defer func() {
		service.DB.Table(TableName()).Delete("", "guild_id = ?", testGuildID)
	}()

	result, err := FindGuildRoomReviewing(testGuildID, 999, TypeScheduleRecommend)
	require.NoError(err)
	assert.Nil(result)

	result, err = FindGuildRoomReviewing(testGuildID, 1, TypeBackground)
	require.NoError(err)
	assert.Nil(result)

	result, err = FindGuildRoomReviewing(testGuildID, 1, TypeScheduleRecommend)
	require.NoError(err)
	assert.NotNil(result)
}

func TestListGuildRoomReviewing(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testGuildID := int64(99999)
	lr := LiveReview{
		RoomID:  1,
		GuildID: testGuildID,
		Type:    TypeScheduleRecommend,
		Status:  StatusReviewing,
	}
	require.NoError(service.DB.Save(&lr).Error)
	defer func() {
		service.DB.Table(TableName()).Delete("", "guild_id = ?", testGuildID)
	}()

	res, err := ListGuildRoomReviewing(testGuildID, []int64{1}, TypeScheduleRecommend)
	require.Nil(err)
	assert.Len(res, 1)

	res, err = ListGuildRoomReviewing(testGuildID, []int64{1}, TypeBackground)
	require.Nil(err)
	assert.Empty(res)
}
