package liveaddendum

import (
	"errors"
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// MaxVitality 满血
const MaxVitality = 12

// MinVitality 用户元气值的最小值
const MinVitality = 0

// Agreed 同意协议
const Agreed = 1

// agree type
const (
	AgreeGuildAgreement = iota + 1 // 同意公会协议
	AgreeLiveAgreement             // 同意主播协议
)

// TableName 元气值表表名
func TableName() string {
	return "live_addendum"
}

// LiveAddendum 元气值表
// NOTICE: 在新主播开播时插入新的
type LiveAddendum struct {
	ID                      int64          `gorm:"column:id;primary_key"`
	Vitality                int            `gorm:"column:vitality"`
	LastPunishedTime        int64          `gorm:"column:last_punished_time"`
	IsAgreed                goutil.BitMask `gorm:"column:is_agreed"`
	AgreeGuildAgreementTime int64          `gorm:"column:agree_guild_agreement_time"`
	AgreeLiveAgreementTime  int64          `gorm:"column:agree_live_agreement_time"`

	CreateTime   int64 `gorm:"column:create_time"`
	ModifiedTime int64 `gorm:"column:modified_time"`

	UserID int64 `gorm:"column:user_id"`
}

// LiveVitality 用户元气值
type LiveVitality struct {
	Vitality int   `gorm:"column:vitality"`
	UserID   int64 `gorm:"column:user_id"`
}

// TableName tablename
func (LiveAddendum) TableName() string {
	return TableName()
}

// DB the db instance of LiveAddendum model
func (l LiveAddendum) DB() *gorm.DB {
	return service.DB.Table(l.TableName())
}

// BeforeCreate automatically set field modified_time
func (l *LiveAddendum) BeforeCreate() error {
	l.CreateTime = goutil.TimeNow().Unix()
	l.ModifiedTime = l.CreateTime
	l.Vitality = MaxVitality
	return nil
}

// BeforeUpdate automatically update field modified_time
func (l *LiveAddendum) BeforeUpdate() error {
	l.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// SetUserID 设置用户 ID
func (l *LiveAddendum) SetUserID(userID int64) {
	l.ID = userID
	l.UserID = userID
}

// Find 查询某主播的 live_addendum
func Find(userID int64) (*LiveAddendum, error) {
	var la LiveAddendum
	err := service.DB.First(&la, "user_id = ?", userID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return &la, nil
}

// Punish 惩罚主播
func Punish(creatorID int64, operator int, vitalityChange uint, reason string) (*LiveAddendum, error) {
	la := new(LiveAddendum)
	err := service.DB.Find(&la, "user_id = ?", creatorID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	l := &Log{
		CreatorID:      creatorID,
		Operator:       operator,
		VitalityChange: vitalityChange,
		Reason:         reason,
		CreateTime:     goutil.TimeNow().Unix(),
	}
	la.LastPunishedTime = l.CreateTime
	la.ModifiedTime = l.CreateTime
	switch operator {
	case OperatorSub:
		la.Vitality -= int(vitalityChange)
		if la.Vitality < MinVitality {
			la.Vitality = MinVitality
		}
	case OperatorCut, OperatorBan:
		// 切断直播，封禁不修改元气值
		l.VitalityChange = 0
	default:
		logger.Warnf("unsupported operator: %d", operator)
		return nil, errors.New("unsupported operator")
	}
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Table(la.TableName()).Where("user_id = ?", la.UserID).
			Updates(map[string]interface{}{
				"vitality":           gorm.Expr(servicedb.IFExpr("vitality > ?", "vitality - ?", "?"), MinVitality+vitalityChange, vitalityChange, MinVitality),
				"last_punished_time": l.CreateTime,
				"modified_time":      l.CreateTime,
			}).Error
		if err != nil {
			return err
		}
		return tx.Save(l).Error
	})
	if err != nil {
		return nil, err
	}
	return la, nil
}

// BanPunish 封禁直播间惩罚主播
func BanPunish(creatorID int64, reason, intro string) (*LiveAddendum, error) {
	la := new(LiveAddendum)
	err := service.DB.Find(&la, "user_id = ?", creatorID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	l := &Log{
		CreatorID:      creatorID,
		Operator:       OperatorBan,
		VitalityChange: 0,
		Reason:         reason,
		Intro:          intro,
		CreateTime:     goutil.TimeNow().Unix(),
	}
	la.LastPunishedTime = l.CreateTime
	la.ModifiedTime = l.CreateTime

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Table(la.TableName()).Where("user_id = ?", la.UserID).
			Updates(map[string]interface{}{
				"last_punished_time": l.CreateTime,
				"modified_time":      l.CreateTime,
			}).Error
		if err != nil {
			return err
		}
		return tx.Save(l).Error
	})
	if err != nil {
		return nil, err
	}
	return la, nil
}

// IncreaseVitality 增加元气值
func IncreaseVitality(creatorID int64, vitalityChange uint) (*LiveAddendum, error) {
	la := new(LiveAddendum)
	err := service.DB.Find(&la, "user_id = ?", creatorID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	l := &Log{
		CreatorID:      creatorID,
		Operator:       OperatorAdd,
		VitalityChange: vitalityChange,
		Reason:         fmt.Sprintf("主播的申诉已通过，恢复 %d 分元气值", vitalityChange),
		CreateTime:     goutil.TimeNow().Unix(),
	}
	la.ModifiedTime = l.CreateTime
	la.Vitality += int(vitalityChange)
	if la.Vitality > MaxVitality {
		la.Vitality = MaxVitality
	}
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Table(la.TableName()).Where("user_id = ?", la.UserID).
			Updates(map[string]interface{}{
				"vitality":      gorm.Expr(servicedb.IFExpr("vitality + ? < ?", "vitality + ?", "?"), vitalityChange, MaxVitality, vitalityChange, MaxVitality),
				"modified_time": l.CreateTime,
			}).Error
		if err != nil {
			return err
		}
		return tx.Save(l).Error
	})
	if err != nil {
		return nil, err
	}
	return la, nil
}

// Vitality 用户的元气值，返回 nil 说明该用户没有元气值
func Vitality(userID int64) (*int, error) {
	var la LiveAddendum
	err := service.DB.Select("vitality").Find(&la, "user_id = ?", userID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return nil, err
	}
	return &la.Vitality, nil
}

// FindVitalityMap 查询用户元气值，返回 map[userID]int
func FindVitalityMap(userIDs []int64) (map[int64]int /* map[userID]Vitality */, error) {
	if len(userIDs) == 0 {
		return map[int64]int{}, nil
	}

	userIDs = util.Uniq(userIDs)
	var liveVitalities []*LiveVitality
	err := service.DB.Table(TableName()).Select("user_id, vitality").
		Find(&liveVitalities, "user_id IN (?)", userIDs).Error
	if err != nil {
		return nil, err
	}
	if len(liveVitalities) == 0 {
		return map[int64]int{}, nil
	}

	m := make(map[int64]int)
	for _, v := range liveVitalities {
		m[v.UserID] = v.Vitality
	}
	return m, nil
}

// Agree 同意协议
func Agree(userID int64, agree int) error {
	var la LiveAddendum
	err := la.DB().Take(&la, "user_id = ?", userID).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return err
	}
	now := goutil.TimeNow().Unix()
	// 即使之前同意过，也更新同意时间
	switch agree {
	case AgreeGuildAgreement:
		la.AgreeGuildAgreementTime = now
	case AgreeLiveAgreement:
		la.AgreeLiveAgreementTime = now
	default:
		panic(fmt.Sprintf("unsupported agree: %d", agree))
	}
	la.IsAgreed.Set(agree)
	if err != nil {
		// 未找到的情况
		la.SetUserID(userID)
		err = service.DB.Create(&la).Error
		if err == nil || !servicedb.IsUniqueError(err) {
			return err
		}
		// 发生唯一索引错误时需要继续执行下面更新的逻辑
		logger.WithField("user_id", userID).Warn(err)
	}
	update := map[string]interface{}{
		"is_agreed":     la.IsAgreed,
		"modified_time": now,
	}
	switch agree {
	case AgreeGuildAgreement:
		update["agree_guild_agreement_time"] = la.AgreeGuildAgreementTime
	case AgreeLiveAgreement:
		update["agree_live_agreement_time"] = la.AgreeLiveAgreementTime
	}
	err = la.DB().Where("user_id = ?", userID).
		Updates(update).Error
	return err
}
