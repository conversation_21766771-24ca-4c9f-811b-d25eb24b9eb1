package liveaddendum

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestListLogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	testUserID := int64(987654321)
	require.NoError(service.DB.Table(LogTableName()).Delete("", "creator_id = ?", testUserID).Error)

	now := goutil.TimeNow()
	ls, pa, err := ListLogs(testUserID, 0, now.Add(-time.Hour), now.Add(time.Minute), 10, 2)
	require.NoError(err)
	assert.NotNil(ls)
	assert.Empty(ls)
	assert.False(pa.Valid())
	log := Log{CreatorID: testUserID, Operator: OperatorBan, Reason: "go test", CreateTime: now.Unix()}
	require.NoError(service.DB.Save(&log).Error)
	ls, _, err = ListLogs(log.CreatorID, 0, now.Add(-time.Hour), now.Add(time.Hour), 1, 10)
	require.NoError(err)
	assert.NotEmpty(ls)

	// 自己查看
	startTime := time.Date(2021, 06, 01, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2021, 06, 07, 0, 0, 0, 0, time.Local)
	ls, _, err = ListLogs(2333666, 0, startTime, endTime, 1, 10)
	require.NoError(err)
	assert.NotEmpty(ls)
	assert.Len(ls, 6)
	assert.Equal("退会了，扣 1 元气", ls[2].Reason)

	// 公会人员查看
	ls, _, err = ListLogs(2333666, 3, startTime, endTime, 1, 10)
	require.NoError(err)
	assert.NotEmpty(ls)
	assert.Len(ls, 4)
	assert.NotEqual("退会了，扣 1 元气", ls[2].Reason)
}

func TestAdminLogInfo(t *testing.T) {
	assert := assert.New(t)

	assert.PanicsWithValue("unsupported operator: 0", func() {
		AdminLogInfo(0, 12)
	})
	intro, catalog := AdminLogInfo(OperatorSub, 12)
	assert.Equal("扣除主播（ID: 12）的元气值", intro)
	assert.Equal(108, catalog)
	intro, catalog = AdminLogInfo(OperatorCut, 13)
	assert.Equal("切断主播（ID: 13）的直播", intro)
	assert.Equal(109, catalog)
	intro, catalog = AdminLogInfo(OperatorBan, 14)
	assert.Equal("封禁主播（ID: 14）的直播间", intro)
	assert.Equal(110, catalog)
}
