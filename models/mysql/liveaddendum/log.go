package liveaddendum

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// operators
const (
	OperatorAdd = 1
	OperatorSub = -1
	OperatorCut = -2
	OperatorBan = -3
)

// LogTableName 日志表表名
func LogTableName() string {
	return "live_addendum_log"
}

// Log 直播间惩罚日志
type Log struct {
	ID int64 `gorm:"column:id;primary_key" json:"-"`

	CreatorID int64 `gorm:"column:creator_id" json:"creator_id"`

	Operator       int    `gorm:"column:operator" json:"operator"`
	VitalityChange uint   `gorm:"column:vitality_change" json:"vitality_change"`
	Reason         string `gorm:"column:reason" json:"reason"`
	Intro          string `gorm:"column:intro" json:"intro"`

	CreateTime int64 `gorm:"column:create_time" json:"create_time"`
}

// TableName tablename
func (Log) TableName() string {
	return LogTableName()
}

// ListLogs 列出元气值变更日志，传入公会 ID 时，返回筛选时间范围内主播在该公会合约（合约解约、合约失效、合约生效中等状态）时间范围内数据
func ListLogs(creatorID, guildID int64, fromTime, toTime time.Time, p, pageSize int64) ([]*Log, goutil.Pagination, error) {
	db := service.DB.Table(LogTableName() + " AS t")
	fields := "t.id, t.creator_id, t.operator, t.vitality_change, t.reason, t.intro, t.create_time"
	if guildID != 0 {
		// 如果一个用户在当天重复签约同一个公会多次，会出现 JOIN 多条数据的问题
		// 通过 DISTINCT 过滤重复数据
		fields = "DISTINCT " + fields
		db = db.Joins(
			"INNER JOIN "+livecontract.TableName()+" AS t1"+
				" ON t.creator_id = t1.live_id AND t1.guild_id = ? AND t1.status IN (?)"+
				" AND t1.contract_start <= t.create_time"+
				" AND t1.contract_end > t.create_time",
			guildID, []int64{livecontract.StatusUseless, livecontract.StatusFinished,
				livecontract.StatusContracting})
	}
	db = db.Select(fields).Where("t.creator_id = ? AND t.create_time >= ? AND t.create_time < ?", creatorID, fromTime.Unix(), toTime.Unix()).
		Order("t.id DESC")

	var count int64
	err := db.Select("COUNT(DISTINCT t.id)").Count(&count).Error
	if err != nil {
		return nil, goutil.Pagination{}, err
	}
	pa := goutil.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*Log, 0), pa, nil
	}
	var res []*Log
	err = pa.ApplyTo(db).Find(&res).Error
	return res, pa, err
}

var catalogs = map[int]int{
	OperatorSub: userapi.CatalogDamageVitality,
	OperatorAdd: userapi.CatalogReturnVitality,
	OperatorCut: userapi.CatalogCutLive,
	OperatorBan: userapi.CatalogBanRoom,
}

// AdminLogInfo 返回管理员操作日志的 intro 和 catalog
func AdminLogInfo(operator int, creatorID int64) (intro string, catalog int) {
	switch operator {
	case OperatorSub:
		intro = fmt.Sprintf("扣除主播（ID: %d）的元气值", creatorID)
	case OperatorCut:
		intro = fmt.Sprintf("切断主播（ID: %d）的直播", creatorID)
	case OperatorBan:
		intro = fmt.Sprintf("封禁主播（ID: %d）的直播间", creatorID)
	case OperatorAdd:
		intro = fmt.Sprintf("返还主播（ID: %d）的元气值", creatorID)
	default:
		panic(fmt.Sprintf("unsupported operator: %d", operator))
	}
	catalog = catalogs[operator]
	return
}
