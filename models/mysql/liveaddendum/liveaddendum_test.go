package liveaddendum

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestTagKeys(t *testing.T) {
	assert := assert.New(t)
	la := LiveAddendum{}
	assert.Empty(tutil.KeyExists(tutil.GORM, la, "id", "vitality", "last_punished_time", "is_agreed",
		"agree_guild_agreement_time", "agree_live_agreement_time", "create_time", "modified_time", "user_id"))
	log := Log{}
	assert.Empty(tutil.KeyExists(tutil.GORM, log, "id", "creator_id", "operator", "vitality_change", "reason", "intro", "create_time"))
	assert.Empty(tutil.KeyExists(tutil.JSON, log, "creator_id", "operator", "vitality_change", "reason", "intro", "create_time"))
}

func TestTableNames(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("live_addendum", LiveAddendum{}.TableName())
	assert.Equal("live_addendum_log", Log{}.TableName())
}

func TestFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	la, err := Find(998)
	require.NoError(err)
	assert.Nil(la)
	la, err = Find(10)
	require.NoError(err)
	assert.NotNil(la)
}

func TestPunish(t *testing.T) {
	la := LiveAddendum{
		Vitality:     MaxVitality,
		CreateTime:   1234567890,
		ModifiedTime: 1234567890,
	}
	la.SetUserID(12)
	require.NoError(t, service.DB.Save(&la).Error)
	t.Run("testPunish", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)
		// 测试主播不存在

		now := goutil.TimeNow()
		goutil.SetTimeNow(func() time.Time {
			return now
		})
		defer goutil.SetTimeNow(nil)
		empty, err := Punish(999, OperatorSub, 1, "go test")
		assert.True(empty == nil && err == nil, err)
		// 测试正常扣元气值
		// 保证日志按照时间顺序插入
		now = now.Add(time.Second)
		after, err := Punish(la.UserID, OperatorSub, 1, "go test")
		require.True(err == nil && after != nil, err)
		assert.Equal(la.Vitality-1, after.Vitality)
		assert.Equal(after.ModifiedTime, after.LastPunishedTime)
		assert.Greater(after.LastPunishedTime, la.ModifiedTime)
		la = *after
		// 测试封禁
		now = now.Add(time.Second)
		after, err = Punish(la.UserID, OperatorBan, 2, "go test")
		require.True(err == nil && after != nil, err)
		assert.Greater(after.LastPunishedTime, la.LastPunishedTime)
		assert.Equal(la.Vitality, after.Vitality)
		// 测试元气值扣光
		now = now.Add(time.Second)
		after, err = Punish(la.UserID, OperatorSub, 20, "go test")
		require.True(err == nil && after != nil, err)
		assert.Zero(after.Vitality)

		// 查看日志是否记录
		logs, _, err := ListLogs(12, 0, now.Add(-time.Hour), now.Add(time.Minute), 1, 10)
		require.NoError(err)
		require.GreaterOrEqual(len(logs), 3)
		assert.Equal([]uint{20, 0, 1},
			[]uint{logs[0].VitalityChange, logs[1].VitalityChange, logs[2].VitalityChange})
		assert.Equal([]int{-1, -3, -1},
			[]int{logs[0].Operator, logs[1].Operator, logs[2].Operator})
		assert.Equal([]int64{after.LastPunishedTime, la.LastPunishedTime},
			[]int64{logs[0].CreateTime, logs[2].CreateTime})
		// 测试错误的 operator
		_, err = Punish(la.UserID, 20, 1, "go test")
		assert.True(err != nil && err.Error() == "unsupported operator")

	})
	// 查看元气值是否扣光
	t.Run("testVitality", subtestVitality)
}

func subtestVitality(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	v, err := Vitality(9000000)
	assert.NoError(err)
	assert.Nil(v)
	v, err = Vitality(12)
	require.True(err == nil && v != nil, err)
	assert.Zero(*v)
	v, err = Vitality(10)
	require.True(err == nil && v != nil, err)
	assert.NotZero(*v)
}

func TestBanPunish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(15)
	la := LiveAddendum{
		Vitality:     MaxVitality,
		CreateTime:   1234567890,
		ModifiedTime: 1234567890,
	}
	la.SetUserID(testUserID)
	require.NoError(service.DB.Save(&la).Error)

	after, err := BanPunish(testUserID, "test", "10 天")
	require.True(err == nil && after != nil, err)
	assert.Greater(after.LastPunishedTime, la.LastPunishedTime)
	assert.Equal(la.Vitality, after.Vitality)
	// 查看日志是否记录
	var l Log
	require.NoError(service.DB.Where("creator_id = ?", testUserID).Order("id DESC").Find(&l).Error)
	assert.Equal("test", l.Reason)
	assert.Equal("10 天", l.Intro)
}

func TestIncreaseVitality(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var la LiveAddendum
	require.NoError(service.DB.Find(&la, "user_id = 13").Error)
	t.Run("testIncreaseVitality", func(t *testing.T) {
		// 测试正常增加元气值
		after, err := IncreaseVitality(la.UserID, 1)
		require.True(err == nil && after != nil, err)
		assert.Equal(la.Vitality+1, after.Vitality)
		// 测试加满元气值
		la = *after
		after, err = IncreaseVitality(la.UserID, 20)
		require.True(err == nil && after != nil, err)
		assert.Equal(MaxVitality, after.Vitality)
		// 查看日志是否记录
		now := goutil.TimeNow()
		logs, _, err := ListLogs(13, 0, now.Add(-time.Hour), now.Add(time.Minute), 1, 10)
		require.NoError(err)
		require.GreaterOrEqual(len(logs), 2)
		assert.Equal([]uint{20, 1},
			[]uint{logs[0].VitalityChange, logs[1].VitalityChange})
		assert.Equal([]int{1, 1},
			[]int{logs[0].Operator, logs[1].Operator})
	})
	// 查看元气值是否已满
	v, err := Vitality(la.UserID)
	require.True(err == nil && v != nil, err)
	assert.Equal(MaxVitality, *v)
}

func TestFindVitalityMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.DB.Exec(
		"INSERT INTO live_addendum (create_time, modified_time, user_id, vitality)" +
			" VALUES (1234567890, 1234567890, 999999, 12)," +
			"(1234567890, 1234567890, 999998, 12);",
	).Error)

	vitalityMap, err := FindVitalityMap([]int64{999998, 999999})
	require.NoError(err)
	assert.Len(vitalityMap, 2)
	assert.Equal(MaxVitality, vitalityMap[999998])
}

func TestAgree(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time { return now })
	defer goutil.SetTimeNow(nil)

	userID := int64(123)
	require.NoError(Agree(userID, AgreeGuildAgreement))
	now = now.Add(time.Second)
	require.NoError(Agree(userID, AgreeLiveAgreement))
	assert.PanicsWithValue("unsupported agree: -1", func() { _ = Agree(userID, -1) })

	la, err := Find(userID)
	require.NoError(err)
	require.NoError(service.DB.First(&la, "user_id = ?", userID).Error)
	assert.True(la.IsAgreed.IsSet(1))
	assert.True(la.IsAgreed.IsSet(2))
	assert.Equal(MaxVitality, la.Vitality)
	assert.Equal(now.Unix()-1, la.CreateTime)
	assert.Equal(now.Unix(), la.ModifiedTime)
	assert.Equal(now.Unix()-1, la.AgreeGuildAgreementTime)
	assert.Equal(now.Unix(), la.AgreeLiveAgreementTime)
}
