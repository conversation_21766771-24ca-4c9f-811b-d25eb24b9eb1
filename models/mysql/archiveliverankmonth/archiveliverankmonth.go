package archiveliverankmonth

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

// Type 榜单类型
const (
	TypeCreatorsRank  = iota + 1 // 主播榜
	TypeRoomUsersRank            // 用户贡献榜
)

// ArchiveLiveRankMonth 直播月榜归档
type ArchiveLiveRankMonth struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Type         int    `gorm:"column:type"`
	Month        string `gorm:"column:month"`
	RoomID       int64  `gorm:"column:room_id"`
	CreatorID    int64  `gorm:"column:creator_id"`
	UserID       int64  `gorm:"column:user_id"`
	Revenue      int64  `gorm:"column:revenue"`
	Rank         int    `gorm:"column:rank"`
}

// TableName of model "archive_live_rank_month"
func (ArchiveLiveRankMonth) TableName() string {
	return "archive_live_rank_month"
}

// BatchInsert 批量插入月榜记录
func BatchInsert(records []ArchiveLiveRankMonth) error {
	return servicedb.SplitBatchInsert(service.LiveDB, ArchiveLiveRankMonth{}.TableName(), records, 1000, false)
}
