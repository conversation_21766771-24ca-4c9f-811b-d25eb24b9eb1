package archiveliverankmonth

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestBatchInsert(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Where("month = ?", "2022-08").Delete(&ArchiveLiveRankMonth{}).Error
	require.NoError(err)

	records := []ArchiveLiveRankMonth{
		{
			Type:      TypeCreatorsRank,
			Month:     "2022-08",
			RoomID:    1102312,
			CreatorID: 9074509,
			Revenue:   2,
			Rank:      1,
		},
		{
			Type:      TypeRoomUsersRank,
			Month:     "2022-08",
			RoomID:    1102312,
			CreatorID: 9074509,
			UserID:    9074510,
			Revenue:   1,
			Rank:      2,
		},
	}
	require.NoError(BatchInsert(records))

	var count int
	err = service.LiveDB.Table(ArchiveLiveRankMonth{}.TableName()).Where("month = ?", "2022-08").Count(&count).Error
	require.NoError(err)
	assert.Equal(2, count)
}
