package livetaggroup

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 直播标签组启用状态
const (
	StatusHide = iota
	StatusShow
)

// LiveTagGroup 直播标签组
type LiveTagGroup struct {
	ID           int64  `gorm:"column:id;primary_key" json:"id"`
	CreateTime   int64  `gorm:"column:create_time" json:"-"`
	ModifiedTime int64  `gorm:"column:modified_time" json:"-"`
	Name         string `gorm:"column:name" json:"name"`
	SortOrder    int    `gorm:"column:sort_order" json:"sort_order"`
	Status       int    `gorm:"column:status" json:"status"`
}

// TableName table name
func (LiveTagGroup) TableName() string {
	return "live_tag_group"
}

// BeforeCreate 添加创建修改时间
func (group *LiveTagGroup) BeforeCreate(scope *gorm.Scope) error {
	now := goutil.TimeNow().Unix()
	group.CreateTime = now
	group.ModifiedTime = now

	return nil
}

// FindAllLiveTagGroups 查询所有生效中的直播标签组
func FindAllLiveTagGroups() ([]*LiveTagGroup, error) {
	var groups []*LiveTagGroup
	err := service.LiveDB.Where("status = ?", StatusShow).Order("sort_order DESC").Find(&groups).Error
	if err != nil {
		return nil, err
	}

	return groups, nil
}

// FindShowLiveTagGroupByID 根据 ID 查询生效中的标签组信息
func FindShowLiveTagGroupByID(groupID int64) (*LiveTagGroup, error) {
	var group LiveTagGroup
	err := service.LiveDB.Take(&group, "id = ? AND status = ?", groupID, StatusShow).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}

	return &group, nil
}
