package livetaggroup

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestFindAllLiveTagGroups(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groups, err := FindAllLiveTagGroups()
	require.NoError(err)
	require.Equal(2, len(groups))
	assert.EqualValues(2, groups[0].ID)
	assert.Equal("group2", groups[0].Name)
	assert.EqualValues(1, groups[1].ID)
	assert.Equal("group1", groups[1].Name)
}

func TestFindShowLiveTagGroupByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	group, err := FindShowLiveTagGroupByID(1)
	require.NoError(err)
	require.NotNil(group)
	assert.EqualValues(1, group.ID)
	assert.Equal("group1", group.Name)

	group, err = FindShowLiveTagGroupByID(3)
	require.NoError(err)
	assert.Nil(group)
}
