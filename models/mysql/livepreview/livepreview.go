package livepreview

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// LivePreview 直播预告表
type LivePreview struct {
	ID               int64  `gorm:"column:id;primary_key"`     // 主键
	CreateTime       int64  `gorm:"column:create_time"`        // 创建时间戳，单位：秒
	ModifiedTime     int64  `gorm:"column:modified_time"`      // 最后更新时间戳，单位：秒
	ElementType      int    `gorm:"column:element_type"`       // 预告关联类型。0：无关联元素；1：剧集
	ElementID        int64  `gorm:"column:element_id"`         // 预告关联元素的 ID，无关联元素时，默认为 0
	RoomID           int64  `gorm:"column:room_id"`            // 直播间 ID
	Title            string `gorm:"column:title"`              // 标题
	LiveStartTime    int64  `gorm:"column:live_start_time"`    // 直播开播时间点，单位：秒
	LiveScheduleTime int64  `gorm:"column:live_schedule_time"` // 直播预计开播时间点，一般比直播时间早，单位：秒
	StartTime        int64  `gorm:"column:start_time"`         // 预告卡上线时间点，单位：秒
	EndTime          int64  `gorm:"column:end_time"`           // 预告卡下线时间点，单位：秒
}

const (
	// ElementTypeDrama 预告关联类型为剧集
	ElementTypeDrama = iota + 1
)

// TableName for current model
func (LivePreview) TableName() string {
	return "live_preview"
}

// DB the db instance of LivePreview model
func (LivePreview) DB() *gorm.DB {
	return service.LiveDB.Table(LivePreview{}.TableName())
}

// FindByID 根据 ID 获取存在且生效的直播预告
func FindByID(previewID int64) (*LivePreview, error) {
	now := util.TimeNow().Unix()
	var previewInfo LivePreview
	err := previewInfo.DB().
		Where("id = ? AND start_time <= ? AND end_time > ?", previewID, now, now).Take(&previewInfo).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &previewInfo, nil
}

// FindPreview 查询生效的直播预告
func FindPreview(elementType int, elementID int64) (*LivePreview, error) {
	now := util.TimeNow().Unix()
	var livePreview LivePreview
	err := LivePreview{}.DB().
		Where("element_type = ? AND element_id = ? AND start_time <= ? AND end_time > ?",
			elementType, elementID, now, now).
		Order("start_time ASC").
		Take(&livePreview).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}
	return &livePreview, nil
}
