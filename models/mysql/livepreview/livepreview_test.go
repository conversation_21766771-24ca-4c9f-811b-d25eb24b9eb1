package livepreview

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	testPreviewID = 2333
	testElementID = 233
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(1, ElementTypeDrama)
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LivePreview{}, "id", "create_time", "modified_time", "element_type", "element_id",
		"room_id", "title", "live_start_time", "live_schedule_time", "start_time", "end_time")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_preview", LivePreview{}.TableName())
}

func TestFindByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不存在直播预告
	require.NoError(LivePreview{}.DB().Delete("", "id = ?", testPreviewID).Error)
	res, err := FindByID(testPreviewID)
	require.NoError(err)
	assert.Nil(res)

	// 测试获取直播预告
	preview := &LivePreview{
		ID:        testPreviewID,
		StartTime: util.TimeNow().Add(-time.Minute).Unix(),
		EndTime:   util.TimeNow().Add(time.Minute).Unix(),
	}
	require.NoError(preview.DB().Create(preview).Error)
	res, err = FindByID(testPreviewID)
	require.NoError(err)
	require.NotNil(res)
	assert.EqualValues(testPreviewID, res.ID)
}

func TestFindPreview(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不存在直播预告时获取
	preview, err := FindPreview(ElementTypeDrama, 999999)
	require.NoError(err)
	assert.Nil(preview)

	// 测试存在直播预告时获取
	preview, err = FindPreview(ElementTypeDrama, testElementID)
	require.NoError(err)
	require.NotNil(preview)
	assert.EqualValues(ElementTypeDrama, preview.ElementType)
	assert.EqualValues(testElementID, preview.ElementID)
}
