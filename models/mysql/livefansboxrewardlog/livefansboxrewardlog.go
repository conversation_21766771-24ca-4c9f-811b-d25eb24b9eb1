package livefansboxrewardlog

import (
	"encoding/json"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const tableName = "live_fans_box_reward_log"

// LiveFansBoxRewardLog 粉丝团宝箱领取日志表
type LiveFansBoxRewardLog struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	UserID            int64  `gorm:"column:user_id"`               // 用户 ID
	RoomID            int64  `gorm:"column:room_id"`               // 直播间 ID
	Level             int    `gorm:"column:level"`                 // 宝箱等级（同 live_fans_box 表 level）
	FansBoxTaskID     int64  `gorm:"column:fans_box_task_id"`      // 粉丝团宝箱任务 ID
	FansBoxUserTaskID int64  `gorm:"column:fans_box_user_task_id"` // 宝箱用户任务 ID
	More              []byte `gorm:"column:more"`                  // 更多信息（领取的礼物信息等）

	MoreInfo More `gorm:"-"`
}

// More 更多信息
type More struct {
	Reward RewardInfo `json:"reward"`
}

// RewardInfo 宝箱奖品信息
type RewardInfo struct {
	Type     int     `json:"type"` // 奖励类型。1：猫粮；2：免费礼物；3：专属礼物 + 免费礼物；4：头像框
	PrizeIDs []int64 `json:"prize_ids"`
}

// DB the db instance of current model
func (l LiveFansBoxRewardLog) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// TableName for current model
func (l LiveFansBoxRewardLog) TableName() string {
	return tableName
}

// BeforeCreate automatically sets columns create_time and modified_time
func (l *LiveFansBoxRewardLog) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now

	return nil
}

// AfterFind gorm 钩子
func (l *LiveFansBoxRewardLog) AfterFind() error {
	if len(l.More) > 0 {
		return json.Unmarshal(l.More, &l.MoreInfo)
	}
	return nil
}

// Create 新增奖励领取日志
func (l *LiveFansBoxRewardLog) Create() (bool, error) {
	err := l.DB().Create(l).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// HasReward 用户今日是否已在某直播间领取过粉丝团宝箱奖励
func HasReward(taskID, userID int64) (bool, error) {
	return servicedb.Exists(LiveFansBoxRewardLog{}.DB().
		Where("fans_box_task_id = ? AND user_id = ?", taskID, userID))
}
