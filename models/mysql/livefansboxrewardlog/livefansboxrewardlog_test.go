package livefansboxrewardlog

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveFansBoxRewardLog{}, "id", "create_time", "modified_time", "user_id", "room_id", "level",
		"fans_box_task_id", "fans_box_user_task_id", "more")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(More{}, "reward")
	kc.Check(RewardInfo{}, "type", "prize_ids")
}

func TestLiveFansBoxRewardLog_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_fans_box_reward_log", LiveFansBoxRewardLog{}.TableName())
}

func TestLiveFansBoxRewardLog_BeforeCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &LiveFansBoxRewardLog{}
	require.NoError(l.BeforeCreate())
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
}

func TestLiveFansBoxRewardLog_AfterFind(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := LiveFansBoxRewardLog{
		More: []byte("{\"reward\":{\"prize_ids\":[1,2],\"type\":1}}"),
	}
	require.NoError(l.AfterFind())
	assert.EqualValues(1, l.MoreInfo.Reward.Type)
	assert.Equal([]int64{1, 2}, l.MoreInfo.Reward.PrizeIDs)
}

func TestCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试记录创建成功
	l := &LiveFansBoxRewardLog{
		UserID:            2245946,
		RoomID:            645321,
		Level:             livefansbox.Level3,
		FansBoxTaskID:     154542,
		FansBoxUserTaskID: 894554,
		More:              []byte("{\"reward\":{\"type\":3,\"prize_ids\":[1,2]}}"),
	}
	ok, err := l.Create()
	require.NoError(err)
	require.True(ok)
	assert.NotZero(l.ID)
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
	// 验证数据正常
	var count int64
	require.NoError(LiveFansBoxRewardLog{}.DB().Where(l).Count(&count).Error)
	assert.EqualValues(1, count)

	// 测试重复记录创建失败
	l2 := &LiveFansBoxRewardLog{
		UserID:            2245946,
		RoomID:            645321,
		Level:             livefansbox.Level3,
		FansBoxTaskID:     154542,
		FansBoxUserTaskID: 894554,
		More:              []byte("{\"reward\":{\"type\":3,\"prize_ids\":[1,2]}}"),
	}
	ok, err = l2.Create()
	require.NoError(err)
	require.False(ok)
	// 验证数据正常
	require.NoError(LiveFansBoxRewardLog{}.DB().Where(l).Count(&count).Error)
	assert.EqualValues(1, count)
}

func TestHasReward(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试今日未领取过的情况
	testTaskID := int64(18975469)
	testRoomID := int64(25454543)
	testUserID := int64(59465465)
	require.NoError(LiveFansBoxRewardLog{}.DB().Delete("", "fans_box_task_id = ?", testTaskID).Error)
	exists, err := HasReward(testTaskID, testUserID)
	require.NoError(err)
	assert.False(exists)

	// 测试今日领取过的情况
	testTaskLog := &LiveFansBoxRewardLog{
		RoomID:            testRoomID,
		UserID:            testUserID,
		FansBoxTaskID:     testTaskID,
		FansBoxUserTaskID: 666,
		More:              []byte("{\"reward\":{\"type\":3,\"prize_ids\":[1,2]}}"),
	}
	require.NoError(LiveFansBoxRewardLog{}.DB().Create(testTaskLog).Error)
	exists, err = HasReward(testTaskID, testUserID)
	require.NoError(err)
	assert.True(exists)
}
