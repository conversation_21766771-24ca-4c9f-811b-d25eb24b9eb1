package livepreviewuserreservation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testUserID    = 2333
	testPreviewID = 12345
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LivePreviewUserReservation{}, "id", "create_time", "modified_time", "preview_id", "user_id")
}

func TestTableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_preview_user_reservation", LivePreviewUserReservation{}.TableName())
}

func TestExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不存在用户没有预约直播
	require.NoError(LivePreviewUserReservation{}.DB().
		Delete("", "preview_id = ? AND user_id = ?", testPreviewID, testUserID).Error)
	res, err := Exists(testPreviewID, testUserID)
	require.NoError(err)
	assert.False(res)

	// 测试用户预约了直播
	userReservation := &LivePreviewUserReservation{
		PreviewID: testPreviewID,
		UserID:    testUserID,
	}
	require.NoError(userReservation.DB().Create(userReservation).Error)
	res, err = Exists(testPreviewID, testUserID)
	require.NoError(err)
	assert.True(res)
}

func TestMakePreviewReservation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户预约直播
	require.NoError(LivePreviewUserReservation{}.DB().
		Delete("", "preview_id = ? AND user_id = ?", testPreviewID, testUserID).Error)
	err := MakePreviewReservation(testPreviewID, testUserID)
	require.NoError(err)

	var userReservation LivePreviewUserReservation
	require.NoError(userReservation.DB().
		Where("preview_id = ? AND user_id = ?", testPreviewID, testUserID).Take(&userReservation).Error)
	assert.EqualValues(testPreviewID, userReservation.PreviewID)
	assert.EqualValues(testUserID, userReservation.UserID)
	assert.NotZero(userReservation.CreateTime)
	assert.NotZero(userReservation.ModifiedTime)

	// 测试触发唯一索引
	require.NoError(MakePreviewReservation(testPreviewID, testUserID))
}

func TestCancelPreviewReservation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户取消预约的直播
	userReservation := &LivePreviewUserReservation{
		PreviewID: testPreviewID,
		UserID:    testUserID,
	}
	require.NoError(userReservation.DB().
		Delete("", "preview_id = ? AND user_id = ?", testPreviewID, testUserID).Error)
	require.NoError(userReservation.DB().Create(userReservation).Error)
	err := CancelPreviewReservation(testPreviewID, testUserID)
	require.NoError(err)

	var userReservation1 LivePreviewUserReservation
	assert.True(servicedb.IsErrNoRows(userReservation1.DB().
		Where("preview_id = ? AND user_id = ?", testPreviewID, testUserID).Take(&userReservation1).Error))
}
