package livepreviewuserreservation

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// LivePreviewUserReservation 直播预约用户表
type LivePreviewUserReservation struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间戳，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 最后更新时间戳，单位：秒
	PreviewID    int64 `gorm:"column:preview_id"`     // 直播预告 ID
	UserID       int64 `gorm:"column:user_id"`        // 用户 ID
}

// TableName for current model
func (LivePreviewUserReservation) TableName() string {
	return "live_preview_user_reservation"
}

// DB the db instance of LivePreviewUserReservation model
func (LivePreviewUserReservation) DB() *gorm.DB {
	return service.LiveDB.Table(LivePreviewUserReservation{}.TableName())
}

// BeforeCreate automatically set field create_time and modified_time
func (p *LivePreviewUserReservation) BeforeCreate(scope *gorm.Scope) (err error) {
	nowStamp := util.TimeNow().Unix()
	err = scope.SetColumn("create_time", nowStamp)
	if err == nil {
		err = scope.SetColumn("modified_time", nowStamp)
	}
	return err
}

// Exists 用户是否预约了直播
func Exists(previewID, userID int64) (bool, error) {
	return servicedb.Exists(LivePreviewUserReservation{}.DB().
		Where("preview_id = ? AND user_id = ?", previewID, userID))
}

// MakePreviewReservation 用户预约直播
func MakePreviewReservation(previewID, userID int64) error {
	preview := LivePreviewUserReservation{
		PreviewID: previewID,
		UserID:    userID,
	}
	err := preview.DB().Create(&preview).Error
	if err != nil && !servicedb.IsUniqueError(err) {
		return err
	}
	return nil
}

// CancelPreviewReservation 用户取消预约的直播
func CancelPreviewReservation(previewID, userID int64) error {
	return LivePreviewUserReservation{}.DB().
		Delete("", "preview_id = ? AND user_id = ?", previewID, userID).Error
}
