package application

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestElemTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Element{}, "id", "create_time", "modified_time", "application_id", "element_id")
}

func TestElementExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := service.LiveDB.Create(&Element{ApplicationID: 1, ElementID: 1}).Error
	require.NoError(err)

	exists, err := ElementExists(1, 1)
	require.NoError(err)
	assert.True(exists)

	exists, err = ElementExists(1, 2)
	require.NoError(err)
	assert.False(exists)
}

func TestElementExistsByType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := elementExistsByType(123456, ScopeRoomInitiateLuckyBag, TypeBlockList, ElementTypeRoomID)
	require.NoError(err)
	assert.True(exists)

	exists, err = elementExistsByType(123456, ScopeRoomInitiateLuckyBag, TypeAllowList, ElementTypeRoomID)
	require.NoError(err)
	assert.False(exists)
}

func TestIsRoomLuckyBagInitiateBlocked(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := IsRoomLuckyBagInitiateBlocked(123456)
	require.NoError(err)
	assert.True(exists)

	exists, err = IsRoomLuckyBagInitiateBlocked(123)
	require.NoError(err)
	assert.False(exists)
}

func TestIsRoomCustomLuckyBagInitiateAllowed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := IsRoomCustomLuckyBagInitiateAllowed(123456)
	require.NoError(err)
	assert.False(exists)

	exists, err = IsRoomCustomLuckyBagInitiateAllowed(121423)
	require.NoError(err)
	assert.True(exists)
}

func TestIsUserLuckyBagJoinBlocked(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	exists, err := IsUserLuckyBagJoinBlocked(123)
	require.NoError(err)
	assert.True(exists)

	exists, err = IsUserLuckyBagJoinBlocked(456)
	require.NoError(err)
	assert.True(exists)

	exists, err = IsUserLuckyBagJoinBlocked(123456)
	require.NoError(err)
	assert.False(exists)
}

func TestAddUserToDrawReceivedPrizeBlockList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testApplicationID int64 = 4
		testUserID        int64 = 123
	)

	err := service.LiveDB.Table(Element{}.TableName()).
		Delete("", "application_id = ? AND element_id = ?", testApplicationID, testUserID).Error
	require.NoError(err)

	blocked, err := IsUserReceivePrizeBlocked(testUserID)
	require.NoError(err)
	assert.False(blocked)

	err = AddUserToDrawReceivedPrizeBlockList(testUserID)
	require.NoError(err)
	blocked, err = IsUserReceivePrizeBlocked(testUserID)
	require.NoError(err)
	assert.True(blocked)
}

func TestIsBoxCreatorHard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testApplicationID int64 = 5
		testUserID        int64 = 123
	)

	err := service.LiveDB.Table(Element{}.TableName()).
		Delete("", "application_id = ? AND element_id = ?", testApplicationID, testUserID).Error
	require.NoError(err)

	ok, err := IsBoxCreatorHard(testUserID)
	require.NoError(err)
	assert.False(ok)

	err = service.LiveDB.Create(&Element{ApplicationID: testApplicationID, ElementID: testUserID}).Error
	require.NoError(err)
	ok, err = IsBoxCreatorHard(testUserID)
	require.NoError(err)
	assert.True(ok)
}

func TestIsBoxCreatorGuild(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testApplicationID int64 = 6
		testUserID        int64 = 123
	)

	err := service.LiveDB.Table(Element{}.TableName()).
		Delete("", "application_id = ? AND element_id = ?", testApplicationID, testUserID).Error
	require.NoError(err)

	ok, err := IsBoxCreatorGuild(testUserID)
	require.NoError(err)
	assert.False(ok)

	err = service.LiveDB.Create(&Element{ApplicationID: testApplicationID, ElementID: testUserID}).Error
	require.NoError(err)
	ok, err = IsBoxCreatorGuild(testUserID)
	require.NoError(err)
	assert.True(ok)
}
