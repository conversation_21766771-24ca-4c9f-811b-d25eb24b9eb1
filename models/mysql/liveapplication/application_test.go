package application

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Application{}, "id", "create_time", "modified_time", "intro", "scope", "type", "element_type")
}

func TestCreatePopupApplication(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	application, err := CreatePopupApplication(nil, "test", TypeAllowList, ElementTypeUserID)
	require.NoError(err)
	require.NotNil(application)
	assert.Equal(TypeAllowList, application.Type)
	assert.Equal(ElementTypeUserID, application.ElementType)
}

func TestUpdateIntro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	application, err := CreatePopupApplication(nil, "test", TypeAllowList, ElementTypeUserID)
	require.NoError(err)
	require.NotNil(application)

	err = UpdateIntro(application.ID, "test1")
	require.NoError(err)

	err = service.LiveDB.Find(application, "id = ?", application.ID).Error
	require.NoError(err)
	assert.Equal("test1", application.Intro)
}

func TestApplication_AddElement(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testApplicationID int64 = 7
		testElementID     int64 = 456
		testScope         int   = ScopeCreatorTask
	)

	// 清理测试数据
	err := service.LiveDB.Table(Element{}.TableName()).
		Delete("", "application_id = ? AND element_id = ?", testApplicationID, testElementID).Error
	require.NoError(err)

	// 获取应用对象
	application := &Application{ID: testApplicationID, Scope: testScope}

	// 测试插入新元素
	err = application.AddElement(ScopeCreatorTask, testElementID)
	require.NoError(err)

	// 验证元素已存在
	exists, err := ElementExists(testApplicationID, testElementID)
	require.NoError(err)
	assert.True(exists)

	// 测试重复插入
	err = application.AddElement(ScopeCreatorTask, testElementID)
	require.NoError(err)

	// 测试插入 scope 错误的数据
	err = application.AddElement(ScopeUserJoinLuckyBag, testElementID)
	assert.Equal(err.Error(), "需插入的名单类型和实际名单类型不符")

	// 清理测试数据
	err = service.LiveDB.Table(Element{}.TableName()).
		Delete("", "application_id = ? AND element_id = ?", testApplicationID, testElementID).Error
	require.NoError(err)
}

func TestFindOne(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试不存在的赛道 ID
	app, err := FindOne(-1)
	require.NoError(err)
	assert.Nil(app)

	// 测试存在的赛道 ID
	app, err = FindOne(6)
	require.NoError(err)
	assert.NotNil(app)
}
