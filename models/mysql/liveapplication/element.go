package application

import (
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Element 名单成员表
type Element struct {
	ID            int64 `gorm:"column:id;primary_key"`
	CreateTime    int64 `gorm:"column:create_time"`
	ModifiedTime  int64 `gorm:"column:modified_time"`
	ApplicationID int64 `gorm:"column:application_id"`
	ElementID     int64 `gorm:"column:element_id"`
}

// TableName 表名
func (element Element) TableName() string {
	return "live_application_element"
}

// BeforeSave gorm hook
func (element *Element) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if service.LiveDB.NewRecord(element) {
		element.CreateTime = nowTime
	}
	element.ModifiedTime = nowTime
	return
}

// ElementExists 是否存在
func ElementExists(applicationID int64, elementID int64) (bool, error) {
	db := service.LiveDB.Model(&Element{}).Where("application_id = ? AND element_id = ?", applicationID, elementID)
	return servicedb.Exists(db)
}

// 根据用途查询是否在名单内
func elementExistsByType(elementID int64, scope, applicationType, elementType int) (bool, error) {
	db := service.LiveDB.Table(Application{}.TableName()+" AS la").
		Joins("LEFT JOIN "+Element{}.TableName()+" AS le ON la.id = le.application_id").
		Where("le.element_id = ? AND la.scope = ? AND la.type = ? AND la.element_type = ?", elementID, scope, applicationType, elementType)
	return servicedb.Exists(db)
}

// IsRoomLuckyBagInitiateBlocked 判断直播间是否在发起福袋黑名单中
func IsRoomLuckyBagInitiateBlocked(roomID int64) (bool, error) {
	return elementExistsByType(roomID, ScopeRoomInitiateLuckyBag, TypeBlockList, ElementTypeRoomID)
}

// IsRoomCustomLuckyBagInitiateAllowed 判断直播间是否在发起自定义福袋白单中
func IsRoomCustomLuckyBagInitiateAllowed(roomID int64) (bool, error) {
	return elementExistsByType(roomID, ScopeRoomInitiateCustomLuckyBag, TypeAllowList, ElementTypeRoomID)
}

// IsUserLuckyBagJoinBlocked 判断用户是否在参与福袋用户黑名单中
func IsUserLuckyBagJoinBlocked(userID int64) (bool, error) {
	return elementExistsByType(userID, ScopeUserJoinLuckyBag, TypeBlockList, ElementTypeUserID)
}

// AddUserToDrawReceivedPrizeBlockList 插入用户福袋和红包抽中资格黑名单
func AddUserToDrawReceivedPrizeBlockList(userID int64) error {
	application := new(Application)
	err := service.LiveDB.Where("scope = ? AND type = ? AND element_type = ?",
		ScopeUserDrawReceivedPrize, TypeBlockList, ElementTypeUserID).
		Take(application).Error
	if err != nil {
		return err
	}

	err = service.LiveDB.Create(&Element{ApplicationID: application.ID, ElementID: userID}).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			// 忽略已存在的唯一索引错误
			return nil
		}
		return err
	}
	return nil
}

// IsUserReceivePrizeBlocked 判断用户在福袋和红包中抽中奖品黑名单中
func IsUserReceivePrizeBlocked(userID int64) (bool, error) {
	return elementExistsByType(userID, ScopeUserDrawReceivedPrize, TypeBlockList, ElementTypeUserID)
}

// IsBoxCreatorHard 判断是否是月度宝箱报名挑战榜（困难）白名单主播
func IsBoxCreatorHard(userID int64) (bool, error) {
	return elementExistsByType(userID, ScopeBoxCreatorHard, TypeAllowList, ElementTypeUserID)
}

// IsBoxCreatorGuild 判断是否是月度宝箱公会报名白名单主播
func IsBoxCreatorGuild(userID int64) (bool, error) {
	return elementExistsByType(userID, ScopeBoxCreatorGuild, TypeAllowList, ElementTypeUserID)
}
