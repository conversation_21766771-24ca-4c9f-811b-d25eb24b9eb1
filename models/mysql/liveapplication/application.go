package application

import (
	"errors"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 名单作用范围
const (
	// TODO: 小窗考虑修改成 ScopeRoomPopup
	ScopePopup                      = iota + 1 // 小窗
	ScopeRoomInitiateLuckyBag                  // 发送福袋入口（仅黑名单）
	ScopeRoomInitiateCustomLuckyBag            // 发送自定义福袋（仅白名单）
	ScopeUserJoinLuckyBag                      // 福袋参与资格（仅黑名单）
	ScopeUserDrawReceivedPrize                 // 福袋和红包中奖资格（仅黑名单）
	ScopeBoxCreatorHard                        // 月度宝箱报名挑战榜（困难）白名单
	ScopeBoxCreatorGuild                       // 月度宝箱公会主播白名单
	ScopeCreatorTask                           // 主播任务白名单
)

// 名单类型
const (
	TypeAllowList = iota + 1
	TypeBlockList
)

// 成员类型
const (
	ElementTypeUserID = iota + 1
	ElementTypeRoomID
)

// Application 名单表
type Application struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`
	ModifiedTime int64  `gorm:"column:modified_time"`
	Intro        string `gorm:"column:intro"`        // 名单用途
	Scope        int    `gorm:"column:scope"`        // 1: 小窗; 2: 发送福袋入口; 3: 发送自定义福袋; 4: 福袋参与资格; 5: 福袋和红包中奖资格
	Type         int    `gorm:"column:type"`         // 1: 白名单，2: 黑名单
	ElementType  int    `gorm:"column:element_type"` // 1: 用户 ID，2: 房间 ID
}

// TableName 表名
func (application Application) TableName() string {
	return "live_application"
}

// BeforeSave gorm hook
func (application *Application) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if service.LiveDB.NewRecord(application) {
		application.CreateTime = nowTime
	}
	application.ModifiedTime = nowTime
	return
}

// AddElement 插入名单成员
func (application *Application) AddElement(scope int, elementID int64) error {
	if application.Scope != scope {
		return errors.New("需插入的名单类型和实际名单类型不符")
	}

	// 已经在名单中，直接返回
	exists, err := ElementExists(application.ID, elementID)
	if err != nil {
		return err
	}
	if exists {
		return nil
	}

	// 不在名单中就插入
	err = service.LiveDB.Create(&Element{ApplicationID: application.ID, ElementID: elementID}).Error
	if err != nil {
		return err
	}
	return nil
}

// CreatePopupApplication 创建小窗黑白名单
func CreatePopupApplication(db *gorm.DB, intro string, applicationType, elementType int) (*Application, error) {
	if db == nil {
		db = service.LiveDB
	}
	application := &Application{
		Intro:       intro,
		Scope:       ScopePopup,
		Type:        applicationType,
		ElementType: elementType,
	}
	err := db.Create(application).Error
	if err != nil {
		return nil, err
	}
	return application, nil
}

// UpdateIntro 更新简介
func UpdateIntro(applicationID int64, intro string) error {
	return service.LiveDB.Model(&Application{}).Where("id = ?", applicationID).Updates(
		map[string]any{
			"intro":         intro,
			"modified_time": goutil.TimeNow().Unix(),
		}).Error
}

// FindOne 根据 ID 查询名单
func FindOne(applicationID int64) (*Application, error) {
	var a Application
	err := service.LiveDB.Where("id = ?", applicationID).Take(&a).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return &a, nil
}
