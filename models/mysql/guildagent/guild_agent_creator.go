package guildagent

import (
	"database/sql"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// AgentCreator model for table missevan_live.guild_agent_creator
type AgentCreator struct {
	ID           int64 `gorm:"column:id;primary_key" json:"id"`
	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
	DeleteTime   int64 `gorm:"column:delete_time" json:"-"`

	GuildID   int64 `gorm:"column:guild_id" json:"guild_id"`
	AgentID   int64 `gorm:"column:agent_id" json:"agent_id"`
	CreatorID int64 `gorm:"column:creator_id" json:"creator_id"`
}

// DB the db instance of AgentCreator model
func (ga AgentCreator) DB() *gorm.DB {
	return service.LiveDB.Table(ga.TableName())
}

// TableName for current model
func (AgentCreator) TableName() string {
	return "guild_agent_creator"
}

// BeforeSave gorm hook
func (ga *AgentCreator) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if ga.DB().NewRecord(ga) {
		ga.CreateTime = nowTime
	}
	ga.ModifiedTime = nowTime
	return
}

// IsAssigned 主播是否分配给特定的经纪人
func IsAssigned(creatorID int64, agentID int64) (bool, error) {
	var agentCreator AgentCreator
	err := AgentCreator{}.DB().
		First(&agentCreator, "creator_id = ? AND agent_id = ? AND delete_time = 0", creatorID, agentID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

// FindAgentCreatorListByGuild 查询指定公会对应的经纪人及主播
func FindAgentCreatorListByGuild(guildID int64) (list []*AgentCreator, err error) {
	err = AgentCreator{}.DB().
		Select("agent_id, creator_id").
		Find(&list, "guild_id = ? AND delete_time = 0", guildID).Error
	return
}

// FindGuildAgentListByCreator 查询指定主播对应的经纪人及公会信息
func FindGuildAgentListByCreator(liveIDs []int64) (list []*AgentCreator, err error) {
	list = make([]*AgentCreator, 0, len(liveIDs))
	if len(liveIDs) == 0 {
		return
	}
	liveIDs = util.Uniq(liveIDs)
	err = AgentCreator{}.DB().
		Select("guild_id, agent_id, creator_id").
		Find(&list, "creator_id IN (?) AND delete_time = 0", liveIDs).Error

	return
}

// Unassign 为特定的主播取消经纪人的分配
func Unassign(creatorID, guildID int64) error {
	err := AgentCreator{}.DB().
		Where("creator_id = ? AND guild_id = ? AND delete_time = 0", creatorID, guildID).
		UpdateColumns(map[string]interface{}{
			"delete_time":   goutil.TimeNow().Unix(),
			"modified_time": goutil.TimeNow().Unix(),
		}).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			return nil
		}
	}

	return nil
}

// AgentID 获取公会主播所属的经纪人
func AgentID(creatorID int64, guildID ...int64) (agentID int64, err error) {
	db := AgentCreator{}.DB().Select("agent_id").
		Where("creator_id = ? AND delete_time = 0", creatorID)
	if len(guildID) > 0 {
		db = db.Where("guild_id = ?", guildID[0])
	}
	err = db.Row().Scan(&agentID)
	if err == sql.ErrNoRows {
		err = nil
	}

	return
}

// FindAgentsByCreatorIDs 批量获取主播对应的经纪人
func FindAgentsByCreatorIDs(tx *gorm.DB, creatorIDs []int64, guildID int64) ([]*AgentCreator, error) {
	if tx == nil {
		tx = AgentCreator{}.DB()
	}
	var list []*AgentCreator
	err := tx.Find(&list, "creator_id IN (?) AND guild_id = ? AND delete_time = 0", creatorIDs, guildID).Error
	if err != nil {
		return nil, err
	}

	return list, nil
}

// BatchAssign 批量分配经纪人
func BatchAssign(tx *gorm.DB, creatorIDs []int64, guildID, agentID int64) error {
	if tx == nil {
		tx = AgentCreator{}.DB()
	}

	agents := make([]*AgentCreator, 0, len(creatorIDs))
	now := goutil.TimeNow().Unix()
	for i := range creatorIDs {
		agents = append(agents, &AgentCreator{
			CreateTime:   now,
			ModifiedTime: now,
			CreatorID:    creatorIDs[i],
			AgentID:      agentID,
			GuildID:      guildID,
		})
	}
	err := servicedb.BatchInsert(tx, AgentCreator{}.TableName(), agents)
	if err != nil {
		return err
	}
	return nil
}

// BatchUnassign 批量取消经纪人的分配
func BatchUnassign(tx *gorm.DB, ids []int64) (bool, error) {
	if tx == nil {
		tx = AgentCreator{}.DB()
	}
	now := goutil.TimeNow().Unix()
	db := tx.Table(AgentCreator{}.TableName()).Where("id IN (?) AND delete_time = 0", ids).
		Updates(map[string]any{
			"delete_time":   now,
			"modified_time": now,
		})
	if err := db.Error; err != nil {
		return false, err
	}
	return db.RowsAffected == int64(len(ids)), nil
}
