package guildagent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestGuildID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	ag := &GuildAgent{
		GuildID: 100,
		AgentID: 15,
	}
	err := GuildAgent{}.DB().Create(ag).Error
	require.NoError(err)
	defer func() {
		require.NoError(GuildAgent{}.DB().Delete("", "id = ?", ag.ID).Error)
	}()

	guildID, err := GuildID(ag.AgentID)
	assert.NoError(err)
	assert.Equal(ag.GuildID, guildID)

	require.NoError(
		GuildAgent{}.DB().
			Where("id = ?", ag.ID).
			UpdateColumn("delete_time", util.TimeNow().Unix()).
			Error,
	)

	guildID, err = GuildID(ag.AgentID)
	assert.NoError(err)
	assert.Equal(int64(0), guildID)
}
