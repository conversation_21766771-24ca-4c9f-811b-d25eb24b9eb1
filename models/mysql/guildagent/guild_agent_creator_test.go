package guildagent

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestIsAssigned(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	gac := &AgentCreator{
		GuildID:   100,
		AgentID:   15,
		CreatorID: 333,
	}
	err := AgentCreator{}.DB().Create(gac).Error
	require.NoError(err)
	defer func() {
		require.NoError(AgentCreator{}.DB().Delete("", "id = ?", gac.ID).Error)
	}()

	isAssigned, err := IsAssigned(gac.CreatorID, gac.AgentID)
	require.NoError(err)
	assert.True(isAssigned)

	err = AgentCreator{}.DB().Where("id = ?", gac.ID).UpdateColumns(map[string]interface{}{
		"delete_time":   util.TimeNow().Unix(),
		"modified_time": util.TimeNow().Unix(),
	}).Error
	require.NoError(err)

	isAssigned, err = IsAssigned(gac.CreatorID, gac.AgentID)
	require.NoError(err)
	assert.False(isAssigned)
}

func TestUnassign(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	gac := &AgentCreator{
		GuildID:   200,
		AgentID:   25,
		CreatorID: 444,
	}
	err := AgentCreator{}.DB().Create(gac).Error
	require.NoError(err)
	defer func() {
		require.NoError(AgentCreator{}.DB().Delete("", "id = ?", gac.ID).Error)
	}()
	err = Unassign(gac.CreatorID, gac.GuildID)
	require.NoError(err)

	require.NoError(AgentCreator{}.DB().Where("id = ?", gac.ID).First(gac).Error)
	assert.Greater(gac.DeleteTime, int64(0))
}

func TestFindAgentCreatorListByGuild(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	gac := &AgentCreator{
		GuildID:   300,
		AgentID:   35,
		CreatorID: 555,
	}
	err := AgentCreator{}.DB().Create(gac).Error
	require.NoError(err)

	list, err := FindAgentCreatorListByGuild(gac.GuildID)
	require.NoError(err)
	assert.Len(list, 1)
	assert.Equal(gac.AgentID, list[0].AgentID)
	assert.Equal(gac.CreatorID, list[0].CreatorID)

	require.NoError(AgentCreator{}.DB().Delete("", "id = ?", gac.ID).Error)
	list, err = FindAgentCreatorListByGuild(gac.GuildID)
	require.NoError(err)
	assert.Len(list, 0)
}

func TestFindGuildAgentListByCreator(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	list, err := FindGuildAgentListByCreator([]int64{})
	require.NoError(err)
	assert.Len(list, 0)

	gac := &AgentCreator{
		GuildID:   400,
		AgentID:   45,
		CreatorID: 666,
	}
	err = AgentCreator{}.DB().Create(gac).Error
	require.NoError(err)

	list, err = FindGuildAgentListByCreator([]int64{gac.CreatorID, gac.CreatorID})
	require.NoError(err)
	assert.Len(list, 1)
	assert.Equal(gac.GuildID, list[0].GuildID)
	assert.Equal(gac.AgentID, list[0].AgentID)
	assert.Equal(gac.CreatorID, list[0].CreatorID)

	require.NoError(AgentCreator{}.DB().Delete("", "id = ?", gac.ID).Error)
	list, err = FindGuildAgentListByCreator([]int64{gac.CreatorID, gac.CreatorID})
	require.NoError(err)
	assert.Len(list, 0)
}

func TestAgentID(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	gac := &AgentCreator{
		GuildID:   500,
		AgentID:   55,
		CreatorID: 777,
	}
	require.NoError(AgentCreator{}.DB().Create(gac).Error)
	defer func() {
		require.NoError(AgentCreator{}.DB().Delete(nil, "id = ?", gac.ID).Error)
	}()

	agentID, err := AgentID(gac.CreatorID)
	require.NoError(err)
	assert.Equal(gac.AgentID, agentID)

	agentID, err = AgentID(gac.CreatorID, gac.GuildID)
	require.NoError(err)
	assert.Equal(gac.AgentID, agentID)

	require.NoError(AgentCreator{}.DB().Where("id = ?", gac.ID).UpdateColumn("delete_time", util.TimeNow().Unix()).Error)

	agentID, err = AgentID(gac.CreatorID)
	require.NoError(err)
	assert.Equal(int64(0), agentID)

	agentID, err = AgentID(gac.CreatorID, gac.GuildID)
	require.NoError(err)
	assert.Equal(int64(0), agentID)

	agentID, err = AgentID(987654321)
	require.NoError(err)
	assert.Equal(int64(0), agentID)
}

func TestFindAgentsByCreatorIDs(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	list := []*AgentCreator{
		{
			GuildID:   100,
			AgentID:   1,
			CreatorID: 5,
		},
		{
			GuildID:   100,
			AgentID:   2,
			CreatorID: 6,
		},
	}
	require.NoError(AgentCreator{}.DB().Delete("", "guild_id = ?", 100).Error)
	require.NoError(servicedb.BatchInsert(AgentCreator{}.DB(), AgentCreator{}.TableName(), list))

	list, err := FindAgentsByCreatorIDs(nil, []int64{5, 6}, 100)
	require.NoError(err)
	assert.Len(list, 2)

	list, err = FindAgentsByCreatorIDs(nil, []int64{5, 6}, -100)
	require.NoError(err)
	assert.Empty(list)
}

func TestBatchAssignAndUnassign(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var (
		testGuildID    = int64(100)
		testAgentID    = int64(1)
		testCreatorIDs = []int64{5, 6, 7}
	)

	require.NoError(AgentCreator{}.DB().Delete("", "guild_id = ?", testGuildID).Error)

	require.NoError(BatchAssign(service.LiveDB, testCreatorIDs, testGuildID, testAgentID))
	agentList, err := FindAgentsByCreatorIDs(nil, testCreatorIDs, testGuildID)
	require.NoError(err)
	assert.Len(agentList, 3)

	ids := util.SliceMap(agentList, func(agent *AgentCreator) int64 {
		return agent.ID
	})
	require.Len(ids, 3)
	ok, err := BatchUnassign(service.LiveDB, ids)
	require.NoError(err)
	require.True(ok)
	agentList, err = FindAgentsByCreatorIDs(nil, testCreatorIDs, testGuildID)
	require.NoError(err)
	assert.Empty(agentList)

	ok, err = BatchUnassign(nil, ids)
	require.NoError(err)
	require.False(ok)
}
