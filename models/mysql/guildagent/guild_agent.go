package guildagent

import (
	"database/sql"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// GuildAgent model for table missevan_live.guild_agent
type GuildAgent struct {
	ID           int64 `gorm:"column:id;primary_key" json:"id"`
	CreateTime   int64 `gorm:"column:create_time" json:"-"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"-"`
	DeleteTime   int64 `gorm:"column:delete_time" json:"-"`

	GuildID int64 `gorm:"column:guild_id" json:"guild_id"`
	AgentID int64 `gorm:"column:agent_id" json:"agent_id"`
}

// DB the db instance of GuildAgent model
func (ga GuildAgent) DB() *gorm.DB {
	return service.LiveDB.Table(ga.TableName())
}

// TableName for current model
func (GuildAgent) TableName() string {
	return "guild_agent"
}

// BeforeSave gorm hook
func (ga *GuildAgent) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if ga.DB().NewRecord(ga) {
		ga.CreateTime = nowTime
	}
	ga.ModifiedTime = nowTime
	return
}

// GuildID 获取经纪人所属公会的 ID，若不属于公会经纪人，则公会 ID 为 0
func GuildID(agentID int64) (guildID int64, err error) {
	err = GuildAgent{}.DB().Table(GuildAgent{}.TableName()).
		Select("guild_id").Where("agent_id = ? AND delete_time = 0", agentID).
		Row().Scan(&guildID)
	if err != nil {
		if err == sql.ErrNoRows {
			err = nil
		}
		return
	}

	return
}
