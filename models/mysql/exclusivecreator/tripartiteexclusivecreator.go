package exclusivecreator

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 状态
const (
	StatusValid   = iota // 生效中
	StatusExpired        // 身份到期
	StatusDeleted        // 超管移除
)

const tableName = "tripartite_exclusive_creator"

// TableName table name
func TableName() string {
	return tableName
}

// TripartiteExclusiveCreator 三方独家主播
type TripartiteExclusiveCreator struct {
	ID              int64 `gorm:"column:id;primary_key"`
	CreateTime      int64 `gorm:"column:create_time"`
	ModifiedTime    int64 `gorm:"column:modified_time"`
	GuildID         int64 `gorm:"column:guild_id"` // 三方独家主播在合约期内不允许退会
	CreatorID       int64 `gorm:"column:creator_id"`
	GuildContractID int64 `gorm:"column:guild_contract_id"`
	ContractEnd     int64 `gorm:"column:contract_end"`
	Status          int   `gorm:"column:status"`
}

// TripartiteCreatorLister 请求三方独家主播列表参数
type TripartiteCreatorLister struct {
	CreatorID int64
	GuildID   int64
	Status    []int
	P         int64
	PageSize  int64
}

// DB the db instance of TripartiteExclusiveCreator model
func (tec TripartiteExclusiveCreator) DB() *gorm.DB {
	return service.LiveDB.Table(tec.TableName())
}

// TableName table name
func (TripartiteExclusiveCreator) TableName() string {
	return TableName()
}

// BeforeSave gorm hook
func (tec *TripartiteExclusiveCreator) BeforeSave() (err error) {
	nowTime := goutil.TimeNow().Unix()
	if tec.DB().NewRecord(tec) {
		tec.CreateTime = nowTime
	}
	tec.ModifiedTime = nowTime
	return
}

// StatusStr 获取三方独家主播状态
func StatusStr(status int) string {
	switch status {
	case StatusValid:
		return "生效中"
	case StatusExpired:
		return "身份到期"
	case StatusDeleted:
		return "超管移除"
	default:
		return ""
	}
}

// List 根据参数获取三方独家主播列表
func (param TripartiteCreatorLister) List() ([]*TripartiteExclusiveCreator, goutil.Pagination, error) {
	db := TripartiteExclusiveCreator{}.DB().
		Select("id, create_time, guild_id, creator_id, status, contract_end").
		Order("id DESC").
		Where("status IN (?)", param.Status)

	if param.GuildID != 0 {
		db = db.Where("guild_id = ?", param.GuildID)
	}
	if param.CreatorID != 0 {
		db = db.Where("creator_id = ?", param.CreatorID)
	}

	var count int64
	var pa goutil.Pagination
	if err := db.Count(&count).Error; err != nil {
		return nil, pa, err
	}
	pa = goutil.MakePagination(count, param.P, param.PageSize)
	if !pa.Valid() {
		return nil, pa, nil
	}

	list := make([]*TripartiteExclusiveCreator, 0, pa.Limit())
	err := pa.ApplyTo(db).Find(&list).Error
	if err != nil {
		return nil, pa, err
	}

	return list, pa, nil
}

// IsExclusiveCreator 判断主播是否是三方独家主播
func IsExclusiveCreator(creatorID int64) (bool, error) {
	tec := new(TripartiteExclusiveCreator)
	err := TripartiteExclusiveCreator{}.DB().Select("id").
		Where("creator_id = ? AND status = ? AND contract_end > ?",
			creatorID, StatusValid, goutil.TimeNow().Unix()).
		First(tec).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

// FindExclusiveCreatorIDs 根据主播 ID 查询三方独家主播 ID
func FindExclusiveCreatorIDs(creatorIDs []int64) ([]int64, error) {
	ids := make([]int64, 0, len(creatorIDs))
	err := TripartiteExclusiveCreator{}.DB().Select("creator_id").
		Where("creator_id IN (?) AND status = ? AND contract_end > ?",
			creatorIDs, StatusValid, goutil.TimeNow().Unix()).
		Pluck("creator_id", &ids).Error
	if err != nil {
		return nil, err
	}

	return ids, nil
}

// FindByID 通过 ID 获取三方独家主播记录
func FindByID(exclusiveID int64) (*TripartiteExclusiveCreator, error) {
	tec := new(TripartiteExclusiveCreator)
	err := TripartiteExclusiveCreator{}.DB().
		Where("id = ? AND status = ?", exclusiveID, StatusValid).
		Find(tec).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			err = nil
		}
		return nil, err
	}

	return tec, nil
}

// FindCreatorIDsByGuildID 根据公会 ID 查询三方独家主播 ID
func FindCreatorIDsByGuildID(guildID int64) ([]int64, error) {
	var ids []int64
	err := TripartiteExclusiveCreator{}.DB().
		Where("guild_id = ? AND status = ?", guildID, StatusValid).
		Pluck("creator_id", &ids).Error
	if err != nil {
		return nil, err
	}

	return ids, nil
}

// FindMapByCreatorIDs 根据主播 ID 查询三方独家主播信息
func FindMapByCreatorIDs(creatorIDs []int64) (map[int64]*TripartiteExclusiveCreator, error) {
	var creators []*TripartiteExclusiveCreator
	err := TripartiteExclusiveCreator{}.DB().
		Where("creator_id IN (?) AND status = ? AND contract_end > ?",
			creatorIDs, StatusValid, goutil.TimeNow().Unix()).
		Scan(&creators).Error
	if err != nil {
		return nil, err
	}

	creatorMap := goutil.ToMap(creators, "CreatorID").(map[int64]*TripartiteExclusiveCreator)

	return creatorMap, nil
}
