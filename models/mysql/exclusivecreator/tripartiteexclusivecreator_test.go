package exclusivecreator

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	m.Run()
}

func TestGuildTransferHistoryTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(TripartiteExclusiveCreator{},
		"id", "create_time", "modified_time", "creator_id", "guild_id", "guild_contract_id", "contract_end", "status")
}

func TestStatusStr(t *testing.T) {
	assert := assert.New(t)

	status := []int{StatusValid, StatusExpired, StatusDeleted, 3}
	expected := []string{"生效中", "身份到期", "超管移除", ""}
	for i, s := range status {
		str := StatusStr(s)
		assert.Equal(expected[i], str)
	}
}

func TestList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	params := TripartiteCreatorLister{
		P:        1,
		PageSize: 20,
		Status:   []int{StatusValid},
	}

	// 测试获取有效的三方独家列表
	ecl, pa, err := params.List()
	require.NoError(err)
	assert.NotNil(pa)
	assert.NotNil(ecl)
	assert.Len(ecl, 2)

	// 测试获取无效的三方独家列表
	params.Status = []int{StatusExpired, StatusDeleted}
	ecl, pa, err = params.List()
	require.NoError(err)
	assert.NotNil(pa)
	assert.NotNil(ecl)
	assert.Len(ecl, 2)

	// 测试主播 ID 搜索
	params.CreatorID = 3
	ecl, pa, err = params.List()
	require.NoError(err)
	assert.NotNil(pa)
	assert.NotNil(ecl)
	assert.Len(ecl, 1)
}

func TestIsExclusiveCreator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	b, err := IsExclusiveCreator(1000)
	require.NoError(err)
	assert.False(b)

	b, err = IsExclusiveCreator(1)
	require.NoError(err)
	assert.True(b)
}

func TestFindExclusiveCreatorIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorIDs := []int64{1, 2, 3, 4}
	ids, err := FindExclusiveCreatorIDs(creatorIDs)
	require.NoError(err)
	assert.Len(ids, 2)

	creatorIDs = []int64{1000, 1001}
	ids, err = FindExclusiveCreatorIDs(creatorIDs)
	require.NoError(err)
	assert.Empty(ids)
}

func TestFindByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	tec, err := FindByID(1000)
	require.NoError(err)
	assert.Nil(tec)

	tec, err = FindByID(1)
	require.NoError(err)
	assert.NotNil(tec)
}

func TestFindCreatorIDsByGuildID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ids, err := FindCreatorIDsByGuildID(3)
	require.NoError(err)
	assert.Equal([]int64{1, 2}, ids)
}

func TestFindMapByCreatorIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorIDs := []int64{1, 2, 3, 4}
	creatorMap, err := FindMapByCreatorIDs(creatorIDs)
	require.NoError(err)
	assert.Len(creatorMap, 2)

	// 测试主播 ID 中没有三方独家主播
	creatorIDs = []int64{3, 4}
	creatorMap, err = FindMapByCreatorIDs(creatorIDs)
	require.NoError(err)
	assert.Empty(creatorMap)
}
