package livefansboxusertask

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveFansBoxUserTask{}, "id", "create_time", "modified_time", "room_id", "user_id",
		"fans_box_task_id", "user_energy")
}

func TestLiveFansBoxTask_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_fans_box_user_task", tableName)
}

func TestLiveFansBoxTask_BeforeCreate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &LiveFansBoxUserTask{}
	require.NoError(l.BeforeCreate())
	assert.NotZero(l.CreateTime)
	assert.NotZero(l.ModifiedTime)
}

func TestLiveFansBoxUserTask_BeforeUpdate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	l := &LiveFansBoxUserTask{}
	// 设置一个初始的修改时间
	initialModifiedTime := int64(1640995200) // 2022-01-01 00:00:00
	l.ModifiedTime = initialModifiedTime

	// 调用 BeforeUpdate 钩子
	require.NoError(l.BeforeUpdate())

	// 验证 ModifiedTime 已被更新
	assert.NotZero(l.ModifiedTime)
	assert.NotEqual(initialModifiedTime, l.ModifiedTime)
	assert.Greater(l.ModifiedTime, initialModifiedTime)

	// 验证 ModifiedTime 是当前时间附近的值
	currentTime := goutil.TimeNow().Unix()
	assert.InDelta(currentTime, l.ModifiedTime, 2) // 允许 2 秒误差
}

func TestLiveFansBoxUserTask_DB(t *testing.T) {
	assert := assert.New(t)

	db := LiveFansBoxUserTask{}.DB()
	assert.NotNil(db)
}

func TestFindUserTask(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试未找到用户任务记录的情况
	testTaskID := int64(48975469)
	testUserID := int64(78445445)
	require.NoError(LiveFansBoxUserTask{}.DB().Delete("", "fans_box_task_id = ?", testTaskID).Error)
	task, err := FindUserTask(testTaskID, testUserID)
	require.NoError(err)
	assert.Nil(task)

	// 测试找到用户任务记录的情况
	testRoomID := int64(941654564)
	testTask := &LiveFansBoxUserTask{
		RoomID:        testRoomID,
		UserID:        testUserID,
		FansBoxTaskID: testTaskID,
		UserEnergy:    1,
	}
	require.NoError(LiveFansBoxUserTask{}.DB().Create(testTask).Error)
	task, err = FindUserTask(testTask.FansBoxTaskID, testUserID)
	require.NoError(err)
	require.NotNil(task)
	assert.Equal(testRoomID, task.RoomID)
	assert.Equal(testUserID, task.UserID)
	assert.Equal(testTaskID, task.FansBoxTaskID)
	assert.Equal(testTask.UserEnergy, task.UserEnergy)
}

func TestHasContributedEnergy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试未贡献过能量值的情况
	testRoomID := int64(4545454)
	testUserID := int64(8946545)
	require.NoError(LiveFansBoxUserTask{}.DB().Delete("", "room_id = ?", testRoomID).Error)
	exists, err := HasContributedEnergy(testRoomID, testUserID)
	require.NoError(err)
	assert.False(exists)

	// 测试贡献过能量值的情况
	testTask := &LiveFansBoxUserTask{
		RoomID:        testRoomID,
		UserID:        testUserID,
		FansBoxTaskID: 233,
		UserEnergy:    1,
	}
	require.NoError(LiveFansBoxUserTask{}.DB().Create(testTask).Error)
	exists, err = HasContributedEnergy(testRoomID, testUserID)
	require.NoError(err)
	assert.True(exists)

	// 测试昨天非今日贡献过能量值的情况
	testTask.CreateTime = goutil.BeginningOfDay(goutil.TimeNow().AddDate(0, 0, -1)).Unix()
	require.NoError(LiveFansBoxUserTask{}.DB().Save(testTask).Error)
	exists, err = HasContributedEnergy(testRoomID, testUserID)
	require.NoError(err)
	assert.False(exists)
}

func TestUpsertUserEnergy(t *testing.T) {
	t.Run("创建新记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		testUserID := int64(12345678)
		testTaskID := int64(87654321)
		testRoomID := int64(11223344)

		// 清理测试数据
		require.NoError(LiveFansBoxUserTask{}.DB().Delete("", "user_id = ? AND fans_box_task_id = ?", testUserID, testTaskID).Error)

		// 测试第一次创建记录
		err := UpsertUserEnergy(nil, testRoomID, testUserID, testTaskID, 10)
		require.NoError(err)

		// 验证记录是否正确创建
		task, err := FindUserTask(testTaskID, testUserID)
		require.NoError(err)
		require.NotNil(task)
		assert.Equal(testRoomID, task.RoomID)
		assert.Equal(testUserID, task.UserID)
		assert.Equal(testTaskID, task.FansBoxTaskID)
		assert.Equal(10, task.UserEnergy)
		assert.NotZero(task.CreateTime)
		assert.NotZero(task.ModifiedTime)
	})

	t.Run("更新现有记录", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		testUserID := int64(12345679)
		testTaskID := int64(87654322)
		testRoomID := int64(11223345)

		// 清理测试数据
		require.NoError(LiveFansBoxUserTask{}.DB().Delete("", "user_id = ? AND fans_box_task_id = ?", testUserID, testTaskID).Error)

		// 先创建一条记录
		err := UpsertUserEnergy(nil, testRoomID, testUserID, testTaskID, 10)
		require.NoError(err)

		// 获取原始记录信息
		originalTask, err := FindUserTask(testTaskID, testUserID)
		require.NoError(err)
		require.NotNil(originalTask)
		originalCreateTime := originalTask.CreateTime
		originalModifiedTime := originalTask.ModifiedTime

		// 稍微等待一下确保时间戳不同
		time.Sleep(time.Second)

		// 测试更新现有记录（累加能量值）
		err = UpsertUserEnergy(nil, testRoomID, testUserID, testTaskID, 15)
		require.NoError(err)

		// 验证记录是否正确更新
		updatedTask, err := FindUserTask(testTaskID, testUserID)
		require.NoError(err)
		require.NotNil(updatedTask)
		assert.Equal(testRoomID, updatedTask.RoomID)
		assert.Equal(testUserID, updatedTask.UserID)
		assert.Equal(testTaskID, updatedTask.FansBoxTaskID)
		assert.Equal(25, updatedTask.UserEnergy)                              // 10 + 15 = 25
		assert.Equal(originalCreateTime, updatedTask.CreateTime)              // 创建时间不变
		assert.GreaterOrEqual(updatedTask.ModifiedTime, originalModifiedTime) // 修改时间应该 >= 原时间
	})

	t.Run("多次累加更新", func(t *testing.T) {
		assert := assert.New(t)
		require := require.New(t)

		testUserID := int64(12345680)
		testTaskID := int64(87654323)
		testRoomID := int64(11223346)

		// 清理测试数据
		require.NoError(LiveFansBoxUserTask{}.DB().Delete("", "user_id = ? AND fans_box_task_id = ?", testUserID, testTaskID).Error)

		// 第一次创建记录
		err := UpsertUserEnergy(nil, testRoomID, testUserID, testTaskID, 10)
		require.NoError(err)

		// 第二次更新
		err = UpsertUserEnergy(nil, testRoomID, testUserID, testTaskID, 15)
		require.NoError(err)

		// 第三次更新
		err = UpsertUserEnergy(nil, testRoomID, testUserID, testTaskID, 5)
		require.NoError(err)

		// 验证最终结果
		finalTask, err := FindUserTask(testTaskID, testUserID)
		require.NoError(err)
		require.NotNil(finalTask)
		assert.Equal(testRoomID, finalTask.RoomID)
		assert.Equal(testUserID, finalTask.UserID)
		assert.Equal(testTaskID, finalTask.FansBoxTaskID)
		assert.Equal(30, finalTask.UserEnergy) // 10 + 15 + 5 = 30
	})
}
