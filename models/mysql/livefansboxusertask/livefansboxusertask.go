package livefansboxusertask

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// LiveFansBoxUserTask 粉丝团宝箱用户任务表
type LiveFansBoxUserTask struct {
	ID           int64 `gorm:"column:id;primary_key"` // 主键
	CreateTime   int64 `gorm:"column:create_time"`    // 创建时间，单位：秒
	ModifiedTime int64 `gorm:"column:modified_time"`  // 更新时间，单位：秒

	RoomID        int64 `gorm:"column:room_id"`          // 直播间 ID
	UserID        int64 `gorm:"column:user_id"`          // 用户 ID
	FansBoxTaskID int64 `gorm:"column:fans_box_task_id"` // 粉丝团宝箱任务 ID（同 live_fans_box_task 表 id）
	UserEnergy    int   `gorm:"column:user_energy"`      // 用户当日贡献的能量值
}

const tableName = "live_fans_box_user_task"

// TableName for current model
func (LiveFansBoxUserTask) TableName() string {
	return tableName
}

// DB .
func (l LiveFansBoxUserTask) DB() *gorm.DB {
	return service.LiveDB.Table(l.TableName())
}

// BeforeCreate automatically sets columns create_time and modified_time
func (l *LiveFansBoxUserTask) BeforeCreate() (err error) {
	now := goutil.TimeNow().Unix()
	l.CreateTime = now
	l.ModifiedTime = now

	return nil
}

// BeforeUpdate automatically sets column modified_time
func (l *LiveFansBoxUserTask) BeforeUpdate() (err error) {
	l.ModifiedTime = goutil.TimeNow().Unix()
	return nil
}

// FindUserTask 查找用户的任务记录
func FindUserTask(taskID, userID int64) (*LiveFansBoxUserTask, error) {
	userTask := &LiveFansBoxUserTask{}
	err := userTask.DB().
		Where("fans_box_task_id = ? AND user_id = ?", taskID, userID).
		Take(userTask).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, nil
		}
		return nil, err
	}
	return userTask, nil
}

// HasContributedEnergy 用户今日是否对某直播间贡献过能量值
func HasContributedEnergy(roomID, userID int64) (bool, error) {
	todayStartUnix := goutil.BeginningOfDay(goutil.TimeNow()).Unix()
	return servicedb.Exists(LiveFansBoxUserTask{}.DB().
		Where("room_id = ? AND user_id = ? AND create_time >= ? AND user_energy > 0",
			roomID, userID, todayStartUnix))
}

// UpsertUserEnergy 在事务中创建或更新用户的能量贡献记录
func UpsertUserEnergy(tx *gorm.DB, roomID, userID, taskID int64, energyAdd int) error {
	if tx == nil {
		tx = LiveFansBoxUserTask{}.DB()
	}

	// 先尝试创建新记录
	newUserTask := &LiveFansBoxUserTask{
		RoomID:        roomID,
		UserID:        userID,
		FansBoxTaskID: taskID,
		UserEnergy:    energyAdd,
	}

	err := tx.Create(newUserTask).Error
	if err != nil {
		if servicedb.IsUniqueError(err) {
			// 唯一键冲突，表示记录已存在，执行更新操作
			return tx.Where("fans_box_task_id = ? AND user_id = ?", taskID, userID).Updates(map[string]interface{}{
				"user_energy": gorm.Expr("user_energy + ?", energyAdd),
			}).Error
		}
		// 不是唯一键冲突，返回原始错误
		return err
	}
	return nil
}
