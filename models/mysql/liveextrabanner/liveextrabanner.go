package liveextrabanner

import (
	"time"

	"github.com/jinzhu/gorm"
	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// 直播通栏位置
const (
	PositionExtraBanner1 = 1 // 【首页 - 推荐】第一个轮播通栏
	PositionExtraBanner2 = 2 // 【首页 - 推荐】第二个轮播通栏
	PositionExtraBanner3 = 3 // 【首页 - 推荐】第三个轮播通栏
	PositionExtraBanner4 = 4 // 【首页 - 推荐】第四个轮播通栏
)

// PositionExtraBannerList 直播通栏位置列表
var PositionExtraBannerList = []int{
	PositionExtraBanner1,
	PositionExtraBanner2,
	PositionExtraBanner3,
	PositionExtraBanner4,
}

// maxLiveExtraBanner 最多获取直播通栏数（兜底）
const maxLiveExtraBanner = 100

// LiveExtraBanner 直播通栏表
type LiveExtraBanner struct {
	ID           int64  `gorm:"column:id;primary_key"`
	CreateTime   int64  `gorm:"column:create_time"`   // 创建时间戳，单位：秒
	ModifiedTime int64  `gorm:"column:modified_time"` // 更新时间戳，单位：秒
	Position     int    `gorm:"column:position"`      // 直播通栏所在位置
	UserID       int64  `gorm:"column:user_id"`       // 用户 ID
	RoomID       int64  `gorm:"column:room_id"`       // 房间 ID
	Cover        string `gorm:"column:cover"`         // 通栏图地址

	CoverURL string `gorm:"-"`
}

// TableName for current model
func (LiveExtraBanner) TableName() string {
	return "live_extra_banner"
}

// DB the db instance of LiveExtraBanner model
func (b LiveExtraBanner) DB() *gorm.DB {
	return service.LiveDB.Table(b.TableName())
}

// AfterFind is a GORM hook for query
func (b *LiveExtraBanner) AfterFind() error {
	b.CoverURL = storage.ParseSchemeURL(b.Cover)
	return nil
}

const cache10s = 10 * time.Second

// GetAllPositionBannersMap 获取直播通栏图列表
func GetAllPositionBannersMap(positionList []int) (map[int][]LiveExtraBanner, error) {
	cacheKey := keys.LocalKeyLiveExtraBannersMap0.Format()
	var liveExtraBannersMap map[int][]LiveExtraBanner
	v, ok := service.Cache5Min.Get(cacheKey)
	if ok {
		liveExtraBannersMap, ok = v.(map[int][]LiveExtraBanner)
		if !ok {
			return map[int][]LiveExtraBanner{}, nil
		}
	} else {
		cacheTime := cache.DefaultExpiration
		var liveExtraBanners []LiveExtraBanner
		// 获取全部直播通栏数据，该表的数据量一般几十条
		err := LiveExtraBanner{}.DB().Limit(maxLiveExtraBanner).Find(&liveExtraBanners).Error
		if err != nil {
			logger.Errorf("获取直播通栏图错误：%v", err)
			// PASS
			cacheTime = cache10s
			liveExtraBannersMap = map[int][]LiveExtraBanner{}
		} else {
			liveExtraBannersMap = make(map[int][]LiveExtraBanner, len(PositionExtraBannerList))
			for _, banner := range liveExtraBanners {
				liveExtraBannersMap[banner.Position] = append(liveExtraBannersMap[banner.Position], banner)
			}
		}
		service.Cache5Min.Set(cacheKey, liveExtraBannersMap, cacheTime)
	}

	res := make(map[int][]LiveExtraBanner, len(positionList))
	for _, position := range positionList {
		if banners, ok := liveExtraBannersMap[position]; ok {
			copyBanners := make([]LiveExtraBanner, len(banners))
			copy(copyBanners, banners)
			res[position] = copyBanners
		}
	}
	return res, nil
}

// ListRandomRecommendRooms 随机获取推荐直播间
// 主播白名单用的是轮播通栏位置 1 的主播名单，随机获取指定个数开播直播间
func ListRandomRecommendRooms(size int64) ([]*room.Simple, error) {
	position1BannersMap, err := GetAllPositionBannersMap([]int{PositionExtraBanner1})
	if err != nil {
		return nil, err
	}

	roomIDs := make([]int64, 0, len(position1BannersMap[PositionExtraBanner1]))
	position1Banners, ok := position1BannersMap[PositionExtraBanner1]
	if !ok || len(position1Banners) == 0 {
		return []*room.Simple{}, nil
	}
	for _, positionBanner := range position1Banners {
		roomIDs = append(roomIDs, positionBanner.RoomID)
	}

	return room.RandomOpenList(bson.M{
		"room_id":     bson.M{"$in": util.Uniq(roomIDs)},
		"status.open": room.StatusOpenTrue,
	}, size)
}
