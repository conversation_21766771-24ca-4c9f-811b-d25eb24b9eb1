package liveextrabanner

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testUserID = 2343
	testRoomID = 1234
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(LiveExtraBanner{}, "id", "create_time", "modified_time", "position", "user_id", "room_id", "cover")
}

func TestLiveExtraBanner_TableName(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("live_extra_banner", LiveExtraBanner{}.TableName())
}

func TestGetAllPositionBannersMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有对应位置的直播通栏图
	cacheKey := keys.LocalKeyLiveExtraBannersMap0.Format()
	service.Cache5Min.Delete(cacheKey)
	require.NoError(LiveExtraBanner{}.DB().
		Delete("", "user_id = ? AND room_id = ?", testUserID, testRoomID).Error)
	res, err := GetAllPositionBannersMap([]int{1})
	require.NoError(err)
	assert.Len(res, 0)

	// 测试获取直播通栏图
	service.Cache5Min.Delete(cacheKey)
	banner := &LiveExtraBanner{
		Position: PositionExtraBanner1,
		UserID:   testUserID,
		RoomID:   testRoomID,
		Cover:    "oss://abc.png",
	}
	require.NoError(banner.DB().Create(banner).Error)
	res, err = GetAllPositionBannersMap([]int{1})
	require.NoError(err)
	assert.Len(res, 1)
	position1, ok := res[PositionExtraBanner1]
	require.True(ok)
	assert.Len(position1, 1)
	assert.EqualValues(testUserID, position1[0].UserID)
	assert.EqualValues(testRoomID, position1[0].RoomID)
	assert.Equal("https://static-test.missevan.com/abc.png", position1[0].CoverURL)

	// 测试从缓存中获取直播通栏图
	require.NoError(LiveExtraBanner{}.DB().
		Delete("", "user_id = ? AND room_id = ?", testUserID, testRoomID).Error)
	res, err = GetAllPositionBannersMap([]int{1})
	require.NoError(err)
	assert.Len(res, 1)
	position1, ok = res[PositionExtraBanner1]
	require.True(ok)
	assert.Len(position1, 1)
	assert.EqualValues(testUserID, position1[0].UserID)
	assert.EqualValues(testRoomID, position1[0].RoomID)
	assert.Equal("https://static-test.missevan.com/abc.png", position1[0].CoverURL)
}

func TestListRandomRecommendRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 主播白名单为空
	cacheKey := keys.LocalKeyLiveExtraBannersMap0.Format()
	service.Cache5Min.Delete(cacheKey)
	require.NoError(LiveExtraBanner{}.DB().Delete("", "position = ?", PositionExtraBanner1).Error)
	roomID, err := ListRandomRecommendRooms(1)
	require.NoError(err)
	assert.Len(roomID, 0)

	// 测试获取房间列表
	service.Cache5Min.Delete(cacheKey)
	banners := []LiveExtraBanner{
		{
			Position: PositionExtraBanner1,
			RoomID:   4381915,
		},
		{
			Position: PositionExtraBanner1,
			RoomID:   516,
		},
		{
			Position: PositionExtraBanner1,
			RoomID:   1003,
		},
		{
			Position: PositionExtraBanner1,
			RoomID:   5202465,
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, banners[0].TableName(), banners))
	roomID, err = ListRandomRecommendRooms(3)
	require.NoError(err)
	assert.Len(roomID, 3)
}
