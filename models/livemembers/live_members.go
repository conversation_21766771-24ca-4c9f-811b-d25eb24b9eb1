package livemembers

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// CollectionName collection name without prefix
const CollectionName = "live_members"

// AdminLimit 每个直播间的房管最大数量
const AdminLimit = 100

// adminExpireDuration 房管过期时间，100 年
const adminExpireDuration = time.Duration(876000 * time.Hour)

// status
const (
	StatusNormal = iota
	StatusAdmin
	StatusMute     // 包含房间内禁言、全站禁言
	StatusHornMute // 全站喇叭封禁
)

// Member document in collection live_members
type Member struct {
	OID    primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Helper `bson:",inline"`
}

// Helper for LiveMember
// NOTICE: 全站禁言时字段 RoomID 值为 0
type Helper struct {
	RoomOID     primitive.ObjectID `bson:"_room_id,omitempty" json:"-"`
	RoomID      int64              `bson:"room_id" json:"room_id"`
	UserID      int64              `bson:"user_id" json:"user_id"`
	Username    string             `bson:"-" json:"username"` // TODO: 移除数据库中该字段存取
	IconURL     string             `bson:"-" json:"iconurl"`  // TODO: 移除数据库中该字段存取
	OperatorID  int64              `bson:"operator_id" json:"-"`
	Status      int                `bson:"status" json:"-"`
	ExpireAt    *time.Time         `bson:"expire_at" json:"expire_at,omitempty"`
	CreatedTime time.Time          `bson:"created_time,omitempty" json:"-"`
	UpdatedTime time.Time          `bson:"updated_time" json:"-"`
}

// Mute 房间内禁言、全站禁言信息
type Mute struct {
	RoomMute, GlobalMute bool
}

// Collection 返回 Member 的 Collection
func Collection() *mongo.Collection {
	return service.MongoDB.Collection(CollectionName)
}

// FindMemberOptions ListMembers 选项
type FindMemberOptions struct {
	FindUserInfo bool
	OnlyAdmin    bool
}

// ListMembers 列出 roomOID 所属 members
func ListMembers(roomOID primitive.ObjectID, opt *FindMemberOptions) (admin, mute []*Member, err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	admin, mute = make([]*Member, 0), make([]*Member, 0)
	filter := bson.M{"_room_id": roomOID}
	if opt != nil && opt.OnlyAdmin {
		filter["status"] = StatusAdmin
	} else {
		filter["status"] = bson.M{"$gt": StatusNormal}
	}
	cur, err := collection.Find(ctx, filter)
	if err != nil {
		return
	}
	all := make([]*Member, 0)
	err = cur.All(ctx, &all)
	if err != nil {
		return
	}
	if len(all) == 0 {
		return
	}
	if opt != nil && opt.FindUserInfo {
		err = buildUserInfo(all)
		if err != nil {
			return
		}
	}
	for i := 0; i < len(all); i++ {
		switch all[i].Status {
		case StatusAdmin:
			all[i].ExpireAt = nil // 房管的过期时间（永久）不需要返回
			admin = append(admin, all[i])
		case StatusMute:
			mute = append(mute, all[i])
		}
	}
	return
}

func buildUserInfo(members []*Member) error {
	userIDs := make([]int64, 0, len(members))
	for i := range members {
		userIDs = append(userIDs, members[i].UserID)
	}
	if len(userIDs) == 0 {
		return nil
	}
	userMap, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": userIDs}}, nil))
	if err != nil {
		return err
	}
	for i := range members {
		member := members[i]
		u := userMap[member.UserID]
		if u == nil {
			continue
		}
		member.Username = u.Username
		member.IconURL = u.IconURL
	}
	return nil
}

// CountAdmins 获取房管数量
func CountAdmins(roomID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	count, err := collection.CountDocuments(ctx, bson.M{
		"room_id": roomID,
		"status":  StatusAdmin,
		// "expire_at": bson.M{"$gt": goutil.TimeNow()}, // 房管目前无过期设定
	})
	if err != nil {
		return 0, err
	}
	return count, nil
}

// IsRoomAdmin 是否房管
func IsRoomAdmin(roomOID primitive.ObjectID, userID int64) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)

	var user struct {
		ID int64 `bson:"user_id"`
	}
	err := collection.FindOne(ctx, bson.M{
		"_room_id": roomOID,
		"status":   StatusAdmin,
		"user_id":  userID,
	}, options.FindOne().SetProjection(bson.M{"user_id": 1})).Decode(&user)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// SetRoomAdmin 设置房管，设置为房管时将解除其在该房间内的禁言
func SetRoomAdmin(user *liveuser.User, roomOID primitive.ObjectID, roomID, operatorID int64, set bool) (isRemoveMute bool, err error) {
	now := goutil.TimeNow()
	if !set {
		// 若为取消房管，则查找并删除记录
		_, err := FindOneAndDelete(bson.M{"_room_id": roomOID, "user_id": user.UID, "status": StatusAdmin})
		if err != nil {
			return isRemoveMute, err
		}
		// 删除相关缓存
		delRoomAdminCache(roomID, false)
		return isRemoveMute, nil
	}
	// 房管过期时间点
	expireAt := now.Add(adminExpireDuration)
	updates := make([]mongo.WriteModel, 2)
	// 设置房管时移除其禁言
	updates[0] = mongo.NewDeleteOneModel().SetFilter(bson.M{
		"user_id": user.UID,
		"room_id": roomID,
		"status":  StatusMute,
	})
	updates[1] = mongo.NewUpdateOneModel().SetFilter(bson.M{"_room_id": roomOID, "user_id": user.UserID()}).
		SetUpsert(true).SetUpdate(
		bson.M{
			"$set": Helper{
				RoomOID:     roomOID,
				RoomID:      roomID,
				UserID:      user.UID,
				Username:    user.Username,
				IconURL:     user.IconURL,
				OperatorID:  operatorID,
				Status:      StatusAdmin,
				ExpireAt:    &expireAt,
				UpdatedTime: now,
			},
			"$setOnInsert": bson.M{
				"created_time": now,
			},
		})
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := Collection().BulkWrite(ctx, updates)
	if err != nil {
		return false, err
	}
	// 删除相关缓存
	delRoomAdminCache(roomID, true)
	return res.DeletedCount == 1, err
}

func delRoomAdminCache(roomID int64, deleteMuteCache bool) {
	// 删除相关缓存（相关缓存将在使用处重新生成）
	delKeys := make([]string, 0, 2)
	adminCacheKey := keys.KeyRoomAdmin1.Format(roomID)
	delKeys = append(delKeys, adminCacheKey)
	if deleteMuteCache {
		// 若需要删除房间内禁言缓存，则一并删除
		muteCacheKey := keys.KeyRoomMute1.Format(roomID)
		delKeys = append(delKeys, muteCacheKey)
	}
	err := service.Redis.Del(delKeys...).Err()
	if err != nil {
		logger.WithField("room_id", roomID).Errorf("Delete room's admin cache failed: %v", err)
		// PASS
	}
}

func delRoomMuteCache(roomID int64) {
	// 删除房间禁言缓存（该缓存将在使用处重新生成）
	cacheKey := keys.KeyRoomMute1.Format(roomID)
	err := service.Redis.Del(cacheKey).Err()
	if err != nil {
		logger.WithField("room_id", roomID).Errorf("Delete room's mute cache failed: %v", err)
		// PASS
	}
}

// SetMute 添加禁言信息
// Notice: 当根据 filter 查询的数据不存在时插入 $set 和 $setOnInsert 指定的值,
// 存在时则只更新 $set 指定字段值
func (m *Member) SetMute() (err error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// NOTICE: 全站禁言时字段 room_id 值为 0
	filter := bson.M{"user_id": m.UserID, "room_id": 0}
	_, err = Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"username":     m.Username,
				"iconurl":      m.IconURL,
				"operator_id":  m.OperatorID,
				"expire_at":    m.ExpireAt,
				"updated_time": m.UpdatedTime,
			},
			"$setOnInsert": bson.M{
				"user_id":      m.UserID,
				"status":       m.Status,
				"room_id":      0,
				"created_time": m.CreatedTime,
			},
		},
		options.Update().SetUpsert(true))

	return
}

// FindOneAndDelete 删除 member
func FindOneAndDelete(filter bson.M) (bool, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := Collection().FindOneAndDelete(ctx, filter).Err()
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// GlobalMuteByUserID 获取用户全局禁言具体信息
func GlobalMuteByUserID(userID int64) (*Member, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": userID, "status": StatusMute, "room_id": 0}
	m := new(Member)
	err := Collection().FindOne(ctx, filter, options.FindOne().
		SetProjection(bson.M{"user_id": 1, "expire_at": 1, "room_id": 1})).Decode(&m)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return m, nil
}

// IsMute 判断是否被禁言（包含房间内禁言和全站禁言）
func IsMute(userID, roomID int64) (*Mute, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"user_id": userID,
		"status":  StatusMute,
		"room_id": bson.M{"$in": [2]int64{0, roomID}}, // 0 全站禁言
	}

	mute := new(Mute)
	cur, err := Collection().Find(ctx, filter, options.Find().SetProjection(bson.M{"room_id": 1}))
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	members := make([]*Member, 0)
	err = cur.All(ctx, &members)
	if err != nil {
		return nil, err
	}
	for _, m := range members {
		if m.RoomID == 0 {
			mute.GlobalMute = true
		} else {
			mute.RoomMute = true
		}
	}
	return mute, err
}

// RemoveRoomMute 解除直播间内禁言
func RemoveRoomMute(userID, roomID int64) (bool, error) {
	filter := bson.M{
		"user_id": userID,
		"room_id": roomID,
		"status":  StatusMute,
	}
	result, err := FindOneAndDelete(filter)
	if err == nil && result {
		// 若数据删除成功，则删除相关缓存
		delRoomMuteCache(roomID)
	}
	return result, err
}

// FindHornMute 查询用户全站喇叭封禁记录
func FindHornMute(userID int64) (*Member, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"user_id": userID,
		"status":  StatusHornMute,
	}
	mute := new(Member)
	err := Collection().FindOne(ctx, filter).Decode(mute)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, nil
		}
		return nil, err
	}
	return mute, nil
}
