package livemembers

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	m.Run()
}

func TestListMembers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		roomOID    = primitive.NewObjectID()
		now        = goutil.TimeNow()
		expireTime = now.Add(time.Minute)
	)

	members := []interface{}{
		Member{
			Helper: Helper{
				RoomOID:     roomOID,
				UserID:      666666,
				Status:      StatusAdmin,
				ExpireAt:    &expireTime,
				CreatedTime: now,
			},
		},
		Member{
			Helper: Helper{
				RoomOID:     roomOID,
				UserID:      55555,
				Status:      StatusMute,
				ExpireAt:    &expireTime,
				CreatedTime: now,
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().InsertMany(ctx, members)
	require.NoError(err)

	admin, mute, err := ListMembers(roomOID, nil)
	require.NoError(err)
	assert.NotEmpty(admin)
	assert.NotEmpty(mute)

	admin, mute, err = ListMembers(roomOID, &FindMemberOptions{OnlyAdmin: true})
	require.NoError(err)
	assert.NotEmpty(admin)
	assert.Empty(mute)

	_, err = Collection().DeleteMany(ctx, bson.M{"_room_id": roomOID})
	require.NoError(err)
	admin, mute, err = ListMembers(roomOID, nil)
	require.NoError(err)
	assert.Empty(admin)
	assert.Empty(mute)
}

func TestBuildUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	members := []*Member{
		{Helper: Helper{UserID: 10}},
		{Helper: Helper{UserID: 12}},
	}
	require.NoError(buildUserInfo(members))
	assert.NotEmpty(members[0].Username)
	assert.NotEmpty(members[1].Username)
	assert.NotEmpty(members[0].IconURL)
	assert.NotEmpty(members[1].IconURL)
}

func TestIsRoomAdmin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	collection := service.MongoDB.Collection(CollectionName)
	_id, err := primitive.ObjectIDFromHex("5d302efbbc1d4f1a52e95ce3")
	require.NoError(err)
	_roomID, err := primitive.ObjectIDFromHex("5822dc0df5a0bcc06268ba85")
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = collection.ReplaceOne(ctx, bson.M{"_id": _id},
		bson.M{
			"room_id":  4381915,
			"_room_id": _roomID,
			"user_id":  1,
			"username": "mowangsk",
			"status":   StatusAdmin,
		}, options.Replace().SetUpsert(true))
	require.NoError(err)

	b, err := IsRoomAdmin(_roomID, 1)
	require.NoError(err)
	assert.True(b)

	b, err = IsRoomAdmin(_roomID, 0)
	require.NoError(err)
	assert.False(b)
}

func TestInsertOneAndRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	when := goutil.TimeNow().Add(5 * time.Minute)
	m := Member{
		Helper: Helper{
			UserID:      55555,
			Username:    "test",
			Status:      StatusMute,
			ExpireAt:    &when,
			CreatedTime: when,
		},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": m.UserID, "status": m.Status, "room_id": m.RoomID}
	_, err := Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	// 首次插入
	require.NoError(m.SetMute())
	r := new(Helper)
	require.NoError(Collection().FindOne(ctx, filter, options.FindOne().
		SetProjection(bson.M{"user_id": 1, "expire_at": 1, "created_time": 1})).Decode(&r))
	assert.Equal(m.UserID, r.UserID)
	assert.Equal(when.Unix(), m.CreatedTime.Unix())
	assert.Equal(m.ExpireAt.Unix(), r.ExpireAt.Unix())

	// 已存在更新
	r = new(Helper)
	expireTime := when.Add(5 * time.Minute)
	m.ExpireAt = &expireTime
	m.Username = "exists"
	require.NoError(m.SetMute())
	require.NoError(Collection().FindOne(ctx, filter, options.FindOne().
		SetProjection(bson.M{"user_id": 1, "expire_at": 1, "created_time": 1})).Decode(&r))
	assert.Equal(m.UserID, r.UserID)
	assert.Equal(when.Unix(), m.CreatedTime.Unix())
	assert.Equal(m.ExpireAt.Unix(), r.ExpireAt.Unix())

	// 删除已存在的
	count, err := FindOneAndDelete(filter)
	require.NoError(err)
	assert.True(count)

	// 删除不存在的
	count, err = FindOneAndDelete(filter)
	require.NoError(err)
	assert.False(count)
}

func TestGlobalMuteByUserID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expireTime := goutil.TimeNow().Add(time.Minute)
	m := Helper{
		UserID:   99999,
		Status:   StatusMute,
		ExpireAt: &expireTime,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": m.UserID, "status": m.Status, "room_id": m.RoomID}
	_, err := Collection().UpdateOne(ctx, filter, bson.M{"$set": m}, options.Update().SetUpsert(true))
	require.NoError(err)
	// 全站禁言用户
	r, err := GlobalMuteByUserID(m.UserID)
	require.NoError(err)
	assert.Equal(m.UserID, r.UserID)

	// 非全站禁言用户
	r, err = GlobalMuteByUserID(1234567890)
	require.NoError(err)
	assert.Empty(r)
}

func TestIsMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expireTime := goutil.TimeNow().Add(time.Minute)
	m := Helper{
		UserID:   int64(99999),
		Status:   StatusMute,
		ExpireAt: &expireTime,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"user_id": m.UserID, "status": StatusMute, "room_id": 0}
	_, err := Collection().UpdateOne(ctx, filter, bson.M{"$set": m}, options.Update().SetUpsert(true))
	require.NoError(err)
	m.RoomID = 12345
	filter["room_id"] = m.RoomID
	_, err = Collection().UpdateOne(ctx, filter, bson.M{"$set": m}, options.Update().SetUpsert(true))
	require.NoError(err)

	mute, err := IsMute(m.UserID, m.RoomID)
	require.NoError(err)
	assert.True(mute.RoomMute)
	assert.True(mute.GlobalMute)
}

func TestCountAdmins(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 清理脏数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testRoomID := int64(99999)
	collection := service.MongoDB.Collection(CollectionName)
	_, err := collection.DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)

	// 测试无记录的情况
	testUserID := int64(99999)
	adminCount, err := CountAdmins(testRoomID)
	require.NoError(err)
	assert.Zero(adminCount)

	// 测试有记录的情况
	require.NoError(err)
	// 添加记录
	now := goutil.TimeNow()
	expireTime := now.AddDate(0, 0, 1)
	helper := Helper{
		RoomID:      testRoomID,
		UserID:      testUserID,
		Status:      StatusAdmin,
		ExpireAt:    &expireTime,
		CreatedTime: now,
		UpdatedTime: now,
	}
	_, err = collection.InsertOne(ctx, &helper)
	require.NoError(err)
	adminCount, err = CountAdmins(testRoomID)
	require.NoError(err)
	assert.Equal(int64(1), adminCount)

	// 测试有记录但是 status 不为房管的情况
	// 修改记录类型为非房管
	_, err = collection.UpdateOne(ctx, bson.M{"room_id": testRoomID, "user_id": testUserID},
		bson.M{"$set": bson.M{"status": StatusMute}})
	require.NoError(err)
	adminCount, err = CountAdmins(testRoomID)
	require.NoError(err)
	assert.Zero(adminCount)
}

func TestSetRoomAdmin(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除脏数据
	testRoomID := int64(99999)
	testRoomCreatorID := int64(99999)
	testUserID := int64(248506)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(CollectionName)
	_, err := collection.DeleteMany(ctx, bson.M{"room_id": testRoomID, "user_id": testUserID})
	require.NoError(err)

	// 测试给用户设置房管的情况
	user, err := liveuser.Find(testUserID)
	require.NoError(err)
	testRoomOID, err := primitive.ObjectIDFromHex("5d302efbbc1d4f1a52e95ce3")
	require.NoError(err)
	// 生成缓存，用于验证该缓存在设置房管后被清理
	adminCacheKey := keys.KeyRoomAdmin1.Format(testRoomID)
	require.NoError(service.Redis.Set(adminCacheKey, "233", 1*time.Minute).Err())
	removeMute, err := SetRoomAdmin(user, testRoomOID, testRoomID, testRoomCreatorID, true)
	require.NoError(err)
	assert.False(removeMute)
	// 验证相关数据是否生成
	err = collection.FindOne(ctx, bson.M{
		"room_id": testRoomID,
		"status":  StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.NoError(err)
	// 验证相关缓存被清理
	_, err = service.Redis.Get(adminCacheKey).Result()
	assert.Equal(redis.Nil, err)

	// 测试取消房管的情况
	// 生成缓存，用于验证该缓存在取消房管后被清理
	require.NoError(service.Redis.Set(adminCacheKey, "233", 1*time.Minute).Err())
	removeMute, err = SetRoomAdmin(user, testRoomOID, testRoomID, testRoomCreatorID, false)
	require.NoError(err)
	assert.False(removeMute)
	err = collection.FindOne(ctx, bson.M{
		"room_id": testRoomID,
		"status":  StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	assert.Equal(mongo.ErrNoDocuments, err)
	// 测试禁言情况下设置房管的情况
	_, err = Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID, "user_id": testUserID},
		bson.M{
			"$set": bson.M{
				"username":     "test",
				"iconurl":      "test.jpg",
				"operator_id":  1,
				"expire_at":    1,
				"updated_time": 1,
				"status":       StatusMute,
			},
			"$setOnInsert": bson.M{
				"user_id":      testUserID,
				"room_id":      testRoomID,
				"created_time": 1,
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	removeMute, err = SetRoomAdmin(user, testRoomOID, testRoomID, testRoomCreatorID, true)
	require.NoError(err)
	assert.True(removeMute)
	// 验证相关数据是否生成
	err = collection.FindOne(ctx, bson.M{
		"room_id": testRoomID,
		"status":  StatusAdmin,
		"user_id": testUserID,
	}, options.FindOne().SetProjection(bson.M{"_id": 1})).Err()
	require.NoError(err)
	admin, err := service.Redis.SIsMember(adminCacheKey, testUserID).Result()
	require.NoError(err)
	assert.False(admin)
	muteCacheKey := keys.KeyRoomMute1.Format(testRoomID)
	mute, err := service.Redis.SIsMember(muteCacheKey, testUserID).Result()
	require.NoError(err)
	assert.False(mute)
}

func TestRemoveRoomMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 新增测试数据
	testUserID := int64(23333)
	testRoomID := int64(99999)
	filter := bson.M{"user_id": testUserID, "room_id": testRoomID}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := Collection().UpdateOne(ctx, filter,
		bson.M{
			"$set": bson.M{
				"username":     "test",
				"iconurl":      "test.jpg",
				"operator_id":  1,
				"expire_at":    1,
				"updated_time": 1,
				"status":       StatusMute,
			},
			"$setOnInsert": bson.M{
				"user_id":      testUserID,
				"room_id":      testRoomID,
				"created_time": 1,
			},
		},
		options.Update().SetUpsert(true))
	require.NoError(err)
	muteCacheKey := keys.KeyRoomMute1.Format(testRoomID)
	require.NoError(service.Redis.SAdd(muteCacheKey, testUserID).Err())

	// 测试已被禁言的情况
	removeMute, err := RemoveRoomMute(testUserID, testRoomID)
	require.NoError(err)
	assert.True(removeMute)
	cacheMute, err := service.Redis.SIsMember(muteCacheKey, testUserID).Result()
	require.NoError(err)
	// 缓存被删除，故为 false
	assert.False(cacheMute)

	// 验证禁言已被取消
	mute, err := IsMute(testUserID, testRoomID)
	require.NoError(err)
	assert.False(mute.RoomMute)

	// 验证未被禁言的情况
	removeMute, err = RemoveRoomMute(testUserID, testRoomID)
	require.NoError(err)
	assert.False(removeMute)
}

func TestDelRoomAdminCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(1)
	adminCacheKey := keys.KeyRoomAdmin1.Format(testRoomID)
	require.NoError(service.Redis.SAdd(adminCacheKey, 1).Err())
	muteCacheKey := keys.KeyRoomMute1.Format(testRoomID)
	require.NoError(service.Redis.SAdd(muteCacheKey, 1).Err())

	// 测试不删除房间内禁言缓存的情况
	delRoomAdminCache(testRoomID, false)
	admin, err := service.Redis.SIsMember(adminCacheKey, 1).Result()
	require.NoError(err)
	assert.False(admin)
	mute, err := service.Redis.SIsMember(muteCacheKey, 1).Result()
	require.NoError(err)
	assert.True(mute)

	// 测试删除房间内禁言缓存的情况
	require.NoError(service.Redis.SAdd(adminCacheKey, 1).Err())
	delRoomAdminCache(testRoomID, true)
	admin, err = service.Redis.SIsMember(adminCacheKey, 1).Result()
	require.NoError(err)
	assert.False(admin)
	mute, err = service.Redis.SIsMember(muteCacheKey, 1).Result()
	require.NoError(err)
	assert.False(mute)
}

func TestDelRoomMuteCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(1)
	muteCacheKey := keys.KeyRoomMute1.Format(testRoomID)
	require.NoError(service.Redis.SAdd(muteCacheKey, 1).Err())
	delRoomMuteCache(testRoomID)
	mute, err := service.Redis.SIsMember(muteCacheKey, 1).Result()
	require.NoError(err)
	assert.False(mute)
}

func TestFindHornMute(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	filter := bson.M{"user_id": 9074509}
	_, err := FindOneAndDelete(filter)
	require.NoError(err)

	member, err := FindHornMute(9074509)
	require.NoError(err)
	assert.Nil(member)

	now := goutil.TimeNow()
	expireAt := now.Add(time.Minute)
	m := &Member{
		Helper: Helper{
			RoomID:      0,
			UserID:      9074509,
			CreatedTime: now,
			ExpireAt:    &expireAt,
			Status:      StatusHornMute,
			UpdatedTime: now,
			OperatorID:  9074511,
		},
	}
	require.NoError(m.SetMute())

	member, err = FindHornMute(9074509)
	require.NoError(err)
	assert.NotNil(member)
}
