package attentionuser

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/person"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// WORKAROUND: 使用 `live.TableName()` 会导致 import cycle 错误
const liveTableName = "live"

const (
	// URLFollow 关注用户
	URLFollow = "go://person/follow"
	// URLUnfollow 取消关注用户
	URLUnfollow = "go://person/unfollow"
)

const tableName = "m_attention_user"

var cacheInvalidateFuncs []func(userID int64)

// TableName table name
func TableName() string {
	return tableName
}

// AttentionUser 用户关注
type AttentionUser struct {
	ID           int64 `gorm:"column:id;primary_key"`
	UserActive   int64 `gorm:"column:user_active"`   // 发起关注的人
	UserPasstive int64 `gorm:"column:user_passtive"` // 被关注的对象
	Time         int64 `gorm:"column:time"`
}

// TableName table name
func (AttentionUser) TableName() string {
	return tableName
}

// Attention is the result of CheckAttention
type Attention struct {
	UserID   int64 `gorm:"column:id" json:"user_id"`
	FansNum  int64 `gorm:"column:fansnum" json:"fansnum"`
	Followed bool  `gorm:"-" json:"followed"`
}

// CheckAttention 检查 userID 关注了 checkUserIDs 中的哪些；
// userID <= 0 代表游客，此时这个函数获取 checkUserIDs 这些人的粉丝数
func CheckAttention(userID int64, checkUserIDs []int64) (attentions []*Attention, err error) {
	attentions = make([]*Attention, 0)
	if len(checkUserIDs) < 1 {
		return
	}
	err = service.DB.Table(mowangskuser.TableName()).Select("id, fansnum").
		Where("id IN (?)", checkUserIDs).Scan(&attentions).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}

	if userID <= 0 {
		return attentions, nil
	}
	var followed []AttentionUser
	err = service.DB.Where("user_passtive IN (?) AND user_active = ?", checkUserIDs, userID).
		Find(&followed).Error
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}

	followedSet := make(map[int64]struct{})
	for _, v := range followed {
		followedSet[v.UserPasstive] = struct{}{}
	}

	for i := range attentions {
		_, attentions[i].Followed = followedSet[attentions[i].UserID]
	}
	return attentions, nil
}

// AttentionSliceToMap Attention slice 转换成 map[userID]*Attention
func AttentionSliceToMap(s []*Attention) map[int64]*Attention /* map[userID]*Attention */ {
	m := make(map[int64]*Attention, len(s))
	for i := 0; i < len(s); i++ {
		if s[i] != nil {
			m[s[i].UserID] = s[i]
		}
	}
	return m
}

// HasFollowed userID 是否已经关注了 attentionID 的 user
func HasFollowed(userID, attentionID int64) (bool, error) {
	var selected struct {
		Active  int64 `gorm:"column:user_active"`
		Passive int64 `gorm:"column:user_passtive"`
	}
	err := service.DB.Table(TableName()).Select("user_active, user_passtive").
		Where("user_active = ? AND user_passtive = ?", userID, attentionID).Scan(&selected).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// IsUniqueConstraintError 判断非空的 err 是否是 UNIQUE 约束失败的错误，可以判断 Driver 为 mysql 和 sqlite 的情况，当
// Driver 为 mysql 时，需要数据库限制 UNIQUE KEY `idx` (`column1`,....)，当
// Driver 为 sqlite 时，需要数据库限制 UNIQUE(`column1`,....)
// TODO: 需要将 IsUniqueConstraintError 迁移到 missevan-go 里
func IsUniqueConstraintError(err error) bool {
	// Driver 为 mysql 时，因为数据库限制 UNIQUE KEY `idx` (`column1`,....)
	// 出现 UNIQUE 约束失败时，会报 Error 1062: Duplicate entry 'column1-....' for key 'idx' 的错误
	if servicedb.Driver == servicedb.DriverMysql && strings.HasPrefix(err.Error(), "Error 1062: Duplicate entry ") {
		return true
	}
	// Driver 为 sqlite 时，因为数据库限制 UNIQUE(`column1`,....)，
	// 出现 UNIQUE 约束失败时，会报 UNIQUE constraint failed: tablename.column1,.... 的错误
	if servicedb.Driver == servicedb.DriverSqlite && strings.HasPrefix(err.Error(), "UNIQUE constraint failed:") {
		return true
	}
	return false
}

var errNoRowsAffected = errors.New("no rows affected")

// Follow 添加关注记录，更新关注数，粉丝数
func Follow(userID, attentionID int64) error {
	defer flushFollowed(userID)
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		var au AttentionUser
		err := tx.Table(TableName()).Where("user_active = ? AND user_passtive = ?", userID, attentionID).First(&au).Error
		if err == nil {
			return nil
		}
		if err != nil && !gorm.IsRecordNotFoundError(err) {
			return err
		}
		// 此时 gorm.IsRecordNotFoundError(err) 为 true，表示没有找到记录，没有关注记录
		au = AttentionUser{
			UserActive:   userID,
			UserPasstive: attentionID,
			Time:         goutil.TimeNow().Unix(),
		}
		db := tx.Create(&au)
		if err := db.Error; err != nil {
			return err
		}
		// 被关注用户粉丝量 + 1
		err = tx.Table(mowangskuser.TableName()).Where("id = ?", attentionID).
			UpdateColumn("fansnum", gorm.Expr("fansnum + 1")).Error
		if err != nil {
			return err
		}
		// 用户关注数量 + 1
		err = tx.Table(mowangskuser.TableName()).Where("id = ?", userID).
			UpdateColumn("follownum", gorm.Expr("follownum + 1")).Error
		if err != nil {
			return err
		}
		return nil
	})

	// err 是 UNIQUE 约束失败的错误，表明数据库已经存在这条记录了，返回
	// 在上述的 tx.Create(&au) 时可能会产生 UNIQUE 约束失败的错误
	if err != nil && IsUniqueConstraintError(err) {
		return nil
	}
	return err
}

// UnFollow 删除对应的关注记录，更新关注数，粉丝数
func UnFollow(userID, attentionID int64) error {
	defer flushFollowed(userID)
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		var au AttentionUser
		err := tx.Table(TableName()).Where("user_active = ? AND user_passtive = ?", userID, attentionID).First(&au).Error
		if err != nil {
			// 找不到记录表示当前已经没有关注，直接返回
			if gorm.IsRecordNotFoundError(err) {
				return nil
			}
			return err
		}

		db := tx.Exec("DELETE FROM "+TableName()+" WHERE user_passtive = ? AND user_active = ?",
			attentionID, userID)
		err = db.Error
		if err != nil {
			return err
		}
		// Already been deleted
		if db.RowsAffected == 0 {
			return errNoRowsAffected
		}
		// 被关注用户粉丝量 - 1
		err = tx.Table(mowangskuser.TableName()).Where("id = ?", attentionID).
			UpdateColumn("fansnum", gorm.Expr(fmt.Sprintf("%s - 1", servicedb.GreatestExpr("fansnum", "1")))).Error
		if err != nil {
			return err
		}
		// 用户关注数量 - 1
		err = tx.Table(mowangskuser.TableName()).Where("id = ?", userID).
			UpdateColumn("follownum", gorm.Expr(fmt.Sprintf("%s - 1", servicedb.GreatestExpr("follownum", "1")))).Error
		if err != nil {
			return err
		}
		return nil
	})

	if err == errNoRowsAffected {
		return nil
	}
	return err
}

// RPCFollow 用户关注
// NOTICE: 有一方拉黑了另一方时，则无法关注
func RPCFollow(userID, followID int64, c goutil.UserContext) error {
	defer flushFollowed(userID)
	input := map[string]interface{}{
		"user_id":        userID,
		"follow_user_id": followID,
		"follow_from":    person.FollowFromLive,
	}
	if c == nil {
		return service.MRPC.Call(URLFollow, "", input, nil)
	}
	u := userapi.NewUserContext(c)
	return service.MRPC.Call(URLFollow, u.ClientIP, input, nil, u.Cookies())
}

// RPCUnfollow 取关用户
func RPCUnfollow(userID, followedID int64, c goutil.UserContext) error {
	defer flushFollowed(userID)
	input := map[string]interface{}{
		"user_id":        userID,
		"follow_user_id": followedID,
		"follow_from":    person.FollowFromLive,
	}
	if c == nil {
		return service.MRPC.Call(URLUnfollow, "", input, nil)
	}
	u := userapi.NewUserContext(c)
	return service.MRPC.Call(URLUnfollow, u.ClientIP, input, nil, u.Cookies())
}

// AllFollowedUserIDs 某用户关注的所有用户 ID
func AllFollowedUserIDs(userID int64) ([]int64, error) {
	// 尝试从缓存获取
	key := keys.KeyUserFollowed1.Format(userID)
	v, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}
	userIDs := make([]int64, 0)
	if v != "" {
		err = json.Unmarshal([]byte(v), &userIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return userIDs, nil
		}
	}
	// 获取失败，从数据库获取
	err = service.DB.Table(TableName()).Select("user_passtive").
		Where("user_active = ?", userID).Pluck("user_passtive", &userIDs).Error
	if err != nil {
		return nil, err
	}
	// 将查询的数据存入缓存
	b, _ := json.Marshal(userIDs)
	err = service.LRURedis.Set(key, string(b), 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return userIDs, nil
}

// AllFollowedCreatorIDs 某用户关注的所有主播 ID
func AllFollowedCreatorIDs(userID int64) ([]int64, error) {
	// 尝试从缓存获取
	key := keys.KeyUserFollowedCreator1.Format(userID)
	v, err := service.LRURedis.Get(key).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		// PASS
	}
	creatorIDs := make([]int64, 0)
	if v != "" {
		err = json.Unmarshal([]byte(v), &creatorIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		} else {
			return creatorIDs, nil
		}
	}
	// 获取失败，从数据库获取
	// TODO: 考虑将主播身份信息存储到 mowangskuser 表，比如新增一个标识
	err = service.DB.Table(TableName()+" AS ta").Select("ta.user_passtive").
		Joins("INNER JOIN "+liveTableName+" AS tl ON tl.user_id = ta.user_passtive").
		Where("ta.user_active = ?", userID).Pluck("ta.user_passtive", &creatorIDs).Error
	if err != nil {
		return nil, err
	}
	// 将查询的数据存入缓存
	b, _ := json.Marshal(creatorIDs)
	err = service.LRURedis.Set(key, string(b), 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return creatorIDs, nil
}

// RegisterCacheInvalidateFunc 注册清空缓存相关函数
func RegisterCacheInvalidateFunc(cacheKeyFunc func(userID int64)) {
	cacheInvalidateFuncs = append(cacheInvalidateFuncs, cacheKeyFunc)
}

func flushFollowed(userID int64) {
	err := service.LRURedis.Del(
		keys.KeyUserFollowed1.Format(userID),
		keys.KeyUserFollowedCreator1.Format(userID),
	).Err()
	if err != nil {
		logger.WithField("user_id", userID).Error(err)
		// PASS
	}

	for _, f := range cacheInvalidateFuncs {
		f(userID)
	}
}

// FindFansNumMap 查询主播粉丝数，返回 map[userID]*Attention
func FindFansNumMap(userIDs []int64) (map[int64]*Attention /* map[userID]*Attention */, error) {
	if len(userIDs) == 0 {
		return map[int64]*Attention{}, nil
	}

	userIDs = util.Uniq(userIDs)
	attentions, err := CheckAttention(0, userIDs)
	if err != nil {
		return nil, err
	}

	return AttentionSliceToMap(attentions), nil
}

// IsRPCUserError 是否为 RPC user code 错误码
func IsRPCUserError(err error) bool {
	rpcError, ok := err.(*mrpc.ClientError)
	if !ok {
		return false
	}
	switch rpcError.Code {
	case handler.CodeUserFollowLimit, handler.CodeBlockedUser, handler.CodeBlockedUserByOthers:
		return true
	}
	return false
}

// ListAllAttentionUserIDs 获取用户关注的所有用户的 ID 列表
// Deprecated: 功能跟 AllFollowedUserIDs 重复并且没有设置缓存，后续考虑删除
func ListAllAttentionUserIDs(userID int64) ([]int64, error) {
	var userIDs []int64
	db := service.DB.Table(TableName()).Where("user_active = ?", userID)
	if err := db.Pluck("user_passtive", &userIDs).Error; err != nil {
		return nil, err
	}
	return userIDs, nil
}
