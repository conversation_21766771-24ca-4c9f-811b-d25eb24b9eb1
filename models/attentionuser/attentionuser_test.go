package attentionuser

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("m_attention_user", TableName())
	assert.Equal("m_attention_user", AttentionUser{}.TableName())
}

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()

	m.Run()
}

func TestCheckAttention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	attention := new(AttentionUser)
	err := service.DB.FirstOrCreate(attention, &AttentionUser{
		UserActive: 12, UserPasstive: 5, Time: 1562075169,
	}).Error
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)

	checkUserIDs := []int64{1, 2, 3, 4, 5}
	userID := int64(12)
	res, err := CheckAttention(userID, checkUserIDs)
	require.NoError(err)
	assert.True(res[4].Followed)

	b, err := HasFollowed(12, 5)
	require.NoError(err)
	assert.True(b)

	b, err = HasFollowed(12, 12)
	require.NoError(err)
	assert.False(b)
}

func TestFollow(t *testing.T) {
	require := require.New(t)

	testFunc := func(f func(int64, int64) error, a, b int64) {
		require.NoError(f(a, b))
		time.Sleep(500 * time.Millisecond)
	}

	testFunc(UnFollow, 12, 6)
	testFunc(Follow, 12, 6)
	testFunc(Follow, 12, 6)
	testFunc(UnFollow, 12, 6)
	testFunc(UnFollow, 12, 6)
}

func TestAttentionSliceToMap(t *testing.T) {
	assert := assert.New(t)
	s := []*Attention{nil, {UserID: 12}, {UserID: 13}}
	m := AttentionSliceToMap(s)
	assert.Len(m, 2)
	assert.Equal(s[1], m[12])
	assert.Equal(s[2], m[13])
}

func TestRPCFollowAndUnfollow(t *testing.T) {
	assert := assert.New(t)

	cancelFollow := mrpc.SetMock("go://person/follow", func(input interface{}) (output interface{}, err error) {
		return "关注成功", nil
	})
	defer cancelFollow()

	cancelUnfollow := mrpc.SetMock("go://person/unfollow", func(input interface{}) (output interface{}, err error) {
		return "取消关注成功", nil
	})
	defer cancelUnfollow()

	// 普通情况
	var c goutil.SmartUserContext
	assert.NoError(RPCFollow(12, 100, c))
	assert.NoError(RPCUnfollow(12, 100, c))

	// nil 情况
	assert.NoError(RPCFollow(12, 100, nil))
	assert.NoError(RPCUnfollow(12, 100, nil))
}

func TestFlushFollowed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyUserFollowed1.Format(12)
	require.NoError(service.LRURedis.Set(key, "[1,2,3]", 5*time.Second).Err())

	key2 := keys.KeyMedalRoomFollowList2.Format(12, 1)
	require.NoError(service.LRURedis.Set(key2, "[1,2,3]", 5*time.Second).Err())

	RegisterCacheInvalidateFunc(func(userID int64) {
		key := keys.KeyMedalRoomFollowList2.Format(userID, 1)
		require.NoError(service.LRURedis.Del(key).Err())
	})

	flushFollowed(12)
	err := service.LRURedis.Get(key).Err()
	assert.True(serviceredis.IsRedisNil(err))
	err = service.LRURedis.Get(key2).Err()
	assert.True(serviceredis.IsRedisNil(err))
}

func TestAllFollowedUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyUserFollowed1.Format(12)
	// 测试读取错误的缓存
	require.NoError(service.LRURedis.Set(key, "1,2,3", 5*time.Second).Err())
	userIDs, err := AllFollowedUserIDs(12)
	require.NoError(err)
	assert.NotEmpty(userIDs)
	v, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	b, _ := json.Marshal(userIDs)
	assert.JSONEq(string(b), v)
	// 从缓存读取
	require.NoError(service.LRURedis.Set(key, "[1,2,3]", time.Second).Err())
	userIDs, err = AllFollowedUserIDs(12)
	require.NoError(err)
	assert.Equal([]int64{1, 2, 3}, userIDs)
}

func TestAllFollowedCreatorIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(100)
	creatorID := int64(10)
	nonCreatorID := int64(101)

	key := keys.KeyUserFollowedCreator1.Format(userID)
	require.NoError(service.LRURedis.Del(key).Err())
	defer require.NoError(service.LRURedis.Del(key).Err())

	for _, v := range []int64{creatorID, nonCreatorID} {
		attention := &AttentionUser{UserActive: userID, UserPasstive: v}
		require.NoError(service.DB.Create(attention).Error)
		defer service.DB.Delete(attention)
	}

	// 测试从数据库读取
	userIDs, err := AllFollowedCreatorIDs(userID)
	require.NoError(err)
	assert.Equal([]int64{creatorID}, userIDs)

	// 测试正确设置了缓存
	cache, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	assert.JSONEq("[10]", cache)

	// 测试从缓存读取
	userIDs, err = AllFollowedCreatorIDs(userID)
	require.NoError(err)
	assert.Equal([]int64{creatorID}, userIDs)

	// 测试设置了错误的缓存
	require.NoError(service.LRURedis.Set(key, "10,11", time.Second).Err())
	userIDs, err = AllFollowedCreatorIDs(userID)
	require.NoError(err)
	assert.Equal([]int64{creatorID}, userIDs)
}

func TestFindFansNumMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.DB.FirstOrCreate(new(user.MowangskUser), &user.MowangskUser{
		Simple: user.Simple{ID: 999999}, FansNum: 3,
	}).Error)

	require.NoError(service.DB.FirstOrCreate(new(user.MowangskUser), &user.MowangskUser{
		Simple: user.Simple{ID: 999998}, FansNum: 2,
	}).Error)

	fansNumMap, err := FindFansNumMap([]int64{999998, 999999})
	require.NoError(err)
	assert.Len(fansNumMap, 2)
	assert.Equal(int64(2), fansNumMap[999998].FansNum)
}

func TestIsRPCUserError(t *testing.T) {
	assert := assert.New(t)

	// 测试 err 为 nil 的情况
	assert.False(IsRPCUserError(nil))

	// 测试不是 rpc user code 错误码
	err := &mrpc.ClientError{
		Code: handler.CodeAlbumNotFound,
	}
	assert.False(IsRPCUserError(err))

	// 测试是 rpc user code 错误码
	err.Code = handler.CodeBlockedUser
	assert.True(IsRPCUserError(err))
}

func TestListAllAttentionUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	followerID1 := int64(100)
	followedID1 := int64(200)

	followerID2 := int64(101)
	followedID2 := int64(201)

	err := service.DB.Delete(&AttentionUser{}, "user_active = ?", followerID1).Error
	require.NoError(err)
	err = service.DB.Create(&AttentionUser{UserActive: followerID1, UserPasstive: followedID1}).Error
	require.NoError(err)

	err = service.DB.Delete(&AttentionUser{}, "user_active = ?", followerID2).Error
	require.NoError(err)
	err = service.DB.Create(&AttentionUser{UserActive: followerID2, UserPasstive: followedID1}).Error
	require.NoError(err)
	err = service.DB.Create(&AttentionUser{UserActive: followerID2, UserPasstive: followedID2}).Error
	require.NoError(err)

	testCases := []struct {
		followerID int64
		want       []int64
	}{
		{
			followerID: 0,
			want:       nil,
		},
		{
			followerID: -1,
			want:       nil,
		},
		{
			followerID: followerID1,
			want:       []int64{followedID1},
		},
		{
			followerID: followerID2,
			want:       []int64{followedID1, followedID2},
		},
	}

	for _, tc := range testCases {
		res, err := ListAllAttentionUserIDs(tc.followerID)
		require.NoError(err)
		assert.Equal(tc.want, res)
	}
}
