package transactionlog

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 订单状态
const (
	StatusCancel  = -2
	StatusUndone  = -1
	StatusAll     = 0
	StatusSuccess = 1
)

// 订单类型
const (
	TypeAll         = 0
	TypeLive        = 1
	TypeSound       = 2
	TypeDrama       = 3
	TypeBoyFriend   = 4
	TypeDrawCard    = 5
	TypeCardPackage = 6
	TypeDramaReward = 7
	TypeOmikuji     = 8
	TypeGuildLive   = 9
)

// 订单属性
const (
	AttrCommon                 int64 = iota // 直播间礼物（gift_id != 0）或问答（gift_id = 0）
	AttrLiveRenewNoble                      // 直播贵族续费（type 为 1 或 9 时）
	AttrLiveRegisterNoble                   // 直播贵族开通（type 为 1 或 9 时），平台统计收益时 开通费用*0.8，用户查看时不需要系数
	AttrLiveRebateGift                      // 直播间白给礼物（type 为 1 或 9 时）
	AttrLiveLuckyGift                       // 直播间幸运签礼物（type 为 1 或 9 时）
	AttrLiveRegisterSuperFan                // 直播间开通超粉（type 为 1 或 9 时）
	AttrLiveRenewSuperFan                   // 直播间续费超粉（type 为 1 或 9 时）
	AttrLiveFukubukuro                      // 直播间购买福袋（type 为 1 或 9 时）
	AttrLiveGashapon                        // 直播间购买超能魔方（type 为 1 或 9 时）
	AttrLiveGashaponGift                    // 直播间超能魔方礼物（type 为 1 或 9 时）
	AttrLiveWishPool                        // 直播间许愿池（type 为 1 或 9 时）
	AttrLiveRedPacket                       // 直播间礼物红包（type 为 1 或 9 时）
	AttrLiveRegisterNobleTrial              // 直播间开通体验贵族（type 为 1 或 9 时）
	AttrLiveRenewNobleTrial                 // 直播间续费体验贵族（type 为 1 或 9 时）
	AttrLiveDanmaku                         // 直播间付费弹幕（type 为 1 或 9 时）
	AttrLiveLuckyBag                        // 直播间购买喵喵福袋（type 为 1 或 9 时）
	AttrLiveBuyLuckyBox                     // 直播间购买宝盒（type 为 1 或 9 时）
)

var (
	// 用户实际消费使用钻石的字段列表
	coinNames = []string{"ios_coin", "android_coin", "paypal_coin", "tmallios_coin", "googlepay_coin"}
)

// attrRateMap 订单分成，使用 string 解决浮点型误差
//
//	map[订单 attr] = 分成
type attrRateMap map[int64]string

// GenerateRateAttrsMap 将相同分成的 attr 合并
//
//	return map[分成][]string{attr...}
func (arm attrRateMap) GenerateRateAttrsMap(attrs []int64) (map[string][]string, error) {
	if len(attrs) <= 0 {
		return nil, fmt.Errorf("attrRateMap.GenerateRateAttrsMap: attrs is empty")
	}
	attrs = goutil.Uniq(attrs)

	r := make(map[string][]string)
	for _, v := range attrs {
		rate, exists := arm[v]
		if !exists {
			return nil, fmt.Errorf("attrRateMap.GenerateRateAttrsMap: attr %d not exists", v)
		}
		r[rate] = append(r[rate], strconv.FormatInt(v, 10))
	}
	return r, nil
}

// GuildIncomeV2AttrRateMap 公会收益直播分成
var GuildIncomeV2AttrRateMap = attrRateMap{
	AttrCommon: "1",
	// 平台统计贵族开通收益 费用*0.8，此处为用户查看不需要系数
	AttrLiveRegisterNoble:    "1",
	AttrLiveRebateGift:       "1",
	AttrLiveLuckyGift:        "1",
	AttrLiveRegisterSuperFan: "1",
	AttrLiveRenewSuperFan:    "1",
	AttrLiveGashaponGift:     "1",
}

// 特殊时间
const (
	// 散人主播显示冻结余额项时间点（2020-07-01 00:00:00）
	TimeStampShowFrozenProfit = 1593532800
	// 旧收益流水和新收益流水的分隔时间（2020-11-01 00:00:00）
	TimeStampShowNewProfit = 1604160000 // TODO: 待定
)

// 获取主播收益详情列表类型
const (
	// 直播打赏收益
	IncomeTypeLiveGift = 0
	// 直播贵族收益
	IncomeTypeLiveNoble = 1
)

// TransactionLog model for table app_missevan.transaction_log
type TransactionLog struct {
	ID     int64  `gorm:"column:id;primary_key" json:"id"`
	FromID int64  `gorm:"column:from_id" json:"from_id"`
	ToID   int64  `gorm:"column:to_id" json:"to_id"`
	GiftID int64  `gorm:"column:gift_id" json:"gift_id"`
	Title  string `gorm:"column:title" json:"title"`

	IOSCoin     int64   `gorm:"column:ios_coin" json:"ios_coin"`
	AndroidCoin int64   `gorm:"column:android_coin" json:"android_coin"`
	PayPalCoin  int64   `gorm:"column:paypal_coin" json:"paypal_coin"`
	AllCoin     int64   `gorm:"column:all_coin" json:"all_coin"`
	Income      float64 `gorm:"column:income" json:"income"`
	Tax         float64 `gorm:"column:tax" json:"tax"`
	Rate        float64 `gorm:"column:rate" json:"rate"`
	Num         int64   `gorm:"column:num" json:"num"`

	Status       int   `gorm:"column:status" json:"status"`
	Type         int   `gorm:"column:type" json:"type"`
	SubordersNum int64 `gorm:"column:suborders_num" json:"suborders_num"` // 公会主播直播时存储公会 ID
	Attr         int64 `gorm:"column:attr" json:"-"`

	CTime        int64 `gorm:"column:c_time" json:"c_time"`
	CreateTime   int64 `gorm:"column:create_time" json:"create_time"`
	ModifiedTime int64 `gorm:"column:modified_time" json:"modified_time"`
	ConfirmTime  int64 `gorm:"column:confirm_time" json:"confirm_time"`
}

// DB the db instance of TransactionLog model
func DB() *gorm.DB {
	return service.PayDB.Table(TransactionLog{}.TableName())
}

// ADB the analytic db instance of TransactionLog model
func ADB(tableAlias ...string) *gorm.DB {
	if len(tableAlias) > 0 {
		return service.NewADB.Table(TransactionLog{}.TableName() + " AS " + tableAlias[0])
	}
	return service.NewADB.Table(TransactionLog{}.TableName())
}

// TableName for current model
func (TransactionLog) TableName() string {
	return "transaction_log"
}

// GiftCondSQL 礼物收益条件 SQL
func GiftCondSQL() string {
	// 礼物收益：需要排除 attr=0 AND gift_id=0（付费问答）的记录
	return fmt.Sprintf(
		"(attr IN (%s) AND NOT (attr = %d AND gift_id = 0))",
		goutil.JoinInt64Array([]int64{
			AttrCommon, AttrLiveRebateGift, AttrLiveLuckyGift, AttrLiveGashaponGift,
		}, ","),
		AttrCommon,
	)
}

// LuckyGiftCondSQL 随机礼物收益条件 SQL
func LuckyGiftCondSQL() string {
	return fmt.Sprintf("(status = %d AND type IN (%s) AND attr = %d)",
		StatusSuccess,
		goutil.JoinInt64Array([]int64{
			TypeLive, TypeGuildLive,
		}, ","),
		AttrLiveLuckyGift)
}

// PlayCondSQL 玩法收益条件 SQL
func PlayCondSQL() string {
	// 玩法收益：需要排除 attr=0 AND gift_id>0（普通礼物）的记录
	return fmt.Sprintf("(attr IN (%s) AND NOT (attr = %d AND gift_id > 0))",
		goutil.JoinInt64Array([]int64{
			AttrCommon, AttrLiveDanmaku, AttrLiveBuyLuckyBox,
		}, ","),
		AttrCommon,
	)
}

// NobleCondSQL 贵族收益条件 SQL
func NobleCondSQL() string {
	return fmt.Sprintf("attr = %d", AttrLiveRegisterNoble)
}

// SuperFanCondSQL 超粉收益条件 SQL
func SuperFanCondSQL() string {
	return fmt.Sprintf("attr IN (%s)",
		goutil.JoinInt64Array([]int64{
			AttrLiveRegisterSuperFan, AttrLiveRenewSuperFan,
		}, ","),
	)
}

// RevenueType 收益类型
type RevenueType int

// 收益类型
const (
	RevenueTypeSingleCreator RevenueType = iota
	RevenueTypeGuildCreator
)

func creatorRevenueColumn(revenueType RevenueType) string {
	var revenueColumn string
	switch revenueType {
	case RevenueTypeSingleCreator:
		revenueColumn = "((income - tax) * rate)"
	case RevenueTypeGuildCreator:
		revenueColumn = "(income - tax)"
	default:
		panic(fmt.Errorf("unknown revenue type: %d", revenueType))
	}

	return revenueColumn
}

// RevenueColumn 收益项 SQL
func RevenueColumn(revenueType RevenueType, alias string) string {
	revenueColumn := creatorRevenueColumn(revenueType)
	return servicedb.NewSQLColumn(revenueColumn).
		Coalesce("0").
		MultiplyBy(1000).
		Round().
		DivideBy(10).
		Floor().
		DivideBy(100).
		Alias(alias).
		String()
}

// SumRevenueColumn 收益项求和 SQL
func SumRevenueColumn(revenueType RevenueType, condSQL, alias string) string {
	revenueColumn := creatorRevenueColumn(revenueType)
	var sc *servicedb.SQLColumn
	if condSQL != "" {
		sc = &servicedb.SQLColumn{}
		sc = sc.If(condSQL, revenueColumn, "0")
	} else {
		sc = servicedb.NewSQLColumn(revenueColumn)
	}
	return sc.
		MultiplyBy(1000).
		Round().
		DivideBy(10).
		Floor().
		DivideBy(100).
		Sum().
		Coalesce("0").
		Alias(alias).
		String()
}

// AllLiveRevenueCondSQL 直播收益条件 SQL
// NOTICE: 注意同步更新 AllRevenueAttrs
func AllLiveRevenueCondSQL(orderType int, guildID ...int64) string {
	condSQL := fmt.Sprintf(
		"status = %d AND type = %d AND attr IN (%s)",
		StatusSuccess,
		orderType,
		goutil.JoinInt64Array([]int64{
			AttrCommon, AttrLiveRebateGift, AttrLiveLuckyGift, AttrLiveGashaponGift,
			AttrLiveRegisterNoble,
			AttrLiveRegisterSuperFan, AttrLiveRenewSuperFan,
			AttrLiveDanmaku,
			AttrLiveBuyLuckyBox,
		}, ","),
	)

	// 在获取公会直播收益时 suborders_num 表示公会 ID
	if orderType == TypeGuildLive && len(guildID) > 0 {
		condSQL += fmt.Sprintf(" AND suborders_num = %d", guildID[0])
	}

	return "(" + condSQL + ")"
}

// GiftAttrs 礼物 attr 类型
// Deprecated: 使用 GiftCondSQL 代替
func GiftAttrs() []int64 {
	return []int64{
		AttrCommon,
		AttrLiveRebateGift,
		AttrLiveLuckyGift,
		AttrLiveGashaponGift,
	}
}

// NobleAttrs 贵族 attr 类型
// Deprecated: 使用 NobleCondSQL 代替
func NobleAttrs() []int64 {
	return []int64{AttrLiveRegisterNoble}
}

// SuperFanAttrs 超粉 attr 类型
// Deprecated: 使用 SuperFanCondSQL 代替
func SuperFanAttrs() []int64 {
	return []int64{AttrLiveRegisterSuperFan, AttrLiveRenewSuperFan}
}

// AllRevenueAttrs 所有可用于计算收益的 attr，部分老接口仍在使用
// Deprecated: 使用 AllLiveRevenueCondSQL 代替
func AllRevenueAttrs() []int64 {
	attrs := append(GiftAttrs(), NobleAttrs()...)
	attrs = append(attrs, SuperFanAttrs()...)
	return append(attrs, AttrLiveDanmaku, AttrLiveBuyLuckyBox)
}

// IncomeTypeName name of various income type
func IncomeTypeName(attr int64, giftID int64) (typeName string, err error) {
	switch attr {
	case AttrLiveRegisterSuperFan:
		typeName = "开通直播超粉"
	case AttrLiveRenewSuperFan:
		typeName = "续费直播超粉"
	case AttrLiveRegisterNoble:
		typeName = "开通直播贵族"
	case AttrLiveDanmaku, AttrLiveBuyLuckyBox:
		typeName = "玩法"
	case AttrLiveGashaponGift, AttrLiveRebateGift, AttrLiveLuckyGift, AttrCommon:
		if giftID == 0 {
			typeName = "玩法"
		} else {
			typeName = "礼物"
		}
	default:
		err = fmt.Errorf("wrong attr: %d", attr)
	}

	return
}

// IncomeTitle 收益标题
func (t *TransactionLog) IncomeTitle(isCreatorSelf bool) string {
	typeName, err := IncomeTypeName(t.Attr, t.GiftID)
	if err != nil {
		// 降级显示为 title（后续可能会增加新的收益项，需要同步调整）
		logger.WithField("tid", t.ID).Error(err)
		return t.Title
	}
	if IsSuperFanIncome(t.Attr) {
		if isCreatorSelf {
			// 超粉收益记录不显示主播自己的名称
			return fmt.Sprintf("%s--%d 个月", typeName, t.Num)
		}
		return fmt.Sprintf("%s--%d 个月（主播：%s）", typeName, t.Num, t.Title)
	}

	if goutil.HasElem(GiftAttrs(), t.Attr) && t.GiftID != 0 {
		if t.Attr == AttrLiveGashaponGift {
			return fmt.Sprintf("%s--%s× %d", typeName, t.Title, t.Num)
		}
		giftNum := t.Num
		if t.Attr == AttrLiveLuckyGift {
			// 随机礼物时 t.Num 表示单次抽取的幸运签个数
			giftNum = 1
		}
		return fmt.Sprintf("%s--%s × %d", typeName, t.Title, giftNum)
	}

	if t.Attr == AttrLiveDanmaku {
		return fmt.Sprintf("%s--%s", typeName, "弹幕")
	}
	if t.Attr == AttrLiveBuyLuckyBox {
		return fmt.Sprintf("%s--%s宝盒 × %d", typeName, t.Title, t.Num)
	}
	if t.Attr == AttrCommon && t.GiftID == 0 {
		return fmt.Sprintf("%s--%s", typeName, "提问")
	}

	// 目前只有贵族开通
	return fmt.Sprintf("%s--%s", typeName, t.Title)
}

// LuckyGiftTitle 随机礼物消费记录标题
func LuckyGiftTitle(giftName, luckyGiftName string) string {
	return fmt.Sprintf("%s（%s）", giftName, luckyGiftName)
}

// IsSuperFanIncome 是否为超粉收益
func IsSuperFanIncome(attr int64) bool {
	return attr == AttrLiveRegisterSuperFan || attr == AttrLiveRenewSuperFan
}

// RevenueExpr 获取收益的原生 SQL 表达式
// TODO: 将项目中获取收益的地方都替换为此方法
func RevenueExpr(alias ...string) string {
	revenue := "income - tax"
	if len(alias) > 0 {
		revenue = fmt.Sprintf("%[1]s.income - %[1]s.tax", alias[0])
	}

	return fmt.Sprintf("FLOOR(ROUND(IFNULL((%s), 0) * 1000) / 10)", revenue)
}

// GenerateAttrsIncomeFieldSQL 根据 attr、收益分成、是否启用渠道费用 返回 SQL
//
//	attrs = 1, rate = 0.2, enableSubTax = true
//	return IF(`attr` = 1, `income` * 0.2 - `tax`, 0)
//
//	attrs = [1, 2, 3], rate = 0.2, enableSubTax = true
//	return IF(`attr` IN (1, 2, 3), `income` * 0.2 - `tax`, 0)
//
//	attrs = 1, rate = 0.2, enableSubTax = false
//	return IF(`attr` = 1, `income` * 0.2, 0)
func GenerateAttrsIncomeFieldSQL(attrs []string, rate string, enableSubTax bool) (string, error) {
	r, _ := strconv.ParseFloat(rate, 64)
	if r <= 0 || r > 1.0 {
		return "", errors.New("transaction_log.GenerateAttrsIncomeFieldSQL: rate less than or equal to 0 or greater than 1")
	}
	part := "`income`"
	if r != 1.0 {
		part += " * " + rate
	}
	if enableSubTax {
		part += " - `tax`"
	}

	if len(attrs) == 1 {
		return fmt.Sprintf("IF(`attr` = %s, %s, 0)", attrs[0], part), nil
	}
	return fmt.Sprintf("IF(`attr` IN (%s), %s, 0)", strings.Join(attrs, ", "), part), nil
}

// safe2dpSQL 处理浮点数求和及空的情况
const safe2dpSQL = "COALESCE(SUM(FLOOR(ROUND((%s) * 1000) / 10) / 100), 0) AS %s"

// GenerateSafe2dpSQL 返回处理浮点数求和及空情况的 SQL
func GenerateSafe2dpSQL(sql, alias string) string {
	return fmt.Sprintf(safe2dpSQL, sql, alias)
}

// UserLiveSpend 用户直播消费
type UserLiveSpend struct {
	FromID     int64   `gorm:"column:from_id"`     // 用户 ID
	TotalSpend float64 `gorm:"column:total_spend"` // 用户直播消费金额，单位：元
}

// GetUsersLiveSpend 获取用户直播消费
func GetUsersLiveSpend(userIDs []int64) ([]UserLiveSpend, error) {
	var usersLiveSpend []UserLiveSpend
	err := ADB().Select(fmt.Sprintf("from_id, SUM(%s) / 10.0 AS total_spend", strings.Join(coinNames, " + "))).
		Where("from_id IN (?) AND type IN (?) AND status = ?", userIDs, []int{TypeLive, TypeGuildLive}, StatusSuccess).
		Group("from_id").Find(&usersLiveSpend).Error
	if err != nil {
		return nil, err
	}

	return usersLiveSpend, nil
}

// GetGuildTotalIncome 获取总收益（总收益包含贵族开通、礼物打赏、玩法、超粉）
func GetGuildTotalIncome(guildID int64, creatorIDs []int64, startDate, endDate time.Time) (util.Float2DP, error) {
	if guildID <= 0 {
		return 0, fmt.Errorf("transaction_log.GetGuildTotalIncome: invalid guildID (%d). guildID must be a positive integer", guildID)
	}

	db := ADB().
		Select(SumRevenueColumn(RevenueTypeGuildCreator, "", "total_income")).
		Where(AllLiveRevenueCondSQL(TypeGuildLive, guildID)).
		Where("confirm_time >= ? AND confirm_time < ?", startDate.Unix(), endDate.Unix())

	if len(creatorIDs) > 0 {
		db = db.Where("to_id IN (?)", creatorIDs)
	}

	var totalRevenue util.Float2DP
	err := db.Row().Scan(&totalRevenue)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return 0, err
	}
	return totalRevenue, nil
}
