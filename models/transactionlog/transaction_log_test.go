package transactionlog

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestGiftAttrs(t *testing.T) {
	assert := assert.New(t)

	attrs := GiftAttrs()
	assert.Equal([]int64{AttrCommon, AttrLiveRebateGift, AttrLiveLuckyGift, AttrLiveGashaponGift}, attrs)
}

func TestNobleAttrs(t *testing.T) {
	assert := assert.New(t)

	attrs := NobleAttrs()
	assert.Equal([]int64{AttrLiveRegisterNoble}, attrs)
}

func TestSuperFanAttrs(t *testing.T) {
	assert := assert.New(t)

	attrs := SuperFanAttrs()
	assert.Equal([]int64{AttrLiveRegisterSuper<PERSON>an, AttrLiveRenewSuperFan}, attrs)
}

func TestAllRevenueAttrs(t *testing.T) {
	assert := assert.New(t)

	attrs := AllRevenueAttrs()
	assert.Equal([]int64{AttrCommon, AttrLiveRebateGift, AttrLiveLuckyGift, AttrLiveGashaponGift, AttrLiveRegisterNoble, AttrLiveRegisterSuperFan, AttrLiveRenewSuperFan, AttrLiveDanmaku, AttrLiveBuyLuckyBox}, attrs)
}

func TestGiftCondSQL(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("(attr IN (0,3,4,9) AND NOT (attr = 0 AND gift_id = 0))", GiftCondSQL())
}

func TestLuckyGiftCondSQL(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("(status = 1 AND type IN (1,9) AND attr = 4)", LuckyGiftCondSQL())
}

func TestPlayCondSQL(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("(attr IN (0,14,16) AND NOT (attr = 0 AND gift_id > 0))", PlayCondSQL())
}

func TestNobleCondSQL(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("attr = 2", NobleCondSQL())
}

func TestSuperFanCondSQL(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("attr IN (5,6)", SuperFanCondSQL())
}

func TestCreatorRevenueColumn(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("((income - tax) * rate)", creatorRevenueColumn(RevenueTypeSingleCreator))
	assert.Equal("(income - tax)", creatorRevenueColumn(RevenueTypeGuildCreator))
	assert.PanicsWithError("unknown revenue type: 999", func() {
		creatorRevenueColumn(999)
	})
}

func TestRevenueColumn(t *testing.T) {
	assert := assert.New(t)

	revenueColumn := RevenueColumn(RevenueTypeSingleCreator, "total")
	assert.Equal("(FLOOR((ROUND((COALESCE(((income - tax) * rate), 0) * 1000)) / 10)) / 100) AS total", revenueColumn)

	revenueColumn = RevenueColumn(RevenueTypeGuildCreator, "total")
	assert.Equal("(FLOOR((ROUND((COALESCE((income - tax), 0) * 1000)) / 10)) / 100) AS total", revenueColumn)
}

func TestSumRevenueColumn(t *testing.T) {
	assert := assert.New(t)

	revenueColumn := SumRevenueColumn(RevenueTypeSingleCreator, "", "total")
	assert.Equal("COALESCE(SUM((FLOOR((ROUND((((income - tax) * rate) * 1000)) / 10)) / 100)), 0) AS total", revenueColumn)

	revenueColumn = SumRevenueColumn(RevenueTypeSingleCreator, "attr = 2", "total")
	assert.Equal("COALESCE(SUM((FLOOR((ROUND((IF(attr = 2, ((income - tax) * rate), 0) * 1000)) / 10)) / 100)), 0) AS total", revenueColumn)

	revenueColumn = SumRevenueColumn(RevenueTypeGuildCreator, "", "total")
	assert.Equal("COALESCE(SUM((FLOOR((ROUND(((income - tax) * 1000)) / 10)) / 100)), 0) AS total", revenueColumn)

	revenueColumn = SumRevenueColumn(RevenueTypeGuildCreator, "attr = 2", "total")
	assert.Equal("COALESCE(SUM((FLOOR((ROUND((IF(attr = 2, (income - tax), 0) * 1000)) / 10)) / 100)), 0) AS total", revenueColumn)
}

func TestIncomeTypeName(t *testing.T) {
	assert := assert.New(t)

	typeName, err := IncomeTypeName(AttrCommon, 0)
	assert.NoError(err)
	assert.Equal("玩法", typeName)

	typeName, err = IncomeTypeName(AttrCommon, 1)
	assert.NoError(err)
	assert.Equal("礼物", typeName)

	typeName, err = IncomeTypeName(AttrLiveLuckyGift, 1)
	assert.NoError(err)
	assert.Equal("礼物", typeName)

	typeName, err = IncomeTypeName(AttrLiveRebateGift, 1)
	assert.NoError(err)
	assert.Equal("礼物", typeName)

	typeName, err = IncomeTypeName(AttrLiveGashaponGift, 1)
	assert.NoError(err)
	assert.Equal("礼物", typeName)

	typeName, err = IncomeTypeName(AttrLiveDanmaku, 1)
	assert.NoError(err)
	assert.Equal("玩法", typeName)

	typeName, err = IncomeTypeName(AttrLiveBuyLuckyBox, 1)
	assert.NoError(err)
	assert.Equal("玩法", typeName)

	typeName, err = IncomeTypeName(AttrLiveRegisterNoble, 1)
	assert.NoError(err)
	assert.Equal("开通直播贵族", typeName)

	typeName, err = IncomeTypeName(AttrLiveRegisterSuperFan, 1)
	assert.NoError(err)
	assert.Equal("开通直播超粉", typeName)

	typeName, err = IncomeTypeName(AttrLiveRenewSuperFan, 1)
	assert.NoError(err)
	assert.Equal("续费直播超粉", typeName)

	_, err = IncomeTypeName(AttrLiveRenewNoble, 1)
	assert.EqualError(err, fmt.Sprintf("wrong attr: %d", AttrLiveRenewNoble))
}

func TestTransactionLog_IncomeTitle(t *testing.T) {
	assert := assert.New(t)

	tradelog := TransactionLog{
		GiftID: 1,
		Num:    5,
		Title:  "药丸",
		Attr:   AttrCommon,
	}
	title := tradelog.IncomeTitle(false)
	assert.Equal("礼物--药丸 × 5", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    5,
		Title:  "好运符（幸运签）",
		Attr:   AttrLiveLuckyGift,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("礼物--好运符（幸运签） × 1", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    5,
		Title:  "玲珑之定",
		Attr:   AttrLiveRebateGift,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("礼物--玲珑之定 × 5", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    5,
		Title:  "加油旗（超能魔方）",
		Attr:   AttrLiveGashaponGift,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("礼物--加油旗（超能魔方）× 5", title)

	tradelog = TransactionLog{
		GiftID: 0,
		Num:    1,
		Title:  "铲屎的十七",
		Attr:   AttrCommon,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("玩法--提问", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    1,
		Title:  "弹幕",
		Attr:   AttrLiveDanmaku,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("玩法--弹幕", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    1,
		Title:  "奇喵妙旅",
		Attr:   AttrLiveBuyLuckyBox,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("玩法--奇喵妙旅宝盒 × 1", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    1,
		Title:  "练习生",
		Attr:   AttrLiveRegisterNoble,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("开通直播贵族--练习生", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    12,
		Title:  "CV知秋_",
		Attr:   AttrLiveRegisterSuperFan,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("开通直播超粉--12 个月（主播：CV知秋_）", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    12,
		Title:  "CV知秋_",
		Attr:   AttrLiveRegisterSuperFan,
	}
	title = tradelog.IncomeTitle(true)
	assert.Equal("开通直播超粉--12 个月", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    12,
		Title:  "卡胖胖和拉胖胖",
		Attr:   AttrLiveRenewSuperFan,
	}
	title = tradelog.IncomeTitle(false)
	assert.Equal("续费直播超粉--12 个月（主播：卡胖胖和拉胖胖）", title)

	tradelog = TransactionLog{
		GiftID: 1,
		Num:    12,
		Title:  "卡胖胖和拉胖胖",
		Attr:   AttrLiveRenewSuperFan,
	}
	title = tradelog.IncomeTitle(true)
	assert.Equal("续费直播超粉--12 个月", title)
}

func TestLuckyGiftTitle(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("礼物名称（随机礼物）", LuckyGiftTitle("礼物名称", "随机礼物"))
}

func TestIsSuperFanIncome(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsSuperFanIncome(AttrLiveLuckyGift))
	assert.True(IsSuperFanIncome(AttrLiveRegisterSuperFan))
	assert.True(IsSuperFanIncome(AttrLiveRenewSuperFan))
}

func TestRevenueExpr(t *testing.T) {
	assert := assert.New(t)

	// 测试不传参数
	sql := RevenueExpr()
	assert.Equal("FLOOR(ROUND(IFNULL((income - tax), 0) * 1000) / 10)", sql)

	// 测试传参数
	sql = RevenueExpr("t")
	assert.Equal("FLOOR(ROUND(IFNULL((t.income - t.tax), 0) * 1000) / 10)", sql)
}

func TestGenerateRateAttrsMap(t *testing.T) {
	assert := assert.New(t)

	// GuildIncomeV2AttrRateMap 公会收益直播分成
	testMap := attrRateMap{
		AttrCommon:            "1",
		AttrLiveRenewNoble:    "1",
		AttrLiveRegisterNoble: "1",
		AttrLiveRebateGift:    "1",
		AttrLiveLuckyGift:     "0.1",
	}

	_, err := testMap.GenerateRateAttrsMap([]int64{})
	assert.Error(err, fmt.Errorf("attrRateMap.GenerateRateAttrsMap: attrs is empty"))

	_, err = testMap.GenerateRateAttrsMap([]int64{
		2333,
	})
	assert.Error(err, fmt.Errorf("attrRateMap.GenerateRateAttrsMap: attr %d not exists", 2333))

	m, err := testMap.GenerateRateAttrsMap([]int64{
		AttrCommon,
		AttrLiveRenewNoble,
		AttrLiveRegisterNoble,
	})
	assert.NoError(err)
	assert.Len(m, 1)

	m, err = testMap.GenerateRateAttrsMap([]int64{
		AttrCommon,
		AttrLiveRenewNoble,
		AttrLiveLuckyGift,
	})
	assert.NoError(err)
	assert.Len(m, 2)
}

func TestGenerateAttrsIncomeFieldSQL(t *testing.T) {
	assert := assert.New(t)
	_, err := GenerateAttrsIncomeFieldSQL([]string{"1", "2", "3"}, "-1", true)
	assert.EqualError(err, "transaction_log.GenerateAttrsIncomeFieldSQL: rate less than or equal to 0 or greater than 1")

	_, err = GenerateAttrsIncomeFieldSQL([]string{"1", "2", "3"}, "1.1", true)
	assert.EqualError(err, "transaction_log.GenerateAttrsIncomeFieldSQL: rate less than or equal to 0 or greater than 1")

	s, err := GenerateAttrsIncomeFieldSQL([]string{"1"}, "1", true)
	assert.NoError(err)
	assert.Equal("IF(`attr` = 1, `income` - `tax`, 0)", s)

	s, err = GenerateAttrsIncomeFieldSQL([]string{"1", "2", "3"}, "0.8", true)
	assert.NoError(err)
	assert.Equal("IF(`attr` IN (1, 2, 3), `income` * 0.8 - `tax`, 0)", s)

	s, err = GenerateAttrsIncomeFieldSQL([]string{"1"}, "0.8", false)
	assert.NoError(err)
	assert.Equal("IF(`attr` = 1, `income` * 0.8, 0)", s)

	s, err = GenerateAttrsIncomeFieldSQL([]string{"1", "2", "3"}, "1", false)
	assert.NoError(err)
	assert.Equal("IF(`attr` IN (1, 2, 3), `income`, 0)", s)
}

func TestGenerateSafe2dpSQL(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("COALESCE(SUM(FLOOR(ROUND((`income` - `tax`) * 1000) / 10) / 100), 0) AS income", GenerateSafe2dpSQL("`income` - `tax`", "income"))
}

func TestGetUsersLiveSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户没有直播消费
	testUserID := int64(2333)
	res, err := GetUsersLiveSpend([]int64{testUserID})
	require.NoError(err)
	assert.Empty(res)

	// 测试获取用户直播消费
	testUserID = int64(12346)
	res, err = GetUsersLiveSpend([]int64{testUserID})
	require.NoError(err)
	assert.Len(res, 1)
	assert.Equal(testUserID, res[0].FromID)
	assert.EqualValues(0.6, res[0].TotalSpend)
}

func TestGetGuildTotalIncome(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	res, err := GetGuildTotalIncome(0, []int64{}, time.Date(2025, 3, 13, 0, 0, 0, 0, time.UTC), time.Date(2025, 3, 15, 0, 0, 0, 0, time.Local))
	require.Error(err, "transaction_log.GetGuildTotalIncome: invalid guildID (0). guildID must be a positive integer")

	// 获取公会 2 在 2025-03-13 到 2025-03-14 的收益
	res, err = GetGuildTotalIncome(2, []int64{}, time.Date(2025, 3, 13, 0, 0, 0, 0, time.Local), time.Date(2025, 3, 15, 0, 0, 0, 0, time.Local))
	require.NoError(err)
	assert.Equal("3.00", res.String())

	// 获取公会 2 的主播 2 在 2025-03-13 到 2025-03-14 的收益
	res, err = GetGuildTotalIncome(2, []int64{2}, time.Date(2025, 3, 13, 0, 0, 0, 0, time.Local), time.Date(2025, 3, 15, 0, 0, 0, 0, time.Local))
	require.NoError(err)
	assert.Equal("2.00", res.String())

	// 获取公会 2 的主播 1 在 2025-03-14 到 2025-03-14 的收益
	res, err = GetGuildTotalIncome(2, []int64{1}, time.Date(2025, 3, 14, 0, 0, 0, 0, time.Local), time.Date(2025, 3, 15, 0, 0, 0, 0, time.Local))
	require.NoError(err)
	assert.Equal("1.00", res.String())
}
