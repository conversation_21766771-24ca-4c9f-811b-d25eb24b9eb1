package vip

import (
	"errors"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	UserIDNoNoble  int64 = 10
	UserIDHasNoble int64 = 3456835 // 2021 年 12 月贵族到期
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	cancel := MockVipList()
	defer cancel()

	m.Run()
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(Info{}, "id", "vip_id", "type", "level", "privilege", "icon", "icon_mini",
		"privilege_num", "effect", "effect_duration", "avatar_frame",
		"card_frame", "name_colors", "new_card_frame", "exp_acceleration", "medal_num", "customer_service_type", "notify_type",
		"notify_duration", "entrance_type", "horn_num", "create_time", "modified_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(Info{}, "vip_id", "level", "type", "privilege", "icon_url", "icon_mini_url",
		"privilege_num", "effect_url", "effect_duration",
		"avatar_frame_url", "card_frame_url", "name_colors", "new_card_frame_url", "exp_acceleration", "medal_num", "notify_type",
		"notify_duration", "entrance_type", "horn_num", "title", "customer_service_info",
		"registration_price", "registration_rebate", "renewal_price", "renewal_rebate")
	kc.Check(Meta{}, "type", "level", "title", "effect_url", "effect_duration", "icon_mini_url",
		"avatar_frame_url", "notify_duration", "card_frame_url")
	kc.Check(priceInfo{}, "id", "level", "title", "registration_price",
		"registration_rebate", "renewal_price", "renewal_rebate")
	kc.Check(UserVip{}, "user_id", "vip_id", "title", "level", "type", "renewal_rebate", "expire_time")
	kc.Check(UserVipLevel{}, "user_id", "type", "level", "expire_time")
}

func TestInfoTableName(t *testing.T) {
	assert := assert.New(t)
	assert.Equal("live_noble", Info{}.TableName())
}

func TestInfoAfterFind(t *testing.T) {
	assert := assert.New(t)

	info := Info{CustomerServiceType: 1}
	assert.NotPanics(func() { _ = info.AfterFind() })
	assert.Equal(customerServiceTypeSpecialQQ, info.CustomerServiceInfo.CopyText)

	strPtrs := [3]*string{&info.Icon, &info.IconMini,
		&info.Effect}
	info.Icon = "oss://icon.png"
	info.IconMini = "oss://icon_mini.png"
	info.Effect = "oss://effect.webp;oss://effect.svga;"
	info.CustomerServiceType = 2
	assert.NotPanics(func() { _ = info.AfterFind() })
	assert.Equal(customerServiceTypeVipQQ, info.CustomerServiceInfo.CopyText)
	prefix := config.Conf.Service.Storage["oss"].PublicURL
	expected := [3]string{
		prefix + "icon.png",
		prefix + "icon_mini.png",
		fmt.Sprintf("%seffect.webp;%seffect.svga;", prefix, prefix),
	}
	for i := 0; i < len(expected); i++ {
		assert.Equal(expected[i], *strPtrs[i])
	}
	assert.NotPanics(func() { _ = info.AfterFind() })
}

func TestNewCustomerServiceInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	info := newCustomerServiceInfo(CustomerServiceTypeSpecial)
	require.NotNil(info)
	assert.EqualValues(customerServiceTypeSpecialQQ, info.CopyText)
	assert.Equal(fmt.Sprintf("QQ：%s", customerServiceTypeSpecialQQ), info.ShowText)

	info = newCustomerServiceInfo(CustomerServiceTypeVip)
	require.NotNil(info)
	assert.EqualValues(customerServiceTypeVipQQ, info.CopyText)
	assert.Equal(fmt.Sprintf("QQ：%s", customerServiceTypeVipQQ), info.ShowText)

	info = newCustomerServiceInfo(4444)
	require.Nil(info)
}

func TestInfoSetPrice(t *testing.T) {
	assert := assert.New(t)
	price := &priceInfo{
		Title:              "price",
		RegistrationPrice:  1,
		RegistrationRebate: 2,
		RenewalPrice:       3,
		RenewalRebate:      4,
	}
	var info Info
	info.setPrice(price)
	assert.Equal(Info{
		Title:              "price",
		RegistrationPrice:  1,
		RegistrationRebate: 2,
		RenewalPrice:       3,
		RenewalRebate:      4}, info)
}

func TestInfoScaleContribution(t *testing.T) {
	assert := assert.New(t)

	var info Info
	assert.Equal(int64(10), info.ScaleContribution(10))
	info.ExpAcceleration = 10
	assert.Equal(int64(11), info.ScaleContribution(10))
}

func TestListByVipType(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	l1, err := ListByVipType(TypeLiveNoble)
	require.NoError(err)
	l2, err := ListByVipType(TypeLiveNoble)
	require.NoError(err)
	assert.Equal(l1, l2)
	assert.Len(l1, 7)
	l1[0].Level = 10
	assert.NotEqual(l1[0].Level, l2[0].Level, "测试列表指针指向不同的值")
}

func TestFindVipInfoByID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	vips := []Info{
		{
			VipID: 12,
			Type:  TypeLiveTrialNoble,
			Level: 4,
		},
	}
	service.Cache5Min.Set(keys.KeyVipList1.Format(TypeLiveTrialNoble), vips, cache.DefaultExpiration)
	defer service.Cache5Min.Flush()

	info, err := FindVipInfoByID(TypeLiveTrialNoble, 12)
	require.NoError(err)
	require.NotNil(info)
	assert.Equal(vips[0], *info)

	vips = []Info{
		{
			VipID: 1,
			Type:  TypeLiveNoble,
			Level: 1,
		},
	}
	service.Cache5Min.Set(keys.KeyVipList1.Format(TypeLiveNoble), vips, cache.DefaultExpiration)
	info, err = FindVipInfoByID(TypeLiveNoble, 1)
	require.NoError(err)
	require.NotNil(info)
	assert.Equal(vips[0], *info)

	info, err = FindVipInfoByID(TypeLiveNoble, 10000)
	require.NoError(err)
	assert.Nil(info)
}

func TestListNoCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	l1, err := listNoCache(TypeLiveNoble)
	require.NoError(err)
	l2 := listFromCache(TypeLiveNoble)
	mVipID := mapVipIDFromCache(TypeLiveNoble)
	mLevel := mapLevelFromCache(TypeLiveNoble)
	for i := 0; i < len(l1); i++ {
		assert.Equal(l1[i], l2[i])
		assert.Equal(*l1[i], mVipID[l1[i].VipID])
		assert.Equal(*l1[i], mLevel[l1[i].Level])
	}
}

func TestFindByTypeAndLevel(t *testing.T) {
	assert := assert.New(t)
	for i := 1; i < 7; i++ {
		info, err := FindByTypeAndLevel(TypeLiveNoble, i)
		if assert.NoError(err) {
			assert.NotNil(info)
		}
	}
	info, err := FindByTypeAndLevel(TypeLiveNoble, 100)
	assert.NoError(err)
	assert.Nil(info)
	service.Cache5Min.Flush()
	info, err = FindByTypeAndLevel(TypeLiveNoble, 100)
	assert.NoError(err)
	assert.Nil(info)
}

func TestRenewDeadline(t *testing.T) {
	assert := assert.New(t)

	key := keys.LocalKeyVipConfig0.Format()
	service.Cache5Min.Delete(key)

	// 测试 RPC 接口报错使用默认值
	cleanup := mrpc.SetMock(URLVipConfig, func(input any) (output any, err error) {
		return nil, actionerrors.ErrServerInternal(errors.New("test error"), nil)
	})
	defer cleanup()
	assert.Equal(int64(864000), RenewDeadline(0))

	// 使用 RPC 返回的值
	cleanup = mrpc.SetMock(URLVipConfig, func(input any) (output any, err error) {
		return &ConfigResp{
			LiveNobleProtectDays: 5,
		}, nil
	})
	defer cleanup()
	assert.Equal(int64(432000), RenewDeadline(0))
}

func TestIsInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ok, err := IsInvisible(123, nil)
	require.NoError(err)
	assert.False(ok)
	info := new(Info)
	ok, err = IsInvisible(123, info)
	require.NoError(err)
	assert.False(ok)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_meta")
	_, err = collection.UpdateOne(ctx, bson.M{"user_id": 12},
		bson.M{"$set": bson.M{"user_id": 12, "invisible": true}},
		options.Update().SetUpsert(true))
	require.NoError(err)

	info.Privilege.Set(PrivilegeInvisible)
	ok, err = IsInvisible(12, info)
	require.NoError(err)
	assert.True(ok)
	ok, err = IsInvisible(123456, info)
	require.NoError(err)
	assert.False(ok)
}

func TestUserHighestVipInfo(t *testing.T) {
	assert := assert.New(t)

	assert.Nil(UserHighestVipInfo(nil))
	assert.Nil(UserHighestVipInfo(&UserInfo{}))
	resp := UserHighestVipInfo(&UserInfo{LiveNoble: &Info{Type: 1}, LiveHighness: &Info{Type: 2}})
	assert.Equal(2, resp.Type)

	resp = UserHighestVipInfo(&UserInfo{LiveNoble: &Info{Type: 1}, LiveTrialNoble: &Info{Type: 3}})
	assert.Equal(3, resp.Type)

	resp = UserHighestVipInfo(&UserInfo{LiveNoble: &Info{Type: 1}})
	assert.Equal(1, resp.Type)
}

func TestUserHavePrivilege(t *testing.T) {
	assert := assert.New(t)

	assert.False(UserHavePrivilege(nil, PrivilegeRankInvisible))

	vips := &UserInfo{
		LiveNoble: &Info{
			Privilege: 4,
		},
		LiveHighness: &Info{
			Privilege: 0,
		},
	}
	assert.True(UserHavePrivilege(vips, PrivilegeRankInvisible))

	vips = &UserInfo{
		LiveNoble: &Info{
			Privilege: 0,
		},
		LiveHighness: &Info{
			Privilege: 4,
		},
	}
	assert.True(UserHavePrivilege(vips, PrivilegeRankInvisible))
}

func TestIsVip(t *testing.T) {
	assert := assert.New(t)

	var info *UserInfo
	isVip := info.IsVip()
	assert.False(isVip)

	info = &UserInfo{}
	isVip = info.IsVip()
	assert.False(isVip)

	info = &UserInfo{
		LiveHighness: &Info{},
	}
	isVip = info.IsVip()
	assert.True(isVip)

	info = &UserInfo{
		LiveNoble: &Info{},
	}
	isVip = info.IsVip()
	assert.True(isVip)

	info = &UserInfo{
		LiveTrialNoble: &Info{},
	}
	isVip = info.IsVip()
	assert.True(isVip)
}

func TestMapUsersInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.Redis.Del(
		keys.KeyNobleUserVipLevel1.Format(UserIDNoNoble),
		keys.KeyNobleUserVipLevel1.Format(UserIDHasNoble),
	).Err())
	u, err := MapUsersInfo([]int64{UserIDNoNoble, UserIDHasNoble}, nil, nil)
	require.NoError(err)
	assert.Nil(u[UserIDNoNoble])
	assert.NotNil(u[UserIDHasNoble])

	found := map[int64]*UserInfo{
		UserIDHasNoble: {LiveNoble: &Info{
			Type:  TypeLiveNoble,
			Level: 1,
		}},
	}
	u, err = MapUsersInfo([]int64{UserIDNoNoble, UserIDHasNoble}, found, nil)
	require.NoError(err)
	assert.Nil(u[UserIDNoNoble])
	assert.NotNil(u[UserIDHasNoble])

	// 从缓存中读取
	u, err = MapUsersInfo([]int64{UserIDNoNoble, UserIDHasNoble}, nil, nil)
	require.NoError(err)
	assert.Nil(u[UserIDNoNoble])
	assert.NotNil(u[UserIDHasNoble])

	// 测试调用者在列表中
	found[UserIDHasNoble+1] = &UserInfo{LiveNoble: &Info{
		Type:  TypeLiveNoble,
		Level: 1,
	}}
	setLevelInfos(UserIDHasNoble+1, `{"1":0}`)
	u, err = MapUsersInfo([]int64{UserIDNoNoble, UserIDHasNoble + 1}, found, nil)
	require.NoError(err)
	assert.NotNil(u[UserIDHasNoble+1])
}

func TestNobleMetas(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m, err := NobleMetas()
	require.NoError(err)
	assert.Len(m, 7)
}

func TestGetMedalNum(t *testing.T) {
	assert := assert.New(t)

	// 超粉未上线
	info := &Info{
		MedalNum: 11,
	}
	assert.Equal(int64(30), info.GetMedalNum())
	info.MedalNum = 41
	assert.Equal(int64(41), info.GetMedalNum())
}

func TestHighnessMeta(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	m, err := HighnessMeta()
	require.NoError(err)
	require.NotNil(m)
	assert.Equal(TypeLiveHighness, m.Type)
}

func TestClearUserVipCache(t *testing.T) {
	require := require.New(t)

	userIDs := []int64{9074509, 9074510}
	pipliner := service.Redis.Pipeline()
	for _, userID := range userIDs {
		// vips
		keyVips := keys.KeyNobleUserVips1.Format(userID)
		pipliner.Set(keyVips, strconv.FormatInt(userID, 10), 30*time.Second)
		// info
		keyInfo := keys.KeyNobleUserInfo1.Format(userID)
		pipliner.Set(keyInfo, strconv.FormatInt(userID, 10), 30*time.Second)
	}
	_, err := pipliner.Exec()
	require.NoError(err)

	ClearUserVipCache(userIDs...)

	numVips, err := service.Redis.Exists(
		keys.KeyNobleUserVips1.Format(userIDs[0]),
		keys.KeyNobleUserVips1.Format(userIDs[1]),
	).Result()
	require.NoError(err)
	require.Zero(numVips)

	numInfo, err := service.Redis.Exists(
		keys.KeyNobleUserInfo1.Format(userIDs[0]),
		keys.KeyNobleUserInfo1.Format(userIDs[1]),
	).Result()
	require.NoError(err)
	require.Zero(numInfo)
}

func TestUserVipFromAppearanceID(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uv := UserVipFromAppearanceID(91, nil)
	assert.Nil(uv)

	appearanceIDs := []int64{91, 92, 12, 23, 101}
	resultSet := [][2]int{
		{TypeLiveHighness, 8},
		{TypeLiveHighness, 8},
		{TypeLiveNoble, 7},
		{TypeLiveNoble, 7},
	}
	uvMap := map[int]*UserVip{
		1: {UserID: 9074509, VipID: 7, Type: TypeLiveNoble},
		2: {UserID: 9074509, VipID: 8, Type: TypeLiveHighness},
	}
	for i := range appearanceIDs {
		uv = UserVipFromAppearanceID(appearanceIDs[i], uvMap)
		if i == len(appearanceIDs)-1 {
			assert.Nil(uv)
			continue
		}
		assert.Equal(resultSet[i][0], uv.Type, i)
		require.NotNil(uv)
		assert.EqualValues(resultSet[i][1], uv.VipID)
	}
}

func TestIsUserHighnessRenewDurationMax(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 6, 6, 12, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	assert.False(IsUserHighnessRenewDurationMax(1657123199))

	assert.True(IsUserHighnessRenewDurationMax(1685635199))

	assert.True(IsUserHighnessRenewDurationMax(1716739199))
}

func TestClearUserVipPrivilege(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserIDs := []int64{1000010, 1000011}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testUserIDs}})
	require.NoError(err)

	testUserVipMap := map[int64]*UserVip{
		testUserIDs[1]: {
			UserID:     testUserIDs[1],
			VipID:      1,
			Level:      1,
			Type:       TypeLiveNoble,
			ExpireTime: goutil.TimeNow().Add(-time.Minute).Unix(),
			Info: &Info{
				Privilege: 0,
			},
		},
	}
	// 测试参数不正确的情况
	ClearUserVipPrivilege([]int64{}, testUserVipMap)

	// usermeta 测试数据
	// NOTICE: 直接使用 userstatus 会包循环引用
	type generalStatus struct {
		UserID       int64 `bson:"user_id"`
		Invisible    *bool `bson:"invisible"`
		NobleHornNum int64 `bson:"noble_horn_num"`
		RecommendNum *int  `bson:"recommend_num"`
	}
	usermeta1000010 := &generalStatus{
		UserID:       testUserIDs[0],
		NobleHornNum: 1,
		RecommendNum: goutil.NewInt(1),
		Invisible:    goutil.NewBool(true),
	}
	usermeta1000012 := &generalStatus{
		UserID:       testUserIDs[1],
		NobleHornNum: 1,
		RecommendNum: goutil.NewInt(1),
		Invisible:    goutil.NewBool(true),
	}
	_, err = usermeta.Collection().InsertMany(ctx, []interface{}{usermeta1000010, usermeta1000012})
	require.NoError(err)

	ClearUserVipPrivilege(testUserIDs, testUserVipMap)

	filter := bson.M{
		"user_id":        bson.M{"$in": testUserIDs},
		"noble_horn_num": bson.M{"$gt": 0},
		"recommend_num":  bson.M{"$exists": true},
		"invisible":      bson.M{"$exists": true},
	}
	count, err := usermeta.Collection().CountDocuments(ctx, filter)
	require.NoError(err)
	assert.Zero(count)
}

func TestBuyNobleExpireFormatTimeTip(t *testing.T) {
	assert := assert.New(t)

	cleanup := goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 4, 0, 0, 0, time.Local)
	})
	defer cleanup()

	when := time.Date(2022, 1, 1, 4, 0, 0, 0, time.Local)
	vipInfo := &Info{VipID: 1, Type: TypeLiveNoble}
	tip := BuyNobleExpireFormatTimeTip(when, vipInfo, nil, false)
	assert.Equal("2022-02-01 00:00", tip)

	when = time.Date(2022, 1, 1, 0, 0, 0, 0, time.Local)
	vipInfo = &Info{VipID: 1, Type: TypeLiveNoble}
	tip = BuyNobleExpireFormatTimeTip(when, vipInfo, nil, false)
	assert.Equal("2022-02-01 00:00", tip)

	// 新开贵族等级 == 当前贵族等级，累加体验贵族时间
	when = time.Date(2022, 1, 1, 4, 0, 0, 0, time.Local)
	uvt := &UserVip{Level: 1, ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix()}
	vipInfo = &Info{VipID: 1, Type: TypeLiveNoble}
	tip = BuyNobleExpireFormatTimeTip(when, vipInfo, uvt, true)
	assert.Equal("2022-02-02 00:00", tip)

	// 新开贵族等级 > 当前贵族等级，不累加体验贵族时间
	when = time.Date(2022, 1, 1, 4, 0, 0, 0, time.Local)
	uvt = &UserVip{Level: 1, ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix()}
	vipInfo = &Info{VipID: 2, Type: TypeLiveNoble}
	tip = BuyNobleExpireFormatTimeTip(when, vipInfo, uvt, false)
	assert.Equal("2022-02-01 00:00", tip)

	// 新开贵族等级 == 当前贵族等级，超过最大有效期，累加体验贵族时间
	when = goutil.TimeNow().Add(24 * thirtyDays)
	when = time.Date(when.Year(), when.Month(), when.Day(), 23, 59, 59, 0, time.Local)
	uvt = &UserVip{Level: 1, ExpireTime: goutil.TimeNow().Add(23 * time.Hour).Unix()}
	vipInfo = &Info{VipID: 1, Type: TypeLiveNoble}
	tip = BuyNobleExpireFormatTimeTip(when, vipInfo, uvt, false)
	assert.Equal("2023-12-24 00:00", tip)

	// 新开贵族等级 > 当前贵族等级，超过最大有效期，不累加体验贵族时间
	when = goutil.TimeNow().Add(23 * thirtyDays).Add(23 * time.Hour)
	uvt = &UserVip{Level: 1, ExpireTime: goutil.TimeNow().Add(23 * time.Hour).Unix()}
	vipInfo = &Info{Level: 2, Type: TypeLiveNoble}
	tip = BuyNobleExpireFormatTimeTip(when, vipInfo, uvt, false)
	assert.Equal("2023-12-23 00:00", tip)
}
