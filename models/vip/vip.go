// Package vip 直播贵族
// TODO: 未考虑主站 vip 增加后的兼容
package vip

import (
	"fmt"
	"time"

	"github.com/patrickmn/go-cache"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// AllPrivilegeNum 特权总数
const AllPrivilegeNum = 20

// LiveNobleMaxRenewDuration 直播贵族最大过期时间（24 个月，每月按 30 天计算）
// NOTICE: 该值和 missevan-sso 的贵族最大续费有效期保持一致
const LiveNobleMaxRenewDuration = 24 * thirtyDays

// 贵族等级
const (
	NobleLevel3 = 3 // 偶像贵族
	NobleLevel4 = 4 // 大咖贵族
	NobleLevel5 = 5 // 巨星贵族
	NobleLevel7 = 7 // 神话贵族
)

// Privilege
const (
	PrivilegeInvisible     = iota + 1 // 进场隐身
	PrivilegeBubble                   // 贵族气泡
	PrivilegeRankInvisible            // 榜单隐身
	PrivilegeFangJinYan               // 防禁言 TODO: 拼音改英文
	PrivilegeCustomGift               // 定制礼物
	PrivilegeRecommend                // 神话推荐
)

// Highness 开通续费上神常量
const (
	HighnessOpenCoin      = 10_000_000 // 开通上神所需钻石数
	HighnessRenewCoin     = 5_000_000  // 续费上神所需钻石数
	HighnessPotentialCoin = 4_000_000  // 潜力上神所需钻石数

	HighnessGiftID            = 30014
	HighnessGiftNum           = 1
	HighnessNobleHornNum      = 5
	HighnessRecommendNumOpen  = 4
	HighnessRecommendNumRenew = 3
	HighnessDefaultGiftName   = "上神 · 降临"
	HighnessMaxRenewDays      = 360 // 上神贵族最长有效期（天）
)

// 客服类型
const (
	CustomerServiceTypeNone = iota
	CustomerServiceTypeSpecial
	CustomerServiceTypeVip
)

// 专属客服
const (
	customerServiceTypeSpecialQQ = "800181712"
	customerServiceTypeVipQQ     = "3008181774"
)

// tableName table name
func tableName() string {
	return "live_noble"
}

// Info vip 信息
type Info struct {
	ID                  int64          `gorm:"column:id" json:"-"`
	VipID               int64          `gorm:"column:vip_id" json:"vip_id"`
	Type                int            `gorm:"column:type" json:"type"`
	Level               int            `gorm:"column:level" json:"level"`
	Privilege           goutil.BitMask `gorm:"column:privilege" json:"privilege"`
	Icon                string         `gorm:"column:icon" json:"icon_url"`
	IconMini            string         `gorm:"column:icon_mini" json:"icon_mini_url"`
	PrivilegeNum        int            `gorm:"column:privilege_num" json:"privilege_num"`
	Effect              string         `gorm:"column:effect" json:"effect_url"`
	EffectDuration      int64          `gorm:"column:effect_duration" json:"effect_duration"`
	AvatarFrame         string         `gorm:"column:avatar_frame" json:"avatar_frame_url"`
	CardFrame           string         `gorm:"column:card_frame" json:"card_frame_url"`
	NameColors          string         `gorm:"column:name_colors" json:"name_colors"` // 渐变色，格式: "#000000;#FFFFFF"
	NewCardFrame        string         `gorm:"column:new_card_frame" json:"new_card_frame_url"`
	ExpAcceleration     int            `gorm:"column:exp_acceleration" json:"exp_acceleration"`
	MedalNum            int            `gorm:"column:medal_num" json:"medal_num"`
	CustomerServiceType int            `gorm:"column:customer_service_type" json:"-"`
	EntranceType        int            `gorm:"column:entrance_type" json:"entrance_type"` // REVIEW: 入场特效放哪里
	NotifyType          int            `gorm:"column:notify_type" json:"notify_type"`
	NotifyDuration      int64          `gorm:"column:notify_duration" json:"notify_duration"`
	HornNum             int64          `gorm:"column:horn_num" json:"horn_num"`
	CreateTime          int64          `gorm:"column:create_time" json:"-"`
	ModifiedTime        int64          `gorm:"column:modified_time" json:"-"`

	Title               string               `gorm:"-" json:"title"`
	CustomerServiceInfo *CustomerServiceInfo `gorm:"-" json:"customer_service_info,omitempty"`
	RegistrationPrice   int                  `gorm:"-" json:"registration_price"`
	RegistrationRebate  int                  `gorm:"-" json:"registration_rebate"`
	RenewalPrice        int                  `gorm:"-" json:"renewal_price"`
	RenewalRebate       int                  `gorm:"-" json:"renewal_rebate"`
}

// CustomerServiceInfo 客服
type CustomerServiceInfo struct {
	ShowText string `json:"show_text"`
	CopyText string `json:"copy_text"`
}

// Meta VIP meta
type Meta struct {
	Type  int    `json:"type"`
	Level int    `json:"level"`
	Title string `json:"title"`

	EffectURL      string `json:"effect_url,omitempty"`
	EffectDuration int64  `json:"effect_duration,omitempty"`

	IconMiniURL    string `json:"icon_mini_url"`
	AvatarFrameURL string `json:"avatar_frame_url,omitempty"`
	NotifyDuration int64  `json:"notify_duration,omitempty"`
	CardFrameURL   string `json:"card_frame_url,omitempty"`
}

// TableName table name
func (Info) TableName() string {
	return tableName()
}

// GetMedalNum 获得贵族用户的最大勋章数量
func (info *Info) GetMedalNum() int64 {
	maxMedalNum := noVipMaxMedalNum
	// 保证贵族最大勋章数量不会比普通用户少
	if int64(info.MedalNum) <= maxMedalNum {
		return maxMedalNum
	}
	return int64(info.MedalNum)
}

// AfterFind gorm 钩子
func (info *Info) AfterFind() error {
	info.Icon = storage.ParseSchemeURL(info.Icon)
	info.IconMini = storage.ParseSchemeURL(info.IconMini)
	info.Effect = storage.ParseSchemeURLs(info.Effect)
	info.AvatarFrame = storage.ParseSchemeURL(info.AvatarFrame)
	info.CardFrame = storage.ParseSchemeURL(info.CardFrame)
	info.NewCardFrame = storage.ParseSchemeURL(info.NewCardFrame)

	info.CustomerServiceInfo = newCustomerServiceInfo(info.CustomerServiceType)
	return nil
}

func newCustomerServiceInfo(customerServiceType int) *CustomerServiceInfo {
	var qq string
	switch customerServiceType {
	case CustomerServiceTypeSpecial:
		qq = customerServiceTypeSpecialQQ
	case CustomerServiceTypeVip:
		qq = customerServiceTypeVipQQ
	default:
		return nil
	}
	return &CustomerServiceInfo{
		ShowText: fmt.Sprintf("QQ：%s", qq),
		CopyText: qq,
	}
}

func (info *Info) setPrice(price *priceInfo) {
	info.Title = price.Title
	info.RegistrationPrice = price.RegistrationPrice
	info.RegistrationRebate = price.RegistrationRebate
	info.RenewalPrice = price.RenewalPrice
	info.RenewalRebate = price.RenewalRebate
}

// ScaleContribution 按贵族等级配置放大等级经验值增加
func (info *Info) ScaleContribution(pointAdd int64) int64 {
	if info.ExpAcceleration > 0 {
		pointAdd = pointAdd * int64(100+info.ExpAcceleration) / 100
	}
	return pointAdd
}

// ListByVipType 根据贵族类型获取特权列表
func ListByVipType(vipType int) (l []*Info, err error) {
	if !goutil.HasElem([]int{TypeLiveNoble, TypeLiveHighness, TypeLiveTrialNoble}, vipType) {
		panic(fmt.Sprintf("unsupported vip type: %d", vipType))
	}
	l = listFromCache(vipType)
	if l != nil {
		// 从缓存获取成功
		return l, nil
	}
	l, err = listNoCache(vipType)
	if err != nil {
		return nil, err
	}
	return l, nil
}

// FindVipInfoByID 根据贵族 ID 获取贵族特权信息
func FindVipInfoByID(vipType int, id int64) (*Info, error) {
	vips, err := ListByVipType(vipType)
	if err != nil {
		return nil, err
	}
	i := goutil.FindIndex(len(vips), func(i int) bool {
		return vips[i].VipID == id
	})
	if i < 0 {
		return nil, nil
	}
	return vips[i], nil
}

// ListAllVip 返回上神和普通贵族特权列表
func ListAllVip() (l []*Info, err error) {
	normal, err := ListByVipType(TypeLiveNoble)
	if err != nil {
		return nil, err
	}
	trialNoble, err := ListByVipType(TypeLiveTrialNoble)
	if err != nil {
		return nil, err
	}
	highness, err := ListByVipType(TypeLiveHighness)
	if err != nil {
		return nil, err
	}
	allVips := make([]*Info, 0, len(normal)+len(trialNoble)+len(highness))
	allVips = append(allVips, normal...)
	allVips = append(allVips, trialNoble...)
	allVips = append(allVips, highness...)
	return allVips, nil
}

func listNoCache(vipType int) ([]*Info, error) {
	priceInfos, vipIDs, err := vipPrice(vipType)
	if err != nil {
		return nil, err
	}
	res := make([]*Info, 0, len(vipIDs))
	err = service.DB.Find(&res, "vip_id IN (?)", vipIDs).Error
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		res[i].setPrice(priceInfos[res[i].VipID])
	}
	saveToCache(vipType, res)
	return res, err
}

func listFromCache(vipType int) []*Info {
	v, ok := service.Cache5Min.Get(keys.KeyVipList1.Format(vipType))
	if !ok {
		return nil
	}
	l := v.([]Info)
	res := make([]*Info, len(l))
	for i := 0; i < len(l); i++ {
		res[i] = new(Info)
		*res[i] = l[i]
	}
	return res
}

// mapVipIDFromCache 从 cache 中获取 map[vipID]
func mapVipIDFromCache(vipType int) map[int64]Info /* map[vipID]Info */ {
	v, ok := service.Cache5Min.Get(keys.KeyMapVipIDInfos1.Format(vipType))
	if !ok {
		return nil
	}
	return v.(map[int64]Info)
}

// mapLevelFromCache 从 cache 中获取 map[level]
func mapLevelFromCache(vipType int) map[int]Info /* map[level]Info */ {
	v, ok := service.Cache5Min.Get(keys.KeyMapVipLevelInfos1.Format(vipType))
	if !ok {
		return nil
	}
	return v.(map[int]Info)
}

func saveToCache(vipType int, infos []*Info) {
	l := make([]Info, len(infos))
	mVipID := make(map[int64]Info, len(infos))
	mLevel := make(map[int]Info, len(infos))
	for i := 0; i < len(infos); i++ {
		l[i] = *infos[i]
		mVipID[l[i].VipID] = l[i]
		mLevel[l[i].Level] = l[i]
	}
	service.Cache5Min.Set(keys.KeyVipList1.Format(vipType), l, cache.DefaultExpiration)
	service.Cache5Min.Set(keys.KeyMapVipIDInfos1.Format(vipType), mVipID, cache.DefaultExpiration)
	service.Cache5Min.Set(keys.KeyMapVipLevelInfos1.Format(vipType), mLevel, cache.DefaultExpiration)
}

// FindByTypeAndLevel 通过贵族类型等级查询贵族特权信息
func FindByTypeAndLevel(vipType, level int) (*Info, error) {
	m := mapLevelFromCache(vipType)
	if m != nil {
		if info, ok := m[level]; ok {
			return &info, nil
		}
		return nil, nil
	}

	list, err := listNoCache(vipType)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(list); i++ {
		if list[i].Level == level {
			return list[i], nil
		}
	}
	return nil, nil
}

// RenewDeadline 计算续费截止日期的时间戳（秒）
func RenewDeadline(expireTime int64) int64 {
	vipConfig, err := GetVipConfig(true)
	if err != nil {
		logger.Error(err)
		// PASS: 获取贵族配置报错时，贵族保护期降级为默认天数
		vipConfig = &ConfigResp{
			LiveNobleProtectDays: vipRenewProtectDay,
		}
	}

	return expireTime + int64(vipConfig.LiveNobleProtectDays)*util.SecondOneDay
}

// IsInvisible 是否是隐身中
func IsInvisible(userID int64, info *Info) (bool, error) {
	if userID <= 0 || info == nil {
		return false, nil
	}
	if !info.Privilege.IsSet(PrivilegeInvisible) {
		return false, nil
	}
	// TODO: 查询用户是否隐身待整合
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_meta")
	var record struct {
		Invisible bool `bson:"invisible"`
	}
	err := collection.FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(bson.M{"invisible": 1})).Decode(&record)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return false, nil
		}
		return false, err
	}
	return record.Invisible, nil
}

// UserHighestVipInfo 用户最高等级贵族信息
func UserHighestVipInfo(infos *UserInfo) *Info {
	if infos == nil {
		return nil
	}
	if infos.LiveHighness != nil {
		return infos.LiveHighness
	}
	if infos.LiveTrialNoble != nil {
		return infos.LiveTrialNoble
	}
	return infos.LiveNoble
}

// UserHavePrivilege 用户是否有贵族某权限
func UserHavePrivilege(vips *UserInfo, privilegeType int) bool {
	if vips == nil {
		return false
	}
	return (vips.LiveHighness != nil && vips.LiveHighness.Privilege.IsSet(privilegeType)) ||
		(vips.LiveNoble != nil && vips.LiveNoble.Privilege.IsSet(privilegeType)) ||
		(vips.LiveTrialNoble != nil && vips.LiveTrialNoble.Privilege.IsSet(privilegeType))
}

// UserInfo 用户 Vip 信息
type UserInfo struct {
	LiveNoble      *Info
	LiveTrialNoble *Info
	LiveHighness   *Info
}

// IsVip 是否是贵族
func (info *UserInfo) IsVip() bool {
	return info != nil && (info.LiveHighness != nil || info.LiveNoble != nil || info.LiveTrialNoble != nil)
}

// MapUsersInfo 用户当前贵族信息
func MapUsersInfo(userIDs []int64, found map[int64]*UserInfo, u util.UserContext) (map[int64]*UserInfo /* map[userID]*UserInfo */, error) {
	if len(userIDs) == 0 {
		return make(map[int64]*UserInfo), nil
	}
	infos, err := userLevelList(userIDs, found, u)
	if err != nil {
		return nil, err
	}
	res := make(map[int64]*UserInfo, len(infos))
	l, err := ListAllVip()
	if err != nil {
		return nil, err
	}

	for _, info := range infos {
		for i := 0; i < len(l); i++ {
			if info.Type == l[i].Type && info.Level == l[i].Level {
				levelInfo := res[info.UserID]
				if levelInfo == nil {
					levelInfo = new(UserInfo)
					res[info.UserID] = levelInfo
				}
				switch l[i].Type {
				case TypeLiveNoble:
					levelInfo.LiveNoble = l[i]
				case TypeLiveTrialNoble:
					levelInfo.LiveTrialNoble = l[i]
				case TypeLiveHighness:
					levelInfo.LiveHighness = l[i]
				}
				break
			}
		}
	}
	return res, nil
}

// NobleMetas 贵族 metas
func NobleMetas() ([]Meta, error) {
	l, err := ListByVipType(TypeLiveNoble)
	if err != nil {
		return nil, err
	}
	metas := make([]Meta, 0, len(l))
	for i := 0; i < len(l); i++ {
		metas = append(metas, Meta{
			Type:           l[i].Type,
			Level:          l[i].Level,
			Title:          l[i].Title,
			EffectURL:      l[i].Effect,
			EffectDuration: l[i].EffectDuration,
			IconMiniURL:    l[i].IconMini,
			NotifyDuration: l[i].NotifyDuration,
			AvatarFrameURL: l[i].AvatarFrame,
			CardFrameURL:   l[i].CardFrame,
		})
	}
	return metas, nil
}

// HighnessMeta 上神 meta
func HighnessMeta() (*Meta, error) {
	l, err := ListByVipType(TypeLiveHighness)
	if err != nil {
		return nil, err
	}
	if len(l) == 0 {
		return nil, nil
	}
	return &Meta{
		Type:        l[0].Type,
		Level:       l[0].Level,
		Title:       l[0].Title,
		IconMiniURL: l[0].IconMini,
	}, nil
}

// ClearUserVipCache clear user vip cache
func ClearUserVipCache(userIDs ...int64) {
	if len(userIDs) == 0 {
		return
	}
	delKeys := make([]string, 0, len(userIDs)*3)
	for _, userID := range userIDs {
		delKeys = append(delKeys,
			keys.KeyNobleUserVips1.Format(userID),
			keys.KeyNobleUserInfo1.Format(userID),
			keys.KeyNobleUserVipLevel1.Format(userID),
		)
	}
	err := service.Redis.Del(delKeys...).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// UserVipFromAppearanceID returns the user vip and noble level from noble appearance id
func UserVipFromAppearanceID(appearanceID int64, uvMap map[int]*UserVip) *UserVip {
	if uvMap == nil {
		return nil
	}
	if appearanceID > appearance.AppearanceIDReserved {
		return nil
	}
	if appearanceID > appearance.AppearanceIDLiveHighnessReserved {
		return uvMap[TypeLiveHighness]
	}
	if uv := uvMap[TypeLiveTrialNoble]; uv != nil {
		return uv
	}
	return uvMap[TypeLiveNoble]
}

// IsUserHighnessRenewDurationMax 用户上神贵族过期时间是否达到上限
func IsUserHighnessRenewDurationMax(expireTime int64) bool {
	y, m, d := goutil.TimeNow().Date()
	maxRenewDayUnix := time.Date(y, m, d, 23, 59, 59, 0, time.Local).
		AddDate(0, 0, HighnessMaxRenewDays).Unix()
	return expireTime >= maxRenewDayUnix
}

// ClearUserVipPrivilege 清空贵族权益
func ClearUserVipPrivilege(userIDs []int64, vipLevelMap map[int64]*UserVip) {
	if len(userIDs) == 0 {
		return
	}

	clearRankInvisibleUserIDs := make([]int64, 0, len(userIDs))
	clearInvisibleUserIDs := make([]int64, 0, len(userIDs))
	clearRecommendUserIDs := make([]int64, 0, len(userIDs))
	clearNobleHornUserIDs := make([]int64, 0, len(userIDs))
	for _, userID := range userIDs {
		uv := vipLevelMap[userID]
		if uv == nil || uv.Info == nil {
			clearRankInvisibleUserIDs = append(clearRankInvisibleUserIDs, userID)
			clearInvisibleUserIDs = append(clearInvisibleUserIDs, userID)
			clearRecommendUserIDs = append(clearRecommendUserIDs, userID)
			clearNobleHornUserIDs = append(clearNobleHornUserIDs, userID)
			continue
		}
		if !HavePrivilege(uv, PrivilegeRankInvisible) {
			clearRankInvisibleUserIDs = append(clearRankInvisibleUserIDs, userID)
		}
		if !HavePrivilege(uv, PrivilegeInvisible) {
			clearInvisibleUserIDs = append(clearInvisibleUserIDs, userID)
		}
		if !HavePrivilege(uv, PrivilegeRecommend) {
			clearRecommendUserIDs = append(clearRecommendUserIDs, userID)
		}
		if uv.Info.HornNum == 0 {
			clearNobleHornUserIDs = append(clearNobleHornUserIDs, userID)
		}
	}
	if len(clearRankInvisibleUserIDs) != 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := service.MongoDB.Collection("live_users").UpdateMany(ctx, bson.M{
			"user_id":        bson.M{"$in": clearRankInvisibleUserIDs},
			"rank_invisible": true,
		}, bson.M{"$set": bson.M{"rank_invisible": false}})
		if err != nil {
			logger.Errorf("清理用户榜单隐身状态失败：%v，涉及用户 ID：%s", err, goutil.JoinInt64Array(clearRankInvisibleUserIDs, ","))
			// PASS
		}
	}

	updates := make([]mongo.WriteModel, 0, 3)
	if len(clearInvisibleUserIDs) != 0 {
		updates = append(updates, mongo.NewUpdateManyModel().SetFilter(bson.M{
			"user_id": bson.M{
				"$in": clearInvisibleUserIDs,
			},
		}).SetUpdate(bson.M{
			"$unset": bson.M{
				"invisible": 0,
			},
		}))
	}
	if len(clearRecommendUserIDs) != 0 {
		updates = append(updates, mongo.NewUpdateManyModel().SetFilter(bson.M{
			"user_id": bson.M{
				"$in": clearRecommendUserIDs,
			},
		}).SetUpdate(bson.M{
			"$unset": bson.M{
				"recommend_num": 0,
			},
		}))
	}
	if len(clearNobleHornUserIDs) != 0 {
		updates = append(updates, mongo.NewUpdateManyModel().SetFilter(bson.M{
			"user_id": bson.M{
				"$in": clearNobleHornUserIDs,
			},
		}).SetUpdate(bson.M{
			"$unset": bson.M{
				"noble_horn_num": 0,
			},
		}))
	}
	if len(updates) == 0 {
		return
	}
	ctx2, cancel2 := service.MongoDB.Context()
	defer cancel2()
	_, err := usermeta.Collection().BulkWrite(ctx2, updates)
	if err != nil {
		logger.Errorf("清空用户隐身状态/神话推荐/贵族喇叭失败：%v，涉及用户 ID：%s;%s;%s", err,
			goutil.JoinInt64Array(clearInvisibleUserIDs, ","),
			goutil.JoinInt64Array(clearRecommendUserIDs, ","),
			goutil.JoinInt64Array(clearNobleHornUserIDs, ","))
		// PASS
	}
}

// BuyNobleExpireFormatTimeTip 购买贵族过期时间提示
// 参数 uvt 为体验贵族；参数 isAddTrialNobleDuration 为是否加上体验贵族剩余时长（开通和当前体验贵族同等级的普通贵族）
func BuyNobleExpireFormatTimeTip(when time.Time, vipInfo *Info, uvt *UserVip, isAddTrialNobleDuration bool) string {
	var (
		now                = goutil.TimeNow()
		trialNobleDuration time.Duration
	)
	if uvt != nil && uvt.IsActive() {
		trialNobleDuration = time.Duration(uvt.ExpireTime-now.Unix()) * time.Second
	}
	if isAddTrialNobleDuration {
		when = when.Add(trialNobleDuration)
	}

	st := goutil.BeginningOfDay(when).AddDate(0, 0, 1) // 贵族默认是第二天零点到期
	maxEt := goutil.BeginningOfDay(now).AddDate(0, 0, 1).Add(LiveNobleMaxRenewDuration)
	// 新开贵族小于或者等于体验贵族时，贵族有效时长从体验贵族到期时间开始计算
	if uvt != nil && uvt.IsActive() && vipInfo.Level <= uvt.Level {
		maxEt = goutil.BeginningOfDay(time.Unix(uvt.ExpireTime, 0)).AddDate(0, 0, 1).Add(LiveNobleMaxRenewDuration)
	}
	et := min(st.Add(thirtyDays).Unix(), maxEt.Unix())

	return time.Unix(et, 0).Format(util.TimeFormatYMDHHMM)
}
