package vip

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMongoUserNoblesKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.BSON)
	kc.Check(projCreatorNoble, "user_id", "name", "level", "price", "is_registration", "bubble", "created_time")
	kc.Check(MongoUserNobles{}, "user_id", "name", "level", "price", "is_registration", "bubble", "created_time")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(MongoUserNobles{}, "type", "user_id", "name", "icon_url", "level", "price",
		"is_registration", "bubble", "created_time", "username", "effect_url", "effect_duration")
}

func TestListNobleBalance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	input := bson.M{
		"user_id":         1000,
		"from_creator_id": 998,
		"name":            "live-service/TestListNobleBalance",
		"price":           998,
		"level":           7,
		"is_registration": 1,
		"created_time":    time.Date(2020, 12, 23, 0, 0, 0, 0, time.Local),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_nobles")
	err := collection.FindOneAndUpdate(ctx, bson.M{
		"user_id": 1000,
		"name":    "live-service/TestListNobleBalance",
	},
		bson.M{"$set": input}, options.FindOneAndUpdate().SetUpsert(true)).Err()
	require.NoError(err)

	res, pa, err := ListNobleBalance(998, 10, 20)
	assert.NoError(err)
	assert.Empty(res)
	assert.False(pa.Valid())
	res, pa, err = ListNobleBalance(998, 1, 20)
	require.NoError(err)
	require.Len(res, 1)
	assert.Equal(goutil.Pagination{P: 1, PageSize: 20, Count: 1, MaxPage: 1}, pa)
	assert.Equal(int64(998), res[0].Price)
	assert.Equal("live-service/TestListNobleBalance", res[0].Name)
	assert.NotEmpty(res[0].Username)

	res, pa, err = ListNobleBalance(998, 1, 20, goutil.TimeNow())
	require.NoError(err)
	require.Empty(res)

	res, pa, err = ListNobleBalance(998, 1, 20, time.Unix(0, 0), goutil.TimeNow())
	require.NoError(err)
	require.Len(res, 1)
}

func TestRoomNobleHistory(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 使用了 TestListNobleBalance 的数据
	r, err := RoomNobleHistory(998, time.Unix(0, 0), goutil.TimeNow(), 0)
	require.NoError(err)
	require.Equal(1, len(r))
	assert.NotEmpty(r[0].IconURL)
	assert.NotEmpty(r[0].EffectURL)
}
