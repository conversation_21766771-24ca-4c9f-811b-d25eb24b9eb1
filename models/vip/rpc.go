package vip

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"time"

	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// urls
const (
	URLVipList = "sso://vip/list"
	// urlBuy       = "sso://vip/buy-noble"

	urlUserLevel         = "sso://vip/user-level"
	URLGetUserByTime     = "sso://vip/get-user-by-time"
	URLUserVips          = "sso://vip/user-vips"            // 获取用户贵族和上神身份信息
	URLLiveUserLevel     = "sso://vip/live-user-level"      // 用户未过期最高等级的直播贵族
	urlBuyLiveHighness   = "sso://vip/buy-live-highness"    // 开通/续订上神贵族
	URLBuyLiveTrialNoble = "sso://vip/buy-live-trial-noble" // 开通/续订体验贵族
	URLVipSetExpire      = "sso://vip/set-expire"           // 设置贵族过期时间
	URLVipConfig         = "sso://vip/config"               // 获取贵族配置

	URLGetExpireBalanceByTime = "app://live/get-expire-balance-by-time"
)

// 贵族状态
const (
	NobleStatusNone      = iota // 没有贵族
	NobleStatusNoble            // 有贵族身份
	NobleStatusProtected        // 贵族身份处于续费保护期
)

// 会员类型
const (
	TypeLiveNoble      = iota + 1 // 普通贵族
	TypeLiveHighness              // 上神
	TypeLiveTrialNoble            // 体验贵族
)

// 贵族允许续费的时长
const (
	// thirtyDays 三十天
	thirtyDays time.Duration = 30 * 24 * time.Hour
)

const (
	vipLimitDay        = 3  // 贵族有效期小于等于 3 天
	vipRenewProtectDay = 10 // 贵族保护期（天）
)

// 上神续费阈值
const (
	HighnessRenewalThreshold int64 = 5000000 // 续费上神所需累计消费总钻石数
)

// 获取贵族钻石某时间范围内过期数据的类型
const (
	TypeExpireBalanceExpired = iota // 取过期数据
	TypeExpireBalanceClear          // 取清零数据
)

// HighnessVipID 上神贵族 ID
const HighnessVipID int64 = 8 // 上神贵族

// HighnessLevel 上神贵族等级
const HighnessLevel = 1

const (
	// statusAll 全部会员
	statusAll = iota
	// statusActive 在有效期内会员
	statusActive
)

var emptyCtx = goutil.SmartUserContext{}

type priceInfo struct {
	ID    int64  `json:"id"`
	Level int    `json:"level"`
	Title string `json:"title"`

	RegistrationPrice  int `json:"registration_price"`
	RegistrationRebate int `json:"registration_rebate"`
	RenewalPrice       int `json:"renewal_price"`
	RenewalRebate      int `json:"renewal_rebate"`
}

// IUser 用户信息接口
type IUser interface {
	UserID() int64
	Token() string
	ClientIP() string
	EquipID() string
}

// vipPrice 从 sso 获取贵族价格
func vipPrice(vipType int) (map[int64]*priceInfo, []int64, error) {
	var resp struct {
		// UserVip *UserVip `json:"user_vip"` 这里不用这个变量
		Data []*priceInfo `json:"Datas"`
	}
	err := service.MRPC.Do(mrpc.NewUserContextFromEnv(), URLVipList, map[string]int{"type": vipType}, &resp)
	if err != nil {
		return nil, nil, err
	}
	vipIDs := make([]int64, len(resp.Data))
	priceMap := make(map[int64]*priceInfo, len(resp.Data))
	for i := 0; i < len(vipIDs); i++ {
		vipIDs[i] = resp.Data[i].ID
		priceMap[resp.Data[i].ID] = resp.Data[i]
	}
	return priceMap, vipIDs, nil
}

// UserVip 用户的 vip 信息
type UserVip struct {
	UserID int64 `json:"user_id"`

	VipID      int64  `json:"vip_id"`
	Title      string `json:"title"`
	Level      int    `json:"level"`
	Type       int    `json:"type"`
	ExpireTime int64  `json:"expire_time"` // 结束时间都在 23:59:59

	RenewalRebate int `json:"renewal_rebate,omitempty"`

	Info *Info `json:"-"`
}

// HavePrivilege 某用户是否有权限
func HavePrivilege(uv *UserVip, privilege int) bool {
	return uv != nil && uv.IsActive() && uv.Info != nil &&
		uv.Info.Privilege.IsSet(privilege)
}

// UserVipLevel 用户 ID 和等级
type UserVipLevel struct {
	UserID     int64 `json:"user_id"`
	Type       int   `json:"type"`
	Level      int   `json:"level"`
	ExpireTime int64 `json:"expire_time,omitempty"` // 过期时间，rpc 获取
}

func userLevelList(userIDs []int64, found map[int64]*UserInfo, c util.UserContext) ([]UserVipLevel, error) {
	userIDs = util.Uniq(userIDs)
	res, remains := loadLevelsFromCache(userIDs, found)
	if len(remains) > 0 {
		if c == nil {
			c = emptyCtx
		}
		var resp []UserVipLevel
		err := service.MRPC.Call(urlUserLevel, c.ClientIP(), map[string]interface{}{
			"types":    []int64{TypeLiveNoble, TypeLiveTrialNoble, TypeLiveHighness},
			"user_ids": remains,
			"status":   1,
		}, &resp, map[string]string{"token": c.Token(), "equip_id": c.EquipID()})
		if err != nil {
			return nil, err
		}

		saveLevelsToCache(remains, resp)
		res = append(res, resp...)
	}
	return res, nil
}

func loadLevelsFromCache(userIDs []int64, found map[int64]*UserInfo) (res []UserVipLevel, remains []int64) {
	defer func() {
		for userID, val := range found {
			if val == nil {
				continue
			}
			if val.LiveNoble != nil {
				res = append(res, UserVipLevel{UserID: userID, Type: TypeLiveNoble, Level: val.LiveNoble.Level})
			}
			if val.LiveTrialNoble != nil {
				res = append(res, UserVipLevel{UserID: userID, Type: TypeLiveTrialNoble, Level: val.LiveTrialNoble.Level})
			}
			if val.LiveHighness != nil {
				res = append(res, UserVipLevel{UserID: userID, Type: TypeLiveHighness, Level: val.LiveHighness.Level})
			}
		}
	}()
	userIDStr := make([]string, 0, len(userIDs))
	userIDRemain := make([]int64, 0, len(userIDs))
	for i := range userIDs {
		if _, ok := found[userIDs[i]]; !ok {
			userIDStr = append(userIDStr, strconv.FormatInt(userIDs[i], 10))
			userIDRemain = append(userIDRemain, userIDs[i])
		}
	}
	if len(userIDStr) == 0 {
		return
	}
	listKey := make([]string, 0, len(userIDRemain))
	for _, userID := range userIDRemain {
		key := keys.KeyNobleUserVipLevel1.Format(userID)
		listKey = append(listKey, key)
	}
	rec, err := service.Redis.MGet(listKey...).Result()
	if err != nil {
		logger.Error(err)
		remains = userIDRemain
		return
	}
	remains = make([]int64, 0, len(userIDRemain))
	res = make([]UserVipLevel, 0, len(rec)*3)
	for i := range rec {
		switch v := rec[i].(type) {
		case string:
			var levelInfo map[int]int
			err := json.Unmarshal([]byte(v), &levelInfo)
			if err != nil {
				logger.Error(err)
				remains = append(remains, userIDRemain[i])
				continue
			}
			for vipType, level := range levelInfo {
				res = append(res, UserVipLevel{UserID: userIDRemain[i], Type: vipType, Level: level})
			}
		default:
			// 没找到对应的值
			remains = append(remains, userIDRemain[i])
		}
	}
	return
}

func saveLevelsToCache(userIDs []int64, levelInfos []UserVipLevel) {
	if len(userIDs) == 0 {
		return
	}
	values := make(map[int64]map[int]int, len(userIDs))
	for _, info := range levelInfos {
		v := values[info.UserID]
		if v == nil {
			values[info.UserID] = map[int]int{info.Type: info.Level}
		} else {
			v[info.Type] = info.Level
		}
	}

	for _, userID := range userIDs {
		if _, ok := values[userID]; !ok {
			// 没有获取贵族信息也要生成空缓存
			values[userID] = map[int]int{}
		}
	}
	res := make([]interface{}, 0, len(values)*2)
	for key, value := range values {
		b, err := json.Marshal(value)
		if err != nil {
			logger.Error(err)
			continue
			// PASS
		}
		res = append(res, strconv.FormatInt(key, 10))
		res = append(res, string(b))
	}
	setLevelInfos(res...)
}

// IsActive 贵族是否是生效中
func (uv UserVip) IsActive() bool {
	return uv.ExpireTime > goutil.TimeNow().Unix()
}

// BeforeRenewDeadline 在续费保护期内
// TODO: 按照贵族类型返回多个续费期状态
func (uv UserVip) BeforeRenewDeadline() bool {
	now := goutil.TimeNow().Unix()
	return uv.ExpireTime <= now && now <= RenewDeadline(uv.ExpireTime)
}

// UserNoble 新的贵族结构体
type UserNoble struct {
	Name       string `json:"name,omitempty"`
	Type       int    `json:"type,omitempty"`
	Level      int    `json:"level,omitempty"`
	ExpireTime int64  `json:"expire_time,omitempty"`
	Status     int    `json:"status"`

	Spend            *int64 `json:"spend,omitempty"`             // 续费上神当前累计消费钻石数
	RenewalThreshold *int64 `json:"renewal_threshold,omitempty"` // 续费上神所需累计消费总钻石数

	// TODO: 对不同场景下发不同文案
	Tip string `json:"tip"`

	RemainDays int64 `json:"-"`
}

// NewUserNoble 生成新的贵族数据
func NewUserNoble(uv *UserVip) *UserNoble {
	un := new(UserNoble)
	if uv == nil {
		un.Status = NobleStatusNone
		un.Tip = "开通贵族优先上榜哦~"
		return un
	}
	un.Type = uv.Type
	now := goutil.TimeNow().Unix()

	buildSpend := func(un *UserNoble) {
		switch un.Type {
		case TypeLiveHighness:
			un.Spend = util.NewInt64(usermeta.HighnessSpend(uv.UserID))
			un.RenewalThreshold = util.NewInt64(HighnessRenewalThreshold)
		}
	}

	var (
		renewProtectTime  int64
		isNobleOrHighness = uv.Type == TypeLiveNoble || uv.Type == TypeLiveHighness // 是否是普通贵族或者上神
	)
	if isNobleOrHighness {
		// 只有普通和上神贵族有续费保护期
		renewProtectTime = RenewDeadline(uv.ExpireTime)
	}
	switch {
	case now < uv.ExpireTime:
		un.Name = uv.Title
		un.ExpireTime = uv.ExpireTime
		un.Level = uv.Level
		un.Tip = BuildTip(uv.Type, uv.Title, uv.ExpireTime, false)
		if uv.Type == TypeLiveTrialNoble {
			un.RemainDays = DayDuration(uv.ExpireTime, now)
		} else {
			limitTime := uv.ExpireTime - vipLimitDay*util.SecondOneDay
			if now >= limitTime {
				un.RemainDays = DayDuration(uv.ExpireTime, now)
			}
		}
		un.Status = NobleStatusNoble
		buildSpend(un)
	case isNobleOrHighness && now < renewProtectTime:
		un.Name = uv.Title
		un.ExpireTime = uv.ExpireTime
		un.Level = uv.Level
		un.Tip = BuildTip(uv.Type, uv.Title, renewProtectTime, true)
		un.RemainDays = DayDuration(renewProtectTime, now)
		un.Status = NobleStatusProtected
		buildSpend(un)
	default:
		un.Status = NobleStatusNone
		un.Tip = "开通贵族优先上榜哦~"
	}
	return un
}

// DayDuration 计算天数，小数进一
func DayDuration(big, small int64) int64 {
	if big <= small {
		panic("big is not greater than small")
	}
	return int64(math.Ceil(float64(big-small) / float64(util.SecondOneDay)))
}

// GetUserByTime 通过过期时间区间获取贵族用户，查询 (startTime, endTime]（单位：秒）范围内贵族过期的用户
func GetUserByTime(typeLive int, startTime, endTime int64) ([]UserVip, error) {
	var resp []UserVip
	err := service.MRPC.Do(mrpc.NewUserContextFromEnv(), URLGetUserByTime, map[string]interface{}{
		"type":       typeLive,
		"start_time": startTime,
		"end_time":   endTime,
	}, &resp)
	return resp, err
}

// UserActivatedVip 返回生效中的最高等级贵族（含上神贵族）
func UserActivatedVip(userID int64, getRenewalRebate bool, c goutil.UserContext) (*UserVip, error) {
	uvs, err := UserVipInfos(userID, getRenewalRebate, c)
	if err != nil {
		return nil, err
	}
	now := goutil.TimeNow().Unix()
	if uvs[TypeLiveHighness] != nil && uvs[TypeLiveHighness].ExpireTime > now {
		return uvs[TypeLiveHighness], nil
	}
	if uvs[TypeLiveTrialNoble] != nil && uvs[TypeLiveTrialNoble].ExpireTime > now {
		return uvs[TypeLiveTrialNoble], nil
	}
	if uvs[TypeLiveNoble] != nil && uvs[TypeLiveNoble].ExpireTime > now {
		return uvs[TypeLiveNoble], nil
	}
	return nil, nil
}

// IsVipEq 比较用户当前贵族等级是否相等
func IsVipEq(uv *UserVip, vipType, level int) bool {
	if uv == nil {
		return false
	}
	if uv.Type == TypeLiveHighness {
		return vipType == TypeLiveHighness && uv.Level == level
	}
	// 普通贵族和体验贵族合并判断
	return vipType != TypeLiveHighness && uv.Level == level
}

// IsVipGte 比较用户当前贵族等级是否不低于某类型的贵族等级
// 上神 > 高等级普通贵族 > 低等级普通贵族
func IsVipGte(uv *UserVip, vipType, level int) bool {
	if uv == nil {
		return false
	}
	if uv.Type == TypeLiveHighness {
		return vipType != TypeLiveHighness || (vipType == TypeLiveHighness && uv.Level >= level)
	}
	// 普通贵族和体验贵族合并判断
	return vipType != TypeLiveHighness && uv.Level >= level
}

// UserVipsResp 用户贵族详情 rpc 的返回值
type UserVipsResp struct {
	Vips map[int]*UserVip `json:"vips"`
}

// UserVipInfos 用户普通贵族和上神贵族信息
// getRenewalRebate: 为 true 的时候会返回 renewal_rebate
// TODO: resp 返回结构体
func UserVipInfos(userID int64, getRenewalRebate bool, c goutil.UserContext) (resp map[int]*UserVip, err error) {
	defer func() {
		if resp != nil {
			if resp[TypeLiveNoble] != nil {
				resp[TypeLiveNoble].Info, err = FindByTypeAndLevel(TypeLiveNoble, resp[TypeLiveNoble].Level)
			}
			if resp[TypeLiveTrialNoble] != nil {
				resp[TypeLiveTrialNoble].Info, err = FindByTypeAndLevel(TypeLiveTrialNoble, resp[TypeLiveTrialNoble].Level)
			}
			if resp[TypeLiveHighness] != nil {
				resp[TypeLiveHighness].Info, err = FindByTypeAndLevel(TypeLiveHighness, resp[TypeLiveHighness].Level)
			}
		}
	}()
	if userID == 0 {
		return
	}
	// getRenewalRebate 为 true 的时候不走缓存
	if !getRenewalRebate {
		// 从 cache 获取 UserVips
		key := keys.KeyNobleUserVips1.Format(userID)
		b, ignoreErr := service.Redis.Get(key).Bytes()
		if ignoreErr != nil && !serviceredis.IsRedisNil(ignoreErr) {
			logger.Error(ignoreErr)
			// PASS
		}
		if len(b) != 0 {
			var uvs map[int]*UserVip
			ignoreErr = json.Unmarshal(b, &uvs)
			if ignoreErr != nil {
				logger.Error(ignoreErr)
				// PASS
			} else {
				resp = make(map[int]*UserVip)
				if uvs[TypeLiveNoble] != nil && uvs[TypeLiveNoble].UserID == userID {
					resp[TypeLiveNoble] = uvs[TypeLiveNoble]
				}
				if uvs[TypeLiveTrialNoble] != nil && uvs[TypeLiveTrialNoble].UserID == userID {
					resp[TypeLiveTrialNoble] = uvs[TypeLiveTrialNoble]
				}
				if uvs[TypeLiveHighness] != nil && uvs[TypeLiveHighness].UserID == userID {
					resp[TypeLiveHighness] = uvs[TypeLiveHighness]
				}
				if len(resp) == 0 {
					resp = nil
				}
				return
			}
		}
	}
	body := map[string]interface{}{
		"user_id": userID,
		"params": []map[string]interface{}{
			{
				"type":   TypeLiveHighness,
				"status": statusAll,
			},
			{
				"type":               TypeLiveNoble,
				"get_renewal_rebate": util.BoolToInt(getRenewalRebate),
				"status":             statusAll,
			},
			{
				"type":   TypeLiveTrialNoble,
				"status": statusActive,
			},
		},
	}
	if c == nil {
		c = emptyCtx
	}
	var userVipsResp UserVipsResp
	err = service.MRPC.Call(URLUserVips, c.ClientIP(), body, &userVipsResp,
		map[string]string{"token": c.Token(), "equip_id": c.EquipID()})
	if err != nil {
		return
	}
	resp = userVipsResp.Vips
	saveUVsToCache(userID, resp)
	return
}

// 保存用户普通贵族和上神信息
func saveUVsToCache(userID int64, uvs map[int]*UserVip) {
	var tmpUv map[int]*UserVip
	if uvs != nil {
		tmpUv = make(map[int]*UserVip)
		if uvs[TypeLiveNoble] != nil {
			tmpUv[TypeLiveNoble] = new(UserVip)
			*tmpUv[TypeLiveNoble] = *uvs[TypeLiveNoble]
			tmpUv[TypeLiveNoble].RenewalRebate = 0 // 缓存不存 RenewalRebate
		}
		if uvs[TypeLiveTrialNoble] != nil {
			tmpUv[TypeLiveTrialNoble] = new(UserVip)
			*tmpUv[TypeLiveTrialNoble] = *uvs[TypeLiveTrialNoble]
		}
		if uvs[TypeLiveHighness] != nil {
			tmpUv[TypeLiveHighness] = new(UserVip)
			*tmpUv[TypeLiveHighness] = *uvs[TypeLiveHighness]
		}
	}
	b, err := json.Marshal(tmpUv)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		key := keys.KeyNobleUserVips1.Format(userID)
		err = service.Redis.Set(key, string(b), 5*time.Minute).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	// 存入 redis
	levelInfos := make(map[int]int)
	if uvs == nil {
		l, err := json.Marshal(levelInfos)
		if err != nil {
			logger.Error(err)
			// PASS
			return
		}
		setLevelInfos(userID, l)
		return
	}
	if highness := uvs[TypeLiveHighness]; highness != nil && highness.IsActive() {
		levelInfos[TypeLiveHighness] = highness.Level
	}
	if noble := uvs[TypeLiveNoble]; noble != nil && noble.IsActive() {
		levelInfos[TypeLiveNoble] = noble.Level
	}
	if trialNoble := uvs[TypeLiveTrialNoble]; trialNoble != nil && trialNoble.IsActive() {
		levelInfos[TypeLiveTrialNoble] = trialNoble.Level
	}
	l, err := json.Marshal(levelInfos)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	setLevelInfos(userID, l)
}

// setLevelInfos set 用户贵族身份等级信息
// values: [userID1, cache1, userID2, cache2]
func setLevelInfos(values ...interface{}) {
	if len(values) == 0 {
		return
	}
	pipe := service.Redis.Pipeline()
	for i := range values {
		if i%2 == 0 {
			var userID int64
			switch v := values[i].(type) {
			case string:
				var err error
				userID, err = strconv.ParseInt(v, 10, 64)
				if err != nil {
					logger.Error(err)
					continue
				}
			case int64:
				userID = v
			default:
				continue
			}
			key := keys.KeyNobleUserVipLevel1.Format(userID)
			// 5 分钟的基础上随机加一个 30 秒内的过期时间，防止缓存击穿
			ttl := time.Duration(5*60+rand.Intn(30)) * time.Second
			pipe.Set(key, values[i+1], ttl)
		}
	}
	_, err := pipe.Exec()
	if err != nil {
		logger.WithField("values", values).Error(err)
		return
	}
}

const (
	// noVipMaxMedalNum 超粉上线后普通用户最大勋章数量
	noVipMaxMedalNum int64 = 30
)

// MaxMedalLimitNum 获取贵族勋章数量限制
func MaxMedalLimitNum(uv *UserVip) int64 {
	if uv != nil && uv.IsActive() && uv.Info != nil {
		return int64(uv.Info.GetMedalNum())
	}
	// WORKAROUND: maxMedalNum 普通用户粉丝勋章数量限制，与 livemedal.UserMaxMedalCount() 含义一致，因为循环引用问题所以定义了本常量
	maxMedalNum := noVipMaxMedalNum
	return maxMedalNum
}

// LiveUsersLevelResp 根据用户 ID 返回对应用户未过期最高等级的直播贵族响应
type LiveUsersLevelResp struct {
	Data []*UserVip `json:"data"`
}

// LiveUserLevelByUserIDs 根据用户 ID 返回对应用户未过期最高等级的直播贵族
func LiveUserLevelByUserIDs(userIDs []int64) ([]*UserVip, error) {
	var resp LiveUsersLevelResp
	err := service.MRPC.Do(mrpc.NewUserContextFromEnv(), URLLiveUserLevel, map[string]interface{}{
		"user_ids": userIDs,
	}, &resp)
	if err != nil {
		return nil, err
	}
	for i := range resp.Data {
		resp.Data[i].Info, err = FindByTypeAndLevel(resp.Data[i].Type, resp.Data[i].Level)
		if err != nil {
			return nil, err
		}
	}
	return resp.Data, nil
}

// BuildTip 贵族过期时间的相关提示
func BuildTip(vipType int, vipTitle string, endTime int64, isBeforeRenewDeadline bool) string {
	if vipType != TypeLiveTrialNoble && isBeforeRenewDeadline {
		return fmt.Sprintf("%s续费保护结束：%s", vipTitle, FormatTipEndTime(vipType, endTime))
	}

	if vipType == TypeLiveTrialNoble {
		return fmt.Sprintf("%s体验到期：%s", vipTitle, FormatTipEndTime(vipType, endTime))
	}
	return fmt.Sprintf("%s到期：%s", vipTitle, FormatTipEndTime(vipType, endTime))
}

// FormatTipEndTime 贵族过期时间的相关提示
func FormatTipEndTime(nobleType int, endTime int64) string {
	if nobleType == TypeLiveTrialNoble {
		// 体验贵族过期时间不一定是整点，可以是任意一个时间点
		return time.Unix(endTime, 0).Format(util.TimeFormatYMDHHMM)
	}
	// NOTICE: 贵族结束时间为闭区间，都在 23:59:59 时结束，这里处理返回结束时间下一天的 0 点
	return util.BeginningOfDay(time.Unix(endTime+util.SecondOneDay, 0)).Format(util.TimeFormatYMDHHMM)
}

// BuyLiveHighness 开通/续费 上神贵族
func BuyLiveHighness(userID int64, isRegistration bool) (*UserVip, error) {
	var uv struct {
		VipInfo UserVip `json:"vip_info"`
	}
	err := service.MRPC.Call(urlBuyLiveHighness, "", map[string]interface{}{
		"user_id":         userID,
		"vip_id":          HighnessVipID,
		"is_registration": isRegistration,
	}, &uv)
	if err != nil {
		return nil, err
	}
	return &uv.VipInfo, nil
}

// BuyTrialNobleRequestParam 开通/续费体验贵族请求参数
type BuyTrialNobleRequestParam struct {
	VipID         int64  `json:"vip_id"`
	UserID        int64  `json:"user_id"`
	CreatorID     int64  `json:"creator_id"`
	Num           int64  `json:"num"`
	Duration      int64  `json:"duration"`
	LiveOpenLogID string `json:"live_open_log_id,omitempty"`
	IP            string `json:"ip"`
	UserAgent     string `json:"user_agent"`
	EquipID       string `json:"equip_id"`
	BUVID         string `json:"buvid"`
}

// BuyLiveTrialNoble 开通/续费 体验贵族
func BuyLiveTrialNoble(ctx mrpc.UserContext, param *BuyTrialNobleRequestParam) (*UserVip, error) {
	var uv struct {
		VipInfo UserVip `json:"vip_info"`
	}
	err := service.MRPC.Do(ctx, URLBuyLiveTrialNoble, param, &uv)
	if err != nil {
		return nil, err
	}
	return &uv.VipInfo, nil
}

// SetExpireResp 设置贵族过期时间接口响应
type SetExpireResp struct {
	UserIDs    []int64 `json:"user_ids"`
	ExpireTime int64   `json:"expire_time"`
}

// SetExpire 设置贵族过期时间
func SetExpire(userIDs []int64, vipTypes []int, protection bool) (*SetExpireResp, error) {
	var resp SetExpireResp
	err := service.MRPC.Call(URLVipSetExpire, "", map[string]interface{}{
		"user_ids":   userIDs,
		"vip_types":  vipTypes,
		"protection": protection,
	}, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// UserExpireBalance 用户即将过期钻石
type UserExpireBalance struct {
	UserID       int64 `json:"user_id"`
	TotalBalance int64 `json:"total_balance"`
}

// GetUserExpireBalanceByTime 查询 [startTime, endTime)（单位：秒）范围内过期的钻石
// 数据库里存的是过期日的最后一秒，查询的时候使用开区间，这里传入的结束时间需要为次日零点的时间
func GetUserExpireBalanceByTime(startTime, endTime int64, expireBalanceType int) ([]UserExpireBalance, error) {
	var resp []UserExpireBalance
	err := service.MRPC.Do(mrpc.NewUserContextFromEnv(), URLGetExpireBalanceByTime, map[string]interface{}{
		"start_time": startTime,
		"end_time":   endTime,
		"type":       expireBalanceType,
	}, &resp)
	return resp, err
}

// ConfigResp 贵族配置
type ConfigResp struct {
	EnableNewVipBalanceTime int64 `json:"enable_new_vip_balance_time"`
	LiveNobleProtectDays    int   `json:"live_noble_protect_days"`
}

// GetVipConfig 获取贵族配置
func GetVipConfig(useCache bool) (*ConfigResp, error) {
	key := keys.LocalKeyVipConfig0.Format()
	var resp ConfigResp
	if useCache {
		res, ok := service.Cache5Min.Get(key)
		if ok {
			resp, ok = res.(ConfigResp)
			if ok {
				return &resp, nil
			}
		}
	}

	err := service.MRPC.Do(mrpc.NewUserContextFromEnv(), URLVipConfig, nil, &resp)
	if err != nil {
		return nil, err
	}
	service.Cache5Min.Set(key, resp, 0)

	return &resp, nil
}

// NewUserVipInfo 生成用户贵族信息
func NewUserVipInfo(uvs map[int]*UserVip) *UserInfo {
	info := new(UserInfo)
	if noble := uvs[TypeLiveNoble]; noble != nil && noble.IsActive() {
		info.LiveNoble = noble.Info
	}
	if highness := uvs[TypeLiveHighness]; highness != nil && highness.IsActive() {
		info.LiveHighness = highness.Info
	}
	if trialNoble := uvs[TypeLiveTrialNoble]; trialNoble != nil && trialNoble.IsActive() {
		info.LiveTrialNoble = trialNoble.Info
	}
	return info
}
