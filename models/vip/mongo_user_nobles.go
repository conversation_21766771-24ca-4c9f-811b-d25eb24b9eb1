package vip

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

const filterCreatorID = "from_creator_id"
const filterIsRegistration = "is_registration"

var projCreatorNoble = struct {
	UserID         int `bson:"user_id"`
	Name           int `bson:"name"`
	Level          int `bson:"level"`
	Price          int `bson:"price"`
	IsRegistration int `bson:"is_registration"`
	Bubble         int `bson:"bubble,omitempty"`
	CreatedTime    int `bson:"created_time"`
}{1, 1, 1, 1, 1, 1, 1}

// MongoUserNobles mongodb 中，主播收到收益的开通记录
type MongoUserNobles struct {
	UserID         int64          `bson:"user_id" json:"user_id"`
	Name           string         `bson:"name" json:"name"`
	IconURL        string         `bson:"-" json:"icon_url,omitempty"`
	Level          int            `bson:"level" json:"level"`
	Price          int64          `bson:"price" json:"price"`
	IsRegistration int            `bson:"is_registration" json:"is_registration"`
	Bubble         *bubble.Simple `bson:"bubble" json:"bubble,omitempty"`
	CreatedTime    time.Time      `bson:"created_time" json:"created_time"`

	// 开通者信息
	Username string `bson:"-" json:"username,omitempty"`

	// 特效
	EffectURL      string `bson:"-" json:"effect_url,omitempty"`
	EffectDuration int64  `bson:"-" json:"effect_duration,omitempty"`

	Type string `bson:"-" json:"type,omitempty"` // 历史消息中标记消息类型
}

// ListNobleBalance 列出主播的直播间开通的贵族
// 参数 times: times[0] start_time，times[1] end_time，length 超过 2 会 panic
// TODO: 需要返回贵族图标
func ListNobleBalance(creatorID, p, pageSize int64, times ...time.Time) ([]*MongoUserNobles, util.Pagination, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_nobles")

	filter := bson.M{
		filterCreatorID:      creatorID,
		filterIsRegistration: 1,
		// 隐藏 2020-06-01 00:00:00 之前的数据
		"created_time": bson.M{
			"$gte": time.Unix(1590940800, 0),
		},
	}

	switch len(times) {
	case 0:
		// PASS
	case 1:
		filter["created_time"] = bson.M{
			"$gte": times[0],
		}
	case 2:
		filter["created_time"] = bson.M{
			"$gte": times[0],
			"$lt":  times[1],
		}
	default:
		panic("too many time values")
	}

	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, util.Pagination{}, err
	}
	pa := util.MakePagination(count, p, pageSize)
	if !pa.Valid() {
		return make([]*MongoUserNobles, 0), pa, nil
	}
	proj := projCreatorNoble
	proj.Bubble = 0
	cur, err := collection.Find(ctx, filter, pa.SetFindOptions(nil).SetProjection(
		proj))
	if err != nil {
		return nil, pa, err
	}
	defer cur.Close(ctx)
	var res []*MongoUserNobles
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, pa, err
	}
	userIDs := make([]int64, len(res))
	for i := 0; i < len(res); i++ {
		userIDs[i] = res[i].UserID
	}
	// NOTICE: 不能引用 liveuser 会循环引用
	u, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, pa, err
	}
	for i := 0; i < len(res); i++ {
		if user := u[res[i].UserID]; user != nil {
			res[i].Username = user.Username
		}
	}
	return res, pa, nil
}

// RoomNobleHistory 房间内开通贵族的历史消息记录
func RoomNobleHistory(creatorID int64, startTime time.Time, endTime time.Time, limit int64) ([]*MongoUserNobles, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_nobles")

	type m bson.M
	filter := m{
		filterCreatorID: creatorID,
		"created_time":  m{"$gte": startTime, "$lt": endTime},
	}
	opts := options.Find().SetSort(m{"created_time": -1}).SetProjection(projCreatorNoble)
	if limit > 0 {
		opts = opts.SetLimit(limit)
	}

	cur, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var res []*MongoUserNobles
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		info, err := FindByTypeAndLevel(TypeLiveNoble, res[i].Level)
		if err != nil {
			logger.Error(err)
		}
		if info == nil {
			continue
		}
		res[i].IconURL = info.IconMini
		res[i].EffectURL = info.Effect
		res[i].EffectDuration = info.NotifyDuration
	}
	return res, nil
}
