package vip

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestVipTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(UserNoble{}, "name", "type", "level", "expire_time", "status", "spend", "renewal_threshold", "tip")
}

func TestVipPrice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mapInfo, vipIDs, err := vipPrice(TypeLiveNoble)
	require.NoError(err)
	assert.Equal(len(vipIDs), len(mapInfo))
	for i := 0; i < len(vipIDs); i++ {
		assert.NotNil(mapInfo[vipIDs[i]], i)
	}
}

const (
	testAllVipUserID = iota + 1
	testLiveNobleUserID
	testHighnessUserID
	testNoVipUserID
)

func TestIsVipEq(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsVipEq(nil, TypeLiveNoble, 1))

	assert.True(IsVipEq(&UserVip{Type: TypeLiveNoble, Level: 1}, TypeLiveNoble, 1))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveNoble, Level: 1}, TypeLiveNoble, 2))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveNoble, Level: 2}, TypeLiveNoble, 1))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveNoble, Level: 7}, TypeLiveHighness, 1))
	assert.True(IsVipEq(&UserVip{Type: TypeLiveNoble, Level: 1}, TypeLiveTrialNoble, 1))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveNoble, Level: 1}, TypeLiveTrialNoble, 2))

	assert.False(IsVipEq(&UserVip{Type: TypeLiveHighness, Level: 1}, TypeLiveNoble, 7))
	assert.True(IsVipEq(&UserVip{Type: TypeLiveHighness, Level: 1}, TypeLiveHighness, 1))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveHighness, Level: 1}, TypeLiveTrialNoble, 1))

	assert.True(IsVipEq(&UserVip{Type: TypeLiveTrialNoble, Level: 5}, TypeLiveNoble, 5))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveTrialNoble, Level: 5}, TypeLiveNoble, 6))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveTrialNoble, Level: 5}, TypeLiveHighness, 1))
	assert.True(IsVipEq(&UserVip{Type: TypeLiveTrialNoble, Level: 6}, TypeLiveTrialNoble, 6))
	assert.False(IsVipEq(&UserVip{Type: TypeLiveTrialNoble, Level: 6}, TypeLiveTrialNoble, 7))
}

func TestIsVipGte(t *testing.T) {
	assert := assert.New(t)

	assert.False(IsVipGte(nil, TypeLiveNoble, 1))
	assert.False(IsVipGte(&UserVip{Type: TypeLiveNoble, Level: 1}, TypeLiveNoble, 2))
	assert.True(IsVipGte(&UserVip{Type: TypeLiveNoble, Level: 1}, TypeLiveNoble, 1))
	assert.False(IsVipGte(&UserVip{Type: TypeLiveNoble, Level: 7}, TypeLiveHighness, 1))
	assert.True(IsVipGte(&UserVip{Type: TypeLiveNoble, Level: 2}, TypeLiveNoble, 1))
	assert.True(IsVipGte(&UserVip{Type: TypeLiveHighness, Level: 1}, TypeLiveNoble, 7))

	assert.False(IsVipGte(&UserVip{Type: TypeLiveTrialNoble, Level: 1}, TypeLiveTrialNoble, 7))
	assert.True(IsVipGte(&UserVip{Type: TypeLiveTrialNoble, Level: 7}, TypeLiveTrialNoble, 6))
	assert.False(IsVipGte(&UserVip{Type: TypeLiveTrialNoble, Level: 1}, TypeLiveNoble, 7))
	assert.True(IsVipGte(&UserVip{Type: TypeLiveTrialNoble, Level: 7}, TypeLiveNoble, 6))
}

func mockUserVipsRPC() func() {
	cancelMock := mrpc.SetMock(URLUserVips,
		func(input interface{}) (interface{}, error) {
			body := input.(map[string]interface{})
			normalNoble := &UserVip{
				VipID:         1,
				Title:         "练习生",
				Level:         1,
				Type:          1,
				ExpireTime:    123456789,
				RenewalRebate: 100,
			}
			highness := &UserVip{
				VipID:      8,
				Title:      "上神",
				Level:      1,
				Type:       2,
				ExpireTime: 123456789,
			}
			switch body["user_id"].(int64) {
			case testAllVipUserID:
				normalNoble.UserID = testAllVipUserID
				highness.UserID = testAllVipUserID
				return UserVipsResp{Vips: map[int]*UserVip{TypeLiveNoble: normalNoble, TypeLiveHighness: highness}}, nil
			case testLiveNobleUserID:
				normalNoble.UserID = testLiveNobleUserID
				return UserVipsResp{Vips: map[int]*UserVip{TypeLiveNoble: normalNoble}}, nil
			case testHighnessUserID:
				highness.UserID = testHighnessUserID
				return UserVipsResp{Vips: map[int]*UserVip{TypeLiveHighness: highness}}, nil
			default:
				return nil, nil
			}
		})
	return cancelMock
}

func TestUserVipInfos(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancelMock := mockUserVipsRPC()
	defer cancelMock()

	uv, err := UserVipInfos(0, false, nil)
	assert.NoError(err)
	assert.Nil(uv)

	uv, err = UserVipInfos(testAllVipUserID, true, nil) // 上神和普通贵族身份都存在
	assert.NoError(err)
	require.NotNil(uv)
	require.NotNil(uv[TypeLiveNoble])
	require.NotNil(uv[TypeLiveHighness])
	assert.Equal(TypeLiveNoble, uv[TypeLiveNoble].Type)
	assert.Equal(1, uv[TypeLiveNoble].Level)
	assert.Equal(100, uv[TypeLiveNoble].RenewalRebate)
	assert.Equal(TypeLiveHighness, uv[TypeLiveHighness].Type)
	assert.Equal(1, uv[TypeLiveHighness].Level)
	uvCache, err := UserVipInfos(testAllVipUserID, false, nil) // 第二遍从缓存获取
	assert.NoError(err)
	assert.Equal(TypeLiveNoble, uvCache[TypeLiveNoble].Type)
	assert.Equal(1, uvCache[TypeLiveNoble].Level)
	assert.Zero(uvCache[TypeLiveNoble].RenewalRebate)
	assert.Equal(TypeLiveHighness, uvCache[TypeLiveHighness].Type)
	assert.Equal(1, uvCache[TypeLiveHighness].Level)

	uv, err = UserVipInfos(testLiveNobleUserID, true, nil) // 仅有普通贵族身份
	require.NoError(err)
	require.NotNil(uv)
	require.NotNil(uv[TypeLiveNoble])
	require.Nil(uv[TypeLiveHighness])
	assert.Equal(TypeLiveNoble, uv[TypeLiveNoble].Type)
	assert.Equal(1, uv[TypeLiveNoble].Level)
	assert.Equal("练习生", uv[TypeLiveNoble].Title)
	uvCache, err = UserVipInfos(testLiveNobleUserID, false, nil) // 第二遍从缓存获取
	require.NoError(err)
	require.NotNil(uvCache[TypeLiveNoble])
	require.Nil(uvCache[TypeLiveHighness])
	assert.Equal(TypeLiveNoble, uvCache[TypeLiveNoble].Type)
	assert.Equal("练习生", uvCache[TypeLiveNoble].Title)

	uv, err = UserVipInfos(testHighnessUserID, true, nil) // 仅有上神贵族身份
	require.NoError(err)
	require.NotNil(uv)
	require.Nil(uv[TypeLiveNoble])
	require.NotNil(uv[TypeLiveHighness])
	assert.Equal(TypeLiveHighness, uv[TypeLiveHighness].Type)
	assert.Equal(1, uv[TypeLiveHighness].Level)
	assert.Equal("上神", uv[TypeLiveHighness].Title)
	uvCache, err = UserVipInfos(testHighnessUserID, false, nil) // 第二遍从缓存获取
	require.NoError(err)
	require.Nil(uvCache[TypeLiveNoble])
	assert.Equal(TypeLiveHighness, uvCache[TypeLiveHighness].Type)
	assert.Equal("上神", uvCache[TypeLiveHighness].Title)

	uv, err = UserVipInfos(testNoVipUserID, true, nil) // 没有贵族身份
	require.NoError(err)
	require.Nil(uv)
	require.Nil(uv[TypeLiveNoble])
	require.Nil(uv[TypeLiveHighness])
	uvCache, err = UserVipInfos(testNoVipUserID, false, nil) // 第二遍从缓存获取
	require.NoError(err)
	require.Nil(uvCache)
	require.Nil(uv[TypeLiveNoble])
	require.Nil(uv[TypeLiveHighness])
}

func TestSaveUVsToCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	userID := int64(123456)
	saveUVsToCache(userID, nil)
	keyM := keys.KeyNobleUserVips1.Format(userID)
	keyR := keys.KeyNobleUserVipLevel1.Format(userID)
	val, err := service.Redis.Get(keyM).Result()
	require.NoError(err)
	assert.Equal("null", val)
	b, err := service.Redis.Get(keys.KeyNobleUserVipLevel1.Format(userID)).Bytes()
	require.NoError(err)
	var levelInfos map[int]int
	require.NoError(json.Unmarshal(b, &levelInfos))
	assert.Empty(levelInfos)

	normalNoble := &UserVip{UserID: userID, Type: 1, Level: 7, ExpireTime: now.Unix() + 1000}
	highness := &UserVip{UserID: userID, Type: 2, Level: 1, ExpireTime: now.Unix() + 1000}
	uvs := map[int]*UserVip{TypeLiveNoble: normalNoble, TypeLiveHighness: highness}
	saveUVsToCache(userID, uvs)
	val, err = service.Redis.Get(keyM).Result()
	require.NoError(err)
	var uvsCache map[int]*UserVip
	require.NoError(json.Unmarshal([]byte(val), &uvsCache))
	assert.Equal(uvsCache, uvs)
	b, err = service.Redis.Get(keyR).Bytes()
	require.NoError(err)
	require.NoError(json.Unmarshal(b, &levelInfos))
	assert.Equal(highness.Level, levelInfos[TypeLiveHighness])
}

func TestUserLevelList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyNobleUserVipLevel1.Format(0) // 缓存过期可能导致测试失效
	require.NoError(service.Redis.Del(key).Err())
	cleanup := mrpc.SetMock(urlUserLevel, func(input interface{}) (output interface{}, err error) {
		return []UserVipLevel{}, nil
	})
	defer cleanup()
	r, err := userLevelList([]int64{0, 0, 0}, nil, nil)
	require.NoError(err)
	assert.Empty(r)

	r, err = userLevelList(nil, nil, nil) // 空数组不会报错，但是有日志记录
	require.NoError(err)
	assert.Empty(r)

	info := Info{
		Type:  1,
		Level: 5,
	}
	found := map[int64]*UserInfo{UserIDHasNoble: {LiveNoble: &info}}
	r, err = userLevelList([]int64{UserIDHasNoble}, found, nil)
	require.NoError(err)
	require.Len(r, 1)
	assert.Equal(r[0].Level, 5)

	testUserID := int64(999999)
	cleanup = mrpc.SetMock(urlUserLevel, func(input interface{}) (output interface{}, err error) {
		res := []UserVipLevel{
			{
				UserID: testUserID,
				Type:   1,
				Level:  1,
			},
		}
		return res, nil
	})
	defer cleanup()
	r, err = userLevelList([]int64{testUserID, UserIDNoNoble}, nil, nil)
	require.NoError(err)
	require.Len(r, 1)
	assert.Equal(testUserID, r[0].UserID)
	assert.Equal(1, r[0].Level)
}

func TestSaveLevelsToCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	d := []UserVipLevel{
		{
			UserID:     1,
			Type:       1,
			Level:      1,
			ExpireTime: 2,
		},
		{
			UserID:     2,
			Type:       2,
			Level:      1,
			ExpireTime: 2,
		},
		{
			UserID:     1,
			Type:       2,
			Level:      3,
			ExpireTime: 1,
		},
	}
	saveLevelsToCache([]int64{1, 2, 3}, d)
	key := keys.KeyNobleUserVipLevel1.Format(1)
	res, err := service.Redis.Get(key).Result()
	require.NoError(err)
	require.NotEmpty(res)
	var levelInfo map[int]int
	require.NoError(json.Unmarshal([]byte(res), &levelInfo))
	assert.Equal(1, levelInfo[1])
	assert.Equal(3, levelInfo[2])

	// 没有贵族信息
	key = keys.KeyNobleUserVipLevel1.Format(3)
	res, err = service.Redis.Get(key).Result()
	require.NoError(err)
	require.NotEmpty(res)
	levelInfo = make(map[int]int)
	require.NoError(json.Unmarshal([]byte(res), &levelInfo))
	assert.Empty(levelInfo)
}

func TestGetUserByTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URLGetUserByTime, func(input interface{}) (output interface{}, err error) {
		body := input.(map[string]interface{})
		if body["type"] != TypeLiveNoble {
			return nil, fmt.Errorf("invalid vip_type: %v", body["vip_type"])
		}
		if body["start_time"] == int64(9999999998) && body["end_time"] == int64(9999999999) {
			return nil, nil
		}
		resp := []UserVip{
			{UserID: 9074509},
		}
		return resp, nil
	})
	defer cancel()

	// 查询为空的情况
	r, err := GetUserByTime(TypeLiveNoble, 9999999998, 9999999999)
	require.NoError(err)
	assert.Empty(r)

	// 查询有数据的情况
	r, err = GetUserByTime(TypeLiveNoble, 2, 3)
	require.NoError(err)
	assert.Equal(1, len(r))
}

func TestHavePrivilege(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var uv *UserVip
	var err error
	assert.False(HavePrivilege(uv, PrivilegeBubble))
	uv = new(UserVip)
	assert.False(HavePrivilege(uv, PrivilegeInvisible))
	uv.ExpireTime = goutil.TimeNow().Unix() + 1000
	assert.False(HavePrivilege(uv, PrivilegeRankInvisible))
	uv.Info, err = FindByTypeAndLevel(TypeLiveNoble, 2)
	require.NoError(err)
	assert.False(HavePrivilege(uv, PrivilegeInvisible))
	uv.Info, err = FindByTypeAndLevel(TypeLiveNoble, 7)
	require.NoError(err)
	assert.True(HavePrivilege(uv, PrivilegeInvisible))
}

func TestMaxMedalNum(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(int64(30), MaxMedalLimitNum(nil))
}

func TestNewUserNoble(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(URLVipConfig, func(input any) (output any, err error) {
		return &ConfigResp{
			LiveNobleProtectDays: 5,
		}, nil
	})
	defer cancel()

	nuv := NewUserNoble(nil)
	assert.Equal(NobleStatusNone, nuv.Status)
	assert.Equal("开通贵族优先上榜哦~", nuv.Tip)

	goutil.SetTimeNow(func() time.Time {
		// 2021-11-29 23:59:59
		return time.Unix(1638201599, 0)
	})
	defer goutil.SetTimeNow(nil)
	expireTime := goutil.TimeNow().Add(24 * 7 * time.Hour)
	uv := &UserVip{
		Title:      "练习生",
		Type:       TypeLiveNoble,
		ExpireTime: expireTime.Unix(),
	}
	nuv = NewUserNoble(uv)
	assert.Equal(NobleStatusNoble, nuv.Status)
	assert.Equal(fmt.Sprintf("练习生到期：%s", FormatTipEndTime(uv.Type, uv.ExpireTime)), nuv.Tip)
	assert.Zero(nuv.RemainDays)

	uv.ExpireTime = goutil.TimeNow().Add(24 * time.Hour).Unix()
	nuv = NewUserNoble(uv)
	assert.Equal(NobleStatusNoble, nuv.Status)
	assert.Equal("练习生到期：2021-12-01 00:00", nuv.Tip)
	assert.EqualValues(1, nuv.RemainDays)

	uv.ExpireTime = goutil.TimeNow().Add(-24 * time.Hour).Unix()
	nuv = NewUserNoble(uv)
	assert.Equal(NobleStatusProtected, nuv.Status)
	assert.Equal("练习生续费保护结束：2021-12-04 00:00", nuv.Tip)
	assert.EqualValues(4, nuv.RemainDays)

	uv.ExpireTime = goutil.TimeNow().Add(-24 * 7 * time.Hour).Unix()
	nuv = NewUserNoble(uv)
	assert.Equal(NobleStatusNone, nuv.Status)
	assert.Equal("开通贵族优先上榜哦~", nuv.Tip)

	uv.Type = TypeLiveHighness
	uv.Title = "上神"
	uv.ExpireTime = goutil.TimeNow().Add(24 * time.Hour).Unix()
	nuv = NewUserNoble(uv)
	assert.Equal(NobleStatusNoble, nuv.Status)
	assert.Equal("上神到期：2021-12-01 00:00", nuv.Tip)
	assert.EqualValues(1, nuv.RemainDays)

	uv.ExpireTime = goutil.TimeNow().Add(-24 * time.Hour).Unix()
	nuv = NewUserNoble(uv)
	assert.Equal(NobleStatusProtected, nuv.Status)
	assert.Equal("上神续费保护结束：2021-12-04 00:00", nuv.Tip)
	assert.EqualValues(4, nuv.RemainDays)
}

func TestBuildTip(t *testing.T) {
	assert := assert.New(t)

	expireTime := time.Date(2024, 5, 17, 10, 20, 59, 0, time.Local)

	uv := &UserVip{
		Title: "练习生",
		Type:  TypeLiveNoble,
	}
	tip := BuildTip(uv.Type, uv.Title, expireTime.Unix(), false)
	assert.Equal("练习生到期：2024-05-18 00:00", tip)

	uv = &UserVip{
		Title: "练习生",
		Type:  TypeLiveNoble,
	}
	tip = BuildTip(uv.Type, uv.Title, expireTime.Unix(), true)
	assert.Equal("练习生续费保护结束：2024-05-18 00:00", tip)

	uv = &UserVip{
		Title: "练习生",
		Type:  TypeLiveTrialNoble,
	}
	tip = BuildTip(uv.Type, uv.Title, expireTime.Unix(), true)
	assert.Equal("练习生体验到期：2024-05-17 10:20", tip)

	uv = &UserVip{
		Title: "练习生",
		Type:  TypeLiveTrialNoble,
	}
	tip = BuildTip(uv.Type, uv.Title, expireTime.Unix(), false)
	assert.Equal("练习生体验到期：2024-05-17 10:20", tip)
}

func TestDayDuration(t *testing.T) {
	assert := assert.New(t)

	assert.Panics(func() {
		DayDuration(2, 3)
	})

	assert.NotPanics(func() {
		duration := DayDuration(1621256888, 1620824889)
		assert.Equal(int64(5), duration)
	})
}

func TestUserVipBeforeRenewDeadline(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(100, 0)
	})
	defer goutil.SetTimeNow(nil)

	uv := UserVip{
		ExpireTime: 1000,
	}
	assert.False(uv.BeforeRenewDeadline())
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(2000, 0)
	})
	assert.True(uv.BeforeRenewDeadline())
	goutil.SetTimeNow(nil)
	assert.False(uv.BeforeRenewDeadline())
}

func TestLiveUserLevelByUserIDs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	cancel := mrpc.SetMock(URLLiveUserLevel, func(input interface{}) (output interface{}, err error) {
		resp := LiveUsersLevelResp{
			Data: []*UserVip{
				{
					UserID: 9074509,
					Type:   TypeLiveHighness,
					Level:  1,
				},
				{
					UserID: 3457114,
					Type:   TypeLiveNoble,
					Level:  NobleLevel7,
				},
			},
		}
		return resp, nil
	})
	defer cancel()

	resp, err := LiveUserLevelByUserIDs([]int64{9074509, 3457114})
	require.NoError(err)
	require.Len(resp, 2)
	assert.Equal(TypeLiveHighness, resp[0].Type)
	assert.NotEmpty(resp[0].Info)
	assert.Equal(TypeLiveNoble, resp[1].Type)
	assert.NotEmpty(resp[1].Info)
}

func TestBuyLiveHighness(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(urlBuyLiveHighness, func(input interface{}) (output interface{}, err error) {
		var uv struct {
			VipInfo UserVip `json:"vip_info"`
		}
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userID, ok := body["user_id"].(int64)
		require.True(ok)
		vipID, ok := body["vip_id"].(int64)
		require.True(ok)
		require.Equal(HighnessVipID, vipID)
		_, ok = body["is_registration"].(bool)
		require.True(ok)

		uv.VipInfo = UserVip{
			UserID:     userID,
			VipID:      vipID,
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		}

		return uv, nil
	})
	defer cancel()

	uv, err := BuyLiveHighness(12, true)
	require.NoError(err)
	assert.NotNil(uv)
}

func TestBuyLiveTrialNoble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(URLBuyLiveTrialNoble, func(input interface{}) (output interface{}, err error) {
		param, ok := input.(*BuyTrialNobleRequestParam)
		require.True(ok)
		assert.EqualValues(9, param.VipID)
		return handler.M{"vip_info": &UserVip{UserID: 1}}, nil
	})
	defer cancel()

	uv, err := BuyLiveTrialNoble(mrpc.NewUserContextFromEnv(), &BuyTrialNobleRequestParam{VipID: 9})
	require.NoError(err)
	require.NotNil(uv)
	assert.EqualValues(1, uv.UserID)
}

func TestSetExpire(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserIDs := []int64{9074501, 9074502}
	testVipTypes := []int{TypeLiveNoble, TypeLiveHighness}
	now := goutil.TimeNow().Unix()
	cancel := mrpc.SetMock(URLVipSetExpire, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userIDs, ok := body["user_ids"].([]int64)
		require.True(ok)
		require.Equal(testUserIDs, userIDs)
		vipTypes, ok := body["vip_types"].([]int)
		require.True(ok)
		require.Equal(testVipTypes, vipTypes)
		protection, ok := body["protection"].(bool)
		require.True(ok)
		require.True(protection)
		return SetExpireResp{
			UserIDs:    testUserIDs,
			ExpireTime: now,
		}, nil
	})
	defer cancel()
	resp, err := SetExpire(testUserIDs, testVipTypes, true)
	require.NoError(err)
	require.NotEmpty(resp)
	assert.Equal(testUserIDs, resp.UserIDs)
	assert.Equal(now, resp.ExpireTime)
}

func TestGetUserExpireBalanceByTime(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock(URLGetExpireBalanceByTime, func(input interface{}) (output interface{}, err error) {
		return []UserExpireBalance{
			{
				UserID:       12,
				TotalBalance: 2000,
			},
			{
				UserID:       13,
				TotalBalance: 2001,
			},
		}, nil
	})
	defer cleanup()

	ueb, err := GetUserExpireBalanceByTime(1, 1, TypeExpireBalanceExpired)
	require.NoError(err)
	require.Len(ueb, 2)
}

func TestGetVipConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.LocalKeyVipConfig0.Format()
	service.Cache5Min.Delete(key)

	expected := &ConfigResp{
		EnableNewVipBalanceTime: 1701187200,
		LiveNobleProtectDays:    10,
	}
	cancel := mrpc.SetMock(URLVipConfig, func(input any) (output any, err error) {
		return expected, nil
	})
	defer cancel()

	// 测试从接口获取数据并写入缓存
	config, err := GetVipConfig(false)
	require.NoError(err)
	assert.Equal(expected, config)
	_, ok := service.Cache5Min.Get(key)
	assert.True(ok)

	// 测试从缓存获取数据
	config, err = GetVipConfig(true)
	require.NoError(err)
	assert.Equal(expected, config)
}

func TestNewUserVipInfo(t *testing.T) {
	assert := assert.New(t)

	uvs := map[int]*UserVip{
		TypeLiveNoble: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(10 * time.Second).Unix(),
			Info:       &Info{},
		},
		TypeLiveHighness: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(10 * time.Second).Unix(),
			Info:       &Info{},
		},
		TypeLiveTrialNoble: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(10 * time.Second).Unix(),
			Info:       &Info{},
		},
	}
	info := NewUserVipInfo(uvs)
	assert.NotNil(info.LiveNoble)
	assert.NotNil(info.LiveHighness)
	assert.NotNil(info.LiveTrialNoble)

	uvs = map[int]*UserVip{
		TypeLiveNoble: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(10 * time.Second).Unix(),
			Info:       &Info{},
		},
		TypeLiveHighness: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(10 * time.Second).Unix(),
			Info:       &Info{},
		},
		TypeLiveTrialNoble: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(-10 * time.Second).Unix(),
			Info:       &Info{},
		},
	}
	info = NewUserVipInfo(uvs)
	assert.NotNil(info.LiveNoble)
	assert.NotNil(info.LiveHighness)
	assert.Nil(info.LiveTrialNoble)

	uvs = map[int]*UserVip{
		TypeLiveNoble: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(-10 * time.Second).Unix(),
			Info:       &Info{},
		},
		TypeLiveHighness: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(-10 * time.Second).Unix(),
			Info:       &Info{},
		},
		TypeLiveTrialNoble: {
			UserID:     2,
			ExpireTime: goutil.TimeNow().Add(-10 * time.Second).Unix(),
			Info:       &Info{},
		},
	}
	info = NewUserVipInfo(uvs)
	assert.Nil(info.LiveNoble)
	assert.Nil(info.LiveHighness)
	assert.Nil(info.LiveTrialNoble)
}
