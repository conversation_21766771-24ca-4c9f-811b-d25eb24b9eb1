//go:build !release
// +build !release

package vip

import "github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"

// MockVipList mock vip list api
func MockVipList() func() {
	cancel := mrpc.SetMock(URLVipList, func(i interface{}) (interface{}, error) {
		input, ok := i.(map[string]int)
		if !ok {
			return map[string]interface{}{
				"Datas": []*priceInfo{},
			}, nil
		}
		var resp interface{}
		switch input["type"] {
		case TypeLiveNoble:
			resp = map[string]interface{}{
				"Datas": []*priceInfo{
					{ID: 1, Level: 1, Title: "练习生"},
					{ID: 2, Level: 2, Title: "新秀"},
					{ID: 3, Level: 3, Title: "偶像"},
					{ID: 4, Level: 4, Title: "大咖"},
					{ID: 5, Level: 5, Title: "巨星"},
					{ID: 6, Level: 6, Title: "传奇"},
					{ID: 7, Level: 7, Title: "神话"},
				},
			}
		case TypeLiveHighness:
			resp = map[string]interface{}{
				"Datas": []*priceInfo{
					{ID: 8, Level: 1, Title: "上神"},
				},
			}
		}
		return resp, nil
	})
	return cancel
}
