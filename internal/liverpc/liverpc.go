package liverpc

import (
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
)

// rpc 地址
const (
	URILiveRoomUserInfo = "live://room/userinfo"
	URILiveRoomUsers    = "live://room/users"
	URILiveRoomInfo     = "live://room/info"
)

// RoomSimple room info
type RoomSimple struct {
	RoomID            int64           `json:"room_id"`
	CatalogID         int64           `json:"catalog_id"`
	Name              string          `json:"name"`
	Announcement      string          `json:"announcement"`
	GuildID           int64           `json:"guild_id,omitempty"`
	CreatorID         int64           `json:"creator_id"`
	CreatorUsername   string          `json:"creator_username"`
	CreatorIconURL    string          `json:"creator_iconurl"`
	Statistics        room.Statistics `json:"statistics"`
	Status            room.Status     `json:"status"`
	CoverURL          string          `json:"cover_url"`
	ActivityCatalogID int64           `json:"activity_catalog_id,omitempty"`
	Limit             *room.Limit     `json:"limit,omitempty"`
	Config            *room.Config    `json:"config,omitempty"`
}

// RoomInfoResp 直播间信息
type RoomInfoResp struct {
	Room *RoomSimple `json:"room"`
}

// RoomInfo 获取直播间信息
func RoomInfo(roomID, userID int64) (*RoomInfoResp, error) {
	resp := new(RoomInfoResp)
	err := service.MRPC.Call(URILiveRoomInfo, "", map[string]interface{}{
		"room_id": roomID,
		"user_id": userID,
	}, resp)
	return resp, err
}

// RoomUserInfoResp 用户信息
type RoomUserInfoResp struct {
	User              *liveuser.Simple     `json:"user"`
	RoomMedal         *livemedal.LiveMedal `json:"room_medal,omitempty"` // 用户对应当前直播间内的勋章
	HasInvisibleRoles bool                 `json:"has_invisible_roles"`  // 是否用户进场隐身角色
	IsValuableUser    bool                 `json:"is_valuable_user"`     // 是否拥有统计热度和贵宾榜资格
	IsInvisible       bool                 `json:"is_invisible"`         // 是否进场隐身
}

// RoomUserInfo 获取直播间用户信息
func RoomUserInfo(userID, roomID int64) (*RoomUserInfoResp, error) {
	resp := new(RoomUserInfoResp)
	err := service.MRPC.Call(URILiveRoomUserInfo, "", map[string]interface{}{
		"user_id": userID,
		"room_id": roomID,
	}, resp)
	return resp, err
}

// RoomUsers 获取直播间批量用户信息
func RoomUsers(userIDs []int64, roomID int64) ([]*liveuser.Simple, error) {
	resp := make([]*liveuser.Simple, 0, len(userIDs))
	err := service.MRPC.Call(URILiveRoomUsers, "", map[string]interface{}{
		"user_ids": userIDs,
		"room_id":  roomID,
	}, &resp)
	return resp, err
}
