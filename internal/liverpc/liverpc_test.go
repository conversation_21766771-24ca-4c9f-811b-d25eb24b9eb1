package liverpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(RoomInfoResp{}, "room")
	kc.Check(RoomUserInfoResp{}, "user", "room_medal", "has_invisible_roles",
		"is_valuable_user", "is_invisible")
}

func TestRoomInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(1234)
	cancel := mrpc.SetMock(URILiveRoomInfo, func(input interface{}) (interface{}, error) {
		if input.(map[string]interface{})["room_id"].(int64) != testRoomID {
			return nil, &mrpc.ClientError{
				Code: actionerrors.ErrCannotFindRoom.ErrorCode(),
			}
		}
		return handler.M{"room": room.Room{
			Helper: room.Helper{RoomID: testRoomID},
		}}, nil
	})
	defer cancel()

	resp, err := RoomInfo(testRoomID, 1)
	require.NoError(err)
	require.NotNil(resp.Room)
	assert.Equal(testRoomID, resp.Room.RoomID)

	_, err = RoomInfo(testRoomID+1, 0)
	require.Error(err)
}

func TestRoomUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1234)
	cancel := mrpc.SetMock(URILiveRoomUserInfo, func(input interface{}) (interface{}, error) {
		if input.(map[string]any)["user_id"].(int64) != testUserID {
			return nil, &mrpc.ClientError{
				Code: actionerrors.ErrCannotFindUser.ErrorCode(),
			}
		}
		return RoomUserInfoResp{
			User: &liveuser.Simple{UID: testUserID},
		}, nil
	})
	defer cancel()

	resp, err := RoomUserInfo(testUserID, 0)
	require.NoError(err)
	require.NotNil(resp.User)
	assert.Equal(testUserID, resp.User.UID)

	_, err = RoomUserInfo(testUserID+1, 0)
	require.Error(err)
}

func TestRoomUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1234)
	cancel := mrpc.SetMock(URILiveRoomUsers, func(input interface{}) (interface{}, error) {
		return []*liveuser.Simple{
			{UID: testUserID},
			{UID: testUserID + 1},
		}, nil
	})
	defer cancel()

	resp, err := RoomUsers([]int64{1, 2, 3, testUserID, testUserID + 1}, 0)
	require.NoError(err)
	assert.Len(resp, 2)
}
