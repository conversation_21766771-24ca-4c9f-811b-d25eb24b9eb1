package prize

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
)

func TestCreatorBackpackSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testCreatorID = int64(223231)
		testGiftID    = int64(30001)
	)

	// 清理测试主播的礼物记录
	_, err := creatoritems.Collection().DeleteMany(
		context.Background(),
		bson.M{
			"creator_id": testCreatorID,
			"gift_id":    testGiftID,
		},
	)
	require.NoError(err)

	// 清理奖品记录
	err = liveprize.DB().Delete(&liveprize.PrizeLog{}).Error
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.Prize{}).Error
	require.NoError(err)

	// 测试发送
	testPrize1 := &liveprize.Prize{
		ID:         5,
		Type:       liveprize.TypeCreatorBackpack,
		ElementID:  testGiftID,
		Num:        5,
		ExpireTime: 10000000000,
		MoreInfo: &liveprize.MoreInfo{
			CreateCard: &liveprize.CreateCard{
				URL: "oss://live/labelicon/livelist/liveshow-1.png",
			},
		},
	}
	log, err := newCreatorBackpackSender(NewUserDistributor(testCreatorID, testPrize1, WithBiz(1, liveprize.TypeCreatorBackpack))).send()
	require.NoError(err)
	require.NotNil(log)

	// 验证奖品日志记录
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testCreatorID, pl.UserID)
	assert.Equal(testPrize1.ID, pl.PrizeID)
	assert.Equal(log.Biz, pl.Biz)

	// 验证主播物品已添加
	items, err := creatoritems.Find(
		bson.M{
			"creator_id": testCreatorID,
			"gift_id":    testGiftID,
			"num":        bson.M{"$gt": 0},
		},
		options.Find(),
	)
	require.NoError(err)
	assert.GreaterOrEqual(len(items), 1)
}
