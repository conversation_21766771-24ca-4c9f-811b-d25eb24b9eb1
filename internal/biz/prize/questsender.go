package prize

import (
	"errors"
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/models/reward"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type questSender struct {
	*liveprize.Prize
	roomID int64
	opt    *option
}

func newQuestSender(distributor Distributor) *questSender {
	return &questSender{
		Prize:  distributor.Prize(),
		roomID: distributor.ReceiverID(),
		opt:    distributor.Option(),
	}
}

func (sender *questSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		RoomID:  sender.roomID,
	}

	return log.Logging(func() (err error) {
		startTime, endTime, err := sender.GetTime()
		if err != nil {
			return err
		}

		now := goutil.TimeNow().Unix()
		if startTime < now || endTime < now {
			return errors.New("开始 / 结束时间不能早于当前时间，请检查后重试")
		}
		if !goutil.HasElem([]int{quests.QuestTypeRoomView, quests.QuestTypeRoomListenDuration}, sender.ElementType) {
			return errors.New("进房有奖任务仅支持「访问直播间」和「直播间连续收听时长」任务类型")
		}
		if sender.MoreInfo == nil || sender.MoreInfo.Quest == nil || sender.MoreInfo.Quest.Intro == "" || sender.MoreInfo.Quest.RewardID == 0 {
			return fmt.Errorf("prize 配置错误 %d", sender.ID)
		}
		if sender.ElementType == quests.QuestTypeRoomListenDuration && sender.MoreInfo.Quest.Duration <= 0 {
			return errors.New("直播间连续收听任务，目标连续收听时长不能为 0")
		}

		// 检查 reward 是否合法
		r, err := reward.FindRewardByRewardIDWithCache(sender.MoreInfo.Quest.RewardID)
		if err != nil {
			return err
		}
		if r == nil {
			return errors.New("奖励 ID 不存在，请检查后重试")
		}
		// TODO: 进房有奖只支持有持续时间的背包礼物奖励，后续支持其他类型奖励
		if r.Type != reward.TypeBackpack {
			return errors.New("只支持背包礼物奖励")
		}
		if r.Duration == 0 {
			return errors.New("只支持有持续时间的奖励")
		}

		g, err := gift.FindShowingGiftByGiftID(r.ElementID)
		if err != nil {
			return err
		}
		if g == nil {
			return fmt.Errorf("礼物 %d 不存在或已下架", r.ElementID)
		}

		quest := quests.Quest{
			CreateTime:   now,
			ModifiedTime: now,
			Type:         sender.ElementType,
			StartTime:    startTime,
			EndTime:      endTime,
			Intro:        sender.MoreInfo.Quest.Intro,
			RewardID:     sender.MoreInfo.Quest.RewardID,
			Daily:        sender.MoreInfo.Quest.Daily,
			Duration:     sender.MoreInfo.Quest.Duration,
			RoomID:       sender.roomID,
		}

		return quests.InsertQuests([]any{quest})
	})
}
