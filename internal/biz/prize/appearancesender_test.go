package prize

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestAppearanceSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		now        = goutil.TimeNow()
		testUserID = int64(223231)
	)
	appearances, err := appearance.Find(
		bson.M{
			"start_time":  bson.M{"$lte": now.Unix()},
			"expire_time": bson.M{"$not": bson.M{"$lte": now.Unix()}},
		},
		&options.FindOptions{Limit: goutil.NewInt64(1)},
	)
	require.NoError(err)
	require.Len(appearances, 1)
	appearanceIDs := goutil.SliceMap(appearances, func(a *appearance.Appearance) int64 {
		return a.ID
	})
	_, err = userappearance.Collection().DeleteMany(
		context.Background(),
		bson.M{
			"user_id":       testUserID,
			"appearance_id": bson.M{"$in": appearanceIDs},
		},
	)
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.PrizeLog{}).Error
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.Prize{}).Error
	require.NoError(err)
	prize := &liveprize.Prize{
		ID:          1,
		Type:        liveprize.TypeUserAppearance,
		ElementID:   appearances[0].ID,
		ElementType: appearances[0].Type,
		Num:         1,
		Duration:    int64(10 * time.Hour.Seconds()),
	}

	log, err := newAppearanceSender(NewUserDistributor(testUserID, prize, WithBiz(1, liveprize.TypeUserAppearance))).send()
	require.NoError(err)
	require.NotNil(log)
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	assert.NotNil(pl)
}
