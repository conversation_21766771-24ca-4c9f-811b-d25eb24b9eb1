package prize

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
)

type creatorBackpackSender struct {
	*liveprize.Prize
	creatorID int64
	opt       *option
}

func newCreatorBackpackSender(distributor Distributor) *creatorBackpackSender {
	return &creatorBackpackSender{
		Prize:     distributor.Prize(),
		creatorID: distributor.ReceiverID(),
		opt:       distributor.Option(),
	}
}

func (sender *creatorBackpackSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		UserID:  sender.creatorID,
	}

	return log.Logging(func() (err error) {
		startTime, endTime, err := sender.GetTime()
		if err != nil {
			return err
		}

		g, err := gift.FindShowingGiftByGiftID(sender.ElementID)
		if err != nil {
			return err
		}
		if g == nil {
			return fmt.Errorf("礼物不存在 %d", sender.ElementID)
		}
		if !creatoritems.IsBackpackGift(g) {
			return fmt.Errorf("礼物 %d 不是主播背包礼物", sender.ElementID)
		}

		return creatoritems.AddGiftToCreators([]int64{sender.creatorID}, g, int64(sender.Num), startTime, endTime)
	})
}
