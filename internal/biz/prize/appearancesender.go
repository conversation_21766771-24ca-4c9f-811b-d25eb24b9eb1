package prize

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
)

type appearanceSender struct {
	*liveprize.Prize
	userID int64
	opt    *option
}

func newAppearanceSender(distributor Distributor) *appearanceSender {
	return &appearanceSender{
		Prize:  distributor.Prize(),
		userID: distributor.ReceiverID(),
		opt:    distributor.Option(),
	}
}

func (sender *appearanceSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		UserID:  sender.userID,
	}
	return log.Logging(func() (err error) {
		aItem, err := appearance.FindOne(sender.ElementID, sender.ElementType)
		if err != nil {
			return err
		}
		if aItem == nil {
			return fmt.Errorf("can not find appearance element_id: %d element_type: %d", sender.ElementID, sender.ElementType)
		}
		err = userappearance.AddAppearance(sender.userID, sender.Duration, sender.ExpireTime, aItem)
		if err != nil {
			return err
		}
		return nil
	})
}
