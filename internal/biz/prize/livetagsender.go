package prize

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
)

type liveTagSender struct {
	*liveprize.Prize
	roomID int64
	opt    *option
}

func newLiveTagSender(distributor Distributor) *liveTagSender {
	return &liveTagSender{
		Prize:  distributor.Prize(),
		roomID: distributor.ReceiverID(),
		opt:    distributor.Option(),
	}
}

func (sender *liveTagSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		RoomID:  sender.roomID,
	}

	return log.Logging(func() (err error) {
		startTime, endTime, err := sender.GetTime()
		if err != nil {
			return err
		}
		if sender.MoreInfo == nil || sender.MoreInfo.CreateCard == nil || sender.MoreInfo.CreateCard.URL == "" {
			return fmt.Errorf("prize 配置错误 %d", sender.ID)
		}
		return liverecommendedelements.AddLiveIcon(sender.roomID, startTime, endTime, sender.MoreInfo.CreateCard.URL)
	})
}
