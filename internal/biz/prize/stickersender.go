package prize

import (
	"errors"
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type stickerSender struct {
	*liveprize.Prize
	roomID int64
	userID int64
	opt    *option
}

// newStickerSender 创建发送表情的 sender
func newStickerSender(distributor Distributor) *stickerSender {
	s := &stickerSender{
		Prize: distributor.Prize(),
		opt:   distributor.Option(),
	}
	if distributor.ReceiverType() == receiverTypeUser {
		s.userID = distributor.ReceiverID()
	} else if distributor.ReceiverType() == receiverTypeRoom {
		s.roomID = distributor.ReceiverID()
	} else {
		panic("无效的 receiver type")
	}

	return s
}

func (sender *stickerSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		UserID:  sender.userID,
		RoomID:  sender.roomID,
	}

	return log.Logging(func() (err error) {
		if sender.ElementType != livesticker.TypeRoom && sender.ElementType != livesticker.TypeUser {
			return errors.New("invalid sticker type")
		}

		sticker, err := livesticker.FindSticker(sender.ElementID)
		if err != nil {
			return err
		}
		if sticker == nil {
			return fmt.Errorf("can not find sticker %d", sender.ElementID)
		}
		now := goutil.TimeNow()
		owner, err := livesticker.FindPackageOwner(sender.ElementType, sender.roomID, sender.userID, now)
		if err != nil {
			return err
		}
		// NOTICE: 这里仅支持用户和直播专属表情。若无对应专属表情包，则会创建一个表情包并分配给用户或直播间；若存在对应表情包，则直接使用。
		var pkg *livesticker.Package
		if owner == nil {
			pkg, err = livesticker.AssignExclusivePackage(sender.ElementType, sender.roomID, sender.userID)
			if err != nil {
				return err
			}
		} else {
			pkg, err = livesticker.FindPackage(owner.PackageID)
			if err != nil {
				return err
			}
			if pkg == nil {
				logger.WithFields(logger.Fields{
					"prize_id":   sender.ID,
					"package_id": owner.PackageID,
					"user_id":    sender.userID,
					"room_id":    sender.roomID,
				}).Error("can not find sticker package")
				// PASS
				return nil
			}
		}
		m, err := pkg.StickerMap(sticker.ID, now)
		if err != nil {
			return err
		}
		if m != nil {
			// 若表情过期时间小于奖励最大有效时间，则给该表情续期
			// 若奖励的表情为永久，但已有的表情有过期时间，则将表情设置为永久（expire_time 为 0）
			if m.ExpireTime != 0 && (sender.ExpireTime == 0 || m.ExpireTime < sender.ExpireTime) {
				err = livesticker.DB().Model(&livesticker.PackageStickerMap{}).Where("id = ?", m.ID).Updates(
					map[string]interface{}{
						"expire_time": sender.calExpireTime(m.ExpireTime),
					},
				).Error
			}
		} else {
			err = livesticker.DB().Create(&livesticker.PackageStickerMap{
				PackageID:  pkg.ID,
				StickerID:  sticker.ID,
				StartTime:  now.Unix(),
				ExpireTime: sender.calExpireTime(now.Unix()),
			}).Error
		}
		if err != nil {
			return err
		}
		return nil
	})
}

// calExpireTime 计算发放表情的过期时间，mExpireTime 表示用户当前该表情的过期时间，如果不拥有该表情，则传入当前时间戳
func (sender *stickerSender) calExpireTime(mExpireTime int64) int64 {
	switch {
	case sender.Duration == 0 && sender.ExpireTime == 0:
		// 如果 duration 和 expire_time 都为 0，则表示发放的表情为永久有效
		return 0
	case sender.Duration == 0 && sender.ExpireTime != 0:
		// 如果 duration 为 0，取奖励表情过期时间和当前过期时间的最大值
		return max(mExpireTime, sender.ExpireTime)
	case sender.Duration != 0 && sender.ExpireTime == 0:
		// 如果 expire_time 为 0，给当前过期时间加上 duration 作为过期时间
		return mExpireTime + sender.Duration
	case sender.Duration != 0 && sender.ExpireTime != 0:
		// 如果都不为 0，取奖励表情过期时间和当前过期时间加上 duration 的最小值
		return min(mExpireTime+sender.Duration, sender.ExpireTime)
	default:
		return mExpireTime // 不应该到达这里，返回当前过期时间作为默认值
	}
}
