package prize

import (
	"time"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type roomGiftCustomSender struct {
	*liveprize.Prize
	roomID int64
	opt    *option
}

func newRoomGiftCustomSender(distributor Distributor) *roomGiftCustomSender {
	return &roomGiftCustomSender{
		Prize:  distributor.Prize(),
		roomID: distributor.ReceiverID(),
		opt:    distributor.Option(),
	}
}

func (sender *roomGiftCustomSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		RoomID:  sender.roomID,
	}

	return log.Logging(func() (err error) {
		duration := sender.Duration
		if duration <= 0 {
			logger.Errorf("prize 配置错误 %d", sender.ID)
			return nil
		}

		nowUnix := goutil.TimeNow().Unix()
		// 若奖励的结束时间小于当前时间，则不发放
		if sender.ExpireTime != 0 && sender.ExpireTime < nowUnix {
			return nil
		}
		giftID := sender.ElementID
		custom, err := livecustom.FindRoomCustomGift(sender.roomID, giftID)
		if err != nil {
			return err
		}
		var customID, durationStartTime int64
		if custom != nil {
			customID = custom.ID
			durationStartTime = custom.EndTime
		} else {
			durationStartTime = nowUnix
		}
		if sender.ExpireTime != 0 {
			remainDuration := sender.ExpireTime - durationStartTime
			if remainDuration <= 0 {
				// 已经奖励到最大时间限制
				return nil
			}
			// 若奖励的结束时间小于过期时间，则以奖励的结束时间为准
			duration = goutil.MinInt64(duration, remainDuration)
		}
		return livecustom.AssignRoomCustomGift(customID, sender.roomID, giftID,
			time.Unix(nowUnix, 0),
			time.Unix(durationStartTime+duration, 0),
			livecustom.SourceDefault)
	})
}
