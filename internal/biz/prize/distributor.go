package prize

import (
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
)

const (
	receiverTypeUser = iota + 1 // 用户
	receiverTypeRoom            // 房间
)

// ReceiverType 接收者类型
type ReceiverType int

// String 接收者类型转换为字符串
func (r ReceiverType) String() string {
	return []string{
		"user",
		"room",
	}[r-1]
}

// Distributor send prize param
type Distributor interface {
	ReceiverType() ReceiverType
	ReceiverID() int64
	Prize() *liveprize.Prize
	Option() *option
}

// baseDistributor send prize param
type baseDistributor struct {
	receiverType int
	receiverID   int64
	prize        *liveprize.Prize
	opt          *option
}

func newBaseDistributor(receiverType int, receiverID int64, prize *liveprize.Prize, opts ...Option) *baseDistributor {
	opt := new(option)
	for _, o := range opts {
		o(opt)
	}
	return &baseDistributor{
		receiverType: receiverType,
		receiverID:   receiverID,
		prize:        prize,
		opt:          opt,
	}
}

// ReceiverType return elem type
func (bd *baseDistributor) ReceiverType() ReceiverType {
	return ReceiverType(bd.receiverType)
}

// ReceiverID return receiver id
func (bd *baseDistributor) ReceiverID() int64 {
	return bd.receiverID
}

// Prize return prize
func (bd *baseDistributor) Prize() *liveprize.Prize {
	return bd.prize
}

// Option return option
func (bd *baseDistributor) Option() *option {
	return bd.opt
}

// UserDistributor send prize param
type UserDistributor struct {
	*baseDistributor
}

// NewUserDistributor create new user baseDistributor
func NewUserDistributor(userID int64, prize *liveprize.Prize, opts ...Option) *UserDistributor {
	return &UserDistributor{
		baseDistributor: newBaseDistributor(receiverTypeUser, userID, prize, opts...),
	}
}

// RoomDistributor send prize param
type RoomDistributor struct {
	*baseDistributor
}

// NewRoomDistributor create new room baseDistributor
func NewRoomDistributor(roomID int64, prize *liveprize.Prize, opts ...Option) *RoomDistributor {
	return &RoomDistributor{
		baseDistributor: newBaseDistributor(receiverTypeRoom, roomID, prize, opts...),
	}
}
