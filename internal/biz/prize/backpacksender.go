package prize

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
)

type backpackSender struct {
	*liveprize.Prize
	userID int64
	opt    *option
}

func newBackpackSender(distributor Distributor) *backpackSender {
	return &backpackSender{
		Prize:  distributor.Prize(),
		userID: distributor.ReceiverID(),
		opt:    distributor.Option(),
	}
}

func (sender *backpackSender) send() (*liveprize.PrizeLog, error) {
	log := &liveprize.PrizeLog{
		Biz:     sender.opt.biz.bizType,
		BizID:   sender.opt.biz.bizID,
		PrizeID: sender.ID,
		UserID:  sender.userID,
	}

	return log.Logging(func() (err error) {
		startTime, endTime, err := sender.GetTime()
		if err != nil {
			return err
		}

		g, err := gift.FindShowingGiftByGiftID(sender.ElementID)
		if err != nil {
			return err
		}
		if g == nil {
			return fmt.Errorf("礼物不存在 %d", sender.ElementID)
		}
		if !useritems.IsBackpackGift(g) {
			return fmt.Errorf("礼物 %d 不是背包礼物", sender.ElementID)
		}

		return useritems.AddGiftToUsers([]int64{sender.userID}, g, int64(sender.Num), useritems.SourceNormal, startTime, endTime)
	})
}
