package prize

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLiveTagSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(333444)
		testURL    = "oss://test_live_tag.png"
	)

	// 清理奖品记录
	err := liveprize.DB().Delete(&liveprize.PrizeLog{}).
		Where("room_id = ?", testRoomID).
		Error
	require.NoError(err)

	// 准备测试数据
	now := goutil.TimeNow().Unix()
	duration := int64(3600)

	testPrize := &liveprize.Prize{
		ID:         100,
		Type:       liveprize.TypeLiveTag,
		StartTime:  now,
		Duration:   duration,
		ExpireTime: 0,
		More:       []byte(`{"create_card":{"url":"` + testURL + `"}}`),
		MoreInfo: &liveprize.MoreInfo{
			CreateCard: &liveprize.CreateCard{
				URL: testURL,
			},
		},
	}

	// 模拟AddLiveIcon函数的行为
	expectedEndTime := now + duration
	model := &liverecommendedelements.LiveRecommendedElements{
		ElementID:   testRoomID,
		ElementType: liverecommendedelements.ElementLiveIcon,
		Sort:        1,
		URL:         testURL,
		StartTime:   &now,
		ExpireTime:  expectedEndTime,
	}
	err = service.DB.Create(model).Error
	require.NoError(err, "创建模拟角标数据失败")

	// 执行测试
	distributor := NewRoomDistributor(testRoomID, testPrize, WithBiz(1, liveprize.TypeLiveTag))
	sender := newLiveTagSender(distributor)

	log, err := sender.send()
	require.NoError(err)
	require.NotNil(log)

	// 验证奖品日志记录
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testRoomID, pl.RoomID)
	assert.Equal(testPrize.ID, pl.PrizeID)

	// 直接查询数据库验证记录是否创建成功
	var count int64
	err = service.DB.Table(liverecommendedelements.TableName()).
		Where("element_id = ? AND element_type = ? AND url = ? AND expire_time = ?",
			testRoomID, liverecommendedelements.ElementLiveIcon, testURL, expectedEndTime).
		Count(&count).Error
	require.NoError(err)
	assert.Greater(count, int64(0), "未找到创建的直播间角标")
}
