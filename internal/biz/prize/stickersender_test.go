package prize

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestStickerSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID  = int64(223231)
		testRoomID  = int64(444555)
		testBizType = 1
		testBizID   = "test_biz_id"
	)

	// 清理奖品记录
	err := liveprize.DB().Delete(&liveprize.PrizeLog{}).
		Where("user_id = ? OR room_id = ?", testUserID, testRoomID).Error
	require.NoError(err)

	// 创建测试表情
	testSticker := &livesticker.LiveSticker{
		Name:  "测试表情",
		Image: "test.png",
	}
	err = livesticker.DB().Create(testSticker).Error
	require.NoError(err)
	defer func() {
		livesticker.DB().Delete(testSticker)
	}()

	// 准备测试数据
	now := goutil.TimeNow()
	duration := int64(3600) // 1小时

	// 1. 测试用户表情发放
	t.Run("SendToUser", func(t *testing.T) {
		testPrize := &liveprize.Prize{
			ID:          201,
			Type:        liveprize.TypeSticker,
			ElementID:   testSticker.ID,
			ElementType: livesticker.TypeUser,
			Duration:    duration,
			ExpireTime:  0, // 无限制
		}

		// 执行测试
		distributor := NewUserDistributor(testUserID, testPrize, WithBiz(testBizType, testBizID))
		sender := newStickerSender(distributor)

		log, err := sender.send()
		require.NoError(err)
		require.NotNil(log)

		// 验证奖品日志记录
		var pl liveprize.PrizeLog
		err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
		require.NoError(err)
		assert.Equal(testUserID, pl.UserID)
		assert.Equal(int64(0), pl.RoomID, "用户表情日志中 RoomID 应为 0 ")
		assert.Equal(testPrize.ID, pl.PrizeID)
		assert.Equal(testBizType, int(pl.Biz))
		assert.Equal(testBizID, pl.BizID)

		// 验证用户表情包是否创建成功
		owner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, testUserID, now)
		require.NoError(err)
		require.NotNil(owner, "未找到创建的用户表情包拥有者")

		// 验证表情是否添加到包中
		pkg, err := livesticker.FindPackage(owner.PackageID)
		require.NoError(err)
		require.NotNil(pkg, "未找到创建的表情包")

		// 验证表情与包的关联
		m, err := pkg.StickerMap(testSticker.ID, now)
		require.NoError(err)
		require.NotNil(m, "未找到表情与包的关联")

		// 验证过期时间
		if m.ExpireTime > 0 {
			expectedExpireTime := now.Unix() + duration
			assert.InDelta(expectedExpireTime, m.ExpireTime, 10, "期望的过期时间和实际过期时间应该接近")
		}
	})

	// 2. 测试直播间表情发放
	t.Run("SendToRoom", func(t *testing.T) {
		testPrize := &liveprize.Prize{
			ID:          202,
			Type:        liveprize.TypeSticker,
			ElementID:   testSticker.ID,
			ElementType: livesticker.TypeRoom,
			Duration:    duration,
			ExpireTime:  0, // 无限制
		}

		// 执行测试
		distributor := NewRoomDistributor(testRoomID, testPrize, WithBiz(testBizType, testBizID))
		sender := newStickerSender(distributor)

		log, err := sender.send()
		require.NoError(err)
		require.NotNil(log)

		// 验证奖品日志记录
		var pl liveprize.PrizeLog
		err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
		require.NoError(err)
		assert.Equal(int64(0), pl.UserID, "直播间表情日志中 UserID 应为 0 ")
		assert.Equal(testRoomID, pl.RoomID, "直播间表情日志中 RoomID 应与传入的值一致")
		assert.Equal(testPrize.ID, pl.PrizeID)
		assert.Equal(testBizType, int(pl.Biz))
		assert.Equal(testBizID, pl.BizID)

		// 验证直播间表情包是否创建成功
		owner, err := livesticker.FindPackageOwner(livesticker.TypeRoom, testRoomID, 0, now)
		require.NoError(err)
		require.NotNil(owner, "未找到创建的直播间表情包拥有者")

		// 验证表情是否添加到包中
		pkg, err := livesticker.FindPackage(owner.PackageID)
		require.NoError(err)
		require.NotNil(pkg, "未找到创建的表情包")

		// 验证表情与包的关联
		m, err := pkg.StickerMap(testSticker.ID, now)
		require.NoError(err)
		require.NotNil(m, "未找到表情与包的关联")

		// 验证过期时间
		if m.ExpireTime > 0 {
			expectedExpireTime := now.Unix() + duration
			assert.InDelta(expectedExpireTime, m.ExpireTime, 10, "期望的过期时间和实际过期时间应该接近")
		}
	})

	// 3. 测试续期
	t.Run("RenewExpireTime", func(t *testing.T) {
		// 准备已有的表情包
		existingOwner, err := livesticker.FindPackageOwner(livesticker.TypeUser, 0, testUserID, now)
		require.NoError(err)
		require.NotNil(existingOwner, "未找到之前创建的表情包拥有者")

		existingPkg, err := livesticker.FindPackage(existingOwner.PackageID)
		require.NoError(err)
		require.NotNil(existingPkg, "未找到之前创建的表情包")

		// 获取已有的表情
		existingMap, err := existingPkg.StickerMap(testSticker.ID, now)
		require.NoError(err)
		require.NotNil(existingMap, "未找到之前创建的表情关联")

		oldExpireTime := existingMap.ExpireTime
		if oldExpireTime == 0 {
			t.Skip("当前实现中表情包是永久的，跳过续期测试")
		}

		// 用新的更长时间发放表情
		newDuration := int64(7200) // 2小时，比之前的1小时长
		testPrize := &liveprize.Prize{
			ID:          203,
			Type:        liveprize.TypeSticker,
			ElementID:   testSticker.ID,
			ElementType: livesticker.TypeUser,
			Duration:    newDuration,
			ExpireTime:  0, // 无限制
		}

		// 执行测试
		distributor := NewUserDistributor(testUserID, testPrize, WithBiz(testBizType, testBizID))
		sender := newStickerSender(distributor)

		log, err := sender.send()
		require.NoError(err)
		require.NotNil(log)

		// 重新获取表情关联，验证过期时间是否已更新
		updatedPkg, err := livesticker.FindPackage(existingOwner.PackageID)
		require.NoError(err)
		require.NotNil(updatedPkg)

		updatedMap, err := updatedPkg.StickerMap(testSticker.ID, now)
		require.NoError(err)
		require.NotNil(updatedMap)

		// 验证过期时间是否已延长
		if oldExpireTime > 0 && updatedMap.ExpireTime > 0 {
			expectedExpireTime := oldExpireTime + newDuration
			assert.InDelta(expectedExpireTime, updatedMap.ExpireTime, 10, "期望的续期后过期时间和实际过期时间应该接近")
		}
	})
}

func TestStickerSender_calExpireTime(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow().Unix()
	tests := []struct {
		name         string
		duration     int64
		expireTime   int64
		mExpireTime  int64
		expectedTime int64
	}{
		{
			name:         "设置表情永久生效",
			duration:     0,
			expireTime:   0,
			mExpireTime:  now,
			expectedTime: 0,
		},
		{
			name:         "只设置 expire_time",
			duration:     0,
			expireTime:   now + 3600,
			mExpireTime:  now,
			expectedTime: now + 3600,
		},
		{
			name:         "只设置 duration",
			duration:     3600,
			expireTime:   0,
			mExpireTime:  now,
			expectedTime: now + 3600,
		},
		{
			name:         "expire_time 和 duration 都设置，选择 duration",
			duration:     600,
			expireTime:   now + 1800,
			mExpireTime:  now,
			expectedTime: now + 600, // 选择较早的时间
		},
		{
			name:         "expire_time 和 duration 都设置，选择 expire_time",
			duration:     7200,
			expireTime:   now + 3600,
			mExpireTime:  now,
			expectedTime: now + 3600,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			sender := &stickerSender{
				Prize: &liveprize.Prize{
					Duration:   tt.duration,
					ExpireTime: tt.expireTime,
				},
			}
			assert.Equal(tt.expectedTime, sender.calExpireTime(tt.mExpireTime))
		})
	}
}
