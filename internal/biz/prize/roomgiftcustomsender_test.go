package prize

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRoomGiftCustomSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(444555)
		testGiftID = int64(12345)
	)

	// 清理奖品记录
	err := liveprize.DB().Delete(&liveprize.PrizeLog{}).
		Where("room_id = ?", testRoomID).Error
	require.NoError(err)

	// 准备测试数据
	now := goutil.TimeNow()
	duration := int64(3600)

	testPrize := &liveprize.Prize{
		ID:         200,
		Type:       liveprize.TypeRoomGiftCustom,
		ElementID:  testGiftID,
		Duration:   duration,
		ExpireTime: 0,
	}

	// 执行测试
	distributor := NewRoomDistributor(testRoomID, testPrize, WithBiz(1, liveprize.TypeRoomGiftCustom))
	sender := newRoomGiftCustomSender(distributor)

	log, err := sender.send()
	require.NoError(err)
	require.NotNil(log)

	// 验证奖品日志记录
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testRoomID, pl.RoomID)
	assert.Equal(testPrize.ID, pl.PrizeID)

	// 验证直播间定制礼物是否创建成功
	custom, err := livecustom.FindRoomCustomGift(testRoomID, testGiftID)
	require.NoError(err)
	require.NotNil(custom, "未找到创建的直播间定制礼物")

	// 验证开始时间和结束时间
	expectedStartTime := now.Unix()
	expectedEndTime := expectedStartTime + duration

	assert.Equal(expectedStartTime, custom.StartTime)
	assert.Equal(expectedEndTime, custom.EndTime)
	assert.Equal(livecustom.SourceDefault, custom.Source)
}
