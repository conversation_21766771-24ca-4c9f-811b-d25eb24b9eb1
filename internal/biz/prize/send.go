package prize

import (
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// Send prize
func Send(distributors []Distributor) ([]*liveprize.PrizeLog, error) {
	observer := &sendObserver{
		senders: make([]sender, 0, len(distributors)),
	}
	for _, distributor := range distributors {
		switch distributor.Prize().Type {
		case liveprize.TypeUserAppearance:
			observer.append(newAppearanceSender(distributor))
		case liveprize.TypeCreatorAppearance:
			observer.append(newAppearanceSender(distributor))
		case liveprize.TypeBackpack:
			observer.append(newBackpackSender(distributor))
		case liveprize.TypeCreatorBackpack:
			observer.append(newCreatorBackpackSender(distributor))
		case liveprize.TypeLiveTag:
			observer.append(newLiveTagSender(distributor))
		case liveprize.TypeSticker:
			observer.append(newStickerSender(distributor))
		case liveprize.TypeRoomGiftCustom:
			observer.append(newRoomGiftCustomSender(distributor))
		case liveprize.TypeQuest:
			observer.append(newQuestSender(distributor))
		default:
			logger.WithFields(logger.Fields{
				"receiver_type": distributor.ReceiverType().String(),
				"receiver_id":   distributor.ReceiverID(),
				"prize_id":      distributor.Prize().ID,
			}).Error("unknown prize type")
			// PASS
		}
	}
	observer.push()

	return observer.logs(), observer.error()
}
