package prize

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBackpackSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		now        = goutil.TimeNow()
		testUserID = int64(223231)
		// 使用白给礼物幻雪冰城，这是背包礼物
		testGiftID = int64(40001)
	)

	// 创建测试礼物数据
	g := &gift.Gift{
		GiftID: testGiftID,
		Name:   "幻雪冰城",
		Type:   gift.TypeRebate, // 白给礼物，可放入背包
		Price:  100,
		Order:  10,
	}
	require.True(useritems.IsBackpackGift(g), "礼物必须是背包礼物")

	// 清理测试用户的该礼物记录
	_, err := useritems.Collection().DeleteMany(
		context.Background(),
		bson.M{
			"user_id": testUserID,
			"gift_id": testGiftID,
		},
	)
	require.NoError(err)

	// 清理奖品记录
	err = liveprize.DB().Delete(&liveprize.PrizeLog{}).Error
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.Prize{}).Error
	require.NoError(err)

	// 创建测试用奖品
	prize := &liveprize.Prize{
		ID:          1,
		Type:        liveprize.TypeBackpack,
		ElementID:   testGiftID,
		ElementType: g.Type,
		Num:         5, // 发放5个礼物
		Duration:    int64(10 * time.Hour.Seconds()),
		StartTime:   now.Unix(),
		ExpireTime:  now.Add(24 * time.Hour).Unix(),
	}

	// 测试发送
	log, err := newBackpackSender(NewUserDistributor(testUserID, prize, WithBiz(1, liveprize.TypeBackpack))).send()
	require.NoError(err)
	require.NotNil(log)

	// 验证奖品日志记录
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testUserID, pl.UserID)
	assert.Equal(prize.ID, pl.PrizeID)
	assert.Equal(log.Biz, pl.Biz)

	// 验证用户物品已添加
	items, err := useritems.Find(
		bson.M{
			"user_id": testUserID,
			"gift_id": testGiftID,
			"num":     bson.M{"$gt": 0},
		},
		options.Find(),
	)
	require.NoError(err)
	assert.GreaterOrEqual(len(items), 1)

	// 验证添加的数量和过期时间
	var totalNum int64
	for _, item := range items {
		totalNum += item.Num
		// 验证礼物的有效期
		if prize.ExpireTime > 0 {
			assert.Equal(prize.ExpireTime, item.EndTime)
		} else if prize.Duration > 0 {
			// 计算预期的结束时间
			expectedEndTime := prize.StartTime + prize.Duration
			assert.Equal(expectedEndTime, item.EndTime)
		}
	}
	assert.Equal(prize.Num, totalNum)
}
