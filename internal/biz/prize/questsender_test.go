package prize

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestQuestSender_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID   = int64(444555)
		testRewardID = int64(19)
		duration     = int64(3600)
	)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 清理奖品记录
	err := liveprize.DB().Delete(&liveprize.PrizeLog{}).
		Where("room_id = ?", testRoomID).Error
	require.NoError(err)

	// 清理任务记录
	_, err = quests.CollectionQuests().DeleteMany(ctx, bson.M{
		"room_id": testRoomID,
	})
	require.NoError(err)

	// 准备测试数据
	now := goutil.TimeNow()

	testPrize := &liveprize.Prize{
		ID:          300,
		Type:        liveprize.TypeQuest,
		ElementType: quests.QuestTypeRoomListenDuration,
		Duration:    duration,

		MoreInfo: &liveprize.MoreInfo{
			Quest: &liveprize.Quest{
				Intro:    "测试任务",
				RewardID: testRewardID,
				Duration: duration,
				Daily:    false,
			},
		},
	}

	// 执行测试
	distributor := NewRoomDistributor(testRoomID, testPrize, WithBiz(1, liveprize.TypeQuest))
	sender := newQuestSender(distributor)

	log, err := sender.send()
	require.NoError(err)
	require.NotNil(log)

	// 验证奖品日志记录
	var pl liveprize.PrizeLog
	err = liveprize.DB().Where("id = ? AND status = ?", log.ID, liveprize.StatusReceived).Take(&pl).Error
	require.NoError(err)
	require.NotNil(pl)
	assert.Equal(testRoomID, pl.RoomID)
	assert.Equal(testPrize.ID, pl.PrizeID)

	// 验证任务是否插入成功
	var quest quests.Quest
	err = quests.CollectionQuests().FindOne(ctx, bson.M{
		"room_id": testRoomID,
	}).Decode(&quest)
	require.NoError(err)
	require.NotNil(quest)

	// 验证开始时间和结束时间
	expectedStartTime := now.Unix()
	expectedEndTime := expectedStartTime + duration

	assert.Equal(expectedStartTime, quest.StartTime)
	assert.Equal(expectedEndTime, quest.EndTime)
	assert.Equal(testRewardID, quest.RewardID)
	assert.Equal(duration, quest.Duration)
	assert.Equal(false, quest.Daily)
	assert.Equal("测试任务", quest.Intro)
	assert.Equal(now.Unix(), quest.CreateTime)
	assert.Equal(now.Unix(), quest.ModifiedTime)
}
