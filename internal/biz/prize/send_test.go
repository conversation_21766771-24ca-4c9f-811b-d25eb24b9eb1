package prize_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/internal/biz/prize"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		now        = goutil.TimeNow()
		testUserID = int64(223231)
	)
	appearances, err := appearance.Find(
		bson.M{
			"start_time":  bson.M{"$lte": now.Unix()},
			"expire_time": bson.M{"$not": bson.M{"$lte": now.Unix()}},
		},
		&options.FindOptions{Limit: goutil.NewInt64(2)},
	)
	require.NoError(err)
	require.Len(appearances, 2)
	appearanceIDs := goutil.SliceMap(appearances, func(a *appearance.Appearance) int64 {
		return a.ID
	})
	_, err = userappearance.Collection().DeleteMany(
		context.Background(),
		bson.M{
			"user_id":       testUserID,
			"appearance_id": bson.M{"$in": appearanceIDs},
		},
	)
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.PrizeLog{}).Error
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.Prize{}).Error
	require.NoError(err)
	prizes := []*liveprize.Prize{
		{
			ID:          1,
			Type:        liveprize.TypeUserAppearance,
			ElementID:   appearances[0].ID,
			ElementType: appearances[0].Type,
			Num:         1,
			Duration:    int64(10 * time.Hour.Seconds()),
		},
		{
			ID:          2,
			Type:        liveprize.TypeUserAppearance,
			ElementID:   appearances[1].ID,
			ElementType: appearances[1].Type,
			Num:         1,
			ExpireTime:  goutil.TimeNow().AddDate(0, 0, 1).Unix(),
			Duration:    int64(100 * time.Hour.Seconds()),
		},
		// 测试发放失败
		{
			ID:          3,
			Type:        liveprize.TypeUserAppearance,
			ElementID:   41241241241,
			ElementType: appearances[1].Type,
			Num:         1,
			ExpireTime:  goutil.TimeNow().AddDate(0, 0, 1).Unix(),
			Duration:    int64(100 * time.Hour.Seconds()),
		},
	}

	logs, err := prize.Send(
		[]prize.Distributor{
			prize.NewUserDistributor(testUserID, prizes[0], prize.WithBiz(liveprize.BizGiftUpgrade, 11)),
			prize.NewUserDistributor(testUserID, prizes[1], prize.WithBiz(liveprize.BizGiftUpgrade, 11)),
			prize.NewUserDistributor(testUserID, prizes[2], prize.WithBiz(liveprize.BizGiftUpgrade, 11)),
		},
	)
	assert.EqualError(err, fmt.Sprintf("error: can not find appearance element_id: %d element_type: %d (prize_log_id: 12)", prizes[2].ElementID, prizes[2].ElementType))
	require.Len(logs, len(prizes))

	// 检查日志
	var pls []*liveprize.PrizeLog
	err = liveprize.DB().
		Where("id IN (?)", goutil.SliceMap(logs, func(pl *liveprize.PrizeLog) int64 { return pl.ID })).
		Find(&pls).Error
	require.NoError(err)
	require.Len(logs, len(prizes))
	for _, log := range pls {
		switch log.PrizeID {
		case prizes[2].ID:
			assert.Equal(log.Status, liveprize.StatusReceiveFailed)
			assert.Equal(fmt.Sprintf("can not find appearance element_id: %d element_type: %d", prizes[2].ElementID, prizes[2].ElementType), log.MoreInfo.Error)
		default:
			assert.Equal(log.Status, liveprize.StatusReceived)
		}
		assert.Equal(log.UserID, testUserID)
		assert.Equal(log.Biz, liveprize.BizGiftUpgrade)
		assert.Equal(log.BizID, "11")
	}

	// 测试用户外观
	assert.Equal(liveprize.BizGiftUpgrade, logs[0].Biz)
	assert.Equal(liveprize.BizGiftUpgrade, logs[1].Biz)
	ua, err := userappearance.Find(bson.M{
		"user_id":       testUserID,
		"appearance_id": bson.M{"$in": appearanceIDs},
	}, nil)
	require.NoError(err)
	require.Len(ua, len(appearances))
	uaMap := util.ToMap(ua, func(a *userappearance.UserAppearance) int64 {
		return a.AppearanceID
	})
	require.NotNil(uaMap[appearances[0].ID])
	assert.LessOrEqual(now.Add(time.Duration(prizes[0].Duration)*time.Second).Unix(), *uaMap[appearances[0].ID].ExpireTime)
	require.NotNil(uaMap[appearances[1].ID])
	assert.LessOrEqual(now.AddDate(0, 0, 1).Unix(), *uaMap[appearances[1].ID].ExpireTime)
}
