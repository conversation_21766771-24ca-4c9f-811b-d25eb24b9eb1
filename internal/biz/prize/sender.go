package prize

import (
	"errors"
	"fmt"

	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
)

type sender interface {
	send() (*liveprize.PrizeLog, error)
}

type sendObserver struct {
	senders []sender

	errs      []error
	prizeLogs []*liveprize.PrizeLog
}

func (so *sendObserver) append(sender sender) {
	so.senders = append(so.senders, sender)
}

func (so *sendObserver) push() {
	for _, s := range so.senders {
		log, err := s.send()
		if err != nil {
			switch {
			case log == nil:
				so.errs = append(so.errs, fmt.Errorf("prize log is nil error: %v", err))
			case log.ID == 0: // 日志插入失败
				so.errs = append(so.errs,
					fmt.Errorf("error: %v (room_id: %d; user_id: %d; biz: %d; biz_id: %v; prize_id: %d)", err, log.RoomID, log.UserID, log.Biz, log.BizID, log.PrizeID))
			default:
				so.errs = append(so.errs, fmt.Errorf("error: %v (prize_log_id: %d)", err, log.ID))
			}
		}
		so.prizeLogs = append(so.prizeLogs, log)
	}
}

func (so *sendObserver) logs() []*liveprize.PrizeLog {
	return so.prizeLogs
}

func (so *sendObserver) error() error {
	if len(so.errs) == 0 {
		return nil
	}
	return errors.Join(so.errs...)
}
