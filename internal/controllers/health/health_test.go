package health

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionHealth(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/health", false, nil)
	_, msg, err := ActionHealth(c)
	require.NoError(err)
	assert.Equal("success", msg)
}
