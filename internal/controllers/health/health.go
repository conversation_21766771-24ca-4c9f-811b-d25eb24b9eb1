package health

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler returns handler
func Handler() *handler.HandlerV2 {
	return &handler.HandlerV2{
		Actions: map[string]*handler.ActionV2{
			"health": handler.NewActionV2(handler.GET, ActionHealth),
		},
	}
}

// ActionHealth action health
func ActionHealth(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "success", nil
}
