package medal

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestChangeOperator(t *testing.T) {
	assert := assert.New(t)

	var count int
	cleanup := mrpc.SetMock(userapi.URIRankEvent, func(any) (any, error) {
		count++
		return nil, nil
	})
	defer cleanup()

	f := ChangeOperator()
	assert.NotPanics(func() { f(&databus.Message{}) })

	msg := medalChangeMessage{
		UserID: 100,
		RoomID: 223344,
	}
	f(&databus.Message{
		Key:   keys.KeyLiveMedalChange1.Format(msg.UserID),
		Value: json.RawMessage(tutil.SprintJSON(msg)),
	})
	assert.Zero(count)

	msg = medalChangeMessage{
		UserID:     100,
		RoomID:     223344,
		ChangeType: livemedal.ChangeTypeNew,
	}
	f(&databus.Message{
		Key:   keys.KeyLiveMedalChange1.Format(msg.UserID),
		Value: json.RawMessage(tutil.SprintJSON(msg)),
	})
	assert.Equal(1, count)
}
