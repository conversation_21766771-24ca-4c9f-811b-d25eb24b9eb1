package medal

import (
	"encoding/json"

	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/activity/rankevent"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

type medalChangeMessage struct {
	UserID       int64 `json:"user_id"`
	RoomID       int64 `json:"room_id"`
	ChangeType   int   `json:"change_type"`
	ChangeReason int   `json:"change_reason"`
	ChangeSource int   `json:"change_source"`
	CreateTime   int64 `json:"create_time"` // 单位：秒
}

// ChangeOperator 用户直播粉丝牌的变化 - 消费者
func ChangeOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !keys.KeyLiveMedalChange1.MatchKey(m.Key) {
			return
		}
		var msg medalChangeMessage
		err := json.Unmarshal(m.Value, &msg)
		if err != nil {
			logger.Error(err)
			return
		}
		if msg.UserID <= 0 {
			return
		}

		switch msg.ChangeType {
		case livemedal.ChangeTypeNew:
			if msg.RoomID <= 0 {
				return
			}
			r, err := room.Find(msg.RoomID)
			if err != nil {
				logger.WithField("room_id", msg.RoomID).Error(err)
				return
			}
			if r == nil {
				logger.WithField("room_id", msg.RoomID).Error("room not found")
				return
			}

			rankevent.NewSyncCommonParam(msg.UserID).
				SetRoomInfo(r.RoomID, r.CreatorID, r.GuildID, r.ActivityCatalogID).
				General(rankevent.EventAddMedal).Send(mrpc.NewUserContextFromEnv())
		default:
			// PASS
		}
	}
}
