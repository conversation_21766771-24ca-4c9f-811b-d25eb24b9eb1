package noble

import (
	"errors"
	"fmt"
	"html"
	"net/http"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionMyRecommend 用户的推荐记录
/**
 * @api {get} /api/v2/noble/myrecommend 我的神话推荐记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] page size
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "room_id": 123, //  "直播间 ID"
 *           "creator_username": "主播昵称",
 *           "creator_id": 123, // "主播 ID"
 *           "creator_iconurl": "主播头像",
 *           "from_username": "推荐人昵称",
 *           "from_user_id": 456, // "推荐人 ID"
 *           "anonymous": 0,  // 是否是匿名推荐：0: 不是匿名推荐；1：匿名推荐
 *           "start_time": 1234567890,
 *           "end_time": 1234567891,
 *           "create_time": 1234567890,
 *           "recommend_time": "1970-01-01 08:00 至 08:30"
 *         }
 *       ],
 *       "pagination":{
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 */
func ActionMyRecommend(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	data, pa, err := livenoblerecommend.FindByFromUser(c.UserID(), p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return handler.M{
		"Datas":      data,
		"pagination": pa,
	}, nil
}

type recommendNewParam struct {
	RoomID    int64 `form:"room_id" json:"room_id"`
	StartTime int64 `form:"start_time" json:"start_time"`
	Anonymous int   `form:"anonymous" json:"anonymous"`

	c       *handler.Context
	r       *room.Room
	s       userstatus.GeneralStatus
	creator *liveuser.User
}

type recommendResp struct {
	Success         bool   `json:"success"`
	Msg             string `json:"msg"`
	RecommendNum    int    `json:"recommend_num"`
	CreatorUsername string `json:"creator_username,omitempty"`
}

// ActionRecommendNew 推荐主播上热门
/**
 * @api {post} /api/v2/noble/recommend/new 推荐主播上热门
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} start_time 推荐开始时间时间戳，单位：秒
 * @apiParam {number=0,1} [anonymous] 是否匿名，0：不匿名；1：匿名
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "success": true,
 *       "recommend_num": 10,
 *       "msg": "推荐成功"
 *     }
 *   }
 * @apiSuccessExample 推荐失败的情况:
 *   {
 *     "code": 0,
 *     "info": {
 *       "success": false,
 *       "msg": "您的推荐次数已用完",
 *       "recommend_num": 100
 *     }
 *   }
 */
func ActionRecommendNew(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendNewParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.StartTime <= 0 ||
		(param.Anonymous != 0 && param.Anonymous != 1) {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	r, err := param.checkUserAndRoom()
	if err != nil {
		return nil, err
	}
	if r != nil {
		return r, nil
	}
	r, err = param.checkStartTime()
	if err != nil {
		return nil, err
	}
	if r != nil {
		return r, nil
	}
	r, err = param.recommend()
	if err != nil {
		return nil, err
	}
	param.afterRecommend()
	return r, nil
}

func (param *recommendNewParam) checkUserAndRoom() (*recommendResp, error) {
	var err error
	param.s, _, err = userstatus.UserGeneral(param.c.UserID(), param.c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.s.RecommendNum == nil {
		return nil, actionerrors.ErrNoAuthority
	}
	if *param.s.RecommendNum == 0 {
		return &recommendResp{
			Success:      false,
			Msg:          "您的推荐次数已用完，续费神话或上神可获得推荐机会哦~",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}

	// 检查房间是否存在
	param.r, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil || param.r.Limit != nil {
		return &recommendResp{
			Success:      false,
			Msg:          "该直播间不存在",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	param.creator, err = liveuser.Find(param.r.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.creator == nil {
		return &recommendResp{
			Success:      false,
			Msg:          "该主播不存在",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	banned, err := userstatus.IsBanned(param.r.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r.IsBan() || banned {
		return &recommendResp{
			Success:      false,
			Msg:          "该直播间已被封禁，暂时无法推荐！",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	v, err := liveaddendum.Vitality(param.creator.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if v == nil {
		logger.WithField("creator_id", param.creator.UserID()).Error("Room creator doesn't have vitality.")
		// PASS
		v = new(int)
		*v = liveaddendum.MaxVitality
	}
	if *v < 6 {
		return &recommendResp{
			Success:      false,
			Msg:          "直播间元气值过低，暂时无法推荐！",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	param.r.SchemeToURL()
	return nil, nil
}

func (param *recommendNewParam) checkStartTime() (*recommendResp, error) {
	now := goutil.TimeNow()
	formatedNow := formatNow(now)

	timeRange := [2]time.Time{
		formatedNow.AddDate(0, 0, 1),
		formatedNow.AddDate(0, 0, 8),
	}

	uvMap, err := vip.UserVipInfos(param.c.UserID(), false, param.c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 获取上神和神话的最长有效期的到期时间
	var maxExpireTime int64
	for key, v := range uvMap {
		if v != nil && v.IsActive() && (key == vip.TypeLiveHighness || v.Level == vip.NobleLevel7) &&
			v.ExpireTime > maxExpireTime {
			maxExpireTime = v.ExpireTime
		}
	}
	if maxExpireTime < timeRange[0].Unix() {
		return &recommendResp{
			Success:      false,
			Msg:          "您选择的推荐时间超出了您的贵族有效期，请重新选择！",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	if maxExpireTime < timeRange[1].Unix() {
		// WORKAROUND: 由于神话过期不是整 30 分，可能会出现神话过期早于推荐结束时间的情况，忽略
		timeRange[1] = time.Unix(maxExpireTime, 0)
	}
	if param.StartTime != livenoblerecommend.StartTime(param.StartTime) {
		return &recommendResp{
			Success:      false,
			Msg:          "推荐开始时间不符规范",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	startTime := time.Unix(param.StartTime, 0)
	if startTime.Before(timeRange[0]) /* st < t0 */ ||
		!startTime.Before(timeRange[1]) /* st >= t1 */ {
		return &recommendResp{
			Success:      false,
			Msg:          "您选择的推荐时间超出了可申请范围，请重新选择！",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	return nil, nil
}

var errRecommendConflicted = errors.New("recommend conflicted")

func (param *recommendNewParam) recommend() (*recommendResp, error) {
	key := keys.LockNobleRecommend1.Format(param.StartTime)
	ok, err := service.Redis.SetNX(key, 1, time.Minute).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewUnknownError(http.StatusBadRequest, "服务器繁忙，请稍后重试")
	}
	defer func() {
		service.Redis.Del(key)
	}()
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		ok, err = livenoblerecommend.IsFreeTime(param.StartTime, tx)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return errRecommendConflicted
		}
		// 添加推荐直播
		topRecommend := liverecommendedelements.Model{
			Sort:        1,
			ElementID:   param.r.RoomID,
			ElementType: liverecommendedelements.ElementSchedule,
		}
		topRecommend.Attr = util.NewBitMaskFromFlag(liverecommendedelements.AttrTagNoble)
		topRecommend.StartTime = &param.StartTime
		topRecommend.ExpireTime = livenoblerecommend.EndTime(param.StartTime)
		err := tx.Create(&topRecommend).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 添加神话推荐记录
		nr := &livenoblerecommend.NobleRecommend{
			FromUserID:           param.c.UserID(),
			CreatorID:            param.r.CreatorID,
			RoomID:               param.RoomID,
			Anonymous:            param.Anonymous,
			StartTime:            param.StartTime,
			RecommededScheduleID: topRecommend.ID,
			EndTime:              livenoblerecommend.EndTime(param.StartTime),
		}
		err = tx.Save(nr).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// NOTICE: 扣除推荐次数在 mongodb 不在 mysql, 不能很好的放在事务中
		ok, res, err := userstatus.DecreaseRecommendNum(param.c.UserID(), 1)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return actionerrors.ErrParamsMsg("扣除推荐次数失败")
		}
		*param.s.RecommendNum = res // 把扣除后的次数传过去
		return nil
	})
	if err != nil {
		if errors.Is(err, errRecommendConflicted) {
			return &recommendResp{
				Success:      false,
				Msg:          "该时间段已有神话推荐，请重新选择！",
				RecommendNum: *param.s.RecommendNum,
			}, nil
		}
		return nil, err
	}
	return &recommendResp{
		Success:      true,
		Msg:          "推荐成功",
		RecommendNum: *param.s.RecommendNum,
	}, nil
}

func (param *recommendNewParam) afterRecommend() {
	st := time.Unix(param.StartTime, 0)
	et := time.Unix(livenoblerecommend.EndTime(param.StartTime), 0)
	// 发邮件
	email := pushservice.Email{
		To:      config.Conf.Params.NobleParams.RecommendNotifyEmail,
		Subject: "【猫耳FM】神话推荐申请",
	}
	email.Body = fmt.Sprintf("<p>神话贵族：%s</p><p>推荐主播：%s (ID: %d)</p>"+
		"<p>房间号：%d</p><p>推荐时段：%s 至 %s</p>",
		html.EscapeString(param.c.User().Username), html.EscapeString(param.r.CreatorUsername), param.r.CreatorID,
		param.r.RoomID, st.Format(util.TimeFormatYMDHMS), et.Format(util.TimeFormatYMDHMS))
	err := service.PushService.SendEmail(email)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type recommendCancelParam struct {
	RecommendID int64 `form:"recommend_id" json:"recommend_id"`
	Confirm     int   `form:"confirm" json:"confirm"`
	c           *handler.Context
	r           *livenoblerecommend.WithNameUserInfo
	startTime   string
	endTime     string
}

// ActionAdminCancelRecommend 取消神话推荐
/**
 * @api {post} /api/v2/admin/noble/recommend/cancel 取消神话推荐
 * @apiVersion 0.1.0
 * @apiGroup api/v2/admin/noble
 *
 * @apiParam {Number} recommend_id 推荐号
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "推荐取消成功"
 *   }
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "推荐主播：bless\n房间号：1234567\n推荐时间：2020-12-02 21:00 至 21:30\n申请人：零月\n匿名状态：否"
 *     }
 *   }
 *
 * @apiErrorExample 推荐失败的情况:
 *   {
 *     "code": 100010002,
 *     "info": "该推荐不存在！"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 */
func ActionAdminCancelRecommend(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendCancelParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.cancelRecommend()
	if err != nil {
		return nil, err
	}
	return "推荐取消成功", nil
}

func (param *recommendCancelParam) check() error {
	var err error
	if param.RecommendID <= 0 {
		return actionerrors.ErrParams
	}
	param.r, err = livenoblerecommend.FindWithNameUserInfo(param.RecommendID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return actionerrors.ErrNotFound("该推荐不存在！")
	}
	st := time.Unix(param.r.StartTime, 0)
	et := time.Unix(param.r.EndTime, 0)
	param.startTime = st.Format(util.TimeFormatYMDHHMM)
	param.endTime = et.Format(util.TimeFormatHHMM)
	if st.Day() != et.Day() {
		param.endTime = et.Format(util.TimeFormatYMDHHMM)
	}
	if param.Confirm == 0 {
		anonymous := "否"
		if param.r.Anonymous != 0 {
			anonymous = "是"
		}
		message := fmt.Sprintf("推荐主播：%s\n房间号：%d\n推荐时间：%s 至 %s\n"+
			"申请人：%s\n匿名状态：%s", param.r.CreatorUsername, param.r.RoomID,
			param.startTime, param.endTime, param.r.FromUsername,
			anonymous)
		return actionerrors.ErrConfirmRequired(message, 1)
	}

	if param.r.Status != livenoblerecommend.StatusNormal {
		return actionerrors.ErrNotFound("该推荐已被取消，无法重复操作！")
	}
	nowUnix := goutil.TimeNow().Unix()
	if param.r.StartTime <= nowUnix {
		return actionerrors.ErrNotFound("该推荐已生效，无法取消！")
	}
	return nil
}

func (param recommendCancelParam) cancelRecommend() error {
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		db := tx.Table(livenoblerecommend.TableName()).Where("id = ? AND status = ?",
			param.r.ID, livenoblerecommend.StatusNormal).Updates(map[string]interface{}{
			"status":        livenoblerecommend.StatusCancel,
			"modified_time": goutil.TimeNow().Unix(),
		})
		if db.Error != nil {
			return db.Error
		}
		if db.RowsAffected != 1 {
			return errors.New("数据库更新失败")
		}

		db = tx.Table(liverecommendedelements.TableName()).Where(
			"id = ? AND sort <> 0",
			param.r.RecommededScheduleID).Updates(map[string]interface{}{
			"sort":          0,
			"modified_time": goutil.TimeNow().Unix(),
		})
		if db.Error != nil {
			return db.Error
		}
		// 判断是否只更新多条记录
		// 存在记录被删除情况 可以为 0
		if db.RowsAffected > 1 {
			return errors.New("数据库更新失败")
		}

		_, _, err := userstatus.IncreaseRecommendNum(param.r.FromUserID, 1)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.afterCancelRecommend()
	return nil
}

func (param recommendCancelParam) afterCancelRecommend() {

	content := fmt.Sprintf(
		`<p>亲爱的神话用户，您为主播 <strong>%s</strong> 申请的 <strong>%s 至 %s</strong> 的神话推荐已取消，推荐次数已返还，如有疑问，请联系您的 VIP 客服。</p>`,
		html.EscapeString(param.r.CreatorUsername),
		param.startTime, param.endTime)
	err := messageassign.SystemMessageAssign(param.r.FromUserID, "神话推荐取消", content)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	logbox := userapi.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("取消神话推荐，推荐 ID: %d, 推荐人（ID: %d）：%s, 主播（ID: %d）：%s", param.r.ID, param.r.FromUserID,
		param.r.FromUsername, param.r.CreatorID, param.r.CreatorUsername)
	logbox.AddAdminLog(intro, userapi.CatalogCancelNobleRecommend)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

var weekStr = [7]string{
	"周日",
	"周一",
	"周二",
	"周三",
	"周四",
	"周五",
	"周六",
}

type recommendCandidateTimeResp struct {
	CandidateDates []wheel `json:"candidate_dates"`
	SelectedDate   string  `json:"selected_date"`
	CandidateTime  []wheel `json:"candidate_time"`
}

type wheel struct {
	Show          string `json:"show,omitempty"`           // CandidateDates, CandidateTime 用
	Date          string `json:"date,omitempty"`           // CandidateDates, SelectedDate 用
	StartTime     int64  `json:"start_time,omitempty"`     // CandidateTime 用
	RecommendTime string `json:"recommend_time,omitempty"` // CandidateTime 用
}

// ActionRecommendCandidateTime 获取神话推荐候选时间
/**
 * @api {get} /api/v2/noble/recommend/candidatetime 获取神话推荐候选时间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {String} [date] 日期 e.g. 2006-01-02, 不传则返回候选的第一日的时间
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "candidate_dates": [{
 *         "show": "2 月 3 日 周一",  // 轮盘中展示该字段
 *         "date": "2020-02-03"
 *       }, {
 *         "show": "2 月 4 日 周二",
 *         "date": "2020-02-04"
 *       }],
 *       "selected_date": "2020-02-03",  // 和传进来的 date 一致
 *       "candidate_time":[{
 *         "show": "00:00",
 *         "start_time": 1234567890, // 申请神话推荐的入参之一
 *         "recommend_range": "2020-02-03 00:00 至 00:30" // 确认推荐信息处返回
 *       }, {
 *         "show": "23:30",
 *         "start_time": 1234567891,
 *         "recommend_range": "2020-02-03 23:30 至 2020-02-04 00:00" // 推荐开始和推荐结束跨天的情况
 *       }]
 *     }
 *   }
 */
func ActionRecommendCandidateTime(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	formatedNow := formatNow(now)

	wheels, timeRange := candidateDates(formatedNow)
	resp := recommendCandidateTimeResp{CandidateDates: wheels}
	dateStr, ok := c.GetParam("date")
	var date time.Time
	if ok {
		var err error
		date, err = time.ParseInLocation(util.TimeFormatYMD, dateStr, time.Local)
		if err != nil {
			return nil, actionerrors.ErrParams
		}
		resp.SelectedDate = dateStr
	} else {
		date, _ = time.ParseInLocation(util.TimeFormatYMD, resp.CandidateDates[0].Date, time.Local)
		resp.SelectedDate = resp.CandidateDates[0].Date
	}
	if timeRange[0].Before(date) {
		timeRange[0] = date
	}
	date = date.Add(24 * time.Hour)
	if timeRange[1].After(date) {
		timeRange[1] = date
	}
	if !timeRange[0].Before(timeRange[1]) {
		resp.CandidateTime = []wheel{}
		return resp, nil
	}
	nrs, err := livenoblerecommend.FindByRecommendTime(timeRange[0].Unix(), timeRange[1].Unix())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	selectedSet := make(map[int64]struct{}, 8) // 8: 认为每天最多有晚上的 4 小时被推荐
	origin := livenoblerecommend.StartTime(timeRange[0].Unix())
	for i := range nrs {
		st := livenoblerecommend.StartTime(nrs[i].StartTime)
		for ; st < nrs[i].EndTime; st += livenoblerecommend.RecommendDuration {
			selectedSet[st] = struct{}{}
		}
	}
	// 一般情况: 一天/一场 + 1
	// 开始和结束的天数会少一点
	maxLen := (livenoblerecommend.EndTime(timeRange[1].Unix()) - origin) / livenoblerecommend.RecommendDuration
	resp.CandidateTime = make([]wheel, 0, maxLen)
	for st := origin; st < timeRange[1].Unix(); st += livenoblerecommend.RecommendDuration {
		if _, ok := selectedSet[st]; !ok {
			t := time.Unix(st, 0)
			resp.CandidateTime = append(resp.CandidateTime,
				wheel{
					Show:      t.Format(util.TimeFormatHHMM),
					StartTime: st,
				})
		}
	}
	for i := range resp.CandidateTime {
		resp.CandidateTime[i].makeRecommendTime()
	}
	return resp, nil
}

func candidateDates(when time.Time) ([]wheel, [2]time.Time) {
	timeRange := [2]time.Time{
		when.AddDate(0, 0, 1),
		when.AddDate(0, 0, 8),
	}
	dates := make([]time.Time, 0, 8)
	for ct := util.BeginningOfDay(timeRange[0]); ct.Before(timeRange[1]); ct = ct.AddDate(0, 0, 1) {
		dates = append(dates, ct)
	}
	wheels := make([]wheel, 0, len(dates))
	for i := 0; i < len(dates); i++ {
		wheels = append(wheels, wheel{
			Show: fmt.Sprintf("%s %s",
				// \u200a 为 unicode 格式的发宽空格
				dates[i].Format("1\u200a月\u200a2\u200a日"), weekStr[dates[i].Weekday()]),
			Date: dates[i].Format(util.TimeFormatYMD),
		})
	}
	return wheels, timeRange
}

func (w *wheel) makeRecommendTime() {
	endTime := livenoblerecommend.EndTime(w.StartTime)
	w.RecommendTime = livenoblerecommend.RecommendTime(w.StartTime, endTime)
}

func formatNow(now time.Time) time.Time {
	duration := livenoblerecommend.RecommendDuration * time.Second
	formatedNow := now.Round(duration)
	if formatedNow.Before(now) {
		return formatedNow.Add(duration)
	}
	return formatedNow
}

// ActionRecommendCheck 检查推荐是否可行
/**
 * @api {post} /api/v2/noble/recommend/new 推荐主播上热门
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} start_time 推荐开始时间时间戳，单位：秒
 * @apiParam {number=0,1} [anonymous] 是否匿名，0：不匿名；1：匿名
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "success": true,
 *       "mgs": "参数无误，可以推荐",
 *       "creator_username": "主播昵称",
 *       "recommend_num": 100
 *     }
 *   }
 * @apiSuccessExample 不可推荐的情况:
 *   {
 *     "code": 0,
 *     "info": {
 *       "success": false,
 *       "msg": "您的推荐次数已用完，续费神话或上神可获得推荐机会哦~",
 *       "recommend_num": 100
 *     }
 *   }
 */
func ActionRecommendCheck(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendNewParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.StartTime <= 0 ||
		(param.Anonymous != 0 && param.Anonymous != 1) {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	r, err := param.checkUserAndRoom()
	if err != nil {
		return nil, err
	}
	if r != nil {
		return r, nil
	}
	r, err = param.checkStartTime()
	if err != nil {
		return nil, err
	}
	if r != nil {
		return r, nil
	}
	ok, err := livenoblerecommend.IsFreeTime(param.StartTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return &recommendResp{
			Success:      false,
			Msg:          "该时间段已有神话推荐，请重新选择！",
			RecommendNum: *param.s.RecommendNum,
		}, nil
	}
	return &recommendResp{
		Success:         true,
		Msg:             "参数无误，可以推荐",
		CreatorUsername: param.r.CreatorUsername,
		RecommendNum:    *param.s.RecommendNum,
	}, nil
}
