package noble

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMyNobleTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(myNobleInfo{}, "level", "title",
		"all_privilege_num", "privilege_num", "privilege_rank_invisible",
		"avatar_frame_url", "expire_time", "renewal_deadline",
		"customer_service_info", "config", "status", "tip")

	kc.Check(nobleConfig{}, "invisible", "recommend_num")

	kc.Check(myVipInfo{}, "level", "title",
		"privilege_num", "privilege_rank_invisible", "avatar_frame_url", "expire_time", "renewal_deadline",
		"status", "tip", "max_tip", "spend", "renewal_threshold")

	kc.Check(myVipResp{}, "all_privilege_num", "noble", "trial_noble", "highness", "potential_highness",
		"customer_service_info", "config")
}

func TestActionMyNoble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "mynoble", true, nil)
	c.User().ID = 10
	r, err := ActionMyNoble(c)
	require.NoError(err)
	assert.Nil(r)

	c = handler.NewTestContext("GET", "mynoble", true, nil)
	c.User().ID = noble7UserID
	r, err = ActionMyNoble(c)
	require.NoError(err)
	assert.NotNil(r)

	c = handler.NewTestContext("GET", "mynoble?type=1", true, nil)
	c.User().ID = noble7UserID
	r, err = ActionMyNoble(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*myVipResp)
	require.NotNil(resp.Noble)
	require.Nil(resp.Highness)
}

func TestOldMyNobleResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()

	info := &vip.Info{
		Level:               7,
		Title:               "测试",
		CustomerServiceInfo: &vip.CustomerServiceInfo{CopyText: "123456"},
	}
	info.Privilege.Set(vip.PrivilegeInvisible)
	uv := &vip.UserVip{ExpireTime: now.Unix() + 10, UserID: noble7UserID, Info: info}
	c := handler.NewTestContext("GET", "/mynoble", true, nil)
	c.User().ID = noble7UserID
	r := myNobleActive(c, uv)
	resp := r.(*myNobleInfo)
	assert.NotNil(resp.Config.Invisible)
	assert.NotNil(resp.Config.RecommendNum)
	assert.Equal("测试", resp.Title)
	assert.Equal(uv.ExpireTime, resp.ExpireTime)
	assert.Zero(resp.RenewalDeadline)
	assert.Equal("123456", info.CustomerServiceInfo.CopyText)

	uv.ExpireTime = 1000
	uv.Level = 4
	assert.Nil(myNobleOutdated(uv, now))
	uv.ExpireTime = now.Unix()
	r = myNobleOutdated(uv, now)
	require.NotNil(r)
	resp = r.(*myNobleInfo)
	assert.NotZero(resp.RenewalDeadline)
	assert.Zero(resp.ExpireTime)
	assert.Equal("测试", resp.Title)
	assert.Equal(4, resp.Level)
	assert.Equal("续费保护期还剩 5 天", resp.Tip)
}

var testMyNobleUserID int64 = 123

func insertTestVipData(t *testing.T) (int64, func()) {
	key := keys.KeyNobleUserVips1.Format(testMyNobleUserID)
	err := service.Redis.Set(key, `{"1":{"user_id":123,"type":1,"level":7,"expire_time":9999999999},"2":{"user_id":123,"type":2,"level":1,"expire_time":9999999999}}`,
		30*time.Second).Err()
	require.NoError(t, err)
	return testMyNobleUserID, func() {
		service.Redis.Del(key)
	}
}

func TestActionMyVip(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID, cleanFunc := insertTestVipData(t)
	defer cleanFunc()
	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	c.User().ID = userID
	r, err := actionMyVip(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*myVipResp)
	require.NotNil(resp)
	assert.NotNil(resp.Noble)
	assert.NotNil(resp.Highness)
	assert.NotEmpty(resp.CustomerServiceInfo)
}

func TestFindVips(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID, cleanFunc := insertTestVipData(t)
	defer cleanFunc()
	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	c.User().ID = userID
	resp := myVipResp{c: c}
	err := resp.findVips()
	require.NoError(err)
	noble := resp.uvMap[vip.TypeLiveNoble]
	require.NotNil(noble)
	assert.Equal(7, noble.Level)
	highness := resp.uvMap[vip.TypeLiveHighness]
	require.NotNil(highness)
	assert.Equal(1, highness.Level)
	require.NotNil(resp.Highness)
	assert.Equal(vip.HighnessRenewalThreshold, *resp.Highness.RenewalThreshold)
}

func TestNewMyVipInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// newMyVipInfoActive
	uv := &vip.UserVip{
		ExpireTime: goutil.TimeNow().AddDate(0, 0, vip.HighnessMaxRenewDays+1).Unix(),
		Type:       vip.TypeLiveHighness,
		Info: &vip.Info{
			Level:        1,
			Title:        "上神",
			PrivilegeNum: 1,
			AvatarFrame:  "oss://test.png",
		},
	}
	info := newMyVipInfo(uv)
	require.NotNil(info)
	assert.Equal("您的上神有效期已达上限，继续续费可获得特权道具，但有效期将不再延长", info.MaxTip)
	assert.Equal(vip.HighnessRenewalThreshold, *info.RenewalThreshold)
	assert.Equal(myVipStatusActive, info.Status)

	// newMyVipInfoActive
	uv = &vip.UserVip{
		ExpireTime: goutil.TimeNow().AddDate(0, 0, vip.HighnessMaxRenewDays).Unix(),
		Type:       vip.TypeLiveHighness,
		Info: &vip.Info{
			Level:        1,
			Title:        "上神",
			PrivilegeNum: 1,
			AvatarFrame:  "oss://test.png",
		},
	}
	info = newMyVipInfo(uv)
	require.NotNil(info)
	assert.Empty(info.MaxTip)
	assert.Equal(vip.HighnessRenewalThreshold, *info.RenewalThreshold)
	assert.Equal(myVipStatusActive, info.Status)

	// newMyVipOutdated
	uv = &vip.UserVip{
		ExpireTime: goutil.TimeNow().Add(-11 * 24 * time.Hour).Unix(),
		Info:       &vip.Info{},
	}
	info = newMyVipInfo(uv)
	require.Nil(info)

	// newMyVipOutdated
	uv = &vip.UserVip{
		ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		Info:       &vip.Info{},
	}
	info = newMyVipInfo(uv)
	require.NotNil(info)
	assert.Equal(myVipStatusProtecting, info.Status)
}

func TestNewMyVipActive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	info := &vip.Info{
		Level:               7,
		Title:               "测试",
		CustomerServiceInfo: &vip.CustomerServiceInfo{CopyText: "123456"},
	}
	info.Privilege.Set(vip.PrivilegeInvisible)
	noble := &vip.UserVip{ExpireTime: now.Unix() + 10, UserID: noble7UserID, Info: info}
	resp := newMyVipInfoActive(noble)
	require.NotNil(resp)
	assert.Equal("测试", resp.Title)
	assert.Equal(resp.ExpireTime, resp.ExpireTime)
	assert.Zero(resp.RenewalDeadline)
	assert.Equal("123456", info.CustomerServiceInfo.CopyText)
	assert.Equal("测试到期："+vip.FormatTipEndTime(vip.TypeLiveNoble, now.Unix()), resp.Tip)
}

func TestNewMyVipOutdated(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	expiredTime := goutil.TimeNow()
	info := &vip.Info{
		Level:               7,
		Title:               "测试",
		CustomerServiceInfo: &vip.CustomerServiceInfo{CopyText: "123456"},
	}
	noble := &vip.UserVip{ExpireTime: expiredTime.Unix(), UserID: noble7UserID, Level: 4, Info: info}

	resp := newMyVipOutdated(noble)
	require.NotNil(resp)
	assert.NotZero(resp.RenewalDeadline)
	assert.Zero(resp.ExpireTime)
	assert.Equal("测试", resp.Title)
	assert.Equal(4, resp.Level)
	assert.Equal("测试续费保护结束："+vip.FormatTipEndTime(vip.TypeLiveNoble, expiredTime.AddDate(0, 0, 10).Unix()), resp.Tip)

	resp = newMyVipOutdated(&vip.UserVip{Type: vip.TypeLiveTrialNoble, ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix()})
	require.Nil(resp)
}

func TestFindUserStatus(t *testing.T) {
	assert := assert.New(t)

	var (
		testSpecialCustomerService = &vip.Info{CustomerServiceInfo: &vip.CustomerServiceInfo{ShowText: "QQ：12345", CopyText: "12345"}}
		testVipCustomerService     = &vip.Info{CustomerServiceInfo: &vip.CustomerServiceInfo{ShowText: "QQ：23456", CopyText: "23456"}}
	)

	userID, cleanFunc := insertTestVipData(t)
	defer cleanFunc()
	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	c.User().ID = userID
	resp := myVipResp{c: c,
		uvMap: map[int]*vip.UserVip{
			vip.TypeLiveNoble:    {Type: vip.TypeLiveNoble, Info: testSpecialCustomerService, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
			vip.TypeLiveHighness: {Type: vip.TypeLiveHighness, Info: testVipCustomerService, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
		},
	}
	resp.findUserStatus()
	assert.NotNil(resp.Config)
	assert.Equal(testVipCustomerService.CustomerServiceInfo.ShowText, resp.CustomerServiceInfo.ShowText)
	assert.Equal(testVipCustomerService.CustomerServiceInfo.CopyText, resp.CustomerServiceInfo.CopyText)

	resp = myVipResp{c: c,
		uvMap: map[int]*vip.UserVip{
			vip.TypeLiveNoble: {Type: vip.TypeLiveNoble, Info: testSpecialCustomerService, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
		},
	}
	resp.findUserStatus()
	assert.Equal(testSpecialCustomerService.CustomerServiceInfo.ShowText, resp.CustomerServiceInfo.ShowText)
	assert.Equal(testSpecialCustomerService.CustomerServiceInfo.CopyText, resp.CustomerServiceInfo.CopyText)

	resp = myVipResp{c: c,
		uvMap: map[int]*vip.UserVip{
			vip.TypeLiveNoble:    {Type: vip.TypeLiveNoble, Info: testSpecialCustomerService, ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix()},
			vip.TypeLiveHighness: {Type: vip.TypeLiveHighness, Info: testVipCustomerService, ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix()},
		},
	}
	resp.findUserStatus()
	assert.Nil(resp.CustomerServiceInfo)
}

func TestPotentialHighness(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyUsersPotentialHighness0.Format()
	require.NoError(service.Redis.Del(key).Err())

	now := goutil.BeginningOfDay(goutil.TimeNow())
	testUserID := int64(9074509)
	key2 := keys.KeyUserConsumptionLast29Days2.Format(testUserID, now.Unix())
	require.NoError(service.Redis.Del(key2).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userconsumption.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	firstDate := now.AddDate(0, 0, -29).Unix()
	_, err = userconsumption.Collection().InsertMany(ctx, []interface{}{
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: now.Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: now.AddDate(0, 0, -15).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: firstDate},
	})
	require.NoError(err)

	var (
		testVipCustomerService = &vip.Info{CustomerServiceInfo: &vip.CustomerServiceInfo{ShowText: "QQ：23456", CopyText: "23456"}}
	)

	// 测试上神有效不返回
	p := &myVipResp{
		uvMap: map[int]*vip.UserVip{
			vip.TypeLiveHighness: {Type: vip.TypeLiveHighness, Info: testVipCustomerService, ExpireTime: goutil.TimeNow().Unix() + 100},
		},
	}
	require.NoError(p.potentialHighness())
	assert.Nil(p.PotentialHighness)

	// 测试上神在保护期不返回
	p = &myVipResp{
		uvMap: map[int]*vip.UserVip{
			vip.TypeLiveHighness: {Type: vip.TypeLiveHighness, Info: testVipCustomerService, ExpireTime: goutil.TimeNow().Unix() - 100},
		},
	}
	require.NoError(p.potentialHighness())
	assert.Nil(p.PotentialHighness)

	// 测试不在潜力上神名单的情况
	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	c.User().ID = testUserID
	p = &myVipResp{c: c}
	require.NoError(p.potentialHighness())
	assert.Nil(p.PotentialHighness)

	// 测试在潜力上神名单的情况
	require.NoError(service.Redis.SAdd(key, testUserID).Err())
	require.NoError(p.potentialHighness())
	resp := p.PotentialHighness
	require.NotNil(resp)
	assert.EqualValues(123, resp.Expire)
	assert.EqualValues(246, resp.Spend)
	assert.EqualValues(9999631, resp.Remain)
	assert.EqualValues(firstDate, resp.StartDate)
	assert.EqualValues(vip.HighnessOpenCoin, resp.OpenThreshold)

	// 测试上神过期期返回进度
	p = &myVipResp{
		c: c,
		uvMap: map[int]*vip.UserVip{
			vip.TypeLiveHighness: {Type: vip.TypeLiveHighness, Info: testVipCustomerService, ExpireTime: goutil.TimeNow().Unix() - 10000000},
		},
	}
	require.NoError(p.potentialHighness())
	resp = p.PotentialHighness
	require.NotNil(resp)
	assert.EqualValues(123, resp.Expire)
	assert.EqualValues(246, resp.Spend)
	assert.EqualValues(9999631, resp.Remain)
	assert.EqualValues(firstDate, resp.StartDate)
	assert.EqualValues(vip.HighnessOpenCoin, resp.OpenThreshold)
}
