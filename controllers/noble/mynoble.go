package noble

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 贵族状态
const (
	myVipStatusActive = iota + 1
	myVipStatusProtecting
)

// 贵族详情类型
const (
	typeOldMyNoble = iota // TODO: 兼容旧版本，web 上神上线后移除
	typeMyNoble
)

type nobleConfig struct {
	Invisible    *bool `json:"invisible,omitempty"`
	RecommendNum *int  `json:"recommend_num,omitempty"`
}

type myNobleInfo struct {
	Level                  int                      `json:"level"`
	Title                  string                   `json:"title"`
	AllPrivilegeNum        int                      `json:"all_privilege_num,omitempty"`
	PrivilegeNum           int                      `json:"privilege_num,omitempty"`
	PrivilegeRankInvisible bool                     `json:"privilege_rank_invisible"`
	AvatarFrame            string                   `json:"avatar_frame_url"`
	ExpireTime             int64                    `json:"expire_time,omitempty"`
	RenewalDeadline        int64                    `json:"renewal_deadline,omitempty"`
	CustomerServiceInfo    *vip.CustomerServiceInfo `json:"customer_service_info,omitempty"`
	Config                 nobleConfig              `json:"config"`
	Status                 int                      `json:"status"`
	Tip                    string                   `json:"tip,omitempty"`
}

type potentialHighness struct {
	StartDate     int64 `json:"start_date"`
	Spend         int64 `json:"spend"`
	Expire        int64 `json:"expire"` // 今晚过期钻石数（第一天的消费）
	Remain        int64 `json:"remain"`
	OpenThreshold int64 `json:"open_threshold"`
}

type myVipResp struct {
	AllPrivilegeNum     int                      `json:"all_privilege_num,omitempty"`
	Noble               *myVipInfo               `json:"noble,omitempty"`
	TrialNoble          *myVipInfo               `json:"trial_noble,omitempty"`
	Highness            *myVipInfo               `json:"highness,omitempty"`
	PotentialHighness   *potentialHighness       `json:"potential_highness,omitempty"`
	CustomerServiceInfo *vip.CustomerServiceInfo `json:"customer_service_info,omitempty"`
	Config              nobleConfig              `json:"config"`

	c     *handler.Context
	uvMap map[int]*vip.UserVip
}

type myVipInfo struct {
	Level                  int    `json:"level"`
	Title                  string `json:"title"`
	PrivilegeNum           int    `json:"privilege_num,omitempty"`
	PrivilegeRankInvisible bool   `json:"privilege_rank_invisible"`
	AvatarFrameURL         string `json:"avatar_frame_url"`
	ExpireTime             int64  `json:"expire_time,omitempty"`
	RenewalDeadline        int64  `json:"renewal_deadline,omitempty"`
	Status                 int    `json:"status"`
	Tip                    string `json:"tip,omitempty"`
	MaxTip                 string `json:"max_tip,omitempty"`

	Spend            *int64 `json:"spend,omitempty"`             // 续费上神当前累计消费钻石数
	RenewalThreshold *int64 `json:"renewal_threshold,omitempty"` // 续费上神所需累计消费总钻石数
}

// ActionMyNoble 我的贵族
/**
 * @api {get} /api/v2/noble/mynoble 我的贵族信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {number=0,1} [type=0] 贵族类型，0: 普通贵族；1: 普通贵族和上神
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample 普通贵族
 *   {
 *     "code": 0,
 *     "info": {
 *       "all_privilege_num": 18,
 *       "level": 1,
 *       "title": "练习生",
 *       "privilege_num": 1,
 *       "privilege_rank_invisible": false, // 是否具有榜单隐身的权限
 *       "avatar_frame_url": "http://example.com/test.png", // 头像框
 *       "expire_time": 1234567890, // 到期时间
 *       "renewal_deadline": 1234567890  // 续费保护期时间，和 expire_time 不会同时出现
 *       "customer_service": 112244558877, // 专属客服，没该权限不返回
 *       "config": {
 *         "invisible": true, // 进场隐身，没该权限不返回
 *         "recommend_num": 0 // 神话推荐剩余次数，没该权限不返回该字段；有权限但是推荐次数用完了会返回
 *       }
 *       "status": 1, // 贵族身份状态：1 有贵族身份，2 贵族身份处于续费保护期
 *       "tip": "" // 状态提示
 *     } // 没有贵族返回 null
 *   }
 *
 * @apiSuccessExample 普通贵族和上神
 *   {
 *     "code": 0,
 *     "info": {
 *       "all_privilege_num": 18,
 *       "noble": { // 没有贵族返回 null
 *         "level": 1,
 *         "title": "练习生",
 *         "privilege_num": 1,
 *         "privilege_rank_invisible": false, // 是否具有榜单隐身的权限
 *         "expire_time": 1234567890, // 到期时间，单位：秒
 *         "renewal_deadline": 1234567890,  // 续费保护期时间，和 expire_time 不会同时出现
 *         "avatar_frame_url": "http://example.com/test.png", // 头像框
 *         "status": 1, // 贵族身份状态：1 有贵族身份，2 贵族身份处于续费保护期
 *         "tip": "练习生到期：yyyy-mm-dd hh:mm" // 状态提示
 *       },
 *       "highness": { // 没有上神返回 null
 *         "level": 1,
 *         "title": "上神",
 *         "privilege_num": 1,
 *         "privilege_rank_invisible": false, // 是否具有榜单隐身的权限
 *         "expire_time": 1234567890, // 到期时间，单位：秒
 *         "renewal_deadline": 1234567890,  // 续费保护期时间，和 expire_time 不会同时出现
 *         "avatar_frame_url": "http://example.com/test.png", // 头像框
 *         "status": 2, // 上神身份状态：1 有上神身份，2 上神身份处于续费保护期
 *         "spend": 1, // 续费上神当前累计消费钻石数
 *         "renewal_threshold": 5000000, // 续费上神所需累计消费总钻石数
 *         "tip": "上神续费保护结束: yyyy-mm-dd hh:mm",
 *         "max_tip": "您的上神有效期已达上限，继续续费可获得特权道具，但有效期将不再延长"
 *       },
 *       "potential_highness": { // 是潜力上神用户返回，已开通上神用户不返回
 *         "start_date": 1649865600, // 第一天的消费日期时间戳（秒）
 *         "spend": 800000, // 今日不会过期的消费
 *         "expire": 1000, // 今晚过期钻石数（第一天的消费），没有过期钻石数返回 0
 *         "remain": 200000, // 还需要的钻石数
 *         "open_threshold": 1000000 // 开通上神所需钻石总数
 *       },
 *       "trial_noble": { // 体验贵族，没有体验贵族返回 null（这里不会影响普通贵族，普通贵族按照原贵族身份下发）
 *         "level": 1,
 *         "title": "大咖", // 贵族名称
 *         "expire_time": 1234567890, // 到期时间，单位：秒
 *         "status": 1, // 体验贵族状态：1 有贵族身份
 *         "tip": "大咖体验到期：yyyy-mm-dd hh:mm" // 状态提示
 *       },
 *       "customer_service": "123456789", // 专属客服，没该权限不返回，已废弃
 *       "customer_service_info": { // 专属客服信息，没该权限不返回
 *         "show_text": "QQ：123456789", // 展示文案
 *         "copy_text": "123456789" // 复制内容
 *       },
 *       "config": {
 *         "invisible": true, // 进场隐身，没该权限不返回
 *         "recommend_num": 0 // 神话推荐剩余次数，没该权限不返回该字段；有权限但是推荐次数用完了会返回
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMyNoble(c *handler.Context) (handler.ActionResponse, error) {
	typeNoble, err := c.GetDefaultParamInt("type", typeOldMyNoble)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	switch typeNoble {
	case typeOldMyNoble:
		// TODO: 兼容旧版本，web 上神上线后移除
		return oldMyNoble(c)
	case typeMyNoble:
		return actionMyVip(c)
	}
	return nil, actionerrors.ErrParams
}

func oldMyNoble(c *handler.Context) (handler.ActionResponse, error) {
	userVips, err := vip.UserVipInfos(c.UserID(), false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userVips) == 0 {
		return nil, nil
	}
	noble := userVips[vip.TypeLiveTrialNoble]
	if noble == nil || !noble.IsActive() {
		noble = userVips[vip.TypeLiveNoble]
		if noble == nil {
			return nil, nil
		}
	}
	now := goutil.TimeNow()
	if noble.IsActive() {
		return myNobleActive(c, noble), nil
	}
	return myNobleOutdated(noble, now), nil
}

func myNobleActive(c *handler.Context, uv *vip.UserVip) handler.ActionResponse {
	info := uv.Info
	if info == nil {
		return nil
	}
	resp := &myNobleInfo{AllPrivilegeNum: vip.AllPrivilegeNum}
	status, _, err := userstatus.UserGeneral(uv.UserID, c)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.Config = nobleConfig{Invisible: status.Invisible, RecommendNum: status.RecommendNum}
	resp.Tip = fmt.Sprintf("到期时间：%s", time.Unix(uv.ExpireTime, 0).Format(util.TimeFormatYMD))
	resp.Status = myVipStatusActive
	resp.Level = info.Level
	resp.Title = info.Title
	resp.PrivilegeNum = info.PrivilegeNum
	resp.PrivilegeRankInvisible = info.Privilege.IsSet(vip.PrivilegeRankInvisible)
	resp.AvatarFrame = info.AvatarFrame
	resp.ExpireTime = uv.ExpireTime
	resp.CustomerServiceInfo = info.CustomerServiceInfo
	return resp
}

func myNobleOutdated(uv *vip.UserVip, now time.Time) handler.ActionResponse {
	info := uv.Info
	if info == nil {
		return nil
	}
	renewDeadline := vip.RenewDeadline(uv.ExpireTime)
	if now.Unix() > renewDeadline {
		return nil
	}
	d := (renewDeadline-now.Unix())/(24*3600) + 1
	d = max(min(d, 5), 0)
	resp := &myNobleInfo{
		RenewalDeadline: renewDeadline,
		Level:           uv.Level,
		Title:           info.Title,
		AvatarFrame:     info.AvatarFrame,
		Status:          myVipStatusProtecting,
		Tip:             fmt.Sprintf("续费保护期还剩 %d 天", d),
	}
	return resp
}

func actionMyVip(c *handler.Context) (handler.ActionResponse, error) {
	resp := &myVipResp{AllPrivilegeNum: vip.AllPrivilegeNum, c: c}
	err := resp.findVips()
	if err != nil {
		return nil, err
	}

	resp.findUserStatus()
	if err = resp.potentialHighness(); err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *myVipResp) findVips() (err error) {
	p.uvMap, err = vip.UserVipInfos(p.c.UserID(), false, p.c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.Noble = newMyVipInfo(p.uvMap[vip.TypeLiveNoble])
	p.TrialNoble = newMyVipInfo(p.uvMap[vip.TypeLiveTrialNoble])
	p.Highness = newMyVipInfo(p.uvMap[vip.TypeLiveHighness])
	return
}

func newMyVipInfo(uv *vip.UserVip) (info *myVipInfo) {
	if uv == nil {
		return nil
	}
	if uv.IsActive() {
		info = newMyVipInfoActive(uv)
	} else {
		info = newMyVipOutdated(uv)
	}
	if uv.Type == vip.TypeLiveHighness && info != nil {
		if vip.IsUserHighnessRenewDurationMax(uv.ExpireTime) {
			info.MaxTip = "您的上神有效期已达上限，继续续费可获得特权道具，但有效期将不再延长"
		}
		info.Spend = util.NewInt64(usermeta.HighnessSpend(uv.UserID))
		info.RenewalThreshold = util.NewInt64(vip.HighnessRenewalThreshold)
	}
	return
}

func newMyVipInfoActive(uv *vip.UserVip) *myVipInfo {
	if uv.Info == nil {
		return nil
	}
	return &myVipInfo{
		Level:                  uv.Info.Level,
		Title:                  uv.Info.Title,
		PrivilegeNum:           uv.Info.PrivilegeNum,
		PrivilegeRankInvisible: uv.Info.Privilege.IsSet(vip.PrivilegeRankInvisible),
		AvatarFrameURL:         uv.Info.AvatarFrame,
		ExpireTime:             uv.ExpireTime,
		RenewalDeadline:        0,
		Status:                 myVipStatusActive,
		Tip:                    vip.BuildTip(uv.Info.Type, uv.Info.Title, uv.ExpireTime, false),
	}
}

func newMyVipOutdated(uv *vip.UserVip) *myVipInfo {
	// 若为体验贵族，且已过期，则不返回提示
	if uv.Info == nil || uv.Type == vip.TypeLiveTrialNoble {
		return nil
	}
	now := goutil.TimeNow()
	renewDeadline := vip.RenewDeadline(uv.ExpireTime)
	if now.Unix() > renewDeadline {
		return nil
	}
	return &myVipInfo{
		RenewalDeadline: renewDeadline,
		Level:           uv.Level,
		Title:           uv.Info.Title,
		AvatarFrameURL:  uv.Info.AvatarFrame,
		Status:          myVipStatusProtecting,
		Tip:             vip.BuildTip(uv.Info.Type, uv.Info.Title, renewDeadline, true),
	}
}

func (p *myVipResp) findUserStatus() {
	status, _, err := userstatus.UserGeneral(p.c.UserID(), p.c)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	p.Config = nobleConfig{Invisible: status.Invisible, RecommendNum: status.RecommendNum}

	// NOTICE: 体验贵族没有专属客服
	if highness := p.uvMap[vip.TypeLiveHighness]; highness != nil && highness.IsActive() {
		p.CustomerServiceInfo = highness.Info.CustomerServiceInfo
	} else if noble := p.uvMap[vip.TypeLiveNoble]; noble != nil && noble.IsActive() {
		p.CustomerServiceInfo = noble.Info.CustomerServiceInfo
	}
}

func (p *myVipResp) potentialHighness() error {
	// 上神有效或上神在保护期不返回
	highness := p.uvMap[vip.TypeLiveHighness]
	if highness != nil && (highness.IsActive() || highness.BeforeRenewDeadline()) {
		return nil
	}
	now := goutil.TimeNow()
	var openHighnessTime int64
	config.GetAB("open_highness", &openHighnessTime)
	if now.Unix() < openHighnessTime {
		return nil
	}
	key := keys.KeyUsersPotentialHighness0.Format()
	userID := p.c.UserID()
	exists, err := service.Redis.SIsMember(key, userID).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		return nil
	}
	expireConsumption, err := userconsumption.FindPotentialHighnessExpireConsumption(userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	consumptionInLast29Days, err := userconsumption.FindUserConsumptionInLast29Days(userID, now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.PotentialHighness = &potentialHighness{
		StartDate:     expireConsumption.StartDate,
		Expire:        expireConsumption.Expire,
		OpenThreshold: vip.HighnessOpenCoin,
	}
	spend := consumptionInLast29Days - expireConsumption.Expire + expireConsumption.Consumption
	if spend > 0 {
		p.PotentialHighness.Spend = spend
	}
	remain := vip.HighnessOpenCoin - consumptionInLast29Days - expireConsumption.Consumption
	if remain > 0 {
		p.PotentialHighness.Remain = remain
	}
	return nil
}
