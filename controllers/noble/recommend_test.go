package noble

import (
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testCancelRecommendID = 5
	RecommendingID        = 2
	CanceledRecommedID    = 4
)

func TestActionMyRecommend(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("GET", "/myrecommend", true, nil)
	r, err := ActionMyRecommend(c)
	if assert.NoError(err) {
		assert.Empty(tutil.KeyExists(tutil.MapString, r, "Datas", "pagination"))
	}
}

func TestCandidateDates(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	when := time.Date(2020, 2, 8, 0, 0, 0, 0, time.Local)
	dates, timeRange := candidateDates(when)
	shows := []string{
		"2\u200a月\u200a9\u200a日 周日",
		"2\u200a月\u200a10\u200a日 周一",
		"2\u200a月\u200a11\u200a日 周二",
		"2\u200a月\u200a12\u200a日 周三",
		"2\u200a月\u200a13\u200a日 周四",
		"2\u200a月\u200a14\u200a日 周五",
		"2\u200a月\u200a15\u200a日 周六",
	}
	require.Len(dates, 7)
	for i := range shows {
		assert.Equal(shows[i], dates[i].Show)
	}
	assert.Equal(when.AddDate(0, 0, 1), timeRange[0])
	assert.Equal(when.AddDate(0, 0, 8), timeRange[1])
	when = time.Date(2020, 2, 8, 12, 0, 0, 0, time.Local)
	dates, timeRange = candidateDates(when)
	shows = append(shows, "2\u200a月\u200a16\u200a日 周日")
	require.Len(dates, 8)
	for i := range shows {
		assert.Equal(shows[i], dates[i].Show)
	}
	assert.Equal(when.AddDate(0, 0, 1), timeRange[0])
	assert.Equal(when.AddDate(0, 0, 8), timeRange[1])
}

func TestActionRecommendCandidateTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	kc := tutil.NewKeyChecker(t, tutil.JSON)

	dates, _ := candidateDates(goutil.TimeNow())
	// 不传参数
	c := handler.NewTestContext("GET", "/candidatetime", false, nil)
	r, err := ActionRecommendCandidateTime(c)
	require.NoError(err)
	resp := r.(recommendCandidateTimeResp)
	kc.Check(resp, "candidate_dates", "selected_date", "candidate_time")
	kc.Check(resp.CandidateDates[0], "show", "date", "start_time", "recommend_time")
	require.Len(resp.CandidateDates, 8)
	assert.Equal(dates[0].Date, resp.SelectedDate)
	// 获取，时间有冲突
	date := resp.CandidateDates[1].Date
	st, _ := time.ParseInLocation(util.TimeFormatYMD, resp.CandidateDates[1].Date, time.Local)
	testNr := livenoblerecommend.NobleRecommend{
		ID:         3,
		CreatorID:  12,
		FromUserID: 12,
		RoomID:     1234567,
		StartTime:  st.Add(45 * time.Minute).Unix(),
		EndTime:    st.Add(100 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Assign(testNr).FirstOrCreate(&testNr).Error)
	c = handler.NewTestContext("GET", "/candidatetime?date="+date, false, nil)
	r, err = ActionRecommendCandidateTime(c)
	require.NoError(err)
	resp = r.(recommendCandidateTimeResp)
	require.LessOrEqual(len(resp.CandidateTime), 45) // 至少少了 00:30 至 01:30
	shows := make([]string, 0, len(resp.CandidateTime))
	for i := range resp.CandidateTime {
		shows = append(shows, resp.CandidateTime[i].Show)
	}
	assert.NotContains(shows, "00:30")
	assert.NotContains(shows, "01:00")
	assert.NotContains(shows, "01:30")
	assert.Contains(shows, "15:30") // 应该不太可能被占用
	// 不可能的时间
	date = "1970-01-01"
	c = handler.NewTestContext("GET", "/candidatetime?date="+date, false, nil)
	r, err = ActionRecommendCandidateTime(c)
	require.NoError(err)
	resp = r.(recommendCandidateTimeResp)
	assert.Equal(date, resp.SelectedDate)
	assert.Empty(resp.CandidateTime)
	date = "2050-01-01"
	c = handler.NewTestContext("GET", "/candidatetime?date="+date, false, nil)
	r, err = ActionRecommendCandidateTime(c)
	require.NoError(err)
	resp = r.(recommendCandidateTimeResp)
	assert.Equal(date, resp.SelectedDate)
	assert.Empty(resp.CandidateTime)
}

func TestWheelMakeRecommendTime(t *testing.T) {
	assert := assert.New(t)
	var w wheel
	w.makeRecommendTime()
	assert.Equal("1970-01-01 08:00 至 08:30", w.RecommendTime)
	w.StartTime = 55800
	w.makeRecommendTime()
	assert.Equal("1970-01-01 23:30 至 1970-01-02 00:00", w.RecommendTime)
}

func TestRecommendNewCheckUserAndRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := recommendNewParam{
		c: handler.NewTestContext("POST", "/recommend/new", true, nil),
	}
	param.c.User().ID = 10
	_, err := param.checkUserAndRoom()
	assert.Equal(actionerrors.ErrNoAuthority, err)
	param.c.User().ID = noble7UserID
	setRecommend := func(num int) {
		require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": noble7UserID},
			bson.M{"user_id": noble7UserID, "recommend_num": num}))
	}
	setRecommend(0)
	r, err := param.checkUserAndRoom()
	require.NoError(err)
	require.NotNil(r)
	assert.Equal("您的推荐次数已用完，续费神话或上神可获得推荐机会哦~", r.Msg)
	setRecommend(10)
	r, err = param.checkUserAndRoom()
	require.NoError(err)
	assert.Equal("该主播不存在", r.Msg)
	// 受限房间禁止神话推荐
	param.RoomID = room.TestLimitedRoomID
	r, err = param.checkUserAndRoom()
	require.NoError(err)
	assert.Equal("该直播间不存在", r.Msg)
	param.RoomID = 369892 // 房间被封禁
	banedRoom, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	once := sync.Once{}
	addUser := false
	handleUser := func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		col := liveuser.Collection()
		var err error
		if addUser {
			_, err = col.InsertOne(ctx, bson.M{"user_id": banedRoom.CreatorID})
		} else {
			_, err = col.DeleteOne(ctx, bson.M{"user_id": banedRoom.CreatorID})
		}
		require.NoError(err)
	}
	for i := 0; i < 2; i++ {
		r, err = param.checkUserAndRoom()
		require.NoError(err)
		require.NotNil(r)
		if r.Msg == "该主播不存在" {
			addUser = true
		} else {
			assert.Equal("该直播间已被封禁，暂时无法推荐！", r.Msg)
			addUser = false
		}
		once.Do(handleUser)
	}

	param.RoomID = 22489473
	var vitality int
	once = sync.Once{}
	normalRoom, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	setVitality := func() {
		require.NoError(service.DB.Table(liveaddendum.TableName()).
			Where("user_id = ?", normalRoom.CreatorID).Update("vitality", vitality).Error)
	}
	for i := 0; i < 2; i++ {
		r, err = param.checkUserAndRoom()
		require.NoError(err)
		if r == nil {
			vitality = 5
		} else {
			assert.Equal("直播间元气值过低，暂时无法推荐！", r.Msg)
			vitality = 6
		}
		once.Do(setVitality)
	}
	vitality = 12
	setVitality()
}

func TestRecommendNewCheckStartTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bod := util.BeginningOfDay(goutil.TimeNow())
	testUserID := int64(20211202)
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	c.User().ID = testUserID
	uvKey := keys.KeyNobleUserVips1.Format(testUserID)
	require.NoError(service.Redis.Set(uvKey, `{"1":{"type":1,"level":7,"user_id":20211202,"expire_time":1}}`,
		30*time.Second).Err())
	param := recommendNewParam{
		StartTime: bod.Unix() + 10,
		c:         c,
		s:         userstatus.GeneralStatus{RecommendNum: new(int)},
	}
	r, err := param.checkStartTime()
	require.NoError(err)
	require.NotNil(r)
	assert.Equal("您选择的推荐时间超出了您的贵族有效期，请重新选择！", r.Msg)

	require.NoError(service.Redis.Set(uvKey, `{"1":{"type":1,"level":7,"user_id":20211202,"expire_time":1999999999}}`,
		30*time.Second).Err())
	r, err = param.checkStartTime()
	require.NoError(err)
	require.NotNil(r)
	assert.Equal("推荐开始时间不符规范", r.Msg)
	param.StartTime = bod.AddDate(0, 0, 10).Unix()
	r, err = param.checkStartTime()
	require.NoError(err)
	require.NotNil(r)
	assert.Equal("您选择的推荐时间超出了可申请范围，请重新选择！", r.Msg)

	require.NoError(service.Redis.Set(uvKey, `{"1":{"type":1,"level":7,"user_id":20211202,"expire_time":1},`+
		`"2":{"type":2,"level":1,"user_id":20211202,"expire_time":1999999999}}`,
		30*time.Minute).Err())
	param.StartTime = bod.AddDate(0, 0, 5).Unix()
	r, err = param.checkStartTime()
	require.NoError(err)
	assert.Nil(r)
}

func TestRecommendNewParam_recommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	bod := util.BeginningOfDay(goutil.TimeNow())
	param := recommendNewParam{
		c:         handler.CreateTestContext(true),
		StartTime: bod.AddDate(0, 0, 2).Add(time.Hour).Unix(), // 使用了上面的测试数据,
		s:         userstatus.GeneralStatus{RecommendNum: new(int)},
		r:         new(room.Room),
	}

	param.s.RecommendNum = new(int)
	param.c.User().ID = noble7UserID
	key := keys.LockNobleRecommend1.Format(param.StartTime)
	require.NoError(service.Redis.Set(key, 1, time.Minute).Err())
	r, err := param.recommend()
	assert.EqualError(err, "服务器繁忙，请稍后重试")
	assert.Nil(r)
	require.NoError(service.Redis.Del(key).Err())
	r, err = param.recommend()
	require.NoError(err)
	require.NotNil(r)
	assert.Equal("该时间段已有神话推荐，请重新选择！", r.Msg)
	param.r = new(room.Room)
	param.r.RoomID = 1234789
	param.StartTime = bod.AddDate(0, 0, 4).Add(time.Hour).Unix()
	key = keys.LockNobleRecommend1.Format(param.StartTime)
	require.NoError(service.Redis.Del(key).Err())
	r, err = param.recommend()
	require.NoError(err)
	require.NotNil(r)
	assert.Equal("推荐成功", r.Msg)
	var recommend []liverecommendedelements.Model
	require.NoError(liverecommendedelements.TableSchedule(service.DB).Find(&recommend, "start_time = ?",
		param.StartTime).Error)
	assert.True(func() bool {
		for i := range recommend {
			if recommend[i].ElementID == param.r.RoomID {
				assert.Equal(util.NewBitMaskFromFlag(liverecommendedelements.AttrTagNoble), recommend[i].Attr)
				return true
			}
		}
		return false
	}())
	assert.NoError(liverecommendedelements.TableSchedule(service.DB).Delete(&recommend, "start_time = ?",
		param.StartTime).Error)
	clearRecommend(t, param.StartTime)
	ok, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	assert.Zero(ok)
}

func clearRecommend(t *testing.T, startTime int64) {
	t.Helper()
	assert := assert.New(t)
	db := service.DB.Table(livenoblerecommend.TableName()).Delete("", "start_time = ?",
		startTime)
	assert.NoError(db.Error)
	assert.NotZero(db.RowsAffected)
	assert.NoError(liverecommendedelements.TableSchedule(service.DB).
		Delete("", "start_time = ?", startTime).Error)
}

func TestRecommendNewAfterRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config.Conf.Params.NobleParams.RecommendNotifyEmail = "<EMAIL>"
	param := recommendNewParam{
		StartTime: 1582819200, // 2020-02-28 00:00:00
		c:         handler.CreateTestContext(true),
		r:         new(room.Room),
	}
	param.r.CreatorUsername = "test"
	called := false
	mockEmailFunc = func(c *handler.Context) (handler.ActionResponse, error) {
		t.Helper()
		var input map[string][]pushservice.Email
		require.NoError(c.BindJSON(&input))
		require.NotEmpty(input["emails"])
		email := input["emails"][0]
		assert.Equal("<p>神话贵族：零月</p><p>推荐主播：test (ID: 0)</p><p>房间号：0</p><p>推荐时段：2020-02-28 00:00:00 至 2020-02-28 00:30:00</p>",
			email.Body)
		assert.Equal("【猫耳FM】神话推荐申请", email.Subject)
		called = true
		return nil, actionerrors.ErrParams
	}
	defer func() {
		mockEmailFunc = func(c *handler.Context) (handler.ActionResponse, error) {
			return "", nil
		}
	}()
	param.afterRecommend()
	assert.True(called)
}

func TestFormatNow(t *testing.T) {
	assert := assert.New(t)
	now := time.Date(2020, 02, 01, 12, 25, 0, 0, time.Local)
	assert.Equal(int64(1580531400), formatNow(now).Unix())
	now = time.Date(2020, 02, 01, 12, 5, 0, 0, time.Local)
	assert.Equal(int64(1580531400), formatNow(now).Unix())
}

func TestActionRecommendCheckANDActionRecommendNew(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(recommendNewParam{}, "room_id", "start_time", "anonymous")
	kc.Check(recommendResp{}, "success", "msg", "recommend_num", "creator_username")
	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(recommendNewParam{}, "room_id", "start_time", "anonymous")

	param := handler.M{
		"room_id":    0,
		"start_time": util.BeginningOfDay(goutil.TimeNow()).Unix(),
	}
	c := handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	_, err := ActionRecommendCheck(c)
	assert.Equal(actionerrors.ErrParams, err)
	param["room_id"] = 1247
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err := ActionRecommendCheck(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*recommendResp)
	assert.Equal("该直播间不存在", resp.Msg)
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendNew(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("该直播间不存在", resp.Msg)

	param["room_id"] = 22489473
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendCheck(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("您选择的推荐时间超出了可申请范围，请重新选择！", resp.Msg)
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendNew(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("您选择的推荐时间超出了可申请范围，请重新选择！", resp.Msg)

	st := util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 4).Add(time.Hour)
	param["start_time"] = st.Unix()
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendCheck(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("参数无误，可以推荐", resp.Msg)
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendNew(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("推荐成功", resp.Msg)

	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendCheck(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("该时间段已有神话推荐，请重新选择！", resp.Msg)
	c = handler.NewTestContext("POST", "/recommend/check", true, param)
	c.User().ID = noble7UserID
	r, err = ActionRecommendNew(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*recommendResp)
	assert.Equal("该时间段已有神话推荐，请重新选择！", resp.Msg)
}

func TestActionRecommendCheck(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := recommendCancelParam{
		c: handler.NewTestContext("POST", "/recommend/cancel", true, nil),
	}
	// 测试参数错误
	err := param.check()
	assert.Equal(actionerrors.ErrParams, err)

	// 测试不存在情况
	param.RecommendID = 88
	err = param.check()
	assert.EqualError(err, "该推荐不存在！")

	// 测试 Confirm
	param.RecommendID = testCancelRecommendID
	err = param.check()
	require.NotNil(param.r)
	st := time.Unix(param.r.StartTime, 0)
	et := time.Unix(param.r.EndTime, 0)
	assert.EqualError(err, fmt.Sprintf("推荐主播：%s\n房间号：%d\n推荐时间：%s 至 %s\n"+
		"申请人：%s\n匿名状态：%s", param.r.CreatorUsername, param.r.RoomID,
		st.Format(util.TimeFormatYMDHHMM),
		et.Format(util.TimeFormatHHMM), param.r.FromUsername, "否"))

	// 测试跨天情况
	db := service.DB.Table(livenoblerecommend.TableName()).Where("id = ?",
		testCancelRecommendID).Updates(map[string]interface{}{
		"start_time": 3392850600, // 2077/7/7 10:30
		"end_time":   3392937000, // 2077/7/8 10:30
	})
	require.NoError(db.Error)
	err = param.check()
	require.NotNil(param.r)
	st = time.Unix(param.r.StartTime, 0)
	et = time.Unix(param.r.EndTime, 0)
	assert.EqualError(err, fmt.Sprintf("推荐主播：%s\n房间号：%d\n推荐时间：%s 至 %s\n"+
		"申请人：%s\n匿名状态：%s", param.r.CreatorUsername, param.r.RoomID,
		"2077-07-07 10:30",
		"2077-07-08 10:30", param.r.FromUsername, "否"))

	// 测试已经被取消情况
	param.Confirm = 1
	param.RecommendID = CanceledRecommedID
	err = param.check()
	assert.EqualError(err, "该推荐已被取消，无法重复操作！")

	// 测试过期情况
	param.RecommendID = RecommendingID
	err = param.check()
	assert.EqualError(err, "该推荐已生效，无法取消！")

	// 正常情况
	param.RecommendID = testCancelRecommendID
	err = param.check()
	require.NoError(err)
	assert.NotNil(param.r)
}

func TestActionCancelRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := handler.M{
		"recommend_id": testCancelRecommendID,
		"confirm":      1,
	}
	c := handler.NewTestContext("POST", "/recommend/cancel", true, param)

	res, err := ActionAdminCancelRecommend(c)
	require.NoError(err)
	assert.Equal("推荐取消成功", res)

	var info livenoblerecommend.WithNameUserInfo
	db := service.DB.Table(livenoblerecommend.TableName()).First(
		&info, "id = ?", testCancelRecommendID)
	require.NoError(db.Error)
	assert.Equal(livenoblerecommend.StatusCancel, info.Status)

	var m liverecommendedelements.Model
	db = service.DB.Table(liverecommendedelements.TableName()).First(&m,
		"id = ?", info.RecommededScheduleID)
	require.NoError(db.Error)
	assert.Zero(m.Sort)
	// 测试是否会加到创建者身上
	userID := 3457024
	err = userstatus.GeneralSetOne(bson.M{"user_id": userID}, bson.M{"recommend_num": 1000})
	require.NoError(err)
	param = handler.M{
		"recommend_id": 220,
		"confirm":      1,
	}
	c = handler.NewTestContext("POST", "/recommend/cancel", true, param)

	res, err = ActionAdminCancelRecommend(c)
	require.NoError(err)
	assert.Equal("推荐取消成功", res)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	col := userstatus.UserMetaCollection()
	proj := bson.M{"recommend_num": 1}
	var after userstatus.GeneralStatus
	err = col.FindOne(ctx, bson.M{"user_id": userID},
		options.FindOne().SetProjection(proj)).Decode(&after)
	require.NoError(err)
	assert.Equal(util.NewInt(1001), after.RecommendNum)
}

func TestAfterCancelRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := recommendCancelParam{
		c:           handler.NewTestContext("POST", "/recommend/cancel", true, nil),
		RecommendID: testCancelRecommendID,
		r: &livenoblerecommend.WithNameUserInfo{
			ID:              testCancelRecommendID,
			FromUserID:      int64(2),
			CreatorUsername: "小明",
		},
		startTime: "2020-10-01 10:00",
		endTime:   "10:30",
	}

	param.afterCancelRecommend()
	var m messageassign.MessageAssign
	db := service.DB.Table(messageassign.TableName()).First(&m, "recuid = ? AND title = ?",
		param.r.FromUserID, "神话推荐取消")
	require.NoError(db.Error)
	assert.Equal(`<p>亲爱的神话用户，您为主播 <strong>小明</strong> 申请的 `+
		`<strong>2020-10-01 10:00 至 10:30</strong> 的神话推荐已取消，`+
		`推荐次数已返还，如有疑问，请联系您的 VIP 客服。</p>`,
		m.Content)
}
