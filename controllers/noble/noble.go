package noble

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// Handler handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "noble",
		Actions: map[string]*handler.Action{
			"list":      handler.NewAction(handler.GET, ActionList, true),
			"mynoble":   handler.NewAction(handler.GET, ActionMyNoble, true),
			"setconfig": handler.NewAction(handler.POST, ActionSetConfig, true),

			"rankinvisible/list": handler.NewAction(handler.GET, ActionRankInvisibleList, true),
			"rankinvisible/set":  handler.NewAction(handler.POST, ActionRankInvisibleSet, true),

			"myrecommend":             handler.NewAction(handler.GET, ActionMyRecommend, true),
			"recommend/candidatetime": handler.NewAction(handler.GET, ActionRecommendCandidateTime, true),
			"recommend/check":         handler.NewAction(handler.POST, ActionRecommendCheck, true),
			"recommend/new":           handler.NewAction(handler.POST, ActionRecommendNew, true),
		},
	}
}
