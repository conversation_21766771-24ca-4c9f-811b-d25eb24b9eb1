package noble

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionSetConfig(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/setconfig",
		tutil.ToRequestBody(map[string]int{"invisible": 2}))
	c.C.Request.Header.Set("Content-Type", "application/json")
	_, err := ActionSetConfig(c)
	assert.Equal(actionerrors.ErrParams, err, "测试传错误的 invisible")
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/setconfig", nil)
	c.C.Request.Header.Set("Content-Type", "application/json")
	_, err = ActionSetConfig(c)
	assert.Equal(actionerrors.ErrParams, err, "测试不传 invisible")

	sess, err := service.SSO.LoginByThirdOpenID(sso.ThirdTypeWeibo, "testweibouid",
		0, "", testUA, "")
	require.NoError(err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/setconfig",
		tutil.ToRequestBody(map[string]int{"invisible": 0}))
	c.C.Request.Header.Set("Content-Type", "application/json")
	c.C.Request.AddCookie(&http.Cookie{Name: "token", Value: sess.Token})
	key := keys.KeyNobleUserVips1.Format(12)
	require.NoError(service.Redis.Set(key, "null", 30*time.Second).Err())
	defer func() {
		assert.NoError(service.Redis.Del(key).Err())
	}()
	_, err = ActionSetConfig(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// TODO: 设置成功待补完
}

func TestSetInvisible(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(setInvisible(123456, 1))
	var param struct {
		Invisible bool `bson:"invisible"`
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_meta")
	filter := bson.M{"user_id": 123456}
	require.NoError(collection.FindOne(ctx, filter).Decode(&param))
	assert.True(param.Invisible)

	_, err := collection.DeleteMany(ctx, filter)
	assert.NoError(err)
}
