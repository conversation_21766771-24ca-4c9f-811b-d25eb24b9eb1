package noble

import (
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/recovery"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const noble7UserID int64 = 3457114 // 神话测试账号

var testUA string

func TestMain(m *testing.M) {
	config.InitTest()
	logger.InitTestLog()

	startMockSendEmailRPCServer()

	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)
	testUA = tutil.PkgPath()

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler()
	assert.Equal("noble", h.Name)
	assert.Empty(tutil.KeyExists(tutil.Actions, h, "list", "mynoble", "setconfig",
		"rankinvisible/list", "rankinvisible/set",
		"myrecommend", "recommend/new", "recommend/check", "recommend/candidatetime",
	))
}

var (
	mockRouter                       = gin.New()
	mockEmailFunc handler.ActionFunc = func(c *handler.Context) (handler.ActionResponse, error) {
		return "", nil
	} // NOTICE: 修改后记得还原
)

// TODO: tutil 需要一个 pushservice 的 mock
func startMockSendEmailRPCServer() {
	mockRouter.Use(recovery.Middleware())
	config.Conf.Service.PushService = pushservice.Config{
		URL: "http://127.0.0.1:9098/rpc/",
		Key: "testkey",
	}
	action := func(c *handler.Context) (handler.ActionResponse, error) {
		return mockEmailFunc(c)
	}
	h := &handler.Handler{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.Middleware("testkey"),
		},
		Actions: map[string]*handler.Action{
			"/api/email": handler.NewAction(handler.POST, action, false),
		},
	}
	tutil.RunMockServer(mockRouter, 9098, h)
}
