package noble

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionRankInvisibleList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "rankinvisible/list", true, nil)
	c.User().ID = 10
	_, err := ActionRankInvisibleList(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 查询不到数据
	// 开启练习生所有贵族权限
	require.NoError(service.DB.Table(vip.Info{}.TableName()).Where("level = 1").Update("privilege", 63).Error)
	service.Cache5Min.Flush()
	c = handler.NewTestContext(http.MethodGet, "rrankinvisible/list?p=100", true, nil)
	c.User().ID = noble7UserID
	r, err := ActionRankInvisibleList(c)
	if assert.NoError(err) {
		resp := r.(handler.M)
		data := resp["Datas"]
		assert.NotNil(data)
		assert.Empty(data)
	}
	// 查询到数据
	if room, err := room.Find(22489473); assert.NoError(err) {
		err = userstatus.EnableRankInvisible(noble7UserID, room.RoomID, room.OID)
		require.NoError(err)
	}
	c = handler.NewTestContext(http.MethodGet, "rankinvisible/list", true, nil)
	c.User().ID = noble7UserID
	r, err = ActionRankInvisibleList(c)
	if assert.NoError(err) {
		resp := r.(handler.M)
		data := resp["Datas"]
		assert.Len(data, 1)
	}
}

func TestActionRankInvisibleSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("checkError", func(t *testing.T) {
		c := handler.NewTestContext("POST", "rankinvisible/set", true, nil)
		c.User().ID = 10
		_, err := ActionRankInvisibleSet(c)
		assert.Equal(actionerrors.ErrNoAuthority, err)

		param := map[string]interface{}{
			"enable":   1,
			"room_ids": []int64{1},
		}
		c = handler.NewTestContext("POST", "rankinvisible/set", true, tutil.ToRequestBody(param))
		c.User().ID = noble7UserID
		_, err = ActionRankInvisibleSet(c)
		assert.Equal(actionerrors.ErrParams, err)

		param = map[string]interface{}{
			"enable": 0,
		}
		c = handler.NewTestContext("POST", "rankinvisible/set", true, tutil.ToRequestBody(param))
		c.User().ID = noble7UserID
		_, err = ActionRankInvisibleSet(c)
		assert.Equal(actionerrors.ErrParams, err)
	})

	testRoom, err := room.Find(3192516)
	require.NoError(err)
	t.Run("enable", func(t *testing.T) {
		param := map[string]interface{}{
			"enable":  1,
			"room_id": testRoom.RoomID + 1,
		}
		c := handler.NewTestContext(http.MethodPost, "rankinvisible/set", true, param)
		c.User().ID = noble7UserID
		_, err := ActionRankInvisibleSet(c)
		assert.Equal(actionerrors.ErrCannotFindRoom, err)

		param = map[string]interface{}{
			"enable":  1,
			"room_id": testRoom.RoomID,
		}
		c = handler.NewTestContext(http.MethodPost, "rankinvisible/set", true, param)
		_, err = ActionRankInvisibleSet(c)
		require.NoError(err)
		l, _, err := userstatus.ListRankInvisibleRoomID(noble7UserID, 1, 20)
		if assert.NoError(err) {
			assert.Contains(l, testRoom.RoomID)
		}
	})
	t.Run("disable", func(t *testing.T) {
		param := map[string]interface{}{
			"enable":   0,
			"room_id":  testRoom.RoomID,
			"room_ids": []int64{1},
		}
		c := handler.NewTestContext(http.MethodPost, "rankinvisible/set", true, param)
		c.User().ID = noble7UserID
		_, err := ActionRankInvisibleSet(c)
		assert.NoError(err)
		l, _, err := userstatus.ListRankInvisibleRoomID(noble7UserID, 1, 20)
		if assert.NoError(err) {
			assert.NotContains(l, testRoom.RoomID)
		}
	})
}
