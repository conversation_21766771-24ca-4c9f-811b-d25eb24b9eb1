package noble

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

var (
	testUserID  int64 = 10
	testHornNum int64 = 10
)

func TestActionAdminHornAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = usermeta.Collection().InsertOne(ctx, bson.M{"user_id": testUserID, "noble_horn_num": testHornNum})
	require.NoError(err)
	service.Cache5Min.Flush()

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "/api/v2/admin/noble/horn/add", true, handler.M{
		"user_id":  0,
		"horn_num": testHornNum,
		"confirm":  0,
	})
	_, err = ActionAdminHornAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 参数错误
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/noble/horn/add", true, handler.M{
		"user_id":  testUserID,
		"horn_num": 0,
		"confirm":  0,
	})
	_, err = ActionAdminHornAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试弹窗内容
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/noble/horn/add", true, handler.M{
		"user_id":  testUserID,
		"horn_num": testUserID,
		"confirm":  0,
	})
	_, err = ActionAdminHornAdd(c)
	assert.EqualError(err, "确认为用户 bless (ID: 10) 补发 10 个喇叭吗？")

	// 测试补发喇叭成功
	c = handler.NewTestContext(http.MethodPost, "/api/v2/admin/noble/horn/add", true, handler.M{
		"user_id":  testUserID,
		"horn_num": testUserID,
		"confirm":  1,
	})
	_, err = ActionAdminHornAdd(c)
	require.NoError(err)
	// 断言喇叭数量
	var u usermeta.UserMeta
	require.NoError(usermeta.Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&u))
	assert.Equal(int64(20), u.NobleHornNum)
}

func TestNewAdminHornAddParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id":  0,
		"horn_num": testHornNum,
	})
	_, err := newAdminHornAddParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试成功
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"user_id":  testUserID,
		"horn_num": testHornNum,
	})
	param, err := newAdminHornAddParam(c)
	require.NoError(err)
	assert.Equal(testUserID, param.UserID)
	assert.Equal(testHornNum, param.HornNum)
}

func TestHornAddParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := hornAddParam{
		UserID:  0,
		HornNum: testHornNum,
		Confirm: 0,
	}

	// 测试用户不存在
	err := param.check()
	assert.Equal(actionerrors.ErrCannotFindUser, err)

	param.UserID = 10
	//  测试弹窗内容
	assert.EqualError(param.check(), "确认为用户 bless (ID: 10) 补发 10 个喇叭吗？")

	// 测试不弹窗
	param.Confirm = 1
	require.NoError(param.check())
}

func TestHornAddParam_add(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = usermeta.Collection().InsertOne(ctx, bson.M{"user_id": testUserID, "noble_horn_num": testHornNum})
	require.NoError(err)
	service.Cache5Min.Flush()

	// 测试补发喇叭成功
	param := hornAddParam{
		UserID:  testUserID,
		HornNum: testHornNum,
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
		u:       &liveuser.Simple{UID: testUserID, Username: "bless"},
	}
	require.NoError(param.add())
	// 断言喇叭数量
	var u usermeta.UserMeta
	require.NoError(usermeta.Collection().FindOne(ctx, bson.M{"user_id": testUserID}).Decode(&u))
	assert.Equal(int64(20), u.NobleHornNum)
}
