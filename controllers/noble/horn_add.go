package noble

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type hornAddParam struct {
	UserID  int64 `form:"user_id" json:"user_id"`
	HornNum int64 `form:"horn_num" json:"horn_num"`
	Confirm int   `form:"confirm" json:"confirm"`

	c *handler.Context
	u *liveuser.Simple
}

// ActionAdminHornAdd 补发喇叭
/**
 * @api {post} /api/v2/admin/noble/horn/add 补发喇叭
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/noble
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} horn_num 补发的喇叭数量
 * @apiParam {number=0,1} [confirm] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "补发成功"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确认为用户 余额宝 (ID: 12345) 用户补发 10 个喇叭吗？"
 *     }
 *   }
 *
 * @apiErrorExample 补发失败的情况:
 *   {
 *     "code": 100010002,
 *     "info": "参数错误"
 *   }
 */
func ActionAdminHornAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newAdminHornAddParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.add()
	if err != nil {
		return nil, err
	}
	return "补发成功", nil
}

func newAdminHornAddParam(c *handler.Context) (*hornAddParam, error) {
	param := new(hornAddParam)
	err := c.Bind(&param)
	if err != nil || param.UserID <= 0 || param.HornNum <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param *hornAddParam) check() error {
	var err error
	param.u, err = liveuser.FindOneSimple(bson.M{"user_id": param.UserID}, nil)
	if err != nil {
		return err
	}
	if param.u == nil {
		return actionerrors.ErrCannotFindUser
	}
	if param.Confirm == 0 {
		msg := fmt.Sprintf("确认为用户 %s (ID: %d) 补发 %d 个喇叭吗？", param.u.Username, param.UserID, param.HornNum)
		return actionerrors.ErrConfirmRequired(msg, 1)
	}
	return nil
}

func (param *hornAddParam) add() error {
	err := usermeta.IncrNobleHornNum(param.UserID, param.HornNum)
	if err != nil {
		return err
	}
	logbox := userapi.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("为用户 %s (ID: %d) 补发 %d 个喇叭", param.u.Username, param.UserID, param.HornNum)
	logbox.AddAdminLog(intro, userapi.CatalogManageNobleHorn)
	err = logbox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
