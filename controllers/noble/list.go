package noble

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	buyStatusDisable = iota - 1
	buyStatusRenewal
	buyStatusRegistration
	buyStatusLevelUp
)

// 获取贵族信息类型
const (
	listTypeLiveNoble = iota
	listTypeAllVip
)

type vipInfo struct {
	Type               int    `json:"type"`
	Level              int    `json:"level"`
	Title              string `json:"title"`
	PrivilegeNum       int    `json:"privilege_num"`
	IconURL            string `json:"icon_url"`
	RegistrationPrice  int    `json:"registration_price,omitempty"`
	RenewalPrice       int    `json:"renewal_price,omitempty"`
	RegistrationRebate int    `json:"registration_rebate,omitempty"`
	RenewalRebate      int    `json:"renewal_rebate,omitempty"`
	Status             *int   `json:"status,omitempty"`
	Tip                string `json:"tip,omitempty"`
}

type listMyNoble struct {
	Level     int   `json:"level"`
	RenewLife int64 `json:"renew_life,omitempty"`
}

type listNobleVipResp struct {
	MyNoble         *listMyNoble `json:"my_noble,omitempty"`
	MyHighness      *listMyNoble `json:"my_highness,omitempty"`
	AllPrivilegeNum int          `json:"all_privilege_num"`
	Data            []vipInfo    `json:"data"`

	// 兼容旧版本，后续需要移除
	MyNobleOld *listMyNoble `json:"mynoble,omitempty"`
	DataOld    []vipInfo    `json:"Datas,omitempty"`
}

// ActionList 列出贵族信息
/**
 * @api {get} /api/v2/noble/list 贵族列表
 * @apiDescription 客户端贵族订单确认页不需要返回上神信息，使用默认参数 type 即可
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {number=0,1} [type=0] 贵族类型，0: 普通贵族；1: 普通贵族和上神
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample 普通贵族
 *   {
 *     "code": 0,
 *     "info": {
 *       "my_noble": {
 *         "level": 1,
 *         "renew_life": 1  // 续费保护期还剩多少天，只有贵族在保护期才返回
 *       }, // 没有贵族不显示
 *       "all_privilege_num": 20,
 *       "data": [
 *         {
 *           "level": 1,
 *           "title": "练习生",
 *           "privilege_num": 1,
 *           "icon_url": "http://example.com/test.png"
 *           "registration_price": 120,  // 开通价（钻石）
 *           "renewal_price": 60,  // 续订价（钻石）
 *           "registration_rebate": 90,  // 开通返钻石
 *           "renewal_rebate": 45,  // 续订返钻石
 *           "status": -1, // 购买状态，-1: 不可购买, 0: 可续费, 1: 可开通, 2: 可升级
 *           "tip": "开通至 yyyy-mm-dd hh:mm" // 开通或续费提示，续费提示："续费后有效期延长至 yyyy-mm-dd hh:mm"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiSuccessExample 上神和贵族
 *   {
 *     "code": 0,
 *     "info": {
 *       "my_noble": {
 *         "level": 1,
 *         "renew_life": 1  // 续费保护期还剩多少天，只有贵族在保护期才返回
 *       }, // 没有贵族不显示
 *       "my_highness": {
 *         "level": 1,
 *         "renew_life": 1  // 续费保护期还剩多少天，只有上神在保护期才返回
 *       }, // 没有上神不显示
 *       "all_privilege_num": 20,
 *       "data": [
 *         {
 *           "type": 2, // 上神贵族
 *           "level": 1,
 *           "title": "上神",
 *           "privilege_num": 20,
 *           "icon_url": "http://example.com/test.png"
 *         },
 *         {
 *           "type": 1, // 普通贵族
 *           "level": 1,
 *           "title": "练习生",
 *           "privilege_num": 1,
 *           "icon_url": "http://example.com/test.png",
 *           "registration_price": 120,  // 开通价（钻石）
 *           "renewal_price": 60,  // 续订价（钻石）
 *           "registration_rebate": 90,  // 开通返钻石
 *           "renewal_rebate": 45,  // 续订返钻石
 *           "status": -1, // 购买状态，-1: 不可购买, 0: 可续费, 1: 可开通, 2: 可升级
 *           "tip": "开通至 yyyy-mm-dd hh:mm" // 开通或续费提示，续费提示："续费后有效期延长至 yyyy-mm-dd hh:mm"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionList(c *handler.Context) (handler.ActionResponse, error) {
	listType, err := c.GetDefaultParamInt("type", listTypeLiveNoble)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	switch listType {
	case listTypeLiveNoble:
		return listNormalVip(c)
	case listTypeAllVip:
		return listAllVip(c)
	}
	return nil, actionerrors.ErrParams
}

// TODO: 这里需要改名，实际没有查询体验贵族
func listAllVip(c *handler.Context) (handler.ActionResponse, error) {
	var userVips map[int]*vip.UserVip
	var err error
	if c.UserID() != 0 {
		userVips, err = vip.UserVipInfos(c.UserID(), true, c)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	nobleVips, err := vip.ListByVipType(vip.TypeLiveNoble)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	highnessVips, err := vip.ListByVipType(vip.TypeLiveHighness)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	list := append(highnessVips, nobleVips...)
	now := goutil.TimeNow().Unix()
	resp := &listNobleVipResp{AllPrivilegeNum: vip.AllPrivilegeNum}

	buildMyVip := func(vu *vip.UserVip) *listMyNoble {
		if vu == nil {
			return nil
		}
		if now < vu.ExpireTime {
			return &listMyNoble{Level: vu.Level}
		}
		renewDeadline := vip.RenewDeadline(vu.ExpireTime)
		if now < renewDeadline {
			return &listMyNoble{Level: vu.Level, RenewLife: vip.DayDuration(renewDeadline, now)}
		}
		return nil
	}

	liveNoble := userVips[vip.TypeLiveNoble]
	if userVips != nil {
		resp.MyNoble = buildMyVip(liveNoble)
		resp.MyHighness = buildMyVip(userVips[vip.TypeLiveHighness])
	}

	resp.Data = make([]vipInfo, len(list))
	for i := 0; i < len(list); i++ {
		resp.Data[i].Type = list[i].Type
		resp.Data[i].Level = list[i].Level
		resp.Data[i].Title = list[i].Title
		resp.Data[i].PrivilegeNum = list[i].PrivilegeNum
		resp.Data[i].IconURL = list[i].Icon
		switch list[i].Type {
		case vip.TypeLiveNoble:
			resp.Data[i].RegistrationPrice = list[i].RegistrationPrice
			resp.Data[i].RenewalPrice = list[i].RenewalPrice
			resp.Data[i].RegistrationRebate = list[i].RegistrationRebate
			resp.Data[i].setRenewalRebate(list[i], liveNoble)
			resp.Data[i].setStatus(list[i], userVips)
			resp.Data[i].setTip(list[i], userVips)
		}
	}
	// 按照等级从高到低排序
	sort.Slice(resp.Data, func(i, j int) bool {
		if resp.Data[i].Type != resp.Data[j].Type {
			return resp.Data[i].Type > resp.Data[j].Type
		}
		return resp.Data[i].Level > resp.Data[j].Level
	})
	return resp, nil
}

// 普通贵族
func listNormalVip(c *handler.Context) (handler.ActionResponse, error) {
	var userVips map[int]*vip.UserVip
	var err error
	if c.UserID() != 0 {
		userVips, err = vip.UserVipInfos(c.UserID(), true, c)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	list, err := vip.ListByVipType(vip.TypeLiveNoble)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()
	resp := &listNobleVipResp{AllPrivilegeNum: vip.AllPrivilegeNum}
	liveNoble := userVips[vip.TypeLiveNoble]
	if liveNoble != nil {
		renewDeadline := vip.RenewDeadline(liveNoble.ExpireTime)
		switch {
		case now.Unix() < liveNoble.ExpireTime:
			resp.MyNoble = &listMyNoble{
				Level: liveNoble.Level,
			}
		case now.Unix() < renewDeadline:
			resp.MyNoble = &listMyNoble{
				Level:     liveNoble.Level,
				RenewLife: vip.DayDuration(renewDeadline, now.Unix()),
			}
		}
	}
	resp.Data = make([]vipInfo, len(list))
	var isIOS bool
	if e := c.Equip(); e != nil && e.OS == goutil.IOS {
		isIOS = true
	}
	for i := 0; i < len(list); i++ {
		resp.Data[i].Type = list[i].Type
		resp.Data[i].Level = list[i].Level
		resp.Data[i].Title = list[i].Title
		resp.Data[i].PrivilegeNum = list[i].PrivilegeNum
		resp.Data[i].IconURL = list[i].Icon
		// TODO: 移除 iOS icon 的特殊处理
		if isIOS && strings.HasSuffix(resp.Data[i].IconURL, "webp") {
			size := len(resp.Data[i].IconURL)
			resp.Data[i].IconURL = resp.Data[i].IconURL[0:size-4] + "gif"
		}
		resp.Data[i].RegistrationPrice = list[i].RegistrationPrice
		resp.Data[i].RenewalPrice = list[i].RenewalPrice
		resp.Data[i].RegistrationRebate = list[i].RegistrationRebate
		resp.Data[i].setRenewalRebate(list[i], liveNoble)
		resp.Data[i].setStatus(list[i], userVips)
		resp.Data[i].setTip(list[i], userVips)
	}
	// 按照等级从高到低排序
	sort.Slice(resp.Data, func(i, j int) bool {
		return resp.Data[i].Level > resp.Data[j].Level
	})
	// 兼容旧版本，后续需要移除
	// 后端接口先于 web 上线，web 旧字段都需要下发
	if e := c.Equip(); !e.FromApp || e.IsAppOlderThan("4.7.5", "5.6.3") {
		resp.DataOld = resp.Data
		resp.MyNobleOld = resp.MyNoble
	}
	return resp, nil
}

func (info *vipInfo) setStatus(vipInfo *vip.Info, uvMap map[int]*vip.UserVip) {
	if !canTrialNobleBuyNoble(vipInfo, uvMap) {
		info.Status = util.NewInt(buyStatusDisable)
		return
	}
	var (
		uv      = uvMap[vip.TypeLiveNoble]
		nowUnix = goutil.TimeNow().Unix()
	)
	if uv == nil {
		// 没有贵族信息
		info.Status = util.NewInt(buyStatusRegistration)
		return
	}
	if uv.ExpireTime >= nowUnix {
		// 未过期的贵族
		switch {
		case vipInfo.Level < uv.Level:
			info.Status = util.NewInt(buyStatusDisable)
		case vipInfo.Level == uv.Level:
			info.Status = util.NewInt(buyStatusRenewal)
		case vipInfo.Level > uv.Level:
			info.Status = util.NewInt(buyStatusLevelUp)
		}
		return
	}
	renewalDeadline := vip.RenewDeadline(uv.ExpireTime)
	if nowUnix <= renewalDeadline && vipInfo.Level == uv.Level {
		// 续费保护期内的贵族
		info.Status = util.NewInt(buyStatusRenewal)
		return
	}
	info.Status = util.NewInt(buyStatusRegistration)
}

// setRenewalRebate 设置续费返钻
// NOTICE: 不检查 vipInfo 和 info 等级是否匹配，只检查 info 和 uv 是否匹配
func (info *vipInfo) setRenewalRebate(vipInfo *vip.Info, uv *vip.UserVip) {
	if uv != nil && info.Level == uv.Level && uv.RenewalRebate != 0 {
		info.RenewalRebate = uv.RenewalRebate
	} else {
		info.RenewalRebate = vipInfo.RenewalRebate
	}
}

func (info *vipInfo) setTip(vipInfo *vip.Info, uvMap map[int]*vip.UserVip) {
	// 仅普通贵族和体验贵族需要提示
	if vipInfo.Type != vip.TypeLiveNoble && vipInfo.Type != vip.TypeLiveTrialNoble {
		return
	}
	if !canTrialNobleBuyNoble(vipInfo, uvMap) {
		return
	}

	// 有体验贵族的情况下，体验贵族的等级一定大于等于普通贵族等级
	var (
		uv  = uvMap[vip.TypeLiveNoble]
		uvt = uvMap[vip.TypeLiveTrialNoble]
		now = goutil.TimeNow()
	)
	switch {
	case uv == nil || (!uv.IsActive() && !uv.BeforeRenewDeadline()) || uv.VipID != vipInfo.VipID:
		// 没贵族 || (贵族过期 && 不在续费保护期内) || 贵族等级不匹配
		info.Tip = fmt.Sprintf("开通至 %s", vip.BuyNobleExpireFormatTimeTip(now, vipInfo, uvt, uvt != nil && uvt.IsActive() && uvt.Level == vipInfo.Level))
	case uv.VipID == vipInfo.VipID && uv.IsActive():
		// 在有效期内
		info.Tip = fmt.Sprintf("续费至 %s", vip.BuyNobleExpireFormatTimeTip(time.Unix(uv.ExpireTime, 0), vipInfo, uvt, false))
	case uv.VipID == vipInfo.VipID && uv.BeforeRenewDeadline():
		// 在续费保护期内
		info.Tip = fmt.Sprintf("续费至 %s", vip.BuyNobleExpireFormatTimeTip(now, vipInfo, uvt, true))
	}
}

// canTrialNobleBuyNoble 体验贵族是否可以开通贵族
func canTrialNobleBuyNoble(vipInfo *vip.Info, uvMap map[int]*vip.UserVip) bool {
	var (
		uv  = uvMap[vip.TypeLiveNoble]
		uvt = uvMap[vip.TypeLiveTrialNoble]
	)
	// 若目标贵族可以续费 && 与目标等级一致，可购买
	if uv != nil && (uv.IsActive() || uv.BeforeRenewDeadline()) && uv.Level == vipInfo.Level {
		return true
	}
	// 无体验贵族 || 体验贵族失效 || 体验贵族等级小于等于目标贵族等级，可购买
	if uvt == nil || !uvt.IsActive() || uvt.Level <= vipInfo.Level {
		return true
	}
	return false
}
