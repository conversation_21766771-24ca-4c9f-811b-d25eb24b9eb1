package noble

import (
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionListTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(vipInfo{}, "type", "level", "title", "privilege_num", "icon_url", "registration_price", "renewal_price",
		"registration_rebate", "renewal_rebate", "status", "tip")
	kc.Check(listMyNoble{}, "level", "renew_life")
	kc.Check(listNobleVipResp{}, "my_noble", "my_highness", "all_privilege_num", "data",
		"mynoble", "Datas")
}

func TestActionList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(vip.URLVipConfig, func(input any) (output any, err error) {
		return &vip.ConfigResp{
			EnableNewVipBalanceTime: 1701187200,
			LiveNobleProtectDays:    10,
		}, nil
	})
	defer cancel()

	c := handler.NewTestContext(http.MethodGet, "/list", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "9.9.9"
	c.Equip().FromApp = true
	r, err := ActionList(c)
	require.NoError(err)
	resp := r.(*listNobleVipResp)
	require.Len(resp.Data, 7)
	assert.True(strings.HasSuffix(resp.Data[0].IconURL, ".gif"))
	require.Equal([]int{7, 6, 1}, []int{resp.Data[0].Level, resp.Data[1].Level, resp.Data[6].Level})
	assert.Nil(resp.MyNobleOld)
	assert.Empty(resp.DataOld)

	c = handler.NewTestContext(http.MethodGet, "/list", false, nil)
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "1.1.1"
	c.Equip().FromApp = true
	r, err = ActionList(c)
	require.NoError(err)
	resp = r.(*listNobleVipResp)
	assert.NotEmpty(resp.DataOld)

	c = handler.NewTestContext(http.MethodGet, "/list", false, nil)
	r, err = ActionList(c)
	require.NoError(err)
	resp = r.(*listNobleVipResp)
	assert.NotEmpty(resp.DataOld)
	assert.Equal(buyStatusRegistration, *resp.DataOld[0].Status)
	assert.NotZero(resp.DataOld[0].RegistrationRebate)

	c = handler.NewTestContext(http.MethodGet, "/list?type=1", false, nil)
	r, err = ActionList(c)
	require.NoError(err)
	resp = r.(*listNobleVipResp)
	require.Len(resp.Data, 8)
	require.Equal([]int{1, 2, 1, 1}, []int{resp.Data[0].Level, resp.Data[0].Type, resp.Data[7].Level, resp.Data[7].Type})

	c = handler.NewTestContext(http.MethodGet, "/list?type=1", false, nil)
	r, err = ActionList(c)
	require.NoError(err)
	resp = r.(*listNobleVipResp)
	require.Len(resp.Data, 8)
	for _, d := range resp.Data {
		if d.Type == vip.TypeLiveHighness {
			continue
		}
		assert.Contains(d.Tip, "开通至")
	}

	cancel = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					VipID:      1,
					Type:       vip.TypeLiveNoble,
					Level:      1,
					ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
				},
			},
		}, nil
	})
	defer cancel()
	c = handler.NewTestContext(http.MethodGet, "/list?type=1", true, nil)
	c.User().ID = 1423432413
	r, err = ActionList(c)
	require.NoError(err)
	resp = r.(*listNobleVipResp)
	require.Len(resp.Data, 8)
	for _, d := range resp.Data {
		if d.Type == vip.TypeLiveHighness {
			continue
		}
		if d.Level == 1 {
			assert.Contains(d.Tip, "续费至")
		} else {
			assert.Contains(d.Tip, "开通至")
		}
	}
}

func TestInfoSetStatus(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()

	t.Run("没有贵族", func(t *testing.T) {
		info := &vipInfo{
			Level: 5,
		}
		vipList := &vip.Info{
			Level: info.Level, Type: vip.TypeLiveNoble,
		}
		info.setStatus(vipList, map[int]*vip.UserVip{})
		assert.Equal(buyStatusRegistration, *info.Status)
	})

	t.Run("未过期贵族", func(t *testing.T) {
		info := &vipInfo{
			Level: 5,
		}
		vipList := &vip.Info{
			Level: info.Level, Type: vip.TypeLiveNoble,
		}
		uv := &vip.UserVip{
			ExpireTime: now.Unix(),
		}
		uvMap := map[int]*vip.UserVip{
			vip.TypeLiveNoble: uv,
			vip.TypeLiveTrialNoble: {
				Level:      info.Level,
				ExpireTime: now.Unix(),
			},
		}
		levels := []int{info.Level + 1, info.Level, info.Level - 1}
		statuses := []int{buyStatusDisable, buyStatusRenewal, buyStatusLevelUp}
		for i := 0; i < len(levels); i++ {
			uv.Level = levels[i]
			info.setStatus(vipList, uvMap)
			assert.Equal(statuses[i], *info.Status)
		}
	})

	t.Run("过期贵族", func(t *testing.T) {
		info := &vipInfo{
			Level: 5,
		}
		vipList := &vip.Info{
			Level: info.Level, Type: vip.TypeLiveNoble,
		}
		uv := &vip.UserVip{
			Level:      info.Level,
			Type:       vip.TypeLiveNoble,
			ExpireTime: now.Add(-time.Hour).Unix(), // 续费保护期
		}
		uvMap := map[int]*vip.UserVip{
			vip.TypeLiveNoble:      uv,
			vip.TypeLiveTrialNoble: nil,
		}
		info.setStatus(vipList, uvMap)
		assert.Equal(buyStatusRenewal, *info.Status)

		uv.Level = info.Level + 1
		info.setStatus(vipList, uvMap)
		assert.Equal(buyStatusRegistration, *info.Status)
	})

	t.Run("体验贵族等级大于目标贵族", func(t *testing.T) {
		info := &vipInfo{
			Level: 5,
		}
		vipList := &vip.Info{
			Level: info.Level, Type: vip.TypeLiveNoble,
		}
		uvMap := map[int]*vip.UserVip{
			vip.TypeLiveNoble: nil,
			vip.TypeLiveTrialNoble: {
				Level:      info.Level + 1,
				ExpireTime: now.Add(time.Hour).Unix(),
			},
		}
		info.setStatus(vipList, uvMap)
		assert.Equal(buyStatusDisable, *info.Status)
	})

	t.Run("原贵族生效中，体验贵族等级大于目标贵族等级，可以续费", func(t *testing.T) {
		info := &vipInfo{
			Level: 4,
		}
		vipList := &vip.Info{
			Level: info.Level, Type: vip.TypeLiveNoble,
		}
		uvMap := map[int]*vip.UserVip{
			vip.TypeLiveNoble: {
				Level:      info.Level,
				ExpireTime: now.Add(time.Hour).Unix(),
			},
			vip.TypeLiveTrialNoble: {
				Level:      info.Level + 1,
				ExpireTime: now.Add(time.Hour).Unix(),
			},
		}
		info.setStatus(vipList, uvMap)
		assert.Equal(buyStatusRenewal, *info.Status)
	})
}

func TestVipInfoSetRenewalRebate(t *testing.T) {
	assert := assert.New(t)

	info := &vipInfo{Level: 1}
	vipInfo := &vip.Info{RenewalRebate: 100}
	info.setRenewalRebate(vipInfo, nil)
	assert.Equal(100, info.RenewalRebate)
	uv := &vip.UserVip{Level: 2, RenewalRebate: 120}
	info.setRenewalRebate(vipInfo, uv)
	assert.Equal(100, info.RenewalRebate)
	info.Level = 2
	info.setRenewalRebate(vipInfo, uv)
	assert.Equal(120, info.RenewalRebate)
}

func TestVipInfo_setTip(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 2, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(time.Now)
	cancel := mrpc.SetMock(vip.URLVipConfig, func(input any) (output any, err error) {
		return &vip.ConfigResp{
			EnableNewVipBalanceTime: 1701187200,
			LiveNobleProtectDays:    10,
		}, nil
	})
	defer cancel()

	info := vipInfo{}
	uvMap := map[int]*vip.UserVip{
		vip.TypeLiveNoble:      nil,
		vip.TypeLiveTrialNoble: {Level: 1, Type: vip.TypeLiveTrialNoble, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
	}
	info.setTip(&vip.Info{Level: 1, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("开通至 2020-02-02 00:00", info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {VipID: 1, Level: 2, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
		vip.TypeLiveTrialNoble: {VipID: 1, Level: 2, Type: vip.TypeLiveTrialNoble, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
	}
	info.setTip(&vip.Info{VipID: 1, Level: 2, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("续费至 2020-02-02 00:00", info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Level: 2, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(time.Hour).Unix()},
		vip.TypeLiveTrialNoble: nil,
	}
	info.setTip(&vip.Info{Level: 2, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("续费至 2020-02-02 00:00", info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Level: 3, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix()},
		vip.TypeLiveTrialNoble: {Level: 3, Type: vip.TypeLiveTrialNoble, ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix()},
	}
	info.setTip(&vip.Info{Level: 2, Type: vip.TypeLiveNoble}, uvMap)
	assert.Empty(info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Level: 2, Type: vip.TypeLiveNoble, ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix()},
		vip.TypeLiveTrialNoble: {Level: 2, Type: vip.TypeLiveTrialNoble, ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix()},
	}
	info.setTip(&vip.Info{Level: 2, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("续费至 2020-02-03 00:00", info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Level: 3, Type: vip.TypeLiveNoble},
		vip.TypeLiveTrialNoble: nil,
	}
	info.setTip(&vip.Info{Level: 2, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("开通至 2020-02-02 00:00", info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      nil,
		vip.TypeLiveTrialNoble: {Level: 4, Type: vip.TypeLiveTrialNoble},
	}
	info.setTip(&vip.Info{Level: 2, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("开通至 2020-02-02 00:00", info.Tip)

	info = vipInfo{}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble:      nil,
		vip.TypeLiveTrialNoble: {Level: 4, Type: vip.TypeLiveTrialNoble},
	}
	info.setTip(&vip.Info{Level: 5, Type: vip.TypeLiveNoble}, uvMap)
	assert.Equal("开通至 2020-02-02 00:00", info.Tip)
}

func TestCanTrialNobleBuyNoble(t *testing.T) {
	assert := assert.New(t)

	vipList := &vip.Info{
		Level: 5, Type: vip.TypeLiveNoble,
	}
	uvMap := map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			Level:      5,
			ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
		},
		vip.TypeLiveTrialNoble: {
			Level:      6,
			ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
		},
	}
	canBuy := canTrialNobleBuyNoble(vipList, uvMap)
	assert.True(canBuy)

	vipList = &vip.Info{
		Level: 5, Type: vip.TypeLiveNoble,
	}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			Level:      4,
			ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix(), // 续费保护期内但是等级与目标等级不一致
		},
		vip.TypeLiveTrialNoble: {
			Level:      6,
			ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
		},
	}
	canBuy = canTrialNobleBuyNoble(vipList, uvMap)
	assert.False(canBuy)

	vipList = &vip.Info{
		Level: 5, Type: vip.TypeLiveNoble,
	}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			Level:      5,
			ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix(), // 续费保护期内
		},
		vip.TypeLiveTrialNoble: {
			Level:      6,
			ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
		},
	}
	canBuy = canTrialNobleBuyNoble(vipList, uvMap)
	assert.True(canBuy)

	vipList = &vip.Info{
		Level: 5, Type: vip.TypeLiveNoble,
	}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			Level:      5,
			ExpireTime: goutil.TimeNow().Add(-11 * 24 * time.Hour).Unix(), // 超过续费保护期
		},
		vip.TypeLiveTrialNoble: {
			Level:      6,
			ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
		},
	}
	canBuy = canTrialNobleBuyNoble(vipList, uvMap)
	assert.False(canBuy)

	vipList = &vip.Info{
		Level: 5, Type: vip.TypeLiveNoble,
	}
	uvMap = map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			Level:      5,
			ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		},
		vip.TypeLiveTrialNoble: {
			Level:      6,
			ExpireTime: goutil.TimeNow().Add(-time.Hour).Unix(),
		},
	}
	canBuy = canTrialNobleBuyNoble(vipList, uvMap)
	assert.True(canBuy)
}
