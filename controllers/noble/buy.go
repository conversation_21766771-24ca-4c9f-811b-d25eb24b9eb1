package noble

// TODO: 不支持更新榜单

/*
type buyParam struct {
	RoomID         *int64 `json:"room_id" form:"room_id"`
	Level          int    `json:"level" form:"level"`
	IsRegistration *int   `json:"is_registration" form:"is_registration"`

	c       *handler.Context
	user    *user.User
	userVip *vip.UserVip
	liveID  int64
	r       *room.Room
	vipInfo *vip.Info // 通过等级查找

	isFirst bool
}
*/

// ActionBuy 购买贵族
/*
 * @api {post} /api/v2/vip/buy 开通/续费贵族
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/vip
 *
 * @apiParam {Number} [room_id] 目前观看的房间
 * @apiParam {Number} level 贵族等级
 * @apiParam {Number} is_registration 是否是开通贵族，0: 不是开通, 1: 是开通
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
/*
func ActionBuy(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newBuyParm(c)
	if err != nil {
		return nil, err
	}
	err = param.buy()
	if err != nil {
		return nil, err
	}
	param.afterBuy()
	return "购买成功", nil
}

func newBuyParm(c *handler.Context) (*buyParam, error) {
	fmt.Println("续订")
	var param buyParam
	err := c.Bind(&param)
	if err != nil || (param.RoomID != nil && *param.RoomID <= 0) ||
		param.Level <= 0 || param.IsRegistration == nil {
		return nil, actionerrors.ErrParams
	}
	param.vipInfo, err = vip.FindByLevel(param.Level)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.vipInfo == nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID != nil {
		param.r, err = room.FindOne(*param.RoomID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.r == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
		param.liveID = param.r.CreatorID
	}
	param.user = c.User()
	param.c = c
	return &param, nil
}

func (param *buyParam) buy() error {
	uv, err := vip.Buy(param.c, param.vipInfo.VipID, param.liveID,
		*param.IsRegistration != 0)
	if err != nil {
		return err
	}
	param.userVip = uv
	return nil
}

func (param *buyParam) afterBuy() {
	if param.userVip == nil {
		return
	}
	// 全站广播
	if param.vipInfo.NotifyType == vip.NotifyTypeAll {
		param.broadcastAll()
	}
	// 房间通知
	if param.RoomID != nil {
		param.broadcastRoom()
	}
}

var buyTypeStr = map[bool]string{
	false: "续约",
	true:  "开通",
}

var buyBroadcastEvent = map[bool]string{
	false: liveim.EventRenewal,
	true:  liveim.EventActivate,
}

func (param *buyParam) broadcastAll() {
	payloadPre := map[string]interface{}{
		"type":        liveim.TypeNotify,
		"notify_type": liveim.TypeVip,
		"event":       buyBroadcastEvent[param.isFirst],
		"vip_level":   param.Level,
		"username":    param.user.Username,
	}
	if param.RoomID != nil {
		payloadPre["room_id"] = *param.RoomID
		payloadPre["room"] = map[string]interface{}{
			"room_id":          *param.RoomID,
			"creator_username": param.r.CreatorUsername,
		}
	}
	p, err := json.Marshal(payloadPre)
	if err != nil {
		logger.Error(err)
		return
	}
	err = userapi.BroadcastAll(p)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *buyParam) broadcastRoom() {
	u := param.user
	payloadPre := map[string]interface{}{
		"type":    liveim.TypeVip,
		"event":   buyBroadcastEvent[param.isFirst],
		"room_id": *param.RoomID,
		"user": map[string]interface{}{
			"user_id":  u.ID,
			"username": u.Username,
			"iconurl":  u.IconURL,
		}, // WORKAROUND: 目前不需要该用户的勋章和等级
		"vip_level": param.vipInfo.Level,
	}
	p, err := json.Marshal(payloadPre)
	if err != nil {
		logger.Error(err)
		return
	}
	err = userapi.Broadcast(*param.RoomID, p)
	if err != nil {
		logger.Error(err)
		return
	}
}
*/
