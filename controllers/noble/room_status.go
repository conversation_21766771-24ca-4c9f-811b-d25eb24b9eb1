package noble

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionRankInvisibleList 获取被设置隐身的直播间
/**
 * @api {get} /api/v2/noble/rankinvisible/list 获取被设置隐身的直播间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [{
 *         "room_id": 1234,
 *         "creator_id": 123456,
 *         "creator_iconurl": "http://static.example.com/avatars/icon01.png",
 *         "creator_username": "主播昵称",
 *       }, {
 *         "room_id": 12345,
 *         "creator_id": 1234566,
 *         "creator_iconurl": "http://static.example.com/avatars/icon01.png",
 *         "creator_username": "主播昵称2",
 *       }],
 *       "pagination":{
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 2,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionRankInvisibleList(c *handler.Context) (handler.ActionResponse, error) {
	uv, err := vip.UserActivatedVip(c.UserID(), false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !vip.HavePrivilege(uv, vip.PrivilegeRankInvisible) {
		return nil, actionerrors.ErrNoAuthority
	}
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	roomIDs, pa, err := userstatus.ListRankInvisibleRoomID(uv.UserID, p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(roomIDs) == 0 {
		return handler.M{
			"Datas":      []*room.Simple{},
			"pagination": pa,
		}, nil
	}
	rooms, err := room.ListSimples(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil,
		&room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 排序无关，所以直接返回了
	return handler.M{
		"Datas":      rooms,
		"pagination": pa,
	}, nil
}

// ActionRankInvisibleSet 设置是否榜单隐身
/**
 * @api {post} /api/v2/noble/rankinvisible/set 设置是否榜单隐身
 * @apiDescription 支持单个房间开启和关闭隐身，支持多个房间关闭隐身
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {number=0,1} enable 是否开启榜单隐身
 * @apiParam {Number} [room_id] 房间号，开启隐身必传
 * @apiParam {Number[]} [room_ids] 房间号数组，只在关闭榜单隐身时有效
 *
 * @apiParamExample {json} 榜单处调用，开启隐身:
 *   {
 *     "enable": 1,
 *     "room_id": 1234567
 *   }
 *
 * @apiParamExample {json} 榜单处调用，关闭隐身:
 *   {
 *     "enable": 0,
 *     "room_id": 1234567
 *   }
 *
 * @apiParamExample {json} 列表中调用:
 *   {
 *     "enable": 0,
 *     "room_ids": [1234567, 745896]
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionRankInvisibleSet(c *handler.Context) (handler.ActionResponse, error) {
	uv, err := vip.UserActivatedVip(c.UserID(), false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !vip.HavePrivilege(uv, vip.PrivilegeRankInvisible) {
		return nil, actionerrors.ErrNoAuthority
	}
	var param struct {
		Enable  *int    `form:"enable" json:"enable"`
		RoomID  int64   `form:"room_id" json:"room_id"`
		RoomIDs []int64 `form:"room_ids" json:"room_ids"`
	}
	err = c.Bind(&param)
	if err != nil ||
		param.Enable == nil ||
		(*param.Enable != 0 && param.RoomID == 0) || /* 开启榜单隐身参数不全 */
		(param.RoomID == 0 && len(param.RoomIDs) == 0) /* 关闭榜单隐身参数不全 */ {
		return nil, actionerrors.ErrParams
	}
	if *param.Enable == 0 {
		if param.RoomID != 0 {
			param.RoomIDs = append(param.RoomIDs, param.RoomID)
		}
		err = userstatus.DisableRankInvisible(uv.UserID, util.Uniq(param.RoomIDs))
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		return true, nil
	}
	r, err := room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	err = userstatus.EnableRankInvisible(uv.UserID, r.RoomID, r.OID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return true, nil
}
