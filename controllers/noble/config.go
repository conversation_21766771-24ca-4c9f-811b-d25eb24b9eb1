package noble

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionSetConfig 设置隐身
/**
 * @api {post} /api/v2/noble/setconfig 设置特权，目前只支持隐身
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/noble
 *
 * @apiParam {nubmer=0,1} invisible 是否隐身；0: 不隐身, 1: 隐身
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSetConfig(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		Invisible *int `form:"invisible" json:"invisible"`
	}
	err := c.Bind(&param)
	if err != nil || param.Invisible == nil ||
		(*param.Invisible != 0 && *param.Invisible != 1) {
		return nil, actionerrors.ErrParams
	}
	uv, err := vip.UserActivatedVip(c.UserID(), false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if uv == nil || !uv.Info.Privilege.IsSet(vip.PrivilegeInvisible) {
		return nil, actionerrors.ErrNoAuthority
	}

	// TODO: 设置用户是否隐身待整合
	err = setInvisible(c.UserID(), *param.Invisible)
	if err != nil {
		return nil, err
	}
	return true, nil
}

func setInvisible(userID int64, invisible int) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection("user_meta")
	_, err := collection.UpdateOne(ctx, bson.M{"user_id": userID},
		bson.M{"$set": bson.M{"user_id": userID, "invisible": invisible != 0}},
		options.Update().SetUpsert(true))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}
