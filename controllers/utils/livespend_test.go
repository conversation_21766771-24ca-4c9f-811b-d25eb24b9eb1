package utils

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestSendLiveSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(service.Redis.Del(keys.KeyLiveSpend1.Format(9074509)).Err())
	service.DatabusPub.ClearDebugPubMsgs()
	defer service.DatabusPub.ClearDebugPubMsgs()
	SendLiveSpend(9074509, 100, 1)

	msgs := service.DatabusPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.KeyLiveSpend1.Format(9074509), m.Key)

	var message liveSpendMessage
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(9074509, message.UserID)
	assert.EqualValues(100, message.Spend)
}

func TestLiveSpendOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRenewUserID := int64(9074509)
	testOpenUserID := int64(9074509)
	cancel1 := mrpc.SetMock("sso://vip/buy-live-highness", func(input interface{}) (output interface{}, err error) {
		var uv struct {
			VipInfo vip.UserVip `json:"vip_info"`
		}
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userID, ok := body["user_id"].(int64)
		require.True(ok)
		vipID, ok := body["vip_id"].(int64)
		require.True(ok)
		require.EqualValues(8, vipID)
		_, ok = body["is_registration"].(bool)
		require.True(ok)

		uv.VipInfo = vip.UserVip{
			UserID:     userID,
			VipID:      vipID,
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		}

		return uv, nil
	})
	defer cancel1()

	msgSentCount := 0
	cancel2 := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		if systemMsgList[0].UserID == testRenewUserID {
			assert.EqualValues("成功续费上神贵族", systemMsgList[0].Title)
			assert.EqualValues(testRenewUserID, systemMsgList[0].UserID)
		} else {
			assert.EqualValues("成功开通上神贵族", systemMsgList[0].Title)
			assert.EqualValues(testOpenUserID, systemMsgList[0].UserID)
		}

		msgSentCount++
		return "success", nil
	})
	defer cancel2()

	// 测试开通上神
	message := liveSpendMessage{
		UserID:     testRenewUserID,
		Spend:      vip.HighnessRenewCoin,
		CreateTime: goutil.TimeNow().Unix() + 10,
	}
	require.NoError(usermeta.ResetHighnessSpend(testRenewUserID, 0))
	assert.NotPanics(func() {
		LiveSpendOperator()(&databus.Message{
			Key:   keys.KeyLiveSpend1.Format(testRenewUserID),
			Value: json.RawMessage(tutil.SprintJSON(message)),
		})
	})
	assert.Equal(1, msgSentCount)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userconsumption.Collection().DeleteMany(ctx, bson.M{"user_id": testOpenUserID})
	require.NoError(err)
	message = liveSpendMessage{
		UserID:     testOpenUserID,
		Spend:      vip.HighnessOpenCoin,
		CreateTime: goutil.TimeNow().Unix() + 10,
	}
	require.NoError(usermeta.ResetHighnessSpend(message.UserID, 0))
	assert.NotPanics(func() {
		LiveSpendOperator()(&databus.Message{
			Key:   keys.KeyLiveSpend1.Format(message.UserID),
			Value: json.RawMessage(tutil.SprintJSON(message)),
		})
	})
	assert.Equal(2, msgSentCount)
}
