package utils

import (
	"sort"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// GetRoom 返回 roomID 的房间信息到 v 指向的结构体，使用 fields 指定返回的字段，为空返回全部字段
func GetRoom(roomID int64, v interface{}, fields ...string) error {
	collection := service.MongoDB.Collection("rooms")
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": roomID}
	var r *mongo.SingleResult
	if len(fields) == 0 {
		r = collection.FindOne(ctx, filter)
	} else {
		projection := bson.M{}
		for _, v := range fields {
			projection[v] = 1
		}
		r = collection.FindOne(ctx, filter, options.FindOne().SetProjection(projection))
	}
	if err := r.Decode(v); err != nil {
		return err
	}
	return nil
}

// ParseBoolFromInt 从 "0", "1" 获取 bool 值
func ParseBoolFromInt(c *handler.Context, key string) bool {
	val, _ := c.GetParam(key)
	return val != "" && val != "0"
}

// TempParsePage 兼容参数 page 方法
// TODO: 统一参数 page 为 p 后，移除该方法
// Deprecated: 使用 handler.Context.GetParamPage()
func TempParsePage(c *handler.Context) (p, pageSize int64, err error) {
	pageSize, err = c.GetDefaultParamInt64("page_size", 20)
	if err != nil {
		return 0, 0, err
	}

	value, ok := c.GetParam("p")
	if ok {
		p, err = strconv.ParseInt(value, 10, 64)
		if err != nil {
			return 0, 0, err
		}
		return p, pageSize, nil
	}
	p, err = c.GetDefaultParamInt64("page", 1)
	if err != nil {
		return 0, 0, err
	}
	return
}

// DelTabsCache 删除 tab 缓存
// WORKAROUND: 放到 apiv2 下会有循环引用的问题
func DelTabsCache() {
	err := service.LRURedis.Del(keys.KeyLiveMetaTabs0.Format()).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// ParseSortStr parse param sort string like "guild_total.asc", "guild_total", etc.
// TODO: 整合至 missevan-go 项目中
func ParseSortStr(sortStr string, sortMapSupported map[string]bool) (orderByField string, order string, ok bool) {
	if isSupported, exists := sortMapSupported[sortStr]; !(exists && isSupported) {
		return "", "", false
	}

	items := strings.SplitN(sortStr, ".", 2)
	if len(items) != 2 {
		items = append(items, "ASC")
	}
	orderByField = items[0]
	order = strings.ToUpper(items[1])

	return orderByField, order, true
}

// GenerateSortMap 生成排序字段的 map
func GenerateSortMap(sortFields []string) map[string]bool {
	m := make(map[string]bool, len(sortFields)*3)
	for _, field := range sortFields {
		m[field] = true
		if !strings.HasSuffix(field, ".asc") && !strings.HasSuffix(field, ".desc") {
			m[field+".asc"] = true
			m[field+".desc"] = true
		}
	}

	return m
}

// NewMedalInfo 生成 MedalInfo
func NewMedalInfo(lm *livemedal.LiveMedal) *livemeta.MedalInfo {
	if lm == nil || lm.Status == livemedal.StatusPending {
		return nil
	}
	return &livemeta.MedalInfo{
		Level:    lm.Level,
		SuperFan: livemedal.IsSuperFanActive(lm.SuperFan),
	}
}

// SpeakLimit 发言限制
type SpeakLimit struct {
	User        *liveuser.Simple
	Room        *room.Room
	IsRoomAdmin bool                 // 是否是房管
	RoomMedal   *livemedal.LiveMedal // 当前房间的粉丝勋章
}

// Check 检查用户是否可以发言
func (sl *SpeakLimit) Check() error {
	// 判断是否是房主
	if sl.Room.IsOwner(sl.User) {
		return nil
	}
	// 是否是超管
	if sl.User.IsRole(liveuser.RoleStaff) {
		return nil
	}
	// 判断是否是房管
	if sl.IsRoomAdmin {
		return nil
	}
	// 查询发言设置
	speakSettings, err := livemeta.FindSpeakSettings(sl.Room.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if speakSettings == nil {
		return nil
	}
	limited := speakSettings.IsSpeakLimited(usercommon.Level(sl.User.Contribution), NewMedalInfo(sl.RoomMedal))
	if limited {
		return actionerrors.NewErrLiveForbidden("由于主播设置，暂时无法操作")
	}
	return nil
}

// limit
const (
	LuckyBagRoomLimit = 3 // 福袋房间数量返回的最大数量
)

// DramaLuckyBagListData 广播剧福袋列表所需要的数据源
type DramaLuckyBagListData struct {
	DramaLuckyBagList []*DramaLuckyBagInfo           // 需要输出的剧集福袋列表
	users             map[int64]*mowangskuser.Simple // 需要输出的用户信息
	initiates         []*luckybag.InitiateRecord     // 数据库中所有进行中或者是在人气周榜数据中的剧集福袋记录
}

// DramaLuckyBagListResp 广播剧福袋列表返回值
type DramaLuckyBagListResp struct {
	HasMore *bool                `json:"has_more,omitempty"`
	Data    []*DramaLuckyBagInfo `json:"data"`
}

// DramaLuckyBagInfo 广播剧福袋信息
type DramaLuckyBagInfo struct {
	IPRID     int64      `json:"ipr_id,omitempty"`
	IPRName   string     `json:"ipr_name,omitempty"`
	DramaID   int64      `json:"drama_id,omitempty"`
	DramaName string     `json:"drama_name,omitempty"`
	CoverURL  string     `json:"cover_url"`
	Num       int64      `json:"num"`
	Rooms     []RoomInfo `json:"rooms"`
}

// RoomInfo 房间信息
type RoomInfo struct {
	RoomID         int64  `json:"room_id"`
	CreatorID      int64  `json:"creator_id"`
	CreatorIconURL string `json:"creator_iconurl"`
}

// findLuckyBagRoomUsers 查询福袋房间的主播信息
func (param *DramaLuckyBagListData) findLuckyBagRoomUsers() error {
	allUserIDs := make([]int64, 0, len(param.initiates))
	for _, initiate := range param.initiates {
		allUserIDs = append(allUserIDs, initiate.CreatorID)
	}
	allUserIDs = util.Uniq(allUserIDs)
	userMap, err := mowangskuser.FindSimpleMap(allUserIDs)
	if err != nil {
		return err
	}
	param.users = userMap
	return nil
}

// buildLuckyBagRoomInfo 构建福袋房间信息
func (param *DramaLuckyBagListData) buildLuckyBagRoomInfo(initiates []*luckybag.InitiateRecord) []RoomInfo {
	roomCount := min(len(initiates), LuckyBagRoomLimit)
	roomsInfo := make([]RoomInfo, 0, roomCount)
	for i := 0; i < roomCount; i++ {
		initiate := initiates[i]
		roomInfo := RoomInfo{
			// 一个房间只会有一条 pending 的 record
			RoomID:    initiate.RoomID,
			CreatorID: initiate.CreatorID,
		}
		if u := param.users[initiate.CreatorID]; u != nil {
			roomInfo.CreatorIconURL = u.IconURL
		}
		roomsInfo = append(roomsInfo, roomInfo)
	}
	return roomsInfo
}

// fillDramaLuckyBagRooms 填充剧集福袋房间
func (param *DramaLuckyBagListData) fillDramaLuckyBagRooms() error {
	aggregateIPRIDsMap := make(map[int64][]*luckybag.InitiateRecord)
	aggregateDramaIDsMap := make(map[int64][]*luckybag.InitiateRecord)

	// 按 PrizeIPRID、PrizeDramaID 分别聚合数据
	for _, initiate := range param.initiates {
		if initiate.PrizeIPRID != 0 {
			aggregateIPRIDsMap[initiate.PrizeIPRID] = append(aggregateIPRIDsMap[initiate.PrizeIPRID], initiate)
		} else {
			aggregateDramaIDsMap[initiate.PrizeDramaID] = append(aggregateDramaIDsMap[initiate.PrizeDramaID], initiate)
		}
	}
	err := param.findLuckyBagRoomUsers()
	if err != nil {
		return err
	}

	for iprID, initiates := range aggregateIPRIDsMap {
		roomsInfo := param.buildLuckyBagRoomInfo(initiates)
		// 将房间放入对应的广播剧福袋中
		for _, info := range param.DramaLuckyBagList {
			if info.IPRID == iprID {
				info.Num = int64(len(initiates))
				info.Rooms = roomsInfo
			}
		}
	}

	for dramaID, initiates := range aggregateDramaIDsMap {
		roomsInfo := param.buildLuckyBagRoomInfo(initiates)
		// 将房间放入对应的广播剧福袋中
		for _, info := range param.DramaLuckyBagList {
			if info.DramaID == dramaID {
				info.Num = int64(len(initiates))
				info.Rooms = roomsInfo
			}
		}
	}

	return nil
}

// ListAll 获取广播剧福袋列表
func (param *DramaLuckyBagListData) ListAll(uc mrpc.UserContext, limits ...int) (*DramaLuckyBagListResp, error) {
	limit := 0
	if len(limits) == 1 {
		limit = limits[0]
	}
	if limit < 0 {
		return nil, actionerrors.ErrParams
	}

	// 查询所有进行中的剧集福袋记录
	var err error
	param.initiates, err = luckybag.FindPendingInitiateDramaRecords()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &DramaLuckyBagListResp{
		Data: make([]*DramaLuckyBagInfo, 0, len(param.initiates)),
	}
	if len(param.initiates) == 0 {
		if limit != 0 {
			resp.HasMore = goutil.NewBool(false)
		}
		return resp, nil
	}

	// 存放剧集 ID 到福袋记录的映射
	dramaInitiateMap := make(map[int64]*luckybag.InitiateRecord, len(param.initiates))
	// 存放 IPR 下最大的剧集 ID 对应的福袋记录
	iprInitiateMap := make(map[int64]*luckybag.InitiateRecord, len(param.initiates))
	allInitiates := make([]*luckybag.InitiateRecord, 0, len(param.initiates))
	for _, initiate := range param.initiates {
		dramaInitiateMap[initiate.PrizeDramaID] = initiate
		iprID := initiate.PrizeIPRID
		if iprID != 0 {
			if i, exists := iprInitiateMap[iprID]; !exists || initiate.PrizeDramaID > i.PrizeDramaID {
				iprInitiateMap[iprID] = initiate
			}
		}
	}

	input := userapi.RankListParam{
		Type:    userapi.RankTypeDramaPopularity,
		SubType: userapi.RankSubTypeWeek,
	}
	// 获取榜单数据
	res, err := userapi.RankList(uc, input)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 存放人气周榜中不重复的 IPR ID
	uniqueRankIPRIDMap := make(map[int64]struct{})
	// 存放在人气周榜剧集中有福袋记录的剧集 ID
	uniqueRankDramaIDMap := make(map[int64]struct{})
	if res != nil && userapi.RankElementTypeDrama == res.RankElementType && len(res.RankElements) > 0 {
		for _, rankElement := range res.RankElements {
			dramaID := rankElement.DramaID
			iprID := rankElement.IPRID
			uniqueRankDramaIDMap[dramaID] = struct{}{}
			/*
				剧集 IP / 剧集排序规则：
				优先按人气周榜中的排名进行排序，排名越高，排序越靠前
				有剧集 IP 的，取 IP 下在人气周榜排名最高的单剧排名，作为本剧集 IP 的排名
				没有剧集 IP 的，直接取本剧集在人气周榜的排名
				没有人气周榜排名的，排在有排名的后面，按照剧集 ID 从大到小排列
				有剧集 IP 的，取 IP 下最大的剧集 ID 来参与排序

				举例：
				已知当前全站正在抽剧集福袋（用 drama_id 标识）：7, 3, 4, 6, 8, 9
				已知 drama7 属于 ip1；drama3，drama4 属于 ip3；drama8 属于 ip4
				已知广播剧人气周榜排序（用 drama_id 标识，没有记录的表示未上榜）：1, 2, 3, 4, 5, 6
				则最后正在抽奖的福袋排序为：ip1（drama7），ip3（drama3，drama4），drama6，drama9，ip4（drama8）
			*/
			if iprID != 0 {
				// 该人气周榜剧集 ID 所属 IPR 已经存在数据了，跳过
				// 举例：两个剧集 ID 上了周榜，同属于同一个 IPR ID，取到第一个满足条件的数据返回，后面的数据跳过
				if _, exists := uniqueRankIPRIDMap[iprID]; exists {
					continue
				}
				if i, exists := dramaInitiateMap[dramaID]; exists && i.PrizeIPRID == iprID {
					// 该人气周榜剧集 ID 有 IPR 且该剧集有进行中的福袋取该剧集的福袋记录
					allInitiates = append(allInitiates, i)
					uniqueRankIPRIDMap[iprID] = struct{}{}
				} else if i, exists := iprInitiateMap[iprID]; exists && i.PrizeIPRID == iprID {
					// 该人气周榜下剧集 ID 所属 IPR 有福袋，但榜中剧集自身没有福袋，取此有福袋的 IPR 下最大剧集 ID 的剧集来使用
					allInitiates = append(allInitiates, i)
					uniqueRankDramaIDMap[i.PrizeDramaID] = struct{}{}
					uniqueRankIPRIDMap[iprID] = struct{}{}
				}
			} else {
				if i, exists := dramaInitiateMap[dramaID]; exists {
					// 没有剧集 IPR 的，取人气周榜的本剧集的福袋记录
					allInitiates = append(allInitiates, i)
				}
			}
		}
	}

	dramaInitiates := make([]*luckybag.InitiateRecord, 0, len(dramaInitiateMap))
	// TODO: Go 1.23 之后使用 maps.Values 处理
	for _, dramaInitiate := range dramaInitiateMap {
		dramaInitiates = append(dramaInitiates, dramaInitiate)
	}

	sort.Slice(dramaInitiates, func(i, j int) bool {
		// 按剧集 ID 从大到小排序
		return dramaInitiates[i].PrizeDramaID > dramaInitiates[j].PrizeDramaID
	})

	// 追加未在人气周榜中的进行中的福袋记录
	for _, dramaInitiate := range dramaInitiates {
		if _, exists := uniqueRankDramaIDMap[dramaInitiate.PrizeDramaID]; exists {
			continue
		}
		allInitiates = append(allInitiates, dramaInitiate)
	}

	// 存放剧集福袋不重复的 IPR ID
	uniqueIPRIDMap := make(map[int64]struct{})
	for _, initiate := range allInitiates {
		info := &DramaLuckyBagInfo{
			CoverURL: initiate.MoreInfo.PrizeIconURL,
		}
		iprID := initiate.PrizeIPRID
		if iprID != 0 {
			// 剧集福袋所属同一个 IPR 聚合展示一条记录
			if _, exists := uniqueIPRIDMap[iprID]; exists {
				continue
			}
			if initiate.MoreInfo == nil || initiate.MoreInfo.PrizeIPRName == "" {
				logger.WithField("lucky_bag_id", initiate.ID).Error("福袋 more 异常")
				// 降级使用广播剧的名称
				info.IPRName = initiate.Name
			} else {
				info.IPRName = initiate.MoreInfo.PrizeIPRName
			}
			info.IPRID = iprID
			uniqueIPRIDMap[iprID] = struct{}{}
		} else {
			info.DramaID = initiate.PrizeDramaID
			info.DramaName = initiate.Name
		}
		param.DramaLuckyBagList = append(param.DramaLuckyBagList, info)
	}

	// 填充福袋房间信息
	err = param.fillDramaLuckyBagRooms()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Data = param.DramaLuckyBagList
	if limit != 0 {
		hasMore := len(resp.Data) > limit
		resp.HasMore = &hasMore
		if hasMore {
			resp.Data = resp.Data[:limit]
		}
	}
	return resp, nil
}
