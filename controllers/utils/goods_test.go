package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
)

func TestLiveGoodsToGoods(t *testing.T) {
	assert := assert.New(t)

	lgs := []livegoods.LiveGoods{{
		ID:          1,
		Num:         1,
		Price:       100,
		Title:       "100",
		Icon:        "test1",
		Description: "11",
	}, {
		ID:          2,
		Num:         2,
		Price:       200,
		Icon:        "test2",
		Description: "22",
	}}
	r := []Goods{
		{
			ID:          1,
			Num:         1,
			Price:       100,
			Title:       "100",
			IconURL:     "https://static-test.missevan.com/test1",
			Description: "11",
		},
		{
			ID:          2,
			Num:         2,
			Price:       200,
			IconURL:     "https://static-test.missevan.com/test2",
			Description: "22",
		},
	}
	assert.Equal(r, LiveGoodsToGoods(lgs))
}
