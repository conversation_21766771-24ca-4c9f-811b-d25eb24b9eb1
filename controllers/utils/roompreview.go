package utils

import (
	"fmt"
	"strconv"
	"time"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/util"
)

// Constants for preview intro icons
const (
	PreviewIntroLuckyBagIcon     = "oss://app/livetab/lucky-bag.png"
	PreviewIntroRedPacketIcon    = "oss://app/livetab/red-packet.png"
	PreviewIntroRankIcon         = "oss://app/livetab/rank.png"
	PreviewIntroPKIcon           = "oss://app/livetab/pk.png"
	PreviewIntroFollowersIcon    = "oss://app/livetab/followers.png"
	PreviewIntroListenersIcon    = "oss://app/livetab/listeners.png"
	PreviewIntroMultiConnectIcon = "oss://app/livetab/multi-connect.png"
)

// BuildPreviewIntro 构建补充信息
func BuildPreviewIntro(r *room.Simple, uc mrpc.UserContext) *room.PreviewIntro {
	var previewIntro *room.PreviewIntro

	// 1. 直播间内有未开奖的福袋
	luckyBagPipe := func() bool {
		bag, err := luckybag.FindPendingInitiateRecordByRoomID(r.RoomID)
		if err != nil {
			logger.Error(err)
			return false
		}
		if bag == nil {
			return false
		}

		switch bag.Type {
		case luckybag.TypeDrama:
			previewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(PreviewIntroLuckyBagIcon),
				Title:   fmt.Sprintf("正在免费抽《%s》福袋", bag.Name),
			}
			return true
		case luckybag.TypeEntity:
			previewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(PreviewIntroLuckyBagIcon),
				Title:   "正在抽周边福袋",
			}
			return true
		default:
			return false
		}
	}

	// 2. 直播间内有未抢完的红包
	redPacketPipe := func() bool {
		if r.Status.RedPacket <= 0 {
			return false
		}

		previewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(PreviewIntroRedPacketIcon),
			Title:   "正在抢礼物红包",
		}
		return true
	}

	// 3. 榜单排名信息
	rankPipe := func() bool {
		const (
			rank3  = 2
			rank10 = 9
			rank20 = 19
		)

		var (
			now          = util.TimeNow()
			creatorIDStr = strconv.FormatInt(r.CreatorID, 10)
		)

		pipe := service.Redis.TxPipeline()
		hourRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeHour, now), creatorIDStr)
		lastHourRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeHour, now.Add(-time.Hour)), creatorIDStr)
		dayRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeDay, now), creatorIDStr)
		weekRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeWeek, now), creatorIDStr)
		monthRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeMonth, now), creatorIDStr)
		novaRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeNova, now), creatorIDStr)
		_, err := pipe.Exec()
		if err != nil && !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}

		hourRank, err := hourRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if hourRank <= rank3 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播小时榜排名第 %d", hourRank+1),
				}
				return true
			}
			if hourRank <= rank10 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   "直播小时榜排名 TOP10",
				}
				return true
			}
		}

		lastHourRank, err := lastHourRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if lastHourRank <= rank3 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播上小时榜排名第 %d", lastHourRank+1),
				}
				return true
			}
		}

		dayRank, err := dayRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if dayRank <= rank10 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播日榜排名第 %d", dayRank+1),
				}
				return true
			}
		}

		weekRank, err := weekRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if weekRank <= rank10 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播周榜排名第 %d", weekRank+1),
				}
				return true
			}
		}

		monthRank, err := monthRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if monthRank <= rank10 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播月榜排名第 %d", monthRank+1),
				}
				return true
			}
			if monthRank <= rank20 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   "直播月榜排名 TOP20",
				}
				return true
			}
		}

		novaRank, err := novaRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if novaRank <= rank3 {
				previewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播新人榜排名第 %d", novaRank+1),
				}
				return true
			}
		}

		return false
	}

	// 4. 在连麦 PK 时
	pkPipe := func() bool {
		if r.Status.PK == 0 {
			return false
		}

		previewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(PreviewIntroPKIcon),
			Title:   "主播正在连麦 PK",
		}
		return true
	}

	// 5. 在主播连线时
	multiConnect := func() bool {
		if r.Status.MultiConnect != room.MultiConnectStatusOngoing {
			return false
		}
		previewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(PreviewIntroMultiConnectIcon),
			Title:   "主播正在多人连线",
		}
		return true
	}

	// 6. 粉丝人数达标
	followerPipe := func() bool {
		attention, err := attentionuser.CheckAttention(0 /* 仅获取粉丝数 */, []int64{r.CreatorID})
		if err != nil {
			logger.Error(err)
			return false
		}
		if len(attention) <= 0 {
			return false
		}

		followersCount := attention[0].FansNum
		if followersCount < 10000 {
			return false
		}
		previewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(PreviewIntroFollowersIcon),
			Title:   fmt.Sprintf("超 %d 万粉宝藏主播", followersCount/10000),
		}
		return true
	}

	// 7. 在线人数 >= 100 时
	onlinePipe := func() bool {
		onlineResp, err := userapi.Online(uc, r.RoomID)
		if err != nil {
			logger.Error(err)
			return false
		}

		hairThinSpace := string([]rune{0x200A})
		n := onlineResp.Count
		switch {
		case n < 100:
			return false
		case n < 1000:
			previewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(PreviewIntroListenersIcon),
				// Sample: 100 + 人正在听
				Title: fmt.Sprintf("%d00%s+%s人正在听", n/100, hairThinSpace, hairThinSpace),
			}
			return true
		case n < 10000:
			previewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(PreviewIntroListenersIcon),
				// Sample: 1 千 + 人正在听
				Title: fmt.Sprintf("%d%s千%s+%s人正在听", n/1000, hairThinSpace, hairThinSpace, hairThinSpace),
			}
			return true
		default:
			previewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(PreviewIntroListenersIcon),
				// Sample: 1 万 + 人正在听
				Title: fmt.Sprintf("%d%s万%s+%s人正在听", n/10000, hairThinSpace, hairThinSpace, hairThinSpace),
			}
			return true
		}
	}

	pipeFuncs := []func() bool{luckyBagPipe, redPacketPipe, rankPipe, pkPipe, multiConnect, followerPipe, onlinePipe}
	for _, f := range pipeFuncs {
		if f() {
			return previewIntro
		}
	}
	return nil
}
