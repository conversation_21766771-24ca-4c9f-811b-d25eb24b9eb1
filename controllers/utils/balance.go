package utils

import (
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
)

// BalanceAfterSend 余额返回
type BalanceAfterSend struct {
	Balance                int64 `json:"balance"`
	LiveNobleBalance       int64 `json:"live_noble_balance"`
	LiveNobleBalanceStatus int   `json:"live_noble_balance_status"`
}

// NewBalanceAfterSend 创建余额返回
func NewBalanceAfterSend(rpcBalance *userapi.BalanceResp, isNoble bool) *BalanceAfterSend {
	return &BalanceAfterSend{
		Balance:                rpcBalance.Balance,
		LiveNobleBalance:       rpcBalance.LiveNobleBalance,
		LiveNobleBalanceStatus: util.BoolToInt(isNoble),
	}
}
