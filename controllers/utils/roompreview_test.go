package utils

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestBuildPreviewIntro(t *testing.T) {
	c := handler.NewTestContext(http.MethodGet, "/", true, nil)

	onlineCount := 0
	// 设置在线人数 mock
	cancel := mrpc.SetMock("im://online/count", func(any) (any, error) {
		return map[string]any{
			"count": onlineCount,
		}, nil
	})
	defer cancel()

	t.Run("empty case", func(t *testing.T) {
		r := &room.Simple{
			RoomID:    114514,
			CreatorID: 114514,
			Status: &room.Status{
				Open: room.StatusOpenTrue,
			},
		}
		result := BuildPreviewIntro(r, c.UserContext())
		assert.Nil(t, result)
	})

	t.Run("online listeners", func(t *testing.T) {
		testCases := []struct {
			count    int
			expected string
		}{
			{100, "100 + 人正在听"},
			{512, "500 + 人正在听"},
			{3234, "3 千 + 人正在听"},
			{53234, "5 万 + 人正在听"},
		}

		for _, tc := range testCases {
			onlineCount = tc.count
			r := &room.Simple{RoomID: 123, Status: &room.Status{Open: room.StatusOpenTrue}}
			result := BuildPreviewIntro(r, c.UserContext())

			assert.NotNil(t, result)
			assert.Equal(t, tc.expected, result.Title)
			assert.Equal(t, service.Storage.Parse(PreviewIntroListenersIcon), result.IconURL)
		}
	})

	t.Run("follower case", func(t *testing.T) {
		r := &room.Simple{
			CreatorID: 3457183, // 假设这个用户有超过7万粉丝
			Status:    &room.Status{Open: room.StatusOpenTrue},
		}
		result := BuildPreviewIntro(r, c.UserContext())

		assert.NotNil(t, result)
		assert.Equal(t, "超 7 万粉宝藏主播", result.Title)
		assert.Equal(t, service.Storage.Parse(PreviewIntroFollowersIcon), result.IconURL)
	})

	t.Run("PK status", func(t *testing.T) {
		r := &room.Simple{
			Status: &room.Status{PK: 1},
		}
		result := BuildPreviewIntro(r, c.UserContext())

		assert.NotNil(t, result)
		assert.Equal(t, "主播正在连麦 PK", result.Title)
		assert.Equal(t, service.Storage.Parse(PreviewIntroPKIcon), result.IconURL)
	})

	t.Run("multi connect status", func(t *testing.T) {
		r := &room.Simple{
			Status: &room.Status{MultiConnect: room.MultiConnectStatusOngoing},
		}
		result := BuildPreviewIntro(r, c.UserContext())

		assert.NotNil(t, result)
		assert.Equal(t, "主播正在多人连线", result.Title)
		assert.Equal(t, service.Storage.Parse(PreviewIntroMultiConnectIcon), result.IconURL)
	})

	t.Run("rank", func(t *testing.T) {
		now := util.TimeNow()
		// 初始化房间基础信息
		r := &room.Room{
			Helper: room.Helper{
				RoomID:    114514,
				CreatorID: 1,
			},
		}

		// 准备测试数据
		zs := make([]*redis.Z, 0, 25)
		for i := 1; i <= 25; i++ {
			zs = append(zs, &redis.Z{
				Score:  float64(26 - i),
				Member: i,
			})
		}

		// 测试新人榜
		novaKey := usersrank.Key(usersrank.TypeNova, now)
		require.NoError(t, service.Redis.Del(novaKey).Err())
		require.NoError(t, service.Redis.ZAdd(novaKey, zs...).Err())
		defer service.Redis.Del(novaKey)

		// 构造房间简单对象
		rSimple := &room.Simple{
			RoomID:    114514,
			CreatorID: 1,
			Status:    &room.Status{Open: room.StatusOpenTrue},
		}
		intro := BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播新人榜排名第 1",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试月榜
		monthKey := usersrank.Key(usersrank.TypeMonth, now)
		require.NoError(t, service.Redis.Del(monthKey).Err())
		require.NoError(t, service.Redis.ZAdd(monthKey, zs...).Err())
		defer service.Redis.Del(monthKey)

		r.Helper.CreatorID = 1
		rSimple.CreatorID = 1
		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播月榜排名第 1",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试TOP20显示
		r.Helper.CreatorID = 19
		rSimple.CreatorID = 19
		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播月榜排名 TOP20",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试周榜
		weekKey := usersrank.Key(usersrank.TypeWeek, now)
		require.NoError(t, service.Redis.Del(weekKey).Err())
		require.NoError(t, service.Redis.ZAdd(weekKey, zs...).Err())
		defer service.Redis.Del(weekKey)

		r.Helper.CreatorID = 9
		rSimple.CreatorID = 9
		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播周榜排名第 9",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试日榜
		dayKey := usersrank.Key(usersrank.TypeDay, now)
		require.NoError(t, service.Redis.Del(dayKey).Err())
		require.NoError(t, service.Redis.ZAdd(dayKey, zs...).Err())
		defer service.Redis.Del(dayKey)

		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播日榜排名第 9",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试上小时榜
		lastHourKey := usersrank.Key(usersrank.TypeHour, now.Add(-time.Hour))
		require.NoError(t, service.Redis.Del(lastHourKey).Err())
		require.NoError(t, service.Redis.ZAdd(lastHourKey, zs...).Err())
		defer service.Redis.Del(lastHourKey)

		r.Helper.CreatorID = 2
		rSimple.CreatorID = 2
		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播上小时榜排名第 2",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试当前小时榜
		hourKey := usersrank.Key(usersrank.TypeHour, now)
		require.NoError(t, service.Redis.Del(hourKey).Err())
		require.NoError(t, service.Redis.ZAdd(hourKey, zs...).Err())
		defer service.Redis.Del(hourKey)

		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播小时榜排名第 2",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)

		// 测试TOP10显示
		r.Helper.CreatorID = 10
		rSimple.CreatorID = 10
		intro = BuildPreviewIntro(rSimple, c.UserContext())
		assert.Equal(t, &room.PreviewIntro{
			Title:   "直播小时榜排名 TOP10",
			IconURL: service.Storage.Parse(PreviewIntroRankIcon),
		}, intro)
	})

	t.Run("red packet case", func(t *testing.T) {
		r := &room.Simple{
			Status: &room.Status{RedPacket: 1},
		}
		result := BuildPreviewIntro(r, c.UserContext())

		assert.NotNil(t, result)
		assert.Equal(t, "正在抢礼物红包", result.Title)
		assert.Equal(t, service.Storage.Parse(PreviewIntroRedPacketIcon), result.IconURL)
	})

	t.Run("lucky bag case", func(t *testing.T) {
		r := &room.Simple{
			RoomID: 18113499,
			Status: &room.Status{RedPacket: 1},
		}
		result := BuildPreviewIntro(r, c.UserContext())

		assert.NotNil(t, result)
		assert.Equal(t, "正在免费抽《广播剧 1》福袋", result.Title)
		assert.Equal(t, service.Storage.Parse(PreviewIntroLuckyBagIcon), result.IconURL)
	})
}
