package utils

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCreatorIncomeListParam_LoadCommonParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := handler.NewTestContext(http.MethodGet, "/?start_date=2024-02-01&end_date=2024-02-29&p=2&pagesize=15&type=3", true, nil)
	var param CreatorIncomeListParam
	require.NoError(param.LoadCommonParams(ctx))

	assert.Equal("2024-02-01", param.startDate.Format(goutil.TimeFormatYMD))
	assert.Equal("2024-02-29", param.endDate.Format(goutil.TimeFormatYMD))
	assert.Equal(int64(2), param.page)
	assert.Equal(int64(15), param.pageSize)
	assert.Equal(3, param.incomeType)
}

func TestCreatorIncomeListParam_IncomeCondSQL(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	param := CreatorIncomeListParam{
		incomeType: IncomeTypeGift,
	}
	condSQL, err := param.IncomeCondSQL()
	require.NoError(err)
	assert.Equal(transactionlog.GiftCondSQL(), condSQL)

	param.incomeType = IncomeTypeNoble
	condSQL, err = param.IncomeCondSQL()
	require.NoError(err)
	assert.Equal(transactionlog.NobleCondSQL(), condSQL)

	param.incomeType = IncomeTypeSuperFan
	condSQL, err = param.IncomeCondSQL()
	require.NoError(err)
	assert.Equal(transactionlog.SuperFanCondSQL(), condSQL)

	param.incomeType = IncomeTypePlay
	condSQL, err = param.IncomeCondSQL()
	require.NoError(err)
	assert.Equal(transactionlog.PlayCondSQL(), condSQL)
}

func TestCreatorIncomeListParam_CreatorIncomeList(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	tradelogList := []*transactionlog.TransactionLog{
		{
			FromID:       3,
			ToID:         8888,
			GiftID:       1,
			Title:        "CreatorIncomeList-素人普通礼物-1",
			IOSCoin:      100,
			AllCoin:      100,
			Income:       10,
			Tax:          3,
			Rate:         0.5,
			Num:          1,
			Status:       1,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrCommon,
			CTime:        time.Date(2024, 2, 2, 1, 0, 0, 0, time.Local).Unix(),
			CreateTime:   time.Date(2024, 2, 2, 1, 0, 0, 0, time.Local).Unix(),
			ModifiedTime: time.Date(2024, 2, 2, 1, 0, 0, 0, time.Local).Unix(),
			ConfirmTime:  time.Date(2024, 2, 2, 1, 0, 0, 0, time.Local).Unix(),
		},
		{
			FromID:       4,
			ToID:         8888,
			GiftID:       555,
			Title:        "CreatorIncomeList-素人随机礼物-1",
			IOSCoin:      500,
			AllCoin:      500,
			Income:       50,
			Tax:          15,
			Rate:         0.5,
			Num:          1,
			Status:       1,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrLiveLuckyGift,
			CTime:        time.Date(2024, 2, 2, 2, 0, 0, 0, time.Local).Unix(),
			CreateTime:   time.Date(2024, 2, 2, 2, 0, 0, 0, time.Local).Unix(),
			ModifiedTime: time.Date(2024, 2, 2, 2, 0, 0, 0, time.Local).Unix(),
			ConfirmTime:  time.Date(2024, 2, 2, 2, 0, 0, 0, time.Local).Unix(),
		},
		{
			FromID:       5,
			ToID:         8888,
			GiftID:       0,
			Title:        "CreatorIncomeList-素人付费问答-1",
			IOSCoin:      300,
			AllCoin:      300,
			Income:       30,
			Tax:          4,
			Rate:         0.5,
			Num:          1,
			Status:       1,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrCommon,
			CTime:        time.Date(2024, 2, 2, 3, 0, 0, 0, time.Local).Unix(),
			CreateTime:   time.Date(2024, 2, 2, 3, 0, 0, 0, time.Local).Unix(),
			ModifiedTime: time.Date(2024, 2, 2, 3, 0, 0, 0, time.Local).Unix(),
			ConfirmTime:  time.Date(2024, 2, 2, 3, 0, 0, 0, time.Local).Unix(),
		},
		{
			FromID:       6,
			ToID:         9999,
			GiftID:       1,
			Title:        "CreatorIncomeList-公会普通礼物-1",
			IOSCoin:      200,
			AllCoin:      200,
			Income:       20,
			Tax:          6,
			Rate:         0.5,
			Num:          1,
			Status:       1,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 999,
			Attr:         transactionlog.AttrCommon,
			CTime:        time.Date(2024, 2, 2, 4, 0, 0, 0, time.Local).Unix(),
			CreateTime:   time.Date(2024, 2, 2, 4, 0, 0, 0, time.Local).Unix(),
			ModifiedTime: time.Date(2024, 2, 2, 4, 0, 0, 0, time.Local).Unix(),
			ConfirmTime:  time.Date(2024, 2, 2, 4, 0, 0, 0, time.Local).Unix(),
		},
	}

	require.NoError(servicedb.BatchInsert(transactionlog.DB(), transactionlog.TransactionLog{}.TableName(), tradelogList))
	defer func() {
		require.NoError(transactionlog.DB().Delete("", "title LIKE 'CreatorIncomeList%'").Error)
	}()

	param := CreatorIncomeListParam{
		incomeType: IncomeTypeGift,
		startDate:  time.Date(2024, 2, 1, 0, 0, 0, 0, time.Local),
		endDate:    time.Date(2024, 2, 10, 0, 0, 0, 0, time.Local),
		page:       1,
		pageSize:   10,
	}
	list, err := param.CreatorIncomeList(8888, 0, true)
	require.NoError(err)
	require.Len(list.Data, 2)
	assert.Equal(int64(2), list.Pagination.Count)
	assert.Equal(util.Float2DP(20), *list.Total)
	assert.Equal(util.Float2DP(17), list.Data[0].Revenue)
	assert.Equal(int64(4), list.Data[0].UserID)
	assert.Equal("迦夜ちゃん", list.Data[0].Username)

	assert.Equal(util.Float2DP(3), list.Data[1].Revenue)
	assert.Equal(int64(3), list.Data[1].UserID)
	assert.Equal("暗切线", list.Data[1].Username)
}
