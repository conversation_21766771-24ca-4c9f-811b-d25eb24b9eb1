package utils

import "github.com/MiaoSiLa/live-service/models/mysql/livegoods"

// Goods 商品信息
type Goods struct {
	ID          int64  `json:"id"`
	Num         int    `json:"num"`
	Price       int    `json:"price"`
	Title       string `json:"title,omitempty"`
	IconURL     string `json:"icon_url"`
	Description string `json:"description,omitempty"`
}

// LiveGoodsToGoods 将 LiveGoods 转换为 goods
func LiveGoodsToGoods(lgs []livegoods.LiveGoods) []Goods {
	gs := make([]Goods, len(lgs))
	for k, v := range lgs {
		gs[k] = Goods{
			ID:          v.ID,
			Num:         v.Num,
			Price:       v.Price,
			Title:       v.Title,
			IconURL:     v.GoodsIconURL(),
			Description: v.Description,
		}
	}
	return gs
}
