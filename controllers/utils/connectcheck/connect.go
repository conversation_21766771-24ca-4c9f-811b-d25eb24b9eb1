package connectcheck

import (
	"github.com/MiaoSiLa/live-service/models/room"
)

// ConnectComponent 听众连麦组件
type ConnectComponent struct {
	base *baseComponent
}

// NewConnectComponent .
func NewConnectComponent(room *room.Room) *ConnectComponent {
	return &ConnectComponent{
		base: &baseComponent{
			connectType: typeConnect,
			fromRoom:    room,
			toRoom:      nil, // 听众连麦时对方非主播所以 toRoom 为 nil
		},
	}
}

// Check 检查
func (component *ConnectComponent) Check() error {
	err := component.base.check()
	if err != nil {
		return err
	}
	// TODO: 具体行为检查迁移过来
	return nil
}
