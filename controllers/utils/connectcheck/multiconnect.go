package connectcheck

import (
	"github.com/MiaoSiLa/live-service/models/room"
)

// MultiConnectComponent 主播连线组件
type MultiConnectComponent struct {
	base *baseComponent
}

// NewMultiConnectComponent .
func NewMultiConnectComponent(fromRoom, toRoom *room.Room) *MultiConnectComponent {
	return &MultiConnectComponent{
		base: &baseComponent{
			connectType: typeMultiConnect,
			fromRoom:    fromRoom,
			toRoom:      toRoom,
		},
	}
}

// Check 检查
func (component *MultiConnectComponent) Check() error {
	err := component.base.check()
	if err != nil {
		return err
	}
	return nil
}
