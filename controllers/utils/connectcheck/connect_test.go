package connectcheck

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
)

func TestNewConnectComponent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := NewConnectComponent(&room.Room{})
	require.NotNil(p)
	assert.NotNil(p.base.fromRoom)
	assert.Nil(p.base.toRoom)
	assert.Equal(typeConnect, p.base.connectType)
}

func TestConnectComponent_Check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromRoomID = &room.Room{Helper: room.Helper{RoomID: 1}}
	)
	insertTestData(t, testFromRoomID, nil)

	err := NewConnectComponent(testFromRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播 PK 中，无法操作"), err)

	_, err = livepk.PoolCollection().DeleteMany(context.Background(), bson.M{"room_id": testFromRoomID.RoomID})
	require.NoError(err)
	err = NewConnectComponent(testFromRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播 PK 中，无法操作"), err)

	_, err = livepk.PKCollection().DeleteMany(context.Background(), bson.M{"fighters.room_id": testFromRoomID.RoomID})
	require.NoError(err)
	err = NewConnectComponent(testFromRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播连线中，无法操作"), err)

	err = livemulticonnect.DB().Where("from_room_id = ?", testFromRoomID.RoomID).Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	err = NewConnectComponent(testFromRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播连线中，无法操作"), err)

	err = livemulticonnect.DB().Where("room_id = ?", testFromRoomID.RoomID).Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)
	err = NewConnectComponent(testFromRoomID).Check()
	assert.NoError(err)
}
