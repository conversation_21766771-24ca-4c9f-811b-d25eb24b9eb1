package connectcheck

import (
	"slices"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
)

// 连线类型
const (
	typeConnect      = iota + 1 // 听众连麦
	typePK                      // PK
	typeMultiConnect            // 主播连线
)

// baseComponent 连线基础组件
type baseComponent struct {
	connectType int
	fromRoom    *room.Room
	toRoom      *room.Room
}

// Check 检查如下内容（不检查当前连线动作是否合法，只检查当前是否有正在进行中与其互斥连线类型）
// 1. 检查发起听众连麦时，是否正在 PK 或主播连线中
// 2. 检查发起 PK 时，是否正在听众连麦或主播连线中
// 3. 检查发起主播连线时，是否正在听众连麦或 PK 中
func (component *baseComponent) check() error {
	if !slices.Contains([]int{typeConnect, typePK, typeMultiConnect}, component.connectType) {
		panic("未知连线类型")
	}

	if component.connectType != typeConnect {
		connecting, err := liveconnect.IsRoomConnecting(component.fromRoom.RoomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if connecting {
			return actionerrors.NewErrForbidden("当前正在听众连麦中，无法操作")
		}
		if component.toRoom != nil {
			connecting, err = liveconnect.IsRoomConnecting(component.toRoom.RoomID)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if connecting {
				return actionerrors.NewErrForbidden("对方正在听众连麦中，无法操作")
			}
		}
	}
	if component.connectType != typePK {
		// 检查是否在 PK 匹配或邀请中
		pool, err := livepk.FindPool(bson.M{
			"status": livepk.PKPoolStatusWaiting,
			"$or":    bson.A{bson.M{"room_id": component.fromRoom.RoomID}, bson.M{"to_room_id": component.fromRoom.RoomID}},
		})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if pool != nil {
			return actionerrors.NewErrForbidden("当前正在主播 PK 中，无法操作")
		}
		// 检查是否在 PK 中
		pk, err := livepk.FindOne(bson.M{
			"fighters.room_id": component.fromRoom.RoomID,
			"status":           bson.M{"$in": []int{livepk.PKRecordStatusConnect, livepk.PKRecordStatusFighting, livepk.PKRecordStatusPunishment}},
		})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if pk != nil {
			return actionerrors.NewErrForbidden("当前正在主播 PK 中，无法操作")
		}
		if component.toRoom != nil {
			// 检查对方直播间是否在 PK 匹配或邀请中
			pool, err = livepk.FindPool(bson.M{
				"status": livepk.PKPoolStatusWaiting,
				"$or":    bson.A{bson.M{"room_id": component.toRoom.RoomID}, bson.M{"to_room_id": component.toRoom.RoomID}},
			})
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if pool != nil {
				return actionerrors.NewErrForbidden("对方正在主播 PK 中，无法操作")
			}
			// 检查对方直播间是否正在 PK 中
			pk, err = livepk.FindOne(bson.M{
				"fighters.room_id": component.toRoom.RoomID,
				"status":           bson.M{"$in": []int{livepk.PKRecordStatusConnect, livepk.PKRecordStatusFighting, livepk.PKRecordStatusPunishment}},
			})
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if pk != nil {
				return actionerrors.NewErrForbidden("对方正在主播 PK 中，无法操作")
			}
		}
	}
	if component.connectType != typeMultiConnect {
		matching, err := livemulticonnect.ExistsPendingMatch(component.fromRoom.RoomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if matching {
			return actionerrors.NewErrForbidden("当前正在主播连线中，无法操作")
		}
		ongoings, err := livemulticonnect.FindOngoingMembers([]int64{component.fromRoom.RoomID})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if len(ongoings) > 0 {
			return actionerrors.NewErrForbidden("当前正在主播连线中，无法操作")
		}
		if component.toRoom != nil {
			matching, err = livemulticonnect.ExistsPendingMatch(component.toRoom.RoomID)
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if matching {
				return actionerrors.NewErrForbidden("对方正在主播连线中，无法操作")
			}
			ongoings, err = livemulticonnect.FindOngoingMembers([]int64{component.toRoom.RoomID})
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if len(ongoings) > 0 {
				return actionerrors.NewErrForbidden("对方正在主播连线中，无法操作")
			}
		}
	}
	return nil
}
