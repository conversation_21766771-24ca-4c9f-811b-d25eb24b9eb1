package connectcheck

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/room"
)

func TestNewPKComponent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := NewPKComponent(&room.Room{}, &room.Room{})
	require.NotNil(p)
	assert.NotNil(p.base.fromRoom)
	assert.NotNil(p.base.toRoom)
	assert.Equal(typePK, p.base.connectType)
}

func TestPKComponent_Check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromRoomID = &room.Room{Helper: room.Helper{RoomID: 1}}
		testToRoomID   = &room.Room{Helper: room.Helper{RoomID: 2}}
	)
	insertTestData(t, testFromRoomID, testToRoomID)

	err := NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在听众连麦中，无法操作"), err)

	_, err = liveconnect.Collection().DeleteOne(context.Background(), bson.M{"room_id": testFromRoomID.RoomID})
	require.NoError(err)
	err = NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("对方正在听众连麦中，无法操作"), err)

	_, err = liveconnect.Collection().DeleteOne(context.Background(), bson.M{"room_id": testToRoomID.RoomID})
	require.NoError(err)
	err = NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播连线中，无法操作"), err)

	err = livemulticonnect.DB().Where("from_room_id = ?", testFromRoomID.RoomID).Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	err = NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播连线中，无法操作"), err)

	err = livemulticonnect.DB().Where("to_room_id = ?", testFromRoomID.RoomID).Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	err = NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("当前正在主播连线中，无法操作"), err)

	err = livemulticonnect.DB().Where("room_id = ?", testFromRoomID.RoomID).Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)
	err = NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.Equal(actionerrors.NewErrForbidden("对方正在主播连线中，无法操作"), err)

	err = livemulticonnect.DB().Where("room_id = ?", testToRoomID.RoomID).Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)
	err = NewPKComponent(testFromRoomID, testToRoomID).Check()
	assert.NoError(err)
}
