package connectcheck

import "github.com/MiaoSiLa/live-service/models/room"

// PKComponent PK 组件
type PKComponent struct {
	base *baseComponent
}

// NewPKComponent .
func NewPKComponent(fromRoom, toRoom *room.Room) *PKComponent {
	return &PKComponent{
		base: &baseComponent{
			connectType: typePK,
			fromRoom:    fromRoom,
			toRoom:      toRoom, // 随机匹配 PK 时 toRoom 为 nil
		},
	}
}

// Check 检查
func (component *PKComponent) Check() error {
	err := component.base.check()
	if err != nil {
		return err
	}
	// TODO: 具体行为检查迁移过来
	return nil
}
