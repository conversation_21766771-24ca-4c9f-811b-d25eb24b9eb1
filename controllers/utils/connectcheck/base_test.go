package connectcheck

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func insertTestData(t *testing.T, fromRoom, toRoom *room.Room) {
	require := require.New(t)

	_, err := liveconnect.Collection().DeleteMany(context.Background(), bson.M{"room_id": fromRoom.RoomID})
	require.NoError(err)
	_, err = liveconnect.Collection().InsertOne(context.Background(),
		liveconnect.LiveConnect{
			OID: primitive.NewObjectID(),
			Helper: liveconnect.Helper{
				RoomID: fromRoom.RoomID,
				Status: liveconnect.StatusJoined,
			},
		},
	)
	require.NoError(err)
	if toRoom != nil {
		_, err = liveconnect.Collection().DeleteMany(context.Background(), bson.M{"room_id": toRoom.RoomID})
		require.NoError(err)
		_, err = liveconnect.Collection().InsertOne(context.Background(),
			liveconnect.LiveConnect{
				OID: primitive.NewObjectID(),
				Helper: liveconnect.Helper{
					RoomID: toRoom.RoomID,
					Status: liveconnect.StatusJoined,
				},
			},
		)
		require.NoError(err)
	}
	_, err = livepk.PoolCollection().DeleteMany(context.Background(), bson.M{"room_id": fromRoom.RoomID})
	require.NoError(err)
	_, err = livepk.PoolCollection().InsertOne(context.Background(),
		livepk.Pool{
			OID:    primitive.NewObjectID(),
			RoomID: fromRoom.RoomID,
			Status: livepk.PKPoolStatusWaiting,
		},
	)
	require.NoError(err)
	if toRoom != nil {
		_, err = livepk.PoolCollection().DeleteMany(context.Background(), bson.M{"room_id": toRoom.RoomID})
		require.NoError(err)
		_, err = livepk.PoolCollection().InsertOne(context.Background(),
			livepk.Pool{
				OID:    primitive.NewObjectID(),
				RoomID: toRoom.RoomID,
				Status: livepk.PKPoolStatusWaiting,
			},
		)
		require.NoError(err)
	}
	_, err = livepk.PKCollection().DeleteMany(context.Background(), bson.M{"fighters.room_id": fromRoom.RoomID})
	require.NoError(err)
	_, err = livepk.PKCollection().InsertOne(context.Background(),
		livepk.LivePK{
			OID:    primitive.NewObjectID(),
			Status: livepk.PKRecordStatusConnect,
			Fighters: [2]*livepk.Fighter{
				{
					RoomID: fromRoom.RoomID,
				},
				{
					RoomID: fromRoom.RoomID,
				},
			},
		},
	)
	require.NoError(err)
	if toRoom != nil {
		_, err = livepk.PKCollection().DeleteMany(context.Background(), bson.M{"fighters.room_id": toRoom.RoomID})
		require.NoError(err)
		_, err = livepk.PKCollection().InsertOne(context.Background(),
			livepk.LivePK{
				OID:    primitive.NewObjectID(),
				Status: livepk.PKRecordStatusConnect,
				Fighters: [2]*livepk.Fighter{
					{
						RoomID: toRoom.RoomID,
					},
					{
						RoomID: toRoom.RoomID,
					},
				},
			},
		)
		require.NoError(err)
	}

	err = livemulticonnect.DB().
		Where("from_room_id = ? OR to_room_id = ?", fromRoom.RoomID, fromRoom.RoomID).
		Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	if toRoom != nil {
		err = livemulticonnect.DB().
			Where("from_room_id = ? OR to_room_id = ?", toRoom.RoomID, toRoom.RoomID).
			Delete(livemulticonnect.Match{}).Error
		require.NoError(err)
	}
	if toRoom != nil {
		err = servicedb.BatchInsert(livemulticonnect.DB(), new(livemulticonnect.Match).TableName(), []*livemulticonnect.Match{
			{
				FromRoomID: fromRoom.RoomID,
				ToRoomID:   toRoom.RoomID,
				Status:     livemulticonnect.MatchStatusPending,
			},
			{
				FromRoomID: toRoom.RoomID,
				ToRoomID:   fromRoom.RoomID,
				Status:     livemulticonnect.MatchStatusPending,
			},
		})
	} else {
		err = livemulticonnect.DB().Create(&livemulticonnect.Match{
			FromRoomID: fromRoom.RoomID,
			ToRoomID:   3231,
			Status:     livemulticonnect.MatchStatusPending,
		}).Error
	}
	require.NoError(err)

	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "room_id = ?", fromRoom.RoomID).Error
	require.NoError(err)
	if toRoom != nil {
		err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "room_id = ?", toRoom.RoomID).Error
		require.NoError(err)
	}

	err = livemulticonnect.DB().Create(&livemulticonnect.GroupMember{
		RoomID:  fromRoom.RoomID,
		Status:  livemulticonnect.MemberStatusOngoing,
		EndTime: 0,
	}).Error
	require.NoError(err)
	if toRoom != nil {
		err = livemulticonnect.DB().Create(&livemulticonnect.GroupMember{
			RoomID:  toRoom.RoomID,
			Status:  livemulticonnect.MemberStatusOngoing,
			EndTime: 0,
		}).Error
		require.NoError(err)
	}
}
