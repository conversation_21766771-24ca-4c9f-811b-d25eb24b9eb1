package utils

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/luavm"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest()
	service.SetDBUseSQLite()
	handler.SetMode(handler.TestMode)
	luavm.InitTest()
	m.Run()
}

func TestGetRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var room struct {
		RoomID    int64 `bson:"room_id"`
		CreatorID int64 `bson:"creator_id"`
	}
	roomID := int64(22489473)
	err := GetRoom(roomID, &room, "room_id", "creator_id")
	require.NoError(err)
	assert.Equal(roomID, room.RoomID)
	assert.NotZero(room.CreatorID)
}

func TestParseBoolFromInt(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "?key=", false, nil)
	assert.False(ParseBoolFromInt(c, "key"))
	c = handler.NewTestContext(http.MethodGet, "?key=0", false, nil)
	assert.False(ParseBoolFromInt(c, "key"))
	c = handler.NewTestContext(http.MethodGet, "?key=1", false, nil)
	assert.True(ParseBoolFromInt(c, "key"))
}

func TestTempParsePage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "?", false, nil)
	p, pageSize, err := TempParsePage(c)
	require.NoError(err)
	assert.True(p == 1 && pageSize == 20)

	c = handler.NewTestContext(http.MethodGet, "?p=2&page_size=1", false, nil)
	p, pageSize, err = TempParsePage(c)
	require.NoError(err)
	assert.True(p == 2 && pageSize == 1)

	c = handler.NewTestContext(http.MethodGet, "?page=2&page_size=1", false, nil)
	p, pageSize, err = TempParsePage(c)
	require.NoError(err)
	assert.True(p == 2 && pageSize == 1)

	c = handler.NewTestContext(http.MethodGet, "?p=1&page=2&page_size=1", false, nil)
	p, pageSize, err = TempParsePage(c)
	require.NoError(err)
	assert.True(p == 1 && pageSize == 1)
}

func TestDelTabsCache(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyLiveMetaTabs0.Format()
	_, err := service.LRURedis.Set(key, "[]", 10*time.Second).Result()
	require.NoError(err)

	DelTabsCache()
	_, err = service.LRURedis.Get(key).Result()
	assert.Equal(redis.Nil, err)
}

func TestParseSortStr(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	orderBy, order, ok := ParseSortStr("guild_id", map[string]bool{
		"guild_id.asc":  true,
		"guild_id.desc": true,
		"guild_id":      true,
	})
	require.True(ok)
	assert.Equal("guild_id", orderBy)
	assert.Equal("ASC", order)

	orderBy, order, ok = ParseSortStr("guild_id.desc", map[string]bool{
		"guild_id.asc":  true,
		"guild_id.desc": true,
		"guild_id":      true,
	})
	require.True(ok)
	assert.Equal("guild_id", orderBy)
	assert.Equal("DESC", order)

	_, _, ok = ParseSortStr("id.asc", map[string]bool{
		"guild_id.asc":  true,
		"guild_id.desc": true,
		"guild_id":      true,
	})
	require.False(ok)
}

func TestGenerateSortMap(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	sortMap := GenerateSortMap([]string{"guild_id"})
	require.Len(sortMap, 3)
	assert.Equal(map[string]bool{
		"guild_id":      true,
		"guild_id.asc":  true,
		"guild_id.desc": true,
	}, sortMap)

	sortMap = GenerateSortMap([]string{
		"guild_id.desc",
		"creator_id.asc",
		"income",
	})
	require.Len(sortMap, 5)
	assert.Equal(map[string]bool{
		"guild_id.desc":  true,
		"creator_id.asc": true,
		"income":         true,
		"income.asc":     true,
		"income.desc":    true,
	}, sortMap)
}

func TestNewMedalInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	info := NewMedalInfo(nil)
	assert.Nil(info)

	info = NewMedalInfo(&livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Status: livemedal.StatusPending,
		},
	})
	assert.Nil(info)

	info = NewMedalInfo(&livemedal.LiveMedal{
		Simple: livemedal.Simple{
			Status: livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Level: 1,
			},
		},
	})
	require.NotNil(info)
	assert.NotZero(info.Level)
}

func TestSpeakLimit_Check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	room, err := room.Find(room.TestExistsRoomID)
	require.NoError(err)
	require.NotNil(room)
	userRole := liveuser.RoleAdmin
	err = livemeta.SetSpeakSettings(room.RoomID, nil)
	require.NoError(err)

	// 超管
	limit := &SpeakLimit{
		Room: room,
		User: &liveuser.Simple{
			UID:    123,
			Group:  &userRole,
			Titles: []liveuser.Title{liveuser.LevelTitle(0)},
		},
	}
	err = limit.Check()
	assert.NoError(err)

	// 房管
	limit = &SpeakLimit{
		Room: room,
		User: &liveuser.Simple{
			UID:    123,
			Titles: []liveuser.Title{liveuser.LevelTitle(0)},
		},
		IsRoomAdmin: true,
	}
	err = limit.Check()
	assert.NoError(err)

	// 无设置
	limit.IsRoomAdmin = false
	err = livemeta.SetSpeakSettings(limit.Room.RoomID, nil)
	require.NoError(err)
	err = limit.Check()
	assert.NoError(err)

	// 不满足发言条件
	ss := &livemeta.SpeakSettings{
		Enable:    true,
		LiveLevel: 2,
	}
	err = livemeta.SetSpeakSettings(limit.Room.RoomID, ss)
	require.NoError(err)
	err = limit.Check()
	assert.EqualError(err, "由于主播设置，暂时无法操作")

	// 满足发言条件
	limit.User.Contribution = 100000
	err = limit.Check()
	assert.NoError(err)
}

func TestDramaLuckyBagListData_findLuckyBagRoomUsers(t *testing.T) {
	require := require.New(t)

	param := &DramaLuckyBagListData{initiates: []*luckybag.InitiateRecord{
		{CreatorID: 3457114},
		{CreatorID: 3457024},
		{CreatorID: 3456864},
	}}
	err := param.findLuckyBagRoomUsers()
	require.NoError(err)

	_, exists := param.users[3457114]
	require.True(exists)
	_, exists = param.users[3457024]
	require.True(exists)
	_, exists = param.users[3456864]
	require.True(exists)
}

func TestDramaLuckyBagListData_buildLuckyBagRoomInfo(t *testing.T) {
	assert := assert.New(t)

	// 某个 IPR 下的福袋房间信息
	initiates := []*luckybag.InitiateRecord{
		{CreatorID: 3456864, RoomID: 100000005},
		{CreatorID: 3457024, RoomID: 100000006},
	}
	param := &DramaLuckyBagListData{
		users: map[int64]*mowangskuser.Simple{
			3456864: {IconURL: "https://static-test.missevan.com/avatars/3456864.png"},
			3457024: {IconURL: "https://static-test.missevan.com/avatars/3457024.png"},
		},
	}
	resp := param.buildLuckyBagRoomInfo(initiates)

	except := []RoomInfo{
		{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: "https://static-test.missevan.com/avatars/3456864.png"},
		{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: "https://static-test.missevan.com/avatars/3457024.png"},
	}
	assert.Equal(except, resp)
}

func TestDramaLuckyBagListData_fillDramaLuckyBagRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	coverURL := "https://static-test.missevan.com/icon3.png"
	creatorIconURL := "https://static-test.missevan.com/avatars/icon01.png"

	param := &DramaLuckyBagListData{
		// 所有剧集福袋记录
		initiates: []*luckybag.InitiateRecord{
			{ID: 6, PrizeIPRID: 1, PrizeDramaID: 106, RoomID: 100000007, CreatorID: 3457114, Num: 20, ScheduledEndTime: 1633021200},
			{ID: 5, PrizeIPRID: 0, PrizeDramaID: 105, RoomID: 100000007, CreatorID: 3457114, Num: 10, ScheduledEndTime: 1633021200},
			{ID: 1, PrizeIPRID: 1, PrizeDramaID: 101, RoomID: 100000005, CreatorID: 3456864, Num: 9, ScheduledEndTime: 1633024800},
			{ID: 3, PrizeIPRID: 3, PrizeDramaID: 103, RoomID: 100000007, CreatorID: 3457114, Num: 5, ScheduledEndTime: 1633021200},
			{ID: 4, PrizeIPRID: 0, PrizeDramaID: 104, RoomID: 100000007, CreatorID: 3457114, Num: 5, ScheduledEndTime: 1633021200},
			{ID: 2, PrizeIPRID: 2, PrizeDramaID: 102, RoomID: 100000006, CreatorID: 3457024, Num: 5, ScheduledEndTime: 1633028400},
		},
		// 需要输出的剧集福袋信息
		DramaLuckyBagList: []*DramaLuckyBagInfo{
			{IPRID: 3, IPRName: "IP", DramaID: 103, DramaName: "测试剧集", CoverURL: coverURL},
			{IPRID: 2, IPRName: "IP", DramaID: 102, DramaName: "测试剧集", CoverURL: coverURL},
			{IPRID: 1, IPRName: "IP", DramaID: 101, DramaName: "测试剧集", CoverURL: coverURL},
			{IPRID: 0, IPRName: "", DramaID: 105, DramaName: "测试剧集", CoverURL: coverURL},
			{IPRID: 0, IPRName: "", DramaID: 104, DramaName: "测试剧集", CoverURL: coverURL},
		},
	}

	err := param.fillDramaLuckyBagRooms()
	require.NoError(err)

	// 填充后的数据
	except := []*DramaLuckyBagInfo{
		{
			IPRID: 3, IPRName: "IP", DramaID: 103, DramaName: "测试剧集", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: creatorIconURL},
			},
		},
		{
			IPRID: 2, IPRName: "IP", DramaID: 102, DramaName: "测试剧集", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: creatorIconURL},
			},
		},
		{
			IPRID: 1, IPRName: "IP", DramaID: 101, DramaName: "测试剧集", CoverURL: coverURL, Num: 2,
			Rooms: []RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: creatorIconURL},
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: creatorIconURL},
			},
		},
		{
			IPRID: 0, IPRName: "", DramaID: 105, DramaName: "测试剧集", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: creatorIconURL},
			},
		},
		{
			IPRID: 0, IPRName: "", DramaID: 104, DramaName: "测试剧集", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: creatorIconURL},
			},
		},
	}
	assert.Equal(except, param.DramaLuckyBagList)
}

func TestListAll(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	coverURL := "https://static-test.missevan.com/icon3.png"
	iconURL := "https://static-test.missevan.com/avatars/icon01.png"
	limit := 2

	// 测试参数错误
	uc := mrpc.UserContext{
		UserAgent: "test-ua",
		IP:        "127.0.0.1",
	}
	param := &DramaLuckyBagListData{}
	_, err := param.ListAll(uc, -1)
	assert.EqualError(err, "参数错误")

	// mock
	cancel := mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{
			RankElementType: userapi.RankElementTypeDrama,
			RankElements: []userapi.RankElementItem{
				{DramaID: 223344, IPRID: 2233}, // 223344 没有福袋
				{DramaID: 6767, IPRID: 2233},
				{DramaID: 6768, IPRID: 0},
				{DramaID: 6769, IPRID: 2234},
			},
			Bizdate: "2024-08-19",
		}, nil
	})
	defer cancel()

	// 测试有人气周榜数据取 2 条
	param = &DramaLuckyBagListData{}
	resp, err := param.ListAll(uc, limit)
	require.NoError(err)
	assert.NotNil(resp)
	assert.True(*resp.HasMore)
	except := []*DramaLuckyBagInfo{
		{
			IPRID: 2233, IPRName: "IP 名称", CoverURL: coverURL, Num: 2,
			Rooms: []RoomInfo{
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: iconURL},
				{RoomID: 100000008, CreatorID: 3457111, CreatorIconURL: iconURL},
			},
		},
		{
			DramaID: 6768, DramaName: "广播剧福袋", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
			},
		},
	}
	assert.Equal(except, resp.Data)

	cancel = mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{}, nil
	})
	defer cancel()
	// 测试无人气周榜数据取 2 条
	param = &DramaLuckyBagListData{}
	resp, err = param.ListAll(uc, limit)
	require.NoError(err)
	assert.NotNil(resp)
	assert.True(*resp.HasMore)
	except = []*DramaLuckyBagInfo{
		{
			IPRID: 2234, IPRName: "IP 名称", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000007, CreatorID: 3457114, CreatorIconURL: iconURL},
			},
		},
		{
			DramaID: 6768, DramaName: "广播剧福袋", CoverURL: coverURL, Num: 1,
			Rooms: []RoomInfo{
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
			},
		},
	}
	assert.Equal(except, resp.Data)
}
