package utils

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcard"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 走 rank/revenue 的消息
type liveSpendMessage struct {
	UserID     int64 `json:"user_id"`
	Spend      int64 `json:"spend"`
	CreateTime int64 `json:"create_time"`
	RoomID     int64 `json:"room_id,omitempty"` // 消费的房间 ID，不是在直播间内消费时为空（用于黑卡开通或升级成功时发送 ws 用户直播间广播消息）

	openCount                int64
	renewCount               int64
	finalExpireTime          int64
	lastExpireTime           int64
	userTodayConsumption     int64 // 用户今日消费
	userHighnessConsumption  int64 // 用户上神消费（近 30 天有效消费）
	userBlackCardConsumption int64 // 用户黑卡消费（本月有效消费）
	userConsumptionTime      time.Time
	fields                   logger.Fields
	gift                     *gift.Gift
	buyHighnessRecordList    []buyHighnessRecord

	blackCardLevelMap                   map[int]liveblackcard.LiveBlackCard // 所有黑卡等级 map，以黑卡等级为 key
	thisMonthUserBlackCardsHighestLevel int                                 // 本月已开通的最高黑卡等级
	lastMonthUserBlackCardHighestLevel  int                                 // 上个月已开通的最高黑卡等级
	currentUserBlackCardLevel           int                                 // 当前有效的黑卡等级（上月和本月中取最高等级）
	newLiveBlackCard                    *liveblackcard.LiveBlackCard        // 本次消费最终达成的黑卡等级
	userAddBlackCardDrawPoint           int64                               // 本次消费最终用户获得的抽奖积分
	blackCardOpenType                   int                                 // 本次消费最终黑卡开通类型（激活、升级或续期）
	blackCardSystemMsgList              []pushservice.SystemMsg             // 本次消费最终开通类型（激活、升级或续期）
	newBlackCardLevelExpireTime         int64                               // 新达成的黑卡等级的过期时间（单位：秒）
}

type buyHighnessRecord struct {
	open       bool
	expireTime int64
}

// SendLiveSpend 发送消费累计钻石
func SendLiveSpend(userID, spend, roomID int64) {
	if userID <= 0 || spend <= 0 {
		return
	}
	message := liveSpendMessage{
		UserID:     userID,
		Spend:      spend,
		CreateTime: goutil.TimeNow().Unix(),
		RoomID:     roomID,
	}

	key := keys.KeyLiveSpend1.Format(userID)
	if err := service.DatabusSend(key, message); err != nil {
		logger.Error(err)
		// PASS
	}
}

// LiveSpendOperator 直播消费 operator
func LiveSpendOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !keys.KeyLiveSpend1.MatchKey(m.Key) {
			return
		}
		var message liveSpendMessage
		err := json.Unmarshal(m.Value, &message)
		if err != nil {
			logger.Error(err)
			return
		}
		if message.UserID <= 0 || message.Spend <= 0 {
			return
		}
		message.fields = logger.Fields{
			"user_id": message.UserID,
			"spend":   message.Spend,
			"room_id": message.RoomID,
		}

		// 添加用户消费
		err = userstatus.AddUserSpend(message.UserID, message.Spend)
		if err != nil {
			logger.WithFields(message.fields).Error(err)
			// PASS
		}
		// 更新用户当天消费记录
		message.userConsumptionTime = time.Unix(message.CreateTime, 0)
		message.userTodayConsumption, err = userconsumption.AddUserConsumption(message.UserID, message.Spend, message.userConsumptionTime)
		if err != nil {
			logger.WithFields(message.fields).Error(err)
			// PASS
		}
		// 上神消费
		message.highnessConsumption()
		// 发送等级礼包
		message.sendLevelGte85ConsumptionPack()
		// 黑卡消费
		message.blackCardConsumption()
	}
}
