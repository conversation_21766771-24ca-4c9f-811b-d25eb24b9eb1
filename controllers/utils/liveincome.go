package utils

import (
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type liveIncomeItem struct {
	UserID      int64         `json:"user_id"`
	Title       string        `json:"title"`
	Revenue     util.Float2DP `json:"revenue"` // 单位：元
	Status      int           `json:"status"`
	ConfirmTime int64         `json:"confirm_time"`
	Username    string        `json:"username"`
}

// LiveIncomeListResp 直播收益记录列表
type LiveIncomeListResp struct {
	Total      *util.Float2DP    `json:"total,omitempty"` // 单位：元
	Data       []liveIncomeItem  `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
}

// IncomeType 收益类型
const (
	IncomeTypeGift = iota
	IncomeTypeNoble
	IncomeTypeSuperFan
	IncomeTypePlay
)

// CreatorIncomeListParam 主播收益记录参数
type CreatorIncomeListParam struct {
	incomeType int

	startDate time.Time
	endDate   time.Time

	page     int64
	pageSize int64
}

// LoadCommonParams 加载参数
func (param *CreatorIncomeListParam) LoadCommonParams(ctx *handler.Context) (err error) {
	param.page, param.pageSize, err = ctx.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}

	now := goutil.TimeNow()
	param.startDate, param.endDate, err = ctx.GetParamDateRange(
		now.AddDate(0, 0, -6).Format(goutil.TimeFormatYMD),
		now.Format(goutil.TimeFormatYMD),
	)
	if err != nil {
		return actionerrors.ErrParams
	}

	param.incomeType, err = ctx.GetDefaultParamInt("type", 0)
	if err != nil {
		return actionerrors.ErrParams
	}

	return nil
}

// IncomeCondSQL 收益条件 SQL
func (param *CreatorIncomeListParam) IncomeCondSQL() (string, error) {
	switch param.incomeType {
	case IncomeTypeGift:
		return transactionlog.GiftCondSQL(), nil
	case IncomeTypeNoble:
		return transactionlog.NobleCondSQL(), nil
	case IncomeTypeSuperFan:
		return transactionlog.SuperFanCondSQL(), nil
	case IncomeTypePlay:
		return transactionlog.PlayCondSQL(), nil
	}

	return "", actionerrors.ErrParams
}

// CreatorIncomeList 主播收益记录
func (param *CreatorIncomeListParam) CreatorIncomeList(creatorID, guildID int64, isCreatorSelf bool) (*LiveIncomeListResp, error) {
	incomeCondSQL, err := param.IncomeCondSQL()
	if err != nil {
		return nil, err
	}
	db := transactionlog.DB().
		Where("to_id = ?", creatorID).
		Where("confirm_time BETWEEN ? AND ?", param.startDate.Unix(), param.endDate.AddDate(0, 0, 1).Unix()-1).
		Where(incomeCondSQL)

	if guildID > 0 {
		db = db.Where(transactionlog.AllLiveRevenueCondSQL(transactionlog.TypeGuildLive, guildID))
	} else {
		db = db.Where(transactionlog.AllLiveRevenueCondSQL(transactionlog.TypeLive))
	}

	var count int64
	if err = db.Count(&count).Error; err != nil && !servicedb.IsErrNoRows(err) {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := &LiveIncomeListResp{
		Total:      nil,
		Pagination: goutil.MakePagination(count, param.page, param.pageSize),
	}
	if count == 0 {
		resp.Data = make([]liveIncomeItem, 0)
		if param.page == 1 {
			r := util.Float2DP(0)
			resp.Total = &r
		}
		return resp, nil
	}

	var revenueColumn, sumRevenueColumn string
	if guildID > 0 {
		sumRevenueColumn = transactionlog.SumRevenueColumn(transactionlog.RevenueTypeGuildCreator, "", "total")
		revenueColumn = transactionlog.RevenueColumn(transactionlog.RevenueTypeGuildCreator, "income")
	} else {
		sumRevenueColumn = transactionlog.SumRevenueColumn(transactionlog.RevenueTypeSingleCreator, "", "total")
		revenueColumn = transactionlog.RevenueColumn(transactionlog.RevenueTypeSingleCreator, "income")
	}
	// page 为 1 时总是计算 total
	if param.page == 1 {
		totalRevenue := new(util.Float2DP)
		err = db.Select(sumRevenueColumn).Row().Scan(totalRevenue)
		if err != nil && !servicedb.IsErrNoRows(err) {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		resp.Total = totalRevenue
	}

	if !resp.Pagination.Valid() {
		resp.Data = make([]liveIncomeItem, 0)
		return resp, nil
	}
	db = resp.Pagination.ApplyTo(db)

	var revenueList []transactionlog.TransactionLog
	err = db.Select([]string{
		"id",
		"from_id",
		"gift_id",
		"attr",
		"num",
		"title",
		revenueColumn,
		"status",
		"confirm_time",
	}).Order("confirm_time DESC, id DESC").Find(&revenueList).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if len(revenueList) > 0 {
		fromUserIDs := make([]int64, len(revenueList))
		for i := range revenueList {
			fromUserIDs[i] = revenueList[i].FromID
		}
		userInfos, err := mowangskuser.FindSimpleMap(fromUserIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		resp.Data = make([]liveIncomeItem, 0, len(revenueList))
		for i := range revenueList {
			resp.Data = append(resp.Data, liveIncomeItem{
				UserID:      revenueList[i].FromID,
				Revenue:     util.Float2DP(revenueList[i].Income),
				Status:      revenueList[i].Status,
				ConfirmTime: revenueList[i].ConfirmTime,
				Title:       revenueList[i].IncomeTitle(isCreatorSelf),
			})

			if u := userInfos[revenueList[i].FromID]; u != nil {
				resp.Data[i].Username = u.Username
			}
		}
	}

	return resp, nil
}
