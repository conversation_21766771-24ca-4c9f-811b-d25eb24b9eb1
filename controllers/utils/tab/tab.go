package tab

import (
	"slices"

	"github.com/spaolacci/murmur3"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ExperimentGroup 实验组类型
type ExperimentGroup int

// ExperimentGroup 实验组类型
const (
	ExperimentGroupControl ExperimentGroup = 0 // 对照组
	ExperimentGroupA       ExperimentGroup = 1 // 实验组 A：展示推荐 Tab
	ExperimentGroupB       ExperimentGroup = 2 // 实验组 B：热门 Tab 使用算法推荐
)

// IsShowRecommendTab 判断能否展示推荐 tab
func IsShowRecommendTab(c *handler.Context) bool {
	e := c.Equip()
	// WORKAROUND: 安卓 < 6.3.6 和 iOS < 6.3.6 的版本上报的埋点数据有误，因此直接返回常规分区
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{Android: "6.3.6", IOS: "6.3.6"}) {
		return false
	}

	if !isEnablePersonalizedRecommend(c.UserContext(), c.BUVID(), c.UserID()) {
		return false
	}

	// 只有实验组 A 才展示推荐 tab
	experimentGroup := GetLiveFeedExperimentGroup(c.BUVID(), c.UserID())
	return experimentGroup == ExperimentGroupA
}

// GetLiveFeedExperimentGroup 获取用户所在的直播 Feed 流实验组
func GetLiveFeedExperimentGroup(buvid string, userID int64) ExperimentGroup {
	if buvid == "" {
		return ExperimentGroupControl
	}

	// 如果用户 ID 在白名单中，进入对应的实验组
	if userID > 0 {
		// 检查是否在实验组 A 白名单中
		if len(config.Conf.Params.LiveFeed.TabABAllowListUserIDsGroupA) > 0 &&
			slices.Contains(config.Conf.Params.LiveFeed.TabABAllowListUserIDsGroupA, userID) {
			return ExperimentGroupA
		}
		// 检查是否在实验组 B 白名单中
		if len(config.Conf.Params.LiveFeed.TabABAllowListUserIDsGroupB) > 0 &&
			slices.Contains(config.Conf.Params.LiveFeed.TabABAllowListUserIDsGroupB, userID) {
			return ExperimentGroupB
		}
	}

	// 实验盐值和分桶信息（从配置中读取）
	salt := config.Conf.Params.LiveFeed.TabABSalt
	bucketCount := config.Conf.Params.LiveFeed.TabABBucketCount
	bucketIDsGroupA := config.Conf.Params.LiveFeed.TabABBucketIDsGroupA
	bucketIDsGroupB := config.Conf.Params.LiveFeed.TabABBucketIDsGroupB

	if salt == "" || bucketCount <= 0 {
		return ExperimentGroupControl
	}

	// 计算 hash 值对桶数取模，得到用户所在的桶 ID
	bucketID := int(murmur3.Sum32([]byte(buvid+salt)) % uint32(bucketCount))

	// 检查用户所在的桶 ID 是否在实验组桶列表中
	if len(bucketIDsGroupA) > 0 && slices.Contains(bucketIDsGroupA, bucketID) {
		return ExperimentGroupA
	}
	if len(bucketIDsGroupB) > 0 && slices.Contains(bucketIDsGroupB, bucketID) {
		return ExperimentGroupB
	}

	return ExperimentGroupControl
}

// isEnablePersonalizedRecommend 判断用户是否开启个性化推荐开关
// 默认开启
func isEnablePersonalizedRecommend(uc mrpc.UserContext, buvid string, userID int64) bool {
	userConfig, err := userapi.GetUserConfig(uc, userID, buvid)
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": userID, "buvid": buvid}).Errorf("获取用户配置出错: %v", err)
		return true
	}

	if userConfig != nil && userConfig.AppConfig.PersonalizedRecommend != nil {
		return *userConfig.AppConfig.PersonalizedRecommend == userapi.ConfigEnable
	}

	return true
}
