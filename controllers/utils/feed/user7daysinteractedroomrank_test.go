package feed

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestListUser7DaysInteractedRoomRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	var (
		testUserID = int64(123)
	)
	require.NoError(service.LRURedis.Del(
		keys.KeyUser7DaysListenLiveTotalDuration1.Format(testUserID),
		keys.KeyUser7DaysRoomFreeGiftNum1.Format(testUserID)).Err())
	service.Cache5Min.Delete(keys.LocalKeyUser7DaysCreatorRank.Format(testUserID))
	_, err := livegifts.Collection().DeleteMany(context.Background(), bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = livelistenlogs.Collection().DeleteMany(context.Background(), bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = livelistenlogs.Collection().InsertMany(context.Background(), []any{
		// rank 2
		livelistenlogs.LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    22334,
			Duration:  7 * time.Hour.Milliseconds(),
		},
		// rank 2
		livelistenlogs.LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    22335,
			Duration:  6 * time.Hour.Milliseconds(),
		},
		// rank 1
		livelistenlogs.LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    18113499,
			Duration:  7 * time.Hour.Milliseconds(),
		},
		// 不在排行榜中，但是收听时长足够
		livelistenlogs.LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    223344,
			Duration:  7 * time.Hour.Milliseconds(),
		},
	})
	require.NoError(err)
	_, err = room.Collection().UpdateMany(context.Background(), bson.M{"room_id": bson.M{"$in": []int64{22334, 22335, 18113499, 223344}}}, bson.M{"$set": bson.M{"status.open": room.StatusOpenTrue}})
	require.NoError(err)

	rs, err := ListUser7DaysInteractedRoomRank(testUserID, nil)
	require.NoError(err)
	require.Len(rs, 5)
	assert.EqualValues(18113499, rs[0].RoomID)
	assert.EqualValues(22334, rs[1].RoomID)
	assert.EqualValues(22335, rs[2].RoomID)
	assert.EqualValues(100000010, rs[3].RoomID)
	assert.EqualValues(223344, rs[4].RoomID)
}

func TestIsInteractedIn7Days(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	var (
		testUserID = int64(123)
	)
	require.NoError(service.LRURedis.Del(
		keys.KeyUser7DaysListenLiveTotalDuration1.Format(testUserID),
		keys.LocalKeyUser7DaysCreatorRank.Format(testUserID)).Err())
	_, err := livelistenlogs.Collection().DeleteMany(context.Background(), bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = livelistenlogs.Collection().InsertMany(context.Background(), []any{
		livelistenlogs.LiveListenLog{
			StartTime: goutil.NewTimeUnixMilli(goutil.TimeNow().AddDate(0, 0, -1)),
			LastTime:  goutil.NewTimeUnixMilli(goutil.TimeNow().Add(-time.Hour)),
			UserID:    testUserID,
			RoomID:    223344,
			Duration:  7 * time.Hour.Milliseconds(),
		},
	})
	require.NoError(err)

	ok, err := IsInteractedIn7Days(testUserID, 223344)
	require.NoError(err)
	assert.True(ok)

	ok, err = IsInteractedIn7Days(testUserID, 22335)
	require.NoError(err)
	assert.True(ok)

	ok, err = IsInteractedIn7Days(testUserID, 1)
	require.NoError(err)
	assert.False(ok)
}
