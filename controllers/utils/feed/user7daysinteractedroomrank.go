package feed

import (
	"math"
	"slices"
	"sort"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/liveuser7daysrewardcreatorrank"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

type creatorRankInfo struct {
	room          *room.Simple
	rank          int64 // 排名
	totalDuration int64 // 收听总时长，单位：毫秒
	roomScore     int64 // 直播间热度
}

// ListUser7DaysInteractedRoomRank 获取用户最近 7 天互动的直播间排行
func ListUser7DaysInteractedRoomRank(userID int64, mongoOpt *options.FindOptions, opt ...*room.FindOptions) ([]*room.Simple, error) {
	ranks, err := liveuser7daysrewardcreatorrank.ListUser7DaysCreatorRank(userID)
	if err != nil {
		return nil, err
	}
	totalDurations, err := livelistenlogs.ListUser7DaysListenLiveTotalDuration(userID)
	if err != nil {
		return nil, err
	}
	roomsWithGiftNum, err := livegifts.ListUser7DaysRoomFreeGiftNum(userID)
	if err != nil {
		return nil, err
	}
	followedCreatorIDs, err := attentionuser.AllFollowedCreatorIDs(userID)
	if err != nil {
		return nil, err
	}

	roomIDs := make([]int64, 0, len(totalDurations)+len(ranks)+len(roomsWithGiftNum))
	rankMap := make(map[int64]*liveuser7daysrewardcreatorrank.LiveUser7DaysRewardCreatorRank, len(ranks))
	for _, rank := range ranks {
		roomIDs = append(roomIDs, rank.RoomID)
		rankMap[rank.RoomID] = rank
	}
	totalDurationMap := make(map[int64]*livelistenlogs.ListenTotalDuration, len(totalDurations))
	for _, totalDuration := range totalDurations {
		roomIDs = append(roomIDs, totalDuration.RoomID)
		totalDurationMap[totalDuration.RoomID] = totalDuration
	}
	for _, gn := range roomsWithGiftNum {
		roomIDs = append(roomIDs, gn.RoomID)
	}

	rooms, err := room.ListSimples(bson.M{
		"room_id": bson.M{
			"$in": sets.Uniq(roomIDs),
		},
		"creator_id": bson.M{
			"$nin": followedCreatorIDs,
		},
		"status.open": room.StatusOpenTrue,
	}, mongoOpt, opt...)
	if err != nil {
		return nil, err
	}

	creatorRanks := make([]*creatorRankInfo, 0, len(rooms))
	for _, r := range rooms {
		creatorRank := &creatorRankInfo{
			room:      r,
			roomScore: r.Statistics.Score,
		}
		if rank, ok := rankMap[r.RoomID]; ok {
			creatorRank.rank = rank.Rank
		} else {
			creatorRank.rank = math.MaxInt64 // 不在排行榜中，排名最低，所以这里设置为最大值方便后续排序
		}
		if totalDuration, ok := totalDurationMap[r.RoomID]; ok {
			creatorRank.totalDuration = totalDuration.TotalDuration
		}
		creatorRanks = append(creatorRanks, creatorRank)
	}
	sort.Slice(creatorRanks, func(i, j int) bool {
		if creatorRanks[i].rank != creatorRanks[j].rank {
			return creatorRanks[i].rank < creatorRanks[j].rank
		}
		if creatorRanks[i].totalDuration != creatorRanks[j].totalDuration {
			return creatorRanks[i].totalDuration > creatorRanks[j].totalDuration
		}
		if creatorRanks[i].roomScore != creatorRanks[j].roomScore {
			return creatorRanks[i].roomScore > creatorRanks[j].roomScore
		}
		return creatorRanks[i].room.RoomID < creatorRanks[j].room.RoomID
	})

	rooms = make([]*room.Simple, 0, len(creatorRanks))
	for _, creatorRank := range creatorRanks {
		rooms = append(rooms, creatorRank.room)
	}
	return rooms, nil
}

// IsInteractedIn7Days 用户是否在最近 7 天内与直播间互动过
func IsInteractedIn7Days(userID, roomID int64) (bool, error) {
	ranks, err := liveuser7daysrewardcreatorrank.ListUser7DaysCreatorRank(userID)
	if err != nil {
		return false, err
	}
	if slices.ContainsFunc(ranks, func(rank *liveuser7daysrewardcreatorrank.LiveUser7DaysRewardCreatorRank) bool {
		return rank.RoomID == roomID
	}) {
		return true, nil
	}
	totalDurations, err := livelistenlogs.ListUser7DaysListenLiveTotalDuration(userID)
	if err != nil {
		return false, err
	}
	if slices.ContainsFunc(totalDurations, func(totalDuration *livelistenlogs.ListenTotalDuration) bool {
		return totalDuration.RoomID == roomID
	}) {
		return true, nil
	}
	roomsWithGiftNum, err := livegifts.ListUser7DaysRoomFreeGiftNum(userID)
	if err != nil {
		return false, err
	}
	if slices.ContainsFunc(roomsWithGiftNum, func(gn livegifts.RoomWithGiftNum) bool {
		return gn.RoomID == roomID
	}) {
		return true, nil
	}

	return false, nil
}
