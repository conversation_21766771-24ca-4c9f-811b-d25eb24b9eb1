package feed

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/liveuser7daysrewardcreatorrank"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type testMedalInfo struct {
	userID     int64
	creatorID  int64
	roomID     int64
	isSuperFan bool
}

func createTestMedal(t *testing.T, m *testMedalInfo) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	medal := &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID:    m.roomID,
			CreatorID: m.creatorID,
			UserID:    m.userID,
			Status:    livemedal.StatusOwned,
		},
	}
	if m.isSuperFan {
		medal.SuperFan = &livemedal.SuperFan{
			ExpireTime: goutil.TimeNow().Add(time.Hour * 24).Unix(),
		}
	}
	_, err := livemedal.Collection().InsertOne(ctx, medal)
	require.NoError(t, err)
	t.Cleanup(func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := livemedal.Collection().DeleteOne(ctx, bson.M{"user_id": m.userID})
		require.NoError(t, err)
	})
}

func TestListMedalAndFollowedRoomsRank(t *testing.T) {
	testUserID := int64(99999)
	test3OpenRooms, err := room.FindAll(
		bson.M{
			"status.open": room.StatusOpenTrue,
			"creator_id":  bson.M{"$gt": 0},
			"limit":       bson.M{"$exists": false},
		},
		options.Find().SetLimit(3),
	)
	require.NoError(t, err)
	testClosedRoom, err := room.FindOneSimple(bson.M{
		"status.open": room.StatusOpenFalse,
		"creator_id":  bson.M{"$gt": 0},
		"limit":       bson.M{"$exists": false},
	})
	require.NoError(t, err)
	testLimitedRoom, err := room.FindOneSimple(bson.M{
		"status.open": room.StatusOpenTrue,
		"creator_id":  bson.M{"$gt": 0},
		"limit":       bson.M{"$exists": true},
	})
	require.NoError(t, err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(t, err)

	followedUserIDs, err := attentionuser.AllFollowedUserIDs(testUserID)
	require.NoError(t, err)
	for _, followedUserID := range followedUserIDs {
		err := attentionuser.UnFollow(testUserID, followedUserID)
		require.NoError(t, err)
	}

	service.Cache5Min.Set(
		keys.LocalKeyUser7DaysCreatorRank.Format(testUserID),
		[]*liveuser7daysrewardcreatorrank.LiveUser7DaysRewardCreatorRank{
			{
				UserID:    testUserID,
				CreatorID: test3OpenRooms[2].CreatorID,
				RoomID:    test3OpenRooms[2].RoomID,
				Rank:      1,
			},
			// 第二位和第三位设置相同的打赏量排名，以便测试次级排序
			{
				UserID:    testUserID,
				CreatorID: test3OpenRooms[1].CreatorID,
				RoomID:    test3OpenRooms[1].RoomID,
				Rank:      2,
			},
			{
				UserID:    testUserID,
				CreatorID: test3OpenRooms[0].CreatorID,
				RoomID:    test3OpenRooms[0].RoomID,
				Rank:      2,
			},
		},
		0,
	)

	t.Run("NoMedalNoFollowed", func(t *testing.T) {
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Empty(t, ranks)
	})

	t.Run("OnlyMedal", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  test3OpenRooms[0].CreatorID,
			roomID:     test3OpenRooms[0].RoomID,
			isSuperFan: false,
		})
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Len(t, ranks, 1)
		assert.Equal(t, test3OpenRooms[0].RoomID, ranks[0].Room.RoomID)
		assert.Equal(t, RankTypeMedal, ranks[0].RankType)
	})

	t.Run("OnlySuperFan", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  test3OpenRooms[1].CreatorID,
			roomID:     test3OpenRooms[1].RoomID,
			isSuperFan: true,
		})
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Len(t, ranks, 1)
		assert.Equal(t, test3OpenRooms[1].RoomID, ranks[0].Room.RoomID)
		assert.Equal(t, RankTypeSuperFan, ranks[0].RankType)
	})

	t.Run("OnlyFollowed", func(t *testing.T) {
		err := attentionuser.Follow(testUserID, test3OpenRooms[2].CreatorID)
		require.NoError(t, err)
		defer attentionuser.UnFollow(testUserID, test3OpenRooms[2].CreatorID)
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Len(t, ranks, 1)
		assert.Equal(t, RankTypeFollow, ranks[0].RankType)
	})

	t.Run("ClosedRoomShouldNotInclude", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  testClosedRoom.CreatorID,
			roomID:     testClosedRoom.RoomID,
			isSuperFan: false,
		})
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Empty(t, ranks)
	})

	t.Run("LimitedRoomShouldNotInclude", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  testLimitedRoom.CreatorID,
			roomID:     testLimitedRoom.RoomID,
			isSuperFan: false,
		})
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Empty(t, ranks)
	})

	t.Run("EachOneOfAllTypes", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  test3OpenRooms[0].CreatorID,
			roomID:     test3OpenRooms[0].RoomID,
			isSuperFan: false,
		})
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  test3OpenRooms[1].CreatorID,
			roomID:     test3OpenRooms[1].RoomID,
			isSuperFan: true,
		})
		err := attentionuser.Follow(testUserID, test3OpenRooms[2].CreatorID)
		require.NoError(t, err)
		defer attentionuser.UnFollow(testUserID, test3OpenRooms[2].CreatorID)
		ranks, err := ListMedalAndFollowedRoomsRank(testUserID, nil)
		require.NoError(t, err)
		assert.Len(t, ranks, 3)
		for _, room := range ranks {
			switch room.RankType {
			case RankTypeMedal:
				assert.Equal(t, test3OpenRooms[0].RoomID, room.Room.RoomID)
			case RankTypeSuperFan:
				assert.Equal(t, test3OpenRooms[1].RoomID, room.Room.RoomID)
			case RankTypeFollow:
				assert.Equal(t, test3OpenRooms[2].RoomID, room.Room.RoomID)
			}
		}
		// 第一位打赏量最多，第二位第三位打赏量相同，按亲密度、热度、开播时间排序
		assert.Equal(t, test3OpenRooms[2].RoomID, ranks[0].Room.RoomID)
		assert.Equal(t, test3OpenRooms[0].RoomID, ranks[1].Room.RoomID)
		assert.Equal(t, test3OpenRooms[1].RoomID, ranks[2].Room.RoomID)
	})
}

func TestFindMedalAndFollowedRankType(t *testing.T) {
	testUserID := int64(99999)
	testRoom, err := room.FindOneSimple(bson.M{
		"creator_id": bson.M{"$gt": 0},
		"limit":      bson.M{"$exists": false},
	})
	require.NoError(t, err)

	t.Run("CreatorSelfShouldReturnZero", func(t *testing.T) {
		rankType, err := FindMedalAndFollowedRankType(testRoom.CreatorID, testRoom)
		require.NoError(t, err)
		assert.Zero(t, rankType)
	})

	t.Run("StrangerShouldReturnZero", func(t *testing.T) {
		rankType, err := FindMedalAndFollowedRankType(testUserID, testRoom)
		require.NoError(t, err)
		assert.Zero(t, rankType)
	})

	t.Run("FollowerShouldReturnRankTypeFollow", func(t *testing.T) {
		err := attentionuser.Follow(testUserID, testRoom.CreatorID)
		require.NoError(t, err)
		defer attentionuser.UnFollow(testUserID, testRoom.CreatorID)
		rankType, err := FindMedalAndFollowedRankType(testUserID, testRoom)
		require.NoError(t, err)
		assert.Equal(t, RankTypeFollow, rankType)
	})

	t.Run("NormalMedalShouldReturnRankTypeMedal", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  testRoom.CreatorID,
			roomID:     testRoom.RoomID,
			isSuperFan: false,
		})
		rankType, err := FindMedalAndFollowedRankType(testUserID, testRoom)
		require.NoError(t, err)
		assert.Equal(t, RankTypeMedal, rankType)
	})

	t.Run("SuperFanShouldReturnRankTypeSuperFan", func(t *testing.T) {
		createTestMedal(t, &testMedalInfo{
			userID:     testUserID,
			creatorID:  testRoom.CreatorID,
			roomID:     testRoom.RoomID,
			isSuperFan: true,
		})
		rankType, err := FindMedalAndFollowedRankType(testUserID, testRoom)
		require.NoError(t, err)
		assert.Equal(t, RankTypeSuperFan, rankType)
	})
}
