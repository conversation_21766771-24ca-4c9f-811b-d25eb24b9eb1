package feed

import (
	"math"
	"sort"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/liveuser7daysrewardcreatorrank"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
)

// 排名类型
const (
	// RankTypeSuperFan 超粉
	RankTypeSuperFan = iota + 1
	// RankTypeMedal 粉丝牌
	RankTypeMedal
	// RankTypeFollow 关注
	RankTypeFollow
)

// MedalAndFollowedRank 用户已开通超粉、已点亮粉丝牌和已关注的直播间排名
type MedalAndFollowedRank struct {
	Room     *room.Simple // 直播间
	RankType int          // 排名类型
}

type medalAndFollowedRankDetail struct {
	CreatorID  int64        // 主播 ID
	Room       *room.Simple // 直播间
	RankType   int          // 排名类型
	RewardRank int64        // 打赏量排名，主播在用户近 7 日打赏的所有主播中的排名，从 1 开始，排名越靠前打赏量越高
	Point      int64        // 亲密度
	Score      float64      // 热度
	OpenTime   int64        // 开播时间，毫秒时间戳
}

// ListMedalAndFollowedRoomsRank 获取用户已开通超粉、已点亮粉丝牌和已关注的直播间排名
func ListMedalAndFollowedRoomsRank(userID int64, mongoOpt *options.FindOptions, opt ...*room.FindOptions) ([]MedalAndFollowedRank, error) {
	medals, err := livemedal.ListUserAll(userID)
	if err != nil {
		return nil, err
	}

	followedCreatorIDs, err := attentionuser.AllFollowedCreatorIDs(userID)
	if err != nil {
		return nil, err
	}

	rankDetailMap := make(map[int64]*medalAndFollowedRankDetail, len(medals)+len(followedCreatorIDs))
	for _, medal := range medals {
		rankDetail := &medalAndFollowedRankDetail{
			CreatorID:  medal.CreatorID,
			RewardRank: math.MaxInt64, // 默认不在打赏排行榜中，所以这里设置为最大值方便后续排序
			Point:      medal.Point,
		}
		if livemedal.IsSuperFanActive(medal.SuperFan) {
			rankDetail.RankType = RankTypeSuperFan
		} else {
			rankDetail.RankType = RankTypeMedal
		}
		rankDetailMap[medal.CreatorID] = rankDetail
	}
	for _, creatorID := range followedCreatorIDs {
		// 推荐标签优先级：超粉 > 粉丝牌 > 关注，所以此处不需要覆盖超粉和粉丝牌的推荐标签
		if _, ok := rankDetailMap[creatorID]; !ok {
			rankDetailMap[creatorID] = &medalAndFollowedRankDetail{
				CreatorID:  creatorID,
				RankType:   RankTypeFollow,
				RewardRank: math.MaxInt64, // 默认不在打赏排行榜中，所以这里设置为最大值方便后续排序
			}
		}
	}

	rewardRanks, err := liveuser7daysrewardcreatorrank.ListUser7DaysCreatorRank(userID)
	if err != nil {
		return nil, err
	}
	for _, rewardRank := range rewardRanks {
		if rankDetail, ok := rankDetailMap[rewardRank.CreatorID]; ok {
			rankDetail.RewardRank = rewardRank.Rank
		}
	}

	creatorIDs := make([]int64, 0, len(rankDetailMap))
	for creatorID := range rankDetailMap {
		creatorIDs = append(creatorIDs, creatorID)
	}
	filter := bson.M{
		"creator_id":  bson.M{"$in": creatorIDs},
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}
	openRooms, err := room.ListSimples(filter, mongoOpt, opt...)
	if err != nil {
		return nil, err
	}
	rankDetails := make([]*medalAndFollowedRankDetail, 0, len(openRooms))
	for _, room := range openRooms {
		rankDetail, ok := rankDetailMap[room.CreatorID]
		if !ok {
			continue
		}
		rankDetail.Room = room
		rankDetail.Score = room.Status.Score
		rankDetail.OpenTime = room.Status.OpenTime
		rankDetails = append(rankDetails, rankDetail)
	}
	sort.SliceStable(rankDetails, func(i, j int) bool {
		a, b := rankDetails[i], rankDetails[j]
		// 排序优先级：打赏量排名（升序） > 亲密度（降序） > 热度（降序） > 开播时间（降序）
		if a.RewardRank != b.RewardRank {
			return a.RewardRank < b.RewardRank
		}
		if a.Point != b.Point {
			return a.Point > b.Point
		}
		if a.Score != b.Score {
			return a.Score > b.Score
		}
		return a.OpenTime >= b.OpenTime
	})

	ranks := make([]MedalAndFollowedRank, 0, len(rankDetails))
	for _, rankDetail := range rankDetails {
		ranks = append(ranks, MedalAndFollowedRank{
			Room:     rankDetail.Room,
			RankType: rankDetail.RankType,
		})
	}
	return ranks, nil
}

// FindMedalAndFollowedRankType 获取直播间属于已开通超粉、已点亮粉丝牌和已关注中的哪一种，不属于这三种则返回 0
func FindMedalAndFollowedRankType(userID int64, room *room.Simple) (int, error) {
	if userID == room.CreatorID {
		return 0, nil
	}
	medal, err := livemedal.FindOwnedMedal(userID, room.RoomID)
	if err != nil {
		return 0, err
	}
	if medal != nil {
		if livemedal.IsSuperFanActive(medal.SuperFan) {
			return RankTypeSuperFan, nil
		}
		return RankTypeMedal, nil
	}
	// TODO: 确认一下是否可以使用 room.Statistics.Attention
	followed, err := attentionuser.HasFollowed(userID, room.CreatorID)
	if err != nil {
		return 0, err
	}
	if followed {
		return RankTypeFollow, nil
	}
	return 0, nil
}
