package utils

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestCheckPotentialHighnessQualification(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key := keys.KeyUsersPotentialHighness0.Format()
	require.NoError(service.Redis.Del(key).Err())

	message := liveSpendMessage{
		UserID:               9074509,
		userTodayConsumption: 2_000_000,
	}
	// 测试不符合潜力上神条件
	require.NotPanics(func() {
		message.checkPotentialHighnessQualification()
	})
	exists, err := service.Redis.SIsMember(key, message.UserID).Result()
	require.NoError(err)
	assert.False(exists)

	// 测试历史消费大于潜力上神条件
	message.userTodayConsumption = 5_000_000
	require.NotPanics(func() {
		message.checkPotentialHighnessQualification()
	})
	exists, err = service.Redis.SIsMember(key, message.UserID).Result()
	require.NoError(err)
	assert.False(exists)

	// 测试符合潜力上神条件加入名单
	message.Spend = 3_000_000
	require.NotPanics(func() {
		message.checkPotentialHighnessQualification()
	})
	exists, err = service.Redis.SIsMember(key, message.UserID).Result()
	require.NoError(err)
	assert.True(exists)
}

func TestTryOpenHighness(t *testing.T) {
	require := require.New(t)
	cancel := mrpc.SetMock("sso://vip/buy-live-highness", func(input interface{}) (output interface{}, err error) {
		var uv struct {
			VipInfo vip.UserVip `json:"vip_info"`
		}
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userID, ok := body["user_id"].(int64)
		require.True(ok)
		vipID, ok := body["vip_id"].(int64)
		require.True(ok)
		require.EqualValues(8, vipID)
		isRegistration, ok := body["is_registration"].(bool)
		require.True(ok)
		require.True(isRegistration)

		uv.VipInfo = vip.UserVip{
			UserID:     userID,
			VipID:      vipID,
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		}

		return uv, nil
	})
	defer cancel()

	message := liveSpendMessage{
		UserID:               9074509,
		userTodayConsumption: vip.HighnessOpenCoin,
		userConsumptionTime:  goutil.TimeNow(),
	}
	require.True(message.tryOpenHighness())

	message.userTodayConsumption = 0
	require.False(message.tryOpenHighness())

	config.Conf.AB["open_highness"] = 1747670400
	require.False(message.tryOpenHighness())
}

func TestRenewHighness(t *testing.T) {
	require := require.New(t)

	cancel := mrpc.SetMock("sso://vip/buy-live-highness", func(input interface{}) (output interface{}, err error) {
		var uv struct {
			VipInfo vip.UserVip `json:"vip_info"`
		}
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userID, ok := body["user_id"].(int64)
		require.True(ok)
		vipID, ok := body["vip_id"].(int64)
		require.True(ok)
		require.EqualValues(8, vipID)
		isRegistration, ok := body["is_registration"].(bool)
		require.True(ok)
		require.False(isRegistration)

		uv.VipInfo = vip.UserVip{
			UserID:     userID,
			VipID:      vipID,
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		}

		return uv, nil
	})
	defer cancel()
	message := liveSpendMessage{
		UserID:     9074509,
		renewCount: 1,
	}
	require.NotPanics(func() {
		message.renewHighness()
	})
}

func TestAddGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userappearance.Collection().DeleteOne(ctx, bson.M{"user_id": 907450912})
	require.NoError(err)

	message := liveSpendMessage{
		UserID:          907450912,
		openCount:       1,
		finalExpireTime: goutil.TimeNow().Unix(),
	}
	require.NotPanics(func() {
		message.addGift()
	})

	var userItem useritems.UserItem
	err = useritems.Collection().FindOne(ctx, bson.M{
		"user_id": 907450912,
		"gift_id": vip.HighnessGiftID,
	}).Decode(&userItem)
	require.NoError(err)
	assert.EqualValues(1, userItem.Num)
}

func TestAddPrivilege(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userappearance.Collection().DeleteOne(ctx, bson.M{"user_id": 907450912})
	require.NoError(err)
	_, err = userstatus.UserMetaCollection().DeleteOne(ctx, bson.M{"user_id": 907450912})
	require.NoError(err)

	message := liveSpendMessage{
		UserID:          907450912,
		renewCount:      1,
		finalExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
	}
	message.addPrivilege()

	appearanceTypes := []int{
		appearance.TypeVehicle, appearance.TypeAvatarFrame, appearance.TypeCardFrame, appearance.TypeMessageBubble,
	}
	opts := options.Find().
		SetSort(bson.M{"appearance_id": 1})
	cur, err := userappearance.Collection().Find(ctx, bson.M{
		"user_id": 907450912,
		"from":    appearance.FromHighness,
		"type": bson.M{
			"$in": appearanceTypes,
		},
	}, opts)
	require.NoError(err)
	defer cur.Close(ctx)
	var aItem []userappearance.UserAppearance
	err = cur.All(ctx, &aItem)
	require.NoError(err)
	require.Len(aItem, 4)

	var res userstatus.GeneralStatus
	err = userstatus.UserMetaCollection().FindOne(ctx, bson.M{"user_id": 907450912},
		options.FindOne().SetProjection(bson.M{
			"noble_horn_num": 1,
			"recommend_num":  1,
		})).
		Decode(&res)
	require.NoError(err)
	assert.EqualValues(5, res.NobleHornNum)
	assert.EqualValues(3, *res.RecommendNum)
}

func TestSendSystemMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.Len(systemMsgList, 3)
		assert.EqualValues("成功开通上神贵族", systemMsgList[0].Title)
		assert.EqualValues("成功续费上神贵族", systemMsgList[1].Title)
		assert.EqualValues("成功续费上神贵族", systemMsgList[2].Title)
		content := fmt.Sprintf("尊敬的用户，恭喜您成功续费上神贵族，由于您的上神有效期已达上限 2022-06-07 23:59:59，本次续费不再延长有效期，"+
			"相关特权道具和上神 · 降临礼物已为您发放，更多问题请查看<a href=\"%s\">猫耳FM直播贵族说明</a>",
			config.Conf.Params.NobleParams.NobleGuideURL)
		assert.Equal(content, systemMsgList[2].Content)
		return "success", nil
	})
	defer cancel()

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 6, 6, 12, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	expireTime := int64(1654617599)
	record := []buyHighnessRecord{
		{open: true, expireTime: expireTime},
		{open: false, expireTime: expireTime},
		{open: false, expireTime: expireTime},
	}
	message := liveSpendMessage{
		UserID:                907450912,
		buyHighnessRecordList: record,
	}
	message.sendSystemMsg()
}

func TestHighnessSpendMessage_sendLevelGte85ConsumptionPack(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cleanup := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		count++
		return nil, nil
	})
	defer cleanup()

	user, err := liveuser.FindOneSimple(bson.M{"contribution": bson.M{"$gt": usercommon.LevelStart[85]}}, nil)
	require.NoError(err)
	require.NotNil(user)
	now := goutil.TimeNow()
	err = service.Redis.Del(keys.KeyUsersLevelGte85Consumption1.Format(now.Format("200601"))).Err()
	require.NoError(err)
	_, err = liveuser.AddConsumptionByLevelGte85InCurrentMonth(now, user.UserID(), 9999)
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": user.UserID(), "type": bson.M{"$in": bson.A{
		appearance.TypeAvatarFrame,
		appearance.TypeMessageBubble,
	}}})
	require.NoError(err)
	_, err = appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": bson.A{
		appearance.AppearanceID40250AvatarFrameForLv85,
		appearance.AppearanceID10113MessageBubbleForLv85,
	}}})
	require.NoError(err)
	_, err = appearance.Collection().InsertMany(ctx, bson.A{
		bson.M{
			"id":   appearance.AppearanceID40250AvatarFrameForLv85,
			"type": appearance.TypeAvatarFrame,
		},
		bson.M{
			"id":   appearance.AppearanceID10113MessageBubbleForLv85,
			"type": appearance.TypeMessageBubble,
		},
	})
	require.NoError(err)

	config.Conf.AB["open_level_gte85_consumption_pack_time"] = goutil.TimeNow().Add(-time.Hour).Unix()
	defer delete(config.Conf.AB, "open_level_gte85_consumption_pack_time")

	message := liveSpendMessage{
		UserID:     user.UserID(),
		Spend:      1,
		CreateTime: now.Unix(),
	}
	message.sendLevelGte85ConsumptionPack()
	assert.Equal(1, count)
	require.NoError(service.Redis.Del(keys.KeyUsersWornAppearancesSets1.Format(user.UserID())).Err())
	avatarFrame, err := userappearance.FindOne(bson.M{"user_id": user.UserID(), "type": appearance.TypeAvatarFrame}, nil)
	require.NoError(err)
	assert.NotNil(avatarFrame)
	bubble, err := userappearance.FindOne(bson.M{"user_id": user.UserID(), "type": appearance.TypeMessageBubble}, nil)
	require.NoError(err)
	assert.NotNil(bubble)
}
