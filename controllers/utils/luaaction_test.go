package utils

import (
	"bytes"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestCheckInitScript(t *testing.T) {
	require := require.New(t)

	service.Cache5Min.Delete(keyLuaFaaSInitScript)
	require.NoError(CheckInitScript())
	// with cache
	require.NoError(CheckInitScript())
}

func TestActionLuaRunAction(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	config.Conf.Lua.InitScript = `
local logger = require('logger')
local crypto = require('crypto')
local json = require('json')
local redis = require('redis')
local regexp = require('regexp')
local mongo = require('mongo')

function run_action(ctx)
  logger.debug(config)
  logger.debug(ctx.params)
  logger.debug(ctx.var)
  logger.debug('user id: ', ctx.req:get_user_id())
  logger.debug('request body: ', ctx.req:get_body_data())
  logger.debug('uri args: ', ctx.req:get_uri_args())
  logger.debug('post args: ', ctx.req:get_post_args())
  logger.debug('db name: ', db:getName())
  if ctx.var.request_method == 'POST' then
    return ctx.req:get_post_args()
  end
  return 'success'
end
`

	service.Cache5Min.Flush()
	defer service.Cache5Min.Flush()
	c := handler.NewTestContext("POST", "/echo", true, bytes.NewBufferString(`a=1`))
	c.C.Request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	form := url.Values{}
	form.Set("a", "1")
	c.C.Request.Form = form
	res, err := ActionLuaRunAction(c, "run_action")
	require.NoError(err)
	assert.Equal(map[string]interface{}{"a": "1"}, res)

	c = handler.NewTestContext("GET", "/?a=1", false, nil)
	res, err = ActionLuaRunAction(c, "run_action")
	require.NoError(err)
	assert.Equal("success", res)
}
