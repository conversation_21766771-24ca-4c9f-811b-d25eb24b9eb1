package utils

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"sync"

	lua "github.com/yuin/gopher-lua"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/luavm"
	gluactxreq "github.com/MiaoSiLa/live-service/luavm/ctxreq"
	luautil "github.com/MiaoSiLa/live-service/luavm/util"
	"github.com/MiaoSiLa/live-service/models/mongodb/activities"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

var (
	errLuaRunActionNotFound = errors.New("lua run_action not found")

	initScriptMutex sync.Mutex
)

const keyLuaFaaSInitScript = "luaFaaSInitScript"

// CheckInitScript check lua pool init script
func CheckInitScript() error {
	_, ok := service.Cache5Min.Get(keyLuaFaaSInitScript)
	if ok {
		return nil
	}

	initScriptMutex.Lock()
	defer initScriptMutex.Unlock()

	// check again inside lock
	_, ok = service.Cache5Min.Get(keyLuaFaaSInitScript)
	if ok {
		return nil
	}

	acts, err := activities.List(activities.TypeFaaSLua)
	if err != nil {
		logger.Error(err)
		if luavm.Pool.InitScript == "" {
			return err
		}
		// 成功获取过的话可进行降级
		// PASS
	}

	faas := ""
	for _, act := range acts {
		faas += act.Content + "\n"
	}
	faas = strings.ReplaceAll(config.Conf.Lua.InitScript,
		"{faas}", faas)

	if luavm.Pool.InitScript != faas {
		// reset the lua vm pool
		logger.Debugf("new lua vm pool init script: %s", faas)
		luavm.Pool.Reset(faas)
	}

	service.Cache5Min.SetDefault(keyLuaFaaSInitScript, "1")
	return nil
}

// GetGlobalLuaFunc get lua global function
func GetGlobalLuaFunc(l *lua.LState, funcName string) (*lua.LFunction, error) {
	runAction, ok := l.GetGlobal(funcName).(*lua.LFunction)
	if !ok || runAction == nil {
		return nil, errLuaRunActionNotFound
	}
	return runAction, nil
}

// ActionLuaRunAction run action in lua
func ActionLuaRunAction(c *handler.Context, funcName string, funcs ...func(l *lua.LState, ctx map[string]interface{})) (handler.ActionResponse, error) {
	// TODO: move to handler
	// like OpenResty: ngx.req.*
	params := make(map[string]string, len(c.C.Params))
	for _, param := range c.C.Params {
		params[param.Key] = param.Value
	}
	ctx := map[string]interface{}{
		"params": params,
		"var": map[string]interface{}{
			"request_method": c.C.Request.Method,
			"host":           c.C.Request.Host,
			"uri":            c.C.Request.URL.Path,
			"request_uri":    c.C.Request.URL.Path,
			"query_string":   c.C.Request.URL.RawQuery,
		},
	}

	err := CheckInitScript()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	l := luavm.Pool.Get()
	defer luavm.Pool.Put(l)

	ctx["req"] = gluactxreq.New(l, c)
	for _, f := range funcs {
		f(l, ctx)
	}

	runAction, err := GetGlobalLuaFunc(l, funcName)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	err = l.CallByParam(lua.P{
		Fn:      runAction,
		NRet:    2,
		Protect: true,
	}, luautil.ToLuaValue(l, ctx))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	v := luautil.GetArgs(l, 1)
	// REVIEW: len(v) is always 2 as NRet == 2
	if len(v) == 0 {
		return nil, actionerrors.NewUnknownError(http.StatusBadRequest, "unknown error")
	}
	if len(v) >= 2 && v[1] != nil {
		msg, ok := v[1].(string)
		if !ok {
			msg = "unknown error"
		}
		return nil, actionerrors.NewErrServerInternal(fmt.Errorf("lua error: %v", msg), nil)
	}
	return v[0], nil
}
