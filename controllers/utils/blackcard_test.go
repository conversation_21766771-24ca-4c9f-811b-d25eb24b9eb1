package utils

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcard"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLiveSpendMessage_blackCardConsumption(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(goutil.TimeNow())
	testTimeUnix := nowStampFirstDayOfThisMonth.AddDate(0, 0, 16).Unix()
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTimeUnix, 0)
	})
	defer goutil.SetTimeNow(nil)

	testUserID := int64(3457181)
	date := goutil.BeginningOfDay(goutil.TimeNow())
	key := keys.KeyUserConsumptionThisMonth2.Format(testUserID, date.Unix())
	require.NoError(service.Redis.Del(key).Err())
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userconsumption.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = userconsumption.Collection().InsertMany(ctx, []interface{}{
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, 3).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, 15).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, 16).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, -1).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: nowStampFirstDayOfThisMonth.AddDate(0, 0, -2).Unix()},
	})
	require.NoError(err)

	message := &liveSpendMessage{
		UserID:               3457181,
		userTodayConsumption: 100,
		userConsumptionTime:  goutil.TimeNow(),
	}
	message.blackCardConsumption()
	assert.EqualValues(469, message.userBlackCardConsumption)
}

func TestLiveSpendMessage_processOpenBlackCard(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(goutil.TimeNow())
	testTimeUnix := nowStampFirstDayOfThisMonth.AddDate(0, 0, 16).Unix()
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTimeUnix, 0)
	})
	defer goutil.SetTimeNow(nil)
	message := &liveSpendMessage{
		UserID:                   3457181,
		RoomID:                   1234,
		userConsumptionTime:      goutil.TimeNow(),
		userBlackCardConsumption: 200000,
	}

	called1 := false
	cancel1 := mrpc.SetMock(userapi.URIIMBroadcastUser, func(input interface{}) (output interface{}, err error) {
		called1 = true
		return "success", nil
	})
	defer cancel1()

	expireTime := goutil.BeginningOfMonth(message.userConsumptionTime).AddDate(0, 2, 0).Unix()
	expireTimeStr := time.Unix(expireTime, 0).Format(util.TimeFormatYMDHHMM)
	called2 := false
	cancel2 := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]any)
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		require.Len(systemMsgList, 1)
		assert.EqualValues(3457181, systemMsgList[0].UserID)
		assert.EqualValues("恭喜解锁星曜 V1 身份！", systemMsgList[0].Title)
		assert.EqualValues(fmt.Sprintf("尊敬的用户，您本月在直播间内累计消费已达成星曜 V1 身份解锁条件，星曜用户可在直播间内体验专属特权。本次解锁，您的身份有效期将被保留至 %s。<a href=\"%s\">点击查看特权 >></a>", expireTimeStr, params.BlackCardURL(false)),
			systemMsgList[0].Content)
		called2 = true
		return "success", nil
	})
	defer cancel2()

	called3 := false
	cancel3 := mrpc.SetMock(userapi.URLMinigameDrawPointUpdate, func(input interface{}) (interface{}, error) {
		called3 = true
		return "success", nil
	})
	defer cancel3()

	// 删除测试记录
	require.NoError(liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", message.UserID).Error)

	// 测试开通黑卡 1
	message.processOpenBlackCard()
	require.True(called1)
	require.True(called2)
	require.True(called3)
	// 验证开通记录已生成
	var userBlackCards []liveuserblackcard.LiveUserBlackCard
	err := liveuserblackcard.LiveUserBlackCard{}.DB().Where("user_id = ? AND start_time = ?", message.UserID, message.userConsumptionTime.Unix()).Find(&userBlackCards).Error
	require.NoError(err)
	require.Len(userBlackCards, 1)
	assert.NotEmpty(userBlackCards[0].CreateTime)
	assert.NotEmpty(userBlackCards[0].ModifiedTime)
	assert.Equal(expireTime, userBlackCards[0].ExpireTime)

	called4 := false
	cancel4 := mrpc.SetMock(userapi.URIIMBroadcastUser, func(input interface{}) (output interface{}, err error) {
		called4 = true
		return "success", nil
	})
	defer cancel4()

	called5 := false
	cancel5 := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]any)
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(systemMsgList)
		require.Len(systemMsgList, 2)
		assert.EqualValues(3457181, systemMsgList[0].UserID)
		assert.EqualValues("恭喜解锁星曜 V2 身份！", systemMsgList[0].Title)
		assert.EqualValues(fmt.Sprintf("尊敬的用户，您本月在直播间内累计消费已达成星曜 V2 身份解锁条件，星曜用户可在直播间内体验专属特权。本次解锁，您的身份有效期将被保留至 %s。<a href=\"%s\">点击查看特权 >></a>", expireTimeStr, params.BlackCardURL(false)),
			systemMsgList[0].Content)
		assert.EqualValues("恭喜解锁星曜 V3 身份！", systemMsgList[1].Title)
		assert.EqualValues(fmt.Sprintf("尊敬的用户，您本月在直播间内累计消费已达成星曜 V3 身份解锁条件，星曜用户可在直播间内体验专属特权。本次解锁，您的身份有效期将被保留至 %s。<a href=\"%s\">点击查看特权 >></a>", expireTimeStr, params.BlackCardURL(false)),
			systemMsgList[1].Content)
		called5 = true
		return "success", nil
	})
	defer cancel5()

	called6 := false
	cancel6 := mrpc.SetMock(userapi.URLMinigameDrawPointUpdate, func(input interface{}) (interface{}, error) {
		called6 = true
		return "success", nil
	})
	defer cancel6()

	// 测试一次解锁多个等级的情况（黑卡 1 到黑卡 3）
	message.userBlackCardConsumption = 1000000
	message.processOpenBlackCard()
	require.True(called4)
	require.True(called5)
	require.True(called6)
	// 验证开通记录已生成
	var userBlackCards1 []liveuserblackcard.LiveUserBlackCard
	err = liveuserblackcard.LiveUserBlackCard{}.DB().Where("user_id = ? AND start_time = ?", message.UserID, message.userConsumptionTime.Unix()).Find(&userBlackCards1).Error
	require.NoError(err)
	require.Len(userBlackCards1, 3)
	assert.NotEmpty(userBlackCards1[0].CreateTime)
	assert.NotEmpty(userBlackCards1[0].ModifiedTime)
	assert.Equal(expireTime, userBlackCards1[0].ExpireTime)
}

func TestGetAllBlackCardMap(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	blackCardMap, err := getAllBlackCardMap()
	require.NoError(err)
	require.Len(blackCardMap, 4)
	assert.NotEmpty(blackCardMap[1])
	assert.NotEmpty(blackCardMap[2].Level)
	assert.NotEmpty(blackCardMap[3].Level)
	assert.NotEmpty(blackCardMap[4].Level)
}

func TestLiveSpendMessage_getUserBlackCard(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	timeNow := goutil.TimeNow()
	// 测试用户没有黑卡信息
	testUserID := int64(2334)
	require.NoError(liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testUserID).Error)
	message := &liveSpendMessage{
		UserID:              testUserID,
		userConsumptionTime: timeNow,
	}
	require.NoError(message.getUserBlackCard())
	assert.Equal(0, message.thisMonthUserBlackCardsHighestLevel)
	assert.Equal(0, message.lastMonthUserBlackCardHighestLevel)
	assert.Equal(0, message.currentUserBlackCardLevel)

	// 测试用户有黑卡信息
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(timeNow)
	infos := []liveuserblackcard.LiveUserBlackCard{
		// 上个月的黑卡开通记录
		{
			UserID:      testUserID,
			BlackCardID: 2,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 1, 0).Unix(),
		},
		{
			UserID:      testUserID,
			BlackCardID: 3,
			StartTime:   timeNow.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 1, 0).Unix(),
		},
		// 本月的黑卡开通记录
		{
			UserID:      testUserID,
			BlackCardID: 3,
			StartTime:   timeNow.Unix() - 10,
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
		{
			UserID:      testUserID,
			BlackCardID: 4,
			StartTime:   timeNow.Unix() - 1,
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 2, 0).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))
	require.NoError(message.getUserBlackCard())
	assert.Equal(4, message.thisMonthUserBlackCardsHighestLevel)
	assert.Equal(3, message.lastMonthUserBlackCardHighestLevel)
	assert.Equal(4, message.currentUserBlackCardLevel)
}

func TestLiveSpendMessage_sendBlackCardMessage(t *testing.T) {
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcastUser, func(input interface{}) (output interface{}, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	message := &liveSpendMessage{
		UserID: 3457181,
	}
	// 测试不满足发送黑卡升级弹窗提示的要求时
	message.sendBlackCardMessage()
	require.False(called)

	// 测试满足要求时
	message = &liveSpendMessage{
		UserID:            3457181,
		RoomID:            12345,
		blackCardOpenType: openTypeActivate,
		newLiveBlackCard: &liveblackcard.LiveBlackCard{
			Level:   1,
			Title:   "星曜 V1",
			IconURL: "https://www.test.com/img/icon.png",
		},
	}
	message.sendBlackCardMessage()
	require.True(called)
}

func TestLiveSpendMessage_sendBlackCardSystemMsg(t *testing.T) {
	require := require.New(t)

	called := false
	cancel1 := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		called = true
		return "success", nil
	})
	defer cancel1()

	message := &liveSpendMessage{
		UserID: 3457181,
	}
	// 测试不发送系统通知
	message.sendBlackCardSystemMsg()
	require.False(called)

	cancel2 := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		called = true
		return "success", nil
	})
	defer cancel2()

	// 测试发送系统通知
	message = &liveSpendMessage{
		UserID: 3457181,
		blackCardSystemMsgList: []pushservice.SystemMsg{
			{
				UserID:   3457181,
				Title:    "测试标题",
				Content:  "测试系统通知内容",
				SendTime: 12345678,
			},
		},
	}
	message.sendBlackCardSystemMsg()
	require.True(called)
}

func TestLiveSpendMessage_addBlackCardDrawPoint(t *testing.T) {
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock(userapi.URLMinigameDrawPointUpdate, func(input interface{}) (interface{}, error) {
		called = true
		return "success", nil
	})
	defer cancel()

	// 测试不增加黑卡抽奖积分
	message := &liveSpendMessage{
		UserID: 3457181,
	}
	message.addBlackCardDrawPoint()
	require.False(called)

	// 测试不增加黑卡抽奖积分
	message = &liveSpendMessage{
		UserID:                    3457181,
		userAddBlackCardDrawPoint: 1,
	}
	message.addBlackCardDrawPoint()
	require.True(called)
}
