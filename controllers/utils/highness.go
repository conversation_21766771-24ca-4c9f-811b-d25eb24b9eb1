package utils

import (
	"fmt"
	"html"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func (message *liveSpendMessage) highnessConsumption() {
	userConsumptionInLast29Days, err := userconsumption.FindUserConsumptionInLast29Days(message.UserID, message.userConsumptionTime)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
	// 今日的消费加上历史 29 天的消费来共同计算近 30 天消费的情况
	message.userHighnessConsumption = message.userTodayConsumption + userConsumptionInLast29Days
	message.checkPotentialHighnessQualification()
	uvMap, err := vip.UserVipInfos(message.UserID, false, nil)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
	uv := uvMap[vip.TypeLiveHighness]
	// 用户没有上神记录或者上神不是生效状态并且不在续费保护期内，走开通逻辑
	if uv == nil || (!uv.IsActive() && !uv.BeforeRenewDeadline()) {
		if ok := message.tryOpenHighness(); !ok {
			return
		}
	} else {
		before, after, err := usermeta.AddHighnessSpend(message.UserID, message.Spend)
		if err != nil {
			logger.WithFields(message.fields).Error(err)
			return
		}
		message.renewCount = after/vip.HighnessRenewCoin - before/vip.HighnessRenewCoin
	}
	if message.renewCount < 0 {
		logger.WithFields(message.fields).Error("count negative")
		return
	}
	if message.renewCount != 0 {
		// FIXME: 开通上神并且直接满足续期要求时，从 uv 中取值会 panic
		message.lastExpireTime = uv.ExpireTime
		message.renewHighness()
	}
	if message.openCount == 0 && message.renewCount == 0 {
		return
	}
	message.addGift()
	message.addPrivilege()
	message.clearCache()
	message.sendSystemMsg()
}

func (message *liveSpendMessage) checkPotentialHighnessQualification() {
	// 历史消费不满足潜力上神条件并且加上本次消费满足潜力上神条件的用户加入潜力上神名单
	if (message.userHighnessConsumption-message.Spend) < vip.HighnessPotentialCoin &&
		message.userHighnessConsumption >= vip.HighnessPotentialCoin {
		key := keys.KeyUsersPotentialHighness0.Format()
		if err := service.Redis.SAdd(key, message.UserID).Err(); err != nil {
			logger.WithFields(message.fields).Error(err)
			return
		}
	}
}

func (message *liveSpendMessage) tryOpenHighness() bool {
	now := goutil.TimeNow().Unix()
	var openHighnessTime int64
	config.GetAB("open_highness", &openHighnessTime)
	if now < openHighnessTime {
		return false
	}
	if message.userHighnessConsumption < vip.HighnessOpenCoin {
		return false
	}
	var uv *vip.UserVip
	uv, err := vip.BuyLiveHighness(message.UserID, true)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return false
	}
	// 重新计算剩余的消费是否满足续费条件并算做上神续费消费进度
	spend := message.userHighnessConsumption - vip.HighnessOpenCoin
	message.renewCount = spend / vip.HighnessRenewCoin
	if err = usermeta.ResetHighnessSpend(message.UserID, spend); err != nil {
		logger.WithFields(logger.Fields{
			"user_id": message.UserID,
		}).Error(err)
		// PASS
	}

	message.buyHighnessRecordList = append(message.buyHighnessRecordList, buyHighnessRecord{
		open:       true,
		expireTime: uv.ExpireTime,
	})
	message.openCount = 1
	message.finalExpireTime = uv.ExpireTime
	return true
}

func (message *liveSpendMessage) renewHighness() {
	var uv *vip.UserVip
	var err error
	var count int64
	for i := int64(0); i < message.renewCount; i++ {
		uv, err = vip.BuyLiveHighness(message.UserID, false)
		if err != nil {
			logger.WithFields(message.fields).Error(err)
			continue
		}
		message.buyHighnessRecordList = append(message.buyHighnessRecordList, buyHighnessRecord{
			open:       false,
			expireTime: uv.ExpireTime,
		})
		count++
	}
	message.renewCount = count
	if message.renewCount == 0 {
		return
	}
	message.finalExpireTime = uv.ExpireTime
}

func (message liveSpendMessage) clearCache() {
	vip.ClearUserVipCache(message.UserID)
}

func (message *liveSpendMessage) addGift() {
	var err error
	message.gift, err = gift.FindShowingGiftByGiftID(vip.HighnessGiftID)
	if err != nil {
		logger.Error(err)
		return
	}
	if message.gift == nil {
		logger.Errorf("can not find gift %d", vip.HighnessGiftID)
		return
	}
	now := goutil.TimeNow().Unix()
	count := message.openCount + message.renewCount
	err = useritems.AddGiftToUserAndUpdateEndTime(
		message.UserID, message.gift, vip.HighnessGiftNum*count, now, message.finalExpireTime,
	)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
}

func (message *liveSpendMessage) addPrivilege() {
	if err := userappearance.AddHighnessAppearances(message.UserID, message.finalExpireTime, true); err != nil {
		logger.WithFields(message.fields).Error(err)
		// PASS
	}

	hornNum := uint(vip.HighnessNobleHornNum * (message.openCount + message.renewCount))
	recommendNum := uint(vip.HighnessRecommendNumOpen*message.openCount +
		vip.HighnessRecommendNumRenew*message.renewCount)
	if err := userstatus.IncreaseHornRecommendNum(message.UserID, hornNum, recommendNum); err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
}

func (message *liveSpendMessage) sendSystemMsg() {
	var giftName string
	if message.gift == nil {
		giftName = vip.HighnessDefaultGiftName
	} else {
		giftName = message.gift.Name
	}
	systemMsgList := make([]pushservice.SystemMsg, 0, len(message.buyHighnessRecordList))
	for _, record := range message.buyHighnessRecordList {
		systemMsg := pushservice.SystemMsg{
			UserID: message.UserID,
		}
		endTimeStr := time.Unix(record.expireTime, 0).Format(util.TimeFormatYMDHMS)
		if record.open {
			systemMsg.Title = "成功开通上神贵族"
			systemMsg.Content = fmt.Sprintf("尊敬的用户，恭喜您成功开通上神贵族，有效期至 %s，已为您发放%s礼物至您的背包，赠送后将触发上神专属特效和飘屏，"+
				"更多问题请查看<a href=\"%s\">猫耳FM直播贵族说明</a>",
				endTimeStr, html.EscapeString(giftName),
				config.Conf.Params.NobleParams.NobleGuideURL)
		} else {
			systemMsg.Title = "成功续费上神贵族"
			if record.expireTime > message.lastExpireTime {
				systemMsg.Content = fmt.Sprintf("尊敬的用户，恭喜您成功续费上神贵族，有效期已延长至 %s，已为您发放%s礼物至您的背包，赠送后将触发上神专属特效和飘屏，"+
					"更多问题请查看<a href=\"%s\">猫耳FM直播贵族说明</a>",
					endTimeStr, html.EscapeString(giftName),
					config.Conf.Params.NobleParams.NobleGuideURL)
				message.lastExpireTime = record.expireTime
			} else {
				systemMsg.Content = fmt.Sprintf("尊敬的用户，恭喜您成功续费上神贵族，由于您的上神有效期已达上限 %s，本次续费不再延长有效期，"+
					"相关特权道具和%s礼物已为您发放，更多问题请查看<a href=\"%s\">猫耳FM直播贵族说明</a>",
					endTimeStr, html.EscapeString(giftName),
					config.Conf.Params.NobleParams.NobleGuideURL)
			}
		}
		systemMsgList = append(systemMsgList, systemMsg)
	}
	if err := service.PushService.SendSystemMsgWithOptions(systemMsgList,
		&pushservice.SystemMsgOptions{
			DisableHTMLEscape: true,
		},
	); err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
}

// sendLevelGte85ConsumptionPack 发送等级礼包
// 头像框 (id: 40250) 与气泡框 (id: 10113)
func (message *liveSpendMessage) sendLevelGte85ConsumptionPack() {
	var openTimeUnix int64
	config.GetAB("open_level_gte85_consumption_pack_time", &openTimeUnix)
	if message.CreateTime < openTimeUnix {
		return
	}

	user, err := liveuser.Find(message.UserID)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
	// 用户等级 < 85 不满足统计和领取条件
	// 忽略等级变动导致消费计算不准确的情况
	if user == nil || usercommon.Level(user.Contribution) < 85 {
		return
	}
	consumption, err := liveuser.AddConsumptionByLevelGte85InCurrentMonth(time.Unix(message.CreateTime, 0), user.UserID(), message.Spend)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		// PASS
	}
	// 当前总消费不足 10000 钻不满足领取条件
	// 当前总消费减去当前消费表示为上次消费钻石，若上次总消费已满 10000 钻，说明已领取过当前月份的礼包，不再重复发送
	if consumption < 10000 || consumption-message.Spend >= 10000 {
		return
	}

	// 添加外观
	appearanceIDs := appearance.UserLevelGte85RewardAppearanceIDs()
	appearances, err := appearance.Find(bson.M{"id": bson.M{"$in": appearanceIDs}}, nil)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":        message.UserID,
			"appearance_ids": appearanceIDs,
		}).Error(err)
		return
	}
	if len(appearances) != len(appearanceIDs) {
		logger.WithFields(logger.Fields{
			"user_id":        message.UserID,
			"appearance_ids": appearanceIDs,
		}).Error("appearances not found")
		return
	}
	for _, a := range appearances {
		err = userappearance.AddAppearance(message.UserID, int64(15*24*time.Hour.Seconds()), 0, a)
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":       message.UserID,
				"appearance_id": a.ID,
			}).Error(err)
			// PASS
		}
	}
	// 系统通知
	systemMsg := pushservice.SystemMsg{
		UserID: message.UserID,
		Title:  "Lv.85 用户直播等级权益解锁通知",
		Content: "尊贵的用户您好！恭喜您本月在直播间累计消费达 10000 钻石，已解锁等级专属头像框、气泡框使用权益！您可以前往外观中心进行佩戴使用。外观使用有效期截止至自通知发送后起 15 天，下月您仍可继续解锁该项权益哦~<br>" +
			"* 特别说明：<br>" +
			"直播间消费累计包含赠送有价值的礼物和付费玩法：提问、红包、开通 / 续费超粉、开通贵族（不包含上神贵族）及其他玩法。其中，赠送幸运礼物按照实际消费钻石数累计，开通贵族按照消费钻石的 80% 累计。",
	}
	err = service.PushService.SendSystemMsgWithOptions([]pushservice.SystemMsg{systemMsg}, &pushservice.SystemMsgOptions{
		DisableHTMLEscape: true,
	})
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		// PASS
	}
}
