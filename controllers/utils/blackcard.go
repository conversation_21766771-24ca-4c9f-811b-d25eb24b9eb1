package utils

import (
	"fmt"
	"slices"
	"time"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcard"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 黑卡开通类型
const (
	openTypeActivate = iota + 1 // 激活
	openTypeUpgrade             // 升级
	openTypeRenewal             // 续期
)

// 黑卡激活、升级 websocket 消息
type blackCardMessage struct {
	Type      string        `json:"type"`
	Event     string        `json:"event"`
	RoomID    int64         `json:"room_id"`
	UserID    int64         `json:"user_id"`
	Message   string        `json:"message"`
	BlackCard blackCardInfo `json:"black_card"`
}

type blackCardInfo struct {
	Level   int    `json:"level"`
	Title   string `json:"title"`
	IconURL string `json:"icon_url"`
	OpenURL string `json:"open_url"`
}

func (message *liveSpendMessage) blackCardConsumption() {
	if message.userConsumptionTime.Unix() < config.Conf.Params.BlackCard.CalculateConsumptionStartTime {
		// 消费时间小于计算黑卡有效消费的开始时间，则不做处理
		return
	}
	// TODO：线上测试结束后此逻辑可以删除
	if goutil.IsProdEnv() && message.userConsumptionTime.Unix() < config.Conf.Params.BlackCard.ProdTestEndTime &&
		!slices.Contains(config.Conf.Params.BlackCard.ProdTestWhiteListUserIDs, message.UserID) {
		// 线上环境在测试结束时间之前，只有在测试白名单中的用户可以开通黑卡
		return
	}
	userConsumptionInThisMonth, err := userconsumption.FindUserConsumptionInThisMonth(message.UserID, message.userConsumptionTime)
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
	// 用户黑卡消费（本月有效消费）
	message.userBlackCardConsumption = message.userTodayConsumption + userConsumptionInThisMonth
	// 处理开通黑卡
	message.processOpenBlackCard()
}

// processOpenBlackCard 处理开通黑卡
func (message *liveSpendMessage) processOpenBlackCard() {
	// 获取所有黑卡等级信息
	blackCardLevelMap, err := getAllBlackCardMap()
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
	if len(blackCardLevelMap) != liveblackcard.LevelNum {
		logger.WithFields(message.fields).Errorf("黑卡等级信息配置错误")
		return
	}
	message.blackCardLevelMap = blackCardLevelMap
	// 获取用户黑卡等级
	err = message.getUserBlackCard()
	if err != nil {
		logger.WithFields(message.fields).Error(err)
		return
	}
	if message.thisMonthUserBlackCardsHighestLevel == liveblackcard.MaxLevel {
		// 本月已达到最高黑卡等级，则不做处理
		return
	}

	message.blackCardSystemMsgList = make([]pushservice.SystemMsg, 0, liveblackcard.LevelNum)
	beginningOfMonth := goutil.BeginningOfMonth(message.userConsumptionTime)
	// 新达成的等级的过期时间
	message.newBlackCardLevelExpireTime = beginningOfMonth.AddDate(0, 2, 0).Unix()
	// 本月的下一个等级
	thisMonthNextLevel := message.thisMonthUserBlackCardsHighestLevel + 1
	for i := thisMonthNextLevel; i <= liveblackcard.MaxLevel; i++ {
		// 从本月下一个等级开始开通或升级，用户可能存在一次消费解锁多个档位的情况
		thisMonthNextBlackCard := message.blackCardLevelMap[i]
		if message.userBlackCardConsumption < thisMonthNextBlackCard.Price {
			// 消费未达到下一个等级时，终止循环
			break
		}
		// 满足开通条件，增加开通记录
		userBlackCard := liveuserblackcard.LiveUserBlackCard{
			UserID:      message.UserID,
			BlackCardID: thisMonthNextBlackCard.ID,
			StartTime:   message.userConsumptionTime.Unix(),
			ExpireTime:  message.newBlackCardLevelExpireTime,
		}
		err := liveuserblackcard.LiveUserBlackCard{}.DB().Create(&userBlackCard).Error
		if err != nil {
			logger.WithFields(message.fields).Errorf("添加黑卡开通记录失败：%v", err)
			// PASS
			return
		}
		// 本次消费最终达成的黑卡等级
		message.newLiveBlackCard = &thisMonthNextBlackCard

		expireTimeStr := time.Unix(message.newBlackCardLevelExpireTime, 0).Format(util.TimeFormatYMDHHMM)
		if thisMonthNextBlackCard.Level == message.currentUserBlackCardLevel {
			// 续期当前黑卡等级
			message.blackCardOpenType = openTypeRenewal
			systemMsg := pushservice.SystemMsg{
				UserID: message.UserID,
				Title:  fmt.Sprintf("%s 身份有效期延长", thisMonthNextBlackCard.Title),
				Content: fmt.Sprintf("尊敬的用户，根据您本月累计消费，已为您自动延期%s 身份至 %s，星曜用户可在直播间内体验专属特权。<a href=\"%s\">点击查看特权 >></a>",
					thisMonthNextBlackCard.Title, expireTimeStr, params.BlackCardURL(false)),
			}
			message.blackCardSystemMsgList = append(message.blackCardSystemMsgList, systemMsg)
		} else if thisMonthNextBlackCard.Level > message.currentUserBlackCardLevel {
			// 激活或升级黑卡等级
			if message.currentUserBlackCardLevel == 0 {
				// 本次消费前没有有效黑卡等级时为激活类型
				message.blackCardOpenType = openTypeActivate
			} else {
				message.blackCardOpenType = openTypeUpgrade
			}
			systemMsg := pushservice.SystemMsg{
				UserID: message.UserID,
				Title:  fmt.Sprintf("恭喜解锁%s 身份！", thisMonthNextBlackCard.Title),
				Content: fmt.Sprintf("尊敬的用户，您本月在直播间内累计消费已达成%s 身份解锁条件，星曜用户可在直播间内体验专属特权。本次解锁，您的身份有效期将被保留至 %s。<a href=\"%s\">点击查看特权 >></a>",
					thisMonthNextBlackCard.Title, expireTimeStr, params.BlackCardURL(false)),
			}
			message.blackCardSystemMsgList = append(message.blackCardSystemMsgList, systemMsg)
		}

		// 每升一级，抽奖积分 +1
		message.userAddBlackCardDrawPoint++
	}

	// 发放或延长黑卡特权
	message.addBlackCardPrivileges()
	// 发送黑卡升级弹窗提示
	message.sendBlackCardMessage()
	// 发送黑卡系统通知
	message.sendBlackCardSystemMsg()
	// 增加黑卡抽奖积分
	message.addBlackCardDrawPoint()
}

func getAllBlackCardMap() (map[int]liveblackcard.LiveBlackCard, error) {
	blackCards, err := liveblackcard.ListAllBlackCard()
	if err != nil {
		return nil, err
	}
	if len(blackCards) <= 0 {
		return nil, nil
	}
	blackCardMap := goutil.ToMap(blackCards, "Level").(map[int]liveblackcard.LiveBlackCard)
	return blackCardMap, nil
}

// getUserBlackCard 获取用户的黑卡信息
func (message *liveSpendMessage) getUserBlackCard() error {
	userBlackCards, err := liveuserblackcard.FindUserBlackCardRecentTwoMonths(message.UserID)
	if err != nil {
		return err
	}
	beginningOfMonth := goutil.BeginningOfMonth(message.userConsumptionTime)
	if len(userBlackCards) > 0 {
		for _, userBlackCard := range userBlackCards {
			if userBlackCard.StartTime >= beginningOfMonth.Unix() {
				if message.thisMonthUserBlackCardsHighestLevel < userBlackCard.Level {
					message.thisMonthUserBlackCardsHighestLevel = userBlackCard.Level
				}
			} else {
				if message.lastMonthUserBlackCardHighestLevel < userBlackCard.Level {
					message.lastMonthUserBlackCardHighestLevel = userBlackCard.Level
				}
			}
		}
		// 当前有效的黑卡等级（上月和本月中取最高等级）
		message.currentUserBlackCardLevel = max(message.thisMonthUserBlackCardsHighestLevel, message.lastMonthUserBlackCardHighestLevel)
	}
	return nil
}

// addBlackCardPrivileges 发放或延长黑卡特权
func (message *liveSpendMessage) addBlackCardPrivileges() {
	if !slices.Contains([]int{openTypeActivate, openTypeUpgrade, openTypeRenewal}, message.blackCardOpenType) || message.newLiveBlackCard == nil {
		return
	}
	// 激活、升级或续期黑卡时发放或延长黑卡特权
	liveuserblackcard.AddBlackCardPrivileges(message.UserID, message.thisMonthUserBlackCardsHighestLevel, message.newLiveBlackCard.Level, message.newBlackCardLevelExpireTime, message.blackCardOpenType == openTypeRenewal)
}

// sendBlackCardMessage 发送黑卡升级弹窗提示
func (message *liveSpendMessage) sendBlackCardMessage() {
	// 发送黑卡升级弹窗提示（用户一次消费解锁多个档位时，只弹窗提示最高档位）
	// 用户在直播间消费并且是激活或升级时才发送黑卡升级弹窗提示
	if message.RoomID <= 0 || !slices.Contains([]int{openTypeActivate, openTypeUpgrade}, message.blackCardOpenType) || message.newLiveBlackCard == nil {
		return
	}
	payload := &blackCardMessage{
		Type:    liveim.TypeBlackCard,
		Event:   liveim.EventBlackCardUpgrade,
		RoomID:  message.RoomID,
		UserID:  message.UserID,
		Message: fmt.Sprintf("您已成功达成%s 身份解锁条件，专属特权已发放，快来看看吧！", message.newLiveBlackCard.Title),
		BlackCard: blackCardInfo{
			Level:   message.newLiveBlackCard.Level,
			Title:   message.newLiveBlackCard.Title,
			IconURL: message.newLiveBlackCard.IconURL,
			OpenURL: params.BlackCardURL(true),
		},
	}
	err := userapi.BroadcastUser(message.RoomID, message.UserID, payload)
	if err != nil {
		logger.WithFields(message.fields).Errorf("黑卡升级弹窗提示发送失败：%v", err)
		// PASS
	}
}

// sendBlackCardSystemMsg 发送黑卡系统通知
func (message *liveSpendMessage) sendBlackCardSystemMsg() {
	if len(message.blackCardSystemMsgList) <= 0 {
		return
	}
	// 发送系统通知（用户一次消费解锁多个档位时，发多个系统通知）
	err := service.PushService.SendSystemMsgWithOptions(message.blackCardSystemMsgList, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.WithFields(message.fields).Errorf("开通或续期黑卡时系统通知发送失败：%v", err)
		// PASS
	}
}

// sendBlackCardSystemMsg 增加黑卡抽奖积分
func (message *liveSpendMessage) addBlackCardDrawPoint() {
	if message.userAddBlackCardDrawPoint <= 0 {
		return
	}
	// 增加抽奖积分
	err := userapi.UpdateDrawPoint(config.Conf.Params.BlackCard.EventIDDraw, message.UserID, message.userAddBlackCardDrawPoint)
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": message.UserID, "point": message.userAddBlackCardDrawPoint}).Errorf("添加黑卡抽奖活动积分失败：%v", err)
		// PASS
	}
}
