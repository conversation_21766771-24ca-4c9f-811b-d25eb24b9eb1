package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service/userapi"
)

func TestNewBalanceAfterSend(t *testing.T) {
	assert := assert.New(t)

	b := NewBalanceAfterSend(&userapi.BalanceResp{
		Balance:          1,
		LiveNobleBalance: 1234,
	}, true)
	assert.Equal(int64(1), b.<PERSON><PERSON>)
	assert.Equal(int64(1234), b.LiveNobleBalance)
	assert.Equal(1, b.LiveNobleBalanceStatus)
}
