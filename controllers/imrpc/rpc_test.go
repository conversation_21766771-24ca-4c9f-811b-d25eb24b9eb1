package imrpc

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func paramToRequestBody(param interface{}) *bytes.Buffer {
	data, _ := json.Marshal(param)
	return bytes.NewBuffer(data)
}

func TestMain(m *testing.M) {
	handler.SetMode(handler.TestMode)
	config.InitTest()
	var err error
	service.IMRedis, err = serviceredis.NewRedisClient(&config.Conf.Service.IMRedis)
	if err != nil {
		logger.Fatal(err)
	}

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("rpc", h.Name)
	assert.Equal(2, len(h.Middlewares))
	assert.Len(h.SubHandlers, 1)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "/broadcast", "/broadcast/all", "/broadcast/user", "/broadcast/many",
		"/activity/broadcast", "/activity/broadcast/all",
		"/online/count", "/online/userstatus", "/online/conn",
		"/room/refresh", "/room/list",
		"/notify/set")
}

func TestCronHandler(t *testing.T) {
	assert := assert.New(t)
	kc := tutil.NewKeyChecker(t, tutil.Actions)

	h := cronHandler()
	assert.Equal("cron", h.Name)
	kc.Check(h, "cleanup")
}

func TestMeticsMiddleware(t *testing.T) {
	assert := assert.New(t)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	middleWare := MeticsMiddleware()
	middleWare(c)

	w = httptest.NewRecorder()
	r, _ := http.NewRequest("GET", "/metrics", nil)
	h := promhttp.HandlerFor(service.PromRegistry, promhttp.HandlerOpts{})
	h.ServeHTTP(w, r)
	assert.Equal(`# HELP http_requests_total The total number of HTTP requests.
# TYPE http_requests_total counter
http_requests_total{status="200"} 1
`, w.Body.String())

	middleWare(c)
	w = httptest.NewRecorder()
	r, _ = http.NewRequest("GET", "/metrics", nil)
	h.ServeHTTP(w, r)
	assert.Equal(`# HELP http_requests_total The total number of HTTP requests.
# TYPE http_requests_total counter
http_requests_total{status="200"} 2
`, w.Body.String())
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("rpc", h.Name)
	assert.Equal(2, len(h.Middlewares))
	assert.Len(h.SubHandlers, 1)

	t.Run("sub room handler v2", func(t *testing.T) {
		h := roomHandlerV2()
		assert.Equal("room", h.Name)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		kc.Check(h.Actions, "batch-list")
	})
}
