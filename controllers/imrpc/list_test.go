package imrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	param := listParam{RoomID: 999}
	kc.Check(param, "room_id", "users", "conns", "valuable_users")
	c := handler.NewRPCTestContext("/room/list", param)
	_, err := ActionList(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = listParam{
		RoomID:        999,
		Users:         true,
		Conns:         true,
		ValuableUsers: true,
	}
	c = handler.NewRPCTestContext("/room/list", param)
	r, err := ActionList(c)
	require.NoError(err)
	kc.Check(r, "user_ids", "conns", "valuable_user_ids", "users")
}

func TestBatchList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := batchListParam{RoomIDs: []int64{}}
	c := handler.NewRPCTestContext("/room/batch-list", param)
	_, _, err := ActionBatchList(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = batchListParam{RoomIDs: []int64{0, 1, 2, 3, 4, 5, 6, 7, 8, 9}, Users: true}
	c = handler.NewRPCTestContext("/room/batch-list", param)
	_, _, err = ActionBatchList(c)
	assert.Equal(actionerrors.ErrParams, err)

	param = batchListParam{RoomIDs: []int64{1, 2, 3}, Users: true}
	c = handler.NewRPCTestContext("/room/batch-list", param)
	_, _, err = ActionBatchList(c)
	require.NoError(err)
	assert.Len(param.RoomIDs, 3)
}
