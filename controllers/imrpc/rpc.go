package imrpc

import (
	"strconv"
	"sync"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/prometheus/client_golang/prometheus"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/im"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
)

var json2 = jsoniter.ConfigCompatibleWithStandardLibrary

// Handler returns the registered handler
func Handler() *handler.Handler {
	return &handler.Handler{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.MiddlewareAppKey(config.Conf.HTTP.RPCKey, config.Conf.HTTP.BiliAppKey),
			MeticsMiddleware(),
		},
		SubHandlers: []handler.Handler{
			cronHandler(),
		},
		Actions: map[string]*handler.Action{
			"/broadcast":      handler.NewAction(handler.POST, ActionBroadcast, false),
			"/broadcast/all":  handler.NewAction(handler.POST, ActionBroadcastAll, false),
			"/broadcast/user": handler.NewAction(handler.POST, ActionBroadcastUser, false),
			"/broadcast/many": handler.NewAction(handler.POST, ActionBroadcastMany, false),

			"/activity/broadcast":     handler.NewAction(handler.POST, ActionActivityBroadcast, false),
			"/activity/broadcast/all": handler.NewAction(handler.POST, ActionActivityBroadcastAll, false),

			"/online/count":      handler.NewAction(handler.POST, ActionOnlineCount, false),
			"/online/userstatus": handler.NewAction(handler.POST, ActionUserStatus, false),
			"/online/conn":       handler.NewAction(handler.POST, im.ActionConn, false),
			"/room/refresh":      handler.NewAction(handler.POST, ActionRefreshUsers, false),
			"/room/list":         handler.NewAction(handler.POST, ActionList, false),
			"/notify/set":        handler.NewAction(handler.POST, ActionNotifySet, false),
		}}
}

func cronHandler() handler.Handler {
	return handler.Handler{
		Name: "cron",
		Actions: map[string]*handler.Action{
			"cleanup": handler.NewAction(handler.POST, ActionCleanup, false),
		},
	}
}

var (
	httpRequestTotal *prometheus.CounterVec
	meticsOnce       sync.Once
)

// MeticsMiddleware metics 中间件
// TODO: 等 missevan-go 的中间件替换
func MeticsMiddleware() gin.HandlerFunc {
	meticsOnce.Do(func() {
		httpRequestTotal = prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Subsystem: "http",
				Name:      "requests_total",
				Help:      "The total number of HTTP requests.",
			},
			[]string{"status"},
		)
		service.PromRegistry.MustRegister(httpRequestTotal)
	})
	return func(c *gin.Context) {
		c.Next()
		status := strconv.Itoa(c.Writer.Status())
		httpRequestTotal.WithLabelValues(status).Inc()
	}
}

// HandlerV2 returns the registered handler v2
func HandlerV2() *handler.HandlerV2 {
	return &handler.HandlerV2{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.MiddlewareAppKey(config.Conf.HTTP.RPCKey, config.Conf.HTTP.BiliAppKey),
			MeticsMiddleware(),
		},
		SubHandlers: []handler.HandlerV2{
			roomHandlerV2(),
		},
	}
}

func roomHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "room",
		Actions: map[string]*handler.ActionV2{
			"batch-list": handler.NewActionV2(handler.POST, ActionBatchList, handler.ActionOption{LoginRequired: false}),
		},
	}
}
