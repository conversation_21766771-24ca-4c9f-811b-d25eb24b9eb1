package wish

import (
	"strconv"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/wishes"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/wish"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type buyParam struct {
	GoodsID int64 `form:"goods_id" json:"goods_id"`
	Num     int   `form:"num" json:"num"`
	Confirm int   `form:"confirm" json:"confirm"`

	userID       int64
	rpcResp      *userapi.BalanceResp
	livegoods    *livegoods.LiveGoods
	more         *livegoods.More
	uv           *vip.UserVip
	c            *handler.Context
	limitStates  map[int]*livegoods.LimitState
	limitType    int
	remainingNum int64
	now          time.Time
}

type buyWishGoodsResp struct {
	Message      string             `json:"message"`
	Balance      userapi.BuyBalance `json:"balance"`
	RemainingNum int64              `json:"remaining_num"`
}

// ActionWishBuy 星座许愿（购买）
/**
 * @api {post} /api/v2/activity/wish/buy 星座许愿（购买）
 * @apiVersion 0.1.0
 * @apiGroup activity
 *
 * @apiParam {Number} goods_id 商品 ID
 * @apiParam {Number} num 购入数量（许愿次数）
 * @apiParam {Number} [confirm=0] 确认参数
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "message": "感应请求已发出，请在本轮感应结束后查看结果哦~", // 提示文案
 *       "balance": { // 需要按照 balance 结构更新所有钻石余额
 *         "balance": 11479968,
 *         "live_noble_balance": 283748,
 *         "live_noble_balance_status": 1
 *       },
 *       "remaining_num": 19 // 剩余感应（购买）次数
 *     }
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确认消耗 1 钻石感应 1000 次吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionWishBuy(c *handler.Context) (handler.ActionResponse, error) {
	var param buyParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	// 创建订单及抽奖记录并扣费
	err = param.createOrderAndBuy()
	if err != nil {
		return nil, err
	}

	// 增加直播间经验值及直播间消费
	goutil.Go(func() {
		param.addUserContribution()
		param.addLiveSpend()
	})

	isNoble := param.uv != nil
	resp := buyWishGoodsResp{
		Message:      "许愿结果稍后通知，奖品过期失效请注意",
		Balance:      userapi.NewBuyBalance(param.rpcResp, isNoble),
		RemainingNum: param.remainingNum,
	}
	return &resp, nil
}

func (param *buyParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil || param.GoodsID <= 0 || param.Num <= 0 {
		return actionerrors.ErrParams
	}

	param.userID = c.UserID()
	param.livegoods, err = livegoods.Find(param.GoodsID, livegoods.GoodsTypeWish)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.livegoods == nil {
		return actionerrors.ErrGoodsNotFound
	}

	param.more, err = param.livegoods.UnmarshalMore()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.more == nil {
		logger.Errorf("许愿池商品（%d）配置错误", param.GoodsID)
		return actionerrors.ErrGoodsNotFound
	}
	tr := goutil.NewTimeRangeUnix(param.livegoods.SaleStartTime, param.livegoods.SaleEndTime)
	if param.more.EventID != 0 {
		e := activity.NewExtendedFields()
		_, err = mevent.FindSimpleWithExtendedFields(param.more.EventID, &e)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		param.now = e.TimeNow()
	} else {
		param.now = goutil.TimeNow()
	}
	if !tr.Between(param.now) {
		return actionerrors.ErrParamsMsg("当前不在许愿时间内哦~")
	}

	if param.more.HasNumLimit() {
		// 检查当前的购买次数
		// 查询当前用户已购买次数
		wishNum, err := param.wishNum()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		limitStates, limitType := param.more.CalculateLimitState(wishNum)
		param.limitStates = limitStates
		param.limitType = limitType
		if param.limitStates[param.limitType].LimitNum <= wishNum[param.limitType] {
			return actionerrors.ErrParamsMsg("本奖池此轮许愿次数已达上限！")
		}
		// 此次购买后剩余可购买数
		param.remainingNum = param.limitStates[param.limitType].RemainingNum - int64(param.Num)
		if param.remainingNum < 0 {
			return actionerrors.ErrParamsMsg("许愿次数超过此轮奖池可许愿次数上限！")
		}
	}

	if param.Confirm == 0 {
		cost := int64(param.livegoods.Price * param.Num)
		// TODO: 颜色配置到 live_goods more 里
		format := map[string]string{
			"normal_color":    "#D8DAFF",
			"highlight_color": "#FFEBA4",
			"cost":            strconv.FormatInt(cost, 10),
			"num":             strconv.FormatInt(int64(param.Num), 10),
		}
		msg := goutil.FormatMessage(`<font color="${normal_color}">确认消耗 </font>`+
			`<font color="${highlight_color}">${cost}</font>`+
			`<font color="${normal_color}"> 钻石许愿 </font>`+
			`<font color="${highlight_color}">${num}</font>`+
			`<font color="${normal_color}"> 次吗？</font>`, format)
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}

	uv, err := vip.UserActivatedVip(param.userID, false, c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}
	param.c = c
	return nil
}

// wishNum 获取用户许愿商品已购买数
func (param *buyParam) wishNum() (map[int]int64, error) {
	wishNum := make(map[int]int64, 1)
	num, err := wish.UserWishNum(param.GoodsID, param.userID, param.now)
	if err != nil {
		return wishNum, err
	}
	wishNum[livegoods.LimitNumDailyUser] = num
	return wishNum, nil
}

// createOrderAndBuy 创建订单及抽奖记录并扣费
func (param *buyParam) createOrderAndBuy() error {
	var err error
	// 防止用户在短期内快速购买造成限购失效
	if param.more.HasNumLimit() {
		lockKey := keys.LockUserBuyWishGoods1.Format(param.userID)
		ok, err := service.Redis.SetNX(lockKey, param.livegoods.ID, 10*time.Second).Result()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return actionerrors.NewErrForbidden("操作太快啦，请稍后再试~")
		}
		// 释放锁
		defer func() {
			err = service.Redis.Del(lockKey).Err()
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}()
	}
	// 发起 rpc 购买请求
	noble := goutil.BoolToInt(param.uv != nil) // 是否使用贵族钻石
	param.rpcResp, err = userapi.BuyWishGoods(param.userID, param.livegoods.ID,
		param.livegoods.Title, param.livegoods.Price, param.Num, noble, userapi.NewUserContext(param.c))
	if err != nil {
		return err
	}

	// 创建订单
	// REVIEW: 需要考虑是否先创建该订单，再去进行 rpc 购买
	more := &livetxnorder.MoreInfo{
		OpenStatus: util.NewInt(livetxnorder.OpenStatusNoRoom),
		Num:        param.Num,
	}
	order := livetxnorder.NewOrder(param.livegoods, param.userID, 0, more)
	// 星座许愿 NewOrder 设置的 Price 不是订单消耗钻石，需要设置成正确的值
	order.Price = param.livegoods.Price * param.Num
	order.Status = livetxnorder.StatusSuccess
	order.TID = param.rpcResp.TransactionID
	db := livetxnorder.LiveTxnOrder{}.DB()
	err = db.Create(order).Error
	lf := logger.Fields{
		"user_id":  param.userID,
		"goods_id": param.GoodsID,
		"num":      param.Num,
	}
	if err != nil {
		logger.WithFields(lf).Errorf("创建许愿订单失败：%v", err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 创建许愿记录
	_, err = wishes.CreateWish(param.livegoods, param.userID,
		param.rpcResp.TransactionID, param.Num, param.now, param.rpcResp.Context)
	if err != nil {
		logger.WithFields(lf).Errorf("创建许愿记录失败：%v", err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 增加购买次数
	currentNum, err := wish.IncrUserWishNum(param.GoodsID, param.userID,
		param.Num, param.now)
	if err != nil {
		logger.WithFields(lf).Errorf("增加用户许愿池许愿次数出错：%v", err)
		// PASS: 此处记录错误日志，不影响正常购买流程
	}
	if currentNum > param.limitStates[param.limitType].LimitNum {
		// 若超过购买限制数量，则记录错误日志
		logger.WithFields(lf).Error("用户许愿池许愿次数超出上限")
		// PASS: 因购买行为加了锁，出现此情况概率较低，故此处先只记录错误信息，不报错
	}
	return nil
}

// addLiveSpend 添加直播消费
func (param *buyParam) addLiveSpend() {
	spend := int64(param.livegoods.Price * param.Num)
	utils.SendLiveSpend(param.userID, spend, 0)
}

// addUserContribution 增加用户直播间经验值
func (param *buyParam) addUserContribution() {
	pointAdd := int64(param.livegoods.Price * param.Num * 10) // 1 钻石 = 10 经验
	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.userID, 0, "", userstatus.FromNormal, param.uv)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}
