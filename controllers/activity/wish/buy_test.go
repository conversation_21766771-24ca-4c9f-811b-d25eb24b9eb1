package wish

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/wishes"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/wish"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testUserID      = 12
	testLiveGoodsID = 1654564
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	err := createTestData()
	if err != nil {
		logger.Fatal(err)
	}
	m.Run()
}

func createTestData() error {
	now := goutil.TimeNow()
	liveGoods := &livegoods.LiveGoods{
		ID:            testLiveGoodsID,
		Type:          livegoods.GoodsTypeWish,
		Price:         100,
		Title:         "许愿池商品",
		SaleStartTime: now.Unix(),
		SaleEndTime:   now.Add(10 * time.Minute).Unix(),
		StartTime:     now.Unix(),
		EndTime:       now.Add(10 * time.Minute).Unix(),
		More:          `{"limits":[{"num":100,"type": 1}],"event_id":214}`,
	}
	err := service.LiveDB.Create(liveGoods).Error
	return err
}

func clearTestData() error {
	when := goutil.TimeNow()
	when = when.Truncate(30 * time.Minute)
	key1 := keys.LockUserBuyWishGoods1.Format(testUserID)
	key2 := wish.KeyWishNum(testLiveGoodsID, when)
	key3 := keys.KeyUserWishNum2.Format(testLiveGoodsID, when.Format(util.TimeFormatYMDHHWithNoSpace))
	err := service.Redis.Del(key1, key2, key3).Err()
	if err != nil {
		return err
	}
	err = livetxnorder.LiveTxnOrder{}.DB().
		Where("goods_id = ?", testLiveGoodsID).
		Delete(livetxnorder.LiveTxnOrder{}).Error
	if err != nil {
		return err
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := wishes.Collection()
	filter := bson.M{"goods_id": testLiveGoodsID}
	_, err = col.DeleteMany(ctx, filter)
	return err
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(buyParam{}, "goods_id", "num", "confirm")
	kc.Check(buyWishGoodsResp{}, "message", "balance", "remaining_num")
}

func TestActionWishBuy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(clearTestData())

	cancel := mrpc.SetMock(userapi.URIBuyWishGoods,
		func(input interface{}) (output interface{}, err error) {
			return userapi.BalanceResp{
				TransactionID:    23333,
				Balance:          123,
				LiveNobleBalance: 123,
				Price:            123,
				Context:          `{"transaction_id":23333,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}`,
			}, nil
		})
	defer cancel()

	// 测试接口请求出错的情况
	param := &buyParam{
		GoodsID: 233,
		Num:     0,
	}
	api := "/api/v2/activity/wish/buy"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	result, err := ActionWishBuy(c)
	assert.Equal(actionerrors.ErrParams, err)
	assert.Nil(result)

	// 测试正常购买成功
	cancel = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cancel()
	err = service.Redis.Del(wish.KeyWishNum(testLiveGoodsID, goutil.TimeNow())).Err()
	require.NoError(err)
	param = &buyParam{
		GoodsID: testLiveGoodsID,
		Num:     1,
		Confirm: 1,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err = ActionWishBuy(c)
	require.NoError(err)
	resp := result.(*buyWishGoodsResp)
	assert.Equal("感应结果稍后通知，奖品过期失效请注意", resp.Message)
	assert.Equal(int64(123), resp.Balance.Balance)
	assert.Equal(int64(123), resp.Balance.LiveNobleBalance)
	assert.Equal(0, resp.Balance.LiveNobleBalanceStatus)
	assert.Equal(int64(99), resp.RemainingNum)
}

func TestBuyParam_load(t *testing.T) {
	assert := assert.New(t)

	// 测试参数错误
	param := &buyParam{
		GoodsID: 233,
		Num:     0,
	}
	api := "/api/v2/activity/wish/buy"
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	err := param.load(c)
	assert.Equal(actionerrors.ErrParams, err)
	param = &buyParam{
		GoodsID: 0,
		Num:     1,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试商品不存在
	param.GoodsID = 9999999
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrGoodsNotFound, err)

	// 测试当前时间段无法购买
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	param.GoodsID = testLiveGoodsID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.EqualError(err, "当前不在感应时间内哦~")

	// 测试可正常购买的情况
	goutil.SetTimeNow(nil)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrConfirmRequired(`<font color="#8B6B44">确认消耗 </font>`+
		fmt.Sprintf(`<font color="#D0714A">%d</font>`, param.livegoods.Price*param.Num)+
		`<font color="#8B6B44"> 钻石感应 </font>`+
		fmt.Sprintf(`<font color="#D0714A">%d</font>`, param.Num)+
		`<font color="#8B6B44"> 次吗？</font>`, 1, true), err)

	// 测试可正常购买的情况
	cancel := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()
	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.NoError(err)

	// 测试购买次数超上限
	param.Num = 101
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.EqualError(err, "感应次数超过此轮奖池可感应次数上限！")
}

func TestBuyParam_WishNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	param := buyParam{
		GoodsID: testLiveGoodsID,
		userID:  testUserID,
		now:     goutil.TimeNow(),
	}

	// 测试无购买次数的而情况
	key := wish.KeyWishNum(testLiveGoodsID, param.now)
	require.NoError(service.Redis.Del(key).Err())
	orderCount, err := param.wishNum()
	require.NoError(err)
	assert.Equal(int64(0), orderCount[livegoods.LimitNumDailyUser])

	// 测试有购买次数的情况
	require.NoError(service.Redis.HSet(key, testUserID, 233).Err())
	orderCount, err = param.wishNum()
	require.NoError(err)
	assert.Equal(int64(233), orderCount[livegoods.LimitNumDailyUser])
}

func TestCreateOrderAndBuy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIBuyWishGoods,
		func(input interface{}) (output interface{}, err error) {
			liveNobleBalance := int64(123)
			param := input.(userapi.BuyWishGoodsParam)
			if param.Noble == 0 {
				// mock 不使用贵族钻石时，返回贵族钻石数为 0
				liveNobleBalance = 0
			}
			return userapi.BalanceResp{
				TransactionID:    23333,
				Balance:          123,
				LiveNobleBalance: liveNobleBalance,
				Price:            123,
				Context:          `{"transaction_id":23333,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}}`,
			}, nil
		})
	defer cancel()
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 07, 07, 1, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	// 测试加锁的情况购买
	key := keys.LockUserBuyWishGoods1.Format(testUserID)
	_, err := service.Redis.SetNX(key, testLiveGoodsID, 10*time.Second).Result()
	require.NoError(err)
	limitStates := make(map[int]*livegoods.LimitState, 1)
	limitStates[livegoods.LimitNumDailyUser] = &livegoods.LimitState{
		LimitNum: 100,
	}
	param := &buyParam{
		c:           handler.NewTestContext(http.MethodPost, "", false, nil),
		GoodsID:     testLiveGoodsID,
		userID:      testUserID,
		Num:         10,
		limitStates: limitStates,
		limitType:   livegoods.LimitNumDailyUser,
		livegoods: &livegoods.LiveGoods{
			ID:    testLiveGoodsID,
			Price: 10,
			Title: "test_goods_title",
		},
		more: &livegoods.More{
			Limits: []livegoods.Limit{
				{
					Num:  100,
					Type: livegoods.LimitNumDailyUser,
				},
			},
		},
		now: goutil.TimeNow(),
	}
	err = param.createOrderAndBuy()
	assert.EqualError(err, "操作太快啦，请稍后再试~")
	err = service.Redis.Del(key).Err()
	require.NoError(err)

	// 测试正常购买的情况
	now := goutil.TimeNow()
	key = wish.KeyWishNum(testLiveGoodsID, now)
	require.NoError(service.Redis.HDel(key, strconv.FormatInt(param.userID, 10)).Err())
	err = param.createOrderAndBuy()
	require.NoError(err)
	// 验证创建了商品订单数据
	order := livetxnorder.LiveTxnOrder{}
	err = service.LiveDB.Table(livetxnorder.TableName()).
		Where("goods_id = ? AND buyer_id = ?", testLiveGoodsID, testUserID).
		Order("id DESC").Take(&order).Error
	require.NoError(err)
	assert.Equal(param.livegoods.Price*param.Num, order.Price)
	assert.Equal(param.Num, order.More.Num)
	// 验证非贵族时不使用贵族钻石
	assert.Equal(int64(0), param.rpcResp.LiveNobleBalance)
	// 验证创建了许愿记录
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	w := new(wishes.Wish)
	err = wishes.Collection().FindOne(ctx,
		bson.M{"goods_id": param.livegoods.ID, "user_id": param.userID},
		options.FindOne().SetSort(bson.M{"_id": -1})).Decode(w)
	require.NoError(err)
	assert.Equal(param.Num, w.Num)
	assert.Equal(int64(param.livegoods.Price), w.GoodsPrice)
	assert.Equal(`{"transaction_id":23333,"tax":12.5,"price":100,"common_coin":{"ios":100,"android":0,"tmallios":0}}`, w.Context)
	assert.Nil(w.Rewards)
	// 验证许愿次数正确
	num, err := wish.UserWishNum(testLiveGoodsID, testUserID, now)
	require.NoError(err)
	assert.Equal(int64(param.Num), num)

	// 测试为贵族的时候购买使用贵族钻石
	param.uv = &vip.UserVip{
		UserID: testUserID,
	}
	err = param.createOrderAndBuy()
	require.NoError(err)
	// 验证使用了贵族钻石
	assert.Equal(int64(123), param.rpcResp.LiveNobleBalance)
	// 验证重复购买许愿次数正确
	num, err = wish.UserWishNum(testLiveGoodsID, testUserID, now)
	require.NoError(err)
	assert.Equal(int64(param.Num*2), num)
}

func TestBuyParamAddUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &buyParam{
		userID:    12,
		Num:       10,
		livegoods: &livegoods.LiveGoods{Price: 10},
	}
	before, err := liveuser.Find(param.userID)
	require.NoError(err)
	require.NotNil(before)
	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.userID)
	require.NoError(err)
	assert.Equal(int64(param.livegoods.Price*param.Num*10), after.Contribution-before.Contribution)
	before = after
	param.uv = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.userID)
	require.NoError(err)
	assert.Equal(int64(param.livegoods.Price*param.Num*10*2), after.Contribution-before.Contribution)
}
