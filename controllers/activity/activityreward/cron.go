package activityreward

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type paramRankIncrease struct {
	EventID int64 `json:"event_id"`
}

// ActionActivityCronRankIncrease 活动榜单稿件和首次投稿加成
/**
 * @api {post} /rpc/activity/cron/rank/increase 活动榜单稿件和首播加成
 * @apiDescription 每小时定时调用，对榜单增加用户对应首播加成、投稿评论观看量加成的眠力值
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number=138} event_id 活动 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionActivityCronRankIncrease(c *handler.Context) (handler.ActionResponse, error) {
	var param paramRankIncrease
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	switch param.EventID {
	case 0:
	default:
		return nil, actionerrors.ErrParams
	}
	return "success", nil
}

// ActionCronActivityReward 直播间定时赠送礼物
/**
 * @api {post} /rpc/activity/cron/reward 直播间定时赠送礼物
 * @apiDescription 每分钟定时调用，给开播的直播间发放礼物奖励
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} event_id 活动 ID
 * @apiParam {Number} user_id 送礼用户 ID
 * @apiParam {Object[]} gifts 礼物
 * @apiParam {Number} gifts[gift_id] 礼物 ID
 * @apiParam {Number} gifts[gift_num] 礼物数量
 * @apiParam {Number} start_time 礼物开始发放时间
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success" // 未到奖励时间，返回 "unstarted"
 *   }
 * @apiSuccessExample {json} WebSocket 送礼成功房间内消息
 *     {
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#F45B41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "highness",
 *           "name": "上神",
 *           "level": 1
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "time": 1576744741101,
 *       "gift": {
 *         "gift_id": 125,
 *         "name": "一期一会",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/001.png",
 *         // 如果下发的消息没有 effect_url，需要支持从 meta/data 存储的数据中查询出对应的 effect_url 和 effect_duration
 *         "effect_url": "https://static-test.missevan.com/gifts/effects/001.lottie", // 礼物特效（有特效的礼物才有这个字段），客户端需要支持 lottie、svga 和 webp 格式
 *         "effect_duration": 5000, // 特效时长，如果有就使用。对于无法计算出时长的特效且无法获取出 effect_duration (包括没有在 meta/data) 数据的，使用默认时长 5000ms
 *         "price": 13142, // 免费礼物价格
 *         "num": 1
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *         "type": "noble", // 气泡类型，目前支持: 贵族气泡 noble
 *         "noble_level": 2 // 使用对应等级的贵族气泡
 *       },
 *       "current_revenue": 1 // 本场榜中此用户的贡献值
 *     }
 *
 * @apiSuccessExample {json} WebSocket 全局消息
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "gift": {
 *         "gift_id": 125,
 *         "name": "一期一会",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/125.png",
 *         "price": 13142,
 *         "num": 1,
 *         "effect_url": "https://static-test.missevan.com/gifts/effects/125-mobile.svga"
 *       },
 *       "message": "<font color="#f4ceff">猫耳Live</font> 给 <font color="#f4ceff">小蜜蜂</font> 赠送了 <b>一期一会</b>，祝有缘人们星河璀璨，天涯与共",
 *       "notify_bubble": {
 *         "type": "custom",
 *         "image_url": "https://example.com/b128_0_10_0_100.png" // 客户端使用
 *       }
 *     }
 */
func ActionCronActivityReward(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newActivityCronRewardParam(c)
	if err != nil {
		return nil, err
	}

	var rooms []*room.Room
	switch param.EventID {
	case 0:
	default:
		return nil, actionerrors.ErrParams
	}

	if rooms == nil {
		return "rooms empty", nil
	}

	for _, r := range rooms {
		for _, giftValue := range param.Gifts {
			f := logger.Fields{
				"room_id":  r.RoomID,
				"gift_id":  giftValue.GiftID,
				"event_id": param.EventID,
			}
			key := keys.KeyGiftsActivityIncreaseCount2.Format(param.EventID, giftValue.GiftID)
			exists, err := service.Redis.SIsMember(key, r.RoomID).Result()
			if err != nil {
				logger.WithFields(f).Error(err)
				continue
			}
			if exists {
				continue
			}
			if err := service.Redis.SAdd(key, r.RoomID).Err(); err != nil {
				logger.WithFields(f).Error(err)
				continue
			}
			if err = param.SendReward(giftValue, r); err != nil {
				logger.WithFields(f).Error(err)
				continue
			}
			logger.WithFields(f).Info("房间活动奖励发放成功")
		}
	}

	return "success", nil
}

func newActivityCronRewardParam(c *handler.Context) (*RewardParam, error) {
	var param RewardParam
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 || len(param.Gifts) == 0 {
		return nil, actionerrors.ErrParams
	}
	if goutil.TimeNow().Unix() < param.StartTime {
		return nil, actionerrors.NewErrForbidden("活动未开始")
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.UserID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	var gifts []int64
	for _, g := range param.Gifts {
		gifts = append(gifts, g.GiftID)
	}
	param.GiftsMap, err = gift.FindGiftMapByGiftIDs(gifts)
	if err != nil {
		logger.Error(err)
	}
	if len(param.GiftsMap) != len(gifts) {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}

	param.bubble, err = userappearance.FindMessageBubble(param.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return &param, nil
}
