package preview

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreview"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreviewuserreservation"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 预约直播状态
const (
	statusNoReservation  = 0 // 未预约直播
	statusHasReservation = 1 // 已预约直播
)

type reservationParam struct {
	PreviewID int64 `json:"preview_id" form:"preview_id"`

	ctx         *handler.Context
	userID      int64
	previewInfo *livepreview.LivePreview
}

type reservationResp struct {
	Msg               string `json:"msg"`
	ReservationStatus int    `json:"reservation_status"`
	FollowedStatus    *int   `json:"followed_status,omitempty"`
}

func newReservationParam(c *handler.Context) (*reservationParam, error) {
	var param reservationParam
	err := c.Bind(&param)
	if err != nil || param.PreviewID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.previewInfo, err = livepreview.FindByID(param.PreviewID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.previewInfo == nil {
		return nil, actionerrors.ErrLivePreviewNotFound
	}
	param.ctx = c
	param.userID = c.UserID()

	return &param, nil
}

// ActionMakeReservation 预约直播
/**
 * @api {post} /api/v2/preview/make-reservation 预约直播
 * @apiDescription 接口具有幂等性
 *
 * @apiVersion 0.1.0
 * @apiName make-reservation
 * @apiGroup /api/v2/preview
 *
 * @apiPermission user
 *
 * @apiParam {Number} preview_id 直播预告 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "已成功预约",
 *         "reservation_status": 1, // 预约直播状态；0: 未预约直播，1: 已预约直播
 *         "followed_status": 1 // 是否已关注主播；0: 未关注，1: 已关注
 *       }
 *     }
 *
 */
func ActionMakeReservation(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newReservationParam(c)
	if err != nil {
		return nil, err
	}

	// 查询主播 ID
	creatorID, err := room.FindCreatorID(param.previewInfo.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if creatorID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if creatorID == param.userID {
		return nil, actionerrors.NewErrForbidden("不能预约自己的直播哟！")
	}
	followed, err := param.followCreator(creatorID)
	if err != nil {
		return nil, err
	}

	exists, err := livepreviewuserreservation.Exists(param.PreviewID, param.userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !exists {
		err = livepreviewuserreservation.MakePreviewReservation(param.PreviewID, param.userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	return reservationResp{
		Msg:               "已成功预约",
		ReservationStatus: statusHasReservation,
		FollowedStatus:    goutil.NewInt(goutil.BoolToInt(followed)),
	}, nil
}

// followCreator 关注主播
func (param *reservationParam) followCreator(creatorID int64) (bool, error) {
	followed, err := attentionuser.HasFollowed(param.userID, creatorID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":    param.userID,
			"creator_id": creatorID,
		}).Errorf("查询用户和主播的关注关系出错：%v", err)
		// PASS
	}
	if followed {
		return true, nil
	}

	// 未关注主播时，进行关注
	err = attentionuser.RPCFollow(param.userID, creatorID, param.ctx)
	if err != nil {
		if attentionuser.IsRPCUserError(err) {
			if rpcError, ok := err.(*mrpc.ClientError); ok && rpcError.Code == handler.CodeBlockedUserByOthers {
				return false, actionerrors.NewErrForbidden("由于对方设置，您暂时无法操作")
			}
			return false, nil
		}
		logger.WithFields(logger.Fields{
			"user_id":    param.userID,
			"creator_id": creatorID,
		}).Errorf("关注主播出错：%v", err)
		// PASS
		return false, nil
	}
	return true, nil
}

// ActionCancelReservation 取消预约直播
/**
 * @api {post} /api/v2/preview/cancel-reservation 取消预约直播
 * @apiDescription 接口具有幂等性
 *
 * @apiVersion 0.1.0
 * @apiName cancel-reservation
 * @apiGroup /api/v2/preview
 *
 * @apiPermission user
 *
 * @apiParam {Number} preview_id 直播预告 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "取消成功",
 *         "reservation_status": 0 // 预约直播状态；0: 未预约直播，1: 已预约直播
 *       }
 *     }
 *
 */
func ActionCancelReservation(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newReservationParam(c)
	if err != nil {
		return nil, err
	}

	exists, err := livepreviewuserreservation.Exists(param.PreviewID, param.userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		err = livepreviewuserreservation.CancelPreviewReservation(param.PreviewID, param.userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	return reservationResp{
		Msg:               "取消成功",
		ReservationStatus: statusNoReservation,
	}, nil
}
