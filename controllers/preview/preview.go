package preview

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "preview",
		Actions: map[string]*handler.Action{
			"make-reservation":   handler.NewAction(handler.POST, ActionMakeReservation, true),
			"cancel-reservation": handler.NewAction(handler.POST, ActionCancelReservation, true),
		},
	}
}
