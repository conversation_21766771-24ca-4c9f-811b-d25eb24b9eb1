package preview

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreview"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreviewuserreservation"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

const (
	testPreviewID = 12345
	testRoomID    = 22489473
)

func TestTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(reservationParam{}, "preview_id")
	kc.Check(reservationResp{}, "msg", "reservation_status", "followed_status")
	kc.CheckOmitEmpty(reservationResp{}, "followed_status")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(reservationParam{}, "preview_id")
}

func TestNewReservationParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/api/v2/preview/make-reservation"
	var param reservationParam
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	_, err := newReservationParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试直播预告卡不存在
	require.NoError(livepreview.LivePreview{}.DB().Delete("", "id = ?", testPreviewID).Error)
	param.PreviewID = testPreviewID
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	_, err = newReservationParam(c)
	assert.Equal(actionerrors.ErrLivePreviewNotFound, err)

	// 测试获取接口参数
	preview := &livepreview.LivePreview{
		ID:        testPreviewID,
		StartTime: util.TimeNow().Add(-time.Minute).Unix(),
		EndTime:   util.TimeNow().Add(time.Minute).Unix(),
	}
	require.NoError(preview.DB().Create(preview).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	res, err := newReservationParam(c)
	require.NoError(err)
	assert.EqualValues(testPreviewID, res.PreviewID)
	assert.NotZero(res.userID)
}

func TestActionMakeReservation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/api/v2/preview/make-reservation"
	preview := &livepreview.LivePreview{
		ID:        testPreviewID,
		StartTime: util.TimeNow().Add(-time.Minute).Unix(),
		EndTime:   util.TimeNow().Add(time.Minute).Unix(),
		RoomID:    9999999,
	}
	require.NoError(preview.DB().Delete("", "id = ?", testPreviewID).Error)
	require.NoError(preview.DB().Create(preview).Error)
	param := reservationParam{
		PreviewID: testPreviewID,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	require.NoError(livepreviewuserreservation.LivePreviewUserReservation{}.DB().
		Delete("", "preview_id = ? AND user_id = ?", testPreviewID, c.UserID()).Error)
	require.NoError(service.DB.Table(attentionuser.TableName()).
		Delete("", "user_active = ? AND user_passtive = ?", 12, 10).Error)
	cancelRPCMock := mrpc.SetMock(attentionuser.URLFollow, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		userID, ok := body["user_id"].(int64)
		require.True(ok)
		switch {
		case userID == 11:
			return nil, &mrpc.ClientError{
				Code: handler.CodeBlockedUser,
			}
		case userID == 13:
			return nil, &mrpc.ClientError{
				Code: handler.CodeBlockedUserByOthers,
			}
		default:
			return nil, nil
		}
	})
	defer cancelRPCMock()

	// 测试直播间不存在
	_, err := ActionMakeReservation(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 测试不能预约自己的直播
	require.NoError(preview.DB().Where("id = ?", testPreviewID).Update("room_id", testRoomID).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	user := c.User()
	user.ID = 10
	_, err = ActionMakeReservation(c)
	assert.EqualError(err, "不能预约自己的直播哟！")

	// 测试用户预约直播
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	result, err := ActionMakeReservation(c)
	require.NoError(err)
	resp, ok := result.(reservationResp)
	require.True(ok)
	assert.Equal("已成功预约", resp.Msg)
	assert.Equal(statusHasReservation, resp.ReservationStatus)
	assert.Equal(1, *resp.FollowedStatus)

	var userReservation livepreviewuserreservation.LivePreviewUserReservation
	require.NoError(userReservation.DB().
		Where("preview_id = ? AND user_id = ?", testPreviewID, c.UserID()).Take(&userReservation).Error)
	assert.EqualValues(testPreviewID, userReservation.PreviewID)
	assert.EqualValues(c.UserID(), userReservation.UserID)
	assert.NotZero(userReservation.CreateTime)
	assert.NotZero(userReservation.ModifiedTime)

	// 测试预约成功但关注失败
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	user = c.User()
	user.ID = 11
	result, err = ActionMakeReservation(c)
	require.NoError(err)
	resp, ok = result.(reservationResp)
	require.True(ok)
	assert.Equal("已成功预约", resp.Msg)
	assert.Equal(statusHasReservation, resp.ReservationStatus)
	assert.Zero(*resp.FollowedStatus)

	// 测试主播拉黑用户
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	user = c.User()
	user.ID = 13
	_, err = ActionMakeReservation(c)
	assert.EqualError(err, "由于对方设置，您暂时无法操作")
}

func TestActionCancelReservation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/api/v2/preview/cancel-reservation"
	preview := &livepreview.LivePreview{
		ID:        testPreviewID,
		StartTime: util.TimeNow().Add(-time.Minute).Unix(),
		EndTime:   util.TimeNow().Add(time.Minute).Unix(),
	}
	require.NoError(preview.DB().Delete("", "id = ?", testPreviewID).Error)
	require.NoError(preview.DB().Create(preview).Error)
	param := reservationParam{
		PreviewID: testPreviewID,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	userReservation := &livepreviewuserreservation.LivePreviewUserReservation{
		PreviewID: testPreviewID,
		UserID:    c.UserID(),
	}
	require.NoError(userReservation.DB().
		Delete("", "preview_id = ? AND user_id = ?", testPreviewID, c.UserID()).Error)
	require.NoError(userReservation.DB().Create(userReservation).Error)

	// 测试用户取消预约直播
	result, err := ActionCancelReservation(c)
	require.NoError(err)
	resp, ok := result.(reservationResp)
	require.True(ok)
	assert.Equal("取消成功", resp.Msg)
	assert.Equal(statusNoReservation, resp.ReservationStatus)
	assert.Nil(resp.FollowedStatus)

	var userReservation1 livepreviewuserreservation.LivePreviewUserReservation
	assert.True(servicedb.IsErrNoRows(userReservation1.DB().
		Where("preview_id = ? AND user_id = ?", testPreviewID, c.UserID()).Take(&userReservation1).Error))
}
