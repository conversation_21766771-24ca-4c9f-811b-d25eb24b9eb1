package report

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/reportlivedailyreport"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewReportLiveInfoParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startDateStr := "2022-02-01"
	endDateStr := "2022-01-02"
	c := handler.NewTestContext(http.MethodGet, "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, nil)
	_, err := newReportLiveInfoParams(c)
	assert.Error(handler.ErrInvalidDateRange, err)

	startDateStr = "2021-01-01"
	endDateStr = "2021-04-31"
	c = handler.NewTestContext(http.MethodGet, "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, nil)
	_, err = newReportLiveInfoParams(c)
	assert.Error(actionerrors.ErrParamsMsg("仅支持查询含 2021-05-01 后的数据"), err)

	startDateStr = "2022-03-20"
	endDateStr = "2022-06-02"
	c = handler.NewTestContext(http.MethodGet, "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, nil)
	_, err = newReportLiveInfoParams(c)
	assert.Error(actionerrors.ErrParamsMsg("最多可选择 60 天的数据查看"), err)

	startDateStr = "2021-04-25"
	endDateStr = "2021-05-25"
	c = handler.NewTestContext(http.MethodGet, "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, nil)
	param, err := newReportLiveInfoParams(c)
	require.NoError(err)
	assert.EqualValues(reportLogsStartTime, param.startDate.Unix())

	// 当输入的结束时间大于当前时间
	now := goutil.TimeNow()
	startDateStr = now.AddDate(0, 0, -1).Format(goutil.TimeFormatYMD)
	endDateStr = now.Format(goutil.TimeFormatYMD)
	c = handler.NewTestContext(http.MethodGet, "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, nil)
	param, err = newReportLiveInfoParams(c)
	require.NoError(err)
	assert.Equal(goutil.BeginningOfDay(now).AddDate(0, 0, -1), param.endDate)
}

func TestActionReportLiveInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	startDateStr := "2022-05-01"
	endDateStr := "2022-06-02"
	c := handler.NewTestContext("GET", "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1&creator_id=10", true, "")

	// 没有权限的人查看
	c.User().ID = 999999
	_, err := ActionReportLiveInfo(c)
	assert.Equal(actionerrors.ErrNoAuthority, err)

	// 有权限的人查看
	c.User().ID = 12
	lc := livecontract.LiveContract{
		ID:            2223334,
		GuildID:       222333,
		LiveID:        10,
		GuildOwner:    c.User().ID,
		Status:        livecontract.StatusContracting,
		ContractStart: 23333333,
		ContractEnd:   goutil.TimeNow().Add(time.Minute).Unix(),
		GuildName:     "测试 GetGuildID 方法",
	}
	err = livecontract.LiveContract{}.DB().FirstOrCreate(lc).Error
	require.NoError(err)
	defer livecontract.LiveContract{}.DB().Delete(livecontract.LiveContract{ID: lc.ID})

	r, err := ActionReportLiveInfo(c)
	require.NoError(err)
	require.NotNil(r)
	require.NotNil(r.(*reportLiveInfoResp))
	assert.Len(r.(*reportLiveInfoResp).Data, 33)

	// 测试默认使用登录用户的 ID
	c = handler.NewTestContext("GET", "/report/live-info?start_date="+startDateStr+"&end_date="+endDateStr+"&p=1", true, "")
	r, err = ActionReportLiveInfo(c)
	require.NoError(err)
	require.NotNil(r)
	require.NotNil(r.(*reportLiveInfoResp))
	resp := r.(*reportLiveInfoResp)
	require.Len(resp.Data, 33)
	assert.Equal(startDateStr, resp.Data[0].BizDate)
	assert.Equal(endDateStr, resp.Data[len(resp.Data)-1].BizDate)
}

func TestBuildMenu(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	testCreatorID := int64(2333666)
	testDuration := int64(108000001)
	err := livelog.Collection().FindOneAndReplace(ctx, bson.M{"creator_id": testCreatorID, "guild_id": 3, "duration": testDuration},
		bson.M{
			"room_id":    0,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2021, 06, 01, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2021, 06, 02, 0, 0, 0, 0, time.Local)),
			"duration":   testDuration,
			"creator_id": testCreatorID,
			"guild_id":   3,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	err = livelog.Collection().FindOneAndReplace(ctx, bson.M{"creator_id": testCreatorID, "guild_id": 0, "duration": testDuration},
		bson.M{
			"room_id":    0,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2021, 06, 04, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2021, 06, 05, 0, 0, 0, 0, time.Local)),
			"duration":   testDuration,
			"creator_id": testCreatorID,
			"guild_id":   0,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	startDate := time.Date(2021, 06, 01, 0, 0, 0, 0, time.Local)
	endDate := time.Date(2021, 06, 07, 0, 0, 0, 0, time.Local)
	// 主播自己查看时
	p := &reportLiveInfoParams{
		creatorID: testCreatorID,
		startDate: startDate,
		endDate:   endDate,
	}
	l := reportLiveInfoResp{}
	err = l.BuildMenu(p)
	require.NoError(err)
	assert.EqualValues(50, l.Menu.NewFansNum)
	assert.EqualValues(util.Float2DP(testDuration*2)/1000/60, l.Menu.TotalLiveDuration)
}

func TestReportLiveInfoResp_buildMenuNewFans(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := reportLiveInfoParams{
		startDate: time.Date(2021, 06, 01, 0, 0, 0, 0, time.Local),
		endDate:   time.Date(2021, 06, 07, 0, 0, 0, 0, time.Local),
	}
	count, err := p.buildMenuNewFans()
	require.NoError(err)
	assert.Zero(count)
	p.creatorID = 2333666
	count, err = p.buildMenuNewFans()
	require.NoError(err)
	assert.EqualValues(50, count)
	p.startDate = p.startDate.AddDate(0, 0, -1)
	count, err = p.buildMenuNewFans()
	require.NoError(err)
	assert.EqualValues(50, count)
}

func TestCountNewFans(t *testing.T) {
	assert := assert.New(t)

	startTime := goutil.BeginningOfDay(goutil.TimeNow())
	prevTime := startTime.AddDate(0, 0, -1)
	list := []*reportlivedailyreport.ReportLiveDailyReport{
		{BizDate: startTime, FollowerCount: 50},
	}
	assert.EqualValues(50, CountNewFans(list, prevTime))
	list = []*reportlivedailyreport.ReportLiveDailyReport{
		{BizDate: prevTime, FollowerCount: 20},
		{BizDate: startTime, FollowerCount: 50},
	}
	assert.EqualValues(30, CountNewFans(list, prevTime))
}

func TestBuildList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := livelog.Collection().FindOneAndReplace(ctx, bson.M{"room_id": 2233552, "guild_id": 0, "duration": 108000001},
		bson.M{
			"room_id":    2233552,
			"start_time": goutil.NewTimeUnixMilli(time.Date(2022, 06, 02, 0, 0, 0, 0, time.Local)),
			"end_time":   goutil.NewTimeUnixMilli(time.Date(2022, 06, 03, 0, 0, 0, 0, time.Local)),
			"duration":   108000001,
			"creator_id": 1,
			"guild_id":   0,
		}, options.FindOneAndReplace().SetUpsert(true).SetReturnDocument(options.After)).Err()
	require.NoError(err)

	p := &reportLiveInfoParams{
		creatorID: 1,
		startDate: time.Date(2022, 06, 01, 0, 0, 0, 0, time.Local),
		endDate:   time.Date(2022, 06, 05, 0, 0, 0, 0, time.Local),
	}
	l := reportLiveInfoResp{}
	err = l.BuildList(p)
	require.NoError(err)
	require.Len(l.Data, 5)
	assert.EqualValues(10, l.Data[2].NewFansNum)
	assert.EqualValues(20, l.Data[2].UnfollowedFansNum)
	assert.EqualValues(util.Float2DP(108000001)/1000/60, l.Data[1].LiveDuration)
}
