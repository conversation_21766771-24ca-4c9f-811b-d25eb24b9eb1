package report

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/reportlivedailyreport"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 2021-05-01 00:00:00 的时间戳（秒），数据从 2021-05-01 开始更新
	reportLogsStartTime int64 = 1619798400
	queryDayMax               = 60
	queryDayMaxInterval       = time.Hour * 24 * queryDayMax
)

type reportLiveInfoParams struct {
	startDate time.Time
	endDate   time.Time

	creatorID int64
	guildID   int64
}

// reportLiveInfoResp 返回结果
type reportLiveInfoResp struct {
	Menu Menu                  `json:"menu"`
	Data []LiveDailyReportItem `json:"data"`
}

// LiveDailyReportItem 数据详情
type LiveDailyReportItem struct {
	BizDate string `json:"bizdate"`
	// 总开播时长（单位：分钟）
	LiveDuration util.Float2DP `json:"live_duration"`
	// 人均收听时长（单位：分钟）
	ListenerDurationAvg util.Float2DP `json:"listener_duration_avg"`
	// 超 5 分钟收听人数占比（单位：百分比）
	ListenerDurationMoreThanFiveMinutesRatio util.Float2DP `json:"listener_duration_more_than_five_minutes_ratio"`
	NewFansNum                               int64         `json:"new_fans_num"`
	UnfollowedFansNum                        int64         `json:"unfollowed_fans_num"`
}

// Menu 菜单栏数据
type Menu struct {
	NewFansNum int64 `gorm:"column:new_fans_num" json:"new_fans_num"`
	// 总开播时长（单位：分钟）
	TotalLiveDuration util.Float2DP `gorm:"-" json:"total_live_duration"`
	// 人均收听时长（单位：分钟）
	TotalListenerDurationAvg util.Float2DP `gorm:"column:total_listener_duration_avg" json:"total_listener_duration_avg"`
	// 超 5 分钟收听人数占比（单位：百分比）
	ListenerDurationMoreThanFiveMinutesRatio util.Float2DP `gorm:"column:listener_duration_more_than_five_minutes_ratio" json:"listener_duration_more_than_five_minutes_ratio"`
}

// ActionReportLiveInfo 主播报表详情
/**
 * @api {get} /api/v2/report/live-info 主播报表详情
 * @apiDescription 主播自己、公会会长和主播的经纪人可以访问，
 *   当公会会长和主播的经纪人访问时，返回筛选时间范围内主播在该公会合约（合约解约、合约失效、合约生效中等状态）时间范围内数据
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/report/live-info
 *
 * @apiParam {Number} [creator_id] 主播 ID，默认为当前登录的用户 ID
 * @apiParam {String} [start_date] 筛选的起始时间，默认为 30 天前，格式 "2021-05-01"
 * @apiParam {String} [end_date] 筛选的结束时间，默认为今天，格式 "2021-05-31" 开始时间至结束时间最大间隔为 60 天
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "menu": {
 *         "total_live_duration": 100.01, // 总开播时长（分钟）
 *         "total_listener_duration_avg": 300.01, // 人均收听时长（分钟）
 *         "listener_duration_more_than_five_minutes_ratio": 10.23, // 超 5 分钟收听人数占比（单位：百分比）
 *         "new_fans_num": 23 // 新增粉丝
 *       },
 *       "data": [
 *         {
 *           "bizdate": "2021-04-01", // 日期
 *           "live_duration": 100.01, // 开播时长（分钟）
 *           "listener_duration_avg": 30.01, // 人均收听时长（分钟）
 *           "listener_duration_more_than_five_minutes_ratio": 10.23, // 超 5 分钟收听人数占比（单位：百分比）
 *           "new_fans_num": 20, // 新增粉丝
 *           "unfollowed_fans_num": 23 // 取关粉丝数
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionReportLiveInfo(c *handler.Context) (handler.ActionResponse, error) {
	params, err := newReportLiveInfoParams(c)
	if err != nil {
		return nil, err
	}

	resp, err := newReportLiveInfoResp(params)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func newReportLiveInfoParams(c *handler.Context) (*reportLiveInfoParams, error) {
	param := new(reportLiveInfoParams)
	var err error
	param.startDate, param.endDate, err = c.GetParamDateRange(goutil.TimeNow().AddDate(0, 0, -30).Format(goutil.TimeFormatYMD),
		goutil.TimeNow().Format(goutil.TimeFormatYMD))
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	if param.endDate.Unix() < reportLogsStartTime {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("仅支持查询 %s 后的数据", time.Unix(reportLogsStartTime, 0).Format(goutil.TimeFormatYMD)))
	}

	if param.endDate.Sub(param.startDate) > queryDayMaxInterval {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("最多可选择 %d 天的数据查看", queryDayMax))
	}

	if param.startDate.Unix() < reportLogsStartTime {
		// 查询时间小于日志的开始时间时，设置查询时间为日志开始时间
		param.startDate = time.Unix(reportLogsStartTime, 0)
	}

	// 不获取当天数据
	beginningOfToday := goutil.BeginningOfDay(goutil.TimeNow())
	if goutil.TimeGte(param.endDate, beginningOfToday) {
		param.endDate = beginningOfToday.AddDate(0, 0, -1)
	}

	param.creatorID, err = c.GetDefaultParamInt64("creator_id", c.UserID())
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.creatorID != c.UserID() {
		isGuildManager, liveContract, err := chatroom.CheckOwnerOrAgentTimeRange(param.creatorID, c.UserID(), &param.startDate, param.endDate)
		if err != nil {
			return nil, err
		}
		if !isGuildManager {
			return nil, actionerrors.ErrNoAuthority
		}
		param.guildID = liveContract.GuildID
	}

	return param, nil
}

func newReportLiveInfoResp(params *reportLiveInfoParams) (*reportLiveInfoResp, error) {
	resp := new(reportLiveInfoResp)
	err := resp.BuildList(params)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	err = resp.BuildMenu(params)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return resp, nil
}

// BuildList 生成列表
func (resp *reportLiveInfoResp) BuildList(param *reportLiveInfoParams) error {
	list, err := reportlivedailyreport.ListLiveDailyReport(param.creatorID, param.guildID, param.startDate, param.endDate)
	if err != nil {
		return err
	}

	// 获取开播时长（包含截止日期当天的数据）
	endTime := goutil.BeginningOfDay(param.endDate).AddDate(0, 0, 1)
	durationMap, err := livelog.GetUserDayLiveDuration(param.creatorID, param.guildID, param.startDate, endTime)
	if err != nil {
		return err
	}

	resp.Data = make([]LiveDailyReportItem, 0, len(list))
	for _, v := range list {
		liveDuration := util.Float2DP(0)
		if durationMap[v.BizDate] != nil {
			// 毫秒转分钟
			liveDuration = util.Float2DP(durationMap[v.BizDate].TotalDuration) / 1000 / 60
		}

		resp.Data = append(resp.Data, LiveDailyReportItem{
			BizDate:                                  v.BizDate,
			ListenerDurationAvg:                      v.ListenerDurationAvg,
			ListenerDurationMoreThanFiveMinutesRatio: v.ListenerDurationMoreThanFiveMinutesRatio,
			NewFansNum:                               v.Follow,
			UnfollowedFansNum:                        v.Unfollow,
			LiveDuration:                             liveDuration,
		})
	}
	return nil
}

// BuildMenu 生成菜单
func (resp *reportLiveInfoResp) BuildMenu(param *reportLiveInfoParams) error {
	fields := "COALESCE(ROUND(SUM(rli.listener_duration_total) / SUM(rli.listener_user_num) * 100, 2) / 100, 0) AS total_listener_duration_avg" +
		", COALESCE(ROUND(SUM(rli.listener_duration_more_than_five_minutes_num) / SUM(rli.listener_user_num) * 100, 2), 0.00) AS listener_duration_more_than_five_minutes_ratio" +
		", COALESCE(SUM(rli.follow), 0) AS new_fans_num"

	err := reportlivedailyreport.ReportLiveDailyReport{}.DB().
		Table(reportlivedailyreport.ReportLiveDailyReport{}.TableName()+" AS rli").
		Select(fields).
		Where("rli.creator_id = ? AND rli.bizdate >= ? AND rli.bizdate <= ?",
			param.creatorID, param.startDate.Format(goutil.TimeFormatYMD), param.endDate.Format(goutil.TimeFormatYMD)).
		Scan(&resp.Menu).Error
	if err != nil {
		return err
	}

	newFans, err := param.buildMenuNewFans()
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		resp.Menu.NewFansNum = newFans
	}

	// 开播时长（包含截止日期当天的数据）
	endTime := goutil.BeginningOfDay(param.endDate).AddDate(0, 0, 1)
	duration, err := livelog.LiveTotalDuration(param.creatorID, 0, param.startDate, endTime)
	if err != nil {
		return err
	}
	if duration > 0 {
		// 毫秒转分钟
		resp.Menu.TotalLiveDuration = util.Float2DP(duration) / 1000 / 60
	}
	return nil
}

// CountNewFans 根据报告计算时间内新增粉丝数
func CountNewFans(report []*reportlivedailyreport.ReportLiveDailyReport, prevDate time.Time) int64 {
	if len(report) == 0 {
		return 0
	}
	prevFollowerCount := report[0].FollowerCount
	if report[0].BizDate.Format(goutil.TimeFormatYMD) != prevDate.Format(goutil.TimeFormatYMD) {
		// 如果报告没有 prevDate 的数据则视为主播初始没有粉丝数
		prevFollowerCount = 0
	}
	// 返回正数的粉丝数变化
	return max(report[len(report)-1].FollowerCount-prevFollowerCount, 0)
}

func (param *reportLiveInfoParams) buildMenuNewFans() (int64, error) {
	prevDate := param.startDate.AddDate(0, 0, -1)
	reportMap, err := reportlivedailyreport.ListLiveDailyReportMap("follower_count", []int64{param.creatorID}, prevDate, param.endDate)
	if err != nil {
		return 0, err
	}
	report := reportMap[param.creatorID]
	if report == nil {
		return 0, nil
	}
	return CountNewFans(report, prevDate), nil
}
