package report

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	checker := tutil.NewKeyChecker(t, tutil.Actions)

	t.Run("report", func(t *testing.T) {
		assert.Equal("report", h.Name)

		checker.Check(h.Actions,
			"live-info",
		)
	})
}
