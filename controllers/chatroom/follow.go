package chatroom

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/activity/rankevent"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	followListTypeAll = iota
	followListTypeOpenOnly
)

const notiyfLimitPerSec = 8

func init() {
	attentionuser.RegisterCacheInvalidateFunc(func(userID int64) {
		err := service.LRURedis.Del(
			keys.KeyMedalRoomFollowList2.Format(userID, followListTypeAll),
			keys.KeyMedalRoomFollowList2.Format(userID, followListTypeOpenOnly),
		).Err()
		if err != nil {
			logger.WithField("user_id", userID).Error(err)
			// PASS
		}
	})
}

type followListParam struct {
	c        *handler.Context
	u        *user.User
	listType int
	p        int64
	pageSize int64

	roomOpt *room.FindOptions
}

// ActionFollowList 列出关注的主播及房间
/**
 * @api {get} /api/v2/chatroom/follow/list 关注的直播间列表
 * @apiDescription 列出的有直播间的关注用户及其房间信息，需要登录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [type=0] 控制返回的榜单类型，0: 列出所有的，1: 列出正在开播的
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 一页显示数目
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "room_id": 152,
 *           "name": "12345",
 *           "announcement": "12345",
 *           "creator_id": 12345,
 *           "creator_username": "1234",
 *           "creator_iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "creator_introduction": "主播个人简介",
 *           "statistics": {
 *             "gift_count": 1,
 *             "question_count": 12,
 *             "revenue": 12,
 *             "point": 12.111,
 *             "accumulation": 123,
 *             "attention": true,
 *             "attention_count": 123,
 *             "online": 123,
 *             "score": 0
 *           },
 *           "status": {
 *             "open": 1
 *           },
 *           "cover_url": "直播间封面"
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionFollowList(c *handler.Context) (handler.ActionResponse, error) {
	param := followListParam{c: c}
	if err := param.load(); err != nil {
		return nil, err
	}
	return param.resp()
}

func (param *followListParam) load() (err error) {
	param.listType, _ = param.c.GetParamInt("type")
	if param.listType < followListTypeAll || param.listType > followListTypeOpenOnly {
		param.listType = followListTypeAll
	}
	param.p, param.pageSize, err = param.c.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}
	param.u = param.c.User()
	param.roomOpt = &room.FindOptions{
		FindOnline:      true,
		ClientIP:        param.c.ClientIP(),
		FindCreator:     true,
		FindFans:        true,
		FindCatalogInfo: true,
	}
	return
}

func (param *followListParam) resp() (*roomSimpleList, error) {
	userIDs, err := attentionuser.AllFollowedCreatorIDs(param.u.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	type m bson.M
	filter := m{
		"creator_id": m{"$in": userIDs},
		"limit":      m{"$exists": false},
	}
	if param.listType == followListTypeOpenOnly {
		filter["status.open"] = room.StatusOpenTrue
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	pa := goutil.MakePagination(count, param.p, param.pageSize)
	if !pa.Valid() {
		return &roomSimpleList{
			Data:       make([]*room.Simple, 0),
			Pagination: pa,
		}, nil
	}
	mongoOpt := pa.SetFindOptions(options.Find().SetSort(room.SortByOpenScore))
	rooms, err := room.ListSimples(filter, mongoOpt, param.roomOpt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := roomSimpleList{
		Data:       rooms,
		Pagination: pa,
	}
	resp.checkResp()
	return &resp, nil
}

type markerPaginationWithCount struct {
	Count                   *int64 `json:"count,omitempty"`
	goutil.MarkerPagination `json:",inline"`
}

// followRoomListParam 获取关注的直播间列表所需参数
type followRoomListParam struct {
	c        *handler.Context
	u        *user.User
	listType int
	marker   *followRoomListMarkerParam
	pageSize int64

	roomOpt *room.FindOptions
}

// followRoomListMarkerParam 标识参数
type followRoomListMarkerParam struct {
	point    int64 // 亲密度
	score    int64 // 热度
	openTime int64 // 开播时间时间戳，单位：毫秒
}

// followRoomListResp 关注的主播及房间列表
type followRoomListResp struct {
	Data         []*room.Simple            `json:"data"`
	FollowStatus *int                      `json:"follow_status,omitempty"`
	Pagination   markerPaginationWithCount `json:"pagination"`
}

type simpleRoomWithPoint struct {
	Room  *room.Simple `json:"room"`
	Point int64        `json:"point"` // 亲密度
}

// ActionFollowRoomList 列出关注的主播及房间
/**
 * @api {get} /api/v2/chatroom/follow/room-list 关注的直播间列表
 * @apiDescription 列出所有关注用户的直播间及房间信息。客户端需要对新加载的和前面重复出现的直播间去重处理
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiPermission user
 *
 * @apiParam {Number} [type=0] 控制返回的榜单类型，0: 列出所有的，1: 列出正在开播的
 * @apiParam {String} [marker] 分页标记，请求第一页数据时不传该参数
 * @apiParam {Number} [page_size=20] 一页显示数目
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "room_id": 152,
 *           "name": "直播间名称",
 *           "announcement": "公告",
 *           "cover_url": "https://static.maoercdn.com/fmcovers/202308/24/7e33de56800dd40fe31f5992cedcc0c9.jpg",
 *           "creator_id": 12345,
 *           "creator_username": "主播昵称",
 *           "creator_iconurl": "https://static.maoercdn.com/avatars/icon01.png",
 *           "catalog_id": 149,
 *           "catalog_name": "配音",
 *           "catalog_color": "#D68DFE",
 *           "custom_tag": { // 个性词条
 *             "tag_id": 10001, // 个性词条 ID
 *             "tag_name": "腹黑青叔" // 个性词条名称
 *           },
 *           "recommend_tag": { // 推荐标签，显示下发的字段，如果都存在，则优先显示 icon_url
 *             "type": 1, // 推荐类型：1: 推荐标签, 2: 上小时榜, 3: 运营配置标签, 4: 个性词条
 *             "text": "我的关注",
 *             "icon_url": "https://static.maoercdn.com/live/labelicon/livelist/lasthour01-1.png"
 *           },
 *           "statistics": {
 *             "score": 0
 *           },
 *           "status": {
 *             "open": 1 // 开播状态 0：未开播；1：开播
 *           }
 *         }
 *       ],
 *       "follow_status": 0, // 用户关注状态，仅在 type = 1 且获取第一页开播列表时下发。0：无关注的主播；1：有关注的主播
 *       "pagination": {
 *         "count": 30, // 关注列表开播中的直播间数量，只在获取第一页开播列表时下发
 *         "has_more": true, // 是否有更多数据
 *         "marker": "40,0,1695717196620" // 当前的 marker，需要加载下一页时回传
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionFollowRoomList(c *handler.Context) (handler.ActionResponse, error) {
	param := followRoomListParam{c: c}
	if err := param.load(); err != nil {
		return nil, err
	}
	return param.resp()
}

// load 参数处理
func (param *followRoomListParam) load() (err error) {
	param.listType, _ = param.c.GetParamInt("type")
	if param.listType < followListTypeAll || param.listType > followListTypeOpenOnly {
		return actionerrors.ErrParams
	}
	marker, _ := param.c.GetParamString("marker")
	err = param.parseMarker(marker)
	if err != nil {
		logger.WithField("marker", marker).Warn(err)
		return actionerrors.ErrParams
	}
	_, param.pageSize, err = param.c.GetParamPage()
	if err != nil {
		return actionerrors.ErrParams
	}
	param.u = param.c.User()
	param.roomOpt = &room.FindOptions{
		FindOnline:      true,
		ClientIP:        param.c.ClientIP(),
		FindCreator:     true,
		FindCatalogInfo: true,
		FindCustomTag:   true,
	}
	// Web 端不显示福袋图标
	if param.c.Equip().FromApp {
		param.roomOpt.FindLuckyBag = true
	}
	return
}

// parseMarker 解析 marker 参数，获取上一页最后一个直播间的亲密度、热度、开播时间
func (param *followRoomListParam) parseMarker(marker string) error {
	if marker == "" {
		return nil
	}
	s := strings.Split(marker, ",")
	if len(s) != 3 {
		return errors.New("参数格式错误")
	}
	point, err := strconv.ParseInt(s[0], 10, 64)
	if err != nil {
		return err
	}
	score, err := strconv.ParseInt(s[1], 10, 64)
	if err != nil {
		return err
	}
	openTime, err := strconv.ParseInt(s[2], 10, 64) // 时间戳，单位：毫秒
	if err != nil {
		return err
	}
	if point < 0 || score < 0 || openTime < 0 {
		return errors.New("参数值不在有效范围内")
	}
	param.marker = &followRoomListMarkerParam{
		point:    point,
		score:    score,
		openTime: openTime,
	}
	return nil
}

// buildMarker 拼接 marker 参数
func (resp *followRoomListResp) buildMarker(point, score, openTime int64) {
	resp.Pagination.Marker = fmt.Sprintf("%d,%d,%d", point, score, openTime)
}

// resp 列出关注的主播及房间
func (param *followRoomListParam) resp() (*followRoomListResp, error) {
	userIDs, err := attentionuser.AllFollowedCreatorIDs(param.u.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var resp followRoomListResp
	var count int64
	if len(userIDs) == 0 {
		if param.listType == followListTypeOpenOnly && param.marker == nil {
			resp.Pagination.Count = &count
			resp.FollowStatus = goutil.NewInt(0)
		}
		return &resp, nil
	}
	type m bson.M
	filter := m{
		"limit": m{"$exists": false},
	}
	if param.listType == followListTypeOpenOnly {
		filter["status.open"] = room.StatusOpenTrue
		if param.marker == nil {
			// 请求第一页开播列表时获取直播间数量
			filter["creator_id"] = m{"$in": userIDs}
			count, err = room.CountByFilter(filter)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			resp.Pagination.Count = &count
			if count > 0 {
				resp.FollowStatus = goutil.NewInt(1)
			} else {
				// 查询是否有关注的主播
				exists, err := room.Exists2(m{
					"limit":      m{"$exists": false},
					"creator_id": m{"$in": userIDs},
				})
				if err != nil {
					logger.Error(err)
					// PASS
				}
				resp.FollowStatus = goutil.NewInt(goutil.BoolToInt(exists))
			}
		}
	}
	var liveMedalRooms []simpleRoomWithPoint
	allRooms := make([]*room.Simple, 0, param.pageSize)
	liveMedalRooms, err = param.getMedalFollowRooms(userIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	liveMedalRoomsLength := int64(len(liveMedalRooms))
	if liveMedalRoomsLength > 0 {
		var lastRoomPoint int64
		if param.marker == nil {
			// 第一页数据
			for _, r := range liveMedalRooms {
				allRooms = append(allRooms, r.Room)
				if int64(len(allRooms)) >= param.pageSize {
					lastRoomPoint = r.Point
					break
				}
			}
		} else if param.marker.point > 0 {
			// 下一页还需获取有亲密度的直播间信息
			for _, r := range liveMedalRooms {
				if param.marker.point > r.Point {
					allRooms = append(allRooms, r.Room)
					if int64(len(allRooms)) >= param.pageSize {
						lastRoomPoint = r.Point
						break
					}
				}
			}
		}
		if int64(len(allRooms)) >= param.pageSize {
			resp.Data = allRooms
			var hasMoreCount int64
			if resp.Pagination.Count != nil {
				hasMoreCount = *resp.Pagination.Count
			} else {
				hasMoreCount, err = room.CountByFilter(filter)
				if err != nil {
					logger.Error(err)
					// PASS
				}
			}
			resp.Pagination.HasMore = hasMoreCount > liveMedalRoomsLength
			if resp.Pagination.HasMore {
				resp.buildMarker(lastRoomPoint, 0, 0)
			}
			return &resp, nil
		}
		// 过滤掉有亲密度的关注主播
		medalRoomCreatorIDs := make([]int64, liveMedalRoomsLength)
		for _, r := range liveMedalRooms {
			medalRoomCreatorIDs = append(medalRoomCreatorIDs, r.Room.CreatorID)
		}
		userIDs = util.SetDifferenceInt64(userIDs, medalRoomCreatorIDs)
	}
	if len(userIDs) != 0 {
		// 过滤掉有亲密度的关注主播后重新赋值
		filter["creator_id"] = m{"$in": userIDs}
		// 首次获取没有亲密度的直播间信息时，需要把 marker 置空，防止出现数据缺失的情况
		if param.marker != nil && param.marker.point > 0 && param.marker.score == 0 && param.marker.openTime == 0 {
			param.marker = nil
		}
		// 排序优先级：开播状态 > 亲密度 > 热度 > 开播时间
		if param.marker != nil {
			// 场景：分页时出现有变动的直播间
			// 1. 新开播的直播间热度 < 前页最后一个直播间的热度时
			// marker 中 score > 0，查询语句的排序（SortByOpenScore）优先级也是热度 > 开播时间，获取的数据符合预期
			// marker 中 score = 0，会对 open_time 做限制，获取的数据符合预期
			// 2. 新开播的直播间热度 > 前页最后一个直播间的热度 或 突然有主播关播（score 会设为 0）时，下页数据获取可能会有重复
			// 因客户端会进行去重处理，即使接口下发了重复数据对用户也不会有明显的影响
			if param.marker.score == 0 {
				// 因为热度的排序优先级比开播时间高，所以在获取下一页热度为 0 的直播间时，再加上开播时间的筛选
				filter["status.score"] = m{"$eq": 0}
				// 按照开播时间降序排，所以获取 < marker 参数中的 open_time 值
				filter["status.open_time"] = m{"$lt": param.marker.openTime}
			} else {
				// 按照热度降序排，所以获取 < marker 参数中的 score 值
				filter["status.score"] = m{"$lt": param.marker.score}
			}
		}
		// 根据有亲密度的关注主播房间数量，计算没有亲密度的关注主播房间数量
		limit := param.pageSize - int64(len(allRooms))
		// 多查一条，用于判断下一页是否有数据
		opt := options.Find().SetLimit(limit + 1).SetSort(room.SortByOpenScore)
		rooms, err := room.ListSimples(filter, opt, param.roomOpt)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if len(rooms) > 0 {
			allRooms = append(allRooms, rooms...)
		}
	}
	if len(allRooms) == 0 {
		return &resp, nil
	}

	buildRecommendTags(allRooms)

	hasMore := int64(len(allRooms)) > param.pageSize
	var pageIndex int64
	if hasMore {
		pageIndex = param.pageSize
	} else {
		pageIndex = int64(len(allRooms))
	}

	resp.Data = allRooms[:pageIndex]
	resp.Pagination.HasMore = hasMore
	if resp.Pagination.HasMore {
		lastRoom := allRooms[pageIndex-1]
		resp.buildMarker(0, lastRoom.Statistics.Score, lastRoom.Status.OpenTime)
	}
	return &resp, nil
}

func buildRecommendTags(allRooms []*room.Simple) {
	rankParam, err := params.FindRank()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	icons := rankParam.LastHourRecommendIcons(goutil.TimeNow())
	lastHourTop3CreatorIDs, err := usersrank.ListLastHourTop3CreatorIDs()
	if err != nil {
		logger.Error(err)
		return
	}
	lastHourTop3Map := make(map[int64]string, len(lastHourTop3CreatorIDs))
	for i, creatorID := range lastHourTop3CreatorIDs {
		lastHourTop3Map[creatorID] = icons[i]
	}

	// TODO: 支持剩余推荐标签，推荐标签 > 上小时榜角标 > 运营配置角标 > 个性词条
	for _, v := range allRooms {
		if icon, ok := lastHourTop3Map[v.CreatorID]; ok {
			v.RecommendTag = &room.RecommendTag{
				Type:    room.RecommendTagTypeLastHourRank,
				IconURL: service.Storage.Parse(icon),
			}
		} else if v.CustomTag != nil {
			v.RecommendTag = &room.RecommendTag{
				Type: room.RecommendTagTypeCustomTag,
				Text: v.CustomTag.TagName,
			}
		}
	}
}

// getMedalFollowRooms 获取有亲密度的关注主播房间信息
func (param *followRoomListParam) getMedalFollowRooms(userIDs []int64) ([]simpleRoomWithPoint, error) {
	key := keys.KeyMedalRoomFollowList2.Format(param.u.ID, param.listType)
	roomBytes, err := service.LRURedis.Get(key).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return nil, err
	}
	if len(roomBytes) != 0 {
		var liveMedalRooms []simpleRoomWithPoint
		err = json.Unmarshal(roomBytes, &liveMedalRooms)
		if err != nil {
			return nil, err
		}
		return liveMedalRooms, nil
	}
	type m bson.M
	filter := m{
		"user_id":    param.u.ID,
		"creator_id": bson.M{"$in": userIDs},
		"status":     bson.M{"$gt": livemedal.StatusPending},
	}
	liveMedals, err := livemedal.List(filter, nil, &livemedal.FindOptions{DisableAll: true})
	if err != nil {
		return nil, err
	}
	var liveMedalRooms []simpleRoomWithPoint
	if len(liveMedals) != 0 {
		liveMedalCreatorIDs := make([]int64, 0, len(liveMedals))
		roomUserPointMap := make(map[int64]int64, len(liveMedals))
		for _, l := range liveMedals {
			liveMedalCreatorIDs = append(liveMedalCreatorIDs, l.CreatorID)
			roomUserPointMap[l.RoomID] = l.Point
		}
		liveMedalRoomFilter := m{
			"creator_id": m{"$in": liveMedalCreatorIDs},
		}
		if param.listType == followListTypeOpenOnly {
			liveMedalRoomFilter["status.open"] = room.StatusOpenTrue
		}
		var rooms []*room.Simple
		rooms, err = room.ListSimples(liveMedalRoomFilter, options.Find().SetSort(room.SortByOpenScore), param.roomOpt)
		if err != nil {
			return nil, err
		}
		liveMedalRooms = make([]simpleRoomWithPoint, len(rooms))
		if len(rooms) != 0 {
			for k, r := range rooms {
				liveMedalRooms[k] = simpleRoomWithPoint{
					Room:  r,
					Point: roomUserPointMap[r.RoomID],
				}
			}
			// 排序优先级：开播状态 > 亲密度 > 热度 > 开播时间（热度和开播时间的排序在上面查询里已处理）
			sort.SliceStable(liveMedalRooms, func(i, j int) bool {
				if param.listType == followListTypeAll {
					if liveMedalRooms[i].Room.Status.Open == liveMedalRooms[j].Room.Status.Open {
						return liveMedalRooms[i].Point > liveMedalRooms[j].Point
					}
					return liveMedalRooms[i].Room.Status.Open > liveMedalRooms[j].Room.Status.Open
				}
				return liveMedalRooms[i].Point > liveMedalRooms[j].Point
			})
		}
	}
	value, err := json.Marshal(liveMedalRooms)
	if err != nil {
		return nil, err
	}
	err = service.LRURedis.Set(key, string(value), 5*time.Minute).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return liveMedalRooms, nil
}

// ActionFollow 关注/取关直播间
/**
 * @api {post} /api/v2/chatroom/follow 关注/取关直播间
 * @apiDescription 关注/取关直播间，需要登录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {string="add","remove"} type 关注：add；取关：remove
 * @apiParam {Number} room_id 房间号
 * @apiParam {number=0,1} [notify=0] 是否广播，0：不广播，1：广播
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "ok": 1,
 *       "type": 1, // 1: 关注，0: 取关
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *     {
 *       "type": "member",
 *       "event": "followed",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [{
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *         "type": "noble", // 气泡类型，目前支持: 贵族气泡 noble
 *         "noble_level": 2 // 使用对应等级的贵族气泡
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionFollow(c *handler.Context) (handler.ActionResponse, error) {
	const (
		typeAdd    = "add"
		typeRemove = "remove"
	)
	var param struct {
		RoomID int64  `json:"room_id" form:"room_id"`
		Type   string `json:"type" form:"type"`
		Notify int    `json:"notify" form:"notify"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 ||
		(param.Type != typeAdd && param.Type != typeRemove) {
		return nil, actionerrors.ErrParams
	}
	roomInfo, err := room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomInfo == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	// 被主播拉黑或拉黑了主播，无法关注主播
	if param.Type == typeAdd {
		// NOTICE: RPCFollow 有一方拉黑则无法关注，这里增加判断是为了返回指定的错误信息
		fromBlockTo, toBlockFrom, err := userapi.UserBlockStatus(roomInfo.CreatorID, c.UserID())
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if fromBlockTo {
			return nil, actionerrors.NewErrBlockUser("由于对方设置，你还不能关注")
		}
		if toBlockFrom {
			return nil, actionerrors.NewErrBlockUser("您已拉黑对方，无法关注主播")
		}
	}
	var typeCode int
	switch param.Type {
	case typeAdd:
		typeCode = 1
		err = attentionuser.RPCFollow(c.UserID(), roomInfo.CreatorID, c)
		if err == nil && param.Notify == 1 {
			broadcastFollow(param.RoomID, c)
		}
	case typeRemove:
		typeCode = 0
		err = attentionuser.RPCUnfollow(c.UserID(), roomInfo.CreatorID, c)
	}
	if err != nil {
		return nil, err
	}
	if param.Type == typeAdd {
		sendFollowRankEvent(c, roomInfo)
	}
	return handler.M{
		"ok":   1,
		"type": typeCode,
	}, nil
}

func sendFollowRankEvent(ctx *handler.Context, roomInfo *room.Room) {
	userID := ctx.UserID()
	err := rankevent.NewSyncCommonParam(userID).
		SetRoomInfo(roomInfo.RoomID, roomInfo.CreatorID, roomInfo.GuildID, roomInfo.ActivityCatalogID).
		Follow(userID, roomInfo.CreatorID).Send(ctx.UserContext())
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":        userID,
			"follow_user_id": roomInfo.CreatorID,
		}).Error(err)
		// PASS
	}
}

func broadcastFollow(roomID int64, c *handler.Context) {
	userID := c.UserID()
	s := liveim.LiveJudgmentUserIDSet()
	if _, ok := s[userID]; ok {
		return
	}
	gs, _, err := userstatus.UserGeneral(userID, c)
	if err != nil {
		logger.Error(err)
		return
	}
	if gs.Invisible != nil && *gs.Invisible {
		return
	}

	key, lock := roomFollowedKeyLock(roomID, goutil.TimeNow())
	addSuccess, err := service.Redis.SAdd(key, userID).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	if addSuccess == 0 {
		return
	}
	pipe := service.Redis.TxPipeline()
	cmd := pipe.Incr(lock)
	pipe.Expire(lock, 2*time.Second) // 防止不同机器有时间差，所以 TTL 是 2s 不是 1s
	_, err = pipe.Exec()
	var pos int64
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		pos = cmd.Val()
	}
	if pos > notiyfLimitPerSec {
		return
	}
	u, err := liveuser.FindOneSimple(bson.M{"user_id": userID}, &liveuser.FindOptions{FindTitles: true, RoomID: roomID})
	if err != nil {
		logger.Error(err)
		return
	}
	if u == nil {
		return
	}
	m := map[string]interface{}{
		"type":    liveim.TypeMember,
		"event":   liveim.EventFollowed,
		"room_id": roomID,
		"user":    u,
	}

	b, err := userappearance.FindMessageBubble(userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if b != nil {
		m["bubble"] = b
	}
	if err := userapi.Broadcast(roomID, m); err != nil {
		logger.Error(err)
		return
	}
}

func roomFollowedKeyLock(roomID int64, when time.Time) (key string, lock string) {
	return keys.KeyRoomsFollowed1.Format(roomID),
		keys.LockRoomsFollowed1.Format(roomID, when.Second())
}
