package chatroom

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testHistoryMessageRoomID     = int64(347142109)
	testHistoryMessageRoomOIDHex = "601a69e210529b57177db821"
)

var (
	testHistoryMessageRoom       *room.Room
	testHistoryMessageRoomOID, _ = primitive.ObjectIDFromHex(testHistoryMessageRoomOIDHex)
	testHistoryMessageGift       = livegifts.LiveGift{RoomOID: testHistoryMessageRoomOID, RoomID: testHistoryMessageRoomID, UserID: adminUserID,
		GiftID: 2, GiftNum: 1, Bubble: &bubble.Simple{Type: "noble", NobleLevel: 5}}
	testHistoryMessageMsg = models.Message{
		RoomOID: testHistoryMessageRoomOID, RoomID: testHistoryMessageRoomID, UserID: normalUserID, MsgID: "test-msg-id",
		Bubble: &bubble.Simple{Type: "noble", NobleLevel: 7}, Message: "This is message."}
)

func setRoomOpen() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	collection := service.MongoDB.Collection(room.CollectionName)
	_, err := collection.UpdateOne(ctx, bson.M{"room_id": testHistoryMessageRoomID},
		bson.D{bson.E{Key: "$set",
			Value: bson.M{
				"status.open":      room.StatusOpenTrue,
				"status.open_time": util.TimeToUnixMilli(now.Add(-2 * time.Second)),
			}}})
	if err != nil {
		logger.Fatal(err)
	}
}

func addNoneMessagesData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	// 更新测试礼物
	collection := service.MongoDB.Collection("live_gifts")
	_, err := collection.InsertOne(ctx, bson.M{
		"_room_id":       testHistoryMessageGift.RoomOID,
		"room_id":        testHistoryMessageGift.RoomID,
		"user_id":        testHistoryMessageGift.UserID,
		"lucky_gift_id":  testHistoryMessageGift.GiftID,
		"lucky_gift_num": 1,
		"gift_id":        testHistoryMessageGift.GiftID,
		"gift_num":       testHistoryMessageGift.GiftNum,
		"gift_price":     6, // TODO: 待替换
		"price":          6 * testHistoryMessageGift.GiftNum,
		"bubble":         bson.M{"type": "noble", "noble_level": 5},
		"sent_time":      now.Add(-1800 * time.Millisecond),
		"combo_end_time": now.Add(-1800 * time.Millisecond),
	})
	if err != nil {
		logger.Fatal(err)
	}

	// 更新测试贵族
	collection = service.MongoDB.Collection("user_nobles")
	_, err = collection.InsertOne(ctx, bson.M{
		"from_creator_id": testHistoryMessageRoom.CreatorID,
		"user_id":         testHistoryMessageGift.UserID, // 共用同一个用户
		"name":            testUA,                        // 包名
		"level":           1,
		"price":           1000,
		"is_registration": 1,
		"created_time":    now.Add(-1700 * time.Millisecond),
	})
	if err != nil {
		logger.Fatal(err)
	}
	// 更新测试超粉
	sfOrder := livetxnorder.LiveTxnOrder{
		CreateTime: now.Add(-1000 * time.Millisecond).Unix(),
		BuyerID:    testHistoryMessageGift.UserID,
		SellerID:   testHistoryMessageRoom.CreatorID,
		Status:     livetxnorder.StatusSuccess,
		GoodsType:  livegoods.GoodsTypeSuperFan,
		GoodsID:    1,
		More:       &livetxnorder.MoreInfo{Bubble: testHistoryMessageGift.Bubble},
	}
	err = livetxnorder.LiveTxnOrder{}.DB().Create(&sfOrder).Error
	if err != nil {
		logger.Fatal(err)
	}
}

func addTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 开启房间
	setRoomOpen()

	now := goutil.TimeNow()
	// 更新测试消息
	collection := service.MongoDB.Collection("messages")
	_, err := collection.InsertOne(ctx, bson.M{
		"_room_id":    testHistoryMessageMsg.RoomOID,
		"room_id":     testHistoryMessageMsg.RoomID,
		"msg_id":      testHistoryMessageMsg.MsgID,
		"user_id":     testHistoryMessageMsg.UserID,
		"message":     testHistoryMessageMsg.Message,
		"bubble":      bson.M{"type": "noble", "noble_level": 7},
		"status":      models.MessageStatusNormal,
		"create_time": now.Add(-1900 * time.Millisecond),
		// "type":        models.MessageTypeNormal, // 取消该注释也能测试通过，测试时间：2020-02-14
	})
	if err != nil {
		logger.Fatal(err)
	}
	_, err = collection.InsertOne(ctx, bson.M{
		"_room_id":    testHistoryMessageMsg.RoomOID,
		"room_id":     testHistoryMessageMsg.RoomID,
		"msg_id":      testHistoryMessageMsg.MsgID + "_test2",
		"user_id":     testHistoryMessageMsg.UserID,
		"message":     testHistoryMessageMsg.Message,
		"status":      models.MessageStatusFaked,
		"create_time": now.Add(-1900 * time.Millisecond),
	})
	if err != nil {
		logger.Fatal(err)
	}

	// 更新测试数据
	addNoneMessagesData()
}

func setRoomClose(t time.Time) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	collection := service.MongoDB.Collection("rooms")
	_, err := collection.UpdateOne(ctx, bson.M{"room_id": testHistoryMessageRoomID},
		bson.M{"$set": bson.M{
			"status.open":       room.StatusOpenFalse,
			"status.close_time": util.TimeToUnixMilli(t),
		}})
	if err != nil {
		panic(err)
	}
}

func deleteTestData() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 关闭房间
	setRoomClose(goutil.TimeNow())

	// 删除测试数据
	collection := service.MongoDB.Collection("messages")
	_, _ = collection.DeleteMany(ctx, bson.M{"msg_id": bson.M{"$in": bson.A{testHistoryMessageMsg.MsgID, testHistoryMessageMsg.MsgID + "_test2"}}})
	collection = service.MongoDB.Collection("live_gifts")
	_, _ = collection.DeleteMany(ctx, bson.M{"room_id": testHistoryMessageGift.RoomID,
		"bubble": bson.M{"$exists": 1}})
	collection = service.MongoDB.Collection("user_nobles")
	_, _ = collection.DeleteMany(ctx, bson.M{"name": testUA, "from_creator_id": testHistoryMessageRoom.CreatorID})
	_ = livetxnorder.LiveTxnOrder{}.DB().Delete("", "buyer_id = ? AND seller_id = ?", testHistoryMessageGift.UserID, testHistoryMessageRoom.CreatorID)
}

func TestActionHistoryMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	setRoomOpen()
	addNoneMessagesData()
	ctx := handler.NewTestContext(http.MethodGet, fmt.Sprintf("?room_id=%d", testHistoryMessageRoomID), false, nil)
	ctx.Equip().OS = goutil.IOS
	ctx.Equip().AppVersion = "4.4.1"
	r, err := ActionHistoryMessage(ctx)
	require.Nil(err)
	resp := r.(*historyMessageResp)
	require.Len(resp.History, 3)
	deleteTestData()
	setRoomClose(goutil.TimeNow())

	addTestData()
	defer deleteTestData()
	ctx = handler.NewTestContext(http.MethodGet, fmt.Sprintf("?room_id=%d", testHistoryMessageRoomID), false, nil)
	ctx.Equip().OS = goutil.IOS
	ctx.Equip().AppVersion = "4.4.1"
	r, err = ActionHistoryMessage(ctx)
	require.Nil(err)

	resp = r.(*historyMessageResp)

	// check history
	checkedTimes := 0
	require.Len(resp.History, 4)
	for i := range resp.History {
		history := resp.History[i]
		require.NotNil(history.User)
		assert.True(history.User.UID == adminUserID || history.User.UID == normalUserID)
		if resp.History[i].Type == msgTypeMsg {
			assert.Equal(testHistoryMessageMsg.Message, history.Message)
			assert.Equal(testHistoryMessageMsg.Bubble, history.Bubble)
			assert.NotNil(history.Bubble)
			checkedTimes++
		}
		if history.Type == msgTypeGift {
			require.NotEmpty(history.Gift)
			assert.Equal(testHistoryMessageGift.GiftID, history.Gift.GiftID)
			assert.Equal(testHistoryMessageGift.GiftNum, history.LuckyGiftNum)
			assert.Equal(testHistoryMessageGift.Bubble, history.Bubble)
			assert.NotNil(history.Bubble)
			checkedTimes++
		}
		if history.Type == msgTypeNoble {
			assert.Equal(testUA, history.Name)
			checkedTimes++
		}
		if history.Type == msgTypeSuperFan {
			assert.Equal(testHistoryMessageGift.Bubble, history.Bubble)
			checkedTimes++
		}
	}
	assert.Equal(4, checkedTimes)

	ctx = handler.NewTestContext(http.MethodGet, fmt.Sprintf("?room_id=%d", testHistoryMessageRoomID), true, nil)
	ctx.Equip().OS = goutil.IOS
	ctx.Equip().AppVersion = "4.4.1"
	ctx.User().ID = normalUserID
	r, err = ActionHistoryMessage(ctx)
	require.Nil(err)

	resp = r.(*historyMessageResp)

	// check history
	checkedTimes = 0
	require.Len(resp.History, 5)
	for i := range resp.History {
		history := resp.History[i]
		require.NotNil(history.User)
		assert.True(history.User.UID == adminUserID || history.User.UID == normalUserID)
		if resp.History[i].Type == msgTypeMsg {
			assert.Equal(testHistoryMessageMsg.Message, history.Message)
			checkedTimes++
		}
	}
	assert.Equal(2, checkedTimes)

	// 查询不到礼物的情况
	goutil.SetTimeNow(func() time.Time { return time.Unix(0, 0) })
	defer goutil.SetTimeNow(nil)
	c := handler.NewTestContext("GET", fmt.Sprintf("?room_id=%d", testHistoryMessageRoomID), false, nil)
	r, err = ActionHistoryMessage(c)
	require.NoError(err)
	tutil.PrintJSON(r)
	resp = r.(*historyMessageResp)
	for i := range resp.History {
		history := resp.History[i]
		if history.Type == msgTypeGift {
			assert.Nil(history.Gift)
		}
	}

	goutil.SetTimeNow(nil)
	setRoomClose(goutil.TimeNow())
	c = handler.NewTestContext("GET", fmt.Sprintf("?room_id=%d", testHistoryMessageRoomID), false, nil)
	r, err = ActionHistoryMessage(c)
	require.NoError(err)
	resp = r.(*historyMessageResp)
	assert.Equal(0, len(resp.History))

	setRoomClose(goutil.TimeNow().Add(-2 * time.Second))
	c = handler.NewTestContext("GET", fmt.Sprintf("?room_id=%d", testHistoryMessageRoomID), false, nil)
	r, err = ActionHistoryMessage(c)
	require.NoError(err)
	resp, ok := r.(*historyMessageResp)
	require.True(ok)
	// 关播直播间不返回历史消息
	assert.Empty(resp.History)
}

func TestHistoryMessageResp_findMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// del history sticker message
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := service.MongoDB.Collection("messages").DeleteMany(ctx, bson.M{"sticker": bson.M{"$exists": true}})
	require.NoError(err)
	testRoomOID := primitive.NewObjectID()
	messages := []interface{}{
		models.Message{
			RoomOID: testRoomOID,
			RoomID:  223344,
			UserID:  223344,
			MsgID:   "3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
			Status:  models.MessageStatusNormal,
			Sticker: &models.Sticker{
				PackageID: 1,
				StickerID: 1,
			},
			CreateTime: goutil.TimeNow(),
		},
		models.Message{
			RoomOID:    testRoomOID,
			RoomID:     223344,
			UserID:     223344,
			MsgID:      "2bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
			Message:    "test",
			Status:     models.MessageStatusNormal,
			CreateTime: goutil.TimeNow(),
		},
	}
	_, err = service.MongoDB.Collection("messages").InsertMany(ctx, messages)
	require.NoError(err)

	// 查询文字消息和表情消息
	resp := &historyMessageResp{
		room: &room.Room{
			OID: testRoomOID,
		},
		c: handler.NewTestContext(http.MethodGet, "", false, nil),
	}
	resp.c.Equip().FromApp = true
	resp.c.Equip().OS = goutil.IOS
	resp.c.Equip().AppVersion = "4.9.8"
	err = resp.findMessage()
	require.NoError(err)
	require.Len(resp.messages, 2)
	require.NotEmpty(resp.packageMap)
	require.NotEmpty(resp.stickerMap)
	assert.NotNil(resp.packageMap[messages[0].(models.Message).Sticker.PackageID])
	assert.NotNil(resp.stickerMap[messages[0].(models.Message).Sticker.StickerID])

	// 查询直播预览页消息
	resp = &historyMessageResp{
		room: &room.Room{
			OID: testRoomOID,
		},
		c:         handler.NewTestContext(http.MethodGet, "", false, nil),
		isPreview: true,
	}
	err = resp.findMessage()
	require.NoError(err)
	require.Len(resp.messages, 1)
	assert.Equal("test", resp.messages[0].Message)

	// 仅查询文字消息
	resp = &historyMessageResp{
		room: &room.Room{
			OID: testRoomOID,
		},
		c: handler.NewTestContext(http.MethodGet, "", false, nil),
	}
	resp.c.Equip().FromApp = true
	resp.c.Equip().OS = goutil.IOS
	resp.c.Equip().AppVersion = "4.9.7"
	err = resp.findMessage()
	require.NoError(err)
	require.Len(resp.messages, 1)
	assert.Equal("test", resp.messages[0].Message)
}

func TestHistoryMessageTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(historyMessage{}, "type", "user", "bubble", "create_time",
		"msg_id", "message",
		"gift", "gift_num", "lucky_gift", "lucky_gift_num", "message_prefix",
		"name", "level",
		"num", "price", "icon_url",
		"is_registered", "sticker")
}

func TestHistoryMessageSortHistory(t *testing.T) {
	assert := assert.New(t)

	current := goutil.TimeNow()
	now := goutil.NewTimeUnixMilli(current)
	add1s := goutil.NewTimeUnixMilli(current.Add(time.Second))
	histories := []*historyMessage{
		{CreateTime: now},
		{CreateTime: add1s},
	}

	r := historyMessageResp{History: histories}
	r.sortHistory()
	assert.Equal([]*historyMessage{histories[0], histories[1]}, r.History)
}

func TestBuildResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var resp historyMessageResp
	resp.room = &room.Room{
		Helper: room.Helper{
			RoomID: testHistoryMessageMsg.RoomID,
		},
	}
	pastTime := goutil.TimeNow().Add(21 * -time.Second)
	resp.messages = make([]*models.Message, 21)
	for i := range resp.messages {
		resp.messages[i] = &models.Message{
			Message:    fmt.Sprintf("test_%d", i+1),
			CreateTime: pastTime.Add(time.Duration(i) * time.Second),
			UserID:     12,
		}
	}
	resp.messageLength = maxMessageLength

	err := resp.buildResp()
	require.NoError(err)
	require.Len(resp.History, 20)
	assert.Equal("test_2", resp.History[0].Message)
	assert.Equal("test_21", resp.History[len(resp.History)-1].Message)
}
