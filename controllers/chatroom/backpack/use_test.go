package backpack

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/backpackitem"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestActionBackpackUse(t *testing.T) {
	require := require.New(t)

	var (
		testUserID = int64(3387502)
		testItemID = int64(1)
	)
	cancel := mrpc.SetMock(vip.URLBuyLiveTrialNoble, func(any) (any, error) {
		return handler.M{
			"vip_info": &vip.Info{VipID: 9, Level: 4},
		}, nil
	})
	defer cancel()

	vips := []vip.Info{
		{
			VipID: 1,
			Type:  vip.TypeLiveNoble,
			Level: 1,
		},
		{
			VipID: 12,
			Type:  vip.TypeLiveTrialNoble,
			Level: 4,
		},
	}
	service.Cache5Min.Set(keys.KeyVipList1.Format(vip.TypeLiveTrialNoble), vips, cache.DefaultExpiration)
	defer service.Cache5Min.Flush()
	uvJSON, err := json.Marshal(map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {VipID: 1, Type: vip.TypeLiveNoble, Level: 1},
		vip.TypeLiveTrialNoble: {VipID: 12, Type: vip.TypeLiveNoble, Level: 4},
	})
	require.NoError(err)
	err = service.Redis.Set(keys.KeyNobleUserVips1.Format(testUserID), uvJSON, time.Hour).Err()
	require.NoError(err)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = useritems.Collection().InsertOne(ctx, useritems.UserItem{
		Type:      useritems.TypeItem,
		UserID:    testUserID,
		ItemID:    testItemID,
		Num:       3,
		StartTime: goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		EndTime:   goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/use", true, handler.M{
		"room_id":  223344,
		"item_id":  testItemID,
		"item_num": 2,
	})
	c.User().ID = testUserID
	resp, err := ActionBackpackUse(c)
	require.NoError(err)
	require.NotNil(resp)
}

func TestActionBackpackUseLiveMedal(t *testing.T) {
	require := require.New(t)

	var (
		testUserID = int64(3387502)
		testItemID = int64(2)
		testRoomID = int64(223344)
	)

	// mock uservip
	uvJSON, err := json.Marshal(map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Type: vip.TypeLiveNoble, Level: 3},
		vip.TypeLiveTrialNoble: {Type: vip.TypeLiveNoble, Level: 4},
	})
	require.NoError(err)
	err = service.Redis.Set(keys.KeyNobleUserVips1.Format(testUserID), uvJSON, time.Hour).Err()
	require.NoError(err)

	// mock 用户背包道具
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = useritems.Collection().InsertOne(ctx, useritems.UserItem{
		Type:      useritems.TypeItem,
		UserID:    testUserID,
		ItemID:    testItemID,
		Num:       3,
		StartTime: goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		EndTime:   goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	})
	require.NoError(err)

	// 确保直播间开通了粉丝勋章
	_, err = room.Update(testRoomID, bson.M{
		"medal": room.Medal{
			Name:      "test",
			NameClean: "test",
		}})
	require.NoError(err)
	// 清空用户已获得的粉丝勋章
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/use", true, handler.M{
		"room_id":  223344,
		"item_id":  testItemID,
		"item_num": 1,
	})
	c.User().ID = testUserID
	resp, err := ActionBackpackUse(c)
	require.NoError(err)
	require.NotNil(resp)
}

func TestNewBackpackUseParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/use", true, handler.M{
		"room_id":  1,
		"item_id":  1,
		"item_num": 1,
	})
	param, err := newBackpackUseParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(int64(1), param.RoomID)

	c = handler.NewTestContext(http.MethodPost, "/use", true, handler.M{
		"room_id":  1,
		"item_num": 1,
	})
	_, err = newBackpackUseParam(c)
	assert.ErrorIs(err, actionerrors.ErrParams)
}

func TestBackpackUseParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID         = int64(3387502)
		testItemID         = int64(1)
		testNotFoundItemID = int64(-999)
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 8, 31, 11, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	cancel := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()
	vips := []vip.Info{
		{
			VipID: 12,
			Type:  vip.TypeLiveTrialNoble,
			Level: 4,
		},
	}
	service.Cache5Min.Set(keys.KeyVipList1.Format(vip.TypeLiveTrialNoble), vips, cache.DefaultExpiration)
	defer service.Cache5Min.Flush()
	uvJSON, err := json.Marshal(map[int]*vip.UserVip{
		vip.TypeLiveNoble:      {Type: vip.TypeLiveNoble, Level: 3},
		vip.TypeLiveTrialNoble: {Type: vip.TypeLiveNoble, Level: 4},
	})
	require.NoError(err)
	err = service.Redis.Set(keys.KeyNobleUserVips1.Format(testUserID), uvJSON, time.Hour).Err()
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = useritems.Collection().InsertOne(ctx, useritems.UserItem{
		Type:      useritems.TypeItem,
		UserID:    testUserID,
		ItemID:    testItemID,
		Num:       10,
		StartTime: goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		EndTime:   goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/use", true, nil)
	c.User().ID = testUserID
	param := &backpackUseParam{
		RoomID:  223344,
		ItemID:  testNotFoundItemID,
		ItemNum: 4,
		c:       c,
	}
	err = param.check()
	require.EqualError(err, "未查询到该道具")

	c = handler.NewTestContext(http.MethodPost, "/use", true, nil)
	c.User().ID = testUserID
	param = &backpackUseParam{
		RoomID:  223344,
		ItemID:  testItemID,
		ItemNum: 4,
		c:       c,
	}
	err = param.check()
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/use", true, nil)
	c.User().ID = testUserID
	param = &backpackUseParam{
		RoomID:  223344,
		ItemID:  testItemID,
		ItemNum: 11,
		c:       c,
	}
	err = param.check()
	assert.EqualError(err, "可用数量不足！")
}

func TestBackpackUseParam_checkLiveMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	type tc struct {
		name               string
		mockRoomMedalIsNil bool // mock 直播间没有开通粉丝勋章
		mockOwnedMedal     bool // mock 已经用有该粉丝勋章
		mockOwnedMedalNum  int  // mock 已经拥有的勋章数量
		mockUserNoble      bool // mock 用户贵族
		assertErr          error
	}

	cases := []tc{
		{
			name:               "case1",
			mockRoomMedalIsNil: true,
			assertErr:          actionerrors.ErrGlobalPopupPromptMsg("当前主播未开启粉丝勋章功能，请提醒主播开启或选择其他主播赠送哦~"),
		},
		{
			name:           "case2",
			mockOwnedMedal: true,
			assertErr:      actionerrors.ErrGlobalPopupPromptMsg("您已拥有该主播的粉丝勋章哦，将心意传递给下一位主播吧~"),
		},
		{
			// 普通用户 - 已有勋章满 30 不通过
			name:              "case3",
			mockOwnedMedalNum: 30,
			assertErr:         actionerrors.ErrGlobalPopupPromptMsg("粉丝勋章已满，暂无法使用"),
		},
		{
			// 普通用户 - 已有勋章不满 30 通过
			name:               "case4",
			mockRoomMedalIsNil: false,
			mockOwnedMedalNum:  1,
			assertErr:          nil,
		},
		{
			// 贵族用户 - 已有勋章满 40 不通过
			name:              "case5",
			mockOwnedMedalNum: 40,
			mockUserNoble:     true,
			assertErr:         actionerrors.ErrGlobalPopupPromptMsg("粉丝勋章已满，暂无法使用"),
		},
		{
			// 贵族用户 - 已有勋章不满 40  通过
			name:              "case6",
			mockOwnedMedalNum: 30,
			mockUserNoble:     true,
			assertErr:         nil,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// mock 贵族身份
	mockUserVip := func(uid int64, ty int, info *vip.UserVip) {
		// mock uservip
		mp := map[int]*vip.UserVip{}
		if ty != 0 {
			mp = map[int]*vip.UserVip{
				ty: info,
			}
		}
		uvJSON, err := json.Marshal(mp)
		require.NoError(err)
		err = service.Redis.Set(keys.KeyNobleUserVips1.Format(uid), uvJSON, time.Hour).Err()
		require.NoError(err)
	}

	for _, tc := range cases {
		t.Run(t.Name(), func(t *testing.T) {
			var (
				testUserID = int64(12)
				testRoomID = int64(999)
			)
			param := &backpackUseParam{
				r: &room.Room{},
				user: &liveuser.Simple{
					UID: testUserID,
				},
				c: handler.NewTestContext(http.MethodPost, "/use", true, nil),
			}
			param.r.RoomID = testRoomID
			param.r.Medal = &room.Medal{Name: "testUserID"}
			if tc.mockRoomMedalIsNil {
				param.r.Medal = nil
			}

			// 清空用户已有的粉丝勋章
			_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
			require.NoError(err)

			if tc.mockUserNoble {
				// mock 贵族身份对应的 vip 信息
				// 设置贵族身份粉丝勋章数量限制 40
				var (
					testVipID    = int64(1)
					testVipLevel = int(3)
					testVipType  = vip.TypeLiveNoble
				)
				vips := map[int]vip.Info{
					testVipLevel: {
						VipID:    testVipID,
						Type:     testVipType,
						Level:    testVipLevel,
						MedalNum: 40,
					},
				}
				service.Cache5Min.Set(keys.KeyMapVipLevelInfos1.Format(vip.TypeLiveNoble), vips, cache.DefaultExpiration)
				defer service.Cache5Min.Flush()

				noble := &vip.UserVip{
					VipID:      testVipID,
					UserID:     testUserID,
					ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
					Level:      testVipLevel,
				}
				mockUserVip(testUserID, testVipType, noble)
			} else {
				mockUserVip(testUserID, 0, nil)
			}

			if tc.mockOwnedMedal {
				medal := livemedal.LiveMedal{
					Simple: livemedal.Simple{RoomID: testRoomID, CreatorID: 11, UserID: testUserID, Status: livemedal.StatusOwned},
				}
				_, err := livemedal.Collection().InsertOne(ctx, medal)
				require.NoError(err)
			}

			if tc.mockOwnedMedalNum > 0 {
				medals := make([]any, 0, tc.mockOwnedMedalNum)
				for i := 0; i < tc.mockOwnedMedalNum; i++ {
					medal := livemedal.LiveMedal{
						Simple: livemedal.Simple{RoomID: int64(i), CreatorID: int64(i), UserID: testUserID, Status: livemedal.StatusOwned},
					}
					medals = append(medals, medal)
				}
				_, err := livemedal.Collection().InsertMany(ctx, medals)
				require.NoError(err)
			}

			err = param.checkLiveMedal()
			assert.Equal(tc.assertErr, err, tc.name)
		})
	}
}

func TestBackpackUseParam_checkNoble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID  = int64(3387502)
		testVipID11 = int64(11)
		testVipID13 = int64(13)
	)
	uvMapJSON, err := json.Marshal(map[int]*vip.UserVip{
		vip.TypeLiveNoble: {
			Type:       vip.TypeLiveNoble,
			Level:      4,
			UserID:     testUserID,
			ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
		},
	})
	require.NoError(err)
	err = service.Redis.Set(keys.KeyNobleUserVips1.Format(testUserID), uvMapJSON, time.Second).Err()
	require.NoError(err)
	vips := []vip.Info{
		{
			VipID: testVipID11,
			Type:  vip.TypeLiveTrialNoble,
			Level: 3,
		},
		{
			VipID: testVipID13,
			Type:  vip.TypeLiveTrialNoble,
			Level: 5,
		},
	}
	service.Cache5Min.Set(keys.KeyVipList1.Format(vip.TypeLiveTrialNoble), vips, cache.DefaultExpiration)
	defer service.Cache5Min.Flush()

	param := &backpackUseParam{
		user: &liveuser.Simple{
			UID: testUserID,
			UserVipMap: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					Type:       vip.TypeLiveNoble,
					Level:      4,
					UserID:     testUserID,
					ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
				},
			},
		},
		backpackItem: &backpackitem.LiveBackpackItem{
			MoreInfo: &backpackitem.More{
				VipID: testVipID13,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/use", true, nil),
	}
	err = param.checkNoble()
	require.NoError(err)

	param = &backpackUseParam{
		user: &liveuser.Simple{
			UID: testUserID,
			UserVipMap: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					Type:       vip.TypeLiveNoble,
					Level:      4,
					UserID:     testUserID,
					ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
				},
			},
		},
		backpackItem: &backpackitem.LiveBackpackItem{
			MoreInfo: &backpackitem.More{
				VipID: testVipID11,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/use", true, nil),
	}
	err = param.checkNoble()
	assert.EqualError(err, "无法使用低于当前贵族等级的体验卡！")
}

func TestBackpackUseParam_use(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1321123)
		testItemID = int64(1)
	)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 8, 31, 11, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	cleanup := mrpc.SetMock(vip.URLBuyLiveTrialNoble, func(any) (any, error) {
		return handler.M{
			"vip_info": &vip.Info{VipID: 9, Level: 1},
		}, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := useritems.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = useritems.Collection().InsertOne(ctx, useritems.UserItem{
		Type:      useritems.TypeItem,
		UserID:    testUserID,
		ItemID:    testItemID,
		Num:       3,
		StartTime: goutil.TimeNow().AddDate(0, 0, -1).Unix(),
		EndTime:   goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	})
	require.NoError(err)

	key := keys.LockUseBackpackItem1.Format(testUserID)
	require.NoError(service.Redis.Del(key).Err())
	param := &backpackUseParam{
		ItemNum: 2,
		ItemID:  testItemID,
		c:       handler.NewTestContext(http.MethodPost, "/use", true, nil),
		r:       new(room.Room),
		user:    &liveuser.Simple{UID: testUserID},
		backpackItem: &backpackitem.LiveBackpackItem{
			Type:     backpackitem.TypeNobleTrialCard,
			MoreInfo: &backpackitem.More{Duration: int64(24 * time.Hour.Seconds())}},
		nobleTrialCardInfo: &vip.Info{VipID: 9, Level: 1},
	}
	resp, err := param.use()
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(1, resp.Remain)
}

func TestBackpackUseParam_refreshUserTitlesAndAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID int64 = ************
	)
	findUserAppearances := func(userID int64, status int) ([]userappearance.UserAppearance, error) {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		opts := options.Find().
			SetSort(bson.M{"appearance_id": 1})
		cur, err := userappearance.Collection().Find(ctx, bson.M{
			"user_id": userID,
			"from":    appearance.FromNoble,
			"status":  status,
			"type": bson.M{
				"$in": []int{appearance.TypeAvatarFrame, appearance.TypeCardFrame, appearance.TypeMessageBubble},
			},
		}, opts)
		if err != nil {
			return nil, err
		}
		var userAppearances []userappearance.UserAppearance
		err = cur.All(ctx, &userAppearances)
		if err != nil {
			return nil, err
		}
		return userAppearances, nil
	}

	mLevel := map[int]vip.Info{
		vip.NobleLevel4: {
			ID:    12,
			Type:  vip.TypeLiveTrialNoble,
			Level: vip.NobleLevel4,
			Title: "大咖",
		},
		vip.NobleLevel3: {
			ID:    3,
			Type:  vip.TypeLiveNoble,
			Level: vip.NobleLevel3,
			Title: "偶像",
		},
	}
	service.Cache5Min.Set(keys.KeyMapVipLevelInfos1.Format(vip.TypeLiveTrialNoble), mLevel, cache.DefaultExpiration)

	uvt := &vip.UserVip{
		VipID:      12,
		UserID:     testUserID,
		Type:       vip.TypeLiveTrialNoble,
		Level:      4,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	uv := &vip.UserVip{
		VipID:      3,
		UserID:     testUserID,
		Type:       vip.TypeLiveNoble,
		Level:      3,
		ExpireTime: goutil.TimeNow().AddDate(1, 0, 1).Unix(),
	}
	cleanup := mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble:      uv,
				vip.TypeLiveTrialNoble: uvt,
			},
		}, nil
	})
	defer cleanup()

	// 检查穿戴高等级的外观
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	assert.NoError(err)
	appearances, err := appearance.AllNobleAppearances(uv.Level)
	require.NoError(err)
	require.Equal(2, len(appearances))
	uas := make([]interface{}, 0, len(appearances))
	for i := range appearances {
		item := userappearance.NewUserAppearance(testUserID, appearances[i])
		item.SetStatus(userappearance.StatusWorn, goutil.TimeNow().AddDate(1, 0, 0).Unix(), 0)
		uas = append(uas, item)
	}
	_, err = userappearance.Collection().InsertMany(ctx, uas)
	require.NoError(err)

	param := &backpackUseParam{
		user: &liveuser.Simple{
			UID: testUserID,
			UserVipMap: map[int]*vip.UserVip{
				vip.TypeLiveNoble:      uv,
				vip.TypeLiveTrialNoble: uvt,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/use", true, nil),
	}
	param.refreshUserTitlesAndAppearances(uvt)
	assert.NotEmpty(param.user.Titles)
	userAppearances, err := findUserAppearances(testUserID, userappearance.StatusWorn)
	require.NoError(err)
	require.Equal(3, len(userAppearances))
	for _, userAppearance := range userAppearances {
		require.NotNil(userAppearance.ExpireTime)
		assert.Equal(uvt.ExpireTime, *userAppearance.ExpireTime)
	}

	uvt = &vip.UserVip{
		VipID:      12,
		UserID:     testUserID,
		Type:       vip.TypeLiveTrialNoble,
		Level:      4,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	uv = &vip.UserVip{
		VipID:      4,
		UserID:     testUserID,
		Type:       vip.TypeLiveNoble,
		Level:      4,
		ExpireTime: goutil.TimeNow().AddDate(1, 0, 2).Unix(),
	}
	mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble:      uv,
				vip.TypeLiveTrialNoble: uvt,
			},
		}, nil
	})
	// 检查穿戴同等级外观
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	assert.NoError(err)
	appearances, err = appearance.AllNobleAppearances(uv.Level)
	require.NoError(err)
	require.Equal(3, len(appearances))
	uas = make([]interface{}, 0, len(appearances))
	for i := range appearances {
		item := userappearance.NewUserAppearance(testUserID, appearances[i])
		item.SetStatus(userappearance.StatusWorn, goutil.TimeNow().AddDate(1, 0, 0).Unix(), 0)
		uas = append(uas, item)
	}
	_, err = userappearance.Collection().InsertMany(ctx, uas)
	require.NoError(err)

	param = &backpackUseParam{
		user: &liveuser.Simple{
			UID: testUserID,
			UserVipMap: map[int]*vip.UserVip{
				vip.TypeLiveNoble:      uv,
				vip.TypeLiveTrialNoble: uvt,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/use", true, nil),
	}
	param.refreshUserTitlesAndAppearances(uvt)
	assert.NotEmpty(param.user.Titles)
	userAppearances, err = findUserAppearances(testUserID, userappearance.StatusWorn)
	require.NoError(err)
	require.Equal(3, len(userAppearances))
	for _, userAppearance := range userAppearances {
		require.NotNil(userAppearance.ExpireTime)
		assert.Equal(uv.ExpireTime, *userAppearance.ExpireTime) // 这里续期所以使用的是更新后的贵族过期时间
	}
}
