package chatroom

import (
	"encoding/json"
	"fmt"
	"html"
	"net/http"
	"slices"
	"strings"

	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type messageHornParam struct {
	RoomID  int64  `form:"room_id" json:"room_id"`
	Message string `form:"message" json:"message"`
	Type    int    `form:"type" json:"type"`

	c    *handler.Context
	user *liveuser.Simple

	room             *room.Room
	hornNum          int64
	nobleHornNum     int64
	blackCardHornNum int64

	horn *notifymessages.Horn // 单元测试用

	hornBubbleParams *params.HornBubble
	uv               *vip.UserVip
	userBlackCard    *liveuserblackcard.UserBlackCardInfo
	bubble           *bubble.Simple
	roomMedal        *livemedal.LiveMedal // 当前房间的粉丝勋章
	isRoomAdmin      bool
}

// ActionMessageHorn 发送全站喇叭消息
/**
 * @api {post} /api/v2/chatroom/message/horn 发送全站喇叭消息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} message 消息
 * @apiParam {number=1,2} type 类型，1：贵族喇叭，2：黑卡喇叭。支持黑卡前的版本不传此参数，默认贵族喇叭；支持黑卡的版本必传
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "horn_num": 9 // 发送成功后的全站喇叭数量，不支持黑卡喇叭的版本返回贵族喇叭数量
 *       }
 *     }
 *
 * @apiSuccessExample 喇叭消息（贵族气泡）:
 *     {
 *       "type": "notify",
 *       "notify_type": "message",
 *       "event": "horn",
 *       "room_id": 123456,
 *       "message": "用户昵称：消息（主播昵称的直播间）",
 *       "notify_bubble": {
 *         "type": "noble", // 气泡类型，目前支持: 贵族气泡 noble 下发气泡 custom
 *         "noble_level": 2 // 使用对应等级的贵族气泡
 *       }
 *     }
 *
 * @apiSuccessExample 喇叭消息（下发气泡）:
 *     {
 *       "type": "notify",
 *       "notify_type": "message",
 *       "event": "horn",
 *       "room_id": 123456,
 *       "message": "用户昵称：消息（主播昵称的直播间）",
 *       "notify_bubble": {
 *         "compat_type": "highness", // 兼容类型，highness: 兼容 iOS < 6.0.9 和 Android < 6.0.9 上神不支持 shine 控制闪光，下发 compat_type 为 highness 时让客户端使用本地的上神飘屏样式
 *         "type": "custom", // 气泡类型，目前支持: 贵族气泡 noble 下发气泡 custom
 *         "image_url": "https://example.com/b001_0_10_0_100.png", // 客户端使用
 *         "effect_image_url": "https://example.com/b001_0_10_0_100.png", // 特效图片
 *         "shine": 0, // 是否闪光，0 为不闪光，1 为闪光，字段不存在则默认不闪光
 *         "float": 1 // 不存在或为 0: 正常飘屏气泡；1: 悬停气泡
 *       }
 *     }
 *
 * @apiError (500) {number} code 500020026
 * @apiError (500) {string} info 喇叭内容含有违规信息
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 *
 * @apiError (403) {number} code 500020025
 * @apiError (403) {string} info 当前用户被全站禁言中
 *
 */
func ActionMessageHorn(c *handler.Context) (handler.ActionResponse, error) {
	if !config.Conf.Web.HornOpen {
		return nil, actionerrors.NewUnknownError(http.StatusForbidden, "全站喇叭暂未开放，请稍作等待哦~")
	}

	param, err := newMessageHornParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.decHorn()
	if err != nil {
		return nil, err
	}
	err = param.send()
	if err != nil {
		return nil, err
	}
	hornNum := param.hornNum
	if c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.3.8", Android: "6.3.8"}) {
		// WORKAROUND: 安卓、iOS <= 6.3.8 版本只支持贵族喇叭，只返回贵族喇叭数量
		hornNum = param.nobleHornNum
	}
	return handler.M{"horn_num": hornNum}, nil
}

func newMessageHornParam(c *handler.Context) (*messageHornParam, error) {
	var param messageHornParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.3.8", Android: "6.3.8"}) {
		// WORKAROUND: 兼容安卓、iOS <= 6.3.8 版本，旧版本不支持 type 参数，默认发送贵族喇叭
		param.Type = userstatus.HornTypeNoble
	}
	if param.RoomID <= 0 || util.UTF8Width(param.Message)/2 > 20 || !slices.Contains([]int{userstatus.HornTypeNoble, userstatus.HornTypeBlackCard}, param.Type) {
		return nil, actionerrors.ErrParams
	}
	cfg, err := params.FindGlobal()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if cfg.IsUnderMaintenance() {
		errMsg := cfg.Maintain.MaintainMsg()
		if param.Type == userstatus.HornTypeNoble {
			errMsg = fmt.Sprintf("%s。%s", errMsg, "大咖及以上贵族用户已延长 3 天贵族有效期，新开贵族和其他问题请联系客服")
		}
		return nil, actionerrors.ErrGlobalPopupPromptMsg(errMsg)
	}
	param.c = c

	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil || param.room.Limit != nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.room.IsBan() {
		return nil, actionerrors.ErrBannedRoom
	}
	if param.room.Status.Open == room.StatusOpenFalse {
		return nil, actionerrors.ErrClosedRoomAlt
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": c.UserID()},
		&liveuser.FindOptions{FindTitles: false, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return nil, actionerrors.ErrUserNotFound
	}

	hornMute, err := livemembers.FindHornMute(c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if hornMute != nil {
		return nil, actionerrors.NewErrForbidden("全站喇叭已被封禁，暂时无法使用")
	}

	param.roomMedal, err = livemedal.FindOwnedMedal(c.UserID(), param.RoomID,
		livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.isRoomAdmin, err = livemembers.IsRoomAdmin(param.room.OID, c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return &param, nil
}

func (param *messageHornParam) check() error {
	exists, err := blocklist.Exists(param.user.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return actionerrors.ErrBannedUser
	}
	// 检查是否被禁言
	// 在直播间禁言期间, 无法通过本直播间发送全站喇叭, 但可通过其他直播间发送全站喇叭
	mute, err := livemembers.IsMute(param.user.UserID(), param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if mute.GlobalMute {
		return actionerrors.ErrGlobalMuteUser
	}
	if mute.RoomMute {
		return actionerrors.NewErrForbidden("您已被禁言，无法在本直播间发送全站喇叭")
	}

	if banned, err := userstatus.IsBanned(param.user.UserID()); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	} else if banned {
		return actionerrors.ErrBannedUser
	}
	isBlackCardType := param.Type == userstatus.HornTypeBlackCard
	if isBlackCardType {
		userBlackCardInfo, err := liveuserblackcard.FindUserActiveBlackCard(param.user.UserID())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if userBlackCardInfo == nil || !liveuserblackcard.HaveBlackCardPrivilege(userBlackCardInfo, liveuserblackcard.PrivilegeBlackCardHorn) {
			return actionerrors.ErrNoAuthority
		}
		param.userBlackCard = userBlackCardInfo
	} else {
		// 检查贵族身份
		uv, err := vip.UserActivatedVip(param.user.UserID(), false, param.c)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if uv == nil || uv.Info == nil || uv.Info.HornNum == 0 {
			return actionerrors.ErrNoAuthority
		}
		param.uv = uv
	}
	if param.Type == userstatus.HornTypeNoble {
		param.newNobleBubble()
	} else {
		param.newBlackCardBubble()
	}
	if param.bubble == nil {
		return actionerrors.ErrCannotFindResource
	}

	nobleHornNum, blackCardHornNum := userstatus.HornNum(param.user.UserID())
	hornNum := nobleHornNum
	if isBlackCardType {
		hornNum = blackCardHornNum
	}
	if hornNum <= 0 {
		return actionerrors.NewErrForbidden("全站喇叭已用完")
	}

	limit := utils.SpeakLimit{
		User:        param.user,
		Room:        param.room,
		IsRoomAdmin: param.isRoomAdmin,
		RoomMedal:   param.roomMedal,
	}
	err = limit.Check()
	if err != nil {
		return err
	}

	// 文本检查
	err = param.checkText()
	if err != nil {
		return err
	}

	return nil
}

func (param *messageHornParam) newNobleBubble() {
	var bubbleID int64
	custom, err := livecustom.FindUserCustomNobleHornBubble(param.uv.UserID, goutil.TimeNow())
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id": param.uv.UserID,
		}).Error(err)
		// PASS
	}
	if custom != nil {
		err = json.Unmarshal([]byte(custom.More), &param.hornBubbleParams)
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":   param.uv.UserID,
				"custom_id": custom.ID,
			}).Error(err)
			// PASS
		} else {
			bubbleID = param.hornBubbleParams.UserVipNotifyBubbleID(param.uv.Type, param.uv.Level)
		}
	}
	// 若定制气泡中未查询到对应贵族气泡 ID, 则查询默认气泡配置
	if bubbleID == 0 {
		bp, err := params.FindBubble()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		param.hornBubbleParams = bp.FindHornBubble(goutil.TimeNow())
		bubbleID = param.hornBubbleParams.UserVipNotifyBubbleID(param.uv.Type, param.uv.Level)
	}
	if bubbleID == 0 {
		logger.WithFields(logger.Fields{
			"user_id": param.uv.UserID,
			"type":    param.uv.Type,
			"level":   param.uv.Level,
		}).Error("对应贵族气泡 ID 不存在")
		// PASS
		return
	}

	switch param.uv.Type {
	case vip.TypeLiveHighness:
		// 上神
		param.bubble, err = bubble.FindSimple(bubbleID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":   param.uv.UserID,
				"bubble_id": bubbleID,
			}).Error(err)
			// PASS
		}
		if param.bubble == nil {
			logger.WithField("bubble_id", bubbleID).Error("bubble not found")
			// PASS
		} else {
			// WORKAROUND: 兼容 iOS < 6.0.9 和 Android < 6.0.9 上神不支持 shine 控制闪光，下发 compat_type 字段让客户端使用本地的上神飘屏样式
			if goutil.IntToBool(param.bubble.Compat) {
				param.bubble.CompatType = "highness"
			}
		}
	case vip.TypeLiveNoble, vip.TypeLiveTrialNoble:
		param.bubble, err = bubble.FindSimple(bubbleID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":   param.uv.UserID,
				"bubble_id": bubbleID,
			}).Error(err)
			// PASS
		}
		if param.bubble == nil {
			logger.WithField("bubble_id", bubbleID).Error("bubble not found")
			// PASS
		} else {
			// WORKAROUND: 兼容 iOS < 6.0.9 和 Android < 6.0.9 神话不支持 shine 控制闪光，故通过配置 compat 字段来控制是否下发原有的神话飘屏样式，后续可以直接在数据库中更新
			if param.uv.Level == vip.NobleLevel7 && goutil.IntToBool(param.bubble.Compat) {
				param.bubble = &bubble.Simple{
					Type:           bubble.TypeStrNoble,
					NobleLevel:     vip.NobleLevel7,
					HighlightColor: "#48FFF4",
					NormalColor:    "#FFFFFF",
				}
			}
		}
	}
}

// newBlackCardBubble 初始化黑卡喇叭信息
func (param *messageHornParam) newBlackCardBubble() {
	bp, err := params.FindBubble()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.hornBubbleParams = bp.FindHornBubble(goutil.TimeNow())
	bubbleInfo := param.hornBubbleParams.BlackCardNotifyBubble(param.userBlackCard.Level)
	if bubbleInfo == nil || bubbleInfo.BubbleID == 0 {
		logger.WithFields(logger.Fields{
			"user_id": param.userBlackCard.UserID,
			"level":   param.userBlackCard.Level,
		}).Error("对应黑卡气泡 ID 不存在")
		// PASS
		return
	}
	param.bubble, err = bubble.FindSimple(bubbleInfo.BubbleID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":   param.userBlackCard.UserID,
			"bubble_id": bubbleInfo.BubbleID,
		}).Error(err)
		// PASS
	}
	if param.bubble == nil {
		logger.WithField("bubble_id", bubbleInfo.BubbleID).Error("bubble not found")
		// PASS
	}
}

func (param *messageHornParam) decHorn() error {
	ok, hornInfo, err := userstatus.DecreaseHornNum(param.user.UserID(), param.Type, 1)
	if err != nil {
		logger.Error(err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("全站喇叭已用完")
	}
	param.hornNum = hornInfo.HornNum
	param.nobleHornNum = hornInfo.NobleHornNum
	param.blackCardHornNum = hornInfo.BlackCardHornNum
	return nil
}

func (param *messageHornParam) newMessage(username string) string {
	format := map[string]string{
		"username":         html.EscapeString(username),
		"message":          strings.ReplaceAll(html.EscapeString(param.Message), " ", "&nbsp;"),
		"creator_username": html.EscapeString(param.room.CreatorUsername),
		"normal_color":     "#FFFFFF",
		"highlight_color":  "#FFFFFF",
	}
	if param.bubble != nil {
		param.bubble.AppendFormatParams(format)
	}
	return goutil.FormatMessage(param.hornBubbleParams.HornMsgTemplate, format)
}

func (param *messageHornParam) send() error {
	message := param.newMessage(param.user.Username)
	h := notifymessages.NewHorn(param.user.UserID(), param.RoomID, message, param.bubble, param.c.ClientIP())
	err := notifymessages.Insert(h)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.horn = h
	err = userapi.BroadcastAll(h)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func makeOSUUID(os goutil.Platform) (string, error) {
	uuid, err := uuid.NewRandom()
	if err != nil {
		return "", err
	}
	switch os {
	case goutil.Android:
		return "1" + uuid.String()[1:], nil
	case goutil.IOS:
		return "2" + uuid.String()[1:], nil
	default:
		return "3" + uuid.String()[1:], nil
	}
}

func (param *messageHornParam) checkText() error {
	uuid, err := makeOSUUID(param.c.Equip().OS)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	input := gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			UserID:    param.c.UserID(),
			EquipID:   param.c.EquipID(),
			IP:        param.c.ClientIP(),
			API:       param.c.Request().URL.Path,
			UserAgent: param.c.UserAgent(),
			Referer:   param.c.Request().Referer(),
			Content:   param.Message,
		},
		RoomID:        param.RoomID,
		RoomCatalogID: param.room.CatalogID,
		RoomCreatorID: param.room.CreatorID,
		// TODO: 之后需要存下 msg_id
		MsgID:        uuid,
		UserIsAdmin:  param.isRoomAdmin,
		UserHasMedal: param.roomMedal != nil,
		Scene:        gaia.IMSceneHorn,
	}
	result, err := userapi.CheckTextIM(param.c.UserContext(), input)
	if err != nil {
		return err
	}
	if result.NotPass {
		return actionerrors.ErrHornMessageIllegal
	}
	if result.HasLabelEvil {
		return actionerrors.ErrHornMessageSensitive
	}
	return nil
}
