package pk

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// HandlerV1 returns a handler for PK actions.
func HandlerV1() handler.Handler {
	return handler.Handler{
		Name: "pk",
		Actions: map[string]*handler.Action{
			"match/start":        handler.NewAction(handler.POST, ActionPKMatchStart, true),
			"match/cancel":       handler.NewAction(handler.POST, ActionPKMatchCancel, true),
			"close":              handler.NewAction(handler.POST, ActionPKClose, true),
			"record/list":        handler.NewAction(handler.GET, ActionRecordList, true),
			"assists/list":       handler.NewAction(handler.GET, ActionPKAssistsList, false),
			"mute":               handler.NewAction(handler.POST, ActionPKMute, true),
			"unmute":             handler.NewAction(handler.POST, ActionPKUnmute, true),
			"settings/get":       handler.NewAction(handler.GET, ActionPKSettingsGet, true),
			"settings/set":       handler.NewAction(handler.POST, ActionPKSettingsSet, true),
			"invitation/request": handler.NewAction(handler.POST, ActionPKInvitationRequest, true),
			"invitation/cancel":  handler.NewAction(handler.POST, ActionPKInvitationCancel, true),
			"invitation/accept":  handler.NewAction(handler.POST, ActionPKInvitationAccept, true),
			"invitation/refuse":  handler.NewAction(handler.POST, ActionPKInvitationRefuse, true),
		},
	}
}
