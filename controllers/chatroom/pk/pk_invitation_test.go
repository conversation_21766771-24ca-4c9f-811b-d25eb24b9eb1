package pk

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	roomID18113499  int64 = 18113499
	roomID223344    int64 = 223344
	roomID114693474 int64 = 114693474 // 开播直播间
	roomID3192516   int64 = 3192516   // 开播直播间
)

func TestActionPKInvitationRequest(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 19, 0, 0, 0, time.Local)
	})
	defer cancel()
	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, false}}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := liveconnect.Collection().UpdateMany(ctx,
		bson.M{"room_id": roomID114693474}, bson.M{"$set": bson.M{"status": liveconnect.StatusFinished}})
	require.NoError(err)
	_, err = livepk.PKCollection().DeleteMany(ctx, bson.M{"status": livepk.PKRecordStatusFighting})
	require.NoError(err)
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"status": livepk.PKPoolStatusWaiting})
	require.NoError(err)
	_, err = livemeta.Collection().UpdateOne(ctx,
		bson.M{"room_id": roomID114693474},
		bson.M{"$set": bson.M{
			"pk_settings.disable_invite":    0,
			"pk_settings.unfollowed_invite": 1,
		}},
	)
	require.NoError(err)
	_, err = liveconnect.Collection().DeleteMany(ctx, bson.M{"room_id": bson.M{"$in": bson.A{roomID114693474, roomID3192516}}})
	require.NoError(err)
	err = livemulticonnect.DB().
		Where("from_room_id IN (?) OR to_room_id IN (?)", []int64{roomID114693474, roomID3192516}, []int64{roomID114693474, roomID3192516}).
		Delete(livemulticonnect.Match{}).Error
	require.NoError(err)
	err = service.Redis.Del(keys.LockRoomPKInvitationRequest1.Format(roomID18113499)).Err()
	require.NoError(err)
	_, err = room.Update(roomID114693474, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)

	param := map[string]interface{}{
		"room_id":           roomID3192516,
		"to_room_id":        roomID114693474,
		"fighting_duration": 5,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, param)
	c.User().ID = 516
	resp, err := ActionPKInvitationRequest(c)
	require.NoError(err)
	assert.NotNil(resp)

	pool := new(livepk.Pool)
	err = livepk.PoolCollection().FindOne(ctx, bson.M{
		"room_id":    roomID3192516,
		"to_room_id": roomID114693474,
	}).Decode(pool)
	require.NoError(err)
	assert.Equal(livepk.PKTypeInvitation, pool.Type)
}

func TestNewPKInvitationRequestParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	openKey := keys.LockInvitationPKOpen0.Format()
	require.NoError(service.Redis.Set(openKey, 1, time.Minute).Err())
	body := map[string]interface{}{
		"room_id":           roomID18113499,
		"to_room_id":        roomID223344,
		"fighting_duration": 5,
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, body)
	_, err := newPKInvitationRequestParam(c)
	assert.EqualError(err, "暂末开放，敬请期待")

	lockKey := keys.LockRoomPKInvitationRequest1.Format(roomID18113499)
	require.NoError(service.Redis.Del(openKey, lockKey).Err())
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, body)
	param, err := newPKInvitationRequestParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.toRoom)
	assert.NotNil(param.fromRoom)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, body)
	_, err = newPKInvitationRequestParam(c)
	assert.EqualError(err, "操作频繁，请稍后再试")

	require.NoError(service.Redis.Del(lockKey).Err())
	body = map[string]interface{}{
		"room_id":           roomID18113499,
		"to_room_id":        1111,
		"fighting_duration": 5,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, body)
	_, err = newPKInvitationRequestParam(c)
	assert.EqualError(err, "无法找到该聊天室")

	require.NoError(service.Redis.Del(lockKey).Err())
	body = map[string]interface{}{
		"room_id":           "roomID18113499",
		"to_room_id":        1111,
		"fighting_duration": 5,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, body)
	_, err = newPKInvitationRequestParam(c)
	assert.EqualError(err, "参数错误")

	require.NoError(service.Redis.Del(lockKey).Err())
	body = map[string]interface{}{
		"room_id":           roomID18113499,
		"to_room_id":        1111,
		"fighting_duration": 6,
	}
	c = handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/pk/invitation/request", true, body)
	_, err = newPKInvitationRequestParam(c)
	assert.EqualError(err, "PK 时长参数错误")
}

func TestCheckFromRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 19, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	limitKey := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.Redis.Del(limitKey).Err())

	param := &pkInvitationRequestParam{fromRoom: new(room.Room), userID: 10}
	err := param.checkFromRoom()
	assert.EqualError(err, "非本房主播，无法发起 PK 邀请")

	param = &pkInvitationRequestParam{fromRoom: new(room.Room), toRoom: new(room.Room)}
	err = param.checkFromRoom()
	assert.EqualError(err, "不能邀请自己哦")

	room223344, err := room.Find(roomID223344)
	require.NoError(err)
	require.NotNil(room223344)
	formRoom := &room.Room{ // 不设置 room.status, 测试关播直播间
		Helper: room.Helper{
			RoomID:    roomID223344,
			CreatorID: room223344.CreatorID,
		},
	}
	param = &pkInvitationRequestParam{fromRoom: formRoom, toRoom: new(room.Room), userID: formRoom.CreatorID}
	err = param.checkFromRoom()
	assert.EqualError(err, "直播间尚未开启")

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pool, err := livepk.InsertRandomPool(roomID114693474, 10, 0)
	require.NoError(err)
	require.NotNil(pool)
	room114693474, err := room.Find(roomID114693474) // 开播直播间
	require.NoError(err)
	require.NotNil(room114693474)
	_, err = room.Update(roomID114693474, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)
	param = &pkInvitationRequestParam{
		fromRoom: room114693474,
		toRoom:   new(room.Room),
		userID:   room114693474.CreatorID,
	}
	err = param.checkFromRoom()
	assert.EqualError(err, "PK 进行中，无法发起邀请！")

	_, err = livepk.PoolCollection().DeleteMany(ctx,
		bson.M{"$or": bson.A{bson.M{"room_id": roomID114693474}, bson.M{"to_room_id": roomID114693474}}})
	require.NoError(err)
	pk, err := livepk.CreatePKRecord(ctx, "", "", [2]*livepk.Fighter{{RoomID: roomID114693474}, {RoomID: 10}})
	require.NoError(err)
	require.NotNil(pk)
	err = param.checkFromRoom()
	assert.EqualError(err, "PK 进行中，无法发起邀请！")

	_, err = livepk.PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": roomID114693474})
	require.NoError(err)
	_, err = liveconnect.Collection().DeleteMany(ctx, bson.M{"_room_id": room114693474.OID})
	require.NoError(err)
	key := keys.LockRoomPKEscapePunishment1.Format(room114693474.RoomID)
	require.NoError(service.Redis.Set(key, 1, livepk.PKRunawayPunishmentDuration).Err())
	err = param.checkFromRoom()
	assert.EqualError(err, "由于多次提前结束 PK，您暂时无法参与 PK 玩法")

	require.NoError(service.Redis.Del(key).Err())
	err = param.checkFromRoom()
	require.NoError(err)

	// 测试高峰时段 PK 次数限制 - 次数未到达上限
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 30, 0, 0, time.Local)
	})
	err = param.checkFromRoom()
	require.NoError(err)

	// 测试高峰时段 PK 次数限制 - 次数到达上限
	require.NoError(service.Redis.ZIncrBy(limitKey, 2, strconv.FormatInt(param.RoomID, 10)).Err())
	err = param.checkFromRoom()
	assert.EqualError(err, "今日 20-22 点 PK 次数已达上限！")

	// 白名单内直播间发起的邀请不受次数限制
	param.isNoLimitInvitation = true
	err = param.checkFromRoom()
	require.NoError(err)
}

func TestCheckToRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 19, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, false}}, nil
	})
	defer cleanup()
	limitKey := keys.KeyRoomPKPeakLimit1.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.Redis.Del(limitKey).Err())

	room223344, err := room.Find(roomID223344)
	require.NoError(err)
	require.NotNil(room223344)
	room223344.Status.Open = room.StatusOpenFalse
	closedRoom := room223344 // 关播直播间
	param := &pkInvitationRequestParam{toRoom: closedRoom}
	err = param.checkToRoom()
	assert.EqualError(err, "主播不在线")

	_, err = room.Update(roomID114693474, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)
	room114693474, err := room.Find(roomID114693474) // 开播直播间
	require.NoError(err)
	require.NotNil(room114693474)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemeta.Collection().UpdateOne(ctx,
		bson.M{"room_id": roomID114693474},
		bson.M{"$set": bson.M{
			"pk_settings.disable_invite":    1,
			"pk_settings.unfollowed_invite": 0,
		}},
	)
	require.NoError(err)
	param = &pkInvitationRequestParam{fromRoom: room223344, toRoom: room114693474}
	err = param.checkToRoom()
	assert.EqualError(err, "主播不允许 PK 邀请")

	_, err = livemeta.Collection().UpdateOne(ctx,
		bson.M{"room_id": roomID114693474},
		bson.M{"$set": bson.M{
			"pk_settings.disable_invite":    0,
			"pk_settings.unfollowed_invite": 0,
		}},
	)
	require.NoError(err)
	err = param.checkToRoom()
	assert.EqualError(err, "主播不允许未关注人的 PK 邀请")

	_, err = livemeta.Collection().UpdateOne(ctx,
		bson.M{"room_id": roomID114693474},
		bson.M{"$set": bson.M{
			"pk_settings.disable_invite":    0,
			"pk_settings.unfollowed_invite": 1,
		}},
	)
	require.NoError(err)
	_, err = liveconnect.Collection().DeleteMany(ctx, bson.M{"_room_id": room114693474.OID})
	require.NoError(err)
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"$or": bson.A{bson.M{"room_id": roomID114693474}, bson.M{"to_room_id": roomID114693474}}})
	require.NoError(err)
	pool, err := livepk.InsertRandomPool(roomID114693474, 10, 0)
	require.NoError(err)
	require.NotNil(pool)
	err = param.checkToRoom()
	assert.EqualError(err, "主播正在连麦，无法接受 PK 邀请")

	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"$or": bson.A{bson.M{"room_id": roomID114693474}, bson.M{"to_room_id": roomID114693474}}})
	require.NoError(err)
	pk, err := livepk.CreatePKRecord(ctx, "", "", [2]*livepk.Fighter{{RoomID: roomID114693474}, {RoomID: 10}})
	require.NoError(err)
	require.NotNil(pk)
	err = param.checkToRoom()
	assert.EqualError(err, "主播正在连麦，无法接受 PK 邀请")

	_, err = livepk.PKCollection().DeleteMany(ctx, bson.M{"fighters.room_id": roomID114693474})
	require.NoError(err)
	pool, err = livepk.AddInvitationToPool(roomID114693474, 10, roomID3192516, 10, 1)
	require.NoError(err)
	require.NotNil(pool)
	err = param.checkToRoom()
	assert.EqualError(err, "主播处理其他邀请中，请稍后再试")

	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"$or": bson.A{bson.M{"room_id": roomID114693474}, bson.M{"to_room_id": roomID114693474}}})
	require.NoError(err)
	err = param.checkToRoom()
	require.NoError(err)

	// 邀请方拉黑被邀请方
	mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{true, false}}, nil
	})
	err = param.checkToRoom()
	assert.EqualError(err, "您已拉黑对方，无法邀请对方 PK")

	// 被邀请方拉黑邀请方
	mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, true}}, nil
	})
	err = param.checkToRoom()
	assert.EqualError(err, "由于对方设置，您无法邀请对方 PK")

	// 测试高峰时段 PK 次数限制 - 次数未到达上限
	mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
		return map[string]interface{}{"block_status": []bool{false, false}}, nil
	})
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 1, 20, 30, 0, 0, time.Local)
	})
	err = param.checkToRoom()
	require.NoError(err)

	// 测试高峰时段 PK 次数限制 - 次数到达上限
	require.NoError(service.Redis.ZIncrBy(limitKey, 2, strconv.FormatInt(param.RoomID, 10)).Err())
	err = param.checkToRoom()
	assert.EqualError(err, "对方今日 20-22 点 PK 次数已达上限！")

	// 白名单内直播间发起的邀请不受次数限制
	param.isNoLimitInvitation = true
	err = param.checkToRoom()
	require.NoError(err)
}

func TestInvite(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	param := &pkInvitationRequestParam{
		RoomID:           roomID18113499,
		ToRoomID:         roomID223344,
		FightingDuration: 5,
		toRoom:           &room.Room{Helper: room.Helper{RoomID: 223344, CreatorID: 10}},
		fromRoom:         new(room.Room),
	}
	param.fromRoom.Status.Score = 10

	err := param.invite()
	require.NoError(err)
	require.NotNil(param.resp)
	assert.NotNil(param.resp.PK)
	assert.NotNil(param.resp.ToRoom)
}

func TestActionPKInvitationCancel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID18113499 := int64(18113499)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PoolCollection().DeleteMany(ctx, bson.M{"room_id": roomID18113499})
	require.NoError(err)
	_, err = livepk.PoolCollection().InsertOne(ctx, &livepk.Pool{
		Type:        livepk.PKTypeInvitation,
		RoomID:      roomID18113499,
		CreatorID:   10,
		ToRoomID:    223344,
		ToCreatorID: 10,
		Status:      livepk.PKPoolStatusWaiting,
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "pk/invitation/cancel", true, handler.M{
		"room_id": roomID18113499,
	})
	resp, err := ActionPKInvitationCancel(c)
	require.NoError(err)
	assert.Equal("success", resp)

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/cancel", true, handler.M{
		"room_id": roomID18113499,
	})
	c.User().ID = 10
	_, err = ActionPKInvitationCancel(c)
	assert.EqualError(err, "非本房房主，无法取消本次 PK 邀请")

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/cancel", true, handler.M{
		"room_id": roomID18113499,
	})
	_, err = ActionPKInvitationCancel(c)
	assert.EqualError(err, "未查询到正在等待回应的指定 PK 邀请记录")
}

func TestActionPKInvitationAccept(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PoolCollection().DeleteMany(ctx, bson.M{"status": livepk.PKPoolStatusWaiting, "type": livepk.PKTypeInvitation})
	require.NoError(err)
	pool, err := livepk.AddInvitationToPool(roomID3192516, 10, roomID114693474, 12, time.Minute.Milliseconds())
	require.NoError(err)
	require.NotNil(pool)
	_, err = room.Update(pool.ToRoomID, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)

	param := handler.M{
		"room_id":  114693474,
		"match_id": pool.OID.Hex(),
	}
	c := handler.NewTestContext(http.MethodPost, "pk/invitation/accept", true, param)
	resp, err := ActionPKInvitationAccept(c)
	require.NoError(err)
	require.NotNil(resp)
	require.IsType(&livepk.LivePK{}, resp)
	pk := resp.(*livepk.LivePK)
	assert.Equal(livepk.PKTypeInvitation, pk.Type)

	rooms, err := room.FindAll(bson.M{"room_id": bson.M{"$in": []int64{pool.RoomID, pool.ToRoomID}}})
	require.NoError(err)
	require.Len(rooms, 2)
	assert.Equal(rooms[0].Connect.ID, rooms[1].Connect.ID)
	assert.Equal(rooms[0].Connect.Provider, rooms[1].Connect.Provider)

	pipe := service.Redis.Pipeline()
	cmd1 := pipe.Get(keys.KeyPKFighting1.Format(pool.RoomID))
	cmd2 := pipe.Get(keys.KeyPKFighting1.Format(pool.ToRoomID))
	_, err = pipe.Exec()
	require.NoError(err)
	assert.Equal(pk.OID.Hex(), cmd1.Val())
	assert.Equal(pk.OID.Hex(), cmd2.Val())
}

func TestNewPKInvitationAcceptParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := handler.M{}
	c := handler.NewTestContext(http.MethodPost, "", true, body)
	_, err := newPKInvitationAcceptParam(c)
	assert.EqualError(err, "参数错误")

	body = handler.M{"room_id": 114693474, "match_id": "111111"}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "参数错误")

	body = handler.M{"room_id": 114693474, "match_id": primitive.NewObjectID()}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "未查询到正在等待回应的指定 PK 邀请记录")

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livepk.PoolCollection().DeleteMany(ctx, bson.M{"status": livepk.PKPoolStatusWaiting, "type": livepk.PKTypeInvitation})
	require.NoError(err)
	pool, err := livepk.AddInvitationToPool(roomID3192516, 10, roomID114693474, 12, time.Minute.Milliseconds())
	require.NoError(err)
	require.NotNil(pool)

	body = handler.M{"room_id": 223344, "match_id": pool.OID.Hex()}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "非本次 PK 被邀请方，无法接受本次 PK 邀请")

	body = handler.M{"room_id": 114693474, "match_id": pool.OID.Hex()}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	c.User().ID = 1
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "非本次 PK 被邀请方，无法接受本次 PK 邀请")

	body = handler.M{"room_id": 114693474, "match_id": pool.OID.Hex()}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	param, err := newPKInvitationAcceptParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.pool)

	_, err = livepk.PoolCollection().UpdateOne(ctx, bson.M{"_id": pool.OID}, bson.M{"$set": bson.M{"room_id": 223344}})
	require.NoError(err)
	_, err = room.Update(pool.ToRoomID, bson.M{"status.open": room.StatusOpenFalse})
	require.NoError(err)
	body = handler.M{"room_id": 114693474, "match_id": pool.OID.Hex()}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "对方已关播，接受 PK 邀请失败")

	_, err = livepk.PoolCollection().UpdateOne(ctx, bson.M{"_id": pool.OID}, bson.M{"$set": bson.M{"status": livepk.PKPoolStatusCancel}})
	require.NoError(err)
	body = handler.M{"room_id": 114693474, "match_id": pool.OID.Hex()}
	c = handler.NewTestContext(http.MethodPost, "", true, body)
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "本次邀请已失效")
}

func TestPKInvitationParamCheckPool(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pool := &livepk.Pool{
		Type:        livepk.PKTypeInvitation,
		ToRoomID:    roomID18113499,
		ToCreatorID: 12,
		Status:      livepk.PKPoolStatusWaiting,
	}
	res, err := livepk.PoolCollection().InsertOne(ctx, pool)
	require.NoError(err)
	pool.OID = res.InsertedID.(primitive.ObjectID)

	param := &pkInvitationParam{
		MatchID: pool.OID.Hex(),
		action:  "测试",
		RoomID:  roomID18113499,
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.checkPool()
	require.NoError(err)

	param = &pkInvitationParam{
		MatchID: pool.OID.Hex(),
		action:  "测试",
		RoomID:  1,
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.checkPool()
	assert.EqualError(err, "非本次 PK 被邀请方，无法测试本次 PK 邀请")

	param = &pkInvitationParam{
		MatchID: pool.OID.Hex(),
		action:  "测试",
		RoomID:  roomID18113499,
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	param.c.User().ID = 1
	err = param.checkPool()
	assert.EqualError(err, "非本次 PK 被邀请方，无法测试本次 PK 邀请")

	_, err = livepk.PoolCollection().UpdateOne(ctx,
		bson.M{"_id": pool.OID},
		bson.M{"$set": bson.M{"status": livepk.PKPoolStatusSuccess}})
	require.NoError(err)
	param = &pkInvitationParam{
		MatchID: pool.OID.Hex(),
		action:  "测试",
		RoomID:  roomID18113499,
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.checkPool()
	assert.EqualError(err, "本次邀请已失效")

	_, err = livepk.PoolCollection().UpdateOne(ctx,
		bson.M{"_id": pool.OID},
		bson.M{"$set": bson.M{"type": livepk.PKTypeRandom}})
	require.NoError(err)
	err = param.checkPool()
	assert.EqualError(err, "未查询到正在等待回应的指定 PK 邀请记录")

	param = &pkInvitationParam{
		MatchID: "624157b6ee4b564c5cac9649",
		action:  "测试",
		RoomID:  roomID18113499,
		c:       handler.NewTestContext(http.MethodPost, "", true, nil),
	}
	err = param.checkPool()
	assert.EqualError(err, "未查询到正在等待回应的指定 PK 邀请记录")
}

func TestPKInvitationParamCreatePK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PoolCollection().DeleteMany(ctx, bson.M{"status": livepk.PKPoolStatusWaiting, "type": livepk.PKTypeInvitation})
	require.NoError(err)
	pool, err := livepk.AddInvitationToPool(114693474, 10, 3192516, 10, time.Minute.Milliseconds())
	require.NoError(err)
	require.NotNil(pool)
	rs, err := room.List(bson.M{
		"room_id": bson.M{"$in": []int64{114693474, 3192516}},
	}, nil, &room.FindOptions{FindCreator: true})
	require.NoError(err)
	require.Len(rs, 2)

	param := &pkInvitationParam{
		pool:  pool,
		rooms: rs,
	}
	err = param.createPK()
	require.NoError(err)
	assert.NotEmpty(param.pk)
	assert.NotEmpty(param.pk.OID)
	assert.NotEmpty(param.pk.ConnectID)

	rooms, err := room.FindAll(bson.M{"room_id": bson.M{"$in": []int64{pool.RoomID, pool.ToRoomID}}})
	require.NoError(err)
	require.Len(rooms, 2)
	assert.Equal(rooms[0].Connect.ID, rooms[1].Connect.ID)
	assert.Equal(rooms[0].Connect.Provider, rooms[1].Connect.Provider)

	pipe := service.Redis.Pipeline()
	cmd1 := pipe.Get(keys.KeyPKFighting1.Format(pool.RoomID))
	cmd2 := pipe.Get(keys.KeyPKFighting1.Format(pool.ToRoomID))
	_, err = pipe.Exec()
	require.NoError(err)
	assert.Equal(param.pk.OID.Hex(), cmd1.Val())
	assert.Equal(param.pk.OID.Hex(), cmd2.Val())
}

func TestActionPKInvitationRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID18113499 := int64(18113499)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livepk.PoolCollection().DeleteMany(ctx, bson.M{"room_id": roomID18113499})
	require.NoError(err)
	res, err := livepk.PoolCollection().InsertOne(ctx, &livepk.Pool{
		Type:        livepk.PKTypeInvitation,
		RoomID:      roomID223344,
		CreatorID:   223344,
		ToRoomID:    roomID18113499,
		ToCreatorID: 12,
		Status:      livepk.PKPoolStatusWaiting,
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": res.InsertedID,
	})
	resp, err := ActionPKInvitationRefuse(c)
	require.NoError(err)
	assert.Equal("success", resp)
	pool, err := livepk.FindPKPoolByOID(res.InsertedID.(primitive.ObjectID))
	require.NoError(err)
	require.NotNil(pool)
	assert.Equal(livepk.PKPoolStatusRefuse, pool.Status)

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": "624157b6ee4b564c5cac9647",
	})
	_, err = ActionPKInvitationRefuse(c)
	assert.EqualError(err, "未查询到正在等待回应的指定 PK 邀请记录")
}

func TestNewPKInvitationRefuseParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := livepk.PoolCollection().InsertOne(ctx, &livepk.Pool{
		Type:        livepk.PKTypeInvitation,
		ToRoomID:    roomID18113499,
		ToCreatorID: 12,
		Status:      livepk.PKPoolStatusWaiting,
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": res.InsertedID,
	})
	param, err := newPKInvitationRefuseParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.pool)

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": res.InsertedID,
	})
	c.User().ID = 1
	_, err = newPKInvitationRefuseParam(c)
	assert.EqualError(err, "非本次 PK 被邀请方，无法拒绝本次 PK 邀请")

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID223344,
		"match_id": res.InsertedID,
	})
	_, err = newPKInvitationRefuseParam(c)
	assert.EqualError(err, "非本次 PK 被邀请方，无法拒绝本次 PK 邀请")

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": "624157b6ee4b564c5cac9647",
	})
	_, err = newPKInvitationRefuseParam(c)
	assert.EqualError(err, "未查询到正在等待回应的指定 PK 邀请记录")

	_, err = livepk.PoolCollection().UpdateOne(ctx,
		bson.M{"_id": res.InsertedID.(primitive.ObjectID)},
		bson.M{"$set": bson.M{"status": livepk.PKPoolStatusRefuse}})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": res.InsertedID,
	})
	_, err = newPKInvitationAcceptParam(c)
	assert.EqualError(err, "本次邀请已失效")

	c = handler.NewTestContext(http.MethodPost, "pk/invitation/refuse", true, handler.M{
		"room_id":  roomID18113499,
		"match_id": "1111",
	})
	_, err = newPKInvitationRefuseParam(c)
	assert.EqualError(err, "参数错误")
}

func TestPKInvitationParamRefuse(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastUser, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	pool := &livepk.Pool{
		Type:        livepk.PKTypeInvitation,
		RoomID:      10,
		CreatorID:   10,
		ToRoomID:    roomID18113499,
		ToCreatorID: 12,
		Status:      livepk.PKPoolStatusWaiting,
		CreateTime:  goutil.TimeNow().Unix(),
	}
	res, err := livepk.PoolCollection().InsertOne(ctx, pool)
	require.NoError(err)
	pool.OID = res.InsertedID.(primitive.ObjectID)

	param := &pkInvitationParam{
		pool: pool,
	}
	err = param.refuse()
	require.NoError(err)
	findPool, err := livepk.FindPKPoolByOID(pool.OID)
	require.NoError(err)
	require.NotNil(findPool)
	assert.Equal(livepk.PKPoolStatusRefuse, findPool.Status)
}
