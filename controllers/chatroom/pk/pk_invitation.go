package pk

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils/connectcheck"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type pkInvitationRequestParam struct {
	RoomID           int64 `form:"room_id" json:"room_id"`
	ToRoomID         int64 `form:"to_room_id" json:"to_room_id"`
	FightingDuration int64 `form:"fighting_duration" json:"fighting_duration"`

	fromRoom, toRoom    *room.Room
	userID              int64
	pool                *livepk.Pool
	isNoLimitInvitation bool // 是否由白名单内直播间发起的邀请
	resp                *pkInvitationRequestResponse
}

type pkRequestInfo struct {
	MatchID          string `json:"match_id"`
	Duration         int64  `json:"duration"`
	RemainDuration   int64  `json:"remain_duration"`
	FightingDuration int64  `json:"fighting_duration"`
	CreateTime       int64  `json:"create_time"`
}

type pkInvitationRequestResponse struct {
	PK     *pkRequestInfo `json:"pk"`
	ToRoom *pkRoomInfo    `json:"to_room"`
}

type pkInvitationPayload struct {
	Type     string         `json:"type"`
	Event    string         `json:"event"`
	PK       *pkRequestInfo `json:"pk"`
	FromRoom *pkRoomInfo    `json:"from_room"`
}

// ActionPKInvitationRequest 发起指定 PK 邀请
// REVIEW: PK 可选时长是否要通过接口下发
/**
 * @api {post} /api/v2/chatroom/pk/invitation/request 发起指定 PK 邀请
 * @apiDescription 主播直播间邀请倒计时结束，在等待 5s 后仍未收到同意/拒绝邀请的消息, 请求保底接口 /api/v2/chatroom/meta 参数 type=8 获取 PK 信息。用户直播间无保底操作
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} to_room_id 被邀请 PK 直播间 ID
 * @apiParam {number=5,10,20,30} fighting_duration PK 时长，单位：分钟
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "pk": {
 *         "match_id": "5ab9d5f1bc9b53298ce5a5a9",
 *         "duration": 10000, // 邀请等待期总时长，单位毫秒
 *         "remain_duration": 10000, // 邀请等待期倒计时，单位毫秒
 *         "fighting_duration": 50000, // PK 打榜总时长，单位毫秒
 *         "create_time": 1584808200 // 单位秒
 *       },
 *       "to_room": { // 被邀请主播方信息
 *         "room_id": 123456,
 *         "creator_id": 13,
 *         "creator_username": "test13",
 *         "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 被邀请主播方消息（仅主播接受消息）
 *   {
 *     "type": "pk",
 *     "event": "invite_request",
 *     "pk": {
 *       "match_id": "5ab9d5f1bc9b53298ce5a5a9",
 *       "duration": 10000, // 邀请等待期总时长，单位毫秒
 *       "remain_duration": 10000, // 邀请等待期倒计时，单位毫秒
 *       "fighting_duration": 50000, // PK 打榜总时长，单位毫秒
 *       "create_time": 1584808200 // 单位秒
 *     },
 *     "from_room": { // 邀请发起主播方信息
 *       "room_id": 123456,
 *       "creator_id": 13,
 *       "creator_username": "test13",
 *       "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *       "statistics": {
 *         "score": 100 // 直播间热度
 *       }
 *     }
 *   }
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该聊天室
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKInvitationRequest(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPKInvitationRequestParam(c)
	if err != nil {
		return nil, err
	}
	err = connectcheck.NewPKComponent(param.fromRoom, param.toRoom).Check()
	if err != nil {
		return nil, err
	}
	err = param.checkFromRoom()
	if err != nil {
		return nil, err
	}
	err = param.checkToRoom()
	if err != nil {
		return nil, err
	}
	err = param.invite()
	if err != nil {
		return nil, err
	}
	return param.resp, nil
}

func newPKInvitationRequestParam(c *handler.Context) (*pkInvitationRequestParam, error) {
	param := new(pkInvitationRequestParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	// 检查非预发环境是否开放指定 PK
	if !goutil.IsPreEnv() {
		count, err := service.Redis.Exists(keys.LockInvitationPKOpen0.Format()).Result()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if count != 0 {
			return nil, actionerrors.NewErrForbidden("暂末开放，敬请期待")
		}
	}

	lockKey := keys.LockRoomPKInvitationRequest1.Format(param.RoomID)
	ok, err := service.Redis.SetNX(lockKey, 1, 2*time.Second).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrForbidden("操作频繁，请稍后再试")
	}

	if !goutil.HasElem(livepk.OptionalFightingDurations, param.FightingDuration) {
		return nil, actionerrors.ErrParamsMsg("PK 时长参数错误")
	}
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": bson.A{param.RoomID, param.ToRoomID}}},
		nil, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) != 2 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	for _, r := range rooms {
		switch r.RoomID {
		case param.RoomID:
			param.fromRoom = r
		case param.ToRoomID:
			param.toRoom = r
		}
	}
	param.userID = c.UserID()

	// TODO: 后续可能去掉对白名单直播间的提前结束惩罚，新增 isNoLimitInvitation 属性便于判断
	pkConfig, err := params.FindPK()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.isNoLimitInvitation = pkConfig.IsNoLimitPKRoomID(param.RoomID)

	return param, nil
}

// 检查发起邀请的房间
func (param *pkInvitationRequestParam) checkFromRoom() error {
	if param.fromRoom.CreatorID != param.userID {
		return actionerrors.NewErrForbidden("非本房主播，无法发起 PK 邀请")
	}
	if param.fromRoom.CreatorID == param.toRoom.CreatorID {
		return actionerrors.NewErrForbidden("不能邀请自己哦")
	}
	if param.fromRoom.Status.Open == room.StatusOpenFalse {
		return actionerrors.ErrClosedRoom
	}
	// 检查是否在 PK 匹配或邀请中
	pool, err := livepk.FindPool(bson.M{
		"status": livepk.PKPoolStatusWaiting,
		"$or":    bson.A{bson.M{"room_id": param.fromRoom.RoomID}, bson.M{"to_room_id": param.fromRoom.RoomID}},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pool != nil {
		return actionerrors.NewErrForbidden("PK 进行中，无法发起邀请！")
	}
	// 检查是否在 PK 中
	pk, err := livepk.FindOne(bson.M{
		"fighters.room_id": param.fromRoom.RoomID,
		"status":           bson.M{"$in": []int{livepk.PKRecordStatusConnect, livepk.PKRecordStatusFighting, livepk.PKRecordStatusPunishment}},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pk != nil {
		return actionerrors.NewErrForbidden("PK 进行中，无法发起邀请！")
	}
	// 检查是否在冷静期
	count, err := service.Redis.Exists(keys.LockRoomPKEscapePunishment1.Format(param.fromRoom.RoomID)).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if count != 0 {
		return actionerrors.NewErrLiveForbidden("由于多次提前结束 PK，您暂时无法参与 PK 玩法")
	}
	// 检查是否超过了高峰时段的 PK 次数限制
	exceed, err := livepk.ExceedPKPeakLimit(param.RoomID, param.isNoLimitInvitation)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exceed {
		return errExceedPKPeakLimit(true)
	}
	return nil
}

// 检查被邀请的房间
func (param *pkInvitationRequestParam) checkToRoom() error {
	if param.toRoom.Status.Open == room.StatusOpenFalse {
		return actionerrors.NewErrForbidden("主播不在线")
	}
	// 检查是否拉黑对方或被对方拉黑
	fromBlockTo, toBlockFrom, err := userapi.UserBlockStatus(
		param.fromRoom.CreatorID, param.toRoom.CreatorID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if fromBlockTo {
		return actionerrors.NewErrBlockUser("您已拉黑对方，无法邀请对方 PK")
	}
	if toBlockFrom {
		return actionerrors.NewErrBlockUser("由于对方设置，您无法邀请对方 PK")
	}
	// PK 设置, 是否允许指定 PK 邀请, 是否允许未关注人指定 PK 邀请
	pkSetting, err := livemeta.FindPKSettings(param.toRoom.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pkSetting != nil && goutil.IntToBool(pkSetting.DisableInvite) {
		return actionerrors.NewErrForbidden("主播不允许 PK 邀请")
	}
	if pkSetting == nil || !goutil.IntToBool(pkSetting.UnfollowedInvite) {
		followed, err := attentionuser.HasFollowed(param.toRoom.CreatorID, param.fromRoom.CreatorID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !followed {
			return actionerrors.NewErrForbidden("主播不允许未关注人的 PK 邀请")
		}
	}
	// 检查是否正在 PK 匹配中
	pool, err := livepk.FindPool(bson.M{
		"type":    livepk.PKTypeRandom,
		"status":  livepk.PKPoolStatusWaiting,
		"room_id": param.toRoom.RoomID,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pool != nil {
		return actionerrors.NewErrForbidden("主播正在连麦，无法接受 PK 邀请")
	}
	// 检查是否正在 PK 中
	pk, err := livepk.FindOne(bson.M{
		"fighters.room_id": param.toRoom.RoomID,
		"status":           bson.M{"$in": []int{livepk.PKRecordStatusConnect, livepk.PKRecordStatusFighting, livepk.PKRecordStatusPunishment}},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pk != nil {
		return actionerrors.NewErrForbidden("主播正在连麦，无法接受 PK 邀请")
	}
	// 检查是否正在 PK 邀请中
	pool, err = livepk.FindPool(bson.M{
		"type":   livepk.PKTypeInvitation,
		"status": livepk.PKPoolStatusWaiting,
		"$or": bson.A{
			bson.M{"to_room_id": param.toRoom.RoomID}, bson.M{"room_id": param.toRoom.RoomID},
		},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pool != nil {
		return actionerrors.NewErrForbidden("主播处理其他邀请中，请稍后再试")
	}
	// 检查是否超过了高峰时段的 PK 次数限制
	exceed, err := livepk.ExceedPKPeakLimit(param.ToRoomID, param.isNoLimitInvitation)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exceed {
		return errExceedPKPeakLimit(false)
	}
	return nil
}

func (param *pkInvitationRequestParam) invite() error {
	var err error
	param.pool, err = livepk.AddInvitationToPool(param.fromRoom.RoomID, param.fromRoom.CreatorID,
		param.toRoom.RoomID, param.toRoom.CreatorID, param.FightingDuration*time.Minute.Milliseconds())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.resp = &pkInvitationRequestResponse{
		PK: &pkRequestInfo{
			MatchID:          param.pool.OID.Hex(),
			Duration:         livepk.PKInvitationDuration.Milliseconds(),
			RemainDuration:   param.pool.WaitingRemainDuration(),
			FightingDuration: param.pool.Duration,
			CreateTime:       param.pool.CreateTime,
		},
		ToRoom: &pkRoomInfo{
			RoomID:          param.toRoom.RoomID,
			CreatorID:       param.toRoom.CreatorID,
			CreatorUsername: param.toRoom.CreatorUsername,
			CreatorIconURL:  param.toRoom.CreatorIconURL,
		},
	}
	livepk.DelayPKMatchTimeout(param.pool)
	param.notify()
	return nil
}

func (param *pkInvitationRequestParam) notify() {
	payload := &pkInvitationPayload{
		Type:  liveim.TypePK,
		Event: liveim.EventPKInviteRequest,
		PK:    param.resp.PK,
		FromRoom: &pkRoomInfo{
			RoomID:          param.fromRoom.RoomID,
			CreatorID:       param.fromRoom.CreatorID,
			CreatorUsername: param.fromRoom.CreatorUsername,
			CreatorIconURL:  param.fromRoom.CreatorIconURL,
			Statistics: &statistics{
				Score: param.fromRoom.Statistics.Score,
			},
		},
	}
	err := userapi.BroadcastUser(param.toRoom.RoomID, param.toRoom.CreatorID, payload)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type pkInvitationCancelParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`
}

// ActionPKInvitationCancel 取消指定 PK 邀请
/**
 * @api {post} /api/v2/chatroom/pk/invitation/cancel 取消指定 PK 邀请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 被邀请主播方消息（仅主播接受消息）
 *   {
 *     "type": "pk",
 *     "event": "invite_cancel",
 *     "room_id": 1, // 当前直播间
 *     "pk": {
 *       "match_id": "5ab9d5f1bc9b53298ce5a5a9",
 *       "from_room_id": 1, // 邀请发起方
 *       "to_room_id": 2,
 *       "create_time": 1584808200
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKInvitationCancel(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPKInvitationCancelParam(c)
	if err != nil {
		return nil, err
	}
	err = param.cancelPK()
	if err != nil {
		return nil, err
	}
	return "success", nil
}

func newPKInvitationCancelParam(c *handler.Context) (*pkInvitationCancelParam, error) {
	param := new(pkInvitationCancelParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.CreatorID != c.UserID() {
		return nil, actionerrors.NewErrForbidden("非本房房主，无法取消本次 PK 邀请")
	}
	return param, nil
}

func (param *pkInvitationCancelParam) cancelPK() error {
	pool, err := livepk.CancelPKInvitation(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pool == nil {
		return actionerrors.ErrNotFound("未查询到正在等待回应的指定 PK 邀请记录")
	}

	err = userapi.BroadcastUser(pool.ToRoomID, pool.ToCreatorID,
		pool.NewPKInvitationFailedPayload(liveim.EventPKInviteCancel))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

type pkInvitationParam struct {
	RoomID  int64  `form:"room_id" json:"room_id"`
	MatchID string `form:"match_id" json:"match_id"`

	c      *handler.Context
	action string
	pool   *livepk.Pool
	pk     *livepk.LivePK
	rooms  []*room.Room
}

type pkInvitationAcceptPayload struct {
	Type   string         `json:"type"`
	Event  string         `json:"event"`
	RoomID int64          `json:"room_id"`
	PK     *livepk.LivePK `json:"pk"`
}

// ActionPKInvitationAccept 接受指定 PK 邀请
/**
 * @api {post} /api/v2/chatroom/pk/invitation/accept 接受指定 PK 邀请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} match_id 邀请记录 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "pk": {
 *         "pk_id": "5ab9d5f1bc9b53298ce5a5a9",
 *         "type": 1, // 0: 随机 PK; 1: 指定 PK; 2: 排位赛
 *         "status": 1, // 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 结束
 *         "start_time": 16414300000000, // PK 开始时间, 单位毫秒
 *         "duration": 500000, // 距离 PK 结束或惩罚结束等待总时长, 单位毫秒
 *         "remain_duration": 200, // 距离 PK 结束、匹配超时、惩罚结束倒计时, 单位毫秒
 *         "fighters": [
 *           {
 *             "room_id": 1,
 *             "creator_id": 1,
 *             "score": 0,
 *             "name": "room1",
 *             "creator_username": "name1",
 *             "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *           },
 *           {
 *             "room_id": 2,
 *             "creator_id": 2,
 *             "score": 0,
 *             "name": "room2",
 *             "creator_username": "name2",
 *             "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *           }
 *         ]
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 接受邀请后双方直播间消息
 *   {
 *     "type": "pk",
 *     "event": "match_success",
 *     "room_id": 10659544,
 *     "pk": {
 *       "pk_id": "5ab9d5f1bc9b53298ce5a5a9",
 *       "type": 1, // 0: 随机 PK; 1: 指定 PK; 2: 排位赛
 *       "status": 1, // 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 结束
 *       "start_time": 16414300000000, // PK 开始时间, 单位毫秒
 *       "duration": 500000, // 距离 PK 结束或惩罚结束等待总时长, 单位毫秒
 *       "remain_duration": 200, // 距离 PK 结束、匹配超时、惩罚结束倒计时, 单位毫秒
 *       "fighters": [
 *         {
 *           "room_id": 1,
 *           "creator_id": 1,
 *           "score": 0,
 *           "name": "room1",
 *           "creator_username": "name1",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *         },
 *         {
 *           "room_id": 2,
 *           "creator_id": 2,
 *           "score": 0,
 *           "name": "room2",
 *           "creator_username": "name2",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKInvitationAccept(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPKInvitationAcceptParam(c)
	if err != nil {
		return nil, err
	}
	err = param.createPK()
	if err != nil {
		return nil, err
	}
	livepk.DelayPKFightingFinish(param.pk)
	livepk.DelayPKPunishFinish(param.pk)
	param.broadcast()
	return param.pk, nil
}

func newPKInvitationAcceptParam(c *handler.Context) (*pkInvitationParam, error) {
	param := &pkInvitationParam{action: "接受"}
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	err = param.checkPool()
	if err != nil {
		return nil, err
	}
	param.rooms, err = room.List(bson.M{
		"room_id": bson.M{"$in": []int64{param.pool.RoomID, param.pool.ToRoomID}},
	}, nil, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.rooms) != 2 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.rooms[0].Status.Open == room.StatusOpenFalse || param.rooms[1].Status.Open == room.StatusOpenFalse {
		_, err := livepk.SetWaitPoolStatusByOID(param.pool.OID, livepk.PKPoolStatusFail)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return nil, actionerrors.NewErrForbidden("对方已关播，接受 PK 邀请失败")
	}
	return param, nil
}

func (param *pkInvitationParam) checkPool() error {
	poolOID, err := primitive.ObjectIDFromHex(param.MatchID)
	if err != nil {
		return actionerrors.ErrParams
	}
	param.pool, err = livepk.FindPKPoolByOID(poolOID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.pool == nil || param.pool.Type != livepk.PKTypeInvitation {
		return actionerrors.ErrNotFound("未查询到正在等待回应的指定 PK 邀请记录")
	}
	if param.pool.ToRoomID != param.RoomID || param.pool.ToCreatorID != param.c.UserID() {
		return actionerrors.NewErrForbidden(fmt.Sprintf("非本次 PK 被邀请方，无法%s本次 PK 邀请", param.action))
	}
	if param.pool.Status != livepk.PKPoolStatusWaiting {
		return actionerrors.NewErrForbidden("本次邀请已失效")
	}
	return nil
}

func (param *pkInvitationParam) createPK() error {
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		param.pk = new(livepk.LivePK)
		for i, r := range param.rooms {
			param.pk.Fighters[i] = &livepk.Fighter{
				RoomID:          r.RoomID,
				CreatorID:       r.CreatorID,
				GuildID:         r.GuildID,
				Name:            r.Name,
				CreatorUsername: r.CreatorUsername,
				CreatorIconURL:  r.CreatorIconURL,
				Provider:        r.Connect.Provider,
				PushType:        r.Connect.PushType,
			}
		}
		connectID, provider, err := param.pk.MatchProvider()
		if err != nil {
			return err
		}
		param.pk, err = livepk.CreatePKInvitationRecord(ctx,
			time.Duration(param.pool.Duration)*time.Millisecond, connectID, provider, param.pk.Fighters)
		if err != nil {
			return err
		}
		// 修改房间状态
		_, err = room.Collection().UpdateMany(ctx,
			bson.M{"room_id": bson.M{"$in": bson.A{param.pool.RoomID, param.pool.ToRoomID}}},
			bson.M{"$set": bson.M{
				"status.pk":        room.PKStatusOngoing,
				"connect.id":       connectID,
				"connect.provider": provider,
			}},
		)
		if err != nil {
			return err
		}
		// 更新 PKPool 状态为匹配成功
		res, err := livepk.PoolCollection().UpdateOne(ctx,
			bson.M{"_id": param.pool.OID, "status": livepk.PKPoolStatusWaiting},
			bson.M{"$set": bson.M{
				"status":        livepk.PKPoolStatusSuccess,
				"modified_time": goutil.TimeNow().Unix(),
			}},
		)
		if err != nil {
			return err
		}
		if res.ModifiedCount != 1 {
			return servicedb.ErrNoRowsAffected
		}
		// PK 打榜计时
		pipe := service.Redis.TxPipeline()
		for _, f := range param.pk.Fighters {
			key := keys.KeyPKFighting1.Format(f.RoomID)
			pipe.Set(key, param.pk.OID.Hex(), time.Duration(param.pool.Duration)*time.Millisecond)
		}
		// 递增双方 PK 高峰时段连通次数
		livepk.IncrPKPeakCount(pipe, param.pk, &param.pool.RoomID)
		if _, err = pipe.Exec(); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		if err == servicedb.ErrNoRowsAffected {
			return actionerrors.NewErrForbidden("本次邀请已失效")
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *pkInvitationParam) broadcast() {
	messages := make([]*userapi.BroadcastElem, 0, len(param.pk.Fighters))
	for i, f := range param.pk.Fighters {
		var pk *livepk.LivePK
		if i == 0 {
			pk = param.pk
		} else {
			pk = livepk.CopyLivePK(param.pk, true)
			pk.BuildDuration()
		}
		payload := &pkInvitationAcceptPayload{
			Type:   liveim.TypePK,
			Event:  liveim.EventPKMatchSuccess,
			RoomID: f.RoomID,
			PK:     pk,
		}
		messages = append(messages, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  payload.RoomID,
			Payload: payload,
		})
	}
	if err := userapi.BroadcastMany(messages); err != nil {
		logger.Error(err)
		// PASS
	}
}

// ActionPKInvitationRefuse 拒绝指定 PK 邀请
/**
 * @api {post} /api/v2/chatroom/pk/invitation/refuse 拒绝指定 PK 邀请
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {String} match_id 邀请记录 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 主动拒绝时，邀请发起方消息
 *   {
 *     "type": "pk",
 *     "event": "invite_refuse",
 *     "room_id": 1, // 当前直播间（本消息只会发给邀请方）
 *     "pk": {
 *       "match_id": "5ab9d5f1bc9b53298ce5a5a9",
 *       "from_room_id": 1, // 邀请发起方
 *       "to_room_id": 2,
 *       "create_time": 1584808200
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 自动超时邀请双方消息
 *   {
 *     "type": "pk",
 *     "event": "invite_timeout",
 *     "room_id": 1, // 当前直播间
 *     "pk": {
 *       "match_id": "5ab9d5f1bc9b53298ce5a5a9",
 *       "from_room_id": 1, // 邀请发起方，双方需要知道自己是邀请方还是被邀请方，来提示不同的信息
 *       "to_room_id": 2,
 *       "create_time": 1584808200
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (404) {Number} code 501010004
 * @apiError (404) {String} info 无法找到对应资源
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKInvitationRefuse(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPKInvitationRefuseParam(c)
	if err != nil {
		return nil, err
	}
	err = param.refuse()
	if err != nil {
		return nil, err
	}
	return "success", nil
}

func newPKInvitationRefuseParam(c *handler.Context) (*pkInvitationParam, error) {
	param := &pkInvitationParam{action: "拒绝"}
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	err = param.checkPool()
	if err != nil {
		return nil, err
	}
	return param, nil
}

func (param *pkInvitationParam) refuse() error {
	ok, err := livepk.SetWaitPoolStatusByOID(param.pool.OID, livepk.PKPoolStatusRefuse)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrNotFound("未查询到正在等待回应的指定 PK 邀请记录")
	}

	err = userapi.BroadcastUser(param.pool.RoomID, param.pool.CreatorID,
		param.pool.NewPKInvitationFailedPayload(liveim.EventPKInviteRefuse))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
