package pk

import (
	"testing"

	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/stretchr/testify/assert"
)

func TestHandlerV1(t *testing.T) {
	handler := HandlerV1()
	assert.Equal(t, "pk", handler.Name)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(handler, "match/start", "match/cancel", "close", "record/list", "assists/list", "mute", "unmute", "settings/get", "settings/set", "invitation/request", "invitation/cancel", "invitation/accept", "invitation/refuse")
}
