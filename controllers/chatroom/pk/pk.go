package pk

import (
	"fmt"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils/connectcheck"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livelog"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type statistics struct {
	Score          int64  `json:"score"`
	AttentionCount *int64 `json:"attention_count,omitempty"` // 仅主播连线搜索页下发
}

type pkRoomInfo struct {
	RoomID          int64       `json:"room_id"`
	CreatorID       int64       `json:"creator_id"`
	CreatorUsername string      `json:"creator_username"`
	CreatorIconURL  string      `json:"creator_iconurl"`
	Statistics      *statistics `json:"statistics,omitempty"`
}

// errExceedPKPeakLimit 超过高峰期 PK 次数限制错误
func errExceedPKPeakLimit(owner bool) error {
	startClock := util.ParseClock(config.Conf.Params.PK.PeakLimitStartTime)
	endClock := util.ParseClock(config.Conf.Params.PK.PeakLimitEndTime)
	var message string
	if owner {
		message = fmt.Sprintf("今日 %d-%d 点 PK 次数已达上限！", startClock.Hour, endClock.Hour)
	} else {
		message = fmt.Sprintf("对方今日 %d-%d 点 PK 次数已达上限！", startClock.Hour, endClock.Hour)
	}
	return actionerrors.NewErrLiveForbidden(message)
}

type pkMatchStartResp struct {
	MatchStatus    int   `json:"match_status"`
	Duration       int64 `json:"duration"`
	RemainDuration int64 `json:"remain_duration"`
	CreateTime     int64 `json:"create_time"`

	roomID int64
	r      *room.Room
	pool   *livepk.Pool
}

// ActionPKMatchStart 开始 PK 匹配
/**
 * @api {post} /api/v2/chatroom/pk/match/start 开始 PK 匹配
 * @apiDescription 主播直播间倒计时结束，在等待 5s 后仍未收到成功/失败消息, 请求保底接口 /api/v2/chatroom/meta 参数 type=8 获取 PK 信息。用户直播间无保底操作
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "pk": {
 *         "match_status": 0, // PK 状态, 0: 匹配中
 *         "duration": 60000, // 匹配期总时长，单位毫秒
 *         "remain_duration": 60000, // 匹配倒计时，单位毫秒
 *         "create_time": 1584808200 // 单位秒
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 开始匹配的房间内消息
 *   {
 *     "type": "pk",
 *     "event": "match_start",
 *     "room_id": 123456,
 *     "pk": {
 *       "match_status": 0, // PK 状态, 0: 匹配中
 *       "duration": 60000, // 匹配期总时长，单位毫秒
 *       "remain_duration": 60000, // 匹配倒计时，单位毫秒
 *       "create_time": 1584808200  // 单位秒
 *     },
 *     "room": {
 *       "room_id": 123456,
 *       "creator_id": 13,
 *       "creator_username": "test13",
 *       "creator_iconurl": "http://aaa.bbb.ccc/test.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 匹配成功消息返回结果
 *   {
 *     "type": "pk",
 *     "event": "match_success",
 *     "room_id": 10659544,
 *     "pk": {
 *       "pk_id": "5ab9d5f1bc9b53298ce5a5a9",
 *       "status": 1, // 0: PK 匹配中; 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 结束
 *       "start_time": 16414300000000, // PK 开始时间, 单位毫秒
 *       "duration": 500000, // 距离 PK 结束或惩罚结束等待总时长, 单位毫秒
 *       "remain_duration": 200, // 距离 PK 结束、匹配超时、惩罚结束倒计时, 单位毫秒
 *       "fighters": [
 *         {
 *           "room_id": 1,
 *           "creator_id": 1,
 *           "score": 0,
 *           "name": "room1",
 *           "creator_username": "name1",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *         },
 *         {
 *           "room_id": 2,
 *           "creator_id": 2,
 *           "score": 0,
 *           "name": "room2",
 *           "creator_username": "name2",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKMatchStart(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		RoomID int64 `form:"room_id" json:"room_id"`
	}
	err := c.Bind(&params)
	if err != nil || params.RoomID < 0 {
		return nil, actionerrors.ErrParams
	}
	resp := pkMatchStartResp{roomID: params.RoomID}
	err = resp.checkRoomMatch(c)
	if err != nil {
		return nil, err
	}
	err = resp.insertPKPool()
	if err != nil {
		return nil, err
	}
	resp.sendDelayMessage()
	resp.buildResp()
	resp.changeRoomStatus()
	resp.broadcast()
	return &resp, nil
}

func (resp *pkMatchStartResp) checkRoomMatch(c *handler.Context) error {
	// 检查直播间状态
	var err error
	resp.r, err = room.FindOne(bson.M{"creator_id": c.UserID()}, &room.FindOptions{FindCreator: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	equip := c.Equip()
	if !equip.IsAppOlderThan("4.7.9", "5.6.7") && resp.roomID != resp.r.RoomID {
		// WORKAROUND: 安卓 >= 5.6.7, iOS >= 4.7.9, 需要传递 room_id
		return actionerrors.ErrForbidden
	}
	if resp.r.Status.Open == room.StatusOpenFalse {
		return actionerrors.ErrClosedRoom
	}
	resp.roomID = resp.r.RoomID
	key := keys.LockRoomPKStartMatch1.Format(resp.roomID)
	// 防止用户操作过快发起多个匹配
	ok, err := service.Redis.SetNX(key, 1, 2*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("操作频繁，请稍后再试")
	}

	// 检查是否在匹配中
	pool, err := livepk.FindWaitingPKByRoomID(resp.r.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pool != nil {
		return actionerrors.NewErrLiveForbidden("PK 进行中无法重复匹配！")
	}
	pk, err := livepk.FindCurrentPKRecord(resp.r.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pk != nil {
		return actionerrors.NewErrLiveForbidden("PK 进行中无法重复匹配！")
	}
	err = connectcheck.NewPKComponent(resp.r, nil).Check()
	if err != nil {
		return err
	}
	// 检查是否在冷静期
	count, err := service.Redis.Exists(keys.LockRoomPKEscapePunishment1.Format(resp.r.RoomID)).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if count > 0 {
		return actionerrors.NewErrLiveForbidden("由于多次提前结束 PK，您暂时无法参与 PK 玩法")
	}
	// 检查是否超过了高峰时段的 PK 次数限制
	exceed, err := livepk.ExceedPKPeakLimit(resp.roomID, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if exceed {
		return errExceedPKPeakLimit(true)
	}
	return nil
}

func (resp *pkMatchStartResp) insertPKPool() (err error) {
	revenue, err := livelog.FindTotalRevenueInLast30Days(resp.r.RoomID)
	if err != nil {
		logger.WithField("room_id", resp.r.RoomID).Error(err)
		// PASS
	}

	resp.pool, err = livepk.InsertRandomPool(resp.r.RoomID, resp.r.CreatorID, livepk.RoomLevel(revenue))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return
}

func (resp *pkMatchStartResp) buildResp() {
	resp.MatchStatus = resp.pool.Status
	resp.Duration = livepk.PKMatchDuration.Milliseconds()
	resp.RemainDuration = livepk.PKMatchDuration.Milliseconds()
	resp.CreateTime = resp.pool.CreateTime
}

func (resp *pkMatchStartResp) changeRoomStatus() {
	_, err := room.UpdateOneRoom(bson.M{"room_id": resp.r.RoomID}, bson.M{"status.pk": room.PKStatusOngoing})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

type matchStartNotify struct {
	Type   string            `json:"type"`
	Event  string            `json:"event"`
	RoomID int64             `json:"room_id"`
	PK     *pkMatchStartResp `json:"pk"`
	Room   struct {
		RoomID          int64  `json:"room_id"`
		CreatorID       int64  `json:"creator_id"`
		CreatorUsername string `json:"creator_username"`
		CreatorIconURL  string `json:"creator_iconurl"`
	} `json:"room"`
}

func (resp *pkMatchStartResp) sendDelayMessage() {
	livepk.DelayPKMatchingStart(resp.pool)
	livepk.DelayPKMatchTimeout(resp.pool)
}

func (resp *pkMatchStartResp) broadcast() {
	notify := matchStartNotify{
		Type:   liveim.TypePK,
		Event:  liveim.EventPKMatchStart,
		RoomID: resp.r.RoomID,
		PK:     resp,
	}
	notify.Room.RoomID = resp.r.RoomID
	notify.Room.CreatorID = resp.r.CreatorID
	notify.Room.CreatorUsername = resp.r.CreatorUsername
	notify.Room.CreatorIconURL = resp.r.CreatorIconURL

	err := userapi.Broadcast(resp.r.RoomID, notify)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// ActionPKMatchCancel 取消 PK 匹配
/**
 * @api {post} /api/v2/chatroom/pk/match/cancel 取消 PK 匹配
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 退出 PK 匹配房间内消息
 *   {
 *     "type": "pk",
 *     "event": "match_stop",
 *     "room_id": 123456,
 *     "pk": {
 *       "match_status": 1, // PK 状态, 0: 匹配中; 1: 退出匹配
 *       "create_time": 1584808200
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKMatchCancel(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		RoomID int64 `form:"room_id" json:"room_id"`
	}
	err := c.Bind(&params)
	if err != nil || params.RoomID < 0 {
		return nil, actionerrors.ErrParams
	}

	r, err := room.FindOne(bson.M{"creator_id": c.UserID()}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	equip := c.Equip()
	if !equip.IsAppOlderThan("4.7.9", "5.6.7") && r.RoomID != params.RoomID {
		// WORKAROUND: 安卓 >= 5.6.7, iOS >= 4.7.9, 需要传递 room_id
		return nil, actionerrors.ErrForbidden
	}
	pool, err := livepk.FindWaitingPKByRoomID(r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if pool == nil {
		return nil, actionerrors.NewErrLiveForbidden("当前没有匹配中的 PK")
	}
	ok, err := livepk.SetWaitPoolStatusByOID(pool.OID, livepk.PKPoolStatusCancel)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrLiveForbidden("当前没有匹配中的 PK")
	}
	err = room.UnsetRoomPKStatus([]int64{r.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	notify := map[string]interface{}{
		"type":    liveim.TypePK,
		"event":   liveim.EventPKMatchStop,
		"room_id": r.RoomID,
		"pk": map[string]interface{}{
			"match_status": livepk.PKPoolStatusCancel,
			"create_time":  pool.CreateTime,
		},
	}
	err = userapi.Broadcast(r.RoomID, notify)
	if err != nil {
		logger.WithField("pool_id", pool.OID).Error(err)
		// PASS
	}
	return "success", nil
}

type pkCloseParam struct {
	RoomID int64  `form:"room_id" json:"room_id"`
	PKID   string `form:"pk_id" json:"pk_id"`
	Type   int    `form:"type" json:"type"`

	pkOID primitive.ObjectID
	r     *room.Room
	pk    *livepk.LivePK
}

const (
	typePKCloseOngoing = iota
	typePKCloseInPunish
	typePKCloseInConnect
)

// ActionPKClose 退出 PK
/**
 * @api {post} /api/v2/chatroom/pk/close 退出 PK
 * @apiDescription 主播直播间在 PK 中、惩罚期、连麦期退出 PK
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {String} pk_id PK ID
 * @apiParam {Number} type 类型 0: 主播在 PK 中逃跑; 1: 惩罚期中退出; 2: PK 结束后退出连麦
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket PK 中主动逃跑双方直播间内消息
 *   {
 *     "type": "pk",
 *     "event": "close",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "close_status": 1, // 退出 PK 时的 PK 状态，1: PK 进行中; 2: PK 惩罚期中; 3: PK 结束后的连麦期
 *       "result": 0, // 0: 输; 1: 胜利
 *       "effect_url": "*.mp4;*.png",
 *       "web_effect_url": "*.mp4;*.webm;*.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket PK 战况自然结束
 *   {
 *     "type": "pk",
 *     "event": "finish",
 *     "room_id": 1,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "status": 2, // 0: PK 匹配中; 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 结束
 *       "close_status": 1, // 取消 PK 时的 PK 状态，1: PK 进行中; 2: PK 惩罚期中; 3: PK 结束后的连麦期
 *       "start_time": 1641430000000, // PK 开始时间, 单位毫秒
 *       "remain_duration": 200, // 距离 PK 惩罚结束倒计时, 单位毫秒,
 *       "duration": 60000, // PK 惩罚期总时长, 单位毫秒
 *       "fighters": [ // 第一位是本房间主播信息
 *         {
 *           "room_id": 1,
 *           "creator_id": 1,
 *           "score": 1,
 *           "name": "room1",
 *           "creator_username": "name1",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png",
 *           "top_fans": [ // 粉丝前三排行榜
 *             {
 *               "user_id": 56,
 *               "username": "test56",
 *               "iconurl": "http://aaa.bbb.ccc/test56.png"
 *             }
 *           ]
 *         },
 *         {
 *           "room_id": 2,
 *           "creator_id": 2,
 *           "score": 2,
 *           "name": "room1",
 *           "creator_username": "name1",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png",
 *           "top_fans": [
 *             {
 *               "user_id": 57,
 *               "username": "test57",
 *               "iconurl": "http://aaa.bbb.ccc/test57.png"
 *             }
 *           ]
 *         }
 *       ]
 *       "result": 0, // 0: 输; 1: 胜利, 2: 平局
 *       "effect_url": "*.mp4;*.png",
 *       "web_effect_url": "*.mp4;*.webm;*.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket PK 惩罚期结束
 *   {
 *     "type": "pk",
 *     "event": "punish_finish",
 *     "room_id": 1,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "status": 3, // 0: PK 匹配中; 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 结束后的连麦期
 *       "close_status": 2, // 取消 PK 时的 PK 状态，1: PK 进行中; 2: PK 惩罚期中; 3: PK 结束后的连麦期
 *       "start_time": 1641430000000, // PK 开始时间, 单位毫秒
 *       "fighters": [ // 第一位是本房间主播信息
 *         {
 *           "room_id": 1,
 *           "creator_id": 1,
 *           "score": 1,
 *           "name": "room1",
 *           "creator_username": "name1",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *         },
 *         {
 *           "room_id": 2,
 *           "creator_id": 2,
 *           "score": 2,
 *           "name": "room1",
 *           "creator_username": "name1",
 *           "creator_iconurl": "http://aaa.bbb.ccc/test56.png"
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 惩罚期败者主动逃跑（关播）, 仅未逃跑方直播间有消息
 *   {
 *     "type": "pk",
 *     "event": "close",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "close_status": 2, // 退出 PK 时的 PK 状态，1: PK 进行中; 2: PK 惩罚期中; 3: PK 结束后的连麦期; 4 结束
 *       "forced_close": 1, // 0: 结束发起方, 1: 被动结束方
 *       "result": 1, // 0: 输; 1: 胜利
 *       "effect_url": "*.mp4;*.png",
 *       "web_effect_url": "*.mp4;*.webm;*.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 惩罚期胜者或平局主动结束, 双方直播间有消息
 *   {
 *     "type": "pk",
 *     "event": "close",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "close_status": 2, // 退出 PK 时的 PK 状态，1: PK 进行中; 2: PK 惩罚期中; 3: PK 结束后的连麦期
 *       "forced_close": 1, // 0: 结束发起方, 1: 被动结束方
 *       "result": 0, // 0: 输; 1: 胜利
 *       "effect_url": "*.mp4;*.png",
 *       "web_effect_url": "*.mp4;*.webm;*.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 连麦期结束, 双方直播间有消息
 *   {
 *     "type": "pk",
 *     "event": "close",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "close_status": 3, // 退出 PK 时的 PK 状态，1: PK 进行中; 2: PK 惩罚期中; 3: PK 结束后的连麦期
 *       "forced_close": 1 // 0: 结束发起方, 1: 被动结束方
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKClose(c *handler.Context) (handler.ActionResponse, error) {
	var p pkCloseParam
	err := p.load(c)
	if err != nil {
		return nil, err
	}
	switch p.Type {
	case typePKCloseOngoing:
		if err = p.closeOngoingPK(); err != nil {
			return nil, err
		}
	case typePKCloseInPunish:
		if err = p.closePunishmentPK(); err != nil {
			return nil, err
		}
	case typePKCloseInConnect:
		if err = p.closeConnectPK(); err != nil {
			return nil, err
		}
	default:
		return nil, actionerrors.ErrParams
	}

	return "success", nil
}

// TODO: 加锁，避免两直播间同时操作
func (p *pkCloseParam) load(c *handler.Context) error {
	err := c.Bind(&p)
	if err != nil || p.RoomID < 0 {
		return actionerrors.ErrParams
	}
	p.pkOID, err = primitive.ObjectIDFromHex(p.PKID)
	if err != nil {
		return actionerrors.ErrParams
	}
	p.r, err = room.FindOne(bson.M{"creator_id": c.UserID()}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	equip := c.Equip()
	if !equip.IsAppOlderThan("4.7.9", "5.6.7") && p.r.RoomID != p.RoomID {
		// WORKAROUND: 安卓 >= 5.6.7, iOS >= 4.7.9, 需要传递 room_id
		return actionerrors.ErrForbidden
	}
	p.pk, err = livepk.FindOne(bson.M{
		"_id":              p.pkOID,
		"fighters.room_id": p.r.RoomID,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.pk == nil {
		return actionerrors.ErrCannotFindResource
	}
	p.pk.ReorderFightersByRoomID(p.r.RoomID)
	return nil
}

func (p *pkCloseParam) closeOngoingPK() error {
	if p.pk.Status != livepk.PKRecordStatusFighting {
		return actionerrors.ErrCannotFindResource
	}
	ok, err := p.pk.ClosePK()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrCannotFindResource
	}
	if p.pk.Type == livepk.PKTypeRandom {
		// 仅随机 PK 处理惩罚冷静期
		pass, err := livepk.CheckRunawayPunishment(p.r.RoomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !pass {
			err = service.Redis.Set(keys.LockRoomPKEscapePunishment1.Format(p.r.RoomID), 1, livepk.PKRunawayPunishmentDuration).Err()
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
		}
	}
	err = room.AfterPKConnectFinish(p.pk.PKRoomIDs())
	if err != nil {
		logger.WithFields(logger.Fields{"pk_id": p.pk.OID}).Error(err)
		// PASS
	}
	notifyBroadcast(p.pk.ClosePKNotifies(livepk.PKRecordStatusFighting))
	return nil
}

func notifyBroadcast(elems []*userapi.BroadcastElem) {
	err := userapi.BroadcastMany(elems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *pkCloseParam) closePunishmentPK() error {
	if p.pk.Status != livepk.PKRecordStatusPunishment {
		return actionerrors.ErrCannotFindResource
	}

	ok, err := p.pk.ClosePK()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrCannotFindResource
	}
	err = room.AfterPKConnectFinish(p.pk.PKRoomIDs())
	if err != nil {
		logger.WithFields(logger.Fields{"pk_id": p.pk.OID}).Error(err)
		// PASS
	}
	notifyBroadcast(p.pk.ClosePKNotifies(livepk.PKRecordStatusPunishment))
	return nil
}

func (p *pkCloseParam) closeConnectPK() error {
	if p.pk.Status != livepk.PKRecordStatusConnect {
		return actionerrors.ErrCannotFindResource
	}
	ok, err := p.pk.ClosePK()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.ErrCannotFindResource
	}
	err = room.AfterPKConnectFinish(p.pk.PKRoomIDs())
	if err != nil {
		logger.WithFields(logger.Fields{"pk_id": p.pk.OID}).Error(err)
		// PASS
	}
	notifyBroadcast(p.pk.ClosePKNotifies(livepk.PKRecordStatusConnect))
	return nil
}

type pkMuteParam struct {
	PKID   string `json:"pk_id" form:"pk_id"`
	RoomID int64  `json:"room_id" form:"room_id"`

	mute bool // 是否静音对方, true: 静音; false: 取消静音
	pkID primitive.ObjectID
	pk   *livepk.LivePK
}

type pkMutePayload struct {
	Type   string `json:"type"`
	Event  string `json:"event"`
	RoomID int64  `json:"room_id"`
	PK     struct {
		PKID       string `json:"pk_id"`
		Mute       int    `json:"mute"`
		ForcedMute int    `json:"forced_mute"`
	} `json:"pk"`
}

// ActionPKMute 直播间 PK 静音对方主播
/**
 * @api {post} /api/v2/chatroom/pk/mute 直播间 PK 静音对方主播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 发起方直播间 ID
 * @apiParam {String} pk_id PK ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 静音发起方直播间消息
 *   {
 *     "type": "pk",
 *     "event": "mute",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "mute": 1, // 当前直播间静音对方，0: 未静音对方; 1: 已静音对方
 *       "forced_mute": 0 // 当前直播间是否被对方静音，0: 未被静音; 1: 被静音
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 被静音方直播间消息
 *   {
 *     "type": "pk",
 *     "event": "forced_mute",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "mute": 0, // 当前直播间静音对方，0: 未静音对方; 1: 已静音对方
 *       "forced_mute": 1 // 当前直播间是否被对方静音，0: 未被静音; 1: 被静音
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKMute(c *handler.Context) (handler.ActionResponse, error) {
	param := &pkMuteParam{
		mute: true,
	}
	if err := param.load(c); err != nil {
		return nil, err
	}
	if err := param.findAndSetMute(); err != nil {
		return nil, err
	}
	param.broadcast()
	return "success", nil
}

// ActionPKUnmute 直播间 PK 取消对手静音
/**
 * @api {post} /api/v2/chatroom/pk/unmute 直播间 PK 取消对手静音
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 发起方直播间 ID
 * @apiParam {String} pk_id PK ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 取消静音发起方直播间消息
 *   {
 *     "type": "pk",
 *     "event": "unmute",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "mute": 0, // 当前直播间静音对方，0: 未静音对方; 1: 已静音对方
 *       "forced_mute": 1 // 当前直播间是否被对方静音，0: 未被静音; 1: 被静音
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 被取消静音方直播间消息
 *   {
 *     "type": "pk",
 *     "event": "forced_unmute",
 *     "room_id": 123456,
 *     "pk": {
 *       "pk_id": "60bdd76afac460c49cdd7605",
 *       "mute": 1, // 当前直播间静音对方，0: 未静音对方; 1: 已静音对方
 *       "forced_mute": 0 // 当前直播间是否被对方静音，0: 未被静音; 1: 被静音
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKUnmute(c *handler.Context) (handler.ActionResponse, error) {
	param := &pkMuteParam{
		mute: false,
	}
	if err := param.load(c); err != nil {
		return nil, err
	}
	if err := param.findAndSetMute(); err != nil {
		return nil, err
	}
	param.broadcast()
	return "success", nil
}

func (param *pkMuteParam) load(c *handler.Context) error {
	err := c.Bind(param)
	if err != nil {
		return actionerrors.ErrParams
	}
	param.pkID, err = primitive.ObjectIDFromHex(param.PKID)
	if err != nil {
		return actionerrors.ErrParams
	}
	return nil
}

func (param *pkMuteParam) findAndSetMute() error {
	var err error
	param.pk, err = livepk.SetFighterMute(param.pkID, param.RoomID, param.mute)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.pk == nil {
		return actionerrors.ErrNotFound("未查询到 PK 记录")
	}
	return nil
}

func (param *pkMuteParam) broadcast() {
	messages := make([]*userapi.BroadcastElem, 0, 2)
	for i, f := range param.pk.Fighters {
		payload := &pkMutePayload{
			Type:   liveim.TypePK,
			RoomID: f.RoomID,
		}
		payload.PK.PKID = param.PKID
		payload.PK.Mute = f.Mute
		payload.PK.ForcedMute = param.pk.Fighters[i^1].Mute // 判断当前直播间是否被对方静音; 例: 0^1 == 1, 1^1 == 0
		payload.Event = param.muteEvent(f.RoomID)
		messages = append(messages, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  f.RoomID,
			Payload: payload,
		})
	}
	if err := userapi.BroadcastMany(messages); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *pkMuteParam) muteEvent(roomID int64) string {
	var event string
	switch {
	case param.mute && roomID == param.RoomID:
		event = liveim.EventPKMute // 静音 && 是本房直播间
	case param.mute && roomID != param.RoomID:
		event = liveim.EventPKForcedMute // 静音 && 非本房直播间
	case !param.mute && roomID == param.RoomID:
		event = liveim.EventPKUnmute // 取消静音 && 是本房直播间
	case !param.mute && roomID != param.RoomID:
		event = liveim.EventPKForcedUnmute // 取消静音 && 非本房直播间
	}
	return event
}

type assistsListParams struct {
	pkOID  primitive.ObjectID
	roomID int64

	data []*roomsrank.Info // 助攻榜
}

// ActionPKAssistsList PK 助攻榜
/**
 * @api {get} /api/v2/chatroom/pk/assists/list PK 助攻榜
 * @apiDescription PK 助攻榜
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {String} pk_id PK ID
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "rank": 1,
 *           "revenue": 111, // 贡献分数
 *           "user_id": 3457114,
 *           "username": "青汉",
 *           "iconurl": "http://static.missevan.com/avatar/icon01.png",
 *           "rank_invisible": false,
 *           "titles": [{
 *             "type": "level",
 *             "level": 120
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4,
 *             "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png",
 *             "super_fan": {
 *               "expire_time": 1576116700
 *             }
 *           }, {
 *             "type": "noble",
 *             "name": "新秀",
 *             "level": 2
 *           }, {
 *             "type": "highness",
 *             "name": "上神",
 *             "level": 1
 *           }, {
 *             "type": "badge",
 *             "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *             "appearance_id": 1
 *           }, {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }]
 *         },
 *         {
 *           "rank": 2,
 *           "revenue": 100,
 *           "user_id": 3457119,
 *           "username": "青汉",
 *           "iconurl": "http://static.missevan.com/avatar/icon01.png",
 *           "rank_invisible": false,
 *           "titles": []
 *         },
 *         {
 *           "rank": 3,
 *           "revenue": 99,
 *           "user_id": 3457110,
 *           "username": "青汉",
 *           "iconurl": "http://static.missevan.com/avatar/icon01.png",
 *           "rank_invisible": false,
 *           "titles": []
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKAssistsList(c *handler.Context) (handler.ActionResponse, error) {
	params := new(assistsListParams)
	if err := params.load(c); err != nil {
		return nil, err
	}
	if err := params.findAssistsList(); err != nil {
		return nil, err
	}
	return handler.M{
		"data": params.data,
	}, nil
}

func (p *assistsListParams) load(c *handler.Context) error {
	pkID, _ := c.GetParamString("pk_id")
	if pkID == "" {
		return actionerrors.ErrParams
	}
	var err error
	p.pkOID, err = primitive.ObjectIDFromHex(pkID)
	if err != nil {
		return actionerrors.ErrParams
	}
	p.roomID, _ = c.GetParamInt64("room_id")
	if p.roomID <= 0 {
		return actionerrors.ErrParams
	}
	return nil
}

func (p *assistsListParams) findAssistsList() error {
	count := roomsrank.RankLen(roomsrank.RankTypePK)
	p.data = make([]*roomsrank.Info, 0, count)
	userIDs := make([]int64, 0, count)
	key := roomsrank.PKKey(p.roomID, p.pkOID)
	cmd := service.Redis.ZRevRangeWithScores(key, 0, count-1)
	if err := cmd.Err(); err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i, val := range cmd.Val() {
		userID, err := strconv.ParseInt(val.Member.(string), 10, 64)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		userIDs = append(userIDs, userID)
		info := &roomsrank.Info{
			Rank:    int64(i + 1),
			Revenue: int64(val.Score),
			Simple: &liveuser.Simple{
				UID: userID,
			},
		}
		p.data = append(p.data, info)
	}
	if len(userIDs) == 0 {
		return nil
	}

	userMap, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(
		bson.M{"user_id": bson.M{"$in": userIDs}}, &liveuser.FindOptions{FindTitles: true}))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, info := range p.data {
		user := userMap[info.UserID()]
		if user == nil {
			continue
		}
		info.Username = user.Username
		info.IconURL = user.IconURL
		info.Titles = user.Titles
	}
	return nil
}

// maxPKRecordCount PK 记录最大阈值
const maxPKRecordCount = 100

// Statistics PK 直播间统计数据
// TODO: 后续需要和 search.go 文件下的 statistics 整合在一起使用
type pkRoomStatistics struct {
	Score int64 `json:"score"` // 热度
}

// fighter 直播间两个主播 PK 详情
type fighter struct {
	RoomID          int64                  `bson:"room_id" json:"room_id"`        // 房间 ID
	CreatorID       int64                  `bson:"creator_id" json:"creator_id"`  // 房主 ID
	Score           int64                  `bson:"score" json:"score"`            // PK 总积分
	Status          *room.SimpleRoomStatus `bson:"-" json:"status,omitempty"`     // 直播间状态
	Statistics      *pkRoomStatistics      `bson:"-" json:"statistics,omitempty"` // 直播间统计数据
	CreatorUsername string                 `bson:"-" json:"creator_username"`     // 房主昵称
	CreatorIconURL  string                 `bson:"-" json:"creator_iconurl"`      // 房主头像
}

// pkRecordItem PK 记录信息列表
type pkRecordItem struct {
	OID          primitive.ObjectID `bson:"_id" json:"pk_id"`                  // ID
	Type         int                `bson:"type" json:"type"`                  // PK 类型
	Fighters     [2]*fighter        `bson:"fighters" json:"fighters"`          // PK 主播详情
	StartTime    int64              `bson:"start_time" json:"start_time"`      // PK 开始时间
	WinnerRoomID int64              `bson:"winner_room_id,omitempty" json:"-"` // PK 胜利的直播间，平局值为 0
	Result       int                `bson:"-" json:"result"`                   // PK 结果
}

// livePKSimpleList
type livePKSimpleList struct {
	Pagination goutil.Pagination `json:"pagination"`
	Data       []*pkRecordItem   `json:"data"`
}

// recordListParam 查询 PK 记录参数
type recordListParam struct {
	roomID   int64
	p        int64
	pageSize int64

	room *room.Room

	*livePKSimpleList
}

// ActionRecordList PK 记录列表
/**
 * @api {get} /api/v2/chatroom/pk/record/list PK 记录列表
 * @apiDescription 查询主播自己的 PK 记录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "pk_id": "61f2447b81d2cba5ec3464d4",
 *           "type": 1, // 0: 随机 PK; 1: 指定 PK; 2: 排位赛
 *           // fighters[0] 是房主信息，fighters[1] 是对手信息
 *           "fighters": [
 *             {
 *               "room_id": 100861,
 *               "creator_id": 10086,
 *               "score": 10,
 *               "creator_username": "小傻子",
 *               "creator_iconurl": "http://static.example.com/creator.png"
 *             },
 *             {
 *               "room_id": 100862,
 *               "creator_id": 10086,
 *               "score": 10,
 *               "status": { // 自己房间无 status 字段
 *                 "open": 1 // 开播状态: 1 开播；0 关播
 *               },
 *               "statistics": { // 自己房间无 statistics 字段
 *                 "score": 10 // 热度值
 *               },
 *               "creator_username": "二傻子",
 *               "creator_iconurl": "http://static.example.com/creator.png"
 *             }
 *           ],
 *           "start_time": 1645065421208, // 开始时间，单位毫秒
 *           "result": 0 // 结果相对于 fighters[0]：0-PK 失败，1-PK 胜利，2-PK 平局
 *         }
 *       ],
 *       "pagination": {
 *         "p": 2,
 *         "pagesize": 20,
 *         "count": 46,
 *         "maxpage": 3
 *       }
 *     }
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 501010000,
 *     "info": "参数错误"
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 501010003,
 *     "info": "您无权执行该操作"
 *   }
 *
 * @apiErrorExample Error-Response:
 *   {
 *     "code": 100010500,
 *     "info": "服务器内部错误"
 *   }
 */
func ActionRecordList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPkRecordListParam(c)
	if err != nil {
		return nil, err
	}

	err = param.findFinishedPKRecord()
	if err != nil {
		return nil, err
	}

	// 格式化记录列表
	param.formatPKRecord(c)

	return param.livePKSimpleList, nil
}

// newPkRecordListParam 初始参数
func newPkRecordListParam(c *handler.Context) (*recordListParam, error) {
	param := recordListParam{
		livePKSimpleList: new(livePKSimpleList),
	}
	var err error

	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	param.room, err = room.FindOne(bson.M{"creator_id": c.UserID()},
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	param.roomID, _ = c.GetParamInt64("room_id")
	if param.room.RoomID != param.roomID {
		return nil, actionerrors.ErrForbidden
	}

	return &param, nil
}

// findFinishedPKRecord 查找已完成的 PK 记录
func (param *recordListParam) findFinishedPKRecord() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{
		"fighters.room_id": param.room.RoomID,
		"status":           livepk.PKRecordStatusFinished,
	}
	// 获得符合条件的记录数
	count, err := livepk.PKCollection().CountDocuments(ctx, filter)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 限制最大记录条数
	if count > maxPKRecordCount {
		count = maxPKRecordCount
	}

	// 获得最大页数并初始化 Pagination
	param.Pagination = goutil.MakePagination(count, param.p, param.pageSize)
	// 参数合规性检查
	if !param.Pagination.Valid() {
		param.livePKSimpleList.Data = make([]*pkRecordItem, 0)
		return nil
	}

	// 设置 mongodb findOptions
	findOptions := param.Pagination.SetFindOptions(options.Find().
		SetProjection(mongodb.NewProjection("_id, type, fighters, start_time, winner_room_id")).
		SetSort(bson.M{
			"start_time": -1,
		}))
	cur, err := livepk.PKCollection().Find(ctx, filter, findOptions)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	defer cur.Close(ctx)
	err = cur.All(ctx, &param.livePKSimpleList.Data)
	if err != nil {
		return err
	}

	return nil
}

// formatPKRecord 格式化 PK 列表
func (param *recordListParam) formatPKRecord(c *handler.Context) {
	size := len(param.Data)
	if size == 0 {
		return
	}
	fighterCreatorIDs := make([]int64, 0, size)

	username := c.User().GetUsername()
	iconURL := c.User().GetIconURL()
	for _, d := range param.Data {
		if d.Fighters[0].RoomID != param.room.RoomID {
			d.Fighters[0], d.Fighters[1] = d.Fighters[1], d.Fighters[0]
		}

		d.Fighters[0].CreatorUsername = username
		d.Fighters[0].CreatorIconURL = iconURL

		fighterCreatorIDs = append(fighterCreatorIDs, d.Fighters[1].CreatorID)

		switch d.WinnerRoomID {
		case d.Fighters[0].RoomID:
			d.Result = livepk.PKResultWin
		case d.Fighters[1].RoomID:
			d.Result = livepk.PKResultLose
		default:
			d.Result = livepk.PKResultDraw
		}
	}

	roomSimpleMap, err := room.FindSimpleMapByCreatorID(fighterCreatorIDs, &room.FindOptions{FindCreator: true})
	if err != nil {
		logger.Error(err)
		return
	}

	for _, d := range param.Data {
		r := roomSimpleMap[d.Fighters[1].CreatorID]
		if r != nil {
			d.Fighters[1].Status = &room.SimpleRoomStatus{Open: r.Status.Open}
			d.Fighters[1].Statistics = &pkRoomStatistics{Score: r.Statistics.Score}
			d.Fighters[1].CreatorUsername = r.CreatorUsername
			d.Fighters[1].CreatorIconURL = r.CreatorIconURL
		}
	}
}
