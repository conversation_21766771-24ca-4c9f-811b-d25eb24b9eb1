package pk

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionPKSettingsGet PK 设置信息
/**
 * @api {get} /api/v2/chatroom/pk/settings/get PK 设置信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "disable_invite": 0, // 是否接受 PK 邀请；0: 接受, 1: 不接受
 *       "unfollowed_invite": 0 // 是否接受我未关注的主播的 PK 邀请；0: 不接受；1：接受
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 501010003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKSettingsGet(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.CreatorID != c.UserID() {
		return nil, actionerrors.ErrForbidden
	}
	settings, err := livemeta.FindPKSettings(roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if settings == nil {
		settings = new(livemeta.PKSettings)
	}
	return settings, nil
}

type pkInvitationSetParam struct {
	RoomID           int64 `form:"room_id" json:"room_id"`
	DisableInvite    *int  `form:"disable_invite" json:"disable_invite"`
	UnfollowedInvite *int  `form:"unfollowed_invite" json:"unfollowed_invite"`

	userID int64
}

// ActionPKSettingsSet PK 设置修改
/**
 * @api {post} /api/v2/chatroom/pk/settings/set PK 设置修改
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {number=0,1} [disable_invite] 是否接受 PK 邀请; 0: 接受, 1: 不接受
 * @apiParam {number=0,1} [unfollowed_invite] 是否接受我未关注的主播的 PK 邀请；0: 不接受；1: 接受
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "disable_invite": 0, // 是否接受 PK 邀请；0: 接受, 1: 不接受
 *       "unfollowed_invite": 0 // 是否接受我未关注的主播的 PK 邀请；0: 不接受；1：接受
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 501010003
 * @apiError (403) {String} info 用户没有权限
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionPKSettingsSet(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPKInvitationSetParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	settings, err := param.update()
	if err != nil {
		return nil, err
	}
	return settings, nil
}

func newPKInvitationSetParam(c *handler.Context) (*pkInvitationSetParam, error) {
	param := new(pkInvitationSetParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.DisableInvite == nil && param.UnfollowedInvite == nil {
		return nil, actionerrors.ErrParams
	}
	param.userID = c.UserID()
	return param, nil
}

func (param *pkInvitationSetParam) check() error {
	r, err := room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if r.CreatorID != param.userID {
		return actionerrors.ErrForbidden
	}

	pkSettings, err := livemeta.FindPKSettings(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if pkSettings == nil {
		return actionerrors.ErrNotFound("未查询到直播间信息")
	}
	if (param.DisableInvite == nil && pkSettings.DisableInvite == 1) ||
		(param.DisableInvite != nil && *param.DisableInvite == 1) {
		if param.UnfollowedInvite != nil && *param.UnfollowedInvite == 1 {
			return actionerrors.NewErrForbidden("已设置不接受 PK 邀请，无法设置接受未关注主播的 PK 邀请")
		}
	}
	return nil
}

func (param *pkInvitationSetParam) update() (*livemeta.PKSettings, error) {
	update := bson.M{}
	if param.DisableInvite != nil {
		update["pk_settings.disable_invite"] = *param.DisableInvite
		// 关闭接受 PK 邀请, 也需要关闭接受未关注主播的 PK 邀请
		if *param.DisableInvite == 1 {
			param.UnfollowedInvite = goutil.NewInt(0)
		}
	}
	if param.UnfollowedInvite != nil {
		update["pk_settings.unfollowed_invite"] = *param.UnfollowedInvite
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	settings := new(livemeta.LiveMeta)
	err := livemeta.Collection().FindOneAndUpdate(ctx,
		bson.M{"room_id": param.RoomID},
		bson.M{"$set": update},
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetProjection(bson.M{"pk_settings": 1}),
	).Decode(&settings)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return settings.PKSettings, nil
}
