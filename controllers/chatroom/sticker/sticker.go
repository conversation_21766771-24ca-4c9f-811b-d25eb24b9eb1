package sticker

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/messagelimit"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type tabsParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`

	c        *handler.Context
	r        *room.Room
	now      time.Time
	packages []*livesticker.Package
	versions map[int64]string
}

type tabsResp struct {
	Tabs []*tabItem `json:"tabs"`
}

type tabItem struct {
	PackageID    int64  `json:"package_id"`
	PackageName  string `json:"package_name"`
	Version      string `json:"version"`
	IconURL      string `json:"icon_url"`
	IconFrameURL string `json:"icon_frame_url,omitempty"`
	Locked       bool   `json:"locked,omitempty"`
	Tips         string `json:"tips,omitempty"`
	Toast        string `json:"toast,omitempty"`
}

// ActionStickerTabs 获取直播间表情包 tabs
/**
 * @api {get} /api/v2/chatroom/sticker/tabs 获取直播间表情包 tabs
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "tabs": [
 *         {
 *           "package_id": 1,
 *           "package_name": "超粉表情包",
 *           "version": "2f797b41a313e63c9ab090836becb9b6", // 用于校验当前表情包下贴图是否有变动
 *           "icon_url": "https://static-test.maoercdn.com/test/test.png",
 *           "icon_frame_url": "https://static-test.maoercdn.com/icon_frame.png", // 表情包图标框，下发该字段时需要对显示的图标（icon_url）进行裁剪
 *           "locked": true, // 是否解锁超粉表情包，true：未解锁；false：解锁
 *           "tips": "开通主播的超粉可使用 520 限定超粉表情哦~",
 *           "toast": "开通超粉才可使用哦" // 未解锁时，点击表情包弹出的提示
 *         },
 *         // ...
 *       ]
 *     }
 *   }
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionStickerTabs(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newTabsParam(c)
	if err != nil {
		return nil, err
	}
	err = param.tabs()
	if err != nil {
		return nil, err
	}
	return param.resp()
}

func newTabsParam(c *handler.Context) (*tabsParam, error) {
	var param *tabsParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.now = goutil.TimeNow()

	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.c = c
	return param, nil
}

func (param *tabsParam) tabs() (err error) {
	param.packages, err = livesticker.FindPackages(param.r.RoomID, param.c.UserID(), param.now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.packages) == 0 {
		return nil
	}
	packageIDs := make([]int64, 0, len(param.packages))
	for _, p := range param.packages {
		packageIDs = append(packageIDs, p.ID)
	}
	param.versions, err = livesticker.PackageVersion(packageIDs, param.now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *tabsParam) resp() (*tabsResp, error) {
	tabs := make([]*tabItem, 0, len(param.packages))
	for _, p := range param.packages {
		version, ok := param.versions[p.ID]
		if !ok || version == "" {
			continue
		}

		t := &tabItem{
			PackageID:   p.ID,
			PackageName: p.Name,
			Version:     version,
			IconURL:     p.Icon,
		}
		switch p.Type {
		case livesticker.TypeRoom:
			t.IconURL = param.r.CreatorIconURL
			t.IconFrameURL = storage.ParseSchemeURL(config.Conf.Params.Sticker.RoomIconFrame)
		case livesticker.TypeUser:
			t.IconURL = param.c.User().IconURL
			t.IconFrameURL = storage.ParseSchemeURL(config.Conf.Params.Sticker.UserIconFrame)
		case livesticker.TypeSuperFans:
			isSuperFan, err := livemedal.IsRoomSuperFan(param.r.RoomID, param.c.UserID())
			if err != nil {
				logger.Error(err)
				// PASS
			}
			if !isSuperFan {
				t.Locked = true
				t.Tips = "开通主播的超粉可使用 520 限定超粉表情哦~"
				t.Toast = "开通超粉才可使用哦"
			}
		}
		tabs = append(tabs, t)
	}
	return &tabsResp{
		Tabs: tabs,
	}, nil
}

type listParam struct {
	PackageID int64 `form:"package_id" json:"package_id"`
	RoomID    int64 `form:"room_id" json:"room_id"`

	c   *handler.Context
	now time.Time
	pkg *livesticker.Package
}

type listResp struct {
	PackageID   int64                      `json:"package_id"`
	PackageName string                     `json:"package_name"`
	Version     string                     `json:"version"`
	Stickers    []*livesticker.StickerInfo `json:"stickers"`
}

// ActionStickerList 获取直播间表情包下的所有贴图
/**
 * @api {get} /api/v2/chatroom/sticker/list 获取直播间表情包下的所有贴图
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} package_id 表情包 ID
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "package_id": 1,
 *       "package_name": "超粉专属表情",
 *       "version": "2f797b41a313e63c9ab090836becb9b6",
 *       "stickers": [
 *         {
 *           "sticker_id": 1,
 *           "name": "贴图名称",
 *           "icon_url": "https://static-test.maoercdn.com/icon.png", // 贴图预览图片地址
 *           "image_url": "https://static-test.maoercdn.com/image.webp", // 贴图图片地址
 *           "intro": "来源" // 贴图来源描述，比如：180 级个人专属
 *         },
 *         // ...
 *       ]
 *     }
 *   }
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionStickerList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newListParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	return param.resp()
}

func newListParam(c *handler.Context) (*listParam, error) {
	var param *listParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.PackageID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	param.now = goutil.TimeNow()

	return param, nil
}

func (param *listParam) check() error {
	r, err := room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	param.pkg, err = livesticker.FindPackage(param.PackageID)
	if err != nil {
		return err
	}
	if param.pkg == nil {
		return actionerrors.ErrNotFound("表情包不存在")
	}
	isOwner, err := param.pkg.IsOwner(param.RoomID, param.c.UserID(), param.now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !isOwner {
		return actionerrors.NewErrForbidden("暂时无法使用该表情")
	}
	return nil
}

func (param *listParam) resp() (*listResp, error) {
	stickers, version, err := param.pkg.Stickers(param.now)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	list := make([]*livesticker.StickerInfo, 0, len(stickers))
	for _, s := range stickers {
		list = append(list, s.NewStickerInfo(nil))
	}

	return &listResp{
		PackageID:   param.pkg.ID,
		PackageName: param.pkg.Name,
		Version:     version,
		Stickers:    list,
	}, nil
}

type sendParam struct {
	StickerID int64 `form:"sticker_id" json:"sticker_id"`
	PackageID int64 `form:"package_id" json:"package_id"`
	RoomID    int64 `form:"room_id" json:"room_id"`

	c   *handler.Context
	r   *room.Room
	u   *liveuser.Simple
	pkg *livesticker.Package
	s   *livesticker.LiveSticker
}

type sendMsg struct {
	Type    string                   `json:"type"`
	Event   string                   `json:"event"`
	RoomID  int64                    `json:"room_id"`
	MsgID   string                   `json:"msg_id"`
	Message string                   `json:"message"`
	User    *liveuser.Simple         `json:"user"`
	Sticker *livesticker.StickerInfo `json:"sticker"`
}

// ActionStickerSend 发送贴图
/**
 * @api {post} /api/v2/chatroom/sticker/send 发送贴图
 * @apiDescription 本地可以优先把贴图显示出来，不用等 WS 或者接口返回；若不支持本地将贴图添加到消息列表，则优先显示最先下发的消息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} sticker_id 贴图 ID
 * @apiParam {Number} package_id 表情包 ID
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "type": "message",
 *       "event": "sticker",
 *       "room_id": 1,
 *       "msg_id": "3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
 *       "message": "[贴图名称]",
 *       "user": {
 *         "user_id": 10000000052,
 *         "username": "jesse_tang",
 *         "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#f45b41"
 *           },
 *           {
 *             "type": "level",
 *             "level": 30
 *           },
 *           {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }
 *         ]
 *       },
 *       "sticker": {
 *         "package_id": 1,
 *         "package_name": "表情包名称",
 *         "sticker_id": 1,
 *         "name": "贴图名称",
 *         "icon_url": "http://static-test.maoercdn.com/avatars/icon.png",
 *         "image_url": "http://static-test.maoercdn.com/avatars/image.webp",
 *         "intro": "超粉专属" // 贴图来源描述
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 发送表情直播间 WS 消息
 *   {
 *     "type": "message",
 *     "event": "sticker",
 *     "room_id": 1,
 *     "msg_id": "3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
 *     "message": "[贴图名称]",
 *     "user": {
 *       "user_id": 10000000052,
 *       "username": "jesse_tang",
 *       "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 30
 *         },
 *         {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }
 *       ]
 *     },
 *     "sticker": {
 *       "package_id": 1,
 *       "package_name": "表情包名称",
 *       "sticker_id": 1,
 *       "name": "贴图名称",
 *       "icon_url": "http://static-test.maoercdn.com/avatars/icon.png",
 *       "image_url": "http://static-test.maoercdn.com/avatars/image.webp",
 *       "intro": "超粉专属" // 贴图来源描述
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 发送表情直播间连线互通 WS 消息
 *   {
 *     "type": "message",
 *     "event": "cross_sticker",
 *     "room_id": 1,
 *     "msg_id": "3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
 *     "message": "[贴图名称]",
 *     "user": {
 *       "user_id": 10000000052,
 *       "username": "jesse_tang",
 *       "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 30
 *         }
 *       ]
 *     },
 *     "sticker": {
 *       "package_id": 1,
 *       "package_name": "表情包名称",
 *       "sticker_id": 1,
 *       "name": "贴图名称",
 *       "icon_url": "http://static-test.maoercdn.com/avatars/icon.png",
 *       "image_url": "http://static-test.maoercdn.com/avatars/image.webp",
 *       "intro": "超粉专属" // 贴图来源描述
 *     },
 *     "room": { // 原始发消息的直播间的信息，举报时给的文案里的相关信息也需要使用这里的
 *       "room_id": 10659544,
 *       "creator_id": 10,
 *       "creator_iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "creator_username": "主播昵称"
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionStickerSend(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSendParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	msg, err := param.sendMsg()
	if err != nil {
		return nil, err
	}
	return msg, nil
}

func newSendParam(c *handler.Context) (*sendParam, error) {
	var param *sendParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.StickerID <= 0 || param.PackageID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param *sendParam) check() error {
	var err error
	param.r, err = room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if !param.r.IsOpen() {
		return actionerrors.ErrClosedRoom
	}
	param.u, err = liveuser.FindOneSimple(bson.M{"user_id": param.c.UserID()}, &liveuser.FindOptions{FindTitles: true, RoomID: param.r.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.u == nil {
		return actionerrors.ErrCannotFindUser
	}
	// NOTICE: 直播等级 < 165，3s 内仅可发送一次表情
	if param.u.Level() < 165 {
		ok, err := service.Redis.SetNX(keys.LockStickerSend1.Format(param.c.UserID()), 1, 3*time.Second).Result()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return actionerrors.NewErrForbidden("操作太快啦，稍后再试吧~")
		}
	}

	isRoomAdmin, err := livemembers.IsRoomAdmin(param.r.OID, param.c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	roomMedal, err := livemedal.FindOwnedMedal(param.c.UserID(), param.RoomID, livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	limit := utils.SpeakLimit{
		User:        param.u,
		Room:        param.r,
		IsRoomAdmin: isRoomAdmin,
		RoomMedal:   roomMedal,
	}
	err = limit.Check()
	if err != nil {
		return err
	}
	// 是否被禁言
	mute, err := livemembers.IsMute(param.c.UserID(), param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if mute.GlobalMute {
		return actionerrors.ErrGlobalMuteUser
	}
	if mute.RoomMute {
		return actionerrors.NewErrForbidden("您已被禁言，无法在本直播间发送表情")
	}
	// 是否被封禁
	ban, err := userstatus.FindBanned(param.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if ban != nil {
		return actionerrors.NewErrForbidden("当前用户已被封禁")
	}
	// 被主播拉黑，无法发送表情
	blocked, err := blocklist.IsBlocked(param.r.CreatorID, param.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
	}
	param.pkg, err = livesticker.FindPackage(param.PackageID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.pkg == nil {
		return actionerrors.ErrNotFound("表情包不存在")
	}
	if param.pkg.Type == livesticker.TypeSuperFans {
		// 是否是超粉
		isSuperFan, err := livemedal.IsRoomSuperFan(param.RoomID, param.c.UserID())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !isSuperFan {
			return actionerrors.NewErrForbidden("开通超粉才可使用哦")
		}
	}
	param.s, err = param.pkg.Sticker(param.StickerID, param.r.RoomID, param.u.UserID(), goutil.TimeNow())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.s == nil {
		return actionerrors.ErrNotFound("暂时无法使用该表情")
	}
	return nil
}

func (param *sendParam) sendMsg() (*sendMsg, error) {
	msgID := util.BuildMsgID(param.c.UserID(), param.r.RoomID, param.s.Message(),
		config.Conf.Params.General.FeedbackKey, param.c.Equip().OS)
	msg := &sendMsg{
		Type:    liveim.TypeMessage,
		Event:   liveim.EventSticker,
		RoomID:  param.r.RoomID,
		MsgID:   msgID,
		Message: param.s.Message(),
		User:    param.u,
		Sticker: param.s.NewStickerInfo(param.pkg),
	}

	goutil.Go(func() {
		param.incrRoomMsgCount()
		param.incrUserMsgCount()
		param.addRevenue()
	})

	status := messagelimit.Status(param.r, param.u, param.s.SimulateMessage())
	switch status {
	case models.MessageStatusIgnore:
		// 消息不广播，也不存入数据库，但接口正常返回
		return msg, nil
	case models.MessageStatusFaked:
		// PASS
	case models.MessageStatusNormal:
		err := userapi.Broadcast(param.r.RoomID, msg)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	case models.MessageStatusLimited:
		// 不记录大主播直播间超限后的消息，缓解活动压力
		if param.r.IsSpecialCreator() {
			return msg, nil
		}
	default:
		logger.WithFields(logger.Fields{
			"status":  status,
			"room_id": param.r.RoomID,
			"user_id": param.u.UserID(),
		}).Error("表情包发送频率限制状态异常")
		// PASS
		return msg, nil
	}

	message := &models.Message{
		RoomOID: param.r.OID,
		RoomID:  param.r.RoomID,
		MsgID:   msgID,
		Status:  status,
		UserID:  param.u.UserID(),
		Message: param.s.Message(),
		Sticker: &models.Sticker{
			PackageID: param.pkg.ID,
			StickerID: param.s.ID,
		},
		IP:         param.c.ClientIP(),
		CreateTime: goutil.TimeNow(),
	}
	_, err := message.Insert()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return msg, nil
}

func (param *sendParam) incrRoomMsgCount() {
	room.IncrMessageCount(param.r.RoomID)
}

func (param *sendParam) incrUserMsgCount() {
	msgCount := usermeta.IncrMessageCount(param.u.UserID())

	// 当日发三条消息，增加贡献值
	if msgCount == usermeta.DailyMsgCount {
		// TODO: 暂不需要使用贵族信息
		contributionParams := userstatus.NewAddContributionParams(param.u.UserID(), param.r.RoomID, param.u.Username, userstatus.FromNormal, nil)
		err := contributionParams.AddContribution(300)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

// 每日第一条消息增加榜单值，主播自己发送不增加榜单值
func (param *sendParam) addRevenue() {
	if param.r.IsOwner(param.u) {
		return
	}
	if !param.r.IsDailyFirstMsg(param.u.UserID()) {
		return
	}

	const score = 1
	err := roomsrank.AddRevenue(param.r.RoomID, param.u.UserID(), score, param.r.IsOpen())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}
