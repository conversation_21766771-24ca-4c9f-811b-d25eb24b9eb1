package chatroom

import (
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygiftdresses"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifteffects"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userdiygifts"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const testDiyGiftID int64 = 60006

var (
	testDiyGiftOnce      sync.Once
	testDiyGiftSendDress []diygifteffects.Dress
)

func initDiyGift() (err error) {
	testDiyGiftOnce.Do(func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()

		// gift
		_, err = gift.Collection().UpdateOne(ctx,
			bson.M{"gift_id": testDiyGiftID},
			bson.M{"$set": gift.Gift{
				GiftID:    testDiyGiftID,
				Type:      gift.TypeNormal,
				Order:     1,
				NameClean: "test diy gift",
				Price:     1,
				AddedTime: time.Unix(0, 0),
			}}, options.Update().SetUpsert(true))
		if err != nil {
			return
		}

		// diygift
		var diygift diygifts.DiyGift
		err = diygifts.Collection().FindOneAndUpdate(ctx,
			bson.M{"gift_id": testDiyGiftID},
			bson.M{"$set": diygifts.DiyGift{
				GiftID: testDiyGiftID,
				Avatars: &diygifts.Avatars{
					CreatorPreviewPosition: []int{1, 2, 3, 4},
					UserPreviewPosition:    []int{5, 6, 7, 8},
				},
				Words:      &diygifts.Words{TextColor: "#FFFFFF"},
				DressTypes: []diygifts.DressType{{Type: 1}, {Type: 2}},
				Status:     diygifts.StatusOn,
			}},
			options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
		).Decode(&diygift)
		if err != nil {
			return
		}

		// dress
		col := diygiftdresses.Collection()
		dresses := []*diygiftdresses.DiyGiftDress{
			{
				GiftID: testDiyGiftID,
				Type:   1,
				Order:  1,
			},
			{
				GiftID: testDiyGiftID,
				Type:   2,
				Order:  1,
			},
		}
		for i, d := range dresses {
			err = col.FindOneAndUpdate(ctx,
				bson.M{"gift_id": d.GiftID, "type": d.Type, "order": d.Order},
				bson.M{"$set": d},
				options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After)).
				Decode(&dresses[i])
			if err != nil {
				return
			}
		}

		testDiyGiftSendDress = []diygifteffects.Dress{
			{Type: dresses[0].Type, DressID: dresses[0].OID.Hex()},
			{Type: dresses[1].Type, DressID: dresses[1].OID.Hex()},
		}

		// effects
		_, err = diygifteffects.Collection().UpdateOne(ctx, bson.M{
			"gift_id":   testDiyGiftID,
			"unique_id": diygifteffects.UniqueID(testDiyGiftSendDress),
		},
			bson.M{"$set": bson.M{
				"status":     diygifteffects.StatusValid,
				"effect":     "oss://effect.mp4",
				"web_effect": "oss://effect-web.mp4",
			}},
			options.Update().SetUpsert(true),
		)
		if err != nil {
			return
		}
	})
	return
}

func TestGiftSendTagKeys(t *testing.T) {
	kcs := []*tutil.KeyChecker{
		tutil.NewKeyChecker(t, tutil.FORM),
		tutil.NewKeyChecker(t, tutil.JSON),
	}

	for i := range kcs {
		kcs[i].Check(diyGiftSendParam{}, "gift_id", "room_id", "from_room_id", "words", "avatars", "dress")
		kcs[i].Check(diyGiftSendAvatars{}, "creator", "user")
	}

	kcs[1].Check(giftSendResp{}, "user", "bubble", "balance", "combo")
}

func TestNewDiyGiftSendParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIGoUserBlocklist, func(input interface{}) (output interface{}, err error) {
		return handler.M{"block_list": []int64{}}, nil
	})()

	newC := func(body interface{}) *handler.Context {
		return handler.NewTestContext(http.MethodPost, "/diygift/send", true,
			body)
	}
	service.Cache5s.Set(
		keys.KeyOnlineGifts0.Format(),
		[]gift.Gift{{GiftID: 1, Type: gift.TypeRoomCustom, RoomID: openingRoomID, Order: 2}}, 0,
	)
	defer service.Cache5s.Flush()

	c := newC(handler.M{"room_id": -1})
	_, err := newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = newC(handler.M{"room_id": 1, "gift_id": -1})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = newC(handler.M{"room_id": 999, "gift_id": 1})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = newC(handler.M{"room_id": room.TestLimitedRoomID, "gift_id": 1})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.NewErrForbidden("本直播间内无法赠送该礼物"), err)

	c = newC(handler.M{"room_id": openingRoomID, "gift_id": 123456789})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)

	c = newC(handler.M{"room_id": openingRoomID, "gift_id": 1})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrNotFound("该礼物无法定制"), err)

	service.Cache5s.Flush()
	require.NoError(initDiyGift())
	c = newC(handler.M{
		"room_id": openingRoomID,
		"gift_id": testDiyGiftID,
		"dress":   testDiyGiftSendDress,
	})
	param, err := newDiyGiftSendParam(c)
	require.NoError(err)
	assert.NotNil(param.r)
	assert.NotNil(param.user)
	assert.NotNil(param.g)
	assert.NotNil(param.diygift)
	assert.NotEmpty(param.uniqueID)
	assert.NotNil(param.effect)
	assert.NotNil(param.userDiyGift)

	r, err := room.Find(223344)
	require.NoError(err)
	require.NotNil(r)
	testBlockUserID := int64(1019019)
	key := blocklist.KeyUserBlock(r.CreatorID)
	require.NoError(service.LRURedis.SAdd(key, testBlockUserID).Err())
	defer func() {
		_ = blocklist.Clear(r.CreatorID)
	}()
	c = newC(handler.M{"room_id": r.RoomID, "gift_id": testDiyGiftID, "dress": testDiyGiftSendDress})
	c.User().ID = testBlockUserID
	_, err = newDiyGiftSendParam(c)
	assert.EqualError(err, "您当前无法在本直播间内进行此操作")
}

func TestDiyGiftSendParamFindGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	allGifts, err := gift.FindAllShowingGifts()
	require.NoError(err)

	param := diyGiftSendParam{
		GiftID: -1,
	}
	err = param.findGift()
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)

	// 不能赠送的礼物
	ok := false
	for _, g := range allGifts {
		if g.Type != gift.TypeFree {
			continue
		}
		ok = true

		param = diyGiftSendParam{
			GiftID: g.GiftID,
		}
		err = param.findGift()
		assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
		break
	}
	assert.True(ok)

	// 贵族礼物
	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeNoble {
			continue
		}
		ok = true

		param = diyGiftSendParam{
			GiftID: g.GiftID,
			uv:     nil,
		}
		err = param.findGift()
		assert.Equal(actionerrors.NewErrForbidden("无法购买当前贵族礼物"), err)
		break
	}
	assert.True(ok)

	// 用户定制礼物
	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeCustom {
			continue
		}
		ok = true

		param = diyGiftSendParam{
			GiftID: g.GiftID,
			user:   &liveuser.Simple{UID: 1},
		}
		err = param.findGift()
		assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
		break
	}
	assert.True(ok)

	// 房间定制
	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeRoomCustom {
			continue
		}
		ok = true

		param = diyGiftSendParam{
			GiftID: g.GiftID,
			user:   &liveuser.Simple{UID: 1},
			r:      new(room.Room),
		}
		err = param.findGift()
		assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
		break
	}
	assert.True(ok)

	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeMedal {
			continue
		}
		ok = true

		param = diyGiftSendParam{
			GiftID: g.GiftID,
			user:   &liveuser.Simple{UID: 1},
			r:      new(room.Room),
		}
		err = param.findGift()
		assert.Equal(actionerrors.NewErrForbidden("无法购买当前粉丝礼物"), err)
		break
	}
	assert.True(ok)
}

func TestDiyGiftSendParamFindDiyInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := initDiyGift()
	require.NoError(err)
	param := diyGiftSendParam{
		r:       openingRoom,
		g:       &gift.Gift{GiftID: 123},
		Words:   "test",
		Avatars: diyGiftSendAvatars{Creator: true, User: true},
		user:    &liveuser.Simple{UID: 12},
		c:       handler.NewTestContext(http.MethodPost, "/diygift/send", true, nil),
	}
	assert.Equal(actionerrors.ErrNotFound("该礼物无法定制"), param.findDiyInfo())

	param.g.GiftID = testDiyGiftID
	param.Words = strings.Repeat("a", 21)
	assert.Equal(actionerrors.NewErrForbidden("赠言最多 10 个汉字或 20 个字母"), param.findDiyInfo())

	param.Words = ""
	param.Dress = []diygifteffects.Dress{testDiyGiftSendDress[0]}
	assert.Equal(actionerrors.ErrParamsMsg("装扮配置错误"), param.findDiyInfo(), "dress type 缺失")

	param.Dress = []diygifteffects.Dress{testDiyGiftSendDress[0], testDiyGiftSendDress[0], testDiyGiftSendDress[1]}
	assert.Equal(actionerrors.ErrParamsMsg("装扮配置错误"), param.findDiyInfo(), "dress type 重复")

	// dress
	errorDress := testDiyGiftSendDress[0]
	errorDress.Type = 9
	param.Dress = []diygifteffects.Dress{testDiyGiftSendDress[0], errorDress}
	assert.Equal(actionerrors.ErrParamsMsg("装扮配置错误"), param.findDiyInfo(), "dress type 不对")

	dress := testDiyGiftSendDress
	param.Dress = []diygifteffects.Dress{dress[0], {Type: dress[1].Type}}
	assert.Equal(actionerrors.ErrParamsMsg("装扮配置错误"), param.findDiyInfo(), "dress_id 不对")

	errorDress = dress[0]
	errorDress.Type = dress[1].Type
	param.Dress = []diygifteffects.Dress{dress[0], errorDress}
	assert.Equal(actionerrors.ErrNotFound("未找到对应装扮"), param.findDiyInfo(), "特效不存在")

	param.Dress = dress
	require.NoError(param.findDiyInfo())
	assert.NotNil(param.diygift)
	assert.NotNil(param.effect)
	require.NotNil(param.userDiyGift)
	assert.Equal(goutil.BitMask(3), param.userDiyGift.IconConfig)
	require.Len(param.userDiyGift.Dress, 2)
	assert.Equal(dress, []diygifteffects.Dress{
		{Type: param.userDiyGift.Dress[0].Type, DressID: param.userDiyGift.Dress[0].DressOID.Hex()},
		{Type: param.userDiyGift.Dress[1].Type, DressID: param.userDiyGift.Dress[1].DressOID.Hex()},
	})
}

func TestDiyGiftSendParamCheckWords(t *testing.T) {
	assert := assert.New(t)

	var rpcResp []*scan.CheckResult
	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return rpcResp, nil
	})
	defer cancel()

	param := diyGiftSendParam{
		r:       openingRoom,
		g:       &gift.Gift{GiftID: 123},
		Words:   "",
		diygift: &diygifts.DiyGift{},
		c:       handler.NewTestContext(http.MethodPost, "/diygift/send", true, nil),
		user:    new(liveuser.Simple),
	}
	assert.NoError(param.checkWords(), "没传赠言")

	param.Words = strings.Repeat("a", 21)
	assert.Equal(actionerrors.NewErrForbidden("该礼物不支持赠言"), param.checkWords())

	param.diygift.Words = new(diygifts.Words)
	assert.Equal(actionerrors.NewErrForbidden("赠言最多 10 个汉字或 20 个字母"), param.checkWords())

	param.Words = "test"
	rpcResp = []*scan.CheckResult{{Pass: false}}
	assert.Equal(actionerrors.NewErrForbidden("赠言内容含有违规信息"), param.checkWords())

	rpcResp = []*scan.CheckResult{{Pass: true, Labels: []string{scan.LabelEvil}}}
	assert.Equal(actionerrors.NewErrForbidden("赠言内容含有敏感信息"), param.checkWords())

	rpcResp = []*scan.CheckResult{{Pass: true}}
	assert.NoError(param.checkWords())
}

func TestActionDiyGiftSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URISendGift, func(input interface{}) (output interface{}, err error) {
		return userapi.BalanceResp{}, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(vip.URLUserVips, func(input any) (output any, err error) {
		return
	})
	defer cancel()
	cancel = mrpc.SetMock(userapi.URIGoUserBlocklist, func(input any) (output any, err error) {
		return
	})
	defer cancel()

	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)
	c := handler.NewTestContext(http.MethodPost, "/diygift/send", true, nil)
	_, err := ActionDiyGiftSend(c)
	assert.Equal(actionerrors.ErrParams, err)

	err = initDiyGift()
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/diygift/send", true,
		handler.M{
			"room_id": openingRoomID,
			"gift_id": testDiyGiftID,
			"dress":   testDiyGiftSendDress,
		})
	_, err = ActionDiyGiftSend(c)
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	var lg livegifts.LiveGift
	err = livegifts.Collection().FindOne(ctx,
		bson.M{
			"gift_id":      testDiyGiftID,
			"user_id":      c.UserID(),
			"updated_time": now,
		}).Decode(&lg)
	assert.NoError(err)

	var ud userdiygifts.UserDiyGift
	err = userdiygifts.Collection().FindOne(ctx,
		bson.M{
			"gift_id":     testDiyGiftID,
			"user_id":     c.UserID(),
			"room_id":     openingRoomID,
			"create_time": now.Unix(),
		}).Decode(&ud)
	assert.NoError(err)
}

func TestDiyGiftSendParamAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := diyGiftSendParam{
		r:    new(room.Room),
		lg:   &livegifts.LiveGift{Price: 1},
		user: &liveuser.Simple{UID: 12},
	}
	param.r.RoomID = 1478963
	now := goutil.TimeNow()
	keys := []string{
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeCurrent, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeHourly, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeWeek, now),
	}
	require.NoError(service.Redis.Del(keys...).Err())
	assert.NotPanics(func() { param.addRevenueRank() })
	// 通过判断键是否存在来看榜单是否添加成功
	val, err := service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.EqualValues(2, val)
	// 测试开播状态下会增加本场榜
	param.r.Status.Open = room.StatusOpenTrue
	assert.NotPanics(func() { param.addRevenueRank() })
	val, err = service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.EqualValues(3, val)
}

func TestDiyGiftSendParamSendAddPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := diyGiftSendParam{
		lg:   &livegifts.LiveGift{GiftNum: 1},
		g:    &gift.Gift{Point: 1},
		user: &liveuser.Simple{UID: 12},
	}
	assert.NotPanics(func() { param.addPK() })

	param.g.Price = 10
	param.r = new(room.Room)
	param.addPK()
	assert.Empty(param.broadcastElems)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livepk.PKCollection()
	_, err := col.DeleteMany(ctx, bson.M{"fighters": bson.M{"$exists": false}})
	require.NoError(err)
	var lp livepk.LivePK
	err = col.FindOne(ctx,
		bson.M{"status": livepk.PKRecordStatusFighting}).Decode(&lp)
	require.NoError(err)
	param.r.RoomID = lp.Fighters[1].RoomID
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(param.r.RoomID),
		"test", time.Second).Err())
	param.addPK()
	assert.Len(param.broadcastElems, 2)
}

func TestDiyGiftSendParamAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := new(room.Room)
	r.RoomID = 9874153

	param := diyGiftSendParam{
		r: r,
		g: &gift.Gift{
			Price: 1,
			Point: 1,
			Type:  gift.TypeRebate,
		},
		lg:   &livegifts.LiveGift{Price: 1},
		user: &liveuser.Simple{UID: 252},
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": r.RoomID, "user_id": param.user.UserID()}
	_, err := livemedal.Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	param.addMedalPoint()
	r.Medal = &room.Medal{Name: "12345"}
	param.addMedalPoint()
	medal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	multi, _ := livemedal.FindMedalPointMultiple(param.r.CreatorID, false)
	assert.EqualValues(2*multi, medal.Point)

	param.g.Price = 0
	param.g.Attr.Set(gift.AttrDisableMedalPoint)
	param.addMedalPoint()
	medal = new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(2*multi, medal.Point)
}

func TestDiyGiftSendParam_addMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	param := diyGiftSendParam{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 1,
				Status: room.Status{MultiConnect: room.MultiConnectStatusOngoing},
			},
		},
		g: &gift.Gift{
			Price: 10,
		},
		lg:   &livegifts.LiveGift{GiftNum: 50},
		user: &liveuser.Simple{UID: 252},
	}
	param.addMultiConnectScore()
	assert.Empty(param.broadcastElems)

	members := []livemulticonnect.GroupMember{
		{RoomID: 1, EndTime: 0, GroupID: 1},
		{RoomID: 2, EndTime: 0, GroupID: 1},
		{RoomID: 3, EndTime: 0, GroupID: 2},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param.addMultiConnectScore()
	assert.Len(param.broadcastElems, 2)
}

func TestDiyGiftSendParamAddUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	}) // 防止任务影响测试
	defer goutil.SetTimeNow(nil)

	param := diyGiftSendParam{
		user: &liveuser.Simple{UID: 3456835},
		lg:   &livegifts.LiveGift{Price: 10},
		r: &room.Room{
			Helper: room.Helper{CreatorUsername: "aaa"},
		},
	}

	before, err := liveuser.Find(param.user.UserID())
	require.NoError(err)
	require.NotNil(before)

	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.user.UserID())
	require.NoError(err)
	assert.Equal(param.lg.Price*10, after.Contribution-before.Contribution)

	before = after
	param.uv = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.user.UserID())
	require.NoError(err)
	assert.Equal(param.lg.Price*2*10, after.Contribution-before.Contribution)
}

func TestDiyGiftSendParamBuildIMMessage(t *testing.T) {
	param := diyGiftSendParam{
		user: &liveuser.Simple{
			UID:     12,
			IconURL: "https://icon.png",
		},
		r: testRoom,
		g: &gift.Gift{
			GiftID: 301,
			Point:  10,
		},
		Avatars: diyGiftSendAvatars{Creator: true, User: true},
		diygift: &diygifts.DiyGift{
			Words:   &diygifts.Words{},
			Avatars: &diygifts.Avatars{},
		},
		effect: &diygifteffects.DiyGiftEffect{
			Effect:    "oss://effect.mp4",
			WebEffect: "oss://effect-web.mp4",
			OldEffect: "oss://effect-old.mp4",
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
		SetGift(param.g, 1)
	param.Words = "test"

	param.buildIMMessage()
	require.Len(t, param.broadcastElems, 1)
	rm, ok := param.broadcastElems[0].Payload.(*livegifts.RoomMessage)
	require.True(t, ok)
	require.NotNil(t, rm.Gift.EffectOptions)
	assert.NotNil(t, rm.Gift.EffectOptions.Words)
	require.NotNil(t, rm.Gift.EffectOptions.Avatars)
	assert.Equal(t, param.user.IconURL, rm.Gift.EffectOptions.Avatars.UserIconURL)
	assert.Equal(t, param.r.CreatorIconURL, rm.Gift.EffectOptions.Avatars.CreatorIconURL)
	assert.Equal(t, storage.ParseSchemeURLs(param.effect.Effect), rm.Gift.NewEffectURL)
	assert.Equal(t, storage.ParseSchemeURLs(param.effect.WebEffect), rm.Gift.WebEffectURL)
	assert.Equal(t, storage.ParseSchemeURLs(param.effect.OldEffect), rm.Gift.EffectURL)

	param.g.Attr.Set(gift.AttrAlwaysNotify)
	param.buildIMMessage()
	assert.Len(t, param.broadcastElems, 3)

	param = diyGiftSendParam{
		FromRoomID: openingRoomID,
		user: &liveuser.Simple{
			UID:     12,
			IconURL: "https://icon.png",
		},
		r: testRoom,
		g: &gift.Gift{
			GiftID: 301,
			Point:  10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
		SetGift(param.g, 1)
	param.buildIMMessage()
	require.Len(t, param.broadcastElems, 2)
	assert.Equal(t, param.r.RoomID, param.broadcastElems[0].RoomID)
	assert.Zero(t, param.broadcastElems[0].UserID)
	assert.Equal(t, param.user.UserID(), param.broadcastElems[1].UserID)
	assert.Equal(t, param.FromRoomID, param.broadcastElems[1].RoomID)

	t.Run("GiftNotification", func(t *testing.T) {
		now := goutil.TimeNow()
		expTime := now.Add(time.Hour).Unix()
		testUserID := int64(12345678)

		ctx, cancelCtx := service.MongoDB.Context()
		defer cancelCtx()

		_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})
		require.NoError(t, err)
		defer userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})

		t.Run("用户没有送礼通知外观", func(t *testing.T) {
			param := diyGiftSendParam{
				user: &liveuser.Simple{UID: testUserID},
				r:    testRoom,
				g: &gift.Gift{
					GiftID: 301,
					Point:  10,
				},
			}
			param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
				SetGift(param.g, 1)

			param.buildIMMessage()
			require.Len(t, param.broadcastElems, 1)

			roomMessage, ok := param.broadcastElems[0].Payload.(*livegifts.RoomMessage)
			require.True(t, ok)
			assert.Nil(t, roomMessage.GiftNotification)
		})

		t.Run("用户佩戴送礼通知外观", func(t *testing.T) {
			testAppearance := &userappearance.UserAppearance{
				UserID:       testUserID,
				AppearanceID: 10001,
				Type:         appearance.TypeGiftNotification,
				Status:       userappearance.StatusWorn,
				StartTime:    now.Add(-time.Minute).Unix(),
				ExpireTime:   &expTime,
				TextColorItem: &appearance.TextColorItem{
					Username: "#FF0000",
				},
				TextColor: "#00FF00",
				Frame:     "oss://test/frame.png",
			}
			_, err := userappearance.Collection().InsertOne(ctx, testAppearance)
			require.NoError(t, err)

			param := diyGiftSendParam{
				user: &liveuser.Simple{UID: testUserID},
				r:    testRoom,
				g: &gift.Gift{
					GiftID: 301,
					Point:  10,
				},
			}
			param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
				SetGift(param.g, 1)

			param.buildIMMessage()
			require.Len(t, param.broadcastElems, 1)

			roomMessage, ok := param.broadcastElems[0].Payload.(*livegifts.RoomMessage)
			require.True(t, ok)
			require.NotNil(t, roomMessage.GiftNotification)
			assert.Equal(t, testAppearance.TextColorItem.Username, roomMessage.GiftNotification.UsernameColor)
			assert.Equal(t, testAppearance.TextColor, roomMessage.GiftNotification.TextColor)
			assert.Contains(t, roomMessage.GiftNotification.FrameURL, "test/frame.png")
		})
	})
}
