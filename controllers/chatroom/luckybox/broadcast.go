package luckybox

import (
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service/userapi"
)

// drawLuckyBoxBroadcast 宝盒广播
type drawLuckyBoxBroadcast struct {
	Type             string                           `json:"type"`
	Event            string                           `json:"event"`
	RoomID           int64                            `json:"room_id"`
	User             *liveuser.Simple                 `json:"user"`
	Time             int64                            `json:"time"` // 单位：毫秒
	CurrentRevenue   int64                            `json:"current_revenue"`
	Bubble           *bubble.Simple                   `json:"bubble,omitempty"`
	GiftNotification *userappearance.GiftNotification `json:"gift_notification,omitempty"`
	LuckyBox         *broadcastLuckyBox               `json:"lucky_box"`
}

// broadcastLuckyBox 宝盒
type broadcastLuckyBox struct {
	GoodsID      int64          `json:"goods_id"`
	Name         string         `json:"name"`
	Contribution int64          `json:"contribution"`
	Num          int            `json:"num"`
	IconURL      string         `json:"icon_url"`
	Gifts        []*boxGiftItem `json:"gifts"`
}

// Send 发送广播
func (b drawLuckyBoxBroadcast) Send() error {
	err := userapi.Broadcast(b.RoomID, b)
	if err != nil {
		return err
	}
	return nil
}
