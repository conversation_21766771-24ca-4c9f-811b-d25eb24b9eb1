package luckybox

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckybox"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLuckyBoxDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(userapi.URIBuyGoods, func(any) (any, error) {
		return userapi.BalanceResp{
			TransactionID:    23333,
			Balance:          123,
			LiveNobleBalance: 123,
			Price:            123,
		}, nil
	})
	defer cleanup()

	var (
		testUserID  = int64(12)
		testGoodsID = int64(14232132)
		testPoolID  = int64(123)
	)
	err := blocklist.Clear(testUserID)
	require.NoError(err)
	err = service.LiveDB.Delete(livegoods.LiveGoods{}, "id = ?", testGoodsID).Error
	require.NoError(err)
	err = service.LiveDB.Create(&livegoods.LiveGoods{
		ID:   testGoodsID,
		Type: livegoods.GoodsTypeLuckyBox,
		More: fmt.Sprintf(`{"lucky_box":{"gift_pool_id":%d,"result_msg":"恭喜你成功获得"}}`, testPoolID),
	}).Error
	require.NoError(err)
	cur, err := gift.Collection().Find(context.Background(),
		bson.M{
			"type": gift.TypeFree,
			"$or": bson.A{
				bson.M{"setorder": bson.M{"$ne": nil}},
				bson.M{"order": bson.M{"$gt": gift.OrderHide}},
			},
		},
	)
	require.NoError(err)
	var gifts []*gift.Gift
	err = cur.All(context.Background(), &gifts)
	require.NoError(err)
	require.NotEmpty(gifts)
	_, err = gift.CollectionDrawPool().DeleteMany(context.Background(), bson.M{"pool_id": testPoolID})
	require.NoError(err)
	_, err = gift.CollectionDrawPool().InsertOne(context.Background(), gift.PoolLuckyBox{
		PoolID: testPoolID,
		Type:   gift.PoolTypeLuckyBox,
		Rates: map[int64]int{
			gifts[0].GiftID: 10,
			gifts[1].GiftID: 90,
		},
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/luckybox/draw", true, handler.M{
		"room_id":  223344,
		"goods_id": testGoodsID,
		"num":      1,
	})
	err = service.Redis.Del(keys.LockLuckyBoxDraw2.Format(testGoodsID, c.UserID())).Err()
	require.NoError(err)
	resp, _, err := ActionLuckyBoxDraw(c)
	require.NoError(err)
	require.NotNil(resp)
	response, ok := resp.(*drawResponse)
	require.True(ok)
	assert.Equal("恭喜你成功获得", response.ResultMsg)
}

func TestNewDrawParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/luckybox/draw", true, handler.M{
		"room_id":  223344,
		"goods_id": 1,
		"num":      2,
	})
	param, err := newDrawParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.EqualValues(223344, param.RoomID)
	assert.EqualValues(1, param.GoodsID)
	assert.EqualValues(2, param.Num)
}

func TestDrawParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	var (
		testUserID  = int64(12)
		testGoodsID = int64(14232132)
	)
	err := blocklist.Clear(testUserID)
	require.NoError(err)
	err = service.LiveDB.Delete(livegoods.LiveGoods{}, "id = ?", testGoodsID).Error
	require.NoError(err)

	param := &drawParam{
		RoomID:  223344,
		GoodsID: testGoodsID,
		Num:     2,
		c:       handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/luckybox/draw", true, nil),
	}
	param.c.User().ID = testUserID
	err = param.check()
	assert.Equal(actionerrors.ErrGoodsNotFound, err)

	err = service.LiveDB.Create(&livegoods.LiveGoods{
		ID:   testGoodsID,
		Type: livegoods.GoodsTypeLuckyBox,
		More: `{"lucky_box":{"result_msg":"","draw_num_options":[1,2,3]}}`,
	}).Error
	require.NoError(err)
	err = param.check()
	require.NoError(err)
}

func TestDrawParam_lock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &drawParam{
		user: &liveuser.Simple{
			UID: 1919810,
		},
		goods: &livegoods.LiveGoods{
			ID: 1,
		},
	}
	key := keys.LockLuckyBoxDraw2.Format(param.goods.GoodsID(), param.user.UserID())
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	unlock, ok := param.lock()
	require.True(ok)
	assert.NotNil(unlock)

	unlock, ok = param.lock()
	require.False(ok)
	assert.Nil(unlock)
}

func TestDrawParam_draw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIBuyGoods, func(any) (any, error) {
		return userapi.BalanceResp{
			TransactionID:    23333,
			Balance:          123,
			LiveNobleBalance: 123,
			Price:            123,
		}, nil
	})
	defer cleanup()

	var (
		testGoodsID = int64(14232132)
		testPoolID  = int64(123)
	)
	cur, err := gift.Collection().Find(context.Background(),
		bson.M{
			"type": gift.TypeFree,
			"$or": bson.A{
				bson.M{"setorder": bson.M{"$ne": nil}},
				bson.M{"order": bson.M{"$gt": gift.OrderHide}},
			},
		},
	)
	require.NoError(err)
	var gifts []*gift.Gift
	err = cur.All(context.Background(), &gifts)
	require.NoError(err)
	require.NotEmpty(gifts)
	_, err = gift.CollectionDrawPool().DeleteMany(context.Background(), bson.M{"pool_id": testPoolID})
	require.NoError(err)
	_, err = gift.CollectionDrawPool().InsertOne(context.Background(), gift.PoolLuckyBox{
		PoolID: testPoolID,
		Type:   gift.PoolTypeLuckyBox,
		Rates: map[int64]int{
			gifts[0].GiftID: 10,
			gifts[1].GiftID: 90,
		},
	})
	require.NoError(err)

	param := drawParam{
		Num:  2,
		user: &liveuser.Simple{},
		goods: &livegoods.LiveGoods{
			ID:   testGoodsID,
			Type: livegoods.GoodsTypeLuckyBox,
		},
		goodsMore: &livegoods.More{
			LuckyBox: &livegoods.LuckyBox{
				GiftPoolID: testPoolID,
				ResultMsg:  "恭喜你成功获得",
			},
		},
		room: &room.Room{
			Helper: room.Helper{
				Status: room.Status{
					Open: room.StatusOpenTrue,
				},
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/api/v2/chatroom/luckybox/draw", true, nil),
	}
	err = service.Redis.Del(keys.LockLuckyBoxDraw2.Format(param.goods.GoodsID(), param.user.UserID())).Err()
	require.NoError(err)
	resp, err := param.draw()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal("恭喜你成功获得", resp.ResultMsg)
	assert.Len(resp.LuckyBox.Gifts, 2)
}

func TestDrawParam_addRecordsAndSendGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID  = int64(1312312)
		testGiftIDs = []int64{1, 2, 3}
		testGiftMap = map[int64]*gift.Gift{
			1: {GiftID: 1, Name: "gift1", Type: gift.TypeFree},
			2: {GiftID: 2, Name: "gift2", Type: gift.TypeFree},
			3: {GiftID: 3, Name: "gift3", Type: gift.TypeFree},
		}
	)
	_, err := useritems.Collection().DeleteMany(context.Background(), bson.M{"user_id": testUserID, "gift_id": bson.M{"$in": testGiftIDs}})
	require.NoError(err)
	err = liveluckybox.DB().Delete(liveluckybox.Record{}, "user_id = ?", testUserID).Error
	require.NoError(err)

	param := &drawParam{
		user: &liveuser.Simple{
			UID: testUserID,
		},
		room: &room.Room{
			Helper: room.Helper{
				RoomID: 223344,
			},
		},
	}
	err = param.addRecordsAndSendGifts(testGiftIDs, testGiftMap, 0)
	require.NoError(err)

	var items []*useritems.UserItem
	res, err := useritems.Collection().Find(context.Background(), bson.M{"user_id": testUserID, "gift_id": bson.M{"$in": testGiftIDs}})
	require.NoError(err)
	err = res.All(context.Background(), &items)
	require.NoError(err)
	assert.Len(items, 3)
	var count int
	err = liveluckybox.DB().Model(liveluckybox.Record{}).Where("user_id = ?", testUserID).Count(&count).Error
	require.NoError(err)
	assert.Equal(3, count)
}

func TestDrawParam_addRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) {
		return "success", nil
	})
	defer cleanup()
	var (
		testRoomID    = int64(223344)
		testCreatorID = int64(1)
		testUserID    = int64(2)
		now           = goutil.TimeNow()

		key = usersrank.Key(usersrank.TypeWeek, now)
	)
	require.NoError(service.Redis.ZRem(key, strconv.FormatInt(testCreatorID, 10)).Err())

	param := &drawParam{
		user: &liveuser.Simple{UID: testUserID, Username: "hhh"},
		room: &room.Room{
			Helper: room.Helper{
				RoomID:    testRoomID,
				CreatorID: testCreatorID,
			},
		},
		goods: &livegoods.LiveGoods{
			Price: 123,
		},
		Num: 2,
	}
	param.addRevenue()

	source, err := service.Redis.ZScore(key, strconv.FormatInt(testCreatorID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(246, source)
}

func TestDrawParam_addUserRedeemPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := drawParam{
		user: &liveuser.Simple{
			UID: 1919810,
		},
		goods: &livegoods.LiveGoods{
			Price: 10,
		},
		Num: 50,
	}
	var rp shop.UserRedeemPoint
	require.NoError(rp.DB().Delete("", "year = ? AND user_id = ?",
		goutil.TimeNow().Year(), param.user.UserID()).Error)
	param.addUserRedeemPoint()
	require.NoError(rp.DB().Take(&rp, "year = ? AND user_id = ?",
		goutil.TimeNow().Year(), param.user.UserID()).Error)
	assert.EqualValues(param.goods.Price*param.Num, rp.Point)
}

func TestDrawParam_addMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	param := drawParam{
		room: &room.Room{
			Helper: room.Helper{
				RoomID: 1,
				Status: room.Status{MultiConnect: room.MultiConnectStatusOngoing},
			},
		},
		goods: &livegoods.LiveGoods{
			Price: 10,
		},
		Num: 50,
	}
	param.addMultiConnectScore()
	assert.Empty(param.broadcastElems)

	members := []livemulticonnect.GroupMember{
		{RoomID: 1, EndTime: 0, GroupID: 1},
		{RoomID: 2, EndTime: 0, GroupID: 1},
		{RoomID: 3, EndTime: 0, GroupID: 2},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param.addMultiConnectScore()
	assert.Len(param.broadcastElems, 2)
}

func TestDrawParam_broadcast(t *testing.T) {
	t.Run("Normal", func(t *testing.T) {
		var count int
		cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) {
			count++
			return "success", nil
		})
		defer cancel()

		param := drawParam{
			goods: &livegoods.LiveGoods{
				ID: 1,
			},
			user: &liveuser.Simple{},
			room: &room.Room{},
		}
		param.broadcast(nil)
		assert.Equal(t, 1, count)
	})

	t.Run("GiftNotification", func(t *testing.T) {
		now := goutil.TimeNow()
		expTime := now.Add(time.Hour).Unix()
		testUserID := int64(12345678)

		ctx, cancelCtx := service.MongoDB.Context()
		defer cancelCtx()

		// 清理测试数据
		_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})
		require.NoError(t, err)
		defer userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})

		// 创建测试数据
		testAppearance := &userappearance.UserAppearance{
			UserID:       testUserID,
			AppearanceID: 10001,
			Type:         appearance.TypeGiftNotification,
			Status:       userappearance.StatusWorn,
			StartTime:    now.Add(-time.Minute).Unix(),
			ExpireTime:   &expTime,
			TextColorItem: &appearance.TextColorItem{
				Username: "#FF0000",
			},
			TextColor: "#00FF00",
			Frame:     "oss://test/frame.png",
		}
		_, err = userappearance.Collection().InsertOne(ctx, testAppearance)
		require.NoError(t, err)

		cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input any) (any, error) {
			var elems []*userapi.BroadcastElem
			err := json.Unmarshal(input.(json.RawMessage), &elems)
			require.NoError(t, err)
			require.Equal(t, 1, len(elems))

			payload, ok := elems[0].Payload.(map[string]interface{})
			require.True(t, ok)
			giftNotification, exists := payload["gift_notification"]
			require.True(t, exists)
			require.NotNil(t, giftNotification)

			giftNotificationMap := giftNotification.(map[string]interface{})
			assert.Equal(t, testAppearance.TextColorItem.Username, giftNotificationMap["username_color"])
			assert.Equal(t, testAppearance.TextColor, giftNotificationMap["text_color"])
			assert.Equal(t, storage.ParseSchemeURL(testAppearance.Frame), giftNotificationMap["frame_url"])

			return "success", nil
		})
		defer cancel()

		param := drawParam{
			RoomID:  1,
			GoodsID: 1,
			Num:     1,
			goods: &livegoods.LiveGoods{
				ID:    1,
				Title: "测试宝盒",
				Price: 10,
				Icon:  "",
			},
			user: &liveuser.Simple{UID: testUserID},
			room: &room.Room{
				Helper: room.Helper{RoomID: 1},
			},
		}

		param.broadcast(nil)
	})
}

func TestDrawParam_addRoomPaidUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID   = int64(9074509)
		testUserID   = int64(1312312)
		testOpenTime = int64(1727409158000)
	)
	key := keys.KeyRoomPaidUser2.Format(testRoomID, testOpenTime)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	ok, err := service.Redis.SIsMember(key, testUserID).Result()
	require.NoError(err)
	assert.False(ok)
	param := drawParam{
		RoomID: testRoomID,
		user: &liveuser.Simple{
			UID: testUserID,
		},
		room: &room.Room{
			Helper: room.Helper{
				Status: room.Status{
					OpenTime: testOpenTime,
				},
			},
		},
	}
	param.addRoomPaidUser()
	ok, err = service.Redis.SIsMember(key, testUserID).Result()
	require.NoError(err)
	assert.True(ok)
}
