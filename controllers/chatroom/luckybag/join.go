package luckybag

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/imuserlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/messagelimit"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type joinParams struct {
	RoomID     int64 `form:"room_id" json:"room_id"`
	LuckyBagID int64 `form:"lucky_bag_id" json:"lucky_bag_id"`
	From       int   `form:"from" json:"from"`

	user     *user.User
	r        *room.Room
	lb       *luckybag.InitiateRecord
	liveUser *liveuser.Simple
	b        *bubble.Simple
	msgID    string
	uc       mrpc.UserContext
	clientIP string
	buvid    string

	resp *joinResp
}

type joinResp struct {
	LuckyBag *luckybag.FullInfo `json:"lucky_bag"`
	MsgID    string             `json:"msg_id"`
	Message  string             `json:"message"`
	User     *liveuser.Simple   `json:"user"`
	Bubble   *bubble.Simple     `json:"bubble,omitempty"`
}

// ActionLuckyBagJoin 参与福袋抽奖
/**
 * @api {post} /api/v2/chatroom/luckybag/join 参与福袋抽奖
 * @apiDescription 客户端根据接口响应的消息格式根据 msg_id 去重后直接回显消息，收到 ws 用户消息后（type=message, event=new）也需要去重处理。
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} lucky_bag_id 福袋 ID
 * @apiParam {Number} [from=0] 参与抽奖来源 0: 来源直播间; 1: 来源广告
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "参与成功！",
 *     "data": {
 *       "lucky_bag": {
 *         "lucky_bag_id": 1,
 *         "status": 0, // 福袋状态 0: 待开奖; 1: 已开奖（发奖中）; 2: 已开奖（已发奖）
 *         "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *         "reward_type": 2, // 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *         "prize_name": "签名照", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理加书名号
 *         "prize_num": 1, // 奖品数量
 *         "prize_price": 100, // 福袋每份价值，只有剧集福袋有价格，单位：钻
 *         "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图
 *         "join_num": 19999, // 参与人数
 *         "target_type": 0, // 参与对象类型 type 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝
 *         "remain_duration": 1800, // 剩余时间（毫秒）
 *         "keyword": "点点关注抽福袋", // 参与口令
 *         "join_status": 1 // 是否参与抽奖 0: 未参与; 1: 已参与
 *       },
 *       "msg_id": "3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
 *       "message": "点点关注抽福袋",
 *       "user": {
 *         "user_id": 10000000052,
 *         "username": "jesse_tang",
 *         "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#f45b41"
 *           },
 *           {
 *             "type": "level",
 *             "level": 30
 *           },
 *           {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }
 *         ]
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段不存在
 *         "type": "message", // 气泡类型，聊天气泡 message
 *         "image_url": "https://static.maoercdn.com/live/bubble/image/001.png"
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 口令消息:
 *   {
 *     "type": "message",
 *     "event": "new",
 *     "room_id": 1,
 *     "msg_id": "3bcfc1d5-adf9-42e4-8bea-f2e7ceb75428",
 *     "message": "点点关注抽福袋",
 *     "user": {
 *       "user_id": 10000000052,
 *       "username": "jesse_tang",
 *       "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 30
 *         },
 *         {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }
 *       ]
 *     },
 *     "bubble": {
 *       "type": "message",
 *       "image_url": "https://static-test.maoercdn.com/live/bubbles/message/10011_36_36_36_36.png"
 *     }
 *   }
 */
func ActionLuckyBagJoin(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newJoinParams(c)
	if err != nil {
		return nil, "", err
	}
	err = param.checkUser()
	if err != nil {
		return nil, "", err
	}
	err = param.checkLuckyBag()
	if err != nil {
		return nil, "", err
	}
	err = param.insertRecord()
	if err != nil {
		return nil, "", err
	}
	goutil.Go(func() {
		param.sendMessage()
	})
	param.buildResp()
	return param.resp, "参与成功！", nil
}

func newJoinParams(c *handler.Context) (*joinParams, error) {
	var param joinParams
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.LuckyBagID <= 0 ||
		(param.From != luckybag.SourceLive && param.From != luckybag.SourceAD) {
		return nil, actionerrors.ErrParams
	}
	// 仅限安卓 >= 6.1.4、iOS >= 6.1.4 的版本客户端用户参与
	if !c.Equip().FromApp || c.Equip().IsAppOlderThan("6.1.4", "6.1.4") {
		return nil, actionerrors.NewErrForbidden("当前版本暂不支持该玩法哦~")
	}
	param.user = c.User()
	lock := keys.LockUserLuckyBagJoin1.Format(param.user.ID)
	ok, err := service.Redis.SetNX(lock, 1, 5*time.Second).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrForbidden("操作太快啦，请稍后再试哦~")
	}

	param.r, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	param.lb, err = luckybag.FindShowingInitiateRecordByID(param.LuckyBagID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.lb == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	if param.lb.RoomID != param.RoomID {
		return nil, actionerrors.NewErrForbidden("不可参与当前福袋")
	}
	if param.lb.Status != luckybag.StatusPending {
		return nil, actionerrors.NewErrLiveForbidden("该福袋已结束，无法参与")
	}
	exists, err := luckybag.JoinRecordExists(param.LuckyBagID, param.user.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.NewErrForbidden("已参与当前福袋")
	}

	param.liveUser, err = liveuser.FindOneSimple(bson.M{"user_id": param.user.ID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.liveUser == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	param.b, err = userappearance.FindMessageBubble(param.user.ID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	param.msgID = util.BuildMsgID(param.user.ID, param.r.RoomID, param.lb.Message,
		config.Conf.Params.General.FeedbackKey, c.Equip().OS)
	param.uc = c.UserContext()
	param.clientIP = c.ClientIP()
	param.buvid = c.BUVID()
	return &param, nil
}

func (param *joinParams) checkUser() error {
	if param.user.ID == param.r.CreatorID {
		return actionerrors.NewErrForbidden("不可参与当前福袋")
	}

	if param.user.GetMobile() == "" {
		// 用户未绑定手机需要报错
		return actionerrors.NewErrForbidden("参与福袋需要绑定手机号哦~")
	}

	userID := param.user.ID
	// 是否被直播间封禁
	isBanned, err := userstatus.IsBanned(userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if isBanned {
		// 被直播间封禁的用户不可参与福袋
		return actionerrors.NewErrForbidden("您的账号已被封禁，无法参与福袋")
	}

	// 是否在全站黑名单
	inBlockList, err := blocklist.Exists(userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if inBlockList {
		// 全站黑名单用户不可参与福袋
		return actionerrors.NewErrForbidden("您的账号已被封禁，无法参与福袋")
	}

	creatorBlockUser, _, err := userapi.UserBlockStatus(param.r.CreatorID, userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if creatorBlockUser {
		// 被主播拉黑的用户不可参与福袋
		return actionerrors.NewErrForbidden("由于主播设置，您无法参与本次喵喵福袋")
	}

	// 是否被禁言
	mute, err := livemembers.IsMute(userID, param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if mute.GlobalMute {
		// 全站禁言用户不可参与福袋
		return actionerrors.NewErrForbidden("您已被全站禁言，无法发送福袋口令")
	}
	if mute.RoomMute {
		// 禁言用户不可参与福袋
		return actionerrors.NewErrForbidden("您已被禁言，无法发送福袋口令")
	}
	// 用户是否在福袋用户黑名单中
	isBlocked, err := application.IsUserLuckyBagJoinBlocked(userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if isBlocked {
		// 福袋黑名单用户不可参与
		return actionerrors.NewErrForbidden("福袋参与失败")
	}

	// 检查用户是否在当前直播间中
	isNoRiskConnect, err := imuserlogs.IsNoRiskConnect(param.user.ID, param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !isNoRiskConnect {
		return actionerrors.NewErrForbidden("福袋参与失败")
	}

	// 检测阿里云营销风险
	pass := param.checkCouponAbuse()
	if !pass {
		return actionerrors.NewErrForbidden("福袋参与失败")
	}
	return nil
}

func (param *joinParams) checkCouponAbuse() bool {
	conf, err := params.FindLuckyBag()
	if err != nil {
		logger.Error(err)
		// PASS: 查询营销风险配置出错时，忽略该错误，视作检测通过，避免影响参与福袋
		return true
	}
	if conf.HighRiskScore == 0 {
		// 高风险分数阈值为 0 时，视作不检测，直接通过
		return true
	}
	input := userapi.ScanRiskParam{
		Scene:  userapi.RiskSceneCoupon,
		UserID: param.user.ID,
		IP:     param.clientIP,
		BUVID:  param.buvid,
	}
	res, err := userapi.ScanRisk(param.uc, input)
	if err != nil {
		logger.Error(err)
		// PASS: 检测营销风险出错时，忽略该错误，视作检测通过，避免影响参与福袋
		return true
	}
	return res.Score < conf.HighRiskScore
}

func (param *joinParams) checkLuckyBag() error {
	switch param.lb.TargetType {
	case luckybag.TargetTypeAll:
		// PASS
	case luckybag.TargetTypeFollow:
		ok := param.followCreator()
		if !ok {
			return actionerrors.NewErrForbidden("福袋参与失败，请稍后再试哦~")
		}
	case luckybag.TargetTypeMedal:
		exists, err := livemedal.HasUserOwnedMedal(param.user.ID, param.r.RoomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !exists {
			// 没有当前直播间勋章
			return actionerrors.NewErrForbidden("福袋参与失败，请稍后再试哦~")
		}
	case luckybag.TargetTypeSuperFan:
		lm, err := livemedal.FindOwnedMedal(param.user.ID, param.r.RoomID, livemedal.FindOptions{OnlyMedal: true})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if lm == nil || !livemedal.IsSuperFanActive(lm.SuperFan) {
			// 非当前直播间超粉
			return actionerrors.NewErrForbidden("福袋参与失败，请稍后再试哦~")
		}
	}
	return nil
}

func (param *joinParams) followCreator() bool {
	followed, err := attentionuser.HasFollowed(param.user.ID, param.r.CreatorID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":    param.user.ID,
			"creator_id": param.r.CreatorID,
		}).Errorf("参加福袋时查询和主播的关注关系出错：%v", err)
		// PASS: 关注过程出错时，不报错避免影响参与福袋
		return true
	}
	if !followed {
		// 未关注主播时，进行关注
		err = attentionuser.RPCFollow(param.user.ID, param.r.CreatorID, nil)
		if err != nil {
			if attentionuser.IsRPCUserError(err) {
				// 关注受限
				return false
			}
			logger.WithFields(logger.Fields{
				"user_id":    param.user.ID,
				"creator_id": param.r.CreatorID,
			}).Errorf("参与福袋时自动关注主播出错：%v", err)
			// PASS: 关注过程出错时，不报错避免影响参与福袋
		}
	}
	return true
}

// 参与福袋限制
const (
	// joinLuckyBagUserDailyLimit 一个账号每个自然日最多参与 49 次福袋
	joinLuckyBagUserDailyLimit = 49
	// joinLuckyBagIPDailyLimit 一个 IP 每个自然日最多参与 99 次福袋
	joinLuckyBagIPDailyLimit = 99

	// joinLuckyBagToBlockUserLevelThreshold 用户参与福袋，不触发自动进黑名单的最小等级
	joinLuckyBagToBlockUserLevelThreshold = 21
)

// joinLimit 检查用户参与福袋限制
func (param *joinParams) joinLimit() bool {
	blocked, err := application.IsUserReceivePrizeBlocked(param.user.ID)
	if err != nil {
		logger.WithField("user_id", param.user.ID).Error(err)
		// NOTICE: 判断黑名单出错时，降级处理，默认用户为黑名单风险用户，降低本次中奖优先级
		return true
	}
	if blocked {
		return true
	}

	userLevel := usercommon.Level(param.liveUser.Contribution)
	if userLevel >= joinLuckyBagToBlockUserLevelThreshold {
		// 用户等级 >= 特定等级时不受黑名单次数限制
		return false
	}

	count, err := luckybag.CountOneDayJoinRecordByUser(param.user.ID)
	if err != nil {
		logger.WithField("user_id", param.user.ID).Error(err)
		// PASS
	}
	if count >= joinLuckyBagUserDailyLimit {
		// 用户达到每日参与次数上限，且直播等级小于特定等级时永久加入黑名单
		err := application.AddUserToDrawReceivedPrizeBlockList(param.user.ID)
		if err != nil {
			logger.WithField("user_id", param.user.ID).Error(err)
			// PASS
		}
		return true
	}

	count, err = luckybag.CountOneDayJoinRecordByIP(param.clientIP)
	if err != nil {
		logger.WithField("ip", param.clientIP).Error(err)
		// PASS
	}
	if count >= joinLuckyBagIPDailyLimit {
		// 用户 IP 达到每日参与次数上限，永久加入黑名单
		err := application.AddUserToDrawReceivedPrizeBlockList(param.user.ID)
		if err != nil {
			logger.WithField("user_id", param.user.ID).Error(err)
			// PASS
		}
		return true
	}
	return false
}

func (param *joinParams) insertRecord() error {
	isRisk := param.joinLimit()
	nowUnix := goutil.TimeNow().Unix()
	record := &luckybag.JoinRecord{
		CreateTime:   nowUnix,
		ModifiedTime: nowUnix,
		LuckyBagID:   param.lb.ID,
		UserID:       param.user.ID,
		IP:           param.clientIP,
		Source:       param.From,
	}
	if isRisk {
		record.Status = luckybag.StatusRisk
	} else {
		record.Status = luckybag.StatusNormal
	}
	err := luckybag.DB().Create(record).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	key := keys.KeyLuckyBagJoinNum1.Format(param.lb.ID)
	pipe := service.Redis.TxPipeline()
	inrCmd := pipe.Incr(key)
	pipe.Expire(key, 24*time.Hour) // 目前福袋最长时长 15 分钟，redis 值仅做福袋未结束时展示 join_num 查询使用
	_, err = pipe.Exec()
	if err != nil {
		logger.WithField("lucky_bag_id", param.lb.ID).Error(err)
		// PASS
	} else {
		param.lb.JoinNum = inrCmd.Val()
	}
	return nil
}

type messagePayload struct {
	Type    string           `json:"type"`
	Event   string           `json:"event"`
	RoomID  int64            `json:"room_id"`
	MsgID   string           `json:"msg_id"`
	Message string           `json:"message"`
	User    *liveuser.Simple `json:"user"`
	Bubble  *bubble.Simple   `json:"bubble"`
}

func (param *joinParams) sendMessage() {
	// 口令消息不加贡献值、热度、直播间消息数
	status := messagelimit.Status(param.r, param.liveUser, param.lb.Message)
	if status != models.MessageStatusNormal {
		return
	}
	err := userapi.Broadcast(param.r.RoomID, &messagePayload{
		Type:    liveim.TypeMessage,
		Event:   liveim.EventNew,
		RoomID:  param.r.RoomID,
		MsgID:   param.msgID,
		Message: param.lb.Message,
		User:    param.liveUser,
		Bubble:  param.b,
	})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *joinParams) buildResp() {
	lb := luckybag.NewFullInfo(param.lb)
	lb.JoinStatus = util.NewInt(1)
	param.resp = &joinResp{
		LuckyBag: lb,
		MsgID:    param.msgID,
		Message:  param.lb.Message,
		User:     param.liveUser,
		Bubble:   param.b,
	}
}
