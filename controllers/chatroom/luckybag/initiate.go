package luckybag

import (
	"strings"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag/databus"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type initiateParams struct {
	RoomID       int64  `form:"room_id" json:"room_id"`
	Type         int    `form:"type" json:"type"`
	RewardType   int    `form:"reward_type" json:"reward_type"`
	PrizeNum     int    `form:"prize_num" json:"prize_num"`
	TargetType   int    `form:"target_type" json:"target_type"`
	Keyword      string `form:"keyword" json:"keyword"`
	Countdown    int64  `form:"countdown" json:"countdown"` // 倒计时（毫秒）
	PrizeDramaID int64  `form:"prize_drama_id" json:"prize_drama_id"`
	RewardIntro  string `form:"reward_intro" json:"reward_intro"`

	userID int64 // 登录用户 ID

	c                *handler.Context
	config           *params.LuckyBagConfig
	room             *room.Room
	dramaDetail      *userapi.DramaInfo
	record           *luckybag.InitiateRecord
	vipUser          bool // 发福袋的用户是否是会员
	prizeActualPrice int  // 奖品的实际支付单价 (单位: 钻)
}

type luckyBagInitiateResp struct {
	LuckyBag *luckybag.FullInfo `json:"lucky_bag"`
}

// ActionLuckyBagInitiate 发起福袋
/**
 * @api {post} /api/v2/chatroom/luckybag/initiate 发起福袋
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {number=1,2} type 福袋类型 1: 广播剧福袋; 2: 实物福袋
 * @apiParam {number=1,2,3} reward_type 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
 * @apiParam {Number} prize_num 奖品数量
 * @apiParam {number=0,1,2,3} target_type 参与对象类型 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝
 * @apiParam {String} keyword 参与口令
 * @apiParam {Number} countdown 倒计时（毫秒）
 * @apiParam {Number} [prize_drama_id] 剧集 ID, 广播剧福袋必传
 * @apiParam {String} [reward_intro] 奖励内容备注
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "lucky_bag": {
 *         "lucky_bag_id": 1,
 *         "status": 0, // 福袋状态 0: 待开奖; 1: 已开奖（发奖中）; 2: 已开奖（已发奖）
 *         "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *         "reward_type": 2, // 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *         "prize_name": "签名照", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示封面图时，需要客户端处理加书名号
 *         "prize_num": 1, // 奖品数量
 *         "prize_price": 100, // 福袋每份价值，只有剧集福袋有价格，单位：钻
 *         "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图
 *         "join_num": 0, // 参与人数
 *         "increase_num": 0, // 新增人数
 *         "target_type": 0, // 参与对象类型 0: 所有人, 1: 关注, 2: 粉丝勋章, 3: 超级粉丝
 *         "remain_duration": 1800, // 剩余时间（毫秒）
 *         "keyword": "点点关注抽福袋" // 参与默认口令
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 发起福袋成功房间内消息:
 *   // 一个直播间只能有一个未开奖的福袋, 如果展示中的福袋没有收到开奖消息, 但收到新的福袋消息时直接显示新的福袋
 *   {
 *     "type": "lucky_bag",
 *     "event": "new",
 *     "room_id": 65261414,
 *     "lucky_bag": { // 当前直播间福袋信息
 *       "lucky_bag_id": 1, // 福袋 ID
 *       "type": 1, // 福袋类型，1: 剧集福袋; 2: 实物福袋
 *       "status": 1, // 福袋状态 1: 待开奖; 2: 已开奖
 *       "image_url": "https://static-test.maoercdn.com/luckybag.png", // 福袋图标
 *       "new_image_url": "https://static-test.maoercdn.com/live/luckybag/icon-v2.png", // 新福袋图标，仅供 APP 新版本使用，如不存在应使用 image_url 字段
 *       "big_image_url": "https://static-test.maoercdn.com/luckybag-big.png", // 福袋大图标
 *       "prize_icon_url": "https://static-test.maoercdn.com/icon.png", // 封面图，下发就显示在福袋上
 *       "remain_duration": 18000, // 开奖、公示剩余时间，如果倒计时结束 10s 后没有收到开奖消息总是隐藏，单位：毫秒
 *       "has_more": true // 是否显示更多福袋入口: true: 显示; false: 隐藏
 *     }
 *   }
 */
func ActionLuckyBagInitiate(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newInitiateParams(c)
	if err != nil {
		return nil, "", err
	}

	switch param.Type {
	case luckybag.TypeDrama: // 广播剧福袋
		return param.luckyBagDrama()
	case luckybag.TypeEntity: // 实物福袋
		return param.luckyBagEntity()
	default:
		return nil, "", actionerrors.ErrParams
	}
}

func newInitiateParams(c *handler.Context) (*initiateParams, error) {
	var param initiateParams
	err := c.Bind(&param)
	if err != nil {
		return nil, err
	}

	if param.RoomID <= 0 || param.Keyword == "" {
		return nil, actionerrors.ErrParams
	}

	param.Keyword = strings.TrimSpace(param.Keyword)
	if len(param.Keyword) == 0 {
		return nil, actionerrors.ErrParams
	}
	if util.UTF8Width(param.Keyword) > 30 {
		return nil, actionerrors.NewErrForbidden("口令最多 15 个汉字")
	}

	if param.Type == luckybag.TypeEntity {
		param.RewardIntro = strings.TrimSpace(param.RewardIntro)
		if len(param.RewardIntro) == 0 {
			return nil, actionerrors.ErrParams
		}
		if util.UTF8Width(param.RewardIntro) > 30 {
			return nil, actionerrors.NewErrForbidden("奖品备注最多 15 个汉字")
		}
	}

	param.config, err = params.FindLuckyBag()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !param.config.IsOpen() {
		return nil, actionerrors.NewErrForbidden("福袋玩法未开启")
	}

	param.room, err = room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.userID = c.UserID()
	if param.room.CreatorID != param.userID {
		return nil, actionerrors.NewErrForbidden("只有主播才可以发福袋")
	}
	if param.room.IsBan() {
		return nil, actionerrors.NewErrForbidden("您的直播间已被封禁，暂时无法操作")
	}
	if param.room.Status.Open == room.StatusOpenFalse {
		return nil, actionerrors.NewErrForbidden("只有开播直播间才可以发福袋")
	}

	// 判断直播间是否在福袋黑名单中
	exists, err := application.IsRoomLuckyBagInitiateBlocked(param.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if exists {
		return nil, actionerrors.NewErrForbidden("您当前无法发起福袋玩法")
	}

	record, err := luckybag.FindLatestInitiateRecord(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if record != nil && record.Status == luckybag.StatusPending {
		// TODO: 处理长时间未开奖的情况
		return nil, actionerrors.NewErrForbidden("已有正在进行的福袋，请等待结束后再试")
	}

	param.c = c
	return &param, nil
}

func (param *initiateParams) luckyBagDrama() (handler.ActionResponse, string, error) {
	if param.PrizeDramaID <= 0 {
		return nil, "", actionerrors.ErrParams
	}

	count, err := luckybag.InitiateDailyCount(param.RoomID, param.Type)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if param.config.LuckyBagDrama.InitiateLimitDaily <= count {
		return nil, "", actionerrors.NewErrForbidden("今日广播剧福袋发放次数已达上限，请明日再试哦")
	}

	config := param.config.LuckyBagDrama
	if param.PrizeNum <= 0 || param.PrizeNum > config.MaxPrizeNum {
		return nil, "", actionerrors.NewErrForbidden("奖品数量不符合规范，请检查后重试")
	}
	if !config.IsRewardTypeValid(param.RewardType) {
		return nil, "", actionerrors.NewErrForbidden("奖励类型不符合规范，请检查后重试")
	}
	if !config.IsTargetTypeValid(param.TargetType) {
		return nil, "", actionerrors.NewErrForbidden("参与目标类型不符合规范，请检查后重试")
	}
	if goutil.HasElem([]int{luckybag.TargetTypeMedal, luckybag.TargetTypeSuperFan}, param.TargetType) &&
		param.room.Medal == nil {
		return nil, "", actionerrors.NewErrForbidden("开通粉丝勋章后才可选择该参与对象哦")
	}

	checkTextResult, err := param.checkText(param.Keyword)
	if err != nil {
		return nil, "", err
	}
	if checkTextResult.NotPass {
		return nil, "", actionerrors.ErrLuckyBagMessageIllegal("福袋口令含有违规信息")
	}
	if checkTextResult.HasLabelEvil {
		return nil, "", actionerrors.ErrLuckyBagMessageIllegal("福袋口令含有敏感信息")
	}

	param.dramaDetail, err = userapi.GetDramaInfo(param.c.UserContext(), param.PrizeDramaID, param.userID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if param.dramaDetail == nil {
		return nil, "", actionerrors.NewErrForbidden("该剧集暂不支持发放福袋")
	}
	param.vipUser, err = userapi.IsVipUser(param.c.UserContext(), param.userID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	param.prizeActualPrice = param.getDramaActualPrice()
	ok, err := param.canSendLuckyBag()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, "", actionerrors.NewErrForbidden("该剧集暂不支持发放福袋")
	}

	// 算出价格才能检查倒计时
	price := param.prizeActualPrice * param.PrizeNum
	if !config.IsCountdownValid(param.Countdown, int64(price)) {
		return nil, "", actionerrors.NewErrForbidden("倒计时不符合规范，请检查后重试")
	}

	err = servicedb.Tx(luckybag.DB(), func(tx *gorm.DB) error {
		param.record, err = param.createRecord(tx)
		if err != nil {
			return err
		}

		resp, err := param.buyDramaLuckyBag()
		if err != nil {
			return err
		}

		return luckybag.UpdateTransactionID(tx, param.record.ID, resp.TransactionID)
	})
	if err != nil {
		if v, ok := err.(*mrpc.ClientError); ok && v.Code == actionerrors.CodeBalanceNotEnough {
			return nil, "", actionerrors.ErrLuckyBagRewardBalanceNotEnough
		}
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	return param.buildResp()
}

// isVipDiscount 喵喵福袋中的剧集是否满足会员折扣条件
func (param *initiateParams) isVipDiscount() bool {
	// NOTICE: 喵喵福袋暂不支持折扣购买剧集
	return false
	// return param.vipUser && param.dramaDetail.Price >= userapi.MinVipDiscountPrice && param.dramaDetail.VipDiscount != nil
}

// getDramaActualPrice 获取整剧付费剧集的实际支付价格
func (param *initiateParams) getDramaActualPrice() int {
	if param.isVipDiscount() {
		return param.dramaDetail.VipDiscount.Price
	}
	return param.dramaDetail.Price
}

// canSendLuckyBag 判断剧集是否可以发福袋使用
func (param *initiateParams) canSendLuckyBag() (bool, error) {
	if param.prizeActualPrice == 0 || param.dramaDetail.Checked != userapi.DramaCheckedPass || param.dramaDetail.PayType != userapi.DramaPayTypeDrama {
		return false, nil
	}

	if param.dramaDetail.Refined.IsSet(userapi.DramaRefinedNoJapanSale) {
		ipInfo, err := goclient.GetIPInfo(param.c, param.c.ClientIP())
		if err != nil {
			return false, err
		}
		if ipInfo.CountryCode == userapi.CountryCodeJP {
			return false, nil
		}
	}
	return true, nil
}

func (param *initiateParams) luckyBagEntity() (handler.ActionResponse, string, error) {
	exists, err := application.IsRoomCustomLuckyBagInitiateAllowed(param.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if !exists {
		return nil, "", actionerrors.NewErrForbidden("无法发送实物福袋")
	}

	config := param.config.LuckyBagEntity
	if param.PrizeNum <= 0 || param.PrizeNum > config.MaxPrizeNum {
		return nil, "", actionerrors.NewErrForbidden("奖品数量不符合规范，请检查后重试")
	}
	if !config.IsRewardTypeValid(param.RewardType) {
		return nil, "", actionerrors.NewErrForbidden("奖励类型不符合规范，请检查后重试")
	}
	if !config.IsTargetTypeValid(param.TargetType) {
		return nil, "", actionerrors.NewErrForbidden("参与目标类型不符合规范，请检查后重试")
	}
	if goutil.HasElem([]int{luckybag.TargetTypeMedal, luckybag.TargetTypeSuperFan}, param.TargetType) &&
		param.room.Medal == nil {
		return nil, "", actionerrors.NewErrForbidden("开通粉丝勋章后才可选择该参与对象哦")
	}
	// 实物福袋没有价格
	if !config.IsCountdownValid(param.Countdown, 0) {
		return nil, "", actionerrors.NewErrForbidden("倒计时不符合规范，请检查后重试")
	}

	checkTextResult, err := param.checkText(param.Keyword)
	if err != nil {
		return nil, "", err
	}
	if checkTextResult.NotPass {
		return nil, "", actionerrors.ErrLuckyBagMessageIllegal("福袋口令含有违规信息")
	}
	if checkTextResult.HasLabelEvil {
		return nil, "", actionerrors.ErrLuckyBagMessageIllegal("福袋口令含有敏感信息")
	}

	checkTextResult, err = param.checkText(param.RewardIntro)
	if err != nil {
		return nil, "", err
	}
	if checkTextResult.NotPass {
		return nil, "", actionerrors.ErrLuckyBagMessageIllegal("福袋备注含有违规信息")
	}
	if checkTextResult.HasLabelEvil {
		return nil, "", actionerrors.ErrLuckyBagMessageIllegal("福袋备注含有敏感信息")
	}

	err = servicedb.Tx(luckybag.DB(), func(tx *gorm.DB) error {
		param.record, err = param.createRecord(tx)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	return param.buildResp()
}

func (param *initiateParams) checkText(text string) (*userapi.CheckTextIMResult, error) {
	msgID := util.BuildMsgID(param.userID, param.RoomID, text,
		config.Conf.Params.General.FeedbackKey, param.c.Equip().OS) // 文字违规和敏感信息检查
	input := gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			UserID:    param.userID,
			EquipID:   param.c.EquipID(),
			IP:        param.c.ClientIP(),
			API:       param.c.Request().URL.Path,
			UserAgent: param.c.UserAgent(),
			Referer:   param.c.Request().Referer(),
			Content:   text,
		},
		RoomID:        param.RoomID,
		RoomCatalogID: param.room.CatalogID,
		RoomCreatorID: param.room.CreatorID,
		MsgID:         msgID,
		UserIsAdmin:   true,
		UserHasMedal:  false, // 主播没有自己直播间勋章
		Scene:         gaia.IMSceneLuckyBag,
	}
	return userapi.CheckTextIM(param.c.UserContext(), input)
}

func (param *initiateParams) createRecord(tx *gorm.DB) (*luckybag.InitiateRecord, error) {
	now := goutil.TimeNow().Unix()
	record := &luckybag.InitiateRecord{
		UserID:           param.userID,
		RoomID:           param.RoomID,
		CreatorID:        param.room.CreatorID,
		Type:             param.Type,
		RewardType:       param.RewardType,
		Status:           luckybag.StatusPending,
		Num:              param.PrizeNum,
		TargetType:       param.TargetType,
		Message:          param.Keyword,
		StartTime:        now,
		ScheduledEndTime: now + (param.Countdown / 1000),
		MoreInfo: &luckybag.MoreInfo{
			CreatorUsername: param.room.CreatorUsername,
		},
	}
	switch param.Type {
	case luckybag.TypeDrama:
		dramaDetail := param.dramaDetail
		record.Name = dramaDetail.Name
		record.PrizeDramaID = param.PrizeDramaID
		record.PrizeIPRID = dramaDetail.IPRID
		record.MoreInfo.PrizePrice = int64(param.prizeActualPrice)
		record.MoreInfo.PrizeIPRName = dramaDetail.IPRName
		if param.isVipDiscount() {
			record.MoreInfo.PrizeVipDiscount = &luckybag.PrizeVipDiscountInfo{
				DiscountPrice: param.prizeActualPrice * param.PrizeNum,
				OriginalPrice: param.dramaDetail.Price * param.PrizeNum,
				Rate:          param.dramaDetail.VipDiscount.Rate,
				Num:           param.PrizeNum,
			}
		}
		// 剧集福袋商品图标在直播间角标也显示
		record.MoreInfo.PrizeIcon, _ = service.Storage.Format(dramaDetail.CoverURL) // 入库使用
		record.MoreInfo.PrizeIconURL = dramaDetail.CoverURL                         // ws 消息使用
		record.MoreInfo.PrizeDramaCoverColor = goutil.NewInt64(dramaDetail.CoverColor)
	case luckybag.TypeEntity:
		record.Name = param.RewardIntro
		config := param.config.LuckyBagEntity
		record.MoreInfo.PrizeIcon, _ = config.PrizeIconURL(param.RewardType) // 实物福袋商品图标只在福袋弹窗详情页展示，直接入口即可
	}

	// 参与类型为所有人不需要获取目标人数
	if param.TargetType != luckybag.TargetTypeAll {
		curNum, _, err := luckybag.FindTargetNum(record)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		record.MoreInfo.StartTargetNum = goutil.NewInt64(curNum)
	}

	err := record.CreateTx(tx)
	if err != nil {
		return nil, err
	}
	return record, nil
}

func (param *initiateParams) buyDramaLuckyBag() (*userapi.BalanceResp, error) {
	buyParam := userapi.BuyLiveGoodsParam{
		BuyerID:   param.userID,
		GoodsType: userapi.GoodsTypeLuckyBag,
		PackageInfo: &userapi.PackageInfo{
			ID:    param.record.ID,
			Title: param.config.LuckyBagDrama.Name,
			Price: param.prizeActualPrice * param.PrizeNum,
			Num:   1,
		},
		Goods: []userapi.LiveGoodsElem{
			{
				ID:              param.dramaDetail.ID,
				Title:           param.dramaDetail.Name,
				Price:           param.prizeActualPrice,
				Num:             param.PrizeNum,
				TransactionType: userapi.TransactionTypeDrama,
			},
		},
		Noble:     0, // 不可用贵族钻石购买
		UserAgent: param.c.UserAgent(),
		EquipID:   param.c.EquipID(),
		BUVID:     param.c.BUVID(),
		IP:        param.c.ClientIP(),
	}
	resp, err := userapi.BuyLiveGoods(param.c.UserContext(), buyParam, param.room.Status.OpenLogID)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

type initiateNotify struct {
	Type     string                    `json:"type"`
	Event    string                    `json:"event"`
	RoomID   int64                     `json:"room_id"`
	LuckyBag *luckybag.RoomMetaMessage `json:"lucky_bag"`
}

func (param *initiateParams) notify() {
	message := param.record.NewRoomMetaMessage(param.userID, param.config.ImageURL, param.config.NewImageURL, param.config.BigImageURL)
	in := initiateNotify{
		Type:     liveim.TypeLuckyBag,
		Event:    liveim.EventLuckyBagNew,
		RoomID:   param.RoomID,
		LuckyBag: message,
	}
	err := userapi.Broadcast(param.RoomID, in, &userapi.BroadcastOption{
		Priority: userapi.BroadcastPriorityPurchased,
	})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *initiateParams) buildResp() (handler.ActionResponse, string, error) {
	goutil.Go(func() {
		param.notify()
		databus.DelayDrawLuckyBag(param.record)
	})

	info := luckybag.NewFullInfo(param.record)
	info.IncreaseNum = goutil.NewInt64(0)
	return &luckyBagInitiateResp{
		LuckyBag: info,
	}, "", nil
}
