package luckybag

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	application "github.com/MiaoSiLa/live-service/models/mysql/liveapplication"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLuckyBagInitiate(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count := 0
	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		count++
		return []*scan.BaseCheckResult{
			{
				Pass: true,
			},
		}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URLGetVipUserIDs, func(input any) (any, error) {
		return userapi.GetVipUserIDsResp{}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URLGetDramaInfo, func(any) (any, error) {
		count++
		return handler.M{"drama": &userapi.DramaInfo{
			ID:         35,
			Name:       "辉子",
			CoverURL:   "https://static-test.missevan.com/6b6083011.jpg",
			CoverColor: 12434877,
			IPRID:      1,
			Price:      10,
			Checked:    userapi.DramaCheckedPass,
			PayType:    userapi.DramaPayTypeDrama,
		}}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIBuyGoods, func(input any) (any, error) {
		count++
		return userapi.BalanceResp{TransactionID: 123}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		return "success", nil
	})
	defer cancel()

	body := initiateParams{
		RoomID:       22489473,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		PrizeNum:     10,
		TargetType:   luckybag.TargetTypeAll,
		Keyword:      "test",
		Countdown:    180000,
		PrizeDramaID: 35,
	}

	err := luckybag.DB().Delete(&luckybag.InitiateRecord{}, "room_id = ?", body.RoomID).Error
	require.NoError(err)

	element := application.Element{
		ApplicationID: 3,
		ElementID:     body.RoomID,
	}
	err = service.LiveDB.Create(&element).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/", true, body)
	c.User().ID = 10
	resp, _, err := ActionLuckyBagInitiate(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(3, count)
	r := resp.(*luckyBagInitiateResp)
	require.NotNil(r.LuckyBag)
	record, err := luckybag.FindShowingInitiateRecordByID(r.LuckyBag.LuckyBagID)
	require.NoError(err)
	require.NotNil(record)
	assert.Equal(r.LuckyBag.LuckyBagID, record.ID)
	assert.Equal(body.Type, record.Type)

	err = luckybag.DB().Delete(&luckybag.InitiateRecord{}, "room_id = ?", body.RoomID).Error
	require.NoError(err)

	count = 0
	body.Type = luckybag.TypeEntity
	body.RewardType = luckybag.RewardTypeEntityDrama
	body.RewardIntro = "test"
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	c.User().ID = 10
	resp, _, err = ActionLuckyBagInitiate(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(2, count)
	r = resp.(*luckyBagInitiateResp)
	require.NotNil(r.LuckyBag)
	record, err = luckybag.FindShowingInitiateRecordByID(r.LuckyBag.LuckyBagID)
	require.NoError(err)
	require.NotNil(record)
	assert.Equal(r.LuckyBag.LuckyBagID, record.ID)
	assert.Equal(body.Type, record.Type)
}

func TestNewInitiateParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := initiateParams{
		RoomID:       22489473,
		Type:         luckybag.TypeDrama,
		RewardType:   luckybag.RewardTypeDrama,
		PrizeNum:     10,
		TargetType:   luckybag.TargetTypeAll,
		Keyword:      "一二三四五一二三四五一二三四五一二三四五",
		Countdown:    180000,
		PrizeDramaID: 35,
	}

	err := luckybag.DB().Delete(&luckybag.InitiateRecord{}, "room_id = ?", body.RoomID).Error
	require.NoError(err)

	c := handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newInitiateParams(c)
	assert.EqualError(err, "口令最多 15 个汉字")

	body.Keyword = "一二三四五一二三四五一二三四五"
	body.Type = luckybag.TypeEntity
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newInitiateParams(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.RewardIntro = "一二三四五一二三四五一二三四五一二三四五"
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newInitiateParams(c)
	assert.EqualError(err, "奖品备注最多 15 个汉字")

	body.RewardIntro = "一二三四五一二三四五一二三四五"
	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	_, err = newInitiateParams(c)
	assert.EqualError(err, "只有主播才可以发福袋")

	c = handler.NewTestContext(http.MethodPost, "/", true, body)
	c.User().ID = 10
	param, err := newInitiateParams(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestInitiateParams_luckyBagDrama(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count := 0
	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		count++
		return []*scan.BaseCheckResult{
			{
				Pass: true,
			},
		}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URLGetVipUserIDs, func(input any) (any, error) {
		return userapi.GetVipUserIDsResp{}, nil
	})
	defer cancel()

	expectedDetail := &userapi.DramaInfo{
		ID:         35,
		Name:       "辉子",
		CoverURL:   "https://static-test.missevan.com/6b6083011.jpg",
		CoverColor: 12434877,
		IPRID:      1,
		Price:      0,
		Checked:    userapi.DramaCheckedContractExpired,
		PayType:    userapi.DramaPayTypeDrama,
	}
	cancel = mrpc.SetMock(userapi.URLGetDramaInfo, func(any) (any, error) {
		count++
		return handler.M{"drama": expectedDetail}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIBuyGoods, func(input any) (any, error) {
		count++
		return userapi.BalanceResp{TransactionID: 123}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		return "success", nil
	})
	defer cancel()

	config, err := params.FindLuckyBag()
	require.NoError(err)
	param := initiateParams{
		RoomID: 9074512,
		Type:   luckybag.TypeDrama,
		config: config,
		room: &room.Room{
			Helper: room.Helper{
				CatalogID: 12,
				CreatorID: 9074509,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/", true, nil),
	}
	_, _, err = param.luckyBagDrama()
	assert.Equal(actionerrors.ErrParams, err)

	param.PrizeDramaID = expectedDetail.ID
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "奖品数量不符合规范，请检查后重试")

	param.PrizeNum = 51
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "奖品数量不符合规范，请检查后重试")

	param.PrizeNum = 10
	param.RewardType = luckybag.RewardTypeEntityDrama
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "奖励类型不符合规范，请检查后重试")

	param.RewardType = luckybag.RewardTypeDrama
	param.TargetType = 10
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "参与目标类型不符合规范，请检查后重试")

	count = 0
	param.TargetType = luckybag.TargetTypeAll
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "该剧集暂不支持发放福袋")
	assert.Equal(2, count)

	count = 0
	expectedDetail.Checked = userapi.DramaCheckedPass
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "该剧集暂不支持发放福袋")
	assert.Equal(2, count)

	expectedDetail.Price = 1
	param.Countdown = 600000
	_, _, err = param.luckyBagDrama()
	assert.EqualError(err, "倒计时不符合规范，请检查后重试")

	count = 0
	expectedDetail.Price = 200
	resp, _, err := param.luckyBagDrama()
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(*luckyBagInitiateResp)
	require.NotNil(r.LuckyBag)
	assert.Equal(3, count)
	record, err := luckybag.FindShowingInitiateRecordByID(r.LuckyBag.LuckyBagID)
	require.NoError(err)
	require.NotNil(record)
	assert.Equal(r.LuckyBag.LuckyBagID, record.ID)
	assert.Equal(luckybag.TypeDrama, record.Type)
}

func TestInitiateParams_luckyBagEntity(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	count := 0
	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		count++
		return []*scan.BaseCheckResult{
			{
				Pass: true,
			},
		}, nil
	})
	defer cancel()

	cancel = mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		return "success", nil
	})
	defer cancel()

	config, err := params.FindLuckyBag()
	require.NoError(err)

	param := initiateParams{
		RoomID: 9074512,
		config: config,
		Type:   luckybag.TypeEntity,
		room: &room.Room{
			Helper: room.Helper{
				CatalogID: 12,
				CreatorID: 9074509,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/", true, nil),
	}
	err = service.LiveDB.Delete(luckybag.InitiateRecord{},
		"room_id = ?", param.RoomID).Error
	require.NoError(err)
	err = service.LiveDB.Delete(&application.Element{}, "element_id = ?", param.RoomID).Error
	require.NoError(err)

	_, _, err = param.luckyBagEntity()
	assert.EqualError(err, "无法发送实物福袋")

	element := application.Element{
		ApplicationID: 3,
		ElementID:     param.RoomID,
	}
	err = service.LiveDB.Create(&element).Error
	require.NoError(err)
	_, _, err = param.luckyBagEntity()
	assert.EqualError(err, "奖品数量不符合规范，请检查后重试")

	param.PrizeNum = 51
	_, _, err = param.luckyBagEntity()
	assert.EqualError(err, "奖品数量不符合规范，请检查后重试")

	param.PrizeNum = 10
	param.RewardType = 10
	_, _, err = param.luckyBagEntity()
	assert.EqualError(err, "奖励类型不符合规范，请检查后重试")

	param.RewardType = luckybag.RewardTypeEntityPersonal
	param.TargetType = 10
	_, _, err = param.luckyBagEntity()
	assert.EqualError(err, "参与目标类型不符合规范，请检查后重试")

	param.TargetType = luckybag.TargetTypeAll
	param.Countdown = 10000
	_, _, err = param.luckyBagEntity()
	assert.EqualError(err, "倒计时不符合规范，请检查后重试")

	param.Countdown = 180000
	resp, _, err := param.luckyBagEntity()
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(*luckyBagInitiateResp)
	require.NotNil(r.LuckyBag)
	assert.Equal(2, count)
	record, err := luckybag.FindShowingInitiateRecordByID(r.LuckyBag.LuckyBagID)
	require.NoError(err)
	require.NotNil(record)
	assert.Equal(r.LuckyBag.LuckyBagID, record.ID)
	assert.Equal(luckybag.TypeEntity, record.Type)
}

func TestInitiateParams_CreateRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := initiateParams{
		Type:       luckybag.TypeDrama,
		userID:     123,
		RoomID:     456,
		RewardType: luckybag.RewardTypeDrama,
		PrizeNum:   10,
		TargetType: luckybag.TargetTypeAll,
		Keyword:    "12312",
		Countdown:  180000,
		dramaDetail: &userapi.DramaInfo{
			Name:       "test",
			IPRID:      1,
			Price:      10,
			CoverURL:   "http://test.com/cover.jpg",
			CoverColor: 5929352,
		},
		room: &room.Room{
			Helper: room.Helper{
				CreatorID:       123,
				CreatorUsername: "asdsa",
			},
		},
		prizeActualPrice: 8,
	}
	record, err := param.createRecord(luckybag.DB())
	require.NoError(err)
	require.NotNil(record)
	assert.EqualValues(luckybag.TypeDrama, record.Type)
	assert.EqualValues(param.dramaDetail.IPRID, record.PrizeIPRID)
	assert.EqualValues(123, record.UserID)
	assert.EqualValues(param.prizeActualPrice, record.MoreInfo.PrizePrice)
}

func TestInitiateParams_checkText(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URLScanIM, func(any) (any, error) {
		return []*scan.BaseCheckResult{
			{
				Pass:   true,
				Labels: []string{scan.LabelEvil},
			},
		}, nil
	})
	defer cancel()

	param := initiateParams{
		RoomID: 1124568,
		room: &room.Room{
			Helper: room.Helper{
				CatalogID: 12,
				CreatorID: 9074509,
			},
		},
		c: handler.NewTestContext(http.MethodPost, "/", true, nil),
	}
	checkTextResult, err := param.checkText("odsaa")
	require.NoError(err)
	assert.False(checkTextResult.NotPass)
	assert.True(checkTextResult.HasLabelEvil)
}

func TestInitiateParams_buyDramaLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	config, err := params.FindLuckyBag()
	require.NoError(err)

	oid, _ := primitive.ObjectIDFromHex("5ab9d5d9bc9b53298ce5a5a5")
	buyParam := userapi.BuyLiveGoodsParam{
		BuyerID:   9074509,
		GoodsType: userapi.GoodsTypeLuckyBag,
		PackageInfo: &userapi.PackageInfo{
			ID:    12,
			Title: config.LuckyBagDrama.Name,
			Price: 3000,
			Num:   1,
		},
		Goods: []userapi.LiveGoodsElem{
			{
				ID:              12,
				Title:           "测试剧集",
				Price:           100,
				Num:             30,
				TransactionType: userapi.TransactionTypeDrama,
			},
		},
		Noble:         0, // 不可用贵族钻石购买
		LiveOpenLogID: oid.Hex(),
	}

	isCalled := false
	cancel := mrpc.SetMock(userapi.URIBuyGoods, func(input any) (any, error) {
		assert.Equal(buyParam, input)
		isCalled = true
		return userapi.BalanceResp{TransactionID: 123}, nil
	})
	defer cancel()

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	param := initiateParams{
		PrizeNum: 30,
		record:   &luckybag.InitiateRecord{ID: 12},
		dramaDetail: &userapi.DramaInfo{
			ID:    12,
			Name:  "测试剧集",
			Price: 100,
		},
		config: config,
		room: &room.Room{
			Helper: room.Helper{
				Status: room.Status{
					OpenLogID: &oid,
				},
			},
		},
		userID:           9074509,
		c:                c,
		prizeActualPrice: 100,
	}
	resp, err := param.buyDramaLuckyBag()
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(123, resp.TransactionID)
	assert.True(isCalled)
}

func TestInitiateParams_notify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	isCalled := false
	cancel := mrpc.SetMock(userapi.URIIMBroadcast, func(input any) (output any, err error) {
		var body struct {
			RoomID  int64          `json:"room_id"`
			Payload initiateNotify `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		require.NotNil(body.Payload)
		assert.Equal(liveim.TypeLuckyBag, body.Payload.Type)
		assert.Equal(liveim.EventLuckyBagNew, body.Payload.Event)
		assert.NotNil(body.Payload.LuckyBag)
		isCalled = true
		return "success", nil
	})
	defer cancel()

	config, err := params.FindLuckyBag()
	require.NoError(err)
	param := initiateParams{
		RoomID: 18113498,
		record: &luckybag.InitiateRecord{
			ID:               11,
			Type:             luckybag.TypeDrama,
			Status:           luckybag.StatusPending,
			ScheduledEndTime: goutil.TimeNow().Add(time.Minute).Unix(),
		},
		config: config,
	}
	param.notify()
	assert.True(isCalled)
}

func TestInitiateParams_buildResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().Add(time.Minute).Unix()
	record := &luckybag.InitiateRecord{
		ID:               12,
		Type:             luckybag.TypeDrama,
		Status:           luckybag.StatusPending,
		ScheduledEndTime: now,
		RewardType:       luckybag.RewardTypeDrama,
		TargetType:       luckybag.TargetTypeAll,
		Name:             "Drama",
		Num:              10,
		Message:          "keyword",

		MoreInfo: &luckybag.MoreInfo{
			PrizePrice:   10,
			PrizeIconURL: "oss://test/1.png",
		},
	}

	config, err := params.FindLuckyBag()
	require.NoError(err)

	param := initiateParams{
		record: record,
		config: config,
		userID: 9074509,
	}
	resp, _, err := param.buildResp()
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(*luckyBagInitiateResp)
	require.NotNil(r.LuckyBag)
	assert.Equal(record.ID, r.LuckyBag.LuckyBagID)
}

func TestInitiateParams_isVipDiscount(t *testing.T) {
	require := require.New(t)

	// 测试会员折扣不作用于福袋中的剧集
	param := &initiateParams{
		vipUser: false,
		dramaDetail: &userapi.DramaInfo{
			ID:          12,
			Name:        "测试剧集",
			Price:       userapi.MinVipDiscountPrice - 1,
			Checked:     userapi.DramaCheckedContractExpired,
			PayType:     userapi.DramaPayTypeDrama,
			VipDiscount: &userapi.VipDiscountInfo{Rate: 0.5, Price: 2},
		},
	}
	require.False(param.isVipDiscount())

	// 测试不是会员
	// require.False(param.isVipDiscount())

	// 测试是会员，但价格低于最低会员折扣价格
	// param.vipUser = true
	// require.False(param.isVipDiscount())

	// 测试是会员，价格高于最低会员折扣价格，剧集没有折扣信息
	// param.dramaDetail.Price = userapi.MinVipDiscountPrice
	// require.False(param.isVipDiscount())

	// 测试是会员，价格高于最低会员折扣价格，剧集有折扣信息
	// param.dramaDetail.VipDiscount = &userapi.VipDiscountInfo{Rate: 0.5, Price: 2}
	// require.True(param.isVipDiscount())
}

func TestInitiateParams_getDramaActualPrice(t *testing.T) {
	require := require.New(t)

	// 测试会员折扣价格不作用于福袋中的剧集
	param := &initiateParams{
		vipUser: false,
		dramaDetail: &userapi.DramaInfo{
			ID:          12,
			Name:        "测试剧集",
			Price:       userapi.MinVipDiscountPrice - 1,
			Checked:     userapi.DramaCheckedContractExpired,
			PayType:     userapi.DramaPayTypeDrama,
			VipDiscount: &userapi.VipDiscountInfo{Rate: 0.5, Price: 2},
		},
	}
	actualPrice := param.getDramaActualPrice()
	require.EqualValues(userapi.MinVipDiscountPrice-1, actualPrice)

	// 测试不是会员
	// actualPrice := param.getDramaActualPrice()
	// require.EqualValues(userapi.MinVipDiscountPrice-1, actualPrice)

	// 测试是会员，但价格低于最低会员折扣价格
	// param.vipUser = true
	// actualPrice = param.getDramaActualPrice()
	// require.EqualValues(userapi.MinVipDiscountPrice-1, actualPrice)

	// 测试是会员，价格高于最低会员折扣价格，剧集没有折扣信息
	// param.dramaDetail.Price = userapi.MinVipDiscountPrice
	// actualPrice = param.getDramaActualPrice()
	// require.EqualValues(param.dramaDetail.Price, actualPrice)

	// 测试是会员，价格高于最低会员折扣价格，剧集有折扣信息
	// param.dramaDetail.VipDiscount = &userapi.VipDiscountInfo{Rate: 0.5, Price: 2}
	// actualPrice = param.getDramaActualPrice()
	// require.EqualValues(param.dramaDetail.VipDiscount.Price, actualPrice)
}

func TestInitiateParams_canSendLuckyBag(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	countryCode := userapi.CountryCodeJP
	cancel := mrpc.SetMock("go://util/geoip", func(input interface{}) (output interface{}, err error) {
		return handler.M{
			"country_code": countryCode,
		}, nil
	})
	defer cancel()

	c := handler.NewTestContext(http.MethodPost, "/", true, nil)
	c.SetClientIP("***********")
	param := &initiateParams{
		record: &luckybag.InitiateRecord{ID: 12},
		dramaDetail: &userapi.DramaInfo{
			ID:    12,
			Name:  "测试剧集",
			Price: 100,
		},
		c:                c,
		prizeActualPrice: 0,
	}

	// 测试实际支付价格为 0
	ok, err := param.canSendLuckyBag()
	require.NoError(err)
	assert.False(ok)

	// 测试剧集实际支付价格不为 0 但未审核通过
	param.prizeActualPrice = 100
	ok, err = param.canSendLuckyBag()
	require.NoError(err)
	assert.False(ok)

	// 测试剧集实际支付价格不为 0，审核通过，但不是整剧付费剧
	param.dramaDetail.Checked = userapi.DramaCheckedPass
	ok, err = param.canSendLuckyBag()
	require.NoError(err)
	assert.False(ok)

	// 测试剧集实际支付价格不为 0，审核通过且是整剧付费剧
	param.dramaDetail.PayType = userapi.DramaPayTypeDrama
	ok, err = param.canSendLuckyBag()
	require.NoError(err)
	assert.True(ok)

	// 测试剧集实际支付价格不为 0，审核通过且是整剧付费剧，但日本地区禁售
	param.dramaDetail.Refined.Set(userapi.DramaRefinedNoJapanSale)
	ok, err = param.canSendLuckyBag()
	require.NoError(err)
	assert.False(ok)

	// 测试剧集实际支付价格不为 0，审核通过且是整剧付费剧，日本地区禁售，但不是在日本购买
	countryCode = "CN"
	ok, err = param.canSendLuckyBag()
	require.NoError(err)
	assert.True(ok)
}
