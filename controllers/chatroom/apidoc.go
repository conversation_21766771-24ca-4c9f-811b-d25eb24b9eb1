package chatroom

// 存放一些跟接口无关的 apidoc

/**
 * @api {post} /effect 直播间广播特效播放
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 直播间广播特效:
 *   {
 *     "type": "effect",
 *     "event": "show",
 *     "room_id": 65261414,
 *     "effect": { // 礼物特效、贵族特效、座驾特效规则与此处相同
 *       "effect_url": "*.mp4;*.png;*.svga",
 *       "web_effect_url": "*.mp4;*.webm;*.png;*.svga"
 *     },
 *     "effect_layer": "special" // 特效展示层，special (或不传): 特殊层；gift: 礼物层
 *   }
 */

/**
 * @api {post} /member/join_queue 进场消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 进场消息:
 *   {
 *     "type": "member",
 *     "event": "join_queue",
 *     "room_id": 65261414,
 *     "queue": [{
 *       "user_id": 12,
 *       "username": "test12",
 *       "iconurl": "http://icon.png",
 *       "titles": [], // titles 同其他
 *       "vehicle": { // 进场座驾，没有座驾则不会下发
 *         "effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga", // 安卓 5.5.0 因为 MP4 支持有问题，所以不是优先取 mp4，而是按顺序取
 *         "web_effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga",
 *         "effect_duration": 10000,
 *         "message_bar": {
 *           "message": "<b>某某骑着某座驾入场</b>", // 进场消息需要支持 html
 *           "image_url": "http://aaa_1_2_3_4.png"
 *         }
 *       },
 *       "custom_welcome_message": { // 自定义进场欢迎语，在用户没有自定义或者有 entry_bubble 时不下发，后续会改为都在 entry_bubble.welcome_message.text 直接下发
 *         "text": "欢迎光临本直播间"
 *       },
 *       "entry_bubble": { // 进场通知，没有进场通知外观则不会下发，后续可能只下发 welcome_message.text，客户端需要支持在该情况下正常展示进场欢迎语
 *         "image_url": "http://entry_image.png",
 *         "entry_style": 0, // 进场通知样式，0: 默认（可不下发），1: 从左往右展开（黑卡 3、4 使用）
 *         "welcome_message": { // 进场通知的文案
 *           "text": "欢迎 XXX 用户进入直播间", // 用户的欢迎语，用户有自定义时改为下发自定义的欢迎语
 *           "colors": "#000000;#FFFFFF", // 文案的颜色，渐变色用分号区分两个颜色，非渐变色则只下发一个颜色
 *           "shine": 0 // 是否闪光，1 为闪光，0 为不闪光，字段不存在则默认不闪光
 *         }
 *       }
 *     }]
 *   }
 */

/**
 * @api {post} /room/join im 加入房间成功消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiParamExample {json} 请求加入房间指令:
 *   {
 *     "uuid": "32772286-c1be-4ec7-9d13-2d25fd2629c1",
 *     "action": "join",
 *     "room_id": 123,
 *     "reconnect": 1, // 是否是重连，重连不会广播进场消息。0：不是重连；1：重连
 *   }
 *
 * @apiSuccessExample {json} im 加入房间成功消息:
 *   {
 *     "type": "room",
 *     "event": "join",
 *     "room_id": 65261414,
 *     "uuid": "123-456-789",
 *     "info": {
 *       "user": {
 *         "user_id": 12,
 *         "username": "test12",
 *         "iconurl": "http://icon.png",
 *         "titles": [], // titles 同其他
 *         "vehicle": { // 进场座驾，没有座驾则不会下发
 *           "effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga",
 *           "web_effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga",
 *           "effect_duration": 10000,
 *           "message_bar": {
 *             "message": "<b>某某骑着某座驾入场</b>", // 进场消息需要支持 html
 *             "image_url": "http://aaa_1_2_3_4.png"
 *           }
 *         },
 *         "custom_welcome_message": { // 自定义进场欢迎语，在用户没有自定义或者有 entry_bubble 时不下发，后续会改为都在 entry_bubble.welcome_message.text 直接下发
 *           "text": "欢迎光临本直播间"
 *         },
 *         "entry_bubble": { // 进场通知，没有进场通知外观则不会下发，后续可能只下发 welcome_message.text，客户端需要支持在该情况下正常展示进场欢迎语
 *           "image_url": "http://entry_image.png",
 *           "entry_style": 0, // 进场通知样式，0: 默认（可不下发），1: 从左往右展开（黑卡 3、4 使用）
 *           "welcome_message": { // 进场通知的文案
 *             "text": "欢迎 XXX 用户进入直播间", // 用户的欢迎语，用户有自定义时改为下发自定义的欢迎语
 *             "colors": "#000000;#FFFFFF", // 文案的颜色，渐变色用分号区分两个颜色，非渐变色则只下发一个颜色
 *             "shine": 0 // 是否闪光，1 为闪光，0 为不闪光，字段不存在则默认不闪光
 *           }
 *         }
 *       },
 *       "medal": { // 用户在本房间的勋章，不一定佩戴，粉丝礼物判断
 *         "name": "我的力量无人能及",
 *         "level": 999,
 *         "frame_url": "http://test.png", // 推荐粉丝勋章
 *       },
 *       "status": {
 *         "invisible": false
 *       },
 *       "room": {
 *         "status": {
 *           "open": 1
 *         }
 *       }
 *     }
 *   }
 */

/**
 * @api {post} /gashapon/update 盲盒能量值更新消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 盲盒能量值更新消息:
 *   {
 *     "type": "gashapon",
 *     "event": "update",
 *     "room_id": 186192636,
 *     "energy": {
 *       "value": 2
 *     },
 *     "buff": { // buff, 为空则只更新能量值
 *       "type": 1, // 0: 能量值 buff; 1: 狂欢 buff
 *       "gift_id": 80001,
 *       "name": "被 buff 的礼物名",
 *       "icon_url": "礼物图标",
 *       "is_new": 1, // 是否是新获取的 buff，不下发或 0 为已存在，1 为新获取
 *       "buff_duration": 180000, // 总时长，毫秒
 *       "remain_duration": 180000,
 *       "multiplier": "2.0", // 倍率
 *       "moment": "超能时刻/织梦时刻", // buff 玩法名称，未下发时，默认超能时刻
 *       "label_intro": ["2.5 倍", "狂欢"], // APP 需要轮播，WEB 拼接内容显示
 *     },
 *     "open_url": "https://www.uat.maoercdn.com/mevent/160?room_id=__ROOM_ID__" // 魔方购买地址，支持模板变量，和小窗一样
 *   }
 */

/**
 * @api {post} /notify/message/new 通用全站广播消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 通用全站广播消息:
 *   {
 *     "type": "notify",
 *     "notify_type": "message",
 *     "event": "new",
 *     "room_id": 123456, // 跳转房间
 *     "message": "XXXX", // html 格式
 *     "notify_bubble": {
 *       "type": "custom", // 气泡类型，下发气泡 custom
 *       "image_url": "https://example.com/b001_0_10_0_100.png",
 *       "float": 1, // 如果 float 为 1, 则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *       "shine": 0 // 是否闪光，0 为不闪光，1 为闪光，字段不存在则默认不闪光
 *     },
 *     // 跳转到房间播放时，应该在本人座驾之后播放特效（礼物层）
 *     "effect": { // 礼物特效、贵族特效、座驾特效规则与此处相同
 *       "effect_url": "*.mp4;*.png;*.svga",
 *       "web_effect_url": "*.mp4;*.webm;*.png;*.svga"
 *     },
 *     "effect_show": 1, // 特效播放场景，0 (或不传)：room_id 对应的房间跳转和在房间内都播放特效
 *                       // 1: 仅在跳转到 room_id 房间时播放特效，在房间内没跳转不播放（兼容老版本 effect 特效消息）
 *     "effect_layer": "special", // 控制本房间内的特效展示层（跳转总是礼物层），special (或不传): 特殊层；gift: 礼物层
 *     "open_url": "http://fm.example.com/open?webview=3&room_id=__ROOM_ID__" // 跳转链接，需要支持模版变量（模版参数同小窗），客户端注意用 msr-0 处理，需要支持半窗打开的逻辑（Web 需单独支持 webview=3 半窗打开的逻辑）
 *                                                                            // 当 room_id 不为 0 时先处理直播间跳转，再处理 open_url
 *   }
 */

/**
 * @api {post} /giftwall/update 礼物墙直播间点亮进度更新消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json}:
 *  {
 *    "type": "gift_wall",
 *    "event": "update",
 *    "room_id": 1,
 *    "time": 1665371308, // 秒级时间戳
 *    "gift_wall": {
 *      "activated_num": 1,
 *      "total_num": 30
 *    },
 *    "creator": {
 *      "user_id": 2,
 *      "username": "主播 1",
 *      "iconurl": "http://test.png"
 *    }
 *  }
 */

/**
 * @api {post} /member/medal/getnew 用户获取勋章直播间内消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json}:
 *  {
 *    "type": "member",
 *    "event": "medal_get_new",
 *    "room_id": 1,
 *    "user": {
 *      "user_id": 12,
 *      "username": "test12",
 *      "iconurl": "http://icon.png",
 *      "titles": [] // titles 同其他
 *    },
 *    "room": {
 *      "room_id": 1,
 *      "creator_id": 2,
 *      "creator_username": "主播 1"
 *    },
 *    "medal": { // 用户在本房间的勋章，不一定佩戴
 *      "name": "我的力量无人能及",
 *      "level": 6,
 *      "frame_url": "http://test.png", // 推荐粉丝勋章
 *      "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *        "expire_time": 1576116700 // 秒级时间戳
 *      }
 *    },
 *    "bubble": {
 *      "type": "message",
 *      "image_url": "https://static-test.maoercdn.com/live/bubbles/message/noble007box_36_36_36_36.png",
 *      "frame_url": "https://static-test.maoercdn.com/live/bubbles/message/noble007frame_corner10.webp"
 *    }
 *  }
 */

/**
 * @api {post} /member/medal/levelup 用户勋章升级直播间内消息
 * @apiDescription 只有升级后 >= 9 级才会发送升级消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json}:
 *  {
 *    "type": "member",
 *    "event": "medal_level_up",
 *    "room_id": 1,
 *    "user": {
 *      "user_id": 12,
 *      "username": "test12",
 *      "iconurl": "http://icon.png",
 *      "titles": [] // titles 同其他
 *    },
 *    "medal": { // 用户在本房间的勋章，不一定佩戴
 *      "name": "我的力量无人能及",
 *      "level": 10,
 *      "frame_url": "http://test.png", // 推荐粉丝勋章
 *      "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *        "expire_time": 1576116700 // 秒级时间戳
 *      }
 *    },
 *    "bubble": {
 *      "type": "message",
 *      "image_url": "https://static-test.maoercdn.com/live/bubbles/message/noble007box_36_36_36_36.png",
 *      "frame_url": "https://static-test.maoercdn.com/live/bubbles/message/noble007frame_corner10.webp"
 *    }
 *  }
 */

/**
 * @api {post} /user_notify/medal/update 用户勋章升级用户消息
 * @apiDescription 当用户获得粉丝牌、粉丝牌升级或开通超粉后，客户端可以实时获取到当前直播间勋章信息，用于赠送粉丝礼物的资格判断
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json}:
 *  {
 *    "type": "user_notify",
 *    "notify_type": "medal",
 *    "event": "update",
 *    "room_id": 1,
 *    "user_id": 12,
 *    "medal": { // 用户在本房间的勋章，不一定佩戴
 *      "name": "我的力量无人能及",
 *      "level": 10,
 *      "frame_url": "http://test.png", // 定制的勋章样式
 *      "super_fan": { // 用户为超粉时返回
 *        ...
 *      }
 *    }
 *  }
 */

/**
 * @api {post} /room/gift_update 礼物栏需要更新
 * @apiDescription 当直播间礼物栏发生变化后，客户端收到该消息，需要重新拉取礼物栏信息 \
 * 客户端: \
 * 收到消息后对于没有打开礼物栏的用户需要删除礼物栏缓存 \
 * 对于打开礼物栏的用户不立即刷新，只清空缓存，下次点击礼物栏的时候再重新请求礼物信息接口 \
 * web 端: 暂时不处理
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 直播间消息:
 * {
 *   "type": "room",
 *   "event": "gift_update",
 *   "room_id": 1
 * }
 */

/**
 * @api {post} /user_notify/level/update 用户直播间礼物半窗等级经验条更新消息
 * @apiDescription 当用户送礼经验值更新后或贵族升级后发送本消息，客户端收到该消息，需要更新礼物半窗的用户等级经验条
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json}:
 *  {
 *    "type": "user_notify",
 *    "notify_type": "level",
 *    "event": "update",
 *    "room_id": 1,
 *    "user_id": 12,
 *    "user_level": { // 用户等级信息
 *      "exp": 1346, // 当前经验值 - 本级初始经验值
 *      "level_up_exp": 1500, // 下级初始经验值 - 本级初始经验值，用户满级时为 0
 *      "level_up_coin": 100, // 钻石差值，用户满级时为 0
 *      "level": 12, // 用户等级
 *      "level_speed_up": { // 无加速特权或用户满级时不返回
 *        "privilege_title": "贵族名称",
 *        "factor": 1.25 // 消费加速倍数
 *      },
 *      "level_icon_url": "https://static-test.maoercdn.com/live/userlevels/level120.webp" // 等级勋章 下发则展示，不下发则自己生成
 *    }
 *  }
 */

/**
 * @api {post} /pk/info/update 直播间 PK 信息更新
 * @apiDescription PK 双方都会收到更新消息，客户端仅在 pk_id 一致的时候刷新比分等信息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} PK 信息更新消息:
 *   {
 *     "type": "pk",
 *     "event": "update", // FIXME: 下发对应接收消息直播间 room_id
 *     "pk": {
 *       "pk_id": "676d0cf9ab406c2b2ccd03d6",
 *       "type": 1,
 *       "fighters": [
 *         {
 *           "room_id": 172842330,
 *           "creator_id": 3457111,
 *           "score": 0, // 当前分数
 *           "name": "猫耳FM111",
 *           "creator_username": "小蜜蜂の",
 *           "creator_iconurl": "https://static-test.maoercdn.com/profile/201912/25/30e551d9a3990ba3c49070a9ba687c5c165120.png"
 *         },
 *         {
 *           "room_id": 789808756,
 *           "creator_id": 3457231,
 *           "score": 1, // 当前分数
 *           "name": "六二半夏的直播间",
 *           "creator_username": "六二半夏我是主播",
 *           "creator_iconurl": "https://static-test.maoercdn.com/avatars/202412/18/a9aaa68a28e297efeb1271527234d1e5150938.png",
 *           "top_fans": [
 *             {
 *               "revenue": 1,
 *               "rank": 1,
 *               "rank_invisible": false,
 *               "user_id": 3457111,
 *               "username": "小蜜蜂の",
 *               "iconurl": "https://static-test.maoercdn.com/profile/201912/25/30e551d9a3990ba3c49070a9ba687c5c165120.png"
 *             }
 *           ]
 *         }
 *       ],
 *       "status": 1, // 0: PK 匹配中; 1: PK 进行中; 2: PK 惩罚期进行中; 3: PK 结束
 *       "start_time": 1735199993561, // PK 开始时间, 单位毫秒
 *       "mute": 0,
 *       "remain_duration": 261463, // 距离 PK 结束、匹配超时、惩罚结束倒计时, 单位毫秒
 *       "duration": 300000 // 距离 PK 结束或惩罚结束等待总时长, 单位毫秒
 *     }
 *   }
 */

/**
 * @api {post} /multi-connect/score/update 主播连麦礼物积分更新消息
 * @apiDescription 所有连麦直播间更新消息，直播间在判断 group_id 一致后，刷新 member.room.room_id 对应直播间的礼物积分
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 主播连麦礼物积分更新消息:
 *   {
 *     "type": "multi_connect",
 *     "event": "score_update",
 *     "room_id": 10659544,
 *     "multi_connect": {
 *       "group_id": 111, // 连线组 ID
 *       "member": { // 需要更新分数的直播间
 *         "index": 1, // 连线序号
 *         "score": 111, // 当前礼物积分
 *         "room": {
 *           "room_id": 10659544,
 *           "creator_id": 3457111
 *         }
 *       }
 *     }
 *   }
 */

/**
 * @api {post} /black-card/upgrade 用户黑卡开通或升级时的直播间弹窗消息
 * @apiDescription 用户黑卡开通或升级时的直播间弹窗消息
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 用户黑卡开通或升级时的直播间弹窗消息:
 *   {
 *     "type": "black_card",
 *     "event": "upgrade",
 *     "room_id": 10659544,
 *     "user_id": 3457181,
 *     "black_card": { // 黑卡信息
 *       "level": 1, //  黑卡等级
 *       "title": "星曜 v1", // 黑卡等级名称
 *       "icon_url": "https://static.example.com/image.png", // 黑卡等级图标
 *       "open_url": "https://www.missevan.com/mevent/9000" // 跳转链接
 *     },
 *     "message": "您已成功达成星曜 v1 身份解锁条件，专属特权已发放，快来看看吧！" // 描述信息
 *   }
 */

/**
 * @api {post} /fans-box/task-new 粉丝团宝箱任务初始化
 * @apiDescription 每天 0 点初始化粉丝团宝箱任务成功后发送，客户端收到消息后需要重置宝箱入口进度，如果用户已经打开粉丝勋章半窗，则同时重新请求粉丝勋章半窗接口刷新半窗
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 粉丝团宝箱任务初始化消息
 *   {
 *     "type": "fans_box",
 *     "event": "task_new",
 *     "room_id": 179257943,
 *     "fans_box": {
 *       "box_task": { // 宝箱任务
 *         "id": 1, // 任务 ID
 *         "target_energy": 10000, // 解锁宝箱所需能量值
 *         "current_energy": 0, // 已达成的能量值
 *         "status": 0 // 宝箱完成状态，0：未完成；1：已完成
 *       }
 *     }
 *   }
 */

/**
 * @api {post} /fans-box/task-update 粉丝团宝箱任务进度更新消息
 * @apiDescription 用户成功助力粉丝团宝箱任务后发送，客户端收到消息后更新宝箱入口进度，如果用户已经打开粉丝勋章半窗，则同时根据任务 ID 是否有变化，以及下发的能量值和状态更新任务进度
 * @apiVersion 0.1.0
 * @apiGroup im-messages
 *
 * @apiSuccessExample {json} 粉丝团宝箱任务进度更新消息
 *   {
 *     "type": "fans_box",
 *     "event": "task_update",
 *     "room_id": 179257943,
 *     "fans_box": {
 *       "box_task": { // 宝箱任务
 *         "id": 1, // 任务 ID
 *         "target_energy": 10000, // 解锁宝箱所需能量值
 *         "current_energy": 1400, // 已达成的能量值
 *         "status": 0 // 宝箱完成状态，0：未完成；1：已完成
 *       }
 *     }
 *   }
 */
