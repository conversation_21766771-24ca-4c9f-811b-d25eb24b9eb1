// Package chatroom api/v2 下的 api/v2/chatroom 的接口集合
package chatroom

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/backpack"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/danmaku"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/fansbox"
	gashaponhandler "github.com/MiaoSiLa/live-service/controllers/chatroom/gashapon"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/gift"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/luckybag"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/luckybox"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/multiconnect"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/pia"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/pk"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/recommend"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/redpacket"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/sticker"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "",
		SubHandlers: []handler.Handler{{
			Name: "chatroom",
			SubHandlers: []handler.Handler{
				gashaponhandler.Handler(),
				pk.HandlerV1(),
				giftWallHandler(),
				redPacketHandler(),
				stickerHandler(),
				piaHandler(),
			},
			Actions: map[string]*handler.Action{
				"mine": handler.NewAction(handler.GET, ActionMine, true),

				"meta":         handler.NewAction(handler.GET, ActionMeta, false),
				"logs/:roomID": handler.NewAction(handler.GET, ActionLogs, true),
				"rank/:roomID": handler.NewAction(handler.GET, ActionRank, false),

				"follow/list":      handler.NewAction(handler.GET, ActionFollowList, true),
				"follow":           handler.NewAction(handler.POST, ActionFollow, true),
				"follow/room-list": handler.NewAction(handler.GET, ActionFollowRoomList, true),

				"fans/rank":     handler.NewAction(handler.GET, ActionFansRank, false),
				"fans/progress": handler.NewAction(handler.GET, ActionFansProgress, false),

				"superfan/rank":  handler.NewAction(handler.GET, ActionSuperFanRank, false),
				"superfan/intro": handler.NewAction(handler.GET, ActionSuperFanIntro, false),
				"superfan/buy":   handler.NewAction(handler.POST, ActionSuperFanBuy, false),

				"list":      handler.NewAction(handler.GET, ActionChatroomList, false),
				"open/list": handler.NewAction(handler.GET, ActionOpenList, false),
				"recommend": handler.NewAction(handler.GET, ActionRecommend, false),

				// 播放页推荐模块
				"sound/recommend":       handler.NewAction(handler.GET, ActionSoundRecommend, false),
				"sound/close-recommend": handler.NewAction(handler.POST, ActionSoundCloseRecommend, false),

				"vitality/logs": handler.NewAction(handler.GET, ActionVitalityLogs, true),
				"vip/list":      handler.NewAction(handler.GET, ActionVipList, false),

				"online": handler.NewAction(handler.POST, ActionOnline, false),
				"close":  handler.NewAction(handler.POST, ActionClose, true),
				"share":  handler.NewAction(handler.POST, ActionShare, false),

				"message/horn": handler.NewAction(handler.POST, ActionMessageHorn, true),

				// 历史记录
				"history/message": handler.NewAction(handler.GET, ActionHistoryMessage, false),
				"history/revenue": handler.NewAction(handler.GET, ActionHistoryRevenue, true),
				// TODO: web 使用 history/revenue 上线后，移除 history/gift
				"history/gift": handler.NewAction(handler.GET, ActionHistoryRevenue, true),

				// 收益记录
				// api/v2/user/revenue/* web 上线使用后，移除 revenue/nobles、revenue/gifts、revenue/superfans、revenue/questions
				"revenue/nobles":    handler.NewAction(handler.GET, ActionRevenueNobles, true),
				"revenue/gifts":     handler.NewAction(handler.GET, ActionRevenueGifts, true),
				"revenue/superfans": handler.NewAction(handler.GET, ActionRevenueSuperFans, true),
				"revenue/questions": handler.NewAction(handler.GET, ActionRevenueQuestions, true),

				"medal/edit":   handler.NewAction(handler.POST, ActionMedalEdit, true),
				"medal/status": handler.NewAction(handler.GET, ActionMedalStatus, true),

				"slide/list": handler.NewAction(handler.GET, ActionSlideList, false),

				"prompt/check":   handler.NewAction(handler.GET, ActionPromptCheck, true),
				"prompt/confirm": handler.NewAction(handler.POST, ActionPromptConfirm, true),

				// 背包
				"backpack/send": handler.NewAction(handler.POST, ActionBackpackSend, true),
				"backpack/use":  handler.NewAction(handler.POST, backpack.ActionBackpackUse, true),

				"backpack/creator/list": handler.NewAction(handler.GET, ActionCreatorBackpackList, true),
				"backpack/creator/use":  handler.NewAction(handler.POST, ActionCreatorBackpackUse, true),

				// 互动
				"interaction/options":   handler.NewAction(handler.GET, ActionInteractionOptions, true),
				"interaction/startvote": handler.NewAction(handler.POST, ActionInteractionStartVote, true),
				"interaction/closevote": handler.NewAction(handler.POST, ActionInteractionCloseVote, true),

				// 礼物
				"gift/send":    handler.NewAction(handler.POST, ActionGiftSend, true),
				"gift/draw":    handler.NewAction(handler.POST, ActionGiftDraw, true),
				"diygift/send": handler.NewAction(handler.POST, ActionDiyGiftSend, true), // TODO: 待移动位置

				// 设置房管
				"admin/add":    handler.NewAction(handler.POST, ActionAddAdmin, true),
				"admin/remove": handler.NewAction(handler.POST, ActionRemoveAdmin, true),

				// 拉黑
				"user/block": handler.NewAction(handler.POST, ActionUserBlock, true),

				// 提问
				// TODO: action 函数名改成和路由一致
				"question/list":         handler.NewAction(handler.GET, ActionListQuestions, false),
				"question/like":         handler.NewAction(handler.POST, ActionLikeQuestion, true),
				"question/append-limit": handler.NewAction(handler.POST, ActionAppendQuestionLimit, true),
				"question/ask":          handler.NewAction(handler.POST, ActionQuestionAsk, true),
				"question/answer":       handler.NewAction(handler.POST, ActionAnswerQuestion, true),

				"search": handler.NewAction(handler.GET, ActionSearch, false),

				// diy 礼物
				"diygift/info": handler.NewAction(handler.GET, ActionDiyGiftInfo, true),

				// 主播个人场小窗
				"liveshow/widget": handler.NewAction(handler.GET, actionLiveShowWidget, false),

				// 直播间设置相关
				"settings/set": handler.NewAction(handler.POST, ActionSettingsSet, true),
				"settings/get": handler.NewAction(handler.GET, ActionSettingsGet, true),

				// 付费弹幕
				"danmaku/info": handler.NewAction(handler.GET, danmaku.ActionDanmakuInfo, true),
				"danmaku/send": handler.NewAction(handler.POST, danmaku.ActionDanmakuSend, true),
			},
		}, {
			Name: "live",
			Actions: map[string]*handler.Action{
				":roomID": handler.NewAction(handler.GET, ActionRoomID, false),
			},
		},
		},
	}
}

func userIDToAccID(userID int64) string {
	return config.Conf.HTTP.AccIDPrefix + strconv.FormatInt(userID, 10)
}

func giftWallHandler() handler.Handler {
	return handler.Handler{
		Name: "giftwall",
		Actions: map[string]*handler.Action{
			"info":   handler.NewAction(handler.GET, ActionGiftWallInfo, false),
			"reward": handler.NewAction(handler.GET, ActionGiftWallReward, false),
			"rank":   handler.NewAction(handler.GET, ActionGiftWallRank, false),
		},
	}
}

func redPacketHandler() handler.Handler {
	return handler.Handler{
		Name: "redpacket",
		Actions: map[string]*handler.Action{
			"config":       handler.NewAction(handler.GET, redpacket.ActionRedPacketConfig, false),
			"grabuserlist": handler.NewAction(handler.GET, redpacket.ActionRedPacketGrabUserList, false),
			"send":         handler.NewAction(handler.POST, redpacket.ActionRedPacketSend, true),
			"grab":         handler.NewAction(handler.POST, redpacket.ActionRedPacketGrab, true),
		},
	}
}

func stickerHandler() handler.Handler {
	return handler.Handler{
		Name: "sticker",
		Actions: map[string]*handler.Action{
			"tabs": handler.NewAction(handler.GET, sticker.ActionStickerTabs, true),
			"list": handler.NewAction(handler.GET, sticker.ActionStickerList, true),
			"send": handler.NewAction(handler.POST, sticker.ActionStickerSend, true),
		},
	}
}

func piaHandler() handler.Handler {
	return handler.Handler{
		Name: "pia",
		Actions: map[string]*handler.Action{
			"start": handler.NewAction(handler.POST, pia.ActionPiaStart, true),
			"stop":  handler.NewAction(handler.POST, pia.ActionPiaStop, true),
		},
	}
}

// HandlerV2 返回 handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "",
		SubHandlers: []handler.HandlerV2{
			{
				Name: "chatroom",
				SubHandlers: []handler.HandlerV2{
					luckyBagHandler(),
					luckybox.HandlerV2(),
					openHandler(),
					multiconnect.Handler(),
					gift.Handler(),
					fansbox.Handler(),
				},
			},
		},
	}
}

func luckyBagHandler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "luckybag",
		Actions: map[string]*handler.ActionV2{
			"config":     handler.NewActionV2(handler.GET, luckybag.ActionLuckyBagConfig, handler.ActionOption{LoginRequired: true}),
			"initiate":   handler.NewActionV2(handler.POST, luckybag.ActionLuckyBagInitiate, handler.ActionOption{LoginRequired: true}),
			"info":       handler.NewActionV2(handler.GET, luckybag.ActionLuckyBagInfo, handler.ActionOption{LoginRequired: true}),
			"finish":     handler.NewActionV2(handler.POST, luckybag.ActionLuckyBagFinish, handler.ActionOption{LoginRequired: true}),
			"join":       handler.NewActionV2(handler.POST, luckybag.ActionLuckyBagJoin, handler.ActionOption{LoginRequired: true}),
			"room/list":  handler.NewActionV2(handler.GET, luckybag.ActionRoomList, handler.ActionOption{LoginRequired: false}),
			"drama/list": handler.NewActionV2(handler.GET, luckybag.ActionDramaList, handler.ActionOption{LoginRequired: false}),
		},
	}
}

func openHandler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "open",
		Actions: map[string]*handler.ActionV2{
			"recommend-list":        handler.NewActionV2(handler.GET, recommend.ActionChatroomOpenRecommendList, handler.ActionOption{LoginRequired: false}),
			"feed-list":             handler.NewActionV2(handler.GET, recommend.ActionChatroomOpenFeedList, handler.ActionOption{LoginRequired: false}),
			"search-recommend-list": handler.NewActionV2(handler.GET, recommend.ActionChatroomOpenSearchRecommendList, handler.ActionOption{LoginRequired: false}),
		},
	}
}
