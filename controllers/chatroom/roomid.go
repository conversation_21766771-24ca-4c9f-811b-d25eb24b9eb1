package chatroom

import (
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/imrpc"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/controllers/utils/feed"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/liveconnect"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/liverecord"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/cdn/agora"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type userRole int

const (
	roleGuest     userRole = iota // 未登录听众
	roleListener                  // 登录听众
	roleConnecter                 // 连麦用户
	roleOwner                     // 主播
)

var agoraLevels = [4]int{
	agora.LevelListener,
	agora.LevelListener,
	agora.LevelBroadCaster,
	agora.LevelChannelOwner,
}

// CheckStatus 使用的常量
const (
	tenSecond    = 10 * time.Second
	thirtySecond = 30 * time.Second
	fiveMinute   = 5 * time.Minute
)

type roomIDParam struct {
	C *handler.Context

	// Room 相关
	RoomID int64
	Room   room.Room
	// 房间是否开启
	RoomOpen bool
	OpenTime time.Time

	// 当间用户
	User *user.User
	Role userRole

	Resp *roomIDResponse
}

type roomIDResponse struct {
	Room        *room.Room                               `json:"room"`
	Creator     *liveuser.User                           `json:"creator"`
	CreatorCard *liverecommendedelements.CreatorCardInfo `json:"creator_card,omitempty"`
	Recommender *liveuser.Simple                         `json:"recommender,omitempty"`
	WebSocket   []string                                 `json:"websocket"`
}

// ActionRoomID /api/v2/live/:roomID 直播间信息
/**
 * @api {get} /api/v2/live/:roomID 直播间信息
 *
 * @apiDescription 一般非必要情况不在此接口增加额外信息，要保证这个基础信息能第一时间响应，同步加载一些 UI 的资源。后续新增直播间额外信息可以统一加到 /api/v2/chatroom/meta 接口
 *
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} :roomID 房间号
 * @apiParam {Number} [preview=0] 是否为直播间预览页，0: 否；1: 是
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample 直播间信息:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room": {
 *         "room_id": 112422405,
 *         "catalog_id": 106,
 *         "custom_tag_id": 10001,
 *         "name": "直播间",
 *         "name_reviewing": true, // 房间名称是否是审核中
 *         "announcement": "testtest",
 *         "background": {
 *           "enable": true,
 *           "image_url": "http://static.maoercdn.com/background/icon01.png",
 *           "pendant_image_url": "http://static.maoercdn.com/background/icon01.avif;http://static.maoercdn.com/background/icon01.webp", // 默认直播间背景的情况下也要显示挂件，会下发多种格式的 URL
 *           "opacity": 1.0 // 背景图和挂件的不透明度均使用此参数
 *         },
 *         "type": "live",
 *         "channel": {
 *           "flv_pull_url": "http://test.flv",
 *           "hls_pull_url": "http://pullhlse9c2439c.live.126.net/live/",
 *           "rtmp_pull_url": "rtmp://ve9c2439c.live.126.net/live/",
 *           "push_url": "rtmp://pe9c2439c.live.126.net/live/"
 *         },
 *         "top": 0,
 *         "creator_id": 3456835,
 *         "creator_username": "123",
 *         "status": {
 *           "open": 1,
 *           "open_question_count": 3,
 *           "open_revenue": 3735,
 *           "open_time": 1568274571359,
 *           "open_log_id": "5d7f7b3b7b8b4b0001b3b3b3", // 开播日志 ID, 关播直播间不下发该字段
 *           "close_time": null,
 *           "channel": {
 *             "type": "open",
 *             "time": 1568027063199
 *           },
 *           "broadcasting": false
 *         },
 *         "notice": "",
 *         "medal": {
 *           "name": "勋章 A",
 *           "default_frame_url": "https://static.maoercdn.com/live/medalframes/3f/level01_0_9_0_54.png" // 房间存在定制粉丝勋章时下发
 *         },
 *         "statistics": {
 *           "revenue": 3735,
 *           "accumulation": 9,
 *           "online": 9,
 *           "attention": false,
 *           "attention_count": 0,
 *           "vip": 100, // 贵宾数量，返回的最大值为 100（实际可能更多，显示 99+）
 *           "score": 0
 *         },
 *         "connect": {
 *           "id": "52cab31801fbd34b02ec32bed5489151",
 *           "provider": "netease",
 *           "forbidden": false,
 *           "queue": [],
 *           "join": [],
 *           "finish": []
 *         },
 *         "question": {
 *           "min_price": 30,
 *           "limit": 100, // 当前提问数量上限
 *           "max_limit": 200, // 当前可被追加的最大提问上限
 *           "join": [],
 *         },
 *         "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *         "image_status": 0 // 第一位：是否曾上传过封面，第三位：封面是否审核中，第四位：背景图是否审核中
 *       },
 *       "creator": {
 *         "user_id": 3456835,
 *         "accid": "3456835",
 *         "username": "aaaa",
 *         "iconurl": "https://static.missevan.com/avatars/icon01.png",
 *         "confirm": 3,
 *         "introduction": "bbbbb",
 *         "online": true
 *       },
 *       "creator_card": {
 *         "frame_url": "https://static.maoercdn.com/live/creatorcard/200001/01/2c145cdb5c7695288ac63e704424b9fa_1_1_1_1.webp" // 主播信息背景地址
 *       },
 *       "recommender": { // 无人推荐时不返回该字段
 *         "user_id": 0,  // 如果时匿名推荐，这里不返回实际用户 ID
 *         "username": "神秘人",
 *         "iconurl": "https://static.missevan.com/avatars/icon01.png",
 *         "recommend_frame_url": "https://static.missevan.com/007.png", // Web 神话/上神推荐的框
 *         "recommend_avatar_frame_url": "https://static.missevan.com/007_3_177_3_21.png" // App 神话/上神推荐的框
 *       },
 *       // 随机选择一个地址，而不是总是第一个。用户正常与 WebSocket 建立连接后，当获取房间信息时发现下发的 WebSocket 地址发生变化，不需要重连
 *       "websocket": ["wss://fm.example.com:3016/ws?room_id=112422405", "wss://fm.example2.com:3016/ws?room_id=112422405"]
 *     }
 *   }
 *
 * @apiSuccessExample 预览页直播间信息:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room": {
 *         "room_id": 112422405,
 *         "catalog_id": 106,
 *         "catalog_name": "分区名",
 *         "catalog_color": "#ffffff",
 *         "custom_tag": {
 *           "tag_id": 10001,
 *           "tag_name": "腹黑青叔"
 *         },
 *         "name": "直播间",
 *         "name_reviewing": true, // 房间名称是否是审核中
 *         "announcement": "testtest",
 *         "background": {
 *           "enable": true,
 *           "image_url": "http://static.maoercdn.com/background/icon01.png",
 *           "pendant_image_url": "http://static.maoercdn.com/background/icon01.avif;http://static.maoercdn.com/background/icon01.webp", // 默认直播间背景的情况下也要显示挂件，会下发多种格式的 URL
 *           "opacity": 1.0 // 背景图和挂件的不透明度均使用此参数
 *         },
 *         "type": "live",
 *         "channel": {
 *           "flv_pull_url": "http://test.flv",
 *           "hls_pull_url": "http://pullhlse9c2439c.live.126.net/live/",
 *           "rtmp_pull_url": "rtmp://ve9c2439c.live.126.net/live/",
 *           "push_url": "rtmp://pe9c2439c.live.126.net/live/"
 *         },
 *         "top": 0,
 *         "creator_id": 3456835,
 *         "creator_username": "123",
 *         "status": {
 *           "open": 1,
 *           "open_question_count": 3,
 *           "open_revenue": 3735,
 *           "open_time": 1568274571359,
 *           "close_time": null,
 *           "channel": {
 *             "type": "open",
 *             "time": 1568027063199
 *           },
 *           "broadcasting": false
 *         },
 *         "notice": "",
 *         "cover_url": "https://static.missevan.com/avatars/icon01.png",
 *         "image_status": 0, // 第一位：是否曾上传过封面，第三位：封面是否审核中，第四位：背景图是否审核中
 *         "preview_tag": "最近看过", // 预览页标签，未下发时默认展示 catalog_name (背景色 catalog_color) + custom_tag.tag_name
 *         "preview_intro": { // 预览页额外信息，未下发时默认展示 name 直播标题
 *           "icon_url": "https://static.missevan.com/icon/icon01.png",
 *           "title": "直播小时榜排名第 N"
 *         }
 *       },
 *       "creator": {
 *         "user_id": 3456835,
 *         "username": "aaaa",
 *         "iconurl": "https://static.missevan.com/avatars/icon01.png",
 *         "confirm": 3,
 *         "introduction": "bbbbb"
 *       },
 *       "creator_card": {
 *         "frame_url": "https://static.maoercdn.com/live/creatorcard/200001/01/2c145cdb5c7695288ac63e704424b9fa_1_1_1_1.webp" // 主播信息背景地址
 *       },
 *       // 随机选择一个地址，而不是总是第一个。用户正常与 WebSocket 建立连接后，当获取房间信息时发现下发的 WebSocket 地址发生变化，不需要重连
 *       "websocket": ["wss://fm.example.com:3016/ws?room_id=112422405&preview=1", "wss://fm.example2.com:3016/ws?room_id=112422405&preview=1"],
 *       "trace": "{\"preview_tags\":[\"最新看过\",\"我的关注\",\"音乐\",\"元气少年\"],\"preview_intro_title\":\"直播小时榜排名第 N\"}" // 埋点数据, 客户端直接透传, 客户端获取到之后, 要更新使用这里的 trace
 *     }
 *   }
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该聊天室
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (401) {Number} code 500020001
 * @apiError (401) {String} info 用户未登录或登录已过期，请重新登录
 *
 * @apiError (403) {Number} code 500030013
 * @apiError (403) {String} info 直播间被封禁
 *
 * @apiError (403) {Number} code 500020023
 * @apiError (403) {String} info 当前用户被封禁
 *
 */
func ActionRoomID(c *handler.Context) (handler.ActionResponse, error) {
	preview, err := c.GetDefaultParamInt("preview", 0)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if goutil.IntToBool(preview) {
		return findPreviewRoom(c)
	}
	return findNormalRoom(c)
}

func findNormalRoom(c *handler.Context) (handler.ActionResponse, error) {
	param := roomIDParam{C: c, Resp: new(roomIDResponse)}
	err := param.PreCheck()
	if err != nil {
		return nil, err
	}
	param.CheckStatus()
	param.CheckRoomType()
	param.buildRecommendBackground()
	param.buildBackgroundURL()

	err = param.BuildQuestion()
	if err != nil {
		return nil, err
	}

	err = param.BuildConnect()
	if err != nil {
		return nil, err
	}
	err = param.BuildMembers()
	if err != nil {
		return nil, err
	}
	param.checkSpecialUserOnline()
	param.checkVipNum()
	param.findCreatorCard()
	param.findRecommender()
	param.BuildChannel()
	param.BuildWebSocketURL()
	return param.Resp, nil
}

// PreCheck 通过 roomID 从数据库获取 room, creator，liveMeta
// 初步确认访问者身份
// TODO: 未给游客添加 session
func (param *roomIDParam) PreCheck() error {
	param.User = param.C.User()

	roomIDStr := param.C.C.Param("roomID")
	var err error
	param.RoomID, err = strconv.ParseInt(roomIDStr, 10, 64)
	if err != nil || param.RoomID <= 0 {
		return actionerrors.ErrCannotFindRoom
	}
	listenerID := param.C.UserID()
	opt := &room.FindOptions{
		ClientIP:          param.C.ClientIP(),
		FindFans:          true,
		FindReviewing:     true,
		ListenerID:        listenerID,
		CreatorFindOnline: true,
		FindPendant:       true,
	}
	param.Resp.Room, err = room.Find(param.RoomID, opt)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.Resp.Room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if param.Resp.Room.LimitStatus(listenerID) == room.LimitStatusBlocked {
		return actionerrors.ErrCannotFindRoom
	}
	// param.Room 不修改，只读
	param.Room = *param.Resp.Room
	if param.Room.IsBan() {
		return actionerrors.ErrBannedRoom
	}
	param.RoomOpen = param.Room.Status.Open != 0
	if param.RoomOpen {
		param.OpenTime = util.UnixMilliToTime(param.Room.Status.OpenTime)
	}
	// 初步确认访问者身份
	switch listenerID {
	case 0:
		param.Role = roleGuest
	case param.Room.CreatorID:
		param.Role = roleOwner
	default:
		param.Role = roleListener
	}
	// REVIEW: 感觉不太对
	// 账号被 ban 进不去，但是游客又能看到
	if param.Role == roleListener {
		exists, err := blocklist.Exists(param.User.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if exists {
			return actionerrors.NewErrForbidden("您已被封禁，无法进入直播间")
		}

		// 主播是否拉黑此用户
		blocked, err := blocklist.IsBlocked(param.Room.CreatorID, param.User.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if blocked {
			return actionerrors.NewErrBlockUser("您当前无法进入直播间")
		}

		banned, err := userstatus.IsBanned(param.User.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if banned {
			return actionerrors.NewErrForbidden("您已被封禁，无法进入直播间")
		}
	}

	// 查询 creator
	param.Resp.Creator, err = liveuser.Find(param.Room.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.Resp.Creator == nil {
		return actionerrors.NewErrServerInternal(errors.New("cannot find the creator"), nil)
	}
	param.Resp.Creator.MakeTitles(&liveuser.FindOptions{RoomID: param.RoomID})

	if param.Resp.Room.Medal != nil {
		// 获取 1 级定制粉丝勋章样式
		param.Resp.Room.Medal.DefaultFrameURL, err = livemedal.FindCustomMedalFrame(param.RoomID, 1)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

// CheckStatus 检查主播是否在推流，
// 如果是房主并且实际流状态和数据库不符，更新数据库
func (param *roomIDParam) CheckStatus() {
	param.Resp.Room.Status.Broadcasting = param.Room.IsPushing()
	if !param.Resp.Room.Status.Broadcasting {
		return
	}

	// 不需要通过流状态检查是否推流
	if !config.Conf.Web.CheckChannel {
		return
	}

	now := goutil.TimeNow()
	key := keys.KeyRoomStatusCache1.Format(param.RoomID)
	// 非房主默认优先从缓存拿结果
	if param.Role != roleOwner {
		res, err := service.LRURedis.Get(key).Result()
		if err != nil && !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}
		if err == nil {
			isPushing, err := strconv.Atoi(res)
			if err != nil {
				logger.Error(err)
				// PASS
				return
			}
			param.Resp.Room.Status.Broadcasting = goutil.IntToBool(isPushing)
			return
		}
	}

	pushing, reliable, err := isRoomPushing(&param.Room)
	if err != nil {
		logger.WithField("room_id", param.RoomID).Error(err)
		return
	}
	err = service.LRURedis.Set(key, goutil.BoolToInt(pushing), tenSecond).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	param.Resp.Room.Status.Broadcasting = pushing
	if !reliable || param.Role != roleOwner || pushing {
		return
	}
	// 是房主、流状态可信、流状态是关闭的，更新数据库
	logger.Warnf("Close room channel %d by stream status", param.RoomID)
	param.Resp.Room.Status.Channel = room.StatusChannel{
		Type: room.TypeOpen,
		Time: util.TimeToUnixMilli(now),
	}
	param.Resp.Room.UpdatedTime = now
	updates := bson.M{
		"status.channel": &param.Resp.Room.Status.Channel,
		"updated_time":   now,
	}
	_, err = room.Update(param.Room.RoomID, updates)
	if err != nil {
		logger.Error(err)
		return
	}
	// TODO: 房间信息缓存未全部迁移
	_, err = service.Redis.Del(keys.KeyRoomsRoomID1.Format(param.RoomID)).Result()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = liverecord.Update(param.Room.OID, param.Room.RoomID, param.Room.GuildID, false)
	if err != nil {
		logger.Error(err)
		return
	}
}

// CheckRoomType 检查房间模式，如果是连麦模式，进一步判断访问者身份
func (param *roomIDParam) CheckRoomType() {
	if !param.RoomOpen {
		return
	}
	param.Resp.Room.TrySwitchProvider(param.Role == roleOwner)

	connect := &param.Resp.Room.Connect
	if connect.Provider != room.ProviderAgora {
		return
	}

	var agoraID int64
	if param.User != nil {
		agoraID = agora.UserIDToAgoraID(param.User.ID, param.C.Equip().OS)
	}
	connect.BuildAgora(agoraID, agoraLevels[param.Role])
}

// buildRecommendBackground 获取直播间推荐背景图
func (param *roomIDParam) buildRecommendBackground() {
	background, err := liverecommendedelements.FindBackground(param.RoomID)
	if err != nil {
		logger.Error(err)
		return
	}
	if background == nil {
		return
	}

	if param.Resp.Room.Background == nil {
		param.Resp.Room.Background = &room.Background{
			Enable:          true,
			Opacity:         background.Opacity,
			ImageURL:        background.ImageURL,
			PendantImageURL: "",
		}
	} else {
		param.Resp.Room.Background = &room.Background{
			Enable:          param.Resp.Room.Background.ImageURL == "" || param.Resp.Room.Background.Enable, // 当主播选择使用默认背景图时，设置的直播间推荐背景图不会生效
			Opacity:         background.Opacity,
			ImageURL:        background.ImageURL,
			PendantImageURL: param.Resp.Room.Background.PendantImageURL,
		}
	}
}

func (param *roomIDParam) buildBackgroundURL() {
	if param.Resp.Room.Background == nil {
		return
	}

	// WORKAROUND: 兼容版本 iOS < 4.9.3 和 Android < 5.7.8 的版本不下发 avif url
	if param.C.Equip().IsAppOlderThan("4.9.3", "5.7.8") {
		// 直播间背景
		if strings.HasSuffix(param.Resp.Room.Background.ImageURL, ".avif") {
			param.Resp.Room.Background.ImageURL = param.Resp.Room.Background.ImageURL[:len(param.Resp.Room.Background.ImageURL)-5] + ".webp"
		}
		// 直播间挂件
		pendantImageURLs := strings.Split(param.Resp.Room.Background.PendantImageURL, ";")
		param.Resp.Room.Background.PendantImageURL = ""
		for _, url := range pendantImageURLs {
			if !strings.HasSuffix(url, ".avif") {
				param.Resp.Room.Background.PendantImageURL = url
				break
			}
		}
	}
}

// BuildQuestion 补全响应的 question
func (param *roomIDParam) BuildQuestion() error {
	if !param.RoomOpen {
		return nil
	}

	var err error
	question := &param.Resp.Room.Question
	// 总是返回正在回答的问题
	question.Join, err = livequestion.ListLiveQuestionsByPage(
		param.C.UserID(), param.Room.RoomID, param.OpenTime, []int32{livequestion.StatusJoined}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

// BuildConnect 补全响应的 connect
func (param *roomIDParam) BuildConnect() error {
	if !param.RoomOpen {
		return nil
	}
	connect := &param.Resp.Room.Connect
	if param.Room.Type == room.TypeLive {
		connect.Queue, connect.Join, connect.Finish = make([]*liveconnect.LiveConnect, 0), make([]*liveconnect.LiveConnect, 0), make([]*liveconnect.LiveConnect, 0)
		return nil
	}
	err := connect.ListConnects(param.Room.OID, param.OpenTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.Role == roleListener &&
		liveconnect.IsUserInQueue(param.User.ID, connect.Join) {
		param.Role = roleConnecter
	}
	if param.Role != roleOwner && param.Role != roleConnecter {
		// 只暴露给房主和连麦者
		connect.ID = ""
	}
	return nil
}

// BuildMembers 补全响应的 members
func (param *roomIDParam) BuildMembers() error {
	e := param.C.Equip()
	// Web 和客户端 >= 6.1.3 的版本不返回 members
	if !e.FromApp || !e.IsAppOlderThan("6.1.3", "6.1.3") {
		return nil
	}
	var err error
	admin, mute, err := livemembers.ListMembers(param.Room.OID, &livemembers.FindMemberOptions{
		FindUserInfo: true,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Resp.Room.Members = &room.Members{
		Admin: admin,
		Mute:  mute,
	}
	return nil
}

// checkSpecialUserOnline 查看连麦用户队列和主播是否在线
func (param *roomIDParam) checkSpecialUserOnline() {
	connect := &param.Resp.Room.Connect
	userIDs := []int64{param.Room.CreatorID}
	connects := make(map[int64]*liveconnect.LiveConnect)
	for _, c := range connect.Join {
		// 只对 join 的 online 添加指针
		c.Online = new(bool)
		userIDs = append(userIDs, c.UserID)
		connects[c.UserID] = c
	}

	var onlineResp imrpc.OnlineStatusResp
	err := service.MRPC.Call("im://online/userstatus", param.C.ClientIP(),
		handler.M{"room_id": param.RoomID, "user_ids": userIDs}, &onlineResp)
	if err != nil {
		logger.Error(err)
		// PASS
		// online 返回 false 会显示等待连接的状态
		// 出错后降级成在线状态
		param.Resp.Creator.Online = true
		for i := range connect.Join {
			connect.Join[i].Online = goutil.NewBool(true)
		}
		return
	}
	for i := 0; i < len(onlineResp.UserStatus); i++ {
		if !onlineResp.UserStatus[i].Online {
			continue
		}
		if onlineResp.UserStatus[i].UserID == param.Room.CreatorID {
			param.Resp.Creator.Online = true
			continue
		}
		*connects[onlineResp.UserStatus[i].UserID].Online = true
	}
}

func (param *roomIDParam) findCreatorCard() {
	creatorCard, err := liverecommendedelements.FindCreatorCard(param.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if creatorCard == nil {
		return
	}
	param.Resp.CreatorCard = creatorCard
}

// findRecommender 获取神话推荐信息
func (param *roomIDParam) findRecommender() {
	nr, err := livenoblerecommend.CurrentRecommend()
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if nr == nil || nr.RoomID != param.RoomID {
		return
	}
	param.Resp.Recommender = nr.FindRecommender()
}

func (param *roomIDParam) checkVipNum() {
	if !param.RoomOpen {
		return
	}
	// 贵宾数量超过 99 之后总是显示 99+
	param.Resp.Room.Statistics.Vip = min(findVipNum(param.C, param.RoomID), maxVipNum)
}

// findVipNum 查询直播间内贵宾数量的简单封装
func findVipNum(c *handler.Context, roomID int64) int64 {
	v := vipNumFromCache(roomID)
	if v.UpdatedTime >= goutil.TimeNow().Unix()-60 {
		return v.VipNum
	}
	var err error
	defer func() {
		if err != nil {
			if e, ok := err.(*logger.ContextError); ok {
				e.Log(logger.ErrorLevel, e.Error())
			} else {
				logger.Error(err)
			}
		}
	}()
	paramVip := newVipListParam(c, false)
	paramVip.roomID = roomID
	r := new(vipListResp)
	if paramVip.vipListFromCache(r) {
		return r.VipNum
	}
	// 异步同步 r.VipNum, 此处不等结果，r.VipNum 总是 0
	// 降级成 vipNumFromCache 的值
	return v.VipNum
}

// BuildChannel 更新 room.Channel 的 URL
func (param *roomIDParam) BuildChannel() {
	e := param.C.Equip()
	param.Resp.Room.BuildAuthedChannelURL(param.C.UserID(), param.C.ClientIP(), e, param.Role == roleOwner, false)
}

// BuildWebSocketURL 下发房间 WebSocket 地址
func (param *roomIDParam) BuildWebSocketURL() {
	param.Resp.WebSocket = param.Resp.Room.WebSocketURLs()
}

type previewRoomResponse struct {
	Room      *room.Room     `json:"room"`
	Creator   *liveuser.User `json:"creator"`
	WebSocket []string       `json:"websocket"`
	Trace     string         `json:"trace"`

	c           *handler.Context
	userID      int64
	previewTags []string
}

func findPreviewRoom(c *handler.Context) (handler.ActionResponse, error) {
	roomIDStr := c.C.Param("roomID")
	roomID, err := strconv.ParseInt(roomIDStr, 10, 64)
	if err != nil || roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	userID := c.UserID()
	r, err := room.Find(roomID, &room.FindOptions{
		FindCatalogInfo: true,
		FindCustomTag:   true,
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.LimitStatus(userID) == room.LimitStatusBlocked {
		// 礼物房间不允许预览
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.IsBan() {
		return nil, actionerrors.ErrBannedRoom
	}
	// NOTICE: 允许被封禁或被拉黑的用户预览直播间
	resp := &previewRoomResponse{Room: r, c: c, userID: userID}
	err = resp.buildCreator()
	if err != nil {
		return nil, err
	}
	resp.buildBuildAuthedChannelURL()
	resp.buildWebSocket()
	resp.buildPreviewTags()
	resp.buildPreviewIntro()
	resp.buildTrace()
	return resp, nil
}

func (resp *previewRoomResponse) buildPreviewIntro() {
	r := resp.Room
	// 补充信息，优先级如下，展示最高优先级的一条
	// 1. 直播间内有未开奖的福袋
	// 	剧集福袋：展示 icon + 正在免费抽《剧集名称》福袋
	// 	实物福袋：展示 icon + 正在抽周边福袋
	luckyBagPipe := func() bool {
		bag, err := luckybag.FindPendingInitiateRecordByRoomID(r.RoomID)
		if err != nil {
			logger.Error(err)
			return false
		}
		if bag == nil {
			return false
		}

		switch bag.Type {
		case luckybag.TypeDrama:
			r.PreviewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(utils.PreviewIntroLuckyBagIcon),
				Title:   fmt.Sprintf("正在免费抽《%s》福袋", bag.Name),
			}
			return true
		case luckybag.TypeEntity:
			r.PreviewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(utils.PreviewIntroLuckyBagIcon),
				Title:   "正在抽周边福袋",
			}
			return true
		default:
			return false
		}
	}

	// 2. 直播间内有未抢完的红包，展示 icon + 正在抢礼物红包
	redPacketPipe := func() bool {
		if r.Status.RedPacket <= 0 {
			return false
		}

		r.PreviewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(utils.PreviewIntroRedPacketIcon),
			Title:   "正在抢礼物红包",
		}
		return true
	}

	// 3. 榜单排名信息，icon + 文案，优先级如下，展示最高优先级的一条
	// 	本小时榜排名 Top 3，文案 直播小时榜排名第 N
	// 	本小时榜排名 4-10，文案 直播小时榜排名 TOP10
	// 	上小时榜排名 Top 3，文案 直播上小时榜排名第 N
	// 	日榜排名 Top 10，文案 直播日榜排名第 N
	// 	周榜排名 Top 10，文案 直播周榜排名第 N
	// 	月榜排名 Top 10，文案 直播月榜排名第 N
	// 	月榜排名 11-20，文案 直播月榜排名 TOP20
	// 	新人榜排名 Top 3，文案 直播新人榜排名第 N
	rankPipe := func() bool {
		// 榜单排名对应的 redis 下标
		const (
			rank3  = 2
			rank10 = 9
			rank20 = 19
		)

		var (
			now          = goutil.TimeNow()
			creatorIDStr = strconv.FormatInt(r.CreatorID, 10)
		)

		pipe := service.Redis.TxPipeline()
		hourRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeHour, now), creatorIDStr)
		lastHourRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeHour, now.Add(-time.Hour)), creatorIDStr)
		dayRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeDay, now), creatorIDStr)
		weekRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeWeek, now), creatorIDStr)
		monthRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeMonth, now), creatorIDStr)
		novaRankCmd := pipe.ZRevRank(usersrank.Key(usersrank.TypeNova, now), creatorIDStr)
		_, err := pipe.Exec()
		if err != nil && !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}

		hourRank, err := hourRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if hourRank <= rank3 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播小时榜排名第 %d", hourRank+1),
				}
				return true
			}
			if hourRank <= rank10 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   "直播小时榜排名 TOP10",
				}
				return true
			}
		}

		lastHourRank, err := lastHourRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if lastHourRank <= rank3 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播上小时榜排名第 %d", lastHourRank+1),
				}
				return true
			}
		}

		dayRank, err := dayRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if dayRank <= rank10 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播日榜排名第 %d", dayRank+1),
				}
				return true
			}
		}

		weekRank, err := weekRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if weekRank <= rank10 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播周榜排名第 %d", weekRank+1),
				}
				return true
			}
		}

		monthRank, err := monthRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if monthRank <= rank10 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播月榜排名第 %d", monthRank+1),
				}
				return true
			}
			if monthRank <= rank20 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   "直播月榜排名 TOP20",
				}
				return true
			}
		}

		novaRank, err := novaRankCmd.Result()
		if err != nil {
			if !serviceredis.IsRedisNil(err) {
				logger.Error(err)
				// PASS
			}
		} else {
			if novaRank <= rank3 {
				r.PreviewIntro = &room.PreviewIntro{
					IconURL: service.Storage.Parse(utils.PreviewIntroRankIcon),
					Title:   fmt.Sprintf("直播新人榜排名第 %d", novaRank+1),
				}
				return true
			}
		}

		return false
	}

	// 4. 在连麦 PK 时，展示 icon + 主播正在连麦 PK
	pkPipe := func() bool {
		if r.Status.PK == 0 {
			return false
		}

		r.PreviewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(utils.PreviewIntroPKIcon),
			Title:   "主播正在连麦 PK",
		}
		return true
	}

	// 5. 在主播连线时，展示 icon + 主播正在多人连线
	multiConnect := func() bool {
		if !r.IsMultiConnect() {
			return false
		}
		r.PreviewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(utils.PreviewIntroMultiConnectIcon),
			Title:   "主播正在多人连线",
		}
		return true
	}

	// 6. 粉丝人数达标
	// 	粉丝人数 >= 1w，展示 icon + 超 N 万粉宝藏主播（N 表示主播的粉丝数，以 万 为单位，保留整数，去掉小数，e.g. 粉丝数 = 59999 时展示 超 5 万粉宝藏主播）
	followerPipe := func() bool {
		attention, err := attentionuser.CheckAttention(0 /* 仅获取粉丝数 */, []int64{r.CreatorID})
		if err != nil {
			logger.Error(err)
			return false
		}
		if len(attention) <= 0 {
			return false
		}

		followersCount := attention[0].FansNum
		if followersCount < 10000 {
			return false
		}
		r.PreviewIntro = &room.PreviewIntro{
			IconURL: service.Storage.Parse(utils.PreviewIntroFollowersIcon),
			Title:   fmt.Sprintf("超 %d 万粉宝藏主播", followersCount/10000),
		}
		return true
	}

	// 7. 在线人数 >= 100 时，展示 icon + N 人正在听（N 表示在线收听人数）
	// 	100 <= N < 1k 时，N 取在线人数的百位数字，比如 543 人记为「500 + 人正在听」
	// 	1k <= N < 1w 时，N 取在线人数的千位数字，记为「x 千 + 人正在听」，比如 5432 记为「5 千 + 人正在听」
	// 	N >= 1w 时，取在线人数的万位数字，记为「x 万 + 人正在听」，比如 54321 记为「5 万 + 人正在听」
	onlinePipe := func() bool {
		onlineResp, err := userapi.Online(resp.c.UserContext(), r.RoomID)
		if err != nil {
			logger.Error(err)
			return false
		}

		hairThinSpace := string([]rune{0x200A})
		n := onlineResp.Count
		switch {
		case n < 100:
			return false
		case n < 1000:
			r.PreviewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(utils.PreviewIntroListenersIcon),
				Title:   fmt.Sprintf("%d00%s+%s人正在听", n/100, hairThinSpace, hairThinSpace),
			}
			return true
		case n < 10000:
			r.PreviewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(utils.PreviewIntroListenersIcon),
				Title:   fmt.Sprintf("%d%s千%s+%s人正在听", n/1000, hairThinSpace, hairThinSpace, hairThinSpace),
			}
			return true
		default:
			r.PreviewIntro = &room.PreviewIntro{
				IconURL: service.Storage.Parse(utils.PreviewIntroListenersIcon),
				Title:   fmt.Sprintf("%d%s万%s+%s人正在听", n/10000, hairThinSpace, hairThinSpace, hairThinSpace),
			}
			return true
		}
	}

	pipeFuncs := []func() bool{luckyBagPipe, redPacketPipe, rankPipe, pkPipe, multiConnect, followerPipe, onlinePipe}
	for _, f := range pipeFuncs {
		if f() {
			return
		}
	}
}

func (resp *previewRoomResponse) buildCreator() error {
	var err error
	resp.Creator, err = liveuser.Find(resp.Room.CreatorID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.Creator == nil {
		return actionerrors.NewErrServerInternal(errors.New("cannot find the creator"), nil)
	}
	resp.Creator.MakeTitles(&liveuser.FindOptions{RoomID: resp.Room.RoomID})
	if resp.userID != 0 && resp.userID != resp.Room.CreatorID {
		// 这里查询关注后赋值，不使用 room.FindOne FindFans Options 避免查询粉丝总数
		resp.Room.Statistics.Attention, err = attentionuser.HasFollowed(resp.userID, resp.Room.CreatorID)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return nil
}

func (resp *previewRoomResponse) buildBuildAuthedChannelURL() {
	resp.Room.BuildAuthedChannelURL(resp.userID, resp.c.ClientIP(), resp.c.Equip(), false, false)
}

func (resp *previewRoomResponse) buildWebSocket() {
	resp.WebSocket = resp.Room.WebSocketURLs()
	for i := range resp.WebSocket {
		// 处理预览页 preview 参数
		if strings.Contains(resp.WebSocket[i], "?") {
			resp.WebSocket[i] += "&preview=1"
		} else {
			resp.WebSocket[i] += "?preview=1"
		}
	}
}

/*
预览页标签：
1. 已成为超粉
2. 已点亮粉丝牌
3. 我关注的
4. 我追的剧集声优
5. 最近看过
6. 直播二级分区 + 个性词条
*/
func (resp *previewRoomResponse) buildPreviewTags() {
	resp.previewTags = make([]string, 0, 7)
	defer func() {
		if resp.Room.CatalogName != "" {
			resp.previewTags = append(resp.previewTags, resp.Room.CatalogName)
		}
		if resp.Room.CustomTag != nil {
			resp.previewTags = append(resp.previewTags, resp.Room.CustomTag.TagName)
		}
	}()
	if resp.userID == 0 {
		// 未登录用户仅返回 直播二级分区+个性词条
		return
	}
	medal, err := livemedal.FindOwnedMedal(resp.userID, resp.Room.RoomID, livemedal.FindOptions{DisableAll: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if medal != nil {
		if livemedal.IsSuperFanActive(medal.SuperFan) {
			resp.previewTags = append(resp.previewTags, "已成为超粉")
		} else {
			resp.previewTags = append(resp.previewTags, "已点亮粉丝牌")
		}
	}

	if resp.Room.Statistics.Attention {
		resp.previewTags = append(resp.previewTags, "我关注的")
	}

	// TODO: 对用户已追剧集声优添加 cache
	cvsInfos, err := userapi.ListUserDramaCVs(resp.c.UserContext(), resp.userID, userapi.DramaCVSceneLiveRecommend)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if slices.ContainsFunc(cvsInfos, func(info userapi.DramaCVInfo) bool { return info.UserID == resp.Room.CreatorID }) {
		resp.previewTags = append(resp.previewTags, "我追的剧集声优")
	}

	ok, err := feed.IsInteractedIn7Days(resp.userID, resp.Room.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if ok {
		resp.previewTags = append(resp.previewTags, "最近看过")
	}

	if len(resp.previewTags) > 0 {
		// 分区和个性词条不通过 preview_tag 字段下发
		resp.Room.PreviewTag = resp.previewTags[0]
	}
}

func (resp *previewRoomResponse) buildTrace() {
	trace := room.PreviewTrace{
		PreviewTags: resp.previewTags,
	}
	if resp.Room.PreviewIntro != nil {
		trace.PreviewIntroTitle = resp.Room.PreviewIntro.Title
	}
	traceBytes, err := json.Marshal(trace)
	if err != nil {
		logger.Error(err)
		return
	}
	resp.Trace = string(traceBytes)
}
