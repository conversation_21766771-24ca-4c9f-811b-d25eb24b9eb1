package chatroom

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livegiftupgrade"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGiftSendKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(roomCreator{}, "room_id", "creator_id", "creator_username", "creator_iconurl")
	kc.Check(giftSendParam{}, "room_id", "from_room_id", "gift_id", "gift_num", "combo", "type")
	kc.Check(sendResp{}, "user", "room", "medal", "bubble", "balance", "combo", "multi_combo", "upgrade")
}

func TestNewGiftSendParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	defer mrpc.SetMock(userapi.URIGoUserBlocklist, func(input interface{}) (output interface{}, err error) {
		return handler.M{"block_list": []int64{}}, nil
	})()
	defer mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})()

	newC := func(body interface{}) *handler.Context {
		return handler.NewTestContext(http.MethodPost, "/gift/send", true,
			body)
	}

	c := newC(handler.M{"room_id": -1})
	_, err := newGiftSendParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = newC(handler.M{"room_id": 1, "gift_num": -1})
	_, err = newGiftSendParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	r, err := room.Find(223344)
	require.NoError(err)
	require.NotNil(r)
	testBlockUserID := int64(1019019)
	key := blocklist.KeyUserBlock(r.CreatorID)
	require.NoError(service.LRURedis.SAdd(key, testBlockUserID).Err())
	defer func() {
		_ = blocklist.Clear(r.CreatorID)
	}()
	c = newC(handler.M{"room_id": r.RoomID, "gift_id": testDiyGiftID})
	c.User().ID = testBlockUserID
	_, err = newDiyGiftSendParam(c)
	assert.EqualError(err, "您当前无法在本直播间内进行此操作")

	c = newC(handler.M{"room_id": 999, "gift_id": 1, "gift_num": 1})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	c = newC(handler.M{"room_id": room.TestLimitedRoomID, "gift_id": 1})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.NewErrForbidden("本直播间内无法赠送该礼物"), err)

	c = newC(handler.M{"room_id": openingRoomID, "gift_id": 123456789})
	_, err = newDiyGiftSendParam(c)
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
}

func TestCheckAllowCrossSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		fromRoomID        = openingRoomID
		toRoomID          = testRoomID
		toRoomID2         = testRoomID + 1
		testGroupID int64 = 100
	)

	require.NoError(livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "room_id IN (?)", []int64{
		fromRoomID, toRoomID, toRoomID2,
	}).Error)
	err := checkAllowCrossSend(fromRoomID, toRoomID)
	assert.EqualError(err, "直播间未在主播连线中，无法赠送该礼物")

	members := []*livemulticonnect.GroupMember{
		{GroupID: testGroupID, Role: livemulticonnect.MemberRoleOwner, Status: livemulticonnect.MemberStatusOngoing, RoomID: fromRoomID},
		{GroupID: testGroupID, Role: livemulticonnect.MemberRoleMember, Status: livemulticonnect.MemberStatusOngoing, RoomID: toRoomID},
		{GroupID: -111, Role: livemulticonnect.MemberRoleMember, Status: livemulticonnect.MemberStatusOngoing, RoomID: toRoomID2},
	}
	require.NoError(servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members))
	err = checkAllowCrossSend(fromRoomID, toRoomID2)
	assert.EqualError(err, "对方直播间未在连线组中，无法赠送该礼物")

	err = checkAllowCrossSend(fromRoomID, toRoomID)
	assert.NoError(err)
}

func TestFindGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	allGifts, err := gift.FindAllShowingGifts()
	require.NoError(err)

	user := new(liveuser.Simple)
	_, err = findGift(-1, 0, user, nil, nil)
	assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)

	// 不能赠送的礼物
	ok := false
	for _, g := range allGifts {
		if g.Type != gift.TypeFree {
			continue
		}
		ok = true

		_, err = findGift(g.GiftID, 0, user, nil, nil)
		assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
		break
	}
	assert.True(ok)

	// 贵族礼物
	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeNoble {
			continue
		}
		ok = true

		_, err = findGift(g.GiftID, 0, user, nil, nil)
		assert.Equal(actionerrors.NewErrForbidden("无法购买当前贵族礼物"), err)
		break
	}
	assert.True(ok)

	// 用户定制礼物
	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeCustom {
			continue
		}
		ok = true

		_, err = findGift(g.GiftID, 1, &liveuser.Simple{UID: 1}, nil, nil)
		assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
		break
	}
	assert.True(ok)

	// 房间定制
	ok = false
	for _, g := range allGifts {
		if g.Type != gift.TypeRoomCustom {
			continue
		}
		ok = true

		_, err = findGift(g.GiftID, 1, user, nil, nil)
		assert.Equal(actionerrors.ErrNotFound("无法找到指定礼物"), err)
		break
	}
	assert.True(ok)
}

func TestGiftSendParam_findGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := giftSendParam{
		SendType: sendTypeObtainMedal,
		GiftID:   1,
	}
	assert.EqualError(param.findGift(), "无法购买当前礼物")

	param = giftSendParam{
		GiftID: 3,
		user:   &liveuser.Simple{},
		r:      &room.Room{},
	}
	require.NoError(param.findGift())
	require.NotNil(param.g)

	param = giftSendParam{
		SendType: sendTypeObtainMedal,
		GiftID:   livemedal.ObtainMedalGiftID,
		GiftNum:  1,
	}
	require.NoError(param.findGift())
	require.NotNil(param.g)
	assert.Equal(param.GiftID, param.g.GiftID)

	param = giftSendParam{
		SendType: sendTypeObtainMedal,
		GiftID:   livemedal.ObtainMedalGiftID,
		GiftNum:  2,
	}
	assert.EqualError(param.findGift(), "无法购买当前礼物")

	// 升级礼物
	now := goutil.TimeNow().Unix()
	service.Cache5s.Flush()
	key := keys.KeyOnlineGifts0.Format()
	attr := goutil.BitMask(0)
	attr.Set(gift.AttrUpgradeBase)
	gifts := []gift.Gift{
		{GiftID: 1, Type: gift.TypeNormal, Attr: attr, ToggleTime: now},
		{GiftID: 2, Type: gift.TypeUpgrade},
		{GiftID: 9999, Type: gift.TypeUpgrade},
	}
	service.Cache5s.Set(key, gifts, 0)
	require.NoError(livegiftupgrade.DB().Delete(&livegiftupgrade.GiftUpgrade{}).Error)
	require.NoError(livegiftupgrade.DB().Delete(&livegiftupgrade.GiftUpgradeRecord{}).Error)
	guRecords := []livegiftupgrade.GiftUpgrade{
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: gifts[0].GiftID, More: []byte(`{}`)},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: gifts[0].GiftID, UpgradeGiftID: gifts[1].GiftID},
	}
	err := servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), guRecords)
	require.NoError(err)
	err = livegiftupgrade.DB().Create(&livegiftupgrade.GiftUpgradeRecord{
		BaseGiftID:    gifts[0].GiftID,
		UpgradeGiftID: gifts[1].GiftID,
		UserID:        1,
		RoomID:        1,
		CreateTime:    now,
	}).Error
	require.NoError(err)

	param = giftSendParam{
		GiftID: gifts[0].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{},
	}
	require.NoError(param.findGift())
	require.NotNil(param.g)
	assert.Equal(param.GiftID, param.g.GiftID)
	require.NotNil(param.baseUpgradeGift)
	assert.Equal(gifts[0].GiftID, param.baseUpgradeGift.GiftID)

	param = giftSendParam{
		GiftID: gifts[1].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{UID: 1},
	}
	require.NoError(param.findGift())
	require.NotNil(param.g)
	require.NotNil(param.baseUpgradeGift)
	assert.Equal(gifts[0].GiftID, param.baseUpgradeGift.GiftID)

	param = giftSendParam{
		GiftID: gifts[1].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{},
	}
	assert.EqualError(param.findGift(), "无法购买当前礼物")

	// 黑卡钻石皮肤礼物
	service.Cache5s.Flush()
	gifts = []gift.Gift{
		{GiftID: 3445, Type: gift.TypeBlackCard},
		{GiftID: 34467, Type: gift.TypeBlackCard},
		{GiftID: 3444, Type: gift.TypeBlackCard},
	}
	service.Cache5s.Set(keys.KeyOnlineGifts0.Format(), gifts, 0)

	// 测试用户没有黑卡信息
	param = giftSendParam{
		GiftID: gifts[0].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{UID: 34341},
	}
	assert.EqualError(param.findGift(), "无法购买当前礼物")

	// 测试无黑卡钻石皮肤信息
	param = giftSendParam{
		GiftID: gifts[1].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{UID: 12},
	}
	assert.EqualError(param.findGift(), "无法购买当前礼物")

	// 测试用户无权送出黑卡钻石皮肤礼物
	param = giftSendParam{
		GiftID: gifts[0].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{UID: 12},
	}
	assert.EqualError(param.findGift(), "无法购买当前礼物")

	// 测试用户正常送出黑卡钻石皮肤礼物
	param = giftSendParam{
		GiftID: gifts[2].GiftID,
		r:      &room.Room{},
		user:   &liveuser.Simple{UID: 12},
	}
	assert.NoError(param.findGift())
}

func TestGiftSendParam_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID        = int64(22489473)
		testUserID        = int64(1)
		testTransactionID = int64(12345678)
	)
	r, err := room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	param := giftSendParam{
		GiftNum:  1,
		SendType: sendTypeObtainMedal,
		r:        r,
		user:     &liveuser.Simple{UID: testUserID},
		g: &gift.Gift{
			GiftID: livemedal.ObtainMedalGiftID,
			Price:  60,
			Type:   gift.TypeNormal,
		},
	}
	cancel := mrpc.SetMock(userapi.URISendGift, func(i interface{}) (interface{}, error) {
		return userapi.BalanceResp{
			TransactionID: testTransactionID,
			Balance:       10,
			Price:         60,
		}, nil
	})
	defer cancel()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": testRoomID, "user_id": testUserID, "gift_id": livemedal.ObtainMedalGiftID}
	_, err = livegifts.Collection().DeleteMany(ctx, filter)
	require.NoError(err)

	resp, err := param.send()
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(int64(10), resp.Balance.Balance)
	require.NotNil(resp.Room)
	require.NotNil(resp.Medal)
	lg, err := livegifts.FindOne(filter)
	require.NoError(err)
	require.NotNil(lg)
	require.NotEmpty(lg.TransactionIDs)
	assert.Equal(testTransactionID, lg.TransactionIDs[0])
}

func TestGiftSendParam_addMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID = int64(22489473)
		testUserID = int64(1)
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": testRoomID, "user_id": testUserID}
	_, err := livemedal.Collection().DeleteOne(ctx, filter)
	require.NoError(err)

	r, err := room.Find(testRoomID)
	require.NoError(err)
	require.NotNil(r)
	param := giftSendParam{
		GiftNum: 1,
		r:       r,
		user:    &liveuser.Simple{UID: testUserID},
		g: &gift.Gift{
			Price: 60,
			Type:  gift.TypeNormal,
		},
		lg: &livegifts.LiveGift{
			Price: 60,
		},
	}

	param.addMedalPoint()
	require.NotNil(param.roomMedal)
	assert.Equal(1, param.roomMedal.Level)

	param.g.Price = 0
	param.g.Attr.Set(gift.AttrDisableMedalPoint)
	param.addMedalPoint()
	assert.Equal(1, param.roomMedal.Level)
}

func TestGiftSendParam_addMultiConnect(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	param := giftSendParam{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 1,
				Status: room.Status{MultiConnect: room.MultiConnectStatusOngoing},
			},
		},
		g: &gift.Gift{
			Price: 10,
		},
		GiftNum: 50,
	}
	param.addMultiConnectScore()
	assert.Empty(param.broadcastElems)

	members := []livemulticonnect.GroupMember{
		{RoomID: 1, EndTime: 0, GroupID: 1},
		{RoomID: 2, EndTime: 0, GroupID: 1},
		{RoomID: 3, EndTime: 0, GroupID: 2},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param.addMultiConnectScore()
	assert.Len(param.broadcastElems, 2)
}

func TestSingleCombo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testKey := "test_combo_cache"
	require.NoError(service.Redis.Del(testKey).Err())
	newTestCombo := func() *singleComboInfo {
		return &singleComboInfo{
			key:                testKey,
			fieldComboLock:     "lock",
			fieldComboID:       "combo_id",
			fieldComboGiftNum:  "gift_num",
			fieldComboLastTime: "last_time",
		}
	}

	p := &giftSendParam{
		GiftNum: 2,
		lg: &livegifts.LiveGift{
			OID: primitive.NewObjectID(),
		},
		g: &gift.Gift{},
	}
	uc := newTestCombo()
	ok, err := uc.loadCache(p)
	require.NoError(err)
	assert.False(ok)

	uc = newTestCombo()
	err = uc.saveCombo(p)
	require.NoError(err)
	res := newTestCombo()
	ok, err = res.loadCache(p)
	require.NoError(err)
	require.True(ok)
	assert.Equal(uc.comboID, res.comboID)
	assert.Equal(uc.giftNum, res.giftNum)
	assert.NotZero(res.lastTimeMilliUnix)

	p.GiftNum = 10
	err = uc.updateCombo(p)
	require.NoError(err)
	assert.EqualValues(12, uc.giftNum)
	res = newTestCombo()
	ok, err = res.loadCache(p)
	require.NoError(err)
	require.True(ok)
	assert.Equal(uc.comboID, res.comboID)
	assert.EqualValues(12, res.giftNum)
	assert.NotZero(res.lastTimeMilliUnix)
}

func TestGiftSendParam_saveCombo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("单人连击", func(t *testing.T) {
		r := &room.Room{Helper: room.Helper{RoomID: 12, CreatorID: 12}}
		u := &liveuser.Simple{UID: 1}
		comboGift := &gift.Gift{
			GiftID:         1,
			Price:          100,
			Comboable:      gift.ComboableTypeSingle,
			ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
			WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
		}
		param := giftSendParam{
			GiftNum: 1,
			Combo:   0,
			r:       r,
			user:    u,
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(1)
		require.NoError(service.Redis.Del(keys.KeyRoomUserGiftCombo3.Format(r.RoomID, u.UserID(), comboGift.GiftID)).Err())
		err := param.saveCombo()
		require.NoError(err)
		assert.Equal(1, param.combo.Num)
		assert.Equal(param.lg.OID.Hex(), param.combo.ID)
		lg, err := livegifts.FindOne(bson.M{"_id": param.lg.OID})
		require.NoError(err)
		require.NotNil(lg)
		assert.EqualValues(1, lg.GiftNum)
		assert.EqualValues(100, lg.Price)
		assert.Equal([]int64{1}, lg.TransactionIDs)

		// 再次连击，触发特效
		param = giftSendParam{
			GiftNum: 10,
			Combo:   1,
			r:       r,
			user:    u,
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(2)
		err = param.saveCombo()
		require.NoError(err)
		require.NotNil(param.combo)
		assert.Equal(11, param.combo.Num)
		assert.NotEmpty(param.combo.EffectURL)
		assert.NotEmpty(param.combo.WebEffectURL)
		assert.Equal(primitive.NilObjectID, param.lg.OID)
		lgOID, err := primitive.ObjectIDFromHex(param.combo.ID)
		require.NoError(err)
		lg, err = livegifts.FindOne(bson.M{"_id": lgOID})
		require.NoError(err)
		require.NotNil(lg)
		assert.EqualValues(11, lg.GiftNum)
		assert.EqualValues(1100, lg.Price)
		assert.Equal([]int64{1, 2}, lg.TransactionIDs)

		// 再次连击，未触发特效
		param = giftSendParam{
			GiftNum: 8,
			Combo:   1,
			r:       r,
			user:    u,
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(3)
		err = param.saveCombo()
		require.NoError(err)
		require.NotNil(param.combo)
		assert.Equal(19, param.combo.Num)
		assert.Empty(param.combo.EffectURL)
		assert.Empty(param.combo.WebEffectURL)
		assert.Equal(primitive.NilObjectID, param.lg.OID)
		lgOID, err = primitive.ObjectIDFromHex(param.combo.ID)
		require.NoError(err)
		lg, err = livegifts.FindOne(bson.M{"_id": lgOID})
		require.NoError(err)
		require.NotNil(lg)
		assert.EqualValues(19, lg.GiftNum)
		assert.EqualValues(1900, lg.Price)
		assert.Equal([]int64{1, 2, 3}, lg.TransactionIDs)

		// 再次送礼，客户端不在连击状态中，触发新连击
		param = giftSendParam{
			GiftNum: 8,
			Combo:   0,
			r:       r,
			user:    u,
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(4)
		err = param.saveCombo()
		require.NoError(err)
		require.NotNil(param.combo)
		assert.Equal(8, param.combo.Num)
		assert.Empty(param.combo.EffectURL)
		assert.Empty(param.combo.WebEffectURL)
		assert.Equal(param.lg.OID.Hex(), param.combo.ID)
		lg, err = livegifts.FindOne(bson.M{"_id": param.lg.OID})
		require.NoError(err)
		require.NotNil(lg)
		assert.EqualValues(8, lg.GiftNum)
		assert.EqualValues(800, lg.Price)
		assert.Equal([]int64{4}, lg.TransactionIDs)
	})

	t.Run("直播间一起送连击", func(t *testing.T) {
		r := &room.Room{Helper: room.Helper{RoomID: 12, CreatorID: 12}}
		comboGift := &gift.Gift{
			GiftID:    2,
			Price:     100,
			Comboable: gift.ComboableTypeMulti,
			Combos: []gift.Combo{
				{
					TargetPrice:    1000,
					RepeatAddPrice: 0,
					ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
					WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
				},
				{
					TargetPrice:    2000,
					RepeatAddPrice: 0,
					ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
					WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
				},
				{
					TargetPrice:    10000,
					RepeatAddPrice: 8000,
					ComboEffect:    "https://static-test.maoercdn.com/gifts/001.png",
					WebComboEffect: "https://static-test.maoercdn.com/gifts/001-web.png",
				},
			},
		}
		param := giftSendParam{
			GiftNum: 3,
			r:       r,
			user:    &liveuser.Simple{UID: 1},
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(1)
		testTimeUnix := int64(123456789)
		goutil.SetTimeNow(func() time.Time { return time.Unix(testTimeUnix, 0) })
		defer goutil.SetTimeNow(nil)
		require.NoError(service.Redis.Del(keys.KeyRoomGiftMultiCombo2.Format(r.RoomID, comboGift.GiftID)).Err())
		// 用户 1 送礼，未触发连击特效
		err := param.saveCombo()
		require.NoError(err)
		assert.Equal(3, param.multiCombo.Num)
		assert.Equal(param.user.UserID(), param.multiCombo.Top1UserID)

		// 用户 2 送礼，触发连击特效
		param = giftSendParam{
			GiftNum: 9,
			r:       r,
			user:    &liveuser.Simple{UID: 2},
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(2)
		err = param.saveCombo()
		require.NoError(err)
		require.NotNil(param.combo)
		assert.Equal(12, param.multiCombo.TotalNum)
		assert.Equal(9, param.multiCombo.Num)
		assert.NotEmpty(param.multiCombo.EffectURL)
		assert.NotEmpty(param.multiCombo.WebEffectURL)
		assert.Equal(param.user.UserID(), param.multiCombo.Top1UserID)

		// 用户 1 再次送礼，触发特效
		param = giftSendParam{
			GiftNum: 9,
			r:       r,
			user:    &liveuser.Simple{UID: 1},
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(3)
		err = param.saveCombo()
		require.NoError(err)
		require.NotNil(param.multiCombo)
		assert.Equal(12, param.multiCombo.Num)
		assert.NotEmpty(param.multiCombo.EffectURL)
		assert.NotEmpty(param.multiCombo.WebEffectURL)

		// 连击结束，用户 1 再次送礼，触发新连击
		goutil.SetTimeNow(func() time.Time { return time.Unix(testTimeUnix+100, 0) })
		param = giftSendParam{
			GiftNum: 2,
			r:       r,
			user:    &liveuser.Simple{UID: 1},
			g:       comboGift,
		}
		param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
			SetGift(param.g, int64(param.GiftNum)).SetTransactionIDs(3)
		err = param.saveCombo()
		require.NoError(err)
		require.NotNil(param.multiCombo)
		assert.Equal(2, param.multiCombo.Num)
		assert.Empty(param.multiCombo.EffectURL)
		assert.Empty(param.multiCombo.WebEffectURL)
	})
}

func TestGiftSendParam_buildUpgradeGift(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().Unix()
	param := giftSendParam{
		GiftNum: 1,
		user:    &liveuser.Simple{UID: 1},
		baseUpgradeGift: &gift.Gift{
			GiftID:     1,
			ToggleTime: now,
		},
	}
	require.NoError(livegiftupgrade.DB().Delete(&livegiftupgrade.GiftUpgrade{}).Error)
	require.NoError(livegiftupgrade.DB().Delete(&livegiftupgrade.GiftUpgradeRecord{}).Error)
	userMeta := usermeta.UserMeta{
		UserID: param.user.UserID(),
	}
	err := usermeta.Collection().FindOneAndReplace(context.Background(),
		bson.M{"user_id": param.user.UserID()}, userMeta).Err()
	require.NoError(err)

	moreInfo := livegiftupgrade.MoreInfo{
		FirstUpgradeNum: 3,
		UpgradeNum:      10,
		LabelIcon:       "oss://icon.png",
	}
	moreInfoBytes, err := json.Marshal(moreInfo)
	require.NoError(err)
	guRecords := []livegiftupgrade.GiftUpgrade{
		{Type: livegiftupgrade.GiftUpgradeTypeBase, BaseGiftID: 1, More: moreInfoBytes},
		{Type: livegiftupgrade.GiftUpgradeTypeFull, BaseGiftID: 1, UpgradeGiftID: 3},
		{Type: livegiftupgrade.GiftUpgradeTypeUpgrade, BaseGiftID: 1, UpgradeGiftID: 6},
	}
	err = servicedb.BatchInsert(livegiftupgrade.DB(), livegiftupgrade.GiftUpgrade{}.TableName(), guRecords)
	require.NoError(err)
	upgradeGift := param.buildUpgradeGift()
	require.NotNil(upgradeGift)
	assert.Zero(upgradeGift.UpgradeNum.RemainUpgradeNum)
	assert.Equal(moreInfo.FirstUpgradeNum, upgradeGift.UpgradeNum.UpgradeGiftNum)
	assert.Equal(1, upgradeGift.UpgradeNum.GiftNum)
	require.NotNil(upgradeGift.Discount)
	assert.NotEmpty(upgradeGift.Discount.LabelIconURL)

	// 触发升级
	param.GiftNum = 2
	upgradeGift = param.buildUpgradeGift()
	require.NotNil(upgradeGift)
	assert.Equal(1, upgradeGift.UpgradeNum.RemainUpgradeNum)
	assert.Equal(moreInfo.UpgradeNum, upgradeGift.UpgradeNum.UpgradeGiftNum)
	assert.Zero(upgradeGift.UpgradeNum.GiftNum)
	require.Nil(upgradeGift.Discount)
}

func TestGiftSendParam_addRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := giftSendParam{
		GiftNum: 1,
		r:       &room.Room{Helper: room.Helper{CreatorID: 12}},
		user:    &liveuser.Simple{UID: 1},
		g: &gift.Gift{
			Price: 1,
		},
		lg: &livegifts.LiveGift{
			Price: 60,
		},
	}
	param.r.RoomID = 1478963
	now := goutil.TimeNow()
	keys := []string{
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeCurrent, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeHourly, now),
		roomsrank.Key(param.r.RoomID, roomsrank.RankTypeWeek, now),
	}
	require.NoError(service.Redis.Del(keys...).Err())
	assert.NotPanics(func() { param.addRevenueRank() })
	// 通过判断键是否存在来看榜单是否添加成功
	val, err := service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.Equal(int64(2), val)
	// 测试开播状态下会增加本场榜
	param.r.Status.Open = room.StatusOpenTrue
	assert.NotPanics(func() { param.addRevenueRank() })
	val, err = service.Redis.Del(keys...).Result()
	require.NoError(err)
	assert.Equal(int64(3), val)
}

func TestGiftSendParam_addPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := giftSendParam{
		user:    &liveuser.Simple{UID: 1},
		g:       &gift.Gift{Price: 10},
		GiftNum: 1,
		lg: &livegifts.LiveGift{
			Price: 10,
		},
	}

	param.r = new(room.Room)
	param.addPK()
	assert.Empty(param.broadcastElems)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livepk.PKCollection()
	_, err := col.DeleteMany(ctx, bson.M{"status": livepk.PKRecordStatusFighting})
	require.NoError(err)
	lp := livepk.LivePK{
		OID:    primitive.NewObjectID(),
		Status: livepk.PKRecordStatusFighting,
		Fighters: [2]*livepk.Fighter{
			{
				RoomID: 1,
			},
			{
				RoomID: 114693474,
			},
		},
	}
	_, err = col.InsertOne(ctx, lp)
	require.NoError(err)
	param.r.RoomID = lp.Fighters[1].RoomID
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(param.r.RoomID),
		"test", time.Second).Err())
	param.addPK()
	assert.Len(param.broadcastElems, 2)
}

func TestGiftSendParam_addUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := giftSendParam{
		RoomID: 123,
		user:   &liveuser.Simple{UID: 12},
		r: &room.Room{
			Helper: room.Helper{CreatorUsername: "aaa"},
		},
		g:       &gift.Gift{Price: 10},
		GiftNum: 1,
		lg: &livegifts.LiveGift{
			Price: 10,
		},
	}
	before, err := liveuser.Find(param.user.UserID())
	require.NoError(err)
	require.NotNil(before)
	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.user.UserID())
	require.NoError(err)
	assert.Equal(param.lg.Price*10, after.Contribution-before.Contribution)
	before = after
	param.uv = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.user.UserID())
	require.NoError(err)
	assert.Equal(param.lg.Price*2*10, after.Contribution-before.Contribution)
}

func TestGiftSendParam_buildIMMessage(t *testing.T) {
	param := giftSendParam{
		user:    &liveuser.Simple{UID: 1},
		GiftNum: 1,
		r:       testRoom,
		g: &gift.Gift{
			GiftID: 1,
			Point:  10,
		},
		lg: &livegifts.LiveGift{
			Price: 10,
		},
		combo: &livegifts.Combo{
			Num:          100,
			EffectURL:    "https://www.test.com/combo.png",
			WebEffectURL: "https://www.test.com/combo-web.png",
		},
		bubble: &bubble.Simple{Type: "normal"},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
		SetGift(param.g, 1)
	param.buildIMMessage()
	assert.Len(t, param.broadcastElems, 1)

	param.g.Attr.Set(gift.AttrAlwaysNotify)
	param.buildIMMessage()
	require.Len(t, param.broadcastElems, 3)
	res, ok := param.broadcastElems[2].Payload.(livegifts.ComboNotifyPayload)
	require.True(t, ok)
	assert.Contains(t, res.Message, "100 个")
	assert.Equal(t, param.bubble, res.Bubble)
	assert.Equal(t, param.combo.EffectURL, res.Combo.EffectURL)
	assert.Equal(t, param.combo.WebEffectURL, res.Combo.WebEffectURL)

	param = giftSendParam{
		FromRoomID: openingRoomID,
		user:       &liveuser.Simple{UID: 1},
		GiftNum:    1,
		r:          testRoom,
		g: &gift.Gift{
			GiftID: 1,
			Point:  10,
		},
		lg: &livegifts.LiveGift{
			Price: 10,
		},
	}
	param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
		SetGift(param.g, 1)
	param.buildIMMessage()
	require.Len(t, param.broadcastElems, 2)
	assert.Equal(t, param.r.RoomID, param.broadcastElems[0].RoomID)
	assert.Zero(t, param.broadcastElems[0].UserID)
	assert.Equal(t, param.user.UserID(), param.broadcastElems[1].UserID)
	assert.Equal(t, param.FromRoomID, param.broadcastElems[1].RoomID)

	t.Run("GiftNotification", func(t *testing.T) {
		now := goutil.TimeNow()
		expTime := now.Add(time.Hour).Unix()
		testUserID := int64(12345678)

		ctx, cancelCtx := service.MongoDB.Context()
		defer cancelCtx()

		_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})
		require.NoError(t, err)
		defer userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})

		t.Run("用户没有送礼通知外观", func(t *testing.T) {
			param := giftSendParam{
				user:    &liveuser.Simple{UID: testUserID},
				GiftNum: 1,
				r:       testRoom,
				g: &gift.Gift{
					GiftID: 301,
					Point:  10,
				},
			}
			param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
				SetGift(param.g, 1)

			param.buildIMMessage()
			require.Len(t, param.broadcastElems, 1)

			roomMessage, ok := param.broadcastElems[0].Payload.(*livegifts.RoomMessage)
			require.True(t, ok)
			assert.Nil(t, roomMessage.GiftNotification)
		})

		t.Run("用户佩戴送礼通知外观", func(t *testing.T) {
			testAppearance := &userappearance.UserAppearance{
				UserID:       testUserID,
				AppearanceID: 10001,
				Type:         appearance.TypeGiftNotification,
				Status:       userappearance.StatusWorn,
				StartTime:    now.Add(-time.Minute).Unix(),
				ExpireTime:   &expTime,
				TextColorItem: &appearance.TextColorItem{
					Username: "#FF0000",
				},
				TextColor: "#00FF00",
				Frame:     "oss://test/frame.png",
			}
			_, err := userappearance.Collection().InsertOne(ctx, testAppearance)
			require.NoError(t, err)

			param := giftSendParam{
				user:    &liveuser.Simple{UID: testUserID},
				GiftNum: 1,
				r:       testRoom,
				g: &gift.Gift{
					GiftID: 301,
					Point:  10,
				},
			}
			param.lg = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID, param.user, nil).
				SetGift(param.g, 1)

			param.buildIMMessage()
			require.Len(t, param.broadcastElems, 1)

			roomMessage, ok := param.broadcastElems[0].Payload.(*livegifts.RoomMessage)
			require.True(t, ok)
			require.NotNil(t, roomMessage.GiftNotification)
			assert.Equal(t, testAppearance.TextColorItem.Username, roomMessage.GiftNotification.UsernameColor)
			assert.Equal(t, testAppearance.TextColor, roomMessage.GiftNotification.TextColor)
			assert.Contains(t, roomMessage.GiftNotification.FrameURL, "test/frame.png")
		})
	})
}

func TestGiftSendParam_broadcast(t *testing.T) {
	assert := assert.New(t)

	var notifyCount int
	cancel := mrpc.SetMock("im://broadcast/many", func(i interface{}) (interface{}, error) {
		notifyCount++
		tutil.PrintJSON(i)
		return true, nil
	})
	defer cancel()
	r := new(room.Room)
	r.CreatorUsername = "test"
	param := giftSendParam{
		broadcastElems: []*userapi.BroadcastElem{
			{Type: 1, RoomID: 123, Payload: "test"},
		},
	}
	param.broadcast()
	assert.Equal(notifyCount, 1)
}
