package fansbox

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxrewardlog"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxusertask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func createTestRoom(roomID int64, medal *room.Medal) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().DeleteMany(ctx, bson.M{
		"room_id": roomID,
	})
	if err != nil {
		return err
	}
	_, err = room.Collection().InsertOne(ctx, room.Room{
		Helper: room.Helper{
			RoomID:    roomID,
			Medal:     medal,
			NameClean: "test_fans_" + strconv.FormatInt(roomID, 10),
		},
	})
	return err
}

func TestDrawTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(drawParam{}, "room_id")
	kc.Check(drawResp{}, "prizes", "prize_tip")
	kc.Check(prizesInfo{}, "prize_type", "prize_element_id", "prize_name", "prize_icon_url", "prize_num")
}

func TestActionFansBoxDraw(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试非 App 请求的情况
	api := "/api/v2/chatroom/fans-box/draw"
	param := &drawParam{
		RoomID: 233,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	c.Equip().FromApp = false
	_, _, err := ActionFansBoxDraw(c)
	assert.Equal(handler.ErrBadRequest, err)

	// 测试接口请求失败（参数错误）的情况
	param = &drawParam{
		RoomID: 0,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.Equip().FromApp = true
	_, _, err = ActionFansBoxDraw(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试接口请求成功的情况
	// Mock data
	testRoomID := int64(45864894)
	testUserID := int64(12)
	testAppearanceID := int64(334455)
	// 创建直播间
	require.NoError(createTestRoom(testRoomID, &room.Medal{
		Name: "test_45864894",
	}))
	// 创建直播间粉丝团宝箱任务
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().
		Delete("", "room_id = ?", testRoomID).Error)
	task := &livefansboxtask.LiveFansBoxTask{
		Level:     livefansbox.Level2,
		Bizdate:   todayFormat,
		RoomID:    testRoomID,
		FansCount: 50,
		Energy:    1,
		Status:    livefansboxtask.StatusFinished,
	}
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Create(task).Error)
	// 添加粉丝勋章数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{
		"room_id": testRoomID,
		"user_id": testUserID,
	})
	require.NoError(err)
	_, err = livemedal.Collection().InsertOne(ctx, livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID: testRoomID,
			UserID: testUserID,
			Status: livemedal.StatusOwned,
		},
	})
	require.NoError(err)
	// 添加用户完成任务的数据
	require.NoError(livefansboxusertask.LiveFansBoxUserTask{}.DB().
		Delete("", "fans_box_task_id = ?", task.ID).Error)
	userTask := &livefansboxusertask.LiveFansBoxUserTask{
		RoomID:        testRoomID,
		UserID:        testUserID,
		FansBoxTaskID: task.ID,
		UserEnergy:    1,
	}
	require.NoError(livefansboxusertask.LiveFansBoxUserTask{}.DB().Create(userTask).Error)
	// 添加奖励的外观数据
	_, err = appearance.Collection().DeleteMany(ctx, bson.M{
		"id": testAppearanceID,
	})
	require.NoError(err)
	prizeAppearance := appearance.Appearance{
		ID:    testAppearanceID,
		Name:  "fans_box_avatar_frame",
		Type:  appearance.TypeAvatarFrame,
		Frame: "test://test/test_frame.png",
	}
	_, err = appearance.Collection().InsertOne(ctx, prizeAppearance)
	require.NoError(err)
	lockKey := keys.LockFansBoxDraw1.Format(param.userID)
	require.NoError(service.Redis.Del(lockKey).Err())
	param = &drawParam{
		RoomID: testRoomID,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.Equip().FromApp = true
	data, message, err := ActionFansBoxDraw(c)
	require.NoError(err)
	assert.Equal("success", message)
	drawData, ok := data.(drawResp)
	require.True(ok)
	assert.Equal("查看路径：外观中心", drawData.PrizeTip)
	require.Len(drawData.Prizes, 1)
	assert.Equal(liveprize.TypeUserAppearance, drawData.Prizes[0].PrizeType)
	assert.Equal(testAppearanceID, drawData.Prizes[0].PrizeElementID)
	assert.Equal("fans_box_avatar_frame", drawData.Prizes[0].PrizeName)
	assert.Equal("https://static-test.missevan.com/test/fans_box.png", drawData.Prizes[0].PrizeIconURL)
	assert.EqualValues(1, drawData.Prizes[0].PrizeNum)

	// 测试重复领取的情况
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	c.Equip().FromApp = true
	_, _, err = ActionFansBoxDraw(c)
	require.EqualError(err, "已领取过奖励")
}

func TestDrawParam_load(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误的情况
	api := "/api/v2/chatroom/fans-box/draw"
	param := &drawParam{
		RoomID: 0,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, param)
	err := param.load(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试直播间不存在的情况
	param = &drawParam{
		RoomID: 99999999999999,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 测试直播间未开通粉丝勋章功能的情况
	testRoomID1 := int64(4864894)
	require.NoError(createTestRoom(testRoomID1, nil))
	param = &drawParam{
		RoomID: testRoomID1,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.EqualError(err, "该直播间未开启粉丝团宝箱任务")

	// 测试直播间未开启粉丝图宝箱任务的情况
	testRoomID2 := int64(4864453)
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().
		Delete("", "room_id = ?", testRoomID2).Error)
	require.NoError(createTestRoom(testRoomID2, &room.Medal{
		Name: "test_4864453",
	}))
	param = &drawParam{
		RoomID: testRoomID2,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.EqualError(err, "该直播间未开启粉丝团宝箱任务")

	// 测试宝箱任务未完成的情况
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	task := &livefansboxtask.LiveFansBoxTask{
		Level:     livefansbox.Level2,
		Bizdate:   todayFormat,
		RoomID:    testRoomID2,
		FansCount: 50,
		Energy:    1,
		Status:    livefansboxtask.StatusUnfinished,
	}
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Create(task).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	err = param.load(c)
	assert.EqualError(err, "粉丝团宝箱任务未完成")

	// 测试正常 load 的情况
	require.NoError(livefansboxtask.LiveFansBoxTask{}.DB().Where("id = ?", task.ID).
		Update("status", livefansboxtask.StatusFinished).Error)
	c = handler.NewTestContext(http.MethodPost, api, true, param)
	require.NoError(param.load(c))
	assert.EqualValues(testRoomID2, param.RoomID)
	assert.EqualValues(12, param.userID)
	require.NotNil(param.room)
	assert.EqualValues(testRoomID2, param.room.RoomID)
	require.NotNil(param.taskInfo)
	assert.EqualValues(task.ID, param.taskInfo.ID)
}

func TestDrawParam_lock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试加锁成功的情况
	param := &drawParam{
		userID: 89456465,
	}
	lockKey := keys.LockFansBoxDraw1.Format(param.userID)
	require.NoError(service.Redis.Del(lockKey).Err())
	lockFunc, err := param.lock()
	require.NoError(err)
	assert.NotNil(lockFunc)

	// 测试加锁失败的情况（重复锁定）
	lockFunc2, err := param.lock()
	require.EqualError(err, "操作频繁，请稍后再试")
	assert.Nil(lockFunc2)

	// 测试释放锁方法有效
	lockFunc()
	count, err := service.Redis.Exists(lockKey).Result()
	require.NoError(err)
	assert.EqualValues(0, count)
}

func TestDrawParam_checkUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试主播不能抽奖的情况
	testRoomID := int64(18786454)
	testCreatorID := int64(986446)
	testUserID := int64(65464574)
	testTaskID := int64(98635544)
	param := &drawParam{
		RoomID: testRoomID,
		userID: testCreatorID,
		room: &room.Room{
			Helper: room.Helper{
				RoomID:    testRoomID,
				CreatorID: testCreatorID,
			},
		},
		taskInfo: &livefansboxtask.TaskInfo{
			ID:     testTaskID,
			RoomID: testRoomID,
			Level:  livefansbox.Level2,
			Status: livefansboxtask.StatusFinished,
		},
	}
	err := param.checkUser()
	assert.EqualError(err, "不可领取自己直播间的粉丝团宝箱奖励")

	// 测试用户未拥有粉丝勋章的情况
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{
		"user_id": testUserID,
	})
	require.NoError(err)
	param.userID = testUserID
	err = param.checkUser()
	assert.EqualError(err, "需开通粉丝勋章才可领取奖励")

	// 测试用户未贡献过能量值的情况
	_, err = livemedal.Collection().InsertOne(ctx, livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID: testRoomID,
			UserID: testUserID,
			Status: livemedal.StatusOwned,
		},
	})
	require.NoError(err)
	param.userTask = nil
	err = param.checkUser()
	assert.EqualError(err, "需贡献过能量值才可领取奖励")

	// 测试用户贡献的能量值为 0 的情况
	require.NoError(livefansboxusertask.LiveFansBoxUserTask{}.DB().
		Delete("", "fans_box_task_id = ?", testTaskID).Error)
	userTask := &livefansboxusertask.LiveFansBoxUserTask{
		RoomID:        testRoomID,
		UserID:        testUserID,
		FansBoxTaskID: testTaskID,
		UserEnergy:    0,
	}
	require.NoError(livefansboxusertask.LiveFansBoxUserTask{}.DB().Create(userTask).Error)
	err = param.checkUser()
	assert.EqualError(err, "需贡献过能量值才可领取奖励")

	// 测试用户可领取奖励的情况
	require.NoError(livefansboxusertask.LiveFansBoxUserTask{}.DB().Where("id = ?", userTask.ID).
		Update("user_energy", 1).Error)
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().
		Delete("", "fans_box_task_id = ?", testTaskID).Error)
	err = param.checkUser()
	require.NoError(err)

	// 测试用户已经领取过奖励的情况
	testTaskLog := &livefansboxrewardlog.LiveFansBoxRewardLog{
		RoomID:            testRoomID,
		UserID:            testUserID,
		FansBoxTaskID:     testTaskID,
		FansBoxUserTaskID: userTask.ID,
		More:              []byte("{\"reward\":{\"type\":3,\"prize_ids\":[1,2]}}"),
	}
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().Create(testTaskLog).Error)
	err = param.checkUser()
	assert.EqualError(err, "已领取过奖励")
}

func TestDrawParam_draw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试抽取奖励失败的情况（未配置奖励）
	testRoomID := int64(28786454)
	testUserID := int64(45464574)
	testTaskID := int64(58635544)
	param := &drawParam{
		RoomID: testRoomID,
		userID: testUserID,
		taskInfo: &livefansboxtask.TaskInfo{
			ID:     testTaskID,
			RoomID: testRoomID,
			Level:  livefansbox.Level1,
			Status: livefansboxtask.StatusFinished,
			MoreInfo: livefansbox.More{
				Rewards: []livefansbox.RewardInfo{},
			},
		},
	}
	reward, err := param.draw()
	assert.Nil(reward)
	assert.EqualError(err, "服务错误，请稍后再试")

	// 测试有库存时，抽取奖励成功的情况
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key1 := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(testRoomID, livefansbox.Level1,
		livefansbox.RewardTypeExclusiveAndFreeGift, todayFormat)
	key2 := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(testRoomID, livefansbox.Level1,
		livefansbox.RewardTypeAvatarFrame, todayFormat)
	require.NoError(service.Redis.Del(key1, key2).Err())
	param.taskInfo.MoreInfo.Rewards = []livefansbox.RewardInfo{
		{Type: livefansbox.RewardTypeExclusiveAndFreeGift, PrizeIDs: []int64{10, 11}, Weight: 500000, DailyStock: 100},
		{Type: livefansbox.RewardTypeAvatarFrame, PrizeIDs: []int64{13}, Weight: 1, DailyStock: 100},
	}
	reward, err = param.draw()
	require.NoError(err)
	assert.NotNil(reward)
	assert.Contains([]int{livefansbox.RewardTypeExclusiveAndFreeGift, livefansbox.RewardTypeAvatarFrame}, reward.Type)

	// 测试部分奖励无库存时，只能抽取到有库存奖励的情况
	require.NoError(service.Redis.Set(key1, 100, 30*time.Second).Err())
	reward, err = param.draw()
	require.NoError(err)
	assert.NotNil(reward)
	assert.EqualValues(livefansbox.RewardTypeAvatarFrame, reward.Type)

	// 测试奖励都无库存时，只能抽取到抽取奖励成功的情况（使用保底奖励）
	require.NoError(service.Redis.Set(key2, 100, 10*time.Second).Err())
	reward, err = param.draw()
	require.NoError(err)
	assert.NotNil(reward)
	assert.EqualValues(livefansbox.GetGuaranteedReward(), reward)
}

func TestDrawParam_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试奖励对应的奖品不存在，导致发放奖励失败的情况
	testRoomID := int64(895486454)
	testUserID := int64(695234574)
	testTaskID := int64(3986544)
	testUserTaskID := int64(65564466)
	testPrizeID := int64(945545)
	testAppearanceID := int64(565454)
	testReward := livefansbox.RewardInfo{
		Type:       livefansbox.RewardTypeAvatarFrame,
		PrizeIDs:   []int64{testPrizeID},
		Weight:     1,
		DailyStock: 100,
	}
	param := &drawParam{
		RoomID: testRoomID,
		userID: testUserID,
		taskInfo: &livefansboxtask.TaskInfo{
			ID:     testTaskID,
			RoomID: testRoomID,
			Level:  livefansbox.Level1,
			Status: livefansboxtask.StatusFinished,
			MoreInfo: livefansbox.More{
				Rewards: []livefansbox.RewardInfo{testReward},
			},
		},
		userTask: &livefansboxusertask.LiveFansBoxUserTask{
			ID:     testUserTaskID,
			RoomID: testRoomID,
			UserID: testUserID,
		},
		reward: &testReward,
	}
	require.NoError(liveprize.DB().Table(liveprize.Prize{}.TableName()).
		Delete("", "id = ?", testPrizeID).Error)
	sendPrizes, err := param.send()
	require.EqualError(err, "服务错误，请稍后再试")
	assert.Nil(sendPrizes)

	// 测试奖励发放失败的情况（奖品对应的外观等不存在的情况）
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = appearance.Collection().DeleteMany(ctx, bson.M{
		"id": testAppearanceID,
	})
	require.NoError(err)
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(testRoomID, livefansbox.Level1,
		livefansbox.RewardTypeAvatarFrame, todayFormat)
	require.NoError(service.Redis.Del(key).Err())
	now := goutil.TimeNow()
	require.NoError(liveprize.DB().Table(liveprize.Prize{}.TableName()).Create(&liveprize.Prize{
		ID:          testPrizeID,
		Type:        liveprize.TypeUserAppearance,
		ElementType: appearance.TypeAvatarFrame,
		ElementID:   testAppearanceID,
		Name:        "fans_box_avatar_frame",
		Icon:        "oss://test/avatar_frame.png",
		Num:         1,
		StartTime:   now.Unix(),
		ExpireTime:  now.Add(24 * time.Hour).Unix(),
	}).Error)
	param.reward = &testReward
	sendPrizes, err = param.send()
	require.EqualError(err, "奖励发放失败")
	assert.Nil(sendPrizes)
	// 验证此时库存消耗量增加，增加了领取记录
	rewardLog := &livefansboxrewardlog.LiveFansBoxRewardLog{}
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().
		Where("fans_box_task_id = ?", testTaskID).Take(rewardLog).Error)
	assert.Equal(testUserID, rewardLog.UserID)
	assert.Equal(testRoomID, rewardLog.RoomID)
	consumedStock, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(1, consumedStock)

	// 测试外观存在，正常发放奖励情况
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().
		Delete("", "fans_box_task_id = ?", testTaskID).Error)
	prizeAppearance := appearance.Appearance{
		ID:    testAppearanceID,
		Name:  "fans_box_avatar_frame",
		Type:  appearance.TypeAvatarFrame,
		Frame: "test://test/test_frame.png",
	}
	_, err = appearance.Collection().InsertOne(ctx, prizeAppearance)
	require.NoError(err)
	param.reward = &testReward
	sendPrizes, err = param.send()
	require.NoError(err)
	// 验证奖励数据正确
	assert.NotEmpty(sendPrizes)
	assert.Equal(testPrizeID, sendPrizes[0].ID)

	// 测试重复领取的情况
	param.reward = &testReward
	sendPrizes, err = param.send()
	require.NoError(err)
	assert.Nil(sendPrizes)

	// 测试领取的时候，奖励超过库存，使用保底礼物的情况
	require.NoError(service.Redis.Set(key, testReward.DailyStock, 10*time.Second).Err())
	require.NoError(livefansboxrewardlog.LiveFansBoxRewardLog{}.DB().
		Delete("", "user_id = ?", testUserID).Error)
	param.reward = &testReward
	sendPrizes, err = param.send()
	// 保底礼物未 mock 数据，故会失败
	require.EqualError(err, "服务错误，请稍后再试")
	assert.Nil(sendPrizes)
}

func TestDrawParam_rollbackRewardStock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(987654321)
	testLevel := livefansbox.Level1
	testRewardType := livefansbox.RewardTypeAvatarFrame
	todayFormat := goutil.TimeNow().Format(goutil.TimeFormatYMD)
	key := keys.KeyFansBoxRewardsPrizeConsumedStockNum4.Format(testRoomID, testLevel, testRewardType, todayFormat)
	service.Redis.Set(key, 100, 10*time.Second)
	param := &drawParam{
		taskInfo: &livefansboxtask.TaskInfo{
			RoomID: testRoomID,
			Level:  testLevel,
		},
		reward: &livefansbox.RewardInfo{
			Type: testRewardType,
		},
	}
	param.rollbackRewardStock()
	num, err := service.Redis.Get(key).Int64()
	require.NoError(err)
	assert.EqualValues(99, num)
}
