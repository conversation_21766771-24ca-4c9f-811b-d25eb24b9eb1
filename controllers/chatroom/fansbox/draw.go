package fansbox

import (
	"encoding/json"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/biz/prize"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansbox"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxrewardlog"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxusertask"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type drawParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`

	userID   int64                                    // 登录用户 ID
	room     *room.Room                               // 直播间信息
	taskInfo *livefansboxtask.TaskInfo                // 粉丝团宝箱任务信息
	userTask *livefansboxusertask.LiveFansBoxUserTask // 用户任务信息
	reward   *livefansbox.RewardInfo                  // 抽奖发放的奖励信息
}

type drawResp struct {
	Prizes   []prizesInfo `json:"prizes"`
	PrizeTip string       `json:"prize_tip"`
}

type prizesInfo struct {
	PrizeType      int    `json:"prize_type"`       // 奖品类型 2：外观；4：背包礼物；10：用户定制礼物
	PrizeElementID int64  `json:"prize_element_id"` // 元素 ID
	PrizeName      string `json:"prize_name"`       // 奖品名称
	PrizeIconURL   string `json:"prize_icon_url"`   // 奖品图标 URL
	PrizeNum       int64  `json:"prize_num"`        // 奖品数量
}

// ActionFansBoxDraw 粉丝团宝箱任务抽奖接口
/**
 * @api {post} /api/v2/chatroom/fans-box/draw 粉丝团宝箱任务抽奖接口
 * @apiVersion 0.1.0
 * @apiGroup fans-box
 *
 * @apiPermission user
 *
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "prizes": [ // 奖励领取详情，仅用户已领取奖励时下发，客户端展示奖励弹窗时根据 prizes 数量和 prize_type 判断是否展示立即送出按钮，
 *                   // 只有一种礼物且 prize_type 为 4（背包礼物）时才展示立即送出按钮，点击立即送出按钮时调用 /api/v2/chatroom/backpack/send 送背包礼物接口送礼
 *         {
 *           "prize_type": 2, // 奖品类型：2：外观；4：背包礼物；10：用户定制礼物
 *           "prize_element_id": 111, // 礼物 ID 或外观 ID
 *           "prize_name": "外观奖励XX头像框", // 奖品名称
 *           "prize_icon_url": "http://static-test.maoercdn.com/xxx/xxx.png", // 奖品图片
 *           "prize_num": 2 // 奖品数量
 *         }
 *       ],
 *       "prize_tip": "查看路径：xxxxxx" // 奖品查看路径提示文案
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 */
func ActionFansBoxDraw(c *handler.Context) (handler.ActionResponse, string, error) {
	if !c.Equip().FromApp {
		// 该接口目前仅可通过客户端访问
		return nil, "", handler.ErrBadRequest
	}
	var param drawParam
	err := param.load(c)
	if err != nil {
		return nil, "", err
	}

	// 请求加上防并发锁
	unlock, err := param.lock()
	if err != nil {
		return nil, "", err
	}
	defer unlock()

	// 检查用户是否有抽奖资格
	err = param.checkUser()
	if err != nil {
		return nil, "", err
	}

	// 抽取奖励
	param.reward, err = param.draw()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 发放奖励（若在发放时奖励库存不够，则会自动变更为发放保底奖励）
	sendPrizes, err := param.send()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if sendPrizes == nil {
		return nil, "", actionerrors.ErrBadRequest(handler.CodeUnknownError, "奖励已发放")
	}

	// 构造并返回响应信息
	prizes := make([]prizesInfo, 0, len(sendPrizes))
	for _, sendPrize := range sendPrizes {
		prizes = append(prizes, prizesInfo{
			PrizeType:      sendPrize.Type,
			PrizeElementID: sendPrize.ElementID,
			PrizeName:      sendPrize.Name,
			PrizeIconURL:   storage.ParseSchemeURL(sendPrize.Icon),
			PrizeNum:       sendPrize.Num,
		})
	}
	return drawResp{
		Prizes:   prizes,
		PrizeTip: livefansbox.RewardTipMap[param.reward.Type], // 若未定义该类型文案，则会返回空字符串
	}, "success", nil
}

func (param *drawParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return actionerrors.ErrParams
	}
	if !livefansbox.EnableFansBox() {
		return actionerrors.ErrBadRequest(handler.CodeUnknownError, "功能未开启")
	}

	// 获取直播间信息
	param.room, err = room.FindOne(bson.M{"room_id": param.RoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		// 直播间不存在时报错
		return actionerrors.ErrCannotFindRoom
	}
	if !param.room.HaveMedal() {
		// 直播间未开通粉丝团时不能抽奖
		return actionerrors.ErrBadRequest(handler.CodeUnknownError, "该直播间未开启粉丝团宝箱任务")
	}

	// 获取宝箱奖励信息
	param.taskInfo, err = livefansboxtask.FindTodayTask(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.taskInfo == nil {
		// 直播间未开启宝箱任务时不能抽奖
		return actionerrors.ErrBadRequest(handler.CodeUnknownError, "该直播间未开启粉丝团宝箱任务")
	}
	if param.taskInfo.Status != livefansboxtask.StatusFinished {
		return actionerrors.ErrBadRequest(handler.CodeUnknownError, "粉丝团宝箱任务未完成")
	}
	param.userID = c.User().ID

	return nil
}

// lock 请求加上防并发锁
func (param *drawParam) lock() (func(), error) {
	lockKey := keys.LockFansBoxDraw1.Format(param.userID)
	ok, err := service.Redis.SetNX(lockKey, "1", time.Minute).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrBadRequest(handler.CodeOperateTooFrequently, "操作频繁，请稍后再试")
	}
	return func() {
		err := service.Redis.Del(lockKey).Err()
		if err != nil {
			logger.Error(err)
			return
		}
	}, nil
}

// checkUser 检查用户是否有抽奖资格
func (param *drawParam) checkUser() error {
	// 判断是否为该直播间主播自己抽奖
	if param.userID == param.room.CreatorID {
		// 主播不能抽奖
		return actionerrors.NewErrLiveForbidden("不可领取自己直播间的粉丝团宝箱奖励")
	}
	// 判断用户是否拥有该直播间的粉丝勋章
	hasMedal, err := livemedal.HasUserOwnedMedal(param.userID, param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !hasMedal {
		return actionerrors.NewErrLiveForbidden("需开通粉丝勋章才可领取奖励")
	}
	// 判断用户是否在该直播间贡献过能量值
	param.userTask, err = livefansboxusertask.FindUserTask(param.taskInfo.ID, param.userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.userTask == nil || param.userTask.UserEnergy <= 0 {
		return actionerrors.NewErrLiveForbidden("需贡献过能量值才可领取奖励")
	}
	// 判断用户是否已在该直播间领取过奖励
	hasReward, err := livefansboxrewardlog.HasReward(param.taskInfo.ID, param.userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if hasReward {
		return actionerrors.ErrBadRequest(handler.CodeUnknownError, "已领取过奖励")
	}
	return nil
}

var (
	randSource = goutil.NewLockedSource(goutil.TimeNow().Unix())
)

// draw 抽取奖励
func (param *drawParam) draw() (*livefansbox.RewardInfo, error) {
	if len(param.taskInfo.MoreInfo.Rewards) == 0 {
		logger.WithField("level", param.taskInfo.Level).Error("直播间粉丝团宝箱任务未配置奖励")
		return nil, errors.New("服务错误，请稍后再试")
	}
	// 获取可进行抽奖的奖品信息
	rewards, err := param.taskInfo.GetHasStockRewards()
	if err != nil {
		return nil, err
	}
	if len(rewards) == 0 {
		// 若奖励库存不足，则直接发放保底礼物
		return livefansbox.GetGuaranteedReward(), nil
	}
	// 按奖励配置的权重进行抽奖
	rewardsLen := len(rewards)
	weights := make([]int, 0, rewardsLen)
	for _, reward := range rewards {
		weights = append(weights, int(reward.Weight))
	}
	d, err := goutil.NewDiscreteDistribution(weights, randSource, false)
	if err != nil {
		return nil, err
	}
	return &rewards[d.NextInt()], nil
}

// send 发放奖励
func (param *drawParam) send() ([]*liveprize.Prize, error) {
	isGuaranteed := false // 是否为保底礼物
	if len(param.reward.PrizeIDs) == 0 {
		logger.WithField("level", param.taskInfo.Level).Error("直播间粉丝团宝箱任务未配置奖励的 prize_ids")
		// PASS: 若未配置奖励的 prize_ids，则直接使用保底奖励，避免影响用户正常使用
		param.reward = livefansbox.GetGuaranteedReward()
		isGuaranteed = true
	}

	if !isGuaranteed {
		// 非保底奖励时，奖励库存的消耗数 +1
		consumedStock, err := livefansbox.IncrConsumedStock(param.taskInfo.RoomID, param.taskInfo.Level,
			param.reward.Type, 1)
		if err != nil {
			return nil, err
		}
		if consumedStock > param.reward.DailyStock {
			// 当日奖励库存已用完，此时使用保底奖励
			param.reward = livefansbox.GetGuaranteedReward()
			isGuaranteed = true
		}
	}
	// 获取发放的礼物信息
	sendPrizes, err := liveprize.FindPrizes(param.reward.PrizeIDs)
	if err != nil {
		return nil, err
	}
	if len(sendPrizes) != len(param.reward.PrizeIDs) {
		logger.WithField("level", param.taskInfo.Level).Error("直播间粉丝团宝箱任务配置奖励的奖品不存在")
		return nil, errors.New("服务错误，请稍后再试")
	}
	// 新增奖品发放记录
	more := livefansboxrewardlog.More{
		Reward: livefansboxrewardlog.RewardInfo{
			Type:     param.taskInfo.Level,
			PrizeIDs: param.reward.PrizeIDs,
		},
	}
	moreJSON, err := json.Marshal(more)
	if err != nil {
		if !isGuaranteed {
			param.rollbackRewardStock()
		}
		return nil, err
	}
	rewardLog := livefansboxrewardlog.LiveFansBoxRewardLog{
		UserID:            param.userID,
		RoomID:            param.RoomID,
		Level:             param.taskInfo.Level,
		FansBoxTaskID:     param.taskInfo.ID,
		FansBoxUserTaskID: param.userTask.ID,
		More:              moreJSON,
	}
	ok, err := rewardLog.Create()
	if err != nil || !ok {
		if !isGuaranteed {
			param.rollbackRewardStock()
		}
		if err != nil {
			return nil, err
		}
		// 对于创建失败（未报错）的情况（一般为唯一索引错误），此时不再发放奖励，不返回奖品信息
		return nil, nil
	}
	// 发放奖品
	prizeDistributors := make([]prize.Distributor, 0, 2)
	for _, sendPrize := range sendPrizes {
		prizeDistributors = append(prizeDistributors, prize.NewUserDistributor(param.userID, sendPrize,
			prize.WithBiz(liveprize.BizFansBoxTask, param.taskInfo.ID)))
	}
	_, err = prize.Send(prizeDistributors)
	if err != nil {
		// 发放失败时记录日志，这种情况需手动补发，消耗的库存不需要回滚
		logger.WithField("reward_log_id", rewardLog.ID).Errorf("直播间粉丝团宝箱任务奖励发放失败: %v", err)
		return nil, errors.New("奖励发放失败")
	}
	return sendPrizes, nil
}

// rollbackRewardStock 回滚奖励库存
func (param *drawParam) rollbackRewardStock() {
	_, err := livefansbox.IncrConsumedStock(param.taskInfo.RoomID, param.taskInfo.Level, param.reward.Type, -1)
	if err != nil {
		logger.WithFields(logger.Fields{
			"level": param.taskInfo.Level,
			"type":  param.reward.Type,
		}).Error("回滚粉丝团宝箱奖励消耗库存失败")
		// PASS: 回滚失败仅日志记录，避免影响用户使用
	}
}
