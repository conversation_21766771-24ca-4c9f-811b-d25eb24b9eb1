package chatroom

import (
	"errors"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/livequestion"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	questionListQueueAndJoined = iota // 未回答和正在回答
	questionListFinished              // 已回答
)

type listQuestionParam struct {
	listType int
	page     int64
	pageSize int64

	userID   int64
	room     *room.Room
	openTime time.Time

	containedStatus []int32
}

type listQuestionResp struct {
	Data       []*livequestion.LiveQuestion `json:"data"`
	TotalValue *int64                       `json:"total_value,omitempty"`
	Pagination goutil.Pagination            `json:"pagination,omitempty"`
}

func (param *listQuestionParam) load(c *handler.Context) error {
	var err error
	param.listType, err = c.GetParamInt("type")
	if err != nil {
		return actionerrors.ErrParams
	}
	switch param.listType {
	case questionListQueueAndJoined:
		// 未回答 以及 正在回答 的提问列表，不做数量限制
		param.containedStatus = []int32{livequestion.StatusQueued, livequestion.StatusJoined}
	case questionListFinished:
		param.containedStatus = []int32{livequestion.StatusFinished}
		param.page, param.pageSize, err = c.GetParamPage()
		if err != nil {
			return actionerrors.ErrParams
		}
	default:
		return actionerrors.ErrParams
	}

	roomID, err := c.GetParamInt64("room_id")
	if err != nil || roomID <= 0 {
		return actionerrors.ErrParams
	}
	param.room, err = findOpenRoom(roomID)
	if err != nil {
		return err
	}

	param.userID = c.UserID()
	param.openTime = util.UnixMilliToTime(param.room.Status.OpenTime)
	return nil
}

func findOpenRoom(roomID int64) (*room.Room, error) {
	r, err := room.Find(roomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.IsBan() {
		return nil, actionerrors.ErrBannedRoom
	}
	if !r.IsOpen() {
		return nil, actionerrors.ErrClosedRoomAlt
	}

	return r, nil
}

// ActionListQuestions 本场提问列表
/**
 * @api {get} /api/v2/chatroom/question/list 本场提问列表
 * @apiDescription 通过提问状态类型获取本场提问列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} type 提问列表类型，0: 未回答和正在回答 (不支持分页); 1: 已回答
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 一页显示数目，客户端获取未回答提问数量时实际无数量限制，需要传 1000
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "total_value": 300, // 总价值，只有当 type 为 1，p 也为 1 时返回
 *       "data": [ // 提问列表
 *         {
 *           "question_id": "60bdd76afac460c49cdd7605", // 提问 OID
 *           "room_id": 3192516, // 房间号
 *           "user_id": 0, // 用户 ID
 *           "username": "", // 用户昵称
 *           "iconurl": "", // 用户头像 URL
 *           "question": "", // 提问内容
 *           "price": 30, // 价格
 *           "status": 2, // 提问状态，0：未回答，1：正在回答，2：已回答
 *           "likes": 0, // 点赞数
 *           "liked": true, // 是否点赞
 *           "created_time": 1626950689001, // 创建时间，毫秒级时间戳
 *           "answered_time": 1626950689001, // 回答完成时间，毫秒级时间戳
 *           "transaction_id": 0, // 交易 ID
 *           "titles": [ // 用户信息
 *             // ...
 *           ]
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 50
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionListQuestions(c *handler.Context) (handler.ActionResponse, error) {
	var param listQuestionParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	var resp listQuestionResp
	if param.listType == questionListQueueAndJoined {
		// 获取 未回答 或是 正在回答 问题列表
		resp.Data, err = livequestion.ListLiveQuestionsByPage(param.userID, param.room.RoomID,
			param.openTime, param.containedStatus, nil)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		return resp, nil
	}

	// 仅在 已回答 提问列表的情况下分页
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	count, err := livequestion.Collection().CountDocuments(ctx, bson.M{
		"_room_id":     param.room.OID,
		"status":       bson.M{"$in": param.containedStatus},
		"created_time": bson.M{"$gte": param.openTime},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp.Pagination = goutil.MakePagination(count, param.page, param.pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]*livequestion.LiveQuestion, 0)
		if param.page == 1 {
			resp.TotalValue = goutil.NewInt64(0)
		}
		return resp, nil
	}

	// 获取 已回答 问题列表
	resp.Data, err = livequestion.ListLiveQuestionsByPage(param.userID, param.room.RoomID,
		param.openTime, param.containedStatus, &resp.Pagination)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 只有第一页需要特别计算
	if param.page == 1 {
		// 获取总价值
		value, err := livequestion.FindTotalValue(param.room.RoomID, param.openTime)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		resp.TotalValue = &value
	}

	return resp, nil
}

type likeQuestionParam struct {
	RoomID     int64  `form:"room_id" json:"room_id"`
	QuestionID string `form:"question_id" json:"question_id"`
	Operation  int    `form:"operation" json:"operation"`

	questionID primitive.ObjectID
	question   *livequestion.LiveQuestion
	room       *room.Room
	userID     int64
	openTime   time.Time
}

// ActionLikeQuestion 点赞提问
/**
 * @api {post} /api/v2/chatroom/question/like 点赞提问
 * @apiDescription 给提问点赞，根据传入的 operation 进行点赞或是取消点赞操作
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} question_id 提问 ID
 * @apiParam {number=0,1} operation 点赞或取消点赞（0：取消点赞；1：点赞）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 点赞/取消点赞成功房间内消息
 *   {
 *     "type": "question",
 *     "event": "update",
 *     "room_id": 123456,
 *     "question": {
 *       "question_id": "60bdd76afac460c49cdd7605", // 提问 ID，如果本字段及以下字段不存在，则不更新
 *       "likes": 100 // 更新成功后的点赞数
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionLikeQuestion(c *handler.Context) (handler.ActionResponse, error) {
	var param likeQuestionParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	err = param.findQuestion()
	if err != nil {
		return nil, err
	}

	err = param.updateLike()
	if err != nil {
		return nil, err
	}

	return "success", nil
}

func (param *likeQuestionParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.QuestionID == "" ||
		!livequestion.ValidLikeOperation(param.Operation) {
		return actionerrors.ErrParams
	}
	param.questionID, err = primitive.ObjectIDFromHex(param.QuestionID)
	if err != nil {
		return actionerrors.ErrParams
	}

	param.userID = c.UserID()
	param.room, err = findOpenRoom(param.RoomID)
	if err != nil {
		return err
	}

	param.openTime = util.UnixMilliToTime(param.room.Status.OpenTime)
	return nil
}

func (param *likeQuestionParam) findQuestion() error {
	var err error
	param.question, err = livequestion.FindOne(bson.M{
		"_id":          param.questionID,
		"room_id":      param.RoomID,
		"status":       bson.M{"$lt": livequestion.StatusCanceled},
		"created_time": bson.M{"$gte": param.openTime},
	}, &livequestion.FindOptions{UserID: param.userID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.question == nil {
		return actionerrors.ErrQuestionNotFound
	}

	return nil
}

func (param *likeQuestionParam) shouldUpdateLike() bool {
	return (!param.question.Liked && param.Operation == livequestion.OperationLike) ||
		(param.question.Liked && param.Operation == livequestion.OperationDislike)
}

func (param *likeQuestionParam) updateLike() error {
	if !param.shouldUpdateLike() {
		return nil
	}

	// 点赞锁
	lock := keys.LockUserQuestionUpdateLike2.Format(param.userID, param.QuestionID)
	ok, err := service.Redis.SetNX(lock, 1, 5*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("操作频繁，请稍后再试")
	}
	defer service.Redis.Del(lock)
	err = param.question.UpdateLike(param.userID, param.Operation)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

type appendQuestionLimitParam struct {
	RoomID int64 `form:"room_id" json:"room_id"`

	room   *room.Room
	userID int64
}

// ActionAppendQuestionLimit 追加提问上限
/**
 * @api {post} /api/v2/chatroom/question/append-limit 追加提问上限
 * @apiDescription 追加提问上限，每个直播间可以追加两次，超过上限 200 个则不能再追加
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "追加成功"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 追加成功房间内消息，上限总是从 websocket 获取
 *   {
 *     "type": "question",
 *     "event": "set",
 *     "room_id": 123456,
 *     "question": {
 *       "min_price": 30, // 如果本字段及以下字段不存在，则不更新
 *       "limit": 100 // 更新成功后的提问数量上限
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionAppendQuestionLimit(c *handler.Context) (handler.ActionResponse, error) {
	var param appendQuestionLimitParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	err = param.appendLimit()
	if err != nil {
		return nil, err
	}

	return "追加成功", nil
}

func (param *appendQuestionLimitParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}
	if param.RoomID <= 0 {
		return actionerrors.ErrParams
	}

	param.room, err = findOpenRoom(param.RoomID)
	if err != nil {
		return err
	}
	if param.room.CreatorID != c.UserID() {
		return actionerrors.ErrForbidden
	}

	param.userID = c.UserID()
	return nil
}

func (param *appendQuestionLimitParam) appendLimit() error {
	if param.room.IsMaxQuestionLimit() {
		return actionerrors.NewErrForbidden("追加次数已达上限")
	}

	ok, err := param.room.AppendQuestionLimit(room.QuestionAppendNum)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("追加次数已达上限")
	}

	return nil
}

type askQuestionParam struct {
	RoomID   int64  `form:"room_id" json:"room_id"`
	Question string `form:"question" json:"question"`
	Price    int64  `form:"price" json:"price"`

	c      *handler.Context
	uc     mrpc.UserContext
	room   *room.Room
	userID int64
	user   *liveuser.Simple

	question *livequestion.LiveQuestion
	rpcResp  *userapi.BalanceResp

	isVip       bool
	isRoomAdmin bool
	roomMedal   *livemedal.LiveMedal // 当前房间的粉丝勋章
}

type askQuestionTransaction struct {
	TransactionID int64 `json:"transaction_id"`
	Price         int64 `json:"price"`
}

type askQuestionResp struct {
	Question    *livequestion.LiveQuestion `json:"question"`
	Transaction askQuestionTransaction     `json:"transaction"`
	User        *liveuser.Simple           `json:"user"`
	Balance     askBalanceResp             `json:"balance"`
}

type askBalanceResp struct {
	Balance                int64 `json:"balance"`
	LiveNobleBalance       int64 `json:"live_noble_balance"`
	LiveNobleBalanceStatus int   `json:"live_noble_balance_status"`
}

// ActionQuestionAsk 给直播间提问
/**
 * @api {post} /api/v2/chatroom/question/ask 给直播间提问
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {String} question 提问内容
 * @apiParam {Number} price 提问价格（钻）
 *
 * @apiSuccessExample {json} Success-Response
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "question": {
 *         "room_id": 65261414,
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "question": "一起肥？",
 *         "price": 200,
 *         "status": 0,
 *         "created_time": 1626950689001, // 创建时间，毫秒级时间戳
 *         "updated_time": 1626950689001, // 更新时间，毫秒级时间戳
 *         "transaction_id": 543,
 *         "question_id": "5dfb38bfa7cc3b2814b5898b"
 *       },
 *       "transaction": {
 *         "transaction_id": 543,
 *         "price": 200
 *       },
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "balance": {
 *         "balance": 11479968,
 *         "live_noble_balance": 283548,
 *         "live_noble_balance_status": 1
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *   {
 *     "type": "question",
 *     "event": "ask",
 *     "room_id": 65261414,
 *     "question": {
 *       "room_id": 65261414,
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *       "question": "一起肥？",
 *       "price": 200,
 *       "status": 0,
 *       "created_time": 1576745151945, // 创建时间，毫秒级时间戳
 *       "updated_time": 1576745151945, // 更新时间，毫秒级时间戳
 *       "transaction_id": 543,
 *       "question_id": "5dfb38bfa7cc3b2814b5898b"
 *     },
 *     "user": {
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *       "titles": [{
 *         "type": "staff",
 *         "name": "超管",
 *         "color": "#f45b41"
 *       }, {
 *         "type": "level",
 *         "level": 9
 *       }, {
 *         "type": "medal",
 *         "name": "独角兽",
 *         "level": 4
 *       }, {
 *         "type": "noble",
 *         "name": "新秀",
 *         "level": 2
 *       }, {
 *         "type": "avatar_frame",
 *         "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *       }, {
 *         "type": "identity_badge", // 身份铭牌
 *         "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *         "appearance_id": 10001 // 图标 ID
 *       }]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 500150001
 * @apiError (400) {String} info 提问价格过低
 *
 * @apiError (400) {Number} code 500150002
 * @apiError (400) {String} info 当前提问数量已满
 *
 * @apiError (400) {Number} code 500150012
 * @apiError (400) {String} info 提问内容含有违规信息
 *
 * @apiError (400) {Number} code 500150012
 * @apiError (400) {String} info 提问内容含有敏感信息
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionQuestionAsk(c *handler.Context) (handler.ActionResponse, error) {
	param := askQuestionParam{c: c, uc: c.UserContext()}
	err := param.load()
	if err != nil {
		return nil, err
	}
	limit := utils.SpeakLimit{
		User:        param.user,
		Room:        param.room,
		IsRoomAdmin: param.isRoomAdmin,
		RoomMedal:   param.roomMedal,
	}
	err = limit.Check()
	if err != nil {
		return nil, err
	}
	err = param.checkText()
	if err != nil {
		return nil, err
	}

	err = param.createOrder()
	if err != nil {
		return nil, err
	}

	err = param.ask()
	if err != nil {
		return nil, err
	}

	err = param.notify()
	if err != nil {
		return nil, err
	}

	return &askQuestionResp{
		Question: param.question,
		Transaction: askQuestionTransaction{
			TransactionID: param.rpcResp.TransactionID,
			Price:         param.rpcResp.Price,
		},
		User: param.user,
		Balance: askBalanceResp{
			Balance:                param.rpcResp.Balance,
			LiveNobleBalance:       param.rpcResp.LiveNobleBalance,
			LiveNobleBalanceStatus: util.BoolToInt(param.isVip),
		},
	}, nil
}

func (param *askQuestionParam) load() error {
	err := param.c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}

	param.Question = strings.TrimSpace(param.Question)
	if param.Question == "" || param.RoomID == 0 {
		return actionerrors.ErrParams
	}

	if utf8.RuneCountInString(param.Question) > livequestion.MaxContentLength {
		return actionerrors.ErrParamsMsg("提问内容过长")
	}

	param.room, err = findOpenRoom(param.RoomID)
	if err != nil {
		return err
	}
	if param.room.Limit != nil {
		return actionerrors.ErrLimitedRoom
	}

	// 被主播拉黑，无法提问
	blocked, err := blocklist.IsBlocked(param.room.CreatorID, param.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
	}

	param.userID = param.c.UserID()
	if param.room.CreatorID == param.userID {
		return actionerrors.ErrParamsMsg("无法向自己提问")
	}
	minPrice := param.room.Question.CurrentMinPrice()
	if param.Price <= 0 || (minPrice > 0 && param.Price < int64(minPrice)) {
		return actionerrors.ErrQuestionPriceTooLow
	}

	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return actionerrors.ErrCannotFindUser
	}
	param.isVip = param.user.VipInfo != nil && param.user.VipInfo.IsVip()

	muted, err := livemembers.IsMute(param.userID, param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if muted.RoomMute || muted.GlobalMute {
		return actionerrors.ErrBannedUser
	}

	banned, err := userstatus.IsBanned(param.userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if banned {
		return actionerrors.ErrBannedUser
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	count, err := livequestion.Collection().CountDocuments(ctx, bson.M{
		"room_id":      param.RoomID,
		"status":       bson.M{"$in": []int32{livequestion.StatusQueued, livequestion.StatusJoined}},
		"created_time": bson.M{"$gte": util.UnixMilliToTime(param.room.Status.OpenTime)},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if count >= param.room.Question.Limit {
		return actionerrors.ErrQuestionLimitReached
	}

	lock := keys.LockRoomQuestion2.Format(param.RoomID, param.userID)
	ok, err := service.Redis.SetNX(lock, 1, 2*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("您提问太频繁啦~")
	}

	param.roomMedal, err = livemedal.FindOwnedMedal(param.userID, param.RoomID, livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.isRoomAdmin, err = livemembers.IsRoomAdmin(param.room.OID, param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func (param *askQuestionParam) checkText() error {
	uuid, err := makeOSUUID(param.c.Equip().OS)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	input := gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			UserID:    param.userID,
			EquipID:   param.c.EquipID(),
			IP:        param.c.ClientIP(),
			API:       param.c.Request().URL.Path,
			UserAgent: param.c.UserAgent(),
			Referer:   param.c.Request().Referer(),
			Content:   param.Question,
		},
		RoomID:        param.RoomID,
		RoomCatalogID: param.room.CatalogID,
		RoomCreatorID: param.room.CreatorID,
		MsgID:         "0" + uuid[1:], // WORKAROUND: 0 开头表示提问
		UserIsAdmin:   param.isRoomAdmin,
		UserHasMedal:  param.roomMedal != nil,
		AllowAD:       param.isRoomAdmin,
		Scene:         gaia.IMSceneQuestion,
	}
	result, err := userapi.CheckTextIM(param.c.UserContext(), input)
	if err != nil {
		return err
	}
	if result.NotPass || result.HasLabelEvil {
		return actionerrors.ErrQuestionSensitive
	}

	return nil
}

func (param *askQuestionParam) createOrder() error {
	var err error
	param.rpcResp, err = userapi.Ask(param.userID, param.room.CreatorID, param.Price,
		param.isVip, param.room.Status.OpenLogID, param.uc)
	if err != nil {
		return err
	}

	return nil
}

func (param *askQuestionParam) ask() error {
	now := goutil.TimeNow()
	nowMsec := goutil.NewTimeUnixMilli(now)
	param.question = &livequestion.LiveQuestion{
		Helper: livequestion.Helper{
			RoomOID:         param.room.OID,
			RoomID:          param.RoomID,
			UserID:          param.userID,
			Username:        param.user.Username,
			IconURL:         param.user.IconURL,
			Question:        param.Question,
			Price:           param.Price,
			TransactionID:   param.rpcResp.TransactionID,
			CreatedTime:     now,
			CreatedTimeMsec: nowMsec,
			UpdatedTime:     now,
			UpdatedTimeMsec: nowMsec,
		},
	}
	// TODO: 提问不需要存贵族信息，后续移除
	if vip := param.user.VipInfo; vip != nil {
		if vip.LiveHighness != nil {
			param.question.VipType = vip.LiveHighness.Type
			param.question.NobleLevel = vip.LiveHighness.Level
		} else if vip.LiveNoble != nil {
			param.question.VipType = vip.LiveNoble.Type
			param.question.NobleLevel = vip.LiveNoble.Level
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	insertRes, err := livequestion.Collection().InsertOne(ctx, param.question)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	var ok bool
	param.question.OID, ok = insertRes.InsertedID.(primitive.ObjectID)
	if !ok {
		return actionerrors.NewErrServerInternal(errors.New("提问 ID 解析错误"), logger.Fields{
			"question": param.question.Question,
			"user_id":  param.question.UserID,
			"room_id":  param.question.RoomID,
		})
	}

	return nil
}

func (param *askQuestionParam) notify() error {
	err := userapi.Broadcast(param.RoomID, map[string]interface{}{
		"type":     liveim.TypeQuestion,
		"event":    liveim.EventAsk,
		"room_id":  param.RoomID,
		"question": param.question,
		"user":     param.user,
	})
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil
}

const (
	questionAnswerJoin   = "join"
	questionAnswerFinish = "finish"
)

type answerQuestionParam struct {
	RoomID     int64  `form:"room_id" json:"room_id"`
	QuestionID string `form:"question_id" json:"question_id"`
	Type       string `form:"type" json:"type"`

	c  *handler.Context
	uc mrpc.UserContext

	questionOID    primitive.ObjectID
	question       *livequestion.LiveQuestion
	room           *room.Room
	userID         int64
	user           *liveuser.Simple
	broadcastElems []*userapi.BroadcastElem
}

type answerQuestionResp struct {
	Ok     int   `json:"ok"`
	Status int32 `json:"status"`
}

// ActionAnswerQuestion 主播直播间对提问进行操作
/**
 * @api {post} /api/v2/chatroom/question/answer 主播直播间对提问进行操作
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {String} question_id 提问 ID
 * @apiParam {string="join","finish"} type 操作类型，join: 回答，finish: 结束回答
 *
 * @apiSuccessExample {json} Success-Response
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 1,
 *         "status": 2 // status 表示问题状态
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 开始回答提问房间内消息
 *     {
 *       "type": "question",
 *       "event": "answer",
 *       "answer_type": "join", // 当有新回答消息时，客户端未回答列表中需要移除当前回答中的问题，更新成最新的
 *       "room_id": 10659544,
 *       "user": { // 主播信息
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/12/dab7a79ca5dbf19d173aac1d719b7fc1131750.jpg"
 *       },
 *       "question": {
 *         "question_id": "5ef2fd3e2beff02c36bbfa79",
 *         "room_id": 10659544,
 *         "user_id": 12,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/12/dab7a79ca5dbf19d173aac1d719b7fc1131750.jpg"",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#F45B41"
 *           },
 *           {
 *             "type": "medal",
 *             "name": "1",
 *             "level": 1
 *           },
 *           {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }
 *         ],
 *         "question": "针对主播的一个提问",
 *         "price": 100,
 *         "status": 1,
 *         "created_time": 1677498370415,
 *         "updated_time": 1677498370415,
 *         "likes": 1,
 *         "liked": false
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 结束回答房间内消息
 *     {
 *       "type": "question",
 *       "event": "answer",
 *       "answer_type": "finish",
 *       "room_id": 10659544,
 *       "user": { // 主播信息
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/12/dab7a79ca5dbf19d173aac1d719b7fc1131750.jpg"
 *       },
 *       "question": {
 *         "question_id": "5ef2fd3e2beff02c36bbfa79",
 *         "room_id": 10659544,
 *         "user_id": 12,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/12/dab7a79ca5dbf19d173aac1d719b7fc1131750.jpg"",
 *         "question": "针对主播的一个提问",
 *         "price": 100,
 *         "status": 2,
 *         "created_time": 1677498370415,
 *         "updated_time": 1677498370415,
 *         "answered_time": 1626945939067, // 主播回答完成提问时，才会下发此字段用于排序
 *         "likes": 1,
 *         "liked": false
 *       },
 *       "current_revenue": 612 // 当前用户在当前直播间本场榜的贡献值，贡献值发生变化后才有该值（主播回答完成提问）
 *     }
 *
 */
func ActionAnswerQuestion(c *handler.Context) (handler.ActionResponse, error) {
	param := answerQuestionParam{c: c, uc: c.UserContext()}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	err = param.check()
	if err != nil {
		return nil, err
	}

	err = param.operate()
	if err != nil {
		return nil, err
	}

	goutil.Go(func() {
		// 在完成提问回答时更新榜单、亲密度等信息
		// NOTICE: 提问不加 PK 榜
		if param.question.Status == livequestion.StatusFinished {
			param.addRevenueRank()
			param.addMedalPoint()
			param.addUserContribution()

			param.addActivity()
			param.addLiveShow()
			param.addLiveSpend()
			param.addRoomPaidUser()
		}
		param.notify()
	})

	return &answerQuestionResp{
		Ok:     1,
		Status: param.question.Status,
	}, nil
}

func (param *answerQuestionParam) check() error {
	var err error
	switch param.Type {
	case questionAnswerJoin, questionAnswerFinish:
	default:
		return actionerrors.ErrParams
	}

	if param.QuestionID == "" || param.RoomID == 0 {
		return actionerrors.ErrParams
	}
	param.questionOID, err = primitive.ObjectIDFromHex(param.QuestionID)
	if err != nil {
		return actionerrors.ErrParams
	}

	param.room, err = findOpenRoom(param.RoomID)
	if err != nil {
		return err
	}

	param.userID = param.c.UserID()
	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return actionerrors.ErrUserNotFound
	}
	if param.room.CreatorID != param.userID {
		return actionerrors.ErrForbidden
	}

	return param.checkQuestion()
}

func (param *answerQuestionParam) checkQuestion() (err error) {
	param.question, err = livequestion.FindOne(bson.M{
		"_id":     param.questionOID,
		"room_id": param.RoomID,
		"status":  bson.M{"$lt": livequestion.StatusFinished},
	},
		&livequestion.FindOptions{
			FindAskUserTitles: true,
			UserID:            param.userID,
		},
	)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.question == nil {
		return actionerrors.ErrNotFound("无法找到该提问或该提问已结束")
	}
	switch param.Type {
	case questionAnswerJoin:
		if param.question.Status != livequestion.StatusQueued {
			return actionerrors.NewErrForbidden("无法回答该提问")
		}
		// 检查是否有回答中的提问
		joinedQuestion, err := livequestion.FindOne(bson.M{
			"room_id": param.RoomID,
			"status":  livequestion.StatusJoined,
		})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if joinedQuestion != nil {
			return actionerrors.ErrParamsMsg("当前正在回答提问中")
		}
	case questionAnswerFinish:
		if param.question.Status != livequestion.StatusJoined {
			return actionerrors.NewErrForbidden("无法结束该提问")
		}
	default:
		panic("提问回答类型错误")
	}
	return nil
}

func (param *answerQuestionParam) addMedalPoint() {
	if param.room.Medal == nil {
		return
	}
	uv, err := vip.UserActivatedVip(param.question.UserID, false, nil)
	if err != nil {
		logger.Error(err)
		return
	}
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    param.room.OID,
		RoomID:     param.room.RoomID,
		CreatorID:  param.room.CreatorID,
		UserID:     param.question.UserID,
		UV:         uv,
		MedalName:  param.room.Medal.Name,
		Type:       livemedal.TypeQuestionAddMedalPoint,
		Source:     livemedal.ChangeSourceQuestion,
		PointAdd:   param.question.Price,
		Scene:      livemedalpointlog.SceneTypeQuestion,
		IsRoomOpen: param.room.IsOpen(),
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		CreatorUsername:  param.room.CreatorUsername,
		UserID:           param.question.UserID,
		RoomID:           param.room.RoomID,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func (param *answerQuestionParam) addUserContribution() {
	userID := param.question.UserID
	pointAdd := param.question.Price * 10 // 1 钻石 = 10 经验
	uv, err := vip.UserActivatedVip(userID, false, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if uv != nil && uv.Info != nil {
		pointAdd = uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(userID, param.RoomID, param.room.CreatorUsername, userstatus.FromNormal, uv)
	err = addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

// addRevenueRank 添加榜单收益
func (param *answerQuestionParam) addRevenueRank() {
	score := param.question.Price
	if score == 0 {
		return
	}

	err := param.room.ReceiveQuestion(score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = roomsrank.AddRevenue(param.question.RoomID, param.question.UserID, score,
		goutil.IntToBool(param.room.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.room.CreatorID, param.room.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, param.room)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddQuestionRevenue(param.question.UserID, param.question.RoomOID, param.question.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *answerQuestionParam) addActivity() {
	r := rank.
		NewSyncParam(param.room.RoomID, param.question.UserID, param.room.CreatorID).
		SetGuildID(param.room.GuildID).
		SetActivityCatalogID(param.room.ActivityCatalogID).
		SetRevenueGoods(rank.AddRevenueTypeQuestion, param.question.Price)
	r.AddRankPoint()
	r.SendLiveActivity(param.uc, userapi.SyncTypeQuestion)
}

func (param *answerQuestionParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.room.RoomID, param.question.UserID, param.room.CreatorID).
		SetPoint(param.question.Price).
		Sync()
}

func (param *answerQuestionParam) addLiveSpend() {
	utils.SendLiveSpend(param.question.UserID, int64(param.question.Price), param.RoomID)
}

func (param *answerQuestionParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.question.UserID, param.room.Status.OpenTime)
}

func (param *answerQuestionParam) operate() error {
	switch param.Type {
	case questionAnswerJoin:
		param.question.Status = livequestion.StatusJoined
	case questionAnswerFinish:
		if param.question.TransactionID != 0 {
			_, err := userapi.ConfirmAsk(param.question.TransactionID,
				param.room.CreatorID, true, param.uc)
			if err != nil {
				return err
			}
		}
		param.question.Status = livequestion.StatusFinished
	default:
		panic("提问回答类型错误")
	}

	now := goutil.TimeNow()
	setModel := bson.M{
		"status":       param.question.Status,
		"updated_time": now,
	}
	param.question.UpdatedTimeMsec = goutil.NewTimeUnixMilli(now)
	if param.question.Status == livequestion.StatusFinished {
		param.question.AnsweredTimeMsec = goutil.NewTimeUnixMilli(now)
		setModel["answered_time"] = now
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livequestion.Collection().UpdateOne(ctx,
		bson.M{"_id": param.question.OID},
		bson.M{"$set": setModel})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type answerQuestionAttachPayload struct {
	Type           string                     `json:"type"`
	Event          string                     `json:"event"`
	AnswerType     string                     `json:"answer_type"`
	RoomID         int64                      `json:"room_id"`
	User           *liveuser.Simple           `json:"user"`
	Question       *livequestion.LiveQuestion `json:"question"`
	CurrentRevenue int64                      `json:"current_revenue,omitempty"`
	Status         int32                      `json:"status"` // WORKAROUND: 兼容 iOS < 4.7.8 安卓 < 5.6.6 的老版本
}

func (param *answerQuestionParam) notify() {
	// 发送广播通知
	payload := answerQuestionAttachPayload{
		Type:       liveim.TypeQuestion,
		Event:      liveim.EventAnswer,
		AnswerType: param.Type,
		RoomID:     param.RoomID,
		User: &liveuser.Simple{
			UID:      param.user.UserID(),
			Username: param.user.Username,
			IconURL:  param.user.IconURL,
		},
		Question: param.question,
	}
	// TODO: 待上线后放出注释（最晚上线版本：1.4.1）
	// if goutil.IsProdEnv() {
	// WORKAROUND: 兼容线上 iOS < 4.7.8 安卓 < 5.6.6 的老版本
	payload.Status = payload.Question.Status
	// }

	if param.question.Status == livequestion.StatusFinished {
		key := roomsrank.Key(param.RoomID, roomsrank.RankTypeCurrent, goutil.TimeNow())
		point, err := service.Redis.ZScore(key, strconv.FormatInt(param.question.UserID, 10)).Result()
		if err != nil && !serviceredis.IsRedisNil(err) {
			logger.Error(err)
			// PASS
		}
		payload.CurrentRevenue = int64(point)
	}
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  param.RoomID,
		Payload: payload,
	})

	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
