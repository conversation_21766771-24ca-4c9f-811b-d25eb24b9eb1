package multiconnect

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestNewUserRankParam(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
	_, err := newUserRankParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  1,
		"group_id": 0,
	})
	_, err = newUserRankParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  1,
		"group_id": 2,
	})
	_, err = newUserRankParam(c)
	assert.NoError(err)
}

func TestUserRankParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(13131313132)
	creatorID := int64(100)
	groupID := int64(13131313133)

	// 清理直播间测试数据
	cleanTestRoom := func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		collection := service.MongoDB.Collection(room.CollectionName)
		_, err := collection.DeleteOne(ctx, bson.M{"room_id": roomID})
		require.NoError(err)
	}

	// 创建直播间测试数据
	insertTestRoom := func() {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		collection := service.MongoDB.Collection(room.CollectionName)
		rh := room.Helper{
			Status:    room.Status{Open: 1, OpenTime: goutil.TimeNow().Unix()},
			RoomID:    roomID,
			Name:      "",
			NameClean: "clean-test",
			Type:      "live",
			CreatorID: creatorID,
		}
		_, err := collection.UpdateOne(
			ctx,
			bson.M{"room_id": roomID},
			bson.M{"$set": rh},
			options.Update().SetUpsert(true),
		)
		require.NoError(err)
	}

	// 清理连线组测试数据
	cleanTestGroup := func() {
		err := livemulticonnect.DB().Delete(livemulticonnect.Group{}, "id = ?", groupID).Error
		require.NoError(err)
		err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", groupID).Error
		require.NoError(err)
	}

	// 创建连线组测试数据
	insertTestGroup := func() {
		err := livemulticonnect.DB().Create(&livemulticonnect.Group{
			ID: groupID,
		}).Error
		require.NoError(err)
		err = livemulticonnect.DB().Create(&livemulticonnect.GroupMember{
			GroupID: groupID,
			RoomID:  roomID,
		}).Error
		require.NoError(err)
	}

	// 设置登录上下文
	setTestLoginContext := func(userID int64) *handler.Context {
		userFunc := user.GetUserFunc(func(c *gin.Context) (*user.User, error) {
			return &user.User{
				IUser: user.IUser{
					ID: userID,
				},
			}, nil
		})
		c := handler.NewTestContext(http.MethodGet, "", true, nil)
		c.C.Set("user", userFunc)
		return c
	}

	t.Run("房间不存在", func(t *testing.T) {
		cleanTestRoom()
		param := &userRankParam{RoomID: roomID, GroupID: groupID, c: setTestLoginContext(creatorID)}
		err := param.check()
		assert.Equal(actionerrors.ErrCannotFindRoom, err)
	})

	t.Run("非主播无权限", func(t *testing.T) {
		insertTestRoom()
		param := &userRankParam{RoomID: roomID, GroupID: groupID, c: setTestLoginContext(1111)}
		err := param.check()
		assert.Equal(actionerrors.ErrForbidden, err)
	})

	t.Run("连线组不存在", func(t *testing.T) {
		insertTestRoom()
		cleanTestGroup()
		param := &userRankParam{RoomID: roomID, GroupID: groupID, c: setTestLoginContext(creatorID)}
		err := param.check()
		assert.Equal(actionerrors.ErrMultiConnectGroupNotFound, err)
	})

	t.Run("榜单开关未开启", func(t *testing.T) {
		cleanTestGroup()
		insertTestRoom()
		insertTestGroup()
		err := livemulticonnect.SwitchUserRankStatus(groupID, false)
		require.NoError(err)
		param := &userRankParam{RoomID: roomID, GroupID: groupID, c: setTestLoginContext(creatorID)}
		err = param.check()
		assert.Equal("暂无查看权限", err.Error())
	})

	t.Run("正常", func(t *testing.T) {
		cleanTestGroup()
		insertTestRoom()
		insertTestGroup()
		err := livemulticonnect.SwitchUserRankStatus(groupID, true)
		require.NoError(err)
		param := &userRankParam{RoomID: roomID, GroupID: groupID, c: setTestLoginContext(creatorID)}
		err = param.check()
		assert.NoError(err)
	})
}

func TestUserRankParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	groupID := int64(999)
	roomID := int64(888)
	rankCacheKey := keys.KeyMultiConnectGroupUserRankCache1.Format(groupID)

	// 基础设置
	param := &userRankParam{
		RoomID:  roomID,
		GroupID: groupID,
		roomMember: &livemulticonnect.GroupMember{
			RoomID: roomID,
			Index:  1,
		},
		groupMemberMap: map[int64]*livemulticonnect.GroupMember{
			roomID: {RoomID: roomID, Index: 1},
		},
		roomMap: map[int64]*room.Room{
			roomID: {
				Helper: room.Helper{
					RoomID:    roomID,
					CreatorID: 1001,
				},
			},
		},
		group: &livemulticonnect.Group{
			ID: groupID,
		},
	}

	t.Run("查询榜单报错", func(t *testing.T) {
		err := service.Redis.Set(rankCacheKey, "invalid json", 10*time.Second).Err()
		require.NoError(err)
		resp, err := param.resp()
		require.Error(err)
		require.Nil(resp)
	})

	t.Run("榜单为空", func(t *testing.T) {
		userRank := make([]*livemulticonnect.RankUserInfo, 0)
		rankData, err := json.Marshal(userRank)
		require.NoError(err)
		err = service.Redis.Set(rankCacheKey, rankData, 10*time.Second).Err()
		require.NoError(err)
		resp, err := param.resp()
		require.NoError(err)
		assert.NotNil(resp)
		assert.Empty(resp.Data)
	})

	t.Run("榜单有数据且用户在当前房间", func(t *testing.T) {
		userRank := []*livemulticonnect.RankUserInfo{
			{
				UserID:   1,
				Username: "user1",
				IconURL:  "icon1",
				RoomIDs:  []int64{roomID}, // 在当前房间
				Score:    100,
			},
		}
		rankData, err := json.Marshal(userRank)
		require.NoError(err)
		err = service.Redis.Set(rankCacheKey, rankData, 10*time.Second).Err()
		require.NoError(err)

		resp, err := param.resp()
		require.NoError(err)
		require.Len(resp.Data, 1)
		assert.Equal(int64(1), resp.Data[0].Rank)
		assert.Equal(1, resp.Data[0].MultiConnect.Member.Index)
		assert.Equal(roomID, resp.Data[0].MultiConnect.Member.Room.RoomID)
	})
}

func TestUserRankParam_selectBestMember(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	currentRoomID := int64(100)
	otherRoomID1 := int64(101)
	otherRoomID2 := int64(102)

	param := &userRankParam{
		roomMember: &livemulticonnect.GroupMember{
			RoomID: currentRoomID,
			Index:  2,
		},
		groupMemberMap: map[int64]*livemulticonnect.GroupMember{
			currentRoomID: {RoomID: currentRoomID, Index: 2},
			otherRoomID1:  {RoomID: otherRoomID1, Index: 1},
			otherRoomID2:  {RoomID: otherRoomID2, Index: 3},
		},
		roomMap: map[int64]*room.Room{
			currentRoomID: {
				Helper: room.Helper{
					RoomID:    currentRoomID,
					CreatorID: 1001,
				},
			},
			otherRoomID1: {
				Helper: room.Helper{
					RoomID:    otherRoomID1,
					CreatorID: 1002,
				},
			},
			otherRoomID2: {
				Helper: room.Helper{
					RoomID:    otherRoomID2,
					CreatorID: 1003,
				},
			},
		},
	}

	t.Run("用户在当前房间", func(t *testing.T) {
		member := param.selectBestMember([]int64{currentRoomID, otherRoomID1})
		require.NotNil(member)
		assert.Equal(2, member.Index)
		assert.Equal(currentRoomID, member.Room.RoomID)
	})

	t.Run("用户不在当前房间时选择最小麦序", func(t *testing.T) {
		member := param.selectBestMember([]int64{otherRoomID1, otherRoomID2})
		assert.NotNil(member)
		assert.Equal(1, member.Index)
		assert.Equal(otherRoomID1, member.Room.RoomID)
	})

	t.Run("房间 ID 不存在", func(t *testing.T) {
		member := param.selectBestMember([]int64{999})
		assert.Nil(member)
	})
}

func TestUserRankParam_buildMemberInfo(t *testing.T) {
	assert := assert.New(t)

	roomID := int64(100)
	param := &userRankParam{
		roomMap: map[int64]*room.Room{
			roomID: {
				Helper: room.Helper{
					RoomID:    roomID,
					CreatorID: 1001,
				},
			},
		},
	}

	t.Run("房间存在", func(t *testing.T) {
		member := param.buildMemberInfo(roomID, 1)
		assert.NotNil(member)
		assert.Equal(1, member.Index)
		assert.Equal(roomID, member.Room.RoomID)
		assert.Equal(int64(1001), member.Room.CreatorID)
	})

	t.Run("房间不存在", func(t *testing.T) {
		member := param.buildMemberInfo(999, 1)
		assert.Nil(member)
	})
}

func TestNewRankSwitchParam(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, handler.M{})
	_, err := newRankSwitchParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  1,
		"group_id": 0,
	})
	_, err = newRankSwitchParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "", true, handler.M{
		"room_id":  1,
		"group_id": 2,
	})
	_, err = newRankSwitchParam(c)
	assert.NoError(err)
}

func TestRankSwitchParam_check(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoomID := int64(18113499) // NewTestContext 登录状态下对应的直播间 ID
	testGroupID := int64(111)

	err := livemulticonnect.DB().Delete(livemulticonnect.Group{}, "id = ?", testGroupID).Error
	require.NoError(err)
	err = livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}, "group_id = ?", testGroupID).Error
	require.NoError(err)

	// group 不存在
	param := &rankSwitchParam{
		RoomID:  testRoomID,
		GroupID: testGroupID,
		c:       handler.NewTestContext(http.MethodPost, "", true, handler.M{}),
	}
	err = param.check()
	assert.Equal(actionerrors.ErrMultiConnectGroupNotFound, err)

	err = livemulticonnect.DB().Create(&livemulticonnect.Group{
		ID: testGroupID,
	}).Error
	require.NoError(err)
	err = livemulticonnect.DB().Create(&livemulticonnect.GroupMember{
		GroupID: testGroupID,
		RoomID:  testRoomID,
	}).Error
	require.NoError(err)

	// 非主麦没有权限
	param.RoomID = testRoomID
	err = param.check()
	assert.Equal(actionerrors.NewErrForbidden("暂时没有该操作的权限"), err)

	err = livemulticonnect.DB().Model(&livemulticonnect.GroupMember{}).
		Where("group_id = ? AND room_id = ?", testGroupID, testRoomID).
		Update("role", livemulticonnect.MemberRoleOwner).Error
	require.NoError(err)

	// 主麦不在白名单没有权限
	param.RoomID = testRoomID
	err = param.check()
	assert.Equal(actionerrors.NewErrForbidden("暂时没有该操作的权限"), err)

	// 更新多人连线配置，将房间加入到白名单
	cacheKey := keys.KeyParams1.Format(params.KeyMultiConnect)
	require.NoError(service.LRURedis.Del(cacheKey).Err())
	mcCfg := params.DefaultMultiConnect()
	mcCfg.BetaRoomIDs = []int64{testRoomID}
	mcCfgData, _ := json.Marshal(mcCfg)
	err = service.LRURedis.Set(cacheKey, mcCfgData, time.Second*2).Err()
	require.NoError(err)

	// 房间有权限
	err = param.check()
	require.NoError(err)
}

func TestRankSwitchParam_switchRankStatusAndBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cancel := mrpc.SetMock("im://broadcast/many", func(input interface{}) (output interface{}, err error) {
		count++
		return "success", nil
	})
	defer cancel()

	testGroupID := int64(200)
	testRoomID := int64(201)
	param := &rankSwitchParam{
		RoomID:  testRoomID,
		GroupID: testGroupID,

		groupMembers: []*livemulticonnect.GroupMember{
			{RoomID: testRoomID},
			{RoomID: 1},
			{RoomID: 2},
			{RoomID: 3},
		},
	}

	key := keys.KeyMultiConnectGroupUserRankEnable1.Format(testGroupID)
	require.NoError(service.Redis.Del(key).Err())

	// 榜单未开启的状态下关闭榜单
	err := param.switchRankStatusAndBroadcast(false)
	assert.NoError(err)
	assert.Equal(0, count)

	// 开启榜单
	err = param.switchRankStatusAndBroadcast(true)
	assert.NoError(err)
	val, err := service.Redis.Get(key).Int()
	assert.NoError(err)
	assert.NotEmpty(val)
	assert.Equal(1, count)

	// 重复开启榜单
	err = param.switchRankStatusAndBroadcast(true)
	assert.NoError(err)
	val, err = service.Redis.Get(key).Int()
	assert.NoError(err)
	assert.NotEmpty(val)
	assert.Equal(1, count)

	// 关闭榜单
	err = param.switchRankStatusAndBroadcast(false)
	assert.NoError(err)
	val, err = service.Redis.Get(key).Int()
	assert.True(serviceredis.IsRedisNil(err))
	assert.Empty(val)
	assert.Equal(2, count)
}
