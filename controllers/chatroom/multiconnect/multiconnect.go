package multiconnect

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler .
func Handler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "multi-connect",
		Actions: map[string]*handler.ActionV2{
			"application/request": handler.NewActionV2(handler.POST, ActionMultiConnectApplicationRequest, handler.ActionOption{LoginRequired: true}),
			"application/cancel":  handler.NewActionV2(handler.POST, ActionMultiConnectApplicationCancel, handler.ActionOption{LoginRequired: true}),
			"application/accept":  handler.NewActionV2(handler.POST, ActionMultiConnectApplicationAccept, handler.ActionOption{LoginRequired: true}),
			"application/refuse":  handler.NewActionV2(handler.POST, ActionMultiConnectApplicationRefuse, handler.ActionOption{LoginRequired: true}),

			"invitation/request": handler.NewActionV2(handler.POST, ActionMultiConnectInvitationRequest, handler.ActionOption{LoginRequired: true}),
			"invitation/cancel":  handler.NewActionV2(handler.POST, ActionMultiConnectInvitationCancel, handler.ActionOption{LoginRequired: true}),
			"invitation/accept":  handler.NewActionV2(handler.POST, ActionMultiConnectInvitationAccept, handler.ActionOption{LoginRequired: true}),
			"invitation/refuse":  handler.NewActionV2(handler.POST, ActionMultiConnectInvitationRefuse, handler.ActionOption{LoginRequired: true}),

			"kickout": handler.NewActionV2(handler.POST, ActionMultiConnectKickout, handler.ActionOption{LoginRequired: true}),
			"quit":    handler.NewActionV2(handler.POST, ActionMultiConnectQuit, handler.ActionOption{LoginRequired: true}),

			"mute":   handler.NewActionV2(handler.POST, ActionMultiConnectMute, handler.ActionOption{LoginRequired: true}),
			"unmute": handler.NewActionV2(handler.POST, ActionMultiConnectUnmute, handler.ActionOption{LoginRequired: true}),

			"mic-off": handler.NewActionV2(handler.POST, ActionMultiConnectMicOff, handler.ActionOption{LoginRequired: true}),
			"mic-on":  handler.NewActionV2(handler.POST, ActionMultiConnectMicOn, handler.ActionOption{LoginRequired: true}),

			"settings/get": handler.NewActionV2(handler.GET, ActionMultiConnectSettingsGet, handler.ActionOption{LoginRequired: true}),
			"settings/set": handler.NewActionV2(handler.POST, ActionMultiConnectSettingsSet, handler.ActionOption{LoginRequired: true}),

			"block-list/get":    handler.NewActionV2(handler.GET, ActionMultiConnectBlockList, handler.ActionOption{LoginRequired: true}),
			"block-list/remove": handler.NewActionV2(handler.POST, ActionMultiConnectBlockListRemove, handler.ActionOption{LoginRequired: true}),
			"block-list/add":    handler.NewActionV2(handler.POST, ActionMultiConnectBlockListAdd, handler.ActionOption{LoginRequired: true}),

			"score/clear": handler.NewActionV2(handler.POST, ActionMultiConnectScoreClear, handler.ActionOption{LoginRequired: true}),

			"record-list":    handler.NewActionV2(handler.GET, ActionMultiConnectRecordList, handler.ActionOption{LoginRequired: true}),
			"recommend-list": handler.NewActionV2(handler.GET, ActionMultiConnectRecommendList, handler.ActionOption{LoginRequired: true}),
			"recent-list":    handler.NewActionV2(handler.GET, ActionMultiConnectRecentList, handler.ActionOption{LoginRequired: true}),

			"cross-msg/on":  handler.NewActionV2(handler.POST, ActionMultiConnectCrossMsgOn, handler.ActionOption{LoginRequired: true}),
			"cross-msg/off": handler.NewActionV2(handler.POST, ActionMultiConnectCrossMsgOff, handler.ActionOption{LoginRequired: true}),

			"user/rank":     handler.NewActionV2(handler.GET, ActionMultiConnectUserRank, handler.ActionOption{LoginRequired: true}),
			"user/rank/on":  handler.NewActionV2(handler.POST, ActionMultiConnectUserRankOn, handler.ActionOption{LoginRequired: true}),
			"user/rank/off": handler.NewActionV2(handler.POST, ActionMultiConnectUserRankOff, handler.ActionOption{LoginRequired: true}),
		},
	}
}
