package multiconnect

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionMultiConnectCrossMsgOn 开启主播连线消息互通
/**
 * @api {post} /api/v2/chatroom/multi-connect/cross-msg/on 开启主播连线消息互通
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 开启主播连线消息互通
 *   {
 *     "type": "multi_connect",
 *     "event": "cross_msg_on",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111 // 连线组 ID
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMultiConnectCrossMsgOn(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}

// ActionMultiConnectCrossMsgOff 关闭主播连线消息互通
/**
 * @api {post} /api/v2/chatroom/multi-connect/cross-msg/off 关闭主播连线消息互通
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 关闭主播连线消息互通
 *   {
 *     "type": "multi_connect",
 *     "event": "cross_msg_off",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111 // 连线组 ID
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMultiConnectCrossMsgOff(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
