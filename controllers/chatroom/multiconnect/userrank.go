package multiconnect

import (
	"slices"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type userRankParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GroupID int64 `form:"group_id" json:"group_id"`

	c              *handler.Context
	group          *livemulticonnect.Group
	groupMemberMap map[int64]*livemulticonnect.GroupMember // 所在连线组的成员
	roomMap        map[int64]*room.Room                    // 所在连线组的成员直播间信息
	roomMember     *livemulticonnect.GroupMember           // 当前房间的连线成员信息
}

type userRankResp struct {
	Data []*rankUserInfo `json:"data"`
}

// rankUserInfo 用户信息
type rankUserInfo struct {
	Rank     int64  `json:"rank"`
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`

	Titles []liveuser.Title `json:"titles,omitempty"`

	MultiConnect *multiConnectInfo `json:"multi_connect,omitempty"`
}

// multiConnectInfo 连线组信息
type multiConnectInfo struct {
	Member *multiConnectMember `json:"member,omitempty"` // 连线组成员信息
}

// multiConnectMember 连线组成员
type multiConnectMember struct {
	Index int                   `json:"index"`
	Room  *multiConnectRoomInfo `json:"room"`
}

// RoomInfo 连麦房间信息
type multiConnectRoomInfo struct {
	RoomID    int64 `json:"room_id"`
	CreatorID int64 `json:"creator_id"`
}

func newUserRankParam(c *handler.Context) (*userRankParam, error) {
	param := &userRankParam{
		c: c,
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.GroupID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *userRankParam) check() error {
	r, err := room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	// 只允许主播查看
	if param.c.UserID() == 0 || param.c.UserID() != r.CreatorID {
		return actionerrors.ErrForbidden
	}

	param.group, err = livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return actionerrors.ErrMultiConnectGroupNotFound
	}
	groupMembers, err := param.group.Members(nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	index := slices.IndexFunc(groupMembers, func(m *livemulticonnect.GroupMember) bool {
		return m.RoomID == param.RoomID
	})
	if index < 0 {
		return actionerrors.ErrMultiConnectGroupMemberModified
	}
	param.roomMember = groupMembers[index]

	param.groupMemberMap = util.ToMap(groupMembers, func(groupMember *livemulticonnect.GroupMember) int64 {
		return groupMember.RoomID
	})

	// 获取主播连线中成员的房间信息
	roomIDs := goutil.SliceMap(groupMembers, func(info *livemulticonnect.GroupMember) int64 {
		return info.RoomID
	})
	rooms, err := room.List(bson.M{"room_id": bson.M{"$in": roomIDs}}, nil, &room.FindOptions{FindCreator: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(roomIDs) != len(rooms) {
		return actionerrors.ErrCannotFindRoom
	}
	param.roomMap = util.ToMap(rooms, func(r *room.Room) int64 {
		return r.RoomID
	})

	enable := livemulticonnect.GetUserRankEnabled(param.GroupID)
	// 判断榜单是否开启
	if enable == livemulticonnect.UserRankDisabled {
		return actionerrors.NewErrForbidden("暂无查看权限")
	}

	return nil
}

func (param *userRankParam) resp() (*userRankResp, error) {
	// 查询榜单信息
	userRank, err := livemulticonnect.GetUserRank(param.group)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	data := make([]*rankUserInfo, 0, len(userRank))
	if len(userRank) == 0 {
		return &userRankResp{Data: data}, nil
	}

	// 构建响应数据
	for i, info := range userRank {
		member := param.selectBestMember(info.RoomIDs)
		// 过滤掉无效连线组成员（因为榜单有 10s 缓存，连线组成员可能此间已经退出连线了）
		if member == nil || member.Room == nil {
			continue
		}
		data = append(data, &rankUserInfo{
			Rank:     int64(i + 1),
			UserID:   info.UserID,
			Username: info.Username,
			IconURL:  info.IconURL,
			Titles:   info.Titles,
			MultiConnect: &multiConnectInfo{
				Member: member,
			},
		})
	}

	return &userRankResp{Data: data}, nil
}

// selectBestMember 选择最佳成员信息
// 优先返回当前主播房间的麦序，如果不在当前直播间则返回所在直播间最小的麦序
func (param *userRankParam) selectBestMember(roomIDs []int64) *multiConnectMember {
	var member *multiConnectMember

	for _, roomID := range roomIDs {
		// 优先选择当前主播房间
		if roomID == param.roomMember.RoomID {
			member = param.buildMemberInfo(roomID, param.roomMember.Index)
			if member != nil {
				return member
			}
		}
		// 选择最小麦序的房间
		if m, ok := param.groupMemberMap[roomID]; ok && (member == nil || m.Index < member.Index) {
			newMember := param.buildMemberInfo(roomID, m.Index)
			if newMember != nil {
				member = newMember
			}
		}
	}

	return member
}

// buildMemberInfo 构建连线组成员信息
func (param *userRankParam) buildMemberInfo(roomID int64, index int) *multiConnectMember {
	member := &multiConnectMember{
		Index: index,
	}

	if r, ok := param.roomMap[roomID]; ok {
		member.Room = &multiConnectRoomInfo{
			RoomID:    r.RoomID,
			CreatorID: r.CreatorID,
		}
		return member
	}

	return nil
}

// ActionMultiConnectUserRank 查询直播间所在连线组的在线用户榜单
/**
 * @api {get} /api/v2/chatroom/multi-connect/user/rank 查询直播间所在连线组的在线用户榜单
 * @apiDescription 获取直播间所在连线组的用户收听排行榜，展示在线用户的前 100 名，按总积分降序和用户等级降序排序，暂只给主播开放。
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "data": [
 *         {
 *           "rank": 1,
 *           "user_id": 12345,
 *           "username": "用户名 12345",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "titles": [
 *             {
 *               "type": "noble",
 *               "name": "练习生",
 *               "level": 1
 *             },
 *             {
 *               "type": "highness",
 *               "name": "上神",
 *               "level": 1
 *             },
 *             {
 *               "type": "avatar_frame",
 *               "icon_url": "https://static-test.maoercdn.com/live/avatarframes/40335.webp"
 *             }
 *           ],
 *           "multi_connect": {
 *             "member": { // 该用户所在直播间连线组的成员信息。如果该用户在多个直播间内，优先返回当前主播，如果不在当前直播间内，则返回麦序最小的主播
 *               "index": 1, // 主播麦序
 *               "room": {
 *                 "room_id": 10659544,
 *                 "creator_id": 3457111
 *               }
 *             }
 *           }
 *         },
 *         {
 *           "rank": 2,
 *           "user_id": 23456,
 *           "username": "用户名 23456",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "titles": [
 *             {
 *               "type": "avatar_frame",
 *               "icon_url": "https://static-test.maoercdn.com/live/avatarframes/40335.webp"
 *             },
 *             {
 *               "type": "badge",
 *               "icon_url": "https://static-test.maoercdn.com/live/badges/50281.webp",
 *               "appearance_id": 50281
 *             },
 *             {
 *               "type": "badge",
 *               "icon_url": "https://static-test.maoercdn.com/live/badges/50310.png",
 *               "appearance_id": 50282
 *             }
 *           ],
 *           "multi_connect": {
 *             "member": { // 该用户所在直播间连线组的成员信息。如果该用户在多个直播间内，优先返回当前主播，如果不在当前直播间内，则返回麦序最小的主播
 *               "index": 1, // 主播麦序
 *               "room": {
 *                 "room_id": 10659544,
 *                 "creator_id": 3457111
 *               }
 *             }
 *           }
 *         }
 *       ]
 *     }
 *   }
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 */
func ActionMultiConnectUserRank(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newUserRankParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	resp, err := param.resp()
	if err != nil {
		return nil, "", err
	}
	return resp, "", nil
}

type rankSwitchParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GroupID int64 `form:"group_id" json:"group_id"`

	c            *handler.Context
	group        *livemulticonnect.Group
	groupMembers []*livemulticonnect.GroupMember // 所在连线组的全部成员
	roomMember   *livemulticonnect.GroupMember   // 当前房间的连线成员信息
}

func newRankSwitchParam(c *handler.Context) (*rankSwitchParam, error) {
	param := &rankSwitchParam{
		c: c,
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.GroupID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *rankSwitchParam) check() error {
	r, err := room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	// 只允许主播操作
	if param.c.UserID() == 0 || param.c.UserID() != r.CreatorID {
		return actionerrors.ErrForbidden
	}

	param.group, err = livemulticonnect.FindOngoingGroup(param.GroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.group == nil {
		return actionerrors.ErrMultiConnectGroupNotFound
	}
	param.groupMembers, err = param.group.Members(nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	index := slices.IndexFunc(param.groupMembers, func(m *livemulticonnect.GroupMember) bool {
		return m.RoomID == param.RoomID
	})
	if index < 0 {
		return actionerrors.ErrMultiConnectGroupMemberModified
	}
	param.roomMember = param.groupMembers[index]

	mcCfg, err := params.FindMultiConnect()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 只有在白名单内的主麦才可以操作
	if param.roomMember.Role != livemulticonnect.MemberRoleOwner || livemulticonnect.GetUserRankGranted(mcCfg, param.RoomID) == livemulticonnect.UserRankNotGranted {
		return actionerrors.NewErrForbidden("暂时没有该操作的权限")
	}
	return nil
}

func (param *rankSwitchParam) switchRankStatusAndBroadcast(rankOn bool) error {
	enable := livemulticonnect.GetUserRankEnabled(param.GroupID)
	// 当前状态和目标状态一致时，直接返回
	if (enable == livemulticonnect.UserRankEnabled && rankOn) ||
		(enable == livemulticonnect.UserRankDisabled && !rankOn) {
		return nil
	}

	err := livemulticonnect.SwitchUserRankStatus(param.GroupID, rankOn)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{"group_id": param.GroupID, "rank_on": rankOn, "enable": enable})
	}

	// 广播消息通知连线组的全部主播
	event := liveim.EventRankOff
	if rankOn {
		event = liveim.EventRankOn
	}
	err = userapi.BroadcastMany(livemulticonnect.NewRankSwitchBroadcastPayload(event, param.GroupID, param.groupMembers))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

// ActionMultiConnectUserRankOn 显示主播连线组的在线用户榜单
/**
 * @api {post} /api/v2/chatroom/multi-connect/user/rank/on 显示主播连线组的在线用户榜单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 显示主播连线组在线用户榜单的消息
 *   {
 *     "type": "multi_connect",
 *     "event": "rank_on",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111 // 连线组 ID
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMultiConnectUserRankOn(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newRankSwitchParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.switchRankStatusAndBroadcast(true)
	if err != nil {
		return nil, "", err
	}
	return nil, "success", nil
}

// ActionMultiConnectUserRankOff 隐藏主播连线组的在线用户榜单
/**
 * @api {post} /api/v2/chatroom/multi-connect/user/rank/off 隐藏主播连线组的在线用户榜单
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 当前直播间 ID
 * @apiParam {Number} group_id 连线组 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiSuccessExample {json} WebSocket 隐藏主播连线组在线用户榜单的消息
 *   {
 *     "type": "multi_connect",
 *     "event": "rank_off",
 *     "room_id": 223344,
 *     "multi_connect": {
 *       "group_id": 111 // 连线组 ID
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 200020003
 * @apiError (403) {String} info 用户没有权限
 */
func ActionMultiConnectUserRankOff(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newRankSwitchParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.switchRankStatusAndBroadcast(false)
	if err != nil {
		return nil, "", err
	}
	return nil, "success", nil
}
