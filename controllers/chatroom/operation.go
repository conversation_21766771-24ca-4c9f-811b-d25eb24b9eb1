package chatroom

import (
	"encoding/json"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/cache/usersession"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/livenewuserrewardrecord"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/viewlog"
	"github.com/MiaoSiLa/live-service/models/redis/rankpoint"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/activity/rankevent"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	onlineTTL                 = 15 * time.Minute
	onlineUpdateInterval      = 60 * 1000
	onlineUpdateLeaveInterval = 5 * 1000      // 快速进出房间时把离开房间的操作也认为成无效
	onlineInRoomTimeInterval  = 5 * 60 * 1000 // 在直播间中心跳间隔 5 分钟
	onlineBackendTimeInterval = 2 * 60 * 1000 // 在后台或最小化心跳间隔 2 分钟
)

const (
	onlineLeaveInterval = 3 * onlineInRoomTimeInterval

	onlineCounterEnterClosedRoom = 0
	onlineCounterEnter           = 1
	onlineCounterLeave           = -1
)

const (
	onlineStatusInRoom = iota
	onlineStatusBackend
	onlineStatusFinishedLiveTask
)

type onlineParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	Counter int   `form:"counter" json:"counter"`
	Status  int   `form:"status" json:"status"`

	room    *room.Room
	c       *handler.Context
	userCtx userapi.UserContext

	isUser bool
	userID int64
	accID  string

	clientIP   string
	equipID    string
	equip      livelistenlogs.Equip
	accessTime time.Time // 用户进入接口的时间
}

type onlineResp struct {
	UserID       int64                `json:"user_id"`
	LastTime     goutil.TimeUnixMilli `json:"lasttime"`
	NextTime     goutil.TimeUnixMilli `json:"nexttime"`
	NewUserPrize *newUserPrize        `json:"new_user_prize,omitempty"`
}

type newUserPrize struct {
	PrizeName    string               `json:"prize_name"`
	IconURL      string               `json:"icon_url"`
	BackpackItem *reward.BackpackItem `json:"backpack_item"`
}

// ActionOnline online 定时接口
/**
 * @api {post} /api/v2/chatroom/online 用户在线心跳
 * @apiDescription 主播、登录用户和游客都要调用每 5 分钟调用一次，APP 在最小化或后台时调用改为每 2 分钟调用一次，App 收听时长任务倒计时为 0 时立即请求（status 为 2）
 * 如果主播是从开播状态切换成关播的，不需要请求，但是如果从关播切换到开播直播间，需要正常开始从 1 传
 * 在关播到开播的切换请求这个接口的时候（用户侧）需要随机 delay 2~10s，再开始请求 online 接口（如果 delay 过程中主播又关播了，需要取消这个动作），主播侧不要 delay 直接请求
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} counter 心跳计数，第一次传 1，后续递增，离开当前直播间（不再收听当前直播）时传 -1，进关播直播间需要传 0，退出关播直播间不用请求
 * @apiParam {number=0,1,2} [status=0] 0 在直播间内，1 在后台或最小化时，2 收听时长任务倒计时为 0 时调用
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user_id": 9074509, // 用户 ID，游客返回 0
 *       "lasttime": 1575710215377, // 毫秒级时间戳
 *       "nexttime": 1575710515377, // 毫秒级时间戳，一般比上一个大 5 分种
 *       "new_user_prize": { // 新客奖励，客户端不需要自己拼名称，直接使用 prize_name 和 icon_url 字段展示
 *         "prize_name": "新客奖励×10",
 *         "icon_url": "https://static-test.maoercdn.com/live/gifts/icons/30010.png",
 *         "backpack_item": { // 同时支持背包礼物和道具
 *           "type": 1, // 背包物品类型：1：礼物；2：道具
 *           "gift_id": 30010, // 礼物 ID，类型为礼物时下发
 *           "item_id": 123, // 道具 ID，类型为道具时下发
 *           "name": "新客奖励",
 *           "price": 0, // 礼物价格（钻）
 *           "num": 10,
 *         }
 *       }
 *     }
 *   }
 *
 */
func ActionOnline(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newOnlineParam(c)
	if err != nil {
		return nil, err
	}
	err = param.checkUser()
	if err != nil {
		return nil, err
	}
	err = param.checkRoom()
	if err != nil {
		return nil, err
	}

	isOpen := util.IntToBool(param.room.Status.Open)
	switch {
	// 开播直播间的 counter 需要大于 0 或为 -1
	case isOpen && (param.Counter > 0 || param.Counter == -1):
		return param.updateOnline()
	// 关播直播间的 counter 需要为 0
	case !isOpen && param.Counter == 0:
		return param.joinClosedRoom()
	default:
		return nil, actionerrors.ErrParams
	}
}

func newOnlineParam(c *handler.Context) (*onlineParam, error) {
	param := new(onlineParam)

	_ = c.Bind(param)
	if param.RoomID <= 0 || (param.Counter < 0 && param.Counter != onlineCounterLeave) {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	param.accessTime = goutil.TimeNow()
	e := c.Equip()
	param.clientIP = c.ClientIP()
	param.equipID = e.EquipID
	param.equip = livelistenlogs.Equip{
		OS: e.OS,
		IP: c.ClientIP(),
	}
	if e.FromApp {
		param.equip.EquipID = &e.EquipID
		param.equip.Version = &e.AppVersion
	}
	if buvid := c.BUVID(); buvid != "" {
		param.equip.BUVID = &buvid
	}
	param.userCtx = userapi.NewUserContext(c)
	return param, nil
}

func (param *onlineParam) checkUser() (err error) {
	u := param.c.User()
	if u == nil {
		// 游客通过 session 获取状态
		session, err := usersession.SessionFromCookie(param.c.Request())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if session == nil || session.Guest == nil {
			// session 不正确
			return actionerrors.ErrUnloggedUser
		}
		param.accID = session.Guest.AccID
		return nil
	}
	// 注册用户
	param.isUser = true
	param.userID = u.ID
	param.accID = userIDToAccID(param.userID)
	return nil
}

func (param *onlineParam) checkRoom() (err error) {
	param.room, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil ||
		param.room.LimitStatus(param.userID) == room.LimitStatusBlocked {
		return actionerrors.ErrCannotFindRoom
	}
	if param.room.IsBan() {
		return actionerrors.ErrBannedRoom
	}
	return nil
}

func (param *onlineParam) joinClosedRoom() (resp *onlineResp, err error) {
	goutil.Go(func() {
		param.addCatFoods() // 获取猫粮与在线心跳计数无关
		param.addLiveHistory()
		param.addViewRankPoint()
		param.addClosedRoomListenLog()
	})

	nowMilli := goutil.NewTimeUnixMilli(param.accessTime)
	return &onlineResp{
		UserID:   param.userID,
		LastTime: nowMilli,
		NextTime: 0, // 客户端不需要再次请求
	}, nil
}

func (param *onlineParam) updateOnline() (resp *onlineResp, err error) {
	goutil.Go(func() {
		param.addCatFoods()           // 获取猫粮与在线心跳计数无关
		param.questActivityViewRoom() // 总是尝试完成活动任务
		param.sendRankEvent()         // 发送进入房间事件
	})

	cacheKey := livelistenlogs.KeyRoomOnline(param.RoomID)
	userKey := param.accID

	nowMilli := goutil.NewTimeUnixMilli(param.accessTime)
	r, err := service.Redis.HGet(cacheKey, userKey).Bytes()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var ot livelistenlogs.OnlineTime
	if serviceredis.IsRedisNil(err) {
		// 本房间没有该用户的 online 缓存，更新房间累计人数
		param.room.AddAccumulate()
	} else {
		err = json.Unmarshal(r, &ot) // 出错了认为没在线
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	newUserPrize := param.addNewUserPrize()
	defer func() {
		nextTime := ot.CurrentTime + onlineInRoomTimeInterval
		if param.Status == onlineStatusBackend {
			nextTime = ot.CurrentTime + onlineBackendTimeInterval
		}
		resp = &onlineResp{
			UserID:       param.userID,
			LastTime:     ot.CurrentTime,
			NextTime:     nextTime,
			NewUserPrize: newUserPrize,
		}
	}()

	if param.Status != onlineStatusFinishedLiveTask &&
		((ot.CurrentTime+onlineUpdateInterval > nowMilli &&
			param.Counter != onlineCounterLeave) ||
			ot.LeaveTime+onlineUpdateLeaveInterval > nowMilli) {
		// 用户访问频繁，不更新 redis 和数据库，完成直播任务除外
		return
	}

	lastCall := ot.CurrentTime
	// 将 ot 从存储结果更新成最新结果
	if ot.StartTime == 0 || ot.StartTime < ot.LeaveTime || ot.CurrentTime+onlineLeaveInterval < nowMilli {
		/*
			1. 没进过房间
			2. 之前出去过房间
			3. 调用间隔太长
		*/
		ot.StartTime = nowMilli
	}

	if param.Counter != onlineCounterLeave {
		ot.CurrentTime = nowMilli
	} else {
		ot.LeaveTime = nowMilli
		// TODO: 无法处理主播多端离开房间，所以去掉对 current_time 的置零
		// ot.CurrentTime = 0 // 把 current_time 设置成 0 保证下一次调用肯定成功写入日志
	}

	goutil.Go(func() {
		param.questActivityListenRoomDuration(ot.StartTime, lastCall)
		param.addOnlineTime(lastCall) // FIXME: 需要处理用户中间离开直播间的情况
		param.addListenLog(ot.StartTime)
		param.addLiveHistory()
		param.addViewRankPoint()
		param.addMultiConnectListenPoint(lastCall.ToTime())
	})

	b, _ := json.Marshal(ot)
	pipe := service.Redis.Pipeline()
	cmd := pipe.HSet(cacheKey, userKey, b)
	pipe.Expire(cacheKey, onlineTTL)
	_, _ = pipe.Exec()
	if err = cmd.Err(); err != nil {
		err = actionerrors.NewErrServerInternal(err, nil)
	}

	return
}

func (param *onlineParam) addOnlineTime(lastCall goutil.TimeUnixMilli) {
	if !param.isUser {
		return
	}
	// TODO: 暂不需要使用贵族信息
	addParam := userstatus.NewAddContributionParams(param.userID, param.RoomID, param.room.CreatorUsername, userstatus.FromNormal, nil)
	if lastCall == 0 && param.Counter == onlineCounterEnter {
		// 第一次访问
		err := viewlog.Update(param.userID, param.room, param.clientIP, param.accessTime)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		err = addParam.QuestJoinRoom()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return
	}
	// 在直播间一阵时间
	// 用户等级任务
	delta := goutil.NewTimeUnixMilli(param.accessTime) - lastCall
	err := addParam.AddOnlineTime(int64(delta))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 勋章亲密度任务
	if param.room.CreatorID != param.userID {
		addOnlineParam := livemedalstats.AddOnlineTimeParam{
			UserID:       param.userID,
			RoomID:       param.RoomID,
			RoomOID:      param.room.OID,
			CreatorID:    param.room.CreatorID,
			AccessTime:   param.accessTime,
			LastCallTime: lastCall.ToTime(),
		}
		medalPointInfo, err := addOnlineParam.AddOnlineTime()
		if err != nil {
			logger.Error(err)
			return
		}
		sendUserMedalNotify(param.userID, param.room, medalPointInfo)
	}
}

func sendUserMedalNotify(userID int64, r *room.Room, medalUpdatedInfo *livemedal.MedalUpdatedInfo) {
	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		CreatorUsername:  r.CreatorUsername,
		UserID:           userID,
		RoomID:           r.RoomID,
	}
	notify := notifyParam.NewUserMedalNotify()
	// NewUserMedalNotify 返回的是封装后的 notify, 需要调用 BroadcastMany
	err := userapi.BroadcastMany([]*userapi.BroadcastElem{notify})
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// REVIEW: 对于多端收听的情况，需要解决多端不同步进出房间的问题
func (param *onlineParam) addListenLog(startTime goutil.TimeUnixMilli) {
	var medalPoint int64
	if param.userID != 0 {
		l, err := livemedal.FindSimples(bson.M{"room_id": param.RoomID, "user_id": param.userID},
			options.Find().SetProjection(bson.M{"point": 1}), false, false)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if len(l) != 0 {
			medalPoint = l[0].Point
		}
	}
	callTime := goutil.NewTimeUnixMilli(param.accessTime)
	now := goutil.TimeNow()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livelistenlogs.Collection()

	var l livelistenlogs.LiveListenLog
	err := col.FindOne(ctx,
		bson.M{
			"accid":       param.accID,
			"room_id":     param.RoomID,
			"start_time":  startTime,
			"open_log_id": param.room.Status.OpenLogID,
		},
	).Decode(&l)
	if err != nil && !mongodb.IsNoDocumentsError(err) {
		logger.Error(err)
		return
	}
	if mongodb.IsNoDocumentsError(err) {
		// 没有数据，添加
		l = livelistenlogs.LiveListenLog{
			UserID:          param.userID,
			AccID:           param.accID,
			RoomID:          param.RoomID,
			CatalogID:       param.room.CatalogID,
			OpenLogID:       param.room.Status.OpenLogID,
			StartTime:       startTime,
			StartMedalPoint: medalPoint,
			LastTime:        callTime,
			LastMedalPoint:  medalPoint,
			Duration:        int64(callTime - startTime),
			IPs:             []string{param.clientIP},
			Equips:          []*livelistenlogs.Equip{&param.equip},
			CreateTime:      now,
			ModifiedTime:    now,
		}
		if param.equipID != "" {
			l.EquipIDs = []string{param.equipID}
		}
		_, err := col.InsertOne(ctx, l)
		if err != nil {
			logger.Error(err)
			return
		}
		return
	}
	// 有数据，更新
	set := bson.M{
		"last_time":        callTime,
		"last_medal_point": medalPoint,
		"duration":         int64(callTime - startTime),
		"modified_time":    now,
	}
	addToSet := bson.M{
		"ips":    param.clientIP,
		"equips": param.equip,
	}
	if param.equipID != "" {
		addToSet["equip_ids"] = param.equipID
	}
	_, err = col.UpdateOne(ctx,
		bson.M{"_id": l.OID},
		bson.M{"$set": set, "$addToSet": addToSet},
	)
	if err != nil {
		logger.Error(err)
		return
	}
}

// addClosedRoomListenLog 记录访问关播直播间的收听日志
func (param *onlineParam) addClosedRoomListenLog() {
	now := goutil.TimeNow()
	l := livelistenlogs.LiveListenLog{
		UserID:       param.userID,
		AccID:        param.accID,
		RoomID:       param.RoomID,
		CatalogID:    param.room.CatalogID,
		OpenLogID:    param.room.Status.OpenLogID,
		StartTime:    goutil.NewTimeUnixMilli(now),
		IPs:          []string{param.clientIP},
		Equips:       []*livelistenlogs.Equip{&param.equip},
		CreateTime:   now,
		ModifiedTime: now,
	}
	if param.equipID != "" {
		l.EquipIDs = []string{param.equipID}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livelistenlogs.Collection().InsertOne(ctx, l)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *onlineParam) addCatFoods() {
	if !param.isUser {
		return
	}
	_, err := useritems.AddCatFoods(param.userID)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *onlineParam) questActivityViewRoom() {
	if param.userID == 0 {
		return
	}

	f := quests.FinishRoomQuestParam{
		RoomID:    param.RoomID,
		CreatorID: param.room.CreatorID,
		UserID:    param.userID,
	}
	f.ViewRoom()
}

// sendRankEvent 发送进入房间事件
func (param *onlineParam) sendRankEvent() {
	if param.Counter != onlineCounterEnter {
		return
	}
	err := rankevent.NewSyncCommonParam(param.c.UserID()).
		SetRoomInfo(param.room.RoomID, param.room.CreatorID, param.room.GuildID, param.room.ActivityCatalogID).
		EnterRoom(param.accessTime).
		Send(param.c.UserContext())
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *onlineParam) questActivityListenRoomDuration(startTime, lastTime goutil.TimeUnixMilli) {
	if param.userID == 0 {
		return
	}

	f := quests.FinishRoomQuestParam{
		RoomID:    param.RoomID,
		CreatorID: param.room.CreatorID,
		UserID:    param.userID,
		StartTime: startTime.ToTime(),
		CallTime:  param.accessTime,
		LastTime:  lastTime.ToTime(),

		Room: param.room,
	}
	f.ListenRoomDuration()
}

func (param *onlineParam) addNewUserPrize() *newUserPrize {
	// 第一次心跳可以判断出是否是新客
	if param.userID == 0 || param.Counter != onlineCounterEnter {
		return nil
	}
	// 非客户端以及客户端版本号小于 6.1.2 都不处理
	if !param.c.Equip().FromApp || param.c.BUVID() == "" {
		return nil
	}
	if param.c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.1.2", Android: "6.1.2"}) {
		return nil
	}

	rewardParam, err := params.FindReward()
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	// 未设置奖励直接返回
	if rewardParam.LiveNewUserRewardID <= 0 {
		return nil
	}

	key := keys.LockLiveNewUserRewardRecord1.Format(param.userID)
	exists, err := service.LRURedis.Exists(key).Result()
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if exists > 0 {
		return nil
	}

	userRecord, err := livenewuserrewardrecord.FindUserRewardRecord(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
		return nil
	}
	if userRecord != nil {
		err := service.LRURedis.Set(key, 1, time.Hour*24).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		return nil
	}

	logFields := logger.Fields{
		"user_id":   param.userID,
		"room_id":   param.RoomID,
		"reward_id": rewardParam.LiveNewUserRewardID,
	}
	reached, err := livelistenlogs.IsUserAppListenLogDurationReached(param.userID, livelistenlogs.NewUserRewardLimitDuration, nil)
	if err != nil {
		logger.WithFields(logFields).Error(err)
		// PASS
		return nil
	}

	if reached {
		logFields["status"] = livenewuserrewardrecord.StatusInvalid
		err = param.addLiveNewUserRewardRecord(rewardParam.LiveNewUserRewardID, livenewuserrewardrecord.StatusInvalid)
		if err != nil {
			logger.WithFields(logFields).Error(err)
			// PASS
			return nil
		}
		err = service.LRURedis.Set(key, 1, time.Hour*24).Err()
		if err != nil {
			logger.WithFields(logFields).Error(err)
			// PASS
		}

		return nil
	}

	logFields["status"] = livenewuserrewardrecord.StatusGranted
	item, err := param.newUserRewardSend(rewardParam.LiveNewUserRewardID)
	if err != nil {
		logger.WithFields(logFields).Error(err)
		// PASS
		return nil
	}
	if item == nil {
		logger.WithFields(logFields).Error("item is empty")
		// PASS
		// 找不到奖励物品，发放记录奖励 ID 存 0
		rewardParam.LiveNewUserRewardID = 0
	}

	err = param.addLiveNewUserRewardRecord(rewardParam.LiveNewUserRewardID, livenewuserrewardrecord.StatusGranted)
	if err != nil {
		logger.WithFields(logFields).Error(err)
		// PASS
	}

	err = service.LRURedis.Set(key, 1, time.Hour*24).Err()
	if err != nil {
		logger.WithFields(logFields).Error(err)
		// PASS
	}

	if item == nil {
		return nil
	}

	return &newUserPrize{
		PrizeName:    fmt.Sprintf("%s×%d", item.Name, item.Num),
		IconURL:      item.IconURL,
		BackpackItem: item,
	}
}

func (param *onlineParam) addLiveNewUserRewardRecord(rewardID int64, status int) error {
	record := livenewuserrewardrecord.LiveNewUserRewardRecord{
		UserID:   param.userID,
		RewardID: rewardID,
		BUVID:    param.c.BUVID(),
		Status:   status,
	}
	err := record.Create()
	if err != nil && !servicedb.IsUniqueError(err) {
		return err
	}
	return nil
}

func (param *onlineParam) newUserRewardSend(liveNewUserRewardID int64) (*reward.BackpackItem, error) {
	reward, err := reward.FindRewardByRewardIDWithCache(liveNewUserRewardID)
	if err != nil {
		return nil, err
	}
	if reward == nil {
		return nil, nil
	}
	item, err := reward.FindBackpackItem()
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, nil
	}
	err = reward.Send(param.RoomID, param.room.CreatorID, param.userID)
	if err != nil {
		return nil, err
	}

	return item, nil
}

// addLiveHistory 添加直播历史记录
func (param *onlineParam) addLiveHistory() {
	if param.Counter != onlineCounterEnter && param.Counter != onlineCounterEnterClosedRoom {
		return
	}
	// 游客没有 userID
	if param.userID == 0 {
		return
	}
	if param.room.Config != nil && param.room.Config.DisableUserHistory {
		return
	}

	accessTime := goutil.NewTimeUnixMilli(param.accessTime)
	err := userapi.SendLiveHistory(param.userID, param.RoomID, accessTime, param.userCtx)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *onlineParam) addViewRankPoint() {
	if param.userID == 0 || param.userID == param.room.CreatorID {
		return
	}
	before, after, err := rankpoint.AddViewCountDaily(param.userID, param.room.RoomID)
	if err != nil {
		logger.Error(err)
		return
	}
	point := after/10 - before/10
	if point <= 0 {
		return
	}
	// 访问直播间只需要加主播榜
	rankChange, err := usersrank.AddRevenue(param.room.CreatorID, param.room.RoomID, point)
	if err != nil {
		logger.Error(err)
		return
	}
	err = room.NotifyHourRank(rankChange, param.room)
	if err != nil {
		logger.Error(err)
		return
	}
}

// addMultiConnectListenPoint 添加连线组的收听积分
func (param *onlineParam) addMultiConnectListenPoint(lastCall time.Time) {
	if param.userID == 0 || param.userID == param.room.CreatorID {
		return
	}
	member, err := livemulticonnect.FindOngoingMemberByRoomID(nil, param.RoomID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id": param.RoomID,
			"user_id": param.userID,
		}).Error(err)
		return
	}
	if member == nil {
		return
	}
	// 只处理非首次进入直播间和主动退出直播间的情况
	if param.Counter <= onlineCounterEnter && param.Counter != onlineCounterLeave {
		return
	}
	// 收听时长小于 30s 不加收听积分
	if param.accessTime.Sub(lastCall) < livemulticonnect.ListenScoreThresholdDuration {
		return
	}
	err = livemulticonnect.AddUserRankScore(livemulticonnect.UserRankScoreSourceListen, member.GroupID, param.userID, livemulticonnect.ListenScore)
	if err != nil {
		logger.WithFields(logger.Fields{
			"group_id": member.GroupID,
			"user_id":  param.userID,
		}).Error(err)
		// PASS
	}
}

type closeResp struct {
	OK         int                   `json:"ok"`
	Statistics *room.CloseStatistics `json:"statistics"`
	Status     *room.CloseStatus     `json:"status"`
}

// ActionClose 关播
/**
 * @api {post} /api/v2/chatroom/close 主播关播
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "ok": 1,
 *       "statistics": {
 *         "duration": 12456, // 直播时长，单位：毫秒
 *         "revenue": 12456, // 直播收益，单位：钻
 *         "accumulation": 16, // 累计人数
 *         "message_count": 100, // 消息数目
 *         // WebSocket 消息不包含以下字段，因此主播不是在当前设备关播或者被超管关播均显示为空
 *         // 如果接口没下发以下字段，也需要显示为空
 *         "new_fans_count": 10, // 新增粉丝人数
 *         "new_medal_count": 5, // 新增粉丝勋章数量
 *         "paid_user_count": 25 // 消费/打赏人数
 *       },
 *       "status": {
 *         "open_time": 1727409158000, // 开播时间戳，单位：毫秒
 *         "close_time": 1727409158000 // 关播时间戳，单位：毫秒
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *   {
 *     "type": "room",
 *     "event": "close",
 *     "room_id": 139879430,
 *     "time": 1234567890000, // 毫秒时间戳
 *     "statistics": {
 *       "duration": 12456, // 直播时长，单位：毫秒
 *       "accumulation": 16, // 累计人数
 *       "message_count": 100 // 消息数目
 *     },
 *     "status": {
 *       "open_time": 1727409158000, // 开播时间戳，单位：毫秒
 *       "close_time": 1727409158000 // 关播时间戳，单位：毫秒
 *     },
 *     "recommend_max_delay": 1000, // 最大等待时间，单位为毫秒，返回 -1 时不请求 recommend 接口（用于 /api/v2/chatroom/recommend 接口）
 *     "recommend": [ // 有此字段时直接用这里的数据展示，不请求 recommend 接口
 *       {
 *         "room_id": 152,
 *         "name": "12345",
 *         "cover_url": "http://static.maoercdn.com/avatars/icon01.png",
 *         "announcement": "12345",
 *         "creator_id": 12345,
 *         "creator_username": "1234",
 *         "creator_iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *         "creator_introduction": "主播个人简介",
 *         "catalog_id": 107,
 *         "catalog_name": "分区名",
 *         "catalog_color": "#ffffff",
 *         "custom_tag": {
 *           "tag_id": 115,
 *           "tag_name": "个性词条"
 *         },
 *         "statistics": {
 *           "accumulation": 123,
 *           "score": 1
 *           ...
 *         },
 *         "status": {
 *           "open": 1,
 *           ...
 *         }
 *       }
 *     ],
 *     "by": "user",
 *     "user": {
 *       "user_id": 12
 *     }
 *   }
 *
 */
func ActionClose(c *handler.Context) (handler.ActionResponse, error) {
	// TODO: admin 下的代码复用
	var input struct {
		RoomID int64 `form:"room_id" json:"room_id"`
	}
	_ = c.Bind(&input)
	if input.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(input.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.CreatorID != c.UserID() {
		return nil, actionerrors.ErrForbidden
	}
	if r.Status.Open == room.StatusOpenFalse {
		return nil, actionerrors.ErrClosedRoom
	}
	cs := room.NewCloseStatistics(r, room.OperatorUser, c.UserID(), "")
	recommend := CloseNotifyRecommend(r, c.ClientIP())
	room.NewCloseNotifier(cs, recommend, c).Notify()
	r.ClearList(c)
	err = r.Close(cs, c.UserContext())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	goutil.Go(func() {
		err = livepk.AfterCloseRoom(r.RoomID)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
		err = livemulticonnect.AfterCloseRoom(r)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
	})

	logger.WithFields(logger.Fields{
		"user_id":    c.UserID(),
		"room_id":    input.RoomID,
		"ip":         c.ClientIP(),
		"user_agent": c.UserAgent(),
	}).Info("Close Room")
	return closeResp{
		OK:         1,
		Statistics: cs,
		Status: &room.CloseStatus{
			OpenTime:  r.Status.OpenTime,
			CloseTime: *r.Status.CloseTime,
		},
	}, nil
}

// CloseNotifyRecommend 关播消息推荐
func CloseNotifyRecommend(r *room.Room, clientIP string) []*room.Simple {
	if !r.IsSpecialCreator() {
		// 普通主播不在这里（即关播消息通知里）下发关播推荐
		return nil
	}
	param := recommendParam{
		preRoomID: r.RoomID,
		catalogID: r.CatalogID,
		opt: &room.FindOptions{
			ClientIP:        clientIP,
			FindCreator:     true,
			FindCatalogInfo: true,
			FindCustomTag:   true,
		},
	}
	param.fillBaseRoomData()

	err := param.findByCatalog()
	if err != nil {
		logger.WithField("room_id", r.RoomID).Errorf("close recommend error: %v", err)
		return make([]*room.Simple, 0)
	}

	err = param.findByHeat()
	if err != nil {
		logger.WithField("room_id", r.RoomID).Errorf("close recommend error: %v", err)
		return make([]*room.Simple, 0)
	}
	param.checkResp()
	return param.Data
}

// ActionShare 分享聊天室
/**
 * @api {post} /api/v2/chatroom/share 分享聊天室
 * @apiDescription 如果可能，需要在客户端分享成功的回调中调用这个接口。如果无法使用分享回调，这块可以通过 App 切换回来的时间判定分享时间大于一定阈值来判断用户进行了分享的操作。
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} type 分享平台 1：微信好友，2：朋友圈，3：QQ 好友，4：新浪微博，5：QQ 空间
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "ok": 1
 *     }
 *   }
 */
func ActionShare(c *handler.Context) (handler.ActionResponse, error) {
	var input struct {
		RoomID int64 `form:"room_id" json:"room_id"`
		Type   int64 `form:"type" json:"type"`
	}
	_ = c.Bind(&input)
	if input.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(input.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	userID := c.UserID()
	if userID == 0 || r.Limit != nil || r.Status.Open != room.StatusOpenTrue {
		// 游客不进行后续操作
		// 房间受限或未开播不增加用户经验、亲密度
		return handler.M{
			"ok": 1,
		}, nil
	}
	// TODO: 暂不需要使用贵族信息
	addParam := userstatus.NewAddContributionParams(userID, input.RoomID, r.CreatorUsername, userstatus.FromNormal, nil)
	err = addParam.AddWithFirstTimeBonus(400)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = addUserShareCountDaily(r, userID)
	if err != nil {
		return nil, err
	}
	return handler.M{
		"ok": 1,
	}, nil
}

func addUserShareCountDaily(r *room.Room, userID int64) error {
	if r.CreatorID == userID {
		// 主播分享自己的直播间不记录分享次数
		return nil
	}
	ok, err := rankpoint.AddUserShareRoomDaily(userID, r.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if ok {
		// 榜单增加人气值
		addRankByPoint(r, userID, rankpoint.PointShareDaily)
	}

	filter := bson.M{"user_id": userID, "room_id": r.RoomID, "status": bson.M{"$gt": livemedal.StatusPending}}
	liveMedal, err := livemedal.FindOne(filter, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 如果找不到勋章，不计算分享
	if liveMedal == nil {
		return nil
	}
	ok, err = livemedal.UpdateUserShareAndCheck(r.OID, r.RoomID, userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if ok {
		goutil.Go(func() {
			addFreeMedalPoint(r, userID)
		})
	}

	return nil
}

func addFreeMedalPoint(r *room.Room, userID int64) {
	// 增加亲密度
	addParam := &livemedalstats.AddFreePointParam{
		RoomID:    r.RoomID,
		CreatorID: r.CreatorID,
		UserID:    userID,
		PointAdd:  10,
		Scene:     livemedalpointlog.SceneTypeTaskShare,
	}
	medalUpdatedInfo, err := addParam.AddFreePoint()
	if err != nil {
		logger.Error(err)
		return
	}
	sendUserMedalNotify(userID, r, medalUpdatedInfo)
}
