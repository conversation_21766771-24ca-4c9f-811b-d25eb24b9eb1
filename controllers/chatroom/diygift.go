package chatroom

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygiftdresses"
	"github.com/MiaoSiLa/live-service/models/mongodb/diygifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userdiygifts"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
)

const (
	giftDiyStatusNoHistory = iota
	giftDiyStatusChooseOne
	giftDiyStatusChooseNone
)

type diyGiftInfoParam struct {
	gift *gift.Gift
	user *user.User
	room *room.Room

	diyGift  *diygifts.DiyGift
	dressMap map[int][]*diygiftdresses.DiyGiftDress

	userDiyGift *userdiygifts.UserDiyGift
}

type giftDiy struct {
	GiftID     int64              `json:"gift_id"`
	Background *giftDiyBackground `json:"background,omitempty"`
	Words      *giftDiyWords      `json:"words,omitempty"`
	Avatars    *giftDiyAvatars    `json:"avatars,omitempty"`
	Dress      []giftDiyDress     `json:"dress"`
}

type giftDiyBackground struct {
	ImageURL string `json:"image_url"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
}

type giftDiyWords struct {
	ImageURL        string `json:"image_url"`
	PreviewPosition []int  `json:"preview_position"`
	Status          int    `json:"status"`
	Text            string `json:"text"`
	TextColor       string `json:"text_color"`
}

type giftDiyAvatars struct {
	Status  int         `json:"status"`
	Creator giftDiyIcon `json:"creator"`
	User    giftDiyIcon `json:"user"`
}

type giftDiyIcon struct {
	IconURL         string `json:"iconurl"`
	PreviewPosition []int  `json:"preview_position"`
	Choose          bool   `json:"choose,omitempty"`
}

type giftDiyDress struct {
	Type int                `json:"type"`
	Name string             `json:"name"`
	Data []giftDiyDressElem `json:"data"`
}

type giftDiyDressElem struct {
	DressID  string `json:"dress_id"`
	IconURL  string `json:"icon_url"`
	ImageURL string `json:"image_url"`
	Choose   bool   `json:"choose,omitempty"`
}

func newDiyGiftInfoParam(c *handler.Context) (*diyGiftInfoParam, error) {
	param := &diyGiftInfoParam{
		user: c.User(),
	}

	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	var err error
	param.room, err = room.Find(roomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}

	giftID, _ := c.GetParamInt64("gift_id")
	if giftID <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.gift, err = gift.FindShowingGiftByGiftID(giftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.gift == nil {
		return nil, actionerrors.ErrNotFound("礼物不存在")
	}

	return param, nil
}

func (param *diyGiftInfoParam) findDiyGift() error {
	var err error
	param.diyGift, err = diygifts.FindDiyGiftByGiftID(param.gift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.diyGift == nil {
		return actionerrors.ErrNotFound("该礼物无法定制")
	}
	if len(param.diyGift.DressTypes) != 0 {
		dressTypes := make([]int, len(param.diyGift.DressTypes))
		for i := range param.diyGift.DressTypes {
			dressTypes[i] = param.diyGift.DressTypes[i].Type
		}
		param.dressMap, err = diygiftdresses.FindDiyGiftDressMapByType(param.gift.GiftID,
			dressTypes)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if len(param.dressMap) != len(param.diyGift.DressTypes) {
			return actionerrors.ErrNotFound("无法找到定制信息")
		}
	}

	param.userDiyGift, err = userdiygifts.FindUserLastDiyGift(
		param.user.ID, param.gift.GiftID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if param.userDiyGift != nil &&
		param.userDiyGift.DiyOID != param.diyGift.OID {
		param.userDiyGift = nil
	}
	return nil
}

func (param *diyGiftInfoParam) resp() handler.M {
	// 构造通用的 giftDiy
	giftDiy := &giftDiy{
		GiftID: param.gift.GiftID,
		Background: &giftDiyBackground{
			ImageURL: storage.ParseSchemeURL(param.diyGift.Background.Image),
			Width:    param.diyGift.Background.Width,
			Height:   param.diyGift.Background.Height,
		},
		Dress: make([]giftDiyDress, 0, len(param.diyGift.DressTypes)),
	}
	if param.diyGift.Words != nil {
		giftDiy.Words = &giftDiyWords{
			ImageURL:        storage.ParseSchemeURL(param.diyGift.Words.Image),
			PreviewPosition: param.diyGift.Words.PreviewPosition,
			Status:          giftDiyStatusNoHistory,
			TextColor:       param.diyGift.Words.TextColor,
		}
	}
	if param.diyGift.Avatars != nil {
		giftDiy.Avatars = &giftDiyAvatars{
			Status: giftDiyStatusNoHistory,
			Creator: giftDiyIcon{
				IconURL:         param.room.CreatorIconURL,
				PreviewPosition: param.diyGift.Avatars.CreatorPreviewPosition,
			},
			User: giftDiyIcon{
				IconURL:         param.user.IconURL,
				PreviewPosition: param.diyGift.Avatars.UserPreviewPosition,
			},
		}
	}
	for _, d := range param.diyGift.DressTypes {
		dress := giftDiyDress{
			Type: d.Type,
			Name: d.Name,
		}
		dressList := param.dressMap[d.Type]
		dress.Data = make([]giftDiyDressElem, 0, len(dressList))
		for i := range dressList {
			dress.Data = append(dress.Data, giftDiyDressElem{
				DressID:  dressList[i].OID.Hex(),
				IconURL:  storage.ParseSchemeURL(dressList[i].Icon),
				ImageURL: storage.ParseSchemeURL(dressList[i].Image),
			})
		}
		giftDiy.Dress = append(giftDiy.Dress, dress)
	}

	resp := handler.M{"gift_diy": giftDiy}

	// 组合历史记录
	if param.userDiyGift == nil {
		for i := range giftDiy.Dress {
			// 默认选中第一个
			if len(giftDiy.Dress[i].Data) != 0 {
				giftDiy.Dress[i].Data[0].Choose = true
			}
		}
		return resp
	}

	if giftDiy.Words != nil {
		if param.userDiyGift.Words != "" {
			giftDiy.Words.Status = giftDiyStatusChooseOne
			giftDiy.Words.Text = param.userDiyGift.Words
		} else {
			giftDiy.Words.Status = giftDiyStatusChooseNone
		}
	}

	if giftDiy.Avatars != nil {
		if param.userDiyGift.IconConfig != 0 {
			giftDiy.Avatars.Status = giftDiyStatusChooseOne
			giftDiy.Avatars.Creator.Choose =
				param.userDiyGift.IconConfig.IsSet(userdiygifts.IconConfigCreator)
			giftDiy.Avatars.User.Choose =
				param.userDiyGift.IconConfig.IsSet(userdiygifts.IconConfigUser)
		} else {
			giftDiy.Avatars.Status = giftDiyStatusChooseNone
		}
	}

	dressIDMap := param.userDiyGift.DressIDMap()
	for i := range giftDiy.Dress {
		if len(giftDiy.Dress[i].Data) == 0 {
			continue
		}
		giftDiy.Dress[i].Data[0].Choose = true
		chooseOID := dressIDMap[giftDiy.Dress[i].Type]
		if !chooseOID.IsZero() {
			for j := range giftDiy.Dress[i].Data {
				if chooseOID.Hex() == giftDiy.Dress[i].Data[j].DressID {
					giftDiy.Dress[i].Data[0].Choose = false
					giftDiy.Dress[i].Data[j].Choose = true
					break
				}
			}
		}
	}

	return resp
}

// ActionDiyGiftInfo 获取 DIY 礼物详情
/**
 * @api {get} /api/v2/chatroom/diygift/info 获取 DIY 礼物详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} gift_id 礼物 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "gift_diy": {
 *         "gift_id": 123,
 *         "background": {
 *           "image_url": "https://statis-test.maoercdn.com/preview.png", // 预览图
 *           "width": 100, // 预览图宽度
 *           "height": 200 // 预览图高度
 *         },
 *         "words": { // 没有该字段说明没有赠言 tab
 *           "image_url": "https://static-test.maoercdn.com/1_2_3_4.png",
 *           "preview_position": [1,2,3,4], // 手机端预览位置 [x,y,w,h]
 *           "status": 1, // 0：没有历史记录，1：有历史记录，2：选择了无需赠言
 *           "text": "", // 用户上次赠送的赠言配置
 *           "text_color": "#FFFFFF" // 赠言颜色
 *         },
 *         "avatars": { // 没有该字段说明没有头像 tab
 *           "status": 1, // 0：没有历史记录，1：有历史记录，2：选择了无需头像
 *           "creator": {
 *             "iconurl": "*.png",
 *             "preview_position": [1,2,3,4],
 *             "choose": true // 是否勾选，false 或不存在为未勾选
 *           },
 *           "user": {
 *             "iconurl": "*.png",
 *             "preview_position": [5,6,3,4],
 *             "choose": false
 *           }
 *         },
 *         "dress": [
 *           {
 *             "type": 1,
 *             "name": "王子礼服", // 装扮分类名
 *             "data": [
 *               {
 *                 "dress_id": "6264b438fabff22a430d71ec", // 这个装扮的唯一 id
 *                 "icon_url": "*.png", // 装扮的缩略图
 *                 "image_url": "*.png", // 装扮的预览图（整个盖在背景上）
 *                 "choose": true // 是否被选中，没有或者 false 为未选中，data 中的 choose 都没有 true 的情况都不选中
 *               },
 *               {
 *                 "dress_id": "6264b438fabff22a430d71ed",
 *                 "icon_url": "*.png",
 *                 "image_url": "*.png"
 *               }
 *             ]
 *           },
 *           {
 *             "type": 2,
 *             "name": "公主礼服",
 *             "data": [
 *               {
 *                 "dress_id": "6264b438fabff22a430d71ea",
 *                 "icon_url": "*.png",
 *                 "image_url": "*.png"
 *               },
 *               {
 *                 "dress_id": "6264b438fabff22a430d71eb",
 *                 "icon_url": "*.png",
 *                 "image_url": "*.png",
 *                 "choose": true
 *               }
 *             ]
 *           }
 *         ]
 *       }
 *     }
 *   }
 *
 */
func ActionDiyGiftInfo(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDiyGiftInfoParam(c)
	if err != nil {
		return nil, err
	}
	err = param.findDiyGift()
	if err != nil {
		return nil, err
	}
	return param.resp(), nil
}

/**
 * @api {post} /api/v2/chatroom/diygift/send 送 DIY 礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParamExample {json} Request-Example：
 *   {
 *     "gift_id": 123, // DIY 礼物只能送一个，所以不传礼物数量
 *     "room_id": 456, // 实际收礼直播间
 *     "from_room_id": 123, // 用户送礼所在的直播间 ID，不传默认用户在实际收礼的直播间内送礼
 *     "words": "赠言，为空或不传则认为没有，最多十个字符",
 *     "avatars": {
 *       "creator": true,
 *       "user": false,
 *     },
 *     "dress": [
 *       {
 *         "type": 1,
 *         "dress_id": "6264b438fabff22a430d71ec",
 *       },
 *       {
 *         "type": 2,
 *         "dress_id": "6264b438fabff22a430d71eb",
 *       }
 *     ]
 *   }
 *
 *
 * @apiSuccessExample {json} Success-Response
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "user": {
 *           "user_id": 10,
 *           "username": "bless",
 *           "iconurl": "https://static-test.missevan.com/avatar/icon01.png",
 *           "titles": [
 *             // ...
 *           ]
 *         },
 *         "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *           "type": "message", // 气泡类型，聊天气泡 message
 *           "image_url": "https://static.missevan.com/live/bubble/image/001.png",
 *           "frame_url": "https://static.missevan.com/live/bubble/frame/001.png",
 *           "text_color": "#F0F0F0"
 *         },
 *         "balance": {
 *           "balance": 11479968,
 *           "live_noble_balance": 283748,
 *           "live_noble_balance_status": 1
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 收礼直播间内消息
 *     {
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/avatar/icon01.png",
 *         "titles": [
 *           // ...
 *         ]
 *       },
 *       "time": 1576744741101,
 *       "gift": {
 *         "gift_id": 1,
 *         "name": "药丸",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/001.png",
 *         "price": 6,
 *         "num": 1,
 *         "effect_url": "*.001.mp4",
 *         "new_effect_url": "*.001.mp4", // 客户端优先读取此特效，没有则使用 effect_url
 *         "web_effect_url": "*.001.mp4",
 *         "effect_duration": 5000, // 特效时长，单位：毫秒
 *         "effect_options": {
 *           "words": {
 *             "text": "赠言，最多 10 个中文字符",
 *             "image_url": "https://static-test.maoercdn.com/1_2_3_4.png"
 *           },
 *           "avatars": {
 *             "creator_iconurl": "*.png", // 主播头像，DIY 时未勾选则没有
 *             "user_iconurl": "*.png" // 用户头像，DIY 时未勾选则没有
 *           }
 *         },
 *         "notify_duration": 5000 // 通知时长，单位：毫秒
 *       },
 *       "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *         "type": "message", // 气泡类型，聊天气泡 message
 *         "image_url": "https://static.missevan.com/live/bubble/image/001.png",
 *         "frame_url": "https://static.missevan.com/live/bubble/frame/001.png",
 *         "text_color": "#F0F0F0"
 *       },
 *       "current_revenue": 612 // 当前用户在当前直播间本场榜的贡献值
 *     }
 *
 * @apiSuccessExample {json} 全站飘屏
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         ... // 同 websocket 消息中的 user
 *       },
 *       "gift": {
 *         ... // 同 websocket 消息中的 gift
 *       },
 *       "notify_bubble": { // 飘屏气泡
 *         "type": "custom",
 *         "image_url": "https://example.com/b128_0_10_0_100.png", // 客户端使用
 *         "float": 1, // 如果 float 为 1, 则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *         "shine": 0 // 是否闪光，0 为不闪光，1 为闪光，字段不存在则默认不闪光
 *       },
 *       "message": "<b>bless</b> 给 <b>绵绵思远道い</b> 送出 <b>1 个更贵的城堡</b>，快来围观吧~"
 *     }
 *
 */
