package chatroom

import (
	"fmt"
	"html"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/box"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const buySuperFanNotifyBubbleID = 50

type superFanRankParam struct {
	c        *handler.Context
	roomID   int64
	user     *user.User
	p        int64
	pageSize int64
	superFanRankResp
}

type superFanRankResp struct {
	Data       []*fansRankElem   `json:"data"`
	Pagination goutil.Pagination `json:"pagination"`
	MyMedal    *mySuperMedal     `json:"my_medal,omitempty"`
}

type mySuperMedal struct {
	*livemedal.LiveMedal
	RankUp int64 `json:"rank_up"`
	Rank   int64 `json:"rank"`
}

// ActionSuperFanRank 直播间超粉榜
/**
 * @api {get} /api/v2/chatroom/superfan/rank 直播间超粉榜
 * @apiDescription 获取直播间超粉榜单，用户设置隐身时，主播端和用户自己可以看到头像和隐身信息，其他用户显示为占位图
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "my_medal": { // 仅第一页返回
 *         "rank": 1, // 排行
 *         "rank_up": 0,  // 排行上升所需亲密度
 *         "room_id": 123, // 房间号
 *         "creator_id": 12,
 *         "name": "勋章名称",
 *         "status": 2, // 勋章状态 0: 未获得，1: 已获得，2: 佩戴中
 *         "point": 1000, // 亲密度
 *         "level": 6, // 等级
 *         "level_up_point": 1500, // 等级上升所需亲密度
 *         "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *           "expire_time": 1576116700 // 秒级时间戳
 *         },
 *         "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 用户佩戴的定制粉丝徽章
 *         "user_id": 1234,
 *         "username": "用户名",
 *         "iconurl": "http://static.example.com/avatars/icon01.png",
 *         "today_threshold": 500, // 今日上限
 *         "today_point": 200, // 今日获取的亲密度
 *         "rank_invisible": false // 是否榜单隐身
 *       },
 *       "data": [
 *         {
 *           "rank": 1,
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "status": 2,
 *           "point": 1000,
 *           "level": 5,
 *           "level_up_point": 1000,
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700 // 秒级时间戳
 *           },
 *           "user_id": 12345,
 *           "username": "用户名",
 *           "iconurl": "http://static.example.com/avatars/icon01.png",
 *           "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 列表展示的定制粉丝徽章
 *           "titles":[{
 *             "type": "level",
 *             "level": 120,
 *             "icon_url": "https://static-test.maoercdn.com/live/userlevels/level120.webp" // 等级勋章
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4
 *             "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 用户佩戴的定制粉丝徽章
 *             "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *               "expire_time": 1576116700 // 秒级时间戳
 *             }
 *           }],
 *           "rank_invisible": false // 榜单没隐身
 *         }, {
 *           "rank": 2,
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "status": 2,
 *           "point": 1000,
 *           "level": 5,
 *           "level_up_point": 1000,
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700 // 秒级时间戳
 *           },
 *           "user_id": 1234,
 *           "username": "勋章持有者和房主不修改其自身的状态",
 *           "iconurl": "http://static.example.com/avatars/icon01.png",
 *           "rank_invisible": true // 榜单隐身中
 *         }, {
 *           "rank": 3,
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "status": 2,
 *           "point": 1000,
 *           "level": 5,
 *           "level_up_point": 1000,
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700 // 秒级时间戳
 *           },
 *           "user_id": 0,
 *           "username": "神秘人",
 *           "iconurl": "http://static.example.com/avatars/inbisible.png",
 *           "rank_invisible": true // 榜单隐身中
 *         }
 *       ],
 *       "pagination": {
 *         "count": 100,
 *         "maxpage": 5,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSuperFanRank(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newSuperFanRankParam(c)
	if err != nil {
		return nil, err
	}
	if ok, _ := room.HaveMedal(param.roomID); !ok {
		param.Pagination = goutil.MakePagination(0, param.p, param.pageSize)
		return param.superFanRankResp, nil
	}
	err = param.findRank()
	if err != nil {
		return nil, err
	}
	param.findMyMedal()
	param.checkRankInvisible()
	return param.superFanRankResp, nil
}

func newSuperFanRankParam(c *handler.Context) (*superFanRankParam, error) {
	param := &superFanRankParam{user: c.User(), c: c}
	param.roomID, _ = c.GetParamInt64("room_id")
	if param.roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	var err error
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.Data = make([]*fansRankElem, 0)
	return param, nil
}

func (param *superFanRankParam) findRank() (err error) {
	filter := bson.M{
		"room_id":               param.roomID,
		"status":                bson.M{"$gt": livemedal.StatusPending},
		"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
	}
	count, err := livemedal.CountMedal(filter)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Pagination = goutil.MakePagination(count, param.p, param.pageSize)
	if !param.Pagination.Valid() {
		return
	}
	mongoOpt := param.Pagination.SetFindOptions(nil)
	// 按照亲密度由高到低、获取时间先后排序
	mongoOpt.SetSort(bson.D{{Key: "point", Value: -1}, {Key: "created_time", Value: 1}})
	medals, err := livemedal.FindSimples(filter, mongoOpt, true, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Data = make([]*fansRankElem, len(medals))
	userIDs := make([]int64, len(medals))
	for i := range param.Data {
		param.Data[i] = &fansRankElem{Simple: medals[i]}
		param.Data[i].Rank = int64(i) + 1 + param.Pagination.Offset()
		userIDs[i] = medals[i].UserID
	}
	users, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(
		bson.M{"user_id": bson.M{"$in": userIDs}}, &liveuser.FindOptions{FindTitles: true, RoomID: param.roomID},
		options.Find().SetProjection(liveuser.ProjectionTitles)))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	invisibleUsers := userstatus.RankInvisibleUsers(param.roomID)
	for i := range param.Data {
		if u := users[param.Data[i].UserID]; u != nil {
			param.Data[i].Titles = u.Titles
		}
		_, param.Data[i].RankInvisible = invisibleUsers[param.Data[i].UserID]
	}
	return
}

func (param *superFanRankParam) findMyMedal() {
	if param.user == nil || param.p != 1 {
		return
	}
	param.MyMedal = new(mySuperMedal)
	var err error
	filter := bson.M{
		"user_id":               param.user.ID,
		"room_id":               param.roomID,
		"status":                bson.M{"$gt": livemedal.StatusPending},
		"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
	}
	param.MyMedal.LiveMedal, err = livemedal.FindOne(filter, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	if param.MyMedal.LiveMedal == nil {
		param.MyMedal.LiveMedal = new(livemedal.LiveMedal)
		param.MyMedal.UserID = param.user.ID
		param.MyMedal.Username = param.user.Username
		param.MyMedal.IconURL = param.user.IconURL
		param.MyMedal.Status = livemedal.StatusPending // 认为是一个未获取的状态
		param.MyMedal.RankInvisible = userstatus.IsRankInvisible(param.user.ID, param.roomID, true)
		return
	}
	param.MyMedal.RankInvisible = userstatus.IsRankInvisible(param.user.ID, param.roomID, true)
	// 用户在榜内，同步榜单隐身状态后返回
	for i := 0; i < len(param.Data); i++ {
		if param.Data[i].UserID == param.user.ID {
			param.Data[i].RankInvisible = param.MyMedal.RankInvisible
			break
		}
	}
	greaterFilter := bson.M{
		"room_id":               param.roomID,
		"status":                bson.M{"$gt": livemedal.StatusPending},
		"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
		"$or": bson.A{
			bson.M{
				"point": bson.M{"$gt": param.MyMedal.Point},
			},
			bson.M{
				"point":        bson.M{"$eq": param.MyMedal.Point},
				"created_time": bson.M{"$lt": param.MyMedal.CreatedTime}, // 亲密度相同的，早获取勋章的在前
			},
		},
	}
	count, err := livemedal.CountMedal(greaterFilter)
	if err != nil {
		logger.Error(err)
		return
	}
	// 榜一
	if count == 0 {
		param.MyMedal.Rank = 1
		return
	}

	greaterMedal, err := livemedal.FindOne(greaterFilter, options.FindOne().SetSort(
		bson.D{{Key: "point", Value: 1}, {Key: "created_time", Value: -1}}))
	if err != nil {
		logger.Error(err)
		return
	}
	if greaterMedal == nil {
		return
	}
	param.MyMedal.Rank = count + int64(1)
	param.MyMedal.RankUp = greaterMedal.Point - param.MyMedal.Point + 1
}

func (param *superFanRankParam) checkRankInvisible() {
	if len(param.Data) == 0 {
		return
	}
	creatorID := param.Data[0].CreatorID
	var userID int64
	if param.user != nil {
		userID = param.user.ID
	}
	checkRankInvisible(creatorID, userID, false, nil, nil, param.Data)
}

type superFanIntroResp struct {
	CreatorID       int64                     `json:"creator_id"`
	CreatorUsername string                    `json:"creator_username"`
	CreatorIconURL  string                    `json:"creator_iconurl"`
	SuperFan        *livemedal.SuperFan       `json:"super_fan,omitempty"`
	SuperFanCount   int64                     `json:"super_fan_count"`
	SuperPrivilege  []*livemedal.FanPrivilege `json:"super_privilege"`
	GoodsList       []utils.Goods             `json:"goods_list"`
	Rule            string                    `json:"rule"`
	ClosedMessage   string                    `json:"closed_message,omitempty"`
}

// ActionSuperFanIntro 直播间超粉支付页介绍
/**
 * @api {get} /api/v2/chatroom/superfan/intro 直播间超粉支付页介绍
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room_id": 13,
 *       "creator_id": 13,
 *       "creator_username": "test13",
 *       "creator_iconurl": "http://aaa.bbb.ccc/test.png",
 *       "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段
 *         "expire_time": 1576116700, // 过期时间，秒级时间戳
 *         "register_time": 1576116700, // 开通时间，秒级时间戳
 *         "days": 5 // 开通天数
 *       },
 *       "super_fan_count": 100, // 超粉数量
 *       "super_privilege": [ // 超粉特权
 *         {
 *           "iconurl": "http://static.example.com/privilege/000.png",
 *           "name": "超粉勋章",
 *           "description": "超粉专属特别勋章"
 *         }
 *       ],
 *       "goods_list": [
 *         {
 *           "id": 1,
 *           "num": 1,
 *           "price": 8500, // 总价
 *           "title": "1 个月",
 *           "icon_url": "http://static.example.com/icon_url.png"
 *         },
 *         {
 *           "id": 2,
 *           "num": 3,
 *           "price": 8500, // 总价
 *           "title": "3 个月",
 *           "description": "折合 2833 钻 / 月" // 如果商品没有描述，该字段不会返回
 *         }
 *       ]
 *       "rule": "http://aaa.bbb.ccc/test", // 超粉规则页
 *       "closed_message": "05.20 00:00 开放购买" // 返回该字段时需要展示相关信息，且不能购买
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSuperFanIntro(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	resp := &superFanIntroResp{
		CreatorID:       r.CreatorID,
		CreatorUsername: r.CreatorUsername,
		CreatorIconURL:  r.CreatorIconURL,
	}
	lm, err := livemedal.FindOne(bson.M{"room_id": roomID, "user_id": c.UserID()}, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if lm != nil {
		if lm.SuperFan != nil {
			data, err := livetxnorder.FindLatestSuperFanRegisterOrder(c.UserID(), lm.CreatorID)
			if err != nil {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			if data != nil {
				lm.BuildSuperFan(data.CreateTime)
			}
		}
		resp.SuperFan = lm.SuperFan
	}
	resp.SuperFanCount, err = livemedal.CountRoomSuperMedal(roomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.SuperPrivilege = livemedal.SuperPrivilege(true)

	gs, err := roomListSuperFan(r.RoomID, r.CreatorID, c.UserID())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.GoodsList = utils.LiveGoodsToGoods(gs)
	resp.Rule = config.Conf.Params.MedalParams.SuperFanRule
	return resp, nil
}

// roomListSuperFan
func roomListSuperFan(roomID, creatorID, userID int64) ([]livegoods.LiveGoods, error) {
	gs, err := livegoods.ListShowingSuperFan()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	showingList := make([]livegoods.LiveGoods, 0, len(gs))
	for i := range gs {
		// NOTICE: 直播间当前仅会存在一个优惠半价超粉，暂时可以将查询放到循环里
		hasBuyQualification, err := hasBuyQualification(roomID, creatorID, userID, gs[i])
		if err != nil {
			logger.WithField("goods_id", gs[i].ID).Error(err)
			continue
		}
		if hasBuyQualification {
			showingList = append(showingList, *gs[i])
		}
	}
	return showingList, nil
}

type superFanBuyParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GoodsID int64 `form:"goods_id" json:"goods_id"`
	Confirm int   `form:"confirm" json:"confirm"`

	c  *handler.Context
	uc mrpc.UserContext

	r                 *room.Room
	uv                *vip.UserVip
	u                 *liveuser.Simple
	bubble            *bubble.Simple
	goods             *livegoods.LiveGoods
	lm                *livemedal.LiveMedal
	buySuperFanResult *buySuperFanResult
	broadcastElems    []*userapi.BroadcastElem
}

type superFanBuyResp struct {
	OK       int                     `json:"ok"`
	User     *liveuser.Simple        `json:"user"`
	Medal    *livemedal.Mini         `json:"medal"`
	SuperFan superFan                `json:"super_fan"`
	Bubble   *bubble.Simple          `json:"bubble,omitempty"`
	Balance  *utils.BalanceAfterSend `json:"balance"`
}

// ActionSuperFanBuy 直播间超粉购买
/**
 * @api {post} /api/v2/chatroom/superfan/buy 直播间超粉购买
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} goods_id 购买的超粉选项 ID
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求不传
 *
 * @apiSuccessExample {json} Success-Response
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 1,
 *         "user": {
 *           "user_id": 10,
 *           "username": "bless",
 *           "iconurl": "https://static-test.missevan.com/profile/123.png",
 *           "titles": [{
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#F45B41"
 *           }, {
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4
 *           }, {
 *             "type": "noble",
 *             "name": "新秀",
 *             "level": 2
 *           }, {
 *             "type": "avatar_frame",
 *             "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *           }, {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }]
 *         },
 *         "medal": { // 用户购买的的勋章，不一定佩戴
 *           "name": "我的力量无人能及",
 *           "level": 999,
 *           "creator_iconurl": "http://static.example.com/avatars/icon01.png",
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700 // 秒级时间戳
 *           },
 *           "frame_url": "http://test.png", // 推荐粉丝勋章
 *         },
 *         "bubble": { // 如果没有特殊气泡，这个字段为 null 或不存在
 *           "type": "message", // 气泡类型，目前支持: 贵族气泡 noble, 聊天气泡 message
 *           "image_url": "https://static.missevan.com/live/bubble/image/001.png",
 *           "frame_url": "https://static.missevan.com/live/bubble/frame/001.png",
 *           "text_color": "#F0F0F0"
 *         },
 *         "balance": { // 需要按照 balance 结构更新所有钻石余额
 *           "balance": 11479968,
 *           "live_noble_balance": 283748,
 *           "live_noble_balance_status": 1
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample 消费确认弹窗:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确定要消耗 <font color=\"#FFFFFF\">2888</font> 钻石开通主播：<font color=\"#FFFFFF\">小蜜蜂</font> 6 个月的超级粉丝吗？"
 *     }
 *   }
 *
 * @apiSuccessExample 续费已满弹窗:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "您的超粉有效期已到达上限（24 个月），继续续费<b>无法延长有效期</b>，确定要继续支付吗？"
 *     }
 *   }
 *
 * @apiSuccessExample 续费剩余生效时长弹窗:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "由于超粉有效期最长为 <b>24</b> 个月，您本次续费 <b>6</b> 个月仅能延长 <b>15</b> 天有效期，确定要继续支付吗？"
 *     }
 *   }
 *
 * @apiSuccessExample {json} 开通/续费超级粉丝房间内通知:
 *   {
 *     "type": "super_fan",
 *     "event": "registration", // 开通: registration; 续费: renewal
 *     "room_id": 65261414,
 *     "user": {
 *       "user_id": 3456961,
 *       "username": "十二半夏",
 *       "iconurl": "https://static-test.missevan.com/avatars/202006/12/65833548b5dc81baef72ab4b50fa1a2f120636.jpg",
 *       "titles": [{
 *         "type": "level",
 *         "level": 61
 *       }, {
 *         "type": "medal",
 *         "name": "测试大王",
 *         "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *           "expire_time": 1576116700 // 秒级时间戳
 *         },
 *         "frame_url": "http://test.png",
 *         "level": 6
 *       }, {
 *         "type": "identity_badge", // 身份铭牌
 *         "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *         "appearance_id": 10001 // 图标 ID
 *       }]
 *     },
 *     "time": 1617175807990, // 毫秒级时间戳
 *     "super_fan": {
 *       "num": 3, // 购买 n 个月
 *       "icon_url": "https://static-test.missevan.com/super-fan.png", // 超粉图标
 *       "contribution": 8500, // 花费钻石, 需要根据该字段更新收益记录
 *       "notify_duration": 7000 // 通知展示的时长
 *     },
 *     "medal": { // 用户在本房间的勋章，不一定佩戴，需要在开通消息里展示
 *       "name": "我的力量无人能及",
 *       "level": 999,
 *       "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *         "expire_time": 1576116700 // 秒级时间戳
 *       },
 *       "frame_url": "http://test.png",
 *     },
 *     "current_revenue": 1015 // 客户端计算本场榜使用
 *   }
 *
 * @apiSuccessExample {json} 开通/续费超级粉丝全站飘屏
 *     {
 *       "type": "notify",
 *       "notify_type": "super_fan",
 *       "event": "registration", // 开通: registration; 续费: renewal
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 3456961,
 *         "username": "十二半夏",
 *         "iconurl": "https://static-test.missevan.com/avatars/202006/12/65833548b5dc81baef72ab4b50fa1a2f120636.jpg",
 *         "titles": [{
 *           "type": "level",
 *           "level": 61
 *         }, {
 *           "type": "medal",
 *           "name": "测试大王",
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700 // 秒级时间戳
 *           },
 *           "level": 6
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "notify_bubble": { // 超粉特有气泡
 *         "type": "custom",
 *         "image_url": "https://example.com/b128_0_10_0_100.png"
 *       },
 *       "super_fan": {
 *         "num": 12, // 购买的月份
 *         "icon_url": "https://static-test.missevan.com/super-fan.png", // 超粉图标
 *         "contribution": 32000, // 花费钻石
 *         "notify_duration": 7000 // 房间内通知展示的时长
 *       },
 *       "message": "<b>十二半夏</b> 开通 / 续费了主播： <b>一点半夏</b> 的超级粉丝 × 12 个月，亲密永相伴~"
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 100010020
 * @apiError (403) {String} info 需要弹窗进行确认
 *
 */
func ActionSuperFanBuy(c *handler.Context) (handler.ActionResponse, error) {
	param := superFanBuyParam{c: c, uc: c.UserContext()}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if err = param.check(); err != nil {
		return nil, err
	}
	resp, err := param.buySuperFan()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *superFanBuyParam) check() error {
	if p.RoomID <= 0 || p.GoodsID <= 0 {
		return actionerrors.ErrParams
	}
	var err error
	p.r, err = room.Find(p.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if p.r.Limit != nil {
		return actionerrors.NewErrForbidden("本直播间暂不支持本功能")
	}
	if p.r.Medal == nil {
		return actionerrors.NewErrForbidden("当前直播间未开通粉丝勋章")
	}
	if p.r.CreatorID == p.c.UserID() {
		return actionerrors.NewErrForbidden("不能购买自己直播间超级粉丝")
	}
	// 被主播拉黑，无法开通或续费直播间的超粉
	blocked, err := blocklist.IsBlocked(p.r.CreatorID, p.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
	}
	p.goods, err = livegoods.FindShowingGoods(p.GoodsID, livegoods.GoodsTypeSuperFan)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.goods == nil || !p.goods.IsValidShowTime() {
		return actionerrors.ErrGoodsNotFound
	}

	// 检查是否符合购买奖励条件
	hasBuyQualification, err := hasBuyQualification(p.r.RoomID, p.r.CreatorID, p.c.UserID(), p.goods)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !hasBuyQualification {
		return actionerrors.ErrGoodsNotFound
	}

	p.goods.SellerID = p.r.CreatorID
	p.goods.TxnOrderTitle = p.r.CreatorUsername
	if err = p.checkConfirm(); err != nil {
		return err
	}
	p.u, err = liveuser.FindOneSimple(bson.M{"user_id": p.c.UserID()}, &liveuser.FindOptions{FindTitles: true, RoomID: p.r.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.u == nil {
		return actionerrors.ErrCannotFindUser
	}
	_, uv, err := userstatus.UserGeneral(p.c.UserID(), p.c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		p.uv = uv
	}

	p.bubble, err = userappearance.FindMessageBubble(p.u.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return nil
}

func (p *superFanBuyParam) checkConfirm() error {
	now := goutil.TimeNow()
	var lastExpireTimeStamp int64
	err := livetxnorder.LiveTxnOrder{}.DB().Select("IFNULL(MAX(expire_time), 0) AS max_expire_time").
		Where("buyer_id = ? AND seller_id = ?", p.c.UserID(), p.goods.SellerID).
		Where("goods_type = ?", p.goods.Type).
		Where("status = ? AND expire_time > ?", livetxnorder.StatusSuccess, now.Unix()).
		Row().Scan(&lastExpireTimeStamp)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	p.goods.Attr = livegoods.AttrSuperFanRegister
	if lastExpireTimeStamp != 0 {
		p.goods.Attr = livegoods.AttrSuperFanRenew
	}
	if p.Confirm != 0 {
		return nil
	}
	newExpireTime, isExceedMax := calculateSuperFanNewExpireTime(lastExpireTimeStamp, p.goods.Num)
	if !isExceedMax {
		return actionerrors.ErrConfirmRequired(fmt.Sprintf(`确定要消耗 <font color="#AC3D3D">%d</font> 钻石%s主播：<font color="#AC3D3D">%s</font> %d 个月的超级粉丝吗？`,
			p.goods.GoodsTotalPrice(),
			p.goods.GoodsAttrStr(), html.EscapeString(p.r.CreatorUsername), p.goods.GoodsNum()), 1, true)
	}
	subDay := int64(newExpireTime.Sub(time.Unix(lastExpireTimeStamp, 0)).Hours()) / 24
	if subDay == 0 {
		return actionerrors.ErrConfirmRequired(`您的超粉有效期已到达上限（24 个月），继续续费<font color="#AC3D3D">无法延长有效期</font>，确定要继续支付吗？`, 1, true)
	}
	return actionerrors.ErrConfirmRequired(fmt.Sprintf(`由于超粉有效期最长为 24 个月，您本次续费 %d 个月仅能延长 <font color="#AC3D3D">%d</font> 天有效期，确定要继续支付吗？`,
		p.goods.Num, subDay), 1, true)
}

func (p *superFanBuyParam) buySuperFan() (*superFanBuyResp, error) {
	var err error
	p.buySuperFanResult, err = buySuperFan(p.uc, p.c.UserID(), p.goods, p.r, p.bubble, nil)
	if err != nil {
		return nil, err
	}
	p.lm, err = p.updateMedal()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p.lm == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	p.checkUserMedalTitle()
	goutil.Go(func() {
		livemedal.DelUsersTitlesCache(p.u.UserID())
		p.addRevenueRank()
		p.addUserContribution()
		p.buildNotifyRoom()
		p.buildNotifyAll()
		p.broadcast()

		p.addCatFoods()
		p.addActivity()
		p.addLiveShow()
		p.addLiveSpend()
		p.addRoomPaidUser()

		// 解锁超粉表情包
		p.tryUnlockSuperFansSticker()
	})
	resp := &superFanBuyResp{
		OK:       1,
		User:     p.u,
		Bubble:   p.bubble,
		Medal:    &p.lm.Mini,
		SuperFan: newSuperFan(p.goods),
		Balance: &utils.BalanceAfterSend{
			Balance:                p.buySuperFanResult.Balance,
			LiveNobleBalance:       p.buySuperFanResult.LiveNobleBalance,
			LiveNobleBalanceStatus: util.BoolToInt(p.uv != nil),
		},
	}
	return resp, nil
}

func (p *superFanBuyParam) updateMedal() (*livemedal.LiveMedal, error) {
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    p.r.OID,
		RoomID:     p.r.RoomID,
		CreatorID:  p.r.CreatorID,
		UserID:     p.u.UserID(),
		UV:         p.uv,
		MedalName:  p.r.Medal.Name,
		Type:       livemedal.TypeSuperFanMedalPoint,
		Source:     livemedal.ChangeSourceBuySuperFan,
		From:       livemedal.FromBuySuperFan,
		PointAdd:   int64(p.goods.GoodsTotalPrice()),
		ExpireTime: p.buySuperFanResult.NewExpireTime.Unix(),
		Scene:      livemedalpointlog.SceneTypeSuperFan,
		IsRoomOpen: p.r.IsOpen(),
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		return nil, err
	}
	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             p.u,
		BubblePtr:        &p.bubble,
		CreatorUsername:  p.r.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		p.broadcastElems = append(p.broadcastElems, notify)
	}
	return medalUpdatedInfo.After, err
}

func (p *superFanBuyParam) addRevenueRank() {
	score := int64(p.goods.GoodsTotalPrice())
	if score == 0 {
		return
	}

	err := roomsrank.AddRevenue(p.r.RoomID, p.u.UserID(), score, goutil.IntToBool(p.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(p.r.CreatorID, p.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, p.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddSuperRevenue(p.u.UserID(), p.r.OID, p.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	if err = p.r.AddSuperFanRevenue(int64(p.goods.GoodsTotalPrice())); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *superFanBuyParam) addUserContribution() {
	pointAdd := int64(p.goods.Price) * 10 // 1 钻石 = 10 经验
	if p.uv != nil {
		pointAdd = p.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(p.u.UserID(), p.r.RoomID, p.r.CreatorUsername, userstatus.FromNormal, p.uv)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

// superFan 超粉购买信息
type superFan struct {
	Num            int    `json:"num"`
	IconURL        string `json:"icon_url"`
	Contribution   int    `json:"contribution"`
	NotifyDuration int64  `json:"notify_duration"`
}

// 购买超粉信息
func newSuperFan(g *livegoods.LiveGoods) superFan {
	return superFan{
		Num:            g.GoodsNum(),
		IconURL:        g.GoodsIconURL(),
		Contribution:   g.GoodsTotalPrice(),
		NotifyDuration: g.GoodsNotifyDuration(),
	}
}

// notifyAllPayload 超粉购买飘屏信息
type notifyAllPayload struct {
	Type         string           `json:"type"`
	NotifyType   string           `json:"notify_type"`
	Event        string           `json:"event"`
	RoomID       int64            `json:"room_id"`
	User         *liveuser.Simple `json:"user"`
	SuperFan     superFan         `json:"super_fan"`
	NotifyBubble *bubble.Simple   `json:"notify_bubble,omitempty"`
	Message      string           `json:"message"`
}

// notifyRoomPayload 开通超粉房间内通知
type notifyRoomPayload struct {
	Type           string               `json:"type"`
	Event          string               `json:"event"`
	RoomID         int64                `json:"room_id"`
	User           *liveuser.Simple     `json:"user"`
	Bubble         *bubble.Simple       `json:"bubble,omitempty"`
	Time           goutil.TimeUnixMilli `json:"time"`
	SuperFan       superFan             `json:"super_fan"`
	Medal          livemedal.Mini       `json:"medal"`
	CurrentRevenue int64                `json:"current_revenue"`
}

func (p *superFanBuyParam) buildNotifyRoom() {
	now := goutil.TimeNow()
	notify := notifyRoomPayload{
		Type:           liveim.TypeSuperFan,
		RoomID:         p.r.RoomID,
		User:           p.u,
		Bubble:         p.bubble,
		Time:           goutil.NewTimeUnixMilli(now),
		SuperFan:       newSuperFan(p.goods),
		Medal:          p.lm.Mini,
		CurrentRevenue: roomsrank.CurrentRevenue(p.r.RoomID, p.c.UserID()),
	}
	notify.Event = liveim.EventSuperFanRegistration
	if p.goods.Attr == livegoods.AttrSuperFanRenew {
		notify.Event = liveim.EventSuperFanRenewal
	}
	p.broadcastElems = append(p.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  p.r.RoomID,
		Payload: notify,
	})
}

// 检查用户 titles, 确保开通后消息中勋章等级和昵称是最新的
func (p *superFanBuyParam) checkUserMedalTitle() {
	var hasMedalTitle bool
	var hasUsernameTitle bool
	for i := range p.u.Titles {
		if p.u.Titles[i].Type == liveuser.TitleTypeMedal {
			hasMedalTitle = true
			if p.u.Titles[i].Name == p.lm.Name {
				p.u.Titles[i] = liveuser.MedalTitle(&p.lm.Mini)
			}
		}
		if p.u.Titles[i].Type == liveuser.TitleTypeUsername {
			hasUsernameTitle = true
		}
	}
	if !hasMedalTitle && p.lm.Status == livemedal.StatusShow {
		p.u.Titles = append(p.u.Titles, liveuser.MedalTitle(&p.lm.Mini))
	}
	if !hasUsernameTitle {
		p.u.Titles = append(p.u.Titles, liveuser.UsernameTitle(livemedal.SuperFanUsernameColor()))
	}
}

func (p *superFanBuyParam) buildNotifyAll() {
	// 用户一次开通/续费小于 12 个月不发送飘屏
	if p.goods.GoodsNum() < 12 {
		return
	}
	payload := notifyAllPayload{
		Type:       liveim.TypeNotify,
		NotifyType: liveim.TypeSuperFan,
		RoomID:     p.r.RoomID,
		User:       p.u,
		SuperFan:   newSuperFan(p.goods),
	}
	payload.Event = liveim.EventSuperFanRegistration
	if p.goods.Attr == livegoods.AttrSuperFanRenew {
		payload.Event = liveim.EventSuperFanRenewal
	}
	payload.Message = fmt.Sprintf(`<font color="#FFFFFF">%s</font><font color="#CD3B4A">%s了主播：</font>`+
		`<font color="#FFFFFF">%s</font><font color="#CD3B4A">的超级粉丝 ×</font> <font color="#FFFFFF">%d</font> <font color="#CD3B4A">个月，亲密永相伴~</font>`,
		p.u.Username, p.goods.GoodsAttrStr(),
		p.r.CreatorUsername, p.goods.Num)
	b, err := bubble.FindOne(buySuperFanNotifyBubbleID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if b != nil {
		payload.NotifyBubble = b.NewSimple()
	}
	p.broadcastElems = append(p.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  p.r.RoomID,
		Payload: payload,
	})
}

func (p *superFanBuyParam) addCatFoods() {
	// 续费超粉不发猫罐头
	if p.goods.Attr == livegoods.AttrSuperFanRenew {
		return
	}

	now := goutil.TimeNow()
	// 对于开通超粉前获取的有效期内（通常为一天）的猫粮，需延长有效期到当周周日
	endTime := util.BeginningOfWeek(now.Add(7 * 24 * time.Hour))
	err := useritems.SetUserGiftEndTime(p.u.UID, useritems.GiftIDCatFood, endTime)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 购买超粉成功后领取猫粮
	// REVIEW: 存在超粉刚刚购买, 读库存在延迟的情况, 里面查询的时候可能还查不出存在勋章的时候
	ok, err := useritems.AddCatFoods(p.u.UID)
	if err != nil {
		logger.Error(err)
		return
	}

	// 如果今天领取过猫罐头，则补发 10 份猫罐头
	if !ok {
		err = useritems.AddCatCanFood(p.u.UID, 10, now.Unix(), endTime.Unix())
		if err != nil {
			logger.Error(err)
			return
		}
	}
}

func (p *superFanBuyParam) addActivity() {
	syncType := userapi.SyncTypeRegisterSuperFan
	if p.goods.Attr != livegoods.AttrSuperFanRegister {
		syncType = userapi.SyncTypeRenewalSuperFan
	}

	r := rank.NewSyncParam(p.r.RoomID, p.u.UID, p.r.CreatorID).
		SetTransaction(p.buySuperFanResult.BalanceResp).
		SetOpenLogID(p.r.Status.OpenLogID).
		SetGuildID(p.r.GuildID).
		SetActivityCatalogID(p.r.ActivityCatalogID).
		SetGoods(p.goods)
	r.AddRankPoint()
	r.SendLiveActivity(p.uc, syncType)

	if p.goods.Attr == livegoods.AttrSuperFanRegister {
		box.SendQuestMessage(p.RoomID, p.r.CreatorID, box.QuestTypeSuperFan, 0)
	}
}

// broadcast 发送 im 消息
func (p *superFanBuyParam) broadcast() {
	err := userapi.BroadcastMany(p.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (p *superFanBuyParam) addLiveSpend() {
	utils.SendLiveSpend(p.u.UID, p.buySuperFanResult.Price, p.RoomID)
}

func (p *superFanBuyParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(p.RoomID, p.u.UserID(), p.r.CreatorID).
		SetSuperFan(
			int64(p.goods.GoodsTotalPrice()),
			p.goods.Attr == livegoods.AttrSuperFanRegister, // 开通后过期了重开也算是首次开通
		).
		Sync()
}

func (p *superFanBuyParam) tryUnlockSuperFansSticker() {
	param, err := params.FindSticker()
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	now := goutil.TimeNow()
	if !param.IsValidSuperFansUnlockTime(now) {
		// 获取超粉表情包时间未到或已过期
		return
	}

	mc, err := livemedal.FindRoomMedalFansAndSuperFansCount(p.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if mc.SuperFansNum < param.SuperFans.UnlockTriggerNum {
		return
	}

	pkg, err := livesticker.FindPackage(param.SuperFans.PackageID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	isOwner, err := pkg.IsOwner(p.RoomID, 0, now)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if isOwner {
		return
	}
	err = livesticker.DB().Create(&livesticker.PackageOwner{
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		PackageID:    pkg.ID,
		RoomID:       p.RoomID,
		UserID:       0, // NOTICE: 超粉表情包只分配给直播间，不分配给用户
		StartTime:    now.Unix(),
		ExpireTime:   pkg.ExpireTime,
	}).Error
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
}

func (p *superFanBuyParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(p.r.RoomID, p.c.UserID(), p.r.Status.OpenTime)
}
