package gashapon

import (
	"encoding/json"
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestGashaponGachaTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(gachaParam{}, "room_id", "goods_id")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(gachaParam{}, "room_id", "goods_id")
	kc.Check(gachaResp{}, "user", "bubble", "balance", "message_prefix", "open_url",
		"gifts", "gashapon_num", "grand_gift_id")
}

func TestNewGachaParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testFromBlockUserID = int64(1)
		testToBlockUserID   = int64(12)
	)
	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			if input.(map[string]any)["user_id"].(int64) == testFromBlockUserID {
				return handler.M{"block_list": []int64{testToBlockUserID}}, nil
			}
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	require.NoError(blocklist.Clear(testFromBlockUserID))
	cleanup = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	testRoom, err := room.Find(22489473)
	require.NoError(err)
	require.NotNil(testRoom)

	newC := func(roomID, goodsID int64, userID ...int64) *handler.Context {
		c := handler.NewTestContext(http.MethodPost, "/gacha", true,
			handler.M{"room_id": roomID, "goods_id": goodsID})
		c.Equip().EquipID = "test"
		if len(userID) != 0 {
			c.User().ID = userID[0]
		}
		return c
	}

	_, err = newGachaParam(newC(0, 1))
	assert.Equal(actionerrors.ErrParams, err, "房间号错误")
	_, err = newGachaParam(newC(1, 0))
	assert.Equal(actionerrors.ErrParams, err, "商品错误")
	_, err = newGachaParam(newC(room.TestNonexistentRoomID, 1))
	assert.Equal(actionerrors.ErrCannotFindRoom, err, "找不到房间")
	_, err = newGachaParam(newC(room.TestLimitedRoomID, 1))
	assert.Equal(actionerrors.NewErrForbidden("本直播间暂不支持本功能"), err, "礼物房")
	_, err = newGachaParam(newC(testRoom.RoomID, 1, testRoom.CreatorID))
	assert.Equal(actionerrors.ErrParamsMsg("主播不能在自己直播间开魔盒哦~"), err)

	_, err = newGachaParam(newC(testRoom.RoomID, 100))
	assert.Equal(actionerrors.ErrNotFound("无法找到指定魔盒"), err, "找不到")

	goodsList, err := livegoods.ListLiveGoods(livegoods.GoodsTypeGashapon)
	require.NoError(err)
	require.NotEmpty(goodsList)
	goods, err := livegoods.Find(goodsList[0].ID, livegoods.GoodsTypeGashapon)
	require.NoError(err)
	require.NotNil(goods)

	goutil.SetTimeNow(func() time.Time { return time.Unix(0, 0) })
	_, err = newGachaParam(newC(testRoom.RoomID, goods.ID))
	assert.Equal(actionerrors.NewErrForbidden("当前时间无法抽奖哦~"), err, "时间不对")
	goutil.SetTimeNow(nil)

	require.NoError(service.LiveDB.Model(&goodsList[0]).Update("more", "").Error)
	_, err = newGachaParam(newC(testRoom.RoomID, goodsList[0].ID))
	assert.Equal(actionerrors.ErrNotFound("无法找到指定魔盒"), err, "more 为空")
	require.NoError(service.LiveDB.Model(&goodsList[0]).
		Update("more", tutil.SprintJSON(livegoods.More{PoolID: -10})).Error)
	_, err = newGachaParam(newC(testRoom.RoomID, goodsList[0].ID))
	assert.Equal(actionerrors.NewErrForbidden("无法购买当前魔盒"), err, "奖池不对")

	require.NoError(service.LiveDB.Model(&goodsList[0]).
		Update("more", goods.More).Error)
	_, err = newGachaParam(newC(testRoom.RoomID, goodsList[0].ID, 98871))
	assert.Equal(actionerrors.ErrCannotFindUser, err, "用户不存在")

	param, err := newGachaParam(newC(testRoom.RoomID, goodsList[0].ID))
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.r)
	assert.NotZero(param.userID)
	assert.NotNil(param.u)
	assert.NotNil(param.goods)
	assert.NotNil(param.more)
	assert.NotNil(param.pool)

	// 测试拉黑
	room, err := room.FindOne(bson.M{"creator_id": testFromBlockUserID})
	require.NoError(err)
	require.NotNil(room)
	c := newC(room.RoomID, goodsList[0].ID)
	c.User().ID = testToBlockUserID
	_, err = newGachaParam(c)
	assert.EqualError(err, "您当前无法在本直播间内进行此操作")
}

func TestGachaParamGacha(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := gachaParam{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 123,
			},
		},
		goods: &livegoods.LiveGoods{
			ID:  1,
			Num: 10,
		},
		pool: &gift.PoolGashapon{
			Type:  gift.PoolTypeGashapon,
			Rates: map[int64]int{998: 1},
		},
		more: &livegoods.More{},
		u:    &liveuser.Simple{UID: 12},
	}
	assert.Equal(actionerrors.ErrNotFound("无法找到礼物"), param.gacha())

	gifts, err := gift.FindAllShowingGifts()
	require.NoError(err)
	require.NotEmpty(gifts)
	param.pool.Rates = map[int64]int{
		gifts[0].GiftID: 1,
		gifts[1].GiftID: 1,
	}
	require.NoError(param.gacha())
	assert.NotZero(param.totalPrice)
	length := len(param.drawResults)
	assert.Equal(param.drawResults[0].GiftID, param.orderMore.Gifts[0].GiftID)
	assert.Equal(param.lgs[0].GiftID, param.orderMore.Gifts[length-1].GiftID)
	assert.Equal(util.NewInt(livetxnorder.OpenStatusClosed), param.orderMore.OpenStatus)
}

func TestGachaParamSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var appResp *userapi.BalanceResp
	var appError error
	cancel := mrpc.SetMock(userapi.URISendGashaponGift, func(interface{}) (interface{}, error) {
		return appResp, appError
	})
	defer cancel()

	testRoom, err := room.Find(22489473)
	require.NoError(err)
	require.NotNil(testRoom)

	// 奖池配置错误
	param := &gachaParam{
		u:           &liveuser.Simple{UID: 12},
		r:           testRoom,
		drawResults: []gift.DrawResult{{GiftID: 1, Gift: &gift.Gift{}, Num: 1}},
		goods:       &livegoods.LiveGoods{TxnOrderTitle: "123"},
		lgs:         make([]*livegifts.LiveGift, 1),
		pool:        &gift.PoolGashapon{},
	}
	param.lgs[0] = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID,
		param.u, nil).SetGift(param.drawResults[0].Gift, 1)
	appError = errors.New("Test")
	_, err = param.send()
	assert.Equal(appError, err, "rpc 错误")

	// 正常情况
	appResp = &userapi.BalanceResp{TransactionID: 1}
	appError = nil
	resp, err := param.send()
	require.NoError(err)
	assert.NotNil(resp)
}

func TestGachaParamAddRevenueRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := gachaParam{
		totalPrice: 1,
		r:          new(room.Room),
		userID:     20201201,
		goods:      &livegoods.LiveGoods{Num: 1},
	}
	param.r.CreatorID = 20201201
	param.r.RoomID = 20201201
	rankPre, err := usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(),
		param.r.CreatorID)
	require.NoError(err)
	assert.NotPanics(func() { param.addRevenueRank() })
	rankAfter, err := usersrank.FindOne(usersrank.TypeDay, goutil.TimeNow(),
		param.r.CreatorID)
	require.NoError(err)
	assert.Equal(rankPre.Revenue+1, rankAfter.Revenue)
}

func TestGachaParamAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoom, err := room.Find(22489473)
	require.NoError(err)
	require.NotNil(testRoom)
	param := gachaParam{
		r:          testRoom,
		totalPrice: 1,
		userID:     20201201,
		drawResults: []gift.DrawResult{
			{Gift: &gift.Gift{Point: 1}, Num: 1},
		},
	}
	p := livemedalstats.AddPointParam{
		RoomOID:   testRoom.OID,
		RoomID:    testRoom.RoomID,
		CreatorID: testRoom.CreatorID,
		MedalName: testRoom.Medal.Name,
		UserID:    param.userID,
		Type:      livemedal.TypeQuestionAddMedalPoint,
		PointAdd:  1,
	}
	medalUpdatedInfo, err := p.AddPoint()
	require.NoError(err)
	require.NotNil(medalUpdatedInfo)
	// 重设今日开始亲密度
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().UpdateOne(ctx, bson.M{"_id": medalUpdatedInfo.After.OID}, bson.M{"$set": bson.M{
		"t_point": medalUpdatedInfo.After.Point,
	}})
	require.NoError(err)

	*param.r = *testRoom
	require.NotNil(param.r.Medal)
	lmPre, err := livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	require.NotNil(lmPre, "数据库不删除应该不会出现问题")

	// 普通
	service.Cache5Min.Flush()
	assert.NotPanics(func() { param.addMedalPoint() })
	lmAfter, err := livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	assert.Equal(lmPre.Point+2, lmAfter.Point)

	// 勋章为空, 提前返回
	param.r.Medal = nil
	assert.NotPanics(func() { param.addMedalPoint() })
	lmAfter, err = livemedal.FindOne(bson.M{"user_id": param.userID, "room_id": param.r.RoomID}, nil)
	require.NoError(err)
	assert.Equal(lmPre.Point+2, lmAfter.Point)
}

func TestGachaParam_addUserRedeemPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := gachaParam{
		userID: 1919810,
		goods: &livegoods.LiveGoods{
			Price: 10,
		},
	}
	var rp shop.UserRedeemPoint
	require.NoError(rp.DB().Delete("", "year = ? AND user_id = ?",
		goutil.TimeNow().Year(), param.userID).Error)
	param.addUserRedeemPoint()
	require.NoError(rp.DB().Take(&rp, "year = ? AND user_id = ?",
		goutil.TimeNow().Year(), param.userID).Error)
	assert.EqualValues(param.goods.Price, rp.Point)
}

func TestGachaParam_addUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(0, 0)
	}) // 防止任务影响测试
	defer goutil.SetTimeNow(nil)
	param := gachaParam{
		userID: 12,
		goods: &livegoods.LiveGoods{
			Price: 10,
		},
		RoomID: 123,
		r: &room.Room{
			Helper: room.Helper{CreatorUsername: "aaa"},
		},
	}
	before, err := liveuser.Find(param.userID)
	require.NoError(err)
	require.NotNil(before)
	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.userID)
	require.NoError(err)
	assert.EqualValues(param.goods.Price*10, after.Contribution-before.Contribution)
	before = after
	param.uv = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.userID)
	require.NoError(err)
	assert.EqualValues(param.goods.Price*10*2, after.Contribution-before.Contribution)
}

func TestGachaParam_AddMultiConnectScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := livemulticonnect.DB().Delete(livemulticonnect.GroupMember{}).Error
	require.NoError(err)

	param := gachaParam{
		r: &room.Room{
			Helper: room.Helper{
				RoomID: 1,
				Status: room.Status{MultiConnect: room.MultiConnectStatusOngoing},
			},
		},
		drawResults: []gift.DrawResult{
			{GiftID: 90001, Num: 10, Gift: &gift.Gift{Price: 10}},
		},
	}
	param.addMultiConnectScore()
	assert.Empty(param.broadcastElems)

	members := []livemulticonnect.GroupMember{
		{RoomID: 1, EndTime: 0, GroupID: 1},
		{RoomID: 2, EndTime: 0, GroupID: 1},
		{RoomID: 3, EndTime: 0, GroupID: 2},
	}
	err = servicedb.BatchInsert(livemulticonnect.DB(), livemulticonnect.GroupMember{}.TableName(), members)
	require.NoError(err)
	param.addMultiConnectScore()
	assert.Len(param.broadcastElems, 2)
}

func TestGachaParamNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(any) (any, error) {
		count++
		return "success", nil
	})
	defer cancel()

	testRoom, err := room.Find(22489473)
	require.NoError(err)
	require.NotNil(testRoom)

	g := &gift.Gift{GiftID: 90001, Price: 10}
	param := gachaParam{
		userID: 12,
		u:      &liveuser.Simple{UID: 12},
		r:      testRoom,
		config: params.Gashapon{
			EnableNewMsgBroadcast: false,
		},
		lgs:         make([]*livegifts.LiveGift, 1),
		goods:       &livegoods.LiveGoods{Num: 1},
		more:        &livegoods.More{PoolID: 1, GashaponName: "超能魔方"},
		pool:        &gift.PoolGashapon{GrandGiftID: g.GiftID},
		drawResults: []gift.DrawResult{{GiftID: g.GiftID, Num: 1, Gift: g}},
	}
	param.lgs[0] = livegifts.NewLiveGifts(param.r.OID, param.r.RoomID,
		param.u, param.bubble).SetGift(g, 1)
	assert.NotPanics(func() { param.notify() })
	assert.Equal(1, count)

	param.broadcastElems = param.broadcastElems[:0]
	_ = mrpc.SetMock(userapi.URIIMBroadcastMany, func(input any) (any, error) {
		count++
		var elems []*userapi.BroadcastElem
		err = json.Unmarshal(input.(json.RawMessage), &elems)
		require.NoError(err)
		require.Equal(2, len(elems))
		payload, ok := elems[0].Payload.(map[string]interface{})
		require.True(ok)
		assert.Equal(liveim.TypeGashapon, payload["type"])
		assert.Equal(liveim.EventGashaponGacha, payload["event"])
		assert.NotNil(payload["gashapon"])
		assert.NotEmpty(payload["gashapon"].(map[string]any)["gifts"])
		return "success", nil
	})
	param.config = params.Gashapon{
		EnableNewMsgBroadcast: true,
	}
	require.NotPanics(func() { param.notify() })
	assert.Equal(2, count)

	t.Run("GiftNotification", func(t *testing.T) {
		now := goutil.TimeNow()
		expTime := now.Add(time.Hour).Unix()
		testUserID := int64(12345)

		ctx, ctxCancel := service.MongoDB.Context()
		defer ctxCancel()

		// 清理测试数据
		_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})
		require.NoError(err)
		defer userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID, "type": appearance.TypeGiftNotification})

		// 插入测试数据
		testAppearance := &userappearance.UserAppearance{
			UserID:       testUserID,
			AppearanceID: 10001,
			Type:         appearance.TypeGiftNotification,
			Status:       userappearance.StatusWorn,
			StartTime:    now.Add(-time.Minute).Unix(),
			ExpireTime:   &expTime,
			TextColorItem: &appearance.TextColorItem{
				Username: "#FF0000",
			},
			TextColor: "#00FF00",
			Frame:     "oss://test/frame.png",
		}
		_, err = userappearance.Collection().InsertOne(ctx, testAppearance)
		require.NoError(err)

		// 设置测试参数
		param := gachaParam{
			userID:      testUserID,
			u:           &liveuser.Simple{UID: testUserID},
			r:           testRoom,
			lgs:         []*livegifts.LiveGift{livegifts.NewLiveGifts(testRoom.OID, testRoom.RoomID, &liveuser.Simple{UID: testUserID}, nil).SetGift(g, 1)},
			goods:       &livegoods.LiveGoods{Num: 1},
			more:        &livegoods.More{PoolID: 1, GashaponName: "超能魔方"},
			pool:        &gift.PoolGashapon{GrandGiftID: g.GiftID},
			drawResults: []gift.DrawResult{{GiftID: g.GiftID, Num: 1, Gift: g}},
			config:      params.Gashapon{EnableNewMsgBroadcast: true},
		}

		cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(input any) (any, error) {
			var elems []*userapi.BroadcastElem
			err := json.Unmarshal(input.(json.RawMessage), &elems)
			require.NoError(err)
			require.Equal(2, len(elems))

			payload, ok := elems[0].Payload.(map[string]interface{})
			require.True(ok)
			assert.Equal(liveim.TypeGashapon, payload["type"])
			assert.Equal(liveim.EventGashaponGacha, payload["event"])

			giftNotification, exists := payload["gift_notification"]
			require.True(exists)
			require.NotNil(giftNotification)

			giftNotifMap := giftNotification.(map[string]interface{})
			assert.Equal(testAppearance.TextColorItem.Username, giftNotifMap["username_color"])
			assert.Equal(testAppearance.TextColor, giftNotifMap["text_color"])
			// 测试 frame_url 已经解析为 http 地址
			assert.Equal(storage.ParseSchemeURL(testAppearance.Frame), giftNotifMap["frame_url"])

			return "success", nil
		})
		defer cancel()

		assert.NotPanics(func() { param.notify() })
	})
}

func TestGachaParam_addGashapon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoom, err := room.Find(22489473)
	require.NoError(err)
	require.NotNil(testRoom)

	param := gachaParam{
		pool: &gift.PoolGashapon{
			PoolID: 1,
		},
		r:     testRoom,
		goods: &livegoods.LiveGoods{Num: 1},
	}
	assert.NotPanics(func() {
		param.addGashapon()
	})
}

func TestGachaParam__addRoomPaidUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID   = int64(9074509)
		testUserID   = int64(1312312)
		testOpenTime = int64(1727409158000)
	)
	key := keys.KeyRoomPaidUser2.Format(testRoomID, testOpenTime)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	ok, err := service.Redis.SIsMember(key, testUserID).Result()
	require.NoError(err)
	assert.False(ok)
	param := gachaParam{
		RoomID: testRoomID,
		userID: testUserID,
		r: &room.Room{
			Helper: room.Helper{
				Status: room.Status{
					OpenTime: testOpenTime,
				},
			},
		},
	}
	param.addRoomPaidUser()
	ok, err = service.Redis.SIsMember(key, testUserID).Result()
	require.NoError(err)
	assert.True(ok)
}

func TestAddGashaponPrizeList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRoom, err := room.Find(22489473)
	require.NoError(err)
	require.NotNil(testRoom)
	testName := goutil.RandomCode(3)

	key := keys.KeyGashaponPrizeList0.Format()
	err = service.Redis.Del(key).Err()
	require.NoError(err)

	param := gachaParam{
		uc: mrpc.UserContext{},
		u: &liveuser.Simple{
			Username: testName,
		},
		pool: &gift.PoolGashapon{
			PoolID: 1,
		},
		r:     testRoom,
		goods: &livegoods.LiveGoods{Num: 1},
	}
	assert.NotPanics(func() {
		param.addGashaponPrizeList()
	})
	rest, err := service.Redis.LRange(key, 0, -1).Result()
	require.NoError(err)
	assert.Empty(rest)

	param.drawResults = []gift.DrawResult{
		{
			GiftID: 10,
			Num:    2,
			Gift: &gift.Gift{
				Price: 20,
			},
		},
	}
	assert.NotPanics(func() {
		param.addGashaponPrizeList()
	})
	rest, err = service.Redis.LRange(key, 0, -1).Result()
	require.NoError(err)
	prize := gashaponPrize{}
	require.NoError(json.Unmarshal([]byte(rest[0]), &prize))
	assert.Equal(testName, prize.Username)
	assert.GreaterOrEqual(gashaponPrizeMaxCount, len(rest))
}
