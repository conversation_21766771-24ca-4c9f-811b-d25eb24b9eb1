package chatroom

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenotice"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type giftWallInfoResp struct {
	ActivatedNum     int64            `json:"activated_num"`
	TotalNum         int64            `json:"total_num"`
	EndTime          int64            `json:"end_time"`
	Rule             string           `json:"rule"`
	Tip              *giftWallInfoTip `json:"tip,omitempty"`
	ActivatedGifts   []*activatedElem `json:"activated_gifts"`
	InactivatedGifts []*activatedGift `json:"inactivated_gifts"`

	roomID int64
	userID int64
	r      *room.Room
	period *giftwall.Period
}

type giftWallInfoTip struct {
	TipID   int64  `json:"tip_id"`
	Message string `json:"message"`
	OpenURL string `json:"open_url"`
}

type activatedElem struct {
	GiftID       int64              `json:"gift_id"`
	GiftName     string             `json:"gift_name"`
	GiftIconURL  string             `json:"gift_icon_url"`
	ActivatedNum int64              `json:"activated_num"`
	Promotion    int                `json:"promotion,omitempty"`
	Top1         *activatedRankElem `json:"top1"`
}

type activatedGift struct {
	GiftID       int64  `json:"gift_id"`
	GiftName     string `json:"gift_name"`
	GiftPrice    int64  `json:"gift_price"`
	GiftIconURL  string `json:"gift_icon_url"`
	TargetGiftID int64  `json:"target_gift_id"`
}

// ActionGiftWallInfo 礼物墙详情页
/**
 * @api {get} /api/v2/chatroom/giftwall/info 礼物墙详情页
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "activated_num": 1, // 已点亮数量
 *       "total_num": 20, // 本期上墙礼物总数
 *       "end_time": 1660104000, // 本期礼物墙结束时间，秒级时间戳
 *       "rule": "https://link.missevan.com/fm/gift-wall-guide",
 *       "tip": {
 *         "tip_id": 1, // 客户端根据 ID 处理用户公告的关闭
 *         "message": "温馨提示",
 *         "open_url" "http://fm.example.com/open?webview=1"
 *       },
 *       "activated_gifts": [ // 返回全部点亮礼物
 *         {
 *           "gift_id": 1, // 上墙礼物 ID
 *           "gift_name": "凤舞九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "activated_num": 1000, // 点亮次数
 *           "promotion": 1, // 达标需要变色, 0 或不下发该字段不算达标
 *           "top1": {
 *             "user_id": 10,
 *             "username": "送礼者",
 *             "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *             "rank_invisible": true // 榜单隐身中
 *           }
 *         }
 *       ],
 *       "inactivated_gifts": [ // 返回全部未点亮的礼物
 *         {
 *           "gift_id": 2, // 上墙礼物 ID
 *           "gift_name": "凤鸣九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "target_gift_id": 3 // 跳转的定位礼物 ID
 *         }
 *       ],
 *       "activated_premium_gifts": [ // 返回甄选点亮礼物，没有甄选礼物不返回该字段
 *         {
 *           "gift_id": 1, // 上墙礼物 ID
 *           "gift_name": "凤舞九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "activated_num": 1000, // 点亮次数
 *           "promotion": 1, // 达标需要变色, 0 或不下发该字段不算达标
 *           "top1": {
 *             "user_id": 10,
 *             "username": "送礼者",
 *             "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *             "rank_invisible": true // 榜单隐身中
 *           }
 *         }
 *       ],
 *       "inactivated_premium_gifts": [ // 返回甄选未点亮的礼物，没有甄选礼物不返回该字段
 *         {
 *           "gift_id": 2, // 上墙礼物 ID
 *           "gift_name": "凤鸣九天",
 *           "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "target_gift_id": 3 // 跳转的定位礼物 ID
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallInfo(c *handler.Context) (handler.ActionResponse, error) {
	resp, err := newGiftWallInfoResp(c)
	if err != nil {
		return nil, err
	}
	if err = resp.buildActivatedInfo(); err != nil {
		return nil, err
	}
	resp.buildTip()
	return resp, nil
}

func newGiftWallInfoResp(c *handler.Context) (*giftWallInfoResp, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	now := goutil.TimeNow()
	period, err := giftwall.CurrentPeriodInfo(now)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if period == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	return &giftWallInfoResp{
		EndTime:  period.EndTime,
		TotalNum: period.TotalNum(),
		Rule:     config.Conf.Params.GiftWall.GuideURL,
		roomID:   roomID,
		userID:   c.UserID(),
		r:        r,
		period:   period,
	}, nil
}

func (resp *giftWallInfoResp) buildActivatedInfo() error {
	records, err := giftwall.ListRecord(bson.M{"room_id": resp.roomID, "_period_id": resp.period.OID},
		options.Find().SetSort(bson.M{"revenue": -1}))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	gsMap, err := gift.FindGiftMapByGiftIDs(resp.period.ShowGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.ActivatedNum = int64(len(records))
	resp.ActivatedGifts = make([]*activatedElem, 0, len(records))
	activeGiftIDs := make([]int64, 0, len(records))
	for i := range records {
		elem := &activatedElem{
			GiftID:       records[i].ShowGiftID,
			ActivatedNum: records[i].ActivatedNum,
			Promotion:    util.BoolToInt(records[i].Revenue >= giftwall.PromotionRevenueThreshold),
		}
		if g := gsMap[elem.GiftID]; g != nil {
			elem.GiftName = g.Name
			elem.GiftIconURL = g.Icon
		}
		resp.ActivatedGifts = append(resp.ActivatedGifts, elem)
		activeGiftIDs = append(activeGiftIDs, elem.GiftID)
	}

	activeUserIDs := make([]int64, 0, len(records))
	if len(activeGiftIDs) >= 0 {
		top1Map, err := giftwall.FindUserGiftsRankTop1(resp.period.OID, resp.roomID, activeGiftIDs)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		for i := range resp.ActivatedGifts {
			top1, ok := top1Map[resp.ActivatedGifts[i].GiftID]
			if !ok {
				continue
			}
			activeUserIDs = append(activeUserIDs, top1.UserID)
			resp.ActivatedGifts[i].Top1 = &activatedRankElem{
				UserID: top1.UserID,
			}
		}
	}
	if len(activeUserIDs) > 0 {
		users, err := mowangskuser.FindSimpleMap(activeUserIDs) // 函数内部已去重
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		invisibleUsers := userstatus.RankInvisibleUsers(resp.roomID)
		icon := service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon)
		for i := range resp.ActivatedGifts {
			if resp.ActivatedGifts[i].Top1 == nil {
				continue
			}
			// 房主和用户自己需要看到全部信息
			if _, ok := invisibleUsers[resp.ActivatedGifts[i].Top1.UserID]; ok {
				resp.ActivatedGifts[i].Top1.RankInvisible = true
				if (resp.userID != resp.ActivatedGifts[i].Top1.UserID) &&
					(resp.userID != resp.r.CreatorID) {
					resp.ActivatedGifts[i].Top1.UserID = 0
					resp.ActivatedGifts[i].Top1.Username = "神秘人"
					resp.ActivatedGifts[i].Top1.IconURL = icon
					continue
				}
			}
			if u := users[resp.ActivatedGifts[i].Top1.UserID]; u != nil {
				resp.ActivatedGifts[i].Top1.Username = u.Username
				resp.ActivatedGifts[i].Top1.IconURL = u.IconURL
			}
		}
	}

	inactivatedGiftIDs := util.DiffInt64(resp.period.ShowGiftIDs, activeGiftIDs)
	resp.InactivatedGifts = make([]*activatedGift, 0, len(inactivatedGiftIDs))
	if len(inactivatedGiftIDs) == 0 {
		return nil
	}
	gs, err := giftwall.FindGiftsByShowGiftIDs(inactivatedGiftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 即使礼物绑定关系被移除，也要显示未点亮的上墙礼物
	giftWallGiftsMap := goutil.ToMap(gs, "ShowGiftID").(map[int64]*giftwall.Gift)
	for _, showGiftID := range resp.period.ShowGiftIDs {
		// 未点亮的礼物需要按照配置的 show_gift_ids 排序
		if !goutil.HasElem(inactivatedGiftIDs, showGiftID) {
			continue
		}
		g := gsMap[showGiftID]
		if g == nil {
			continue
		}
		elem := &activatedGift{
			GiftID:      g.GiftID,
			GiftName:    g.Name,
			GiftPrice:   g.Price,
			GiftIconURL: g.Icon,
		}
		if gw := giftWallGiftsMap[elem.GiftID]; gw != nil {
			elem.TargetGiftID = gw.TargetGiftID
		}
		resp.InactivatedGifts = append(resp.InactivatedGifts, elem)
	}
	return nil
}

func (resp *giftWallInfoResp) buildTip() {
	ln, err := livenotice.FindGiftWallNotice()
	if err != nil {
		logger.Error(err)
		return
	}
	if ln != nil {
		resp.Tip = &giftWallInfoTip{
			TipID:   ln.ID,
			Message: ln.Content,
			OpenURL: ln.OpenURL,
		}
	}
}

type rewardListItem struct {
	GiftID      int64  `json:"gift_id"`
	GiftName    string `json:"gift_name"`
	GiftIconURL string `json:"gift_icon_url"`
	Threshold   int64  `json:"threshold"`
	Received    int    `json:"received,omitempty"` // 完成任务获得奖励，1 为完成任务
}

type rewardListResp struct {
	Rewards []*rewardListItemNew `json:"rewards"`
}

type rewardListItemNew struct {
	Type           int    `json:"type"`       // 奖励类型
	ElementID      int64  `json:"element_id"` // 奖励 ID（直播间专属礼物 ID、外观 ID）
	ElementName    string `json:"element_name"`
	ElementIconURL string `json:"element_icon_url"`
	Threshold      int64  `json:"threshold"`
	Received       int    `json:"received,omitempty"` // 完成任务获得奖励，1 为完成任务
}

// ActionGiftWallReward 礼物墙点亮进度及奖励
/**
 * @api {get} /api/v2/chatroom/giftwall/reward 礼物墙点亮进度及奖励
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "rewards": [
 *         {
 *           "type": 1, // 1：直播间专属礼物奖励；2：直播间外观奖励；3：直播间专属表情
 *           "element_id": 1, // 当 type = 1 时，element_id 表示直播间专属礼物 ID
 *           "element_name": "直播间专属礼物【神明少女 1】",
 *           "element_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "threshold": 5,
 *           "received": 1 // 完成任务获得奖励，下发 1 表示完成任务，不下发表示未完成任务
 *         },
 *         {
 *           "type": 1,
 *           "element_id": 2,
 *           "element_name": "直播间专属礼物【神明少女 2】",
 *           "element_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "threshold": 15,
 *           "received": 1
 *         },
 *         {
 *           "type": 2,
 *           "element_id": 3, // 当 type = 2 时，element_id 表示外观 ID
 *           "element_name": "称号【人气冠军】",
 *           "element_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *           "threshold": 30
 *         },
 *         {
 *           "type": 3,
 *           "element_id": 4, // 当 type = 3 时，element_id 表示表情 ID
 *           "element_name": "直播间专属表情",
 *           "element_icon_url": "http://static.maoercdn.com/sticker/icon01.png",
 *           "threshold": 35
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallReward(c *handler.Context) (handler.ActionResponse, error) {
	roomID, _ := c.GetParamInt64("room_id")
	if roomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	period, err := giftwall.CurrentPeriodInfo(goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	e := c.Equip()
	// WORKAROUND: 安卓 < 6.1.0、iOS < 6.1.0 的版本返回的是旧格式
	isOldApp := e.IsOldApp(goutil.AppVersions{IOS: "6.1.0", Android: "6.1.0"})

	if period == nil {
		if !e.FromApp {
			return handler.M{"reward_gifts": []*rewardListItem{}, "rewards": []*rewardListItemNew{}}, nil
		}
		if isOldApp {
			return handler.M{"reward_gifts": []*rewardListItem{}}, nil
		}
		return rewardListResp{
			Rewards: make([]*rewardListItemNew, 0),
		}, nil
	}

	rewardGiftIDs := make([]int64, 0, len(period.Rewards))
	rewardAppearanceIDs := make([]int64, 0, len(period.Rewards))
	for _, reward := range period.Rewards {
		switch reward.Type {
		case giftwall.RewardTypeCustomGift:
			rewardGiftIDs = append(rewardGiftIDs, reward.ElementID)
		case giftwall.RewardTypeAppearance:
			rewardAppearanceIDs = append(rewardAppearanceIDs, reward.ElementID)
		}
	}

	giftMap := make(map[int64]*gift.Gift)
	if len(rewardGiftIDs) > 0 {
		giftMap, err = gift.FindGiftMapByGiftIDs(rewardGiftIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	appearancesMap := make(map[int64]*appearance.Appearance)
	if len(rewardAppearanceIDs) > 0 {
		appearancesMap, err = appearance.FindMapByIDs(rewardAppearanceIDs)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}

	activatedCount, err := giftwall.ActivatedCountByRoomID(period.OID, roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	rewardGifts := make([]*rewardListItem, 0, len(period.Rewards))
	rewards := make([]*rewardListItemNew, 0, len(period.Rewards))
	for _, reward := range period.Rewards {
		switch reward.Type {
		case giftwall.RewardTypeCustomGift:
			g, ok := giftMap[reward.ElementID]
			if !ok {
				continue
			}
			name := fmt.Sprintf("直播间专属礼物【%s】", g.Name)
			item := &rewardListItem{
				GiftID:      g.GiftID,
				GiftName:    name,
				GiftIconURL: g.Icon,
				Threshold:   reward.Threshold,
				Received:    goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewardGifts = append(rewardGifts, item)
			itemNew := &rewardListItemNew{
				Type:           reward.Type,
				ElementID:      reward.ElementID,
				ElementName:    name,
				ElementIconURL: g.Icon,
				Threshold:      reward.Threshold,
				Received:       goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewards = append(rewards, itemNew)
		case giftwall.RewardTypeAppearance:
			a, ok := appearancesMap[reward.ElementID]
			if !ok {
				continue
			}
			name := fmt.Sprintf("%s【%s】", appearance.TypeName(a.Type), a.Name)
			item := &rewardListItem{
				GiftID:      reward.ElementID, // 兼容前端当作数组成员组件的 key
				GiftName:    name,
				GiftIconURL: storage.ParseSchemeURL(a.Icon),
				Threshold:   reward.Threshold,
				Received:    goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewardGifts = append(rewardGifts, item)
			itemNew := &rewardListItemNew{
				Type:           reward.Type,
				ElementID:      reward.ElementID,
				ElementName:    name,
				ElementIconURL: storage.ParseSchemeURL(a.Icon),
				Threshold:      reward.Threshold,
				Received:       goutil.BoolToInt(activatedCount >= reward.Threshold),
			}
			rewards = append(rewards, itemNew)
		}
	}
	// WORKAROUND: Web 两种数据结构都返回，等前端迁移到 rewards 之后，删除 reward_gifts
	if !e.FromApp {
		return handler.M{"reward_gifts": rewardGifts, "rewards": rewards}, nil
	}
	if isOldApp {
		return handler.M{"reward_gifts": rewardGifts}, nil
	}
	return rewardListResp{
		Rewards: rewards,
	}, nil
}

type giftWallRankResp struct {
	ActivatedGift *activatedGift       `json:"activated_gift"`
	ActivatedNum  int64                `json:"activated_num"`
	Data          []*activatedRankElem `json:"data"`
	MyRank        *activatedRankElem   `json:"my_rank,omitempty"`

	r        *room.Room
	u        *user.User
	period   *giftwall.Period
	showGift *gift.Gift
}

// activatedRankElem 榜单信息
type activatedRankElem struct {
	Rank     int64  `json:"rank"`
	Score    int64  `json:"score"`
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`

	RankUp        int64 `json:"rank_up,omitempty"`
	RankInvisible bool  `json:"rank_invisible,omitempty"`
}

// ActionGiftWallRank 礼物墙用户贡献榜
/**
 * @api {get} /api/v2/chatroom/giftwall/rank 礼物墙用户贡献榜
 * @apiDescription 只返回请求时间对应周期的榜单，跨周期导致的上墙礼物不存在会返回参数错误
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} gift_id 上墙礼物 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "activated_gift": {
 *         "gift_id": 1,
 *         "gift_name": "凤鸣九天",
 *         "gift_price": 100, // 单位: 钻
 *         "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *         "target_gift_id": 3 // 跳转的定位礼物 ID
 *       },
 *       "activated_num": 1, // 被点亮次数
 *       "data": [
 *         {
 *           "rank": 1,
 *           "score": 10,
 *           "user_id": 1234, // 用户 id
 *           "username": "1234",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *         },
 *         {
 *           "rank": 2,
 *           "score": 9,
 *           "user_id": 12345,
 *           "username": "123456",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *         },
 *         {
 *           "rank": 2,
 *           "score": 9,
 *           "user_id": 0,
 *           "username": "神秘人",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "rank_invisible": true // 榜单隐身中
 *         }
 *       ],
 *       "my_rank": { // 未登录不返回该字段
 *         "rank": 2,
 *         "score": 9,
 *         "rank_up": 2, // 上升名次或上榜还需要的数量
 *         "remain_num": 10, //  剩余多少数量到榜首
 *         "user_id": 123456,
 *         "username": "123456",
 *         "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *       },
 *       "sponsor": { // 冠名相关
 *         "label_icon_url": "http://static.maoercdn.com/sponsor_tag_url.png", // 即将冠名角标，没有即将冠名不下发该字段
 *         "detail_image_url": "http://static.maoercdn.com/detail.png" // 冠名详情
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionGiftWallRank(c *handler.Context) (handler.ActionResponse, error) {
	resp, err := newGiftWallRankResp(c)
	if err != nil {
		return nil, err
	}
	if err = resp.buildData(); err != nil {
		return nil, err
	}
	if err = resp.buildMyRank(); err != nil {
		return nil, err
	}
	resp.buildRankInvisible()
	if err = resp.buildActivatedInfo(); err != nil {
		return nil, err
	}
	return resp, nil
}

func newGiftWallRankResp(c *handler.Context) (*giftWallRankResp, error) {
	roomID, _ := c.GetParamInt64("room_id")
	giftID, _ := c.GetParamInt64("gift_id")
	if roomID <= 0 || giftID <= 0 {
		return nil, actionerrors.ErrParams
	}
	now := goutil.TimeNow()
	period, err := giftwall.CurrentPeriodInfo(now)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if period == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	if !goutil.HasElem(period.ShowGiftIDs, giftID) {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": roomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	g, err := gift.FindByGiftID(giftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrCannotFindResource
	}

	return &giftWallRankResp{
		r:        r,
		u:        c.User(),
		period:   period,
		showGift: g,
	}, nil
}

func (resp *giftWallRankResp) buildData() error {
	top5, err := giftwall.FindTop5(resp.period.OID, resp.r.RoomID, resp.showGift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Data = make([]*activatedRankElem, 0, len(top5))
	userIDs := make([]int64, 0, len(top5))
	for i, v := range top5 {
		info := &activatedRankElem{
			Rank:   int64(i + 1),
			Score:  v.ActivatedNum,
			UserID: v.UserID,
		}
		resp.Data = append(resp.Data, info)
		userIDs = append(userIDs, v.UserID)
	}
	users, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(resp.Data); i++ {
		u := users[resp.Data[i].UserID]
		if u == nil {
			continue
		}
		resp.Data[i].IconURL = u.IconURL
		resp.Data[i].Username = u.Username
	}
	return nil
}

func (resp *giftWallRankResp) buildMyRank() error {
	if resp.u == nil || resp.u.ID == 0 || resp.u.ID == resp.r.CreatorID {
		return nil
	}
	resp.MyRank = &activatedRankElem{
		UserID:        resp.u.ID,
		Username:      resp.u.Username,
		IconURL:       resp.u.IconURL,
		RankUp:        1,
		RankInvisible: userstatus.IsRankInvisible(resp.u.ID, resp.r.RoomID, true),
	}

	for i := range resp.Data {
		if resp.Data[i].UserID != resp.u.ID {
			continue
		}
		resp.MyRank.Rank = resp.Data[i].Rank
		resp.MyRank.Score = resp.Data[i].Score
		if i == 0 {
			resp.MyRank.RankUp = 0
		} else {
			resp.MyRank.RankUp = resp.Data[i-1].Score - resp.Data[i].Score + 1
		}
		return nil
	}
	// 非前 5 名
	myRank, err := giftwall.FindUserRank(resp.period.OID, resp.r.RoomID, resp.u.ID, resp.showGift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if myRank == nil {
		if len(resp.Data) >= 5 {
			resp.MyRank.RankUp = resp.Data[4].Score + 1
		}
		return nil
	}

	resp.MyRank.Score = myRank.ActivatedNum
	if len(resp.Data) >= 5 {
		// 榜单满了
		resp.MyRank.RankUp = max(resp.Data[4].Score-myRank.ActivatedNum+1, 1)
	}
	return nil
}

func (resp *giftWallRankResp) buildRankInvisible() {
	invisibleUsers := userstatus.RankInvisibleUsers(resp.r.RoomID)
	icon := service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon)
	for i := 0; i < len(resp.Data); i++ {
		if _, ok := invisibleUsers[resp.Data[i].UserID]; ok {
			resp.Data[i].RankInvisible = true
			// 用户自己和主播需要查看用户隐身前的信息
			if resp.u == nil || (resp.Data[i].UserID != resp.u.ID && resp.r.CreatorID != resp.u.ID) {
				resp.Data[i].UserID = 0
				resp.Data[i].Username = "神秘人"
				resp.Data[i].IconURL = icon
			}
		}
	}
}

func (resp *giftWallRankResp) buildActivatedInfo() error {
	g, err := giftwall.FindOneByShowGiftID(resp.showGift.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return actionerrors.ErrCannotFindResource
	}
	resp.ActivatedGift = &activatedGift{
		GiftID:       resp.showGift.GiftID,
		GiftName:     resp.showGift.Name,
		GiftPrice:    resp.showGift.Price,
		GiftIconURL:  resp.showGift.Icon,
		TargetGiftID: g.TargetGiftID,
	}

	record, err := giftwall.FindOneRecord(bson.M{
		"show_gift_id": resp.showGift.GiftID,
		"room_id":      resp.r.RoomID,
		"_period_id":   resp.period.OID,
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if record == nil {
		return actionerrors.ErrCannotFindResource
	}
	resp.ActivatedNum = record.ActivatedNum
	return nil
}

/**
 * @api {get} /api/v2/chatroom/giftwall/sponsor/list 冠名礼物墙列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "", // 在福袋黑名单的主播请求返回错误信息
 *     "data": {
 *      "premium_gifts": [ // 返回甄选点亮礼物，没有甄选礼物不返回该字段
 *        {
 *          "gift_id": 1, // 上墙礼物 ID
 *          "gift_name": "凤舞九天",
 *          "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *          "user": {
 *            "user_id": 10,
 *            "username": "送礼者",
 *            "iconurl": "http://static.maoercdn.com/avatars/icon01.png"
 *          }
 *        }
 *      ],
 *      "gifts": [ // 返回普通点亮礼物，没有普通点亮礼物不返回该字段
 *        {
 *          "gift_id": 1, // 上墙礼物 ID
 *          "gift_name": "凤舞九天",
 *          "gift_icon_url": "http://static.maoercdn.com/avatars/icon01.png",
 *          "user": {
 *            "user_id": 10,
 *            "username": "送礼者",
 *            "iconurl": "http://static.maoercdn.com/avatars/icon01.png"
 *          }
 *        }
 *      ],
 *      "end_time": 1660104000 // 本周期结束时间，单位秒
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
