package chatroom

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type backpackResp struct {
	Backpack []creatoritems.BackpackItem `json:"backpack"`
}

// ActionCreatorBackpackList 获取主播背包信息
/**
 * @api {get} /api/v2/chatroom/backpack/creator/list 获取主播背包信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "backpack":[{
 *         "type": 1, // 1 说明是礼物
 *         "gift_id": 301, // 礼物 id
 *         "comboable": 1, // 同 meta/data, 该礼物支持连击，不能连击不返回该字段
 *         "price": 0, // 礼物价格
 *         "num": 5000,
 *         "allowed_nums": [], // 配置的送礼数量，不下发时默认的配置数量
 *         // 下面的字段基本都会有
 *         "name": "猫粮",
 *         "icon_url": "https://static.example.com/cat_food.png",
 *         "icon_active_url": "https://static-test.missevan.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *         "intro": "free gift: cat food",
 *         "intro_icon_url": "https://static.expamle.com/intro.png",
 *         "intro_open_url": "https://fm.uat.missevan.com",
 *         "label_icon_url": "https://static-test.missevan.com/live/gifts/icons/label/001.webp",
 *         "time_left": 5000, // 剩余时间，单位：秒
 *       }]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionCreatorBackpackList(c *handler.Context) (handler.ActionResponse, error) {
	backpackItems, err := creatoritems.FindBackpackItems(c.UserID(), c.Equip())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &backpackResp{
		Backpack: backpackItems,
	}, nil
}

type creatorBackpackSendParam struct {
	RoomID  int64 `form:"room_id" json:"room_id"`
	GiftID  int64 `form:"gift_id" json:"gift_id"`
	GiftNum int   `form:"gift_num" json:"gift_num"`

	// TODO: 送礼使用统一结构体继承
	c           *handler.Context
	uc          mrpc.UserContext
	r           *room.Room
	bubble      *bubble.Simple
	g           *gift.Gift
	sendUser    *liveuser.Simple
	sendUserVip *vip.UserVip
	lg          *livegifts.LiveGift
	sendUserID  int64

	broadcastElems []*userapi.BroadcastElem
}

type creatorBackpackSendGiftResp struct {
	Ok      int    `json:"ok"`
	GiftID  int64  `json:"gift_id"`
	Remain  *int64 `json:"remain,omitempty"`
	Message string `json:"message,omitempty"`
}

// ActionCreatorBackpackUse 使用背包中的礼物
/**
 * @api {post} /api/v2/chatroom/backpack/creator/use 使用背包中的礼物
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} gift_num 礼物数量
 *
 * @apiSuccessExample {json} 送礼成功响应
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 1,
 *         "gift_id": 301,
 *         "remain": 10 // 该礼物剩余数量
 *       }
 *     }
 *
 * @apiSuccessExample {json} 送礼数量不足响应
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 0,
 *         "gift_id": 301,
 *         "remain": 10, // 该礼物剩余数量
 *         "message": "礼物数量不足哦~"
 *       }
 *     }
 *
 */
func ActionCreatorBackpackUse(c *handler.Context) (handler.ActionResponse, error) {
	var param creatorBackpackSendParam
	resp, err := param.load(c)
	if err != nil || resp != nil {
		return resp, err
	}
	return param.send()
}

func (param *creatorBackpackSendParam) load(c *handler.Context) (handler.ActionResponse, error) {
	err := c.Bind(param)
	if err != nil ||
		param.RoomID <= 0 || param.GiftID <= 0 || param.GiftNum <= 0 {
		return nil, actionerrors.ErrParams
	}

	param.sendUserID = creatoritems.MaoerWalletUserID
	param.c = c
	param.uc = c.UserContext()
	param.g, err = gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.g == nil {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}
	switch param.g.Type {
	case gift.TypeFree, gift.TypeRebate:
	default:
		return &backpackSendGiftResp{
			Ok:      0,
			GiftID:  param.GiftID,
			Remain:  util.NewInt64(0),
			Message: "遭遇到了意想不到的错误哦~",
		}, nil
	}

	param.r, err = room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if c.UserID() != param.r.CreatorID {
		return nil, actionerrors.ErrParams
	}
	if param.g.IsHotCard() && !param.r.IsOpen() {
		return nil, actionerrors.ErrDisableUseHotCardInCloseRoom
	}

	count, err := creatoritems.CountGiftNum(c.UserID(), param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if count < int64(param.GiftNum) {
		return &backpackSendGiftResp{
			Ok:      0,
			GiftID:  param.GiftID,
			Remain:  util.NewInt64(count),
			Message: "礼物数量不足哦~",
		}, nil
	}

	param.sendUser, err = liveuser.FindOneSimple(bson.M{"user_id": param.sendUserID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.sendUser == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	_, uv, err := userstatus.UserGeneral(param.sendUserID, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 总是获取贵族状态来获取贵族气泡和计算用户的直播间等级经验值
	if uv != nil && uv.IsActive() {
		param.sendUserVip = uv
	}
	param.bubble, err = userappearance.FindMessageBubble(param.sendUserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return nil, nil
}

func (param *creatorBackpackSendParam) send() (*creatorBackpackSendGiftResp, error) {
	sender := creatoritems.GiftSender{
		RoomID:     param.RoomID,
		CreatorID:  param.r.CreatorID,
		SendUserID: param.sendUserID,
		Gift:       param.g,
		Num:        int64(param.GiftNum),
		OpenLogID:  param.r.Status.OpenLogID,
		C:          param.c,
	}
	ok, count, tids, err := sender.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	resp := &creatorBackpackSendGiftResp{
		Ok:      0,
		GiftID:  param.GiftID,
		Remain:  &count,
		Message: "遭遇到了意想不到的错误哦~",
	}

	if err != nil || !ok {
		if count < int64(param.GiftNum) {
			resp.Message = "礼物数量不足哦~"
		}
		return resp, nil
	}

	param.lg = livegifts.
		NewLiveGifts(param.r.OID, param.RoomID, param.sendUser, param.bubble).
		SetGift(param.g, int64(param.GiftNum)).
		SetRoomOpenStatus(param.r.IsOpen()).
		SetTransactionIDs(tids...)
	_, err = livegifts.UpdateSave(param.lg, nil, primitive.NilObjectID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// TODO: 考虑与其他送礼整合到一起
	goutil.Go(func() {
		param.addRevenueRank()
		param.addPK()
		param.addMedalPoint()
		param.activatedGiftWall()
		param.buildIMMessage()
		param.broadcast()

		param.addActivity()
		param.addLiveShow()
	})

	resp.Message = ""
	resp.Ok = 1
	return resp, nil
}

func (param *creatorBackpackSendParam) addRevenueRank() {
	if param.g.Price == 0 {
		return
	}
	score := param.g.Price * int64(param.GiftNum)
	err := roomsrank.AddRevenue(param.r.RoomID, param.sendUserID, score, goutil.IntToBool(param.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	// TODO: 主播背包送免费礼物
	err = liverevenues.AddGiftRevenue(param.sendUserID, param.r.OID, param.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = param.r.ReceiveGift(param.GiftNum, param.g.Price)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *creatorBackpackSendParam) addPK() {
	score, freeScore := param.g.PKScores(param.GiftNum)
	elems, err := livepk.AddPKScore(param.r, param.sendUserID, score, freeScore)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

// NOTICE: 主播背包里送出的礼物不需要发送送礼人（猫耳娘的零钱袋）粉丝勋章获取、升级消息
func (param *creatorBackpackSendParam) addMedalPoint() {
	if param.r.Medal == nil {
		return
	}
	medalPoint := param.g.MedalPoint(param.GiftNum)
	if medalPoint == 0 {
		return
	}
	switch param.g.Type {
	case gift.TypeFree:
		// 免费礼物
		addParam := &livemedalstats.AddFreePointParam{
			RoomID:    param.RoomID,
			CreatorID: param.r.CreatorID,
			UserID:    param.sendUserID,
			PointAdd:  medalPoint,
			Scene:     livemedalpointlog.SceneTypeFreeGift,
		}
		_, err := addParam.AddFreePoint()
		if err != nil {
			logger.Error(err)
			return
		}
	case gift.TypeRebate:
		medalParam := livemedalstats.AddPointParam{
			RoomOID:    param.r.OID,
			RoomID:     param.r.RoomID,
			CreatorID:  param.r.CreatorID,
			UserID:     param.sendUserID,
			UV:         param.sendUserVip,
			MedalName:  param.r.Medal.Name,
			Type:       livemedal.TypeGiftAddMedalPoint,
			Source:     livemedal.ChangeSourceGift,
			PointAdd:   medalPoint,
			Scene:      livemedalpointlog.SceneTypePayGift,
			IsRoomOpen: param.r.IsOpen(),
		}
		_, err := medalParam.AddPoint()
		if err != nil {
			logger.Error(err)
			return
		}
	}
}

func (param *creatorBackpackSendParam) activatedGiftWall() {
	revenue := param.g.Price * int64(param.GiftNum)
	notifyElem, err := giftwall.ActiveGift(param.r, param.sendUserID, param.g.GiftID, revenue, param.GiftNum)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if notifyElem != nil {
		param.broadcastElems = append(param.broadcastElems, notifyElem)
	}
}

func (param *creatorBackpackSendParam) buildIMMessage() {
	// 房间送礼消息
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeNormal,
		RoomID:  param.RoomID,
		Payload: param.lg.RoomMessage(),
	})

	// 全站飘屏
	price := param.g.Price * int64(param.GiftNum)
	if !param.g.AlwaysNotify() && price < gift.ComboNotifyMinPrice {
		// 未设置成总是飘屏和价值不够
		return
	}
	nb := gift.NotifyBuilder{
		RoomID:          param.RoomID,
		CreatorUsername: param.r.CreatorUsername,
		User:            param.sendUser,
		Gift:            param.g,
		GiftNum:         param.GiftNum,
	}
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  param.RoomID,
		Payload: nb.Build(),
	})
}

// broadcast 发送 im 消息
func (param creatorBackpackSendParam) broadcast() {
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *creatorBackpackSendParam) addActivity() {
	r := rank.
		NewSyncParam(param.r.RoomID, param.sendUserID, param.r.CreatorID).
		SetGuildID(param.r.GuildID).
		SetActivityCatalogID(param.r.ActivityCatalogID).
		SetGift(param.g, param.GiftNum)
	r.AddRankPoint()
	r.SendLiveActivity(param.uc)
}

func (param *creatorBackpackSendParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.RoomID, param.sendUserID, param.r.CreatorID).
		SetGift(param.g.GiftID, param.g.Price, param.GiftNum).
		Sync()
}
