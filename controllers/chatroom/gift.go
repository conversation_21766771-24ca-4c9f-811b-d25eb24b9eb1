package chatroom

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/liveluckygiftdrop"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/box"
	"github.com/MiaoSiLa/live-service/models/mongodb/livegifts"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livefansboxtask"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	liveserviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	redismutex "github.com/MiaoSiLa/missevan-go/service/redis/mutex"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionGiftDraw 给直播间抽礼物
/**
 * @api {post} /api/v2/chatroom/gift/draw 给直播间抽礼物
 * @apiDescription 送随机礼物，无连击
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 实际收礼直播间 ID
 * @apiParam {Number} [from_room_id=0] 用户送礼所在的直播间 ID，不传默认用户在实际收礼的直播间内送礼
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} gift_num 礼物数量
 *
 * @apiSuccessExample {json} Success-Response
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "ok": 1,
 *         "user": {
 *           "user_id": 10,
 *           "username": "bless",
 *           "iconurl": "https://static-test.missevan.com/profile/123.png",
 *           "titles": [{
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#F45B41"
 *           }, {
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "medal",
 *             "name": "独角兽",
 *             "level": 4
 *           }, {
 *             "type": "noble",
 *             "name": "新秀",
 *             "level": 2
 *           }, {
 *             "type": "highness",
 *             "name": "上神",
 *             "level": 1
 *           }, {
 *             "type": "avatar_frame",
 *             "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *           }, {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }]
 *         },
 *         "bubble": { // 如果没有特殊气泡，这个字段不存在
 *           "type": "noble", // 气泡类型，当前是贵族气泡
 *           "noble_level": 2 // 使用对应等级的贵族气泡
 *         },
 *         "balance": {
 *           "balance": 11479968,
 *           "live_noble_balance": 283748,
 *           "live_noble_balance_status": 1
 *         },
 *         "lucky": { // 抽的随机礼物
 *           "gift_id": 80001,
 *           "name: "礼物名称",
 *           "icon_url": "https://static-test.missevan.com/gifts/icons/80001.png",
 *           "effect_url": "*.mp4;*.webp;*.webm", // 特效规则同通用广播特效
 *           "web_effect_url": "*.mp4;*.webp;*.webm",
 *           "price": 6,
 *           "num": 10
 *         },
 *         "gift": { // 抽出的礼物
 *           "gift_id": 90001,
 *           "name: "礼物名称",
 *           "icon_url": "https://static-test.missevan.com/gifts/icons/90001.png",
 *           "effect_url": "*.mp4;*.webp;*.webm", // 特效规则同通用广播特效
 *           "web_effect_url": "*.mp4;*.webp;*.webm",
 *           "effect_duration": 5000,
 *           "price": 6,
 *           "num": 1
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 收礼直播间内消息
 *     // 和普通送礼几乎一致，只是额外多了 lucky 字段
 *     {
 *       "type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/test.png",
 *         "titles": [{
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#F45B41"
 *         }, {
 *           "type": "level",
 *           "level": 9
 *         }, {
 *           "type": "medal",
 *           "name": "独角兽",
 *           "level": 4
 *         }, {
 *           "type": "noble",
 *           "name": "新秀",
 *           "level": 2
 *         }, {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *         }, {
 *           "type": "badge",
 *           "icon_url": "https://static-test.missevan.com/live/medalframes/3f12/level01_0_9_0_54.png",
 *           "appearance_id": 1
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "time": 1576744741101,
 *       "lucky": { // 和普通消息不一样，额外多了 lucky 字段表明赠送的是随机礼物
 *         "gift_id": 80001,
 *         "name": "幸运签",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/80001.png",
 *         "effect_url": "*.svga" // 客户端送礼人（无论是否本机送出）先播放此特效，特效规则同通用广播特效
 *         "web_effect_url": "*.svga" // PC 端送礼人先播放此特效
 *         "price": 6,
 *         "num": 10
 *       },
 *       "message_prefix": "通过超能魔方", // 消息前缀，字段不存在则不需要组合
 *       "gift": {
 *         "gift_id": 90001,
 *         "name": "魔法王冠",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/90001.png",
 *         "effect_url": "*.lottie;*.mp4", // 特效规则同通用广播特效
 *         "web_effect_url": "*.lottie;*.mp4'*.png",
 *         "effect_duration": 5000,
 *         "price": 6,
 *         "num": 1,
 *         "sponsor": { // 冠名信息，没有冠名没有该字段
 *           "icon_url": "https://static-test.maoercdn.com/gifts/labels/001.png"
 *         }
 *       },
 *       "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *         "username_color": "#F0F0F0",
 *         "text_color": "#F0F0F0",
 *         "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *       },
 *       "bubble": {
 *         "type": "noble",
 *         "noble_level": 2
 *       },
 *       "current_revenue": 612
 *     }
 *
 * @apiSuccessExample {json} WebSocket 送礼用户消息，仅当用户在非收礼直播间内送礼时下发，仅送礼用户自己可见
 *     // 和普通送礼几乎一致，没有连击 combo 和 lucky 字段
 *     {
 *       "type": "gift",
 *       "event": "cross_send", // 非收礼直播间内送礼通知使用，不需要累加当前直播间的收益和更新小时榜排名
 *       "room_id": 65261414, // 用户送礼时所在的直播间
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.maoercdn.com/avatar/icon01.png"
 *       },
 *       "room": { // 收礼直播间信息
 *         "room_id": 1234,
 *         "creator_id": 123456,
 *         "creator_iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         "creator_username": "主播昵称"
 *       },
 *       "time": 1576744741101, // 毫秒时间戳
 *       "gift": {
 *         "gift_id": 1,
 *         "name": "药丸",
 *         "icon_url": "https://static-test.maoercdn.com/gifts/icons/001.png",
 *         "price": 6, // 单位：钻石
 *         "num": 1
 *       },
 *       "gift_notification": { // 用户佩戴的送礼通知皮肤，未佩戴送礼通知皮肤时不下发，客户端和前端使用默认送礼通知皮肤
 *         "username_color": "#F0F0F0",
 *         "text_color": "#F0F0F0",
 *         "frame_url": "https://static-test.maoercdn.com/live/gift/notification/blackcard-1.png"
 *       }
 *     }
 *
 * @apiSuccessExample {json} WebSocket 全局消息:
 *     // 和普通送礼产生的飘屏一致
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         ...
 *       },
 *       "gift": {
 *         ...
 *       },
 *       "notify_bubble": { // 随机礼物特有气泡
 *         "type": "custom",
 *         "image_url": "https://example.com/b128_0_10_0_100.png" // 客户端使用
 *         "float": 1, // 如果 float 为 1, 则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *         "shine": 0 // 是否闪光，0 为不闪光，1 为闪光，字段不存在则默认不闪光
 *       },
 *       "message": "<b>bless</b> 给 <b>绵绵思远道い</b> 送出 <b>幸运签</b>*1，抽出惊喜大奖 <b>更贵的城堡</b>*1"
 *     }
 *
 */
func ActionGiftDraw(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDrawGiftParam(c)
	if err != nil {
		return nil, err
	}
	return param.send()
}

type drawGiftResp struct {
	Ok      int                     `json:"ok"`
	User    *liveuser.Simple        `json:"user,omitempty"`
	Bubble  *bubble.Simple          `json:"bubble,omitempty"`
	Balance *utils.BalanceAfterSend `json:"balance"`
	Lucky   *gift.NotifyGift        `json:"lucky"`
	Gift    *gift.NotifyGift        `json:"gift"`
}

type drawGiftParam struct {
	RoomID     int64 `form:"room_id" json:"room_id"`
	FromRoomID int64 `form:"from_room_id" json:"from_room_id"`
	GiftID     int64 `form:"gift_id" json:"gift_id"`
	GiftNum    int   `form:"gift_num" json:"gift_num"`

	userID int64
	c      *handler.Context
	uc     mrpc.UserContext

	bubble    *bubble.Simple
	r         *room.Room
	fromRoom  *room.Room
	giftSend  *gift.Gift
	drawPool  *gift.PoolGift
	poolGifts map[int64]gift.Gift
	uv        *vip.UserVip

	u           *liveuser.Simple
	giftReceive *gift.Gift
	lg          *livegifts.LiveGift
	balance     *userapi.BalanceResp

	luckyGiftFrom int // 随机礼物来源，0: 随机抽取，1: 随机投放，2: 随机抽取命中保底

	userCtx        userapi.UserContext
	broadcastElems []*userapi.BroadcastElem
}

func newDrawGiftParam(c *handler.Context) (*drawGiftParam, error) {
	var param drawGiftParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 || param.GiftID <= 0 || param.GiftNum <= 0 ||
		param.FromRoomID < 0 || param.FromRoomID == param.RoomID {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	param.uc = c.UserContext()
	param.userID = c.UserID()

	// 限制赠送随机礼物
	num, err := service.Redis.Exists(keys.KeyUserForbidSendLuckyGift1.Format(param.userID)).Result()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if num > 0 {
		return nil, actionerrors.NewErrForbidden("暂时无法赠送该礼物")
	}

	param.r, err = room.Find(param.RoomID, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.r.Status.Open != room.StatusOpenTrue {
		giftParams, err := params.FindGift()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if !giftParams.ClosedRoomAllowDrawGift {
			return nil, actionerrors.NewErrForbidden("直播间未开播，无法赠送该礼物")
		}
	}
	if param.r.Limit != nil {
		return nil, actionerrors.NewErrForbidden("本直播间内无法赠送该礼物")
	}
	if param.r.CreatorID == param.userID {
		return nil, actionerrors.ErrParamsMsg("无法给自己的直播间送礼物")
	}
	if param.FromRoomID > 0 {
		param.fromRoom, err = room.Find(param.FromRoomID, &room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.fromRoom == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
	}
	// NOTICE: 猫耳娘的零钱袋是活动发奖账号，不受黑名单的限制
	if param.userID != userstatus.MaoerWalletUserID {
		// 被主播拉黑无法送礼
		blocked, err := blocklist.IsBlocked(param.r.CreatorID, param.userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if blocked {
			return nil, actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
		}
	}
	// 判断是否允许跨直播送礼
	if err = checkAllowCrossSend(param.FromRoomID, param.RoomID); err != nil {
		return nil, err
	}

	param.u, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.u == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	param.giftSend, err = gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.giftSend == nil || param.giftSend.Type != gift.TypeDrawSend ||
		!param.giftSend.OwnedByRoom(param.r.RoomID) ||
		!param.giftSend.OwnedByUser(c.UserID(), param.u.Level()) {
		return nil, actionerrors.ErrNotFound("无法找到指定礼物")
	}
	if !goutil.HasElem(param.giftSend.AllowedNums, param.GiftNum) {
		// 随机礼物不支持自定义数量
		return nil, actionerrors.ErrGiftNum
	}
	// NOTICE: 超粉专属随机礼物只能由超粉送出
	if param.giftSend.IsDrawSendSuperFan() {
		roomMedal, err := livemedal.FindOwnedMedal(param.u.UserID(), param.r.RoomID, livemedal.FindOptions{OnlyMedal: true})
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if !param.giftSend.OwnedByMedal(roomMedal) {
			return nil, actionerrors.NewErrForbidden("无法购买当前粉丝礼物")
		}
	}
	param.drawPool, err = gift.FindPoolGift(param.GiftID, param.GiftNum)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.drawPool == nil {
		return nil, actionerrors.NewErrForbidden("无法购买当前礼物")
	}
	var ok bool
	ok, param.poolGifts = param.drawPool.Valid()
	if !ok {
		return nil, actionerrors.NewErrForbidden("无法购买当前礼物")
	}

	_, uv, err := userstatus.UserGeneral(c.UserID(), c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}

	param.bubble, err = userappearance.FindMessageBubble(c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.userCtx = userapi.NewUserContext(c)
	return &param, nil
}

func (param *drawGiftParam) send() (*drawGiftResp, error) {
	drawResult, err := param.draw()
	if err != nil {
		return nil, err
	}

	param.lg = livegifts.NewLiveGifts(param.r.OID, param.RoomID, param.u, param.bubble).
		SetGift(param.giftReceive, 1 /* 随机礼物总是一个 */).
		SetLuckyGift(param.giftSend, param.GiftNum, drawResult.guaranteed).
		SetRoomOpenStatus(param.r.IsOpen()).
		SetTransactionIDs(drawResult.balance.TransactionID)
	// 随机礼物没有连击
	_, err = livegifts.UpdateSave(param.lg, nil, primitive.NilObjectID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	goutil.Go(func() {
		param.tryDisableDraw(drawResult.sendGiftID)
		param.addRevenueRank()
		param.addPK()
		param.addMedalPoint()
		param.addMultiConnectScore()
		param.addUserRedeemPoint()
		param.addUserContribution()
		param.activatedGiftWall()
		param.addFansBoxEnergy()
		param.buildIMMessage()
		param.broadcast()

		param.addActivity()
		param.addLiveShow()
		param.addLiveSpend()
		param.addRoomPaidUser()
	})
	resp := &drawGiftResp{
		Ok:      1,
		User:    param.u,
		Bubble:  param.bubble,
		Balance: utils.NewBalanceAfterSend(drawResult.balance, param.uv != nil),
		Lucky:   gift.NewNotifyGiftLucky(param.giftSend, param.giftReceive, param.GiftNum),
		Gift:    gift.NewNotifyGift(param.giftReceive, 1),
	}
	return resp, nil
}

type drawResultWithBalance struct {
	sendGiftID int64
	guaranteed bool
	balance    *userapi.BalanceResp
}

// drop 随机礼物投放，当命中投放礼物时返回奖品掉落结果的指针
func (param *drawGiftParam) drop() *liveluckygiftdrop.GiftDropResult {
	timeNowUnix := goutil.TimeNow().Unix()
	// 根据送出的随机礼物档位获取当前时间生效的奖池配置
	dropCfg, err := liveluckygiftdrop.FindCurrentConfig(param.GiftID, int64(param.GiftNum))
	if err != nil {
		logger.Error(err)
		return nil
	}
	if dropCfg == nil {
		return nil
	}

	// 如果配置的礼物不在奖池中直接返回
	if _, ok := param.poolGifts[dropCfg.PrizeGiftID]; !ok {
		logger.WithField("config_id", dropCfg.ID).Error("投放礼物不在随机礼物奖池中")
		return nil
	}

	// 获取该配置发放的礼物数量，确认还存在剩余礼物
	countKey := keys.KeyLuckyGiftDropCount1.Format(dropCfg.ID)
	dropCount, err := service.Redis.Get(countKey).Int64()
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.Error(err)
		return nil
	}
	if dropCount >= dropCfg.PrizeGiftTotalNum {
		return nil
	}

	// 获取该配置最新一条投放记录
	record, err := liveluckygiftdrop.FindLastRecord(dropCfg.ID)
	if err != nil {
		logger.Error(err)
		return nil
	}
	// 根据投放记录确定加锁的时间点，如果没有投放记录则锁定 0 值
	var timeStamp int64
	if record != nil {
		// 当前时间如果在期望投放时间之前，或最新的投放记录已经实际发放（不会生成新的时间点）则直接返回，不尝试计算是否命中掉落礼物
		if timeNowUnix < record.ExpectedTime || record.ActualTime != 0 {
			return nil
		}
		timeStamp = record.ExpectedTime
	}
	// 加锁判断用户是否能触发掉落礼物，获取锁失败时直接返回，走正常抽取的逻辑
	// TODO: 使用新的分布式锁的封装，需要新增只尝试一次的方法
	lockKey := keys.LockLuckyGiftDrop2.Format(dropCfg.ID, timeStamp)
	ok, err := service.Redis.SetNX(lockKey, 1, time.Second*2).Result()
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	defer func() {
		err = service.Redis.Del(lockKey).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}()

	dropResult := dropCfg.GiftDrop(timeStamp, timeNowUnix, dropCfg.PrizeGiftTotalNum-dropCount)
	var hitRecordID int64
	err = servicedb.Tx(liveluckygiftdrop.DB(), func(tx *gorm.DB) error {
		// 获取轮空和下一次投放的时间记录，批量插入
		newRecords := dropResult.NewRecords(dropCfg.ID, timeNowUnix)
		if len(newRecords) > 0 {
			err = servicedb.BatchInsert(tx, liveluckygiftdrop.Record{}.TableName(), newRecords)
			if err != nil {
				return err
			}
		}

		// 没有命中时，只需要新插入轮空的记录
		if !dropResult.IsHit() {
			return nil
		}

		if dropResult.IsHitPrevDropTime {
			// 命中上次的记录时，需要对该记录进行更新
			db := tx.Table(liveluckygiftdrop.Record{}.TableName()).Where("id = ? AND actual_time = 0", record.ID).
				Updates(map[string]interface{}{
					"modified_time": timeNowUnix,
					"actual_time":   dropResult.ActualTime,
					"user_id":       param.userID,
					"room_id":       param.RoomID,
				})
			if err = db.Error; err != nil {
				return err
			}
			if db.RowsAffected == 0 {
				return servicedb.ErrNoRowsAffected
			}
			hitRecordID = record.ID
		} else {
			// 没有命中上次的记录的话，需要对命中记录单独插入
			newRecord := &liveluckygiftdrop.Record{
				CreateTime:   timeNowUnix,
				ModifiedTime: timeNowUnix,
				ConfigID:     dropCfg.ID,
				ExpectedTime: dropResult.ExpectTime,
				ActualTime:   dropResult.ActualTime,
				UserID:       param.userID,
				RoomID:       param.RoomID,
			}
			err = tx.Create(newRecord).Error
			if err != nil {
				return err
			}
			hitRecordID = newRecord.ID
		}

		// 尝试增加分配的礼物数量，如果超出配置数量则直接返回
		pipe := service.Redis.TxPipeline()
		incrCmd := pipe.Incr(countKey)
		liveserviceredis.ExpireAt(pipe, countKey, time.Unix(dropCfg.EndTime, 0))
		_, err = pipe.Exec()
		if err != nil {
			return err
		}
		// 如果增加后的发放结果大于配置的礼物数，直接返回避免超发
		if incrCmd.Val() > dropCfg.PrizeGiftTotalNum {
			return fmt.Errorf("投放礼物数大于配置总数量，config_id: %d", dropCfg.ID)
		}
		return nil
	})
	if err != nil {
		logger.Error(err)
		return nil
	}

	if !dropResult.IsHit() {
		return nil
	}
	dropResult.PrizeGiftID = dropCfg.PrizeGiftID
	dropResult.HitRecordID = hitRecordID
	return &dropResult
}

func (param *drawGiftParam) draw() (*drawResultWithBalance, error) {
	if param.drawPool.IsLockRequired() {
		lock := redismutex.New(
			service.Redis,
			param.drawPool.KeyLockGiftDraw(param.userID),
			2*time.Second,
		)
		if !lock.TryLock() {
			return nil, actionerrors.NewErrForbidden("操作过于频繁，请稍后再试")
		}
		defer lock.Unlock()
	}

	var (
		sendGiftID int64
		drawResult *gift.PoolGiftDrawResult
		dropResult *liveluckygiftdrop.GiftDropResult
		err        error
	)
	dropResult = param.drop()
	if dropResult != nil {
		// 命中掉落大奖
		sendGiftID = dropResult.PrizeGiftID
		param.luckyGiftFrom = userapi.LuckyGiftFromDrop
	} else {
		drawResult, err = param.drawPool.Draw(param.userID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		sendGiftID = drawResult.GiftID
		if drawResult.Guaranteed {
			param.luckyGiftFrom = userapi.LuckyGiftFromGuaranteed
		} else {
			param.luckyGiftFrom = userapi.LuckyGiftFromDraw
		}
	}
	// NOTICE: 奖池中的礼物理论上不会出现漏的情况
	g := param.poolGifts[sendGiftID]
	if g.Type != gift.TypeDrawReceive {
		logger.Errorf("抽到了未在奖池礼物中的礼物，礼物 ID: %d", sendGiftID)
		return nil, actionerrors.NewErrForbidden("无法购买当前礼物")
	}
	param.giftReceive = &g
	param.balance, err = userapi.SendLuckyGift(
		param.userID, param.r.CreatorID,
		&userapi.Gift{ID: param.giftSend.GiftID, Title: param.giftSend.Name, Price: param.giftSend.Price, Num: int64(param.GiftNum)},
		&userapi.Gift{ID: param.giftReceive.GiftID, Title: param.giftReceive.Name, Price: param.giftReceive.Price, Num: 1},
		param.uv != nil, param.r.Status.OpenLogID, param.luckyGiftFrom, userapi.NewUserContext(param.c),
	)
	if err != nil {
		// 添加回滚机制的主要目的是避免用户利用钻石余额不足时抽奖导致白嫖保底计数的问题。
		// 不过即使 RPC 接口返回错误，也有可能扣除了用户的钻石并发放给主播，比如调用超时。
		// 所以在触发保底这种特殊场景下不回滚抽奖结果，避免连续触发保底。
		if drawResult != nil && !drawResult.Guaranteed {
			err2 := param.drawPool.RollbackDrawResult(param.userID, drawResult)
			if err2 != nil {
				logger.Error(err2)
				// PASS
			}
		}
		return nil, err
	}

	if dropResult != nil {
		// 在投放大奖成功时，更新投放记录的 transaction_id
		err = liveluckygiftdrop.UpdateTransactionID(dropResult, param.balance.TransactionID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"record_id":      dropResult.HitRecordID,
				"transaction_id": param.balance.TransactionID}).Error(err)
			// PASS
		}
	}

	guaranteed := false
	if drawResult != nil {
		guaranteed = drawResult.Guaranteed
	}
	return &drawResultWithBalance{
		sendGiftID: sendGiftID,
		guaranteed: guaranteed,
		balance:    param.balance,
	}, nil
}

func (param *drawGiftParam) tryDisableDraw(sendGiftID int64) {
	if !param.giftSend.IsDisableDrawRoomCustomAfterSSR() {
		return
	}

	if !param.drawPool.IsSSR(sendGiftID) {
		return
	}

	err := livecustom.RemoveRoomCustomGift(param.r.RoomID, param.giftSend.GiftID)
	if err != nil {
		logger.Error(err)
		return
	}

	err = liveim.NotifyRoomGiftUpdate(param.r.RoomID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *drawGiftParam) addRevenueRank() {
	score := param.giftReceive.Price
	if score == 0 {
		return
	}
	err := roomsrank.AddRevenue(param.r.RoomID, param.userID, score, goutil.IntToBool(param.r.Status.Open))
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.r.CreatorID, param.r.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err := room.NotifyHourRank(rankChange, param.r)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	err = liverevenues.AddGiftRevenue(param.userID, param.r.OID, param.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 随机礼物只收一个
	err = param.r.ReceiveGift(1, param.giftReceive.Price)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *drawGiftParam) addPK() {
	score, freeScore := param.giftReceive.PKScores(1)
	elems, err := livepk.AddPKScore(param.r, param.userID, score, freeScore)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *drawGiftParam) addMedalPoint() {
	if param.r.Medal == nil {
		return
	}
	medalPoint := param.giftReceive.MedalPoint(1) // 抽奖只会抽出一个礼物
	if medalPoint == 0 {
		return
	}
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    param.r.OID,
		RoomID:     param.r.RoomID,
		CreatorID:  param.r.CreatorID,
		FromRoomID: param.FromRoomID,
		UserID:     param.userID,
		UV:         param.uv,
		MedalName:  param.r.Medal.Name,
		Type:       livemedal.TypeGiftAddMedalPoint,
		Source:     livemedal.ChangeSourceGift,
		PointAdd:   medalPoint,
		Scene:      livemedalpointlog.SceneTypePayGift,
		IsRoomOpen: param.r.IsOpen(),
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.u,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.r.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func (param *drawGiftParam) addMultiConnectScore() {
	if !param.r.IsMultiConnect() {
		return
	}
	elems, err := livemulticonnect.ScoreHelper{
		Room:   param.r,
		Gift:   param.giftReceive,
		Num:    1,
		UserID: param.userID,
	}.AddScore()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(elems) != 0 {
		param.broadcastElems = append(param.broadcastElems, elems...)
	}
}

func (param *drawGiftParam) addUserRedeemPoint() {
	// 黑名单内的随机礼物不添加获取积分的进度
	if shop.IsBlockedGift(param.giftSend.GiftID) {
		return
	}
	p, err := params.FindRedeemShop()
	if err != nil {
		logger.Error(err)
		return
	}
	// 常驻商城未开始时不添加获取积分的进度
	if goutil.TimeNow().Unix() < p.ShowTime {
		return
	}
	err = shop.AddUserRedeemPoint(param.userID, param.giftSend.Price*int64(param.GiftNum))
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *drawGiftParam) addUserContribution() {
	// 按照实际消费金额增加等级经验值
	pointAdd := param.giftSend.Price * int64(param.GiftNum) * 10 // 1 钻石 = 10 经验
	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.userID, param.RoomID, param.r.CreatorUsername, userstatus.FromGiftSend, param.uv)
	if param.fromRoom != nil {
		addParam.SetFromRoom(param.fromRoom.RoomID, param.fromRoom.CreatorUsername)
	}
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *drawGiftParam) activatedGiftWall() {
	notifyElem, err := giftwall.ActiveGift(param.r, param.userID, param.giftReceive.GiftID, param.giftReceive.Price, 1)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if notifyElem != nil {
		param.broadcastElems = append(param.broadcastElems, notifyElem)
	}
}

func (param *drawGiftParam) buildIMMessage() {
	// 随机礼物没有飘屏文案的不发全站飘屏，也没有默认飘屏文案
	sendNotify := param.giftReceive.NotifyMessageTemplate() != ""

	// 查询用户已佩戴的送礼通知外观
	giftNotification, err := userappearance.FindWornAppearance(param.userID, appearance.TypeGiftNotification)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 房间送礼消息
	roomMsg := param.lg.RoomMessage()
	if giftNotification != nil {
		roomMsg.GiftNotification = userappearance.NewGiftNotification(giftNotification)
	}
	param.broadcastElems = append(param.broadcastElems,
		param.lg.BuildBroadcastMessage(!sendNotify && param.r.FilterGiftMessage(), roomMsg),
	)
	if param.FromRoomID != 0 {
		// 跨房间送礼消息
		crossRoomMsg := param.lg.CrossRoomMessage(param.FromRoomID, param.r)
		if giftNotification != nil {
			crossRoomMsg.GiftNotification = userappearance.NewGiftNotification(giftNotification)
		}
		param.broadcastElems = append(param.broadcastElems,
			param.lg.BuildCrossRoomBroadcastMessage(param.FromRoomID, param.r, crossRoomMsg),
		)
	}

	if !sendNotify {
		return
	}

	// 全站飘屏
	nb := gift.NotifyBuilder{
		RoomID:          param.r.RoomID,
		CreatorUsername: param.r.CreatorUsername,
		User:            param.u,
		Gift:            param.giftReceive,
		GiftNum:         1,
		LuckyGift:       param.giftSend,
		LuckyGiftNum:    param.GiftNum,
	}
	param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
		Type:    liveim.IMMessageTypeAll,
		RoomID:  param.RoomID,
		Payload: nb.Build(),
	})
}

// broadcast 发送 im 消息
func (param drawGiftParam) broadcast() {
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *drawGiftParam) addActivity() {
	box.SendQuestMessage(param.r.RoomID, param.r.CreatorID, box.QuestTypeGift, param.giftReceive.Price)
	// 同步送礼收益
	r := rank.
		NewSyncParam(param.r.RoomID, param.u.UserID(), param.r.CreatorID).
		SetTransaction(param.balance).
		SetOpenLogID(param.r.Status.OpenLogID).
		SetGuildID(param.r.GuildID).
		SetActivityCatalogID(param.r.ActivityCatalogID).
		SetGift(param.giftReceive, 1).
		SetLuckyGift(param.giftSend, param.GiftNum)
	r.AddRankPoint()
	r.SendLiveActivity(param.uc)
}

func (param *drawGiftParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.RoomID, param.u.UserID(), param.r.CreatorID).
		SetGift(param.giftReceive.GiftID, param.giftReceive.Price, 1).
		Sync()
}

func (param *drawGiftParam) addLiveSpend() {
	roomID := param.RoomID
	if param.FromRoomID > 0 {
		// 主播连线时，用户实际所在的直播间是 FromRoomID
		roomID = param.FromRoomID
	}
	utils.SendLiveSpend(param.userID, param.giftSend.Price*int64(param.GiftNum), roomID)
}

func (param *drawGiftParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.userID, param.r.Status.OpenTime)
}

func (param *drawGiftParam) addFansBoxEnergy() {
	task, err := livefansboxtask.FindTodayTask(param.RoomID)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":         param.userID,
			"room_id":         param.RoomID,
			"gift_id":         param.giftSend.GiftID,
			"gift_num":        param.GiftNum,
			"receive_gift_id": param.giftReceive.GiftID,
			"receive_price":   param.giftReceive.Price,
		}).Error("查询粉丝团宝箱当日任务失败：", err)
		// PASS
		return
	}
	if task == nil {
		return
	}

	updated, err := task.ContributeFromGift(livefansboxtask.GiftContribution{
		RoomID:  param.RoomID,
		UserID:  param.userID,
		Gift:    param.giftReceive,
		GiftNum: 1, // 抽取礼物固定数量为1
	})
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":         param.userID,
			"room_id":         param.RoomID,
			"gift_id":         param.giftSend.GiftID,
			"gift_num":        param.GiftNum,
			"receive_gift_id": param.giftReceive.GiftID,
			"receive_price":   param.giftReceive.Price,
		}).Error("粉丝团宝箱能量贡献处理失败：", err)
		// PASS
		return
	}

	if updated {
		taskUpdateMessage := task.NewTaskUpdateMessage()
		param.broadcastElems = append(param.broadcastElems, &userapi.BroadcastElem{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  task.RoomID,
			Payload: taskUpdateMessage,
		})
	}
}
