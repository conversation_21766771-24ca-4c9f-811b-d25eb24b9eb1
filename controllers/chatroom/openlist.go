package chatroom

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/recommended"
	"github.com/MiaoSiLa/live-service/controllers/utils/tab"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/mrecommendedelementsdailyexposure"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/biliai"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
	uuid "github.com/satori/go.uuid"
)

// 榜单类型
const (
	OpenListTypeHot       int64 = iota // 热门分区
	openListTypeNew                    // 最新榜单
	openListTypeNova                   // 新星榜单
	OpenListTypeRecommend              // 推荐分区

	openListTypeLimit
)

const recommendLastHourIndex = 2 // 上小时榜保底第二

// TODO: 兼容当前客户端版本的分页，预设算法侧能够返回的最大直播间数量为 1000，线上预计开播直播间数量为 400 个
const liveRoomCount = 1000

const (
	hotTabAssignRecommendDisable = iota // 热门 tab 关闭干预
	hotTabAssignRecommendEnable         // 热门 tab 开启干预
)

type roomSimpleList struct {
	Pagination goutil.Pagination `json:"pagination"`
	Data       []*room.Simple    `json:"Datas"`
}

func (list *roomSimpleList) fillEmptyCoverURL() {
	defaultCoverURL := service.Storage.Parse(config.Conf.Params.URL.DefaultIconURL)
	for i := 0; i < len(list.Data); i++ {
		if list.Data[i].CoverURL == "" {
			list.Data[i].CoverURL = defaultCoverURL
		}
	}
}

// checkResp 保证返回的房间都有封面等必要字段
func (list *roomSimpleList) checkResp() {
	list.fillEmptyCoverURL()
}

// openListResponse 直播 tab 响应
type openListResponse struct {
	Pagination goutil.Pagination            `json:"pagination"`
	Data       []*recommended.RoomWithTrace `json:"Datas"`
}

func (o *openListResponse) saveCache(cacheKey string, exposureLog map[string]any, isTianmaRecommend bool) {
	// 非第一页请求、算法推荐不进行缓存
	if o.Pagination.P != 1 || isTianmaRecommend {
		return
	}
	data := cacheData{
		Data:        o,
		ExposureLog: exposureLog,
	}

	v, err := json.Marshal(data)
	if err != nil {
		logger.Error(err)
		return
	}
	service.Cache10s.Set(cacheKey, v, 0)
}

func (o *openListResponse) clearRoomStatus(c *handler.Context) {
	// WORKAROUND: 针对 Web 端将红包和福袋状态置为 0，避免被爬取相关状态
	if !c.Equip().FromApp {
		for i := 0; i < len(o.Data); i++ {
			o.Data[i].Status.RedPacket = 0
			o.Data[i].Status.LuckyBag = 0
		}
	}
}

type cacheData struct {
	Data        *openListResponse `json:"Datas"`
	ExposureLog map[string]any    `json:"exposure_log"`
}

// AlgoRecommendRoomInfo 算法推荐卡信息
type AlgoRecommendRoomInfo struct {
	Trace       string
	AvFeature   string
	Goto        tianma.Goto
	Source      string
	Attr        int
	OldPosition *int // 卡片的原始推荐位，用于曝光上报，位置发生变更时需要上报
}

// AlgorithmInterventionAvFeature 算法干预卡特征信息
type AlgorithmInterventionAvFeature struct {
	OperateID     int64 `json:"operate_id,omitempty"`     // 操作 ID（live_recommended_elements 表 ID）
	OperateSource int   `json:"operate_source,omitempty"` // 操作资源类型（1：热 4；2：直播列表推荐算法曝光干预卡）
}

// AlgoRecommendUserFeature 算法推荐用户特征
type AlgoRecommendUserFeature struct {
	SampleIDs string `json:"sample_ids,omitempty"` // 天马算法下发的实验组分组
}

// 流程：查询开播房间列表 -> 查询推荐房间 -> 查询房间的推荐图标 -> 将推荐房间插入开播房间中
type openListParam struct {
	p           int64
	pageSize    int64
	catalogID   int64
	tagID       int64
	listType    int64
	refreshType int // TODO：当前版本客户端未接入刷新方式
	network     int // TODO：当前版本客户端未接入网络类型
	buvid       string
	userID      int64
	uc          mrpc.UserContext

	cacheKey         string
	cacheExposureLog map[string]any // 缓存的曝光日志

	subCatalogIDs []int64

	*roomSimpleList

	// 推荐图标映射，元素是 map[roomID]iconURL
	// 推荐房间的优先级显示：神话推荐 > 上小时榜 > 活动配置
	/* 下标和图标类型
	0: 运营配置的图片
	1: 上小时榜
	2: 神话推荐
	*/
	iconURLs [3]map[int64]string

	// nrRoom 神话推荐的直播间，效果为保底
	nrRoom *room.Simple
	// lastHourRoom 上小时榜的直播间，效果为固定
	lastHourRoom *room.Simple
	// recommendRooms 运营配置的推荐直播间，效果为固定
	recommendRooms []*room.Simple
	// hot6Room 热 6 推荐直播间，效果为固定
	hot6Room *room.Simple

	opt                                     *room.FindOptions
	isTianmaRecommend                       bool                             // 标记本次请求是否来自算法推荐
	algoRecommendRoomInfoMap                map[int64]*AlgoRecommendRoomInfo // 算法下发的直播间的信息 map（以 room ID 为 key）
	trackID                                 string                           // 算法返回，标记一次数据请求的 ID，同一刷中所有卡片的 track_id 取值一样
	userFeature                             string                           // 算法返回，表示用户特征，用于曝光上报
	algorithmInterventionAvFeatureOperateID []int64                          // 算法干预卡特征信息中的 operate_id （live_recommended_elements 表 ID），用于实时更新干预卡每日曝光量
	isHotTabAssignRecommend                 bool                             // 标记热门 tab 是否开启干预
	cachedUserConfig                        *userapi.MUserConfig             // 缓存 GetUserConfig 的结果，避免在同一请求中重复调用
}

// ActionOpenList 开播列表
/**
 * @api {get} /api/v2/chatroom/open/list 开播列表
 * @apiDescription 给出正在开播的房间
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 * @apiParam {Number} [catalog_id] 分类 id
 * @apiParam {Number} [tag_id] 标签 id
 * @apiParam {number=0,1,2,3} [type=0] 列表类型，0：热门分区，1：最新榜单，2：新星榜单，3：推荐分区
 * @apiParam {number=0~4} [network=0] 用户当前网络状况 （同埋点上报参数约定值，即：0: UNKNOWN; 1: WIFI; 2: CELLULAR; 3: OFFLINE; 4: OTHERNET）
 * @apiParam {number=0,1,2,4} [refresh_type=0] 刷新方式 (0：默认；1：自动刷新；2：顶部下拉；4：底部上滑)
 *
 * @apiSuccess {number} info.rank.recommend[].index 用户实际看到的开播列表中推荐房间的下标, \
 *   index 在 recommend 数组中从小到大排，不会重复
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "track_id": "ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0",  // 标记一次数据请求的 ID，同一刷中所有卡片的 track_id 取值一样
 *           "trace": "{\"track_id\":\"ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0\",\"refresh_type\":1,\"refresh_num\":3,\"attr\":1}",
 *           "room_id": 152,
 *           "name": "12345",
 *           "cover_url": "http://static.maoercdn.com/cover.png",
 *           "announcement": "12345",
 *           "creator_id": 12345,
 *           "creator_username": "1234",
 *           "creator_iconurl": "http://static.maoercdn.com/creator.png",
 *           "creator_introduction": "主播个人简介",
 *           "catalog_id": 107,
 *           "catalog_name": "分区名",
 *           "catalog_color": "#ffffff",
 *           "custom_tag": {
 *             "tag_id": 10001,
 *             "tag_name": "腹黑青叔"
 *           },
 *           "icon_url": "http://static.maoercdn.com/recommend.gif",  // 超管设置的推荐图标
 *           "statistics": {
 *             "accumulation": 123,
 *             "score": 123
 *             ...
 *           },
 *           "status": {
 *             "open": 1,
 *             "pk": 1, // 1：直播间在 PK 状态，0 或不存在：直播间不在 PK 状态
 *             "red_packet": 1, // 当前待抢红包或可抢红包状态，0 或不存在表示无待抢红包或者可抢红包
 *             "lucky_bag": 1, // 直播间福袋状态，0 或不存在表示直播间没有福袋；1: 直播间存在进行中的福袋
 *             "multi_connect": 1, // 多人连线，0 或不存在表示直播间没有进行中的连线；1: 直播间存在进行中的连线
 *             ...
 *           }
 *         }
 *       ],
 *       "pagination": {
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 1,
 *         "maxpage": 1
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionOpenList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newOpenListParam(c)
	if err != nil {
		return nil, err
	}

	err = param.findSubCatalogIDs()
	if err != nil {
		return nil, err
	}

	if !c.Equip().FromApp {
		resp, err := param.handleNonAppRequest(c)
		if err != nil {
			return nil, err
		}
		return resp, nil
	}
	resp, err := param.handleAppRequest(c)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newOpenListParam(c *handler.Context) (*openListParam, error) {
	var param openListParam
	var err error
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.userID = c.UserID()
	if c.Equip().FromApp {
		equipment := c.Equip()
		if equipment.BUVID == "" {
			logger.WithFields(logger.Fields{"equip_id": equipment.EquipID, "ip": c.ClientIP(), "user_id": param.userID}).Error("buvid 为空")
			return nil, actionerrors.ErrParams
		}
		param.buvid = equipment.BUVID
		// pageSize 与算法最大的返回推荐卡对齐
		param.pageSize = biliai.MaxTopRoomCount
	}
	param.listType, _ = c.GetParamInt64("type")
	if param.listType < OpenListTypeHot || param.listType >= openListTypeLimit {
		return nil, actionerrors.ErrParams
	}
	param.catalogID, _ = c.GetParamInt64("catalog_id")
	param.tagID, _ = c.GetParamInt64("tag_id")
	param.refreshType, _ = c.GetDefaultParamInt("refresh_type", -1) // TODO：客户端在当前版本暂不支持刷新方式
	param.network, _ = c.GetDefaultParamInt("network", -1)          // TODO：客户端在当前版本暂不支持网络类型
	if param.listType != OpenListTypeHot {
		param.catalogID = 0
	}
	if param.catalogID != 0 && param.tagID != 0 {
		return nil, actionerrors.ErrParams
	}
	// 当 tagID 为"萌新"时，通过把 tagID 更改为 ListType 来使用原查询"萌新"的方法
	// TODO: 后续优化成在 /meta/data 中下发新星分区的 list_type 避免在 /open/list 中额外做转换
	if param.tagID == tag.TagNova {
		param.listType, param.tagID = openListTypeNova, 0
	}

	hotTabAssignRecommend := hotTabAssignRecommendDisable
	// 不展示推荐 tab 时，热门 tab 开启干预
	if param.listType == OpenListTypeHot && !param.isShowRecommendTab(c) {
		param.isHotTabAssignRecommend = true
		hotTabAssignRecommend = hotTabAssignRecommendEnable
	}

	v := url.Values{}
	v.Set("p", strconv.FormatInt(param.p, 10))
	v.Set("pagesize", strconv.FormatInt(param.pageSize, 10))
	v.Set("type", strconv.FormatInt(param.listType, 10))
	v.Set("catalog_id", strconv.FormatInt(param.catalogID, 10))
	v.Set("tag_id", strconv.FormatInt(param.tagID, 10))
	param.cacheKey = keys.KeyChatroomOpenList2.Format(v.Encode(), hotTabAssignRecommend)

	param.opt = &room.FindOptions{
		FindCreator:     true,
		FindCatalogInfo: true,
		FindCustomTag:   true,
		FindLuckyBag:    true,
	}
	param.uc = c.UserContext()
	return &param, nil
}

// 处理 APP 请求
func (param *openListParam) handleAppRequest(c *handler.Context) (*openListResponse, error) {
	// WORKAROUND: Android < 6.3.5 和 iOS < 6.3.5 的版本不使用算法推荐，因此直接走兜底逻辑
	if c.Equip().IsOldApp(goutil.AppVersions{Android: "6.3.5", IOS: "6.3.5"}) {
		return param.handleFallback(c)
	}
	// 非实验组 B 的热门 Tab 不使用算法推荐
	if param.listType == OpenListTypeHot && param.catalogID == 0 && !param.isHotTabUseAlgorithm(c) {
		return param.handleFallback(c)
	}

	var err error
	// 初始化分页参数
	err = param.initPagination()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.p == 1 {
		err = param.handleFirstPage(c)
		if err != nil || len(param.Data) < biliai.MaxTopRoomCount {
			// 第一页，如果请求出错或请求无数据则走兜底逻辑
			return param.handleFallback(c)
		}
	} else {
		err = param.handleNonFirstPage(c)
		if err != nil {
			return nil, err
		}
	}
	if len(param.Data) <= 0 {
		return nil, nil
	}
	return param.buildResponse(c)
}

func (param *openListParam) handleFallback(c *handler.Context) (*openListResponse, error) {
	param.isTianmaRecommend = false
	// 设置兜底状态
	if err := setFallbackState(param.buvid, 10*time.Minute); err != nil {
		logger.WithFields(logger.Fields{"buvid": param.buvid}).Errorf("设置兜底状态失败：%v", err)
		return nil, err
	}

	// 加载兜底数据
	if err := param.findList(); err != nil {
		return nil, err
	}

	return param.buildResponse(c)
}

// 处理非 APP 请求
func (param *openListParam) handleNonAppRequest(c *handler.Context) (*openListResponse, error) {
	if resp := param.loadCache(); resp != nil {
		return resp.Data, nil
	}

	if err := param.findList(); err != nil {
		return nil, err
	}

	return param.buildResponse(c)
}

// 判断是否需要干预插卡
func (param *openListParam) canAssignRecommend(c *handler.Context) bool {
	// 天马推荐不插
	if param.isTianmaRecommend {
		return false
	}
	// 用户展示推荐 tab，对应的热门 tab 不插卡
	return param.isHotTabAssignRecommend
}

// 构建响应
func (param *openListParam) buildResponse(c *handler.Context) (*openListResponse, error) {
	if param.canAssignRecommend(c) {
		param.findRecommendRoom()
		param.assignRecommend()
	}

	if c.Equip().FromApp {
		param.RecommendsExposureLog(c)
	}

	param.assignIconURL()
	param.checkResp()

	resp := &openListResponse{
		Pagination: param.Pagination,
		Data:       param.buildTrace(),
	}

	resp.clearRoomStatus(c)
	resp.saveCache(param.cacheKey, param.cacheExposureLog, param.isTianmaRecommend)
	return resp, nil
}

// 请求第一页时，清除兜底状态，并请求推荐算法
func (param *openListParam) handleFirstPage(c *handler.Context) error {
	// 清除兜底状态
	err := cleanFallbackState(param.buvid)
	if err != nil && !serviceredis.IsRedisNil(err) {
		logger.WithFields(logger.Fields{"buvid": param.buvid}).Errorf("清除兜底状态失败: %v", err)
		return err
	}

	// 请求天马推荐算法
	err = param.listFromTianma(c)
	if err != nil {
		logger.WithFields(logger.Fields{"buvid": param.buvid}).Error(err)
		return err
	}
	return nil
}

// handleNonFirstPage 处理非第一页的请求
func (param *openListParam) handleNonFirstPage(c *handler.Context) error {
	// 检查是否处于兜底状态
	inFallback, err := isInFallbackState(param.buvid)
	if err != nil {
		logger.WithFields(logger.Fields{"buvid": param.buvid}).Errorf("检查兜底状态失败：%v", err)
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 如果在兜底状态，走兜底逻辑
	if inFallback {
		param.isTianmaRecommend = false
		if err := param.findList(); err != nil {
			return err
		}
		return nil
	}

	// 不在兜底状态，请求算法推荐
	if err := param.listFromTianma(c); err != nil {
		logger.WithFields(logger.Fields{"buvid": param.buvid}).Errorf("推荐算法请求失败：%v", err)
		// 设置兜底状态
		if err := setFallbackState(param.buvid, 10*time.Minute); err != nil {
			logger.WithFields(logger.Fields{"buvid": param.buvid}).Errorf("设置兜底状态失败：%v", err)
			// PASS
		}
		// 算法推荐失败，直接返回错误
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *openListParam) buildTrace() []*recommended.RoomWithTrace {
	var sampleIDs *string
	if param.userFeature != "" {
		userFeature := new(AlgoRecommendUserFeature)
		err := json.Unmarshal([]byte(param.userFeature), userFeature)
		if err != nil {
			logger.Errorf("解析算法推荐下发的用户特征失败: %v", err)
			// PASS
		}
		sampleIDs = &userFeature.SampleIDs
	}
	requestID := uuid.NewV4().String()
	roomListWithTrace := make([]*recommended.RoomWithTrace, 0, len(param.Data))
	for i := range param.Data {
		room := &recommended.RoomWithTrace{
			Simple: param.Data[i],
		}

		trace := recommended.Trace{
			RefreshType: param.refreshType,
			RefreshNum:  param.p,
			Attr:        recommended.RecommendAttrOpConfig,
		}
		// 触发兜底逻辑，Attr 采用补位标记
		if !param.isTianmaRecommend {
			trace.Attr = recommended.RecommendAttrSupplement
			trace.Goto = tianma.GotoLive
			if param.isOpLive(param.Data[i].RoomID) {
				trace.Goto = tianma.GotoOpLive
			}
			trace.RequestID = requestID
		} else {
			if extendInfo, exists := param.algoRecommendRoomInfoMap[param.Data[i].RoomID]; exists {
				trace.Attr = extendInfo.Attr
				trace.Goto = extendInfo.Goto
			}
		}
		room.TrackID = param.trackID
		trace.TrackID = param.trackID
		trace.SampleIDs = sampleIDs
		traceStr, err := json.Marshal(trace)
		if err != nil {
			logger.Errorf("marshal trace error for room %d: %v", param.Data[i].RoomID, err)
			continue
		}
		room.Trace = string(traceStr)
		roomListWithTrace = append(roomListWithTrace, room)
	}
	return roomListWithTrace
}

// RecommendsExposureLog 埋点上报和更新干预卡当日（实时）曝光量
func (param *openListParam) RecommendsExposureLog(c *handler.Context) {
	key := recommended.BuildRecommendLogKey(c.UserID(), c.ClientIP())
	param.cacheExposureLog = param.buildRecommendsExposureLog(c)
	// 更新当日实时曝光量
	if len(param.algorithmInterventionAvFeatureOperateID) > 0 {
		elementIDs := sets.Uniq(param.algorithmInterventionAvFeatureOperateID)
		mrecommendedelementsdailyexposure.UpdateOrCreateDailyExposure(elementIDs, mrecommendedelementsdailyexposure.SceneLivePage)
	}

	goutil.Go(func() {
		err := service.DatabusLogSend(key, param.cacheExposureLog)
		if err != nil {
			logger.Errorf("埋点上报失败: %v", err)
			// PASS
		}
	})
}

func (param *openListParam) buildRecommendsExposureLog(c *handler.Context) map[string]interface{} {
	equipment := c.Equip()
	channel := c.Request().Header.Get("channel")
	log := map[string]interface{}{
		"user_id":      c.UserID(),
		"os":           equipment.OS,
		"equip_id":     equipment.EquipID,
		"buvid":        equipment.BUVID,
		"channel":      channel,
		"app_version":  equipment.AppVersion,
		"user_agent":   c.UserAgent(),
		"ip":           c.ClientIP(),
		"network":      param.network,     // TODO：这个版本暂时不接入该字段
		"refresh_type": param.refreshType, // TODO：这个版本暂时不接入该字段
		"refresh_num":  param.p,
		"create_time":  goutil.TimeNow().Unix(),
		"track_id":     param.trackID,
		"user_feature": param.userFeature,
		"env":          os.Getenv(util.EnvDeploy),
		"scene":        biliai.RecommendSceneLiveTab,
	}

	showList := make([]map[string]interface{}, len(param.Data))
	for i, item := range param.Data {
		showList[i] = map[string]interface{}{
			"id":  item.RoomID,
			"pos": i + 1,
		}
		if !param.isTianmaRecommend {
			// 处理非算法推荐时的资源位来源及类型
			showList[i]["attr"] = recommended.RecommendAttrSupplement
			showList[i]["goto"] = tianma.GotoLive
			if param.isOpLive(item.RoomID) {
				showList[i]["goto"] = tianma.GotoOpLive
			}
		} else {
			// 处理算法推荐时的资源位来源
			if extendInfo, ok := param.algoRecommendRoomInfoMap[item.RoomID]; ok {
				if extendInfo.Goto != "" {
					showList[i]["goto"] = extendInfo.Goto
				}
				if extendInfo.Source != "" {
					showList[i]["source"] = extendInfo.Source
				}
				if extendInfo.AvFeature != "" {
					showList[i]["av_feature"] = extendInfo.AvFeature
					avFeature := new(AlgorithmInterventionAvFeature)
					err := json.Unmarshal([]byte(extendInfo.AvFeature), avFeature)
					if err != nil {
						logger.Errorf("解析算法下发直播间的卡片特征失败: %v", err)
						// PASS
					}
					if avFeature.OperateID != 0 && avFeature.OperateSource != 0 {
						// 算法干预卡特征信息中的 operate_id （live_recommended_elements 表 ID），用于实时更新干预卡每日曝光量
						param.algorithmInterventionAvFeatureOperateID = append(param.algorithmInterventionAvFeatureOperateID, avFeature.OperateID)
					}
				}
				if extendInfo.Attr != 0 {
					showList[i]["attr"] = extendInfo.Attr
				}
				// 算法推荐卡位置发生变更时需要上报
				oldPosition := extendInfo.OldPosition
				if oldPosition != nil && i != *oldPosition-1 {
					showList[i]["old_pos"] = *oldPosition
				}
			} else {
				showList[i]["attr"] = recommended.RecommendAttrOpConfig
				showList[i]["goto"] = tianma.GotoOpLive
			}
		}
	}
	log["showlist"] = showList
	return log
}

func (param *openListParam) listFromTianma(c *handler.Context) error {
	// 获取推荐数据
	res, err := fetchTianmaRecommendFunc(c, param)
	if err != nil {
		return err
	}
	if res == nil {
		return errors.New("请求推荐算法失败")
	}
	param.userFeature = res.UserFeature
	if res.Code == tianma.CodeNoResult || len(res.Data) <= 0 {
		// 进入对照组的设备算法侧会下发 code 为 -77，此时直接返回，走后续的兜底逻辑
		return nil
	}
	// 构建直播间的扩展信息
	param.buildAlgoRecommendRoomInfo(res)
	if err = param.fetchRoomsInfo(res.Data); err != nil {
		return err
	}
	// 标识本次请求由算法下发
	param.isTianmaRecommend = true
	return nil
}

var fetchTianmaRecommendFunc = fetchTianmaRecommend

// 获取推荐数据
// uat 测试阶段暂时使用 mock 的数据
func fetchTianmaRecommend(c *handler.Context, param *openListParam) (*tianma.RecommendResult, error) {
	requestParams := param.newRecommendParams(c)
	if requestParams == nil {
		return nil, errors.New("构建 tianma 推荐算法请求入参失败")
	}
	if !goutil.IsProdEnv() {
		// WORKAROUND: UAT 测试阶段使用 redis 的测试直播间
		res := mockTianMaResFromCache(*requestParams)
		return res, nil
	}
	// 获取推荐数据
	client := tianma.NewClient()
	res, err := client.Recommend(*requestParams)
	if err != nil {
		return nil, err
	}
	return res, nil
}

// uat 测试阶段使用 mock 的数据
func mockTianMaResFromCache(param tianma.RecommendParams) *tianma.RecommendResult {
	key := keys.TestLiveTabRecommendRoomList1.Format(param.LiveTabScene)
	// 获取测试直播间 ID 列表，直播间列表是逗号分割的字符串
	roomIDs, err := service.Redis.Get(key).Result()
	if err != nil {
		logger.Errorf("获取 TestLiveTabRecommendRoomList1 失败: %v", err)
		return nil
	}
	// 根据 roomIDs 获取直播间信息，并返回 10 条
	roomIDsList := strings.Split(roomIDs, ",")
	roomIDsInt64 := make([]int64, 0, len(roomIDsList))
	for _, id := range roomIDsList {
		if idInt, err := strconv.ParseInt(id, 10, 64); err == nil {
			roomIDsInt64 = append(roomIDsInt64, idInt)
		}
	}
	rooms, err := recommended.ListByRoomIDs(roomIDsInt64)
	if err != nil {
		logger.Errorf("获取直播间聚合查询失败: %v", err)
		return nil
	}
	var items []tianma.RecommendItem
	for i, r := range rooms {
		var gotoType tianma.Goto
		if i == 3 { // 第四个数据特殊处理
			gotoType = tianma.GotoOpLive
		} else {
			gotoType = tianma.GotoLive
		}
		item := tianma.RecommendItem{
			ID:        r.RoomID,
			Goto:      gotoType,
			Source:    "mock_source",
			AVFeature: "{\"operate_id\":1,\"operate_source\":2}",
			TrackID:   "mock_track_id",
		}
		items = append(items, item)
	}

	return &tianma.RecommendResult{
		UserFeature: "mock_user_feature",
		Data:        items,
	}
}

func (param *openListParam) initPagination() error {
	if param.roomSimpleList == nil {
		param.roomSimpleList = new(roomSimpleList)
	}
	param.Pagination = goutil.MakePagination(liveRoomCount, param.p, param.pageSize)
	if !param.Pagination.Valid() {
		param.Data = make([]*room.Simple, 0)
		param.isTianmaRecommend = false
		return errors.New("分页无效")
	}
	return nil
}

func (param *openListParam) buildAlgoRecommendRoomInfo(res *tianma.RecommendResult) {
	param.algoRecommendRoomInfoMap = make(map[int64]*AlgoRecommendRoomInfo, len(res.Data))
	for i, item := range res.Data {
		if item.ID == 0 {
			continue
		}
		if param.trackID == "" {
			param.trackID = item.TrackID
		}
		// 只有算法侧推荐卡需要记录额外信息
		param.algoRecommendRoomInfoMap[item.ID] = &AlgoRecommendRoomInfo{
			AvFeature:   item.AVFeature,
			Attr:        recommended.RecommendAttrTianma,
			Goto:        item.Goto,
			Source:      item.Source,
			OldPosition: goutil.NewInt(i + 1),
		}
	}
}

// 获取直播间信息
func (param *openListParam) fetchRoomsInfo(data []tianma.RecommendItem) error {
	queryIDs := make([]int64, 0, len(data))
	for i := range data {
		if data[i].ID == 0 {
			continue
		}
		queryIDs = append(queryIDs, data[i].ID)
	}
	rooms, err := recommended.ListByRoomIDs(queryIDs)
	if err != nil {
		param.isTianmaRecommend = false
		return err
	}
	if param.roomSimpleList == nil {
		param.roomSimpleList = new(roomSimpleList)
	}
	param.Data = rooms
	return nil
}

func (param *openListParam) newRecommendParams(c *handler.Context) *tianma.RecommendParams {
	userID := c.UserID()
	equipment := c.Equip()
	buvid := equipment.BUVID
	if buvid == "" {
		logger.WithFields(logger.Fields{"equip_id": equipment.EquipID, "ip": c.ClientIP(), "user_id": userID}).Error("buvid 为空")
		return nil
	}
	persona, sex := biliai.GetUserPersonaAndSex(param.uc, userID, equipment.EquipID, buvid)
	locationInfo := recommended.GetLocationInfo(c)
	reqParams := &tianma.RecommendParams{
		Cmd:            tianma.RecommendCmdLiveTab,
		MID:            userID,
		Buvid:          buvid,
		RequestCnt:     biliai.MaxTopRoomCount,
		Timeout:        biliai.DefaultTimeout,
		DisplayID:      param.p,                  // TODO：当前版本使用页码作为刷新次数，需要和算法侧对齐
		FreshType:      int32(param.refreshType), // TODO：客户端暂时不在当前版本支持这个字段，使用 -1 作为请求参数
		Chid:           c.Request().Header.Get("channel"),
		Model:          equipment.DeviceModel,
		Platform:       biliai.GetPlatform(equipment),
		Version:        equipment.AppVersion,
		Network:        recommended.GetNetworkType(param.network), // TODO：客户端暂时不在当前版本支持这个字段，使用 -1 作为请求参数
		Country:        locationInfo.CountryName,
		Province:       locationInfo.RegionName,
		City:           locationInfo.CityName,
		Sex:            sex,
		Persona:        persona,
		FirstLoginTime: biliai.UnknownIntValue, // TODO: 首次登录时间暂时传 -1，后续需要传入正常值
		TS:             goutil.TimeNow().Unix(),
		LiveTabScene:   param.getAlgorithmLiveTabScene(),
	}
	// 查询需要强插的数据（排除热 4 和热 6）
	param.findRecommendRoom(true)
	operateForcedInsertionItems := make([]tianma.OperateForcedInsertionItem, 0, len(param.recommendRooms)+3)
	if param.lastHourRoom != nil {
		// 小时榜直播间强插至第 2 位
		operateForcedInsertionItems = append(operateForcedInsertionItems, tianma.OperateForcedInsertionItem{
			ID: param.lastHourRoom.RoomID,
			// Position 从 0 开始计算，此处需要减 1
			Position: int64(*param.lastHourRoom.Index) - 1,
		})
	}
	if param.nrRoom != nil {
		// 神话推荐的直播间强插至第 5 位
		operateForcedInsertionItems = append(operateForcedInsertionItems, tianma.OperateForcedInsertionItem{
			ID:       param.nrRoom.RoomID,
			Position: int64(*param.nrRoom.Index) - 1,
		})
	}
	if param.hot6Room != nil {
		// 热 6 推荐直播间强插至第 6 位（需要做频控）
		operateForcedInsertionItems = append(operateForcedInsertionItems, tianma.OperateForcedInsertionItem{
			ID:        param.hot6Room.RoomID,
			Position:  int64(*param.hot6Room.Index) - 1,
			PvControl: tianma.ForcedInsertionPvControlYes,
		})
	}
	if param.recommendRooms != nil {
		// 运营配置的推荐直播间（需要做频控）
		for _, recommendRoom := range param.recommendRooms {
			operateForcedInsertionItems = append(operateForcedInsertionItems, tianma.OperateForcedInsertionItem{
				ID:        recommendRoom.RoomID,
				Position:  int64(*recommendRoom.Index) - 1,
				PvControl: tianma.ForcedInsertionPvControlYes,
			})
		}
	}
	operateForcedInsertionsBytes, err := json.Marshal(operateForcedInsertionItems)
	if err != nil {
		logger.Error("强插位置列表 JSON 序列化失败:", err)
		// PASS
	}
	reqParams.OperateForcedInsertions = string(operateForcedInsertionsBytes)

	// 用户是否开启个性化推荐（默认开启）
	if !param.isEnablePersonalizedRecommend() {
		// 未开启个性化推荐
		reqParams.ClosePersonalizedRecommend = tianma.PersonalizedClose
	}

	return reqParams
}

func (param *openListParam) loadCache() *cacheData {
	if param.p != 1 {
		return nil
	}
	v, ok := service.Cache10s.Get(param.cacheKey)
	if !ok {
		return nil
	}
	var resp cacheData
	err := json.Unmarshal(v.([]byte), &resp)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &resp
}

func (param *openListParam) findSubCatalogIDs() error {
	if param.catalogID == 0 {
		return nil
	}
	subCatalogs, err := catalog.LiveSubCatalogs(true)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 目前一个父分区最大有 6 个子分区，额外预留 2 个内存，凑够 8 个
	param.subCatalogIDs = make([]int64, 0, 8)
	for _, sc := range subCatalogs {
		if sc.ParentID == param.catalogID {
			param.subCatalogIDs = append(param.subCatalogIDs, sc.ID)
		}
	}
	return nil
}

func (param *openListParam) findList() error {
	var filter bson.M

	switch param.listType {
	case openListTypeNova:
		filter = bson.M{
			"statistics.revenue":        bson.M{"$lte": room.NovaRevenueThreshold},       // 收入限制
			"statistics.total_duration": bson.M{"$lte": room.NovaTotalDurationThreshold}, // 直播时长限制
			"room_id":                   bson.M{"$nin": room.OpenListExcludeRoomIDs()},
			"status.open":               room.StatusOpenTrue,
			"limit":                     bson.M{"$exists": false},
		}
	default:
		filter = bson.M{
			"room_id":     bson.M{"$nin": room.OpenListExcludeRoomIDs()},
			"status.open": room.StatusOpenTrue,
			"limit":       bson.M{"$exists": false},
		}

		if param.catalogID != 0 {
			filter["catalog_id"] = bson.M{"$in": param.subCatalogIDs}
		}
		if param.tagID > 0 {
			filter["tag_ids"] = param.tagID
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	count, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.roomSimpleList = new(roomSimpleList)
	param.Pagination = goutil.MakePagination(count, param.p, param.pageSize)
	if !param.Pagination.Valid() {
		param.Data = make([]*room.Simple, 0)
		return nil
	}
	mongoOpt := param.Pagination.SetFindOptions(nil)

	switch param.listType {
	case OpenListTypeRecommend:
		mongoOpt = mongoOpt.SetSort(room.SortByOpenAccumulation)
	case openListTypeNew:
		mongoOpt = mongoOpt.SetSort(room.SortByOpenTime)
	case OpenListTypeHot, openListTypeNova:
		fallthrough
	default:
		mongoOpt = mongoOpt.SetSort(room.SortByScore)
	}

	param.Data, err = room.ListSimples(filter, mongoOpt, param.opt)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.listType == OpenListTypeHot &&
		param.p == 1 &&
		param.pageSize >= recommendLimit+1 {
		// 设置关播时推荐缓存
		length := len(param.Data)
		if length > recommendLimit+1 {
			length = recommendLimit + 1
		}
		dst := make([]*room.Simple, length)
		// NOTICE: 只复制了数组，并未深度复制
		copy(dst, param.Data)
		// TODO: 缓存需要支持 JSON 序列化
		key := keys.KeyChatroomRecommend1.Format(param.catalogID)
		service.Cache10s.Set(key, dst, 0)
	}
	return nil
}

// findLastHourTop3 查询上小时榜推荐的用户
func (param *openListParam) findLastHourTop3() []int64 {
	// 只有热门或推荐才查询上小时榜推荐
	if (param.listType != OpenListTypeHot && param.listType != OpenListTypeRecommend) || param.catalogID != 0 {
		return nil
	}
	lastHourTop3, err := usersrank.ListLastHourTop3CreatorIDs()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return lastHourTop3
}

// findRecommendRoom 查询推荐房间
// 上小时榜排名：第二，即使热度更高也会固定第二
// 贵族推荐最低排名：热门列表第五，分区第一
func (param *openListParam) findRecommendRoom(filters ...bool) {
	if param.tagID != 0 {
		// TODO: tag 暂不支持推荐
		return
	}
	// 是否是天马推荐算法强插
	isTianmaRecommendInsertion := len(filters) != 0 && filters[0]
	if param.p != 1 || param.listType == openListTypeNew || (!isTianmaRecommendInsertion && len(param.Data) == 0) {
		// 不是第一页不查，最新不查，非算法强插时第一页没数据不查
		return
	}
	rec, err := liverecommendedelements.FindOpenList(recommendedSquareType(param.listType, param.catalogID), param.catalogID, isTianmaRecommendInsertion)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// roomIDs 包含运营配置的推荐直播间 + 神话推荐 + 热 6
	roomIDs := make([]int64, 0, 2+len(rec))
	// mapRoomIDIdx 运营配置的推荐直播间，room_id 到推荐位置的对应关系，推荐位置从 1 开始
	mapRoomIDIdx := make(map[int64]int, len(rec))
	for i := range rec {
		roomIDs = append(roomIDs, rec[i].ElementID)
		mapRoomIDIdx[rec[i].ElementID] = rec[i].Sort
	}
	var hot6LiveRecommend *liverecommendedelements.Model
	shouldForceInsertHot6 := isTianmaRecommendInsertion &&
		config.Conf.Params.LiveFeed.PositionForceInsertionSwitch &&
		(param.listType == OpenListTypeHot || param.listType == OpenListTypeRecommend) &&
		param.catalogID == 0
	if shouldForceInsertHot6 {
		// 若为天马推荐算法强插且强插热 6 开关开启，则需要强插热 6
		hot6LiveRecommend, err = liverecommendedelements.FindActiveHot6()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	if hot6LiveRecommend != nil {
		roomIDs = append(roomIDs, hot6LiveRecommend.ElementID)
	}

	var nr *livenoblerecommend.NobleRecommend
	// 新星没有神话推荐
	if param.listType != openListTypeNova {
		nr, err = livenoblerecommend.CurrentRecommend()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	if nr != nil {
		roomIDs = append(roomIDs, nr.RoomID)
	}

	lastHourTop3 := param.findLastHourTop3()

	// 因为不仅根据 roomID 查询房间，所以在查询房间后排除不在开播列表显示的房间
	type m bson.M
	filter := m{"status.open": room.StatusOpenTrue}
	switch {
	case len(lastHourTop3) > 0 && len(roomIDs) > 0:
		// 热门
		filter["$or"] = []m{
			{"creator_id": m{"$in": lastHourTop3}},
			{"room_id": m{"$in": sets.Uniq(roomIDs)}},
		}
	case len(lastHourTop3) > 0:
		// 热门
		filter["creator_id"] = m{"$in": lastHourTop3}
	case len(roomIDs) > 0:
		// 分区、新星的情况
		filter["room_id"] = m{"$in": roomIDs}
		if param.listType == openListTypeNova {
			filter["statistics.revenue"] = m{"$lte": room.NovaRevenueThreshold}
			filter["statistics.total_duration"] = m{"$lte": room.NovaTotalDurationThreshold}
		}
	default:
		return
	}
	if param.catalogID != 0 {
		filter["catalog_id"] = m{"$in": param.subCatalogIDs}
	}
	// rooms 包括运营手动配置的推荐直播间，神话推荐和上小时榜的数据
	rooms, err := room.ListSimples(filter, options.Find().SetSort(room.SortByOpenScore), param.opt)
	if err != nil {
		filterJSON, err := json.Marshal(filter)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		logger.WithFields(logger.Fields{
			"filter":     string(filterJSON),
			"user_id":    param.userID,
			"catalog_id": param.catalogID,
			"list_type":  param.listType,
			"tag_id":     param.tagID,
		}).Error(err)
		return
	}
	if len(rooms) == 0 {
		return
	}
	excludeRoomIDs := room.OpenListExcludeRoomIDs()
	param.recommendRooms = make([]*room.Simple, 0, len(mapRoomIDIdx))
	roomMap := make(map[int64]*room.Simple, len(rooms)) // map[CreatorID]*room.Simple
	for i := range rooms {
		// 排除不在开播列表显示的房间
		if goutil.HasElem(excludeRoomIDs, rooms[i].RoomID) {
			continue
		}

		roomMap[rooms[i].CreatorID] = rooms[i]

		// 运营手动配置的推荐直播间
		if index, ok := mapRoomIDIdx[rooms[i].RoomID]; ok {
			r := *rooms[i]
			r.SetIndex(index)
			param.recommendRooms = append(param.recommendRooms, &r)
		}

		// 神话推荐
		if nr != nil && rooms[i].RoomID == nr.RoomID {
			r := *rooms[i]
			if param.catalogID == 0 {
				// 热门分区保底第五，命中算法则固定第五
				r.SetIndex(5)
			} else {
				// 分区第一
				r.SetIndex(1)
			}
			param.nrRoom = &r

			// 设置神话推荐图标
			if nr.IsHighness {
				param.iconURLs[2] = map[int64]string{
					r.RoomID: service.Storage.Parse(config.Conf.Params.NobleParams.RecommendHighnessListIcon),
				}
			} else {
				param.iconURLs[2] = map[int64]string{
					r.RoomID: service.Storage.Parse(config.Conf.Params.NobleParams.RecommendListIcon),
				}
			}
		}

		// 热 6 推荐直播间
		if hot6LiveRecommend != nil && rooms[i].RoomID == hot6LiveRecommend.ElementID {
			r := *rooms[i]
			r.SetIndex(6)
			param.hot6Room = &r
		}
	}

	rankParam, err := params.FindRank()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	icons := rankParam.LastHourRecommendIcons(goutil.TimeNow())
	// 上小时榜
	for i := range lastHourTop3 {
		if top := roomMap[lastHourTop3[i]]; top != nil {
			r := *top
			r.SetIndex(recommendLastHourIndex) // 上小时榜固定第二位置
			param.lastHourRoom = &r

			// 设置上小时榜图标
			param.iconURLs[1] = map[int64]string{
				r.RoomID: service.Storage.Parse(icons[i]),
			}
			break
		}
	}
}

// assignRecommend 添加推荐房间到列表中
/*
	排序依照的规则：
	1. 神话推荐保底，上小时榜固定，运营配置的推荐直播间固定
	2. 固定和保底的逻辑优先
	3. 运营配置的开播列表优先级弱于神话推荐和上小时榜第一
*/
func (param *openListParam) assignRecommend() {
	if param.lastHourRoom == nil && param.nrRoom == nil && len(param.recommendRooms) == 0 {
		return
	}
	mapRoomIDIdx := make(map[int64]int, 1+len(param.recommendRooms))
	mapIdxRoomID := make(map[int]int64, 1+len(param.recommendRooms))
	fixedIdxRooms := make([]*room.Simple, 0, 1+len(param.recommendRooms))
	addFixedIdxRoom := func(index int, r *room.Simple) {
		mapRoomIDIdx[r.RoomID] = index
		mapIdxRoomID[index] = r.RoomID
		r.SetIndex(index)
		fixedIdxRooms = append(fixedIdxRooms, r)
	}
	if param.lastHourRoom != nil {
		addFixedIdxRoom(*param.lastHourRoom.Index, param.lastHourRoom)
	}
	// 将固定推荐位房间插入 fixIdxRooms
	for _, r := range param.recommendRooms {
		if mapRoomIDIdx[r.RoomID] != 0 {
			// 该房间已被设置过固定推荐位，跳过
			continue
		}
		idx := *r.Index
		for {
			if mapIdxRoomID[idx] != 0 {
				// 排名被占用，固定位下移一位
				logger.WithField("idx", idx).Error("开播列表推荐位异常，同一推荐位有多个房间")
				idx++
				continue
			}
			if param.nrRoom != nil && param.nrRoom.RoomID == r.RoomID {
				// 如果某房间同时有神话推荐和固定位推荐，以位置更高的为准
				if *r.Index >= *param.nrRoom.Index {
					// 神话推荐位置更高，跳过
					break
				}
				// 固定位更高，将神话推荐删除，后将该房间设置如固定推荐位
				param.nrRoom = nil
			}
			addFixedIdxRoom(idx, r)
			break
		}
	}
	// 根据位置排序
	sort.Slice(fixedIdxRooms, func(i, j int) bool {
		return mapRoomIDIdx[fixedIdxRooms[i].RoomID] < mapRoomIDIdx[fixedIdxRooms[j].RoomID]
	})
	// 检查神话推荐是否被固定推荐位占用，被占用前进一位
	if param.nrRoom != nil {
		idx := *param.nrRoom.Index
		for ; idx > 1; idx-- {
			if mapIdxRoomID[idx] == 0 {
				break
			}
		}
		*param.nrRoom.Index = idx
	}
	// 被占满后，固定推荐位在插入时需要偏移
	newData := make([]*room.Simple, 0, 1+len(param.Data)+len(fixedIdxRooms))
	addNewData := func(r *room.Simple) {
		newData = append(newData, r)
		mapRoomIDIdx[r.RoomID] = len(newData)
	}
	for {
		if len(param.Data) == 0 && len(fixedIdxRooms) == 0 && param.nrRoom == nil {
			break
		}
		curIdx := len(newData) + 1
		if param.nrRoom != nil {
			if mapRoomIDIdx[param.nrRoom.RoomID] != 0 {
				// 更高位置已进入 newData
				param.nrRoom = nil
				continue
			}
			if curIdx >= *param.nrRoom.Index || (len(param.Data) == 0 && len(fixedIdxRooms) == 0) {
				addNewData(param.nrRoom)
				param.nrRoom = nil
				continue
			}
		}
		if len(fixedIdxRooms) != 0 {
			r := fixedIdxRooms[0]
			// fixedIdxRooms 中的房间总是在 mapRoomIDIdx, 不获取房间 idx
			if curIdx >= *r.Index || len(param.Data) == 0 {
				addNewData(r)
				fixedIdxRooms = fixedIdxRooms[1:]
				continue
			}
		}
		if len(param.Data) != 0 {
			r := param.Data[0]
			// 不进入 newData 也从出队
			param.Data = param.Data[1:]
			// 房间位置未被预定或插入
			if mapRoomIDIdx[r.RoomID] == 0 {
				addNewData(r)
			}
		}
	}
	param.Data = newData
}

// findRecommendIconURL 查询推荐图标
func (param *openListParam) findRecommendIconURL() {
	roomIDs := make([]int64, 0, len(param.Data))
	for i := range param.Data {
		roomIDs = append(roomIDs, param.Data[i].RoomID)
	}

	icons, err := liverecommendedelements.FindRoomIcon(roomIDs)
	if err != nil {
		logger.Error(err)
		return
	}
	param.iconURLs[0] = make(map[int64]string, len(icons))
	for i := range icons {
		param.iconURLs[0][icons[i].RoomID] = icons[i].IconURL
	}
}

// assignIconURL 设置推荐图标
func (param *openListParam) assignIconURL() {
	param.findRecommendIconURL()
	for i := range param.Data {
		// 先获取高优先级的图标，获取到了跳出循环
		for j := len(param.iconURLs) - 1; j >= 0; j-- {
			if len(param.iconURLs[j]) == 0 {
				continue
			}
			iconURL, ok := param.iconURLs[j][param.Data[i].RoomID]
			if ok {
				param.Data[i].IconURL = iconURL
				break
			}
		}
	}
}

func (param *openListParam) getAlgorithmLiveTabScene() int64 {
	// 标识二级分区场景（新星：-1；热门：0；其他二级分区：分区 ID）
	if param.listType == openListTypeNova {
		return -1
	}
	// 推荐列表与热门列表一样处理
	return param.catalogID
}

// isOpLive 检查是否是直播运营干预的直播间
func (param *openListParam) isOpLive(roomID int64) bool {
	if param.lastHourRoom != nil && roomID == param.lastHourRoom.RoomID {
		return true
	}
	if param.nrRoom != nil && roomID == param.nrRoom.RoomID {
		return true
	}
	for _, v := range param.recommendRooms {
		if v.RoomID == roomID {
			return true
		}
	}
	return false
}

func recommendedSquareType(listType int64, catalogID int64) int {
	switch listType {
	case OpenListTypeHot, OpenListTypeRecommend:
		if catalogID != 0 {
			return liverecommendedelements.SquareTypeCatalog
		}
		return liverecommendedelements.SquareTypeHot
	case openListTypeNova:
		return liverecommendedelements.SquareTypeNova
	default:
		panic(fmt.Sprintf("unsupported listType: %d", listType))
	}
}

// 检查是否处于兜底状态
func isInFallbackState(buvid string) (bool, error) {
	key := buildFallbackKey(buvid)
	ok, err := service.Redis.Exists(key).Result()
	if err != nil {
		return false, err
	}
	return ok != 0, nil
}

func setFallbackState(buvid string, duration time.Duration) error {
	key := buildFallbackKey(buvid)
	return service.Redis.Set(key, "1", duration).Err()
}

func cleanFallbackState(buvid string) error {
	key := buildFallbackKey(buvid)
	return service.Redis.Del(key).Err()
}

// buildFallbackKey 根据设备号生成 key，该 key 用于获取兜底缓存数据
func buildFallbackKey(buvid string) string {
	return keys.FallbackKey1.Format(buvid)
}

// getUserConfig 获取用户配置
func (param *openListParam) getUserConfig() (*userapi.MUserConfig, error) {
	if param.cachedUserConfig != nil {
		return param.cachedUserConfig, nil
	}

	userConfig, err := userapi.GetUserConfig(param.uc, param.userID, param.buvid)
	if err != nil {
		return nil, err
	}

	param.cachedUserConfig = userConfig
	return userConfig, nil
}

// isEnablePersonalizedRecommend 判断用户是否开启个性化推荐开关（默认开启）
func (param *openListParam) isEnablePersonalizedRecommend() bool {
	userConfig, err := param.getUserConfig()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": param.userID, "buvid": param.buvid}).Errorf("获取用户配置出错: %v", err)
		return true
	}

	if userConfig != nil && userConfig.AppConfig.PersonalizedRecommend != nil {
		return *userConfig.AppConfig.PersonalizedRecommend == userapi.ConfigEnable
	}

	return true
}

// isShowRecommendTab 判断能否展示推荐 tab
func (param *openListParam) isShowRecommendTab(c *handler.Context) bool {
	e := c.Equip()
	// WORKAROUND: 安卓 < 6.3.5 和 iOS < 6.3.5 的版本不支持下发热门分区，因此直接返回常规分区
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{Android: "6.3.5", IOS: "6.3.5"}) {
		return false
	}

	if !param.isEnablePersonalizedRecommend() {
		return false
	}

	// 只有实验组 A 才展示推荐 tab
	experimentGroup := tab.GetLiveFeedExperimentGroup(param.buvid, param.userID)
	return experimentGroup == tab.ExperimentGroupA
}

// isHotTabUseAlgorithm 判断热门 tab 是否使用算法推荐
func (param *openListParam) isHotTabUseAlgorithm(c *handler.Context) bool {
	e := c.Equip()
	// WORKAROUND: 安卓 < 6.3.6 和 iOS < 6.3.6 的版本上报的埋点数据有误，因此不使用算法推荐
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{Android: "6.3.6", IOS: "6.3.6"}) {
		return false
	}

	if !param.isEnablePersonalizedRecommend() {
		return false
	}

	// 实验组 B 的热门 tab 使用算法推荐
	experimentGroup := tab.GetLiveFeedExperimentGroup(param.buvid, param.userID)
	return experimentGroup == tab.ExperimentGroupB
}
