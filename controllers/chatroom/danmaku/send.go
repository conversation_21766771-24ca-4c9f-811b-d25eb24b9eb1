package danmaku

import (
	"fmt"
	"html"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livemembers"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/liverevenues"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/bilibili/gaia"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// danmakuMsgTemplate 付费弹幕消息模板
const danmakuMsgTemplate = `<font color="${highlight_color}">${username}</font><font color="${normal_color}">：${message}</font>`

type sendParam struct {
	RoomID     int64  `form:"room_id" json:"room_id"`
	Message    string `form:"message" json:"message"`
	GoodsID    int64  `form:"goods_id" json:"goods_id"`
	BubbleID   int64  `form:"bubble_id" json:"bubble_id"`
	EffectType int64  `form:"effect_type" json:"effect_type"`

	c              *handler.Context
	user           *liveuser.Simple
	userVip        *vip.UserVip
	room           *room.Room
	goods          *livegoods.LiveGoods
	bubble         *bubble.Simple
	danmakuBubble  *bubble.Simple
	isRoomAdmin    bool
	roomMedal      *livemedal.LiveMedal
	balance        *userapi.BalanceResp // 交易响应信息
	broadcastElems []*userapi.BroadcastElem
}

type sendResp struct {
	MsgID         string             `json:"msg_id"`
	User          *liveuser.Simple   `json:"user"`
	Bubble        *bubble.Simple     `json:"bubble"`
	DanmakuBubble *danmakuBubbleInfo `json:"danmaku_bubble"`
	Balance       userapi.BuyBalance `json:"balance"`
}

type sendDanmakuBroadcast struct {
	Type           string             `json:"type"`
	Event          string             `json:"event"`
	RoomID         int64              `json:"room_id"`
	MsgID          string             `json:"msg_id,omitempty"`
	Price          int                `json:"price,omitempty"`
	Message        string             `json:"message"`
	Time           int64              `json:"time,omitempty"` // 单位：毫秒
	User           *liveuser.Simple   `json:"user"`
	Bubble         *bubble.Simple     `json:"bubble,omitempty"`
	DanmakuBubble  *danmakuBubbleInfo `json:"danmaku_bubble,omitempty"`
	CurrentRevenue int64              `json:"current_revenue,omitempty"`
}

type danmakuBubbleInfo struct {
	ImageURL string `json:"image_url"`
	Float    int    `json:"float,omitempty"`
	Shine    int    `json:"shine,omitempty"`
	ShowIcon int    `json:"show_icon,omitempty"`

	normalColor    string
	highlightColor string
}

func newDanmakuBubbleInfo(user *liveuser.Simple, b *bubble.Simple) *danmakuBubbleInfo {
	return &danmakuBubbleInfo{
		ImageURL:       b.ImageURL,
		Shine:          goutil.BoolToInt(user.Level() >= 190),
		ShowIcon:       goutil.BoolToInt(user.Level() >= 180),
		normalColor:    b.NormalColor,
		highlightColor: b.HighlightColor,
	}
}

// isOpen 付费弹幕玩法是否开放
func isOpen() (int64, bool) {
	nowUnix := goutil.TimeNow().Unix()
	var openTimeUnix int64
	config.GetAB("open_danmaku_time", &openTimeUnix)
	return openTimeUnix, nowUnix >= openTimeUnix
}

// ActionDanmakuSend 发送直播间付费弹幕
/**
 * @api {post} /api/v2/chatroom/danmaku/send 发送直播间付费弹幕
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {String} message 弹幕信息
 * @apiParam {Number} goods_id 商品 ID
 * @apiParam {Number} bubble_id 气泡 ID
 * @apiParam {Number} effect_type 动画效果类型
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg_id": "33ea9d12-2253-46fb-a630-a23a6b8fa55e",
 *       "user": {
 *         "user_id": 3457111,
 *         "username": "小蜜蜂の",
 *         "iconurl": "https://static-test.maoercdn.com/profile/201912/25/30e551d9a3990ba3c49070a9ba687c5c165120.png",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#f45b41"
 *           },
 *           {
 *             "type": "identity_badge", // 身份铭牌
 *             "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *             "appearance_id": 10001 // 图标 ID
 *           }
 *         ]
 *       },
 *       "bubble": {
 *         "type": "message",
 *         "image_url": "https://static-test.maoercdn.com/live/bubbles/message/10011_36_36_36_36.png"
 *       },
 *       "danmaku_bubble": { // 弹幕气泡信息
 *         "image_url": "https://static-test.maoercdn.com/live/bubbles/message/10011_36_36_36_36.png",
 *         "float": 0, // 如果 float 为 1，则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *         "shine": 0, // 是否闪光，1 为闪光，0 为不闪光，字段不存在则默认不闪光
 *         "show_icon": 1 // 是否展示头像，1 为展示，0 为不展示，字段不存在则默认不展示
 *       },
 *       "balance": {
 *         "balance": 11479968,
 *         "live_noble_balance": 283548,
 *         "live_noble_balance_status": 1
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 直播间 WS 聊天消息
 *   {
 *     "type": "message",
 *     "event": "new",
 *     "room_id": 1,
 *     "msg_id": "33ea9d12-2253-46fb-a630-a23a6b8fa55e",
 *     "message": "这是一个测试消息",
 *     "user": {
 *       "user_id": 10000000052,
 *       "username": "jesse_tang",
 *       "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 30
 *         },
 *         {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }
 *       ]
 *     },
 *     "bubble": {
 *       "type": "message",
 *       "image_url": "https://static-test.maoercdn.com/live/bubbles/message/10011_36_36_36_36.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 直播间 WS 付费弹幕消息
 *   {
 *     "type": "message",
 *     "event": "danmaku",
 *     "room_id": 1,
 *     "message": "这是一个测试消息", // html 格式
 *     "price": 50, // 价格，客户端计算主播实时收益使用，单位钻石
 *     "time": 1710831560376, // 弹幕发送时间，单位：毫秒
 *     "user": {
 *       "user_id": 10000000052,
 *       "username": "jesse_tang",
 *       "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "staff",
 *           "name": "超管",
 *           "color": "#f45b41"
 *         },
 *         {
 *           "type": "level",
 *           "level": 30
 *         },
 *         {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }
 *       ]
 *     },
 *     "danmaku_bubble": {
 *       "image_url": "https://static-test.maoercdn.com/live/bubbles/message/10011_36_36_36_36.png",
 *       "float": 0, // 如果 float 为 1，则是悬停气泡，不存在或为 0 则是正常飘屏气泡
 *       "shine": 0, // 是否闪光，1 为闪光，0 为不闪光，字段不存在则默认不闪光
 *       "show_icon": 1 // 是否展示头像，1 为展示，0 为不展示，字段不存在则默认不展示
 *     },
 *     "current_revenue": 1015 // 客户端计算本场榜使用
 *   }
 */
func ActionDanmakuSend(c *handler.Context) (handler.ActionResponse, error) {
	if openTimeUnix, ok := isOpen(); !ok {
		return nil, actionerrors.ErrGlobalToast(
			fmt.Sprintf("本功能将于 %s 开放", time.Unix(openTimeUnix, 0).Format("01.02 15:04")),
		)
	}
	cfg, err := params.FindGlobal()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if cfg.IsUnderMaintenance() {
		return nil, actionerrors.ErrGlobalPopupPromptMsg(cfg.Maintain.MaintainMsg())
	}

	param, err := newSendParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	resp, err := param.send()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newSendParam(c *handler.Context) (*sendParam, error) {
	var param *sendParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID <= 0 || param.Message == "" || util.UTF8Width(param.Message)/2 > 20 ||
		param.GoodsID <= 0 || param.BubbleID <= 0 || param.EffectType < 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param *sendParam) check() (err error) {
	param.room, err = room.Find(param.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	if !param.room.IsOpen() {
		return actionerrors.ErrClosedRoom
	}
	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.c.UserID()},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.room.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.user == nil {
		return actionerrors.ErrUserNotFound
	}
	ok, err := service.Redis.SetNX(keys.LockDanmakuSendLimit1.Format(param.user.UserID()), 1, 3*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("操作太快啦，稍等一下吧~")
	}
	_, userVip, err := userstatus.UserGeneral(param.user.UserID(), param.c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if userVip != nil && userVip.IsActive() {
		param.userVip = userVip
	}
	// 是否被禁言
	mute, err := livemembers.IsMute(param.c.UserID(), param.room.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if mute.GlobalMute {
		return actionerrors.ErrGlobalMuteUser
	}
	if mute.RoomMute {
		return actionerrors.NewErrForbidden("您已被禁言，无法在本直播间发送弹幕")
	}
	// 是否被封禁
	ban, err := userstatus.FindBanned(param.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if ban != nil {
		return actionerrors.NewErrForbidden("当前用户已被封禁")
	}
	// 被主播拉黑，无法发送弹幕
	blocked, err := blocklist.IsBlocked(param.room.CreatorID, param.c.UserID())
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return actionerrors.NewErrBlockUser("您当前无法在本直播间内进行此操作")
	}
	if param.room.IsOwner(param.user) {
		return actionerrors.ErrParamsMsg("主播不可发弹幕")
	}
	param.isRoomAdmin, err = livemembers.IsRoomAdmin(param.room.OID, param.user.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	param.roomMedal, err = livemedal.FindOwnedMedal(param.user.UserID(), param.room.RoomID, livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	limit := utils.SpeakLimit{
		User:        param.user,
		Room:        param.room,
		IsRoomAdmin: param.isRoomAdmin,
		RoomMedal:   param.roomMedal,
	}
	err = limit.Check()
	if err != nil {
		return err
	}
	param.goods, err = livegoods.Find(param.GoodsID, livegoods.GoodsTypeDanmaku)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.goods == nil {
		return actionerrors.ErrGoodsNotFound
	}
	dBubble, err := bubble.FindOne(param.BubbleID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if dBubble == nil || dBubble.Type != bubble.TypeDanmaku {
		return actionerrors.ErrNotFound("弹幕气泡不存在")
	}
	if !isShowDanmakuBubble(param.user, *dBubble, false) {
		return actionerrors.NewErrForbidden("当前用户无法使用该气泡")
	}
	param.danmakuBubble = dBubble.NewSimple()
	param.bubble, err = userappearance.FindMessageBubble(param.user.UserID())
	if err != nil {
		logger.WithField("user_id", param.user.UserID()).Errorf("send danmaku find user message bubble error: %v", err)
		// PASS
	}
	return nil
}

func (param *sendParam) send() (*sendResp, error) {
	msgID := util.BuildMsgID(param.user.UserID(), param.room.RoomID, param.Message,
		config.Conf.Params.General.FeedbackKey, param.c.Equip().OS)
	// 文字违规和敏感信息检查
	input := gaia.ParamLiveIM{
		ParamFilter: gaia.ParamFilter{
			UserID:    param.c.UserID(),
			EquipID:   param.c.EquipID(),
			IP:        param.c.ClientIP(),
			API:       param.c.Request().URL.Path,
			UserAgent: param.c.UserAgent(),
			Referer:   param.c.Request().Referer(),
			Content:   param.Message,
		},
		RoomID:        param.RoomID,
		RoomCatalogID: param.room.CatalogID,
		RoomCreatorID: param.room.CreatorID,
		MsgID:         msgID,
		UserIsAdmin:   param.isRoomAdmin,
		UserHasMedal:  param.roomMedal != nil,
		Scene:         gaia.IMSceneDanmaku,
	}
	result, err := userapi.CheckTextIM(param.c.UserContext(), input)
	if err != nil {
		return nil, err
	}
	if result.NotPass {
		return nil, actionerrors.ErrDanmakuMessageIllegal
	}
	if result.HasLabelEvil {
		return nil, actionerrors.ErrDanmakuMessageSensitive
	}

	// 购买付费弹幕并创建订单
	balanceResp, orderID, err := param.buyDanmaku()
	if err != nil {
		return nil, err
	}
	param.balance = balanceResp

	danmaku := newDanmakuBubbleInfo(param.user, param.danmakuBubble)
	danmakuMsg := param.newDanmakuMsg()

	// NOTICE: 付费弹幕不加 PK 榜
	goutil.Go(func() {
		param.incrRoomMsgCount()
		param.incrUserMsgCount()
		param.addRevenue()
		param.addMedalPoint()
		param.addUserContribution()
		param.broadcast(msgID, danmakuMsg, danmaku)
		param.addActivity()
		param.addLiveShow()
		utils.SendLiveSpend(param.user.UserID(), int64(param.goods.Price), param.RoomID)
		param.addRoomPaidUser()
	})

	err = notifymessages.Insert(
		notifymessages.NewDanmaku(param.user.UserID(), param.room.RoomID, danmakuMsg, param.danmakuBubble, param.c.ClientIP()),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	message := &models.Message{
		RoomOID: param.room.OID,
		RoomID:  param.room.RoomID,
		MsgID:   msgID,
		Status:  models.MessageStatusNormal,
		UserID:  param.user.UserID(),
		Message: param.Message,
		Bubble:  param.bubble,
		Danmaku: &models.Danmaku{
			OrderID: orderID,
			Bubble:  param.danmakuBubble,
		},
		IP:         param.c.ClientIP(),
		CreateTime: goutil.TimeNow(),
	}
	_, err = message.Insert()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return &sendResp{
		MsgID:         msgID,
		User:          param.user,
		Bubble:        param.bubble,
		DanmakuBubble: danmaku,
		Balance:       userapi.NewBuyBalance(balanceResp, param.userVip != nil && param.userVip.IsActive()),
	}, nil
}

func (param *sendParam) buyDanmaku() (*userapi.BalanceResp, int64, error) {
	// 创建订单
	// RPC 购买成功后再更新订单状态
	more := &livetxnorder.MoreInfo{
		DanmakuMessage: param.Message,
	}
	if param.room.IsOpen() {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusOpen)
	} else {
		more.OpenStatus = util.NewInt(livetxnorder.OpenStatusClosed)
	}
	param.goods.SellerID = param.room.CreatorID
	order := livetxnorder.NewOrder(param.goods, param.user.UserID(), 0, more)
	err := livetxnorder.LiveTxnOrder{}.DB().Create(order).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id":  param.room.RoomID,
			"user_id":  param.user.UserID(),
			"goods_id": param.GoodsID,
		}).Errorf("创建付费弹幕订单失败：%v", err)
		return nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}

	// 购买付费弹幕
	balanceResp, err := userapi.BuyLiveGoods(
		param.c.UserContext(),
		userapi.BuyLiveGoodsParam{
			BuyerID:    param.user.UserID(),
			ReceiverID: param.room.CreatorID,
			GoodsType:  userapi.GoodsTypeDanmaku,
			Goods: []userapi.LiveGoodsElem{
				{
					ID:    param.goods.ID,
					Title: param.goods.Title,
					Price: param.goods.Price,
					Num:   1,
				},
			},
			Noble:     1, // 可用贵族钻石购买
			UserAgent: param.c.UserAgent(),
			EquipID:   param.c.EquipID(),
			BUVID:     param.c.BUVID(),
			IP:        param.c.ClientIP(),
		},
		param.room.Status.OpenLogID,
	)
	if err != nil {
		return nil, 0, err
	}

	// 更新订单
	err = livetxnorder.LiveTxnOrder{}.DB().
		Where("id = ?", order.ID).
		Updates(map[string]interface{}{
			"status":        livetxnorder.StatusSuccess,
			"tid":           balanceResp.TransactionID,
			"modified_time": goutil.TimeNow().Unix(),
		}).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"room_id":        param.room.RoomID,
			"user_id":        param.user.UserID(),
			"goods_id":       param.goods.ID,
			"order_id":       order.ID,
			"transaction_id": balanceResp.TransactionID,
		}).Errorf("更新付费弹幕订单失败：%v", err)
		return nil, 0, actionerrors.NewErrServerInternal(err, nil)
	}
	return balanceResp, order.ID, nil
}

func (param *sendParam) newDanmakuMsg() string {
	formatParams := map[string]string{
		"username":        html.EscapeString(param.user.Username),
		"message":         strings.ReplaceAll(html.EscapeString(param.Message), " ", "&nbsp;"),
		"highlight_color": "#FFFFFF",
		"normal_color":    "#FFFFFF",
	}
	param.danmakuBubble.AppendFormatParams(formatParams)
	return goutil.FormatMessage(danmakuMsgTemplate, formatParams)
}

func (param *sendParam) incrRoomMsgCount() {
	room.IncrMessageCount(param.room.RoomID)
}

func (param *sendParam) incrUserMsgCount() {
	msgCount := usermeta.IncrMessageCount(param.user.UserID())

	// 当日发三条消息，增加贡献值
	if msgCount == usermeta.DailyMsgCount {
		contributionParams := userstatus.NewAddContributionParams(param.user.UserID(), param.room.RoomID, param.user.Username, userstatus.FromNormal, param.userVip)
		err := contributionParams.AddContribution(300)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

func (param *sendParam) addRevenue() {
	if param.room.IsOwner(param.user) {
		return
	}
	score := int64(param.goods.Price)
	if score <= 0 {
		return
	}

	err := roomsrank.AddRevenue(param.room.RoomID, param.user.UserID(), score, param.room.IsOpen())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	rankChange, err := usersrank.AddRevenue(param.room.CreatorID, param.room.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	} else {
		err = room.NotifyHourRank(rankChange, param.room)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	err = param.room.ReceiveDanmaku(score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = liverevenues.AddDanmakuRevenue(param.user.UserID(), param.room.OID, param.room.RoomID, score)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// addUserContribution 增加用户直播间经验值
func (param *sendParam) addUserContribution() {
	pointAdd := int64(param.goods.Price) * 10 // 1 钻石 = 10 经验
	if param.userVip != nil && param.userVip.Info != nil {
		pointAdd = param.userVip.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.user.UserID(), param.RoomID, param.room.CreatorUsername, userstatus.FromNormal, param.userVip)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

// 添加亲密度
func (param *sendParam) addMedalPoint() {
	if param.room.Medal == nil {
		return
	}
	medalParam := livemedalstats.AddPointParam{
		RoomOID:    param.room.OID,
		RoomID:     param.room.RoomID,
		CreatorID:  param.room.CreatorID,
		UserID:     param.user.UserID(),
		UV:         param.userVip,
		MedalName:  param.room.Medal.Name,
		Type:       livemedal.TypeDanmakuMedalPoint,
		Source:     livemedal.ChangeSourceBuyDanmaku,
		PointAdd:   int64(param.goods.Price),
		Scene:      livemedalpointlog.SceneTypeDanmaku,
		IsRoomOpen: param.room.IsOpen(),
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}

	notifyParam := &liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		User:             param.user,
		BubblePtr:        &param.bubble,
		CreatorUsername:  param.room.CreatorUsername,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func (param *sendParam) addActivity() {
	r := rank.
		NewSyncParam(param.room.RoomID, param.user.UserID(), param.room.CreatorID).
		SetTransaction(param.balance).
		SetOpenLogID(param.room.Status.OpenLogID).
		SetGuildID(param.room.GuildID).
		SetActivityCatalogID(param.room.ActivityCatalogID).
		SetRevenueGoods(rank.AddRevenueTypeDanmaku, int64(param.goods.Price))
	r.AddRankPoint()
	r.SendLiveActivity(param.c.UserContext(), userapi.SyncTypeDanmaku)
}

func (param *sendParam) addLiveShow() {
	liveshow.
		NewSyncLiveShow(param.RoomID, param.user.UserID(), param.room.CreatorID).
		SetPoint(int64(param.goods.Price)).
		Sync()
}

func (param *sendParam) broadcast(msgID string, danmakuMsg string, danmaku *danmakuBubbleInfo) {
	elems := []*userapi.BroadcastElem{
		// 普通消息
		{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: param.room.RoomID,
			Payload: &sendDanmakuBroadcast{
				Type:    liveim.TypeMessage,
				Event:   liveim.EventNew,
				RoomID:  param.room.RoomID,
				MsgID:   msgID,
				Message: param.Message,
				User:    param.user,
				Bubble:  param.bubble,
			},
		},
		// 弹幕消息
		{
			Type:     liveim.IMMessageTypeNormal,
			RoomID:   param.room.RoomID,
			Priority: userapi.BroadcastPriorityPurchased,
			Payload: &sendDanmakuBroadcast{
				Type:           liveim.TypeMessage,
				Event:          liveim.EventDanmaku,
				RoomID:         param.room.RoomID,
				Price:          param.goods.Price,
				Message:        danmakuMsg,
				Time:           goutil.TimeNow().UnixMilli(),
				User:           param.user,
				DanmakuBubble:  danmaku,
				CurrentRevenue: roomsrank.CurrentRevenue(param.room.RoomID, param.user.UserID()),
			},
		},
	}
	param.broadcastElems = append(param.broadcastElems, elems...)
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *sendParam) addRoomPaidUser() {
	chatroom.AddCurrentRoomPaidUser(param.RoomID, param.user.UserID(), param.room.Status.OpenTime)
}
