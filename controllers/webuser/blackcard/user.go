package blackcard

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveblackcard"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type blackCardInfo struct {
	BlackCardList []blackCardItem   `json:"black_card_list"` // 黑卡等级列表，按等级从低到高排序
	UserBlackCard userBlackCardInfo `json:"user_black_card"` // 用户当前黑卡信息
}

type blackCardItem struct {
	Level   int    `json:"level"`    // 黑卡等级
	Title   string `json:"title"`    // 黑卡等级名称
	Price   int64  `json:"price"`    // 开通价格（单位：钻）
	IconURL string `json:"icon_url"` // 黑卡等级图标
	Status  int    `json:"status"`   // 黑卡身份状态：0 未解锁，1 已解锁
	Tip     string `json:"tip"`      // 提示信息
}

type userBlackCardInfo struct {
	Level        int    `json:"level,omitempty"` // 黑卡等级，当前用户无黑卡等级时不下发
	Title        string `json:"title,omitempty"` // 黑卡等级名称，当前用户无黑卡等级时不下发
	Status       int    `json:"status"`          // 黑卡身份状态：0 未解锁，1 已解锁
	CurrentSpend int64  `json:"current_spend"`   // 黑卡有效消费（单位：钻）
}

const (
	blackCardStatusInactive = iota // 黑卡身份状态未解锁
	blackCardStatusActive          // 黑卡身份状态已解锁
)

func (b *blackCardItem) processTipAndStatus(card liveblackcard.LiveBlackCard, userBlackCard *liveuserblackcard.UserBlackCardInfo) {
	if userBlackCard == nil || userBlackCard.Level < card.Level {
		b.Status = blackCardStatusInactive
		b.Tip = fmt.Sprintf("月消费达 %d 钻可解锁", card.Price)
		return
	}
	b.Status = blackCardStatusActive
	if userBlackCard.Level == card.Level {
		b.Tip = fmt.Sprintf("有效期至 %s", time.Unix(userBlackCard.ExpireTime, 0).Format(util.TimeFormatYMDHHMM))
	} else {
		b.Tip = "您已超越该等级"
	}
	return
}

// ActionBlackCardInfo 获取黑卡列表和用户当前黑卡等级信息
/**
 * @api {get} /api/v2/user/black-card/info 获取黑卡列表和用户当前黑卡等级信息
 * @apiVersion 0.1.0
 * @apiGroup black-card
 *
 * @apiDefine user
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "black_card_list": [ // 黑卡等级列表，按等级从低到高排序
 *         {
 *           "level": 1, // 等级
 *           "title": "黑卡 1", // 黑卡等级名称
 *           "price": 100000, // 开通价格（单位：钻）
 *           "icon_url": "https://static.example.com/icon.png", // 黑卡等级图标
 *           "status": 0, // 黑卡身份状态：0 未解锁，1 已解锁
 *           "tip": "月消费达 200000 钻可解锁" // 提示信息
 *         },
 *         {
 *           "level": 2,
 *           "title": "黑卡 2",
 *           "price": 500000,
 *           "icon_url": "https://static.example.com/image.png",
 *           "status": 1,
 *           "tip": "您已超越该等级" // 提示信息
 *         }，
 *         {
 *           "level": 3,
 *           "title": "黑卡 3",
 *           "price": 1000000,
 *           "icon_url": "https://static.example.com/image.png",
 *           "status": 1,
 *           "tip": "有效期至 2025-05-20 00:00" // 提示信息
 *         }
 *       ],
 *       "user_black_card": { // 当前用户黑卡等级信息
 *         "level": 2, // 用户当前黑卡等级，当前用户无黑卡等级时不下发
 *         "title": "黑卡 2", // 用户当前黑卡等级名称，当前用户无黑卡等级时不下发
 *         "status": 1, // 黑卡身份状态：0 未解锁，1 已解锁
 *         "current_spend": 100000 // 黑卡有效消费（单位：钻）
 *       }
 *     }
 *   }
 */
func ActionBlackCardInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	blackCards, err := liveblackcard.ListAllBlackCard()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	userBlackCard, err := liveuserblackcard.FindUserActiveBlackCard(c.UserID())
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	var resp blackCardInfo
	for _, card := range blackCards {
		item := blackCardItem{
			Level:   card.Level,
			Title:   card.Title,
			Price:   card.Price,
			IconURL: card.IconURL,
		}
		item.processTipAndStatus(card, userBlackCard)
		resp.BlackCardList = append(resp.BlackCardList, item)
	}
	resp.UserBlackCard.CurrentSpend, err = userconsumption.FindUserBlackCardSpend(c.UserID())
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if userBlackCard != nil {
		resp.UserBlackCard.Level = userBlackCard.Level
		resp.UserBlackCard.Title = userBlackCard.Title
		resp.UserBlackCard.Status = blackCardStatusActive
	}
	return resp, "", nil
}
