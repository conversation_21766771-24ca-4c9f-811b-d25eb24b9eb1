package blackcard

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/mongodb/userconsumption"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"go.mongodb.org/mongo-driver/bson"
)

func TestTagKeys(t *testing.T) {
	ck := tutil.NewKeyChecker(t, tutil.JSON)
	ck.Check(blackCardInfo{}, "black_card_list", "user_black_card")
	ck.Check(blackCardItem{}, "level", "title", "price", "icon_url", "status", "tip")
	ck.Check(userBlackCardInfo{}, "level", "title", "status", "current_spend")
	ck.CheckOmitEmpty(userBlackCardInfo{}, "level", "title")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, blackCardStatusInactive)
	assert.Equal(1, blackCardStatusActive)
}

func TestActionBlackCardInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试用户无黑卡信息的情况
	testUserID := int64(12)
	require.NoError(liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testUserID).Error)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userconsumption.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	timeNow := goutil.TimeNow()
	_, err = userconsumption.Collection().InsertMany(ctx, []interface{}{
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: timeNow.Add(-2 * time.Second).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: timeNow.Add(-4 * time.Second).Unix()},
		userconsumption.UserConsumption{UserID: testUserID, Consumption: 123, Date: timeNow.Add(-6 * time.Second).Unix()},
	})
	require.NoError(err)

	api := "/api/v2/user/black-card/info"
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	res, _, err := ActionBlackCardInfo(c)
	require.NoError(err)
	data, ok := res.(blackCardInfo)
	require.True(ok)
	require.Len(data.BlackCardList, 4)
	assert.Equal(1, data.BlackCardList[0].Level)
	assert.Equal("星曜 V1", data.BlackCardList[0].Title)
	assert.EqualValues(200000, data.BlackCardList[0].Price)
	assert.Equal("https://static-test.missevan.com/test1.png", data.BlackCardList[0].IconURL)
	assert.Zero(data.BlackCardList[0].Status)
	assert.Equal("月消费达 200000 钻可解锁", data.BlackCardList[0].Tip)
	assert.Equal(2, data.BlackCardList[1].Level)
	assert.Equal("星曜 V2", data.BlackCardList[1].Title)
	assert.EqualValues(500000, data.BlackCardList[1].Price)
	assert.Equal("https://static-test.missevan.com/test2.png", data.BlackCardList[1].IconURL)
	assert.Zero(data.BlackCardList[1].Status)
	assert.Equal("月消费达 500000 钻可解锁", data.BlackCardList[1].Tip)
	assert.Equal(3, data.BlackCardList[2].Level)
	assert.Equal("星曜 V3", data.BlackCardList[2].Title)
	assert.EqualValues(1000000, data.BlackCardList[2].Price)
	assert.Equal("https://static-test.missevan.com/test3.png", data.BlackCardList[2].IconURL)
	assert.Zero(data.BlackCardList[2].Status)
	assert.Equal("月消费达 1000000 钻可解锁", data.BlackCardList[2].Tip)
	assert.Equal(4, data.BlackCardList[3].Level)
	assert.Equal("星曜 V4", data.BlackCardList[3].Title)
	assert.EqualValues(2000000, data.BlackCardList[3].Price)
	assert.Equal("https://static-test.missevan.com/test4.png", data.BlackCardList[3].IconURL)
	assert.Zero(data.BlackCardList[3].Status)
	assert.Equal("月消费达 2000000 钻可解锁", data.BlackCardList[3].Tip)
	assert.Zero(data.UserBlackCard.Level)
	assert.Equal("", data.UserBlackCard.Title)
	assert.Zero(data.BlackCardList[0].Status)
	assert.EqualValues(369, data.UserBlackCard.CurrentSpend)

	// 测试用户有黑卡信息的情况
	infos := []liveuserblackcard.LiveUserBlackCard{
		{
			UserID:      testUserID,
			BlackCardID: 2,
			StartTime:   timeNow.Unix(),
			ExpireTime:  timeNow.Add(time.Minute).Unix(),
		},
		{
			UserID:      testUserID,
			BlackCardID: 3,
			StartTime:   timeNow.Unix(),
			ExpireTime:  timeNow.Add(time.Hour).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))
	res, _, err = ActionBlackCardInfo(c)
	require.NoError(err)
	data, ok = res.(blackCardInfo)
	require.True(ok)
	require.Len(data.BlackCardList, 4)
	assert.Equal(1, data.BlackCardList[0].Level)
	assert.Equal("星曜 V1", data.BlackCardList[0].Title)
	assert.EqualValues(200000, data.BlackCardList[0].Price)
	assert.Equal("https://static-test.missevan.com/test1.png", data.BlackCardList[0].IconURL)
	assert.NotZero(data.BlackCardList[0].Status)
	assert.Equal("您已超越该等级", data.BlackCardList[0].Tip)
	assert.Equal(2, data.BlackCardList[1].Level)
	assert.Equal("星曜 V2", data.BlackCardList[1].Title)
	assert.EqualValues(500000, data.BlackCardList[1].Price)
	assert.Equal("https://static-test.missevan.com/test2.png", data.BlackCardList[1].IconURL)
	assert.NotZero(data.BlackCardList[1].Status)
	assert.Equal("您已超越该等级", data.BlackCardList[1].Tip)
	assert.Equal(3, data.BlackCardList[2].Level)
	assert.Equal("星曜 V3", data.BlackCardList[2].Title)
	assert.EqualValues(1000000, data.BlackCardList[2].Price)
	assert.Equal("https://static-test.missevan.com/test3.png", data.BlackCardList[2].IconURL)
	assert.NotZero(data.BlackCardList[2].Status)
	assert.Equal(fmt.Sprintf("有效期至 %s", timeNow.Add(time.Hour).Format(util.TimeFormatYMDHHMM)), data.BlackCardList[2].Tip)
	assert.Equal(4, data.BlackCardList[3].Level)
	assert.Equal("星曜 V4", data.BlackCardList[3].Title)
	assert.EqualValues(2000000, data.BlackCardList[3].Price)
	assert.Equal("https://static-test.missevan.com/test4.png", data.BlackCardList[3].IconURL)
	assert.Zero(data.BlackCardList[3].Status)
	assert.Equal("月消费达 2000000 钻可解锁", data.BlackCardList[3].Tip)
	assert.Equal(3, data.UserBlackCard.Level)
	assert.Equal("星曜 V3", data.UserBlackCard.Title)
	assert.NotZero(data.BlackCardList[0].Status)
	assert.EqualValues(369, data.UserBlackCard.CurrentSpend)
}
