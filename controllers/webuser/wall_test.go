package webuser

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/activitymessage"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var wallOnce sync.Once

func init() {
	wallNotifyDelay = 0
}

func wallAddTestData() {
	wallOnce.Do(func() {
		t := time.Unix(1234567890, 0)
		testMsg := activitymessage.ActivityMessage{
			EventID:      usersrank.EventIDShowLoveAction,
			Type:         activitymessage.TypeNotifyShowLove,
			FromUserID:   12,
			ToUserID:     10,
			RoomID:       123,
			Content:      "test",
			CreateTime:   t,
			ModifiedTime: goutil.TimeNow(),
		}
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := activitymessage.Collection().UpdateOne(ctx, bson.M{"event_id": 128, "create_time": t},
			bson.M{"$set": testMsg}, options.Update().SetUpsert(true))
		if err != nil {
			logger.Fatal(err)
		}
	})
}

func TestWallTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(wallMessageProj, "type", "from_user_id", "to_user_id", "room_id", "content", "create_time")

	wallSendParamKeys := []string{"event_id", "type", "from_user_id", "to_user_id", "content_id"}
	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(wallBarrageParam{}, "event_id", "marker", "group")
	kc.Check(wallSendParam{}, wallSendParamKeys...)

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(wallBarrageResp{}, "data", "has_more", "marker")
	kc.Check(event128WallMeta{}, "all_count", "contents", "user_id", "my_count", "my_point")
	kc.Check(event166WallMeta{}, "all_count", "contents", "user_id", "my_count", "sweet_machine_num", "love_billboard_num")
	kc.Check(wallContent{}, "id", "content")
	kc.Check(wallSendParam{}, wallSendParamKeys...)
	kc.Check(wallListResp{}, "data", "pagination", "count_1", "count_2")
}

func TestWallMetaFindCommon(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	wallAddTestData()

	key := keys.KeyActivityWallContents1.Format(usersrank.EventIDShowLoveAction)
	pipe := service.Redis.Pipeline()
	pipe.HSet(key, 100, "test", 10, "test2")
	pipe.HSet(key, "empty", "test", 10, "test2") // 测试存入的 ID 不正确
	pipe.Expire(key, time.Hour)
	_, err := pipe.Exec()
	require.NoError(err)
	var w event166WallMeta
	require.NoError(w.FindCommon())
	assert.NotZero(w.AllCount)
	require.GreaterOrEqual(len(w.Contents), 2)
	assert.Less(w.Contents[0].ID, w.Contents[1].ID)
}

func TestWallMetaFindUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var w event166WallMeta
	require.NoError(w.FindUsers(0))
	assert.Nil(w.MyCount)
	assert.Nil(w.SweetMachineNum)
	assert.Nil(w.LoveBillboardNum)
	require.NoError(w.FindUsers(123))
	assert.Equal(int64(123), w.UserID)
	require.NotNil(w.MyCount)
	require.NotNil(w.SweetMachineNum)
	assert.NotNil(w.LoveBillboardNum)
}

func TestActionWallMeta(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("GET", "/wall/meta?event_id=test", false, nil)
	_, err := ActionWallMeta(c)
	assert.Equal(actionerrors.ErrParams, err, "event_id 不是数字")
	c = handler.NewTestContext("GET", "/wall/meta?event_id=-10", false, nil)
	_, err = ActionWallMeta(c)
	assert.Equal(actionerrors.ErrParams, err, "event_id 不正确")
	c = handler.NewTestContext("GET", "/wall/meta?event_id=128", false, nil)
	resp, err := ActionWallMeta(c)
	require.NoError(err)
	assert.NotNil(resp, "响应不正确")
}

func TestWallSendLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyActivityWallContents1.Format(usersrank.EventIDShowLoveAction)
	require.NoError(service.Redis.HSet(key, "1", "test").Err())

	c := handler.NewTestContext("POST", "/wall/send", true, "&event_id=test")
	var param wallSendParam
	assert.Equal(actionerrors.ErrParams, param.load(c), "bind 失败")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=128&type=1&content_id=1&to_user_id=10")
	assert.Equal(handler.ErrBadRequest, param.load(c), "不支持的活动")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=0")
	assert.Equal(actionerrors.ErrParams, param.load(c), "告白类型错误")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=3")
	assert.Equal(actionerrors.ErrParams, param.load(c), "告白类型错误")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=-10")
	assert.EqualError(param.load(c), "告白内容未找到")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=1&to_user_id=-10")
	assert.Equal(actionerrors.ErrCannotFindRoom, param.load(c), "不是主播")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=1&to_user_id=10")

	require.NoError(param.load(c))
	assert.NotEmpty(param.content)
	assert.NotNil(param.room)
	assert.NotNil(param.fromUser)
}

func TestWallSendAdminLoad(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyActivityWallContents1.Format(usersrank.EventIDShowLoveAction)
	require.NoError(service.Redis.HSet(key, "1", "test").Err())

	c := handler.NewTestContext("POST", "/wall/send", true, "&event_id=test")
	var param wallSendParam
	assert.Equal(actionerrors.ErrParams, param.adminload(c), "bind 失败")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=-10&type=1")
	assert.Equal(handler.ErrBadRequest, param.adminload(c), "event_id 不正确")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=-1")
	assert.Equal(actionerrors.ErrParams, param.adminload(c), "告白类型错误")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=3")
	assert.Equal(actionerrors.ErrParams, param.adminload(c), "告白类型错误")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=-10")
	assert.EqualError(param.adminload(c), "告白内容未找到")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=1&to_user_id=-10")
	assert.Equal(actionerrors.ErrCannotFindRoom, param.adminload(c), "不是主播")
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=1&to_user_id=10")
	assert.Equal(actionerrors.ErrCannotFindUser, param.adminload(c))
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=2&content_id=1&from_user_id=12&to_user_id=10")
	require.NoError(param.adminload(c))
	c = handler.NewTestContext("POST", "/wall/send", true, "&event_id=166&type=1&content_id=1&from_user_id=12&to_user_id=10")
	require.NoError(param.adminload(c))
	assert.NotEmpty(param.content)
	assert.NotNil(param.room)
	assert.NotNil(param.fromUser)
}

func TestWallSendSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := wallSendParam{
		EventID:  usersrank.EventIDShowLoveAction,
		Type:     activitymessage.TypeNormalShowLove,
		fromUser: handler.CreateTestUser(),
		room:     new(room.Room),
		content:  "test",
	}
	param.room.CreatorID = 10
	assert.EqualError(param.send(), "告白次数不足~")
	param.room.CreatorID = 12
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": param.fromUser.ID},
		bson.M{"user_id": param.fromUser.ID, "event_166": userstatus.Event166Status{LoveBillboardNum: 1, SweetMachineNum: 1}}))
	require.NoError(param.send())
	require.NotNil(param.am)
	require.NotNil(param.event166Status)
	require.EqualValues(1, param.event166Status.LoveBillboardNum)
	require.Zero(param.event166Status.SweetMachineNum)
	assert.NotNil(param.am.FromUser)
	assert.NotNil(param.am.ToUser)
	assert.Equal(param.Type, param.am.Type)
	assert.NotEmpty(param.am.Content)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	err := activitymessage.Collection().FindOne(ctx, bson.M{"_id": param.am.OID}).Err()
	assert.NoError(err)

	// 无限制表白
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": param.fromUser.ID},
		bson.M{"user_id": param.fromUser.ID, "event_166": userstatus.Event166Status{LoveBillboardNum: 1, SweetMachineNum: -1}}))
	require.NoError(param.send())
	require.NotNil(param.am)
	require.NotNil(param.event166Status)
	require.EqualValues(-1, param.event166Status.SweetMachineNum)
	require.EqualValues(1, param.event166Status.LoveBillboardNum)
}

func TestWallNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ch := make(chan int, 2)
	var receive *notifymessages.General
	cancel := mrpc.SetMock("im://broadcast/all", func(i interface{}) (interface{}, error) {
		v, ok := i.(map[string]interface{})
		if !ok {
			return true, nil
		}
		r, ok := v["payload"].(*notifymessages.General)
		if ok {
			receive = r
			ch <- 1
		}
		return true, nil
	})
	defer cancel()

	am := activitymessage.ActivityMessage{
		RoomID: 123456,
		FromUser: &mowangskuser.Simple{
			Username: "<123",
		},
		ToUser: &mowangskuser.Simple{
			Username: "456>",
		},
		Content: "<test>",
	}
	wallNotify(&am)
	<-time.After(2 * time.Second)
	assert.Nil(receive)

	goutil.SetTimeNow(func() time.Time {
		return time.Unix(123, 0)
	})
	defer goutil.SetTimeNow(nil)
	am2 := am
	am2.EventID = usersrank.EventIDShowLoveAction
	am2.Type = activitymessage.TypeNotifyShowLove
	b := bubble.Bubble{BubbleID: usersrank.EventIDShowLoveAction, Image: "oss://test.png"}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"type":        notifymessages.TypeGeneral,
		"room_id":     am2.RoomID,
		"create_time": goutil.TimeNow(),
	}
	_, err := notifymessages.Collection().DeleteMany(ctx, filter)
	require.NoError(err)
	_, err = bubble.Collection().UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)
	wallNotify(&am2)
	select {
	case <-ch:
	case <-time.After(10 * time.Second):
	}
	require.NotNil(receive)
	message := goutil.FormatMessage(wallFormat.Format("&lt;123", "456&gt;", "&lt;test&gt;"), map[string]string{
		"highlight_color": "#FFC3F9",
		"text_color":      "#FFFFFF",
	})
	assert.Equal(message, receive.Message)
	assert.Equal(am.RoomID, receive.RoomID)
	assert.NotNil(receive.NotifyBubble, "气泡")
	// 查看是否存入数据库，因为是并行操作，需要等待插入 mongodb
	<-time.After(500 * time.Millisecond)
	var g notifymessages.General
	err = notifymessages.Collection().FindOne(ctx, filter).Decode(&g)
	require.NoError(err)
	assert.NotNil(g.NotifyBubble)
	logger.Debug(g.Message)
}

func TestActionWallSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/wall/send", true,
		"&event_id=test")
	_, err := ActionWallSend(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("POST", "/wall/send", true,
		"&event_id=166&to_user_id=12&content_id=1&type=1")
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": c.User().ID},
		bson.M{"user_id": c.User().ID,
			"event_166": userstatus.Event166Status{LoveBillboardNum: 1}}))
	r, err := ActionWallSend(c)
	require.NoError(err)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(r, "sweet_machine_num", "love_billboard_num", "message")
}

func TestActionAdminWallSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/wall/send", false,
		"&event_id=test")
	_, err := ActionAdminWallSend(c)
	assert.Equal(actionerrors.ErrParams, err)

	pubsub := tutil.NewPubSub(imRedis.Subscribe(keys.KeyIMPubSub2.Format(2, 0), keys.KeyIMPubSub2.Format(2, 1)))
	defer pubsub.Close()
	c = handler.NewTestContext("POST", "/wall/send", false,
		"&event_id=166&from_user_id=10&to_user_id=12&content_id=1&type=1")
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": 10},
		bson.M{"user_id": 10,
			"event_166": userstatus.Event166Status{LoveBillboardNum: 1}}))
	r, err := ActionAdminWallSend(c)
	require.NoError(err)
	kc := tutil.NewKeyChecker(t, tutil.MapString)
	kc.Check(r, "sweet_machine_num", "love_billboard_num", "message")
	time.Sleep(time.Second)
	val := pubsub.Receive()
	logger.Debug(val)
}

func TestActionWallList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	wallAddTestData()

	c := handler.NewTestContext("GET", "/wall/list?event_id=test", false, nil)
	_, err := ActionWallList(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("GET", "/wall/list?event_id=10", false, nil)
	_, err = ActionWallList(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("GET", "/wall/list?event_id=166&mine=1", false, nil)
	_, err = ActionWallList(c)
	assert.Equal(actionerrors.ErrUnloggedUser, err)
	c = handler.NewTestContext("GET", "/wall/list?event_id=166&type=-1", false, nil)
	_, err = ActionWallList(c)
	assert.Equal(actionerrors.ErrParams, err)
	// type 0
	c = handler.NewTestContext("GET", fmt.Sprintf("/wall/list?event_id=166&type=%d&mine=1", 0), true, nil)
	r, err := ActionWallList(c)
	require.NoError(err)
	resp := r.(*wallListResp)
	assert.NotNil(resp.Count1)
	assert.NotNil(resp.Count2)
	assert.NotEmpty(resp.Data)
	// type 1
	c = handler.NewTestContext("GET", fmt.Sprintf("/wall/list?event_id=166&type=%d&mine=1",
		activitymessage.TypeNotifyShowLove), true, nil)
	r, err = ActionWallList(c)
	require.NoError(err)
	resp = r.(*wallListResp)
	assert.NotNil(resp.Count1)
	assert.NotNil(resp.Count2)
	assert.NotNil(resp.Data)
	// type 2
	c = handler.NewTestContext("GET", fmt.Sprintf("/wall/list?event_id=166&type=%d&mine=1",
		activitymessage.TypeNormalShowLove), true, nil)
	r, err = ActionWallList(c)
	require.NoError(err)
	resp = r.(*wallListResp)
	assert.NotNil(resp.Count1)
	assert.NotNil(resp.Count2)
	assert.NotNil(resp.Data)
	c = handler.NewTestContext("GET", fmt.Sprintf("/wall/list?event_id=166&type=%d&p=2",
		activitymessage.TypeNormalShowLove), true, nil)
	r, err = ActionWallList(c)
	require.NoError(err)
	resp = r.(*wallListResp)
	assert.Nil(resp.Count1)
	assert.Nil(resp.Count2)
	assert.NotNil(resp.Data)
}

func TestWallCount(t *testing.T) {
	require := require.New(t)

	count := wallCount(usersrank.EventIDShowLoveAction, 0, activitymessage.TypeNotifyShowLove)
	require.NotNil(count)
	require.NotZero(*count)
	count = wallCount(usersrank.EventIDShowLoveAction, 12, activitymessage.TypeNormalShowLove)
	require.NotNil(count)
	require.NotZero(*count)
}

func TestActionWallBarrage(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("GET", "/wall/barrage?event_id=128&marker=xxx", false, nil)
	_, err := ActionWallBarrage(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("GET", "/wall/barrage?event_id=128", false, nil)
	_, err = ActionWallBarrage(c)
	assert.NoError(err)
}

func TestWallBarrageLoad(t *testing.T) {
	assert := assert.New(t)

	var param wallBarrageParam
	c := handler.NewTestContext("GET", "/?event_id=test", false, nil)
	assert.Equal(actionerrors.ErrParams, param.Load(c), "bind 错误")
	c = handler.NewTestContext("GET", "/?event_id=10", false, nil)
	assert.Equal(actionerrors.ErrParams, param.Load(c), "event_id 错误")
	c = handler.NewTestContext("GET", "/?event_id=128&group=0", false, nil)
	assert.Equal(actionerrors.ErrParams, param.Load(c), "组数错误,太小")
	c = handler.NewTestContext("GET", "/?event_id=128&group=6", false, nil)
	assert.Equal(actionerrors.ErrParams, param.Load(c), "组数错误，太大")
	c = handler.NewTestContext("GET", "/?event_id=128", false, nil)
	assert.NoError(param.Load(c))
	assert.Equal(int64(3), param.Group)
	assert.Equal([3]primitive.ObjectID{}, param.oids)
	c = handler.NewTestContext("GET", "/?event_id=128&marker=test", false, nil)
	assert.Equal(actionerrors.ErrParams, param.Load(c))
	c = handler.NewTestContext("GET", "/?event_id=128&marker=1|2|3", false, nil)
	assert.Equal(actionerrors.ErrParams, param.Load(c))

	oid := primitive.NewObjectID()
	hex := oid.Hex()
	c = handler.NewTestContext("GET", fmt.Sprintf("/?event_id=128&marker=%s|%s|%s", hex, hex, hex), true, nil)
	assert.NoError(param.Load(c))
	assert.Equal(c.UserID(), param.userID)
	assert.Equal([3]primitive.ObjectID{oid, oid, oid}, param.oids)
}

func TestWallBarrageFindCommonMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := wallBarrageParam{
		EventID: usersrank.EventIDShowLoveAction,
		Marker:  "test",
		Group:   1,
	}
	require.NoError(param.FindCommonMsg())
	assert.Nil(param.msgArrays[0])
	param.oids[0] = primitive.NewObjectID()
	require.NoError(param.FindCommonMsg())
	assert.NotNil(param.msgArrays[0])
}

func TestWallBarrageFindUserMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := wallBarrageParam{
		EventID: usersrank.EventIDShowLoveAction,
		Marker:  "test",
		Group:   1,
		userID:  0,
	}
	param.oids[2] = primitive.NewObjectID()
	param.msgArrays[0] = []*activitymessage.ActivityMessage{{Content: "test"}}
	require.NoError(param.FindUserMsg())
	assert.Nil(param.msgArrays[2])
	param.userID = 12
	require.NoError(param.FindUserMsg())
	assert.NotEmpty(param.msgArrays[2])
}

func TestWallBarrageBuildData(t *testing.T) {
	assert := assert.New(t)

	var param wallBarrageParam
	buildData := func(sizes [3]int64) {
		param = wallBarrageParam{Group: 3, dataPre: make([][]*activitymessage.ActivityMessage, 3)}
		for i := int64(0); i < 3; i++ {
			param.msgArrays[i] = make([]*activitymessage.ActivityMessage, sizes[i])
			for j := int64(0); j < sizes[i]; j++ {
				param.msgArrays[i][j] = &activitymessage.ActivityMessage{FromUserID: i, ToUserID: j}
			}
		}
	}
	assertLen := func(lens [3]int) {
		t.Helper()
		for i := range lens {
			assert.Len(param.dataPre[i], lens[i], i)
		}
	}
	buildData([3]int64{31, 31, 4})
	param.buildData()
	assertLen([3]int{10, 10, 10})
	assert.Len(param.data, 30)
	buildData([3]int64{12, 3, 1})
	param.buildData()
	assertLen([3]int{10, 6, 0})
	assert.Len(param.data, 16)
}

func TestWallBarrageBuildResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	oid := primitive.NewObjectID()
	param := wallBarrageParam{Group: 1}
	param.msgArrays[0] = []*activitymessage.ActivityMessage{
		{OID: oid, FromUserID: 12, ToUserID: 12},
	}
	resp, err := param.BuildResp()
	require.NoError(err)
	require.NotNil(resp)
	require.Len(resp.Data, 1)
	assert.NotNil(resp.Data[0].FromUser)
	assert.NotNil(resp.Data[0].ToUser)
	assert.Equal(fmt.Sprintf("%s||", oid.Hex()), resp.Marker)
	assert.False(resp.HasMore)
}

func TestWallBarrageMayFind(t *testing.T) {
	assert := assert.New(t)

	var param wallBarrageParam
	assert.True(param.mayFind(0))
	assert.False(param.mayFind(2))
	param.userID = 1
	assert.True(param.mayFind(2))
	param.Marker = "test"
	assert.False(param.mayFind(0))
	assert.False(param.mayFind(2))
	oid := primitive.NewObjectID()
	param.oids = [3]primitive.ObjectID{oid, primitive.NilObjectID, oid}
	assert.True(param.mayFind(0))
	assert.True(param.mayFind(2))
}

func TestWallBarrageAppendData(t *testing.T) {
	assert := assert.New(t)

	param := wallBarrageParam{
		Group:   2,
		dataPre: [][]*activitymessage.ActivityMessage{{}, make([]*activitymessage.ActivityMessage, 0, 10)},
		msgArrays: [3][]*activitymessage.ActivityMessage{
			{{FromUserID: 1}, {FromUserID: 1}},
			{{FromUserID: 2}},
			{{FromUserID: 3}, {FromUserID: 4},
				{FromUserID: 5}, {FromUserID: 6}, {FromUserID: 7}, {FromUserID: 8},
				{FromUserID: 9}, {FromUserID: 10}, {FromUserID: 11}},
		},
	}
	param.appendData(1, 0, 1)                       // limit 限制
	param.appendData(1, 1, 3)                       // array 限制
	param.appendData(1, 2, len(param.msgArrays[2])) // data 限制
	ids := make([]int64, len(param.dataPre[1]))
	for i := range ids {
		ids[i] = param.dataPre[1][i].FromUserID
	}
	assert.Equal([]int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}, ids)
	assert.Equal([3]int{1, 1, 8}, param.msgArraysEnd)
}

func TestWallBarrageHasMore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := wallBarrageParam{Group: 1}
	assertSum := func() {
		t.Helper()
		assert.Equal(wallSizePerBarrage*param.Group, int64(param.msgArraysEnd[0]+param.msgArraysEnd[1]+param.msgArraysEnd[2])) // 如果能填满弹幕列表， end 之和总是 10
	}
	// 恰好填满的情况
	param.msgArrays = [3][]*activitymessage.ActivityMessage{
		{{FromUserID: 0}, {FromUserID: 1}},
		{{FromUserID: 2}, {FromUserID: 3}, {FromUserID: 4}, {FromUserID: 5}, {FromUserID: 6}, {FromUserID: 7}, {FromUserID: 8}},
		{{FromUserID: 9}},
	}
	resp, err := param.BuildResp()
	require.NoError(err)
	assert.False(resp.HasMore)

	// 模拟还有剩下数据的情况
	param = wallBarrageParam{Group: 1}
	param.msgArrays = [3][]*activitymessage.ActivityMessage{
		{{FromUserID: 0}, {FromUserID: 1}},
		{
			{FromUserID: 2}, {FromUserID: 3}, {FromUserID: 4}, {FromUserID: 5},
			{FromUserID: 6}, {FromUserID: 7}, {FromUserID: 8}, {FromUserID: 9}, {FromUserID: 10},
		},
		{},
	}
	resp, err = param.BuildResp()
	require.NoError(err)
	assert.True(resp.HasMore)
	assertSum()
	// 只查询到一组的情况
	param = wallBarrageParam{Group: 1}
	param.msgArrays = [3][]*activitymessage.ActivityMessage{
		{
			{FromUserID: 0}, {FromUserID: 1}, {FromUserID: 2}, {FromUserID: 3},
			{FromUserID: 4}, {FromUserID: 5}, {FromUserID: 6}, {FromUserID: 7},
			{FromUserID: 8}, {FromUserID: 9}, {FromUserID: 10},
		},
		{},
		{},
	}
	resp, err = param.BuildResp()
	require.NoError(err)
	assert.True(resp.HasMore)
	assertSum()
}
