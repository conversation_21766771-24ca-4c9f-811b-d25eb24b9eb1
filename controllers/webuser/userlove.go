package webuser

import (
	"math/rand"
	"net/http"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveranklove"
	"github.com/MiaoSiLa/live-service/models/mysql/liverankloveselection"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type rankLove struct {
	Month     string               `json:"month"`
	CreatorID int64                `json:"creator_id"`
	Ups       []liveranklove.Model `json:"ups"`
}

var liveRankLoveGetLatest = liveranklove.GetLatest

// ActionRankLove 用户端获取心动主播月榜
/**
 * @api {get} /api/v2/user/rank/love 用户端获取心动主播月榜
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "month": "", // "" 表示从未设置心动主播月榜数据，"2019-11" 表示当前为 11 月榜
 *       "creator_id": 1234, // 用户该月的心动主播 ID，0 表示用户该月尚未进行心动主播测试
 *       "ups": [
 *         {
 *           "user_id": 123,
 *           "name": "name",
 *           "icon_url": "http://static-test.missevan.com/icon.png",
 *           "room_id": 123,
 *           "character": "正太音",
 *           "point": 12,
 *           "sound_id": 1,
 *           "soundurl": "http://static-test.missevan.com/sound.mp3"
 *           "status": {
 *             "open": 1, // 主播直播状态：1 开播，0 未开播
 *           }
 *         }
 *       ],
 *     }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 */
func ActionRankLove(c *handler.Context) (handler.ActionResponse, error) {
	list, err := liveRankLoveGetLatest()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(list) == 0 {
		return &rankLove{}, nil
	}

	err = addOpenStatus(list)
	if err != nil {
		return nil, err
	}

	var creatorID int64
	if userID := c.UserID(); userID != 0 {
		liveMonth, err := liverankloveselection.GetMonthRankLove(userID, list[0].Month)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if liveMonth != nil && liveMonth.ModifiedTime > list[0].ModifiedTime {
			creatorID = liveMonth.CreatorID
		}
	}

	return &rankLove{
		Month:     yearMonthStr(list[0].Month),
		CreatorID: creatorID,
		Ups:       list,
	}, nil
}

// yearMonth e.g. 201912
func yearMonthStr(yearMonth int) string {
	if yearMonth < 0 {
		return ""
	}
	str := strconv.Itoa(yearMonth)
	if len(str) != 6 {
		return ""
	}
	return str[:4] + "-" + str[4:]
}

func addOpenStatus(list []liveranklove.Model) error {
	roomIDs := make([]int64, len(list))
	for i, v := range list {
		roomIDs[i] = v.RoomID
	}
	rooms, err := room.ListSimples(
		bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(bson.M{"room_id": 1, "status.open": 1}))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i, v := range list {
		for _, r := range rooms {
			if v.RoomID == r.RoomID {
				if r.IsOpen() {
					list[i].Status.Open = 1
				}
				break
			}
		}
	}
	return nil
}

// ActionRankLoveSelection “测一下你的心动主播”功能接口
/**
 * @api {post} /api/v2/user/rank/loveselection “测一下你的心动主播”功能接口
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": 123, // 主播 ID
 *   }
 *
 * @apiError (500) {Number} code 100010007
 * @apiError (500) {String} info 相关错误信息
 */
func ActionRankLoveSelection(c *handler.Context) (handler.ActionResponse, error) {
	u := c.User()
	if u == nil || u.ID == 0 {
		return nil, actionerrors.ErrUnloggedUser
	}

	list, err := liveRankLoveGetLatest()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(list) == 0 {
		return nil, handler.NewActionError(http.StatusNotFound, handler.CodeUnknownError, "月榜未设置")
	}

	liveMonth, err := liverankloveselection.GetMonthRankLove(u.ID, list[0].Month)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if liveMonth != nil && liveMonth.ModifiedTime > list[0].ModifiedTime {
		return liveMonth.CreatorID, nil
	}

	creatorIDs := make([]int64, len(list))
	for i, v := range list {
		creatorIDs[i] = v.UserID
	}

	attentions, err := attentionuser.CheckAttention(u.ID, creatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var followed []int64
	for _, v := range attentions {
		if v.Followed {
			followed = append(followed, v.UserID)
		}
	}

	var selected int64
	if len(followed) > 0 {
		selected = followed[rand.Intn(len(followed))]
	} else {
		selected = creatorIDs[rand.Intn(len(creatorIDs))]
	}

	err = liverankloveselection.SetMonthRankLove(u.ID, selected, list[0].Month)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return selected, nil
}
