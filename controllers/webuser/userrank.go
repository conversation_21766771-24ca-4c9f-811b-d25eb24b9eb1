package webuser

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func rankRuleURL() string {
	return config.Conf.Params.LiveURL.UserRankRule
}

type rankTopResp []rankTopInfo

type rankTopInfo struct {
	Type         int               `json:"type"`
	Title        string            `json:"title"`
	Introduction string            `json:"introduction"`
	Data         []*usersrank.Info `json:"Datas"`
}

// UserRankResp 主播贡献榜响应
// TODO：修改为返回 data，my_rank
type UserRankResp struct {
	Data    []*RankList     `json:"Datas"`
	MyRank  *usersrank.Info `json:"myrank,omitempty"`
	Event   *rankEvent      `json:"event,omitempty"`
	Refresh int64           `json:"refresh,omitempty"`
	Title   string          `json:"title,omitempty"`
	// WORKAROUND: 客户端 6.1.4 之前的版本使用 rule，新版本使用 rule_url
	Rule      string `json:"rule,omitempty"`
	RuleURL   string `json:"rule_url,omitempty"`
	RewardURL string `json:"reward_url,omitempty"`
}

type hourRankResp struct {
	Last        []*RankList     `json:"last"`
	Current     []*RankList     `json:"current"`
	CreatorRank *usersrank.Info `json:"creator_rank,omitempty"`
	Refresh     int64           `json:"refresh"`
}

type rankEvent struct {
	IconURL string `json:"icon_url"`
	OpenURL string `json:"open_url"`
}

// RankList 主播贡献榜单条信息
type RankList struct {
	*usersrank.Info
	Room      *room.Simple `json:"room"`
	Attention *bool        `json:"attention,omitempty"`
}

// UserRankParam 主播贡献榜结果生成器
type UserRankParam struct {
	*UserRankResp

	// User 用户信息
	User        *user.User
	c           *handler.Context
	rankType    int
	roomID      int64
	creatorID   int64
	homepage    int
	listUserIDs []int64
}

// 是否是首页排行榜的请求（0: 否，1: 是）
const (
	homepageNo = iota
	homepageYes
)

// ActionUserRank 查询某个主播榜
/**
 * @api {get} /api/v2/user/rank 主播收益榜详细信息
 * @apiDescription 主播收益榜具体查询
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} type 榜单类型, 1: 日榜, 2: 周榜, 3: 月榜, 4: 小时榜, 5: 盲盒周榜, 7: 新人榜, 8: 上期月榜
 * @apiParam {Number} [room_id] 直播间 ID, 返回当前直播间相对应的榜单排名信息（仅支持盲盒周榜），若盲盒周榜 room_id 和 creator_id 都传的话以 creator_id 为准
 * @apiParam {Number} [creator_id] 主播 ID, 返回当前直播间相对应的榜单排名信息（仅支持新人榜、盲盒周榜）
 * @apiParam {Number} [homepage] 是否是首页排行榜的请求（0: 否，1: 是）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "rank": 1,
 *           "rank_changes": 1, // 排名变化 0: 排名无变化; 正数: 排名上升名次; 负数: 排名下降名次; null: 主播新上榜
 *           "revenue": 10,
 *           "user_id": 12345,
 *           "username": "1234",
 *           "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *           "attention": false,
 *           "room": {
 *             "room_id": 152,
 *             "name": "12345",
 *             "announcement": "12345",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "status": {
 *               "open": 1
 *             },
 *             "cover_url": "http://static.maoercdn.com/avatars/icon01.png"
 *           }
 *         },
 *         {
 *           "rank": 2,
 *           "rank_changes": -1, // 排名变化 0: 排名无变化; 正数: 排名上升名次; 负数: 排名下降名次; null: 主播新上榜
 *           "revenue": 1,
 *           "rank_up": 10,
 *           "user_id": 123456,
 *           "username": "12346",
 *           "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *           "attention": true,
 *           "room": {
 *             // 结构同上
 *           }
 *         }
 *       ],
 *       "myrank": {
 *         "is_nova": true, // 是否是新人 (若没有直播间也返回该字段, 不下发代表不是新人), false: 不是, true: 是
 *         "rank": 1,
 *         "revenue": 10,
 *         "user_id": 12345,
 *         "username": "1234",
 *         "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *         "rank_up": 10
 *       },
 *       "event": { // 排行榜顶部 tab 栏最右侧的活动配置，没有则不返回该字段
 *         "icon_url": "http://static.maoercdn.com/icon01.png", // 入口 icon
 *         "open_url": "https://link.missevan.com/starlight" // 入口链接
 *       },
 *       "refresh": 60000, // 距离截至日期还剩的时间，单位：秒
 *       "rule_url": "https://link.missevan.com/help/fm-ranks", // 规则
 *       "reward_url": "https://link.missevan.com/help/fm-ranks" // 奖励，目前仅月榜下发
 *     }
 *   }
 *
 * @apiSuccessExample 上期月榜:
 *   {
 *     "code": 0,
 *     "info": {
 *       "title": "2024.04",
 *       "Datas": [
 *         {
 *           "rank": 1,
 *           "revenue": 10,
 *           "user_id": 12345,
 *           "username": "1234",
 *           "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *           "attention": false,
 *           "room": {
 *             "room_id": 152,
 *             "name": "12345",
 *             "announcement": "12345",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "status": {
 *               "open": 1
 *             },
 *             "cover_url": "http://static.maoercdn.com/avatars/icon01.png"
 *           }
 *         },
 *         {
 *           "rank": 2,
 *           "revenue": 1,
 *           "user_id": 123456,
 *           "username": "12346",
 *           "iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *           "attention": true,
 *           "room": {
 *             // 结构同上
 *           }
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionUserRank(c *handler.Context) (handler.ActionResponse, error) {
	param := &UserRankParam{c: c, UserRankResp: new(UserRankResp)}
	param.rankType, _ = c.GetParamInt("type")

	switch param.rankType {
	case usersrank.TypeDay, usersrank.TypeWeek, usersrank.TypeMonth, usersrank.TypeHour:
		param.homepage, _ = param.c.GetParamInt("homepage")
		if err := param.buildNormalRank(); err != nil {
			return nil, err
		}
	case usersrank.TypeNova:
		param.creatorID, _ = param.c.GetParamInt64("creator_id")
		if param.creatorID < 0 {
			return nil, actionerrors.ErrParams
		}
		if err := param.buildNovaRank(); err != nil {
			return nil, err
		}
	case usersrank.TypeGashaponWeek:
		param.creatorID, _ = param.c.GetParamInt64("creator_id")
		param.roomID, _ = param.c.GetParamInt64("room_id")
		if param.creatorID < 0 && param.roomID < 0 {
			return nil, actionerrors.ErrParams
		}
		if err := param.buildGashaponRank(); err != nil {
			return nil, err
		}
	case usersrank.TypeLastMonth:
		if err := param.buildLastMonthRank(); err != nil {
			return nil, err
		}
	default:
		return nil, actionerrors.ErrParams
	}

	param.findEvent()

	return param.UserRankResp, nil
}

func (param *UserRankParam) buildNormalRank() error {
	param.RuleURL = rankRuleURL()
	// WORKAROUND: 客户端 6.1.4 之前的版本使用 rule
	e := param.c.Equip()
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{IOS: "6.1.4", Android: "6.1.4"}) {
		param.Rule = param.RuleURL
	}

	param.User = param.c.User()
	if param.User != nil {
		param.MyRank = &usersrank.Info{
			UserID:   param.User.ID,
			IconURL:  param.User.IconURL,
			Username: param.User.Username,
		}
	}
	err := param.findRank()
	if err != nil {
		return err
	}
	param.updateMyRankRankUp()
	err = param.FindRooms()
	if err != nil {
		return err
	}
	return nil
}

func (param *UserRankParam) buildNovaRank() error {
	param.RuleURL = rankRuleURL()
	// WORKAROUND: 客户端 6.1.4 之前的版本使用 rule
	e := param.c.Equip()
	if !e.FromApp || e.IsOldApp(goutil.AppVersions{IOS: "6.1.4", Android: "6.1.4"}) {
		param.Rule = param.RuleURL
	}

	// NOTICE: 传递了 creator_id 则查看房主的新人榜排名，如果没有传则查看自己的排名
	switch {
	case param.creatorID > 0:
		creator, err := mowangskuser.FindByUserID(param.creatorID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if creator == nil {
			return actionerrors.ErrCannotFindUser
		}
		nova, err := usersrank.IsNova(creator.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		param.MyRank = &usersrank.Info{
			IsNova:   nova,
			UserID:   creator.ID,
			IconURL:  creator.IconURL,
			Username: creator.Username,
		}
	default:
		param.User = param.c.User()
		if param.User == nil {
			break
		}
		param.MyRank = &usersrank.Info{
			UserID:   param.User.ID,
			IconURL:  param.User.IconURL,
			Username: param.User.Username,
		}

		// NOTICE: 符合新人榜上榜条件或房间不存在，都满足新人榜资格
		nova, err := usersrank.IsNova(param.User.ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !nova {
			exists, err := room.Exists2(bson.M{"creator_id": param.c.UserID()})
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}
			if !exists {
				nova = true
			}
		}
		param.MyRank.IsNova = nova
	}
	err := param.findRank()
	if err != nil {
		return err
	}
	param.updateMyRankRankUp()
	err = param.FindRooms()
	if err != nil {
		return err
	}
	return nil
}

func (param *UserRankParam) buildGashaponRank() error {
	param.User = param.c.User()
	if param.User != nil {
		param.MyRank = &usersrank.Info{
			UserID:   param.User.ID,
			IconURL:  param.User.IconURL,
			Username: param.User.Username,
		}
	}
	switch {
	case param.creatorID > 0:
		creator, err := mowangskuser.FindByUserID(param.creatorID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if creator != nil {
			param.MyRank = &usersrank.Info{
				UserID:   creator.ID,
				Username: creator.Username,
				IconURL:  creator.IconURL,
			}
		}
	case param.roomID > 0:
		// roomID 不为空时，改为查询该直播间榜单信息
		r, err := room.FindOneSimple(bson.M{"room_id": param.roomID}, &room.FindOptions{FindCreator: true})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if r != nil {
			param.MyRank = &usersrank.Info{
				UserID:   r.CreatorID,
				Username: r.CreatorUsername,
				IconURL:  r.CreatorIconURL,
			}
		}
	}
	err := param.findRank()
	if err != nil {
		return err
	}
	param.updateMyRankRankUp()
	err = param.FindRooms()
	if err != nil {
		return err
	}
	return nil
}

func (param *UserRankParam) buildLastMonthRank() error {
	param.User = param.c.User()

	now := goutil.TimeNow()
	// 上个月 1 号
	when := util.BeginningOfMonth(now).AddDate(0, -1, 0)

	param.Title = when.Format(util.TimeFormatYMWithDot)
	tmpList, err := usersrank.Find(usersrank.TypeMonth, when, param.MyRank,
		usersrank.FindOptions{
			DisableFindRankChange: true,
			RankCount:             10, // 上期月榜只展示 10 名
		})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Data = make([]*RankList, len(tmpList))
	for i := 0; i < len(tmpList); i++ {
		param.Data[i] = &RankList{Info: tmpList[i]}
	}

	err = param.FindRooms()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *UserRankParam) findEvent() {
	re, err := params.FindRank()
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}

	switch param.rankType {
	case usersrank.TypeLastMonth:
		// 上期月榜无需返回活动配置
		return
	case usersrank.TypeMonth:
		// 月榜需要返回奖励链接
		param.UserRankResp.RewardURL = re.MonthRewardURL
	}

	if re.RankEvent == nil {
		return
	}

	param.UserRankResp.Event = &rankEvent{
		IconURL: re.RankEvent.IconURL,
		OpenURL: re.RankEvent.OpenURL,
	}
}

func (param *UserRankParam) findRank() (err error) {
	now := goutil.TimeNow()
	deadline := usersrank.Deadline(param.rankType, now)
	// NOTICE: 多个 3 秒的误差
	param.Refresh = int64(deadline.Sub(now).Seconds()) + 3
	if param.rankType == usersrank.TypeHour && param.homepage == homepageYes {
		// 首页排行榜的直播小时榜需要单独区分 type, 用来返回不同的榜单数量
		param.rankType = usersrank.TypeHourHomepage
	}
	tmpList, err := usersrank.Find(param.rankType, now, param.MyRank)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.Data = make([]*RankList, len(tmpList))
	for i := 0; i < len(tmpList); i++ {
		param.Data[i] = &RankList{Info: tmpList[i]}
	}
	return nil
}

func (param *UserRankParam) updateMyRankRankUp() {
	if param.MyRank == nil {
		return
	}
	if param.MyRank.Rank == 1 {
		// 第一名显示与第二名的差值
		if len(param.Data) >= 2 {
			param.MyRank.RankUp = param.MyRank.Revenue - param.Data[1].Revenue
		} else {
			// 没有第二名认为第二名分数为 0
			param.MyRank.RankUp = param.MyRank.Revenue
		}
		return
	}

	rankLen := usersrank.RankLen(param.rankType)
	switch {
	case param.MyRank.Rank == 0 && int64(len(param.Data)) < rankLen:
		param.MyRank.RankUp = 1
	case param.MyRank.Rank <= rankLen && param.MyRank.Rank > 1:
		param.MyRank.RankUp = param.Data[param.MyRank.Rank-2].Revenue - param.MyRank.Revenue + 1
	default:
		// 榜外并且榜单满了
		param.MyRank.RankUp = param.Data[rankLen-1].Revenue - param.MyRank.Revenue + 1
		param.MyRank.Rank = 0
	}
}

// SetInfo 为 param 设置查询到的用户排行信息，每次调用会覆盖原来的结果
// NOTICE: 确保传入的 usersrank.Info 不是 nil
func (param *UserRankParam) SetInfo(infos ...*usersrank.Info) {
	if param.UserRankResp == nil {
		param.UserRankResp = new(UserRankResp)
	}
	param.Data = make([]*RankList, 0, len(infos))
	for i := 0; i < len(infos); i++ {
		param.Data = append(param.Data, &RankList{Info: infos[i]})
	}
}

// FindRooms 为表单查询房间
func (param *UserRankParam) FindRooms() error {
	userIDs := make(map[int64]bool, len(param.Data))
	for i := 0; i < len(param.Data); i++ {
		userIDs[param.Data[i].UserID] = true
	}
	param.listUserIDs = make([]int64, 0, len(userIDs))
	for userID := range userIDs {
		param.listUserIDs = append(param.listUserIDs, userID)
	}

	var option *room.FindOptions
	if param.User != nil {
		option = &room.FindOptions{FindFans: true, ListenerID: param.User.ID}
	}
	rooms, err := room.FindSimpleMapByCreatorID(param.listUserIDs, option)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i := 0; i < len(param.Data); i++ {
		param.Data[i].Room = rooms[param.Data[i].UserID]
		// 理论上应该是都有的，不过可能房间被删除
		if param.Data[i].Room == nil {
			continue
		}
		if param.User != nil && param.Data[i].Room.Statistics != nil {
			param.Data[i].Attention = &param.Data[i].Room.Statistics.Attention
		}
		param.Data[i].Room.Statistics = nil
	}
	return nil
}

// FindAttention 判断用户是否关注
// NOTICE: 依赖项在 FindRooms 中初始化
func (param *UserRankParam) FindAttention() {
	if param.User == nil {
		return
	}
	s, err := attentionuser.CheckAttention(param.User.ID, param.listUserIDs)
	if err != nil {
		logger.Error(err)
		return
	}
	m := attentionuser.AttentionSliceToMap(s)
	for i := 0; i < len(param.Data); i++ {
		if attention := m[param.Data[i].UserID]; attention != nil {
			param.Data[i].Attention = &attention.Followed
		}
	}
}

// ActionUserRankTop 主播收益榜 top3 概览
/**
 * @api {get} /api/v2/user/rank/top 主播收益榜 top3 概览
 * @apiDescription 主播收益榜前三，用于首页
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "type": 4,
 *         "title": "小时榜",
 *         "introduction": "本小时直播间主播排行榜",
 *         "Datas": [
 *           {
 *             "rank": 1,
 *             "revenue": 10,
 *             "user_id": 12345,
 *             "username": "1234",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *           {
 *             "rank": 2,
 *             "revenue": 1,
 *             "user_id": 123456,
 *             "username": "12346",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *         ]
 *       },
 *       {
 *         "type": 1,
 *         "title": "今日榜",
 *         "introduction": "今日直播间主播排行榜",
 *         "Datas": [
 *           {
 *             "rank": 1,
 *             "revenue": 10,
 *             "user_id": 12345,
 *             "username": "1234",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *           {
 *             "rank": 2,
 *             "revenue": 1,
 *             "user_id": 123456,
 *             "username": "12346",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *         ]
 *       },
 *       {
 *         "type": 2,
 *         "title": "周榜",
 *         "introduction": "本周直播间主播排行榜",
 *         "Datas": [
 *           {
 *             "rank": 1,
 *             "revenue": 10,
 *             "user_id": 12345,
 *             "username": "1234",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *           {
 *             "rank": 2,
 *             "revenue": 1,
 *             "user_id": 123456,
 *             "username": "12346",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *         ]
 *       },
 *       {
 *         "type": 3,
 *         "title": "月榜",
 *         "introduction": "本月直播间主播排行榜",
 *         "Datas": [
 *           {
 *             "rank": 1,
 *             "revenue": 10,
 *             "user_id": 12345,
 *             "username": "1234",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *           {
 *             "rank": 2,
 *             "revenue": 1,
 *             "user_id": 123456,
 *             "username": "12346",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *         ]
 *       },
 *       {
 *         "type": 7,
 *         "title": "新人榜",
 *         "introduction": "今日新人主播排行榜",
 *         "Datas": [
 *           {
 *             "rank": 1,
 *             "revenue": 10,
 *             "user_id": 12345,
 *             "username": "1234",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *           {
 *             "rank": 2,
 *             "revenue": 1,
 *             "user_id": 123456,
 *             "username": "12346",
 *             "iconurl": "http://static.missevan.com/avatars/icon01.png"
 *           },
 *         ]
 *       }
 *     ]
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionUserRankTop(c *handler.Context) (handler.ActionResponse, error) {
	infos, err := usersrank.Top3(goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp := defaultRankTopResp(c)
	for i := 0; i < len(resp); i++ {
		resp[i].Data = infos[resp[i].Type]
	}

	return resp, nil
}

func defaultRankTopResp(c *handler.Context) rankTopResp {
	resp := rankTopResp{
		rankTopInfo{Type: usersrank.TypeHour, Title: "小时榜", Introduction: "本小时直播间主播排行榜"},
		rankTopInfo{Type: usersrank.TypeDay, Title: "今日榜", Introduction: "今日直播间主播排行榜"},
		rankTopInfo{Type: usersrank.TypeWeek, Title: "周榜", Introduction: "本周直播间主播排行榜"},
		rankTopInfo{Type: usersrank.TypeMonth, Title: "月榜", Introduction: "本月直播间主播排行榜"},
	}
	// WORKAROUND: 兼容安卓 < 5.7.2 iOS < 4.8.5 的版本不下发新人榜 top3 概览
	if !c.Equip().IsAppOlderThan("4.8.5", "5.7.2") {
		resp = append(resp, rankTopInfo{Type: usersrank.TypeNova, Title: "新人榜", Introduction: "今日新人主播排行榜"})
	}
	return resp
}

// ActionUserRankHourly 主播贡献榜小时榜的接口
/**
 * @api {get} /api/v2/user/rank/hourly 主播贡献榜小时榜的接口
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} [creator_id] 额外查询的用户，房主
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "last": [ // 上一场小时榜前三
 *         {
 *           "rank": 1,
 *           "revenue": 10,
 *           "user_id": 12345,
 *           "username": "1234",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "attention": false,
 *           "room": {
 *             "room_id": 152,
 *             "name": "12345",
 *             "announcement": "12345",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "status": {
 *               "open": 1
 *             },
 *             "cover_url": "http://static.missevan.com/avatars/icon01.png"
 *           }
 *         }
 *       ],
 *       "current": [ // 本场小时榜
 *         {
 *           "rank": 1,
 *           "rank_changes": 1, // 排名变化 0: 排名无变化; 正数: 排名上升名次; 负数: 排名下降名次; null: 主播新上榜
 *           "revenue": 10,
 *           "user_id": 12345,
 *           "username": "1234",
 *           "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *           "attention": false,
 *           "room": {
 *             "room_id": 152,
 *             "name": "12345",
 *             "announcement": "12345",
 *             "creator_id": 12345,
 *             "creator_username": "1234",
 *             "status": {
 *               "open": 1
 *             },
 *             "cover_url": "http://static.missevan.com/avatars/icon01.png"
 *           }
 *         }
 *       ],
 *       "creator_rank": {
 *         "rank": 1,
 *         "revenue": 10,
 *         "user_id": 12345,
 *         "username": "1234",
 *         "iconurl": "http://static.missevan.com/avatars/icon01.png",
 *         "rank_up": 10
 *       },
 *       "refresh": 10000
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionUserRankHourly(c *handler.Context) (handler.ActionResponse, error) {
	resp := new(hourRankResp)
	creatorID, _ := c.GetParamInt64("creator_id")
	var owner *mowangskuser.Simple
	var err error
	if creatorID != 0 {
		owner, err = mowangskuser.FindByUserID(creatorID)
		if err != nil {
			logger.Error(err)
			owner = nil
		}
	}
	var ownerRankInfo *usersrank.Info
	if owner != nil {
		ownerRankInfo = &usersrank.Info{
			UserID:   owner.ID,
			IconURL:  owner.IconURL,
			Username: owner.Username,
		}
	}
	now := goutil.TimeNow()
	deadline := usersrank.Deadline(usersrank.TypeHour, now)
	// NOTICE: 多个 3 秒的误差
	resp.Refresh = int64(deadline.Sub(now).Seconds()) + 3
	top3, top10, err := usersrank.FindHourRank(now, ownerRankInfo)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param := UserRankParam{UserRankResp: new(UserRankResp), rankType: usersrank.TypeHour}
	infos := make([]*usersrank.Info, 0, len(top3)+len(top10))
	infos = append(infos, top3...)
	infos = append(infos, top10...)
	param.SetInfo(infos...)
	err = param.FindRooms()
	if err != nil {
		return nil, err
	}
	param.User = c.User()
	param.FindAttention()
	resp.Last = param.Data[0:len(top3)]
	resp.Current = param.Data[len(top3):len(param.Data)]
	if ownerRankInfo != nil {
		resp.CreatorRank = ownerRankInfo
		param.Data = resp.Current
		param.MyRank = ownerRankInfo
		param.updateMyRankRankUp()
	}

	return resp, nil
}
