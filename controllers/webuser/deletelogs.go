package webuser

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/viewlog"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type deleteLogsParam struct {
	RoomIDs string `form:"room_ids" json:"room_ids"`
	Type    int    `form:"type" json:"type"`

	roomIDs []int64
	userID  int64
	userCtx userapi.UserContext
}

// 删除请求的类型
const (
	deleteLogsTypeRoom = iota // 根据房间删除
	deleteLogsTypeAll         // 全部删除
)

// ActionDeleteLogs 删除用户直播间访问记录
/**
 * @api {post} /api/v2/user/deletelogs 删除用户直播间访问记录
 * @apiDescription 删除用户直播间的访问记录，需要登录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=0,1} type 操作类型，0 为通过房间 ID 删除，1 为全部删除
 * @apiParam {String} [room_ids] 需要删除的房间 ID 列表，用逗号隔开，全部删除时不需传入
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionDeleteLogs(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDeleteLogsParam(c)
	if err != nil {
		return nil, err
	}
	err = param.delete()
	if err != nil {
		return nil, err
	}

	return "删除成功", nil
}

func newDeleteLogsParam(c *handler.Context) (*deleteLogsParam, error) {
	var param deleteLogsParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Type != deleteLogsTypeRoom && param.Type != deleteLogsTypeAll {
		return nil, actionerrors.ErrParams
	}
	if param.Type == deleteLogsTypeRoom {
		if param.RoomIDs == "" {
			return nil, actionerrors.ErrParams
		}
		param.roomIDs, err = util.SplitToInt64Array(param.RoomIDs, ",")
		if err != nil {
			return nil, actionerrors.ErrParams
		}
	}
	param.userID = c.UserID()
	param.userCtx = userapi.NewUserContext(c)

	return &param, nil
}

func (param *deleteLogsParam) delete() error {
	if param.Type == deleteLogsTypeAll {
		err := viewlog.DeleteAll(param.userID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		return userapi.ClearLivePlayHistory(param.userID, param.userCtx)
	}
	err := viewlog.DeleteByRoomIDs(param.userID, param.roomIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return userapi.DelLivePlayHistory(param.userID, param.roomIDs, param.userCtx)
}
