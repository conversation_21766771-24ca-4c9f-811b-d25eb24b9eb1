package webuser

import (
	"context"
	"html"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/mongodb/activitymessage"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/mevent"
	"github.com/MiaoSiLa/missevan-go/service/cache"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const wallSizePerBarrage int64 = 10

var (
	wallMessageProj = mongodb.NewProjection(
		"type, from_user_id, to_user_id, room_id, content, create_time")

	wallNotifyDelay = 2 * time.Minute
)

type event128WallMeta struct {
	AllCount int64         `json:"all_count"`
	Contents []wallContent `json:"contents"`
	UserID   int64         `json:"user_id"`
	MyCount  *int64        `json:"my_count,omitempty"`
	MyPoint  *int64        `json:"my_point,omitempty"`
}

type event166WallMeta struct {
	AllCount         int64         `json:"all_count"`
	Contents         []wallContent `json:"contents"`
	UserID           int64         `json:"user_id"`
	MyCount          *int64        `json:"my_count,omitempty"`
	SweetMachineNum  *int64        `json:"sweet_machine_num,omitempty"`
	LoveBillboardNum *int64        `json:"love_billboard_num,omitempty"`
}

type wallContent struct {
	ID      int    `json:"id"`
	Content string `json:"content"`
}

func (w *event128WallMeta) FindCommon() error {
	var err error
	w.AllCount, err = countWall(usersrank.EventIDQixiFestival, 0)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 活动已结束，返回空数组
	w.Contents = make([]wallContent, 0)
	return nil
}

func (w *event128WallMeta) FindUsers(userID int64) error {
	if userID == 0 {
		return nil
	}
	w.UserID = userID
	count, err := countWall(usersrank.EventIDQixiFestival, userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	w.MyCount = &count
	w.MyPoint = util.NewInt64(0)
	return nil
}

func (w *event166WallMeta) FindCommon() error {
	var err error
	w.AllCount, err = countWall(usersrank.EventIDShowLoveAction, 0)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	key := keys.KeyActivityWallContents1.Format(usersrank.EventIDShowLoveAction)
	val, err := service.Redis.HGetAll(key).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	w.Contents = make([]wallContent, 0, len(val))
	for idStr, content := range val {
		id, err := strconv.Atoi(idStr)
		if err != nil {
			logger.Error(err)
			continue
			// PASS
		}
		w.Contents = append(w.Contents, wallContent{ID: id, Content: content})
	}
	sort.Slice(w.Contents, func(i, j int) bool {
		return w.Contents[i].ID < w.Contents[j].ID
	})
	return nil
}

func (w *event166WallMeta) FindUsers(userID int64) error {
	if userID == 0 {
		return nil
	}
	w.UserID = userID
	count, err := countWall(usersrank.EventIDShowLoveAction, userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	w.MyCount = &count
	var event166Status userstatus.ActivityStatus
	err = userstatus.UserEventData(userID, userstatus.EventKey(usersrank.EventIDShowLoveAction), &event166Status)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	w.SweetMachineNum = util.NewInt64(event166Status.Event166.SweetMachineNum)
	w.LoveBillboardNum = util.NewInt64(event166Status.Event166.LoveBillboardNum)
	return nil
}

func countWall(eventID, userID int64) (int64, error) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"event_id": eventID}
	if userID != 0 {
		filter["from_user_id"] = userID
	}
	return activitymessage.Collection().CountDocuments(ctx, filter)
}

// ActionWallMeta 弹幕墙-信息
/**
 * @api {get} /api/v2/user/wall/meta 告白墙 meta 信息
 * @apiDescription 告白墙 meta 信息，告白数量、拥有的缘/告白次数、告白文案等
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=128,166} event_id 活动 ID
 *
 * @apiSuccessExample event 128 Success-Response:
 *   {
 *     "all_count": 1000, // 全站告白数量
 *     "contents": [
 *       {"id": 1, "message": "告白内容1"},
 *       {"id": 2, "message": "告白内容2"}
 *     ],
 *     "user_id": 123, // 访问用户 ID, 未登录为 0
 *     "my_count": 10, // 我的告白数量，未登录不显示
 *     "my_point": 0 // 我的缘，未登录不显示
 *   }
 *
 * @apiSuccessExample event 166 Success-Response:
 *   {
 *     "all_count": 1000, // 全站告白数量
 *     "contents": [
 *       {"id": 1, "message": "告白内容1"},
 *       {"id": 2, "message": "告白内容2"}
 *     ],
 *     "user_id": 123, // 访问用户 ID, 未登录为 0
 *     "my_count": 10, // 我的告白数量，未登录不显示
 *     "sweet_machine_num": 10, // 甜蜜留声机剩余次数，-1 表示无限制
 *     "love_billboard_num": 0 // 爱的公告牌剩余次数，-1 表示无限制
 *   }
 */
func ActionWallMeta(c *handler.Context) (handler.ActionResponse, error) {
	eventID, err := c.GetParamInt64("event_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	switch eventID {
	case usersrank.EventIDQixiFestival:
		return event128WallMetaResp(c)
	case usersrank.EventIDShowLoveAction:
		return event166WallMetaResp(c)
	}
	return nil, actionerrors.ErrParams
}

func event128WallMetaResp(c *handler.Context) (handler.ActionResponse, error) {
	resp := new(event128WallMeta)
	err := resp.FindCommon()
	if err != nil {
		return nil, err
	}
	err = resp.FindUsers(c.UserID())
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func event166WallMetaResp(c *handler.Context) (handler.ActionResponse, error) {
	resp := new(event166WallMeta)
	err := resp.FindCommon()
	if err != nil {
		return nil, err
	}
	err = resp.FindUsers(c.UserID())
	if err != nil {
		return nil, err
	}
	return resp, nil
}

type wallSendParam struct {
	EventID    int64 `form:"event_id" json:"event_id"`
	Type       int   `form:"type" json:"type"`
	FromUserID int64 `form:"from_user_id" json:"from_user_id"`
	ToUserID   int64 `form:"to_user_id" json:"to_user_id"`
	MessageID  int   `form:"content_id" json:"content_id"`

	content        string // 告白内容
	room           *room.Room
	am             *activitymessage.ActivityMessage
	fromUser       *user.User
	event166Status userstatus.Event166Status
}

func (param *wallSendParam) load(c *handler.Context) error {
	err := c.Bind(param)
	if err != nil || param.Type < userstatus.TypeNotifyShowLove || param.Type >= userstatus.TypeLimit {
		return actionerrors.ErrParams
	}
	switch param.EventID {
	case usersrank.EventIDShowLoveAction:
		event, err := mevent.FindSimple(usersrank.EventIDShowLoveAction)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if event == nil {
			return actionerrors.ErrParamsMsg("未找到该活动")
		}
		if goutil.TimeNow().Unix() >= event.EndTime {
			return actionerrors.NewErrForbidden("活动已结束")
		}
	default:
		return handler.ErrBadRequest
	}
	key := keys.KeyActivityWallContents1.Format(param.EventID)
	param.content, err = service.Redis.HGet(key, strconv.Itoa(param.MessageID)).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.content == "" {
		return actionerrors.ErrParamsMsg("告白内容未找到")
	}
	param.room, err = room.FindOne(bson.M{
		"creator_id": param.ToUserID,
		"limit":      bson.M{"$exists": false},
	}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	param.fromUser = c.User()
	return nil
}

func (param *wallSendParam) adminload(c *handler.Context) error {
	err := c.Bind(param)
	if err != nil || param.Type < userstatus.TypeNotifyShowLove || param.Type >= userstatus.TypeLimit {
		return actionerrors.ErrParams
	}
	switch param.EventID {
	case usersrank.EventIDShowLoveAction:
	default:
		return handler.ErrBadRequest
	}
	key := keys.KeyActivityWallContents1.Format(param.EventID)
	param.content, err = service.Redis.HGet(key, strconv.Itoa(param.MessageID)).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.content == "" {
		return actionerrors.ErrParamsMsg("告白内容未找到")
	}
	param.room, err = room.FindOne(bson.M{
		"creator_id": param.ToUserID,
		"limit":      bson.M{"$exists": false},
	}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.room == nil {
		return actionerrors.ErrCannotFindRoom
	}
	u, err := mowangskuser.FindByUserID(param.FromUserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if u == nil {
		return actionerrors.ErrCannotFindUser
	}
	param.fromUser = new(user.User)
	param.fromUser.ID = u.ID
	param.fromUser.Username = u.Username
	param.fromUser.IconURL = u.IconURL
	return nil
}

func (param *wallSendParam) send() error {
	now := goutil.TimeNow()
	param.am = &activitymessage.ActivityMessage{
		EventID:      param.EventID,
		Type:         param.Type,
		FromUserID:   param.fromUser.ID,
		ToUserID:     param.room.CreatorID,
		RoomID:       param.room.RoomID,
		Content:      param.content,
		CreateTime:   now,
		ModifiedTime: now,

		FromUser: &mowangskuser.Simple{
			ID:       param.fromUser.ID,
			Username: param.fromUser.Username,
			IconURL:  param.fromUser.IconURL,
		},
		ToUser: &mowangskuser.Simple{
			ID:       param.room.CreatorID,
			Username: param.room.CreatorUsername,
		},
		CreateTimeJSON: goutil.NewTimeUnixMilli(now),
	}
	err := mongodb.UseSession(service.MongoDB, func(ctx context.Context) error {
		// 消耗告白次数
		ok, event166Status, err := userstatus.DecreaseEvent166ShowLoveNum(ctx, param.fromUser.ID, param.Type)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if !ok {
			return actionerrors.NewErrForbidden("告白次数不足~")
		}
		param.event166Status = event166Status
		// 新建告白
		col := activitymessage.Collection()
		v, err := col.InsertOne(ctx, param.am)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		param.am.OID, ok = v.InsertedID.(primitive.ObjectID)
		if !ok {
			logger.Errorf("_id not found: %v", v.InsertedID)
			// PASS
		}
		return nil
	})
	if err != nil {
		return err
	}

	wallNotify(param.am)
	return nil
}

const wallFormat cache.KeyFormat = `<font color="${highlight_color}">%s</font><font color="${text_color}"> 向 </font><font color="${highlight_color}">%s</font><font color="${text_color}"> 告白：%s</font>`

func wallNotify(am *activitymessage.ActivityMessage) {
	goutil.Go(func() {
		switch {
		case am.EventID == usersrank.EventIDShowLoveAction && am.Type == activitymessage.TypeNotifyShowLove:
			time.Sleep(wallNotifyDelay)
			message := wallFormat.Format(html.EscapeString(am.FromUser.Username),
				html.EscapeString(am.ToUser.Username),
				html.EscapeString(am.Content),
			)
			message = goutil.FormatMessage(message, map[string]string{
				"highlight_color": "#FFC3F9",
				"text_color":      "#FFFFFF",
			})
			b, err := bubble.FindSimple(usersrank.EventIDShowLoveAction)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			notify := notifymessages.NewGeneral(am.RoomID, message, b)
			err = userapi.BroadcastAll(notify)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			// 因为和告白是异步发送，所以需要存入数据库
			err = notifymessages.Insert(notify)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	})
}

// ActionWallSend 弹幕墙-新建信息
/**
 * @api {POST} /api/v2/user/wall/send 告白墙-发送告白
 * @apiDescription 告白墙-发送告白
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=128,166} event_id 活动 ID
 * @apiParam {number=1,2} type 告白类型，1：爱的公告牌，2：甜蜜留声机
 * @apiParam {Number} to_user_id 被告白的主播
 * @apiParam {Number} content_id 告白内容 ID
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "sweet_machine_num": 9, // 甜蜜留声机剩余次数，-1 表示无限制
 *         "love_billboard_num": 0, // 爱的公告牌剩余次数，-1 表示无限制
 *         "message": {
 *           "from_user": {
 *             "id": 10,
 *             "username": "123",
 *             "iconurl": "https://example.com/test.png"
 *           },
 *           "to_user": {
 *             "id": 12,
 *             "username": "123",
 *             "iconurl": "" // 这个接口没有给被告白用户的头像
 *           },
 *           "type": 1, // 1 为爱的公告牌，2 为甜蜜留声机
 *           "content": "告白内容",
 *           "create_time": 123456789000, // 毫秒时间戳
 *         }
 *       }
 *     }
 *
 * @apiSuccessExample 两分钟后的全站消息:
 *     {
 *       "type": "notify",
 *       "notify_type": "message",
 *       "event": "new",
 *       "room_id": 123456,
 *       "message": "A 向 B 告白：内容",
 *       "notify_bubble": {
 *         "type": "custom", // 气泡类型，下发气泡 custom
 *         "image_url": "https://example.com/b001_0_10_0_100.png" // 客户端使用
 *       }
 *     }
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 *
 */
func ActionWallSend(c *handler.Context) (handler.ActionResponse, error) {
	var param wallSendParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}
	err = param.send()
	if err != nil {
		return nil, err
	}
	return handler.M{
		"sweet_machine_num":  param.event166Status.SweetMachineNum,
		"love_billboard_num": param.event166Status.LoveBillboardNum,
		"message":            param.am,
	}, nil
}

// ActionAdminWallSend 弹幕墙-管理员新建信息
/**
 * @api {POST} /api/v2/admin/user/wall/send 告白墙-后台管理员发送告白
 * @apiDescription 告白墙-发送告白，响应同用户的
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 *
 * @apiParam {number=166} event_id 活动 ID
 * @apiParam {number=1,2} type 告白类型，1：爱的公告牌，2：甜蜜留声机
 * @apiParam {Number} from_user_id 告白的用户
 * @apiParam {Number} to_user_id 被告白的主播
 * @apiParam {Number} content_id 告白内容 ID
 *
 */
func ActionAdminWallSend(c *handler.Context) (handler.ActionResponse, error) {
	var param wallSendParam
	err := param.adminload(c)
	if err != nil {
		return nil, err
	}
	err = param.send()
	if err != nil {
		return nil, err
	}
	return handler.M{
		"sweet_machine_num":  param.event166Status.SweetMachineNum,
		"love_billboard_num": param.event166Status.LoveBillboardNum,
		"message":            param.am,
	}, nil
}

type wallListResp struct {
	Data       []*activitymessage.ActivityMessage `json:"data"`
	Pagination goutil.Pagination                  `json:"pagination"`
	Count1     *int64                             `json:"count_1,omitempty"`
	Count2     *int64                             `json:"count_2,omitempty"`
}

// ActionWallList 弹幕墙-列表
/**
 * @api {get} /api/v2/user/wall/list 告白墙-告白列表
 * @apiDescription 告白墙-告白列表，时间倒序排列
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=128,166} event_id 活动 ID
 * @apiParam {number=0,1,2} [type=0] 告白类型，0: 所有; 1: 浓情告白/爱的公告牌; 2: 心意告白/甜蜜留声机
 * @apiParam {number=0,1} [mine=0] 是否是我的告白
 * @apiParam {Number} [p=1] 页数
 * @apiParam {Number} [pagesize=20] 每页数量
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "from_user": {
 *           "id": 10,
 *           "username": "123",
 *           "iconurl": "https://example.com/test.png"
 *         },
 *         "to_user": {
 *           "id": 12,
 *           "username": "123",
 *           "iconurl": "https://example.com/test.png"
 *         },
 *         "type": 1, // 1 为浓情告白/爱的公告牌，2 为心意告白/甜蜜留声机
 *         "content": "告白内容",
 *         "create_time": 123456789000, // 毫秒时间戳
 *       }],
 *       "pagination": {
 *         "p": 1,
 *         "pagesize": 6,
 *         "count": 1,
 *         "maxpage": 1
 *       },
 *       "count_1": 1, // 浓情告白/爱的公告牌数量
 *       "count_2": 1  // 心意告白/甜蜜留声机数量
 *     }
 *   }
 *
 */
func ActionWallList(c *handler.Context) (handler.ActionResponse, error) {
	eventID, err := c.GetParamInt64("event_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	switch eventID {
	case usersrank.EventIDQixiFestival, usersrank.EventIDShowLoveAction:
		// PASS
	default:
		return nil, actionerrors.ErrParams
	}
	isMine := utils.ParseBoolFromInt(c, "mine")
	if isMine && c.UserID() == 0 {
		return nil, actionerrors.ErrUnloggedUser
	}
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	messageType, _ := c.GetDefaultParamInt("type", 0)
	if messageType < 0 || messageType > activitymessage.TypeLimit {
		return nil, actionerrors.ErrParams
	}
	filter := bson.M{"event_id": eventID}
	if messageType != 0 {
		filter["type"] = messageType
	}
	var fromUserID int64
	if isMine {
		fromUserID = c.UserID()
		filter["from_user_id"] = fromUserID
	}
	opt := options.Find().SetProjection(wallMessageProj).
		SetSort(bson.M{"create_time": -1})
	resp := new(wallListResp)
	resp.Data, resp.Pagination, err = activitymessage.ListByPage(filter, opt, p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if p == 1 {
		switch messageType {
		case activitymessage.TypeNotifyShowLove:
			resp.Count1 = &resp.Pagination.Count
			resp.Count2 = wallCount(eventID, fromUserID, activitymessage.TypeNormalShowLove)
		case activitymessage.TypeNormalShowLove:
			resp.Count1 = wallCount(eventID, fromUserID, activitymessage.TypeNotifyShowLove)
			resp.Count2 = &resp.Pagination.Count
		default: // 全部
			resp.Count1 = wallCount(eventID, fromUserID, activitymessage.TypeNotifyShowLove)
			resp.Count2 = wallCount(eventID, fromUserID, activitymessage.TypeNormalShowLove)
		}
	}
	return resp, nil
}

func wallCount(eventID, fromUserID int64, messageType int) *int64 {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{
		"event_id": eventID,
		"type":     messageType,
	}
	if fromUserID != 0 {
		filter["from_user_id"] = fromUserID
	}
	count, err := activitymessage.Collection().CountDocuments(ctx, filter)
	if err != nil {
		logger.Error(err)
		return new(int64)
	}
	return &count
}

// wallBarrageParam 弹幕墙-弹幕列表响应参数
// [3]type 的数组，0 代表非自己的浓情告白，1 代表非自己的心意告白，2 代表自己的告白
type wallBarrageParam struct {
	EventID int    `form:"event_id"`
	Marker  string `form:"marker"`
	Group   int64  `form:"group,default=3"`

	userID int64
	oids   [3]primitive.ObjectID

	msgArrays    [3][]*activitymessage.ActivityMessage
	msgArraysEnd [3]int

	dataPre [][]*activitymessage.ActivityMessage // 每组数据，长度和 ArraySize 一致
	data    []*activitymessage.ActivityMessage
}

type wallBarrageResp struct {
	Data    []*activitymessage.ActivityMessage `json:"data"`
	HasMore bool                               `json:"has_more"`
	Marker  string                             `json:"marker"`
}

// ActionWallBarrage 弹幕墙-弹幕
/**
 * @api {get} /api/v2/user/wall/barrage 告白墙-弹幕列表
 * @apiDescription 告白墙-弹幕列表，根据规则排序，（各个子规则中按照插入时间倒序排序）
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=128,166} event_id 活动 ID
 * @apiParam {String} [marker] marker 标记
 * @apiParam {Number} [group=3] 取几组数据，最多 5 组
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "from_user": {
 *           "id": 10,
 *           "username": "123",
 *           "iconurl": "https://example.com/test.png"
 *         },
 *         "to_user": {
 *           "id": 12,
 *           "username": "123",
 *           "iconurl": "https://example.com/test.png"
 *         },
 *         "type": 1, // 1 为浓情告白/爱的公告牌，2 为心意告白/甜蜜留声机
 *         "content": "告白内容",
 *         "create_time": 1234567890000, // 毫秒时间戳
 *       }],
 *       "marker": "marker", // 下次请求的游标
 *       "has_more": true, // 还有数据返回 true，反之 false
 *     }
 *   }
 *
 */
func ActionWallBarrage(c *handler.Context) (handler.ActionResponse, error) {
	var param wallBarrageParam
	err := param.Load(c)
	if err != nil {
		return nil, err
	}
	err = param.FindCommonMsg()
	if err != nil {
		return nil, err
	}
	err = param.FindUserMsg()
	if err != nil {
		return nil, err
	}
	return param.BuildResp()
}

// Load 读取参数
func (param *wallBarrageParam) Load(c *handler.Context) error {
	err := c.Bind(param)
	if err != nil {
		return actionerrors.ErrParams
	}
	switch param.EventID {
	case usersrank.EventIDQixiFestival, usersrank.EventIDShowLoveAction:
		// PASS
	default:
		return actionerrors.ErrParams
	}
	if param.Group <= 0 || param.Group > 5 {
		return actionerrors.ErrParams
	}
	param.dataPre = make([][]*activitymessage.ActivityMessage, param.Group)
	param.userID = c.UserID()

	if param.Marker == "" {
		return nil
	}
	s := strings.Split(param.Marker, "|")
	if len(s) != 3 {
		return actionerrors.ErrParams
	}
	for i := range s {
		if s[i] != "" {
			param.oids[i], err = primitive.ObjectIDFromHex(s[i])
			if err != nil {
				return actionerrors.ErrParams
			}
		}
	}
	return nil
}

// FindCommonMsg 查找附带飘屏的告白和普通告白（非自己的）
func (param *wallBarrageParam) FindCommonMsg() error {
	var err error
	types := [2]int{activitymessage.TypeNotifyShowLove, activitymessage.TypeNormalShowLove}
	for i := 0; i < 2; i++ {
		if !param.mayFind(i) {
			continue
		}
		filter := bson.M{
			"event_id":     param.EventID,
			"type":         types[i],
			"from_user_id": bson.M{"$ne": param.userID},
		}
		if param.oids[i] != primitive.NilObjectID {
			filter["_id"] = bson.M{"$lt": param.oids[i]}
		}
		opt := options.Find().SetProjection(wallMessageProj).SetLimit(wallSizePerBarrage*param.Group + 1).
			SetSort(bson.M{"_id": -1}) // 插入时间排序，基本同 create_time 排序
		param.msgArrays[i], err = activitymessage.List(filter, opt, false)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

// FindUserMsg 查找用户自己的告白
func (param *wallBarrageParam) FindUserMsg() error {
	if !param.mayFind(2) {
		return nil
	}
	filter := bson.M{
		"event_id":     param.EventID,
		"from_user_id": param.userID,
	}
	if param.oids[2] != primitive.NilObjectID {
		filter["_id"] = bson.M{"$lt": param.oids[2]}
	}
	// 每组需要的数量和补全每组的数量的最大值
	limit := max(param.Group,
		int64(len(param.msgArrays[0])+len(param.msgArrays[1]))) + 1
	opt := options.Find().SetProjection(wallMessageProj).
		SetLimit(limit). // 用用户自己的数据填充不足的
		SetSort(bson.M{"_id": -1})
	var err error
	param.msgArrays[2], err = activitymessage.List(filter, opt, false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *wallBarrageParam) buildData() {
	param.dataPre = make([][]*activitymessage.ActivityMessage, param.Group)
	param.data = make([]*activitymessage.ActivityMessage, 0, param.Group*wallSizePerBarrage)
	for i := int64(0); i < param.Group; i++ {
		param.appendData(i, 0, 2)
		param.appendData(i, 1, 7)
		param.appendData(i, 2, 1)
		param.appendData(i, 0, int(wallSizePerBarrage))
		param.appendData(i, 1, int(wallSizePerBarrage))
		param.appendData(i, 2, int(wallSizePerBarrage))
		rand.Shuffle(len(param.dataPre[i]), func(j, k int) {
			param.dataPre[i][j], param.dataPre[i][k] = param.dataPre[i][k], param.dataPre[i][j]
		})
		param.data = append(param.data, param.dataPre[i]...)
	}
}

// BuildResp 生成响应结果
func (param *wallBarrageParam) BuildResp() (*wallBarrageResp, error) {
	param.buildData()
	resp := &wallBarrageResp{Data: param.data}

	// 查询的总数大于每页的数目，说明还有多余的可以返回
	// 详见 TestWallBarrageHasMore
	resp.HasMore = int64(
		len(param.msgArrays[0])+
			len(param.msgArrays[1])+
			len(param.msgArrays[2])) > wallSizePerBarrage*param.Group

	err := activitymessage.AssignJSON(param.data)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	markerPre := make([]string, 3)
	for i := 0; i < 3; i++ {
		if param.msgArraysEnd[i] > 0 {
			markerPre[i] = param.msgArrays[i][param.msgArraysEnd[i]-1].OID.Hex()
		}
	}
	resp.Marker = strings.Join(markerPre, "|")
	return resp, nil
}

func (param wallBarrageParam) mayFind(index int) bool {
	switch index {
	case 2:
		// 是登录用户，marker 未传或者 marker 传了并且上一次查询到了数据
		return param.userID != 0 && (param.Marker == "" || param.oids[2] != primitive.NilObjectID)
	default: // 0, 1
		return param.Marker == "" || param.oids[index] != primitive.NilObjectID
	}
}

func (param *wallBarrageParam) appendData(dataIndex int64, msgIndex, limit int) {
	st := param.msgArraysEnd[msgIndex]
	// 判断被插入的某组是否有余量
	for ; int64(len(param.dataPre[dataIndex])) < wallSizePerBarrage &&
		// 判断查询到的告白是否有余量
		param.msgArraysEnd[msgIndex] < len(param.msgArrays[msgIndex]) &&
		// 判断插入过的数据是否有余量
		param.msgArraysEnd[msgIndex]-st < limit; param.msgArraysEnd[msgIndex]++ {
		param.dataPre[dataIndex] = append(param.dataPre[dataIndex],
			param.msgArrays[msgIndex][param.msgArraysEnd[msgIndex]])
	}
}
