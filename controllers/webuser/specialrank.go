package webuser

import (
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
)

// ActionActivitySpend 活动期间用户消费总额
/**
 * @api {get} /api/v2/user/activity/spend 活动期间用户消费总额
 * @apiDescription 活动期间用户消费总额
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {string="20200707"} activity_key 活动 key
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "amount": 250000
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionActivitySpend(c *handler.Context) (handler.ActionResponse, error) {
	activityKey, _ := c.GetParam("activity_key")

	var amount float64
	var err error
	switch activityKey {
	case usersrank.ActivityJulyRebate:
		amount, err = service.Redis.ZScore(keys.KeyUsersActivityRank1.Format(activityKey),
			strconv.FormatInt(c.UserID(), 10)).Result()
		if err != nil && !serviceredis.IsRedisNil(err) {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	default:
		return nil, actionerrors.ErrParams
	}

	return handler.M{"amount": amount}, nil
}
