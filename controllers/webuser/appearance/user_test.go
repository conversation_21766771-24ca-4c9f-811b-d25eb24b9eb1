package appearance

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	nobleUserID = int64(3456835)
)

// mockVipUserVips mocks VIP user vips RPC call
func mockVipUserVips() func() {
	// Mock VIP list API
	cancelVipList := mrpc.SetMock(vip.URLVipList, func(input interface{}) (interface{}, error) {
		inputData, ok := input.(map[string]int)
		if !ok {
			return map[string]interface{}{
				"Datas": []*struct {
					ID    int64  `json:"id"`
					Level int    `json:"level"`
					Title string `json:"title"`
				}{},
			}, nil
		}
		var resp interface{}
		switch inputData["type"] {
		case vip.TypeLiveNoble:
			resp = map[string]interface{}{
				"Datas": []map[string]interface{}{
					{"id": 1, "level": 1, "title": "练习生"},
					{"id": 2, "level": 2, "title": "新秀"},
					{"id": 3, "level": 3, "title": "偶像"},
					{"id": 4, "level": 4, "title": "大咖"},
					{"id": 5, "level": 5, "title": "巨星"},
					{"id": 6, "level": 6, "title": "传奇"},
					{"id": 7, "level": 7, "title": "神话"},
				},
			}
		case vip.TypeLiveHighness:
			resp = map[string]interface{}{
				"Datas": []map[string]interface{}{
					{"id": 8, "level": 1, "title": "上神"},
				},
			}
		default:
			resp = map[string]interface{}{
				"Datas": []map[string]interface{}{},
			}
		}
		return resp, nil
	})

	// Mock user vips API
	cancelUserVips := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (interface{}, error) {
		body := input.(map[string]interface{})
		userID := body["user_id"].(int64)

		if userID == nobleUserID {
			// 为贵族用户返回贵族身份信息
			normalNoble := &vip.UserVip{
				VipID:      1,
				Title:      "练习生",
				Level:      5,
				Type:       vip.TypeLiveNoble,
				ExpireTime: 9999999999, // 长期有效
				UserID:     userID,
			}
			return vip.UserVipsResp{Vips: map[int]*vip.UserVip{vip.TypeLiveNoble: normalNoble}}, nil
		}

		// 对于其他用户，返回空结果（没有贵族身份）
		return vip.UserVipsResp{Vips: nil}, nil
	})

	// 返回清理函数
	return func() {
		cancelVipList()
		cancelUserVips()
	}
}

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(getAppearanceItem{}, "appearance_id", "name", "type", "status", "own_status", "intro", "lifetime", "icon_url",
		"start_time", "expire_time")
	kc.Check(getAppearanceResp{}, "custom_welcome_message", "data", "pagination")
	kc.Check(wearActionParam{}, "appearance_id", "type")
	kc.Check(infoResp{}, "appearance_id", "type", "name", "intro", "icon_url", "intro_open_url")
}

func TestActionListAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock VIP RPC 调用
	cancelVipMock := mockVipUserVips()
	defer cancelVipMock()

	// 参数错误
	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type=1&p=-1", true, nil)
	_, err := ActionListAppearance(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type=test", true, nil)
	_, err = ActionListAppearance(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type=999", true, nil)
	_, err = ActionListAppearance(c)
	assert.Equal(actionerrors.ErrParams, err)

	now := goutil.TimeNow()
	expTime := now.Add(time.Minute).Unix()
	expTime2 := now.Add(-time.Minute).Unix()
	a := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusWorn,
		UserID:       c.UserID(),
		AppearanceID: 1,
		Name:         "测试查询座驾",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a2 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusOwned,
		UserID:       c.UserID(),
		AppearanceID: 1,
		Name:         "测试查询永久座驾",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   nil,
	}
	a3 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusOwned,
		UserID:       c.UserID(),
		AppearanceID: 1,
		Name:         "测试查询过期座驾",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime2,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"appearance_id": bson.M{"$in": bson.A{20027, a.AppearanceID}}})
	require.NoError(err)

	key := keys.KeyNobleUserVips1.Format(c.UserID())
	err = service.Redis.Set(key, fmt.Sprintf(`{"1":{"type":1,"level":5,"user_id":%d,"expire_time":9999999999}}`, c.UserID()), 10*time.Second).Err()
	require.NoError(err)
	uvMap, err := vip.UserVipInfos(c.UserID(), false, nil)
	require.NoError(err)
	uv := uvMap[vip.TypeLiveNoble]
	require.NotNil(uv)
	nobleAppearanceID := appearance.VipAppearanceID(vip.TypeLiveNoble, uv.Level, appearance.TypeAvatarFrame)
	a4 := &userappearance.UserAppearance{
		Type:         appearance.TypeAvatarFrame,
		Status:       userappearance.StatusWorn,
		UserID:       c.UserID(),
		AppearanceID: nobleAppearanceID,
		Name:         "测试查询贵族外观",
		From:         appearance.FromNoble,
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime2,
	}

	highnessAppearanceID := appearance.VipAppearanceID(vip.TypeLiveHighness, 1, appearance.TypeVehicle)
	a5 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusWorn,
		UserID:       c.UserID(),
		AppearanceID: highnessAppearanceID,
		Name:         "测试查询上神外观",
		From:         appearance.FromHighness,
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}

	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": c.UserID()})
	require.NoError(err)
	_, err = userappearance.Collection().InsertMany(ctx, []interface{}{a, a2, a3, a4, a5})
	require.NoError(err)

	// 正常查询
	uri := fmt.Sprintf("/api/v2/user/appearance/list?type=%d&p=%d&pagesize=%d", appearance.TypeVehicle, 1, 20)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	a.UserID = c.UserID()
	r, err := ActionListAppearance(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(getAppearanceResp)
	require.NotEmpty(resp.Pagination)

	resultSet := []getAppearanceItem{
		{
			AppearanceID: a.AppearanceID,
			ExpireTime:   expTime - 1,
			Status:       userappearance.StatusWorn,
			OwnStatus:    userappearance.OwnStatusNew,
			Lifetime:     limitedTime,
		},
		// 测试上神外观
		{
			AppearanceID: appearance.LiveHighnessAppearanceIDByType(appearance.TypeVehicle),
			ExpireTime:   expTime,
			Status:       userappearance.StatusWorn,
			OwnStatus:    userappearance.OwnStatusNew,
			Lifetime:     limitedTime,
		},
		// 测试查询永久座驾
		{
			AppearanceID: a.AppearanceID,
			ExpireTime:   0,
			Status:       userappearance.StatusOwned,
			OwnStatus:    userappearance.OwnStatusNew,
			Lifetime:     lifeTime,
		},
		// 测试查询过期座驾
		{
			AppearanceID: a.AppearanceID,
			ExpireTime:   expTime2 - 1,
			Status:       userappearance.StatusExpired,
			OwnStatus:    userappearance.OwnStatusUsed,
			Lifetime:     limitedTime,
		},
	}
	require.Len(resp.Data, len(resultSet))
	for i := range resp.Data {
		assert.Equal(resultSet[i].AppearanceID, resp.Data[i].AppearanceID, i)
		assert.Equal(resultSet[i].ExpireTime, resp.Data[i].ExpireTime, i)
		assert.Equal(resultSet[i].Status, resp.Data[i].Status, i)
		assert.Equal(resultSet[i].OwnStatus, resp.Data[i].OwnStatus, i)
		assert.Equal(resultSet[i].Lifetime, resp.Data[i].Lifetime, i)
	}

	// 超出页码查询
	uri = fmt.Sprintf("/api/v2/user/appearance/list?type=%d&p=%d&pagesize=%d", appearance.TypeVehicle, 2, 20)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	a.UserID = c.UserID()
	r, err = ActionListAppearance(c)
	require.NoError(err)
	resp = r.(getAppearanceResp)
	require.Empty(resp.Data)

	// 其他类型查询
	uri = fmt.Sprintf("/api/v2/user/appearance/list?type=%d&pagesize=%d", appearance.TypeAvatarFrame, 20)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	_, err = ActionListAppearance(c)
	require.NoError(err)

	// 测试查询已有数据
	uri = fmt.Sprintf("/api/v2/user/appearance/list?type=%d&pagesize=%d", appearance.TypeAvatarFrame, 20)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	r, err = ActionListAppearance(c)
	require.NoError(err)
	resp = r.(getAppearanceResp)
	require.NotEmpty(resp.Data)
	require.GreaterOrEqual(len(resp.Data), 1)
	aItem, err := appearance.FindVipAppearance(vip.TypeLiveNoble, uv.Level, appearance.TypeAvatarFrame)
	require.NoError(err)
	require.NotNil(aItem)
	for i := range resp.Data {
		if resp.Data[i].Name == aItem.Name {
			assert.Equal(nobleAppearanceID, resp.Data[i].AppearanceID)
			assert.Equal(appearance.TypeAvatarFrame, resp.Data[i].Type)
			assert.Equal(uv.ExpireTime, resp.Data[i].ExpireTime)
		}
	}

	// 测试查询残缺数据
	uri = fmt.Sprintf("/api/v2/user/appearance/list?type=%d&pagesize=%d", appearance.TypeCardFrame, 20)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	r, err = ActionListAppearance(c)
	require.NoError(err)
	resp = r.(getAppearanceResp)
	require.NotEmpty(resp.Data)
	require.GreaterOrEqual(len(resp.Data), 1)
	aItem, err = appearance.FindVipAppearance(vip.TypeLiveNoble, uv.Level, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	nobleAppearanceID = appearance.VipAppearanceID(vip.TypeLiveNoble, uv.Level, appearance.TypeCardFrame)
	for i := range resp.Data {
		if resp.Data[i].Name == aItem.Name {
			assert.Equal(nobleAppearanceID, resp.Data[i].AppearanceID)
			assert.Equal(appearance.TypeCardFrame, resp.Data[i].Type)
			assert.Equal(uv.ExpireTime, resp.Data[i].ExpireTime)
		}
	}

	t.Run("测试黑卡外观逻辑", func(t *testing.T) {
		testBlackCardUserID := int64(999999)
		testNonBlackCardUserID := int64(888888)
		testBlackCardLevel := 3

		// 清理测试数据
		testUserIDs := []int64{testBlackCardUserID, testNonBlackCardUserID}
		_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testUserIDs}})
		require.NoError(err)
		defer func() {
			_, _ = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testUserIDs}})
		}()

		err = liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testBlackCardUserID).Error
		require.NoError(err)
		defer func() {
			_ = liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testBlackCardUserID).Error
		}()

		// 准备黑卡外观数据
		testBlackCardAppearanceIDs := appearance.AllBlackCardAppearanceIDs(testBlackCardLevel)
		testBlackCardAppearances := make([]*appearance.Appearance, 0, len(testBlackCardAppearanceIDs))
		for _, appearanceID := range testBlackCardAppearanceIDs {
			appearanceType := int(appearanceID-100) % 20
			testBlackCardAppearances = append(testBlackCardAppearances, &appearance.Appearance{
				ID:         appearanceID,
				Name:       fmt.Sprintf("测试黑卡外观 %d", appearanceID),
				Type:       appearanceType,
				From:       appearance.FromBlackCard,
				Icon:       "test_icon.png",
				Image:      "test_image.png",
				StartTime:  now.Unix(),
				ExpireTime: nil, // 永久有效
			})
		}

		// 准备非黑卡外观数据
		testNonBlackCardAppearances := []*appearance.Appearance{
			{
				ID:         50001,
				Name:       "测试普通身份铭牌",
				Type:       appearance.TypeIdentityBadge,
				From:       appearance.FromCustom,
				Icon:       "test_normal_badge_icon.png",
				Image:      "test_normal_badge_image.png",
				StartTime:  now.Unix(),
				ExpireTime: nil,
			},
			{
				ID:         50002,
				Name:       "测试普通进场通知",
				Type:       appearance.TypeEntryBubble,
				From:       appearance.FromCustom,
				Icon:       "test_normal_entry_icon.png",
				Image:      "test_normal_entry_image.png",
				StartTime:  now.Unix(),
				ExpireTime: nil,
			},
			{
				ID:         50003,
				Name:       "测试普通座驾",
				Type:       appearance.TypeVehicle,
				From:       appearance.FromCustom,
				Icon:       "test_normal_vehicle_icon.png",
				Image:      "test_normal_vehicle_image.png",
				StartTime:  now.Unix(),
				ExpireTime: nil,
			},
		}
		testNonBlackCardAppearanceIDs := goutil.SliceMap(testNonBlackCardAppearances, func(app *appearance.Appearance) int64 {
			return app.ID
		})
		testNonBlackCardAppearanceCount := len(testNonBlackCardAppearances)

		// 汇总所有测试外观数据
		testAppearances := make([]*appearance.Appearance, 0, len(testBlackCardAppearances)+testNonBlackCardAppearanceCount)
		testAppearances = append(testAppearances, testBlackCardAppearances...)
		testAppearances = append(testAppearances, testNonBlackCardAppearances...)

		// 记录所有外观 ID 用于清理
		allTestAppearanceIDs := make([]int64, 0, len(testBlackCardAppearanceIDs)+testNonBlackCardAppearanceCount)
		allTestAppearanceIDs = append(allTestAppearanceIDs, testBlackCardAppearanceIDs...)
		allTestAppearanceIDs = append(allTestAppearanceIDs, testNonBlackCardAppearanceIDs...)

		// 插入所有测试外观数据
		_, err = appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": allTestAppearanceIDs}})
		require.NoError(err)
		defer func() {
			_, _ = appearance.Collection().DeleteMany(ctx, bson.M{"id": bson.M{"$in": allTestAppearanceIDs}})
		}()

		_, err = appearance.Collection().InsertMany(ctx, goutil.SliceMap(testAppearances, func(item *appearance.Appearance) interface{} {
			return item
		}))
		require.NoError(err)

		// 给测试用户添加非黑卡外观到 userappearance 集合
		normalUserExpireTime := now.Add(30 * 24 * time.Hour).Unix()
		allUserAppearances := make([]*userappearance.UserAppearance, 0, len(testNonBlackCardAppearances)*len(testUserIDs))

		for _, userID := range testUserIDs {
			for _, appearanceTemplate := range testNonBlackCardAppearances {
				allUserAppearances = append(allUserAppearances, &userappearance.UserAppearance{
					UserID:       userID,
					AppearanceID: appearanceTemplate.ID,
					Type:         appearanceTemplate.Type,
					Status:       userappearance.StatusOwned,
					Name:         appearanceTemplate.Name,
					From:         appearanceTemplate.From,
					StartTime:    now.Unix(),
					ExpireTime:   &normalUserExpireTime,
				})
			}
		}

		// 插入用户外观数据
		if len(allUserAppearances) > 0 {
			_, err = userappearance.Collection().InsertMany(ctx, goutil.SliceMap(allUserAppearances, func(item *userappearance.UserAppearance) interface{} {
				return item
			}))
			require.NoError(err)
		}

		// 创建黑卡记录
		testBlackCardStartTime := now.Unix()
		testBlackCardExpireTime := now.Add(30 * 24 * time.Hour).Unix()
		testBlackCard := liveuserblackcard.LiveUserBlackCard{
			UserID:      testBlackCardUserID,
			BlackCardID: int64(testBlackCardLevel),
			StartTime:   testBlackCardStartTime,
			ExpireTime:  testBlackCardExpireTime,
		}
		err = testBlackCard.DB().Create(&testBlackCard).Error
		require.NoError(err)

		t.Run("用户没有黑卡时不应该添加黑卡外观", func(t *testing.T) {
			normalUserContext := handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type="+fmt.Sprintf("%d", appearance.TypeIdentityBadge), true, nil)
			normalUserContext.User().ID = testNonBlackCardUserID

			resp, err := ActionListAppearance(normalUserContext)
			require.NoError(err)

			data := resp.(getAppearanceResp)
			require.Equal(1, len(data.Data))
			// 验证没有黑卡外观，只有普通外观
			assert.NotEqual(appearance.FromBlackCard, data.Data[0].from)
		})

		t.Run("用户有黑卡且查询支持的外观类型时应该添加对应外观", func(t *testing.T) {
			testUserContext := handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type="+fmt.Sprintf("%d", appearance.TypeIdentityBadge), true, nil)
			testUserContext.User().ID = testBlackCardUserID

			resp, err := ActionListAppearance(testUserContext)
			require.NoError(err)

			data := resp.(getAppearanceResp)
			require.Len(data.Data, 2)

			// 验证包含黑卡身份铭牌外观和普通身份铭牌外观
			foundBlackCard := false
			foundNormal := false
			expectedBlackCardAppearanceID := appearance.BlackCardAppearanceID(testBlackCardLevel, appearance.TypeIdentityBadge)

			for _, item := range data.Data {
				if item.AppearanceID == expectedBlackCardAppearanceID {
					foundBlackCard = true
					assert.Equal(appearance.TypeIdentityBadge, item.Type)
					assert.Equal(userappearance.StatusOwned, item.Status)
					assert.Equal(testBlackCardExpireTime-1, item.ExpireTime, "黑卡外观展示的过期时间应该减 1 秒")
					assert.Contains(item.Name, "测试黑卡外观")
				} else if item.AppearanceID == 50001 {
					foundNormal = true
					assert.Equal(appearance.TypeIdentityBadge, item.Type)
					assert.Equal("测试普通身份铭牌", item.Name)
					assert.Equal(userappearance.StatusOwned, item.Status)
					assert.Equal(appearance.FromCustom, item.from)
				}
			}
			assert.True(foundBlackCard, "应该找到黑卡身份铭牌外观")
			assert.True(foundNormal, "应该找到普通身份铭牌外观")
		})

		t.Run("用户有黑卡但查询不支持的外观类型时不应该添加外观", func(t *testing.T) {
			// 黑卡 3 级不支持座驾
			testUserContext := handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type="+fmt.Sprintf("%d", appearance.TypeVehicle), true, nil)
			testUserContext.User().ID = testBlackCardUserID

			resp, err := ActionListAppearance(testUserContext)
			require.NoError(err)

			data := resp.(getAppearanceResp)
			require.Len(data.Data, 1)
			assert.NotEqual(appearance.FromBlackCard, data.Data[0].from)
		})

		t.Run("用户已有对应外观时不应该重复添加", func(t *testing.T) {
			// 先插入一个用户已有的黑卡外观
			existingAppearanceID := appearance.BlackCardAppearanceID(testBlackCardLevel, appearance.TypeEntryBubble)
			existingUserAppearance := &userappearance.UserAppearance{
				UserID:       testBlackCardUserID,
				AppearanceID: existingAppearanceID,
				Type:         appearance.TypeEntryBubble,
				Status:       userappearance.StatusWorn,
				Name:         "已佩戴的黑卡进场通知",
				From:         appearance.FromBlackCard,
				StartTime:    now.Unix(),
				ExpireTime:   &testBlackCardExpireTime,
			}
			_, err := userappearance.Collection().InsertOne(ctx, existingUserAppearance)
			require.NoError(err)

			testUserContext := handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/list?type="+fmt.Sprintf("%d", appearance.TypeEntryBubble), true, nil)
			testUserContext.User().ID = testBlackCardUserID

			resp, err := ActionListAppearance(testUserContext)
			require.NoError(err)

			data := resp.(getAppearanceResp)
			require.Len(data.Data, 2)

			// 验证不重复添加已佩戴的黑卡外观，同时包含普通进场通知
			blackCardCount := 0
			normalCount := 0
			for _, item := range data.Data {
				if item.AppearanceID == existingAppearanceID {
					blackCardCount++
					assert.Equal(userappearance.StatusWorn, item.Status, "已有的黑卡外观应该是使用中状态")
					assert.Equal(appearance.FromBlackCard, item.from)
				} else if item.AppearanceID == 50002 {
					normalCount++
					assert.Equal("测试普通进场通知", item.Name)
					assert.Equal(userappearance.StatusOwned, item.Status)
					assert.Equal(appearance.FromCustom, item.from)
				}
			}
			assert.Equal(1, blackCardCount, "不应该重复添加已有的黑卡外观")
			assert.Equal(1, normalCount, "应该包含普通进场通知外观")
		})
	})
}

func TestGetAppearanceResp_findCustomWelcomeMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(191918)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = usermeta.Collection().InsertOne(ctx, bson.M{
		"user_id": testUserID,
		"custom_welcome_message": usermeta.CustomWelcomeMessage{
			Text:       "test",
			ExpireTime: goutil.TimeNow().Add(time.Second).Unix(),
		},
	})
	require.NoError(err)
	r := getAppearanceResp{}
	require.NoError(r.findCustomWelcomeMessage(testUserID, appearance.TypeBadge))
	assert.Nil(r.CustomWelcomeMessage)
	require.NoError(r.findCustomWelcomeMessage(testUserID, appearance.TypeEntryBubble))
	require.NotNil(r.CustomWelcomeMessage)
	assert.Equal("test", r.CustomWelcomeMessage.Text)
}

func TestActionWearAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock VIP RPC 调用
	cancelVipMock := mockVipUserVips()
	defer cancelVipMock()

	testUserID := int64(9874)
	c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, nil)
	c.User().ID = testUserID
	_, err := ActionWearAppearance(c)
	require.Equal(actionerrors.ErrParams, err)

	var param struct {
		AppearanceID int64 `form:"appearance_id" json:"appearance_id"`
		Type         int   `form:"type" json:"type"`
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 删除历史测试数据
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{
		"type":    bson.M{"$in": bson.A{appearance.TypeVehicle, appearance.TypeBadge}},
		"user_id": bson.M{"$in": []int64{testUserID, testUserID + 1}},
	})
	require.NoError(err)

	now := goutil.TimeNow()
	expTime := now.Add(4 * time.Minute).Unix()
	a0 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusOwned,
		UserID:       testUserID,
		AppearanceID: now.Unix(),
		Name:         "测试佩戴座驾",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(2 * time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	expTime = now.Add(240 * time.Hour).Unix()
	a1 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusOwned,
		UserID:       testUserID + 1,
		AppearanceID: 2 + now.Unix(),
		Name:         "测试佩戴座驾",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	a2 := *a1
	a2.UserID = testUserID
	a3 := a2
	a3.AppearanceID++
	a3.Status = userappearance.StatusWorn
	a4 := &userappearance.UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       userappearance.StatusOwned,
		UserID:       testUserID,
		AppearanceID: 1020,
		Name:         "测试佩戴称号 1",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a5 := &userappearance.UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       userappearance.StatusOwned,
		UserID:       testUserID,
		AppearanceID: 1021,
		Name:         "测试佩戴称号 2",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a6 := &userappearance.UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       userappearance.StatusOwned,
		UserID:       testUserID,
		AppearanceID: 1022,
		Name:         "测试佩戴称号 3",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}

	res, err := userappearance.Collection().InsertMany(ctx, []interface{}{a0, a1, a2, a3, a4, a5, a6})
	require.NoError(err)
	param.AppearanceID = 1 + now.Unix()
	param.Type = appearance.TypeVehicle
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	assert.EqualError(err, "该外观不存在、未生效或已过期")

	param.AppearanceID = a2.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	require.NoError(err)
	var wornItem userappearance.UserAppearance
	err = userappearance.Collection().FindOne(ctx,
		bson.M{"_id": res.InsertedIDs[2]}).Decode(&wornItem)
	require.NoError(err)
	assert.Equal(userappearance.StatusWorn, wornItem.Status)
	assert.Equal(userappearance.OwnStatusUsed, wornItem.OwnStatus)
	err = userappearance.Collection().FindOne(ctx,
		bson.M{"_id": res.InsertedIDs[3]}).Decode(&wornItem)
	require.NoError(err)
	assert.Equal(userappearance.StatusOwned, wornItem.Status)

	// 重复佩戴
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	assert.EqualError(err, "已经使用了该外观")

	defer func() {
		_, err = userappearance.Collection().DeleteMany(ctx, bson.M{
			"type":    appearance.TypeAvatarFrame,
			"user_id": nobleUserID,
		})
		assert.NoError(err)
	}()

	// 佩戴不存在的称号
	param.Type = appearance.TypeBadge
	param.AppearanceID = -99999
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	require.EqualError(err, "该外观不存在、未生效或已过期")

	// 佩戴称号
	param.AppearanceID = a4.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	require.NoError(err)
	wearingBadges, err := userappearance.FindBadges(testUserID)
	require.NoError(err)
	require.NotEmpty(wearingBadges)
	require.Len(wearingBadges, 1)
	assert.Equal(a4.AppearanceID, wearingBadges[0].AppearanceID)

	// 重复佩戴称号
	param.AppearanceID = a4.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	assert.EqualError(err, "已经使用了该外观")

	// 佩戴第二个称号
	param.AppearanceID = a5.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	require.NoError(err)
	userappearance.ClearCache(testUserID)
	wearingBadges, err = userappearance.FindBadges(testUserID)
	require.NoError(err)
	require.NotEmpty(wearingBadges)
	require.Len(wearingBadges, 2)
	assert.Equal(a4.AppearanceID, wearingBadges[0].AppearanceID)
	assert.Equal(a5.AppearanceID, wearingBadges[1].AppearanceID)

	// 佩戴第三个称号
	param.AppearanceID = a6.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	assert.EqualError(err, "最多可使用 2 个，请先取消使用中的称号")

	// 佩戴第一个称号
	param.AppearanceID = a4.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = testUserID
	_, err = ActionWearAppearance(c)
	assert.EqualError(err, "已经使用了该外观")

	// 查询贵族外观
	uvMap, err := vip.UserVipInfos(nobleUserID, false, nil)
	require.NoError(err)
	uv := uvMap[vip.TypeLiveNoble]
	require.NotNil(uv)

	// 清理数据
	param.AppearanceID = int64(appearance.TypeAvatarFrame*10 + uv.Level)
	_, err = userappearance.Collection().DeleteOne(ctx, bson.M{"user_id": nobleUserID, "appearance_id": param.AppearanceID})
	require.NoError(err)

	// 佩戴贵族外观
	param.Type = appearance.TypeAvatarFrame
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = nobleUserID
	_, err = ActionWearAppearance(c)
	require.NoError(err)
	err = userappearance.Collection().FindOne(ctx,
		bson.M{
			"user_id":       c.UserID(),
			"type":          param.Type,
			"appearance_id": param.AppearanceID,
		}).Decode(&wornItem)
	require.NoError(err)
	assert.Equal(appearance.FromNoble, wornItem.From)
	assert.Equal(userappearance.StatusWorn, wornItem.Status)
	assert.Equal(userappearance.OwnStatusUsed, wornItem.OwnStatus)
	assert.Equal(param.AppearanceID, wornItem.AppearanceID)

	// 重复佩戴贵族外观
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = nobleUserID
	_, err = ActionWearAppearance(c)
	assert.EqualError(err, "已经使用了该外观")

	t.Run("测试黑卡外观佩戴", func(t *testing.T) {
		testBlackCardUserID := int64(666666)
		testBlackCardLevel := 3
		testBlackCardType := appearance.TypeIdentityBadge
		testBlackCardAppearanceID := appearance.BlackCardAppearanceID(testBlackCardLevel, testBlackCardType)

		// 清理测试数据
		_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testBlackCardUserID})
		require.NoError(err)
		defer func() {
			_, _ = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": testBlackCardUserID})
		}()

		err = liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testBlackCardUserID).Error
		require.NoError(err)
		defer func() {
			_ = liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id = ?", testBlackCardUserID).Error
		}()

		// 准备黑卡外观模版数据
		testBlackCardAppearance := &appearance.Appearance{
			ID:         testBlackCardAppearanceID,
			Name:       "测试黑卡身份铭牌",
			Type:       testBlackCardType,
			From:       appearance.FromBlackCard,
			Icon:       "test_blackcard_icon.png",
			Image:      "test_blackcard_image.png",
			StartTime:  now.Unix(),
			ExpireTime: nil, // 永久有效
		}

		// 插入黑卡外观模版
		_, err = appearance.Collection().DeleteMany(ctx, bson.M{"id": testBlackCardAppearanceID})
		require.NoError(err)
		defer func() {
			_, _ = appearance.Collection().DeleteMany(ctx, bson.M{"id": testBlackCardAppearanceID})
		}()

		_, err = appearance.Collection().InsertOne(ctx, testBlackCardAppearance)
		require.NoError(err)

		testBlackCardStartTime := now.Unix()
		testBlackCardExpireTime := now.Add(30 * 24 * time.Hour).Unix()

		param := struct {
			AppearanceID int64 `form:"appearance_id" json:"appearance_id"`
			Type         int   `form:"type" json:"type"`
		}{
			AppearanceID: testBlackCardAppearanceID,
			Type:         testBlackCardType,
		}

		t.Run("用户没有黑卡时不能佩戴黑卡外观", func(t *testing.T) {
			c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
			c.User().ID = testBlackCardUserID
			_, err := ActionWearAppearance(c)
			assert.EqualError(err, "该外观不存在、未生效或已过期")
		})

		t.Run("用户有黑卡时可以成功佩戴黑卡外观", func(t *testing.T) {
			// 创建黑卡记录
			testBlackCard := liveuserblackcard.LiveUserBlackCard{
				UserID:      testBlackCardUserID,
				BlackCardID: int64(testBlackCardLevel),
				StartTime:   testBlackCardStartTime,
				ExpireTime:  testBlackCardExpireTime,
			}
			err := testBlackCard.DB().Create(&testBlackCard).Error
			require.NoError(err)

			// 佩戴黑卡外观
			c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
			c.User().ID = testBlackCardUserID
			resp, err := ActionWearAppearance(c)
			require.NoError(err)
			assert.Equal("成功使用该外观", resp)

			// 验证黑卡外观已佩戴
			var wornBlackCardItem userappearance.UserAppearance
			err = userappearance.Collection().FindOne(ctx, bson.M{
				"user_id":       testBlackCardUserID,
				"appearance_id": testBlackCardAppearanceID,
				"type":          testBlackCardType,
			}).Decode(&wornBlackCardItem)
			require.NoError(err)
			assert.Equal(userappearance.StatusWorn, wornBlackCardItem.Status)
			assert.Equal(userappearance.OwnStatusUsed, wornBlackCardItem.OwnStatus)
			assert.Equal(testBlackCardAppearanceID, wornBlackCardItem.AppearanceID)
			assert.Equal(appearance.FromBlackCard, wornBlackCardItem.From)
			require.NotNil(wornBlackCardItem.ExpireTime)
			assert.Equal(testBlackCardExpireTime, *wornBlackCardItem.ExpireTime)
			assert.Equal(testBlackCardStartTime, wornBlackCardItem.StartTime)
			assert.Equal("测试黑卡身份铭牌", wornBlackCardItem.Name)
		})
	})
}

func TestActionTakeOffAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// Mock VIP RPC 调用
	cancelVipMock := mockVipUserVips()
	defer cancelVipMock()

	c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, nil)
	_, err := ActionTakeOffAppearance(c)
	require.Equal(actionerrors.ErrParams, err)

	var param struct {
		AppearanceID int64 `form:"appearance_id" json:"appearance_id"`
		Type         int   `form:"type" json:"type"`
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 删除历史测试数据
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{
		"type": bson.M{"$in": bson.A{
			appearance.TypeVehicle,
			appearance.TypeAvatarFrame,
			appearance.TypeMessageBubble,
			appearance.TypeBadge,
		}},
		"name": bson.M{"$in": bson.A{
			"测试卸下座驾",
			"测试卸下头像框",
			"测试卸下气泡框",
			"测试佩戴称号 1",
			"测试佩戴称号 2",
		}},
	})
	require.NoError(err)

	now := goutil.TimeNow()
	expTime := now.Add(4 * time.Minute).Unix()
	testUserID := int64(1234)
	a1 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusOwned,
		UserID:       testUserID,
		AppearanceID: 3 + now.Unix(),
		Name:         "测试卸下座驾",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(2 * time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	expTime = now.Add(240 * time.Hour).Unix()
	a2 := &userappearance.UserAppearance{
		Type:         appearance.TypeVehicle,
		Status:       userappearance.StatusWorn,
		UserID:       testUserID + 1,
		AppearanceID: 4 + now.Unix(),
		Name:         "测试卸下座驾",
		MessageBar: &appearance.MessageBar{
			Message: "${username}测试${vehicle_name}",
			Image:   "oss://testdata/test.webp",
		},
		StartTime:  now.Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}
	a3 := *a2
	a3.UserID = testUserID
	a3.Name = "测试卸下头像框"
	a4 := &userappearance.UserAppearance{
		Type:         appearance.TypeMessageBubble,
		Status:       userappearance.StatusWorn,
		UserID:       testUserID,
		AppearanceID: 1011,
		Name:         "测试卸下气泡框",
		Image:        "oss://testdata/test.webp",
		Frame:        "oss://testdata/test.webp",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a5 := &userappearance.UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       userappearance.StatusWorn,
		UserID:       testUserID,
		AppearanceID: 1020,
		Name:         "测试佩戴称号 1",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}
	a6 := &userappearance.UserAppearance{
		Type:         appearance.TypeBadge,
		Status:       userappearance.StatusWorn,
		UserID:       testUserID,
		AppearanceID: 1021,
		Name:         "测试佩戴称号 2",
		StartTime:    now.Add(-time.Minute).Unix(),
		ExpireTime:   &expTime,
	}

	res, err := userappearance.Collection().InsertMany(ctx, []interface{}{a1, a2, a3, a4, a5, a6})
	require.NoError(err)

	param.AppearanceID = a1.AppearanceID
	param.Type = appearance.TypeVehicle
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, param)
	c.User().ID = testUserID
	_, err = ActionTakeOffAppearance(c)
	assert.Equal(actionerrors.ErrParamsMsg("该外观不存在、未生效或已过期"), err)

	param.AppearanceID = a3.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, param)
	c.User().ID = testUserID
	_, err = ActionTakeOffAppearance(c)
	require.NoError(err)
	var appearanceItem userappearance.UserAppearance
	err = userappearance.Collection().FindOne(ctx,
		bson.M{"_id": res.InsertedIDs[2]}).Decode(&appearanceItem)
	require.NoError(err)
	assert.Equal(userappearance.StatusOwned, appearanceItem.Status)

	// 重复卸下
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, param)
	c.User().ID = testUserID
	_, err = ActionTakeOffAppearance(c)
	assert.EqualError(err, "已经没有在使用该外观了")

	// 卸下称号
	param.Type = appearance.TypeBadge
	param.AppearanceID = a5.AppearanceID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, param)
	c.User().ID = testUserID
	_, err = ActionTakeOffAppearance(c)
	require.NoError(err)

	// 验证
	wearingBadges, err := userappearance.FindBadges(testUserID)
	require.NoError(err)
	require.NotEmpty(wearingBadges)
	require.Len(wearingBadges, 1)
	assert.Equal(a6.AppearanceID, wearingBadges[0].AppearanceID)

	// 先佩戴贵族外观
	uvMap, err := vip.UserVipInfos(nobleUserID, false, nil)
	require.NoError(err)
	uv := uvMap[vip.TypeLiveNoble]
	require.NotNil(uv)
	param.AppearanceID = int64(appearance.TypeAvatarFrame*10 + uv.Level)
	param.Type = appearance.TypeAvatarFrame
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/wear", true, param)
	c.User().ID = nobleUserID
	_, err = ActionWearAppearance(c)
	require.NoError(err)
	var wornItem userappearance.UserAppearance
	err = userappearance.Collection().FindOne(ctx,
		bson.M{
			"user_id":       c.UserID(),
			"type":          param.Type,
			"appearance_id": param.AppearanceID,
		}).Decode(&wornItem)
	require.NoError(err)
	assert.Equal(appearance.FromNoble, wornItem.From)
	assert.Equal(userappearance.StatusWorn, wornItem.Status)
	assert.Equal(userappearance.OwnStatusUsed, wornItem.OwnStatus)
	assert.Equal(param.AppearanceID, wornItem.AppearanceID)

	// 卸下贵族外观
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, param)
	c.User().ID = nobleUserID
	_, err = ActionTakeOffAppearance(c)
	require.NoError(err)

	err = userappearance.Collection().FindOne(ctx,
		bson.M{
			"user_id":       c.UserID(),
			"type":          param.Type,
			"appearance_id": param.AppearanceID,
		}).Decode(&wornItem)
	assert.True(mongodb.IsNoDocumentsError(err))

	// 卸下气泡框
	param.AppearanceID = a4.AppearanceID
	param.Type = appearance.TypeMessageBubble
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/takeoff", true, param)
	c.User().ID = testUserID
	_, err = ActionTakeOffAppearance(c)
	require.NoError(err)
}

func TestActionInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/info", false, nil)
	_, err := ActionInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	uri := fmt.Sprintf("/api/v2/user/appearance/info?appearance_id=%d&type=%d", 999999999, appearance.TypeBadge)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	_, err = ActionInfo(c)
	assert.EqualError(err, "该外观不存在")

	aItem, err := appearance.FindOne(1008, appearance.TypeBadge)
	require.NoError(err)
	require.NotNil(aItem)

	uri = fmt.Sprintf("/api/v2/user/appearance/info?appearance_id=%d&type=%d", aItem.ID, appearance.TypeBadge)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	r, err := ActionInfo(c)
	require.NoError(err)
	require.NotNil(r)
	resp, ok := r.(*infoResp)
	require.True(ok)
	assert.Equal(aItem.ID, resp.AppearanceID)
	assert.Equal(aItem.Name, resp.Name)
	assert.Equal(aItem.Intro, resp.Intro)
	assert.Equal(aItem.Type, resp.Type)
	assert.Equal(storage.ParseSchemeURL(aItem.Icon), resp.IconURL)

	// 测试获取黑卡铭牌详情（下发跳转链接）
	testID := int64(1009)
	expTime := goutil.TimeNow().Add(240 * time.Hour).Unix()
	a := &appearance.Appearance{
		ID:         testID,
		Type:       appearance.TypeIdentityBadge,
		Name:       "测试黑卡铭牌",
		From:       appearance.FromBlackCard,
		StartTime:  goutil.TimeNow().Add(-time.Minute).Unix(),
		ExpireTime: &expTime,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = appearance.Collection().DeleteMany(ctx, bson.M{"appearance_id": testID})
	require.NoError(err)
	_, err = appearance.Collection().InsertOne(ctx, a)
	require.NoError(err)

	uri = fmt.Sprintf("/api/v2/user/appearance/info?appearance_id=%d&type=%d", testID, appearance.TypeIdentityBadge)
	c = handler.NewTestContext(http.MethodGet, uri, false, nil)
	r, err = ActionInfo(c)
	require.NoError(err)
	require.NotNil(r)
	resp, ok = r.(*infoResp)
	require.True(ok)
	assert.EqualValues(testID, resp.AppearanceID)
	assert.Equal("https://www.missevan.com/mevent/9000?from_room_id=__ROOM_ID__&navhide=1&type=1", resp.IntroOpenURL)
}

func TestWearActionParam_NotifyRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testImage := "test_url"
	testImageURL := "https://static-test.missevan.com/test_url"
	a := appearance.Appearance{
		Image: testImage,
	}

	r, err := room.Update(4381915, bson.M{"custom_tag_id": 10001})
	require.NoError(err)

	param := wearActionParam{
		userAppearance: userappearance.NewUserAppearance(r.CreatorID, &a),
		userID:         r.CreatorID,
	}

	var count int
	wear := true
	cancel := mrpc.SetMock("im://broadcast", func(input interface{}) (output interface{}, err error) {
		count++
		var body struct {
			Payload *struct {
				Room struct {
					CustomTagID int64 `json:"custom_tag_id"`
					Background  *struct {
						PendantImage    string `json:"pendant_image"`
						PendantImageURL string `json:"pendant_image_url"`
					} `json:"background"`
				} `json:"room"`
			} `json:"payload"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		payload := body.Payload
		require.NotNil(payload)
		bg := payload.Room.Background
		if wear {
			assert.Equal(testImageURL, bg.PendantImageURL)
		} else {
			assert.Nil(bg)
		}
		assert.EqualValues(10001, payload.Room.CustomTagID)

		return "success", nil
	})
	defer cancel()

	param.notifyRoom(wear)
	assert.Zero(count, "外观类型不是挂件不需要下发消息")
	// 穿上外观
	param.Type = appearance.TypeBackgroundPendant
	param.notifyRoom(wear)
	assert.Equal(1, count)

	// 卸下外观
	wear = false
	param.notifyRoom(wear)
	assert.Equal(2, count)
}

func TestActionCustomizeWelcome(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("go://scan/text", func(input any) (output any, err error) {
		return []any{map[string]bool{
			"pass": true,
		}}, nil
	})
	defer cancel()
	param := customizeWelcomeParam{
		WelcomeMessageText: "测试",
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/welcome/customize", true, param)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": c.UserID()})
	require.NoError(err)
	_, _, err = ActionCustomizeWelcome(c)
	assert.EqualError(err, "未拥有自定义进场欢迎语资格")
	_, err = usermeta.Collection().InsertOne(ctx, bson.M{
		"user_id": c.UserID(),
		"custom_welcome_message": &usermeta.CustomWelcomeMessage{
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		},
	})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/welcome/customize", true, param)
	resp, msg, err := ActionCustomizeWelcome(c)
	require.NoError(err)
	assert.Equal("success", msg)
	require.NotNil(resp)
	welcomeMsg := resp.(bson.M)["custom_welcome_message"].(*usermeta.CustomWelcomeMessage)
	require.NotNil(welcomeMsg)
	assert.Equal("测试", welcomeMsg.Text)
}

func TestNewCustomizeWelcomeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := customizeWelcomeParam{
		WelcomeMessageText: "                                测试",
	}
	c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/welcome/customize", true, param)
	p, err := newCustomizeWelcomeParam(c)
	require.NoError(err)
	require.NotNil(p)
	assert.Equal("测试", p.WelcomeMessageText)
}

func TestCustomizeWelcomeParam_check(t *testing.T) {
	assert := assert.New(t)

	checkRes := map[string]bool{
		"pass": true,
	}
	cancel := mrpc.SetMock("go://scan/text", func(input any) (output any, err error) {
		return []any{checkRes}, nil
	})
	defer cancel()

	p := customizeWelcomeParam{
		WelcomeMessageText: "测试测试测试测试测试测试",
	}
	assert.EqualError(p.check(), "欢迎语最多 10 个字")
	p.WelcomeMessageText = "测😃试"
	assert.EqualError(p.check(), "暂不支持 emoji 输入")
	p.WelcomeMessageText = "测🇯🇵试"
	assert.EqualError(p.check(), "暂不支持 emoji 输入")
	c := handler.NewTestContext(http.MethodPost, "/api/v2/user/appearance/welcome/customize", true, p)
	p = customizeWelcomeParam{
		WelcomeMessageText: "测试",
		c:                  c,
	}
	assert.NoError(p.check())
	checkRes["pass"] = false
	assert.EqualError(p.check(), "含有违规内容，请重新输入")
	p.WelcomeMessageText = ""
	assert.NoError(p.check())
}
