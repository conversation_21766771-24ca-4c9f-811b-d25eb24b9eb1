package appearance

import (
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type tab struct {
	Type int    `json:"type"`
	Name string `json:"name"`
}

type tabsResp struct {
	Data []tab `json:"data"`
}

// ActionTabs 获取外观类型列表
/**
 * @api {get} /api/v2/user/appearance/tabs 获取外观类型列表
 * @apiVersion 0.1.0
 * @apiGroup appearance
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "type": 5, // 外观类型
 *           "name": "称号"
 *         },
 *         {
 *           "type": 2,
 *           "name": "头像框"
 *         },
 *         {
 *           "type": 4,
 *           "name": "气泡框"
 *         },
 *         {
 *           "type": 1,
 *           "name": "座驾"
 *         },
 *         {
 *           "type": 3,
 *           "name": "名片框"
 *         },
 *         {
 *           "type": 8,
 *           "name": "红包封面"
 *         },
 *         {
 *           "type": 7,
 *           "name": "进场通知"
 *         },
 *         {
 *           "type": 6,
 *           "name": "直播间挂件"
 *         }
 *       ]
 *     }
 *   }
 */
func ActionTabs(c *handler.Context) (handler.ActionResponse, error) {
	resp := tabsResp{Data: []tab{
		{Type: appearance.TypeBadge, Name: "称号"},
		{Type: appearance.TypeAvatarFrame, Name: "头像框"},
		{Type: appearance.TypeMessageBubble, Name: "气泡框"},
		{Type: appearance.TypeVehicle, Name: "座驾"},
		{Type: appearance.TypeCardFrame, Name: "名片框"},
		{Type: appearance.TypeRedPacket, Name: "红包封面"},
		{Type: appearance.TypeEntryBubble, Name: "进场通知"},
		{Type: appearance.TypeBackgroundPendant, Name: "直播间挂件"},
	}}
	return resp, nil
}
