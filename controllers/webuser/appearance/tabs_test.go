package appearance

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionTabs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp, err := ActionTabs(
		handler.NewTestContext(http.MethodGet, "/api/v2/user/appearance/tabs", false, nil))
	require.NoError(err)
	res, ok := resp.(tabsResp)
	require.True(ok)
	assert.Equal(8, len(res.Data))
	assert.Equal(appearance.TypeBadge, res.Data[0].Type)
	assert.Equal("称号", res.Data[0].Name)
}
