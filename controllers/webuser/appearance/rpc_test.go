package appearance

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestRPCTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(appearanceAddParam{}, "type", "user_id", "duration", "appearance_id")
}

func TestActionAppearanceAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数为空
	c := handler.NewRPCTestContext("/rpc/user/appearance/add", nil)
	_, err := ActionAppearanceAdd(c)
	require.Equal(actionerrors.ErrParams, err)

	// 参数类型超出限制
	var param appearanceAddParam
	param.Type = appearance.TypeLimit
	c = handler.NewRPCTestContext("/rpc/user/appearance/add", param)
	_, err = ActionAppearanceAdd(c)
	require.Equal(actionerrors.ErrParams, err)

	// 参数中持续时间错误
	param.Type = appearance.TypeCardFrame
	param.UserID = int64(1000)
	param.Duration = 0
	param.AppearanceID = 1
	c = handler.NewRPCTestContext("/rpc/user/appearance/add", param)
	_, err = ActionAppearanceAdd(c)
	require.Equal(actionerrors.ErrParams, err)

	// 物品不存在
	param.Type = appearance.TypeCardFrame
	param.UserID = int64(1000)
	param.Duration = int64(7 * 24 * 60 * 60)
	param.AppearanceID = 99999
	c = handler.NewRPCTestContext("/rpc/user/appearance/add", param)
	_, err = ActionAppearanceAdd(c)
	require.Equal(actionerrors.ErrParamsMsg("该外观当前不可用"), err)

	now := goutil.TimeNow()
	expTime := now.Add(2 * 7 * 24 * time.Hour).Unix()
	a := &appearance.Appearance{
		ID:             1003,
		Name:           "测试外观模版下发",
		Type:           appearance.TypeCardFrame,
		From:           appearance.FromCustom,
		Effect:         "oss://testdata/test.webp",
		WebEffect:      "oss://testdata/test.webp",
		EffectDuration: 5,
		Image:          "oss://testdata/test.webp",
		StartTime:      now.Unix(),
		ExpireTime:     &expTime,
	}
	nobleA := &appearance.Appearance{
		ID:         1010,
		Name:       "测试贵族外观模版下发",
		Type:       appearance.TypeCardFrame,
		From:       appearance.FromNoble,
		Image:      "oss://testdata/test.webp",
		StartTime:  now.Unix(),
		ExpireTime: &expTime,
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = appearance.Collection().DeleteOne(ctx, bson.M{"id": a.ID})
	require.NoError(err)
	defer func() {
		_, err = appearance.Collection().DeleteOne(ctx, bson.M{"id": nobleA.ID})
		assert.NoError(err)
	}()
	_, err = appearance.Collection().InsertMany(ctx, []interface{}{a, nobleA})
	require.NoError(err)

	param.UserID = int64(12)
	param.Type = appearance.TypeCardFrame
	param.AppearanceID = a.ID
	c = handler.NewRPCTestContext("/rpc/user/appearance/add", param)
	_, err = ActionAppearanceAdd(c)
	require.NoError(err)

	aItem, err := userappearance.FindValidAppearance(a.ID, param.UserID, appearance.TypeCardFrame)
	require.NoError(err)
	require.NotNil(aItem)
	assert.Equal(appearance.FromCustom, aItem.From)
	assert.Equal(aItem.UserID, param.UserID)
	expectedTime := now.Unix() + param.Duration
	assert.GreaterOrEqual(*aItem.ExpireTime, expectedTime)

	param.AppearanceID = nobleA.ID
	c = handler.NewRPCTestContext("/rpc/user/appearance/add", param)
	_, err = ActionAppearanceAdd(c)
	require.Equal(actionerrors.ErrParamsMsg("该外观当前不可用"), err)
}
