package appearance

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	service.InitTest(true)

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)

	h := Handler()
	assert.Equal("appearance", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h,
		"list", "wear", "takeoff", "info", "tabs",
	)
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := HandlerV2()
	assert.Equal("appearance", h.Name)
	kc := tutil.New<PERSON>eyChecker(t, tutil.Actions)
	kc.Check(h,
		"welcome/customize",
	)
}
