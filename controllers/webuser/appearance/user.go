package appearance

import (
	"fmt"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/userapi/scan"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// lifetime 永久与否
const (
	limitedTime = iota // 有限时间
	lifeTime           // 永久
)

type getAppearanceParam struct {
	p        int64
	pageSize int64
	Type     int
}

type getAppearanceItem struct {
	AppearanceID int64  `json:"appearance_id"`
	Name         string `json:"name"`
	Type         int    `json:"type"`
	Status       int    `json:"status"`
	OwnStatus    int    `json:"own_status"`
	Intro        string `json:"intro"`
	Lifetime     int    `json:"lifetime"`
	IconURL      string `json:"icon_url"`

	StartTime  int64 `json:"start_time"`
	ExpireTime int64 `json:"expire_time,omitempty"`

	from int
}

type getAppearanceResp struct {
	CustomWelcomeMessage *usermeta.CustomWelcomeMessage `json:"custom_welcome_message,omitempty"`

	Data       []getAppearanceItem `json:"data"`
	Pagination goutil.Pagination   `json:"pagination"`
}

// ActionListAppearance 查看用户所有的外观列表
/**
 * @api {get} /api/v2/user/appearance/list 查看用户所有的外观列表
 * @apiDescription 查看用户所有的外观列表，需要指定类型
 * @apiVersion 0.1.0
 * @apiGroup appearance
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 * @apiParam {number=1,2,3,4,5,6,7,8,10} type 需要筛选的外观物品类型，1 为座驾，2 为头像框，3 为名片框，4 为气泡框，5 为称号，6 为直播间挂件，7 为进场通知（气泡），8 为红包封面，10 为送礼通知
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "custom_welcome_message": { // 自定义进场欢迎语，仅在查询进场通知时对有效资格的用户下发，其余情景不下发
 *         "text": "欢迎光临本直播间", // 欢迎文案，未修改过默认为空字符串
 *         "expire_time": 1618714747 // 失效时间时间戳，单位：秒
 *       },
 *       "data": [
 *         {
 *           "appearance_id": 1,
 *           "name": "座驾", // 外观名称
 *           "type": 0, // 外观类型
 *           "status": 1, // 佩戴状态，1 为已持有，2 为正在使用，3 为已过期
 *           "own_status": 1, // 物品历史状态，0 为新物品，1 为用过的物品
 *           "intro": "", // 外观的来源描述，比如来自："年度活动"
 *           "lifetime": 0, // 是否永久，0 为有限时间，1 为永久
 *           "start_time": 1618196305, // 开始时间时间戳，单位：秒
 *           "expire_time": 1618714747 // 失效时间时间戳，单位：秒，如果是永久外观则不返回该字段
 *         }
 *       ],
 *       "pagination": {
 *         "count": 50,
 *         "maxpage": 5,
 *         "p": 1,
 *         "pagesize": 10
 *       }
 *     }
 *   }
 *
 */
func ActionListAppearance(c *handler.Context) (handler.ActionResponse, error) {
	var param getAppearanceParam
	var err error
	param.p, param.pageSize, err = c.GetParamPage(&handler.PageOption{})
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.Type, err = c.GetParamInt("type")
	// TypeVehicle 为当前外观中心第一种类型枚举值
	if err != nil ||
		param.Type >= appearance.TypeLimit ||
		param.Type < appearance.TypeVehicle {
		return nil, actionerrors.ErrParams
	}

	appearances, err := userappearance.ListAppearanceByUserID(c.UserID(), param.Type)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"user_id": c.UserID})
	}
	appearancesMap := goutil.ToMap(appearances, "AppearanceID").(map[int64]userappearance.UserAppearance)

	// 查询贵族外观
	uvMap, err := vip.UserVipInfos(c.UserID(), false, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 如果没有佩戴指定类型的贵族外观，则新建
	for _, uv := range uvMap {
		if uv == nil || uv.Info == nil || !uv.IsActive() {
			continue
		}
		if uv.Type == vip.TypeLiveNoble {
			// 若体验贵族生效中，则直接跳过普通贵族
			if uvt, ok := uvMap[vip.TypeLiveTrialNoble]; ok && uvt.IsActive() {
				continue
			}
		}
		appearanceID := appearance.VipAppearanceID(uv.Type, uv.Level, param.Type)
		if _, exists := appearancesMap[appearanceID]; exists {
			continue
		}
		aItem, err := appearance.FindVipAppearance(uv.Type, uv.Level, param.Type)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		if aItem == nil {
			continue
		}
		nobleAppearance := userappearance.NewUserAppearance(c.UserID(), aItem)
		nobleAppearance.SetStatus(userappearance.StatusOwned, uv.ExpireTime, uv.Info.CreateTime)
		nobleAppearance.ParseSchemeURL()
		appearances = append(appearances, *nobleAppearance)
	}

	// 查询黑卡信息
	blackCardInfo, err := liveuserblackcard.FindUserActiveBlackCard(c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 如果有有效的黑卡，且没有佩戴指定类型的黑卡外观，则新建
	if blackCardInfo != nil && blackCardInfo.Level > 0 {
		// 检查当前查询的外观类型是否在黑卡可用的外观类型列表中
		availableTypes := appearance.BlackCardAppearanceTypes(blackCardInfo.Level)
		for _, t := range availableTypes {
			if t == param.Type {
				appearanceID := appearance.BlackCardAppearanceID(blackCardInfo.Level, param.Type)
				if _, exists := appearancesMap[appearanceID]; exists {
					continue
				}

				// 查找黑卡外观
				aItem, err := appearance.FindOne(appearanceID, param.Type)
				if err != nil {
					logger.Error(err)
					// PASS
				}
				if aItem == nil {
					continue
				}

				blackCardAppearance := userappearance.NewUserAppearance(c.UserID(), aItem)
				blackCardAppearance.SetStatus(userappearance.StatusOwned, blackCardInfo.ExpireTime, blackCardInfo.StartTime)
				blackCardAppearance.ParseSchemeURL()
				appearances = append(appearances, *blackCardAppearance)
				break
			}
		}
	}

	var resp getAppearanceResp
	err = resp.findCustomWelcomeMessage(c.UserID(), param.Type)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.Pagination = goutil.MakePagination(int64(len(appearances)), param.p, param.pageSize)
	if !resp.Pagination.Valid() {
		resp.Data = make([]getAppearanceItem, 0)
		return resp, nil
	}
	// WORKAROUND: 由于排序的逻辑相对复杂，这里暂时使用全部查询出来，然后内存里分页的方式
	userappearance.SortAppearances(appearances)
	resp.Data = splitAppearanceItemsSlice(appearances, param.p, param.pageSize)
	return resp, nil
}

func (r *getAppearanceResp) findCustomWelcomeMessage(userID int64, appearanceType int) error {
	// 不是进场通知类型时不显示自定义欢迎语
	if appearanceType != appearance.TypeEntryBubble {
		return nil
	}
	var err error
	r.CustomWelcomeMessage, err = usermeta.FindUserValidCustomWelcomeMessage(userID)
	return err
}

func splitAppearanceItemsSlice(slice []userappearance.UserAppearance, page, pageSize int64) []getAppearanceItem {
	start := (page - 1) * pageSize
	end := start + pageSize

	res := make([]getAppearanceItem, 0, pageSize)
	for i := start; i < int64(len(slice)) && i < end; i++ {
		item := getAppearanceItem{
			AppearanceID: slice[i].AppearanceID,
			Name:         slice[i].Name,
			Type:         slice[i].Type,
			Status:       slice[i].Status,
			OwnStatus:    slice[i].OwnStatus,
			Intro:        slice[i].Intro,
			IconURL:      storage.ParseSchemeURL(slice[i].Icon),
			StartTime:    slice[i].StartTime,
			from:         slice[i].From,
		}
		if slice[i].ExpireTime == nil {
			item.Lifetime = lifeTime
		} else {
			item.Lifetime = limitedTime
			item.ExpireTime = *slice[i].ExpireTime
			if item.ExpireTime < goutil.TimeNow().Unix() {
				item.Status = userappearance.StatusExpired
				// 如果已经过期但是从未使用过，则应该返回 OwnStatusUsed
				if item.OwnStatus == userappearance.OwnStatusNew {
					item.OwnStatus = userappearance.OwnStatusUsed
				}
			}
			if !appearance.IsVip(item.from) {
				// 此处减一秒，以供前端展示时取最后的过期时间点
				item.ExpireTime = item.ExpireTime - 1
			}
		}
		res = append(res, item)
	}

	return res
}

type wearActionParam struct {
	AppearanceID int64 `form:"appearance_id" json:"appearance_id"`
	Type         int   `form:"type" json:"type"`

	userID         int64
	updates        []mongo.WriteModel
	userAppearance *userappearance.UserAppearance
}

// ActionWearAppearance 佩戴指定的外观
/**
 * @api {post} /api/v2/user/appearance/wear 佩戴指定的外观
 * @apiDescription 佩戴指定的外观
 * @apiVersion 0.1.0
 * @apiGroup appearance
 *
 * @apiParam {Number} appearance_id 需要佩戴的外观物品 ID
 * @apiParam {number=1,2,3,4,5,6,7,8,10} type 需要佩戴的外观物品类型，1 为座驾，2 为头像框，3 为名片框，4 为气泡框，5 为称号，6 为直播间挂件，7 为进场通知，8 为红包封面，10 为送礼通知
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "成功使用该外观"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 佩戴直播间挂件消息
 *   {
 *     "type": "room",
 *     "event": "update",
 *     "room_id": 65261414,
 *     "room": {
 *       "room_id": 65261414,
 *       "catalog_id": 115,
 *       "custom_tag_id": 10001,
 *       "name": "直播间名字",
 *       "announcement": "公告",
 *       "creator_id": 65261514,
 *       "type": "connect",
 *       "notice": "通知",
 *       "background": {
 *         "enable": true,
 *         "image_url": "http://static.maoercdn.com/background/icon01.png",
 *         "pendant_image_url": "http://static.maoercdn.com/background/icon01.png",
 *         "opacity": 1.0,
 *       },
 *       "cover_url": "https://static.maoercdn.com/profile/01.png"
 *     }
 *   }
 */
func ActionWearAppearance(c *handler.Context) (handler.ActionResponse, error) {
	var param wearActionParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = param.check(c)
	if err != nil {
		return nil, err
	}

	// 判断外观是否已经存在并且佩戴了，贵族只存在 佩戴中 的状态，一同在此处进行判断
	if param.userAppearance != nil && param.userAppearance.Status == userappearance.StatusWorn {
		return nil, actionerrors.ErrParamsMsg("已经使用了该外观")
	}

	switch param.Type {
	case appearance.TypeBadge:
		err = param.wearBadges()
		if err != nil {
			return nil, err
		}
	default:
		err = param.wear()
		if err != nil {
			return nil, err
		}
		param.notifyRoom(true)
	}

	return "成功使用该外观", nil
}

func (param *wearActionParam) notifyRoom(wear bool) {
	if param.Type != appearance.TypeBackgroundPendant {
		// 不是挂件不需要发送通知
		return
	}
	r, err := room.FindOne(bson.M{"creator_id": param.userID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		logger.Error(err)
		return
	}
	// 没有开播不需要发送
	if r == nil || r.Status.Open == 0 {
		return
	}
	// 更新直播间挂件信息
	if wear {
		r.SetPendant(param.userAppearance)
	} else {
		r.SetPendant(nil)
	}
	// 发送消息
	err = r.NotifyUpdate()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *wearActionParam) wear() error {
	// 查询贵族外观
	uvMap, err := vip.UserVipInfos(param.userID, false, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	uv := vip.UserVipFromAppearanceID(param.AppearanceID, uvMap)
	// 如果 AppearanceID 匹配，视为操作贵族外观
	isNoble := uv != nil && uv.Info != nil && uv.IsActive() &&
		param.AppearanceID == appearance.VipAppearanceID(uv.Type, uv.Level, param.Type)
	// 查询用户当前有效黑卡
	ub, err := liveuserblackcard.FindUserActiveBlackCard(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 如果 AppearanceID 匹配，视为操作黑卡外观
	isBlackCard := ub != nil && param.AppearanceID == appearance.BlackCardAppearanceID(ub.Level, param.Type)
	now := goutil.TimeNow().Unix()
	param.updates = make([]mongo.WriteModel, 3)
	// 对于贵族和黑卡外观，移除所有因为过期需要移除的外观以及当前类型的外观
	param.updates[0] = mongo.NewDeleteManyModel().SetFilter(bson.M{
		"user_id": param.userID,
		"from": bson.M{
			"$in": append(appearance.VipFromList(), appearance.FromBlackCard),
		},
		"$or": bson.A{
			bson.M{"expire_time": bson.M{"$lt": now}},
			// NOTICE: 这里会删除用户所有贵族和黑卡的同类型外观（导致 user_appearances 中数据与用户应该拥有的外观数量不一致），
			// 但会在外观列表接口按照用户的贵族或黑卡身份构建相应的外观数据，不会影响用户重新佩戴
			bson.M{"type": param.Type},
		},
	})

	// 如果有别的已佩戴物品，则全数卸下
	param.updates[1] = userappearance.NewTakeOffAppearancesUpdateByType(param.userID, param.Type)

	if isNoble { // 操作对象为贵族外观
		// 查询贵族外观模版
		nobleAppearance, err := appearance.FindVipAppearance(uv.Type, uv.Level, param.Type)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if nobleAppearance == nil {
			return actionerrors.ErrParamsMsg("该外观不存在、未生效或已过期")
		}

		// 转换为 UserAppearance，从 appearance 模版覆盖数据
		newUserAppearance := userappearance.NewUserAppearance(param.userID, nobleAppearance)
		newUserAppearance.SetStatus(userappearance.StatusWorn, uv.ExpireTime, 0)

		// 佩戴物品
		param.updates[2] = mongo.NewInsertOneModel().SetDocument(newUserAppearance)
	} else if isBlackCard { // 操作对象为黑卡外观
		// 查询黑卡外观模版
		blackCardAppearance, err := appearance.FindOne(param.AppearanceID, param.Type)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if blackCardAppearance == nil {
			return actionerrors.ErrParamsMsg("该外观不存在、未生效或已过期")
		}

		// 转换为 UserAppearance，从 appearance 模版覆盖数据
		newUserAppearance := userappearance.NewUserAppearance(param.userID, blackCardAppearance)
		newUserAppearance.SetStatus(userappearance.StatusWorn, ub.ExpireTime, ub.StartTime)

		// 佩戴物品
		param.updates[2] = mongo.NewInsertOneModel().SetDocument(newUserAppearance)
	} else { // 操作对象为普通外观
		if param.userAppearance == nil {
			return actionerrors.ErrParamsMsg("该外观不存在、未生效或已过期")
		}

		// 佩戴物品
		param.updates[2] = mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": param.userAppearance.OID}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"status":        userappearance.StatusWorn,
					"own_status":    userappearance.OwnStatusUsed,
					"modified_time": now,
				},
			})
	}

	err = param.writeData()
	if err != nil {
		return err
	}

	return nil
}

func (param *wearActionParam) wearBadges() error {
	if param.userAppearance == nil {
		return actionerrors.ErrParamsMsg("该外观不存在、未生效或已过期")
	}

	// 查询所有用户当前佩戴中的称号外观
	wearingBadges, err := userappearance.FindBadges(param.userID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(wearingBadges) >= userappearance.MaxWearingBadgesNum {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("最多可使用 %d 个，请先取消使用中的称号", userappearance.MaxWearingBadgesNum))
	}

	// 将当前正在操作的称号放到最后一位
	wearingBadges = append(wearingBadges, param.userAppearance)
	userappearance.AssignBadgesPosition(wearingBadges)
	// 卸下所有外观
	now := goutil.TimeNow().Unix()
	param.updates = make([]mongo.WriteModel, 0, 3)
	param.updates = append(param.updates, userappearance.NewTakeOffAppearancesUpdateByType(param.userID, param.Type))

	// 佩戴称号
	for _, v := range wearingBadges {
		param.updates = append(param.updates, mongo.NewUpdateOneModel().
			SetFilter(bson.M{
				"appearance_id": v.AppearanceID,
				"user_id":       param.userID,
				"type":          param.Type,
				"status":        userappearance.StatusOwned,
			}).
			SetUpdate(bson.M{
				"$set": bson.M{
					"status":        userappearance.StatusWorn,
					"own_status":    userappearance.OwnStatusUsed,
					"modified_time": now,
					"position":      v.Position,
				},
			}))
	}

	err = param.writeData()
	if err != nil {
		return err
	}

	return nil
}

// ActionTakeOffAppearance 卸下指定的外观
/**
 * @api {post} /api/v2/user/appearance/takeoff 卸下指定的外观
 * @apiDescription 卸下指定的外观
 * @apiVersion 0.1.0
 * @apiGroup appearance
 *
 * @apiParam {Number} appearance_id 需要卸下的外观物品 ID
 * @apiParam {number=1,2,3,4,5,6,7,8,10} type 需要卸下的外观物品类型，1 为座驾，2 为头像框，3 为名片框，4 为气泡框，5 为称号，6 为直播间挂件，7 为进场通知，8 为红包封面，10 为送礼通知
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "已取消使用该外观"
 *   }
 *
 * @apiSuccessExample {json} WebSocket 卸下直播间挂件消息
 *   {
 *     "type": "room",
 *     "event": "update",
 *     "room_id": 65261414,
 *     "room": {
 *       "room_id": 65261414,
 *       "catalog_id": 115,
 *       "custom_tag_id": 10001,
 *       "name": "直播间名字",
 *       "announcement": "公告",
 *       "creator_id": 65261514,
 *       "type": "connect",
 *       "notice": "通知",
 *       "background": {
 *         "enable": true,
 *         "image_url": "http://static.maoercdn.com/background/icon01.png",
 *         "opacity": 1.0,
 *       },
 *       "cover_url": "https://static.maoercdn.com/profile/01.png"
 *     }
 *   }
 */
func ActionTakeOffAppearance(c *handler.Context) (handler.ActionResponse, error) {
	var param wearActionParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = param.check(c)
	if err != nil {
		return nil, err
	}
	if param.userAppearance == nil {
		return nil, actionerrors.ErrParamsMsg("该外观不存在、未生效或已过期")
	}

	// 查询贵族外观
	uvMap, err := vip.UserVipInfos(param.userID, false, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	uv := vip.UserVipFromAppearanceID(param.AppearanceID, uvMap)
	isNoble := uv != nil && uv.Info != nil && uv.IsActive() &&
		param.AppearanceID == appearance.VipAppearanceID(uv.Type, uv.Level, param.Type)

	// 查询黑卡信息
	ub, err := liveuserblackcard.FindUserActiveBlackCard(param.userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	// 如果 AppearanceID 匹配，视为操作黑卡外观
	isBlackCard := ub != nil && param.AppearanceID == appearance.BlackCardAppearanceID(ub.Level, param.Type)

	param.updates = make([]mongo.WriteModel, 0, 1)
	if isNoble {
		param.updates = append(param.updates, mongo.NewDeleteManyModel().
			SetFilter(bson.M{
				"user_id":       param.userID,
				"appearance_id": param.AppearanceID,
				"from": bson.M{
					"$in": appearance.VipFromList(),
				},
			}))
	} else if isBlackCard {
		param.updates = append(param.updates, mongo.NewDeleteManyModel().
			SetFilter(bson.M{
				"user_id":       param.userID,
				"appearance_id": param.AppearanceID,
				"from":          appearance.FromBlackCard,
			}))
	} else {
		if param.userAppearance.Status == userappearance.StatusOwned {
			return nil, actionerrors.ErrParamsMsg("已经没有在使用该外观了")
		}

		param.updates = append(param.updates, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": param.userAppearance.OID}).SetUpdate(bson.M{
			"$set": bson.M{
				"status":        userappearance.StatusOwned,
				"modified_time": goutil.TimeNow().Unix(),
			},
		}))
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userappearance.Collection().BulkWrite(ctx, param.updates)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.notifyRoom(false)

	userappearance.ClearCache(param.userID)
	return "已取消使用该外观", nil
}

func (param *wearActionParam) check(c *handler.Context) error {
	// TypeVehicle 为当前外观中心第一种类型枚举值
	if param.Type >= appearance.TypeLimit ||
		param.Type < appearance.TypeVehicle {
		return actionerrors.ErrParams
	}

	param.userID = c.UserID()
	var err error
	param.userAppearance, err = userappearance.FindValidAppearance(param.AppearanceID, param.userID, param.Type)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *wearActionParam) writeData() error {
	if len(param.updates) != 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err := userappearance.Collection().BulkWrite(ctx, param.updates, options.BulkWrite().SetOrdered(true))
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		userappearance.ClearCache(param.userID)
	}

	return nil
}

type infoResp struct {
	AppearanceID int64  `json:"appearance_id"`
	Type         int    `json:"type"`
	Name         string `json:"name"`
	Intro        string `json:"intro"`
	IconURL      string `json:"icon_url"`
	IntroOpenURL string `json:"intro_open_url,omitempty"` // 跳转链接
}

// ActionInfo 获取外观详细信息
/**
 * @api {get} /api/v2/user/appearance/info 获取外观详细信息
 * @apiDescription 获取外观详细信息，不需要传外观类型参数，目前只有类型 5（称号）和 9（身份铭牌）需要额外获取信息
 * @apiVersion 0.1.0
 * @apiGroup appearance
 *
 * @apiParam {Number} appearance_id 需要获取信息的外观物品 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "appearance_id": 1,
 *       "type": 5,
 *       "name": "称号",
 *       "intro": "活动奖励获得",
 *       "icon_url": "https://example.com/title.png",
 *       "intro_open_url": "https://www.missevan.com/mevent/9000?from_room_id=__ROOM_ID__" // 跳转链接，支持模版变量，当下发时需要支持跳转
 *     }
 *   }
 *
 */
func ActionInfo(c *handler.Context) (handler.ActionResponse, error) {
	appearanceID, err := c.GetParamInt64("appearance_id")
	if err != nil || appearanceID <= appearance.AppearanceIDReserved {
		return nil, actionerrors.ErrParams
	}

	foundAppearance, err := appearance.FindOne(appearanceID, appearance.TypeNoSpecified)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if foundAppearance == nil {
		return nil, actionerrors.ErrNotFound("该外观不存在")
	}

	resp := &infoResp{
		AppearanceID: appearanceID,
		Type:         foundAppearance.Type,
		Name:         foundAppearance.Name,
		Intro:        foundAppearance.Intro,
		IconURL:      storage.ParseSchemeURL(foundAppearance.Icon),
	}
	if foundAppearance.Type == appearance.TypeIdentityBadge && foundAppearance.From == appearance.FromBlackCard {
		resp.IntroOpenURL = params.BlackCardURL(true)
	}
	return resp, nil
}

type customizeWelcomeParam struct {
	WelcomeMessageText string `form:"welcome_message_text" json:"welcome_message_text"`

	c *handler.Context
}

// ActionCustomizeWelcome 自定义进场欢迎语
/**
 * @api {post} /api/v2/user/appearance/welcome/customize 自定义进场欢迎语
 * @apiDescription 自定义进场欢迎语
 * @apiVersion 0.1.0
 * @apiGroup appearance
 *
 * @apiParam {String} welcome_message_text 需要修改成的新欢迎语，可传入空字符串，传入空时视为还原成默认欢迎语
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "custom_welcome_message": {
 *         "text": "欢迎光临本直播间",
 *         "expire_time": 1618714747 // 单位：秒
 *       },
 *     }
 *   }
 *
 */
func ActionCustomizeWelcome(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newCustomizeWelcomeParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	welcomeMsg, err := usermeta.UpdateWelcomeMessageText(c.UserID(), param.WelcomeMessageText)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if welcomeMsg == nil {
		return nil, "", actionerrors.NewErrForbidden("未拥有自定义进场欢迎语资格")
	}
	return bson.M{
		"custom_welcome_message": welcomeMsg,
	}, "success", nil
}

func newCustomizeWelcomeParam(c *handler.Context) (*customizeWelcomeParam, error) {
	var param customizeWelcomeParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg(err.Error())
	}
	param.WelcomeMessageText = strings.TrimSpace(param.WelcomeMessageText)
	param.c = c
	return &param, nil
}

func (p *customizeWelcomeParam) check() error {
	// 检测欢迎语长度
	if len(p.WelcomeMessageText) == 0 {
		return nil
	}
	if util.UTF8Width(p.WelcomeMessageText) > 20 {
		return actionerrors.NewErrForbidden("欢迎语最多 10 个字")
	}
	if goutil.ContainsEmoji(p.WelcomeMessageText) {
		return actionerrors.NewErrForbidden("暂不支持 emoji 输入")
	}
	// 违禁词检测
	checkRes, err := goclient.CheckText(p.c, p.WelcomeMessageText, scan.SceneUserInfo)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !checkRes.Pass {
		return actionerrors.ErrParamsMsg("含有违规内容，请重新输入")
	}
	return nil
}
