package appearance

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type appearanceAddParam struct {
	Type         int   `json:"type"`
	UserID       int64 `json:"user_id"`
	Duration     int64 `json:"duration"`
	AppearanceID int64 `json:"appearance_id"`
}

func (p *appearanceAddParam) checkAddParam() error {
	// TypeVehicle 为当前外观中心第一种类型枚举值
	if p.Type >= appearance.TypeLimit ||
		p.Type < appearance.TypeVehicle {
		return actionerrors.ErrParams
	}
	if p.UserID <= 0 || p.Duration <= 0 || p.AppearanceID <= appearance.AppearanceIDReserved {
		return actionerrors.ErrParams
	}
	return nil
}

// ActionAppearanceAdd 发放外观物品
/**
 * @api {post} /rpc/user/appearance/add 发放外观物品
 * @apiVersion 0.1.0
 * @apiGroup imrpc
 *
 * @apiParam {number=1,2,3,4,5,6} type 需要筛选的外观物品类型，1 为座驾，2 为头像框，3 为名片框，4 为气泡框，5 为称号，6 为直播间挂件
 * @apiParam {Number} user_id 发放奖励的目标用户 ID
 * @apiParam {Number} duration 外观的持续时间，单位：秒，如：生效 1 小时则为 3600
 * @apiParam {Number} appearance_id 发放的外观 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionAppearanceAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param appearanceAddParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	err = param.checkAddParam()
	if err != nil {
		return nil, err
	}

	aItem, err := appearance.FindOne(param.AppearanceID, param.Type)
	if err != nil {
		return nil, err
	}
	if aItem == nil || appearance.IsVipOrBlackCard(aItem.From) {
		return nil, actionerrors.ErrParamsMsg("该外观当前不可用")
	}

	err = userappearance.AddAppearance(param.UserID, param.Duration, 0, aItem)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"user_id": param.UserID})
	}
	return "success", nil
}
