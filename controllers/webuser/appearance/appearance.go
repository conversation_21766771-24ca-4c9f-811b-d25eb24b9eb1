package appearance

import (
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "appearance",
		Actions: map[string]*handler.Action{
			"list":    handler.NewAction(handler.GET, ActionListAppearance, true),
			"wear":    handler.NewAction(handler.POST, ActionWearAppearance, true),
			"takeoff": handler.NewAction(handler.POST, ActionTakeOffAppearance, true),
			"info":    handler.NewAction(handler.GET, ActionInfo, false),
			"tabs":    handler.NewAction(handler.GET, ActionTabs, false),
		},
	}
}

// HandlerV2 返回 handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "appearance",
		Actions: map[string]*handler.ActionV2{
			"welcome/customize": handler.NewActionV2(handler.POST, ActionCustomizeWelcome, handler.ActionOption{LoginRequired: true}),
		},
	}
}
