package webuser

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestViewlogsTagKeys(t *testing.T) {
	assert.Empty(t, tutil.KeyExists(tutil.JSON, viewlogsResp{}, "Datas", "pagination"))
}

func TestActionViewlogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := handler.CreateTestContext(true)
	ctx.User().ID = -20
	ctx.C.Request, _ = http.NewRequest("GET", "/user/viewlogs", nil)
	r, err := ActionViewlogs(ctx)
	require.NoError(err)
	resp := r.(*viewlogsResp)
	assert.Equal(int64(1), resp.Pagination.Count)
	assert.Zero(resp.Data[0].Statistics.Accumulation)
}
