package webuser

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/viewlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

func TestActionDeleteLogs(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(1919810)
	param := deleteLogsParam{
		RoomIDs: "1919810",
		Type:    deleteLogsTypeRoom,
	}
	ctx := handler.NewTestContext(http.MethodPost, "/user/deletelogs", true, param)

	// 去除旧数据
	mongoCtx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := viewlog.Collection().DeleteOne(mongoCtx, bson.M{"user_id": ctx.UserID(), "room_id": roomID})
	require.NoError(err)
	// 插入数据
	_, err = viewlog.Collection().InsertOne(mongoCtx, viewlog.ViewLog{
		OID: primitive.NewObjectID(),
		Helper: viewlog.Helper{
			UserID: ctx.UserID(),
			RoomID: roomID,
		},
	})
	require.NoError(err)

	cleanup := mrpc.SetMock(userapi.URLGOHistoryDelLivePlayHistory, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()

	r, err := ActionDeleteLogs(ctx)
	require.NoError(err)
	assert.Equal("删除成功", r)
}

func TestNewDeleteLogsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := deleteLogsParam{
		RoomIDs: "",
		Type:    -1,
	}
	ctx := handler.NewTestContext(http.MethodPost, "/user/deletelogs", true, param)
	_, err := newDeleteLogsParam(ctx)
	assert.Equal(actionerrors.ErrParams, err)

	param = deleteLogsParam{
		RoomIDs: "",
		Type:    deleteLogsTypeRoom,
	}
	ctx = handler.NewTestContext(http.MethodPost, "/user/deletelogs", true, param)
	_, err = newDeleteLogsParam(ctx)
	assert.Equal(actionerrors.ErrParams, err)

	param = deleteLogsParam{
		RoomIDs: "1919810",
		Type:    deleteLogsTypeRoom,
	}
	ctx = handler.NewTestContext(http.MethodPost, "/user/deletelogs", true, param)
	p, err := newDeleteLogsParam(ctx)
	require.NoError(err)
	assert.Equal(param.Type, p.Type)
	assert.Equal(param.RoomIDs[0], p.RoomIDs[0])
	assert.Equal(ctx.UserID(), p.userID)
	assert.Equal(ctx.ClientIP(), p.userCtx.ClientIP)
}

func TestDeleteLogsParam_delete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(114514)
	roomIDs := []int64{1919810, 1919816, 1919819}
	param := deleteLogsParam{
		roomIDs: []int64{roomIDs[0]},
		Type:    deleteLogsTypeRoom,
		userID:  userID,
		userCtx: userapi.UserContext{ClientIP: ""},
	}

	// 去除旧数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := viewlog.Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	require.NoError(err)
	// 插入数据
	viewLogs := make([]interface{}, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		vl := viewlog.ViewLog{
			OID: primitive.NewObjectID(),
			Helper: viewlog.Helper{
				UserID: userID,
				RoomID: roomID,
			},
		}
		viewLogs = append(viewLogs, vl)
	}
	_, err = viewlog.Collection().InsertMany(ctx, viewLogs)
	require.NoError(err)

	// 删除一个
	cleanup := mrpc.SetMock(userapi.URLGOHistoryDelLivePlayHistory, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()
	require.NoError(param.delete())
	tryFind := func(userID int64, roomID int64) error {
		return viewlog.Collection().FindOne(ctx, bson.M{"user_id": userID, "room_id": roomID}).Err()
	}
	assert.True(mongodb.IsNoDocumentsError(tryFind(userID, roomIDs[0])))
	assert.NoError(tryFind(userID, roomIDs[1]))
	assert.NoError(tryFind(userID, roomIDs[2]))

	// 全部删除
	param.Type = deleteLogsTypeAll
	cleanup = mrpc.SetMock(userapi.URLGOHistoryClearLivePlayHistory, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()
	require.NoError(param.delete())
	assert.True(mongodb.IsNoDocumentsError(tryFind(userID, roomIDs[1])))
	assert.True(mongodb.IsNoDocumentsError(tryFind(userID, roomIDs[2])))
}
