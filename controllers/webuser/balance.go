package webuser

import (
	"encoding/json"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const urlBalanceDetails = "app://live/balance-details"

// ActionBalanceDetails 余额详情
/**
 * @api {get} /api/v2/user/balance-details 用户余额详情
 * @apiDescription 用户余额详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionBalanceDetails(c *handler.Context) (handler.ActionResponse, error) {
	var resp json.RawMessage
	err := service.MRPC.Call(urlBalanceDetails, c.ClientIP(),
		map[string]int64{"user_id": c.UserID()},
		&resp, map[string]string{"token": c.<PERSON>(), "equip_id": c.EquipID()})
	return resp, err
}
