package webuser

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// block status type
const (
	blockStatusIBlocked  = iota + 1 // 拉黑当前用户
	blockStatusBeBlocked            // 被当前用户拉黑
)

type userInfoCardParam struct {
	roomID        int64
	paramUserID   int64
	requestUserID int64
}

func newUserInfoCardParam(c *handler.Context) (*userInfoCardParam, error) {
	roomID, err := c.GetDefaultParamInt64("room_id", 0)
	if err != nil || roomID < 0 {
		return nil, actionerrors.ErrParams
	}
	userID, err := c.GetParamInt64("user_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if userID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return &userInfoCardParam{
		roomID:        roomID,
		paramUserID:   userID,
		requestUserID: c.UserID(),
	}, nil
}

func checkFollowed(userID, requestUserID int64) *bool {
	// 当前用户未登录或查询用户为当前登录用户时，不查询关注情况
	if requestUserID == 0 || userID == requestUserID {
		return nil
	}

	followed, err := attentionuser.HasFollowed(requestUserID, userID)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return goutil.NewBool(followed)
}

func updateCardFrameURL(equip *goutil.Equipment, cardFrameURL string) string {
	if cardFrameURL == "" {
		return storage.ParseSchemeURL(config.Conf.Params.UserInfo.DefaultCardFrame.ImageURL)
	}
	replaceAVIF := func(url string) string {
		if strings.HasSuffix(url, ".avif") {
			return url[:len(url)-5] + ".webp"
		}
		return url
	}
	// WORKAROUND: 兼容 web 不下发 avif url
	if !equip.FromApp {
		return replaceAVIF(cardFrameURL)
	}
	// WORKAROUND: 兼容版本 iOS < 4.9.3 和 Android < 5.8.0 的版本，不下发 avif url
	if equip.IsOldApp(goutil.AppVersions{IOS: "4.9.3", Android: "5.8.0"}) {
		return replaceAVIF(cardFrameURL)
	}
	// WORKAROUND: 兼容 iOS 14 以下的版本和 Android 8 以下的版本，不下发 avif url
	osVersion, err := strconv.Atoi(strings.Split(equip.OSVersion, ".")[0])
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if (equip.OS == goutil.IOS && osVersion < 14) || (equip.OS == goutil.Android && osVersion < 8) {
		return replaceAVIF(cardFrameURL)
	}
	return cardFrameURL
}

// 查询 mowangskuser 用户 intro 信息并同步到 MongoDB
func findUserIntro(userInfo *userItemResp) {
	user, err := mowangskuser.FindByUserID(userInfo.UserID())
	if err != nil {
		logger.Error(err)
		return
	}
	if user == nil {
		logger.WithField("user_id", userInfo.UserID()).Error("mowangskuser 表中未找到该直播间用户")
		return
	}
	if userInfo.Introduction != user.UserIntro {
		userInfo.Introduction = user.UserIntro
		_, err := liveuser.Update(userInfo.UserID(), bson.M{"introduction": user.UserIntro})
		if err != nil {
			logger.WithField("user_id", userInfo.UserID()).
				Errorf("failed to update user introduction to users collection: %v", err)
			return
		}
	}
}

// userItemResp 用户信息
type userItemResp struct {
	*liveuser.UserInfo `json:",inline"`

	AuthInfo *userapi.UserAuthInfo `json:"auth_info,omitempty"`
	// 位运算，第 1 位：拉黑当前用户，第 2 位：被当前用户拉黑
	BlockStatus goutil.BitMask `json:"block_status"`
}

type userCardResp struct {
	User *userItemResp `json:"user"`
	// 同 CardFrame.ImageURL，用于兼容旧版本（iOS、Android < 6.0.2），iOS、Android 新版本不再下发
	CardFrameURL string     `json:"card_frame_url,omitempty"`
	CardFrame    *cardFrame `json:"card_frame"`
	// Followed 是否关注, 未登录用户或用户自己不返回该字段
	Followed *bool `json:"followed,omitempty"`
	// Room user_id 是传入的 room_id 的主播的时候有此字段
	Room *userCardRespRoom `json:"room,omitempty"`
}

// cardFrame 用户定制名片框
type cardFrame struct {
	ImageURL      string                    `json:"image_url"`
	TextColorItem *appearance.TextColorItem `json:"text_color_item"`
}

type userCardRespRoom struct {
	RoomID    int64                     `json:"room_id"`
	CreatorID int64                     `json:"creator_id"`
	Medal     *userCardRespRoomMedal    `json:"medal,omitempty"`
	GiftWall  *giftwall.ActivatedDetail `json:"gift_wall,omitempty"`
}

type userCardRespRoomMedal struct {
	Name            string `json:"name"`
	FansNum         int64  `json:"fans_num"`
	SuperFansNum    int64  `json:"super_fans_num"`
	SuperFanIconURL string `json:"super_fan_icon_url"`
}

func (r *userCardResp) setUserCardFrame(c *handler.Context, userID int64) {
	userCardFrame, err := userappearance.FindCardFrame(userID)
	if err != nil {
		logger.WithField("user_id", userID).Error(err)
		// PASS
	}
	if userCardFrame == nil {
		// 没有个人名片框或已失效，则返回默认图片和默认字体颜色
		r.CardFrame = &cardFrame{
			ImageURL: updateCardFrameURL(c.Equip(), storage.ParseSchemeURL(config.Conf.Params.UserInfo.DefaultCardFrame.ImageURL)),
			TextColorItem: &appearance.TextColorItem{
				Username:        config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Username,
				Introduction:    config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Introduction,
				ReportAndManage: config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.ReportAndManage,
			},
		}
	} else {
		r.CardFrame = &cardFrame{
			ImageURL:      updateCardFrameURL(c.Equip(), userCardFrame.ImageNew),
			TextColorItem: userCardFrame.TextColorItem,
		}
	}
	// WORKAROUND: 兼容 iOS、Android < 6.0.2 以及 web 端下发 card_frame_url
	// TODO: web 端上线后不再下发 card_frame_url
	if c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.0.2", Android: "6.0.2"}) || !c.Equip().FromApp {
		r.CardFrameURL = r.CardFrame.ImageURL
	}
}

func (r *userCardResp) findMedals() error {
	ok, medalName := room.HaveMedal(r.Room.RoomID)
	if !ok {
		return nil
	}

	result, err := livemedal.FindRoomMedalFansAndSuperFansCount(r.Room.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if result == nil {
		return nil
	}

	r.Room.Medal = &userCardRespRoomMedal{
		Name:         medalName,
		FansNum:      result.FansNum,
		SuperFansNum: result.SuperFansNum,
	}
	err = r.findSuperFans()
	if err != nil {
		return err
	}

	return nil
}

func (r *userCardResp) findGiftWall() error {
	var err error
	r.Room.GiftWall, err = giftwall.FindActivatedDetail(r.Room.RoomID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (r *userCardResp) findSuperFans() error {
	if r.Room.Medal == nil {
		return nil
	}

	// 使用缓存的超粉列表数据获取超粉图标
	goods, err := livegoods.ListShowingSuperFan()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(goods) == 0 {
		logger.Error("failed to find super fan from livegoods")
		return nil
	}

	// 默认取第一位
	r.Room.Medal.SuperFanIconURL = goods[0].GoodsIconURL()
	return nil
}

func (r *userCardResp) findRoomInfo(param *userInfoCardParam) {
	if param.roomID == 0 {
		return
	}

	// 查询主播 ID
	// TODO: 查询主播 ID 可以和下面的勋章查询合并到一个查询
	creatorID, err := room.FindCreatorID(param.roomID)
	if err != nil {
		logger.WithField("room_id", param.roomID).Errorf("failed to find creator_id: %v", err)
		return
	}
	// 如果不是主播，则返回
	if creatorID != param.paramUserID {
		return
	}

	r.Room = &userCardRespRoom{
		RoomID:    param.roomID,
		CreatorID: creatorID,
	}
	err = r.findMedals()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	err = r.findGiftWall()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (r *userCardResp) findUserBlockStatus(param *userInfoCardParam) {
	if param.requestUserID == 0 || param.paramUserID == param.requestUserID {
		return
	}
	u1BlockU2, u2BlockU1, err := userapi.UserBlockStatus(param.requestUserID, param.paramUserID)
	if err != nil {
		logger.Error(err)
		return
	}
	if u1BlockU2 {
		r.User.BlockStatus.Set(blockStatusIBlocked)
	}
	if u2BlockU1 {
		r.User.BlockStatus.Set(blockStatusBeBlocked)
	}
}

// ActionUserCard 用户名片
/**
 * @api {get} /api/v2/user/card 用户信息（新版名片）
 * @apiVersion 0.1.0
 * @apiName card
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} [room_id] 直播间 ID，根据用户在直播间的角色（主播，超粉等）有不同的响应
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "user": {
 *           "user_id": 12,
 *           "username": "零月",
 *           "iconurl": "http://static.missevan.com/avatar/icon01.png",
 *           "introduction": "喵喵喵", // 个性签名
 *           "block_status": 1, // 位运算，第 1 位：拉黑当前用户，第 2 位：被当前用户拉黑
 *           "titles": [
 *             {
 *                "type": "staff",
 *                "name": "超管",
 *                "color": "#F45B41"
 *             },
 *             {
 *                "type": "username",
 *                "color": "#000000", // 兼容使用，后续不再下发
 *                "colors": "#000000;#FFFFFF" // 下发两个颜色时支持渐变色
 *             },
 *             {
 *                "type": "medal",
 *                "name": "1",
 *                "frame_url": "http://test.png",  // 用户粉丝徽章
 *                "level": 1,
 *                "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *                  "expire_time": 1576116700 // 秒级时间戳
 *                },
 *             }
 *           ],
 *           "auth_info": { // 主播或用户拥有认证时才会下发
 *             "type": 2, // 认证类型，2：金 V；3：蓝 V
 *             "title": "UP 主认证", // 认证类型描述
 *             "subtitle": "猫耳FM 2023直播盛典年度冠军主播、人气音乐人" // 认证头衔，无头衔时不下发
 *           }
 *         },
 *         "card_frame_url": "http://test.png", // 同 card_frame.image_url 用于兼容旧版本（iOS、Android < 6.0.2），iOS、Android 新版本不再下发
 *         "card_frame": { // 名片框配置
 *           "image_url": "http://test.png", // 用户定制名片框框体，用户名片框未配置下发默认框体，若未下发客户端使用默认框体
 *           "text_color_item": { // 名片框字体颜色，未配置下发默认颜色，若未下发客户端使用默认颜色
 *             "username": "#FFFFFF", // 用户昵称字体颜色，优先使用 user.titles 里，type 为 username 的颜色
 *             "introduction": "#FFFFFF", // 个性签名字体颜色
 *             "report_and_manage": "#FFFFFF" // 举报和管理按钮字体颜色
 *           }
 *         },
 *         "followed": true, // 是否关注, 未登录用户或用户自己不返回该字段
 *         "room": { // user_id 是传入的 room_id 的主播的时候有此字段
 *           "room_id": 124,
 *           "creator_id": 12,
 *           "medal": { // 没有该字段说明该房间没有粉丝勋章
 *             "name": "123",
 *             "fans_num": 10, // 粉丝数量
 *             "super_fans_num": 10, // 超粉数量
 *             "super_fan_icon_url": "http://test.png" // 超粉图标
 *           },
 *           "gift_wall": { // 只有主播名片框会返回
 *             "activated_num": 1, // 已点亮数量
 *             "total_num": 20 // 本期上墙礼物总数
 *           }
 *         }
 *       }
 *     }
 */
func ActionUserCard(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newUserInfoCardParam(c)
	if err != nil {
		return nil, err
	}

	foundUserInfo, err := liveuser.FindUserInfo(param.paramUserID, param.roomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if foundUserInfo == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	authInfo, err := userapi.GetUserAuthInfo(c.UserContext(), param.paramUserID)
	if err != nil {
		return nil, err
	}
	resp := userCardResp{
		User: &userItemResp{
			UserInfo: foundUserInfo,
			AuthInfo: authInfo,
		},
		Followed: checkFollowed(param.paramUserID, param.requestUserID),
	}
	resp.setUserCardFrame(c, param.paramUserID)
	findUserIntro(resp.User)
	resp.findRoomInfo(param)
	resp.findUserBlockStatus(param)
	return &resp, nil
}

type userVipInfo struct {
	Level      int    `json:"level"`
	Name       string `json:"name"`
	ExpireTime int64  `json:"expire_time,omitempty"`
	Status     int    `json:"status"`
}

type userMyNobleResp struct {
	User       *liveuser.UserInfo `json:"user,omitempty"`
	Noble      *userVipInfo       `json:"noble,omitempty"`
	Highness   *userVipInfo       `json:"highness,omitempty"`
	TrialNoble *userVipInfo       `json:"trial_noble,omitempty"`
	VipTip     string             `json:"vip_tip,omitempty"`
}

// ActionUserMyNoble 获取当前请求用户贵族信息
/**
 * @api {get} /api/v2/user/mynoble 获取当前请求用户贵族信息
 * @apiVersion 0.1.0
 * @apiName myinfo
 * @apiGroup /api/v2/user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "user": { // 未登录无该字段
 *           "user_id": 12,
 *           "username": "零月",
 *           "iconurl": "http://static.missevan.com/avatar/icon01.png",
 *           "confirm": 2,
 *           "introduction": "喵喵喵", // 个性签名
 *           "titles": [] // 同其他 titles
 *         },
 *         "noble": { // 没有贵族无该字段
 *           "level": 1,
 *           "name": "练习生",
 *           "expire_time": 1234567890, // 到期时间
 *           "status": 1 // 贵族身份状态：1 有贵族身份，2 贵族身份处于续费保护期
 *         },
 *         "highness": { // 没有上神无该字段
 *           "level": 1,
 *           "name": "上神",
 *           "expire_time": 1234567890, // 到期时间
 *           "status": 2 // 上神身份状态：1 有上神身份，2 上神身份处于续费保护期
 *         },
 *         "trial_noble": { // 没有体验贵族返回 null（这里不会影响普通贵族，普通贵族按照原贵族身份下发）
 *           "name": "大咖",
 *           "level": 1,
 *           "expire_time": 1234567890, // 到期时间，单位：秒
 *           "status": 1 // 体验贵族身份状态：1 有体验贵族身份
 *         },
 *         "vip_tip": "剩余 3 天" // 显示在直播中心“我的贵族”状态提示，无贵族和上神时，无该字段
 *       }
 *     }
 */
func ActionUserMyNoble(c *handler.Context) (handler.ActionResponse, error) {
	var resp userMyNobleResp
	if c.UserID() == 0 {
		return resp, nil
	}
	var err error
	resp.User, err = liveuser.FindUserInfo(c.UserID(), 0)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.User == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	uvMap, err := vip.UserVipInfos(c.UserID(), false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// NOTICE: tip 会根据贵族处理顺序被覆盖，优先展示上神的提示
	resp.Noble = newUserVipInfo(uvMap[vip.TypeLiveNoble], &resp.VipTip)
	resp.Highness = newUserVipInfo(uvMap[vip.TypeLiveHighness], &resp.VipTip)
	resp.TrialNoble = newUserVipInfo(uvMap[vip.TypeLiveTrialNoble], &resp.VipTip)
	return resp, nil
}

// TODO: 该方法使用字符串判断续费保护，后续需要重构该方法
func newUserVipInfo(uv *vip.UserVip, tip *string) *userVipInfo {
	if tip == nil {
		panic("tip is nil")
	}
	if uv == nil {
		return nil
	}
	v := vip.NewUserNoble(uv)
	if v.Status == vip.NobleStatusNone {
		return nil
	}
	if v.RemainDays > 0 {
		switch v.Status {
		case vip.NobleStatusNoble:
			switch {
			case uv.Type == vip.TypeLiveTrialNoble:
				if goutil.TimeNow().AddDate(0, 0, 3).After(time.Unix(uv.ExpireTime, 0)) && *tip == "" {
					*tip = fmt.Sprintf("%s体验剩余 %d 天", v.Name, v.RemainDays)
				}
			case !strings.Contains(*tip, "续费保护"):
				// WORKAROUND: 上神有效期 <= 3（RemainDays > 0）且普通贵族在续费保护期时，优先展示普通贵族续费保护期的提示
				// 规则文档: https://info.missevan.com/pages/viewpage.action?pageId=50727430
				*tip = fmt.Sprintf("%s有效期剩余 %d 天", v.Name, v.RemainDays)
			}
		case vip.NobleStatusProtected:
			*tip = fmt.Sprintf("%s续费保护还剩 %d 天", v.Name, v.RemainDays)
		}
	}
	return &userVipInfo{
		Level:      v.Level,
		Name:       v.Name,
		ExpireTime: v.ExpireTime,
		Status:     v.Status,
	}
}
