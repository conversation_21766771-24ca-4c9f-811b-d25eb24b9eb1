package webuser

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionVipBalanceDetails(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	respStr := "test"
	var resp json.RawMessage
	b, _ := json.Marshal(respStr)
	require.NoError(json.Unmarshal(b, &resp))
	assert.JSONEq(`"test"`, string(resp))
	respArray := []int{1, 2}
	b, _ = json.Marshal(respArray)
	require.NoError(json.Unmarshal(b, &resp))
	assert.JSONEq(`[1,2]`, string(resp))
	respJSON := map[string]interface{}{"test": true, "null": nil}
	b, _ = json.Marshal(respJSON)
	require.NoError(json.Unmarshal(b, &resp))
	assert.JSONEq(`{"null":null,"test":true}`, string(resp))

	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("GET", "/vip/balance-details", nil)
	_, err := ActionBalanceDetails(c)
	if _, ok := err.(*mrpc.ClientError); !ok {
		assert.NoError(err) // 不 ok 才需要认为这个是 nil
	}
}
