package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type initiateListParam struct {
	marker   *luckybag.MarkerListOption
	pageSize int64
	userID   int64
}

type initiateRecord struct {
	LuckyBagID  int64  `json:"lucky_bag_id"`
	Type        int    `json:"type"`
	RewardType  int    `json:"reward_type"`
	PrizeName   string `json:"prize_name"`
	PrizeNum    int    `json:"prize_num"`
	StartTime   int64  `json:"start_time"`
	EndTime     int64  `json:"end_time"`
	JoinNum     int64  `json:"join_num"`
	TargetType  int    `json:"target_type"`
	IncreaseNum int64  `json:"increase_num"`
}

// initiateListResp 福袋发起记录列表
type initiateListResp struct {
	Data       []*initiateRecord       `json:"data"`
	Pagination goutil.MarkerPagination `json:"pagination"`
}

// ActionInitiateList 获取福袋发起记录
/**
 * @api {get} /api/v2/user/luckybag/initiate/list 获取福袋发起记录
 * @apiDescription 主播查看自己发起的福袋历史记录
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {String} [marker] 游标，第一次调用不需要传，后续请求返回前一次响应的请求中的 marker
 * @apiParam {Number} [page_size=20] 一页显示数目
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "data": [
 *         {
 *           "lucky_bag_id": 1,
 *           "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *           "reward_type": 1, // 奖励类型; 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *           "prize_name": "魔道祖师第一季", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示剧集封面图时，需要客户端处理添加书名号
 *           "prize_num": 3, // 奖品数量
 *           "start_time": 1618196305, // 开始时间时间戳，单位：秒
 *           "end_time": 1618714747, // 结束时间时间戳，单位：秒
 *           "join_num": 19999, // 参与人数
 *           "target_type": 3, // 参与对象类型: 0: 所有人; 1: 关注; 2: 粉丝勋章; 3:超粉
 *           "increase_num": 15 // 对应参与对象增长数量，对象为所有人时不返回该字段
 *         }
 *       ],
 *       "pagination": {
 *         "has_more": true, // 是否有更多数据
 *         "marker": "1234567890,1,0" // 当前的 marker，需要加载下一页时回传
 *       }
 *     }
 *   }
 *
 */
func ActionInitiateList(c *handler.Context) (handler.ActionResponse, string, error) {
	var param initiateListParam
	if err := param.load(c); err != nil {
		return nil, "", err
	}
	return param.resp()
}

// load 参数处理
func (param *initiateListParam) load(c *handler.Context) (err error) {
	marker, pageSize, err := c.GetParamMarker()
	if err != nil {
		return actionerrors.ErrParams
	}
	param.marker, err = luckybag.ParseMarker(marker)
	if err != nil {
		logger.WithField("marker", marker).Warn(err)
		return actionerrors.ErrParams
	}
	param.pageSize = pageSize
	param.userID = c.UserID()
	return nil
}

// resp 获取福袋发起记录
func (param *initiateListParam) resp() (*initiateListResp, string, error) {
	// 获取主播福袋发起记录列表
	initiates, nextMarker, err := luckybag.ListFinishInitiateRecord(param.userID, param.pageSize, param.marker)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &initiateListResp{
		Data: make([]*initiateRecord, 0, len(initiates)),
	}
	if len(initiates) == 0 {
		return resp, "success", nil
	}

	for _, initiate := range initiates {
		record := &initiateRecord{
			LuckyBagID:  initiate.ID,
			Type:        initiate.Type,
			RewardType:  initiate.RewardType,
			PrizeName:   initiate.Name,
			PrizeNum:    initiate.Num,
			StartTime:   initiate.StartTime,
			EndTime:     initiate.EndTime,
			JoinNum:     initiate.JoinNum,
			TargetType:  initiate.TargetType,
			IncreaseNum: luckybag.CalculateIncreaseNum(initiate.MoreInfo),
		}
		resp.Data = append(resp.Data, record)
	}
	if nextMarker != nil {
		resp.Pagination.HasMore = true
		resp.Pagination.Marker = nextMarker.Encode()
	}
	return resp, "success", nil
}
