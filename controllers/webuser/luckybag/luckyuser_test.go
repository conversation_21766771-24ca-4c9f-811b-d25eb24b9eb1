package luckybag

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionLuckyUser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12)
	luckyBagID := int64(10)

	// 删除测试数据
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("id = ?", luckyBagID).Delete("").Error)
	require.NoError(luckybag.DB().Table(luckybag.UserPrize{}.TableName()).
		Where("lucky_bag_id = ?", luckyBagID).Delete("").Error)

	// 测试福袋不存在
	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/luckyuser/list?lucky_bag_id=10", true, nil)
	resp, message, err := ActionLuckyUser(c)
	require.Equal(actionerrors.ErrParamsMsg("福袋不存在"), err)
	assert.Equal("", message)
	assert.Nil(resp)

	// 创建福袋发起记录
	record := luckybag.InitiateRecord{
		ID:         luckyBagID,
		UserID:     10,
		CreatorID:  10,
		RoomID:     10,
		Type:       luckybag.TypeDrama,
		RewardType: luckybag.RewardTypeDrama,
		Name:       "剧集福袋",
	}
	require.NoError(record.Create())
	// 测试无权限查看中奖用户名单
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/luckyuser/list?lucky_bag_id=10", true, nil)
	resp, message, err = ActionLuckyUser(c)
	require.Equal(actionerrors.NewErrForbidden("仅支持福袋发起人查看"), err)
	assert.Equal("", message)
	assert.Nil(resp)
	// 删除测试数据
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).Where("id = ?", record.ID).Delete("").Error)

	// 创建福袋发起记录
	record = luckybag.InitiateRecord{
		ID:         luckyBagID,
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     10,
		Type:       luckybag.TypeDrama,
		RewardType: luckybag.RewardTypeDrama,
		Name:       "剧集福袋",
	}
	require.NoError(record.Create())
	// 测试中奖用户名单为空
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/luckyuser/list?lucky_bag_id=10", true, nil)
	resp, message, err = ActionLuckyUser(c)
	require.NoError(err)
	assert.Equal("success", message)
	r := resp.(*luckyUserResp)
	assert.Empty([]int64{}, r.Data)

	// 创建中奖记录
	record2 := luckybag.UserPrize{
		LuckyBagID: luckyBagID,
		RewardTime: 1717642800,
		UserID:     12,
	}
	require.NoError(record2.Create())

	record2 = luckybag.UserPrize{
		LuckyBagID: luckyBagID,
		RewardTime: 1717675200,
		UserID:     10,
	}
	require.NoError(record2.Create())
	// 测试查看中奖用户名单
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/luckyuser/list?lucky_bag_id=10", true, nil)
	resp, message, err = ActionLuckyUser(c)
	require.NoError(err)
	assert.Equal("success", message)
	r = resp.(*luckyUserResp)
	require.NotEmpty(r.Data)
	assert.Equal(int64(10), r.Data[0].UserID)
	assert.Equal(int64(12), r.Data[1].UserID)
	assert.Equal("bless", r.Data[0].Username)
	assert.Equal("零月", r.Data[1].Username)
	assert.Equal("https://static-test.missevan.com/profile/201704/07/9b3529a08130e74da1dcd8b53feb50c5155007.png", r.Data[0].IconURL)
	assert.Equal("https://static-test.missevan.com/profile/201703/20/c006f762e3b0eb0b6aa2ffa8e59635fb173833.png", r.Data[1].IconURL)
}
