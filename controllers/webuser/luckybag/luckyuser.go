package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type luckyUserInfo struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
}

type luckyUserResp struct {
	Data []luckyUserInfo `json:"data"`
}

// ActionLuckyUser 获取福袋中奖用户
/**
 * @api {get} /api/v2/user/luckybag/luckyuser/list 获取福袋中奖用户
 * @apiDescription 仅支持福袋发起人查看
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} lucky_bag_id 福袋 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "data": [ // 数量不多，暂不分页
 *         {
 *           "user_id": 123,
 *           "username": "name",
 *           "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         },
 *         {
 *           "user_id": 1234,
 *           "username": "name",
 *           "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         }
 *       ]
 *     }
 *   }
 *
 */
func ActionLuckyUser(c *handler.Context) (handler.ActionResponse, string, error) {
	luckyBagID, _ := c.GetParamInt64("lucky_bag_id")
	if luckyBagID <= 0 {
		return nil, "", actionerrors.ErrParams
	}

	luckyBag, err := luckybag.FindShowingInitiateRecordByID(luckyBagID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if luckyBag == nil {
		return nil, "", actionerrors.ErrParamsMsg("福袋不存在")
	}
	if luckyBag.UserID != c.UserID() {
		return nil, "", actionerrors.NewErrForbidden("仅支持福袋发起人查看")
	}

	luckyUserIDs, err := luckybag.ListLuckyUserIDs(luckyBagID)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &luckyUserResp{
		Data: make([]luckyUserInfo, 0, len(luckyUserIDs)),
	}
	if len(luckyUserIDs) == 0 {
		return resp, "success", nil
	}

	userMap, err := mowangskuser.FindSimpleMap(luckyUserIDs) // 函数内部已去重
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	for _, luckyUserID := range luckyUserIDs {
		u, ok := userMap[luckyUserID]
		if !ok {
			logger.WithField("user_id", luckyUserID).Error("中奖用户不存在")
			continue
		}
		resp.Data = append(resp.Data, luckyUserInfo{
			UserID:   luckyUserID,
			Username: u.Username,
			IconURL:  u.IconURL,
		})
	}
	return resp, "success", nil
}
