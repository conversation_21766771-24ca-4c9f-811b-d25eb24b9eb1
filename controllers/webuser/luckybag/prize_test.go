package luckybag

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestPrizeInfoTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(prizeInfoResp{}, "lucky_bag_id", "status", "type", "reward_type", "reward_time", "prize_id",
		"prize_name", "prize_icon_url", "prize_drama", "initiate_user", "redeem_time", "redeem_end_time", "redeem_user")
	kc.Check(prizeDrama{}, "id", "cover_color")
	kc.Check(prizeInfoUser{}, "user_id", "username", "iconurl", "room_id")

	kc.CheckOmitEmpty(prizeInfoResp{}, "type", "reward_type", "reward_time", "prize_id", "prize_name",
		"prize_icon_url", "prize_drama", "initiate_user", "redeem_time", "redeem_end_time", "redeem_user")
	kc.CheckOmitEmpty(prizeInfoUser{}, "room_id")
}

func TestRedeemPrizeTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(redeemPrizeParams{}, "prize_id", "user_id", "confirm")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(redeemPrizeParams{}, "prize_id", "user_id", "confirm")
}

func TestActionPrizeList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(luckybag.DB().Table(luckybag.UserPrize{}.TableName()).
		Where("user_id = ?", 12).Delete("").Error)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/prize/list", true, nil)
	resp, message, err := ActionPrizeList(c)
	require.NoError(err)
	r := resp.(*prizeListResp)
	require.Empty(r.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: r.Pagination.Marker}, r.Pagination)
	assert.Equal("success", message)
}

func TestPrizeListParam_load(t *testing.T) {
	assert := assert.New(t)

	param := new(prizeListParam)
	err := param.load(handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/prize/list?marker=1717482605,99,0", true, nil))
	assert.NoError(err)
	assert.Equal(int64(12), param.userID)
	assert.Equal(&luckybag.MarkerListOption{CreateTime: 1717482605, ID: 99, HasSameTimeLast: false}, param.marker)
}

func TestPrizeListParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 删除测试数据
	require.NoError(luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()).
		Where("id IN (?)", []int64{10, 11, 12}).Delete("").Error)
	require.NoError(luckybag.DB().Table(luckybag.UserPrize{}.TableName()).
		Where("lucky_bag_id IN (?)", []int64{10, 11, 12}).Delete("").Error)

	userID := int64(12)
	creatorID := int64(12345)

	// 测试无中奖记录
	param := &prizeListParam{
		userID: userID,
	}
	resp, message, err := param.resp()
	require.NoError(err)
	require.Empty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: resp.Pagination.Marker}, resp.Pagination)
	assert.Equal("success", message)

	// 创建福袋发起记录
	irecord1 := luckybag.InitiateRecord{
		ID:         10,
		UserID:     creatorID,
		CreatorID:  creatorID,
		RoomID:     10,
		Type:       luckybag.TypeDrama,
		RewardType: luckybag.RewardTypeDrama,
		Name:       "剧集福袋",
	}
	require.NoError(irecord1.Create())

	irecord2 := luckybag.InitiateRecord{
		ID:         11,
		UserID:     creatorID,
		CreatorID:  creatorID,
		RoomID:     10,
		Type:       luckybag.TypeEntity,
		RewardType: luckybag.RewardTypeEntityPersonal,
		Name:       "实物福袋",
	}
	require.NoError(irecord2.Create())

	irecord3 := luckybag.InitiateRecord{
		ID:         12,
		UserID:     creatorID,
		CreatorID:  creatorID,
		RoomID:     10,
		Type:       luckybag.TypeEntity,
		RewardType: luckybag.RewardTypeEntityDrama,
		Name:       "剧集实物福袋",
	}
	require.NoError(irecord3.Create())

	// mock 时间，保证中奖时间
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 21, 0, 0, 0, time.Local)
	})
	defer cancel()
	// 创建用户福袋中奖记录
	record := luckybag.UserPrize{
		LuckyBagID: irecord1.ID, // 对应福袋发起记录的 ID
		RewardTime: 1717642800,
		UserID:     userID,
	}
	require.NoError(record.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 20, 0, 0, 0, time.Local)
	})
	record2 := luckybag.UserPrize{
		LuckyBagID: irecord2.ID,
		RewardTime: 1717675200,
		UserID:     userID,
	}
	require.NoError(record2.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 10, 0, 0, 0, time.Local)
	})
	record3 := luckybag.UserPrize{
		LuckyBagID: irecord3.ID,
		RewardTime: 1717639200,
		UserID:     userID,
	}
	require.NoError(record3.Create())

	// 测试请求第一页
	param.pageSize = 2
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: true,
		Marker: fmt.Sprintf("%d,%d,%d", record2.CreateTime, record2.ID, 0)}, resp.Pagination)
	except := []*prizeRecord{
		{
			PrizeID:         record.ID,
			LuckyBagID:      record.LuckyBagID,
			Type:            luckybag.TypeDrama,
			RewardType:      luckybag.RewardTypeDrama,
			RewardTime:      record.RewardTime,
			PrizeName:       "剧集福袋",
			RoomID:          10,
			CreatorID:       creatorID,
			CreatorUsername: "测试首页推荐",
		},
		{
			PrizeID:         record2.ID,
			LuckyBagID:      record2.LuckyBagID,
			Type:            luckybag.TypeEntity,
			RewardType:      luckybag.RewardTypeEntityPersonal,
			RewardTime:      record2.RewardTime,
			PrizeName:       "实物福袋",
			RoomID:          10,
			CreatorID:       creatorID,
			CreatorUsername: "测试首页推荐",
		},
	}
	assert.Equal(except, resp.Data)

	// 请求第二页
	param.pageSize = 2
	param.marker = &luckybag.MarkerListOption{CreateTime: record2.CreateTime, ID: record2.ID, HasSameTimeLast: false}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: ""}, resp.Pagination)
	except = []*prizeRecord{
		{
			PrizeID:         record3.ID,
			LuckyBagID:      record3.LuckyBagID,
			Type:            luckybag.TypeEntity,
			RewardType:      luckybag.RewardTypeEntityDrama,
			RewardTime:      record3.RewardTime,
			PrizeName:       "剧集实物福袋",
			RoomID:          10,
			CreatorID:       creatorID,
			CreatorUsername: "测试首页推荐",
		},
	}
	assert.Equal(except, resp.Data)

	// 测试无分页
	param = &prizeListParam{
		userID:   userID,
		pageSize: 3, // 一共三条中奖记录
	}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: ""}, resp.Pagination)
}

func TestActionPrizeInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/api/v2/user/luckybag/prize/info"
	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	c.User().ID = 3457182
	resp, message, err := ActionPrizeInfo(c)
	require.Error(actionerrors.ErrParams, err)
	assert.Nil(resp)
	assert.Empty(message)

	// 测试参数错误 (福袋不存在)
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=1000", true, nil)
	c.User().ID = 3457182
	resp, message, err = ActionPrizeInfo(c)
	require.Error(actionerrors.ErrNotFound("福袋不存在"), err)
	assert.Nil(resp)
	assert.Empty(message)

	// 测试未中奖（无资格）时
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=1", true, nil)
	c.User().ID = 1
	resp, message, err = ActionPrizeInfo(c)
	require.NoError(err)
	assert.Equal("success", message)
	r := resp.(*prizeInfoResp)
	require.NotNil(r)
	assert.EqualValues(1, r.LuckyBagID)
	assert.Equal(PrizeStatusNotEligible, r.Status)

	// 测试已中奖但未兑奖（兑奖时间内）时
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=1", true, nil)
	c.User().ID = 3457182
	resp, message, err = ActionPrizeInfo(c)
	require.NoError(err)
	assert.Equal("success", message)
	r = resp.(*prizeInfoResp)
	require.NotNil(r)
	assert.EqualValues(1, r.LuckyBagID)
	assert.Equal(PrizeStatusNotRedeemed, r.Status)
	assert.NotNil(r.InitiateUser)
	assert.EqualValues(3457181, r.InitiateUser.UserID)
	assert.Nil(r.RedeemUser)

	// 测试已中奖并且已经兑奖时
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=2", true, nil)
	c.User().ID = 3457182
	resp, message, err = ActionPrizeInfo(c)
	require.NoError(err)
	assert.Equal("success", message)
	r = resp.(*prizeInfoResp)
	require.NotNil(r)
	assert.EqualValues(2, r.LuckyBagID)
	assert.Equal(PrizeStatusRedeemed, r.Status)
	assert.NotNil(r.InitiateUser)
	assert.EqualValues(3457181, r.InitiateUser.UserID)
	assert.NotNil(r.RedeemUser)
	assert.EqualValues(3457182, r.RedeemUser.UserID)

	// 测试已中奖并且已过兑奖时间时
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=3", true, nil)
	c.User().ID = 3457182
	resp, message, err = ActionPrizeInfo(c)
	require.NoError(err)
	assert.Equal("success", message)
	r = resp.(*prizeInfoResp)
	require.NotNil(r)
	assert.EqualValues(3, r.LuckyBagID)
	assert.Equal(PrizeStatusExpired, r.Status)
	assert.NotNil(r.InitiateUser)
	assert.EqualValues(3457181, r.InitiateUser.UserID)
	assert.Nil(r.RedeemUser)
}

func TestNewPrizeInfoParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/api/v2/user/luckybag/prize/info"
	// 测试参数错误
	c := handler.NewTestContext(http.MethodGet, api, true, nil)
	c.User().ID = 3457182
	params, err := newPrizeInfoParams(c)
	require.Error(actionerrors.ErrParams, err)
	require.Nil(params)

	// 测试参数错误 (福袋不存在)
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=1000", true, nil)
	params, err = newPrizeInfoParams(c)
	require.Error(actionerrors.ErrNotFound("福袋不存在"), err)
	require.Nil(params)

	// 测试正常情况
	c = handler.NewTestContext(http.MethodGet, api+"?lucky_bag_id=1", true, nil)
	params, err = newPrizeInfoParams(c)
	require.NoError(err)
	require.NotNil(params)
	assert.EqualValues(1, params.LuckyBagID)
	assert.EqualValues(12, params.userID)
	assert.NotNil(params.luckyBag)
}

func TestPrizeInfoParams_getPrizeInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试未中奖（无资格）时
	luckyBag1 := new(luckybag.InitiateRecord)
	require.NoError(luckybag.DB().Where("id = ?", 1).Take(luckyBag1).Error)
	params := prizeInfoParams{
		LuckyBagID: 1,
		userID:     13,
		luckyBag:   luckyBag1,
	}
	require.NoError(params.getPrizeInfo())
	require.NotNil(params.resp)
	assert.EqualValues(1, params.resp.LuckyBagID)
	assert.Equal(PrizeStatusNotEligible, params.resp.Status)

	// 测试已中奖但未兑奖（兑奖时间内）时
	params.userID = 3457182
	require.NoError(params.getPrizeInfo())
	require.NotNil(params.resp)
	assert.EqualValues(1, params.resp.LuckyBagID)
	assert.Equal(PrizeStatusNotRedeemed, params.resp.Status)
	assert.NotNil(params.resp.InitiateUser)
	assert.EqualValues(3457181, params.resp.InitiateUser.UserID)
	assert.Nil(params.resp.RedeemUser)

	// 测试已中奖并且已经兑奖时
	luckyBag2 := new(luckybag.InitiateRecord)
	require.NoError(luckybag.DB().Where("id = ?", 2).Take(luckyBag2).Error)
	params = prizeInfoParams{
		LuckyBagID: 2,
		userID:     3457182,
		luckyBag:   luckyBag2,
	}
	require.NoError(params.getPrizeInfo())
	require.NotNil(params.resp)
	assert.EqualValues(2, params.resp.LuckyBagID)
	assert.Equal(PrizeStatusRedeemed, params.resp.Status)
	assert.NotNil(params.resp.InitiateUser)
	assert.EqualValues(3457181, params.resp.InitiateUser.UserID)
	assert.NotNil(params.resp.RedeemUser)
	assert.EqualValues(3457182, params.resp.RedeemUser.UserID)

	// 测试已中奖并且已过兑奖时间时
	luckyBag3 := new(luckybag.InitiateRecord)
	require.NoError(luckybag.DB().Where("id = ?", 3).Take(luckyBag3).Error)
	params = prizeInfoParams{
		LuckyBagID: 3,
		userID:     3457182,
		luckyBag:   luckyBag3,
	}
	require.NoError(params.getPrizeInfo())
	require.NotNil(params.resp)
	assert.EqualValues(3, params.resp.LuckyBagID)
	assert.Equal(PrizeStatusExpired, params.resp.Status)
	assert.NotNil(params.resp.InitiateUser)
	assert.EqualValues(3457181, params.resp.InitiateUser.UserID)
	assert.Nil(params.resp.RedeemUser)
}

func TestGetPrizeStatus(t *testing.T) {
	assert := assert.New(t)

	// 测试未中奖（无资格）
	status := getPrizeStatus(nil)
	assert.Equal(PrizeStatusNotEligible, status)

	// 测试未兑奖（兑奖时间内）
	nowUnix := goutil.TimeNow().Unix()
	userPrize := &luckybag.UserPrize{
		ID:            1,
		CreateTime:    nowUnix,
		ModifiedTime:  nowUnix,
		LuckyBagID:    1,
		RewardTime:    nowUnix,
		UserID:        12,
		RedeemEndTime: nowUnix + 3600,
		RedeemTime:    0,
		RedeemUserID:  0,
	}
	status = getPrizeStatus(userPrize)
	assert.Equal(PrizeStatusNotRedeemed, status)

	// 测试已过兑奖时间
	userPrize.RedeemEndTime = nowUnix - 1
	status = getPrizeStatus(userPrize)
	assert.Equal(PrizeStatusExpired, status)

	// 测试已经兑奖
	userPrize.RedeemTime = nowUnix
	userPrize.RedeemUserID = 1
	status = getPrizeStatus(userPrize)
	assert.Equal(PrizeStatusRedeemed, status)
}

func TestActionRedeemPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIAddDramaTransactionRecord, func(interface{}) (interface{}, error) {
		return &userapi.AddDramaTransactionRecordResp{
			TransactionIDs: []int64{1},
		}, nil
	})
	defer cleanup()

	cleanup1 := mrpc.SetMock(userapi.URIDramaBought, func(input interface{}) (interface{}, error) {
		dramaBoughtParams, ok := input.(userapi.IsDramaBoughtParams)
		require.True(ok)
		if dramaBoughtParams.UserID == 12 {
			return true, nil
		}
		return false, nil
	})
	defer cleanup1()

	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (interface{}, error) {
			sms, ok := input.(map[string]interface{})
			require.True(ok)
			require.NotEmpty(sms)
			return handler.M{
				"count": 1,
			}, nil
		})
	defer cancel()

	// 测试参数错误
	api := "/api/v2/user/luckybag/redeem/prize"
	body := redeemPrizeParams{
		PrizeID: 0,
		UserID:  12,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	resp, message, err := ActionRedeemPrize(c)
	require.Error(actionerrors.ErrParams, err)
	assert.Nil(resp)
	assert.Empty(message)

	// 测试中奖记录不存在
	body = redeemPrizeParams{
		PrizeID: 1000,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	resp, message, err = ActionRedeemPrize(c)
	require.Error(actionerrors.ErrNotFound("中奖记录不存在！"), err)
	assert.Nil(resp)
	assert.Empty(message)

	// 测试中奖记录存在但已兑奖
	body = redeemPrizeParams{
		PrizeID: 5,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	resp, message, err = ActionRedeemPrize(c)
	require.Error(actionerrors.NewErrForbidden("该福袋已兑奖！"), err)
	assert.Nil(resp)
	assert.Empty(message)

	// 测试该M号已拥有本剧
	body = redeemPrizeParams{
		PrizeID: 4,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457181
	resp, message, err = ActionRedeemPrize(c)
	require.Error(actionerrors.NewErrForbidden("该M号已拥有本剧，无法兑换！"), err)
	assert.Nil(resp)
	assert.Empty(message)

	// 测试确认弹窗
	body = redeemPrizeParams{
		PrizeID: 7,
		UserID:  3457181,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457181
	resp, message, err = ActionRedeemPrize(c)
	msg := `M号：3457181<br>昵称：test_user<br>兑换剧集：广播剧 1`
	require.Error(actionerrors.ErrConfirmRequired(msg, 1, true), err)
	assert.Nil(resp)
	assert.Empty(message)

	// 删除测试数据
	lockKey := keys.LockLuckyBagRedeemPrizeLimit1.Format(7)
	require.NoError(service.Redis.Del(lockKey).Err())

	// 测试兑换成功
	body = redeemPrizeParams{
		PrizeID: 7,
		UserID:  3457181,
		Confirm: 1,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457181
	resp, message, err = ActionRedeemPrize(c)
	require.NoError(err)
	assert.Nil(resp)
	assert.Equal("success", message)
}

func TestNewRedeemPrizeParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup1 := mrpc.SetMock(userapi.URIDramaBought, func(input interface{}) (interface{}, error) {
		dramaBoughtParams, ok := input.(userapi.IsDramaBoughtParams)
		require.True(ok)
		if dramaBoughtParams.UserID == 12 {
			return true, nil
		}
		return false, nil
	})
	defer cleanup1()

	// 测试参数错误
	api := "/api/v2/user/luckybag/redeem/prize"
	body := redeemPrizeParams{
		PrizeID: 0,
		UserID:  12,
	}
	c := handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err := newRedeemPrizeParams(c)
	require.Error(actionerrors.ErrParams, err)
	require.Nil(params)

	// 测试中奖记录不存在
	body = redeemPrizeParams{
		PrizeID: 1000,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err = newRedeemPrizeParams(c)
	require.Error(actionerrors.ErrNotFound("中奖记录不存在！"), err)
	require.Nil(params)

	// 测试中奖记录存在但已兑奖
	body = redeemPrizeParams{
		PrizeID: 5,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err = newRedeemPrizeParams(c)
	require.Error(actionerrors.NewErrForbidden("该福袋已兑奖！"), err)
	require.Nil(params)

	// 测试中奖记录存在但已过截止时间
	body = redeemPrizeParams{
		PrizeID: 6,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err = newRedeemPrizeParams(c)
	require.Error(actionerrors.NewErrForbidden("该福袋兑奖已截止！"), err)
	require.Nil(params)

	// 测试实际收奖人用户信息不存在
	body = redeemPrizeParams{
		PrizeID: 6,
		UserID:  10000000,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err = newRedeemPrizeParams(c)
	require.Error(actionerrors.ErrNotFound("M号不存在，请检查后重试！"), err)
	require.Nil(params)

	// 测试实际收奖人用户信息不存在
	body = redeemPrizeParams{
		PrizeID: 4,
		UserID:  12,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457181
	params, err = newRedeemPrizeParams(c)
	require.Error(actionerrors.NewErrForbidden("该M号已拥有本剧，无法兑换！"), err)
	require.Nil(params)

	// 测试确认弹窗
	body = redeemPrizeParams{
		PrizeID: 4,
		UserID:  3457182,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err = newRedeemPrizeParams(c)
	message := `M号：3457182<br>昵称：test_user<br>兑换剧集：广播剧 1`
	require.Error(actionerrors.ErrConfirmRequired(message, 1, true), err)
	require.Nil(params)

	// 测试正常情况
	body = redeemPrizeParams{
		PrizeID: 4,
		UserID:  3457182,
		Confirm: 1,
	}
	c = handler.NewTestContext(http.MethodPost, api, true, body)
	c.User().ID = 3457182
	params, err = newRedeemPrizeParams(c)
	require.NoError(err)
	require.NotNil(params)
	assert.EqualValues(4, params.PrizeID)
	assert.EqualValues(3457182, params.UserID)
	assert.EqualValues(3457182, params.prizeUserID)
	require.NotNil(params.prizeUser)
	assert.EqualValues(3457182, params.prizeUser.ID)
	require.NotNil(params.redeemUser)
	assert.EqualValues(3457182, params.redeemUser.ID)
	require.NotNil(params.creatorUser)
	assert.EqualValues(3457181, params.creatorUser.ID)
	require.NotNil(params.userPrize)
	assert.EqualValues(4, params.userPrize.ID)
	require.NotNil(params.luckyBag)
	assert.EqualValues(4, params.luckyBag.ID)
}

func TestRedeemPrizeParams_redeemPrize(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(userapi.URIAddDramaTransactionRecord, func(input interface{}) (output interface{}, err error) {
		return &userapi.AddDramaTransactionRecordResp{
			TransactionIDs: []int64{1},
		}, nil
	})
	defer cleanup()

	api := "/api/v2/user/luckybag/redeem/prize"
	c := handler.NewTestContext(http.MethodPost, api, true, nil)

	luckyBag := new(luckybag.InitiateRecord)
	require.NoError(luckybag.DB().Where("id = ?", 4).Take(luckyBag).Error)
	userPrize := new(luckybag.UserPrize)
	require.NoError(luckybag.DB().Where("id = ?", 4).Take(userPrize).Error)
	require.EqualValues(0, userPrize.RedeemUserID)
	require.EqualValues(0, userPrize.RedeemTime)
	require.EqualValues(0, userPrize.TransactionID)
	params := redeemPrizeParams{
		PrizeID:   4,
		UserID:    3457182,
		luckyBag:  luckyBag,
		userPrize: userPrize,
		c:         c,
	}

	lockKey := keys.LockLuckyBagRedeemPrizeLimit1.Format(params.PrizeID)
	require.NoError(service.Redis.Del(lockKey).Err())
	ok, err := service.Redis.SetNX(lockKey, params.UserID, 10*time.Second).Result()
	require.NoError(err)
	require.True(ok)
	// 测试有并发锁时
	err = params.redeemPrize()
	require.Error(actionerrors.NewErrForbidden("操作太快啦，请稍后再试~"), err)

	// 测试没有并发锁时兑换成功
	require.NoError(service.Redis.Del(lockKey).Err())
	err = params.redeemPrize()
	require.NoError(err)
	// 验证兑换成功后中奖记录数据是否已更新
	userPrize = new(luckybag.UserPrize)
	require.NoError(luckybag.DB().Where("id = ?", 4).Take(userPrize).Error)
	assert.EqualValues(3457182, userPrize.RedeemUserID)
	assert.NotEqualValues(0, userPrize.RedeemTime)
	assert.EqualValues(1, userPrize.TransactionID)
}

func TestRedeemPrizeParams_sendSystemMsg(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var content string
	cancel := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (interface{}, error) {
			sms, ok := input.(map[string]interface{})
			require.True(ok)
			require.NotEmpty(sms)
			content = sms["systemmsgs"].([]pushservice.SystemMsg)[0].Content
			return handler.M{
				"count": 1,
			}, nil
		})
	defer cancel()

	luckyBag := new(luckybag.InitiateRecord)
	require.NoError(luckybag.DB().Where("id = ?", 4).Take(luckyBag).Error)
	userPrize := new(luckybag.UserPrize)
	require.NoError(luckybag.DB().Where("id = ?", 4).Take(userPrize).Error)
	param := redeemPrizeParams{
		PrizeID:     4,
		UserID:      3457182,
		prizeUserID: 3457182,
		prizeUser: &mowangskuser.Simple{
			ID:       3457182,
			Username: "test_user_1",
		},
		redeemUser: &mowangskuser.Simple{
			ID:       3457182,
			Username: "test_user_1",
		},
		creatorUser: &mowangskuser.Simple{
			ID:       3457181,
			Username: "test_user_2",
		},
		luckyBag:  luckyBag,
		userPrize: userPrize,
	}

	// 测试中奖人 = 收剧人时的系统通知内容
	param.sendSystemMsg()
	expectedContent := fmt.Sprintf(`你在【<a href="%s" target="_blank">test_user_2</a>】直播间抽中的广播剧福袋（福袋 ID: <a href="copy:4">4</a>）奖品《广播剧 1》已兑换成功，可<a href="missevan://drama/purchased" onclick="window.open('%s');return false" target="_blank">前往已购查看</a>。`,
		params.RoomURL(luckyBag.RoomID), params.PurchasedURL())
	assert.Equal(expectedContent, content)

	// 测试中奖人 != 收剧人时的系统通知内容
	param.UserID = 12
	param.redeemUser = &mowangskuser.Simple{
		ID:       12,
		Username: "test_user_12",
	}
	param.sendSystemMsg()
	expectedContent = fmt.Sprintf(`【<a href="%s" target="_blank">test_user_1</a>】（M号: 3457182）已为你成功兑换《广播剧 1》，奖品来源：【<a href="%s" target="_blank">test_user_2</a>】直播间的广播剧福袋（福袋 ID: <a href="copy:4">4</a>），可<a href="missevan://drama/purchased" onclick="window.open('%s');return false" target="_blank">前往已购查看</a>。`,
		params.UserHomepageURL(param.prizeUserID), params.RoomURL(luckyBag.RoomID), params.PurchasedURL())
	assert.Equal(expectedContent, content)
}
