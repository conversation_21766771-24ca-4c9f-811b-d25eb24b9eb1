package luckybag

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionInitiateList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/initiate/list", true, nil)
	resp, message, err := ActionInitiateList(c)
	require.NoError(err)
	assert.Equal("success", message)
	r := resp.(*initiateListResp)
	assert.Empty(r.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: ""}, r.<PERSON>gination)
}

func TestInitiateListParam_load(t *testing.T) {
	assert := assert.New(t)

	param := new(initiateListParam)
	err := param.load(handler.NewTestContext(http.MethodGet, "/api/v2/user/luckybag/initiate/list?marker=1717482605,99,0&page=20", true, nil))
	assert.NoError(err)
	assert.Equal(int64(12), param.userID)
	assert.Equal(int64(20), param.pageSize)
	assert.Equal(&luckybag.MarkerListOption{CreateTime: 1717482605, ID: 99, HasSameTimeLast: false}, param.marker)
}

func TestInitiateListParam_resp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(12345)

	// 测试没有福袋发起记录
	param := &initiateListParam{
		marker:   nil,
		userID:   userID,
		pageSize: 20,
	}
	resp, message, err := param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	assert.Empty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: ""}, resp.Pagination)

	more := luckybag.MoreInfo{
		StartTargetNum: goutil.NewInt64(10),
		EndTargetNum:   goutil.NewInt64(20),
	}
	moreJSON, err := json.Marshal(more)
	require.NoError(err)

	// mock 时间，保证福袋发起时间
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	// 创建福袋发起记录
	record := luckybag.InitiateRecord{
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     30,
		Type:       luckybag.TypeDrama,
		RewardType: luckybag.RewardTypeDrama,
		Status:     luckybag.StatusFinish,
		Name:       "剧集福袋",
		More:       moreJSON,
	}
	require.NoError(record.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 10, 0, 0, 0, time.Local)
	})
	record2 := luckybag.InitiateRecord{
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     20,
		Type:       luckybag.TypeEntity,
		RewardType: luckybag.RewardTypeEntityPersonal,
		Status:     luckybag.StatusFinish,
		Name:       "实物福袋",
		More:       moreJSON,
	}
	require.NoError(record2.Create())

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 6, 6, 11, 0, 0, 0, time.Local)
	})
	record3 := luckybag.InitiateRecord{
		UserID:     userID,
		CreatorID:  userID,
		RoomID:     10,
		Type:       luckybag.TypeEntity,
		RewardType: luckybag.RewardTypeEntityDrama,
		Status:     luckybag.StatusFinish,
		Name:       "剧集实物福袋",
		More:       moreJSON,
	}
	require.NoError(record3.Create())

	// 测试请求第一页
	param.pageSize = 2
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	assert.NotEmpty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: true,
		Marker: fmt.Sprintf("%d,%d,0", record2.CreateTime, record2.ID)}, resp.Pagination)
	except := []*initiateRecord{
		{

			LuckyBagID:  record3.ID,
			Type:        record3.Type,
			RewardType:  record3.RewardType,
			PrizeName:   record3.Name,
			PrizeNum:    record3.Num,
			StartTime:   record3.StartTime,
			EndTime:     record3.EndTime,
			JoinNum:     record3.JoinNum,
			TargetType:  record3.TargetType,
			IncreaseNum: *more.EndTargetNum - *more.StartTargetNum,
		},
		{
			LuckyBagID:  record2.ID,
			Type:        record2.Type,
			RewardType:  record2.RewardType,
			PrizeName:   record2.Name,
			PrizeNum:    record2.Num,
			StartTime:   record2.StartTime,
			EndTime:     record2.EndTime,
			JoinNum:     record2.JoinNum,
			TargetType:  record2.TargetType,
			IncreaseNum: *more.EndTargetNum - *more.StartTargetNum,
		},
	}
	assert.Equal(except, resp.Data)

	// 请求第二页
	param.pageSize = 2
	param.marker = &luckybag.MarkerListOption{CreateTime: record2.CreateTime, ID: record2.ID, HasSameTimeLast: false}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: ""}, resp.Pagination)
	except = []*initiateRecord{
		{
			LuckyBagID:  record.ID,
			Type:        record.Type,
			RewardType:  record.RewardType,
			PrizeName:   record.Name,
			PrizeNum:    record.Num,
			StartTime:   record.StartTime,
			EndTime:     record.EndTime,
			JoinNum:     record.JoinNum,
			TargetType:  record.TargetType,
			IncreaseNum: *more.EndTargetNum - *more.StartTargetNum,
		},
	}
	assert.Equal(except, resp.Data)

	// 测试无分页
	param = &initiateListParam{
		marker:   nil,
		userID:   userID,
		pageSize: 3, // 一共三条福袋发起记录
	}
	resp, message, err = param.resp()
	require.NoError(err)
	assert.Equal("success", message)
	require.NotEmpty(resp.Data)
	assert.Equal(goutil.MarkerPagination{HasMore: false, Marker: ""}, resp.Pagination)
}
