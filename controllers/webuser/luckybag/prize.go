package luckybag

import (
	"fmt"
	"html"
	"time"

	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type prizeListParam struct {
	marker   *luckybag.MarkerListOption
	pageSize int64
	userID   int64
}

type prizeRecord struct {
	PrizeID         int64  `json:"prize_id"`
	LuckyBagID      int64  `json:"lucky_bag_id"`
	Type            int    `json:"type"`
	RewardType      int    `json:"reward_type"`
	RewardTime      int64  `json:"reward_time"`
	PrizeName       string `json:"prize_name"`
	RoomID          int64  `json:"room_id"`
	CreatorID       int64  `json:"creator_id"`
	CreatorUsername string `json:"creator_username"`
}

// prizeListResp 福袋中奖记录列表
type prizeListResp struct {
	Data       []*prizeRecord          `json:"data"`
	Pagination goutil.MarkerPagination `json:"pagination"`
}

// ActionPrizeList 获取福袋中奖记录
/**
 * @api {get} /api/v2/user/luckybag/prize/list 获取福袋中奖记录
 * @apiDescription 用户查看自己福袋中奖历史记录
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {String} [marker] 游标，第一次调用不需要传，后续请求返回前一次响应的请求中的 marker
 * @apiParam {Number} [page_size=20] 一页显示数目
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "data": [
 *         {
 *           "prize_id": 1,
 *           "lucky_bag_id": 2,
 *           "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *           "reward_type": 1, // 奖励类型; 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *           "reward_time": 1618196305, // 中奖时间时间戳，单位：秒
 *           "prize_name": "魔道祖师第一季", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示剧集封面图时，需要客户端处理添加书名号
 *           "room_id": 19999,
 *           "creator_id": 1,
 *           "creator_username": "主播甲",
 *         }
 *       ],
 *       "pagination": {
 *         "has_more": true, // 是否有更多数据
 *         "marker": "1234567890,1,0" // 当前的 marker，需要加载下一页时回传
 *       }
 *     }
 *   }
 *
 */
func ActionPrizeList(c *handler.Context) (handler.ActionResponse, string, error) {
	var param prizeListParam
	if err := param.load(c); err != nil {
		return nil, "", err
	}
	return param.resp()
}

// load 参数处理
func (param *prizeListParam) load(c *handler.Context) (err error) {
	marker, pageSize, err := c.GetParamMarker()
	if err != nil {
		return actionerrors.ErrParams
	}
	param.marker, err = luckybag.ParseMarker(marker)
	if err != nil {
		logger.WithField("marker", marker).Warn(err)
		return actionerrors.ErrParams
	}
	param.pageSize = pageSize
	param.userID = c.UserID()
	return nil
}

// resp 获取福袋中奖记录
func (param *prizeListParam) resp() (*prizeListResp, string, error) {
	// 获取用户的中奖记录列表
	userPrizes, nextMarker, err := luckybag.ListUserPrize(param.userID, param.pageSize, param.marker)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	resp := &prizeListResp{
		Data: make([]*prizeRecord, 0, len(userPrizes)),
	}
	if len(userPrizes) == 0 {
		return resp, "success", nil
	}

	// 获取中奖记录列表对应的福袋 IDs
	luckyBagIDs := make([]int64, len(userPrizes))
	for i, prize := range userPrizes {
		luckyBagIDs[i] = prize.LuckyBagID
	}

	// 根据福袋 IDs 批量获取福袋发起记录
	initiateRecords, err := luckybag.FindInitiateRecordsByIDs(luckyBagIDs)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	initiateMap := make(map[int64]*luckybag.InitiateRecord, len(initiateRecords))
	creatorIDs := make([]int64, len(initiateRecords))
	for i, record := range initiateRecords {
		initiateMap[record.ID] = record
		creatorIDs[i] = record.CreatorID
	}

	userMap, err := mowangskuser.FindSimpleMap(creatorIDs) // 函数内部已去重
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	for _, userPrize := range userPrizes {
		i, ok := initiateMap[userPrize.LuckyBagID]
		if !ok {
			// 福袋记录一般不会被删除，若福袋不存在，需要记录日志
			logger.WithFields(logger.Fields{"id": userPrize.LuckyBagID, "user_id": param.userID}).Error("福袋不存在")
			continue
		}
		record := &prizeRecord{
			PrizeID:    userPrize.ID,
			LuckyBagID: userPrize.LuckyBagID,
			Type:       i.Type,
			RewardType: i.RewardType,
			RewardTime: userPrize.RewardTime,
			PrizeName:  i.Name,
			RoomID:     i.RoomID,
			CreatorID:  i.CreatorID,
		}
		if u := userMap[initiateMap[userPrize.LuckyBagID].UserID]; u != nil {
			record.CreatorUsername = u.Username
		}
		resp.Data = append(resp.Data, record)
	}
	if nextMarker != nil {
		resp.Pagination.HasMore = true
		resp.Pagination.Marker = nextMarker.Encode()
	}
	return resp, "success", nil
}

// 兑奖状态 0: 无资格; 1: 未兑奖（兑奖时间内）; 2: 已经兑奖; 3: 已过兑奖时间
const (
	PrizeStatusNotEligible = iota
	PrizeStatusNotRedeemed
	PrizeStatusRedeemed
	PrizeStatusExpired
)

type prizeInfoParams struct {
	LuckyBagID int64

	userID   int64
	luckyBag *luckybag.InitiateRecord
	resp     *prizeInfoResp
}

type prizeInfoResp struct {
	LuckyBagID    int64          `json:"lucky_bag_id"`
	Status        int            `json:"status"`                    // 兑奖状态 0: 无资格; 1: 兑奖时间内; 2: 已经兑奖; 3: 已过兑奖时间
	Type          int            `json:"type,omitempty"`            // 福袋类型 1: 广播剧福袋; 2: 实物福袋
	RewardType    int            `json:"reward_type,omitempty"`     // 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
	RewardTime    int64          `json:"reward_time,omitempty"`     // 中奖时间时间戳，单位：秒
	PrizeID       int64          `json:"prize_id,omitempty"`        // 中奖记录 ID
	PrizeName     string         `json:"prize_name,omitempty"`      // 奖品名称（广播剧名称）
	PrizeIconURL  string         `json:"prize_icon_url,omitempty"`  // 封面图
	PrizeDrama    *prizeDrama    `json:"prize_drama,omitempty"`     // 中奖剧集信息
	InitiateUser  *prizeInfoUser `json:"initiate_user,omitempty"`   // 福袋发起人
	RedeemTime    int64          `json:"redeem_time,omitempty"`     // 兑奖时间，单位：秒，仅已经兑奖后下发
	RedeemEndTime int64          `json:"redeem_end_time,omitempty"` // 兑奖截止时间，单位：秒
	RedeemUser    *prizeInfoUser `json:"redeem_user,omitempty"`     // 实际收奖人，仅已经兑奖后下发
}

// prizeDrama 中奖剧集信息
type prizeDrama struct {
	ID         int64  `json:"id"`          // 剧集 ID
	CoverColor *int64 `json:"cover_color"` // 剧集背景图主颜色，十进制表示
}

type prizeInfoUser struct {
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
	RoomID   int64  `json:"room_id,omitempty"` // 福袋发起人时需要返回其直播间 ID
}

// ActionPrizeInfo 获取福袋中奖详情
/**
 * @api {get} /api/v2/user/luckybag/prize/info 获取福袋中奖详情
 * @apiDescription 用户查看自己福袋中奖详情
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} lucky_bag_id 中奖福袋 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "lucky_bag_id": 2,
 *       "status": 0, // 兑奖状态 0: 无资格; 1: 兑奖时间内; 2: 已经兑奖; 3: 已过兑奖时间
 *       "type": 1, // 福袋类型 1: 广播剧福袋; 2: 实物福袋
 *       "reward_type": 1, // 奖励类型 1: 广播剧; 2: 个人周边; 3: 剧集周边
 *       "reward_time": 1618196305, // 中奖时间时间戳，单位：秒
 *       "prize_id": 1,
 *       "prize_name": "魔道祖师第一季", // 奖品名称，广播剧奖励（reward_type = 1）的页面不需要展示剧集封面图时，需要客户端处理添加书名号
 *       "prize_icon_url": "https://static-test.maoercdn.com/dramacovers/202208/15/5793676f2056184417.jpg", // 封面图
 *       "prize_drama": { // 中奖剧集信息
 *         "id": 1, // 剧集 ID
 *         "cover_color": 16777215 // 剧集背景图主颜色，十进制表示
 *       },
 *       "initiate_user": { // 福袋发起人
 *         "user_id": 1,
 *         "username": "主播甲",
 *         "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *         "room_id": 1234 // 主播房间 ID
 *       },
 *       "redeem_time": 1618196305, // 兑奖时间，单位：秒，仅已经兑奖后下发
 *       "redeem_end_time": 1618196305, // 兑奖截止时间，单位：秒
 *       "redeem_user": { // 实际收奖人，仅已经兑奖后下发
 *         "user_id": 1,
 *         "username": "主播甲",
 *         "iconurl": "http://static-test.maoercdn.com/avatars/icon01.png"
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response（无资格时）:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": {
 *       "lucky_bag_id": 2,
 *       "status": 0 // 兑奖状态 0: 无资格
 *     }
 *   }
 *
 */
func ActionPrizeInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newPrizeInfoParams(c)
	if err != nil {
		return nil, "", err
	}

	err = param.getPrizeInfo()
	if err != nil {
		return nil, "", err
	}

	return param.resp, "success", nil
}

func newPrizeInfoParams(c *handler.Context) (*prizeInfoParams, error) {
	param := new(prizeInfoParams)
	var err error
	param.LuckyBagID, err = c.GetParamInt64("lucky_bag_id")
	if err != nil || param.LuckyBagID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 根据福袋 ID 查询福袋数据
	param.luckyBag, err = luckybag.FindShowingInitiateRecordByID(param.LuckyBagID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.luckyBag == nil || param.luckyBag.Type != luckybag.TypeDrama {
		// 仅剧集福袋会调用此接口，因此不是剧集福袋时直接返回福袋不存在
		return nil, actionerrors.ErrNotFound("福袋不存在")
	}

	param.userID = c.UserID()

	return param, nil
}

// getPrizeInfo 获取福袋中奖详情
func (param *prizeInfoParams) getPrizeInfo() error {
	// 查询中奖记录
	userPrize, err := luckybag.FindUserPrizeByLuckyBagID(param.userID, param.LuckyBagID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if userPrize == nil {
		// 未中奖时状态为无资格，接口不返回与奖品相关的信息
		param.resp = &prizeInfoResp{
			LuckyBagID: param.LuckyBagID,
			Status:     PrizeStatusNotEligible,
		}
		return nil
	}

	userIDs := make([]int64, 0, 2)
	userIDs = append(userIDs, param.luckyBag.UserID)
	if userPrize.RedeemUserID != 0 {
		// 已兑奖时，需要获取实际收奖人信息
		userIDs = append(userIDs, userPrize.RedeemUserID)
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	initiateUser, ok := userMap[param.luckyBag.UserID]
	if !ok {
		logger.WithFields(logger.Fields{
			"user_id":       param.luckyBag.UserID,
			"lucky_bag_id":  param.luckyBag.ID,
			"user_prize_id": userPrize.ID,
		}).Error("福袋发起人（主播）不存在")
		return actionerrors.ErrUserNotFound
	}

	// 获取福袋中奖详情的状态
	param.resp = &prizeInfoResp{
		LuckyBagID:    param.LuckyBagID,
		Status:        getPrizeStatus(userPrize),
		Type:          param.luckyBag.Type,
		RewardType:    param.luckyBag.RewardType,
		PrizeDrama:    &prizeDrama{ID: param.luckyBag.PrizeDramaID},
		PrizeName:     param.luckyBag.Name,
		RewardTime:    userPrize.RewardTime, // 中奖时间时间戳，单位：秒
		PrizeID:       userPrize.ID,
		RedeemEndTime: userPrize.RedeemEndTime, // 兑奖截止时间，单位：秒
		InitiateUser: &prizeInfoUser{
			UserID:   initiateUser.ID,
			Username: initiateUser.Username,
			IconURL:  initiateUser.IconURL,
			RoomID:   param.luckyBag.RoomID,
		},
	}
	if param.luckyBag.MoreInfo != nil {
		param.resp.PrizeIconURL = param.luckyBag.MoreInfo.PrizeIconURL
		param.resp.PrizeDrama.CoverColor = param.luckyBag.MoreInfo.PrizeDramaCoverColor
	}

	if userPrize.RedeemUserID != 0 {
		// 用户已兑奖时
		redeemUser, ok := userMap[userPrize.RedeemUserID]
		if !ok {
			logger.WithFields(logger.Fields{
				"user_id":       userPrize.RedeemUserID,
				"lucky_bag_id":  param.luckyBag.ID,
				"user_prize_id": userPrize.ID,
			}).Error("福袋实际收奖人不存在")
			return actionerrors.ErrUserNotFound
		}
		param.resp.RedeemUser = &prizeInfoUser{
			UserID:   redeemUser.ID,
			Username: redeemUser.Username,
			IconURL:  redeemUser.IconURL,
		}
		// 兑奖时间
		param.resp.RedeemTime = userPrize.RedeemTime
	}

	return nil
}

// getPrizeStatus 获取福袋中奖详情的状态
func getPrizeStatus(userPrice *luckybag.UserPrize) int {
	if userPrice == nil {
		// 中奖记录不存在时状态为无资格
		return PrizeStatusNotEligible
	}
	if userPrice.RedeemUserID != 0 {
		// 用户已兑奖
		return PrizeStatusRedeemed
	}
	if userPrice.RedeemEndTime > goutil.TimeNow().Unix() {
		// 用户未兑奖并且在兑奖时间内
		return PrizeStatusNotRedeemed
	}
	// 用户未兑奖并且已过兑换截止时间
	return PrizeStatusExpired
}

type redeemPrizeParams struct {
	PrizeID int64 `form:"prize_id" json:"prize_id"` // 中奖记录 ID
	UserID  int64 `form:"user_id" json:"user_id"`   // 实际收奖人用户 ID
	Confirm int   `form:"confirm" json:"confirm"`

	prizeUserID int64                    // 中奖人用户 ID
	prizeUser   *mowangskuser.Simple     // 中奖人用户信息
	redeemUser  *mowangskuser.Simple     // 实际收奖人用户信息
	creatorUser *mowangskuser.Simple     // 福袋对应的主播用户信息
	userPrize   *luckybag.UserPrize      // 中奖记录
	luckyBag    *luckybag.InitiateRecord // 福袋信息
	c           *handler.Context
}

// ActionRedeemPrize 用户福袋兑奖
/**
 * @api {post} /api/v2/user/luckybag/redeem/prize 用户福袋兑奖
 * @apiDescription 用户福袋兑奖
 * @apiVersion 0.1.0
 * @apiGroup luckybag
 *
 * @apiParam {Number} prize_id 中奖记录 ID
 * @apiParam {Number} user_id 实际收奖用户 ID
 * @apiParam {Number} [confirm=0] 弹窗控制参数
 *
 * @apiSuccessExample 确认信息弹窗（仅 Web 使用）:
 *   HTTP/1.1 428
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "M号：123456<br/>昵称：XXX<br/>..."
 *     }
 *   }
 *
 * @apiSuccessExample 兑换成功弹窗:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 400
 *     {
 *       "code": 501010000,
 *       "message": "参数错误",
 *       "data": null
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403
 *     {
 *       "code": 200020003,
 *       "message": "该M号已拥有本剧，无法兑换！",
 *       "data": null
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403
 *     {
 *       "code": 200020003,
 *       "message": "该福袋已兑奖！",
 *       "data": null
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403
 *     {
 *       "code": 200020003,
 *       "message": "该福袋兑奖已截止！",
 *       "data": null
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 403
 *     {
 *       "code": 200020003,
 *       "message": "操作太快啦，请稍后再试~",
 *       "data": null
 *     }
 *
 * @apiErrorExample {json} Error-Response:
 *     HTTP/1.1 404
 *     {
 *       "code": 501010004,
 *       "message": "M号不存在，请检查后重试！",
 *       "data": null
 *     }
 */
func ActionRedeemPrize(c *handler.Context) (handler.ActionResponse, string, error) {
	params, err := newRedeemPrizeParams(c)
	if err != nil {
		return nil, "", err
	}

	// 兑奖
	err = params.redeemPrize()
	if err != nil {
		return nil, "", err
	}

	// 兑奖成功发系统通知
	params.sendSystemMsg()

	return nil, "success", nil
}

func newRedeemPrizeParams(c *handler.Context) (*redeemPrizeParams, error) {
	param := new(redeemPrizeParams)
	err := c.BindJSON(&param)
	if err != nil || param.PrizeID <= 0 || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}

	// 中奖用户 ID
	param.prizeUserID = c.UserID()

	param.userPrize, err = luckybag.FindUserPrizeByID(param.PrizeID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.userPrize == nil || param.userPrize.UserID != param.prizeUserID {
		return nil, actionerrors.ErrNotFound("中奖记录不存在！")
	}
	if param.userPrize.RedeemUserID != 0 {
		return nil, actionerrors.NewErrForbidden("该福袋已兑奖！")
	}
	nowUnix := goutil.TimeNow().Unix()
	if param.userPrize.RedeemEndTime < nowUnix {
		return nil, actionerrors.NewErrForbidden("该福袋兑奖已截止！")
	}

	// 根据福袋 ID 查询福袋数据
	param.luckyBag, err = luckybag.FindShowingInitiateRecordByID(param.userPrize.LuckyBagID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.luckyBag == nil || param.luckyBag.Type != luckybag.TypeDrama {
		// 仅支持剧集福袋兑换，不是剧集福袋时直接返回福袋不存在
		return nil, actionerrors.ErrNotFound("福袋不存在")
	}

	userIDs := make([]int64, 0, 3)
	userIDs = append(userIDs, param.UserID)
	userIDs = append(userIDs, param.luckyBag.CreatorID)
	if param.UserID != param.prizeUserID {
		// 中奖人和实际收奖人不是同一人时
		userIDs = append(userIDs, param.prizeUserID)
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 实际收奖人用户信息
	redeemUser, ok := userMap[param.UserID]
	if !ok {
		return nil, actionerrors.ErrNotFound("M号不存在，请检查后重试！")
	}
	param.redeemUser = redeemUser

	// 中奖人用户信息
	prizeUser, ok := userMap[param.prizeUserID]
	if !ok {
		logger.WithFields(logger.Fields{
			"user_id":       param.prizeUserID,
			"user_prize_id": param.PrizeID,
		}).Error("中奖用户不存在")
		return nil, actionerrors.ErrUserNotFound
	}
	param.prizeUser = prizeUser

	// 福袋对应的主播用户信息
	creatorUser, ok := userMap[param.luckyBag.CreatorID]
	if !ok {
		logger.WithFields(logger.Fields{
			"user_id":       param.luckyBag.CreatorID,
			"user_prize_id": param.PrizeID,
		}).Error("福袋对应的主播不存在")
		return nil, actionerrors.ErrUserNotFound
	}
	param.creatorUser = creatorUser

	// 用户是否已购买（已拥有）该剧集
	dramaBought, err := userapi.IsDramaBought(c.UserContext(),
		userapi.IsDramaBoughtParams{
			DramaID: param.luckyBag.PrizeDramaID,
			UserID:  param.UserID,
		})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if dramaBought {
		return nil, actionerrors.NewErrForbidden("该M号已拥有本剧，无法兑换！")
	}

	if param.Confirm == 0 {
		// 确认弹窗信息
		message := fmt.Sprintf(`M号：%d<br>昵称：%s<br>兑换剧集：%s`,
			param.UserID,
			html.EscapeString(param.redeemUser.Username),
			html.EscapeString(param.luckyBag.Name))
		return nil, actionerrors.ErrConfirmRequired(message, 1, true)
	}

	param.c = c

	return param, nil
}

// redeemPrize 兑奖
func (param *redeemPrizeParams) redeemPrize() error {
	// 加锁防止用户在短期内快速兑换造成重复兑换
	lockKey := keys.LockLuckyBagRedeemPrizeLimit1.Format(param.PrizeID)
	ok, err := service.Redis.SetNX(lockKey, param.UserID, 10*time.Second).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return actionerrors.NewErrForbidden("操作太快啦，请稍后再试~")
	}

	// 释放锁
	defer func() {
		err = service.Redis.Del(lockKey).Err()
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}()

	resp, err := userapi.AddDramaTransactionRecord(param.c.UserContext(),
		userapi.AddDramaTransactionRecordParams{
			UserID:               param.UserID,
			OperatorID:           param.prizeUserID,
			DramaIDs:             []int64{param.luckyBag.PrizeDramaID},
			Scene:                userapi.RedeemSceneTypeLuckyBag,
			ContextTransactionID: param.luckyBag.TransactionID,
			UserAgent:            param.c.UserAgent(),
			EquipID:              param.c.EquipID(),
			BUVID:                param.c.BUVID(),
			IP:                   param.c.ClientIP(),
		})
	if err != nil {
		if rpcError, ok := err.(*mrpc.ClientError); ok {
			return rpcError
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}

	// 更新中奖记录
	nowUnix := goutil.TimeNow().Unix()
	db := luckybag.DB().Model(&param.userPrize).
		Where("id = ? AND redeem_user_id = 0", param.userPrize.ID).
		Updates(map[string]interface{}{
			"redeem_user_id": param.UserID,           // 实际收奖人
			"redeem_time":    nowUnix,                // 兑奖时间
			"transaction_id": resp.TransactionIDs[0], // 兑奖订单号
			"modified_time":  nowUnix,
		})
	if err := db.Error; err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if db.RowsAffected == 0 {
		logger.WithFields(logger.Fields{
			"id":             param.userPrize.ID,
			"redeem_user_id": param.UserID,
			"transaction_id": resp.TransactionIDs[0],
		}).Error("兑奖成功后更新中奖记录失败")
		// PASS
	}

	return nil
}

// sendSystemMsg 发送系统通知
func (param *redeemPrizeParams) sendSystemMsg() {
	var content string
	if param.UserID == param.prizeUserID {
		// 中奖人 = 收剧人时的系统通知内容
		// WORKAROUND: 已购页链接临时使用 onclick 的方式兼容 Web 和客户端，待客户端 msr 库支持已购页跳转后可删除此兼容
		content = fmt.Sprintf(`你在【<a href="%s" target="_blank">%s</a>】直播间抽中的广播剧福袋（福袋 ID: <a href="copy:%d">%d</a>）奖品《%s》已兑换成功，可<a href="missevan://drama/purchased" onclick="window.open('%s');return false" target="_blank">前往已购查看</a>。`,
			params.RoomURL(param.luckyBag.RoomID),
			html.EscapeString(param.creatorUser.Username),
			param.luckyBag.ID,
			param.luckyBag.ID,
			html.EscapeString(param.luckyBag.Name),
			params.PurchasedURL())
	} else {
		// 中奖人 != 收剧人时的系统通知内容
		// WORKAROUND: 已购页链接临时使用 onclick 的方式兼容 Web 和客户端，待客户端 msr 库支持已购页跳转后可删除此兼容
		content = fmt.Sprintf(`【<a href="%s" target="_blank">%s</a>】（M号: %d）已为你成功兑换《%s》，奖品来源：【<a href="%s" target="_blank">%s</a>】直播间的广播剧福袋（福袋 ID: <a href="copy:%d">%d</a>），可<a href="missevan://drama/purchased" onclick="window.open('%s');return false" target="_blank">前往已购查看</a>。`,
			params.UserHomepageURL(param.prizeUserID),
			html.EscapeString(param.prizeUser.Username),
			param.prizeUserID,
			html.EscapeString(param.luckyBag.Name),
			params.RoomURL(param.luckyBag.RoomID),
			html.EscapeString(param.creatorUser.Username),
			param.luckyBag.ID,
			param.luckyBag.ID,
			params.PurchasedURL())
	}
	sysMsg := []pushservice.SystemMsg{
		{
			UserID:  param.UserID,
			Title:   "广播剧福袋兑奖成功",
			Content: content,
		},
	}
	err := service.PushService.SendSystemMsgWithOptions(sysMsg, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Errorf("广播剧福袋兑奖成功后系统通知发送失败: %v", err)
		// PASS
	}
}
