package webuser

import (
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

// testUA 访问 sso 用
const testUA = "live-service/controllers/webuser/"

const (
	noble7UserID int64 = 3457114 // 神话测试账号
)

var (
	imRedis *redis.Client
)

func TestMain(m *testing.M) {
	config.InitTest()
	handler.SetMode(handler.TestMode)
	logger.InitTestLog()
	service.InitTest()
	service.SetDBUseSQLite()

	var err error
	redisConf := serviceredis.Config{
		Addr: "redis.srv.maoer.co:6379",
		DB:   106,
	}
	imRedis, err = serviceredis.NewRedisClient(&redisConf)
	if err != nil {
		logger.Fatal(err)
	}

	m.Run()
}

func TestHandler(t *testing.T) {
	t.Run("user", func(t *testing.T) {
		assert := assert.New(t)
		h := Handler()
		assert.Equal("user", h.Name)
		assert.Len(h.SubHandlers, 4)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		kc.Check(h,
			"viewlogs", "deletelogs", "rank", "rank/top", "rank/hourly", "medal/list",
			"medal/wear", "medal/takeoff", "medal/remove", "medal/get",
			"balance-details",
			"rank/love", "rank/loveselection",
			"card", "mynoble",
			"activity/spend",
			"wall/meta", "wall/send", "wall/barrage", "wall/list",
			"liveincome",
			"revenue",
			"withdraw",
		)
	})

	t.Run("status", func(t *testing.T) {
		assert := assert.New(t)
		h := statusHandler()
		assert.Equal("status", h.Name)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		kc.Check(h, "get", "set-general")
	})

	t.Run("revenue", func(t *testing.T) {
		assert := assert.New(t)
		h := revenueHandler()
		assert.Equal("revenue", h.Name)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		kc.Check(h, "noble", "gift", "superfan", "play")
	})
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	handlers := HandlerV2().SubHandlers
	require.Len(handlers, 1)
	assert.Equal("user", handlers[0].Name)

	h := handlers[0].SubHandlers
	require.Len(h, 3)
	assert.Equal("luckybag", h[0].Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h[0], "prize/info", "prize/list", "initiate/list", "luckyuser/list", "redeem/prize")
	assert.Equal("appearance", h[1].Name)
	kc.Check(h[1], "welcome/customize")
	assert.Equal("black-card", h[2].Name)
	kc.Check(h[2], "info")
}
