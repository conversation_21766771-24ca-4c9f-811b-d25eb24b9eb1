package webuser

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/viewlog"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type viewlogsResp struct {
	Data       []*room.Simple    `json:"Datas"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionViewlogs 用户直播间访问记录
/**
 * @api {get} /api/v2/user/viewlogs 用户直播间访问记录
 * @apiDescription 用户直播间访问记录，需要登录
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 一页显示数目
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "Datas": [
 *         {
 *           "room_id": 152,
 *           "name": "12345",
 *           "cover_url": "http://static.maoercdn.com/cover.png",
 *           "announcement": "12345",
 *           "creator_username": "1234",
 *           "creator_iconurl": "http://static.maoercdn.com/avatars/icon01.png",
 *           "creator_id": 12345,
 *           "catalog_id": 106,
 *           "catalog_name": "闲聊",
 *           "catalog_color": "#6FEFEC",
 *           "statistics": {
 *             "attention": true,
 *             "attention_count": 123
 *           },
 *           "status": {
 *             "open": 1
 *           }
 *         }
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionViewlogs(c *handler.Context) (handler.ActionResponse, error) {
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	logs, pa, err := viewlog.Find(c.User().ID, p, pageSize)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := &viewlogsResp{
		Data:       make([]*room.Simple, 0, len(logs)),
		Pagination: pa,
	}
	if pa.Valid() {
		ownerIDs := make([]int64, len(logs))
		for i := 0; i < len(logs); i++ {
			ownerIDs[i] = logs[i].RoomCreatorID
		}
		opt := room.FindOptions{
			ListenerID:      c.User().ID,
			FindFans:        true,
			FindCatalogInfo: true,
			FindCreator:     true}
		rooms, err := room.FindSimpleMapByCreatorID(ownerIDs, &opt)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		for _, log := range logs {
			r := rooms[log.RoomCreatorID]
			if r == nil {
				continue
			}
			r.Statistics.Accumulation = 0
			resp.Data = append(resp.Data, r)
		}
	}
	return resp, nil
}
