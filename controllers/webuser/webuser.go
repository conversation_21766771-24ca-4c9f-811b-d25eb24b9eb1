package webuser

import (
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/controllers/webuser/appearance"
	"github.com/MiaoSiLa/live-service/controllers/webuser/blackcard"
	"github.com/MiaoSiLa/live-service/controllers/webuser/level"
	"github.com/MiaoSiLa/live-service/controllers/webuser/luckybag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "user",
		SubHandlers: []handler.Handler{
			statusHandler(),
			revenueHandler(),
			appearance.Handler(),
			level.Handler(),
		},
		Actions: map[string]*handler.Action{
			"viewlogs":           handler.NewAction(handler.GET, ActionViewlogs, true),
			"deletelogs":         handler.NewAction(handler.POST, ActionDeleteLogs, true),
			"rank/top":           handler.NewAction(handler.GET, ActionUserRankTop, false),
			"rank":               handler.NewAction(handler.GET, ActionUserRank, false),
			"rank/hourly":        handler.NewAction(handler.GET, ActionUserRankHourly, false),
			"rank/love":          handler.NewAction(handler.GET, ActionRankLove, false),
			"rank/loveselection": handler.NewAction(handler.POST, ActionRankLoveSelection, true),
			"medal/list":         handler.NewAction(handler.GET, ActionMedalList, true),
			"medal/wear":         handler.NewAction(handler.POST, ActionMedalWear, true),
			"medal/takeoff":      handler.NewAction(handler.POST, ActionMedalTakeoff, true),
			"medal/remove":       handler.NewAction(handler.POST, ActionMedalRemove, true),
			"medal/get":          handler.NewAction(handler.GET, ActionMedalGet, false),
			"balance-details":    handler.NewAction(handler.GET, ActionBalanceDetails, true),
			"card":               handler.NewAction(handler.GET, ActionUserCard, false),
			"mynoble":            handler.NewAction(handler.GET, ActionUserMyNoble, false),

			"activity/spend": handler.NewAction(handler.GET, ActionActivitySpend, true),

			"wall/meta":    handler.NewAction(handler.GET, ActionWallMeta, false),
			"wall/send":    handler.NewAction(handler.POST, ActionWallSend, true),
			"wall/barrage": handler.NewAction(handler.GET, ActionWallBarrage, false),
			"wall/list":    handler.NewAction(handler.GET, ActionWallList, false),

			// 素人收益相关
			"liveincome": handler.NewAction(handler.GET, ActionLiveIncome, true), // 主播收益记录
			"revenue":    handler.NewAction(handler.GET, ActionRevenue, true),    // 主播收益总额
			"withdraw":   handler.NewAction(handler.POST, ActionWithdraw, true),  // 主播提现
		},
	}
}

func statusHandler() handler.Handler {
	return handler.Handler{
		Name: "status",
		Actions: map[string]*handler.Action{
			"set-general": handler.NewAction(handler.POST, ActionSettingsSetGeneral, true),
			"get":         handler.NewAction(handler.GET, ActionStatusGet, true),
		},
	}
}

func revenueHandler() handler.Handler {
	return handler.Handler{
		Name: "revenue",
		Actions: map[string]*handler.Action{
			"noble":    handler.NewAction(handler.GET, chatroom.ActionRevenueNobles, true),
			"gift":     handler.NewAction(handler.GET, chatroom.ActionRevenueGifts, true),
			"superfan": handler.NewAction(handler.GET, chatroom.ActionRevenueSuperFans, true),
			"play":     handler.NewAction(handler.GET, ActionRevenuePlay, true),
		},
	}
}

// HandlerV2 返回 handlerV2
func HandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "",
		SubHandlers: []handler.HandlerV2{
			{
				Name: "user",
				SubHandlers: []handler.HandlerV2{
					luckyBagHandler(),
					appearance.HandlerV2(),
					blackcard.HandlerV2(),
				},
			},
		},
	}
}

func luckyBagHandler() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "luckybag",
		Actions: map[string]*handler.ActionV2{
			"prize/info":     handler.NewActionV2(handler.GET, luckybag.ActionPrizeInfo, handler.ActionOption{LoginRequired: true}),
			"prize/list":     handler.NewActionV2(handler.GET, luckybag.ActionPrizeList, handler.ActionOption{LoginRequired: true}),
			"initiate/list":  handler.NewActionV2(handler.GET, luckybag.ActionInitiateList, handler.ActionOption{LoginRequired: true}),
			"luckyuser/list": handler.NewActionV2(handler.GET, luckybag.ActionLuckyUser, handler.ActionOption{LoginRequired: true}),
			"redeem/prize":   handler.NewActionV2(handler.POST, luckybag.ActionRedeemPrize, handler.ActionOption{LoginRequired: true}),
		},
	}
}
