package webuser

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestUserRankTagKeys(t *testing.T) {
	assert := assert.New(t)
	assert.Empty(tutil.KeyExists(tutil.JSON, rankTopInfo{}, "type", "title", "introduction", "Datas"))
	assert.Empty(tutil.KeyExists(tutil.JSON, UserRankResp{}, "Datas", "myrank", "event", "refresh", "title", "rule", "rule_url", "reward_url"))
	assert.Empty(tutil.KeyExists(tutil.JSON, RankList{}, "room", "attention"))
	assert.Empty(tutil.KeyExists(tutil.JSON, hourRankResp{}, "current", "last", "creator_rank", "refresh"))
}

func TestActionUserRankTop(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := usersrank.Key(usersrank.TypeMonth, goutil.TimeNow())
	pipe := service.Redis.Pipeline()
	pipe.Del(key)
	pipe.ZAdd(key, &redis.Z{Member: 12, Score: 12})
	pipe.Expire(key, 5*time.Minute)
	_, err := pipe.Exec()
	require.NoError(err)
	c := handler.NewTestContext(http.MethodGet, "/user/rank/top3", false, nil)
	e := c.Equip()
	e.FromApp = true
	e.OS = goutil.IOS
	e.AppVersion = "4.8.5"
	r, err := ActionUserRankTop(c)
	require.NoError(err)
	resp := r.(rankTopResp)
	expected := rankTopResp{
		rankTopInfo{Type: 4, Title: "小时榜", Introduction: "本小时直播间主播排行榜"},
		rankTopInfo{Type: 1, Title: "今日榜", Introduction: "今日直播间主播排行榜"},
		rankTopInfo{Type: 2, Title: "周榜", Introduction: "本周直播间主播排行榜"},
		rankTopInfo{Type: 3, Title: "月榜", Introduction: "本月直播间主播排行榜"},
		rankTopInfo{Type: 7, Title: "新人榜", Introduction: "今日新人主播排行榜"},
	}
	require.Len(resp, 5)
	for i := 0; i < len(expected); i++ {
		assert.Equal(expected[i].Type, resp[i].Type)
		assert.Equal(expected[i].Title, resp[i].Title)
		assert.Equal(expected[i].Introduction, resp[i].Introduction)
	}
	require.Len(resp[3].Data, 1)
	assert.Equal(int64(12), resp[3].Data[0].UserID)
	assert.Equal(int64(12), resp[3].Data[0].Revenue)
	assert.Equal(int64(1), resp[3].Data[0].Rank)
}

func TestDefaultRankTopResp(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "", false, nil)
	e := c.Equip()
	e.FromApp = true

	e.OS = goutil.IOS
	e.AppVersion = "4.8.5"
	assert.Len(defaultRankTopResp(c), 5)
	e.AppVersion = "4.8.4"
	assert.Len(defaultRankTopResp(c), 4)

	e.OS = goutil.Android
	e.AppVersion = "5.7.2"
	assert.Len(defaultRankTopResp(c), 5)
	e.AppVersion = "5.7.1"
	assert.Len(defaultRankTopResp(c), 4)
}

func TestUserRankParam_buildNovaRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(1000)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	key := usersrank.NovaKey(goutil.TimeNow(), usersrank.NovaKeySlot(testUserID))
	_, err := service.Redis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		pipeliner.SAdd(key, testUserID)
		serviceredis.ExpireAt(pipeliner, key, usersrank.Deadline(usersrank.TypeNova, goutil.TimeNow()))
		return nil
	})
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "", false, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.8.5"
	// 传 creator_id，my_rank 为主播排名
	param := &UserRankParam{
		c:            c,
		UserRankResp: new(UserRankResp),
		creatorID:    testUserID,
		rankType:     usersrank.TypeNova,
	}
	err = param.buildNovaRank()
	require.NoError(err)
	require.NotNil(param.UserRankResp.MyRank)
	assert.True(param.UserRankResp.MyRank.IsNova)
	assert.EqualValues(testUserID, param.UserRankResp.MyRank.UserID)
	assert.NotEmpty(param.UserRankResp.Rule)
	assert.NotEmpty(param.UserRankResp.RuleURL)

	c = handler.NewTestContext(http.MethodGet, "", false, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "6.1.4"

	// 未登录状态，无 creator_id，无 my_rank
	param = &UserRankParam{
		c:            c,
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeNova,
	}
	err = param.buildNovaRank()
	require.NoError(err)
	assert.Nil(param.UserRankResp.MyRank)
	assert.Empty(param.UserRankResp.Rule)
	assert.NotEmpty(param.UserRankResp.RuleURL)

	// 登录状态，该用户有直播间
	param = &UserRankParam{
		c:            handler.NewTestContext(http.MethodGet, "", true, nil),
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeNova,
	}
	key = usersrank.NovaKey(goutil.TimeNow(), usersrank.NovaKeySlot(param.c.UserID()))
	require.NoError(service.Redis.SRem(key, param.c.UserID()).Err()) // 删除该用户新人榜资格
	err = param.buildNovaRank()
	require.NoError(err)
	require.NotNil(param.UserRankResp.MyRank)
	assert.False(param.UserRankResp.MyRank.IsNova)

	// 登录状态，该用户没有直播间
	param = &UserRankParam{
		c:            handler.NewTestContext(http.MethodGet, "", true, nil),
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeNova,
	}
	param.c.User().ID = 10000
	key = usersrank.NovaKey(goutil.TimeNow(), usersrank.NovaKeySlot(param.c.UserID()))
	require.NoError(service.Redis.SRem(key, param.c.UserID()).Err())
	err = param.buildNovaRank()
	require.NoError(err)
	require.NotNil(param.UserRankResp.MyRank)
	assert.True(param.UserRankResp.MyRank.IsNova)
}

func TestBuildGashaponRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := UserRankParam{
		c:            handler.NewTestContext(http.MethodGet, "", false, nil),
		UserRankResp: new(UserRankResp),
		roomID:       18113499,
		rankType:     usersrank.TypeGashaponWeek,
	}
	now := goutil.TimeNow()
	key := usersrank.Key(usersrank.TypeGashaponWeek, now)
	pipe := service.Redis.Pipeline()
	pipe.Del(key)
	pipe.ZAdd(key, &redis.Z{Member: 12, Score: 12})
	pipe.Expire(key, 5*time.Minute)
	_, err := pipe.Exec()
	require.NoError(err)

	require.NoError(param.buildGashaponRank())
	require.NotNil(param.MyRank)
	assert.EqualValues(1, param.MyRank.Rank)
	assert.EqualValues(12, param.MyRank.Revenue)
	require.NotEmpty(param.Data)
	assert.EqualValues(12, param.Data[0].UserID)
	assert.Nil(param.Data[0].Attention)
}

func TestBuildLastMonthRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 1, 2, 0, 0, 0, 0, time.Local)
	})
	defer cancel()

	when := time.Date(2022, 12, 1, 0, 0, 0, 0, time.Local)
	key := usersrank.Key(usersrank.TypeMonth, when)
	err := service.Redis.Del(key).Err()
	require.NoError(err)

	roomIDs := []int64{347142108, 186192636, 172842330, 100000009, 24113499, 22489473, 100000005, 11223344, 1003, 369892}
	rooms, err := room.FindAll(bson.M{"room_id": bson.M{"$in": roomIDs}})
	require.NoError(err)

	data := make([]*redis.Z, 0, len(rooms))
	for i, r := range rooms {
		data = append(data, &redis.Z{
			Score:  float64(i + 1),
			Member: r.CreatorID,
		})
	}
	err = service.Redis.ZAdd(key, data...).Err()
	require.NoError(err)

	param := UserRankParam{
		UserRankResp: new(UserRankResp),
		c:            handler.NewTestContext(http.MethodGet, "", false, nil),
	}
	err = param.buildLastMonthRank()
	require.NoError(err)
	assert.Equal("2022.12", param.UserRankResp.Title)
	assert.Len(param.Data, len(roomIDs))
	assert.LessOrEqual(len(param.Data), 10)
	for i, e := range param.Data {
		assert.NotEmpty(e.UserID)
		assert.EqualValues(i+1, e.Rank)
		assert.EqualValues(len(param.Data)-i, e.Revenue)
		assert.NotEmpty(e.Room)
	}
}

func TestUserRankParam_findEvent(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyParams1.Format(params.KeyRank)
	rank := params.DefaultRank()
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(rank), 5*time.Second).Err())

	param := UserRankParam{
		UserRankResp: new(UserRankResp),
	}
	param.findEvent()
	assert.Nil(param.UserRankResp.Event)

	rank.RankEvent = &params.RankEvent{
		Icon:    "oss://test-icon.jpg",
		OpenURL: "http://open",
	}
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(rank), 5*time.Second).Err())
	param.findEvent()
	require.NotNil(param.UserRankResp.Event)
	assert.Equal("https://static-test.missevan.com/test-icon.jpg", param.UserRankResp.Event.IconURL)
	assert.Equal(rank.RankEvent.OpenURL, param.UserRankResp.Event.OpenURL)

	param = UserRankParam{
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeWeek,
	}
	param.findEvent()
	assert.Empty(param.UserRankResp.RewardURL)
	assert.NotEmpty(param.UserRankResp.Event)

	param = UserRankParam{
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeMonth,
	}
	param.findEvent()
	assert.NotEmpty(param.UserRankResp.RewardURL)
	assert.NotEmpty(param.UserRankResp.Event)

	param = UserRankParam{
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeLastMonth,
	}
	param.findEvent()
	assert.Empty(param.UserRankResp.RewardURL)
	assert.Empty(param.UserRankResp.Event)
}

func TestFindRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	param := UserRankParam{
		c:            handler.CreateTestContext(true),
		UserRankResp: new(UserRankResp),
		rankType:     usersrank.TypeDay,
	}
	param.MyRank = &usersrank.Info{
		UserID: 12,
	}
	now := goutil.TimeNow()
	key := usersrank.Key(usersrank.TypeDay, now)
	assert.NoError(service.Redis.Del(key).Err())
	assert.NoError(service.Redis.ZAdd(key, &redis.Z{Member: 12, Score: 12}).Err())
	assert.NoError(service.Redis.Expire(key, 5*time.Minute).Err())

	assert.NoError(param.findRank())
	assert.LessOrEqual(usersrank.Deadline(usersrank.TypeDay, now).Unix()-now.Unix()-2,
		param.Refresh)
	require.NotNil(param.MyRank)
	require.Len(param.Data, 1)
	assert.Equal(int64(1), param.MyRank.Rank)
	assert.Equal(int64(12), param.MyRank.Revenue)
	assert.Equal(int64(1), param.Data[0].Rank)
	assert.Equal(int64(12), param.Data[0].Revenue)
}

func TestUpdateMyRankRankUp(t *testing.T) {
	assert := assert.New(t)
	param := UserRankParam{
		UserRankResp: new(UserRankResp),
	}
	// 不 panic 就表示测试过了
	param.updateMyRankRankUp()

	param.rankType = usersrank.TypeDay
	param.MyRank = &usersrank.Info{
		Revenue: 10,
		Rank:    1,
	}
	param.updateMyRankRankUp()
	assert.Equal(param.MyRank.Revenue, param.MyRank.RankUp) // 没有第二名

	param.rankType = usersrank.TypeWeek
	param.Data = make([]*RankList, 2)
	param.Data[1] = &RankList{Info: &usersrank.Info{Revenue: 9, RankUp: 5}}
	param.updateMyRankRankUp()
	assert.Equal(int64(1), param.MyRank.RankUp)

	param.rankType = usersrank.TypeMonth
	param.MyRank.Rank = 0
	param.MyRank.Revenue = 0
	param.updateMyRankRankUp()
	assert.Equal(int64(1), param.MyRank.RankUp)

	param.MyRank.Rank = 5
	param.MyRank.Revenue = 10
	param.Data = make([]*RankList, usersrank.RankLen(param.rankType))
	param.Data[3] = &RankList{Info: &usersrank.Info{Revenue: 11}}
	param.updateMyRankRankUp()
	assert.Equal(int64(2), param.MyRank.RankUp)

	param.MyRank = &usersrank.Info{Rank: 100}
	param.Data[usersrank.RankLen(param.rankType)-1] = &RankList{Info: &usersrank.Info{Revenue: 5}}
	param.updateMyRankRankUp()
	assert.Equal(int64(6), param.MyRank.RankUp)
	assert.Zero(param.MyRank.Rank)
}

func TestUserRankSetInfo(t *testing.T) {
	assert := assert.New(t)
	var param UserRankParam
	// NOTICE: 此处只是测试，不进行其他操作，所以入参使用 nil
	param.SetInfo(nil)
	assert.NotNil(param.UserRankResp)
	assert.Len(param.Data, 1)
}

func TestRankFindRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := UserRankParam{UserRankResp: new(UserRankResp)}
	param.Data = []*RankList{
		{Info: &usersrank.Info{UserID: 10, IconURL: "123"}},
		{Info: &usersrank.Info{UserID: 3387502, IconURL: "456"}},
	}
	require.NoError(param.FindRooms())
	require.NotNil(param.Data[0].Room)
	require.NotNil(param.Data[1].Room)
	assert.Nil(param.Data[0].Room.Statistics)
	assert.Equal(int64(10), param.Data[0].Room.CreatorID)
	assert.Equal(int64(3387502), param.Data[1].Room.CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", param.Data[1].Room.CoverURL)
}

func TestRankFindAttention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := UserRankParam{
		User:         new(user.User),
		UserRankResp: new(UserRankResp),
	}
	param.User.ID = 12
	res, err := attentionuser.CheckAttention(12, []int64{45})
	require.NoError(err)
	require.Len(res, 1)
	param.Data = append(param.Data, &RankList{Info: &usersrank.Info{UserID: 45}})
	followed := !res[0].Followed
	param.Data[0].Attention = &followed
	param.listUserIDs = []int64{45}
	param.FindAttention()
	assert.Equal(res[0].Followed, *param.Data[0].Attention)

	param.listUserIDs = nil
	param.Data = nil
	param.User = new(user.User)
	param.User.ID = 12
	param.FindAttention()
	// 不 panic 就对了
}

func TestActionUserRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyParams1.Format(params.KeyRank)
	re := &params.Rank{
		RankEvent: &params.RankEvent{
			Icon:    "oss://test-icon.jpg",
			OpenURL: "http://open",
		},
	}
	require.NoError(service.LRURedis.Set(key, tutil.SprintJSON(re), 5*time.Second).Err())

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/rank?type=1", false, nil)
	r, err := ActionUserRank(c)
	require.NoError(err)
	resp := r.(*UserRankResp)
	assert.Nil(resp.MyRank)
	assert.NotEmpty(resp.Rule)
	assert.Equal(rankRuleURL(), resp.Rule)
	require.NotNil(resp.Event)
	assert.NotEmpty(resp.Event.IconURL)
	assert.NotEmpty(resp.Event.OpenURL)

	// 盲盒周榜
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/rank?type=5", false, nil)
	r, err = ActionUserRank(c)
	require.NoError(err)
	resp = r.(*UserRankResp)
	assert.Nil(resp.MyRank)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/rank?type=5&room_id=18113499", false, nil)
	r, err = ActionUserRank(c)
	require.NoError(err)
	resp = r.(*UserRankResp)
	assert.NotNil(resp.MyRank)
}

func TestActionUserRankHour(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 12, 7, 17, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	last := now.Add(-time.Hour)
	pipe := service.Redis.Pipeline()
	keys := [2]string{
		usersrank.Key(usersrank.TypeHour, now),
		usersrank.Key(usersrank.TypeHour, last),
	}
	testData := []*redis.Z{
		{Score: 1300, Member: 13},
		{Score: 1200, Member: 12},
		{Score: 1100, Member: 11},
		{Score: 1000, Member: 10},
	}
	pipe.Del(keys[1])
	pipe.ZAdd(keys[0], testData...)
	pipe.ZAdd(keys[1], testData[0], testData[1])
	pipe.Expire(keys[0], 5*time.Minute)
	pipe.Expire(keys[1], 5*time.Minute)
	_, err := pipe.Exec()
	require.NoError(err)

	req, _ := http.NewRequest("GET", "/user/rank/hourly?creator_id=10", nil)
	c := handler.CreateTestContextWithMiddlewares(req)
	r, err := ActionUserRankHourly(c)
	require.NoError(err)
	resp := r.(*hourRankResp)
	assert.GreaterOrEqual(len(resp.Current), 4) // 送礼测试会导致榜单超过 4 个
	assert.Len(resp.Last, 2)
	assert.Equal(int64(10), resp.CreatorRank.UserID)
	assert.Equal(int64(101), resp.CreatorRank.RankUp)
}
