package level

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	targetSpendLv85 = 10000 // 85 级目标奖励所需钻石数量
)

type rewardInfoResp struct {
	UserLevel    int                         `json:"user_level"`
	LevelRewards map[string]*levelRewardInfo `json:"level_rewards"`

	user          *liveuser.Simple
	appearanceMap map[int64]*appearance.Appearance
}

type progress struct {
	Current int64 `json:"current"`
	Target  int64 `json:"target"`
}

type reward struct {
	Name    string `json:"name"`
	IconURL string `json:"icon_url"`
}

type levelRewardInfo struct {
	Lock        int       `json:"lock"`
	Title       string    `json:"title"`
	Tip         string    `json:"tip"`
	Rewards     []reward  `json:"rewards"`
	ReleaseTime *int64    `json:"release_time,omitempty"` // 45 级等级奖励的发放时间
	Progress    *progress `json:"progress,omitempty"`     // 85 级等级奖励的获取进度
}

// ActionRewardInfo 等级奖励详情
/**
 * @api {get} /api/v2/user/level/reward-info 等级奖励详情
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiPermission user
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user_level": 85, // 用户当前等级
 *       "level_rewards": { // 不同等级对应的奖励详情。key 是等级，对应的奖励的详情
 *         "45": { // 45 表示用户等级
 *           "lock": 0, // 是否解锁当前等级奖励，0 或不存在表示未锁定，1 表示锁定中
 *           "release_time": 1712564508, // 下次发放的时间，秒级时间戳，没有设置过生日的话为 0
 *           "title": "- 生日登录猫耳可享特权 -",
 *           "tip": "有效期：当天 00:00 - 24:00",
 *           "rewards": [
 *             {
 *               "name": "xx 头像框",
 *               "icon_url": "http://static.maoercdn.com/live/avatarframes/icons/40056.png"
 *             }
 *           ]
 *         },
 *         "85": { // 85 表示用户等级
 *           "lock": 0, // 是否解锁当前等级奖励，0 或不存在表示未锁定，1 表示锁定中
 *           "title": "本月直播消费满 10000 钻可获得",
 *           "tip": "有效期：自获得时起 15 天",
 *           "progress": { // 任务进度
 *             "current": 5000, // 当前已消费的钻石数量
 *             "target": 10000, // 目标消费的钻石数量
 *           },
 *           "rewards": [
 *             {
 *               "name": "xx 头像框",
 *               "icon_url": "http://static.maoercdn.com/live/avatarframes/icons/40056.png"
 *             }
 *           ]
 *         },
 *         "160": {
 *           "lock": 0, // 是否解锁当前等级奖励，0 或不存在表示未锁定，1 表示锁定中
 *           "title": "LV 160 等级专属外观",
 *           "tip": "有效期：永久",
 *           "rewards": [
 *             {
 *               "name": "xx 头像框",
 *               "icon_url": "https://static.maoercdn.com/live/avatarframes/icons/40262.webp"
 *             },
 *             {
 *               "name": "xx 气泡框",
 *               "icon_url": "http://static.maoercdn.com/live/bubbles/message/10118-icon.webp"
 *             },
 *             {
 *               "name": "xx 名片框",
 *               "icon_url": "http://static.maoercdn.com/live/cardframes/icons/30033.webp"
 *             },
 *             {
 *               "name": "xx 座驾",
 *               "icon_url": "http://static.maoercdn.com/live/vehicles/icons/20107.png"
 *             },
 *           ]
 *         }
 *       }
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRewardInfo(c *handler.Context) (handler.ActionResponse, error) {
	resp, err := newRewardInfoResp(c)
	if err != nil {
		return nil, err
	}
	err = resp.findAppearances()
	if err != nil {
		return nil, err
	}

	resp.buildLv45()
	resp.buildLv85()
	resp.buildLv160()
	return resp, nil
}

func newRewardInfoResp(c *handler.Context) (*rewardInfoResp, error) {
	resp := &rewardInfoResp{
		LevelRewards: make(map[string]*levelRewardInfo, 1),
	}

	var err error
	resp.user, err = liveuser.FindOneSimple(bson.M{"user_id": c.UserID()}, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	resp.UserLevel = usercommon.Level(resp.user.Contribution)

	return resp, nil
}

func (resp *rewardInfoResp) findAppearances() error {
	appearanceIDs := make([]int64, 0, 8) // 一共有 8 个外观
	appearanceIDs = append(appearanceIDs, appearance.UserLevelGte45RewardAppearanceIDs()...)
	appearanceIDs = append(appearanceIDs, appearance.UserLevelGte85RewardAppearanceIDs()...)
	appearanceIDs = append(appearanceIDs, appearance.UserLevelGte160RewardAppearanceIDs()...)

	appearances, err := appearance.Find(bson.M{"id": bson.M{"$in": appearanceIDs}}, nil)
	if err != nil {
		logger.WithField("appearance_ids", appearanceIDs).Error(err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// NOTICE: 此处不判断外观数量是否一致，具体使用的地方进行判断
	resp.appearanceMap = goutil.ToMap(appearances, "ID").(map[int64]*appearance.Appearance)
	return nil
}

// 计算 releaseTime 并进行闰年相关的特殊处理
func getReleaseTime(year int, month time.Month, day int, leapMethod int) time.Time {
	releaseTime := func() time.Time {
		return time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	}
	if goutil.IsLeapYear(year) || !(month == time.February && day == 29) {
		return releaseTime()
	}
	// FIXME: LeapYearLookForward 等常量名应该更加直观才对，避免歧义
	switch leapMethod {
	case user.LeapYearLookForward:
		// 算作提前一天 2 月 28 日
		day--
	case user.LeapYearDoNothing:
		// 算作下一次闰年的 2 月 29 日
		for !goutil.IsLeapYear(year) {
			year++
		}
	default:
		// 默认传入的日期参数等同于 LeapYearLookBackward，time.Date 会自动计算为 3 月 1 日
	}
	return releaseTime()
}

func lv45PrivReleaseTime(userID int64) (int64, error) {
	var birthday *time.Time
	err := user.Addendum{}.DB().Select("birthday").
		Where("id = ?", userID).Row().Scan(&birthday)
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return 0, nil
		}
		return 0, err
	}
	if birthday == nil {
		return 0, nil
	}
	now := goutil.TimeNow()
	year := now.Year()
	rewarded, err := livebirthdayprivrecord.IsRewarded(userID, year)
	if err != nil {
		return 0, err
	}
	if rewarded {
		// 今年已经发放过需要等待明年的发放
		releaseTime := getReleaseTime(year+1, birthday.Month(), birthday.Day(), user.LeapYearLookForward)
		return releaseTime.Unix(), nil
	}
	releaseTime := getReleaseTime(year, birthday.Month(), birthday.Day(), user.LeapYearLookForward)
	if releaseTime.Unix() < goutil.TimeNow().Unix() {
		// 已经过了今年的发放时间也需要等待明年的发放
		releaseTime = getReleaseTime(year+1, birthday.Month(), birthday.Day(), user.LeapYearLookForward)
	}
	return releaseTime.Unix(), nil
}

func (resp *rewardInfoResp) buildLv45() {
	appearanceIDs := appearance.UserLevelGte45RewardAppearanceIDs()
	rewards := make([]reward, 0, len(appearanceIDs))
	for _, aID := range appearanceIDs {
		a := resp.appearanceMap[aID]
		if a == nil {
			logger.WithField("appearance_id", aID).Error("等级权益外观缺失")
			continue
		}
		rewards = append(rewards, newRewardFromAppearance(a))
	}
	releaseTime, err := lv45PrivReleaseTime(resp.user.UserID())
	if err != nil {
		logger.WithField("user_id", resp.user.UserID()).Errorf("user birthday search error: %v", err)
		// PASS
	}
	resp.LevelRewards["45"] = &levelRewardInfo{
		Lock:        goutil.BoolToInt(resp.UserLevel < 45),
		ReleaseTime: &releaseTime,
		Title:       "- 生日登录猫耳可享特权 -",
		Tip:         "有效期：当天 00:00 - 24:00",
		Rewards:     rewards,
	}
}

func newRewardFromAppearance(a *appearance.Appearance) reward {
	var name string
	// NOTICE: 需要判断外观名称最后一个字符是否是半角字符，若是则添加空格；外观的 type name 总是中文，这里不需要判断
	if r := []rune(a.Name); len(r) > 0 && util.IsHalfWidthChar(r[len(r)-1]) {
		name = fmt.Sprintf("%s %s", a.Name, appearance.TypeName(a.Type))
	} else {
		name = fmt.Sprintf("%s%s", a.Name, appearance.TypeName(a.Type))
	}
	return reward{
		Name:    name,
		IconURL: storage.ParseSchemeURL(a.Icon),
	}
}

func (resp *rewardInfoResp) buildLv85() {
	appearanceIDs := appearance.UserLevelGte85RewardAppearanceIDs()
	rewards := make([]reward, 0, len(appearanceIDs))
	for _, aID := range appearanceIDs {
		a := resp.appearanceMap[aID]
		if a == nil {
			logger.WithField("appearance_id", aID).Error("等级权益外观缺失")
			continue
		}
		rewards = append(rewards, newRewardFromAppearance(a))
	}

	spend, err := liveuser.ConsumptionByLevelGte85InCurrentMonth(goutil.TimeNow(), resp.user.UserID())
	if err != nil {
		logger.WithField("user_id", resp.user.UserID()).Error(err)
		return
	}
	// 若用户在自然月内消费超过目标钻数量，始终展示目标钻数量
	if spend > targetSpendLv85 {
		spend = targetSpendLv85
	}

	resp.LevelRewards["85"] = &levelRewardInfo{
		Lock:  goutil.BoolToInt(resp.UserLevel < 85),
		Title: fmt.Sprintf("本月直播消费满 %d 钻可获得", targetSpendLv85),
		Tip:   "有效期：自获得时起 15 天",
		Progress: &progress{
			Current: spend,           // 单位：钻
			Target:  targetSpendLv85, // 单位：钻
		},
		Rewards: rewards,
	}
}

func (resp *rewardInfoResp) buildLv160() {
	appearanceIDs := appearance.UserLevelGte160RewardAppearanceIDs()
	rewards := make([]reward, 0, len(appearanceIDs))
	for _, aID := range appearanceIDs {
		a := resp.appearanceMap[aID]
		if a == nil {
			logger.WithField("appearance_id", aID).Error("等级权益外观缺失")
			continue
		}
		rewards = append(rewards, newRewardFromAppearance(a))
	}
	rewardInfo := &levelRewardInfo{
		Lock:    goutil.BoolToInt(resp.UserLevel < 85),
		Title:   "LV 160 等级专属外观",
		Tip:     "有效期：永久",
		Rewards: rewards,
	}
	resp.LevelRewards["160"] = rewardInfo
}
