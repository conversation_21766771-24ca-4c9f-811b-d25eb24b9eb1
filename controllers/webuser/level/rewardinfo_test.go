package level

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func setLv85RewardTestData() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceID40250AvatarFrameForLv85,
			"type": appearance.TypeAvatarFrame,
		},
		bson.M{
			"$set": bson.M{
				"name": "虹梦云端",
				"icon": "oss://live/avatarframe/40250.png",
			},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	_, err = appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceID10113MessageBubbleForLv85,
			"type": appearance.TypeMessageBubble,
		},
		bson.M{
			"$set": bson.M{
				"name": "虹梦云端 *",
				"icon": "oss://live/messagebubble/10113.png",
			},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

func setLv45RewardTestData() error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceIDBirthdayAvatarFrameForLv45,
			"type": appearance.TypeAvatarFrame,
		},
		bson.M{
			"$set": bson.M{
				"name": "限定寿星",
				"icon": "oss://live/avatarframes/40261.png",
			},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	_, err = appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceIDBirthdayBadgeForLv45,
			"type": appearance.TypeBadge,
		},
		bson.M{
			"$set": bson.M{
				"name": "限定寿星",
				"icon": "oss://live/badges/50227.png",
			},
		},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

func TestActionRewardInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(setLv85RewardTestData())
	require.NoError(setLv45RewardTestData())

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/level/reward-info", true, nil)
	ok, err := liveuser.Update(c.UserID(), bson.M{"contribution": usercommon.LevelStart[100]})
	require.NoError(err)
	require.True(ok)
	resp, err := ActionRewardInfo(c)
	require.NoError(err)
	require.NotNil(resp)
	response, ok := resp.(*rewardInfoResp)
	require.True(ok)
	assert.Equal(101, response.UserLevel)
	assert.NotNil(response.LevelRewards["85"])
	assert.NotNil(response.LevelRewards["160"])
}

func TestNewRewardInfoResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	err := setLv85RewardTestData()
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/level/reward-info", true, nil)
	resp, err := newRewardInfoResp(c)
	require.NoError(err)
	require.NotNil(resp)
	assert.Equal(101, resp.UserLevel)
}

func TestGetReleaseTime(t *testing.T) {
	assert := assert.New(t)

	// 测试不需要闰年处理的情况
	releaseTime := getReleaseTime(2024, time.February, 29, user.LeapYearLookForward)
	assert.Equal(time.Date(2024, time.February, 29, 0, 0, 0, 0, time.Local), releaseTime)
	// 测试提前处理
	releaseTime = getReleaseTime(2023, time.February, 29, user.LeapYearLookForward)
	assert.Equal(time.Date(2023, time.February, 28, 0, 0, 0, 0, time.Local), releaseTime)
	// 测试延后处理
	releaseTime = getReleaseTime(2023, time.February, 29, user.LeapYearLookBackward)
	assert.Equal(time.Date(2023, time.March, 1, 0, 0, 0, 0, time.Local), releaseTime)
	// 测试无闰年处理选项（下一次闰年才会处理）
	releaseTime = getReleaseTime(2025, time.February, 29, user.LeapYearDoNothing)
	assert.Equal(time.Date(2028, time.February, 29, 0, 0, 0, 0, time.Local), releaseTime)
}

func TestLv45PrivReleaseTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有录入 user_addendum 表的用户
	releaseTime, err := lv45PrivReleaseTime(114)
	assert.NoError(err)
	assert.EqualValues(0, releaseTime)
	// 测试没有填写生日的用户
	releaseTime, err = lv45PrivReleaseTime(1717)
	require.NoError(err)
	assert.EqualValues(0, releaseTime)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 1, 2, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	// 测试正常今年待发放的用户
	releaseTime, err = lv45PrivReleaseTime(1818)
	require.NoError(err)
	assert.EqualValues(2024, time.Unix(releaseTime, 0).Year())
	// 测试今年已发放过的用户
	releaseTime, err = lv45PrivReleaseTime(1919)
	require.NoError(err)
	assert.EqualValues(2025, time.Unix(releaseTime, 0).Year())
	// 测试今年已经过了发放时间的用户
	releaseTime, err = lv45PrivReleaseTime(1414)
	require.NoError(err)
	assert.EqualValues(2025, time.Unix(releaseTime, 0).Year())
}

func TestRewardInfoResp_buildLv45(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(setLv45RewardTestData())

	resp := &rewardInfoResp{
		LevelRewards: map[string]*levelRewardInfo{},
		user: &liveuser.Simple{
			UID: 1919,
		},
	}
	require.NoError(resp.findAppearances())
	resp.buildLv45()
	lv45 := resp.LevelRewards["45"]
	require.NotNil(lv45)
	assert.Equal(1, lv45.Lock)
	assert.EqualValues(goutil.TimeNow().Year()+1, time.Unix(*lv45.ReleaseTime, 0).Year())
	assert.Equal("- 生日登录猫耳可享特权 -", lv45.Title)
	assert.Equal("有效期：当天 00:00 - 24:00", lv45.Tip)
	require.Len(lv45.Rewards, 2)
	assert.Equal("限定寿星头像框", lv45.Rewards[0].Name)
	assert.Equal("https://static-test.missevan.com/live/avatarframes/40261.png", lv45.Rewards[0].IconURL)
	assert.Equal("限定寿星称号", lv45.Rewards[1].Name)
	assert.Equal("https://static-test.missevan.com/live/badges/50227.png", lv45.Rewards[1].IconURL)
}

func TestRewardInfoResp_buildLv85(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(100)
	)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 5, 1, 0, 0, 0, 0, time.UTC)
	})
	defer goutil.SetTimeNow(nil)
	err := setLv85RewardTestData()
	require.NoError(err)
	_, err = liveuser.AddConsumptionByLevelGte85InCurrentMonth(goutil.TimeNow(), testUserID, 10002)
	require.NoError(err)

	resp := &rewardInfoResp{
		LevelRewards: map[string]*levelRewardInfo{},
		user: &liveuser.Simple{
			UID: testUserID,
		},
	}
	require.NoError(resp.findAppearances())
	resp.buildLv85()
	lv85 := resp.LevelRewards["85"]
	require.NotNil(lv85)
	assert.Equal(1, lv85.Lock)
	assert.Equal("本月直播消费满 10000 钻可获得", lv85.Title)
	assert.Len(lv85.Rewards, 2)
	assert.EqualValues(targetSpendLv85, lv85.Progress.Current)
	require.Len(lv85.Rewards, 2)
	assert.Equal("虹梦云端头像框", lv85.Rewards[0].Name)
	assert.Equal("https://static-test.missevan.com/live/avatarframe/40250.png", lv85.Rewards[0].IconURL)
	assert.Equal("虹梦云端 * 气泡框", lv85.Rewards[1].Name)
	assert.Equal("https://static-test.missevan.com/live/messagebubble/10113.png", lv85.Rewards[1].IconURL)
}

func TestRewardInfoResp_buildLv160(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &rewardInfoResp{
		LevelRewards: map[string]*levelRewardInfo{},
		user: &liveuser.Simple{
			UID: 100,
		},
	}
	require.NoError(resp.findAppearances())
	resp.buildLv160()
	lv160 := resp.LevelRewards["160"]
	require.NotNil(lv160)
	assert.Equal("LV 160 等级专属外观", lv160.Title)
	assert.Equal("有效期：永久", lv160.Tip)
	appearanceIDs := appearance.UserLevelGte160RewardAppearanceIDs()
	assert.Equal(len(appearanceIDs), len(lv160.Rewards))
}
