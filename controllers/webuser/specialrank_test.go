package webuser

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionActivitySpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := keys.KeyUsersActivityRank1.Format(usersrank.ActivityJulyRebate)
	service.Redis.ZAdd(key, &redis.Z{Score: 250001, Member: 10})

	uri := fmt.Sprintf("/?activity_key=%s", usersrank.ActivityJulyRebate)
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	r, err := ActionActivitySpend(c)
	require.NoError(err)
	assert.Zero(r.(handler.M)["amount"])

	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	c.User().ID = 10
	r, err = ActionActivitySpend(c)
	require.NoError(err)
	assert.Equal(float64(250001), r.(handler.M)["amount"])

	c = handler.NewTestContext(http.MethodGet, "/", true, nil)
	_, err = ActionActivitySpend(c)
	assert.Equal(actionerrors.ErrParams, err)
}
