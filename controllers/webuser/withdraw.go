package webuser

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	"github.com/MiaoSiLa/live-service/models/mysql/withdrawalrecord"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

type withdrawParam struct {
	Type      string        `form:"type" json:"type" binding:"required"`
	Profit    util.Float2DP `form:"profit" json:"profit" binding:"required"`
	AccountID int64         `form:"account_id" json:"account_id" binding:"required"`
}

// ActionWithdraw 申请提现
/**
 * @api {post} /api/v2/user/withdraw 申请提现
 * @apiVersion 0.1.0
 * @apiName withdraw
 * @apiGroup /api/v2/user
 *
 * @apiParam {string=single,guild} type 提现的收益类型（single 素人主播收益，guild 公会主播收益）
 * @apiParam {String} profit 提现金额
 * @apiParam {Number} account_id 账户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "提现申请已提交"
 *     }
 *   }
 *
 * @apiError (400) {Number} code *********
 * @apiError (400) {String} info 参数错误
 */
func ActionWithdraw(c *handler.Context) (handler.ActionResponse, error) {
	var param withdrawParam
	err := c.Bind(&param)
	if err != nil || param.Profit <= 0 || param.AccountID <= 0 || (param.Type != creatorTypeSingle && param.Type != creatorTypeGuild) {
		return nil, actionerrors.ErrParams
	}
	var accountInfo accountinfo.AccountInfo
	err = accountInfo.DB().Take(&accountInfo, "id = ?", param.AccountID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.NewErrForbidden("提现信息不存在")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	creatorID := c.UserID()
	rp := new(revenueResp)
	if err = rp.loadEntranceStatus(); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if rp.EntranceStatus == entranceStatusNotWithdrawTime {
		return nil, actionerrors.NewErrForbidden("每月仅 1 ~ 3 号开放提现申请入口")
	}
	if err = rp.loadBankInfo(creatorID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if rp.AccountID != param.AccountID || rp.BankInfoStatus != bankInfoStatusCorrect {
		return nil, actionerrors.NewErrForbidden("您的提现信息有误，请修改后再提现！")
	}
	if _, err = rp.loadCreatorType(creatorID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err = rp.loadWithdrawLimit(creatorID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var lr *creatorRevenue
	if param.Type == creatorTypeGuild {
		lr, err = getGuildCreatorRevenue(creatorID)
	} else {
		lr, err = getSingleCreatorRevenue(creatorID)
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if lr.DrawableLiveProfit < param.Profit {
		return nil, actionerrors.NewErrForbidden("提现金额不得大于可提现余额")
	}
	if rp.MinWithdrawalLimit > param.Profit {
		return nil, actionerrors.NewErrForbidden(fmt.Sprintf("提现金额最少为 %.2f 元", rp.MinWithdrawalLimit))
	}
	if rp.MaxWithdrawalLimit < param.Profit {
		return nil, actionerrors.NewErrForbidden(fmt.Sprintf("提现金额最大为 %.2f 元", rp.MaxWithdrawalLimit))
	}
	if rp.RemainingWithdrawalLimit < param.Profit {
		return nil, actionerrors.NewErrForbidden("剩余可提现额度不足")
	}
	var res map[string]interface{}
	var withdrawType int
	if param.Type == creatorTypeSingle {
		withdrawType = withdrawalrecord.TypeWithDrawLiveNew
	} else {
		return nil, actionerrors.NewErrForbidden("公会主播暂不支持自提")
		// withdrawType = withdrawalrecord.TypeWithDrawGuildCreator
	}
	err = service.MRPC.Call("app://financial/withdrawal", c.ClientIP(), map[string]interface{}{
		"user_id":       creatorID,
		"account_id":    param.AccountID,
		"price":         param.Profit,
		"withdraw_type": withdrawType,
	}, &res)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return handler.M{
		"msg": "提现申请已提交",
	}, nil
}
