package webuser

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalremoverecord"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type medalListResp struct {
	Data             []*livemedal.LiveMedal `json:"data"`
	Pagination       goutil.Pagination      `json:"pagination"`
	Rule             string                 `json:"rule"`
	MedalLimit       int                    `json:"medal_limit"`
	MaxLevel         int                    `json:"max_level"`
	NormalMedalCount *int64                 `json:"normal_medal_count,omitempty"`
	SuperMedalCount  *int64                 `json:"super_medal_count,omitempty"`
}

const (
	typeAllListMedal   = iota // 全部勋章
	typeSuperListMedal        // 超粉勋章
)

// ActionMedalList 列出用户拥有的勋章
/**
 * @api {get} /api/v2/user/medal/list 用户勋章列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=0,1} [type=0] 0: 全部勋章; 1: 超粉勋章
 * @apiParam {Number} [p=1] 页码
 * @apiParam {Number} [pagesize=20] 每页大小
 *
 * @apiSuccessExample:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "room_id": 123,
 *           "creator_id": 12,
 *           "name": "勋章名称",
 *           "user_id": 1234,
 *           "status": 2, // 勋章状态，1：拥有的，2：佩戴的
 *           "point": 1000,
 *           "level": 6,
 *           "level_up_point": 1000,
 *           "creator_username": "主播昵称",
 *           "creator_iconurl": "http://static.example.com/avatars/icon01.png",
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700, // 过期时间，秒级时间戳
 *             "register_time": 1576116700, // 开通时间，秒级时间戳
 *             "days": 5 // 开通天数
 *           },
 *           "today_threshold": 500,
 *           "today_point": 0,
 *           "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png",
 *           "live_status": 1 // 开播状态: 1 开播；0 关播
 *         }
 *       ],
 *       "pagination": {
 *         "p": 1,
 *         "pagesize": 20,
 *         "count": 1,
 *         "maxpage": 1
 *       },
 *       "rule": "https://link.uat.missevan.com/fm/fans-system-guide",
 *       "medal_limit": 20, // 勋章数量限制
 *       "max_level": 40, // 勋章等级限制
 *       "normal_medal_count": 20, // 普通粉丝勋章数量，仅在查询全部勋章第一页的时候返回
 *       "super_medal_count": 2 // 超粉勋章数量，仅在查询全部勋章第一页的时候返回
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalList(c *handler.Context) (handler.ActionResponse, error) {
	medalType, _ := c.GetParamInt("type")
	if medalType < typeAllListMedal || medalType > typeSuperListMedal {
		return nil, actionerrors.ErrParams
	}
	p, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	resp := &medalListResp{
		Rule:       config.Conf.Params.MedalParams.FanRule,
		MedalLimit: int(livemedal.UserMaxMedalCount()),
		MaxLevel:   livemedal.UserMedalLevelLimit(),
	}
	uv, err := vip.UserActivatedVip(c.UserID(), false, c)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if uv != nil && uv.Info.MedalNum > resp.MedalLimit {
		resp.MedalLimit = uv.Info.MedalNum
	}

	filter := bson.M{
		"user_id": c.UserID(),
		"status":  bson.M{"$gt": livemedal.StatusPending},
	}
	if medalType == typeSuperListMedal {
		filter["super_fan.expire_time"] = bson.M{"$gt": goutil.TimeNow().Unix()}
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	count, err := livemedal.Collection().CountDocuments(ctx, filter)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Pagination = goutil.MakePagination(count, p, pageSize)
	if !resp.Pagination.Valid() {
		if medalType == typeAllListMedal && p == 1 {
			resp.NormalMedalCount = util.NewInt64(0)
			resp.SuperMedalCount = util.NewInt64(0)
		}
		resp.Data = make([]*livemedal.LiveMedal, 0)
		return resp, nil
	}
	mongoOpt := resp.Pagination.SetFindOptions(nil)
	switch medalType {
	case typeAllListMedal:
		mongoOpt.SetSort(bson.D{bson.E{Key: "status", Value: -1}, bson.E{Key: "point", Value: -1}})
		if p == 1 {
			// 只有第一页，才会返回勋章总数
			normalCount, err := livemedal.CountNormalMedal(c.UserID())
			if err != nil {
				logger.Error(err)
				// PASS
			}
			resp.NormalMedalCount = util.NewInt64(normalCount)
			superCount, err := livemedal.CountSuperMedal(c.UserID())
			if err != nil {
				logger.Error(err)
				// PASS
			}
			resp.SuperMedalCount = util.NewInt64(superCount)
		}
	case typeSuperListMedal:
		mongoOpt.SetSort(bson.M{"super_fan.expire_time": 1})
	}
	liveMedals, err := livemedal.List(filter, mongoOpt, &livemedal.FindOptions{FindCreator: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.buildData(c.UserID(), liveMedals)

	return resp, nil
}

func (m *medalListResp) buildData(userID int64, liveMedals []*livemedal.LiveMedal) {
	allCreatorIDs := make([]int64, 0, len(liveMedals))
	superFanCreatorIDs := make([]int64, 0, len(liveMedals))
	for _, v := range liveMedals {
		allCreatorIDs = append(allCreatorIDs, v.CreatorID)
		if v.SuperFan == nil {
			continue
		}
		superFanCreatorIDs = append(superFanCreatorIDs, v.CreatorID)
	}

	roomSimpleMap, err := room.FindSimpleMapByCreatorID(allCreatorIDs, &room.FindOptions{DisableAll: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}

	var orderMap map[int64]livetxnorder.LiveTxnOrder
	if len(superFanCreatorIDs) != 0 {
		orderMap, err = livetxnorder.FindLatestSuperFanRegisterMap(userID, superFanCreatorIDs)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	for _, v := range liveMedals {
		if roomSimpleMap != nil {
			roomSimple, ok := roomSimpleMap[v.CreatorID]
			if ok {
				v.LiveStatus = util.NewInt(roomSimple.Status.Open)
			} else {
				v.LiveStatus = util.NewInt(room.StatusOpenFalse)
			}
		} else {
			v.LiveStatus = util.NewInt(room.StatusOpenFalse)
		}

		if v.SuperFan == nil || orderMap == nil {
			continue
		}
		order, ok := orderMap[v.CreatorID]
		if !ok {
			continue
		}
		v.BuildSuperFan(order.CreateTime)
	}
	m.Data = liveMedals
}

// ActionMedalWear 佩戴勋章
/**
 * @api {post} /api/v2/user/medal/wear 用户佩戴勋章
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} creator_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "佩戴成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalWear(c *handler.Context) (handler.ActionResponse, error) {
	creatorID, err := c.GetParamInt64("creator_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	success, err := livemedal.Wear(c.UserID(), creatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !success {
		return nil, actionerrors.ErrParams
	}
	return "佩戴成功", nil
}

// ActionMedalTakeoff 卸下勋章
/**
 * @api {post} /api/v2/user/medal/takeoff 用户卸下勋章
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} creator_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "卸下成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalTakeoff(c *handler.Context) (handler.ActionResponse, error) {
	creatorID, err := c.GetParamInt64("creator_id")
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	success, err := livemedal.TakeOff(c.UserID(), creatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !success {
		return nil, actionerrors.ErrParams
	}
	return "卸下成功", nil
}

// ActionMedalRemove 删除勋章
/**
 * @api {post} /api/v2/user/medal/remove 用户删除勋章
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} creator_id 主播 id
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (403) {Number} code 500030025
 * @apiError (403) {String} info 无法删除超级粉丝勋章哦
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalRemove(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		CreatorID int64 `form:"creator_id" json:"creator_id"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	lm, err := livemedal.FindOne(bson.M{"creator_id": param.CreatorID, "user_id": c.UserID()}, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if lm == nil {
		return nil, actionerrors.ErrCannotFindResource
	}
	isSuperFanActive := livemedal.IsSuperFanActive(lm.SuperFan)
	if e := c.Equip(); e.IsAppOlderThan("4.9.0", "5.7.5") && isSuperFanActive {
		// WORKAROUND: 仅 iOS < 4.9.0, 安卓 < 5.7.5 的版本需要限制删除超粉勋章
		return nil, actionerrors.ErrCannotRemoveSuperMedal
	}
	// REVIEW: 是否要禁止删除佩戴中的勋章
	success, err := livemedal.Remove(c.User().ID, param.CreatorID, lm.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !success {
		return nil, actionerrors.ErrParams
	}
	if isSuperFanActive {
		err := livetxnorder.SetSuperFanOrderExpired([]int64{c.UserID()}, param.CreatorID)
		if err != nil {
			logger.WithFields(logger.Fields{"user_id": c.UserID(), "creator_id": param.CreatorID}).Error(err)
			// PASS
		}
	}
	if err = livemedalremoverecord.AddRemoveRecords(livemedalremoverecord.TypeRemoveUser, lm); err != nil {
		logger.WithFields(logger.Fields{"room_id": lm.RoomID, "user_id": c.UserID()}).Error(err)
		// PASS
	}
	logger.WithFields(logger.Fields{"room_id": lm.RoomID, "user_id": c.UserID()}).Info("用户删除勋章")
	return "删除成功", nil
}

type medalGetResp struct {
	Medal      livemedal.From `json:"medal"`
	Rule       string         `json:"rule"`
	OwnerCount int64          `json:"owner_count"`
}

// ActionMedalGet 通过参数获取指定勋章信息
/**
 * @api {get} /api/v2/user/medal/get 通过参数获取指定勋章信息
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {Number} [creator_id] 主播 id
 * @apiParam {String} [name] 勋章名称
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "medal": {
 *         "name": "123"
 *         "creator_id": 12,
 *         "creator_username": "test",
 *         "creator_iconurl": "https://static.example.com/avatars/icon01.png",
 *         "room_id": 123456
 *       }，
 *       "rule": "https://link.uat.missevan.com/fm/fans-system-guide",
 *       "owner_count": 10 // 持有当前勋章的总人数
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionMedalGet(c *handler.Context) (handler.ActionResponse, error) {
	name, ok := c.GetParam("name")
	creatorID, _ := c.GetParamInt64("creator_id")
	if creatorID == 0 && !ok {
		return nil, actionerrors.ErrParams
	}
	filter := make(bson.M, 1)
	switch {
	case ok:
		filter["medal.name"] = name
	case creatorID != 0:
		filter["creator_id"] = creatorID
	}
	opts := options.FindOne().SetProjection(bson.M{"medal": 1, "creator_id": 1, "room_id": 1})
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	var findRes room.Room
	err := collection.FindOne(ctx, filter, opts).Decode(&findRes)
	if err != nil {
		if mongodb.IsNoDocumentsError(err) {
			return nil, actionerrors.ErrNotFound("无法找到该勋章")
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if findRes.Medal == nil {
		return nil, actionerrors.ErrNotFound("无法找到该勋章")
	}

	collection = service.MongoDB.Collection(livemedal.CollectionName)
	count, err := collection.CountDocuments(ctx, bson.M{
		"room_id": findRes.RoomID,
		"status":  bson.M{"$gt": livemedal.StatusPending},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	creator, err := mowangskuser.FindByUserID(findRes.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if creator == nil {
		logger.WithField("creator_id", findRes.CreatorID).Error("find room creator failed")
		return nil, actionerrors.ErrCannotFindUser
	}

	return &medalGetResp{
		Medal: livemedal.From{
			Name:            findRes.Medal.Name,
			CreatorID:       findRes.CreatorID,
			CreatorUsername: creator.Username,
			CreatorIconURL:  creator.IconURL,
			RoomID:          findRes.RoomID,
		},
		Rule:       config.Conf.Params.MedalParams.FanRule,
		OwnerCount: count,
	}, nil
}
