package webuser

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func createLiveIncomeTestData() (func(), error) {
	tradelogList := []*transactionlog.TransactionLog{
		{
			FromID:       3,
			GiftID:       1,
			Title:        "素人普通礼物-1",
			IOSCoin:      100,
			AllCoin:      100,
			Income:       10,
			Tax:          5,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrCommon,
		},
		{
			FromID:       5,
			GiftID:       0,
			Title:        "素人付费问答-1",
			IOSCoin:      500,
			AllCoin:      500,
			Income:       50,
			Tax:          25,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrCommon,
		},
		{
			FromID:       5,
			GiftID:       0,
			Title:        "素人付费弹幕-1",
			IOSCoin:      800,
			AllCoin:      800,
			Income:       80,
			Tax:          40,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrLiveDanmaku,
		},
		{
			FromID:       5,
			ToID:         88888888,
			GiftID:       666,
			Title:        "素人贵族开通-1",
			IOSCoin:      1000,
			AllCoin:      1000,
			Income:       100,
			Tax:          50,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrLiveRegisterNoble,
		},
		{
			FromID:       5,
			ToID:         88888888,
			GiftID:       999,
			Title:        "素人超粉开通-1",
			IOSCoin:      2000,
			AllCoin:      2000,
			Income:       200,
			Tax:          100,
			Type:         transactionlog.TypeLive,
			SubordersNum: 0,
			Attr:         transactionlog.AttrLiveRegisterSuperFan,
		},
		{
			FromID:       6,
			GiftID:       1,
			Title:        "公会普通礼物-1",
			IOSCoin:      200,
			AllCoin:      200,
			Income:       20,
			Tax:          10,
			Type:         transactionlog.TypeGuildLive,
			SubordersNum: 999,
			Attr:         transactionlog.AttrCommon,
		},
	}
	tradeIDs := make([]int64, len(tradelogList))
	for _, tradelog := range tradelogList {
		tradelog.ToID = 88888888
		tradelog.Status = transactionlog.StatusSuccess
		tradelog.Num = 1
		tradelog.Rate = 0.5
		tradelog.CTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()
		tradelog.CreateTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()
		tradelog.ModifiedTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()
		tradelog.ConfirmTime = time.Date(2024, 2, 16, 4, 0, 0, 0, time.Local).Unix()

		err := transactionlog.DB().Create(&tradelog).Error
		if err != nil {
			return nil, err
		}
		tradeIDs = append(tradeIDs, tradelog.ID)
	}

	return func() {
		transactionlog.DB().Delete("", "id IN (?)", tradeIDs)
	}, nil
}

func TestActionLiveIncome(t *testing.T) {
	require := require.New(t)

	cleanup, err := createLiveIncomeTestData()
	require.NoError(err)
	defer cleanup()

	ctx := handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/user/liveincome?start_date=%s&end_date=%s&type=%d",
			"2024-02-15", "2024-02-20", utils.IncomeTypeGift,
		),
		true,
		nil)
	ctx.User().ID = 88888888
	resp, err := ActionLiveIncome(ctx)
	require.NoError(err)
	listResp, ok := resp.(*utils.LiveIncomeListResp)
	require.True(ok)
	require.Equal(util.Float2DP(2), *listResp.Total)
	require.Len(listResp.Data, 1)

	ctx = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/user/liveincome?start_date=%s&end_date=%s&type=%d",
			"2024-02-15", "2024-02-20", utils.IncomeTypeNoble,
		),
		true,
		nil)
	ctx.User().ID = 88888888
	resp, err = ActionLiveIncome(ctx)
	require.NoError(err)
	listResp, ok = resp.(*utils.LiveIncomeListResp)
	require.True(ok)
	require.Equal(util.Float2DP(25), *listResp.Total)
	require.Len(listResp.Data, 1)

	ctx = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/user/liveincome?start_date=%s&end_date=%s&type=%d",
			"2024-02-15", "2024-02-20", utils.IncomeTypeSuperFan,
		),
		true,
		nil)
	ctx.User().ID = 88888888
	resp, err = ActionLiveIncome(ctx)
	require.NoError(err)
	listResp, ok = resp.(*utils.LiveIncomeListResp)
	require.True(ok)
	require.Equal(util.Float2DP(50), *listResp.Total)
	require.Len(listResp.Data, 1)

	ctx = handler.NewTestContext(http.MethodGet,
		fmt.Sprintf("/api/v2/user/liveincome?start_date=%s&end_date=%s&type=%d",
			"2024-02-15", "2024-02-20", utils.IncomeTypePlay,
		),
		true,
		nil)
	ctx.User().ID = 88888888
	resp, err = ActionLiveIncome(ctx)
	require.NoError(err)
	listResp, ok = resp.(*utils.LiveIncomeListResp)
	require.True(ok)
	require.Equal(util.Float2DP(32), *listResp.Total)
	require.Len(listResp.Data, 2)
}
