package webuser

import (
	"net/http"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var (
	user3456835 *sso.ClientResponse
	user12      *sso.ClientResponse
	userInit    sync.Once
)

func getUser() {
	userInit.Do(func() {
		var err error
		user12, err = service.SSO.LoginByThirdOpenID(sso.ThirdTypeQQ, "testqquid", 0,
			"", testUA, "")
		if err != nil {
			logger.Fatal(err)
		}
		user3456835, err = service.SSO.LoginByEmail("<EMAIL>", "a.123456", 0,
			"", testUA, "")
		if err != nil {
			logger.Fatal(err)
		}
	})
}

func TestStatusTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(statusGetResp{}, "backpack", "new_appearance", "medal_status", "medal_discount", "medal", "user_level", "preset_messages", "user_black_card", "horn_list")
	kc.Check(userBlackCard{}, "level", "title")
}

func TestConst(t *testing.T) {
	assert := assert.New(t)

	assert.Equal(0, unlock)
	assert.Equal(1, lock)
}

func TestActionSettingsSetGeneral(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	t.Run("checkParams", func(t *testing.T) {
		c := handler.CreateTestContext(true)
		c.C.Request, _ = http.NewRequest("POST", "/set-general", nil)
		_, err := ActionSettingsSetGeneral(c)
		assert.Equal(actionerrors.ErrParams, err)
	})
	t.Run("ErrNoAuthority", func(t *testing.T) {
		// 隐身
		param := map[string]int{"invisible": 1}
		c := handler.NewTestContext(http.MethodPost, "/set-general", true, param)
		c.User().ID = 10
		_, err := ActionSettingsSetGeneral(c)
		assert.Equal(actionerrors.ErrNoAuthority, err)
	})
	t.Run("updateSuccess", func(t *testing.T) {
		userID := noble7UserID
		// 隐身
		param := map[string]int{"invisible": 0}
		c := handler.CreateTestContext(true)
		c.C.Request, _ = http.NewRequest("POST", "/set-general", tutil.ToRequestBody(param))
		c.C.Request.Header.Set("Content-Type", "application/json")
		c.User().ID = userID
		_, err := ActionSettingsSetGeneral(c)
		require.NoError(err)

		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		var settings userstatus.GeneralStatus
		err = userstatus.UserMetaCollection().FindOne(ctx, bson.M{"user_id": userID},
			options.FindOne().SetProjection(bson.M{"bubble": 1, "invisible": 1})).
			Decode(&settings)
		require.NoError(err)
		assert.True(settings.Invisible != nil && !*settings.Invisible)
	})
}

func TestActionStatusGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	key := keys.KeyParams1.Format(params.KeyBubble)
	err := service.LRURedis.Del(key).Err()
	require.NoError(err)

	b := bubble.Bubble{BubbleID: 84, Image: "oss://test.png"}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = service.MongoDB.Collection("bubbles").UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)

	b = bubble.Bubble{BubbleID: 91, Image: "oss://test2.png"}
	_, err = service.MongoDB.Collection("bubbles").UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/status/get", true, nil)
	r, err := ActionStatusGet(c)
	require.NoError(err)

	// 检查新外观状态是否正确
	require.NotNil(r)
	resp := r.(*statusGetResp)
	assert.True(resp.NewAppearance)

	// 勋章状态
	require.Equal(medalStatusWearing, resp.MedalStatus)
	assert.NotNil(resp.Medal)

	// 获取黑卡状态
	require.NotNil(resp.UserBlackCard)
	assert.Equal(3, resp.UserBlackCard.Level)
	assert.Equal("星曜 V3", resp.UserBlackCard.Title)
	// 获取喇叭状态
	require.NotNil(resp.HornList)
	assert.Equal(2, len(resp.HornList))
	expectedNobleHorn := hornItem{
		Type:         userstatus.HornTypeNoble,
		Lock:         1,
		Title:        "贵族全站喇叭",
		IconURL:      "https://static-test.missevan.com/live/bubbles/notify/icons/84.png",
		Num:          0,
		ExpireTime:   0,
		Intro:        "开通或续费大咖及以上贵族可获得",
		IntroOpenURL: "",
		LabelIconURL: "",
	}
	expectedBlackCardHorn := hornItem{
		Type:         userstatus.HornTypeBlackCard,
		Lock:         0,
		Title:        "星曜 V3 全站喇叭",
		IconURL:      "https://static-test.missevan.com/live/bubbles/notify/icons/blackcard3.png",
		Num:          0,
		ExpireTime:   1999999999,
		Intro:        "参与【星曜活动】可获得",
		IntroOpenURL: "https://www.missevan.com/mevent/9000?from_room_id=__ROOM_ID__&navhide=1&type=1",
		LabelIconURL: "",
	}
	expectedHornList := []hornItem{
		expectedBlackCardHorn,
		expectedNobleHorn,
	}
	assert.Equal(expectedHornList, resp.HornList)
}

func TestNewUserLevel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return &vip.UserVipsResp{}, nil
	})
	defer cancel()

	userLevelInfo, err := newUserLevel(int64(12), nil)
	require.NoError(err)
	require.NotNil(userLevelInfo)
	assert.NotZero(userLevelInfo.Exp)
	assert.NotZero(userLevelInfo.Level)
	assert.NotZero(userLevelInfo.LevelUpCoin)
	assert.NotZero(userLevelInfo.LevelUpExp)
}

func TestGetPresetMessage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	messages, err := getPresetMessage()
	require.NoError(err)
	// 返回的消息数量等于 10 条
	assert.Equal(len(messages), 10)
}

func TestSortHornList(t *testing.T) {
	assert := assert.New(t)

	nobleHorn := hornItem{
		Type:         userstatus.HornTypeNoble,
		Lock:         1,
		Title:        "贵族",
		IconURL:      "https://static-test.missevan.com/avatars/noble/noble.png",
		Num:          0,
		ExpireTime:   0,
		Intro:        "开通或续费大咖及以上贵族可获得",
		IntroOpenURL: "",
		LabelIconURL: "",
	}
	blackCardHorn := hornItem{
		Type:         userstatus.HornTypeBlackCard,
		Lock:         0,
		Title:        "星曜 V3",
		IconURL:      "https://static-test.missevan.com/live/stickers/package/black_card.png",
		Num:          0,
		ExpireTime:   1999999999,
		Intro:        "参与【星曜活动】可获得",
		IntroOpenURL: "https://www.missevan.com/mevent/9000?type=1",
		LabelIconURL: "",
	}
	expectedHornList := []hornItem{blackCardHorn, nobleHorn}
	assert.Equal(expectedHornList, sortHornItems(nobleHorn, blackCardHorn))

	nobleHorn.Num = 2
	expectedHornList = []hornItem{nobleHorn, blackCardHorn}
	assert.Equal(expectedHornList, sortHornItems(nobleHorn, blackCardHorn))
}
