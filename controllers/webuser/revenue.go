package webuser

import (
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	"github.com/MiaoSiLa/live-service/models/mysql/certification"
	"github.com/MiaoSiLa/live-service/models/mysql/withdrawalrecord"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/balance"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// 月提现总额一、二级限制（单位：元）
	maxWithdrawValueLevelOne util.Float2DP = 10e4
	maxWithdrawValueLevelTwo util.Float2DP = 40e4
	// 默认的月提现最小金额（单位：元）
	minWithdrawValueDefault util.Float2DP = 100

	// 主播类型：素人主播、公会主播
	creatorTypeSingle = "single"
	creatorTypeGuild  = "guild"
)

const (
	bankInfoStatusError = iota - 1
	bankInfoStatusEmpty
	bankInfoStatusCorrect
)
const (
	entranceStatusNotWithdrawTime = iota - 2
	_
	entranceStatusCanWithdraw
)

type revenueResp struct {
	EntranceStatus int `json:"entrance_status"`
	BankInfoStatus int `json:"bank_info_status"`

	AccountID   int64  `json:"account_id"`
	RealName    string `json:"real_name"`
	IDNumber    string `json:"id_number"`
	Mobile      string `json:"mobile"`
	Bank        string `json:"bank"`
	BankAccount string `json:"bank_account"`

	MinWithdrawalLimit       util.Float2DP `json:"min_withdrawal_limit"`
	MaxWithdrawalLimit       util.Float2DP `json:"max_withdrawal_limit"`
	RemainingWithdrawalLimit util.Float2DP `json:"remaining_withdrawal_limit"`

	CreatorType string `json:"creator_type"`

	SingleCreatorRevenue *creatorRevenue `json:"single"`
	GuildCreatorRevenue  *creatorRevenue `json:"guild"`
}

type creatorRevenue struct {
	DrawableLiveProfit util.Float2DP `json:"drawable_live_profit"`
	LiveProfit         util.Float2DP `json:"live_profit"`
	FrozenLiveProfit   util.Float2DP `json:"frozen_live_profit"`
}

// ActionRevenue 主播收益
/**
* @api {get} /api/v2/user/revenue 主播收益
* @apiVersion 0.1.0
* @apiGroup /api/v2/user
*
* @apiSuccess {Number} code
* @apiSuccess {Object} info
*
* @apiSuccessExample Success-Response:
*     {
*       "code": 0,
*       "info": {
*         "account_id": 4210,  // 提现账户 ID
*         "id_number": "35***************9",  // 打码的身份证号
*         "real_name": "习**",  // 打码的真实姓名
*         "mobile": "187******18",  // 打码的手机号
*         "bank_account": "**** **** **** ***0 018",  // 打码的银行卡号
*         "bank": "中国农业银行",  // 开启行
*         "entrance_status": -2,  // -2 未到可提现时间的直播收益，0 可进行提现操作
*         "bank_info_status": -1,  // 银行账户状态 -1 有误，0 为空，1 正确
*         "creator_type": "single",  // 当前主播的身份：single 素人主播，guild 公会主播
*         "min_withdrawal_limit": 100,  // 最低提现额度限制
*         "max_withdrawal_limit": 100000,  // 最高提现额度限制
*         "remaining_withdrawal_limit": 9988.02,  // 剩余可提现额度
*         "single": {  // 素人主播收益
*           "drawable_live_profit": 728.01,  // 当前可提现余额
*           "live_profit": 820.01,  // 全部可提现余额
*           "frozen_live_profit": 820.01  // 冻结余额
*         },
*         "guild": {  // 公会主播收益
*           "drawable_live_profit": 728.01,
*           "live_profit": 820.01,
*           "frozen_live_profit": 820.01
*         }
*       }
*     }
 */
func ActionRevenue(c *handler.Context) (handler.ActionResponse, error) {
	var err error
	creatorID := c.UserID()
	resp := new(revenueResp)

	if err = resp.loadEntranceStatus(); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if err = resp.loadBankInfo(creatorID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if resp.BankInfoStatus == bankInfoStatusEmpty {
		cert, err := certification.Info(creatorID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if cert == nil {
			return nil, actionerrors.NewErrForbidden("请先进行实名认证")
		}

		roomID, err := live.FindRoomID(creatorID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if roomID == 0 {
			return nil, actionerrors.NewErrForbidden("您还不是主播哦")
		}

		resp.RealName = cert.MosaicRealName()
		resp.IDNumber = cert.MosaicIDNumber()
	}

	if _, err = resp.loadCreatorType(creatorID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if err = resp.loadWithdrawLimit(creatorID); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp.GuildCreatorRevenue, err = getGuildCreatorRevenue(creatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.SingleCreatorRevenue, err = getSingleCreatorRevenue(creatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return resp, nil
}

func getSingleCreatorRevenue(creatorID int64) (cr *creatorRevenue, err error) {
	cr = new(creatorRevenue)
	var creatorBalance balance.Balance
	err = creatorBalance.DB().
		Select("id, live_profit, new_live_profit").
		First(&creatorBalance, "id = ?", creatorID).Error
	if err != nil {
		if gorm.IsRecordNotFoundError(err) {
			err = nil
		}
		return
	}
	if creatorBalance.ID == 0 || (creatorBalance.LiveProfit == 0 && creatorBalance.NewLiveProfit == 0) {
		return
	}
	// 全部可提现余额（截止到当前时间点未提现）
	cr.LiveProfit = util.Float2DP(creatorBalance.NewLiveProfit)
	// 冻结余额（2020.06.01 00:00:00 前未提现部分）
	cr.FrozenLiveProfit = util.Float2DP(creatorBalance.LiveProfit)
	// 当前可提现余额（截止到上月最后一天 23:59:59 未提现部分）
	cr.DrawableLiveProfit = cr.LiveProfit
	if cr.DrawableLiveProfit > 0 {
		now := goutil.TimeNow()
		y, m, _ := now.Date()
		nowStampFirstDayOfThisMonth := time.Date(y, m, 1, 0, 0, 0, 0, now.Location()).Unix()
		// 单位为分（统计本月的收益）
		var revenueThisMonth util.Float2DP
		err = transactionlog.ADB().
			Select("IFNULL(SUM(FLOOR(ROUND((income - tax) * rate * 1000) / 10)), 0) AS revenue").
			Where("to_id = ? AND type = ? AND status = ?", creatorID, transactionlog.TypeLive, transactionlog.StatusSuccess).
			Where("confirm_time BETWEEN ? AND ?", nowStampFirstDayOfThisMonth, now.Unix()).
			Where("attr IN (?)", transactionlog.AllRevenueAttrs()).
			Row().Scan(&revenueThisMonth)
		if err != nil && !servicedb.IsErrNoRows(err) {
			return
		}
		if revenueThisMonth > 0 {
			cr.DrawableLiveProfit -= revenueThisMonth
			if cr.DrawableLiveProfit < 0 {
				cr.DrawableLiveProfit = 0
			}
		}
	}
	// 分转换为元单位
	cr.LiveProfit /= 100
	cr.DrawableLiveProfit /= 100
	cr.FrozenLiveProfit /= 100

	return
}

func getGuildCreatorRevenue(creatorID int64) (cr *creatorRevenue, err error) {
	cr = new(creatorRevenue)
	// TODO: 在公会奖励金需求中实现（公会主播变为对私结算）
	return
}

func (rr *revenueResp) loadBankInfo(creatorID int64) (err error) {
	var bankAccount accountinfo.AccountInfo
	err = bankAccount.DB().
		Where("user_id = ? AND type = ?", creatorID, accountinfo.TypeBank).
		Order("modified_time DESC, id DESC").
		First(&bankAccount).Error
	if err != nil {
		if !gorm.IsRecordNotFoundError(err) {
			return
		}
		rr.BankInfoStatus = bankInfoStatusEmpty
	}
	if bankAccount.ID > 0 {
		rr.BankInfoStatus = bankInfoStatusCorrect

		rr.AccountID = bankAccount.ID
		rr.Bank = bankAccount.Bank
		rr.IDNumber = bankAccount.MosaicIDNumber()
		rr.RealName = bankAccount.MosaicRealName()
		rr.BankAccount = bankAccount.MosaicBankAccount()
		rr.Mobile = bankAccount.MosaicMobile()
	}
	isBankInfoWrong, err := service.Redis.SIsMember(keys.KeyUserIDsWrongBankInfo0.Format(), creatorID).Result()
	if err != nil {
		return
	}
	if isBankInfoWrong {
		rr.BankInfoStatus = bankInfoStatusError
	}

	return
}

func (rr *revenueResp) loadCreatorType(creatorID int64) (guildID int64, err error) {
	guildID, err = livecontract.GetGuildID(creatorID)
	if err != nil {
		return
	}
	if guildID != 0 {
		rr.CreatorType = creatorTypeGuild
	} else {
		rr.CreatorType = creatorTypeSingle
	}

	return
}

func (rr *revenueResp) loadEntranceStatus() error {
	if isWithinWithdrawTime(goutil.TimeNow()) {
		rr.EntranceStatus = entranceStatusCanWithdraw
	} else {
		rr.EntranceStatus = entranceStatusNotWithdrawTime
	}

	return nil
}

func (rr *revenueResp) loadWithdrawLimit(creatorID int64) (err error) {
	minWithDrawValueStr, err := service.Redis.Get(keys.KeyMinWithdrawValueLimit0.Format()).Result()
	if err != nil && !serviceredis.IsRedisNil(err) {
		return
	}
	if minWithDrawValueStr != "" {
		minWithDrawValue, err := strconv.ParseFloat(minWithDrawValueStr, 64)
		if err != nil {
			return err
		}
		rr.MinWithdrawalLimit = util.Float2DP(minWithDrawValue)
	} else {
		rr.MinWithdrawalLimit = minWithdrawValueDefault
	}

	isWithoutLevelOneLimited, err := service.Redis.SIsMember(keys.KeyUserIDsWithoutWithdrawLevelOneLimit0.Format(), creatorID).Result()
	if err != nil {
		return
	}
	if isWithoutLevelOneLimited {
		rr.MaxWithdrawalLimit = maxWithdrawValueLevelTwo
	} else {
		rr.MaxWithdrawalLimit = maxWithdrawValueLevelOne
	}

	now := goutil.TimeNow()
	timeStampMonthStart := util.BeginningOfMonth(now).Unix()
	var revenueWithdrawing util.Float2DP
	err = withdrawalrecord.WithdrawalRecord{}.DB().
		Select("IFNULL(SUM(profit), 0) AS profit").
		Where("user_id = ? AND status = ?", creatorID, withdrawalrecord.StatusCreate).
		Where("create_time >= ?", timeStampMonthStart).
		Row().Scan(&revenueWithdrawing)
	if err != nil && err != sql.ErrNoRows {
		return err
	}
	rr.RemainingWithdrawalLimit = rr.MaxWithdrawalLimit - revenueWithdrawing
	if rr.RemainingWithdrawalLimit < 0 {
		rr.RemainingWithdrawalLimit = 0
	}
	return
}

// isWithinWithdrawTime 指定的时间是否可进行提现操作
func isWithinWithdrawTime(tm time.Time) bool {
	var openWithdrawRevenueEntrance bool
	config.GetAB("open_withdraw_revenue_entrance", &openWithdrawRevenueEntrance)
	if openWithdrawRevenueEntrance {
		return true
	}

	// 每月仅 1 至 3 号可进行提现操作
	return tm.Day() <= 3
}

type revenuePlayResp struct {
	Data       []*revenuePlayElem      `json:"data"`
	Pagination goutil.MarkerPagination `json:"pagination"`
}

type revenuePlayElem struct {
	Type        string  `json:"type"`
	UserID      int64   `json:"user_id"`
	Username    string  `json:"username"`
	IconURL     string  `json:"iconurl"`
	Price       int64   `json:"price"`           // 钻石
	Num         *int64  `json:"num,omitempty"`   // 数量，仅当 type 为 lucky_box 时下发
	Title       *string `json:"title,omitempty"` // 标题，仅当 type 为 lucky_box 时下发
	ConfirmTime int64   `json:"confirm_time"`    // 秒级时间戳
}

const (
	revenueTypeDanmaku  = "danmaku"
	revenueTypeQuestion = "question"
	revenueTypeLuckyBox = "lucky_box"
)

// ActionRevenuePlay 主播收益记录-玩法
/**
 * @api {get} /api/v2/user/revenue/play 主播收益记录-玩法
 * @apiDescription 玩法中包含提问和付费弹幕
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/chatroom
 *
 * @apiParam {String} [marker] marker 标记，用于分页，第一次请求不传，后续请求传上一次返回的 marker
 * @apiParam {Number} [page_size=20] 一页显示数目
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [{
 *         "type": "question", // question: 提问；danmaku: 付费弹幕；lucky_box: 宝盒
 *         "user_id": 3457230,
 *         "username": "六一半夏sixsixsix666",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/26/8b709126be0b9b1145bf9eb491d0efea145823.jpg",
 *         "price": 10000, // 单位：钻石
 *         "confirm_time": 1234567890 // 单位：秒
 *       },
 *       {
 *         "type": "danmaku",
 *         "user_id": 3457230,
 *         "username": "六一半夏sixsixsix666",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/26/8b709126be0b9b1145bf9eb491d0efea145823.jpg",
 *         "price": 10000, // 单位：钻石
 *         "confirm_time": 1234567890 // 单位：秒
 *       },
 *       {
 *         "type": "lucky_box",
 *         "user_id": 3457230,
 *         "username": "六一半夏sixsixsix666",
 *         "iconurl": "https://static-test.maoercdn.com/avatars/202005/26/8b709126be0b9b1145bf9eb491d0efea145823.jpg",
 *         "price": 10000, // 单位：钻石
 *         "num": 10, // 数量
 *         "title": "宝盒名称",
 *         "confirm_time": 1234567890 // 单位：秒
 *       }],
 *       "pagination": {
 *         "has_more": true, // 如果还有记录未返回，则是 true, 反之 false
 *         "marker": "1234567890,1" // 下一次请求的游标
 *       }
 *     }
 *   }
 *
 */
func ActionRevenuePlay(c *handler.Context) (handler.ActionResponse, error) {
	_, pageSize, err := c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	marker, _ := c.GetParamString("marker")
	var logs []transactionlog.TransactionLog
	db := transactionlog.DB()
	if servicedb.Driver != servicedb.DriverSqlite {
		// 部分用户因执行计划有时使用错误索引查询时扫描行数过大，导致慢查询
		// 强制指定 idx_toid_type_status_confirmtime 解决查询缓慢
		// 显式使用索引记录: https://info.missevan.com/pages/viewpage.action?pageId=69831572
		db = db.Table(
			transactionlog.TransactionLog{}.TableName() + " FORCE INDEX(idx_toid_type_status_confirmtime)",
		)
	}
	db = db.Where("to_id = ? AND status = ? AND type IN (?)", c.UserID(), transactionlog.StatusSuccess,
		[]int64{transactionlog.TypeLive, transactionlog.TypeGuildLive}).
		Where(transactionlog.PlayCondSQL())
	if marker != "" {
		marker, err := parseRevenuePlayMarker(marker)
		if err != nil || marker == nil {
			return nil, actionerrors.ErrParams
		}
		// 订单在创建后 confirm_time 可能会很久后更新，出现很早的 id 有个很晚的 confirm_time 的情况
		// 不能写成 confirm_time <= ? AND id < ?, marker 中 confirm_time = 10, id = 4 的情况，会遗漏 id = 6 的数据
		/*
			id confirm_time
			3  9
			4  10
			5  10
			6  9
		*/
		db = db.Where("confirm_time < ? OR (confirm_time = ? AND id < ?)",
			marker.confirmTime, marker.confirmTime, marker.logID)
	}
	// 多查询一个判断 hasMore
	err = db.Order("confirm_time DESC, id DESC").Limit(pageSize + 1).Find(&logs).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp := revenuePlayResp{
		Data: make([]*revenuePlayElem, 0, len(logs)),
	}
	if len(logs) == 0 {
		resp.Pagination = goutil.MarkerPagination{
			HasMore: false,
			Marker:  marker, // 无记录时，marker 不变
		}
		return resp, nil
	}
	hasMore := int64(len(logs)) > pageSize
	var pageIndex int64
	if hasMore {
		pageIndex = pageSize
	} else {
		pageIndex = int64(len(logs))
	}
	resp.Pagination = goutil.MarkerPagination{
		HasMore: hasMore,
		Marker:  buildRevenuePlayMarker(logs[pageIndex-1].ConfirmTime, logs[pageIndex-1].ID),
	}
	logs = logs[:pageIndex]
	userIDs := make([]int64, 0, len(logs))
	for i := range logs {
		userIDs = append(userIDs, logs[i].FromID)
	}
	userMap, err := mowangskuser.FindSimpleMap(userIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, log := range logs {
		u := userMap[log.FromID]
		if u == nil {
			continue
		}
		elem := &revenuePlayElem{
			ConfirmTime: log.ConfirmTime,
			Price:       log.AllCoin,
			UserID:      log.FromID,
			Username:    u.Username,
			IconURL:     u.IconURL,
		}
		switch log.Attr {
		case transactionlog.AttrLiveDanmaku:
			elem.Type = revenueTypeDanmaku
		case transactionlog.AttrLiveBuyLuckyBox:
			elem.Type = revenueTypeLuckyBox
			elem.Num = goutil.NewInt64(log.Num)
			elem.Title = goutil.NewString(log.Title)
		case transactionlog.AttrCommon:
			// AttrCommon 表示普通礼物（gift_id != 0）或问答（gift_id = 0），并且上面的查询条件已排除普通礼物的情况
			elem.Type = revenueTypeQuestion
		default:
			logger.WithFields(logger.Fields{
				"id":   log.ID,
				"attr": log.Attr,
			}).Error("invalid attr in revenue play transactionlog")
			continue
		}
		resp.Data = append(resp.Data, elem)
	}
	return resp, nil
}

type revenueMarker struct {
	confirmTime int64
	logID       int64
}

func parseRevenuePlayMarker(marker string) (*revenueMarker, error) {
	strs := strings.Split(marker, ",")
	if len(strs) != 2 {
		return nil, actionerrors.ErrParams
	}
	confirmTime, err := strconv.ParseInt(strs[0], 10, 64)
	if err != nil || confirmTime <= 0 {
		return nil, actionerrors.ErrParams
	}
	logID, err := strconv.ParseInt(strs[1], 10, 64)
	if err != nil || logID < 0 {
		return nil, actionerrors.ErrParams
	}
	return &revenueMarker{
		confirmTime: confirmTime,
		logID:       logID,
	}, nil
}

func buildRevenuePlayMarker(confirmTime, logID int64) string {
	return fmt.Sprintf("%d,%d", confirmTime, logID)
}
