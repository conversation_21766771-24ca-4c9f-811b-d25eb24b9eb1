package webuser

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testRoomID    = int64(347142109)
	testCreatorID = int64(9075623)
	nobleUserID   = int64(3456835)
)

func TestUserInfoTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(userCardResp{}, "user", "card_frame_url", "card_frame", "followed", "room")
	kc.Check(userCardRespRoom{}, "room_id", "creator_id", "medal", "gift_wall")
	kc.Check(userCardRespRoomMedal{}, "name", "fans_num", "super_fans_num", "super_fan_icon_url")
}

func TestNewUserInfoCardParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api?room_id=12", true, nil)
	_, err := newUserInfoCardParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, "/api?room_id=347142108&user_id=1023", true, nil)
	param, err := newUserInfoCardParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(c.UserID(), param.requestUserID)
	assert.Equal(int64(1023), param.paramUserID)
	assert.Equal(int64(347142108), param.roomID)
}

func TestCheckFolllowed(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	attention := new(attentionuser.AttentionUser)
	require.NoError(service.DB.FirstOrCreate(attention, &attentionuser.AttentionUser{
		UserActive: 999999, UserPasstive: 12, Time: 1562075169}).Error)

	followed := checkFollowed(12, 0)
	assert.Nil(followed)
	followed = checkFollowed(12, 12)
	assert.Nil(followed)
	followed = checkFollowed(12, 999999)
	require.NotNil(followed)
	assert.True(*followed)
	followed = checkFollowed(12, 999998)
	require.NotNil(followed)
	assert.False(*followed)
}

func TestFindUserIntro(t *testing.T) {
	assert := assert.New(t)

	userInfo := &userItemResp{
		UserInfo: &liveuser.UserInfo{
			Simple:       liveuser.Simple{UID: 10},
			Introduction: "测试",
		},
	}
	findUserIntro(userInfo)
	assert.Empty(userInfo.Introduction)

	userInfo = &userItemResp{
		UserInfo: &liveuser.UserInfo{
			Simple: liveuser.Simple{UID: -1234567890},
		},
	}
	findUserIntro(userInfo)
	assert.Empty(userInfo.Introduction)
}

func TestUserCardResp_setUserCardFrame(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 12
	// set cache
	b, err := json.Marshal(map[int][]*userappearance.UserAppearance{
		appearance.TypeCardFrame: {
			{
				UserID:   testUserID,
				Type:     appearance.TypeCardFrame,
				ImageNew: storage.ParseSchemeURL("oss://testdata/testCardFrame.avif"),
				TextColorItem: &appearance.TextColorItem{
					Introduction: "#FFFF00",
				},
			},
		},
	})
	require.NoError(err)
	err = service.Redis.Set(keys.KeyUsersWornAppearancesSets1.Format(testUserID), b, 10*time.Second).Err()
	require.NoError(err)

	// 个人名片框失效
	ucr := new(userCardResp)
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	ucr.setUserCardFrame(c, 120)
	require.NotNil(ucr.CardFrame)
	assert.Equal(storage.ParseSchemeURL(config.Conf.Params.UserInfo.DefaultCardFrame.ImageURL), ucr.CardFrame.ImageURL)
	require.NotNil(ucr.CardFrame.TextColorItem)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Username, ucr.CardFrame.TextColorItem.Username)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Introduction, ucr.CardFrame.TextColorItem.Introduction)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.ReportAndManage, ucr.CardFrame.TextColorItem.ReportAndManage)

	// 个人名片框未失效
	// 系统版本过低，返回 webp
	c = handler.NewTestContext(http.MethodPost, "", true, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().OSVersion = "13.0.0"
	c.Equip().AppVersion = "4.9.3"
	ucr = new(userCardResp)
	ucr.setUserCardFrame(c, testUserID)
	require.NotNil(ucr.CardFrame)
	assert.Equal(storage.ParseSchemeURL("oss://testdata/testCardFrame.webp"), ucr.CardFrame.ImageURL)
	require.NotNil(ucr.CardFrame.TextColorItem)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.Username, ucr.CardFrame.TextColorItem.Username)
	assert.Equal("#FFFF00", ucr.CardFrame.TextColorItem.Introduction)
	assert.Equal(config.Conf.Params.UserInfo.DefaultCardFrame.TextColorItem.ReportAndManage, ucr.CardFrame.TextColorItem.ReportAndManage)
	assert.Equal(ucr.CardFrame.ImageURL, ucr.CardFrameURL)

	// app 版本过低，返回 webp
	c = handler.NewTestContext(http.MethodPost, "", true, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().OSVersion = "14.0.0"
	c.Equip().AppVersion = "4.9.2"
	ucr = new(userCardResp)
	ucr.setUserCardFrame(c, testUserID)
	assert.Equal(storage.ParseSchemeURL("oss://testdata/testCardFrame.webp"), ucr.CardFrameURL)

	// 返回 avif
	c = handler.NewTestContext(http.MethodPost, "", true, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().OSVersion = "14.0.0"
	c.Equip().AppVersion = "4.9.3"
	ucr = new(userCardResp)
	ucr.setUserCardFrame(c, testUserID)
	assert.Equal(storage.ParseSchemeURL("oss://testdata/testCardFrame.avif"), ucr.CardFrameURL)

	// 新版本不再下发 card_frame_url
	c = handler.NewTestContext(http.MethodPost, "", true, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().OSVersion = "14.0.0"
	c.Equip().AppVersion = "6.0.2"
	ucr = new(userCardResp)
	ucr.setUserCardFrame(c, testUserID)
	assert.Empty(ucr.CardFrameURL)
}

func TestUserCardRespFindMedals(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	lm := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID:    347142109,
			CreatorID: 9075623,
			UserID:    10341,
			Point:     1,
			Status:    livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Name: "Ayaka 勋章测试",
			},
		},
	}
	lm2 := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID:    347142109,
			CreatorID: 9075623,
			UserID:    10342,
			Point:     1,
			Status:    livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Name:     "Ayaka 勋章测试",
				SuperFan: &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(10 * time.Minute).Unix()},
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().UpdateOne(ctx,
		bson.M{"user_id": lm.UserID, "room_id": lm.RoomID},
		bson.M{"$set": lm},
		options.Update().SetUpsert(true))
	require.NoError(err)
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{"user_id": lm2.UserID, "room_id": lm.RoomID},
		bson.M{"$set": lm2},
		options.Update().SetUpsert(true))
	require.NoError(err)

	ok, name := room.HaveMedal(testRoomID)
	require.True(ok)
	require.NotEmpty(name)

	result, err := livemedal.FindRoomMedalFansAndSuperFansCount(testRoomID)
	require.NoError(err)
	require.NotNil(result)

	resp := &userCardResp{
		Room: &userCardRespRoom{
			RoomID: testRoomID,
			Medal:  new(userCardRespRoomMedal),
		},
	}

	err = resp.findMedals()
	require.NoError(err)
	assert.Equal(result.FansNum, result.FansNum)
	assert.Equal(result.SuperFansNum, result.SuperFansNum)
}

func TestUserCardResp_findGiftWall(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testShowGiftIDs := []int64{1000, 2000}
	testRoomID := int64(223344)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{"start_time": bson.M{"$lte": now.Unix()}, "end_time": bson.M{"$gt": now.Unix()}})
	require.NoError(err)
	res, err := giftwall.PeriodCollection().InsertOne(ctx, giftwall.Period{
		StartTime:   now.Unix(),
		EndTime:     now.Add(1 * time.Hour).Unix(),
		ShowGiftIDs: testShowGiftIDs,
	})
	require.NoError(err)
	_, err = giftwall.RecordCollection().DeleteMany(ctx, bson.M{"room_id": testRoomID})
	require.NoError(err)
	_, err = giftwall.RecordCollection().InsertOne(ctx, giftwall.ActivatedRecord{
		PeriodOID: res.InsertedID.(primitive.ObjectID),
		RoomID:    testRoomID,
	})
	require.NoError(err)

	resp := &userCardResp{
		Room: &userCardRespRoom{RoomID: testRoomID},
	}
	err = resp.findGiftWall()
	require.NoError(err)
	require.NotNil(resp.Room.GiftWall)
	assert.EqualValues(len(testShowGiftIDs), resp.Room.GiftWall.TotalNum)
	assert.EqualValues(1, resp.Room.GiftWall.ActivatedNum)
}

func TestUserCardRespFindSuperFans(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &userCardResp{
		Room: &userCardRespRoom{
			Medal: new(userCardRespRoomMedal),
		},
	}
	err := resp.findSuperFans()
	require.NoError(err)

	goods, err := livegoods.ListShowingSuperFan()
	require.NoError(err)
	require.NotNil(goods)
	require.NotEmpty(goods)

	assert.Equal(resp.Room.Medal.SuperFanIconURL, goods[0].GoodsIconURL())
}

func TestUserCardRespFindRoomInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	resp := &userCardResp{
		User: &userItemResp{
			UserInfo: &liveuser.UserInfo{
				Simple: liveuser.Simple{UID: 1023},
			},
		},
	}
	resp.findRoomInfo(&userInfoCardParam{
		roomID:        347142108,
		requestUserID: 12,
		paramUserID:   1023,
	})
	require.NotNil(resp.Room)
	assert.Equal(int64(1023), resp.Room.CreatorID)
	assert.NotNil(resp.Room.Medal)
	assert.Zero(resp.Room.Medal.FansNum)
	assert.Zero(resp.Room.Medal.SuperFansNum)

	resp = &userCardResp{
		User: &userItemResp{
			UserInfo: &liveuser.UserInfo{
				Simple: liveuser.Simple{UID: testCreatorID},
			},
		},
	}
	resp.findRoomInfo(&userInfoCardParam{
		roomID:        testRoomID,
		requestUserID: 12,
		paramUserID:   testCreatorID,
	})
	require.NotNil(resp.Room)
	assert.Equal(int64(testCreatorID), resp.Room.CreatorID)
	require.NotNil(resp.Room.Medal)
}

func TestActionUserCard(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URLMainUserGetAuthInfo, func(any) (any, error) {
		return nil, nil
	})
	defer cancel()
	// 普通情况
	uri := fmt.Sprintf("/api/v2/user/card?room_id=%d&user_id=%d", 347142108, 12)
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	r, err := ActionUserCard(c)
	require.NoError(err)
	resp := r.(*userCardResp)
	assert.NotNil(resp.User)
	assert.Nil(resp.Followed)
	assert.NotEmpty(resp.CardFrameURL)
	assert.NotNil(resp.CardFrame)
	assert.Nil(resp.Room)
	assert.Nil(resp.User.AuthInfo)

	// 有粉丝勋章的直播间
	uri = fmt.Sprintf("/api/v2/user/card?room_id=%d&user_id=%d", testRoomID, testCreatorID)
	c = handler.NewTestContext(http.MethodGet, uri, true, nil)
	r, err = ActionUserCard(c)
	require.NoError(err)
	resp = r.(*userCardResp)
	assert.Equal(int64(testCreatorID), resp.User.UserID())
	require.NotNil(resp.Followed)
	assert.False(*resp.Followed)
	assert.NotNil(resp.Room)
	assert.Equal(int64(testCreatorID), resp.Room.CreatorID)
	require.NotNil(resp.Room.Medal)
}

func TestActionUserMyNoble(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/mynoble", false, nil)
	uvKey := keys.KeyNobleUserVips1.Format(c.UserID())
	require.NoError(service.Redis.Set(uvKey, `{}`, 30*time.Second).Err())
	r, err := ActionUserMyNoble(c)
	require.NoError(err)
	resp := r.(userMyNobleResp)
	assert.Nil(resp.User)
	assert.Nil(resp.Noble)
	assert.Nil(resp.Highness)
	assert.Empty(resp.VipTip)

	uvKey = keys.KeyNobleUserVips1.Format(nobleUserID)
	require.NoError(service.Redis.Set(uvKey, `{"1":{"type":1,"level":1,"user_id":3456835,"expire_time":9999999999}}`,
		30*time.Second).Err())
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/mynoble", true, nil)
	c.User().ID = nobleUserID
	r, err = ActionUserMyNoble(c)
	require.NoError(err)
	resp = r.(userMyNobleResp)
	assert.NotNil(resp.User)
	assert.NotNil(resp.Noble)
	assert.Nil(resp.Highness)
}

func TestNewUserVipInfo(t *testing.T) {
	assert := assert.New(t)

	var tip string
	assert.Nil(newUserVipInfo(nil, &tip))
	assert.Empty(tip)
	p := &vip.UserVip{
		Title:      "test",
		Type:       vip.TypeLiveNoble,
		Level:      1,
		ExpireTime: 999999999999,
	}
	resp := newUserVipInfo(p, &tip)
	assert.Equal("test", resp.Name)
	assert.Equal(1, resp.Level)
	assert.EqualValues(999999999999, resp.ExpireTime)
	assert.Equal(vip.NobleStatusNoble, resp.Status)
	assert.Empty(tip)

	p.ExpireTime = 111
	resp = newUserVipInfo(p, &tip)
	assert.Nil(resp)

	p.ExpireTime = goutil.TimeNow().AddDate(0, 0, 1).Unix()
	resp = newUserVipInfo(p, &tip)
	assert.Equal(vip.NobleStatusNoble, resp.Status)
	assert.Equal("test有效期剩余 1 天", tip)

	p.ExpireTime = goutil.TimeNow().AddDate(0, 0, -2).Unix()
	resp = newUserVipInfo(p, &tip)
	assert.Equal(vip.NobleStatusProtected, resp.Status)
	assert.Equal("test续费保护还剩 8 天", tip)

	tip = "练习生续费保护还剩 3 天"
	p = &vip.UserVip{
		Title:      "上神",
		Type:       vip.TypeLiveHighness,
		Level:      1,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(), // 上神有效期还剩 1 天
	}
	newUserVipInfo(p, &tip)
	assert.Equal("练习生续费保护还剩 3 天", tip)

	p = &vip.UserVip{
		Title:      "大咖",
		Type:       vip.TypeLiveTrialNoble,
		Level:      4,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	newUserVipInfo(p, &tip)
	assert.Equal("练习生续费保护还剩 3 天", tip)

	tip = ""
	p = &vip.UserVip{
		Title:      "大咖",
		Type:       vip.TypeLiveTrialNoble,
		Level:      4,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 1).Unix(),
	}
	newUserVipInfo(p, &tip)
	assert.Equal("大咖体验剩余 1 天", tip)

	tip = ""
	p = &vip.UserVip{
		Title:      "大咖",
		Type:       vip.TypeLiveTrialNoble,
		Level:      4,
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 4).Unix(),
	}
	newUserVipInfo(p, &tip)
	assert.Empty(tip)
}

func Test_userCardResp_findUserBlockStatus(t *testing.T) {
	assert := assert.New(t)

	resp := &userCardResp{
		User: &userItemResp{
			UserInfo: &liveuser.UserInfo{
				Simple: liveuser.Simple{UID: 1023},
			},
		},
	}
	param := &userInfoCardParam{
		requestUserID: 12,
		paramUserID:   1023,
	}
	t.Run("未拉黑", func(t *testing.T) {
		cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
			return map[string]interface{}{"block_status": []bool{false, false}}, nil
		})
		defer cleanup()
		resp.findUserBlockStatus(param)
		assert.EqualValues(0, resp.User.BlockStatus)
	})
	t.Run("仅拉黑当前用户", func(t *testing.T) {
		cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
			return map[string]interface{}{"block_status": []bool{true, false}}, nil
		})
		defer cleanup()
		resp.findUserBlockStatus(param)
		assert.EqualValues(1, resp.User.BlockStatus)
	})
	t.Run("仅被当前用户拉黑", func(t *testing.T) {
		cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
			return map[string]interface{}{"block_status": []bool{false, true}}, nil
		})
		defer cleanup()
		resp.findUserBlockStatus(param)
		assert.EqualValues(2, resp.User.BlockStatus)
	})
	t.Run("相互拉黑", func(t *testing.T) {
		cleanup := mrpc.SetMock(userapi.URIGoUserBlockStatus, func(i interface{}) (interface{}, error) {
			return map[string]interface{}{"block_status": []bool{true, true}}, nil
		})
		defer cleanup()
		resp.findUserBlockStatus(param)
		assert.EqualValues(3, resp.User.BlockStatus)
	})
}
