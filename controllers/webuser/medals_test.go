package webuser

import (
	"fmt"
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKey(t *testing.T) {
	var medalGetResp medalGetResp
	kcJSON := tutil.NewKeyChecker(t, tutil.JSON)
	kcJSON.Check(medalGetResp, "medal", "rule", "owner_count")

	kcJSON.Check(medalListResp{}, "data", "pagination", "rule", "medal_limit", "max_level", "normal_medal_count", "super_medal_count")
}

func TestActionMedalList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/medal/list", true, nil)
	c.User().ID = 99999
	r, err := ActionMedalList(c)
	require.NoError(err)
	resp := r.(*medalListResp)
	assert.Empty(resp.Data)
	require.NotNil(resp.NormalMedalCount)
	assert.Zero(*resp.NormalMedalCount)
	require.NotNil(resp.SuperMedalCount)
	assert.Zero(*resp.SuperMedalCount)

	testUserID := int64(2021041417)
	testCreatorID := int64(9074511)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	lm := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			UserID:    testUserID,
			CreatorID: testCreatorID,
			Point:     1,
			Status:    livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Name:     "test",
				SuperFan: &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(time.Minute).Unix()},
			},
		},
	}
	_, err = livemedal.Collection().UpdateOne(ctx, bson.M{"user_id": testUserID}, bson.M{"$set": lm},
		options.Update().SetUpsert(true))
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/medal/list", true, nil)
	c.User().ID = testUserID
	r, err = ActionMedalList(c)
	require.NoError(err)
	resp = r.(*medalListResp)
	assert.NotEmpty(resp.Data)
	assert.Zero(*resp.NormalMedalCount)
	assert.NotZero(*resp.SuperMedalCount)

	c = handler.NewTestContext(http.MethodGet, "/medal/list?type=1", true, nil)
	c.User().ID = testUserID
	r, err = ActionMedalList(c)
	require.NoError(err)
	resp = r.(*medalListResp)
	assert.NotEmpty(resp.Data)
	require.NotNil(resp.Data[0].SuperFan)
	require.NotZero(resp.Data[0].SuperFan.Days)
	require.NotZero(resp.Data[0].SuperFan.RegisterTime)

	_, err = livemedal.Collection().UpdateOne(ctx, bson.M{"user_id": testUserID}, bson.M{"$set": bson.M{"super_fan.expire_time": goutil.TimeNow().Add(-time.Minute).Unix()}})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, "/medal/list?type=1", true, nil)
	c.User().ID = testUserID
	r, err = ActionMedalList(c)
	require.NoError(err)
	resp = r.(*medalListResp)
	assert.Empty(resp.Data)
}

func TestMedalListResp_buildData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(9074509)
		now        = goutil.TimeNow()
	)

	// 测试非超粉直播间开播状态
	_, err := room.UpdateOneRoom(bson.M{"creator_id": 9074509}, bson.M{"status.open": room.StatusOpenTrue})
	require.NoError(err)

	liveMedals := []*livemedal.LiveMedal{
		{
			Simple: livemedal.Simple{
				CreatorID: 9074509,
			},
		},
	}
	m := medalListResp{}
	m.buildData(testUserID, liveMedals)
	require.Equal(1, len(m.Data))
	require.Nil(m.Data[0].SuperFan)
	assert.EqualValues(room.StatusOpenTrue, *m.Data[0].LiveStatus)

	// 测试超粉用户返回开通天数和开通时间
	liveMedals = append(liveMedals, []*livemedal.LiveMedal{
		{Simple: livemedal.Simple{
			CreatorID: 9074510,
			Mini: livemedal.Mini{
				SuperFan: &livemedal.SuperFan{
					ExpireTime: now.AddDate(0, 0, 1).Unix(),
				},
			},
		}},
		{Simple: livemedal.Simple{
			CreatorID: 9074511,
			Mini: livemedal.Mini{
				SuperFan: &livemedal.SuperFan{
					ExpireTime: now.AddDate(0, 0, 1).Unix(),
				},
			},
		}},
	}...)
	m.buildData(testUserID, liveMedals)
	require.Equal(3, len(m.Data))
	assert.EqualValues(room.StatusOpenTrue, *m.Data[0].LiveStatus)
	r1, err := room.FindOne(bson.M{"creator_id": liveMedals[1].CreatorID}, nil)
	require.NoError(err)
	assert.Equal(r1.Status.Open, *m.Data[1].LiveStatus)
	require.NotNil(m.Data[1].SuperFan)
	assert.NotZero(m.Data[1].SuperFan.Days)
	assert.NotZero(m.Data[1].SuperFan.RegisterTime)
	require.NotNil(m.Data[2].SuperFan)
	assert.NotZero(m.Data[2].SuperFan.Days)
	assert.NotZero(m.Data[2].SuperFan.RegisterTime)
}

func TestMedalWearTakeoffRemove(t *testing.T) {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := livemedal.Collection()
	update := bson.M{
		"room_id":    123456789,
		"creator_id": 123456,
		"user_id":    12,
		"status":     livemedal.StatusOwned,
		"point":      100,
		"super_fan":  livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(time.Minute).Unix()},
	}
	_, err := collection.UpdateOne(ctx, bson.M{"creator_id": 123456, "user_id": 12},
		bson.M{"$set": update}, options.Update().SetUpsert(true))
	require.NoError(t, err)
	t.Run("wear", subTestMedalWear)
	t.Run("take off", subTestMedalTakeoff)
	t.Run("remove", subTestMedalRemove)
}

func subTestMedalWear(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/medal/wear", nil)
	_, err := ActionMedalWear(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/medal/wear", nil)
	form := url.Values{}
	form.Add("creator_id", "11111")
	c.C.Request.PostForm = form
	_, err = ActionMedalWear(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/medal/wear", nil)
	form = url.Values{}
	form.Add("creator_id", "123456")
	c.C.Request.PostForm = form
	r, err := ActionMedalWear(c)
	require.NoError(err)
	assert.Equal("佩戴成功", r)
}

func subTestMedalTakeoff(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/medal/takeoff", nil)
	_, err := ActionMedalTakeoff(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/medal/takeoff", nil)
	form := url.Values{}
	form.Add("creator_id", "11111")
	c.C.Request.PostForm = form
	_, err = ActionMedalTakeoff(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.CreateTestContext(true)
	c.C.Request, _ = http.NewRequest("POST", "/medal/takeoff", nil)
	form = url.Values{}
	form.Add("creator_id", "123456")
	c.C.Request.PostForm = form
	r, err := ActionMedalTakeoff(c)
	require.NoError(err)
	assert.Equal("卸下成功", r)
}

func subTestMedalRemove(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "/medal/remove", true, nil)
	_, err := ActionMedalRemove(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/medal/remove", true, handler.M{"creator_id": 11111})
	_, err = ActionMedalRemove(c)
	assert.Equal(actionerrors.ErrCannotFindResource, err)

	c = handler.NewTestContext(http.MethodPost, "/medal/remove", true, handler.M{"creator_id": 123456})
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "4.8.9"
	c.Equip().FromApp = true
	_, err = ActionMedalRemove(c)
	assert.Equal(actionerrors.ErrCannotRemoveSuperMedal, err)

	c = handler.NewTestContext(http.MethodPost, "/medal/remove", true, handler.M{"creator_id": 123456})
	r, err := ActionMedalRemove(c)
	require.NoError(err)
	assert.Equal("删除成功", r)

	var superFanOrder livetxnorder.LiveTxnOrder
	err = superFanOrder.DB().Select("id, expire_time").
		Where("buyer_id = ? AND seller_id = ?", 12, 123456).
		Where("goods_type = ?", livegoods.GoodsTypeSuperFan).
		Order("id DESC").Take(&superFanOrder).Error
	require.NoError(err)
	assert.LessOrEqual(superFanOrder.ExpireTime, goutil.TimeNow().Unix())
}

func TestActionMedalGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	roomCollection := service.MongoDB.Collection(room.CollectionName)
	livemedalCollection := service.MongoDB.Collection(livemedal.CollectionName)
	defer func() {
		_, err := livemedalCollection.DeleteMany(ctx, bson.M{"room_id": 347142108})
		assert.NoError(err)
	}()
	_, err := roomCollection.UpdateOne(ctx, bson.M{"room_id": 347142108}, bson.M{
		"$set": bson.M{
			"medal.name":       "勋章 Test",
			"medal.name_clean": "勋章 Test",
		},
	})
	require.NoError(err)

	lm := livemedal.LiveMedal{
		Simple: livemedal.Simple{
			RoomID:    347142108,
			CreatorID: 1023,
			UserID:    1023,
			Point:     10,
			Status:    livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Name: "勋章 Test",
			},
		},
	}
	_, err = livemedalCollection.InsertOne(ctx, lm)
	require.NoError(err)
	lm.Simple.Status = livemedal.StatusPending
	_, err = livemedalCollection.InsertOne(ctx, lm)
	require.NoError(err)

	c := handler.NewTestContext(http.MethodGet, "/medal/get", false, nil)
	_, err = ActionMedalGet(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/medal/get?creator_id=%d", lm.CreatorID), false, nil)
	r, err := ActionMedalGet(c)
	require.NoError(err)
	resp := r.(*medalGetResp)
	assert.Equal("勋章 Test", resp.Medal.Name)
	assert.Equal(int64(1), resp.OwnerCount)
	assert.Equal(lm.RoomID, resp.Medal.RoomID)

	room, err := room.Update(369892, bson.M{"medal": room.Medal{Name: "test medal/get"}})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/medal/get?creator_id=%d", room.CreatorID), false, nil)
	r, err = ActionMedalGet(c)
	require.NoError(err)

	resp = r.(*medalGetResp)
	assert.Equal("test medal/get", resp.Medal.Name)
	assert.NotEqual("", resp.Medal.CreatorUsername)
	assert.NotEqual("", resp.Medal.CreatorIconURL)

	c = handler.NewTestContext(http.MethodGet, "/medal/get?name=test medal/get", false, nil)
	r, err = ActionMedalGet(c)
	require.NoError(err)
	resp = r.(*medalGetResp)
	assert.Equal(room.CreatorID, resp.Medal.CreatorID)

	query := fmt.Sprintf("/medal/get?creator_id=%d", -100)
	c = handler.NewTestContext(http.MethodGet, query, false, nil)
	_, err = ActionMedalGet(c)
	assert.EqualError(err, "无法找到该勋章")
	query = fmt.Sprintf("/medal/get?creator_id=%d", 1)
	c = handler.NewTestContext(http.MethodGet, query, false, nil)
	_, err = ActionMedalGet(c)
	assert.EqualError(err, "无法找到该勋章")
}
