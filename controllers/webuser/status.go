package webuser

import (
	"fmt"
	"math/rand"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service/storage"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	confparam "github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalremoverecord"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/presetmessage"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	medalStatusNoMedal    = iota // 未拥有
	medalStatusNoWear            // 拥有但是未佩戴
	medalStatusWearing           // 佩戴中
	medalStatusNeverOwned        // 没有拥有过任何粉丝勋章
)

// 预设消息数量
const presetMessageCount = 10

// 全站喇叭锁定状态
const (
	unlock = iota // 已解锁
	lock          // 未解锁
)

type generalSettings struct {
	Bubble    *int `form:"bubble" json:"bubble"`
	Invisible *int `form:"invisible" json:"invisible"`

	c *handler.Context
}

// ActionSettingsSetGeneral 用户进行设置通用选项
/**
 * @api {post} /api/v2/user/status/set-general 设置通用选项
 * @apiDescription 设置通用选项，每次调用只支持设置一个选项。当前仅支持：隐身
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=0,1} [invisible] 是否隐身； 0：不隐身, 1: 隐身
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "设置成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionSettingsSetGeneral(c *handler.Context) (handler.ActionResponse, error) {
	var gs generalSettings
	err := c.Bind(&gs)
	if err != nil ||
		(gs.Bubble == nil && gs.Invisible == nil) {
		return nil, actionerrors.ErrParams
	}
	gs.c = c
	switch {
	case gs.Invisible != nil:
		err = gs.updateInvisible()
	}
	if err != nil {
		return nil, err
	}
	return "设置成功", nil
}

func (gs *generalSettings) updateInvisible() error {
	uv, err := vip.UserActivatedVip(gs.c.UserID(), false, gs.c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if !vip.HavePrivilege(uv, vip.PrivilegeBubble) {
		return actionerrors.ErrNoAuthority
	}
	err = userstatus.SetInvisible(gs.c.UserID(), *gs.Invisible != 0)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

type statusGetResp struct {
	userstatus.GeneralStatus

	Backpack      []useritems.BackpackItem `json:"backpack"`
	NewAppearance bool                     `json:"new_appearance"`

	MedalStatus   int                       `json:"medal_status"`
	MedalDiscount *medalDiscount            `json:"medal_discount,omitempty"`
	Medal         *livemedal.Mini           `json:"medal,omitempty"`
	UserLevel     *userstatus.UserLevelInfo `json:"user_level"`
	UserBlackCard *userBlackCard            `json:"user_black_card,omitempty"`
	HornList      []hornItem                `json:"horn_list"`

	PresetMessages []string `json:"preset_messages"`
}

type medalDiscount struct {
	Tip string `json:"tip,omitempty"`
}

type userBlackCard struct {
	Level int    `json:"level"` // 用户当前黑卡等级
	Title string `json:"title"` // 用户当前黑卡等级名称
}

// hornItem 全站喇叭信息
type hornItem struct {
	Type         int    `json:"type"`                     // 1: 贵族喇叭，2: 黑卡喇叭
	Lock         int    `json:"lock"`                     // 当前全站喇叭锁定状态，0: 已解锁，1: 未解锁
	Title        string `json:"title"`                    // 当前等级贵族或黑卡名称，num 为 0 时下发“星曜/贵族”
	Intro        string `json:"intro,omitempty"`          // 获取喇叭途径提示文案，仅在 num 为 0 时下发
	IconURL      string `json:"icon_url"`                 // 喇叭图标
	Num          int64  `json:"num"`                      // 当前拥有数量。为 0 时表示喇叭已用尽或未获得
	ExpireTime   int64  `json:"expire_time"`              // 身份失效时间戳，单位：秒。不包含此时刻
	IntroOpenURL string `json:"intro_open_url,omitempty"` // 黑卡活动页面地址，仅在黑卡喇叭中下发
	LabelIconURL string `json:"label_icon_url,omitempty"` // 喇叭角标，仅下发时需要展示
}

// ActionStatusGet 用户获取自己的直播间配置
// TODO: 支持： @apiParam {Number} [room_id] 房间号，返回该房间特殊设置
/**
 * @api {get} /api/v2/user/status/get 获取直播相关状态
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/user
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "invisible": false, // 是否隐身，只有有权限的贵族会返回该字段
 *       "horn_num": 0, // 全站喇叭数量，包含：贵族 + 黑卡。不支持黑卡的客户端版本下发贵族喇叭数量
 *       "horn_list": [ // 全站喇叭列表，默认选中第一个喇叭
 *         {
 *           "type": 2, // 类型，1: 贵族喇叭，2: 黑卡喇叭
 *           "lock": 0, // 当前全站喇叭锁定状态，0 或不下发：已解锁，1：未解锁
 *           "title": "星曜 V4 全站喇叭", // 当前等级贵族或黑卡全站喇叭名称，num 为 0 时下发“星曜全站喇叭/贵族全站喇叭”
 *           "intro": "参与【星曜活动】可获得", // 获取喇叭途径提示文案，仅在 num 为 0 时下发
 *           "icon_url": "https://static-test.maoercdn.com/cat_food.png", // 喇叭图标
 *           "num": 1, // 当前拥有数量。为 0 时表示喇叭已用尽或未获得
 *           "expire_time": 1612345678, // 身份失效时间戳，单位：秒。不包含此时刻
 *           "intro_open_url": "https://www.missevan.com/mevent/9000?from_room_id=__ROOM_ID", // 黑卡活动页面地址，支持模版变量，仅在黑卡喇叭中下发
 *           "label_icon_url": "https://static-test.maoercdn.com/live/bubbles/notify/labels/blackcard4.png" // 喇叭角标，仅下发时需要展示
 *         },
 *         {
 *           "type": 1,
 *           "lock": 0,
 *           "title": "传奇全站喇叭",
 *           "icon_url": "https://static-test.maoercdn.com/cat_food.png",
 *           "num": 1,
 *           "intro": "开通或续费大咖及以上贵族可获得",
 *           "expire_time": 1612345678
 *         }
 *       ],
 *       "recommend_num": 0 // 神话推荐次数，只有有权限的贵族会返回该字段
 *       "backpack":[{
 *         "type": 1, // 1 说明是礼物，后续可能会加 xxx 体验卡之类的
 *         "gift_id": 301, // 礼物 id
 *         "comboable": 1, // 同 meta/data, 该礼物支持连击，不能连击不返回该字段
 *         "price": 0, // 礼物价格（钻），可用于连击礼物倒计时的判断
 *         "num": 5000,
 *         "allowed_nums": [], // 配置的送礼数量
 *         // 下面的字段基本都会有
 *         "name": "猫粮",
 *         "icon_url": "https://static-test.maoercdn.com/cat_food.png",
 *         "icon_active_url": "https://static-test.maoercdn.com/gifts/icons/active/001.webp", // 礼物被选中时显示的动画
 *         "intro": "free gift: cat food",
 *         "intro_icon_url": "https://static-test.maoercdn.com/intro.png",
 *         "intro_open_url": "https://fm.uat.missevan.com",
 *         "label_icon_url": "https://static-test.maoercdn.com/live/gifts/icons/label/001.webp",
 *         "time_left": 5000 // 剩余时间，单位：秒
 *       }, {
 *         "type": 2, // 2 说明是道具
 *         "item_id": 1, // 道具 ID
 *         "item_type": 1, // 道具类型，1：贵族体验卡 2：粉丝勋章兑换卡
 *         "price": 0, // 价格（钻）
 *         "num": 2,
 *         "allowed_nums": [], // 配置的数量
 *         "name": "大咖 1 天体验卡",
 *         "icon_url": "https://static-test.maoercdn.com/vip_trial_card.png",
 *         "intro": "试用后可体验 1 天大咖贵族",
 *         "intro_icon_url": "https://static-test.maoercdn.com/intro.png",
 *         "intro_open_url": "https://fm.uat.missevan.com",
 *         "label_icon_url": "https://static-test.maoercdn.com/live/gifts/icons/label/001.webp",
 *         "time_left": 5000, // 剩余时间，单位：秒
 *         "trial_noble": { // 仅贵族体验卡有该字段
 *           "level": 1, // 贵族等级，用于比较用户当前贵族等级
 *           "title": "大咖",
 *           "icon_url": "https://static-test.maoercdn.com/live/006.png", // 用于贵族体验卡使用成功的弹窗显示
 *         }
 *       }],
 *       "new_appearance": true, // 是否有全新外观，false 为无
 *       "medal_status": 2, // 勋章状态 0: 未获得任何勋章，1: 有勋章，但是未佩戴，2: 佩戴中，3: 无粉丝勋章开通记录
 *       "medal_discount": { // 折扣信息，没有需要展示的折扣不下发此字段
 *         "tip": "粉团 1 折" // 获取粉丝勋章的优惠信息
 *       },
 *       "medal": { // 当前佩戴的勋章，仅 medal_status 为佩戴中时返回
 *         "name": "勋章名称",
 *         "level": 12,
 *         "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *           "expire_time": 1576116700 // 秒级时间戳
 *         },
 *         "frame_url": "https://static-test.missevan.com/live/medalframes/3f12/level15_0_9_0_54.png", // 用户佩戴的定制粉丝徽章
 *         "name_color": "#FFFFFF"
 *       },
 *       "user_level": { // 用户等级信息
 *         "exp": 1346, // 当前经验值 - 本级初始经验值
 *         "level_up_exp": 1500, // 下级初始经验值 - 本级初始经验值，用户满级时为 0
 *         "level_up_coin": 100, // 钻石差值，用户满级时为 0
 *         "level": 12, // 用户等级
 *         "level_speed_up": { // 无加速特权或用户满级时不返回
 *           "privilege_title": "贵族名称",
 *           "factor": 1.25 // 消费加速倍数
 *         },
 *         "level_icon_url": "https://static-test.maoercdn.com/live/userlevels/level120.webp" // 等级勋章 下发则展示，不下发则自己生成
 *       },
 *       "user_black_card": { // 当前用户黑卡等级信息，当前用户无黑卡等级时不下发
 *         "level": 2, // 用户当前黑卡等级
 *         "title": "黑卡 2" // 用户当前黑卡等级名称
 *       },
 *       "preset_messages": ["大家好！", "主播好厉害！", "支持主播~"] // 预设消息列表，每次返回最多 10 条
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionStatusGet(c *handler.Context) (handler.ActionResponse, error) {
	var resp statusGetResp
	var err error
	var userVip *vip.UserVip
	resp.GeneralStatus, userVip, err = userstatus.UserGeneral(c.UserID(), c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	resp.Backpack, err = useritems.FindBackpackItems(c.Equip(), c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.NewAppearance, err = userappearance.UserHasNewAppearance(c.UserID())
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 查询勋章状态
	l, err := livemedal.FindOne(
		bson.M{
			"user_id": c.UserID(),
			"status":  bson.M{"$gt": livemedal.StatusPending},
		}, options.FindOne().SetSort(bson.M{"status": -1}),
		livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if l == nil {
		resp.MedalStatus = medalStatusNoMedal

		// 查询是否拥有过粉丝勋章，判断是否返回折扣购买粉丝勋章的状态和文案
		medalCfg, err := params.FindMedal()
		if err != nil {
			logger.Error(err)
			// PASS
		}
		// 获取配置失败时，IsFirstMedalDiscountEnable 返回 false
		if medalCfg.IsFirstMedalDiscountEnable(c.Equip()) {
			exists, err := livemedalremoverecord.IsUserEverHadMedal(c.UserID())
			if err != nil {
				logger.Error(err)
				// PASS
			} else if !exists {
				// 没有拥有过任何粉丝勋章时，下发对应状态和折扣文案
				resp.MedalStatus = medalStatusNeverOwned
				resp.MedalDiscount = &medalDiscount{
					Tip: medalCfg.FirstDiscount.StatusTip,
				}
			}
		}
	} else {
		if l.Status != livemedal.StatusShow {
			resp.MedalStatus = medalStatusNoWear
		} else {
			resp.MedalStatus = medalStatusWearing
			resp.Medal = &l.Mini
		}
	}
	resp.UserLevel, err = newUserLevel(c.UserID(), userVip)
	if err != nil {
		return nil, err
	}
	userBlackCardInfo, err := liveuserblackcard.FindUserActiveBlackCard(c.UserID())
	if err != nil {
		return nil, err
	}
	if userBlackCardInfo != nil {
		resp.UserBlackCard = &userBlackCard{
			Level: userBlackCardInfo.Level,
			Title: userBlackCardInfo.Title,
		}
	}
	nobleHorn, err := getNobleHorn(c, resp.NobleHornNum)
	if err != nil {
		return nil, err
	}
	blackCardHorn, err := getBlackCardHorn(userBlackCardInfo, resp.BlackCardHornNum)
	if err != nil {
		return nil, err
	}
	resp.HornList = sortHornItems(*nobleHorn, *blackCardHorn)
	if c.Equip().IsOldApp(goutil.AppVersions{IOS: "6.3.8", Android: "6.3.8"}) {
		// WORKAROUND: 安卓、iOS <= 6.3.8 版本只支持贵族喇叭，只返回贵族喇叭数量
		resp.HornNum = resp.NobleHornNum
	}
	messages, err := getPresetMessage()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if len(messages) > 0 {
		resp.PresetMessages = messages
	}
	return &resp, nil
}

func newUserLevel(userID int64, userVip *vip.UserVip) (*userstatus.UserLevelInfo, error) {
	user, err := liveuser.FindOneSimple(bson.M{"user_id": userID}, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	userLevelInfo, err := userstatus.NewUserLevelInfo(user, userVip)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return userLevelInfo, nil
}

// getPresetMessage 获取预设消息
func getPresetMessage() ([]string, error) {
	messages, err := presetmessage.FindAllMessages()
	if err != nil {
		return nil, err
	}
	if len(messages) <= presetMessageCount {
		return messages, nil
	}
	// 随机获取指定数量的消息
	indices := rand.Perm(len(messages))
	result := make([]string, presetMessageCount)
	for i := 0; i < presetMessageCount; i++ {
		result[i] = messages[indices[i]]
	}
	return result, nil
}

// getNobleHorn 获取贵族喇叭信息
func getNobleHorn(c *handler.Context, nobleHornNum int64) (*hornItem, error) {
	// 检查贵族身份
	uv, err := vip.UserActivatedVip(c.UserID(), false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	nobleLevel := 0
	title := "贵族全站喇叭"
	num, expireTime := int64(0), int64(0)
	lock := lock
	if uv != nil && (((uv.Type == vip.TypeLiveNoble || uv.Type == vip.TypeLiveTrialNoble) && uv.Level >= vip.NobleLevel4) || uv.Type == vip.TypeLiveHighness) {
		// 四级及以上普通贵族和上神贵族可以展示对应等级全站喇叭信息
		nobleLevel = uv.Level
		lock = unlock
		title = fmt.Sprintf("%s全站喇叭", uv.Title)
		num = nobleHornNum
		// 贵族喇叭存放的有效期时间戳为 23:59:59, 统一返回整点
		expireTime = uv.ExpireTime + 1
	}
	bubble, err := params.FindNobleBubble(uv)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var icon string
	if bubble == nil {
		logger.WithField("noble_level", nobleLevel).Error("bubble not found")
		// PASS
	} else {
		icon = bubble.Icon
	}

	horn := hornItem{
		Type:       userstatus.HornTypeNoble,
		Lock:       lock,
		Title:      title,
		IconURL:    storage.ParseSchemeURL(icon),
		Num:        num,
		ExpireTime: expireTime,
	}
	if num == 0 {
		// 不看当前贵族等级，只要当前没有喇叭，就下发开通提示
		horn.Intro = config.Conf.Params.NobleParams.HornAccessIntro
	}
	return &horn, nil
}

// getBlackCardHorn 获取黑卡喇叭信息
func getBlackCardHorn(userBlackCard *liveuserblackcard.UserBlackCardInfo, blackCardHornNum int64) (*hornItem, error) {
	blackCardLevel := 0
	lock := lock
	title := "星曜全站喇叭"
	num, expireTime := int64(0), int64(0)
	if userBlackCard != nil && (userBlackCard.Level >= liveuserblackcard.MinBlackCardLevelHasHorn) {
		blackCardLevel = userBlackCard.Level
		lock = unlock
		title = fmt.Sprintf("%s 全站喇叭", userBlackCard.Title)
		expireTime = userBlackCard.ExpireTime
		num = blackCardHornNum
	}
	bubble, err := params.FindBlackCardBubble(userBlackCard)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var icon string
	if bubble == nil {
		logger.WithField("black_card_level", blackCardLevel).Error("bubble not found")
		// PASS
	} else {
		icon = bubble.Icon
	}
	horn := hornItem{
		Type:         userstatus.HornTypeBlackCard,
		Lock:         lock,
		Title:        title,
		IconURL:      storage.ParseSchemeURL(icon),
		Num:          num,
		ExpireTime:   expireTime,
		IntroOpenURL: confparam.BlackCardURL(true),
	}
	if num == 0 {
		// 不看当前黑卡等级，只要当前没有喇叭，就下发开通提示
		horn.Intro = config.Conf.Params.BlackCard.HornAccessIntro
	}
	if blackCardLevel == liveuserblackcard.BlackCardLevelHasLiveHorn {
		horn.LabelIconURL = storage.ParseSchemeURL(config.Conf.Params.BlackCard.HornEffectLabelIconURL)
	}
	return &horn, nil
}

// sortHornItems 排序。排序规则为：1: 优先展示当前可用的喇叭；2: 优先展示黑卡喇叭
func sortHornItems(nobleHorn, blackCardHorn hornItem) []hornItem {
	hornItems := []hornItem{blackCardHorn, nobleHorn}
	if blackCardHorn.Num == 0 && nobleHorn.Num > 0 {
		hornItems = []hornItem{nobleHorn, blackCardHorn}
	}
	return hornItems
}
