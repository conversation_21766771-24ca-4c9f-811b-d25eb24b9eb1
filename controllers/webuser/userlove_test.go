package webuser

import (
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/liveranklove"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

var testOpeningRoomID = int64(3192516)

func TestRankLove(t *testing.T) {
	defer func() {
		liveRankLoveGetLatest = liveranklove.GetLatest
	}()
	assert := assert.New(t)
	require := require.New(t)

	var (
		list []liveranklove.Model
		erro error
	)
	liveRankLoveGetLatest = func() ([]liveranklove.Model, error) {
		return list, erro
	}
	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/rank/love", false, nil)
	resp, err := ActionRankLove(c)
	require.NoError(err)
	assert.Zero(*resp.(*rankLove))

	erro = errors.New("mock db error")
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/rank/love", false, nil)
	_, err = ActionRankLove(c)
	assert.EqualError(err, "服务器内部错误: mock db error")

	erro = nil
	roomID, _ := room.FindRoomID(10)
	list = []liveranklove.Model{
		{
			UserID: 10,
			Month:  201912,
			RoomID: roomID,
		},
		{
			UserID: 11,
			Month:  201912,
			RoomID: testOpeningRoomID,
		},
		{
			UserID: 12,
			Month:  201912,
		},
	}
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/rank/loveselection", true, nil)

	result, err := ActionRankLoveSelection(c)
	require.NoError(err)

	result2, err := ActionRankLoveSelection(c)
	require.NoError(err)

	assert.Equal(result, result2)

	resp, err = ActionRankLove(c)
	require.NoError(err)
	result3 := resp.(*rankLove)
	assert.Equal(result3.CreatorID, result.(int64))

	// 判断主播直播状态，status.open 字段是否读到了
	assert.Equal(1, list[1].Status.Open)
}

func TestYearMonthStr(t *testing.T) {
	assert := assert.New(t)

	assert.Equal("", yearMonthStr(012345))
	assert.Equal("", yearMonthStr(-12345))
	assert.Equal("2019-12", yearMonthStr(201912))
}
