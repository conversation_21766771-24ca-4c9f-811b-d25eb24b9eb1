package webuser

import (
	"fmt"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	"github.com/MiaoSiLa/live-service/models/mysql/withdrawalrecord"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/balance"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRevenue(t *testing.T) {
	require := require.New(t)

	creatorID := int64(44444)
	timeStamp := goutil.TimeNow().Unix()
	iv := strconv.FormatInt(timeStamp, 10)
	acc := &accountinfo.AccountInfo{
		UserID:       creatorID,
		RealName:     "Real Name",
		Mobile:       goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, "***********"),
		IDNumber:     goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, "123456789012345678"),
		Bank:         "南京银行",
		BankAccount:  goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, "**********"),
		Type:         accountinfo.TypeBank,
		CreateTime:   timeStamp,
		ModifiedTime: timeStamp,
	}
	require.NoError(acc.DB().Create(acc).Error)
	defer require.NoError(acc.DB().Where("id = ?", acc.ID).Error)
	require.NoError(service.DB.Table(livecontract.TableName()).Delete("", "live_id = ?", creatorID).Error)

	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/revenue", true, nil)
	c.User().ID = creatorID
	resp, err := ActionRevenue(c)
	require.NoError(err)
	require.Equal(creatorTypeSingle, resp.(*revenueResp).CreatorType)
}

func TestIsWithinWithdrawTime(t *testing.T) {
	require := require.New(t)
	config.Conf.AB["open_withdraw_revenue_entrance"] = false

	// 处于提现期内
	tm := time.Date(2020, 10, 3, 10, 0, 0, 0, time.Local)
	require.True(isWithinWithdrawTime(tm))

	// 不处于提现期内
	tm = time.Date(2020, 10, 7, 10, 0, 0, 0, time.Local)
	require.False(isWithinWithdrawTime(tm))

	// 手动打开提现入口
	config.Conf.AB["open_withdraw_revenue_entrance"] = true
	require.True(isWithinWithdrawTime(tm))
}

func TestLoadCreatorType(t *testing.T) {
	require := require.New(t)

	// 素人主播
	rr := new(revenueResp)
	guildID, err := rr.loadCreatorType(111111111)
	require.NoError(err)
	require.Zero(guildID)
	require.Equal(creatorTypeSingle, rr.CreatorType)

	// 公会主播
	lc := &livecontract.LiveContract{
		LiveID:      38577770,
		GuildID:     385,
		Status:      livecontract.StatusContracting,
		ContractEnd: goutil.TimeNow().Add(5 * time.Minute).Unix(),
	}
	require.NoError(service.DB.Create(lc).Error)
	defer func() {
		require.NoError(service.DB.Table(lc.TableName()).Delete("", "id = ?", lc.ID).Error)
	}()

	guildID, err = rr.loadCreatorType(lc.LiveID)
	require.NoError(err)
	require.Equal(lc.GuildID, guildID)
	require.Equal(creatorTypeGuild, rr.CreatorType)
}

func TestLoadEntranceStatus(t *testing.T) {
	require := require.New(t)

	config.Conf.AB["open_withdraw_revenue_entrance"] = false
	rr := new(revenueResp)

	// 非提现期内不可提现
	goutil.SetTimeNow(func() time.Time {
		tm, err := time.Parse("2006-01-02 15:04:05", "2020-11-11 05:11:25")
		if err != nil {
			require.NoError(err)
		}
		return tm
	})
	defer goutil.SetTimeNow(nil)
	err := rr.loadEntranceStatus()
	require.NoError(err)
	require.Equal(entranceStatusNotWithdrawTime, rr.EntranceStatus)

	// 提现期内可提现
	goutil.SetTimeNow(func() time.Time {
		tm, err := time.Parse("2006-01-02 15:04:05", "2020-11-02 05:11:25")
		if err != nil {
			require.NoError(err)
		}
		return tm
	})
	err = rr.loadEntranceStatus()
	require.NoError(err)
	require.Equal(entranceStatusCanWithdraw, rr.EntranceStatus)
}

func TestLoadBankInfo(t *testing.T) {
	require := require.New(t)

	rr := new(revenueResp)
	// 银行卡信息为空
	err := rr.loadBankInfo(************)
	require.NoError(err)
	require.Equal(bankInfoStatusEmpty, rr.BankInfoStatus)

	// 银行卡信息正确
	timeStamp := goutil.TimeNow().Unix()
	idNumber := "123456789012345678"
	bankAccount := "**********"
	mobile := "***********"
	iv := strconv.FormatInt(timeStamp, 10)
	acc := &accountinfo.AccountInfo{
		UserID:       33333,
		RealName:     "Real Name",
		Mobile:       goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, mobile),
		IDNumber:     goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, idNumber),
		Bank:         "南京银行",
		BankAccount:  goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, bankAccount),
		Type:         accountinfo.TypeBank,
		CreateTime:   timeStamp,
		ModifiedTime: timeStamp,
	}
	require.NoError(acc.DB().Create(acc).Error)
	defer require.NoError(acc.DB().Where("id = ?", acc.ID).Error)

	_, err = service.Redis.SRem(keys.KeyUserIDsWrongBankInfo0.Format(), acc.UserID).Result()
	require.NoError(err)
	err = rr.loadBankInfo(acc.UserID)
	require.NoError(err)
	require.Equal(bankInfoStatusCorrect, rr.BankInfoStatus)

	// 银行卡信息错误
	numAffected, err := service.Redis.SAdd(keys.KeyUserIDsWrongBankInfo0.Format(), acc.UserID).Result()
	require.NoError(err)
	require.Equal(int64(1), numAffected)
	err = rr.loadBankInfo(acc.UserID)
	require.NoError(err)
	require.Equal(bankInfoStatusError, rr.BankInfoStatus)
}

func TestGetSingleCreatorRevenue(t *testing.T) {
	orginalNewADB := service.NewADB
	service.NewADB = transactionlog.DB()
	defer func(db *gorm.DB) {
		service.NewADB = db
	}(orginalNewADB)

	require := require.New(t)

	creatorID := int64(33333)

	defer require.NoError(balance.Balance{}.DB().Delete("", "id = ?", creatorID).Error)
	require.NoError(
		balance.Balance{}.DB().Create(&balance.Balance{
			ID:            creatorID,
			LiveProfit:    3000 * 100,
			NewLiveProfit: 1000 * 100,
		}).Error,
	)
	defer require.NoError(transactionlog.DB().Delete("", "to_id = ? AND type = ?", creatorID, transactionlog.TypeLive).Error)
	now := goutil.TimeNow()
	y, m, _ := now.Date()
	timeStamp := time.Date(y, m, 1, 0, 0, 0, 0, now.Location()).Unix()
	require.NoError(transactionlog.DB().Create(&transactionlog.TransactionLog{
		Title:        "测试主播获取收益",
		Income:       110,
		Tax:          10,
		Rate:         0.5,
		ToID:         creatorID,
		Type:         transactionlog.TypeLive,
		Status:       transactionlog.StatusSuccess,
		CTime:        timeStamp,
		CreateTime:   timeStamp,
		ModifiedTime: timeStamp,
		ConfirmTime:  timeStamp,
	}).Error)
	revenue, err := getSingleCreatorRevenue(creatorID)
	require.NoError(err)

	require.Equal(util.Float2DP(3000), revenue.FrozenLiveProfit)
	require.Equal(util.Float2DP(1000), revenue.LiveProfit)
	require.Equal(util.Float2DP(1000-(110-10)*0.5), revenue.DrawableLiveProfit)
}

func TestGetGuildCreatorRevenue(t *testing.T) {
	// TODO
}

func TestLoadWithdrawLimit(t *testing.T) {
	require := require.New(t)

	creatorID := int64(33333)

	err := service.Redis.Set(keys.KeyMinWithdrawValueLimit0.Format(), 299, time.Minute).Err()
	require.NoError(err)

	defer require.NoError(service.Redis.SRem(keys.KeyUserIDsWithoutWithdrawLevelOneLimit0.Format(), creatorID).Err())
	require.NoError(err)
	numAffected, err := service.Redis.SAdd(keys.KeyUserIDsWithoutWithdrawLevelOneLimit0.Format(), creatorID).Result()
	require.NoError(err)
	require.Equal(int64(1), numAffected)

	defer require.NoError(withdrawalrecord.WithdrawalRecord{}.DB().Delete("", "user_id = ?", creatorID).Error)
	require.NoError(
		withdrawalrecord.WithdrawalRecord{}.DB().Create(&withdrawalrecord.WithdrawalRecord{
			UserID:     creatorID,
			AccountID:  11,
			Profit:     4000,
			Status:     withdrawalrecord.StatusConfirm,
			Type:       withdrawalrecord.TypeWithDrawGuildCreator,
			CreateTime: goutil.TimeNow().Unix(),
		}).Error,
	)
	require.NoError(
		withdrawalrecord.WithdrawalRecord{}.DB().Create(&withdrawalrecord.WithdrawalRecord{
			UserID:     creatorID,
			AccountID:  11,
			Profit:     50000.5,
			Status:     withdrawalrecord.StatusCreate,
			Type:       withdrawalrecord.TypeWithDrawGuildCreator,
			CreateTime: goutil.TimeNow().Unix(),
		}).Error,
	)

	rr := new(revenueResp)
	err = rr.loadWithdrawLimit(creatorID)
	require.NoError(err)
	require.Equal(util.Float2DP(299), rr.MinWithdrawalLimit)
	require.Equal(maxWithdrawValueLevelTwo, rr.MaxWithdrawalLimit)
	require.Equal(maxWithdrawValueLevelTwo-50000.5, rr.RemainingWithdrawalLimit)

	require.NoError(
		withdrawalrecord.WithdrawalRecord{}.DB().Create(&withdrawalrecord.WithdrawalRecord{
			UserID:     creatorID,
			AccountID:  11,
			Profit:     float64(maxWithdrawValueLevelTwo + 1000),
			Status:     withdrawalrecord.StatusCreate,
			Type:       withdrawalrecord.TypeWithDrawGuildCreator,
			CreateTime: goutil.TimeNow().Unix(),
		}).Error,
	)
	err = rr.loadWithdrawLimit(creatorID)
	require.NoError(err)
	require.Equal(util.Float2DP(0), rr.RemainingWithdrawalLimit)
}

func TestActionRevenuePlay(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testToID   = int64(********)
		testFromID = int64(12)
	)
	require.NoError(transactionlog.DB().Delete("", "to_id = ?", testToID).Error)
	c := handler.NewTestContext(http.MethodGet, "/api/v2/user/revenue", true, nil)
	c.User().ID = testToID
	resp, err := ActionRevenuePlay(c)
	require.NoError(err)
	res := resp.(revenuePlayResp)
	assert.Empty(res.Data)
	assert.False(res.Pagination.HasMore)

	nowUnix := goutil.TimeNow().Unix()
	questionLog := transactionlog.TransactionLog{
		Title:        "测试玩法-提问收益",
		AllCoin:      30,
		ToID:         testToID,
		FromID:       testFromID,
		GiftID:       0,
		Type:         transactionlog.TypeLive,
		Status:       transactionlog.StatusSuccess,
		Attr:         transactionlog.AttrCommon,
		CTime:        nowUnix,
		CreateTime:   nowUnix,
		ModifiedTime: nowUnix,
		ConfirmTime:  nowUnix,
	}
	require.NoError(transactionlog.DB().Create(&questionLog).Error)
	c = handler.NewTestContext(http.MethodGet, "/api/v2/user/revenue?page_size=1", true, nil)
	c.User().ID = testToID
	resp, err = ActionRevenuePlay(c)
	require.NoError(err)
	res = resp.(revenuePlayResp)
	require.Len(res.Data, 1)
	assert.Equal(revenueTypeQuestion, res.Data[0].Type)
	assert.Equal(testFromID, res.Data[0].UserID)
	assert.Equal(int64(30), res.Data[0].Price)
	assert.Equal(nowUnix, res.Data[0].ConfirmTime)
	assert.False(res.Pagination.HasMore)

	ts := []*transactionlog.TransactionLog{
		{
			Title:        "奇喵妙旅",
			AllCoin:      1000,
			ToID:         testToID,
			FromID:       testFromID,
			Type:         transactionlog.TypeLive,
			Num:          10,
			Status:       transactionlog.StatusSuccess,
			Attr:         transactionlog.AttrLiveBuyLuckyBox,
			CTime:        nowUnix,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			ConfirmTime:  nowUnix - 1,
		},
		{
			Title:        "测试玩法-付费弹幕收益-1",
			AllCoin:      100,
			ToID:         testToID,
			FromID:       testFromID,
			Type:         transactionlog.TypeGuildLive,
			Status:       transactionlog.StatusSuccess,
			Attr:         transactionlog.AttrLiveDanmaku,
			CTime:        nowUnix,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			ConfirmTime:  nowUnix - 2,
		},
		{
			Title:        "测试玩法-付费弹幕收益-2",
			AllCoin:      1000,
			ToID:         testToID,
			FromID:       testFromID,
			Type:         transactionlog.TypeLive,
			Status:       transactionlog.StatusSuccess,
			Attr:         transactionlog.AttrLiveDanmaku,
			CTime:        nowUnix,
			CreateTime:   nowUnix,
			ModifiedTime: nowUnix,
			ConfirmTime:  nowUnix - 2,
		},
	}
	require.NoError(servicedb.BatchInsert(transactionlog.DB(), transactionlog.TransactionLog{}.TableName(), ts))
	marker := buildRevenuePlayMarker(questionLog.ConfirmTime, questionLog.ID)
	c = handler.NewTestContext(http.MethodGet, fmt.Sprintf("/api/v2/user/revenue?page_size=2&marker=%s", marker), true, nil)
	c.User().ID = testToID
	resp, err = ActionRevenuePlay(c)
	require.NoError(err)
	res = resp.(revenuePlayResp)
	require.Len(res.Data, 2)
	assert.Equal(revenueTypeLuckyBox, res.Data[0].Type)
	assert.Equal(testFromID, res.Data[0].UserID)
	assert.Equal(int64(1000), res.Data[0].Price)
	require.NotNil(res.Data[0].Num)
	assert.Equal(int64(10), *res.Data[0].Num)
	require.NotNil(res.Data[0].Title)
	assert.Equal("奇喵妙旅", *res.Data[0].Title)
	assert.Equal(nowUnix-1, res.Data[0].ConfirmTime)
	assert.Equal(revenueTypeDanmaku, res.Data[1].Type)
	assert.Equal(testFromID, res.Data[1].UserID)
	assert.Equal(int64(1000), res.Data[1].Price)
	assert.Equal(nowUnix-2, res.Data[1].ConfirmTime)
	assert.True(res.Pagination.HasMore)
	assert.NotEmpty(res.Pagination.Marker)
}
