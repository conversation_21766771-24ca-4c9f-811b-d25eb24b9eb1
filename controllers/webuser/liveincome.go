package webuser

import (
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionLiveIncome 主播工作台素人收益记录
/**
 * @api {get} /api/v2/user/liveincome 主播工作台素人收益记录
 * @apiVersion 0.1.0
 * @apiName liveflow
 * @apiGroup /api/v2/user
 *
 * @apiParam {number=0,1,2,3} [type=0] 收益类型，0：礼物收益，1：贵族收益，2：超粉收益，3：玩法收益
 * @apiParam {String} start_date 开始时间（例 2019-05-01）
 * @apiParam {String} end_date 结束时间（例 2019-05-15）
 * @apiParam {Number} [p=1] 第几页
 * @apiParam {Number} [pagesize=20] 每页个数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *     {
 *       "code": 0,
 *       "info": {
 *         "total": 1981.09,  // 收益总额（单位：元），仅第一页时返回
 *         "data": [{
 *           "user_id": 3456854,
 *           "username": "名字比较长的子辰",
 *           "title": "礼物--草坪婚礼",
 *           "revenue": 731.07,  // 金额（单位：元）
 *           "status": 1,  // 状态：1 交易成功
 *           "confirm_time": 1589427036  // 秒级时间戳
 *         },
 *         {
 *           "user_id": 3456866,
 *           "username": "猫嘚",
 *           "title": "礼物--草坪婚礼",
 *           "revenue": 731.07,
 *           "status": 1,
 *           "confirm_time": 1589426514
 *         }],
 *         "pagination": {
 *           "count": 1,
 *           "maxpage": 1,
 *           "p": 1,
 *           "pagesize": 20
 *         }
 *       }
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
func ActionLiveIncome(ctx *handler.Context) (handler.ActionResponse, error) {
	var param utils.CreatorIncomeListParam
	if err := param.LoadCommonParams(ctx); err != nil {
		return nil, err
	}

	return param.CreatorIncomeList(ctx.UserID(), 0, true)
}
