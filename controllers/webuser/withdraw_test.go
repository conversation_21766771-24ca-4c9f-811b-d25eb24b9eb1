package webuser

import (
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/accountinfo"
	"github.com/MiaoSiLa/live-service/models/mysql/withdrawalrecord"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/models/balance"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

var (
	once         sync.Once
	sqliteDB     *gorm.DB
	sqliteLiveDB *gorm.DB

	originalDB     *gorm.DB
	originalLiveDB *gorm.DB
)

func switchToSqlite() func() {
	once.Do(func() {
		originalDB = service.DB
		sqliteDB = tutil.NewSqlite(&tutil.ConfigSqlite{
			DBFile:    "test.db",
			QueryFile: "../../testdata/test.sql",
		}, nil)

		originalLiveDB = service.LiveDB
		sqliteLiveDB = tutil.NewSqlite(&tutil.ConfigSqlite{
			DBFile:    "missevan_live.db",
			QueryFile: "../../testdata/missevan_live.sql",
		}, sqliteLiveDB)
	})
	service.DB = sqliteDB
	service.LiveDB = sqliteLiveDB
	servicedb.Driver = servicedb.DriverSqlite
	return func() {
		service.DB = originalDB
		service.LiveDB = originalLiveDB
		servicedb.Driver = servicedb.DriverMysql
	}
}

func mockAppRPCServer() func() {
	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"/financial/withdrawal": handler.NewAction(handler.POST, func(c *handler.Context) (response handler.ActionResponse, e error) {
				return handler.M{
					"msg": "提现申请已提交",
					"id":  3321,
				}, nil
			}, false),
		},
	}
	addr := tutil.RunMockServer(r, 28017, &h)

	appRPCEntryOriginal := service.MRPC.Config["app"]
	service.MRPC.Config["app"] = mrpc.ConfigEntry{
		URL: "http://" + addr + "/",
		Key: "testkey",
	}
	return func() {
		service.MRPC.Config["app"] = appRPCEntryOriginal
	}
}

func TestActionWithdraw(t *testing.T) {
	cleanup := mockAppRPCServer()
	defer cleanup()

	require := require.New(t)

	creatorID := int64(55555)
	timeStamp := goutil.TimeNow().Unix()
	iv := strconv.FormatInt(timeStamp, 10)
	acc := &accountinfo.AccountInfo{
		UserID:       creatorID,
		RealName:     "Real Name",
		Mobile:       goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, "***********"),
		IDNumber:     goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, "123456789012345678"),
		Bank:         "南京银行",
		BankAccount:  goutil.Encrypt(config.Conf.Params.Security.SensitiveInformationKey, iv, "**********"),
		Type:         accountinfo.TypeBank,
		CreateTime:   timeStamp,
		ModifiedTime: timeStamp,
	}
	require.NoError(accountinfo.AccountInfo{}.DB().Create(acc).Error)
	defer require.NoError(accountinfo.AccountInfo{}.DB().Where("id = ?", acc.ID).Error)
	require.NoError(service.DB.Table(livecontract.TableName()).Delete("", "live_id = ?", creatorID).Error)

	defer require.NoError(balance.Balance{}.DB().Delete("", "id = ?", creatorID).Error)
	require.NoError(
		balance.Balance{}.DB().Create(&balance.Balance{
			ID:            creatorID,
			LiveProfit:    1000 * 100,
			NewLiveProfit: 2000 * 100,
		}).Error,
	)
	require.NoError(transactionlog.DB().Delete("", "to_id = ?", creatorID).Error)
	defer require.NoError(withdrawalrecord.WithdrawalRecord{}.DB().Delete("", "user_id = ?", creatorID).Error)

	c := handler.NewTestContext(http.MethodPost, "/api/v2/user/withdraw", true, fmt.Sprintf("&type=single&profit=200&account_id=%d", acc.ID))
	c.User().ID = creatorID

	goutil.SetTimeNow(func() time.Time {
		tm, err := time.Parse("2006-01-02 15:04:05", "2020-11-11 05:11:25")
		if err != nil {
			require.NoError(err)
		}
		return tm
	})
	defer goutil.SetTimeNow(nil)
	_, err := ActionWithdraw(c)
	require.Error(actionerrors.NewErrForbidden("每月仅 1 ~ 3 号开放提现申请入口"), err)

	goutil.SetTimeNow(func() time.Time {
		tm, err := time.Parse("2006-01-02 15:04:05", "2020-11-02 12:00:25")
		if err != nil {
			require.NoError(err)
		}
		return tm
	})
	err = service.Redis.Set(keys.KeyMinWithdrawValueLimit0.Format(), "999", 5*time.Minute).Err()
	require.NoError(err)
	_, err = ActionWithdraw(c)
	require.Error(actionerrors.NewErrForbidden("提现金额最少为 999.00 元"), err)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/user/withdraw", true, fmt.Sprintf("&type=single&profit=1000&account_id=%d", acc.ID))
	c.User().ID = creatorID
	resp, err := ActionWithdraw(c)
	require.NoError(err)
	r := resp.(handler.M)
	require.Equal("提现申请已提交", r["msg"])
}
