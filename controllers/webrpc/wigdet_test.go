package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionWidgetRefresh(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("im://broadcast", func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	c := handler.NewTestContext("POST", "", false, nil)
	_, err := ActionWidgetRefresh(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("POST", "", false, map[string]interface{}{
		"event_id": 196,
		"room_id":  12,
	})
	_, err = ActionWidgetRefresh(c)
	require.NoError(err)
}
