package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionHomepage(t *testing.T) {
	c := handler.NewRPCTestContext("/recommended/homepage", nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS

	r, _, err := ActionRecommendedHomepage(c)
	require.NoError(t, err)
	data, ok := r.(map[string]any)
	require.True(t, ok)
	assert.Len(t, data["rooms"], 3)
}
