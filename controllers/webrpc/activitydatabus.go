package webrpc

import (
	"encoding/json"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/databus"
)

type databusDelayAddParams struct {
	Key       string `json:"key"`
	Message   string `json:"message"`
	DeliverAt int64  `json:"deliver_at"`
}

// ActionActivityDatabusSendDelay 向 Databus 延时队列添加活动用的消息
/**
 * @api {post} /rpc/activity/databus/senddelay 向 Databus 延时队列添加活动用的消息
 * @apiDescription 向 Databus 延时队列添加活动用的消息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {String} key databus key (避免传入过长的 key) 延时队列 key 不支持冒号
 * @apiParam {String} message 消息内容
 * @apiParam {Number} [deliver_at=0] 延时触发时间 (秒级时间戳)
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionActivityDatabusSendDelay(c *handler.Context) (handler.ActionResponse, error) {
	param := new(databusDelayAddParams)
	err := c.BindJSON(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Key == "" || param.Message == "" || param.DeliverAt < 0 {
		return nil, actionerrors.ErrParams
	}
	key := keys.DelayDatabusKeyActivityRPC1.Format(param.Key)
	message := userapi.ActivityDatabusMessage{
		Key:     param.Key,
		Message: param.Message,
	}
	if param.DeliverAt == 0 {
		err = service.DatabusDelayPubSend(key, message)
	} else {
		deliverAt := time.Unix(param.DeliverAt, 0)
		err = service.DatabusSendDelay(key, message, deliverAt)
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

// DelayActivityDatabusOperator 活动消费者
func DelayActivityDatabusOperator() func(*databus.Message) {
	return func(m *databus.Message) {
		if !keys.DelayDatabusKeyActivityRPC1.MatchKey(m.Key) {
			return
		}
		var message userapi.ActivityDatabusMessage
		err := json.Unmarshal(m.Value, &message)
		if err != nil {
			logger.Error(err)
			return
		}
		if err = userapi.ActivityDatabusDelayMessage(message); err != nil {
			logger.Error(err)
			// PASS
		}
	}
}
