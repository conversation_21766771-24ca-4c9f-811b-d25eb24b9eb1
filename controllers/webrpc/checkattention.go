package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type checkAttentionInput struct {
	CheckUserIDs []int64 `json:"check_user_ids"`
	UserID       int64   `json:"user_id"`
}

// ActionCheckAttention handler
func ActionCheckAttention(c *handler.Context) (handler.ActionResponse, error) {
	var input checkAttentionInput
	err := c.BindJSON(&input)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	if len(input.CheckUserIDs) < 1 {
		return nil, actionerrors.ErrParams
	}
	attentions, err := attentionuser.CheckAttention(input.UserID, input.CheckUserIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return attentions, nil
}
