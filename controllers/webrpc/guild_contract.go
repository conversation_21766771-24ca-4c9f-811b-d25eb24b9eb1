package webrpc

import (
	"fmt"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// getContractParam
type getContractParam struct {
	UserID int64 `json:"user_id"`
}

type liveContractResp struct {
	livecontract.LiveContract `json:",inline"`

	RoomID int64 `json:"room_id"`
}

// ActionGuildGetContract 获取用户与公会生效中的合约
/**
 * @api {post} /rpc/guild/get-contract 获取用户与公会生效中的合约
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {  // 当用户与公会不存在生效中的合约时返回 null
 *         "id": 1,
 *         "guild_id": 1897,
 *         "guild_owner": 3013090,
 *         "guild_name": "测试公会",
 *         "live_id": 5003,
 *         "room_id": 5004,
 *         "contract_duration": 0,
 *         "contract_start": 1569378503,
 *         "contract_end": 1999999999,
 *         "rate": 0,
 *         "kpi": "",
 *         "type": 1,
 *         "attr": 0,
 *         "status": 1,
 *         "create_time": 1569378503
 *       }
 *     }
 */
func ActionGuildGetContract(ctx *handler.Context) (handler.ActionResponse, error) {
	var param getContractParam
	err := ctx.BindJSON(&param)
	if err != nil || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	contract, err := livecontract.FindInContractingByLiveID(param.UserID, 0)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if contract == nil {
		return nil, nil
	}

	// 有合约的情况下也不一定会有 room id
	roomID, err := room.FindRoomID(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return buildResp(contract, roomID)
}

func buildResp(contract *livecontract.LiveContract, roomID int64) (*liveContractResp, error) {
	return &liveContractResp{
		LiveContract: *contract,
		RoomID:       roomID,
	}, nil
}

// ActionExpelLiveSilent 清退合约申请手动生效，静默地清退公会主播
/*
 * @api {post} /rpc/guild/terminate/exec-silent 清退合约申请手动生效，静默地清退公会主播
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionExpelLiveSilent(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	// 获取待生效的清退申请列表
	var applymentList []*contractapplyment.ContractApplyment
	err := service.DB.
		Table(contractapplyment.TableName()).
		Select("guild_id, guild_name, live_id").
		Where("expire_time <= ? AND status = ? AND type = ?",
			now.Unix(), contractapplyment.StatusPending, contractapplyment.TypePlatformExpel).
		Scan(&applymentList).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(applymentList) == 0 {
		return "没有清退合约申请可生效，不需要清退主播", nil
	}
	// 执行清退
	goutil.Go(func() {
		expelLives(applymentList, now)
	})
	return "success", nil
}

func expelLives(applyments []*contractapplyment.ContractApplyment, now time.Time) {
	failedLives := make([]int64, 0, len(applyments))
	for _, applyment := range applyments {
		err := expelOneLive(applyment, now)
		if err != nil {
			logger.WithFields(logger.Fields{
				"guild_id": applyment.GuildID,
				"live_id":  applyment.LiveID,
			}).Error(err)
			failedLives = append(failedLives, applyment.LiveID)
		}
	}
	logger.WithFields(logger.Fields{
		"success": len(applyments) - len(failedLives),
		"failure": len(failedLives),
	}).Infof("平台清退公会主播完成。")
	if len(failedLives) != 0 {
		logger.Errorf("部分公会主播清退失败：%v", failedLives)
	}
}

func expelOneLive(applyment *contractapplyment.ContractApplyment, now time.Time) error {
	err := servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 同意申请
		db := tx.Table(contractapplyment.TableName()).
			Where("live_id = ?", applyment.LiveID).
			Where("guild_id = ?", applyment.GuildID).
			Where("status = ? AND type = ?", contractapplyment.StatusPending, contractapplyment.TypePlatformExpel).
			Where("expire_time <= ?", now.Unix()).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusAgreed,
				"process_time":  now.Unix(),
				"modified_time": now.Unix(),
			})
		if db.Error != nil {
			return db.Error
		}
		// 没有修改时代表申请已执行或不存在
		if db.RowsAffected == 0 {
			return fmt.Errorf("清退主播申请冲突")
		}
		// 清退主播
		db = tx.Table(livecontract.TableName()).
			Where("live_id = ?", applyment.LiveID).
			Where("guild_id = ?", applyment.GuildID).
			Where("status = ?", livecontract.StatusContracting).
			Updates(map[string]interface{}{
				"status":        livecontract.StatusUseless,
				"contract_end":  now.Unix(),
				"modified_time": now.Unix(),
			})
		if db.Error != nil {
			return db.Error
		}
		// 没有修改时代表主播已被清退或不存在
		if db.RowsAffected == 0 {
			return fmt.Errorf("清退主播冲突")
		}
		// 未处理的续约、协商解约、降薪申请失效
		err := tx.Table(contractapplyment.TableName()).
			Where("live_id = ? AND guild_id = ? AND status = ?", applyment.LiveID, applyment.GuildID, contractapplyment.StatusPending).
			Where("type IN (?)", []int64{contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew,
				contractapplyment.TypeLiveTerminate, contractapplyment.TypeRateDown}).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusInvalid,
				"process_time":  now.Unix(),
				"modified_time": now.Unix(),
			}).Error
		if err != nil {
			return err
		}
		// 更新主播数冗余字段
		err = guild.IncreaseLiveNum(applyment.GuildID, -1, tx)
		if err != nil {
			return err
		}
		// TODO: 迁移到同一个库后使用 tx
		// 解除经纪人与主播的关系
		err = guildagent.Unassign(applyment.LiveID, applyment.GuildID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"guild_id": applyment.GuildID,
				"live_id":  applyment.LiveID,
			}).Error(err)
			// PASS
		}
		err = guildscheduleapply.AfterQuitGuild(applyment.GuildID, []int64{applyment.LiveID})
		if err != nil {
			logger.WithFields(logger.Fields{
				"guild_id": applyment.GuildID,
				"live_id":  applyment.LiveID,
			}).Error(err)
			// PASS
		}
		return nil
	})
	return err
}
