package webrpc

import (
	"encoding/json"
	"slices"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func keyTodayDisplayTimes(userID int64) string {
	return keys.KeyUserLiveFeedTodayDisplayTimes2.Format(goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace), userID)
}

// liveFeedParams 请求直播动态参数
type liveFeedParams struct {
	UserID int64 `json:"user_id"`

	displayTwiceCreatorIDs []int64 // 今日展示过两次的主播 ID
	todayDisplayTimesKey   string

	resp *attentionFeed
}

// attentionFeed 用户关注的主播的直播动态
type attentionFeed struct {
	UserID        int64  `json:"user_id"`
	Username      string `json:"username"`
	IconURL       string `json:"iconurl"`
	Confirm       uint   `json:"confirm"`
	Title         string `json:"title"`
	Status        int    `json:"status"`
	LiveStartTime int64  `json:"live_start_time"`
	RoomID        int64  `json:"room_id"`
	CoverURL      string `json:"cover_url"`
	CatalogID     int64  `json:"catalog_id"`
	CatalogName   string `json:"catalog_name"`
	CatalogColor  string `json:"catalog_color"`
}

// ActionLiveFeed 获取用户直播动态
/**
 * @api {post} /rpc/user/live/feed 获取用户直播动态
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": { // 没有直播动态时，该值为 null
 *       "user_id": 1, // 主播 ID
 *       "title": "直播间标题", // 直播间标题
 *       "status": 1, // 直播间状态（1: 开播中）
 *       "live_start_time": 1653326985, // 开播时间（单位：秒）
 *       "room_id": 100000, // 直播间 ID
 *       "cover_url": "https://static-test.missevan.com/icon01.png", // 直播间封面图
 *       "catalog_id": 14, // 直播分区 ID
 *       "catalog_name": "直播分区名称", // 直播分区名称
 *       "catalog_color": "#6FEFEC", // 直播间 catalog 的颜色
 *       "username": "主播昵称", // 主播昵称
 *       "iconurl": "https://static-test.missevan.com/avatars/icon01.png", // 主播头像
 *       "confirm": 2 // confirm 调用方根据 confirm 和客户端版本号获取加 V 标识
 *     }
 *   }
 */
func ActionLiveFeed(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newLiveFeedParams(c)
	if err != nil {
		return nil, err
	}

	// 获取用户直播动态数据
	err = p.liveFeed()
	if err != nil {
		return nil, err
	}

	// 增加主播今日展示次数
	p.incrTodayDisplayTimes()

	return p.resp, nil
}

func newLiveFeedParams(c *handler.Context) (*liveFeedParams, error) {
	p := new(liveFeedParams)
	err := c.Bind(p)
	if err != nil || p.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	p.todayDisplayTimesKey = keyTodayDisplayTimes(p.UserID)

	return p, nil
}

func (p *liveFeedParams) liveFeed() error {
	// 查询缓存
	key := keys.KeyUserLiveFeed1.Format(p.UserID)
	val, err := service.LRURedis.Get(key).Result()
	var rebuildCache bool
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		// 如果缓存不存在，需要生成缓存
		rebuildCache = true
	}
	if val != "" {
		err = json.Unmarshal([]byte(val), &p.resp)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if p.resp == nil {
			return nil
		}
	}

	// 查询今日展示次数大于等于 2 的主播的用户 ID（重新生成缓存时需要排除掉）
	displayTwiceCreatorIDs, err := service.Redis.ZRangeByScore(p.todayDisplayTimesKey, &redis.ZRangeBy{Min: "2", Max: "+inf"}).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(displayTwiceCreatorIDs) > 0 {
		for _, idStr := range displayTwiceCreatorIDs {
			userID, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				logger.WithField("user_id", idStr).Error(err)
				// PASS
				continue
			}
			if p.resp != nil && p.resp.UserID == userID {
				// 如果该主播动态今日展示次数大于等于 2 则不再展示，需要重新生成缓存
				rebuildCache = true
				p.resp = nil
			}
			p.displayTwiceCreatorIDs = append(p.displayTwiceCreatorIDs, userID)
		}
	}

	if !rebuildCache {
		return nil
	}

	lock := keys.LockUserLiveFeed1.Format(p.UserID)
	// 防止用户操作过快多次查库
	ok, err := service.Redis.SetNX(lock, 1, time.Second).Result()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": p.UserID}).Error(err)
		// PASS
		return nil
	}
	if !ok {
		// 加锁失败直接返回 nil
		return nil
	}
	defer service.Redis.Del(lock)

	followRoom, err := p.findLiveFeed()
	if err != nil {
		return err
	}
	if followRoom != nil {
		p.resp = &attentionFeed{
			UserID:        followRoom.CreatorID,
			Title:         followRoom.Name,
			Status:        followRoom.Status.Open,
			LiveStartTime: followRoom.Status.OpenTime / 1000,
			RoomID:        followRoom.RoomID,
			CoverURL:      followRoom.CoverURL,
			CatalogID:     followRoom.CatalogID,
			CatalogName:   followRoom.CatalogName,
			CatalogColor:  followRoom.CatalogColor,
		}

		// 获取用户信息
		u, err := mowangskuser.FindByUserID(p.resp.UserID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if u != nil {
			p.resp.IconURL = u.IconURL
			p.resp.Username = u.Username
			p.resp.Confirm = u.Confirm
		} else {
			// 查询不到用户信息时，生成空缓存
			p.resp = nil
		}
	}

	bytes, err := json.Marshal(p.resp)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// p.resp 为 nil 会生成 null 的缓存，防止没有直播动态时每次都需要查库重新生成缓存
	err = service.LRURedis.Set(key, bytes, 10*time.Minute).Err()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (p *liveFeedParams) findLiveFeed() (*room.Simple, error) {
	followRooms, err := p.listFollowFeed()
	if err != nil {
		return nil, err
	}
	l := len(followRooms)
	if l == 0 {
		return nil, nil
	}
	if l == 1 {
		return followRooms[0], nil
	}
	roomMap := make(map[int64]*room.Simple, len(followRooms))
	creatorIDs := make([]int64, 0, len(followRooms))
	for _, followRoom := range followRooms {
		creatorIDs = append(creatorIDs, followRoom.CreatorID)
		roomMap[followRoom.CreatorID] = followRoom
	}

	// 获取用户与主播的亲密度, 按照亲密度由高到低排序
	filter := bson.M{
		"user_id":    p.UserID,
		"creator_id": bson.M{"$in": creatorIDs},
	}
	// 使用 bson.M 有可能出现排序顺序出错
	pointSort := bson.D{bson.E{Key: "point", Value: -1}}
	opt := options.Find().SetSort(pointSort)
	medals, err := livemedal.FindSimples(filter, opt, false, false)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(medals) == 0 {
		// 若没有亲密度，则取直播间热度最高的一条数据（followRoom 已经按直播间热度倒序排序）
		return followRooms[0], nil
	}
	// 有亲密度时取亲密度最高的数据
	maxPoint := medals[0].Point
	afs := make([]*room.Simple, 0, len(medals))
	for _, medal := range medals {
		if medal.Point != maxPoint {
			// medals 已经是排序之后的数据，medal.Point != maxPoint 说明已经没有亲密度最高的数据，直接 break
			break
		}
		if af := roomMap[medal.CreatorID]; af != nil {
			afs = append(afs, af)
		}
	}

	// 根据亲密度最高的主播 ID 取热度最高的主播
	sort.Slice(afs, func(i, j int) bool {
		return afs[i].Status.Score > afs[j].Status.Score
	})

	return afs[0], nil
}

func (p *liveFeedParams) listFollowFeed() ([]*room.Simple, error) {
	// 查询用户关注的所有主播
	userIDs, err := attentionuser.AllFollowedUserIDs(p.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(p.displayTwiceCreatorIDs) != 0 {
		// 排除今日展示次数大于等于两次的主播
		diffUserIDs := make([]int64, 0, len(userIDs))
		for _, userID := range userIDs {
			if !goutil.HasElem(p.displayTwiceCreatorIDs, userID) {
				diffUserIDs = append(diffUserIDs, userID)
			}
		}
		userIDs = diffUserIDs
	}
	if len(userIDs) == 0 {
		return nil, nil
	}

	// 查询开播时长大于等于 10 分钟的直播间信息（包含直播间分类信息）
	filter := bson.M{
		"creator_id":       bson.M{"$in": userIDs},
		"status.open":      room.StatusOpenTrue,
		"status.open_time": bson.M{"$lt": util.TimeToUnixMilli(goutil.TimeNow().Add(-10 * time.Minute))},
	}
	followRooms, err := room.ListSimples(filter, options.Find().SetSort(room.SortByScore), &room.FindOptions{FindCatalogInfo: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return followRooms, nil
}

func (p *liveFeedParams) incrTodayDisplayTimes() {
	if p.resp == nil {
		return
	}
	pipe := service.Redis.TxPipeline()
	// 该主播动态今日展示次数加 1
	pipe.ZIncrBy(p.todayDisplayTimesKey, 1, strconv.FormatInt(p.resp.UserID, 10))
	pipe.Expire(p.todayDisplayTimesKey, 36*time.Hour)
	_, err := pipe.Exec()
	if err != nil {
		logger.WithFields(logger.Fields{"user_id": p.UserID, "creator_id": p.resp.UserID}).Error(err)
		// PASS
	}
}

// liveFeedNoticeParam 我听图标直播动态提醒参数
type liveFeedNoticeParam struct {
	UserID    int64 `json:"user_id"`
	StartTime int64 `json:"start_time"` // 时间戳，单位：秒
}

// liveFeedNoticeResp 我听图标直播动态提醒接口响应
type liveFeedNoticeResp struct {
	Room *followRoomFeed `json:"room,omitempty"`
}

// followRoomFeed 直播动态结构体
type followRoomFeed struct {
	RoomID         int64  `json:"room_id"`
	CreatorID      int64  `json:"creator_id"`
	CreatorIconURL string `json:"creator_iconurl"`

	point int64
}

// newLiveFeedNoticeParam 我听图标直播动态提醒参数构建
func newLiveFeedNoticeParam(c *handler.Context) (*liveFeedNoticeParam, error) {
	p := new(liveFeedNoticeParam)
	err := c.Bind(p)
	if err != nil || p.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return p, nil
}

// ActionLiveFeedNotice 获取我听图标直播动态提醒
/**
 * @api {post} /rpc/user/live/feed-notice 获取我听图标直播动态提醒
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} [start_time=0] 起始时间戳（获取这个时间之后开播的主播，单位：秒）
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} 获取成功:
 *     {
 *       "code": 0,
 *       "info": {
 *         "room": {
 *           "room_id": 753779961, // 房间号 ID
 *           "creator_id": 19820640, // 主播 ID
 *           "creator_iconurl": "http://static.maoercdn.com/avatars/202208/11/c48004c80847bf88442eee1440872793134627.jpg" // 主播头像
 *         }
 *       }
 *     }
 */
func ActionLiveFeedNotice(c *handler.Context) (handler.ActionResponse, error) {
	p, err := newLiveFeedNoticeParam(c)
	if err != nil {
		return nil, err
	}
	resp, err := p.getNotice()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// getNotice 获取动态提醒
func (p liveFeedNoticeParam) getNotice() (*liveFeedNoticeResp, error) {
	// 获取粉丝勋章等级 ≥ 5 级的主播
	medals, err := livemedal.FindUserMedalsLevelGte(p.UserID, 5)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(medals) == 0 {
		return &liveFeedNoticeResp{}, nil
	}
	// 获取热度限制直播间
	hotSuppressionRoomIDs, err := usersrank.ListHotSuppressionRoomIDs()
	if err != nil {
		logger.Errorf("获取超管后台屏蔽小时榜的直播间错误: %v", err)
		// PASS
	}
	// 获取粉丝勋章等级 ≥ 5 级且已关注的开播主播
	medalRoomIDs := make([]int64, 0, len(medals))
	medalMap := make(map[int64]*livemedal.Simple, len(medals))
	for _, m := range medals {
		// 过滤热度限制直播间
		if slices.Contains(hotSuppressionRoomIDs, m.RoomID) {
			continue
		}
		medalRoomIDs = append(medalRoomIDs, m.RoomID)
		medalMap[m.RoomID] = m
	}
	if len(medalRoomIDs) == 0 {
		return &liveFeedNoticeResp{}, nil
	}
	filter := bson.M{
		"room_id":          bson.M{"$in": medalRoomIDs},
		"status.open":      room.StatusOpenTrue,
		"status.open_time": bson.M{"$gt": util.TimeToUnixMilli(time.Unix(p.StartTime, 0))},
	}
	opt := &room.FindOptions{FindCreator: true, FindFans: true, ListenerID: p.UserID}
	openLiveRooms, err := room.ListSimples(filter, options.Find().SetSort(room.SortByOpenScore), opt)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(openLiveRooms) == 0 {
		return &liveFeedNoticeResp{}, nil
	}
	followRoomFeeds := make([]*followRoomFeed, 0, len(openLiveRooms))
	for _, r := range openLiveRooms {
		if !r.Statistics.Attention {
			continue
		}
		if m, ok := medalMap[r.RoomID]; ok {
			followRoomFeeds = append(followRoomFeeds, &followRoomFeed{
				RoomID:         r.RoomID,
				CreatorID:      r.CreatorID,
				CreatorIconURL: r.CreatorIconURL,
				point:          m.Point,
			})
		}
	}
	if len(followRoomFeeds) == 0 {
		return &liveFeedNoticeResp{}, nil
	}
	// 排序优先级：亲密度 > 热度 > 开播时间
	sort.SliceStable(followRoomFeeds, func(i, j int) bool {
		return followRoomFeeds[i].point > followRoomFeeds[j].point
	})
	return &liveFeedNoticeResp{
		Room: followRoomFeeds[0],
	}, nil
}
