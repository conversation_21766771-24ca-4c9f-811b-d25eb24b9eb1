package webrpc

import (
	"errors"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

var imRedis *redis.Client

func TestMain(m *testing.M) {
	initTest()
	m.Run()
}

func initTest() {
	config.InitTest()
	handler.SetMode(handler.TestMode)

	var err error
	redisConf := serviceredis.Config{
		Addr: "redis.srv.maoer.co:6379",
		DB:   106,
	}
	imRedis, err = serviceredis.NewRedisClient(&redisConf)
	if err != nil {
		logger.Fatal(err)
	}
	service.InitTest()
	service.SetDBUseSQLite()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	h := Handler()
	assert.Equal("rpc", h.Name)
	require.Len(h.Middlewares, 1)
	require.Len(h.SubHandlers, 3)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	assert.Equal("cron", h.SubHandlers[0].Name)
	assert.Equal("activity", h.SubHandlers[1].Name)
	assert.Equal("cron", h.SubHandlers[1].SubHandlers[0].Name)
	kc.Check(h,
		"live/set", "live/del", "live/logpunish", "live/reissuegift",
		"live/sync-score", "live/hourrank/random", "live/recommend-info", "live/check-pm", "live/extra-banners",
		"chatroom/close",
		"chatroom/image/set", "chatroom/review/set", "chatroom/review/get", "chatroom/vip/num",
		"rank/revenue", "rank/notifyhourrank", "util/giftnotify",
		"util/set-wrong-bank-info-userids",
		"recommended/background",
		"guild/creator-terminate-forcely",
		"guild/fix-hot-recommend-vancancy",
		"guild/get-contract",
		"guild/terminate/exec-silent",
		"widget/refresh",
		"activity/databus/senddelay",
		"room/userinfo", "room/users",
		"room/info", "room/open", "room/taginfo",
		"preview/get",
		"luckybag/get-drama-luckybags",
		"luckybag/drama-list",
	)
	kc.Check(h.SubHandlers[0],
		"noble/recommend/notify", "noble/expire", "noble/trial-expire",
		"live/recommendation", "live/vitality/renewal",
		"rank/user-hourly", "rank/hourly/notify", "rank/hourly/lasthourtop3/notify", "rank/monthly/notify", "rank/nova",
		"rank/archive/month", "rank/archive/hour", "rank/archive/nova",
		"user/superfanrank", "user/birthdaypriv",
		"cleanup", "medal/cleanup",
		"guild/cronjob",
		"superfan/expire-notice",
		"heat/superfans", "hotscore/goods",
		"recommend/schedule/record",
		"giftwall/period/extend",
		"sticker/superfans",
		"luckybag/draw",
	)
	kc.Check(h.SubHandlers[1], "userapply", "prepare", "reward")
	kc.Check(h.SubHandlers[1].SubHandlers[0], "rank/increase", "reward",
		"wish/draw",
	)
	assert.Equal("user", h.SubHandlers[2].Name)
	kc.Check(h.SubHandlers[2], "titles", "checkattention",
		"changeattention", "info", "block/add", "block/remove", "notify", "level", "level-up", "viewlog/delete",
		"backpack/add", "gift/custom/add",
		"appearance/add", "appearance/syncnoble",
		"live/feed", "live/feed-notice", "live/rank", "live/get-spend", "noble/sync", "birthday/priv",
	)
}

// receiveMessage 收取广播消息
func receiveMessage(ch <-chan *redis.Message, f func(*redis.Message) (bool, error)) error {
	timer := time.NewTimer(2 * time.Second)
	for i := 0; i < 5; i++ {
		select {
		case <-timer.C:
			return errors.New("timeout")
		case m, ok := <-ch:
			if !ok {
				return errors.New("ch closed")
			}
			logger.Debugf("receive: %s", m.Payload)
			ok, err := f(m)
			if err != nil {
				return err
			}
			if ok {
				return nil
			}
		}
	}
	return errors.New("receive message failed")
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	h := HandlerV2()
	assert.Equal("rpc", h.Name)
	require.Len(h.Middlewares, 1)

	t.Run("sub live handler v2", func(t *testing.T) {
		h := liveHandlerV2()
		assert.Equal("live-service", h.Name)
		kc := tutil.NewKeyChecker(t, tutil.Actions)
		kc.Check(h.Actions, "daily/task", "recommended/homepage")
	})
}

func TestChatroomHandleV2(t *testing.T) {
	assert := assert.New(t)

	h := chatroomHandlerV2()
	assert.Equal("chatroom", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "open/home-feed", "message/cross-send")
}

func TestCronHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := cronHandlerV2()
	assert.Equal("cron", h.Name)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.Actions, "user/backpack/expire-notify",
		"gashapon/rank/weekly/reward", "user/black-card/expire",
	)
}
