package webrpc

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionSetWrongBankInfoUserIDs(t *testing.T) {
	require := require.New(t)

	// 参数错误
	ctx := handler.NewRPCTestContext("/rpc/util/set-wrong-bank-info-userids", map[string]interface{}{
		"type": -20,
		"user_ids": []int64{
			333, 444, 555,
		},
	})
	_, err := ActionSetWrongBankInfoUserIDs(ctx)
	require.Equal(actionerrors.ErrParams, err)

	ctx = handler.NewRPCTestContext("/rpc/util/set-wrong-bank-info-userids", map[string]interface{}{
		"type": 30,
		"user_ids": []int64{
			333, 444, 555,
		},
	})
	_, err = ActionSetWrongBankInfoUserIDs(ctx)
	require.Equal(actionerrors.ErrParams, err)

	ctx = handler.NewRPCTestContext("/rpc/util/set-wrong-bank-info-userids", map[string]interface{}{
		"type":     operateWrongBankInfoTypeAdd,
		"user_ids": []int64{},
	})
	_, err = ActionSetWrongBankInfoUserIDs(ctx)
	require.Equal(actionerrors.ErrParams, err)

	// 正常情况
	require.NoError(service.Redis.Del(keys.KeyUserIDsWrongBankInfo0.Format()).Err())

	// 添加
	ctx = handler.NewRPCTestContext("/rpc/util/set-wrong-bank-info-userids", map[string]interface{}{
		"type": operateWrongBankInfoTypeAdd,
		"user_ids": []int64{
			333, 444, 555,
		},
	})
	resp, err := ActionSetWrongBankInfoUserIDs(ctx)
	require.NoError(err)
	require.Equal(int64(3), resp)
	count, err := service.Redis.SCard(keys.KeyUserIDsWrongBankInfo0.Format()).Result()
	require.NoError(err)
	require.Equal(int64(3), count)

	// 删除
	ctx = handler.NewRPCTestContext("/rpc/util/set-wrong-bank-info-userids", map[string]interface{}{
		"type": operateWrongBankInfoTypeDel,
		"user_ids": []int64{
			333, 444,
		},
	})
	resp, err = ActionSetWrongBankInfoUserIDs(ctx)
	require.NoError(err)
	require.Equal(int64(2), resp)
	count, err = service.Redis.SCard(keys.KeyUserIDsWrongBankInfo0.Format()).Result()
	require.NoError(err)
	require.Equal(int64(1), count)

	// 清空
	memberCount, err := service.Redis.SCard(keys.KeyUserIDsWrongBankInfo0.Format()).Result()
	require.NoError(err)
	ctx = handler.NewRPCTestContext("/rpc/util/set-wrong-bank-info-userids", map[string]interface{}{
		"type": operateWrongBankInfoTypeClear,
	})
	resp, err = ActionSetWrongBankInfoUserIDs(ctx)
	require.NoError(err)
	require.Equal(memberCount, resp)
	count, err = service.Redis.Exists(keys.KeyUserIDsWrongBankInfo0.Format()).Result()
	require.NoError(err)
	require.Zero(count)
}
