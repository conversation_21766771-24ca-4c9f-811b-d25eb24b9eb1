package webrpc

import (
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type giftNotifyParam struct {
	GiftID     int64 `json:"gift_id" binding:"gt=0"`
	Num        int   `json:"num" binding:"gt=0"`
	FromUserID int64 `json:"from_user_id" binding:"gt=0"`
	RoomID     int64 `json:"room_id" binding:"gt=0"`

	u *liveuser.Simple
	g *gift.Gift
	r *room.Room
}

// ActionGiftNotify 补发礼物飘屏
/* 不放出
 * @api {post} /rpc/util/giftnotify 补发礼物飘屏
 * @apiDescription 补发礼物飘屏，其他直播间用户点击进入有特效
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} num 礼物数量
 * @apiParam {Number} from_user_id 送礼人 ID
 * @apiParam {Number} room_id 直播间 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 全局消息
 *     {
 *       "type": "notify",
 *       "notify_type": "gift",
 *       "event": "send",
 *       "room_id": 65261414,
 *       "user": {
 *         "user_id": 3456961,
 *         "username": "十二半夏",
 *         "iconurl": "https://static-test.missevan.com/avatars/202006/12/65833548b5dc81baef72ab4b50fa1a2f120636.jpg",
 *         "titles": [{
 *           "type": "level",
 *           "level": 61
 *         }, {
 *           "type": "medal",
 *           "name": "蜂王浆",
 *           "level": 6
 *         }, {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }]
 *       },
 *       "gift": {
 *         "gift_id": 102,
 *         "name": "玫瑰",
 *         "icon_url": "https://static-test.missevan.com/gifts/icons/102.png",
 *         "price": 10,
 *         "num": 3000,
 *         "effect_url": "https://static-test.missevan.com/gifts/comboeffects/102.svga"
 *       },
 *       "message": "<b>十二半夏</b> 给 <b>一点半夏</b> 送出 <b>3000 个玫瑰</b>，快来围观吧~"
 *     }
 */
func ActionGiftNotify(c *handler.Context) (handler.ActionResponse, error) {
	var param giftNotifyParam
	if err := param.load(c); err != nil {
		return nil, err
	}
	if err := param.sendNotify(); err != nil {
		return nil, err
	}
	return "success", nil
}

func (p *giftNotifyParam) load(c *handler.Context) error {
	err := c.BindJSON(p)
	if err != nil {
		return actionerrors.ErrParams
	}

	p.u, err = liveuser.FindOneSimple(bson.M{"user_id": p.FromUserID}, &liveuser.FindOptions{FindTitles: true, RoomID: p.RoomID})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.u == nil {
		return actionerrors.ErrCannotFindUser
	}
	p.g, err = gift.FindShowingGiftByGiftID(p.GiftID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.g == nil {
		return actionerrors.ErrNotFound("无法找到指定礼物")
	}
	p.r, err = room.Find(p.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if p.r == nil {
		return actionerrors.ErrNotFound("无法找到指定房间")
	}
	return nil
}

func (p *giftNotifyParam) sendNotify() (err error) {
	// TODO: 使用 models 的参数
	message := "<b>${username}</b> 给 <b>${creator_username}</b> 送出 <b>${gift_num} 个${gift_name}</b>，快来围观吧~"
	if p.g.NotifyMessage != "" {
		message = p.g.NotifyMessage
	}
	msgParam := map[string]string{
		"username":         p.u.Username,
		"creator_username": p.r.CreatorUsername,
		"gift_num":         strconv.Itoa(p.Num),
		"gift_name":        p.g.Name,
	}
	gInfo := gift.NewNotifyGift(p.g, p.Num)
	if gInfo.EffectURL == "" {
		gInfo.EffectURL = p.g.ComboEffect
	}
	payloadPre := gift.NotifyPayload{
		Type:       liveim.TypeNotify,
		NotifyType: liveim.TypeGift,
		Event:      liveim.EventSend,
		RoomID:     p.RoomID,
		User:       p.u,
		Gift:       gInfo,
		Message:    goutil.FormatMessage(message, msgParam),
	}
	if p.g.NotifyBubbleID != 0 {
		payloadPre.NotifyBubble, err = bubble.FindSimple(p.g.NotifyBubbleID)
		if err != nil {
			logger.Info(err)
			// PASS
		}
	}
	err = userapi.BroadcastAll(payloadPre)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 记录全局消息
	h := notifymessages.NewGiftNotify(payloadPre.User.UserID(), payloadPre.RoomID, payloadPre.Message, nil)
	err = notifymessages.Insert(h)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return
}
