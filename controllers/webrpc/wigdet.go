package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionWidgetRefresh 指定直播间刷新小窗
/**
 * @api {post} /rpc/widget/refresh 指定直播间刷新小窗
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number} event_id 活动 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *   {
 *     "type": "event",
 *     "event": "widget",
 *     "room_id": 65261414, // 0 更新所有房间小窗
 *     "event_id": 223,
 *     "widget": {}
 *   }
 *
 */
func ActionWidgetRefresh(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID  int64 `json:"room_id"`
		EventID int64 `json:"event_id"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.RoomID <= 0 || param.EventID <= 0 {
		return nil, actionerrors.ErrParams
	}

	resp, err := rank.Widget(c, param.EventID, param.RoomID)
	if err != nil {
		return nil, err
	}

	// TODO: 支持更新所有房间小窗
	err = userapi.Broadcast(param.RoomID, map[string]interface{}{
		"type":     "event",
		"event":    "widget",
		"room_id":  param.RoomID,
		"event_id": param.EventID,
		"widget":   resp,
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return "success", nil
}
