package webrpc

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// ActionRoomInfo 获取直播间信息
/**
 * @api {post} /rpc/room/info 获取直播间信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {Number} [user_id] 用户 ID
 * @apiParam {number=0,1} [ignore_ban=0] 是否忽略封禁, 0: 不忽略, 1: 忽略，仍获取直播间信息
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "room": {
 *         "room_id": 112422405,
 *         "catalog_id": 106,
 *         "name": "直播间",
 *         "announcement": "testtest",
 *         "guild_id": 123,
 *         "creator_id": 12345,
 *         "creator_username": "1234",
 *         "creator_iconurl": "https://static.maoercdn.com/profile/01.png", // 主播头像
 *         "cover_url": "http://static.example.com/cover.png",
 *         "statistics": {
 *           "accumulation": 123
 *         },
 *         "status": {
 *           "open": 1,
 *           "open_question_count": 3,
 *           "open_revenue": 3735,
 *           "open_time": 1568274571359,
 *           "close_time": null,
 *           "channel": {
 *             "type": "open",
 *             "time": 1568027063199
 *           },
 *           "broadcasting": false
 *         },
 *         "limit": {
 *           "type": 2,
 *           "allowed_user_ids": [12]
 *         },
 *         "config": {
 *           "disable_join_queue": false
 *         }
 *       }
 *     }
 *   }
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该聊天室
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (403) {Number} code 500030013
 * @apiError (403) {String} info 直播间被封禁
 *
 * @apiError (403) {Number} code 500020023
 * @apiError (403) {String} info 当前用户被封禁
 *
 */
func ActionRoomInfo(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID    int64 `json:"room_id"`
		UserID    int64 `json:"user_id"`
		IgnoreBan int   `json:"ignore_ban"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.RoomID <= 0 || param.UserID < 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.FindOne(bson.M{"room_id": param.RoomID}, &room.FindOptions{FindCreator: true})
	if err != nil {
		return nil, err
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	resp := &liverpc.RoomInfoResp{
		Room: &liverpc.RoomSimple{
			RoomID:            r.RoomID,
			CatalogID:         r.CatalogID,
			Name:              r.Name,
			Announcement:      r.Announcement,
			GuildID:           r.GuildID,
			CreatorID:         r.CreatorID,
			CreatorUsername:   r.CreatorUsername,
			CreatorIconURL:    r.CreatorIconURL,
			Statistics:        r.Statistics,
			Status:            r.Status,
			CoverURL:          r.CoverURL,
			ActivityCatalogID: r.ActivityCatalogID,
			Limit:             r.Limit,
			Config:            r.Config,
		},
	}
	if param.IgnoreBan != 0 {
		// 忽略直播间或用户封禁，直接返回直播间信息
		return resp, nil
	}
	// 直播间被封禁
	ban, err := livemeta.FindBanned(r.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if ban != nil {
		return nil, actionerrors.ErrBannedRoom
	}

	if param.UserID == 0 || param.UserID == r.CreatorID {
		// 游客或主播直接返回直播间信息
		return resp, nil
	}
	// 主站后台黑名单
	exists, err := blocklist.Exists(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return nil, actionerrors.ErrBlockUser
	}
	// 管理后台封禁
	banned, err := userstatus.IsBanned(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if banned {
		return nil, actionerrors.ErrBlockUser
	}
	// 主播是否拉黑此用户
	blocked, err := blocklist.IsBlocked(r.CreatorID, param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if blocked {
		return nil, actionerrors.ErrBlockUser
	}

	return resp, nil
}

type roomOpenParam struct {
	RoomID    int64  `json:"room_id"`
	OpenLogID string `json:"open_log_id"`

	r *room.Room
	c *handler.Context
}

// ActionRoomOpen 直播间开播
/**
 * @api {post} /rpc/room/open 直播间开播
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} [open_log_id] 房间开播日志 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "success"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 加入听剧标签主播消息
 *   {
 *     "type": "pia",
 *     "event": "start",
 *     "room_id": 10659544,
 *     "by": "admin"
 *   }
 *
 * @apiError (404) {Number} code 500030004
 * @apiError (404) {String} info 无法找到该聊天室
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 */
func ActionRoomOpen(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newRoomOpenParam(c)
	if err != nil {
		return nil, err
	}
	param.checkPiaBroadcast()
	param.sendRoomOnOpenEvent()
	return handler.M{"msg": "success"}, nil
}

func newRoomOpenParam(c *handler.Context) (*roomOpenParam, error) {
	var param roomOpenParam
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	param.r, err = room.FindOne(bson.M{"room_id": param.RoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, err
	}
	if param.r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	param.c = c
	return &param, nil
}

func (param *roomOpenParam) checkPiaBroadcast() {
	if !param.r.ContainsTag(tag.TagListenDrama) {
		return
	}

	p, err := params.FindPia()
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if !p.IsShow() {
		return
	}

	// 开播时给主播发送 pia 戏开始的消息（前端需要展示弹窗）
	err = userapi.BroadcastUser(param.r.RoomID, param.r.CreatorID,
		tag.NewPiaPayload(true, param.r.RoomID, true)) // 发送被管理员添加标签的消息
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *roomOpenParam) sendRoomOnOpenEvent() {
	if param.OpenLogID == "" {
		return
	}
	err := userapi.SendRoomOnOpenEvent(param.c.UserContext(), param.RoomID, param.OpenLogID)
	if err != nil {
		logger.WithField("room_id", param.RoomID).Error(err)
		// PASS
	}
}
