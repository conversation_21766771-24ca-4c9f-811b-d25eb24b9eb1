package webrpc

import (
	"fmt"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

type guildCreatorTerminateForcelyParam struct {
	ApplymentID int64 `json:"applyment_id"`
}

// ActionGuildCreatorTerminateForcely 主播强制解约支付违约金的回调
/**
 * @api {post} /rpc/guild/creator-terminate-forcely 主播强制解约支付违约金的回调
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} applyment_id 强制解约申请 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": true
 *     }
 */
func ActionGuildCreatorTerminateForcely(ctx *handler.Context) (handler.ActionResponse, error) {
	var param guildCreatorTerminateForcelyParam
	if err := ctx.BindJSON(&param); err != nil || param.ApplymentID <= 0 {
		return nil, actionerrors.ErrParams
	}

	applyment, err := contractapplyment.FindByID(param.ApplymentID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if applyment == nil {
		return nil, actionerrors.ErrApplymentNotExist
	}
	if applyment.Type != contractapplyment.TypeLiveTerminateForcely {
		return nil, actionerrors.ErrApplymentType
	}
	// 不能使用 applyment.IsPending()，避免判断 applyment.ExpireTime（强制解约申请过期时间为立即失效）
	if applyment.Status != contractapplyment.StatusPending {
		return nil, actionerrors.ErrApplymentFreezed
	}

	var guildOwner int64
	err = servicedb.Tx(applyment.DB(), func(tx *gorm.DB) error {
		nowStamp := util.TimeNow().Unix()
		db := tx.Table(applyment.TableName()).Where("id = ? AND status = ?", applyment.ID, contractapplyment.StatusPending).Update(map[string]interface{}{
			"status":               contractapplyment.StatusAgreed,
			"contract_expire_time": nowStamp,
			"process_time":         nowStamp,
			"modified_time":        nowStamp,
		})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return actionerrors.ErrApplymentNotExist
		}

		var contract livecontract.LiveContract
		err = tx.Table(livecontract.TableName()).Where("live_id = ? AND guild_id = ? AND status = ?",
			applyment.LiveID, applyment.GuildID, livecontract.StatusContracting).Scan(&contract).Error
		if err != nil {
			if gorm.IsRecordNotFoundError(err) {
				return actionerrors.ErrContractNotExist
			}
			return err
		}
		guildOwner = contract.GuildOwner

		db = tx.Table(contract.TableName()).Where("id = ? AND status = ?", contract.ID, livecontract.StatusContracting).Update(map[string]interface{}{
			"status":        livecontract.StatusFinished,
			"contract_end":  nowStamp,
			"modified_time": nowStamp,
		})
		if err := db.Error; err != nil {
			return err
		}
		if db.RowsAffected == 0 {
			return actionerrors.ErrContractNotExist
		}
		err := guild.IncreaseLiveNum(applyment.GuildID, -1, tx)
		if err != nil {
			return err
		}

		// 未处理的降薪申请失效
		if err = contractapplyment.ApplicationEditRateInvalid(contract.GuildID, contract.LiveID, tx); err != nil {
			return err
		}

		// NOTICE: guild_agent_creator 与 tx 目前不在同一个 db 中，事务不完全可靠
		db = guildagent.AgentCreator{}.DB().
			Where("guild_id = ? AND creator_id = ? AND delete_time = 0", applyment.GuildID, applyment.LiveID).
			Update(map[string]interface{}{
				"delete_time":   nowStamp,
				"modified_time": nowStamp,
			})
		if err = db.Error; err != nil {
			return err
		}

		// 同步修改直播间公会信息
		err = room.UpdateGuildID(applyment.LiveID, 0)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	creator, err := mowangskuser.FindByUserID(applyment.LiveID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if creator == nil {
		logger.WithField("live_id", applyment.LiveID).Error("主播未找到")
		return nil, actionerrors.ErrCannotFindUser
	}

	err = messageassign.SystemMessageAssign(
		guildOwner,
		"主播已与您解约",
		fmt.Sprintf("很遗憾 _(:3 」∠)_ 主播 %s（MID：%d）于%s与您解约。", creator.Username, creator.ID, util.TimeNow().Format(" 2006 年 01 月 02 日")),
	)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	return true, nil
}
