package webrpc

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/databus"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionActivityDatabusSendDelay(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := "databus/delay/test"
	require.NoError(service.Redis.Del(keys.DelayDatabusKeyActivityRPC1.Format(key)).Err())
	service.DatabusPub.ClearDebugPubMsgs()
	defer service.DatabusPub.ClearDebugPubMsgs()

	body := handler.M{
		"key":        key,
		"message":    "test",
		"deliver_at": -1,
	}
	c := handler.NewRPCTestContext("/rpc/activity/databus/senddelay", body)
	_, err := ActionActivityDatabusSendDelay(c)
	assert.EqualError(err, actionerrors.ErrParams.Message)

	body["deliver_at"] = 0
	c = handler.NewRPCTestContext("/rpc/activity/databus/senddelay", body)
	resp, err := ActionActivityDatabusSendDelay(c)
	require.NoError(err)
	assert.Equal("success", resp)

	body["deliver_at"] = util.TimeNow().Unix()
	c = handler.NewRPCTestContext("/rpc/activity/databus/senddelay", body)
	resp, err = ActionActivityDatabusSendDelay(c)
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestDelayActivityDatabusOperator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testKey := "testkey"
	testMessage := "11b9d5d9bc9b53298ce5a6a6"
	cancel := mrpc.SetMock(userapi.URLDatabusDelayMessage, func(input interface{}) (output interface{}, err error) {
		body, ok := input.(*userapi.ActivityDatabusMessage)
		require.True(ok)
		require.NotNil(body)
		assert.Equal(testKey, body.Key)
		assert.Equal(testMessage, body.Message)
		return "success", nil
	})
	defer cancel()

	message := userapi.ActivityDatabusMessage{
		Key:     testKey,
		Message: testMessage,
	}
	assert.NotPanics(func() {
		DelayActivityDatabusOperator()(&databus.Message{
			Key:   keys.DelayDatabusKeyActivityRPC1[1],
			Value: json.RawMessage(tutil.SprintJSON(message)),
		})
	})
}
