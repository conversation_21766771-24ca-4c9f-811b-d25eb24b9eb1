package user

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionNobleSync(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/rpc/user/noble/sync", handler.M{
		"is_new":      true,
		"user_id":     999,
		"noble_level": 3,
		"price":       100,
		"expire_time": goutil.TimeNow().Add(24 * time.Hour).Unix(),
	})
	resp, err := ActionNobleSync(c)
	require.NoError(err)
	info, ok := resp.(handler.M)
	require.True(ok)
	assert.Equal("success", info["msg"])
}

func TestNewNobleSyncParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/rpc/user/noble/sync", nil)
	_, err := newNobleSyncParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewRPCTestContext("/rpc/user/noble/sync", handler.M{
		"is_new":      true,
		"user_id":     999,
		"noble_level": 3,
		"price":       100,
		"expire_time": goutil.TimeNow().Add(24 * time.Hour).Unix(),
	})
	param, err := newNobleSyncParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.True(param.IsNew)
}

func TestNobleSyncParam_syncAppearance(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(999)
		level      = 1
		now        = goutil.TimeNow()
		expTime    = now.Add(2 * time.Minute).Unix()
	)
	a := &userappearance.UserAppearance{
		UserID:       testUserID,
		AppearanceID: appearance.VipAppearanceID(vip.TypeLiveNoble, level, appearance.TypeAvatarFrame),
		Name:         "测试同步外观数据",
		Type:         appearance.TypeAvatarFrame,
		Image:        "oss://testdata/test.webp",
		From:         appearance.FromNoble,
		Status:       userappearance.StatusWorn,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	a2 := &userappearance.UserAppearance{
		UserID:       testUserID,
		AppearanceID: appearance.VipAppearanceID(vip.TypeLiveNoble, level, appearance.TypeCardFrame),
		Name:         "测试同步外观数据",
		Type:         appearance.TypeCardFrame,
		Image:        "oss://testdata/test.webp",
		From:         appearance.FromNoble,
		Status:       userappearance.StatusWorn,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userappearance.Collection().DeleteMany(ctx, bson.M{
		"user_id": bson.M{"$in": bson.A{testUserID, testUserID + 1, testUserID + 2}},
	})
	require.NoError(err)
	_, err = userappearance.Collection().InsertMany(ctx, []interface{}{a, a2})
	require.NoError(err)

	param := &nobleSyncParam{
		UserID:     testUserID,
		ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix(),
		NobleLevel: 3,
		IsNew:      true,
	}
	err = param.syncAppearance()
	require.NoError(err)
	var aItem userappearance.UserAppearance
	err = userappearance.Collection().FindOne(ctx, bson.M{
		"user_id": param.UserID,
		"from":    appearance.FromNoble,
		"type":    appearance.TypeAvatarFrame,
	}).Decode(&aItem)
	require.NoError(err)
	assert.Equal(appearance.FromNoble, aItem.From)
	assert.Equal(appearance.VipAppearanceID(vip.TypeLiveNoble, param.NobleLevel, appearance.TypeAvatarFrame), aItem.AppearanceID)
	assert.GreaterOrEqual(aItem.StartTime, a.StartTime)
	assert.GreaterOrEqual(*aItem.ExpireTime, *a.ExpireTime)
	err = userappearance.Collection().FindOne(ctx, bson.M{
		"user_id": param.UserID,
		"from":    appearance.FromNoble,
		"type":    appearance.TypeCardFrame,
	}).Decode(&aItem)
	require.NoError(err)
	assert.Equal(appearance.FromNoble, aItem.From)
	assert.Equal(appearance.VipAppearanceID(vip.TypeLiveNoble, param.NobleLevel, appearance.TypeCardFrame), aItem.AppearanceID)
	assert.GreaterOrEqual(aItem.StartTime, a2.StartTime)
	assert.GreaterOrEqual(*aItem.ExpireTime, *a2.ExpireTime)

	// 测试残缺数据的新用户
	_, err = userappearance.Collection().DeleteOne(ctx, bson.M{
		"user_id":       param.UserID,
		"from":          appearance.FromNoble,
		"type":          appearance.TypeCardFrame,
		"appearance_id": aItem.AppearanceID,
	})
	require.NoError(err)
	err = param.syncAppearance()
	require.NoError(err)
	err = userappearance.Collection().FindOne(ctx, bson.M{
		"user_id": param.UserID,
		"from":    appearance.FromNoble,
		"type":    appearance.TypeCardFrame,
	}).Decode(&aItem)
	require.NoError(err)
	assert.Equal(appearance.FromNoble, aItem.From)
	assert.Equal(appearance.VipAppearanceID(vip.TypeLiveNoble, param.NobleLevel, appearance.TypeCardFrame), aItem.AppearanceID)
	assert.GreaterOrEqual(aItem.StartTime, a2.StartTime)
	assert.GreaterOrEqual(*aItem.ExpireTime, *a2.ExpireTime)

	// 测试全新用户
	testUserID1 := testUserID + 1
	param = &nobleSyncParam{
		IsNew:      true,
		UserID:     testUserID1,
		ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix(),
		NobleLevel: 3,
	}
	err = param.syncAppearance()
	require.NoError(err)
	types := appearance.NobleAppearanceTypes(param.NobleLevel)
	appearanceIDs := make([]int64, 0, len(types))
	for _, appearanceType := range types {
		appearanceIDs = append(appearanceIDs, appearance.VipAppearanceID(vip.TypeLiveNoble, param.NobleLevel, appearanceType))
	}
	nobleAppearnces, err := appearance.Find(bson.M{"id": bson.M{"$in": appearanceIDs}}, nil)
	require.NoError(err)
	userAppearances, err := userappearance.Find(bson.M{
		"user_id": param.UserID,
		"from":    appearance.FromNoble,
	}, nil)
	require.NoError(err)
	require.NotEmpty(userAppearances)
	assert.Len(userAppearances, len(nobleAppearnces))
	mAppearance := goutil.ToMap(nobleAppearnces, "ID").(map[int64]*appearance.Appearance)
	mUserAppearance := goutil.ToMap(userAppearances, "AppearanceID").(map[int64]*userappearance.UserAppearance)
	for i := range appearanceIDs {
		a, ok := mAppearance[appearanceIDs[i]]
		ua, uaOk := mUserAppearance[appearanceIDs[i]]
		if ok && uaOk {
			assert.Equal(a.ID, ua.AppearanceID)
			assert.Equal(a.Type, ua.Type)
			assert.Equal(appearance.FromNoble, ua.From)
			assert.Equal(userappearance.StatusWorn, ua.Status)
			assert.Equal(userappearance.OwnStatusUsed, ua.OwnStatus)
			assert.GreaterOrEqual(*ua.ExpireTime, param.ExpireTime)
		}
	}

	// 测试残缺数据的老贵族
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{
		"user_id": testUserID,
		"from":    appearance.FromNoble,
		"type":    appearance.TypeCardFrame,
	})
	require.NoError(err)

	param = &nobleSyncParam{
		IsNew:      false,
		UserID:     testUserID,
		ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix(),
		NobleLevel: 3,
	}
	err = param.syncAppearance()
	require.NoError(err)

	userAppearances, err = userappearance.Find(bson.M{
		"user_id": param.UserID,
		"from":    appearance.FromNoble,
	}, nil)
	require.NoError(err)
	require.NotEmpty(userAppearances)
	assert.Len(userAppearances, len(nobleAppearnces)-1)

	mUserAppearance = goutil.ToMap(userAppearances, "AppearanceID").(map[int64]*userappearance.UserAppearance)
	for i := range appearanceIDs {
		a, ok := mAppearance[appearanceIDs[i]]
		ua, uaOk := mUserAppearance[appearanceIDs[i]]
		if ok && uaOk {
			assert.Equal(a.ID, ua.AppearanceID)
			assert.Equal(a.Type, ua.Type)
			assert.Equal(appearance.FromNoble, ua.From)
			assert.Equal(userappearance.StatusWorn, ua.Status)
			assert.Equal(userappearance.OwnStatusUsed, ua.OwnStatus)
			assert.GreaterOrEqual(*ua.ExpireTime, param.ExpireTime)
		}
	}

	highnessAppearance := &userappearance.UserAppearance{
		UserID:       testUserID + 2,
		AppearanceID: appearance.VipAppearanceID(vip.TypeLiveHighness, 1, appearance.TypeAvatarFrame),
		Name:         "测试上神头像框数据",
		Type:         appearance.TypeAvatarFrame,
		Image:        "oss://testdata/test.webp",
		From:         appearance.FromHighness,
		Status:       userappearance.StatusWorn,
		StartTime:    now.Unix(),
		ExpireTime:   &expTime,
	}
	_, err = userappearance.Collection().InsertOne(ctx, highnessAppearance)
	require.NoError(err)
	// 测试佩戴其他外观
	param = &nobleSyncParam{
		IsNew:      false,
		UserID:     highnessAppearance.UserID,
		ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix(),
		NobleLevel: 3,
	}
	err = param.syncAppearance()
	require.NoError(err)
	userAppearance, err := userappearance.FindOne(bson.M{
		"user_id":       highnessAppearance.UserID,
		"appearance_id": highnessAppearance.AppearanceID,
	}, nil)
	require.NoError(err)
	require.NotEmpty(userAppearance)
	assert.Equal(userappearance.StatusWorn, userAppearance.Status)
}

func TestNobleSyncParam_syncHorn(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(1234567890)
		now        = goutil.TimeNow()
	)

	p := params.Bubble{
		Key: params.KeyBubble,
		CustomHornBubble: &params.HornBubble{
			StartTime: now.Unix(),
			EndTime:   now.AddDate(0, 1, 0).Unix(),
			NobleBubbles: []params.NobleBubble{
				{
					Type:     vip.TypeLiveNoble,
					Level:    vip.NobleLevel4,
					BubbleID: 87,
				},
			},
		},
	}
	defaultBubbleJSON, err := json.Marshal(p)
	require.NoError(err)
	err = service.LRURedis.Set(keys.KeyParams1.Format(p.Key), defaultBubbleJSON, time.Minute).Err()
	require.NoError(err)
	err = service.LiveDB.Delete(&livecustom.LiveCustom{}, "element_id = ? AND custom_type = ?", testUserID, livecustom.TypeNobleHornBubble).Error
	require.NoError(err)

	param := &nobleSyncParam{
		IsNew:      false,
		UserID:     1234567890,
		ExpireTime: goutil.TimeNow().Add(24 * time.Hour).Unix(),
		NobleLevel: vip.NobleLevel4,
	}
	err = param.syncHorn()
	require.NoError(err)

	bubble, err := livecustom.FindUserCustomNobleHornBubble(param.UserID, now)
	require.NoError(err)
	require.NotNil(bubble)
	customHornBubbleJSON, err := json.Marshal(p.CustomHornBubble)
	require.NoError(err)
	assert.EqualValues(customHornBubbleJSON, bubble.More)
}

func TestNobleSyncParam_syncActivity(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var count int
	cleanup := mrpc.SetMock(userapi.URIRankRevenue, func(input any) (any, error) {
		count++
		req, ok := input.(*userapi.RankRevenueParams)
		require.True(ok)
		require.NotNil(req)
		assert.Equal(1, req.Noble.PreviousNobleLevel)
		assert.Equal(2, req.Noble.NobleLevel)
		return nil, nil
	})
	defer cleanup()

	param := &nobleSyncParam{
		NobleLevel:         2,
		PreviousNobleLevel: 1,

		c: handler.NewRPCTestContext("", nil),
	}
	param.syncActivity()
	assert.Equal(1, count)
}
