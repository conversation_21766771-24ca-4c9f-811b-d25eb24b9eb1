package user

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type nobleSyncParam struct {
	IsNew              bool  `json:"is_new"`
	RoomID             int64 `json:"room_id"`
	UserID             int64 `json:"user_id"`
	Price              int64 `json:"price"`
	PreviousNobleLevel int   `json:"previous_noble_level"`
	NobleLevel         int   `json:"noble_level"`
	ExpireTime         int64 `json:"expire_time"`

	c    *handler.Context
	room *room.Room
}

// ActionNobleSync 用户开通/续费贵族回调
/**
 * @api {post} /rpc/user/noble/sync 用户开通/续费贵族回调
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Boolean} is_new 是否为新开通贵族
 * @apiParam {Number} [room_id] 直播间 ID，若不在直播间内开通/续费则不传
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} price 贵族的价格，单位：钻
 * @apiParam {Number} [previous_noble_level] 用户之前的贵族等级，若之前没有贵族则不传
 * @apiParam {Number} noble_level 用户购买的贵族等级
 * @apiParam {Number} expire_time 用户的贵族过期时间，单位：秒
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "success"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 */
func ActionNobleSync(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newNobleSyncParam(c)
	if err != nil {
		return nil, err
	}
	err = param.syncAppearance()
	if err != nil {
		return nil, err
	}
	err = param.syncHorn()
	if err != nil {
		return nil, err
	}
	param.syncActivity()
	return handler.M{"msg": "success"}, nil
}

func newNobleSyncParam(c *handler.Context) (*nobleSyncParam, error) {
	var param *nobleSyncParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.NobleLevel <= 0 || param.ExpireTime <= 0 || param.Price <= 0 {
		return nil, actionerrors.ErrParams
	}
	if param.RoomID > 0 {
		param.room, err = room.Find(param.RoomID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if param.room == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
	}
	param.c = c
	return param, nil
}

func (param *nobleSyncParam) syncAppearance() error {
	// TODO: 使用用户佩戴外观缓存
	uaItems, err := userappearance.Find(bson.M{
		"user_id": param.UserID,
		"from": bson.M{
			"$in": appearance.VipFromList(),
		},
	}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	wornAppearancesByType, err := userappearance.FindUserAppearances(param.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	types := appearance.NobleAppearanceTypes(param.NobleLevel)
	appearanceIDs := make([]int64, 0, len(types))
	for _, appearanceType := range types {
		appearanceIDs = append(appearanceIDs, appearance.VipAppearanceID(vip.TypeLiveNoble, param.NobleLevel, appearanceType))
	}

	nobleAppearances, err := appearance.Find(bson.M{"id": bson.M{"$in": appearanceIDs}}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	now := goutil.TimeNow().Unix()
	updates := make([]mongo.WriteModel, 0)
	// 如果是新贵族用户
	// TODO: 开通的贵族等级比当前等级低的时候，不替换外观
	if param.IsNew {
		mAppearanceByID := goutil.ToMap(nobleAppearances, "ID").(map[int64]*appearance.Appearance)
		// 如果有残余数据，清空并新建
		if len(uaItems) > 0 {
			updates = append(updates, mongo.NewDeleteManyModel().SetFilter(bson.M{
				"user_id": param.UserID,
				"from": bson.M{
					"$in": appearance.VipFromList(),
				},
			}))
		}
		for i := range appearanceIDs {
			// 过滤贵族外观
			if a, ok := mAppearanceByID[appearanceIDs[i]]; ok {
				newAppearance := userappearance.NewUserAppearance(param.UserID, a)
				newAppearance.SetStatus(userappearance.StatusWorn, param.ExpireTime, 0)
				var wornAppearance *userappearance.UserAppearance
				if wornAppearances, exists := wornAppearancesByType[a.Type]; exists && len(wornAppearances) > 0 {
					wornAppearance = wornAppearances[0]
				}
				if wornAppearance != nil && !userappearance.ShouldAutoWear(wornAppearance, newAppearance) {
					continue
				}

				updates = append(
					updates,
					// 设定当前类型对应的其他佩戴中的外观为持有状态
					mongo.NewUpdateManyModel().SetFilter(bson.M{
						"user_id": param.UserID,
						"type":    a.Type,
						"status":  userappearance.StatusWorn,
					}).SetUpdate(bson.M{
						"$set": bson.M{
							"status":        userappearance.StatusOwned,
							"modified_time": now,
						},
					}),
					// 插入新的贵族外观
					mongo.NewInsertOneModel().SetDocument(newAppearance),
				)
			}
		}
	} else {
		// 如果是老贵族，则更新原有数据，由于用户可以不佩戴贵族外观，所以此处缺失数据不作处理
		mAppearance := goutil.ToMap(nobleAppearances, "Type").(map[int]*appearance.Appearance)
		for i := range uaItems {
			uaItem := uaItems[i]
			if a, ok := mAppearance[uaItem.Type]; ok {
				// 用户佩戴的外观不是普通贵族外观不做处理
				if uaItem.AppearanceID != a.ID {
					continue
				}
				item := userappearance.NewUserAppearance(param.UserID, a)
				item.SetStatus(userappearance.StatusWorn, param.ExpireTime, now)
				updates = append(updates, mongo.NewUpdateOneModel().
					SetFilter(bson.M{"_id": uaItem.OID}).
					SetUpdate(bson.M{
						"$set": item,
					}))
			}
		}
	}

	// 如果贵族没有佩戴外观并且续费了贵族，则不做更新
	if len(updates) != 0 {
		ctx, cancel := service.MongoDB.Context()
		defer cancel()
		_, err = userappearance.Collection().BulkWrite(ctx, updates)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	userappearance.ClearCache(param.UserID)
	return nil
}

func (param *nobleSyncParam) syncHorn() error {
	now := goutil.TimeNow()

	// 为用户添加喇叭定制气泡
	b, err := params.FindBubble()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 定制气泡生效中并且存在当前贵族等级的定制气泡，才给用户添加定制气泡
	if b.CustomHornBubble != nil && b.CustomHornBubble.IsActive(now) &&
		b.CustomHornBubble.UserVipNotifyBubbleID(vip.TypeLiveNoble, param.NobleLevel) != 0 {
		err = livecustom.AddUserCustomNobleHornBubble(param.UserID, b.CustomHornBubble, b.CustomHornBubble.StartTime, b.CustomHornBubble.EndTime)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return nil
}

func (param *nobleSyncParam) syncActivity() {
	var sync *rank.SyncCommonParam
	if param.room != nil {
		sync = rank.NewSyncParam(param.room.RoomID, param.UserID, param.room.CreatorID).
			SetGuildID(param.room.GuildID).
			SetActivityCatalogID(param.room.ActivityCatalogID)
	} else {
		sync = rank.NewSyncParam(0, param.UserID, 0)
	}
	sync.SetNoble(param.IsNew, vip.TypeLiveNoble, param.Price, param.NobleLevel, param.PreviousNobleLevel).
		SendLiveActivity(param.c.UserContext())
}
