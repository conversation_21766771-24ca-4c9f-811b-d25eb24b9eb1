package user

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	m.Run()
}

func TestLiveGetSpendTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(liveGetSpendParam{}, "user_ids")
	kc.Check(liveGetSpendResp{}, "data")
}

func TestNewLiveGetSpendParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/rpc/user/live/get-spend"
	var param liveGetSpendParam
	c := handler.NewRPCTestContext(api, param)
	_, err := newLiveGetSpendParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取接口参数
	param.UserIDs = []int64{1234}
	c = handler.NewRPCTestContext(api, param)
	res, err := newLiveGetSpendParam(c)
	require.NoError(err)
	require.NotNil(res)
	assert.Len(res.UserIDs, 1)
	assert.EqualValues(1234, res.UserIDs[0])
}

func TestActionLiveGetSpend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误
	api := "/rpc/user/live/get-spend"
	var param liveGetSpendParam
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionLiveGetSpend(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试用户没有直播消费
	param.UserIDs = []int64{2333}
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionLiveGetSpend(c)
	require.NoError(err)
	data, ok := res.(liveGetSpendResp)
	require.True(ok)
	assert.Len(data.Data, 1)
	assert.EqualValues(0, data.Data[2333])

	// 测试获取用户直播消费
	param.UserIDs = append(param.UserIDs, 12346, 12347)
	c = handler.NewRPCTestContext(api, param)
	res, err = ActionLiveGetSpend(c)
	require.NoError(err)
	data, ok = res.(liveGetSpendResp)
	require.True(ok)
	assert.Len(data.Data, 3)
	assert.EqualValues(0, data.Data[2333])
	assert.EqualValues(0.6, data.Data[12346])
	assert.EqualValues(2, data.Data[12347])
}
