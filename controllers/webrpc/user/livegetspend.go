package user

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

type liveGetSpendParam struct {
	UserIDs []int64 `json:"user_ids"` // 用户 ID 列表
}

type liveGetSpendResp struct {
	Data map[int64]util.Float2DP `json:"data"` // key 为用户 ID, value 为用户直播消费金额，单位：元
}

func newLiveGetSpendParam(c *handler.Context) (*liveGetSpendParam, error) {
	var param liveGetSpendParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if len(param.UserIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

// ActionLiveGetSpend 获取用户直播消费
/**
 * @api {post} /rpc/user/live/get-spend 获取用户直播消费
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} user_ids 用户 ID 列表
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": { // key 为用户 ID, value 为用户直播消费金额，单位：元
 *         "233": 0,
 *         "1234": 233.20
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 */
func ActionLiveGetSpend(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newLiveGetSpendParam(c)
	if err != nil {
		return nil, err
	}

	usersLiveSpend, err := transactionlog.GetUsersLiveSpend(param.UserIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var resp liveGetSpendResp
	resp.Data = make(map[int64]util.Float2DP, len(param.UserIDs))
	usersLiveSpendMap := util.ToMap(usersLiveSpend, "FromID").(map[int64]transactionlog.UserLiveSpend)
	for _, userID := range param.UserIDs {
		resp.Data[userID] = util.Float2DP(usersLiveSpendMap[userID].TotalSpend)
	}
	return resp, nil
}
