package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type roomInfo struct {
	RoomID          int64                  `json:"room_id"`
	CatalogID       int64                  `json:"catalog_id"`
	Name            string                 `json:"name"`
	Announcement    string                 `json:"announcement"`
	CreatorID       int64                  `json:"creator_id"`
	CreatorUsername string                 `json:"creator_username"`
	Status          *room.SimpleRoomStatus `json:"status"`
	CoverURL        string                 `json:"cover_url"`
}

type userLiveRank struct {
	*usersrank.Info
	Room *roomInfo `json:"room"`
}

type userLiveRankResp struct {
	Data []*userLiveRank `json:"data"`
}

type userLiveRankParam struct {
	RankType int   `json:"type"`
	RankNum  int64 `json:"rank_num"`

	listUserIDs []int64
	resp        userLiveRankResp
}

func newUserLiveRankParam(ctx *handler.Context) (*userLiveRankParam, error) {
	param := new(userLiveRankParam)
	err := ctx.BindJSON(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if !goutil.HasElem([]int{usersrank.TypeDay, usersrank.TypeWeek,
		usersrank.TypeMonth, usersrank.TypeHour}, param.RankType) {
		return nil, actionerrors.ErrParams
	}
	if param.RankNum > usersrank.RankLen(param.RankType) {
		return nil, actionerrors.ErrParams
	}
	return param, nil
}

func (param *userLiveRankParam) findRank() error {
	tmpList, err := usersrank.FindRankInfoWithRankNum(param.RankType, goutil.TimeNow(), param.RankNum)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.resp.Data = make([]*userLiveRank, 0, len(tmpList))
	param.listUserIDs = make([]int64, 0, len(tmpList))
	for _, tmp := range tmpList {
		param.resp.Data = append(param.resp.Data, &userLiveRank{Info: tmp})
		param.listUserIDs = append(param.listUserIDs, tmp.UserID)
	}
	return nil
}

func (param *userLiveRankParam) findRooms() error {
	if len(param.listUserIDs) == 0 {
		return nil
	}
	rooms, err := room.FindSimpleMapByCreatorID(param.listUserIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	for _, dataInfo := range param.resp.Data {
		r, ok := rooms[dataInfo.UserID]
		if !ok || r == nil {
			// 理论上应该是都有的，不过可能房间被删除
			logger.WithField("user_id", dataInfo.UserID).Error("未查询到直播间信息")
			continue
		}
		dataInfo.Room = &roomInfo{
			RoomID:          r.RoomID,
			CatalogID:       r.CatalogID,
			Name:            r.Name,
			Announcement:    r.Announcement,
			CreatorID:       r.CreatorID,
			CreatorUsername: r.CreatorUsername,
			Status: &room.SimpleRoomStatus{
				Open: r.Status.Open,
			},
			CoverURL: r.CoverURL,
		}
	}
	return nil
}

// ActionLiveUserRank 主播收益榜列表
/**
 * @api {post} /rpc/user/live/rank
 *
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} type 榜单类型（1: 日榜；2: 周榜；3: 月榜；4: 小时榜）
 * @apiParam {Number} rank_num 榜单数量
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "user_id": 9467681, // 用户 ID
 *           "username": "2Fire", // 用户名称
 *           "iconurl": "https://test.com/profile/icon01.png", // 用户头像
 *           "revenue": 60927, // 收益（分数值）
 *           "rank": 1, // 排名
 *           "rank_up": 10131, // 前一名的收益 - 自己的收益 + 1
 *           "room": { // 直播间信息
 *             "room_id": 868858629,
 *             "catalog_id": 145,
 *             "name": "听歌进",
 *             "announcement": "欢迎来到我的直播间！",
 *             "creator_id": 9467681,
 *             "creator_username": "2Fire",
 *             "status": {
 *               "open": 1
 *             },
 *             "cover_url": "https://test.com/fmcovers/test.jpg" // 直播间封面
 *           }
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionLiveUserRank(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newUserLiveRankParam(c)
	if err != nil {
		return nil, err
	}
	err = param.findRank()
	if err != nil {
		return nil, err
	}
	err = param.findRooms()
	if err != nil {
		return nil, err
	}
	return param.resp, nil
}
