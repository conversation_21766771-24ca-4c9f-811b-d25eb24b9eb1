package webrpc

/**
 * @api {post} /rpc/live-service/notify/batch-send 批量发送飘屏消息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Array} items 飘屏消息列表
 * @apiParam {Number} items.bubble_id 气泡样式 ID
 * @apiParam {String} items.message 飘屏消息模板，是 html 的格式，html 的特殊字符需要转义。所有文本内容都必须使用 font 标签包裹。支持以下变量：
 * <br>• **${normal_color}** - 气泡普通颜色（自动从气泡样式获取）
 * <br>• **${highlight_color}** - 气泡高亮颜色（自动从气泡样式获取）
 * <br>• **${creator_username}** - 主播用户名（当 format_params 中包含 creator_id 或 room_id 时自动获取）
 * <br>• **${username}** - 用户名（当 format_params 中包含 user_id 时自动获取）
 * <br>• 其他变量如 creator_id、room_id、user_id、guild_name、team_name、start_time、end_time、num、score、rank 等（通过 format_params 指定）
 * @apiParam {Number} [items.room_id] 房间 ID（用于点击飘屏消息后跳转到对应直播间，不传则不跳转）
 * @apiParam {String} [items.open_url] 点击飘屏消息后打开的链接，链接支持 msr-0 标准（其中，在直播间内 url 参数中包含 webview=3 时支持半窗打开）
 *                                     当 room_id 不为 0 时先处理直播间跳转，再处理 open_url
 *                                     msr-0 文档：https://git.bilibili.co/maoer/missevan-doc/-/blob/master/app/rules/msr-0.md
 * @apiParam {Number} [items.notify_queue=0] 消息优先级队列，值越大优先级越高
 * @apiParam {Number} [items.delay=0] 发送延迟（毫秒），为 0 时立即发送
 * @apiParam {Object} [items.format_params] 自定义格式化参数，用于替换消息模板中的变量。支持以下特殊参数：
 * <br>• **creator_id** - 主播 ID（int64 类型），传入后会自动获取 creator_username（主播用户名）
 * <br>• **room_id** - 房间 ID（int64 类型），传入后会自动获取 creator_username（房间主播用户名）
 * <br>• **user_id** - 用户 ID（int64 类型），传入后会自动获取 username（用户名）
 * <br>• 其他自定义参数如 guild_name、team_name、start_time、end_time、num、score、rank 等（string 类型）
 * <br>• 服务端会自动对格式化参数中的字符串进行 HTML 转义处理，调用方无需预先转义
 *
 * @apiParamExample {json} Request-Example:
 *     {
 *       "items": [
 *         {
 *           "bubble_id": 1001,
 *           "message": "<font color=\"${normal_color}\">恭喜</font> <font color=\"${highlight_color}\">${creator_username}</font> <font color=\"${normal_color}\">成为冠军主播！</font>",
 *           "room_id": 123456,
 *           "notify_queue": 1,
 *           "delay": 2000,
 *           "format_params": {
 *             "creator_id": 789012
 *           }
 *         },
 *         {
 *           "bubble_id": 1002,
 *           "message": "<font color=\"${normal_color}\">恭喜</font> <font color=\"${highlight_color}\">${username}</font> <font color=\"${normal_color}\">获得 ${score} 分！</font>",
 *           "open_url": "http://fm.example.com/open?webview=3&room_id=__ROOM_ID__",
 *           "notify_queue": 0,
 *           "delay": 1500,
 *           "format_params": {
 *             "user_id": 999999,
 *             "score": "10000"
 *           }
 *         },
 *         {
 *           "bubble_id": 1003,
 *           "message": "<font color=\"${normal_color}\">恭喜</font> <font color=\"${highlight_color}\">${guild_name}</font> <font color=\"${normal_color}\">公会在 ${start_time}-${end_time} 时段荣获第 ${rank} 名！</font>",
 *           "notify_queue": 0,
 *           "delay": 3000,
 *           "format_params": {
 *             "guild_name": "星辰<特殊>公会",
 *             "start_time": "19:00",
 *             "end_time": "22:00",
 *             "rank": "1"
 *           }
 *         },
 *         {
 *           "bubble_id": 1004,
 *           "message": "<font color=\"${normal_color}\">主播</font> <font color=\"${highlight_color}\">${creator_username}</font> <font color=\"${normal_color}\">和用户</font> <font color=\"${highlight_color}\">${username}</font> <font color=\"${normal_color}\">在活动中获得了 ${score} 分！</font>",
 *           "notify_queue": 0,
 *           "delay": 0,
 *           "format_params": {
 *             "creator_id": 123456,
 *             "user_id": 999999,
 *             "score": "88888"
 *           }
 *         }
 *       ]
 *     }
 *
 * @apiSuccess {Number} code 状态码
 * @apiSuccess {String} message 响应信息
 * @apiSuccess {Object} data 响应数据
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "message": "success",
 *       "data": null
 *     }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} message 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 */
