package webrpc

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGuildFixHotRecommendVacancy(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	ctx := handler.NewTestContext(http.MethodPost, "/rpc/guild/fix-hot-recommend-vancancy", false, nil)

	resp, err := ActionGuildFixHotRecommendVacancy(ctx)
	require.NoError(err)
	assert.Equal(true, resp)
}

func TestFixGuildHotRecommendVacancy(t *testing.T) {
	require := require.New(t)

	v1 := recommend.GuildRecommendVacancy{
		GuildID:   1,
		Position:  4,
		StartTime: 1,
		EndTime:   2,
		Vacancy:   1,
	}
	v2 := recommend.GuildRecommendVacancy{
		GuildID:        2,
		Position:       6,
		StartTime:      1,
		EndTime:        2,
		Vacancy:        2,
		InitialVacancy: 4,
		EditedVacancy:  5,
	}
	v3 := recommend.GuildRecommendVacancy{
		GuildID:   3,
		Position:  4,
		StartTime: 2,
		EndTime:   3,
		Vacancy:   3,
	}
	require.NoError(service.LiveDB.Create(&v1).Error)
	require.NoError(service.LiveDB.Create(&v2).Error)
	require.NoError(service.LiveDB.Create(&v3).Error)
	require.NoError(fixGuildHotRecommendVacancy())
}

func TestFixVacancyByPeriod(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	guildID1 := int64(56)
	guildID2 := int64(57)
	startTime := int64(20)
	endTime := int64(40)
	position := 4
	v1 := recommend.GuildRecommendVacancy{
		GuildID:   guildID1,
		Position:  position,
		Vacancy:   1,
		StartTime: startTime,
		EndTime:   endTime,
	}
	v2 := recommend.GuildRecommendVacancy{
		GuildID:   guildID2,
		Position:  position,
		Vacancy:   1,
		StartTime: startTime,
		EndTime:   endTime,
	}
	require.NoError(service.LiveDB.Create(&v1).Error)
	require.NoError(service.LiveDB.Create(&v2).Error)
	r1 := recommend.GuildRecommendSquareHot{
		Position:  position,
		GuildID:   guildID1,
		StartTime: startTime,
		EndTime:   endTime - 1,
	}
	r2 := recommend.GuildRecommendSquareHot{
		Position:  position,
		GuildID:   guildID1,
		StartTime: startTime - 1,
		EndTime:   startTime + 1,
	}
	r3 := recommend.GuildRecommendSquareHot{
		Position:  position,
		GuildID:   guildID1,
		StartTime: startTime - 1,
		EndTime:   startTime,
	}
	r4 := recommend.GuildRecommendSquareHot{
		Position:  position,
		GuildID:   guildID1,
		StartTime: endTime,
		EndTime:   endTime + 1,
	}
	require.NoError(service.LiveDB.Create(&r1).Error)
	require.NoError(service.LiveDB.Create(&r2).Error)
	require.NoError(service.LiveDB.Create(&r3).Error)
	require.NoError(service.LiveDB.Create(&r4).Error)

	fixVacancy(v1)
	fixVacancy(v2)
	guild1Vacancy := new(recommend.GuildRecommendVacancy)
	require.NoError(recommend.GuildRecommendVacancy{}.DB().
		Select("vacancy, initial_vacancy, edited_vacancy").
		First(&guild1Vacancy, "guild_id = ? AND start_time = ? AND position = ?",
			guildID1, startTime, position).Error)
	assert.Equal(int64(1), guild1Vacancy.Vacancy)
	assert.Equal(int64(3), guild1Vacancy.InitialVacancy)
	assert.Equal(int64(3), guild1Vacancy.EditedVacancy)
	guild2Vacancy := new(recommend.GuildRecommendVacancy)
	require.NoError(recommend.GuildRecommendVacancy{}.DB().
		Select("vacancy, initial_vacancy, edited_vacancy").
		First(&guild2Vacancy, "guild_id = ? AND start_time = ? AND position = ?",
			guildID2, startTime, position).Error)
	assert.Equal(int64(1), guild2Vacancy.Vacancy)
	assert.Equal(int64(1), guild2Vacancy.InitialVacancy)
	assert.Equal(int64(1), guild2Vacancy.EditedVacancy)
}
