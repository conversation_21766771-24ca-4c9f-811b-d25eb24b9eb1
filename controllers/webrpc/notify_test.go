package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestAvatar(t *testing.T) {
	assert := assert.New(t)

	assert.Empty(avatar(""))
	assert.Equal("icon01.png", avatar("https://static.maoercdn.com/avatars/icon01.png"))
	assert.Equal("icon01.png", avatar("http://static.maoercdn.com/avatars/icon01.png"))
	assert.Equal("https://static.maoercdn.com/avatars/", avatar("https://static.maoercdn.com/avatars/"))
	assert.Equal("https://static.maoercdn.com/avatars/", avatar("http://static.maoercdn.com/avatars/"))
	assert.Equal("https://static.maoercdn.com/mimages/201409/24/e00d39ad91235577072505f5377fac52145452.png", avatar("http://static.maoercdn.com/mimages/201409/24/e00d39ad91235577072505f5377fac52145452.png"))
	assert.Equal("https://static.maoercdn.com/mimages/201409/24/e00d39ad91235577072505f5377fac52145452.png", avatar("https://static.maoercdn.com/mimages/201409/24/e00d39ad91235577072505f5377fac52145452.png"))
	assert.Equal("202009/27/fcf84b2ce5ac2ecc81088219ada120bf055208.jpg", avatar("https://static.maoercdn.com/avatars/202009/27/fcf84b2ce5ac2ecc81088219ada120bf055208.jpg"))
}

func TestFindMowangskuser(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var userID int64 = 12
	user, err := findMowangskuser(userID)
	require.NoError(err)
	assert.Equal("零月", user.UserName)
	assert.Equal(userID, user.ID)

	// 数据不存在情况
	_, err = findMowangskuser(709148093454412)
	assert.EqualError(err, "无法找到该用户")
}

func TestActionNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUser := new(userInfo)
	testUser.IconURL = new(string)
	*testUser.IconURL = "https://static.missevan.com/avatars/icon02.png"
	testUser.Username = new(string)
	*testUser.Username = "测试"
	testUser.Confirm = new(uint)
	*testUser.Confirm = 3

	var userID int64 = 6
	c := handler.NewTestContext("POST", "/user/notify", false,
		notifyBody{
			UserID: userID,
			Event:  notifyTypeUpdated,
			User:   testUser,
		})

	r, err := ActionNotify(c)
	require.NoError(err)
	notify, ok := r.(*notifyRespInfo)
	require.True(ok)
	require.NotNil(notify)
	// 验证数据库
	i, err := liveuser.Find(userID)
	require.NoError(err)
	require.NotNil(i)
	assert.Equal("“猫耳FM就是M站的APP哦！”", i.Introduction)
	assert.Equal("测试", i.Username)
	assert.Equal("https://static.missevan.com/avatars/icon02.png", i.IconURL)
	assert.Equal("icon02.png", i.Avatar)
	assert.Equal(int32(3), i.Confirm)
	// 验证房间
	// 不存在不会创建
	filter := bson.M{
		"creator_id": userID,
	}
	testRoom, err := room.FindOne(filter)
	require.NoError(err)
	assert.Nil(testRoom)
	// 验证更新
	userID = int64(892)
	roomID := int64(369892)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	u := bson.M{
		"user_id":  userID,
		"username": "987654",
	}
	_, err = liveuser.Collection().UpdateOne(ctx, bson.M{"user_id": userID},
		bson.M{"$set": u}, options.Update().SetUpsert(true))
	require.NoError(err)
	*testUser.Username = "987654321"
	_, err = room.Update(roomID, bson.M{"creator_username": "987654"})
	require.NoError(err)
	c = handler.NewTestContext("POST", "/user/notify", false,
		notifyBody{
			UserID: userID,
			Event:  notifyTypeUpdated,
			User:   testUser,
		})
	r, err = ActionNotify(c)
	require.NoError(err)
	notify, ok = r.(*notifyRespInfo)
	require.True(ok)
	require.NotNil(notify)
	require.Equal(1, notify.OK)
	filter = bson.M{"room_id": roomID}
	testRoom, err = room.FindOne(filter)
	require.NoError(err)
	require.NotNil(testRoom)
	assert.Equal(*testUser.Username, testRoom.CreatorUsername)
	// 验证用户不存在情况
	var notFoundUserID int64 = 57897897
	c = handler.NewTestContext("POST", "/user/notify", false,
		notifyBody{
			UserID: notFoundUserID,
			Event:  notifyTypeUpdated,
			User:   testUser,
		})
	r, err = ActionNotify(c)
	require.NoError(err)
	resp, ok := r.(*notifyRespInfo)
	require.True(ok)
	assert.Zero(resp.OK)
}
