package webrpc

import (
	"fmt"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/webrpc/cron"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/viewlog"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/models/redis/blocklist"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	usermiddleware "github.com/MiaoSiLa/missevan-go/middlewares/user"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const minValuableUserSpend = 1 // 用户计入统计热度最小消费，单位：钻石

// ActionRoomUserInfo 获取指定直播间内用户信息
/**
 * @api {post} /rpc/room/userinfo 获取指定直播间内用户信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "user": {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#f45b41"
 *           }, {
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "avatar_frame",
 *             "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *           }
 *         ],
 *         "vehicle": { // 进场座驾，没有座驾或用户进场隐身时没有该字段
 *           "effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga", // 安卓 5.5.0 因为 MP4 支持有问题，所以不是优先取 mp4，而是按顺序取
 *           "web_effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga",
 *           "effect_duration": 10000,
 *           "message_bar": {
 *             "message": "<b>某某骑着某座驾入场</b>", // 进场消息需要支持 html
 *             "image_url": "http://aaa_1_2_3_4.png"
 *           }
 *         },
 *         "custom_welcome_message": { // 自定义进场欢迎语，用户没有自定义或有 entry_bubble 时没有该字段，后续会都改为 entry_bubble.welcome_message.text 直接下发
 *           "text": "欢迎光临本直播间"
 *         },
 *         "entry_bubble": { // 进场通知，没有进场通知或用户进场隐身时没有该字段
 *           "image_url": "http://entry_image.png",
 *           "entry_style": 0, // 进场通知样式，0: 默认（可不下发），1: 从左往右展开（黑卡 3、4 使用）
 *           "welcome_message": { // 进场通知的文案
 *             "text": "欢迎 XXX 用户进入直播间", // 进场欢迎语，用户有自定义时改为下发自定义的欢迎语
 *             "colors": "#000000;#FFFFFF", // 文案的颜色，渐变色用分号区分两个颜色，非渐变色则只下发一个颜色
 *           }
 *         }
 *       },
 *       "room_medal": { // 用户在对应房间下的粉丝勋章信息，没有信息不返回
 *          "name": "test",
 *          "level": 1,
 *          "frame_url": "https://static-test.maoercdn.com/gifts/cardfarmes/000.png",
 *          "name_color": "#FFFFFF",
 *          "super_fan": {
 *            "expire_time": 1584808200
 *          }
 *        },
 *       "has_invisible_roles": false, // 是否用户进场隐身角色
 *       "is_valuable_user": true, // 是否拥有统计热度和贵宾榜资格
 *       "is_invisible": false // 是否设置进场隐身
 *     }
 *   }
 *
 */
func ActionRoomUserInfo(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID int64 `json:"user_id"`
		RoomID int64 `json:"room_id"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.RoomID <= 0 || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	var resp liverpc.RoomUserInfoResp
	resp.User, err = liveuser.FindOneSimple(bson.M{"user_id": param.UserID},
		&liveuser.FindOptions{FindTitles: true, RoomID: param.RoomID})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if resp.User == nil {
		return nil, actionerrors.ErrUserNotFound
	}
	r, err := room.FindOne(bson.M{"room_id": param.RoomID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if param.UserID == r.CreatorID {
		// 主播本人不需要判断是否被拉黑封禁、是否进场隐身、查询进场座驾
		return resp, nil
	}

	resp.RoomMedal, err = livemedal.FindOne(bson.M{
		"user_id": param.UserID,
		"room_id": param.RoomID,
		"status":  bson.M{"$gt": livemedal.StatusPending},
	}, nil, livemedal.FindOptions{OnlyMedal: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	gs, uv, err := userstatus.UserGeneral(param.UserID, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp.HasInvisibleRoles = liveim.HasInvisibleRoles(c.UserContext(), param.UserID)

	// 是否统计热度和贵宾榜资格
	// 未消费用户通过白给礼物获取并佩戴了当前房间的勋章也拥有贵宾榜资格
	// 未消费用户通过贵族体验卡获得贵族也拥有贵宾榜资格
	// WORKAROUND: 未消费用户通过白给礼物获取并佩戴了当前房间的勋章临时也统计热度
	resp.IsValuableUser = (gs.TotalSpend >= minValuableUserSpend) ||
		(resp.RoomMedal != nil && resp.RoomMedal.Status == livemedal.StatusShow) ||
		(uv != nil && uv.IsActive())
	// 是否隐身中
	if resp.HasInvisibleRoles || (gs.Invisible != nil && *gs.Invisible) {
		resp.IsInvisible = true
	}
	if !resp.IsInvisible {
		liveuser.AssignEntryAppearances(resp.User)
		liveuser.AssignCustomWelcomeMessage(resp.User)
	}
	return &resp, nil
}

// ActionRoomUsers 获取指定直播间内批量用户信息
/**
 * @api {post} /rpc/room/users 获取指定直播间内批量用户信息
 * @apiDescription 忽略不存在的用户信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_ids 用户 ID
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccess {Number} code
 * @apiSuccess {[]Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "user_id": 10,
 *         "username": "bless",
 *         "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *         "titles": [
 *           {
 *             "type": "staff",
 *             "name": "超管",
 *             "color": "#f45b41"
 *           }, {
 *             "type": "level",
 *             "level": 9
 *           }, {
 *             "type": "avatar_frame",
 *             "icon_url": "https://static.missevan.com/gifts/avatarframes/002.png"
 *           }
 *         ],
 *         "vehicle": { // 进场座驾，没有座驾或用户进场隐身时没有该字段
 *           "effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga", // 安卓 5.5.0 因为 MP4 支持有问题，所以不是优先取 mp4，而是按顺序取
 *           "web_effect_url": "*.mp4;*.webp;*.webm;*.png;*.lottie;*.svga",
 *           "effect_duration": 10000,
 *           "message_bar": {
 *             "message": "<b>某某骑着某座驾入场</b>", // 进场消息需要支持 html
 *             "image_url": "http://aaa_1_2_3_4.png"
 *           }
 *         },
 *         "custom_welcome_message": { // 自定义进场欢迎语，用户没有自定义或有 entry_bubble 时没有该字段，后续会都改为 entry_bubble.welcome_message.text 直接下发
 *           "text": "欢迎光临本直播间"
 *         },
 *         "entry_bubble": { // 进场通知，没有进场通知或用户进场隐身时没有该字段
 *           "image_url": "http://entry_image.png",
 *           "welcome_message": { // 进场通知的文案
 *             "text": "欢迎 XXX 用户进入直播间", // 进场欢迎语，用户有自定义时改为下发自定义的欢迎语
 *             "colors": "#000000;#FFFFFF", // 文案的颜色，渐变色用分号区分两个颜色，非渐变色则只下发一个颜色
 *           }
 *         }
 *       }
 *     ]
 *   }
 *
 */
func ActionRoomUsers(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserIDs []int64 `json:"user_ids"`
		RoomID  int64   `json:"room_id"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.RoomID <= 0 || len(param.UserIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	users, err := liveuser.ListSimpleByOpts(&liveuser.ListOptions{
		Filter:               bson.M{"user_id": bson.M{"$in": param.UserIDs}},
		FindTitles:           true,
		RoomID:               param.RoomID,
		FindEntryAppearances: true,
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	assignUserCustomWelcomeMessages(users)
	return users, nil
}

func assignUserCustomWelcomeMessages(users []*liveuser.Simple) {
	userIDs := make([]int64, 0, len(users))
	for _, u := range users {
		userIDs = append(userIDs, u.UserID())
	}
	msgMap, err := usermeta.FindUserValidCustomWelcomeMessageMap(userIDs)
	if err != nil {
		logger.Error(err)
		return
	}
	for i, u := range users {
		msg := msgMap[u.UserID()]
		if msg == nil || msg.Text == "" {
			continue
		}
		if u.EntryBubble == nil || u.EntryBubble.WelcomeMessage == nil {
			// 如果没有进场通知，当前就只给 CustomWelcomeMessage 赋值（临时兼容用）
			users[i].CustomWelcomeMessage = msg
			continue
		}
		// 替换进场通知文本
		users[i].EntryBubble.WelcomeMessage.Text = msg.Text
	}
}

// UserInfo is the type of the data returned by ActionUser
type UserInfo struct {
	ID            int64  `json:"id"`
	UserName      string `json:"username"`
	UserIntro     string `json:"userintro"`
	Confirm       uint   `json:"confirm"`
	IconURL       string `json:"iconurl"`
	Authenticated uint   `json:"authenticated"`
	FansNum       int64  `json:"fansnum"`
	FollowNum     int64  `json:"follownum"`
	Ban           bool   `json:"ban"`
}

// ActionUser 获取单个用户信息
/**
 * @api {post} /rpc/user/info 获取单个用户信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number} user_id 用户 ID
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 * @apiSuccess (200) {string} info.id 用户 ID
 * @apiSuccess (200) {number} info.username 用户名
 * @apiSuccess (200) {number} info.userintro 用户简介
 * @apiSuccess (200) {number} info.confirm 用户权限或身份标识
 * @apiSuccess (200) {number} info.iconurl icon url
 * @apiSuccess (200) {number} info.authenticated 加 V 认证标识，从 confirm 中得来的
 * @apiSuccess (200) {number} info.fansnum 粉丝数
 * @apiSuccess (200) {number} info.follownum 关注数
 * @apiSuccess (200) {number} info.ban 是否在黑名单
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 */
func ActionUser(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID int64 `json:"user_id"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	u := usermiddleware.User{}
	u.ID = param.UserID
	var user user.MowangskUser
	err = service.DB.Where("id = ?", u.ID).Take(&user).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrCannotFindUser
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	ban, err := blocklist.Exists(u.ID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return &UserInfo{
		ID:            user.ID,
		UserName:      user.UserName,
		UserIntro:     user.UserIntro,
		Confirm:       user.Confirm,
		IconURL:       user.BoardIconURL2,
		Authenticated: user.Authenticated,
		FansNum:       user.FansNum,
		FollowNum:     user.FollowNum,
		Ban:           ban,
	}, nil
}

// ActionUserTitles 获取用户的 titles
/**
 * @api {post} /rpc/user/titles 获取用户的 titles
 * @apiDescription 获取用户的 titles
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     {
 *       "code": 0,
 *       "info": [ // 可能为空数组
 *         {
 *           "type": "avatar_frame",
 *           "icon_url": "https://static-test.missevan.com/gifts/avatarframes/007.webp"
 *         },
 *         {
 *           "type": "username",
 *           "color": "#F45B41"
 *         },
 *         {
 *           "type": "medal",
 *           "name": "test",
 *           "level": 1,
 *           "super_fan": { // 超粉标识字段, 只有超粉生效用户返回该字段, 客户端不用特意判断 expire_time
 *             "expire_time": 1576116700 // 秒级时间戳
 *           },
 *           "frame_url": "http://test.png" // 用户粉丝徽章
 *         },
 *         {
 *           "type": "identity_badge", // 身份铭牌
 *           "icon_url": "https://static-test.maoercdn.com/live/identitybadges/10001.webp", // 图标地址
 *           "appearance_id": 10001 // 图标 ID
 *         }
 *       ]
 *     }
 */
func ActionUserTitles(c *handler.Context) (handler.ActionResponse, error) {
	res := make([]liveuser.Title, 0)
	var param struct {
		UserID int64 `json:"user_id"`
	}
	_ = c.BindJSON(&param)
	if param.UserID <= 0 {
		return res, nil
	}
	u, err := liveuser.FindUserInfo(param.UserID, 0)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if u != nil {
		res = u.Titles
	}
	return res, nil
}

// ActionUserBlockAdd 批量将用户添加到黑名单
/**
 * @api {post} /rpc/user/block/add 批量将用户添加到黑名单
 * @apiDescription 主站管理后台用户添加黑名单
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} user_ids 封禁用户 ID 数组
 * @apiParam {Number} [expire_time=0] 秒级时间戳, 如：1584808800, 永久封禁时传 0 或不传
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionUserBlockAdd(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		UserIDs    []int64 `json:"user_ids" binding:"gt=0"`
		ExpireTime int64   `json:"expire_time" binding:"gte=0"`
	}
	err := c.BindJSON(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if params.ExpireTime != 0 && params.ExpireTime <= goutil.TimeNow().Unix() {
		return nil, actionerrors.ErrParamsMsg("封禁时间错误")
	}
	data := make([]*redis.Z, len(params.UserIDs))
	for i := range params.UserIDs {
		if params.ExpireTime == 0 {
			// 永封
			data[i] = &redis.Z{Score: blocklist.StatusBlockUserForever, Member: params.UserIDs[i]}
		} else {
			data[i] = &redis.Z{Score: float64(params.ExpireTime), Member: params.UserIDs[i]}
		}
	}
	err = service.Redis.ZAdd(keys.KeyUsersBlockList0.Format(), data...).Err()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

// ActionUserBlockRemove 批量将用户移除黑名单
/**
 * @api {post} /rpc/user/block/remove 批量将用户移除黑名单
 * @apiDescription 批量将用户移除黑名单, 忽略不存在的用户
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} user_ids 封禁用户 ID 数组
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionUserBlockRemove(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		UserIDs []int64 `json:"user_ids"`
	}
	err := c.BindJSON(&params)
	if err != nil || len(params.UserIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	memberKeys := make([]interface{}, 0, len(params.UserIDs))
	for _, userID := range params.UserIDs {
		memberKeys = append(memberKeys, userID)
	}
	err = service.Redis.ZRem(keys.KeyUsersBlockList0.Format(), memberKeys...).Err()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

type userLevelResp struct {
	LiveLevel     int             `json:"live_level"`
	NobelLevel    int             `json:"noble_level"`
	HighnessLevel int             `json:"highness_level"`
	RoomMedal     *livemedal.Mini `json:"room_medal,omitempty"`
}

// ActionUserLevel 获得用户的直播等级和贵族等级
/**
 * @api {post} /rpc/user/level 获得用户的直播等级和贵族等级
 * @apiDescription 获得用户的直播等级和贵族等级
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} [room_id] 房间 ID，需要房间粉丝牌信息必传
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "live_level": 1, // 直播等级
 *       "noble_level": 1, // 贵族等级
 *       "highness_level": 1, // 上神等级
 *       "room_medal": { // 用户在对应房间下的粉丝勋章信息，不传 room_id 或没有信息不返回
 *         "name": "test",
 *         "level": 1,
 *         "frame_url": "https://static-test.maoercdn.com/gifts/cardfarmes/000.png",
 *         "name_color": "#FFFFFF",
 *         "super_fan": {
 *           "expire_time": 1584808200
 *         }
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 500020004
 * @apiError (500) {String} info 无法找到该用户
 *
 */
func ActionUserLevel(c *handler.Context) (handler.ActionResponse, error) {
	var params struct {
		UserID int64 `json:"user_id"`
		RoomID int64 `json:"room_id"`
	}
	err := c.BindJSON(&params)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	u, err := liveuser.Find(params.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if u == nil {
		return nil, actionerrors.ErrCannotFindUser
	}

	resp := new(userLevelResp)
	resp.LiveLevel = usercommon.Level(u.Contribution)
	uvs, err := vip.UserVipInfos(params.UserID, false, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if highness := uvs[vip.TypeLiveHighness]; highness != nil && highness.IsActive() {
		resp.HighnessLevel = highness.Level
	}
	if noble := uvs[vip.TypeLiveNoble]; noble != nil && noble.IsActive() {
		resp.NobelLevel = noble.Level
	}

	if params.RoomID == 0 {
		return resp, nil
	}

	roomMedal, err := livemedal.FindOwnedMedal(params.UserID,
		params.RoomID, livemedal.FindOptions{OnlyMedal: true},
	)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomMedal != nil {
		resp.RoomMedal = &roomMedal.Mini
	}

	return resp, nil
}

type userLevelUpParams struct {
	UserID    int64 `json:"user_id"`
	RoomID    int64 `json:"room_id"`
	LevelFrom int   `json:"level_from"`
	LevelTo   int   `json:"level_to"`
}

// ActionUserLevelUp 直播间处理用户等级升级
/**
 * @api {post} /rpc/user/level-up 直播间处理用户等级升级
 * @apiDescription 处理用户等级升级后的飘屏、特效、系统通知、刷新礼物栏等操作
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} [room_id] 直播间 ID, 不传或者值为 0 表示在直播间外升级
 * @apiParam {Number} level_from 升级前的等级
 * @apiParam {Number} level_to 升级后的等级
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "msg": "success"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 500020004
 * @apiError (500) {String} info 无法找到该用户
 *
 * @apiSuccessExample {json} WebSocket 全站 WS 升级飘屏消息
 *   {
 *     "type": "notify",
 *     "notify_type": "message",
 *     "event": "new",
 *     "message": "<font color=\"#FFE072\">恭喜</font> <font color=\"#FF8F52\">楚川</font> <font color=\"#FFE072\">升级到</font> <font color=\"#FF8F52\">182</font> <font color=\"#FFE072\">级~</font>",
 *     "notify_bubble": {
 *       "type": "custom",
 *       "bubble_id": 12006,
 *       "image_url": "https://static-test.maoercdn.com/live/bubbles/b12005_0_102_0_175.png"
 *     }
 *   }
 *
 * @apiSuccessExample {json} WebSocket 直播间 WS 升级特效消息
 *   {
 *     "type": "user",
 *     "event": "level_up",
 *     "room_id": 441179085,
 *     "message_bar": {
 *       "image_url": "https://static-test.maoercdn.com/live/userlevels/messagebars/150_0_122_0_122.png",
 *       "message": "<font color=\"#FFE072\">恭喜您升级到</font> <font color=\"#FF8F52\">180</font> <font color=\"#FFE072\">级</font>"
 *     },
 *     "level": {
 *       "level": 180,
 *       "icon_url": "https://static-test.maoercdn.com/live/userlevels/level180.webp",
 *       "effect_url": "https://static-test.maoercdn.com/live/userlevels/effects/v2/180.mp4",
 *       "web_effect_url": "https://static-test.maoercdn.com/live/userlevels/effects/v2/180-web.mp4",
 *       "effect_duration": 3000
 *     },
 *     "user": {
 *       "user_id": 10000001600,
 *       "username": "楚川",
 *       "iconurl": "https://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "noble",
 *           "name": "练习生",
 *           "level": 1
 *         }
 *       ],
 *       "confirm": 2,
 *       "introduction": ""
 *     },
 *     "bubble": {
 *       "type": "message",
 *       "image_url": "https://static-test.maoercdn.com/live/bubbles/message/highnessbox_36_36_36_36.png",
 *       "text_color": "#FFEAC4;#FFD672",
 *       "frame_url": "https://static-test.maoercdn.com/live/bubbles/message/highnessframe_corner15.webp"
 *     },
 *     "disable_effect": false
 *   }
 *
 * @apiSuccessExample {json} WebSocket 全站 WS 升级特效消息
 *   {
 *     "type": "notify",
 *     "notify_type": "user",
 *     "event": "level_up",
 *     "room_id": 441179085,
 *     "message_bar": {
 *       "image_url": "https://static-test.maoercdn.com/live/userlevels/messagebars/150_0_122_0_122.png",
 *       "message": "<font color=\"#FFE072\">恭喜您升级到</font> <font color=\"#FF8F52\">200</font> <font color=\"#FFE072\">级</font>"
 *     },
 *     "level": {
 *       "level": 200,
 *       "icon_url": "https://static-test.maoercdn.com/live/userlevels/level200.webp",
 *       "effect_url": "https://static-test.maoercdn.com/live/userlevels/effects/v2/new200.mp4",
 *       "web_effect_url": "https://static-test.maoercdn.com/live/userlevels/effects/v2/new200-web.mp4",
 *       "effect_duration": 3000
 *     },
 *     "user": {
 *       "user_id": 10000001600,
 *       "username": "楚川",
 *       "iconurl": "https://static-test.maoercdn.com/avatars/icon01.png",
 *       "titles": [
 *         {
 *           "type": "noble",
 *           "name": "练习生",
 *           "level": 1
 *         }
 *       ],
 *       "confirm": 2,
 *       "introduction": ""
 *     },
 *     "bubble": {
 *       "type": "message",
 *       "image_url": "https://static-test.maoercdn.com/live/bubbles/message/highnessbox_36_36_36_36.png",
 *       "text_color": "#FFEAC4;#FFD672",
 *       "frame_url": "https://static-test.maoercdn.com/live/bubbles/message/highnessframe_corner15.webp"
 *     },
 *     "disable_effect": false
 *   }
 */
func ActionUserLevelUp(c *handler.Context) (handler.ActionResponse, error) {
	var params userLevelUpParams
	err := c.BindJSON(&params)
	if err != nil || params.UserID <= 0 || params.RoomID < 0 ||
		params.LevelFrom <= 0 || params.LevelFrom >= params.LevelTo {
		return nil, actionerrors.ErrParams
	}
	var creatorUsername string
	if params.RoomID > 0 {
		r, err := room.Find(params.RoomID, &room.FindOptions{DisableAll: true})
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			return nil, actionerrors.ErrCannotFindRoom
		}
		creatorUsername = r.CreatorUsername
	}
	u, err := liveuser.FindOneSimple(bson.M{"user_id": params.UserID}, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if u == nil {
		return nil, actionerrors.ErrUserNotFound
	}
	ap := userstatus.AfterUserLevelUpParam{
		User:            u,
		RoomID:          params.RoomID,
		CreatorUsername: creatorUsername,
		BeforeLevel:     params.LevelFrom,
		AfterLevel:      params.LevelTo,
	}
	ap.AfterUserLevelUp()
	return handler.M{"msg": "success"}, nil
}

// 删除用户直播间访问记录的操作类型
const (
	viewLogDeleteTypeRoom = iota // 根据房间删除
	viewLogDeleteTypeAll         // 全部删除
)

// ActionUserViewLogDelete 删除用户的直播间访问记录
/**
 * @api {post} /rpc/user/viewlog/delete 删除用户的直播间访问记录
 * @apiDescription 删除用户的直播间访问记录
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {number=0,1} type 操作类型，0 为通过房间 ID 删除，1 为全部删除
 * @apiParam {Number[]} [room_ids] 需要删除的房间 ID 列表
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionUserViewLogDelete(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID  int64   `json:"user_id"`
		Type    int64   `json:"type"`
		RoomIDs []int64 `json:"room_ids"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	switch param.Type {
	case viewLogDeleteTypeAll:
		// 删除全部
		err = viewlog.DeleteAll(param.UserID)
	case viewLogDeleteTypeRoom:
		if len(param.RoomIDs) == 0 {
			return nil, actionerrors.ErrParams
		}
		// 根据房间号删除
		err = viewlog.DeleteByRoomIDs(param.UserID, param.RoomIDs)
	default:
		return nil, actionerrors.ErrParams
	}
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return "删除成功", nil
}

// backpackAddParam 给用户添加背包礼物接口的请求参数
type backpackAddParam struct {
	UserIDs []int64 `json:"user_ids"`
	GiftID  int64   `json:"gift_id"`
	Num     int64   `json:"num"`
	EndTime int64   `json:"end_time"`
}

// ActionBackpackAdd 给用户添加背包礼物
/**
 * @api {post} /rpc/user/backpack/add 给用户增加背包礼物
 * @apiDescription 给用户添加背包礼物
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} user_ids 用户 ID 数组
 * @apiParam {Number} gift_id 背包礼物 ID
 * @apiParam {Number} num 礼物数量
 * @apiParam {Number} end_time 背包礼物过期时间
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "发放成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 500020004
 * @apiError (500) {String} info 无法找到该用户
 *
 */
func ActionBackpackAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param backpackAddParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.Num <= 0 {
		return nil, actionerrors.ErrParamsMsg("礼物数量错误")
	}
	now := goutil.TimeNow()
	if param.EndTime <= now.Unix() {
		return nil, actionerrors.ErrParamsMsg("结束时间错误")
	}

	g, err := gift.FindShowingGiftByGiftID(param.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrParamsMsg("礼物不存在")
	}
	if !useritems.IsBackpackGift(g) {
		return nil, actionerrors.ErrParamsMsg("该礼物不是背包礼物")
	}

	// check users
	if len(param.UserIDs) == 0 {
		return nil, actionerrors.ErrParamsMsg("请输入用户 ID")
	}
	duplicates := util.FindInt64Duplicates(param.UserIDs)
	if len(duplicates) != 0 {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("用户 %s 重复输入",
			goutil.JoinInt64Array(duplicates, ", ")))
	}
	userMap, err := liveuser.SimpleSliceToMap(liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": param.UserIDs}}, nil))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userMap) != len(param.UserIDs) {
		errList := make([]int64, 0, len(param.UserIDs))
		for _, u := range param.UserIDs {
			if _, ok := userMap[u]; !ok {
				errList = append(errList, u)
			}
		}
		str := goutil.JoinInt64Array(errList, ", ")
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("用户 %s 不存在", str))
	}

	err = useritems.AddGiftToUsers(param.UserIDs, g, param.Num, useritems.SourceNormal,
		goutil.TimeNow().Unix(), param.EndTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "发放成功", nil
}

type addGiftCustomParam struct {
	UserID int64 `form:"user_id" json:"user_id"`
	GiftID int64 `form:"gift_id" json:"gift_id"`
	Day    int   `form:"day" json:"day"`
}

// ActionGiftCustomAdd 给用户发放礼物赠送资格
/**
 * @api {post} /rpc/user/gift/custom/add 给用户发放礼物赠送资格
 * @apiDescription 给用户发放礼物赠送资格
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} gift_id 礼物 ID
 * @apiParam {Number} day 天数
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "发放成功"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 * @apiError (500) {Number} code 500020004
 * @apiError (500) {String} info 无法找到该用户
 *
 */
func ActionGiftCustomAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param addGiftCustomParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.GiftID <= 0 || param.Day <= 0 {
		return nil, actionerrors.ErrParams
	}
	err = livecustom.AddUserCustomGift(param.UserID, param.GiftID,
		time.Duration(param.Day)*24*time.Hour, true)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "发放成功", nil
}

type birthdayPrivParam struct {
	UserID        int64  `json:"user_id"`
	BirthdateMMDD string `json:"birthdate_mmdd"`
}

// ActionBirthdayPriv 生日设置回调
/**
 * @api {post} /rpc/user/birthday/priv 生日设置回调
 * @apiDescription 生日设置回调
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} birthdate_mmdd 用户 mmdd 格式的生日日期
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 */
func ActionBirthdayPriv(c *handler.Context) (handler.ActionResponse, error) {
	var params birthdayPrivParam
	err := c.BindJSON(&params)
	if err != nil || params.UserID == 0 || params.BirthdateMMDD == "" {
		return nil, actionerrors.ErrParams
	}
	// 确认用户是否今天生日
	now := goutil.TimeNow()
	if !params.isBirthday(now) {
		return "success", nil
	}
	// 确认用户是否超过 45 级
	filter := bson.M{"user_id": params.UserID}
	simple, err := liveuser.FindOneSimple(filter, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if simple == nil || usercommon.Level(simple.Contribution) < 45 {
		return "success", nil
	}
	// 确认用户今年是否已经发放过
	exists, err := livebirthdayprivrecord.IsRewarded(params.UserID, now.Year())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if exists {
		return "success", nil
	}
	// 发放外观
	err = params.sendBirthdayAppearances(now)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

func (p *birthdayPrivParam) isBirthday(now time.Time) bool {
	todayMD := now.Format("0102")
	// TODO: 改为使用 go 的 NeedLeapYearProcess，对应 LeapYearLookForward 的方案
	if !goutil.IsLeapYear(now.Year()) && now.Month() == time.February && now.Day() == 28 {
		// 闰年相关日期需要特殊判断
		return p.BirthdateMMDD == todayMD || p.BirthdateMMDD == user.LeapYearSpecialDateMMDD
	}
	return p.BirthdateMMDD == todayMD
}

func (p *birthdayPrivParam) sendBirthdayAppearances(now time.Time) error {
	// 获取生日特权外观
	appearances, err := appearance.GetBirthdayAppearances()
	if err != nil {
		return err
	}
	err = livebirthdayprivrecord.RecordUserBirthday(p.UserID, now)
	if err != nil {
		return fmt.Errorf("生日特权记录错误：%v", err)
	}
	return cron.SendBirthdayAppearances([]int64{p.UserID}, appearances)
}
