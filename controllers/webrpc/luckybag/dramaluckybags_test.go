package luckybag

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionGetDramaLuckyBags(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := handler.M{}
	c := handler.NewRPCTestContext("/rpc/luckybag/get-drama-luckybags", body)
	_, err := ActionGetDramaLuckyBags(c)
	assert.Equal(actionerrors.ErrParams, err)

	body = handler.M{
		"ipr_ids":   []int64{98, 99},
		"drama_ids": []int64{98, 99},
	}
	c = handler.NewRPCTestContext("/rpc/luckybag/get-drama-luckybags", body)
	resp, err := ActionGetDramaLuckyBags(c)
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(luckyBagInfoResp)
	ipr := r.IPRLuckyBags
	require.Nil(ipr)
	drama := r.DramaLuckyBags
	require.Nil(drama)

	body = handler.M{
		"ipr_ids":   []int64{4567},
		"drama_ids": []int64{1234},
	}
	c = handler.NewRPCTestContext("/rpc/luckybag/get-drama-luckybags", body)
	resp, err = ActionGetDramaLuckyBags(c)
	require.NoError(err)
	require.NotNil(resp)
	r = resp.(luckyBagInfoResp)
	ipr = r.IPRLuckyBags
	require.NotNil(ipr)
	data := ipr[4567]
	assert.Equal(int64(100000007), data.Data[0].RoomID)
	assert.Equal(int64(3457114), data.Data[0].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[0].CreatorIconURL)
	drama = r.DramaLuckyBags
	require.NotNil(drama)
	dramaData := drama[1234]
	assert.Equal(int64(100000007), dramaData.Data[0].RoomID)
	assert.Equal(int64(3457114), dramaData.Data[0].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", dramaData.Data[0].CreatorIconURL)
}

func TestNewLuckyBagInfoParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := handler.M{}
	c := handler.NewRPCTestContext("/rpc/luckybag/get-drama-luckybags", body)
	_, err := newLuckyBagInfoParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body["ipr_ids"] = []int64{99}
	c = handler.NewRPCTestContext("/rpc/luckybag/get-drama-luckybags", body)
	param, err := newLuckyBagInfoParam(c)
	require.NoError(err)
	assert.Equal(1, len(param.IPRIDs))
	assert.Equal(body["ipr_ids"], param.IPRIDs)

	body["drama_ids"] = []int64{98}
	c = handler.NewRPCTestContext("/rpc/luckybag/get-drama-luckybags", body)
	param, err = newLuckyBagInfoParam(c)
	require.NoError(err)
	assert.Equal(1, len(param.DramaIDs))
	assert.Equal(body["drama_ids"], param.DramaIDs)
}

func TestLuckyBagInfoParam_iprLuckyBagsResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &luckyBagInfoParam{}
	assert.Nil(param.iprLuckyBagsResp())
	assert.Nil(param.resp.IPRLuckyBags)

	param.IPRIDs = []int64{99}
	require.NoError(param.iprLuckyBagsResp())
	assert.Nil(param.resp.IPRLuckyBags)

	param.IPRIDs = []int64{4567}
	require.NoError(param.iprLuckyBagsResp())
	ipr := param.resp.IPRLuckyBags
	require.NotNil(ipr)
	data := ipr[4567]
	assert.Equal(int64(4567), data.IPRID)
	assert.Equal("IP 名称", data.IPRName)
	assert.Equal(int64(4), data.Num)
	assert.Equal(3, len(data.Data))
	// 测试第一个福袋房间
	assert.Equal(int64(100000007), data.Data[0].RoomID)
	assert.Equal(int64(3457114), data.Data[0].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[0].CreatorIconURL)
	// 测试第二个福袋房间
	assert.Equal(int64(100000006), data.Data[1].RoomID)
	assert.Equal(int64(3457024), data.Data[1].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[1].CreatorIconURL)
	// 测试第三个福袋房间
	assert.Equal(int64(100000005), data.Data[2].RoomID)
	assert.Equal(int64(3456864), data.Data[2].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[2].CreatorIconURL)
}

func TestLuckyBagInfoParam_dramaLuckyBags(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &luckyBagInfoParam{}
	assert.Nil(param.dramaLuckyBags())
	assert.Nil(param.resp.DramaLuckyBags)

	param.DramaIDs = []int64{99}
	require.NoError(param.dramaLuckyBags())
	assert.Nil(param.resp.DramaLuckyBags)

	param.DramaIDs = []int64{1234}
	require.NoError(param.dramaLuckyBags())
	drama := param.resp.DramaLuckyBags
	require.NotNil(drama)
	data := drama[1234]
	assert.Equal("福袋 rpc", data.DramaName)
	assert.Equal(int64(4), data.Num)
	assert.Equal(3, len(data.Data))
	// 测试第一个福袋房间
	assert.Equal(int64(100000007), data.Data[0].RoomID)
	assert.Equal(int64(3457114), data.Data[0].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[0].CreatorIconURL)
	// 测试第二个福袋房间
	assert.Equal(int64(100000006), data.Data[1].RoomID)
	assert.Equal(int64(3457024), data.Data[1].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[1].CreatorIconURL)
	// 测试第三个福袋房间
	assert.Equal(int64(100000005), data.Data[2].RoomID)
	assert.Equal(int64(3456864), data.Data[2].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data.Data[2].CreatorIconURL)
}

func TestLuckyBagInfoParam_findRoomsMap(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	iprID := int64(4567)

	record := map[int64][]*luckybag.InitiateRecord{
		iprID: {
			{
				RoomID:    100000007,
				CreatorID: 3457114,
			},
			{
				RoomID:    100000006,
				CreatorID: 3457024,
			},
			{
				RoomID:    100000005,
				CreatorID: 3456864,
			},
		},
	}

	roomsMap, err := findRoomsMap(record)
	require.NoError(err)
	require.NotNil(roomsMap)
	// 测试第一个福袋房间
	data := roomsMap[iprID]
	assert.Equal(int64(100000007), data[0].RoomID)
	assert.Equal(int64(3457114), data[0].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data[0].CreatorIconURL)
	// 测试第二个福袋房间
	assert.Equal(int64(100000006), data[1].RoomID)
	assert.Equal(int64(3457024), data[1].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data[1].CreatorIconURL)
	// 测试第三个福袋房间
	assert.Equal(int64(100000005), data[2].RoomID)
	assert.Equal(int64(3456864), data[2].CreatorID)
	assert.Equal("https://static-test.missevan.com/avatars/icon01.png", data[2].CreatorIconURL)
}
