package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

type dramaListParam struct {
	Limit int `json:"limit"`

	uc           mrpc.UserContext
	luckyBagData utils.DramaLuckyBagListData
}

// ActionDramaList 获取广播剧福袋列表
/**
 * @api {post} /rpc/luckybag/drama-list 获取广播剧福袋列表
 * @apiDescription 当福袋所属剧集有 IPR ID 时，通过 IPR 来聚合，当福袋所属剧集没有 IPR ID 时，通过剧集来聚合
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} limit 返回数量，0 表示查全部
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "has_more": true, // 是否有更多数据
 *       "data": [ // 没有数据时返回空数组
 *         {
 *           "ipr_id": 2333, // 剧集所属 IPR ID，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
 *           "ipr_name": "魔道祖师", // 剧集所属 IPR 名，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
 *           "drama_id": 2334, // 剧集 ID，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
 *           "drama_name": "烟火", // 剧集名称，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
 *           "cover_url": "https://static-test.maoercdn.com/cover.png", // 剧集封面
 *           "num": 5, // 当前正在发放此剧集福袋的直播间数量
 *           "rooms": [ // 排名前三的直播间信息
 *             {
 *               "room_id": 100000, // 直播间 ID
 *               "creator_id": 11, // 主播 ID
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png" // 主播头像
 *             },
 *             {
 *               "room_id": 100001,
 *               "creator_id": 12,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             },
 *             {
 *               "room_id": 100002,
 *               "creator_id": 13,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             }
 *           ]
 *         }
 *       ]
 *     }
 *   }
 */
func ActionDramaList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDramaListParam(c)
	if err != nil {
		return nil, err
	}
	return param.luckyBagData.ListAll(param.uc, param.Limit)
}

func newDramaListParam(c *handler.Context) (*dramaListParam, error) {
	var param dramaListParam
	err := c.BindJSON(&param)
	if err != nil || param.Limit < 0 {
		return nil, actionerrors.ErrParams
	}
	param.uc = c.UserContext()
	return &param, nil
}

// GetMockData mock 数据
func GetMockData() interface{} {
	return map[string]interface{}{
		"has_more": true,
		"data": []map[string]interface{}{
			{
				"ipr_id":    2333,
				"ipr_name":  "魔道祖师",
				"cover_url": "https://static-test.maoercdn.com/cover.png",
				"num":       5,
				"rooms": []map[string]interface{}{
					{
						"room_id":         100000,
						"name":            "直播间标题1",
						"creator_id":      11,
						"creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
					},
					{
						"room_id":         100001,
						"name":            "直播间标题2",
						"creator_id":      12,
						"creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
					},
					{
						"room_id":         100002,
						"name":            "直播间标题3",
						"creator_id":      13,
						"creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
					},
				},
			},
			{
				"drama_id":   2334,
				"drama_name": "烟火",
				"cover_url":  "https://static-test.maoercdn.com/cover.png",
				"num":        6,
				"rooms": []map[string]interface{}{
					{
						"room_id":         100000,
						"name":            "直播间标题1",
						"creator_id":      11,
						"creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
					},
					{
						"room_id":         100001,
						"name":            "直播间标题2",
						"creator_id":      12,
						"creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
					},
					{
						"room_id":         100002,
						"name":            "直播间标题3",
						"creator_id":      13,
						"creator_iconurl": "https://static-test.maoercdn.com/icon01.png",
					},
				},
			},
		},
	}
}
