package luckybag

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	luckybagutils "github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

type luckyBagInfoParam struct {
	IPRIDs   []int64 `json:"ipr_ids"`
	DramaIDs []int64 `json:"drama_ids"`

	resp luckyBagInfoResp
}

type luckyBagInfoResp struct {
	IPRLuckyBags   map[int64]*iprLuckyBags   `json:"ipr_lucky_bags,omitempty"`
	DramaLuckyBags map[int64]*dramaLuckyBags `json:"drama_lucky_bags,omitempty"`
}

type iprLuckyBags struct {
	IPRID   int64                     `json:"ipr_id"`
	IPRName string                    `json:"ipr_name"`
	Num     int64                     `json:"num"`
	Data    []*luckybagutils.RoomInfo `json:"data"`
}

type dramaLuckyBags struct {
	DramaID   int64                     `json:"drama_id"`
	DramaName string                    `json:"drama_name"`
	Num       int64                     `json:"num"`
	Data      []*luckybagutils.RoomInfo `json:"data"`
}

// ActionGetDramaLuckyBags 获取拥有对应广播剧福袋的房间列表
/**
 * @api {post} /rpc/luckybag/get-drama-luckybags 获取拥有对应广播剧福袋的房间列表
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} [ipr_ids] 剧集所属 IPR 的 IDs，搜索结果中包含多个 IPR 的剧集，需要传多个 IPR IDs
 * @apiParam {Number[]} [drama_ids] 剧集 IDs
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "ipr_lucky_bags": { // IPR 下有未结束的福袋的房间列表，传 IPR IDs 返回，IPR IDs 都不存在不返回该字段
 *         "2333": { // 该 field 为 ipr_id
 *           "ipr_id": 2333,
 *           "ipr_name": "IPR 名",
 *           "num": 5, // 当前正在发放此剧集福袋的直播间数量
 *           "data": [ // 排名前三的直播间信息
 *             {
 *               "room_id": 100000, // 直播间 ID
 *               "creator_id": 11, // 主播 ID
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png" // 主播头像
 *             },
 *             {
 *               "room_id": 100001,
 *               "creator_id": 12,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             },
 *             {
 *               "room_id": 100002,
 *               "creator_id": 13,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             }
 *           ]
 *         }
 *       },
 *       "drama_lucky_bags": { // 剧集下有未结束的福袋的房间列表，传剧集 IDs 返回，剧集 IDs 都不存在不返回该字段
 *         "2334": { // 该 field 为 drama_id
 *           "drama_id": 2334,
 *           "drama_name": "剧集名称",
 *           "num": 5,
 *           "data": [
 *             {
 *               "room_id": 100000,
 *               "name": "直播间标题",
 *               "creator_id": 11,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             },
 *             {
 *               "room_id": 100001,
 *               "name": "直播间标题",
 *               "creator_id": 12,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             },
 *             {
 *               "room_id": 100002,
 *               "name": "直播间标题",
 *               "creator_id": 13,
 *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
 *             }
 *           ]
 *         }
 *       }
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 */
func ActionGetDramaLuckyBags(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newLuckyBagInfoParam(c)
	if err != nil {
		return nil, err
	}

	err = param.iprLuckyBagsResp()
	if err != nil {
		return nil, err
	}

	err = param.dramaLuckyBags()
	if err != nil {
		return nil, err
	}

	return param.resp, nil
}

func newLuckyBagInfoParam(c *handler.Context) (*luckyBagInfoParam, error) {
	var param luckyBagInfoParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if len(param.IPRIDs) == 0 && len(param.DramaIDs) == 0 {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

// iprLuckyBagsResp 获取 IPR 下房间列表
func (param *luckyBagInfoParam) iprLuckyBagsResp() error {
	initiateMaps, err := luckybag.FindPendingInitiateMaps(luckybag.TypeDrama, param.IPRIDs, "prize_ipr_id", luckybagutils.LuckyBagRoomLimit)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if initiateMaps == nil {
		return nil
	}

	roomsMap, err := findRoomsMap(initiateMaps)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	param.resp.IPRLuckyBags = make(map[int64]*iprLuckyBags, len(initiateMaps))
	for _, iprID := range param.IPRIDs {
		initiates, ok := initiateMaps[iprID]
		if !ok {
			continue
		}

		var count int64
		roomList := roomsMap[iprID]
		if len(roomList) < luckybagutils.LuckyBagRoomLimit {
			// 一个房间只会有一条 pending 的 record
			count = int64(len(roomList))
		} else {
			count, err = luckybag.CountPendingInitiateRecordBy(initiates[0])
			if err != nil {
				logger.Errorf("failed to count: %v", err)
				// PASS
				count = int64(len(roomList))
			}
		}
		var iprName string
		if initiates[0].MoreInfo != nil && initiates[0].MoreInfo.PrizeIPRName != "" {
			iprName = initiates[0].MoreInfo.PrizeIPRName
		}
		param.resp.IPRLuckyBags[iprID] = &iprLuckyBags{
			IPRID:   iprID,
			IPRName: iprName,
			Num:     count,
			Data:    roomList,
		}
	}
	if len(param.resp.IPRLuckyBags) == 0 {
		param.resp.IPRLuckyBags = nil
	}

	return nil
}

// dramaLuckyBags 获取剧集下房间列表
func (param *luckyBagInfoParam) dramaLuckyBags() error {
	initiateMaps, err := luckybag.FindPendingInitiateMaps(luckybag.TypeDrama, param.DramaIDs, "prize_drama_id", luckybagutils.LuckyBagRoomLimit)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	if initiateMaps == nil {
		return nil
	}

	roomsMap, err := findRoomsMap(initiateMaps)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	param.resp.DramaLuckyBags = make(map[int64]*dramaLuckyBags, len(initiateMaps))
	for _, dramaID := range param.DramaIDs {
		initiates, ok := initiateMaps[dramaID]
		if !ok {
			continue
		}

		var count int64
		roomList := roomsMap[dramaID]
		if len(roomList) < luckybagutils.LuckyBagRoomLimit {
			// 一个房间只会有一条 pending 的 record
			count = int64(len(roomList))
		} else {
			count, err = luckybag.CountPendingInitiateRecordBy(initiates[0])
			if err != nil {
				logger.Errorf("failed to count: %v", err)
				// PASS
				count = int64(len(roomList))
			}
		}

		param.resp.DramaLuckyBags[dramaID] = &dramaLuckyBags{
			DramaID:   dramaID,
			DramaName: initiates[0].Name,
			Num:       count,
			Data:      roomList,
		}
	}
	if len(param.resp.DramaLuckyBags) == 0 {
		param.resp.DramaLuckyBags = nil
	}

	return nil
}

// findRoomsMap 返回 map[key][]*roomInfo
func findRoomsMap(initiateMaps map[int64][]*luckybag.InitiateRecord) (map[int64][]*luckybagutils.RoomInfo, error) {
	var allUserIDs []int64
	var allRoomIDs []int64

	for _, initiates := range initiateMaps {
		for _, initiate := range initiates {
			allUserIDs = append(allUserIDs, initiate.CreatorID)
			allRoomIDs = append(allRoomIDs, initiate.RoomID)
		}
	}

	allUserIDs = util.Uniq(allUserIDs)
	allRoomIDs = util.Uniq(allRoomIDs)

	userMap, err := mowangskuser.FindSimpleMap(allUserIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	roomsMap := make(map[int64][]*luckybagutils.RoomInfo, len(initiateMaps))
	for id, initiates := range initiateMaps {
		infos := make([]*luckybagutils.RoomInfo, len(initiates))
		for i, initiate := range initiates {
			info := &luckybagutils.RoomInfo{
				RoomID:    initiate.RoomID,
				CreatorID: initiate.CreatorID,
			}
			if u := userMap[initiate.CreatorID]; u != nil {
				info.CreatorIconURL = u.IconURL
			}
			infos[i] = info
		}
		roomsMap[id] = infos
	}

	return roomsMap, nil
}
