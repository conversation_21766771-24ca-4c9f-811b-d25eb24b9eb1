package luckybag

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionDramaList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	coverURL := "https://static-test.missevan.com/icon3.png"
	iconURL := "https://static-test.missevan.com/avatars/icon01.png"

	// mock
	cancel := mrpc.SetMock(userapi.URLRankList, func(input any) (any, error) {
		return userapi.RankListResp{
			RankElementType: userapi.RankElementTypeDrama,
			RankElements: []userapi.RankElementItem{
				{DramaID: 223344, IPRID: 2233}, // 223344 没有福袋
				{DramaID: 6767, IPRID: 2233},
				{DramaID: 6768, IPRID: 0},
				{DramaID: 6769, IPRID: 2234},
			},
			Bizdate: "2024-08-19",
		}, nil
	})
	defer cancel()

	api := "/rpc/luckybag/drama-list"
	// 测试有人气周榜数据取 2 条
	body := handler.M{
		"limit": 2,
	}
	c := handler.NewRPCTestContext(api, body)
	resp, err := ActionDramaList(c)
	require.NoError(err)
	r, ok := resp.(*utils.DramaLuckyBagListResp)
	require.True(ok)
	assert.True(*r.HasMore)
	except := []*utils.DramaLuckyBagInfo{
		{
			IPRID: 2233, IPRName: "IP 名称", CoverURL: coverURL, Num: 2,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000005, CreatorID: 3456864, CreatorIconURL: iconURL},
				{RoomID: 100000008, CreatorID: 3457111, CreatorIconURL: iconURL},
			},
		},
		{
			DramaID: 6768, DramaName: "广播剧福袋", CoverURL: coverURL, Num: 1,
			Rooms: []utils.RoomInfo{
				{RoomID: 100000006, CreatorID: 3457024, CreatorIconURL: iconURL},
			},
		},
	}
	assert.Equal(except, r.Data)
}

func TestNewDramaListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	api := "/rpc/luckybag/drama-list"
	body := handler.M{
		"limit": -1,
	}
	c := handler.NewRPCTestContext(api, body)
	_, err := newDramaListParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	body["limit"] = 0 // 查询全部
	c = handler.NewRPCTestContext(api, body)
	param, err := newDramaListParam(c)
	require.NoError(err)
	assert.Equal(body["limit"], param.Limit)

	body["limit"] = 2
	c = handler.NewRPCTestContext(api, body)
	param, err = newDramaListParam(c)
	require.NoError(err)
	assert.Equal(body["limit"], param.Limit)
}
