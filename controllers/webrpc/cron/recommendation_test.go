package cron

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/reportlivefanslog"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestBuildParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var p calcRecommendationParam
	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/live/recommendation", true, p)
	require.NoError(p.buildParam(c))
	assert.EqualValues(2500, *p.Start)
	assert.EqualValues(10000, *p.Stop)
	assert.EqualValues(30, *p.FansIncreaseNum)
}

func TestActionCalcRecommendation(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)
	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/live/recommendation", true,
		map[string]int64{
			"start": 0,
			"stop":  7000,
		})
	res, err := ActionCalcRecommendation(c)
	require.NoError(err)
	assert.NotNil(res)
}

func TestCalcRecommendation(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testUserID int64 = 123
	var testUserID2 int64 = 321
	var testUserID3 int64 = 1234
	c := &calcRecommendationParam{
		Start:           util.NewInt64(-1),
		Stop:            util.NewInt64(-10),
		FansIncreaseNum: util.NewInt64(0),
	}
	ids, err := c.calcRecommendation()
	require.NoError(err)
	assert.NotNil(ids)
	assert.Empty(ids)

	members, err := service.Redis.SMembers(keys.KeyCronRecommendLiveIDs0.Format()).Result()
	require.NoError(err)
	assert.Empty(members)

	// prepare data
	now := goutil.TimeNow()
	trans := transactionlog.TransactionLog{
		ID:          12345,
		ToID:        testUserID,
		ConfirmTime: now.Add(-time.Hour * 24).Unix(),
		Income:      80,
		Attr:        transactionlog.AttrCommon,
		Type:        transactionlog.TypeGuildLive,
		Status:      transactionlog.StatusSuccess,
		Rate:        0.3,
	}
	// update using primary key
	err = transactionlog.ADB().Assign(trans).FirstOrCreate(&trans).Error
	require.NoError(err)

	c = &calcRecommendationParam{
		Start:           util.NewInt64(0),
		Stop:            util.NewInt64(7000),
		FansIncreaseNum: util.NewInt64(10),
	}
	ids, err = c.calcRecommendation()
	require.NoError(err)
	assert.Equal([]int64{123}, ids)

	fansLogs := []reportlivefanslog.LiveFansLog{
		{
			ID:          12345,
			GmtCreate:   now,
			GmtModified: now,
			UserID:      testUserID2,
			BizDate:     util.BeginningOfDay(now.AddDate(0, 0, -1)),
			Follow:      11,
			Unfollow:    1,
		},
		{
			ID:          123456,
			GmtCreate:   now,
			GmtModified: now,
			UserID:      testUserID3,
			BizDate:     util.BeginningOfDay(now.AddDate(0, 0, -1)),
			Follow:      11,
			Unfollow:    2,
		},
	}
	// update using primary key
	require.NoError(fansLogs[0].DB().Assign(fansLogs[0]).FirstOrCreate(&fansLogs[0]).Error)
	ids, err = c.calcRecommendation()
	require.NoError(err)
	assert.NotEmpty(ids)

	require.NoError(fansLogs[1].DB().Assign(fansLogs[1]).FirstOrCreate(&fansLogs[1]).Error)
	ids2, err := c.calcRecommendation()
	require.NoError(err)
	assert.ElementsMatch(ids, ids2)

	members, err = service.Redis.SMembers(keys.KeyCronRecommendLiveIDs0.Format()).Result()
	require.NoError(err)

	// compare ids with members
	idStr := make([]string, len(ids))
	for i, v := range ids {
		idStr[i] = fmt.Sprintf("%d", v)
	}
	assert.ElementsMatch(idStr, members)

	assert.Contains(members, "123")
	assert.Contains(members, "321")
	assert.NotContains(members, "1234")
}
