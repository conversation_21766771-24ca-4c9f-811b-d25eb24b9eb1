package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronUserBlackCardExpire(t *testing.T) {
	require := require.New(t)

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(goutil.TimeNow())
	timeNow := nowStampFirstDayOfThisMonth.Add(-2 * time.Minute)
	goutil.SetTimeNow(func() time.Time {
		return timeNow
	})
	defer goutil.SetTimeNow(nil)

	testUserID1 := int64(1234)
	testUserID2 := int64(2234)
	require.NoError(liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id IN (?)", []int64{testUserID1, testUserID2}).Error)
	infos := []liveuserblackcard.LiveUserBlackCard{
		// 上上个月的黑卡开通记录
		{
			UserID:      testUserID1,
			BlackCardID: 4,
			StartTime:   nowStampFirstDayOfThisMonth.AddDate(0, -2, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		{
			UserID:      testUserID2,
			BlackCardID: 3,
			StartTime:   nowStampFirstDayOfThisMonth.AddDate(0, -2, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		// 上个月的黑卡开通记录
		{
			UserID:      testUserID1,
			BlackCardID: 3,
			StartTime:   nowStampFirstDayOfThisMonth.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 1, 0).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))

	// 测试本月 1 号过期
	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/user/black-card/expire", false, handler.M{})
	_, message, err := ActionCronUserBlackCardExpire(c)
	require.NoError(err)
	require.Equal("黑卡到期、降级处理成功，总共处理 2 个用户", message)

	// 测试下个月 1 号过期
	c = handler.NewTestContext(http.MethodPost, "/rpc/cron/user/black-card/expire", false, handler.M{
		"now_time": nowStampFirstDayOfThisMonth.AddDate(0, 1, 0).Unix(),
	})
	_, message, err = ActionCronUserBlackCardExpire(c)
	require.NoError(err)
	require.Equal("黑卡到期、降级处理成功，总共处理 1 个用户", message)
}

func TestNewBlackCardParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(goutil.TimeNow())
	timeNow := nowStampFirstDayOfThisMonth.AddDate(0, 0, 1)
	goutil.SetTimeNow(func() time.Time {
		return timeNow
	})
	defer goutil.SetTimeNow(nil)

	// 测试当前时间不是 1 号时
	api := "/rpc/cron/user/black-card/expire"
	c := handler.NewTestContext(http.MethodPost, api, false, handler.M{})
	param, err := newBlackCardParam(c)
	require.NoError(err)
	require.Nil(param)

	goutil.SetTimeNow(func() time.Time {
		return nowStampFirstDayOfThisMonth
	})
	defer goutil.SetTimeNow(nil)

	// 测试当前时间是 1 号时
	c = handler.NewTestContext(http.MethodPost, api, false, handler.M{})
	param, err = newBlackCardParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(nowStampFirstDayOfThisMonth.Add(2*time.Minute).Unix(), param.NowTime)
	assert.Equal(nowStampFirstDayOfThisMonth.Unix(), param.expireTime)
}

func TestBlackCardParam_getExpiredUserBlackCard(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	testUserID1 := int64(1234)
	testUserID2 := int64(2234)
	require.NoError(liveuserblackcard.LiveUserBlackCard{}.DB().Delete("", "user_id IN (?)", []int64{testUserID1, testUserID2}).Error)
	nowStampFirstDayOfThisMonth := goutil.BeginningOfMonth(goutil.TimeNow())
	infos := []liveuserblackcard.LiveUserBlackCard{
		// 上上个月的黑卡开通记录
		{
			UserID:      testUserID1,
			BlackCardID: 4,
			StartTime:   nowStampFirstDayOfThisMonth.AddDate(0, -2, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		{
			UserID:      testUserID2,
			BlackCardID: 3,
			StartTime:   nowStampFirstDayOfThisMonth.AddDate(0, -2, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.Unix(),
		},
		// 上个月的黑卡开通记录
		{
			UserID:      testUserID1,
			BlackCardID: 3,
			StartTime:   nowStampFirstDayOfThisMonth.AddDate(0, -1, 0).Unix(),
			ExpireTime:  nowStampFirstDayOfThisMonth.AddDate(0, 1, 0).Unix(),
		},
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, infos[0].TableName(), infos))

	param := &blackCardParam{
		expireTime: nowStampFirstDayOfThisMonth.Unix(),
	}

	require.NoError(param.getExpiredUserBlackCard())
	require.NotNil(param.userIDBlackCardMap)
	require.Len(param.userIDBlackCardMap, 2)
	assert.NotNil(param.userIDBlackCardMap[testUserID1])
	assert.Equal(3, param.userIDBlackCardMap[testUserID1].Level)
	assert.Nil(param.userIDBlackCardMap[testUserID2])
}
