package cron

import (
	"fmt"
	"html"
	"strings"
	"testing"
	"time"

	"github.com/jinzhu/gorm"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbalance"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	testGuildOwner int64 = 3000000
	testLiveID1    int64 = 3000001
	testLiveID2    int64 = 3000002
	testLiveID3    int64 = 3000003
	testLiveID4    int64 = 3000004
)

const expelGuildID = 11

var (
	testGuildAutoRenewalCancelTime = time.Date(2021, 7, 1, 0, 0, 0, 0, time.Local)
	testGuildID                    int64
)

func TestActionCronSendRenewApplyment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanTestDataFunc, err := createRenewApplymentTestData()
	assert.NoError(err)
	if cleanTestDataFunc != nil {
		defer cleanTestDataFunc()
	}
	var systemMsgs []pushservice.SystemMsg
	// mock pushservice
	cancelSystemMsgMock := mrpc.SetMock(pushservice.Scheme+"://api/systemmsg", func(i interface{}) (interface{}, error) {
		body, ok := i.(map[string]interface{})
		require.True(ok)
		msgs, ok := body["systemmsgs"]
		require.True(ok)
		pushserviceMsgs, ok := msgs.([]pushservice.SystemMsg)
		require.True(ok)
		systemMsgs = append(systemMsgs, pushserviceMsgs...)
		return "success", nil
	})
	defer cancelSystemMsgMock()

	cancelUserInfoMock := mrpc.SetMock("sso://sso/list", func(i interface{}) (interface{}, error) {
		return handler.M{"data": []sso.Account{
			{ID: testLiveID1, Mobile: "***********"},
			{ID: testLiveID2, Mobile: "***********"},
		}}, nil
	})
	defer cancelUserInfoMock()

	testShortURL := "https://123.cc"
	cleanup := mrpc.SetMock(userapi.URLGoShortURL, func(input interface{}) (output interface{}, err error) {
		return handler.M{"short_url": testShortURL}, nil
	})
	defer cleanup()

	cancelSmsMock := mrpc.SetMock(pushservice.Scheme+"://api/sms", func(i interface{}) (interface{}, error) {
		body, ok := i.(map[string]interface{})
		require.True(ok)
		msgs, ok := body["smses"]
		require.True(ok)
		smsMsgs, ok := msgs.([]pushservice.SMS)
		require.True(ok)
		require.Len(smsMsgs, 2)
		for i := range smsMsgs {
			assert.True(strings.Contains(smsMsgs[i].Payload["link"].(string), testShortURL))
		}
		return "success", nil
	})
	defer cancelSmsMock()

	config.Conf.AB["enable_send_new_guild_sign_system_msg"] = true
	defer func() {
		delete(config.Conf.AB, "enable_send_new_guild_sign_system_msg")
	}()

	c := handler.NewRPCTestContext("/rpc/cron/guild/renew/send", nil)
	resp, err := ActionCronSendRenewApplyment(c)
	assert.Equal("success", resp)
	require.NoError(err)
	defer func() {
		service.DB.Table(contractapplyment.TableName()).
			Delete("", "live_id IN (?) AND type = ?",
				[]int64{testLiveID1, testLiveID2}, contractapplyment.TypeGuildRenew)
	}()

	var renewApplymentCount int
	err = service.DB.Table(contractapplyment.TableName()).
		Where("status = ? AND type = ? AND contract_duration = ?", contractapplyment.StatusPending,
			contractapplyment.TypeGuildRenew, contractapplyment.ContractDurationFiveYear).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).
		Count(&renewApplymentCount).Error
	assert.NoError(err)
	assert.Equal(2, renewApplymentCount)

	assert.Len(systemMsgs, 4)
	for _, msg := range systemMsgs {
		if msg.UserID != testGuildOwner {
			assert.True(strings.HasSuffix(msg.Content, "前往处理</a>"))
			continue
		}
		assert.True(strings.HasSuffix(msg.Content, fmt.Sprintf("平台已代您向主播发送时限为 5 年的续约邀请，可在 %s 查看",
			html.EscapeString("公会工作台 > 签约管理 > 续约申请"))))
	}
}

func createRenewApplymentTestData() (cleanTestData func(), err error) {
	now := goutil.TimeNow()
	// 创建公会
	testGuild := guild.Guild{
		Name:                      "测试平台替公会代发续约申请定时任务",
		Intro:                     "xxx",
		OwnerName:                 "test",
		OwnerIDNumber:             "1234",
		OwnerIDPeople:             "oss://test.jpg",
		OwnerBackcover:            "oss://test.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		CorporationName:           "test",
		CorporationAddress:        "test",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "***********",
		BusinessLicenseFrontcover: "oss://test.jpg",
		TaxAccount:                "***********",
		BankAccount:               "***********",
		BankAccountName:           "test",
		Bank:                      "test",
		BankAddress:               "test",
		BankBranch:                "test",
		Checked:                   guild.CheckedPass,
		UserID:                    testGuildOwner,
		ApplyTime:                 now.AddDate(0, 0, -10).Unix(),
	}
	err = service.DB.Table(guild.TableName()).Create(&testGuild).Error
	if err != nil {
		return nil, err
	}

	// 创建合同
	contractList := make([]livecontract.LiveContract, 2)
	contractList[0] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID1,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -6, 0).Unix(),
		ContractEnd:      now.AddDate(0, 0, 7).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -6, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -6, 0).Unix(),
	}
	contractList[1] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID2,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -10, 0).Unix(),
		ContractEnd:      now.AddDate(0, 0, 7).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -10, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -10, 0).Unix(),
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		return helper.BatchInsert(tx, livecontract.TableName(), contractList)
	})

	if err != nil {
		return func() {
			service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		}, err
	}

	return func() {
		service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		service.DB.Table(livecontract.TableName()).Delete("", "guild_id = ?", testGuild.ID)
	}, nil
}

func createExpiredRenewApplymentTestData() (cleanTestData func(), err error) {
	now := goutil.TimeNow()
	// 创建公会
	testGuild := guild.Guild{
		Name:                      "测试续约申请未处理超时自动续约公会",
		Intro:                     "xxx",
		OwnerName:                 "test",
		OwnerIDNumber:             "1234",
		OwnerIDPeople:             "oss://test.jpg",
		OwnerBackcover:            "oss://test.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		CorporationName:           "test",
		CorporationAddress:        "test",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "***********",
		BusinessLicenseFrontcover: "oss://test.jpg",
		TaxAccount:                "***********",
		BankAccount:               "***********",
		BankAccountName:           "test",
		Bank:                      "test",
		BankAddress:               "test",
		BankBranch:                "test",
		Checked:                   guild.CheckedPass,
		UserID:                    testGuildOwner,
		ApplyTime:                 now.AddDate(0, 0, -10).Unix(),
	}
	err = service.DB.Table(guild.TableName()).Save(&testGuild).Error
	if err != nil {
		return nil, err
	}

	// 创建合同
	contractList := make([]livecontract.LiveContract, 2)
	contractList[0] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID1,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -6, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		Attr:             1,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}
	contractList[1] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID2,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -10, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}

	// 创建超时未处理的续约申请
	expelApplymentList := make([]contractapplyment.ContractApplyment, 2)
	expelApplymentList[0] = contractapplyment.ContractApplyment{
		LiveID:             testLiveID1,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         0,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeGuildRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -1).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               0,
		CreateTime:         now.AddDate(0, 0, -4).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -4).Unix(),
	}
	expelApplymentList[1] = contractapplyment.ContractApplyment{
		LiveID:             testLiveID2,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         0,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeLiveRenew,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -1).Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               0,
		CreateTime:         now.AddDate(0, 0, -4).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -4).Unix(),
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if err := helper.BatchInsert(tx, livecontract.TableName(), contractList); err != nil {
			return err
		}
		if err := helper.BatchInsert(tx, contractapplyment.TableName(), expelApplymentList); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return func() {
			service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		}, err
	}

	return func() {
		service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		service.DB.Table(livecontract.TableName()).Delete("", "guild_id = ?", testGuild.ID)
		service.DB.Table(contractapplyment.TableName()).Delete("", "guild_id = ?", testGuild.ID)
	}, nil
}

func TestActionCronRenewExpiredApplyment(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return testGuildAutoRenewalCancelTime
	})
	defer goutil.SetTimeNow(nil)
	c := handler.NewRPCTestContext("/rpc/cron/guild/renew/exec", nil)
	resp, err := ActionCronRenewExpiredApplyment(c)
	require.NoError(err)
	assert.Equal("已取消自动续约功能", resp)

	goutil.SetTimeNow(func() time.Time {
		return testGuildAutoRenewalCancelTime.Add(-time.Hour)
	})

	cleanTestDataFunc, err := createExpiredRenewApplymentTestData()
	assert.NoError(err)
	if cleanTestDataFunc != nil {
		defer cleanTestDataFunc()
	}

	messageCount := 0
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		messageCount += len(systemMsgList)

		return "success", nil
	})
	defer cancel()

	c = handler.NewRPCTestContext("/rpc/cron/guild/renew/exec", nil)
	resp, err = ActionCronRenewExpiredApplyment(c)
	require.NoError(err)
	assert.Equal("success", resp)
	assert.Equal(2, messageCount)

	var renewApplymentCount int
	err = service.DB.Table(contractapplyment.TableName()).
		Where("status = ?", contractapplyment.StatusAgreed).
		Where("type IN (?)", []int64{contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew}).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).
		Count(&renewApplymentCount).Error
	assert.NoError(err)
	assert.Equal(2, renewApplymentCount)

	var contractCount int
	err = service.DB.Table(livecontract.TableName()).
		Where("status = ?", livecontract.StatusContracting).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).
		Count(&contractCount).Error
	assert.NoError(err)
	assert.Equal(2, contractCount)

	// 验证续约后重置主播在合约期内是否申请过协商解约的状态
	contract1 := new(livecontract.LiveContract)
	require.NoError(service.DB.Table(livecontract.TableName()).
		Where("live_id = ? AND status = ?", testLiveID1, livecontract.StatusContracting).First(&contract1).Error)
	assert.False(contract1.Attr.IsSet(livecontract.AttrBitMaskLiveTerminated))
}

func createExpelTestData() (cleanTestData func(), err error) {
	now := goutil.TimeNow()
	// 创建公会
	testGuild := guild.Guild{
		ID:                        expelGuildID,
		Name:                      "测试执行清退定时任务公会",
		Intro:                     "xxx",
		OwnerName:                 "test",
		OwnerIDNumber:             "1234",
		OwnerIDPeople:             "oss://test.jpg",
		OwnerBackcover:            "oss://test.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		CorporationName:           "test",
		CorporationAddress:        "test",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "***********",
		BusinessLicenseFrontcover: "oss://test.jpg",
		TaxAccount:                "***********",
		BankAccount:               "***********",
		BankAccountName:           "test",
		Bank:                      "test",
		BankAddress:               "test",
		BankBranch:                "test",
		Checked:                   guild.CheckedPass,
		UserID:                    testGuildOwner,
		ApplyTime:                 now.AddDate(0, 0, -10).Unix(),
	}
	err = service.DB.Assign(testGuild).FirstOrCreate(&testGuild).Error
	if err != nil {
		return
	}
	// 创建合同
	contractList := make([]livecontract.LiveContract, 2)
	contractList[0] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID1,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -6, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}
	contractList[1] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID2,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -10, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}

	// 创建清退申请
	expelApplymentList := make([]contractapplyment.ContractApplyment, 2)
	expelApplymentList[0] = contractapplyment.ContractApplyment{
		LiveID:             testLiveID1,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         0,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeGuildExpel,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -1).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               0,
		CreateTime:         now.AddDate(0, 0, -4).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -4).Unix(),
	}
	expelApplymentList[1] = contractapplyment.ContractApplyment{
		LiveID:             testLiveID2,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         0,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeGuildExpel,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -1).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               0,
		CreateTime:         now.AddDate(0, 0, -4).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -4).Unix(),
	}

	// 创建初未处理的降薪申请
	application := contractapplyment.ContractApplyment{
		LiveID:    testLiveID2,
		GuildID:   testGuild.ID,
		GuildName: testGuild.Name,
		Type:      contractapplyment.TypeRateDown,
		Status:    contractapplyment.StatusPending,
		Initiator: contractapplyment.InitiatorLive,
		Rate:      40,
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if err := helper.BatchInsert(tx, livecontract.TableName(), contractList); err != nil {
			return err
		}
		if err := helper.BatchInsert(tx, contractapplyment.TableName(), expelApplymentList); err != nil {
			return err
		}
		if err = tx.Create(&application).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return func() {
			service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		}, err
	}

	return func() {
		service.DB.Table(livecontract.TableName()).Delete("", "guild_id = ?", expelGuildID)
		service.DB.Table(contractapplyment.TableName()).Delete("", "guild_id = ?", expelGuildID)
	}, nil
}

func TestActionCronExpelLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanTestDataFunc, err := createExpelTestData()
	assert.NoError(err)
	if cleanTestDataFunc != nil {
		defer cleanTestDataFunc()
	}

	messageCount := 0
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		messageCount += len(systemMsgList)

		return "success", nil
	})
	defer cancel()

	c := handler.NewRPCTestContext("/rpc/cron/guild/terminate/exec", nil)
	resp, err := ActionCronExpelLive(c)
	assert.Equal("success", resp)
	require.NoError(err)
	assert.Equal(4, messageCount)

	var expelApplymentCount int
	err = service.DB.Table(contractapplyment.TableName()).
		Where("type = ? AND status = ?", contractapplyment.TypeGuildExpel, contractapplyment.StatusAgreed).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).Count(&expelApplymentCount).Error
	require.NoError(err)
	assert.Equal(2, expelApplymentCount)

	application := new(contractapplyment.ContractApplyment)
	require.NoError(application.DB().Where("guild_id = ? AND live_id = ? AND type = ?",
		expelGuildID, testLiveID2, contractapplyment.TypeRateDown).First(application).Error)
	assert.Equal(contractapplyment.StatusInvalid, application.Status)

	var uselessContractCount int
	err = service.DB.Table(livecontract.TableName()).
		Where("status = ?", livecontract.StatusUseless).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).Count(&uselessContractCount).Error
	require.NoError(err)
	assert.Equal(2, uselessContractCount)
}

func createExpiredContractTestData() (cleanTestData func(), err error) {
	now := goutil.TimeNow()
	// 创建公会
	testGuild := guild.Guild{
		Name:                      "测试执行合约到期失效定时任务公会",
		Intro:                     "xxx",
		OwnerName:                 "test",
		OwnerIDNumber:             "1234",
		OwnerIDPeople:             "oss://test.jpg",
		OwnerBackcover:            "oss://test.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		CorporationName:           "test",
		CorporationAddress:        "test",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "***********",
		BusinessLicenseFrontcover: "oss://test.jpg",
		TaxAccount:                "***********",
		BankAccount:               "***********",
		BankAccountName:           "test",
		Bank:                      "test",
		BankAddress:               "test",
		BankBranch:                "test",
		Checked:                   guild.CheckedPass,
		UserID:                    testGuildOwner,
		ApplyTime:                 now.AddDate(0, 0, -10).Unix(),
		LiveNum:                   2,
	}
	err = service.DB.Table(guild.TableName()).Create(&testGuild).Error
	if err != nil {
		return nil, err
	}
	guildBalance := guildbalance.GuildBalance{
		ID: testGuild.ID,
	}
	err = guildbalance.GuildBalance{}.DB().Create(&guildBalance).Error
	if err != nil {
		return func() {
			service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		}, err
	}

	// 创建合同
	contractList := make([]livecontract.LiveContract, 2)
	contractList[0] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID3,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -6, 0).Unix(),
		ContractEnd:      now.AddDate(0, 0, -1).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}
	contractList[1] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID4,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -10, 0).Unix(),
		ContractEnd:      now.AddDate(0, 0, -1).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		return helper.BatchInsert(tx, livecontract.TableName(), contractList)
	})

	if err != nil {
		return func() {
			service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
			guildbalance.GuildBalance{}.DB().Delete("", "id = ?", testGuild.ID)
		}, err
	}

	testGuildID = testGuild.ID
	return func() {
		service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		guildbalance.GuildBalance{}.DB().Delete("", "id = ?", testGuild.ID)
		service.DB.Table(livecontract.TableName()).Delete("", "guild_id = ?", testGuild.ID)
	}, nil
}

func TestActionCronInvalidExpiredContract(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return testGuildAutoRenewalCancelTime
	})
	defer goutil.SetTimeNow(nil)
	cleanTestDataFunc, err := createExpiredContractTestData()
	assert.NoError(err)
	if cleanTestDataFunc != nil {
		defer cleanTestDataFunc()
	}

	messageCount := 0
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		messageCount += len(systemMsgList)

		return "success", nil
	})
	defer cancel()

	rr := &room.Room{
		Helper: room.Helper{
			RoomID:    2101100111112,
			CreatorID: testLiveID3,
			GuildID:   123456,
			Name:      "测试直播间",
			NameClean: fmt.Sprintf("测试直播间 %d", 2101100111112),
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = room.Collection().DeleteOne(ctx, bson.M{"creator_id": testLiveID3})
	require.NoError(err)
	_, err = room.Collection().InsertOne(ctx, rr)
	require.NoError(err)

	c := handler.NewRPCTestContext("/rpc/cron/guild/applyment/expire", nil)
	resp, err := ActionCronInvalidExpiredContract(c)
	require.NoError(err)
	assert.Equal("success", resp)
	assert.Equal(4, messageCount)

	var finishedContractCount int
	err = service.DB.Table(livecontract.TableName()).
		Where("status = ?", livecontract.StatusFinished).
		Where("live_id IN (?)", []int64{testLiveID3, testLiveID4}).Count(&finishedContractCount).Error
	require.NoError(err)
	assert.Equal(2, finishedContractCount)

	var liveNum int
	err = service.DB.Table(guild.TableName()).
		Select("live_num").
		Where("id = ?", testGuildID).Row().Scan(&liveNum)
	require.NoError(err)
	assert.Equal(0, liveNum)

	// 测试 room.guild_id 已经被置为 0
	r, err := room.FindOne(bson.M{"creator_id": testLiveID3})
	require.NoError(err)
	assert.Zero(r.GuildID)
}

func TestGuildAutoRenewalCanceled(t *testing.T) {
	assert := assert.New(t)

	config.Conf.AB["cancel_guild_auto_renewal_time"] = 2
	assert.True(guildAutoRenewalCanceled(2), "到达配置的时间")
	assert.False(guildAutoRenewalCanceled(1), "未到达配置时间")
	delete(config.Conf.AB, "cancel_guild_auto_renewal_time")

	assert.True(guildAutoRenewalCanceled(testGuildAutoRenewalCancelTime.Unix()), "未配置开始时间")
	assert.False(guildAutoRenewalCanceled(testGuildAutoRenewalCancelTime.Add(-time.Second).Unix()), "未配置开始时间")
}

func TestCronExpelEditRateApplication(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanTestDataFunc, err := createExpelGuildRateTestData()
	require.NoError(err)
	defer cleanTestDataFunc()
	cronExpelEditRateApplication(handler.CreateTestContext(false))

	var editRateCount int
	require.NoError(contractapplyment.ContractApplyment{}.DB().
		Where("status = ? AND type = ?", contractapplyment.StatusOutdated, contractapplyment.TypeRateDown).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).
		Count(&editRateCount).Error)
	assert.Equal(2, editRateCount)

	var contracts []livecontract.LiveContract
	require.NoError(livecontract.LiveContract{}.DB().
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).
		Find(&contracts).Error)
	require.Len(contracts, 2)
	assert.False(contracts[0].Attr.IsSet(livecontract.AttrBitMaskLiveGuildRate))
	assert.False(contracts[1].Attr.IsSet(livecontract.AttrBitMaskLiveGuildRate))
}

func createExpelGuildRateTestData() (cleanTestData func(), err error) {
	now := goutil.TimeNow()
	// 创建公会
	testGuild := guild.Guild{
		Name:                      "测试续约申请未处理超时自动续约公会",
		Intro:                     "xxx",
		OwnerName:                 "test",
		OwnerIDNumber:             "1234",
		OwnerIDPeople:             "oss://test.jpg",
		OwnerBackcover:            "oss://test.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		CorporationName:           "test",
		CorporationAddress:        "test",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "***********",
		BusinessLicenseFrontcover: "oss://test.jpg",
		TaxAccount:                "***********",
		BankAccount:               "***********",
		BankAccountName:           "test",
		Bank:                      "test",
		BankAddress:               "test",
		BankBranch:                "test",
		Checked:                   guild.CheckedPass,
		UserID:                    testGuildOwner,
		ApplyTime:                 now.AddDate(0, 0, -10).Unix(),
	}
	err = guild.Guild{}.DB().Save(&testGuild).Error
	if err != nil {
		return nil, err
	}

	// 创建用户
	expelSimpleUser := make([]mowangskuser.Simple, 2)
	expelSimpleUser[0] = mowangskuser.Simple{
		ID:       testLiveID1,
		Username: "剑来",
	}
	expelSimpleUser[1] = mowangskuser.Simple{
		ID:       testLiveID2,
		Username: "来剑",
	}

	// 创建合同
	contractList := make([]livecontract.LiveContract, 2)
	contractList[0] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID1,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -6, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             45,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		Attr:             1,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}
	contractList[0].Attr.Set(livecontract.AttrBitMaskLiveGuildRate)
	err = livecontract.LiveContract{}.DB().Create(&contractList[0]).Error
	if err != nil {
		service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		return nil, err
	}
	contractList[1] = livecontract.LiveContract{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID2,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -10, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             56,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}
	contractList[1].Attr.Set(livecontract.AttrBitMaskLiveGuildRate)
	err = livecontract.LiveContract{}.DB().Create(&contractList[1]).Error
	if err != nil {
		service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		livecontract.LiveContract{}.DB().Delete("", "id = ?", contractList[0].ID)
		return nil, err
	}

	// 创建超时未处理的申请
	expelApplicationList := make([]contractapplyment.ContractApplyment, 2)
	expelApplicationList[0] = contractapplyment.ContractApplyment{
		LiveID:             testLiveID1,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         contractList[0].ID,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeRateDown,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -contractapplyment.ExpireDaysRateDown).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               40,
		More:               "{\"rate\":45}",
		CreateTime:         now.AddDate(0, 0, -7).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -7).Unix(),
	}
	expelApplicationList[1] = contractapplyment.ContractApplyment{
		LiveID:             testLiveID2,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         contractList[1].ID,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypeRateDown,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -contractapplyment.ExpireDaysRateDown).Unix(),
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               41,
		More:               "{\"rate\":56}",
		CreateTime:         now.AddDate(0, 0, -7).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -7).Unix(),
	}

	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if err := helper.BatchInsert(tx, contractapplyment.TableName(), expelApplicationList); err != nil {
			return err
		}
		if err := helper.BatchInsert(tx, mowangskuser.TableName(), expelSimpleUser); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		service.DB.Table(guild.TableName()).Delete("", "id = ?", testGuild.ID)
		livecontract.LiveContract{}.DB().Delete("", "guild_id = ?", testGuild.ID)
		return nil, err
	}

	return func() {
		guild.Guild{}.DB().Delete("", "id = ?", testGuild.ID)
		livecontract.LiveContract{}.DB().Delete("", "guild_id = ?", testGuild.ID)
		contractapplyment.ContractApplyment{}.DB().Delete("", "guild_id = ?", testGuild.ID)
		mowangskuser.Simple{}.DB().Delete("", "id IN (?)", []int64{testLiveID1, testLiveID2})
	}, nil
}

func TestCronExpiredExclusiveCreator(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancelUserInfoMock := mrpc.SetMock("sso://sso/list", func(i interface{}) (interface{}, error) {
		return handler.M{"data": []sso.Account{
			{ID: 2, Mobile: "***********"},
			{ID: 12, Mobile: "***********"},
		}}, nil
	})
	defer cancelUserInfoMock()

	now := goutil.TimeNow()
	tm := goutil.BeginningOfDay(now)

	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete(nil, "id = ?", 11).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              11,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       12,
		GuildContractID: ********,
		ContractEnd:     tm.AddDate(0, 0, -1).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	cronExpiredExclusiveCreator()

	// 验证三方独家主播身份是否已失效
	var tec1 exclusivecreator.TripartiteExclusiveCreator
	require.NoError(tec1.DB().First(&tec1, tec.ID).Error)
	assert.Equal(exclusivecreator.StatusExpired, tec1.Status)
}

func TestCronExclusiveCreatorSendMsg(t *testing.T) {
	require := require.New(t)

	cancelUserInfoMock := mrpc.SetMock("sso://sso/list", func(i interface{}) (interface{}, error) {
		return handler.M{"data": []sso.Account{
			{ID: 2, Mobile: "***********"},
			{ID: 12, Mobile: "***********"},
		}}, nil
	})
	defer cancelUserInfoMock()

	now := goutil.TimeNow()
	tm := goutil.BeginningOfDay(now)

	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete(nil, "id IN (?)", []int64{20, 21, 22}).Error)
	tecs := make([]*exclusivecreator.TripartiteExclusiveCreator, 3)
	// 今日修改的三方独家主播
	tecs[0] = &exclusivecreator.TripartiteExclusiveCreator{
		ID:              20,
		CreateTime:      tm.AddDate(0, -1, 0).Unix(),
		ModifiedTime:    tm.AddDate(0, 0, -1).Add(time.Minute).Unix(),
		GuildID:         3,
		CreatorID:       12,
		GuildContractID: ********,
		ContractEnd:     tm.AddDate(0, 4, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}

	// 今日移除的三方独家主播
	tecs[1] = &exclusivecreator.TripartiteExclusiveCreator{
		ID:              21,
		CreateTime:      tm.AddDate(0, -1, 0).Unix(),
		ModifiedTime:    tm.AddDate(0, 0, -1).Add(time.Minute).Unix(),
		GuildID:         3,
		CreatorID:       12,
		GuildContractID: ********,
		ContractEnd:     tm.AddDate(2, 0, 0).Unix(),
		Status:          exclusivecreator.StatusDeleted,
	}

	// 今日新增的三方独家主播
	tecs[2] = &exclusivecreator.TripartiteExclusiveCreator{
		ID:              22,
		CreateTime:      tm.AddDate(0, 0, -1).Add(time.Minute).Unix(),
		ModifiedTime:    tm.AddDate(0, 0, -1).Add(time.Minute).Unix(),
		GuildID:         3,
		CreatorID:       12,
		GuildContractID: ********,
		ContractEnd:     tm.AddDate(2, 0, 0).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(servicedb.BatchInsert(service.LiveDB, exclusivecreator.TableName(), tecs))

	// 生成公会经纪人与主播关系
	require.NoError(guildagent.AgentCreator{}.DB().Delete("", "id = ?", 1000).Error)
	gac := guildagent.AgentCreator{
		ID:           1000,
		CreateTime:   now.Unix(),
		ModifiedTime: now.Unix(),
		DeleteTime:   0,
		GuildID:      3,
		AgentID:      2,
		CreatorID:    12,
	}
	require.NoError(gac.DB().Save(&gac).Error)

	cronExclusiveCreatorSendMsg()
}

func TestFindExclusiveInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancelUserInfoMock := mrpc.SetMock("sso://sso/list", func(i interface{}) (interface{}, error) {
		return handler.M{"data": []sso.Account{
			{ID: 2, Mobile: "***********"},
			{ID: 12, Mobile: "***********"},
		}}, nil
	})
	defer cancelUserInfoMock()

	now := goutil.TimeNow()
	tm := goutil.BeginningOfDay(now)
	beginningOfDay := tm.Unix()
	beginningOfYesterday := goutil.BeginningOfDay(tm.AddDate(0, 0, -1)).Unix()

	require.NoError(exclusivecreator.TripartiteExclusiveCreator{}.DB().Delete("", "id = ?", 25).Error)
	tec := exclusivecreator.TripartiteExclusiveCreator{
		ID:              25,
		CreateTime:      now.Unix(),
		ModifiedTime:    now.Unix(),
		GuildID:         3,
		CreatorID:       12,
		GuildContractID: ********,
		ContractEnd:     tm.AddDate(0, 0, -1).Unix(),
		Status:          exclusivecreator.StatusValid,
	}
	require.NoError(tec.DB().Save(&tec).Error)

	// 测试查询三方独家身份到期所需信息
	epi, err := findExclusiveInfo(cronExpiredExclusiveType, beginningOfDay, beginningOfYesterday)
	require.NoError(err)
	assert.Len(epi.exclusiveList, 1)
	assert.Len(epi.guildMap, 1)
	assert.Len(epi.userMap, 2)
	assert.Len(epi.agentMap, 1)

	// 测试查询给新增、修改或移除的三方独家主播发短信所需信息
	epi, err = findExclusiveInfo(cronExclusiveSendMsgType, beginningOfDay, beginningOfYesterday)
	require.NoError(err)
	assert.Len(epi.exclusiveList, 3)
	assert.Len(epi.userMap, 2)
	assert.Len(epi.guildMap, 1)
	assert.Len(epi.agentMap, 1)
}

func TestRegionMobile(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testMobile := "13266956558"
	// 测试不支持的 region
	str, err := regionMobile("01", testMobile)
	require.NoError(err)
	assert.Equal("", str)

	// 测试支持的 region
	str, err = regionMobile(vcode.CNRegionNumber, testMobile)
	require.NoError(err)
	assert.Equal("8613266956558", str)

	// 测试手机号为空
	str, err = regionMobile(vcode.CNRegionNumber, "")
	require.NoError(err)
	assert.Equal("", str)

	// 测试手机号格式错误
	str, err = regionMobile(vcode.CNRegionNumber, "42733")
	assert.Error(err, "手机号错误")
	assert.Equal("", str)
}
