package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronGiftWallPeriodExtend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	mockTime := time.Date(2100, 1, 1, 0, 0, 0, 0, time.Local)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := giftwall.PeriodCollection().DeleteMany(ctx, bson.M{
		"start_time": mockTime.Unix(),
	})
	require.NoError(err)

	goutil.SetTimeNow(func() time.Time {
		return mockTime.Add(-30 * time.Minute)
	})
	defer goutil.SetTimeNow(nil)
	key := keys.KeyCurrentGiftWallPeriod1.Format(mockTime.Add(-30 * time.Minute).Format(util.TimeFormatYMDWithNoSpace))
	p := &giftwall.Period{
		StartTime:   mockTime.AddDate(0, 0, -14).Unix(),
		EndTime:     mockTime.Unix(),
		ShowGiftIDs: []int64{1, 2, 3},
		Rewards:     []*giftwall.RewardInfo{{Threshold: 1, Type: giftwall.RewardTypeCustomGift, ElementID: 1000}},
	}
	service.Cache5Min.Set(key, p, 0)
	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	resp, err := ActionCronGiftWallPeriodExtend(c)
	require.NoError(err)
	assert.Equal("创建周期成功", resp)
}
