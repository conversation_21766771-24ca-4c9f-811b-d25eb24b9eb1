package cron

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
)

type hotScoreParam struct {
	RoomIDs []int64 `json:"room_ids"`
	NowTime int64   `json:"now_time"`
}

type goodsHotScore struct {
	CreatorID int64   `gorm:"column:seller_id" json:"-"`
	RoomID    int64   `gorm:"-" json:"room_id"`
	Score     float64 `gorm:"column:score" json:"score"`
}

type hotScoreGoodsResp struct {
	Data []goodsHotScore `json:"data"`
}

// ActionHotScoreGoods 某时间点某些房间超粉和付费弹幕的热度
/**
 * @api {post} /rpc/cron/hotscore/goods 某时间点某些房间超粉和付费弹幕的热度
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} room_ids 房间号数组
 * @apiParam {Number} now_time 计算时间，单位：秒
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": [
 *           {
 *             "room_id": 123,
 *             "score": 1234.5
 *           },
 *           {
 *             "room_id": 1234,
 *             "score": 55.145
 *           }
 *         ]
 *       }
 *     }
 */
func ActionHotScoreGoods(c *handler.Context) (handler.ActionResponse, error) {
	var param hotScoreParam
	err := c.BindJSON(&param)
	if err != nil ||
		param.NowTime <= 0 {
		return nil, actionerrors.ErrParams
	}
	resp := &hotScoreGoodsResp{Data: make([]goodsHotScore, 0)}
	if len(param.RoomIDs) == 0 {
		// 可能有无人开播的情况
		return resp, nil
	}
	rooms, err := room.ListSimples(bson.M{"room_id": bson.M{"$in": param.RoomIDs}},
		options.Find().SetProjection(mongodb.NewProjection("room_id, creator_id")))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	creatorIDs := make([]int64, len(rooms))
	roomMap := make(map[int64]*room.Simple, len(rooms))
	for i := range rooms {
		creatorIDs[i] = rooms[i].CreatorID
		roomMap[rooms[i].CreatorID] = rooms[i]
	}
	attenuation := fmt.Sprintf(`((%d-create_time)/60*(CASE
  WHEN price < 100 THEN 0.1
  WHEN price >= 100 AND price < 1000 THEN 0.06
  WHEN price >= 1000 AND price < 5000 THEN 0.05
  WHEN price >= 5000 AND price < 10000 THEN 0.04
  ELSE 0.03
END))`, param.NowTime)
	selectScore := "SUM(price*(1-" + attenuation + ")) AS score"
	scoreWhere := attenuation + "< 1.0"
	db := livetxnorder.ADB().Select("seller_id, "+selectScore).
		Where("status = ?", livetxnorder.StatusSuccess).
		Where("goods_type IN (?)", []int{livegoods.GoodsTypeSuperFan, livegoods.GoodsTypeDanmaku, livegoods.GoodsTypeLuckyBox}).
		Where("seller_id IN (?)", creatorIDs).
		Where("more <> ''").
		Where("JSON_EXTRACT(more, '$.open_status') = ?", livetxnorder.OpenStatusOpen).
		// 34 分钟可以保证所有热度衰减至 0
		Where("create_time >= ? AND create_time < ?", param.NowTime-34*util.SecondOneMinute, param.NowTime).
		Where(scoreWhere).
		Group("seller_id")
	err = db.Find(&resp.Data).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i := range resp.Data {
		if r := roomMap[resp.Data[i].CreatorID]; r != nil {
			resp.Data[i].RoomID = r.RoomID
		}
	}
	return resp, nil
}
