package cron

import (
	"fmt"
	"html"
	"time"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 通知类型
const (
	noticeTypeThreeDay = iota + 1 // 1：贵族有效期剩余 3 天
	noticeTypeExpired             // 2：贵族身份失效
)

type nobleTrialExpireParam struct {
	key               string
	now               time.Time
	lastExecutionTime time.Time
}

// ActionCronNobleTrialExpire 体验贵族过期通知
// 运行周期：0 0/10 * * * * 每十分钟执行一次
/*
 * @api {post} /rpc/cron/noble/trial-expire 体验贵族过期通知
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "成功"
 *       }
 *     }
 */
func ActionCronNobleTrialExpire(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newNobleTrialExpireParam()
	if err != nil {
		return nil, err
	}
	err = param.expireNotice(noticeTypeThreeDay)
	if err != nil {
		return nil, err
	}
	err = param.expireNotice(noticeTypeExpired)
	if err != nil {
		return nil, err
	}
	param.saveExecutionTime()
	return handler.M{"msg": "成功"}, nil
}

func newNobleTrialExpireParam() (*nobleTrialExpireParam, error) {
	var (
		now = goutil.TimeNow()
		key = keys.KeyNobleTrialExpireCronExecutionTime0.Format()
	)
	et, err := service.Redis.Get(key).Int64()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		// 首次执行没有值，默认为 10 分钟前
		et = now.Add(-10 * time.Minute).Unix()
	}
	param := &nobleTrialExpireParam{
		key:               key,
		now:               now,
		lastExecutionTime: time.Unix(et, 0),
	}
	return param, nil
}

func (param *nobleTrialExpireParam) expireNotice(notifyType int) error {
	var (
		startTime int64
		endTime   int64
	)
	switch notifyType {
	case noticeTypeThreeDay:
		threeDays := 3 * 24 * time.Hour
		startTime = param.lastExecutionTime.Add(threeDays).Unix()
		endTime = param.now.Add(threeDays).Unix()
	case noticeTypeExpired:
		startTime = param.lastExecutionTime.Unix()
		endTime = param.now.Unix()
	default:
		return nil
	}

	uvs, err := vip.GetUserByTime(vip.TypeLiveTrialNoble, startTime, endTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(uvs) == 0 {
		return nil
	}
	msgs := make([]pushservice.SystemMsg, 0, len(uvs))
	for _, uv := range uvs {
		msgs = append(msgs, param.buildMsg(uv, notifyType))
	}
	if len(msgs) != 0 {
		err = service.PushService.SendSystemMsgWithOptions(msgs, &pushservice.SystemMsgOptions{
			DisableHTMLEscape: true,
		})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	switch notifyType {
	case noticeTypeThreeDay:
		logger.Infof("【体验贵族三天后过期】共处理用户数：%d", len(uvs))
	case noticeTypeExpired:
		userIDs := make([]int64, 0, len(uvs))
		for _, uv := range uvs {
			userIDs = append(userIDs, uv.UserID)
		}
		// 重新佩戴最高等级贵族的外观
		param.wearUserMaxNobleAppearances(userIDs)
		// 删除用户过期贵族缓存
		vip.ClearUserVipCache(userIDs...)
		logger.Infof("【体验贵族已过期】共处理用户数：%d", len(uvs))
	}

	return nil
}

// 记录当前定时任务执行时间
func (param *nobleTrialExpireParam) saveExecutionTime() {
	err := service.Redis.Set(param.key, param.now.Unix(), 0).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *nobleTrialExpireParam) buildMsg(uv vip.UserVip, notifyType int) pushservice.SystemMsg {
	msg := pushservice.SystemMsg{
		UserID: uv.UserID,
		Title:  "直播贵族体验到期提示",
	}
	// FIXME: 缺少 target="_blank"
	htmlTip := fmt.Sprintf(`<a href="%s">开通更多贵族</a>`, config.Conf.Params.NobleParams.BuyNobleURL)
	switch notifyType {
	case noticeTypeExpired:
		msg.Content = fmt.Sprintf("您的%s贵族体验期现已结束，您可%s以便继续享受尊贵特权。", html.EscapeString(uv.Title), htmlTip)
	case noticeTypeThreeDay:
		et := time.Unix(uv.ExpireTime, 0).Format(util.TimeFormatYMDHMS)
		msg.Content = fmt.Sprintf("您的%s贵族体验期将在 %s 结束，您可%s以便继续享受尊贵特权。", html.EscapeString(uv.Title), et, htmlTip)
	}
	return msg
}

func (param *nobleTrialExpireParam) wearUserMaxNobleAppearances(userIDs []int64) {
	if len(userIDs) == 0 {
		return
	}

	err := userappearance.WearUserMaxNobleAppearances(userIDs)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_count": len(userIDs),
			"time_range": fmt.Sprintf("%s - %s", param.lastExecutionTime.Format(util.TimeFormatYMDHMS), param.now.Format(util.TimeFormatYMDHHMM)),
		}).Errorf("重新佩戴外观失败：%v", err)
		// PASS
	}
}
