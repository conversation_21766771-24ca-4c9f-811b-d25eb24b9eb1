package cron

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronUserBackpackExpireNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID = int64(12)
		testGiftID = int64(304)
		now        = goutil.TimeNow()
	)

	require.NoError(userstatus.SaveUsersBackPackNotifiedTime([]int64{testUserID}, now.Add(-24*time.Hour)))
	err := useritems.AddGiftToUsers([]int64{testUserID}, &gift.Gift{
		GiftID: testGiftID,
		Type:   gift.TypeFree,
	}, 10, 0, now.Add(-48*time.Hour).Unix(), now.Add(24*time.Hour).Add(-10*time.Minute).Unix())
	require.NoError(err)
	var count int
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input any) (output any, err error) {
		count++
		return
	})
	defer cancel()
	c := handler.NewRPCTestContext("", handler.M{})
	_, resp, err := ActionCronUserBackpackExpireNotify(c)
	require.NoError(err)
	assert.Equal("success", resp)
	assert.Equal(1, count)

	c = handler.NewRPCTestContext("", handler.M{"exclude_gift_ids": []int64{testGiftID}})
	_, resp, err = ActionCronUserBackpackExpireNotify(c)
	require.NoError(err)
	assert.Equal("empty expire", resp)
	assert.Equal(1, count)
}
