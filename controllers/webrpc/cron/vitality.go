package cron

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionCronVitalityRenewal 元气值恢复
/**
 * @api {post} /rpc/cron/live/vitality/renewal 触发元气值自动恢复
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "成功"
 *     }
 */
func ActionCronVitalityRenewal(c *handler.Context) (handler.ActionResponse, error) {
	logger.Info("活力值恢复开始")
	now := goutil.TimeNow()
	fiveDaysBefore := now.AddDate(0, 0, -5).Unix()
	var needAdd []*liveaddendum.LiveAddendum
	// TODO: 有 bug 元气值的 user_id 有 nil 值
	err := service.DB.Select("user_id, vitality").
		Where("last_punished_time < ? AND vitality < ?",
			fiveDaysBefore, liveaddendum.MaxVitality).
		Order("user_id ASC").Find(&needAdd).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(needAdd) == 0 {
		logger.Info("活力值恢复结束：主播都元气满满")
		return "成功", nil
	}
	creatorIDs := make([]int64, len(needAdd))
	for i := 0; i < len(needAdd); i++ {
		creatorIDs[i] = needAdd[i].UserID
	}
	var added []int64
	// 查找 5 天内已经恢复元气值的用户
	err = service.DB.Table(liveaddendum.LogTableName()).
		Where("creator_id IN (?) AND create_time > ? AND operator = ?",
			creatorIDs, fiveDaysBefore, liveaddendum.OperatorAdd).Order("creator_id ASC").
		Pluck("creator_id", &added).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	needAddUserIDs := removeAddedCreatorIDs(needAdd, added)
	if len(needAddUserIDs) == 0 {
		logger.Info("活力值恢复结束：没有主播需要恢复")
		return "成功", nil
	}
	logs := make([]liveaddendum.Log, len(needAddUserIDs))
	for i := 0; i < len(needAddUserIDs); i++ {
		logs[i] = liveaddendum.Log{
			CreatorID:      needAddUserIDs[i],
			Operator:       liveaddendum.OperatorAdd,
			VitalityChange: 1,
			Reason:         "主播在 5 个自然日内无任何违规行为，增加 1 分",
			CreateTime:     now.Unix(),
		}
	}
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		err := tx.Exec("UPDATE "+liveaddendum.TableName()+
			" SET modified_time = ?, vitality = LEAST(?, vitality+1) WHERE user_id IN (?)",
			now.Unix(), liveaddendum.MaxVitality, needAddUserIDs).Error
		if err != nil {
			return err
		}
		return servicedb.BatchInsert(tx, liveaddendum.LogTableName(), logs)
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	logger.Info("活力值恢复结束：已恢复活力值")
	return "成功", nil
}

// removeAddedCreatorIDs 去掉已经恢复元气值的用户，返回需要恢复元气值的用户 ID
// NOTICE: needAdd 和 addedID 已通过用户 ID 排序
func removeAddedCreatorIDs(needAdd []*liveaddendum.LiveAddendum, addedID []int64) []int64 {
	res := make([]int64, 0, len(needAdd))
	for i, j := 0, 0; i < len(needAdd); {
		if j >= len(addedID) {
			res = append(res, needAdd[i].UserID)
			i++
			continue
		}
		if needAdd[i].UserID < addedID[j] {
			res = append(res, needAdd[i].UserID)
			i++
		} else {
			if needAdd[i].UserID == addedID[j] {
				i++
			}
			j++
		}
	}
	return res
}
