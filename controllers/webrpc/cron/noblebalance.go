package cron

import (
	"errors"
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 钻石过期通知类型
const (
	NoticeTypeClearBalancesInOneDay    = iota + 1 // 1：贵族钻石清零剩余 1 天时
	NoticeTypeClearBalancesInThreeDays            // 2：贵族钻石清零剩余 3 天时
	NoticeTypeClearBalancesClear                  // 3：贵族钻石清零通知
)

type expireBalanceNoticeParam struct {
	now                    time.Time
	title                  string
	balanceNoticeInfo      string
	balanceExpireStartTime time.Time
	balanceExpireEndTime   time.Time
	userBalances           []vip.UserExpireBalance // 指定过期时间内的用户剩余钻石

	noticeType int
	vipConfig  *vip.ConfigResp
}

// UserBalanceExpireNotice 用户钻石过期通知
func UserBalanceExpireNotice(noticeType int, vipConfig *vip.ConfigResp) error {
	// WORKAROUND: 贵族钻石新规则生效前，贵族钻石清零通知不发送
	if noticeType == NoticeTypeClearBalancesClear &&
		vipConfig.EnableNewVipBalanceTime > goutil.TimeNow().Unix() {
		return nil
	}

	balanceParam, err := newExpireBalanceNoticeParam(noticeType, vipConfig)
	if err != nil {
		return err
	}
	if len(balanceParam.userBalances) == 0 {
		logger.Infof("【贵族钻石清零通知】类型：%s，已通知 0 个符合条件的用户", balanceParam.balanceNoticeInfo)
		return nil
	}
	balanceParam.sendNotice()
	logger.Infof("【贵族钻石清零通知】类型：%s，已通知 %d 个符合条件的用户", balanceParam.balanceNoticeInfo, len(balanceParam.userBalances))
	return nil
}

func newExpireBalanceNoticeParam(noticeType int, vipConfig *vip.ConfigResp) (*expireBalanceNoticeParam, error) {
	if !goutil.HasElem(
		[]int{NoticeTypeClearBalancesInOneDay, NoticeTypeClearBalancesInThreeDays, NoticeTypeClearBalancesClear},
		noticeType,
	) {
		return nil, errors.New("贵族钻石清零通知类型错误")
	}

	param := &expireBalanceNoticeParam{
		now:        goutil.TimeNow().Add(2 * time.Minute), // 获取当日时间（允许提前两分钟执行）
		noticeType: noticeType,
		vipConfig:  vipConfig,
	}
	// 获取贵族钻石某时间范围过期数据
	// 新规则生效后查询某时间范围含冻结时间的过期数据
	expireBalanceType := vip.TypeExpireBalanceClear
	if param.vipConfig.EnableNewVipBalanceTime > goutil.TimeNow().Unix() {
		// 新规则生效前查询某时间范围的过期数据
		expireBalanceType = vip.TypeExpireBalanceExpired
	}
	param.loadTime(expireBalanceType)

	var err error
	param.userBalances, err = vip.GetUserExpireBalanceByTime(param.balanceExpireStartTime.Unix(), param.balanceExpireEndTime.Unix(),
		expireBalanceType)
	if err != nil {
		return nil, err
	}
	if len(param.userBalances) == 0 {
		return param, nil
	}
	return param, nil
}

func (param *expireBalanceNoticeParam) loadTime(expireBalanceType int) {
	y, m, d := param.now.Date()
	switch param.noticeType {
	case NoticeTypeClearBalancesInOneDay:
		param.balanceNoticeInfo = "钻石有效期剩余 1 天通知"
		if expireBalanceType == vip.TypeExpireBalanceExpired {
			param.title = "贵族钻石即将到期"
		} else {
			param.title = "贵族钻石即将清零"
		}
		param.balanceExpireStartTime = time.Date(y, m, d, 0, 0, 0, 0, time.Local)
		param.balanceExpireEndTime = time.Date(y, m, d+1, 0, 0, 0, 0, time.Local)
	case NoticeTypeClearBalancesInThreeDays:
		param.balanceNoticeInfo = "钻石有效期剩余 3 天通知"
		if expireBalanceType == vip.TypeExpireBalanceExpired {
			param.title = "贵族钻石即将到期"
		} else {
			param.title = "贵族钻石即将清零"
		}
		param.balanceExpireStartTime = time.Date(y, m, d+2, 0, 0, 0, 0, time.Local)
		param.balanceExpireEndTime = time.Date(y, m, d+3, 0, 0, 0, 0, time.Local)
	case NoticeTypeClearBalancesClear: // 新规则生效前，贵族清零通知不会发送
		param.balanceNoticeInfo = "贵族钻石清零通知"
		param.title = "贵族钻石清零"
		param.balanceExpireStartTime = time.Date(y, m, d-1, 0, 0, 0, 0, time.Local)
		param.balanceExpireEndTime = time.Date(y, m, d, 0, 0, 0, 0, time.Local)
	default:
		panic("贵族钻石过期通知类型错误")
	}
}

func (param *expireBalanceNoticeParam) sendNotice() {
	y, m, d := param.now.Date()
	// 钻石过期通知固定为 0 点发送
	noticeTime := time.Date(y, m, d, 0, 0, 0, 0, time.Local).Unix()
	msgs := make([]pushservice.SystemMsg, 0, len(param.userBalances))
	for _, user := range param.userBalances {
		msgs = append(msgs, pushservice.SystemMsg{
			UserID:   user.UserID,
			Title:    param.title,
			Content:  param.buildMessage(user),
			SendTime: noticeTime,
		})
	}
	err := service.PushService.SendSystemMsgWithOptions(msgs, &pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.WithFields(logger.Fields{
			"notice_type": param.noticeType,
			"user_num":    len(param.userBalances),
		}).Error(err)
		// PASS
	}
}

func (param *expireBalanceNoticeParam) buildMessage(user vip.UserExpireBalance) string {
	// NOTICE: 过期时间需要显示为 23:59:59 故减一秒
	balanceExpireEndTimeStr := param.balanceExpireEndTime.Add(-time.Second).Format(goutil.TimeFormatHMS)
	url := config.Conf.Params.URL.Live + "noble/mynoble?tab=balance"
	if param.vipConfig.EnableNewVipBalanceTime > goutil.TimeNow().Unix() {
		return fmt.Sprintf(
			`尊敬的用户，您有 %d 贵族钻石将在 %s 到期清零，请尽快使用（若您的贵族钻石为冻结状态，请先开通或续费贵族来解除冻结状态）<a href="%s">查看我的贵族钻石</a>`,
			user.TotalBalance, balanceExpireEndTimeStr, url,
		)
	}

	if param.noticeType == NoticeTypeClearBalancesClear {
		return fmt.Sprintf(
			`尊敬的用户，您有 %d 贵族钻石已在 %s 到期清零。`,
			user.TotalBalance, balanceExpireEndTimeStr)
	}

	return fmt.Sprintf(
		`尊敬的用户，您有 %d 贵族钻石将在 %s 到期清零，重新开通贵族可解冻贵族钻石并恢复正常使用。<a href="%s">查看我的贵族钻石</a>`,
		user.TotalBalance, balanceExpireEndTimeStr, url,
	)
}
