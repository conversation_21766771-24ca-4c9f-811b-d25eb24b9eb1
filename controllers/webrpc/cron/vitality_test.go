package cron

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronVitalityRenewal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	la := []liveaddendum.LiveAddendum{
		{ID: 1234, Vitality: 11, UserID: 1234},
		{ID: 5678, Vitality: 10, UserID: 5678},
		{ID: 4567, Vitality: 9, UserID: 4567},
	}
	insertSQL := fmt.Sprintf("INSERT INTO %s (id, vitality, user_id, create_time, modified_time) VALUES", la[0].TableName()) +
		fmt.Sprintf("(%d, %d, %d, 1, 1),", la[0].ID, la[0].Vitality, la[0].UserID) +
		fmt.Sprintf("(%d, %d, %d, 1, 1),", la[1].ID, la[1].Vitality, la[1].UserID) +
		fmt.Sprintf("(%d, %d, %d, 1, 1)", la[2].ID, la[2].Vitality, la[2].UserID)
	require.NoError(service.DB.Exec(insertSQL).Error)
	require.NoError(service.DB.Table(liveaddendum.LogTableName()).Delete("", "creator_id IN (?)", []int64{1234, 4567, 5678}).Error)
	log := &liveaddendum.Log{
		CreatorID:  4567,
		CreateTime: goutil.TimeNow().Unix() - 1000,
		Operator:   liveaddendum.OperatorAdd,
		Reason:     "TestActionCronVitalityRenewal"}
	require.NoError(service.DB.Create(log).Error)
	c := handler.NewRPCTestContext("vitality/renewal", nil)
	_, err := ActionCronVitalityRenewal(c)
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)
	require.NoError(service.DB.Where("user_id IN (?,?,?)", 1234, 5678, 4567).Find(&la).Error)
	assert.Equal([]int64{1234, 4567, 5678}, []int64{la[0].UserID, la[1].UserID, la[2].UserID})
	assert.Equal(12, la[0].Vitality)
	assert.Equal(9, la[1].Vitality)
	assert.Equal(11, la[2].Vitality)
}

func TestRemoveAddedCreatorIDs(t *testing.T) {
	assert := assert.New(t)
	needAdd := []*liveaddendum.LiveAddendum{
		{UserID: 1},
		{UserID: 2},
		{UserID: 3},
		{UserID: 4},
		{UserID: 5},
		{UserID: 6},
		{UserID: 7},
	}
	addedID := []int64{2, 2, 4, 4, 5}
	after := removeAddedCreatorIDs(needAdd, addedID)
	assert.Equal([]int64{1, 3, 6, 7}, after)
}
