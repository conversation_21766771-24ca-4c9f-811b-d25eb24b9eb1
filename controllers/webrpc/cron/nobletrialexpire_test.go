package cron

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronNobleTrialExpire(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(vip.URLGetUserByTime, func(input any) (any, error) {
		return []vip.UserVip{}, nil
	})
	defer cleanup()

	reps, err := ActionCronNobleTrialExpire(handler.NewRPCTestContext("", nil))
	require.NoError(err)
	assert.Equal("成功", reps.(handler.M)["msg"])
}

func TestNewNobleTrialExpireParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 2, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	err := service.Redis.Set(keys.KeyNobleTrialExpireCronExecutionTime0.Format(), goutil.TimeNow().Add(-time.Hour).Unix(), 0).Err()
	require.NoError(err)
	param, err := newNobleTrialExpireParam()
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(goutil.TimeNow(), param.now)
	assert.Equal(goutil.TimeNow().Add(-time.Hour), param.lastExecutionTime)
}

func TestNobleTrialExpireParam_expireNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	cleanup := mrpc.SetMock(vip.URLGetUserByTime, func(any) (any, error) {
		return []vip.UserVip{
			{
				UserID:     1,
				Title:      "无敌选手",
				ExpireTime: goutil.TimeNow().AddDate(0, 0, 3).Unix(),
			},
		}, nil
	})
	defer cleanup()
	var count int
	cleanup = mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		count++
		body, ok := input.(map[string]interface{})
		require.True(ok)
		msgs, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		require.NotEmpty(msgs)
		assert.Equal("直播贵族体验到期提示", msgs[0].Title)
		switch count {
		case noticeTypeThreeDay:
			assert.Equal(fmt.Sprintf("您的无敌选手贵族体验期将在 2020-01-04 00:00:00 结束，您可<a href=\"%s\">开通更多贵族</a>以便继续享受尊贵特权。", config.Conf.Params.NobleParams.BuyNobleURL), msgs[0].Content)
		case noticeTypeExpired:
			assert.Equal(fmt.Sprintf("您的无敌选手贵族体验期现已结束，您可<a href=\"%s\">开通更多贵族</a>以便继续享受尊贵特权。", config.Conf.Params.NobleParams.BuyNobleURL), msgs[0].Content)
		}
		return "success", nil
	})
	defer cleanup()

	now := goutil.TimeNow()
	param := &nobleTrialExpireParam{
		now:               now,
		lastExecutionTime: now.Add(-time.Hour),
	}
	err := param.expireNotice(noticeTypeThreeDay)
	require.NoError(err)
	assert.Equal(1, count)

	param = &nobleTrialExpireParam{
		now:               now,
		lastExecutionTime: now.Add(-time.Hour),
	}
	err = param.expireNotice(noticeTypeExpired)
	require.NoError(err)
	assert.Equal(2, count)
}

func TestNobleTrialExpireParam_saveExecutionTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	param := &nobleTrialExpireParam{
		key: keys.KeyNobleTrialExpireCronExecutionTime0.Format(),
		now: goutil.TimeNow(),
	}
	err := service.Redis.Set(param.key, goutil.TimeNow().Add(-time.Hour).Unix(), 0).Err()
	require.NoError(err)

	param.saveExecutionTime()
	et, err := service.Redis.Get(param.key).Int64()
	require.NoError(err)
	assert.Equal(goutil.TimeNow().Unix(), et)
}

func TestNobleTrialExpireParam_buildMsg(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2020, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	param := &nobleTrialExpireParam{}

	msg := param.buildMsg(vip.UserVip{
		UserID:     1,
		Title:      "无敌选手",
		ExpireTime: goutil.TimeNow().AddDate(0, 0, 3).Unix(),
	}, noticeTypeThreeDay)
	assert.Equal(fmt.Sprintf("您的无敌选手贵族体验期将在 2020-01-04 00:00:00 结束，您可<a href=\"%s\">开通更多贵族</a>以便继续享受尊贵特权。", config.Conf.Params.NobleParams.BuyNobleURL), msg.Content)

	msg = param.buildMsg(vip.UserVip{
		UserID:     1,
		Title:      "无敌选手",
		ExpireTime: goutil.TimeNow().Add(-time.Second).Unix(),
	}, noticeTypeExpired)
	assert.Equal(fmt.Sprintf("您的无敌选手贵族体验期现已结束，您可<a href=\"%s\">开通更多贵族</a>以便继续享受尊贵特权。", config.Conf.Params.NobleParams.BuyNobleURL), msg.Content)
}

func TestNobleTrialExpireParam_wearUserMaxNobleAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserIDs = []int64{230043242141231}
	)
	cancel := mrpc.SetMock(vip.URLLiveUserLevel, func(any) (any, error) {
		return nil, nil
	})
	defer cancel()

	userappearances := make([]interface{}, 0, len(testUserIDs))
	for _, id := range testUserIDs {
		userappearances = append(userappearances, userappearance.UserAppearance{
			UserID:     id,
			From:       appearance.FromNoble,
			Type:       appearance.TypeMessageBubble,
			Status:     userappearance.StatusOwned,
			StartTime:  goutil.TimeNow().Add(-2 * time.Hour).Unix(),
			ExpireTime: goutil.NewInt64(goutil.TimeNow().Add(-time.Hour).Unix()),
		})
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userappearance.Collection().DeleteMany(ctx, bson.M{
		"user_id": bson.M{"$in": testUserIDs},
	})
	require.NoError(err)
	_, err = userappearance.Collection().InsertMany(ctx, userappearances)
	require.NoError(err)

	param := &nobleTrialExpireParam{
		now:               goutil.TimeNow(),
		lastExecutionTime: goutil.TimeNow().Add(-time.Hour),
	}
	param.wearUserMaxNobleAppearances(testUserIDs)

	uas, err := userappearance.ListAppearanceByUserID(testUserIDs[0], appearance.TypeMessageBubble)
	require.NoError(err)
	assert.Empty(uas)
}
