package cron

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionStickerSuperFans 接收 rpc 消息后更新超粉榜单
// 运行周期：0 0 20 19 5
/**
 * @api {post} /rpc/cron/sticker/superfans 解锁超粉表情包
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionStickerSuperFans(c *handler.Context) (handler.ActionResponse, error) {
	param, err := params.FindSticker()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	now := goutil.TimeNow()
	if !param.IsValidSuperFansUnlockTime(now) {
		return "不在解锁超粉表情包时间范围内", nil
	}

	pkg, err := livesticker.FindPackage(param.SuperFans.PackageID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if pkg == nil {
		return nil, actionerrors.ErrNotFound("表情包不存在")
	}
	owners, err := pkg.Owners(now)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	roomIDs := make([]int64, 0, len(owners))
	for _, owner := range owners {
		roomIDs = append(roomIDs, owner.RoomID)
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := livemedal.Collection().Aggregate(ctx, bson.A{
		bson.M{
			"$match": bson.M{
				"status":                bson.M{"$gt": livemedal.StatusPending},
				"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
				"room_id":               bson.M{"$nin": roomIDs},
			},
		},
		bson.M{"$group": bson.M{
			"_id":            "$room_id",
			"super_fans_num": bson.M{"$sum": 1},
		}},
		bson.M{"$match": bson.M{"super_fans_num": bson.M{"$gte": param.SuperFans.UnlockTriggerNum}}},
	})
	if err != nil {
		return nil, err
	}
	var res []struct {
		RoomID       int64 `bson:"_id"`
		SuperFansNum int64 `bson:"super_fans_num"`
	}
	err = cur.All(ctx, &res)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(res) == 0 {
		return "成功解锁 0 条", nil
	}

	addOwners := make([]*livesticker.PackageOwner, 0, len(res))
	for _, r := range res {
		addOwners = append(addOwners, &livesticker.PackageOwner{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			PackageID:    pkg.ID,
			RoomID:       r.RoomID,
			UserID:       0, // NOTICE: 超粉表情包只分配给直播间，不分配给用户
			StartTime:    now.Unix(),
			ExpireTime:   pkg.ExpireTime,
		})
	}
	// NOTICE: 如果触发唯一索引错误，这里的其他解锁也会失败
	err = servicedb.BatchInsert(livesticker.DB(), livesticker.PackageOwner{}.TableName(), addOwners)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return fmt.Sprintf("成功解锁 %d 条", len(addOwners)), nil
}
