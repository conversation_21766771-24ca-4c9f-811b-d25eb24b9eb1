package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/models/mysql/archiveliverankhour"
	"github.com/MiaoSiLa/live-service/models/mysql/archiveliverankmonth"
	"github.com/MiaoSiLa/live-service/models/mysql/archiveliveranknova"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronRankArchiveMonth(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testTime := int64(1659608685)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTime, 0)
	})
	defer goutil.SetTimeNow(nil)

	when := time.Unix(testTime, 0).AddDate(0, -1, 0)
	key := usersrank.Key(usersrank.TypeMonth, when)
	require.NoError(service.Redis.Del(key).Err())

	// 测试没有主播榜的情况
	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/rank/archive/month", false, nil)
	resp, err := ActionCronRankArchiveMonth(c)
	require.NoError(err)
	assert.EqualValues("主播榜月榜为空", resp)

	testData := []*redis.Z{
		{Score: 10, Member: 5},
		{Score: 11, Member: 10},
	}
	require.NoError(service.Redis.ZAdd(key, testData...).Err())

	testRoomIDs := []int64{24113499, 22489473}
	keys := make([]string, 0, len(testRoomIDs))
	for _, roomID := range testRoomIDs {
		keys = append(keys, roomsrank.Key(roomID, roomsrank.RankTypeMonth, when))
	}
	require.NoError(service.Redis.Del(keys...).Err())
	pipe := service.Redis.Pipeline()
	for _, key := range keys {
		pipe.ZAdd(key,
			&redis.Z{Score: 10, Member: 1234},
			&redis.Z{Score: 11, Member: 5678},
		)
	}
	_, err = pipe.Exec()
	require.NoError(err)

	month := when.Format(util.TimeFormatYM)
	err = service.LiveDB.Where("month = ?", month).Delete(&archiveliverankmonth.ArchiveLiveRankMonth{}).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/rpc/cron/rank/archive/month", false, nil)
	resp, err = ActionCronRankArchiveMonth(c)
	require.NoError(err)
	assert.EqualValues("归档成功", resp)

	var count int
	err = service.LiveDB.Table(archiveliverankmonth.ArchiveLiveRankMonth{}.TableName()).
		Where("month = ?", month).Count(&count).Error
	require.NoError(err)
	assert.Equal(6, count)
}

func TestActionCronRankArchiveHour(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testTime := int64(1659608685)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTime, 0)
	})
	defer goutil.SetTimeNow(nil)

	when := time.Unix(testTime, 0).Add(-time.Hour)
	key := usersrank.Key(usersrank.TypeHour, when)
	require.NoError(service.Redis.Del(key).Err())
	var testCreatorID int64 = 5
	testRoomID, err := room.FindRoomID(testCreatorID)
	require.NoError(err)
	require.Greater(testRoomID, int64(0))
	roomUsersRankKey := roomsrank.Key(testRoomID, roomsrank.RankTypeHourly, when)
	require.NoError(service.Redis.Del(roomUsersRankKey).Err())

	// 测试没有主播榜的情况
	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/rank/archive/hour", false, nil)
	resp, err := ActionCronRankArchiveHour(c)
	require.NoError(err)
	assert.EqualValues("主播榜小时榜为空", resp)

	testData := []*redis.Z{
		{Score: 10, Member: testCreatorID},
		{Score: 11, Member: 10},
	}
	require.NoError(service.Redis.ZAdd(key, testData...).Err())
	testRoomUsersRankData := []*redis.Z{
		{Score: 100000, Member: 1},
		{Score: 100000, Member: 2},
	}
	require.NoError(service.Redis.ZAdd(roomUsersRankKey, testRoomUsersRankData...).Err())

	rankTime := time.Date(when.Year(), when.Month(), when.Day(), when.Hour(), 0, 0, 0, time.Local).
		Format(util.TimeFormatYMDHMS)
	err = service.LiveDB.Where("rank_time = ?", rankTime).Delete(&archiveliverankhour.ArchiveLiveRankHour{}).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/rpc/cron/rank/archive/hour", false, nil)
	resp, err = ActionCronRankArchiveHour(c)
	require.NoError(err)
	assert.Equal("归档成功", resp)

	var count int
	err = service.LiveDB.Table(archiveliverankhour.ArchiveLiveRankHour{}.TableName()).
		Where("rank_time = ?", rankTime).Count(&count).Error
	require.NoError(err)
	assert.Equal(len(testData)+len(testRoomUsersRankData), count)
}

func TestFinishTopQuests(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := time.Unix(1705656051, 0)
	qs, err := quests.FindHourQuests(now)
	require.NoError(err)
	require.EqualValues(3, len(qs))

	ids := make([]primitive.ObjectID, 0, len(qs))
	for _, q := range qs {
		ids = append(ids, q.OID)
	}

	_, err = quests.CollectionUserQuests().DeleteMany(ctx, bson.M{
		"_quest_id": bson.M{"$in": ids},
	})
	require.NoError(err)

	finishTopQuests([]usersrank.Info{
		{Rank: 1, UserID: 1, RoomID: 1},
		{Rank: 2, UserID: 2, RoomID: 1},
		{Rank: 3, UserID: 3, RoomID: 1},
	}, now)

	count, err := quests.CollectionUserQuests().CountDocuments(ctx, bson.M{
		"_quest_id": bson.M{"$in": ids},
	})
	require.NoError(err)
	assert.EqualValues(3, count)
}

func TestActionCronRankArchiveNova(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testTime := int64(1659608685)
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTime, 0)
	})
	defer goutil.SetTimeNow(nil)

	when := time.Unix(testTime, 0).AddDate(0, 0, -1)
	key := usersrank.Key(usersrank.TypeNova, when)
	require.NoError(service.Redis.Del(key).Err())

	// 测试没有主播榜的情况
	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/rank/archive/nova", false, nil)
	resp, err := ActionCronRankArchiveNova(c)
	require.NoError(err)
	assert.EqualValues("主播新人榜为空", resp)

	testData := []*redis.Z{
		{Score: 10, Member: 5},
		{Score: 11, Member: 10},
	}
	require.NoError(service.Redis.ZAdd(key, testData...).Err())

	rankTime := util.BeginningOfDay(when).Format(util.TimeFormatYMD)
	err = service.LiveDB.Where("rank_time = ?", rankTime).Delete(&archiveliveranknova.ArchiveLiveRankNova{}).Error
	require.NoError(err)

	c = handler.NewTestContext(http.MethodPost, "/rpc/cron/rank/archive/nova", false, nil)
	resp, err = ActionCronRankArchiveNova(c)
	require.NoError(err)
	assert.Equal("归档成功", resp)

	var count int
	err = service.LiveDB.Table(archiveliveranknova.ArchiveLiveRankNova{}.TableName()).
		Where("rank_time = ?", rankTime).Count(&count).Error
	require.NoError(err)
	assert.Equal(2, count)
}
