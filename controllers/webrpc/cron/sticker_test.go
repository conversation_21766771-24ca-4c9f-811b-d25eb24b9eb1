package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionStickerSuperFans(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	sticker := params.Sticker{
		SuperFans: params.SuperFansStickerPackage{
			PackageID:        1,
			UnlockStartTime:  1,
			UnlockEndTime:    10000000000000,
			UnlockTriggerNum: 1,
		},
	}
	require.NoError(service.LRURedis.Set(keys.KeyParams1.Format(params.KeySticker), tutil.SprintJSON(sticker), 10*time.Second).Err())

	require.NoError(livesticker.DB().Delete(livesticker.PackageOwner{}, "package_id = ?", sticker.SuperFans.PackageID).Error)

	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	resp, err := ActionStickerSuperFans(c)
	require.NoError(err)
	assert.Contains(resp, "成功解锁")

	var owners []*livesticker.PackageOwner
	require.NoError(livesticker.DB().Where("package_id = ?", sticker.SuperFans.PackageID).Find(&owners).Error)
	assert.NotEmpty(owners)
}
