package cron

import (
	"encoding/json"
	"html"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(notifyMonthlyParam{}, "notify_type")
}

func TestActionCronRankUserHourly(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	roomIDs := []int64{3192516, 369892, 122340}
	_, err := collection.UpdateMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$set": bson.M{"status.open": room.StatusOpenTrue}})
	require.NoError(err)

	cur, err := collection.Find(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(bson.M{"room_id": 1, "creator_id": 1}))
	require.NoError(err)
	var rooms []*room.Simple
	err = cur.All(ctx, &rooms)
	require.NoError(err)
	require.Len(rooms, 3)
	roomsMap := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Simple)

	now := goutil.TimeNow()
	key := usersrank.Key(usersrank.TypeHour, now)
	pipe := service.Redis.Pipeline()
	pipe.Del(key)
	pipe.ZAdd(key,
		&redis.Z{Score: 1000, Member: roomsMap[roomIDs[0]].CreatorID},
		&redis.Z{Score: 500, Member: roomsMap[roomIDs[1]].CreatorID},
	)
	pipe.Expire(key, 1*time.Minute)
	_, err = pipe.Exec()
	require.NoError(err)

	res, err := rankUserHourly(true, true)
	require.NoError(err)

	expectedRes := []newRankPayload{
		{RoomID: roomIDs[0], Rank: 1, RankUp: util.NewInt64(500)}, // 第一名领先第二名的差值
		{RoomID: roomIDs[1], Rank: 2, RankUp: util.NewInt64(501)},
		{RoomID: roomIDs[2], Rank: 0, RankUp: util.NewInt64(1)},
	}
	require.GreaterOrEqual(len(res), len(expectedRes), "预期开播直播间数量大于小时榜数量")
	for _, exp := range expectedRes {
		found := false
		for _, rank := range res {
			if rank.RoomID == exp.RoomID {
				assert.Equal(usersrank.TypeHour, rank.RankType)
				assert.Equal(exp.Rank, rank.Rank)
				assert.Equal(*exp.RankUp, *rank.RankUp)
				found = true
				break
			}
		}
		assert.True(found, "预期可以找到小时榜上的直播间")
	}

	// 测试针对大主播的筛选
	_, err = collection.UpdateMany(ctx, bson.M{"room_id": roomIDs[1]},
		bson.M{"$set": bson.M{"config.popularity": 1}})
	require.NoError(err)

	res, err = rankUserHourly(false, false)
	require.NoError(err)
	require.Len(res, 1)
	assert.Equal(roomIDs[1], res[0].RoomID)
}

func TestActionCronNotifyHourRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("im://broadcast", func(interface{}) (interface{}, error) {
		return "success", nil
	})
	defer cancel()

	notifyKey := keys.KeyCronNotifyHourRank0.Format()
	_, err := service.Redis.Del(notifyKey).Result()
	require.NoError(err)
	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	_, err = service.Redis.Set(notifyKey, "1", time.Minute).Result()
	require.NoError(err)
	_, err = ActionCronNotifyHourRank(c)
	require.NoError(err)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	collection := service.MongoDB.Collection(room.CollectionName)
	roomsInRank := []int64{22335, 22334}
	roomsOutRank := []int64{22331, 22330}
	roomIDs := append(roomsInRank, roomsOutRank...)
	_, err = collection.UpdateMany(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}},
		bson.M{"$set": bson.M{"status.open": room.StatusOpenTrue}})
	require.NoError(err)
	cur, err := collection.Find(ctx, bson.M{"room_id": bson.M{"$in": roomIDs}},
		options.Find().SetProjection(bson.M{"room_id": 1, "creator_id": 1}))
	require.NoError(err)
	var rooms []*room.Simple
	err = cur.All(ctx, &rooms)
	require.NoError(err)
	require.Len(rooms, 4)
	roomsMap := goutil.ToMap(rooms, "RoomID").(map[int64]*room.Simple)
	now := goutil.TimeNow()
	key := usersrank.Key(usersrank.TypeHour, now)
	pipe := service.Redis.Pipeline()
	pipe.Del(key)
	pipe.ZAdd(key,
		&redis.Z{Score: 1000, Member: roomsMap[roomsInRank[0]].CreatorID},
		&redis.Z{Score: 500, Member: roomsMap[roomsInRank[1]].CreatorID},
	)
	pipe.Expire(key, time.Minute)
	_, err = pipe.Exec()
	require.NoError(err)

	res, err := rankUserHourly(false, true)
	require.NoError(err)

	require.GreaterOrEqual(len(res), len(roomsOutRank), "预期通知的直播间个数大于等于现有榜外直播间个数")
	expectedRes := []newRankPayload{
		{RoomID: roomsOutRank[0], Rank: 0, RankUp: util.NewInt64(1)},
		{RoomID: roomsOutRank[1], Rank: 0, RankUp: util.NewInt64(1)},
	}
	for _, id := range roomsInRank {
		found := false
		for _, rank := range res {
			if rank.RoomID == id {
				found = true
				break
			}
		}
		assert.False(found, "预期找不到上榜的直播间")
	}
	for _, exp := range expectedRes {
		found := false
		for _, rank := range res {
			if rank.RoomID == exp.RoomID {
				assert.Equal(usersrank.TypeHour, rank.RankType)
				assert.Equal(exp.Rank, rank.Rank)
				assert.Equal(*exp.RankUp, *rank.RankUp)
				found = true
				break
			}
		}
		assert.True(found, "预期可以找到小时榜外的直播间")
	}
}

func TestActionCronMedalCleanup(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := map[string]int{
		"run_now": 1,
	}
	c := handler.NewTestContext(http.MethodPost, "", false, body)
	// 没有配置，默认超粉已经上线
	config.Conf.AB = make(map[string]interface{})
	// 设置当前为周二
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 5, 25, 0, 0, 0, 0, time.Local)
	})
	resp, err := ActionCronMedalCleanup(c)
	require.NoError(err)
	assert.Equal("success", resp)

	// 设置当前为周一
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 5, 24, 0, 0, 0, 0, time.Local)
	})

	body["run_now"] = 0
	c = handler.NewTestContext(http.MethodPost, "", false, body)
	defer goutil.SetTimeNow(nil)
	resp, err = ActionCronMedalCleanup(c)
	require.NoError(err)
	assert.Equal("success", resp)

	c = handler.NewTestContext(http.MethodPost, "", false, body)
	resp, err = ActionCronMedalCleanup(c)
	require.NoError(err)
	assert.Equal("success", resp)

	c = handler.NewTestContext(http.MethodPost, "", false, map[string]int{})
	resp, err = ActionCronMedalCleanup(c)
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestCleanupMedal(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock("sso://vip/user-level",
		func(interface{}) (interface{}, error) {
			return nil, nil
		})
	defer cancel()
	userIDNoNoble := int64(195002)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := service.MongoDB.Collection(livemedal.CollectionName)
	now := goutil.TimeNow()
	_, err := col.DeleteMany(ctx, bson.M{"user_id": userIDNoNoble})
	assert.NoError(err)

	insertList := make([]interface{}, 0)
	var i int64
	for i = 0; i < livemedal.UserMaxMedalCount(); i++ {
		insertList = append(insertList, livemedal.LiveMedal{
			CreatedTime: now,
			Simple: livemedal.Simple{
				UserID:    userIDNoNoble,
				Point:     100,
				Status:    livemedal.StatusOwned,
				CreatorID: i + 1,
			},
		})
	}
	insertList = append(insertList,
		livemedal.LiveMedal{ // 超粉
			CreatedTime: now,
			Simple: livemedal.Simple{
				UserID:    userIDNoNoble,
				Point:     10,
				Status:    livemedal.StatusOwned,
				CreatorID: i + 1,
				Mini: livemedal.Mini{
					SuperFan: &livemedal.SuperFan{
						ExpireTime: goutil.TimeNow().Add(time.Hour).Unix(),
					},
				},
			},
		}, livemedal.LiveMedal{ // status 为 pending
			CreatedTime: now,
			Simple: livemedal.Simple{
				UserID:    userIDNoNoble,
				Point:     10,
				Status:    livemedal.StatusPending,
				CreatorID: i + 2,
			},
		}, livemedal.LiveMedal{ // status 为 pending
			CreatedTime: now,
			Simple: livemedal.Simple{
				UserID:    userIDNoNoble,
				Point:     10,
				Status:    livemedal.StatusPending,
				CreatorID: i + 2,
			},
		}, livemedal.LiveMedal{ // 被删除的 1
			CreatedTime: now,
			Simple: livemedal.Simple{
				UserID:    userIDNoNoble,
				Point:     10,
				Status:    livemedal.StatusOwned,
				CreatorID: i + 2,
			},
		}, livemedal.LiveMedal{ // 被删除的 2
			CreatedTime: now.Add(-2 * time.Hour),
			Simple: livemedal.Simple{
				UserID:    userIDNoNoble,
				Point:     100,
				Status:    livemedal.StatusOwned,
				CreatorID: i + 3,
			},
		})
	res, err := col.InsertMany(ctx, insertList)
	require.NoError(err)
	insertIDs := res.InsertedIDs
	defer func() {
		_, err = col.DeleteMany(ctx, bson.M{"user_id": userIDNoNoble})
		assert.NoError(err)
	}()
	require.NoError(cleanupMedal())
	cleanupSuccess := false
	for i := 0; i < 10; i++ {
		<-time.After(time.Second)
		id1, ok := insertIDs[len(insertIDs)-1].(primitive.ObjectID)
		require.True(ok)
		id2, ok := insertIDs[len(insertIDs)-2].(primitive.ObjectID)
		require.True(ok)
		idExceptedDeleted := []primitive.ObjectID{id1, id2}
		count, err := col.CountDocuments(ctx,
			bson.M{"_id": bson.M{"$in": idExceptedDeleted}})
		require.NoError(err)
		if count == 0 {
			cleanupSuccess = true
			break
		}
	}
	require.True(cleanupSuccess)
	count, err := col.CountDocuments(ctx,
		bson.M{"user_id": userIDNoNoble, "status": bson.M{"$gt": livemedal.StatusPending}})
	require.NoError(err)
	assert.Equal(livemedal.UserMaxMedalCount()+1, count)
	count, err = col.CountDocuments(ctx,
		bson.M{"user_id": userIDNoNoble, "status": livemedal.StatusPending})
	require.NoError(err)
	// status 为 pending 的勋章还在
	assert.Equal(int64(2), count)
	r1, err := col.Find(ctx, bson.M{"user_id": userIDNoNoble})
	require.NoError(err)
	var result []livemedal.LiveMedal
	err = r1.All(ctx, &result)
	// 新增了一个超粉的粉丝勋章，两个 status 为 pending 的勋章
	assert.Equal(livemedal.UserMaxMedalCount()+3, int64(len(result)))
	// 验证超粉勋章未被删除掉
	foundSuperMedal := false
	for _, v := range result {
		if v.Point == 10 && v.Simple.Mini.SuperFan != nil {
			foundSuperMedal = true
		}
	}
	assert.True(foundSuperMedal)
}

func TestArrayGroup(t *testing.T) {
	assert := assert.New(t)

	array := []userIDCount{{
		Count: int64(0),
	}}
	result := arrayGroup(array, 3)
	assert.Equal(1, len(result))
	assert.Equal([][]userIDCount{{{
		Count: int64(0),
	}}}, result)

	count := 6
	for i := 1; i < count; i++ {
		array = append(array, userIDCount{
			Count: int64(i),
		})
	}
	result = arrayGroup(array, 3)
	assert.Equal(2, len(result))
	assert.Equal([]userIDCount{{Count: int64(0)}, {Count: int64(1)}, {Count: int64(2)}}, result[0])
	assert.Equal([]userIDCount{{Count: int64(3)}, {Count: int64(4)}, {Count: int64(5)}}, result[1])
	array = append(array, userIDCount{
		Count: int64(count),
	})
	result = arrayGroup(array, 3)
	assert.Equal(3, len(result))
	assert.Equal([]userIDCount{{Count: int64(6)}}, result[2])
}

func TestGetUserMaxMedalCount(t *testing.T) {
	assert := assert.New(t)

	mNoble := map[int64]*vip.UserInfo{
		3456835: {
			LiveNoble: &vip.Info{
				Type:     1,
				Level:    1,
				MedalNum: 50,
			},
		},
	}
	assert.EqualValues(50, getUserMaxMedalCount(mNoble, 3456835))
	assert.Equal(livemedal.UserMaxMedalCount(), getUserMaxMedalCount(mNoble, 195002))
}

func TestMedalPointDecline(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	beginOfDay := util.BeginningOfDay(goutil.TimeNow())

	insertList := make([]interface{}, 0)

	updatedTime := goutil.TimeNow().Add(-time.Hour)
	medal1 := livemedal.LiveMedal{ // 该勋章会因为亲密度会衰减到非正值而被删除
		CreatedTime: beginOfDay.AddDate(0, 0, -9),
		Simple: livemedal.Simple{
			Point:  10,
			Status: livemedal.StatusOwned,
		},
		TUpdatedTime: beginOfDay.AddDate(0, 0, -8),
	}
	medal2 := livemedal.LiveMedal{ // 该勋章的亲密度会减去指定值
		CreatedTime: beginOfDay.AddDate(0, 0, -10),
		Simple: livemedal.Simple{
			Point:  100,
			Status: livemedal.StatusOwned,
		},
		TUpdatedTime: beginOfDay.AddDate(0, 0, -8),
		UpdatedTime:  updatedTime,
	}
	medal3 := livemedal.LiveMedal{ // 该勋章的亲密度会按比例衰减
		CreatedTime: beginOfDay.AddDate(0, 0, -11),
		Simple: livemedal.Simple{
			Point:  5000000,
			Status: livemedal.StatusOwned,
		},
		TUpdatedTime: beginOfDay.AddDate(0, 0, -8),
		UpdatedTime:  updatedTime,
	}
	medal4 := livemedal.LiveMedal{ // 该勋章的亲密度不会衰减
		CreatedTime: beginOfDay.AddDate(0, 0, -12),
		Simple: livemedal.Simple{
			Point:  10,
			Status: livemedal.StatusOwned,
		},
		TUpdatedTime: beginOfDay.AddDate(0, 0, -5),
		UpdatedTime:  updatedTime,
	}
	medal5 := livemedal.LiveMedal{ // 该勋章的是超粉勋章，亲密度的值会衰减到 1
		CreatedTime: beginOfDay.AddDate(0, 0, -9),
		Simple: livemedal.Simple{
			Point:  10,
			Status: livemedal.StatusOwned,
			Mini: livemedal.Mini{
				Name:     "test",
				SuperFan: &livemedal.SuperFan{ExpireTime: goutil.TimeNow().Add(time.Minute).Unix()},
			},
		},
		TUpdatedTime: beginOfDay.AddDate(0, 0, -8),
		UpdatedTime:  updatedTime,
	}
	insertList = append(insertList, medal1, medal2, medal3, medal4, medal5)
	col := livemedal.Collection()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	res, err := col.InsertMany(ctx, insertList)
	require.NoError(err)
	insertIDs := res.InsertedIDs
	require.NoError(medalPointDecline())
	err = col.FindOne(ctx, bson.M{"_id": insertIDs[0]}).Err()
	assert.Equal(mongo.ErrNoDocuments, err)
	filter := bson.M{
		"_id": bson.M{"$in": insertIDs[1:]},
	}
	var liveMedal []struct {
		OID          primitive.ObjectID `bson:"_id,omitempty"`
		TReducePoint *int64             `bson:"t_reduce_point"`
		Point        int64              `bson:"point"`
		UpdatedTime  time.Time          `bson:"updated_time"`
	}
	cur, err := col.Find(ctx, filter, options.Find().SetSort(bson.M{"create_time": -1}))
	require.NoError(err)
	require.NoError(cur.All(ctx, &liveMedal))
	defer require.NoError(cur.Close(ctx))
	assert.Equal(insertIDs[1].(primitive.ObjectID), liveMedal[0].OID)
	assert.Equal(int64(50), liveMedal[0].Point)
	assert.Equal(int64(-50), *(liveMedal[0].TReducePoint))
	assert.Greater(liveMedal[0].UpdatedTime.Unix(), updatedTime.Unix())
	assert.Equal(insertIDs[2].(primitive.ObjectID), liveMedal[1].OID)
	assert.Equal(int64(4875000), liveMedal[1].Point)
	assert.Equal(-int64(125000), *(liveMedal[1].TReducePoint))
	assert.Greater(liveMedal[1].UpdatedTime.Unix(), updatedTime.Unix())
	assert.Equal(insertIDs[3].(primitive.ObjectID), liveMedal[2].OID)
	assert.Equal(int64(10), liveMedal[2].Point)
	assert.Nil(liveMedal[2].TReducePoint)
	assert.Equal(liveMedal[2].UpdatedTime.Unix(), updatedTime.Unix())
	assert.Equal(insertIDs[4].(primitive.ObjectID), liveMedal[3].OID)
	assert.Equal(int64(1), liveMedal[3].Point)
	assert.Equal(-int64(9), *(liveMedal[3].TReducePoint))
	assert.Greater(liveMedal[3].UpdatedTime.Unix(), updatedTime.Unix())
	_, err = col.DeleteMany(ctx, bson.M{"_id": bson.M{"$in": insertIDs[1:]}})
	require.NoError(err)
}

func TestSendMedalSystemNotify(t *testing.T) {
	assert := assert.New(t)

	var msgCount int
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		body := input.(map[string]interface{})["systemmsgs"].([]pushservice.SystemMsg)
		msgCount = len(body)

		testContent := "您在bless直播间的粉丝勋章亲密度将从今天开始下降，若今日重新投喂礼物给主播，系统将免费返还今日下降的亲密度哦~ " +
			"<a href=\"https://fm.uat.missevan.com/live/123\">点击此处前往直播间</a>"
		assert.Equal(testContent, body[0].Content)

		testContent = "您在bless直播间的粉丝勋章亲密度已于 2023-09-25 12:00 下降到 0，勋章已失去，如需再次获取，可以重新对主播投喂哦~ " +
			"<a href=\"https://fm.uat.missevan.com/live/789\">点击此处前往直播间</a>"
		assert.Equal(testContent, body[2].Content)

		return "success", nil
	})
	defer cancel()

	sendMedalSystemNotify(nil, nil)
	assert.Zero(msgCount)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 9, 25, 12, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	beginDecreaseMedals := []*livemedal.LiveMedal{
		{
			Simple: livemedal.Simple{
				CreatorID: 10,
				UserID:    12,
				RoomID:    123,
			},
		},
		{
			Simple: livemedal.Simple{
				CreatorID: 12,
				UserID:    10,
				RoomID:    456,
			},
		},
	}
	deletedMedals := []*livemedal.LiveMedal{
		{
			Simple: livemedal.Simple{
				CreatorID: 10,
				UserID:    1,
				RoomID:    789,
			},
		},
	}
	sendMedalSystemNotify(beginDecreaseMedals, deletedMedals)
	sendMedalSystemNotify(nil, nil)
	assert.Equal(3, msgCount)
}

func TestActionCronNotifyMonthlyRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 初始设定为每月最后一天 23 点
	now := goutil.TimeNow().AddDate(0, -2, 0)
	goutil.SetTimeNow(func() time.Time {
		return util.EndOfMonth(now).Add(-time.Hour)
	})
	defer goutil.SetTimeNow(nil)

	// 传入值为空的情况
	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	_, err := ActionCronNotifyMonthlyRank(c)
	assert.Equal(actionerrors.ErrParams, err)

	param := notifyMonthlyParam{
		NotifyType: notifyTypeBeforeEndOfMonth,
	}

	param.NotifyType = 999
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	_, err = ActionCronNotifyMonthlyRank(c)
	assert.Equal(actionerrors.ErrParams, err)

	cancel := mrpc.SetMock("im://broadcast/many", func(input interface{}) (output interface{}, err error) {
		var body []*userapi.BroadcastElem
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		require.NotEmpty(body)
		notify := body[0]
		require.NotNil(notify)
		assert.Equal(liveim.IMMessageTypeAll, notify.Type)
		assert.NotNil(notify.Payload)
		return "success", nil
	})
	defer cancel()

	// 正常流程
	// 默认执行 结束前通知
	param = notifyMonthlyParam{}
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	_, err = ActionCronNotifyMonthlyRank(c)
	require.NoError(err)

	// 设定为每月第一天 0 点
	goutil.SetTimeNow(func() time.Time {
		return util.EndOfMonth(now)
	})
	defer goutil.SetTimeNow(nil)

	// 默认执行 结束后通知
	param = notifyMonthlyParam{}
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	_, err = ActionCronNotifyMonthlyRank(c)
	require.NoError(err)

	// 手动传参 结束前通知
	param = notifyMonthlyParam{}
	param.NotifyType = notifyTypeBeforeEndOfMonth
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	_, err = ActionCronNotifyMonthlyRank(c)
	require.NoError(err)

	// 插入测试数据
	testData := []*redis.Z{
		{Score: 10, Member: 516},
		{Score: 11, Member: 3013063},
		{Score: 12, Member: 3387502},
	}

	testTop1UserID := int64(testData[2].Member.(int))
	r, err := room.FindOneSimple(bson.M{"creator_id": testTop1UserID})
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(testTop1UserID, r.CreatorID)

	key := usersrank.Key(usersrank.TypeMonth, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, testData...).Err())
	require.NoError(service.Redis.Expire(key, 10*time.Second).Err())
	goutil.SetTimeNow(nil)

	// 手动传参 结束后通知
	param = notifyMonthlyParam{}
	param.NotifyType = notifyTypeAfterEndOfMonth
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	_, err = ActionCronNotifyMonthlyRank(c)
	require.NoError(err)

	// 无需执行的情况
	goutil.SetTimeNow(func() time.Time {
		return util.EndOfMonth(now).AddDate(0, 0, -2)
	})
	defer goutil.SetTimeNow(nil)

	param = notifyMonthlyParam{}
	param.NotifyType = 0
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	_, err = ActionCronNotifyMonthlyRank(c)
	require.NoError(err)
}

func TestCheckRunTime(t *testing.T) {
	t.Run("ShouldBeBeforeEndOfMonth", func(t *testing.T) {
		assert := assert.New(t)

		now := goutil.TimeNow()
		goutil.SetTimeNow(func() time.Time {
			return util.EndOfMonth(now).Add(-time.Hour)
		})
		defer goutil.SetTimeNow(nil)

		var param notifyMonthlyParam
		param.checkRunTime()
		assert.Equal(notifyTypeBeforeEndOfMonth, param.NotifyType)
	})

	t.Run("ShouldBeAfterEndOfMonth", func(t *testing.T) {
		assert := assert.New(t)

		now := goutil.TimeNow()
		goutil.SetTimeNow(func() time.Time {
			return util.EndOfMonth(now)
		})
		defer goutil.SetTimeNow(nil)

		var param notifyMonthlyParam
		param.checkRunTime()
		assert.Equal(notifyTypeAfterEndOfMonth, param.NotifyType)
	})

	t.Run("ShouldBeZero", func(t *testing.T) {
		assert := assert.New(t)

		now := goutil.TimeNow()
		goutil.SetTimeNow(func() time.Time {
			return util.BeginningOfMonth(now).Add(2 * time.Hour)
		})
		defer goutil.SetTimeNow(nil)

		var param notifyMonthlyParam
		param.checkRunTime()
		assert.Zero(param.NotifyType)
	})
}

func TestNotifyMonthlyNotifyBeforeAndAfterEnd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow().AddDate(0, -2, 0)
	goutil.SetTimeNow(func() time.Time {
		return util.EndOfMonth(now)
	})
	defer goutil.SetTimeNow(nil)

	// 结束前通知
	param := notifyMonthlyParam{
		NotifyType: notifyTypeBeforeEndOfMonth,
	}

	cancel := mrpc.SetMock("im://broadcast/many", func(input interface{}) (output interface{}, err error) {
		var elems []userapi.BroadcastElem
		err = json.Unmarshal(input.(json.RawMessage), &elems)
		require.NoError(err)
		require.GreaterOrEqual(len(elems), 1)
		notify := elems[0]
		require.NotNil(notify)
		assert.Equal(liveim.IMMessageTypeAll, notify.Type)
		assert.NotNil(notify.Payload)
		return "success", nil
	})
	defer cancel()

	err := param.notifyBeforeEnd()
	require.NoError(err)
	require.Len(param.notifyQueue, 1)

	message := `<font color="${normal_color}">距本月</font>` +
		`<font color="${highlight_color}">巅峰月榜</font>` +
		`<font color="${normal_color}">统计截止仅剩 60 分钟</font>`
	replaces := map[string]string{
		"normal_color":    "#FFFFFF",
		"highlight_color": "#917AE3",
	}
	formattedStr := param.notifyQueue[0].Payload.(*notifymessages.General).Message
	assert.Equal(goutil.FormatMessage(message, replaces), formattedStr)
	assert.NotContains(formattedStr, "normal_color")
	assert.Contains(formattedStr, replaces["normal_color"])
	assert.NotContains(formattedStr, "highlight_color")
	assert.Contains(formattedStr, replaces["highlight_color"])
	assert.NotContains(formattedStr, "creator_username")

	// 结束后通知
	// 无榜单情况
	param.NotifyType = notifyTypeAfterEndOfMonth
	err = param.notiftAfterEnd()
	require.NoError(err)

	// 删除测试数据
	key := usersrank.Key(usersrank.TypeMonth, now)
	require.NoError(service.Redis.Del(key).Err())

	testData := []*redis.Z{
		{Score: 10, Member: 99999999},
	}
	require.NoError(service.Redis.ZAdd(key, testData...).Err())
	require.NoError(service.Redis.Expire(key, 5*time.Second).Err())
	err = param.notiftAfterEnd()
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 插入测试数据
	testData = []*redis.Z{
		{Score: 10, Member: 516},
		{Score: 11, Member: 3013063},
		{Score: 12, Member: 3387502},
	}

	testTop1UserID := int64(testData[2].Member.(int))
	r, err := room.FindOne(bson.M{"creator_id": testTop1UserID}, &room.FindOptions{FindCreator: true})
	require.NoError(err)
	require.NotNil(r)
	assert.Equal(testTop1UserID, r.CreatorID)
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(key, testData...).Err())
	require.NoError(service.Redis.Expire(key, 5*time.Second).Err())

	// 正常情况
	param.NotifyType = notifyTypeAfterEndOfMonth
	err = param.notiftAfterEnd()
	require.NoError(err)
	require.Len(param.notifyQueue, 2)
	lastMonthNumber := int(now.Month())

	message = `<font color="${normal_color}">恭喜</font> ` +
		`<font color="${highlight_color}">${creator_username}</font> ` +
		`<font color="${normal_color}">荣登 ${month} 月</font>` +
		`<font color="${highlight_color}">巅峰月榜 TOP1！</font>`
	replaces = map[string]string{
		"normal_color":     "#FFFFFF",
		"highlight_color":  "#917AE3",
		"creator_username": html.EscapeString(r.CreatorUsername),
		"month":            strconv.Itoa(lastMonthNumber),
	}
	general := param.notifyQueue[0].Payload.(*notifymessages.General)
	formattedStr = general.Message
	assert.Equal(goutil.FormatMessage(message, replaces), formattedStr)
	assert.NotContains(formattedStr, "normal_color")
	assert.Contains(formattedStr, replaces["normal_color"])
	assert.NotContains(formattedStr, "highlight_color")
	assert.Contains(formattedStr, replaces["highlight_color"])
	assert.NotContains(formattedStr, "creator_username")
	assert.Contains(formattedStr, r.CreatorUsername)
	assert.NotContains(formattedStr, "month")
	assert.Contains(formattedStr, strconv.Itoa(lastMonthNumber))
	assert.NotEmpty(general.Effect.EffectURL)
	assert.NotEmpty(general.Effect.WebEffectURL)
	assert.Equal(1, general.EffectShow)

	roomEffect := param.notifyQueue[1].Payload.(*liveim.RoomEffect)
	assert.NotEmpty(roomEffect.Effect.EffectURL)
	assert.NotEmpty(roomEffect.Effect.WebEffectURL)
}

func TestActionCronLastHourTop3Notify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := mrpc.SetMock(userapi.URIIMBroadcastMany, func(i interface{}) (interface{}, error) {
		type body struct {
			RoomID  int64        `json:"room_id"`
			Payload NotifyAttach `json:"payload"`
		}
		var elems []body
		err := json.Unmarshal(i.(json.RawMessage), &elems)
		require.NoError(err)
		require.Equal(4, len(elems), "len(elems)")
		na := elems[0].Payload
		assert.Equal(na.Type, liveim.TypeNotify)
		na = elems[1].Payload
		require.Equal(na.Type, liveim.TypeCreator)
		assert.NotEmpty(na.FrameURL)
		return "success", nil
	})
	defer cancel()
	c := handler.NewRPCTestContext("", nil)
	require.NoError(prepareTestData())
	resp, err := ActionCronLastHourTop3Notify(c)
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestLastHourStartAndEnd(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 10, 18, 17, 41, 22, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	s, e := lastHourStartAndEnd()
	assert.Equal(time.Date(2021, 10, 18, 16, 0, 00, 0, time.Local), s)
	assert.Equal(time.Date(2021, 10, 18, 17, 0, 00, 0, time.Local), e)
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 10, 18, 00, 41, 22, 0, time.Local)
	})
	s, e = lastHourStartAndEnd()
	assert.Equal(time.Date(2021, 10, 17, 23, 0, 00, 0, time.Local), s)
	assert.Equal(time.Date(2021, 10, 18, 00, 0, 00, 0, time.Local), e)
}

func TestLastHourTop3Rooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := userstatus.KeyRankInvisible(347142109)
	userIDs := []int64{14}
	v, err := json.Marshal(userIDs)
	require.NoError(err)
	require.NoError(service.LRURedis.Del(key).Err())
	require.NoError(service.LRURedis.Set(key, string(v), 10*time.Minute).Err())
	require.NoError(prepareTestData())
	r, err := lastHourTop3Rooms()
	require.NoError(err)
	require.Len(r, 3)
	assert.Equal(1, r[0].Rank)
	assert.Equal(int64(25), r[0].Revenue)
	require.Len(r[0].Top3Users, 3)
	assert.Equal(int64(12), r[0].Top3Users[0].UserID)
	assert.Equal("零月", r[0].Top3Users[0].Username)
	assert.Equal(int64(0), r[0].Top3Users[2].UserID)
	assert.Equal("神秘人", r[0].Top3Users[2].Username)
	assert.Equal(2, r[1].Rank)
	assert.Equal(int64(10244), r[1].RoomID)
	require.Len(r[1].Top3Users, 3)
	assert.Equal(int64(101), r[1].Top3Users[0].UserID)
	assert.Equal("零月101", r[1].Top3Users[0].Username)
	assert.Equal(3, r[2].Rank)
	require.Len(r[2].Top3Users, 3)
	assert.Equal(int64(5), r[2].Room.CreatorID)
}

func TestLastHourRankTop3Users(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomID := int64(234)
	key := roomsrank.Key(roomID, roomsrank.RankTypeHourly, goutil.TimeNow().Add((-time.Hour)))
	require.NoError(service.Redis.Del(key).Err())
	require.NoError(service.Redis.ZAdd(
		key,
		&redis.Z{Score: 1, Member: 1},
		&redis.Z{Score: 2, Member: 2},
		&redis.Z{Score: 3, Member: 3},
		&redis.Z{Score: 4, Member: 4},
	).Err())
	info, err := lastHourRankTop3Users(roomID)
	require.NoError(err)
	require.Len(info, 3)
	assert.Equal(UserRankInfo{
		Rank:    1,
		UserID:  4,
		Revenue: 4,
	}, *info[0])
	assert.Equal(UserRankInfo{
		Rank:    2,
		UserID:  3,
		Revenue: 3,
	}, *info[1])
	assert.Equal(UserRankInfo{
		Rank:    3,
		UserID:  2,
		Revenue: 2,
	}, *info[2])
}

func prepareTestData() error {
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow().Add(-time.Hour))
	if err := service.Redis.Del(key).Err(); err != nil {
		return err
	}
	creatorID1 := int64(9075623)
	roomID1 := int64(347142109)
	creatorID2 := int64(2233)
	roomID2 := int64(10244)
	creatorID3 := int64(5)
	roomID3 := int64(24113499)
	if err := service.Redis.ZAdd(
		key, &redis.Z{Score: float64(22), Member: 5010},
		&redis.Z{Score: float64(23), Member: creatorID3},
		&redis.Z{Score: float64(24), Member: creatorID2},
		&redis.Z{Score: float64(25), Member: creatorID1},
	).Err(); err != nil {
		return err
	}
	roomID1Key := roomsrank.Key(roomID1, roomsrank.RankTypeHourly, goutil.TimeNow().Add((-time.Hour)))
	if err := service.Redis.Del(roomID1Key).Err(); err != nil {
		return err
	}
	if err := service.Redis.ZAdd(
		roomID1Key,
		&redis.Z{Score: 19, Member: 12},
		&redis.Z{Score: 18, Member: 13},
		&redis.Z{Score: 17, Member: 14},
		&redis.Z{Score: 16, Member: 15},
	).Err(); err != nil {
		return err
	}
	roomID2Key := roomsrank.Key(roomID2, roomsrank.RankTypeHourly, goutil.TimeNow().Add((-time.Hour)))
	if err := service.Redis.Del(roomID2Key).Err(); err != nil {
		return err
	}
	if err := service.Redis.ZAdd(
		roomID2Key,
		&redis.Z{Score: 27, Member: 101},
		&redis.Z{Score: 26, Member: 102},
		&redis.Z{Score: 25, Member: 103},
		&redis.Z{Score: 24, Member: 104},
	).Err(); err != nil {
		return err
	}
	roomID3Key := roomsrank.Key(roomID3, roomsrank.RankTypeHourly, goutil.TimeNow().Add((-time.Hour)))
	if err := service.Redis.Del(roomID3Key).Err(); err != nil {
		return err
	}
	if err := service.Redis.ZAdd(
		roomID3Key,
		&redis.Z{Score: 35, Member: 104},
		&redis.Z{Score: 34, Member: 105},
		&redis.Z{Score: 33, Member: 12},
	).Err(); err != nil {
		return err
	}
	return nil
}

func TestActionCronRankNova(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	_, err := service.Redis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		for i := 0; i < usersrank.KeyUsersNovaSlotNum; i++ {
			key := usersrank.NovaKey(now, i)
			pipeliner.Del(key)
		}
		return nil
	})
	require.NoError(err)

	c := handler.NewRPCTestContext("", nil)
	resp, err := ActionCronRankNova(c)
	require.NoError(err)
	assert.Equal("success", resp)

	key := usersrank.NovaKey(now, 0)
	exists, err := service.Redis.Exists(key).Result()
	require.NoError(err)
	assert.NotZero(exists)
}
