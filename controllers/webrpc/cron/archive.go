package cron

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/quests"
	"github.com/MiaoSiLa/live-service/models/mysql/archiveliverankhour"
	"github.com/MiaoSiLa/live-service/models/mysql/archiveliverankmonth"
	"github.com/MiaoSiLa/live-service/models/mysql/archiveliveranknova"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionCronRankArchiveMonth 归档主播月榜数据
// 运行周期：10 0 0 1 * *
/**
 * @api {post} /rpc/cron/rank/archive/month 归档主播月榜数据
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "归档成功"
 *     }
 */
func ActionCronRankArchiveMonth(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	when := now.AddDate(0, -1, 0)
	usersRank, err := usersrank.FindRank(when, usersrank.TypeMonth)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(usersRank) == 0 {
		return "主播榜月榜为空", nil
	}
	creatorIDs := make([]int64, 0, len(usersRank))
	for _, v := range usersRank {
		creatorIDs = append(creatorIDs, v.UserID)
	}
	rooms, err := room.List(
		bson.M{"creator_id": bson.M{"$in": creatorIDs}}, nil,
		&room.FindOptions{DisableAll: true},
	)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	roomIDs := make([]int64, 0, len(rooms))
	for _, room := range rooms {
		roomIDs = append(roomIDs, room.RoomID)
	}
	roomsRank, err := roomsrank.FindRankMapByRoomIDs(when, roomsrank.RankTypeMonth, roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	month := when.Format(util.TimeFormatYM)
	records := make([]archiveliverankmonth.ArchiveLiveRankMonth, 0,
		usersrank.RankLen(usersrank.TypeMonth)+int64(len(roomsRank))*roomsrank.RankLen(roomsrank.RankTypeMonth))
	usersRankMap := goutil.ToMap(usersRank, "UserID").(map[int64]usersrank.Info)
	for _, room := range rooms {
		userRank, ok := usersRankMap[room.CreatorID]
		if !ok {
			return nil, actionerrors.ErrNotFound("usersRank not found")
		}
		records = append(records, archiveliverankmonth.ArchiveLiveRankMonth{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			Type:         archiveliverankmonth.TypeCreatorsRank,
			Month:        month,
			RoomID:       room.RoomID,
			CreatorID:    room.CreatorID,
			Revenue:      userRank.Revenue,
			Rank:         int(userRank.Rank),
		})
		roomRank, ok := roomsRank[room.RoomID]
		if !ok {
			logger.WithField("room_id", room.RoomID).Error("roomRank not found")
			continue
		}
		for _, v := range roomRank {
			records = append(records, archiveliverankmonth.ArchiveLiveRankMonth{
				CreateTime:   now.Unix(),
				ModifiedTime: now.Unix(),
				Type:         archiveliverankmonth.TypeRoomUsersRank,
				Month:        month,
				RoomID:       room.RoomID,
				CreatorID:    room.CreatorID,
				UserID:       v.ID,
				Revenue:      v.Revenue,
				Rank:         int(v.Rank),
			})
		}
	}
	err = archiveliverankmonth.BatchInsert(records)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "归档成功", nil
}

// ActionCronRankArchiveHour 归档主播小时榜数据
// 运行周期：10 0 * * * *
/**
 * @api {post} /rpc/cron/rank/archive/hour 归档主播小时榜数据
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "归档成功"
 *     }
 */
func ActionCronRankArchiveHour(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	when := now.Add(-time.Hour)
	usersRank, err := usersrank.FindRank(when, usersrank.TypeHour)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(usersRank) == 0 {
		return "主播榜小时榜为空", nil
	}
	creatorIDs := make([]int64, 0, len(usersRank))
	for _, v := range usersRank {
		creatorIDs = append(creatorIDs, v.UserID)
	}
	rooms, err := room.List(
		bson.M{"creator_id": bson.M{"$in": creatorIDs}}, nil,
		&room.FindOptions{DisableAll: true},
	)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	rankTime := time.Date(when.Year(), when.Month(), when.Day(), when.Hour(), 0, 0, 0, time.Local).
		Format(util.TimeFormatYMDHMS)
	roomMap := goutil.ToMap(rooms, "CreatorID").(map[int64]*room.Room)
	roomIDs := make([]int64, 0, len(rooms))
	for i := range rooms {
		roomIDs = append(roomIDs, rooms[i].RoomID)
	}
	roomsRankMap, err := roomsrank.FindRankMapByRoomIDs(when, roomsrank.RankTypeHourly, roomIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	maxRoomsUsersCount := len(roomsRankMap) * int(roomsrank.RankLen(roomsrank.RankTypeHourly))
	records := make([]archiveliverankhour.ArchiveLiveRankHour, 0, len(usersRank)+maxRoomsUsersCount)
	for i, rank := range usersRank {
		room, ok := roomMap[rank.UserID]
		if !ok {
			logger.WithField("creator_id", rank.UserID).Error("room not found")
			// PASS
			continue
		}
		usersRank[i].RoomID = room.RoomID
		records = append(records, archiveliverankhour.ArchiveLiveRankHour{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			Type:         archiveliverankhour.TypeCreatorsRank,
			RankTime:     rankTime,
			RoomID:       room.RoomID,
			CreatorID:    rank.UserID,
			Score:        rank.Revenue,
			Rank:         int(rank.Rank),
		})
		roomUsersRank, ok := roomsRankMap[room.RoomID]
		if !ok {
			logger.WithField("room_id", room.RoomID).Error("room user not found")
			// PASS
			continue
		}
		usersRank[i].RoomUsersRank = roomUsersRank
		for _, roomUserRank := range roomUsersRank {
			records = append(records, archiveliverankhour.ArchiveLiveRankHour{
				CreateTime:   now.Unix(),
				ModifiedTime: now.Unix(),
				Type:         archiveliverankhour.TypeRoomUsersRank,
				RankTime:     rankTime,
				RoomID:       room.RoomID,
				CreatorID:    room.CreatorID,
				UserID:       roomUserRank.ID,
				Score:        roomUserRank.Revenue,
				Rank:         int(roomUserRank.Rank),
			})
		}
	}
	err = archiveliverankhour.BatchInsert(records)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 完成小时榜任务
	finishTopQuests(usersRank, now)

	return "归档成功", nil
}

func finishTopQuests(userRank []usersrank.Info, startTime time.Time) {
	if len(userRank) == 0 {
		return
	}

	qs, err := quests.FindHourQuests(startTime)
	if err != nil {
		logger.Error(err)
		return
	}
	if len(qs) == 0 {
		return
	}
	userRankMap := util.ToMap(userRank, func(ur usersrank.Info) int {
		return int(ur.Rank)
	})
	for _, q := range qs {
		ur, ok := userRankMap[q.Rank]
		if !ok {
			continue
		}
		q.FinishHourTop(ur.RoomID, ur.UserID, ur.RoomUsersRank)
	}
}

// ActionCronRankArchiveNova 归档昨日主播新人榜数据
// 运行周期：0 10 0 * * *
/**
 * @api {post} /rpc/cron/rank/archive/nova 归档主播新人榜数据
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "归档成功"
 *     }
 */
func ActionCronRankArchiveNova(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	when := now.AddDate(0, 0, -1)
	usersRank, err := usersrank.FindRank(when, usersrank.TypeNova)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(usersRank) == 0 {
		return "主播新人榜为空", nil
	}
	creatorIDs := make([]int64, 0, len(usersRank))
	for _, v := range usersRank {
		creatorIDs = append(creatorIDs, v.UserID)
	}
	rooms, err := room.List(
		bson.M{"creator_id": bson.M{"$in": creatorIDs}}, nil,
		&room.FindOptions{DisableAll: true},
	)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	rankTime := util.BeginningOfDay(when).Format(util.TimeFormatYMD)
	roomMap := goutil.ToMap(rooms, "CreatorID").(map[int64]*room.Room)
	records := make([]archiveliveranknova.ArchiveLiveRankNova, 0, len(usersRank))
	for _, rank := range usersRank {
		room, ok := roomMap[rank.UserID]
		if !ok {
			logger.WithField("creator_id", rank.UserID).Error("room not found")
			// PASS
			continue
		}
		records = append(records, archiveliveranknova.ArchiveLiveRankNova{
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
			RankTime:     rankTime,
			RoomID:       room.RoomID,
			CreatorID:    rank.UserID,
			Score:        rank.Revenue,
			Rank:         int(rank.Rank),
		})
	}
	err = archiveliveranknova.BatchInsert(records)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return "归档成功", nil
}
