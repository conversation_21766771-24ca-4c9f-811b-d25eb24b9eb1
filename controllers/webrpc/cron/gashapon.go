package cron

import (
	"html"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	defaultColor = "#FFFFFF"
)

type gashaponRankReward struct {
	top3CreatorIDs []int64
	conf           params.Gashapon
}

// ActionCronGashaponWeeklyReward 盲盒周榜奖励发放
// 运行周期：5 0 0 ? * 1
/**
 * @api {post} /rpc/cron/gashapon/rank/weekly/reward 盲盒周榜奖励发放
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "success",
 *       "data": nil
 *     }
 */
func ActionCronGashaponWeeklyReward(c *handler.Context) (handler.ActionResponse, string, error) {
	lastWeek := goutil.TimeNow().AddDate(0, 0, -7)
	key := usersrank.Key(usersrank.TypeGashaponWeek, lastWeek)
	rankList, err := service.Redis.ZRevRange(key, 0, 2).Result()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	creatorIDs := make([]int64, 0, len(rankList))
	for _, v := range rankList {
		creatorID, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, "", actionerrors.NewErrServerInternal(err, nil)
		}
		creatorIDs = append(creatorIDs, creatorID)
	}
	if len(creatorIDs) == 0 {
		return nil, "empty weekly rank", nil
	}
	param, err := params.FindGashapon()
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.RewardLiveIconURLs) != 3 {
		return nil, "", actionerrors.ErrNotFound("reward live icon url not enough")
	}

	rewardParam := gashaponRankReward{
		top3CreatorIDs: creatorIDs,
		conf:           param,
	}
	rewardParam.rewardTop3()
	err = rewardParam.broadcastTop1()
	if err != nil {
		return nil, "", err
	}
	return nil, "success", nil
}

func (param *gashaponRankReward) rewardTop3() {
	var (
		now        = goutil.TimeNow()
		startTime  = now.Unix()
		expireTime = now.AddDate(0, 0, 7).Unix()
	)

	roomsMap, err := room.FindSimpleMapByCreatorID(param.top3CreatorIDs, &room.FindOptions{DisableAll: true})
	if err != nil {
		logger.WithField("creator_ids", param.top3CreatorIDs).Error(err)
		return
	}

	icons := make([]*liverecommendedelements.LiveRecommendedElements, 0, len(param.top3CreatorIDs))
	for i := range param.top3CreatorIDs {
		r := roomsMap[param.top3CreatorIDs[i]]
		if r == nil {
			logger.WithField("creator_id", param.top3CreatorIDs[i]).Error("room not found")
			continue
		}
		icons = append(icons, &liverecommendedelements.LiveRecommendedElements{
			Sort:         1,
			ElementID:    r.RoomID,
			ElementType:  liverecommendedelements.ElementLiveIcon,
			URL:          storage.ParseSchemeURL(param.conf.RewardLiveIconURLs[i]),
			StartTime:    &startTime,
			ExpireTime:   expireTime,
			CreateTime:   now.Unix(),
			ModifiedTime: now.Unix(),
		})
	}
	err = servicedb.BatchInsert(service.DB, liverecommendedelements.TableName(), icons)
	if err != nil {
		logger.WithField("creator_ids", param.top3CreatorIDs).Error(err)
		// PASS
	}
}

func (param *gashaponRankReward) broadcastTop1() error {
	r, err := room.FindOne(bson.M{"creator_id": param.top3CreatorIDs[0]}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}
	format := map[string]string{
		"gashapon_name":    html.EscapeString(param.conf.Name),
		"creator_username": html.EscapeString(r.CreatorUsername),
		"highlight_color":  defaultColor,
		"normal_color":     defaultColor,
	}
	b, err := bubble.FindSimple(param.conf.RewardBubbleID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if b != nil {
		if b.HighlightColor != "" {
			format["highlight_color"] = b.HighlightColor
		}
		if b.NormalColor != "" {
			format["normal_color"] = b.NormalColor
		}
	}
	message := goutil.FormatMessage(param.conf.RewardNotifyMessage, format)
	notify := notifymessages.NewGeneral(r.RoomID, message, b)
	err = userapi.BroadcastAll(notify)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
