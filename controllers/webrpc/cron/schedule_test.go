package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/liveschedulerecord"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRecommendScheduleRecord(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := time.Date(2021, 6, 17, 19, 59, 0, 0, time.Local)
	goutil.SetTimeNow(func() time.Time {
		return now
	})
	defer goutil.SetTimeNow(nil)

	c := handler.NewTestContext(http.MethodPost, "/", false, handler.M{})
	res, err := ActionRecommendScheduleRecord(c)
	require.NoError(err)
	assert.Equal("未到同步时间", res)

	c = handler.NewTestContext(http.MethodPost, "/", false, handler.M{"date": "2021-06-18"})
	_, err = ActionRecommendScheduleRecord(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext(http.MethodPost, "/", false, handler.M{"date": "2021-06-17"})
	_, err = ActionRecommendScheduleRecord(c)
	require.NoError(err)
}

func TestRecordLiveScheduleRecommend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	day := goutil.BeginningOfDay(now)
	err := recordLiveScheduleRecommend(day)
	require.NoError(err)
	var result []liveschedulerecord.ScheduleRecord
	require.NoError(liveschedulerecord.ScheduleRecord{}.DB().Where("", "day = ?", day.Unix()).Find(&result).Error)
	assert.GreaterOrEqual(len(result), 1)

	err = recordLiveScheduleRecommend(day)
	require.NoError(err, "重复添加")
	var result2 []liveschedulerecord.ScheduleRecord
	require.NoError(liveschedulerecord.ScheduleRecord{}.DB().Where("", "day = ?", day.Unix()).Find(&result2).Error)
	assert.Equal(len(result), len(result2))

	require.NoError(liverecommendedelements.TableSchedule(service.DB).Delete("", "expire_time > ? AND start_time < ?",
		now.Unix(), now.AddDate(0, 0, 1).Unix()).Error)
	err = recordLiveScheduleRecommend(day)
	require.NoError(err, "推荐为空")
	var result3 []liveschedulerecord.ScheduleRecord
	require.NoError(liveschedulerecord.ScheduleRecord{}.DB().Where("", "day = ?", day.Unix()).Find(&result2).Error)
	assert.Empty(result3)
}
