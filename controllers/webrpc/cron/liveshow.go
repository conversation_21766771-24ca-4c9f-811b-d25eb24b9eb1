package cron

/**
 * @api {post} /rpc/cron/liveshow/popup
 * @apiDescription 为防止上线小窗时，同时请求接口，导致 qps 过高的问题，客户端和 web 直播间需随机延迟 5s 请求时间，避免集中请求
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *     {
 *       "type": "room",
 *       "event": "widget_update",
 *       "room_id": 1,
 *       "popups": {
 *         "fold": true, // 是否默认折叠，true: 折叠; false: 展开。通过消息下发的折叠状态仅在之前没有小窗时生效
 *         "data": [{
 *           "mini_url": "http://fm.example.com/mini?webview=1", // 小的 webview 显示
 *           "image_url": "http://fm.example.com/image.png", // 表示 webview 加载完成前 / 加载错误显示的图
 *           "fold_image_url": "http://fm.example.com/fold_image?webview=1", // 小窗折叠图
 *           "full_url": "http://fm.example.com/full?webview=1", // full_url 和 open_url 互斥，分别代表半窗的 url 和在新页面打开的 url
 *           "open_url": "http://fm.example.com/open?webview=1" // full_url 和 open_url 互斥，同时只会返回一个字段
 *         }]
 *       },
 *       "events": [
 *         {
 *           "cover": "封面图",
 *           "url": "链接",
 *           "show_close": true // 是否可关闭
 *         }
 *       ]
 *     }
 *
 */
