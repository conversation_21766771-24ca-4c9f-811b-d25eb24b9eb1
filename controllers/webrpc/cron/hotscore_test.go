package cron

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestHotScoreTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.GORM)
	kc.Check(goodsHotScore{}, "seller_id", "score")

	kc = tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(hotScoreParam{}, "room_ids", "now_time")
	kc.Check(goodsHotScore{}, "room_id", "score")
	kc.Check(hotScoreGoodsResp{}, "data")
}

func TestActionHotScoreGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	api := "/cron/hotscore/goods"
	// 参数错误
	c := handler.NewRPCTestContext(api, handler.M{
		"room_ids": nil,
		"now_time": 0,
	})
	_, err := ActionHotScoreGoods(c)
	assert.Equal(actionerrors.ErrParams, err)
	// 模拟未开播的情况
	c = handler.NewRPCTestContext(api, handler.M{
		"room_ids": nil,
		"now_time": now.Unix(),
	})
	r, err := ActionHotScoreGoods(c)
	require.NoError(err)
	resp := r.(*hotScoreGoodsResp)
	require.Len(resp.Data, 0)

	rooms, err := room.ListSimples(bson.M{}, nil)
	require.NoError(err)
	require.GreaterOrEqual(len(rooms), 5)
	lPre := livetxnorder.LiveTxnOrder{
		CreateTime:   now.Unix() - 60,
		ModifiedTime: now.Unix() - 60,
		Price:        10,
		Status:       livetxnorder.StatusSuccess,
		More: &livetxnorder.MoreInfo{
			OpenStatus: util.NewInt(livetxnorder.OpenStatusOpen),
		},
	}
	prices := []int{10, 100, 1000, 5000, 10000}
	roomIDs := make([]int64, 5)
	ltos := make([]livetxnorder.LiveTxnOrder, 0, 7)
	// 添加超粉
	for i := 0; i < 5; i++ {
		roomIDs[i] = rooms[i].RoomID
		lPre.GoodsType = livegoods.GoodsTypeSuperFan
		lPre.Price = prices[i]
		lPre.SellerID = rooms[i].CreatorID
		ltos = append(ltos, lPre)
	}
	// 添加付费弹幕
	lPre.GoodsType = livegoods.GoodsTypeDanmaku
	lPre.Price = prices[1]
	lPre.SellerID = rooms[1].CreatorID
	ltos = append(ltos, lPre)
	require.NoError(servicedb.BatchInsert(livetxnorder.ADB(), livetxnorder.ADBTableName(), ltos))

	c = handler.NewRPCTestContext(api, handler.M{
		"room_ids": roomIDs,
		"now_time": now.Unix(),
	})
	r, err = ActionHotScoreGoods(c)
	require.NoError(err)
	resp = r.(*hotScoreGoodsResp)
	require.EqualValues(5, len(resp.Data))
	expected := map[int64]float64{
		roomIDs[0]: 9.0,
		roomIDs[1]: 94 + 94, // 超粉 + 弹幕
		roomIDs[2]: 950,
		roomIDs[3]: 0.96 * 5000,
		roomIDs[4]: 9700,
	}
	for _, v := range resp.Data {
		assert.Equal(expected[v.RoomID], v.Score)
	}
}
