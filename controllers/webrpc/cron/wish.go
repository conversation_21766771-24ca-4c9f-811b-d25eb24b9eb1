package cron

import (
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/wishes"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// TODO: 后续活动应该会每次都要变动，考虑将这两个参数作为接口参数传入或者从数据库中读取
// 活动 ID 和气泡 ID
const (
	bubbleIDWishes = 78901
	eventIDWishes  = 789
)

type drawParams struct {
	EventID int64 `json:"event_id"` // 活动 ID
	Period  int64 `json:"period"`   // 开奖周期（单位：秒），默认 15min

	nowTime          int64                         // 当前时间（单位：秒）
	draw             bool                          // 是否进行开奖
	startTime        int64                         // 本轮许愿的开始时间（单位：秒）
	endTime          int64                         // 本轮许愿的结束时间（单位：秒）
	wishesList       []*wishes.Wish                // 本轮许愿记录
	liveGoodsMap     map[int64]livegoods.LiveGoods // 许愿池商品 map[goodID]商品信息
	liveGoodsMoreMap map[int64]*livegoods.More     // 许愿池商品 More 信息 map[goodID]商品 More 信息
	giftMap          map[int64]*gift.Gift          // 商品对应的的奖品 map[giftID]奖品信息
}

var (
	source = goutil.NewLockedSource(goutil.TimeNow().Unix())
)

// ActionCronWishesDraw 许愿池开奖
// 运行周期：1 0/15 * * * *
/**
 * @api {post} /rpc/activity/cron/wish/draw 许愿池开奖
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} event_id 活动 ID
 * @apiParam {Number} period 开奖周期（单位：秒），默认 15min
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronWishesDraw(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newDrawParams(c)
	if err != nil {
		return "", err
	}

	if param == nil {
		return "未到开奖时间", nil
	}

	if len(param.wishesList) == 0 {
		logger.Warn("本轮许愿记录为空")
		return "本轮许愿记录为空", nil
	}

	goutil.Go(func() {
		// 开奖
		err = param.wishDraw()
		if err != nil {
			logger.Error(err)
		}
	})

	return "success", nil
}

func newDrawParams(c *handler.Context) (*drawParams, error) {
	param := new(drawParams)
	err := c.BindJSON(param)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.Period <= 0 {
		// 若不传默认周期为 15 分钟
		param.Period = 15 * goutil.SecondOneMinute
	}

	if param.EventID != 0 {
		simple, e, err := activity.FindLiveExtendedFields(param.EventID, false)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if simple == nil {
			return nil, actionerrors.ErrParamsMsg("活动不存在")
		}

		param.nowTime = e.TimeNow().Unix()
	} else {
		param.nowTime = goutil.TimeNow().Unix()
	}

	// 查询商品信息
	liveGoodsList, err := livegoods.ListLiveGoods(livegoods.GoodsTypeWish)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	avaliableGoods := make([]livegoods.LiveGoods, 0, len(liveGoodsList))
	for _, g := range liveGoodsList {
		tr := goutil.NewTimeRangeUnix(g.SaleStartTime, g.SaleEndTime+2*goutil.SecondOneMinute) // 由于是准点执行，售卖结束时间多加 2 分钟保证可以查询到本轮许愿池商品
		if tr.Between(time.Unix(param.nowTime, 0)) {
			avaliableGoods = append(avaliableGoods, g)
		}
	}
	if len(avaliableGoods) == 0 {
		return nil, actionerrors.ErrParamsMsg("未找到商品信息")
	}

	// 若同时存在多个奖池，奖池的售卖时间一致，只需判断一个奖池的售卖时间
	param.checkDrawTime(avaliableGoods[0].SaleStartTime, avaliableGoods[0].SaleEndTime)
	if !param.draw {
		return nil, nil
	}

	if param.EventID == 0 {
		// 查询活动信息
		more, err := avaliableGoods[0].UnmarshalMore()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		param.EventID = more.EventID
	}

	// 查询许愿记录
	param.wishesList, err = wishes.ListStatusWaiting(param.startTime, param.endTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.wishesList) == 0 {
		return param, nil
	}

	param.liveGoodsMap = make(map[int64]livegoods.LiveGoods, len(avaliableGoods))
	param.liveGoodsMoreMap = make(map[int64]*livegoods.More, len(avaliableGoods))
	giftIDs := make([]int64, 0, len(avaliableGoods)*2)
	for _, liveGoods := range avaliableGoods {
		param.liveGoodsMap[liveGoods.ID] = liveGoods

		more, err := liveGoods.UnmarshalMore()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if more == nil {
			return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("商品的 More 信息不存在，商品 ID: %d", liveGoods.ID))
		}

		param.liveGoodsMoreMap[liveGoods.ID] = more

		for _, giftItem := range more.Gifts {
			giftIDs = append(giftIDs, giftItem.ID)
		}
	}

	// 获取礼物信息
	param.giftMap, err = gift.FindGiftMapByGiftIDs(giftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return param, nil
}

func (param *drawParams) checkDrawTime(saleStartTime, saleEndTime int64) {
	// 设置 2 分钟的时间偏移量，允许最多晚两分钟执行
	timeOffset := 2 * goutil.SecondOneMinute
	checkTime := param.nowTime - timeOffset
	if checkTime < saleStartTime || checkTime >= saleEndTime {
		// 定时任务每 15min 执行一次，不在商品售卖时间内（售卖结束需要开奖）不需要开奖，直接 return
		return
	}

	// 根据商品售卖开始时间和结束时间计算开奖周期（每个月两天活动，每 15min 开奖一次）
	for i := 0; ; i++ {
		startTime := saleStartTime + int64(i)*param.Period
		if startTime >= saleEndTime {
			break
		}
		endTime := min(startTime+param.Period, saleEndTime)
		// 判断当前时间是否是开奖时间（是否在时间偏移量范围内）
		if param.nowTime >= endTime && param.nowTime-endTime <= timeOffset {
			param.startTime = startTime
			param.endTime = endTime
			param.draw = true
			break
		}
	}
}

func (param *drawParams) wishDraw() error {
	var err error
	baseGiftIDsMap := make(map[int64][]int64, len(param.liveGoodsMoreMap))
	baseDistributionMap := make(map[int64]goutil.Distribution, len(param.liveGoodsMoreMap))
	// 本次活动共涉及三个礼物和积分（初级奖池一个礼物和积分，高级奖池两个礼物和积分）
	giftItemMap := make(map[int64]livegoods.GiftItem, 3)
	for goodsID, liveGoodsMore := range param.liveGoodsMoreMap {
		var weights []int
		var baseGiftIDs []int64
		for _, g := range liveGoodsMore.Gifts {
			baseGiftIDs = append(baseGiftIDs, g.ID)
			weights = append(weights, g.Rate)
			giftItemMap[g.ID] = g
		}
		// giftID 为 0 表示对应的礼物是积分
		baseGiftIDsMap[goodsID] = append(baseGiftIDs, 0)
		weights = append(weights, liveGoodsMore.PointRate)
		// 根据奖池权重获得随机分布
		baseDistributionMap[goodsID], err = goutil.NewDiscreteDistribution(weights, source, false)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	// 用户获得的积分 Map
	userPointsMap := make(map[int64]int64, len(param.wishesList))
	var sysMsgs []pushservice.SystemMsg
	// 统一使用本轮许愿的结束时间（抽奖时间）作为当前时间
	updates := make([]mongo.WriteModel, 0, len(param.wishesList))
	for _, wish := range param.wishesList {
		fields := logger.Fields{"goods_id": wish.GoodsID, "wish_id": wish.OID}
		_, ok := param.liveGoodsMap[wish.GoodsID]
		if !ok {
			logger.WithFields(fields).Error("许愿记录对应商品不存在")
			continue
		}
		goodsMore, ok := param.liveGoodsMoreMap[wish.GoodsID]
		if !ok {
			logger.WithFields(fields).Error("许愿记录对应商品的 More 信息不存在")
			continue
		}
		for _, giftItem := range goodsMore.Gifts {
			_, ok = param.giftMap[giftItem.ID]
			if !ok {
				logger.WithFields(fields).Error("许愿记录对应商品的奖品不存在")
				continue
			}
		}

		giftIDs, ok := baseGiftIDsMap[wish.GoodsID]
		if !ok {
			logger.WithFields(fields).Error("许愿记录对应商品的奖品不存在")
			continue
		}
		// 抽中的不同礼物的数量（一条许愿记录可以许愿多次（抽奖多次），最多能抽中两种礼物【高级奖池的两种奖品】）
		giftNumMap := make(map[int64]int64, 2)
		// 抽中的积分的数量
		var points int64
		for i := 0; i < wish.Num; i++ {
			giftID := giftIDs[baseDistributionMap[wish.GoodsID].NextInt()]
			if giftID != 0 {
				// 抽中礼物，礼物数量增加
				giftNumMap[giftID] += giftItemMap[giftID].Num
				hours := giftItemMap[giftID].Duration / goutil.SecondOneHour / 1000
				// 用户中奖系统消息内容，抽中一次发一条
				sysMsgs = append(sysMsgs, pushservice.SystemMsg{
					UserID: wish.UserID,
					Title:  "秘语许愿池玩法中奖通知",
					Content: fmt.Sprintf("好运降临~ 恭喜你在「晶灵物语」活动的秘语许愿池玩法中被幸运之神眷顾，成功许愿并获得「%s × %dd」，"+
						"请尽快前往直播间礼物背包查看并使用哦！"+
						"注意：礼物自发放后 %dh 内有效，若未在有效期内使用，礼物将立即消失且不予补偿。如有任何疑问请联系客服~",
						param.giftMap[giftID].Name, hours/24, hours),
				})
			} else {
				// 未抽中礼物，获得同等单价的积分（星愿币）
				points += wish.GoodsPrice
				userPointsMap[wish.UserID] += wish.GoodsPrice
			}
		}

		// 抽中的礼物数量
		nowUnix := goutil.TimeNow().Unix()
		lenGift := len(giftNumMap)
		rewards := make([]wishes.Reward, 0, lenGift+1)
		if lenGift > 0 {
			adder := useritems.NewTransactionAdder(wish.UserID, wish.TransactionID, wish.Context, lenGift)
			for giftID, num := range giftNumMap {
				adder = adder.Append(param.giftMap[giftID], num, nowUnix, nowUnix+giftItemMap[giftID].Duration/1000)

				rewards = append(rewards, wishes.Reward{
					Type:   wishes.RewardTypeBackpackGift,
					GiftID: giftID,
					Num:    int(num),
				})
			}
			// 将抽中的礼物下发到背包
			err := adder.Add()
			if err != nil {
				logger.WithFields(logger.Fields{"user_id": wish.UserID, "wish_id": wish.OID}).Error(err)
				// PASS
			}
		}

		if points > 0 {
			rewards = append(rewards, wishes.Reward{
				Type:  wishes.RewardTypeRedeemPoint,
				Point: points,
			})
		}

		updates = append(updates, mongo.NewUpdateOneModel().
			SetFilter(bson.M{"_id": wish.OID, "status": wishes.StatusWaiting}).
			SetUpdate(bson.M{"$set": bson.M{
				"status":        wishes.StatusFinished,
				"rewards":       rewards,
				"modified_time": nowUnix,
			}}))
	}

	// 更新许愿记录抽中的积分或礼物
	if len(updates) > 0 {
		err := wishes.BatchUpdate(updates)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	// 添加用户活动积分
	updateDrawPoint(param.EventID, userPointsMap)

	// 发送系统通知
	if len(sysMsgs) > 0 {
		err := service.PushService.SendSystemMsgWithOptions(sysMsgs, nil)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}

	// 本轮开奖结束，全站飘屏
	notifyDrawEnd()

	return nil
}

func updateDrawPoint(eventID int64, userPointsMap map[int64]int64) {
	// 每轮开奖人数预计 1000 - 5000 人
	for userID, userAddPoint := range userPointsMap {
		err := userapi.UpdateDrawPoint(eventID, userID, userAddPoint)
		if err != nil {
			logger.WithFields(logger.Fields{"user_id": userID, "point": userAddPoint}).Error(err)
			// PASS
		}
	}
}

func notifyDrawEnd() {
	b, err := bubble.FindSimple(bubbleIDWishes)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	formatMap := map[string]string{"normal_color": "#FFFFFF"}
	if b != nil {
		b.AppendFormatParams(formatMap)
	}
	message := `<font color="${normal_color}">本轮秘语许愿已结束，各位小耳朵可前往查看本轮许愿结果哦~</font>`
	notify := notifymessages.NewGeneral(0, goutil.FormatMessage(message, formatMap), b)
	err = userapi.BroadcastAll(notify)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}
