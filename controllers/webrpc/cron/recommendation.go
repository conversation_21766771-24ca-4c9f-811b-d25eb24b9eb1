package cron

import (
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/reportlivefanslog"
	"github.com/MiaoSiLa/live-service/models/transactionlog"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	calcIncomeStart     int64 = 2500  // 单位：元
	calcIncomeStop      int64 = 10000 // 单位：元
	calcFansIncreaseNum int64 = 30    // 单位：个
)

type calcRecommendationParam struct {
	Start           *int64 `json:"start"`
	Stop            *int64 `json:"stop"`
	FansIncreaseNum *int64 `json:"fans_increase_num"`
}

// ActionCalcRecommendation 计算腰部主播
/**
 * @api {post} /rpc/cron/live/recommendation 计算腰部主播
 * @apiDescription 前七日流水（统计口径同 grafana 后台的【礼物流水】，不包含贵族）在区间 [start,stop] 内或新增粉丝数不少于 fans_increase_num 的主播 ID
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} [start=2500] 起始区间，单位元
 * @apiParam {Number} [stop=10000] 终止区间，单位元
 * @apiParam {Number} [fans_increase_num=30] 新增粉丝数（新增加的关注数 - 减少的关注数）
 *
 * @apiParamExample {json} Request-Example:
 *   {
 *     "start": 2500,
 *     "stop": 10000,
 *     "fans_increase_num": 30
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [1,2,3]
 *   }
 */
func ActionCalcRecommendation(c *handler.Context) (handler.ActionResponse, error) {
	var param calcRecommendationParam
	err := param.buildParam(c)
	if err != nil {
		return nil, err
	}
	ids, err := param.calcRecommendation()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return ids, nil
}

func (c *calcRecommendationParam) buildParam(ctx *handler.Context) error {
	err := ctx.BindJSON(c)
	if err != nil {
		return actionerrors.ErrParams
	}
	if c.Start == nil {
		c.Start = util.NewInt64(calcIncomeStart)
	}
	if c.Stop == nil {
		c.Stop = util.NewInt64(calcIncomeStop)
	}
	if c.FansIncreaseNum == nil {
		c.FansIncreaseNum = util.NewInt64(calcFansIncreaseNum)
	}
	return nil
}

// CalcRecommendation 前七日流水 在区间 [start,stop] 内的主播 ID
func (c *calcRecommendationParam) calcRecommendation() ([]int64, error) {
	logger.Infof("recommended live_ids start calculation")

	week := 7 * 24 * time.Hour
	today := util.BeginningOfDay(goutil.TimeNow())

	userIDs := make([]int64, 0)
	err := transactionlog.ADB().
		Select("to_id").
		// 仅统计礼物、提问、超粉的收益，不包含贵族
		Where("attr IN (?)", append(transactionlog.GiftAttrs(), transactionlog.SuperFanAttrs()...)).
		Where("type IN (?)", []int{transactionlog.TypeLive, transactionlog.TypeGuildLive}).
		Where("status = ?", transactionlog.StatusSuccess).
		Where("confirm_time BETWEEN ? AND ?", today.Add(-week).Unix(), today.Unix()-1).
		Group("to_id").Having("SUM(income) BETWEEN ? AND ?", *c.Start, *c.Stop).Pluck("to_id", &userIDs).Error
	if err != nil {
		return nil, err
	}

	ids := make([]int64, 0)
	err = reportlivefanslog.LiveFansLog{}.DB().
		Select("user_id").
		Where("bizdate >= ? AND bizdate < ?",
			today.Add(-week).Format(util.TimeFormatYMD), today.Format(util.TimeFormatYMD)).
		Group("user_id").
		Having("SUM(follow) - SUM(unfollow) >= ?", *c.FansIncreaseNum).Pluck("user_id", &ids).Error
	if err != nil {
		return nil, err
	}
	recommendUserIDs := util.Uniq(append(userIDs, ids...))
	key := keys.KeyCronRecommendLiveIDs0.Format()
	pipe := service.Redis.TxPipeline()

	values := make([]interface{}, len(recommendUserIDs))
	for i, v := range recommendUserIDs {
		values[i] = v
	}

	pipe.Del(key)
	if len(recommendUserIDs) != 0 {
		pipe.SAdd(key, values...)
		pipe.Expire(key, week)
	}

	if _, err = pipe.Exec(); err != nil {
		return nil, err
	}

	logger.WithField("user_ids", recommendUserIDs).Infof("recommended live_ids stored")

	return recommendUserIDs, nil
}
