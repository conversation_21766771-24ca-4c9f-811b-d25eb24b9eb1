package cron

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestUserBalanceExpireNotice(t *testing.T) {
	require := require.New(t)

	cleanup := mrpc.SetMock(vip.URLGetExpireBalanceByTime, func(input interface{}) (output interface{}, err error) {
		return []vip.UserExpireBalance{
			{
				UserID:       12,
				TotalBalance: 2000,
			},
		}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLGetUserByTime, func(input interface{}) (output interface{}, err error) {
		resp := []vip.UserVip{
			{UserID: 9074509},
			{UserID: 9074510},
		}
		return resp, nil
	})
	defer cleanup()

	vipConfig := &vip.ConfigResp{
		EnableNewVipBalanceTime: util.TimeNow().Unix(),
	}
	err := UserBalanceExpireNotice(33, vipConfig)
	require.EqualError(err, "贵族钻石清零通知类型错误")

	err = UserBalanceExpireNotice(NoticeTypeClearBalancesInOneDay, vipConfig)
	require.NoError(err)

	err = UserBalanceExpireNotice(NoticeTypeClearBalancesInThreeDays, vipConfig)
	require.NoError(err)

	err = UserBalanceExpireNotice(NoticeTypeClearBalancesClear, vipConfig)
	require.NoError(err)
}

func TestNewExpireBalanceNoticeParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var expireBalanceTypeExpected int
	cleanup := mrpc.SetMock(vip.URLGetExpireBalanceByTime, func(input interface{}) (output interface{}, err error) {
		body := input.(map[string]interface{})
		expireBalanceType, ok := body["type"]
		require.True(ok)
		assert.Equal(expireBalanceTypeExpected, expireBalanceType)

		return []vip.UserExpireBalance{
			{
				UserID:       12,
				TotalBalance: 2000,
			},
		}, nil
	})
	defer cleanup()

	vipConfig := &vip.ConfigResp{
		EnableNewVipBalanceTime: util.TimeNow().Add(-2 * time.Minute).Unix(),
	}
	param, err := newExpireBalanceNoticeParam(33, vipConfig)
	require.EqualError(err, "贵族钻石清零通知类型错误")
	assert.Nil(param)

	expireBalanceTypeExpected = vip.TypeExpireBalanceClear
	param, err = newExpireBalanceNoticeParam(NoticeTypeClearBalancesInOneDay, vipConfig)
	require.NoError(err)
	assert.NotNil(param)

	param, err = newExpireBalanceNoticeParam(NoticeTypeClearBalancesInThreeDays, vipConfig)
	require.NoError(err)
	assert.NotNil(param)

	param, err = newExpireBalanceNoticeParam(NoticeTypeClearBalancesClear, vipConfig)
	require.NoError(err)
	assert.NotNil(param)

	expireBalanceTypeExpected = vip.TypeExpireBalanceExpired
	vipConfig.EnableNewVipBalanceTime = util.TimeNow().Add(2 * time.Minute).Unix()
	param, err = newExpireBalanceNoticeParam(NoticeTypeClearBalancesInOneDay, vipConfig)
	require.NoError(err)
	assert.NotNil(param)

	cleanup = mrpc.SetMock(vip.URLGetExpireBalanceByTime, func(input interface{}) (output interface{}, err error) {
		return []vip.UserExpireBalance{}, nil
	})
	defer cleanup()
	param, err = newExpireBalanceNoticeParam(NoticeTypeClearBalancesInOneDay, vipConfig)
	require.NoError(err)
	require.NotNil(param)
	assert.Empty(param.userBalances)
}

func TestExpireBalanceNoticeParam_loadTime(t *testing.T) {
	assert := assert.New(t)

	vipConfig := &vip.ConfigResp{
		EnableNewVipBalanceTime: util.TimeNow().Add(-time.Minute).Unix(),
	}
	param := &expireBalanceNoticeParam{
		now:        time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local),
		noticeType: NoticeTypeClearBalancesInOneDay,
	}
	param.loadTime(vip.TypeExpireBalanceClear)
	assert.Equal(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local), param.balanceExpireStartTime)
	assert.Equal(time.Date(2023, 1, 2, 0, 0, 0, 0, time.Local), param.balanceExpireEndTime)
	assert.Equal("钻石有效期剩余 1 天通知", param.balanceNoticeInfo)
	assert.Equal("贵族钻石即将清零", param.title)

	param = &expireBalanceNoticeParam{
		now:        time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local),
		noticeType: NoticeTypeClearBalancesInThreeDays,
	}
	param.loadTime(vip.TypeExpireBalanceClear)
	assert.Equal(time.Date(2023, 1, 3, 0, 0, 0, 0, time.Local), param.balanceExpireStartTime)
	assert.Equal(time.Date(2023, 1, 4, 0, 0, 0, 0, time.Local), param.balanceExpireEndTime)
	assert.Equal("钻石有效期剩余 3 天通知", param.balanceNoticeInfo)
	assert.Equal("贵族钻石即将清零", param.title)

	param = &expireBalanceNoticeParam{
		now:        time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local),
		noticeType: NoticeTypeClearBalancesClear,
	}
	param.loadTime(vip.TypeExpireBalanceClear)
	assert.Equal(time.Date(2022, 12, 31, 0, 0, 0, 0, time.Local), param.balanceExpireStartTime)
	assert.Equal(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local), param.balanceExpireEndTime)
	assert.Equal("贵族钻石清零通知", param.balanceNoticeInfo)
	assert.Equal("贵族钻石清零", param.title)

	vipConfig.EnableNewVipBalanceTime = util.TimeNow().Add(time.Minute).Unix()
	param = &expireBalanceNoticeParam{
		now:        time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local),
		noticeType: NoticeTypeClearBalancesInOneDay,
	}
	param.loadTime(vip.TypeExpireBalanceExpired)
	assert.Equal(time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local), param.balanceExpireStartTime)
	assert.Equal(time.Date(2023, 1, 2, 0, 0, 0, 0, time.Local), param.balanceExpireEndTime)
	assert.Equal("钻石有效期剩余 1 天通知", param.balanceNoticeInfo)
	assert.Equal("贵族钻石即将到期", param.title)

	param = &expireBalanceNoticeParam{
		now:        time.Date(2023, 1, 1, 0, 0, 0, 0, time.Local),
		noticeType: NoticeTypeClearBalancesInThreeDays,
	}
	param.loadTime(vip.TypeExpireBalanceExpired)
	assert.Equal(time.Date(2023, 1, 3, 0, 0, 0, 0, time.Local), param.balanceExpireStartTime)
	assert.Equal(time.Date(2023, 1, 4, 0, 0, 0, 0, time.Local), param.balanceExpireEndTime)
	assert.Equal("钻石有效期剩余 3 天通知", param.balanceNoticeInfo)
	assert.Equal("贵族钻石即将到期", param.title)

	assert.Panics(func() {
		param = &expireBalanceNoticeParam{
			noticeType: 33,
		}
		param.loadTime(vip.TypeExpireBalanceClear)
	})
}

func TestExpireBalanceNoticeParam_sendNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var called bool
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (interface{}, error) {
		body, ok := input.(map[string]interface{})
		require.True(ok)
		systemMsgList, ok := body["systemmsgs"].([]pushservice.SystemMsg)
		require.True(ok)
		assert.Equal(systemMsgList[0].Title, "贵族钻石清零")
		assert.NotEmpty(systemMsgList[0].Content)
		expectedTime := time.Date(2024, 3, 20, 0, 0, 0, 0, time.Local).Unix()
		assert.EqualValues(expectedTime, systemMsgList[0].SendTime)

		called = true
		return "success", nil
	})
	defer cancel()

	testTime := time.Date(2024, 3, 20, 12, 0, 0, 0, time.Local)
	param := &expireBalanceNoticeParam{
		title:                "贵族钻石清零",
		now:                  testTime,
		balanceExpireEndTime: testTime,
		userBalances: []vip.UserExpireBalance{
			{UserID: 100, TotalBalance: 10},
		},
		vipConfig: &vip.ConfigResp{
			EnableNewVipBalanceTime: util.TimeNow().Add(time.Minute).Unix(),
		},
	}
	param.sendNotice()
	assert.True(called)
}

func TestExpireBalanceNoticeParam_buildMessage(t *testing.T) {
	assert := assert.New(t)

	now := util.TimeNow()
	param := &expireBalanceNoticeParam{
		balanceExpireEndTime: time.Date(2022, 1, 2, 0, 0, 0, 0, time.Local),
		vipConfig: &vip.ConfigResp{
			EnableNewVipBalanceTime: now.Add(time.Minute).Unix(),
		},
	}
	msg := param.buildMessage(vip.UserExpireBalance{UserID: 100, TotalBalance: 10})
	assert.Equal(`尊敬的用户，您有 10 贵族钻石将在 2022-01-01 23:59:59 到期清零，请尽快使用（若您的贵族钻石为冻结状态，请先开通或续费贵族来解除冻结状态）<a href="https://fm.uat.missevan.com/noble/mynoble?tab=balance">查看我的贵族钻石</a>`, msg)

	param.vipConfig.EnableNewVipBalanceTime = now.Unix()
	param.noticeType = NoticeTypeClearBalancesClear
	msg = param.buildMessage(vip.UserExpireBalance{UserID: 100, TotalBalance: 10})
	assert.Equal(`尊敬的用户，您有 10 贵族钻石已在 2022-01-01 23:59:59 到期清零。`, msg)

	param.noticeType = NoticeTypeClearBalancesInThreeDays
	msg = param.buildMessage(vip.UserExpireBalance{UserID: 100, TotalBalance: 10})
	assert.Equal(`尊敬的用户，您有 10 贵族钻石将在 2022-01-01 23:59:59 到期清零，重新开通贵族可解冻贵族钻石并恢复正常使用。<a href="https://fm.uat.missevan.com/noble/mynoble?tab=balance">查看我的贵族钻石</a>`, msg)
}
