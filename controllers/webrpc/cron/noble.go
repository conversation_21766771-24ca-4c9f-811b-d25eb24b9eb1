package cron

import (
	"errors"
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// 通知类型
const (
	NoticeTypeThreeDay      = iota + 1 // 1：贵族有效期剩余 3 天
	NoticeTypeExpired                  // 2：贵族身份失效
	NoticeTypeProtectOneDay            // 3：贵族续费保护剩余 1 天
)

type expireNoticeParam struct {
	now time.Time
	// 获取 [vipMinExpireTime, vipMaxExpireTime] 时间范围内的过期贵族
	vipMinExpireTime time.Time
	vipMaxExpireTime time.Time
	title            string
	users            []vip.UserVip          // 指定过期时间内的直播贵族用户
	vipLevelMap      map[int64]*vip.UserVip // 指定过期时间内的直播贵族用户最高等级贵族

	noticeType int
	vipType    int
	vipConfig  *vip.ConfigResp
}

// ActionCronExpireNotice 直播贵族过期通知
/*
 * @api {post} /rpc/cron/noble/expire 直播贵族过期通知
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronExpireNotice(ctx *handler.Context) (handler.ActionResponse, error) {
	vipConfig, err := vip.GetVipConfig(false)
	if err != nil {
		return nil, err
	}

	if err := VipExpireNotice(NoticeTypeThreeDay, vip.TypeLiveNoble, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := VipExpireNotice(NoticeTypeExpired, vip.TypeLiveNoble, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := VipExpireNotice(NoticeTypeProtectOneDay, vip.TypeLiveNoble, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := VipExpireNotice(NoticeTypeThreeDay, vip.TypeLiveHighness, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := VipExpireNotice(NoticeTypeExpired, vip.TypeLiveHighness, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := VipExpireNotice(NoticeTypeProtectOneDay, vip.TypeLiveHighness, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if err := UserBalanceExpireNotice(NoticeTypeClearBalancesInOneDay, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := UserBalanceExpireNotice(NoticeTypeClearBalancesInThreeDays, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if err := UserBalanceExpireNotice(NoticeTypeClearBalancesClear, vipConfig); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return "success", nil
}

// VipExpireNotice 贵族过期通知
func VipExpireNotice(noticeType, vipType int, vipConfig *vip.ConfigResp) error {
	switch noticeType {
	case NoticeTypeThreeDay, NoticeTypeExpired, NoticeTypeProtectOneDay:
	default:
		return errors.New("贵族通知类型错误")
	}
	switch vipType {
	case vip.TypeLiveNoble, vip.TypeLiveHighness:
	default:
		return errors.New("直播贵族类型错误")
	}
	param := &expireNoticeParam{
		noticeType: noticeType,
		vipType:    vipType,
		vipConfig:  vipConfig,
	}
	err := param.loadTime()
	if err != nil {
		return err
	}

	// 请求 SSO 获取指定过期时间内的直播贵族用户
	param.users, err = vip.GetUserByTime(vipType, param.vipMinExpireTime.Unix(), param.vipMaxExpireTime.Unix())
	if err != nil {
		return err
	}
	usersLen := len(param.users)
	if usersLen == 0 {
		logger.Infof("【直播贵族过期通知】类型：%s，已通知 0 个符合条件的用户", param.vipNoticeInfo())
		return nil
	}
	userIDs := make([]int64, 0, len(param.users))
	for _, user := range param.users {
		userIDs = append(userIDs, user.UserID)
	}
	// TODO: 通过获取指定过期时间内的用户接口获取
	// 获取用户最高等级贵族，上神用户的贵族过期了，称呼是尊敬的上神用户
	vipLevel, err := vip.LiveUserLevelByUserIDs(userIDs)
	if err != nil {
		return err
	}
	param.vipLevelMap = goutil.ToMap(vipLevel, "UserID").(map[int64]*vip.UserVip)

	param.clearUserNobleData()
	if err = param.sendNotice(); err != nil {
		logger.WithFields(logger.Fields{
			"notice_type": noticeType,
			"vip_type":    vipType,
			"user_num":    len(param.users),
		}).Error(err)
		// PASS
	}
	logger.Infof("【直播贵族过期通知】类型：%s，已通知 %d 个符合条件的用户", param.vipNoticeInfo(), len(param.users))
	return nil
}

func (param *expireNoticeParam) sendNotice() error {
	// 发送系统通知
	y, m, d := param.now.Date()
	noticeTime := time.Date(y, m, d, 0, 0, 0, 0, time.Local)
	sysMsgBox := userapi.NewSystemMsgBox(noticeTime.Unix())
	for _, user := range param.users {
		switch param.vipType {
		case vip.TypeLiveNoble:
			expireNoticeMessage := param.buildNobleMessage(user.Title, param.vipLevelMap[user.UserID])
			sysMsgBox.AddMessage(user.UserID, param.title, expireNoticeMessage)
		case vip.TypeLiveHighness:
			expireNoticeMessage := param.buildHighnessMessage(user.UserID)
			sysMsgBox.AddMessage(user.UserID, param.title, expireNoticeMessage)
		default:
			panic("vipType error")
		}
	}
	return sysMsgBox.Send(&pushservice.SystemMsgOptions{
		DisableHTMLEscape: true,
	})
}

func (param *expireNoticeParam) loadTime() error {
	// 获取当日时间（允许提前两分钟执行）
	param.now = goutil.TimeNow().Add(2 * time.Minute)
	y, m, d := param.now.Date()
	switch param.noticeType {
	case NoticeTypeThreeDay:
		param.vipMaxExpireTime = time.Date(y, m, d+2, 23, 59, 59, 0, time.Local)
	case NoticeTypeProtectOneDay:
		param.vipMaxExpireTime = time.Date(y, m, d-param.vipConfig.LiveNobleProtectDays, 23, 59, 59, 0, time.Local)
	case NoticeTypeExpired:
		param.vipMaxExpireTime = time.Date(y, m, d-1, 23, 59, 59, 0, time.Local)
	default:
		panic("直播贵族过期通知类型错误")
	}
	param.vipMinExpireTime = param.vipMaxExpireTime.AddDate(0, 0, -1)
	return nil
}

func (param *expireNoticeParam) buildNobleMessage(nobleTitle string, userLevel *vip.UserVip) string {
	isHighness := userLevel != nil && userLevel.Type == vip.TypeLiveHighness
	var noticeContent string
	switch param.noticeType {
	case NoticeTypeThreeDay:
		endTimeStr := param.vipMaxExpireTime.Format(goutil.TimeFormatHMS)
		if isHighness {
			noticeContent = fmt.Sprintf("您的%s贵族将在 %s 到期，请您及时续费。", nobleTitle, endTimeStr)
		} else {
			noticeContent = fmt.Sprintf("您的贵族身份将在 %s 到期，为避免贵族特权失效，请及时续费。", endTimeStr)
		}
		param.title = "直播贵族续费提示"
	case NoticeTypeProtectOneDay:
		noticeContent = fmt.Sprintf("您的%s贵族续费保护期将在今日 23:59:59 结束。今日之内，您可以续费价格购买原有贵族。续费保护期结束后，您只能以开通价格重新购买。",
			nobleTitle)
		param.title = "直播贵族续费提示"
	case NoticeTypeExpired:
		// TODO: 按照数据库中的用户过期时间计算
		y, m, d := param.now.Date()
		protectEndTimeStr := time.Date(y, m, d+param.vipConfig.LiveNobleProtectDays, 0, 0, 0, 0, time.Local).
			Format(goutil.TimeFormatHMS)
		if isHighness {
			noticeContent = fmt.Sprintf("您的%s贵族现已失效。您可以在 %s 续费保护期结束前进行续费来恢复%s贵族身份。续费保护期结束后，您只能以开通价格重新购买%s贵族。",
				nobleTitle, protectEndTimeStr, nobleTitle, nobleTitle)
		} else {
			noticeContent = fmt.Sprintf("您的贵族身份现已失效。您可以在 %s 续费保护期结束前以续费价格购买原有贵族。续费保护期结束后，您只能以开通价格重新购买。",
				protectEndTimeStr)
		}
		param.title = "直播贵族失效提示"
	}

	salutation := func(nobleTitle string, userLevel *vip.UserVip) string {
		if isHighness {
			return "尊敬的上神用户，"
		}
		return fmt.Sprintf("尊敬的%s贵族用户，", nobleTitle)
	}
	return fmt.Sprintf("%s%s<a href=\"%s\">立即续费</a>",
		salutation(nobleTitle, userLevel), noticeContent, config.Conf.Params.NobleParams.NobleDetailsURL)
}

func (param *expireNoticeParam) buildHighnessMessage(userID int64) string {
	progress := vip.HighnessRenewalThreshold - usermeta.HighnessSpend(userID)
	var noticeContent string
	switch param.noticeType {
	case NoticeTypeThreeDay:
		endTimeStr := param.vipMaxExpireTime.Format(goutil.TimeFormatHMS)
		noticeContent = fmt.Sprintf("尊敬的上神用户，您的上神贵族将在 %s 到期，您还需消费 %d 钻即可延长 30 天的上神身份有效期，请您及时续费~", endTimeStr, progress)
		param.title = "上神贵族即将到期"
	case NoticeTypeProtectOneDay:
		noticeContent = fmt.Sprintf("尊敬的上神用户，您的上神续费保护期将在今日 23:59:59 结束。今日之内，您还需消费 %d 钻即可获得 30 天的上神身份有效期。续费保护期结束后，您需要按开通条件重新获取上神身份。",
			progress)
		param.title = "上神贵族续费提示"
	case NoticeTypeExpired:
		// TODO: 按照数据库中的用户过期时间计算
		y, m, d := param.now.Date()
		protectEndTimeStr := time.Date(y, m, d+param.vipConfig.LiveNobleProtectDays, 0, 0, 0, 0, time.Local).
			Format(goutil.TimeFormatHMS)
		noticeContent = fmt.Sprintf("尊敬的上神用户，您的上神身份现已失效。在 %s 续费保护期结束前消费 %d 钻即可获得 30 天的上神身份有效期。续费保护期结束后，您需要按开通条件重新获取上神身份。",
			protectEndTimeStr, progress)
		param.title = "上神贵族失效提示"
	}
	return noticeContent
}

func (param *expireNoticeParam) vipNoticeInfo() string {
	var prefix string
	switch param.vipType {
	case vip.TypeLiveNoble:
		prefix = "直播贵族"
	case vip.TypeLiveHighness:
		prefix = "上神贵族"
	}
	var noticeInfo string
	switch param.noticeType {
	case NoticeTypeThreeDay:
		noticeInfo = fmt.Sprintf("%s有效期剩余 3 天通知", prefix)
	case NoticeTypeProtectOneDay:
		noticeInfo = fmt.Sprintf("%s续费保护剩余 1 天通知", prefix)
	case NoticeTypeExpired:
		noticeInfo = fmt.Sprintf("%s失效通知", prefix)
	}
	return noticeInfo
}

func (param *expireNoticeParam) clearUserNobleData() {
	if param.noticeType != NoticeTypeExpired || len(param.users) == 0 {
		return
	}

	userIDs := make([]int64, 0, len(param.users))
	for _, user := range param.users {
		userIDs = append(userIDs, user.UserID)
	}
	// 移除过期的贵族外观
	userappearance.ClearUserVipAppearance(userIDs, []int{param.vipType})
	// 清空贵族权益
	vip.ClearUserVipPrivilege(userIDs, param.vipLevelMap)

	// 删除用户过期贵族缓存
	vip.ClearUserVipCache(userIDs...)
}
