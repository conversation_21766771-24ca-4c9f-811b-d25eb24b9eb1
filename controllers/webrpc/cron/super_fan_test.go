package cron

import (
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
)

func mockPushServiceServer() func() {
	r := gin.Default()
	h := handler.Handler{
		Middlewares: gin.HandlersChain{rpc.Middleware("testkey")},
		Actions: map[string]*handler.Action{
			"/api/systemmsg": handler.NewAction(handler.POST, func(c *handler.Context) (response handler.ActionResponse, e error) {
				return handler.M{
					"count": 3,
				}, nil
			}, false),
		},
	}
	addr := tutil.RunMockServer(r, 0, &h)
	pushServiceOriginal := service.PushService
	service.PushService, _ = pushservice.NewPushServiceClient(&pushservice.Config{
		URL: "http://" + addr + "/",
		Key: "testkey",
	})
	return func() {
		service.PushService = pushServiceOriginal
	}
}

func TestActionSuperFanExpireNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mockPushServiceServer()
	defer cleanup()

	util.SetTimeNow(func() time.Time {
		tm, err := time.ParseInLocation("2006-01-02 15:04:05", "2021-01-15 13:00:00", time.Local)
		require.NoError(err)
		return tm
	})
	defer util.SetTimeNow(nil)
	creators, err := mowangskuser.FindSimpleMap([]int64{2, 3, 4})
	require.NoError(err)
	require.Len(creators, 3)

	sql := "INSERT INTO live_txn_order " +
		"(create_time, modified_time, expire_time, title, price, status, tid, goods_id, goods_type, attr, buyer_id, seller_id, more) " +
		"VALUES " +
		"(UNIX_TIMESTAMP('2020-12-01 13:00'), UNIX_TIMESTAMP('2020-12-01 13:00'), UNIX_TIMESTAMP('2021-05-31 00:00'), '开通直播超级粉丝 ×6 个月', 600, 1, 1001, 11, 1, 1, 3333, 1, ''), " +
		// 一天后到期
		"(UNIX_TIMESTAMP('2020-12-16 18:00'), UNIX_TIMESTAMP('2020-12-16 18:00'), UNIX_TIMESTAMP('2021-01-16 00:00'), '开通直播超级粉丝 ×1 个月', 100, 1, 1001, 12, 1, 1, 3333, 2, ''), " +
		// 三天后到期
		"(UNIX_TIMESTAMP('2020-12-18 18:00'), UNIX_TIMESTAMP('2020-12-18 18:00'), UNIX_TIMESTAMP('2021-01-18 00:00'), '开通直播超级粉丝 ×1 个月', 100, 1, 1001, 13, 1, 1, 3333, 3, ''), " +
		// 一天后到期
		"(UNIX_TIMESTAMP('2020-12-16 18:00'), UNIX_TIMESTAMP('2020-12-16 18:00'), UNIX_TIMESTAMP('2021-01-16 00:00'), '开通直播超级粉丝 ×1 个月', 100, 1, 1001, 14, 1, 1, 4444, 4, ''); "
	require.NoError(livetxnorder.LiveTxnOrder{}.DB().Exec(sql).Error)
	defer func() {
		require.NoError(livetxnorder.LiveTxnOrder{}.DB().Delete(nil, "buyer_id IN (3333, 4444) AND seller_id IN (1, 2, 3, 4)").Error)
	}()
	err = live.Live{}.DB().Exec(
		"INSERT INTO live " +
			"(id, user_id, room_id, title, status, create_time, modified_time) " +
			"VALUES " +
			"(11001, 1, 901, 'room-1', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(12001, 2, 902, 'room-2', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(13001, 3, 903, 'room-3', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()), " +
			"(14001, 4, 904, 'room-4', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());",
	).Error
	require.NoError(err)
	defer func() {
		require.NoError(live.Live{}.DB().Delete(nil, "user_id IN (1, 2, 3, 4)").Error)
	}()

	t.Run("getAboutToExpireSuperFans", func(t *testing.T) {
		superFans, err := getAboutToExpireSuperFans(superFanExpireNoticeOneDay)
		require.NoError(err)
		require.Len(superFans, 2)
		assert.Equal(int64(3333), superFans[0].UserID)
		assert.Equal(int64(2), superFans[0].CreatorID)
		assert.Equal(int64(902), superFans[0].RoomID)
		assert.Equal(int64(12), superFans[0].GoodsID)
		assert.Equal(creators[2].Username, superFans[0].CreatorUsername)
		assert.Equal(int64(4444), superFans[1].UserID)
		assert.Equal(int64(4), superFans[1].CreatorID)
		assert.Equal(int64(904), superFans[1].RoomID)
		assert.Equal(int64(14), superFans[1].GoodsID)
		assert.Equal(creators[4].Username, superFans[1].CreatorUsername)

		superFans, err = getAboutToExpireSuperFans(superFanExpireNoticeThreeDay)
		require.NoError(err)
		require.Len(superFans, 1)
		assert.Equal(int64(3333), superFans[0].UserID)
		assert.Equal(int64(3), superFans[0].CreatorID)
		assert.Equal(int64(903), superFans[0].RoomID)
		assert.Equal(int64(13), superFans[0].GoodsID)
		assert.Equal(creators[3].Username, superFans[0].CreatorUsername)
	})

	t.Run("sendSuperFanExpireNotice", func(t *testing.T) {
		require.NoError(sendSuperFanExpireNotice(superFanExpireNoticeOneDay))
		require.NoError(sendSuperFanExpireNotice(superFanExpireNoticeThreeDay))
	})

	t.Run("ActionSuperFanExpireNotice", func(t *testing.T) {
		ctx := handler.NewTestContext("POST", "/rpc/cron/superfan/expire-notice", true, nil)
		resp, err := ActionSuperFanExpireNotice(ctx)
		require.NoError(err)
		s := resp.(string)
		assert.Equal("success", s)
	})
}
