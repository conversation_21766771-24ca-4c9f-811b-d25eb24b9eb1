package cron

import (
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
)

// ActionCronLuckyBagDraw 延期未开奖福袋兜底开奖
// 运行周期：0 */2 * * * *
/**
 * @api {post} /rpc/cron/luckybag/draw 延期未开奖福袋兜底开奖
 * @apiDescription 处理在预期结束一分钟后仍处于未开奖 pending 状态的福袋
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronLuckyBagDraw(c *handler.Context) (handler.ActionResponse, error) {
	records, err := luckybag.FindDelayPendingInitiateRecord(time.Minute)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(records) == 0 {
		return "success", nil
	}
	for i := range records {
		key := keys.DelayKeyDrawLuckyBag1.Format(records[i].ID)
		err := service.DatabusDelayPubSend(key, map[string]int64{"lucky_bag_id": records[i].ID})
		if err != nil {
			logger.WithField("lucky_bag_id", records[i].ID).Errorf("send delay msg error: %v", err)
			// PASS
			continue
		}
		logger.WithField("lucky_bag_id", records[i].ID).Warn("send unhandle draw lucky bag databus msg")
	}
	return "success", nil
}
