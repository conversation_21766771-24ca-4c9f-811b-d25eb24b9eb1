package cron

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionSuperRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.Find(room.TestExistsRoomID)
	require.NoError(err)
	require.NotNil(r)

	// 保证主播有超粉用户，确保在榜单
	app := livemedalstats.AddPointParam{
		RoomOID:    r.OID,
		RoomID:     r.Room<PERSON>,
		CreatorID:  r.CreatorID,
		UserID:     123,
		Type:       livemedal.TypeSuperFanMedalPoint,
		PointAdd:   100,
		ExpireTime: goutil.TimeNow().AddDate(1, 0, 0).Unix(),
	}
	_, err = app.AddPoint()
	require.NoError(err)

	resp, err := ActionSuperFanRank(nil)
	require.NoError(err)
	assert.Equal("success", resp)

	key := activity.KeyRank(175, "super_fan")
	result, err := service.Redis.ZRevRangeWithScores(key, 0, -1).Result()
	require.NoError(err)
	var ok bool
	for _, z := range result {
		if z.Member == strconv.FormatInt(r.CreatorID, 10) {
			ok = true
			break
		}
	}
	assert.True(ok)
}
