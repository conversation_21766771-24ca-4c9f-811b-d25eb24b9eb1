package cron

import (
	"fmt"
	"html"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalremoverecord"
	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/notifymessages"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	serviceredis "github.com/MiaoSiLa/live-service/service/redis"
	"github.com/MiaoSiLa/live-service/service/storage"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const cleanupRunNow = 1

const (
	notifyTypeDefault          = iota // 默认自动执行
	notifyTypeBeforeEndOfMonth        // 主播月榜结束前通知类型
	notifyTypeAfterEndOfMonth         // 主播月榜结束后通知类型
)

const (
	bubbleIDMonthlyBeforeEnd = 14 // 主播月榜结束前通知气泡 ID
	bubbleIDMonthlyAfterEnd  = 14 // 主播月榜结束后通知气泡 ID
)

type newRankPayload struct {
	Type     string `json:"type"`
	Event    string `json:"event"`
	RoomID   int64  `json:"room_id"`
	RankType int    `json:"rank_type"` // 榜单类型，目前仅支持 4: 小时榜
	Rank     int64  `json:"rank"`      // 0 表示未上榜，从 1 开始计数
	// 如果是负数的话说明是第一名领先第二名的值
	// 如果没有这个字段不要更新小时榜差值，如果为 0 也更新差值为 0
	RankUp *int64 `json:"rank_up,omitempty"`
}

// ActionCronRankUserHourly 每小时刷新主播小时榜排名定时任务
// 运行周期：0 0 * * * *
/*
 * @api {post} /rpc/cron/rank/user-hourly 每小时刷新主播小时榜排名定时任务，每小时调用一次
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *   {
 *     "type": "creator",
 *     "event": "new_rank",
 *     "room_id": 139879430,
 *     "rank_type": 4, // 榜单类型，目前仅支持 4: 小时榜，代码中注意显式判断
 *     "rank": 2,      // 0 表示未上榜，从 1 开始计数
 *     "rank_up": 200  // 如果是第一名的话说明是第一名领先第二名的值
 *                     // 如果没有这个字段不要更新小时榜差值，如果为 0 也更新差值为 0
 *   }
 */
func ActionCronRankUserHourly(c *handler.Context) (handler.ActionResponse, error) {
	res, err := rankUserHourly(true, true)
	if err != nil {
		return nil, err
	}

	for _, notify := range res {
		err := userapi.Broadcast(notify.RoomID, notify)
		if err != nil {
			logger.Errorf("broadcast new rank error: %v", err)
			// PASS
		}
	}
	return "success", nil
}

// notifyInRank 和 notifyOutRank 分别用户通知榜内用户和榜外用户
// 有时不需要通知榜内用户，比如服务端自动下发小时榜排名和积分差轮播给榜外用户的场景下只需要通知榜外用户
func rankUserHourly(notifyInRank, notifyOutRank bool) ([]newRankPayload, error) {
	now := goutil.TimeNow()
	rooms, err := room.List(bson.M{"status.open": room.StatusOpenTrue},
		options.Find().SetProjection(bson.M{"room_id": 1, "creator_id": 1, "config": 1}),
		&room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var lastRank *usersrank.Info
	rankMaxLen := usersrank.RankLen(usersrank.TypeHour)
	key := usersrank.Key(usersrank.TypeHour, now)

	listCmd := service.Redis.ZRevRangeWithScores(key, 0, -1)
	val, err := listCmd.Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 获取小时榜最后一位：榜单已满时取定义的榜单数量的最后一个，未满时取最后一个
	lastRankIdx := -1
	if int64(len(val)) > rankMaxLen {
		lastRankIdx = int(rankMaxLen - 1)
	} else if len(val) > 0 {
		lastRankIdx = len(val) - 1
	}
	rankMap := make(map[int64]*usersrank.Info, len(val))
	for i := 0; i < len(val); i++ {
		rank := new(usersrank.Info)
		rank.UserID, err = strconv.ParseInt(val[i].Member.(string), 10, 64)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		rank.Revenue = int64(val[i].Score)
		rank.Rank = int64(i) + 1
		if i >= 1 {
			rank.RankUp = int64(val[i-1].Score) - rank.Revenue + 1
		} else if len(val) > 1 {
			// 第一名领先第二名的差值
			rank.RankUp = rank.Revenue - int64(val[i+1].Score)
		} else {
			rank.RankUp = rank.Revenue
		}
		rankMap[rank.UserID] = rank

		if i == lastRankIdx {
			lastRank = rank
		}
	}

	res := make([]newRankPayload, 0, len(rooms))

	for _, r := range rooms {
		rank := rankMap[r.CreatorID]
		notify := newRankPayload{
			Type:     liveim.TypeCreator,
			Event:    liveim.EventNewRank,
			RoomID:   r.RoomID,
			RankType: usersrank.TypeHour,
		}
		// 大主播不实时广播，总是在这里定时广播
		mustNotify := r.IsSpecialCreator()
		if rank == nil || rank.Rank > rankMaxLen {
			// 小时榜外
			if notifyOutRank || mustNotify {
				notify.Rank = 0
				if lastRank != nil && lastRank.Rank == rankMaxLen {
					rankUp := lastRank.Revenue + 1
					if rank != nil {
						rankUp -= rank.Revenue
					}
					notify.RankUp = util.NewInt64(rankUp)
				} else {
					notify.RankUp = util.NewInt64(1) // 小时榜未满时，榜外主播上榜差值总是显示 1
				}
				res = append(res, notify)
			}
		} else if notifyInRank || mustNotify {
			// 小时榜上
			notify.Rank = rank.Rank
			notify.RankUp = util.NewInt64(rank.RankUp)
			res = append(res, notify)
		}
	}

	return res, nil
}

// ActionCronNotifyHourRank 小时榜变更后通知榜外直播间
// 运行周期：1 */2 * * * *
/*
 * @api {post} /rpc/cron/rank/hourly/notify 小时榜变更定时通知
 * @apiDescription 小时榜变更后通知榜外直播间，总是同步大主播直播间，每 2 分钟调用一次
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success" // 不需要发送通知时，会返回"当前时间不需要向小时榜榜外直播间发送排名变更信息"
 *     }
 *
 * @apiSuccessExample {json} WebSocket 房间内消息
 *   {
 *     "type": "creator",
 *     "event": "new_rank",
 *     "room_id": 139879430,
 *     "rank_type": 4, // 榜单类型，目前仅支持 4: 小时榜
 *     "rank": 0,      // 0 表示未上榜，从 1 开始计数
 *     "rank_up": 200  // 如果是第一名的话说明是第一名领先第二名的值
 *                     // 如果没有这个字段不要更新小时榜差值，如果为 0 也更新差值为 0
 *   }
 */
func ActionCronNotifyHourRank(c *handler.Context) (handler.ActionResponse, error) {
	notifyKey := keys.KeyCronNotifyHourRank0.Format()
	exists, err := service.Redis.Exists(notifyKey).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	notifyOutRank := exists != 0
	res, err := rankUserHourly(false, notifyOutRank)
	if err != nil {
		return nil, err
	}
	for _, notify := range res {
		err := userapi.Broadcast(notify.RoomID, notify)
		if err != nil {
			logger.Errorf("broadcast new rank to room out of rank error: %v", err)
			// PASS
		}
	}
	if notifyOutRank {
		_, err = service.Redis.Del(notifyKey).Result()
		if err != nil {
			logger.Warnf("redis del error: %v", err)
		}
	}
	return "success", nil
}

// ActionCronMedalCleanup 清理用户超过上限的粉丝勋章，勋章亲密度衰减
/*
 * @api {post} /rpc/cron/medal/cleanup 清理用户超过上限的粉丝勋章，勋章亲密度衰减
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number=0,1} [run_now] 现在执行，0：正常执行清理，1：立即清理用户超过上限的粉丝勋章
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronMedalCleanup(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RunNow int `json:"run_now"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	now := goutil.TimeNow()
	// 清理用户超过上限的粉丝勋章的任务在周一执行
	// 如果 RunNow 为 1 表示执行清理
	if param.RunNow == cleanupRunNow || now.Weekday() == time.Monday {
		err := cleanupMedal()
		if err != nil {
			return nil, err
		}
	}
	if err := medalPointDecline(); err != nil {
		return nil, err
	}
	return "success", nil
}

// userIDCount 用户 ID 和对应的勋章个数
type userIDCount struct {
	UserID int64 `bson:"_id" json:"-"`
	Count  int64 `bson:"count" json:"count"`
}

func cleanupMedal() error {
	col := service.MongoDB.Collection(livemedal.CollectionName)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 需要对排除超粉勋章后的剩余勋章做清理
	cur, err := col.Aggregate(ctx, bson.A{
		bson.M{"$match": bson.M{"status": bson.M{"$gt": livemedal.StatusPending},
			// 查找不是超粉和超粉已过期的勋章
			"super_fan.expire_time": bson.M{"$not": bson.M{"$gt": goutil.TimeNow().Unix()}}}},
		bson.M{"$group": bson.M{
			"_id":   "$user_id",
			"count": bson.M{"$sum": 1}},
		},
		bson.M{"$match": bson.M{"count": bson.M{"$gt": livemedal.UserMaxMedalCount()}}},
	})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	defer cur.Close(ctx)
	var uc []userIDCount
	err = cur.All(ctx, &uc)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	result := arrayGroup(uc, 50)
	for _, r := range result {
		value := make([]userIDCount, len(r))
		copy(value, r)
		goutil.Go(func() {
			userIDs := make([]int64, len(value))
			for i, v := range value {
				userIDs[i] = v.UserID
			}
			mNoble, err := vip.MapUsersInfo(userIDs, nil, nil)
			if err != nil {
				logger.WithFields(logger.Fields{
					"user_ids": userIDs,
				}).Errorf("get user noble info error: %v", err)
				// PASS
				return
			}
			for _, v := range value {
				maxCount := getUserMaxMedalCount(mNoble, v.UserID)
				if maxCount != 0 && v.Count <= maxCount {
					continue
				}
				// 设在清理时间点用户的拥有的粉丝勋章数量为 m，上限数量为 n。则需要删除 n-m 个亲密度最低的粉丝勋章
				// 若粉丝勋章的亲密度相同，则优先删除更早获得的粉丝勋章
				limits := v.Count - maxCount
				ctx, cancel := service.MongoDB.Context()
				defer cancel()
				r, err := col.Find(ctx,
					bson.M{"user_id": v.UserID, "status": bson.M{"$gt": livemedal.StatusPending},
						"super_fan.expire_time": bson.M{"$not": bson.M{"$gt": goutil.TimeNow().Unix()}}},
					options.Find().SetSort(bson.D{{Key: "point", Value: 1}, {Key: "created_time", Value: 1}}).
						SetLimit(limits), options.Find().SetProjection(bson.M{"_id": 1}))
				if err != nil {
					logger.WithFields(logger.Fields{
						"user_id": v.UserID,
					}).Errorf("get live medal error: %v", err)
					// PASS
					continue
				}
				var res []livemedal.LiveMedal
				err = r.All(ctx, &res)
				if err != nil {
					logger.WithFields(logger.Fields{
						"user_id": v.UserID,
					}).Errorf("parse live medal error: %v", err)
					// PASS
					continue
				}
				if len(res) == 0 {
					continue
				}
				oids := make([]primitive.ObjectID, len(res))
				for i, v := range res {
					oids[i] = v.OID
				}
				deleteRes, err := col.DeleteMany(ctx, bson.M{
					"_id":                   bson.M{"$in": oids},
					"super_fan.expire_time": bson.M{"$not": bson.M{"$gt": goutil.TimeNow().Unix()}}})
				if err != nil {
					logger.WithFields(logger.Fields{
						"user_id": v.UserID,
					}).Errorf("delete fan medal error: %v", err)
					// PASS
					continue
				}
				logger.WithFields(logger.Fields{
					"user_id": v.UserID,
				}).Infof("成功删除 %d 个粉丝勋章", deleteRes.DeletedCount)
			}
		})
	}
	return nil
}

// arrayGroup 将 a 转换为 [][size]userIDCount 的格式，即将 a 按照一组 size 个的标准分组
func arrayGroup(a []userIDCount, size int) [][]userIDCount {
	length := len(a) / size
	if len(a)%size != 0 {
		length++
	}
	res := make([][]userIDCount, 0, length)
	for i := 0; i < len(a); i += size {
		end := i + size
		if end > len(a) {
			end = len(a)
		}
		res = append(res, a[i:end])
	}
	return res
}

// getUserMaxMedalCount 获得用户的勋章数量上限，普通用户和贵族上限不同
func getUserMaxMedalCount(mNoble map[int64]*vip.UserInfo, userID int64) int64 {
	vipInfo := vip.UserHighestVipInfo(mNoble[userID])
	if vipInfo == nil {
		return livemedal.UserMaxMedalCount()
	}
	return vipInfo.GetMedalNum()
}

const (
	updatePatchSize = 100
)

func medalPointDecline() error {
	lastDay := util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, -7)
	filter := bson.M{
		"t_updated_time": bson.M{"$lt": lastDay},
		"status":         bson.M{"$gt": livemedal.StatusPending},
	}
	outdatedMedals, err := livemedal.List(filter, nil,
		&livemedal.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	updates := make([]mongo.WriteModel, 0, len(outdatedMedals))
	removeOIDs := make([]primitive.ObjectID, 0, len(outdatedMedals))
	reductionMap := make(map[int][]primitive.ObjectID, 0) // map[DailyReduction]primitive.ObjectID
	beginDecreaseMedals := make([]*livemedal.LiveMedal, 0, len(outdatedMedals))
	deletedMedals := make([]*livemedal.LiveMedal, 0, len(outdatedMedals))
	for _, medal := range outdatedMedals {
		if medal.Level >= 10 && medal.TReducePoint == nil {
			// 等级 >=10 且刚开始衰减的勋章
			beginDecreaseMedals = append(beginDecreaseMedals, medal)
		}
		dailyReduction := livemedal.GetDailyReduction(medal.Point, medal.Level)
		if medal.Point > int64(dailyReduction) {
			// 20 级以下勋章按固定数值衰减，按照下降数值聚合批量更新，可以减少 BulkWrite 子操作
			reductionMap[dailyReduction] = append(reductionMap[dailyReduction], medal.OID)
		} else if livemedal.IsSuperFanActive(medal.SuperFan) {
			// 超粉勋章的亲密度最少减到 1
			if medal.Point == 1 {
				continue
			}
			m := mongo.NewUpdateOneModel()
			m.SetFilter(bson.M{"_id": medal.OID, "t_updated_time": bson.M{"$lt": lastDay}})
			m.SetUpdate(bson.M{
				"$set": bson.M{
					"t_reduce_point": -medal.Point + 1,
					"updated_time":   goutil.TimeNow(),
				},
				"$inc": bson.M{"point": -medal.Point + 1},
			})
			updates = append(updates, m)
		} else {
			deletedMedals = append(deletedMedals, medal)
			removeOIDs = append(removeOIDs, medal.OID)
		}
	}
	var reduceMedalCount int
	for dailyReduction, oids := range reductionMap {
		updateCount := len(oids)
		reduceMedalCount += updateCount
		var batchUpdateOIDs []primitive.ObjectID
		// 因为 in 性能问题把 oids 按 updatePatchSize 100 个再次进行分组进行 update
		for offset := 0; offset < updateCount; offset += updatePatchSize {
			leftBorder, rightBorder := offset, offset+updatePatchSize
			if rightBorder >= updateCount {
				batchUpdateOIDs = oids[leftBorder:]
			} else {
				batchUpdateOIDs = oids[leftBorder:rightBorder]
			}
			m := mongo.NewUpdateManyModel()
			m.SetFilter(bson.M{
				"_id":            bson.M{"$in": batchUpdateOIDs},
				"t_updated_time": bson.M{"$lt": lastDay}},
			)
			m.SetUpdate(
				bson.M{
					"$set": bson.M{
						"t_reduce_point": -dailyReduction,
						"updated_time":   goutil.TimeNow(),
					},
					"$inc": bson.M{"point": -dailyReduction},
				})
			updates = append(updates, m)
		}
	}

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemedal.Collection()
	if len(updates) > 0 {
		_, err = col.BulkWrite(ctx, updates, options.BulkWrite().SetOrdered(false)) // 无序执行
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		logger.Infof("medal point decline, reduce medal count: %d, batch count: %d", reduceMedalCount, len(updates))
	}
	if len(removeOIDs) > 0 {
		_, err = col.DeleteMany(ctx, bson.M{
			"_id":            bson.M{"$in": removeOIDs},
			"t_updated_time": bson.M{"$lt": lastDay}})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		logger.Infof("medal point decline, delete medal count: %d", len(removeOIDs))
	}
	goutil.Go(func() {
		if err = livemedalremoverecord.AddRemoveRecords(livemedalremoverecord.TypeRemoveDecline, deletedMedals...); err != nil {
			logger.Error(err)
			// PASS
		}
		for _, medal := range deletedMedals {
			livemedal.SendLiveMedalChange(medal.UserID, medal.RoomID, livemedal.ChangeTypeRemove, livemedal.ChangeReasonLoss, livemedal.ChangeSourceDecline)
		}
		sendMedalSystemNotify(beginDecreaseMedals, deletedMedals)
	})
	return nil
}

func sendMedalSystemNotify(beginDecreaseMedals, deletedMedals []*livemedal.LiveMedal) {
	if len(beginDecreaseMedals) == 0 && len(deletedMedals) == 0 {
		return
	}
	maxLength := len(beginDecreaseMedals) + len(deletedMedals)
	creatorIDs := make([]int64, 0, maxLength)
	for i := range beginDecreaseMedals {
		creatorIDs = append(creatorIDs, beginDecreaseMedals[i].CreatorID)
	}
	for i := range deletedMedals {
		creatorIDs = append(creatorIDs, deletedMedals[i].CreatorID)
	}
	creators, err := mowangskuser.FindSimpleMap(creatorIDs) // 方法中已去重
	if err != nil {
		logger.Error(err)
		return
	}
	sysMsgs := make([]pushservice.SystemMsg, 0, maxLength)
	for i := range beginDecreaseMedals {
		creator := creators[beginDecreaseMedals[i].CreatorID]
		if creator == nil || creator.Username == "" {
			continue
		}
		contentFmt := "您在%s直播间的粉丝勋章亲密度将从今天开始下降，若今日重新投喂礼物给主播，系统将免费返还今日下降的亲密度哦~ " +
			"<a href=\"%slive/%d\">点击此处前往直播间</a>"
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: beginDecreaseMedals[i].UserID,
			Title:  "粉丝勋章亲密度衰减提醒",
			Content: fmt.Sprintf(contentFmt, html.EscapeString(creator.Username),
				config.Conf.Params.URL.Live, beginDecreaseMedals[i].RoomID),
		})
	}

	nowFormatStr := goutil.TimeNow().Format(util.TimeFormatYMDHHMM)
	for i := range deletedMedals {
		creator := creators[deletedMedals[i].CreatorID]
		if creator == nil || creator.Username == "" {
			continue
		}
		contentFmt := "您在%s直播间的粉丝勋章亲密度已于 %s 下降到 0，勋章已失去，如需再次获取，可以重新对主播投喂哦~ " +
			"<a href=\"%slive/%d\">点击此处前往直播间</a>"
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: deletedMedals[i].UserID,
			Title:  "粉丝勋章失去提醒",
			Content: fmt.Sprintf(contentFmt, html.EscapeString(creator.Username), nowFormatStr,
				config.Conf.Params.URL.Live, deletedMedals[i].RoomID),
		})
	}

	// 发送系统通知
	err = service.PushService.SendSystemMsgWithOptions(sysMsgs, &pushservice.SystemMsgOptions{
		DisableHTMLEscape: true,
	})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	logger.Infof("已发送 %d 条亲密度衰减系统通知；%d 条勋章删除通知", len(beginDecreaseMedals), len(deletedMedals))
}

type notifyMonthlyParam struct {
	NotifyType int `json:"notify_type"`

	notifyQueue []*userapi.BroadcastElem
	notifyFunc  func() error
}

// ActionCronNotifyMonthlyRank 主播月榜飘屏通知
/**
 * @api {post} /rpc/cron/rank/monthly/notify 主播月榜飘屏通知
 * @apiDescription 运行周期：1 0 23,0 1,28,29,30,31 * *
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number=1,2} [notify_type=0,1,2] notify_type 通知类型，默认根据时间自动判断，1：结束前通知（本月最后一天的 23:00 发生），2：结束后（新的一个月的第一天的 0:00 发生）
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 *
 */
func ActionCronNotifyMonthlyRank(c *handler.Context) (handler.ActionResponse, error) {
	var param notifyMonthlyParam
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	// 默认自行判断时间
	param.checkRunTime()
	switch param.NotifyType {
	case notifyTypeDefault:
		return "无需执行", nil
	case notifyTypeBeforeEndOfMonth:
		param.notifyFunc = param.notifyBeforeEnd
	case notifyTypeAfterEndOfMonth:
		param.notifyFunc = param.notiftAfterEnd
	default:
		return nil, actionerrors.ErrParams
	}

	err = param.notifyFunc()
	if err != nil {
		return nil, err
	}

	return "success", nil
}

func (param *notifyMonthlyParam) checkRunTime() {
	if param.NotifyType != notifyTypeDefault {
		return
	}

	now := goutil.TimeNow()
	beginingOfMonth := util.BeginningOfMonth(now)
	// 24 <= now < 25
	if goutil.TimeGte(now, beginingOfMonth) && now.Before(beginingOfMonth.Add(time.Hour)) {
		param.NotifyType = notifyTypeAfterEndOfMonth
		return
	}
	endOfMonth := util.EndOfMonth(now)
	// 22 <= now < 24
	if goutil.TimeGte(now, endOfMonth.Add(2*-time.Hour)) && now.Before(endOfMonth) {
		param.NotifyType = notifyTypeBeforeEndOfMonth
		return
	}
}

func (param *notifyMonthlyParam) notifyBeforeEnd() error {
	b, err := bubble.FindSimple(bubbleIDMonthlyBeforeEnd)
	if err != nil {
		logger.WithField("bubble_id", bubbleIDMonthlyBeforeEnd).Error(err)
		// PASS
	}

	format := map[string]string{
		"normal_color":    "#FFFFFF",
		"highlight_color": "#F87655",
	}
	if b != nil {
		b.AppendFormatParams(format)
	}
	message := `<font color="${normal_color}">距本月</font>` +
		`<font color="${highlight_color}">巅峰月榜</font>` +
		`<font color="${normal_color}">统计截止仅剩 60 分钟</font>`
	// 无需支持点击进入直播间，只是全站通知，roomID 为 0
	param.notifyQueue = []*userapi.BroadcastElem{
		{
			Type:    liveim.IMMessageTypeAll,
			Payload: notifymessages.NewGeneral(0, goutil.FormatMessage(message, format), b),
		},
	}
	return param.broadcastAll()
}

func buildEffectURI(base string, exts []string) string {
	strs := make([]string, len(exts))
	for i, ext := range exts {
		strs[i] = storage.ParseSchemeURL(base + "." + ext)
	}

	return strings.Join(strs, ";")
}

func (param *notifyMonthlyParam) notiftAfterEnd() error {
	now := goutil.TimeNow().AddDate(0, -1, 0)
	key := usersrank.Key(usersrank.TypeMonth, now)
	result, err := service.Redis.ZRevRangeWithScores(key, 0, 0).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	lastMonthNumber := int(now.Month())
	if len(result) == 0 {
		// 不存在月榜
		logger.WithField("month", lastMonthNumber).Warn("该月无月榜")
		return nil
	}

	userID, err := strconv.ParseInt(result[0].Member.(string), 10, 64)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	r, err := room.FindOne(bson.M{"creator_id": userID}, &room.FindOptions{FindCreator: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return actionerrors.ErrCannotFindRoom
	}

	b, err := bubble.FindSimple(bubbleIDMonthlyAfterEnd)
	if err != nil {
		logger.WithField("bubble_id", bubbleIDMonthlyAfterEnd).Error(err)
		// PASS
	}

	format := map[string]string{
		"creator_username": html.EscapeString(r.CreatorUsername),
		"month":            strconv.Itoa(lastMonthNumber),
		"normal_color":     "#FFFFFF",
		"highlight_color":  "#FFE4A0",
	}
	if b != nil {
		b.AppendFormatParams(format)
	}
	message := `<font color="${normal_color}">恭喜</font> ` +
		`<font color="${highlight_color}">${creator_username}</font> ` +
		`<font color="${normal_color}">荣登 ${month} 月</font>` +
		`<font color="${highlight_color}">巅峰月榜 TOP1！</font>`
	effect := liveim.Effect{
		EffectURL:    buildEffectURI("oss://live/period/effects/monthlytop1", []string{"mp4", "png"}),
		WebEffectURL: buildEffectURI("oss://live/period/effects/monthlytop1-web", []string{"mp4", "webm", "png"}),
	}
	general := notifymessages.NewPriorityNotify(r.RoomID, goutil.FormatMessage(message, format), b)
	general.Effect = &effect
	general.EffectShow = notifymessages.EffectShowRoomJump
	param.notifyQueue = []*userapi.BroadcastElem{
		{
			Type:    liveim.IMMessageTypeAll,
			RoomID:  r.RoomID,
			Payload: general,
		},
		// 月榜第一的主播直播间，播放月榜第一的特效
		// TODO: 后续支持 notify 消息带特效的版本覆盖上来后，调整为 RoomNormal 模式，并删除单独的特效下发
		{
			Type:    liveim.IMMessageTypeNormal,
			RoomID:  r.RoomID,
			Payload: liveim.NewRoomEffect(r.RoomID, effect),
		},
	}
	return param.broadcastAll()
}

func (param *notifyMonthlyParam) broadcastAll() error {
	err := userapi.BroadcastMany(param.notifyQueue)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

// ActionCronLastHourTop3Notify 广播小时榜 top3
/**
 * @api {post} /rpc/cron/rank/hourly/lasthourtop3/notify 广播小时榜 top3
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 * @apiSuccessExample {json} WebSocket 房间内消息，只有上小时榜 top3 的房间才有
 *     {
 *       "type": "creator",
 *       "event": "last_hour_rank",
 *       "room_id": 347142109,
 *       "rank_type": 4,
 *       "rank": 1,
 *       "start_time": "16:00",
 *       "end_time": "17:00",
 *       "frame_url": "https://static.maoercdn.com/frame.png", // 为空或读取异常使用本地默认图
 *       "top_users": [
 *         {
 *           "rank": 1,
 *           "user_id": 12,
 *           "username": "零月",
 *           "iconurl": "http://static-test.missevan.com/profile/201507/08/611c58b81a28c44819276224dac0cdb1015739.png",
 *           "revenue": 19
 *         },
 *         {
 *           "rank": 2,
 *           "user_id": 13,
 *           "username": "yyy",
 *           "iconurl": "http://static-test.missevan.com/profile/201507/08/xxx.png",
 *           "revenue": 18
 *         },
 *         {
 *           "rank": 3,
 *           "user_id": 0,
 *           "username": "神秘人",
 *           "iconurl": "https://static-test.missevan.com/avatars/invisible.png",
 *           "revenue": 17
 *         }
 *       ]
 *     }
 *
 * @apiSuccessExample {json} WebSocket 全局消息，上小时榜第一飘屏
 *     {
 *       "type": "notify",
 *       "notify_type": "creator",
 *       "event": "last_hour_rank",
 *       "room_id": 347142109,
 *       "rank_type": 4,
 *       "rank": 1,
 *       "start_time": "16:00",
 *       "end_time": "17:00",
 *       "message": "恭喜主播 \u003cb\u003eAyaka\u003c/b\u003e 获得 16:00-17:00 小时榜第一名！快来围观吧~",
 *       "notify_bubble": {
 *         "type": "custom",
 *         "bubble_id": 3,
 *         "image_url": "https://static-test.missevan.com/live/bubbles/b3_0_50_0_86.png"
 *       }
 *     }
 */
func ActionCronLastHourTop3Notify(c *handler.Context) (handler.ActionResponse, error) {
	top3Rooms, err := lastHourTop3Rooms()
	if err != nil {
		return nil, err
	}
	if len(top3Rooms) == 0 {
		return "empty", nil
	}
	top3RoomIDs := make([]int64, len(top3Rooms))
	for i, v := range top3Rooms {
		top3RoomIDs[i] = v.RoomID
	}
	logger.Infof("Top 3 room ids: %v", top3RoomIDs)
	hourStart, hourEnd := lastHourStartAndEnd()
	hourStartStr, hourEndStr := hourStart.Format("15:00"), hourEnd.Format("15:00")
	// 前三房间内通知 + 第一飘屏
	top3Notify := make([]*userapi.BroadcastElem, 0, len(top3Rooms)+1)
	param, err := params.FindRank()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	for _, r := range top3Rooms {
		if r.Rank == 1 {
			// 上小时榜第一发全站广播
			na := NotifyAttach{
				Type:        liveim.TypeNotify,
				NotifyType:  liveim.TypeCreator,
				Event:       liveim.EventLastHourRank,
				NotifyQueue: liveim.NotifyQueuePriority,
				RoomID:      r.Room.RoomID,
				RankType:    roomsrank.RankTypeHourly,
				Rank:        r.Rank,
				StartTime:   hourStartStr,
				EndTime:     hourEndStr,
				Message: fmt.Sprintf(`恭喜主播 <b>%s</b> 获得 %s-%s 小时榜第一名！快来围观吧~`,
					html.EscapeString(r.Room.CreatorUsername), hourStartStr, hourEndStr),
			}
			na.Message, na.NotifyBubble =
				param.HourTop1NotifyInfo(r.Room.CreatorUsername, hourStartStr, hourEndStr)
			top3Notify = append(top3Notify, &userapi.BroadcastElem{
				Type:    liveim.IMMessageTypeAll,
				RoomID:  r.Room.RoomID,
				Payload: na,
			})
		}
		top3Notify = append(top3Notify, &userapi.BroadcastElem{
			Type:   liveim.IMMessageTypeNormal,
			RoomID: r.Room.RoomID,
			Payload: NotifyAttach{
				Type:      liveim.TypeCreator,
				Event:     liveim.EventLastHourRank,
				RoomID:    r.Room.RoomID,
				RankType:  roomsrank.RankTypeHourly,
				Rank:      r.Rank,
				StartTime: hourStartStr,
				EndTime:   hourEndStr,
				FrameURL:  param.HourTop3FrameURL(r.Rank),
				TopUsers:  r.Top3Users,
			}})
	}

	err = userapi.BroadcastMany(top3Notify)
	if err != nil {
		logger.Errorf("broadcast many error: %v", err)
		// PASS
	}

	return "success", nil
}

func lastHourStartAndEnd() (time.Time, time.Time) {
	now := goutil.TimeNow()
	lastHour := now.Add(-time.Hour)

	hourEnd := time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), 0, 0, 0, now.Location())
	hourStart := time.Date(lastHour.Year(), lastHour.Month(), lastHour.Day(), lastHour.Hour(), 0, 0, 0, lastHour.Location())
	return hourStart, hourEnd
}

// NotifyAttach 通知内容
type NotifyAttach struct {
	Type         string          `json:"type"`
	NotifyType   string          `json:"notify_type,omitempty"`
	Event        string          `json:"event"`
	NotifyQueue  int             `json:"notify_queue,omitempty"`
	RoomID       int64           `json:"room_id"`
	RankType     int64           `json:"rank_type"`
	Rank         int             `json:"rank"`
	StartTime    string          `json:"start_time"`
	EndTime      string          `json:"end_time"`
	FrameURL     string          `json:"frame_url,omitempty"`
	Message      string          `json:"message,omitempty"`
	NotifyBubble *bubble.Simple  `json:"notify_bubble,omitempty"`
	TopUsers     []*UserRankInfo `json:"top_users,omitempty"`
}

type roomRankInfo struct {
	Rank      int
	UserID    int64
	Revenue   int64
	RoomID    int64
	Room      *room.Room
	Top3Users []*UserRankInfo
}

// lastHourTop3Rooms 获取上小时榜前 3 的直播间
// TODO: 后续需要将该函数移动到 roomsrank 包里
func lastHourTop3Rooms() ([]*roomRankInfo, error) {
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow().Add(-time.Hour))
	val, err := service.Redis.ZRevRangeWithScores(key, 0, 2).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	top3CreatorIDs := make([]int64, 0, len(val))

	top3Info := make([]*roomRankInfo, 0, 3)
	for i := range val {
		userID, err := strconv.ParseInt(val[i].Member.(string), 10, 64)
		if err != nil {
			logger.Error(err)
			// PASS
			continue
		}
		top3Info = append(top3Info, &roomRankInfo{
			Rank:    i + 1,
			UserID:  userID,
			Revenue: int64(val[i].Score),
		})
		top3CreatorIDs = append(top3CreatorIDs, userID)
	}
	if len(top3CreatorIDs) == 0 {
		return nil, nil
	}
	rooms, err := room.FindAll(bson.M{"creator_id": bson.M{"$in": top3CreatorIDs}})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	m := goutil.ToMap(rooms, "CreatorID").(map[int64]*room.Room)
	allTop3UserIDs := make([]int64, 0, len(top3Info)*3)
	for _, info := range top3Info {
		r := m[info.UserID]
		if r == nil {
			continue
		}
		top3Users, err := lastHourRankTop3Users(r.RoomID)
		if err != nil {
			logger.Error(err)
			// PASS
			continue
		}
		for _, u := range top3Users {
			allTop3UserIDs = append(allTop3UserIDs, u.UserID)
		}
		info.RoomID = r.RoomID
		info.Room = r
		info.Top3Users = top3Users
	}
	userMap := make(map[int64]*liveuser.Simple)
	if len(allTop3UserIDs) > 0 {
		userMap, err = liveuser.SimpleSliceToMap(liveuser.ListSimples(bson.M{"user_id": bson.M{"$in": allTop3UserIDs}}, nil))
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	for _, topRoom := range top3Info {
		usersInvisibleInfo := userstatus.RankInvisibleUsers(topRoom.Room.RoomID)
		for _, v := range topRoom.Top3Users {
			_, rankInvisible := usersInvisibleInfo[v.UserID]
			if rankInvisible {
				// TODO: 看看设置神秘人是否能写个工具函数，可以的话看看其他设置神秘人的地方是否都能用这个工具函数
				v.UserID = 0
				v.Username = "神秘人"
				v.IconURL = service.Storage.Parse(config.Conf.Params.NobleParams.InvisibleIcon)
				continue
			}
			if userMap[v.UserID] == nil {
				continue
			}
			v.Username = userMap[v.UserID].Username
			v.IconURL = userMap[v.UserID].IconURL
		}
	}
	return top3Info, nil
}

// UserRankInfo 小时榜上主播信息
type UserRankInfo struct {
	Rank     int    `json:"rank"`
	UserID   int64  `json:"user_id"`
	Username string `json:"username"`
	IconURL  string `json:"iconurl"`
	Revenue  int64  `json:"revenue"`
}

// lastHourRankTop3Users 上小时榜 top3
func lastHourRankTop3Users(roomID int64) ([]*UserRankInfo, error) {
	key := roomsrank.Key(roomID, roomsrank.RankTypeHourly, goutil.TimeNow().Add(-time.Hour))
	val, err := service.Redis.ZRevRangeWithScores(key, 0, 2).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	var r []*UserRankInfo
	for i := 0; i < len(val); i++ {
		userID, err := strconv.ParseInt(val[i].Member.(string), 10, 64)
		if err != nil {
			logger.Error(err)
			// PASS
			continue
		}
		r = append(r, &UserRankInfo{
			Rank:    i + 1,
			UserID:  userID,
			Revenue: int64(val[i].Score),
		})
	}
	return r, nil
}

// ActionCronRankNova 满足新人榜上榜条件的用户
/**
 * @api {post} /rpc/cron/rank/nova 满足新人榜上榜条件的用户
 * @apiDescription 跑两次为了防止漏主播, 运行周期：50,59 59 23 * * *
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionCronRankNova(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.BeginningOfDay(goutil.TimeNow().Add(2 * time.Minute)) // 允许提前两分钟执行
	rooms, err := room.FindAll(
		bson.M{"$or": bson.A{
			bson.M{"created_time": bson.M{"$gte": now.Add(-30 * 24 * time.Hour)}},                // 距离第一次开播小于等于 30 个自然日（含今天）
			bson.M{"statistics.revenue": bson.M{"$lte": room.NovaRevenueThreshold}},              // 累计直播总收入小于等于 10 万钻石
			bson.M{"statistics.total_duration": bson.M{"$lte": room.NovaTotalDurationThreshold}}, // 开播时长累计小于等于 120 个小时
		}}, options.Find().SetProjection(bson.M{"creator_id": 1}))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var slots [usersrank.KeyUsersNovaSlotNum][]interface{}
	insert := func(pipe redis.Pipeliner, slot int) {
		pipe.SAdd(usersrank.NovaKey(now, slot), slots[slot]...)
		slots[slot] = slots[slot][:0] // sadd 一次需清空一次切片中暂存的数据，方便后续累加新的数据
	}
	pipe := service.Redis.Pipeline() // 数据量过大，为避免批量命令占用时间过长，需要串行执行命令故不使用 TxPipeline
	for _, r := range rooms {
		slot := usersrank.NovaKeySlot(r.CreatorID)
		if slots[slot] == nil {
			slots[slot] = make([]interface{}, 0, 100)
		}
		slots[slot] = append(slots[slot], r.CreatorID)
		if len(slots[slot]) >= 100 {
			insert(pipe, slot)
		}
	}
	// 处理最后剩余的数据
	for i, s := range slots {
		if len(s) != 0 {
			insert(pipe, i)
		}
		serviceredis.ExpireAt(pipe, usersrank.NovaKey(now, i), usersrank.Deadline(usersrank.TypeNova, now))
	}
	if _, err := pipe.Exec(); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}
