package cron

import (
	"errors"
	"fmt"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionCronCleanup 移除过期需要清理的数据
/*
 * @api {post} /rpc/cron/cleanup 移除过期需要清理的数据
 * @apiDescription 清理过期的黑名单用户、直播间封禁信息，总是会成功，返回 success
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronCleanup(c *handler.Context) (handler.ActionResponse, error) {
	removeExpiredBlockUser()
	removeExpiredRoomBan()
	removeExpiredUserBan()
	removeCloseRoomScore()
	removeExpiredExtraScore()
	return "success", nil
}

func removeExpiredBlockUser() {
	result, err := service.Redis.ZRemRangeByScore(keys.KeyUsersBlockList0.Format(), "0",
		strconv.FormatInt(goutil.TimeNow().Unix(), 10)).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Infof("已移除 %d 个已过期的黑名单用户", result)
}

func removeExpiredRoomBan() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"ban.type": livemeta.TypeBanDuration, "ban.expire_time": bson.M{"$lt": goutil.TimeNow().Unix()}}
	result, err := livemeta.Collection().UpdateMany(ctx, filter,
		bson.M{"$unset": bson.M{"ban": ""}})
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Infof("已移除 %d 个已过期的直播间封禁信息", result.ModifiedCount)
}

func removeExpiredUserBan() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"ban.type": userstatus.TypeBanDuration, "ban.expire_time": bson.M{"$lt": goutil.TimeNow().Unix()}}
	result, err := userstatus.UserMetaCollection().UpdateMany(ctx, filter,
		bson.M{"$unset": bson.M{"ban": ""}})
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Infof("已移除 %d 个已过期的用户封禁信息", result.ModifiedCount)
}

// 清理关闭房间热度
func removeCloseRoomScore() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"status.open": room.StatusOpenFalse,
		"status.score": bson.M{"$ne": 0}}
	result, err := room.Collection().UpdateMany(ctx, filter,
		bson.M{"$set": bson.M{"status.score": 0}})
	if err != nil {
		logger.Error(err)
		return
	}
	logger.Infof("已清理 %d 个已关闭房间的热度", result.ModifiedCount)
}

// 清理过期的直播间奖励热度
func removeExpiredExtraScore() {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"extra_score.end_time": bson.M{"$lt": goutil.TimeNow().Unix()}}
	result, err := livemeta.Collection().UpdateMany(ctx, filter,
		bson.M{"$pull": bson.M{"extra_score": bson.M{"end_time": bson.M{"$lt": goutil.TimeNow().Unix()}}}})
	if err != nil {
		logger.Error(err)
		return
	}

	filter = bson.M{"extra_score": bson.M{"$size": 0}}
	_, err = livemeta.Collection().UpdateMany(ctx, filter,
		bson.M{"$unset": bson.M{"extra_score": ""}})
	if err != nil {
		logger.Error(err)
		return
	}

	logger.Infof("已清理 %d 个直播间的过期奖励热度", result.ModifiedCount)
}

const birthdayPrivAvailableLevel = 45

// ActionCronSendBirthdayPriv 为用户（45 级以上）发放生日礼物
// 运行周期：0 0 0 * * ?
/**
 * @api {post} /rpc/cron/user/birthdaypriv 为用户发放生日礼物
 * @apiDescription 为用户（45 级以上）发放生日礼物，返回 success
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "今日为 2 名用户发放生日特权"
 *     }
 */
func ActionCronSendBirthdayPriv(c *handler.Context) (handler.ActionResponse, error) {
	// 获取生日特权外观
	appearances, err := appearance.GetBirthdayAppearances()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(appearances) == 0 {
		return nil, actionerrors.NewErrServerInternal(errors.New("生日特权外观缺失"), nil)
	}
	// 筛选今日生日的用户
	birthdayUserIDs, err := user.FindUserIDsByBirthday(goutil.TimeNow(), user.LeapYearLookForward)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(birthdayUserIDs) == 0 {
		logger.Info("今日没有过生日的用户")
		return "今日没有过生日的用户", nil
	}
	selectedUserIDs, err := selectByUserLevel(birthdayUserIDs, birthdayPrivAvailableLevel)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	unrewardedUserIDs, err := filterUnrewarded(selectedUserIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(unrewardedUserIDs) == 0 {
		logger.Info("今日没有可发放生日特权的用户")
		return "今日没有可发放生日特权的用户", nil
	}
	// 记录发放
	err = livebirthdayprivrecord.BatchRecordUserBirthday(unrewardedUserIDs, goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 发放特权外观
	err = SendBirthdayAppearances(unrewardedUserIDs, appearances)
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_ids": unrewardedUserIDs,
		}).Errorf("生日特权外观发放错误：%v", err)
		// PASS
		return fmt.Sprintf("生日特权外观发放错误：%v", err), nil
	}
	logger.Infof("今日为 %d 名用户发放生日特权", len(unrewardedUserIDs))
	return fmt.Sprintf("今日为 %d 名用户发放生日特权", len(unrewardedUserIDs)), nil
}

// 选取特定等级以上的用户
func selectByUserLevel(userIDs []int64, userLevel int) ([]int64, error) {
	targetUserIDs := make([]int64, 0, len(userIDs))
	targetContribution := usercommon.LevelStart[userLevel-1]
	maxSearchLen := 1000
	for start := 0; start < len(userIDs); start += maxSearchLen {
		end := min(int64(start+maxSearchLen), int64(len(userIDs)))
		ids := userIDs[start:end]
		filter := bson.M{
			"user_id":      bson.M{"$in": ids},
			"contribution": bson.M{"$gte": targetContribution},
		}
		simples, err := liveuser.ListSimples(filter, nil)
		if err != nil {
			return []int64{}, err
		}
		for _, s := range simples {
			targetUserIDs = append(targetUserIDs, s.UserID())
		}
	}
	return targetUserIDs, nil
}

// 筛选出今年未发放生日特权的用户
func filterUnrewarded(userIDs []int64) ([]int64, error) {
	rewardedUserIDs, err := livebirthdayprivrecord.FilterRewardedUserIDs(userIDs, goutil.TimeNow().Year())
	if err != nil {
		return nil, err
	}
	unrewardedUserIDs := util.SetDifferenceInt64(userIDs, rewardedUserIDs)
	return unrewardedUserIDs, nil
}

// SendBirthdayAppearances 发放生日外观特权
func SendBirthdayAppearances(userIDs []int64, appearances []*appearance.Appearance) error {
	expireTime := util.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, 1).Unix()
	// 批量添加外观
	err := userappearance.BatchAddAppearances(userIDs, 0, expireTime, appearances, true)
	if err != nil {
		return err
	}
	userappearance.ClearCache(userIDs...)
	return nil
}
