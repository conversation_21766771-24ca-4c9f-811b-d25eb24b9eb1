package cron

import (
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/activity"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionSuperFanRank 接收 rpc 消息后更新超粉榜单
/**
 * @api {post} /rpc/cron/user/superfanrank 接收 rpc 消息后更新超粉榜单
 * @apiDescription 接收 rpc 消息后更新超粉榜单
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 * {
 *   "code": 0,
 *   "info": "success"
 * }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionSuperFanRank(c *handler.Context) (handler.ActionResponse, error) {
	// TODO: 之后支持从配置读取
	key := activity.KeyRank(175, "super_fan")
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	cursor, err := livemedal.Collection().Aggregate(ctx, []bson.M{
		{
			"$match": bson.M{
				"status":                bson.M{"$gt": 0},
				"super_fan.expire_time": bson.M{"$gt": goutil.TimeNow().Unix()},
			},
		},
		{
			"$group": bson.M{
				"_id":   "$creator_id",
				"count": bson.M{"$sum": 1},
			},
		},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	defer cursor.Close(ctx)

	var res []struct {
		CreatorID int64 `bson:"_id"`
		Count     int64 `bson:"count"`
	}

	if err := cursor.All(ctx, &res); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if len(res) == 0 {
		err = service.Redis.Del(key).Err()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		return "success", nil
	}

	z := make([]*redis.Z, 0, len(res))
	for _, item := range res {
		z = append(z, &redis.Z{
			Score:  float64(item.Count),
			Member: item.CreatorID,
		})
	}

	pipe := service.Redis.TxPipeline()
	pipe.Del(key)
	pipe.ZAdd(key, z...)
	pipe.Expire(key, 15*24*time.Hour)
	_, err = pipe.Exec()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}
