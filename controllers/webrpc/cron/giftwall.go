package cron

import (
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionCronGiftWallPeriodExtend 礼物墙自动生成新周期
// 运行周期：0 30 23 * * 0
/**
 * @api {post} /rpc/cron/giftwall/period/extend 礼物墙自动生成新周期
 * @apiDescription 在当前周期即将结束新周期仍未创建时，使用当前周期信息创建新周期
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "创建周期成功"
 *     }
 */
func ActionCronGiftWallPeriodExtend(c *handler.Context) (handler.ActionResponse, error) {
	p, err := giftwall.CurrentPeriodInfo(goutil.TimeNow())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p == nil {
		return "当前没有生效周期", nil
	}
	if goutil.TimeNow().AddDate(0, 0, 1).Unix() <= p.EndTime {
		return "最多允许在当前周期即将结束的最后一天生成新周期", nil
	}

	newStartTime := time.Unix(p.EndTime, 0)
	newEndTime := newStartTime.Add(giftwall.PeriodDuration)
	period, err := giftwall.FindPeriods(newStartTime, newEndTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if period != nil {
		return "下周期已存在", nil
	}

	err = giftwall.CreatePeriod(newStartTime, newEndTime, p.ShowGiftIDs, p.Rewards)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "创建周期成功", nil
}
