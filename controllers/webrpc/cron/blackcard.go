package cron

import (
	"fmt"
	"time"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/blackcard/liveuserblackcard"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type blackCardParam struct {
	NowTime            int64 `json:"now_time"`
	expireTime         int64
	userIDBlackCardMap map[int64]*liveuserblackcard.UserBlackCardInfo // 到期或降级后的黑卡用户的黑卡信息 map
}

// ActionCronUserBlackCardExpire 黑卡到期、降级处理
// 运行周期：40 59 23 * * * 每天 23 点 59 分 40 秒执行
/**
 * @api {post} /rpc/cron/user/black-card/expire 黑卡到期、降级处理
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} [now_time] 当前时间，单位：秒
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "message": "黑卡到期、降级处理成功，总共处理 5 个用户",
 *     "data": null
 *   }
 */
func ActionCronUserBlackCardExpire(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newBlackCardParam(c)
	if err != nil {
		return nil, "", err
	}
	if param == nil {
		return nil, "今日无到期或降级的黑卡用户", nil
	}

	// 获取到期或降级的黑卡用户
	err = param.getExpiredUserBlackCard()
	if err != nil {
		return nil, "", err
	}

	// 清理黑卡权益
	param.clearBlackCardPrivileges()

	logStr := fmt.Sprintf("黑卡到期、降级处理成功，总共处理 %d 个用户", len(param.userIDBlackCardMap))
	logger.Info(logStr)
	return nil, logStr, nil
}

func newBlackCardParam(c *handler.Context) (*blackCardParam, error) {
	param := new(blackCardParam)
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.NowTime <= 0 {
		// 允许提前两分钟执行，超过两分钟会导致黑卡用户特权晚一天过期或降级
		param.NowTime = goutil.TimeNow().Add(2 * time.Minute).Unix()
	}
	now := time.Unix(param.NowTime, 0)
	_, _, d := now.Date()
	if d != 1 {
		// 每月一号才可能有黑卡过期和降级
		return nil, nil
	}
	param.expireTime = goutil.BeginningOfDay(now).Unix()

	return param, nil
}

// getExpiredUserBlackCard 获取到期或降级的黑卡用户
func (param *blackCardParam) getExpiredUserBlackCard() error {
	// 查询指定日期到期的用户
	userIDs, err := liveuserblackcard.FindUserIDsByExpireTime(param.expireTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userIDs) <= 0 {
		logger.Info("今日无到期或降级的黑卡用户")
		return nil
	}

	// 查询过期当日的黑卡用户中，还存在有效黑卡等级的用户黑卡记录
	userBlackCards, err := liveuserblackcard.FindUserBlackCardByUserIDs(userIDs, param.expireTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	userBlackCardLen := len(userBlackCards)
	userIDHighestLevelBlackCardMap := make(map[int64]*liveuserblackcard.UserBlackCardInfo, userBlackCardLen)
	if userBlackCardLen > 0 {
		// 用户未到期的黑卡中最高的等级 map
		for _, userBlackCard := range userBlackCards {
			highestLevelUserBlackCard, ok := userIDHighestLevelBlackCardMap[userBlackCard.UserID]
			if !ok {
				userIDHighestLevelBlackCardMap[userBlackCard.UserID] = userBlackCard
			} else if highestLevelUserBlackCard.Level < userBlackCard.Level {
				userIDHighestLevelBlackCardMap[userBlackCard.UserID] = userBlackCard
			}
		}
	}
	param.userIDBlackCardMap = make(map[int64]*liveuserblackcard.UserBlackCardInfo, len(userIDs))
	for _, userID := range userIDs {
		param.userIDBlackCardMap[userID] = nil
		highestLevelUserBlackCard, ok := userIDHighestLevelBlackCardMap[userID]
		if ok {
			param.userIDBlackCardMap[userID] = highestLevelUserBlackCard
		}
	}

	return nil
}

// clearBlackCardPrivileges 清理黑卡权益
func (param *blackCardParam) clearBlackCardPrivileges() {
	if len(param.userIDBlackCardMap) <= 0 {
		return
	}
	// 清理黑卡权益, 如果用户没有黑卡则清空，如果用户黑卡等级降级则清理高等级权益
	liveuserblackcard.ClearBlackCardPrivileges(param.userIDBlackCardMap)
}
