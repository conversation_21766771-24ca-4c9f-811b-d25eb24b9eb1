package cron

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/mysql/liveschedulerecord"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionRecommendScheduleRecord 首页资源位排期记录
// 运行周期：5 0 20 * * *
/*
 * @api {post} /rpc/cron/recommend/schedule/record 首页资源位排期记录
 * @apiDescription 每日 20:00:05 进行明日首页资源位排期的记录，历史数据可通过接口参数进行控制同步
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {String} [date] 例如 "2006-01-02", 不传默认同步明日的推荐数据
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionRecommendScheduleRecord(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		Date string `json:"date"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	var date time.Time
	now := goutil.TimeNow()
	if param.Date == "" {
		// 不传参数, 默认同步明天的推荐数据
		if now.Hour() < 20 {
			return "未到同步时间", nil
		}
		date = goutil.BeginningOfDay(now).AddDate(0, 0, 1)
	} else {
		// 传递参数，同步历史数据
		date, err = time.ParseInLocation(util.TimeFormatYMD, param.Date, time.Local)
		if err != nil || now.Before(date) {
			return nil, actionerrors.ErrParams
		}
	}
	err = recordLiveScheduleRecommend(date)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

func recordLiveScheduleRecommend(day time.Time) error {
	res, err := liverecommendedelements.ListScheduleDuration(day, day.AddDate(0, 0, 1))
	if err != nil {
		return err
	}
	roomIDs := make([]int64, 0, len(res))
	for i := range res {
		roomIDs = append(roomIDs, res[i].ElementID)
	}
	roomsMap := make(map[int64]*room.Room)
	if len(roomIDs) > 0 {
		rooms, err := room.FindAll(bson.M{"room_id": bson.M{"$in": roomIDs}})
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		roomsMap = goutil.ToMap(rooms, "RoomID").(map[int64]*room.Room)
	}

	data := make([]*liveschedulerecord.ScheduleRecord, len(res))
	now := goutil.TimeNow().Unix()
	for i := range res {
		data[i] = &liveschedulerecord.ScheduleRecord{
			CreateTime:   now,
			ModifiedTime: now,
			RoomID:       res[i].ElementID,
			RecommendID:  res[i].ID,
			Attr:         res[i].Attr,
			Day:          day.Unix(),
			StartTime:    *res[i].StartTime,
			ExpireTime:   res[i].ExpireTime,
		}
		if r := roomsMap[res[i].ElementID]; r != nil {
			data[i].CreatorID = r.CreatorID
			data[i].GuildID = r.GuildID
		}
	}
	return liveschedulerecord.RecordScheduleByDay(day.Unix(), data)
}
