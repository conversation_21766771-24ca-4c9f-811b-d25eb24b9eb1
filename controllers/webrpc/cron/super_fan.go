package cron

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	superFanExpireNoticeOneDay   = 1
	superFanExpireNoticeThreeDay = 3
)

const superFanRenewLinkTpl = "missevan://live/superfans/purchase?room_id=%d&goods_id=%d"

// ActionSuperFanExpireNotice 直播超粉到期提醒
// 运行周期：30 59 23 * * *
func ActionSuperFanExpireNotice(ctx *handler.Context) (handler.ActionResponse, error) {
	var wg sync.WaitGroup
	var lock sync.Mutex
	noticeErrMsg := make(map[int]string)
	wg.Add(2)

	goutil.Go(func() {
		defer wg.Done()

		err := sendSuperFanExpireNotice(superFanExpireNoticeOneDay)
		if err != nil {
			logger.Error(err)

			lock.Lock()
			defer lock.Unlock()
			noticeErrMsg[superFanExpireNoticeOneDay] = err.Error()
		}
	})

	goutil.Go(func() {
		defer wg.Done()

		err := sendSuperFanExpireNotice(superFanExpireNoticeThreeDay)
		if err != nil {
			logger.Error(err)

			lock.Lock()
			defer lock.Unlock()
			noticeErrMsg[superFanExpireNoticeThreeDay] = err.Error()
		}
	})
	wg.Wait()

	if len(noticeErrMsg) != 0 {
		return noticeErrMsg, nil
	}
	return "success", nil
}

func sendSuperFanExpireNotice(noticeDayNum int) error {
	superFans, err := getAboutToExpireSuperFans(noticeDayNum)
	if err != nil {
		return err
	}
	if len(superFans) == 0 {
		logger.Infof("超粉到期前 %d 天提醒 user_ids=[]", noticeDayNum)
		return nil
	}

	// 允许提前两分钟执行
	now := goutil.TimeNow().Add(2 * time.Minute)
	noticeTime := util.BeginningOfDay(now)
	expireTimeStr := noticeTime.AddDate(0, 0, noticeDayNum).Format(util.TimeFormatYMDHHMM)

	userIDs := make([]int64, len(superFans))
	sysMsgs := make([]pushservice.SystemMsg, len(superFans))
	for i, fan := range superFans {
		sysMsgs[i] = pushservice.SystemMsg{
			UserID: fan.UserID,
			Title:  fmt.Sprintf("%s超粉到期提示", fan.CreatorUsername),
			Content: fmt.Sprintf(
				`亲爱的用户，您在%s直播间开通的超级粉丝将在 %d 天后（%s）到期，为避免超粉特权失效，请您及时续费哦~ <a href="%s" target="_blank" style="color:#6D1806">点击立即续费</a>`,
				fan.CreatorUsername,
				noticeDayNum,
				expireTimeStr,
				fmt.Sprintf(superFanRenewLinkTpl, fan.RoomID, fan.GoodsID),
			),
		}
		userIDs[i] = fan.UserID
	}

	logger.Infof("超粉到期前 %d 天提醒 user_ids=%v", noticeDayNum, userIDs)
	return service.PushService.SendSystemMsg(sysMsgs)
}

type aboutToExpireSuperFan struct {
	GoodsID         int64  `gorm:"column:goods_id"`
	UserID          int64  `gorm:"column:buyer_id"`
	CreatorID       int64  `gorm:"column:seller_id"`
	CreatorUsername string `gorm:"column:-"`
	RoomID          int64  `gorm:"column:-"`
}

func getAboutToExpireSuperFans(noticeType int) (superFans []aboutToExpireSuperFan, err error) {
	// 允许提前两分钟执行
	now := goutil.TimeNow().Add(2 * time.Minute)
	y, m, d := now.Date()
	var startTime time.Time
	switch noticeType {
	case superFanExpireNoticeThreeDay:
		startTime = time.Date(y, m, d+3, 0, 0, 0, 0, now.Location())
	case superFanExpireNoticeOneDay:
		startTime = time.Date(y, m, d+1, 0, 0, 0, 0, now.Location())
	default:
		panic(errors.New("超粉到期提醒日期类型错误"))
	}

	err = livetxnorder.LiveTxnOrder{}.DB().
		Select("DISTINCT buyer_id, seller_id, goods_id").
		Where("goods_type = ? AND status = ?", livegoods.GoodsTypeSuperFan, livetxnorder.StatusSuccess).
		Where("expire_time BETWEEN ? AND ?", startTime.Unix(), startTime.AddDate(0, 0, 1).Unix()-1).
		Scan(&superFans).Error
	if err != nil {
		return
	}
	if len(superFans) == 0 {
		return
	}
	creatorIDs := make([]int64, len(superFans))
	for i, item := range superFans {
		creatorIDs[i] = item.CreatorID
	}
	var creators map[int64]*mowangskuser.Simple
	creators, err = mowangskuser.FindSimpleMap(creatorIDs)
	if err != nil {
		return
	}
	lives, err := live.FindLives(creatorIDs)
	if err != nil {
		return
	}
	creatorIDRoomIDMap := make(map[int64]int64, len(lives))
	for _, l := range lives {
		creatorIDRoomIDMap[l.UserID] = l.RoomID
	}

	for i, item := range superFans {
		if roomID := creatorIDRoomIDMap[item.CreatorID]; roomID != 0 {
			superFans[i].RoomID = roomID
		} else {
			logger.WithField("creator_id", item.CreatorID).Error("超粉：未找到对应的房间信息")
			// PASS
		}
		if u := creators[item.CreatorID]; u != nil {
			superFans[i].CreatorUsername = u.Username
		} else {
			logger.WithField("creator_id", item.CreatorID).Error("超粉：未找到对应的主播信息")
			// PASS
		}
	}

	return
}
