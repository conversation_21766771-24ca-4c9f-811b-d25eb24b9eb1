package cron

import (
	"net/http"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronGashaponWeeklyReward(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	key := usersrank.Key(usersrank.TypeGashaponWeek, goutil.TimeNow().AddDate(0, 0, -7))
	require.NoError(service.Redis.Del(key).Err())
	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	_, result, err := ActionCronGashaponWeeklyReward(c)
	require.NoError(err)
	assert.Equal("empty weekly rank", result)

	require.NoError(service.Redis.ZAdd(key, &redis.Z{Score: 1, Member: "12"}).Err())
	c = handler.NewTestContext(http.MethodPost, "", false, nil)
	_, result, err = ActionCronGashaponWeeklyReward(c)
	require.NoError(err)
	assert.Equal("success", result)
}

func TestGashaponRankReward_rewardTop3(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	creatorIDs := []int64{10}
	r, err := room.FindOneSimple(bson.M{"creator_id": creatorIDs[0]})
	require.NoError(err)
	require.NotNil(r)
	require.NoError(liverecommendedelements.TableLiveIcon(service.DB).Delete(nil, "element_id IN (?) AND element_type = ?", r.RoomID, liverecommendedelements.ElementLiveIcon).Error)

	p := &gashaponRankReward{
		top3CreatorIDs: creatorIDs,
		conf:           params.Gashapon{RewardLiveIconURLs: []string{"https://www.baidu.com/1.png", "https://www.baidu.com/2.png", "https://www.baidu.com/3.png"}},
	}
	p.rewardTop3()
	var count int64
	require.NoError(liverecommendedelements.TableLiveIcon(service.DB).
		Where("element_id = ? AND element_type = ?", r.RoomID, liverecommendedelements.ElementLiveIcon).
		Count(&count).Error)
	assert.EqualValues(1, count)
}
