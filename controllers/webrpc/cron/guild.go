package cron

import (
	"fmt"
	"html"
	"strconv"
	"sync"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildrate"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/exclusivecreator"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/mysql/guildscheduleapply"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/vcode"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/service/missevan/sso"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionGuildCronJob 公会定时任务
// 运行周期：0 59 23 * * *
func ActionGuildCronJob(c *handler.Context) (handler.ActionResponse, error) {
	var wg sync.WaitGroup
	wg.Add(6)

	goutil.Go(func() {
		defer wg.Done()

		// 平台替公会代发续约申请
		_, err := ActionCronSendRenewApplyment(c)
		if err != nil {
			logger.Error(err)
		}
	})

	goutil.Go(func() {
		defer wg.Done()

		// 续约申请未处理超时，自动续约
		_, err := ActionCronRenewExpiredApplyment(c)
		if err != nil {
			logger.Error(err)
		}
	})

	goutil.Go(func() {
		defer wg.Done()

		// 清退合约申请到期失效
		_, err := ActionCronExpelLive(c)
		if err != nil {
			logger.Error(err)
		}
	})

	goutil.Go(func() {
		defer wg.Done()

		// 降薪申请到期失效
		cronExpelEditRateApplication(c)
	})

	goutil.Go(func() {
		defer wg.Done()

		// 三方独家主播身份到期失效
		cronExpiredExclusiveCreator()
	})

	goutil.Go(func() {
		defer wg.Done()

		// 三方独家主播相关短信发送（三方独家主播、公会经纪人、公会长）
		cronExclusiveCreatorSendMsg()
	})

	wg.Wait()

	// 合约到期失效（在最后执行，避免先执行而影响到超时的续约申请进行自动续约）
	return ActionCronInvalidExpiredContract(c)
}

// ActionCronSendRenewApplyment 合约快到期，平台替公会代发续约申请
/*
 * @api {post} /rpc/cron/guild/renew/send 合约快到期，平台替公会代发续约申请
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronSendRenewApplyment(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	// 允许提前两分钟执行
	now = now.Add(2 * time.Minute)
	// 获取到期前 7 天的合约
	var contractList []livecontract.LiveContract
	err := service.DB.Table(livecontract.TableName()).
		Select("id, live_id, guild_id, contract_end, guild_name, guild_owner").
		Where("status = ? AND contract_end > ? AND contract_end <= ?",
			livecontract.StatusContracting, now.AddDate(0, 0, 6).Unix(), now.AddDate(0, 0, 7).Unix()).
		Scan(&contractList).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(contractList) == 0 {
		return "暂不需要平台替公会代发续约申请", nil
	}

	// 获取发过续约或含未处理的解约申请的合约
	allContractIDs := make([]int64, len(contractList))
	for i, item := range contractList {
		allContractIDs[i] = item.ID
	}
	var excludeContractIDs []int64
	err = service.DB.Table(contractapplyment.TableName()).
		Select("contract_id").
		Where("contract_id IN (?)", allContractIDs).
		Where("type IN (?) OR (status = ? AND type IN (?))",
			[]int64{contractapplyment.TypeLiveRenew, contractapplyment.TypeGuildRenew},
			contractapplyment.StatusPending,
			[]int64{contractapplyment.TypeGuildExpel, contractapplyment.TypeLiveTerminate}).
		Pluck("contract_id", &excludeContractIDs).Error

	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	excludeContractIDsMap := make(map[int64]bool, len(excludeContractIDs))
	for _, id := range excludeContractIDs {
		excludeContractIDsMap[id] = true
	}

	// 获取相关数据
	liveIDs := make([]int64, len(contractList))
	liveNames := make(map[int64]string, len(contractList))
	for i, contract := range contractList {
		liveIDs[i] = contract.LiveID
	}
	var liveInfos []*mowangskuser.Simple
	if liveInfos, err = mowangskuser.FindSimpleList(liveIDs); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, liveInfo := range liveInfos {
		liveNames[liveInfo.ID] = liveInfo.Username
	}

	// 代发续约申请
	contractLength := len(contractList) - len(excludeContractIDsMap)
	if contractLength <= 0 {
		return "暂不需要平台替公会代发续约申请", nil
	}
	logger.Infof("平台代发续约申请 live_ids=%v", liveIDs)

	// 发送系统通知
	renewContractDuration := contractapplyment.ContractDurationTwoYear
	if contractapplyment.IsNewContractDuration() {
		renewContractDuration = contractapplyment.ContractDurationFiveYear
	}
	var (
		renewApplymentList = make([]contractapplyment.ContractApplyment, 0, contractLength)
		systemMsgs         = make([]pushservice.SystemMsg, 0, 2*len(contractList))
		sendMsgLiveIDs     = make([]int64, 0, len(contractList))

		guildBackendApplyAddr = "公会工作台 > 签约管理 > 续约申请"
		liveBackendApplyAddr  = "主播工作台 > 申请 / 邀请记录 > 邀请记录"

		userContext = c.UserContext()
	)
	for _, contract := range contractList {
		if excludeContractIDsMap[contract.ID] {
			continue
		}
		agentID, err := guildagent.AgentID(contract.LiveID, contract.GuildID)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		renewApplymentList = append(renewApplymentList, contractapplyment.ContractApplyment{
			LiveID:    contract.LiveID,
			GuildID:   contract.GuildID,
			GuildName: contract.GuildName,
			AgentID:   agentID,

			Type:   contractapplyment.TypeGuildRenew,
			Status: contractapplyment.StatusPending,

			Rate:               guildrate.ApplymentRate(contract.Rate),
			ContractID:         contract.ID,
			ContractDuration:   renewContractDuration,
			ContractExpireTime: contractapplyment.GetContractEnd(time.Unix(contract.ContractEnd, 0), renewContractDuration).Unix(),

			ExpireTime:   contract.ContractEnd,
			Initiator:    contractapplyment.InitiatorGuild,
			CreateTime:   nowStamp,
			ModifiedTime: nowStamp,
		})
		sendMsgLiveIDs = append(sendMsgLiveIDs, contract.LiveID)
		systemMsgs = append(systemMsgs, pushservice.SystemMsg{
			UserID: contract.GuildOwner,
			Title:  "平台代发续约邀请",
			Content: fmt.Sprintf("您与主播 %s（MID：%d）的合约即将到期，平台已代您向主播发送时限为 %s的续约邀请，可在 %s 查看",
				html.EscapeString(liveNames[contract.LiveID]), contract.LiveID,
				contractapplyment.DurationLabelMap[renewContractDuration], html.EscapeString(guildBackendApplyAddr)),
		})
	}

	if err = servicedb.BatchInsert(service.DB, contractapplyment.TableName(), renewApplymentList); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// FIXME: servicedb.BatchInsert 不能回填记录 ID, 需要再次查询
	renewApplyList := make([]contractapplyment.ContractApplyment, 0, contractLength)
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 使用事务，确保查询落在主库
		return tx.Table(contractapplyment.TableName()).
			Where("live_id IN (?)", sendMsgLiveIDs).
			Where("type = ? AND status = ? AND initiator = ?",
				contractapplyment.TypeGuildRenew, contractapplyment.StatusPending, contractapplyment.InitiatorGuild).
			Find(&renewApplyList).Error
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	accList, err := service.SSO.ListUserInfo(sendMsgLiveIDs)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	accMap := goutil.ToMap(accList, "ID").(map[int64]*sso.Account)
	var sendNewMsg bool
	config.GetAB("enable_send_new_guild_sign_system_msg", &sendNewMsg)
	smsList := make([]pushservice.SMS, 0, len(contractList))
	for _, apply := range renewApplyList {
		var (
			title   string
			content string

			link          = fmt.Sprintf(config.Conf.Params.LiveURL.GuildApplyContract, apply.ID)
			expireTimeStr = time.Unix(apply.ExpireTime, 0).Format(util.TimeFormatYMDHHMM)
		)
		if sendNewMsg {
			title = "公会续约邀请"
			content = fmt.Sprintf("主播您好，公会【%s】邀请您续约，请在 %s 前处理。<a href=\"%s\">前往处理</a>",
				html.EscapeString(apply.GuildName), expireTimeStr, link)
		} else {
			title = "直播公会续约邀请"
			content = fmt.Sprintf("直播公会 %s 向您发出时限为 %s的续约邀请，合约到期前未处理，将自动解约，可在 %s 查看",
				html.EscapeString(apply.GuildName), contractapplyment.DurationLabelMap[renewContractDuration],
				html.EscapeString(liveBackendApplyAddr))
		}
		systemMsgs = append(systemMsgs, pushservice.SystemMsg{
			UserID:  apply.LiveID,
			Title:   title,
			Content: content,
		})

		if !sendNewMsg {
			continue
		}
		acc, ok := accMap[apply.LiveID]
		if !ok || acc.Mobile == "" {
			// 主播未绑定手机号，不发送短信
			continue
		}
		shortURL, err := userapi.ShortURL(userContext, link) // TODO: 批量获取短链接
		if err != nil {
			logger.WithField("live_id", apply.LiveID).Error(err)
			continue
		}
		smsList = append(smsList, pushservice.SMS{
			To:         fmt.Sprintf("+%d%s", acc.Region, acc.Mobile),
			RegionCode: acc.Region,
			Scene:      "guild_live_contract_renew",
			Payload: map[string]interface{}{
				"guild_name": apply.GuildName,
				"time":       expireTimeStr,
				"link":       shortURL,
			},
		})
	}
	if err = service.PushService.SendSystemMsgWithOptions(systemMsgs, &pushservice.SystemMsgOptions{
		DisableHTMLEscape: true}); err != nil {
		logger.Error(err)
		// PASS
	}
	if len(smsList) > 0 {
		err = service.PushService.SendSMSBatch(smsList)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	logger.Infof("代发续约邀请 %d 位主播, 共发送系统通知 %d 条, 短信 %d 条", len(sendMsgLiveIDs), len(systemMsgs), len(smsList))
	return "success", nil
}

// ActionCronRenewExpiredApplyment 续约申请未处理超时，自动续约
// TODO: 2021-07-01 00:00:00 后移除自动续约相关代码
/*
 * @api {post} /rpc/cron/guild/renew/exec 续约申请未处理超时，自动续约
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronRenewExpiredApplyment(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	// 允许提前两分钟执行
	now = now.Add(2 * time.Minute)

	if guildAutoRenewalCanceled(now.Unix()) {
		return "已取消自动续约功能", nil
	}
	// 获取超时未处理的续约申请列表
	tm := now.Unix()
	var applymentList []contractapplyment.ContractApplyment
	err := service.DB.
		Table(contractapplyment.TableName()+" AS c").
		Select("c.id, c.guild_id, c.guild_name, c.live_id, c.type, c.contract_expire_time, c.contract_duration").
		Where("c.type IN (?) AND c.status = ?",
			[]int64{contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew}, contractapplyment.StatusPending).
		Where("c.expire_time <= ? OR l.contract_end <= ?", tm, tm).
		Joins(fmt.Sprintf("LEFT JOIN %s AS l ON l.id = c.contract_id", livecontract.TableName())).
		Scan(&applymentList).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(applymentList) == 0 {
		return "没有续约申请未处理超时时需要自动续约", nil
	}

	// 处理相关数据
	liveIDs := make([]int64, len(applymentList))
	liveNames := make(map[int64]string, len(applymentList))
	guildIDs := make([]int64, len(applymentList))
	guildOwers := make([]int64, len(applymentList))
	applymentIDs := make([]int64, len(applymentList))
	for i, item := range applymentList {
		liveIDs[i] = item.LiveID
		guildIDs[i] = item.GuildID
		applymentIDs[i] = item.ID
	}
	logger.Infof("自动续约 applyment_ids=%v", applymentIDs)

	var liveInfos []*mowangskuser.Simple
	if liveInfos, err = mowangskuser.FindSimpleList(liveIDs); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, liveInfo := range liveInfos {
		liveNames[liveInfo.ID] = liveInfo.Username
	}

	var guildInfos []guild.Guild
	err = service.DB.Table(guild.TableName()).
		Select("id, user_id").
		Where("id IN (?)", guildIDs).Scan(&guildInfos).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i, guildID := range guildIDs {
		for _, guildInfo := range guildInfos {
			if guildID == guildInfo.ID {
				guildOwers[i] = guildInfo.UserID
				break
			}
		}
	}

	// 执行续约
	todayStr := now.Format(" 2006 年 01 月 02 日")
	// 发送系统通知
	y, m, d := now.Date()
	noticeTime := time.Date(y, m, d, 0, 0, 0, 0, time.Local)
	sysMsgBox := userapi.NewSystemMsgBox(noticeTime.Unix())
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		for i, applyment := range applymentList {
			// 续约申请生效
			err := tx.Table(contractapplyment.TableName()).
				Where("id = ?", applyment.ID).
				Updates(map[string]interface{}{"status": contractapplyment.StatusAgreed, "modified_time": nowStamp}).Error
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}

			// 续约
			err = tx.Table(livecontract.TableName()).
				Where("status = ? AND live_id = ? AND guild_id = ?", livecontract.StatusContracting, applyment.LiveID, applyment.GuildID).
				Updates(map[string]interface{}{
					"contract_end":      applyment.ContractExpireTime,
					"contract_duration": applyment.ContractDuration,
					// 续约后重置主播在合约期内是否申请过协商解约的状态
					"attr":          livecontract.UnsetAttrBitMaskExpr(livecontract.AttrBitMaskLiveTerminated),
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}

			// 系统通知
			var (
				recUID         int64
				title, content string
			)
			if applyment.Type == contractapplyment.TypeLiveRenew {
				recUID, title, content = applyment.LiveID, "您的续约申请已通过", fmt.Sprintf("直播公会 %s 已通过您的续约申请。您于%s与该直播公会成功续约。", applyment.GuildName, todayStr)
			} else {
				recUID, title, content = guildOwers[i], "您的续约邀请已通过", fmt.Sprintf("主播 %s（MID：%d）已通过您的直播公会续约邀请。您于%s与该主播成功续约。", liveNames[applyment.LiveID], applyment.LiveID, todayStr)
			}
			sysMsgBox.AddMessage(recUID, title, content)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	// 发送系统通知
	if err = sysMsgBox.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "success", nil
}

// ActionCronExpelLive 清退合约申请到期生效，公会清退主播
/*
 * @api {post} /rpc/cron/guild/terminate/exec 清退合约申请到期生效，公会清退主播
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronExpelLive(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	// 允许提前两分钟执行
	now = now.Add(2 * time.Minute)
	// 获取待生效的清退申请列表
	var applymentList []contractapplyment.ContractApplyment
	err := service.DB.
		Table(contractapplyment.TableName()).
		Select("guild_id, guild_name, live_id").
		Where("expire_time <= ? AND status = ? AND type = ?",
			now.Unix(), contractapplyment.StatusPending, contractapplyment.TypeGuildExpel).
		Scan(&applymentList).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(applymentList) == 0 {
		return "没有清退合约申请到期生效，不需要公会清退主播", nil
	}

	// 处理相关数据
	liveIDs := make([]int64, len(applymentList))
	liveNames := make(map[int64]string, len(applymentList))
	guildIDs := make([]int64, len(applymentList))
	guildOwners := make([]int64, len(applymentList))
	for i, item := range applymentList {
		liveIDs[i] = item.LiveID
		guildIDs[i] = item.GuildID
	}
	logger.Infof("公会清退主播 live_ids=%v", liveIDs)

	var liveInfos []*mowangskuser.Simple
	if liveInfos, err = mowangskuser.FindSimpleList(liveIDs); err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for _, liveInfo := range liveInfos {
		liveNames[liveInfo.ID] = liveInfo.Username
	}

	var guildInfos []guild.Guild
	err = service.DB.Table(guild.TableName()).
		Select("id, user_id").
		Where("id IN (?)", guildIDs).Scan(&guildInfos).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	for i, guildID := range guildIDs {
		for _, guildInfo := range guildInfos {
			if guildID == guildInfo.ID {
				guildOwners[i] = guildInfo.UserID
				break
			}
		}
	}

	// 执行清退
	// 发送系统通知
	y, m, d := now.Date()
	noticeTime := time.Date(y, m, d, 0, 0, 0, 0, time.Local)
	sysMsgBox := userapi.NewSystemMsgBox(noticeTime.Unix())
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 清退申请生效（允许提前两分钟执行）
		err := tx.Table(contractapplyment.TableName()).
			Where("status = ? AND type = ?", contractapplyment.StatusPending, contractapplyment.TypeGuildExpel).
			Where("expire_time <= ?", now.Unix()).
			Updates(map[string]interface{}{
				"status":        contractapplyment.StatusAgreed,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		// 清退主播
		err = tx.Table(livecontract.TableName()).
			Where("live_id IN (?)", liveIDs).
			Where("guild_id IN (?)", guildIDs).
			Where("status = ?", livecontract.StatusContracting).
			Updates(map[string]interface{}{
				"status":        livecontract.StatusUseless,
				"contract_end":  nowStamp,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		todayStr := now.Format(" 2006 年 01 月 02 日")
		for i, applyment := range applymentList {
			// 未处理的续约、协商解约、降薪申请失效
			err = tx.Table(contractapplyment.TableName()).
				Where("live_id = ? AND guild_id = ? AND status = ?", applyment.LiveID, applyment.GuildID, contractapplyment.StatusPending).
				Where("type IN (?)", []int64{contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew,
					contractapplyment.TypeLiveTerminate, contractapplyment.TypeRateDown}).
				Updates(map[string]interface{}{
					"status":        contractapplyment.StatusInvalid,
					"process_time":  nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				logger.Error(err)
				// PASS
			}
			// 更新主播数冗余字段
			err = guild.IncreaseLiveNum(applyment.GuildID, -1, tx)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			// TODO: 迁移到同一个库后使用 tx
			// 解除经纪人与主播的关系
			err = guildagent.Unassign(applyment.LiveID, applyment.GuildID)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			err = guildscheduleapply.AfterQuitGuild(applyment.GuildID, []int64{applyment.LiveID})
			if err != nil {
				logger.WithFields(logger.Fields{
					"guild_id":   applyment.GuildID,
					"creator_id": applyment.LiveID,
				}).Error(err)
				// PASS
			}

			// 主播通知
			sysMsgBox.AddMessage(applyment.LiveID, "公会已与您解约", fmt.Sprintf("很遗憾 _(:3 」∠)_ 直播公会 %s 于%s与您解约。", applymentList[i].GuildName, todayStr))
			// 公会通知
			sysMsgBox.AddMessage(guildOwners[i], "主播已被清退", fmt.Sprintf("主播 %s（MID：%d）已被清退。您于%s与该主播成功解约。", liveNames[applyment.LiveID], applyment.LiveID, todayStr))
		}
		return nil
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	// 发送系统通知
	if err = sysMsgBox.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
	return "success", nil
}

type guildContractCount struct {
	GuildID              int64 `gorm:"column:guild_id"`
	ExpiredContractCount int   `gorm:"column:contract_count"`
}

// ActionCronInvalidExpiredContract 合约到期失效
/*
 * @api {post} /rpc/cron/guild/applyment/expire 合约到期失效
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionCronInvalidExpiredContract(c *handler.Context) (handler.ActionResponse, error) {
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	// 允许提前两分钟执行
	offsetNow := now.Add(2 * time.Minute)
	// 获取到期的合约（允许提前两分钟执行）
	var guildInfos []guildContractCount
	err := service.DB.Table(livecontract.TableName()).
		Select("guild_id, COUNT(*) AS contract_count").
		Where("contract_end <= ? AND status = ?", offsetNow.Unix(), livecontract.StatusContracting).
		Group("guild_id").Scan(&guildInfos).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(guildInfos) == 0 {
		return "没有到期失效的合约", nil
	}
	var expiredContracts []livecontract.LiveContract
	err = livecontract.LiveContract{}.DB().
		Select("guild_id, live_id, guild_owner, guild_name").
		Where("contract_end <= ? AND status = ?", offsetNow.Unix(), livecontract.StatusContracting).
		Scan(&expiredContracts).Error
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	// 处理相关数据
	creatorIDs := make([]int64, len(expiredContracts))
	for i, item := range expiredContracts {
		creatorIDs[i] = item.LiveID
	}
	creatorMap, err := mowangskuser.FindSimpleMap(creatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	numGroup := make(map[int][]int64)
	for _, item := range guildInfos {
		numGroup[item.ExpiredContractCount] = append(numGroup[item.ExpiredContractCount], item.GuildID)
	}
	logger.Infof("合约到期失效 %v", numGroup)
	autoRenewalCanceled := guildAutoRenewalCanceled(offsetNow.Unix())
	sysMsgBox := userapi.NewSystemMsgBox(goutil.BeginningOfDay(offsetNow).Unix())
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		// 到期失效
		err = tx.Table(livecontract.TableName()).
			Where("contract_end <= ? AND status = ?", offsetNow.Unix(), livecontract.StatusContracting).
			Updates(map[string]interface{}{
				"status":        livecontract.StatusFinished,
				"modified_time": nowStamp,
			}).Error
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		// 更新主播数冗余字段
		for num, guildIDs := range numGroup {
			err = tx.Table(guild.TableName()).
				Where("id IN (?)", guildIDs).
				Updates(map[string]interface{}{
					"live_num":      gorm.Expr(fmt.Sprintf("GREATEST(live_num, %d) - %d", num, num)),
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
		todayStr := offsetNow.Format("2006 年 01 月 02 日")
		for _, item := range expiredContracts {
			// 未处理的续约、协商解约申请失效
			err = tx.Table(contractapplyment.TableName()).
				Where("live_id = ? AND guild_id = ? AND status = ?", item.LiveID, item.GuildID, contractapplyment.StatusPending).
				Where("type IN (?)", []int64{contractapplyment.TypeGuildRenew, contractapplyment.TypeLiveRenew, contractapplyment.TypeLiveTerminate, contractapplyment.TypeGuildExpel}).
				Updates(map[string]interface{}{
					"status":        contractapplyment.StatusOutdated,
					"process_time":  nowStamp,
					"modified_time": nowStamp,
				}).Error
			if err != nil {
				logger.Error(err)
				// PASS
			}

			// TODO: 迁移到同一个库后使用 tx
			agentID, err := guildagent.AgentID(item.LiveID, item.GuildID)
			if err != nil {
				logger.Error(err)
				// PASS
			}
			if agentID != 0 {
				// 解除经纪人与主播的关系
				err = guildagent.Unassign(item.LiveID, item.GuildID)
				if err != nil {
					logger.Error(err)
					// PASS
				}
			}

			err = guildscheduleapply.AfterQuitGuild(item.GuildID, []int64{item.LiveID})
			if err != nil {
				logger.WithFields(logger.Fields{
					"guild_id":   item.GuildID,
					"creator_id": item.LiveID,
				}).Error(err)
				// PASS
			}

			if autoRenewalCanceled {
				// 主播通知
				sysMsgBox.AddMessage(item.LiveID, "公会合约到期", fmt.Sprintf("您与直播公会 %s 的合约已于 %s到期，您已退出该公会。",
					html.EscapeString(item.GuildName), todayStr))
				// 公会会长通知
				if creatorInfo, ok := creatorMap[item.LiveID]; ok {
					title := "主播合约到期"
					guildMsg := fmt.Sprintf("主播 %s（MID：%d）与直播公会 %s 的合约于 %s到期，已退出公会。",
						html.EscapeString(creatorInfo.Username), item.LiveID, html.EscapeString(item.GuildName), todayStr)
					sysMsgBox.AddMessage(item.GuildOwner, title, guildMsg)
					if agentID != 0 && agentID != item.GuildOwner {
						// 公会经纪人通知
						sysMsgBox.AddMessage(agentID, title, guildMsg)
					}
				}
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	if len(creatorIDs) > 0 {
		// 将合约到期的主播的 guild_id 设置为 0
		err = room.SetGuildID(creatorIDs, 0)
		if err != nil {
			logger.WithFields(logger.Fields{
				"creator_ids": creatorIDs,
			}).Error(err)
			// PASS
		}
	}

	if len(sysMsgBox.Messages) > 0 {
		if err = sysMsgBox.Send(); err != nil {
			logger.Error(err)
			// PASS
		}
	}
	return "success", nil
}

func guildAutoRenewalCanceled(when int64) bool {
	var startTime int64
	config.GetAB("cancel_guild_auto_renewal_time", &startTime)
	if startTime == 0 {
		// 未配置开始时间，默认开始时间为 2021-07-01 00:00:00
		startTime = 1625068800
	}
	return when >= startTime
}

// cronExpelEditRateApplication 降薪申请到期失效
func cronExpelEditRateApplication(c *handler.Context) {
	now := goutil.TimeNow()
	nowStamp := now.Unix()

	// 允许提前两分钟执行
	beginningOfDay := goutil.BeginningOfDay(now.Add(2 * time.Minute))

	// 获取过期公会修改主播最低分成比例申请
	var applicationList []contractapplyment.ContractApplyment
	err := contractapplyment.ContractApplyment{}.DB().
		Where("type = ? AND status = ? AND expire_time <= ?",
			contractapplyment.TypeRateDown, contractapplyment.StatusPending, beginningOfDay.Unix()).
		Scan(&applicationList).Error
	if err != nil {
		logger.Error(err)
		return
	}
	if len(applicationList) == 0 {
		logger.Info("没有过期的降薪申请")
		return
	}

	// 获取用户名
	creatorIDs := make([]int64, len(applicationList))
	for i, item := range applicationList {
		creatorIDs[i] = item.LiveID
	}
	creatorMap, err := mowangskuser.FindSimpleMap(creatorIDs)
	if err != nil {
		logger.Error(err)
		return
	}

	// 处理相关数据
	var count int
	sysMessages := make([]pushservice.SystemMsg, 0, len(applicationList))
	for i := range applicationList {
		f := logger.Fields{
			"application_id": applicationList[i].ID,
			"creator_id":     applicationList[i].LiveID,
			"guild_id":       applicationList[i].GuildID,
		}
		err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
			// 更新申请表状态
			updateApp := map[string]interface{}{
				"status":        contractapplyment.StatusOutdated,
				"process_time":  nowStamp,
				"modified_time": nowStamp,
			}
			dbRes := tx.Table(contractapplyment.TableName()).
				Where("id = ? AND status = ?", applicationList[i].ID, contractapplyment.StatusPending).
				Updates(updateApp)
			if dbRes.Error != nil {
				return dbRes.Error
			}
			if dbRes.RowsAffected == 0 {
				return servicedb.ErrNoRowsAffected
			}

			// 更新合约表 attr 字段
			updateContract := map[string]interface{}{
				"modified_time": nowStamp,
				"attr":          livecontract.UnsetAttrBitMaskExpr(livecontract.AttrBitMaskLiveGuildRate),
			}
			if errContract := tx.Table(livecontract.TableName()).Where("id = ?", applicationList[i].ContractID).
				Update(updateContract).Error; errContract != nil {
				return errContract
			}
			return nil
		})
		if err != nil {
			logger.WithFields(f).Error(err)
			continue
		}

		logger.WithFields(f).Info("过期降薪申请失效")
		// 构建系统消息
		if creator, ok := creatorMap[applicationList[i].LiveID]; ok {
			// 获取申请发起前主播的最低分成比例
			more, err := applicationList[i].UnmarshalMore()
			if err != nil {
				logger.WithFields(f).Error(err)
				continue
			}
			if more == nil || more.Rate == nil {
				logger.WithFields(f).Error("申请发起前主播最低分成比例为空")
				continue
			}

			// 查询公会长 ID，若主播目前不属于该公会则不发送系统通知
			contract := livecontract.LiveContract{}
			err = contract.DB().Select("guild_owner").
				Where("id = ? AND status = ?", applicationList[i].ContractID, livecontract.StatusContracting).
				Find(&contract).Error
			if err != nil && !servicedb.IsErrNoRows(err) {
				logger.WithFields(f).Error(err)
				continue
			}
			if contract.GuildOwner > 0 {
				// 主播个人主页地址
				url := fmt.Sprintf("%s%d", config.Conf.Params.URL.Main, applicationList[i].LiveID)
				sysMessage := pushservice.SystemMsg{
					Title:  "主播超时未接受最低分成比例调整",
					UserID: contract.GuildOwner,
					Content: fmt.Sprintf("主播<a href=\"%s\">%s</a>超时未接受最低分成比例调整（%d%% → %d%%），可重新发出申请。",
						url, html.EscapeString(creator.Username),
						*more.Rate, applicationList[i].Rate),
				}
				sysMessages = append(sysMessages, sysMessage)
			}
		}
		count++
	}

	// 发送系统通知
	err = service.PushService.SendSystemMsgWithOptions(sysMessages,
		&pushservice.SystemMsgOptions{DisableHTMLEscape: true})
	if err != nil {
		logger.Error(err)
		// PASS
	}
	logger.WithFields(logger.Fields{"count": count}).Info("清理过期降薪申请完成")
}

const (
	sceneExclusiveAdd     = iota + 1 // 添加三方独家主播
	sceneExclusiveEdit               // 更新三方独家主播
	sceneExclusiveDeleted            // 移除三方独家主播
	sceneExclusiveExpired            // 三方独家主播身份到期
)

const (
	cronExpiredExclusiveType = iota // 三方独家身份到期
	cronExclusiveSendMsgType        // 给新增、修改或移除的三方独家主播发短信
)

type exclusiveInfo struct {
	exclusiveList []exclusivecreator.TripartiteExclusiveCreator // 三方独家主播列表
	exclusiveIDs  []int64                                       // 三方独家主播 ID
	agentMap      map[int64]*guildagent.AgentCreator            // 经纪人信息，以主播的用户 ID 为 key
	guildMap      map[int64]guild.IDName                        // 会长信息，以公会 ID 为 key
	userMap       map[int64]*sso.Account                        // 主播、会长和经纪人的账号信息，以用户 ID 为 key
}

type sendMsgParams struct {
	scene               int                                         // 短信发送场景
	creatorRegionMobile string                                      // 主播手机号
	guildRegionMobile   string                                      // 会长手机号
	agentRegionMobile   string                                      // 经纪人手机号
	exclusive           exclusivecreator.TripartiteExclusiveCreator // 三方独家主播信息
	guild               guild.IDName                                // 公会信息
	user                *sso.Account                                // 主播账号信息
}

// 三方独家主播身份到期
func cronExpiredExclusiveCreator() {
	now := goutil.TimeNow()
	nowStamp := now.Unix()
	// 允许提前两分钟执行
	beginningOfDayTime := goutil.BeginningOfDay(now.Add(2 * time.Minute))
	beginningOfDay := beginningOfDayTime.Unix()
	beginningOfYesterday := goutil.BeginningOfDay(beginningOfDayTime.AddDate(0, 0, -1)).Unix()

	ei, err := findExclusiveInfo(cronExpiredExclusiveType, beginningOfDay, beginningOfYesterday)
	if err != nil {
		logger.Error(err)
		return
	}
	if ei == nil {
		logger.Info("没有到期的三方独家主播")
		return
	}
	if ei.userMap == nil || ei.guildMap == nil {
		return
	}

	// 更新三方独家主播状态为身份到期
	db := exclusivecreator.TripartiteExclusiveCreator{}.DB().
		Where("id IN (?) AND status = ? AND contract_end <= ?",
			ei.exclusiveIDs, exclusivecreator.StatusValid, beginningOfDay).
		Updates(map[string]interface{}{
			"status":        exclusivecreator.StatusExpired,
			"modified_time": nowStamp,
		})
	if db.Error != nil {
		logger.Error(db.Error)
		return
	}
	if db.RowsAffected == 0 {
		logger.Error("更新失败，请检查三方独家主播身份是否到期或不存在")
		return
	}

	sysMsgs := make([]pushservice.SystemMsg, 0, len(ei.exclusiveList)*3)
	smps := make([]sendMsgParams, 0, len(ei.exclusiveList))
	for _, exclusive := range ei.exclusiveList {
		user, ok := ei.userMap[exclusive.CreatorID]
		if !ok || user == nil {
			continue
		}

		g, ok := ei.guildMap[exclusive.GuildID]
		if !ok {
			continue
		}
		guildUser, ok := ei.userMap[g.UserID]
		if !ok || guildUser == nil {
			continue
		}

		smp := sendMsgParams{
			scene:     sceneExclusiveExpired,
			guild:     g,
			user:      user,
			exclusive: exclusive,
		}
		smp.creatorRegionMobile, err = regionMobile(strconv.Itoa(user.Region), user.Mobile)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		smp.guildRegionMobile, err = regionMobile(strconv.Itoa(guildUser.Region), guildUser.Mobile)
		if err != nil {
			logger.Error(err)
			// PASS
		}

		// 主播系统消息内容
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID: user.ID,
			Title:  "您的三方独家主播身份已失效",
			Content: fmt.Sprintf("您的三方独家主播身份已于 %s 失效，请知悉。",
				time.Unix(exclusive.ContractEnd, 0).Format(util.TimeFormatYMDHMS)),
		})
		// 公会长系统消息内容
		title := fmt.Sprintf("主播%s的三方独家主播身份已失效", user.Username)
		content := fmt.Sprintf("主播%s的三方独家主播身份已于 %s 失效，请知悉。",
			user.Username, time.Unix(exclusive.ContractEnd, 0).Format(util.TimeFormatYMDHMS))
		sysMsgs = append(sysMsgs, pushservice.SystemMsg{
			UserID:  g.UserID,
			Title:   title,
			Content: content,
		})

		if ei.agentMap == nil {
			smps = append(smps, smp)
			continue
		}
		if agent, ok := ei.agentMap[exclusive.CreatorID]; ok {
			agentUser, ok := ei.userMap[agent.AgentID]
			// 若公会长也是该主播的经纪人，发送一次短信和系统通知即可
			if ok && agentUser != nil && agent.AgentID != g.UserID {
				// 经纪人系统消息内容
				sysMsgs = append(sysMsgs, pushservice.SystemMsg{
					UserID:  agent.AgentID,
					Title:   title,
					Content: content,
				})
				smp.agentRegionMobile, err = regionMobile(strconv.Itoa(agentUser.Region), agentUser.Mobile)
				if err != nil {
					logger.Error(err)
					// PASS
				}
			}
		}
		smps = append(smps, smp)
	}

	// 发送系统通知
	err = service.PushService.SendSystemMsgWithOptions(sysMsgs, nil)
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 给主播、会长和经纪人发短信
	sendMsgByScene(smps)

	logger.WithFields(logger.Fields{"count": len(ei.exclusiveList)}).Info("三方独家主播身份到期失效完成")
}

// 给新增、修改或移除的三方独家主播发短信
func cronExclusiveCreatorSendMsg() {
	now := goutil.TimeNow()
	// 允许提前两分钟执行
	beginningOfDayTime := goutil.BeginningOfDay(now.Add(2 * time.Minute))
	beginningOfDay := beginningOfDayTime.Unix()
	beginningOfYesterday := goutil.BeginningOfDay(beginningOfDayTime.AddDate(0, 0, -1)).Unix()

	ei, err := findExclusiveInfo(cronExclusiveSendMsgType, beginningOfDay, beginningOfYesterday)
	if err != nil {
		logger.Error(err)
		return
	}
	if ei == nil {
		logger.Info("没有新增、修改或移除的三方独家主播")
		return
	}
	if ei.userMap == nil || ei.guildMap == nil {
		return
	}

	smps := make([]sendMsgParams, 0, len(ei.exclusiveList))
	for _, exclusive := range ei.exclusiveList {
		user, ok := ei.userMap[exclusive.CreatorID]
		if !ok || user == nil {
			continue
		}

		g, ok := ei.guildMap[exclusive.GuildID]
		if !ok {
			continue
		}
		guildUser, ok := ei.userMap[g.UserID]
		if !ok || guildUser == nil {
			continue
		}

		smp := sendMsgParams{
			guild:     g,
			user:      user,
			exclusive: exclusive,
		}
		smp.creatorRegionMobile, err = regionMobile(strconv.Itoa(user.Region), user.Mobile)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		smp.guildRegionMobile, err = regionMobile(strconv.Itoa(guildUser.Region), guildUser.Mobile)
		if err != nil {
			logger.Error(err)
			// PASS
		}
		// 新增的三方独家主播
		if exclusive.Status == exclusivecreator.StatusValid &&
			exclusive.CreateTime > beginningOfYesterday &&
			exclusive.CreateTime <= beginningOfDay {
			smp.scene = sceneExclusiveAdd
		}
		// 修改的三方独家主播 (在当天创建，又在当天修改的不用发修改短信通知)
		if exclusive.Status == exclusivecreator.StatusValid &&
			exclusive.CreateTime <= beginningOfYesterday {
			smp.scene = sceneExclusiveEdit
		}
		// 移除的三方独家主播 (在当天创建，又在当天移除的不用发移除短信通知)
		if exclusive.Status == exclusivecreator.StatusDeleted &&
			exclusive.CreateTime <= beginningOfYesterday {
			smp.scene = sceneExclusiveDeleted
		}

		if ei.agentMap == nil {
			smps = append(smps, smp)
			continue
		}
		if agent, ok := ei.agentMap[exclusive.CreatorID]; ok {
			agentUser, ok := ei.userMap[agent.AgentID]
			// 若公会长也是该主播的经纪人，发送一次短信即可
			if ok && agentUser != nil && agent.AgentID != g.UserID {
				smp.agentRegionMobile, err = regionMobile(strconv.Itoa(agentUser.Region), agentUser.Mobile)
				if err != nil {
					logger.Error(err)
					// PASS
				}
			}
		}
		smps = append(smps, smp)
	}

	// 给主播、会长和经纪人发短信
	sendMsgByScene(smps)

	logger.WithFields(logger.Fields{"count": len(ei.userMap)}).Info("给新增、修改或移除的三方独家主播发短信通知完成")
}

func findExclusiveInfo(selectType int, beginningOfDay, beginningOfYesterday int64) (*exclusiveInfo, error) {
	var ei exclusiveInfo
	var exclusiveList []exclusivecreator.TripartiteExclusiveCreator
	query := exclusivecreator.TripartiteExclusiveCreator{}.DB()
	if selectType == cronExclusiveSendMsgType {
		// 获取今日新增、修改或移除的三方独家主播
		query = query.Where("status IN (?) AND modified_time > ? AND modified_time <= ?",
			[]int64{exclusivecreator.StatusValid, exclusivecreator.StatusDeleted}, beginningOfYesterday, beginningOfDay)
	} else {
		// 获取即将到期（状态为生效中，合约到期时间为当天 00:00::00）的三方独家主播
		query = query.Where("status = ? AND contract_end <= ?",
			exclusivecreator.StatusValid, beginningOfDay)
	}
	err := query.Find(&ei.exclusiveList).Error
	if err != nil {
		return nil, err
	}
	if len(ei.exclusiveList) == 0 {
		return nil, nil
	}

	guildIDs := make([]int64, 0, len(exclusiveList))
	creatorIDs := make([]int64, 0, len(exclusiveList))
	exclusiveIDs := make([]int64, 0, len(exclusiveList))
	userIDs := make([]int64, 0, len(exclusiveList)*3)
	for _, exclusive := range ei.exclusiveList {
		guildIDs = append(guildIDs, exclusive.GuildID)
		creatorIDs = append(creatorIDs, exclusive.CreatorID)
		userIDs = append(userIDs, exclusive.CreatorID)
		exclusiveIDs = append(exclusiveIDs, exclusive.ID)
	}
	ei.exclusiveIDs = exclusiveIDs
	// 查询公会信息
	ei.guildMap, err = guild.FindSimpleMap(guildIDs)
	if err != nil {
		return nil, err
	}
	for _, g := range ei.guildMap {
		userIDs = append(userIDs, g.UserID)
	}

	// 查询主播经纪人信息
	agentList, err := guildagent.FindGuildAgentListByCreator(creatorIDs)
	if err != nil {
		return nil, err
	}
	ei.agentMap = goutil.ToMap(agentList, "CreatorID").(map[int64]*guildagent.AgentCreator)

	for _, agent := range agentList {
		userIDs = append(userIDs, agent.AgentID)
	}
	// 查询主播、公会长、经纪人用户信息
	if len(userIDs) > 0 {
		userIDs = util.Uniq(userIDs)
		userList, err := service.SSO.ListUserInfo(userIDs)
		if err != nil {
			return nil, err
		}
		ei.userMap = goutil.ToMap(userList, "ID").(map[int64]*sso.Account)
	}

	return &ei, nil
}

func sendMsgByScene(params []sendMsgParams) {
	var (
		creatorScene   string
		guildScene     string
		creatorPayload map[string]interface{}
		guildPayload   map[string]interface{}
	)
	sms := make([]pushservice.SMS, 0, len(params)*3)
	for _, smp := range params {
		contractEndTime := time.Unix(smp.exclusive.ContractEnd, 0).Format(util.TimeFormatYMDHMS)
		modifiedTime := time.Unix(smp.exclusive.ModifiedTime, 0).Format(util.TimeFormatYMDHMS)
		switch smp.scene {
		case sceneExclusiveAdd:
			creatorScene = "add_exclusive_to_creator"
			creatorPayload = map[string]interface{}{"time": contractEndTime, "name": smp.guild.Name}
			guildScene = "add_exclusive_to_guild"
			guildPayload = map[string]interface{}{"time": contractEndTime, "name": smp.user.Username}
		case sceneExclusiveEdit:
			creatorScene = "edit_exclusive_to_creator"
			creatorPayload = map[string]interface{}{"time": contractEndTime}
			guildScene = "edit_exclusive_to_guild"
			guildPayload = map[string]interface{}{"time": contractEndTime, "name": smp.user.Username}
		case sceneExclusiveDeleted:
			creatorScene = "delete_exclusive_to_creator"
			creatorPayload = map[string]interface{}{"time": modifiedTime}
			guildScene = "delete_exclusive_to_guild"
			guildPayload = map[string]interface{}{"time": modifiedTime, "name": smp.user.Username}
		case sceneExclusiveExpired:
			creatorScene = "expire_exclusive_to_creator"
			creatorPayload = map[string]interface{}{"time": contractEndTime}
			guildScene = "expire_exclusive_to_guild"
			guildPayload = map[string]interface{}{"time": contractEndTime, "name": smp.user.Username}
		default:
			continue
		}

		if smp.creatorRegionMobile != "" {
			sms = append(sms, pushservice.SMS{
				To:         "+" + smp.creatorRegionMobile,
				RegionCode: smp.user.Region,
				Scene:      creatorScene,
				Payload:    creatorPayload,
			})
		}
		if smp.guildRegionMobile != "" {
			sms = append(sms, pushservice.SMS{
				To:         "+" + smp.guildRegionMobile,
				RegionCode: smp.user.Region,
				Scene:      guildScene,
				Payload:    guildPayload,
			})
		}
		if smp.agentRegionMobile != "" {
			sms = append(sms, pushservice.SMS{
				To:         "+" + smp.agentRegionMobile,
				RegionCode: smp.user.Region,
				Scene:      guildScene,
				Payload:    guildPayload,
			})
		}
	}

	// 给主播、公会长和经纪人发短信
	if len(sms) > 0 {
		err := service.PushService.SendSMSBatch(sms)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
}

// TODO: 后面统一换成 vcode.MobileNumer
func regionMobile(regionNumber, mobile string) (string, error) {
	// 只支持中国大陆地区手机号
	if mobile != "" && regionNumber == vcode.CNRegionNumber {
		mobileNum, err := vcode.RegionMobile(mobile, vcode.DefaultRegion)
		if err != nil {
			return "", err
		}
		return mobileNum.RegionMobile, nil
	}

	return "", nil
}
