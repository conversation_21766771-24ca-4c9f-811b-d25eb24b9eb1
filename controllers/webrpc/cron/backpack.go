package cron

import (
	"fmt"
	"html"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type backpackExpireNotifyParam struct {
	ExcludeGiftIDs []int64 `json:"exclude_gift_ids"`
}

// ActionCronUserBackpackExpireNotify 用户背包礼物过期通知
// 运行周期：0 0/10 * * * * 每十分钟执行一次
/**
 * @api {post} /rpc/live-service/cron/user/backpack/expire-notify 用户背包礼物过期通知
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} [exclude_gift_ids] 排除的礼物 ID 列表
 *
 * @apiSuccessExample {json} Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": nil
 *   }
 */
func ActionCronUserBackpackExpireNotify(c *handler.Context) (handler.ActionResponse, string, error) {
	var param backpackExpireNotifyParam
	if err := c.BindJSON(&param); err != nil {
		return nil, "", actionerrors.ErrParams
	}
	now := goutil.TimeNow()
	userIDs, err := useritems.ListExpireBackpackUserIDs(now, param.ExcludeGiftIDs)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userIDs) == 0 {
		return nil, "empty expire", nil
	}
	notifyUserIDs, err := userstatus.FindNotBackPackNotifiedUserIDs(userIDs, now)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(userIDs) == 0 {
		return nil, "empty notify", nil
	}
	users, err := mowangskuser.FindSimpleList(notifyUserIDs)
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	if len(users) == 0 {
		logger.Errorf("can not find users: %v", notifyUserIDs)
		return nil, "empty users", nil
	}
	sysMsgBox := userapi.NewSystemMsgBox()
	for _, u := range users {
		sysMsgBox.AddMessage(u.ID, "背包礼物即将过期提醒",
			fmt.Sprintf("亲爱的 %s，你的背包中有礼物将在 24 小时内过期，请及时到直播间-背包送给心仪的主播哦~"+
				"<a href=\"%s\">点击跳转直播页面</a>", html.EscapeString(u.Username), config.Conf.Params.URL.Live))
	}
	if err = sysMsgBox.Send(&pushservice.SystemMsgOptions{DisableHTMLEscape: true}); err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	logger.Infof("本次共发送 %d 条背包礼物过期提醒系统通知", len(sysMsgBox.Messages))
	err = userstatus.SaveUsersBackPackNotifiedTime(notifyUserIDs, now)
	if err != nil {
		logger.WithField("notifyUserIDs", notifyUserIDs).Error(err)
		// PASS
	}
	return nil, "success", nil
}
