package cron

import (
	"errors"
	"math"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronExpireNotice(t *testing.T) {
	require := require.New(t)

	cancel := vip.MockVipList()
	defer cancel()

	cleanup := mrpc.SetMock(vip.URLGetExpireBalanceByTime, func(input interface{}) (output interface{}, err error) {
		return []vip.UserExpireBalance{}, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLLiveUserLevel, func(input interface{}) (output interface{}, err error) {
		resp := vip.LiveUsersLevelResp{
			Data: []*vip.UserVip{
				{
					UserID:     12,
					VipID:      vip.HighnessVipID,
					Level:      vip.HighnessLevel,
					Type:       vip.TypeLiveHighness,
					ExpireTime: goutil.TimeNow().Unix(),
				},
			},
		}
		return resp, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLGetUserByTime, func(input interface{}) (output interface{}, err error) {
		resp := []vip.UserVip{
			{UserID: 9074509},
			{UserID: 9074510},
		}
		return resp, nil
	})
	defer cleanup()

	cleanup = mrpc.SetMock(vip.URLVipConfig, func(interface{}) (output interface{}, err error) {
		now := goutil.TimeNow().Unix()
		return &vip.ConfigResp{
			EnableNewVipBalanceTime: now,
			LiveNobleProtectDays:    5,
		}, nil
	})
	defer cleanup()

	c := handler.NewTestContext(http.MethodPost, "/rpc/cron/noble/expire", false, nil)
	_, err := ActionCronExpireNotice(c)
	require.NoError(err)
}

func TestVipExpireNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cancel := vip.MockVipList()
	defer cancel()

	cancel = mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		// 1. 类型断言
		data, ok := input.(map[string]interface{})
		if !ok {
			return nil, errors.New("输入类型无效")
		}

		// 2. 提取 batchMsg
		batchMsg, ok := data["systemmsgs"].([]pushservice.SystemMsg)
		if !ok {
			return nil, errors.New("systemmsgs 类型无效")
		}

		// 遍历并保存每条消息
		for _, msg := range batchMsg {
			// 3. 创建一个新的 MessageAssign
			messageAssign := messageassign.MessageAssign{
				RecUID:  msg.UserID,
				SendUID: 0,
				Title:   msg.Title,
				Content: msg.Content,
				Status:  0,
				Time:    msg.SendTime,
			}

			// 如果 SendTime 是零，则使用当前时间
			if messageAssign.Time == 0 {
				messageAssign.Time = goutil.TimeNow().Unix()
			}

			// 4. 保存到数据库
			if err := service.DB.Create(&messageAssign).Error; err != nil {
				return nil, err
			}
		}

		return "success", nil
	})
	defer cancel()

	cancel = mrpc.SetMock(vip.URLGetUserByTime, func(input interface{}) (output interface{}, err error) {
		resp := []vip.UserVip{
			{UserID: 9074509},
			{UserID: 9074510},
		}
		return resp, nil
	})
	defer cancel()
	cancel = mrpc.SetMock(vip.URLLiveUserLevel, func(input interface{}) (output interface{}, err error) {
		resp := vip.LiveUsersLevelResp{
			Data: []*vip.UserVip{
				{
					UserID: 9074509,
					Type:   vip.TypeLiveHighness,
					Level:  1,
				},
				{
					UserID: 9074510,
					Type:   vip.TypeLiveNoble,
					Level:  vip.NobleLevel7,
				},
			},
		}
		return resp, nil
	})
	defer cancel()

	err := service.DB.Table(messageassign.TableName()).Delete("", "recuid IN (?)", []int64{9074509, 9074510}).Error
	require.NoError(err)

	vipConfig := &vip.ConfigResp{
		LiveNobleProtectDays: 5,
	}

	err = VipExpireNotice(NoticeTypeThreeDay, vip.TypeLiveNoble, vipConfig)
	require.NoError(err)
	err = VipExpireNotice(NoticeTypeProtectOneDay, vip.TypeLiveNoble, vipConfig)
	require.NoError(err)
	err = VipExpireNotice(NoticeTypeExpired, vip.TypeLiveNoble, vipConfig)
	require.NoError(err)

	err = VipExpireNotice(NoticeTypeThreeDay, vip.TypeLiveHighness, vipConfig)
	require.NoError(err)
	err = VipExpireNotice(NoticeTypeProtectOneDay, vip.TypeLiveHighness, vipConfig)
	require.NoError(err)
	err = VipExpireNotice(NoticeTypeExpired, vip.TypeLiveHighness, vipConfig)
	require.NoError(err)

	var sysMsgCount int
	err = service.DB.Table(messageassign.TableName()).
		Where("status = ?", messageassign.StatusUnread).
		Where("recuid IN (?)", []int64{9074509, 9074510}).
		Count(&sysMsgCount).Error
	require.NoError(err)
	assert.Equal(12, sysMsgCount)

	err = VipExpireNotice(0, vip.TypeLiveHighness, vipConfig)
	assert.EqualError(err, "贵族通知类型错误")
	err = VipExpireNotice(NoticeTypeExpired, 0, vipConfig)
	assert.EqualError(err, "直播贵族类型错误")
}

func TestSendNotice(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	called := false
	cancel := mrpc.SetMock("pushservice://api/systemmsg", func(input interface{}) (output interface{}, err error) {
		called = true
		return "success", nil
	})
	defer cancel()

	param := &expireNoticeParam{
		noticeType: NoticeTypeThreeDay,
		vipType:    vip.TypeLiveNoble,
		users: []vip.UserVip{
			{UserID: 9074509, Title: "神话"},
		},
	}
	require.NoError(param.sendNotice())
	assert.True(called)

	called = false
	param.vipType = vip.TypeLiveHighness
	require.NoError(param.sendNotice())
	assert.True(called)
}

func TestLoadTime(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := expireNoticeParam{
		vipConfig: &vip.ConfigResp{
			LiveNobleProtectDays: 5,
		},
	}
	assert.PanicsWithValue("直播贵族过期通知类型错误", func() {
		_ = param.loadTime()
	})
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2021, 12, 19, 19, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	now := goutil.TimeNow().Add(2 * time.Minute)
	y, m, d := now.Date()
	param.noticeType = NoticeTypeThreeDay
	require.NoError(param.loadTime())
	endTime := time.Date(y, m, d+2, 23, 59, 59, 0, time.Local)
	assert.Equal(now, param.now)
	assert.Equal(endTime.AddDate(0, 0, -1), param.vipMinExpireTime)
	assert.Equal(endTime, param.vipMaxExpireTime)

	param.noticeType = NoticeTypeExpired
	require.NoError(param.loadTime())
	endTime = time.Date(y, m, d-1, 23, 59, 59, 0, time.Local)
	assert.Equal(now, param.now)
	assert.Equal(endTime.AddDate(0, 0, -1), param.vipMinExpireTime)
	assert.Equal(endTime, param.vipMaxExpireTime)

	param.noticeType = NoticeTypeProtectOneDay
	require.NoError(param.loadTime())
	endTime = time.Date(y, m, d-param.vipConfig.LiveNobleProtectDays, 23, 59, 59, 0, time.Local)
	assert.Equal(now, param.now)
	assert.Equal(endTime.AddDate(0, 0, -1), param.vipMinExpireTime)
	assert.Equal(endTime, param.vipMaxExpireTime)
}

func TestBuildNobleMessage(t *testing.T) {
	assert := assert.New(t)

	testNobleTitle := "神话"
	testTime := time.Date(2021, 12, 19, 23, 59, 59, 0, time.Local)
	param := expireNoticeParam{
		noticeType: NoticeTypeThreeDay,
		vipConfig: &vip.ConfigResp{
			LiveNobleProtectDays: 5,
		},
		now:              testTime,
		vipMaxExpireTime: testTime.Add(24 * time.Hour),
	}
	noticeContent := param.buildNobleMessage(testNobleTitle, nil)
	content := "尊敬的神话贵族用户，您的贵族身份将在 2021-12-20 23:59:59 到期，为避免贵族特权失效，请及时续费。" +
		"<a href=\"https://fm.example.com/noble/privilege\">立即续费</a>"
	assert.Equal(content, noticeContent)

	param.noticeType = NoticeTypeProtectOneDay
	noticeContent = param.buildNobleMessage(testNobleTitle, nil)
	content = "尊敬的神话贵族用户，您的神话贵族续费保护期将在今日 23:59:59 结束。今日之内，您可以续费价格购买原有贵族。续费保护期结束后，" +
		"您只能以开通价格重新购买。<a href=\"https://fm.example.com/noble/privilege\">立即续费</a>"
	assert.Equal(content, noticeContent)

	param.noticeType = NoticeTypeExpired
	noticeContent = param.buildNobleMessage(testNobleTitle, nil)
	content = "尊敬的神话贵族用户，您的贵族身份现已失效。您可以在 2021-12-24 00:00:00 续费保护期结束前以续费价格购买原有贵族。" +
		"续费保护期结束后，您只能以开通价格重新购买。<a href=\"https://fm.example.com/noble/privilege\">立即续费</a>"
	assert.Equal(content, noticeContent)

	highness := &vip.UserVip{
		Type: vip.TypeLiveHighness,
	}
	param.noticeType = NoticeTypeThreeDay
	noticeContent = param.buildNobleMessage(testNobleTitle, highness)
	content = "尊敬的上神用户，您的神话贵族将在 2021-12-20 23:59:59 到期，请您及时续费。" +
		"<a href=\"https://fm.example.com/noble/privilege\">立即续费</a>"
	assert.Equal(content, noticeContent)

	param.noticeType = NoticeTypeProtectOneDay
	noticeContent = param.buildNobleMessage(testNobleTitle, highness)
	content = "尊敬的上神用户，您的神话贵族续费保护期将在今日 23:59:59 结束。今日之内，您可以续费价格购买原有贵族。续费保护期结束后，" +
		"您只能以开通价格重新购买。<a href=\"https://fm.example.com/noble/privilege\">立即续费</a>"
	assert.Equal(content, noticeContent)

	param.noticeType = NoticeTypeExpired
	noticeContent = param.buildNobleMessage(testNobleTitle, highness)
	content = "尊敬的上神用户，您的神话贵族现已失效。您可以在 2021-12-24 00:00:00 续费保护期结束前进行续费来恢复神话贵族身份。" +
		"续费保护期结束后，您只能以开通价格重新购买神话贵族。<a href=\"https://fm.example.com/noble/privilege\">立即续费</a>"
	assert.Equal(content, noticeContent)
}

func TestBuildHighnessMessage(t *testing.T) {
	assert := assert.New(t)

	testUserID := int64(22335566)
	testTime := time.Date(2021, 12, 19, 23, 59, 59, 0, time.Local)
	param := expireNoticeParam{
		now:              testTime,
		vipMaxExpireTime: testTime.Add(24 * time.Hour),
		noticeType:       NoticeTypeThreeDay,
		vipConfig: &vip.ConfigResp{
			LiveNobleProtectDays: 5,
		},
	}
	noticeContent := param.buildHighnessMessage(testUserID)
	assert.Equal("上神贵族即将到期", param.title)
	content := "尊敬的上神用户，您的上神贵族将在 2021-12-20 23:59:59 到期，您还需消费 5000000 钻即可延长 30 天的上神身份有效期，请您及时续费~"
	assert.Equal(content, noticeContent)

	param.noticeType = NoticeTypeProtectOneDay
	noticeContent = param.buildHighnessMessage(testUserID)
	assert.Equal("上神贵族续费提示", param.title)
	content = "尊敬的上神用户，您的上神续费保护期将在今日 23:59:59 结束。今日之内，您还需消费 5000000 钻即可获得 30 天的上神身份有效期。" +
		"续费保护期结束后，您需要按开通条件重新获取上神身份。"
	assert.Equal(content, noticeContent)

	param.noticeType = NoticeTypeExpired
	noticeContent = param.buildHighnessMessage(testUserID)
	assert.Equal("上神贵族失效提示", param.title)
	content = "尊敬的上神用户，您的上神身份现已失效。在 2021-12-24 00:00:00 续费保护期结束前消费 5000000 钻即可获得 30 天的上神身份有效期。" +
		"续费保护期结束后，您需要按开通条件重新获取上神身份。"
	assert.Equal(content, noticeContent)
}

func TestVipNoticeInfo(t *testing.T) {
	assert := assert.New(t)

	param := expireNoticeParam{
		noticeType: NoticeTypeThreeDay,
		vipType:    vip.TypeLiveNoble,
	}
	assert.Equal("直播贵族有效期剩余 3 天通知", param.vipNoticeInfo())
	param.noticeType = NoticeTypeProtectOneDay
	assert.Equal("直播贵族续费保护剩余 1 天通知", param.vipNoticeInfo())
	param.noticeType = NoticeTypeExpired
	assert.Equal("直播贵族失效通知", param.vipNoticeInfo())

	param = expireNoticeParam{
		noticeType: NoticeTypeThreeDay,
		vipType:    vip.TypeLiveHighness,
	}
	assert.Equal("上神贵族有效期剩余 3 天通知", param.vipNoticeInfo())
	param.noticeType = NoticeTypeProtectOneDay
	assert.Equal("上神贵族续费保护剩余 1 天通知", param.vipNoticeInfo())
	param.noticeType = NoticeTypeExpired
	assert.Equal("上神贵族失效通知", param.vipNoticeInfo())
}

func TestClearUserNobleData(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := expireNoticeParam{
		noticeType: NoticeTypeExpired,
		vipType:    vip.TypeLiveNoble,
	}
	param.users = []vip.UserVip{
		{UserID: 10010},
		{UserID: 10011},
		{UserID: 10012},
		{UserID: 10013},
		{UserID: 10014},
	}

	userIDs := make([]int64, 0)
	for i := range param.users {
		userIDs = append(userIDs, param.users[i].UserID)
	}

	// 插入测试数据
	// 外观测试数据
	ua10010 := &userappearance.UserAppearance{
		UserID: 10010,
		From:   appearance.FromNoble,
	}
	ua10011 := new(userappearance.UserAppearance)
	*ua10011 = *ua10010
	ua10011.UserID = 10011

	ua10012 := new(userappearance.UserAppearance)
	*ua10012 = *ua10010
	ua10012.UserID = 10012

	ua10013 := new(userappearance.UserAppearance)
	*ua10013 = *ua10010
	ua10013.UserID = 10013

	ua10014 := new(userappearance.UserAppearance)
	*ua10014 = *ua10010
	ua10014.From = appearance.FromHighness
	ua10014.UserID = 10014

	// usermeta 测试数据
	testValueTrue := true
	testValueInt := 1
	usermeta10010 := &userstatus.GeneralStatus{
		UserID:       10010,
		NobleHornNum: 1,
		RecommendNum: &testValueInt,
		Invisible:    &testValueTrue,
	}

	usermeta10011 := new(userstatus.GeneralStatus)
	*usermeta10011 = *usermeta10010
	usermeta10011.UserID = 10011

	usermeta10012 := new(userstatus.GeneralStatus)
	*usermeta10012 = *usermeta10010
	usermeta10012.UserID = 10012

	usermeta10013 := new(userstatus.GeneralStatus)
	*usermeta10013 = *usermeta10010
	usermeta10013.UserID = 10013

	// liveuser 测试数据
	type testLiveUsers struct {
		UserID        int64 `bson:"user_id"`
		RankInvisible bool  `bson:"rank_invisible"`
	}

	liveuser10010 := &testLiveUsers{
		UserID:        10010,
		RankInvisible: true,
	}

	liveuser10011 := new(testLiveUsers)
	*liveuser10011 = *liveuser10010
	liveuser10011.UserID = 10011

	liveuser10012 := new(testLiveUsers)
	*liveuser10012 = *liveuser10010
	liveuser10012.UserID = 10012

	liveuser10013 := new(testLiveUsers)
	*liveuser10013 = *liveuser10010
	liveuser10013.UserID = 10013

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"user_id": bson.M{"$in": userIDs}}
	defer func() {
		_, err := userstatus.UserMetaCollection().DeleteMany(ctx, filter)
		assert.NoError(err)
		_, err = service.MongoDB.Collection("live_users").DeleteMany(ctx, filter)
		assert.NoError(err)
	}()
	_, err := userappearance.Collection().InsertMany(ctx, []interface{}{ua10010, ua10011, ua10012, ua10013, ua10014})
	require.NoError(err)
	_, err = userstatus.UserMetaCollection().InsertMany(ctx, []interface{}{usermeta10010, usermeta10011, usermeta10012, usermeta10013})
	require.NoError(err)
	_, err = service.MongoDB.Collection("live_users").InsertMany(ctx, []interface{}{liveuser10010, liveuser10011, liveuser10012, liveuser10013})
	require.NoError(err)

	param.clearUserNobleData()

	// 检查数据
	filter2 := bson.M{"user_id": bson.M{"$in": userIDs}, "from": appearance.FromNoble}
	count, err := userappearance.Collection().CountDocuments(ctx, filter2)
	require.NoError(err)
	assert.Zero(count)

	filter3 := bson.M{"user_id": bson.M{"$in": userIDs}, "noble_horn_num": bson.M{"$gt": 0}, "recommend_num": bson.M{"$exists": true}, "invisible": bson.M{"$exists": true}}
	count, err = userstatus.UserMetaCollection().CountDocuments(ctx, filter3)
	require.NoError(err)
	assert.Zero(count)

	filter4 := bson.M{"user_id": bson.M{"$in": userIDs}, "rank_invisible": true}
	count, err = service.MongoDB.Collection("live_users").CountDocuments(ctx, filter4)
	require.NoError(err)
	assert.Zero(count)

	filter5 := bson.M{"user_id": bson.M{"$in": userIDs}, "from": appearance.FromHighness}
	count, err = userappearance.Collection().CountDocuments(ctx, filter5)
	require.NoError(err)
	assert.NotZero(count)

	param.vipType = vip.TypeLiveHighness
	param.clearUserNobleData()
	filter6 := bson.M{"user_id": bson.M{"$in": userIDs}, "from": appearance.FromHighness}
	count, err = userappearance.Collection().CountDocuments(ctx, filter6)
	require.NoError(err)
	assert.Zero(count)

	err = userstatus.GeneralSetOne(bson.M{"user_id": 10014}, bson.M{"noble_horn_num": 1000})
	require.NoError(err)

	param.vipLevelMap = map[int64]*vip.UserVip{
		10014: {
			VipID: vip.HighnessVipID,
			Type:  vip.TypeLiveHighness,
			Level: vip.HighnessLevel,
			Info: &vip.Info{
				Privilege: math.MaxInt16,
				HornNum:   1,
			},
		},
	}
	param.clearUserNobleData()

	var res userstatus.GeneralStatus
	err = userstatus.UserMetaCollection().FindOne(ctx,
		bson.M{"user_id": 10014},
		options.FindOne().SetProjection(bson.M{"noble_horn_num": 1}),
	).Decode(&res)
	require.NoError(err)
	assert.EqualValues(1000, res.NobleHornNum)
}
