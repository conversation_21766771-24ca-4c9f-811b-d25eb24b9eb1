package cron

import (
	"context"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/livemeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronCleanup(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	key := keys.KeyUsersBlockList0.Format()
	service.Redis.ZAdd(key, &redis.Z{
		Score:  10,
		Member: 20201016,
	})
	testRoomID := int64(20201026)
	when := goutil.TimeNow().Add(-time.Minute).Unix()
	meta := livemeta.LiveMeta{
		RoomOID: primitive.NewObjectID(),
		RoomID:  testRoomID,
		Ban: &livemeta.Ban{
			Type:       livemeta.TypeBanDuration,
			StartTime:  when,
			ExpireTime: &when,
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemeta.Collection().UpdateOne(ctx, bson.M{"room_id": testRoomID}, bson.M{"$set": meta},
		options.Update().SetUpsert(true))
	require.NoError(err)
	testBanUserID := int64(20201105)
	require.NoError(userstatus.BanUser(testBanUserID, when, -util.SecondOneMinute, userstatus.TypeBanDuration))

	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	res, err := ActionCronCleanup(c)
	require.NoError(err)
	assert.NotNil(res)

	count, err := service.Redis.ZCount(key, "-inf", "0").Result()
	require.NoError(err)
	assert.NotZero(count)

	count, err = service.Redis.ZCount(key, "0", strconv.FormatInt(goutil.TimeNow().Add(-time.Second).Unix(), 10)).Result()
	require.NoError(err)
	assert.Zero(count)

	var lm livemeta.LiveMeta
	require.NoError(livemeta.Collection().FindOne(ctx, bson.M{"room_id": testRoomID}).Decode(&lm))
	assert.Equal(testRoomID, lm.RoomID)
	assert.Nil(lm.Ban)

	ban, err := userstatus.FindBanned(testBanUserID)
	require.NoError(err)
	assert.Nil(ban)
}

func TestRemoveCloseRoomScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		closeRoomID1 int64 = 676768
		closeRoomID2 int64 = 676769
		openRoomID1  int64 = 676770
	)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	filter := bson.M{"room_id": bson.M{"$in": []int64{closeRoomID1, closeRoomID2}}}
	_, err := room.Collection().UpdateMany(ctx, filter,
		bson.M{"$set": bson.M{"status.score": 30, "status.open": room.StatusOpenFalse}})
	require.NoError(err)
	filter = bson.M{"room_id": openRoomID1}
	_, err = room.Collection().UpdateOne(ctx, filter,
		bson.M{"$set": bson.M{"status.score": 30, "status.open": room.StatusOpenTrue}})
	require.NoError(err)
	removeCloseRoomScore()
	filter = bson.M{"room_id": bson.M{"$in": []int64{closeRoomID1, closeRoomID2, openRoomID1}}}
	opt := options.Find().SetSort(bson.M{"room_id": 1})
	rooms, err := room.FindAll(filter, opt)
	require.NoError(err)
	require.Len(rooms, 3)
	assert.Zero(rooms[0].Status.Score)
	assert.Zero(rooms[1].Status.Score)
	assert.Equal(float64(30), rooms[2].Status.Score)
}

func TestRemoveExpiredExtraScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		expiredRoom1    int64 = 2021021801
		expiredRoom2    int64 = 2021021802
		notExpiredRoom1 int64 = 2021021803
	)
	now := goutil.TimeNow()
	expiredScore1 := &livemeta.ExtraScore{
		Score:     100,
		StartTime: now.Add(-48 * time.Hour).Unix(),
		EndTime:   now.Add(-24 * time.Hour).Unix(),
	}
	expiredScore2 := &livemeta.ExtraScore{
		Score:     200,
		StartTime: now.Add(-12 * time.Hour).Unix(),
		EndTime:   now.Add(-6 * time.Hour).Unix(),
	}
	notExpiredScore1 := &livemeta.ExtraScore{
		Score:     300,
		StartTime: now.Add(-12 * time.Hour).Unix(),
		EndTime:   now.Add(24 * time.Hour).Unix(),
	}
	notExpiredScore2 := &livemeta.ExtraScore{
		Score:     300,
		StartTime: now.Add(12 * time.Hour).Unix(),
		EndTime:   now.Add(24 * time.Hour).Unix(),
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livemeta.Collection()
	_, err := col.UpdateOne(ctx, bson.M{"room_id": expiredRoom1},
		bson.M{"$set": bson.M{"extra_score": []*livemeta.ExtraScore{expiredScore1, expiredScore2}}})
	require.NoError(err)
	_, err = col.UpdateOne(ctx, bson.M{"room_id": expiredRoom2},
		bson.M{"$set": bson.M{"extra_score": []*livemeta.ExtraScore{notExpiredScore1, expiredScore2, notExpiredScore2}}})
	require.NoError(err)
	_, err = col.UpdateOne(ctx, bson.M{"room_id": notExpiredRoom1},
		bson.M{"$set": bson.M{"extra_score": []*livemeta.ExtraScore{notExpiredScore1, notExpiredScore2}}})
	require.NoError(err)

	removeExpiredExtraScore()

	lm, err := livemeta.Find(expiredRoom1)
	require.NoError(err)
	assert.Empty(lm.ExtraScore, "全部奖励热度都过期的直播间热度清理完毕")
	lm, err = livemeta.Find(expiredRoom2)
	require.NoError(err)
	assert.Equal(lm.ExtraScore, []*livemeta.ExtraScore{notExpiredScore1, notExpiredScore2}, "部分奖励热度过期的直播间热度清理完毕")
	lm, err = livemeta.Find(notExpiredRoom1)
	require.NoError(err)
	assert.Equal(lm.ExtraScore, []*livemeta.ExtraScore{notExpiredScore1, notExpiredScore2}, "奖励热度都未过期的直播间热度不必清理")
}

func setLv45RewardTestData(ctx context.Context) error {
	_, err := appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceIDBirthdayAvatarFrameForLv45,
			"type": appearance.TypeAvatarFrame,
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "oss://live/avatarframes/40261.png",
		}},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	_, err = appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceIDBirthdayBadgeForLv45,
			"type": appearance.TypeBadge,
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "oss://live/badges/50227.png",
		}},
		options.Update().SetUpsert(true),
	)
	if err != nil {
		return err
	}
	return nil
}

func TestActionCronSendBirthdayPriv(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 1, 26, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)
	now := goutil.TimeNow()
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	require.NoError(setLv45RewardTestData(ctx))

	testIDs := []int64{1919, 1616, 1515}
	_, err := liveuser.Collection().UpdateOne(ctx,
		bson.M{"user_id": testIDs[2]},
		bson.M{"$set": bson.M{"contribution": 3940000000}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	_, err = liveuser.Collection().UpdateOne(ctx,
		bson.M{"user_id": testIDs[0]},
		bson.M{"$set": bson.M{"contribution": 3940000000}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testIDs}})
	require.NoError(err)
	err = livebirthdayprivrecord.LiveBirthdayPrivRecord{}.DB().Delete("", "user_id = ?", testIDs[2]).Error
	require.NoError(err)

	require.False(livebirthdayprivrecord.IsRewarded(testIDs[2], now.Year()))
	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	res, err := ActionCronSendBirthdayPriv(c)
	require.NoError(err)
	assert.NotNil(res)
	var ua userappearance.UserAppearance
	require.NoError(userappearance.Collection().FindOne(ctx, bson.M{"user_id": testIDs[2]}).Decode(&ua))
	assert.Equal(now.Day()+1, time.Unix(*ua.ExpireTime, 0).Day())
	assert.True(livebirthdayprivrecord.IsRewarded(testIDs[2], now.Year()))
	assert.False(livebirthdayprivrecord.IsRewarded(testIDs[1], now.Year()))
}

func TestSelectByUserLevel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	testIDs := []int64{181819, 191918}
	_, err := liveuser.Collection().UpdateOne(ctx,
		bson.M{"user_id": testIDs[0]},
		bson.M{"$set": bson.M{"contribution": 3940000000}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	_, err = liveuser.Collection().UpdateOne(ctx,
		bson.M{"user_id": testIDs[1]},
		bson.M{"$set": bson.M{"contribution": 2200000}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	res, err := selectByUserLevel(testIDs, 45)
	require.NoError(err)
	require.Len(res, 1)
	assert.Equal(testIDs[0], res[0])
	res, err = selectByUserLevel(testIDs, 1)
	require.NoError(err)
	assert.Len(res, 2)
	res, err = selectByUserLevel(testIDs, 200)
	require.NoError(err)
	assert.Len(res, 0)
}

func TestFilterUnrewarded(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testIDs := []int64{181819, 191918}
	err := service.LiveDB.Table(livebirthdayprivrecord.TableName()).Delete("", "user_id IN (?)", testIDs).Error
	require.NoError(err)

	res, err := filterUnrewarded(testIDs)
	require.NoError(err)
	assert.EqualValues(testIDs, res)

	now := goutil.TimeNow()
	require.NoError(livebirthdayprivrecord.RecordUserBirthday(testIDs[1], now))

	res, err = filterUnrewarded(testIDs)
	require.NoError(err)
	require.Len(res, 1)
	assert.EqualValues(testIDs[0], res[0])
}

func TestSendBirthdayAppearances(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(1145)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": userID})
	require.NoError(err)

	require.NoError(setLv45RewardTestData(ctx))
	appearances, err := appearance.GetBirthdayAppearances()
	require.NoError(err)
	require.NoError(SendBirthdayAppearances([]int64{userID}, appearances))
	ua, err := userappearance.FindValidAppearance(
		appearance.AppearanceIDBirthdayAvatarFrameForLv45, userID, appearance.TypeAvatarFrame)
	require.NoError(err)
	require.NotNil(ua)
	now := goutil.TimeNow()
	assert.Equal(now.Day()+1, time.Unix(*ua.ExpireTime, 0).Day())
	ua, err = userappearance.FindValidAppearance(
		appearance.AppearanceIDBirthdayBadgeForLv45, userID, appearance.TypeBadge)
	require.NoError(err)
	require.NotNil(ua)
	assert.Equal(userappearance.StatusWorn, ua.Status)
	assert.Equal(userappearance.OwnStatusNew, ua.OwnStatus)
	assert.Equal(now.Day()+1, time.Unix(*ua.ExpireTime, 0).Day())
}
