package cron

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/models/mongodb/bubble"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/wishes"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestExclusiveCreatorAddParamsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(drawParams{}, "event_id", "period")
}

func TestActionCronWishesDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := map[string]any{"now_time": 0, "period": 30 * goutil.SecondOneMinute}
	// 更新商品售卖时间
	startTime := time.Date(2022, 9, 22, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2022, 9, 23, 23, 30, 0, 0, time.Local)
	require.NoError(service.LiveDB.Table(livegoods.TableName()).
		Where("type = ?", livegoods.GoodsTypeWish).
		Update(map[string]interface{}{
			"sale_start_time": startTime.Unix(),
			"sale_end_time":   endTime.Unix(),
		}).Error)

	// 查询商品信息
	require.NoError(service.LRURedis.Del(keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeWish)).Err())
	liveGoodsList, err := livegoods.ListLiveGoods(livegoods.GoodsTypeWish)
	require.NoError(err)
	assert.Len(liveGoodsList, 2)

	// 测试未到开奖时间
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 9, 22, 0, 29, 0, 0, time.Local)
	})
	res, err := ActionCronWishesDraw(handler.NewTestContext(http.MethodPost, "/", false, body))
	require.NoError(err)
	assert.Equal("未到开奖时间", res)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 9, 22, 0, 30, 0, 0, time.Local)
	})

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	// 删除许愿记录
	_, err = wishes.Collection().DeleteMany(ctx, bson.M{
		"status":      wishes.StatusWaiting,
		"create_time": bson.M{"$gte": startTime.Unix(), "$lt": endTime.Unix()},
	})
	assert.NoError(err)

	// 测试在开奖时间但没有许愿记录
	res, err = ActionCronWishesDraw(handler.NewTestContext(http.MethodPost, "/", false, body))
	require.NoError(err)
	assert.Equal("本轮许愿记录为空", res)

	now := goutil.TimeNow()
	wish1 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   startTime.Unix() + 1,
		ModifiedTime: startTime.Unix() + 1,
		UserID:       10,
		GoodsID:      liveGoodsList[0].ID,
		GoodsPrice:   100,
		Num:          2,
		Status:       wishes.StatusWaiting,
	}
	wish2 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   startTime.Unix() + goutil.SecondOneMinute,
		ModifiedTime: startTime.Unix() + goutil.SecondOneMinute,
		UserID:       11,
		GoodsID:      liveGoodsList[1].ID,
		GoodsPrice:   10,
		Num:          2,
		Status:       wishes.StatusWaiting,
	}
	// 插入许愿记录测试数据
	_, err = wishes.Collection().InsertMany(ctx, []interface{}{wish1, wish2})
	require.NoError(err)

	gift1 := gift.Gift{
		OID:       primitive.NewObjectIDFromTimestamp(now),
		GiftID:    40052,
		Name:      "许愿池初级礼物",
		NameClean: "许愿池初级礼物",
		Type:      gift.TypeRebate,
		Order:     1,
		AddedTime: now.Add(5 * -time.Minute),
	}
	gift2 := gift.Gift{
		OID:       primitive.NewObjectIDFromTimestamp(now),
		GiftID:    40050,
		Name:      "许愿池高级礼物（大奖）",
		NameClean: "许愿池高级礼物（大奖）",
		Type:      gift.TypeRebate,
		Order:     2,
		AddedTime: now.Add(5 * -time.Minute),
	}
	gift3 := gift.Gift{
		OID:       primitive.NewObjectIDFromTimestamp(now),
		GiftID:    40051,
		Name:      "许愿池高级礼物（小奖）",
		NameClean: "许愿池高级礼物（小奖）",
		Type:      gift.TypeRebate,
		Order:     3,
		AddedTime: now.Add(5 * -time.Minute),
	}
	// 清空测试用礼物数据
	_, err = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": []int64{40050, 40051, 40052}}})
	require.NoError(err)
	// 生成测试礼物数据
	_, err = gift.Collection().InsertMany(ctx, []interface{}{gift1, gift2, gift3})
	require.NoError(err)

	// 设置飘屏气泡框
	b := bubble.Bubble{BubbleID: bubbleIDWishes, Image: "oss://live/bubbles/notify/b214_0_84_0_128.png", Type: bubble.TypeNotify, NormalColor: "#E8E9FF"}
	_, err = bubble.Collection().UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)

	cancel = mrpc.SetMock("go://event/drawpoint/update",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"point": 100}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"count": 2}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock("im://broadcast/all",
		func(input interface{}) (output interface{}, err error) {
			return "success", nil
		})
	defer cancel()

	// 测试正常开奖
	res, err = ActionCronWishesDraw(handler.NewTestContext(http.MethodPost, "/", false, body))
	require.NoError(err)
	assert.Equal("success", res)

	// 重置 TimeNow 函数
	goutil.SetTimeNow(nil)
}

func TestNewDrawParams(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 更新商品售卖时间
	startTime := time.Date(2022, 9, 22, 0, 0, 0, 0, time.Local)
	endTime := time.Date(2022, 9, 23, 23, 30, 0, 0, time.Local)
	require.NoError(service.LiveDB.Table(livegoods.TableName()).
		Where("type = ?", livegoods.GoodsTypeWish).
		Update(map[string]interface{}{
			"sale_start_time": startTime.Unix(),
			"sale_end_time":   endTime.Unix(),
		}).Error)

	// 删除商品缓存
	require.NoError(service.LRURedis.Del(keys.KeyLiveGoodsList1.Format(livegoods.GoodsTypeWish)).Err())

	// 测试不在活动时间范围内
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 9, 21, 0, 0, 0, 0, time.Local)
	})
	body := map[string]any{"now_time": 0, "period": 30 * goutil.SecondOneMinute}
	_, err := newDrawParams(handler.NewTestContext(http.MethodPost, "/", false, body))
	assert.EqualError(err, "未找到商品信息")

	// 测试活动开始 1 小时（第二次抽奖时间）
	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 9, 22, 1, 0, 0, 0, time.Local)
	})
	param, err := newDrawParams(handler.NewTestContext(http.MethodPost, "/", false, body))
	require.NoError(err)
	assert.True(param.draw)
	assert.Equal(startTime.Add(30*time.Minute).Unix(), param.startTime)
	assert.Equal(startTime.Add(60*time.Minute).Unix(), param.endTime)
	assert.Nil(param.liveGoodsMap)
	assert.Nil(param.liveGoodsMoreMap)
	assert.Nil(param.giftMap)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	gift1 := gift.Gift{
		OID:       primitive.NewObjectIDFromTimestamp(now),
		GiftID:    40052,
		Name:      "许愿池初级礼物",
		NameClean: "许愿池初级礼物",
		Type:      gift.TypeRebate,
		Order:     1,
		AddedTime: now.Add(5 * -time.Minute),
	}
	gift2 := gift.Gift{
		OID:       primitive.NewObjectIDFromTimestamp(now),
		GiftID:    40050,
		Name:      "许愿池高级礼物（大奖）",
		NameClean: "许愿池高级礼物（大奖）",
		Type:      gift.TypeRebate,
		Order:     2,
		AddedTime: now.Add(5 * -time.Minute),
	}
	gift3 := gift.Gift{
		OID:       primitive.NewObjectIDFromTimestamp(now),
		GiftID:    40051,
		Name:      "许愿池高级礼物（小奖）",
		NameClean: "许愿池高级礼物（小奖）",
		Type:      gift.TypeRebate,
		Order:     3,
		AddedTime: now.Add(5 * -time.Minute),
	}
	// 清空测试用礼物数据
	_, err = gift.Collection().DeleteMany(ctx, bson.M{"gift_id": bson.M{"$in": []int64{40050, 40051, 40052}}})
	require.NoError(err)
	// 生成测试礼物数据
	_, err = gift.Collection().InsertMany(ctx, []interface{}{gift1, gift2, gift3})
	require.NoError(err)

	wish1 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   startTime.Add(30 * time.Minute).Unix(),
		ModifiedTime: startTime.Add(30 * time.Minute).Unix(),
		UserID:       10,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          2,
		Status:       wishes.StatusWaiting,
	}
	wish2 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   startTime.Add(time.Hour).Unix(),
		ModifiedTime: startTime.Add(time.Hour).Unix(),
		UserID:       11,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          2,
		Status:       wishes.StatusWaiting,
	}
	// 删除数据
	_, err = wishes.Collection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{wish1.OID, wish2.OID}}})
	assert.NoError(err)
	_, err = wishes.Collection().InsertMany(ctx, []interface{}{wish1, wish2})
	require.NoError(err)

	// 测试有许愿记录和礼物数据
	param, err = newDrawParams(handler.NewTestContext(http.MethodPost, "/", false, body))
	require.NoError(err)
	assert.True(param.draw)
	assert.Equal(startTime.Add(30*time.Minute).Unix(), param.startTime)
	assert.Equal(startTime.Add(time.Hour).Unix(), param.endTime)
	assert.Len(param.liveGoodsMap, 2)
	assert.Len(param.liveGoodsMoreMap, 2)
	assert.Len(param.giftMap, 3)

	// 重置 TimeNow 函数
	goutil.SetTimeNow(nil)
}

func TestCheckDrawTime(t *testing.T) {
	assert := assert.New(t)

	startTime := time.Date(2022, 9, 22, 0, 0, 0, 0, time.Local).Unix()
	endTime := time.Date(2022, 9, 23, 23, 30, 0, 0, time.Local).Unix()

	// 测试活动未开始
	param := &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: startTime - 1}
	param.checkDrawTime(startTime, endTime)
	assert.False(param.draw)
	assert.EqualValues(0, param.startTime)
	assert.EqualValues(0, param.endTime)

	// 测试活动刚开始
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: startTime}
	param.checkDrawTime(startTime, endTime)
	assert.False(param.draw)
	assert.EqualValues(0, param.startTime)
	assert.EqualValues(0, param.endTime)

	// 测试第一轮抽奖
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: startTime + 30*goutil.SecondOneMinute}
	param.checkDrawTime(startTime, endTime)
	assert.True(param.draw)
	assert.EqualValues(startTime, param.startTime)
	assert.EqualValues(startTime+30*goutil.SecondOneMinute, param.endTime)

	// 测试第一轮抽奖 (过了 1 分钟，在时间偏移量范围内)
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: startTime + 31*goutil.SecondOneMinute}
	param.checkDrawTime(startTime, endTime)
	assert.True(param.draw)
	assert.Equal(startTime, param.startTime)
	assert.Equal(startTime+30*goutil.SecondOneMinute, param.endTime)

	// 测试第一轮抽奖 (过了 3 分钟，超过时间偏移量)
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: startTime + 33*goutil.SecondOneMinute}
	param.checkDrawTime(startTime, endTime)
	assert.False(param.draw)
	assert.EqualValues(0, param.startTime)
	assert.EqualValues(0, param.endTime)

	// 测试第一天的最后一轮开奖
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: startTime + goutil.SecondOneDay + goutil.SecondOneMinute}
	param.checkDrawTime(startTime, endTime)
	assert.True(param.draw)
	assert.Equal(startTime+goutil.SecondOneDay-30*goutil.SecondOneMinute, param.startTime)
	assert.Equal(startTime+goutil.SecondOneDay, param.endTime)

	// 测试第二天最后一轮开奖 (过了 1 分钟，在时间偏移量范围内)
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: endTime + goutil.SecondOneMinute}
	param.checkDrawTime(startTime, endTime)
	assert.True(param.draw)
	assert.Equal(endTime-30*goutil.SecondOneMinute, param.startTime)
	assert.Equal(endTime, param.endTime)

	// 测试第二天最后一轮开奖 (过了 3 分钟，超过时间偏移量)
	param = &drawParams{Period: 30 * goutil.SecondOneMinute, nowTime: endTime + 3*goutil.SecondOneMinute}
	param.checkDrawTime(startTime, endTime)
	assert.False(param.draw)
	assert.EqualValues(0, param.startTime)
	assert.EqualValues(0, param.endTime)
}

func TestWishDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	now := goutil.TimeNow()
	nowStamp := now.Unix()
	wish1 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		UserID:       10,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          2,
		Status:       wishes.StatusWaiting,
	}
	wish2 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		UserID:       11,
		GoodsID:      100,
		GoodsPrice:   100,
		Num:          2,
		Status:       wishes.StatusWaiting,
	}
	wish3 := wishes.Wish{
		OID:          primitive.NewObjectIDFromTimestamp(now),
		CreateTime:   nowStamp,
		ModifiedTime: nowStamp,
		UserID:       12,
		GoodsID:      101,
		GoodsPrice:   100,
		Num:          3,
		Status:       wishes.StatusWaiting,
	}
	// 删除数据
	_, err := wishes.Collection().DeleteMany(ctx, bson.M{"_id": bson.M{"$in": []primitive.ObjectID{wish1.OID, wish2.OID, wish3.OID}}})
	assert.NoError(err)
	_, err = wishes.Collection().InsertMany(ctx, []interface{}{wish1, wish2, wish3})
	require.NoError(err)
	param := new(drawParams)
	param.wishesList = []*wishes.Wish{&wish1, &wish2, &wish3}
	param.liveGoodsMap = map[int64]livegoods.LiveGoods{
		100: {
			ID:    100,
			Type:  livegoods.GoodsTypeWish,
			Num:   1,
			Price: 500,
			Title: "秘境感应",
		},
		101: {
			ID:    101,
			Type:  livegoods.GoodsTypeWish,
			Num:   1,
			Price: 10,
			Title: "梦墟感应",
		},
	}
	param.liveGoodsMoreMap = make(map[int64]*livegoods.More, 2)
	param.liveGoodsMoreMap[100] = &livegoods.More{
		Gifts:     []livegoods.GiftItem{{ID: 40052, Num: 1, Duration: 21600000, Rate: 22500}},
		PointRate: 977500,
	}
	param.liveGoodsMoreMap[101] = &livegoods.More{
		Gifts:     []livegoods.GiftItem{{ID: 40050, Num: 1, Duration: 21600000, Rate: 170}, {ID: 40051, Num: 1, Duration: 21600000, Rate: 250000}},
		PointRate: 749830,
	}

	param.giftMap = make(map[int64]*gift.Gift, 3)
	param.giftMap[40050] = &gift.Gift{GiftID: 40050, Name: "礼物名称 1", Type: useritems.TypeRebateGift}
	param.giftMap[40051] = &gift.Gift{GiftID: 40051, Name: "礼物名称 2", Type: useritems.TypeRebateGift}
	param.giftMap[40052] = &gift.Gift{GiftID: 40052, Name: "礼物名称 3", Type: useritems.TypeRebateGift}

	cancel = mrpc.SetMock("go://event/drawpoint/update",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"point": 100}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock(pushservice.Scheme+"://api/systemmsg",
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"count": 2}, nil
		})
	defer cancel()

	cancel = mrpc.SetMock("im://broadcast/all",
		func(input interface{}) (output interface{}, err error) {
			return "success", nil
		})
	defer cancel()

	// 测试正常开奖
	err = param.wishDraw()
	require.NoError(err)
}

func TestNotifyDrawEnd(t *testing.T) {
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	b := bubble.Bubble{BubbleID: bubbleIDWishes, Image: "oss://live/bubbles/notify/b214_0_84_0_128.png", Type: bubble.TypeNotify, NormalColor: "#E8E9FF"}
	_, err := bubble.Collection().UpdateOne(ctx, bson.M{"bubble_id": b.BubbleID},
		bson.M{"$set": b}, options.Update().SetUpsert(true))
	require.NoError(err)

	cancel = mrpc.SetMock("im://broadcast/all",
		func(input interface{}) (output interface{}, err error) {
			return "success", nil
		})
	defer cancel()

	notifyDrawEnd()
}
