package cron

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionCronLuckyBagDraw(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var testRoomID int64 = 20240702
	require.NoError(luckybag.DB().Delete(luckybag.InitiateRecord{}, "room_id = ?", testRoomID).Error)

	res, err := luckybag.FindDelayPendingInitiateRecord(time.Minute)
	require.NoError(err)
	assert.Empty(res)

	now := goutil.TimeNow()
	r := luckybag.InitiateRecord{
		RoomID:           testRoomID,
		StartTime:        now.Unix(),
		PrizeIPRID:       1,
		Type:             luckybag.TypeDrama,
		Status:           luckybag.StatusPending,
		ScheduledEndTime: now.Add(-5 * time.Minute).Unix(),
	}
	require.NoError(r.Create())
	require.NoError(service.Redis.Del(keys.DelayKeyDrawLuckyBag1.Format(r.ID)).Err())
	service.DatabusDelayPub.ClearDebugPubMsgs()
	defer service.DatabusDelayPub.ClearDebugPubMsgs()

	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	_, err = ActionCronLuckyBagDraw(c)
	require.NoError(err)

	msgs := service.DatabusDelayPub.DebugPubMsgs()
	require.Len(msgs, 1)
	m := <-msgs
	require.Equal(keys.DelayKeyDrawLuckyBag1.Format(r.ID), m.Key)

	var message struct {
		LuckyBagID int64 `json:"lucky_bag_id"`
	}
	require.NoError(json.Unmarshal(m.Value, &message))
	assert.EqualValues(r.ID, message.LuckyBagID)
}
