package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
)

const (
	followed   = 1
	unfollowed = 0
)

// ChangeAttentionParam is the input of ActionChangeAttention
type ChangeAttentionParam struct {
	Attention   *int  `json:"attention"` // 1 为关注，0 取消关注
	AttentionID int64 `json:"attention_id"`
	UserID      int64 `json:"user_id"`
}

// ChangeAttentionResult is the result of ActionChangeAttention
type ChangeAttentionResult struct {
	Msg         string `json:"msg"`
	Type        int    `json:"type"`
	AttentionID int64  `json:"attention_id"`
}

// ActionChangeAttention 设置用户关注状态
/**
 * @api {post} /rpc/user/changeattention 设置用户关注状态
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number} attention 1 为关注，0 取消关注
 * @apiParam {number} attention_id 被关注者 ID
 * @apiParam {number} user_id 关注者 ID
 *
 * @apiSuccess (200) {number} code CodeSuccess = 0.
 * @apiSuccess (200) {object} info
 * @apiSuccess (200) {string} info.msg 消息："关注成功"，"取消关注成功"
 * @apiSuccess (200) {number} info.type 1 为关注，0 取消关注
 * @apiSuccess (200) {number} info.attention_id 被关注者 ID
 *
 * @apiError (500) {number} code 100010007
 * @apiError (500) {string} info 相关错误信息
 */
func ActionChangeAttention(c *handler.Context) (handler.ActionResponse, error) {
	var input ChangeAttentionParam
	err := input.load(c)
	if err != nil {
		return nil, err
	}

	resp := ChangeAttentionResult{
		Type:        *input.Attention,
		AttentionID: input.AttentionID,
	}
	if *input.Attention != unfollowed {
		err = attentionuser.RPCFollow(input.UserID, input.AttentionID, c)
		if err != nil {
			return nil, err
		}
		resp.Msg = "关注成功"
		return resp, nil
	}
	err = attentionuser.RPCUnfollow(input.UserID, input.AttentionID, c)
	if err != nil {
		return nil, err
	}
	resp.Msg = "取消关注成功"
	return resp, nil
}

func (input *ChangeAttentionParam) load(c *handler.Context) error {
	err := c.BindJSON(&input)
	if err != nil {
		return actionerrors.ErrParams
	}

	if input.UserID == 0 || input.AttentionID == 0 || input.Attention == nil {
		return actionerrors.ErrParamsMsg("输入参数不可为空")
	}

	if *input.Attention != followed && *input.Attention != unfollowed {
		return actionerrors.ErrParams
	}

	if input.UserID == input.AttentionID {
		return actionerrors.ErrParamsMsg("不能关注自己")
	}

	var user struct {
		ID int64 `gorm:"column:id"`
	}
	// rpc 只判断了被关注人是否存在，未判断粉丝是否存在
	err = service.DB.Table(mowangskuser.TableName()).Select("id").
		Where("id = ?", input.UserID).Find(&user).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return actionerrors.ErrCannotFindUser
		}
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}
