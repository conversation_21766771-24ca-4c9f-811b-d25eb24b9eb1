package webrpc

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mongodb/viewlog"
	"github.com/MiaoSiLa/live-service/models/mysql/livebirthdayprivrecord"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	"github.com/MiaoSiLa/missevan-go/tutil"
	"github.com/MiaoSiLa/missevan-go/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRoomUserInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": 12})
	_, err := ActionRoomUserInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	var (
		testUserID int64 = 12
		testRoomID int64 = 4381915
	)
	ua := userappearance.UserAppearance{
		UserID: testUserID,
		Status: userappearance.StatusWorn,
		Type:   appearance.TypeEntryBubble,
		Image:  "test_image",
	}
	va := userappearance.UserAppearance{
		UserID:     testUserID,
		Status:     userappearance.StatusWorn,
		Type:       appearance.TypeVehicle,
		MessageBar: &appearance.MessageBar{Message: "message"},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = userappearance.Collection().DeleteOne(ctx, bson.M{
		"user_id": testUserID,
		"type":    appearance.TypeEntryBubble,
	})
	require.NoError(err)
	_, err = userappearance.Collection().DeleteOne(ctx, bson.M{
		"user_id": testUserID,
		"type":    appearance.TypeVehicle,
	})
	require.NoError(err)
	uaKey := keys.KeyUsersWornAppearancesSets1.Format(testUserID)
	require.NoError(service.Redis.Del(uaKey).Err())

	testExpireTime := goutil.TimeNow().Add(time.Hour).Unix()
	cancel = mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return vip.UserVipsResp{
			Vips: map[int]*vip.UserVip{
				vip.TypeLiveNoble: {
					UserID:     testUserID,
					Type:       vip.TypeLiveNoble,
					ExpireTime: testExpireTime,
				},
			},
		}, nil
	})
	defer cancel()
	vip.MockVipList()

	ctx, cancel = service.MongoDB.Context()
	defer cancel()
	_, err = usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)
	_, err = usermeta.Collection().InsertOne(ctx, bson.M{
		"user_id": testUserID,
		"custom_welcome_message": &usermeta.CustomWelcomeMessage{
			Text:       "测试欢迎语",
			ExpireTime: testExpireTime,
		},
	})
	require.NoError(err)

	// 不隐身，有自定义欢迎语，没有进场通知
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": testUserID, "room_id": testRoomID})
	r, err := ActionRoomUserInfo(c)
	require.NoError(err)
	resp := r.(*liverpc.RoomUserInfoResp)
	require.NotNil(resp.User)
	require.NotNil(resp.User.CustomWelcomeMessage)
	assert.Equal("测试欢迎语", resp.User.CustomWelcomeMessage.Text)

	_, err = userappearance.Collection().InsertOne(ctx, ua)
	require.NoError(err)
	_, err = userappearance.Collection().InsertOne(ctx, va)
	require.NoError(err)
	require.NoError(service.Redis.Del(uaKey).Err())

	// 不隐身，有座驾，有进场通知
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": testUserID, "room_id": testRoomID})
	r, err = ActionRoomUserInfo(c)
	require.NoError(err)
	resp = r.(*liverpc.RoomUserInfoResp)
	require.NotNil(resp.User)
	assert.Equal(testUserID, resp.User.UserID())
	assert.True(resp.IsValuableUser)
	assert.False(resp.IsInvisible)
	require.NotNil(resp.User.Vehicle)
	assert.Equal(va.MessageBar.Message, resp.User.Vehicle.MessageBar.Message)
	assert.Nil(resp.User.CustomWelcomeMessage)
	require.NotNil(resp.User.EntryBubble)
	assert.Equal("测试欢迎语", resp.User.EntryBubble.WelcomeMessage.Text)

	// 贵族隐身
	testUserID = int64(3456835)
	testRoomID = int64(4381915)
	service.Cache5Min.Set(keys.LocalKeyIMJoinInvisibleUserIDs0.Format(), map[int64]struct{}{testUserID: {}}, 0)
	defer func() {
		service.Cache5Min.Flush()
	}()
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": testUserID},
		bson.M{"total_spend": minValuableUserSpend}))
	_, err = livemedal.Collection().DeleteOne(ctx, bson.M{
		"user_id": testUserID,
		"room_id": testRoomID,
	})
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": testUserID, "room_id": testRoomID})
	r, err = ActionRoomUserInfo(c)
	require.NoError(err)
	resp = r.(*liverpc.RoomUserInfoResp)
	require.NotNil(resp.User)
	assert.Equal(testUserID, resp.User.UserID())
	assert.True(resp.IsValuableUser)
	assert.True(resp.IsInvisible)

	// 消费不达标，无勋章，有贵族
	require.NoError(userstatus.GeneralSetOne(bson.M{"user_id": testUserID},
		bson.M{"total_spend": 0}))
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": testUserID, "room_id": testRoomID})
	r, err = ActionRoomUserInfo(c)
	require.NoError(err)
	resp = r.(*liverpc.RoomUserInfoResp)
	require.NotNil(resp.User)
	assert.True(resp.IsValuableUser)

	// 消费不达标，无勋章，无贵族
	key := keys.KeyNobleUserVips1.Format(testUserID)
	require.NoError(service.Redis.Set(key, "{}", 10*time.Second).Err())
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": testUserID, "room_id": testRoomID})
	r, err = ActionRoomUserInfo(c)
	require.NoError(err)
	resp = r.(*liverpc.RoomUserInfoResp)
	require.NotNil(resp.User)
	assert.False(resp.IsValuableUser)

	// 消费不达标，有勋章，无贵族
	lm := &livemedal.LiveMedal{
		Simple: livemedal.Simple{
			UserID: testUserID,
			RoomID: testRoomID,
			Point:  1,
			Status: livemedal.StatusShow,
		},
	}
	_, err = livemedal.Collection().InsertOne(ctx, lm)
	require.NoError(err)
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"user_id": testUserID, "room_id": testRoomID})
	r, err = ActionRoomUserInfo(c)
	require.NoError(err)
	resp = r.(*liverpc.RoomUserInfoResp)
	require.NotNil(resp.User)
	require.NotNil(resp.RoomMedal)
	assert.True(resp.IsValuableUser)
}

func TestActionRoomUsers(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "", false,
		handler.M{"user_ids": []int64{}, "room_id": 4381915})
	_, err := ActionRoomUsers(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewTestContext("POST", "", false,
		handler.M{"user_ids": []int64{-1, 12, 10}, "room_id": 4381915})
	resp, err := ActionRoomUsers(c)
	require.NoError(err)
	require.NotEmpty(resp)
}

func TestAssignUserCustomWelcomeMessages(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	user := liveuser.Simple{
		UID: 191819,
		EntryBubble: &userappearance.EntryBubble{
			WelcomeMessage: &appearance.WelcomeMessage{},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := usermeta.Collection().DeleteOne(ctx, bson.M{"user_id": user.UserID()})
	require.NoError(err)
	assignUserCustomWelcomeMessages([]*liveuser.Simple{&user})
	assert.Empty(user.EntryBubble.WelcomeMessage)

	um := usermeta.UserMeta{
		UserID: user.UserID(),
		CustomWelcomeMessage: &usermeta.CustomWelcomeMessage{
			Text:       "test",
			ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
		},
	}
	_, err = usermeta.Collection().InsertOne(ctx, um)
	require.NoError(err)
	assignUserCustomWelcomeMessages([]*liveuser.Simple{&user})
	assert.Equal("test", user.EntryBubble.WelcomeMessage.Text)
	user.EntryBubble = nil
	assignUserCustomWelcomeMessages([]*liveuser.Simple{&user})
	require.NotNil(user.CustomWelcomeMessage)
	assert.Equal("test", user.CustomWelcomeMessage.Text)
}

func TestActionUser(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewTestContext("POST", "/user/info", false,
		tutil.ToRequestBody(`{"user_id":-1}`))
	_, err := ActionUser(c)
	assert.Equal(actionerrors.ErrParams, err)
	c = handler.NewTestContext("POST", "/user/info", false,
		tutil.ToRequestBody(`{"user_id":123416789}`))
	_, err = ActionUser(c)
	assert.Equal(actionerrors.ErrCannotFindUser, err)
	c = handler.NewTestContext("POST", "/user/info", false,
		tutil.ToRequestBody(`{"user_id":12}`))
	data, err := ActionUser(c)
	assert.NoError(err)
	assert.NotNil(data)
}

func TestActionUserTitles(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 查找为空
	c := handler.NewTestContext(http.MethodPost, "/user/titles", false, nil)
	r, err := ActionUserTitles(c)
	require.NoError(err)
	var resp []liveuser.Title
	require.IsType(resp, r)
	resp = r.([]liveuser.Title)
	assert.Empty(resp)

	// 正确查找
	mAppearance := make(map[int][]*userappearance.UserAppearance)
	mAppearance[appearance.TypeAvatarFrame] = []*userappearance.UserAppearance{{
		AppearanceID: 12,
		UserID:       12,
		Name:         "测试用户 Title 头像框",
		Image:        "https://http://test.png",
	}}
	b, err := json.Marshal(mAppearance)
	require.NoError(err)
	key := keys.KeyUsersWornAppearancesSets1.Format(12)
	err = service.Redis.Set(key, b, 10*time.Second).Err()
	require.NoError(err)

	_, err = liveuser.Update(12, bson.M{"name_color": "test_color"})
	require.NoError(err)
	c = handler.NewTestContext("POST", "/user/titles", false, map[string]int64{"user_id": 12})
	r, err = ActionUserTitles(c)
	require.NoError(err)
	resp = r.([]liveuser.Title)
	assert.GreaterOrEqual(len(resp), 2) // 至少有推荐头像框 + 彩色昵称

	userappearance.ClearCache(3456835)

	c = handler.NewTestContext("POST", "/user/titles", false, map[string]int64{"user_id": 3456835})
	r, err = ActionUserTitles(c)
	require.NoError(err)
	resp = r.([]liveuser.Title)
	assert.NotEmpty(resp)
	var found bool
	for i := range resp {
		if resp[i].Type == liveuser.TitleTypeMedal && resp[i].Level == 1 {
			found = true
			break
		}
	}
	assert.True(found)
}

func TestActionUserBlock(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/", false, nil)
	_, err := ActionUserBlockAdd(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrParams, err)

	input := handler.M{
		"user_ids":    []int64{-13, -14},
		"expire_time": 0,
	}
	// 封禁
	c = handler.NewTestContext("POST", "/", false, input)
	_, err = ActionUserBlockAdd(c)
	require.NoError(err)

	// 解封
	input = handler.M{
		"user_ids": []int64{-13, -14, 999999999},
	}
	c = handler.NewTestContext("POST", "/", false, input)
	_, err = ActionUserBlockRemove(c)
	require.NoError(err)
}

func TestActionUserLevel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(vip.URLUserVips, func(any) (any, error) {
		return nil, nil
	})
	defer cleanup()

	cancel := vip.MockVipList()
	defer cancel()

	c := handler.NewTestContext("POST", "/", false, nil)
	_, err := ActionUserLevel(c)
	require.Error(err)
	assert.Equal(actionerrors.ErrParams, err)
	input := handler.M{
		"user_id": 999999999,
	}
	c = handler.NewTestContext("POST", "/", false, input)
	_, err = ActionUserLevel(c)
	require.Error(err, "cannot find live level of user")
	input = handler.M{
		"user_id": 346286,
	}
	c = handler.NewTestContext("POST", "/", false, input)
	r, err := ActionUserLevel(c)
	require.NoError(err)
	assert.JSONEq(`{"live_level":4,"noble_level":0,"highness_level":0}`, tutil.SprintJSON(r))

	testVipUserID := int64(3456835)
	key := keys.KeyNobleUserVips1.Format(testVipUserID)
	require.NoError(service.Redis.Set(key, `{"1":{"type":1,"level":4,"user_id":3456835},"2":{"type":2,"level":1,"user_id":3456835,"expire_time":9999999999}}`,
		30*time.Second).Err())
	require.NoError(err)
	defer func() {
		assert.NoError(service.Redis.Del(key).Err())
	}()
	input = handler.M{
		"user_id": testVipUserID,
	}
	c = handler.NewTestContext("POST", "/", false, input)
	r, err = ActionUserLevel(c)
	require.NoError(err)
	resp, ok := r.(*userLevelResp)
	require.True(ok)
	assert.Equal(1, resp.HighnessLevel)
	assert.Zero(resp.NobelLevel, "过期贵族")
	assert.Nil(resp.RoomMedal)

	testMedal, err := livemedal.FindOne(bson.M{
		"user_id": testVipUserID,
		"status":  bson.M{"$gt": livemedal.StatusPending},
	}, nil, livemedal.FindOptions{OnlyMedal: true})
	require.NoError(err)
	require.NotNil(testMedal)

	input["user_id"] = testMedal.UserID
	input["room_id"] = testMedal.RoomID
	c = handler.NewTestContext("POST", "/", false, input)
	r, err = ActionUserLevel(c)
	require.NoError(err)
	resp, ok = r.(*userLevelResp)
	require.True(ok)
	assert.NotNil(resp.RoomMedal)

	// 测试没粉丝勋章
	input["user_id"] = 9074509
	c = handler.NewTestContext("POST", "/", false, input)
	r, err = ActionUserLevel(c)
	require.NoError(err)
	resp, ok = r.(*userLevelResp)
	require.True(ok)
	assert.Nil(resp.RoomMedal)
}

func TestActionUserLevelUp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := userLevelUpParams{
		UserID:    10,
		LevelFrom: 10,
		LevelTo:   5,
	}
	c := handler.NewTestContext(http.MethodPost, "/", false, p)
	_, err := ActionUserLevelUp(c)
	assert.Equal(actionerrors.ErrParams, err)

	p.LevelTo = 11
	c = handler.NewTestContext(http.MethodPost, "/", false, p)
	res, err := ActionUserLevelUp(c)
	require.NoError(err)
	r, ok := res.(handler.M)
	require.True(ok)
	assert.Equal("success", r["msg"])
}

func TestActionUserViewLogDelete(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	updateViewLog := func(userID int64, roomID int64, roomIODHex string) error {
		roomOID, err := primitive.ObjectIDFromHex(roomIODHex)
		if err != nil {
			return err
		}
		r := room.Room{
			OID: roomOID,
			Helper: room.Helper{
				RoomID: roomID,
			},
		}
		return viewlog.Update(userID, &r, "127.0.0.1", util.TimeNow())
	}

	// 插入数据
	userID := int64(1145142)
	require.NoError(updateViewLog(userID, 1919810, "63886201968c1c17734091c1"))
	require.NoError(updateViewLog(userID, 1919816, "63886201968c1c17734091c2"))
	require.NoError(updateViewLog(userID, 1919818, "63886201968c1c17734091c3"))

	// 测试参数错误
	input := handler.M{
		"user_id":  userID,
		"room_ids": []int64{1919810},
		"type":     2,
	}
	c := handler.NewTestContext("POST", "/", false, input)
	_, err := ActionUserViewLogDelete(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试单独删除
	input["type"] = 0
	c = handler.NewTestContext("POST", "/", false, input)
	resp, err := ActionUserViewLogDelete(c)
	require.NoError(err)
	assert.Equal("删除成功", resp)

	// 确认目标已删除
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	tryFindViewLog := func(userID int64, roomID int64) error {
		return viewlog.Collection().FindOne(ctx, bson.M{"user_id": userID, "room_id": roomID}).Err()
	}
	assert.NoError(tryFindViewLog(userID, 1919816))
	assert.NoError(tryFindViewLog(userID, 1919818))
	assert.True(mongodb.IsNoDocumentsError(tryFindViewLog(userID, 1919810)))

	// 测试全部删除
	input["type"] = 1
	c = handler.NewTestContext("POST", "/", false, input)
	resp, err = ActionUserViewLogDelete(c)
	require.NoError(err)
	assert.Equal("删除成功", resp)

	// 确认全部已删除
	assert.True(mongodb.IsNoDocumentsError(tryFindViewLog(userID, 1919816)))
	assert.True(mongodb.IsNoDocumentsError(tryFindViewLog(userID, 1919818)))
}

func TestActionBackpackAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uri := "user/backpack/add"
	c := handler.NewRPCTestContext(uri, handler.M{"user_ids": "12"})
	_, err := ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewRPCTestContext(uri, handler.M{"user_ids": []int64{12}, "num": 0})
	_, err = ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("礼物数量错误"), err)

	c = handler.NewRPCTestContext(uri, handler.M{
		"user_ids": []int64{12},
		"num":      1,
		"end_time": 1,
	})
	_, err = ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("结束时间错误"), err)

	c = handler.NewRPCTestContext(uri, handler.M{
		"user_ids": []int64{12},
		"num":      1,
		"end_time": goutil.TimeNow().Unix() + 1000,
		"gift_id":  -1,
	})
	_, err = ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("礼物不存在"), err)

	c = handler.NewRPCTestContext(uri, handler.M{
		"user_ids": []int64{12},
		"num":      1,
		"end_time": goutil.TimeNow().Unix() + 1000,
		"gift_id":  4,
	})
	_, err = ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("该礼物不是背包礼物"), err)

	c = handler.NewRPCTestContext(uri, handler.M{
		"user_ids": []int64{12, 12},
		"num":      1,
		"end_time": goutil.TimeNow().Unix() + 1000,
		"gift_id":  useritems.GiftIDCatFood,
	})
	_, err = ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("用户 12 重复输入"), err)

	c = handler.NewRPCTestContext(uri, handler.M{
		"user_ids": []int64{999},
		"num":      1,
		"end_time": goutil.TimeNow().Unix() + 1000,
		"gift_id":  useritems.GiftIDCatFood,
	})
	_, err = ActionBackpackAdd(c)
	assert.Equal(actionerrors.ErrParamsMsg("用户 999 不存在"), err)

	now := goutil.TimeNow()
	c = handler.NewRPCTestContext(uri, handler.M{
		"user_ids": []int64{12},
		"num":      1,
		"end_time": now.Unix() + 1000,
		"gift_id":  useritems.GiftIDCatFood,
	})
	r, err := ActionBackpackAdd(c)
	require.NoError(err)
	assert.Equal("发放成功", r)
}

func TestActionGiftCustomAdd(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uri := "/rpc/user/gift/custom/add"
	c := handler.NewRPCTestContext(uri, handler.M{"user_id": 12})
	_, err := ActionGiftCustomAdd(c)
	assert.Equal(actionerrors.ErrParams, err)

	c = handler.NewRPCTestContext(uri, handler.M{
		"user_id": 122,
		"gift_id": 11,
		"day":     3,
	})
	resp, err := ActionGiftCustomAdd(c)
	require.NoError(err)
	assert.Equal("发放成功", resp)
}

func TestActionBirthdayPriv(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 1, 26, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceIDBirthdayAvatarFrameForLv45,
			"type": appearance.TypeAvatarFrame,
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "oss://live/avatarframes/40261.png",
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	_, err = appearance.Collection().UpdateOne(ctx,
		bson.M{
			"id":   appearance.AppearanceIDBirthdayBadgeForLv45,
			"type": appearance.TypeBadge,
		},
		bson.M{"$set": bson.M{
			"name": "限定寿星",
			"icon": "oss://live/badges/50227.png",
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	testIDs := []int64{1919, 1515, 1616}
	_, err = liveuser.Collection().UpdateMany(ctx,
		bson.M{"user_id": bson.M{"$in": testIDs[0:2]}},
		bson.M{"$set": bson.M{"contribution": 3940000000}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)
	_, err = liveuser.Collection().UpdateOne(ctx,
		bson.M{"user_id": testIDs[2]},
		bson.M{"$set": bson.M{"contribution": 1}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	_, err = userappearance.Collection().DeleteMany(ctx, bson.M{"user_id": bson.M{"$in": testIDs}})
	require.NoError(err)

	now := goutil.TimeNow()
	param := birthdayPrivParam{
		UserID:        testIDs[0],
		BirthdateMMDD: "0126",
	}
	c := handler.NewTestContext(http.MethodPost, "", true, param)
	res, err := ActionBirthdayPriv(c)
	require.NoError(err)
	assert.Equal("success", res)
	param.UserID = testIDs[2]
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	res, err = ActionBirthdayPriv(c)
	require.NoError(err)
	assert.Equal("success", res)

	err = livebirthdayprivrecord.LiveBirthdayPrivRecord{}.DB().Delete("", "user_id = ?", testIDs[1]).Error
	require.NoError(err)
	require.False(livebirthdayprivrecord.IsRewarded(testIDs[1], now.Year()))
	param.UserID = testIDs[1]
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	res, err = ActionBirthdayPriv(c)
	require.NoError(err)
	assert.Equal("success", res)
	require.True(livebirthdayprivrecord.IsRewarded(testIDs[1], now.Year()))

	var ua userappearance.UserAppearance
	require.NoError(userappearance.Collection().FindOne(ctx, bson.M{"user_id": testIDs[1]}).Decode(&ua))
	require.NotNil(ua)
	assert.Equal(now.Day()+1, time.Unix(*ua.ExpireTime, 0).Day())
}

func TestBirthdayPrivParam_isBirthday(t *testing.T) {
	assert := assert.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2023, 2, 28, 0, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	now := goutil.TimeNow()
	params := birthdayPrivParam{BirthdateMMDD: "0227"}
	assert.False(params.isBirthday(now))
	params.BirthdateMMDD = "0228"
	assert.True(params.isBirthday(now))
	params.BirthdateMMDD = "0229"
	assert.True(params.isBirthday(now))

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2024, 2, 28, 0, 0, 0, 0, time.Local)
	})
	now = goutil.TimeNow()
	assert.False(params.isBirthday(now))
	params.BirthdateMMDD = "0228"
	assert.True(params.isBirthday(now))
}
