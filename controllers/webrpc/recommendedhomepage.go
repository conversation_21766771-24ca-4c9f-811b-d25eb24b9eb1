package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/recommended"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionRecommendedHomepage 获取 APP 首页"正在直播"模块的内容
/**
 * @api {post} /rpc/live-service/recommended/homepage 获取 APP 首页"正在直播"模块的内容
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "rooms": [
 *         {
 *           "position": 1,                                                   // 该字段用作后台设置，这里请忽略
 *           "room_id": 22489473,                                             // 房间号
 *           "name": "23333333",                                              // 直播标题
 *           "cover_url": "https://static.missevan.com/profile/01.png",       // 直播封面图
 *           "creator_id": 10,
 *           "creator_username": "bless01",                                   // 主播昵称
 *           "creator_iconurl": "https://static.missevan.com/profile/01.png", // 主播头像
 *           "catalog_id": 10,                                                // 分区 ID
 *           "catalog_name": "娱乐",                                           // 分区名称
 *           "catalog_color": "#D68DFE",                                      // 分区颜色
 *           "custom_tag": {                                                  // 个性词条
 *             "tag_id": 10001,                                               // 个性词条 ID
 *             "tag_name": "腹黑青叔"                                          // 个性词条名称
 *           },
 *           "status": {                                                      // 该结构体忽略
 *             "open": 1,
 *             "pk": 1, // 1：直播间在 PK 状态，0 或不存在：直播间不在 PK 状态
 *             "red_packet": 1 // 当前待抢红包或可抢红包数量，0 或不存在表示无待抢红包或者可抢红包
 *           }
 *         },
 *         {
 *           "position": 0,
 *           "room_id": 22489474,
 *           "name": "22222223",
 *           "cover_url": "https://static.missevan.com/profile/02.png",
 *           "creator_id": 1234,
 *           "creator_username": "bless02",
 *           "creator_iconurl": "https://static.missevan.com/profile/02.png",
 *           "catalog_id": 10,                                                // 分区 ID
 *           "catalog_name": "娱乐",                                           // 分区名称
 *           "catalog_color": "#D68DFE",                                      // 分区颜色
 *           "custom_tag": {                                                  // 个性词条
 *             "tag_id": 10001,                                               // 个性词条 ID
 *             "tag_name": "腹黑青叔"                                          // 个性词条名称
 *           },
 *           "status": {
 *             "open": 1,
 *             "red_packet": 1
 *           }
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 */
func ActionRecommendedHomepage(c *handler.Context) (handler.ActionResponse, string, error) {
	rooms, err := recommended.ListHomepageRooms()
	if err != nil {
		return nil, "", err
	}
	response := map[string]any{
		"rooms": rooms,
	}
	return response, "", nil
}
