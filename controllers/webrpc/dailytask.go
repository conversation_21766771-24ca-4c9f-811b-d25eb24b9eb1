package webrpc

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mongodb/usermeta"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type dailyTaskParam struct {
	UserID int64 `json:"user_id"`

	resp *dailyTaskResp
}

type dailyTaskResp struct {
	OpenRoomURL                   string `json:"open_room_url"`
	IsNew                         bool   `json:"is_new"`
	TodayListenDuration           *int64 `json:"today_listen_duration,omitempty"`
	IsViewedLiveToday             bool   `json:"is_viewed_live_today"`
	IsFinishedListenDurationToday bool   `json:"is_finished_listen_duration_today"`
	TodayMessageCount             int64  `json:"today_message_count"`
}

// ActionLiveDailyTask 直播每日任务数据
/**
 * @api {post} /rpc/live-service/daily/task 直播每日任务数据
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "",
 *     "data": {
 *       "open_room_url": "missevan://live/9075111", // 随机打开直播间地址
 *       "is_new": true, // 是否有新人任务资格，当天状态不会改变
 *       "today_listen_duration": 100000, // 用户本日收听时长，当日没有进过直播间不返回此字段 单位：毫秒（后续删除）
 *       "today_message_count": 3, // 用户本日在直播间发送的消息数量
 *       "is_viewed_live_today": true, // 用户本日是否访问过直播间，true: 访问过; false: 未访问
 *       "is_finished_listen_duration_today": true // 用户本日收听时长任务是否完成, true: 完成; false: 未完成
 *     }
 *   }
 */
func ActionLiveDailyTask(c *handler.Context) (handler.ActionResponse, string, error) {
	var param dailyTaskParam
	err := c.BindJSON(&param)
	if err != nil || param.UserID <= 0 {
		return nil, "", actionerrors.ErrParams
	}
	param.resp = new(dailyTaskResp)
	err = param.buildOpenRoomURL()
	if err != nil {
		return nil, "", err
	}
	err = param.buildIsNew()
	if err != nil {
		return nil, "", err
	}
	err = param.buildTodayListenDuration()
	if err != nil {
		return nil, "", err
	}
	err = param.buildTodayMessageCount()
	if err != nil {
		return nil, "", err
	}
	return param.resp, "", nil
}

func (param *dailyTaskParam) buildOpenRoomURL() error {
	// 已关注 > 上小时榜 TOP10 > 本小时榜 TOP10 > 实时热度 TOP50 的直播间
	funcList := []func() (int64, error){
		param.findFollowedRoom,
		param.findLastHourRankRoom,
		param.findHourRankRoom,
		param.findHotScoreRoom,
	}
	for _, f := range funcList {
		roomID, err := f()
		if err != nil {
			return err
		}
		if roomID > 0 {
			param.resp.OpenRoomURL = fmt.Sprintf(config.Conf.Params.LiveURL.AppRoom, roomID)
			return nil
		}
	}
	return nil
}

func (param *dailyTaskParam) findFollowedRoom() (int64, error) {
	creatorIDs, err := attentionuser.AllFollowedCreatorIDs(param.UserID)
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	dailyTaskAllowCreatorIDs, err := liverecommendedelements.ListDailyTaskAllowList()
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	creatorIDs = append(creatorIDs, dailyTaskAllowCreatorIDs...)
	if len(creatorIDs) == 0 {
		return 0, nil
	}
	return room.RandomOpenRoomID(creatorIDs)
}

func (param *dailyTaskParam) findLastHourRankRoom() (int64, error) {
	lastRanks, err := usersrank.FindLastRank(usersrank.TypeHour, goutil.TimeNow())
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(lastRanks) == 0 {
		return 0, nil
	}
	creatorIDs := make([]int64, 0, len(lastRanks))
	for _, info := range lastRanks {
		creatorIDs = append(creatorIDs, info.UserID)
	}
	return room.RandomOpenRoomID(creatorIDs)
}

func (param *dailyTaskParam) findHourRankRoom() (int64, error) {
	ranks, err := usersrank.FindRank(goutil.TimeNow(), usersrank.TypeHour)
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(ranks) == 0 {
		return 0, nil
	}
	creatorIDs := make([]int64, 0, len(ranks))
	for _, info := range ranks {
		creatorIDs = append(creatorIDs, info.UserID)
	}
	return room.RandomOpenRoomID(creatorIDs)
}

func (param *dailyTaskParam) findHotScoreRoom() (int64, error) {
	pipeline := []bson.M{
		{"$match": bson.M{
			"status.open": room.StatusOpenTrue,
		}},
		{"$sort": room.SortByScore},
		{"$limit": 50}, // TOP50
		{"$sample": bson.M{"size": 1}},
		{"$project": mongodb.NewProjection("room_id")},
	}
	rooms, err := room.AggregateSimples(pipeline, &room.FindOptions{DisableAll: true})
	if err != nil {
		return 0, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(rooms) == 0 {
		return 0, nil
	}
	return rooms[0].RoomID, nil
}

func (param *dailyTaskParam) buildIsNew() error {
	isNew, err := livelistenlogs.IsNewUser(param.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.resp.IsNew = isNew
	return nil
}

func (param *dailyTaskParam) buildTodayListenDuration() error {
	var err error
	param.resp.TodayListenDuration, err = livelistenlogs.UserTodayAppListenDuration(param.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	param.resp.IsViewedLiveToday = param.resp.TodayListenDuration != nil
	if !param.resp.IsViewedLiveToday {
		return nil
	}

	liveTask, err := params.FindLiveTask()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.resp.IsNew {
		param.resp.IsFinishedListenDurationToday = liveTask.DailyListenDuration.NewUserFinishDuration <= *param.resp.TodayListenDuration
	} else {
		param.resp.IsFinishedListenDurationToday = liveTask.DailyListenDuration.OldUserFinishDuration <= *param.resp.TodayListenDuration
	}
	return nil
}

// buildTodayMessageCount 构建用户当天发送消息的次数
func (param *dailyTaskParam) buildTodayMessageCount() error {
	count, err := usermeta.CountUserTodayMessage(param.UserID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.resp.TodayMessageCount = count
	return nil
}
