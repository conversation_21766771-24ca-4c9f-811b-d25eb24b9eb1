package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type reviewingRoom struct {
	RoomID     int64                  `json:"room_id"`
	Name       string                 `json:"name"`
	CreatorID  int64                  `json:"creator_id"`
	CoverURL   *string                `json:"cover_url"`
	Background *livereview.ReviewInfo `json:"background"`
}

// ActionReviewSet 提交直播间待审核的信息
/**
 * @api {post} /rpc/chatroom/review/set 提交直播间待审核的信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} [name] 房间名称
 * @apiParam {String} [cover] 封面图
 * @apiParam {Object} [background]背景信息
 * @apiParamExample {json} Request-Example:
 *   {
 *     "room_id": 12,
 *     "name": "房间名称"
 *     "cover_url":  "http://static.missevan.com/avatars/icon01.png",
 *     "background": {
 *       "image_url": "http://static.missevan.com/avatars/icon01.png",
 *       "opacity": 1.0
 *     }
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionReviewSet(c *handler.Context) (handler.ActionResponse, error) {
	var param reviewingRoom
	err := c.BindJSON(&param)
	if err != nil || param.RoomID == 0 {
		return nil, actionerrors.ErrParams
	}
	param.CreatorID, err = room.FindCreatorID(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.CreatorID == 0 {
		return nil, actionerrors.ErrCannotFindRoom
	}
	reviewInfo := make([]livereview.ReviewInfo, 0, 3)
	if param.Name != "" {
		reviewInfo = append(reviewInfo, livereview.ReviewInfo{Type: livereview.TypeName,
			Name: param.Name})
	}
	if param.CoverURL != nil {
		reviewInfo = append(reviewInfo, livereview.ReviewInfo{Type: livereview.TypeCover,
			ImageURL: *param.CoverURL})
	}
	if param.Background != nil {
		param.Background.Type = livereview.TypeBackground
		reviewInfo = append(reviewInfo, *param.Background)
	}
	for i := range reviewInfo {
		switch reviewInfo[i].Type {
		case livereview.TypeCover, livereview.TypeBackground:
			if !reviewInfo[i].URLToScheme() {
				return nil, actionerrors.ErrParamsMsg("图片地址错误")
			}
		}
	}
	err = livereview.Submit(param.RoomID, param.CreatorID, reviewInfo)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}

type reviewingInfo struct {
	Name       *string                `json:"name"`
	CoverURL   *string                `json:"cover_url"`
	Background *livereview.ReviewInfo `json:"background"`
}

// ActionReviewGet 获取审核中的直播间的名称、封面、背景图
/**
 * @api {post} /rpc/chatroom/review/get 获取审核中的直播间的名称、封面、背景图
 * @apiDescription 无待审核信息时，对应字段值为 nil
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间 ID
 * @apiParamExample {json} Request-Example:
 *   {
 *     "room_id": 12,
 *   }
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "name": "待审核",
 *       "cover_url":  "http://static.missevan.com/avatars/icon01.png",
 *       "background": {
 *         "image_url": "http://static.missevan.com/avatars/icon01.png",
 *         "opacity": 1.0
 *       }
 *     }
 *   }
 */
func ActionReviewGet(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID int64 `json:"room_id"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	var resp reviewingInfo
	reviewing, err := livereview.FindRoomReviewing(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if cover := reviewing[livereview.TypeCover]; cover != nil {
		cover.SchemeToURL()
		resp.CoverURL = &cover.ImageURL
	}
	if background := reviewing[livereview.TypeBackground]; background != nil {
		background.SchemeToURL()
		resp.Background = background
		if resp.Background.Opacity == nil {
			resp.Background.Opacity = new(float64)
			*resp.Background.Opacity = 1.0
		}
	}
	if name := reviewing[livereview.TypeName]; name != nil {
		resp.Name = &name.Name
	}
	return &resp, nil
}
