package webrpc

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionCheckAttention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	attention := new(attentionuser.AttentionUser)
	err := service.DB.FirstOrCreate(attention, &attentionuser.AttentionUser{
		UserActive: 12, UserPasstive: 5, Time: 1562075169}).Error
	require.NoError(err)
	time.Sleep(200 * time.Millisecond)

	time.Sleep(500 * time.Millisecond)
	input := checkAttentionInput{
		CheckUserIDs: []int64{1, 2, 3, 4, 5},
		UserID:       12,
	}
	byteSlice, _ := json.Marshal(input)
	req := httptest.NewRequest(http.MethodPost, "/person/checkattention", bytes.NewReader(byteSlice))

	c := handler.CreateTestContext(false)
	c.C.Request = req
	res, err := ActionCheckAttention(c)
	require.NoError(err)
	result := res.([]*attentionuser.Attention)
	assert.True(result[4].Followed)
}
