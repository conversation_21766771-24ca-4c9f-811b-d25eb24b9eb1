package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionRoomTagInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := handler.M{}
	c := handler.NewRPCTestContext("/rpc/room/taginfo", body)
	_, err := ActionRoomTagInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	body = handler.M{
		"catalog_id":    104,
		"custom_tag_id": 10001,
	}
	c = handler.NewRPCTestContext("/rpc/room/taginfo", body)
	resp, err := ActionRoomTagInfo(c)
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(tagInfoResp)
	catalog := r.Catalog
	require.NotNil(catalog)
	assert.EqualValues(body["catalog_id"], catalog.CatalogID)
	assert.Equal("音乐", catalog.CatalogName)
	assert.Equal("#AAFAF9", catalog.CatalogColor)
	tag := r.CustomTag
	require.NotNil(tag)
	assert.EqualValues(1, tag.TagGroupID)
	assert.EqualValues("group1", tag.TagGroupName)
	assert.EqualValues(body["custom_tag_id"], tag.TagID)
	assert.EqualValues("test10001", tag.TagName)
}

func TestNewTagInfoParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试分区 ID 和个性词条 ID 为空
	body := handler.M{}
	c := handler.NewRPCTestContext("/rpc/room/taginfo", body)
	_, err := newTagInfoParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试只传 CatalogID
	body["catalog_id"] = 104
	c = handler.NewRPCTestContext("/rpc/room/taginfo", body)
	param, err := newTagInfoParam(c)
	require.NoError(err)
	assert.EqualValues(body["catalog_id"], param.CatalogID)

	// 测试同时传分区 ID 和个性词条 ID
	body["custom_tag_id"] = 10001
	c = handler.NewRPCTestContext("/rpc/room/taginfo", body)
	param, err = newTagInfoParam(c)
	require.NoError(err)
	assert.EqualValues(body["catalog_id"], param.CatalogID)
	assert.EqualValues(body["custom_tag_id"], param.CustomTagID)
}

func TestTagInfoParam_liveCatalogResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有分区 ID
	param := &tagInfoParam{}
	assert.Nil(param.liveCatalogResp())
	assert.Nil(param.resp.Catalog)

	// 测试不存在的分区 ID 返回空
	param.CatalogID = 1
	require.NoError(param.liveCatalogResp())
	assert.Nil(param.resp.Catalog)

	// 测试返回分区信息
	param.CatalogID = 118
	require.NoError(param.liveCatalogResp())
	catalog := param.resp.Catalog
	require.NotNil(catalog)
	assert.Equal(param.CatalogID, catalog.CatalogID)
	assert.Equal("二级音乐1", catalog.CatalogName)
	assert.Equal("#AAFAF9", catalog.CatalogColor)
}

func TestTagInfoParam_customTagResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试没有个性词条 ID
	param := &tagInfoParam{}
	assert.Nil(param.customTagResp())
	assert.Nil(param.resp.CustomTag)

	// 测试不存在的性词条 ID 返回空
	param.CustomTagID = 1
	require.NoError(param.customTagResp())
	assert.Nil(param.resp.CustomTag)

	// 测试返回个性词条信息
	param.CustomTagID = 10001
	require.NoError(param.customTagResp())
	tag := param.resp.CustomTag
	require.NotNil(tag)
	assert.EqualValues(1, tag.TagGroupID)
	assert.EqualValues("group1", tag.TagGroupName)
	assert.Equal(param.CustomTagID, tag.TagID)
	assert.EqualValues("test10001", tag.TagName)
}
