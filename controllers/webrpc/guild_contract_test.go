package webrpc

import (
	"testing"
	"time"

	"github.com/jinzhu/gorm"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/helper"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildGetContract(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	params := handler.M{"user_id": -1}
	ctx := handler.NewRPCTestContext("/rpc/guild/get-contract", params)
	_, err := ActionGuildGetContract(ctx)
	require.EqualError(err, actionerrors.ErrParams.Message)

	// 测试用户与公会不存在生效中的合约
	params = handler.M{"user_id": 99999}
	ctx = handler.NewRPCTestContext("/rpc/guild/get-contract", params)
	resp, err := ActionGuildGetContract(ctx)
	require.NoError(err)
	require.Nil(resp)

	// 测试用户与公会存在生效中的合约
	params = handler.M{"user_id": 5003}
	ctx = handler.NewRPCTestContext("/rpc/guild/get-contract", params)
	resp, err = ActionGuildGetContract(ctx)
	require.NoError(err)
	require.NotNil(resp)
	r := resp.(*liveContractResp)
	assert.Equal(int64(11167), r.ID)
	assert.NotZero(r.RoomID)
}

const (
	testLiveID1 int64 = 3000001
	testLiveID2 int64 = 3000002
)

const expelGuildID = 11

func createExpelTestData() error {
	// 清除旧数据
	err := service.DB.Table(guild.TableName()).Delete("", "id = ?", expelGuildID).Error
	if err != nil {
		return err
	}
	err = service.DB.Table(livecontract.TableName()).Delete("", "guild_id = ?", expelGuildID).Error
	if err != nil {
		return err
	}
	err = service.DB.Table(contractapplyment.TableName()).Delete("", "guild_id = ?", expelGuildID).Error
	if err != nil {
		return err
	}
	// 测试公会
	now := goutil.TimeNow()
	testGuild := guild.Guild{
		ID:                        expelGuildID,
		Name:                      "测试执行清退定时任务公会",
		Intro:                     "xxx",
		OwnerName:                 "test",
		OwnerIDNumber:             "1234",
		OwnerIDPeople:             "oss://test.jpg",
		OwnerBackcover:            "oss://test.jpg",
		Mobile:                    "***********",
		Email:                     "<EMAIL>",
		CorporationName:           "test",
		CorporationAddress:        "test",
		CorporationPhone:          "***********",
		BusinessLicenseNumber:     "***********",
		BusinessLicenseFrontcover: "oss://test.jpg",
		TaxAccount:                "***********",
		BankAccount:               "***********",
		BankAccountName:           "test",
		Bank:                      "test",
		BankAddress:               "test",
		BankBranch:                "test",
		Checked:                   guild.CheckedPass,
		UserID:                    3000000,
		ApplyTime:                 now.AddDate(0, 0, -10).Unix(),
	}
	// 测试合同
	contractList := []livecontract.LiveContract{{
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID1,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -6, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}, {
		GuildID:          testGuild.ID,
		GuildOwner:       testGuild.UserID,
		GuildName:        testGuild.Name,
		LiveID:           testLiveID2,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    now.AddDate(0, -10, 0).Unix(),
		ContractEnd:      now.AddDate(0, 10, 0).Unix(),
		Rate:             0,
		KPI:              "",
		Type:             livecontract.FromGuild,
		Status:           livecontract.StatusContracting,
		CreateTime:       now.AddDate(0, -1, 0).Unix(),
		ModifiedTime:     now.AddDate(0, -1, 0).Unix(),
	}}
	// 测试清退申请
	expelApplymentList := []contractapplyment.ContractApplyment{{
		LiveID:             testLiveID1,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         0,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypePlatformExpel,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -1).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               0,
		CreateTime:         now.AddDate(0, 0, -4).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -4).Unix(),
	}, {
		LiveID:             testLiveID2,
		GuildID:            testGuild.ID,
		GuildName:          testGuild.Name,
		ContractID:         0,
		ContractDuration:   0,
		ContractExpireTime: 0,
		Type:               contractapplyment.TypePlatformExpel,
		Status:             contractapplyment.StatusPending,
		ExpireTime:         now.AddDate(0, 0, -1).Unix(),
		Initiator:          contractapplyment.InitiatorGuild,
		Rate:               0,
		CreateTime:         now.AddDate(0, 0, -4).Unix(),
		ModifiedTime:       now.AddDate(0, 0, -4).Unix(),
	}}
	// 未处理的降薪申请
	application := contractapplyment.ContractApplyment{
		LiveID:    testLiveID2,
		GuildID:   testGuild.ID,
		GuildName: testGuild.Name,
		Type:      contractapplyment.TypeRateDown,
		Status:    contractapplyment.StatusPending,
		Initiator: contractapplyment.InitiatorLive,
		Rate:      40,
	}
	// 插入新数据
	err = service.DB.Assign(testGuild).FirstOrCreate(&testGuild).Error
	if err != nil {
		return err
	}
	err = servicedb.Tx(service.DB, func(tx *gorm.DB) error {
		if err = helper.BatchInsert(tx, livecontract.TableName(), contractList); err != nil {
			return err
		}
		if err = helper.BatchInsert(tx, contractapplyment.TableName(), expelApplymentList); err != nil {
			return err
		}
		if err = tx.Create(&application).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

func TestActionCronExpelLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx := handler.NewRPCTestContext("/rpc/guild/terminate/exec-silent", nil)
	resp, err := ActionExpelLiveSilent(ctx)
	require.NoError(err)
	assert.Equal("没有清退合约申请可生效，不需要清退主播", resp)

	require.NoError(createExpelTestData())

	ctx = handler.NewRPCTestContext("/rpc/guild/terminate/exec-silent", nil)
	resp, err = ActionExpelLiveSilent(ctx)
	time.Sleep(time.Second)
	require.NoError(err)
	assert.Equal("success", resp)
	var expelApplymentCount int
	err = service.DB.Table(contractapplyment.TableName()).
		Where("type = ? AND status = ?", contractapplyment.TypePlatformExpel, contractapplyment.StatusAgreed).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).Count(&expelApplymentCount).Error
	require.NoError(err)
	assert.Equal(2, expelApplymentCount)

	application := new(contractapplyment.ContractApplyment)
	require.NoError(application.DB().Where("guild_id = ? AND live_id = ? AND type = ?",
		expelGuildID, testLiveID2, contractapplyment.TypeRateDown).First(application).Error)
	assert.Equal(contractapplyment.StatusInvalid, application.Status)

	var uselessContractCount int
	err = service.DB.Table(livecontract.TableName()).
		Where("status = ?", livecontract.StatusUseless).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).Count(&uselessContractCount).Error
	require.NoError(err)
	assert.Equal(2, uselessContractCount)
}

func TestExpelOneLive(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	require.NoError(createExpelTestData())

	ca := contractapplyment.ContractApplyment{
		LiveID:  testLiveID2,
		GuildID: expelGuildID,
	}
	err := expelOneLive(&ca, goutil.TimeNow())
	require.NoError(err)

	var expelApplymentCount int
	err = service.DB.Table(contractapplyment.TableName()).
		Where("type = ? AND status = ?", contractapplyment.TypePlatformExpel, contractapplyment.StatusAgreed).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).Count(&expelApplymentCount).Error
	require.NoError(err)
	assert.Equal(1, expelApplymentCount)

	application := new(contractapplyment.ContractApplyment)
	require.NoError(application.DB().Where("guild_id = ? AND live_id = ? AND type = ?",
		expelGuildID, testLiveID2, contractapplyment.TypeRateDown).First(application).Error)
	assert.Equal(contractapplyment.StatusInvalid, application.Status)

	var uselessContractCount int
	err = service.DB.Table(livecontract.TableName()).
		Where("status = ?", livecontract.StatusUseless).
		Where("live_id IN (?)", []int64{testLiveID1, testLiveID2}).Count(&uselessContractCount).Error
	require.NoError(err)
	assert.Equal(1, uselessContractCount)
}
