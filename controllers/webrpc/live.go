package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/livedb/livemulticonnect"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/mysql/liveaddendum"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/adminlogger"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type actionLiveSetInput struct {
	LiveID    int64  `json:"live_id"`
	RoomID    int64  `json:"room_id"`
	CatalogID int64  `json:"catalog_id"`
	Title     string `json:"title"`
	Intro     string `json:"intro"`
	Status    int    `json:"status"`
}

type actionLiveDelInput struct {
	LiveID int64 `json:"live_id"`
}

// ActionLiveSet handler
/**
 * @api {post} /rpc/live/set 增加或更新直播间信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} live_id 主播的用户 ID
 * @apiParam {Number} [room_id] 直播间 ID, 去掉云信后不传
 * @apiParam {Number} catalog_id 直播间 catalog_id
 * @apiParam {String} title 直播间名称
 * @apiParam {String} intro 直播间公告（简介）
 * @apiParam {Number} status 直播间状态值
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "添加成功",
 *         "room_id": 123,
 *         "guild_id": 123
 *       }
 *     }
 */
func ActionLiveSet(c *handler.Context) (handler.ActionResponse, error) {
	var input actionLiveSetInput
	err := c.BindJSON(&input)
	if err != nil {
		return nil, handler.ErrBadRequest
	}

	if !(input.LiveID >= 0 && input.RoomID >= 0 && input.Title != "") {
		return nil, handler.ErrBadRequest
	}
	u, err := mowangskuser.FindByUserID(input.LiveID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	p := &live.SaveParams{
		CreatorID: input.LiveID,
		CatalogID: input.CatalogID,
		Title:     input.Title,
		Intro:     input.Intro,
		Status:    input.Status,
		RoomID:    input.RoomID,
	}
	if u != nil {
		p.Username = u.Username
	}
	l, err := live.Save(p)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	contractEffective := []int64{livecontract.StatusContracting, livecontract.StatusTerminating}
	var guildID int64
	err = service.DB.Table(livecontract.TableName()).Select("guild_id").Limit(1).
		Where("live_id = ? AND status IN (?)", input.LiveID, contractEffective).Row().Scan(&guildID)
	if err != nil && !servicedb.IsErrNoRows(err) {
		return nil, err
	}

	return handler.M{
		"msg":      "添加成功",
		"room_id":  l.RoomID,
		"guild_id": guildID,
	}, nil
}

// ActionLiveDel handler
/**
 * @api {post} /rpc/live/del 删除直播间信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} live_id 主播的用户 ID
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "msg": "删除成功"
 *       }
 *     }
 */
func ActionLiveDel(c *handler.Context) (handler.ActionResponse, error) {
	var input actionLiveDelInput
	err := c.BindJSON(&input)
	if err != nil {
		return nil, handler.ErrBadRequest
	}

	ok, err := live.DeleteByCreatorID(input.LiveID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrNotFound("该主播不存在")
	}
	return handler.M{
		"msg": "删除成功",
	}, nil
}

// TODO: 移除记录惩罚日志 rpc

// ActionLiveLogPunish 记录惩罚日志
/**
 * @api {post} /rpc/live/logpunish 记录惩罚日志
 * @apiDescription 记录惩罚日志，支持封禁和切断直播
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} creator_id 主播的用户 ID
 * @apiParam {number=-2,-3} operator 惩罚操作类型，-2：切断直播，-3：封禁
 * @apiParam {String} reason 惩罚原因
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionLiveLogPunish(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID    int64  `json:"user_id"`
		CreatorID int64  `json:"creator_id"`
		Operator  int    `json:"operator"`
		Reason    string `json:"reason"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.CreatorID == 0 || param.Reason == "" || param.UserID <= 0 ||
		(param.Operator != liveaddendum.OperatorCut && param.Operator != liveaddendum.OperatorBan) {
		return nil, actionerrors.ErrParams
	}
	ok, err := live.EnsureLiveAddendum(param.CreatorID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrCannotFindUser
	}
	la, err := liveaddendum.Punish(param.CreatorID, param.Operator, 0, param.Reason)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if la == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	// 这里因为 rpc 请求以参数中的 user_id 为准，所以没使用 box 发送管理员日志
	log := adminlogger.AdminLog{
		UserID:     param.UserID,
		URL:        c.C.Request.URL.Path,
		IP:         c.ClientIP(),
		CreateTime: goutil.TimeNow().Unix(),
	}
	log.Intro, log.Catalog = liveaddendum.AdminLogInfo(param.Operator, param.CreatorID)
	if err = userapi.SendAdminLogs(c, log); err != nil {
		logger.Error(err)
		// PASS
	}
	return "success", nil
}

// ActionChatroomClose 关闭直播间
/**
 * @api {post} /rpc/chatroom/close 关闭直播间 RPC
 * @apiDescription 关闭的操作来源总是 system
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间号
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 */
func ActionChatroomClose(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID int64 `json:"room_id"`
	}
	err := c.Bind(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}
	r, err := room.Find(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	cs := room.NewCloseStatistics(r, room.OperatorSystem, 0, "")
	recommend := chatroom.CloseNotifyRecommend(r, c.ClientIP())
	// 部分听众可能收不到主播关播消息（比如网络问题），管理员可以再次发送
	room.NewCloseNotifier(cs, recommend, c).Notify()
	// 清空提问列表和连麦列表，以防未处理的提问钻石未退回
	r.ClearList(c)
	if r.Status.Open == room.StatusOpenFalse {
		return "success", nil
	}
	err = r.Close(cs, c.UserContext())
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	goutil.Go(func() {
		err = livepk.AfterCloseRoom(r.RoomID)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
		err = livemulticonnect.AfterCloseRoom(r)
		if err != nil {
			logger.WithField("room_id", r.RoomID).Error(err)
			// PASS
		}
	})
	return "success", nil
}

type syncScoreElem struct {
	RoomID int64   `json:"room_id"`
	Score  float64 `json:"score"`
}

// ActionLiveSyncScore 同步 live 表热度
/**
 * @api {post} /rpc/live/sync-score 同步 live 表热度
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Object[]} data 请求房间
 * @apiParam {Number} data.room_id 房间号
 * @apiParam {Number} data.score 热度
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 */
func ActionLiveSyncScore(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		Data []syncScoreElem `json:"data"`
	}
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	now := goutil.TimeNow().Unix()
	for i := range param.Data {
		err := service.DB.Table(live.TableName()).
			Where("room_id = ?", param.Data[i].RoomID).
			Where("status = ?", live.StatusOpen). // 只对开播中的房间更新热度
			Updates(
				map[string]interface{}{
					"modified_time": now,
					"score":         int64(param.Data[i].Score),
				}).Error
		if err != nil {
			logger.Error(err)
			continue
		}
	}
	return "success", nil
}
