package webrpc

import (
	"regexp"
	"strings"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/models/user"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/util"
)

// notify event 类型
const (
	notifyTypeUpdated = "updated"
)

type notifyBody struct {
	UserID int64     `json:"user_id" binding:"required"`
	Event  string    `json:"event" binding:"required"`
	User   *userInfo `json:"user"`
}

type userInfo struct {
	Username *string `json:"username"`
	IconURL  *string `json:"iconurl"`
	Confirm  *uint   `json:"confirm"`
}

type notifyRespInfo struct {
	OK int `json:"ok"`
}

// 这里保存 avatar 的路径
// TODO: 之后看看移除 avatar 字段
var avatarRegexp = regexp.MustCompile("^https?://(.*?)/avatars/(.+)$")

// ActionNotify 接收 rpc 消息后更新用户信息
/**
 * @api {post} /rpc/user/notify 接收 rpc 消息后更新用户信息
 * @apiDescription 更新 mongodb users rooms
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 id
 * @apiParam {String} event 消息类型
 * @apiParam {Object} user 用户信息
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample Success-Response:
 * {
 *   "code": 0,
 *   "info": {
 *     "ok": 1, // 更新成功返回 1, 更新失败返回 0
 *   }
 * }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (400) {Number} code 500020004
 * @apiError (400) {String} info 无法找到该用户
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 */
func ActionNotify(c *handler.Context) (handler.ActionResponse, error) {
	var message notifyBody
	if err := c.BindJSON(&message); err != nil {
		return nil, actionerrors.ErrParams
	}
	resp := &notifyRespInfo{OK: 0}
	if message.Event != notifyTypeUpdated {
		return resp, nil
	}
	messageUser := message.User
	if messageUser == nil {
		return nil, actionerrors.ErrParams
	}
	u, err := liveuser.Find(message.UserID)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if u == nil {
		return resp, nil
	}

	// get user
	user, err := findMowangskuser(message.UserID)
	if err != nil {
		return nil, err
	}
	update := bson.M{}
	// 优先使用消息中的数据避免读写延迟导致读取的数据不准确
	if messageUser.Username != nil {
		update["username"] = *messageUser.Username
	} else {
		update["username"] = user.UserName
	}
	if u.Username != update["username"] {
		filter := bson.M{
			"creator_id": user.ID,
		}
		updateRoom := bson.M{
			"creator_username": update["username"],
			"updated_time":     util.TimeNow(),
		}
		_, err := room.UpdateOneRoom(filter, updateRoom)
		if err != nil {
			logger.Error(err)
			// PASS
		}
	}
	var str string
	if messageUser.IconURL != nil {
		str = *messageUser.IconURL
	} else {
		str = user.BoardIconURL2
	}
	update["iconurl"] = str
	update["avatar"] = avatar(str)
	if messageUser.Confirm != nil {
		update["confirm"] = *messageUser.Confirm
	} else {
		update["confirm"] = user.Confirm
	}
	update["introduction"] = user.UserIntro
	ok, err := liveuser.Update(user.ID, update)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if !ok {
		return resp, nil
	}
	// 清除用户信息缓存
	err = service.Redis.Del(keys.KeyUsersUserID1.Format(message.UserID)).Err()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	resp.OK = 1
	return resp, nil
}

func findMowangskuser(userID int64) (*user.MowangskUser, error) {
	var user user.MowangskUser
	err := service.DB.Table(mowangskuser.TableName()).First(&user, userID).Error
	if err != nil {
		if servicedb.IsErrNoRows(err) {
			return nil, actionerrors.ErrCannotFindUser
		}
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &user, nil
}

func avatar(str string) string {
	if str == "" {
		return ""
	}
	allString := avatarRegexp.FindStringSubmatch(str)
	if len(allString) == 3 {
		return allString[2]
	}
	if strings.HasPrefix(str, "http://") {
		// normalize url, http: -> https:
		str = "https://" + str[7:]
	}
	return str
}
