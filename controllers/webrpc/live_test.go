package webrpc

import (
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const TestRoomID = 123

func subtestActionLiveSet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	contract := livecontract.LiveContract{
		GuildID:       3,
		LiveID:        12,
		ContractStart: 1565002308,
		ContractEnd:   1999999999,
		Status:        livecontract.StatusContracting,
	}
	err := service.DB.Where("guild_id = ? AND live_id = ? AND status = ?",
		contract.GuildID, contract.LiveID, contract.Status).FirstOrCreate(&contract).Error
	require.NoError(err)

	// 测试参数错误
	input := actionLiveSetInput{
		LiveID: 12,
		RoomID: TestRoomID,
	}

	c := handler.NewRPCTestContext("/", input)
	_, err = ActionLiveSet(c)
	assert.Equal(handler.ErrBadRequest, err)

	// 测试一般情况
	input.Title = "test title"
	input.Status = live.StatusClose

	c = handler.NewRPCTestContext("/", input)
	r, err := ActionLiveSet(c)
	require.NoError(err)
	result := r.(handler.M)
	assert.Equal(int64(3), result["guild_id"])
}

func subtestActionLiveDel(t *testing.T) {
	assert := assert.New(t)

	c := handler.NewRPCTestContext("/", strings.NewReader(`{"live_id":12}`))
	_, err := ActionLiveDel(c)
	assert.NoError(err)

	c = handler.NewRPCTestContext("/", strings.NewReader(`{"live_id":12}`))
	_, err = ActionLiveDel(c)
	assert.EqualError(err, "该主播不存在")
}

func TestAddOrDelLives(t *testing.T) {
	subtestActionLiveSet(t)
	subtestActionLiveDel(t)
}

func TestActionLiveLogPunish(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := map[string]interface{}{
		"creator_id": 999999999,
		"operator":   -2,
		"user_id":    12,
	}
	newC := func() *handler.Context {
		return handler.NewTestContext(http.MethodPost, "/live/logpunish", false, param)
	}
	_, err := ActionLiveLogPunish(newC())
	assert.Equal(actionerrors.ErrParams, err)
	param["reason"] = "ActionLivelogPunish"
	_, err = ActionLiveLogPunish(newC())
	assert.Equal(actionerrors.ErrCannotFindUser, err)
	l, err := live.FindLiveByRoomID(22489473)
	require.NoError(err)
	require.NotNil(l)
	param["creator_id"] = l.ID
	_, err = ActionLiveLogPunish(newC())
	assert.NoError(err)
}

func TestActionChatroomClose(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	newC := func(param map[string]interface{}) *handler.Context {
		return handler.NewRPCTestContext("/close", param)
	}
	_, err := ActionChatroomClose(newC(nil))
	assert.Equal(actionerrors.ErrParams, err)
	_, err = ActionChatroomClose(newC(map[string]interface{}{"room_id": 99999999}))
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	roomID := int64(18113499)
	r, err := room.Update(roomID, bson.M{"status.open": room.StatusOpenTrue, "status.open_log_id": primitive.NewObjectID()})
	require.NoError(err)
	resp, err := ActionChatroomClose(newC(map[string]interface{}{"room_id": r.RoomID}))
	require.NoError(err)
	assert.Equal("success", resp)
	resp, err = ActionChatroomClose(newC(map[string]interface{}{"room_id": r.RoomID}))
	require.NoError(err)
	assert.Equal("success", resp)
}

func TestActionLiveSyncScore(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var l []live.Live
	require.NoError(service.DB.Limit(2).Find(&l).Error)
	require.Len(l, 2)
	require.NoError(service.DB.Table(l[0].TableName()).Where("id = ?", l[0].ID).
		Updates(map[string]interface{}{
			"score":  0,
			"status": live.StatusClose,
		}).Error)
	require.NoError(service.DB.Table(l[0].TableName()).Where("id = ?", l[1].ID).
		Updates(map[string]interface{}{
			"score":  0,
			"status": live.StatusOpen,
		}).Error)
	param := map[string]interface{}{
		"data": []syncScoreElem{
			{RoomID: l[0].RoomID, Score: 987.9},
			{RoomID: l[1].RoomID, Score: 987.9},
			{RoomID: 89756, Score: 0.0}, // 测试查不到不会报错
		},
	}
	c := handler.NewRPCTestContext("/sync-score", param)
	r, err := ActionLiveSyncScore(c)
	require.NoError(err)
	assert.Equal("success", r)
	require.NoError(service.DB.Where("id IN (?)", []int64{l[0].ID, l[1].ID}).Find(&l).Error)
	require.Len(l, 2)
	require.NotNil(l[0].Score)
	assert.Zero(*l[0].Score)
	require.NotNil(l[1].Score)
	assert.EqualValues(987, *l[1].Score)
}
