package webrpc

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionLiveCheckPM(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := liveCheckPM{
		FromUserID: 3456835,
		ToUserID:   9074509,
	}
	c := handler.NewRPCTestContext("/live/check-pm", body)
	resp, err := ActionLiveCheckPM(c)
	require.NoError(err)
	r := resp.(*liveCheckPMResp)
	assert.Equal(sendStatusAllow, r.Status)
	assert.False(r.<PERSON>uck<PERSON>ag)

	record := luckybag.InitiateRecord{
		Type:      luckybag.TypeEntity,
		CreatorID: body.ToUserID,
	}
	require.NoError(record.Create())

	prize := luckybag.UserPrize{
		UserID:     body.FromUserID,
		LuckyBagID: record.ID,
	}
	require.NoError(prize.Create())
	c = handler.NewRPCTestContext("/live/check-pm", body)
	resp, err = ActionLiveCheckPM(c)
	require.NoError(err)
	r = resp.(*liveCheckPMResp)
	assert.Equal(sendStatusAllow, r.Status)
	assert.True(r.HasLuckyBag)
}

func TestNewLiveCheckPM(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	body := liveCheckPM{
		FromUserID: -1,
		ToUserID:   9074509,
	}
	c := handler.NewRPCTestContext("/live/check-pm", body)
	_, err := newLiveCheckPM(c)
	assert.Equal(actionerrors.ErrParams, err)

	body.FromUserID = 9074509
	c = handler.NewRPCTestContext("/live/check-pm", body)
	param, err := newLiveCheckPM(c)
	require.NoError(err)
	assert.NotNil(param)
}

func TestLiveCheckPM_isLuckyBagExists(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	record := luckybag.InitiateRecord{
		Type:      luckybag.TypeEntity,
		CreatorID: 9074509,
	}
	require.NoError(record.Create())

	prize := luckybag.UserPrize{
		UserID:     3456835,
		LuckyBagID: record.ID,
	}
	require.NoError(prize.Create())

	// 测试用户给主播发私信
	param := liveCheckPM{
		FromUserID: prize.UserID,
		ToUserID:   record.CreatorID,
	}
	ok, err := param.isLuckyBagExists()
	require.NoError(err)
	assert.True(ok)

	// 测试主播给用户发私信
	param = liveCheckPM{
		FromUserID: record.CreatorID,
		ToUserID:   prize.UserID,
	}
	ok, err = param.isLuckyBagExists()
	require.NoError(err)
	assert.True(ok)

	// 测试超过 60 天
	now := goutil.TimeNow()
	goutil.SetTimeNow(func() time.Time {
		return now.AddDate(0, 0, 62)
	})
	defer goutil.SetTimeNow(nil)
	ok, err = param.isLuckyBagExists()
	require.NoError(err)
	assert.False(ok)
}

func TestLiveCheckPM_creatorContractLimit(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := liveCheckPM{
		ToUserID: 5003,
	}
	ok, err := param.checkCreatorContract()
	require.NoError(err)
	assert.False(ok)

	param.ToUserID = 3456835
	ok, err = param.checkCreatorContract()
	require.NoError(err)
	assert.True(ok)
}

func TestLiveCheckPM_checkUserLevel(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := liveCheckPM{
		FromUserID: 90745,
	}
	// 测试不是直播用户
	ok, err := param.checkUserLevel()
	require.NoError(err)
	assert.False(ok)

	// 测试用户等级 < 17 级，没粉丝牌
	param.FromUserID = 3387502
	ok, err = param.checkUserLevel()
	require.NoError(err)
	assert.False(ok)

	// 测试用户等级 >= 17 级，没粉丝牌
	param.FromUserID = 3456835
	ok, err = param.checkUserLevel()
	require.NoError(err)
	assert.True(ok)

	// 测试用户等级 < 17 级，有粉丝牌
	param.ToUserID = 3457111
	param.FromUserID = 12
	ok, err = param.checkUserLevel()
	require.NoError(err)
	assert.True(ok)
}
