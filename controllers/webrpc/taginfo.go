package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/livetaggroup"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type tagInfoParam struct {
	CatalogID   int64 `json:"catalog_id,omitempty"`
	CustomTagID int64 `json:"custom_tag_id,omitempty"`

	resp tagInfoResp
}

type tagInfoResp struct {
	Catalog   *liveCatalog `json:"catalog,omitempty"`
	CustomTag *customTag   `json:"custom_tag,omitempty"`
}

type liveCatalog struct {
	CatalogID    int64  `json:"catalog_id"`
	CatalogName  string `json:"catalog_name"`
	CatalogColor string `json:"catalog_color"`
}

type customTag struct {
	TagGroupID   int64  `json:"tag_group_id"`
	TagGroupName string `json:"tag_group_name"`
	TagID        int64  `json:"tag_id"`
	TagName      string `json:"tag_name"`
}

/**
 * @api {post} /rpc/room/taginfo 获取直播间标签信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} [catalog_id] 分区 ID
 * @apiParam {Number} [custom_tag_id] 个性词条 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "catalog": { // 传分区 ID 返回，分区 ID 不存在不返回
 *         "catalog_id": 107,
 *         "catalog_name": "分区名",
 *         "catalog_color": "#ffffff"
 *       },
 *       "custom_tag": { // 传个性词条 ID 返回，个性词条 ID 不存在不返回
 *         "tag_group_id": 1,
 *         "tag_group_name": "人设（女性向）",
 *         "tag_id": 10001,
 *         "tag_name": "腹黑青叔"
 *       }
 *     }
 *   }
 *
 */
func ActionRoomTagInfo(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newTagInfoParam(c)
	if err != nil {
		return nil, err
	}

	err = param.liveCatalogResp()
	if err != nil {
		return nil, err
	}

	err = param.customTagResp()
	if err != nil {
		return nil, err
	}

	return param.resp, nil
}

func newTagInfoParam(c *handler.Context) (*tagInfoParam, error) {
	var param tagInfoParam
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.CatalogID <= 0 && param.CustomTagID <= 0 {
		return nil, actionerrors.ErrParams
	}

	return &param, nil
}

func (param *tagInfoParam) liveCatalogResp() error {
	if param.CatalogID <= 0 {
		return nil
	}

	subCatalogsMap, err := catalog.AllLiveCatalogsWithSubMap(false)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	catalog, ok := subCatalogsMap[param.CatalogID]
	if !ok {
		return nil
	}

	param.resp.Catalog = &liveCatalog{
		CatalogID:    catalog.ID,
		CatalogName:  catalog.CatalogName,
		CatalogColor: catalog.Color,
	}

	return nil
}

func (param *tagInfoParam) customTagResp() error {
	if param.CustomTagID <= 0 {
		return nil
	}

	tagsMap, err := tag.AllShowCustomTagsMap()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	tag, ok := tagsMap[param.CustomTagID]
	if !ok {
		return nil
	}

	group, err := livetaggroup.FindShowLiveTagGroupByID(tag.TagGroupID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if group == nil {
		return nil
	}

	param.resp.CustomTag = &customTag{
		TagGroupID:   group.ID,
		TagGroupName: group.Name,
		TagID:        tag.ID,
		TagName:      tag.TagName,
	}

	return nil
}
