package webrpc

import (
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/liverpc"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRoomInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", false, handler.M{"room_id": 0})
	_, err := ActionRoomInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	var (
		testUserID       int64 = 12
		testBannerUserID int64 = 20201104
		testRoomID       int64 = 4381915
	)
	testRoom, err := room.Find(testRoomID, &room.FindOptions{DisableAll: true})
	require.NoError(err)
	require.NotNil(testRoom)

	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"room_id": testRoomID, "user_id": testBannerUserID})
	_, err = ActionRoomInfo(c)
	assert.Equal(actionerrors.ErrBlockUser, err)

	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{
		"room_id": testRoomID, "user_id": testBannerUserID, "ignore_ban": 1,
	})
	r, err := ActionRoomInfo(c)
	require.NoError(err)
	resp := r.(*liverpc.RoomInfoResp)
	require.NotNil(resp.Room)

	cleanup := mrpc.SetMock(userapi.URIGoUserBlocklist,
		func(input interface{}) (output interface{}, err error) {
			return handler.M{"block_list": []int64{}}, nil
		})
	defer cleanup()
	c = handler.NewTestContext(http.MethodPost, "", false, handler.M{"room_id": testRoomID, "user_id": testUserID})
	r, err = ActionRoomInfo(c)
	require.NoError(err)
	resp = r.(*liverpc.RoomInfoResp)
	require.NotNil(resp.Room)
	assert.Equal(testRoomID, resp.Room.RoomID)
	assert.EqualValues(testRoom.GuildID, resp.Room.GuildID)
}

func TestActionRoomOpen(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		count      int
		testRoomID = int64(223344)
	)
	cancel := mrpc.SetMock("im://broadcast/user", func(any) (any, error) {
		count++
		return "success", nil
	})
	defer cancel()
	cancel = mrpc.SetMock(userapi.URLInteractionRoomOnOpen,
		func(any) (any, error) {
			count++
			return "success", nil
		})
	defer cancel()

	err := room.AddTag([]int64{testRoomID}, tag.TagListenDrama)
	require.NoError(err)
	byteParams, err := json.Marshal(params.Pia{
		Key:      params.KeyPia,
		Name:     "checkPiaBroadcast",
		ShowTime: goutil.TimeNow().Unix() - 1,
	})
	require.NoError(err)
	require.NotNil(byteParams)
	err = service.LRURedis.Set(keys.KeyParams1.Format(params.KeyPia), byteParams, time.Minute).Err()
	require.NoError(err)

	c := handler.NewRPCTestContext("/room/open", handler.M{
		"room_id": testRoomID,
	})
	resp, err := ActionRoomOpen(c)
	require.NoError(err)
	info, ok := resp.(handler.M)
	require.True(ok)
	assert.Equal("success", info["msg"])
	assert.Equal(2, count)
}

func TestNewRoomOpenParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/room/open", handler.M{
		"room_id": 0,
	})
	_, err := newRoomOpenParam(c)
	assert.EqualError(err, "参数错误")

	c = handler.NewRPCTestContext("/room/open", handler.M{
		"room_id": 223344,
	})
	param, err := newRoomOpenParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.NotNil(param.r)
}

func TestRoomOpenParam_checkPiaBroadcast(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		count      int
		testRoomID = int64(223344)
	)
	cancel := mrpc.SetMock("im://broadcast/user", func(input interface{}) (output interface{}, err error) {
		var body struct {
			RoomID int64 `json:"room_id,omitempty"`
			UserID int64 `json:"user_id,omitempty"`
		}
		err = json.Unmarshal(input.(json.RawMessage), &body)
		require.NoError(err)
		assert.Equal(testRoomID, body.RoomID)
		count++
		return "success", nil
	})
	defer cancel()
	goutil.SetTimeNow(func() time.Time {
		return time.Unix(2, 0)
	})
	defer goutil.SetTimeNow(nil)

	byteParams, err := json.Marshal(params.Pia{
		Key:      params.KeyPia,
		Name:     "checkPiaBroadcast",
		ShowTime: goutil.TimeNow().Unix() - 1,
	})
	require.NoError(err)
	require.NotNil(byteParams)
	err = service.LRURedis.Set(keys.KeyParams1.Format(params.KeyPia), byteParams, time.Minute).Err()
	require.NoError(err)

	param := &roomOpenParam{r: &room.Room{
		Helper: room.Helper{
			RoomID:    testRoomID,
			CreatorID: 20231025,
			TagIDs:    []int64{tag.TagListenDrama},
		},
	}}
	param.checkPiaBroadcast()
	assert.Equal(1, count)
}

func TestRoomOpenParam_sendRoomOnOpen(t *testing.T) {
	assert := assert.New(t)

	isCalled := false
	cancel := mrpc.SetMock(userapi.URLInteractionRoomOnOpen,
		func(any) (any, error) {
			isCalled = true
			return "success", nil
		})
	defer cancel()

	param := &roomOpenParam{
		RoomID: 9074509,

		c: handler.NewRPCTestContext("/rpc/room/open", nil),
	}
	param.sendRoomOnOpenEvent()
	assert.False(isCalled)

	param.OpenLogID = "620645ac16c52d447566bfa9"
	param.sendRoomOnOpenEvent()
	assert.True(isCalled)
}
