package webrpc

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const existsRoomID int64 = 18113499

func TestActionRecommendedBackground(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", false, nil)
	_, err := ActionRecommendedBackground(c)
	assert.Equal(actionerrors.ErrParams, err)

	param := handler.M{
		"room_id": existsRoomID,
	}
	service.Cache5Min.Flush()
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	r, err := ActionRecommendedBackground(c)
	require.NoError(err)
	assert.Nil(r)

	param = handler.M{
		"room_id": existsRoomID,
	}
	service.Cache5Min.Set(keys.KeyRecommend0.Format(),
		liverecommendedelements.RecommendCache{
			existsRoomID: {
				liverecommendedelements.ElementBackground: {Attribute: liverecommendedelements.Attribute{URL: "http://background.webp;0.55"}},
			},
		}, 0)
	c = handler.NewTestContext(http.MethodPost, "", false, param)
	r, err = ActionRecommendedBackground(c)
	require.NoError(err)
	assert.NotNil(r)
}
