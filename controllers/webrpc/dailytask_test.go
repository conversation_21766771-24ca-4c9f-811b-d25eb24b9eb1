package webrpc

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/livelistenlogs"
	"github.com/MiaoSiLa/live-service/models/mongodb/params"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestDailyTaskParam_findFollowedRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := dailyTaskParam{
		UserID: -9999,
	}
	roomID, err := p.findFollowedRoom()
	require.NoError(err)
	assert.Zero(roomID)

	rooms, err := room.AggregateSimples([]bson.M{{
		"$match": bson.M{"status.open": room.StatusOpenTrue},
	}, {
		"$limit": 2,
	}})
	require.NoError(err)
	require.Len(rooms, 2)
	b, _ := json.Marshal([]int64{rooms[0].CreatorID})
	key := keys.KeyUserFollowedCreator1.Format(testUserID)
	require.NoError(service.LRURedis.Set(key, b, time.Minute).Err())
	p = dailyTaskParam{
		UserID: testUserID,
	}
	roomID, err = p.findFollowedRoom()
	require.NoError(err)
	assert.Equal(roomID, rooms[0].RoomID)

	testTime := int64(2123356789)
	cancel := goutil.SetTimeNow(func() time.Time {
		return time.Unix(testTime, 0)
	})
	defer cancel()
	testAllowList := liverecommendedelements.Model{
		Sort:        1,
		ElementID:   rooms[1].CreatorID,
		ElementType: liverecommendedelements.ElementDailyTaskAllowList,
		Attribute: liverecommendedelements.Attribute{
			StartTime: &testTime,
		},
		ExpireTime: testTime + 10,
	}
	require.NoError(service.DB.Create(&testAllowList).Error)
	roomID, err = p.findFollowedRoom()
	require.NoError(err)
	assert.Contains([]int64{rooms[0].RoomID, rooms[1].RoomID}, roomID)
}

func TestDailyTaskParam_findLastHourRankRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rooms, err := room.AggregateSimples([]bson.M{{
		"$match": bson.M{"status.open": room.StatusOpenTrue},
	}, {
		"$limit": 1,
	}})
	require.NoError(err)
	require.NotEmpty(rooms)

	key := usersrank.LastRankKey(usersrank.TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())

	p := dailyTaskParam{
		resp: &dailyTaskResp{},
	}
	roomID, err := p.findLastHourRankRoom()
	require.NoError(err)
	assert.Zero(roomID)

	pipe := service.Redis.Pipeline()
	pipe.ZAdd(key, &redis.Z{Score: 1, Member: rooms[0].CreatorID})
	pipe.ZAdd(key, &redis.Z{Score: 2, Member: -99999})
	pipe.Expire(key, time.Minute)
	_, err = pipe.Exec()
	service.Cache5Min.Flush()
	require.NoError(err)
	roomID, err = p.findLastHourRankRoom()
	require.NoError(err)
	assert.Equal(rooms[0].RoomID, roomID)
}

func TestDailyTaskParam_findHourRankRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rooms, err := room.AggregateSimples([]bson.M{{
		"$match": bson.M{"status.open": room.StatusOpenTrue},
	}, {
		"$limit": 1,
	}})
	require.NoError(err)
	require.NotEmpty(rooms)

	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())

	p := dailyTaskParam{
		resp: &dailyTaskResp{},
	}
	roomID, err := p.findHourRankRoom()
	require.NoError(err)
	assert.Zero(roomID)

	pipe := service.Redis.Pipeline()
	pipe.ZAdd(key, &redis.Z{Score: 1, Member: rooms[0].CreatorID})
	pipe.ZAdd(key, &redis.Z{Score: 2, Member: -99999})
	pipe.Expire(key, time.Minute)
	_, err = pipe.Exec()
	require.NoError(err)
	roomID, err = p.findHourRankRoom()
	require.NoError(err)
	assert.Equal(rooms[0].RoomID, roomID)
}

func TestDailyTaskParam_findHotScoreRoom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := dailyTaskParam{
		resp: &dailyTaskResp{},
	}
	roomID, err := p.findHotScoreRoom()
	require.NoError(err)
	assert.NotZero(roomID)
}

func TestDailyTaskParam_buildIsNew(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testNewUserID = int64(999999)
		now           = goutil.TimeNow()
	)

	beginTime := goutil.BeginningOfDay(now)
	key := keys.KeyUserDailyTaskIsNewStatus2.Format(testNewUserID, beginTime.Format(util.TimeFormatYMDWithNoSpace))
	require.NoError(service.LRURedis.Del(key).Err())

	p := dailyTaskParam{
		UserID: testNewUserID,
		resp:   &dailyTaskResp{},
	}
	require.NoError(p.buildIsNew())
	assert.True(p.resp.IsNew)

	require.NoError(service.LRURedis.Set(key, 0, time.Minute).Err())
	require.NoError(p.buildIsNew())
	assert.False(p.resp.IsNew)
}

func TestDailyTaskParam_buildTodayListenDuration(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livelistenlogs.Collection().DeleteMany(ctx, bson.M{"user_id": testUserID})
	require.NoError(err)

	param := dailyTaskParam{
		UserID: testUserID,
		resp: &dailyTaskResp{
			IsNew: true,
		},
	}
	err = param.buildTodayListenDuration()
	require.NoError(err)
	assert.Nil(param.resp.TodayListenDuration)
	assert.False(param.resp.IsViewedLiveToday)
	assert.False(param.resp.IsFinishedListenDurationToday)

	liveTask, err := params.FindLiveTask()
	require.NoError(err)
	document := &livelistenlogs.LiveListenLog{
		UserID:    testUserID,
		Duration:  liveTask.DailyListenDuration.NewUserFinishDuration,
		StartTime: goutil.TimeUnixMilli(goutil.TimeNow().UnixMilli()),
		Equips: []*livelistenlogs.Equip{
			{BUVID: goutil.NewString("00a233d2-b7ae-11ef-951f-155b72b8d081")},
		},
	}
	_, err = livelistenlogs.Collection().InsertOne(ctx, document)
	require.NoError(err)

	err = param.buildTodayListenDuration()
	require.NoError(err)
	assert.NotNil(param.resp.TodayListenDuration)
	assert.True(param.resp.IsViewedLiveToday)
	assert.True(param.resp.IsFinishedListenDurationToday)

	param.resp.IsNew = false
	err = param.buildTodayListenDuration()
	require.NoError(err)
	assert.NotNil(param.resp.TodayListenDuration)
	assert.True(param.resp.IsViewedLiveToday)
	assert.False(param.resp.IsFinishedListenDurationToday)

	_, err = livelistenlogs.Collection().UpdateOne(ctx,
		bson.M{"user_id": param.UserID},
		bson.M{
			"$set": bson.M{
				"duration": liveTask.DailyListenDuration.OldUserFinishDuration,
			},
		},
	)
	require.NoError(err)
	err = param.buildTodayListenDuration()
	require.NoError(err)
	assert.NotNil(param.resp.TodayListenDuration)
	assert.True(param.resp.IsViewedLiveToday)
	assert.True(param.resp.IsFinishedListenDurationToday)
}

func TestDailyTaskParam_buildTodayMessageCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testUserID := int64(9074509)
	param := dailyTaskParam{
		UserID: testUserID,
		resp:   &dailyTaskResp{},
	}
	err := param.buildTodayMessageCount()
	require.NoError(err)
	assert.GreaterOrEqual(param.resp.TodayMessageCount, int64(0))
}
