package webrpc

import (
	"bytes"
	"encoding/json"
	"io"
	"strconv"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/liveshow"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveactivity/box"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/redis/chatroom"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// AddRevenueTypeGift 收益类型为 gift
	AddRevenueTypeGift = "gift"
	// AddRevenueTypeQuestion 收益类型为 question
	AddRevenueTypeQuestion = "question"
	// AddRevenueTypeNoble 收益类型为 noble
	AddRevenueTypeNoble = "noble"
)

// ActionRankRevenue handler
/**
 * @api {post} /rpc/rank/revenue 同步榜单收益
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} room_id 直播间 ID
 * @apiParam {Number} creator_id 主播用户 ID
 * @apiParam {Number} catalog_id 直播间分类 ID
 * @apiParam {string="gift","question","noble"} type 收益类型
 * @apiParam {Number} revenue 收益值（钻）
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": "success"
 *     }
 */
func ActionRankRevenue(c *handler.Context) (handler.ActionResponse, error) {
	req := c.Request()
	if req.Body == nil {
		return nil, actionerrors.ErrParams
	}
	data, err := io.ReadAll(req.Body)
	if err != nil {
		req.Body.Close()
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	req.Body.Close()
	// TODO: 复制 body, 防止 lua 复用问题
	req.Body = io.NopCloser(bytes.NewReader(data))
	var param rankScoreReq
	err = json.Unmarshal(data, &param)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	param.uc = c.UserContext()
	goutil.Go(func() {
		param.findRoom()
		param.findGift()
		param.addPK()
		param.addMedalPoint()
		param.activatedGiftWall()
		param.broadcast()
		param.addNovaRank()

		param.addActivity()
		param.addLiveShow()
		// param.syncActivityBox() // TODO: 移除宝箱相关逻辑
		param.addLiveSpend()
		param.addRoomPaidUser()
	})
	return "success", nil
}

type rankScoreReq struct {
	Type    string    `json:"type"`
	Room    rsRoom    `json:"room"` // 存在贵族不在房间里开通的情况
	User    rsUser    `json:"user"`
	Receive rsReceive `json:"receive"`

	uc             mrpc.UserContext
	syncParam      *rank.SyncGiftParam // 用于单元测试
	r              *room.Room
	g              *gift.Gift
	broadcastElems []*userapi.BroadcastElem
}

type rsRoom struct {
	RoomID            int64 `json:"room_id"`
	CatalogID         int64 `json:"catalog_id"`
	CreatorID         int64 `json:"creator_id"`
	GuildID           int64 `json:"guild_id"`
	ActivityCatalogID int64 `json:"activity_catalog_id"`
}

type rsUser struct {
	UserID int64        `json:"user_id"`
	Noble  *rsUserNoble `json:"noble"` // 贵族开通时为旧贵族信息，不包含贵族保护期
}

type rsUserNoble struct {
	Name  string `json:"name"`
	Level int64  `json:"level"`
}

type rsReceive struct {
	Revenue float64 `json:"revenue"`
	Price   float64 `json:"price"` // 原始的价格

	// 礼物
	GiftID    int64 `json:"gift_id,omitempty"`
	GiftType  int64 `json:"gift_type,omitempty"` // 同步送礼收益时会传递
	GiftNum   int   `json:"gift_num,omitempty"`
	GiftValue int64 `json:"gift_value,omitempty"`

	// 贵族
	NobleLevel int `json:"noble_level"`
}

func (param *rankScoreReq) findRoom() {
	var err error
	param.r, err = room.Find(param.Room.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *rankScoreReq) findGift() {
	if param.Type != AddRevenueTypeGift {
		return
	}

	var err error
	param.g, err = gift.FindShowingGiftByGiftID(param.Receive.GiftID)
	if err != nil {
		logger.Error(err)
		return
	}
	if param.g == nil {
		logger.Errorf("gift %d 不存在", param.Receive.GiftID)
		return
	}
}

func (param *rankScoreReq) addPK() {
	if param.Type != AddRevenueTypeGift || param.r == nil {
		return
	}
	elems, err := livepk.AddPKScore(param.r, param.User.UserID, int64(param.Receive.Revenue), 0)
	if err != nil {
		logger.Error(err)
		return
	}
	if len(elems) == 0 {
		return
	}
	param.broadcastElems = append(param.broadcastElems, elems...)
}

func (param *rankScoreReq) addMedalPoint() {
	if param.r == nil || param.r.Medal == nil {
		return
	}
	t, ok := getAddMedalPointType(param.Type)
	if !ok {
		return
	}

	uv, err := vip.UserActivatedVip(param.User.UserID, false, nil)
	if err != nil {
		logger.Error(err)
		return
	}

	medalParam := livemedalstats.AddPointParam{
		RoomOID:    param.r.OID,
		RoomID:     param.r.RoomID,
		CreatorID:  param.Room.CreatorID,
		UserID:     param.User.UserID,
		UV:         uv,
		MedalName:  param.r.Medal.Name,
		Type:       t,
		PointAdd:   int64(param.Receive.Revenue),
		IsRoomOpen: param.r.IsOpen(),
	}
	medalUpdatedInfo, err := medalParam.AddPoint()
	if err != nil {
		logger.Error(err)
		return
	}
	notifyParam := liveuser.MedalNotifyParam{
		MedalUpdatedInfo: medalUpdatedInfo,
		CreatorUsername:  param.r.CreatorUsername,
		UserID:           param.User.UserID,
		RoomID:           param.r.RoomID,
	}
	notify := notifyParam.NewUserMedalNotify()
	if notify != nil {
		param.broadcastElems = append(param.broadcastElems, notify)
	}
}

func getAddMedalPointType(t string) (int, bool) {
	switch t {
	case AddRevenueTypeGift:
		return livemedal.TypeGiftAddMedalPoint, true
	case AddRevenueTypeQuestion:
		return livemedal.TypeQuestionAddMedalPoint, true
	}
	return 0, false
}

func (param *rankScoreReq) activatedGiftWall() {
	if param.Room.RoomID == 0 || param.Type != AddRevenueTypeGift {
		return
	}

	notifyElem, err := giftwall.ActiveGift(param.r, param.User.UserID, param.Receive.GiftID,
		int64(param.Receive.Revenue), param.Receive.GiftNum)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	if notifyElem != nil {
		param.broadcastElems = append(param.broadcastElems, notifyElem)
	}
}

// broadcast 发送 im 消息
func (param rankScoreReq) broadcast() {
	if len(param.broadcastElems) <= 0 {
		return
	}
	err := userapi.BroadcastMany(param.broadcastElems)
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *rankScoreReq) addNovaRank() {
	if param.Room.CreatorID <= 0 {
		return
	}
	exists, err := usersrank.IsNova(param.Room.CreatorID)
	if err != nil {
		logger.Error(err)
		// PASS
		return
	}
	if !exists {
		return
	}
	err = usersrank.AddNovaRevenue(param.Room.CreatorID, int64(param.Receive.Revenue))
	if err != nil {
		logger.WithFields(
			logger.Fields{"user_id": param.Room.CreatorID, "revenue": param.Receive.Revenue},
		).Error(err)
		// PASS
		return
	}
}

func (param *rankScoreReq) addActivity() {
	syncParam := rank.
		NewSyncParam(param.Room.RoomID, param.User.UserID, param.Room.CreatorID).
		SetGuildID(param.Room.GuildID).
		SetActivityCatalogID(param.Room.ActivityCatalogID)
	if param.Type == AddRevenueTypeGift {
		// 查询需要确定 attr，gift_point
		if param.g == nil {
			return
		}
		param.syncParam = syncParam.
			SetGift(param.g, param.Receive.GiftNum)
		param.syncParam.AddRankPoint()
		param.syncParam.SendLiveActivity(param.uc)
		return
	}
}

func (param *rankScoreReq) addLiveShow() {
	if param.Room.RoomID == 0 {
		// 贵族不在房间里开通
		return
	}

	syncParam := liveshow.
		NewSyncLiveShow(param.Room.RoomID, param.User.UserID, param.Room.CreatorID)
	if param.Type != AddRevenueTypeGift {
		syncParam.
			SetPoint(int64(param.Receive.Revenue)).
			Sync()
		return
	}

	if param.g == nil {
		return
	}
	syncParam.
		SetGift(param.g.GiftID, param.g.Price, param.Receive.GiftNum).
		Sync()
}

func (param *rankScoreReq) syncActivityBox() {
	if param.Room.RoomID == 0 {
		return
	}
	if param.Type == AddRevenueTypeGift {
		box.SendQuestMessage(param.Room.RoomID, param.Room.CreatorID, box.QuestTypeGift, int64(param.Receive.Revenue))
	} else if param.Type == AddRevenueTypeNoble {
		// 只有练习生以下的贵族开通新秀及以上等级的贵族才算完成任务
		if (param.User.Noble == nil || param.User.Noble.Level <= 1) && param.Receive.NobleLevel > 1 {
			box.SendQuestMessage(param.Room.RoomID, param.Room.CreatorID, box.QuestTypeNoble, 0)
		}
	}
}

func (param *rankScoreReq) addLiveSpend() {
	utils.SendLiveSpend(param.User.UserID, int64(param.Receive.Revenue), param.Room.RoomID)
}

func (param *rankScoreReq) addRoomPaidUser() {
	if param.r == nil {
		return
	}
	chatroom.AddCurrentRoomPaidUser(param.r.RoomID, param.User.UserID, param.r.Status.OpenTime)
}

type hourRankRandomResp struct {
	RoomID int64 `json:"room_id"`
}

// ActionHourRankRandom 随机获取一个小时榜开播直播间
/**
 * @api {post} /rpc/live/hourrank/random 随机获取一个小时榜开播直播间
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "room_id": 463640018 // 小时榜前十没有开播主播返回 0
 *       }
 *     }
 */
func ActionHourRankRandom(*handler.Context) (handler.ActionResponse, error) {
	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow())
	rankList, err := service.Redis.ZRevRange(key, 0, usersrank.RankLen(usersrank.TypeHour)-1).Result()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	creatorIDs := make([]int64, 0, len(rankList))
	for _, v := range rankList {
		creatorID, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		creatorIDs = append(creatorIDs, creatorID)
	}

	resp := hourRankRandomResp{}
	if len(creatorIDs) == 0 {
		return resp, nil
	}
	roomID, err := room.RandomOpenRoomID(creatorIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if roomID != 0 {
		resp.RoomID = roomID
	}
	return resp, nil
}
