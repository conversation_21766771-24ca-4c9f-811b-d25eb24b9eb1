package webrpc

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

type recommendInfoParam struct {
	CreatorIDs []int64 `json:"creator_ids"`
	IsOld      bool    `json:"is_old"`
}

type recommendInfoRoom struct {
	RoomID       int64          `json:"room_id"`
	Title        string         `json:"title"`
	CatalogID    int64          `json:"catalog_id"`
	CatalogName  string         `json:"catalog_name"`
	CatalogColor string         `json:"catalog_color"`
	CustomTag    *tag.CustomTag `json:"custom_tag,omitempty"`
	UserID       int64          `json:"user_id"`
	Username     string         `json:"username"`
	IconURL      string         `json:"iconurl"`
	CoverURL     string         `json:"cover_url"`
	Status       int            `json:"status"`
}

// ActionLiveRecommendInfo 批量获取推荐直播间信息
/**
 * @api {post} /rpc/live/recommend-info 批量获取推荐直播间信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number[]} creator_ids 主播 IDs
 * @apiParam {Boolean} [is_old=false] 是否是旧版本，iOS < 6.0.8、安卓 < 6.0.9 的版本传 true
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": {
 *         "9074501": { // 主播 ID
 *           "room_id": 12202121, // 直播间 ID
 *           "title": "直播间标题", // 直播间标题
 *           "catalog_id": 233, // 直播间分类 ID
 *           "catalog_name": "配音", // 直播间分类名称
 *           "catalog_color": "#D68DFE", // 直播间分类颜色
 *           "custom_tag": { // 个性词条
 *             "tag_id": 10001, // 词条 ID
 *             "tag_name": "腹黑青叔" // 词条名称
 *           },
 *           "user_id": 9074501, // 主播 ID
 *           "username": "主播昵称", // 主播昵称
 *           "iconurl": "https://static-test.missevan.com/avatars/icon01.png", // 主播头像
 *           "cover_url": "https://static-test.missevan.com/fmcovers/icon01.png", // 直播间封面图
 *           "status": 1 // 直播间状态。0：未开播；1：开播中
 *         },
 *         "9074502": { // 主播 ID
 *           "room_id": 12202122, // 直播间 ID
 *           "title": "直播间标题", // 直播间标题
 *           "catalog_id": 233, // 直播间分类 ID
 *           "catalog_name": "配音", // 直播间分类名称
 *           "catalog_color": "#D68DFE", // 直播间分类颜色
 *           "custom_tag": { // 个性词条
 *             "tag_id": 10001, // 词条 ID
 *             "tag_name": "腹黑青叔" // 词条名称
 *           },
 *           "user_id": 9074502, // 主播 ID
 *           "username": "主播昵称", // 主播昵称
 *           "iconurl": "https://static-test.missevan.com/avatars/icon01.png", // 主播头像
 *           "cover_url": "https://static-test.missevan.com/fmcovers/icon01.png", // 直播间封面图
 *           "status": 1 // 直播间状态。0：未开播；1：开播中
 *         }
 *       }
 *     }
 *   }
 *
 */
func ActionLiveRecommendInfo(c *handler.Context) (handler.ActionResponse, error) {
	var param recommendInfoParam
	err := c.BindJSON(&param)
	if err != nil || len(param.CreatorIDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	filter := bson.M{"creator_id": bson.M{"$in": param.CreatorIDs}}
	roomOptions := room.FindOptions{FindCreator: true, FindCatalogInfo: true, FindCustomTag: true}
	rooms, err := room.ListSimples(filter, nil, &roomOptions)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	data := make(map[int64]*recommendInfoRoom)
	for _, room := range rooms {
		catalogName := room.CatalogName
		// WORKAROUND: iOS < 6.0.8、安卓 < 6.0.9 的版本用分区名称下发个性词条名称
		if param.IsOld && room.CustomTag != nil {
			catalogName = room.CustomTag.TagName
		}

		info := &recommendInfoRoom{
			RoomID:       room.RoomID,
			Title:        room.Name,
			CatalogID:    room.CatalogID,
			CatalogName:  catalogName,
			CatalogColor: room.CatalogColor,
			CustomTag:    room.CustomTag,
			UserID:       room.CreatorID,
			Username:     room.CreatorUsername,
			IconURL:      room.CreatorIconURL,
			CoverURL:     room.CoverURL,
		}
		if room.Status != nil {
			info.Status = room.Status.Open
		}
		data[room.CreatorID] = info
	}

	return handler.M{
		"data": data,
	}, nil
}
