package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/liveextrabanner"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testRoomID                = 4381915
	testCreatorID             = 1
	testLiveExtraBannerUserID = 233
)

func TestLiveExtraBannersTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(liveExtraBannersParam{}, "user_id", "positions")
	kc.Check(liveExtraBannersResp{}, "data")
	kc.Check(liveExtraBannersItem{}, "pic", "url")
	kc.CheckOmitEmpty(liveExtraBannersParam{}, "user_id")
}

func TestNewLiveExtraBannersParam(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 用户 ID 参数错误
	api := "/rpc/live/extra-banners"
	param := liveExtraBannersParam{
		UserID: -1,
	}
	c := handler.NewRPCTestContext(api, param)
	_, err := newLiveExtraBannerParam(c)
	require.Equal(actionerrors.ErrParams, err)

	// 测试直播通栏位置列表参数为空
	param.UserID = 0
	c = handler.NewRPCTestContext(api, param)
	_, err = newLiveExtraBannerParam(c)
	require.Equal(actionerrors.ErrParams, err)

	// 测试直播通栏位置列表参数错误
	param.Positions = []int{233}
	c = handler.NewRPCTestContext(api, param)
	_, err = newLiveExtraBannerParam(c)
	require.Equal(actionerrors.ErrParams, err)

	// 测试获取正常接口参数
	param.Positions = []int{1, 2, 3, 4}
	c = handler.NewRPCTestContext(api, param)
	res, err := newLiveExtraBannerParam(c)
	require.NoError(err)
	assert.Zero(res.UserID)
	assert.Equal([]int{1, 2, 3, 4}, res.Positions)

	param.UserID = testLiveExtraBannerUserID
	c = handler.NewRPCTestContext(api, param)
	res, err = newLiveExtraBannerParam(c)
	require.NoError(err)
	assert.EqualValues(testLiveExtraBannerUserID, res.UserID)
}

func TestActionLiveExtraBanners(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	api := "/rpc/live/extra-banners"
	param := liveExtraBannersParam{}
	c := handler.NewRPCTestContext(api, param)
	_, err := ActionLiveExtraBanners(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试获取直播通栏数据
	cacheKey := keys.LocalKeyLiveExtraBannersMap0.Format()
	service.Cache5Min.Delete(cacheKey)
	require.NoError(liveextrabanner.LiveExtraBanner{}.DB().
		Delete("", "user_id = ? AND room_id = ?", testCreatorID, testRoomID).Error)
	banner := &liveextrabanner.LiveExtraBanner{
		Position: liveextrabanner.PositionExtraBanner1,
		UserID:   testCreatorID,
		RoomID:   testRoomID,
		Cover:    "oss://1.png",
	}
	require.NoError(banner.DB().Create(banner).Error)

	param.Positions = []int{liveextrabanner.PositionExtraBanner1}
	c = handler.NewRPCTestContext(api, param)
	res, err := ActionLiveExtraBanners(c)
	require.NoError(err)
	data, ok := res.(*liveExtraBannersResp)
	require.True(ok)
	require.Len(data.Data, 1)
	require.Len(data.Data[liveextrabanner.PositionExtraBanner1], 1)
	assert.Equal("https://static-test.missevan.com/1.png", data.Data[liveextrabanner.PositionExtraBanner1][0].Pic)
	assert.Equal("https://fm.uat.missevan.com/live/4381915", data.Data[liveextrabanner.PositionExtraBanner1][0].URL)
}

func TestLiveExtraBannerParam_listLiveMedal(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试获取用户勋章信息
	require.NoError(service.DB.Table(attentionuser.TableName()).
		Delete("", "user_active = ?", testLiveExtraBannerUserID).Error)
	followed := []attentionuser.AttentionUser{
		{UserActive: testLiveExtraBannerUserID, UserPasstive: 1},
		{UserActive: testLiveExtraBannerUserID, UserPasstive: 2},
	}
	require.NoError(servicedb.BatchInsert(service.DB, followed[0].TableName(), followed))

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"user_id": testLiveExtraBannerUserID})
	require.NoError(err)
	_, err = livemedal.Collection().InsertMany(ctx, []interface{}{
		&livemedal.LiveMedal{
			Simple: livemedal.Simple{
				CreatorID: 2,
				Point:     1,
				UserID:    testLiveExtraBannerUserID,
				Status:    livemedal.StatusOwned,
			},
		},
		&livemedal.LiveMedal{
			Simple: livemedal.Simple{
				CreatorID: 3,
				Point:     2,
				UserID:    testLiveExtraBannerUserID,
				Status:    livemedal.StatusShow,
			},
		},
	})
	require.NoError(err)

	param := liveExtraBannersParam{
		UserID: testLiveExtraBannerUserID,
		rooms: []*room.Room{
			{Helper: room.Helper{CreatorID: 1}},
			{Helper: room.Helper{CreatorID: 2}},
			{Helper: room.Helper{CreatorID: 3}},
		},
	}
	err = param.listLiveMedal()
	require.NoError(err)
	require.Len(param.followUserIDsMap, 2)
	require.Len(param.medals, 1)
	assert.EqualValues(2, param.medals[0].CreatorID)
	assert.EqualValues(testLiveExtraBannerUserID, param.medals[0].UserID)
	assert.EqualValues(1, param.medals[0].Point)
}

func TestLiveExtraBannerParam_buildResp(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试获取直播通栏数据
	param := liveExtraBannersParam{
		UserID: testLiveExtraBannerUserID,
		positionBannersMap: map[int][]liveextrabanner.LiveExtraBanner{
			liveextrabanner.PositionExtraBanner1: {
				{UserID: 1, RoomID: 1, CoverURL: "https://static-test.missevan.com/1.png"},
				{UserID: 2, RoomID: 2, CoverURL: "https://static-test.missevan.com/2.png"},
				{UserID: 3, RoomID: 3, CoverURL: "https://static-test.missevan.com/3.png"},
				{UserID: 4, RoomID: 4, CoverURL: "https://static-test.missevan.com/4.png"},
			},
			liveextrabanner.PositionExtraBanner2: {
				{UserID: 5, RoomID: 5, CoverURL: "https://static-test.missevan.com/5.png"},
				{UserID: 6, RoomID: 6, CoverURL: "https://static-test.missevan.com/6.png"},
			},
		},
		rooms: []*room.Room{
			{Helper: room.Helper{CreatorID: 1, RoomID: 1, Status: room.Status{Score: 1, OpenTime: 1}}},
			{Helper: room.Helper{CreatorID: 2, RoomID: 2, Status: room.Status{Score: 1, OpenTime: 2}}},
			{Helper: room.Helper{CreatorID: 4, RoomID: 4, Status: room.Status{Score: 2, OpenTime: 1}}},
			{Helper: room.Helper{CreatorID: 5, RoomID: 5, Status: room.Status{Score: 1, OpenTime: 1}}},
			{Helper: room.Helper{CreatorID: 6, RoomID: 6, Status: room.Status{Score: 2, OpenTime: 2}}},
		},
		followUserIDsMap: map[int64]struct{}{1: {}, 2: {}, 5: {}, 6: {}},
		medals: []*livemedal.LiveMedal{
			{Simple: livemedal.Simple{CreatorID: 1, Point: 2}},
			{Simple: livemedal.Simple{CreatorID: 2, Point: 1}},
			{Simple: livemedal.Simple{CreatorID: 5, Point: 1}},
		},
	}

	resp := param.buildResp()
	require.Len(resp.Data, 2)
	require.Len(resp.Data[liveextrabanner.PositionExtraBanner1], 3)
	assert.Equal("https://fm.uat.missevan.com/live/1", resp.Data[liveextrabanner.PositionExtraBanner1][0].URL)
	assert.Equal("https://static-test.missevan.com/1.png", resp.Data[liveextrabanner.PositionExtraBanner1][0].Pic)
	assert.Equal("https://fm.uat.missevan.com/live/2", resp.Data[liveextrabanner.PositionExtraBanner1][1].URL)
	assert.Equal("https://static-test.missevan.com/2.png", resp.Data[liveextrabanner.PositionExtraBanner1][1].Pic)
	assert.Equal("https://fm.uat.missevan.com/live/4", resp.Data[liveextrabanner.PositionExtraBanner1][2].URL)
	assert.Equal("https://static-test.missevan.com/4.png", resp.Data[liveextrabanner.PositionExtraBanner1][2].Pic)
	require.Len(resp.Data[liveextrabanner.PositionExtraBanner2], 2)
	assert.Equal("https://fm.uat.missevan.com/live/5", resp.Data[liveextrabanner.PositionExtraBanner2][0].URL)
	assert.Equal("https://static-test.missevan.com/5.png", resp.Data[liveextrabanner.PositionExtraBanner2][0].Pic)
	assert.Equal("https://fm.uat.missevan.com/live/6", resp.Data[liveextrabanner.PositionExtraBanner2][1].URL)
	assert.Equal("https://static-test.missevan.com/6.png", resp.Data[liveextrabanner.PositionExtraBanner2][1].Pic)
}
