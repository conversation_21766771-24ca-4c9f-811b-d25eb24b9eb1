package webrpc

import (
	"github.com/jinzhu/gorm"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/recommend"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// ActionGuildFixHotRecommendVacancy 修复公会热门推荐位的次数
/**
 * @api {post} /rpc/guild/fix-hot-recommend-vancancy 修复公会热门推荐位的次数
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": true
 *     }
 */
func ActionGuildFixHotRecommendVacancy(ctx *handler.Context) (handler.ActionResponse, error) {
	if err := fixGuildHotRecommendVacancy(); err != nil {
		return nil, err
	}
	return true, nil
}

func fixGuildHotRecommendVacancy() error {
	var allData []recommend.GuildRecommendVacancy
	err := recommend.GuildRecommendVacancy{}.DB().Where("initial_vacancy = 0").Find(&allData).Error
	if err != nil {
		logger.Error(err)
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for _, v := range allData {
		fixVacancy(v)
	}
	return nil
}

func fixVacancy(v recommend.GuildRecommendVacancy) {
	var timesUsed int
	err := recommend.GuildRecommendSquareHot{}.DB().
		Where("guild_id = ? AND start_time < ? AND end_time > ? AND position = ?", v.GuildID, v.EndTime, v.StartTime, v.Position).
		Count(&timesUsed).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"id": v.ID,
		}).Errorf("fix vancancy error: %v", err)
		// PASS
	}
	err = v.DB().Where("id = ?", v.ID).Updates(
		map[string]interface{}{
			"initial_vacancy": gorm.Expr("vacancy + ?", timesUsed),
			"edited_vacancy":  gorm.Expr("vacancy + ?", timesUsed),
			"modified_time":   goutil.TimeNow().Unix(),
		}).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"id": v.ID,
		}).Errorf("fix vancancy error: %v", err)
		// PASS
	}
}
