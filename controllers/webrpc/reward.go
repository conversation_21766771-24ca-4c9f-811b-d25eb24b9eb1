package webrpc

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/activityreward"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/models/mongodb/creatoritems"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

type rewarder interface {
	reward() (handler.ActionResponse, error)
}

const (
	paramTypeGift            = "gift"
	paramTypeDrawGift        = "draw_gift"
	paramTypeAppearance      = "appearance"
	paramTypeBackpack        = "backpack"
	paramTypeCreatorBackpack = "creator_backpack"
	paramTypeLiveTag         = "live_tag"
	paramTypeReward          = "reward"
	paramTypeRewards         = "rewards"
)

type rewardParam struct {
	Type string `json:"type"`
}

// ActionActivityReward handler
/**
 * @api {post} /rpc/activity/reward 发放活动奖励
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParamExample {json} 直播间送礼：
 *   {
 *     "type": "gift",
 *     "event_id": 196,
 *     "room_id": 111,
 *     "gift_params": [
 *       {
 *         "gift_id": 1,
 *         "gift_num": 1
 *       },
 *       {
 *         "gift_id": 2,
 *         "gift_num": 2
 *       }
 *     ]
 *   }
 *
 * @apiParamExample {json} 直播间随机送礼，只支持白给礼物奖池：
 *   {
 *     "type": "draw_gift",
 *     "event_id": 364,
 *     "room_id": 111,
 *     "pool_id": 111
 *   }
 *
 * @apiParamExample {json} 发放外观：
 *   {
 *     "type": "appearance",
 *     "user_id": 111,
 *     "appearance_params": [
 *       {
 *         "appearance_id": 1,
 *         "duration": 1 // 有效期，秒
 *       },
 *       {
 *         "appearance_id": 2,
 *         "expire_time": 1656052847 // 过期时间，秒
 *       }
 *     ]
 *   }
 *
 * @apiParamExample {json} 发放背包礼物：
 *   {
 *     "type": "backpack", // 发放主播背包为 creator_backpack
 *     "user_id": 111,
 *     "gift_id": 111,
 *     "gift_num": 111,
 *     "start_time": 1656052847, // 秒
 *     "end_time": 1656052847
 *   }
 *
 * @apiParamExample {json} 发放直播间角标：
 *   {
 *     "type": "live_tag",
 *     "room_id": 111,
 *     "icon_url": "oss://icon.png",
 *     "start_time": 1656052847, // 秒
 *     "end_time": 1656052847
 *   }
 *
 * @apiParamExample {json} 通过 reward id 发放奖励：
 *   {
 *     "type": "reward",
 *     "reward_id": 111,
 *     "room_id": 111,
 *     "creator_id": 111,
 *     "user_id": 222
 *   }
 *
 * @apiParamExample {json} 通过 reward 批量发放奖励：
 *   {
 *     "type": "rewards",
 *     "trace_id": "1234567890",
 *     "rewards": [
 *       {
 *         "reward_id": 1,
 *         "room_id": 111,
 *         "creator_id": 111,
 *         "user_id": 222,
 *         "uuid": "uuid1"
 *       },
 *       {
 *         "reward_id": 2,
 *         "room_id": 222,
 *         "creator_id": 222,
 *         "user_id": 1,
 *         "uuid": "uuid2"
 *       },
 *       {
 *         "reward_id": 2,
 *         "room_id": 222,
 *         "creator_id": 222,
 *         "user_id": 1,
 *         "uuid": "uuid3"
 *       }
 *     ]
 *   }
 *
 * @apiSuccessExample {json} Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "rewards": {
 *         "uuid1": 1, // 发奖成功
 *         "uuid2": 0, // 发放过, 暂不支持幂等
 *         "uuid3": -1 // 发奖失败
 *       }
 *     }
 *   }
 *
 * @apiSuccessExample {json} 直播间随机送礼 Success-Response:
 *   HTTP/1.1 200 OK
 *   {
 *     "code": 0,
 *     "info": {
 *       "gift": {
 *         "gift_id": 1,
 *         "name": "礼物名称"
 *       }
 *     }
 *   }
 *
 */
func ActionActivityReward(c *handler.Context) (handler.ActionResponse, error) {
	req := c.Request()
	if req.Body == nil {
		return nil, actionerrors.ErrParams
	}
	defer req.Body.Close()

	data, err := io.ReadAll(req.Body)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var param rewardParam
	err = json.Unmarshal(data, &param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	list := map[string]rewarder{
		paramTypeGift:            &rewardGiftParam{},
		paramTypeDrawGift:        &rewardDrawGiftParam{},
		paramTypeAppearance:      &rewardAppearanceParam{},
		paramTypeBackpack:        &rewardBackpackParam{},
		paramTypeCreatorBackpack: &rewardCreatorBackpackParam{},
		paramTypeLiveTag:         &rewardLiveTagParam{},
		paramTypeReward:          &rewardRewardParam{},
		paramTypeRewards:         &rewardRewardsParam{},
	}

	t, ok := list[param.Type]
	if !ok {
		return nil, actionerrors.ErrParams
	}

	err = json.Unmarshal(data, t)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	return t.reward()
}

type rewardGiftParam struct {
	EventID    int64                      `json:"event_id"`
	RoomID     int64                      `json:"room_id"`
	UserID     int64                      `json:"user_id"`
	GiftParams []activityreward.GiftParam `json:"gift_params"`
}

func (s rewardGiftParam) reward() (handler.ActionResponse, error) {
	userID := activityreward.MaoerWalletUserID
	if s.UserID > 0 {
		userID = s.UserID
	}

	giftParam := activityreward.NewReward(s.EventID,
		s.RoomID,
		userID,
		s.GiftParams, nil,
	)

	err := giftParam.BuildRewardParam()
	if err != nil {
		return nil, err
	}

	err = giftParam.SendActivityReward()
	if err != nil {
		return nil, err
	}

	return handler.M{"success": true}, nil
}

type rewardDrawGiftParam struct {
	EventID int64 `json:"event_id"`
	RoomID  int64 `json:"room_id"`
	PoolID  int64 `json:"pool_id"`
}

type rewardDrawGiftResp struct {
	Gift rewardDrawGift `json:"gift"`
}

type rewardDrawGift struct {
	GiftID int64  `json:"gift_id"`
	Name   string `json:"name"`
}

func (s rewardDrawGiftParam) reward() (handler.ActionResponse, error) {
	if s.RoomID <= 0 || s.PoolID <= 0 {
		return nil, actionerrors.ErrParams
	}

	drawPool, err := gift.FindPool(s.PoolID, gift.PoolTypeRebateGift)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if drawPool == nil {
		return nil, actionerrors.ErrNotFound("奖池不存在")
	}

	ok, gifts := drawPool.Valid()
	if !ok {
		return nil, actionerrors.NewErrServerInternal(errors.New("奖池数据异常"), logger.Fields{
			"pool_id": s.PoolID,
		})
	}

	drawResult, err := drawPool.Draw(activityreward.MaoerWalletUserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	gift, ok := gifts[drawResult.GiftID]
	if !ok {
		return nil, actionerrors.ErrNotFound("礼物不存在")
	}

	giftParam := activityreward.NewReward(
		s.EventID, s.RoomID,
		activityreward.MaoerWalletUserID,
		[]activityreward.GiftParam{
			{GiftID: drawResult.GiftID, GiftNum: 1},
		}, nil,
	)

	err = giftParam.BuildRewardParam()
	if err != nil {
		return nil, err
	}

	err = giftParam.SendActivityReward()
	if err != nil {
		return nil, err
	}

	return rewardDrawGiftResp{
		Gift: rewardDrawGift{
			GiftID: gift.GiftID,
			Name:   gift.Name,
		},
	}, nil
}

type rewardAppearanceParam struct {
	UserID           int64                      `json:"user_id"`
	AppearanceParams []rank.SendAppearanceParam `json:"appearance_params"`
}

func (s rewardAppearanceParam) reward() (handler.ActionResponse, error) {
	if s.UserID == 0 {
		return nil, actionerrors.ErrParams
	}

	for i := range s.AppearanceParams {
		s.AppearanceParams[i].UserID = s.UserID
	}

	err := rank.SendAppearances(s.AppearanceParams...)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return handler.M{"success": true}, nil
}

type rewardBackpackParam struct {
	UserID    int64 `json:"user_id"`
	GiftID    int64 `json:"gift_id"`
	GiftNum   int64 `json:"gift_num"`
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
}

func (s rewardBackpackParam) reward() (handler.ActionResponse, error) {
	if s.UserID <= 0 || s.GiftID <= 0 || s.GiftNum <= 0 || s.StartTime <= 0 || s.EndTime <= 0 {
		return nil, actionerrors.ErrParams
	}

	g, err := gift.FindShowingGiftByGiftID(s.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrNotFound(fmt.Sprintf("can not found gift %d", s.GiftID))
	}

	if !useritems.IsBackpackGift(g) {
		return nil, actionerrors.ErrParamsMsg("gift is not a backpack gift")
	}

	err = useritems.AddGiftToUsers([]int64{s.UserID}, g, s.GiftNum, useritems.SourceNormal, s.StartTime, s.EndTime)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return handler.M{"success": true}, nil
}

type rewardCreatorBackpackParam struct {
	UserID    int64 `json:"user_id"`
	GiftID    int64 `json:"gift_id"`
	GiftNum   int64 `json:"gift_num"`
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
}

func (s rewardCreatorBackpackParam) reward() (handler.ActionResponse, error) {
	if s.UserID <= 0 || s.GiftID <= 0 || s.GiftNum <= 0 || s.StartTime <= 0 || s.EndTime <= 0 {
		return nil, actionerrors.ErrParams
	}

	g, err := gift.FindShowingGiftByGiftID(s.GiftID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if g == nil {
		return nil, actionerrors.ErrNotFound(fmt.Sprintf("can not found gift %d", s.GiftID))
	}

	if !creatoritems.IsBackpackGift(g) {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("gift %d is not a backpack gift", s.GiftID))
	}

	return nil, creatoritems.AddGiftToCreators([]int64{s.UserID}, g, s.GiftNum, s.StartTime, s.EndTime)
}

type rewardLiveTagParam struct {
	RoomID    int64  `json:"room_id"`
	IconURL   string `json:"icon_url"`
	StartTime int64  `json:"start_time"`
	EndTime   int64  `json:"end_time"`
}

func (s rewardLiveTagParam) reward() (handler.ActionResponse, error) {
	if s.RoomID <= 0 || s.IconURL == "" || s.StartTime <= 0 || s.EndTime <= 0 {
		return nil, actionerrors.ErrParams
	}

	err := liverecommendedelements.AddLiveIcon(s.RoomID, s.StartTime, s.EndTime, s.IconURL)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return handler.M{"success": true}, nil
}

type rewardRewardParam struct {
	UUID      string `json:"uuid"`
	RewardID  int64  `json:"reward_id"`
	UserID    int64  `json:"user_id"`
	CreatorID int64  `json:"creator_id"`
	RoomID    int64  `json:"room_id"`
}

func (r rewardRewardParam) reward() (handler.ActionResponse, error) {
	if r.RewardID <= 0 || r.RoomID < 0 || r.CreatorID < 0 || r.UserID < 0 {
		return nil, actionerrors.ErrParams
	}

	if r.RoomID == 0 && r.CreatorID == 0 && r.UserID == 0 {
		return nil, actionerrors.ErrParams
	}

	reward, err := reward.FindRewardByRewardIDWithCache(r.RewardID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if reward == nil {
		return nil, actionerrors.ErrNotFound(fmt.Sprintf("reward_id: %d not found", r.RewardID))
	}

	err = reward.Send(r.RoomID, r.CreatorID, r.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return handler.M{"success": true}, nil
}

type rewardRewardsParam struct {
	TraceID string              `json:"trace_id"`
	Rewards []rewardRewardParam `json:"rewards"`
}

func (r rewardRewardsParam) reward() (handler.ActionResponse, error) {
	if len(r.Rewards) == 0 {
		return nil, actionerrors.ErrParams
	}

	rewardIDs := make([]int64, 0, len(r.Rewards))
	for _, rw := range r.Rewards {
		if rw.RewardID <= 0 || rw.RoomID < 0 || rw.CreatorID < 0 || rw.UserID < 0 {
			return nil, actionerrors.ErrParams
		}

		if rw.RoomID == 0 && rw.CreatorID == 0 && rw.UserID == 0 {
			return nil, actionerrors.ErrParams
		}
		rewardIDs = append(rewardIDs, rw.RewardID)
	}
	if len(rewardIDs) == 0 {
		return nil, actionerrors.ErrParams
	}

	rs, err := reward.FindRewards(sets.Uniq(rewardIDs))
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	var (
		rewardRespMap = make(map[string]int, len(r.Rewards))
		rewardMap     = util.ToMap(rs, func(r *reward.Reward) int64 {
			return r.RewardID
		})
	)
	// TODO: 需要支持幂等
	for _, rw := range r.Rewards {
		rd, ok := rewardMap[rw.RewardID]
		if !ok {
			logger.WithFields(logger.Fields{
				"reward_id": rw.RewardID,
				"trace_id":  r.TraceID,
				"uuid":      rw.UUID,
			}).Error("reward not found")
			rewardRespMap[rw.UUID] = -1 // 发奖失败
			continue
		}
		err = rd.SetTrace(r.TraceID, rw.UUID).Send(rw.RoomID, rw.CreatorID, rw.UserID)
		if err != nil {
			logger.WithFields(logger.Fields{
				"room_id":   rw.RoomID,
				"user_id":   rw.UserID,
				"reward_id": rw.RewardID,
				"trace_id":  r.TraceID,
				"uuid":      rw.UUID,
			}).Error(err)
			rewardRespMap[rw.UUID] = -1 // 发奖失败
		} else {
			rewardRespMap[rw.UUID] = 1 // 发奖成功
		}
	}
	return handler.M{"rewards": rewardRespMap}, nil
}
