package webrpc

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/activity/activityreward"
	"github.com/MiaoSiLa/live-service/controllers/activity/rank"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionActivityReward(t *testing.T) {
	assert := assert.New(t)

	key := keys.KeyOnlineGifts0.Format()
	service.Cache5s.Set(key, []gift.Gift{}, 0)
	defer func() {
		service.Cache5s.Flush()
	}()

	c := handler.NewTestContext("POST", "", false, map[string]interface{}{
		"type":       "creator_backpack",
		"user_id":    12,
		"gift_id":    2,
		"gift_num":   1,
		"start_time": 1,
		"end_time":   2,
	})
	_, err := ActionActivityReward(c)
	assert.EqualError(err, "can not found gift 2")
}

func TestRewardGiftParam(t *testing.T) {
	assert := assert.New(t)

	cancel := mrpc.SetMock(vip.URLUserVips, func(interface{}) (interface{}, error) {
		return handler.M{"vips": handler.M{}}, nil
	})
	defer cancel()

	p := rewardGiftParam{
		EventID: 1,
		RoomID:  22489473,
		GiftParams: []activityreward.GiftParam{
			{GiftID: 1, GiftNum: 2},
		},
	}
	_, err := p.reward()
	assert.NoError(err)

	p1 := rewardGiftParam{
		EventID: 1,
		UserID:  12,
		RoomID:  22489473,
		GiftParams: []activityreward.GiftParam{
			{GiftID: 1, GiftNum: 2},
		},
	}
	_, err = p1.reward()
	assert.NoError(err)
}

func TestRewardDrawGiftParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := gift.CollectionDrawPool().UpdateOne(ctx,
		bson.M{"pool_id": 999},
		bson.M{"$set": bson.M{
			"type":  gift.PoolTypeRebateGift,
			"rates": map[int64]int{1: 100},
		}},
		options.Update().SetUpsert(true),
	)
	require.NoError(err)

	g, err := gift.FindShowingGiftByGiftID(1)
	require.NoError(err)
	require.NotNil(g)

	p := rewardDrawGiftParam{
		EventID: 1,
		RoomID:  22489473,
		PoolID:  999,
	}
	resp, err := p.reward()
	require.NoError(err)
	assert.Equal(rewardDrawGiftResp{
		rewardDrawGift{
			GiftID: g.GiftID,
			Name:   g.Name,
		},
	}, resp)
}

func TestRewardAppearanceParam(t *testing.T) {
	assert := assert.New(t)

	p := rewardAppearanceParam{
		UserID: 12,
		AppearanceParams: []rank.SendAppearanceParam{
			{AppearanceID: 1011, Duration: 3600},
		},
	}
	_, err := p.reward()
	assert.NoError(err)
}

func TestRewardBackpack(t *testing.T) {
	assert := assert.New(t)

	p := rewardBackpackParam{
		UserID:    12,
		GiftID:    301,
		StartTime: 1,
		EndTime:   2,
	}

	_, err := p.reward()
	assert.Equal(actionerrors.ErrParams, err)

	p.GiftNum = 1
	_, err = p.reward()
	assert.NoError(err)
}

func TestRewardCreatorBackpackParam(t *testing.T) {
	assert := assert.New(t)

	p := rewardCreatorBackpackParam{
		UserID:    12,
		GiftID:    301,
		StartTime: 1,
		EndTime:   2,
	}
	_, err := p.reward()
	assert.Equal(actionerrors.ErrParams, err)

	p.GiftNum = 1
	_, err = p.reward()
	assert.NoError(err)
}

func TestRewardLiveTagParam(t *testing.T) {
	assert := assert.New(t)

	p := rewardLiveTagParam{
		RoomID:    22489473,
		StartTime: 1,
		EndTime:   12,
	}
	_, err := p.reward()
	assert.Equal(actionerrors.ErrParams, err)

	p.IconURL = "oss://example"
	_, err = p.reward()
	assert.NoError(err)
}

func TestRewardRewardParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := rewardRewardParam{
		RewardID:  0,
		RoomID:    22489473,
		CreatorID: 12,
		UserID:    13,
	}

	_, err := r.reward()
	assert.Equal(actionerrors.ErrParams, err)

	r.RewardID = 2
	key := keys.KeyReward1.Format(r.RewardID)
	err = service.LRURedis.Set(key, tutil.SprintJSON(reward.Reward{
		RewardID:  2,
		Type:      10,
		ElementID: 1,
		Duration:  3600,
	}), time.Minute).Err()
	require.NoError(err)
	defer service.LRURedis.Del(key)

	require.NoError(livecustom.LiveCustom{}.DB().Delete("", "custom_type = ? AND element_id = ?",
		livecustom.TypeUserCustomGift, r.UserID).Error)

	_, err = r.reward()
	require.NoError(err)
	res, err := livecustom.FindUserCustomGift(r.UserID, 1)
	require.NoError(err)
	require.NotNil(res)
}

func TestRewardRewardsParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := rewardRewardsParam{}
	_, err := r.reward()
	assert.Equal(actionerrors.ErrParams, err)

	r = rewardRewardsParam{
		Rewards: []rewardRewardParam{
			{
				RewardID:  1,
				UserID:    12,
				CreatorID: 1,
				RoomID:    1,
			},
			{
				RewardID:  2,
				UserID:    12,
				CreatorID: 1,
				RoomID:    1,
			},
		},
	}
	_, err = r.reward()
	require.NoError(err)
}
