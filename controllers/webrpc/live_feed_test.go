package webrpc

import (
	"strconv"
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestLiveFeedParamsTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(liveFeedParams{}, "user_id")
}

func TestAttentionFeedTags(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(attentionFeed{}, "user_id", "room_id", "title", "status", "live_start_time", "cover_url",
		"catalog_id", "catalog_name", "catalog_color", "username", "iconurl", "confirm")
}

func addFeedTestData(r room.Room) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := room.Collection().InsertOne(ctx, r)
	if err != nil {
		return err
	}

	return nil
}

func clearFeedTestData() error {
	// 清空缓存
	service.LRURedis.Del(keys.KeyUserLiveFeed1.Format(109))
	service.Redis.Del(keyTodayDisplayTimes(109))
	// 删除亲密度测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().DeleteMany(ctx, bson.M{"creator_id": bson.M{"$in": []int64{12, 13}}, "user_id": 109})
	if err != nil {
		return err
	}
	// 删除直播间测试数据
	_, err = room.Collection().DeleteMany(ctx,
		bson.M{"creator_id": bson.M{"$in": []int64{12, 13, 14, 15}},
			"room_id":     bson.M{"$in": []int64{100000001, 100000002, 100000003, 100000004, 100000005, 100000006, 100000007, 100000008}},
			"status.open": room.StatusOpenTrue})
	if err != nil {
		return err
	}
	_, err = livemedal.Collection().DeleteMany(ctx,
		bson.M{"room_id": bson.M{"$in": []int64{100000005, 100000006, 100000007, 100000008}},
			"user_id":     int64(109),
			"status.open": room.StatusOpenTrue})
	if err != nil {
		return err
	}

	return nil
}

func TestActionLiveFeed(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除测试数据
	err := clearFeedTestData()
	require.NoError(err)

	// 测试直播动态为空
	input := liveFeedParams{UserID: 109}
	c := handler.NewRPCTestContext("/", input)
	resp, err := ActionLiveFeed(c)
	require.NoError(err)
	res := resp.(*attentionFeed)
	assert.Nil(res)
	// 确认空缓存是否生成
	key := keys.KeyUserLiveFeed1.Format(109)
	val, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	assert.Equal("null", val)

	// 测试有直播动态空缓存
	c = handler.NewRPCTestContext("/", input)
	resp, err = ActionLiveFeed(c)
	require.NoError(err)
	res = resp.(*attentionFeed)
	assert.Nil(res)
	// 删除空缓存
	require.NoError(service.LRURedis.Del(key).Err())

	// 新建直播间测试数据
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 12,
			RoomID:    100000001,
			Name:      "测试直播间 1",
			NameClean: "测试直播间 1",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: util.TimeToUnixMilli(goutil.TimeNow().Add(-11 * time.Minute)), Score: 100},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)

	// 测试有直播动态
	input = liveFeedParams{UserID: 109}
	c = handler.NewRPCTestContext("/", input)
	resp, err = ActionLiveFeed(c)
	require.NoError(err)
	res = resp.(*attentionFeed)
	require.NotNil(res)
	assert.EqualValues(12, res.UserID)
	// 测试缓存是否生成
	exist, err := service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.EqualValues(1, exist)
	// 测试今日展示次数是否加 1
	todayDisplayTimesKey := keyTodayDisplayTimes(109)
	displayTimes, err := service.Redis.ZScore(todayDisplayTimesKey, strconv.FormatInt(res.UserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1, displayTimes)
}

func TestNewLiveFeedParams(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试参数错误
	input := liveFeedParams{UserID: -2}
	c := handler.NewRPCTestContext("/", input)
	p, err := newLiveFeedParams(c)
	require.Equal(actionerrors.ErrParams, err)
	assert.Nil(p)

	// 测试参数正常
	input = liveFeedParams{UserID: 2}
	c = handler.NewRPCTestContext("/", input)
	p, err = newLiveFeedParams(c)
	require.NoError(err)
	assert.NotNil(p)
	assert.EqualValues(2, p.UserID)
	assert.NotEmpty(p.todayDisplayTimesKey)
}

func TestLiveFeed(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除测试数据
	err := clearFeedTestData()
	require.NoError(err)

	// 测试缓存不存在
	p := &liveFeedParams{UserID: 109, todayDisplayTimesKey: keyTodayDisplayTimes(109)}
	err = p.liveFeed()
	require.NoError(err)
	assert.Nil(p.resp)
	// 确认空缓存是否生成
	key := keys.KeyUserLiveFeed1.Format(109)
	val, err := service.LRURedis.Get(key).Result()
	require.NoError(err)
	assert.Equal("null", val)
	// 删除缓存
	require.NoError(service.LRURedis.Del(key).Err())

	// 新建直播间测试数据
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 12,
			RoomID:    100000002,
			Name:      "测试直播间 2",
			NameClean: "测试直播间 2",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: util.TimeToUnixMilli(goutil.TimeNow().Add(-11 * time.Minute)), Score: 100},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)

	// 测试无缓存，有直播动态数据
	p = &liveFeedParams{UserID: 109, todayDisplayTimesKey: keyTodayDisplayTimes(109)}
	err = p.liveFeed()
	require.NoError(err)
	require.NotNil(p.resp)
	assert.EqualValues(12, p.resp.UserID)
	// 测试缓存是否生成
	exist, err := service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.EqualValues(1, exist)

	// 修改今日展示次数为 1
	todayDisplayTimesKey := keyTodayDisplayTimes(109)
	displayTimes, err := service.Redis.ZIncrBy(todayDisplayTimesKey, 1, strconv.FormatInt(12, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1, displayTimes)

	// 测试有缓存，今日第二次展示
	err = p.liveFeed()
	require.NoError(err)
	assert.NotNil(p.resp)
	assert.EqualValues(12, p.resp.UserID)
	// 测试缓存是否生成
	exist, err = service.LRURedis.Exists(key).Result()
	require.NoError(err)
	assert.EqualValues(1, exist)

	// 修改今日展示次数为 2
	displayTimes, err = service.Redis.ZIncrBy(todayDisplayTimesKey, 1, strconv.FormatInt(12, 10)).Result()
	require.NoError(err)
	assert.EqualValues(2, displayTimes)

	// 测试今日已经展示过两次时
	p = &liveFeedParams{UserID: 109, todayDisplayTimesKey: keyTodayDisplayTimes(109)}
	err = p.liveFeed()
	require.NoError(err)
	assert.Nil(p.resp)
	assert.Equal([]int64{12}, p.displayTwiceCreatorIDs)
	// 测试空缓存是否生成
	val, err = service.LRURedis.Get(key).Result()
	require.NoError(err)
	assert.Equal("null", val)

	// 测试有空缓存时
	err = p.liveFeed()
	require.NoError(err)
	assert.Nil(p.resp)
}

func TestFindLiveFeed(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除直播间测试数据
	err := clearFeedTestData()
	require.NoError(err)

	// 测试直播动态不存在
	p := &liveFeedParams{UserID: 109, todayDisplayTimesKey: keyTodayDisplayTimes(109)}
	followRoom, err := p.findLiveFeed()
	require.NoError(err)
	assert.Nil(followRoom)

	// 新建直播间测试数据
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	openTime := util.TimeToUnixMilli(goutil.TimeNow().Add(-11 * time.Minute))
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 12,
			RoomID:    100000003,
			Name:      "测试直播间 3",
			NameClean: "测试直播间 3",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 100},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)

	// 测试关注的主播中只有一个开播中（开播时长大于 10 分钟）的主播
	followRoom, err = p.findLiveFeed()
	require.NoError(err)
	require.NotNil(followRoom)
	assert.EqualValues(12, followRoom.CreatorID)

	// 新建测试直播间
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 13,
			RoomID:    100000004,
			Name:      "测试直播间 4",
			NameClean: "测试直播间 4",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 101},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)

	// 生成亲密度数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().DeleteMany(ctx, bson.M{"creator_id": bson.M{"$in": []int64{12, 13}}, "user_id": 109})
	require.NoError(err)
	_, err = livemedal.Collection().InsertMany(ctx, []interface{}{
		livemedal.LiveMedal{Simple: livemedal.Simple{RoomID: 100000, CreatorID: 12, UserID: 109, Point: 100}},
		livemedal.LiveMedal{Simple: livemedal.Simple{RoomID: 100001, CreatorID: 13, UserID: 109, Point: 99}},
	})
	require.NoError(err)

	// 测试取亲密度最高的数据
	followRoom, err = p.findLiveFeed()
	require.NoError(err)
	require.NotNil(followRoom)
	assert.EqualValues(12, followRoom.CreatorID)

	// 将两个主播的亲密度设为相同
	_, err = livemedal.Collection().UpdateOne(ctx,
		bson.M{"creator_id": 13, "user_id": 109},
		bson.M{"$set": bson.M{"point": 100}},
		options.Update().SetUpsert(true))
	require.NoError(err)

	// 测试两个主播打赏额度相同，亲密度也相同时，取热度最高的数据
	followRoom, err = p.findLiveFeed()
	require.NoError(err)
	require.NotNil(followRoom)
	assert.EqualValues(13, followRoom.CreatorID)

	// 测试主播信息已展示过两次
	p.displayTwiceCreatorIDs = []int64{13}
	followRoom, err = p.findLiveFeed()
	require.NoError(err)
	require.NotNil(followRoom)
	assert.EqualValues(12, followRoom.CreatorID)
}

func TestListFollowFeed(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除直播间测试数据
	err := clearFeedTestData()
	require.NoError(err)

	// 测试无直播动态数据时
	p := &liveFeedParams{UserID: 100000, todayDisplayTimesKey: keyTodayDisplayTimes(100000)}
	followRooms, err := p.listFollowFeed()
	require.NoError(err)
	assert.Empty(followRooms)

	// 新建测试直播间
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	openTime := util.TimeToUnixMilli(goutil.TimeNow().Add(-11 * time.Minute))
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 12,
			RoomID:    100000005,
			Name:      "测试直播间 5",
			NameClean: "测试直播间 5",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 100},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)

	// 测试有一个直播动态数据
	p.UserID = 109
	p.todayDisplayTimesKey = keyTodayDisplayTimes(109)
	followRooms, err = p.listFollowFeed()
	require.NoError(err)
	require.Len(followRooms, 1)
	assert.EqualValues(12, followRooms[0].CreatorID)

	// 新建测试直播间
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 13,
			RoomID:    100000006,
			Name:      "测试直播间 6",
			NameClean: "测试直播间 6",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 1000},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)

	// 测试有多个直播动态数据
	followRooms, err = p.listFollowFeed()
	require.NoError(err)
	assert.Len(followRooms, 2)
	assert.EqualValues(13, followRooms[0].CreatorID)
	assert.EqualValues(12, followRooms[1].CreatorID)

	// 测试需要排除的主播数据
	p.UserID = 109
	p.displayTwiceCreatorIDs = []int64{12}
	followRooms, err = p.listFollowFeed()
	require.NoError(err)
	assert.Len(followRooms, 1)
	assert.EqualValues(13, followRooms[0].CreatorID)
}

func TestIncrTodayDisplayTimes(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 测试无直播动态数据时
	attentionFeed := attentionFeed{UserID: 10}
	todayDisplayTimesKey := keyTodayDisplayTimes(100000)
	require.NoError(service.Redis.Del(todayDisplayTimesKey).Err())
	p := &liveFeedParams{
		UserID:               100000,
		todayDisplayTimesKey: todayDisplayTimesKey,
		resp:                 &attentionFeed,
	}
	p.incrTodayDisplayTimes()
	// 测试今日展示次数是否加 1
	displayTimes, err := service.Redis.ZScore(todayDisplayTimesKey, strconv.FormatInt(p.resp.UserID, 10)).Result()
	require.NoError(err)
	assert.EqualValues(1, displayTimes)
}

func addMedalTestData(s *livemedal.Simple) error {
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err := livemedal.Collection().InsertOne(ctx, s)
	if err != nil {
		return err
	}
	return nil
}

func TestActionLiveFeedNotice(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除测试数据
	err := clearFeedTestData()
	require.NoError(err)

	userID := int64(109)
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	openTime := util.TimeToUnixMilli(goutil.TimeNow())
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 12,
			RoomID:    100000005,
			Name:      "测试直播间 5",
			NameClean: "测试直播间 5",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 50},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)
	s := &livemedal.Simple{RoomID: 100000005, CreatorID: 12, UserID: userID, Point: 1000, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)

	p := liveFeedNoticeParam{UserID: userID}
	c := handler.NewRPCTestContext("/", p)
	resp, err := ActionLiveFeedNotice(c)
	require.NoError(err)
	assert.NotNil(resp)
}

func TestGetNotice(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// 删除测试数据
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	// 删除直播间测试数据
	_, err := room.Collection().DeleteMany(ctx,
		bson.M{"creator_id": bson.M{"$in": []int64{120, 121, 122, 123}},
			"room_id":     bson.M{"$in": []int64{100000120, 100000121, 100000122, 100000123}},
			"status.open": room.StatusOpenTrue})
	require.NoError(err)
	userID := int64(110)
	_, err = livemedal.Collection().DeleteMany(ctx,
		bson.M{"room_id": bson.M{"$in": []int64{100000120, 100000121, 100000122, 100000123}},
			"user_id": userID})
	require.NoError(err)
	key := keys.KeyRoomsSuppressionHotList0.Format()
	err = service.Redis.Del(key).Err()
	require.NoError(err)

	// 测试没有粉丝勋章 ≥ 5 级的用户
	noDataUserID := int64(12)
	StartTime := int64(1678118400)
	p := liveFeedNoticeParam{UserID: noDataUserID, StartTime: StartTime}
	resp, err := p.getNotice()
	require.NoError(err)
	require.NotNil(resp)
	assert.Nil(resp.Room)

	// 创建房间号为 100000120 测试直播间
	cover := "live/room/schedule/202107/27/a871fa15d179cdcb5aaff8e5905f6c74123501.png"
	openTime := util.TimeToUnixMilli(time.Unix(StartTime, 0))
	r := room.Room{
		Helper: room.Helper{
			CreatorID: 120,
			RoomID:    100000120,
			Name:      "测试直播间 120",
			NameClean: "测试直播间 120",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 50},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)
	// 创建房间号为 100000121 测试直播间
	openTime = util.TimeToUnixMilli(time.Unix(StartTime, 0).Add(time.Minute))
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 121,
			RoomID:    100000121,
			Name:      "测试直播间 121",
			NameClean: "测试直播间 121",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 100},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)
	// 创建房间号为 100000122 测试直播间
	openTime = util.TimeToUnixMilli(time.Unix(StartTime, 0).Add(2 * time.Minute))
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 122,
			RoomID:    100000122,
			Name:      "测试直播间 122",
			NameClean: "测试直播间 122",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 50},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)
	// 创建房间号为 100000123 测试直播间
	openTime = util.TimeToUnixMilli(time.Unix(StartTime, 0).Add(3 * time.Minute))
	r = room.Room{
		Helper: room.Helper{
			CreatorID: 123,
			RoomID:    100000123,
			Name:      "测试直播间 123",
			NameClean: "测试直播间 123",
			Cover:     &cover,
			CatalogID: 75,
			Status:    room.Status{Open: room.StatusOpenTrue, OpenTime: openTime, Score: 50},
		},
	}
	err = addFeedTestData(r)
	require.NoError(err)
	// 创建直播间 100000120 的粉丝勋章
	s := &livemedal.Simple{RoomID: 100000120, CreatorID: 120, UserID: userID, Point: 1000, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)
	// 创建直播间 100000121 的粉丝勋章
	s = &livemedal.Simple{RoomID: 100000121, CreatorID: 121, UserID: userID, Point: 700, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)
	// 创建直播间 100000122 的粉丝勋章
	s = &livemedal.Simple{RoomID: 100000122, CreatorID: 122, UserID: userID, Point: 700, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)
	// 创建直播间 100000123 的粉丝勋章
	s = &livemedal.Simple{RoomID: 100000123, CreatorID: 123, UserID: userID, Point: 700, Status: livemedal.StatusOwned}
	err = addMedalTestData(s)
	require.NoError(err)

	// 测试获取亲密度最高的主播
	p.UserID = userID
	p.StartTime = 0
	resp, err = p.getNotice()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Room)
	assert.Equal(int64(100000120), resp.Room.RoomID)

	// 测试亲密度相同，获取的热度最高的主播
	p.UserID = userID
	p.StartTime = StartTime
	resp, err = p.getNotice()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Room)
	assert.Equal(int64(100000121), resp.Room.RoomID)

	// 测试亲密度和热度相同，获取最新开播的主播
	p.StartTime = StartTime + 60
	resp, err = p.getNotice()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Room)
	assert.Equal(int64(100000123), resp.Room.RoomID)

	// 测试过滤热度限制直播间
	err = service.Redis.ZAdd(key, &redis.Z{Member: int64(100000123), Score: float64(-1)}).Err()
	require.NoError(err)
	resp, err = p.getNotice()
	require.NoError(err)
	require.NotNil(resp)
	require.NotNil(resp.Room)
	assert.Equal(int64(100000122), resp.Room.RoomID)
}
