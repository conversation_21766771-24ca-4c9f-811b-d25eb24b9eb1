package webrpc

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestTagKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(userLiveRankParam{}, "type", "rank_num")
	kc.Check(userLiveRankResp{}, "data")
	kc.Check(userLiveRank{}, "room")
	kc.Check(roomInfo{}, "room_id", "catalog_id", "name", "announcement",
		"creator_id", "creator_username", "status", "cover_url")
}

func TestNewUserLiveRankParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var param userLiveRankParam
	c := handler.NewRPCTestContext("/rpc/user/live/rank", param)
	_, err := newUserLiveRankParam(c)
	assert.EqualError(err, "参数错误")

	param.RankType = usersrank.TypeNova
	c = handler.NewRPCTestContext("/rpc/user/live/rank", param)
	_, err = newUserLiveRankParam(c)
	assert.EqualError(err, "参数错误")

	param.RankType = usersrank.TypeDay
	param.RankNum = 51
	c = handler.NewRPCTestContext("/rpc/user/live/rank", param)
	_, err = newUserLiveRankParam(c)
	assert.EqualError(err, "参数错误")

	_, err = service.Redis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		now := goutil.TimeNow()
		key := usersrank.Key(usersrank.TypeDay, now)
		pipeliner.Del(key)
		pipeliner.ZAdd(key, &redis.Z{Score: 10, Member: 10}, &redis.Z{Score: 11, Member: 11},
			&redis.Z{Score: 12, Member: 12}, &redis.Z{Score: 13, Member: 13})
		pipeliner.Expire(key, 5*time.Minute)
		return nil
	})
	require.NoError(err)
	param.RankType = usersrank.TypeDay
	param.RankNum = 50
	c = handler.NewRPCTestContext("/rpc/user/live/rank", param)
	_, err = newUserLiveRankParam(c)
	require.NoError(err)
}

func TestUserLiveRankParam_findRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := service.Redis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		now := goutil.TimeNow()
		key := usersrank.Key(usersrank.TypeDay, now)
		pipeliner.Del(key)
		pipeliner.ZAdd(key, &redis.Z{Score: 10, Member: 10}, &redis.Z{Score: 11, Member: 11},
			&redis.Z{Score: 12, Member: 12}, &redis.Z{Score: 13, Member: 13})
		pipeliner.Expire(key, 5*time.Minute)
		return nil
	})
	require.NoError(err)

	p := &userLiveRankParam{
		RankType: usersrank.TypeDay,
		RankNum:  50,
	}
	require.NoError(p.findRank())
	assert.Len(p.resp.Data, 4)
	assert.EqualValues(13, p.resp.Data[0].UserID)
	assert.EqualValues(1, p.resp.Data[0].Rank)
	assert.EqualValues(10, p.resp.Data[3].UserID)
	assert.EqualValues(4, p.resp.Data[3].Rank)
}

func TestUserLiveRankParam_findRooms(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := &userLiveRankParam{
		resp: userLiveRankResp{
			Data: []*userLiveRank{
				{Info: &usersrank.Info{UserID: 13}},
				{Info: &usersrank.Info{UserID: 12}},
				{Info: &usersrank.Info{UserID: 11}},
				{Info: &usersrank.Info{UserID: 10}},
			},
		},
	}
	p.listUserIDs = []int64{13, 12, 11, 10}
	require.NoError(p.findRooms())
	assert.EqualValues(100000006, p.resp.Data[0].Room.RoomID)
	assert.Nil(p.resp.Data[2].Room)
}

func TestActionLiveUserRank(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	_, err := service.Redis.TxPipelined(func(pipeliner redis.Pipeliner) error {
		key := usersrank.Key(usersrank.TypeDay, now)
		pipeliner.Del(key)
		pipeliner.ZAdd(key, &redis.Z{Score: 10, Member: 10}, &redis.Z{Score: 11, Member: 11},
			&redis.Z{Score: 12, Member: 12}, &redis.Z{Score: 13, Member: 13})
		pipeliner.Expire(key, 5*time.Minute)
		return nil
	})
	require.NoError(err)

	param := userLiveRankParam{
		RankType: usersrank.TypeDay,
		RankNum:  50,
	}
	c := handler.NewRPCTestContext("/rpc/user/live/rank", param)
	res, err := ActionLiveUserRank(c)
	require.NoError(err)
	data := res.(userLiveRankResp)
	assert.Len(data.Data, 4)
	assert.EqualValues(13, data.Data[0].UserID)
	assert.EqualValues(1, data.Data[0].Rank)
	assert.EqualValues(100000006, data.Data[0].Room.RoomID)
	assert.EqualValues(10, data.Data[3].UserID)
	assert.EqualValues(4, data.Data[3].Rank)
	assert.EqualValues(22489473, data.Data[3].Room.RoomID)
}
