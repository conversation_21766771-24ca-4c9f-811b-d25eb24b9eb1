package webrpc

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/activity"
	"github.com/MiaoSiLa/live-service/controllers/activity/activityreward"
	"github.com/MiaoSiLa/live-service/controllers/admin/user"
	"github.com/MiaoSiLa/live-service/controllers/chatroom"
	"github.com/MiaoSiLa/live-service/controllers/chatroom/recommend"
	"github.com/MiaoSiLa/live-service/controllers/webrpc/cron"
	"github.com/MiaoSiLa/live-service/controllers/webrpc/luckybag"
	"github.com/MiaoSiLa/live-service/controllers/webrpc/prize"
	webrpcuser "github.com/MiaoSiLa/live-service/controllers/webrpc/user"
	"github.com/MiaoSiLa/live-service/controllers/webuser/appearance"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/rpc"
)

// Handler returns the registered handler
func Handler() *handler.Handler {
	return &handler.Handler{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.MiddlewareAppKey(config.Conf.HTTP.RPCKey, config.Conf.HTTP.BiliAppKey),
		},
		Actions: map[string]*handler.Action{
			"live/set":             handler.NewAction(handler.POST, ActionLiveSet, false),
			"live/del":             handler.NewAction(handler.POST, ActionLiveDel, false),
			"live/logpunish":       handler.NewAction(handler.POST, ActionLiveLogPunish, false),
			"live/reissuegift":     handler.NewAction(handler.POST, user.ActionReissueGift, false),
			"live/sync-score":      handler.NewAction(handler.POST, ActionLiveSyncScore, false),
			"live/hourrank/random": handler.NewAction(handler.POST, ActionHourRankRandom, false),
			"live/recommend-info":  handler.NewAction(handler.POST, ActionLiveRecommendInfo, false),
			"live/check-pm":        handler.NewAction(handler.POST, ActionLiveCheckPM, false),
			"live/extra-banners":   handler.NewAction(handler.POST, ActionLiveExtraBanners, false),

			"chatroom/close":      handler.NewAction(handler.POST, ActionChatroomClose, false),
			"chatroom/image/set":  handler.NewAction(handler.POST, ActionReviewSet, false),
			"chatroom/review/set": handler.NewAction(handler.POST, ActionReviewSet, false),
			"chatroom/review/get": handler.NewAction(handler.POST, ActionReviewGet, false),
			"chatroom/vip/num":    handler.NewAction(handler.POST, chatroom.ActionVipNumber, false),

			"rank/revenue":        handler.NewAction(handler.POST, ActionRankRevenue, false),
			"rank/notifyhourrank": handler.NewAction(handler.POST, ActionNotifyHourRank, false),

			"util/giftnotify": handler.NewAction(handler.POST, ActionGiftNotify, false),

			"util/set-wrong-bank-info-userids": handler.NewAction(handler.POST, ActionSetWrongBankInfoUserIDs, false),

			"recommended/background": handler.NewAction(handler.POST, ActionRecommendedBackground, false),

			// 主播强制解约支付违约金的回调
			"guild/creator-terminate-forcely": handler.NewAction(handler.POST, ActionGuildCreatorTerminateForcely, false),

			// 修复公会热门推荐位的次数
			"guild/fix-hot-recommend-vancancy": handler.NewAction(handler.POST, ActionGuildFixHotRecommendVacancy, false),

			"guild/get-contract":          handler.NewAction(handler.POST, ActionGuildGetContract, false),
			"guild/terminate/exec-silent": handler.NewAction(handler.POST, ActionExpelLiveSilent, false),

			"widget/refresh": handler.NewAction(handler.POST, ActionWidgetRefresh, false),

			"activity/databus/senddelay": handler.NewAction(handler.POST, ActionActivityDatabusSendDelay, false),

			"room/userinfo": handler.NewAction(handler.POST, ActionRoomUserInfo, false),
			"room/users":    handler.NewAction(handler.POST, ActionRoomUsers, false),
			"room/info":     handler.NewAction(handler.POST, ActionRoomInfo, false),
			"room/open":     handler.NewAction(handler.POST, ActionRoomOpen, false),
			"room/taginfo":  handler.NewAction(handler.POST, ActionRoomTagInfo, false),

			"preview/get": handler.NewAction(handler.POST, ActionPreviewGet, false),

			"luckybag/get-drama-luckybags": handler.NewAction(handler.POST, luckybag.ActionGetDramaLuckyBags, false),
			"luckybag/drama-list":          handler.NewAction(handler.POST, luckybag.ActionDramaList, false),
		},
		SubHandlers: []handler.Handler{cronHandler(), activityHandler(), userHandler()}}
}

func cronHandler() handler.Handler {
	cron := handler.Handler{
		Name: "cron",
		Actions: map[string]*handler.Action{
			"noble/recommend/notify": handler.NewAction(handler.POST, ActionNobleRecommendNotify, false),
			"noble/expire":           handler.NewAction(handler.POST, cron.ActionCronExpireNotice, false),
			"noble/trial-expire":     handler.NewAction(handler.POST, cron.ActionCronNobleTrialExpire, false),

			"live/recommendation":   handler.NewAction(handler.POST, cron.ActionCalcRecommendation, false),
			"live/vitality/renewal": handler.NewAction(handler.POST, cron.ActionCronVitalityRenewal, false),

			"rank/user-hourly":                handler.NewAction(handler.POST, cron.ActionCronRankUserHourly, false),
			"rank/hourly/notify":              handler.NewAction(handler.POST, cron.ActionCronNotifyHourRank, false),
			"rank/hourly/lasthourtop3/notify": handler.NewAction(handler.POST, cron.ActionCronLastHourTop3Notify, false),
			"rank/monthly/notify":             handler.NewAction(handler.POST, cron.ActionCronNotifyMonthlyRank, false),
			"rank/nova":                       handler.NewAction(handler.POST, cron.ActionCronRankNova, false),
			"rank/archive/month":              handler.NewAction(handler.POST, cron.ActionCronRankArchiveMonth, false),
			"rank/archive/hour":               handler.NewAction(handler.POST, cron.ActionCronRankArchiveHour, false),
			"rank/archive/nova":               handler.NewAction(handler.POST, cron.ActionCronRankArchiveNova, false),

			"user/superfanrank": handler.NewAction(handler.POST, cron.ActionSuperFanRank, false),
			"user/birthdaypriv": handler.NewAction(handler.POST, cron.ActionCronSendBirthdayPriv, false),

			"cleanup":       handler.NewAction(handler.POST, cron.ActionCronCleanup, false),
			"medal/cleanup": handler.NewAction(handler.POST, cron.ActionCronMedalCleanup, false),

			"guild/cronjob":          handler.NewAction(handler.POST, cron.ActionGuildCronJob, false),
			"superfan/expire-notice": handler.NewAction(handler.POST, cron.ActionSuperFanExpireNotice, false),

			// WORKAROUND: 待 audio-chatroom-api 调整 "heat/superfans" 为 "hotscore/goods" 后，移除 "heat/superfans" 路由
			"heat/superfans": handler.NewAction(handler.POST, cron.ActionHotScoreGoods, false),
			"hotscore/goods": handler.NewAction(handler.POST, cron.ActionHotScoreGoods, false),

			"recommend/schedule/record": handler.NewAction(handler.POST, cron.ActionRecommendScheduleRecord, false),

			"giftwall/period/extend": handler.NewAction(handler.POST, cron.ActionCronGiftWallPeriodExtend, false),

			"sticker/superfans": handler.NewAction(handler.POST, cron.ActionStickerSuperFans, false),

			"luckybag/draw": handler.NewAction(handler.POST, cron.ActionCronLuckyBagDraw, false),
		},
	}
	return cron
}

func activityHandler() handler.Handler {
	return handler.Handler{
		Name: "activity",
		Actions: map[string]*handler.Action{
			"userapply": handler.NewAction(handler.POST, activity.ActionUserApply, false),
			"prepare":   handler.NewAction(handler.POST, activity.ActionActivityPrepare, false),
			"reward":    handler.NewAction(handler.POST, ActionActivityReward, false),
		},
		SubHandlers: []handler.Handler{activityCronHandler()},
	}
}

func activityCronHandler() handler.Handler {
	return handler.Handler{
		Name: "cron",
		Actions: map[string]*handler.Action{
			"rank/increase": handler.NewAction(handler.POST, activityreward.ActionActivityCronRankIncrease, false),
			"reward":        handler.NewAction(handler.POST, activityreward.ActionCronActivityReward, false),

			"wish/draw": handler.NewAction(handler.POST, cron.ActionCronWishesDraw, false),
		},
		SubHandlers: []handler.Handler{},
	}
}

func userHandler() handler.Handler {
	return handler.Handler{
		Name: "user",
		Actions: map[string]*handler.Action{
			"titles":          handler.NewAction(handler.POST, ActionUserTitles, false),
			"checkattention":  handler.NewAction(handler.POST, ActionCheckAttention, false),
			"changeattention": handler.NewAction(handler.POST, ActionChangeAttention, false),
			"info":            handler.NewAction(handler.POST, ActionUser, false),
			"block/add":       handler.NewAction(handler.POST, ActionUserBlockAdd, false),
			"block/remove":    handler.NewAction(handler.POST, ActionUserBlockRemove, false),
			"notify":          handler.NewAction(handler.POST, ActionNotify, false),
			"level":           handler.NewAction(handler.POST, ActionUserLevel, false),
			"level-up":        handler.NewAction(handler.POST, ActionUserLevelUp, false),
			"viewlog/delete":  handler.NewAction(handler.POST, ActionUserViewLogDelete, false),

			"backpack/add":    handler.NewAction(handler.POST, ActionBackpackAdd, false),
			"gift/custom/add": handler.NewAction(handler.POST, ActionGiftCustomAdd, false),

			"appearance/add": handler.NewAction(handler.POST, appearance.ActionAppearanceAdd, false),
			// WORKAROUND: 待 audio-chatroom-api 调整 "appearance/syncnoble" 为 "noble/sync" 后，移除 "appearance/syncnoble" 路由
			"appearance/syncnoble": handler.NewAction(handler.POST, webrpcuser.ActionNobleSync, false),

			"live/feed":        handler.NewAction(handler.POST, ActionLiveFeed, false),       // 获取用户直播动态
			"live/feed-notice": handler.NewAction(handler.POST, ActionLiveFeedNotice, false), // 获取用户直播动态提醒
			"live/rank":        handler.NewAction(handler.POST, ActionLiveUserRank, false),   // 获取主播收益榜列表

			"live/get-spend": handler.NewAction(handler.POST, webrpcuser.ActionLiveGetSpend, false),

			"noble/sync": handler.NewAction(handler.POST, webrpcuser.ActionNobleSync, false),

			"birthday/priv": handler.NewAction(handler.POST, ActionBirthdayPriv, false),
		},
	}
}

// HandlerV2 returns the registered handler v2
func HandlerV2() *handler.HandlerV2 {
	return &handler.HandlerV2{
		Name: "rpc",
		Middlewares: gin.HandlersChain{
			rpc.Middleware(config.Conf.HTTP.RPCKey)},

		SubHandlers: []handler.HandlerV2{
			liveHandlerV2(),
		},
	}
}

func chatroomHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "chatroom",
		Actions: map[string]*handler.ActionV2{
			"open/home-feed":     handler.NewActionV2(handler.POST, recommend.ActionHomeFeed, handler.ActionOption{LoginRequired: false}),
			"message/cross-send": handler.NewActionV2(handler.POST, ActionMessageCrossSend, handler.ActionOption{LoginRequired: false}),
		},
	}
}

func liveHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "live-service",
		Actions: map[string]*handler.ActionV2{
			"daily/task":           handler.NewActionV2(handler.POST, ActionLiveDailyTask, handler.ActionOption{LoginRequired: false}),
			"recommended/homepage": handler.NewActionV2(handler.POST, ActionRecommendedHomepage, handler.ActionOption{LoginRequired: false}),
		},
		SubHandlers: []handler.HandlerV2{
			cronHandlerV2(),
			chatroomHandlerV2(),
			prize.HandlerV2(),
		},
	}
}

func cronHandlerV2() handler.HandlerV2 {
	return handler.HandlerV2{
		Name: "cron",
		Actions: map[string]*handler.ActionV2{
			"user/backpack/expire-notify": handler.NewActionV2(handler.POST, cron.ActionCronUserBackpackExpireNotify, handler.ActionOption{LoginRequired: false}),

			"gashapon/rank/weekly/reward": handler.NewActionV2(handler.POST, cron.ActionCronGashaponWeeklyReward, handler.ActionOption{LoginRequired: false}),
			"user/black-card/expire":      handler.NewActionV2(handler.POST, cron.ActionCronUserBlackCardExpire, handler.ActionOption{LoginRequired: false}),
		},
	}
}
