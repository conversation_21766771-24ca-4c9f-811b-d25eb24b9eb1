package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionNotifyHourRank(t *testing.T) {
	assert := assert.New(t)

	// 当前排名比之前排名低
	req := usersrank.RankChange{
		UserID:   12,
		PrevRank: 1,
		Rank:     6,
		Score:    80,
	}
	c := handler.NewTestContext("POST", "", false, req)
	_, err := ActionNotifyHourRank(c)
	assert.Equal(actionerrors.ErrParams, err)
	req.PrevRank = 3
	c = handler.NewTestContext("POST", "", false, req)
	_, err = ActionNotifyHourRank(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 参数正确
	req.PrevRank = 9
	c = handler.NewTestContext("POST", "", false, req)
	_, err = ActionNotifyHourRank(c)
	assert.NoError(err)
}
