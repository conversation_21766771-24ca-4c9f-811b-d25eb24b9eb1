package webrpc

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestActionChangeAttention(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(ChangeAttentionParam{}, "attention", "attention_id", "user_id")
	kc.Check(ChangeAttentionResult{}, "msg", "type", "attention_id")

	cancelFollow := mrpc.SetMock("go://person/follow", func(input interface{}) (output interface{}, err error) {
		return "关注成功", nil
	})
	defer cancelFollow()

	cancelUnfollow := mrpc.SetMock("go://person/unfollow", func(input interface{}) (output interface{}, err error) {
		return "取消关注成功", nil
	})
	defer cancelUnfollow()

	param := handler.M{
		"user_id":      2,
		"attention_id": 1,
		"attention":    0,
	}
	c := handler.NewTestContext(http.MethodPost, "/user/changeattention", false, param)
	r, err := ActionChangeAttention(c)
	require.NoError(err)
	resp := r.(ChangeAttentionResult)
	assert.Equal("取消关注成功", resp.Msg)
	assert.Equal(int64(1), resp.AttentionID)
	assert.Equal(0, resp.Type)
	param["attention"] = 1
	c = handler.NewTestContext(http.MethodPost, "/user/changeattention", false, param)
	r, err = ActionChangeAttention(c)
	require.NoError(err)
	resp = r.(ChangeAttentionResult)
	assert.Equal("关注成功", resp.Msg)
	assert.Equal(int64(1), resp.AttentionID)
	assert.Equal(1, resp.Type)
}

func TestChangeAttentionLoad(t *testing.T) {
	assert := assert.New(t)

	var param ChangeAttentionParam
	input := handler.M{"attention_id": "string"}
	c := handler.NewTestContext(http.MethodPost, "/user/changeattention", false, input)
	assert.Equal(actionerrors.ErrParams, param.load(c))
	input = handler.M{}
	c = handler.NewTestContext(http.MethodPost, "/user/changeattention", false, input)
	assert.EqualError(param.load(c), "输入参数不可为空")
	input = handler.M{
		"attention":    3,
		"user_id":      10,
		"attention_id": 12,
	}
	c = handler.NewTestContext(http.MethodPost, "/user/changeattention", false, input)
	assert.Equal(actionerrors.ErrParams, param.load(c))
	input = handler.M{
		"attention":    1,
		"user_id":      10,
		"attention_id": 10,
	}
	c = handler.NewTestContext(http.MethodPost, "/user/changeattention", false, input)
	assert.EqualError(param.load(c), "不能关注自己")
	input = handler.M{
		"attention":    1,
		"user_id":      123456789,
		"attention_id": 10,
	}
	c = handler.NewTestContext(http.MethodPost, "/user/changeattention", false, input)
	assert.Equal(actionerrors.ErrCannotFindUser, param.load(c))
	input = handler.M{
		"attention":    1,
		"user_id":      12,
		"attention_id": 10,
	}
	c = handler.NewTestContext(http.MethodPost, "/user/changeattention", false, input)
	assert.NoError(param.load(c))
}
