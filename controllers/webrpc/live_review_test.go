package webrpc

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/mysql/livereview"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestReviewingRoomTagKey(t *testing.T) {
	assert := assert.New(t)
	var r reviewingRoom
	assert.Empty(tutil.KeyExists(tutil.JSON, r, "room_id", "creator_id",
		"name", "cover_url", "background"))
}

func TestActionReviewSetAndGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	now := goutil.TimeNow()
	imageURL := "https://static-test.missevan.com/test.png"
	param := reviewingRoom{
		RoomID:    369892,
		CreatorID: 892,
		Name:      "测试审核",
		CoverURL:  &imageURL,
		Background: &livereview.ReviewInfo{
			ImageURL: imageURL,
		},
	}
	ctx := handler.CreateTestContext(false)
	ctx.C.Request, _ = http.NewRequest("POST", "/rpc/chatroom/image/set", tutil.ToRequestBody(param))
	_, err := ActionReviewSet(ctx)
	require.NoError(err)
	defer service.DB.Table(livereview.TableName()).Delete("", "user_id = ? ", param.CreatorID)
	var lrs []*livereview.LiveReview
	require.NoError(service.DB.Find(&lrs, "user_id = ? AND status = ?", param.CreatorID, livereview.StatusReviewing).Error)
	require.Len(lrs, 3)
	for i := range lrs {
		assert.GreaterOrEqual(lrs[i].UploadTime, now.Unix())
		switch lrs[i].Type {
		case livereview.TypeName:
			assert.NotEmpty(lrs[i].Name)
		default:
			assert.NotEmpty(lrs[i].ImageURL)
		}
	}

	// 获取房间审核信息
	input := map[string]interface{}{"room_id": 369892}
	c := handler.NewTestContext("POST", "/rpc/chatroom/review/get", false, input)
	r, err := ActionReviewGet(c)
	require.NoError(err)
	resp := r.(*reviewingInfo)
	assert.Equal(imageURL, *resp.CoverURL)
	assert.Equal(imageURL, resp.Background.ImageURL)
	require.NotNil(resp.Background.Opacity)
	assert.Equal(float64(1.0), *resp.Background.Opacity)
	assert.Equal(param.Name, *resp.Name)

	input["room_id"] = 1234567890
	c = handler.NewTestContext("POST", "/rpc/chatroom/review/get", false, input)
	r, err = ActionReviewGet(c)
	require.NoError(err)
	resp = r.(*reviewingInfo)
	assert.True(resp.CoverURL == nil && resp.Name == nil && resp.Background == nil)
}
