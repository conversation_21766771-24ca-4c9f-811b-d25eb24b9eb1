package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
)

func TestActionGiftNotify(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext("POST", "/util/giftnotify", false, nil)
	_, err := ActionGiftNotify(c)
	assert.Equal(actionerrors.ErrParams, err)

	var (
		userID       int64 = 3456835
		existsRoomID int64 = 18113499
	)

	ok := false
	cancel := mrpc.SetMock("im://broadcast/all", func(input interface{}) (output interface{}, err error) {
		ok = true
		return "success", nil
	})
	defer cancel()

	input := handler.M{"gift_id": 1, "num": 1, "from_user_id": userID, "room_id": existsRoomID, "has_effect": true}
	c = handler.NewTestContext("POST", "/util/giftnotify", false, input)
	_, err = ActionGiftNotify(c)
	require.NoError(err)
	assert.True(ok)
}
