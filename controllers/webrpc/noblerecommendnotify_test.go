package webrpc

import (
	"encoding/json"
	"testing"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionNobleRecommendNotify(t *testing.T) {
	require := require.New(t)

	c := handler.NewTestContext("POST", "/", true, nil)
	_, err := ActionNobleRecommendNotify(c)
	require.NoError(err)
}

func TestNewRecommendPayload(t *testing.T) {
	assert := assert.New(t)

	n := newRecommendPayload(123)
	assert.Equal(int64(123), n.RoomID)
	assert.Equal("room", n.Type)
	assert.Equal("recommend", n.Event)
}

func TestFindRecommend(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	// TODO: 等待切换成 sqlite 添加测试数据
	when := goutil.TimeNow().AddDate(-10, 1, 1)
	re, err := findRecommend(when)
	require.NoError(err)
	assert.Empty(re)
}

func TestSendNotify(t *testing.T) {
	require := require.New(t)
	assert := assert.New(t)

	var expireRc, startRc []*livenoblerecommend.NobleRecommend
	// 当前无神话推荐
	require.NotPanics(func() { sendNotify(expireRc, startRc) })

	// 当前神话推荐房间连续，无通知发送
	expireRc = append(expireRc, &livenoblerecommend.NobleRecommend{RoomID: 1})
	require.NotPanics(func() { sendNotify(expireRc, expireRc) })

	// 发送过期消息
	require.NotPanics(func() { sendNotify(expireRc, startRc) })

	// 发送开始消息
	startRc = append(expireRc, &livenoblerecommend.NobleRecommend{RoomID: 2, FromUserID: 3456835})
	require.NotPanics(func() { sendNotify(expireRc, startRc) })
	// 发送开始神话推荐通知
	keys := []string{
		keys.KeyIMPubSub2.Format(2, 0), // 测试数据库上的配置
		keys.KeyIMPubSub2.Format(2, 1),
	}
	watcher := imRedis.Subscribe(keys...)
	defer watcher.Close()
	ch := watcher.Channel()
	sendNotify(expireRc, startRc)
	// 接收消息
	f := func(m *redis.Message) (bool, error) {
		var rec struct {
			Payload recommendPayload `json:"payload"`
		}
		_ = json.Unmarshal([]byte(m.Payload), &rec)
		logger.Debug(m.Payload)
		if rec.Payload.Type == "room" && rec.Payload.Event == "recommend" &&
			rec.Payload.RoomID == int64(2) && rec.Payload.Recommender.Username == "我的力量无人能及" {
			return true, nil
		}
		return false, nil
	}
	assert.NoError(receiveMessage(ch, f))
}

func TestCheckContinueRoom(t *testing.T) {
	assert := assert.New(t)

	var firstRc, secondRc []*livenoblerecommend.NobleRecommend
	firstRc = []*livenoblerecommend.NobleRecommend{{RoomID: 1}}
	r := checkContinueRoom(firstRc, secondRc)
	assert.Len(r, 1)

	firstRc = []*livenoblerecommend.NobleRecommend{
		{RoomID: 1},
		{RoomID: 5},
		{RoomID: 3},
		{RoomID: 4},
		{RoomID: 2},
		{RoomID: 6},
	}
	secondRc = []*livenoblerecommend.NobleRecommend{
		{RoomID: 2},
		{RoomID: 3},
		{RoomID: 4},
		{RoomID: 5},
		{RoomID: 7},
		{RoomID: 8},
	}

	r = checkContinueRoom(firstRc, secondRc)
	assert.Len(r, 2)
	assert.Equal(int64(1), r[0].RoomID)
	assert.Equal(int64(6), r[1].RoomID)
}
