package webrpc

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/contractapplyment"
	"github.com/MiaoSiLa/live-service/models/guild/guild"
	"github.com/MiaoSiLa/live-service/models/guild/guildbalance"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/mysql/guildagent"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/models/messageassign"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionGuildCreatorTerminateForcely(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 参数错误
	ctx := handler.NewTestContext(http.MethodPost, "/rpc/guild/creator-terminate-forcely", false, map[string]interface{}{
		"applyment_id": -1,
	})
	_, err := ActionGuildCreatorTerminateForcely(ctx)
	require.EqualError(err, actionerrors.ErrParams.Message)

	// 未找到合约申请
	ctx = handler.NewTestContext(http.MethodPost, "/rpc/guild/creator-terminate-forcely", false, map[string]interface{}{
		"applyment_id": 987654321,
	})
	_, err = ActionGuildCreatorTerminateForcely(ctx)
	require.EqualError(err, actionerrors.ErrApplymentNotExist.Message)

	contract := livecontract.LiveContract{
		GuildID:          66,
		GuildOwner:       333,
		GuildName:        "测试公会",
		LiveID:           15,
		ContractDuration: contractapplyment.ContractDurationSixMonths,
		ContractStart:    util.TimeNow().AddDate(0, 0, -90).Unix(),
		ContractEnd:      util.TimeNow().AddDate(0, 0, 90).Unix(),
		Rate:             45,
		KPI:              "",
		Type:             livecontract.FromLiver,
		Status:           livecontract.StatusContracting,
	}
	require.NoError(contract.DB().Create(&contract).Error)
	defer func() {
		require.NoError(contract.DB().Delete(nil, "id = ?", contract.ID).Error)
	}()

	applyment := contractapplyment.ContractApplyment{
		GuildID:            contract.GuildID,
		LiveID:             contract.LiveID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   contract.ContractDuration,
		ContractExpireTime: contract.ContractEnd,
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               45,

		Type:       contractapplyment.TypeGuildExpel,
		Status:     contractapplyment.StatusInvalid,
		ExpireTime: util.TimeNow().Add(-time.Hour).Unix(),
	}
	require.NoError(applyment.DB().Create(&applyment).Error)
	defer func() {
		require.NoError(applyment.DB().Delete(nil, "id = ?", applyment.ID).Error)
	}()

	application := contractapplyment.ContractApplyment{
		GuildID:            contract.GuildID,
		LiveID:             contract.LiveID,
		GuildName:          contract.GuildName,
		ContractID:         contract.ID,
		ContractDuration:   contract.ContractDuration,
		ContractExpireTime: contract.ContractEnd,
		Initiator:          contractapplyment.InitiatorLive,
		Rate:               45,

		Type:       contractapplyment.TypeRateDown,
		Status:     contractapplyment.StatusPending,
		ExpireTime: util.TimeNow().Add(-time.Hour).Unix(),
	}
	require.NoError(application.DB().Create(&application).Error)
	defer func() {
		require.NoError(application.DB().Delete(nil, "id = ?", application.ID).Error)
	}()

	gb := guildbalance.GuildBalance{
		ID: contract.GuildID,
	}
	require.NoError(gb.DB().Create(&gb).Error)
	defer func() {
		require.NoError(gb.DB().Delete(nil, "id = ?", gb.ID).Error)
	}()

	agentCreator := guildagent.AgentCreator{
		GuildID:   contract.GuildID,
		CreatorID: contract.LiveID,
		AgentID:   8888,
	}
	require.NoError(agentCreator.DB().Create(&agentCreator).Error)
	defer func() {
		require.NoError(agentCreator.DB().Delete(nil, "id = ?", agentCreator.ID).Error)
	}()

	// 合约申请类型错误
	ctx = handler.NewTestContext(http.MethodPost, "/rpc/guild/creator-terminate-forcely", false, map[string]interface{}{
		"applyment_id": applyment.ID,
	})
	_, err = ActionGuildCreatorTerminateForcely(ctx)
	require.EqualError(err, actionerrors.ErrApplymentType.Message)
	require.NoError(applyment.DB().Where("id = ?", applyment.ID).Update("type", contractapplyment.TypeLiveTerminateForcely).Error)

	// 合约已过期
	ctx = handler.NewTestContext(http.MethodPost, "/rpc/guild/creator-terminate-forcely", false, map[string]interface{}{
		"applyment_id": applyment.ID,
	})
	_, err = ActionGuildCreatorTerminateForcely(ctx)
	require.EqualError(err, actionerrors.ErrApplymentFreezed.Message)
	require.NoError(applyment.DB().Where("id = ?", applyment.ID).Update("status", contractapplyment.StatusPending).Error)

	// 正常情况
	now := util.TimeNow()
	util.SetTimeNow(func() time.Time {
		return now
	})
	defer func() {
		util.SetTimeNow(nil)
	}()
	nowStamp := now.Unix()
	require.NoError(room.UpdateGuildID(contract.LiveID, contract.GuildID))
	ctx = handler.NewTestContext(http.MethodPost, "/rpc/guild/creator-terminate-forcely", false, map[string]interface{}{
		"applyment_id": applyment.ID,
	})
	resp, err := ActionGuildCreatorTerminateForcely(ctx)
	require.NoError(err)
	ok := resp.(bool)
	assert.True(ok)
	defer func() {
		require.NoError(service.DB.Table(messageassign.TableName()).Delete(nil, "recuid = ?", contract.GuildOwner).Error)
	}()

	require.NoError(applyment.DB().First(&applyment, "id = ?", applyment.ID).Error)
	assert.Equal(contractapplyment.StatusAgreed, applyment.Status)
	assert.Equal(nowStamp, applyment.ContractExpireTime)
	assert.Equal(nowStamp, applyment.ProcessTime)
	assert.Equal(nowStamp, applyment.ModifiedTime)

	require.NoError(application.DB().First(&application, "id = ?", application.ID).Error)
	assert.Equal(contractapplyment.StatusInvalid, application.Status)

	require.NoError(contract.DB().First(&contract, "id = ?", contract.ID).Error)
	assert.Equal(livecontract.StatusFinished, contract.Status)
	assert.Equal(nowStamp, contract.ContractEnd)
	assert.Equal(nowStamp, contract.ModifiedTime)

	var g guild.Guild
	require.NoError(guild.Guild{}.DB().First(&g, "id = ?", gb.ID).Error)
	assert.EqualValues(9, g.LiveNum)
	assert.Equal(nowStamp, gb.ModifiedTime)

	require.NoError(agentCreator.DB().First(&agentCreator, "id = ?", agentCreator.ID).Error)
	assert.Equal(nowStamp, agentCreator.DeleteTime)
	assert.Equal(nowStamp, agentCreator.ModifiedTime)

	r, err := room.FindOne(bson.M{"creator_id": contract.LiveID})
	require.NoError(err)
	require.NotNil(r)
	assert.Zero(r.GuildID)

	var sysMsg messageassign.MessageAssign
	require.NoError(service.DB.First(&sysMsg, "recuid = ?", contract.GuildOwner).Error)
	assert.Equal("主播已与您解约", sysMsg.Title)
	assert.Equal(nowStamp, sysMsg.Time)
}
