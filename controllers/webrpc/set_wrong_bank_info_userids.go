package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

const (
	operateWrongBankInfoTypeAdd uint8 = iota
	operateWrongBankInfoTypeDel
	operateWrongBankInfoTypeClear
)

type operateWrongBankInfoUserIDList struct {
	UserIDs []int64 `json:"user_ids"`
	Type    uint8   `json:"type"`

	members []interface{}
}

// ActionSetWrongBankInfoUserIDs 操作银行信息有误的用户 ID 列表
/**
 * @api {post} /rpc/util/set-wrong-bank-info-userids
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number[]} user_ids 用户 ID
 * @apiParam {number=0,1,2} [type=0] 操作类型（0 添加，1 移除，2 清空全部）
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": 3
 *     }
 */
func ActionSetWrongBankInfoUserIDs(ctx *handler.Context) (handler.ActionResponse, error) {
	var (
		param operateWrongBankInfoUserIDList
		err   error
	)
	if err = ctx.BindJSON(&param); err != nil ||
		param.Type > operateWrongBankInfoTypeClear ||
		(len(param.UserIDs) == 0 && param.Type != operateWrongBankInfoTypeClear) {
		return nil, actionerrors.ErrParams
	}

	if length := len(param.UserIDs); length != 0 {
		param.members = make([]interface{}, length)
		for i := 0; i < length; i++ {
			param.members[i] = param.UserIDs[i]
		}
	}

	// TODO: 之后将数据挪到 MySQL 中
	var affectedNum int64
	switch param.Type {
	case operateWrongBankInfoTypeAdd:
		affectedNum, err = service.Redis.SAdd(keys.KeyUserIDsWrongBankInfo0.Format(), param.members...).Result()
	case operateWrongBankInfoTypeDel:
		affectedNum, err = service.Redis.SRem(keys.KeyUserIDsWrongBankInfo0.Format(), param.members...).Result()
	case operateWrongBankInfoTypeClear:
		affectedNum, err = service.Redis.SCard(keys.KeyUserIDsWrongBankInfo0.Format()).Result()
		if err == nil {
			_, err = service.Redis.Del(keys.KeyUserIDsWrongBankInfo0.Format()).Result()
		}
	}
	if err != nil {
		err = actionerrors.NewErrServerInternal(err, nil)
	}

	return affectedNum, err
}
