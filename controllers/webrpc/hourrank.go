package webrpc

import (
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionNotifyHourRank 小时榜变化后下发通知
/**
 * @api {post} /rank/notifyhourrank 小时榜变化后下发通知
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} user_id 主播的用户 ID
 * @apiParam {Number} prev_rank 用户之前的在小时榜上的排名，如果之前的小时榜排名大于 10，则表明之前未上榜。0 表示用户之前没有上榜且用户之前 score 为 0
 * @apiParam {Number} rank 用户当前在小时榜上的排名，这里要给出具体的排名而不是用 0 表示未上榜
 * @apiParam {Number} score 收礼之后当前用户的分数
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 */
func ActionNotifyHourRank(c *handler.Context) (handler.ActionResponse, error) {
	var param usersrank.RankChange
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID == 0 || param.Rank <= 0 || param.PrevRank < 0 {
		return nil, actionerrors.ErrParams
	}
	if param.PrevRank != 0 && param.PrevRank < param.Rank {
		// 如果 PrevRank 非 0，则 PrevRank 不可以小于当前 Rank，即当前排名不能比之前排名低
		return nil, actionerrors.ErrParams
	}

	r, err := room.FindOne(bson.M{"creator_id": param.UserID}, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	err = room.NotifyHourRank(&param, r)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return "success", nil
}
