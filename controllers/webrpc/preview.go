package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreview"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreviewuserreservation"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/util"
)

// previewParam 获取直播预告参数
type previewParam struct {
	ElementType int   `json:"element_type"`
	ElementID   int64 `json:"element_id"`
	UserID      int64 `json:"user_id"`

	preview           *livepreview.LivePreview
	live              *live.Live
	reservationStatus int
}

// previewResp 直播预告信息
type previewResp struct {
	ID                int64  `json:"id"`
	Title             string `json:"title"`
	LiveStartTime     int64  `json:"live_start_time"`
	LiveScheduleTime  int64  `json:"live_schedule_time"`
	RoomID            int64  `json:"room_id"`
	CreatorID         int64  `json:"creator_id"`
	LiveStatus        int    `json:"live_status"`
	ReservationStatus *int   `json:"reservation_status,omitempty"`
}

// ActionPreviewGet 获取直播预告
/**
 * @api {post} /rpc/preview/get 获取直播预告
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {number=1} element_type 预告关联元素类型。1：剧集直播预告
 * @apiParam {Number} element_id 预告关联元素 ID
 * @apiParam {Number} [user_id=0] 用户 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": { // 若无预告时返回 null
 *         "id": 1, // 直播预告 ID
 *         "title": "卡片标题",
 *         "live_start_time": 1703001600, // 开播时间点。单位：秒
 *         "live_schedule_time": 1703000000, // 预计开播时间点。单位：秒
 *         "room_id": 233, // 直播间 ID
 *         "creator_id": 11, // 主播 ID
 *         "live_status": 0, // 直播间状态。0：未开播；1：直播中
 *         "reservation_status": 1 // 预约直播状态；0: 未预约直播，1: 已预约直播。未登录用户不返回
 *       }
 *     }
 */
func ActionPreviewGet(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPreviewParam(c)
	if err != nil {
		return nil, err
	}
	if param.preview == nil {
		return nil, nil
	}
	if param.live == nil {
		logger.WithFields(logger.Fields{
			"preview_id": param.preview.ID,
			"room_id":    param.preview.RoomID,
		}).Error("直播预告配置的直播间不存在")
		return nil, nil
	}
	resp := previewResp{
		ID:               param.preview.ID,
		Title:            param.preview.Title,
		LiveStartTime:    param.preview.LiveStartTime,
		LiveScheduleTime: param.preview.LiveScheduleTime,
		RoomID:           param.preview.RoomID,
		CreatorID:        param.live.UserID,
		LiveStatus:       param.live.Status,
	}
	if param.UserID > 0 {
		resp.ReservationStatus = &param.reservationStatus
	}
	return resp, nil
}

func newPreviewParam(c *handler.Context) (*previewParam, error) {
	var param previewParam
	err := c.BindJSON(&param)
	if err != nil || param.ElementType <= 0 || param.ElementID <= 0 || param.UserID < 0 {
		return nil, actionerrors.ErrParams
	}
	// 查询直播预告
	param.preview, err = livepreview.FindPreview(param.ElementType, param.ElementID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.preview == nil {
		return &param, nil
	}
	if param.UserID > 0 {
		// 获取直播预约状态
		exists, err := livepreviewuserreservation.Exists(param.preview.ID, param.UserID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		param.reservationStatus = util.BoolToInt(exists)
	}
	// 获取直播间信息
	param.live, err = live.FindLiveByRoomID(param.preview.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return &param, nil
}
