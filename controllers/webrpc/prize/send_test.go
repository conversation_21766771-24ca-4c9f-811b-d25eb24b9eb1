package prize

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/internal/biz/prize"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionPrizeSend(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		validRoomID  = int64(100000006)
		validPrizeID = int64(3)
		now          = goutil.TimeNow()
		testUserID   = int64(223231)
	)

	// 测试场景 1：成功发送奖品到房间 - 跳过实际发送过程
	t.Run("Success Validation", func(t *testing.T) {
		// 准备测试参数
		appearances, err := appearance.Find(
			bson.M{
				"start_time":  bson.M{"$lte": now.Unix()},
				"expire_time": bson.M{"$not": bson.M{"$lte": now.Unix()}},
			},
			&options.FindOptions{Limit: goutil.NewInt64(2)},
		)
		require.NoError(err)
		require.Len(appearances, 2)
		appearanceIDs := goutil.SliceMap(appearances, func(a *appearance.Appearance) int64 {
			return a.ID
		})
		_, err = userappearance.Collection().DeleteMany(
			context.Background(),
			bson.M{
				"user_id":       testUserID,
				"appearance_id": bson.M{"$in": appearanceIDs},
			},
		)
		require.NoError(err)
		err = liveprize.DB().Delete(&liveprize.PrizeLog{}).Error
		require.NoError(err)

		_, err = appearance.Collection().DeleteMany(
			context.Background(),
			bson.M{
				"id": 1011,
			},
		)
		require.NoError(err)

		_, err = appearance.Collection().InsertOne(
			context.Background(),
			bson.M{
				"id":              1011,
				"name":            "粉梦星辉",
				"type":            1,
				"from":            1,
				"intro":           "2024 直播盛典",
				"icon":            "oss://live/vehicles/icons/20138.png",
				"effect":          "oss://live/vehicles/effects/v2/20138.mp4;oss://live/vehicles/effects/v2/20138.png",
				"web_effect":      "oss://live/vehicles/effects/v2/20138-web.mp4;oss://live/vehicles/effects/v2/20138-web.webm;oss://live/vehicles/effects/v2/20138-web.png",
				"effect_duration": 3000,
				"message_bar": bson.M{
					"message": "<font color=\"#FC577C\">${username}</font><font color=\"#833586\">乘坐</font><font color=\"#FC577C\">${vehicle_name}</font><font color=\"#833586\">来了</font>",
					"image":   "oss://live/vehicles/messagebars/v20138_0_148_0_184.png",
				},
				"start_time":    now.Unix(),
				"modified_time": now.Unix(),
			},
		)
		require.NoError(err)

		invalidParam := handler.M{
			"user_id": 1,
			"room_id": validRoomID,
			"prizes": []handler.M{
				{
					"prize_id": validPrizeID,
					"biz": handler.M{
						"biz_type": liveprize.BizGiftUpgrade,
						"biz_id":   "11",
					},
				},
			},
		}

		// 创建测试 Context，直接传入参数
		ctx := handler.NewRPCTestContext("/rpc/live-service/prize/send", invalidParam)

		resp, msg, err := ActionPrizeSend(ctx)
		require.NoError(err)
		assert.Nil(resp)
		assert.Equal("success", msg)
	})

	// 测试场景 2：参数无效 - 缺少 userID 或 roomID
	t.Run("Invalid ReceiverType", func(t *testing.T) {
		// 准备测试参数
		invalidParam := handler.M{
			"prizes": []handler.M{
				{
					"prize_id": validPrizeID,
					"biz": handler.M{
						"biz_type": liveprize.BizGiftUpgrade,
						"biz_id":   "11",
					},
				},
			},
		}

		// 创建测试 Context，直接传入参数
		ctx := handler.NewTestContext("POST", "", true, invalidParam)

		resp, msg, err := ActionPrizeSend(ctx)
		assert.Error(err)
		assert.Nil(resp)
		assert.Equal("", msg)
		assert.EqualError(err, "缺少用户 ID")
	})

	// 测试场景 3：参数无效 - ID非法
	t.Run("Invalid IDs", func(t *testing.T) {
		// 准备测试参数
		invalidParam := handler.M{
			"room_id": -1,
			"prizes": []handler.M{
				{
					"prize_id": validPrizeID,
				},
			},
		}

		// 创建测试 Context，直接传入参数
		ctx := handler.NewTestContext("POST", "", true, invalidParam)

		resp, msg, err := ActionPrizeSend(ctx)
		assert.Error(err)
		assert.Nil(resp)
		assert.Equal("", msg)
		assert.EqualError(err, "缺少用户 ID")
	})
}

func TestNewSendPrizeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testRoomID  = int64(100000006)
		testPrizeID = int64(3)
	)

	p := handler.M{
		"user_id": 1,
		"room_id": testRoomID,
		"prizes": []handler.M{
			{
				"prize_id": testPrizeID,
				"biz": handler.M{
					"biz_type": liveprize.BizGiftUpgrade,
					"biz_id":   "test_event",
				},
			},
		},
	}

	c := handler.NewTestContext(http.MethodPost, "/", true, p)

	param, err := newSendPrizeParam(c)
	require.NoError(err)

	assert.Equal(testRoomID, param.RoomID)
	assert.Equal(1, len(param.Prizes))
	assert.Equal(testPrizeID, param.Prizes[0].PrizeID)
	require.NotNil(param.Prizes[0].Biz)
	assert.Equal(1, param.Prizes[0].Biz.BizType)
	assert.Equal("test_event", param.Prizes[0].Biz.BizID)
}

func TestSendPrizeParam_build(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID  = int64(223231)
		testPrizeID = int64(3)
	)

	p := &sendPrizeParam{
		UserID: testUserID,
		Prizes: []prizeInfo{
			{
				PrizeID: testPrizeID,
			},
		},
	}
	err := p.build()
	require.NoError(err)

	assert.Equal(testUserID, p.UserID)
	assert.Equal(1, len(p.Prizes))
	assert.Equal(testPrizeID, p.Prizes[0].PrizeID)
}

func TestSendPrizeParam_buildPrizeDistributors(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var (
		testUserID   = int64(223231)
		testPrizeID1 = int64(3)
		testPrizeID2 = int64(103)
	)

	p := &sendPrizeParam{
		UserID: testUserID,
		Prizes: []prizeInfo{
			{
				PrizeID: testPrizeID1,
			},
		},
	}
	distributors, err := p.buildPrizeDistributors()
	require.NoError(err)

	assert.Equal(1, len(distributors))
	assert.Equal(testUserID, distributors[0].ReceiverID())
	assert.Equal("user", distributors[0].ReceiverType().String())
	assert.Equal(testPrizeID1, distributors[0].Prize().ID)

	p2 := &sendPrizeParam{
		UserID: testUserID,
		Prizes: []prizeInfo{
			{
				PrizeID: testPrizeID2,
			},
		},
	}

	distributors, err = p2.buildPrizeDistributors()
	require.NoError(err)
	assert.Equal(0, len(distributors))
}

func TestSendPrizeParam_buildPrizePackageDistributors(t *testing.T) {
	// PASS
}

func TestSendPrizeParam_send(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)
	var (
		now        = goutil.TimeNow()
		testUserID = int64(223231)
	)
	appearances, err := appearance.Find(
		bson.M{
			"start_time":  bson.M{"$lte": now.Unix()},
			"expire_time": bson.M{"$not": bson.M{"$lte": now.Unix()}},
		},
		&options.FindOptions{Limit: goutil.NewInt64(1)},
	)
	require.NoError(err)
	require.Len(appearances, 1)
	appearanceIDs := goutil.SliceMap(appearances, func(a *appearance.Appearance) int64 {
		return a.ID
	})
	_, err = userappearance.Collection().DeleteMany(
		context.Background(),
		bson.M{
			"user_id":       testUserID,
			"appearance_id": bson.M{"$in": appearanceIDs},
		},
	)
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.PrizeLog{}).Error
	require.NoError(err)
	err = liveprize.DB().Delete(&liveprize.Prize{}).Error
	require.NoError(err)
	p := &liveprize.Prize{
		ID:          1,
		Type:        liveprize.TypeUserAppearance,
		ElementID:   appearances[0].ID,
		ElementType: appearances[0].Type,
		Num:         1,
		Duration:    int64(10 * time.Hour.Seconds()),
	}

	logs, err := prize.Send(
		[]prize.Distributor{
			prize.NewUserDistributor(testUserID, p, prize.WithBiz(liveprize.BizGiftUpgrade, 11)),
		},
	)
	require.NoError(err)
	require.Len(logs, 1)

	ua, err := userappearance.Find(bson.M{
		"user_id":       testUserID,
		"appearance_id": bson.M{"$in": appearanceIDs},
	}, nil)
	require.NoError(err)
	require.Len(ua, 1)
	uaMap := util.ToMap(ua, func(a *userappearance.UserAppearance) int64 {
		return a.AppearanceID
	})
	require.NotNil(uaMap[appearances[0].ID])
	assert.LessOrEqual(now.Add(time.Duration(p.Duration)*time.Second).Unix(), *uaMap[appearances[0].ID].ExpireTime)
}
