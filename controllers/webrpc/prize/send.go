package prize

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/internal/biz/prize"
	"github.com/MiaoSiLa/live-service/models/livedb/liveprize"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type sendPrizeParam struct {
	UserID        int64              `json:"user_id"`        // 用户 ID
	RoomID        int64              `json:"room_id"`        // 房间 ID
	Prizes        []prizeInfo        `json:"prizes"`         // 奖品列表（与 PrizePackages 二选一）
	PrizePackages []prizePackageInfo `json:"prize_packages"` // 奖品包列表（与 Prizes 二选一）

	distributors []prize.Distributor
}

type prizeInfo struct {
	PrizeID int64    `json:"prize_id"` // 奖品 ID
	Biz     *bizInfo `json:"biz"`      // 业务信息（可选）
}

type prizePackageInfo struct {
	PrizePackageID int64    `json:"prize_package_id"` // 奖品包 ID
	Biz            *bizInfo `json:"biz"`              // 业务信息（可选）
}

type bizInfo struct {
	BizType int    `json:"biz_type"` // 业务类型
	BizID   string `json:"biz_id"`   // 业务 ID
}

// ActionPrizeSend 发送奖品 RPC
/**
 * @api {post} /rpc/live-service/prize/send 发送奖品
 * @apiDescription 向指定用户或房间发送奖品
 * @apiName send
 * @apiGroup webrpc
 * @apiVersion 1.0.0
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} room_id 房间 ID
 * @apiParam {Number[]} [prizes] 奖品列表（与 prize_packages 二选一）
 * @apiParam {Number[]} [prize_packages] 奖品包列表（与 prizes 二选一）
 *
 * @apiParamExample {json} prize 请求示例:
 *   {
 *     "user_id": 223344,
 *     "room_id": 223344,
 *     "prizes": [
 *       {
 *         "prize_id": 1001,
 *         "biz": {
 *           "biz_type": 1, // 业务类型 1: 礼物升级 2: 主播任务
 *           "biz_id": "activity_123" // 业务 ID
 *         }
 *       },
 *       // ...
 *     ]
 *   }
 *
 * @apiParamExample {json} prize_packages 请求示例:
 *   {
 *     "user_id": 223344,
 *     "room_id": 223344,
 *     "prize_packages": [
 *       {
 *         "prize_package_id": 1001,
 *         "biz": {
 *           "biz_type": 1, // 业务类型 1: 礼物升级 2: 主播任务
 *           "biz_id": "activity_123" // 业务 ID
 *         }
 *       },
 *       // ...
 *     ],
 *   }
 *
 * @apiSuccessExample {json} 成功响应:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 */
func ActionPrizeSend(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newSendPrizeParam(c)
	if err != nil {
		return nil, "", err
	}

	if err := param.build(); err != nil {
		return nil, "", err
	}
	if err := param.send(); err != nil {
		return nil, "", err
	}

	return nil, "success", nil
}

// newSendPrizeParam 创建发送奖品参数并进行校验
func newSendPrizeParam(c *handler.Context) (*sendPrizeParam, error) {
	var param sendPrizeParam
	if err := c.BindJSON(&param); err != nil {
		return nil, actionerrors.ErrParams
	}

	// 检查奖品或奖品包必须指定一个
	if (len(param.Prizes) == 0 && len(param.PrizePackages) == 0) || (len(param.Prizes) > 0 && len(param.PrizePackages) > 0) {
		return nil, actionerrors.ErrParams
	}

	return &param, nil
}

func (param *sendPrizeParam) build() error {
	var err error
	switch {
	case len(param.Prizes) > 0:
		param.distributors, err = param.buildPrizeDistributors()
	case len(param.PrizePackages) > 0:
		param.distributors, err = param.buildPrizePackageDistributors()
	default:
		return actionerrors.ErrParams
	}
	if err != nil {
		return err
	}
	return nil
}

func (param *sendPrizeParam) buildPrizeDistributors() ([]prize.Distributor, error) {
	prizeIDs := goutil.SliceMap(param.Prizes, func(p prizeInfo) int64 {
		return p.PrizeID
	})
	prizes, err := liveprize.FindPrizes(prizeIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	prizeMap := util.ToMap(prizes, func(p *liveprize.Prize) int64 {
		return p.ID
	})
	distributors := make([]prize.Distributor, 0, len(param.Prizes))
	for _, pp := range param.Prizes {
		p, ok := prizeMap[pp.PrizeID]
		if !ok {
			logger.Errorf("奖品不存在，prize_id: %d", pp.PrizeID)
			// PASS
			continue
		}
		var opts []prize.Option
		if pp.Biz != nil {
			opts = append(opts, prize.WithBiz(pp.Biz.BizType, pp.Biz.BizID))
		}
		switch p.Type {
		case liveprize.TypeUserAppearance, liveprize.TypeCreatorAppearance, liveprize.TypeBackpack, liveprize.TypeCreatorBackpack:
			if param.UserID <= 0 {
				return nil, actionerrors.NewErrForbidden("缺少用户 ID")
			}
			distributors = append(distributors, prize.NewUserDistributor(param.UserID, p, opts...))
		case liveprize.TypeLiveTag, liveprize.TypeRoomGiftCustom, liveprize.TypeQuest:
			if param.RoomID <= 0 {
				return nil, actionerrors.NewErrForbidden("缺少房间 ID")
			}
			distributors = append(distributors, prize.NewRoomDistributor(param.RoomID, p, opts...))
		case liveprize.TypeSticker:
			// 表情类型奖品需要根据 element type 来确定是给用户还是直播间发送
			switch p.ElementType {
			case livesticker.TypeUser:
				if param.UserID <= 0 {
					return nil, actionerrors.NewErrForbidden("缺少用户 ID")
				}
				distributors = append(distributors, prize.NewUserDistributor(param.UserID, p, opts...))
			case livesticker.TypeRoom:
				if param.RoomID <= 0 {
					return nil, actionerrors.NewErrForbidden("缺少房间 ID")
				}
				distributors = append(distributors, prize.NewRoomDistributor(param.RoomID, p, opts...))
			default:
				return nil, actionerrors.NewErrForbidden("不支持的表情类型")
			}
		default:
			return nil, actionerrors.NewErrForbidden("不支持的奖品类型")
		}
	}
	return distributors, nil
}

// TODO: 支持 prize_package_id 的逻辑
func (param *sendPrizeParam) buildPrizePackageDistributors() ([]prize.Distributor, error) {
	panic("not implemented")
}

// send 执行发送奖品
func (param *sendPrizeParam) send() error {
	_, err := prize.Send(param.distributors)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}
