package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

func TestActionLiveRecommendInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewRPCTestContext("/rpc/live/recommend-info", nil)
	_, err := ActionLiveRecommendInfo(c)
	assert.Equal(actionerrors.ErrParams, err)

	testCreatorIDs := []int64{9074509, 9074510}
	c = handler.NewRPCTestContext("/rpc/live/recommend-info", handler.M{
		"creator_ids": testCreatorIDs,
	})
	resp, err := ActionLiveRecommendInfo(c)
	require.NoError(err)
	require.NotEmpty(resp)
	r := resp.(handler.M)
	require.NotEmpty(r)
	data, ok := r["data"].(map[int64]*recommendInfoRoom)
	require.True(ok)
	testData1, ok := data[testCreatorIDs[0]]
	require.True(ok)
	assert.Equal(testCreatorIDs[0], testData1.UserID)
	assert.NotEmpty(testData1.Title)
	assert.NotEmpty(testData1.CatalogID)
	assert.NotEmpty(testData1.CatalogName)
	assert.NotEmpty(testData1.CoverURL)
	require.NotNil(testData1.CustomTag)
	assert.EqualValues(10001, testData1.CustomTag.TagID)
	assert.EqualValues("test10001", testData1.CustomTag.TagName)
	testData2, ok := data[testCreatorIDs[1]]
	require.True(ok)
	assert.Equal(testCreatorIDs[1], testData2.UserID)
	assert.Nil(testData2.CustomTag)

	// 测试旧版本分区下发个性词条
	c = handler.NewRPCTestContext("/rpc/live/recommend-info", handler.M{
		"creator_ids": testCreatorIDs,
		"is_old":      true,
	})
	resp, err = ActionLiveRecommendInfo(c)
	require.NoError(err)
	require.NotEmpty(resp)
	r = resp.(handler.M)
	require.NotEmpty(r)
	data, ok = r["data"].(map[int64]*recommendInfoRoom)
	require.True(ok)
	testData3, ok := data[testCreatorIDs[0]]
	require.True(ok)
	require.NotNil(testData1.CustomTag)
	assert.Equal(testData3.CustomTag.TagName, testData3.CatalogName)
}
