package webrpc

import (
	"slices"
	"sort"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config/params"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/attentionuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mysql/liveextrabanner"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// maxLiveExtraBannersLength 每个位置返回的直播广告通栏最大数量
const maxLiveExtraBannersLength = 3

type liveExtraBannersParam struct {
	UserID    int64 `json:"user_id,omitempty"` // 用户 ID
	Positions []int `json:"positions"`         // 直播通栏位置列表

	positionBannersMap map[int][]liveextrabanner.LiveExtraBanner // 直播通栏列表，key 为直播通栏位置
	rooms              []*room.Room                              // 直播间信息
	followUserIDsMap   map[int64]struct{}                        // 用户关注主播列表，key 为主播 ID
	medals             []*livemedal.LiveMedal                    // 用户关注的主播勋章信息（亲密度）
}

type liveExtraBannersResp struct {
	Data map[int][]liveExtraBannersItem `json:"data"`
}

type liveExtraBannersItem struct {
	Pic string `json:"pic"` // 通栏图地址
	URL string `json:"url"` // 直播间地址

	openTime int64   // 开播时间
	score    float64 // 直播热度
	point    int64   // 亲密度
	isFollow bool    // 用户是否关注主播
}

func newLiveExtraBannerParam(c *handler.Context) (*liveExtraBannersParam, error) {
	p := new(liveExtraBannersParam)
	if err := c.Bind(p); err != nil {
		return nil, actionerrors.ErrParams
	}
	if p.UserID < 0 || len(p.Positions) == 0 {
		return nil, actionerrors.ErrParams
	}
	for _, position := range p.Positions {
		if !slices.Contains(liveextrabanner.PositionExtraBannerList, position) {
			return nil, actionerrors.ErrParams
		}
	}
	return p, nil
}

// ActionLiveExtraBanners 获取直播通栏信息
/**
 * @api {post} /rpc/live/extra-banners 获取直播通栏信息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} [user_id] 用户 ID
 * @apiParam {Number[]} positions 直播通栏位置列表
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "info": {
 *         "data": { // 无数据时返回空 map
 *           "1": [ // 直播通栏位置
 *             {
 *               "pic": "https://static-test.maoercdn.com/test/test.png", // 通栏图地址
 *               "url": "https://www.uat.missevan.com/live/665152" // 直播间地址
 *             }
 *           ]
 *         }
 *       }
 *     }
 */
func ActionLiveExtraBanners(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newLiveExtraBannerParam(c)
	if err != nil {
		return nil, err
	}

	// 获取直播通栏列表
	param.positionBannersMap, err = liveextrabanner.GetAllPositionBannersMap(param.Positions)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.positionBannersMap) == 0 {
		return liveExtraBannersResp{Data: map[int][]liveExtraBannersItem{}}, nil
	}

	// 获取直播间 ID
	roomIDs := make([]int64, 0, len(param.positionBannersMap))
	for _, positionBanners := range param.positionBannersMap {
		for _, positionBanner := range positionBanners {
			roomIDs = append(roomIDs, positionBanner.RoomID)
		}
	}
	// 获取符合条件的直播间（开播时长满 15 分钟）
	param.rooms, err = room.FindAll(bson.M{
		"room_id":          bson.M{"$in": util.Uniq(roomIDs)},
		"status.open":      room.StatusOpenTrue,
		"status.open_time": bson.M{"$lte": goutil.TimeNow().Add(-15 * time.Minute).UnixMilli()},
	})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if len(param.rooms) == 0 {
		return liveExtraBannersResp{Data: map[int][]liveExtraBannersItem{}}, nil
	}

	if param.UserID > 0 {
		if err = param.listLiveMedal(); err != nil {
			return nil, err
		}
	}
	return param.buildResp(), nil
}

func (param *liveExtraBannersParam) listLiveMedal() error {
	// 获取主播 ID 列表
	creatorIDs := make([]int64, 0, len(param.rooms))
	for _, r := range param.rooms {
		creatorIDs = append(creatorIDs, r.CreatorID)
	}
	// 查询用户关注了哪些主播
	attentions, err := attentionuser.CheckAttention(param.UserID, creatorIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	followUserIDs := make([]int64, 0, len(attentions))
	param.followUserIDsMap = make(map[int64]struct{}, len(attentions))
	for _, attention := range attentions {
		if attention.Followed {
			followUserIDs = append(followUserIDs, attention.UserID)
			param.followUserIDsMap[attention.UserID] = struct{}{}
		}
	}
	if len(followUserIDs) == 0 {
		return nil
	}

	// 获取用户和已关注主播的亲密度
	param.medals, err = livemedal.List(bson.M{
		"user_id":    param.UserID,
		"creator_id": bson.M{"$in": followUserIDs},
		"status":     bson.M{"$gt": livemedal.StatusPending},
	}, nil, &livemedal.FindOptions{DisableAll: true})
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *liveExtraBannersParam) buildResp() *liveExtraBannersResp {
	roomMap := goutil.ToMap(param.rooms, "CreatorID").(map[int64]*room.Room)
	medalMap := goutil.ToMap(param.medals, "CreatorID").(map[int64]*livemedal.LiveMedal)
	// 依次按照用户关注主播大于未关注主播、亲密度降序、直播热度降序、开播时间倒序的方式排列直播通栏
	// 用户关注的主播排序规则为：依次按照亲密度降序、直播热度降序、开播时间倒序的方式排列直播通栏
	// 用户未关注的主播排序规则为：依次按照直播热度降序、开播时间倒序的方式排列直播通栏
	sortFunc := func(items []liveExtraBannersItem) {
		if len(items) <= 1 {
			return
		}
		sort.Slice(items, func(i, j int) bool {
			if items[i].isFollow != items[j].isFollow {
				return items[i].isFollow && !items[j].isFollow
			}
			if items[i].point != items[j].point {
				return items[i].point > items[j].point
			}
			if items[i].score != items[j].score {
				return items[i].score > items[j].score
			}
			return items[i].openTime > items[j].openTime
		})
	}

	resp := &liveExtraBannersResp{
		Data: make(map[int][]liveExtraBannersItem, len(param.positionBannersMap)),
	}
	for position, banners := range param.positionBannersMap {
		items := make([]liveExtraBannersItem, 0, len(banners))
		for _, banner := range banners {
			if r, ok := roomMap[banner.UserID]; ok {
				item := liveExtraBannersItem{
					Pic:      banner.CoverURL,
					URL:      params.RoomURL(banner.RoomID),
					score:    r.Status.Score,
					openTime: r.Status.OpenTime,
				}
				if _, ok = param.followUserIDsMap[banner.UserID]; ok {
					item.isFollow = true
					if m, ok := medalMap[banner.UserID]; ok {
						item.point = m.Point
					}
				}
				items = append(items, item)
			}
		}
		if len(items) > 0 {
			sortFunc(items)
			if len(items) > maxLiveExtraBannersLength {
				items = items[:maxLiveExtraBannersLength]
			}
			resp.Data[position] = items
		}
	}
	return resp
}
