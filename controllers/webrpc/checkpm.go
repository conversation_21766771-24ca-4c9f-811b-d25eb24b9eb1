package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/guild/livecontract"
	"github.com/MiaoSiLa/live-service/models/livedb/luckybag"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	servicedb "github.com/MiaoSiLa/missevan-go/service/db"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	// sendStatusShamAllow 假发送
	sendStatusShamAllow = iota
	// sendStatusAllow 允许发送
	sendStatusAllow
)

type liveCheckPM struct {
	FromUserID int64 `json:"from_user_id"`
	ToUserID   int64 `json:"to_user_id"`
}

type liveCheckPMResp struct {
	Status      int  `json:"status"`
	HasLuckyBag bool `json:"has_lucky_bag"`
}

// ActionLiveCheckPM 直播相关私信检查
/**
 * @api {post} /rpc/live/check-pm 直播相关私信检查
 * @apiVersion 0.1.0
 * @apiGroup rpc
 *
 * @apiParam {Number} from_user_id 发送人
 * @apiParam {Number} to_user_id 接收用户
 *
 * @apiSuccess (200) {Number} code
 * @apiSuccess (200) {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "status": 0, // 发送状态：0 假发送，1 真发送
 *       "has_lucky_bag": true // 是否有中奖的实物福袋：false 没有，true 有；用于判断是否需要跳过【每天最多给 10 个未关注自己的人发私信】和【高消费规则限制】检查
 *     }
 *   }
 */
func ActionLiveCheckPM(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newLiveCheckPM(c)
	if err != nil {
		return nil, actionerrors.ErrParams
	}

	pass, err := param.isLuckyBagExists()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if pass {
		return buildCheckPMResp(sendStatusAllow, true), nil
	}

	pass, err = param.checkCreatorContract()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if pass {
		return buildCheckPMResp(sendStatusAllow, false), nil
	}

	pass, err = param.checkUserLevel()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if pass {
		return buildCheckPMResp(sendStatusAllow, false), nil
	}

	return buildCheckPMResp(sendStatusShamAllow, false), nil
}

func newLiveCheckPM(c *handler.Context) (*liveCheckPM, error) {
	var param liveCheckPM
	err := c.BindJSON(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.FromUserID <= 0 || param.ToUserID <= 0 {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

func (param *liveCheckPM) isLuckyBagExists() (bool, error) {
	// 主播最晚 60 个自然日发放实物奖励
	expireTime := goutil.BeginningOfDay(goutil.TimeNow()).AddDate(0, 0, -61).Unix()
	// 主播和中奖用户互相私信
	userIDs := []int64{param.FromUserID, param.ToUserID}
	db := luckybag.DB().Table(luckybag.InitiateRecord{}.TableName()+" AS ir").
		Joins("LEFT JOIN "+luckybag.UserPrize{}.TableName()+" AS up ON ir.id = up.lucky_bag_id").
		Where("ir.type = ? AND ir.creator_id IN (?) AND up.user_id IN (?) AND up.create_time >= ?",
			luckybag.TypeEntity, userIDs, userIDs, expireTime)
	return servicedb.Exists(db)
}

func (param *liveCheckPM) checkCreatorContract() (pass bool, err error) {
	// 获取用户与公会生效中的合约
	var contract *livecontract.LiveContract
	contract, err = livecontract.FindInContractingByLiveID(param.ToUserID, 0)
	if err != nil {
		return
	}
	// 主播没有公会生效合约时正常发送
	pass = contract == nil
	return
}

func (param *liveCheckPM) checkUserLevel() (pass bool, err error) {
	var user *liveuser.User
	user, err = liveuser.Find(param.FromUserID)
	if err != nil {
		return
	}
	// 如果用户没有直播间账号，默认为假发送
	if user == nil {
		return
	}

	// 直播等级 >= 17 或拥有该主播的粉丝牌时正常发送
	if usercommon.Level(user.Contribution) >= 17 {
		pass = true
		return
	}

	pass, err = livemedal.HasUserOwnedMedalByCreatorID(param.FromUserID, param.ToUserID)
	return
}

func buildCheckPMResp(status int, hasLuckyBag bool) *liveCheckPMResp {
	return &liveCheckPMResp{
		Status:      status,
		HasLuckyBag: hasLuckyBag,
	}
}
