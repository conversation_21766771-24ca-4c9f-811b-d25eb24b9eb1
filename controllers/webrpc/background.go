package webrpc

import (
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionRecommendedBackground 获取直播间推荐的背景图
/**
 * @api {post} /rpc/recommended/background/get 获取直播间推荐的背景图
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 房间 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {  // 没有推荐背景图时，该值为 nil
 *       "image_url": "http://static.missevan.com/avatars/icon01.png",
 *       "opacity": 1.0
 *     }
 *   }
 */
func ActionRecommendedBackground(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		RoomID int64 `json:"room_id"`
	}
	err := c.BindJSON(&param)
	if err != nil || param.RoomID <= 0 {
		return nil, actionerrors.ErrParams
	}

	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	background, err := liverecommendedelements.FindBackground(param.RoomID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	return background, nil
}
