package webrpc

import (
	"time"

	"github.com/MiaoSiLa/live-service/models/liveim"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type recommendPayload struct {
	Type        string           `json:"type"`
	Event       string           `json:"event"`
	RoomID      int64            `json:"room_id"`
	Recommender *liveuser.Simple `json:"recommender"`
}

// ActionNobleRecommendNotify 发送神话推荐房间开始和过期通知
// TODO: 挪到 cron
/**
 * @api {post} /rpc/cron/noble/recommend/notify 发送神话推荐房间开始和过期通知
 * @apiDescription 发送神话推荐房间内通知，总是成功
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": true
 *   }
 *
 * @apiSuccessExample {json} WebSocket 推荐过期房间内消息
 *   {
 *     "type": "room",
 *     "event": "recommend",
 *     "room_id": 139879430,
 *     "recommender": null,
 *   }
 *
 * @apiSuccessExample {json} WebSocket 推荐开始房间内消息
 *   {
 *     "type": "room",
 *     "event": "recommend",
 *     "room_id": 139879430,
 *     "recommender": {
 *       "user_id": 1,
 *       "username": "bless",
 *       "iconurl": "https://static.missevan.com/avatars/icon01.png",
 *       "recommend_avatar_frame_url": "https://static.missevan.com/007_3_177_3_21.png"
 *     }
 *   }
 */
func ActionNobleRecommendNotify(c *handler.Context) (handler.ActionResponse, error) {
	logger.Info("神话推荐房间通知任务开始")
	// 获取当前时间，允许误差 30 秒
	now := goutil.TimeNow()

	// 防止短时间内多次请求，重复发送通知
	key := keys.LockNobleRecommendNotify0.Format()
	ok, err := service.Redis.SetNX(key, 1, time.Minute).Result()
	if err != nil {
		logger.Error(err)
		return true, nil
	}
	if !ok {
		logger.Warn("当前时段不可重复发送神话推荐通知")
		return true, nil
	}

	expireTime := now.Add(-30 * time.Minute)
	expireRc, err := findRecommend(expireTime)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	startRc, err := findRecommend(now)
	if err != nil {
		logger.Error(err)
		// PASS
	}
	sendNotify(expireRc, startRc)

	logger.Info("神话推荐房间通知任务结束")
	return true, nil
}

func newRecommendPayload(roomID int64) *recommendPayload {
	return &recommendPayload{
		Type:   liveim.TypeRoom,
		Event:  liveim.EventRecommend,
		RoomID: roomID,
	}
}

func findRecommend(now time.Time) (rc []*livenoblerecommend.NobleRecommend, err error) {
	// 允许误差 30 秒
	minTime, maxTime := now.Add(-30*time.Second).Unix(), now.Add(30*time.Second).Unix()
	err = service.DB.Where("start_time BETWEEN ? AND ?", minTime, maxTime).
		Where("status = ?", livenoblerecommend.StatusNormal).
		Find(&rc).Error
	return rc, err
}

func sendNotify(expireRc, startRc []*livenoblerecommend.NobleRecommend) {
	expireLen, startLen := len(expireRc), len(startRc)
	if expireLen == 0 && startLen == 0 {
		logger.Info("当前时段没有神话推荐房间需要通知")
		return
	}
	// 当房间推荐连续时，仅需要发送开始通知
	expireRc = checkContinueRoom(expireRc, startRc)
	var log *logger.Entry
	for _, expire := range expireRc {
		log = logger.WithFields(logger.Fields{
			"room_id":      expire.RoomID,
			"from_user_id": expire.FromUserID,
		})
		if err := userapi.Broadcast(expire.RoomID, newRecommendPayload(expire.RoomID)); err != nil {
			log.Errorf("发送神话推荐房间到期通知失败: %v", err)
			// PASS
		} else {
			log.Info("发送神话推荐房间到期通知成功")
		}
	}

	for _, start := range startRc {
		log = logger.WithFields(logger.Fields{
			"room_id":      start.RoomID,
			"from_user_id": start.FromUserID,
		})
		payload := newRecommendPayload(start.RoomID)
		payload.Recommender = start.FindRecommender()
		if err := userapi.Broadcast(start.RoomID, payload); err != nil {
			log.Errorf("发送神话推荐房间开始通知失败: %v", err)
			// PASS
		} else {
			log.Info("发送神话推荐房间开始通知成功")
		}
	}
}

func checkContinueRoom(expireRc, startRc []*livenoblerecommend.NobleRecommend) []*livenoblerecommend.NobleRecommend {
	var change bool
	for i := 0; i < len(expireRc); {
		change = false
		for _, start := range startRc {
			if expireRc[i].RoomID == start.RoomID {
				logger.WithFields(logger.Fields{
					"room_id":      expireRc[i].RoomID,
					"from_user_id": expireRc[i].FromUserID,
				}).Infof("当前房间神话推荐连续, 仅发送推荐开始通知")
				expireRc = append(expireRc[:i], expireRc[i+1:]...)
				change = true
				break
			}
		}
		if !change {
			i++
		}
	}
	return expireRc
}
