package webrpc

import "github.com/MiaoSiLa/missevan-go/controllers/handler"

// ActionMessageCrossSend 主播连线跨直播间推送聊天消息
/**
 * @api {post} /rpc/message/cross-send 主播连线跨直播间推送聊天消息
 * @apiVersion 0.1.0
 * @apiGroup webrpc
 *
 * @apiParam {Number} room_id 发出消息的直播间号
 * @apiParam {Number} user_id 发出消息的用户 ID
 * @apiParam {String} message 消息内容
 * @apiParam {String} msg_id 消息 ID
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} message
 * @apiSuccess {Object} data
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "success",
 *     "data": null
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} message 服务器内部错误
 *
 * @apiSuccessExample {json} WebSocket 主播连线聊天互通 WS 消息
 *   {
 *     "type": "message",
 *     "event": "cross_new", // 直播间互通聊天消息，在当前直播间展示其他直播的消息时使用
 *     "room_id": 1234, // 当前直播间 ID
 *     "user": {
 *       "user_id": 10,
 *       "username": "bless",
 *       "iconurl": "https://static-test.missevan.com/profile/201704/07/fdbc76ccfaaccbd3891313c6084724d9211734.png",
 *       "titles": [{
 *         "type": "staff",
 *         "name": "超管",
 *         "color": "#f45b41"
 *       }, {
 *         "type": "level",
 *         "level": 4
 *       }, {
 *         "type": "noble",
 *         "name": "新秀",
 *         "level": 2
 *       }, {
 *         "type": "avatar_frame",
 *         "icon_url": "https://static.missevan.com/gifts/avatarframes/006.png"
 *       }]
 *     },
 *     "room": { // 原始发消息的直播间的信息，举报时给的文案里的相关信息也需要使用这里的
 *       "room_id": 10659544,
 *       "creator_id": 10,
 *       "creator_iconurl": "http://static-test.maoercdn.com/avatars/icon01.png",
 *       "creator_username": "主播昵称"
 *     },
 *     "msg_id": "3ae42491-1784-4eb3-bc8c-71f966cc0e37",
 *     "message": "消息内容",
 *     "bubble": {
 *       "type": "noble", // 气泡类型，目前支持: 贵族气泡 noble
 *       "noble_level": 2 // 使用对应等级的贵族气泡
 *     }
 *   }
 */
func ActionMessageCrossSend(c *handler.Context) (handler.ActionResponse, string, error) {
	return nil, "", nil
}
