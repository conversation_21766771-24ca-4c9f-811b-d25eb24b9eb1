package webrpc

import (
	"testing"
	"time"

	"github.com/go-redis/redis/v7"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livepk"
	"github.com/MiaoSiLa/live-service/models/redis/usersrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRankRevenue(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r, err := room.FindOne(bson.M{"creator_id": 10})
	require.NoError(err)
	require.NotNil(r)
	body := handler.M{
		"type":    AddRevenueTypeNoble,
		"room":    handler.M{"room_id": r.RoomID, "creator_id": r.CreatorID},
		"user":    handler.M{"user_id": 12},
		"receive": handler.M{"revenue": 6},
	}
	c := handler.NewRPCTestContext("/rpc/rank/revenue", body)
	res, err := ActionRankRevenue(c)
	require.NoError(err)
	assert.Equal("success", res)
	body["receive"] = handler.M{"revenue": 6, "add_medal_point": true}
	c = handler.NewRPCTestContext("/rpc/rank/revenue", body)
	res, err = ActionRankRevenue(c)
	require.NoError(err)
	assert.Equal("success", res)
}

func TestRankRevenueFindRoom(t *testing.T) {
	assert := assert.New(t)

	param := rankScoreReq{Room: rsRoom{RoomID: room.TestExistsRoomID}}
	param.findRoom()
	assert.NotNil(param.r)
}

func TestRankRevenueAddPK(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	col := livepk.PKCollection()
	_, err := col.DeleteMany(ctx, bson.M{"fighters": bson.M{"$exists": false}})
	require.NoError(err)
	var lp livepk.LivePK
	err = col.FindOne(ctx,
		bson.M{"status": livepk.PKRecordStatusFighting}).Decode(&lp)
	require.NoError(err)
	param := rankScoreReq{Room: rsRoom{RoomID: lp.Fighters[0].RoomID}}
	assert.NotPanics(func() { param.addPK() })
	param.Type = AddRevenueTypeGift
	param.r = new(room.Room)
	param.r.RoomID = lp.Fighters[0].RoomID
	param.Receive.Revenue = 100
	ok := false
	cancel = mrpc.SetMock("im://broadcast/many", func(interface{}) (interface{}, error) {
		ok = true
		return "success", nil
	})
	defer cancel()
	require.NoError(service.Redis.Set(keys.KeyPKFighting1.Format(param.r.RoomID),
		"test", time.Second).Err())
	param.addPK()
	param.broadcast()
	assert.True(ok)
}

func TestAddMedalPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 没有直播间
	roomID := int64(22334)
	userID := int64(77785795)
	revenue := float64(6)
	param := &rankScoreReq{
		Type: AddRevenueTypeGift,
		Room: rsRoom{RoomID: roomID, CreatorID: 22334},
		User: rsUser{UserID: userID}, Receive: rsReceive{Revenue: revenue},
	}
	param.addMedalPoint()
	// 没有勋章
	param.r = new(room.Room)
	param.addMedalPoint()

	// 有勋章的直播间
	roomID = int64(10240)
	r, err := room.Find(roomID)
	require.NoError(err)
	require.NotNil(r)
	require.NotNil(r.Medal)
	// type 不对不加积分
	param = &rankScoreReq{
		Type:    AddRevenueTypeNoble,
		Room:    rsRoom{RoomID: roomID, CreatorID: 10240},
		User:    rsUser{UserID: userID},
		Receive: rsReceive{GiftType: 1, Revenue: revenue},
		r:       r,
	}
	param.addMedalPoint()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	filter := bson.M{"room_id": roomID, "user_id": userID}
	_, err = livemedal.Collection().DeleteOne(ctx, filter)
	require.NoError(err)
	param = &rankScoreReq{
		Type:    AddRevenueTypeGift,
		Room:    rsRoom{RoomID: roomID, CreatorID: 10240},
		User:    rsUser{UserID: userID},
		Receive: rsReceive{GiftType: 1, Revenue: revenue},
		r:       r,
	}
	param.r, err = room.Find(roomID)
	require.NoError(err)
	require.NotNil(param.r)
	param.addMedalPoint()
	medal := new(livemedal.LiveMedal)
	err = livemedal.Collection().FindOne(ctx, filter).Decode(medal)
	require.NoError(err)
	assert.Equal(livemedal.StatusPending, medal.Status)
	assert.Equal(revenue, float64(medal.Point))
}

func TestGetAddMedalPointType(t *testing.T) {
	assert := assert.New(t)

	addType, ok := getAddMedalPointType(AddRevenueTypeGift)
	assert.True(ok)
	assert.Equal(livemedal.TypeGiftAddMedalPoint, addType)
	addType, ok = getAddMedalPointType(AddRevenueTypeQuestion)
	assert.True(ok)
	assert.Equal(livemedal.TypeQuestionAddMedalPoint, addType)
	addType, ok = getAddMedalPointType("invalid")
	assert.False(ok)
	assert.Equal(0, addType)
}

func TestRankScoreReqAddActivity(t *testing.T) {
	assert := assert.New(t)

	param := &rankScoreReq{
		Type: AddRevenueTypeGift,
		Room: rsRoom{
			RoomID:    22334,
			CreatorID: 10240,
		},
		User: rsUser{UserID: 77785795},
		Receive: rsReceive{
			GiftID:   10,
			GiftType: 1,
			Revenue:  6,
		},
		g: &gift.Gift{},
	}
	param.addActivity()
	assert.NotNil(param.syncParam)
}

func TestActionHourRankRandom(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goutil.SetTimeNow(func() time.Time {
		return time.Date(2022, 1, 7, 15, 0, 0, 0, time.Local)
	})
	defer goutil.SetTimeNow(nil)

	key := usersrank.Key(usersrank.TypeHour, goutil.TimeNow())
	require.NoError(service.Redis.Del(key).Err())

	c := handler.NewRPCTestContext("/rpc/hourrank/random", nil)
	resp, err := ActionHourRankRandom(c)
	require.NoError(err)
	randomResp := resp.(hourRankRandomResp)
	assert.Zero(randomResp.RoomID)

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	var r room.Room
	err = room.Collection().FindOne(ctx, bson.M{"status.open": room.StatusOpenTrue}).Decode(&r)
	require.NoError(err)
	require.NoError(
		service.Redis.ZAdd(key, &redis.Z{Score: 50000, Member: r.CreatorID}).Err(),
	)
	c = handler.NewRPCTestContext("/rpc/hourrank/random", nil)
	resp, err = ActionHourRankRandom(c)
	require.NoError(err)
	randomResp = resp.(hourRankRandomResp)
	assert.Equal(r.RoomID, randomResp.RoomID)
}
