package webrpc

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/models/mysql/livepreview"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

const (
	testElementID = 233
	testUserID    = 4234234
)

func TestPreviewTagKeys(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)

	kc.Check(previewParam{}, "element_type", "element_id", "user_id")
	kc.Check(previewResp{}, "id", "room_id", "title", "live_start_time", "live_schedule_time",
		"creator_id", "live_status", "reservation_status")
}

func TestActionPreviewGet(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试请求失败的情况
	api := "/rpc/preview/get"
	c := handler.NewRPCTestContext(api, nil)
	_, err := ActionPreviewGet(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试无数据的情况下请求成功
	param := previewParam{
		ElementType: livepreview.ElementTypeDrama,
		ElementID:   9999999,
	}
	c = handler.NewRPCTestContext(api, param)
	result, err := ActionPreviewGet(c)
	require.NoError(err)
	require.Nil(result)

	// 测试有数据的情况下请求成功（未传入用户 ID）
	param.ElementID = testElementID
	c = handler.NewRPCTestContext(api, param)
	result, err = ActionPreviewGet(c)
	require.NoError(err)
	resp, ok := result.(previewResp)
	require.True(ok)
	require.NotNil(resp)
	assert.EqualValues(1001, resp.ID)
	assert.Equal(live.StatusOpen, resp.LiveStatus)
	assert.Nil(resp.ReservationStatus)

	// 测试有数据的情况下请求成功（传入用户 ID）
	param.UserID = testUserID
	c = handler.NewRPCTestContext(api, param)
	result, err = ActionPreviewGet(c)
	require.NoError(err)
	resp, ok = result.(previewResp)
	require.True(ok)
	require.NotNil(resp)
	assert.EqualValues(1001, resp.ID)
	assert.Equal(live.StatusOpen, resp.LiveStatus)
	assert.EqualValues(1, *resp.ReservationStatus)
}

func TestNewPreviewParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试参数错误的情况
	api := "/rpc/preview/get"
	c := handler.NewRPCTestContext(api, nil)
	_, err := newPreviewParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试 element_type 参数错误的情况
	param := previewParam{
		ElementType: 0,
	}
	c = handler.NewRPCTestContext(api, param)
	_, err = newPreviewParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试 element_type 参数错误的情况
	param = previewParam{
		ElementType: livepreview.ElementTypeDrama,
		ElementID:   0,
	}
	c = handler.NewRPCTestContext(api, param)
	_, err = newPreviewParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试 user_id 参数错误的情况
	param = previewParam{
		ElementType: livepreview.ElementTypeDrama,
		ElementID:   testElementID,
		UserID:      -1,
	}
	c = handler.NewRPCTestContext(api, param)
	_, err = newPreviewParam(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 测试无直播预告数据的情况
	param = previewParam{
		ElementType: livepreview.ElementTypeDrama,
		ElementID:   99999999,
	}
	c = handler.NewRPCTestContext(api, param)
	p, err := newPreviewParam(c)
	require.NoError(err)
	assert.Nil(p.preview)

	// 测试有直播预告数据的情况
	param.ElementID = testElementID
	c = handler.NewRPCTestContext(api, param)
	p, err = newPreviewParam(c)
	require.NoError(err)
	assert.NotNil(p.preview)
	assert.NotNil(p.live)
}
