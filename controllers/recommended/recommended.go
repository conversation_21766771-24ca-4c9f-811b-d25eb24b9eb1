package recommended

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"strconv"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	lre "github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/biliai"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

// Handler returns the registered handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "recommended",
		Actions: map[string]*handler.Action{
			"homepage": handler.NewAction(handler.GET, ActionHomepage, false),
			"top":      handler.NewAction(handler.GET, ActionTop, false),
		},
	}
}

// 资源位来源
const (
	RecommendAttrTianma     = iota + 1 // 天马推荐算法下发
	RecommendAttrSupplement            // 天马推荐算法下发不足时进行补位
	RecommendAttrOpConfig              // 运营干预卡
)

// 客户端上报的网络类型
const (
	networkUnknown = iota
	networkWifi
	networkCellular
	networkOffline
	networkOthernet
)

// GetNetworkType 判断并返回网络类型
func GetNetworkType(network int) string {
	switch network {
	case networkWifi:
		return tianma.NetworkWifi
	case networkCellular:
		return tianma.NetworkCellular
	case networkOthernet:
		return tianma.NetworkOtherNet
	case networkOffline:
		fallthrough
	default:
		return tianma.UnknownStringValue
	}
}

// ActionHomepage 获取 APP 首页 "正在直播" 模块的内容
/**
 * @api {get} /api/v2/recommended/homepage 读取 APP 首页 "正在直播" 模块的内容
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/recommended
 *
 * @apiDescription 忽略 position 字段，以 info 中出现的实际次序为准；返回的数量少于 3 个时，该模块隐藏；
 *
 * @apiSuccess {Number} code
 * @apiSuccess {Object} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": [
 *       {
 *         "position": 1,                                                   // 该字段用作后台设置，这里请忽略
 *         "room_id": 22489473,                                             // 房间号
 *         "name": "23333333",                                              // 直播标题
 *         "cover_url": "https://static.missevan.com/profile/01.png",       // 直播封面图
 *         "creator_id": 10,
 *         "creator_username": "bless01",                                   // 主播昵称
 *         "creator_iconurl": "https://static.missevan.com/profile/01.png", // 主播头像
 *         "catalog_id": 10,                                                // 分区 ID
 *         "catalog_name": "娱乐",                                           // 分区名称
 *         "catalog_color": "#D68DFE",                                      // 分区颜色
 *         "custom_tag": {                                                  // 个性词条
 *           "tag_id": 10001,                                               // 个性词条 ID
 *           "tag_name": "腹黑青叔"                                          // 个性词条名称
 *         },
 *         "status": {                                                      // 该结构体忽略
 *           "open": 1,
 *           "pk": 1, // 1：直播间在 PK 状态，0 或不存在：直播间不在 PK 状态
 *           "red_packet": 1 // 当前待抢红包或可抢红包数量，0 或不存在表示无待抢红包或者可抢红包
 *         }
 *       },
 *       {
 *         "position": 0,
 *         "room_id": 22489474,
 *         "name": "22222223",
 *         "cover_url": "https://static.missevan.com/profile/02.png",
 *         "creator_id": 1234,
 *         "creator_username": "bless02",
 *         "creator_iconurl": "https://static.missevan.com/profile/02.png",
 *         "catalog_id": 10,                                                // 分区 ID
 *         "catalog_name": "娱乐",                                           // 分区名称
 *         "catalog_color": "#D68DFE",                                      // 分区颜色
 *         "custom_tag": {                                                  // 个性词条
 *           "tag_id": 10001,                                               // 个性词条 ID
 *           "tag_name": "腹黑青叔"                                          // 个性词条名称
 *         },
 *         "status": {
 *           "open": 1,
 *           "red_packet": 1
 *         }
 *       },
 *     ]
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionHomepage(c *handler.Context) (handler.ActionResponse, error) {
	return ListHomepageRooms()
}

// ListHomepageRooms 获取 APP 首页 "正在直播" 模块的内容
func ListHomepageRooms() ([]liverecommendedelements.Room, error) {
	const maxNum = 3

	list, err := ReadConfiguredRoom(maxNum)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	result := make([]liverecommendedelements.Room, 0, maxNum)
	for _, v := range list {
		if v.RoomID != 0 && v.Status.Open != room.StatusOpenFalse {
			result = append(result, v)
		}
	}

	var excludeIDs []int64
	// TODO: 把 issue 换成文档链接
	// https://github.com/MiaoSiLa/requirements-doc/issues/444
	if len(result) < maxNum {
		excludeIDs = make([]int64, len(result))
		for i, v := range result {
			excludeIDs[i] = v.RoomID
		}

		more, err := addRecommends(maxNum-len(result), excludeIDs...)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		result = append(result, more...)
		for _, v := range more {
			excludeIDs = append(excludeIDs, v.RoomID)
		}
	}
	if len(result) < maxNum {
		more, err := ReadTopOpened(maxNum-len(result), excludeIDs...)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		result = append(result, more...)
	}

	return result, nil
}

// ReadConfiguredRoom 读取直播间推荐位
func ReadConfiguredRoom(limit int) ([]liverecommendedelements.Room, error) {
	var list []liverecommendedelements.Model
	err := liverecommendedelements.TableRoom(service.DB).Limit(limit).Order("sort").Find(&list).Error
	if err != nil {
		return nil, err
	}
	rooms, err := makeRooms(list)
	if err != nil {
		return nil, err
	}
	return rooms, nil
}

func makeRooms(list []liverecommendedelements.Model) ([]liverecommendedelements.Room, error) {
	roomIDs := make([]int64, len(list))
	result := make([]liverecommendedelements.Room, len(list))
	for i, v := range list {
		roomIDs[i] = v.ElementID
		result[i].Set(v)
	}
	filter := bson.M{
		"room_id": bson.M{"$in": roomIDs},
		"limit":   bson.M{"$exists": false},
	}
	if !HasHypnosisRoom() {
		filter["catalog_id"] = bson.M{"$ne": catalog.CatalogIDLiveHypnosis}
	}
	roomSimple, err := room.ListSimples(filter, nil,
		&room.FindOptions{FindCreator: true, FindCatalogInfo: true, FindCustomTag: true})
	if err != nil {
		return nil, err
	}
	for i := range roomSimple {
		roomSimple[i].SchemeToURL()
	}

	for i := range result {
		for _, v := range roomSimple {
			if result[i].RoomID == v.RoomID {
				result[i].Name = v.Name
				result[i].CoverURL = v.CoverURL
				result[i].CreatorID = v.CreatorID
				result[i].CreatorUsername = v.CreatorUsername
				result[i].CreatorIconURL = v.CreatorIconURL
				result[i].CatalogID = v.CatalogID
				result[i].CatalogName = v.CatalogName
				result[i].CatalogColor = v.CatalogColor
				result[i].CustomTag = v.CustomTag
				if v.IsOpen() {
					result[i].Status.Open = room.StatusOpenTrue
				}
				result[i].EnsureCoverURL()
			}
		}
	}
	return result, nil
}

// ReadTopOpened 获取热度排名前 num 的正在直播的直播间
func ReadTopOpened(num int, excludeIDs ...int64) (result []liverecommendedelements.Room, err error) {
	option := options.Find().SetLimit(int64(num)).SetSort(room.SortByScore)
	filter := bson.M{
		"status.open": room.StatusOpenTrue,
		"limit":       bson.M{"$exists": false},
	}
	if len(excludeIDs) > 0 {
		filter["room_id"] = bson.M{"$nin": excludeIDs}
	}
	if !HasHypnosisRoom() {
		filter["catalog_id"] = bson.M{"$ne": catalog.CatalogIDLiveHypnosis}
	}
	simples, err := room.ListSimples(filter, option,
		&room.FindOptions{FindCreator: true, FindCatalogInfo: true, FindCustomTag: true})
	if err != nil {
		return nil, err
	}
	result = ConvertOpenedSimpleRooms(simples)
	return result, nil
}

// ConvertOpenedSimpleRooms 把 room.Simple 转换成 Room
func ConvertOpenedSimpleRooms(simples []*room.Simple) []liverecommendedelements.Room {
	result := make([]liverecommendedelements.Room, 0, len(simples))
	for i := range simples {
		simples[i].SchemeToURL()
		m := liverecommendedelements.Room{
			RoomID:          simples[i].RoomID,
			Name:            simples[i].Name,
			CoverURL:        simples[i].CoverURL,
			CreatorID:       simples[i].CreatorID,
			CreatorUsername: simples[i].CreatorUsername,
			CreatorIconURL:  simples[i].CreatorIconURL,
			CatalogID:       simples[i].CatalogID,
			CatalogName:     simples[i].CatalogName,
			CatalogColor:    simples[i].CatalogColor,
			CustomTag:       simples[i].CustomTag,
			Status: liverecommendedelements.Status{
				Open: room.StatusOpenTrue,
			},
		}
		m.EnsureCoverURL()
		result = append(result, m)
	}
	return result
}

// HasHypnosisRoom 允许助眠分区
func HasHypnosisRoom() bool {
	c := util.MakeClock(goutil.TimeNow())
	for _, v := range config.Conf.Params.Recommended.HypnosisDisableClocks {
		if c.GreaterOrEqual(v[0]) && c.Less(v[1]) {
			return false
		}
	}
	return true
}

var sMembersCandidates = func() ([]string, error) {
	return service.Redis.SMembers(keys.KeyCronRecommendLiveIDs0.Format()).Result()
}

func getCandidates(excludeIDs ...int64) ([]int64, error) {
	members, err := sMembersCandidates()
	if err != nil {
		return nil, err
	}
	creatorIDs := make([]int64, 0, len(members))
	for _, v := range members {
		id, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			logger.Errorf("invalid CronRecommendLiveID: %s", v)
			// PASS
		} else {
			exist := false
			for _, v := range excludeIDs {
				if id == v {
					exist = true
				}
			}
			if !exist {
				creatorIDs = append(creatorIDs, id)
			}
		}
	}
	return creatorIDs, nil
}

func addRecommends(num int, excludeIDs ...int64) (result []liverecommendedelements.Room, err error) {
	members, err := getCandidates(excludeIDs...)
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return nil, nil
	}

	// TODO: 直接 find 即可
	type m bson.M
	match := m{
		"creator_id":  m{"$in": members},
		"status.open": room.StatusOpenTrue,
		"limit":       m{"$exists": false},
	}
	if !HasHypnosisRoom() {
		match["catalog_id"] = m{"$ne": catalog.CatalogIDLiveHypnosis}
	}
	pipeline := []m{
		{"$match": match},
		{"$sample": m{"size": num}},
		{"$project": room.SimpleProjection()},
	}
	simples, err := room.AggregateSimples(pipeline,
		&room.FindOptions{FindCreator: true, FindCatalogInfo: true, FindCustomTag: true})
	if err != nil {
		return nil, err
	}
	result = ConvertOpenedSimpleRooms(simples)
	return result, nil
}

var listSchedule = liverecommendedelements.ListSchedule

// RoomWithTrace 直播间信息及对应的 trackID 和 trace
type RoomWithTrace struct {
	*room.Simple `json:",inline"`

	TrackID string `json:"track_id,omitempty"` // 算法返回，标记一次数据请求的 ID，同一刷中所有卡片的 track_id 取值一样
	Trace   string `json:"trace,omitempty"`    // 埋点上报字段
}

type topResp struct {
	Marker string           `json:"marker,omitempty"`
	Rooms  []*RoomWithTrace `json:"rooms"`
}

func (r *topResp) filterRoomsCover() {
	if len(r.Rooms) == 0 {
		return
	}

	// TODO: lock

	enable, err := service.Redis.HGet(keys.KeyRecommendedTopCoverGray0.Format(), "enable").Result()
	if err != nil {
		if serviceredis.IsRedisNil(err) {
			return
		}
		logger.Error(err)
		return
	}
	if enable == "" {
		return
	}

	covers := make([]string, 0, len(r.Rooms))
	coverIndexs := make([]int, 0, len(r.Rooms))
	for i, room := range r.Rooms {
		// TODO: 换成 storage 的写法
		u, err := url.Parse(room.CoverURL)
		if err != nil {
			continue
		}
		if len(u.Path) > 1 {
			covers = append(covers, config.Conf.Params.URL.CDN+u.Path[1:])
			coverIndexs = append(coverIndexs, i)
		}
	}
	grayCovers, err := service.Redis.HMGet(keys.KeyRecommendedTopCoverGray0.Format(), covers...).Result()
	if err != nil {
		logger.Error(err)
		return
	}

	setProcessCovers := make([]interface{}, 0, len(r.Rooms)*2)
	for i, grayCover := range grayCovers {
		if grayCover == nil {
			setProcessCovers = append(setProcessCovers, covers[i], "-")
			continue
		}
		v, ok := grayCover.(string)
		if !ok {
			continue
		}
		switch v {
		case "":
			setProcessCovers = append(setProcessCovers, covers[i], "-")
		case "-":
			// wait worker to process
		default:
			r.Rooms[coverIndexs[i]].CoverURL = service.Storage.Parse(v)
		}
	}
	if len(setProcessCovers) > 0 {
		_, err = service.Redis.HMSet(keys.KeyRecommendedTopCoverGray0.Format(), setProcessCovers...).Result()
		if err != nil {
			logger.Error(err)
			return
		}
	}
}

type exposureParam struct {
	RefreshType int
	RefreshNum  int64
	Network     int
}

// Trace 直播间 trace 信息
type Trace struct {
	TrackID     string      `json:"track_id,omitempty"`
	RefreshType int         `json:"refresh_type,omitempty"`
	RefreshNum  int64       `json:"refresh_num,omitempty"`
	Attr        int         `json:"attr,omitempty"`
	Goto        tianma.Goto `json:"goto,omitempty"`       // 卡片类型
	SampleIDs   *string     `json:"sample_ids,omitempty"` // 天马算法下发的实验组分组
	RequestID   string      `json:"request_id,omitempty"` // 服务端生成的标识本次请求的唯一 ID
}

func (ep *exposureParam) getItemTrace(item biliai.ResponseItem) string {
	t := Trace{
		TrackID:     item.TrackID,
		RefreshType: ep.RefreshType,
		RefreshNum:  ep.RefreshNum,
		Attr:        item.Attr,
	}
	traceStr, err := json.Marshal(t)
	if err != nil {
		logger.Errorf("marshal trace error: %v", err)
		return ""
	}

	// 将 JSON 字符串赋值给 item 的某个字段
	return string(traceStr)
}

func newExposureParam(c *handler.Context) *exposureParam {
	var (
		marker, _      = c.GetParamString("marker")
		refreshType, _ = c.GetDefaultParamInt("refresh_type", -1)
		network, _     = c.GetDefaultParamInt("network", networkUnknown)
	)
	// 非正常刷新方式 (Web 端、客户端老版本没有这个字段)
	if refreshType < 0 {
		return &exposureParam{
			RefreshType: 0,
			RefreshNum:  1,
			Network:     network,
		}
	}

	// decode marker
	markerMap := make(map[string]string)
	if marker != "" {
		markers := strings.Split(marker, ",")
		for _, m := range markers {
			item := strings.Split(m, ":")
			if len(item) != 2 {
				logger.Errorf("marker(%s) item error: %s", marker, item)
				// PASS
				continue
			}
			markerMap[item[0]] = item[1]
		}
	}

	var (
		num int64
		err error
	)

	if refreshNum, ok := markerMap["refresh_num"]; ok && refreshNum != "" {
		num, err = strconv.ParseInt(refreshNum, 10, 64)
		if err != nil {
			logger.WithFields(logger.Fields{
				"marker":      marker,
				"refresh_num": refreshNum,
				"network":     network,
			}).Error(err)
			// PASS
		} else {
			num++
		}
	}

	return &exposureParam{
		RefreshType: refreshType,
		RefreshNum:  num,
		Network:     network,
	}
}

func (param *exposureParam) marker() string {
	if param == nil {
		return ""
	}
	return fmt.Sprintf("refresh_num:%d", param.RefreshNum)
}

func newRecommendParams(c *handler.Context, ep *exposureParam) *biliai.RecommendParams {
	u := c.User()
	var userID int64
	if u != nil {
		userID = u.GetID()
	}
	equipment := c.Equip()
	if equipment.BUVID == "" {
		logger.WithFields(logger.Fields{"equip_id": equipment.EquipID, "ip": c.ClientIP(), "user_id": userID}).Error("buvid 为空")
		return nil
	}

	locationInfo := GetLocationInfo(c)
	personaID, sex := biliai.GetUserPersonaAndSex(c.UserContext(), userID, equipment.EquipID, equipment.BUVID)
	res := &biliai.RecommendParams{
		Cmd:            biliai.TianmaSceneLiveRecommends,
		MID:            userID,
		Buvid:          equipment.BUVID,
		RequestCnt:     biliai.MaxTopRoomCount,
		Timeout:        biliai.DefaultTimeout,
		DisplayID:      ep.RefreshNum,
		FreshType:      ep.RefreshType,
		Chid:           getChid(c),
		Model:          equipment.DeviceModel,
		Platform:       biliai.GetPlatform(equipment),
		Version:        equipment.AppVersion,
		Network:        GetNetworkType(ep.Network),
		Country:        locationInfo.CountryName,
		Province:       locationInfo.RegionName,
		City:           locationInfo.CityName,
		Sex:            int(sex),
		Persona:        int(personaID),
		FirstLoginTime: -1, // TODO: 首次登录时间暂时传 -1，后续需要传入正常值
		Ts:             goutil.TimeNow().Unix(),
	}
	return res
}

func getChid(c *handler.Context) string {
	return c.Request().Header.Get("channel")
}

// GetLocationInfo 获取地理位置信息
func GetLocationInfo(c *handler.Context) goclient.IPInfo {
	// 从 RPC 请求获取用户的地理位置信息
	equipment := c.Equip()
	sm := goutil.SmartUserContext{
		UID:         c.UserID(),
		IP:          c.ClientIP(),
		UserToken:   c.Token(),
		UserEquipID: equipment.EquipID,
		UserBUVID:   equipment.BUVID,
		UA:          c.UserAgent(),
		Req:         c.Request(),
	}
	locationInfo, err := goclient.GetIPInfo(sm, c.ClientIP())
	if err != nil {
		logger.WithFields(logger.Fields{"ip": c.ClientIP(), "user_id": c.UserID()}).Errorf("获取用户地理位置信息失败: %v", err)
		// PASS
	}
	return locationInfo
}

// BuildRecommendLogKey 构建 dataBus key
func BuildRecommendLogKey(userID int64, ip string) string {
	var keySuffix string

	if userID != 0 {
		keySuffix = strconv.FormatInt(userID, 10)
	} else {
		keySuffix = fmt.Sprintf(":%08x", util.CRC32(ip))
	}

	return keys.KeyRecomendExposureLog1.Format(keySuffix)
}

func buildRecommendsExposureLog(c *handler.Context, responseItems []biliai.ResponseItem, recommendTrackID string, userFeature string, ep *exposureParam) (key string, log map[string]interface{}) {
	equipment := c.Equip()
	log = map[string]interface{}{
		"user_id":      c.UserID(),
		"os":           equipment.OS,
		"equip_id":     equipment.EquipID,
		"buvid":        equipment.BUVID,
		"channel":      getChid(c),
		"app_version":  equipment.AppVersion,
		"user_agent":   c.UserAgent(),
		"ip":           c.ClientIP(),
		"network":      ep.Network,
		"refresh_type": ep.RefreshType,
		"refresh_num":  ep.RefreshNum,
		"create_time":  goutil.TimeNow().Unix(),
		"track_id":     recommendTrackID,
		"user_feature": userFeature,
		"env":          os.Getenv(util.EnvDeploy),
		"scene":        biliai.RecommendSceneLiveRecommendTop,
	}

	showList := make([]map[string]interface{}, len(responseItems))
	for i, item := range responseItems {
		showList[i] = map[string]interface{}{
			"id":         item.ID,
			"goto":       biliai.TianmaGotoTypeLive,
			"pos":        i + 1,
			"source":     item.Source,
			"av_feature": item.AvFeature,
			"attr":       item.Attr,
		}
		if item.OldPos != nil {
			showList[i]["old_pos"] = item.OldPos
		}
	}
	log["showlist"] = showList
	logger.Debugf("buildRecommendsExposureLog: %v", log)
	key = BuildRecommendLogKey(c.UserID(), c.ClientIP())
	return key, log
}

// ListByRoomIDs 根据直播间 ID 列表获取直播间信息，按列表顺序返回
// TODO: 这部分代码应该封装在 room 包或 mongodb 的包中
func ListByRoomIDs(roomIDs []int64) ([]*room.Simple, error) {
	simpleList, err := room.ListSimples(
		bson.M{
			"room_id":     bson.M{"$in": roomIDs},
			"status.open": room.StatusOpenTrue,
			"limit":       bson.M{"$exists": false},
		},
		options.Find().SetProjection(mongodb.NewProjection(
			"room_id, catalog_id, custom_tag_id, name, announcement, creator_id, "+
				"creator_username, statistics, status, cover")),
		&room.FindOptions{FindCreator: true, FindCatalogInfo: true, FindCustomTag: true},
	)
	if err != nil {
		return nil, err
	}

	roomMap := make(map[int64]*room.Simple, len(simpleList))
	for _, r := range simpleList {
		roomMap[r.RoomID] = r
	}

	// 按照 roomIDs 的顺序构建返回列表
	result := make([]*room.Simple, 0, len(roomMap))
	for _, roomID := range roomIDs {
		if r, exists := roomMap[roomID]; exists {
			result = append(result, r)
		}
	}

	return result, nil
}

// 获取运营侧数据用于兜底
func listRemedyRoom() ([]lre.Model, error) {
	recommends, err := listSchedule(goutil.TimeNow())
	if err != nil || len(recommends) == 0 {
		return nil, err
	}

	// 去重
	uniqueRecommends := make(map[int64]struct{}, len(recommends))
	uniqueList := make([]lre.Model, 0, len(recommends))
	for _, recommend := range recommends {
		if _, ok := uniqueRecommends[recommend.ID]; !ok {
			uniqueRecommends[recommend.ID] = struct{}{}
			uniqueList = append(uniqueList, recommend)
		}
	}

	// 随机排序
	rand.Shuffle(len(uniqueList), func(i, j int) {
		uniqueList[i], uniqueList[j] = uniqueList[j], uniqueList[i]
	})

	// 提取所有补位房间的 ID
	fillingRoomIDs := make([]int64, 0, len(uniqueList))
	for _, r := range uniqueList {
		fillingRoomIDs = append(fillingRoomIDs, r.ElementID)
	}

	// 获取有效的直播间信息
	validRooms, err := ListByRoomIDs(fillingRoomIDs)
	if err != nil {
		logger.Errorf("获取补位房间失败: %v", err)
		return []lre.Model{}, nil
	}
	validRoomMap := make(map[int64]bool, len(validRooms))
	for _, r := range validRooms {
		validRoomMap[r.RoomID] = true
	}

	// 过滤未开播的房间
	filteredList := make([]lre.Model, 0, len(uniqueList))
	for _, r := range uniqueList {
		if validRoomMap[r.ElementID] {
			filteredList = append(filteredList, r)
		}
	}

	return filteredList, nil
}

var getTianmaRecommendFunc = biliai.GetTianmaRecommend
var newRecommendParamsFunc = newRecommendParams
var listRemedyRoomFunc = listRemedyRoom
var listByRoomIDsFunc = ListByRoomIDs

// ActionTop “推荐直播（接入推荐天马算法）” 模块接口
/**
 * @api {get} /api/v2/recommended/top 获取“推荐直播（接入推荐天马算法）”列表
 * @apiDescription app 首页 banner 下的“推荐直播”模块和网页直播间页面的“推荐直播”模块
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/recommended
 *
 * @apiParam {String} marker 标记，需要带上上一响应的 marker
 * @apiParam {number=0~4} [network=0] 用户当前网络状况 （同埋点上报参数约定值，即：0: UNKNOWN; 1: WIFI; 2: CELLULAR; 3: OFFLINE; 4: OTHERNET）
 * @apiParam {number=0,1} [refresh_type=0] 刷新方式
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info":{
 *       "marker": "refresh_num:1",  // 若未传 refresh_type 则不下发该字段
 *       "rooms": [{
 *         "track_id": "ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0",
 *         "trace": "{\"track_id\":\"ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0\",\"refresh_type\":1,\"refresh_num\":3,\"attr\":1}",
 *         "room_id": 22489473,
 *         "name": "233333333",
 *         "creator_username": "bless",
 *         "cover_url": "http://static.example.com/cover.png",
 *         "catalog_id": 10,
 *         "catalog_name": "娱乐",
 *         "catalog_color": "#D68DFE",
 *         "custom_tag": {
 *           "tag_id": 10001,
 *           "tag_name": "腹黑青叔"
 *         },
 *         "statistics": {
 *           "accumulation": 123,
 *           ...
 *         },
 *         "creator_iconurl": "https://static-test.missevan.com/profile/201507/30/8f5fac6a968c59e0125334b941996ead215147.png",
 *         "status": {
 *           "open": 1
 *           ...
 *         },
 *         "from": "noble", // noble: 来自贵族推荐，默认不返回该字段
 *         "recommend_id": 1
 *       }]
 *     }
 *   }
 *
 * @apiError (500) {Number} code 100010500
 * @apiError (500) {String} info 服务器内部错误
 *
 */
func ActionTop(c *handler.Context) (handler.ActionResponse, error) {
	var (
		recommendResp    *biliai.RecommendResponse // 天马推荐算法响应
		responseItems    []biliai.ResponseItem     // 直播间数据项
		userFeature      string                    // 用户特征
		recommendTrackID string
		remedyRooms      []lre.Model // 兜底直播间
		err              error
	)
	ep := newExposureParam(c)

	queryIDs := make([]int64, 0, biliai.MaxTopRoomCount) // 合并后的直播间 IDs
	excludeRoomIDs := room.OpenListExcludeRoomIDs()      // 需要移除的测试直播间
	nr, _ := livenoblerecommend.CurrentRecommend()       // 神话推荐房间

	if c.Equip().FromApp {
		params := newRecommendParamsFunc(c, ep)
		if params != nil {
			recommendResp, err = getTianmaRecommendFunc(params)
			if err != nil {
				logger.Error(err)
				// PASS
			}
		}
	}

	returnItems := make([]biliai.ResponseItem, 0, biliai.MaxTopRoomCount)
	if recommendResp != nil {
		responseItems = recommendResp.Data
		userFeature = recommendResp.UserFeature

		// 天马推荐直播间 ID 列表
		for i, item := range responseItems {
			if recommendTrackID == "" {
				recommendTrackID = item.TrackID
			}
			responseItems[i].Attr = RecommendAttrTianma
			// 记录直播间的原始位置
			responseItems[i].OldPos = goutil.NewInt(i + 1)
		}

		// 触发补位标记
		needFill := false
		existingIDs := make(map[int64]bool, len(responseItems))
		for _, item := range responseItems { // 检查是否需要补位，将待补位的房间 ID 设置为 0
			if item.ID == 0 {
				needFill = true
				continue
			}
			// TODO: 不处理开播状态，具体情况后续和产品确认后补齐
			existingIDs[item.ID] = true
		}

		// 调用运营侧接口获取补位数据
		var fillingRooms []lre.Model
		if needFill {
			fillingRooms, err = listRemedyRoomFunc()
			if err != nil {
				logger.Errorf("获取补位数据失败: %v", err)
				// PASS
			}
		}

		fillingRoomsIndex := 0
		for index, item := range responseItems {
			for item.ID == 0 && fillingRoomsIndex < len(fillingRooms) {
				fillingRoom := fillingRooms[fillingRoomsIndex]
				itemID := fillingRoom.ElementID
				if existingIDs[itemID] {
					fillingRoomsIndex++
					continue
				}
				item.ID = itemID
				item.Name = fillingRoom.Name
				item.Attr = RecommendAttrSupplement
				item.RecommendID = fillingRoom.ID
				item.TrackID = "" // 非算法推荐项，需要将 track_id 置为空
				fillingRoomsIndex++
				break
			}
			responseItems[index] = item
			returnItems = append(returnItems, item)
		}
	} else {
		remedyRooms, err = listRemedyRoomFunc()
		if err != nil {
			logger.Error(err)
			return &topResp{Rooms: make([]*RoomWithTrace, 0), Marker: ep.marker()}, nil
		}
		for _, remedyRoom := range remedyRooms {
			item := biliai.NewResponseItem(biliai.TianmaGotoTypeLive, remedyRoom.ElementID)
			item.Name = remedyRoom.Name
			item.RecommendID = remedyRoom.ID
			item.Attr = RecommendAttrSupplement
			returnItems = append(returnItems, item)
		}
	}

	newReturnItems := make([]biliai.ResponseItem, 0, biliai.MaxTopRoomCount)
	if nr != nil {
		// 神话推荐房间存在则优先插入首位
		nrItem := biliai.NewResponseItem(biliai.TianmaGotoTypeLive, nr.RoomID)
		nrItem.Attr = RecommendAttrSupplement
		nrItem.TrackID = "" // 非算法推荐项，需要将 track_id 置为空
		nrItem.Trace = ep.getItemTrace(nrItem)
		newReturnItems = append(newReturnItems, nrItem)
	}
	for _, item := range returnItems {
		if len(newReturnItems) >= len(returnItems) { // 下发的卡片数最多只能和算法侧提供的卡片数一致
			break
		}

		if item.ID == 0 || goutil.HasElem(excludeRoomIDs, item.ID) || item.Goto != biliai.TianmaGotoTypeLive { // 跳过补位失败的直播间
			continue
		}
		item.Trace = ep.getItemTrace(item)
		if nr != nil && item.ID == nr.RoomID {
			// 神话推荐房间存在且在算法推荐列表中，则置换首位，保留 track_id
			newReturnItems[0] = item
		} else {
			newReturnItems = append(newReturnItems, item)
		}
	}

	for _, item := range newReturnItems {
		queryIDs = append(queryIDs, item.ID)
	}

	// 获取直播间信息
	simpleList, err := listByRoomIDsFunc(queryIDs)
	if err != nil || len(simpleList) < 3 { // 直播间数量小于三个不进行下发
		if err != nil {
			logger.Errorf("获取直播间信息失败: %v", err)
		}
		return &topResp{Rooms: make([]*RoomWithTrace, 0), Marker: ep.marker()}, nil
	}

	// 来自 APP 的请求需要上报
	if c.Equip().FromApp {
		// 上报前检查位置是否发生变更
		for i, item := range newReturnItems {
			// 如果 OldPos 存在且不等于当前的位置，保留 OldPos 原值
			if item.OldPos != nil && *item.OldPos != (i+1) {
				continue
			}
			// 如果 OldPos 与当前的位置相同，则置空
			newReturnItems[i].OldPos = nil
		}
		key, log := buildRecommendsExposureLog(c, newReturnItems, recommendTrackID, userFeature, ep)
		goutil.Go(func() {
			err = service.DatabusLogSend(key, log)
			if err != nil {
				logger.Errorf("埋点上报失败: %v", err)
				// PASS
			}
		})
	}

	roomList := make([]*RoomWithTrace, 0, len(simpleList))
	newReturnItemMap := goutil.ToMap(newReturnItems, "ID").(map[int64]biliai.ResponseItem)
	for i := range simpleList {
		r := &RoomWithTrace{
			Simple: simpleList[i],
		}
		if nr != nil && r.RoomID == nr.RoomID {
			r.From = room.RecommendFromVip
		}
		if item, ok := newReturnItemMap[simpleList[i].RoomID]; ok {
			if item.Attr == RecommendAttrSupplement {
				r.RecommendID = item.RecommendID
				// 优先使用运营侧提供的直播间名称
				if item.Name != "" {
					r.Name = item.Name
				}
			}
			r.TrackID = item.TrackID
			r.Trace = item.Trace
		}
		roomList = append(roomList, r)
	}

	res := &topResp{
		Marker: ep.marker(),
		Rooms:  roomList,
	}

	if c.Equip().FromApp {
		res.filterRoomsCover()
	}

	return res, nil
}
