package recommended

import (
	"encoding/json"
	"fmt"

	lre "github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service/biliai"
)

// getRoomsInfo 用于模拟 mongodb 返回数据
func getRoomsInfo(roomIDs []int64) ([]*room.Simple, error) {
	res := make([]*room.Simple, 0, len(roomIDs))
	for _, roomID := range roomIDs {
		if roomID == 0 {
			continue
		}
		roomName := fmt.Sprintf("mongodb-name-%d", roomID)
		simple := &room.Simple{
			RoomID: roomID,
			Name:   roomName,
		}
		res = append(res, simple)
	}
	return res, nil
}

// tianmaRecommendCase1 模拟 tianma 推荐算法响应 code = 0
// 算法推荐 10 个直播间
func tianmaRecommendCase1(params *biliai.RecommendParams) (*biliai.RecommendResponse, error) {
	data := make([]biliai.ResponseItem, 0, biliai.MaxTopRoomCount)
	// 模拟的直播间 ID 列表
	roomIDs := []int64{0, 1001, 0, 1002, 0, 1003, 0, 1004, 0, 1005}
	for i := 0; i < len(roomIDs); i++ {
		data = append(data, biliai.ResponseItem{
			AvFeature:  "some string",
			Goto:       "live",
			ID:         roomIDs[i],
			RcmdReason: nil,
			Source:     "",
			Tid:        0,
			TrackID:    "maoer_home_0.router-main-1561512-2q46r.1732179280941.605",
		})
	}
	return &biliai.RecommendResponse{
		Data:        data,
		UserFeature: "user-feature-1",
		Debug:       nil,
		SceneType:   0,
		Sid:         "0",
	}, nil
}

// tianmaRecommendCase2 模拟 tianma 推荐算法响应 code = -3
// 算法推荐 8 个直播间
// 以 roomID = 0 填充补足 10 个
func tianmaRecommendCase2(params *biliai.RecommendParams) (*biliai.RecommendResponse, error) {
	data := make([]biliai.ResponseItem, 0, biliai.MaxTopRoomCount)
	// 模拟的直播间 ID 列表
	// 算法返回的直播间长度是 8 业务侧需要补足长度到 10，用 ID 为 0 表示运营侧补位
	roomIDs := []int64{0, 1001, 0, 1002, 0, 1003, 0, 1004, 0, 0}
	for i := 0; i < len(roomIDs); i++ {
		data = append(data, biliai.ResponseItem{
			AvFeature:  "some string",
			Goto:       "live",
			ID:         roomIDs[i],
			RcmdReason: nil,
			Source:     "",
			Tid:        0,
			TrackID:    "maoer_home_0.router-main-1561512-2q46r.1732179280941.605",
		})
	}
	return &biliai.RecommendResponse{
		Data:        data,
		UserFeature: "user-feature-1",
		Debug:       nil,
		SceneType:   0,
		Sid:         "0",
	}, nil
}

// tianmaRecommendCase2 模拟 tianma 推荐算法响应 code = -77
// 算法推荐 0 个直播间
// 以 roomID = 0 填充补足 10 个
func tianmaRecommendCase3(params *biliai.RecommendParams) (*biliai.RecommendResponse, error) {
	data := make([]biliai.ResponseItem, 0, biliai.MaxTopRoomCount)
	// 模拟的直播间 ID 列表
	// 算法返回的直播间长度是 8 业务侧需要补足长度到 10，用 ID 为 0 表示运营侧补位
	roomIDs := []int64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0}
	for i := 0; i < len(roomIDs); i++ {
		data = append(data, biliai.ResponseItem{
			AvFeature:  "some string",
			Goto:       "live",
			ID:         roomIDs[i],
			RcmdReason: nil,
			Source:     "",
			Tid:        0,
			TrackID:    "maoer_home_0.router-main-1561512-2q46r.1732179280941.605",
		})
	}
	return &biliai.RecommendResponse{
		Data:        data,
		UserFeature: "user-feature-1",
		Debug:       nil,
		SceneType:   0,
		Sid:         "0",
	}, nil
}

// remedyRoomCase1 用于模拟运营侧的返回 10 个补位数据的场景
func remedyRoomCase1() ([]lre.Model, error) {
	res := make([]lre.Model, 0, biliai.MaxTopRoomCount)
	// 返回模拟的补位数据
	elementIDs := []int64{2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010}
	for i := 1; i <= len(elementIDs); i++ {
		roomName := fmt.Sprintf("运营侧补位房间-%d", elementIDs[i-1])
		model := lre.Model{
			ID:        int64(i),
			ElementID: elementIDs[i-1],
			Attribute: lre.Attribute{
				Name: roomName,
			},
		}
		res = append(res, model)
	}
	return res, nil
}

// remedyRoomCase2 用于模拟运营侧的返回 4 个补位数据的场景
func remedyRoomCase2() ([]lre.Model, error) {
	res := make([]lre.Model, 0, biliai.MaxTopRoomCount)
	// 返回模拟的补位数据
	elementIDs := []int64{2001, 2002, 2003, 2004}
	for i := 1; i <= len(elementIDs); i++ {
		roomName := fmt.Sprintf("运营侧补位房间-%d", elementIDs[i-1])
		model := lre.Model{
			ID:        int64(i),
			ElementID: elementIDs[i-1],
			Attribute: lre.Attribute{
				Name: roomName,
			},
		}
		res = append(res, model)
	}
	return res, nil
}

// remedyRoomCase3 用于模拟运营侧的返回 0 个补位数据的场景
func remedyRoomCase3() ([]lre.Model, error) {
	res := make([]lre.Model, 0, biliai.MaxTopRoomCount)
	// 返回模拟的补位数据
	var elementIDs []int64
	for i := 1; i <= len(elementIDs); i++ {
		roomName := fmt.Sprintf("运营侧补位房间-%d", elementIDs[i-1])
		model := lre.Model{
			ID:        int64(i),
			ElementID: elementIDs[i-1],
			Attribute: lre.Attribute{
				Name: roomName,
			},
		}
		res = append(res, model)
	}
	return res, nil
}

// remedyRoomCase4 用于模拟运营侧的返回 10 个补位数据
// 补位数据缺少直播间名称需要采用 mongodb 返回直播间名称
func remedyRoomCase4() ([]lre.Model, error) {
	res := make([]lre.Model, 0, biliai.MaxTopRoomCount)
	// 返回模拟的补位数据
	elementIDs := []int64{2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010}
	for i := 1; i <= len(elementIDs); i++ {
		model := lre.Model{
			ID:        int64(i),
			ElementID: elementIDs[i-1],
		}
		res = append(res, model)
	}
	return res, nil
}

// remedyRoomCase5 用于模拟运营侧的返回 7 个补位数据
// 补位数据存在和算法推荐重复项的场景
func remedyRoomCase5() ([]lre.Model, error) {
	res := make([]lre.Model, 0, biliai.MaxTopRoomCount)
	// 返回模拟的补位数据
	elementIDs := []int64{2001, 2002, 2003, 2004, 1001, 1002, 1003}
	for i := 1; i <= len(elementIDs); i++ {
		roomName := fmt.Sprintf("运营侧补位房间-%d", elementIDs[i-1])
		model := lre.Model{
			ID:        int64(i),
			ElementID: elementIDs[i-1],
			Attribute: lre.Attribute{
				Name: roomName,
			},
		}
		res = append(res, model)
	}
	return res, nil
}

/**
 * expectedRespCase1 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、运营侧补位数据采用 remedyRoomCase1
 * 3、神话房间为空
 */
func expectedRespCase1() *topResp {
	roomIDs := []int64{2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004, 2005, 1005}
	roomName := []string{
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"运营侧补位房间-2002",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
		"运营侧补位房间-2005",
		"mongodb-name-1005",
	}
	attr := []int{2, 1, 2, 1, 2, 1, 2, 1, 2, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4, 5}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase2 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、运营侧补位数据采用 remedyRoomCase2
 * 3、神话房间为空
 */
func expectedRespCase2() *topResp {
	roomIDs := []int64{2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004, 1005}
	roomName := []string{
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"运营侧补位房间-2002",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
		"mongodb-name-1005",
	}
	attr := []int{2, 1, 2, 1, 2, 1, 2, 1, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase3 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、运营侧补位数据采用 remedyRoomCase3
 * 3、神话房间为空
 */
func expectedRespCase3() *topResp {
	roomIDs := []int64{1001, 1002, 1003, 1004, 1005}
	roomName := []string{
		"mongodb-name-1001",
		"mongodb-name-1002",
		"mongodb-name-1003",
		"mongodb-name-1004",
		"mongodb-name-1005",
	}
	attr := []int{1, 1, 1, 1, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase4 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase2
 * 2、运营侧补位数据采用 remedyRoomCase1
 * 3、神话房间为空
 */
func expectedRespCase4() *topResp {
	roomIDs := []int64{2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004, 2005, 2006}
	roomName := []string{
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"运营侧补位房间-2002",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
		"运营侧补位房间-2005",
		"运营侧补位房间-2006",
	}
	attr := []int{2, 1, 2, 1, 2, 1, 2, 1, 2, 2}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4, 5, 6}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase5 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase2
 * 2、补位数据采用 remedyRoomCase2
 * 3、神话房间为空
 */
func expectedRespCase5() *topResp {
	roomIDs := []int64{2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004}
	roomName := []string{
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"运营侧补位房间-2002",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
	}
	attr := []int{2, 1, 2, 1, 2, 1, 2, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase6 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase2
 * 2、补位数据采用 remedyRoomCase3
 * 3、神话房间为空
 */
func expectedRespCase6() *topResp {
	roomIDs := []int64{1001, 1002, 1003, 1004}
	roomName := []string{
		"mongodb-name-1001",
		"mongodb-name-1002",
		"mongodb-name-1003",
		"mongodb-name-1004",
	}
	attr := []int{1, 1, 1, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase7 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase3
 * 2、补位数据采用 remedyRoomCase1
 * 3、神话房间为空
 */
func expectedRespCase7() *topResp {
	roomIDs := []int64{2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010}
	roomName := []string{
		"运营侧补位房间-2001",
		"运营侧补位房间-2002",
		"运营侧补位房间-2003",
		"运营侧补位房间-2004",
		"运营侧补位房间-2005",
		"运营侧补位房间-2006",
		"运营侧补位房间-2007",
		"运营侧补位房间-2008",
		"运营侧补位房间-2009",
		"运营侧补位房间-2010",
	}
	attr := []int{2, 2, 2, 2, 2, 2, 2, 2, 2, 2}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase8 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase3
 * 2、补位数据采用 remedyRoomCase2
 * 3、神话房间为空
 */
func expectedRespCase8() *topResp {
	roomIDs := []int64{2001, 2002, 2003, 2004}
	roomName := []string{
		"运营侧补位房间-2001",
		"运营侧补位房间-2002",
		"运营侧补位房间-2003",
		"运营侧补位房间-2004",
	}
	attr := []int{2, 2, 2, 2}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase9 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase3
 * 2、补位数据采用 remedyRoomCase3
 * 3、神话房间为空
 */
func expectedRespCase9() *topResp {
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  []*RoomWithTrace{},
	}
	return expectedTopResp
}

/**
 * expectedRespCase10 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、补位数据采用 remedyRoomCase1
 * 3、神话房间不为空，且神话推荐房间不在推荐列表中，神话房间 roomID 为 3192516
 */
func expectedRespCase10() *topResp {
	roomIDs := []int64{3192516, 2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004, 2005}
	roomName := []string{
		"mongodb-name-3192516",
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"运营侧补位房间-2002",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
		"运营侧补位房间-2005",
	}
	attr := []int{2, 2, 1, 2, 1, 2, 1, 2, 1, 2}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4, 5}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			if roomIDs[i] != 3192516 { // 神话房间不含 recommendID，跳过神话房间
				rwt.RecommendID = recommendIDs[recommendIndex]
				recommendIndex++
			}
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase11 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、补位数据采用 remedyRoomCase1
 * 3、神话房间不为空，且神话推荐房间在推荐列表中，神话房间 roomID 为 1002
 */
func expectedRespCase11() *topResp {
	roomIDs := []int64{2002, 2001, 1001, 1002, 2003, 1003, 2004, 1004, 2005, 1005}
	roomName := []string{
		"运营侧补位房间-2002",
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
		"运营侧补位房间-2005",
		"mongodb-name-1005",
	}
	attr := []int{2, 2, 1, 1, 2, 1, 2, 1, 2, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{2, 1, 3, 4, 5} // 神话推荐房间强插首位导致元素乱序
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase12 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、补位数据采用 remedyRoomCase4
 * 3、神话房间为空
 */
func expectedRespCase12() *topResp {
	roomIDs := []int64{2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004, 2005, 1005}
	roomName := []string{
		"mongodb-name-2001",
		"mongodb-name-1001",
		"mongodb-name-2002",
		"mongodb-name-1002",
		"mongodb-name-2003",
		"mongodb-name-1003",
		"mongodb-name-2004",
		"mongodb-name-1004",
		"mongodb-name-2005",
		"mongodb-name-1005",
	}
	attr := []int{2, 1, 2, 1, 2, 1, 2, 1, 2, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4, 5}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}

/**
 * expectedRespCase13 为以下场景的预期返回
 * 1、推荐算法侧数据采用 tianmaRecommendCase1
 * 2、补位数据采用 remedyRoomCase5
 * 3、神话房间为空
 */
func expectedRespCase13() *topResp {
	roomIDs := []int64{2001, 1001, 2002, 1002, 2003, 1003, 2004, 1004, 1005}
	roomName := []string{
		"运营侧补位房间-2001",
		"mongodb-name-1001",
		"运营侧补位房间-2002",
		"mongodb-name-1002",
		"运营侧补位房间-2003",
		"mongodb-name-1003",
		"运营侧补位房间-2004",
		"mongodb-name-1004",
		"mongodb-name-1005",
	}
	attr := []int{2, 1, 2, 1, 2, 1, 2, 1, 1, 1}
	rwts := make([]*RoomWithTrace, 0, len(roomIDs))
	recommendIDs := []int64{1, 2, 3, 4}
	recommendIndex := 0
	for i := 0; i < len(roomIDs); i++ {
		t := Trace{
			RefreshType: 1,
			RefreshNum:  1,
			Attr:        attr[i],
		}
		traceStr, _ := json.Marshal(t)
		simple := &room.Simple{
			RoomID: roomIDs[i],
			Name:   roomName[i],
		}
		rwt := &RoomWithTrace{
			Simple: simple,
			Trace:  string(traceStr),
		}
		if attr[i] == 2 {
			rwt.RecommendID = recommendIDs[recommendIndex]
			recommendIndex++
			// 运营侧的 TrackID 为空
			rwt.TrackID = ""
		} else {
			rwt.TrackID = "maoer_home_0.router-main-1561512-2q46r.1732179280941.605"
		}
		rwts = append(rwts, rwt)
	}
	expectedTopResp := &topResp{
		Marker: "refresh_num:1",
		Rooms:  rwts,
	}
	return expectedTopResp
}
