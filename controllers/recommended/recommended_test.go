package recommended

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/live-service/models/livenoblerecommend"
	"github.com/MiaoSiLa/live-service/models/mysql/liverecommendedelements"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/biliai"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/bilibili/tianma"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const openingRoomID = int64(3192516)
const mockNobleRoomID = int64(2002) // 用于直播推荐接口 mock 神话推荐直播间
var openingRoomCreatorID = int64(516)

func TestMain(m *testing.M) {
	config.InitTest()
	service.InitTest(true)
	handler.SetMode(handler.TestMode)

	openingRoomCreatorID, _ = room.FindCreatorID(openingRoomID)

	os.Exit(m.Run())
}

func TestActionHomepage(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := room.Update(1003, bson.M{"custom_tag_id": 10001})
	require.NoError(err)

	c := handler.NewTestContext("GET", "/recommended/homepage", false, nil)
	r, err := ActionHomepage(c)
	require.NoError(err)
	data := r.([]liverecommendedelements.Room)
	var count int
	for _, v := range data {
		count++
		assert.NotEmpty(v.CoverURL)
		assert.NotZero(v.CatalogID)
		assert.NotEmpty(v.CatalogName)
		if v.RoomID == 1003 {
			assert.NotNil(v.CustomTag)
		}
	}
	assert.EqualValues(3, count)

	c = handler.NewTestContext("GET", "/recommended/homepage", false, nil)
	c.Equip().FromApp = true
	c.Equip().OS = goutil.IOS
	c.Equip().AppVersion = "6.0.6"
	r, err = ActionHomepage(c)
	require.NoError(err)
	data = r.([]liverecommendedelements.Room)
	require.Equal(3, len(data))
	assert.Equal(data[0].CustomTag.TagName, data[0].CatalogName)
}

func TestAddRecommends(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	old := sMembersCandidates
	defer func() {
		sMembersCandidates = old
	}()
	sMembersCandidates = func() ([]string, error) {
		return []string{strconv.FormatInt(openingRoomCreatorID, 10)}, nil
	}

	_, err := room.Update(3192516, bson.M{"custom_tag_id": 10001})
	require.NoError(err)

	r, err := addRecommends(3, -13)
	require.NoError(err)
	assert.Len(r, 1)
	assert.NotEmpty(r[0].CatalogName)
	require.NotEmpty(r[0].CustomTag)
	assert.EqualValues(10001, r[0].CustomTag.TagID)
	assert.Equal("test10001", r[0].CustomTag.TagName)

	r, err = addRecommends(3, openingRoomCreatorID)
	require.NoError(err)
	assert.Len(r, 0)
}

func TestActionTop(t *testing.T) {
	// 公共的测试环境设置
	setupTestContext := func() (*handler.Context, func()) {
		c := handler.NewTestContext("GET", "/recommended/top?marker=refresh_num:0&network=1&refresh_type=1", false, nil)
		c.Equip().FromApp = true // 默认来自 APP 请求

		// 清理工作
		return c, func() {
			// 恢复所有的 mock 函数
			newRecommendParamsFunc = newRecommendParams
			getTianmaRecommendFunc = biliai.GetTianmaRecommend
			listRemedyRoomFunc = listRemedyRoom
			listByRoomIDsFunc = ListByRoomIDs

			// 清理缓存，确保每个测试用例不会受到缓存数据的影响
			now := goutil.TimeNow()
			service.Cache5Min.Delete(livenoblerecommend.KeyCurrentRecommend(now))
		}
	}

	testCases := []struct {
		name                string
		mockRecommendParams func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams
		mockTianmaData      func(params *biliai.RecommendParams) (*biliai.RecommendResponse, error)
		mockRemedyData      func() ([]liverecommendedelements.Model, error)
		mockMongodbData     func([]int64) ([]*room.Simple, error)
		expectedData        func() *topResp
	}{
		{
			name:                "case 1",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase1,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase1,
		},
		{
			name:                "case 2",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase2,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase2,
		},
		{
			name:                "case 3",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase3,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase3,
		},
		{
			name:                "case 4",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase2,
			mockRemedyData:      remedyRoomCase1,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase4,
		},
		{
			name:                "case 5",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase2,
			mockRemedyData:      remedyRoomCase2,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase5,
		},
		{
			name:                "case 6",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase2,
			mockRemedyData:      remedyRoomCase3,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase6,
		},
		{
			name:                "case 7",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase3,
			mockRemedyData:      remedyRoomCase1,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase7,
		},
		{
			name:                "case 8",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase3,
			mockRemedyData:      remedyRoomCase2,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase8,
		},
		{
			name:                "case 9",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase3,
			mockRemedyData:      remedyRoomCase3,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase9,
		},
		{
			name:                "case 10",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase1,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase10,
		},
		{
			name:                "case 11",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase1,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase11,
		},
		{
			name:                "case 12",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase4,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase12,
		},
		{
			name:                "case 13",
			mockRecommendParams: func(c *handler.Context, ep *exposureParam) *biliai.RecommendParams { return &biliai.RecommendParams{} },
			mockTianmaData:      tianmaRecommendCase1,
			mockRemedyData:      remedyRoomCase5,
			mockMongodbData:     getRoomsInfo,
			expectedData:        expectedRespCase13,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			c, cleanup := setupTestContext()
			defer cleanup()
			// 更换 mock 数据
			newRecommendParamsFunc = tc.mockRecommendParams
			getTianmaRecommendFunc = tc.mockTianmaData
			listRemedyRoomFunc = tc.mockRemedyData
			listByRoomIDsFunc = tc.mockMongodbData

			if tc.name == "case 10" {
				// 添加神话推荐贵族缓存
				now := goutil.TimeNow()
				key := livenoblerecommend.KeyCurrentRecommend(now)
				service.Cache5Min.Set(key,
					&livenoblerecommend.NobleRecommend{RoomID: openingRoomID}, 0)
			}
			if tc.name == "case 11" {
				// 添加神话推荐贵族缓存
				now := goutil.TimeNow()
				key := livenoblerecommend.KeyCurrentRecommend(now)
				service.Cache5Min.Set(key,
					&livenoblerecommend.NobleRecommend{RoomID: mockNobleRoomID}, 0)
			}

			r, err := ActionTop(c)
			assert.NoError(err)

			resp, ok := r.(*topResp)
			assert.True(ok, "返回的结果不是 *topResp 类型")

			// 校验 Marker 字段
			expectedResp := tc.expectedData()
			assert.Equal(expectedResp.Marker, resp.Marker, "Marker 字段不匹配")

			// 校验 Rooms 数组长度
			assert.Equal(len(expectedResp.Rooms), len(resp.Rooms), "Rooms 数组长度不匹配")

			// 校验 Rooms 数组中的每个元素
			for i := 0; i < len(expectedResp.Rooms); i++ {
				assert.Equal(expectedResp.Rooms[i].Simple.RoomID, resp.Rooms[i].Simple.RoomID, fmt.Sprintf("RoomID 第 %d 个元素不匹配", i+1))
				assert.Equal(expectedResp.Rooms[i].Simple.Name, resp.Rooms[i].Simple.Name, fmt.Sprintf("第 %d 个房间的名称不匹配", i+1))
				assert.Equal(expectedResp.Rooms[i].Trace, resp.Rooms[i].Trace, fmt.Sprintf("第 %d 个房间的 Trace 不匹配", i+1))
				assert.Equal(expectedResp.Rooms[i].TrackID, resp.Rooms[i].TrackID, fmt.Sprintf("第 %d 个房间的 TrackID 不匹配", i+1))
				assert.Equal(expectedResp.Rooms[i].RecommendID, resp.Rooms[i].RecommendID, fmt.Sprintf("第 %d 个房间的 RecommendID 不匹配", i+1))
			}
		})
	}
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	h := Handler()
	assert.Equal("recommended", h.Name)
	assert.Empty(tutil.KeyExists(tutil.Actions, h, "homepage", "top"))
}

func TestConvertOpenedSimpleRooms(t *testing.T) {
	assert := assert.New(t)
	assert.Empty(ConvertOpenedSimpleRooms(nil))
	in := []*room.Simple{{}}
	out := ConvertOpenedSimpleRooms(in)
	assert.Len(out, 1)
	assert.Equal(out[0].Status.Open, 1)
	assert.NotEmpty(out[0].CoverURL)
}

func TestHasHypnosisRoom(t *testing.T) {
	assert := assert.New(t)
	orgClock := config.Conf.Params.Recommended.HypnosisDisableClocks
	defer func() {
		config.Conf.Params.Recommended.HypnosisDisableClocks = orgClock
	}()
	assert.False(HasHypnosisRoom())
	config.Conf.Params.Recommended.HypnosisDisableClocks = nil
	assert.True(HasHypnosisRoom())
}

func TestReadTopOpened(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	_, err := room.Update(100000017, bson.M{"custom_tag_id": 10001})
	require.NoError(err)

	rooms, err := ReadTopOpened(1)
	require.NoError(err)
	require.Len(rooms, 1)
	assert.NotEmpty(rooms[0].CatalogName)
	require.NotEmpty(rooms[0].CustomTag)
	assert.EqualValues(10001, rooms[0].CustomTag.TagID)
	assert.Equal("test10001", rooms[0].CustomTag.TagName)

	id := rooms[0].RoomID
	rooms2, err := ReadTopOpened(1, id)
	require.NoError(err)
	assert.NotEqual(rooms, rooms2)
	assert.NotEmpty(rooms[0].CatalogName)
}

func TestNewExposureParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "/recommended/top?marker=refresh_num:1", false, nil)
	param := newExposureParam(c)
	assert.Nil(param)

	c = handler.NewTestContext(http.MethodGet, "/recommended/top?refresh_type=0", false, nil)
	param = newExposureParam(c)
	require.NotNil(param)
	assert.EqualValues(1, param.RefreshNum)
	assert.EqualValues(0, param.RefreshType)

	c = handler.NewTestContext(http.MethodGet, "/recommended/top?marker=refresh_num:1&refresh_type=1", false, nil)
	param = newExposureParam(c)
	require.NotNil(param)
	assert.EqualValues(2, param.RefreshNum)
	assert.EqualValues(1, param.RefreshType)
}

func TestExposureParam_marker(t *testing.T) {
	assert := assert.New(t)

	param := &exposureParam{
		RefreshType: 1,
		RefreshNum:  2,
	}
	assert.Equal("refresh_num:2", param.marker())
}

func TestExposureParam_traceID(t *testing.T) {
	// TODO: trace_id 目前返回的是空字符串，后续补充逻辑后再补充测试
}

func TestGetNetworkType(t *testing.T) {
	assert := assert.New(t)

	// 测试未知网络类型 (networkUnknown)
	t.Run("Network Unknown", func(t *testing.T) {
		result := GetNetworkType(networkUnknown)
		assert.Equal(tianma.UnknownStringValue, result, "预期未知网络类型返回空字符串")
	})

	// 测试 WiFi 网络
	t.Run("Network Wifi", func(t *testing.T) {
		result := GetNetworkType(networkWifi)
		assert.Equal(tianma.NetworkWifi, result, "预期 WiFi 网络返回 'WIFI'")
	})

	// 测试蜂窝网络
	t.Run("Network Cellular", func(t *testing.T) {
		result := GetNetworkType(networkCellular)
		assert.Equal(tianma.NetworkCellular, result, "预期蜂窝网络返回 'CELLULAR'")
	})

	// 测试离线网络
	t.Run("Network Offline", func(t *testing.T) {
		result := GetNetworkType(networkOffline)
		assert.Equal(tianma.UnknownStringValue, result, "预期离线网络返回空字符串")
	})

	// 测试其他网络
	t.Run("Network Othernet", func(t *testing.T) {
		result := GetNetworkType(networkOthernet)
		assert.Equal(tianma.NetworkOtherNet, result, "预期其他网络返回 'OTHERNET'")
	})

	// 测试未定义的网络类型
	t.Run("Network Undefined", func(t *testing.T) {
		result := GetNetworkType(999) // 假设 999 是一个未定义的值
		assert.Equal(tianma.UnknownStringValue, result, "预期未定义的网络类型返回空字符串")
	})
}
