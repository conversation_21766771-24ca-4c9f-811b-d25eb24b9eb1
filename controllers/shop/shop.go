package shop

import (
	"github.com/MiaoSiLa/live-service/controllers/shop/fukubukuro"
	"github.com/MiaoSiLa/live-service/controllers/shop/redeem"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// Handler 返回 handler
func Handler() handler.Handler {
	return handler.Handler{
		Name: "shop",
		SubHandlers: []handler.Handler{
			redeemHandler(),
			fukubukuroHandler(),
		},
	}
}

func redeemHandler() handler.Handler {
	return handler.Handler{
		Name: "redeem",
		Actions: map[string]*handler.Action{
			"list":        handler.NewAction(handler.GET, redeem.ActionRedeemShopList, false),
			"record/list": handler.NewAction(handler.GET, redeem.ActionRedeemRecordList, true),

			"exchange": handler.NewAction(handler.POST, redeem.ActionRedeemShopExchange, true),
		},
	}
}

func fukubukuroHandler() handler.Handler {
	return handler.Handler{
		Name: "fukubukuro",
		Actions: map[string]*handler.Action{
			"list": handler.NewAction(handler.GET, fukubukuro.ActionListFukubukuro, false),
			"buy":  handler.NewAction(handler.POST, fukubukuro.ActionBuyFukubukuro, true),
		},
	}
}
