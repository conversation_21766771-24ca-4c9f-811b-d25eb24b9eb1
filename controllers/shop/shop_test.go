package shop

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestHandler(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	h := Handler()
	assert.Equal("shop", h.Name)
	require.Len(h.SubHandlers, 2)
	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h.SubHandlers[0].Actions,
		"exchange", "list", "record/list",
	)
	kc.Check(h.SubHandlers[1].Actions,
		"buy", "list",
	)
}
