package fukubukuro

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livesticker"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/service/missevan/mrpc"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

var rpcResp = userapi.BalanceResp{
	TransactionID:    10095893,
	Balance:          1000,
	LiveNobleBalance: 1000,
	Price:            99999,
	Context:          "{\"transaction_id\":10095893,\"tax\":12.5,\"price\":99999,\"common_coin\":{\"ios\":400,\"android\":500,\"tmallios\":100}",
}

func TestBuyFukubukuroKey(t *testing.T) {
	kc := tutil.NewKeyChecker(t, tutil.JSON)
	kc.Check(listFukubukuroResp{}, "data")
	kc.Check(fukubukuroItem{}, "id", "title", "price", "status", "limited", "limit_type", "requirement_type", "max_purchase_num", "remaining_purchase_num", "refresh_duration")
	kc.Check(buyFukubukuroResp{}, "message", "balance", "max_purchase_num", "remaining_purchase_num", "refresh_duration", "order_num", "order_reward_status")
	kc.Check(fukubukuroParam{}, "goods_id", "room_id")

	kc = tutil.NewKeyChecker(t, tutil.FORM)
	kc.Check(fukubukuroParam{}, "goods_id", "room_id")
}

func TestCountHistoryOrderNum(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	const testUserID = 12
	lg, err := livegoods.FindAllShowingFukubukuro()
	require.NoError(err)
	require.NotEmpty(lg)

	now := goutil.TimeNow()
	nowStr := now.Format(util.TimeFormatYMDWithNoSpace)
	mLiveGoods := goutil.ToMap(lg, "Title").(map[string]*livegoods.LiveGoods)

	livegoods1 := mLiveGoods["测试限购次数"]
	limitKey := keys.KeyBuyFukubukuroDailyUserLimit3.Format(livegoods1.ID, testUserID, nowStr)
	require.NoError(service.Redis.Set(limitKey, 2, time.Second).Err())
	defer service.Redis.Del(limitKey)

	more, err := livegoods1.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)

	mOrderCount, err := countHistoryOrderNum(more, testUserID, mLiveGoods["测试限购次数"].ID, now)
	require.NoError(err)
	require.Equal(int64(2), mOrderCount[livegoods.LimitNumDailyUser])

	livegoods2 := mLiveGoods["测试全站限购次数"]
	limitKey2 := keys.KeyBuyFukubukuroDailyGlobalLimit2.Format(livegoods2.ID, nowStr)
	require.NoError(service.Redis.Set(limitKey2, 2, time.Second).Err())
	defer service.Redis.Del(limitKey2)

	more, err = livegoods2.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)

	mOrderCount, err = countHistoryOrderNum(more, testUserID, mLiveGoods["测试全站限购次数"].ID, now)
	require.NoError(err)
	require.Equal(int64(2), mOrderCount[livegoods.LimitNumDailyGlobal])

	livegoods3 := mLiveGoods["测试商品限购次数"]
	limitKey3 := keys.KeyBuyFukubukuroUserLimit2.Format(livegoods3.ID, testUserID)
	require.NoError(service.Redis.Set(limitKey3, 2, time.Second).Err())
	defer service.Redis.Del(limitKey3)

	more, err = livegoods3.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)

	mOrderCount, err = countHistoryOrderNum(more, testUserID, mLiveGoods["测试商品限购次数"].ID, now)
	require.NoError(err)
	require.Equal(int64(2), mOrderCount[livegoods.LimitTypeUser])

	more.Limits[0].Type = 0
	require.PanicsWithValue(fmt.Sprintf("unknown limit type for fukubukuro id %d", mLiveGoods["测试限购次数"].ID), func() {
		_, _ = countHistoryOrderNum(more, testUserID, mLiveGoods["测试限购次数"].ID, now)
	})

	more = &livegoods.More{Limits: []livegoods.Limit{
		{Requirement: livegoods.RequireSuperFan},
	}}
	res, err := countHistoryOrderNum(more, testUserID, 1, now)
	require.NoError(err)
	assert.Empty(res)
}

func TestCalculateRefreshDuration(t *testing.T) {
	assert := assert.New(t)

	now := goutil.TimeNow()
	beginOfToday := util.BeginningOfDay(now)
	nextDayFromToday := beginOfToday.AddDate(0, 0, 1)
	saleEndTimestamp := beginOfToday.AddDate(0, 0, 2).Unix()
	// 售卖不是最后一天，结束时间是两天后
	assert.Equal(util.TimeToUnixMilli(nextDayFromToday)-util.TimeToUnixMilli(now), *calculateRefreshDuration(now, saleEndTimestamp))

	goutil.SetTimeNow(func() time.Time {
		return beginOfToday
	})
	// 售卖最后一天，结束时间是 24 小时后
	saleEndTimestamp = nextDayFromToday.Unix()
	assert.Zero(*calculateRefreshDuration(now, saleEndTimestamp))

	// 售卖最后一天，结束时间是 23 小时后
	saleEndTimestamp = nextDayFromToday.Add(-time.Hour).Unix()
	assert.Zero(*calculateRefreshDuration(now, saleEndTimestamp))

	// 售卖最后一天，结束时间是 0 小时后
	defer goutil.SetTimeNow(nil)
	saleEndTimestamp = beginOfToday.Unix()
	assert.Zero(*calculateRefreshDuration(now, saleEndTimestamp))
}

func TestActionListFukubukuro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	cleanup := mrpc.SetMock(vip.URLVipList, func(input any) (output any, err error) {
		return nil, nil
	})
	defer cleanup()

	lg, err := livegoods.FindAllShowingFukubukuro()
	require.NoError(err)
	require.NotEmpty(lg)

	mLiveGoods := goutil.ToMap(lg, "Title").(map[string]*livegoods.LiveGoods)

	const testUserID = 12
	nowStr := goutil.TimeNow().Format(util.TimeFormatYMDWithNoSpace)
	livegoods1 := mLiveGoods["测试限购次数"]
	limitKey := keys.KeyBuyFukubukuroDailyUserLimit3.Format(livegoods1.ID, testUserID, nowStr)
	require.NoError(service.Redis.Set(limitKey, 2, time.Second).Err())
	defer service.Redis.Del(limitKey)
	livegoods2 := mLiveGoods["测试全站限购次数"]
	limitKey2 := keys.KeyBuyFukubukuroDailyGlobalLimit2.Format(livegoods2.ID, nowStr)
	require.NoError(service.Redis.Set(limitKey2, 2, time.Second).Err())
	defer service.Redis.Del(limitKey2)

	more, err := mLiveGoods["A 级福袋"].UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)

	// 未登录用户
	c := handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/list", false, nil)
	r, err := ActionListFukubukuro(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*listFukubukuroResp)
	assert.Equal(len(lg), len(resp.Data))
	checkedTimes := 0
	for _, fukubukuro := range resp.Data {
		require.NotNil(mLiveGoods["A 级福袋"])
		if fukubukuro.ID == mLiveGoods["A 级福袋"].ID {
			checkedTimes++
			require.NotEmpty(more.Limits)
			assert.Equal("A 级福袋", fukubukuro.Title)
			assert.Equal(hasLimit, fukubukuro.Limited)
			assert.Equal(more.Limits[0].Num, *fukubukuro.MaxPurchaseNum)
			assert.Equal(int64(1), *fukubukuro.RemainingPurchaseNum)
			assert.Equal(livegoods.SaleOngoing, fukubukuro.Status)
		}
	}
	assert.Equal(1, checkedTimes)

	cancel := mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cancel()
	// 查询限购福袋
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/list", true, nil)
	_, err = livemedal.Collection().UpdateOne(context.Background(),
		bson.M{"user_id": c.UserID()},
		bson.M{"$set": bson.M{"user_id": c.UserID(), "super_fan.expire_time": goutil.TimeNow().Add(time.Hour).Unix()}},
		options.Update().SetUpsert(true))
	require.NoError(err)
	r, err = ActionListFukubukuro(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*listFukubukuroResp)
	assert.Equal(len(lg), len(resp.Data))

	now := goutil.TimeNow()
	beginOfToday := util.BeginningOfDay(now)
	nextDayFromToday := beginOfToday.AddDate(0, 0, 1)

	checkedTimes = 0
	for _, fukubukuro := range resp.Data {
		require.Nil(mLiveGoods["S 级福袋"])
		require.NotNil(mLiveGoods["A 级福袋"])
		if fukubukuro.ID == mLiveGoods["A 级福袋"].ID {
			checkedTimes++
			require.NotEmpty(more.Limits)
			assert.Equal("A 级福袋", fukubukuro.Title)
			assert.Equal(hasLimit, fukubukuro.Limited)
			assert.Equal(more.Limits[0].Num, *fukubukuro.MaxPurchaseNum)
			assert.Equal(int64(1), *fukubukuro.RemainingPurchaseNum)
			assert.Equal(livegoods.SaleOngoing, fukubukuro.Status)
		}
		require.NotNil(mLiveGoods["SS 级福袋"])
		if fukubukuro.ID == mLiveGoods["SS 级福袋"].ID {
			checkedTimes++
			assert.Equal(mLiveGoods["SS 级福袋"].Price, fukubukuro.Price)
			assert.Equal("SS 级福袋", fukubukuro.Title)
			assert.Equal(hasNoLimit, fukubukuro.Limited)
			assert.Nil(fukubukuro.MaxPurchaseNum)
			assert.Nil(fukubukuro.RemainingPurchaseNum)
			assert.Nil(fukubukuro.RefreshDuration)
			assert.NotNil(fukubukuro.RequirementType)
			assert.Equal(livegoods.RequireSuperFan, *fukubukuro.RequirementType)
			assert.Equal(livegoods.SaleOngoing, fukubukuro.Status)
		}
		require.NotNil(mLiveGoods["测试未开始售卖"])
		if fukubukuro.ID == mLiveGoods["测试未开始售卖"].ID {
			checkedTimes++
			assert.Equal(livegoods.SaleNotStarted, fukubukuro.Status)
		}
		require.NotNil(mLiveGoods["测试售卖结束"])
		if fukubukuro.ID == mLiveGoods["测试售卖结束"].ID {
			checkedTimes++
			assert.Equal(livegoods.SaleEnded, fukubukuro.Status)
		}
		require.NotNil(mLiveGoods["测试限购次数"])
		if fukubukuro.ID == mLiveGoods["测试限购次数"].ID {
			checkedTimes++
			assert.Equal(hasLimit, fukubukuro.Limited)
			assert.NotZero(*fukubukuro.MaxPurchaseNum)
			assert.Equal(livegoods.LimitNumDailyUser, *fukubukuro.LimitType)
			assert.Equal(livegoods.SalePurchaseLimitReached, fukubukuro.Status)
			require.NotNil(fukubukuro.RefreshDuration)
			require.LessOrEqual(nextDayFromToday.Unix()-now.Unix(), *fukubukuro.RefreshDuration)
		}
		require.NotNil(mLiveGoods["测试全站限购次数"])
		if fukubukuro.ID == mLiveGoods["测试全站限购次数"].ID {
			checkedTimes++
			assert.Equal(hasLimit, fukubukuro.Limited)
			assert.NotZero(*fukubukuro.MaxPurchaseNum)
			assert.Equal(livegoods.LimitNumDailyGlobal, *fukubukuro.LimitType)
			assert.Zero(*fukubukuro.RemainingPurchaseNum)
			assert.Equal(livegoods.SalePurchaseLimitReached, fukubukuro.Status)
			require.NotNil(fukubukuro.RefreshDuration)
			require.LessOrEqual(nextDayFromToday.Unix()-now.Unix(), *fukubukuro.RefreshDuration)
		}
	}

	require.Equal(6, checkedTimes)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/list", true, nil)
	c.User().ID = 10
	r, err = ActionListFukubukuro(c)
	require.NoError(err)
	require.NotNil(r)
	resp = r.(*listFukubukuroResp)
	checkedTimes = 0
	for _, fukubukuro := range resp.Data {
		require.NotNil(mLiveGoods["测试购买资格"])
		if fukubukuro.ID == mLiveGoods["测试购买资格"].ID {
			checkedTimes++
			assert.Equal(livegoods.SalePurchaseRequirementNotMet, fukubukuro.Status)
		}
	}

	require.Equal(1, checkedTimes)

	livegoods3 := mLiveGoods["A 级福袋"]
	limitKey3 := keys.KeyBuyFukubukuroDailyUserLimit3.Format(livegoods3.ID, testUserID, nowStr)
	require.NoError(service.Redis.Set(limitKey3, 1, time.Second).Err())
	defer service.Redis.Del(limitKey3)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/list", true, nil)
	r, err = ActionListFukubukuro(c)
	resp = r.(*listFukubukuroResp)
	require.NoError(err)
	checkedTimes = 0
	for i := range resp.Data {
		if resp.Data[i].ID == mLiveGoods["A 级福袋"].ID {
			checkedTimes++
			assert.Zero(*resp.Data[i].RemainingPurchaseNum)
			assert.Equal(livegoods.SalePurchaseLimitReached, resp.Data[i].Status)
		}
	}

	assert.Equal(1, checkedTimes)

	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/list", false, nil)
	r, err = ActionListFukubukuro(c)
	resp = r.(*listFukubukuroResp)
	require.NoError(err)
	checkedTimes = 0
	for i := range resp.Data {
		if resp.Data[i].ID == mLiveGoods["SS 级福袋"].ID {
			assert.Equal(livegoods.SaleOngoing, resp.Data[i].Status)
			checkedTimes++
		}
	}

	assert.GreaterOrEqual(1, checkedTimes)
}

type testFukubukuro struct {
	mGifts     map[int64]gift.Gift
	testUserID int64
}

func TestActionBuyFukubukuro(t *testing.T) {
	cleanup := mrpc.SetMock("app://live/buy-fukubukuro", func(input interface{}) (output interface{}, err error) {
		return rpcResp, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLUserVips, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()
	cleanup = mrpc.SetMock(vip.URLVipList, func(input interface{}) (output interface{}, err error) {
		return nil, nil
	})
	defer cleanup()

	require := require.New(t)

	var tf testFukubukuro
	tf.testParamError(t)

	// 礼物数据初始化
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cur, err := gift.Collection().Find(ctx, bson.M{})
	require.NoError(err)
	var allGifts []gift.Gift
	err = cur.All(ctx, &allGifts)
	require.NoError(err)
	require.NotEmpty(allGifts)

	tf.mGifts = goutil.ToMap(allGifts, "GiftID").(map[int64]gift.Gift)
	require.NotEmpty(tf.mGifts)
	tf.testUserID = -123321

	now := goutil.TimeNow()
	// 设定过期时间
	require.NoError(service.LiveDB.Table(livegoods.TableName()).
		Where("type = ?", livegoods.GoodsTypeFukubukuro).
		Update(map[string]interface{}{
			"sale_end_time": now.Add(time.Minute).Unix(),
			"end_time":      now.Add(time.Minute).Unix(),
		}).Error)

	tf.testSuperFanBuyFukubukuro(t)
	tf.testNobleBuyFukubukuro(t)
	tf.testBuyLimitedFukubukuro(t)
}

func (tf *testFukubukuro) testParamError(t *testing.T) {
	assert := assert.New(t)

	// 参数为空
	c := handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, nil)
	_, err := ActionBuyFukubukuro(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 参数错误
	var param fukubukuroParam
	param.GoodsID = -10
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	_, err = ActionBuyFukubukuro(c)
	assert.Equal(actionerrors.ErrParams, err)

	// 直播间不存在
	param.RoomID = 46129461729
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	_, err = ActionBuyFukubukuro(c)
	assert.Equal(actionerrors.ErrCannotFindRoom, err)

	// 商品不存在
	param.GoodsID = 999
	param.RoomID = 223344
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	_, err = ActionBuyFukubukuro(c)
	assert.Equal(actionerrors.ErrGoodsNotFound, err)
}

func (tf *testFukubukuro) testSuperFanBuyFukubukuro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var lg livegoods.LiveGoods
	err := service.LiveDB.
		Table(livegoods.TableName()).
		Where("type = ? AND title = ?", livegoods.GoodsTypeFukubukuro, "SS 级福袋").
		First(&lg).Error
	require.NoError(err)
	require.NotNil(lg)
	require.NotZero(lg.ID)

	var param fukubukuroParam
	param.GoodsID = lg.ID
	c := handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	_, err = ActionBuyFukubukuro(c)
	assert.EqualError(err, "开通任意直播间的超粉才能购买哦~")

	more, err := lg.UnmarshalMore()
	require.NoError(err)
	require.NotNil(more)
	require.Len(more.Gifts, 8)
	require.Len(more.Appearances, 3)

	gifts := make([]interface{}, 0)
	giftIDs := make([]int64, 0)
	now := goutil.TimeNow()
	for i := range more.Gifts {
		// 筛选当前已经存在的礼物
		if _, ok := tf.mGifts[more.Gifts[i].ID]; !ok {
			gifts = append(gifts, &gift.Gift{
				GiftID:    more.Gifts[i].ID,
				Name:      fmt.Sprintf("测试福袋礼物下发 %d", more.Gifts[i].ID),
				NameClean: fmt.Sprintf("测试福袋礼物下发 %d", more.Gifts[i].ID),
				Type:      gift.TypeRebate,
				Order:     int(more.Gifts[i].Num),
				AddedTime: now.Add(5 * -time.Minute),
			})
			giftIDs = append(giftIDs, more.Gifts[i].ID)
		}
	}

	// 外观数据初始化
	appearances := make([]interface{}, len(more.Appearances))
	appearanceIDs := make([]int64, len(more.Appearances))
	durationMap := make(map[int64]int64, len(more.Appearances))
	expTime := now.Add(30 * 24 * time.Hour).Unix()
	for i := range more.Appearances {
		appearances[i] = &appearance.Appearance{
			ID:             more.Appearances[i].ID,
			Name:           "测试超粉嘉年华福袋内置外观",
			Type:           appearance.TypeCardFrame,
			Effect:         "oss://testdata/test.webp",
			WebEffect:      "oss://testdata/test.webp",
			EffectDuration: 5,
			Image:          "oss://testdata/test.webp",
			StartTime:      now.Unix(),
			ExpireTime:     &expTime,
		}
		appearanceIDs[i] = more.Appearances[i].ID
	}

	// 插入超粉用户
	tf.testUserID = 123321
	opt := options.Update()
	opt.SetUpsert(true)
	var lmUser livemedal.LiveMedal
	lmUser.Simple.UserID = tf.testUserID
	lmUser.SuperFan = new(livemedal.SuperFan)
	lmUser.SuperFan.ExpireTime = now.Add(5 * time.Minute).Unix()

	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = livemedal.Collection().UpdateOne(ctx, bson.M{"user_id": tf.testUserID}, bson.M{"$set": lmUser}, opt)
	require.NoError(err)

	clearTestData := func() {
		// 清空测试用礼物数据
		_, err = gift.Collection().DeleteMany(ctx, bson.M{
			"gift_id": bson.M{"$in": giftIDs},
		})
		assert.NoError(err)

		// 清空测试用的下发到用户背包的礼物数据
		_, err = useritems.Collection().DeleteMany(ctx, bson.M{
			"gift_id": bson.M{"$in": giftIDs},
			"user_id": tf.testUserID,
		})

		_, err = appearance.Collection().DeleteMany(ctx, bson.M{
			"id":   bson.M{"$in": appearanceIDs},
			"type": appearance.TypeCardFrame,
		})
		assert.NoError(err)
		_, err = userappearance.Collection().DeleteMany(ctx, bson.M{
			"user_id": tf.testUserID,
		})
		assert.NoError(err)
	}
	clearTestData()
	defer clearTestData()

	if len(gifts) != 0 {
		_, err = gift.Collection().InsertMany(ctx, gifts)
		require.NoError(err)
	}
	if len(appearances) != 0 {
		_, err = appearance.Collection().InsertMany(ctx, appearances)
		require.NoError(err)
	}

	// 刷新缓存
	service.Cache5s.Flush()

	param.GoodsID = lg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	key := keys.KeyNobleUserVips1.Format(c.User().ID)
	err = service.Redis.Set(key, `{}`, 30*time.Second).Err()
	require.NoError(err)
	r, err := ActionBuyFukubukuro(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*buyFukubukuroResp)
	assert.Equal(rpcResp.Balance, resp.Balance.Balance)
	assert.Equal(rpcResp.LiveNobleBalance, resp.Balance.LiveNobleBalance)
	assert.Zero(resp.Balance.LiveNobleBalanceStatus)

	// 检查礼物是否有下发
	e := &goutil.Equipment{
		FromApp:    true,
		OS:         goutil.Android,
		AppVersion: "6.0.7",
	}
	backpackItems, err := useritems.FindBackpackItems(e, tf.testUserID)
	require.NoError(err)
	require.NotEmpty(backpackItems)
	mCheckBackpackItems := goutil.ToMap(backpackItems, "GiftID").(map[int64]useritems.BackpackItem)
	for i := range more.Gifts {
		item, ok := mCheckBackpackItems[more.Gifts[i].ID]
		assert.True(ok)
		assert.NotEmpty(item)
	}

	// 检查外观是否有下发
	for i := range appearances {
		aItem := appearances[i].(*appearance.Appearance)
		ua, err := userappearance.FindValidAppearance(appearanceIDs[i], c.UserID(), appearance.TypeCardFrame)
		require.NoError(err)
		require.NotNil(ua)
		assert.Equal(c.UserID(), ua.UserID)
		assert.Equal(aItem.ID, ua.AppearanceID)
		assert.GreaterOrEqual(ua.StartTime, now.Unix())
		assert.GreaterOrEqual(*ua.ExpireTime, now.Unix()+durationMap[aItem.ID])
	}
}

func (tf *testFukubukuro) testNobleBuyFukubukuro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	var lg livegoods.LiveGoods
	err := service.LiveDB.
		Table(livegoods.TableName()).
		Where("type = ? AND title = ?", livegoods.GoodsTypeFukubukuro, "贵族福袋").
		First(&lg).Error
	require.NoError(err)
	require.NotNil(lg)
	require.NotZero(lg.ID)

	var param fukubukuroParam
	param.GoodsID = lg.ID
	c := handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = 1
	_, err = ActionBuyFukubukuro(c)
	assert.EqualError(err, "暂无购买资格")
}

func (tf *testFukubukuro) testBuyLimitedFukubukuro(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	// 测试购买限购福袋
	var limitedLg livegoods.LiveGoods
	err := service.LiveDB.
		Table(livegoods.TableName()).
		Where("type = ? AND title = ?", livegoods.GoodsTypeFukubukuro, "A 级福袋").
		First(&limitedLg).Error
	require.NoError(err)
	require.NotNil(limitedLg)
	require.NotZero(limitedLg.ID)

	limitedMore, err := limitedLg.UnmarshalMore()
	require.NoError(err)
	require.NotEmpty(limitedMore.Limits)
	assert.NotZero(limitedMore.Limits[0].Num)
	require.Nil(limitedMore.Requirement())

	// 测试购买全站限购福袋
	var globalLimitedLg livegoods.LiveGoods
	err = service.LiveDB.
		Table(livegoods.TableName()).
		Where("type = ? AND title = ?", livegoods.GoodsTypeFukubukuro, "测试全站限购次数").
		First(&globalLimitedLg).Error
	require.NoError(err)
	require.NotNil(globalLimitedLg)
	require.NotZero(globalLimitedLg.ID)

	globalLimitedMore, err := globalLimitedLg.UnmarshalMore()
	require.NoError(err)
	require.NotEmpty(globalLimitedMore.Limits)
	assert.NotZero(globalLimitedMore.Limits[0].Num)
	assert.Equal(livegoods.LimitNumDailyGlobal, globalLimitedMore.Limits[0].Type)

	limitedGifts := make([]interface{}, 0)
	limitedGiftIDs := make([]int64, 0)
	now := goutil.TimeNow()
	for i := range limitedMore.Gifts {
		// 筛选当前已经存在的礼物
		if _, ok := tf.mGifts[limitedMore.Gifts[i].ID]; !ok {
			limitedGifts = append(limitedGifts, &gift.Gift{
				GiftID:    limitedMore.Gifts[i].ID,
				Name:      fmt.Sprintf("测试限购福袋礼物下发 %d", limitedMore.Gifts[i].ID),
				NameClean: fmt.Sprintf("测试限购福袋礼物下发 %d", limitedMore.Gifts[i].ID),
				Type:      gift.TypeRebate,
				Order:     int(limitedMore.Gifts[i].Num),
				AddedTime: now.Add(5 * -time.Minute),
			})
			limitedGiftIDs = append(limitedGiftIDs, limitedMore.Gifts[i].ID)
		}
	}

	// 外观数据初始化
	limitedAppearances := make([]interface{}, len(limitedMore.Appearances))
	limitedAppearanceIDs := make([]int64, len(limitedMore.Appearances))
	durationMap := make(map[int64]int64, len(limitedMore.Appearances))
	expTime := now.Add(30 * 24 * time.Hour).Unix()
	for i := range limitedMore.Appearances {
		durationMap[limitedMore.Appearances[i].ID] = limitedMore.Appearances[i].Duration
		limitedAppearances[i] = &appearance.Appearance{
			ID:             limitedMore.Appearances[i].ID,
			Name:           "测试超粉嘉年华限购福袋内置外观",
			Type:           appearance.TypeCardFrame,
			Effect:         "oss://testdata/test.webp",
			WebEffect:      "oss://testdata/test.webp",
			EffectDuration: 5,
			Image:          "oss://testdata/test.webp",
			StartTime:      now.Unix(),
			ExpireTime:     &expTime,
		}
		limitedAppearanceIDs[i] = limitedMore.Appearances[i].ID
	}

	nowStr := now.Format(util.TimeFormatYMDWithNoSpace)
	limitKey := keys.KeyBuyFukubukuroDailyUserLimit3.Format(limitedLg.ID, tf.testUserID, nowStr)
	globalLimitKey := keys.KeyBuyFukubukuroDailyGlobalLimit2.Format(globalLimitedLg.ID, nowStr)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	clearTestData := func() {
		// 清空测试用礼物数据
		_, err = gift.Collection().DeleteMany(ctx, bson.M{
			"gift_id": bson.M{"$in": limitedGiftIDs},
		})
		assert.NoError(err)

		// 清空测试用的下发到用户背包的礼物数据
		_, err = useritems.Collection().DeleteMany(ctx, bson.M{
			"gift_id": bson.M{"$in": limitedGiftIDs},
			"user_id": tf.testUserID,
		})

		_, err = appearance.Collection().DeleteMany(ctx, bson.M{
			"id":   bson.M{"$in": limitedAppearanceIDs},
			"type": appearance.TypeCardFrame,
		})
		assert.NoError(err)

		_, err = userappearance.Collection().DeleteMany(ctx, bson.M{
			"user_id": tf.testUserID,
		})
		assert.NoError(err)
		assert.NoError(service.Redis.Del(limitKey, globalLimitKey).Err())
	}

	clearTestData()
	defer clearTestData()

	// 如果有测试需要且数据库中没有的数据就插入到数据库
	if len(limitedGifts) > 0 {
		_, err = gift.Collection().InsertMany(ctx, limitedGifts)
		require.NoError(err)
	}
	if len(limitedAppearances) > 0 {
		_, err = appearance.Collection().InsertMany(ctx, limitedAppearances)
		require.NoError(err)
	}

	// 刷新缓存
	service.Cache5s.Flush()

	var param fukubukuroParam
	param.GoodsID = limitedLg.ID
	c := handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	r, err := ActionBuyFukubukuro(c)
	require.NoError(err)
	require.NotNil(r)
	resp := r.(*buyFukubukuroResp)
	require.NotNil(resp.MaxPurchaseNum)
	assert.Equal(limitedMore.Limits[0].Num, *resp.MaxPurchaseNum)
	require.NotNil(resp.RemainingPurchaseNum)
	assert.Equal(int64(1), *resp.MaxPurchaseNum)
	require.NotNil(resp.RefreshDuration)
	assert.NotZero(resp.RefreshDuration)

	// 检查礼物是否有下发
	e := &goutil.Equipment{
		FromApp:    true,
		OS:         goutil.Android,
		AppVersion: "6.0.7",
	}
	backpackItems, err := useritems.FindBackpackItems(e, tf.testUserID)
	require.NoError(err)
	require.NotEmpty(backpackItems)
	mCheckBackpackItems := goutil.ToMap(backpackItems, "GiftID").(map[int64]useritems.BackpackItem)
	for i := range limitedGiftIDs {
		item, ok := mCheckBackpackItems[limitedGiftIDs[i]]
		assert.True(ok)
		assert.NotEmpty(item)
	}

	// 发放指定时长的背包礼物
	durationGift, ok := mCheckBackpackItems[301]
	require.True(ok)
	assert.NotZero(durationGift.TimeLeft)

	// 检查外观是否有下发
	for i := range limitedAppearances {
		aItem := limitedAppearances[i].(*appearance.Appearance)
		ua, err := userappearance.FindValidAppearance(limitedAppearanceIDs[i], tf.testUserID, appearance.TypeCardFrame)
		require.NoError(err)
		require.NotNil(ua)
		assert.Equal(tf.testUserID, ua.UserID)
		assert.Equal(aItem.ID, ua.AppearanceID)
		assert.GreaterOrEqual(ua.StartTime, now.Unix())
		assert.GreaterOrEqual(*ua.ExpireTime, now.Unix()+durationMap[aItem.ID])
	}

	// 测试超出限购上限
	param.GoodsID = limitedLg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	_, err = ActionBuyFukubukuro(c)
	assert.EqualError(err, fmt.Sprintf("每日限购 %d 份哦~", limitedMore.Limits[0].Num))

	// 测试全站限购
	// 先购入两次
	param.GoodsID = globalLimitedLg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	_, err = ActionBuyFukubukuro(c)
	require.NoError(err)

	param.GoodsID = globalLimitedLg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID + 1
	key := keys.KeyNobleUserVips1.Format(c.User().ID)
	err = service.Redis.Set(key, `{}`, 30*time.Second).Err()
	require.NoError(err)
	_, err = ActionBuyFukubukuro(c)
	require.NoError(err)

	// 达到上限，应该返回限购信息
	param.GoodsID = globalLimitedLg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID + 2
	_, err = ActionBuyFukubukuro(c)
	assert.EqualError(err, fmt.Sprintf("每日限购 %d 份哦~", globalLimitedMore.Limits[0].Num))

	var order livetxnorder.LiveTxnOrder
	require.NoError(order.DB().Where("buyer_id = ? AND status = ?", tf.testUserID, livetxnorder.StatusSuccess).
		Where("goods_type = ? AND goods_id = ?", livegoods.GoodsTypeFukubukuro, limitedLg.ID).
		Where("create_time > ? AND create_time <= ?", now.Add(-time.Minute).Unix(), now.Add(time.Minute).Unix()).
		Take(&order).Error)
	assert.Equal(util.NewInt(livetxnorder.OpenStatusNoRoom), order.More.OpenStatus)
	// 清理数据
	require.NoError(livetxnorder.LiveTxnOrder{}.DB().
		Where("buyer_id = ? AND status = ?", tf.testUserID, livetxnorder.StatusSuccess).
		Where("goods_type = ? AND goods_id = ?", livegoods.GoodsTypeFukubukuro, limitedLg.ID).
		Where("create_time > ? AND create_time <= ?", now.Add(-time.Minute).Unix(), now.Add(time.Minute).Unix()).
		Delete(&livetxnorder.LiveTxnOrder{}).Error)
	require.NoError(livetxnorder.LiveTxnOrder{}.DB().
		Where("status = ?", livetxnorder.StatusSuccess).
		Where("goods_type = ? AND goods_id = ?", livegoods.GoodsTypeFukubukuro, globalLimitedLg.ID).
		Where("create_time > ? AND create_time <= ?", now.Add(-time.Minute).Unix(), now.Add(time.Minute).Unix()).
		Delete(&livetxnorder.LiveTxnOrder{}).Error)

	// 测试购买时段错误
	require.NoError(service.LiveDB.Table(livegoods.TableName()).
		Where("title = ?", "A 级福袋").
		Update("sale_end_time", now.Add(-time.Minute).Unix()).Error)
	param.GoodsID = limitedLg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	_, err = ActionBuyFukubukuro(c)
	assert.EqualError(err, "当前时段无法购买")

	require.NoError(service.LiveDB.Table(livegoods.TableName()).
		Where("title = ?", "A 级福袋").
		Update(map[string]interface{}{
			"sale_end_time": now.Unix(),
			"end_time":      now.Add(-time.Minute).Unix(),
		}).Error)
	param.GoodsID = limitedLg.ID
	c = handler.NewTestContext(http.MethodPost, "/api/v2/activity/fukubukuro/buy", true, param)
	c.User().ID = tf.testUserID
	_, err = ActionBuyFukubukuro(c)
	assert.EqualError(err, "当前时段无法购买")
}

func TestFukubukuroParam_limitKeyTTl(t *testing.T) {
	assert := assert.New(t)

	mockNow := time.Date(2025, 5, 30, 0, 0, 0, 0, time.Local)
	defer goutil.SetTimeNow(func() time.Time {
		return mockNow
	})()

	now := goutil.TimeNow()
	param := &fukubukuroParam{
		limitType: livegoods.LimitTypeUser,
		livegoods: &livegoods.LiveGoods{EndTime: now.Add(2 * 24 * time.Hour).Unix()},
	}
	ttl := param.limitKeyTTL()
	assert.Equal(17*24*time.Hour, ttl)

	// 测试商品已经下架
	param = &fukubukuroParam{
		limitType: livegoods.LimitTypeUser,
		livegoods: &livegoods.LiveGoods{EndTime: now.Add(-(2 * 24 * time.Hour)).Unix()},
	}
	ttl = param.limitKeyTTL()
	assert.Equal(15*24*time.Hour, ttl)

	// 测试没有商品下架时间
	param = &fukubukuroParam{
		limitType: livegoods.LimitTypeUser,
		livegoods: &livegoods.LiveGoods{EndTime: 0},
	}
	ttl = param.limitKeyTTL()
	assert.Equal(15*24*time.Hour, ttl)

	// 测试非用户商品限购
	param = &fukubukuroParam{
		limitType: livegoods.LimitNumDailyGlobal,
		livegoods: &livegoods.LiveGoods{},
	}
	ttl = param.limitKeyTTL()
	assert.Equal(15*24*time.Hour, ttl)
}

func TestFukubukuroParam_purchaseLimitExceededError(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &fukubukuroParam{
		limitType: livegoods.LimitNumDailyUser,
		limitNum:  3,
	}
	err := param.purchaseLimitExceededError(3)
	require.NotNil(err)
	assert.EqualError(err, "每日限购 3 份哦~")

	param = &fukubukuroParam{
		limitType: livegoods.LimitNumDailyGlobal,
		limitNum:  2,
	}
	err = param.purchaseLimitExceededError(2)
	require.NotNil(err)
	assert.EqualError(err, "每日限购 2 份哦~")

	param = &fukubukuroParam{
		limitType: livegoods.LimitTypeUser,
		limitNum:  1,
	}
	err = param.purchaseLimitExceededError(1)
	require.NotNil(err)
	assert.EqualError(err, "此礼包限购 1 份哦~")
}

// TestWearMessageBubble 测试福袋购买下发消息气泡的时候自动佩戴
func TestWearMessageBubble(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	userID := int64(998)
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	a, err := appearance.Find(bson.M{
		"type":        appearance.TypeMessageBubble,
		"from":        bson.M{"$ne": appearance.FromNoble},
		"expire_time": nil}, nil)
	require.NoError(err)
	require.NotEmpty(a)
	col := userappearance.Collection()
	_, err = col.DeleteMany(ctx, bson.M{
		"user_id": userID,
		"status":  userappearance.StatusWorn,
		"type":    appearance.TypeMessageBubble,
	})
	require.NoError(err)

	param := fukubukuroParam{
		userID: userID,
		more: &livegoods.More{
			Appearances: []livegoods.AppearanceItem{{ID: a[0].ID, Duration: 10}},
		},
		c: handler.NewTestContext(http.MethodPost, "/", true, nil),
	}
	require.NoError(param.sendAppearances())
	b, err := userappearance.FindMessageBubble(userID)
	require.NoError(err)
	assert.Nil(b)
}

func TestFukubukuroParamAddUserContribution(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := &fukubukuroParam{
		RoomID:    123,
		userID:    12,
		livegoods: &livegoods.LiveGoods{Price: 10},
	}
	before, err := liveuser.Find(param.userID)
	require.NoError(err)
	require.NotNil(before)
	assert.NotPanics(func() { param.addUserContribution() })
	after, err := liveuser.Find(param.userID)
	require.NoError(err)
	assert.Equal(int64(param.livegoods.Price*10), after.Contribution-before.Contribution)
	before = after
	param.uv = &vip.UserVip{Info: &vip.Info{ExpAcceleration: 100}}
	assert.NotPanics(func() { param.addUserContribution() })
	after, err = liveuser.Find(param.userID)
	require.NoError(err)
	assert.Equal(int64(param.livegoods.Price*2*10), after.Contribution-before.Contribution)
}

func TestFukubukuroParam_sendRewards(t *testing.T) {
	require := require.New(t)

	testRewardID := int64(101)
	param := fukubukuroParam{
		currentOrderCount: int64(5),
		more: &livegoods.More{
			RewardIDs: []int64{testRewardID},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := reward.Collection().DeleteOne(ctx, bson.M{"reward_id": testRewardID})
	require.NoError(err)
	testReward := &reward.Reward{
		OID:         primitive.NewObjectID(),
		RewardID:    testRewardID,
		Type:        reward.TypeSticker,
		ElementType: livesticker.TypeUser,
		ElementID:   3,
	}
	_, err = reward.Collection().InsertOne(ctx, testReward)
	require.NoError(err)

	err = param.sendRewards()
	require.NoError(err)
}

func TestFukubukuroParam_sendOrderRewards(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	testRewardID := int64(101)
	param := fukubukuroParam{
		currentOrderCount: int64(5),
		more: &livegoods.More{
			OrderRewardsInfo: &livegoods.OrderRewardsInfo{
				OrderRewards: []livegoods.OrderRewardItem{
					{
						OrderCounts: []int64{1, 2, 3},
						RewardIDs:   []int64{testRewardID},
					},
				},
				Type: livegoods.OrderCountGlobal,
			},
		},
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()

	_, err := reward.Collection().DeleteOne(ctx, bson.M{"reward_id": testRewardID})
	require.NoError(err)
	testReward := &reward.Reward{
		OID:      primitive.NewObjectID(),
		RewardID: testRewardID,
		Type:     reward.TypeUserAppearance,
	}
	_, err = reward.Collection().InsertOne(ctx, testReward)
	require.NoError(err)

	err = param.sendOrderRewards()
	require.NoError(err)
	assert.EqualValues(orderRewardStatusNotAcquired, param.orderRewardStatus)

	param.currentOrderCount = int64(2)
	err = param.sendOrderRewards()
	require.NoError(err)
	assert.EqualValues(orderRewardStatusAcquired, param.orderRewardStatus)
}
