package fukubukuro

import (
	"errors"
	"fmt"
	"slices"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/controllers/utils"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/userappearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/useritems"
	"github.com/MiaoSiLa/live-service/models/mysql/livegoods"
	"github.com/MiaoSiLa/live-service/models/mysql/livetxnorder"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/activity/rankevent"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
	"github.com/MiaoSiLa/missevan-go/util/sets"
)

// 是否限购
const (
	hasNoLimit = iota // 无限购
	hasLimit          // 有限购
)

// countHistoryOrderNum 计算历史购买次数
// 购买的逻辑使用 redis 进行数量限制，所以查询跟购买使用相同的数据源，也从 redis 获取历史购买次数
func countHistoryOrderNum(more *livegoods.More, userID, fukubukuroGoodsID int64, now time.Time) (map[int]int64, error) {
	mOrderCount := make(map[int]int64, len(more.Limits))
	nowStr := now.Format(util.TimeFormatYMDWithNoSpace)
	for _, limit := range more.Limits {
		if limit.Requirement > 0 && limit.Num <= 0 {
			// 非购买数量限制
			continue
		}
		switch limit.Type {
		case livegoods.LimitNumDailyUser:
			if userID == 0 {
				// 用户未登录时，需要展示初始限制数量，不查询个人购买数量
				mOrderCount[limit.Type] = 0
			} else {
				limitKey := keys.KeyBuyFukubukuroDailyUserLimit3.Format(fukubukuroGoodsID, userID, nowStr)
				orderCount, err := service.Redis.Get(limitKey).Int64()
				if err != nil && !serviceredis.IsRedisNil(err) {
					return nil, err
				}
				mOrderCount[limit.Type] = orderCount
			}
		case livegoods.LimitNumDailyGlobal:
			limitKey := keys.KeyBuyFukubukuroDailyGlobalLimit2.Format(fukubukuroGoodsID, nowStr)
			orderCount, err := service.Redis.Get(limitKey).Int64()
			if err != nil && !serviceredis.IsRedisNil(err) {
				return nil, err
			}
			mOrderCount[limit.Type] = orderCount
		case livegoods.LimitTypeUser:
			limitKey := keys.KeyBuyFukubukuroUserLimit2.Format(fukubukuroGoodsID, userID)
			orderCount, err := service.Redis.Get(limitKey).Int64()
			if err != nil && !serviceredis.IsRedisNil(err) {
				return nil, err
			}
			mOrderCount[limit.Type] = orderCount
		default:
			// 未知限购次数类型
			panic(fmt.Sprintf("unknown limit type for fukubukuro id %d", fukubukuroGoodsID))
		}
	}

	return mOrderCount, nil
}

func calculateRefreshDuration(now time.Time, saleEndTimestamp int64) *int64 {
	// 如果已经在最后一天范围里，则直接返回 0
	// NOTICE: 此处 saleEndTimestamp 时间戳先减去 1 秒是为了保证在函数 BeginningOfDay 执行后依然能返回结束当天 0 点的时间
	if now.After(util.BeginningOfDay(time.Unix(saleEndTimestamp-1, 0))) {
		return util.NewInt64(0)
	}

	// 剩余刷新时间
	beginOfToday := util.BeginningOfDay(now)
	nextDayFromToday := beginOfToday.AddDate(0, 0, 1)
	refreshDuration := util.TimeToUnixMilli(nextDayFromToday) - util.TimeToUnixMilli(now) // 计算今天到下一天之间的差值
	return &refreshDuration
}

type fukubukuroItem struct {
	ID      int64  `json:"id"`
	Title   string `json:"title"`
	Price   int    `json:"price"`
	Status  int    `json:"status"`
	Limited int    `json:"limited"`

	// 以下字段只有限购商品才会返回
	LimitType            *int   `json:"limit_type,omitempty"`
	RequirementType      *int   `json:"requirement_type,omitempty"`
	MaxPurchaseNum       *int64 `json:"max_purchase_num,omitempty"`
	RemainingPurchaseNum *int64 `json:"remaining_purchase_num,omitempty"`
	RefreshDuration      *int64 `json:"refresh_duration,omitempty"`

	limits        []livegoods.Limit
	saleStartTime int64
	saleEndTime   int64
}

type listFukubukuroResp struct {
	Data []fukubukuroItem `json:"data"`
}

// ActionListFukubukuro 获取福袋信息
/**
 * @api {get} /api/v2/shop/fukubukuro/list 福袋列表
 * @apiDescription 获取当前可用的福袋
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/shop/fukubukuro
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "id": 3, // 福袋 ID
 *           "title": "尊享福袋", // 福袋名称
 *           "price": 99999, // 价格
 *           "status": 1, // 当前福袋售卖状态，0: 未开始售卖，1: 正常售卖中，2: 售卖已结束，3: 购买次数达到上限，4: 没有购买资格
 *           "limited": 1, // 0 为自由购买商品，1 为限购商品
 *           "limit_type": 1, // 限购类型，1: 用户每日限购，2: 全站每日限购，3: 用户限购
 *           "requirement_type": 1, // 购买资格类型，1: 超粉, 2: 贵族
 *           "max_purchase_num": 1, // 最大可购买上限
 *           "remaining_purchase_num": 1, // 剩余可购买份数
 *           "refresh_duration": 100000 // 刷新倒计时，单位：毫秒
 *         }
 *       ]
 *     }
 *   }
 *
 */
func ActionListFukubukuro(c *handler.Context) (handler.ActionResponse, error) {
	var resp listFukubukuroResp
	lgs, err := livegoods.FindAllShowingFukubukuro()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	resp.Data = make([]fukubukuroItem, len(lgs))
	for i, lg := range lgs {
		var fukubukuro fukubukuroItem
		more, err := lg.UnmarshalMore()
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, logger.Fields{"fukubukuro_id": lg.ID})
		}
		if more == nil {
			// TODO: 临时防止接口 panic, 待后续确认逻辑
			more = &livegoods.More{}
		}

		fukubukuro.ID = lg.ID
		fukubukuro.Title = lg.Title
		fukubukuro.Price = lg.Price
		fukubukuro.saleStartTime = lg.SaleStartTime
		fukubukuro.saleEndTime = lg.SaleEndTime
		fukubukuro.limits = more.Limits
		foundOrderCount := int64(0)
		activeLimitType := 0
		// 根据限购份数计算剩余可购买份数
		if more.HasNumLimit() {
			fukubukuro.Limited = hasLimit
			now := goutil.TimeNow()
			// 查询当日 0 点至 24 点的购买记录
			mOrderCount, err := countHistoryOrderNum(more, c.UserID(), lg.ID, now)
			if err != nil {
				return nil, actionerrors.NewErrServerInternal(err, nil)
			}
			mLimitState, limitType := more.CalculateLimitState(mOrderCount)
			foundOrderCount = mLimitState[limitType].OrderCount
			activeLimitType = limitType
			// 限购次数
			fukubukuro.MaxPurchaseNum = &mLimitState[limitType].LimitNum
			// 限购类型
			fukubukuro.LimitType = &limitType
			// 剩余购买次数
			fukubukuro.RemainingPurchaseNum = &mLimitState[limitType].RemainingNum
			// 剩余刷新时间
			fukubukuro.RefreshDuration = calculateRefreshDuration(now, lg.SaleEndTime)
		}

		// 购买资格类型
		limit := more.Requirement()
		if limit != nil {
			fukubukuro.RequirementType = &limit.Requirement
		}

		fukubukuro.Status, err = lg.CheckSaleStatus(c.UserID(), foundOrderCount, activeLimitType)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}

		resp.Data[i] = fukubukuro
	}

	return &resp, nil
}

type fukubukuroParam struct {
	GoodsID int64 `form:"goods_id" json:"goods_id"`
	RoomID  int64 `form:"room_id" json:"room_id"`

	userID            int64
	creatorID         int64
	rpcResp           *userapi.BalanceResp
	livegoods         *livegoods.LiveGoods
	room              *room.Room
	more              *livegoods.More
	now               time.Time
	uv                *vip.UserVip
	gs                userstatus.GeneralStatus
	c                 *handler.Context
	historyOrderCount int64
	limitType         int
	limitNum          int64
	remainingNum      int64
	orderRewardStatus int
	currentOrderCount int64
}

type buyFukubukuroResp struct {
	Message string             `json:"message"`
	Balance userapi.BuyBalance `json:"balance"`

	// 以下字段只有限购商品才会返回
	// TODO: 之后需要把 max_purchase_num 命名简化为 max_num，remaining_purchase_num 命名简化为 remaining_num
	MaxPurchaseNum       *int64 `json:"max_purchase_num,omitempty"`
	RemainingPurchaseNum *int64 `json:"remaining_purchase_num,omitempty"`
	RefreshDuration      *int64 `json:"refresh_duration,omitempty"`

	// 以下字段只有礼包需求有订单奖励条件时返回
	OrderRewardStatus *int   `json:"order_reward_status,omitempty"`
	OrderNum          *int64 `json:"order_num,omitempty"`
}

// 订单中奖状态
const (
	orderRewardStatusNotAcquired = iota // 未中奖
	orderRewardStatusAcquired           // 触发奖励
)

// ActionBuyFukubukuro 下单购买福袋
/**
 * @api {post} /api/v2/shop/fukubukuro/buy 下单购买福袋
 * @apiDescription 创建一个福袋订单然后购买，如果成功则自动下发所有内置物品
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/shop/fukubukuro
 *
 * @apiParam {Number} goods_id 商品（福袋） ID
 * @apiParam {Number} [room_id] 房间 ID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "message": "购买成功！",
 *       "balance": {
 *         "balance": 1000,
 *         "live_noble_balance": 1000,
 *         "live_noble_balance_status": 1
 *       },
 *       "max_purchase_num": 1, // 最大可购买上限
 *       "remaining_purchase_num": 1, // 剩余可购买份数
 *       "refresh_duration": 100000, // 刷新倒计时，单位：毫秒
 *       "order_reward_status": 0, // 0: 未中奖 1: 触发奖励 只在礼包需求有订单奖励条件时返回
 *       "order_num": 88 // 第 x 笔订单 只在礼包需求有订单奖励条件时返回
 *     }
 *   }
 *
 */
func ActionBuyFukubukuro(c *handler.Context) (handler.ActionResponse, error) {
	var param fukubukuroParam
	err := param.load(c)
	if err != nil {
		return nil, err
	}

	// 创建订单并扣费
	err = param.createOrderAndBuy()
	if err != nil {
		return nil, err
	}

	// 发放礼物
	err = param.sendGifts()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 发放外观
	err = param.sendAppearances()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 发放奖励
	err = param.sendRewards()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 发放订单奖励
	err = param.sendOrderRewards()
	if err != nil {
		logger.Error(err)
		// PASS
	}

	// 添加活动积分
	goutil.Go(func() {
		param.addActivity()
		param.addUserContribution()
		param.addLiveSpend()
	})

	// 发送购买商品事件
	goutil.Go(func() {
		param.sendRankEvent()
	})

	isNoble := param.uv != nil
	resp := buyFukubukuroResp{
		Message: "购买成功！",
		Balance: userapi.NewBuyBalance(param.rpcResp, isNoble),
	}
	if param.more.HasNumLimit() {
		// 限购次数
		resp.MaxPurchaseNum = &param.limitNum
		// 剩余可购买份数
		resp.RemainingPurchaseNum = &param.remainingNum
		// 剩余刷新时间
		resp.RefreshDuration = calculateRefreshDuration(goutil.TimeNow(), param.livegoods.SaleEndTime)
	}
	if param.more.HasOrderRewards() {
		resp.OrderNum = &param.currentOrderCount
		resp.OrderRewardStatus = &param.orderRewardStatus
	}

	return &resp, nil
}

func (param *fukubukuroParam) load(c *handler.Context) error {
	err := c.Bind(&param)
	if err != nil {
		return actionerrors.ErrParams
	}
	if param.RoomID < 0 {
		return actionerrors.ErrParams
	}
	if param.RoomID > 0 {
		r, err := room.Find(param.RoomID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if r == nil {
			return actionerrors.ErrCannotFindRoom
		}
		param.room = r
		param.creatorID = r.CreatorID
	}
	if param.GoodsID <= 0 {
		return actionerrors.ErrParams
	}

	param.userID = c.UserID()
	param.now = goutil.TimeNow()
	param.livegoods, err = livegoods.Find(param.GoodsID, livegoods.GoodsTypeFukubukuro)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.livegoods == nil {
		return actionerrors.ErrGoodsNotFound
	}
	// 检查售卖时间
	if !(param.livegoods.IsValidSaleTime() && param.livegoods.IsValidShowTime()) {
		return actionerrors.ErrParamsMsg("当前时段无法购买")
	}

	param.more, err = param.livegoods.UnmarshalMore()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.more == nil {
		// TODO: 临时防止接口 panic, 待后续确认逻辑
		return actionerrors.ErrNotFound("福袋不存在")
	}

	// 检查当前的购买次数
	if param.more.HasNumLimit() {
		// 查询当日 0 点至 24 点的购买记录
		mOrderCount, err := countHistoryOrderNum(param.more, param.userID, param.GoodsID, goutil.TimeNow())
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}

		mLimitState, limitType := param.more.CalculateLimitState(mOrderCount)
		param.historyOrderCount = mLimitState[limitType].OrderCount
		param.limitType = limitType
		param.limitNum = mLimitState[limitType].LimitNum
		param.remainingNum = mLimitState[limitType].RemainingNum
	}

	status, err := param.livegoods.CheckSaleStatus(param.userID, param.historyOrderCount, param.limitType)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	switch status {
	case livegoods.SalePurchaseRequirementNotMet:
		limit := param.more.Requirement()
		if limit != nil {
			switch limit.Requirement {
			case livegoods.RequireSuperFan:
				return actionerrors.ErrParamsMsg("开通任意直播间的超粉才能购买哦~")
			case livegoods.RequireNoble:
				return actionerrors.ErrParamsMsg("暂无购买资格")
			}
		}
	case livegoods.SalePurchaseLimitReached:
		// WORKAROUND: 多个限购条件叠加时，默认读取第一个限购条件的限购次数
		// NOTICE: 需要保证配置福袋限购时保证第一个限购条件的限购份数都是最小的
		return param.purchaseLimitExceededError(param.more.Limits[0].Num)
	}

	gs, uv, err := userstatus.UserGeneral(param.userID, c)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if uv != nil && uv.IsActive() {
		param.uv = uv
	}
	param.gs = gs
	param.c = c
	return nil
}

func (param *fukubukuroParam) createOrderAndBuy() error {
	var err error
	// 防止用户在短期内刷单造成限购失效
	// 数据库还有一层通过订单限制购买数量，但因为并发和主从延迟问题还需要 redis 做兜底
	if param.more.HasNumLimit() {
		limitKey := param.limitKey()
		if limitKey != nil {
			pipe := service.Redis.TxPipeline()
			cmd := pipe.Incr(*limitKey)
			pipe.Expire(*limitKey, param.limitKeyTTL())
			_, err = pipe.Exec()
			if err != nil {
				return actionerrors.NewErrServerInternal(err, nil)
			}

			count := cmd.Val()
			if count > param.limitNum {
				return param.purchaseLimitExceededError(param.limitNum)
			}

			defer func() {
				if err != nil {
					// 购买失败，扣减购买次数
					if err := service.Redis.Decr(*limitKey).Err(); err != nil {
						logger.Error(err)
						// PASS
					}
				}
			}()
		}
	}

	// 有订单奖励的礼包记录购买次数
	// NOTICE: 目前仅支持全站当日订单数量统计
	if param.more.HasOrderRewards() {
		orderRewardKey := getOrderRewardKey(param)
		if orderRewardKey == "" {
			return actionerrors.NewErrServerInternal(errors.New("order reward key was empty"), logger.Fields{
				"user_id":  param.userID,
				"goods_id": param.GoodsID,
			})
		}
		pipe := service.Redis.TxPipeline()
		cmd := pipe.Incr(orderRewardKey)
		pipe.Expire(orderRewardKey, 15*24*time.Hour)
		_, err = pipe.Exec()
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		param.currentOrderCount = cmd.Val()

		defer func() {
			if err != nil {
				// 购买失败，扣减购买次数
				if err := service.Redis.Decr(orderRewardKey).Err(); err != nil {
					logger.Error(err)
					// PASS
				}
			}
		}()
	}

	// 发起请求
	// TODO: 这里需要给 RPC 传 order id 以建立映射关系
	noble := goutil.BoolToInt(param.uv != nil) // 是否使用贵族钻石
	param.rpcResp, err = userapi.BuyFukubukuro(param.userID, param.livegoods.ID,
		param.livegoods.Title, param.livegoods.Price, noble, userapi.NewUserContext(param.c))
	if err != nil {
		return err
	}

	// 创建订单
	more := &livetxnorder.MoreInfo{
		OpenStatus: util.NewInt(livetxnorder.OpenStatusNoRoom),
	}
	order := livetxnorder.NewOrder(param.livegoods, param.userID, 0, more)
	order.Status = livetxnorder.StatusSuccess
	order.TID = param.rpcResp.TransactionID
	db := livetxnorder.LiveTxnOrder{}.DB()
	err = db.Create(order).Error
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *fukubukuroParam) limitKey() *string {
	var limitKey *string
	nowStr := param.now.Format(util.TimeFormatYMDWithNoSpace)
	switch param.limitType {
	case livegoods.LimitNumDailyUser:
		key := keys.KeyBuyFukubukuroDailyUserLimit3.Format(param.livegoods.ID, param.userID, nowStr)
		limitKey = &key
	case livegoods.LimitNumDailyGlobal:
		key := keys.KeyBuyFukubukuroDailyGlobalLimit2.Format(param.livegoods.ID, nowStr)
		limitKey = &key
	case livegoods.LimitTypeUser:
		key := keys.KeyBuyFukubukuroUserLimit2.Format(param.livegoods.ID, param.userID)
		limitKey = &key
	}
	return limitKey
}

func (param *fukubukuroParam) limitKeyTTL() time.Duration {
	now := goutil.TimeNow()
	ttl := 15 * 24 * time.Hour
	if param.limitType == livegoods.LimitTypeUser && param.livegoods.EndTime >= now.Unix() {
		ttl = time.Unix(param.livegoods.EndTime, 0).Add(ttl).Sub(now)
	}
	return ttl
}

func (param *fukubukuroParam) purchaseLimitExceededError(limitNum int64) error {
	if param.limitType == livegoods.LimitTypeUser {
		return actionerrors.ErrParamsMsg(fmt.Sprintf("此礼包限购 %d 份哦~", limitNum))
	}
	return actionerrors.ErrParamsMsg(fmt.Sprintf("每日限购 %d 份哦~", limitNum))
}

func getOrderRewardKey(param *fukubukuroParam) string {
	nowStr := param.now.Format(util.TimeFormatYMDWithNoSpace)
	switch param.more.OrderRewardsInfo.Type {
	case livegoods.OrderCountGlobal:
		return keys.KeyBuyFukubukuroGlobalRewardCount2.Format(param.livegoods.ID, nowStr)
	}
	return ""
}

func (param *fukubukuroParam) sendGifts() error {
	fukubukuroAdder := useritems.NewTransactionAdder(param.userID, param.rpcResp.TransactionID, param.rpcResp.Context, len(param.more.Gifts))
	for i := range param.more.Gifts {
		g, err := gift.FindShowingGiftByGiftID(param.more.Gifts[i].ID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if g == nil {
			return actionerrors.NewErrServerInternal(fmt.Errorf("礼物 ID：%d 不存在", param.more.Gifts[i].ID), nil)
		}
		if !useritems.IsBackpackGift(g) {
			return actionerrors.NewErrServerInternal(fmt.Errorf("礼物 %d 不是背包礼物", g.GiftID), nil)
		}

		nowUnix := param.now.Unix()
		expireTime := param.more.Gifts[i].ExpireTime
		// ExpireTime 与 Duration 同时只会存在一个
		if param.more.Gifts[i].Duration > 0 {
			expireTime = nowUnix + param.more.Gifts[i].Duration/1000
		}
		fukubukuroAdder = fukubukuroAdder.Append(g, param.more.Gifts[i].Num, nowUnix, expireTime)
	}
	err := fukubukuroAdder.Add()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	return nil
}

func (param *fukubukuroParam) sendAppearances() error {
	appearanceIDs := make([]int64, len(param.more.Appearances))
	for i := range param.more.Appearances {
		appearanceIDs[i] = param.more.Appearances[i].ID
	}

	appearances, err := appearance.Find(bson.M{
		"id": bson.M{"$in": appearanceIDs},
	}, nil)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	mAppearances := goutil.ToMap(appearances, "ID").(map[int64]*appearance.Appearance)
	for i := range param.more.Appearances {
		aItem := mAppearances[param.more.Appearances[i].ID]
		if aItem == nil {
			logger.Error(fmt.Errorf("failed to find appearance id: %d", param.more.Appearances[i].ID))
			continue
		}
		err := userappearance.AddAppearance(param.userID, param.more.Appearances[i].Duration, 0, aItem)
		if err != nil {
			logger.WithField("user_id", param.userID).Error(err)
			return actionerrors.NewErrServerInternal(err, nil)
		}
	}

	return nil
}

func (param *fukubukuroParam) addActivity() {
}

func (param *fukubukuroParam) addLiveSpend() {
	utils.SendLiveSpend(param.userID, int64(param.livegoods.GoodsTotalPrice()), param.RoomID)
}

// sendRankEvent 发送购买商品事件
func (param *fukubukuroParam) sendRankEvent() {
	syncCommon := rankevent.NewSyncCommonParam(param.userID)
	// 存在不是直播间购买商品的情况
	if param.room != nil {
		syncCommon.SetRoomInfo(param.room.RoomID, param.room.CreatorID, param.room.GuildID, param.room.ActivityCatalogID)
	}
	err := syncCommon.BuyGoods(
		param.livegoods,
		param.rpcResp.TransactionID,
		param.now,
	).Send(param.c.UserContext())
	if err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param *fukubukuroParam) addUserContribution() {
	pointAdd := int64(param.livegoods.Price) * 10 // 1 钻石 = 10 经验

	if param.uv != nil && param.uv.Info != nil {
		pointAdd = param.uv.Info.ScaleContribution(pointAdd)
	}
	addParam := userstatus.NewAddContributionParams(param.userID, param.RoomID, "", userstatus.FromNormal, param.uv)
	err := addParam.AddPurchaseContribution(pointAdd)
	if err != nil {
		logger.Error(err)
		return
	}
}

func (param *fukubukuroParam) sendRewards() error {
	rewardIDs := param.more.RewardIDs
	if len(rewardIDs) == 0 {
		return nil
	}
	rewardMap, err := reward.FindRewardsMapWithCache(rewardIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{
			"user_id":  param.userID,
			"goods_id": param.GoodsID,
		})
	}
	for _, rID := range rewardIDs {
		rd, ok := rewardMap[rID]
		if !ok {
			logger.WithFields(logger.Fields{
				"user_id":   param.userID,
				"reward_id": rID,
				"goods_id":  param.GoodsID,
			}).Error("find fukubukuro reward failed: reward not found")
			continue
		}
		err = rd.Send(param.RoomID, param.creatorID, param.userID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, logger.Fields{
				"user_id":   param.userID,
				"reward_id": rID,
				"goods_id":  param.GoodsID,
			})
		}
	}
	return nil
}

func (param *fukubukuroParam) sendOrderRewards() error {
	rewardIDs, ok := param.getOrderRewardIDs()
	if !ok {
		return nil
	}
	if len(rewardIDs) == 0 {
		logger.WithFields(logger.Fields{
			"user_id":     param.userID,
			"order_count": param.currentOrderCount,
			"goods_id":    param.GoodsID,
		}).Error("rewardIDs is empty")
		return nil
	}
	param.orderRewardStatus = orderRewardStatusAcquired
	rewardMap, err := reward.FindRewardsMapWithCache(sets.Uniq(rewardIDs))
	if err != nil {
		return actionerrors.NewErrServerInternal(err, logger.Fields{
			"user_id":     param.userID,
			"order_count": param.currentOrderCount,
			"goods_id":    param.GoodsID,
		})
	}
	for _, rID := range rewardIDs {
		rd, ok := rewardMap[rID]
		if !ok {
			logger.WithFields(logger.Fields{
				"user_id":     param.userID,
				"order_count": param.currentOrderCount,
				"reward_id":   rID,
				"goods_id":    param.GoodsID,
			}).Error("find fukubukuro with order reward failed: reward not found")
			continue
		}
		err = rd.Send(param.RoomID, param.creatorID, param.userID)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, logger.Fields{
				"user_id":     param.userID,
				"order_count": param.currentOrderCount,
				"reward_id":   rID,
				"goods_id":    param.GoodsID,
			})
		}
	}
	return nil
}

func (param *fukubukuroParam) getOrderRewardIDs() ([]int64, bool) {
	if !param.more.HasOrderRewards() {
		return nil, false
	}
	for _, info := range param.more.OrderRewardsInfo.OrderRewards {
		if slices.Contains(info.OrderCounts, param.currentOrderCount) {
			return info.RewardIDs, true
		}
	}
	return nil, false
}
