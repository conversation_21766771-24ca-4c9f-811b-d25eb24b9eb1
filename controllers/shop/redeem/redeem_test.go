package redeem

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

func TestMain(m *testing.M) {
	service.InitTest(true)

	m.Run()
}

func TestTagKeys(t *testing.T) {
	jsonKC := tutil.NewKeyChecker(t, tutil.JSON)
	jsonKC.Check(GoodsInfo{}, "id", "name", "point", "icon_url", "max_redeem_count", "remaining_count")
	jsonKC.Check(RecordInfo{}, "id", "name", "create_time")
}

func TestActionRedeemShopExchange(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goods := shop.RedeemGoods{
		ID:         114514,
		Point:      10,
		Name:       "Hello",
		Sort:       1,
		More:       `{"limit": {"per_user": 5}}`,
		ExpireTime: goutil.TimeNow().Add(time.Minute).Unix(),
	}
	param := exchangeParam{
		GoodsID: goods.ID,
	}
	require.NoError(goods.DB().Delete("", "id = ?", goods.ID).Error)
	require.NoError(goods.DB().Create(&goods).Error)

	c := handler.NewTestContext(http.MethodPost, "", true, param)
	key := shop.KeyUserRedeemedCount(c.UserID(), param.GoodsID)
	require.NoError(service.Redis.Del(key).Err())
	key = shop.KeyUserPoint(goutil.TimeNow().Year(), c.UserID())
	require.NoError(service.Redis.Del(key).Err())

	_, err := ActionRedeemShopExchange(c)
	assert.EqualError(err, "兑换积分不足")

	c = handler.NewTestContext(http.MethodPost, "", true, param)
	require.NoError(shop.AddUserRedeemPoint(c.UserID(), shop.RedeemPointToSpent(goods.Point)))
	_, err = ActionRedeemShopExchange(c)
	assert.EqualError(err, "无法找到对应资源")
}

func TestNewExchangeParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodPost, "", true, nil)
	_, err := newExchangeParam(c)
	assert.EqualError(err, "参数错误")

	goods := shop.RedeemGoods{
		ID:       114514,
		Point:    10,
		Name:     "Hello",
		ShopType: shop.ShopTypeRedeem,
		Sort:     1,
		More:     `{"limit": {"per_user": 5}}`,
	}
	require.NoError(goods.DB().Delete("", "id = ?", goods.ID).Error)
	urp := shop.UserRedeemPoint{
		UserID: c.UserID(),
		Year:   goutil.TimeNow().Year(),
		Point:  shop.RedeemPointToSpent(goods.Point),
	}
	require.NoError(urp.DB().Delete("", "user_id = ? AND year = ?", urp.UserID, urp.Year).Error)

	param := exchangeParam{
		GoodsID:  goods.ID,
		ShopType: shop.ShopTypeRedeem,
	}
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newExchangeParam(c)
	assert.EqualError(err, "无法找到对应资源")

	require.NoError(goods.DB().Create(&goods).Error)

	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newExchangeParam(c)
	assert.EqualError(err, "该商品已下架，无法兑换")

	require.NoError(goods.DB().Where("id = ?", goods.ID).
		Update("expire_time", goutil.TimeNow().Add(time.Minute).Unix()).Error)

	key := shop.KeyUserRedeemedCount(c.UserID(), param.GoodsID)
	require.NoError(service.Redis.Set(key, 5, time.Minute).Err())
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newExchangeParam(c)
	assert.EqualError(err, "兑换次数不足")

	require.NoError(service.Redis.Del(key).Err())
	require.NoError(urp.DB().Create(&urp).Error)
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newExchangeParam(c)
	assert.EqualError(err, "无法找到对应资源")

	require.NoError(goods.DB().Where("id = ?", goods.ID).
		Update("reward_id", reward.RewardID6).Error)
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newExchangeParam(c)
	assert.EqualError(err, "无法找到对应资源")

	r := &reward.Reward{
		OID:      primitive.NewObjectID(),
		RewardID: 1111999,
		Type:     reward.TypeUserAppearance,
	}
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	_, err = reward.Collection().DeleteOne(ctx, bson.M{"reward_id": r.RewardID})
	require.NoError(err)
	_, err = reward.Collection().InsertOne(ctx, r)
	require.NoError(err)

	require.NoError(goods.DB().Where("id = ?", goods.ID).
		Update("reward_id", r.RewardID).Error)
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	_, err = newExchangeParam(c)
	assert.Error(err)

	param.Confirm = 1
	c = handler.NewTestContext(http.MethodPost, "", true, param)
	p, err := newExchangeParam(c)
	require.NoError(err)
	require.NotNil(p)
	assert.Equal(param.GoodsID, p.GoodsID)
}

func TestExchangeParam_checkUserLevel(t *testing.T) {
	assert := assert.New(t)

	param := exchangeParam{
		user:     &liveuser.Simple{},
		ShopType: shop.ShopTypeRedeem,
	}
	assert.NoError(param.checkUserLevel())

	param.ShopType = shop.ShopTypeLevelPrivilege
	assert.EqualError(param.checkUserLevel(), "当前等级未到 185")
	param.user.Contribution = usercommon.LevelStart[184]
	assert.NoError(param.checkUserLevel())
}

func TestExchangeParam_checkRedeemPoint(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	goods := shop.RedeemGoods{
		ID:    114514,
		Point: 10,
	}
	param := exchangeParam{
		userID:   114514,
		now:      goutil.TimeNow(),
		goods:    &goods,
		ShopType: shop.ShopTypeLevelPrivilege,
	}
	urp := shop.UserRedeemPoint{
		UserID: param.userID,
		Year:   param.now.Year(),
		Point:  shop.RedeemPointToSpent(goods.Point),
	}
	require.NoError(urp.DB().Delete("", "user_id = ? AND year = ?", urp.UserID, urp.Year).Error)
	assert.NoError(param.checkRedeemPoint())

	param.ShopType = shop.ShopTypeRedeem
	assert.EqualError(param.checkRedeemPoint(), "兑换积分不足")

	require.NoError(urp.DB().Create(&urp).Error)
	assert.NoError(param.checkRedeemPoint())
}

func TestExchangeParam_confirmHTMLMsg(t *testing.T) {
	assert := assert.New(t)

	goods := shop.RedeemGoods{
		Point: 10,
		Name:  "test",
	}
	param := exchangeParam{
		ShopType: shop.ShopTypeRedeem,
		goods:    &goods,
	}
	assert.Equal(`<font color="{normal_color}">确认要消耗</font> <font color="{highlight_color}">10</font> <font color="{normal_color}">{point_name}，兑换</font> <font color="{highlight_color}">test</font> <font color="{normal_color}">吗？</font>`, param.confirmHTMLMsg())

	param.ShopType = shop.ShopTypeLevelPrivilege
	assert.Equal(`<font color="{normal_color}">确认使用</font> <font color="{highlight_color}">1</font> <font color="{normal_color}">次领取机会立即获得</font> <font color="{highlight_color}">test</font> <font color="{normal_color}">吗？</font>`, param.confirmHTMLMsg())
}

func TestExchangeParam_checkCustomGifts(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	now := goutil.TimeNow()
	lc := livecustom.LiveCustom{
		ID:         11415141919,
		CustomType: 3,
		CustomID:   11881818,
		ElementID:  11991919,
		StartTime:  now.Unix(),
		EndTime:    now.Add(time.Minute).Unix(),
	}
	p := exchangeParam{
		user: &liveuser.Simple{
			UID: lc.ElementID,
		},
		reward: &reward.Reward{
			ElementID: lc.CustomID,
		},
		now: now,
	}
	assert.NoError(p.checkCustomGifts())
	p.user.Contribution = usercommon.LevelStart[199]
	p.reward.Type = reward.TypeUserGiftCustom
	require.NoError(service.LiveDB.Table(lc.TableName()).Delete("", "id = ?", lc.ID).Error)
	assert.NoError(p.checkCustomGifts())
	require.NoError(service.LiveDB.Create(&lc).Error)
	assert.EqualError(p.checkCustomGifts(), "您已拥有当前礼物赠送资格")
}

func TestExchangeParam_redeem(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	r := reward.Reward{
		RewardID: 1919811,
		Type:     0,
	}
	now := goutil.TimeNow()
	goods := shop.RedeemGoods{
		ID:         11451444,
		RewardID:   r.RewardID,
		Point:      10,
		ExpireTime: now.Add(time.Minute).Unix(),
		MoreInfo:   &shop.More{Limit: &shop.Limit{PerUser: 1}},
	}
	param := exchangeParam{
		userID: 19191,
		goods:  &goods,
		reward: &r,
		now:    now,
	}
	urp := shop.UserRedeemPoint{
		UserID: param.userID,
		Year:   goutil.TimeNow().Year(),
		Point:  shop.RedeemPointToSpent(goods.Point) * 2,
	}
	require.NoError(urp.DB().Delete("", "user_id = ? AND year = ?", urp.UserID, urp.Year).Error)
	countKey := shop.KeyUserRedeemedCount(param.userID, goods.ID)
	require.NoError(service.Redis.Del(countKey).Err())

	resp, err := param.redeem()
	require.Empty(resp)
	assert.EqualError(err, "兑换积分不足")

	require.NoError(urp.DB().Create(&urp).Error)
	require.NoError(shop.RedeemRecord{}.DB().Delete("", "user_id = ? AND goods_id = ?", param.userID, goods.ID).Error)
	resp, err = param.redeem()
	require.NoError(err)
	assert.EqualValues(goods.Point, resp["draw"].(drawResp).Point)
	redeemed, err := service.Redis.Get(countKey).Int()
	require.NoError(err)
	assert.Equal(1, redeemed)
	var count int64
	require.NoError(shop.RedeemRecord{}.DB().Where("user_id = ? AND goods_id = ?", param.userID, goods.ID).Count(&count).Error)
	assert.EqualValues(1, count)

	_, err = param.redeem()
	require.EqualError(err, "兑换次数不足")
	remaining, err := shop.FindRedeemPoint(param.userID, goutil.TimeNow())
	require.NoError(err)
	assert.EqualValues(goods.Point, remaining)
	redeemed, err = service.Redis.Get(countKey).Int()
	require.NoError(err)
	assert.Equal(1, redeemed)

	param.ShopType = shop.ShopTypeLevelPrivilege
	_, err = param.redeem()
	require.EqualError(err, "领取次数不足")
}

func TestActionRedeemShopList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uri := fmt.Sprintf("/?shop_type=%d", shop.ShopTypeRedeem)
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	resp, err := ActionRedeemShopList(c)
	require.NoError(err)
	assert.NotNil(resp.(handler.M)["data"])
}

func TestNewShopListParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	uri := fmt.Sprintf("/?shop_type=%d", shop.ShopTypeRedeem)
	c := handler.NewTestContext(http.MethodGet, uri, true, nil)
	param, err := newShopListParam(c)
	require.NoError(err)
	assert.Equal(c.UserID(), param.userID)
	assert.Equal(shop.ShopTypeRedeem, param.shopType)
}

func TestListRedeemGoods(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	p := shopListParam{
		userID: 114514,
	}
	startTime := goutil.TimeNow().Add(-time.Minute).Unix()
	expireTime := goutil.TimeNow().Add(time.Minute).Unix()
	rg := shop.RedeemGoods{
		ID:         114514,
		Sort:       1,
		ShopType:   shop.ShopTypeRedeem,
		StartTime:  startTime,
		ExpireTime: expireTime,
		More:       `{"limit": {"per_user": 5}}`,
	}
	require.NoError(rg.DB().Delete("", "id = ?", rg.ID).Error)
	require.NoError(rg.DB().Create(rg).Error)

	err := p.listRedeemGoods()
	require.NoError(err)
	require.NotEmpty(p.list)
	assert.Equal(len(p.list), len(p.limitKeyList))
	assert.True(goutil.Includes(len(p.list), func(i int) bool { return p.list[i].ID == rg.ID }))

	p.shopType = shop.ShopTypeLevelPrivilege
	err = p.listRedeemGoods()
	require.NoError(err)
	assert.Empty(p.list)
}

func TestShopListParam_fillRemainingCount(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	maxCount := 5
	redeemedCount := 2
	info := GoodsInfo{ID: 114515, MaxRedeemCount: &maxCount}
	p := shopListParam{
		userID: 1919,
		list:   []*GoodsInfo{&info},
		limitList: []*GoodsInfo{
			{ID: 114514, MaxRedeemCount: &maxCount},
			&info,
			{ID: 114516},
		},
	}
	p.limitKeyList = []string{
		shop.KeyUserRedeemedCount(p.userID, p.limitList[0].ID),
		shop.KeyUserRedeemedCount(p.userID, p.limitList[1].ID),
		shop.KeyUserRedeemedCount(p.userID, p.limitList[2].ID),
	}
	require.NoError(service.Redis.Del(p.limitKeyList[0]).Err())
	require.NoError(service.Redis.Set(p.limitKeyList[0], redeemedCount, time.Minute).Err())

	require.NoError(p.fillRemainingCount())
	assert.EqualValues(maxCount-redeemedCount, *p.limitList[0].RemainingCount)
	assert.EqualValues(maxCount, *p.limitList[1].RemainingCount)
	assert.EqualValues(maxCount, *p.list[0].RemainingCount)
	assert.Nil(p.limitList[2].RemainingCount)

	p.shopType = shop.ShopTypeLevelPrivilege
	p.userID = 0
	require.NoError(p.fillRemainingCount())
	assert.Zero(*p.list[0].RemainingCount)

	p.shopType = shop.ShopTypeRedeem
	require.NoError(p.fillRemainingCount())
	assert.EqualValues(maxCount, *p.limitList[0].RemainingCount)
	assert.EqualValues(maxCount, *p.list[0].RemainingCount)
}

func TestActionRedeemRecordList(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	year := goutil.TimeNow().Year()
	key := shop.KeyUserPoint(year, c.UserID())
	require.NoError(service.Redis.Del(key).Err())

	resp, err := ActionRedeemRecordList(c)
	require.NoError(err)
	pa := resp.(redeemRecordResp).Pagination
	assert.EqualValues(1, pa.P)
	assert.EqualValues(20, pa.PageSize)
}

func TestNewRedeemRecordParam(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	c := handler.NewTestContext(http.MethodGet, "", true, nil)
	param, err := newRedeemRecordParam(c)
	require.NoError(err)
	require.NotNil(param)
	assert.Equal(c.UserID(), param.userID)
	assert.Equal(shop.ShopTypeRedeem, param.shopType)
	assert.EqualValues(1, param.p)
	assert.EqualValues(20, param.pageSize)
}

func TestRedeemRecordParam_findRedeemRecords(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	rr := shop.RedeemRecord{
		ID:         1919,
		UserID:     810,
		GoodsID:    11451411,
		Name:       "test1",
		ShopType:   shop.ShopTypeRedeem,
		CreateTime: goutil.TimeNow().Unix(),
	}
	require.NoError(rr.DB().Delete("", "id = ?", rr.ID).Error)
	require.NoError(rr.DB().Create(rr).Error)

	param := redeemRecordParam{
		userID:   rr.UserID,
		shopType: shop.ShopTypeRedeem,
		p:        1,
		pageSize: 20,
	}
	require.NoError(param.findRedeemRecords())
	require.True(param.pa.Valid())
	assert.NotZero(param.pa.Count)
	assert.NotEmpty(param.list)

	param.shopType = shop.ShopTypeLevelPrivilege
	require.NoError(param.findRedeemRecords())
	assert.Empty(param.list)
}

func TestRedeemRecordParam_buildRedeemRecordResp(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	param := redeemRecordParam{
		userID:   114514,
		shopType: shop.ShopTypeRedeem,
		p:        1,
		pageSize: 20,
	}
	point := int64(2002)
	year := goutil.TimeNow().Year()
	urp := shop.UserRedeemPoint{
		ID:     11919,
		Year:   year,
		UserID: param.userID,
		Point:  point,
	}
	require.NoError(urp.DB().Where("year = ? AND user_id = ?", year, param.userID).Delete("").Error)
	require.NoError(urp.DB().Create(urp).Error)

	resp, err := param.buildRedeemRecordResp()
	require.NoError(err)
	require.NotNil(resp)
	assert.EqualValues(shop.SpentToRedeemPoint(point), resp.(redeemRecordResp).Draw.Point)

	param.shopType = shop.ShopTypeLevelPrivilege
	resp, err = param.buildRedeemRecordResp()
	require.NoError(err)
	require.NotNil(resp)
	assert.Zero(resp.(redeemRecordResp).Draw.Point)
}
