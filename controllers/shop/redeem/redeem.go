package redeem

import (
	"fmt"
	"html"
	"strconv"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livecustom"
	"github.com/MiaoSiLa/live-service/models/livedb/shop"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mongodb/usercommon"
	"github.com/MiaoSiLa/live-service/models/reward"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

const (
	privilegeLimitLevel185  = 185
	customGiftLimitLevel200 = 200
)

// GoodsInfo 列表展示兑换商品所需要的信息
type GoodsInfo struct {
	ID             int64  `json:"id"`
	Name           string `json:"name"`
	Point          int64  `json:"point"`
	IconURL        string `json:"icon_url"`
	MaxRedeemCount *int   `json:"max_redeem_count,omitempty"`
	RemainingCount *int   `json:"remaining_count,omitempty"`
}

// RecordInfo 列表展示兑换记录所需要的信息
type RecordInfo struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	CreateTime int64  `json:"create_time"`
}

type shopListParam struct {
	shopType int
	userID   int64

	list         []*GoodsInfo
	limitList    []*GoodsInfo
	limitKeyList []string
}

// ActionRedeemShopList 兑换商城的商品列表
/**
 * @api {get} /api/v2/shop/redeem/list 兑换商城的商品列表
 * @apiVersion 0.1.0
 * @apiGroup shop
 *
 * @apiParam {number=0,1} [shop_type=0] 兑换商城的类型，0: 万事屋常驻兑换商城；1: 星享馆等级权益商城
 *
 * @apiSuccessExample {json} 商品列表:
 *   {
 *     "code": 0,
 *     "info": { // TODO: 改用 actionV2 格式返回值
 *       "data": [
 *         {
 *           "id": 114514,
 *           "name": "6K 热度卡 × 1",
 *           "point": 30, // 兑换该商品所需要的积分，星享馆商品该字段均为 0
 *           "icon_url": "https://static.maoercdn.com/shopgifts/001.png",
 *           "remaining_count": 5, // 该用户剩余的可兑换次数
 *           "max_redeem_count": 5 // 该商品的最大可兑换次数
 *         },
 *         {
 *           "id": 1919810,
 *           "name": "01期限定称号 × 7d",
 *           "point": 50, // 兑换该商品所需要的积分，星享馆商品该字段均为 0
 *           "icon_url": "https://static.maoercdn.com/shopgifts/002.png",
 *           "remaining_count": 2, // 该用户剩余的可兑换次数
 *           "max_redeem_count": 5 // 该商品的最大可兑换次数
 *         },
 *         ...
 *       ]
 *     }
 *   }
 *
 */
func ActionRedeemShopList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newShopListParam(c)
	if err != nil {
		return nil, err
	}
	err = param.listRedeemGoods()
	if err != nil {
		return nil, err
	}
	err = param.fillRemainingCount()
	if err != nil {
		return nil, err
	}
	return handler.M{
		"data": param.list,
	}, nil
}

func newShopListParam(c *handler.Context) (*shopListParam, error) {
	param := shopListParam{}
	param.shopType, _ = c.GetParamInt("shop_type")
	if param.shopType != shop.ShopTypeRedeem && param.shopType != shop.ShopTypeLevelPrivilege {
		return nil, actionerrors.ErrParams
	}
	param.userID = c.UserID()
	return &param, nil
}

func (p *shopListParam) listRedeemGoods() error {
	goodsList, err := shop.ListRedeemGoods(goutil.TimeNow(), p.shopType)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.list = make([]*GoodsInfo, 0, len(goodsList))
	p.limitList = make([]*GoodsInfo, 0, len(goodsList))
	p.limitKeyList = make([]string, 0, len(goodsList))
	if len(goodsList) == 0 {
		return nil
	}
	for _, goods := range goodsList {
		info := GoodsInfo{
			ID:      goods.ID,
			Name:    goods.Name,
			Point:   goods.Point,
			IconURL: goods.IconURL,
		}
		more := goods.MoreInfo
		p.list = append(p.list, &info)
		if more != nil && more.Limit != nil {
			info.MaxRedeemCount = goutil.NewInt(more.Limit.PerUser)
			p.limitList = append(p.limitList, &info)
			p.limitKeyList = append(p.limitKeyList, shop.KeyUserRedeemedCount(p.userID, goods.ID))
		}
	}
	return nil
}

func (p *shopListParam) fillRemainingCount() error {
	if p.shopType == shop.ShopTypeRedeem && p.userID == 0 {
		// 万事屋内若用户未登录需始终保持剩余兑换次数为最大值
		for i := range p.limitList {
			p.limitList[i].RemainingCount = p.limitList[i].MaxRedeemCount
		}
		return nil
	}
	if p.shopType == shop.ShopTypeLevelPrivilege {
		if p.userID == 0 {
			// 星享馆内若用户未登录需始终保持剩余兑换次数为 0
			for i := range p.list {
				p.list[i].RemainingCount = goutil.NewInt(0)
			}
			return nil
		}
		user, err := liveuser.FindOneSimple(bson.M{"user_id": p.userID}, nil)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if user == nil {
			return actionerrors.ErrCannotFindUser
		}
		if usercommon.Level(user.Contribution) < privilegeLimitLevel185 {
			// 星享馆内若用户未达到等级要求需始终保持剩余兑换次数为 0
			for i := range p.list {
				p.list[i].RemainingCount = goutil.NewInt(0)
			}
			return nil
		}
	}
	if len(p.limitKeyList) == 0 {
		return nil
	}
	result, err := service.Redis.MGet(p.limitKeyList...).Result()
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	for i, r := range result {
		if r == nil {
			p.limitList[i].RemainingCount = p.limitList[i].MaxRedeemCount
			continue
		}
		if p.limitList[i].MaxRedeemCount == nil {
			continue
		}
		redeemedCount, err := strconv.Atoi(r.(string))
		if err != nil {
			logger.WithFields(logger.Fields{
				"user_id":  p.userID,
				"goods_id": p.limitList[i].ID,
			}).Errorf("Error on getting redeemed count: %v", err)
			p.limitList[i].RemainingCount = goutil.NewInt(0)
			continue
		}
		remaining := max(*p.limitList[i].MaxRedeemCount-redeemedCount, 0)
		p.limitList[i].RemainingCount = goutil.NewInt(remaining)
	}
	return nil
}

type exchangeParam struct {
	GoodsID  int64 `form:"goods_id" json:"goods_id"`
	ShopType int   `form:"shop_type" json:"shop_type"`
	Confirm  int   `form:"confirm" json:"confirm"`

	userID int64
	user   *liveuser.Simple
	goods  *shop.RedeemGoods
	reward *reward.Reward
	now    time.Time
}

type drawResp struct {
	Point int64 `json:"point"`
}

// ActionRedeemShopExchange 商城兑换
/**
 * @api {post} /api/v2/shop/redeem/exchange 商城兑换
 * @apiVersion 0.1.0
 * @apiGroup shop
 *
 * @apiParam {Number} goods_id 商城商品 ID
 * @apiParam {number=0,1} [shop_type=0] 兑换商城的类型，0: 万事屋常驻兑换商城；1: 星享馆等级权益商城
 * @apiParam {number=0,1} [confirm=0] 二次确认弹窗，默认都是有弹窗的
 *
 * @apiSuccessExample 万事屋确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       // msg 为二次确认弹窗显示的文案，其中 {normal_color} {highlight_color} 和 {point_name} 需要在前端进行替换
 *       "msg": "<font color=\"{normal_color}\">确认要消耗</font> <font color=\"{highlight_color}\">30</font> <font color=\"{normal_color}\">{point_name}，兑换</font> <font color=\"{highlight_color}\">6K 热度卡 × 1</font> <font color=\"{normal_color}\">吗？</font>"
 *     }
 *   }
 *
 * @apiSuccessExample 星享馆确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1,
 *       // msg 为二次确认弹窗显示的文案，其中 {normal_color} 和 {highlight_color} 需要在前端进行替换
 *       "msg": "<font color=\"{normal_color}\">确认使用</font> <font color=\"{highlight_color}\">1</font> <font color=\"{normal_color}\">次领取机会立即获得</font> <font color=\"{highlight_color}\">185 级用户限定头像框</font> <font color=\"{normal_color}\">吗？</font>"
 *     }
 *   }
 *
 * @apiSuccessExample {json} 兑换成功:
 *   {
 *     "code": 0,
 *     "info": { // TODO: 改用 actionV2 格式返回值
 *       "draw": {
 *         "point": 10 // 用户的剩余积分，星享馆的话无需消耗积分，因此此处始终为 0
 *       }
 *     }
 *   }
 *
 */
func ActionRedeemShopExchange(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newExchangeParam(c)
	if err != nil {
		return nil, err
	}
	resp, err := param.redeem()
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func newExchangeParam(c *handler.Context) (*exchangeParam, error) {
	var param exchangeParam
	err := c.Bind(&param)
	if err != nil || param.GoodsID == 0 {
		return nil, actionerrors.ErrParams
	}
	if param.ShopType != shop.ShopTypeRedeem && param.ShopType != shop.ShopTypeLevelPrivilege {
		return nil, actionerrors.ErrParams
	}
	param.userID = c.UserID()
	param.user, err = liveuser.FindOneSimple(bson.M{"user_id": param.userID}, nil)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	err = param.checkUserLevel()
	if err != nil {
		return nil, err
	}
	param.goods, err = shop.FindRedeemGoods(param.GoodsID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.goods == nil || param.goods.ShopType != param.ShopType {
		return nil, actionerrors.ErrCannotFindResource
	}
	// 为防止程序执行期间的时间流动影响数据和判断，统一使用此刻的时间
	param.now = goutil.TimeNow()
	if param.now.Unix() < param.goods.StartTime || param.now.Unix() >= param.goods.ExpireTime {
		msg := "该商品已下架，无法兑换"
		if param.ShopType == shop.ShopTypeLevelPrivilege {
			// 星享馆需要的报错提示不同
			msg = "该物品已下架，无法领取"
		}
		return nil, actionerrors.NewErrLiveForbidden(msg)
	}
	more := param.goods.MoreInfo
	if more != nil && more.Limit != nil {
		redeemedCount, err := shop.FindUserRedeemedCount(c.UserID(), param.GoodsID)
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
		if more.Limit.PerUser <= redeemedCount {
			msg := "兑换次数不足"
			if param.ShopType == shop.ShopTypeLevelPrivilege {
				// 星享馆需要的报错提示不同
				msg = "领取次数不足"
			}
			return nil, actionerrors.NewErrLiveForbidden(msg)
		}
	}
	err = param.checkRedeemPoint()
	if err != nil {
		return nil, err
	}

	param.reward, err = reward.FindRewardByRewardIDWithCache(param.goods.RewardID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if param.reward == nil || !param.reward.IsForUser() {
		logger.WithFields(logger.Fields{
			"goods_id":  param.GoodsID,
			"reward_id": param.goods.RewardID,
		}).Error("兑换商城内容配置错误")
		return nil, actionerrors.ErrCannotFindResource
	}

	if param.Confirm != 1 {
		return nil, actionerrors.ErrConfirmRequired(param.confirmHTMLMsg(), 1, true)
	}

	err = param.checkCustomGifts()
	if err != nil {
		return nil, err
	}
	return &param, nil
}

func (p *exchangeParam) checkUserLevel() error {
	// 只有星享馆商城需要限制等级
	if p.ShopType != shop.ShopTypeLevelPrivilege {
		return nil
	}
	if p.user == nil || usercommon.Level(p.user.Contribution) < privilegeLimitLevel185 {
		return actionerrors.NewErrLiveForbidden("当前等级未到 185")
	}
	return nil
}

func (p *exchangeParam) checkRedeemPoint() error {
	// 只有万事屋需要确认兑换积分
	if p.ShopType != shop.ShopTypeRedeem {
		return nil
	}
	remainingPoint, err := shop.FindRedeemPoint(p.userID, p.now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if remainingPoint < p.goods.Point {
		return actionerrors.ErrNotEnoughRedeemPoint
	}
	return nil
}

func (p *exchangeParam) confirmHTMLMsg() string {
	switch p.ShopType {
	case shop.ShopTypeRedeem:
		return fmt.Sprintf(
			`<font color="{normal_color}">确认要消耗</font> <font color="{highlight_color}">%d</font> <font color="{normal_color}">{point_name}，兑换</font> <font color="{highlight_color}">%s</font> <font color="{normal_color}">吗？</font>`,
			p.goods.Point, html.EscapeString(p.goods.Name))
	case shop.ShopTypeLevelPrivilege:
		return fmt.Sprintf(
			`<font color="{normal_color}">确认使用</font> <font color="{highlight_color}">1</font> <font color="{normal_color}">次领取机会立即获得</font> <font color="{highlight_color}">%s</font> <font color="{normal_color}">吗？</font>`,
			html.EscapeString(p.goods.Name))
	default:
		return ""
	}
}

func (p *exchangeParam) checkCustomGifts() error {
	if p.user == nil || usercommon.Level(p.user.Contribution) < customGiftLimitLevel200 {
		// 200 级以下用户不会有相关赠送资格
		return nil
	}
	if p.reward == nil || p.reward.Type != reward.TypeUserGiftCustom {
		return nil
	}
	exists, err := livecustom.IsUserCustomGiftExists(p.user.UserID(), p.reward.ElementID)
	if err != nil {
		return err
	}
	// 如果这个用户有生效中的相关记录则不允许兑换
	if exists {
		return actionerrors.NewErrLiveForbidden("您已拥有当前礼物赠送资格")
	}
	return nil
}

func (p *exchangeParam) redeem() (handler.M, error) {
	// 万事屋商城需要计算兑换积分
	remainingPoint, success, err := shop.ProcessUserPoints(p.userID, p.goods, p.now)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !success {
		if remainingPoint >= 0 {
			msg := "兑换次数不足"
			if p.ShopType == shop.ShopTypeLevelPrivilege {
				msg = "领取次数不足"
			}
			return nil, actionerrors.NewErrLiveForbidden(msg)
		}
		return nil, actionerrors.ErrNotEnoughRedeemPoint
	}
	record := shop.RedeemRecord{
		UserID:   p.userID,
		GoodsID:  p.goods.ID,
		Name:     p.goods.Name,
		ShopType: p.ShopType,
	}
	err = record.DB().Create(&record).Error
	if err != nil {
		logger.WithFields(logger.Fields{
			"user_id":  p.userID,
			"goods_id": p.goods.ID,
		}).Errorf("create redeem record error: %v", err)
		// PASS
	}
	// 由于商品对应的奖励均为用户奖励，因此 Send 仅需要传入用户 ID 参数
	err = p.reward.Send(0, 0, p.userID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	return handler.M{
		"draw": drawResp{Point: remainingPoint},
	}, nil
}

type redeemRecordParam struct {
	userID   int64
	shopType int
	p        int64
	pageSize int64

	list []RecordInfo
	pa   goutil.Pagination
}

type redeemRecordResp struct {
	Draw       drawResp          `json:"draw"`
	Prizes     []RecordInfo      `json:"prizes"`
	Pagination goutil.Pagination `json:"pagination"`
}

// ActionRedeemRecordList 兑换商城的兑换记录列表，最多显示 50 条
/**
 * @api {get} /api/v2/shop/redeem/record/list 兑换商城的兑换记录列表，最多显示 50 条
 * @apiVersion 0.1.0
 * @apiGroup shop
 *
 * @apiParam {number=0,1} [shop_type=0] 兑换商城的类型，0: 万事屋常驻兑换商城；1: 星享馆等级权益商城
 * @apiParam {Number} [p=1] 页码 // TODO: 改用新版游标格式 pagination
 * @apiParam {Number} [pagesize=20] page size
 *
 * @apiSuccessExample {json} 兑换记录列表:
 *   {
 *     "code": 0,
 *     "info": { // TODO: 改用 actionV2 格式返回值
 *       "draw": {
 *         "point": 10, // 用户的剩余积分，星享馆不消耗积分，因此此处为 0
 *       },
 *       "prizes": [
 *         {
 *           "id": 1, // 兑换记录的 ID
 *           "name": "6K 热度卡 × 1",
 *           "create_time": 1685362717 // 单位：秒
 *         },
 *         {
 *           "id": 2, // 兑换记录的 ID
 *           "name": "01期限定称号 × 7d",
 *           "create_time": 1685362726 // 单位：秒
 *         },
 *         ...
 *       ],
 *       "pagination": {
 *         "count": 1,
 *         "maxpage": 1,
 *         "p": 1,
 *         "pagesize": 20
 *       }
 *     }
 *   }
 *
 */
func ActionRedeemRecordList(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newRedeemRecordParam(c)
	if err != nil {
		return nil, err
	}
	err = param.findRedeemRecords()
	if err != nil {
		return nil, err
	}
	resp, err := param.buildRedeemRecordResp()
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func newRedeemRecordParam(c *handler.Context) (*redeemRecordParam, error) {
	param := redeemRecordParam{userID: c.UserID()}
	var err error
	param.p, param.pageSize, err = c.GetParamPage()
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.shopType, _ = c.GetParamInt("shop_type")
	if param.shopType != shop.ShopTypeRedeem && param.shopType != shop.ShopTypeLevelPrivilege {
		return nil, actionerrors.ErrParams
	}
	return &param, nil
}

func (p *redeemRecordParam) findRedeemRecords() error {
	recordsList, pa, err := shop.ListRedeemRecords(p.userID, p.shopType, p.p, p.pageSize)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	p.pa = pa
	p.list = make([]RecordInfo, 0, len(recordsList))
	if len(recordsList) == 0 {
		return nil
	}
	for _, record := range recordsList {
		info := RecordInfo{
			ID:         record.ID,
			Name:       record.Name,
			CreateTime: record.CreateTime,
		}
		p.list = append(p.list, info)
	}
	return nil
}

func (p *redeemRecordParam) buildRedeemRecordResp() (handler.ActionResponse, error) {
	var point int64
	// 星享馆兑换不消耗积分，只有万事屋兑换商城需要查询积分
	if p.shopType == shop.ShopTypeRedeem {
		var err error
		point, err = shop.FindRedeemPoint(p.userID, goutil.TimeNow())
		if err != nil {
			return nil, actionerrors.NewErrServerInternal(err, nil)
		}
	}
	return redeemRecordResp{
		Draw:       drawResp{Point: point},
		Prizes:     p.list,
		Pagination: p.pa,
	}, nil
}
