package openapi

import (
	"fmt"
	"net/http"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/MiaoSiLa/live-service/models/live"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/util"
)

func TestActionRoomsExtraInfo(t *testing.T) {
	assert := assert.New(t)
	require := require.New(t)

	roomIDs := make([]int64, 0)
	err := service.DB.Table(live.TableName()).
		Select("room_id").
		Limit(10).Pluck("room_id", &roomIDs).Error
	require.NoError(err)
	assert.Greater(len(roomIDs), 0)

	var params = url.Values{}
	params.Add("room_ids", util.JoinInt64Array(roomIDs, ","))
	params.Add("appkey", "testkey")
	params.Add("ts", fmt.Sprintf("%d", util.TimeNow().Unix()))
	c := handler.NewTestContext(http.MethodPost, "/openapi/rooms-extra-info", false, params)
	resp, _, err := ActionRoomsExtraInfo(c)
	require.NoError(err)
	info, ok := resp.(roomsExtraInfoResp)
	assert.True(ok)
	assert.Greater(len(info.Data), 0)
}

func TestUniqWithout(t *testing.T) {
	assert := assert.New(t)

	ids := uniqWithout([]int64{1, 2, 3, 3}, []int64{2})
	assert.Equal([]int64{1, 3}, ids)

	ids = uniqWithout([]int64{1, 2, 3, 3}, []int64{5})
	assert.Equal([]int64{1, 2, 3}, ids)
}
