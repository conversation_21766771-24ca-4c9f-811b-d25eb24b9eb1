package openapi

import (
	"github.com/gin-gonic/gin"

	"github.com/MiaoSiLa/live-service/config"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/middlewares/openapi"
)

// HandlerV2 returns the registered handler v2
func HandlerV2() *handler.HandlerV2 {
	return &handler.HandlerV2{
		Name: "openapi",
		Middlewares: gin.HandlersChain{
			openapi.Middleware(config.Conf.HTTP.OpenAPIAppKeySecret),
		},
		Actions: map[string]*handler.ActionV2{
			"rooms-extra-info": handler.NewActionV2(handler.POST, ActionRoomsExtraInfo, handler.ActionOption{LoginRequired: false}),
		},
	}
}
