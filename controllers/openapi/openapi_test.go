package openapi

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/tutil"
)

func TestMain(m *testing.M) {
	handler.SetMode(handler.TestMode)
	service.InitTest(true)
	m.Run()
}

func TestHandlerV2(t *testing.T) {
	assert := assert.New(t)

	h := HandlerV2()
	assert.Equal("openapi", h.Name)
	assert.Len(h.Middlewares, 1)
	assert.Len(h.SubHandlers, 0)

	kc := tutil.NewKeyChecker(t, tutil.Actions)
	kc.Check(h, "rooms-extra-info")
}
