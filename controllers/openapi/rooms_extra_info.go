package openapi

import (
	"strconv"
	"time"

	"github.com/go-redis/redis/v7"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/livestatistics"
	"github.com/MiaoSiLa/live-service/models/mysql/catalog"
	"github.com/MiaoSiLa/live-service/models/mysql/tag"
	"github.com/MiaoSiLa/live-service/models/redis/roomsrank"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/keys"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/mongodb"
	serviceredis "github.com/MiaoSiLa/missevan-go/service/redis"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type roomsExtraInfoResp struct {
	Data []*roomsExtraInfoItem `json:"data"`
}

type roomsExtraInfoItem struct {
	RoomID         int64  `json:"room_id"`
	CatalogID      int64  `json:"catalog_id"`
	SubCatalogID   int64  `json:"sub_catalog_id"`
	CustomTagID    int64  `json:"custom_tag_id"`
	CustomTagName  string `json:"custom_tag_name"`
	Online         int64  `json:"online"`
	ActuallyOnline int64  `json:"actually_online"`
	MessageCount   int64  `json:"message_count"`
	Board          int64  `json:"board"`
	IsNewStar      bool   `json:"is_new_star,omitempty"`
}

// ActionRoomsExtraInfo 查询房间其它相关信息
/*
 * @api {post} /openapi/rooms-extra-info 查询房间其它相关信息
 * @apiDescription 用于 idc 查询，供给算法侧使用（注：只查询开播的直播间）
 * @apiVersion 0.1.0
 * @apiGroup openapi
 *
 * @apiParam {String} room_ids 直播间 ID（多个用逗号分隔，例 111,222,333）
 *
 * @apiSuccessExample {json} Success-Response:
 *     HTTP/1.1 200 OK
 *     {
 *       "code": 0,
 *       "message": "",
 *       "data": {
 *         "data": [
 *           {
 *             "room_id": 1111,  // 房间 ID
 *             "catalog_id": 105,  // 直播间一级分区 ID
 *             "sub_catalog_id": 149,  // 直播间二级分区 ID
 *             "custom_tag_id": 69,  // 直播间挂载的个性词条 ID
 *             "custom_tag_name": "e 人派对",  // 直播间挂载的个性词条名称
 *             "online": 25,  // 实时在线人数（展示）
 *             "actually_online": 15,  // 总进房
 *             "message_count": 39,  // 实时弹幕数（本场累积消息数）
 *             "board": 19,  // 实时上榜人数（本场榜）
 *             "is_new_star": false  // 是否是新星
 *           }
 *         ]
 *       }
 *     }
 */
func ActionRoomsExtraInfo(c *handler.Context) (handler.ActionResponse, string, error) {
	var params struct {
		RoomIDs string `form:"room_ids"`
	}
	if err := c.Bind(&params); err != nil || params.RoomIDs == "" {
		return nil, "", actionerrors.ErrParams
	}
	roomIDs, err := goutil.SplitToInt64Array(params.RoomIDs, ",")
	if err != nil || len(roomIDs) == 0 {
		return nil, "", actionerrors.ErrParams
	}
	roomIDs = uniqWithout(roomIDs, room.OpenListExcludeRoomIDs())
	// 过滤直播热榜限制的直播间
	roomIDs = room.FilterLiveHotSuppression(roomIDs)

	// 开播的直播间分区 ID、个性词条 ID
	roomInfos, err := room.ListSimples(bson.M{
		"room_id":     bson.M{"$in": roomIDs},
		"status.open": room.StatusOpenTrue, // 只查询开播的直播间
	}, options.Find().SetProjection(mongodb.NewProjection("room_id, catalog_id, custom_tag_id, statistics")))
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}

	// 开播的直播间 ID 列表
	var openRoomIDs []int64
	if len(roomIDs) == len(roomInfos) {
		openRoomIDs = roomIDs
	} else {
		openRoomIDs = make([]int64, 0, len(roomInfos))
		for _, roomInfo := range roomInfos {
			openRoomIDs = append(openRoomIDs, roomInfo.RoomID)
		}
	}

	// 获取直播间实时在线人数、总进房数
	ctx, cancel := service.MongoDB.Context()
	defer cancel()
	cursor, err := livestatistics.Collection().Find(ctx, bson.M{
		"room_id": bson.M{
			"$in": openRoomIDs,
		},
		"time": bson.M{
			// live_statistics 为定时生成（2 分钟），开播期间主播会有多条记录
			// 取最近 5 分钟的数据，若有多条则取其最新一条数据
			"$gt": goutil.TimeNow().Add(-5 * time.Minute).UnixMilli(),
		},
	}, options.Find().SetProjection(mongodb.NewProjection("room_id, time, display_online, online")))
	if err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	defer cursor.Close(ctx)
	var statistics []livestatistics.OnlineStatistics
	if err := cursor.All(ctx, &statistics); err != nil {
		return nil, "", actionerrors.NewErrServerInternal(err, nil)
	}
	statisticsMap := make(map[int64]livestatistics.OnlineStatistics, len(statistics))
	for _, item := range statistics {
		stat, ok := statisticsMap[item.RoomID]
		if !ok || stat.Time < item.Time { // 同一个主播可能有多条记录，取最大的 time
			statisticsMap[item.RoomID] = item
		}
	}

	// 实时上榜人数
	boardRedisResult := make(map[int64]*redis.IntCmd, len(openRoomIDs))
	pipe := service.Redis.Pipeline()
	for _, roomID := range openRoomIDs {
		key := roomsrank.Key(roomID, roomsrank.RankTypeCurrent, goutil.TimeNow())
		boardRedisResult[roomID] = pipe.ZCard(key)
	}
	_, err = pipe.Exec()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Errorf("redis pipe error: %v", err)
		}
		// PASS
	}

	// 实时弹幕数
	messageCountRedisResult := make(map[int64]*redis.StringCmd, len(openRoomIDs))
	pipe = service.Redis.Pipeline()
	for _, roomID := range openRoomIDs {
		key := keys.KeyRoomsMeta1.Format(roomID)
		messageCountRedisResult[roomID] = pipe.HGet(key, "message_count")
	}
	_, err = pipe.Exec()
	if err != nil {
		if !serviceredis.IsRedisNil(err) {
			logger.Errorf("redis pipe error: %v", err)
		}
		// PASS
	}

	// 个性词条
	tagsMap, err := tag.AllShowCustomTagsMap()
	if err != nil {
		logger.Errorf("AllShowCustomTagsMap error: %v", err)
		// PASS
	}

	// 分区信息
	catalogMap, err := catalog.AllLiveCatalogsWithSubMap(false)
	if err != nil {
		logger.Errorf("AllLiveCatalogsWithSubMap error: %v", err)
		// PASS
	}

	resp := make([]*roomsExtraInfoItem, 0, len(roomInfos))
	for _, roomInfo := range roomInfos {
		item := &roomsExtraInfoItem{
			RoomID:       roomInfo.RoomID,
			SubCatalogID: roomInfo.CatalogID,
			CustomTagID:  roomInfo.CustomTagID,
			IsNewStar:    roomInfo.HasNovaTag(),
		}
		if stat, ok := statisticsMap[item.RoomID]; ok {
			item.Online = stat.DisplayOnline
			item.ActuallyOnline = stat.Online
		}
		if cmd, ok := boardRedisResult[item.RoomID]; ok {
			board, err := cmd.Result()
			if err != nil {
				logger.Errorf("redis zcard error: %v", err)
				// PASS
			} else {
				item.Board = goutil.MinInt64(board, roomsrank.RankLen(roomsrank.RankTypeCurrent))
			}
		}
		if cmd, ok := messageCountRedisResult[item.RoomID]; ok {
			messageCount, err := cmd.Result()
			if err != nil {
				if !serviceredis.IsRedisNil(err) {
					logger.Errorf("redis hget error: %v", err)
				}
				// PASS
			} else {
				item.MessageCount, _ = strconv.ParseInt(messageCount, 10, 64)
			}
		}
		if item.CustomTagID > 0 {
			if tmpTag, ok := tagsMap[item.CustomTagID]; ok {
				item.CustomTagName = tmpTag.TagName
			} else {
				logger.WithField("room_id", item.RoomID).Errorf("custom_tag not found: %d", item.CustomTagID)
				// PASS
			}
		}
		if tmpCatalog, ok := catalogMap[item.SubCatalogID]; ok {
			item.CatalogID = tmpCatalog.ParentID
		} else {
			logger.WithField("room_id", item.RoomID).Errorf("catalog not found: %d", item.SubCatalogID)
			// PASS
		}
		resp = append(resp, item)
	}

	return roomsExtraInfoResp{Data: resp}, "", nil
}

func uniqWithout[T comparable](slice []T, withoutSlice []T) []T {
	uniqueMap := make(map[T]struct{}, len(slice)+len(withoutSlice))
	for _, v := range withoutSlice {
		uniqueMap[v] = struct{}{}
	}
	uniqueSlice := make([]T, 0, len(slice))
	for _, v := range slice {
		if _, ok := uniqueMap[v]; !ok {
			uniqueMap[v] = struct{}{}
			uniqueSlice = append(uniqueSlice, v)
		}
	}
	return uniqueSlice
}
