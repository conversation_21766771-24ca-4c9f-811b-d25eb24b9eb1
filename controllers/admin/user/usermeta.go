package user

import (
	"fmt"
	"html"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/mowangskuser"
	"github.com/MiaoSiLa/live-service/models/userstatus"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	"github.com/MiaoSiLa/missevan-go/service/missevan/pushservice"
)

const (
	recommendNumUpdateTypeAdd = iota + 1
	recommendNumUpdateTypeSub
)

type recommendNumUpdateParam struct {
	UserID  int64  `form:"user_id" json:"user_id"`
	Reason  string `form:"reason" json:"reason"`
	Num     int    `form:"num" json:"num"`
	Type    int    `form:"type" json:"type"`
	Confirm int    `form:"confirm" json:"confirm"`

	updateNum    int
	recommendNum int
	uv           *vip.UserVip
}

// ActionRecommendNumUpdate 增减神话推荐次数
/**
 * @api {post} /api/v2/admin/user/recommendnum/update 增减神话推荐次数
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {String} reason 原因
 * @apiParam {Number} num 数量
 * @apiParam {number=1,2} type 增减类型 1：增加，2：减少
 * @apiParam {number=0,1} [confirm=0] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "success"
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "info": {
 *       "confirm": 1, // 再次请求需要传递的参数值
 *       "msg": "确认为 <b>上神</b> 用户 <b>啊啊啊</b> 增加 1 次神话推荐机会吗？"
 *     }
 *   }
 *
 * @apiError (400) {Number} code 501010000
 * @apiError (400) {String} info 参数错误
 *
 * @apiError (500) {Number} code 100010002
 * @apiError (500) {String} info 数据库错误
 *
 */
func ActionRecommendNumUpdate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newRecommendNumUpdateParam(c)
	if err != nil {
		return nil, err
	}
	if err = param.recommendNumUpdate(); err != nil {
		return nil, err
	}
	param.sendUpdateMessage()
	param.recommendNumUpdateLog(c)
	return "success", nil
}

func newRecommendNumUpdateParam(c *handler.Context) (*recommendNumUpdateParam, error) {
	param := new(recommendNumUpdateParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	if param.UserID <= 0 || param.Reason == "" || param.Num <= 0 {
		return nil, actionerrors.ErrParams
	}

	switch param.Type {
	case recommendNumUpdateTypeAdd:
		param.updateNum = param.Num
	case recommendNumUpdateTypeSub:
		param.updateNum = -param.Num
	default:
		return nil, actionerrors.ErrParams
	}
	user, err := mowangskuser.FindByUserID(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if user == nil {
		return nil, actionerrors.ErrCannotFindUser
	}
	param.uv, err = vip.UserActivatedVip(param.UserID, false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !vip.HavePrivilege(param.uv, vip.PrivilegeRecommend) {
		return nil, actionerrors.NewErrForbidden("该用户不是神话 / 上神贵族")
	}

	if param.Confirm == 0 {
		message := fmt.Sprintf("确认为 <b>%s</b> 用户 <b>%s</b> %s %d 次神话推荐机会吗？",
			param.uv.Title, html.EscapeString(user.Username), param.updateTypeStr(), param.Num)
		return nil, actionerrors.ErrConfirmRequired(message, 1, true)
	}
	return param, nil
}

func (param *recommendNumUpdateParam) recommendNumUpdate() error {
	ok, recommendNum, err := userstatus.IncreaseRecommendNum(param.UserID, param.updateNum)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// 神话推荐次数低于需要扣除的数量时，更新失败
	if !ok {
		return actionerrors.NewErrForbidden("推荐次数不足，无法扣除！")
	}
	param.recommendNum = recommendNum
	return nil
}

func (param recommendNumUpdateParam) sendUpdateMessage() {
	systemMsg := []pushservice.SystemMsg{
		{
			UserID: param.UserID,
			Title:  "神话推荐次数" + param.updateTypeStr(),
			Content: fmt.Sprintf("尊敬的%s用户，由于%s，已为您%s %d 次神话推荐机会，剩余推荐次数总量为 %d，如有疑问，请联系您的 VIP 客服。",
				param.uv.Title, param.Reason, param.updateTypeStr(), param.Num, param.recommendNum,
			),
		},
	}
	if err := service.PushService.SendSystemMsgWithOptions(systemMsg,
		&pushservice.SystemMsgOptions{
			DisableHTMLEscape: false,
		},
	); err != nil {
		logger.Error(err)
		// PASS
	}
}

func (param recommendNumUpdateParam) updateTypeStr() string {
	switch param.Type {
	case recommendNumUpdateTypeAdd:
		return "增加"
	case recommendNumUpdateTypeSub:
		return "扣除"
	default:
		panic("type error")
	}
}

func (param recommendNumUpdateParam) recommendNumUpdateLog(c *handler.Context) {
	msg := fmt.Sprintf("%s (%d) 神话推荐次数, 原因: %s", param.updateTypeStr(), param.UserID, param.Reason)
	// 管理员操作日志
	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogMangeNobleRecommendNum, msg)
	if err := box.Send(); err != nil {
		logger.Error(err)
		// PASS
	}
}

type hornRemoveParam struct {
	UserID  int64 `form:"user_id" json:"user_id"`
	HornNum int64 `form:"horn_num" json:"horn_num"`
	Confirm int   `form:"confirm" json:"confirm"`

	c *handler.Context
	u *liveuser.Simple
}

// ActionAdminHornRemove 扣除用户全站喇叭
/**
 * @api {post} /api/v2/admin/noble/horn/remove 扣除用户全站喇叭
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/noble
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} horn_num 扣除的喇叭数量
 * @apiParam {number=0,1} [confirm] 确认次数, 首次请求传 0
 *
 * @apiSuccess {Number} code
 * @apiSuccess {String} info
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "message": "扣除成功",
 *     "data": null
 *   }
 *
 * @apiSuccessExample 确认消息内容:
 *   {
 *     "code": 100010020,
 *     "message": "",
 *     "data": {
 *       "confirm": 1,
 *       "msg": "用户 余额宝 (ID: 12345) 当前拥有 10 个喇叭，确认扣除 1 个喇叭吗？"
 *     }
 *   }
 *
 */
func ActionAdminHornRemove(c *handler.Context) (handler.ActionResponse, string, error) {
	param, err := newAdminHornRemoveParam(c)
	if err != nil {
		return nil, "", err
	}
	err = param.check()
	if err != nil {
		return nil, "", err
	}
	err = param.remove()
	if err != nil {
		return nil, "", err
	}
	return nil, "扣除成功", nil
}

func newAdminHornRemoveParam(c *handler.Context) (*hornRemoveParam, error) {
	param := new(hornRemoveParam)
	err := c.Bind(&param)
	if err != nil || param.UserID <= 0 || param.HornNum <= 0 {
		return nil, actionerrors.ErrParams
	}
	param.c = c
	return param, nil
}

func (param *hornRemoveParam) check() error {
	var err error
	param.u, err = liveuser.FindOneSimple(bson.M{"user_id": param.UserID}, nil)
	if err != nil {
		return err
	}
	if param.u == nil {
		return actionerrors.ErrCannotFindUser
	}
	ownNum, _ := userstatus.HornNum(param.UserID)
	if ownNum < param.HornNum {
		return actionerrors.NewErrForbidden("扣除数量大于用户拥有的喇叭数量，请检查后重新输入")
	}
	if param.Confirm == 0 {
		msg := fmt.Sprintf("用户 %s (ID: %d) 当前拥有 %d 个喇叭，确认扣除 %d 个喇叭吗？", param.u.Username,
			param.UserID, ownNum, param.HornNum)
		return actionerrors.ErrConfirmRequired(msg, 1)
	}
	return nil
}

func (param *hornRemoveParam) remove() error {
	ok, _, err := userstatus.DecreaseHornNum(param.UserID, userstatus.HornTypeNoble, uint(param.HornNum))
	if err != nil {
		return err
	}
	if !ok {
		return actionerrors.NewErrForbidden("扣除失败，请检查数量后重新输入")
	}
	logBox := goclient.NewAdminLogBox(param.c)
	intro := fmt.Sprintf("用户 %s (ID: %d) 扣除 %d 个喇叭", param.u.Username, param.UserID, param.HornNum)
	logBox.Add(userapi.CatalogManageNobleHorn, intro)
	err = logBox.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}
