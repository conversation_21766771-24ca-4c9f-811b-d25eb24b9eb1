package user

import (
	"fmt"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/livedb/livemedalpointlog"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal"
	"github.com/MiaoSiLa/live-service/models/mongodb/livemedal/livemedalstats"
	"github.com/MiaoSiLa/live-service/models/mongodb/liveuser"
	"github.com/MiaoSiLa/live-service/models/room"
	"github.com/MiaoSiLa/live-service/models/vip"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
)

// ActionMedalPointAdd 补发亲密度接口
/**
 * @api {post} /api/v2/admin/user/medal/point/add 补发亲密度
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin/user
 *
 * @apiParam {Number} user_id 用户 ID
 * @apiParam {Number} room_id 房间号
 * @apiParam {String} point 需要增加的亲密度
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "亲密度增加成功"
 *   }
 *
 */
func ActionMedalPointAdd(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		UserID int64 `form:"user_id" json:"user_id"`
		RoomID int64 `form:"room_id" json:"room_id"`
		Point  int64 `form:"point" json:"point"`
	}
	err := c.Bind(&param)
	if err != nil || param.UserID <= 0 || param.RoomID <= 0 || param.Point <= 0 {
		return nil, actionerrors.ErrParams
	}
	ok, err := liveuser.Exists(param.UserID)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.ErrUserNotFound
	}
	r, err := room.Find(param.RoomID, &room.FindOptions{DisableAll: true})
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if r == nil {
		return nil, actionerrors.ErrCannotFindRoom
	}
	if r.Medal == nil {
		return nil, actionerrors.NewErrForbidden("此房间未开通勋章")
	}
	uv, err := vip.UserActivatedVip(param.UserID, false, c)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	addParam := livemedalstats.AddPointParam{
		RoomOID:    r.OID,
		RoomID:     r.RoomID,
		CreatorID:  r.CreatorID,
		MedalName:  r.Medal.Name,
		UserID:     param.UserID,
		UV:         uv,
		Type:       livemedal.TypeGiftAddMedalPoint,
		Source:     livemedal.ChangeSourceAdmin,
		From:       livemedal.FromAdmin,
		PointAdd:   param.Point,
		Scene:      livemedalpointlog.SceneTypeAdmin,
		IsRoomOpen: r.IsOpen(),
	}
	// TODO: 控制每日勋章上限是否生效
	_, err = addParam.AddPoint()
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	box := userapi.NewAdminLogBox(c)
	box.AddWithChannelID(userapi.CatalogManageUserLiveMedals, param.UserID,
		fmt.Sprintf("补发粉丝勋章亲密度，用户 ID: %d，房间 ID: %d，亲密度增加：%d",
			param.UserID, param.RoomID, param.Point))
	return "亲密度增加成功", nil
}
