package giftwall

import (
	"fmt"
	"html"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/MiaoSiLa/live-service/controllers/actionerrors"
	"github.com/MiaoSiLa/live-service/models/mongodb/appearance"
	"github.com/MiaoSiLa/live-service/models/mongodb/gift"
	"github.com/MiaoSiLa/live-service/models/mongodb/giftwall"
	"github.com/MiaoSiLa/live-service/service/userapi"
	"github.com/MiaoSiLa/live-service/util"
	"github.com/MiaoSiLa/missevan-go/controllers/handler"
	"github.com/MiaoSiLa/missevan-go/logger"
	"github.com/MiaoSiLa/missevan-go/service/missevan/goclient"
	goutil "github.com/MiaoSiLa/missevan-go/util"
)

type periodDetailsParam struct {
	periodOID        primitive.ObjectID
	giftIDs          []int64
	giftWallGiftsMap map[int64]*giftwall.Gift
	giftMap          map[int64]*gift.Gift
}

// ActionPeriodDetails 礼物墙详情列表
/**
 * @api {get} /api/v2/admin/giftwall/period/details 礼物墙详情列表
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} period_id 周期 OID
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": {
 *       "data": [
 *         {
 *           "show_gift_id": "1",
 *           "show_gift_name": "show-礼物",
 *           "show_gift_icon_url": "https://static.missevan.com/gifts/avatarframes/001.png",
 *           "target_gift_id": "2",
 *           "target_gift_name": "target-礼物",
 *           "target_gift_icon_url": "https://static.missevan.com/gifts/avatarframes/002.png",
 *           "effective_gift_id": "3,4,5",
 *           "effective_gift_name": "礼物3,礼物4,礼物5",
 *           "effective_gift_icon_url": "https://static.missevan.com/gifts/avatarframes/003.png" // 只取 effective_gift 下第一个礼物的图标地址
 *         }
 *       ]
 *     }
 *   }
 *
 * @apiError (500) {number} code 100010500
 * @apiError (500) {string} info 服务器内部错误
 */
func ActionPeriodDetails(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPeriodDetailsParam(c)
	if err != nil {
		return nil, err
	}
	err = param.findPeriodInfo()
	if err != nil {
		return nil, err
	}
	return param.resp(), nil
}

func newPeriodDetailsParam(c *handler.Context) (*periodDetailsParam, error) {
	param := new(periodDetailsParam)
	oid, _ := c.GetParamString("period_id")
	var err error
	param.periodOID, err = primitive.ObjectIDFromHex(oid)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("参数错误: %s", err))
	}
	return param, nil
}

func (param *periodDetailsParam) findPeriodInfo() error {
	period, err := giftwall.FindByPeriodOID(param.periodOID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if period == nil {
		return actionerrors.ErrNotFound("未查询到该周期内的上墙礼物")
	}
	param.giftIDs = period.ShowGiftIDs

	var walls []*giftwall.Gift
	walls, err = giftwall.FindGiftsByShowGiftIDs(param.giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	// resp() 中礼物列表会按照 param.gift_ids 排序，这里 to map 只保存上墙礼物对应关系
	param.giftWallGiftsMap = goutil.ToMap(walls, "ShowGiftID").(map[int64]*giftwall.Gift)
	if len(param.giftWallGiftsMap) <= 0 {
		return actionerrors.ErrNotFound("未查询到上墙礼物")
	}
	param.giftMap, err = gift.FindGiftMapByGiftIDs(param.giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	return nil
}

func (param *periodDetailsParam) resp() handler.M {
	list := make([]*giftwall.ListItem, 0, len(param.giftIDs))
	for _, id := range param.giftIDs {
		wg, ok := param.giftWallGiftsMap[id]
		if !ok {
			continue
		}
		list = append(list, giftwall.NewListItem(wg, param.giftMap))
	}
	return handler.M{"data": list}
}

type periodAddParam struct {
	StartDate   string                 `form:"start_date" json:"start_date"`
	EndDate     string                 `form:"end_date" json:"end_date"`
	ShowGiftIDs string                 `form:"show_gift_ids" json:"show_gift_ids"`
	Rewards     []*giftwall.RewardInfo `form:"rewards" json:"rewards"`
	Confirm     int                    `form:"confirm" json:"confirm"`

	c                 *handler.Context
	giftIDs           []int64
	rewards           []*giftwall.RewardInfo
	rewardGifts       map[int64]*gift.Gift
	rewardAppearances map[int64]*appearance.Appearance
	startTime         time.Time
	endTime           time.Time
}

// ActionPeriodAdd 新增礼物墙周期
/**
 * @api {post} /api/v2/admin/giftwall/period/add 新增礼物墙周期
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} start_date 开始日期，包含当日
 * @apiParam {String} end_date 结束日期，包含当日
 * @apiParam {String} show_gift_ids 上墙礼物名单
 * @apiParam {Object[]} rewards 点亮目标及奖励
 * @apiParam {Number} rewards.threshold 点亮目标数
 * @apiParam {Number} rewards.type 奖励类型
 * @apiParam {Number} rewards.element_id 奖励 ID
 * @apiParam {number=0,1} [confirm] 确认次数, 首次请求传 0
 * @apiParamExample {json} Request-Example:
 *   {
 *     "start_date": "2022-10-20",
 *     "end_date": "2022-11-03",
 *     "show_gift_ids": "1,2,3", // 礼物 ID，使用半角逗号分割
 *     "rewards": [
 *       {
 *         "threshold": 1,
 *         "type": 1,
 *         "element_id": 2000
 *       },
 *       {
 *         "threshold": 2,
 *         "type": 2,
 *         "element_id": 3000
 *       }
 *     ],
 *     "confirm": 0
 *   }
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "提交成功"
 *   }
 *
 * @apiError (500) {number} code 100010500
 * @apiError (500) {string} info 服务器内部错误
 */
func ActionPeriodAdd(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPeriodAddParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.insert()
	if err != nil {
		return nil, err
	}
	return "提交成功", nil
}

func newPeriodAddParam(c *handler.Context) (*periodAddParam, error) {
	param := new(periodAddParam)
	err := c.Bind(param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	param.startTime, param.endTime, err = c.GetParamDateRange(param.StartDate, param.EndDate)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("周期开始或结束日期输入错误")
	}
	param.endTime = param.endTime.Add(24 * time.Hour) // 结束日期包含当日，故 +24h
	if param.startTime.Weekday() != time.Monday ||    // 确保是自然周
		param.endTime.Sub(param.startTime) != giftwall.PeriodDuration { // 确保持续时间是两个自然周
		return nil, actionerrors.ErrParamsMsg("周期日期范围非 2 个自然周")
	}
	param.giftIDs, err = goutil.SplitToInt64Array(param.ShowGiftIDs, ",")
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("上墙礼物 ID 格式错误")
	}
	if len(param.giftIDs) == 0 {
		return nil, actionerrors.ErrParamsMsg("请输入上墙礼物名单")
	}
	if len(param.Rewards) == 0 {
		return nil, actionerrors.ErrParamsMsg("请输入点亮目标和奖励设置")
	}
	param.rewards = make([]*giftwall.RewardInfo, 0, len(param.Rewards))
	for i, reward := range param.Rewards {
		if reward.Threshold <= 0 {
			return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("第 %d 项点亮目标数错误", i+1))
		}
		if !goutil.HasElem([]int{giftwall.RewardTypeCustomGift, giftwall.RewardTypeAppearance}, reward.Type) {
			return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("第 %d 项奖励类型错误", i+1))
		}
		if reward.ElementID <= 0 {
			return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("第 %d 项奖励 ID 错误", i+1))
		}
		param.rewards = append(param.rewards, &giftwall.RewardInfo{
			Threshold: reward.Threshold,
			Type:      reward.Type,
			ElementID: reward.ElementID,
		})
	}
	return param, nil
}

func (param *periodAddParam) check() error {
	// check period
	periods, err := giftwall.FindPeriods(param.startTime, param.endTime)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(periods) != 0 {
		return actionerrors.NewErrForbidden("已存在该礼物墙周期！")
	}
	now := goutil.TimeNow()
	period, err := giftwall.CurrentPeriodInfo(now)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if period != nil {
		if param.startTime.Unix() >= time.Unix(period.EndTime, 0).Add(giftwall.PeriodMaxSpanDuration).Unix() {
			return actionerrors.NewErrForbidden(fmt.Sprintf("仅可创建未来的 %d 个周期", giftwall.PeriodMaxSpanNum))
		}
		interval := time.Duration(param.startTime.Unix()-period.EndTime) * time.Second
		if interval%giftwall.PeriodDuration != 0 {
			return actionerrors.NewErrForbidden("当前选择的周期与上个礼物墙周期间隔了奇数周")
		}
	} else {
		if param.startTime.Unix() >= goutil.BeginningOfWeek(now.Add(7*24*time.Hour)).Add(giftwall.PeriodMaxSpanDuration).Unix() {
			return actionerrors.NewErrForbidden(fmt.Sprintf("仅可创建未来的 %d 个周期", giftwall.PeriodMaxSpanNum))
		}
	}

	// check show_gift_ids
	if len(goutil.Uniq(param.giftIDs)) != len(param.giftIDs) {
		return actionerrors.ErrParamsMsg("存在重复的礼物 ID，请检查后重试")
	}
	gifts, err := gift.FindGiftMapByGiftIDs(param.giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if len(gifts) != len(param.giftIDs) {
		return actionerrors.ErrParamsMsg("存在无效礼物 ID，请检查后重试")
	}
	giftWalls, err := giftwall.FindGiftsByShowGiftIDs(param.giftIDs)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	gfMap := goutil.ToMap(giftWalls, "ShowGiftID").(map[int64]*giftwall.Gift)
	if len(gfMap) != len(param.giftIDs) {
		var diff []string
		for _, id := range param.giftIDs {
			if _, ok := gfMap[id]; !ok {
				diff = append(diff, strconv.FormatInt(id, 10))
			}
		}
		return actionerrors.ErrParamsMsg(
			fmt.Sprintf("存在尚未录入的上墙礼物，请先前往上墙礼物管理后台录入: %s", strings.Join(diff, ",")))
	}

	// check rewards
	rewardGiftIDs := make([]int64, 0, len(param.rewards))
	rewardAppearanceIDs := make([]int64, 0, len(param.rewards))
	rewardThresholds := make([]int64, 0, len(param.rewards))
	for _, reward := range param.rewards {
		if int(reward.Threshold) > len(param.giftIDs) {
			return actionerrors.ErrParamsMsg("点亮目标数不得超过上墙礼物数量")
		}
		switch reward.Type {
		case giftwall.RewardTypeCustomGift:
			rewardGiftIDs = append(rewardGiftIDs, reward.ElementID)
		case giftwall.RewardTypeAppearance:
			rewardAppearanceIDs = append(rewardAppearanceIDs, reward.ElementID)
		default:
			return actionerrors.ErrParams
		}
		rewardThresholds = append(rewardThresholds, reward.Threshold)
	}

	for i := 1; i < len(rewardThresholds); i++ {
		if rewardThresholds[i] <= rewardThresholds[i-1] {
			return actionerrors.ErrParamsMsg("点亮目标数不得重复或者需要递增")
		}
	}

	param.rewardGifts, err = findRewardGifts(rewardGiftIDs)
	if err != nil {
		return err
	}

	param.rewardAppearances, err = findRewardAppearances(rewardAppearanceIDs)
	if err != nil {
		return err
	}

	return nil
}

func (param *periodAddParam) insert() error {
	startDate := param.startTime.Format(util.TimeFormatYMD)
	endDate := param.endTime.Add(-24 * time.Hour).Format(util.TimeFormatYMD) // endTime 已 +24h 包含当日，故 -24h 将显示日期还原为当日
	if param.Confirm == 0 {
		rewardsHTMLMsgList := make([]string, 0, len(param.rewards)) // 礼物名称已被 html encode
		var rewardName string
		var rewardTypeStr string
		for _, reward := range param.rewards {
			switch reward.Type {
			case giftwall.RewardTypeCustomGift:
				rewardName = param.rewardGifts[reward.ElementID].Name
				rewardTypeStr = "直播间专属礼物"
			case giftwall.RewardTypeAppearance:
				rewardName = param.rewardAppearances[reward.ElementID].Name
				rewardTypeStr = "直播间外观"
			}
			rewardsHTMLMsgList = append(rewardsHTMLMsgList,
				fmt.Sprintf("[%d,%s,%s]", reward.Threshold, rewardTypeStr, html.EscapeString(rewardName)))
		}
		msg := fmt.Sprintf("确定创建礼物墙周期吗？<br>"+
			`<li>周期时间：<font color="red">%s 至 %s</font></li>`+
			`<li>上墙礼物数量：<font color="red">%d</font></li>`+
			"<li>点亮目标及奖励：%s</li>",
			startDate, endDate, len(param.giftIDs), strings.Join(rewardsHTMLMsgList, ","))
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}

	err := giftwall.CreatePeriod(param.startTime, param.endTime, param.giftIDs, param.rewards)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(param.c)
	box.Add(userapi.CatalogManageGiftWall, fmt.Sprintf("新增礼物墙周期：%s 至 %s", startDate, endDate))
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

// ActionPeriodDel 删除礼物墙周期
/**
 * @api {post} /api/v2/admin/giftwall/period/del 删除礼物墙周期
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} period_id 周期 ID
 * @apiParam {number=0,1} [confirm] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample Success-Response:
 *   {
 *     "code": 0,
 *     "info": "删除成功"
 *   }
 *
 * @apiError (500) {number} code 100010500
 * @apiError (500) {string} info 服务器内部错误
 */
func ActionPeriodDel(c *handler.Context) (handler.ActionResponse, error) {
	var param struct {
		Confirm  int    `form:"confirm" json:"confirm"`
		PeriodID string `form:"period_id" json:"period_id"`
	}
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	oid, err := primitive.ObjectIDFromHex(param.PeriodID)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	p, err := giftwall.FindByPeriodOID(oid)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if p == nil {
		return nil, actionerrors.ErrNotFound("该周期不存在")
	}
	if p.StartTime <= goutil.TimeNow().Unix() {
		return nil, actionerrors.NewErrForbidden("只能删除未开始的周期")
	}
	startDate := time.Unix(p.StartTime, 0).Format(goutil.TimeFormatYMD)
	// NOTICE: 两周的时间范围是 1 号零点到 15 号零点，也就是 14 号 23:59:59 结束，故 -1 将结束日期显示为 14 号
	endDate := time.Unix(p.EndTime-1, 0).Format(goutil.TimeFormatYMD)
	if param.Confirm == 0 {
		msg := fmt.Sprintf("确定删除礼物墙周期吗？<br>%s 至 %s", startDate, endDate)
		return nil, actionerrors.ErrConfirmRequired(msg, 1, true)
	}
	ok, err := giftwall.DelPeriod(oid)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}
	if !ok {
		return nil, actionerrors.NewErrForbidden("删除失败")
	}

	box := goclient.NewAdminLogBox(c)
	box.Add(userapi.CatalogManageGiftWall, fmt.Sprintf("删除礼物墙周期：%s 至 %s", startDate, endDate))
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return "删除成功", nil
}

type periodUpdateParam struct {
	PeriodID    string                 `form:"period_id" json:"period_id"`
	ShowGiftIDs string                 `form:"show_gift_ids" json:"show_gift_ids"`
	Rewards     []*giftwall.RewardInfo `form:"rewards" json:"rewards"`
	Confirm     int64                  `form:"confirm" json:"confirm"`

	c                 *handler.Context
	giftIDs           []int64
	rewards           []*giftwall.RewardInfo
	rewardGifts       map[int64]*gift.Gift
	rewardAppearances map[int64]*appearance.Appearance

	period *giftwall.Period
	// periodHasStarted 周期是否已经开始
	periodHasStarted bool
}

// ActionPeriodUpdate 更新礼物墙周期
/**
 * @api {post} /api/v2/admin/giftwall/period/update 更新礼物墙周期
 * @apiVersion 0.1.0
 * @apiGroup /api/v2/admin
 *
 * @apiParam {String} period_id 周期 ID
 * @apiParam {String} [show_gift_ids] 上墙礼物 IDs（礼物 ID 按半角逗号分割）
 * @apiParam {Object[]} [rewards] 点亮目标及奖励，已开始的礼物墙周期，只允许往后新增奖励设置。其他是覆盖本周期现有奖励设置
 * @apiParam {Number} [rewards.threshold] 点亮目标数
 * @apiParam {Number} [rewards.type] 奖励类型
 * @apiParam {Number} [rewards.element_id] 奖励 ID
 * @apiParam {number=0,1} [confirm] 确认次数, 首次请求传 0
 *
 * @apiSuccessExample 更新成功:
 *   {
 *     "code": 0,
 *     "info": "提交成功"
 *   }
 * @apiSuccessExample 确认更新信息:
 *   {
 *     "code": 100010020,
 *     "info": "确定更新礼物墙周期吗？<br>周期：2024-03-18 ~ 2024-03-31<br>删除礼物 ID: 1,2,3<br>增加礼物 ID: 4,5,6<br>上墙礼物总量：3<br>【新增】点亮目标及奖励：[1,直播间专属礼物,四千],[2,直播间专属礼物,五千],[3,直播间外观,座驾]"
 *   }
 *
 * @apiError (500) {number} code 100010500
 * @apiError (500) {string} info 服务器内部错误
 */
func ActionPeriodUpdate(c *handler.Context) (handler.ActionResponse, error) {
	param, err := newPeriodUpdateParam(c)
	if err != nil {
		return nil, err
	}
	err = param.check()
	if err != nil {
		return nil, err
	}
	err = param.update()
	if err != nil {
		return nil, err
	}
	return "提交成功", nil
}

func newPeriodUpdateParam(c *handler.Context) (*periodUpdateParam, error) {
	param := new(periodUpdateParam)
	err := c.Bind(&param)
	if err != nil {
		return nil, actionerrors.ErrParams
	}
	param.c = c

	_, err = primitive.ObjectIDFromHex(param.PeriodID)
	if err != nil {
		return nil, actionerrors.ErrParamsMsg("周期 ID 错误")
	}
	if len(param.ShowGiftIDs) == 0 && len(param.Rewards) == 0 {
		return nil, actionerrors.ErrParamsMsg("至少更新一项")
	}
	if len(param.ShowGiftIDs) > 0 {
		param.giftIDs, err = goutil.SplitToInt64Array(param.ShowGiftIDs, ",")
		if err != nil {
			return nil, actionerrors.ErrParamsMsg("上墙礼物 ID 格式错误")
		}
	}
	if len(param.Rewards) > 0 {
		param.rewards = make([]*giftwall.RewardInfo, 0, len(param.Rewards))
		for i, reward := range param.Rewards {
			if reward.Threshold <= 0 {
				return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("第 %d 项点亮目标数错误", i+1))
			}
			if !goutil.HasElem([]int{giftwall.RewardTypeCustomGift, giftwall.RewardTypeAppearance}, reward.Type) {
				return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("第 %d 项奖励类型错误", i+1))
			}
			if reward.ElementID <= 0 {
				return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("第 %d 项奖励 ID 错误", i+1))
			}
			param.rewards = append(param.rewards, &giftwall.RewardInfo{
				Threshold: reward.Threshold,
				Type:      reward.Type,
				ElementID: reward.ElementID,
			})
		}
	}
	return param, nil
}

func (param *periodUpdateParam) check() error {
	oid, err := primitive.ObjectIDFromHex(param.PeriodID)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	param.period, err = giftwall.FindByPeriodOID(oid)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}
	if param.period == nil {
		return actionerrors.ErrNotFound("未查询到相关周期")
	}
	if goutil.TimeNow().Unix() >= param.period.EndTime {
		return actionerrors.NewErrForbidden("仅可修改未结束的周期")
	}
	if goutil.TimeNow().Unix() >= param.period.StartTime {
		param.periodHasStarted = true
	}

	// 修改上墙礼物进行检查
	if len(param.giftIDs) > 0 {
		if len(goutil.Uniq(param.giftIDs)) != len(param.giftIDs) {
			return actionerrors.ErrParamsMsg("存在重复礼物 ID，请检查后重试")
		}
		gifts, err := gift.FindGiftMapByGiftIDs(param.giftIDs)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		if len(gifts) != len(param.giftIDs) {
			return actionerrors.ErrParamsMsg("存在无效礼物 ID，请检查后重试")
		}
		gws, err := giftwall.FindGiftsByShowGiftIDs(param.giftIDs)
		if err != nil {
			return actionerrors.NewErrServerInternal(err, nil)
		}
		gfMap := goutil.ToMap(gws, "ShowGiftID").(map[int64]*giftwall.Gift)
		if len(gfMap) != len(param.giftIDs) {
			var diff []string
			for _, id := range param.giftIDs {
				if _, ok := gfMap[id]; !ok {
					diff = append(diff, strconv.FormatInt(id, 10))
				}
			}
			return actionerrors.ErrParamsMsg(
				fmt.Sprintf("存在尚未录入的上墙礼物，请先前往上墙礼物管理后台录入: %s", strings.Join(diff, ",")))
		}
	}
	// 修改了奖励配置进行检查
	if len(param.rewards) > 0 {
		rewardGiftIDs := make([]int64, 0, len(param.rewards))
		rewardAppearanceIDs := make([]int64, 0, len(param.rewards))
		rewardThresholds := make([]int64, 0, len(param.rewards))
		if param.periodHasStarted {
			for _, reward := range param.period.Rewards {
				rewardThresholds = append(rewardThresholds, reward.Threshold)
			}
		}
		for _, reward := range param.rewards {
			if len(param.giftIDs) > 0 && int(reward.Threshold) > len(param.giftIDs) || (int(reward.Threshold) > len(param.period.ShowGiftIDs)) {
				return actionerrors.ErrParamsMsg("点亮目标数不得超过上墙礼物数量")
			}
			switch reward.Type {
			case giftwall.RewardTypeCustomGift:
				rewardGiftIDs = append(rewardGiftIDs, reward.ElementID)
			case giftwall.RewardTypeAppearance:
				rewardAppearanceIDs = append(rewardAppearanceIDs, reward.ElementID)
			default:
				return actionerrors.ErrParams
			}
			rewardThresholds = append(rewardThresholds, reward.Threshold)
		}

		for i := 1; i < len(rewardThresholds); i++ {
			if rewardThresholds[i] <= rewardThresholds[i-1] {
				return actionerrors.ErrParamsMsg("点亮目标数不得重复或者需要递增")
			}
		}

		param.rewardGifts, err = findRewardGifts(rewardGiftIDs)
		if err != nil {
			return err
		}

		param.rewardAppearances, err = findRewardAppearances(rewardAppearanceIDs)
		if err != nil {
			return err
		}
	}
	return nil
}

func (param *periodUpdateParam) update() error {
	msg := fmt.Sprintf(
		"确定更新礼物墙周期吗？<br>周期：%s ~ %s",
		time.Unix(param.period.StartTime, 0).Format(util.TimeFormatYMD), time.Unix(param.period.EndTime-1, 0).Format(util.TimeFormatYMD),
	)
	logMsg := fmt.Sprintf("更新礼物墙周期：%s ~ %s",
		time.Unix(param.period.StartTime, 0).Format(util.TimeFormatYMD), time.Unix(param.period.EndTime-1, 0).Format(util.TimeFormatYMD))

	if len(param.giftIDs) > 0 {
		// 弹窗确认是否更新上墙礼物 ID
		set := make(map[int64]struct{}) // 保存 param.giftIDs（新的上墙礼物 IDs）和 period.ShowGiftIDs（旧的上墙礼物 IDs）的并集
		addGiftIDs := make([]string, 0, len(param.giftIDs))
		removeGiftIDs := make([]string, 0, len(param.period.ShowGiftIDs))
		for _, id := range param.giftIDs {
			if goutil.HasElem(param.period.ShowGiftIDs, id) {
				set[id] = struct{}{}
				continue
			}
			addGiftIDs = append(addGiftIDs, strconv.FormatInt(id, 10))
		}
		for _, id := range param.period.ShowGiftIDs {
			if _, ok := set[id]; ok {
				continue
			}
			removeGiftIDs = append(removeGiftIDs, strconv.FormatInt(id, 10))
		}
		removeGiftIDsStr := formatGiftIDs(removeGiftIDs)
		addGiftIDsStr := formatGiftIDs(addGiftIDs)
		msg += fmt.Sprintf("<br>删除礼物 ID: %s<br>增加礼物 ID: %s<br>上墙礼物总量：%d",
			removeGiftIDsStr, addGiftIDsStr, len(param.giftIDs),
		)
		logMsg += fmt.Sprintf("，上墙礼物名单删除（%s），增加（%s）",
			removeGiftIDsStr, addGiftIDsStr,
		)
	}

	if len(param.rewards) > 0 {
		rewardsHTMLMsgList := make([]string, 0, len(param.rewards)) // 礼物名称已被 html encode
		var rewardName string
		var rewardTypeStr string
		for _, reward := range param.rewards {
			switch reward.Type {
			case giftwall.RewardTypeCustomGift:
				rewardName = param.rewardGifts[reward.ElementID].Name
				rewardTypeStr = "直播间专属礼物"
			case giftwall.RewardTypeAppearance:
				rewardName = param.rewardAppearances[reward.ElementID].Name
				rewardTypeStr = "直播间外观"
			}
			rewardsHTMLMsgList = append(rewardsHTMLMsgList,
				fmt.Sprintf("[%d,%s,%s]", reward.Threshold, rewardTypeStr, html.EscapeString(rewardName)))
		}
		var rewardsPre string
		if param.periodHasStarted {
			rewardsPre = "新增"
		} else {
			rewardsPre = "覆盖"
		}
		msg += fmt.Sprintf("<br>【%s】点亮目标及奖励：%s",
			rewardsPre,
			strings.Join(rewardsHTMLMsgList, ","),
		)
		logMsg += fmt.Sprintf("；【%s】点亮目标及奖励配置（%s）",
			rewardsPre,
			strings.Join(rewardsHTMLMsgList, ","),
		)
	}

	if param.Confirm == 0 {
		return actionerrors.ErrConfirmRequired(msg, 1, true)
	}

	if len(param.rewards) > 0 && param.periodHasStarted {
		// 已开始的礼物墙只支持新增
		param.rewards = append(param.period.Rewards, param.rewards...)
	}
	err := giftwall.UpdatePeriod(param.period.OID, param.giftIDs, param.rewards)
	if err != nil {
		return actionerrors.NewErrServerInternal(err, nil)
	}

	box := goclient.NewAdminLogBox(param.c)
	box.Add(userapi.CatalogManageGiftWall, logMsg)
	err = box.Send()
	if err != nil {
		logger.Error(err)
		// PASS
	}
	return nil
}

func formatGiftIDs(giftIDs []string) string {
	lineLen := 7 // 每 7 个 ID 换一行
	for i := lineLen; i < len(giftIDs); i = i + lineLen {
		giftIDs[i] = "<br>" + giftIDs[i]
	}
	return strings.Join(giftIDs, ",")
}

// findRewardGifts 获取直播间专属礼物奖励
func findRewardGifts(rewardGiftIDs []int64) (map[int64]*gift.Gift, error) {
	if len(rewardGiftIDs) == 0 {
		return nil, nil
	}

	if len(rewardGiftIDs) != len(goutil.Uniq(rewardGiftIDs)) {
		return nil, actionerrors.ErrParamsMsg("奖励的礼物 ID 不得重复")
	}

	rewardGifts, err := gift.FindGiftMapByGiftIDs(rewardGiftIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if len(rewardGifts) != len(rewardGiftIDs) {
		var diff []string
		for _, id := range rewardGiftIDs {
			if _, ok := rewardGifts[id]; !ok {
				diff = append(diff, strconv.FormatInt(id, 10))
			}
		}
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("点亮目标和奖励，存在无效礼物 ID: %s", strings.Join(diff, ",")))
	}

	return rewardGifts, nil
}

// findRewardAppearances 获取直播间外观奖励
func findRewardAppearances(rewardAppearanceIDs []int64) (map[int64]*appearance.Appearance, error) {
	if len(rewardAppearanceIDs) == 0 {
		return nil, nil
	}

	if len(rewardAppearanceIDs) != len(goutil.Uniq(rewardAppearanceIDs)) {
		return nil, actionerrors.ErrParamsMsg("奖励的外观 ID 不得重复")
	}

	rewardAppearances, err := appearance.FindMapByIDs(rewardAppearanceIDs)
	if err != nil {
		return nil, actionerrors.NewErrServerInternal(err, nil)
	}

	if len(rewardAppearances) != len(rewardAppearanceIDs) {
		var diff []string
		for _, id := range rewardAppearanceIDs {
			if _, ok := rewardAppearances[id]; !ok {
				diff = append(diff, strconv.FormatInt(id, 10))
			}
		}
		return nil, actionerrors.ErrParamsMsg(fmt.Sprintf("点亮目标和奖励，存在无效外观 ID: %s", strings.Join(diff, ",")))
	}

	return rewardAppearances, nil
}
